import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { UserService } from './user.service';
import { ApiService } from './api_base/api.service';
import { of, throwError } from 'rxjs';

describe('UserService', (): void => {
  let service: UserService;
  let apiServiceMock: jest.Mocked<ApiService>;

  beforeEach((): void => {
    // Create mock for ApiService
    apiServiceMock = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn()
    } as unknown as jest.Mocked<ApiService>;

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        UserService,
        { provide: ApiService, useValue: apiServiceMock }
      ]
    });

    service = TestBed.inject(UserService);
  });

  it('should be created', (): void => {
    expect(service).toBeTruthy();
  });

  describe('usersList', (): void => {
    it('should call get with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        pageSize: 10,
        pageNo: 1,
        search: 'test',
        sort: 'DESC'
      };

      const mockResponse = {
        data: {
          rows: [
            { id: 1, name: 'User 1', email: '<EMAIL>' },
            { id: 2, name: 'User 2', email: '<EMAIL>' }
          ],
          count: 2
        }
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.usersList(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith('user', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('deleteUser', (): void => {
    it('should call delete with correct parameters', (): void => {
      // Arrange
      const mockPayload = { ProjectId: 123 };
      const userId = 1;

      const mockResponse = {
        success: true,
        message: 'User deleted successfully'
      };

      apiServiceMock.delete.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.deleteUser(mockPayload, userId).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.delete).toHaveBeenCalledWith(`user/${userId}`, mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('userDetails', (): void => {
    it('should call get with correct parameters', (): void => {
      // Arrange
      const mockPayload = { ProjectId: 123 };
      const userId = 1;

      const mockResponse = {
        data: {
          id: 1,
          name: 'Test User',
          email: '<EMAIL>',
          role: 'Admin'
        }
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.userDetails(mockPayload, userId).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`user/${userId}`, mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('checkUser', (): void => {
    it('should call post with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        email: '<EMAIL>',
        ProjectId: 123
      };

      const mockResponse = {
        exists: true,
        user: {
          id: 1,
          name: 'Test User',
          email: '<EMAIL>'
        }
      };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.checkUser(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('member/check_user', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('updateUser', (): void => {
    it('should call put with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        name: 'Updated User',
        email: '<EMAIL>',
        role: 'Editor',
        ProjectId: 123
      };
      const userId = 1;

      const mockResponse = {
        success: true,
        message: 'User updated successfully',
        data: {
          id: 1,
          name: 'Updated User',
          email: '<EMAIL>',
          role: 'Editor'
        }
      };

      apiServiceMock.put.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateUser(mockPayload, userId).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.put).toHaveBeenCalledWith(`user/${userId}`, mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('addUser', (): void => {
    it('should call post with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        name: 'New User',
        email: '<EMAIL>',
        role: 'Viewer',
        ProjectId: 123
      };

      const mockResponse = {
        success: true,
        message: 'User added successfully',
        data: {
          id: 3,
          name: 'New User',
          email: '<EMAIL>',
          role: 'Viewer'
        }
      };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.addUser(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('user', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Error handling', (): void => {
    it('should propagate errors from the API service in usersList', (done): void => {
      // Arrange
      const mockPayload = { pageSize: 10, pageNo: 1 };
      const mockError = 'API Error';

      apiServiceMock.get.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.usersList(mockPayload).subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(mockError);
          done();
        }
      });
    });

    it('should propagate errors from the API service in deleteUser', (done): void => {
      // Arrange
      const mockPayload = { ProjectId: 123 };
      const mockId = 456;
      const mockError = 'API Error';

      apiServiceMock.delete.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.deleteUser(mockPayload, mockId).subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(mockError);
          done();
        }
      });
    });

    it('should propagate errors from the API service in userDetails', (done): void => {
      // Arrange
      const mockPayload = { ProjectId: 123 };
      const mockId = 456;
      const mockError = 'API Error';

      apiServiceMock.get.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.userDetails(mockPayload, mockId).subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(mockError);
          done();
        }
      });
    });

    it('should propagate errors from the API service in checkUser', (done): void => {
      // Arrange
      const mockPayload = { email: '<EMAIL>' };
      const mockError = 'API Error';

      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.checkUser(mockPayload).subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(mockError);
          done();
        }
      });
    });

    it('should propagate errors from the API service in updateUser', (done): void => {
      // Arrange
      const mockPayload = { name: 'Updated User' };
      const mockId = 456;
      const mockError = 'API Error';

      apiServiceMock.put.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.updateUser(mockPayload, mockId).subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(mockError);
          done();
        }
      });
    });

    it('should propagate errors from the API service in addUser', (done): void => {
      // Arrange
      const mockPayload = { name: 'New User', email: '<EMAIL>', ProjectId: 123 };
      const mockError = 'API Error';

      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.addUser(mockPayload).subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(mockError);
          done();
        }
      });
    });
  });

  describe('Service dependencies', (): void => {
    it('should have ApiService injected', (): void => {
      // This test verifies that the ApiService is properly injected
      expect(TestBed.inject(ApiService)).toBeDefined();
    });
  });

  describe('Integration with components', (): void => {
    it('should provide user data in the format expected by user components', (): void => {
      // Arrange
      const mockPayload = { pageSize: 10, pageNo: 1 };

      const mockResponse = {
        data: {
          rows: [
            {
              id: 1,
              name: 'User 1',
              email: '<EMAIL>',
              role: 'Admin',
              createdAt: '2023-01-01T00:00:00.000Z',
              updatedAt: '2023-01-02T00:00:00.000Z'
            }
          ],
          count: 1
        }
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.usersList(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert - verify the structure matches what's expected in the component
      expect(result.data).toHaveProperty('rows');
      expect(result.data).toHaveProperty('count');
      expect(result.data.rows[0]).toHaveProperty('id');
      expect(result.data.rows[0]).toHaveProperty('name');
      expect(result.data.rows[0]).toHaveProperty('email');
      expect(result.data.rows[0]).toHaveProperty('role');
      expect(result.data.rows[0]).toHaveProperty('createdAt');
      expect(result.data.rows[0]).toHaveProperty('updatedAt');
    });
  });
});
