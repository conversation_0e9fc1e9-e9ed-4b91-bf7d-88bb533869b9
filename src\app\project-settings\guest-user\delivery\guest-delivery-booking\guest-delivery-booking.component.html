<ngx-loading [show]="modalLoader" [config]="{ backdropBorderRadius: '3px' }"> </ngx-loading>
<div class="modal-header p-3 border-bottom-grayb">
  <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">
    <img src="./assets/images/noun_request.svg" alt="Delivery" class="me-2" />New Delivery Booking
  </h1>
</div>
<div class="modal-body p-3">
  <div class="addcalendar-details">
    <form
      name="form"
      class="custom-material-form"
      id="deliverydetails-form3"
      [formGroup]="deliverDetailsForm"
      (ngSubmit)="onSubmit()"
      novalidate
    >
      <div class="row">
        <div class="col-md-6 float-start">
          <div class="form-group">
            <label class="fs12 fw600" for="description">Description<sup>*</sup></label>
            <textarea id="description"
              class="form-control fs11 radius0"
              placeholder="Enter Description"
              rows="2"
              formControlName="description"
              maxlength="150"
            ></textarea>
            <div
              class="color-red"
              *ngIf="submitted && deliverDetailsForm.get('description').errors"
            >
              <small *ngIf="deliverDetailsForm.get('description').errors.required"
                >*Description is Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6 float-start" *ngIf="!craneEquipmentTypeChosen">
          <div class="form-group mb-0">
            <label class="fs12 fw600" for="delId">Delivery ID</label>
            <input id="delId"
              type="text"
              class="form-control fs10 color-orange fw500 material-input ps-3"
              placeholder=""
              value="{{ lastId?.DeliveryId }}"
              disabled="disabled"
            />
          </div>
        </div>
        <div class="col-md-3 float-start" *ngIf="craneEquipmentTypeChosen">
          <div class="form-group mb-0">
            <label class="fs12 fw600" for="delId">Delivery ID</label>
            <input id="delId"
              type="text"
              class="form-control fs10 color-orange fw500 material-input ps-3"
              placeholder=""
              value="{{ lastId?.DeliveryId }}"
              disabled="disabled"
            />
          </div>
        </div>
        <div class="col-md-3 float-start" *ngIf="craneEquipmentTypeChosen">
          <div class="form-group mb-0">
            <label class="fs12 fw600" for="cranID">Crane Pick Booking ID</label>
            <input  id="cranID"
              type="text"
              class="form-control fs10 color-orange fw500 material-input ps-3"
              placeholder=""
              value="{{ craneRequestLastId }}"
              disabled="disabled"
            />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 float-start">
          <div class="form-group guest-company-select" id="company-select4">
            <label class="fs12 fw600"  for="resCompany">Responsible Company<sup>*</sup></label>
            <ng-multiselect-dropdown id="resCompany"
              [placeholder]="'Responsible Company*'"
              [settings]="newNdrCompanyDropdownSettings"
              [data]="companyList"
              formControlName="companyItems"
              [(ngModel)]="responsibleCompanySelectedItems"
            >
            </ng-multiselect-dropdown>
            <div
              class="color-red"
              *ngIf="submitted && deliverDetailsForm.get('companyItems').errors"
            >
              <small *ngIf="deliverDetailsForm.get('companyItems').errors.required"
                >*Company is Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6 float-start">
          <div class="form-group guest-company-select" id="company-select8">
            <label class="fs12 fw600" for="defineWork">Definable Feature Of Work</label>
            <ng-multiselect-dropdown  id="defineWork"
              [placeholder]="'Definable Feature Of Work (Scope)'"
              [settings]="newNdrDefinableDropdownSettings"
              [data]="defineList"
              formControlName="defineItems"
            >
            </ng-multiselect-dropdown>
            <div
              class="color-red"
              *ngIf="submitted && deliverDetailsForm.get('defineItems').errors"
            >
              <small *ngIf="deliverDetailsForm.get('defineItems').errors.required"
                >*Definable Feature Of Work is Required.</small
              >
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="form-group mb14">
            <label class="fs12 fw600"  for="resPerson">Responsible Person<sup>*</sup></label>
            <ul
              class="follo-switch list-group list-group-horizontal justify-content-end float-end newfixswitch"
              id="switch-control4"
            >
              <li class="fs12 list-group-item border-0 p-0 pe-3 escort--text py-0 float-start">
                Escort Needed?<sup>*</sup>
              </li>
              <li class="list-group-item border-0 px-0 py-0">
                <ui-switch
                  switchColor="#fff"
                  defaultBoColor="#CECECE"
                  defaultBgColor="#CECECE"
                  formControlName="escort"
                >
                </ui-switch>
              </li>
            </ul>

            <div class="float-start w-100 pt-3">
              <tag-input
                formControlName="person"
                [onlyFromAutocomplete]="true"
                [placeholder]="'Responsible Person *'"
                [onTextChangeDebounce]="500"
                class="tag-layout"
                [identifyBy]="'id'"
                [displayBy]="'email'"
              >
                <tag-input-dropdown
                  [showDropdownIfEmpty]="false"
                  [displayBy]="'email'"
                  [identifyBy]="'id'"
                  [autocompleteObservable]="requestAutocompleteItems"
                >
                  <ng-template let-item="item" let-index="index">
                    <span class="fs10">{{ item.email }}</span>
                  </ng-template>
                </tag-input-dropdown>
              </tag-input>
            </div>
            <div class="color-red" *ngIf="submitted && deliverDetailsForm.get('person').errors">
              <small *ngIf="deliverDetailsForm.get('person').errors.required"
                >*Please choose Responsible person.</small
              >
            </div>
          </div>
          <div class="form-group">
            <label class="fs12 fw600 mt-4"  for="gate">Gate<sup>*</sup></label>
            <select  id="gate"
              class="form-control select-dropdown fs12 material-input px-0 mt-1"
              formControlName="GateId"
            >
              <option value="" disabled selected hidden>Gate<sup>*</sup></option>
              <option *ngFor="let item of gateList" value="{{ item.id }}">
                {{ item.gateName }}
              </option>
            </select>
            <div class="color-red" *ngIf="submitted && deliverDetailsForm.get('GateId').errors">
              <small *ngIf="deliverDetailsForm.get('GateId').errors.required"
                >*Gate is Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="row">
            <div class="col-md-12">
              <label class="fs12 fw600"  for="delDate">Delivery Date<sup>*</sup></label>
              <div class="input-group mb-3">
                <input  id="delDate"
                  class="form-control fs12 ps-0 fw500 material-input"
                  #dp="bsDatepicker"
                  bsDatepicker
                  formControlName="deliveryDate"
                  placeholder="Delivery Date *"
                  [bsConfig]="{
                    isAnimated: true,
                    showWeekNumbers: false,
                    customTodayClass: 'today'
                  }"
                  (ngModelChange)="showMonthlyRecurrence()"
                />
                <div
                  class="color-red"
                  *ngIf="submitted && deliverDetailsForm.get('deliveryDate').errors"
                >
                  <small *ngIf="deliverDetailsForm.get('deliveryDate').errors.required"
                    >*Date is Required.</small
                  >
                </div>
                  <span class="input-group-text">
                    <img
                      src="./assets/images/date.svg"
                      class="h-12px"
                      alt="Date"
                      (click)="dp.toggle()"
                      (keydown) = "dp.toggle()"
                      [attr.aria-expanded]="dp.isOpen"
                    />
                  </span>
              </div>
            </div>
          </div>
          <div class="form-group mb-0 timezone-formgroup">
            <label for="timezone" class="fs12 fw600">Time Zone</label>
            <ng-multiselect-dropdown id="timezone"
              [placeholder]="'Choose TimeZone'"
              [settings]="dropdownSettings"
              [data]="timezoneList"
              (change)="timeZoneSelected($event.target.value)"
              [(ngModel)]="defaultTimeZone"
              formControlName="TimeZoneId"
            >
            </ng-multiselect-dropdown>
            <div class="color-red" *ngIf="submitted && deliverDetailsForm.get('TimeZoneId').errors">
              <small *ngIf="deliverDetailsForm.get('TimeZoneId').errors.required"
                >*TimeZone is Required.</small
              >
            </div>
          </div>
          <div class="row">
            <div class="col-md-12 timezone-flex mt-4">
              <div class="input-group mb-3 delivery-time">
                <label class="fs12 fw600" for="fromTme">From Time:</label>
                <timepicker id="fromTme"
                  [formControlName]="'deliveryStart'"
                  (ngModelChange)="changeDate($event)"
                  (keypress)="numberOnly($event)"
                ></timepicker>
                <div
                  class="color-red mt35"
                  *ngIf="submitted && deliverDetailsForm.get('deliveryStart').errors"
                >
                  <small *ngIf="deliverDetailsForm.get('deliveryStart').errors.required"
                    >*Start time is Required.</small
                  >
                </div>
              </div>

              <div class="input-group mb-3 delivery-time">
                <label class="fs12 fw600" for="toTme">To Time:</label>
                <timepicker  id="toTme"
                  [formControlName]="'deliveryEnd'"
                  (keypress)="numberOnly($event)"
                  (ngModelChange)="deliveryEndTimeChangeDetection()"
                ></timepicker>
                <div
                  class="color-red mt35"
                  *ngIf="submitted && deliverDetailsForm.get('deliveryEnd').errors"
                >
                  <small *ngIf="deliverDetailsForm.get('deliveryEnd').errors.required"
                    >*End time is Required.</small
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-6">
          <div class="form-group mt-1 mb-0 guest-company-select guestdelivery-equipment-select">
            <label class="fs12 fw600" for="equipment">Equipment<sup>*</sup></label>
            <ng-multiselect-dropdown id="equipment"
              [placeholder]="'Equipment'"
              [settings]="equipmentDropdownSettings"
              [data]="equipmentList"
              formControlName="EquipmentId"
              (ngModelChange)="checkEquipmentType($event)"
            >
            </ng-multiselect-dropdown>
            <div
              class="color-red"
              *ngIf="submitted && deliverDetailsForm.get('EquipmentId').errors"
            >
              <small *ngIf="deliverDetailsForm.get('EquipmentId').errors.required"
                >*Equipment is Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6 primary-tooltip">
          <div class="form-group mb-0 timezone-formgroup">
            <label class="fs12 fw600" for="location"
              >Location<sup>*</sup>
              <div class="dot-border-info location-border-info tooltip location-tooltip">
                <span class="fw700 info-icon fs12">i</span>
                <span class="tooltiptext tooltiptext-info"
                  >Where will the materials/equipment be installed</span
                >
                <div class="arrow-down"></div>
              </div>
            </label>
            <ng-multiselect-dropdown
              [placeholder]="'Choose Location'"
              [settings]="locationDropdownSettings"
              [data]="locationList"
              (onSelect)="locationSelected($event)"
              formControlName="LocationId"
              [(ngModel)]="defaultLocation"
            >
            </ng-multiselect-dropdown>
            <div class="color-red" *ngIf="submitted && deliverDetailsForm.get('LocationId').errors">
              <small *ngIf="deliverDetailsForm.get('LocationId').errors.required"
                >*Location is Required.</small
              >
            </div>
          </div>
        </div>
      </div>

      <div class="row"*ngIf="craneEquipmentTypeChosen">
          <div class="col-md-6 float-start">
            <div class="form-group mb-0">
              <label class="fs11 fw600" for="pickForm">Picking From<sup>*</sup></label>
              <textarea  id="pickForm"
                class="form-control fs10 radius0"
                placeholder="Picking From"
                rows="2"
                formControlName="cranePickUpLocation"
                maxlength="150"
              ></textarea>
            </div>
          </div>
          <div class="col-md-6 float-start">
            <div class="form-group mb-0">
              <label class="fs11 fw600"  for="pickTo">Picking To<sup>*</sup></label>
              <textarea  id="pickTo"
                class="form-control fs10 radius0"
                placeholder="Picking To"
                rows="2"
                formControlName="craneDropOffLocation"
                maxlength="150"
              ></textarea>
            </div>
          </div>
      </div>
      <div class="row pt-2">
        <div class="col-md-6">
          <!-- recurrance code start -->

          <div class="col-md-12 p-0">
            <label class="fs12 fw600 mb-0"  for="recurrence">Recurrence<sup>*</sup></label>
            <div class="form-group">
              <select  id="recurrence"
                class="form-control select-dropdown fs12 material-input px-0"
                formControlName="recurrence"
                (change)="onRecurrenceSelect($event.target.value)"
              >
                <option *ngFor="let type of recurrence" value="{{ type.value }}">
                  {{ type.value }}
                </option>
              </select>
            </div>
            <div
              class="row mt-2"
              *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
            >
              <div
                class="col-md-12 mt-md-0"
                *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
              >
                <label class="fs12 fw600 mb-0" for="repeatEvery">Repeat Every</label>
              </div>
              <div
                class="col-md-6 mt-md-0"
                *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
              >
                <div class="form-group">
                  <input id="repeatEvery"
                    type="text"
                    formControlName="repeatEveryCount"
                    class="form-control fs12 material-input p-0"
                    (input)="changeRecurrenceCount($event.target.value)"
                    min="1"
                  />
                </div>
              </div>

              <div class="col-md-6 mt-md-0" *ngIf="isRepeatWithSingleRecurrence">
                <div class="form-group">
                  <select
                    class="form-control select-dropdown fs12 material-input px-2"
                    formControlName="repeatEveryType"
                    (change)="chooseRepeatEveryType($event.target.value)"
                  >
                    <option value="" disabled selected hidden>Select Recurrence</option>
                    <option
                      *ngFor="let type of repeatWithSingleRecurrence"
                      value="{{ type.value }}"
                    >
                      {{ type.value }}
                    </option>
                  </select>
                </div>
              </div>
              <div
                class="col-md-4 mt-md-0"
                *ngIf="isRepeatWithMultipleRecurrence || showRecurrenceTypeDropdown"
              >
                <div class="form-group">
                  <select
                    class="form-control fs12 material-input px-2"
                    formControlName="repeatEveryType"
                    (change)="chooseRepeatEveryType($event.target.value)"
                  >
                    <option value="" disabled selected hidden>Select Recurrence</option>
                    <option
                      *ngFor="let type of repeatWithMultipleRecurrence"
                      value="{{ type.value }}"
                    >
                      {{ type.value }}
                    </option>
                  </select>
                </div>
              </div>
            </div>
            <div class="row addcalendar-displaydays">
              <div
                class="col-md-12 pt-0"
                *ngIf="
                  (selectedRecurrence === 'Weekly' ||
                    isRepeatWithMultipleRecurrence ||
                    isRepeatWithSingleRecurrence) &&
                  selectedRecurrence !== 'Monthly' &&
                  selectedRecurrence !== 'Yearly'
                "
              >
                <ul class="displaylists ps-0 mb-0">
                  <li *ngFor="let item of weekDays; let i = index" class="fs12 list-inline-item">
                    <input
                      type="checkbox"
                      [disabled]="item.isDisabled"
                      [value]="item.value"
                      class="d-none"
                      id="days-{{ i }}"
                      (change)="onChange($event)"
                      [checked]="item.checked"
                    />
                    <label for="days-{{ i }}">{{ item.display }}</label>
                  </li>
                  <div
                    class="color-red"
                    *ngIf="submitted && deliverDetailsForm.controls['days'].errors"
                  >
                    <small *ngIf="deliverDetailsForm.controls['days'].errors.required"
                      >*Required
                    </small>
                  </div>
                </ul>
              </div>
            </div>
            <div
              class="row"
              *ngIf="selectedRecurrence === 'Monthly' || selectedRecurrence === 'Yearly'"
              [ngClass]="selectedRecurrence === 'Yearly' ? 'recurrence-year' : ''"
            >
              <div class="col-md-12 mt-md-0 ps-2 pt-0 recurrance-column-day">
                <div class="w-100 float-start">
                  <div class="form-check">
                    <input
                      class="form-check-input c-pointer"
                      type="radio"
                      formControlName="chosenDateOfMonth"
                      id="flexRadioDefault1"
                      [value]="1"
                      (change)="changeMonthlyRecurrence()"
                    />
                    <label class="form-check-label fs12 color-orange" for="flexRadioDefault1">
                      On day {{ monthlyDate }}
                    </label>
                  </div>
                  <div class="form-check">
                    <input
                      class="form-check-input c-pointer"
                      type="radio"
                      formControlName="chosenDateOfMonth"
                      id="flexRadioDefault2"
                      [value]="2"
                      (change)="changeMonthlyRecurrence()"
                    />
                    <label class="form-check-label fs12 color-orange" for="flexRadioDefault2">
                      On the
                      {{ monthlyDayOfWeek }}
                      <p *ngIf="selectedRecurrence === 'Yearly'">
                        of
                        {{ deliverDetailsForm.get('deliveryDate').value | date : 'LLLL' }}
                      </p>
                    </label>
                  </div>
                  <div class="form-check" *ngIf="enableOption">
                    <input
                      class="form-check-input c-pointer"
                      type="radio"
                      formControlName="chosenDateOfMonth"
                      id="flexRadioDefault3"
                      [value]="3"
                      (change)="changeMonthlyRecurrence()"
                    />
                    <label class="form-check-label fs12 color-orange" for="flexRadioDefault3">
                      On the
                      {{ monthlyLastDayOfWeek }}
                      <p *ngIf="selectedRecurrence === 'Yearly'">
                        of
                        {{ deliverDetailsForm.get('deliveryDate').value | date : 'LLLL' }}
                      </p>
                    </label>
                  </div>
                </div>
                <div>
                  <div
                    class="color-red"
                    *ngIf="
                      submitted &&
                      (deliverDetailsForm.get('monthlyRepeatType')?.errors ||
                        deliverDetailsForm.get('dateOfMonth')?.errors)
                    "
                  >
                    <small *ngIf="deliverDetailsForm.get('monthlyRepeatType')?.errors?.required"
                      >*required</small
                    >
                    <small *ngIf="deliverDetailsForm.get('dateOfMonth')?.errors?.required"
                      >*required</small
                    >
                  </div>
                </div>
              </div>
            </div>
            <div class="row addcalendar-displaydays">
              <div class="col-md-12 mt-md-0 pb-0" *ngIf="message">
                <p class="fs12 color-grey11 mb-0">
                  <span class="color-red fw-bold">*</span>
                  {{ message }}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="col-md-12 float-start">
            <div
              class="row mt-0"
              *ngIf="
                selectedRecurrence === 'Daily' ||
                selectedRecurrence === 'Monthly' ||
                selectedRecurrence === 'Yearly' ||
                selectedRecurrence === 'Weekly'
              "
            >
              <div class="col-md-12 p-0">
                <label class="fs12 fw600 mb-0" for="endDate">End Date<sup>*</sup></label>
                <div class="input-group mb-3">
                  <input id="endDate"
                    class="form-control fs12 fw500 material-input"
                    #dp="bsDatepicker"
                    bsDatepicker
                    formControlName="endDate"
                    [minDate]="recurrenceMinDate"
                    placement="top"
                    placeholder="End Date *"
                    [bsConfig]="{
                      isAnimated: true,
                      showWeekNumbers: false,
                      customTodayClass: 'today'
                    }"
                    (ngModelChange)="showMonthlyRecurrence()"
                  />
                    <span class="input-group-text">
                      <img
                        src="./assets/images/date.svg"
                        class="h-12px"
                        alt="Date"
                        (click)="dp.toggle()" (keydown) ="dp.toggle()"
                        [attr.aria-expanded]="dp.isOpen"
                      />
                    </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-md-12">
          <div class="form-group">
            <label class="fs12 fw600" for="addNotes">Additional Notes</label>
            <textarea id="addNotes"
              class="form-control fs12 min-h-65px"
              placeholder=""
              rows="2"
              formControlName="notes"
            ></textarea>
            <div class="color-red" *ngIf="submitted && deliverDetailsForm.get('notes').errors">
              <small *ngIf="deliverDetailsForm.get('notes').errors.required"
                >*Notes is Required.</small
              >
            </div>
          </div>
        </div>
      </div>
      <div class="my-1 modal-footers text-center">
        <button
          class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3 px-2rem"
          type="button"
          (click)="close(cancelConfirmation)"
        >
          Cancel
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem"
          type="submit"
          [disabled]="formSubmitted && deliverDetailsForm.valid"
        >
          <em
            class="fa fa-spinner"
            aria-hidden="true"
            *ngIf="formSubmitted && deliverDetailsForm.valid"
          ></em
          >Submit
        </button>
      </div>
    </form>
  </div>
</div>
<!--Confirmation Popup-->
<div id="confirm-popup7">
  <ng-template #cancelConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure you want to cancel?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetForm('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="resetForm('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
