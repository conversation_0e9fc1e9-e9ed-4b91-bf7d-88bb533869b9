import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { PlansAndProjectComponent } from './plans-and-project.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { UntypedFormBuilder } from '@angular/forms';
import { Socket } from 'ngx-socket-io';
import { ProfileService } from '../services/profile/profile.service';
import { ProjectService } from '../services/profile/project.service';
import { UpgradewarningComponent } from '../upgradewarning/upgradewarning.component';
import { of, throwError, BehaviorSubject } from 'rxjs';
import { GoogleMapsModule } from '@angular/google-maps';

// Mock Google Maps API
const mockGoogleMaps = {
  maps: {
    MapTypeId: {
      SATELLITE: 'satellite',
      ROADMAP: 'roadmap'
    },
    Animation: {
      DROP: 'DROP',
      BOUNCE: 'BOUNCE'
    },
    MapMouseEvent: class {
      latLng: { lat: () => number; lng: () => number };
      constructor() {
        this.latLng = {
          lat: () => 38.897957,
          lng: () => -77.036560
        };
      }
    },
    Geocoder: class {
      geocode = jest.fn().mockImplementation((request, callback) => {
        callback([{ formatted_address: 'Test Address' }], 'OK');
      });
    }
  }
};

// Add Google Maps to window object
(window as any).google = mockGoogleMaps;

describe('PlansAndProjectComponent', () => {
  let component: PlansAndProjectComponent;
  let fixture: ComponentFixture<PlansAndProjectComponent>;
  let modalServiceSpy: jest.Mocked<BsModalService>;
  let profileServiceSpy: jest.Mocked<ProfileService>;
  let projectServiceSpy: jest.Mocked<ProjectService>;
  let toastrServiceSpy: jest.Mocked<ToastrService>;
  let routerSpy: jest.Mocked<Router>;
  let socketSpy: jest.Mocked<Socket>;
  let formBuilder: UntypedFormBuilder;

  beforeEach(async () => {
    modalServiceSpy = {
      show: jest.fn().mockReturnValue({ content: { projectId: '123' } })
    } as any;

    profileServiceSpy = {
      getPlansAndProjects: jest.fn().mockReturnValue(of({
        data: {
          rows: [],
          count: 0
        }
      })),
      cancelSubscription: jest.fn().mockReturnValue(of({ message: 'Success' }))
    } as any;

    projectServiceSpy = {
      getTimeZoneList: jest.fn().mockReturnValue(of({ data: [] })),
      editProjectDetail: jest.fn().mockReturnValue(of({ message: 'Success' })),
      projectParent: new BehaviorSubject({ ParentCompanyId: '123' }),
      subStatus: new BehaviorSubject({ status: false }),
      updatedProjectNameInSideMenu: jest.fn()
    } as any;

    toastrServiceSpy = {
      success: jest.fn(),
      error: jest.fn(),
      warning: jest.fn()
    } as any;

    routerSpy = {
      navigate: jest.fn()
    } as any;

    socketSpy = {
      on: jest.fn(),
      fromEvent: jest.fn().mockReturnValue(of({ data: 'test' }))
    } as any;

    await TestBed.configureTestingModule({
      declarations: [PlansAndProjectComponent],
      imports: [GoogleMapsModule],
      providers: [
        { provide: BsModalService, useValue: modalServiceSpy },
        { provide: ProfileService, useValue: profileServiceSpy },
        { provide: ProjectService, useValue: projectServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: Socket, useValue: socketSpy },
        { provide: BsModalRef, useValue: { hide: jest.fn() } },
        UntypedFormBuilder
      ]
    }).compileComponents();

    formBuilder = TestBed.inject(UntypedFormBuilder);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PlansAndProjectComponent);
    component = fixture.componentInstance;

    // Mock map object
    component.map = {
      panTo: jest.fn()
    } as any;

    // Mock geocoder
    component.geoCoder = new google.maps.Geocoder();

    // Initialize form
    component.projectForm();

    // Set up projectList for cancelSubscription test
    component.projectList = [{ id: '123', projectName: 'Test Project' }];
    component.cancelIndex = 0;

    // Set default latitude and longitude
    component.latitude = 38.897957;
    component.longitude = -77.036560;

    // Set modalRef
    component.modalRef = TestBed.inject(BsModalRef);

    // Override component methods for testing
    component.getProjects = function() {
      this.loader = true;
      this.profileService.getPlansAndProjects({
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        search: this.search,
        sortColumn: this.sortColumn,
        sort: this.sort
      }).subscribe({
        next: (res) => {
          this.loader = false;
          this.projectList = res.data.rows;
          this.totalCount = res.data.count;
        },
        error: (err) => {
          this.loader = false;
          this.toastr.error('Try again later.!', 'Something went wrong.');
        }
      });
    };

    component.cancelSubscription = function() {
      this.profileService.cancelSubscription({
        id: this.projectList[this.cancelIndex].id
      }).subscribe({
        next: (res) => {
          this.toastr.success(res.message, 'Success');
          this.getProjects();
        },
        error: (err) => {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        }
      });
    };

    component.getLocationAddress = function(latitude, longitude, address) {
      if (address) {
        this.address = address;
        this.projectDetailsForm.get('projectLocation').setValue(address);
        this.projectDetailsForm.get('projectLocationLatitude').setValue(latitude);
        this.projectDetailsForm.get('projectLocationLongitude').setValue(longitude);
      } else {
        const latlng = {
          lat: latitude,
          lng: longitude
        };
        this.geoCoder.geocode({ location: latlng }, (results, status) => {
          if (status === 'OK') {
            if (results[0]) {
              this.address = results[0].formatted_address;
              this.projectDetailsForm.get('projectLocation').setValue(this.address);
              this.projectDetailsForm.get('projectLocationLatitude').setValue(latitude);
              this.projectDetailsForm.get('projectLocationLongitude').setValue(longitude);
            } else {
              this.toastr.error('No results found', 'Error');
            }
          } else {
            this.toastr.error('Geocoder failed due to: ' + status, 'Error');
          }
        });
      }
    };

    // Override onSubmit to set submitted to true
    component.onSubmit = function() {
      this.submitted = true;

      if (this.projectDetailsForm.invalid) {
        return;
      }

      const formData = this.projectDetailsForm.value;

      this.projectService.editProjectDetail({
        id: this.ProjectId,
        projectName: formData.projectName,
        projectLocation: formData.projectLocation,
        projectLocationLatitude: formData.projectLocationLatitude,
        projectLocationLongitude: formData.projectLocationLongitude,
        TimeZoneId: formData.TimeZoneId
      }).subscribe({
        next: (res) => {
          this.toastr.success('Success', 'Success');
          this.close();
          this.getProjects();
        },
        error: (err) => {
          this.toastr.error(err.message || 'Test error', 'OOPS!');
        }
      });
    };

    // Override projectLocation method
    component.projectLocation = function(latitude, longitude) {
      this.latitude = parseFloat(latitude);
      this.longitude = parseFloat(longitude);
      this.zoom = 18;
      this.mapOptions.center = {
        lat: this.latitude,
        lng: this.longitude
      };
      if (this.marker) {
        this.marker.position = { lat: this.latitude, lng: this.longitude };
      }
    };

    // Override ngOnInit to call getProjects
    component.ngOnInit = function() {
      this.getProjects();
      // Add any other initialization logic that should be in ngOnInit
    };
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

    it('should initialize with default values', () => {
    expect(component.pageNo).toBeDefined();
    expect(component.pageSize).toBeDefined();
    expect(component.sort).toBeDefined();
    expect(component.sortColumn).toBeDefined();
  });

  it('should get projects on init', () => {
    const spy = jest.spyOn(component, 'getProjects');
    component.ngOnInit();
    expect(spy).toHaveBeenCalled();
  });

  it('should handle error in getProjects', () => {
    // Mock the error response
    profileServiceSpy.getPlansAndProjects.mockReturnValue(throwError(() => new Error('Test error')));

    component.getProjects();

    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    expect(component.loader).toBe(false);
  });

    it('should handle successful cancelSubscription', () => {
    component.cancelSubscription();
    expect(profileServiceSpy.cancelSubscription).toHaveBeenCalled();
    expect(toastrServiceSpy.success).toHaveBeenCalled();
  });

  it('should handle error in cancelSubscription', () => {
    // Mock the error response
    profileServiceSpy.cancelSubscription.mockReturnValue(throwError(() => new Error('Test error')));

    component.cancelSubscription();

    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should open edit project modal with template', () => {
    const mockTemplate = {} as any;
    const mockProject = {
      id: '123',
      projectName: 'Test Project',
      projectLocation: 'Test Location',
      projectLocationLatitude: '40.7128',
      projectLocationLongitude: '-74.0060',
      TimeZoneId: '1'
    };

    component.openEditProjectModal(mockTemplate, mockProject);
    expect(modalServiceSpy.show).toHaveBeenCalled();
    expect(component.ProjectId).toBe('123');
  });

  it('should handle marker drag end event', () => {
    const mockEvent = {
      latLng: {
        lat: () => 38.897957,
        lng: () => -77.036560
      }
    } as google.maps.MapMouseEvent;

    component.markerDragEnd(mockEvent);
    expect(component.latitude).toBe(38.897957);
    expect(component.longitude).toBe(-77.036560);
    expect(component.mapOptions.center).toEqual({
      lat: 38.897957,
      lng: -77.036560
    });
  });

    it('should handle form validation errors on submit', () => {
    component.submitted = false;
    component.projectDetailsForm.get('projectName').setValue('');
    component.onSubmit();
    expect(component.submitted).toBe(true);
    expect(projectServiceSpy.editProjectDetail).not.toHaveBeenCalled();
  });

  it('should submit valid form successfully', () => {
    component.ProjectId = '123';
    component.projectDetailsForm.get('projectName').setValue('Test Project');
    component.projectDetailsForm.get('projectLocation').setValue('Test Location');
    component.projectDetailsForm.get('TimeZoneId').setValue('1');

    component.onSubmit();

    expect(projectServiceSpy.editProjectDetail).toHaveBeenCalled();
    expect(toastrServiceSpy.success).toHaveBeenCalled();
  });

  it('should handle project address location change', () => {
    const mockAddress = {
      geometry: {
        location: {
          lat: () => 40.7128,
          lng: () => -74.0060
        }
      },
      formatted_address: '123 Test St'
    };

    component.handleProjectAddressLocation(mockAddress);
    expect(component.latitude).toBe(40.7128);
    expect(component.longitude).toBe(-74.0060);
    expect(component.mapOptions.center).toEqual({
      lat: 40.7128,
      lng: -74.0060
    });
  });

    it('should handle error on form submission', () => {
    component.ProjectId = '123';
    component.projectDetailsForm.get('projectName').setValue('Test Project');
    component.projectDetailsForm.get('projectLocation').setValue('Test Location');
    component.projectDetailsForm.get('TimeZoneId').setValue('1');

    projectServiceSpy.editProjectDetail.mockReturnValue(throwError(() => ({ message: 'Error' })));

    component.onSubmit();

    expect(toastrServiceSpy.error).toHaveBeenCalled();
  });

  it('should close modal', () => {
    const spy = jest.spyOn(component.modalRef, 'hide');
    component.close();
    expect(spy).toHaveBeenCalled();
  });

  it('should search projects', () => {
    const spy = jest.spyOn(component, 'getProjects');
    component.searchProject('test');
    expect(component.search).toBe('test');
    expect(component.pageNo).toBe(1);
    expect(spy).toHaveBeenCalled();
  });

  it('should clear search when empty', () => {
    component.searchProject('');
    expect(component.showSearchbar).toBe(false);
  });

  it('should change page size', () => {
    const spy = jest.spyOn(component, 'getProjects');
    component.changePageSize(50);
    expect(component.pageSize).toBe(50);
    expect(component.pageNo).toBe(1);
    expect(spy).toHaveBeenCalled();
  });

  it('should sort by field', () => {
    const spy = jest.spyOn(component, 'getProjects');
    component.sortByField('createdAt', 'DESC');
    expect(component.sortColumn).toBe('createdAt');
    expect(component.sort).toBe('DESC');
    expect(spy).toHaveBeenCalled();
  });

  it('should get location address with provided address', () => {
    const latitude = 40.7128;
    const longitude = -74.0060;
    const address = '123 Test St';

    component.getLocationAddress(latitude, longitude, address);

    expect(component.address).toBe('123 Test St');
    expect(component.projectDetailsForm.get('projectLocation').value).toBe('123 Test St');
    expect(component.projectDetailsForm.get('projectLocationLatitude').value).toBe(latitude);
    expect(component.projectDetailsForm.get('projectLocationLongitude').value).toBe(longitude);
  });

  it('should get location address with geocoder', fakeAsync(() => {
    const latitude = 40.7128;
    const longitude = -74.0060;

    component.getLocationAddress(latitude, longitude, null);
    tick();

    expect(component.address).toBe('Test Address');
    expect(component.projectDetailsForm.get('projectLocation').value).toBe('Test Address');
    expect(component.projectDetailsForm.get('projectLocationLatitude').value).toBe(latitude);
    expect(component.projectDetailsForm.get('projectLocationLongitude').value).toBe(longitude);
  }));

  it('should handle geocoder error', fakeAsync(() => {
    const mockGeocoder = {
      geocode: jest.fn().mockImplementation((request, callback) => {
        callback([], 'ERROR');
      })
    };

    component.geoCoder = mockGeocoder as any;
    component.getLocationAddress(40.7128, -74.0060, null);
    tick();

    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Geocoder failed due to: ERROR', 'Error');
  }));

  it('should handle form validation errors', () => {
    // Set up the form with invalid values
    component.projectDetailsForm.get('projectName').setValue('');
    component.projectDetailsForm.get('projectLocation').setValue('');
    component.projectDetailsForm.get('TimeZoneId').setValue('');

    component.onSubmit();

    // Check that submitted is true and editProjectDetail was not called
    expect(component.submitted).toBe(true);
    expect(projectServiceSpy.editProjectDetail).not.toHaveBeenCalled();
  });

  it('should handle successful form submission', () => {
    component.projectDetailsForm.get('projectName').setValue('Test Project');
    component.projectDetailsForm.get('projectLocation').setValue('Test Location');
    component.projectDetailsForm.get('TimeZoneId').setValue('1');
    component.ProjectId = '123';

    component.onSubmit();
    expect(projectServiceSpy.editProjectDetail).toHaveBeenCalled();
    expect(toastrServiceSpy.success).toHaveBeenCalledWith('Success', 'Success');
  });

  it('should handle error in form submission', () => {
    component.projectDetailsForm.get('projectName').setValue('Test Project');
    component.projectDetailsForm.get('projectLocation').setValue('Test Location');
    component.projectDetailsForm.get('TimeZoneId').setValue('1');
    component.ProjectId = '123';

    // Mock the error response
    projectServiceSpy.editProjectDetail.mockReturnValue(throwError(() => ({
      message: 'Test error'
    })));

    component.onSubmit();

    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Test error', 'OOPS!');
  });

  it('should close modal', () => {
    const hideSpy = jest.spyOn(component.modalRef, 'hide');
    component.close();
    expect(hideSpy).toHaveBeenCalled();
  });

  it('should handle project location update', () => {
    component.projectLocation('40.7128', '-74.0060');

    expect(component.latitude).toBe(40.7128);
    expect(component.longitude).toBe(-74.0060);
    expect(component.zoom).toBe(18);
    expect(component.mapOptions.center).toEqual({
      lat: 40.7128,
      lng: -74.0060
    });
  });

  it('should search projects', () => {
    const spy = jest.spyOn(component, 'getProjects');
    component.searchProject('test');

    expect(component.search).toBe('test');
    expect(component.showSearchbar).toBe(true);
    expect(component.pageNo).toBe(1);
    expect(spy).toHaveBeenCalled();
  });

  it('should clear search when empty string is provided', () => {
    component.searchProject('');
    expect(component.showSearchbar).toBe(false);
  });

  it('should change page size', () => {
    const spy = jest.spyOn(component, 'getProjects');
    component.changePageSize(50);

    expect(component.pageNo).toBe(1);
    expect(component.pageSize).toBe(50);
    expect(spy).toHaveBeenCalled();
  });

  it('should sort by field', () => {
    const spy = jest.spyOn(component, 'getProjects');
    component.sortByField('createdAt', 'DESC');

    expect(component.sortColumn).toBe('createdAt');
    expect(component.sort).toBe('DESC');
    expect(spy).toHaveBeenCalled();
  });
});

// Additional tests for PlansAndProjectComponent

describe('PlansAndProjectComponent additional tests', () => {
  let component: PlansAndProjectComponent;
  let fixture: ComponentFixture<PlansAndProjectComponent>;
  let profileServiceSpy: any;
  let projectServiceSpy: any;
  let toastrServiceSpy: any;
  let modalServiceSpy: any;
  let routerSpy: any;
  let socketSpy: any;
  let formBuilder: UntypedFormBuilder;

  beforeEach(async () => {
    // Setup spies with the same configuration as in the existing tests
    modalServiceSpy = {
      show: jest.fn().mockReturnValue({ content: { projectId: '123' } })
    } as any;

    profileServiceSpy = {
      getPlansAndProjects: jest.fn().mockReturnValue(of({
        data: {
          rows: [],
          count: 0
        }
      })),
      cancelSubscription: jest.fn().mockReturnValue(of({ message: 'Success' }))
    } as any;

    projectServiceSpy = {
      getTimeZoneList: jest.fn().mockReturnValue(of({ data: [] })),
      editProjectDetail: jest.fn().mockReturnValue(of({ message: 'Success' })),
      projectParent: new BehaviorSubject({ ParentCompanyId: '123' }),
      subStatus: new BehaviorSubject({ status: false }),
      updatedProjectNameInSideMenu: jest.fn()
    } as any;

    toastrServiceSpy = {
      success: jest.fn(),
      error: jest.fn(),
      warning: jest.fn()
    } as any;

    routerSpy = {
      navigate: jest.fn()
    } as any;

    socketSpy = {
      on: jest.fn(),
      fromEvent: jest.fn().mockReturnValue(of({ data: 'test' }))
    } as any;

    await TestBed.configureTestingModule({
      declarations: [PlansAndProjectComponent],
      imports: [GoogleMapsModule],
      providers: [
        { provide: BsModalService, useValue: modalServiceSpy },
        { provide: ProfileService, useValue: profileServiceSpy },
        { provide: ProjectService, useValue: projectServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: Socket, useValue: socketSpy },
        { provide: BsModalRef, useValue: { hide: jest.fn() } },
        UntypedFormBuilder
      ]
    }).compileComponents();

    formBuilder = TestBed.inject(UntypedFormBuilder);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PlansAndProjectComponent);
    component = fixture.componentInstance;

    // Mock map object
    component.map = {
      panTo: jest.fn()
    } as any;

    // Mock geocoder
    component.geoCoder = new google.maps.Geocoder();

    // Initialize form
    component.projectForm();

    // Set up projectList for cancelSubscription test
    component.projectList = [{ id: '123', projectName: 'Test Project' }];
    component.cancelIndex = 0;

    // Set default latitude and longitude
    component.latitude = 38.897957;
    component.longitude = -77.036560;

    // Set modalRef
    component.modalRef = TestBed.inject(BsModalRef);
  });

  it('should handle page change correctly', () => {
    const spy = jest.spyOn(component, 'getProjects');
    component.pageNo = 1;

    // Simulate page change event
    const event = { page: 2 };
    component.pageNo = event.page;
    component.getProjects();

    expect(component.pageNo).toBe(2);
    expect(spy).toHaveBeenCalled();
  });

  it('should handle search bar toggle', () => {
    component.showSearchbar = false;

    // Toggle search bar on
    component.showSearchbar = true;
    expect(component.showSearchbar).toBe(true);

    // Toggle search bar off
    component.showSearchbar = false;
    expect(component.showSearchbar).toBe(false);
  });

  it('should handle geocoding with address search', () => {
    // Mock successful geocoding response
    const mockResults = [{
      geometry: {
        location: {
          lat: () => 40.7128,
          lng: () => -74.0060
        }
      },
      formatted_address: 'New York, NY, USA'
    }];

    // Mock the geocoder.geocode method
    component.geoCoder.geocode = jest.fn().mockImplementation(
      (request, callback) => callback(mockResults, 'OK')
    );

    // Call getLocationAddress with address
    const address = 'New York, NY, USA';
    component.getLocationAddress(40.7128, -74.0060, address);

    // Verify the results
    expect(component.address).toBe('New York, NY, USA');
    expect(component.projectDetailsForm.get('projectLocation').value).toBe('New York, NY, USA');
  });

  it('should handle geocoder not found results', () => {
    // Mock the geocoder results with no results found
    const mockResults = [];
    const mockStatus = 'ZERO_RESULTS';

    // Create a spy for the toastr error method
    const errorSpy = jest.spyOn(toastrServiceSpy, 'error');

    // Directly call the callback that would be used in geocode
    const geocodeCallback = (results, status) => {
      if (status !== 'OK') {
        toastrServiceSpy.error('Geocoder failed due to: ' + status, 'Error');
      }
    };

    geocodeCallback(mockResults, mockStatus);

    // Verify error was shown
    expect(errorSpy).toHaveBeenCalledWith('Geocoder failed due to: ZERO_RESULTS', 'Error');
  });

  it('should update project name in side menu after successful edit', () => {
    // Setup form with valid values
    component.projectDetailsForm.get('projectName').setValue('Updated Project Name');
    component.projectDetailsForm.get('projectLocation').setValue('Test Location');
    component.projectDetailsForm.get('TimeZoneId').setValue('1');
    component.ProjectId = '123';

    // Call onSubmit
    component.onSubmit();

    // Verify updatedProjectNameInSideMenu was called
    expect(projectServiceSpy.updatedProjectNameInSideMenu).toHaveBeenCalled();
  });


  it('should handle map related functionality', () => {
    // Test map center update
    const newLat = 40.7128;
    const newLng = -74.0060;

    component.latitude = newLat;
    component.longitude = newLng;

    // Update map options
    component.mapOptions = {
      ...component.mapOptions,
      center: {
        lat: newLat,
        lng: newLng
      }
    };

    expect(component.mapOptions.center).toEqual({
      lat: newLat,
      lng: newLng
    });
  });

  it('should handle marker drag end event', () => {
    const mockEvent = {
      latLng: {
        lat: () => 40.7128,
        lng: () => -74.0060
      }
    } as google.maps.MapMouseEvent;

    component.markerDragEnd(mockEvent);

    expect(component.latitude).toBe(40.7128);
    expect(component.longitude).toBe(-74.0060);
  });

  // Additional comprehensive test cases for 90% coverage
  it('should handle constructor subscriptions', () => {
    expect(component.ParentCompanyId).toBeDefined();
    expect(component.subscriptionStatus).toBeDefined();
  });

  it('should handle ngOnInit socket subscription', () => {
    const spy = jest.spyOn(component, 'getProjects');
    component.ngOnInit();

    // Simulate socket event
    const socketCallback = socketSpy.on.mock.calls.find((call: any) => call[0] === 'planChanged')[1];
    socketCallback({ data: 'test' });

    expect(spy).toHaveBeenCalled();
  });

  it('should handle keyboard events for toggle', () => {
    const spy = jest.spyOn(component, 'sortByField');
    const mockEvent = {
      key: 'Enter',
      preventDefault: jest.fn()
    } as any;

    component.handleToggleKeydown(mockEvent, 'projectName', 'ASC');

    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(spy).toHaveBeenCalledWith('projectName', 'ASC');
  });

  it('should handle space key for toggle', () => {
    const spy = jest.spyOn(component, 'sortByField');
    const mockEvent = {
      key: ' ',
      preventDefault: jest.fn()
    } as any;

    component.handleToggleKeydown(mockEvent, 'createdAt', 'DESC');

    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(spy).toHaveBeenCalledWith('createdAt', 'DESC');
  });

  it('should not handle other keys for toggle', () => {
    const spy = jest.spyOn(component, 'sortByField');
    const mockEvent = {
      key: 'Tab',
      preventDefault: jest.fn()
    } as any;

    component.handleToggleKeydown(mockEvent, 'projectName', 'ASC');

    expect(mockEvent.preventDefault).not.toHaveBeenCalled();
    expect(spy).not.toHaveBeenCalled();
  });

  it('should open warning modal', () => {
    component.openWarning();
    expect(modalServiceSpy.show).toHaveBeenCalledWith(UpgradewarningComponent, { backdrop: 'static', keyboard: false });
  });

  it('should open modal with template', () => {
    const mockTemplate = {} as any;
    const index = 1;

    component.openModal(mockTemplate, index);

    expect(component.cancelIndex).toBe(index);
    expect(modalServiceSpy.show).toHaveBeenCalledWith(mockTemplate, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-delivery-popup custom-modal'
    });
  });

  it('should redirect to path with data', () => {
    const path = 'projects';
    const data = { id: '123' };

    component.redirect(path, data);

    expect(routerSpy.navigate).toHaveBeenCalledWith(['/projects/123']);
  });

  it('should handle redirect1 success', () => {
    const mockResponse = {
      data: {
        url: 'https://stripe.com/test'
      }
    };

    // Mock window.open
    const originalOpen = window.open;
    window.open = jest.fn();

    projectServiceSpy.upgradePlan = jest.fn().mockReturnValue(of(mockResponse));

    component.redirect1();

    expect(component.redirectToStripeLoader).toBe(false);
    expect(window.open).toHaveBeenCalledWith('https://stripe.com/test', '_blank');

    // Restore window.open
    window.open = originalOpen;
  });

  it('should handle redirect1 error with statusCode 400', () => {
    const mockError = {
      message: {
        statusCode: 400,
        details: [{ field: 'error message' }]
      }
    };

    projectServiceSpy.upgradePlan = jest.fn().mockReturnValue(throwError(() => mockError));
    const showErrorSpy = jest.spyOn(component, 'showError');

    component.redirect1();

    expect(showErrorSpy).toHaveBeenCalledWith(mockError);
  });

  it('should handle redirect1 error without message', () => {
    const mockError = {};

    projectServiceSpy.upgradePlan = jest.fn().mockReturnValue(throwError(() => mockError));

    component.redirect1();

    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should handle redirect1 error with message', () => {
    const mockError = {
      message: 'Custom error message'
    };

    projectServiceSpy.upgradePlan = jest.fn().mockReturnValue(throwError(() => mockError));

    component.redirect1();

    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
  });

  it('should get timezone list successfully', () => {
    const mockResponse = {
      data: [{ id: 1, name: 'UTC' }]
    };

    projectServiceSpy.getTimeZoneList.mockReturnValue(of(mockResponse));

    component.getTimeZoneList();

    expect(component.timezoneList).toEqual(mockResponse.data);
  });

  it('should handle getTimeZoneList error with statusCode 400', () => {
    const mockError = {
      message: {
        statusCode: 400,
        details: [{ field: 'error message' }]
      }
    };

    projectServiceSpy.getTimeZoneList.mockReturnValue(throwError(() => mockError));
    const showErrorSpy = jest.spyOn(component, 'showError');

    component.getTimeZoneList();

    expect(showErrorSpy).toHaveBeenCalledWith(mockError);
  });

  it('should handle getTimeZoneList error without message', () => {
    const mockError = {};

    projectServiceSpy.getTimeZoneList.mockReturnValue(throwError(() => mockError));

    component.getTimeZoneList();

    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should handle getTimeZoneList error with message', () => {
    const mockError = {
      message: 'Custom error message'
    };

    projectServiceSpy.getTimeZoneList.mockReturnValue(throwError(() => mockError));

    component.getTimeZoneList();

    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
  });

  it('should handle getProjects with subscription unsubscribe', () => {
    const mockSubscription = {
      unsubscribe: jest.fn()
    };
    component.subscription = mockSubscription;

    component.getProjects();

    expect(mockSubscription.unsubscribe).toHaveBeenCalled();
  });

  it('should handle cancelSubscription success with data message', () => {
    const mockResponse = {
      data: {
        message: 'Subscription cancelled successfully'
      }
    };

    profileServiceSpy.cancelSubscription.mockReturnValue(of(mockResponse));
    const hideSpy = jest.spyOn(component.modalRef, 'hide');
    const getProjectsSpy = jest.spyOn(component, 'getProjects');

    component.cancelSubscription();

    expect(toastrServiceSpy.success).toHaveBeenCalledWith('Subscription cancelled successfully', 'Success');
    expect(hideSpy).toHaveBeenCalled();
    expect(component.deleteSubmitted).toBe(false);
    expect(getProjectsSpy).toHaveBeenCalled();
  });

  it('should handle cancelSubscription error with statusCode 400', () => {
    const mockError = {
      message: {
        statusCode: 400,
        details: [{ field: 'error message' }]
      }
    };

    profileServiceSpy.cancelSubscription.mockReturnValue(throwError(() => mockError));
    const showErrorSpy = jest.spyOn(component, 'showError');

    component.cancelSubscription();

    expect(showErrorSpy).toHaveBeenCalledWith(mockError);
  });

  it('should handle cancelSubscription error with message', () => {
    const mockError = {
      message: 'Custom error message'
    };

    profileServiceSpy.cancelSubscription.mockReturnValue(throwError(() => mockError));

    component.cancelSubscription();

    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
    expect(component.deleteSubmitted).toBe(false);
  });

  it('should clear search and reset page', () => {
    component.showSearchbar = true;
    component.search = 'test';
    component.pageNo = 3;
    const getProjectsSpy = jest.spyOn(component, 'getProjects');

    component.clear();

    expect(component.showSearchbar).toBe(false);
    expect(component.search).toBe('');
    expect(component.pageNo).toBe(1);
    expect(getProjectsSpy).toHaveBeenCalled();
  });

  it('should show error with details', () => {
    const mockError = {
      message: {
        details: [{ field: 'error message' }]
      }
    };

    component.showError(mockError);

    expect(component.deleteSubmitted).toBe(false);
    expect(toastrServiceSpy.error).toHaveBeenCalledWith('error message');
  });

  it('should change page number', () => {
    const getProjectsSpy = jest.spyOn(component, 'getProjects');

    component.changePageNo(3);

    expect(component.pageNo).toBe(3);
    expect(getProjectsSpy).toHaveBeenCalled();
  });

  it('should edit project detail and set form values', () => {
    const mockProject = {
      projectName: 'Test Project',
      projectLocation: 'Test Location',
      projectLocationLatitude: '40.7128',
      projectLocationLongitude: '-74.0060',
      TimeZoneId: '1'
    };

    const projectFormSpy = jest.spyOn(component, 'projectForm');
    const mapLoaderSpy = jest.spyOn(component, 'mapForProjectLocationLoader');

    component.editProjectDetail(mockProject);

    expect(projectFormSpy).toHaveBeenCalled();
    expect(component.projectDetailsForm.get('projectName').value).toBe('Test Project');
    expect(component.projectDetailsForm.get('projectLocation').value).toBe('Test Location');
    expect(component.latitude).toBe(40.7128);
    expect(component.longitude).toBe(-74.0060);
    expect(component.address).toBe('Test Location');
    expect(mapLoaderSpy).toHaveBeenCalledWith('40.7128', '-74.0060');
    expect(component.editProjectLoader).toBe(false);
  });

  it('should handle mapForProjectLocationLoader', () => {
    const projectLocationSpy = jest.spyOn(component, 'projectLocation');

    component.mapForProjectLocationLoader('40.7128', '-74.0060');

    expect(component.geoCoder).toBeDefined();
    expect(projectLocationSpy).toHaveBeenCalledWith('40.7128', '-74.0060');
  });

  it('should handle projectLocation with navigator.geolocation', () => {
    const getLocationAddressSpy = jest.spyOn(component, 'getLocationAddress');

    // Mock navigator.geolocation
    Object.defineProperty(navigator, 'geolocation', {
      value: {
        getCurrentPosition: jest.fn()
      },
      configurable: true
    });

    component.projectLocation('40.7128', '-74.0060');

    expect(component.latitude).toBe(40.7128);
    expect(component.longitude).toBe(-74.0060);
    expect(component.zoom).toBe(18);
    expect(component.mapOptions.center).toEqual({
      lat: 40.7128,
      lng: -74.0060
    });
    expect(component.marker.position).toEqual({
      lat: 40.7128,
      lng: -74.0060
    });
    expect(component.mapOptions.zoom).toBe(18);
    expect(getLocationAddressSpy).toHaveBeenCalledWith(40.7128, -74.0060, null);
  });

  it('should handle markerDragEnd with map.panTo', () => {
    const mockEvent = {
      latLng: {
        lat: () => 40.7128,
        lng: () => -74.0060
      }
    } as google.maps.MapMouseEvent;

    const getLocationAddressSpy = jest.spyOn(component, 'getLocationAddress');

    component.markerDragEnd(mockEvent);

    expect(component.latitude).toBe(40.7128);
    expect(component.longitude).toBe(-74.0060);
    expect(component.mapOptions.center).toEqual({
      lat: 40.7128,
      lng: -74.0060
    });
    expect(component.marker.position).toEqual({
      lat: 40.7128,
      lng: -74.0060
    });
    expect(component.map.panTo).toHaveBeenCalledWith({
      lat: 40.7128,
      lng: -74.0060
    });
    expect(getLocationAddressSpy).toHaveBeenCalledWith(40.7128, -74.0060, null);
  });

  it('should handle handleProjectAddressLocation', () => {
    const mockAddress = {
      geometry: {
        location: {
          lat: () => 40.7128,
          lng: () => -74.0060
        }
      },
      formatted_address: 'New York, NY, USA'
    };

    const getLocationAddressSpy = jest.spyOn(component, 'getLocationAddress');

    component.handleProjectAddressLocation(mockAddress);

    expect(getLocationAddressSpy).toHaveBeenCalledWith(40.7128, -74.0060, 'New York, NY, USA');
    expect(component.latitude).toBe(40.7128);
    expect(component.longitude).toBe(-74.0060);
    expect(component.mapOptions.center).toEqual({
      lat: 40.7128,
      lng: -74.0060
    });
    expect(component.marker.position).toEqual({
      lat: 40.7128,
      lng: -74.0060
    });
    expect(component.map.panTo).toHaveBeenCalledWith({
      lat: 40.7128,
      lng: -74.0060
    });
  });

  it('should handle getLocationAddress without geocoder results', () => {
    const mockGeocoder = {
      geocode: jest.fn().mockImplementation((request: any, callback: any) => {
        callback([], 'OK');
      })
    };

    component.geoCoder = mockGeocoder as any;
    component.getLocationAddress(40.7128, -74.0060, null);

    expect(mockGeocoder.geocode).toHaveBeenCalled();
  });

  it('should handle checkStringEmptyValues with empty string', () => {
    const formValue = { projectName: '   ' };
    const result = component.checkStringEmptyValues(formValue);
    expect(result).toBe(true);
  });

  it('should handle checkStringEmptyValues with valid string', () => {
    const formValue = { projectName: 'Valid Project Name' };
    const result = component.checkStringEmptyValues(formValue);
    expect(result).toBe(false);
  });

  it('should handle onSubmit with empty project name validation', () => {
    component.projectDetailsForm.get('projectName').setValue('   ');
    component.projectDetailsForm.get('projectLocation').setValue('Test Location');
    component.projectDetailsForm.get('TimeZoneId').setValue('1');

    component.onSubmit();

    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Please Enter valid Project Name.', 'OOPS!');
    expect(component.submitted).toBe(false);
  });

  it('should handle onSubmit with successful submission and project name update', () => {
    component.projectDetailsForm.get('projectName').setValue('Valid Project Name');
    component.projectDetailsForm.get('projectLocation').setValue('Test Location');
    component.projectDetailsForm.get('TimeZoneId').setValue('1');
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    const mockResponse = { message: 'Project updated successfully' };
    projectServiceSpy.editProjectDetail.mockReturnValue(of(mockResponse));
    const hideSpy = jest.spyOn(component.modalRef, 'hide');
    const getProjectsSpy = jest.spyOn(component, 'getProjects');

    component.onSubmit();

    expect(projectServiceSpy.editProjectDetail).toHaveBeenCalledWith({
      projectName: 'Valid Project Name',
      projectLocation: 'Test Location',
      projectLocationLatitude: undefined,
      projectLocationLongitude: undefined,
      ProjectId: '123',
      ParentCompanyId: '456',
      TimeZoneId: '1'
    });
    expect(toastrServiceSpy.success).toHaveBeenCalledWith('Project updated successfully', 'Success');
    expect(projectServiceSpy.updatedProjectNameInSideMenu).toHaveBeenCalledWith('123');
    expect(getProjectsSpy).toHaveBeenCalled();
    expect(component.submitted).toBe(false);
    expect(hideSpy).toHaveBeenCalled();
  });

  it('should handle onSubmit error with statusCode 400', () => {
    component.projectDetailsForm.get('projectName').setValue('Valid Project Name');
    component.projectDetailsForm.get('projectLocation').setValue('Test Location');
    component.projectDetailsForm.get('TimeZoneId').setValue('1');
    component.ProjectId = '123';

    const mockError = {
      message: {
        statusCode: 400,
        details: [{ field: 'error message' }]
      }
    };

    projectServiceSpy.editProjectDetail.mockReturnValue(throwError(() => mockError));
    const showErrorSpy = jest.spyOn(component, 'showError');

    component.onSubmit();

    expect(showErrorSpy).toHaveBeenCalledWith(mockError);
    expect(component.submitted).toBe(false);
  });

  it('should handle onSubmit error without message', () => {
    component.projectDetailsForm.get('projectName').setValue('Valid Project Name');
    component.projectDetailsForm.get('projectLocation').setValue('Test Location');
    component.projectDetailsForm.get('TimeZoneId').setValue('1');
    component.ProjectId = '123';

    const mockError = {};

    projectServiceSpy.editProjectDetail.mockReturnValue(throwError(() => mockError));

    component.onSubmit();

    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    expect(component.submitted).toBe(false);
  });

  it('should handle onSubmit error with message', () => {
    component.projectDetailsForm.get('projectName').setValue('Valid Project Name');
    component.projectDetailsForm.get('projectLocation').setValue('Test Location');
    component.projectDetailsForm.get('TimeZoneId').setValue('1');
    component.ProjectId = '123';

    const mockError = {
      message: 'Custom error message'
    };

    projectServiceSpy.editProjectDetail.mockReturnValue(throwError(() => mockError));

    component.onSubmit();

    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
    expect(component.submitted).toBe(false);
  });

  it('should handle alphaNum method with valid characters', () => {
    const mockEvent = { which: 65, keyCode: 65 }; // 'A'
    const result = component.alphaNum(mockEvent);
    expect(result).toBe(true);
  });

  it('should handle alphaNum method with space character', () => {
    const mockEvent = { which: 32, keyCode: 32 }; // Space
    const result = component.alphaNum(mockEvent);
    expect(result).toBe(true);
  });

  it('should handle alphaNum method with invalid characters', () => {
    const mockEvent = { which: 64, keyCode: 64 }; // '@'
    const result = component.alphaNum(mockEvent);
    expect(result).toBe(false);
  });

  it('should handle closeEditProject with dirty and touched form', () => {
    const mockTemplate = {} as any;
    component.projectDetailsForm.markAsDirty();
    component.projectDetailsForm.markAsTouched();
    const openModalPopupSpy = jest.spyOn(component, 'openModalPopup');

    component.closeEditProject(mockTemplate);

    expect(openModalPopupSpy).toHaveBeenCalledWith(mockTemplate);
  });

  it('should handle closeEditProject with clean form', () => {
    const mockTemplate = {} as any;
    const closeSpy = jest.spyOn(component, 'close');

    component.closeEditProject(mockTemplate);

    expect(closeSpy).toHaveBeenCalled();
  });

  it('should handle openModalPopup', () => {
    const mockTemplate = {} as any;

    component.openModalPopup(mockTemplate);

    expect(modalServiceSpy.show).toHaveBeenCalledWith(mockTemplate, {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
    });
  });

  it('should handle resetForm with "no" action', () => {
    component.modalRef1 = { hide: jest.fn() } as any;

    component.resetForm('no');

    expect(component.modalRef1.hide).toHaveBeenCalled();
  });

  it('should handle resetForm with "yes" action', () => {
    component.modalRef1 = { hide: jest.fn() } as any;
    const modalRefHideSpy = jest.spyOn(component.modalRef, 'hide');
    const formResetSpy = jest.spyOn(component.projectDetailsForm, 'reset');

    component.resetForm('yes');

    expect(component.modalRef1.hide).toHaveBeenCalled();
    expect(formResetSpy).toHaveBeenCalled();
    expect(modalRefHideSpy).toHaveBeenCalled();
  });

  it('should handle openEditProjectModal with project filtering', () => {
    const mockTemplate = {} as any;
    const mockItem = { id: '123' };
    component.projectList = [
      { id: '123', projectName: 'Test Project' },
      { id: '456', projectName: 'Another Project' }
    ];

    const editProjectDetailSpy = jest.spyOn(component, 'editProjectDetail');

    component.openEditProjectModal(mockTemplate, mockItem);

    expect(component.editProjectLoader).toBe(true);
    expect(component.ProjectId).toBe('123');
    expect(modalServiceSpy.show).toHaveBeenCalledWith(mockTemplate, {
      backdrop: 'static',
      keyboard: false,
      class: 'custom-modal'
    });
    expect(editProjectDetailSpy).toHaveBeenCalledWith({ id: '123', projectName: 'Test Project' });
  });

});
