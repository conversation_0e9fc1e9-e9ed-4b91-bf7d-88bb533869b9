import { Location } from '@angular/common';
import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { Router } from '@angular/router';
import { routes } from './app-routing.module';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { AuthGuard } from './auth.guard';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

// Create a simpler approach that doesn't require all components
describe('AppRoutingModule', () => {
  let location: Location;
  let router: Router;
  let authGuard: AuthGuard;

  beforeEach(() => {
    // Create a mock for AuthGuard that always returns true
    const authGuardMock = {
      canActivate: jest.fn().mockReturnValue(true),
      ProjectId: -1,
      getProjectInfo: jest.fn()
    };

    // Mock WrappedSocket
    const socketMock = {
      on: jest.fn(),
      emit: jest.fn(),
      connect: jest.fn(),
      disconnect: jest.fn()
    };

    TestBed.configureTestingModule({
      imports: [
        RouterTestingModule.withRoutes(routes),
        HttpClientTestingModule,
        BrowserAnimationsModule
      ],
      providers: [
        { provide: AuthGuard, useValue: authGuardMock },
        { provide: 'WrappedSocket', useValue: socketMock }
      ],
      schemas: [NO_ERRORS_SCHEMA] // Ignore unknown elements and attributes
    });

    router = TestBed.inject(Router);
    location = TestBed.inject(Location);
    authGuard = TestBed.inject(AuthGuard);

    router.initialNavigation();
  });

  // Test the default route
  it('should navigate to "" redirects to /home', fakeAsync(() => {
    router.navigate(['']);
    tick();
    expect(location.path()).toBe('/home');
  }));

  // Test a few key routes instead of all of them
  it('should navigate to "login"', fakeAsync(() => {
    router.navigate(['/login']);
    tick();
    expect(location.path()).toBe('/login');
  }));

  it('should navigate to "forgot-password"', fakeAsync(() => {
    router.navigate(['/forgot-password']);
    tick();
    expect(location.path()).toBe('/forgot-password');
  }));

  it('should navigate to "dashboard" with AuthGuard', fakeAsync(() => {
    router.navigate(['/dashboard']);
    tick();
    expect(location.path()).toBe('/dashboard');
    expect(authGuard.canActivate).toHaveBeenCalled();
  }));

  it('should navigate to "members" with AuthGuard', fakeAsync(() => {
    router.navigate(['/members']);
    tick();
    expect(location.path()).toBe('/members');
    expect(authGuard.canActivate).toHaveBeenCalled();
  }));
});

