{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"follo-web": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/follo-web", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/bootstrap/dist/css/bootstrap.min.css", "./node_modules/ngx-bootstrap/datepicker/bs-datepicker.css", "src/styles.scss", "./node_modules/ngx-toastr/toastr.css", "./node_modules/ngx-ui-switch/ui-switch.component.css"], "scripts": ["./node_modules/@lottiefiles/lottie-player/dist/lottie-player.js"], "allowedCommonJsDependencies": ["lodash", "xlsx", "file-saver", "mixpanel-browser", "qrcode", "pdfjs-dist/build/pdf", "pdfjs-dist/web/pdf_viewer", "highcharts", "highcharts/highcharts-more", "highcharts/modules/boost", "highcharts/modules/no-data-to-display", "moment"], "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "buildOptimizer": true, "budgets": [{"type": "bundle", "name": "main", "maximumWarning": "14mb", "maximumError": "14mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}, {"type": "allScript", "maximumWarning": "14mb", "maximumError": "14mb"}, {"type": "all", "maximumWarning": "14mb", "maximumError": "14mb"}, {"type": "initial", "maximumWarning": "14mb", "maximumError": "14mb"}]}, "development": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "buildOptimizer": true, "budgets": [{"type": "bundle", "name": "main", "maximumWarning": "14mb", "maximumError": "14mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}, {"type": "allScript", "maximumWarning": "14mb", "maximumError": "14mb"}, {"type": "all", "maximumWarning": "14mb", "maximumError": "14mb"}, {"type": "initial", "maximumWarning": "14mb", "maximumError": "14mb"}]}, "qa": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.qa.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "buildOptimizer": true, "budgets": [{"type": "bundle", "name": "main", "maximumWarning": "14mb", "maximumError": "14mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}, {"type": "allScript", "maximumWarning": "14mb", "maximumError": "14mb"}, {"type": "all", "maximumWarning": "14mb", "maximumError": "14mb"}, {"type": "initial", "maximumWarning": "14mb", "maximumError": "14mb"}]}, "staging": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "buildOptimizer": true, "budgets": [{"type": "bundle", "name": "main", "maximumWarning": "14mb", "maximumError": "14mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb"}, {"type": "allScript", "maximumWarning": "14mb", "maximumError": "14mb"}, {"type": "all", "maximumWarning": "14mb", "maximumError": "14mb"}, {"type": "initial", "maximumWarning": "14mb", "maximumError": "14mb"}]}}, "defaultConfiguration": ""}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "follo-web:build"}, "configurations": {"production": {"browserTarget": "follo-web:build:production"}, "development": {"browserTarget": "follo-web:build:development"}, "staging": {"browserTarget": "follo-web:build:staging"}, "qa": {"browserTarget": "follo-web:build:qa"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "follo-web:build"}}, "test": {"builder": "@angular-devkit/build-angular:browser", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "styles": ["./node_modules/bootstrap/dist/css/bootstrap.min.css", "./node_modules/ngx-bootstrap/datepicker/bs-datepicker.css", "src/styles.scss"], "scripts": [], "assets": ["src/favicon.ico", "src/assets"]}}}}, "follo-web-e2e": {"root": "e2e/", "projectType": "application", "prefix": "", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "follo-web:serve"}, "configurations": {"production": {"devServerTarget": "follo-web:serve:production"}, "staging": {"devServerTarget": "follo-web:serve:staging"}, "qa": {"devServerTarget": "follo-web:serve:qa"}, "development": {"devServerTarget": "follo-web:serve:development"}}}}}}, "cli": {"analytics": "128ca058-c7f0-4b1d-8c9a-2b8639f3f2ae"}}