name: <PERSON><PERSON><PERSON>
on:
  push:
    branches: [staging]
  pull_request:
    branches: [staging]

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.x]
    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node-version }}
      - name: Dependency install
        run: npm install
      - name: <PERSON><PERSON> checking
        run: npm run start-lint
