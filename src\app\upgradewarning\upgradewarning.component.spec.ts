import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UpgradewarningComponent } from './upgradewarning.component';
import { BsModalRef } from 'ngx-bootstrap/modal';

describe('UpgradewarningComponent', () => {
  let component: UpgradewarningComponent;
  let fixture: ComponentFixture<UpgradewarningComponent>;
  let mockBsModalRef: Partial<BsModalRef>;

  beforeEach(async () => {
    mockBsModalRef = {
      hide: jest.fn()
    };

    await TestBed.configureTestingModule({
      declarations: [ UpgradewarningComponent ],
      providers: [
        { provide: BsModalRef, useValue: mockBsModalRef }
      ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(UpgradewarningComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have BsModalRef injected', () => {
    expect(component.bsModalRef).toBeTruthy();
  });

  it('should call ngOnInit', () => {
    const spy = jest.spyOn(component, 'ngOnInit');
    component.ngOnInit();
    expect(spy).toHaveBeenCalled();
  });
});
