<section class="page-section">
  <div class="page-inner-content members-content">
    <div class="top-header mb-3 pt-0">
      <div class="row">
        <div class="col-md-8">
          <div class="top-btn" *ngIf="isAdmin">
            <ul class="list-group list-group-horizontal">
              <li class="list-group-item p0 border-0 bg-transparent me-4">
                <button
                  class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular"
                  (click)="setCompany(inviteMember)"
                >
                  Invite Member
                </button>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent">
                <button
                  class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular"
                  (click)="openDeleteModal(-1, deleteList)"
                  [disabled]="checkSelectedRow()"
                >
                  Remove
                </button>
              </li>
            </ul>
          </div>
        </div>
        <div class="col-md-4">
          <div class="top-filter">
            <ul class="list-group list-group-horizontal justify-content-end">
              <li class="list-group-item p0 border-0 bg-transparent me-2">
                <div class="search-icon">
                  <input
                    class="form-control fs12 color-grey8"
                    [ngClass]="showSearchbar ? 'input-hover-disable' : 'input-search'"
                    placeholder="What are you looking for?"
                    (input)="getSearchMember($event.target.value)"
                    [(ngModel)]="search"
                  />
                  <div class="icon">
                    <img
                      src="./assets/images/cross-close.svg"
                      *ngIf="showSearchbar"
                      (click)="clear()" (keydown)="clear()"
                      alt="close-cross"
                    />
                    <em class="fa fa-search fs12 color-grey8" *ngIf="!showSearchbar"></em>
                  </div>
                </div>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent me-2 position-relative">
                <div class="filter-icon" (click)="openModal1(filter)"  (keydown)="handleDownKeydown($event, filter,'','','open')">
                  <img src="./assets/images/filter.svg" class="h-12px icon" alt="Filter" />
                </div>
                <div
                  class="bg-orange rounded-circle position-absolute text-white filter-count"
                  *ngIf="filterCount > 0"
                >
                  <p class="m-0 text-center fs12">{{ filterCount }}</p>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="page-card bg-white rounded">
      <div class="table-responsive rounded tab-grid">
        <table
          class="table table-custom mb-0 members-table members-table-resize"
          aria-describedby="Memtable"
        >
          <thead>
            <th scope="col" *ngIf="isAdmin" resizable>
              <div class="custom-checkbox form-check ps-0">
                <input
                  class="form-check-input float-none ms-0"
                  type="checkbox"
                  (change)="selectAllMemberData()"
                  [checked]="selectAll"
                  id="tblData"
                  name="tblData"
                />
                <label class="form-check-label c-pointer fs12" for="tblData"> </label>
              </div>
            </th>
            <th scope="col" resizable>
              Name
              <span>
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('firstName', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'firstName', 'ASC')"
                  *ngIf="sortColumn !== 'firstName'"
                />
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('firstName', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'firstName', 'ASC')"
                  *ngIf="sort === 'DESC' && sortColumn === 'firstName'"
                />
                <img
                  src="./assets/images/up-chevron.svg"
                  alt="up-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('firstName', 'DESC')"
                  (keydown)="handleToggleKeydown($event, 'firstName', 'DESC')"
                  *ngIf="sort === 'ASC' && sortColumn === 'firstName'"
                />
              </span>
            </th>
            <th scope="col" resizable>
              Company
              <span>
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('Company', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'Company', 'ASC')"
                  *ngIf="sortColumn !== 'Company'"
                />
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('Company', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'Company', 'ASC')"
                  *ngIf="sort === 'DESC' && sortColumn === 'Company'"
                />
                <img
                  src="./assets/images/up-chevron.svg"
                  alt="up-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('Company', 'DESC')"
                  (keydown)="handleToggleKeydown($event, 'Company', 'DESC')"
                  *ngIf="sort === 'ASC' && sortColumn === 'Company'"
                />
              </span>
            </th>
            <th scope="col" resizable>
              Email
              <span>
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('email', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'email', 'ASC')"
                  *ngIf="sortColumn !== 'email'"
                />
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('email', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'email', 'ASC')"
                  *ngIf="sort === 'DESC' && sortColumn === 'email'"
                />
                <img
                  src="./assets/images/up-chevron.svg"
                  alt="up-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('email', 'DESC')"
                  (keydown)="handleToggleKeydown($event, 'email', 'DESC')"
                  *ngIf="sort === 'ASC' && sortColumn === 'email'"
                />
              </span>
            </th>
            <th scope="col" resizable>
              Phone No
              <span>
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('phoneNumber', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'phoneNumber', 'ASC')"
                  *ngIf="sortColumn !== 'phoneNumber'"
                />
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('phoneNumber', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'phoneNumber', 'ASC')"
                  *ngIf="sort === 'DESC' && sortColumn === 'phoneNumber'"
                />
                <img
                  src="./assets/images/up-chevron.svg"
                  alt="up-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('phoneNumber', 'DESC')"
                  (keydown)="handleToggleKeydown($event, 'phoneNumber', 'DESC')"
                  *ngIf="sort === 'ASC' && sortColumn === 'phoneNumber'"
                />
              </span>
            </th>
            <th scope="col" resizable>
              Role
              <span>
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('roleName', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'roleName', 'ASC')"
                  *ngIf="sortColumn !== 'roleName'"
                />
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('roleName', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'roleName', 'ASC')"
                  *ngIf="sort === 'DESC' && sortColumn === 'roleName'"
                />
                <img
                  src="./assets/images/up-chevron.svg"
                  alt="up-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('roleName', 'DESC')"
                  (keydown)="handleToggleKeydown($event, 'roleName', 'DESC')"
                  *ngIf="sort === 'ASC' && sortColumn === 'roleName'"
                />
              </span>
            </th>
            <th scope="col" resizable>
              Status
              <span>
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('status', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'status', 'ASC')"
                  *ngIf="sortColumn !== 'status'"
                />
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('status', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'status', 'ASC')"
                  *ngIf="sort === 'DESC' && sortColumn === 'status'"
                />
                <img
                  src="./assets/images/up-chevron.svg"
                  alt="up-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('status', 'DESC')"
                  (keydown)="handleToggleKeydown($event, 'status', 'DESC')"
                  *ngIf="sort === 'ASC' && sortColumn === 'status'"
                />
              </span>
            </th>
            <th
              scope="col"
              *ngIf="authUser.RoleId === 1 || authUser.RoleId === 2"
              class="text-center"
              resizable
            >
              Activate / Deactivate
            </th>
            <th scope="col" *ngIf="isAdmin" resizable>Action</th>
          </thead>
          <tbody *ngIf="loader == false && memberList.length > 0">
            <tr
              *ngFor="
                let item of memberList
                  | paginate
                    : {
                        itemsPerPage: pageSize,
                        currentPage: currentPageNo,
                        totalItems: totalCount
                      };
                let i = index
              "
            >
              <td *ngIf="isAdmin" class="custom-checkbox">
                <div class="form-check text-start ps-0">
                  <input
                    class="form-check-input float-none ms-0"
                    type="checkbox"
                    [checked]="item.isChecked"
                    (change)="setSelectedItem(i)"
                    id="tblData1_{{ i }}"
                    name="tblData1"
                  />
                  <label class="form-check-label c-pointer fs12" for="tblData1_{{ i }}"> </label>
                </div>
              </td>
              <td>
                <span *ngIf="item.User?.firstName">
                  <img
                    src="{{ item.User.profilePic }}"
                    class="member-img"
                    alt="Member"
                    *ngIf="item.User.profilePic != '' && item.User.profilePic != null"
                  />
                  {{ item?.User?.firstName }} {{ item?.User?.lastName }}
                </span>
                <span *ngIf="!item.User?.firstName">-</span>
              </td>

              <td>
                <div class="d-flex" *ngIf="item.Company">
                  <div>
                    <img
                      src="{{ item.logo }}"
                      class="member-img"
                      alt="Member"
                      *ngIf="item.logo != '' && item.logo != null"
                    />
                    <img
                      src="./assets/images/default-user.svg"
                      class="member-img"
                      alt="Profile"
                      *ngIf="item.logo == '' || item.logo == null"
                    />
                  </div>
                  <div class="my-auto">
                    {{ item.Company?.companyName }}
                  </div>
                </div>
                <span *ngIf="!item.Company">-</span>
              </td>

              <td>
                <span *ngIf="item.User?.email">{{ item.User?.email }}</span>
                <span *ngIf="!item.User?.email">-</span>
              </td>

              <td>
                <span *ngIf="item.phoneCode || item.phoneNumber"
                  >{{ item.phoneCode }} {{ item.phoneNumber }}</span
                >
                <span *ngIf="!item.phoneCode && !item.phoneNumber">-</span>
              </td>

              <td>
                <span *ngIf="item.Role?.roleName">{{ item.Role?.roleName }}</span>
                <span *ngIf="!item.Role?.roleName">-</span>
              </td>

              <td>
                <p *ngIf="item.status == 'completed'" class="color-green m-0">Completed</p>
                <p *ngIf="item.status == 'pending'" class="color-red1 m-0">Pending</p>
              </td>
              <td class="text-center">
                <p
                  *ngIf="
                    item?.UserId === item?.createdBy &&
                    authUser.UserId === item.UserId &&
                    (authUser.RoleId === 1 || authUser.RoleId === 2)
                  "
                ></p>

                <p
                  *ngIf="
                    item?.UserId !== item?.createdBy &&
                    authUser.UserId !== item.UserId &&
                    (authUser.RoleId === 1 || authUser.RoleId === 2) &&
                    item.status === 'pending'
                  "
                ></p>

                <div
                  class="form-group"
                  *ngIf="
                    item?.UserId !== item?.createdBy &&
                    authUser.UserId !== item.UserId &&
                    (authUser.RoleId === 1 || authUser.RoleId === 2) &&
                    item.status === 'completed'
                  "
                >
                  <ul
                    class="small-switch list-group list-group-horizontal justify-content-center"
                    id="switch-control4"
                  >
                    <li class="list-group-item border-0 px-0 py-0 bg-transparent">
                      <ui-switch
                        switchColor="#fff"
                        class="small-switch"
                        defaultBoColor="#CECECE"
                        defaultBgColor="#CECECE"
                        (change)="openDeactivateModal(deactivateMember, item, $event)"
                        [checked]="item?.isActive"
                      >
                      </ui-switch>
                    </li>
                  </ul>
                </div>
              </td>
              <td class="position-relative invite-member-dot-option">
                <span *ngIf="isAdmin">
                  <span
                    dropdown
                    (onShown)="onShown()"
                    (onHidden)="onHidden()"
                    (isOpenChange)="isOpenChange()"
                    class="fs12"
                  >
                    <a
                      href
                      id="basic-link"
                      dropdownToggle
                      (click)="(false)"
                      aria-controls="basic-link-dropdown"
                      ><img
                        src="./assets/images/more-horizontal.svg"
                        class="h-15px vertical-dots"
                        alt="dots"
                    /></a>
                    <ul
                      id="basic-link-dropdown"
                      *dropdownMenu
                      class="dropdown-menu"
                      aria-labelledby="basic-link"
                      placement="bottom"
                      isOpen="true"
                    >
                      <li>
                        <a
                          class="dropdown-item c-pointer"
                          (click)="openWaitModal(waitList, copy, item)"
                          *ngIf="item?.status == 'pending' && item.id !== authUser.id"
                        >
                          <span class=""
                            ><img
                              src="./assets/images/link-icon.svg"
                              alt="deleteicon"
                              class="w15 me-4"
                            /> </span
                          >Copy Invite Link
                        </a>
                        <a
                          class="dropdown-item cairo-regular fs12 c-pointer"
                          (click)="openEditModal(i, editMember)"
                          *ngIf="item?.status !== 'pending' || item.id === authUser.id"
                        >
                          <span class=""
                            ><img
                              src="./assets/images/edit.svg"
                              alt="editicon"
                              class="w15 mr22 h-15px"
                            />
                          </span>
                          Edit
                        </a>
                        <a
                          class="dropdown-item cairo-regular fs12 c-pointer"
                          (click)="resendEmailLink(item)"
                          *ngIf="item?.status == 'pending' && item.id !== authUser.id"
                        >
                          <span class="cairo-regular"
                            ><img
                              src="./assets/images/email-resend.svg"
                              alt="editicon"
                              class="w15 me-4 h-15px"
                            />Resend
                          </span>
                        </a>
                        <a
                          class="dropdown-item c-pointer"
                          (click)="openDeleteModal(i, deleteList)"
                          *ngIf="
                            item?.UserId !== item?.createdBy && authUser.UserId !== item.UserId
                          "
                        >
                          <span class=""
                            ><img
                              src="./assets/images/delete.svg"
                              alt="deleteicon"
                              class="w15 me-4"
                            /> </span
                          >Delete
                        </a>
                      </li>
                    </ul>
                  </span>
                </span>
              </td>
            </tr>
          </tbody>
          <tr *ngIf="loader == true">
            <td colspan="8" class="text-center">
              <div class="fs18 fw-bold cairo-regular my-5 text-black">Loading...</div>
            </td>
          </tr>
          <tr *ngIf="loader == false && memberList.length == 0">
            <td colspan="8" class="text-center">
              <div class="fs18 fw-bold cairo-regular my-5 text-black">No Records Found</div>
            </td>
          </tr>
        </table>
      </div>
      <div
        class="tab-pagination px-2"
        id="tab-pagination7"
        *ngIf="loader == false && totalCount > 25"
      >
        <div class="row">
          <div class="col-md-2 align-items-center">
            <ul class="list-inline my-3">
              <li class="list-inline-item notify-pagination">
                <label class="fs12 color-grey4" for="showEnt">Show entries</label>
              </li>
              <li class="list-inline-item">
                <select id="showEnt"
                  class="w-auto form-select fs12 color-grey4"
                  (change)="changePageSize($event.target.value)"
                  [ngModel]="pageSize"
                >
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                  <option value="150">150</option>
                </select>
              </li>
            </ul>
          </div>
          <div class="col-md-8 text-center">
            <div class="my-3 position-relative d-inline-block">
              <pagination-controls
                (pageChange)="changePageNo($event)"
                previousLabel=""
                nextLabel=""
              >
              </pagination-controls>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- modal -->
<ng-template #editMember>
  <div class="modal-header">
    <h4 class="fs14 fw-bold cairo-regular color-text7 my-1">
      <img src="./assets/images/delivery-pop.svg" alt="Delivery" class="me-2" />Edit Member
    </h4>
    <button
      type="button"
      class="close ms-auto"
      aria-label="Close"
      (click)="close(cancelConfirmation, 'editMember')"
    >
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body" *ngIf="!modalLoader">
    <form
      name="form"
      class="custom-material-form"
      [formGroup]="memberEditForm"
      (ngSubmit)="onEditSubmit()"
      novalidate
    >
      <div class="row">
        <div class="col-md-6">
          <div class="form-group mb-3">
            <div class="upload-profile">
              <div class="member">
                <div
                  class="default-profile"
                  *ngIf="
                    memberList[editIndex].User?.profilePic == null ||
                    memberList[editIndex]?.User?.profilePic == undefined ||
                    memberList[editIndex]?.User?.profilePic == ''
                  "
                >
                  <img src="./assets/images/default-user.svg" class="profile-img" alt="Profile" />
                </div>
                <div
                  class="original-profile"
                  *ngIf="
                    memberList[editIndex].User?.profilePic != null &&
                    memberList[editIndex]?.User?.profilePic != undefined &&
                    memberList[editIndex]?.User?.profilePic != ''
                  "
                >
                  <img
                    src="{{ memberList[editIndex]?.User?.profilePic }}"
                    class="profile-img"
                    alt="Profile"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group mb-3">
            <label class="font-weight fs12 fw600" for="member">Member ID</label>
            <input id="member"
              type="text"
              class="form-control fs12 color-orange material-input px-2"
              placeholder=""
              value="{{ memberList[editIndex]?.memberId }}"
              disabled="disabled"
            />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <label class="font-weight fs12 fw600" for="role">&nbsp;</label>
            <select formControlName="RoleId" class="form-control fs12 material-input px-2" id="role">
              <option value="" disabled selected hidden>Role*</option>
              <option *ngFor="let item of roleList" [value]="item.id">
                {{ item.roleName }}
              </option>
            </select>
            <div class="color-red" *ngIf="editSubmitted && memberEditForm.get('RoleId').errors">
              <small *ngIf="memberEditForm.get('RoleId').errors.required">*Choose Role</small>
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-2">
          <div class="form-group mt-4">
            <input
              type="text"
              class="form-control fs12 material-input"
              placeholder="First Name"
              formControlName="firstName"
              (keypress)="alphaOnly($event)"
            />
            <div class="color-red" *ngIf="editSubmitted && memberEditForm.get('firstName').errors">
              <small *ngIf="memberEditForm.get('firstName').errors.required"
                >First Name is Required.</small
              >
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 mb-3">
          <div class="form-group">
            <select formControlName="CompanyId" class="form-control fs12 material-input px-2">
              <option value="" disabled selected hidden>Company Name*</option>
              <option *ngFor="let item of companyList" [value]="item.id">
                {{ item.companyName }}
              </option>
            </select>
            <div class="color-red" *ngIf="editSubmitted && memberEditForm.get('CompanyId').errors">
              <small *ngIf="memberEditForm.get('CompanyId').errors.required">*Choose Company</small>
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <input
              type="text"
              class="form-control fs12 material-input"
              placeholder="Last Name"
              formControlName="lastName"
              (keypress)="alphaOnly($event)"
            />
            <div class="color-red" *ngIf="editSubmitted && memberEditForm.get('lastName').errors">
              <small *ngIf="memberEditForm.get('lastName').errors.required"
                >Last Name is Required.</small
              >
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <input
              type="email"
              class="form-control fs12 material-input"
              placeholder="Email ID*"
              formControlName="email"
              disabled
            />
            <div class="color-red" *ngIf="editSubmitted && memberEditForm.get('email').errors">
              <small *ngIf="memberEditForm.get('email').errors.required">*Email is required</small>
              <small *ngIf="memberEditForm.get('email').errors.pattern"
                >*Enter valid email address</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <div class="input-group align-items-baseline">
              <div class="input-group-prepend">
                <select
                  name="phonecode"
                  formControlName="phoneCode"
                  (change)="changeMask($event.target.value)"
                  class="custom-select no-arrow fs12 material-input shadow-none"
                >
                  <option *ngFor="let item of countryCode" [value]="item.countryDialCode">
                    {{ item.countryDialCode }} ({{ item.name }})
                  </option>
                </select>
              </div>
              <div
                class="color-red"
                *ngIf="editSubmitted && memberEditForm.get('phoneCode').errors"
              >
                <small *ngIf="memberEditForm.get('phoneCode').errors.required"
                  >Enter Phone code</small
                >
              </div>
              <input
                type="text"
                class="form-control material-input fs12 edit-phone-number"
                name="mobile"
                formControlName="phoneNumber"
                placeholder="Mobile number*"
                (keypress)="numberOnly($event)"
                mask="{{ phoneMask }}"
              />
            </div>
            <div
              class="color-red"
              *ngIf="editSubmitted && memberEditForm.get('phoneNumber').errors"
            >
              <small *ngIf="memberEditForm.get('phoneNumber').errors.required"
                >*Phone Number is required</small
              >
            </div>
          </div>
        </div>
      </div>
      <div class="text-center mt-3 mb-4">
        <button
          class="btn btn-grey color-dark-grey radius20 fs12 mb-3 me-md-3 fw-bold cairo-regular px-5"
          (click)="close(cancelConfirmation, 'editMember')"
          type="button"
        >
          Cancel
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular mb-3 px-5"
          type="submit"
          [disabled]="formEditSubmitted && memberEditForm.valid"
        >
          <em
            class="fa fa-spinner"
            aria-hidden="true"
            *ngIf="formEditSubmitted && memberEditForm.valid"
          ></em
          >Submit
        </button>
      </div>
    </form>
  </div>
  <div class="modal-body text-center fw-bold" *ngIf="modalLoader">Loading...</div>
</ng-template>
<div id="fiter-temp5">
  <!--Filter Modal-->
  <ng-template #filter>
    <div class="modal-header border-0 pb-0">
      <h4 class="fs14 fw-bold cairo-regular color-text7 my-0">Filter</h4>
      <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true"
          ><img src="./assets/images/modal-close.svg" alt="Modal Close"
        /></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="filter-content">
        <form class="custom-material-form" [formGroup]="filterForm" (ngSubmit)="filterSubmit()">
          <div class="row">
            <div class="col-md-12">
              <div class="input-group mb-3">
                <input
                  type="text"
                  class="form-control fs12 material-input"
                  placeholder="Name"
                  formControlName="nameFilter"
                />
                <span class="input-group-text">
                  <img src="./assets/images/search-icon.svg" alt="Search" />
                </span>
              </div>
              <div class="form-group">
                <ngx-select-dropdown
                  class="fs12"
                  [formControl]="filterForm.controls['companyFilter']"
                  [multiple]="false"
                  [config]="config"
                  [options]="companyListForFilter"
                ></ngx-select-dropdown>
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="roleFilter">
                  <option value="" disabled selected hidden>Role</option>
                  <option *ngFor="let item of roleList" value="{{ item.id }}">
                    {{ item.roleName }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="statusFilter">
                  <option value="" disabled selected hidden>Status</option>
                  <option *ngFor="let item of wholeStatus" value="{{ item }}">{{ item }}</option>
                </select>
              </div>
              <div class="row justify-content-end">
                <button
                  class="btn btn-orange radius20 col-4 mt-2 fs12 fw-bold cairo-regular mx-1"
                  type="submit"
                >
                  Apply
                </button>
                <button
                  class="btn btn-orange radius20 fs12 col-4 mt-2 fw-bold cairo-regular mx-1"
                  type="button"
                  (click)="resetFilter()"
                >
                  Reset
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
  <!--Filter Modal-->
</div>
<!--Remove Modal-->
<ng-template #deleteList>
  <div class="modal-body">
    <div class="text-center my-4" *ngIf="!remove">
      <p
        class="color-grey15 fs14 fw-bold cairo-regular mb-5"
        *ngIf="memberList[currentDeleteId]?.firstName"
      >
        Are you sure you want to delete '{{ memberList[currentDeleteId]?.firstName | titlecase }}'?
      </p>
      <p
        class="color-grey15 fs14 fw-bold cairo-regular mb-5"
        *ngIf="!memberList[currentDeleteId]?.firstName"
      >
        Are you sure you want to delete '{{ memberList[currentDeleteId]?.User?.email }}'?
      </p>
      <button
        class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
        (click)="resetAndClose()"
      >
        No
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
        type="submit"
        (click)="deleteMember()"
        [disabled]="deleteSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="deleteSubmitted"></em>Yes
      </button>
    </div>
    <div class="text-center my-4" id="remove-popup5" *ngIf="remove">
      <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">Are you sure you want to delete?</p>
      <button
        class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
        (click)="resetAndClose()"
      >
        No
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
        type="submit"
        (click)="removeItem()"
        [disabled]="deleteSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="deleteSubmitted"></em>Yes
      </button>
    </div>
  </div>
</ng-template>
<!--Remove Modal-->
<!--Copy Modal-->
<ng-template #copy>
  <div class="modal-body">
    <div class="modal-header border-0 p-0">
      <button
        type="button"
        class="close ms-auto"
        aria-label="Close"
        (click)="closeCopyLinkPopup()"
      >
        <span aria-hidden="true"
          ><img src="./assets/images/modal-close.svg" alt="Modal Close"
        /></span>
      </button>
    </div>
    <div class="text-center my-4">
      <img src="../../assets/images/success-check-member.svg" alt="success-icon" />
      <p class="color-grey7 fs14 fw-bold cairo-regular mt-2 mb-4">
        Invite link to onboard the new member
      </p>
      <div class="row justify-content-center">
        <div class="col-10">
          <input
            type="text"
            class="border-grey-color col-12 text-center"
            value="{{ onboardingInviteLink }}"
            #userinput
          />
        </div>
      </div>
      <div class="row justify-content-center">
        <button
          class="btn btn-orange radius20 col-2 mt-4 fs12 fw-bold cairo-regular w-100 mx-1"
          (click)="copyInputMessage(userinput)"
        >
          Copy
        </button>
      </div>
    </div>
  </div>
</ng-template>
<!--Copy Modal-->

<!--Waitlist Modal-->
<ng-template #waitList>
  <div class="modal-body wait-modal-body">
    <div class="text-center my-0">
      <img src="../../assets/images/loader-member.gif" alt="loader-icon" class="loader-icon" />
      <p class="color-grey7 fs14 fw-bold cairo-regular mb-5">Generating the invite link</p>
    </div>
  </div>
</ng-template>
<!--Waitlist Modal-->

<!-- Invite Member -->
<ng-template #inviteMember>
  <div class="modal-header">
    <h4 class="fs14 fw-bold cairo-regular color-text7 my-1">
      <img src="./assets/images/noun_member.svg" alt="Delivery" class="me-2" />Invite members
    </h4>
    <button
      type="button"
      class="close ms-auto"
      aria-label="Close"
      (click)="close(cancelConfirmation, 'addMember')"
    >
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body px-md-5 py-4 members-invite-modal" *ngIf="!createNewModalLoader">
    <h3 class="fs14 color-grey8 fw500 text-center">
      Add and Invite multiple users to your project with their emails.
    </h3>
    <form
      name="form"
      class="custom-material-form invite-member-form"
      [formGroup]="inviteDetailsForm"
      novalidate
    >
      <div class="form-group my-4 tag-input-form w-100">
        <tag-input
          formControlName="email"
          [(ngModel)]="inviteMemberList"
          (ngModelChange)="appendProfilePicture($event)"
          placeholder="Enter an Email Address"
          secondaryPlaceholder="Enter an Email Address"
          [onTextChangeDebounce]="500"
          class="tag-layout w-100"
          [identifyBy]="'id'"
          [displayBy]="'email'"
          [addOnPaste]="true"
          [separatorKeyCodes]="[32, 188, 186, 13, 9]"
          data-role="tagsinput"
          [onlyFromAutocomplete]="false"
        >
          <tag-input-dropdown
            [showDropdownIfEmpty]="false"
            [displayBy]="'email'"
            [identifyBy]="'id'"
            id="tag-input"
            [autocompleteObservable]="requestAutocompleteItems"
          >
            <ng-template let-item="item" let-index="index">
              {{ item.email }}
            </ng-template>
          </tag-input-dropdown>
        </tag-input>
        <p class="fs12 fw500 color-grey8">
          * Note : Please press "Enter key" after entering an email.
        </p>

        <div class="color-red" *ngIf="submitted && inviteDetailsForm.get('email').errors">
          <small *ngIf="inviteDetailsForm.get('email').errors.required"
            >*Please choose At least one member.</small
          >
        </div>
      </div>
      <div class="px-2" *ngIf="inviteMemberList && inviteMemberList?.length > 0">
        <div class="row my-0 align-items-center pt-2" *ngFor="let member of inviteMemberList">
          <div class="col-1 me-3 me-md-0">
            <img
              src="{{ member.profilePic }}"
              alt="user-profile"
              class="rounded-circle h-40px w-40px"
              *ngIf="member?.profilePic"
            />
            <img
              src="./assets/images/default-user.svg"
              alt="user-profile"
              class="rounded-circle h-40px w-40px"
              *ngIf="!member?.profilePic"
            />
          </div>
          <div class="col-11 col-md-5">
            <p class="color-grey4 fs12 m-0 text-break membermobile-email-text">
              {{ member.email }}
            </p>
          </div>
          <div
            class="col-12 col-md-3 pt-2 mb-0 select-member-option"
            *ngIf="member?.companyDefaultName"
          >
            <div class="form-group m-0">
              <div class="btn-group w-100 custom-select-li" dropdown>
                <button
                  id="button-basic"
                  dropdownToggle
                  type="button"
                  class="btn btn-primary dropdown-toggle w-155px space-normal"
                  aria-controls="dropdown-basic"
                >
                  <div *ngIf="!member?.companyName">
                    {{ member.companyDefaultName }} <span class="caret"></span>
                  </div>
                  <div *ngIf="member?.companyName">
                    {{ member.companyName }} <span class="caret"></span>
                  </div>
                </button>
                <ul
                  id="dropdown-basic member-scroll"
                  *dropdownMenu
                  class="dropdown-menu company-scroll"
                  aria-labelledby="button-basic"
                >
                  <li
                    contenteditable="true"
                    *ngFor="let commpany of companyList"
                    [collapse]="isCompanyCollapsed && isCollapsed"
                    (click)="appendCompany(commpany.id, member, commpany.companyName)"
                    (keydown)="handleDownKeydown($event, commpany.id,member,commpany.companyName,'company')">
                  >
                    <a class="dropdown-item" href="javascript:void(0)">
                      {{ commpany.companyName }}
                    </a>
                  </li>
                  <li
                    [collapse]="isCompanyCollapsed && isCollapsed"
                    (click)="goToCompany()"  (keydown)="handleDownKeydown($event, '','','','goto')"
                  >
                    <button class="btn btn-orange color-orange add-pre-btn fs12 w-100 text-left">
                      <img src="./assets/images/add.svg" alt="Add" class="me-2 h-15px plus" /> Add
                      new company
                    </button>
                  </li>
                </ul>
              </div>

              <div class="color-red" *ngIf="submitted && !member.CompanyId">
                <small>*choose company</small>
              </div>
            </div>
          </div>

          <div class="col-12 col-md-3 mb-0 pt-2 pt-md-0px" *ngIf="member?.roleDefaultName">
            <div class="form-group m-0">
              <div class="btn-group w-100 custom-select-li" dropdown>
                <button
                  id="button-basic"
                  dropdownToggle
                  type="button"
                  class="btn btn-primary dropdown-toggle"
                  aria-controls="dropdown-basic"
                >
                  <div *ngIf="!member?.roleName">
                    {{ member.roleDefaultName }} <span class="caret"></span>
                  </div>
                  <div *ngIf="member?.roleName">
                    {{ member.roleName }} <span class="caret"></span>
                  </div>
                </button>
                <ul
                  id="dropdown-basic member-scroll"
                  *dropdownMenu
                  class="dropdown-menu"
                  aria-labelledby="button-basic"
                >
                  <li
                    *ngFor="let item of roleList"
                    [collapse]="isRoleCollapsed && isCollapsed"
                    (click)="appendRole(item.id, member, item.roleName)"
                    (keydown)="handleDownKeydown($event, item.id, member, item.roleName,'role')"
                  >
                    <a class="dropdown-item" href="javascript:void(0)">
                      {{ item.roleName }}
                    </a>
                  </li>
                </ul>
              </div>
              <div class="color-red" *ngIf="submitted && !member.RoleId">
                <small>*choose role</small>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="member-scroll px-2" *ngIf="!inviteMemberList || inviteMemberList.length === 0">
        <div class="my-3 text-center fw600">No members yet</div>
      </div>
      <div class="text-center mt-5">
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-4 px-md-5"
          type="button"
          (click)="close(cancelConfirmation, 'addMember')"
        >
          Cancel
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-4 px-md-5"
          (click)="onInviteSubmit()"
          [disabled]="submitted && !invalidMemberForm"
        >
          <em class="fa fa-spinner" aria-hidden="true" *ngIf="submitted && !invalidMemberForm"></em>
          <span
            *ngIf="
              !inviteMemberList || inviteMemberList.length === 0 || inviteMemberList.length === 1
            "
          >
            Send Invite
          </span>
          <span *ngIf="inviteMemberList?.length > 1"> Send Invites </span>
        </button>
      </div>
    </form>
  </div>
  <div class="modal-body text-center fw-bold" *ngIf="createNewModalLoader">Loading...</div>
</ng-template>

<!-- Invite Member -->
<ng-template #optionDetail>
  <div class="modal-body"></div>
</ng-template>
<!--Confirmation Popup-->
<div id="confirm-popup12">
  <ng-template #cancelConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">Are you sure you want to cancel?</p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="cancelForm('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="cancelForm('yes')"
        >
          yes
        </button>
      </div>
    </div>
  </ng-template>
</div>

<ng-template #deactivateMember>
  <div class="modal-header">
    <h4 class="fs14 fw-bold cairo-regular color-text7 my-1">
      <img src="./assets/images/sidemenu-icons/member.svg" alt="Gate" class="me-2" />
      Deactivate Member
    </h4>
    <button
      type="button"
      class="close ms-auto"
      aria-label="Close"
      (click)="closeDeactivateMember()"
    >
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body" *ngIf="!getMappedRequestLoader">
    <div class="row">
      <div class="col-md-12">
        <div class="form-group">
          <p class="text-center fw-bold fs14">Are you sure you want to deactivate this Member?</p>
          <div *ngIf="mappedRequestList.length > 0">
            <p class="text-center fs13 content-deactivate">
              This Member is associated with the following items. If applicable, please switch the
              Member assignment for the respective items.
            </p>
            <div class="table-responsive rounded tab-grid fixTableHead">
              <table class="table table-custom mb-0 members-table" aria-describedby="Memtable">
                <thead>
                  <tr class="member-table-row">
                    <th scope="col"></th>
                    <th scope="col" resizable>ID and Description</th>
                    <th scope="col" resizable>Date and Time</th>
                    <th scope="col" resizable>Member</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let item of mappedRequestList">
                    <td>
                      <img
                        src="./assets/images/cranelift.png"
                        alt="cranelift"
                        *ngIf="
                          item?.requestType === 'deliveryRequestWithCrane' ||
                          item?.requestType === 'craneRequest'
                        "
                      />
                      <img
                        src="./assets/images/bluetruck.svg"
                        alt="bluetruck"
                        *ngIf="item?.requestType === 'deliveryRequest'"
                      />
                      <img
                        src="./assets/images/concrete-request-icon.svg"
                        alt="bluetruck"
                        *ngIf="item?.requestType === 'concreteRequest'"
                      />
                      <img
                        src="./assets/images/sidemenu-icons/equipments.svg"
                        alt="bluetruck"
                        *ngIf="!item?.requestType"
                        class="ps-2"
                      />
                    </td>
                    <td
                      *ngIf="
                        item?.requestType === 'deliveryRequest' ||
                        item?.requestType === 'deliveryRequestWithCrane'
                      "
                    >
                      <p class="mb-0">{{ item?.DeliveryId }}-{{ item?.description }}</p>
                    </td>
                    <td *ngIf="item?.requestType === 'craneRequest'">
                      <p class="mb-0">{{ item?.CraneRequestId }}-{{ item?.description }}</p>
                    </td>
                    <td *ngIf="item?.requestType === 'concreteRequest'">
                      <p class="mb-0">{{ item?.ConcreteRequestId }}-{{ item?.description }}</p>
                    </td>
                    <td *ngIf="!item?.requestType">
                      <p class="mb-0">{{ item?.equipmentAutoId }}-{{ item?.equipmentName }}</p>
                    </td>
                    <td
                      *ngIf="
                        item?.requestType === 'deliveryRequest' ||
                        item?.requestType === 'deliveryRequestWithCrane'
                      "
                    >
                      {{ item?.deliveryStart | date : 'medium' }}
                    </td>
                    <td *ngIf="item?.requestType === 'concreteRequest'">
                      <p class="mb-0">{{ item?.concretePlacementStart | date : 'medium' }}</p>
                    </td>
                    <td *ngIf="item?.requestType === 'craneRequest'">
                      {{ item?.craneDeliveryStart | date : 'medium' }}
                    </td>
                    <td *ngIf="!item?.requestType">
                      <p class="mb-0">-</p>
                    </td>
                    <td class="deactivate-select-control" *ngIf="item?.requestType">
                      <select
                        class="form-control fs12 material-input ps-2"
                        (change)="switchMember(item, $event.target.value)"
                      >
                        <option
                          *ngFor="let itemvalue of allMemberList"
                          value="{{ itemvalue.id }}"
                          [ngValue]="itemvalue?.id"
                          [selected]="item?.memberDetails[0]?.Member?.id == itemvalue?.id"
                        >
                          {{ itemvalue?.User?.firstName }} {{ itemvalue?.User?.lastName }} ({{
                            itemvalue?.User?.email
                          }})
                        </option>
                      </select>
                    </td>
                    <td class="deactivate-select-control" *ngIf="!item?.requestType">
                      <select
                        class="form-control fs12 material-input ps-2"
                        (change)="switchMember(item, $event.target.value)"
                      >
                        <option
                          *ngFor="let itemvalue of allMemberList"
                          value="{{ itemvalue.id }}"
                          [ngValue]="itemvalue?.id"
                          [selected]="item?.controllUserDetails?.id == itemvalue?.id"
                        >
                          {{ itemvalue?.User?.firstName }} {{ itemvalue?.User?.lastName }} ({{
                            itemvalue?.User?.email
                          }})
                        </option>
                      </select>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <p class="text-center fw-bold fs12">
              *Note: If you need to deactivate this member, you will have to switch the member for
              all the future bookings listed above
            </p>
          </div>
        </div>
      </div>
    </div>
    <div class="text-center py-4">
      <button
        class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular mb-3 px-2rem me-2"
        (click)="closeDeactivateMember()"
        type="button"
      >
        Cancel
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular mb-3 px-2rem"
        [disabled]="deactivateMemberLoader"
        (click)="deactivateMemberAction()"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="deactivateMemberLoader"></em>Confirm
      </button>
    </div>
  </div>
  <div class="modal-body fw-bold text-center text-black" *ngIf="getMappedRequestLoader">
    Loading...
  </div>
</ng-template>
