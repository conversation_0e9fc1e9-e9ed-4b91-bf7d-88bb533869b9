/* eslint-disable max-len */
/* eslint-disable max-lines-per-function */
import {
  Component, TemplateRef, OnInit, AfterViewInit,ElementRef,
  ViewChild,
  Input
} from '@angular/core';
import {
  UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators,
} from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import moment from 'moment';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { Socket } from 'ngx-socket-io';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';
import { MixpanelService } from '../../services/mixpanel.service';
import { ProjectSettingsService } from '../../services/project_settings/project-settings.service';
import {
  weekDays, editRecurrence, repeatWithSingleRecurrence, repeatWithMultipleRecurrence,
} from '../../services/common';
import { TimeslotComponent } from 'src/app/layout/time-slot/time-slot.component';

type DateInput = string | number | Date;
@Component({
  selector: 'app-edit-crane-request',
  templateUrl: './edit-crane-request.component.html',
})
export class EditCraneRequestComponent implements OnInit, AfterViewInit {
  @ViewChild('cancelRecurrence') cancelRecurrenceTemplate: TemplateRef<any>;

  @ViewChild('timeSlotsContainer') timeSlotsContainer: ElementRef;

  @ViewChild('timeSlotRef') timeSlotComponent: TimeslotComponent;

  @Input() seriesoption: number;

  public craneEditRequest: UntypedFormGroup;

  public ProjectId: any;

  public submitted = false;

  public escort = false;

  public formSubmitted = false;

  public editSubmitted = false;

  public modalLoader = false;

  public authUser: any = {};

  public loader = false;

  public gateList: any = [];

  public equipmentList: any = [];

  public definableFeatureOfWorkList: any = [];

  public editNdrCompanyDropdownSettings: IDropdownSettings;

  public companyList: any = [];

  public lastId: any = {};

  public formEditSubmitted = false;

  public editNdrDefinableDropdownSettings: IDropdownSettings;

  public currentEditItem: any = {};

  public craneRequestId: any;

  public editProjectId: any;

  public ParentCompanyId: any;

  public deliveryEnd: Date;

  public deliveryStart: Date;

  public NDRTimingChanged = false;

  public editBeforeDFOW = [];

  public editBeforeCompany = [];

  public editBeforeEquipment = [];

  public array3Value = [];

  public formEdited = true;

  public errequipmentenable = false;

  public errmemberenable = false;

  public memberList: any = [];

  public deliveryWindowTime;

  public deliveryWindowTimeUnit;

  public seriesOption: number;

  public recurrenceId: number;

  public recurrenceEndDate;

  public isDisabledDate = false;

  public minDateOfrecurrenceEndDate;

  public selectedLocationId: any;

  public getChosenLocation: any;

  public editNdrLocationDropdownSettings: IDropdownSettings;

  public locationList: any = [];

  public equipmentDropdownSettings: IDropdownSettings;

  public checkform: any = new UntypedFormArray([]);

  public enableOption = false;

  public message = '';

  public valueExists = [];

  public recurrence = editRecurrence;

  public selectedRecurrence = 'Does Not Repeat';

  public isRepeatWithSingleRecurrence = false;

  public isRepeatWithMultipleRecurrence = false;

  public showRecurrenceTypeDropdown = false;

  public weekDays: any = weekDays;

  public monthlyDayOfWeek = '';

  public monthlyDate = '';

  public monthlyLastDayOfWeek = '';

  public repeatWithSingleRecurrence = repeatWithSingleRecurrence;

  public repeatWithMultipleRecurrence = repeatWithMultipleRecurrence;

  public recurrenceMinDate = new Date();

  public daysEdited = false;

  public valueEdited = false;

  public previousRecurrence: any;

  public previousEndDate: Date;

  public previousRepeatEveryCount: any;

  public previousRepeatEveryType: any;

  public previousChosenDateOfMonth: any;

  public previousDays: any;

  public previousDateOfMonth: any;

  public previousMonthlyRepeatType: any;

  public previousChosenDateOfMonthValue: any;

  public payloadDays: any = [];

  public recurrenceEdited = false;

  public selectedTime: string | null = null;

  public endPickerTime: string;
  public durationisOpen = false; // Controls dropdown visibility

  public timeZone: any ;

  public weekDates : any = [];

  public minutes: number[] = [0 ,15, 30, 45, 60]; // Common minute intervals

  hours = Array.from({ length: 24 }, (_, i) => i)

  public selectedHour: number | null = null;

  public selectedMinute: number | null = 30;

  public selectedMinutes: any;

  public availableTimes = [];

  public isAM = true;

  public selectedDate;

  public isSlotsNull = false;

  public noEquipmentOption = { id: 0, equipmentName: 'No Equipment Needed' };

  public constructor(
    private readonly formBuilder: UntypedFormBuilder,
    public socket: Socket,
    private readonly toastr: ToastrService,
    public router: Router,
    public modalRef: BsModalRef,
    private readonly modalService: BsModalService,
    public modalRef1: BsModalRef,
    public modalRef2: BsModalRef,
    private readonly mixpanelService: MixpanelService,
    private readonly deliveryService: DeliveryService,
    public projectService: ProjectService,
    public projectSettingsService: ProjectSettingsService,
  ) {
    this.projectService.projectParent.subscribe((response): void => {
      this.getProjectIdAndParentCompanyId(response);
    });
    this.projectService.accountProjectParent.subscribe((res): void => {
      this.getProjectIdAndParentCompanyId(res);
    });
    this.editCraneRequestForm();
    this.getMembers();
    this.getProjectSettings();
  }

  public getProjectIdAndParentCompanyId(response: any): void {
    if (response !== undefined && response !== null && response !== '') {
      this.ProjectId = response.ProjectId;
      this.ParentCompanyId = response.ParentCompanyId;
      this.loader = true;
    }
  }

  public ngOnInit(): void { /* */ }

  public ngAfterViewInit(): void {
    this.seriesOption = this.modalRef?.content?.seriesOption?this.modalRef.content.seriesOption:this.seriesoption;
    this.isDisabledDate = this.seriesOption !== 1;
    this.deliveryService.EditCraneRequestId.subscribe((getEditCraneRequestIdResponse4): void => {
      this.craneRequestId = getEditCraneRequestIdResponse4;
      this.getOverAllGate();
    });
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
      }
    });
  }

  public getProjectSettings(): void {
    if (this.ProjectId) {
      const params = {
        ProjectId: this.ProjectId,
      };
      this.projectSettingsService.getProjectSettings(params).subscribe((res): void => {
        const responseData = res.data;
        this.deliveryWindowTime = responseData.deliveryWindowTime;
        this.deliveryWindowTimeUnit = responseData.deliveryWindowTimeUnit;
      });
    }
  }

  getBookingData() {
    const equipmentId = this.craneEditRequest.get('EquipmentId').value;
    const locationId = this.craneEditRequest.get('LocationId').value[0].id;
    const gateId = this.craneEditRequest.get('GateId').value;

    if (this.timeSlotComponent) {
      this.timeSlotComponent.getEventNDR(equipmentId, locationId, gateId, this.timeZone,'');
    }
  }

  public getOverAllGate(): void {
    this.modalLoader = true;
    const params = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .gateList(params, { isFilter: true, showActivatedAlone: true })
      .subscribe((res): void => {
        this.gateList = res.data;
        this.getOverAllEquipmentforEditNdr();
      });
  }

  public getOverAllEquipmentforEditNdr(): void {
    const editNdrGetEquipmentParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listCraneEquipment(editNdrGetEquipmentParams, {
        showActivatedAlone: true,
      })
      .subscribe((editNdrEquipmentResponse): void => {
        this.equipmentList = [this.noEquipmentOption, ...editNdrEquipmentResponse.data.rows];
        this.equipmentDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'equipmentName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
        this.getCompaniesForEditNdr();
      });
  }

  public getCompaniesForEditNdr(): void {
    const getCompaniesForEditNdrParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getCompanies(getCompaniesForEditNdrParams)
      .subscribe((getCompaniesForEditNdrResponse: any): void => {
        if (getCompaniesForEditNdrResponse) {
          this.companyList = getCompaniesForEditNdrResponse.data;
          this.getDefinableForEditNdr();
          this.editNdrCompanyDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'companyName',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
        }
      });
  }

  public selectAM() {
    this.isAM = true;
  }

  public selectPM() {
    this.isAM = false;
  }

  public selectTime(startStr: string, endStr: string) {
    const startDate = new Date(startStr);
    const endDate = new Date(endStr);

    this.craneEditRequest.patchValue({
      craneDeliveryStart: startDate,
      craneDeliveryEnd: endDate,
      deliveryDate: startDate
    });

    this.selectedTime = `${startDate.toLocaleTimeString()} - ${endDate.toLocaleTimeString()}`;
    this.NDRTimingChanged = true;
  }

  public scrollToTime(index: number) {
    const container = this.timeSlotsContainer.nativeElement;
    const buttons = container.querySelectorAll('button');

    if (buttons[index]) {
      const selectedButton = buttons[index];
      container.scrollTop = selectedButton.offsetTop - container.offsetTop;
    }
  }

  public updateWeeklyDates() {
    const deliveryEnd = this.craneEditRequest.get('endDate').value;

    if (deliveryEnd) {
      let endDate = moment(deliveryEnd, 'YYYY-MM-DD');

      let currentDate;
      if (this.weekDates.length > 0) {
        currentDate = moment(this.weekDates[0].fullDate, 'YYYY-MM-DD');
      } else {
        currentDate = moment();
      }

      while (currentDate.isSameOrBefore(endDate)) {
        const exists = this.weekDates.some(weekDate => weekDate.fullDate === currentDate.format('YYYY-MM-DD'));

        if (!exists) {
          this.weekDates.push({
            name: currentDate.format('ddd'),
            date: currentDate.format('DD'),
            fullDate: currentDate.format('YYYY-MM-DD'),
          });
        }

        currentDate.add(1, 'day');
      }
    }
  }

  public generateWeekDates(selectedDate: Date | string): void {
    this.weekDates = [];

    for (let i = 0; i < 5; i++) {
      const nextDate = moment(selectedDate).add(i, 'days'); // Get the next 5 dates

      const dayName = nextDate.format('ddd'); // Get short day name like 'Wed'
      const day = nextDate.format('DD'); // Extract the day (e.g., '05')
      const fullDate = nextDate.format('YYYY-MM-DD'); // Full date like '2024-11-06'

      this.weekDates.push({
        name: dayName,
        date: day,
        fullDate: fullDate,
      });
    }
    this.selectedDate = this.weekDates[0]
  }


  public selectDate(day: any) {
    this.selectedDate = day;
    this.craneEditRequest
    .get('deliveryDate')
    .setValue(moment(this.selectedDate.fullDate).format('MM/DD/YYYY'));
  }

  public getAvailableSlots(date){
    const payload = {
      date,
      equipmentId: this.craneEditRequest.get('EquipmentId').value.map((data:any)=>{
        return data.id
      }),
      timeZone: this.timeZone?this.timeZone:'',
      duration: this.selectedMinutes?this.selectedMinutes : '',
      GateId: this.craneEditRequest.get('GateId').value ? this.craneEditRequest.get('GateId').value : '',
      LocationId: this.craneEditRequest.get('LocationId').value[0].id,
      bookingType : 'crane'
    }
      this.deliveryService.getAvailableTimeSlots(this.ProjectId,payload).subscribe((res)=>{
        if (res !== undefined && res !== null && res !== '') {
          this.availableTimes = res.slots
        }
        if(res.slots.AM.length === 0 && res.slots.PM.length === 0){
          this.isSlotsNull = true
        } else{
          this.isSlotsNull = false
        }
      })
  }

  public selectDuration(event){
    this.selectedMinutes = (this.selectedHour * 60) + this.selectedMinute
  }

  public selectHour(hour: number) {
    this.selectedHour = hour;
    const totalMinutes = (this.selectedHour * 60) + this.selectedMinute;
    const deliveryStartValue = this.craneEditRequest.get('craneDeliveryStart')?.value;
    this.deliveryStart = new Date(deliveryStartValue);
    this.deliveryEnd = new Date(deliveryStartValue); // Start with the same date
    this.deliveryEnd.setMinutes(this.deliveryStart.getMinutes() + totalMinutes);

    this.craneEditRequest.get('craneDeliveryEnd').setValue(this.deliveryEnd)
    this.endPickerTime = moment(this.deliveryEnd).format('HH:mm');
    this.durationCloseDropdown()
    this.updateDropdownState();
    this.selectDuration(hour)
  }


  public selectMinute(minute: number) {
    this.selectedMinute = minute;
    const totalMinutes = (this.selectedHour * 60) + this.selectedMinute;
    const deliveryStartValue = this.craneEditRequest.get('craneDeliveryStart')?.value;
    this.deliveryStart = new Date(deliveryStartValue);
    this.deliveryEnd = new Date(deliveryStartValue); // Start with the same date
    this.deliveryEnd.setMinutes(this.deliveryStart.getMinutes() + totalMinutes);

    this.craneEditRequest.get('craneDeliveryEnd').setValue(this.deliveryEnd)
    this.endPickerTime = moment(this.deliveryEnd).format('HH:mm');
    this.durationCloseDropdown()
    this.updateDropdownState();
    this.selectDuration(minute)
  }
  public durationCloseDropdown() {
    this.durationisOpen = false;
  }

  public updateDropdownState() {
    // Close the dropdown after selecting both hour and minute
    if (this.selectedHour !== null && this.selectedMinutes !== null) {
      this.durationisOpen = false;
    }
  }

  public durationToggleDropdown() {
    this.durationisOpen = !this.durationisOpen;
  }

  public getDefinableForEditNdr(): void {
    const getDefinableForEditNdrParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getDefinableWork(getDefinableForEditNdrParams)
      .subscribe((getDefinableForEditNdrResponse: any): void => {
        if (getDefinableForEditNdrResponse) {
          const { data } = getDefinableForEditNdrResponse;
          this.definableFeatureOfWorkList = data;
          this.editNdrDefinableDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'DFOW',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            allowSearchFilter: true,
          };
          this.getLocationForEditNdr();
        }
      });
  }

  public getLocationForEditNdr(): void {
    const getLocationForEditNdrParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getLocations(getLocationForEditNdrParams)
      .subscribe((getLocationForEditNdrResponse: any): void => {
        if (getLocationForEditNdrResponse) {
          const { data } = getLocationForEditNdrResponse;
          this.locationList = data;
          this.gateList = data[0].gateDetails;
          this.equipmentList = (data[0]?.EquipmentId || []).filter(
            ({ PresetEquipmentType }) => PresetEquipmentType?.isCraneType,
          );
          this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
          this.editNdrLocationDropdownSettings = {
            singleSelection: true,
            idField: 'id',
            textField: 'locationPath',
            allowSearchFilter: true,
            closeDropDownOnSelection: true,
          };
          this.getEquipmentCraneRequest();
        }
      });
  }

  public getEquipmentCraneRequest(): void {
    this.loader = true;
    const param = {
      CraneRequestId: this.craneRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    this.deliveryService.getEquipmentCraneRequest(param).subscribe((res): void => {
      this.currentEditItem = res.data;
      this.craneEditRequest.get('id').setValue(this.currentEditItem.id);
      this.craneEditRequest.get('CraneRequestId').setValue(this.currentEditItem.CraneRequestId);
      this.craneEditRequest.get('description').setValue(this.currentEditItem.description);
      this.craneEditRequest
        .get('deliveryDate')
        .setValue(moment(this.currentEditItem.craneDeliveryStart).format('MM/DD/YYYY'));
      this.craneEditRequest
        .get('craneDeliveryStart')
        .setValue(new Date(this.currentEditItem.craneDeliveryStart));
      this.craneEditRequest
        .get('craneDeliveryEnd')
        .setValue(new Date(this.currentEditItem.craneDeliveryEnd));
      this.craneEditRequest.get('recurrenceId').setValue(this.currentEditItem?.recurrence?.id);
      this.craneEditRequest
        .get('recurrence')
        .setValue(this.currentEditItem?.recurrence?.recurrence);
      if (this.currentEditItem?.recurrence?.recurrenceEndDate) {
        this.craneEditRequest
          .get('recurrenceEndDate')
          .setValue(
            new Date(this.currentEditItem?.recurrence?.recurrenceEndDate),
          );
      }
      this.previousEndDate = this.craneEditRequest.get('craneDeliveryEnd').value;
      this.previousRecurrence = this.craneEditRequest.get('recurrence').value;
      this.selectedRecurrence = this.craneEditRequest.get('recurrence').value;
      this.craneEditRequest.get('repeatEveryCount').setValue(+this.currentEditItem?.recurrence?.repeatEveryCount);
      this.previousRepeatEveryCount = this.craneEditRequest.get('repeatEveryCount').value;
      this.craneEditRequest.get('repeatEveryType').setValue(this.currentEditItem?.recurrence?.repeatEveryType);
      this.previousRepeatEveryType = this.craneEditRequest.get('repeatEveryType').value;
      this.craneEditRequest.get('chosenDateOfMonth').setValue(this.currentEditItem?.recurrence?.chosenDateOfMonthValue);
      this.previousChosenDateOfMonth = this.currentEditItem?.recurrence?.chosenDateOfMonth;
      this.previousChosenDateOfMonthValue = this.currentEditItem?.recurrence?.chosenDateOfMonthValue;
      this.craneEditRequest.get('dateOfMonth').setValue(this.currentEditItem?.recurrence?.dateOfMonth);
      this.previousDateOfMonth = this.craneEditRequest.get('dateOfMonth').value;
      this.craneEditRequest.get('monthlyRepeatType').setValue(this.currentEditItem?.recurrence?.monthlyRepeatType);
      this.previousMonthlyRepeatType = this.craneEditRequest.get('monthlyRepeatType').value;
      this.previousDays = this.currentEditItem?.recurrence?.days;
      this.minDateOfrecurrenceEndDate = new Date(
        this.currentEditItem?.recurrence?.recurrenceEndDate,
      );
      this.craneEditRequest.get('isEscortNeeded').setValue(this.currentEditItem.escort);
      this.craneEditRequest.get('additionalNotes').setValue(this.currentEditItem.additionalNotes);
      this.craneEditRequest.get('GateId').setValue(this.currentEditItem.gateDetails[0]?.Gate?.id);

      this.setlocation();
      this.generateWeekDates(moment(this.craneEditRequest.get('craneDeliveryStart').value).format('YYYY-MM-DD'))
      this.craneEditRequest.get('pickUpLocation').setValue(this.currentEditItem.pickUpLocation);
      this.craneEditRequest.get('dropOffLocation').setValue(this.currentEditItem.dropOffLocation);
      this.craneEditRequest
        .get('isAssociatedWithDeliveryRequest')
        .setValue(this.currentEditItem.isAssociatedWithDeliveryRequest);
      this.setCompany();
      this.setDefine();
      this.setMember();
      this.setEquipment();
      this.setRepeat(this.currentEditItem?.recurrence);
      this.openContentModal();
      this.seriesOption = this.modalRef?.content?.seriesOption?this.modalRef.content.seriesOption:this.seriesoption;
      this.isDisabledDate = this.seriesOption !== 1;
      this.getBookingData()
    });
  }

  public openContentModal(): void {
    this.modalLoader = false;
  }

  public locationSelected(data): void {
    this.getChosenLocation = this.locationList.filter((obj: any): any => +obj.id === +data.id);
    if (this.getChosenLocation) {
      this.gateList = []
      this.gateList = this.getChosenLocation[0].gateDetails;
      this.equipmentList = []
      this.equipmentList = [this.noEquipmentOption, ...this.getChosenLocation[0].EquipmentId?this.getChosenLocation[0].EquipmentId : []];
      if (this.equipmentList.length) {
        let newEquipmentList = []
        this.equipmentList.forEach((element) => {
          const equipmentdata = {
            id: element.id,
            equipmentName: element.equipmentName,
          };
          newEquipmentList.push(equipmentdata);
        });
        this.editBeforeEquipment = newEquipmentList;
        this.craneEditRequest.get('EquipmentId').patchValue(newEquipmentList);
      } else {
        this.craneEditRequest.get('EquipmentId').patchValue([]);
      }
      this.timeZone = this.getChosenLocation[0].TimeZoneId?this.getChosenLocation[0].TimeZoneId[0].location:''
    }
    this.selectedLocationId = this.getChosenLocation[0]?.id;
  }

  public setlocation(): void {
    if (this.currentEditItem.location) {
      this.getChosenLocation = [];
      const data = {
        id: this.currentEditItem.location.id,
        locationPath: this.currentEditItem.location.locationPath,
        TimeZoneId: this.currentEditItem.location.TimeZoneId,
      };
      this.getChosenLocation.push(data);
      this.craneEditRequest.get('LocationId').patchValue(this.getChosenLocation);
      this.selectedLocationId = this.getChosenLocation[0]?.id;
      this.timeZone = this.getChosenLocation[0].TimeZoneId?this.getChosenLocation[0].TimeZoneId[0].location:''
    }
  }

  public setMember(): void {
    const { memberDetails } = this.currentEditItem;
    const newMemberList = [];
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.getMemberRole(params).subscribe((res): void => {
      this.authUser = res.data;
      if (memberDetails !== undefined) {
        memberDetails.forEach(
          (element: {
            Member: { User: { firstName: any; lastName: any; email: any }; id: any; isGuestUser: any };
          }): void => {
            let email: string | undefined;

            const user = element.Member?.User;
            const isGuest = element.Member?.isGuestUser;
            if (user?.firstName != null) {
              email = `${user.firstName} ${user.lastName} (${user.email}${isGuest ? ' - Guest' : ''})`;
            } else if (user?.email) {
              email = `(${user.email})${isGuest ? ' (Guest)' : ''}`;
            }
            const data: any = {
              email,
              id: element.Member?.id,
            };
            if (user?.email === this.authUser?.User?.email) {
              data.readonly = true;
            }
            if (user?.email) {
              newMemberList.push(data);
            }
          },
        );
      }
    });
    this.craneEditRequest.get('responsiblePersons').patchValue(newMemberList);
  }

  public setDefine(): void {
    const { defineWorkDetails } = this.currentEditItem;
    const newDefineList = [];
    if (defineWorkDetails !== undefined) {
      defineWorkDetails.forEach((element: { DeliverDefineWork: { id: any; DFOW: any } }): void => {
        const data = {
          id: element.DeliverDefineWork.id,
          DFOW: element.DeliverDefineWork.DFOW,
        };
        newDefineList.push(data);
      });
    }
    this.editBeforeDFOW = newDefineList;
    this.craneEditRequest.get('definableFeatureOfWorks').patchValue(newDefineList);
  }

  public setCompany(): void {
    const { companyDetails } = this.currentEditItem;
    const newCompanyList = [];
    if (companyDetails !== undefined) {
      companyDetails.forEach((element: { Company: { id: any; companyName: any } }): void => {
        const data = {
          id: element.Company.id,
          companyName: element.Company.companyName,
        };
        newCompanyList.push(data);
      });
    }
    this.editBeforeCompany = newCompanyList;
    this.craneEditRequest.get('companies').patchValue(newCompanyList);
  }

  public editCraneRequestForm(): void {
    this.craneEditRequest = this.formBuilder.group({
      id: [''],
      EquipmentId: [this.formBuilder.array([])],
      LocationId: ['', Validators.compose([Validators.required])],
      GateId: ['', Validators.compose([Validators.required])],
      additionalNotes: [''],
      CraneRequestId: [''],
      responsiblePersons: [''],
      description: ['', Validators.compose([Validators.required])],
      deliveryDate: ['', Validators.compose([Validators.required])],
      craneDeliveryStart: ['', Validators.compose([Validators.required])],
      craneDeliveryEnd: ['', Validators.compose([Validators.required])],
      isEscortNeeded: [false],
      companies: [this.formBuilder.array([])],
      definableFeatureOfWorks: [this.formBuilder.array([])],
      pickUpLocation: [''],
      dropOffLocation: [''],
      isAssociatedWithDeliveryRequest: [false, Validators.compose([Validators.required])],
      recurrenceId: [''],
      recurrence: [''],
      recurrenceEndDate: [''],
      repeatEveryCount: [''],
      repeatEveryType: [''],
      days: new UntypedFormArray([]),
      chosenDateOfMonth: [false, ''],
      dateOfMonth: [''],
      monthlyRepeatType: [''],
      endDate: [''],
    });
    this.formControlValueChanged();
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.formEditSubmitted = false;
      this.editSubmitted = false;
      this.modalRef.hide();
      this.NDRTimingChanged = false;
    }
  }

  public onEditSubmitForm(): void {
    if (this.craneEditRequest.dirty || this.NDRTimingChanged || this.daysEdited) {
      this.formEdited = false;
      this.valueEdited = true;
    }
  }

  public convertStart(deliveryDate: Date, startHours: number, startMinutes: number): Date {
    const fullYear = deliveryDate.getFullYear();
    const fullMonth = deliveryDate.getMonth();
    const date = deliveryDate.getDate();
    const deliveryNewStart = new Date(fullYear, fullMonth, date, startHours, startMinutes);
    return deliveryNewStart;
  }

  public checkEditDeliveryFutureDate(
    editDeliveryStart: DateInput,
    editDeliveryEnd: DateInput,
  ): boolean {
    const editStartDate = moment(new Date(editDeliveryStart));
    const editCurrentDate = moment()
      .clone()
      .add(this.deliveryWindowTime, this.deliveryWindowTimeUnit);
    const editEndDate = moment(new Date(editDeliveryEnd));
    if (editStartDate.isAfter(editCurrentDate) && editEndDate.isAfter(editCurrentDate)) {
      return true;
    }
    return false;
  }

  public throwError(action: string): void {
    this.editSubmitted = false;
    this.formEditSubmitted = false;
    if (action === 'time error') {
      this.toastr.error('Please Enter Start time Lesser than End time');
    } else {
      this.toastr.error(
        'Booking not allowed to edit. Please contact the project administrator to edit this booking',
      );
    }
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }

  public gatecheck(value, menuname): void {
  if (menuname === 'Person') {
        const result = this.memberList.filter((o) => value.some(({ id }) => o.id === id));
        if (value.length !== result.length) {
          this.errmemberenable = true;
        } else {
          this.errmemberenable = false;
        }
      }
  }

  public getIndexValue(formValue) {
    const arr1 = this.memberList;
    const arr2 = formValue.responsiblePersons;
    let index2: number;
    const result = arr1.filter((o) => arr2.some(({ id }) => o.id === id));

    if (formValue.responsiblePersons.length !== result.length) {
      index2 = -1;
    } else {
      index2 = 0;
    }
    return index2;
  }

  public onEditSubmit(): void {
    this.editSubmitted = true;
    const companies = [];
    const persons = [];
    const define = [];
    const equipments = [];
    const gates = [];
    const formValue = this.craneEditRequest.value;
    if (formValue.EquipmentId.length <= 0) {
      this.toastr.error('Equipment is required');
      this.formEditSubmitted = false;
      return;
    }
    const deliveryDate = new Date(formValue.deliveryDate);
    const startHours = new Date(formValue.craneDeliveryStart).getHours();
    const startMinutes = new Date(formValue.craneDeliveryStart).getMinutes();
    const deliveryStart = this.convertStart(deliveryDate, startHours, startMinutes);
    const endHours = new Date(formValue.craneDeliveryEnd).getHours();
    const endMinutes = new Date(formValue.craneDeliveryEnd).getMinutes();
    const deliveryEnd = this.convertStart(deliveryDate, endHours, endMinutes);
    if (this.craneEditRequest.invalid) {
      this.formEditSubmitted = false;
      return;
    }
    if (this.checkEditDeliveryFutureDate(deliveryStart, deliveryEnd)) {
      const index2 = this.getIndexValue(formValue);

      if (index2 === -1) {
        if (index2 === -1) {
          this.errmemberenable = true;
        }

        this.formEditSubmitted = false;
      } else {
        if (index2 === -1) {
          this.errmemberenable = false;
        }
        this.checkRequestStatusAndUserRole({
          formValue,
          deliveryStart,
          deliveryEnd,
          companies,
          persons,
          define,
          equipments,
          gates,
        });
      }
    } else {
      this.checkRequestStatusAndUserRole({
        formValue,
        deliveryStart,
        deliveryEnd,
        companies,
        persons,
        define,
        equipments,
        gates,
      });
    }
  }

  public checkRequestStatusAndUserRole(params: {
    formValue;
    deliveryStart;
    deliveryEnd;
    companies;
    persons;
    define;
    equipments;
    gates;
  }): void {
    const {
      formValue, deliveryStart, deliveryEnd, companies, persons, define, equipments, gates,
    } = params;
    if (this.currentEditItem.status === 'Completed' && this.authUser.RoleId !== 2) {
      if (
        moment(this.currentEditItem.craneDeliveryStart).format('MM/DD/YYYY')
          !== moment(this.craneEditRequest.get('deliveryDate').value).format('MM/DD/YYYY')
        || new Date(this.currentEditItem.craneDeliveryStart).getTime()
          !== new Date(this.craneEditRequest.get('craneDeliveryStart').value).getTime()
        || new Date(this.currentEditItem.craneDeliveryEnd).getTime()
          !== new Date(this.craneEditRequest.get('craneDeliveryEnd').value).getTime()
      ) {
        this.toastr.error('You are not allowed to change the date/time ');
        this.formEditSubmitted = false;
        return;
      }
    }
    if (this.currentEditItem.status === 'Approved' && this.authUser.RoleId !== 2) {
      if (
        moment(this.currentEditItem.deliveryDate).format('MM/DD/YYYY')
          !== moment(this.craneEditRequest.get('deliveryDate').value).format('MM/DD/YYYY')
        || new Date(this.currentEditItem.craneDeliveryStart).getTime()
          !== new Date(this.craneEditRequest.get('craneDeliveryStart').value).getTime()
        || new Date(this.currentEditItem.craneDeliveryEnd).getTime()
          !== new Date(this.craneEditRequest.get('craneDeliveryEnd').value).getTime()
      ) {
        if (!this.checkEditDeliveryFutureDate(deliveryStart, deliveryEnd)) {
          this.throwError('error');
          this.formEditSubmitted = false;
          return;
        }
      }
    }
    this.updateCraneDelivery({
      editCraneRequestFormValue: formValue,
      deliveryStart,
      deliveryEnd,
      companies,
      responsbilePersonsData: persons,
      definableFeatureOfWorkData: define,
      equipments,
      gates,
    });
  }

  public checkStartEnd(
    deliveryStart: DateInput,
    deliveryEnd: DateInput,
  ): boolean {
    const startDate = new Date(deliveryStart).getTime();
    const endDate = new Date(deliveryEnd).getTime();
    if (startDate < endDate) {
      return true;
    }
    return false;
  }

  public checkEditDeliveryStringEmptyValues(formValue: {
    description: string;
    additionalNotes: string;
  }): boolean {
    if (formValue.description.trim() === '') {
      this.toastr.error('Please Enter valid description.', 'OOPS!');
      return true;
    }
    if (formValue.additionalNotes) {
      if (formValue.additionalNotes.trim() === '') {
        this.toastr.error('Please Enter valid notes.', 'OOPS!');
        return true;
      }
    }
    return false;
  }

  public formReset(): void {
    this.formEditSubmitted = false;
    this.editSubmitted = false;
  }

  public close(template: TemplateRef<any>): void {
    if (
      this.craneEditRequest.touched
      || this.NDRTimingChanged
      || (this.craneEditRequest.get('definableFeatureOfWorks').dirty
        && this.craneEditRequest.get('definableFeatureOfWorks').value
        && this.craneEditRequest.get('definableFeatureOfWorks').value.length > 0
        && this.areDifferentByProperty(
          this.editBeforeDFOW,
          this.craneEditRequest.get('definableFeatureOfWorks').value,
          'DFOW',
        ))
      || (this.craneEditRequest.get('companies').dirty
        && this.craneEditRequest.get('companies').value
        && this.craneEditRequest.get('companies').value.length > 0
        && this.areDifferentByProperty(
          this.editBeforeCompany,
          this.craneEditRequest.get('companies').value,
          'companyName',
        ))
        || (this.craneEditRequest.get('EquipmentId').dirty
        && this.craneEditRequest.get('EquipmentId').value
        && this.craneEditRequest.get('EquipmentId').value.length > 0
        && this.areDifferentByProperty(
          this.editBeforeEquipment,
          this.craneEditRequest.get('EquipmentId').value,
          'equipmentName',
        ))
    ) {
      this.openConfirmationModalPopupForEditNDR(template);
    } else {
      this.resetForm('yes');
    }
  }

  public changeDate(event: any): void {
    if (!this.modalLoader) {
      const startTime = new Date(event).getHours();
      const minutes = new Date(event).getMinutes();
      this.deliveryEnd = new Date();
      this.deliveryEnd.setHours(startTime + 1);
      this.deliveryEnd.setMinutes(minutes);
      this.craneEditRequest.get('craneDeliveryEnd').setValue(this.deliveryEnd);
      this.NDRTimingChanged = true;
    }
    this.onEditSubmitForm();
  }

  public openConfirmationModalPopupForEditNDR(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public requestAutoEditcompleteItems = (text: string): Observable<any> => {
    const param = {
      ProjectId: this.ProjectId,
      search: text,
      ParentCompanyId: this.ParentCompanyId,
    };
    return this.deliveryService.searchNewMember(param);
  };

  public deliveryEndTimeChangeDetection(): void {
    this.NDRTimingChanged = true;
    this.onEditSubmitForm();
  }

  public numberOnly(event: { which: any; keyCode: any }): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public areDifferentByProperty(a: any[], b: any[], prop: string): boolean {
    const array1 = a.map((x: { [x: string]: any }): any => x[prop]);
    const array2 = b.map((x: { [x: string]: any }): any => x[prop]);
    this.array3Value = array1.concat(array2);
    this.array3Value = [...new Set([...array1, ...array2])];
    return this.array3Value.length !== array1.length;
  }

  public editCraneRequestSuccess(response: { message: string }): void {
    this.toastr.success(response.message, 'Success');
    this.socket.emit('CraneEditHistory', response);
    this.formReset();
    this.deliveryService.updateCraneRequestHistory({ status: true }, 'CraneEditHistory');
    this.resetForm('yes');
    this.NDRTimingChanged = false;
    this.formEditSubmitted = false;
  }

  public editCraneRequest(payload: any): void {
    this.formEditSubmitted = true;
    this.deliveryService.editCraneRequest(payload).subscribe({
      next: (editNdrResponse: any): void => {
        if (editNdrResponse) {
          this.editCraneRequestSuccess(editNdrResponse);
          this.mixpanelService.addMixpanelEvents('Edited Crane Booking');
        }
      },
      error: (editNDRHistoryError): void => {
        this.formReset();
        this.NDRTimingChanged = false;
        if (editNDRHistoryError.message?.statusCode === 400) {
          this.showError(editNDRHistoryError);
        } else if (!editNDRHistoryError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(editNDRHistoryError.message, 'OOPS!');
        }
      },
    });
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public validateEditCraneRequestInputs(editCraneRequestFormValue): boolean {
    const companyDetails = editCraneRequestFormValue.companies;
    const personDetails = editCraneRequestFormValue.responsiblePersons;

    if (!companyDetails || companyDetails.length === 0) {
      this.formReset();
      this.toastr.error('Responsible Company is required');
      return false;
    }

    if (!personDetails || personDetails.length === 0) {
      this.formReset();
      this.toastr.error('Responsible Person is required');
      return false;
    }

    if (this.checkEditDeliveryStringEmptyValues(editCraneRequestFormValue)) {
      this.formReset();
      return false;
    }

    return true;
  }

  public buildEditCraneRequestPayload(editCraneRequestFormValue, deliveryStart, deliveryEnd, companies, responsbilePersonsData, definableFeatureOfWorkData, equipments): any {
    const escortCondition = editCraneRequestFormValue.isEscortNeeded === null
      || editCraneRequestFormValue.isEscortNeeded === undefined
      || editCraneRequestFormValue.escort === '';

    return {
      id: editCraneRequestFormValue.id,
      description: editCraneRequestFormValue.description,
      companies,
      isEscortNeeded: escortCondition ? false : editCraneRequestFormValue.isEscortNeeded,
      ProjectId: this.ProjectId,
      additionalNotes: editCraneRequestFormValue.additionalNotes,
      EquipmentId: equipments,
      LocationId: this.selectedLocationId,
      craneDeliveryStart: deliveryStart,
      craneDeliveryEnd: deliveryEnd,
      ParentCompanyId: this.ParentCompanyId,
      responsiblePersons: responsbilePersonsData,
      definableFeatureOfWorks: definableFeatureOfWorkData,
      isAssociatedWithDeliveryRequest: false,
      pickUpLocation: editCraneRequestFormValue.pickUpLocation,
      dropOffLocation: editCraneRequestFormValue.dropOffLocation,
      seriesOption: this.seriesOption,
      recurrenceId: editCraneRequestFormValue.recurrenceId,
      recurrenceEndDate: editCraneRequestFormValue.recurrenceEndDate
        ? moment(editCraneRequestFormValue.recurrenceEndDate).format('YYYY-MM-DD')
        : null,
      recurrenceSeriesStartDate: moment(editCraneRequestFormValue.craneDeliveryStart).format('YYYY-MM-DD'),
      recurrenceSeriesEndDate: moment(editCraneRequestFormValue.craneDeliveryEnd).format('YYYY-MM-DD'),
      previousSeriesRecurrenceEndDate: moment(editCraneRequestFormValue.craneDeliveryStart).add(-1, 'days').format('YYYY-MM-DD'),
      nextSeriesRecurrenceStartDate: moment(editCraneRequestFormValue.craneDeliveryStart).add(1, 'days').format('YYYY-MM-DD'),
      deliveryStartTime: moment(editCraneRequestFormValue.craneDeliveryStart).format('HH:mm'),
      deliveryEndTime: moment(editCraneRequestFormValue.craneDeliveryEnd).format('HH:mm'),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      recurrence: editCraneRequestFormValue.recurrence,
      chosenDateOfMonthValue: editCraneRequestFormValue.chosenDateOfMonth,
      chosenDateOfMonth: editCraneRequestFormValue.chosenDateOfMonth === 1,
      dateOfMonth: editCraneRequestFormValue.dateOfMonth,
      monthlyRepeatType: editCraneRequestFormValue.monthlyRepeatType,
      days: this.payloadDays,
      repeatEveryType: editCraneRequestFormValue.repeatEveryType ?? null,
      repeatEveryCount: editCraneRequestFormValue.repeatEveryCount?.toString() ?? null,
      recurrenceEdited: this.recurrenceEdited,
      GateId: editCraneRequestFormValue.GateId,
    };
  }

  public updateCraneDelivery(params: {
    editCraneRequestFormValue;
    deliveryStart: Date;
    deliveryEnd: Date;
    companies: any[];
    responsbilePersonsData: any[];
    definableFeatureOfWorkData: any[];
    equipments: any[];
    gates: any[];
  }): void {
    const {
      editCraneRequestFormValue,
      deliveryStart,
      deliveryEnd,
      companies,
      responsbilePersonsData,
      definableFeatureOfWorkData,
      equipments,
    } = params;

    if (!this.checkStartEnd(deliveryStart, deliveryEnd)) {
      this.throwError('time error');
      return;
    }

    if (!this.validateEditCraneRequestInputs(editCraneRequestFormValue)) {
      return;
    }

    editCraneRequestFormValue.companies?.forEach((el: { id: any }) => companies.push(el.id));
    editCraneRequestFormValue.responsiblePersons?.forEach((el: { id: any }) => responsbilePersonsData.push(el.id));
    editCraneRequestFormValue.definableFeatureOfWorks?.forEach((el: { id: any }) => definableFeatureOfWorkData.push(el.id));
    editCraneRequestFormValue.EquipmentId?.forEach((el: { id: any }) => equipments.push(el.id));

    const payload = this.buildEditCraneRequestPayload(
      editCraneRequestFormValue,
      deliveryStart,
      deliveryEnd,
      companies,
      responsbilePersonsData,
      definableFeatureOfWorkData,
      equipments,
    );

    this.editCraneRequest(payload);
  }


  public setEquipment(): void {
    const { equipmentDetails } = this.currentEditItem;
    if(equipmentDetails.length && !equipmentDetails[0].Equipment){
      equipmentDetails[0].Equipment = { id: 0, equipmentName: 'No Equipment Needed' }
    }
    const newEquipmentList = [];
    if (equipmentDetails !== undefined) {
      equipmentDetails.forEach((element: { Equipment: { id: any; equipmentName: any } }): void => {
        const data = {
          id: element.Equipment.id,
          equipmentName: element.Equipment?.equipmentName,
        };
        newEquipmentList.push(data);
      });
    }
    this.editBeforeEquipment = newEquipmentList;
    this.craneEditRequest.get('EquipmentId').patchValue(newEquipmentList);
  }

  public equipmentSelected(value: any): void {
    if(value) {
    if(value.length == this.equipmentList.length -1 && this.equipmentList[0].id == 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
      }
      if(value.length != this.equipmentList.length && this.equipmentList[0].id != 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
      }
      if(value.length == 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
      }
      let hasNoEquipmentOption = false;
      let hasOtherEquipment = false;
      // Check if "No Equipment Needed" (id = 0) is selected
      hasNoEquipmentOption = value.some((item: any) => item.id === 0);

      // Check if other equipment is selected
      hasOtherEquipment = value.some((item: any) => item.id !== 0);

      const previousSelection = this.craneEditRequest.get('EquipmentId').value || [];
      const previousHasOther = previousSelection.some((item: any) => item.id !== 0);

      // Rule 1: If "No Equipment Needed" is selected and other items are selected, keep only "No Equipment Needed"
      if (hasNoEquipmentOption && hasOtherEquipment && !previousHasOther) {
        this.toastr.warning('When "No Equipment Needed" is selected, other equipment options cannot be selected.', 'Warning');
        const noEquipmentOnly = value.filter((item: any) => item.id === 0);
        this.craneEditRequest.get('EquipmentId').setValue(noEquipmentOnly);
        value = noEquipmentOnly;
        hasOtherEquipment = false;
      }

      // Rule 2: If other equipment is already selected and "No Equipment Needed" is now selected, remove it
      if (previousHasOther && hasNoEquipmentOption) {
        this.toastr.warning('When other equipment is selected, "No Equipment Needed" cannot be selected.', 'Warning');
        const filteredSelection = value.filter((item: any) => item.id !== 0);
        this.craneEditRequest.get('EquipmentId').setValue(filteredSelection);
        value = filteredSelection;
        hasNoEquipmentOption = false;
      }
    }

  }

  public repeatEveryRecurrence(value) {
    if (value === 'Does Not Repeat') {
      this.craneEditRequest.get('repeatEveryType').setValue('');
    } else {
      this.craneEditRequest.get('repeatEveryCount').setValue(1);
    }
    if (value === 'Daily') {
      this.craneEditRequest.get('repeatEveryType').setValue('Day');
    }
    if (value === 'Weekly') {
      this.craneEditRequest.get('repeatEveryType').setValue('Week');
    }
    if (value === 'Monthly') {
      this.craneEditRequest.get('repeatEveryType').setValue('Month');
    }
    if (value === 'Yearly') {
      this.craneEditRequest.get('repeatEveryType').setValue('Year');
    }
  }

  public onRecurrenceSelect(value: string): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    this.selectedRecurrence = value;
    this.repeatEveryRecurrence(value);
    if (this.craneEditRequest.get('repeatEveryCount').value > 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.checkform = this.craneEditRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day11: any): void => {
        const dayObj11 = day11;
        dayObj11.checked = true;
        dayObj11.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj11.value));
        return dayObj11;
      });
    }
    if (this.craneEditRequest.get('repeatEveryCount').value > 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.weekDays = this.weekDays.map((day12: any): void => {
        const dayObj12 = day12;
        if (dayObj12.value === 'Monday') {
          dayObj12.checked = true;
        } else {
          dayObj12.checked = false;
        }
        dayObj12.isDisabled = false;
        return dayObj12;
      });
      this.checkform = this.craneEditRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.craneEditRequest.get('repeatEveryCount').value === 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.weekDays = this.weekDays.map((day13: any): void => {
        const dayObj13 = day13;
        if (dayObj13.value === 'Monday') {
          dayObj13.checked = true;
        } else {
          dayObj13.checked = false;
        }
        dayObj13.isDisabled = false;
        return dayObj13;
      });
      this.checkform = this.craneEditRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.craneEditRequest.get('repeatEveryCount').value === 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.checkform = this.craneEditRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day14: any): void => {
        const dayObj14 = day14;
        dayObj14.checked = true;
        dayObj14.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj14.value));
        return dayObj14;
      });
    }
    if (
      this.craneEditRequest.get('repeatEveryCount').value === 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.craneEditRequest.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showMonthlyRecurrence();
    }
    if (
      this.craneEditRequest.get('repeatEveryCount').value > 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.craneEditRequest.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.showMonthlyRecurrence();
    }
    this.occurMessage();
    this.onEditSubmitForm();
  }

  public changeRecurrenceCount(value): void {
    if (value > 0) {
      this.updateRecurrenceUIState(value);
      this.updateRecurrenceFormValues(value);
      this.selectedRecurrence = this.craneEditRequest.get('recurrence').value;
      this.occurMessage();
    } else if (value < 0) {
      this.craneEditRequest.get('repeatEveryCount').setValue(1);
    }
  }

  public updateRecurrenceUIState(value: number): void {
    const recurrencedata = this.craneEditRequest.get('recurrence').value;

    if (recurrencedata === 'Daily' || recurrencedata === 'Weekly' || recurrencedata === 'Monthly' || recurrencedata === 'Yearly') {
      if (+value === 1) {
        this.isRepeatWithSingleRecurrence = true;
        this.isRepeatWithMultipleRecurrence = false;
        this.showRecurrenceTypeDropdown = false;
      } else {
        this.isRepeatWithSingleRecurrence = false;
        this.isRepeatWithMultipleRecurrence = recurrencedata !== 'Daily';
        this.showRecurrenceTypeDropdown = recurrencedata === 'Daily';
      }
    }
  }


  public updateRecurrenceFormValues(value: number): void {
    const recurrencedata = this.craneEditRequest.get('recurrence').value;

    switch (recurrencedata) {
      case 'Daily':
        this.craneEditRequest.get('repeatEveryType').setValue(+value > 1 ? 'Days' : 'Day');
        break;

      case 'Weekly':
        this.craneEditRequest.get('repeatEveryType').setValue(+value > 1 ? 'Weeks' : 'Week');
        break;

      case 'Monthly':
        this.craneEditRequest.get('repeatEveryType').setValue(+value > 1 ? 'Months' : 'Month');
        this.changeMonthlyRecurrence();
        this.showMonthlyRecurrence();
        break;

      case 'Yearly':
        this.craneEditRequest.get('repeatEveryType').setValue(+value > 1 ? 'Years' : 'Year');
        break;

      default:
        break;
    }
  }

  public chooseRepeatEveryType(value: string, eventDetail: any): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    if (value === 'Day' || value === 'Days') {
      this.craneEditRequest.get('recurrence').setValue('Daily');
    }
    if (value === 'Week' || value === 'Weeks') {
      this.craneEditRequest.get('recurrence').setValue('Weekly');
    }
    if (value === 'Month' || value === 'Months') {
      this.craneEditRequest.get('recurrence').setValue('Monthly');
    }
    if (value === 'Year' || value === 'Years') {
      this.craneEditRequest.get('recurrence').setValue('Yearly');
    }
    if (value === 'Day' || value === 'Days') {
      this.checkform = this.craneEditRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day: any): any => {
        const dayObj = day;
        dayObj.checked = true;
        dayObj.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj.value));
        return dayObj;
      });
    }
    if (value === 'Week' || value === 'Weeks') {
      if (eventDetail?.days?.length > 0) {
        this.checkform = this.craneEditRequest.get('days') as UntypedFormArray;
        this.checkform.controls = [];
        this.weekDays = this.weekDays.map((day1: any): void => {
          const dayObj1 = day1;
          if (eventDetail.days.includes(dayObj1.value)) {
            dayObj1.checked = true;
            this.checkform.push(new UntypedFormControl(dayObj1.value));
          } else {
            dayObj1.checked = false;
          }
          dayObj1.isDisabled = false;
          return dayObj1;
        });
      } else {
        this.weekDays = this.weekDays.map((day2: any): void => {
          const dayObj2 = day2;
          if (dayObj2.value === 'Monday') {
            dayObj2.checked = true;
          } else {
            dayObj2.checked = false;
          }
          dayObj2.isDisabled = false;
          return dayObj2;
        });
        this.checkform = this.craneEditRequest.get('days') as UntypedFormArray;
        this.checkform.controls = [];
        this.checkform.push(new UntypedFormControl('Monday'));
      }
    }
    if (value === 'Day' || value === 'Week' || value === 'Month' || value === 'Year') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showRecurrenceTypeDropdown = false;
    }
    if (value === 'Days' || value === 'Weeks' || value === 'Months' || value === 'Years') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.showRecurrenceTypeDropdown = false;
    }
    if (this.craneEditRequest.get('repeatEveryCount').value > 1) {
      this.showRecurrenceTypeDropdown = true;
      this.isRepeatWithMultipleRecurrence = false;
    }
    this.selectedRecurrence = this.craneEditRequest.get('recurrence').value;
    this.occurMessage();
    this.onEditSubmitForm();
  }

  public changeMonthlyRecurrence(): void {
    this.setMonthlyOrYearlyRecurrenceOption();
    this.updateFormValidation();
    this.showMonthlyRecurrence();
    this.occurMessage();
    this.onEditSubmitForm();
  }

  public setMonthlyOrYearlyRecurrenceOption(): void {
    if (this.craneEditRequest.get('chosenDateOfMonth').value === 1) {
      this.craneEditRequest
        .get('dateOfMonth')
        .setValue(moment(this.craneEditRequest.get('deliveryDate').value).format('DD'));
      this.craneEditRequest.get('monthlyRepeatType').setValue(null);
    } else if (this.craneEditRequest.get('chosenDateOfMonth').value === 2) {
      this.craneEditRequest.get('dateOfMonth').setValue(null);
      this.craneEditRequest.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
    } else if (this.craneEditRequest.get('chosenDateOfMonth').value === 3) {
      this.craneEditRequest.get('dateOfMonth').setValue(null);
      this.craneEditRequest.get('monthlyRepeatType').setValue(this.monthlyLastDayOfWeek);
    }
  }

  public updateFormValidation(): void {
    const chosenDateOfMonth = this.craneEditRequest.get('chosenDateOfMonth');
    const dateOfMonth = this.craneEditRequest.get('dateOfMonth');
    const monthlyRepeatType = this.craneEditRequest.get('monthlyRepeatType');
    if (this.craneEditRequest.get('chosenDateOfMonth').value === 1) {
      dateOfMonth.setValidators([Validators.required]);
      monthlyRepeatType.clearValidators();
    } else {
      monthlyRepeatType.setValidators([Validators.required]);
      dateOfMonth.clearValidators();
    }
    chosenDateOfMonth.updateValueAndValidity();
    dateOfMonth.updateValueAndValidity();
    monthlyRepeatType.updateValueAndValidity();
  }

  public showMonthlyRecurrence(): void {
    if (this.craneEditRequest.get('deliveryDate').value) {
      const startDate = moment(this.craneEditRequest.get('deliveryDate').value).format('YYYY-MM');
      const chosenDay = moment(this.craneEditRequest.get('deliveryDate').value).format('dddd');
      this.monthlyDate = moment(this.craneEditRequest.get('deliveryDate').value).format('DD');
      const day = moment(startDate, 'YYYY-MM').startOf('month').day(chosenDay);
      const getAllDays = [];
      if (day.date() > 7) day.add(7, 'd');
      const month = day.month();
      while (month === day.month()) {
        getAllDays.push(day.format('YYYY-MM-DD'));
        day.add(7, 'd');
      }
      let week: string;
      let extraOption: string;
      this.enableOption = false;
      getAllDays.forEach((element, i): void => {
        if (
          moment(this.craneEditRequest.get('deliveryDate').value).format('YYYY-MM-DD')
          === moment(element).format('YYYY-MM-DD')
        ) {
          const number = i + 1;
          if (number === 1) {
            week = 'First';
          }
          if (number === 2) {
            week = 'Second';
          }
          if (number === 3) {
            week = 'Third';
          }
          if (number === 4) {
            this.enableOption = true;
            extraOption = 'Last';
            week = 'Fourth';
          }
          if (number === 5) {
            week = 'Last';
          }
          if (number === 6) {
            week = 'Last';
          }
        }
      });
      this.monthlyDayOfWeek = `${week} ${chosenDay}`;
      this.monthlyLastDayOfWeek = `${extraOption} ${chosenDay}`;
      if (!this.enableOption && this.craneEditRequest.get('chosenDateOfMonth').value === 3) {
        this.craneEditRequest.get('chosenDateOfMonth').setValue(2);
        this.craneEditRequest.get('dateOfMonth').setValue(null);
        this.craneEditRequest.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
      }
      this.setMonthlyOrYearlyRecurrenceOption();
      this.occurMessage();
      this.onEditSubmitForm();
    }
  }

  public repeatEveryMessage() {
    if (this.craneEditRequest.get('repeatEveryType').value === 'Day') {
      this.message = 'Occurs every day';
    }
    if (this.craneEditRequest.get('repeatEveryType').value === 'Days') {
      if (+this.craneEditRequest.get('repeatEveryCount').value === 2) {
        this.message = 'Occurs every other day';
      } else {
        this.message = `Occurs every ${this.craneEditRequest.get('repeatEveryCount').value} days`;
      }
    }
  }

  public occurMessage(): void {
    this.message = '';
    this.repeatEveryMessage();
    if (this.craneEditRequest.get('repeatEveryType').value === 'Week') {
      let weekDays = '';
      this.weekDays.map((dayObj1: any): any => {
        if (dayObj1.checked) {
          weekDays = `${weekDays + dayObj1.value},`;
        }
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message += `Occurs every ${weekDays}`;
    }
    if (this.craneEditRequest.get('repeatEveryType').value === 'Weeks') {
      let weekDays = '';
      this.weekDays.map((dayObj2: any): any => {
        if (dayObj2.checked) {
          weekDays = `${weekDays + dayObj2.value},`;
        }
        return false;
      });
      if (+this.craneEditRequest.get('repeatEveryCount').value === 2) {
        this.message = `Occurs every other  ${weekDays}`;
      } else {
        this.message = `Occurs every ${
          this.craneEditRequest.get('repeatEveryCount').value
        } weeks on ${weekDays}`;
      }
      weekDays = weekDays.replace(/,\s*$/, '');
    }
    if (
      this.craneEditRequest.get('repeatEveryType').value === 'Month'
      || this.craneEditRequest.get('repeatEveryType').value === 'Months'
      || this.craneEditRequest.get('repeatEveryType').value === 'Year'
      || this.craneEditRequest.get('repeatEveryType').value === 'Years'
    ) {
      if (this.craneEditRequest.get('chosenDateOfMonth').value === 1) {
        this.message = `Occurs on day ${this.monthlyDate}`;
      } else if (this.craneEditRequest.get('chosenDateOfMonth').value === 2) {
        this.message = `Occurs on the ${this.monthlyDayOfWeek}`;
      } else {
        this.message = `Occurs on the ${this.monthlyLastDayOfWeek}`;
      }
    }
    if (this.message) {
      this.message += ` until ${moment(this.craneEditRequest.get('recurrenceEndDate').value).format(
        'MMMM DD, YYYY',
      )}`;
    }
  }

  public onChange(event: { target: { value: any; checked: any } }): void {
    this.checkform = this.craneEditRequest.get('days') as UntypedFormArray;
    this.daysEdited = true;
    this.valueExists = this.checkform.controls.filter(
      (object: { value: any }): any => object.value === event.target.value,
    );
    if (event.target.checked) {
      this.checkform.push(new UntypedFormControl(event.target.value));
      this.weekDays = this.weekDays.map((day16: any): void => {
        const dayObj16 = day16;
        if (day16.value === event.target.value) {
          dayObj16.checked = true;
        }
        return dayObj16;
      });
      if (this.checkform.controls.length === 2) {
        this.weekDays = this.weekDays.map((day17: any): void => {
          const dayObj17 = day17;
          dayObj17.isDisabled = false;
          return dayObj17;
        });
      }
    } else if (this.selectedRecurrence === 'Weekly') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day18: any): void => {
            const dayObj18 = day18;
            if (dayObj18.value === event.target.value) {
              dayObj18.checked = false;
            }
            return dayObj18;
          });
        }
        if (this.checkform.controls.length === 1) {
          this.weekDays = this.weekDays.map((day19: any): void => {
            const dayObj19 = day19;
            if (dayObj19.value === this.checkform.controls[0].value) {
              dayObj19.isDisabled = true;
              dayObj19.checked = true;
            }
            return dayObj19;
          });
          return;
        }
        i += 1;
      });
    } else if (this.selectedRecurrence === 'Daily') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day: any): void => {
            const dayObj = day;
            if (dayObj.value === event.target.value) {
              dayObj.checked = false;
              dayObj.isDisabled = false;
            }
            return dayObj;
          });
          return;
        }
        i += 1;
      });
    }
    if (this.checkform.controls.length !== 7) {
      this.craneEditRequest.get('recurrence').setValue('Weekly');
      if (+this.craneEditRequest.get('repeatEveryCount').value === 1) {
        this.craneEditRequest.get('repeatEveryType').setValue('Week');
      } else {
        this.craneEditRequest.get('repeatEveryType').setValue('Weeks');
      }
      this.selectedRecurrence = this.craneEditRequest.get('recurrence').value;
    }
    if (this.checkform.controls.length === 7) {
      this.craneEditRequest.get('recurrence').setValue('Daily');
      if (+this.craneEditRequest.get('repeatEveryCount').value === 1) {
        this.craneEditRequest.get('repeatEveryType').setValue('Day');
      } else {
        this.craneEditRequest.get('repeatEveryType').setValue('Days');
      }
      this.selectedRecurrence = this.craneEditRequest.get('recurrence').value;
    }
    this.occurMessage();
    this.onEditSubmitForm();
  }

  public formControlValueChanged(): void {
    this.craneEditRequest.get('repeatEveryType').valueChanges.subscribe((value: string): void => {
      const days = this.craneEditRequest.get('days');
      const chosenDateOfMonth = this.craneEditRequest.get('chosenDateOfMonth');
      const dateOfMonth = this.craneEditRequest.get('dateOfMonth');
      const monthlyRepeatType = this.craneEditRequest.get('monthlyRepeatType');
      if (value === 'Week' || value === 'Day' || value === 'Weeks') {
        days.setValidators([Validators.required]);
      } else {
        days.clearValidators();
      }
      if (value === 'Month' || value === 'Months' || value === 'Year' || value === 'Years') {
        if (this.craneEditRequest.get('chosenDateOfMonth').value === 1) {
          dateOfMonth.setValidators([Validators.required]);
          monthlyRepeatType.clearValidators();
        } else {
          monthlyRepeatType.setValidators([Validators.required]);
          dateOfMonth.clearValidators();
        }
      } else {
        chosenDateOfMonth.clearValidators();
        dateOfMonth.clearValidators();
        monthlyRepeatType.clearValidators();
      }
      chosenDateOfMonth.updateValueAndValidity();
      dateOfMonth.updateValueAndValidity();
      monthlyRepeatType.updateValueAndValidity();
      days.updateValueAndValidity();
    });
  }

  public setRepeat(data: any): void {
    const setValue = this.craneEditRequest.get('recurrence').value;
    const selectedDays = data.days;
    if (this.craneEditRequest.get('repeatEveryCount').value > 1 && setValue === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.checkform = this.craneEditRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day11: any): void => {
        const dayObj11 = day11;
        dayObj11.checked = true;
        dayObj11.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj11.value));
        return dayObj11;
      });
    }
    if (this.craneEditRequest.get('repeatEveryCount').value > 1 && setValue === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.checkform = this.craneEditRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day13: any): any => {
        if (selectedDays.includes(day13.value)) {
          day13.checked = true;
        } else {
          day13.checked = false;
        }
        day13.isDisabled = false;
        this.checkform.push(new UntypedFormControl(day13.value));
        return day13;
      });
    }
    if (this.craneEditRequest.get('repeatEveryCount').value === 1 && setValue === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.checkform = this.craneEditRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day13: any): any => {
        // Check if the day13.value is in the days array
        if (selectedDays.includes(day13.value)) {
          day13.checked = true;
        } else {
          day13.checked = false;
        }
        day13.isDisabled = false;
        this.checkform.push(new UntypedFormControl(day13.value));
        return day13;
      });
    }
    if (this.craneEditRequest.get('repeatEveryCount').value === 1 && setValue === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.checkform = this.craneEditRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day14: any): void => {
        const dayObj14 = day14;
        dayObj14.checked = true;
        dayObj14.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj14.value));
        return dayObj14;
      });
    }
    if (
      this.craneEditRequest.get('repeatEveryCount').value === 1
      && (setValue === 'Monthly' || setValue === 'Yearly')
    ) {
      this.changeMonthlyRecurrence();
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showMonthlyRecurrence();
    }
    if (
      this.craneEditRequest.get('repeatEveryCount').value > 1
      && (setValue === 'Monthly' || setValue === 'Yearly')
    ) {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.changeMonthlyRecurrence();
      this.showMonthlyRecurrence();
    }
    this.occurMessage();
  }

  public sortWeekDays(data: any[]): any {
    const order = {
      Sunday: 1,
      Monday: 2,
      Tuesday: 3,
      Wednesday: 4,
      Thursday: 5,
      Friday: 6,
      Saturday: 7,
    };

    if (data.length > 0) {
      return data.sort((a, b): any => order[a] - order[b]);
    }
  }

  public onSubmit(): void {
    const formValue = this.craneEditRequest.value;
    if (this.craneEditRequest.get('recurrence').value === 'Weekly' || this.craneEditRequest.get('recurrence').value === 'Daily') {
      this.weekDays.forEach((day11) => {
        if (day11.checked === true) {
          this.payloadDays.push(day11.value);
        }
      });
    } else {
      this.payloadDays = [];
    }
    if (
      formValue.recurrence !== this.previousRecurrence
      || this.previousRepeatEveryCount !== formValue.repeatEveryCount
      || this.previousRepeatEveryType !== formValue.repeatEveryType
      || JSON.stringify(this.previousDays) !== JSON.stringify(this.payloadDays)
      || this.previousDateOfMonth !== formValue.dateOfMonth
      || this.previousMonthlyRepeatType !== formValue.monthlyRepeatType
      || this.daysEdited
    ) {
      this.recurrenceEdited = true;
    } else {
      this.recurrenceEdited = false;
    }
    this.openRecurrencePopup();
  }

  public openRecurrencePopup(): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef2 = this.modalService.show(this.cancelRecurrenceTemplate, data);
  }

  public recurrenceSubmit(action: string): any {
    if (action === 'no') {
      this.modalRef2.hide();
    } else {
      if (this.modalRef2) {
        this.modalRef2.hide();
      }
      this.onEditSubmit();
    }
  }
}
