<div class="delivery-ddetails-content my-3 mx-2 text-center" *ngIf="loader">
  <div class="fs18 fw-bold cairo-regular my-5 text-black">Loading...</div>
</div>
<div class="delivery-ddetails-content my-3 mx-2" *ngIf="!loader">
  <div class="col-md-10 my-4 px-2">
    <h4 class="color-grey14 fs12 font-weight-normal">Description</h4>
    <p class="color-grey15 fs12 fw500">{{ NDRData.description }}</p>
  </div>
  <div class="row px-2 my-4">
    <div class="col-md-4 col-lg-4">
      <h4 class="color-grey14 fs12 mb5 font-weight-normal">Delivery ID</h4>
      <p class="color-grey15 fs12 mb-2 fw500">{{ NDRData.DeliveryId }}</p>
    </div>
    <div class="col-md-4 col-lg-4">
      <h4 class="color-grey14 fs12 mb5 font-weight-normal">Time - Date</h4>
      <p class="color-grey15 fs12 mb-1 fw500" *ngIf="NDRData.deliveryStart">
        {{ NDRData.deliveryStart | date : 'shortTime' }} - {{ NDRData.deliveryEnd | date : 'shortTime' }}
        {{ NDRData.deliveryStart | date : 'MM/dd/yyyy' }}
      </p>
      <p class="color-grey15 fs12 mb-1 fw500" *ngIf="!NDRData.deliveryStart">-</p>
    </div>

    <div class="col-md-5 col-lg-4">
      <h4 class="color-grey14 fs12 mb5 font-weight-normal">Responsible Company</h4>
      <div *ngIf="NDRData.companyDetails && NDRData.companyDetails?.length > 0">
        <p class="color-grey15 fs12 mb-2 fw500" *ngFor="let item of NDRData.companyDetails">
          {{ item?.Company?.companyName }}
        </p>
      </div>
      <p
        class="color-grey15 fs12 mb-2 fw500"
        *ngIf="!NDRData.companyDetails || NDRData.companyDetails?.length === 0"
      >
        -
      </p>
    </div>
  </div>
  <div class="row px-2 my-4">
    <div class="col-md-4 col-lg-4" *ngIf="NDRData?.isAssociatedWithCraneRequest">
      <h4 class="color-grey14 fs12 mb5 font-weight-normal">Crane Pick ID</h4>
      <p class="color-grey15 fs12 mb-2 fw500">{{ NDRData?.CraneRequestId }}</p>
    </div>
    <div class="col-md-4 col-lg-4">
      <h4 class="color-grey14 fs12 mb5 font-weight-normal">Gate</h4>
      <div *ngIf="NDRData.gateDetails && NDRData.gateDetails?.length > 0">
        <p class="color-grey15 fs12 mb-2 fw500" *ngFor="let item of NDRData.gateDetails">
          {{ item?.Gate?.gateName }}
        </p>
      </div>
      <p
        class="color-grey15 fs12 mb-2 fw500"
        *ngIf="!NDRData.gateDetails || NDRData.gateDetails?.length === 0"
      >
        -
      </p>
    </div>
      <div class="col-md-5 col-lg-4">
      <h4 class="color-grey14 fs12 mb5 font-weight-normal">Responsible Person</h4>
      <div *ngIf="NDRData.memberDetails && NDRData.memberDetails?.length > 0">
        <p
          class="bg-grey14 rounded-circle w30 h30 text-center d-inline-block me-2 text-white fs10 fw700 lh-30 eye-cursor"
          *ngFor="let item of NDRData.memberDetails?.slice(0, 3); let i = index"
        >
          <span
            *ngIf="item?.Member?.User?.firstName && item?.Member.isGuestUser === false "
            tooltip="{{ item?.Member?.User?.firstName }} {{ item?.Member?.User?.lastName }}"
            placement="top"
          >
            {{ getResponsiblePeople(item?.Member?.User) }}
          </span>
          <span
            *ngIf="item?.Member?.User?.firstName && item?.Member.isGuestUser === true"
            tooltip="{{ item?.Member?.User?.firstName }} {{ item?.Member?.User?.lastName }} (Guest)"
            placement="top"
          >
            {{ getResponsiblePeople(item?.Member?.User) }}
          </span>
          <span
            *ngIf="!item?.Member?.User?.firstName && item?.Member.isGuestUser === false"
            tooltip="{{ item?.Member?.User?.email }}"
            placement="top"
          >
            {{ getResponsiblePeople(item?.Member?.User) }}
          </span>
          <span
            *ngIf="!item?.Member?.User?.firstName && item?.Member.isGuestUser === true"
            tooltip="{{ item?.Member?.User?.email }} (Guest)"
            placement="top"
          >
            {{ getResponsiblePeople(item?.Member?.User) }}
          </span>
        </p>
        <p
          class="bg-grey14 rounded-circle w30 h30 text-center d-inline-block me-2 text-white fs10 fw700 lh-30 eye-cursor"
          *ngIf="NDRData?.memberDetails?.length > 3"
        >
          <span tooltip="{{ toolTipContent }}" placement="top">
            +{{ NDRData?.memberDetails?.length - 3 }}
          </span>
        </p>
      </div>
    </div>
    
    <div class="col-md-4 col-lg-4" *ngIf="!NDRData?.isAssociatedWithCraneRequest">
      <h4 class="color-grey14 fs12 mb5 font-weight-normal">Equipment Needed</h4>
      <div *ngIf="NDRData.equipmentDetails && NDRData.equipmentDetails?.length > 0">
        <p class="color-grey15 fs12 mb0 fw500" *ngFor="let item of NDRData.equipmentDetails">
          {{ item?.Equipment?.equipmentName }}
        </p>
      </div>
      <p
        class="color-grey15 fs12 mb0 fw500"
        *ngIf="!NDRData.equipmentDetails || NDRData.equipmentDetails?.length === 0"
      >
        -
      </p>
    </div>
  </div>
  <div class="row px-2 my-4" *ngIf="NDRData?.isAssociatedWithCraneRequest">
    <div class="col-md-4 col-lg-4">
      <h4 class="color-grey14 fs12 mb5 font-weight-normal">Equipment Needed</h4>
      <div *ngIf="NDRData.equipmentDetails && NDRData.equipmentDetails?.length > 0">
        <p class="color-grey15 fs12 mb0 fw500" *ngFor="let item of NDRData.equipmentDetails">
          {{ item?.Equipment?.equipmentName }}
        </p>
      </div>
      <p
        class="color-grey15 fs12 mb0 fw500"
        *ngIf="!NDRData.equipmentDetails || NDRData.equipmentDetails?.length === 0"
      >
        -
      </p>
    </div>
    <div class="col-md-4 col-lg-4" *ngIf="NDRData?.isAssociatedWithCraneRequest">
      <h4 class="color-grey14 fs12 mb5 font-weight-normal">Picking From</h4>
      <p class="color-grey15 fs12 mb-1 fw500" *ngIf="NDRData?.cranePickUpLocation">
        {{ NDRData?.cranePickUpLocation }}
      </p>
      <p class="color-grey15 fs12 mb-1 fw500" *ngIf="!NDRData?.cranePickUpLocation">-</p>
    </div>
    <div class="col-md-4 col-lg-4" *ngIf="NDRData?.isAssociatedWithCraneRequest">
      <h4 class="color-grey14 fs12 mb5 font-weight-normal">Picking To</h4>
      <p class="color-grey15 fs12 mb-1 fw500" *ngIf="NDRData?.craneDropOffLocation">
        {{ NDRData?.craneDropOffLocation }}
      </p>
      <p class="color-grey15 fs12 mb-1 fw500" *ngIf="!NDRData?.craneDropOffLocation">-</p>
    </div>
  </div>
  <div class="row px-2 my-4">
    <div class="col-md-4 col-lg-4" *ngIf="NDRData?.location">
      <h4 class="color-grey14 fs12 mb5 font-weight-normal">Location</h4>
      <p class="color-grey15 fs12 mb-2 fw500">
        {{ NDRData?.location?.locationPath }}
      </p>
    </div>
    <div class="col-md-4 col-lg-4" *ngIf="NDRData?.recurrence?.recurrence">
      <h4 class="color-grey14 fs12 mb5 font-weight-normal">Recurrence</h4>
      <p class="color-grey15 fs12 mb-2 fw500">
        {{ NDRData?.recurrence?.recurrence }}
      </p>
    </div>
    <div class="col-md-4 col-lg-4">
      <h4 class="color-grey14 fs12 mb5 font-weight-normal">Delivery Status</h4>
      <p class="color-grey15 fs12 mb-2 fw500" *ngIf="NDRData?.status === 'Approved'">
        {{ NDRData?.status }} by {{ NDRData?.approverDetails?.User?.firstName }}
        {{ NDRData?.approverDetails?.User?.lastName }} on
        {{ NDRData?.approved_at | date : 'mediumDate' }}
      </p>
      <p class="color-grey15 fs12 mb-2 fw500" *ngIf="NDRData?.status === 'Pending'">Pending</p>
      <p class="color-grey15 fs12 mb-2 fw500" *ngIf="NDRData?.status === 'Delivered'">Delivered</p>
      <p class="color-grey15 fs12 mb-2 fw500" *ngIf="NDRData?.status === 'Expired'">Expired</p>
      <p class="color-grey15 fs12 mb-2 fw500" *ngIf="NDRData?.status === 'Declined'">Declined</p>
    </div>
  </div>
  <div class="row px-2 my-4">
    <div class="col-md-4 col-lg-4">
      <h4 class="color-grey14 fs12 mb5 font-weight-normal">Definable Feature of work</h4>
      <div *ngIf="NDRData.defineWorkDetails && NDRData.defineWorkDetails?.length > 0">
        <p class="color-grey15 fs12 mb-2 fw500" *ngFor="let item of NDRData.defineWorkDetails">
          {{ item?.DeliverDefineWork?.DFOW }}
        </p>
      </div>
      <p
        class="color-grey15 fs11 mb-2 fw500"
        *ngIf="!NDRData.defineWorkDetails || NDRData.defineWorkDetails?.length === 0"
      >
        -
      </p>
    </div>
    <div class="col-md-4 col-lg-4">
      <h4 class="color-grey14 fs12 mb5 font-weight-normal">Origination Address</h4>
      <div *ngIf="NDRData?.OriginationAddress">
      <p class="color-grey15 fs12 mb-2 fw500">
        {{ NDRData?.OriginationAddress }}
      </p>
    </div>
      <p
      class="color-grey15 fs11 mb-2 fw500"
      *ngIf="!NDRData.OriginationAddress"
    >
      -
    </p>
    </div>
    <div class="col-md-4 col-lg-4">
      <h4 class="color-grey14 fs12 mb5 font-weight-normal">Vehicle Type</h4>
      <div *ngIf="NDRData?.vehicleType">
      <p class="color-grey15 fs12 mb-2 fw500">
        {{ NDRData?.vehicleType }}
      </p>
      </div>
      <p
      class="color-grey15 fs11 mb-2 fw500"
      *ngIf="!NDRData.vehicleType"
    >
      -
    </p>
    </div>
  </div>
  <div class="row px-2 my-4" *ngIf="NDRData?.notes">
    <div class="col-md-12 col-lg-12">
      <h4 class="color-grey14 fs12 mb5 mt-3 font-weight-normal ps-2 ps-md-0" *ngIf="NDRData?.notes">
        Note
      </h4>
      <p class="color-grey15 fs12 mb-2 fw500 col-md-10 ps-2 ps-md-0">{{ NDRData.notes }}</p>
    </div>
  </div>
</div>
