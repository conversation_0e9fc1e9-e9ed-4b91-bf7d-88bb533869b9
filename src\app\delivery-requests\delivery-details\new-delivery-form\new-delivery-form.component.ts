/* eslint-disable max-len */
/* eslint-disable @typescript-eslint/camelcase */
/* eslint-disable max-lines-per-function */
import {
  Component, OnInit, TemplateRef, AfterViewInit, Input, ElementRef, ViewChild
} from '@angular/core';
import {
  UntypedFormBuilder, UntypedFormGroup, Validators, UntypedFormArray, UntypedFormControl,
} from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { BsModalService, BsModalRef, ModalOptions } from 'ngx-bootstrap/modal';
import { Observable } from 'rxjs';
import moment from 'moment';
import { Router } from '@angular/router';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { Socket } from 'ngx-socket-io';
import { BookingTemplatesService } from '../../../services/booking-templates/booking-templates.service';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';
import { MixpanelService } from '../../../services/mixpanel.service';
import {
  weekDays, recurrence, repeatWithSingleRecurrence, repeatWithMultipleRecurrence,
} from '../../../services/common';
import { TimeslotComponent } from 'src/app/layout/time-slot/time-slot.component';

@Component({
  selector: 'app-new-delivery-form',
  templateUrl: './new-delivery-form.component.html',
})
export class NewDeliveryFormComponent implements OnInit, AfterViewInit {
  @Input() data: any;

  @Input() title: string;

  @Input() currentPage: string;

  @Input() selectfunction: (type: any, date: any)=> void

  @Input() selectedEventDate: any;

  @Input() selectedParams: string;

  @ViewChild('timeSlotRef') timeSlotComponent: TimeslotComponent;

  @ViewChild('timeSlotsContainer') timeSlotsContainer: ElementRef;

  public bookingTypesList: any = ['New Delivery Booking','New Crane Booking' , 'New Concrete Booking', 'New Inspection Booking'];

  public selectedType = 'New Delivery Booking';

  public ProjectId: any;

  public submitted = false;

  public escort = false;

  public formSubmitted = false;

  public templateSubmitted = false;

  public editSubmitted = false;

  public modalLoader = false;

  public deliverDetailsForm: UntypedFormGroup;

  public authUser: any = {};

  public loader = false;

  public gateList: any = [];

  public equipmentList: any = [];

  public timeZone: any ;

  public defineList: any = [];

  public locationList: any = [];

  public newNdrCompanyDropdownSettings: IDropdownSettings;

  public companyList: any = [];

  public lastId: any = {};

  public ParentCompanyId: any;

  public newNdrDefinableDropdownSettings: IDropdownSettings;

  public deliveryEnd: Date;

  public deliveryStart: Date;

  public todayDate = new Date();

  public NDRTimingChanged = false;

  public craneEquipmentTypeChosen = false;

  public craneRequestLastId = null;

  public selectedRecurrence = 'Does Not Repeat';

  public recurrence = recurrence;

  public repeatWithSingleRecurrence = repeatWithSingleRecurrence;

  public repeatWithMultipleRecurrence = repeatWithMultipleRecurrence;

  public weekDays: any = weekDays;

  public isRepeatWithMultipleRecurrence = false;

  public isRepeatWithSingleRecurrence = false;

  public showRecurrenceTypeDropdown = false;

  public checkform: any = new UntypedFormArray([]);

  public message = '';

  public monthlyDate = '';

  public monthlyDayOfWeek = '';

  public monthlyLastDayOfWeek = '';

  public enableOption = false;

  public valueExists = [];

  public endTime: Date;

  public startTime: Date;

  public selectedValue: string;

  public selectedTimeZoneValue: any;

  public selectedLocationId: any;

  public dropdownSettings: IDropdownSettings;

  public durationisOpen = false; // Controls dropdown visibility

  public locationDropdownSettings: IDropdownSettings;

  public vehicleTypes = [
    { id: 1, type: 'Medium and Heavy Duty Truck' },
    { id: 2, type: 'Passenger Car' },
    { id: 3, type: 'Light Duty Truck' },
  ]

  public vehicleTypeDropdownSettings: IDropdownSettings = {
    singleSelection: true,
    idField: 'id',
    textField: 'type',
    allowSearchFilter: true,
    closeDropDownOnSelection: true,
  };

  public selectedVehicleType: any;

  public timeZoneValues: any;

  public timezoneList: [];

  public getSelectedTimeZone: never[];

  public recurrenceMinDate = new Date();

  public newRequestTypeDropdownSettings: IDropdownSettings;

  public equipmentDropdownSettings: IDropdownSettings;

  public templateList: any = [];

  public selectedTemplate: any;

  public templateDropdownSettings: any;

  public disableSaveAsTemplate: boolean = false;

  public memberList: any;

  public templateName: any = '';

  public isBookingTemplate = false;

  public isEditTemplate = false;

  public templateData: any;

  public popupName = 'New Delivery Request';

  public saveAsTemplatePayload: any;

  public editTemplateId: any;

  public weekDates : any = [];

  public minutes: number[] = [0 ,15, 30, 45, 60]; // Common minute intervals

  hours = Array.from({ length: 24 }, (_, i) => i)

  public selectedHour: number | null = null;

  public selectedMinute: number | null = 30;

  public selectedMinutes: any;

  public availableTimes = [];

  public isSlotsNull = false;

  public selectedTime: string | null = null;

  public isAM = true;

  public selectedDate;

  public noEquipmentOption = { id: 0, equipmentName: 'No Equipment Needed' };

  endPickerTime: any;

  public constructor(
    private readonly formBuilder: UntypedFormBuilder,
    public socket: Socket,
    private readonly toastr: ToastrService,
    public router: Router,
    private readonly mixpanelService: MixpanelService,
    public modalRef: BsModalRef,
    public modalRef1: BsModalRef,
    public modalRef3: BsModalRef,
    private readonly modalService: BsModalService,
    private readonly deliveryService: DeliveryService,
    public projectService: ProjectService,
    public bookingTemplatesService: BookingTemplatesService,
    public modalRef2: BsModalRef,
    private readonly option: ModalOptions,
  ) {
    this.selectedValue = this.recurrence[0].value;
    this.projectService.projectParent.subscribe((response7): void => {
      if (response7 !== undefined && response7 !== null && response7 !== '') {
        this.loader = true;
        this.ProjectId = response7.ProjectId;
        this.ParentCompanyId = response7.ParentCompanyId;
        this.loader = true;
        this.getOverAllGateInNewDelivery();
        this.getTemplates();
      }
    });
    this.deliverForm();
    this.getTimeZoneList();
    if (this.selectedValue === 'Does Not Repeat') {
      this.deliverDetailsForm.get('repeatEveryType').setValue('');
    } else {
      this.deliverDetailsForm.get('repeatEveryCount').setValue(1);
    }
  }

  public ngOnInit(): void {
    this.getSelectedDate();
    this.generateWeekDates(moment(this.deliverDetailsForm.get('deliveryDate').value).format('YYYY-MM-DD'))
    this.router.events.subscribe((e): void => {
      this.modalRef.hide();
    });
  }

  public durationToggleDropdown() {
    this.durationisOpen = !this.durationisOpen;
  }

  public durationCloseDropdown() {
    this.durationisOpen = false;
  }

  getBookingData() {
    console.log("===================", this.deliverDetailsForm.get('LocationId'))
    const equipmentId = this.deliverDetailsForm.get('EquipmentId').value;
    const locationId = this.deliverDetailsForm.get('LocationId')?.value[0]?.id;
    const gateId = this.deliverDetailsForm.get('GateId').value;

    if (this.timeSlotComponent) {
      this.timeSlotComponent.getEventNDR(equipmentId, locationId, gateId, this.timeZone,'');
    }
  }

  public selectHour(hour: number) {
    this.selectedHour = hour;
    const totalMinutes = (this.selectedHour * 60) + this.selectedMinute;
    const deliveryStartValue = this.deliverDetailsForm.get('deliveryStart')?.value;
    this.deliveryStart = new Date(deliveryStartValue);
    this.deliveryEnd = new Date(deliveryStartValue); // Start with the same date
    this.deliveryEnd.setMinutes(this.deliveryStart.getMinutes() + totalMinutes);

    this.deliverDetailsForm.get('deliveryEnd')?.setValue(this.deliveryEnd);
    this.endPickerTime = moment(this.deliveryEnd).format('HH:mm');
    this.durationCloseDropdown()
    this.updateDropdownState();
    this.selectDuration(hour)
  }



  public selectMinute(minute: number) {
    this.selectedMinute = minute;
    const totalMinutes = (this.selectedHour * 60) + this.selectedMinute;
    const deliveryStartValue = this.deliverDetailsForm.get('deliveryStart')?.value;
    this.deliveryStart = new Date(deliveryStartValue);
    this.deliveryEnd = new Date(deliveryStartValue); // Start with the same date
    this.deliveryEnd.setMinutes(this.deliveryStart.getMinutes() + totalMinutes);

    this.deliverDetailsForm.get('deliveryEnd')?.setValue(this.deliveryEnd);
    this.endPickerTime = moment(this.deliveryEnd).format('HH:mm');
    this.durationCloseDropdown()
    this.updateDropdownState();
    this.selectDuration(minute)
  }

  public updateDropdownState() {
    // Close the dropdown after selecting both hour and minute
    if (this.selectedHour !== null && this.selectedMinutes !== null) {
      this.durationisOpen = false;
    }
  }


  public generateWeekDates(selectedDate: Date | string): void {
    this.weekDates = [];

    for (let i = 0; i < 5; i++) {
      const nextDate = moment(selectedDate).add(i, 'days');

      const dayName = nextDate.format('ddd');
      const day = nextDate.format('DD');
      const fullDate = nextDate.format('YYYY-MM-DD');

      this.weekDates.push({
        name: dayName,
        date: day,
        fullDate: fullDate,
      });
    }
    this.selectedDate = this.weekDates[0]
  }

  public setDefaultDateAndTime(date: string | number | Date): void {
    this.deliveryStart = new Date();
    this.deliveryEnd = new Date();
    if (date) {
      this.deliveryStart = new Date(date);
      this.deliveryEnd = new Date(date);
    }
  }

  public selectDate(day: any) {
    this.selectedDate = day;
    this.deliverDetailsForm
    .get('deliveryDate')
    .setValue(moment(this.selectedDate.fullDate).format('MM/DD/YYYY'));
  }

  public getAvailableSlots(date) {
    const payload = {
      date,
      equipmentId: this.deliverDetailsForm.get('EquipmentId').value?.map((data: any) => {
        return data.id
      }),
      timeZone: this.timeZone ? this.timeZone : '',
      duration: this.selectedMinutes ? this.selectedMinutes : '',
      GateId: this.deliverDetailsForm.get('GateId').value ? this.deliverDetailsForm.get('GateId').value : '',
      LocationId: this.deliverDetailsForm.get('LocationId').value[0].id,
      bookingType: 'delivery'
    }
    this.deliveryService.getAvailableTimeSlots(this.ProjectId, payload).subscribe((res) => {
      if (res !== undefined && res !== null && res !== '') {
        this.availableTimes = res.slots
      }
      if (res.slots.AM.length === 0 && res.slots.PM.length === 0) {
        this.isSlotsNull = true
      } else {
        this.isSlotsNull = false
      }
    })
  }

  public ngAfterViewInit(): void {
    this.lastId = this.modalRef?.content?.lastId;
    const stateValue = this.data;
    this.isBookingTemplate = stateValue?.bookingTemplate;
    this.popupName = this.isBookingTemplate ? 'New Delivery Booking Template' : 'New Delivery Booking';
    this.isEditTemplate = stateValue?.isEditTemplate;
    if (this.isEditTemplate) {
      this.popupName = 'Edit Delivery Booking Template';
      const id = stateValue?.id;
      this.getTemplate(id);
    }
    this.getLastCraneRequestId();
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
        this.setDefaultPerson();
      }
    });
  }


  public selectTime(startStr: string, endStr: string) {
    const startDate = new Date(startStr);
    const endDate = new Date(endStr);

    this.deliverDetailsForm.patchValue({
      deliveryStart: startDate,
      deliveryEnd: endDate,
      deliveryDate: startDate
    });

    this.selectedTime = `${startDate.toLocaleTimeString()} - ${endDate.toLocaleTimeString()}`;
  }


  public convertTo24HourFormat(hours: number): number {
    if (!this.isAM) {
      return hours === 12 ? 12 : hours + 12;
    }
    return hours === 12 ? 0 : hours;
  }

  public getTotalDuration(): number {
    return (this.selectedHour * 60) + this.selectedMinute;
  }

  public scrollToTime(index: number) {
    const container = this.timeSlotsContainer.nativeElement;
    const buttons = container.querySelectorAll('button');

    if (buttons[index]) {
      const selectedButton = buttons[index];
      container.scrollTop = selectedButton.offsetTop - container.offsetTop;
    }
  }



  public selectedBookingtype(type, template): void {
    this.selectedParams = type;
    if (this.deliverDetailsForm.touched && this.deliverDetailsForm.dirty) {
      this.openChangeConfirm(template);
    } else if (type) {
      this.selectfunction(this.selectedParams, this.deliverDetailsForm.get('deliveryDate').value);
    }
  }

  public selectDuration(event) {
    this.selectedMinutes = (this.selectedHour * 60) + this.selectedMinute
  }

  public openChangeConfirm(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md changeConfirmation-popup modal-dialog-centered custom-modal',
      date: this.deliverDetailsForm.get('deliveryDate').value
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public closeChangeConfirm(confirmed){
    if(confirmed === 'yes'){
      this.modalRef1.hide();
      this.selectfunction(this.selectedParams , this.deliverDetailsForm.get('deliveryDate').value)
    }
    this.modalRef1.hide();
  }

  public getTemplate(id): void {
    const queryParams = {
      id,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    this.editTemplateId = id;
    this.bookingTemplatesService.getTemplate(queryParams).subscribe((res): void => {
      this.templateData = res.data;
      this.setBookingTemplateData(this.templateData);
    });
  }

  public handleAddressChange(address): void {
    this.deliverDetailsForm.get('originationAddress').setValue(address.formatted_address);
  }

  public getSelectedDate(): void {
    const getData = this.data;
    if (getData) {
      if (getData.date && getData.currentView === 'Month') {
        this.deliverDetailsForm
          .get('deliveryDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00'));
        this.setDefaultDateAndTime(this.deliverDetailsForm.get('deliveryDate').value);
      }
      if (
        (getData.date && getData.currentView === 'Week')
        || (getData.date && getData.currentView === 'Day')
      ) {
        this.deliverDetailsForm
          .get('deliveryDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00:00'));
        this.deliverDetailsForm
          .get('deliveryStart')
          .setValue(moment(getData.date, 'YYYY-MM-DD hh:mm:ss').format());
        this.deliverDetailsForm
          .get('deliveryEnd')
          .setValue(moment(getData.date, 'YYYY-MM-DD hh:mm:ss').add(30, 'minutes').format());
      }
    }
  }

  public getLastCraneRequestId(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getLastCraneRequestId(params).subscribe((response): void => {
      this.craneRequestLastId = response.lastId?.CraneRequestId;
      this.deliverDetailsForm.get('CraneRequestId').setValue(response.lastId?.CraneRequestId);
    });
  }

  public getOverAllGateInNewDelivery(): void {
    this.modalLoader = true;
    const params = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .gateList(params, { isFilter: true, showActivatedAlone: true })
      .subscribe((res): void => {
        this.getOverAllEquipmentInNewDelivery();
      });
  }

  public getOverAllEquipmentInNewDelivery(): void {
    const newNdrGetEquipmentsParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listEquipment(newNdrGetEquipmentsParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((equipmentListResponseForNewNdr): void => {
        this.equipmentDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'equipmentName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
        this.newNdrgetCompanies();
      });
  }

  public newNdrgetCompanies(): void {
    const newNdrGetCompaniesParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getCompanies(newNdrGetCompaniesParams)
      .subscribe((companiesResponseForNewNdr: any): void => {
        if (companiesResponseForNewNdr) {
          this.companyList = companiesResponseForNewNdr.data;
          const loggedInUser = this.companyList.filter(
            (a: { id: any }): any => a.id === this.authUser.CompanyId,
          );
          this.deliverDetailsForm.get('companyItems').setValue(loggedInUser);
          this.getDefinable();
          this.newNdrCompanyDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'companyName',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
        }
      });
  }

  public getDefinable(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getDefinableWork(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.defineList = data;
        this.newNdrDefinableDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'DFOW',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          allowSearchFilter: true,
        };
        this.getLocations();
      }
    });
  }

  public getLocations(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getLocations(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.locationList = data;
        if(data[0].gateDetails){
          this.gateList = data[0].gateDetails
        }else{
          this.gateList = []
        }
        this.equipmentList = [this.noEquipmentOption, ...( data[0].EquipmentId?data[0].EquipmentId:[])];
        this.timeZone = data[0].TimeZoneId?data[0].TimeZoneId[0].location :''
        this.locationDropdownSettings = {
          singleSelection: true,
          idField: 'id',
          textField: 'locationPath',
          allowSearchFilter: true,
          closeDropDownOnSelection: true,
        };
        this.setDefaultLocationPath();
      }
    });
  }

  public requestAutocompleteItems = (text: string): Observable<any> => {
    const param = {
      ProjectId: this.ProjectId,
      search: text,
      ParentCompanyId: this.ParentCompanyId,
    };
    return this.deliveryService.searchNewMember(param);
  };

  public closeModalPopup(): void {
    this.modalLoader = false;
  }

  public deliverForm(): void {
    this.deliverDetailsForm = this.formBuilder.group({
      LocationId: ['', Validators.compose([Validators.required])],
      EquipmentId: [this.formBuilder.array([])],
      GateId: ['', Validators.compose([Validators.required])],
      notes: [''],
      CraneRequestId: [''],
      person: ['', Validators.compose([Validators.required])],
      description: ['', Validators.compose([Validators.required])],
      deliveryDate: ['', Validators.compose([Validators.required])],
      deliveryStart: ['', Validators.compose([Validators.required])],
      deliveryEnd: ['', Validators.compose([Validators.required])],
      escort: [false],
      companyItems: [this.formBuilder.array([])],
      defineItems: [this.formBuilder.array([])],
      cranePickUpLocation: [''],
      craneDropOffLocation: [''],
      isAssociatedWithCraneRequest: [false, Validators.compose([Validators.required])],
      recurrence: [''],
      repeatEveryCount: [''],
      repeatEveryType: [''],
      days: new UntypedFormArray([]),
      chosenDateOfMonth: [false, ''],
      dateOfMonth: [''],
      monthlyRepeatType: [''],
      endDate: [''],
      templateName: [''],
      TimeZoneId: ['', Validators.compose([Validators.required])],
      originationAddress: [''],
      vehicleType: [''],
    });
    this.deliverDetailsForm.get('escort').setValue(false);
    const newDate = moment().format('MM/DD/YYYY');
    this.deliverDetailsForm.get('deliveryDate').setValue(newDate);
    this.deliverDetailsForm.get('deliveryStart').setValue(this.deliveryStart);
    this.deliverDetailsForm.get('deliveryEnd').setValue(this.deliveryEnd);
    this.deliverDetailsForm.get('recurrence').setValue(this.selectedValue);

    this.formControlValueChanged();
    this.setCurrentTiming();
  }

  public convertStart(deliveryDate: Date, startHours: number, startMinutes: number): Date {
    const fullYear = deliveryDate.getFullYear();
    const fullMonth = deliveryDate.getMonth();
    const date = deliveryDate.getDate();
    const deliveryNewStart = new Date(fullYear, fullMonth, date, startHours, startMinutes);
    return deliveryNewStart;
  }

  public setDefaultLocationPath(): void {
    if (this.locationList.length > 0) {
      const getChosenLocation: any = this.locationList.filter((obj: any): any => obj.isDefault === true);
      if (getChosenLocation) {
        this.deliverDetailsForm.get('LocationId').patchValue(getChosenLocation);
        this.selectedLocationId = getChosenLocation[0]?.id;
      }
    }
    this.closeModalPopup();
  }

  public numberOnly(event: { which: any; keyCode: any }): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }


  selectAM() {
    this.isAM = true;
  }

  selectPM() {
    this.isAM = false;
  }

  public onSubmit(action, modalName): void {
    this.submitted = true;
    if (!action) {
      this.formSubmitted = true;
    }
        const companies = [];
    const persons = [];
    const define = [];
    const equipments = [];
    if (this.deliverDetailsForm.invalid) {
      this.formSubmitted = false;
      return;
    }
    if (this.deliverDetailsForm.get('isAssociatedWithCraneRequest').value) {
    if (
        !this.deliverDetailsForm.get('cranePickUpLocation').value
        || !this.deliverDetailsForm.get('craneDropOffLocation').value
    ) {
      this.formSubmitted = false;
        this.toastr.error('Please enter Picking From and Picking To');
      return;
    }
    }
    const formValue = this.deliverDetailsForm.value;
    if (formValue.EquipmentId.length <= 0) {
      this.toastr.error('Equipment is required');
      this.formSubmitted = false;
      return;
    }

    const newTimeZoneDetails = formValue.TimeZoneId;
    const deliveryDate = new Date(formValue.deliveryDate);
    const EndDate = new Date(formValue.endDate);
    const startNewDate = new Date(formValue.deliveryStart);
    const startHours = startNewDate.getHours();
    const startMinutes = startNewDate.getMinutes();
    const deliveryStart = this.convertStart(deliveryDate, startHours, startMinutes);
    const endNewDate = new Date(formValue.deliveryEnd);
    const endHours = endNewDate.getHours();
    const endMinutes = endNewDate.getMinutes();
    const startPicker = moment(formValue.deliveryStart).format('HH:mm');
    const endPicker = moment(formValue.deliveryEnd).format('HH:mm');
    this.endPickerTime = moment(formValue.deliveryEnd).format('HH:mm');
    const weekStartDate = moment(formValue.deliveryDate).format('YYYY MM DD 00:00:00');
    const weekEndDate = formValue.recurrence !== 'Does Not Repeat'
      ? moment(formValue.endDate).format('YYYY MM DD 00:00:00')
      : moment(formValue.deliveryDate).format('YYYY MM DD 00:00:00');
    const deliveryEnd = formValue.recurrence !== 'Does Not Repeat'
      ? this.convertStart(EndDate, endHours, endMinutes)
      : this.convertStart(deliveryDate, endHours, endMinutes);
    this.createDelivery({
      newNdrFormValue: formValue,
      deliveryStart,
      deliveryEnd,
      companies,
      persons,
      define,
      newTimeZoneDetails,
      startPicker,
      endPicker,
      weekStartDate,
      weekEndDate,
      equipments,
      action,
      modalName,
    });
  }


  public openTimeSlot() {
    this.modalRef3 = this.modalService.show(TimeslotComponent, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-md timeslot-popup timeslot-modal-dialog-centered',
      ignoreBackdropClick: true,
      initialState: {
        closeModal: this.closeTimeSlot.bind(this), // Pass close function
        selectTime: this.selectTime.bind(this)
      },
    });
    setTimeout(() => {
      window.dispatchEvent(new Event('resize')); // Force UI update
    }, 100);
  }

  public closeTimeSlot() {
    if (this.modalRef3) {
      this.modalRef3.hide();
      this.modalRef3 = null;
    }
  }

  public checkEquipmentType(value: any): void {
    let findEquipmentType: any;
    let count = 0;
    let hasNoEquipmentOption = false;
    let hasOtherEquipment = false;


    if (value) {
      if(value.length == this.equipmentList.length -1 && this.equipmentList[0].id == 0) {
      this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
      }
      if(value.length != this.equipmentList.length && this.equipmentList[0].id != 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
      }
      if(value.length == 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
      }
        // Check if "No Equipment Needed" (id = 0) is selected
    hasNoEquipmentOption = value.some((item: any) => item.id === 0);
 
    // Check if other equipment is selected
    hasOtherEquipment = value.some((item: any) => item.id !== 0);
 
    const previousSelection = this.deliverDetailsForm.get('EquipmentId').value || [];
    const previousHasOther = previousSelection.some((item: any) => item.id !== 0);
 
    // Rule 1: If "No Equipment Needed" is selected and other items are selected, keep only "No Equipment Needed"
    if (hasNoEquipmentOption && hasOtherEquipment && !previousHasOther) {
      this.toastr.warning('When "No Equipment Needed" is selected, other equipment options cannot be selected.', 'Warning');
      const noEquipmentOnly = value.filter((item: any) => item.id === 0);
      this.deliverDetailsForm.get('EquipmentId').setValue(noEquipmentOnly);
      value = noEquipmentOnly;
      hasOtherEquipment = false;
    }
 
    // Rule 2: If other equipment is already selected and "No Equipment Needed" is now selected, remove it
    if (previousHasOther && hasNoEquipmentOption) {
      this.toastr.warning('When other equipment is selected, "No Equipment Needed" cannot be selected.', 'Warning');
      const filteredSelection = value.filter((item: any) => item.id !== 0);
      this.deliverDetailsForm.get('EquipmentId').setValue(filteredSelection);
      value = filteredSelection;
      hasNoEquipmentOption = false;
    }
      value.some((val: { id: any }) => {
        findEquipmentType = this.equipmentList.find((item: { id: any }) => item.id === val.id);
        if (findEquipmentType) {
          const isCraneType = findEquipmentType.PresetEquipmentType?.isCraneType;
          this.craneEquipmentTypeChosen = isCraneType;

          if (isCraneType) {
            count++;
            this.deliverDetailsForm.get('isAssociatedWithCraneRequest').setValue(true);
            return true;
          }
          this.deliverDetailsForm.get('isAssociatedWithCraneRequest').setValue(false);
        }
        return false;
      });

      if (count > 0) {
        this.getLastCraneRequestId();
      }
      if (value.length === 0) {
        this.craneEquipmentTypeChosen = false;
      }
    }
    this.getBookingData();
  }

  public createDelivery(params: any): void {
    const {
      newNdrFormValue,
      deliveryStart,
      deliveryEnd,
      companies,
      persons,
      define,
      newTimeZoneDetails,
      startPicker,
      endPicker,
      weekStartDate,
      weekEndDate,
      equipments,
      action,
      modalName,
    } = params;

    const payload = this.prepareDeliveryPayload({
      newNdrFormValue,
      deliveryStart,
      deliveryEnd,
      companies,
      persons,
      define,
      newTimeZoneDetails,
      startPicker,
      endPicker,
      weekStartDate,
      weekEndDate,
      equipments,
    });

    if (!payload) return;

    this.createNDR(payload, action, modalName);
  }

  public prepareDeliveryPayload(params: any) {
    const {
      newNdrFormValue, companies, persons, define, newTimeZoneDetails, equipments
    } = params;

    if (!this.validateAndExtractData(newNdrFormValue, companies, persons, define, newTimeZoneDetails, equipments)) {
      return null;
    }

    return this.buildDeliveryPayload(params);
  }

  public validateAndExtractData(
    newNdrFormValue: any,
    companies: any[],
    persons: any[],
    define: any[],
    newTimeZoneDetails: any[],
    equipments: any[],
  ): boolean {
    const {
      companyItems, person, defineItems, EquipmentId, TimeZoneId,
    } = newNdrFormValue;

    if (!companyItems?.length) {
      this.formReset();
      this.toastr.error('Responsible Company is required');
      return false;
    }

    if (!person?.length) {
      this.formReset();
      this.toastr.error('Responsible Person is required');
      return false;
    }

    companyItems.forEach((e: { id: any }) => companies.push(e.id));
    person.forEach((e: { id: any }) => persons.push(e.id));
    if (Array.isArray(defineItems)) {
      defineItems.forEach((e: { id: any }) => define.push(e.id));
    }
    if (Array.isArray(EquipmentId)) {
      EquipmentId.forEach((e: { id: any }) => equipments.push(e.id));
    }

    if (TimeZoneId?.length && newTimeZoneDetails?.length) {
      TimeZoneId.forEach((e: { id: any }) => {
        this.timeZoneValues = e.id;
      });
    }

    if (this.checkStringEmptyValues(newNdrFormValue)) {
      this.formReset();
      return false;
    }

    return true;
  }


  public buildDeliveryPayload(params: any): any {
    const {
      newNdrFormValue,
      deliveryStart,
      deliveryEnd,
      companies,
      persons,
      define,
      equipments,
      startPicker,
      endPicker,
      weekStartDate,
      weekEndDate,
    } = params;

    const payload = {
      description: newNdrFormValue.description,
      companies,
      escort: newNdrFormValue.escort ?? false,
      ProjectId: this.ProjectId,
      GateId: newNdrFormValue.GateId,
      notes: newNdrFormValue.notes,
      EquipmentId: equipments,
      deliveryStart,
      deliveryEnd,
      ParentCompanyId: this.ParentCompanyId,
      persons,
      define,
      isAssociatedWithCraneRequest: newNdrFormValue.isAssociatedWithCraneRequest,
      cranePickUpLocation: '',
      craneDropOffLocation: '',
      CraneRequestId: '',
      requestType: 'deliveryRequest',
      LocationId: this.selectedLocationId,
      recurrence: newNdrFormValue.recurrence,
      chosenDateOfMonthValue: newNdrFormValue.chosenDateOfMonth !== false ? newNdrFormValue.chosenDateOfMonth : 1,
      chosenDateOfMonth: newNdrFormValue.chosenDateOfMonth === 1,
      dateOfMonth: newNdrFormValue.dateOfMonth,
      monthlyRepeatType: newNdrFormValue.monthlyRepeatType,
      days: newNdrFormValue.days ? this.sortWeekDays(newNdrFormValue.days) : [],
      repeatEveryType: newNdrFormValue.repeatEveryType ?? null,
      repeatEveryCount: newNdrFormValue.repeatEveryCount?.toString() ?? null,
      TimeZoneId: this.timeZoneValues,
      startPicker,
      endPicker,
      originationAddress: newNdrFormValue.originationAddress,
      vehicleType: this.selectedVehicleType,
    };

    if (payload.isAssociatedWithCraneRequest) {
      payload.cranePickUpLocation = newNdrFormValue.cranePickUpLocation?.trim() || '';
      payload.craneDropOffLocation = newNdrFormValue.craneDropOffLocation?.trim() || '';
      payload.CraneRequestId = newNdrFormValue.CraneRequestId;
      payload.requestType = 'deliveryRequestWithCrane';
    }

    if (['Monthly', 'Yearly'].includes(payload.recurrence)) {
      payload.dateOfMonth = this.monthlyDate;
    }

    if (payload.recurrence) {
      payload.startPicker = startPicker;
      payload.endPicker = endPicker;
      payload.deliveryStart = weekStartDate;
      payload.deliveryEnd = weekEndDate;

      if (startPicker === endPicker) {
        this.toastr.error('Delivery Start time and End time should not be the same');
        this.formSubmitted = false;
        this.submitted = false;
        return null;
      }

      if (startPicker > endPicker) {
        this.toastr.error('Please enter From Time lesser than To Time');
        this.formSubmitted = false;
        this.submitted = false;
        return null;
      }

      if (payload.recurrence !== 'Does Not Repeat'
        && !moment(payload.deliveryEnd).isAfter(moment(payload.deliveryStart))) {
        this.toastr.error('Please enter End Date greater than Start Date');
        this.formSubmitted = false;
        this.submitted = false;
        return null;
      }
    }

    return payload;
  }




  public sortWeekDays(data: any[]): any {
    const order = {
      Sunday: 1,
      Monday: 2,
      Tuesday: 3,
      Wednesday: 4,
      Thursday: 5,
      Friday: 6,
      Saturday: 7,
    };

    if (data.length > 0) {
      return data.sort((a, b): any => order[a] - order[b]);
    }
  }

  public createNDR(payload: {
    description: any;
    companies: any;
    escort: any;
    ProjectId: any;
    GateId: any;
    notes: any;
    EquipmentId: any;
    LocationId: any;
    deliveryStart: any;
    deliveryEnd: any;
    ParentCompanyId: any;
    persons: any;
    define: any;
    isAssociatedWithCraneRequest: any;
    cranePickUpLocation: string;
    craneDropOffLocation: string;
    CraneRequestId: string;
    requestType: string;
    recurrence: any;
    chosenDateOfMonth: any;
    dateOfMonth: any;
    monthlyRepeatType: any;
    days: any;
    repeatEveryType: any;
    repeatEveryCount: any;
  },
  action: any, modalName: any): void {
    if (action === 'template') {
      this.saveAsTemplatePopup(modalName, payload);
    } else if (this.isBookingTemplate) {
      this.createTemplate(payload, null);
    } else {
      this.deliveryService.createNDR(payload).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.socket.emit('NDRCreateHistory', response);
            this.mixpanelService.addMixpanelEvents('Created New Delivery Booking');
            this.formReset();
            this.deliverDetailsForm.reset();
            this.deliveryService.updatedHistory({ status: true }, 'NDRCreateHistory');
            this.resetForm('yes');
            this.NDRTimingChanged = false;
            this.disableSaveAsTemplate = false;
          }
        },
        error: (NDRCreateHistoryError): void => {
          this.formReset();
          this.NDRTimingChanged = false;
          if (NDRCreateHistoryError.message?.statusCode === 400) {
            this.showError(NDRCreateHistoryError);
          } else if (!NDRCreateHistoryError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else if (NDRCreateHistoryError.message.includes('overlaps')) {
            this.toastr.error(NDRCreateHistoryError.message, 'OOPS!');
          } else {
            this.toastr.error(NDRCreateHistoryError.message, 'OOPS!');
          }
        },
      });
    }
  }

  public createTemplate(data, action): void {
    const deliveryDate = new Date(this.deliverDetailsForm.value.deliveryDate);
    const EndDate = new Date(this.deliverDetailsForm.value.endDate);
    const startNewDate = new Date(this.deliverDetailsForm.value.deliveryStart);
    const startHours = startNewDate.getHours();
    const startMinutes = startNewDate.getMinutes();
    const deliveryStart = this.convertStart(deliveryDate, startHours, startMinutes);
    const endNewDate = new Date(this.deliverDetailsForm.value.deliveryEnd);
    const endHours = endNewDate.getHours();
    const endMinutes = endNewDate.getMinutes();
    const deliveryEnd = data.recurrence !== 'Does Not Repeat'
      ? this.convertStart(EndDate, endHours, endMinutes)
      : this.convertStart(deliveryDate, endHours, endMinutes);
    const payload: any = {
      template_name: action === 'saveAsTemplate' ? this.templateName : this.deliverDetailsForm.value.templateName,
      description: data.description,
      responsible_company: JSON.stringify(data.companies),
      responsible_person: JSON.stringify(data.persons),
      date: deliveryStart,
      from_time: deliveryStart,
      to_time: deliveryEnd,
      time_zone: data.TimeZoneId,
      location: data.LocationId,
      notes: data.notes,
      picking_from: data.cranePickUpLocation,
      picking_to: data.craneDropOffLocation,
      is_escort_needed: data.escort ? false : data.escort,
      dfow: JSON.stringify(data.define),
      equipment: JSON.stringify(data.EquipmentId),
      gate: data.GateId,
      isAssociatedWithCraneRequest: data.isAssociatedWithCraneRequest,
      recurrence: {
        recurrence: data.recurrence,
        dateOfMonth: data.recurrence === 'Monthly' || data.recurrence === 'Yearly' ? this.monthlyDate : data.dateOfMonth,
        monthlyRepeatType: data.monthlyRepeatType,
        repeatEveryCount: data.repeatEveryCount
          ? data.repeatEveryCount.toString()
          : null,
        requestType: data.isAssociatedWithCraneRequest ? 'deliveryRequestWithCrane' : 'deliveryRequest',
        repeatEveryType: data.repeatEveryType
          ? data.repeatEveryType
          : null,
        chosenDateOfMonth: data.chosenDateOfMonth,
        endDate: this.deliverDetailsForm.value.endDate,
        days: data.days,
      },
      template_type: 'delivery',
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      originationAddress: data.originationAddress,
      vehicleType: this.selectedVehicleType,
    };
    if (this.editTemplateId && this.isEditTemplate) {
      payload.id = this.editTemplateId;
      this.bookingTemplatesService.editTemplate(payload).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success('Booking Template Updated Successfully', 'Success');
            this.mixpanelService.addMixpanelEvents('Created Delivery Booking Template');
            this.socket.emit('DeliveryTemplateCreate', response);
            this.formReset();
            this.deliverDetailsForm.reset();
            this.resetForm('yes');
            this.NDRTimingChanged = false;
            this.templateSubmitted = false;
            this.editTemplateId = '';
          }
        },
        error: (NDRCreateHistoryError): void => {
          this.formReset();
          this.NDRTimingChanged = false;
          this.templateSubmitted = false;
          if (NDRCreateHistoryError.message?.statusCode === 400) {
            this.showError(NDRCreateHistoryError);
          } else if (!NDRCreateHistoryError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else if (NDRCreateHistoryError.message.includes('overlaps')) {
            this.toastr.error(NDRCreateHistoryError.message, 'OOPS!');
          } else {
            this.toastr.error(NDRCreateHistoryError.message.split(': ')[1], 'OOPS!');
          }
        },
      });
    } else {
      this.bookingTemplatesService.saveTemplate(payload).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success('Booking Template Saved Successfully', 'Success');
            this.mixpanelService.addMixpanelEvents('Created Delivery Booking Template');
            this.socket.emit('DeliveryTemplateCreate', response);
            this.formReset();
            this.deliverDetailsForm.reset();
            this.resetForm('yes');
            this.NDRTimingChanged = false;
            this.templateSubmitted = false;
          }
        },
        error: (NDRCreateHistoryError): void => {
          this.formReset();
          this.NDRTimingChanged = false;
          this.templateSubmitted = false;
          if (NDRCreateHistoryError.message?.statusCode === 400) {
            this.showError(NDRCreateHistoryError);
          } else if (!NDRCreateHistoryError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else if (NDRCreateHistoryError.message.includes('overlaps')) {
            this.toastr.error(NDRCreateHistoryError.message, 'OOPS!');
          } else {
            this.toastr.error(NDRCreateHistoryError.message.split(': ')[1], 'OOPS!');
          }
        },
      });
    }
  }

  public closeTemplateNamePopup(action: string): void {
    if (action === 'no') {
      this.modalRef2.hide();
    } else {
      if (!this.templateName) {
        this.toastr.error('Please Enter a Template Name');
        return;
      }
      this.modalRef2.hide();
      this.templateSubmitted = true;
      this.createTemplate(this.saveAsTemplatePayload, 'saveAsTemplate');
    }
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.deliverDetailsForm.reset();
      this.setDefaultPerson();
      this.deliverDetailsForm.get('GateId').setValue('');
      this.submitted = false;
      this.formSubmitted = false;
      this.editSubmitted = false;
      this.modalRef.hide();
      this.NDRTimingChanged = false;
      this.templateName = '';
      this.editTemplateId = '';
    }
  }

  public close(template: TemplateRef<any>): void {
    if (
      (this.deliverDetailsForm.touched && this.deliverDetailsForm.dirty)
      || (this.deliverDetailsForm.get('defineItems').dirty
        && this.deliverDetailsForm.get('defineItems').value
        && this.deliverDetailsForm.get('defineItems').value.length > 0)
      || (this.deliverDetailsForm.get('companyItems').dirty
        && this.deliverDetailsForm.get('companyItems').value
        && this.deliverDetailsForm.get('companyItems').value.length > 0)
        || (this.deliverDetailsForm.get('EquipmentId').dirty
        && this.deliverDetailsForm.get('EquipmentId').value
        && this.deliverDetailsForm.get('EquipmentId').value.length > 0)
      || this.NDRTimingChanged
    ) {
      this.openConfirmationModalPopup(template);
    } else {
      this.resetForm('yes');
    }
  }

  public deliveryEndTimeChangeDetection(): void {
    this.NDRTimingChanged = true;
  }

  public openConfirmationModalPopup(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public changeDate(event: any): void {
    if (!this.modalLoader) {
      const startTime = new Date(event).getHours();
      const minutes = new Date(event).getMinutes();
      this.deliveryEnd = new Date();
      this.deliveryEnd.setHours(startTime + 1);
      this.deliveryEnd.setMinutes(minutes);
      this.deliverDetailsForm.get('deliveryEnd').setValue(this.deliveryEnd);
      this.NDRTimingChanged = true;
    }
  }

  public setDefaultPerson(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.getMemberRole(params).subscribe((res): void => {
      this.authUser = res.data;
      let email: string;
      if (this.authUser.User.lastName != null) {
        email = `${this.authUser.User.firstName} ${this.authUser?.User?.lastName} (${this.authUser.User.email})`;
      } else {
        email = `${this.authUser.User.firstName} (${this.authUser.User.email})`;
      }
      const newMemberList = [
        {
          email,
          id: this.authUser.id,
          readonly: true,
        },
      ];
      this.deliverDetailsForm.get('person').patchValue(newMemberList);
    });
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public formReset(): void {
    this.formSubmitted = false;
    this.submitted = false;
  }

  public checkStringEmptyValues(formValue: { description: string; notes: string }): boolean {
    if (formValue.description.trim() === '') {
      this.toastr.error('Please Enter valid Company Name.', 'OOPS!');
      return true;
    }
    if (formValue.notes) {
      if (formValue.notes.trim() === '') {
        this.toastr.error('Please Enter valid address.', 'OOPS!');
        return true;
      }
    }
    return false;
  }

  public formControlValueChanged(): void {
    this.deliverDetailsForm.get('repeatEveryType').valueChanges.subscribe((value: string): void => {
      const days = this.deliverDetailsForm.get('days');
      const chosenDateOfMonth = this.deliverDetailsForm.get('chosenDateOfMonth');
      const dateOfMonth = this.deliverDetailsForm.get('dateOfMonth');
      const monthlyRepeatType = this.deliverDetailsForm.get('monthlyRepeatType');
      if (value === 'Week' || value === 'Day' || value === 'Weeks') {
        days.setValidators([Validators.required]);
      } else {
        days.clearValidators();
      }
      if (value === 'Month' || value === 'Months' || value === 'Year' || value === 'Years') {
        if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 1) {
          dateOfMonth.setValidators([Validators.required]);
          monthlyRepeatType.clearValidators();
        } else {
          monthlyRepeatType.setValidators([Validators.required]);
          dateOfMonth.clearValidators();
        }
      } else {
        chosenDateOfMonth.clearValidators();
        dateOfMonth.clearValidators();
        monthlyRepeatType.clearValidators();
      }
      chosenDateOfMonth.updateValueAndValidity();
      dateOfMonth.updateValueAndValidity();
      monthlyRepeatType.updateValueAndValidity();
      days.updateValueAndValidity();
    });
  }

  public setCurrentTiming(): void {
    const newDate = moment().format('MM-DD-YYYY');
    const hours = moment(new Date()).format('HH');
    this.startTime = new Date();
    this.startTime.setHours(+hours);
    this.startTime.setMinutes(0);
    this.endTime = new Date();
    this.endTime.setHours(+hours);
    this.endTime.setMinutes(30);
    if (!this.deliverDetailsForm.get('endDate')?.value) {
      this.deliverDetailsForm.get('endDate').setValue(newDate);
    }
    this.changeMonthlyRecurrence();
  }

  public chooseRepeatEveryType(value: string, eventDetail: any): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    if (value === 'Day' || value === 'Days') {
      this.deliverDetailsForm.get('recurrence').setValue('Daily');
    }
    if (value === 'Week' || value === 'Weeks') {
      this.deliverDetailsForm.get('recurrence').setValue('Weekly');
    }
    if (value === 'Month' || value === 'Months') {
      this.deliverDetailsForm.get('recurrence').setValue('Monthly');
    }
    if (value === 'Year' || value === 'Years') {
      this.deliverDetailsForm.get('recurrence').setValue('Yearly');
    }
    if (value === 'Day' || value === 'Days') {
      this.checkform = this.deliverDetailsForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day: any): any => {
        const dayObj = day;
        dayObj.checked = true;
        dayObj.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj.value));
        return dayObj;
      });
    }
    if (value === 'Week' || value === 'Weeks') {
      if (eventDetail?.days?.length > 0) {
        this.checkform = this.deliverDetailsForm.get('days') as UntypedFormArray;
        this.checkform.controls = [];
        this.weekDays = this.weekDays.map((day1: any): void => {
          const dayObj1 = day1;
          if (eventDetail.days.includes(dayObj1.value)) {
            dayObj1.checked = true;
            this.checkform.push(new UntypedFormControl(dayObj1.value));
          } else {
            dayObj1.checked = false;
          }
          dayObj1.isDisabled = false;
          return dayObj1;
        });
      } else {
        this.weekDays = this.weekDays.map((day2: any): void => {
          const dayObj2 = day2;
          if (dayObj2.value === 'Monday') {
            dayObj2.checked = true;
          } else {
            dayObj2.checked = false;
          }
          dayObj2.isDisabled = false;
          return dayObj2;
        });
        this.checkform = this.deliverDetailsForm.get('days') as UntypedFormArray;
        this.checkform.controls = [];
        this.checkform.push(new UntypedFormControl('Monday'));
      }
    }
    if (value === 'Day' || value === 'Week' || value === 'Month' || value === 'Year') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showRecurrenceTypeDropdown = false;
    }
    if (value === 'Days' || value === 'Weeks' || value === 'Months' || value === 'Years') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.showRecurrenceTypeDropdown = false;
    }
    if (this.deliverDetailsForm.get('repeatEveryCount').value > 1) {
      this.showRecurrenceTypeDropdown = true;
      this.isRepeatWithMultipleRecurrence = false;
    }
    this.selectedRecurrence = this.deliverDetailsForm.get('recurrence').value;
    this.occurMessage();
  }

  public occurMessage(): void {
    this.message = '';
    this.repeatEveryTypeMassage();
    if (this.deliverDetailsForm.get('repeatEveryType').value === 'Week') {
      let weekDays = '';
      this.weekDays.map((dayObj1: any): any => {
        if (dayObj1.checked) {
          weekDays = `${weekDays + dayObj1.value},`;
        }
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message += `Occurs every ${weekDays}`;
    }
    if (this.deliverDetailsForm.get('repeatEveryType').value === 'Weeks') {
      let weekDays = '';
      this.weekDays.map((dayObj2: any): any => {
        if (dayObj2.checked) {
          weekDays = `${weekDays + dayObj2.value},`;
        }
        return false;
      });
      if (+this.deliverDetailsForm.get('repeatEveryCount').value === 2) {
        this.message = `Occurs every other  ${weekDays}`;
      } else {
        this.message = `Occurs every ${
          this.deliverDetailsForm.get('repeatEveryCount').value
        } weeks on ${weekDays}`;
      }
      weekDays = weekDays.replace(/,\s*$/, '');
    }
    if (
      this.deliverDetailsForm.get('repeatEveryType').value === 'Month'
      || this.deliverDetailsForm.get('repeatEveryType').value === 'Months'
      || this.deliverDetailsForm.get('repeatEveryType').value === 'Year'
      || this.deliverDetailsForm.get('repeatEveryType').value === 'Years'
    ) {
      if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 1) {
        this.message = `Occurs on day ${this.monthlyDate}`;
      } else if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 2) {
        this.message = `Occurs on the ${this.monthlyDayOfWeek}`;
      } else {
        this.message = `Occurs on the ${this.monthlyLastDayOfWeek}`;
      }
    }
    if (this.message) {
      this.message += ` until ${moment(this.deliverDetailsForm.get('endDate').value).format(
        'MMMM DD, YYYY',
      )}`;
    }
  }

  public repeatEveryTypeMassage() {
    if (this.deliverDetailsForm.get('repeatEveryType').value === 'Day') {
      this.message = 'Occurs every day';
    }
    if (this.deliverDetailsForm.get('repeatEveryType').value === 'Days') {
      if (+this.deliverDetailsForm.get('repeatEveryCount').value === 2) {
        this.message = 'Occurs every other day';
      } else {
        this.message = `Occurs every ${this.deliverDetailsForm.get('repeatEveryCount').value} days`;
      }
    }
  }

  public changeRecurrenceCount(value: number): void {
    if (value > 0) {
      const recurrencedata = this.deliverDetailsForm.get('recurrence').value;

      this.updateRecurrenceFlags(recurrencedata, value);
      this.updateRepeatEveryType(recurrencedata, value);

      this.selectedRecurrence = recurrencedata;
      this.occurMessage();
    } else if (value < 0) {
      this.deliverDetailsForm.get('repeatEveryCount').setValue(1);
    }
  }

  public updateRecurrenceFlags(recurrencedata: string, value: number): void {
    const isSingle = +value === 1;

    if (recurrencedata === 'Daily') {
      this.isRepeatWithSingleRecurrence = isSingle;
      this.isRepeatWithMultipleRecurrence = false;
      this.showRecurrenceTypeDropdown = !isSingle;
    }

    if (recurrencedata === 'Weekly') {
      this.isRepeatWithSingleRecurrence = isSingle;
      this.isRepeatWithMultipleRecurrence = !isSingle;
      this.showRecurrenceTypeDropdown = false;
    }

    if (recurrencedata === 'Monthly' || recurrencedata === 'Yearly') {
      this.isRepeatWithSingleRecurrence = isSingle;
      this.isRepeatWithMultipleRecurrence = !isSingle;
      this.showRecurrenceTypeDropdown = false;
    }
  }

  public updateRepeatEveryType(recurrencedata: string, value: number): void {
    const repeatEveryTypeControl = this.deliverDetailsForm.get('repeatEveryType');

    switch (recurrencedata) {
      case 'Daily':
        repeatEveryTypeControl.setValue(value > 1 ? 'Days' : 'Day');
        break;

      case 'Weekly':
        repeatEveryTypeControl.setValue(value > 1 ? 'Weeks' : 'Week');
        break;

      case 'Monthly':
        repeatEveryTypeControl.setValue(value > 1 ? 'Months' : 'Month');
        this.changeMonthlyRecurrence();
        this.showMonthlyRecurrence();
        break;

      case 'Yearly':
        repeatEveryTypeControl.setValue(value > 1 ? 'Years' : 'Year');
        break;

      default:
        break;
    }
  }

  public changeMonthlyRecurrence(): void {
    this.setMonthlyOrYearlyRecurrenceOption();
    this.updateFormValidation();
    this.showMonthlyRecurrence();
    this.occurMessage();
  }

  public setMonthlyOrYearlyRecurrenceOption(): void {
    if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 1) {
      this.deliverDetailsForm
        .get('dateOfMonth')
        .setValue(moment(this.deliverDetailsForm.get('deliveryDate').value).format('DD'));
      this.deliverDetailsForm.get('monthlyRepeatType').setValue(null);
    } else if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 2) {
      this.deliverDetailsForm.get('dateOfMonth').setValue(null);
      this.deliverDetailsForm.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
    } else if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 3) {
      this.deliverDetailsForm.get('dateOfMonth').setValue(null);
      this.deliverDetailsForm.get('monthlyRepeatType').setValue(this.monthlyLastDayOfWeek);
    }
  }

  public showMonthlyRecurrence(): void {
    if (this.deliverDetailsForm.get('deliveryDate').value) {
      this.generateWeekDates(moment(this.deliverDetailsForm.get('deliveryDate').value).format('YYYY-MM-DD'))
      const startDate = moment(this.deliverDetailsForm.get('deliveryDate').value).format('YYYY-MM');
      const chosenDay = moment(this.deliverDetailsForm.get('deliveryDate').value).format('dddd');
      this.monthlyDate = moment(this.deliverDetailsForm.get('deliveryDate').value).format('DD');
      const day = moment(startDate, 'YYYY-MM').startOf('month').day(chosenDay);
      const getAllDays = [];
      if (day.date() > 7) day.add(7, 'd');
      const month = day.month();
      while (month === day.month()) {
        getAllDays.push(day.format('YYYY-MM-DD'));
        day.add(7, 'd');
      }
      let week: string;
      let extraOption: string;
      this.enableOption = false;
      getAllDays.forEach((element, i): void => {
        if (
          moment(this.deliverDetailsForm.get('deliveryDate').value).format('YYYY-MM-DD')
          === moment(element).format('YYYY-MM-DD')
        ) {
          const number = i + 1;
          if (number === 1) {
            week = 'First';
          }
          if (number === 2) {
            week = 'Second';
          }
          if (number === 3) {
            week = 'Third';
          }
          if (number === 4) {
            this.enableOption = true;
            extraOption = 'Last';
            week = 'Fourth';
          }
          if (number === 5) {
            week = 'Last';
          }
          if (number === 6) {
            week = 'Last';
          }
        }
      });
      this.monthlyDayOfWeek = `${week} ${chosenDay}`;
      this.monthlyLastDayOfWeek = `${extraOption} ${chosenDay}`;
      if (!this.enableOption && this.deliverDetailsForm.get('chosenDateOfMonth').value === 3) {
        this.deliverDetailsForm.get('chosenDateOfMonth').setValue(2);
        this.deliverDetailsForm.get('dateOfMonth').setValue(null);
        this.deliverDetailsForm.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
      }
      this.setMonthlyOrYearlyRecurrenceOption();
      this.occurMessage();
      if(this.deliverDetailsForm.get('endDate').value){
        this.updateWeeklyDates()
      }
    }
  }

  public updateWeeklyDates() {
    const deliveryEnd = this.deliverDetailsForm.get('endDate').value;

    if (deliveryEnd) {
      let endDate = moment(deliveryEnd, 'YYYY-MM-DD');

      let currentDate;
      if (this.weekDates.length > 0) {
        currentDate = moment(this.weekDates[0].fullDate, 'YYYY-MM-DD');
      } else {
        currentDate = moment();
      }

      while (currentDate.isSameOrBefore(endDate)) {
        const exists = this.weekDates.some(weekDate => weekDate.fullDate === currentDate.format('YYYY-MM-DD'));

        if (!exists) {
          this.weekDates.push({
            name: currentDate.format('ddd'),
            date: currentDate.format('DD'),
            fullDate: currentDate.format('YYYY-MM-DD'),
          });
        }

        currentDate.add(1, 'day');
      }
    }
  }


  public updateFormValidation(): void {
    const chosenDateOfMonth = this.deliverDetailsForm.get('chosenDateOfMonth');
    const dateOfMonth = this.deliverDetailsForm.get('dateOfMonth');
    const monthlyRepeatType = this.deliverDetailsForm.get('monthlyRepeatType');
    if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 1) {
      dateOfMonth.setValidators([Validators.required]);
      monthlyRepeatType.clearValidators();
    } else {
      monthlyRepeatType.setValidators([Validators.required]);
      dateOfMonth.clearValidators();
    }
    chosenDateOfMonth.updateValueAndValidity();
    dateOfMonth.updateValueAndValidity();
    monthlyRepeatType.updateValueAndValidity();
  }

  public onChange(event: { target: { value: any; checked: any } }): void {
    this.checkform = this.deliverDetailsForm.get('days') as UntypedFormArray;
    this.valueExists = this.checkform.controls.filter(
      (object: { value: any }): any => object.value === event.target.value,
    );
    if (event.target.checked) {
      this.checkform.push(new UntypedFormControl(event.target.value));
      this.weekDays = this.weekDays.map((day16: any): void => {
        const dayObj16 = day16;
        if (day16.value === event.target.value) {
          dayObj16.checked = true;
        }
        return dayObj16;
      });
      if (this.checkform.controls.length === 2) {
        this.weekDays = this.weekDays.map((day17: any): void => {
          const dayObj17 = day17;
          dayObj17.isDisabled = false;
          return dayObj17;
        });
      }
    } else if (this.selectedRecurrence === 'Weekly') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day18: any): void => {
            const dayObj18 = day18;
            if (dayObj18.value === event.target.value) {
              dayObj18.checked = false;
            }
            return dayObj18;
          });
        }
        if (this.checkform.controls.length === 1) {
          this.weekDays = this.weekDays.map((day19: any): void => {
            const dayObj19 = day19;
            if (dayObj19.value === this.checkform.controls[0].value) {
              dayObj19.isDisabled = true;
              dayObj19.checked = true;
            }
            return dayObj19;
          });
          return;
        }
        i += 1;
      });
    } else if (this.selectedRecurrence === 'Daily') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day: any): void => {
            const dayObj = day;
            if (dayObj.value === event.target.value) {
              dayObj.checked = false;
              dayObj.isDisabled = false;
            }
            return dayObj;
          });
          return;
        }
        i += 1;
      });
    }
    if (this.checkform.controls.length !== 7) {
      this.deliverDetailsForm.get('recurrence').setValue('Weekly');
      if (+this.deliverDetailsForm.get('repeatEveryCount').value === 1) {
        this.deliverDetailsForm.get('repeatEveryType').setValue('Week');
      } else {
        this.deliverDetailsForm.get('repeatEveryType').setValue('Weeks');
      }
      this.selectedRecurrence = this.deliverDetailsForm.get('recurrence').value;
    }
    if (this.checkform.controls.length === 7) {
      this.deliverDetailsForm.get('recurrence').setValue('Daily');
      if (+this.deliverDetailsForm.get('repeatEveryCount').value === 1) {
        this.deliverDetailsForm.get('repeatEveryType').setValue('Day');
      } else {
        this.deliverDetailsForm.get('repeatEveryType').setValue('Days');
      }
      this.selectedRecurrence = this.deliverDetailsForm.get('recurrence').value;
    }
    this.occurMessage();
  }

  public getRepeatEveryType(value) {
    if (value === 'Does Not Repeat') {
      this.deliverDetailsForm.get('repeatEveryType').setValue('');
    } else {
      this.deliverDetailsForm.get('repeatEveryCount').setValue(1);
    }
    if (value === 'Daily') {
      this.deliverDetailsForm.get('repeatEveryType').setValue('Day');
    }
    if (value === 'Weekly') {
      this.deliverDetailsForm.get('repeatEveryType').setValue('Week');
    }
    if (value === 'Monthly') {
      this.deliverDetailsForm.get('repeatEveryType').setValue('Month');
    }
    if (value === 'Yearly') {
      this.deliverDetailsForm.get('repeatEveryType').setValue('Year');
    }
  }

  public onRecurrenceSelect(value: string): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    this.selectedRecurrence = value;
    this.getRepeatEveryType(value);

    if (this.deliverDetailsForm.get('repeatEveryCount').value > 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.checkform = this.deliverDetailsForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day11: any): void => {
        const dayObj11 = day11;
        dayObj11.checked = true;
        dayObj11.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj11.value));
        return dayObj11;
      });
    }
    if (this.deliverDetailsForm.get('repeatEveryCount').value > 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.weekDays = this.weekDays.map((day12: any): void => {
        const dayObj12 = day12;
        if (dayObj12.value === 'Monday') {
          dayObj12.checked = true;
        } else {
          dayObj12.checked = false;
        }
        dayObj12.isDisabled = false;
        return dayObj12;
      });
      this.checkform = this.deliverDetailsForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.deliverDetailsForm.get('repeatEveryCount').value === 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.weekDays = this.weekDays.map((day13: any): void => {
        const dayObj13 = day13;
        if (dayObj13.value === 'Monday') {
          dayObj13.checked = true;
        } else {
          dayObj13.checked = false;
        }
        dayObj13.isDisabled = false;
        return dayObj13;
      });
      this.checkform = this.deliverDetailsForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.deliverDetailsForm.get('repeatEveryCount').value === 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.checkform = this.deliverDetailsForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day14: any): void => {
        const dayObj14 = day14;
        dayObj14.checked = true;
        dayObj14.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj14.value));
        return dayObj14;
      });
    }
    if (
      this.deliverDetailsForm.get('repeatEveryCount').value === 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.deliverDetailsForm.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showMonthlyRecurrence();
    }
    if (
      this.deliverDetailsForm.get('repeatEveryCount').value > 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.deliverDetailsForm.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.showMonthlyRecurrence();
    }
    this.occurMessage();
  }

  public timeZoneSelected(id: string | number): void {
    this.selectedTimeZoneValue = this.timezoneList.find((obj: any): any => +obj.id === +id);
  }

  public locationSelected(data: { id: string | number }): void {
    const getChosenLocation = this.locationList.find((obj: any): any => +obj.id === +data.id);
    if (getChosenLocation) {
      this.deliverDetailsForm.get('GateId').setValue('');
      this.deliverDetailsForm
        .get('EquipmentId')
        .setValue('');
      this.gateList = []
      this.equipmentList = []
      if(getChosenLocation.gateDetails){
        this.gateList = getChosenLocation.gateDetails;
        this.equipmentList = [this.noEquipmentOption, ...getChosenLocation.EquipmentId];
        this.timeZone = getChosenLocation.TimeZoneId[0].location
      }
    }
    this.selectedLocationId = getChosenLocation?.id;
  }

  public vehicleTypeSelected(data: { id: string | number }): void {
    const getVehicleType = this.vehicleTypes.find((obj: any): any => +obj.id === +data.id);
    this.selectedVehicleType = getVehicleType?.type;
  }

  public getTimeZoneList(): void {
    this.projectService.getTimeZoneList().subscribe({
      next: (response: any): void => {
        this.loader = true;
        if (response) {
          const params = {
            ProjectId: this.ProjectId,
          };
          if (params.ProjectId) {
            this.projectService.getSingleProject(params).subscribe((projectList: any): void => {
              if (projectList) {
                this.timezoneList = response.data;
                this.dropdownSettings = {
                  singleSelection: true,
                  idField: 'id',
                  textField: 'location',
                  allowSearchFilter: true,
                  closeDropDownOnSelection: true,
                };
                this.getSelectedTimeZone = this.timezoneList.filter(
                  (obj: any): any => +obj.id === +projectList.data.TimeZoneId,
                );
                this.deliverDetailsForm.get('TimeZoneId').patchValue(this.getSelectedTimeZone);
                this.loader = false;
              }
            });
          }
        }
      },
      error: (getTimeZoneListErr): void => {
        if (getTimeZoneListErr.message?.statusCode === 400) {
          this.showError(getTimeZoneListErr);
        } else if (!getTimeZoneListErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(getTimeZoneListErr.message, 'OOPS!');
        }
      },
    });
  }

  public saveAsTemplatePopup(template: TemplateRef<any>, payload): void {
    this.saveAsTemplatePayload = '';
    this.saveAsTemplatePayload = payload;
    const templateName = this.deliverDetailsForm.get('templateName');
    templateName.setValidators([Validators.required]);
    if (!this.selectedTemplate) {
      let data = {};
      data = {
        keyboard: false,
        class: 'modal-md openTemplateName-popup modal-dialog-centered custom-modal',
      };
      this.modalRef2 = this.modalService.show(template, data);
    }
  }

  public template(object): void {
    const getSelected = this.templateList.find((obj: any): any => +obj.id === +object.id);
    this.selectedTemplate = getSelected;
    this.disableSaveAsTemplate = true;
    this.setBookingTemplateData(this.selectedTemplate);
  }

  public getTemplates(): void {
    this.loader = true;
    this.templateDropdownSettings = {
      singleSelection: true,
      idField: 'id',
      textField: 'template_name',
      allowSearchFilter: true,
      closeDropDownOnSelection: true,
    };
    if (this.ProjectId) {
      const params = {
        ParentCompanyId: this.ParentCompanyId,
        isDropdown: true,
      };
      const queryPath = {
        ProjectId: this.ProjectId,
        isDropdown: true,
      };
      this.bookingTemplatesService.getTemplates(queryPath, params).subscribe((res): void => {
        this.templateList = res.data.rows;
        const templateName = this.deliverDetailsForm.get('templateName');
        if (this.isBookingTemplate || this.isEditTemplate) {
          templateName.setValidators([Validators.required]);
        } else {
          templateName.clearValidators();
        }
        this.loader = false;
      });
    }
  }

  public setBookingTemplateData(data): void {
    this.selectedTemplate = data;
    this.loader = true;
    if (this.deliverDetailsForm) {
      this.deliverDetailsForm.get('templateName').setValue(this.selectedTemplate.template_name);
      this.deliverDetailsForm.get('description').setValue(this.selectedTemplate.description);
      this.deliverDetailsForm
        .get('deliveryDate')
        .setValue(moment(this.selectedTemplate.date).format('MM/DD/YYYY'));
      this.deliverDetailsForm
        .get('deliveryStart')
        .setValue(new Date(this.selectedTemplate.from_time));
      this.deliverDetailsForm.get('deliveryEnd').setValue(new Date(this.selectedTemplate.to_time));
      this.deliverDetailsForm.get('escort').setValue(this.selectedTemplate.is_escort_needed);
      this.deliverDetailsForm.get('notes').setValue(this.selectedTemplate.notes);
      this.deliverDetailsForm.get('GateId').setValue(this.selectedTemplate.gate);
      this.deliverDetailsForm.get('originationAddress').setValue(this.selectedTemplate.originationAddress);
      this.deliverDetailsForm
        .get('EquipmentId')
        .setValue(JSON.parse(this.selectedTemplate.equipment));
      const { isAssociatedWithCraneRequest } = this.selectedTemplate;
      if (isAssociatedWithCraneRequest) {
        this.deliverDetailsForm
          .get('isAssociatedWithCraneRequest')
          .setValue(this.selectedTemplate.isAssociatedWithCraneRequest);
      } else {
        this.deliverDetailsForm.get('isAssociatedWithCraneRequest').setValue(false);
      }
      this.deliverDetailsForm
        .get('cranePickUpLocation')
        .setValue(this.selectedTemplate.picking_from);
      this.deliverDetailsForm
        .get('craneDropOffLocation')
        .setValue(this.selectedTemplate.picking_to);
      if (this.deliverDetailsForm.get('isAssociatedWithCraneRequest').value) {
        this.craneEquipmentTypeChosen = true;
      }
      this.setlocation();
      this.setVehicleType();
      this.setCompany();
      this.setEquipment();
      this.setDefine();
      this.setMember();
      this.setTimeZone();
      this.setRecurrence();
    }
  }

  public setVehicleType(): void {
    if (this.selectedTemplate.vehicleType) {
      const vehicleTypeChosen = [];
      const getVehicleChosen = this.vehicleTypes.find((obj: any): any => obj.type === this.selectedTemplate.vehicleType);
      const data = {
        id: getVehicleChosen.id,
        type: getVehicleChosen.type,
      };
      vehicleTypeChosen.push(data);
      this.deliverDetailsForm.get('vehicleType').patchValue(vehicleTypeChosen);
      this.selectedVehicleType = getVehicleChosen?.type;
    }
  }

  public setCompany(): void {
    if (this.companyList?.length === 0) {
      const newNdrGetCompaniesParams = {
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.projectService
        .getCompanies(newNdrGetCompaniesParams)
        .subscribe((companiesResponseForNewNdr: any): void => {
          if (companiesResponseForNewNdr) {
            this.companyList = companiesResponseForNewNdr.data;
            const companyDetails = JSON.parse(this.selectedTemplate.responsible_company);
            const selectedCompanies = this.companyList.filter((company): any => companyDetails.includes(company.id));
            this.deliverDetailsForm.get('companyItems').patchValue(selectedCompanies);
          }
        });
    } else {
      const companyDetails = JSON.parse(this.selectedTemplate.responsible_company);
      const selectedCompanies = this.companyList.filter((company): any => companyDetails.includes(company.id));
      this.deliverDetailsForm.get('companyItems').patchValue(selectedCompanies);
    }
  }

  public setMember(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
        const memberDetails = JSON.parse(this.selectedTemplate.responsible_person);
        const selectedMembers = this.memberList.filter((member): any => memberDetails.includes(member.id));
        const newMemberList = [];
        selectedMembers.forEach(
          (element:any): void => {
            let email: string;
            if (element?.User?.firstName != null && element?.isGuestUser === false) {
              email = `${element?.User?.firstName} ${element?.User?.lastName} (${element?.User?.email})`;
            } else if (element?.User?.firstName != null && element?.isGuestUser === true) {
              email = `${element?.User?.firstName} ${element?.User?.lastName} (${element?.User?.email} - Guest)`;
            } else if (element?.User?.firstName == null && element?.isGuestUser === false) {
              email = `(${element?.User?.email})`;
            } else if (element?.User?.firstName == null && element?.isGuestUser === true) {
              email = `(${element?.User?.email} - Guest)`;
            }
            const data: any = {
              email,
              id: element?.id,
            };
            if (element?.User?.email === this.authUser?.User?.email) {
              data.readonly = true;
            }
            if (element?.User?.email) {
              newMemberList.push(data);
            }
          },
        );
        this.deliverDetailsForm.get('person').patchValue(newMemberList);
      }
    });
  }

  public setEquipment(): void {
    if (this.equipmentList?.length === 0) {
      const newNdrGetEquipmentsParams = {
        ProjectId: this.ProjectId,
        pageSize: 0,
        pageNo: 0,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.projectService
        .listEquipment(newNdrGetEquipmentsParams, {
          isFilter: true,
          showActivatedAlone: true,
        })
        .subscribe((equipmentListResponseForNewNdr): void => {
          this.equipmentList = [this.noEquipmentOption, ...equipmentListResponseForNewNdr.data];
          const equipmentDetails = JSON.parse(this.selectedTemplate.equipment);
          const selectedEquipment = this.equipmentList.filter((equipment): any => equipmentDetails.includes(equipment.id));
          this.deliverDetailsForm.get('EquipmentId').patchValue(selectedEquipment);
          this.checkEquipmentType(selectedEquipment);
          if (this.craneEquipmentTypeChosen) {
            this.deliverDetailsForm.get('cranePickUpLocation').patchValue(this.selectedTemplate.picking_from);
          }
        });
    } else {
      const equipmentDetails = JSON.parse(this.selectedTemplate.equipment);
      const selectedEquipment = this.equipmentList.filter((equipment): any => equipmentDetails.includes(equipment.id));
      this.deliverDetailsForm.get('EquipmentId').patchValue(selectedEquipment);
      this.checkEquipmentType(selectedEquipment);
      if (this.craneEquipmentTypeChosen) {
        this.deliverDetailsForm.get('craneDropOffLocation').patchValue(this.selectedTemplate.picking_to);
      }
    }
  }

  public setDefine(): void {
    if (this.defineList?.length === 0) {
      const param = {
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.projectService.getDefinableWork(param).subscribe((response: any): void => {
        if (response) {
          const { data } = response;
          this.defineList = data;
          const defineWorkDetails = JSON.parse(this.selectedTemplate.dfow);
          const selectedDfow = this.defineList.filter((dfow): any => defineWorkDetails.includes(dfow.id));
          this.deliverDetailsForm.get('defineItems').patchValue(selectedDfow);
        }
      });
    } else {
      const defineWorkDetails = JSON.parse(this.selectedTemplate.dfow);
      const selectedDfow = this.defineList.filter((dfow): any => defineWorkDetails.includes(dfow.id));
      this.deliverDetailsForm.get('defineItems').patchValue(selectedDfow);
    }
  }

  public setlocation(): void {
    const { location } = this.selectedTemplate;
    if (this.locationList?.length === 0) {
      const param = {
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.projectService.getLocations(param).subscribe((response: any): void => {
        if (response) {
          const { data } = response;
          this.locationList = data;
          const selectedLocation = this.locationList.filter((loc): any => location === loc.id);
          this.deliverDetailsForm.get('LocationId').patchValue(selectedLocation);
        }
      });
    } else {
      const selectedLocation = this.locationList.filter((loc): any => location === loc.id);
      this.deliverDetailsForm.get('LocationId').patchValue(selectedLocation);
    }
  }

  public setTimeZone(): void {
    if (!this.timezoneList) {
      this.projectService.getTimeZoneList().subscribe(
        (response: any): void => {
          this.timezoneList = response.data;
          this.getSelectedTimeZone = this.timezoneList.filter(
            (obj: any): any => +obj.id === +this.selectedTemplate.time_zone,
          );
          this.deliverDetailsForm.get('TimeZoneId').patchValue(this.getSelectedTimeZone);
        },
      );
    } else {
      this.getSelectedTimeZone = this.timezoneList.filter(
        (obj: any): any => +obj.id === +this.selectedTemplate.time_zone,
      );
      this.deliverDetailsForm.get('TimeZoneId').patchValue(this.getSelectedTimeZone);
    }
  }

  public setRecurrence(): void {
    if (this.selectedTemplate.recurrence) {
      const recurrenceDetails = JSON.parse(this.selectedTemplate.recurrence);
      this.deliverDetailsForm.get('recurrence').setValue(recurrenceDetails?.recurrence);
      this.deliverDetailsForm.get('dateOfMonth').setValue(recurrenceDetails?.dateOfMonth);
      this.deliverDetailsForm.get('monthlyRepeatType').setValue(recurrenceDetails?.monthlyRepeatType);
      this.deliverDetailsForm.get('repeatEveryCount').setValue(recurrenceDetails?.repeatEveryCount);
      this.deliverDetailsForm.get('repeatEveryType').setValue(recurrenceDetails?.repeatEveryType);
      if (recurrenceDetails.chosenDateOfMonth) {
        this.deliverDetailsForm.get('chosenDateOfMonth').setValue(1);
      } else {
        this.showMonthlyRecurrence();
        if (this.deliverDetailsForm.get('monthlyRepeatType').value === this.monthlyDayOfWeek) {
          this.deliverDetailsForm.get('chosenDateOfMonth').setValue(2);
        }
        if (this.deliverDetailsForm.get('monthlyRepeatType').value === this.monthlyLastDayOfWeek) {
          this.deliverDetailsForm.get('chosenDateOfMonth').setValue(3);
        }
      }
      this.changeRecurrenceCount(+recurrenceDetails.repeatEveryCount);
      this.deliverDetailsForm
        .get('endDate')
        .setValue(
          moment(recurrenceDetails?.endDate).format('MM/DD/YYYY'),
        );
      this.chooseRepeatEveryType(recurrenceDetails.repeatEveryType, recurrenceDetails);
    }
  }
}
