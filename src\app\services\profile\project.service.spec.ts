import { TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { ProjectService } from './project.service';
import { ApiService } from '../api_base/api.service';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { OverrideRequestComponent } from '../../override-request/override-request.component';

describe('ProjectService', (): void => {
  let service: ProjectService;
  let apiServiceMock: jest.Mocked<ApiService>;
  let routerMock: jest.Mocked<Router>;
  let localStorageMock: any;

  beforeEach((): void => {
    // Create mocks
    apiServiceMock = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      putMethod: jest.fn()
    } as unknown as jest.Mocked<ApiService>;

    routerMock = {
      navigate: jest.fn()
    } as unknown as jest.Mocked<Router>;

    // Mock localStorage
    localStorageMock = {
      getItem: jest.fn(),
      setItem: jest.fn(),
      removeItem: jest.fn()
    };
    Object.defineProperty(window, 'localStorage', { value: localStorageMock });

    TestBed.configureTestingModule({
      declarations: [OverrideRequestComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
      imports: [
        RouterTestingModule.withRoutes([]),
        HttpClientTestingModule,
      ],
      providers: [
        ProjectService,
        { provide: ApiService, useValue: apiServiceMock },
        { provide: Router, useValue: routerMock }
      ]
    });

    service = TestBed.inject(ProjectService);
  });

  it('should be created', (): void => {
    expect(service).toBeTruthy();
  });

  describe('BehaviorSubject methods', (): void => {
    it('should update project ID', (): void => {
      // Arrange
      const testId = '123';
      let result: any;

      // Subscribe to the BehaviorSubject
      service.projectDetails.subscribe(data => {
        result = data;
      });

      // Act
      service.updatedProjectId(testId);

      // Assert
      expect(result).toEqual(testId);
    });

    it('should update company list', (): void => {
      // Arrange
      const testData = [
        { id: 1, name: 'Company 1' },
        { id: 2, name: 'Company 2' }
      ];
      let result: any;

      // Subscribe to the BehaviorSubject
      service.companyList.subscribe(data => {
        result = data;
      });

      // Act
      service.updateCompanyList(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update project account', (): void => {
      // Arrange
      const testData = { isProjectAccount: true };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.isProjectAccount.subscribe(data => {
        result = data;
      });

      // Act
      service.updateProjectAccount(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should refresh project status', (): void => {
      // Arrange
      const testData = { refresh: true };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.refreshProject.subscribe(data => {
        result = data;
      });

      // Act
      service.refreshProjectStatus(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update subscription status', (): void => {
      // Arrange
      const testData = { status: 'active' };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.subStatus.subscribe(data => {
        result = data;
      });

      // Act
      service.updatedSubStatus(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should set project with timeout', (): void => {
      // Arrange
      jest.useFakeTimers();
      const testData = { id: 123, name: 'Test Project' };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.isProject.subscribe(data => {
        result = data;
      });

      // Act
      service.setProject(testData);

      // Assert initial value
      expect(result).toEqual(testData);

      // Fast-forward time
      jest.advanceTimersByTime(100);

      // Assert after timeout
      expect(result).toBeNull();

      jest.useRealTimers();
    });

    it('should update user data', (): void => {
      // Arrange
      const testData = { id: 1, name: 'Test User' };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.userData.subscribe(data => {
        result = data;
      });

      // Act
      service.updatedUserData(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update dashboard siteplan', (): void => {
      // Arrange
      const testData = { uploaded: true };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.siteplanUploaded.subscribe(data => {
        result = data;
      });

      // Act
      service.updateDashboardSiteplan(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update project name in side menu', (): void => {
      // Arrange
      const testData = 'Test Project';
      let result: any;

      // Subscribe to the BehaviorSubject
      service.projectNameInSideMenu.subscribe(data => {
        result = data;
      });

      // Act
      service.updatedProjectNameInSideMenu(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update clear project', (): void => {
      // Arrange
      jest.useFakeTimers();
      const testData = { id: 123 };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.clearProject.subscribe(data => {
        result = data;
      });

      // Act
      service.updateClearProject(testData);

      // Assert initial value
      expect(result).toEqual(testData);

      // Fast-forward time
      jest.advanceTimersByTime(100);

      // Assert after timeout
      expect(result).toBeNull();

      jest.useRealTimers();
    });

    it('should update project parent', (): void => {
      // Arrange
      const testData = { id: 123 };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.projectParent.subscribe(data => {
        result = data;
      });

      // Act
      service.updateProjectParent(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update account project parent', (): void => {
      // Arrange
      const testData = { id: 123 };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.accountProjectParent.subscribe(data => {
        result = data;
      });

      // Act
      service.updateAccountProjectParent(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update account admin', (): void => {
      // Arrange
      const testData = { isAdmin: true };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.isAccountAdmin.subscribe(data => {
        result = data;
      });

      // Act
      service.updateAccountAdmin(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update my account', (): void => {
      // Arrange
      const testData = { isMyAccount: true };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.isMyAccount.subscribe(data => {
        result = data;
      });

      // Act
      service.updateMyAccount(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update parent company id', (): void => {
      // Arrange
      const testData = 123;
      let result: any;

      // Subscribe to the BehaviorSubject
      service.ParentCompanyId.subscribe(data => {
        result = data;
      });

      // Act
      service.updateParentCompanyId(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should upload bulk ndr file', (): void => {
      // Arrange
      const testData = { file: 'test.pdf' };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.fileUpload.subscribe(data => {
        result = data;
      });

      // Act
      service.uploadBulkNdrFile(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should check if new project created with project plan', (): void => {
      // Arrange
      const testData = { created: true };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.newProjectCreatedWithProjectPlan.subscribe(data => {
        result = data;
      });

      // Act
      service.checkIfNewProjectCreatedWithProjectPlan(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should upload bulk company file', (): void => {
      // Arrange
      const testData = { file: 'test.pdf' };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.companyFileUpload.subscribe(data => {
        result = data;
      });

      // Act
      service.uploadBulkCompanyFile(testData);

      // Assert
      expect(result).toEqual(testData);
    });
  });

  describe('API methods', (): void => {
    it('should call getProject', (): void => {
      // Arrange
      const mockResponse = {
        data: [
          { id: 123, name: 'Test Project' }
        ]
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getProject().subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith('project/get_project');
      expect(result).toEqual(mockResponse);
    });

    it('should call getAccountProject with correct parameters', (): void => {
      // Arrange
      const companyId = 456;
      const mockResponse = {
        data: [
          { id: 123, name: 'Test Project', companyId: 456 }
        ]
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getAccountProject(companyId).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`project/get_account_projects/${companyId}`);
      expect(result).toEqual(mockResponse);
    });

    it('should call getTimeZoneList', (): void => {
      // Arrange
      const mockResponse = {
        data: [
          { id: 1, name: 'UTC' },
          { id: 2, name: 'EST' }
        ]
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getTimeZoneList().subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith('timezone_list/get_timezone_list');
      expect(result).toEqual(mockResponse);
    });

    it('should call getLastDevlieryRequestId with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: 123,
        ParentCompanyId: 456
      };
      const mockResponse = {
        data: { id: 789 }
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getLastDevlieryRequestId(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `delivery/get_last_delivery_request_id/${mockParams.ProjectId}/${mockParams.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getLastCraneRequestId with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: 123,
        ParentCompanyId: 456
      };
      const mockResponse = {
        data: { id: 789 }
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getLastCraneRequestId(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `crane_request/get_last_crane_request_id/${mockParams.ProjectId}/${mockParams.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call upgradeProjectPlans with correct parameters', (): void => {
      // Arrange
      const mockData = {
        projectId: 123,
        planId: 456
      };
      const mockResponse = {
        success: true,
        message: 'Plan upgraded successfully'
      };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.upgradeProjectPlans(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('project/upgrade_plan', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call getSingleProject with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: 123
      };
      const mockResponse = {
        data: {
          id: 123,
          name: 'Test Project',
          description: 'Test Description'
        }
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getSingleProject(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`project/get_single_project/${mockParams.ProjectId}`);
      expect(result).toEqual(mockResponse);
    });

    it('should call getProjectSubscription with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: 123
      };
      const mockResponse = {
        data: {
          id: 123,
          name: 'Test Project',
          subscription: {
            id: 789,
            status: 'active',
            planId: 456
          }
        }
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getProjectSubscription(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`project/get_project_profile/${mockParams.ProjectId}`);
      expect(result).toEqual(mockResponse);
    });

    it('should call createProject with timezone', (): void => {
      // Arrange
      const mockData = { name: 'Test Project' };
      const mockResponse = { success: true };
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.createProject(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('project/create_project', {
        ...mockData,
        timezone
      });
      expect(result).toEqual(mockResponse);
    });

    it('should call applyOverRide', (): void => {
      // Arrange
      const mockData = { overrideId: 123 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.applyOverRide(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('overRide/apply_override', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call deleteGate', (): void => {
      // Arrange
      const mockData = { gateId: 123 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.deleteGate(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('gates/delete_gates', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call getMappedRequests', (): void => {
      // Arrange
      const mockData = { gateId: 123 };
      const mockResponse = { data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getMappedRequests(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('gates/get_mapped_requests', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call deactivateGate', (): void => {
      // Arrange
      const mockData = { gateId: 123 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.deactivateGate(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('gates/deactivate_gate', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call deactiveEquipment', (): void => {
      // Arrange
      const mockData = { equipmentId: 123 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.deactiveEquipment(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('equipment/deactivate_equipment', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call getEquipmentMappedRequests', (): void => {
      // Arrange
      const mockData = { equipmentId: 123 };
      const mockResponse = { data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getEquipmentMappedRequests(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('equipment/get_mapped_requests', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call deactivateMember', (): void => {
      // Arrange
      const mockData = { memberId: 123 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.deactivateMember(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('member/deactivate_member', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call activateMember', (): void => {
      // Arrange
      const mockData = { memberId: 123 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.activateMember(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('member/activate_member', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call getMemberMappedRequests', (): void => {
      // Arrange
      const mockData = { memberId: 123 };
      const mockResponse = { data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getMemberMappedRequests(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('member/get_mapped_requests', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call listMember', (): void => {
      // Arrange
      const params = { ProjectId: 123, pageSize: 10, pageNo: 1, ParentCompanyId: 456 };
      const payload = { search: 'test' };
      const mockResponse = { data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.listMember(params, payload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `member/list_member/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.ParentCompanyId}`,
        payload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call listAllMember', (): void => {
      // Arrange
      const params = { ProjectId: 123, ParentCompanyId: 456 };
      const mockResponse = { data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.listAllMember(params).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `member/list_all_member/${params.ProjectId}/${params.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call searchAllMember', (): void => {
      // Arrange
      const params = { ProjectId: 123, search: 'test', ParentCompanyId: 456 };
      const mockResponse = { data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.searchAllMember(params).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `member/search_all_member/${params.ProjectId}/${params.search}/${params.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call listEquipmentLog', (): void => {
      // Arrange
      const params = { pageSize: 10, pageNo: 1 };
      const payload = { search: 'test' };
      const mockResponse = { data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.listEquipmentLog(params, payload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `equipmentLog/equipmentlog_list/${params.pageSize}/${params.pageNo}`,
        payload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call listWasteLog', (): void => {
      // Arrange
      const params = { pageSize: 10, pageNo: 1 };
      const payload = { search: 'test' };
      const mockResponse = { data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.listWasteLog(params, payload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `wasteLog/wastelog_list/${params.pageSize}/${params.pageNo}`,
        payload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call listHaulingLog', (): void => {
      // Arrange
      const params = { pageSize: 10, pageNo: 1 };
      const payload = { search: 'test' };
      const mockResponse = { data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.listHaulingLog(params, payload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `haulingLog/haulinglog_list/${params.pageSize}/${params.pageNo}`,
        payload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call listEquipment', (): void => {
      // Arrange
      const params = { pageSize: 10, pageNo: 1 };
      const payload = { search: 'test' };
      const mockResponse = { data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.listEquipment(params, payload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `equipment/list_equipment/${params.pageSize}/${params.pageNo}`,
        payload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call listCraneEquipment', (): void => {
      // Arrange
      const params = { pageSize: 10, pageNo: 1 };
      const payload = { search: 'test' };
      const mockResponse = { data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.listCraneEquipment(params, payload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `equipment/list_crane_equipment/${params.pageSize}/${params.pageNo}`,
        payload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getLastDevlieryRequestId', (): void => {
      // Arrange
      const params = { projectId: 123 };
      const mockResponse = { data: { id: 456 } };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getLastDevlieryRequestId(params).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `equipment/get_last_delivery_request_id/${params.projectId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getLastCraneRequestId', (): void => {
      // Arrange
      const params = { projectId: 123 };
      const mockResponse = { data: { id: 456 } };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getLastCraneRequestId(params).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `equipment/get_last_crane_request_id/${params.projectId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call upgradeProjectPlans', (): void => {
      // Arrange
      const mockData = { planId: 123 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.upgradeProjectPlans(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('project/upgrade_project_plans', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call getSingleProject', (): void => {
      // Arrange
      const params = { projectId: 123 };
      const mockResponse = { data: { id: 123, name: 'Test Project' } };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getSingleProject(params).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`project/get_single_project/${params.projectId}`);
      expect(result).toEqual(mockResponse);
    });

    it('should call getProjectSubscription', (): void => {
      // Arrange
      const params = { projectId: 123 };
      const mockResponse = { data: { status: 'active' } };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getProjectSubscription(params).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`project/get_project_subscription/${params.projectId}`);
      expect(result).toEqual(mockResponse);
    });

    it('should call getAccountCompany', (): void => {
      // Arrange
      const mockResponse = { data: [{ id: 123, name: 'Test Company' }] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getAccountCompany().subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith('company/get_account_company');
      expect(result).toEqual(mockResponse);
    });

    it('should call deleteCalendarEvent', (): void => {
      // Arrange
      const id = 123;
      const params = { projectId: 456 };
      const payload = { eventId: 123 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.deleteCalendarEvent(id, params, payload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `calendar/delete_calendar_event/${id}/${params.projectId}`,
        payload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call listCompany', (): void => {
      // Arrange
      const params = { pageSize: 10, pageNo: 1 };
      const payload = { search: 'test' };
      const mockResponse = { data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.listCompany(params, payload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `company/list_company/${params.pageSize}/${params.pageNo}`,
        payload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call addCompany', (): void => {
      // Arrange
      const mockData = { name: 'Test Company' };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.addCompany(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('company/add_company', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call checkExistCompany', (): void => {
      // Arrange
      const params = { name: 'Test Company' };
      const payload = { companyId: 123 };
      const mockResponse = { exists: false };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.checkExistCompany(params, payload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `company/check_exist_company/${params.name}`,
        payload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call companyLogoUpload', (): void => {
      // Arrange
      const params = { companyId: 123 };
      const payload = new FormData();
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.companyLogoUpload(params, payload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `company/company_logo_upload/${params.companyId}`,
        payload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call utilitiesUpload', (): void => {
      // Arrange
      const params = { companyId: 123 };
      const payload = new FormData();
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.utilitiesUpload(params, payload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `company/utilities_upload/${params.companyId}`,
        payload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call editCompany', (): void => {
      // Arrange
      const mockData = { id: 123, name: 'Updated Company' };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.editCompany(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('company/edit_company', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call listEquipmentType', (): void => {
      // Arrange
      const params = { projectId: 123 };
      const mockResponse = { data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.listEquipmentType(params).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`equipment/list_equipment_type/${params.projectId}`);
      expect(result).toEqual(mockResponse);
    });

    it('should call getOnboardingInviteLink', (): void => {
      // Arrange
      const mockData = { projectId: 123 };
      const mockResponse = { data: { link: 'https://example.com/invite' } };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getOnboardingInviteLink(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('member/get_onboarding_invite_link', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call listPresetEquipmentType', (): void => {
      // Arrange
      const mockResponse = { data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.listPresetEquipmentType().subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith('equipment/list_preset_equipment_type');
      expect(result).toEqual(mockResponse);
    });

    it('should call deleteCompany', (): void => {
      // Arrange
      const mockData = { companyId: 123 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.deleteCompany(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('company/delete_company', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call addEquipment', (): void => {
      // Arrange
      const mockData = { name: 'Test Equipment' };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.addEquipment(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('equipment/add_equipment', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call addEquipmentLog', (): void => {
      // Arrange
      const mockData = { equipmentId: 123 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.addEquipmentLog(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('equipmentLog/add_equipment_log', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call addWasteLog', (): void => {
      // Arrange
      const mockData = { wasteId: 123 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.addWasteLog(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('wasteLog/add_waste_log', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call addHaulingLog', (): void => {
      // Arrange
      const mockData = { haulingId: 123 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.addHaulingLog(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('haulingLog/add_hauling_log', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call addUtilitiesLog', (): void => {
      // Arrange
      const mockData = { utilityId: 123 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.addUtilitiesLog(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('utilitiesLog/add_utilities_log', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call getDashboardData', (): void => {
      // Arrange
      const projectId = 123;
      const mockResponse = { data: {} };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getDashboardData(projectId).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`dashboard/get_dashboard_data/${projectId}`);
      expect(result).toEqual(mockResponse);
    });

    it('should call editEquipment', (): void => {
      // Arrange
      const mockData = { id: 123, name: 'Updated Equipment' };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.editEquipment(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('equipment/edit_equipment', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call editProjectDetail', (): void => {
      // Arrange
      const mockData = { id: 123, name: 'Updated Project' };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.editProjectDetail(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('project/edit_project_detail', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call deleteEquipment', (): void => {
      // Arrange
      const mockData = { equipmentId: 123 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.deleteEquipment(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('equipment/delete_equipment', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call existProject', (): void => {
      // Arrange
      const mockData = { name: 'Test Project' };
      const mockResponse = { exists: false };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.existProject(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('project/exist_project', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call addGate', (): void => {
      // Arrange
      const mockData = { name: 'Test Gate' };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.addGate(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('gates/add_gate', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call updateGate', (): void => {
      // Arrange
      const mockData = { id: 123, name: 'Updated Gate' };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateGate(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('gates/update_gate', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call inviteMembers', (): void => {
      // Arrange
      const mockData = { emails: ['<EMAIL>'] };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.inviteMembers(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('member/invite_members', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call editMember', (): void => {
      // Arrange
      const mockData = { id: 123, name: 'Updated Member' };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.editMember(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('member/edit_member', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call updateMember', (): void => {
      // Arrange
      const mockData = { id: 123, name: 'Updated Member' };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateMember(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('member/update_member', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call resendInviteLink', (): void => {
      // Arrange
      const mockData = { memberId: 123 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.resendInviteLink(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('member/resend_invite_link', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call deleteMember', (): void => {
      // Arrange
      const mockData = { memberId: 123 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.deleteMember(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('member/delete_member', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call getRoles', (): void => {
      // Arrange
      const mockResponse = { data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getRoles().subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith('member/get_roles');
      expect(result).toEqual(mockResponse);
    });

    it('should call getCompanies', (): void => {
      // Arrange
      const params = { projectId: 123 };
      const mockResponse = { data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getCompanies(params).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`company/get_companies/${params.projectId}`);
      expect(result).toEqual(mockResponse);
    });

    it('should call getDefinableWork', (): void => {
      // Arrange
      const params = { projectId: 123 };
      const mockResponse = { data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getDefinableWork(params).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`definableWork/get_definable_work/${params.projectId}`);
      expect(result).toEqual(mockResponse);
    });

    it('should call getLocations', (): void => {
      // Arrange
      const params = { projectId: 123 };
      const mockResponse = { data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getLocations(params).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`location/get_locations/${params.projectId}`);
      expect(result).toEqual(mockResponse);
    });

    it('should call gateList', (): void => {
      // Arrange
      const params = { projectId: 123 };
      const payload = { search: 'test' };
      const mockResponse = { data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.gateList(params, payload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `gates/gate_list/${params.projectId}`,
        payload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call unReadCount', (): void => {
      // Arrange
      const params = { projectId: 123 };
      const mockResponse = { count: 5 };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.unReadCount(params).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`notification/unread_count/${params.projectId}`);
      expect(result).toEqual(mockResponse);
    });

    it('should call upgradePlan', (): void => {
      // Arrange
      const mockResponse = { data: { url: 'https://example.com/upgrade' } };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.upgradePlan().subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith('subscription/upgrade_plan');
      expect(result).toEqual(mockResponse);
    });

    it('should call checkout', (): void => {
      // Arrange
      const mockData = { planId: 123 };
      const mockResponse = { data: { sessionId: 'test_session' } };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.checkout(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('subscription/checkout', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call checkoutSession', (): void => {
      // Arrange
      const mockData = { sessionId: 'test_session' };
      const mockResponse = { data: { status: 'success' } };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.checkoutSession(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('subscription/checkout_session', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call versionUpdate', (): void => {
      // Arrange
      const mockData = { version: '1.0.0' };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.versionUpdate(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('version/version_update', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call exportreport', (): void => {
      // Arrange
      const params = { projectId: 123 };
      const payload = { type: 'pdf' };
      const mockResponse = { data: { url: 'https://example.com/report.pdf' } };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.exportreport(params, payload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `report/export_report/${params.projectId}`,
        payload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call updateProjectSharingSettings', (): void => {
      // Arrange
      const mockData = { projectId: 123, settings: {} };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateProjectSharingSettings(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('project/update_project_sharing_settings', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call uploadProjectLogisticPlanUrl', (): void => {
      // Arrange
      const params = { projectId: 123 };
      const payload = new FormData();
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.uploadProjectLogisticPlanUrl(params, payload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `project/upload_project_logistic_plan_url/${params.projectId}`,
        payload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call decodeProjectDetailUrl', (): void => {
      // Arrange
      const mockData = { url: 'https://example.com/project/123' };
      const mockResponse = { data: { projectId: 123 } };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.decodeProjectDetailUrl(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('project/decode_project_detail_url', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call updateLogisticPlanSettings', (): void => {
      // Arrange
      const mockData = { projectId: 123, settings: {} };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateLogisticPlanSettings(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('project/update_logistic_plan_settings', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call getRegisteredMember', (): void => {
      // Arrange
      const params = { projectId: 123 };
      const mockResponse = { data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getRegisteredMember(params).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`member/get_registered_member/${params.projectId}`);
      expect(result).toEqual(mockResponse);
    });

    it('should call listGuestMembers', (): void => {
      // Arrange
      const params = { projectId: 123 };
      const payload = { search: 'test' };
      const mockResponse = { data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.listGuestMembers(params, payload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `member/list_guest_members/${params.projectId}`,
        payload
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getProjects method', (): void => {
    it('should get project from API when no ProjectId in localStorage', (): void => {
      // Arrange
      localStorageMock.getItem.mockReturnValue(null);
      const mockResponse = {
        data: [{ id: 123, name: 'Test Project' }]
      };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      service.getProjects();

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith('project/get_project');
      expect(service.projectId.value).toBe(123);
    });

    it('should get project from localStorage when ProjectId exists', (): void => {
      // Arrange
      localStorageMock.getItem.mockReturnValue('123');

      // Act
      service.getProjects();

      // Assert
      expect(apiServiceMock.get).not.toHaveBeenCalled();
      expect(service.projectId.value).toBe('123');
    });
  });

  describe('checkCurrentDomain method', (): void => {
    it('should return false for invite domain', (): void => {
      // Arrange
      Object.defineProperty(window, 'location', {
        value: { href: 'https://invite.example.com' },
        writable: true
      });

      // Act
      const result = service.checkCurrentDomain();

      // Assert
      expect(result).toBe(false);
    });

    it('should return false for localhost', (): void => {
      // Arrange
      Object.defineProperty(window, 'location', {
        value: { href: 'http://localhost:4200' },
        writable: true
      });

      // Act
      const result = service.checkCurrentDomain();

      // Assert
      expect(result).toBe(false);
    });

    it('should return true for valid domain with hyphen', (): void => {
      // Arrange
      Object.defineProperty(window, 'location', {
        value: { href: 'https://test-company.example.com' },
        writable: true
      });

      // Act
      const result = service.checkCurrentDomain();

      // Assert
      expect(result).toBe(true);
    });

    it('should return false for invalid domain format', (): void => {
      // Arrange
      Object.defineProperty(window, 'location', {
        value: { href: 'https://example.com' },
        writable: true
      });

      // Act
      const result = service.checkCurrentDomain();

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('Error handling', (): void => {
    it('should propagate errors from the API service', (done): void => {
      // Arrange
      const mockError = 'API Error';

      apiServiceMock.get.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.getProject().subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(mockError);
          done();
        }
      });
    });
  });

  describe('Service dependencies', (): void => {
    it('should have ApiService injected', (): void => {
      // This test verifies that the ApiService is properly injected
      expect(TestBed.inject(ApiService)).toBeDefined();
    });

    it('should have Router injected', (): void => {
      // This test verifies that the Router is properly injected
      expect(TestBed.inject(Router)).toBeDefined();
    });
  });

  describe('Service initialization', (): void => {
    it('should initialize BehaviorSubjects with default values', (): void => {
      // Verify that BehaviorSubjects are initialized with appropriate default values
      let projectIdValue: any;
      let companyListValue: any;
      let isProjectAccountValue: any;
      let refreshProjectValue: any;
      let subStatusValue: any;
      let isProjectValue: any;
      let userDataValue: any;
      let siteplanUploadedValue: any;

      service.projectId.subscribe(value => {
        projectIdValue = value;
      });

      service.companyList.subscribe(value => {
        companyListValue = value;
      });

      service.isProjectAccount.subscribe(value => {
        isProjectAccountValue = value;
      });

      service.refreshProject.subscribe(value => {
        refreshProjectValue = value;
      });

      service.subStatus.subscribe(value => {
        subStatusValue = value;
      });

      service.isProject.subscribe(value => {
        isProjectValue = value;
      });

      service.userData.subscribe(value => {
        userDataValue = value;
      });

      service.siteplanUploaded.subscribe(value => {
        siteplanUploadedValue = value;
      });

      // Assert default values
      expect(projectIdValue).toBeDefined();
      expect(companyListValue).toBeDefined();
      expect(isProjectAccountValue).toBeDefined();
      expect(refreshProjectValue).toBeDefined();
      expect(subStatusValue).toBeDefined();
      expect(isProjectValue).toBeDefined();
      expect(userDataValue).toBeDefined();
      expect(siteplanUploadedValue).toBeDefined();
    });
  });

  // Additional comprehensive test cases for better coverage
  describe('BehaviorSubject edge cases', (): void => {
    it('should handle null values in BehaviorSubjects', (): void => {
      let result: any;

      service.projectId.subscribe(value => {
        result = value;
      });

      service.updatedProjectId(null);
      expect(result).toBeNull();
    });

    it('should handle undefined values in BehaviorSubjects', (): void => {
      let result: any;

      service.companyList.subscribe(value => {
        result = value;
      });

      service.updateCompanyList(undefined);
      expect(result).toBeUndefined();
    });

    it('should handle empty arrays in company list', (): void => {
      let result: any;

      service.companyList.subscribe(value => {
        result = value;
      });

      service.updateCompanyList([]);
      expect(result).toEqual([]);
    });

    it('should handle complex objects in user data', (): void => {
      const complexUserData = {
        id: 1,
        name: 'Test User',
        permissions: ['read', 'write'],
        profile: {
          avatar: 'avatar.jpg',
          settings: {
            theme: 'dark',
            notifications: true
          }
        }
      };
      let result: any;

      service.userData.subscribe(value => {
        result = value;
      });

      service.updatedUserData(complexUserData);
      expect(result).toEqual(complexUserData);
    });

    it('should handle updateClearProject with timeout behavior', (): void => {
      jest.useFakeTimers();
      let results: any[] = [];

      service.clearProject.subscribe(value => {
        results.push(value);
      });

      const testData = { cleared: true };
      service.updateClearProject(testData);

      // Should immediately emit the data
      expect(results[results.length - 1]).toEqual(testData);

      // Fast-forward time to trigger timeout
      jest.advanceTimersByTime(100);

      // Should emit null after timeout
      expect(results[results.length - 1]).toBeNull();

      jest.useRealTimers();
    });
  });

  describe('Additional API methods', (): void => {
    it('should call createProject with timezone', (): void => {
      const mockData = {
        name: 'Test Project',
        description: 'Test Description'
      };
      const mockResponse = { success: true, data: { id: 123 } };

      // Mock Intl.DateTimeFormat
      const mockTimeZone = 'America/New_York';
      jest.spyOn(Intl, 'DateTimeFormat').mockReturnValue({
        resolvedOptions: () => ({ timeZone: mockTimeZone })
      } as any);

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.createProject(mockData).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('project/create_project', {
        ...mockData,
        timezone: mockTimeZone
      });
      expect(result).toEqual(mockResponse);
    });

    it('should call applyOverRide with correct parameters', (): void => {
      const mockData = {
        projectId: 123,
        overrideType: 'permission'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.applyOverRide(mockData).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('overRide/apply_override', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call deleteGate with correct parameters', (): void => {
      const mockPayload = {
        gateId: 123,
        projectId: 456
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.deleteGate(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('gates/delete_gates', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call getMappedRequests with correct parameters', (): void => {
      const mockPayload = {
        gateId: 123,
        projectId: 456
      };
      const mockResponse = { data: [{ id: 1, name: 'Request 1' }] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.getMappedRequests(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('gates/get_mapped_requests', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call deactivateGate with correct parameters', (): void => {
      const mockPayload = {
        gateId: 123,
        active: false
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.deactivateGate(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('gates/deactivate_gate', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('API Error Handling', (): void => {
    it('should handle network errors in getProject', (done): void => {
      const networkError = new Error('Network Error');
      apiServiceMock.get.mockReturnValue(throwError(() => networkError));

      service.getProject().subscribe({
        next: () => done.fail('Expected error but got success'),
        error: (error) => {
          expect(error).toBe(networkError);
          done();
        }
      });
    });

    it('should handle 404 errors in getSingleProject', (done): void => {
      const notFoundError = { status: 404, message: 'Project not found' };
      apiServiceMock.get.mockReturnValue(throwError(() => notFoundError));

      service.getSingleProject({ ProjectId: 999 }).subscribe({
        next: () => done.fail('Expected error but got success'),
        error: (error) => {
          expect(error.status).toBe(404);
          done();
        }
      });
    });

    it('should handle validation errors in createProject', (done): void => {
      const validationError = { status: 400, message: 'Invalid project data' };
      apiServiceMock.post.mockReturnValue(throwError(() => validationError));

      service.createProject({ name: '', description: '' }).subscribe({
        next: () => done.fail('Expected error but got success'),
        error: (error) => {
          expect(error.status).toBe(400);
          done();
        }
      });
    });
  });

  describe('Edge Cases', (): void => {
    it('should handle empty response from getProject', (): void => {
      const emptyResponse = { data: [] };
      apiServiceMock.get.mockReturnValue(of(emptyResponse));

      let result: any;
      service.getProject().subscribe(response => {
        result = response;
      });

      expect(result).toEqual(emptyResponse);
      expect(result.data).toEqual([]);
    });

    it('should handle malformed response from getTimeZoneList', (): void => {
      const malformedResponse = { data: null };
      apiServiceMock.get.mockReturnValue(of(malformedResponse));

      let result: any;
      service.getTimeZoneList().subscribe(response => {
        result = response;
      });

      expect(result.data).toBeNull();
    });

    it('should handle concurrent API calls', (): void => {
      const mockResponse1 = { data: { id: 1 } };
      const mockResponse2 = { data: { id: 2 } };

      apiServiceMock.get
        .mockReturnValueOnce(of(mockResponse1))
        .mockReturnValueOnce(of(mockResponse2));

      let results: any[] = [];

      service.getSingleProject({ ProjectId: 1 }).subscribe(r => results.push(r));
      service.getSingleProject({ ProjectId: 2 }).subscribe(r => results.push(r));

      expect(results).toHaveLength(2);
      expect(results[0].data.id).toBe(1);
      expect(results[1].data.id).toBe(2);
    });
  });

  describe('BehaviorSubject Edge Cases', (): void => {
    it('should handle multiple subscribers to projectId', (): void => {
      const testId = '123';
      let results: any[] = [];

      // Subscribe after service creation to avoid initial empty values
      const sub1 = service.projectId.subscribe(value => {
        if (value !== '') results.push(value);
      });
      const sub2 = service.projectId.subscribe(value => {
        if (value !== '') results.push(value);
      });

      service.updatedProjectId(testId);

      expect(results).toHaveLength(2);
      expect(results[0]).toBe(testId);
      expect(results[1]).toBe(testId);

      sub1.unsubscribe();
      sub2.unsubscribe();
    });

    it('should handle unsubscribe from BehaviorSubjects', (): void => {
      const testData = { id: 1, name: 'Test' };
      let result: any;
      let subscription = service.projectId.subscribe(value => {
        result = value;
      });

      service.updatedProjectId(testData);
      expect(result).toEqual(testData);

      subscription.unsubscribe();
      service.updatedProjectId({ id: 2, name: 'New Test' });
      expect(result).toEqual(testData); // Should not update after unsubscribe
    });

    it('should handle rapid updates to BehaviorSubjects', (): void => {
      jest.useFakeTimers();
      let results: any[] = [];

      service.projectId.subscribe(value => {
        if (value !== '') results.push(value);
      });

      service.updatedProjectId('1');
      service.updatedProjectId('2');
      service.updatedProjectId('3');

      expect(results).toHaveLength(3);
      expect(results[2]).toBe('3');

      jest.useRealTimers();
    });
  });

  describe('API Method Edge Cases', (): void => {
    it('should handle empty payload in deleteGate', (): void => {
      const emptyPayload = {};
      const mockResponse = { success: false, message: 'Invalid payload' };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.deleteGate(emptyPayload as any).subscribe(response => {
        result = response;
      });

      expect(result.success).toBe(false);
    });

    it('should handle invalid projectId in getProjectSubscription', (): void => {
      const invalidParams = { ProjectId: -1 };
      const mockResponse = { data: null };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      let result: any;
      service.getProjectSubscription(invalidParams).subscribe(response => {
        result = response;
      });

      expect(result.data).toBeNull();
    });

    it('should handle special characters in project name', (): void => {
      const specialCharsData = {
        name: 'Test Project!@#$%^&*()',
        description: 'Test Description'
      };
      const mockResponse = { success: true, data: { id: 123 } };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.createProject(specialCharsData).subscribe(response => {
        result = response;
      });

      expect(result.success).toBe(true);
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        'project/create_project',
        expect.objectContaining({ name: specialCharsData.name })
      );
    });
  });

  describe('Rate Limiting and Timeout Scenarios', (): void => {
    it('should handle rate limiting in getProject', (done): void => {
      const rateLimitError = { status: 429, message: 'Too Many Requests' };
      apiServiceMock.get.mockReturnValue(throwError(() => rateLimitError));

      service.getProject().subscribe({
        next: () => done.fail('Expected error but got success'),
        error: (error) => {
          expect(error.status).toBe(429);
          done();
        }
      });
    });

    it('should handle request timeout in getSingleProject', (done): void => {
      const timeoutError = { status: 408, message: 'Request Timeout' };
      apiServiceMock.get.mockReturnValue(throwError(() => timeoutError));

      service.getSingleProject({ ProjectId: 123 }).subscribe({
        next: () => done.fail('Expected error but got success'),
        error: (error) => {
          expect(error.status).toBe(408);
          done();
        }
      });
    });

    it('should handle server overload in createProject', (done): void => {
      const serverError = { status: 503, message: 'Service Unavailable' };
      apiServiceMock.post.mockReturnValue(throwError(() => serverError));

      service.createProject({ name: 'Test', description: 'Test' }).subscribe({
        next: () => done.fail('Expected error but got success'),
        error: (error) => {
          expect(error.status).toBe(503);
          done();
        }
      });
    });
  });

  describe('State Management Scenarios', (): void => {
    it('should maintain state consistency after multiple updates', (): void => {
      const updates = [
        { id: 1, name: 'Project 1' },
        { id: 2, name: 'Project 2' },
        { id: 3, name: 'Project 3' }
      ];
      let results: any[] = [];

      service.projectId.subscribe(value => {
        if (value !== '') results.push(value);
      });

      updates.forEach(update => service.updatedProjectId(update));

      expect(results).toHaveLength(3);
      expect(results[2]).toEqual(updates[2]);
    });

    it('should handle state rollback on failed update', (): void => {
      const initialData = { id: 1, name: 'Initial Project' };
      const failedUpdate = { id: 2, name: 'Failed Project' };
      let currentState: any;

      service.projectId.subscribe(value => currentState = value);

      service.updatedProjectId(initialData);
      expect(currentState).toEqual(initialData);

      // Simulate failed update
      service.updatedProjectId(failedUpdate);
      service.updatedProjectId(initialData); // Rollback

      expect(currentState).toEqual(initialData);
    });

    it('should handle state synchronization between multiple subjects', (): void => {
      const projectData = { id: 1, name: 'Test Project' };
      let projectIdValue: any;
      let projectDetailsValue: any;

      service.projectId.subscribe(value => projectIdValue = value);
      service.projectDetails.subscribe(value => projectDetailsValue = value);

      service.updatedProjectId(projectData);
      service.setProject(projectData);

      expect(projectIdValue).toEqual(projectData);
      expect(projectDetailsValue).toEqual(projectData);
    });
  });

  describe('Input Validation Scenarios', (): void => {
    it('should handle extremely long project names', (): void => {
      const longName = 'a'.repeat(1000);
      const mockResponse = { success: true, data: { id: 123 } };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.createProject({ name: longName, description: 'Test' }).subscribe(response => {
        result = response;
      });

      expect(result.success).toBe(true);
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        'project/create_project',
        expect.objectContaining({ name: longName })
      );
    });

    it('should handle project names with emojis and special characters', (): void => {
      const specialName = '🌟 Project 🚀 Test!@#$%^&*()_+{}|:"<>?';
      const mockResponse = { success: true, data: { id: 123 } };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.createProject({ name: specialName, description: 'Test' }).subscribe(response => {
        result = response;
      });

      expect(result.success).toBe(true);
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        'project/create_project',
        expect.objectContaining({ name: specialName })
      );
    });

    it('should handle empty or whitespace-only project names', (): void => {
      const emptyName = '   ';
      const mockResponse = { success: false, message: 'Invalid project name' };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.createProject({ name: emptyName, description: 'Test' }).subscribe(response => {
        result = response;
      });

      expect(result.success).toBe(false);
    });
  });

  describe('Concurrent Operation Scenarios', (): void => {
    it('should handle multiple simultaneous project updates', (): void => {
      const updates = [
        { id: 1, name: 'Project 1' },
        { id: 2, name: 'Project 2' },
        { id: 3, name: 'Project 3' }
      ];
      const mockResponses = updates.map(update => ({ success: true, data: update }));

      mockResponses.forEach((response, index) => {
        apiServiceMock.get.mockReturnValueOnce(of(response));
      });

      const results: any[] = [];
      updates.forEach(update => {
        service.getSingleProject({ ProjectId: update.id }).subscribe(response => {
          results.push(response);
        });
      });

      expect(results).toHaveLength(updates.length);
      expect(apiServiceMock.get).toHaveBeenCalledTimes(updates.length);
    });

    it('should handle race conditions in project updates', (): void => {
      jest.useFakeTimers();
      const updates = [
        { id: 1, name: 'Project 1' },
        { id: 2, name: 'Project 2' }
      ];
      let results: any[] = [];

      service.projectId.subscribe(value => {
        if (value !== '') results.push(value);
      });

      // Simulate race condition - last update should win
      service.updatedProjectId(updates[0]);
      setTimeout(() => service.updatedProjectId(updates[1]), 0);

      jest.runAllTimers();

      expect(results[results.length - 1]).toEqual(updates[1]);
      jest.useRealTimers();
    });
  });

  describe('Error Recovery Scenarios', (): void => {
    it('should recover from temporary network errors', (done): void => {
      const networkError = new Error('Network Error');
      const successResponse = { data: { id: 123, name: 'Test Project' } };

      apiServiceMock.get.mockReturnValueOnce(throwError(() => networkError));

      service.getProject().subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(networkError);

          // Now test successful retry
          apiServiceMock.get.mockReturnValueOnce(of(successResponse));
          service.getProject().subscribe({
            next: (response) => {
              expect(response).toEqual(successResponse);
              done();
            },
            error: () => done.fail('Unexpected error on retry')
          });
        }
      });
    }, 10000);

    it('should handle partial success in batch operations', (): void => {
      const batchData = [
        { id: 1, name: 'Project 1' },
        { id: 2, name: 'Project 2' },
        { id: 3, name: 'Project 3' }
      ];
      const mockResponses = [
        { success: true, data: batchData[0] },
        { success: false, error: 'Failed' },
        { success: true, data: batchData[2] }
      ];

      mockResponses.forEach((response, index) => {
        apiServiceMock.post.mockReturnValueOnce(of(response));
      });

      const results: any[] = [];
      batchData.forEach((data, index) => {
        service.createProject(data).subscribe(response => {
          results.push(response);
        });
      });

      expect(results).toHaveLength(3);
      expect(results[1].success).toBe(false);
      expect(results[0].success && results[2].success).toBe(true);
    });
  });

  describe('Authentication and Authorization Scenarios', (): void => {
    it('should handle unauthorized access in getProject', (done): void => {
      const unauthorizedError = { status: 401, message: 'Unauthorized' };
      apiServiceMock.get.mockReturnValue(throwError(() => unauthorizedError));

      service.getProject().subscribe({
        next: () => done.fail('Expected error but got success'),
        error: (error) => {
          expect(error.status).toBe(401);
          done();
        }
      });
    }, 10000);

    it('should handle forbidden access in createProject', (done): void => {
      const forbiddenError = { status: 403, message: 'Forbidden' };
      apiServiceMock.post.mockReturnValue(throwError(() => forbiddenError));

      service.createProject({ name: 'Test', description: 'Test' }).subscribe({
        next: () => done.fail('Expected error but got success'),
        error: (error) => {
          expect(error.status).toBe(403);
          done();
        }
      });
    });

    it('should handle token expiration during operation', (done): void => {
      const tokenExpiredError = { status: 401, message: 'Token Expired' };
      apiServiceMock.get.mockReturnValue(throwError(() => tokenExpiredError));

      service.getProject().subscribe({
        next: () => done.fail('Expected error but got success'),
        error: (error) => {
          expect(error.status).toBe(401);
          expect(error.message).toBe('Token Expired');
          done();
        }
      });
    }, 10000);
  });

  describe('Data Transformation Scenarios', (): void => {
    it('should transform project data correctly', (): void => {
      const rawData = {
        id: 123,
        name: 'Test Project',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-02T00:00:00Z',
        status: 'active',
        metadata: {
          owner: 'John Doe',
          tags: ['test', 'demo']
        }
      };
      const mockResponse = { data: rawData };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      let result: any;
      service.getSingleProject({ ProjectId: 123 }).subscribe(response => {
        result = response;
      });

      expect(result.data).toEqual(rawData);
      expect(result.data.created_at).toBeDefined();
      expect(result.data.metadata).toBeDefined();
    });

    it('should handle malformed date formats', (): void => {
      const malformedData = {
        id: 123,
        name: 'Test Project',
        created_at: 'invalid-date',
        updated_at: '2024-01-02T00:00:00Z'
      };
      const mockResponse = { data: malformedData };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      let result: any;
      service.getSingleProject({ ProjectId: 123 }).subscribe(response => {
        result = response;
      });

      expect(result.data.created_at).toBe('invalid-date');
      expect(result.data.updated_at).toBeDefined();
    });

    it('should handle nested object transformations', (): void => {
      const nestedData = {
        id: 123,
        name: 'Test Project',
        settings: {
          notifications: {
            email: true,
            push: false,
            frequency: 'daily'
          },
          permissions: {
            read: ['user1', 'user2'],
            write: ['user1']
          }
        }
      };
      const mockResponse = { data: nestedData };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      let result: any;
      service.getSingleProject({ ProjectId: 123 }).subscribe(response => {
        result = response;
      });

      expect(result.data.settings.notifications).toBeDefined();
      expect(result.data.settings.permissions).toBeDefined();
      expect(Array.isArray(result.data.settings.permissions.read)).toBe(true);
    });
  });

  describe('Cache Handling Scenarios', (): void => {
    it('should use cached data when available', (): void => {
      const cachedProjectId = '123';
      localStorageMock.getItem.mockReturnValue(cachedProjectId);

      // Spy on updatedProjectId to verify it's called with cached data
      const updatedProjectIdSpy = jest.spyOn(service, 'updatedProjectId');

      service.getProjects();

      expect(apiServiceMock.get).not.toHaveBeenCalled();
      expect(localStorageMock.getItem).toHaveBeenCalledWith('ProjectId');
      expect(updatedProjectIdSpy).toHaveBeenCalledWith(cachedProjectId);
    });

    it('should call API when cache is empty', (): void => {
      localStorageMock.getItem.mockReturnValue(null);
      const mockResponse = { data: [{ id: 123, name: 'New Project' }] };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Spy on updatedProjectId to verify it's called with API data
      const updatedProjectIdSpy = jest.spyOn(service, 'updatedProjectId');

      service.getProjects();

      expect(apiServiceMock.get).toHaveBeenCalledWith('project/get_project');
      expect(localStorageMock.getItem).toHaveBeenCalledWith('ProjectId');
      expect(updatedProjectIdSpy).toHaveBeenCalledWith(123);
    });

    it('should handle localStorage getItem returning undefined', (): void => {
      localStorageMock.getItem.mockReturnValue(undefined);
      const mockResponse = { data: [{ id: 123, name: 'New Project' }] };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      service.getProjects();

      expect(apiServiceMock.get).toHaveBeenCalledWith('project/get_project');
    });
  });

  describe('Complex State Transition Scenarios', (): void => {
    it('should handle project status transitions', (): void => {
      const statuses = ['draft', 'pending', 'active', 'completed'];
      let currentStatus: any;

      service.subStatus.subscribe(status => {
        currentStatus = status;
      });

      statuses.forEach(status => {
        service.updatedSubStatus({ status });
        expect(currentStatus.status).toBe(status);
      });
    });

    it('should handle project lifecycle events', (): void => {
      const events = [
        { type: 'created', data: { id: 123, name: 'New Project' } },
        { type: 'updated', data: { id: 123, name: 'Updated Project' } },
        { type: 'deleted', data: { id: 123 } }
      ];
      let results: any[] = [];

      service.projectDetails.subscribe(event => {
        results.push(event);
      });

      events.forEach(event => {
        switch (event.type) {
          case 'created':
            service.setProject(event.data);
            break;
          case 'updated':
            service.updatedProjectId(event.data);
            break;
          case 'deleted':
            service.updatedProjectId(null);
            break;
        }
      });

      expect(results).toHaveLength(events.length);
      expect(results[results.length - 1]).toBeNull();
    });

    it('should handle concurrent state updates', (): void => {
      jest.useFakeTimers();
      const updates = [
        { id: 1, status: 'pending' },
        { id: 1, status: 'active' },
        { id: 1, status: 'completed' }
      ];
      let results: any[] = [];

      service.subStatus.subscribe(status => {
        if (status !== '') results.push(status);
      });

      updates.forEach(update => {
        service.updatedSubStatus(update);
        jest.advanceTimersByTime(100);
      });

      expect(results).toHaveLength(updates.length);
      expect(results[results.length - 1].status).toBe('completed');
      jest.useRealTimers();
    });
  });

  describe('Error Boundary Scenarios', (): void => {
    it('should handle circular reference in project data', (): void => {
      const circularData: any = { id: 123, name: 'Test Project' };
      circularData.self = circularData;
      const mockResponse = { data: circularData };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      let result: any;
      service.getSingleProject({ ProjectId: 123 }).subscribe(response => {
        result = response;
      });

      expect(result.data).toBeDefined();
      expect(result.data.id).toBe(123);
    });

    it('should handle undefined method calls', (): void => {
      const undefinedMethod = (service as any).nonExistentMethod;
      expect(() => undefinedMethod()).toThrow();
    });

    it('should handle invalid subscription parameters', (): void => {
      // RxJS BehaviorSubject actually handles null observers gracefully
      expect(() => {
        service.projectId.subscribe(null as any);
      }).not.toThrow();
    });
  });

  describe('Integration Scenarios', (): void => {
    it('should handle complete project lifecycle', (): void => {
      const projectData = {
        name: 'Test Project',
        description: 'Test Description'
      };
      const createdResponse = { success: true, data: { id: 123, ...projectData } };
      const getResponse = { data: { id: 123, ...projectData, status: 'active' } };
      const deletedResponse = { success: true };

      // Create project
      apiServiceMock.post.mockReturnValueOnce(of(createdResponse));
      let createdProject: any;
      service.createProject(projectData).subscribe(response => {
        createdProject = response;
      });
      expect(createdProject.success).toBe(true);

      // Get project
      apiServiceMock.get.mockReturnValueOnce(of(getResponse));
      let retrievedProject: any;
      service.getSingleProject({ ProjectId: 123 }).subscribe(response => {
        retrievedProject = response;
      });
      expect(retrievedProject.data.status).toBe('active');

      // Delete project
      apiServiceMock.post.mockReturnValueOnce(of(deletedResponse));
      let deleteResult: any;
      service.deleteGate({ gateId: 123, projectId: 123 }).subscribe(response => {
        deleteResult = response;
      });
      expect(deleteResult.success).toBe(true);
    });

    it('should handle project with multiple gates', (): void => {
      const gates = [
        { id: 1, name: 'Gate 1' },
        { id: 2, name: 'Gate 2' },
        { id: 3, name: 'Gate 3' }
      ];
      const mockResponses = gates.map(gate => ({ success: true, data: gate }));

      mockResponses.forEach((response, index) => {
        apiServiceMock.post.mockReturnValueOnce(of(response));
      });

      const results: any[] = [];
      gates.forEach(gate => {
        service.deleteGate({ gateId: gate.id, projectId: 123 }).subscribe(response => {
          results.push(response);
        });
      });

      expect(results).toHaveLength(gates.length);
      expect(results.every(r => r.success)).toBe(true);
    });
  });

  // Additional tests for missing BehaviorSubject methods
  describe('Missing BehaviorSubject Methods', (): void => {
    it('should update project name in side menu', (): void => {
      const testName = 'Test Project Name';
      let result: any;

      service.projectNameInSideMenu.subscribe(value => {
        result = value;
      });

      service.updatedProjectNameInSideMenu(testName);
      expect(result).toEqual(testName);
    });

    it('should update project parent', (): void => {
      const testData = { parentId: 123, name: 'Parent Project' };
      let result: any;

      service.projectParent.subscribe(value => {
        result = value;
      });

      service.updateProjectParent(testData);
      expect(result).toEqual(testData);
    });

    it('should update account project parent', (): void => {
      const testData = { accountParentId: 456, name: 'Account Parent' };
      let result: any;

      service.accountProjectParent.subscribe(value => {
        result = value;
      });

      service.updateAccountProjectParent(testData);
      expect(result).toEqual(testData);
    });

    it('should update account admin status', (): void => {
      const testData = { isAdmin: true };
      let result: any;

      service.isAccountAdmin.subscribe(value => {
        result = value;
      });

      service.updateAccountAdmin(testData);
      expect(result).toEqual(testData);
    });

    it('should update my account status', (): void => {
      const testData = { isMyAccount: true };
      let result: any;

      service.isMyAccount.subscribe(value => {
        result = value;
      });

      service.updateMyAccount(testData);
      expect(result).toEqual(testData);
    });

    it('should update parent company ID', (): void => {
      const testId = 789;
      let result: any;

      service.ParentCompanyId.subscribe(value => {
        result = value;
      });

      service.updateParentCompanyId(testId);
      expect(result).toEqual(testId);
    });

    it('should update file upload status', (): void => {
      const testData = { uploaded: true, fileName: 'test.pdf' };
      let result: any;

      service.fileUpload.subscribe(value => {
        result = value;
      });

      // Note: This method doesn't exist in the service, but the BehaviorSubject does
      service.fileUpload.next(testData);
      expect(result).toEqual(testData);
    });

    it('should update company file upload status', (): void => {
      const testData = { uploaded: true, companyId: 123 };
      let result: any;

      service.companyFileUpload.subscribe(value => {
        result = value;
      });

      service.companyFileUpload.next(testData);
      expect(result).toEqual(testData);
    });

    it('should update new project created with project plan', (): void => {
      const testData = { created: true, planId: 456 };
      let result: any;

      service.newProjectCreatedWithProjectPlan.subscribe(value => {
        result = value;
      });

      service.newProjectCreatedWithProjectPlan.next(testData);
      expect(result).toEqual(testData);
    });
  });

  // Additional tests for missing API methods
  describe('Missing API Methods', (): void => {
    it('should call deactiveEquipment with correct parameters', (): void => {
      const mockPayload = { equipmentId: 123, active: false };
      const mockResponse = { success: true };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.deactiveEquipment(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('equipment/deactivate_equipment', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call getEquipmentMappedRequests with correct parameters', (): void => {
      const mockPayload = { equipmentId: 123, projectId: 456 };
      const mockResponse = { data: [{ id: 1, name: 'Request 1' }] };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.getEquipmentMappedRequests(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('equipment/get_mapped_requests', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call deactivateMember with correct parameters', (): void => {
      const mockPayload = { memberId: 123, active: false };
      const mockResponse = { success: true };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.deactivateMember(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('member/deactivate_member', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call activateMember with correct parameters', (): void => {
      const mockPayload = { memberId: 123, active: true };
      const mockResponse = { success: true };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.activateMember(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('member/activate_member', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call getMemberMappedRequests with correct parameters', (): void => {
      const mockPayload = { memberId: 123, projectId: 456 };
      const mockResponse = { data: [{ id: 1, name: 'Member Request 1' }] };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.getMemberMappedRequests(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('member/get_mapped_requests', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call listMember with correct parameters', (): void => {
      const mockParams = { ProjectId: 123, pageSize: 10, pageNo: 1, ParentCompanyId: 456 };
      const mockPayload = { search: 'test' };
      const mockResponse = { data: [{ id: 1, name: 'Member 1' }] };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.listMember(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `member/list_member/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.ParentCompanyId}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call listAllMember with correct parameters', (): void => {
      const mockParams = { ProjectId: 123, ParentCompanyId: 456 };
      const mockResponse = { data: [{ id: 1, name: 'All Member 1' }] };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      let result: any;
      service.listAllMember(mockParams).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `member/list_all_member/${mockParams.ProjectId}/${mockParams.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call searchAllMember with correct parameters', (): void => {
      const mockParams = { ProjectId: 123, search: 'john', ParentCompanyId: 456 };
      const mockResponse = { data: [{ id: 1, name: 'John Doe' }] };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      let result: any;
      service.searchAllMember(mockParams).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `member/search_all_member/${mockParams.ProjectId}/${mockParams.search}/${mockParams.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call listEquipmentLog with correct parameters', (): void => {
      const mockParams = { pageSize: 10, pageNo: 1 };
      const mockPayload = { projectId: 123 };
      const mockResponse = { data: [{ id: 1, equipment: 'Excavator' }] };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      let result: any;
      service.listEquipmentLog(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `equipmentLog/equipmentlog_list/${mockParams.pageSize}/${mockParams.pageNo}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call listWasteLog with correct parameters', (): void => {
      const mockParams = { pageSize: 10, pageNo: 1 };
      const mockPayload = { projectId: 123 };
      const mockResponse = { data: [{ id: 1, waste: 'Concrete' }] };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      let result: any;
      service.listWasteLog(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `wasteLog/wastelog_list/${mockParams.pageSize}/${mockParams.pageNo}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getAccountCompany', (): void => {
      const mockResponse = { data: [{ id: 1, name: 'Company 1' }] };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      let result: any;
      service.getAccountCompany().subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.get).toHaveBeenCalledWith('project/get_accounts_company');
      expect(result).toEqual(mockResponse);
    });

    it('should call deleteCalendarEvent with correct parameters', (): void => {
      const id = 123;
      const mockParams = { projectId: 456 };
      const mockPayload = { reason: 'cancelled' };
      const mockResponse = { success: true };
      apiServiceMock.putMethod.mockReturnValue(of(mockResponse));

      let result: any;
      service.deleteCalendarEvent(id, mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.putMethod).toHaveBeenCalledWith(
        `calendar_settings/delete_event/${id}`,
        mockPayload,
        mockParams
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call listCompany with correct parameters', (): void => {
      const mockParams = { ProjectId: 123, pageSize: 10, pageNo: 1, ParentCompanyId: 456 };
      const mockPayload = { search: 'test company' };
      const mockResponse = { data: [{ id: 1, name: 'Test Company' }] };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.listCompany(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `company/get_companies/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.ParentCompanyId}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call addCompany with correct parameters', (): void => {
      const mockPayload = { name: 'New Company', address: '123 Main St' };
      const mockResponse = { success: true, data: { id: 123 } };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.addCompany(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('company/add_company', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call checkExistCompany with correct parameters', (): void => {
      const mockParams = { ProjectId: 123, ParentCompanyId: 456 };
      const mockPayload = { name: 'Existing Company' };
      const mockResponse = { exists: true };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.checkExistCompany(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `company/check_exist_company/${mockParams.ProjectId}/${mockParams.ParentCompanyId}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call companyLogoUpload with correct parameters', (): void => {
      const mockParams = { ProjectId: 123, ParentCompanyId: 456 };
      const mockPayload = new FormData();
      const mockResponse = { success: true, url: 'logo.jpg' };
      apiServiceMock.requestImage = jest.fn().mockReturnValue(of(mockResponse));

      let result: any;
      service.companyLogoUpload(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.requestImage).toHaveBeenCalledWith(
        `company/upload_logo/${mockParams.ProjectId}/${mockParams.ParentCompanyId}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call utilitiesUpload with correct parameters', (): void => {
      const mockParams = { zipcode: '12345', projectId: 123 };
      const mockPayload = new FormData();
      const mockResponse = { success: true, url: 'utilities.pdf' };
      apiServiceMock.requestImage = jest.fn().mockReturnValue(of(mockResponse));

      let result: any;
      service.utilitiesUpload(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.requestImage).toHaveBeenCalledWith(
        `carbon_emission/upload_utilities/${mockParams.zipcode}/${mockParams.projectId}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call editCompany with correct parameters', (): void => {
      const mockPayload = { id: 123, name: 'Updated Company', address: '456 Oak St' };
      const mockResponse = { success: true };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.editCompany(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('company/edit_company', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call listEquipmentType with correct parameters', (): void => {
      const mockParams = { ProjectId: 123 };
      const mockResponse = { data: [{ id: 1, type: 'Excavator' }] };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      let result: any;
      service.listEquipmentType(mockParams).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.get).toHaveBeenCalledWith(`equipment/equipment_type_list/${mockParams.ProjectId}`);
      expect(result).toEqual(mockResponse);
    });

    it('should call getOnboardingInviteLink with correct parameters', (): void => {
      const mockPayload = { projectId: 123, email: '<EMAIL>' };
      const mockResponse = { success: true, link: 'https://invite.link' };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.getOnboardingInviteLink(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('member/get_onboarding_invite_link', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call addUtilitiesLog with correct parameters', (): void => {
      const mockPayload = { projectId: 123, type: 'electricity', amount: 100 };
      const mockResponse = { success: true, id: 456 };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.addUtilitiesLog(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('utilities/add_Utilities', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call getDashboardData with correct parameters', (): void => {
      const projectId = 123;
      const mockResponse = { data: { emissions: 100, savings: 50 } };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      let result: any;
      service.getDashboardData(projectId).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.get).toHaveBeenCalledWith(`carbon_emission/get_dashboard_data/${projectId}`);
      expect(result).toEqual(mockResponse);
    });

    it('should call editEquipment with correct parameters', (): void => {
      const mockPayload = { id: 123, name: 'Updated Equipment', type: 'Crane' };
      const mockResponse = { success: true };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.editEquipment(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('equipment/update_equipment', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call editProjectDetail with correct parameters', (): void => {
      const mockPayload = { id: 123, name: 'Updated Project', description: 'New description' };
      const mockResponse = { success: true };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.editProjectDetail(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('project/edit_project', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call deleteEquipment with correct parameters', (): void => {
      const mockPayload = { equipmentIds: [123, 456], projectId: 789 };
      const mockResponse = { success: true };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.deleteEquipment(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('equipment/delete_equipments', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call existProject with correct parameters', (): void => {
      const mockPayload = { name: 'Test Project', companyId: 123 };
      const mockResponse = { exists: true };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.existProject(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('project/exist_project', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call getRoles', (): void => {
      const mockResponse = { data: [{ id: 1, name: 'Admin' }, { id: 2, name: 'User' }] };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      let result: any;
      service.getRoles().subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.get).toHaveBeenCalledWith('member/get_roles');
      expect(result).toEqual(mockResponse);
    });

    it('should call getCompanies with correct parameters', (): void => {
      const mockParams = { ProjectId: 123, ParentCompanyId: 456 };
      const mockResponse = { data: [{ id: 1, name: 'Company 1' }] };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      let result: any;
      service.getCompanies(mockParams).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `company/get_newcompanies/${mockParams.ProjectId}/${mockParams.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getDefinableWork with correct parameters', (): void => {
      const mockParams = { ProjectId: 123, ParentCompanyId: 456 };
      const mockResponse = { data: [{ id: 1, work: 'Construction' }] };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      let result: any;
      service.getDefinableWork(mockParams).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `company/get_definable_work/${mockParams.ProjectId}/${mockParams.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getLocations with correct parameters', (): void => {
      const mockParams = { search: 'test location' };
      const mockResponse = { data: [{ id: 1, name: 'Location 1' }] };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      let result: any;
      service.getLocations(mockParams).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.get).toHaveBeenCalledWith('location/get_locations', mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should call gateList with correct parameters', (): void => {
      const mockParams = { ProjectId: 123, pageSize: 10, pageNo: 1, ParentCompanyId: 456 };
      const mockPayload = { search: 'test gate' };
      const mockResponse = { data: [{ id: 1, name: 'Gate 1' }] };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.gateList(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `gates/gate_list/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.ParentCompanyId}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call unReadCount with correct parameters', (): void => {
      const mockParams = { ProjectId: 123, ParentCompanyId: 456 };
      const mockResponse = { count: 5 };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      let result: any;
      service.unReadCount(mockParams).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `notification/unread_count/?ProjectId=${mockParams.ProjectId}&ParentCompanyId=${mockParams.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call upgradePlan', (): void => {
      const mockResponse = { url: 'https://payment.url' };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      let result: any;
      service.upgradePlan().subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.get).toHaveBeenCalledWith('payment/get_session_url');
      expect(result).toEqual(mockResponse);
    });

    it('should call checkout with correct parameters', (): void => {
      const mockPayload = { planId: 123, amount: 100 };
      const mockResponse = { success: true, transactionId: 'txn_123' };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.checkout(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('payment/checkout', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call checkoutSession with correct parameters', (): void => {
      const mockPayload = { planId: 123, successUrl: 'https://success.url' };
      const mockResponse = { sessionId: 'session_123' };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.checkoutSession(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('payment/create_checkout_session', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call versionUpdate with correct parameters', (): void => {
      const mockPayload = { version: '1.2.3', userId: 123 };
      const mockResponse = { success: true };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.versionUpdate(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith('notification/version_update', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call exportreport with correct parameters', (): void => {
      const mockParams = { ProjectId: 123, pageSize: 10, pageNo: 1, void: 'all' };
      const mockPayload = { format: 'excel' };
      const mockResponse = { file: 'report.xlsx' };
      apiServiceMock.getExcel1 = jest.fn().mockReturnValue(of(mockResponse));

      let result: any;
      service.exportreport(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.getExcel1).toHaveBeenCalledWith(
        `delivery_reports/exportreports/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.void}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call updateProjectSharingSettings with correct parameters', (): void => {
      const mockPayload = { projectId: 123, shareWithPublic: true };
      const mockResponse = { success: true };
      apiServiceMock.put.mockReturnValue(of(mockResponse));

      let result: any;
      service.updateProjectSharingSettings(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.put).toHaveBeenCalledWith('project/project_sharing_settings', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call uploadProjectLogisticPlanUrl with correct parameters', (): void => {
      const mockParams = { ProjectId: 123 };
      const mockPayload = new FormData();
      const mockResponse = { success: true, url: 'plan.pdf' };
      apiServiceMock.requestImage = jest.fn().mockReturnValue(of(mockResponse));

      let result: any;
      service.uploadProjectLogisticPlanUrl(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.requestImage).toHaveBeenCalledWith(
        `project/upload_logistic_plan/${mockParams.ProjectId}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call decodeProjectDetailUrl with correct parameters', (): void => {
      const mockPayload = { encodedUrl: 'encoded_url_string' };
      const mockResponse = { decodedUrl: 'https://decoded.url' };
      apiServiceMock.put.mockReturnValue(of(mockResponse));

      let result: any;
      service.decodeProjectDetailUrl(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.put).toHaveBeenCalledWith('project/decode_project_detail_url', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call updateLogisticPlanSettings with correct parameters', (): void => {
      const mockPayload = { projectId: 123, showPlan: true };
      const mockResponse = { success: true };
      apiServiceMock.put.mockReturnValue(of(mockResponse));

      let result: any;
      service.updateLogisticPlanSettings(mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.put).toHaveBeenCalledWith('project/update_dashboard_logistic_plan', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call getRegisteredMember with correct parameters', (): void => {
      const mockParams = { ProjectId: 123, ParentCompanyId: 456 };
      const mockResponse = { data: [{ id: 1, name: 'Member 1', registered: true }] };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      let result: any;
      service.getRegisteredMember(mockParams).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `member/list_registered_members/${mockParams.ProjectId}/${mockParams.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call listGuestMembers with correct parameters', (): void => {
      const mockParams = { ProjectId: 123, pageSize: 10, pageNo: 1, ParentCompanyId: 456 };
      const mockPayload = { search: 'guest' };
      const mockResponse = { data: [{ id: 1, name: 'Guest Member 1' }] };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      let result: any;
      service.listGuestMembers(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `member/list_guest_member/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.ParentCompanyId}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });
  });
});
