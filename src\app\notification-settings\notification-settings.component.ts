import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import moment from 'moment';
import { NotificationPreferenceService } from '../services/notificationPreference/notification-preference.service';
import { ProjectService } from '../services/profile/project.service';
import { DeliveryService } from '../services/profile/delivery.service';

declare const google: any;
@Component({
  selector: 'app-notification-settings',
  templateUrl: './notification-settings.component.html',
  })
export class NotificationSettingsComponent implements OnInit {
  public MemberId: any;

  public ProjectId: any;

  public ParentCompanyId: any;

  public notificationList: any;

  public oneAtATime = true;

  public isFirstOpen = true;

  public loader = true;

  public notificationListLength = 0;

  public timezoneList: any = [];

  public submitted = false;

  public notificationForm: UntypedFormGroup;

  public isAppNotificationExpanded = false;

  public isEmailNotificationExpanded = false;

  public isAllInAppInstantSelected: any = 0;

  public isAllEmailInstantSelected: any = 0;

  public isAllInAppDailyDigestSelected: any = 0;

  public isAllEmailDailyDigestSelected: any = 0;

  public constructor(private readonly notificationService: NotificationPreferenceService,
    private readonly deliveryService: DeliveryService, public toastr: ToastrService,
    private readonly formBuilder: UntypedFormBuilder, private readonly titleService: Title,
    public projectService: ProjectService) {
    this.titleService.setTitle('Follo - Notification Settings');
    this.notificationPreferenceForm();
    this.projectService.projectParent.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ProjectId = res.ProjectId;
        this.ParentCompanyId = res.ParentCompanyId;
        const param = {
          ProjectId: this.ProjectId,
          ParentCompanyId: this.ParentCompanyId,
        };
        this.deliveryService.getMemberRole(param).subscribe((response: any): void => {
          if (response) {
            this.MemberId = response?.data?.id;
            this.getTimeZoneList();
            this.getNotificationList();
          }
        });
      }
    });
  }

  public ngOnInit(): void { /* */ }

  public getNotificationList(): void {
    this.loader = true;
    const payload = {
      MemberId: this.MemberId,
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.notificationService.getNotificationList(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          [this.notificationList] = [response.data[0]];
          if (this.notificationList?.digestTiming?.time) {
            this.notificationForm.get('time').setValue(this.notificationList?.digestTiming?.time);
            this.notificationForm.get('timeFormat').setValue(this.notificationList?.digestTiming?.timeFormat);
            this.notificationForm.get('TimeZoneId').setValue(this.notificationList?.digestTiming?.TimeZoneId);
          } else {
            this.notificationForm.get('time').setValue('05:00');
            this.notificationForm.get('timeFormat').setValue('AM');
            const getUserTimeZone = moment().utcOffset();
            const findTimeZoneId = this.timezoneList.filter(
              (object): any => Number(object.timeZoneOffsetInMinutes) === Number(getUserTimeZone),
            );
            this.notificationForm.get('TimeZoneId').setValue(findTimeZoneId[0].id);
          }
          this.notificationListLength = response?.data?.length;
          this.loader = false;
          this.checkWholeState(true);
        }
      },
      error: (getNotificationListError): void => {
        if (getNotificationListError.message?.statusCode === 400) {
          this.getNotificationListError(getNotificationListError);
        } else {
          this.toastr.error(getNotificationListError.message, 'OOPS!');
        }
      },
    });
  }

  public getNotificationListError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }

  public checkInstantValue(data, event, type): void {
    const object = data;
    if (event.target.checked) {
      object.instant = true;
      object.dailyDigest = false;
    }
    if (!event.target.checked) {
      object.instant = false;
    }
    this.checkWholeState(type);
  }


  public getEmailNotificationValue() {
    const getEmailNotification1 = this.notificationList.emailNotification.filter(
      (object3): any => object3.instant === true,
    );
    const getEmailNotification = this.notificationList.emailNotification.filter(
      (object4): any => object4.dailyDigest === true,
    );
    if (this.notificationList.emailNotification.length === getEmailNotification1.length) {
      this.isAllEmailInstantSelected = 2;
      this.isAllEmailDailyDigestSelected = 0;
    } else if (this.notificationList.emailNotification.length === getEmailNotification.length) {
      this.isAllEmailDailyDigestSelected = 2;
      this.isAllEmailInstantSelected = 0;
    } else {
      if (getEmailNotification1.length > 0) {
        this.isAllEmailInstantSelected = 1;
      }
      if (getEmailNotification.length > 0) {
        this.isAllEmailDailyDigestSelected = 1;
      }
      if (getEmailNotification1.length === 0) {
        this.isAllEmailInstantSelected = 0;
      }
      if (getEmailNotification.length === 0) {
        this.isAllEmailDailyDigestSelected = 0;
      }
    }
  }

  public getAppNotificationValue() {
    const getInappNotification1 = this.notificationList.inAppNotification.filter(
      (object1): any => object1.instant === true,
    );
    const getInappNotification = this.notificationList.inAppNotification.filter(
      (object2): any => object2.dailyDigest === true,
    );
    if (this.notificationList.inAppNotification.length === getInappNotification1.length) {
      this.isAllInAppInstantSelected = 2;
      this.isAllInAppDailyDigestSelected = 0;
    } else if (this.notificationList.inAppNotification.length === getInappNotification.length) {
      this.isAllInAppDailyDigestSelected = 2;
      this.isAllInAppInstantSelected = 0;
    } else {
      if (getInappNotification1.length > 0) {
        this.isAllInAppInstantSelected = 1;
      }
      if (getInappNotification.length > 0) {
        this.isAllInAppDailyDigestSelected = 1;
      }
      if (getInappNotification1.length === 0) {
        this.isAllInAppInstantSelected = 0;
      }
      if (getInappNotification.length === 0) {
        this.isAllInAppDailyDigestSelected = 0;
      }
    }
  }

  public checkWholeState(type): void {
    if (type === 'inApp' || type === true) {
      this.getAppNotificationValue();
    }

    if (type === 'email' || type === true) {
      this.getEmailNotificationValue();
    }
  }

  public checkDailyDigestValue(data, event, type): void {
    const object = data;
    if (event.target.checked) {
      object.dailyDigest = true;
      object.instant = false;
    }
    if (!event.target.checked) {
      object.dailyDigest = false;
    }
    this.checkWholeState(type);
  }

  public getTimeZoneList(): void {
    this.projectService.getTimeZoneList().subscribe({
      next: (response: any): void => {
        if (response) {
          this.timezoneList = response.data;
        }
      },
      error: (getTimeZoneListErr): void => {
        if (getTimeZoneListErr.message?.statusCode === 400) {
          this.showError(getTimeZoneListErr);
        } else if (!getTimeZoneListErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(getTimeZoneListErr.message, 'OOPS!');
        }
      },
    });
  }

  public onSubmit(): void {
    this.submitted = true;
    const params = {
      MemberId: this.MemberId,
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    const payload = {
      inAppNotification: this.notificationList.inAppNotification,
      emailNotification: this.notificationList.emailNotification,
      dailyDigestTiming: this.notificationForm.value,
    };
    this.notificationService.setNotificationPreference(params, payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success(response.message);
          this.submitted = false;
          this.getNotificationList();
        }
      },
      error: (setNotificationPreferenceErr): void => {
        this.submitted = false;
        if (setNotificationPreferenceErr.message?.statusCode === 400) {
          this.showError(setNotificationPreferenceErr);
        } else if (!setNotificationPreferenceErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(setNotificationPreferenceErr.message, 'OOPS!');
        }
      },
    });
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.toastr.error(errorMessage);
  }

  public inAppNotification(state): void {
    this.isAppNotificationExpanded = state;
  }

  public emailNotification(state): void {
    this.isEmailNotificationExpanded = state;
  }

  public notificationPreferenceForm(): void {
    this.notificationForm = this.formBuilder.group(
      {
        time: [
          '',
          Validators.compose([
            Validators.required,
          ]),
        ],
        timeFormat: [
          '',
          Validators.compose([
            Validators.required,
          ]),
        ],
        TimeZoneId: [
          '',
          Validators.compose([
            Validators.required,
          ]),
        ],
      },
    );
  }
}
