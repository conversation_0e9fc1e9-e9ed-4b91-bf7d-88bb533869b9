import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { VoidListComponent } from './void-list.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { ProjectService } from '../services/profile/project.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { of, throwError } from 'rxjs';
import { Pipe, PipeTransform, TemplateRef } from '@angular/core';
import { NO_ERRORS_SCHEMA } from '@angular/core';

@Pipe({
  name: 'paginate'
})
class MockPaginatePipe implements PipeTransform {
  transform(value: any[], config: any): any[] {
    return value;
  }
}

describe('VoidListComponent', () => {
  let component: VoidListComponent;
  let fixture: ComponentFixture<VoidListComponent>;
  let modalService: jest.Mocked<BsModalService>;
  let projectService: jest.Mocked<ProjectService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let router: jest.Mocked<Router>;
  let toastr: jest.Mocked<ToastrService>;
  let titleService: jest.Mocked<Title>;

  const mockProjectData = {
    ProjectId: '123',
    ParentCompanyId: '456'
  };

  const mockVoidListResponse = {
    data: {
      rows: [{
        isChecked: false,
        voidList: [
          { id: 1, MemberId: '123' }
        ],
        description: 'Test Description',
        requestType: 'deliveryRequest',
        DeliveryId: 'DEL123',
        deliveryStart: new Date().toISOString(),
        approvedUser: 'Test User',
        equipment: 'Test Equipment',
        equipmentDetails: [{ Equipment: { equipmentName: 'Test Equipment' } }],
        approverDetails: { User: { firstName: 'Test', lastName: 'User' } }
      }],
      count: 1
    },
    lastId: 1
  };

  beforeEach(async () => {
    modalService = {
      show: jest.fn(),
      hide: jest.fn()
    } as any;

    projectService = {
      projectParent: of(mockProjectData),
      listAllMember: jest.fn().mockReturnValue(of({ data: [] })),
      getCompanies: jest.fn().mockReturnValue(of({ data: [] })),
      getDefinableWork: jest.fn().mockReturnValue(of({ data: [] })),
      getOverAllGate: jest.fn().mockReturnValue(of({ data: [] })),
      getOverAllEquipment: jest.fn().mockReturnValue(of({ data: [] })),
      gateList: jest.fn().mockReturnValue(of({ data: [] })),
      listEquipment: jest.fn().mockReturnValue(of({ data: [] }))
    } as any;

    deliveryService = {
      getMemberRole: jest.fn().mockReturnValue(of({ data: {} })),
      getConcreteRequestDropdownData: jest.fn().mockReturnValue(of({ data: {} })),
      getVoidList: jest.fn().mockReturnValue(of(mockVoidListResponse)),
      updatedEditCraneRequestId: jest.fn(),
      updatedDeliveryId: jest.fn(),
      updatedEditConcreteRequestId: jest.fn(),
      updatedInspectionId: jest.fn(),
      removeVoid: jest.fn().mockReturnValue(of({ message: 'Success' })),
      checkOverlapping: jest.fn().mockReturnValue(of({ isAllowed: false }))
    } as any;

    router = {
      navigate: jest.fn()
    } as any;

    toastr = {
      success: jest.fn(),
      error: jest.fn()
    } as any;

    titleService = {
      setTitle: jest.fn()
    } as any;

    await TestBed.configureTestingModule({
      declarations: [
        VoidListComponent,
        MockPaginatePipe
      ],
      imports: [ReactiveFormsModule, FormsModule],
      providers: [
        { provide: BsModalService, useValue: modalService },
        { provide: ProjectService, useValue: projectService },
        { provide: DeliveryService, useValue: deliveryService },
        { provide: Router, useValue: router },
        { provide: ToastrService, useValue: toastr },
        { provide: Title, useValue: titleService },
        UntypedFormBuilder
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  });

// Additional tests for 100% coverage

describe('Additional coverage for restoreItemRequest, removeItem, handleDeliveryRequest', () => {
  let modalService: jest.Mocked<BsModalService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let toastr: jest.Mocked<ToastrService>;
  let component: VoidListComponent;

  beforeEach(() => {
    modalService = {
      show: jest.fn().mockReturnValue({ content: {} }),
      hide: jest.fn()
    } as any;
    deliveryService = {
      checkOverlapping: jest.fn().mockReturnValue({ subscribe: (cb: any) => cb({ isAllowed: true }) }),
      removeVoid: jest.fn().mockReturnValue({ subscribe: (obj: any) => obj.next && obj.next({ message: 'Success' }) }),
      updatedDeliveryId: jest.fn()
    } as any;
    toastr = {
      info: jest.fn(),
      error: jest.fn(),
      success: jest.fn()
    } as any;
    component = new VoidListComponent(
      modalService,
      {} as any,
      new UntypedFormBuilder(),
      {} as any,
      toastr,
      {} as any,
      deliveryService
    );
    component.voidPageDeliveryList = [{ id: 1, isChecked: true, voidList: [], requestType: 'deliveryRequest', recurrence: { recurrence: 'Weekly', id: 2, recurrenceEndDate: '2024-12-31' } }];
    component.selectedVoidIndex = 0;
    component.modalRef = { hide: jest.fn() } as any;
    component.modalRef3 = { content: {} } as any;
    component.restoreIndex = [];
    component.selectAll = false;
    component.restoreSubmitted = false;
  });

  it('should handle overlapping booking in restoreItemRequest (else branch)', () => {
    // Simulate checkOverlapping returns true (overlap exists)
    deliveryService.checkOverlapping = jest.fn().mockReturnValue({ subscribe: (cb: any) => cb({ isAllowed: true }) });
    component.voidPageDeliveryList[0].recurrence = { recurrence: 'Weekly', id: 2, recurrenceEndDate: '2024-12-31' };
    component.restoreItemRequest();
    expect(toastr.info).toHaveBeenCalled();
    expect(component.restoreSubmitted).toBe(false);
    expect(deliveryService.updatedDeliveryId).toHaveBeenCalledWith(1);
    expect(modalService.show).toHaveBeenCalled();
  });

  it('should call restoreRequest if no overlap in restoreItemRequest', () => {
    deliveryService.checkOverlapping = jest.fn().mockReturnValue({ subscribe: (cb: any) => cb({ isAllowed: false }) });
    const restoreRequestSpy = jest.spyOn(component, 'restoreRequest');
    component.restoreItemRequest();
    expect(restoreRequestSpy).toHaveBeenCalled();
  });

  it('should call restoreRequest if selectAll is true in removeItem', () => {
    component.selectAll = true;
    const restoreRequestSpy = jest.spyOn(component, 'restoreRequest');
    component.removeItem();
    expect(restoreRequestSpy).toHaveBeenCalled();
  });

  it('should handle per-item removal in removeItem', () => {
    component.selectAll = false;
    component.voidPageDeliveryList = [
      { isChecked: true, voidList: [{ DeliveryRequestId: 1, id: 10 }], requestType: 'deliveryRequest', id: 1 },
      { isChecked: false, voidList: [{ DeliveryRequestId: 2, id: 20 }], requestType: 'deliveryRequest', id: 2 }
    ];
    const getItemIdSpy = jest.spyOn(component, 'getItemId').mockReturnValue(10);
    const handleDeliveryRequestSpy = jest.spyOn(component, 'handleDeliveryRequest');
    component.removeItem();
    expect(getItemIdSpy).toHaveBeenCalled();
    expect(handleDeliveryRequestSpy).toHaveBeenCalledWith(component.voidPageDeliveryList[0]);
    expect(component.restoreIndex).toContain(10);
  });

  it('should handle overlapping booking in handleDeliveryRequest (else branch)', () => {
    deliveryService.checkOverlapping = jest.fn().mockReturnValue({ subscribe: (cb: any) => cb({ isAllowed: true }) });
    component.modalRef = { hide: jest.fn() } as any;
    component.modalRef3 = { content: {} } as any;
    const element = { id: 1, recurrence: { recurrence: 'Weekly', id: 2, recurrenceEndDate: '2024-12-31' } };
    component.handleDeliveryRequest(element);
    expect(toastr.info).toHaveBeenCalled();
    expect(component.restoreSubmitted).toBe(false);
    expect(deliveryService.updatedDeliveryId).toHaveBeenCalledWith(1);
    expect(modalService.show).toHaveBeenCalled();
    expect(component.modalRef3.content.closeBtnName).toBe('Close');
    expect(component.modalRef3.content.seriesOption).toBe(true);
    expect(component.modalRef3.content.recurrenceId).toBe(2);
    expect(component.modalRef3.content.recurrenceEndDate).toBe('2024-12-31');
  });
});

  beforeEach(fakeAsync(() => {
    fixture = TestBed.createComponent(VoidListComponent);
    component = fixture.componentInstance;
    
    // Initialize filterForm with all required controls
    component.filterForm = new UntypedFormBuilder().group({
      descriptionFilter: [''],
      companyFilter: [''],
      memberFilter: [''],
      gateFilter: [''],
      equipmentFilter: [''],
      statusFilter: [''],
      dateFilter: [''],
      locationFilter: [''],
      mixDesignFilter: ['']
    });

    // Initialize loginMemberData first
    component.loginMemberData = { id: '123' };

    // Initialize voidPageDeliveryList with mock data that matches loginMemberData
    component.voidPageDeliveryList = [{
      isChecked: false,
      voidList: [
        { id: 1, MemberId: '123' }  // This matches the loginMemberData.id
      ],
      description: 'Test Description',
      requestType: 'deliveryRequest',
      DeliveryId: 'DEL123',
      deliveryStart: new Date().toISOString(),
      approvedUser: 'Test User',
      equipment: 'Test Equipment',
      equipmentDetails: [{ Equipment: { equipmentName: 'Test Equipment' } }],
      approverDetails: { User: { firstName: 'Test', lastName: 'User' } }
    }];

    // Initialize other required properties
    component.companyList = [];
    component.defineList = [];
    component.gateList = [];
    component.equipmentList = [];
    component.memberList = [];
    component.locationDropdown = [];
    component.mixDesignDropdown = [];
    component.wholeStatus = ['Approved', 'Completed', 'Declined', 'Delivered', 'Pending', 'Tentative'];
    component.currentRestoreId = 0;
    component.totalCount = 1;
    component.search = '';
    component.showSearchbar = false;
    component.filterCount = 0;
    component.sortColumn = '';
    component.sort = 'ASC';
    component.pageNo = 1;
    component.pageSize = 25;
    component.loader = false;
    component.selectAll = false;
    component.remove = false;
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.restoreIndex = [1]; // Initialize with the expected id
    component.selectedVoidIndex = 0;

    // Mock the modalRef
    component.modalRef = {
      hide: jest.fn()
    } as any;

    // Mock template references
    component.deliveryDetails = {} as TemplateRef<any>;
    component.craneRequestDetail = {} as TemplateRef<any>;
    component.concreteRequestDetail = {} as TemplateRef<any>;
    component.inspectionRequestDetail = {} as TemplateRef<any>;

    // Initialize the component
    component.ngOnInit();
    fixture.detectChanges();
    tick(); // Wait for async operations to complete
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.currentPageNo).toBe(1);
    expect(component.pageSize).toBe(25);
    expect(component.pageNo).toBe(1);
    expect(component.loader).toBe(false);
    expect(component.selectAll).toBe(false);
  });

  it('should set title on initialization', () => {
    expect(titleService.setTitle).toHaveBeenCalledWith('Follo - Void List');
  });

  it('should get login member data', () => {
    const mockResponse = { data: { role: 'admin' } };
    deliveryService.getMemberRole.mockReturnValue(of(mockResponse));

    component.getLoginMember();

    expect(deliveryService.getMemberRole).toHaveBeenCalledWith({
      ProjectId: component.ProjectId,
      ParentCompanyId: component.ParentCompanyId
    });
    expect(component.loginMemberData).toEqual(mockResponse.data);
  });

  it('should handle filter reset', () => {
    component.filterCount = 5;
    component.filterForm = new UntypedFormBuilder().group({
      descriptionFilter: ['test'],
      companyFilter: ['test']
    });

    component.resetFilter();

    expect(component.filterCount).toBe(0);
    expect(component.pageNo).toBe(1);
    expect(deliveryService.getVoidList).toHaveBeenCalled();
  });

  it('should handle keyboard events for toggle', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const data = 0;
    const preventDefaultSpy = jest.spyOn(event, 'preventDefault');

    component.handleToggleKeydown(event, data);

    expect(preventDefaultSpy).toHaveBeenCalled();
    expect(component.voidPageDeliveryList[0].isChecked).toBe(true);
  });

  it('should handle keyboard events for delete', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const data = -1;
    const template = {} as any;
    const preventDefaultSpy = jest.spyOn(event, 'preventDefault');

    // Reset restoreIndex before the test
    component.restoreIndex = [];
    
    component.handleDeleteKeydown(event, data, template);

    expect(preventDefaultSpy).toHaveBeenCalled();
    expect(modalService.show).toHaveBeenCalled();
    expect(component.remove).toBe(true);
  });

  it('should handle sort by field', () => {
    const fieldName = 'id';
    const sortType = 'ASC';
    
    component.sortByField(fieldName, sortType);

    expect(component.sortColumn).toBe(fieldName);
    expect(component.sort).toBe(sortType);
    expect(deliveryService.getVoidList).toHaveBeenCalled();
  });

  it('should handle error in getLoginMember', () => {
    const error = { message: { details: [{ message: 'Error occurred' }] } };
    deliveryService.getMemberRole.mockReturnValue(throwError(() => error));

    component.getLoginMember();

    expect(component.loginMemberData).toEqual({});
  });

  it('should handle filter submission', () => {
    component.filterForm = new UntypedFormBuilder().group({
      descriptionFilter: ['test'],
      companyFilter: ['test'],
      memberFilter: [''],
      gateFilter: [''],
      equipmentFilter: [''],
      statusFilter: [''],
      dateFilter: [''],
      locationFilter: [''],
      mixDesignFilter: ['']
    });

    component.filterSubmit();

    expect(component.filterCount).toBe(2);
    expect(deliveryService.getVoidList).toHaveBeenCalled();
  });

  it('should handle page size change', () => {
    const newPageSize = 50;
    component.changePageSize(newPageSize);

    expect(component.pageSize).toBe(newPageSize);
    expect(component.pageNo).toBe(1);
    expect(deliveryService.getVoidList).toHaveBeenCalled();
  });

  it('should handle page number change', () => {
    const newPageNo = 2;
    component.changePageNo(newPageNo);

    expect(component.pageNo).toBe(newPageNo);
    expect(deliveryService.getVoidList).toHaveBeenCalled();
  });

  // Additional tests for better coverage
  it('should close modal', () => {
    component.close();
    expect(component.modalRef.hide).toHaveBeenCalled();
  });

  it('should open modal with template', () => {
    const template = {} as any;
    component.openModal(template);
    expect(modalService.show).toHaveBeenCalledWith(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg modal-dialog-centered new-delivery-popup custom-modal'
    });
  });

  it('should open modal1 with specific config', () => {
    const template = {} as any;
    component.openModal1(template);
    expect(modalService.show).toHaveBeenCalledWith(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-sm filter-popup custom-modal'
    });
  });

  it('should get company data', () => {
    component.voidPageGetCompany();
    expect(projectService.getCompanies).toHaveBeenCalledWith({
      ProjectId: component.ProjectId,
      ParentCompanyId: component.ParentCompanyId
    });
  });

  it('should handle error in company data fetching', () => {
    projectService.getCompanies.mockReturnValue(throwError(() => new Error('API Error')));
    component.voidPageGetCompany();
    expect(projectService.getCompanies).toHaveBeenCalled();
  });

  it('should get definable work data', () => {
    component.voidPageGetDefinable();
    expect(projectService.getDefinableWork).toHaveBeenCalledWith({
      ProjectId: component.ProjectId,
      ParentCompanyId: component.ParentCompanyId
    });
  });

  it('should handle error in definable work data fetching', () => {
    projectService.getDefinableWork.mockReturnValue(throwError(() => new Error('API Error')));
    component.voidPageGetDefinable();
    expect(projectService.getDefinableWork).toHaveBeenCalled();
  });

  it('should get gate data', () => {
    component.voidPageGetOverAllGate();
    expect(projectService.gateList).toHaveBeenCalledWith({
      ProjectId: component.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: component.ParentCompanyId
    }, { isFilter: true, showActivatedAlone: true });
  });

  it('should get equipment data', () => {
    component.voidPageGetOverAllEquipment();
    expect(projectService.listEquipment).toHaveBeenCalledWith({
      ProjectId: component.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: component.ParentCompanyId
    }, { isFilter: true, showActivatedAlone: true });
  });

  it('should get filter dropdown data', () => {
    component.getFilterDropdown();
    expect(deliveryService.getConcreteRequestDropdownData).toHaveBeenCalledWith({
      ProjectId: component.ProjectId,
      ParentCompanyId: component.ParentCompanyId
    });
  });

  it('should handle error in filter dropdown data fetching', () => {
    deliveryService.getConcreteRequestDropdownData.mockReturnValue(throwError(() => new Error('API Error')));
    component.getFilterDropdown();
    expect(deliveryService.getConcreteRequestDropdownData).toHaveBeenCalled();
  });

  it('should get members data', () => {
    component.voidPageGetMembers();
    expect(projectService.listAllMember).toHaveBeenCalledWith({
      ProjectId: component.ProjectId,
      pageSize: component.pageSize,
      pageNo: component.pageNo,
      ParentCompanyId: component.ParentCompanyId
    });
  });

  it('should check if any row is selected when selectAll is true', () => {
    component.selectAll = true;
    const result = component.checkSelectedRow();
    expect(result).toBe(false);
  });

  it('should check if any row is selected when no items are checked', () => {
    component.selectAll = false;
    component.voidPageDeliveryList = [
      { isChecked: false },
      { isChecked: false }
    ];
    const result = component.checkSelectedRow();
    expect(result).toBe(true);
  });

  it('should check if any row is selected when some items are checked', () => {
    component.selectAll = false;
    component.voidPageDeliveryList = [
      { isChecked: true },
      { isChecked: false }
    ];
    const result = component.checkSelectedRow();
    expect(result).toBe(false);
  });

  it('should set selected item and toggle checkbox', () => {
    const index = 0;
    component.voidPageDeliveryList[0].isChecked = false;

    component.setSelectedItem(index);

    expect(component.selectedVoidIndex).toBe(index);
    expect(component.voidPageDeliveryList[0].isChecked).toBe(true);
  });

  it('should select all void list data', () => {
    component.selectAll = false;
    component.voidPageDeliveryList = [
      { isChecked: false },
      { isChecked: false }
    ];

    component.selectAllVoidListData();

    expect(component.selectAll).toBe(true);
    expect(component.voidPageDeliveryList[0].isChecked).toBe(true);
    expect(component.voidPageDeliveryList[1].isChecked).toBe(true);
  });

  it('should deselect all void list data', () => {
    component.selectAll = true;
    component.voidPageDeliveryList = [
      { isChecked: true },
      { isChecked: true }
    ];

    component.selectAllVoidListData();

    expect(component.selectAll).toBe(false);
    expect(component.voidPageDeliveryList[0].isChecked).toBe(false);
    expect(component.voidPageDeliveryList[1].isChecked).toBe(false);
  });

  // Keyboard event tests
  it('should handle sort keyboard events', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const fieldName = 'id';
    const sortType = 'ASC';
    const preventDefaultSpy = jest.spyOn(event, 'preventDefault');

    component.handleSortKeydown(event, fieldName, sortType);

    expect(preventDefaultSpy).toHaveBeenCalled();
    expect(component.sortColumn).toBe(fieldName);
    expect(component.sort).toBe(sortType);
  });

  it('should handle modal keyboard events with data', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const template = {} as any;
    const preventDefaultSpy = jest.spyOn(event, 'preventDefault');

    component.handleModalKeydown(event, template);

    expect(preventDefaultSpy).toHaveBeenCalled();
    expect(modalService.show).toHaveBeenCalled();
  });

  it('should handle modal keyboard events without data', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const preventDefaultSpy = jest.spyOn(event, 'preventDefault');
    component.showSearchbar = true;
    component.search = 'test';

    component.handleModalKeydown(event);

    expect(preventDefaultSpy).toHaveBeenCalled();
    expect(component.showSearchbar).toBe(false);
    expect(component.search).toBe('');
  });

  it('should handle general keyboard events', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const deliveryDetails = {} as any;
    const craneRequestDetail = {} as any;
    const concreteRequestDetail = {} as any;
    const inspectionRequestDetail = {} as any;
    const item = { requestType: 'deliveryRequest', id: 1, status: 'Pending' };
    const preventDefaultSpy = jest.spyOn(event, 'preventDefault');

    component.handleKeyDown(event, deliveryDetails, craneRequestDetail, concreteRequestDetail, inspectionRequestDetail, item);

    expect(preventDefaultSpy).toHaveBeenCalled();
    expect(component.currentDeliveryItem).toEqual(item);
    expect(component.showStatus).toBe(true);
  });

  // Search functionality tests
  it('should handle search with data', () => {
    const searchData = 'test search';
    component.getSearchNDR(searchData);

    expect(component.showSearchbar).toBe(true);
    expect(component.search).toBe(searchData);
    expect(component.pageNo).toBe(1);
    expect(deliveryService.getVoidList).toHaveBeenCalled();
  });

  it('should handle search with empty data', () => {
    const searchData = '';
    component.getSearchNDR(searchData);

    expect(component.showSearchbar).toBe(false);
    expect(component.search).toBe(searchData);
    expect(component.pageNo).toBe(1);
    expect(deliveryService.getVoidList).toHaveBeenCalled();
  });

  it('should clear search', () => {
    component.showSearchbar = true;
    component.search = 'test';
    component.pageNo = 2;

    component.clear();

    expect(component.showSearchbar).toBe(false);
    expect(component.search).toBe('');
    expect(component.pageNo).toBe(1);
    expect(deliveryService.getVoidList).toHaveBeenCalled();
  });

  // Delete modal tests
  it('should open delete modal for specific index', () => {
    const index = 0;
    const template = {} as any;
    component.voidPageDeliveryList = [{
      voidList: [{ id: 1, MemberId: '123' }]
    }];
    component.loginMemberData = { id: '123' };

    component.openDeleteModal(index, template);

    expect(component.restoreIndex[0]).toBe(1);
    expect(component.currentRestoreId).toBe(index);
    expect(component.remove).toBe(false);
    expect(modalService.show).toHaveBeenCalled();
  });

  it('should open delete modal for select all', () => {
    const index = -1;
    const template = {} as any;

    component.openDeleteModal(index, template);

    expect(component.remove).toBe(true);
    expect(modalService.show).toHaveBeenCalled();
  });

  // Modal opening tests for different request types
  it('should open crane request modal', () => {
    const template = {} as any;
    const template1 = {} as any;
    const template2 = {} as any;
    const template3 = {} as any;
    const item = {
      requestType: 'craneRequest',
      CraneRequestId: 123,
      id: null,
      ConcreteRequestId: null,
      InspectionId: null,
      status: 'Approved'
    };

    component.openIdModal(template, template1, template2, template3, item);

    expect(deliveryService.updatedEditCraneRequestId).toHaveBeenCalledWith(123);
    expect(modalService.show).toHaveBeenCalledWith(template1, expect.any(Object));
    expect(component.currentDeliveryItem).toEqual(item);
    expect(component.showStatus).toBe(false);
  });

  it('should open delivery request modal', () => {
    const template = {} as any;
    const template1 = {} as any;
    const template2 = {} as any;
    const template3 = {} as any;
    const item = {
      requestType: 'deliveryRequest',
      CraneRequestId: null,
      id: 123,
      ConcreteRequestId: null,
      InspectionId: null,
      status: 'Pending'
    };

    component.openIdModal(template, template1, template2, template3, item);

    expect(deliveryService.updatedDeliveryId).toHaveBeenCalledWith(123);
    expect(modalService.show).toHaveBeenCalledWith(template, expect.any(Object));
    expect(component.currentDeliveryItem).toEqual(item);
    expect(component.showStatus).toBe(true);
  });

  it('should open delivery request with crane modal', () => {
    const template = {} as any;
    const template1 = {} as any;
    const template2 = {} as any;
    const template3 = {} as any;
    const item = {
      requestType: 'deliveryRequestWithCrane',
      CraneRequestId: null,
      id: 123,
      ConcreteRequestId: null,
      InspectionId: null,
      status: 'Approved'
    };

    component.openIdModal(template, template1, template2, template3, item);

    expect(deliveryService.updatedDeliveryId).toHaveBeenCalledWith(123);
    expect(modalService.show).toHaveBeenCalledWith(template, expect.any(Object));
    expect(component.currentDeliveryItem).toEqual(item);
    expect(component.showStatus).toBe(false);
  });

  it('should open concrete request modal', () => {
    const template = {} as any;
    const template1 = {} as any;
    const template2 = {} as any;
    const template3 = {} as any;
    const item = {
      requestType: 'concreteRequest',
      CraneRequestId: null,
      id: null,
      ConcreteRequestId: 456,
      InspectionId: null,
      status: 'Pending'
    };

    component.openIdModal(template, template1, template2, template3, item);

    expect(deliveryService.updatedEditConcreteRequestId).toHaveBeenCalledWith(456);
    expect(modalService.show).toHaveBeenCalledWith(template2, expect.any(Object));
    expect(component.currentDeliveryItem).toEqual(item);
    expect(component.showStatus).toBe(true);
  });

  it('should open inspection request modal', () => {
    const template = {} as any;
    const template1 = {} as any;
    const template2 = {} as any;
    const template3 = {} as any;
    const item = {
      requestType: 'inspectionRequest',
      CraneRequestId: null,
      id: null,
      ConcreteRequestId: null,
      InspectionId: 789,
      status: 'Completed'
    };

    component.openIdModal(template, template1, template2, template3, item);

    expect(deliveryService.updatedInspectionId).toHaveBeenCalledWith(789);
    expect(modalService.show).toHaveBeenCalledWith(template3, expect.any(Object));
    expect(component.currentDeliveryItem).toEqual(item);
    expect(component.showStatus).toBe(false);
  });

  // Restore functionality tests
  it('should restore request successfully', () => {
    component.restoreIndex = [1, 2];
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.selectAll = false;

    component.restoreRequest();

    expect(deliveryService.removeVoid).toHaveBeenCalledWith({
      id: [1, 2],
      ProjectId: '123',
      ParentCompanyId: '456',
      isSelectAll: false
    });
    expect(toastr.success).toHaveBeenCalledWith('Success', 'Success');
    expect(component.pageNo).toBe(1);
    expect(component.restoreSubmitted).toBe(false);
    expect(component.modalRef.hide).toHaveBeenCalled();
  });

  it('should handle restore request error with status code 400', () => {
    const error = {
      message: {
        statusCode: 400,
        details: [{ message: 'Validation error' }]
      }
    };
    deliveryService.removeVoid.mockReturnValue(throwError(() => error));
    component.restoreIndex = [1];

    component.restoreRequest();

    expect(component.restoreSubmitted).toBe(false);
  });

  it('should handle restore request error without message', () => {
    const error = {};
    deliveryService.removeVoid.mockReturnValue(throwError(() => error));
    component.restoreIndex = [1];

    component.restoreRequest();

    expect(toastr.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    expect(component.restoreSubmitted).toBe(false);
  });

  it('should handle restore request error with message', () => {
    const error = { message: 'Custom error message' };
    deliveryService.removeVoid.mockReturnValue(throwError(() => error));
    component.restoreIndex = [1];

    component.restoreRequest();

    expect(toastr.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
    expect(component.restoreSubmitted).toBe(false);
  });

  // Error handling tests
  it('should show error message', () => {
    const error = {
      message: {
        details: [{ field: 'Error message' }]
      }
    };

    component.showError(error);

    expect(component.submitted).toBe(false);
    expect(component.editSubmitted).toBe(false);
    expect(toastr.error).toHaveBeenCalledWith('Error message');
  });

  // getItemId tests
  it('should get item id for concrete request', () => {
    const voidList = [
      { ConcreteRequestId: 123, id: 1 },
      { ConcreteRequestId: 456, id: 2 }
    ];
    const requestType = 'concreteRequest';
    const id = 123;

    const result = component.getItemId(voidList, requestType, id);

    expect(result).toBe(1);
  });

  it('should get item id for crane request', () => {
    const voidList = [
      { CraneRequestId: 123, id: 1 },
      { CraneRequestId: 456, id: 2 }
    ];
    const requestType = 'craneRequest';
    const id = 456;

    const result = component.getItemId(voidList, requestType, id);

    expect(result).toBe(2);
  });

  it('should return null for unknown request type', () => {
    const voidList = [{ id: 1 }];
    const requestType = 'unknownRequest';
    const id = 123;

    const result = component.getItemId(voidList, requestType, id);

    expect(result).toBeNull();
  });

  it('should return null when item not found', () => {
    const voidList = [{ ConcreteRequestId: 123, id: 1 }];
    const requestType = 'concreteRequest';
    const id = 999;

    const result = component.getItemId(voidList, requestType, id);

    expect(result).toBeNull();
  });
});
