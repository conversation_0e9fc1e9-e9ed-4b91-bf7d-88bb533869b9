<div class="timeslot-modal-body taginput--heightfix">
  <div class="timeslot-container">
    <div class="timeslot-header">
      <form [formGroup]="timeslotForm" novalidate>
        <div class="row time-selection-container">
          <div class="col-6 time-selection form-group d-flex align-items-center">
            <button class="today-btn" (click)="setToday()">Today</button>
          </div>
          <div class="col-6 date-picker-container">
            <img src="../../../assets/images/left.png" alt="Previous Day" class="nav-arrow" (click)="changeDay(-1)" (keydown)="handleDownKeydown($event, -1 ,'day')">

            <div class="date-picker" (click)="openDatePicker()"  (keydown)="handleDownKeydown($event, '' ,'date')">
              <span class="selected-date">
                {{ timeslotForm.get('selectedDate')?.value | date: 'EEE, MMM dd, yyyy' }}
              </span>
              <img src="../../../assets/images/calendar.svg" alt="Calendar Icon" class="calendar-icon">
            </div>

            <img src="../../../assets/images/right.png" alt="Next Day" class="nav-arrow" (click)="changeDay(1)"  (keydown)="handleDownKeydown($event, 1 ,'day')">

            <input type="date" formControlName="selectedDate" #datePicker class="hidden-input" (change)="onDateChange($event)">
          </div>
        </div>
        <div class="row">
          <p><strong>Working Hours</strong>:
            The calendar displays time based on working hours
            ({{ formatWorkingTime(startHour, startMinute) }} to {{ formatWorkingTime(endHour, endMinute) }})
          </p>
        </div>
        <div class="row timezone-box">
          Time Zone: {{ timeZone }}
        </div>
      </form>
    </div>
    <full-calendar #fullCalendar [options]="calendarOptions"></full-calendar>
  </div>
</div>
