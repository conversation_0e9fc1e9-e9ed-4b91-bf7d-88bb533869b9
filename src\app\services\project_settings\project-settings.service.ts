import { Observable } from 'rxjs';
import { Injectable } from '@angular/core';
import { ApiService } from '../api_base/api.service';

@Injectable({
  providedIn: 'root',
  })
export class ProjectSettingsService {
  public constructor(private readonly api: ApiService) {}

  public getProjectSettings(params): Observable<any> {
    return this.api.get('project_settings/get_projectDetails', params);
  }

  public updateProjectSettings(payload): Observable<any> {
    return this.api.put('project_settings', payload);
  }

  public getGuestProjectSettings(params): Observable<any> {
    return this.api.get('project_settings/get_guest_projectDetails', params);
  }
}
