import { ToastrModule, ToastrService } from 'ngx-toastr';
import { ModalModule, BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ForgotPasswordComponent } from './forgot-password.component';
import { AuthService } from '../../services/auth/auth.service';
import { Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { of, throwError } from 'rxjs';
import { RouterModule } from '@angular/router';

describe('ForgotPasswordComponent', () => {
  let component: ForgotPasswordComponent;
  let fixture: ComponentFixture<ForgotPasswordComponent>;
  let authService: AuthService;
  let router: Router;
  let toastr: ToastrService;
  let modalService: BsModalService;
  let titleService: Title;

  const mockAuthService = {
    checkAuthentication: jest.fn(),
    forgotPassword: jest.fn()
  };

  const mockToastrService = {
    error: jest.fn(),
    success: jest.fn()
  };

  const mockBsModalRef = {
    hide: jest.fn()
  };

  const mockBsModalService = {
    show: jest.fn().mockReturnValue(mockBsModalRef)
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ForgotPasswordComponent],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        RouterTestingModule.withRoutes([
          { path: 'login', component: ForgotPasswordComponent }
        ]),
        HttpClientTestingModule,
        ToastrModule.forRoot(),
        ModalModule.forRoot(),
      ],
      providers: [
        { provide: AuthService, useValue: mockAuthService },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: BsModalService, useValue: mockBsModalService },
        { provide: BsModalRef, useValue: mockBsModalRef },
        Title
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ForgotPasswordComponent);
    component = fixture.componentInstance;
    authService = TestBed.inject(AuthService);
    router = TestBed.inject(Router);
    toastr = TestBed.inject(ToastrService);
    modalService = TestBed.inject(BsModalService);
    titleService = TestBed.inject(Title);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct title', () => {
    expect(titleService.getTitle()).toBe('Follo - Forgot Password');
  });

  it('should initialize form with empty email', () => {
    expect(component.forgotpasswordForm.get('email').value).toBe('');
  });

  it('form should not be submitted initially', () => {
    expect(component.submitted).toBeFalsy();
    expect(component.formSubmitted).toBeFalsy();
  });

  it('form should be invalid when email is empty', () => {
    component.forgotpasswordForm.controls.email.setValue('');
    component.onSubmit();
    expect(component.forgotpasswordForm.invalid).toBeTruthy();
    expect(component.formSubmitted).toBeFalsy();
  });

  it('form should be invalid when email format is incorrect', () => {
    component.forgotpasswordForm.controls.email.setValue('invalid-email');
    component.onSubmit();
    expect(component.forgotpasswordForm.invalid).toBeTruthy();
    expect(component.formSubmitted).toBeFalsy();
  });

  it('form should be valid when email format is correct', () => {
    component.forgotpasswordForm.controls.email.setValue('<EMAIL>');
    expect(component.forgotpasswordForm.valid).toBeTruthy();
  });

  it('should handle successful password reset request', fakeAsync(() => {
    const mockResponse = { success: true };
    mockAuthService.forgotPassword.mockReturnValue(of(mockResponse));
    const routerSpy = jest.spyOn(router, 'navigate');

    component.forgotpasswordForm.controls.email.setValue('<EMAIL>');
    component.onSubmit();
    tick();

    expect(mockAuthService.forgotPassword).toHaveBeenCalledWith({
      email: '<EMAIL>',
      requestType: 0
    });
    expect(component.submitted).toBeFalsy();
    expect(component.formSubmitted).toBeFalsy();
    expect(routerSpy).toHaveBeenCalledWith(['/login']);
  }));

  it('should handle 400 error response', fakeAsync(() => {
    const mockError = {
      message: {
        statusCode: 400,
        details: [['Invalid email address']]
      }
    };
    mockAuthService.forgotPassword.mockReturnValue(throwError(() => mockError));

    component.forgotpasswordForm.controls.email.setValue('<EMAIL>');
    component.onSubmit();
    tick();

    expect(mockToastrService.error).toHaveBeenCalledWith(['Invalid email address']);
    expect(component.submitted).toBeFalsy();
    expect(component.formSubmitted).toBeFalsy();
  }));

  it('should handle generic error response', fakeAsync(() => {
    const mockError = {
      message: 'Server error'
    };
    mockAuthService.forgotPassword.mockReturnValue(throwError(() => mockError));

    component.forgotpasswordForm.controls.email.setValue('<EMAIL>');
    component.onSubmit();
    tick();

    expect(mockToastrService.error).toHaveBeenCalledWith('Server error', 'OOPS!');
    expect(component.submitted).toBeFalsy();
    expect(component.formSubmitted).toBeFalsy();
  }));

  it('should handle error without message', fakeAsync(() => {
    const mockError = {};
    mockAuthService.forgotPassword.mockReturnValue(throwError(() => mockError));

    component.forgotpasswordForm.controls.email.setValue('<EMAIL>');
    component.onSubmit();
    tick();

    expect(mockToastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    expect(component.submitted).toBeFalsy();
    expect(component.formSubmitted).toBeFalsy();
  }));

  it('should open modal with correct configuration', () => {
    const template = {} as any;
    component.openModal(template);
    
    expect(mockBsModalService.show).toHaveBeenCalledWith(template, {
      keyboard: false,
      class: 'modal-md reset-popup custom-modal'
    });
  });
});
