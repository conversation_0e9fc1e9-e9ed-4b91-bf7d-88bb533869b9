import { ToastrService } from 'ngx-toastr';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';

import {
  Component, OnInit, TemplateRef, ViewChild,
} from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Title } from '@angular/platform-browser';

import { AuthService } from '../../services/auth/auth.service';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  })
export class ForgotPasswordComponent implements OnInit {
  public forgotpasswordForm: UntypedFormGroup;

  public submitted = false;

  public formSubmitted = false;

  public modalRef: BsModalRef;

  @ViewChild('resetPassword') public resetPopup: TemplateRef<any>;

  public constructor(
    private readonly formBuilder: UntypedFormBuilder,
    private readonly router: Router,
    private readonly api: AuthService,
    private readonly toastr: ToastrService,
    private readonly modalService: BsModalService,
    private readonly titleService: Title,
  ) {
    this.titleService.setTitle('Follo - Forgot Password');
    this.api.checkAuthentication();
  }

  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = { keyboard: false, class: 'modal-md reset-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }

  public ngOnInit(): void {
    const pattern = "^[a-zA-Z0-9._%+'-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$";
    this.forgotpasswordForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.pattern(pattern)]],
    });
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    if (this.forgotpasswordForm.invalid) {
      this.formSubmitted = false;
      return;
    }
    const email = this.forgotpasswordForm.controls.email.value;
    if (email) {
      const payload = {
        email: email.trim(),
        requestType: 0, // web - 0,app -1
      };
      this.api.forgotPassword(payload).subscribe({
        next: (response: any): void => {
          if (response) {
            this.openModal(this.resetPopup);
            this.submitted = false;
            this.formSubmitted = false;
            this.router.navigate(['/login']);
          }
        },
        error: (forgotPasswordError): void => {
          if (forgotPasswordError.message?.statusCode === 400) {
            this.showError(forgotPasswordError);
            this.formSubmitted = false;
          } else if (!forgotPasswordError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(forgotPasswordError.message, 'OOPS!');
          }
          this.submitted = false;
          this.formSubmitted = false;
        },
      });
    } else {
      this.toastr.error('Email is required.', 'OOPS!');
    }
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }
}
