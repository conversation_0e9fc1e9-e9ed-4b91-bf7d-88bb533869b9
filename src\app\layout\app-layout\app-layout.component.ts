import { Component, OnInit } from '@angular/core';
import { Socket } from 'ngx-socket-io';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { ProjectService } from '../../services/profile/project.service';
import { ProfileService } from '../../services/profile/profile.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { BookingTemplatesService } from '../../services/booking-templates/booking-templates.service';
import { AuthService } from '../../services/auth/auth.service';
import { environment } from '../../../environments/environment';

declare let Intercom: any;

@Component({
  selector: 'app-app-layout',
  templateUrl: './app-layout.component.html',
  })
export class AppLayoutComponent implements OnInit {
  public toggleClass = false;

  public exist = false;

  public ProjectId: number;

  public userData: any = {};

  public notifyData: any = {};

  public notifyCraneRequestData: any = {};

  public notifyConcreteRequestData: any = {};

  public showNotification = false;

  public showCraneRequestNotification = false;

  public showConcreteRequestNotification = false;

  public interval;

  public projectList: any = [];

  public myAccount = false;

  public ParentCompanyId: any;

  public currentUser: any = {};

  public constructor(
    public socket: Socket,
    public profileService: ProfileService,
    public authApi: AuthService,
    public projectService: ProjectService,
    public deliveryService: DeliveryService,
    private readonly toastr: ToastrService,
    public router: Router,
    public templateService: BookingTemplatesService,
  ) {
    this.projectService.userData.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.userData = res;
        Intercom('boot', {
          api_base: environment.intercomApiBaseUrl,
          app_id: environment.intercomAppId,
          name: `${this.userData.User.firstName} ${this.userData.User.lastName}`,
          email: this.userData.User.email,
          created_at: new Date().getTime(),
        });
      }
    });
    this.projectService.ParentCompanyId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ParentCompanyId = res;
        this.getProjects();
      }
    });
    this.projectService.projectId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ProjectId = res;
      }
    });
    this.projectService.projectParent.subscribe((response8): void => {
      this.ProjectId = response8.ProjectId;
      if (response8 !== undefined && response8 !== null && response8 !== '') {
        this.myAccount = false;
        this.getMemberInfo({ ProjectId: this.ProjectId });
      }
    });
    this.projectService.accountProjectParent.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ProjectId = res.ProjectId;
        this.getMemberInfo({ ProjectId: this.ProjectId });
      }
    });
    this.projectService.isAccountAdmin.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.myAccount = true;
      }
    });
  }

  public getProjects(): void {
    this.projectService.getAccountProject(this.ParentCompanyId).subscribe((response): void => {
      this.projectList = response.data;
    });
  }

  public getMemberInfo(data: { ProjectId: any }): void {
    const param = {
      ProjectId: data.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.profileService.getOverView(param).subscribe((response: any): void => {
      if (response) {
        this.userData = response.data;
        this.projectService.updatedUserData(this.userData);
      }
    });
  }

  public closePopup(): void {
    this.showNotification = false;
  }

  public closePopup1(): void {
    this.showCraneRequestNotification = false;
  }

  public closePopup2(): void {
    this.showConcreteRequestNotification = false;
  }

  public sideMenuChange(val: boolean): void {
    this.toggleClass = val;
  }

  // eslint-disable-next-line max-lines-per-function
  public ngOnInit(): void {
    this.socket.on('bulkNdrNotification', (response: any): void => {
      if (response) {
        this.authApi.getUser().subscribe((DeliveryRequestFileUploadedResponse: any): any => {
          if (+response.loginUserId === +DeliveryRequestFileUploadedResponse.id) {
            this.toastr.success('Delivery Booking File uploaded.!', 'Success');
            this.projectService.uploadBulkNdrFile({ status: 'uploadDone' });
          }
        });
      }
    });
    this.socket.on('bulkCompanyNotification', (response: any): void => {
      if (response) {
        this.authApi.getUser().subscribe((CompanyFileUploadedResponse: any): any => {
          if (+response.loginUserId === +CompanyFileUploadedResponse.id) {
            this.toastr.success('Company File uploaded.!', 'Success');
            this.projectService.uploadBulkCompanyFile({ status: 'companyUploadDone' });
          }
        });
      }
    });
    this.socket.on('getCommentHistory', (response: any): void => {
      this.exist = false;
      this.existData(response, { approve: false });
    });
    this.socket.on('getNDRCreateHistory', (response: any): void => {
      this.exist = false;
      this.existData(response, { approve: false });
    });
    this.socket.on('getNDREditHistory', (response: any): void => {
      this.exist = false;
      this.existData(response, { approve: true });
    });
    this.socket.on('getNDRApproveHistory', (response: any): void => {
      this.exist = false;
      this.existData(response, { approve: true });
    });
    this.socket.on('getAttachmentDeleteHistory', (response: any): void => {
      this.exist = false;
      this.existData(response, { approve: false });
    });
    this.socket.on('getCraneCommentHistory', (response: any): void => {
      this.exist = false;
      this.craneRequestNotification(response, { approve: false });
    });
    this.socket.on('getCraneCreateHistory', (response: any): void => {
      this.exist = false;
      this.craneRequestNotification(response, { approve: false });
    });
    this.socket.on('getCraneEditHistory', (response: any): void => {
      this.exist = false;
      this.craneRequestNotification(response, { approve: true });
    });
    this.socket.on('getCraneApproveHistory', (response: any): void => {
      this.exist = false;
      this.craneRequestNotification(response, { approve: true });
    });
    this.socket.on('getCraneAttachmentDeleteHistory', (response: any): void => {
      this.exist = false;
      this.craneRequestNotification(response, { approve: false });
    });
    this.socket.on('deactivatedUser', (response: any): void => {
      this.authApi.getUser().subscribe((userDeactivatedResponse: any): any => {
        if (response.email === userDeactivatedResponse.email && response.status === false) {
          this.toastr.error('You were deactivated by a Super admin');
          setTimeout((): void => {
            this.authApi.logout();
          }, 3000);
        }
      });
    });
    this.socket.on('deactivatedMember', (response: any): void => {
      this.authApi.getUser().subscribe((userDeactivatedFromProjectResponse: any): any => {
        if (
          response.email === userDeactivatedFromProjectResponse.email
          && response.status === 'memberDeactivated'
        ) {
          this.toastr.info('You were deactivated. Please try to login again!');
          localStorage.clear();
          this.router.navigate(['/login']);
        }
        if (
          response.email === userDeactivatedFromProjectResponse.email
          && response.status === 'deactive'
        ) {
          this.toastr.info('You were deactivated from one of your projects.!');
          setTimeout((): void => {
            window.location.reload();
          }, 3000);
        }
        if (
          response.email === userDeactivatedFromProjectResponse.email
          && response.status !== 'deactive'
          && response.status !== 'memberDeactivated'
        ) {
          this.toastr.info('You were activated for one of your projects.!');
          setTimeout((): void => {
            window.location.reload();
          }, 3000);
        }
      });
    });
    this.socket.on('projectAssigned', (response: any): void => {
      this.authApi.getUser().subscribe((userAddedToProjectResponse: any): any => {
        if (response === userAddedToProjectResponse.email) {
          this.toastr.info('New Project was assigned by a Super admin.!');
          setTimeout((): void => {
            window.location.reload();
          }, 3000);
        }
      });
    });
    this.socket.on('projectEdited', (response: any): void => {
      this.authApi.getUser().subscribe((userProjectEditedResponse: any): any => {
        if (response === userProjectEditedResponse.email) {
          this.toastr.info('Your project was modified by a Super admin.!');
          setTimeout((): void => {
            window.location.reload();
          }, 3000);
        }
      });
    });
    this.socket.on('memberDetailUpdated', (response: any): void => {
      this.authApi.getUser().subscribe((userMemberDetailEditedResponse: any): any => {
        if (response === userMemberDetailEditedResponse.email) {
          this.toastr.info('Your Detail was modified by a Super admin.!');
          setTimeout((): void => {
            window.location.reload();
          }, 3000);
        }
      });
    });
    this.socket.on('getConcreteCreateHistory', (response: any): void => {
      this.exist = false;
      this.concreteRequestNotification(response, { approve: false });
    });
    this.socket.on('getConcreteEditHistory', (response: any): void => {
      this.exist = false;
      this.concreteRequestNotification(response, { approve: true });
    });
    this.socket.on('getConcreteAttachmentDeleteHistory', (response: any): void => {
      this.exist = false;
      this.concreteRequestNotification(response, { approve: false });
    });
    this.socket.on('getConcreteApproveHistory', (response: any): void => {
      this.exist = false;
      this.concreteRequestNotification(response, { approve: true });
    });
    this.socket.on('getConcreteAttachHistory', (response: any): void => {
      this.exist = false;
      this.concreteRequestNotification(response, { approve: true });
    });
    this.socket.on('getConcreteCommentHistory', (response: any): void => {
      this.exist = false;
      this.concreteRequestNotification(response, { approve: true });
    });
    this.socket.on('newSubscriptionCreated', (response: any): void => {
      localStorage.setItem('newSubscriptionCreated', JSON.stringify(response));
      this.projectService.checkIfNewProjectCreatedWithProjectPlan(response);
    });
    this.socket.on('getnotificationhistory', (response: any): void => {
      this.notificationcount(response);
    });
    this.socket.on('getDeliveryTemplateCreate', (response: any): void => {
      if (response) {
        this.updateTemplates();
      }
    });
    this.socket.on('getInspectionCommentHistory', (response: any): void => {
      this.exist = false;
      this.inspectionAttachmentOrCommentUpdated(response, { approve: false });
    });
    this.socket.on('getInspectionAttachmentHistory', (response: any): void => {
      this.exist = false;
      this.inspectionAttachmentOrCommentUpdated(response, { approve: false });
    });
    this.socket.on('getInspectionNDRApproveHistory', (response: any): void => {
      this.exist = false;
      this.existDataInspection(response, { approve: true });
    });
    this.socket.on('pdfToImageConvertion', (response: any): void => {
      this.updateDashboardSiteplan(response);
    });
  }

  public getMemberDetailData(data: { memberData: any[]; adminData: any[] }): boolean {
    this.exist = false;
    if (data?.memberData !== undefined) {
      data.memberData.forEach((element: { Member: { User: { id: any } } }): void => {
        if (element?.Member?.User?.id === this.userData?.User?.id) {
          this.exist = true;
        }
      });
    }
    if (data?.adminData !== undefined) {
      data.adminData.forEach((element: { User: { id: any } }): void => {
        if (element?.User?.id === this.userData?.User?.id) {
          this.exist = true;
        }
      });
    }
    return this.exist;
  }

  public existData(response, approveStatus): void {
    const { data } = response;
    let index = -1;
    let condition;
    if (this.myAccount) {
      const con1 = +data.ProjectId;
      const con2 = data.projectName.toLowerCase();
      const pr = this.projectList;
      index = pr.findIndex((i): boolean => i.id === con1 && i.projectName.toLowerCase() === con2);
      this.ProjectId = +data.ProjectId;
    }
    if (index === -1) {
      this.getMemberDetailData(data);
    }
    if (approveStatus) {
      condition = data?.MemberId !== this.userData?.id;
    }
    if ((this.exist === true || index !== -1) && condition) {
      if (this.ProjectId === +data.ProjectId) {
        if (approveStatus) {
          this.deliveryService.updatedCurrentStatus(data.DeliveryRequestId);
        }
        this.deliveryService.updatedHistory({ status: true }, 'historyUpdate');
        this.deliveryService.updateCraneRequestHistory({ status: true }, 'historyUpdate');
        this.deliveryService.updatedHistory1({ status: true }, 'historyUpdate');
        this.deliveryService.updateCraneRequestHistory1({ status: true }, 'historyUpdate');
      }
      if (data.MemberId !== this.userData.id && Object.keys(this.userData).length !== 0) {
        this.showNotification = true;
        this.interval = setInterval((): void => {
          this.showNotification = false;
          this.clearValue();
        }, 10000);
        this.notifyData = data;
      }
    }
  }

  public redirection(path: any): void {
    this.router.navigate([`/${path}`]);
  }

  public clearValue(): void {
    if (this.interval) {
      clearInterval(this.interval);
    }
  }

  public craneRequestNotification(
    response: { data: any },
    approveStatus: { approve: boolean },
  ): void {
    const { data } = response;
    let index = -1;
    let condition: boolean;
    if (this.myAccount) {
      const con1 = +data.ProjectId;
      const con2 = data.projectName.toLowerCase();
      const pr = this.projectList;
      index = pr.findIndex(
        (i: { id: number; projectName: string }): boolean => i.id === con1 && i.projectName.toLowerCase() === con2,
      );
      this.ProjectId = +data.ProjectId;
    }
    if (index === -1) {
      this.getMemberDetailData(data);
    }

    if (approveStatus) {
      condition = data?.MemberId !== this.userData?.id;
    }

    if ((this.exist === true || index !== -1) && condition) {
      if (this.ProjectId === +data.ProjectId) {
        if (approveStatus) {
          this.deliveryService.updatedCurrentCraneRequestStatus(+data.CraneRequestId);
        }
        this.deliveryService.updateCraneRequestHistory({ status: true }, 'historyUpdate');
      }
      if (data.MemberId !== this.userData.id && Object.keys(this.userData).length !== 0) {
        this.showCraneRequestNotification = true;
        this.interval = setInterval((): void => {
          this.showCraneRequestNotification = false;
          this.clearValue();
        }, 10000);
        this.notifyCraneRequestData = data;
      }
    }
  }

  public concreteRequestNotification(
    response: { data: any },
    approveStatus: { approve: boolean },
  ): void {
    const { data } = response;
    let index = -1;
    let condition: boolean;
    if (this.myAccount) {
      const con1 = +data.ProjectId;
      const con2 = data.projectName.toLowerCase();
      const pr = this.projectList;
      index = pr.findIndex(
        (i: { id: number; projectName: string }): boolean => i.id === con1 && i.projectName.toLowerCase() === con2,
      );
      this.ProjectId = +data.ProjectId;
    }
    if (index === -1) {
      this.getMemberDetailData(data);
    }
    if (approveStatus) {
      condition = data?.MemberId !== this.userData?.id;
    }
    if ((this.exist === true || index !== -1) && condition) {
      if (this.ProjectId === +data.ProjectId) {
        if (approveStatus) {
          this.deliveryService.updatedCurrentConcreteRequestStatus(+data.ConcreteRequestId);
        }
        this.deliveryService.updateConcreteRequestHistory({ status: true }, 'historyUpdate');
        this.deliveryService.updateConcreteRequestHistory1({ status: true }, 'historyUpdate');
      }
      if (data.MemberId !== this.userData.id && Object.keys(this.userData).length !== 0) {
        this.showConcreteRequestNotification = true;
        this.interval = setInterval((): void => {
          this.showConcreteRequestNotification = false;
          this.clearValue();
        }, 10000);
        this.notifyConcreteRequestData = data;
      }
    }
  }

  public notificationcount(response: { data: any }): void {
    const object = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.unReadCount(object).subscribe((res): void => {
      this.deliveryService.updatedRefreshCount(true);
    });
  }

  public updateTemplates(): void {
    this.templateService.updateTemplate().subscribe((res): void => {
      this.deliveryService.updatedRefreshCount(true);
    });
  }

  public existDataInspection(response, approveStatus): void {
    const { data } = response;
    let index = -1;
    let condition;
    if (this.myAccount) {
      const con1 = +data.ProjectId;
      const con2 = data.projectName.toLowerCase();
      const pr = this.projectList;
      index = pr.findIndex((i): boolean => i.id === con1 && i.projectName.toLowerCase() === con2);
      this.ProjectId = +data.ProjectId;
    }
    if (index === -1) {
      this.getMemberDetailData(data);
    }
    if (approveStatus) {
      condition = data?.MemberId !== this.userData?.id;
    }
    if ((this.exist === true || index !== -1) && condition) {
      if (this.ProjectId === +data.ProjectId) {
        if (approveStatus) {
          this.deliveryService.updatedInspectionCurrentStatus(+data.DeliveryRequestId);
        }
        this.deliveryService.updatedInspectionHistory({ status: true }, 'historyUpdate');
      }
      if (data.MemberId !== this.userData.id && Object.keys(this.userData).length !== 0) {
        this.showNotification = true;
        this.interval = setInterval((): void => {
          this.showNotification = false;
          this.clearValue();
        }, 10000);
        this.notifyData = data;
      }
    }
  }

  public inspectionAttachmentOrCommentUpdated(response, approveStatus): void {
    const { data } = response;
    let index = -1;
    let condition;
    if (this.myAccount) {
      const con1 = +data.ProjectId;
      const con2 = data.projectName.toLowerCase();
      const pr = this.projectList;
      index = pr.findIndex((i): boolean => i.id === con1 && i.projectName.toLowerCase() === con2);
      this.ProjectId = +data.ProjectId;
    }
    if (index === -1) {
      this.getMemberDetailData(data);
    }
    if (approveStatus) {
      condition = data?.MemberId !== this.userData?.id;
    }
    if ((this.exist === true || index !== -1) && condition) {
      if (this.ProjectId === +data.ProjectId) {
        if (approveStatus) {
          this.deliveryService.updatedInspectionCurrentStatus(+data.DeliveryRequestId);
        }
        this.deliveryService.updatedInspectionHistory1({ status: true }, 'historyUpdate');
      }
      if (data.MemberId !== this.userData.id && Object.keys(this.userData).length !== 0) {
        this.showNotification = true;
        this.interval = setInterval((): void => {
          this.showNotification = false;
          this.clearValue();
        }, 10000);
        this.notifyData = data;
      }
    }
  }

  public updateDashboardSiteplan(response): void {
    this.projectService.updateDashboardSiteplan(response);
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'notify':
          this.redirection(data);
          break;
        case 'close':
          this.closePopup();
          break;

        case 'close1':
          this.closePopup1();
          break;

        case 'close2':
          this.closePopup2();
          break;
        default:
          break;
      }
    }
  }
}
