import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import {
  debounceTime, filter, merge, Subscription, tap,
} from 'rxjs';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';
import { MixpanelService } from '../../../services/mixpanel.service';

@Component({
  selector: 'app-inspection-comments',
  templateUrl: './comments.component.html',
})
export class InspectionCommentsComponent implements OnInit, OnDestroy {
  public commentDetailsForm: FormGroup;

  public ProjectId: any;

  public commentList: any = [];

  public loader = true;

  public submitted = false;

  public formSubmitted = false;

  public inspectionRequestId: any;

  public ParentCompanyId: any;

  private readonly subscription: Subscription = new Subscription();

  public constructor(
    public DeliveryService: DeliveryService,
    private readonly formBuilder: FormBuilder,
    private readonly mixpanelService: MixpanelService,
    private readonly toastr: ToastrService,
    public socket: Socket,
    public projectService: ProjectService,
  ) {
    this.projectService.ParentCompanyId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ParentCompanyId = res;
      }
    });
    this.projectService.projectParent.subscribe((response2): void => {
      if (response2 !== undefined && response2 !== null && response2 !== '') {
        this.loader = true;
        this.ProjectId = response2.ProjectId;
        this.ParentCompanyId = response2.ParentCompanyId;
        this.loader = true;
      }
    });
    const historyTrigger$ = merge(
      this.DeliveryService.InspectionRequestId.pipe(
        tap((id) => {
          this.inspectionRequestId = id;
        }),
      ),
      this.DeliveryService.inspectionUpdated1,
    ).pipe(
      debounceTime(100),
      filter(() => !!this.inspectionRequestId),
    );

    this.subscription.add(
      historyTrigger$.subscribe(() => {
        this.getHistory();
      }),
    );

    this.commentForm();
  }

  public ngOnInit(): void {
    /* */
  }

  public getHistory(): void {
    this.loader = true;
    const param = {
      InspectionRequestId: this.inspectionRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    this.DeliveryService.getInspectionRequestComment(param).subscribe((res): void => {
      this.commentList = res?.data?.rows;
      this.loader = false;
    });
  }

  public commentForm(): void {
    this.commentDetailsForm = this.formBuilder.group(
      {
        comment: [
          '',
          Validators.compose([
            Validators.required,
          ]),
        ],
      },
    );
  }

  public checkStringEmptyValues(formValue: { comment: string; }): boolean {
    if (formValue.comment.trim() === '') {
      return true;
    }
    return false;
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    if (this.commentDetailsForm.invalid) {
      this.formSubmitted = false;

      return;
    }
    const formValue = this.commentDetailsForm.value;
    if (!this.checkStringEmptyValues(formValue)) {
      const payload = {
        comment: formValue.comment.trim(),
        InspectionRequestId: this.inspectionRequestId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.DeliveryService.createInspectionComment(payload).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Comment added against a inspection Booking');
            this.socket.emit('InspectionCommentHistory', response);
            this.submitted = false;
            this.formSubmitted = false;
            this.DeliveryService.updatedInspectionHistory1({ status: true }, 'commentHistory');
            this.commentDetailsForm.reset();
          }
        },
        error: (commentError): void => {
          this.commentDetailsForm.reset();
          if (commentError.message?.statusCode === 400) {
            this.showCommentError(commentError);
          } else {
            this.toastr.error(commentError.message, 'OOPS!');
            this.submitted = false;
          }
        },
      });
    } else {
      this.toastr.error('Please enter a valid comment.', 'OOPS!');
      this.submitted = false;
      this.formSubmitted = false;
    }
  }

  public showCommentError(commentError: { message: { details: ({ [s: string]: unknown; } | ArrayLike<unknown>)[]; }; }): void {
    let commentErrorMessage: any = '';
    commentErrorMessage = Object.values(commentError.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(commentErrorMessage);
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
