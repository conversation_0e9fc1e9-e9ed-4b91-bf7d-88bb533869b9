import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { NgxPaginationModule } from 'ngx-pagination';
import { ModalModule, BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { Title } from '@angular/platform-browser';
import { WasteLogComponent } from './waste-log.component';
import { ProjectService } from '../../services/profile/project.service';
import { AuthService } from '../../services/auth/auth.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { MixpanelService } from '../../services/mixpanel.service';
import { of, throwError } from 'rxjs';

describe('WasteLogComponent', () => {
  let component: WasteLogComponent;
  let fixture: ComponentFixture<WasteLogComponent>;
  let mockProjectService: jest.Mocked<ProjectService>;
  let mockToastrService: jest.Mocked<ToastrService>;
  let mockMixpanelService: jest.Mocked<MixpanelService>;
  let mockModalService: jest.Mocked<BsModalService>;
  let mockDeliveryService: jest.Mocked<DeliveryService>;
  let mockAuthService: jest.Mocked<AuthService>;
  let mockModalRef: jest.Mocked<BsModalRef>;
  let mockTitleService: jest.Mocked<Title>;

  beforeEach(async () => {
    mockProjectService = {
      projectId: of('123'),
      ParentCompanyId: of('456'),
      listWasteLog: jest.fn(),
      addWasteLog: jest.fn(),
      deleteEquipment: jest.fn(),
      editEquipment: jest.fn(),
      deactiveEquipment: jest.fn(),
      getEquipmentMappedRequests: jest.fn()
    } as any;

    mockToastrService = {
      success: jest.fn(),
      error: jest.fn()
    } as any;

    mockMixpanelService = {
      addMixpanelEvents: jest.fn()
    } as any;

    mockModalService = {
      show: jest.fn()
    } as any;

    mockDeliveryService = {
      loginUser: of({ id: 1, name: 'Test User' })
    } as any;

    mockAuthService = {} as any;

    mockModalRef = {
      hide: jest.fn()
    } as any;

    mockTitleService = {
      setTitle: jest.fn()
    } as any;

    await TestBed.configureTestingModule({
      declarations: [WasteLogComponent],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        RouterTestingModule,
        HttpClientTestingModule,
        NgxPaginationModule,
        ModalModule.forRoot(),
        ToastrModule.forRoot()
      ],
      providers: [
        UntypedFormBuilder,
        { provide: ProjectService, useValue: mockProjectService },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: MixpanelService, useValue: mockMixpanelService },
        { provide: BsModalService, useValue: mockModalService },
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: AuthService, useValue: mockAuthService },
        { provide: Title, useValue: mockTitleService },
        { provide: BsModalRef, useValue: mockModalRef }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(WasteLogComponent);
    component = fixture.componentInstance;
    component.modalRef = mockModalRef;
    component.modalRef1 = mockModalRef;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Initialization', () => {
    it('should initialize with correct default values', () => {
      expect(component.currentPageNo).toBe(1);
      expect(component.pageSize).toBe(25);
      expect(component.pageNo).toBe(1);
      expect(component.totalCount).toBe(0);
      expect(component.loader).toBe(true);
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.wasteTypeList).toEqual(['Textile Waste']);
      expect(component.haulVehicleList).toEqual(['Medium Duty Truck', 'Heavy Duty Truck', 'Light Duty Truck', 'Passenger Car']);
    });

    it('should set title on initialization', () => {
      expect(mockTitleService.setTitle).toHaveBeenCalledWith('Follo - Equipment');
    });

    it('should subscribe to loginUser and set authUser', () => {
      expect(component.authUser).toEqual({ id: 1, name: 'Test User' });
    });

    it('should subscribe to projectId and call getEquipments', () => {
      jest.spyOn(component, 'getEquipments').mockImplementation();
      component.ProjectId = '123';

      expect(component.ProjectId).toBe('123');
    });

    it('should subscribe to ParentCompanyId and call getEquipments', () => {
      jest.spyOn(component, 'getEquipments').mockImplementation();
      component.ParentCompanyId = '456';

      expect(component.ParentCompanyId).toBe('456');
    });
  });

  describe('Form Initialization', () => {
    it('should create waste log details form with correct validators', () => {
      expect(component.wasteLogDetailsForm).toBeDefined();
      expect(component.wasteLogDetailsForm.get('hauler')).toBeDefined();
      expect(component.wasteLogDetailsForm.get('wasteType')).toBeDefined();
      expect(component.wasteLogDetailsForm.get('quantity')).toBeDefined();
      expect(component.wasteLogDetailsForm.get('landfillDestination')).toBeDefined();
      expect(component.wasteLogDetailsForm.get('haulVehicle')).toBeDefined();
    });

    it('should create equipment edit form with correct validators', () => {
      expect(component.equipmentEditForm).toBeDefined();
      expect(component.equipmentEditForm.get('equipmentName')).toBeDefined();
      expect(component.equipmentEditForm.get('EquipmentType')).toBeDefined();
      expect(component.equipmentEditForm.get('controlledBy')).toBeDefined();
    });

    it('should create filter form', () => {
      expect(component.filterForm).toBeDefined();
      expect(component.filterForm.get('idFilter')).toBeDefined();
      expect(component.filterForm.get('nameFilter')).toBeDefined();
      expect(component.filterForm.get('companyNameFilter')).toBeDefined();
    });
  });

  describe('Keyboard Event Handlers', () => {
    it('should handle toggle keydown with Enter key', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'sortByField').mockImplementation();

      component.handleToggleKeydown(event, 'name', 'ASC');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.sortByField).toHaveBeenCalledWith('name', 'ASC');
    });

    it('should handle toggle keydown with Space key', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'sortByField').mockImplementation();

      component.handleToggleKeydown(event, 'name', 'ASC');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.sortByField).toHaveBeenCalledWith('name', 'ASC');
    });

    it('should handle down keydown with clear action', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'clear').mockImplementation();

      component.handleDownKeydown(event, 'data', 'item', 'clear');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.clear).toHaveBeenCalled();
    });

    it('should handle down keydown with delete action', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'openDeleteModal').mockImplementation();

      component.handleDownKeydown(event, 'data', 'item', 'delete');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.openDeleteModal).toHaveBeenCalledWith('data', 'item');
    });

    it('should handle down keydown with filter action', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'openFilterModal').mockImplementation();

      component.handleDownKeydown(event, 'data', 'item', 'filter');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.openFilterModal).toHaveBeenCalledWith('data');
    });
  });

  describe('Sorting and Filtering', () => {
    it('should sort by field', () => {
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.sortByField('name', 'ASC');

      expect(component.sortColumn).toBe('name');
      expect(component.sort).toBe('ASC');
      expect(component.getEquipments).toHaveBeenCalled();
    });

    it('should clear search and filters', () => {
      component.showSearchbar = true;
      component.search = 'test';
      component.pageNo = 2;
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.clear();

      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
      expect(component.pageNo).toBe(1);
      expect(component.getEquipments).toHaveBeenCalled();
    });

    it('should handle search with data', () => {
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.getSearchEquipment('test search');

      expect(component.showSearchbar).toBe(true);
      expect(component.pageNo).toBe(1);
      expect(component.search).toBe('test search');
      expect(component.getEquipments).toHaveBeenCalled();
    });

    it('should handle search with empty data', () => {
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.getSearchEquipment('');

      expect(component.showSearchbar).toBe(false);
      expect(component.pageNo).toBe(1);
      expect(component.search).toBe('');
      expect(component.getEquipments).toHaveBeenCalled();
    });
  });

  describe('Data Loading', () => {
    it('should fetch waste logs successfully', () => {
      const mockResponse = {
        data: {
          rows: [{ id: 1, name: 'Test Waste' }],
          count: 1
        }
      };
      mockProjectService.listWasteLog.mockReturnValue(of(mockResponse));
      component.ProjectId = '123';
      component.ParentCompanyId = '456';

      component.getEquipments();

      expect(component.loader).toBe(false);
      expect(component.equipmentlogList).toEqual(mockResponse.data.rows);
      expect(component.totalCount).toBe(mockResponse.data.count);
      expect(mockProjectService.listWasteLog).toHaveBeenCalledWith(
        {
          ProjectId: '123',
          pageSize: 25,
          pageNo: 1,
          ParentCompanyId: '456'
        },
        expect.any(Object)
      );
    });

    it('should not fetch waste logs when ProjectId or ParentCompanyId is missing', () => {
      // Reset the mock to clear any calls from constructor
      mockProjectService.listWasteLog.mockClear();

      component.ProjectId = null;
      component.ParentCompanyId = '456';

      component.getEquipments();

      expect(mockProjectService.listWasteLog).not.toHaveBeenCalled();
    });
  });

  describe('Form Reset and Modal Operations', () => {
    it('should reset filter and close modal', () => {
      component.filterCount = 5;
      component.search = 'test';
      component.pageNo = 2;
      jest.spyOn(component, 'filterDetailsForm').mockImplementation();
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.resetFilter();

      expect(component.filterCount).toBe(0);
      expect(component.search).toBe('');
      expect(component.pageNo).toBe(1);
      expect(component.filterDetailsForm).toHaveBeenCalled();
      expect(component.getEquipments).toHaveBeenCalled();
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should reset form with no action', () => {
      component.resetForm('no');
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should reset form with yes action', () => {
      component.submitted = true;
      component.formSubmitted = true;
      component.editSubmitted = true;
      component.formEditSubmitted = true;

      component.resetForm('yes');

      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should close modal with dirty form', () => {
      const mockTemplate = {} as any;
      component.wasteLogDetailsForm.markAsDirty();
      jest.spyOn(component, 'closeModalPopup').mockImplementation();

      component.close(mockTemplate, 'newequipment');

      expect(component.closeModalPopup).toHaveBeenCalledWith(mockTemplate);
    });

    it('should close modal with clean form', () => {
      const mockTemplate = {} as any;
      jest.spyOn(component, 'resetForm').mockImplementation();

      component.close(mockTemplate, 'newequipment');

      expect(component.resetForm).toHaveBeenCalledWith('yes');
    });

    it('should close modal for deactivateEquipment', () => {
      const mockTemplate = {} as any;
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.close(mockTemplate, 'deactivateEquipment');

      expect(mockModalRef.hide).toHaveBeenCalled();
      expect(component.getEquipments).toHaveBeenCalled();
    });

    it('should open close modal popup', () => {
      const mockTemplate = {} as any;
      mockModalService.show.mockReturnValue(mockModalRef);

      component.closeModalPopup(mockTemplate);

      expect(mockModalService.show).toHaveBeenCalledWith(mockTemplate, {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
      });
    });
  });

  describe('Pagination', () => {
    it('should change page size and fetch equipment', () => {
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.changePageSize(50);

      expect(component.pageSize).toBe(50);
      expect(component.getEquipments).toHaveBeenCalled();
    });

    it('should change page number and fetch equipment', () => {
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.changePageNo(3);

      expect(component.currentPageNo).toBe(3);
      expect(component.getEquipments).toHaveBeenCalled();
    });
  });

  describe('Form Submission', () => {
    beforeEach(() => {
      component.wasteLogDetailsForm.patchValue({
        hauler: 'Test Hauler',
        wasteType: 'Textile Waste',
        quantity: '100',
        landfillDestination: 'Test Destination',
        haulVehicle: 'Medium Duty Truck',
        wasteTickets: 'TK123',
        projectId: '123'
      });
      component.ProjectId = '123';
    });

    it('should submit waste log successfully', () => {
      const mockResponse = { message: 'Success' };
      mockProjectService.addWasteLog.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.onSubmit();

      // Check that the service was called with correct parameters
      expect(mockProjectService.addWasteLog).toHaveBeenCalledWith({
        hauler: 'Test Hauler',
        wasteType: 'Textile Waste',
        quantity: '100',
        landfillDestination: 'Test Destination',
        haulVehicle: 'Medium Duty Truck',
        wasteTickets: 'TK123',
        projectId: '123'
      });
      expect(mockToastrService.success).toHaveBeenCalledWith('Success', 'Success');
      expect(mockMixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Added Equipment');
    });

    it('should handle invalid form submission', () => {
      component.wasteLogDetailsForm.patchValue({
        hauler: '',
        wasteType: '',
        quantity: '',
        landfillDestination: '',
        haulVehicle: ''
      });

      component.onSubmit();

      expect(component.submitted).toBe(true);
      expect(component.formSubmitted).toBe(false);
      expect(mockProjectService.addWasteLog).not.toHaveBeenCalled();
    });

    it('should handle form submission error with status code 400', () => {
      const mockError = {
        message: {
          statusCode: 400,
          details: [{ field: 'Error message' }]
        }
      };
      mockProjectService.addWasteLog.mockReturnValue(throwError(() => mockError));
      jest.spyOn(component, 'showError').mockImplementation();

      component.onSubmit();

      expect(component.showError).toHaveBeenCalledWith(mockError);
    });

    it('should handle form submission error without message', () => {
      const mockError = {};
      mockProjectService.addWasteLog.mockReturnValue(throwError(() => mockError));

      component.onSubmit();

      expect(mockToastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should handle form submission error with message', () => {
      const mockError = { message: 'Custom error message' };
      mockProjectService.addWasteLog.mockReturnValue(throwError(() => mockError));

      component.onSubmit();

      expect(mockToastrService.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
    });

    it('should handle string empty values validation', () => {
      jest.spyOn(component, 'checkStringEmptyValues').mockReturnValue(true);

      component.onSubmit();

      expect(mockToastrService.error).toHaveBeenCalledWith('Please Enter valid Equipment Name/Type.', 'OOPS!');
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
    });
  });

  describe('Equipment Operations', () => {
    it('should switch equipment correctly', () => {
      const mockData: any = {
        equipmentDetails: [{ Equipment: { id: 1 } }]
      };

      component.switchEquipment(mockData, 2);

      expect(mockData.changedEquipmentId).toBe(2);
    });

    it('should not switch equipment when same id', () => {
      const mockData: any = {
        equipmentDetails: [{ Equipment: { id: 1 } }]
      };

      component.switchEquipment(mockData, 1);

      expect(mockData.changedEquipmentId).toBeNull();
    });

    it('should handle status change', () => {
      const mockEvent = { target: { checked: true } } as any;
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      component.onStatusChange(mockEvent);

      expect(consoleSpy).toHaveBeenCalledWith('Switch status:', true);
      consoleSpy.mockRestore();
    });

    it('should open deactivate modal when event is false', () => {
      const mockTemplate = {} as any;
      const mockData = { equipmentAutoId: 1, equipmentName: 'Test Equipment' };
      mockModalService.show.mockReturnValue(mockModalRef);
      mockProjectService.getEquipmentMappedRequests.mockReturnValue(of({
        data: {
          mappedRequest: [],
          equipments: {
            craneEquipments: [],
            nonCraneEquipments: []
          }
        }
      }));
      component.ProjectId = '123';

      component.openDeactivateModal(mockTemplate, mockData, false);

      expect(component.equipmentData).toBe(mockData);
      expect(mockModalService.show).toHaveBeenCalled();
      // The loader is set to false after the API call completes, so we check the service call instead
      expect(mockProjectService.getEquipmentMappedRequests).toHaveBeenCalledWith({
        ProjectId: '123',
        id: 1,
        equipmentName: 'Test Equipment'
      });
    });

    it('should activate equipment when event is true', () => {
      const mockTemplate = {} as any;
      const mockData = { id: 1, equipmentName: 'Test Equipment' };
      jest.spyOn(component, 'activateEquipment').mockImplementation();

      component.openDeactivateModal(mockTemplate, mockData, true);

      expect(component.activateEquipment).toHaveBeenCalledWith(mockData);
    });

    it('should activate equipment successfully', () => {
      const mockData = { id: 1, equipmentName: 'Test Equipment' };
      const mockResponse = { message: 'Activated successfully' };
      mockProjectService.editEquipment.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getEquipments').mockImplementation();
      component.ProjectId = '123';
      component.ParentCompanyId = '456';

      component.activateEquipment(mockData);

      expect(mockProjectService.editEquipment).toHaveBeenCalledWith({
        id: 1,
        equipmentName: 'Test Equipment',
        ProjectId: '123',
        ParentCompanyId: '456',
        isActive: true
      });
      expect(mockToastrService.success).toHaveBeenCalledWith('Activated successfully', 'Success');
      expect(component.getEquipments).toHaveBeenCalled();
    });

    it('should handle activate equipment error', () => {
      const mockData = { id: 1, equipmentName: 'Test Equipment' };
      const mockError = { message: 'Activation failed' };
      mockProjectService.editEquipment.mockReturnValue(throwError(() => mockError));

      component.activateEquipment(mockData);

      expect(mockToastrService.error).toHaveBeenCalledWith('Activation failed', 'OOPS!');
    });
  });

  describe('Equipment Edit Form', () => {
    beforeEach(() => {
      component.equipmentEditForm.patchValue({
        equipmentName: 'Updated Equipment',
        EquipmentType: '1',
        controlledBy: 'Test User',
        id: '1'
      });
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
    });

    it('should submit edit form successfully', () => {
      const mockResponse = { message: 'Updated successfully' };
      mockProjectService.editEquipment.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.onEditSubmit();

      // Check that the service was called with correct parameters
      expect(mockProjectService.editEquipment).toHaveBeenCalledWith({
        equipmentName: 'Updated Equipment',
        PresetEquipmentTypeId: 1,
        controlledBy: 'Test User',
        id: '1',
        ProjectId: '123',
        ParentCompanyId: '456'
      });
      expect(mockToastrService.success).toHaveBeenCalledWith('Updated successfully', 'Success');
      expect(mockMixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Edited  Equipment');
    });

    it('should handle invalid edit form submission', () => {
      component.equipmentEditForm.patchValue({
        equipmentName: '',
        EquipmentType: '',
        controlledBy: ''
      });

      component.onEditSubmit();

      expect(component.editSubmitted).toBe(true);
      expect(component.formEditSubmitted).toBe(false);
      expect(mockProjectService.editEquipment).not.toHaveBeenCalled();
    });

    it('should handle edit form submission error', () => {
      const mockError = { message: 'Edit failed' };
      mockProjectService.editEquipment.mockReturnValue(throwError(() => mockError));

      component.onEditSubmit();

      expect(mockToastrService.error).toHaveBeenCalledWith('Edit failed', 'OOPS!');
    });
  });

  describe('Delete Operations', () => {
    it('should delete equipment successfully', () => {
      const mockResponse = { message: 'Deleted successfully' };
      component.deleteIndex = [1];
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      mockProjectService.deleteEquipment.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.deleteEquipment();

      // Check that the service was called with correct parameters
      expect(mockProjectService.deleteEquipment).toHaveBeenCalledWith({
        id: [1],
        ProjectId: '123',
        isSelectAll: false,
        ParentCompanyId: '456'
      });
      expect(mockToastrService.success).toHaveBeenCalledWith('Deleted successfully', 'Success');
      expect(mockMixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Deleted  Equipment');
    });

    it('should handle delete equipment error', () => {
      const mockError = { message: 'Delete failed' };
      component.deleteIndex = [1];
      mockProjectService.deleteEquipment.mockReturnValue(throwError(() => mockError));
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.deleteEquipment();

      expect(mockToastrService.error).toHaveBeenCalledWith('Delete failed', 'OOPS!');
      expect(component.getEquipments).toHaveBeenCalled();
    });

    it('should open delete modal for specific index', () => {
      const mockTemplate = {} as any;
      component.equipmentlogList = [{ id: 1 }, { id: 2 }];
      jest.spyOn(component, 'openModal').mockImplementation();

      component.openDeleteModal(0, mockTemplate);

      expect(component.deleteIndex[0]).toBe(1);
      expect(component.currentDeleteId).toBe(0);
      expect(component.remove).toBe(false);
      expect(component.openModal).toHaveBeenCalledWith(mockTemplate);
    });

    it('should open delete modal for select all', () => {
      const mockTemplate = {} as any;
      jest.spyOn(component, 'openModal').mockImplementation();

      component.openDeleteModal(-1, mockTemplate);

      expect(component.remove).toBe(true);
      expect(component.openModal).toHaveBeenCalledWith(mockTemplate);
    });

    it('should remove selected items when selectAll is true', () => {
      component.selectAll = true;
      jest.spyOn(component, 'deleteEquipment').mockImplementation();

      component.removeItem();

      expect(component.deleteSubmitted).toBe(true);
      expect(component.deleteEquipment).toHaveBeenCalled();
    });

    it('should remove selected items when selectAll is false', () => {
      component.selectAll = false;
      component.equipmentlogList = [
        { id: 1, isChecked: true },
        { id: 2, isChecked: false },
        { id: 3, isChecked: true }
      ];
      jest.spyOn(component, 'deleteEquipment').mockImplementation();

      component.removeItem();

      expect(component.deleteIndex).toEqual([1, 3]);
      expect(component.deleteEquipment).toHaveBeenCalled();
    });
  });

  describe('Selection Operations', () => {
    it('should check selected row when selectAll is true', () => {
      component.selectAll = true;

      const result = component.checkSelectedRow();

      expect(result).toBe(false);
    });

    it('should check selected row when items are checked', () => {
      component.selectAll = false;
      component.equipmentlogList = [
        { isChecked: true },
        { isChecked: false }
      ];

      const result = component.checkSelectedRow();

      expect(result).toBe(false);
    });

    it('should check selected row when no items are checked', () => {
      component.selectAll = false;
      component.equipmentlogList = [
        { isChecked: false },
        { isChecked: false }
      ];

      const result = component.checkSelectedRow();

      expect(result).toBe(true);
    });

    it('should set selected item', () => {
      component.equipmentlogList = [
        { isChecked: false },
        { isChecked: false }
      ];

      component.setSelectedItem(0);

      expect(component.equipmentlogList[0].isChecked).toBe(true);
    });

    it('should select all equipments', () => {
      component.selectAll = false;
      component.equipmentlogList = [
        { isChecked: false },
        { isChecked: false }
      ];

      component.selectAllEquipmentsData();

      expect(component.selectAll).toBe(true);
      expect(component.equipmentlogList[0].isChecked).toBe(true);
      expect(component.equipmentlogList[1].isChecked).toBe(true);
    });

    it('should deselect all equipments', () => {
      component.selectAll = true;
      component.equipmentlogList = [
        { isChecked: true },
        { isChecked: true }
      ];

      component.selectAllEquipmentsData();

      expect(component.selectAll).toBe(false);
      expect(component.equipmentlogList[0].isChecked).toBe(false);
      expect(component.equipmentlogList[1].isChecked).toBe(false);
    });
  });

  describe('Modal Operations', () => {
    it('should open modal with correct configuration', () => {
      const mockTemplate = {} as any;
      mockModalService.show.mockReturnValue(mockModalRef);

      component.openModal(mockTemplate);

      expect(mockModalService.show).toHaveBeenCalledWith(mockTemplate, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal'
      });
    });

    it('should open filter modal with correct configuration', () => {
      const mockTemplate = {} as any;
      mockModalService.show.mockReturnValue(mockModalRef);

      component.openFilterModal(mockTemplate);

      expect(mockModalService.show).toHaveBeenCalledWith(mockTemplate, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-sm filter-popup custom-modal'
      });
    });

    it('should reset and close modal', () => {
      component.submitted = true;
      component.formSubmitted = true;
      component.editSubmitted = true;
      component.formEditSubmitted = true;

      component.resetAndClose();

      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should open edit modal and populate form', () => {
      const mockTemplate = {} as any;
      component.equipmentlogList = [{
        id: 1,
        equipmentAutoId: 2,
        equipmentName: 'Test Equipment',
        PresetEquipmentType: { id: 3 },
        controlledBy: 'Test User'
      }];
      jest.spyOn(component, 'openModal').mockImplementation();

      component.openEditModal(0, mockTemplate);

      expect(component.editIndex).toBe(0);
      expect(component.equipmentEditForm.get('id').value).toBe(1);
      expect(component.equipmentEditForm.get('equipmentAutoId').value).toBe(2);
      expect(component.equipmentEditForm.get('equipmentName').value).toBe('Test Equipment');
      expect(component.equipmentEditForm.get('EquipmentType').value).toBe(3);
      expect(component.equipmentEditForm.get('controlledBy').value).toBe('Test User');
      expect(component.openModal).toHaveBeenCalledWith(mockTemplate);
    });
  });

  describe('Filter Operations', () => {
    beforeEach(() => {
      component.filterForm.patchValue({
        idFilter: '1',
        nameFilter: 'Test',
        companyNameFilter: 'Company',
        typeFilter: 'Type',
        memberFilter: '2'
      });
    });

    it('should submit filter with all fields filled', () => {
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.filterSubmit();

      expect(component.filterCount).toBe(5);
      expect(component.pageNo).toBe(1);
      expect(component.getEquipments).toHaveBeenCalled();
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should submit filter with some fields empty', () => {
      component.filterForm.patchValue({
        idFilter: '',
        nameFilter: 'Test',
        companyNameFilter: '',
        typeFilter: 'Type',
        memberFilter: ''
      });
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.filterSubmit();

      expect(component.filterCount).toBe(2);
      expect(component.getEquipments).toHaveBeenCalled();
    });
  });

  describe('Utility Functions', () => {
    it('should validate alphanumeric input for equipments - valid characters', () => {
      const validEvents = [
        { which: 65, keyCode: 65 }, // A
        { which: 90, keyCode: 90 }, // Z
        { which: 97, keyCode: 97 }, // a
        { which: 122, keyCode: 122 }, // z
        { which: 48, keyCode: 48 }, // 0
        { which: 57, keyCode: 57 }, // 9
        { which: 8, keyCode: 8 }, // Backspace
        { which: 32, keyCode: 32 } // Space
      ];

      validEvents.forEach(event => {
        expect(component.alphaNumericForEquipments(event)).toBe(true);
      });
    });

    it('should validate alphanumeric input for equipments - invalid characters', () => {
      const invalidEvents = [
        { which: 33, keyCode: 33 }, // !
        { which: 64, keyCode: 64 }, // @
        { which: 35, keyCode: 35 } // #
      ];

      invalidEvents.forEach(event => {
        expect(component.alphaNumericForEquipments(event)).toBe(false);
      });
    });

    it('should show error correctly', () => {
      const mockError = {
        message: {
          details: [{ field: 'Error message' }]
        }
      };

      component.showError(mockError);

      expect(component.submitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(mockToastrService.error).toHaveBeenCalledWith(['Error message']);
    });

    it('should check string empty values', () => {
      const result = component.checkStringEmptyValues({ EquipmentName: 'Test' });
      expect(result).toBe(false);
    });
  });

  describe('Deactivate Equipment', () => {
    beforeEach(() => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.equipmentData = { id: 1 };
    });

    it('should deactivate equipment with changed equipment data', () => {
      component.mappedRequestList = [
        { changedEquipmentId: 2 },
        { changedEquipmentId: 3 }
      ];
      const mockResponse = { message: 'Deactivated successfully' };
      mockProjectService.deactiveEquipment.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.deactiveEquipment();

      // Check that the service was called with correct parameters
      expect(mockProjectService.deactiveEquipment).toHaveBeenCalledWith({
        id: 1,
        equipmentSwitchedRequests: [
          { changedEquipmentId: 2 },
          { changedEquipmentId: 3 }
        ],
        ProjectId: '123',
        ParentCompanyId: '456'
      });
      expect(mockToastrService.success).toHaveBeenCalledWith('Deactivated successfully', 'Success');
    });

    it('should deactivate equipment with empty mapped request list', () => {
      component.mappedRequestList = [];
      const mockResponse = { message: 'Deactivated successfully' };
      mockProjectService.deactiveEquipment.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.deactiveEquipment();

      expect(mockProjectService.deactiveEquipment).toHaveBeenCalledWith({
        id: 1,
        ProjectId: '123',
        ParentCompanyId: '456'
      });
      expect(mockToastrService.success).toHaveBeenCalledWith('Deactivated successfully', 'Success');
    });

    it('should handle deactivate equipment error', () => {
      component.mappedRequestList = [];
      const mockError = { message: 'Deactivation failed' };
      mockProjectService.deactiveEquipment.mockReturnValue(throwError(() => mockError));

      component.deactiveEquipment();

      expect(mockToastrService.error).toHaveBeenCalledWith('Deactivation failed', 'OOPS!');
    });

    it('should not deactivate when conditions are not met', () => {
      component.mappedRequestList = [
        { changedEquipmentId: null },
        { changedEquipmentId: 2 }
      ];

      component.deactiveEquipment();

      expect(component.deactivateEquipmentLoader).toBe(false);
      expect(mockProjectService.deactiveEquipment).not.toHaveBeenCalled();
    });
  });
});
