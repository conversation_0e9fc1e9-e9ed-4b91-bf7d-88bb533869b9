import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { of } from 'rxjs';

import { CarbonTrackingComponent } from './carbon-tracking.component';
import { ProjectService } from '../../../app/services/profile/project.service';

describe('CarbonTrackingComponent', () => {
  let component: CarbonTrackingComponent;
  let fixture: ComponentFixture<CarbonTrackingComponent>;
  let projectServiceMock: jest.Mocked<ProjectService>;

  const mockProjectId = '123';
  const mockDashboardData = {
    data: [
      {
        utilityType: 'Electricity',
        totalCost: '1000',
        totalEmissions: '500',
      },
      {
        utilityType: 'Water',
        totalCost: '500',
        totalEmissions: '200',
      },
    ],
  };

  beforeEach(async () => {
    const mock = {
      getDashboardData: jest.fn().mockReturnValue(of(mockDashboardData)),
      projectId: of(mockProjectId),
    };

    await TestBed.configureTestingModule({
      declarations: [ CarbonTrackingComponent ],
      providers: [
        { provide: ProjectService, useValue: mock },
      ],
      schemas: [NO_ERRORS_SCHEMA],
    })
    .compileComponents();

    projectServiceMock = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
  });

  beforeEach(fakeAsync(() => {
    fixture = TestBed.createComponent(CarbonTrackingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    tick(); // Wait for async operations
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.view).toEqual([700, 400]);
    expect(component.colorScheme.domain).toEqual(['#5AA454', '#A10A28', '#C7B42C', '#7B68EE', '#FF5733']);
    expect(component.loader).toBe(false);
  });

  it('should process dashboard data correctly', fakeAsync(() => {
    // Trigger data loading
    component.getDashboardData(mockProjectId);
    tick(); // Wait for async operations
    fixture.detectChanges();

    expect(component.chartData.length).toBe(2);
    expect(component.chartData[0]).toEqual({
      name: 'Electricity',
      value: 1000,
    });
    expect(component.totalCO2Emissions).toBe(700); // 500 + 200
  }));

  it('should format X axis tick values correctly', () => {
    const longValue = 'This is a very long value that needs to be truncated';
    const shortValue = 'Short value';
    expect(component.formatXAxisTick(longValue)).toBe('This is a ...');
    expect(component.formatXAxisTick(shortValue)).toBe(shortValue);
  });

  it('should handle empty dashboard data', fakeAsync(() => {
    projectServiceMock.getDashboardData.mockReturnValue(of({ data: null }));
    component.getDashboardData(mockProjectId);
    tick();
    fixture.detectChanges();
    expect(component.chartData).toEqual([]);
    expect(component.totalCO2Emissions).toBe(0);
    expect(component.groupedChartData).toEqual([]);
    expect(component.pieChartData).toEqual([]);
  }));

  it('should process pie chart data correctly', fakeAsync(() => {
    component.getDashboardData(mockProjectId);
    tick();
    fixture.detectChanges();

    expect(component.pieChartData.length).toBe(2);
    expect(component.pieChartData[0]).toEqual({
      name: 'Electricity',
      value: 500,
    });
  }));

  it('should process grouped chart data correctly', fakeAsync(() => {
    component.getDashboardData(mockProjectId);
    tick();
    fixture.detectChanges();

    expect(component.groupedChartData.length).toBe(2);
    expect(component.groupedChartData[0]).toEqual({
      name: 'Electricity',
      co2Emission: 500,
    });
  }));

  it('should set loader flag correctly during data fetch', fakeAsync(() => {
    // loader should be true when fetching, false after
    projectServiceMock.getDashboardData.mockReturnValue(of(mockDashboardData));
    component.loader = false;
    component.getDashboardData(mockProjectId);
    expect(component.loader).toBe(true); // set before async
    tick();
    expect(component.loader).toBe(false); // set after async
  }));

  it('should handle items with missing totalEmissions', fakeAsync(() => {
    const dataWithMissingEmissions = {
      data: [
        { utilityType: 'Gas', totalCost: '300' },
        { utilityType: 'Oil', totalCost: '200', totalEmissions: null },
      ],
    };
    projectServiceMock.getDashboardData.mockReturnValue(of(dataWithMissingEmissions));
    component.getDashboardData(mockProjectId);
    tick();
    expect(component.totalCO2Emissions).toBe(0);
    expect(component.pieChartData).toEqual([]);
    expect(component.groupedChartData).toEqual([
      { name: 'Gas', co2Emission: 0 },
      { name: 'Oil', co2Emission: 0 },
    ]);
  }));
});
