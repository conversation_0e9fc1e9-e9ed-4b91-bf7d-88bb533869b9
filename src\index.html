<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Follo</title>
    <base href="/" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <meta name="”description”" content="Follo Web" />
    <meta name="”keywords”" content="Follo Web" />
    <meta name="”author”" content="Follo Web" />

    <link rel="icon" type="image/x-icon" href="./assets/images/favicon.png" />

    <!-- dns-prefetch -->
    <link rel="dns-prefetch" href="apis.google.com" />
    <link rel="dns-prefetch" href="maps.googleapis.com" />
    <link rel="dns-prefetch" href="ajax.googleapis.com" />
    <link rel="dns-prefetch" href="https://api-development.folloit.com" />
    <!--Preload-->
    <link
      rel="preload"
      href="assets/fonts/montserrat.css"
      type="text/css"
      as="style"
      onload="this.onload=null;this.rel='stylesheet';"
      crossorigin="anonymous"
    />

    <link
      rel="preload"
      href="assets/fonts/ubuntu.css"
      type="text/css"
      as="style"
      onload="this.onload=null;this.rel='stylesheet';"
      crossorigin="anonymous"
    />

    <link
      rel="preload"
      href="assets/css/all.min.css"
      type="text/css"
      as="style"
      onload="this.onload=null;this.rel='stylesheet';"
      crossorigin="anonymous"
    />

    <link
      rel="preload"
      href="assets/css/icon-font.min.css"
      type="text/css"
      as="style"
      onload="this.onload=null;this.rel='stylesheet';"
      crossorigin="anonymous"
    />

    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <link
      href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />

  <!-- Google Tag Manager -->
  <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
  new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
  j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
  'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
  })(window,document,'script','dataLayer','GTM-T4QKNH8');</script>
  <!-- End Google Tag Manager -->

    <!-- Hotjar Tracking Code for www.folloit.com -->
    <script>
      (function(h,o,t,j,a,r){
          h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
          h._hjSettings={hjid:2927807,hjsv:6};
          a=o.getElementsByTagName('head')[0];
          r=o.createElement('script');
          r.async=1;
          r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
          a.appendChild(r);
      })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
    </script>

    <script src="https://cdn.jsdelivr.net/npm/fullcalendar-scheduler@5.10.1/main.min.js"></script>

    <!-- Mix Panel Tracking -->
    <script type="text/javascript">
      (function (f, b) {
        if (!b.__SV) {
          var e, g, i, h;
          window.mixpanel = b;
          b._i = [];
          b.init = function (e, f, c) {
            function g(a, d) {
              var b = d.split('.');
              2 == b.length && ((a = a[b[0]]), (d = b[1]));
              a[d] = function () {
                a.push([d].concat(Array.prototype.slice.call(arguments, 0)));
              };
            }
            var a = b;
            'undefined' !== typeof c ? (a = b[c] = []) : (c = 'mixpanel');
            a.people = a.people || [];
            a.toString = function (a) {
              var d = 'mixpanel';
              'mixpanel' !== c && (d += '.' + c);
              a || (d += ' (stub)');
              return d;
            };
            a.people.toString = function () {
              return a.toString(1) + '.people (stub)';
            };
            i =
              'disable time_event track track_pageview track_links track_forms track_with_groups add_group set_group remove_group register register_once alias unregister identify name_tag set_config reset opt_in_tracking opt_out_tracking has_opted_in_tracking has_opted_out_tracking clear_opt_in_out_tracking start_batch_senders people.set people.set_once people.unset people.increment people.append people.union people.track_charge people.clear_charges people.delete_user people.remove'.split(
                ' ',
              );
            for (h = 0; h < i.length; h++) g(a, i[h]);
            var j = 'set set_once union unset remove delete'.split(' ');
            a.get_group = function () {
              function b(c) {
                d[c] = function () {
                  call2_args = arguments;
                  call2 = [c].concat(Array.prototype.slice.call(call2_args, 0));
                  a.push([e, call2]);
                };
              }
              for (
                var d = {},
                  e = ['get_group'].concat(Array.prototype.slice.call(arguments, 0)),
                  c = 0;
                c < j.length;
                c++
              )
                b(j[c]);
              return d;
            };
            b._i.push([e, f, c]);
          };
          b.__SV = 1.2;
          e = f.createElement('script');
          e.type = 'text/javascript';
          e.async = !0;
          e.src =
            'undefined' !== typeof MIXPANEL_CUSTOM_LIB_URL
              ? MIXPANEL_CUSTOM_LIB_URL
              : 'file:' === f.location.protocol &&
                '//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js'.match(/^\/\//)
              ? 'https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js'
              : '//cdn.mxpnl.com/libs/mixpanel-2-latest.min.js';
          g = f.getElementsByTagName('script')[0];
          g.parentNode.insertBefore(e, g);
        }
      })(document, window.mixpanel || []);
      mixpanel.init('a7f3fd49a6dfdbbe81727f3beadcd383');
    </script>
  </head>
  <body>
  <!-- Google Tag Manager (noscript) -->
  <noscript>
    <iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T4QKNH8"
  height="0" width="0" style="display:none;visibility:hidden" title="gtag">
</iframe></noscript>
  <!-- End Google Tag Manager (noscript) -->
    <app-root></app-root>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCMb1xasd2lYrWEA6K4p2HSVSnX5T0pWpI&libraries=places&language=en"></script>
    <script>
      //Delay Load Third Part script & Add Dynamic script version
      var load = setTimeout(function () {
        var script = document.createElement('script');
        var newscript = +new Date();
        document.getElementsByTagName('head')[0].appendChild(script);

        // document.getElementById("googleapis").src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCMb1xasd2lYrWEA6K4p2HSVSnX5T0pWpI&libraries=places&language=en?v="+ newscript;
        document.getElementById('prloadjs').src =
          'https://unpkg.com/preload-it@latest/dist/preload-it.js';
        document.getElementById('api-loader').src =
          'https://unpkg.com/@googlemaps/js-api-loader@^1.2.0/dist/index.min.js';
      }, 8000);
    </script>
    <script type="text/javascript" id="googleapis" defer></script>
    <script type="text/javascript" id="prloadjs" defer></script>
    <script type="text/javascript" id="api-loader" defer></script>
    <noscript>Your browser does not support JavaScript!</noscript>
    <!-- Intercom -->
    <script>
      // We pre-filled your app ID in the widget URL: 'https://widget.intercom.io/widget/kdk2uwzd'
      (function () {
        var w = window;
        var ic = w.Intercom;
        if (typeof ic === 'function') {
          ic('reattach_activator');
          ic('update', w.intercomSettings);
        } else {
          var d = document;
          var i = function () {
            i.c(arguments);
          };
          i.q = [];
          i.c = function (args) {
            i.q.push(args);
          };
          w.Intercom = i;
          var l = function () {
            var s = d.createElement('script');
            s.type = 'text/javascript';
            s.async = true;
            s.src = 'https://widget.intercom.io/widget/kdk2uwzd';
            var x = d.getElementsByTagName('script')[0];
            x.parentNode.insertBefore(s, x);
          };
          if (document.readyState === 'complete') {
            l();
          } else if (w.attachEvent) {
            w.attachEvent('onload', l);
          } else {
            w.addEventListener('load', l, false);
          }
        }
      })();
    </script>
  </body>
</html>
