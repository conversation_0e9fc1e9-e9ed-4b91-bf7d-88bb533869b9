import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import {
  debounceTime, filter, merge, Subscription, tap,
} from 'rxjs';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';
import { MixpanelService } from '../../../services/mixpanel.service';


@Component({
  selector: 'app-comments',
  templateUrl: './comments.component.html',
})
export class CommentsComponent implements OnInit {
  public commentDetailsForm: UntypedFormGroup;

  public ProjectId: any;

  public commentList: any = [];

  public loader = true;

  public submitted = false;

  public formSubmitted = false;

  public DeliveryRequestId: any;

  public ParentCompanyId: any;

  private readonly subscription: Subscription = new Subscription();

  public constructor(
    public deliveryService: DeliveryService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly mixpanelService: MixpanelService,
    private readonly toastr: ToastrService,
    public socket: Socket,
    public projectService: ProjectService,
  ) {
    this.deliveryService.DeliveryRequestId.subscribe((getDeliveryRequestIdResponse1): void => {
      this.DeliveryRequestId = getDeliveryRequestIdResponse1;
    });
    this.projectService.ParentCompanyId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ParentCompanyId = res;
      }
    });
    this.projectService.projectParent.subscribe((response2): void => {
      if (response2 !== undefined && response2 !== null && response2 !== '') {
        this.loader = true;
        this.ProjectId = response2.ProjectId;
        this.ParentCompanyId = response2.ParentCompanyId;
        this.loader = true;
      }
    });
    const historyTrigger$ = merge(
      this.deliveryService.DeliveryRequestId.pipe(
        tap((val) => {
          this.DeliveryRequestId = val;
        }),
      ),
      this.deliveryService.refresh1,
    ).pipe(
      debounceTime(100),
      filter(() => !!this.DeliveryRequestId),
    );

    this.subscription.add(
      historyTrigger$.subscribe(() => {
        this.getHistory();
      }),
    );

    this.commentForm();
  }

  public ngOnInit(): void {
    /* */
  }

  public getHistory(): void {
    this.loader = true;
    const param = {
      DeliveryRequestId: this.DeliveryRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    this.deliveryService.getDeliveryRequestComment(param).subscribe((res): void => {
      this.commentList = res?.data?.rows;
      this.loader = false;
    });
  }

  public commentForm(): void {
    this.commentDetailsForm = this.formBuilder.group(
      {
        comment: [
          '',
          Validators.compose([
            Validators.required,
          ]),
        ],
      },
    );
  }

  public checkStringEmptyValues(formValue: { comment: string }): boolean {
    if (formValue.comment.trim() === '') {
      return true;
    }
    return false;
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    if (this.commentDetailsForm.invalid) {
      this.formSubmitted = false;

      return;
    }
    const formValue = this.commentDetailsForm.value;
    if (!this.checkStringEmptyValues(formValue)) {
      const payload = {
        comment: formValue.comment.trim(),
        DeliveryRequestId: this.DeliveryRequestId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.deliveryService.createComment(payload).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Comment added against a Delivery Booking');
            this.socket.emit('commentHistory', response);
            this.submitted = false;
            this.formSubmitted = false;
            this.deliveryService.updatedHistory1({ status: true }, 'commentHistory');
            this.commentDetailsForm.reset();
          }
        },
        error: (commentError): void => {
          this.commentDetailsForm.reset();
          if (commentError.message?.statusCode === 400) {
            this.showCommentError(commentError);
          } else {
            this.toastr.error(commentError.message, 'OOPS!');
            this.submitted = false;
          }
        },
      });
    } else {
      this.toastr.error('Please enter a valid comment.', 'OOPS!');
      this.submitted = false;
      this.formSubmitted = false;
    }
  }

  public showCommentError(commentError: { message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] } }): void {
    let commentErrorMessage: any = '';
    commentErrorMessage = Object.values(commentError.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(commentErrorMessage);
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
