import { Component, OnInit, TemplateRef } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { ProjectService } from '../../services/profile/project.service';
import { BookingTemplatesService } from '../../services/booking-templates/booking-templates.service';
import { MixpanelService } from '../../services/mixpanel.service';
import { NewDeliveryFormComponent } from '../../delivery-requests/delivery-details/new-delivery-form/new-delivery-form.component';

@Component({
  selector: 'app-template-grid',
  templateUrl: './template-grid.component.html',
})
export class TemplateGridComponent implements OnInit {
  public ProjectId = '';

  public totalCount = 0;

  public loader = true;

  public templateList: any;

  public modalRef: BsModalRef;

  public pageSize = 25;

  public pageNo = 1;

  public selectAll = false;

  public deleteIndex: any = [];

  public dropdownSettings: IDropdownSettings = {
    singleSelection: false,
    idField: 'id',
    textField: 'companyName',
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    itemsShowLimit: 6,
    allowSearchFilter: true,
  };

  public ParentCompanyId: any;

  public deleteSubmitted = false;

  public templateData: any;

  public remove = false;

  public constructor(
    public projectService: ProjectService,
    public bookingTemplateService: BookingTemplatesService,
    private readonly modalService: BsModalService,
    private readonly toastr: ToastrService,
    private readonly mixpanelService: MixpanelService,
  ) {
    this.bookingTemplateService.fetchTemplates.subscribe((data): void => {
      if (data) {
        this.getTemplates();
      }
    });
    this.projectService.projectParent.subscribe((response): void => {
      if (response !== undefined && response !== null && response !== '') {
        this.ParentCompanyId = response.ParentCompanyId;
        this.ProjectId = response.ProjectId;
        this.getTemplates();
      }
    });
  }

  public ngOnInit(): void { /* */ }

  public handleToggleKeydown(event: KeyboardEvent, data: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.openTemplate(data);
    }
  }

  public handleDeleteKeydown(event: KeyboardEvent, data: any, filter: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.openDeleteModal(data, filter);
    }
  }

  public getTemplates(): void {
    this.loader = true;
    if (this.ProjectId && this.pageSize && this.pageNo) {
      if (this.ProjectId) {
        const params = {
          pageSize: this.pageSize,
          pageNo: this.pageNo,
          ParentCompanyId: this.ParentCompanyId,
        };
        const queryPath = {
          ProjectId: this.ProjectId,
        };
        this.bookingTemplateService.getTemplates(queryPath, params).subscribe((res): void => {
          this.templateList = res.data.rows;
          this.totalCount = res.data.count;
          this.loader = false;
        });
      }
    }
  }

  public openDeleteModal(index: number, template: TemplateRef<any>): void {
    if (index !== -1) {
      this.deleteIndex[0] = this.templateList[index].id;
      this.remove = false;
    } else if (index === -1) {
      this.remove = true;
    }
    this.openModal(template);
  }

  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-md new-gate-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }

  public resetAndClose(): void {
    this.deleteSubmitted = false;
    this.modalRef.hide();
  }

  public removeItem(): void {
    this.deleteSubmitted = true;
    if (this.selectAll) {
      this.deleteTemplate();
    } else {
      this.templateList.forEach((element): void => {
        if (element.isChecked) {
          this.deleteIndex.push(+element.id);
        }
      });
      this.deleteTemplate();
    }
  }

  public deleteTemplate(): void {
    this.deleteSubmitted = true;
    const path = {
      ProjectId: this.ProjectId,
    };
    const payload = {
      id: this.deleteIndex,
      ParentCompanyId: this.ParentCompanyId,
      selectAll: this.selectAll,
    };
    this.bookingTemplateService
      .deleteBookingTemplate(path, payload)
      .subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Deleted Delivery Template');
            this.getTemplates();
            this.deleteSubmitted = false;
            this.modalRef.hide();
          }
        },
        error: (deleteError): void => {
          this.deleteSubmitted = false;
          this.getTemplates();
          if (deleteError.message?.statusCode === 400) {
            this.showError(deleteError);
          } else if (!deleteError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deleteError.message, 'OOPS!');
          }
        },
      });
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.deleteSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public changePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.getTemplates();
  }

  public changePageNo(pageNo): void {
    this.pageNo = pageNo;
    this.getTemplates();
  }

  public openTemplate(data): void {
    const initialState = {
      data: {
        bookingTemplate: true,
        isEditTemplate: false,
        id: null,
      },
      title: 'Modal with component',
    };
    if (data) {
      initialState.data.isEditTemplate = true;
      initialState.data.id = data?.id;
    }
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(NewDeliveryFormComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
      initialState,
    });
    this.modalRef.content.closeBtnName = 'Close';
  }

  public selectAllTemplatesData(): void {
    this.selectAll = !this.selectAll;
    if (this.selectAll) {
      this.templateList.forEach((obj, index): void => {
        this.templateList[index].isChecked = true;
        return null;
      });
    } else {
      this.templateList.forEach((obj, index): void => {
        this.templateList[index].isChecked = false;
        return null;
      });
    }
  }

  public setSelectedItem(index: string | number): void {
    this.templateList[index].isChecked = !this.templateList[index].isChecked;
  }

  public checkSelectedRow(): boolean {
    if (this.selectAll) {
      return false;
    }
    const index = this.templateList?.findIndex((item): boolean => item.isChecked === true);
    if (index !== -1) {
      return false;
    }
    return true;
  }
}
