import { ComponentFixture, TestBed, fakeAsync, tick, waitForAsync } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import { of, throwError } from 'rxjs';
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { InspectionAttachmentsComponent } from './attachments.component';
import { ProjectService } from '../../../services/profile/project.service';
import { MixpanelService } from '../../../services/mixpanel.service';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { AuthService } from '../../../services/auth/auth.service';
import { environment } from '../../../../environments/environment';

describe('InspectionAttachmentsComponent', () => {
  let component: InspectionAttachmentsComponent;
  let fixture: ComponentFixture<InspectionAttachmentsComponent>;
  let toastrService: jest.Mocked<ToastrService>;
  let projectService: jest.Mocked<ProjectService>;
  let mixpanelService: jest.Mocked<MixpanelService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let socket: jest.Mocked<Socket>;
  let authService: jest.Mocked<AuthService>;
  const mockUser = { id: '789', name: 'Test User' };

  beforeAll(() => {
    // Mock environment
    (environment as any).apiBaseUrl = 'http://test-api.com/';
  });

  beforeEach(waitForAsync(async () => {
    const mockServices = {
      toastr: {
        success: jest.fn(),
        error: jest.fn(),
      },
      project: {
        ParentCompanyId: of('123'),
      },
      mixpanel: {
        addMixpanelEvents: jest.fn(),
      },
      delivery: {
        removeInspectionAttachement: jest.fn(),
        inspectionAttachement: jest.fn(),
        getInspectionAttachement: jest.fn().mockReturnValue(of({ data: [] })),
        InspectionRequestId: of('456'),
        loginUser: of({ id: '789' }),
        inspectionUpdated1: of({}),
        updatedInspectionHistory1: jest.fn(),
      },
      socket: {
        emit: jest.fn(),
      },
      auth: {
        getUser: jest.fn().mockReturnValue(of(mockUser)),
      },
    };

    await TestBed.configureTestingModule({
      imports: [HttpClientTestingModule, RouterTestingModule],
      declarations: [InspectionAttachmentsComponent],
      providers: [
        { provide: ToastrService, useValue: mockServices.toastr },
        { provide: ProjectService, useValue: mockServices.project },
        { provide: MixpanelService, useValue: mockServices.mixpanel },
        { provide: DeliveryService, useValue: mockServices.delivery },
        { provide: Socket, useValue: mockServices.socket },
        { provide: AuthService, useValue: mockServices.auth },
      ],
    }).compileComponents();

    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    mixpanelService = TestBed.inject(MixpanelService) as jest.Mocked<MixpanelService>;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    socket = TestBed.inject(Socket) as jest.Mocked<Socket>;
    authService = TestBed.inject(AuthService) as jest.Mocked<AuthService>;
  }));

  afterEach(() => {
    localStorage.clear();
  });

  beforeEach(fakeAsync(() => {
    fixture = TestBed.createComponent(InspectionAttachmentsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    tick(100); // Wait for async operations to complete
  }));

  it('should create', fakeAsync(() => {
    expect(component).toBeTruthy();
    tick();
  }));

  it('should initialize with default values', fakeAsync(() => {
    expect(component.files).toEqual([]);
    expect(component.fileData).toEqual([]);
    expect(component.uploadSubmitted).toBe(false);
    expect(component.deleteUploadedFile).toBe(false);
    expect(component.loader).toBe(false);
    tick();
  }));

  it('should get auth user on init', fakeAsync(() => {
    expect(authService.getUser).toHaveBeenCalled();
    expect(component.currentUser).toEqual(mockUser);
    tick();
  }));

  describe('File handling', () => {
    it('should validate file extensions', fakeAsync(() => {
      expect(component.isValidExtension('jpg')).toBe(true);
      expect(component.isValidExtension('jpeg')).toBe(true);
      expect(component.isValidExtension('png')).toBe(true);
      expect(component.isValidExtension('pdf')).toBe(true);
      expect(component.isValidExtension('doc')).toBe(true);
      expect(component.isValidExtension('exe')).toBe(false);
      tick();
    }));

    it('should get file extension correctly', fakeAsync(() => {
      expect(component.getFileExtension('test.jpg')).toBe('jpg');
      expect(component.getFileExtension('document.pdf')).toBe('pdf');
      expect(component.getFileExtension('image.PNG')).toBe('png');
      tick();
    }));

    it('should handle invalid file extensions', fakeAsync(() => {
      const mockFile = {
        relativePath: 'test.exe',
        fileEntry: { isFile: true } as FileSystemFileEntry,
      } as NgxFileDropEntry;

      component.dropped([mockFile]);
      tick();
      expect(toastrService.error).toHaveBeenCalledWith(
        'Please select a valid file. Supported file format (.jpg,.jpeg,.png,.pdf,.doc)',
        'OOPS!',
      );
    }));
  });

  describe('File upload', () => {
    beforeEach(() => {
      component.formData = new FormData();
      component.formData.append('test.jpg', new Blob(['test']), 'test.jpg');
      component.inspectionRequestId = '456';
      component.ParentCompanyId = '123';
    });

    it('should handle successful file upload', fakeAsync(() => {
      const mockResponse = { message: 'File uploaded successfully' };
      deliveryService.inspectionAttachement.mockReturnValue(of(mockResponse));

      component.uploadData();
      tick();

      expect(deliveryService.inspectionAttachement).toHaveBeenCalledWith(
        {
          InspectionRequestId: '456',
          ParentCompanyId: '123',
        },
        component.formData,
      );
      expect(toastrService.success).toHaveBeenCalledWith('File uploaded successfully', 'SUCCESS!');
      expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith(
        'Attachment added against a inspection Booking',
      );
    }));

    it('should handle upload error', fakeAsync(() => {
      const mockError = { message: 'Upload failed' };
      deliveryService.inspectionAttachement.mockReturnValue(throwError(() => mockError));

      component.uploadData();
      tick();

      expect(toastrService.error).toHaveBeenCalledWith('Upload failed', 'OOPS!');
    }));

    it('should handle file upload error', fakeAsync(() => {
      deliveryService.inspectionAttachement.mockReturnValue(
        throwError(() => new Error('Upload failed'))
      );

      component.uploadData();
      tick();

      expect(deliveryService.inspectionAttachement).toHaveBeenCalled();
      expect(toastrService.error).toHaveBeenCalled();
    }));

    it('should handle file drop events', () => {
      const mockFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
      const mockFileEntry = {
        file: (callback: (file: File) => void) => callback(mockFile),
        name: 'test.pdf',
        isFile: true,
        isDirectory: false
      } as FileSystemFileEntry;

      const mockDropEvent: NgxFileDropEntry[] = [{
        relativePath: 'test.pdf',
        fileEntry: mockFileEntry
      }];

      component.dropped(mockDropEvent);

      // Verify files array is updated
      expect(component.files).toEqual(mockDropEvent);

      // Verify fileData array is updated
      expect(component.fileData.length).toBeGreaterThan(0);

      // The component doesn't directly add to formData in the dropped method,
      // so we shouldn't check formData.has('file') here
    });

    it('should handle non-file entries in dropped files', () => {
      const mockDirectoryEntry = {
        isFile: false,
        isDirectory: true,
        name: 'test-folder'
      } as FileSystemDirectoryEntry;

      const mockDropEvent: NgxFileDropEntry[] = [{
        relativePath: 'test-folder',
        fileEntry: mockDirectoryEntry
      }];

      component.dropped(mockDropEvent);
      // Should not process directory entries
      expect(component.formData.has('file')).toBeFalsy();
    });

    it('should handle fileOver event', () => {
      const event = {};
      component.fileOver(event);
      expect(component).toBeTruthy();
    });

    it('should handle fileLeave event', () => {
      const event = {};
      component.fileLeave(event);
      expect(component).toBeTruthy();
    });
  });

  describe('File removal', () => {
    it('should handle successful file removal', fakeAsync(() => {
      const mockResponse = { message: 'File removed successfully' };
      deliveryService.removeInspectionAttachement.mockReturnValue(of(mockResponse));

      component.removeExistingFile({ id: '123' });
      tick();
      expect(deliveryService.removeInspectionAttachement).toHaveBeenCalledWith({
        id: '123',
        ParentCompanyId: '123',
      });
      expect(toastrService.success).toHaveBeenCalledWith('File removed successfully', 'Success');
    }));

    it('should handle file removal error', fakeAsync(() => {
      const mockError = { message: { statusCode: 400, details: [{ error: 'Removal failed' }] } };
      deliveryService.removeInspectionAttachement.mockReturnValue(throwError(() => mockError));

      component.removeExistingFile({ id: '123' });
      tick();
      expect(toastrService.error).toHaveBeenCalled();
    }));
  });

  describe('Attachment retrieval', () => {
    it('should get attachments successfully', fakeAsync(() => {
      const mockAttachments = {
        data: [
          { id: '1', filename: 'test.jpg' },
          { id: '2', filename: 'doc.pdf' },
        ],
      };
      deliveryService.getInspectionAttachement.mockReturnValue(of(mockAttachments));
      component.ParentCompanyId = '123';

      component.getAttachements();
      tick();

      expect(deliveryService.getInspectionAttachement).toHaveBeenCalledWith({
        InspectionRequestId: '456',
        ParentCompanyId: '123',
      });
      expect(component.fileArray).toEqual(mockAttachments.data);
    }));

  });


  it('should clean up subscriptions on destroy', fakeAsync(() => {
    const unsubscribeSpy = jest.spyOn(component['subscription'], 'unsubscribe');
    component.ngOnDestroy();
    tick();
    expect(unsubscribeSpy).toHaveBeenCalled();
  }));
});
