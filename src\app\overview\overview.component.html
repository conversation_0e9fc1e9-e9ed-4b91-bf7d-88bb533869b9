<form
  name="form"
  class="custom-material-form over-view-form"
  [formGroup]="overViewDetailsForm"
  (ngSubmit)="onSubmit()"
  novalidate
>
  <div class="row" *ngIf="loader == false">
    <div class="col-md-6 mb-2 over-view left-form">
      <div class="form-group">
        <label class="fs14 fw600" for="fsName">First Name</label>
        <input id="lsName"
          type="text"
          class="form-control fs12 material-input"
          placeholder="First Name"
          formControlName="firstName"
          (keypress)="alphaOnly($event)"
        />
        <div class="color-red" *ngIf="submitted && overViewDetailsForm.get('firstName').errors">
          <small *ngIf="overViewDetailsForm.get('firstName').errors.required"
            >*First Name is required.</small
          >
        </div>
      </div>
      <div class="form-group">
        <label class="fs14 fw600" for="lsName">Last Name</label>
        <input id="lsName"
          type="text"
          class="form-control fs12 material-input"
          placeholder="Last Name"
          formControlName="lastName"
          (keypress)="alphaOnly($event)"
        />
        <div class="color-red" *ngIf="submitted && overViewDetailsForm.get('lastName').errors">
          <small *ngIf="overViewDetailsForm.get('lastName').errors.required"
            >*Last Name is required.</small
          >
        </div>
      </div>
      <div class="form-group">
        <label class="fs14 fw600" for="comName" >Company Name</label>
        <input id="comName"
          type="text"
          class="form-control fs12 material-input"
          placeholder="Company Name"
          formControlName="companyName"
          (keypress)="companyNameCheck($event)"
        />
        <div class="color-red" *ngIf="submitted && overViewDetailsForm.get('companyName').errors">
          <small *ngIf="overViewDetailsForm.get('companyName').errors.required"
            >*Company Name is required.</small
          >
        </div>
      </div>
      <div class="form-group">
        <label class="fs14 fw600"  for="workEmail">Work Email</label>
        <input  id="workEmail"
          type="text"
          class="form-control fs12 material-input"
          placeholder="Work Email"
          formControlName="email"
          disabled="disabled"
        />
      </div>
      <div class="form-group">
        <label class="fs14 fw600" for="mobNum">Mobile Number</label>
        <div class="input-group align-items-baseline input-pl-10-overview mt-1">
          <select  id="mobNum"
            name="phonecode"
            formControlName="phoneCode"
            (change)="changeMask($event.target.value)"
            class="custom-select no-arrow member-no-arrow fs12 material-input pb4 shadow-none"
          >
            <option *ngFor="let item of countryCode" [value]="item.countryDialCode">
              {{ item.countryDialCode }}
            </option>
          </select>
          <input
            type="text"
            class="form-control material-input pb4 members-mobilenumber-input fs12"
            name="mobile"
            formControlName="phoneNumber"
            placeholder="Mobile Number*"
            (keypress)="numberOnly($event)"
            mask="{{ phoneMask }}"
          />
        </div>
        <div class="color-red" *ngIf="submitted && overViewDetailsForm.get('phoneCode').errors">
          <small *ngIf="overViewDetailsForm.get('phoneCode').errors.required"
            >Enter Phone code</small
          >
        </div>
        <div class="color-red" *ngIf="submitted && overViewDetailsForm.get('phoneNumber').errors">
          <small *ngIf="overViewDetailsForm.get('phoneNumber').errors.required"
            >*Phone Number is required</small
          >
          <small
            *ngIf="
              !overViewDetailsForm.get('phoneNumber').errors.required &&
              overViewDetailsForm.get('phoneNumber').errors
            "
            >*Enter Valid Phone Number</small
          >
        </div>
      </div>
    </div>
  </div>

  <div class="text-center over-view" *ngIf="loader == false">
    <button
      class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular mb-3 me-3 px-5"
      type="submit"
      [disabled]="formSubmitted && overViewDetailsForm.valid"
    >
      <em
        class="fa fa-spinner"
        aria-hidden="true"
        *ngIf="formSubmitted && overViewDetailsForm.valid"
      ></em
      >Submit
    </button>
  </div>
  <div class="row" *ngIf="loader == true" class="text-center over-view">
    <div class="fs18 fw-bold cairo-regular my-5 text-black">Loading...</div>
  </div>
</form>
