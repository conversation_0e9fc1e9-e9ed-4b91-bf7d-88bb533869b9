<section class="reportsgrid page-section">
  <div class="page-inner-content float-start w-100 pb-4">
    <div class="row pt-md-40px">
      <div class="col-md-3">
        <ul class="list-group list-group-horizontal">
          <li class="list-group-item p0 border-0 bg-transparent">
            <a class="backicon btn btn-orange" routerLink="/reports">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="currentColor"
                class="bi bi-chevron-left"
                viewBox="0 0 16 16"
              >
                <path
                  fill-rule="evenodd"
                  d="M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z"
                />
              </svg>
              Back
            </a>
          </li>
          <li class="list-group-item p0 border-0 bg-transparent">
            <h1 class="fs14 fw-bold ps-2 pt7 mb-0">Deliveries</h1>
          </li>
        </ul>
      </div>
      <div class="top-header calendar-filter col-md-9">
        <div class="buttons--section float-end">
          <a class="float-end topaction--styles" (click)="openModalSave()">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
              <path
                d="M224 256c-35.2 0-64 28.8-64 64c0 35.2 28.8 64 64 64c35.2 0 64-28.8 64-64C288 284.8 259.2 256 224 256zM433.1 129.1l-83.9-83.9C341.1 37.06 328.8 32 316.1 32H64C28.65 32 0 60.65 0 96v320c0 35.35 28.65 64 64 64h320c35.35 0 64-28.65 64-64V163.9C448 151.2 442.9 138.9 433.1 129.1zM128 80h144V160H128V80zM400 416c0 8.836-7.164 16-16 16H64c-8.836 0-16-7.164-16-16V96c0-8.838 7.164-16 16-16h16v104c0 13.25 10.75 24 24 24h192C309.3 208 320 197.3 320 184V83.88l78.25 78.25C399.4 163.2 400 164.8 400 166.3V416z"
              />
            </svg>
            <span class="textalign">Save</span>
          </a>
          <a class="float-end topaction--styles" (click)="openModal(template)">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              fill="currentColor"
              class="bi bi-download"
              viewBox="0 0 16 16"
            >
              <path
                d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"
              />
              <path
                d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z"
              />
            </svg>
            <span class="textalign">Export</span>
          </a>
          <a class="float-end topaction--styles" (click)="openSchedulePopup()">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              fill="currentColor"
              class="bi bi-clock"
              viewBox="0 0 16 16"
            >
              <path
                d="M8 3.5a.5.5 0 0 0-1 0V9a.5.5 0 0 0 .252.434l3.5 2a.5.5 0 0 0 .496-.868L8 8.71V3.5z"
              />
              <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm7-8A7 7 0 1 1 1 8a7 7 0 0 1 14 0z" />
            </svg>
            <span class="textalign">Schedule</span>
          </a>
          <div class="btn-group" dropdown [insideClick]="true">
            <a dropdownToggle class="float-end topaction--styles dropdown-toggle">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                fill="currentColor"
                class="bi bi-pause-fill"
                viewBox="0 0 16 16"
              >
                <path
                  d="M5.5 3.5A1.5 1.5 0 0 1 7 5v6a1.5 1.5 0 0 1-3 0V5a1.5 1.5 0 0 1 1.5-1.5zm5 0A1.5 1.5 0 0 1 12 5v6a1.5 1.5 0 0 1-3 0V5a1.5 1.5 0 0 1 1.5-1.5z"
                />
              </svg>
              <span class="textalign">Edit Columns</span>
            </a>
            <ul
              *dropdownMenu
              class="dropdown-menu dropdown-custom project-information-checkbox dropdown-options"

            >
              <li>
                <div class="form-check ps-0 mb-2">
                  <input
                    class="form-check-input float-end ms-0 mt6"
                    type="checkbox"
                    id="selectedall"
                    name="example1"
                    (change)="selectall('selectedall')"
                  />
                  <label
                    class="form-check-label c-pointer fs12 ps-0 text-black fw700"
                    for="selectedall"
                    >Select All
                  </label>
                </div>
              </li>
              <li>
                <div class="form-check ps-0">
                  <input
                    class="form-check-input float-end ms-0 mt6"
                    type="checkbox"
                    id="id"
                    name="example2"
                    (change)="handleInputChange('id')"
                  />
                  <label class="form-check-label c-pointer fs12 ps-0 text-black fw700" for="id"
                    >ID
                  </label>
                </div>
              </li>
              <li>
                <div class="form-check ps-0">
                  <input
                    class="form-check-input float-end ms-0 mt6"
                    type="checkbox"
                    id="description"
                    name="example2"
                    [checked]="true"
                    (change)="handleInputChange('description')"
                    [disabled]="activeHeaders?.length === 1 && tableHeaders?.description?.isActive"
                  />
                  <label
                    class="form-check-label c-pointer fs12 ps-0 text-black fw700"
                    for="description"
                    >Description
                  </label>
                </div>
              </li>
              <li>
                <div class="form-check ps-0">
                  <input
                    class="form-check-input float-end ms-0 mt6"
                    type="checkbox"
                    id="date"
                    name="example2"
                    [checked]="true"
                    (change)="handleInputChange('date')"
                    [disabled]="activeHeaders?.length === 1 && tableHeaders?.date?.isActive"
                  />
                  <label class="form-check-label c-pointer fs12 ps-0 text-black fw700" for="date"
                    >Date & Time
                  </label>
                </div>
              </li>
              <li>
                <div class="form-check ps-0">
                  <input
                    class="form-check-input float-end ms-0 mt6"
                    type="checkbox"
                    id="status"
                    name="example2"
                    [checked]="true"
                    (change)="handleInputChange('status')"
                    [disabled]="activeHeaders?.length === 1 && tableHeaders?.status?.isActive"
                  />
                  <label class="form-check-label c-pointer fs12 ps-0 text-black fw700" for="status"
                    >Status
                  </label>
                </div>
              </li>
              <li>
                <div class="form-check ps-0">
                  <input
                    class="form-check-input float-end ms-0 mt6"
                    type="checkbox"
                    id="approvedby"
                    name="example2"
                    [checked]="true"
                    (change)="handleInputChange('approvedby')"
                    [disabled]="activeHeaders?.length === 1 && tableHeaders?.approvedby?.isActive"
                  />
                  <label
                    class="form-check-label c-pointer fs12 ps-0 text-black fw700"
                    for="approvedby"
                    >Approved By
                  </label>
                </div>

              </li>
              <li>
                <div class="form-check ps-0">
                  <input
                    class="form-check-input float-end ms-0 mt6"
                    type="checkbox"
                    id="equipment"
                    name="example2"
                    [checked]="false"
                    (change)="handleInputChange('equipment')"
                    [disabled]="activeHeaders?.length === 1 && tableHeaders?.equipment?.isActive"
                  />
                  <label
                    class="form-check-label c-pointer fs12 ps-0 text-black fw700"
                    for="equipment"
                    >Equipment
                  </label>
                </div>

              </li>
              <li>
                <div class="form-check ps-0">
                  <input
                    class="form-check-input float-end ms- mt6"
                    type="checkbox"
                    id="dfow"
                    name="example2"
                    [checked]="false"
                    (change)="handleInputChange('dfow')"
                    [disabled]="activeHeaders?.length === 1 && tableHeaders?.dfow?.isActive"
                  />
                  <label class="form-check-label c-pointer fs12 ps-0 text-black fw700" for="dfow"
                    >Definable Feature of Work
                  </label>
                </div>

              </li>
              <li>
                <div class="form-check ps-0">
                  <input
                    class="form-check-input float-end ms-0 mt6"
                    type="checkbox"
                    id="gate"
                    name="example2"
                    [checked]="false"
                    (change)="handleInputChange('gate')"
                    [disabled]="activeHeaders?.length === 1 && tableHeaders?.gate?.isActive"
                  />
                  <label class="form-check-label c-pointer fs12 text-black fw700" for="gate"
                    >Gate
                  </label>
                </div>

              </li>
              <li>

                <div class="form-check ps-0">
                  <input
                    class="form-check-input float-end ms-0 mt6"
                    type="checkbox"
                    id="company"
                    name="example2"
                    [checked]="false"
                    (change)="handleInputChange('company')"
                    [disabled]="activeHeaders?.length === 1 && tableHeaders?.company?.isActive"
                  />
                  <label class="form-check-label c-pointer fs12 text-black fw700" for="company"
                    >Responsible Company
                  </label>
                </div>

              </li>
              <li>

                <div class="form-check ps-0">
                  <input
                    class="form-check-input float-end mt6"
                    type="checkbox"
                    id="name"
                    name="example2"
                    [checked]="false"
                    (change)="handleInputChange('name')"
                    [disabled]="activeHeaders?.length === 1 && tableHeaders?.name?.isActive"
                  />
                  <label class="form-check-label c-pointer fs12 text-black fw700" for="name"
                    >Responsible Person
                  </label>
                </div>
              </li>
              <li>
                <div class="form-check ps-0">
                  <input
                    class="form-check-input float-end ms-0 mt6"
                    type="checkbox"
                    id="location"
                    name="loc"
                    [checked]="false"
                    (change)="handleInputChange('location')"
                    [disabled]="activeHeaders?.length === 1 && tableHeaders?.location?.isActive"
                  />
                  <label class="form-check-label c-pointer fs12 ps-0 text-black fw700" for="location"
                    >Location
                  </label>
                </div>
              </li>
            </ul>
          </div>
          <a
            class="float-end topaction--styles position-relative"
            [ngClass]="{ 'btn-orange radius30': filterCount > 0 }"
            (click)="openModal4(filter)"
          >
            <img src="../../../assets/images/filterreport.svg" alt="view" class="mx-1" />
            <span class="textalign">Filter</span>
            <span
              class="bg-orange filter-count-report rounded-circle position-absolute text-white filter-count"
              *ngIf="filterCount > 0"
            >
              {{ filterCount }}
            </span>
          </a>
          <div id="fiter-temp3">
            <!--Filter Modal-->
            <ng-template #filter>
              <div class="modal-header py-1">
                <h3 class="fs14 fw-bold cairo-regular color-text7 ms-3 my-0">Filter</h3>
                <button
                  type="button"
                  class="close me-3 px-0 py-2 ms-auto"
                  aria-label="Close"
                  (click)="closeFilterPopup()"
                >
                  <span aria-hidden="true"
                    ><img src="./assets/images/modal-close.svg" alt="Modal Close"
                  /></span>
                </button>
              </div>
              <div class="modal-body">
                <div class="filter-content">
                  <form
                    name="form"
                    class="custom-material-form"
                    id="filter-form2"
                    [formGroup]="filterForm"
                    (ngSubmit)="filterSubmit()"
                    novalidate
                  >
                    <div class="row">
                      <div class="col-md-12">
                        <div class="col-md-6 float-start px-3">
                          <div class="input-group mb-3">
                            <input
                              type="text"
                              class="form-control fs12 material-input"
                              placeholder="ID"
                              formControlName="idFilter"
                              oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1').replace(/^0[^.]/, '0');"
                            />
                            <span class="input-group-text">
                              <img src="./assets/images/search-icon.svg" alt="Search" />
                            </span>
                          </div>
                          <div class="input-group mb-3">
                            <input
                              class="form-control fs12 material-input"
                              bsDaterangepicker
                              formControlName="dateFilter"
                              placeholder="Date Range"
                              [bsConfig]="{
                                isAnimated: true,
                                showWeekNumbers: false,
                                displayMonths: 1,
                                customTodayClass: 'today'
                              }"
                            />
                            <span class="input-group-text">
                              <img src="./assets/images/sidemenu-icons/calendar.svg" alt="Search" />
                            </span>
                          </div>

                          <div class="custom-dropdown company-select equipment-select">
                            <div class="dropdown-header" (click)="toggleDropdown('status')"  (keydown)="handleDownKeydown($event, 'status')">
                              <span [ngClass]="statusPlaceholder !== 'Select Status' ? 'selected-filter' : ''">{{ statusPlaceholder }}</span>
                              <span class="arrow">{{ isStatusDropdownOpen ? '▲' : '▼' }}</span>
                            </div>

                            <div class="dropdown-list" *ngIf="isStatusDropdownOpen">
                              <label *ngFor="let status of statusList" class="dropdown-item">
                                <input
                                  type="checkbox"
                                  [checked]="isSelected(status, 'status')"
                                  (change)="toggleSelection(status, 'status',$event)"
                                />
                                {{ status.name }}
                              </label>
                              <div *ngIf="!statusList.length" class="dropdown-item">
                                No Data
                              </div>
                            </div>
                          </div>
                          <div class="form-group">
                            <div class="custom-dropdown company-select equipment-select">
                              <div class="dropdown-header" (click)="toggleDropdown('define')"  (keydown)="handleDownKeydown($event, 'define')">
                                <span [ngClass]="definePlaceholder !== 'Select DFOW' ? 'selected-filter' : ''">{{ definePlaceholder.length > 15 ?  definePlaceholder.substring(0, 15) + '...' : definePlaceholder}}</span>
                                <span class="arrow">{{ isDefineDropdownOpen ? '▲' : '▼' }}</span>
                              </div>

                              <div class="dropdown-list" *ngIf="isDefineDropdownOpen">
                                <label *ngFor="let define of defineList" class="dropdown-item">
                                  <input
                                    type="checkbox"
                                    [checked]="isSelected(define, 'define')"
                                    (change)="toggleSelection(define, 'define',$event)"
                                  />
                                  {{ define.DFOW }}
                                </label>
                                <div *ngIf="!defineList.length" class="dropdown-item">
                                  No Data
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="form-group">
                            <div class="custom-dropdown company-select equipment-select">
                              <div class="dropdown-header" (click)="toggleDropdown('responsible')"  (keydown)="handleDownKeydown($event, 'responsible')">
                                <span [ngClass]="responsiblePlaceholder !== 'Select Responsible Person' ? 'selected-filter' : ''">{{ responsiblePlaceholder }}</span>
                                <span class="arrow">{{ isResponsibleDropdownOpen ? '▲' : '▼' }}</span>
                              </div>

                              <div class="dropdown-list" *ngIf="isResponsibleDropdownOpen">
                                <label *ngFor="let item of memberList" class="dropdown-item">
                                  <input type="checkbox" formControlName="deineFilter"
                                         [checked]="isSelected(item, 'responsible')"
                                         (change)="toggleSelection(item, 'responsible',$event)" />
                                  {{ item.User.email }}
                                </label>
                                <div *ngIf="!memberList.length" class="dropdown-item">
                                  No Data
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="col-md-6 float-start px-3">
                          <div class="input-group mb-3">
                            <input
                              type="text"
                              class="form-control fs12 material-input"
                              placeholder="Description"
                              formControlName="descriptionFilter"
                            />
                            <span class="input-group-text">
                              <img src="./assets/images/search-icon.svg" alt="Search" />
                            </span>
                          </div>
                          <div class="form-group">
                            <div class="custom-dropdown company-select equipment-select">
                              <div class="dropdown-header" (click)="toggleDropdown('equipment')"  (keydown)="handleDownKeydown($event, 'equipment')">
                                <span [ngClass]="equipmentPlaceholder !== 'Select Equipment' ? 'selected-filter' : ''">{{ equipmentPlaceholder
                                  }}</span>
                                <span class="arrow">{{ isEquipmentDropdownOpen ? '▲' : '▼' }}</span>
                              </div>

                              <div class="dropdown-list" *ngIf="isEquipmentDropdownOpen">
                                <label *ngFor="let equipment of equipmentList" class="dropdown-item">
                                  <input
                                    type="checkbox"
                                    [checked]="isSelected(equipment, 'equipment')"
                                    (change)="toggleSelection(equipment, 'equipment',$event)"
                                  />
                                  {{ equipment.equipmentName }}
                                </label>
                                <div *ngIf="!equipmentList.length" class="dropdown-item">
                                  No Data
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="custom-dropdown company-select equipment-select">
                            <div class="dropdown-header" (click)="toggleDropdown('gate')"  (keydown)="handleDownKeydown($event, 'gate')">
                              <span [ngClass]="gatePlaceholder !== 'Select Gate' ? 'selected-filter' : ''">
                                {{ gatePlaceholder }}
                              </span>
                              <span class="arrow">{{ isGateDropdownOpen ? '▲' : '▼' }}</span>
                            </div>
                            <div class="dropdown-list" *ngIf="isGateDropdownOpen">
                              <label *ngFor="let gate of gateList" class="dropdown-item">
                                <input
                                  type="checkbox"
                                  [checked]="isSelected(gate, 'gate')"
                                  (change)="toggleSelection(gate, 'gate',$event)"
                                />
                                {{ gate.gateName }}
                              </label>
                              <div *ngIf="!gateList.length" class="dropdown-item">
                                No Data
                              </div>
                            </div>
                          </div>

                          <div class="custom-dropdown company-select equipment-select">
                            <div class="dropdown-header" (click)="toggleDropdown('company')"  (keydown)="handleDownKeydown($event, 'company')">
                              <span  [ngClass]="companyPlaceholder !== 'Select Company' ? 'selected-filter' : ''">{{ companyPlaceholder.length > 15 ? companyPlaceholder.substring(0, 15) + '...' : companyPlaceholder }}</span>
                              <span class="arrow">{{ isCompanyDropdownOpen ? '▲' : '▼' }}</span>
                            </div>
                            <div class="dropdown-list" *ngIf="isCompanyDropdownOpen">
                              <label *ngFor="let item of companyList" class="dropdown-item">
                                <input type="checkbox"
                                       [checked]="isSelected(item, 'company')"
                                       (change)="toggleSelection(item, 'company',$event)" />
                                {{ item.companyName }}
                              </label>
                              <div *ngIf="!companyList.length" class="dropdown-item">
                                No Data
                              </div>
                            </div>
                          </div>

                          <div class="custom-dropdown">
                            <div class="dropdown-header" (click)="toggleDropdown('location')" (keydown)="handleDownKeydown($event, 'location')">
                              <span [ngClass]="locationPlaceholder !== 'Select Location' ? 'selected-filter' : ''">
                                {{ locationPlaceholder }}
                              </span>
                              <span class="arrow">{{ isLocationDropdownOpen ? '▲' : '▼' }}</span>
                            </div>

                            <div class="dropdown-list" *ngIf="isLocationDropdownOpen">
                              <input
                                type="text"
                                [value]="searchLocation"
                                placeholder="Search Location..."
                                class="dropdown-search"
                                (input)="changeLocationFilterOptionList($event)"
                              />

                              <!-- Filtered Location List -->
                              <label *ngFor="let item of filteredLocationList" class="dropdown-item">
                                <input
                                  type="checkbox"
                                  [checked]="isSelected(item, 'location')"
                                  (change)="toggleSelection(item, 'location', $event)"
                                />
                                {{ item.locationPath }}
                              </label>
                              <div *ngIf="!filteredLocationList.length" class="dropdown-item">
                                No Data
                              </div>
                            </div>
                          </div>


                        </div>
                      </div>
                      <div class="w-100 text-center">
                        <button
                          class="btn btn-orange text-dark radius20 px-5 mt-2 fs12 fw-bold cairo-regular mx-1"
                          type="button"
                          (click)="resetFilter()"
                        >
                          Reset
                        </button>
                        <button
                          class="btn btn-orange radius20 fs12 px-5 mt-2 fw-bold cairo-regular mx-1"
                          type="submit"
                        >
                          <em class="fa fa-spinner" aria-hidden="true" *ngIf="formSubmitted"></em>
                          Apply
                        </button>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="table-responsive mt-3" id="content">
    <table
      id="exceltable"
      class="table table-custom request-resizable mb-0"
      aria-describedby="deliveries"
    >
      <thead>
        <tr>
          <th *ngIf="tableHeaders.id.isActive === true" scope="col" resizable>
            ID<span>
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('id', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'id', 'ASC')"
                *ngIf="sortColumn !== 'id'"
              />
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('id', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'id', 'ASC')"
                *ngIf="sort === 'DESC' && sortColumn === 'id'"
              />
              <img
                src="./assets/images/up-chevron.svg"
                alt="up-arrow"
                class="h-10px ms-2"
                (click)="sortByField('id', 'DESC')"
                (keydown)="handleToggleKeydown($event, 'id', 'DESC')"
                *ngIf="sort === 'ASC' && sortColumn === 'id'"
              />
            </span>
          </th>
          <th *ngIf="tableHeaders.description.isActive === true" scope="col" resizable>
            Description<span>
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('description', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'description', 'ASC')"
                *ngIf="sortColumn !== 'description'"
              />
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('description', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'description', 'ASC')"
                *ngIf="sort === 'DESC' && sortColumn === 'description'"
              />
              <img
                src="./assets/images/up-chevron.svg"
                alt="up-arrow"
                class="h-10px ms-2"
                (click)="sortByField('description', 'DESC')"
                (keydown)="handleToggleKeydown($event, 'description', 'DESC')"
                *ngIf="sort === 'ASC' && sortColumn === 'description'"
              />
            </span>
          </th>
          <th *ngIf="tableHeaders.date.isActive === true" scope="col" resizable>
            Date & Time
            <span>
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('deliveryStart', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'deliveryStart', 'ASC')"
                *ngIf="sortColumn !== 'deliveryStart'"
              />
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('deliveryStart', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'deliveryStart', 'ASC')"
                *ngIf="sort === 'DESC' && sortColumn === 'deliveryStart'"
              />
              <img
                src="./assets/images/up-chevron.svg"
                alt="up-arrow"
                class="h-10px ms-2"
                (click)="sortByField('deliveryStart', 'DESC')"
                (keydown)="handleToggleKeydown($event, 'deliveryStart', 'DESC')"
                *ngIf="sort === 'ASC' && sortColumn === 'deliveryStart'"
              />
            </span>
          </th>
          <th *ngIf="tableHeaders.status.isActive === true" scope="col" resizable>
            Status<span>
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('status', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'status', 'ASC')"
                *ngIf="sortColumn !== 'status'"
              />
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('status', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'status', 'ASC')"
                *ngIf="sort === 'DESC' && sortColumn === 'status'"
              />
              <img
                src="./assets/images/up-chevron.svg"
                alt="up-arrow"
                class="h-10px ms-2"
                (click)="sortByField('status', 'DESC')"
                (keydown)="handleToggleKeydown($event, 'status', 'DESC')"
                *ngIf="sort === 'ASC' && sortColumn === 'status'"
              />
            </span>
          </th>
          <th *ngIf="tableHeaders.approvedby.isActive === true" scope="col" resizable>
            Approved by<span>
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('approvedUser', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'approvedUser', 'ASC')"
                *ngIf="sortColumn !== 'approvedUser'"
              />
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('approvedUser', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'approvedUser', 'ASC')"
                *ngIf="sort === 'DESC' && sortColumn === 'approvedUser'"
              />
              <img
                src="./assets/images/up-chevron.svg"
                alt="up-arrow"
                class="h-10px ms-2"
                (click)="sortByField('approvedUser', 'DESC')"
                (keydown)="handleToggleKeydown($event, 'approvedUser', 'DESC')"
                *ngIf="sort === 'ASC' && sortColumn === 'approvedUser'"
              />
            </span>
          </th>
          <th *ngIf="tableHeaders.equipment.isActive === true" scope="col" resizable>
            Equipment<span>
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('equipment', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'equipment', 'ASC')"
                *ngIf="sortColumn !== 'equipment'"
              />
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('equipment', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'equipment', 'ASC')"
                *ngIf="sort === 'DESC' && sortColumn === 'equipment'"
              />
              <img
                src="./assets/images/up-chevron.svg"
                alt="up-arrow"
                class="h-10px ms-2"
                (click)="sortByField('equipment', 'DESC')"
                (keydown)="handleToggleKeydown($event, 'equipment', 'DESC')"
                *ngIf="sort === 'ASC' && sortColumn === 'equipment'"
              />
            </span>
          </th>
          <th *ngIf="tableHeaders.dfow.isActive === true" scope="col" resizable>
            Definable Feature of Work<span>
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('dfow', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'dfow', 'ASC')"
                *ngIf="sortColumn !== 'dfow'"
              />
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('dfow', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'dfow', 'ASC')"
                *ngIf="sort === 'DESC' && sortColumn === 'dfow'"
              />
              <img
                src="./assets/images/up-chevron.svg"
                alt="up-arrow"
                class="h-10px ms-2"
                (click)="sortByField('dfow', 'DESC')"
                (keydown)="handleToggleKeydown($event, 'dfow', 'DESC')"
                *ngIf="sort === 'ASC' && sortColumn === 'dfow'"
              />
            </span>
          </th>
          <th *ngIf="tableHeaders.gate.isActive === true" scope="col" resizable>
            Gate<span>
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('gate', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'gate', 'ASC')"
                *ngIf="sortColumn !== 'gate'"
              />
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('gate', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'gate', 'DESC')"
                *ngIf="sort === 'DESC' && sortColumn === 'gate'"
              />
              <img
                src="./assets/images/up-chevron.svg"
                alt="up-arrow"
                class="h-10px ms-2"
                (click)="sortByField('gate', 'DESC')"
                (keydown)="handleToggleKeydown($event, 'gate', 'DESC')"
                *ngIf="sort === 'ASC' && sortColumn === 'gate'"
              />
            </span>
          </th>
          <th *ngIf="tableHeaders.company.isActive === true" scope="col" resizable>
            Responsible Company<span>
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('company', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'company', 'ASC')"
                *ngIf="sortColumn !== 'company'"
              />
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('company', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'company', 'ASC')"
                *ngIf="sort === 'DESC' && sortColumn === 'company'"
              />
              <img
                src="./assets/images/up-chevron.svg"
                alt="up-arrow"
                class="h-10px ms-2"
                (click)="sortByField('company', 'DESC')"
                (keydown)="handleToggleKeydown($event, 'company', 'DESC')"
                *ngIf="sort === 'ASC' && sortColumn === 'company'"
              />
            </span>
          </th>
          <th *ngIf="tableHeaders.name.isActive === true" scope="col" resizable>
            Responsible Person<span>
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('member', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'member', 'ASC')"
                *ngIf="sortColumn !== 'member'"
              />
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('member', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'member', 'ASC')"
                *ngIf="sort === 'DESC' && sortColumn === 'member'"
              />
              <img
                src="./assets/images/up-chevron.svg"
                alt="up-arrow"
                class="h-10px ms-2"
                (click)="sortByField('member', 'DESC')"
                (keydown)="handleToggleKeydown($event, 'member', 'DESC')"
                *ngIf="sort === 'ASC' && sortColumn === 'member'"
              />
            </span>
          </th>
          <th *ngIf="tableHeaders.location.isActive === true" scope="col" resizable>
            Location<span>
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('location', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'location', 'ASC')"
                *ngIf="sortColumn !== 'location'"
              />
              <img
                src="./assets/images/down-chevron.svg"
                alt="down-arrow"
                class="h-10px ms-2"
                (click)="sortByField('location', 'ASC')"
                (keydown)="handleToggleKeydown($event, 'location', 'ASC')"
                *ngIf="sort === 'DESC' && sortColumn === 'location'"
              />
              <img
                src="./assets/images/up-chevron.svg"
                alt="up-arrow"
                class="h-10px ms-2"
                (click)="sortByField('location', 'DESC')"
                (keydown)="handleToggleKeydown($event, 'location', 'DESC')"
                *ngIf="sort === 'ASC' && sortColumn === 'location'"
              />
            </span>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          *ngFor="
            let item of deliveryList
              | paginate
                : {
                    id: 'pagination1',
                    itemsPerPage: pageSize,
                    currentPage: pageNo,
                    totalItems: totalDeliveryRequestCount
                  };
            let i = index
          "
        >
          <td *ngIf="tableHeaders.id.isActive === true">{{ item.DeliveryId }}</td>
          <td class="w-300px" *ngIf="tableHeaders.description.isActive === true">
            {{ item.description }}
          </td>
          <td class="c-pointer" *ngIf="tableHeaders.date.isActive">
            {{ item.deliveryStart | date: 'MM/dd/yyyy hh:mm a' }} - {{ item.deliveryEnd | date: 'hh:mm a' }}
          </td>
          <td class="c-pointer" *ngIf="tableHeaders.status.isActive === true">
            <div class="status-approved" *ngIf="item.status == 'Approved'">
              <span class="approved--icon">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  fill="currentColor"
                  class="bi bi-check2"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M13.854 3.646a.5.5 0 0 1 0 .708l-7 7a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6.5 10.293l6.646-6.647a.5.5 0 0 1 .708 0z"
                  />
                </svg> </span
              >{{ item.status }}
            </div>
            <div class="status-declined" *ngIf="item.status == 'Declined'">
              <span class="approved--icon">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  fill="currentColor"
                  class="bi bi-x"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"
                  />
                </svg> </span
              >{{ item.status }}
            </div>
            <div class="status-pending" *ngIf="item.status == 'Pending'">
              <span class="approved--icon">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  fill="currentColor"
                  class="bi bi-clock"
                  viewBox="0 0 16 16"
                >
                  <path
                    d="M8 3.5a.5.5 0 0 0-1 0V9a.5.5 0 0 0 .252.434l3.5 2a.5.5 0 0 0 .496-.868L8 8.71V3.5z"
                  />
                  <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm7-8A7 7 0 1 1 1 8a7 7 0 0 1 14 0z" />
                </svg> </span
              >{{ item.status }}
            </div>
            <div class="status-expired" *ngIf="item.status == 'Expired'">{{ item.status }}</div>
            <div class="status-delivered" *ngIf="item.status == 'Delivered'">{{ item.status }}</div>
          </td>
          <td
            class="c-pointer"
            *ngIf="
              tableHeaders.approvedby.isActive === true && item.approverDetails?.User?.firstName
            "
          >
            {{ item.approverDetails?.User?.firstName }} {{ item?.approverDetails?.User?.lastName }}
          </td>
          <td
            class="c-pointer"
            *ngIf="
              !item.approverDetails?.User?.firstName && tableHeaders.approvedby.isActive === true
            "
          >
            -
          </td>
          <td
            class="c-pointer"
            *ngIf="
              !item.equipmentDetails ||
              (item.equipmentDetails?.length === 0 && tableHeaders.equipment.isActive === true)
            "
          >
            -
          </td>
          <td
            class="c-pointer"
            *ngIf="tableHeaders.equipment.isActive === true && item.equipmentDetails?.length > 0"
          >
            <p *ngFor="let item1 of item.equipmentDetails">
              {{ item1?.Equipment?.equipmentName }}
            </p>
            <!-- {{ item.equipmentDetails[0]?.Equipment?.equipmentName }} -->
          </td>
          <td
            class="c-pointer"
            *ngIf="
              !item.defineWorkDetails ||
              (item.defineWorkDetails?.length === 0 && tableHeaders.dfow.isActive === true)
            "
          >
            -
          </td>
          <td
            class="c-pointer"
            *ngIf="tableHeaders.dfow.isActive === true && item.defineWorkDetails?.length > 0"
          >
            <div *ngFor="let newitem of item.defineWorkDetails">
              {{ newitem?.DeliverDefineWork.DFOW }}
            </div>
          </td>
          <td
            class="c-pointer"
            *ngIf="
              item &&
              item.gateDetails &&
              item.gateDetails?.length === 0 &&
              tableHeaders.gate.isActive === true
            "
          >
            -
          </td>
          <td
            class="c-pointer"
            *ngIf="
              item &&
              item.gateDetails &&
              item.gateDetails?.length > 0 &&
              tableHeaders.gate.isActive === true
            "
          >
            <div *ngFor="let newitem of item.gateDetails">{{ newitem?.Gate.gateName }}</div>
          </td>
          <td
            class="c-pointer"
            *ngIf="
              !item.companyDetails ||
              (item.companyDetails?.length === 0 && tableHeaders.company.isActive === true)
            "
          >
            -
          </td>
          <td
            class="c-pointer"
            *ngIf="tableHeaders.company.isActive === true && item.companyDetails?.length > 0"
          >
            <div *ngFor="let newitem of item.companyDetails">
              {{ newitem?.Company.companyName }}
            </div>
          </td>
          <td
            class="c-pointer"
            *ngIf="
              !item.memberDetails ||
              (item.memberDetails?.length === 0 && tableHeaders.name.isActive === true)
            "
          >
            -
          </td>
          <td
            class="c-pointer"
            *ngIf="tableHeaders.name.isActive === true && item.memberDetails?.length > 0"
          >
            <div *ngFor="let newitem of item.memberDetails">
              {{ newitem?.Member.User.firstName }} {{ newitem?.Member.User.lastName }}
            </div>
          </td>
          <td
          class="c-pointer"
          *ngIf="
            tableHeaders.location.isActive === true && item.location?.locationPath
          "
        >
          {{ item.location?.locationPath }}
        </td>
        </tr>
      </tbody>
      <tr *ngIf="loader == true">
        <td colspan="7" class="text-center">
          <div class="fs18 fw-bold cairo-regular my-5 text-black">
            <img
              src="../../assets/images/loader-member.gif"
              alt="loader-icon"
              class="loader-icon"
            />
          </div>
        </td>
      </tr>
      <tr *ngIf="loader == false && totalDeliveryRequestCount == 0">
        <td colspan="7" class="text-center">
          <div class="fs18 fw-bold cairo-regular my-5 text-black">No Records Found</div>
        </td>
      </tr>
    </table>
  </div>
  <div class="tab-pagination px-2" *ngIf="loader == false && totalDeliveryRequestCount > 25">
    <div class="row">
      <div class="col-md-3 align-items-center">
        <ul class="list-inline my-3 showentries">
          <li class="list-inline-item">
            <label class="fs12 color-grey4 float-start" for="showEnt">Show entries</label>
          </li>
          <li class="list-inline-item dropdown--counts">
            <select  id="showEnt"
              class="w-auto form-control fs12 color-grey4 form-control-sm"
              (change)="changePageSize($event.target.value)"
              ngModel="{{ pageSize }}"
            >
              <option value="25">25</option>
              <option value="50">50</option>
              <option value="100">100</option>
              <option value="150">150</option>
            </select>
          </li>
        </ul>
      </div>
      <div class="col-md-8 text-center">
        <div class="my-3 position-relative d-inline-block">
          <pagination-controls
            id="pagination1"
            (pageChange)="changePageNo($event)"
            previousLabel=""
            nextLabel=""
          >
          </pagination-controls>
        </div>
      </div>
    </div>
  </div>
</section>

<!--Export Popup Start-->
<ng-template #template>
  <div class="modal-header">
    <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Deliveries Export</h1>
    <button type="button" class="close ms-auto" aria-label="Close" (click)="cancelexport()">
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body">
    <div class="addcalendar-details">
      <form
        name="exportForm"
        class="custom-material-form add-concrete-material-form"
        [formGroup]="exportForm"
      >
        <div class="row">
          <div class="col-md-12">
            <div class="floating-concrete mt-3">
              <div class="form-group floating-label">
                <input  id="reportNam"
                  class="floating-input form-control fs12 px-0"
                  type="text"
                  placeholder=""
                  formControlName="reportName"
                  [(ngModel)]="reportName"
                />
                <div
                  class="color-red fs12"
                  *ngIf="formSubmitted && exportForm.get('reportName').errors"
                >
                  <small *ngIf="exportForm.get('reportName').errors.required"
                    >*Report Name is Required.</small
                  >
                </div>
                <label class="fs12 fw600 m-0 color-grey11 schedule-form-label" for="reportNam"
                  >Report Name
                  <span class="color-red">
                    <sup>*</sup>
                  </span></label
                >
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="form-group pt-0 mt-0">
              <label class="fs12 fw600 m-0 color-grey11" for="outFor"
                >Output format
                <span class="color-red">
                  <sup>*</sup>
                </span></label
              >
              <select id="outFor"
                class="form-control fs12 material-input px-1 color-grey11"
                formControlName="reportType"
                [(ngModel)]="reportType"
              >
                <option value="EXCEL">EXCEL</option>
                <option value="PDF">PDF</option>
                <option value="CSV">CSV</option>
              </select>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
  <div class="modal-footer border-0 justify-content-center add-calendar-footer">
    <div class="mt-0 mb15 text-center">
      <button
        class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3 px-2rem"
        type="button"
        (click)="cancelexport()"
      >
        Cancel
      </button>
      <button
        (click)="export()"
        type="button"
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular me-3 px-2rem iconremove"
        [disabled]="exportSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="exportSubmitted"></em>
        Download
      </button>
    </div>
  </div>
</ng-template>
<!--Export Popup End-->

<!--Send to Popup-->
<ng-template #template3>
  <div class="modal-header">
    <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Average_Time_To_Close.pdf is ready</h1>
    <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef?.hide()">
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body">
    <div class="addcalendar-details">
      <form name="form" class="custom-material-form add-concrete-material-form">
        <div class="row">
          <div class="col-md-12">
            <div class="form-group custom-tag-input">
              <label class="fs12 fw600 color-orange mb-0"  for="reportTo">Send Report To</label>
              <tag-input [ngModel]="['@item']" [onlyFromAutocomplete]="true" id="reportTo">
                <tag-input-dropdown
                  [showDropdownIfEmpty]="true"
                  [focusFirstElement]="true"
                  [identifyBy]="'id'"
                  [placeholder]="'Send Report To'"
                  [secondaryPlaceholder]="'Send Report To'"
                  [onTextChangeDebounce]="500"
                  [displayBy]="'email'"
                  class="custom-tag-dropdown"
                  [autocompleteItems]="autocompleteItemsAsObjects"
                >
                  <ng-template let-item="item" let-index="index">
                    {{ item.email }}
                  </ng-template>
                </tag-input-dropdown>
              </tag-input>
            </div>
          </div>
          <div class="col-md-12">
            <div class="form-group">
              <input
                class="form-control fs12 material-input px-1 color-grey11"
                type="text"
                placeholder="Subject"
              />
            </div>
          </div>
          <div class="col-md-12">
            <div class="form-group">
              <label class="fs12 fw600 color-grey11" for="msg">Message</label>
              <textarea  id="msg"
                class="form-control fs11 radius0 mt-1"
                rows="2"
                formControlName="notes"
              ></textarea>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
  <div class="modal-footer border-0 justify-content-center add-calendar-footer">
    <div class="mt-0 mb15 text-center">
      <button
        class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3 px-2rem"
        type="button"
      >
        Cancel
      </button>
      <div class="btn-group" dropdown>
        <button
          id="button-basic"
          type="button"
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem"
          aria-controls="dropdown-basic"
        >
          Send
        </button>
      </div>
    </div>
  </div>
</ng-template>
