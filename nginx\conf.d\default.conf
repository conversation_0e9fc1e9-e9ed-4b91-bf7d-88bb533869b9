server_tokens off;
add_header X-Frame-Options SAMEORIGIN;
add_header X-Content-Type-Options nosniff;

add_header X-XSS-Protection "1; mode=block";


server {
  listen 8201;

  root /home/<USER>
  index index.html index.htm;
  charset utf-8;
    

  location / {
    try_files $uri $uri/ /index.html;
    # kill cache
      add_header Last-Modified $date_gmt;
      add_header Cache-Control 'no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0';
      if_modified_since off;
      expires off;
      etag off;
  }

  error_page 500 502 503 504 /50x.html;
  location = /50x.html {
    root /usr/share/nginx/html;

  }

 location ~* \.(css|js)$ {
    add_header Cache-Control no-cache;
 }
 
  location ~* \.(css|js|ico|gif|jpeg|jpg|webp|png|svg|eot|otf|woff|woff2|ttf|ogg|json)$ {
    expires 365d;
    access_log off;
    log_not_found off;
    etag off;
    add_header Pragma public;
    add_header Cache-Control "max-age=31536000, no-transform, no-cache, public"; 
    add_header Access-Control-Allow-Origin *;
  }

  location ~ /\. {
    deny all;
  }
}
