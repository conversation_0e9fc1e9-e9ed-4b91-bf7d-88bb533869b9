import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { FullCalenderComponent } from './full-calender.component';

describe('FullCalenderComponent', () => {
  let component: FullCalenderComponent;
  let fixture: ComponentFixture<FullCalenderComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [FullCalenderComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FullCalenderComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize successfully', () => {
    expect(component.ngOnInit).toBeDefined();
  });

  it('should render the Follo logo', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    const logoImg = compiled.querySelector('img.full-logo');
    expect(logoImg).toBeTruthy();
    expect(logoImg?.getAttribute('src')).toBe('/assets/images/logo.svg');
    expect(logoImg?.getAttribute('alt')).toBe('Follo Logo');
  });
});
