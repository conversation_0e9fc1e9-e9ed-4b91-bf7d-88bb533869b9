/* eslint-disable @typescript-eslint/camelcase */
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import mixpanel from 'mixpanel-browser';
import { ApiService } from './api_base/api.service';
import { environment } from '../../environments/environment';

mixpanel.init('a7f3fd49a6dfdbbe81727f3beadcd383');

@Injectable({
  providedIn: 'root',
  })
export class MixpanelService {
  public constructor(private readonly api: ApiService) {
    /**/
  }

  public addMixpanelEvents(event): void {
    if (environment.environmentName === 'prod') {
      const ProjectId = localStorage.getItem('ProjectId');
      const payload = {
        ProjectId,
      };
      if (ProjectId) {
        this.getMemberData(payload).subscribe((response: any): void => {
          if (response) {
            mixpanel.identify(response.data.User.email);
            mixpanel.track(event, {
              Date: new Date().toUTCString(),
              ProjectName: response.data.Project.projectName,
              CompanyName: response.data.Company.companyName,
              Email: response.data.User.email,
              FirstName: response.data.User.firstName,
              LastName: response.data.User.lastName,
              PhoneNumber: response.data.User.phoneNumber,
              PhoneCode: response.data.User.phoneCode,
              Role: response.data.Role.roleName,
            });
          }
        });
      } else {
        mixpanel.track(event, {
          Date: new Date().toUTCString(),
        });
      }
    }
  }

  public getMemberData(payload): Observable<any> {
    return this.api.post('member/get_member_data', payload);
  }

  public guestGetMemberData(payload): Observable<any> {
    return this.api.post('guest_user/get_member_data', payload);
  }

  public updateUserProfile(data): void {
    if (environment.environmentName === 'prod') {
      mixpanel.identify(data.userDetails.email);
      mixpanel.people.set(
        {
          $first_name: data.userDetails.firstName,
          $last_name: data.userDetails.lastName,
          $name: `${data.userDetails.firstName} ${data.userDetails.lastName}`,
          $email: data.userDetails.email,
          $phone: `${data.userDetails.phoneCode} ${data.userDetails.phoneNumber}`,
          $timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          $created: new Date().toUTCString(),
        },
      );
    }
  }

  public createUserProfile(data): void {
    if (environment.environmentName === 'prod') {
      mixpanel.identify(data.basicDetails.email);
      mixpanel.people.set(
        {
          $first_name: data.companyDetails.fullName,
          $last_name: data.companyDetails.lastName,
          $name: `${data.companyDetails.fullName} ${data.companyDetails.lastName}`,
          $email: data.basicDetails.email,
          $phone: `${data.basicDetails.phoneCode} ${data.basicDetails.phoneNumber}`,
          $city: data.companyDetails.city,
          $timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          $created: new Date().toUTCString(),
        },
      );
    }
  }

  public addGuestUserMixpanelEvents(event): void {
    if (environment.environmentName === 'prod') {
      const ProjectId = +window.atob(localStorage.getItem('guestProjectId'));
      const id = +window.atob(localStorage.getItem('guestId'));
      const payload = {
        ProjectId,
        id,
      };
      if (ProjectId) {
        this.guestGetMemberData(payload).subscribe((response: any): void => {
          if (response) {
            mixpanel.identify(response.data.User.email);
            mixpanel.track(event, {
              Date: new Date().toUTCString(),
              ProjectName: response.data.Project.projectName,
              CompanyName: response.data.Company.companyName,
              Email: response.data.User.email,
              FirstName: response.data.User.firstName,
              LastName: response.data.User.lastName,
              PhoneNumber: response.data.User.phoneNumber,
              PhoneCode: response.data.User.phoneCode,
              Role: response.data.Role.roleName,
            });
          }
        });
      } else {
        mixpanel.track(event, {
          Date: new Date().toUTCString(),
        });
      }
    }
  }
}
