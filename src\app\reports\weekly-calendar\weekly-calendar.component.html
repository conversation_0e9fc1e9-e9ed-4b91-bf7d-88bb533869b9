<ngx-loading [show]="loader" [config]="{ backdropBorderRadius: '3px' }"> </ngx-loading>
<section class="reportsgrid page-section">
  <div class="page-inner-content pb-2">
    <div class="top-header my-0">
      <div class="row pt-md-40px">
        <div class="col-md-3">
          <ul class="list-group list-group-horizontal">
            <li class="list-group-item p0 border-0 bg-transparent report-backbtn-header">
              <button
                type="button"
                class="btn btn-orange px-2 py-1 radius20 fs12 fw-bold"
                (click)="redirect('reports')"
              >
                <em class="fa fa-chevron-left me-1"></em> Back
              </button>
            </li>
            <li class="list-group-item p0 border-0 bg-transparent">
              <h1 class="fs14 fw-bold ps-2 pt7 mb-0">Weekly Calendar</h1>
            </li>
          </ul>
        </div>

        <div class="col-md-9">
          <div class="top-filter">
            <ul class="list-group list-group-horizontal justify-content-end">
              <li class="list-group-item p0 border-0 bg-transparent me-2">
                <a
                  class="float-end topaction--styles position-relative mb-d-flex"
                  [ngClass]="{ 'btn-orange radius30': filterCount > 0 }"
                  (click)="openModal(template)"
                >
                  <img src="../../../assets/images/filterreport.svg" alt="view" class="mx-1" />

                  <span class="textalign fw700">Filter</span>
                  <span
                    class="bg-orange filter-count-report rounded-circle position-absolute text-white filter-count"
                    *ngIf="filterCount > 0"
                  >
                    {{ filterCount }}
                  </span>
                </a>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent">
                <button
                  type="button"
                  class="btn btn-transparent-gray px-2 py-1 radius20 fs12 ml20"
                  (click)="openSchedulePopup()"
                >
                  <img
                    src="../../../assets/images/schedule_report.svg"
                    alt="schedule"
                    class="mx-1"
                  />
                  <span class="color-grey7 fw700">Schedule</span>
                </button>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent">
                <button
                  type="button"
                  class="btn btn-transparent-gray px-2 py-1 radius20 fs12 ml20"
                  (click)="openModalExport(templateexport)"
                >
                  <img src="../../../assets/images/download_report.svg" alt="export" class="mx-1" />
                  <span class="color-grey7 fw700">Export</span>
                </button>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent">
                <button
                  type="button"
                  (click)="openModalSave()"
                  class="btn btn-transparent-gray px-2 py-1 radius20 fs12 ml20"
                >
                  <img src="../../../assets/images/save_report.svg" alt="save" class="mx-1" />
                  <span class="color-grey7 fw700">Save</span>
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="delivery-calendar custom-full-calendar weekly-calendar">
      <full-calendar
        [options]="calendarOptions"
        class="primary-full-calendar"
        id="calendar"
        #fullcalendar
      >
      </full-calendar>

      <h2 class="color-grey7 fs12 fw600 mt-3">Legend</h2>
      <div class="timezone-flex flex-wrap flex-md-nowrap">
        <div class="d-flex align-items-center me-3 mb-2 mb-md-0">
          <div class="w10 h10 radius10 bg-green3 me-2"></div>
          <p class="mb-0 color-grey7 fs12 fw600">Approved</p>
        </div>
        <div class="d-flex align-items-center me-3 mb-2 mb-md-0">
          <div class="w10 h10 radius10 bg-orange-light me-2"></div>
          <p class="mb-0 color-grey7 fs12 fw600">In Review</p>
        </div>
        <div class="d-flex align-items-center me-3 mb-2 mb-md-0">
          <div class="w10 h10 radius10 bg-blue6 me-2"></div>
          <p class="mb-0 color-grey7 fs12 fw600">Delivered</p>
        </div>
        <div class="d-flex align-items-center me-3 mb-2 mb-md-0">
          <div class="w10 h10 radius10 bg-red1 me-2"></div>
          <p class="mb-0 color-grey7 fs12 fw600">Rejected</p>
        </div>
        <div class="d-flex align-items-center me-3 mb-2 mb-md-0">
          <div class="w10 h10 radius10 bg-grey14 me-2"></div>
          <p class="mb-0 color-grey7 fs12 fw600">Expired</p>
        </div>
        <div class="d-flex align-items-center me-3 mb-2 mb-md-0">
          <div class="w10 h10 radius10 bg-grayed-out me-2"></div>
          <p class="mb-0 color-grey7 fs12 fw600">Calendar Event</p>
        </div>

        <div class="d-flex align-items-center me-3 mb-2 mb-md-0"></div>

        <div class="d-flex align-items-center mb-2 mb-md-0 flex-wrap flex-md-nowrap ms-5 ml-md-9px">
          <img
            src="./assets/images/sidemenu-icons/delivery.svg"
            class="form-icon w10 h10 me-2"
            alt="allday"
          />
          <p class="mb-0 color-grey7 fs12 fw600">Delivery</p>
        </div>
        <div class="d-flex align-items-center mb-2 mb-md-0 flex-wrap flex-md-nowrap ms-2">
          <img src="./assets/images/cranesvg.svg" class="form-icon w10 h10 me-2" alt="allday" />
          <p class="mb-0 color-grey7 fs12 fw600">Crane</p>
        </div>
        <div class="d-flex align-items-center mb-2 mb-md-0 flex-wrap flex-md-nowrap ms-2">
          <img
            src="./assets/images/sidemenu-icons/concretesvg.svg"
            class="form-icon w10 h10 me-2"
            alt="allday"
          />
          <p class="mb-0 color-grey7 fs12 fw600">Concrete</p>
        </div>
        <div class="d-flex align-items-center mb-2 mb-md-0 flex-wrap flex-md-nowrap ms-2">
          <img
            src="./assets/images/sidemenu-icons/inspection2.png"
            class="form-icon w10 h10 me-2"
            alt="allday"
          />
          <p class="mb-0 color-grey7 fs12 fw600">Inspection</p>
        </div>
        <div class="d-flex align-items-center mb-2 mb-md-0 flex-wrap flex-md-nowrap ms-2">
          <img
            src="./assets/images/sidemenu-icons/calendarsettings.svg"
            class="form-icon w10 h10 me-2"
            alt="allday"
          />
          <p class="mb-0 color-grey7 fs12 fw600">Calender events</p>
        </div>
        <div class="d-flex align-items-center mb-2 mb-md-0 flex-wrap flex-md-nowrap ms-2">
          <img
            src="./assets/images/noun-event-alert.svg"
            class="form-icon w10 h10 me-2"
            alt="allday"
          />
          <p class="mb-0 color-grey7 fs12 fw600">All day calendar event</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!--Export Popup Start-->
<ng-template #templateexport>
  <div class="modal-header">
    <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Weekly Calendar Export</h1>
    <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef?.hide()">
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body">
    <div class="addcalendar-details">
      <form
        name="exportform"
        class="custom-material-form add-concrete-material-form"
        [formGroup]="exportForm"
      >
        <div class="row">
          <div class="col-md-12">
            <div class="floating-concrete mt-3">
              <div class="form-group floating-label">
                <input id="reportName"
                  class="floating-input form-control fs12 px-0"
                  type="text"
                  placeholder=""
                  formControlName="reportName"
                  [(ngModel)]="reportName"
                />
                <div
                  class="color-red fs12"
                  *ngIf="formSubmitted && exportForm.get('reportName').errors"
                >
                  <small *ngIf="exportForm.get('reportName').errors.required"
                    >*Report Name is Required.</small
                  >
                </div>
                <label for="reportName" class="fs12 fw600 m-0 color-grey11 schedule-form-label"
                  >Report Name
                  <span class="color-red">
                    <sup>*</sup>
                  </span></label
                >
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="form-group pt-0 mt-0">
              <label for="outpuFormat" class="fs12 fw600 m-0 color-grey11"
                >Output format
                <span class="color-red">
                  <sup>*</sup>
                </span></label
              >
              <select id="outpuFormat"
                class="form-control fs12 material-input px-1 color-grey11"
                formControlName="reportType"
              >
                <option value="EXCEL">EXCEL</option>
                <option value="PDF">PDF</option>
              </select>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
  <div class="modal-footer border-0 justify-content-center add-calendar-footer">
    <div class="mt-0 mb15 text-center">
      <button
        class="btn btn-grey color-dark-grey radius20 fs12 fw-bold me-3 px-2rem"
        type="button"
        (click)="cancelExport()"
      >
        Cancel
      </button>
      <button
        type="button"
        (click)="export()"
        class="btn btn-orange color-orange radius20 fs12 fw-bold me-3 px-2rem iconremove"
        [disabled]="exportSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="exportSubmitted"></em>
        Download
      </button>
    </div>
  </div>
</ng-template>
<!--Export Popup End-->

<!--Filter Popup Start-->
<ng-template #template>
  <div class="modal-header">
    <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Filter</h1>
    <button type="button" class="close ms-auto" aria-label="Close" (click)="closeIcon()">
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body py-0">
    <div class="report-heatmap-details">
      <form
        name="form"
        class="custom-material-form add-concrete-material-form"
        id="filter-form2"
        [formGroup]="filterForm"
        (ngSubmit)="filterSubmit()"
        novalidate
      >
        <div class="row mx-2">
          <div class="col-md-6 p-0">
            <div class="form-group company-select" id="company-select7">
              <label class="fs12 fw600 mt15" for="bookType">Booking Type </label>
              <ng-multiselect-dropdown id="bookType"
                [placeholder]="'Booking Type'"
                [settings]="newRequestTypeDropdownSettings"
                [data]="defineListData"
                formControlName="templateType"
              >
              </ng-multiselect-dropdown>
            </div>
          </div>
          <div class="col-md-6 py-0 px-3">
            <label class="fs12 fw600 mb-0 mt-3" for="timeRange">Time Range</label>
            <div class="timezone-flex pt-0">
              <div class="input-group mb-0 delivery-time weeklycalendar-time">
                <timepicker to="timeRange"
                  [formControlName]="'startTime'"
                  (keypress)="numberOnly($event)"
                  class="mt-0 Weeklycalendar-picker"
                  (ngModelChange)="changeDate($event)"
                >
                </timepicker>
              </div>
              <div class="color-red"></div>
              <div class="input-group mb-0 delivery-time weeklycalendar-time week-end-time">
                <timepicker
                  [formControlName]="'endTime'"
                  (keypress)="numberOnly($event)"
                  class="Weeklycalendar-picker"
                >
                </timepicker>
              </div>
            </div>
          </div>
        </div>
        <div class="row mx-2">
          <div class="col-md-6 px-0">
            <div class="input-group mt-0">
              <input
                type="text"
                formControlName="dateFilter"
                placeholder="Date Range Picker"
                class="form-control"
                bsDaterangepicker
                [bsConfig]="{
                  isAnimated: true,
                  showWeekNumbers: false,
                  displayMonths: 1,
                  customTodayClass: 'today'
                }"
              />
                <span class="input-group-text">
                  <img
                    src="../../../assets/images/range_icon.svg"
                    class="range-image"
                    alt="range"
                  />
                </span>
            </div>
          </div>
          <div class="col-md-6 padding-0px">
            <div class="form-group mb-3 timezone-formgroup equipment-formgroup">
              <ng-multiselect-dropdown
                [placeholder]="'Equipment'"
                [settings]="equipmentDropdownSettings"
                [data]="equipmentList"
                formControlName="equipmentFilter"
              >
              </ng-multiselect-dropdown>
            </div>
          </div>
        </div>
        <div class="row mx-2">
          <div class="col-md-6 px-0">
            <div class="form-group mb-3 timezone-formgroup equipment-formgroup">
              <ng-multiselect-dropdown
                [placeholder]="'Definable Feature of Work'"
                [settings]="dfowDropdownSettings"
                [data]="defineList"
                formControlName="defineFilter"
              >
              </ng-multiselect-dropdown>
            </div>
          </div>
          <div class="col-md-6 padding-0px">
            <div class="form-group mb-3 timezone-formgroup equipment-formgroup">
              <ng-multiselect-dropdown
                [placeholder]="'Status'"
                [settings]="statusDropdownSettings"
                [data]="wholeStatus"
                formControlName="statusFilter"
              >
              </ng-multiselect-dropdown>
            </div>
          </div>
        </div>

        <div class="row mx-2">
          <div class="col-md-6 px-0 padding-0px">
            <div class="form-group">
              <div class="form-group mb-3 timezone-formgroup equipment-formgroup">
                <ng-multiselect-dropdown
                  [placeholder]="'Responsible Person'"
                  [settings]="responsiblepersonDropdownSettings"
                  [data]="updatedMemberList"
                  formControlName="memberFilter"
                >
                </ng-multiselect-dropdown>
              </div>
            </div>
          </div>
          <div class="col-md-6 padding-0px">
            <div class="form-group mb-3 timezone-formgroup equipment-formgroup">
              <ng-multiselect-dropdown
                [placeholder]="'Responsible Company'"
                [settings]="responsiblecompanyDropdownSettings"
                [data]="companyList"
                formControlName="companyFilter"
              >
              </ng-multiselect-dropdown>
            </div>
          </div>
        </div>
        <div class="row mx-2">
          <div class="col-md-6 px-0">
            <div class="form-group mb-3 timezone-formgroup equipment-formgroup">
              <ng-multiselect-dropdown
                [placeholder]="'Gate'"
                [settings]="gateDropdownSettings"
                [data]="gateList"
                formControlName="gateFilter"
              >
              </ng-multiselect-dropdown>
            </div>
          </div>
          <div class="col-md-6 padding-0px">
            <div class="form-group mb-3 timezone-formgroup equipment-formgroup">
              <ng-multiselect-dropdown
                [placeholder]="'Location'"
                [settings]="locationDropdownSettings"
                [data]="locationList"
                formControlName="locationFilter"
              >
              </ng-multiselect-dropdown>
            </div>
          </div>
        </div>
        <div class="modal-footer border-0 justify-content-center add-calendar-footer">
          <div class="mt-0 mb15 text-center">
            <button
              class="btn btn-orange color-black radius20 fs12 fw-bold me-3 px-2rem iconremove"
              type="button"
              (click)="resetFilter()"
            >
              Reset
            </button>
            <button
              type="submit"
              class="btn btn-orange color-orange radius20 fs12 fw-bold me-3 px-2rem iconremove"
            >
              <em class="fa fa-spinner" aria-hidden="true" *ngIf="formSubmitted"></em>
              Apply
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</ng-template>
<!--Filter Popup Start-->
