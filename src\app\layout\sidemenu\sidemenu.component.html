<aside class="sidemenu-main">
  <div class="side-logo d-none d-md-none d-lg-flex justify-content-center mx-auto mt-4 mb-2">
    <img src="./assets/images/logo.svg" alt="Follo" class="img-fluid" />
  </div>
  <div [ngClass]="{ 'custom-scroll': isCollapsed && isCompanyCollapsed }">
    <ul class="list-group side-list-group text-center" *ngIf="loader">
      Loading..
    </ul>
    <ul class="list-group side-list-group position-relative" *ngIf="!loader">
      <!-- Dropdown Menu -->
      <li class="list-group-item dropdown-parent-list project-list mb-3 py-2" routerLinkActive="active"
        *ngIf="!accountAdmin">
        <p class="fs12 color-grey8 mx-1 mb-1">Company</p>
        <a href="javascript:void(0)" [attr.aria-expanded]="!isCompanyCollapsed" aria-controls="collapseBasic"
          class="position-relative">
          <div class="d-flex justify-content-between align-items-center" *ngIf="!myAccount">
            <p class="menu-txt side-menu-li fw-bold cairo-regular m-0">
              {{ companyList[currentCompanyIndex]?.companyName }}
            </p>
            <img [ngClass]="{ rotateup : !isCompanyCollapsed }" (click)="isCompanyCollapsed = !isCompanyCollapsed"
              *ngIf="companyList.length > 1" src="./assets/images/down-arrow.svg" alt="Down Arrow"
              class="arrow mt-n2" (keydown.enter)="isCompanyCollapsed = !isCompanyCollapsed" />
          </div>
        </a>
      </li>
      <li class="sub-menu menu-txt" [collapse]="isCompanyCollapsed" [isAnimated]="true">
        <ul class="sub-list company-name-lists">
          <div>
            <div *ngFor="let item of companyList; let i = index">
              <li class="color-grey7 fs12 fw600 eye-cursor mb-1" *ngIf="currentCompanyIndex != i"
                (click)="selectCompany(i)" (keydown)="handleDownKeydown($event,i,'select')">
                {{ item.companyName }}
              </li>
            </div>
          </div>
        </ul>
      </li>
      <li class="list-group-item dropdown-parent-list project-list mb-3 py-2" routerLinkActive="active">
        <p class="fs12 color-grey8 mx-1 mb-1" *ngIf="!myAccount">Project</p>
        <p class="fs12 color-grey8 mx-1 mb-1" *ngIf="myAccount">Account Portal</p>
        <a href="javascript:void(0)" [attr.aria-expanded]="!isCollapsed" aria-controls="collapseBasic"
          class="position-relative">
          <div class="d-flex justify-content-between align-items-center" *ngIf="!myAccount">
            <p
              class="menu-txt side-menu-li fw-bold cairo-regular m-0 d-inline-block text-truncate lh15"
              style="max-width: 150px"

            >
              {{ projectList[currentIndex]?.projectName }}
            </p>
            <img [ngClass]="{'rotateup': !isCollapsed && projectList[currentIndex]?.projectName?.length<10}"
              [ngClass]="{'rotateup mb35': !isCollapsed && projectList[currentIndex]?.projectName?.length>10}"
              (click)="changeProjectCollapse()" src="./assets/images/down-arrow.svg" alt="Down Arrow" [ngClass]="{
                'arrow mt-4rem': projectList[currentIndex]?.projectName?.length>10,
                'arrow mt-3rem': projectList[currentIndex]?.projectName?.length<10
              }" (keydown)="handleDownKeydown($event,'','changeproject')" />
          </div>
          <div class="d-flex justify-content-between align-items-center" *ngIf="myAccount">
            <p class="menu-txt side-menu-li fw-bold cairo-regular m-0">My Account</p>
            <img
              [ngClass]="{ rotateup: !isCollapsed }"
              (click)="isCollapsed = !isCollapsed"
              src="./assets/images/down-arrow.svg"
              alt="Down Arrow"
              class="arrow mt-n2" (keydown.enter)="isCompanyCollapsed = !isCompanyCollapsed"
            />
          </div>
        </a>
      </li>
      <li class="sub-menu" [ngClass]="accountAdmin == true ? '' : 'project-list-menu'" [collapse]="isCollapsed"
        [isAnimated]="true">
        <ul class="sub-list">
          <div *ngIf="accountAdmin">
            <li class="color-orange fs12 fw500 eye-cursor mb-1" (click)="myAccountPortal()" (keydown)="handleDownKeydown($event,'','account')">
              <div class="d-flex align-items-center">
                <img
                  src="../../../assets/images/user-circle.svg"
                  alt="user-circle"
                  class="me-2"
                />My Account
              </div>
            </li>
            <li class="project-head">
              <p class="color-grey8 fs12 fw500 m-0">Projects</p>
            </li>
          </div>
          <div class="sub-list-scroll">
            <div *ngFor="let item of projectList; let i = index">
              <li class="color-grey7 fs12 fw600 eye-cursor mb-1 project-name-sublist" (click)="selectProject(i)" (keydown)="handleDownKeydown($event, i ,'selectpro')">
                {{ item.projectName }}
              </li>
            </div>
          </div>
        </ul>
        <button
          class="btn btn-orange color-orange add-pre-btn fs12 w-100 text-left"
          (click)="addProjects()"
          *ngIf="roleId == 2 || accountAdmin"
        >
          <img src="./assets/images/add.svg" alt="Add" class="me-2 h-15px plus" /> Add new project
        </button>
      </li>
      <!-- Dropdown Menu -->
      <li class="list-group-item" routerLinkActive="active" (click)="closeSettingsCollapse()" (keydown)="handleDownKeydown($event,'','close')">
        <a routerLink="/dashboard">
          <span>
            <div class="menu-icon">
              <img src="./assets/images/sidemenu-icons/dashboard.svg" alt="ActiveDashboard" class="active-img" />
              <img src="./assets/images/sidemenu-icons/dashboard.svg" alt="Dashboard" class="inactive-img" />
            </div>
            <span class="menu-txt side-menu-li">Dashboard</span>
          </span>
        </a>
      </li>
      <li class="list-group-item" *ngIf="myAccount" routerLinkActive="active" (click)="closeSettingsCollapse()" (keydown)="handleDownKeydown($event,'','close')">
        <a routerLink="/projects">
          <span>
            <div class="menu-icon">
              <img src="./assets/images/sidemenu-icons/project-icon.svg" alt="ActiveProjects" class="active-img" />
              <img src="./assets/images/sidemenu-icons/project-icon.svg" alt="Projects" class="inactive-img" />
            </div>
            <span class="menu-txt side-menu-li">Projects</span>
          </span>
        </a>
      </li>

      <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
        <a [attr.aria-expanded]="!allCalendarIsOpened" aria-controls="collapseBasic" class="position-relative">
          <span>
            <a routerLink="/all-calender">
            <div class="menu-icon">
              <img src="./assets/images/sidemenu-icons/request-active.svg" alt="ActiveDashboard" class="active-img" />
              <img src="./assets/images/sidemenu-icons/request.svg" alt="Delivery" class="inactive-img" />
            </div>
            <span class="menu-txt side-menu-li settings-width eye-cursor">
              <span class="all-calendar-span"> All Calendars </span>
              <span class="bg-orange rounded-circle sidemenu-count">
              <p class="m-0 text-center fs10 text-white h-20px w-20px d-flex justify-content-center align-items-center"
                *ngIf="selectedTypesCount">
                {{ selectedTypesCount }}
              </p>
            </span>
              </span>
            </a>
          </span>
        </a>
      </li>

      <li [collapse]="allCalendarIsOpened" [isAnimated]="true" (click)="closeSettingsCollapse()" (keydown)="handleDownKeydown($event,'','close')">
        <ul class="p-0">
          <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
            <span class="pl30" style="font-size: 12px;">
              <input class="side-menu-checkbox" type="checkbox" [checked]="true" (change)="onCheckboxClicked('deliveryData', $event.target.checked)">
              <span class="menu-txt side-menu-li">Delivery Calendar</span>
              <img class="add_calender-icon" src="./assets/images/add.svg"
              (click)="openAddNDRModal('delivery')" (keydown)="handleDownKeydown($event,'delivery','open')" alt="Down Arrow"/>
            </span>
          </li>
          <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
            <span class="pl30" style="font-size: 12px;">
              <input class="side-menu-checkbox" type="checkbox" [checked]="true" (change)="onCheckboxClicked('craneData', $event.target.checked)">
              <span class="menu-txt side-menu-li">Crane Calendar</span>
              <img class="add_calender-icon" src="./assets/images/add.svg"
              (click)="openAddNDRModal('crane')" alt="Down Arrow" (keydown)="handleDownKeydown($event,'crane','open')"/>
            </span>
          </li>
          <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
            <span class="pl30" style="font-size: 12px;">
              <input class="side-menu-checkbox" type="checkbox" [checked]="true" (change)="onCheckboxClicked('concreteData', $event.target.checked)">
              <span class="menu-txt side-menu-li">Concrete Calendar</span>
              <img class="add_calender-icon" src="./assets/images/add.svg"
              (click)="openAddNDRModal('concrete')" (keydown)="handleDownKeydown($event,'concrete','open')" alt="Down Arrow"/>
            </span>
          </li>
          <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
            <span class="pl30" style="font-size: 12px;">
              <input class="side-menu-checkbox" type="checkbox" [checked]="true" (change)="onCheckboxClicked('inspectionData', $event.target.checked)">
              <span class="menu-txt side-menu-li">Inspection Calendar</span>
              <img class="add_calender-icon" src="./assets/images/add.svg"
              alt="Down Arrow" (click)="openAddNDRModal('inspection')"  (keydown)="handleDownKeydown($event,'inspection','open')"/>
            </span>
          </li>
        </ul>
      </li>


      <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount" (click)="closeSettingsCollapse()"  (keydown)="handleDownKeydown($event,'','close')">
        <a [attr.aria-expanded]="!allRequestIsOpened" aria-controls="collapseBasic" class="position-relative">
          <span>
            <div class="menu-icon">
              <img src="./assets/images/sidemenu-icons/request-active.svg" alt="ActiveDashboard" class="active-img" />
              <img src="./assets/images/sidemenu-icons/request.svg" alt="Delivery" class="inactive-img" />
            </div>
            <span class="menu-txt side-menu-li settings-width eye-cursor">
              <span (click)="changeRequestCollapse()"  (keydown)="handleDownKeydown($event,'','change')"> All Bookings </span>

              <img
                [ngClass]="{ rotateright: allRequestIsOpened }"
                (click)="changeRequestCollapse()"  (keydown)="handleDownKeydown($event,'','request')"
                src="./assets/images/down-arrow.svg"
                alt="Down Arrow"
                class="arrow float-end eye-cursor"
            /></span>
          </span>
        </a>
      </li>


      <li [collapse]="allRequestIsOpened" [isAnimated]="true" (click)="closeSettingsCollapse()"  (keydown)="handleDownKeydown($event,'','close')">
        <ul class="p-0">
          <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
            <a routerLink="/delivery-request" class="pl30">
              <span>
                <span class="menu-txt side-menu-li">Delivery Bookings</span>
              </span>
            </a>
          </li>
          <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
            <a routerLink="/queued-delivery-request" class="pl30">
              <span>
                <span class="menu-txt side-menu-li">Queued Delivery Bookings</span>
              </span>
            </a>
          </li>
          <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
            <a routerLink="/crane-request" class="pl30">
              <span>
                <span class="menu-txt side-menu-li">Crane Bookings</span>
              </span>
            </a>
          </li>
          <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
            <a routerLink="/concrete-request" class="pl30">
              <span>
                <span class="menu-txt side-menu-li">Concrete Bookings</span>
              </span>
            </a>
          </li>
          <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
            <a routerLink="/inspection-request" class="pl30">
              <span>
                <span class="menu-txt side-menu-li">Inspection Bookings</span>
              </span>
            </a>
          </li>
        </ul>
      </li>

      <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount" (click)="changeAllCarbonCollapse()"  (keydown)="handleDownKeydown($event,'','changeall')">
        <a [attr.aria-expanded]="!allCarbonIsOpened" aria-controls="collapseBasic" class="position-relative">
          <span>
            <div class="menu-icon">
              <img src="./assets/images/sidemenu-icons/carbon.svg" alt="ActiveDashboard" class="active-img" />
              <img src="./assets/images/sidemenu-icons/carbon_unselect.svg" alt="carbon" class="inactive-img" />
            </div>
            <span class="menu-txt side-menu-li settings-width eye-cursor">
              <span> Carbon Tracking </span>
              <img
                [ngClass]="{ rotateright: allCarbonIsOpened }"
                src="./assets/images/down-arrow.svg"
                alt="Down Arrow"
                class="arrow float-end eye-cursor"
            /></span>
          </span>
        </a>
      </li>

      <li [collapse]="allCarbonIsOpened" [isAnimated]="true">
        <ul class="p-0">
          <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
            <a routerLink="/carbon-dashboard" class="pl30">
            <span style="font-size: 12px;">
              <span class="menu-txt side-menu-li">Dashboard</span>
            </span>
            </a>
          </li>
        </ul>
      </li>

      <li [collapse]="allCarbonIsOpened" [isAnimated]="true">
        <ul class="p-0">
          <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
            <a routerLink="/equipment-log" class="pl30">
            <span style="font-size: 12px;">
              <span class="menu-txt side-menu-li">Equipment Log</span>
            </span>
          </a>
          </li>
        </ul>
      </li>

      <li [collapse]="allCarbonIsOpened" [isAnimated]="true">
        <ul class="p-0">
          <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
            <a routerLink="/utilities-log" class="pl30">
            <span style="font-size: 12px;">
              <span class="menu-txt side-menu-li">Utilities</span>
            </span>
          </a>
          </li>
        </ul>
      </li>

      <li [collapse]="allCarbonIsOpened" [isAnimated]="true">
        <ul class="p-0">
          <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
            <a routerLink="/waste-log" class="pl30">
            <span style="font-size: 12px;">
              <span class="menu-txt side-menu-li">Waste Log</span>
            </span>
          </a>
          </li>
        </ul>
      </li>

      <li [collapse]="allCarbonIsOpened" [isAnimated]="true">
        <ul class="p-0">
          <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
            <a routerLink="/hauling-log" class="pl30">
            <span style="font-size: 12px;">
              <span class="menu-txt side-menu-li">Hauling Log</span>
            </span>
          </a>
          </li>
        </ul>
      </li>


      <li class="list-group-item" routerLinkActive="active" (click)="closeSettingsCollapse()" (keydown)="handleDownKeydown($event,'','close')">
        <a routerLink="/notification">
          <span>
            <div class="menu-icon">
              <img src="./assets/images/sidemenu-icons/member-active.svg" alt="ActiveDashboard" class="active-img" />
              <img src="./assets/images/sidemenu-icons/member.svg" alt="Notifications" class="inactive-img" />
            </div>
            <span class="menu-txt side-menu-li w-100">Notifications</span>
            <span class="bg-orange rounded-circle">
              <p class="m-0 text-center fs10 text-white h-20px w-20px d-flex justify-content-center align-items-center"
                *ngIf="unReadCount > 0">
                {{ unReadCount }}
              </p>
            </span>
          </span>
        </a>
      </li>

      <li class="list-group-item" routerLinkActive="active">
        <a routerLink="/reports">
          <span>
            <div class="menu-icon">
              <img src="./assets/images/Group.png" alt="ActiveDashboard" class="active-img" />
              <img src="./assets/images/reporticon-gray.png" alt="Notifications" class="inactive-img" />
            </div>
            <span class="menu-txt side-menu-li">Reports</span>
          </span>
        </a>
      </li>
      <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
        <a [attr.aria-expanded]="!isOpened" aria-controls="collapseBasic" class="position-relative">
          <span>
            <div class="menu-icon">
              <img src="./assets/images/sidemenu-icons/settings-active.svg" (click)="changeSettingsCollapse()" (keydown)="handleDownKeydown($event,'','changeset')"
                alt="ActiveDashboard" class="active-img eye-cursor" />
              <img src="./assets/images/sidemenu-icons/settings.svg" (click)="changeSettingsCollapse()" alt="Settings" (keydown)="handleDownKeydown($event,'','changeset')"
                class="inactive-img" />
            </div>
            <span class="menu-txt side-menu-li settings-width eye-cursor">
              <span (click)="changeSettingsCollapse()" (keydown)="handleDownKeydown($event,'','changeset')"> Settings </span>
              <img
                [ngClass]="{ rotateright: isOpened }"
                (click)="changeSettingsCollapse()"
                src="./assets/images/down-arrow.svg"
                alt="Down Arrow" (keydown)="handleDownKeydown($event,'','changeset')"
                class="arrow float-end eye-cursor"
            /></span>
          </span>
        </a>
      </li>
      <li [collapse]="isOpened" [isAnimated]="true">
        <ul class="p-0">
          <li class="list-group-item" routerLinkActive="active" *ngIf="(roleId == 2 || roleId == 1) && !myAccount">
            <a routerLink="/project-settings" class="pl15">
              <span>
                <div class="menu-icon">
                  <img src="./assets/images/sidemenu-icons/projectsettings-active.svg" alt="ProjectDashboard"
                    class="active-img" />
                  <img src="./assets/images/sidemenu-icons/projectsettings.svg" alt="Delivery" class="inactive-img" />
                </div>
                <span class="menu-txt side-menu-li">Project Settings</span>
              </span>
            </a>
          </li>
          <li class="list-group-item" routerLinkActive="active">
            <a routerLink="/templates" class="pl15">
              <span>
                <div class="menu-icon">
                  <img src="./assets/images/Group.png" alt="ActiveDashboard" class="active-img" />
                  <img src="./assets/images/reporticon-gray.png" alt="Notifications" class="inactive-img" />
                </div>
                <span class="menu-txt side-menu-li">Templates</span>
              </span>
            </a>
          </li>
          <li class="list-group-item" routerLinkActive="active" *ngIf="roleId == 2 && !myAccount">
            <a routerLink="/calendar-settings" class="pl15">
              <span>
                <div class="menu-icon">
                  <img src="./assets/images/sidemenu-icons/calendar-active.svg" alt="ActiveDashboard"
                    class="active-img" />
                  <img src="./assets/images/sidemenu-icons/calendar.svg" alt="calendar" class="inactive-img" />
                </div>
                <span class="menu-txt side-menu-li">Calendar Settings</span>
              </span>
            </a>
          </li>
          <li class="list-group-item" routerLinkActive="active">
            <a routerLink="/notification-settings" class="pl15">
              <span>
                <div class="menu-icon">
                  <img src="./assets/images/sidemenu-icons/notification-active.svg" alt="ActiveDashboard"
                    class="active-img" />
                  <img src="./assets/images/sidemenu-icons/notification.svg" alt="Notifications" class="inactive-img" />
                </div>
                <span class="menu-txt side-menu-li">Notifications Preferences</span>
              </span>
            </a>
          </li>
          <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
            <a routerLink="/companies" class="pl15">
              <span>
                <div class="menu-icon">
                  <img src="./assets/images/sidemenu-icons/company-active.svg" alt="ActiveDashboard"
                    class="active-img" />
                  <img src="./assets/images/sidemenu-icons/company.svg" alt="company" class="inactive-img" />
                </div>
                <span class="menu-txt side-menu-li">Companies</span>
              </span>
            </a>
          </li>
          <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
            <a routerLink="/location" class="pl15">
              <span>
                <div class="menu-icon">
                  <img src="./assets/images/sidemenu-icons/location.svg" alt="Location" class="active-img" />
                  <img src="./assets/images/sidemenu-icons/location-inactive.svg" alt="Location" class="inactive-img" />
                </div>
                <span class="menu-txt side-menu-li">Locations</span>
              </span>
            </a>
          </li>
          <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
            <a routerLink="/members-settings" class="pl15">
              <span>
                <div class="menu-icon">
                  <img src="./assets/images/sidemenu-icons/member-active.svg" alt="ActiveDashboard"
                    class="active-img" />
                  <img src="./assets/images/sidemenu-icons/member.svg" alt="Members" class="inactive-img" />
                </div>
                <span class="menu-txt side-menu-li">Members</span>
              </span>
            </a>
          </li>
          <li class="list-group-item" routerLinkActive="active"
            *ngIf="(roleId == 2 || roleId == 1 || roleId == 3) && !myAccount">
            <a routerLink="/gates" class="pl15">
              <span>
                <div class="menu-icon">
                  <img src="./assets/images/sidemenu-icons/gate-active.svg" alt="ActiveDashboard" class="active-img" />
                  <img src="./assets/images/sidemenu-icons/gate.svg" alt="Gates" class="inactive-img" />
                </div>
                <span class="menu-txt side-menu-li">Gates</span>
              </span>
            </a>
          </li>
          <li class="list-group-item" routerLinkActive="active"
            *ngIf="(roleId == 2 || roleId == 1 || roleId == 3) && !myAccount">
            <a routerLink="/equipments" class="pl15">
              <span>
                <div class="menu-icon">
                  <img src="./assets/images/sidemenu-icons/equipments-active.svg" alt="ActiveDashboard"
                    class="active-img" />
                  <img src="./assets/images/sidemenu-icons/equipments.svg" alt="Dashboard" class="inactive-img" />
                </div>
                <span class="menu-txt side-menu-li">Equipment</span>
              </span>
            </a>
          </li>
          <li class="list-group-item" routerLinkActive="active"
            *ngIf="(roleId == 2 || roleId == 1 || roleId == 3) && !myAccount">
            <a routerLink="/dfow" class="pl15">
              <span>
                <div class="menu-icon">
                  <img src="./assets/images/sidemenu-icons/request-active.svg" alt="ActiveDashboard"
                    class="active-img" />
                  <img src="./assets/images/sidemenu-icons/request.svg" alt="Delivery" class="inactive-img" />
                </div>
                <span class="menu-txt side-menu-li">Definable Feature Of Work</span>
              </span>
            </a>
          </li>
          <li class="list-group-item" routerLinkActive="active" *ngIf="!myAccount">
            <a routerLink="/void-list" class="pl15">
              <span>
                <div class="menu-icon">
                  <img src="./assets/images/sidemenu-icons/request-active.svg" alt="ActiveDashboard"
                    class="active-img" />
                  <img src="./assets/images/sidemenu-icons/request.svg" alt="Delivery" class="inactive-img" />
                </div>
                <span class="menu-txt side-menu-li">Void List</span>
              </span>
            </a>
          </li>
        </ul>
      </li>
    </ul>
  </div>
</aside>

<!--AddNew  Modal-->
<ng-template #addProject>
  <div class="modal-header px-3 py-1 border-0">
    <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body">
    <form class="custom-material-form">
      <div class="row">
        <div class="col-md-12">
          <div class="form-group mb-4">
            <label class="color-orange fs13" for="proname">Enter Your Project Name</label>
            <input type="text" id="proname" class="form-control material-input fs13" placeholder="Project Name" />
          </div>
          <div class="form-group mb-4">
            <label class="color-orange fs13" for="prolocation">Enter Your Project Location</label>
            <select class="custom-select form-control fs13 material-input" name="State" id="prolocation">
              <option value="" disabled selected hidden>Location</option>
            </select>
          </div>
          <button
            class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
            (click)="modalRef.hide()"
          >
            Cancel
          </button>
          <button
            class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
            type="submit"
          >
            Add
          </button>
        </div>
      </div>
    </form>
  </div>
</ng-template>
