
    @font-face {
    font-family: 'Ubuntu Regular';
    font-style: normal;
    font-weight: normal;
    src: local('Ubuntu Regular'), url('Ubuntu-R.woff') format('woff');
    font-display: swap; /* Define how the browser behaves during download */
    }
    

    @font-face {
    font-family: 'Ubuntu Italic';
    font-style: normal;
    font-weight: normal;
    src: local('Ubuntu Italic'), url('Ubuntu-RI.woff') format('woff');
    font-display: swap; /* Define how the browser behaves during download */
    }
    

    @font-face {
    font-family: 'Ubuntu Light';
    font-style: normal;
    font-weight: normal;
    src: local('Ubuntu Light'), url('Ubuntu-L.woff') format('woff');
    font-display: swap; /* Define how the browser behaves during download */
    }
    

    @font-face {
    font-family: 'Ubuntu Light Italic';
    font-style: normal;
    font-weight: normal;
    src: local('Ubuntu Light Italic'), url('Ubuntu-LI.woff') format('woff');
    font-display: swap; /* Define how the browser behaves during download */
    }
    

    @font-face {
    font-family: 'Ubuntu Medium';
    font-style: normal;
    font-weight: normal;
    src: local('Ubuntu Medium'), url('Ubuntu-M.woff') format('woff');
    font-display: swap; /* Define how the browser behaves during download */
    }
    

    @font-face {
    font-family: 'Ubuntu Medium Italic';
    font-style: normal;
    font-weight: normal;
    src: local('Ubuntu Medium Italic'), url('Ubuntu-MI.woff') format('woff');
    font-display: swap; /* Define how the browser behaves during download */
    }
    

    @font-face {
    font-family: 'Ubuntu Bold';
    font-style: normal;
    font-weight: normal;
    src: local('Ubuntu Bold'), url('Ubuntu-B.woff') format('woff');
    font-display: swap; /* Define how the browser behaves during download */
    }
    

    @font-face {
    font-family: 'Ubuntu Bold Italic';
    font-style: normal;
    font-weight: normal;
    src: local('Ubuntu Bold Italic'), url('Ubuntu-BI.woff') format('woff');
    font-display: swap; /* Define how the browser behaves during download */
    }
    

    @font-face {
    font-family: 'Ubuntu Condensed Regular';
    font-style: normal;
    font-weight: normal;
    src: local('Ubuntu Condensed Regular'), url('Ubuntu-C.woff') format('woff');
    font-display: swap; /* Define how the browser behaves during download */
    }