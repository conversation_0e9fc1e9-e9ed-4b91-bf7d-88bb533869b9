/* eslint no-unused-vars: "error" */
/* eslint import/no-unresolved */
import { BrowserModule } from '@angular/platform-browser';
import { APP_INITIALIZER, CUSTOM_ELEMENTS_SCHEMA, Error<PERSON><PERSON><PERSON>, NgModule,NO_ERRORS_SCHEMA} from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { NgxGpAutocompleteModule } from '@angular-magic/ngx-gp-autocomplete';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { Router } from '@angular/router';
import * as Sentry from '@sentry/angular';
import { CommonModule } from '@angular/common';

// Plugins
import { SelectDropDownModule } from 'ngx-select-dropdown';
import { PopoverModule } from 'ngx-bootstrap/popover';
import { FullCalendarModule } from '@fullcalendar/angular';
import { NgxFileDropModule } from 'ngx-file-drop';
import { UserIdleModule } from 'angular-user-idle';
import { NgxLoadingModule } from 'ngx-loading';
import { TagInputModule } from 'ngx-chips';
import { NgxMaskModule } from 'ngx-mask';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { ModalModule, BsModalRef } from 'ngx-bootstrap/modal';
import { CollapseModule } from 'ngx-bootstrap/collapse';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { TimepickerModule } from 'ngx-bootstrap/timepicker';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { PdfViewerModule } from 'ng2-pdf-viewer';
import { ToastrModule } from 'ngx-toastr';
import { NgxPaginationModule } from 'ngx-pagination';
import { UiSwitchModule } from 'ngx-ui-switch';
import { ColorPickerModule } from 'ngx-color-picker';

import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { SocketIoModule, SocketIoConfig } from 'ngx-socket-io';
import { GoogleMapsModule } from '@angular/google-maps';
import { AccordionModule } from 'ngx-bootstrap/accordion';
import { QRCodeModule } from 'angularx-qrcode';
import { Loader } from '@googlemaps/js-api-loader';import { AppComponent } from './app.component';
import { AppRoutingModule } from './app-routing.module';
import { ResizableModule } from './resizable/resizable.module';
import { NgxChartsModule } from '@swimlane/ngx-charts';


// Services
import {
  ApiService,
  AuthService,
  UserService,
  NotificationPreferenceService,

} from './services/index';


// Page Components
import { AppLayoutComponent } from './layout/app-layout/app-layout.component';
import { HeaderComponent } from './layout/header/header.component';
import { Header1Component } from './layout/header1/header1.component';
import { SidemenuComponent } from './layout/sidemenu/sidemenu.component';
import { TimeslotComponent } from './layout/time-slot/time-slot.component';
import { LoginComponent } from './auth/login/login.component';
import { ForgotPasswordComponent } from './auth/forgot-password/forgot-password.component';
import { ResetPasswordComponent } from './auth/reset-password/reset-password.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { ChangePasswordComponent } from './change-password/change-password.component';
import { HomePageComponent } from './home-page/home-page.component';
import { MainLayoutComponent } from './layout/main-layout/main-layout.component';
import { DeliveryRequestsComponent } from './delivery-requests/delivery-requests.component';
import { InspectionRequestComponent } from './inspection-request/inspection-request.component'
import { InspectionDetailsNewComponent } from './inspection-request/inspection_details/inspection-details-new/inspection-details-new.component'
import { CalendarComponent } from './calendar/calendar.component';
import { InspectionCalendarComponent } from './inspection-calender/inspection-calender.component'
import { AllCalendarComponent } from './all-calender/all-calender.component';
import { MembersComponent } from './members/members.component';
import { GatesComponent } from './gates/gates.component';
import { EquipmentsComponent } from './equipments/equipments.component';
import { EquipmentLogComponent } from './carbon-tracking/equipment-log/equipment-log.component';
import { WasteLogComponent } from './carbon-tracking/waste-log/waste-log.component';
import { UtilitiesLogComponent } from './carbon-tracking/utilities/utilities.component';
import { HaulingLogComponent } from './carbon-tracking/hauling-log/hauling-log.component';
import { DetailsComponent } from './delivery-requests/delivery-details/details/details.component';
import { CommentsComponent } from './delivery-requests/delivery-details/comments/comments.component';
import { AttachmentsComponent } from './delivery-requests/delivery-details/attachments/attachments.component';
import { HistoryComponent } from './delivery-requests/delivery-details/history/history.component';
import { InspectionDetailsComponent } from './inspection-request/inspection_details/details/details.component';
import { InspectionCommentsComponent} from './inspection-request/inspection_details/comments/comments.component'
import { InspectionAttachmentsComponent } from './inspection-request/inspection_details/attachments/attachments.component';
import { PayementComponent } from './payement/payement.component';
import { FooterComponent } from './layout/footer/footer.component';
import { ChoosePlansComponent } from './choose-plans/choose-plans.component';
import { ProjectComponent } from './project/project.component';
import { SettingsComponent } from './settings/settings.component';
import { OverrideRequestComponent } from './override-request/override-request.component';
import { CompaniesComponent } from './companies/companies.component';
import { environment } from '../environments/environment';
import { VoidListComponent } from './void-list/void-list.component';
import { ProfileComponent } from './profile/profile.component';
import { OverviewComponent } from './overview/overview.component';
import { PlansAndProjectComponent } from './plans-and-project/plans-and-project.component';
import { DFOWComponent } from './dfow/dfow.component';
import { UpgradewarningComponent } from './upgradewarning/upgradewarning.component';
import { NewDeliveryFormComponent } from './delivery-requests/delivery-details/new-delivery-form/new-delivery-form.component';
import { EditDeliveryFormComponent } from './delivery-requests/delivery-details/edit-delivery-form/edit-delivery-form.component';
import { FilterDeliveryFormComponent } from './delivery-requests/delivery-details/filter-delivery-form/filter-delivery-form.component';
import { NewinspectionFormComponent} from './inspection-request/inspection_details/new-inspection-form/new-inspection-form.component'
import { EditinspectionFormComponent} from './inspection-request/inspection_details/edit-inspection-form/edit-inspection-form.component'
import { FilterinspectionFormComponent } from './inspection-request/inspection_details/filter-inspection-form/filter-inspection-form.component'
import { NotificationComponent } from './notification/notification.component';
import { ProjectAccountAdminComponent } from './project-account-admin/project-account-admin.component';
import { BarChartComponent } from './bar-chart/bar-chart.component';
import { BillingComponent } from './billing/billing.component';
import { DeliveryDetailsNewComponent } from './delivery-requests/delivery-details/delivery-details-new/delivery-details-new.component';
import { CarbonTrackingComponent } from './carbon-tracking/carbon-tracking/carbon-tracking.component';

// Directives
import { NumericOnlyDirective } from './directives/numericonly.directive';
import { BillingsComponent } from './billings/billings.component';
import { MycardsComponent } from './mycards/mycards.component';
import { ProfileHistoryComponent } from './profile-history/profile-history.component';
import { CraneCalendarComponent } from './crane-calendar/crane-calendar.component';
import { NewCraneRequestCreationFormComponent } from './crane-requests/new-crane-request-creation-form/new-crane-request-creation-form.component';
import { EditCraneRequestComponent } from './crane-requests/edit-crane-request/edit-crane-request.component';
import { CraneRequestGridComponent } from './crane-requests/crane-request-grid/crane-request-grid.component';
import { QueuedDeliveryRequestComponent } from './delivery-requests/queued-delivery-request/queued-delivery-request.component';
import { CraneRequestAttachmentComponent } from './crane-requests/crane-request-attachment/crane-request-attachment.component';
import { CraneRequestCommentComponent } from './crane-requests/crane-request-comment/crane-request-comment.component';
import { CraneRequestHistoryComponent } from './crane-requests/crane-request-history/crane-request-history.component';
import { CraneRequestDetailViewHeaderComponent } from './crane-requests/crane-request-detail-view-header/crane-request-detail-view-header.component';
import { CraneRequestDetailViewContentComponent } from './crane-requests/crane-request-detail-view-content/crane-request-detail-view-content.component';
import { CalendarViewComponent } from './calendar-settings/calendar-view/calendar-view.component';
import { AddCalendarEventComponent } from './calendar-settings/add-calendar-event/add-calendar-event.component';
import { EditCalendarEventComponent } from './calendar-settings/edit-calendar-event/edit-calendar-event.component';
import { NotificationSettingsComponent } from './notification-settings/notification-settings.component';
import { ConcreteCalendarComponent } from './concrete-request/concrete-calendar/concrete-calendar.component';
import { ConcreteAttachmentsComponent } from './concrete-request/concrete-attachments/concrete-attachments.component';
import { ConcreteCommentComponent } from './concrete-request/concrete-comment/concrete-comment.component';
import { ConcreteHistoryComponent } from './concrete-request/concrete-history/concrete-history.component';
import {InspectionHistoryComponent} from './inspection-request/inspection_details/history/inspection-history.component'
import { ConcreteRequestsGridComponent } from './concrete-request/concrete-requests-grid/concrete-requests-grid.component';
import { EditConcreteRequestsComponent } from './concrete-request/edit-concrete-requests/edit-concrete-requests.component';
import { AddConcreteRequestComponent } from './concrete-request/add-concrete-request/add-concrete-request.component';
import { ConcreteDetailHeaderComponent } from './concrete-request/concrete-detail-header/concrete-detail-header.component';
import { ConcreteDetailContentComponent } from './concrete-request/concrete-detail-content/concrete-detail-content.component';
import { ReportsComponent } from './reports/reports.component';
import { DeliveriesComponent } from './reports/deliveries/deliveries.component';
import { CraneComponent } from './reports/crane/crane.component';
import { ConcreteComponent } from './reports/concrete/concrete.component';
import { HeatmapComponent } from './reports/heat-map/heat-map.component';
import { WeeklycalendarComponent } from './reports/weekly-calendar/weekly-calendar.component';
import { ScheduledReportsComponent } from './reports/scheduled-reports/scheduled-reports.component';
import { SavedReportsComponent } from './reports/saved-reports/saved-reports.component';
import { RecentReportsComponent } from './reports/recent-reports/recent-reports.component';
import { ProjectSettingsComponent } from './project-settings/project-settings.component';
import { ProjectInformationComponent } from './project-settings/project-information/project-information.component';
import { ProjectSettingsTabComponent } from './project-settings/project-settings-tab/project-settings-tab.component';
import { FullCalendarRenderComponent } from './full-calendar-render/full-calendar-render.component';
import { FullCalenderComponent } from './layout/full-calender/full-calender.component';
import { SchedulerFormComponent } from './reports/scheduler-form/scheduler-form.component';
import { SaveReportFormComponent } from './reports/save-report-form/save-report-form.component';
import { ProjectDetailComponent } from './project-settings/guest-user/project-detail/project-detail.component';
import { SubmitBookComponent } from './project-settings/guest-user/submit-book/submit-book.component';
import { InviteLinkComponent } from './project-settings/guest-user/invite-link/invite-link.component';
import { DeliveryCalendarComponent } from './project-settings/guest-user/delivery-calendar/delivery-calendar.component';
import { EnterDetailsComponent } from './project-settings/guest-user/enter-details/enter-details.component';
import { SidemenuGuestuserComponent } from './project-settings/guest-user/sidemenu-guestuser/sidemenu-guestuser.component';
import { GuestuserCalendarComponent } from './project-settings/guest-user/guestuser-calendar/guestuser-calendar.component';
import { GuestuserCranecalendarComponent } from './project-settings/guest-user/guestuser-cranecalendar/guestuser-cranecalendar.component';
import { GuestuserConcretecalendarComponent } from './project-settings/guest-user/guestuser-concretecalendar/guestuser-concretecalendar.component';
import { LocationSettingsComponent } from './project-settings/location-settings/location-settings.component';
import { LocationComponent } from './location/location.component';
import { GuestConcreteBookingComponent } from './project-settings/guest-user/concrete/guest-concrete-booking/guest-concrete-booking.component';
import { GuestCraneBookingComponent } from './project-settings/guest-user/crane/guest-crane-booking/guest-crane-booking.component';
import { GuestDeliveryBookingComponent } from './project-settings/guest-user/delivery/guest-delivery-booking/guest-delivery-booking.component';
import { EditDeliveryBookingComponent } from './project-settings/guest-user/delivery/edit-delivery-booking/edit-delivery-booking.component';
import { EditCraneBookingComponent } from './project-settings/guest-user/crane/edit-crane-booking/edit-crane-booking.component';
import { EditConcreteBookingComponent } from './project-settings/guest-user/concrete/edit-concrete-booking/edit-concrete-booking.component';
import { GuestDeliveryDetailsComponent } from './project-settings/guest-user/delivery/guest-delivery-details/guest-delivery-details.component';
import { GuestCraneDetailsComponent } from './project-settings/guest-user/crane/guest-crane-details/guest-crane-details.component';
import { GuestConcreteDetailsComponent } from './project-settings/guest-user/concrete/guest-concrete-details/guest-concrete-details.component';
import { MembersSettingsComponent } from './members/members-settings/members-settings.component';
import { GuestmembersComponent } from './members/guestmembers/guestmembers.component';
import { TemplateGridComponent } from './booking-templates/templates-grid/template-grid.component';
import { HomePageMapComponent } from './home-page/home-page-map/home-page-map.component';
import { InspectionComponent } from './reports/inspection/inspection.component';
import { UtilitesDetailsComponent } from './carbon-tracking/utilities/utilites-details/utilites-details.component';
import { AuthInterceptor } from './auth/auth.interceptor';


const config: SocketIoConfig = { url: environment.apiSocketUrl, options: {} };

@NgModule({
  declarations: [
    AppComponent,
    AppLayoutComponent,
    HeaderComponent,
    Header1Component,
    SidemenuComponent,
    TimeslotComponent,
    LoginComponent,
    ForgotPasswordComponent,
    ResetPasswordComponent,
    DashboardComponent,
    ChangePasswordComponent,
    HomePageComponent,
    MainLayoutComponent,
    DeliveryRequestsComponent,
    InspectionRequestComponent,
    CalendarComponent,
    InspectionCalendarComponent,
    AllCalendarComponent,
    MembersComponent,
    GatesComponent,
    EquipmentsComponent,
    EquipmentLogComponent,
    WasteLogComponent,
    UtilitiesLogComponent,
    HaulingLogComponent,
    DetailsComponent,
    InspectionDetailsComponent,
    InspectionCommentsComponent,
    InspectionAttachmentsComponent,
    InspectionHistoryComponent,
    CommentsComponent,
    AttachmentsComponent,
    HistoryComponent,
    PayementComponent,
    FooterComponent,
    ChoosePlansComponent,
    ProjectComponent,
    SettingsComponent,
    OverrideRequestComponent,
    CompaniesComponent,
    VoidListComponent,
    ProfileComponent,
    OverviewComponent,
    PlansAndProjectComponent,
    DFOWComponent,
    UpgradewarningComponent,
    NewDeliveryFormComponent,
    EditDeliveryFormComponent,
    FilterDeliveryFormComponent,
    EditinspectionFormComponent,
    NewinspectionFormComponent,
    FilterinspectionFormComponent,
    NotificationComponent,
    ProjectAccountAdminComponent,
    BarChartComponent,
    BillingComponent,
    DeliveryDetailsNewComponent,
    InspectionDetailsNewComponent,
    NumericOnlyDirective,
    BillingsComponent,
    MycardsComponent,
    ProfileHistoryComponent,
    CraneCalendarComponent,
    NewCraneRequestCreationFormComponent,
    EditCraneRequestComponent,
    CraneRequestGridComponent,
    QueuedDeliveryRequestComponent,
    CraneRequestAttachmentComponent,
    CraneRequestCommentComponent,
    CraneRequestHistoryComponent,
    CraneRequestDetailViewHeaderComponent,
    CraneRequestDetailViewContentComponent,
    CalendarViewComponent,
    AddCalendarEventComponent,
    EditCalendarEventComponent,
    NotificationSettingsComponent,
    ConcreteCalendarComponent,
    ConcreteAttachmentsComponent,
    ConcreteCommentComponent,
    ConcreteHistoryComponent,
    ConcreteRequestsGridComponent,
    EditConcreteRequestsComponent,
    AddConcreteRequestComponent,
    ConcreteDetailHeaderComponent,
    ConcreteDetailContentComponent,
    ReportsComponent,
    DeliveriesComponent,
    CraneComponent,
    ConcreteComponent,
    HeatmapComponent,
    WeeklycalendarComponent,
    ScheduledReportsComponent,
    SavedReportsComponent,
    RecentReportsComponent,
    ProjectSettingsComponent,
    ProjectInformationComponent,
    ProjectSettingsTabComponent,
    FullCalendarRenderComponent,
    FullCalenderComponent,
    SchedulerFormComponent,
    SaveReportFormComponent,
    ProjectDetailComponent,
    SubmitBookComponent,
    InviteLinkComponent,
    DeliveryCalendarComponent,
    EnterDetailsComponent,
    SidemenuGuestuserComponent,
    GuestuserCalendarComponent,
    GuestuserCranecalendarComponent,
    GuestuserConcretecalendarComponent,
    LocationSettingsComponent,
    LocationComponent,
    GuestConcreteBookingComponent,
    GuestCraneBookingComponent,
    GuestDeliveryBookingComponent,
    EditDeliveryBookingComponent,
    EditCraneBookingComponent,
    EditConcreteBookingComponent,
    GuestDeliveryDetailsComponent,
    GuestConcreteDetailsComponent,
    GuestCraneDetailsComponent,
    MembersSettingsComponent,
    GuestmembersComponent,
    TemplateGridComponent,
    HomePageMapComponent,
    InspectionComponent,
    UtilitesDetailsComponent,
    CarbonTrackingComponent
  ],
  imports: [
    CommonModule,
    NgxGpAutocompleteModule,
    BrowserModule,
    AppRoutingModule,
    FormsModule,
    ColorPickerModule,
    ReactiveFormsModule,
    ModalModule.forRoot(),
    PopoverModule.forRoot(),
    BrowserAnimationsModule,
    CollapseModule.forRoot(),
    NgMultiSelectDropDownModule.forRoot(),
    NgxFileDropModule,
    BsDatepickerModule.forRoot(),
    TabsModule.forRoot(),
    PdfViewerModule,
    BsDropdownModule.forRoot(),
    NgxLoadingModule.forRoot({ fullScreenBackdrop: true }),
    SocketIoModule.forRoot(config),
    ToastrModule.forRoot(),
    NgxMaskModule.forRoot(),
    TagInputModule,
    TimepickerModule.forRoot(),
    UserIdleModule.forRoot({ idle: 43200, timeout: 300, ping: 1 }), // 12hours idle
    NgxPaginationModule,
    HttpClientModule,
    UiSwitchModule,
    FullCalendarModule, // register FullCalendar with you app
    GoogleMapsModule,
    TooltipModule.forRoot(),
    SelectDropDownModule,
    ResizableModule,
    AccordionModule.forRoot(),
    QRCodeModule,
    NgxChartsModule
  ],
  exports: [
    CommonModule,
    ColorPickerModule,
  ],
  providers: [
    ApiService,
    AuthService,
    UserService,
    NotificationPreferenceService,
    BsModalRef,
    {
      provide: ErrorHandler,
      useValue: Sentry.createErrorHandler({
        showDialog: true,
      }),
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true,
    },
    {
      provide: Sentry.TraceService,
      deps: [Router],
      useValue: undefined,
    },
    {
      provide: APP_INITIALIZER,
      // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
      useFactory: () => () => {},
      deps: [Sentry.TraceService],
      multi: true,
    },
    {
      provide: Loader,
      useValue: new Loader({
        apiKey: '',
        libraries: ['places'],
      }),
    },
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
  bootstrap: [AppComponent],
})
export class AppModule {}
