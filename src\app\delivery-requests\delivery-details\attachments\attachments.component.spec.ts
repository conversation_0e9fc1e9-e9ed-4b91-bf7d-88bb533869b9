import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { SocketIoModule, SocketIoConfig, Socket } from 'ngx-socket-io';
import { AttachmentsComponent } from './attachments.component';
import { environment } from '../../../../environments/environment';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';
import { AuthService } from '../../../services/auth/auth.service';
import { MixpanelService } from '../../../services/mixpanel.service';
import { of, throwError } from 'rxjs';
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';

const config: SocketIoConfig = { url: environment.apiSocketUrl, options: {} };

describe('AttachmentsComponent', () => {
  let component: AttachmentsComponent;
  let fixture: ComponentFixture<AttachmentsComponent>;
  let toastrService: ToastrService;
  let deliveryService: DeliveryService;
  let projectService: ProjectService;
  let authService: AuthService;
  let mixpanelService: MixpanelService;
  let socket: Socket;

  const mockDeliveryService = {
    DeliveryRequestId: of('123'),
    loginUser: of({ RoleId: 1 }),
    refresh: of(true),
    refresh1: of(true),
    getAttachement: jest.fn(),
    attachement: jest.fn(),
    removeAttachement: jest.fn(),
    updatedHistory1: jest.fn()
  };

  const mockProjectService = {
    ParentCompanyId: of('456')
  };

  const mockAuthService = {
    getUser: jest.fn().mockReturnValue(of({ id: 1, name: 'Test User' })),
  };

  const mockMixpanelService = {
    addMixpanelEvents: jest.fn()
  };

  const mockSocket = {
    emit: jest.fn()
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [AttachmentsComponent],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        HttpClientTestingModule,
        RouterTestingModule,
        ToastrModule.forRoot(),
        SocketIoModule.forRoot(config),
      ],
      providers: [
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: ProjectService, useValue: mockProjectService },
        { provide: AuthService, useValue: mockAuthService },
        { provide: MixpanelService, useValue: mockMixpanelService },
        { provide: Socket, useValue: mockSocket }
      ]
    }).compileComponents();

    toastrService = TestBed.inject(ToastrService);
    deliveryService = TestBed.inject(DeliveryService);
    projectService = TestBed.inject(ProjectService);
    authService = TestBed.inject(AuthService);
    mixpanelService = TestBed.inject(MixpanelService);
    socket = TestBed.inject(Socket);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AttachmentsComponent);
    component = fixture.componentInstance;
    component.getAuthUser();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.files).toEqual([]);
    expect(component.fileData).toEqual([]);
    expect(component.fileArray).toEqual([]);
    expect(component.uploadSubmitted).toBeFalsy();
    expect(component.deleteUploadedFile).toBeFalsy();
    expect(component.loader).toBeFalsy();
  });

  it('should get auth user on initialization', () => {
    const mockUser = { id: 1, name: 'Test User' };
    mockAuthService.getUser.mockReturnValue(of(mockUser));

    component.getAuthUser();

    expect(mockAuthService.getUser).toHaveBeenCalled();
    expect(component.currentUser).toEqual(mockUser);
  });

  it('should validate file extensions correctly', () => {
    expect(component.isValidExtension('jpg')).toBeTruthy();
    expect(component.isValidExtension('jpeg')).toBeTruthy();
    expect(component.isValidExtension('png')).toBeTruthy();
    expect(component.isValidExtension('pdf')).toBeTruthy();
    expect(component.isValidExtension('doc')).toBeTruthy();
    expect(component.isValidExtension('txt')).toBeFalsy();
  });

  it('should handle file drop with valid file', () => {
    const mockFile = {
      relativePath: 'test.jpg',
      fileEntry: {
        name: 'test.jpg',
        isFile: true,
        isDirectory: false,
        file: (callback: (file: File) => void) => {
          callback(new File([''], 'test.jpg', { type: 'image/jpeg' }));
        }
      } as FileSystemFileEntry
    } as NgxFileDropEntry;

    const mockFileReader = {
      readAsDataURL: jest.fn(),
      onload: null,
      result: 'data:image/jpeg;base64,test'
    };

    // Mock FileReader constructor
    Object.defineProperty(window, 'FileReader', {
      writable: true,
      configurable: true,
      value: jest.fn(() => mockFileReader)
    });

    component.dropped([mockFile]);

    expect(component.files).toEqual([mockFile]);
    expect(component.fileData.length).toBeGreaterThan(0);
  });

  it('should handle file drop with invalid file extension', () => {
    const mockFile = {
      relativePath: 'test.txt',
      fileEntry: {
        isFile: true
      }
    } as NgxFileDropEntry;

    const toastrSpy = jest.spyOn(toastrService, 'error');

    component.dropped([mockFile]);

    expect(toastrSpy).toHaveBeenCalledWith(
      'Please select a valid file. Supported file format (.jpg,.jpeg,.png,.pdf,.doc)',
      'OOPS!'
    );
  });

  it('should remove file from fileData', () => {
    component.fileData = [
      [
        { relativePath: 'test.jpg', fileEntry: {} }
      ]
    ];

    component.removeFile(0, 0);

    expect(component.fileData).toEqual([]);
  });

  it('should get attachments successfully', () => {
    const mockAttachments = {
      data: [
        { id: 1, filename: 'test.jpg', attachement: 'url/to/file' }
      ]
    };
    mockDeliveryService.getAttachement.mockReturnValue(of(mockAttachments));

    component.getAttachements();

    expect(mockDeliveryService.getAttachement).toHaveBeenCalled();
    expect(component.fileArray).toEqual(mockAttachments.data);
  });

  it('should handle attachment upload successfully', () => {
    const mockResponse = { message: 'Uploaded Successfully.' };
    mockDeliveryService.attachement.mockReturnValue(of(mockResponse));
    component.formData = new FormData();
    component.formData.append('test', 'test');

    component.uploadData();

    expect(mockDeliveryService.attachement).toHaveBeenCalled();
    expect(component.fileData).toEqual([]);
    expect(component.uploadSubmitted).toBeFalsy();
  });

  it('should handle attachment upload error', () => {
    const mockError = { message: 'Upload failed' };
    mockDeliveryService.attachement.mockReturnValue(throwError(() => mockError));
    const toastrSpy = jest.spyOn(toastrService, 'error');
    component.formData = new FormData();
    component.formData.append('test', 'test');

    component.uploadData();

    expect(toastrSpy).toHaveBeenCalledWith(mockError.message, 'OOPS!');
    expect(component.uploadSubmitted).toBeFalsy();
  });

  it('should remove existing file successfully', () => {
    const mockResponse = { message: 'Attachment Deleted Successfully.' };
    mockDeliveryService.removeAttachement.mockReturnValue(of(mockResponse));
    const mockItem = { id: 1 };
    component.ParentCompanyId = '456';

    component.removeExistingFile(mockItem);

    expect(mockDeliveryService.removeAttachement).toHaveBeenCalledWith({
      id: mockItem.id,
      ParentCompanyId: component.ParentCompanyId
    });
    expect(component.fileData).toEqual([]);
    expect(component.deleteUploadedFile).toBeFalsy();
  });

  it('should handle remove existing file error', () => {
    const mockError = { message: { statusCode: 400, details: [{ message: 'Error' }] } };
    mockDeliveryService.removeAttachement.mockReturnValue(throwError(() => mockError));
    const toastrSpy = jest.spyOn(toastrService, 'error');
    const mockItem = { id: 1 };

    component.removeExistingFile(mockItem);

    expect(toastrSpy).toHaveBeenCalled();
    expect(component.loader).toBeFalsy();
  });

  it('should unsubscribe on destroy', () => {
    const unsubscribeSpy = jest.spyOn(component['subscription'], 'unsubscribe');

    component.ngOnDestroy();

    expect(unsubscribeSpy).toHaveBeenCalled();
  });
});
