import {
  Component, OnInit,
  ViewChild,
} from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { GoogleMap } from '@angular/google-maps';
import { AuthService } from '../services/auth/auth.service';
import { ProjectService } from '../services/profile/project.service';

declare let google: any;

@Component({
  selector: 'app-project',
  templateUrl: './project.component.html',
})
export class ProjectComponent implements OnInit {
  @ViewChild(GoogleMap, { static: false }) map!: GoogleMap;

  public projectDetailsForm: UntypedFormGroup;

  public submitted = false;

  public ProjectId: string;

  public myAccount = false;

  public accountAdmin = false;

  public formExecution = false;

  public ParentCompanyId: any;

  public latitude = 38.897957;

  public longitude = -77.036560;

  public address: any;

  public location: string;

  public geoCoder: { geocode: (arg0: { location: { lat: any; lng: any } }, arg1: (results: any, status: any) => void) => void };

  public zoom: number;

  public userLocationLatitude: number;

  public userLocationLongitude: number;

  public projectLocationLatitude: any;

  public projectLocationLongitude: any;

  public mapOptions: google.maps.MapOptions = {
    center: { lat: this.latitude, lng: this.longitude },
    zoomControl: true,
    mapTypeControl: true,
    streetViewControl: false,
    fullscreenControl: true,
    draggable: true,
    zoom: 18,
    mapTypeId: google.maps.MapTypeId.SATELLITE,
  };

  public marker = {
    position: { lat: this.latitude, lng: this.longitude },
    options: {
      animation: google.maps.Animation.DROP,
      draggable: true,
    },
  };

  public constructor(private readonly router: Router,
    private readonly modalService: BsModalService,
    public bsModalRef: BsModalRef,
    private readonly toastr: ToastrService, private readonly authService: AuthService,
    private readonly projectService: ProjectService,
    private readonly formBuilder: UntypedFormBuilder) {
    this.checkCurrentDomain();
    this.projectService.projectParent.subscribe((response15): void => {
      this.ProjectId = response15.ProjectId;
      this.ParentCompanyId = response15.ParentCompanyId;
    });
    this.projectService.ParentCompanyId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ParentCompanyId = res;
      }
    });
    this.projectForm();
    this.setCurrentLocation();
  }

  // Get Current user's Location Coordinates
  public setCurrentLocation(): void {
    this.geoCoder = new google.maps.Geocoder();
    if ('geolocation' in navigator) {
      navigator.permissions.query({
        name: 'geolocation', // NOSONAR
      }).then((permission): any => {
        if (permission.state === 'granted') {
          navigator.geolocation.getCurrentPosition((position): void => { // NOSONAR
            if (position?.coords?.latitude
               && position?.coords?.longitude) {
              this.latitude = position.coords.latitude;
              this.longitude = position.coords.longitude;
              this.mapForProjectLocationLoader(this.latitude, this.longitude);
            }
          });
        } else {
          this.mapForProjectLocationLoader(this.latitude, this.longitude);
        }
      });
    }
  }

  public checkCurrentDomain(): void {
    this.authService.getUser().subscribe((res): void => {
      if (res.isAccount) {
        this.accountAdmin = true;
        this.projectAccountForm();
      } else {
        this.projectForm();
      }
    });
  }

  public close(): void {
    this.toastr.clear();
    if (!this.accountAdmin) {
      const url = this.router.url.split('/')[1];
      const formProjectValue = this.projectDetailsForm.value.projectName;
      const trimValue = formProjectValue ? formProjectValue.trim() : formProjectValue;
      const trimCondition = (trimValue === '');
      if (url === 'plans' || url === 'payment') {
        if (this.projectDetailsForm.invalid || trimCondition) {
          this.toastr.error('Please Fill the project Details to continue.', 'OOPS!');
        } else {
          this.bsModalRef.hide();
        }
      } else {
        this.bsModalRef.hide();
      }
    } else {
      this.bsModalRef.hide();
    }
  }

  public ngOnInit(): void {
    this.router.events.subscribe((e): void => {
      this.bsModalRef.hide();
    });
    if (!this.checkProject()) {
      this.router.navigate(['/dashboard']);
    }
  }

  public mapForProjectLocationLoader(latitude: any, longitude: any): void {
    this.geoCoder = new google.maps.Geocoder();
    this.currentLocation(latitude, longitude);
  }

  public currentLocation(latitude: string, longitude: string): void {
    const lat = parseFloat(latitude);
    const long = parseFloat(longitude);
    if (navigator.geolocation) {
      this.latitude = lat;
      this.longitude = long;
      this.zoom = 18;
      this.mapOptions.center = {
        lat: this.latitude,
        lng: this.longitude,
      };
      this.marker.position = { lat: this.latitude, lng: this.longitude };
      this.mapOptions.zoom = 18;
      this.getLocationAddress(this.latitude, this.longitude, null);
    }
  }

  public markerDragEnd(event: google.maps.MapMouseEvent) {
    if (event.latLng) {
      this.latitude = event.latLng.lat();
      this.longitude = event.latLng.lng();
      this.mapOptions.center = {
        lat: this.latitude,
        lng: this.longitude,
      };
      this.marker.position = {
        lat: this.latitude,
        lng: this.longitude,
      };
      this.map.panTo(this.marker.position);
      this.getLocationAddress(this.latitude, this.longitude, null);
    }
  }

  public handleProjectAddressLocation(address: { geometry: { location: { lat: () => number; lng: () => number } }; formatted_address: any }): void {
    this.getLocationAddress(address.geometry.location.lat(),
      address.geometry.location.lng(), address.formatted_address);
    this.latitude = address.geometry.location.lat();
    this.longitude = address.geometry.location.lng();
    this.mapOptions.center = {
      lat: this.latitude,
      lng: this.longitude,
    };
    this.marker.position = {
      lat: this.latitude,
      lng: this.longitude,
    };
    this.map.panTo(this.marker.position);
  }

  public getLocationAddress(latitude: number, longitude: number, address: any): void {
    if (address) {
      this.address = address;
      this.projectDetailsForm.get('projectLocation').setValue(this.address);
    } else {
      this.geoCoder.geocode({ location: { lat: latitude, lng: longitude } },
        (results: { formatted_address: any }[], status: string): void => {
          if (status === 'OK') {
            if (results[0]) {
              this.address = results[0].formatted_address;
              this.projectDetailsForm.get('projectLocation').setValue(this.address);
              this.projectDetailsForm.get('projectLocationLatitude').setValue(latitude);
              this.projectDetailsForm.get('projectLocationLongitude').setValue(longitude);
            }
          }
        });
    }
  }

  public alphaNum(event: { which: any; keyCode: any }): boolean {
    const key = (event.which) ? event.which : event.keyCode;
    const firstCondition = (key >= 65 && key <= 90);
    const secondCondition = (key >= 97 && key <= 128);
    const thirdCondition = (key === 32) || (key > 31 && (key < 48 || key > 57));
    if ((key > 31 && (key < 48 || key > 57)) && !firstCondition && !secondCondition
    && !thirdCondition) {
      return false;
    }

    return true;
  }

  public handleProjectAddressChange(address: { formatted_address: string; geometry: { location: { lat: () => number; lng: () => number } } }): void {
    this.location = address.formatted_address;
    this.latitude = address.geometry.location.lat();
    this.longitude = address.geometry.location.lng();
  }

  public checkProject(): boolean {
    const projectId = localStorage.getItem('ProjectId');
    if (projectId === undefined || projectId === null) {
      return false;
    }
    return true;
  }

  public onSubmit(): void {
    this.submitted = true;
    const projectId = localStorage.getItem('ProjectId');
    if (this.projectDetailsForm.invalid) {
      return;
    }
    const { projectName } = this.projectDetailsForm.value;
    if (/^\d+$/.test(projectName)) {
      this.toastr.clear();
      this.toastr.error('Project cannot only be numbers.!');
      this.submitted = false;
      return;
    }
    const regex = /[a-zA-Z]/;
    if (!regex.test(projectName)) {
      this.toastr.clear();
      this.toastr.error('Please enter valid Project Name.!');
      this.submitted = false;
      return;
    }
    const payload = {
      id: this.ProjectId,
      projectName: this.projectDetailsForm.value.projectName,
      ParentCompanyId: this.ParentCompanyId,
    };
    if ((this.ProjectId === null || this.ProjectId === undefined || this.ProjectId === '') && !this.accountAdmin) {
      this.router.navigate(['/dashboard']);
    } else if ((projectId !== undefined && projectId !== null) || this.accountAdmin) {
      if (!this.checkStringEmptyValues(payload)) {
        this.ProjectId = projectId;
        this.projectService.existProject(payload).subscribe({
          next: (response: any): void => {
          if (response) {
            if (this.accountAdmin) {
              this.createAccountProject();
            } else {
              this.projectDetailsForm.get('ParentCompanyId').setValue(this.ParentCompanyId);
              localStorage.setItem('newproject', JSON.stringify(this.projectDetailsForm.value));
              if (this.router.url.split('/')[1] === 'plans') {
                this.bsModalRef.hide();
              } else {
                this.router.navigate(['/plans']);
              }
            }
          }
          },
          error: (existProjectErr): void => {
          if (existProjectErr.message?.statusCode === 400) {
            this.showError(existProjectErr);
          } else if (!existProjectErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(existProjectErr.message, 'OOPS!');
          }
          this.submitted = false;
          },
        });
      } else {
        this.toastr.error('Please Enter valid Project Name.', 'OOPS!');
        this.removeLocal();
        this.submitted = false;
      }
    } else {
      this.removeLocal();
    }
  }

  public createAccountProject(): void {
    const data = this.projectDetailsForm.value;
    data.ParentCompanyId = this.ParentCompanyId;
    const startDate = new Date(data.startDate).getTime();
    const endDate = new Date(data.endDate).getTime();
    if (startDate < endDate) {
      this.projectService.createAccountProject(data).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message);
            this.bsModalRef.hide();
            this.submitted = false;
            this.projectService.refreshProjectStatus(true);
          }
        },
        error: (createAccountProjectErr): void => {
          this.submitted = false;
          if (createAccountProjectErr.message?.statusCode === 400) {
            this.showError(createAccountProjectErr);
          } else if (!createAccountProjectErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(createAccountProjectErr.message, 'OOPS!');
          }
          this.submitted = false;
        },
      });
    } else {
      this.toastr.error('End Date must be higher than Start Date.', 'OOPS!');
      this.submitted = false;
    }
  }

  public showError(err: { message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] } }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.toastr.error(errorMessage);
  }

  public removeLocal(): void {
    localStorage.removeItem('newproject');
    const url = this.router.url.split('/')[1];
    if (url === 'plans' || url === 'payment') {
      this.router.navigate(['/dashboard']);
    }
  }

  public redirect(path: any): void {
    this.router.navigate([`/${path}`]);
    this.bsModalRef.hide();
  }

  public checkStringEmptyValues(formValue: { id?: any; projectName: any; ParentCompanyId?: any }): boolean {
    if (formValue.projectName.trim() === '') {
      return true;
    }
    return false;
  }

  public projectAccountForm(): void {
    this.projectDetailsForm = this.formBuilder.group(
      {
        projectName: [
          '',
          Validators.compose([
            Validators.required,
          ]),
        ],
        projectLocation: [
          '',
          Validators.compose([
            Validators.required,
          ]),
        ],
        startDate: [
          '',
          Validators.compose([
            Validators.required,
          ]),
        ],
        endDate: [
          '',
          Validators.compose([
            Validators.required,
          ]),
        ],
        ParentCompanyId: [
          '',
        ],
      },
    );
    this.formExecution = true;
  }

  public projectForm(): void {
    this.projectDetailsForm = this.formBuilder.group(
      {
        projectName: [
          '',
          Validators.compose([
            Validators.required,
          ]),
        ],
        projectLocation: [
          '',
          Validators.compose([
            Validators.required,
          ]),
        ],
        projectLocationLatitude: [
          '',
        ],
        projectLocationLongitude: [
          '',
        ],
        ParentCompanyId: [
          '',
        ],
      },
    );
    const getProjectDetails = localStorage.getItem('newproject');
    const projectDetailsInfo = JSON.parse(getProjectDetails);
    if (projectDetailsInfo !== undefined && projectDetailsInfo !== null) {
      this.projectDetailsForm.get('projectName').setValue(projectDetailsInfo.projectName);
      this.projectDetailsForm.get('projectLocation').setValue(projectDetailsInfo.projectLocation);
    }
    this.formExecution = true;
  }
}
