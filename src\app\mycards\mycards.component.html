<div class="row payment-card-layout mt-5">
    <div class="col-xl-4 col-md-6 mb-3">
        <div class="payment-card br-10 py-3 px-4" style="background: url('../../assets/images/payment-bg.svg') no-repeat;background-size: cover;">
            <img src="./assets/images/delete-white.svg" alt="delete-group" class="delete-card c-pointer">
            <img src="./assets/images/visa.svg" alt="card" class="mb-3 master-card">
            <p class="fs13 text-white">4125-XXXXXXXX-2363</p>
            <div class="d-flex">
                <p class="fs11 text-white fw600 me-2">Debit Card</p><span class="border-left-white"></span>
                <p class="fs11 text-white  fw600 me-2 ms-1">VALID TILL 06/24</p>
            </div>
            <div class="d-flex">
               <p class="fs11 text-white fw600">John doe</p>
               <em class="fa fa-ellipsis-h text-white c-pointer ms-auto" ></em>
            </div>
            <div class="d-flex justify-content-between">
                <div class="btn-group card-custom-dropdown" dropdown>
                    <button dropdownToggle type="button" class="btn btn-primary dropdown-toggle fw600">
                            Primary <em class="fas fa-chevron-down arrow-bottom"></em>
                    </button>
                    <ul *dropdownMenu class="dropdown-menu"
                       aria-labelledby="button-basic">
                      <li><a class="dropdown-item" href="#">Primary</a></li>
                      <li><a class="dropdown-item" href="#">Secondary</a></li>
                    </ul>
                </div>
               <button class="btn btn-dark-grey text-white radius20 py-1 px-2 fs8 fw600">View CVV</button>
            </div>
        </div>
    </div>
    <div class="col-xl-4 col-md-6 mb-3">
      <div class="add-new-card d-flex justify-conntent-center align-items-center c-pointer" (click)="openCardModal(cardModel)" (keydown)="handleToggleKeydown($event, cardModel)">
          <div class="mx-auto text-center">
              <img src="../assets/images/pay-icon.png" class="add-pay-icon" alt="pay">
              <p class="fs12 color-grey7 mt-1">Add Card</p>
          </div>
      </div>
    </div>
</div>

<ng-template #cardModel>
<div class="modal-header d-flex align-items-center">
    <h3 class="fs12 fw-bold cairo-regular color-text7 m-0">
        <img src="../assets/images/debit-card.svg" alt="card" class="me-1 h12"> Add New Card</h3>
    <button type="button" class="close py-1" aria-label="Close" (click)="modalRef.hide()">
        <img src="./assets/images/modal-close.svg" class="w10 h10" alt="Modal Close">
    </button>
    </div>
    <div class="modal-body">
          <form name="form"  novalidate>
            <div class="row">
               <div class="col-md-12">
                  <div class="form-group">
                     <label class="fs14 color-grey23 mb-0" for="name">Name on card</label>
                     <input class="form-control" id="name" placeholder="" >
                  </div>
               </div>
            </div>
            <div class="row">
               <div class="col-md-6 pe-md-1">
                  <div class="form-group">
                     <label class="fs14 color-grey23 mb-0" for="cardnumber">Card number</label>
                     <input class="form-control" placeholder="" id="cardnumber">
                  </div>
               </div>
               <div class="col-md-6">
                  <div class="row">
                     <div class="col-md-8 col-8">
                        <label class="fs14 color-grey23 mb-0" for="expiry">Expiry date (MM YYYY)</label>
                        <div class="row">
                           <div class="col-md-6 col-6 pe-1">
                              <div class="form-group">
                                 <select class="custom-select month pe-0"  id="expiry"
                                 required>
                                 <option selected hidden value="">MM</option>
                               </select>
                              </div>
                           </div>
                           <div class="col-md-6 col-6 pe-1">
                              <div class="form-group">
                                 <select class="custom-select year pe-0"
                                 required>
                                 <option selected hidden value="">YYYY</option>
                               </select>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="col-md-4 col-4">
                        <label class="fs14 color-grey23 mb-0"  for="cvv">(CVC/CVV)</label>
                        <div class="form-group">
                           <input class="form-control" placeholder="" formControlName='cvc' maxlength="3"  id="cvv">
                        </div>
                     </div>
                  </div>
            </div>
            </div>
            <div class="row">
               <div class="col-md-6 pe-md-1">
                  <div class="form-group">
                     <label class="fs14 color-grey23 mb-0"  for="country">Country</label>
                     <select class="custom-select form-control material-input fs13 color-grey23"  id="country">
                        <option value="" disabled selected hidden>Country</option>
                    </select>
                  </div>
               </div>
               <div class="col-md-6">
                  <div class="form-group">
                     <label class="fs14 color-grey23 mb-0"  for="zipcode">Zip code</label>
                     <input class="form-control" placeholder=""  id="zipcode">
                  </div>
               </div>
            </div>
            <div class="my-3 text-center">
               <a class="btn btn-grey-light color-dark-grey radius20 fs12 me-4  mt-2 fw-bold cairo-regular px-2rem" (click)="modalRef.hide()">Cancel</a>
               <button class="btn btn-orange radius20 fs12  mt-2 fw-bold cairo-regular px-5" type="submit" (click)="modalRef.hide()"> Add</button>
            </div>
         </form>
         </div>
</ng-template>
