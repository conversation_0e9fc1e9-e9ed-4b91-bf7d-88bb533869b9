import { ComponentFixture, TestBed } from '@angular/core/testing';
import { GuestmembersComponent } from './guestmembers.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Title } from '@angular/platform-browser';
import { UntypedFormBuilder, ReactiveFormsModule, FormsModule, NgControl, ControlValueAccessor } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { ProjectSharingService } from '../../services/projectSharingService/project-sharing.service';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { of, throwError } from 'rxjs';

class MockNgControl extends NgControl {
  viewToModelUpdate() {}
  get control() { return null; }
}

describe('GuestmembersComponent', () => {
  let component: GuestmembersComponent;
  let fixture: ComponentFixture<GuestmembersComponent>;
  let modalServiceSpy: jest.Mocked<BsModalService>;
  let titleServiceSpy: jest.Mocked<Title>;
  let projectServiceSpy: jest.Mocked<ProjectService>;
  let deliveryServiceSpy: jest.Mocked<DeliveryService>;
  let projectSharingServiceSpy: jest.Mocked<ProjectSharingService>;
  let toastrServiceSpy: jest.Mocked<ToastrService>;

  beforeEach(async () => {
    const modalSpy = {
      show: jest.fn().mockReturnValue({
        hide: jest.fn()
      })
    };
    const titleSpy = {
      setTitle: jest.fn()
    };
    const projectSpy = {
      listGuestMembers: jest.fn().mockReturnValue(of({
        data: {
          rows: [],
          count: 0
        }
      })),
      listCompany: jest.fn().mockReturnValue(of({
        data: { rows: [] },
        parentCompany: [{ id: 1, companyName: 'Parent Company' }]
      })),
      projectParent: of({ ProjectId: 1, ParentCompanyId: 1 }),
      userData: of({ RoleId: 2 }),
      getRoles: jest.fn().mockReturnValue(of({ data: [] }))
    };
    const deliverySpy = {};
    const projectSharingSpy = {
      addGuestAsMember: jest.fn().mockReturnValue(of({ success: true })),
      rejectGuestRequest: jest.fn().mockReturnValue(of({ success: true }))
    };
    const toastrSpy = {
      success: jest.fn(),
      error: jest.fn()
    };

    await TestBed.configureTestingModule({
      declarations: [ GuestmembersComponent ],
      imports: [ 
        ReactiveFormsModule,
        FormsModule
      ],
      providers: [
        { provide: BsModalService, useValue: modalSpy },
        { provide: Title, useValue: titleSpy },
        { provide: ProjectService, useValue: projectSpy },
        { provide: DeliveryService, useValue: deliverySpy },
        { provide: ProjectSharingService, useValue: projectSharingSpy },
        { provide: ToastrService, useValue: toastrSpy },
        UntypedFormBuilder,
        { provide: NgControl, useClass: MockNgControl }
      ]
    })
    .compileComponents();

    modalServiceSpy = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    titleServiceSpy = TestBed.inject(Title) as jest.Mocked<Title>;
    projectServiceSpy = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    deliveryServiceSpy = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    projectSharingServiceSpy = TestBed.inject(ProjectSharingService) as jest.Mocked<ProjectSharingService>;
    toastrServiceSpy = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(GuestmembersComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.showSearchbar).toBe(false);
    expect(component.filterCount).toBe(0);
    expect(component.pageSize).toBe(25);
    expect(component.pageNo).toBe(1);
    expect(component.currentPageNo).toBe(1);
    expect(component.loader).toBe(true);
  });

  it('should set title on initialization', () => {
    expect(titleServiceSpy.setTitle).toHaveBeenCalledWith('Follo - Members');
  });

  it('should get members when project data is available', () => {
    const mockResponse = {
      data: {
        rows: [{ id: 1, name: 'Test Member' }],
        count: 1
      }
    };
    projectServiceSpy.listGuestMembers.mockReturnValue(of(mockResponse));

    component.getMembers();

    expect(projectServiceSpy.listGuestMembers).toHaveBeenCalled();
    expect(component.memberList).toEqual(mockResponse.data.rows);
    expect(component.totalCount).toBe(mockResponse.data.count);
    expect(component.loader).toBe(false);
  });

  it('should handle filter submission', () => {
    const mockResponse = {
      data: {
        rows: [{ id: 1, name: 'Filtered Member' }],
        count: 1
      }
    };
    projectServiceSpy.listGuestMembers.mockReturnValue(of(mockResponse));

    component.filterForm.patchValue({
      companyFilter: 'Test Company',
      nameFilter: 'Test Name',
      statusFilter: 'Pending'
    });

    component.modalRef = { hide: jest.fn() } as any;
    component.filterSubmit();

    expect(projectServiceSpy.listGuestMembers).toHaveBeenCalled();
    expect(component.memberList).toEqual(mockResponse.data.rows);
    expect(component.modalRef.hide).toHaveBeenCalled();
  });

  it('should reset filter form', () => {
    const mockResponse = {
      data: {
        rows: [],
        count: 0
      }
    };
    projectServiceSpy.listGuestMembers.mockReturnValue(of(mockResponse));

    component.filterForm.patchValue({
      companyFilter: 'Test Company',
      nameFilter: 'Test Name',
      statusFilter: 'Pending'
    });

    component.modalRef = { hide: jest.fn() } as any;
    component.resetFilter();

    expect(component.filterForm.get('companyFilter')?.value).toBe('');
    expect(component.filterForm.get('nameFilter')?.value).toBe('');
    expect(component.filterForm.get('statusFilter')?.value).toBe('');
    expect(component.search).toBe('');
    expect(component.pageNo).toBe(1);
    expect(component.filterCount).toBe(0);
    expect(projectServiceSpy.listGuestMembers).toHaveBeenCalled();
    expect(component.modalRef.hide).toHaveBeenCalled();
  });

  it('should change page size and reload members', () => {
    const mockResponse = {
      data: {
        rows: [],
        count: 0
      }
    };
    projectServiceSpy.listGuestMembers.mockReturnValue(of(mockResponse));

    component.changePageSize(50);

    expect(component.pageSize).toBe(50);
    expect(projectServiceSpy.listGuestMembers).toHaveBeenCalled();
  });

  it('should handle sort by field', () => {
    const mockResponse = {
      data: {
        rows: [],
        count: 0
      }
    };
    projectServiceSpy.listGuestMembers.mockReturnValue(of(mockResponse));

    component.sortByField('name', 'ASC');

    expect(component.sortColumn).toBe('name');
    expect(component.sort).toBe('ASC');
    expect(projectServiceSpy.listGuestMembers).toHaveBeenCalled();
  });

  it('should open modal with correct configuration', () => {
    const template = {} as any;
    const mockModalRef = {} as BsModalRef;
    modalServiceSpy.show.mockReturnValue(mockModalRef);

    component.openModal(template);

    expect(modalServiceSpy.show).toHaveBeenCalledWith(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg members-popup custom-modal'
    });
  });

  it('should handle keyboard events for toggle', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const mockResponse = {
      data: {
        rows: [],
        count: 0
      }
    };
    projectServiceSpy.listGuestMembers.mockReturnValue(of(mockResponse));

    component.handleToggleKeydown(event, 'name', 'ASC');

    expect(component.sortColumn).toBe('name');
    expect(component.sort).toBe('ASC');
  });

  // Additional positive test cases
  it('should change page number and reload members', () => {
    const mockResponse = {
      data: {
        rows: [{ id: 1, name: 'Test Member' }],
        count: 1
      }
    };
    projectServiceSpy.listGuestMembers.mockReturnValue(of(mockResponse));

    component.changePageNo(2);

    expect(component.currentPageNo).toBe(2);
    expect(projectServiceSpy.listGuestMembers).toHaveBeenCalled();
  });

  it('should handle search member with data', () => {
    const mockResponse = {
      data: {
        rows: [{ id: 1, name: 'Search Result' }],
        count: 1
      }
    };
    projectServiceSpy.listGuestMembers.mockReturnValue(of(mockResponse));

    component.getSearchMember('test search');

    expect(component.showSearchbar).toBe(true);
    expect(component.search).toBe('test search');
    expect(component.pageNo).toBe(1);
    expect(projectServiceSpy.listGuestMembers).toHaveBeenCalled();
  });

  it('should handle search member with empty data', () => {
    const mockResponse = {
      data: {
        rows: [],
        count: 0
      }
    };
    projectServiceSpy.listGuestMembers.mockReturnValue(of(mockResponse));

    component.getSearchMember('');

    expect(component.showSearchbar).toBe(false);
    expect(component.search).toBe('');
    expect(component.pageNo).toBe(1);
    expect(projectServiceSpy.listGuestMembers).toHaveBeenCalled();
  });

  it('should clear search', () => {
    const mockResponse = {
      data: {
        rows: [],
        count: 0
      }
    };
    projectServiceSpy.listGuestMembers.mockReturnValue(of(mockResponse));

    component.showSearchbar = true;
    component.search = 'test';
    component.clear();

    expect(component.showSearchbar).toBe(false);
    expect(component.search).toBe('');
    expect(component.pageNo).toBe(1);
    expect(projectServiceSpy.listGuestMembers).toHaveBeenCalled();
  });

  it('should get roles successfully', () => {
    const mockRoles = {
      data: [
        { id: 1, name: 'Admin' },
        { id: 2, name: 'User' }
      ]
    };
    projectServiceSpy.getRoles.mockReturnValue(of(mockRoles));

    component.getRoles();

    expect(projectServiceSpy.getRoles).toHaveBeenCalled();
    expect(component.roleList).toEqual(mockRoles.data);
  });

  it('should handle role change', () => {
    const item: any = { id: 1, name: 'Test Item' };
    const selectedRole = 2;

    component.onRoleChange(item, selectedRole);

    expect(item.selectedRoleId).toBe(selectedRole);
  });

  it('should get country codes', () => {
    component.getCountryCode();

    expect(component.countryCode.length).toBeGreaterThan(0);
    expect(component.countryCode[0]).toHaveProperty('countryDialCode');
    expect(component.countryCode[0]).toHaveProperty('name');
  });

  it('should open modal1 with filter configuration', () => {
    const template = {} as any;
    const mockModalRef = {} as BsModalRef;
    modalServiceSpy.show.mockReturnValue(mockModalRef);

    component.openModal1(template);

    expect(component.filterModalLoader).toBe(true);
    setTimeout(() => {
      expect(modalServiceSpy.show).toHaveBeenCalledWith(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-sm filter-popup custom-modal'
      });
    }, 100);
  });

  it('should set company by opening modal', () => {
    const template = {} as any;
    const mockModalRef = {} as BsModalRef;
    modalServiceSpy.show.mockReturnValue(mockModalRef);

    component.setCompany(template);

    expect(modalServiceSpy.show).toHaveBeenCalledWith(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg members-popup custom-modal'
    });
  });

  it('should handle keyboard events for space key', () => {
    const event = new KeyboardEvent('keydown', { key: ' ' });
    const mockResponse = {
      data: {
        rows: [],
        count: 0
      }
    };
    projectServiceSpy.listGuestMembers.mockReturnValue(of(mockResponse));

    component.handleToggleKeydown(event, 'email', 'DESC');

    expect(component.sortColumn).toBe('email');
    expect(component.sort).toBe('DESC');
  });

  it('should not handle keyboard events for other keys', () => {
    const event = new KeyboardEvent('keydown', { key: 'Tab' });
    const originalSort = component.sort;
    const originalSortColumn = component.sortColumn;

    component.handleToggleKeydown(event, 'name', 'ASC');

    expect(component.sortColumn).toBe(originalSortColumn);
    expect(component.sort).toBe(originalSort);
  });

  // Negative test cases
  it('should handle empty response from getMembers', () => {
    const mockResponse = null;
    projectServiceSpy.listGuestMembers.mockReturnValue(of(mockResponse));

    component.getMembers();

    expect(component.loader).toBe(true);
    expect(projectServiceSpy.listGuestMembers).toHaveBeenCalled();
  });

  it('should handle undefined filterForm in getMembers', () => {
    component.filterForm = undefined as any;
    const mockResponse = {
      data: {
        rows: [],
        count: 0
      }
    };
    projectServiceSpy.listGuestMembers.mockReturnValue(of(mockResponse));

    component.getMembers();

    expect(projectServiceSpy.listGuestMembers).toHaveBeenCalled();
  });

  it('should handle empty roles response', () => {
    const mockRoles = null;
    projectServiceSpy.getRoles.mockReturnValue(of(mockRoles));

    component.getRoles();

    expect(projectServiceSpy.getRoles).toHaveBeenCalled();
    expect(component.roleList).toBeUndefined();
  });

  it('should handle filter submission with all filters empty', () => {
    const mockResponse = {
      data: {
        rows: [],
        count: 0
      }
    };
    projectServiceSpy.listGuestMembers.mockReturnValue(of(mockResponse));

    component.filterForm.patchValue({
      companyFilter: '',
      nameFilter: '',
      statusFilter: ''
    });

    component.modalRef = { hide: jest.fn() } as any;
    component.filterSubmit();

    expect(component.filterCount).toBe(0);
    expect(component.pageNo).toBe(1);
    expect(projectServiceSpy.listGuestMembers).toHaveBeenCalled();
    expect(component.modalRef.hide).toHaveBeenCalled();
  });

  it('should handle filter submission with mixed filters', () => {
    const mockResponse = {
      data: {
        rows: [],
        count: 0
      }
    };
    projectServiceSpy.listGuestMembers.mockReturnValue(of(mockResponse));

    component.filterForm.patchValue({
      companyFilter: 'Test Company',
      nameFilter: '',
      statusFilter: 'PENDING'
    });

    component.modalRef = { hide: jest.fn() } as any;
    component.filterSubmit();

    expect(component.filterCount).toBe(2);
    expect(component.pageNo).toBe(1);
    expect(projectServiceSpy.listGuestMembers).toHaveBeenCalled();
    expect(component.modalRef.hide).toHaveBeenCalled();
  });

  it('should get companies successfully', () => {
    const mockCompaniesResponse = {
      data: {
        rows: [
          { id: 1, companyName: 'Company A' },
          { id: 2, companyName: 'Company B' },
          { id: 3, companyName: null } // Test null company name
        ]
      },
      parentCompany: [
        { id: 4, companyName: 'Parent Company' }
      ]
    };
    projectServiceSpy.listCompany.mockReturnValue(of(mockCompaniesResponse));

    component.getCompanies();

    expect(projectServiceSpy.listCompany).toHaveBeenCalled();
    expect(component.companyList.length).toBeGreaterThan(0);
    expect(component.companyListForFilter.length).toBeGreaterThan(0);
    expect(component.filterModalLoader).toBe(false);
  });

  it('should handle companies response with no data', () => {
    const mockCompaniesResponse = null;
    projectServiceSpy.listCompany.mockReturnValue(of(mockCompaniesResponse));

    component.getCompanies();

    expect(projectServiceSpy.listCompany).toHaveBeenCalled();
  });

  it('should add guest as member successfully', () => {
    const mockGuestData = {
      ProjectId: 1,
      ParentCompanyId: 1,
      UserId: 123,
      id: 456,
      User: {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe'
      },
      selectedRoleId: 2
    };
    component.userData = {
      User: {
        id: 789,
        firstName: 'Admin',
        lastName: 'User'
      }
    };

    const mockResponse = { success: true };
    projectSharingServiceSpy.addGuestAsMember.mockReturnValue(of(mockResponse));
    const mockMembersResponse = {
      data: {
        rows: [],
        count: 0
      }
    };
    projectServiceSpy.listGuestMembers.mockReturnValue(of(mockMembersResponse));

    component.addGuestAsMember(mockGuestData);

    expect(component.addGuestLoader).toBe(true);
    expect(projectSharingServiceSpy.addGuestAsMember).toHaveBeenCalledWith({
      ProjectId: 1,
      ParentCompanyId: 1,
      guestUserId: 123,
      guestId: 456,
      guestEmail: '<EMAIL>',
      selectedRoleId: 2,
      guestFirstName: 'John',
      guestLastName: 'Doe',
      memberId: 789,
      memberFirstName: 'Admin',
      memberLastName: 'User'
    });
    expect(toastrServiceSpy.success).toHaveBeenCalledWith('Guest User approved successfully.You can see them in Members list');
  });

  it('should show error when adding guest without selected role', () => {
    const mockGuestData = {
      ProjectId: 1,
      ParentCompanyId: 1,
      UserId: 123,
      id: 456,
      User: {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe'
      }
      // selectedRoleId is missing
    };

    component.addGuestAsMember(mockGuestData);

    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Please select role');
    expect(projectSharingServiceSpy.addGuestAsMember).not.toHaveBeenCalled();
  });

  it('should handle error when adding guest as member', () => {
    const mockGuestData = {
      ProjectId: 1,
      ParentCompanyId: 1,
      UserId: 123,
      id: 456,
      User: {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe'
      },
      selectedRoleId: 2
    };
    component.userData = {
      User: {
        id: 789,
        firstName: 'Admin',
        lastName: 'User'
      }
    };

    const mockError = new Error('Server error');
    projectSharingServiceSpy.addGuestAsMember.mockReturnValue(throwError(mockError));

    component.addGuestAsMember(mockGuestData);

    expect(projectSharingServiceSpy.addGuestAsMember).toHaveBeenCalled();
    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should open confirmation decline modal', () => {
    const template = {} as any;
    const item = { id: 1, name: 'Test Item' };
    const mockModalRef = {} as BsModalRef;
    modalServiceSpy.show.mockReturnValue(mockModalRef);

    component.confirmationDecline(template, item);

    expect(component.responseData).toBe(item);
    expect(modalServiceSpy.show).toHaveBeenCalledWith(template, {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
    });
  });

  it('should handle resetForm with "no" action', () => {
    component.modalRef1 = { hide: jest.fn() } as any;

    component.resetForm('no');

    expect(component.modalRef1.hide).toHaveBeenCalled();
  });

  it('should handle resetForm with decline action', () => {
    component.modalRef1 = { hide: jest.fn() } as any;
    component.responseData = {
      ProjectId: 1,
      ParentCompanyId: 1,
      UserId: 123,
      id: 456,
      User: {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe'
      },
      selectedRoleId: 2
    };
    component.userData = {
      User: {
        id: 789,
        firstName: 'Admin',
        lastName: 'User'
      }
    };

    const mockResponse = { success: true };
    projectSharingServiceSpy.rejectGuestRequest.mockReturnValue(of(mockResponse));
    const mockMembersResponse = {
      data: {
        rows: [],
        count: 0
      }
    };
    projectServiceSpy.listGuestMembers.mockReturnValue(of(mockMembersResponse));

    component.resetForm('yes');

    expect(component.modalRef1.hide).toHaveBeenCalled();
    expect(projectSharingServiceSpy.rejectGuestRequest).toHaveBeenCalled();
    expect(toastrServiceSpy.success).toHaveBeenCalledWith('Guest Declined Successfully');
    expect(projectServiceSpy.listGuestMembers).toHaveBeenCalled();
  });

  it('should handle error in resetForm decline action', () => {
    component.modalRef1 = { hide: jest.fn() } as any;
    component.responseData = {
      ProjectId: 1,
      ParentCompanyId: 1,
      UserId: 123,
      id: 456,
      User: {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe'
      },
      selectedRoleId: 2
    };
    component.userData = {
      User: {
        id: 789,
        firstName: 'Admin',
        lastName: 'User'
      }
    };

    const mockError = new Error('Server error');
    projectSharingServiceSpy.rejectGuestRequest.mockReturnValue(throwError(() => mockError));

    component.resetForm('yes');

    expect(component.modalRef1.hide).toHaveBeenCalled();
    expect(projectSharingServiceSpy.rejectGuestRequest).toHaveBeenCalled();
    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should handle handleDownKeydown with Enter key for approved action', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const data = { id: 1, selectedRoleId: 2 };
    const item = { id: 1 };

    component.userData = {
      User: {
        id: 789,
        firstName: 'Admin',
        lastName: 'User'
      }
    };

    const mockResponse = { success: true };
    projectSharingServiceSpy.addGuestAsMember.mockReturnValue(of(mockResponse));
    const mockMembersResponse = {
      data: {
        rows: [],
        count: 0
      }
    };
    projectServiceSpy.listGuestMembers.mockReturnValue(of(mockMembersResponse));

    component.handleDownKeydown(event, data, item, 'approved');

    expect(projectSharingServiceSpy.addGuestAsMember).toHaveBeenCalled();
  });

  it('should handle handleDownKeydown with space key for declined action', () => {
    const event = new KeyboardEvent('keydown', { key: ' ' });
    const data = { id: 1 };
    const item = { id: 1 };
    const mockModalRef = {} as BsModalRef;
    modalServiceSpy.show.mockReturnValue(mockModalRef);

    component.handleDownKeydown(event, data, item, 'declined');

    expect(component.responseData).toBe(item);
    expect(modalServiceSpy.show).toHaveBeenCalled();
  });

  it('should handle handleDownKeydown with Enter key for auto action', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const data = {} as any;
    const item = { id: 1 };
    const mockModalRef = {} as BsModalRef;
    modalServiceSpy.show.mockReturnValue(mockModalRef);

    component.handleDownKeydown(event, data, item, 'auto');

    expect(component.filterModalLoader).toBe(true);
  });

  it('should not handle handleDownKeydown for other keys', () => {
    const event = new KeyboardEvent('keydown', { key: 'Tab' });
    const data = { id: 1 };
    const item = { id: 1 };

    const originalResponseData = component.responseData;
    component.handleDownKeydown(event, data, item, 'approved');

    expect(component.responseData).toBe(originalResponseData);
  });

  it('should handle handleDownKeydown with default case', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const data = { id: 1 };
    const item = { id: 1 };

    const originalResponseData = component.responseData;
    component.handleDownKeydown(event, data, item, 'unknown');

    expect(component.responseData).toBe(originalResponseData);
  });
});
