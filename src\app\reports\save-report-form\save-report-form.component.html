<div class="modal-header">
  <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Save Reports</h1>
  <button type="button" class="close ms-auto" aria-label="Close" (click)="saveReportClose()">
    <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close" /></span>
  </button>
</div>
<div class="modal-body">
  <div class="addcalendar-details">
    <form
      name="reportForm"
      class="custom-material-form add-concrete-material-form"
      [formGroup]="reportForm"
      (ngSubmit)="reportSubmit()"
    >
      <div class="row">
        <div class="col-md-12">
          <div class="floating-concrete mt-3">
            <div class="form-group floating-label">
              <input
                class="floating-input form-control fs12 px-0"
                type="text"
                placeholder=""
                formControlName="reportName"
              />
              <div class="color-red fs12" *ngIf="submitted && reportForm.get('reportName').errors">
                <small  id="reportName" *ngIf="reportForm.get('reportName').errors.required"
                  >*Report Name is Required.</small
                >
              </div>
              <label for="reportName" class="fs12 fw600 m-0 color-grey11 schedule-form-label"
                >Report Name
                <span class="color-red">
                  <sup>*</sup>
                </span></label
              >
            </div>
          </div>
        </div>
        <div class="col-md-12">
          <div class="form-group pt-0 mt-0">
            <label class="fs12 fw600 m-0 color-grey11"  for="outForm"
              >Output format
              <span class="color-red">
                <sup>*</sup>
              </span></label
            >
            <select id="outForm"
              class="form-control fs12 material-input px-1 color-grey11"
              formControlName="outputFormat"
            >
              <option *ngFor="let type of exportType" value="{{ type }}">
                {{ type }}
              </option>
            </select>
          </div>
        </div>
      </div>
      <div class="modal-footer border-0 justify-content-center add-calendar-footer">
        <div class="mt-0 mb15 text-center">
          <button
            class="btn btn-grey color-dark-grey radius20 fs12 fw-bold me-3 px-2rem"
            type="button"
            (click)="saveReportClose()"
          >
            Cancel
          </button>

          <button class="btn btn-orange-dark radius20 fs12 fw-bold px-2rem iconremove">
            <em class="fa fa-spinner" aria-hidden="true" *ngIf="formSubmitted"></em>
            Save
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
