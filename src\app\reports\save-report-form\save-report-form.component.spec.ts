import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { ReactiveFormsModule, Validators, UntypedFormBuilder } from '@angular/forms';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { of, throwError } from 'rxjs';
import { SaveReportFormComponent } from './save-report-form.component';
import { ReportsService } from '../../services/reports/reports.service';
import { DeliveryService } from '../../services/profile/delivery.service';

describe('SaveReportFormComponent', () => {
  let component: SaveReportFormComponent;
  let fixture: ComponentFixture<SaveReportFormComponent>;
  let reportsServiceMock: jest.Mocked<ReportsService>;
  let deliveryServiceMock: jest.Mocked<DeliveryService>;
  let toastrServiceMock: jest.Mocked<ToastrService>;
  let modalServiceMock: jest.Mocked<BsModalService>;
  let modalRefMock: jest.Mocked<BsModalRef>;

  beforeEach(async () => {
    reportsServiceMock = {
      saveReportConcreteRequest: jest.fn(),
      saveReportCraneRequest: jest.fn(),
      saveReportDeliveryRequest: jest.fn(),
      saveReportHeatMap: jest.fn(),
      saveReportInspectionRequest: jest.fn(),
    } as any;

    deliveryServiceMock = {
      saveReportWeeklyCalendarRequest: jest.fn(),
    } as any;

    toastrServiceMock = {
      success: jest.fn(),
      error: jest.fn(),
    } as any;

    modalServiceMock = {
      show: jest.fn(),
    } as any;

    modalRefMock = {
      hide: jest.fn(),
    } as any;

    await TestBed.configureTestingModule({
      declarations: [SaveReportFormComponent],
      imports: [ReactiveFormsModule],
      providers: [
        { provide: ReportsService, useValue: reportsServiceMock },
        { provide: DeliveryService, useValue: deliveryServiceMock },
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: BsModalService, useValue: modalServiceMock },
        { provide: BsModalRef, useValue: modalRefMock },
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SaveReportFormComponent);
    component = fixture.componentInstance;
    component.ProjectId = '123';
    component.reportType = 'Heat Map';
    component.filterPayload = {
      startTime: '10:00:00',
      endTime: '18:00:00',
      timezone: 'UTC',
    };
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the form with default values', () => {
    expect(component.reportForm).toBeTruthy();
    expect(component.reportForm.get('outputFormat').value).toBe('PDF');
  });

  it('should validate required fields', () => {
    component.reportForm.patchValue({
      reportName: '',
      outputFormat: 'PDF'
    });
    component.reportSubmit();
    expect(component.submitted).toBeTruthy();
    expect(component.formSubmitted).toBeFalsy();
    expect(toastrServiceMock.error).not.toHaveBeenCalled();
  });

  it('should handle form submission with valid data', fakeAsync(() => {
    component.reportForm.patchValue({
      reportName: 'Test Report',
      outputFormat: 'PDF',
    });

    reportsServiceMock.saveReportHeatMap.mockReturnValue(of({ message: 'Success' }));
    component.reportSubmit();
    tick(); // Process all pending asynchronous activities

    expect(component.submitted).toBeTruthy();
    // expect(component.formSubmitted).toBeTruthy();
    expect(reportsServiceMock.saveReportHeatMap).toHaveBeenCalled();
  }));

  it('should handle no data found response', () => {
    component.reportForm.patchValue({
      reportName: 'Test Report',
      outputFormat: 'PDF',
    });

    reportsServiceMock.saveReportHeatMap.mockReturnValue(of({ message: 'No data found.' }));
    component.reportSubmit();

    expect(toastrServiceMock.error).toHaveBeenCalledWith('No data found');
    expect(modalRefMock.hide).toHaveBeenCalled();
  });

  it('should handle API error', () => {
    component.reportForm.patchValue({
      reportName: 'Test Report',
      outputFormat: 'PDF',
    });

    reportsServiceMock.saveReportHeatMap.mockReturnValue(throwError(() => new Error('API Error')));
    component.reportSubmit();

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    expect(component.formSubmitted).toBeFalsy();
  });

  it('should handle different report types', () => {
    const testCases = [
      { type: 'Delivery', service: reportsServiceMock.saveReportDeliveryRequest },
      { type: 'Crane', service: reportsServiceMock.saveReportCraneRequest },
      { type: 'Concrete', service: reportsServiceMock.saveReportConcreteRequest },
      { type: 'Weekly Calender', service: deliveryServiceMock.saveReportWeeklyCalendarRequest },
    ];

    testCases.forEach(({ type, service }) => {
      component.reportType = type;
      component.reportForm.patchValue({
        reportName: 'Test Report',
        outputFormat: 'PDF',
      });

      service.mockReturnValue(of({ message: 'Success' }));
      component.reportSubmit();

      expect(service).toHaveBeenCalled();
    });
  });

  it('should close modal on successful report save', () => {
    component.reportForm.patchValue({
      reportName: 'Test Report',
      outputFormat: 'PDF',
    });

    reportsServiceMock.saveReportHeatMap.mockReturnValue(of({ message: 'Success' }));
    component.reportSubmit();

    expect(toastrServiceMock.success).toHaveBeenCalledWith('Report saved successfully');
    expect(modalRefMock.hide).toHaveBeenCalled();
  });

  it('should handle weekly calendar specific error message', () => {
    component.reportType = 'Weekly Calender';
    component.reportForm.patchValue({
      reportName: 'Test Report',
      outputFormat: 'PDF',
    });

    deliveryServiceMock.saveReportWeeklyCalendarRequest.mockReturnValue(
      of({ message: 'Currently no data available' })
    );
    component.reportSubmit();

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Currently no data available');
    expect(modalRefMock.hide).toHaveBeenCalled();
  });

  it('should handle API error during form submission', fakeAsync(() => {
    component.reportForm.patchValue({
      reportName: 'Test Report',
      outputFormat: 'PDF',
    });

    reportsServiceMock.saveReportHeatMap.mockReturnValue(
      throwError(() => new Error('API Error'))
    );

    component.reportSubmit();
    tick(); // Process all pending asynchronous activities

    expect(component.submitted).toBeTruthy();
    expect(toastrServiceMock.error).toHaveBeenCalled();
  }));

  it('should close modal on cancel', () => {
    component.saveReportClose();
    expect(modalRefMock.hide).toHaveBeenCalled();
  });

  it('should handle different output formats', () => {
    const formats = ['PDF', 'Excel', 'CSV'];
    
    formats.forEach(format => {
      component.reportForm.patchValue({
        reportName: 'Test Report',
        outputFormat: format,
      });
      
      reportsServiceMock.saveReportHeatMap.mockReturnValue(of({ message: 'Success' }));
      component.reportSubmit();
      
      const lastCall = reportsServiceMock.saveReportHeatMap.mock.calls.pop();
      expect(lastCall[0].outputFormat).toBe(format);
    });
  });

  it('should validate report name length', () => {
    // Setup the mock to return an Observable
    reportsServiceMock.saveReportHeatMap.mockReturnValue(of({ message: 'Success' }));

    // Test with a name that's too long (if there's validation for this)
    component.reportForm.patchValue({
      reportName: 'A'.repeat(101), // Assuming max length is 100
      outputFormat: 'PDF'
    });

    // Add validation if it doesn't exist
    const reportNameControl = component.reportForm.get('reportName');
    reportNameControl.setValidators([Validators.required, Validators.maxLength(100)]);
    reportNameControl.updateValueAndValidity();

    // Now the control should be invalid
    expect(reportNameControl.valid).toBeFalsy();

    // Call submit
    component.reportSubmit();

    // Form should be invalid, so formSubmitted should remain false
    expect(component.formSubmitted).toBeFalsy();
  });

  it('should validate form with empty output format', () => {
    component.reportForm.patchValue({
      reportName: 'Test Report',
      outputFormat: ''
    });
    component.reportSubmit();
    expect(component.submitted).toBeTruthy();
    expect(component.formSubmitted).toBeFalsy();
  });

  it('should handle form reset via reportDetailsForm', () => {
    // Set initial values
    component.reportForm.patchValue({
      reportName: 'Test Report',
      outputFormat: 'Excel'
    });
    component.submitted = true;

    // Reset the form using the existing method
    component.reportDetailsForm();

    // Check if form is reset
    expect(component.reportForm.get('reportName').value).toBe('');
    expect(component.reportForm.get('outputFormat').value).toBe('PDF');
  });

  it('should initialize with correct validators', () => {
    expect(component.reportForm.get('reportName').validator).toEqual(Validators.required);
    expect(component.reportForm.get('outputFormat').validator).toEqual(Validators.required);
  });

  it('should handle specific error messages from API', () => {
    component.reportForm.patchValue({
      reportName: 'Test Report',
      outputFormat: 'PDF',
    });

    const errorResponse = { 
      error: { 
        message: 'Validation failed',
        details: [{ field: 'reportName', message: 'Name already exists' }]
      }
    };
    
    reportsServiceMock.saveReportHeatMap.mockReturnValue(throwError(() => errorResponse));
    component.reportSubmit();

    expect(toastrServiceMock.error).toHaveBeenCalled();
    expect(component.formSubmitted).toBeFalsy();
  });

  it('should handle ngOnInit lifecycle hook', () => {
    // Reset component
    component.ngOnInit();
    
    // Check if form is initialized
    expect(component.reportForm).toBeTruthy();
    expect(component.submitted).toBeFalsy();
  });

  it('should handle different filter payloads', () => {
    const testPayloads = [
      { startDate: '2023-01-01', endDate: '2023-01-31' },
      { locations: [1, 2, 3], equipmentTypes: [4, 5, 6] },
      { companies: ['Company A', 'Company B'] }
    ];
    
    testPayloads.forEach(payload => {
      component.filterPayload = payload;
      component.reportForm.patchValue({
        reportName: 'Test Report',
        outputFormat: 'PDF',
      });
      
      reportsServiceMock.saveReportHeatMap.mockReturnValue(of({ message: 'Success' }));
      component.reportSubmit();
      
      const lastCall = reportsServiceMock.saveReportHeatMap.mock.calls.pop();
      expect(lastCall[0].filterPayload).toEqual(payload);
    });
  });

  it('should handle form submission when already submitted', fakeAsync(() => {
    component.formSubmitted = true;
    component.reportForm.patchValue({
      reportName: 'Test Report',
      outputFormat: 'PDF',
    });
    
    component.reportSubmit();
    tick();
    
    // Should not call service again if already submitted
    expect(reportsServiceMock.saveReportHeatMap).not.toHaveBeenCalled();
  }));

  it('should handle unknown report type gracefully', () => {
    component.reportType = 'Unknown Type';
    component.reportForm.patchValue({
      reportName: 'Test Report',
      outputFormat: 'PDF',
    });

    component.reportSubmit();

    expect(toastrServiceMock.error).toHaveBeenCalled();
  });

  // Test Inspection report type
  it('should handle Inspection report type', () => {
    component.reportType = 'Inspection';
    component.reportForm.patchValue({
      reportName: 'Inspection Report',
      outputFormat: 'PDF',
    });

    reportsServiceMock.saveReportInspectionRequest.mockReturnValue(of({ message: 'Success' }));
    component.reportSubmit();

    expect(reportsServiceMock.saveReportInspectionRequest).toHaveBeenCalled();
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Report saved successfully');
    expect(modalRefMock.hide).toHaveBeenCalled();
  });

  it('should handle Inspection report with no data found', () => {
    component.reportType = 'Inspection';
    component.reportForm.patchValue({
      reportName: 'Inspection Report',
      outputFormat: 'PDF',
    });

    reportsServiceMock.saveReportInspectionRequest.mockReturnValue(of({ message: 'No data found.' }));
    component.reportSubmit();

    expect(toastrServiceMock.error).toHaveBeenCalledWith('No data found');
    expect(modalRefMock.hide).toHaveBeenCalled();
  });

  it('should handle Inspection report API error', () => {
    component.reportType = 'Inspection';
    component.reportForm.patchValue({
      reportName: 'Inspection Report',
      outputFormat: 'PDF',
    });

    reportsServiceMock.saveReportInspectionRequest.mockReturnValue(throwError(() => new Error('API Error')));
    component.reportSubmit();

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    expect(component.formSubmitted).toBeFalsy();
  });

  // Test Heat Map specific logic for non-PDF export types
  it('should not call saveReportHeatMap for non-PDF export types', () => {
    component.reportType = 'Heat Map';
    component.reportForm.patchValue({
      reportName: 'Heat Map Report',
      outputFormat: 'Excel',
    });

    component.reportSubmit();

    expect(reportsServiceMock.saveReportHeatMap).not.toHaveBeenCalled();
  });

  // Test constructor behavior
  it('should initialize form in constructor', () => {
    const newComponent = new SaveReportFormComponent(
      modalServiceMock,
      reportsServiceMock,
      TestBed.inject(UntypedFormBuilder),
      toastrServiceMock,
      deliveryServiceMock,
      modalRefMock
    );

    expect(newComponent.reportForm).toBeTruthy();
    expect(newComponent.reportForm.get('outputFormat').value).toBe('PDF');
  });

  // Test form validation edge cases
  it('should handle whitespace-only report name', () => {
    component.reportForm.patchValue({
      reportName: '   ',
      outputFormat: 'PDF'
    });

    component.reportSubmit();

    expect(component.submitted).toBeTruthy();
    expect(component.formSubmitted).toBeFalsy();
  });

  it('should handle special characters in report name', () => {
    component.reportForm.patchValue({
      reportName: 'Test@Report#123',
      outputFormat: 'PDF',
    });

    reportsServiceMock.saveReportHeatMap.mockReturnValue(of({ message: 'Success' }));
    component.reportSubmit();

    expect(reportsServiceMock.saveReportHeatMap).toHaveBeenCalled();
    const lastCall = reportsServiceMock.saveReportHeatMap.mock.calls.pop();
    expect(lastCall[1].reportName).toBe('Test@Report#123');
  });

  // Test null/undefined filterPayload handling
  it('should handle null filterPayload for Heat Map', () => {
    component.reportType = 'Heat Map';
    component.filterPayload = null;
    component.reportForm.patchValue({
      reportName: 'Test Report',
      outputFormat: 'PDF',
    });

    expect(() => component.reportSubmit()).toThrow();
  });

  it('should handle undefined filterPayload properties', () => {
    component.reportType = 'Heat Map';
    component.filterPayload = {};
    component.reportForm.patchValue({
      reportName: 'Test Report',
      outputFormat: 'PDF',
    });

    reportsServiceMock.saveReportHeatMap.mockReturnValue(of({ message: 'Success' }));
    component.reportSubmit();

    expect(reportsServiceMock.saveReportHeatMap).toHaveBeenCalled();
  });

  // Test timezone handling
  it('should use default timezone when not provided', () => {
    component.reportType = 'Delivery';
    component.filterPayload = { startdate: '2023-01-01', enddate: '2023-01-31' };
    component.reportForm.patchValue({
      reportName: 'Delivery Report',
      outputFormat: 'PDF',
    });

    reportsServiceMock.saveReportDeliveryRequest.mockReturnValue(of({ message: 'Success' }));
    component.reportSubmit();

    const lastCall = reportsServiceMock.saveReportDeliveryRequest.mock.calls.pop();
    expect(lastCall[1].timezone).toBe(Intl.DateTimeFormat().resolvedOptions().timeZone);
  });

  // Test payload methods individually
  it('should call heatMapPayload correctly', () => {
    const params = { ProjectId: '123', pageSize: 10, pageNo: 1, sortOrder: 'ASC', void: 0 };
    const data = { reportName: 'Test', exportType: 'PDF' };

    component.filterPayload = {
      startTime: '10:00:00',
      endTime: '18:00:00',
      timezone: 'UTC',
      companyFilter: 1,
      memberFilter: 2
    };

    reportsServiceMock.saveReportHeatMap.mockReturnValue(of({ message: 'Success' }));

    component.heatMapPayload(params, data);

    expect(reportsServiceMock.saveReportHeatMap).toHaveBeenCalled();
  });

  it('should call deliveryPayload correctly', () => {
    const params = { ProjectId: '123', pageSize: 10, pageNo: 1, void: 0 };
    const data = { reportName: 'Delivery Test', exportType: 'PDF' };

    component.filterPayload = {
      companyFilter: 1,
      descriptionFilter: 'test',
      statusFilter: 'active',
      startdate: '2023-01-01',
      enddate: '2023-01-31',
      sort: 'DESC',
      sortByField: ' id'
    };

    reportsServiceMock.saveReportDeliveryRequest.mockReturnValue(of({ message: 'Success' }));

    component.deliveryPayload(params, data);

    expect(reportsServiceMock.saveReportDeliveryRequest).toHaveBeenCalled();
    const lastCall = reportsServiceMock.saveReportDeliveryRequest.mock.calls.pop();
    expect(lastCall[1].sort).toBe('ASC');
    expect(lastCall[1].sortByField).toBe('deliveryStart');
  });

  it('should call cranePayload correctly', () => {
    const params = { ProjectId: '123', pageSize: 10, pageNo: 1, void: 0 };
    const data = { reportName: 'Crane Test', exportType: 'PDF' };

    component.filterPayload = {
      companyFilter: 1,
      descriptionFilter: 'crane test',
      statusFilter: 'active',
      startdate: '2023-01-01',
      enddate: '2023-01-31'
    };

    reportsServiceMock.saveReportCraneRequest.mockReturnValue(of({ message: 'Success' }));

    component.cranePayload(params, data);

    expect(reportsServiceMock.saveReportCraneRequest).toHaveBeenCalled();
  });

  it('should call concretePayload correctly', () => {
    const params = { ProjectId: '123', pageSize: 10, pageNo: 1, void: 0 };
    const data = { reportName: 'Concrete Test', exportType: 'PDF' };

    component.filterPayload = {
      companyFilter: 1,
      descriptionFilter: 'concrete test',
      statusFilter: 'active',
      startdate: '2023-01-01',
      enddate: '2023-01-31',
      concreteSupplierFilter: 'supplier1'
    };

    reportsServiceMock.saveReportConcreteRequest.mockReturnValue(of({ message: 'Success' }));

    component.concretePayload(params, data);

    expect(reportsServiceMock.saveReportConcreteRequest).toHaveBeenCalled();
  });

  it('should call weeklyPayload correctly', () => {
    const params = { ProjectId: '123', pageSize: 10, pageNo: 1, void: 0 };
    const data = { reportName: 'Weekly Test', exportType: 'PDF' };

    component.filterPayload = {
      companyFilter: 1,
      memberFilter: 2,
      startdate: '2023-01-01',
      enddate: '2023-01-31',
      isDST: true,
      currentStart: '2023-01-01',
      currentEnd: '2023-01-07',
      typeFormat: 'weekly'
    };

    deliveryServiceMock.saveReportWeeklyCalendarRequest.mockReturnValue(of({ message: 'Success' }));

    component.weeklyPayload(params, data);

    expect(deliveryServiceMock.saveReportWeeklyCalendarRequest).toHaveBeenCalled();
  });

  it('should call inspectionPayload correctly', () => {
    const params = { ProjectId: '123', pageSize: 10, pageNo: 1, void: 0 };
    const data = { reportName: 'Inspection Test', exportType: 'PDF' };

    component.filterPayload = {
      companyFilter: 1,
      descriptionFilter: 'inspection test',
      statusFilter: 'active',
      inspectionStatusFilter: 'completed',
      inspectionTypeFilter: 'safety',
      startdate: '2023-01-01',
      enddate: '2023-01-31'
    };

    reportsServiceMock.saveReportInspectionRequest.mockReturnValue(of({ message: 'Success' }));

    component.inspectionPayload(params, data);

    expect(reportsServiceMock.saveReportInspectionRequest).toHaveBeenCalled();
  });

  // Test individual save methods
  it('should call heatMapSavedReports correctly', () => {
    const params = { ProjectId: '123', pageSize: 10, pageNo: 1, sortOrder: 'ASC', void: 0 };
    const payload = { reportName: 'Heat Map Test', exportType: 'PDF' };

    reportsServiceMock.saveReportHeatMap.mockReturnValue(of({ message: 'Success' }));

    component.heatMapSavedReports(params, payload);

    expect(reportsServiceMock.saveReportHeatMap).toHaveBeenCalledWith(params, payload);
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Report saved successfully');
    expect(modalRefMock.hide).toHaveBeenCalled();
  });

  it('should call deliverySavedReports correctly', () => {
    const params = { ProjectId: '123', pageSize: 10, pageNo: 1, void: 0 };
    const payload = { reportName: 'Delivery Test', exportType: 'PDF' };

    reportsServiceMock.saveReportDeliveryRequest.mockReturnValue(of({ message: 'Success' }));

    component.deliverySavedReports(params, payload);

    expect(reportsServiceMock.saveReportDeliveryRequest).toHaveBeenCalledWith(params, payload);
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Report saved successfully');
    expect(modalRefMock.hide).toHaveBeenCalled();
  });

  it('should call craneSavedReports correctly', () => {
    const params = { ProjectId: '123', pageSize: 10, pageNo: 1, void: 0 };
    const payload = { reportName: 'Crane Test', exportType: 'PDF' };

    reportsServiceMock.saveReportCraneRequest.mockReturnValue(of({ message: 'Success' }));

    component.craneSavedReports(params, payload);

    expect(reportsServiceMock.saveReportCraneRequest).toHaveBeenCalledWith(params, payload);
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Report saved successfully');
    expect(modalRefMock.hide).toHaveBeenCalled();
  });

  it('should call concreateSavedReports correctly', () => {
    const params = { ProjectId: '123', pageSize: 10, pageNo: 1, void: 0 };
    const payload = { reportName: 'Concrete Test', exportType: 'PDF' };

    reportsServiceMock.saveReportConcreteRequest.mockReturnValue(of({ message: 'Success' }));

    component.concreateSavedReports(params, payload);

    expect(reportsServiceMock.saveReportConcreteRequest).toHaveBeenCalledWith(params, payload);
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Report saved successfully');
    expect(modalRefMock.hide).toHaveBeenCalled();
  });

  it('should call weeklySavedReports correctly', () => {
    const params = { ProjectId: '123', pageSize: 10, pageNo: 1, void: 0 };
    const payload = { reportName: 'Weekly Test', exportType: 'PDF' };

    deliveryServiceMock.saveReportWeeklyCalendarRequest.mockReturnValue(of({ message: 'Success' }));

    component.weeklySavedReports(params, payload);

    expect(deliveryServiceMock.saveReportWeeklyCalendarRequest).toHaveBeenCalledWith(params, payload);
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Report saved successfully');
    expect(modalRefMock.hide).toHaveBeenCalled();
  });

  it('should call inspectionSavedReports correctly', () => {
    const params = { ProjectId: '123', pageSize: 10, pageNo: 1, void: 0 };
    const payload = { reportName: 'Inspection Test', exportType: 'PDF' };

    reportsServiceMock.saveReportInspectionRequest.mockReturnValue(of({ message: 'Success' }));

    component.inspectionSavedReports(params, payload);

    expect(reportsServiceMock.saveReportInspectionRequest).toHaveBeenCalledWith(params, payload);
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Report saved successfully');
    expect(modalRefMock.hide).toHaveBeenCalled();
  });

  // Test error handling for individual save methods
  it('should handle heatMapSavedReports error', () => {
    const params = { ProjectId: '123', pageSize: 10, pageNo: 1, sortOrder: 'ASC', void: 0 };
    const payload = { reportName: 'Heat Map Test', exportType: 'PDF' };

    reportsServiceMock.saveReportHeatMap.mockReturnValue(throwError(() => new Error('API Error')));

    component.heatMapSavedReports(params, payload);

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    expect(component.formSubmitted).toBeFalsy();
  });

  it('should handle deliverySavedReports no data found', () => {
    const params = { ProjectId: '123', pageSize: 10, pageNo: 1, void: 0 };
    const payload = { reportName: 'Delivery Test', exportType: 'PDF' };

    reportsServiceMock.saveReportDeliveryRequest.mockReturnValue(of({ message: 'No data found.' }));

    component.deliverySavedReports(params, payload);

    expect(toastrServiceMock.error).toHaveBeenCalledWith('No data found');
    expect(modalRefMock.hide).toHaveBeenCalled();
  });

  it('should handle craneSavedReports error', () => {
    const params = { ProjectId: '123', pageSize: 10, pageNo: 1, void: 0 };
    const payload = { reportName: 'Crane Test', exportType: 'PDF' };

    reportsServiceMock.saveReportCraneRequest.mockReturnValue(throwError(() => new Error('API Error')));

    component.craneSavedReports(params, payload);

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    expect(component.formSubmitted).toBeFalsy();
  });

  it('should handle concreateSavedReports no data found', () => {
    const params = { ProjectId: '123', pageSize: 10, pageNo: 1, void: 0 };
    const payload = { reportName: 'Concrete Test', exportType: 'PDF' };

    reportsServiceMock.saveReportConcreteRequest.mockReturnValue(of({ message: 'No data found.' }));

    component.concreateSavedReports(params, payload);

    expect(toastrServiceMock.error).toHaveBeenCalledWith('No data found');
    expect(modalRefMock.hide).toHaveBeenCalled();
  });

  it('should handle weeklySavedReports error', () => {
    const params = { ProjectId: '123', pageSize: 10, pageNo: 1, void: 0 };
    const payload = { reportName: 'Weekly Test', exportType: 'PDF' };

    deliveryServiceMock.saveReportWeeklyCalendarRequest.mockReturnValue(throwError(() => new Error('API Error')));

    component.weeklySavedReports(params, payload);

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    expect(component.formSubmitted).toBeFalsy();
  });

  it('should handle inspectionSavedReports error', () => {
    const params = { ProjectId: '123', pageSize: 10, pageNo: 1, void: 0 };
    const payload = { reportName: 'Inspection Test', exportType: 'PDF' };

    reportsServiceMock.saveReportInspectionRequest.mockReturnValue(throwError(() => new Error('API Error')));

    component.inspectionSavedReports(params, payload);

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    expect(component.formSubmitted).toBeFalsy();
  });

  // Test edge cases for form validation
  it('should handle form with null report name', () => {
    component.reportForm.patchValue({
      reportName: null,
      outputFormat: 'PDF'
    });

    component.reportSubmit();

    expect(component.submitted).toBeTruthy();
    expect(component.formSubmitted).toBeFalsy();
  });

  it('should handle form with undefined output format', () => {
    component.reportForm.patchValue({
      reportName: 'Test Report',
      outputFormat: undefined
    });

    component.reportSubmit();

    expect(component.submitted).toBeTruthy();
    expect(component.formSubmitted).toBeFalsy();
  });

  // Test payload building edge cases
  it('should handle null report name in payload', () => {
    component.reportType = 'Heat Map';
    component.reportForm.patchValue({
      reportName: null,
      outputFormat: 'PDF',
    });

    component.filterPayload = {
      startTime: '10:00:00',
      endTime: '18:00:00',
      timezone: 'UTC'
    };

    reportsServiceMock.saveReportHeatMap.mockReturnValue(of({ message: 'Success' }));
    component.reportSubmit();

    const lastCall = reportsServiceMock.saveReportHeatMap.mock.calls.pop();
    expect(lastCall[1].reportName).toBeNull();
  });

  it('should handle empty output format in payload', () => {
    component.reportType = 'Heat Map';
    component.reportForm.patchValue({
      reportName: 'Test Report',
      outputFormat: '',
    });

    component.filterPayload = {
      startTime: '10:00:00',
      endTime: '18:00:00',
      timezone: 'UTC'
    };

    reportsServiceMock.saveReportHeatMap.mockReturnValue(of({ message: 'Success' }));
    component.reportSubmit();

    const lastCall = reportsServiceMock.saveReportHeatMap.mock.calls.pop();
    expect(lastCall[1].exportType).toBe('PDF'); // Should default to PDF
  });

  // Test component properties
  it('should handle different ProjectId types', () => {
    component.ProjectId = 123; // number instead of string
    component.reportType = 'Heat Map';
    component.reportForm.patchValue({
      reportName: 'Test Report',
      outputFormat: 'PDF',
    });

    component.filterPayload = {
      startTime: '10:00:00',
      endTime: '18:00:00',
      timezone: 'UTC'
    };

    reportsServiceMock.saveReportHeatMap.mockReturnValue(of({ message: 'Success' }));
    component.reportSubmit();

    const lastCall = reportsServiceMock.saveReportHeatMap.mock.calls.pop();
    expect(lastCall[0].ProjectId).toBe(123);
  });

  it('should handle missing component properties', () => {
    component.ProjectId = undefined;
    component.pageSize = undefined;
    component.pageNo = undefined;
    component.sortOrder = undefined;
    component.reportType = 'Heat Map';

    component.reportForm.patchValue({
      reportName: 'Test Report',
      outputFormat: 'PDF',
    });

    component.filterPayload = {
      startTime: '10:00:00',
      endTime: '18:00:00',
      timezone: 'UTC'
    };

    reportsServiceMock.saveReportHeatMap.mockReturnValue(of({ message: 'Success' }));
    component.reportSubmit();

    expect(reportsServiceMock.saveReportHeatMap).toHaveBeenCalled();
  });
});
