import { Directive, HostListener } from '@angular/core';

@Directive({
  selector: '[appNumericonly]',
})
export class NumericOnlyDirective {
  @HostListener('keydown', ['$event']) public onKeydown(e: KeyboardEvent): void {
    const keyChar = e.key;
    let allowCharacter: boolean;

    if (
      keyChar === 'Tab' ||
      keyChar === 'Enter' ||
      keyChar === 'Backspace' ||
      keyChar === 'ArrowLeft' ||
      keyChar === 'ArrowRight' ||
      keyChar === 'Delete'
    ) {
      allowCharacter = true;
    } else {
      allowCharacter = keyChar >= '0' && keyChar <= '9';
    }

    if (!allowCharacter) {
      e.preventDefault();
    }
  }
}
