import { Component, TemplateRef } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Observable } from 'rxjs';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { ProjectService } from '../services/profile/project.service';
import { ProfileService } from '../services/profile/profile.service';
import { UserService } from '../services/user.service';
import { AuthService } from '../services/auth/auth.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { countryCodes } from '../services/countryCodes';
import { countryNames } from '../services/countryNames';
import { MixpanelService } from '../services/mixpanel.service';

@Component({
  selector: 'app-members',
  templateUrl: './members.component.html',
  })
export class MembersComponent {
  public currentPageNo = 1;

  public userData: any = {};

  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public ProjectId: number;

  public memberList: any = [];

  public roleList: any = [];

  public submitted = false;

  public formSubmitted = false;

  public formEditSubmitted = false;

  public formDeleteSubmitted = false;

  public editSubmitted = false;

  public editIndex: string | number;

  public currentDeleteId: any;

  public deleteSubmitted = false;

  public loader = true;

  public filterCount = 0;

  public pageSize = 25;

  public pageNo = 1;

  public totalCount = 0;

  public inviteDetailsForm: UntypedFormGroup;

  public memberEditForm: UntypedFormGroup;

  public selectAll = false;

  public companyList: any = [];

  public companyListForFilter: any = [];

  public countryCode = [];

  public inviteMemberList = [];

  public deleteIndex: any = [];

  public phoneMask = '00000-00000';

  public remove = false;

  public modalLoader = false;

  public filterModalLoader = false;

  public lastId: any = 0;

  public filterForm: UntypedFormGroup;

  public search = '';

  public ParentCompanyId: number;

  public isAdmin = false;

  public invalidMemberForm = true;

  public authUser: any = {};

  public userExists: any = {};

  public isCollapsed = false;

  public isCompanyCollapsed = false;

  public isRoleCollapsed = false;

  public createNewModalLoader = false;

  public invalidMemberObject: number;

  public sort = 'DESC';

  public sortColumn = 'id';

  public showSearchbar = false;

  public items: string[] = [
    'The first choice!',
    'And another choice for you.',
    'but wait! A third!',
  ];

  public config = {
    displayKey: 'companyName',
    search: true,
    height: 'auto',
    placeholder: 'Company',
    customComparator: (): void => {},
    limitTo: 0,
    moreText: 'more',
    noResultsFound: 'No results found!',
    searchPlaceholder: 'Search',
    searchOnKey: 'companyName',
    clearOnSelection: false,
    inputDirection: 'ltr',
  };

  public mappedRequestList: any[];

  public getMappedRequestLoader: boolean;

  public allMemberList: any;

  public memberData: any;

  public deactivateMemberLoader: boolean;

  public linkGeneratingWaitingModal: BsModalRef;

  public copyInviteLinkPopup: BsModalRef;

  public onboardingInviteLink: any;

  public wholeStatus = ['Completed', 'Pending'];

  public constructor(
    private readonly modalService: BsModalService,
    public projectService: ProjectService,
    private readonly mixpanelService: MixpanelService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly authService: AuthService,
    private readonly profileService: ProfileService,
    public deliveryService: DeliveryService,
    private readonly userService: UserService,
    private readonly router: Router,
    private readonly titleService: Title,
    private readonly toastr: ToastrService,
  ) {
    this.titleService.setTitle('Follo - Members');
    this.projectService.projectParent.subscribe((response11): void => {
      this.ProjectId = response11.ProjectId;
      this.ParentCompanyId = response11.ParentCompanyId;
      if (
        this.ProjectId !== undefined
        && this.ProjectId !== null
        && this.ProjectId > 0
        && this.ParentCompanyId !== undefined
        && this.ParentCompanyId !== null
        && this.ParentCompanyId > 0
      ) {
        this.loader = true;
        this.resetAndClose();
        this.getMembers();
        this.getCompanies();
        this.getMembers();
      }
    });
    this.projectService.userData.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.userData = res;
        if (
          this.userData.RoleId === 2
          || this.userData.RoleId === 1
          || this.userData.RoleId === 3
        ) {
          this.isAdmin = true;
        }
      }
    });
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
      }
    });
    this.inviteForm();
    this.editMemberForm();
    this.getCountryCode();
    this.getRoles();
    this.filterDetailsForm();
  }

  public onHidden(): void {}

  public onShown(): void {}

  public isOpenChange(): void { /* */ }

  public alphaOnly(event: { keyCode: any }): boolean {
    const key = event.keyCode;
    return (key >= 65 && key <= 90) || (key >= 97 && key <= 128) || key === 8 || key === 32;
  }

  public checkStringEmptyValues(formValue: { firstName: string; lastName: string }): boolean {
    if (formValue.firstName.trim() === '') {
      return true;
    }
    if (formValue.lastName.trim() === '') {
      return true;
    }
    return false;
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.search = '';
    this.pageNo = 1;
    this.filterDetailsForm();
    this.getMembers();
    this.modalRef.hide();
  }

  public sortByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getMembers();
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('companyFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('nameFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('roleFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('statusFilter').value !== '') {
      this.filterCount += 1;
    }
    this.pageNo = 1;
    this.getMembers();
    this.modalRef.hide();
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      companyFilter: [''],
      nameFilter: [''],
      roleFilter: [''],
      statusFilter: [''],
    });
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, value: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'company':
          this.appendCompany(data, item, value);
          break;
        case 'role':
          this.appendRole(data, item, value);
          break;
        case 'goto':
          this.goToCompany();
          break;
        case 'open':
          this.openModal1(data);
          break;
        default:
          break;
      }
    }
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.pageNo = 1;
    this.getMembers();
  }

  public getSearchMember(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.pageNo = 1;
    this.search = data;
    this.getMembers();
  }

  public getCompanies(): void {
    if (
      this.modalLoader === false
      && this.createNewModalLoader === false
      && this.filterModalLoader === false
    ) {
      this.loader = true;
    }
    const param = {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.currentPageNo,
      ParentCompanyId: this.ParentCompanyId,
    };
    const payload = {
      inviteMember: true,
    };
    this.projectService.listCompany(param, payload).subscribe((response: any): void => {
      if (response) {
        this.companyList = [];
        this.companyListForFilter = [];
        const responseData = response.data;
        this.loader = false;
        responseData.rows.map((x: { companyName: any; id: any }): any => {
          if (x.companyName) {
            this.companyList.push({ id: x.id, companyName: x.companyName });
          }
          return null;
        });
        this.companyList.push({
          id: response?.parentCompany[0]?.id,
          companyName: response?.parentCompany[0]?.companyName,
        });
        const newCompanyArray = this.companyList.reduce(
          (arr: any[], item: { companyName: string }): any => {
            const exists = !!arr.find(
              (x: { companyName: string }): any => x.companyName.toLowerCase() === item.companyName.toLowerCase(),
            );
            if (!exists) {
              arr.push(item);
            }
            return arr;
          },
          [],
        );
        this.companyList = newCompanyArray;
        this.companyList.sort(
          (
            a: { companyName: { toLowerCase: () => number } },
            b: { companyName: { toLowerCase: () => number } },
          ): any => (a.companyName.toLowerCase() > b.companyName.toLowerCase() ? 1 : -1),
        );
        responseData.rows.map((x: { companyName: any }): any => {
          if (x.companyName) {
            this.companyListForFilter.push(x.companyName);
          }
          return null;
        });
        this.companyListForFilter.push(response?.parentCompany[0]?.companyName);
        const newCompanyFilterArray = this.companyListForFilter.reduce(
          (arr: any[], item: string): any => {
            const exists = !!arr.find((x: string): any => x.toLowerCase() === item.toLowerCase());
            if (!exists) {
              arr.push(item);
            }
            return arr;
          },
          [],
        );
        this.companyListForFilter = newCompanyFilterArray;
        this.companyListForFilter.sort(
          (a: { toLowerCase: () => number }, b: { toLowerCase: () => number }): any => (a.toLowerCase() > b.toLowerCase() ? 1 : -1),
        );
        this.filterModalLoader = false;
      }
    });
  }

  public getMembers(): void {
    this.loader = true;
    const param = {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.currentPageNo,
      ParentCompanyId: this.ParentCompanyId,
    };
    let payload: any = {};
    if (this.filterForm !== undefined) {
      payload = {
        companyFilter: this.filterForm.value.companyFilter,
        nameFilter: this.filterForm.value.nameFilter,
        roleFilter: +this.filterForm.value.roleFilter,
        statusFilter: this.filterForm.value.statusFilter ? this.filterForm.value.statusFilter.toLowerCase() : this.filterForm.value.statusFilter,
        search: this.search,
      };
    }
    payload.search = this.search;
    payload.sort = this.sort;
    payload.sortByField = this.sortColumn;
    this.projectService.listMember(param, payload).subscribe((response: any): void => {
      if (response) {
        const responseData = response.data;
        this.lastId = response.lastId;
        this.loader = false;
        this.memberList = responseData.rows;
        if (this.selectAll && this.memberList.length > 0) {
          this.memberList.map((obj: any, index: string | number): void => {
            this.memberList[index].isChecked = true;
            return null;
          });
        } else {
          this.selectAll = false;
          this.memberList.map((obj: any, index: string | number): void => {
            this.memberList[index].isChecked = false;
            return null;
          });
        }
        this.selectAll = false;
        this.totalCount = responseData.count;
      }
    });
  }

  public numberOnly(event: { which: any; keyCode: any }): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public getRoles(): void {
    this.projectService.getRoles().subscribe((response: any): void => {
      if (response) {
        this.roleList = response.data;
      }
    });
  }

  public changePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.getMembers();
  }

  public changePageNo(pageNo: number): void {
    this.currentPageNo = pageNo;
    this.getMembers();
  }

  public openDeactivateModal(template, data, event): void {
    this.memberData = data;
    if (!event) {
      this.modalRef = this.modalService.show(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-md new-gate-popup custom-modal deactivate-modal',
      });
      this.mappedRequestList = [];
      this.getMappedRequestLoader = true;
      const payload = {
        ProjectId: this.ProjectId,
        id: data.id,
      };
      this.projectService.getMemberMappedRequests(payload).subscribe((response: any): void => {
        if (response) {
          this.mappedRequestList = response.data.mappedRequest;
          this.allMemberList = response.data.members;
          this.getMappedRequestLoader = false;
        }
      });
    }
    if (event) {
      this.activateMember(data);
    }
  }

  public activateMember(data): void {
    this.memberData = data;
    const payload = {
      id: data.id,
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      isActive: true,
    };
    this.projectService.activateMember(payload).subscribe({
      next: (response): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.getMembers();
        }
      },
      error: (activateMemberErr): void => {
        if (activateMemberErr.message?.statusCode === 400) {
          this.showError(activateMemberErr);
        } else if (!activateMemberErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(activateMemberErr.message, 'OOPS!');
        }
      },
    });
  }

  public switchMember(data, memberId): void {
    const memberObject = data;
    if (data.requestType) {
      if (+data.memberDetails[0].Member.id !== +memberId) {
        memberObject.changedMemberId = +memberId;
      } else {
        memberObject.changedMemberId = null;
      }
    } else if (+data.controllUserDetails.id !== +memberId) {
      memberObject.changedMemberId = +memberId;
    } else {
      memberObject.changedMemberId = null;
    }
    return memberObject;
  }

  public deactivateMemberAction(): void {
    this.deactivateMemberLoader = true;
    let filterChangedMemberData = [];
    filterChangedMemberData = this.mappedRequestList.filter(
      (obj): any => obj.changedMemberId && obj.changedMemberId !== null,
    );
    if (
      filterChangedMemberData
      && filterChangedMemberData.length > 0
      && this.mappedRequestList.length === filterChangedMemberData.length
    ) {
      const payload = {
        id: this.memberData.id,
        memberSwitchedRequests: filterChangedMemberData,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.projectService.deactivateMember(payload).subscribe({
        next: (response): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.getMembers();
            filterChangedMemberData = [];
            this.modalRef.hide();
            this.deactivateMemberLoader = false;
          }
        },
        error: (deactivateGateErr): void => {
          if (deactivateGateErr.message?.statusCode === 400) {
            this.showError(deactivateGateErr);
          } else if (!deactivateGateErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deactivateGateErr.message, 'OOPS!');
          }
        },
      });
    } else if (this.mappedRequestList.length === 0) {
      const payload = {
        id: this.memberData.id,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.projectService.deactivateMember(payload).subscribe({
        next: (response): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.getMembers();
            this.modalRef.hide();
            this.deactivateMemberLoader = false;
          }
        },
        error: (deactivateGateErr): void => {
          if (deactivateGateErr.message?.statusCode === 400) {
            this.showError(deactivateGateErr);
          } else if (!deactivateGateErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deactivateGateErr.message, 'OOPS!');
          }
        },
      });
    } else {
      this.deactivateMemberLoader = false;
    }
  }

  public closeDeactivateMember(): void {
    this.modalRef.hide();
    this.getMembers();
  }

  public inviteForm(): void {
    this.inviteDetailsForm = this.formBuilder.group({
      RoleId: ['', Validators.compose([Validators.required])],
      CompanyId: ['', Validators.compose([Validators.required])],
      email: ['', Validators.compose([Validators.required])],
    });
  }

  public appendRole(roleId: any, member: any, roleName: any): any {
    this.isRoleCollapsed = true;
    this.onItemAdded(member, roleId, roleName);
  }

  public appendCompany(companyId: any, member: any, companyName: any): void {
    this.isCompanyCollapsed = true;
    this.onCompanyItemAdded(member, companyId, companyName);
  }

  public appendProfilePicture(userList: string | any[]): void {
    let currentUser: { email: any; id: any };
    let payload: { email: any; ProjectId: any; ParentCompanyId: any };
    if (userList && userList.length >= 1) {
      currentUser = userList[userList.length - 1];
      // tslint:disable-next-line: max-line-length
      const emailRegex =  /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (!emailRegex.test(String(currentUser.email).toLowerCase())) {
        this.toastr.clear();
        this.toastr.error('Please enter valid email');
        const e = currentUser.email;
        const removeInvalidEmail: any = this.inviteMemberList.filter((x): boolean => x.id === e);
        if (removeInvalidEmail) {
          this.inviteMemberList.pop();
        }
        return;
      }
      payload = {
        email: currentUser.email,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      // check item contents
      this.userService.checkUser(payload).subscribe((data: any): void => {
        const userResponse = data.existUser;
        const { isMemberExists } = data;
        if (isMemberExists) {
          this.toastr.clear();
          this.toastr.error(`Member ${currentUser.email} already exists in this project`);

          this.userExists = this.inviteMemberList.filter((x): any => x.id === currentUser.email);
          if (this.userExists.length) {
            this.inviteMemberList.pop();
          }
        }
        for (let index = 0; index < this.inviteMemberList?.length; index += 1) {
          if (this.inviteMemberList[index].id === currentUser.id) {
            const profileUrl = userResponse ? userResponse.profilePic : '';
            this.inviteMemberList[index].profilePic = profileUrl;
            this.inviteMemberList[index].companyDefaultName = 'Select Company';
            this.inviteMemberList[index].roleDefaultName = 'Select Role';
            break;
          }
        }
      });
    }
  }

  public onItemAdded(value: { id: any }, roleId: any, roleName: any): void {
    for (let index = 0; index < this.inviteMemberList?.length; index += 1) {
      if (this.inviteMemberList[index].id === value.id) {
        this.inviteMemberList[index].RoleId = roleId;
        this.inviteMemberList[index].roleName = roleName;
        this.inviteMemberList[index].ProjectId = this.ProjectId;
        this.inviteMemberList[index].ParentCompanyId = this.ParentCompanyId;
        const payload = this.inviteMemberList[index];
        this.userService.checkUser(payload).subscribe((data: any): void => {
          const { isRestrictedEmail } = data;
          if (isRestrictedEmail) {
            this.toastr.clear();
            this.toastr.error('Please use work email address to invite Project Admin');
            this.userExists = this.inviteMemberList.filter((x): any => x.email === payload.email);
            if (this.userExists) {
              this.inviteMemberList.pop();
            }
          }
        });
        break;
      }
    }
  }

  public onCompanyItemAdded(value: { id: any }, companyId: any, companyName: any): void {
    for (let index = 0; index < this.inviteMemberList?.length; index += 1) {
      if (this.inviteMemberList[index].id === value.id) {
        this.inviteMemberList[index].CompanyId = companyId;
        this.inviteMemberList[index].companyName = companyName;
        break;
      }
    }
  }

  public goToCompany(): void {
    this.modalRef.hide();
    this.router.navigate(['/companies']);
  }

  public openOptionModal(template: TemplateRef<any>): void {
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-md new-gate-popup custom-modal options-modal',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public onInviteSubmit(): void {
    this.submitted = true;
    this.invalidMemberForm = true;
    this.invalidMemberObject = 0;
    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
    this.inviteMemberList.forEach((list) => {
      // eslint-disable-next-line no-prototype-builtins
      if (!list.hasOwnProperty('RoleId') || !list.hasOwnProperty('CompanyId')) {
        this.invalidMemberObject += 1;
      }
    });

    if (this.invalidMemberObject >= 1) {
      this.invalidMemberForm = true;
    } else {
      this.invalidMemberForm = false;
    }

    if (this.invalidMemberForm === true) {
      return;
    }


    if (!this.invalidMemberForm) {
      const payload = {
        membersList: this.inviteMemberList,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
        requestType: 0, // web - 0 , app - 1
      };
      this.projectService.inviteMembers(payload).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Member Onboard Invite Sent');
            this.submitted = false;
            this.formSubmitted = false;
            this.resetAndClose();
            this.selectAll = false;
            this.getMembers();
            this.modalRef.hide();
          }
        },
        error: (inviteMembersErr): void => {
          this.submitted = false;
          this.resetAndClose();
          if (inviteMembersErr.message?.statusCode === 400) {
            this.showError(inviteMembersErr);
          } else if (!inviteMembersErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(inviteMembersErr.message, 'OOPS!');
          }
        },
      });
    }
  }

  public requestAutocompleteItems = (text: string): Observable<any> => {
    const param = {
      ParentCompanyId: this.ParentCompanyId,
      search: text,
      ProjectId: this.ProjectId,
    };
    return this.projectService.searchAllMember(param);
  };

  public editMemberForm(): void {
    this.memberEditForm = this.formBuilder.group({
      RoleId: ['', Validators.compose([Validators.required])],
      MemberId: [''],
      CompanyId: ['', Validators.compose([Validators.required])],
      email: [
        '',
        Validators.compose([
          Validators.required,
          Validators.pattern("^[a-zA-Z0-9._%+'-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$"),
        ]),
      ],
      firstName: ['', Validators.compose([Validators.required])],
      lastName: ['', Validators.compose([Validators.required])],
      phoneNumber: ['', Validators.compose([Validators.required])],
      phoneCode: ['', Validators.compose([Validators.required])],
    });
    this.memberEditForm.get('phoneCode').setValue('+1');
    this.changeMask(this.memberEditForm.value.phoneCode);
  }

  public changeMask(value: any): string {
    this.phoneMask = this.authService.checkDialCode(value);
    return this.phoneMask;
  }

  public resendEmailLink(data: {
    id: any;
    ParentCompanyId: any;
    User: { email: any };
    Role: { roleName: any };
  }): void {
    const payload = {
      memberId: data.id,
      ParentCompanyId: data.ParentCompanyId,
      email: data.User?.email,
      type: data.Role?.roleName,
      requestType: 0, // web - 0,app -1
    };
    this.projectService.resendInviteLink(payload).subscribe((response: any): void => {
      if (response) {
        this.toastr.success(response.message);
        this.mixpanelService.addMixpanelEvents('Member Onboard Invite Resent');
      }
    });
  }

  public getCountryCode(): void {
    Object.keys(countryCodes).forEach((key): void => {
      const countryName = countryNames[key];
      this.countryCode.push({ countryDialCode: key, name: countryName });
    });
    this.countryCode.sort((a, b): number => (a.name > b.name ? 1 : -1));
  }

  public onEditSubmit(): any {
    this.editSubmitted = true;
    this.formEditSubmitted = true;
    if (this.memberEditForm.invalid) {
      this.formEditSubmitted = false;
      return;
    }
    const formValue = this.memberEditForm.value;
    if (!this.checkStringEmptyValues(formValue)) {
      const payload = {
        id: this.memberList[this.editIndex].id,
        firstName: formValue.firstName.trim(),
        lastName: formValue.lastName.trim(),
        email: formValue.email,
        phoneNumber: formValue.phoneNumber,
        phoneCode: formValue.phoneCode,
        ProjectId: this.ProjectId,
        RoleId: formValue.RoleId,
        CompanyId: formValue.CompanyId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.projectService.editMember(payload).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Edited Member');
            this.editSubmitted = false;
            this.formEditSubmitted = false;
            this.memberEditForm.reset();
            this.selectAll = false;
            this.getMembers();
            this.getOverView();
            this.modalRef.hide();
          }
        },
        error: (editMemberErr): void => {
          this.editSubmitted = false;
          this.formEditSubmitted = false;
          if (editMemberErr.message?.statusCode === 400) {
            this.showError(editMemberErr);
            this.formEditSubmitted = false;
          } else if (!editMemberErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(editMemberErr.message, 'OOPS!');
          }
        },
      });
    } else {
      this.toastr.error('Please enter valid Name.', 'OOPS!');
      this.editSubmitted = false;
      this.formEditSubmitted = false;
    }
  }

  public getOverView(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.profileService.getOverView(param).subscribe((response: any): void => {
      if (response) {
        this.userData = response.data;
        this.profileService.updatedOverView(this.userData);
      }
    });
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.editSubmitted = false;
    this.formSubmitted = false;
    this.formEditSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public openModal(
    template: TemplateRef<any>,
    action: string,
    chosenValue: { CompanyId: any },
  ): void {
    if (action !== 'delete') {
      this.getCompanies();
    }
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg members-popup custom-modal',
    };
    setTimeout((): void => {
      this.modalRef = this.modalService.show(template, data);
    }, 100);
    if (action === 'create') {
      setTimeout((): void => {
        this.createNewModalLoader = false;
      }, 3500);
    }
    if (action === 'edit') {
      setTimeout((): void => {
        this.memberEditForm.get('CompanyId').setValue(chosenValue.CompanyId);
        this.modalLoader = false;
      }, 3500);
    }
  }

  public openModal1(template: TemplateRef<any>): void {
    this.filterModalLoader = true;
    this.getCompanies();
    setTimeout((): void => {
      this.modalRef = this.modalService.show(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-sm filter-popup custom-modal',
      });
    }, 100);
  }

  public close(template: TemplateRef<any>, form: string): void {
    if (form === 'addMember') {
      if (
        (this.inviteDetailsForm.dirty
          && this.inviteDetailsForm.get('email').value
          && this.inviteDetailsForm.get('email').value.length > 0)
        || this.inviteDetailsForm.touched
      ) {
        this.openModalPopup(template);
      } else {
        this.cancelForm('yes');
      }
    }
    if (form === 'editMember') {
      if (this.memberEditForm.dirty && this.memberEditForm.touched) {
        this.openModalPopup(template);
      } else {
        this.cancelForm('yes');
      }
    }
    if (form === 'deactivateMember') {
      this.modalRef.hide();
    }
  }

  public copyInputMessage(inputElement: HTMLInputElement): void {
    const textToCopy = inputElement.value;
    navigator.clipboard.writeText(textToCopy)
      .then(() => {
        this.toastr.success('Invite link copied successfully');
      })
      .catch((err) => {
        this.toastr.error('Failed to copy invite link');
      });
  }

  public closeCopyLinkPopup(): void {
    this.copyInviteLinkPopup.hide();
    this.onboardingInviteLink = '';
  }

  public openModalPopup(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public cancelForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.inviteDetailsForm.reset();
      this.inviteMemberList = [];
      this.memberEditForm.reset();
      this.inviteDetailsForm.get('RoleId').setValue('');
      this.modalRef.hide();
    }
  }

  public openDeleteModal(index: number, template: TemplateRef<any>): void {
    if (index !== -1) {
      this.deleteIndex[0] = this.memberList[index].id;
      this.currentDeleteId = index;
      this.remove = false;
    } else if (index === -1) {
      this.remove = true;
    }
    this.openModal(template, 'delete', null);
  }

  public openWaitModal(template: TemplateRef<any>, template1: TemplateRef<any>, data): void {
    let data1 = {};
    data1 = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.linkGeneratingWaitingModal = this.modalService.show(template, data1);
    const payload = {
      ProjectId: data.ProjectId,
      ParentCompanyId: data.ParentCompanyId,
      id: data.id,
      UserId: data.UserId,
      email: data.User.email,
    };
    this.projectService.getOnboardingInviteLink(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.linkGeneratingWaitingModal.hide();
          let data2 = {};
          data2 = {
            keyboard: false,
            class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
          };
          this.copyInviteLinkPopup = this.modalService.show(template1, data2);
          this.onboardingInviteLink = response.data.link;
        }
      },
      error: (getOnboardingInviteLinkError): void => {
        this.deleteSubmitted = false;
        this.getMembers();
        if (getOnboardingInviteLinkError.message?.statusCode === 400) {
          this.showError(getOnboardingInviteLinkError);
        } else if (!getOnboardingInviteLinkError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(getOnboardingInviteLinkError.message, 'OOPS!');
        }
      },
    });
  }

  public checkSelectedRow(): boolean {
    if (this.selectAll) {
      return false;
    }
    const index = this.memberList.findIndex(
      (item: { isChecked: boolean }): boolean => item.isChecked === true,
    );
    if (index !== -1) {
      return false;
    }
    return true;
  }

  public setSelectedItem(index: string | number): void {
    this.memberList[index].isChecked = !this.memberList[index].isChecked;
  }

  public deleteMember(): void {
    this.deleteSubmitted = true;
    this.projectService
      .deleteMember({
        id: this.deleteIndex,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
        isSelectAll: this.selectAll,
      })
      .subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Deleted Member');
            this.getMembers();
            this.deleteSubmitted = false;
            this.selectAll = false;
            this.modalRef.hide();
            this.deleteIndex = [];
          }
        },
        error: (deleteMemberError): void => {
          this.deleteSubmitted = false;
          this.getMembers();
          if (deleteMemberError.message?.statusCode === 400) {
            this.showError(deleteMemberError);
          } else if (!deleteMemberError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deleteMemberError.message, 'OOPS!');
          }
        },
      });
  }

  public resetAndClose(): void {
    if (this.inviteDetailsForm) {
      this.inviteDetailsForm.reset();
      this.inviteDetailsForm.get('RoleId').setValue('');
    }
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  public selectAllMemberData(): void {
    this.selectAll = !this.selectAll;
    if (this.selectAll) {
      this.memberList.map((obj: any, index: string | number): void => {
        this.memberList[index].isChecked = true;
        return null;
      });
    } else {
      this.memberList.map((obj: any, index: string | number): void => {
        this.memberList[index].isChecked = false;
        return null;
      });
    }
  }

  public setCompany(template: TemplateRef<any>): void {
    this.createNewModalLoader = true;
    this.memberEditForm.get('CompanyId').setValue('');
    this.openModal(template, 'create', null);
  }

  public removeItem(): void {
    this.deleteSubmitted = true;
    if (this.selectAll) {
      this.deleteMember();
    } else {
      this.memberList.forEach((element: { isChecked: any; id: any }): void => {
        if (element.isChecked) {
          this.deleteIndex.push(element.id);
        }
      });
      this.deleteMember();
    }
  }

  public openEditModal(index: string | number, template: TemplateRef<any>): void {
    this.modalLoader = true;
    this.editIndex = index;
    this.memberEditForm.get('phoneCode').setValue(this.memberList[index].phoneCode);
    this.memberEditForm.get('phoneNumber').setValue(this.memberList[index].phoneNumber);
    this.memberEditForm.get('firstName').setValue(this.memberList[index].User.firstName);
    this.memberEditForm.get('lastName').setValue(this.memberList[index].User.lastName);
    this.memberEditForm.get('email').setValue(this.memberList[index].User.email);
    this.memberEditForm.get('RoleId').setValue(this.memberList[index].RoleId);
    this.changeMask(this.memberList[index].phoneCode);
    this.openModal(template, 'edit', this.memberList[index]);
  }

  public ngOnInit(): void { /* */ }

  ngAfterViewInit() {
    this.getMembers();
  }
}
