import { TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ProfileService } from './profile.service';
import { ApiService } from '../api_base/api.service';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';

describe('ProfileService', (): void => {
  let service: ProfileService;
  let apiServiceMock: jest.Mocked<ApiService>;
  let routerMock: jest.Mocked<Router>;

  beforeEach((): void => {
    // Create mocks
    apiServiceMock = {
      get: jest.fn(),
      post: jest.fn(),
      requestImage: jest.fn()
    } as unknown as jest.Mocked<ApiService>;

    routerMock = {
      navigate: jest.fn()
    } as unknown as jest.Mocked<Router>;

    TestBed.configureTestingModule({
      imports: [
        RouterTestingModule.withRoutes([]),
        HttpClientTestingModule,
      ],
      providers: [
        ProfileService,
        { provide: ApiService, useValue: apiServiceMock },
        { provide: Router, useValue: routerMock }
      ]
    });

    service = TestBed.inject(ProfileService);
  });

  it('should be created', (): void => {
    expect(service).toBeTruthy();
  });

  describe('BehaviorSubject methods', (): void => {
    it('should update overview data', (): void => {
      // Arrange
      const testData = {
        id: 1,
        name: 'Test User',
        company: 'Test Company'
      };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.overViewDetail.subscribe(data => {
        result = data;
      });

      // Act
      service.updatedOverView(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update refresh overview data', (): void => {
      // Arrange
      const testData = { refresh: true };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.refreshData.subscribe(data => {
        result = data;
      });

      // Act
      service.updatedRefreshOverview(testData);

      // Assert
      expect(result).toEqual(testData);
    });
  });

  describe('API methods', (): void => {
    it('should call getOverView with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: 123,
        ParentCompanyId: 456
      };
      const mockResponse = {
        data: {
          user: {
            id: 1,
            name: 'Test User',
            email: '<EMAIL>'
          },
          company: {
            id: 456,
            name: 'Test Company'
          }
        }
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getOverView(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `member/get_overview_detail/${mockParams.ProjectId}/${mockParams.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call cancelSubscription with correct parameters', (): void => {
      // Arrange
      const mockParams = { ProjectId: 123 };
      const mockResponse = {
        success: true,
        message: 'Subscription cancelled successfully'
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.cancelSubscription(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `payment/cancel_subscription/${mockParams.ProjectId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call updateProfile with correct parameters', (): void => {
      // Arrange
      const mockData = {
        id: 1,
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phoneNumber: '1234567890'
      };
      const mockResponse = {
        success: true,
        message: 'Profile updated successfully'
      };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateProfile(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('member/update_profile', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call uploadProfile with correct parameters', (): void => {
      // Arrange
      const mockData = new FormData();
      mockData.append('file', new Blob(['test'], { type: 'image/jpeg' }), 'test.jpg');

      const mockResponse = {
        success: true,
        message: 'Profile picture uploaded successfully',
        data: {
          imageUrl: 'https://example.com/images/profile.jpg'
        }
      };

      apiServiceMock.requestImage.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.uploadProfile(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.requestImage).toHaveBeenCalledWith('user/upload_profile/', mockData);
      expect(result).toEqual(mockResponse);
    });

    it('should call getProjectList with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        pageSize: 10,
        pageNo: 1,
        ParentCompanyId: 456
      };
      const mockPayload = {
        searchText: '',
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };
      const mockResponse = {
        data: {
          projects: [
            { id: 1, name: 'Project 1' },
            { id: 2, name: 'Project 2' }
          ],
          totalCount: 2
        }
      };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getProjectList(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `project/get_projects_list/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.ParentCompanyId}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getPlansAndProjects with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        pageSize: 10,
        pageNo: 1,
        sort: 'asc',
        sortByField: 'name'
      };
      const mockPayload = {
        searchText: '',
        projectIds: [1, 2, 3]
      };
      const mockResponse = {
        data: {
          plans: [
            { id: 1, name: 'Plan 1', price: 9.99 },
            { id: 2, name: 'Plan 2', price: 19.99 }
          ],
          projects: [
            { id: 1, name: 'Project 1', planId: 1 },
            { id: 2, name: 'Project 2', planId: 2 },
            { id: 3, name: 'Project 3', planId: 1 }
          ],
          totalCount: 3
        }
      };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getPlansAndProjects(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `project/get_plans_projects/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.sort}/${mockParams.sortByField}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Error handling', (): void => {
    it('should propagate errors from the API service', (done): void => {
      // Arrange
      const mockParams = {
        ProjectId: 123,
        ParentCompanyId: 456
      };
      const mockError = 'API Error';

      apiServiceMock.get.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.getOverView(mockParams).subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(mockError);
          done();
        }
      });
    });
  });

  describe('Service dependencies', (): void => {
    it('should have ApiService injected', (): void => {
      // This test verifies that the ApiService is properly injected
      expect(TestBed.inject(ApiService)).toBeDefined();
    });

    it('should have Router injected', (): void => {
      // This test verifies that the Router is properly injected
      expect(TestBed.inject(Router)).toBeDefined();
    });
  });

  describe('Service initialization', (): void => {
    it('should initialize BehaviorSubjects with default values', (): void => {
      // Verify that BehaviorSubjects are initialized with empty objects
      let overViewDetailValue: any;
      let refreshDataValue: any;

      service.overViewDetail.subscribe(value => {
        overViewDetailValue = value;
      });

      service.refreshData.subscribe(value => {
        refreshDataValue = value;
      });

      expect(overViewDetailValue).toEqual({});
      expect(refreshDataValue).toEqual({});
    });
  });
});
