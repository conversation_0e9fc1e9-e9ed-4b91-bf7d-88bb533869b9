<section class="page-section">
  <div class="page-card bg-white rounded mb-3">
    <div class="row">
      <div class="col-lg-5 col-md-5">
        <form
          class="custom-material-form my-4"
          [formGroup]="changePasswordForm"
          (ngSubmit)="changePassword()"
          novalidate
        >
          <div class="mb-5">
            <div class="input-group">
                <span class="input-group-text pb5">
                  <img
                    src="./assets/images/noun_password_1439121.svg"
                    width="14"
                    class="form-icon me-2"
                    alt="password"
                  />
                </span>
              <input
                type="{{ togglePassword ? 'text' : 'password' }}"
                class="form-control material-input fs12"
                placeholder="Current Password"
                formControlName="currentPassword"
              />
                <span class="input-group-text pb5" (click)="passwordToggle()" (keydown)="handleDownKeydown($event,'password')"
                  ><em
                    class="fas color-grey4 fs12 eye-cursor"
                    [ngClass]="{ 'fa-eye-slash': !togglePassword, 'fa-eye': togglePassword }"
                  ></em
                ></span>
            </div>
            <div
              class="color-red"
              *ngIf="changeFormSubmitted && changePasswordForm.get('currentPassword').errors"
            >
              <small *ngIf="changePasswordForm.get('currentPassword').errors.required"
                >*Current password is required.</small
              >
            </div>
          </div>

          <div class="mb-5">
            <div class="input-group">
                <span class="input-group-text pb5"
                  ><img
                    src="./assets/images/noun_password_1439121.svg"
                    class="form-icon me-2"
                    width="14"
                    alt="password"
                /></span>
              <input
                type="{{ newTogglePassword ? 'text' : 'password' }}"
                class="form-control material-input fs12"
                placeholder="New Password"
                formControlName="newPassword" id="npassword"
                (keyup)="passwordValid($event)"
              />
                <span class="input-group-text pb5" (click)="newPasswordToggle()" (keydown)="handleDownKeydown($event,'newpassword')"
                  ><em
                    class="fas color-grey4 fs12 eye-cursor"
                    [ngClass]="{ 'fa-eye-slash': !newTogglePassword, 'fa-eye': newTogglePassword }"
                  ></em
                ></span>
            </div>
            <div
              class="color-red"
              *ngIf="changeFormSubmitted && changePasswordForm.get('newPassword')?.errors"
            >
              <small *ngIf="changePasswordForm.get('newPassword')?.errors.required"
                >*New Password is Required</small
              >
            </div>
            <div *ngIf="changePasswordForm.get('newPassword')?.value?.length > 0">
              <div>
              <label for="npassword"
                class="col ps-0"
                [ngClass]="
                  changePasswordForm.controls['newPassword'].hasError('required') ||
                  changePasswordForm.controls['newPassword'].hasError('hasNumber')
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                <p class="d-flex mb-0">
                  <span
                    ><img
                      alt="tick"
                      [src]="
                        changePasswordForm.controls['newPassword'].hasError('required') ||
                        changePasswordForm.controls['newPassword'].hasError('hasNumber')
                          ? './assets/images/red-tick.svg'
                          : './assets/images/green-tick.svg'
                      "
                  /></span>
                  <small class="d-block my-1 ms-3 responsive-font-validation"
                    >At least 1 number</small
                  >
                </p>
              </label>
            </div>
            <div>
              <label for="npassword"
                class="col ps-0"
                [ngClass]="
                  changePasswordForm.controls['newPassword'].hasError('required') ||
                  changePasswordForm.controls['newPassword'].hasError('minlength')
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                <p class="d-flex mb-0">
                  <span
                    ><img
                      alt="tick"
                      [src]="
                        changePasswordForm.controls['newPassword'].hasError('required') ||
                        changePasswordForm.controls['newPassword'].hasError('minlength')
                          ? './assets/images/red-tick.svg'
                          : './assets/images/green-tick.svg'
                      "
                  /></span>
                  <small class="d-block my-1 ms-3 responsive-font-validation"
                    >Password must contain at least 8 characters</small
                  >
                </p>
              </label>
            </div>
            <div>
              <label for="npassword"
                class="col ps-0"
                [ngClass]="
                  changePasswordForm.controls['newPassword'].hasError('required') ||
                  changePasswordForm.controls['newPassword'].hasError('hasCapitalCase')
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                <p class="d-flex mb-0">
                  <span
                    ><img
                      alt="tick"
                      [src]="
                        changePasswordForm.controls['newPassword'].hasError('required') ||
                        changePasswordForm.controls['newPassword'].hasError('hasCapitalCase')
                          ? './assets/images/red-tick.svg'
                          : './assets/images/green-tick.svg'
                      "
                  /></span>
                  <small class="d-block my-1 ms-3 responsive-font-validation"
                    >At least 1 uppercase letter</small
                  >
                </p>
              </label>
            </div>
            <div>
              <label for="npassword"
                class="col ps-0"
                [ngClass]="
                  changePasswordForm.controls['newPassword'].hasError('required') ||
                  changePasswordForm.controls['newPassword'].hasError('hasSmallCase')
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                <p class="d-flex mb-0">
                  <span
                    ><img
                      alt="tick"
                      [src]="
                        changePasswordForm.controls['newPassword'].hasError('required') ||
                        changePasswordForm.controls['newPassword'].hasError('hasSmallCase')
                          ? './assets/images/red-tick.svg'
                          : './assets/images/green-tick.svg'
                      "
                  /></span>
                  <small class="d-block my-1 ms-3 responsive-font-validation"
                    >At least 1 lowercase letter</small
                  >
                </p>
              </label>
            </div>
            <div>
              <label for="npassword"
                class="col ps-0"
                [ngClass]="
                  changePasswordForm.controls['newPassword'].hasError('required') ||
                  changePasswordForm.controls['newPassword'].hasError('hasSpecialCharacters')
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                <p class="d-flex mb-0">
                  <span
                    ><img
                      alt="tick"
                      [src]="
                        changePasswordForm.controls['newPassword'].hasError('required') ||
                        changePasswordForm.controls['newPassword'].hasError('hasSpecialCharacters')
                          ? './assets/images/red-tick.svg'
                          : './assets/images/green-tick.svg'
                      "
                  /></span>
                  <small class="d-block my-1 ms-3 responsive-font-validation"
                    >At least 1 special character(!#_$%*)</small
                  >
                </p>
              </label>
            </div>
            </div>
          </div>
          <div class="mb-5">
            <div class="input-group">
                <span class="input-group-text pb5"
                  ><img
                    src="./assets/images/noun_password_1439121.svg"
                    alt="password"
                    width="14"
                    class="form-icon me-2"
                /></span>
              <input
                type="{{ confirmTogglePassword ? 'text' : 'password' }}"
                class="form-control material-input fs12"
                placeholder="Confirm Password"
                formControlName="confirmPassword"
              />
                <span class="input-group-text pb5" (click)="confirmPasswordToggle()" (keydown)="handleDownKeydown($event,'cpassword')"
                  ><em
                    class="fas color-grey4 fs12 eye-cursor"
                    [ngClass]="{
                      'fa-eye-slash': !confirmTogglePassword,
                      'fa-eye': confirmTogglePassword
                    }"
                  ></em
                ></span>
            </div>
            <div
              class="color-red"
              *ngIf="changeFormSubmitted && changePasswordForm.get('confirmPassword').errors"
            >
              <small *ngIf="changePasswordForm.get('confirmPassword').errors.required"
                >*Confirm Password is required.</small
              >
              <small *ngIf="changePasswordForm.get('confirmPassword').errors.mustMatch">
                *Passwords must match</small
              >
            </div>
          </div>

          <div class="clearfix"></div>
          <div class="d-flex justify-content-end">
            <button
              class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem"
              type="submit"
              [disabled]="formSubmitted && changePasswordForm.valid"
            >
              <em
                class="fa fa-spinner"
                aria-hidden="true"
                *ngIf="formSubmitted && changePasswordForm.valid"
              ></em
              >Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>
