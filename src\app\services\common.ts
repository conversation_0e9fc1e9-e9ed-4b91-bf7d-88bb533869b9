export const recurrence = [
  { type: 0, value: 'Does Not Repeat' },
  { type: 1, value: 'Daily' },
  { type: 2, value: 'Weekly' },
  { type: 3, value: 'Monthly' },
  { type: 3, value: 'Yearly' },
];

export const repeatWithSingleRecurrence = [
  { type: 0, value: 'Day' },
  { type: 1, value: 'Week' },
  { type: 2, value: 'Month' },
  { type: 3, value: 'Year' },
];

export const repeatWithMultipleRecurrence = [
  { type: 0, value: 'Days' },
  { type: 1, value: 'Weeks' },
  { type: 2, value: 'Months' },
  { type: 3, value: 'Years' },
];

  export const inspectionType = ['Material',
  'Quality Control',
  'Special Inspection',
  'Equipment',
  'Safety',
  'Other']

  export const weekDays: any = [
    {
      display: 'S',
      value: 'Sunday',
      data: 'SUN',
      isDisabled: false,
    },
    {
      display: 'M',
      value: 'Monday',
      data: 'MON',
      isDisabled: false,
    },
    {
      display: 'T',
      value: 'Tuesday',
      data: 'TUE',
      isDisabled: false,
    },
    {
      display: 'W',
      value: 'Wednesday',
      data: 'WED',
      isDisabled: false,
    },
    {
      display: 'T',
      value: 'Thursday',
      data: 'THU',
      isDisabled: false,
    },
    {
      display: 'F',
      value: 'Friday',
      data: 'FRI',
      isDisabled: false,
    },
    {
      display: 'S',
      value: 'Saturday',
      data: 'SAT',
      isDisabled: false,
    },
  ];

export const editRecurrence = [
  { type: 0, value: 'Daily' },
  { type: 1, value: 'Weekly' },
  { type: 2, value: 'Monthly' },
  { type: 2, value: 'Yearly' },
];
