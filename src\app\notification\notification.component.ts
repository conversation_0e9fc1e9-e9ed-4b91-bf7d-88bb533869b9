/* eslint-disable max-lines-per-function */
import { Component, OnInit, TemplateRef } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { Socket } from 'ngx-socket-io';
import { Title } from '@angular/platform-browser';
import { DeliveryService } from '../services/profile/delivery.service';
import { ProjectService } from '../services/profile/project.service';
import { EditDeliveryFormComponent } from '../delivery-requests/delivery-details/edit-delivery-form/edit-delivery-form.component';
import { DeliveryDetailsNewComponent } from '../delivery-requests/delivery-details/delivery-details-new/delivery-details-new.component';
import { CraneRequestDetailViewHeaderComponent } from '../crane-requests/crane-request-detail-view-header/crane-request-detail-view-header.component';
import { ConcreteDetailHeaderComponent } from '../concrete-request/concrete-detail-header/concrete-detail-header.component';


@Component({
  selector: 'app-notification',
  templateUrl: './notification.component.html',
  })
export class NotificationComponent implements OnInit {
  public modalRef: BsModalRef;

  public currentPageNo = 1;

  public notificationList: any = [];

  public loader = true;

  public filterForm: UntypedFormGroup;

  public search = '';

  public pageSize = 25;

  public pageNo = 1;

  public totalCount = 0;

  public deleteSubmitted = false;

  public deleteNotification: any = {};

  public wholeStatus = ['Approved', 'Declined', 'Delivered'];

  public filterCount = 0;

  public projectList: any = [];

  public authUser: any = {};

  public statusValue: any = [];

  public showStatus = false;

  public DeliveryRequestId: any;

  public currentDeliverySaveItem: any = {};

  public currentStatus = '';

  public statusSubmitted = false;

  public voidSubmitted = false;

  public ProjectId;

  public void = false;

  public show = false;

  public ParentCompanyId;

  public accountAdmin = false;

  public myAccount = false;

  public showSearchbar = false;

  public unReadCount = 0;


  public constructor(private readonly modalService: BsModalService,
    public deliveryService: DeliveryService,
    public socket: Socket, public formBuilder: UntypedFormBuilder,
    public router: Router, private readonly titleService: Title,
    public toastr: ToastrService, public projectService: ProjectService) {
    this.titleService.setTitle('Follo - Notifications');
    this.deliveryService.refresh.subscribe((getNdrResponse5): void => {
      if (getNdrResponse5 !== undefined && getNdrResponse5 !== null && getNdrResponse5 !== '') {
        this.getNotification();
      }
    });
    this.deliveryService.refresh1.subscribe((getNdrResponse5): void => {
      if (getNdrResponse5 !== undefined && getNdrResponse5 !== null && getNdrResponse5 !== '') {
        this.getNotification();
      }
    });
    this.deliveryService.inspectionUpdated.subscribe((getNdrResponse5): void => {
      if (getNdrResponse5 !== undefined && getNdrResponse5 !== null && getNdrResponse5 !== '') {
        this.getNotification();
      }
    });
    this.deliveryService.inspectionUpdated1.subscribe((getNdrResponse5): void => {
      if (getNdrResponse5 !== undefined && getNdrResponse5 !== null && getNdrResponse5 !== '') {
        this.getNotification();
      }
    });
    this.deliveryService.fetchData.subscribe((getRequestResponse5): void => {
      if (getRequestResponse5 !== undefined && getRequestResponse5 !== null && getRequestResponse5 !== '') {
        this.getNotification();
      }
    });
    this.deliveryService.fetchData1.subscribe((getRequestResponse5): void => {
      if (getRequestResponse5 !== undefined && getRequestResponse5 !== null && getRequestResponse5 !== '') {
        this.getNotification();
      }
    });
    this.deliveryService.fetchConcreteData.subscribe((getRequestResponse5): void => {
      if (getRequestResponse5 !== undefined && getRequestResponse5 !== null && getRequestResponse5 !== '') {
        this.getNotification();
      }
    });
    this.deliveryService.fetchConcreteData1.subscribe((getRequestResponse5): void => {
      if (getRequestResponse5 !== undefined && getRequestResponse5 !== null && getRequestResponse5 !== '') {
        this.getNotification();
      }
    });
    this.projectService.projectParent.subscribe((response12): void => {
      if (response12 !== undefined && response12 !== null && response12 !== '') {
        this.ProjectId = response12.ProjectId;
        this.ParentCompanyId = response12.ParentCompanyId;
        this.accountAdmin = false;
        this.getNotification();
      }
    });
    this.projectService.isAccountAdmin.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        if (!this.ProjectId || this.ProjectId === -1) {
          this.accountAdmin = res;
          this.getNotification();
        }
      }
    });
    this.projectService.ParentCompanyId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ParentCompanyId = res;
        this.getProjects();
      }
    });
    this.projectService.clearProject.subscribe((res1): void => {
      const data = { ...res1 };
      if (data !== undefined && data !== null && data !== '') {
        if (data.status === true) {
          this.ProjectId = -1;
        }
      }
    });
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
        if (this.authUser.RoleId === 2) {
          this.statusValue = ['Approved', 'Declined'];
        } else if (this.authUser.RoleId === 3) {
          this.statusValue = ['Delivered'];
        }
      }
    });
    this.deliveryService.getCurrentStatus.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.DeliveryRequestId = res;
        this.getNDR();
      }
    });
    this.projectService.isMyAccount.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.myAccount = true;
      }
    });
    this.projectService.isProject.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.myAccount = false;
      }
    });
    this.filterDetailsForm();
    this.getProjects();
    this.getUnReadNotification();
  }

  public ngOnInit(): void { /* */ }

  public handleToggleKeydown(event: KeyboardEvent, data: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.openIdModal(data);
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'clear':
          this.clear();
          break;
        case 'open':
          this.openModal1(data);
          break;
        case 'setRead':
          this.setRead(data, item);
          break;
        default:
          break;
      }
    }
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group(
      {

        dateFilter: [
          '',
        ],
        projectNameFilter: [
          '',
        ],
        descriptionFilter: [
          '',
        ],
        statusFilter: [
          '',
        ],
      },
    );
  }

  public getProjects(): void {
    this.projectService.getProject().subscribe((response): void => {
      this.projectList = response.data;
    });
  }

  public setDeleteContent(template: TemplateRef<any>, data): void {
    this.deleteNotification = data;
    this.openModal(template);
  }

  public selectStatus(status): void {
    this.currentStatus = status;
  }

  public close(): void {
    this.modalRef.hide();
  }

  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-lg new-delivery-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }

  public openIdModal(item): void {
    if (item.requestType === 'craneRequest') {
      const newPayload = {
        id: item.CraneRequest.CraneRequestId,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      const initialState = {
        data: newPayload,
        title: 'Modal with component',
      };
      this.modalRef = this.modalService.show(CraneRequestDetailViewHeaderComponent, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal',
        initialState,
      });
    } else if (item.requestType === 'deliveryRequest') {
      const newPayload = {
        id: item.DeliveryRequestId,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      const initialState = {
        data: newPayload,
        title: 'Modal with component',
      };
      this.modalRef = this.modalService.show(DeliveryDetailsNewComponent, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal',
        initialState,
      });
    } else if (item.requestType === 'concreteRequest') {
      const newPayload = {
        id: item.ConcreteRequest.ConcreteRequestId,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      const initialState = {
        data: newPayload,
        title: 'Modal with component',
      };
      this.modalRef = this.modalService.show(ConcreteDetailHeaderComponent, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal',
        initialState,
      });
    }
    this.modalRef.content.closeBtnName = 'Close';
  }

  public setStatus(item): void {
    this.DeliveryRequestId = item.DeliveryRequest.id;
    this.ProjectId = item.Project.id;
    this.deliveryService.updatedDeliveryId(this.DeliveryRequestId);
    this.getNDR();
  }

  public getNDR(): void {
    const param = {
      DeliveryRequestId: this.DeliveryRequestId,
    };
    this.void = false;
    this.show = false;
    this.deliveryService.getNDRData(param).subscribe((res): void => {
      this.currentDeliverySaveItem = res.data;
      const item = this.currentDeliverySaveItem;
      const authId = this.authUser.id;
      const voidData = this.currentDeliverySaveItem.voidList;
      const voidIndex = voidData.findIndex((i): boolean => (i.MemberId === authId));
      if (voidIndex !== -1) {
        this.void = true;
      }
      this.show = true;
      if ((item.status === 'Pending' && this.authUser.RoleId === 2) || (this.authUser.RoleId === 3 && item.status === 'Approved')) {
        this.showStatus = true;
      }
    });
  }

  public openEditModal(item): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.deliveryService.updatedDeliveryId(item.id);
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(EditDeliveryFormComponent, { backdrop: 'static', keyboard: false, class: className });
    this.modalRef.content.closeBtnName = 'Close';
  }

  public addToVoid(): void {
    if (!this.voidSubmitted) {
      this.voidSubmitted = true;
      const currentDeliveryItem = this.currentDeliverySaveItem;
      const data = {
        DeliveryRequestId: currentDeliveryItem.id,
        ProjectId: this.ProjectId,
      };
      this.deliveryService.createVoid(data).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.voidSubmitted = false;
            this.router.navigate(['/void-list']);
            if (this.modalRef) {
              this.modalRef.hide();
            }
          }
        },
        error: (notificationError): void => {
          this.voidSubmitted = false;
          if (notificationError.message?.statusCode === 400) {
            this.showError(notificationError);
          } else if (!notificationError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(notificationError.message, 'OOPS!');
          }
        },
      });
    }
  }


  public delete(): void {
    this.deleteSubmitted = true;
    this.deliveryService.deleteNotification({
      id: this.deleteNotification.id,
      ParentCompanyId: this.ParentCompanyId,
    }).subscribe({
      next: (response: any): void => {
        this.deleteSubmitted = false;
        if (this.modalRef) {
          this.modalRef.hide();
        }
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.getNotification();
        }
      },
      error: (deleteNotificationErr): void => {
        if (deleteNotificationErr.message?.statusCode === 400) {
          this.showError(deleteNotificationErr);
        } else if (!deleteNotificationErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(deleteNotificationErr.message, 'OOPS!');
        }
      },
    });
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('descriptionFilter').value !== '') { this.filterCount += 1; }
    if (this.filterForm.get('projectNameFilter').value !== '') { this.filterCount += 1; }
    if (this.filterForm.get('dateFilter').value !== '') { this.filterCount += 1; }
    if (this.filterForm.get('statusFilter').value !== '') { this.filterCount += 1; }
    this.pageNo = 1;
    this.getNotification();
    this.modalRef.hide();
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.search = '';
    this.pageNo = 1;
    this.filterDetailsForm();
    this.getNotification();
    this.modalRef.hide();
  }


  public getNotification(): void {
    this.notificationList = [];
    const param: any = {
      pageSize: this.pageSize,
      pageNo: this.pageNo,
    };
    let payload: any = {};
    if (this.filterForm !== undefined) {
      payload = {
        descriptionFilter: this.filterForm.value.descriptionFilter,
        projectNameFilter: +this.filterForm.value.projectNameFilter,
        dateFilter: new Date(this.filterForm.value.dateFilter),
        statusFilter: this.filterForm.value.statusFilter.toLowerCase(),
        search: this.search,
      };
    }
    payload.search = this.search;
    if (!this.accountAdmin) {
      payload.ProjectId = this.ProjectId;
    }
    payload.ParentCompanyId = this.ParentCompanyId;
    if (this.ProjectId && this.ParentCompanyId) {
      this.loader = true;
      this.deliveryService.listNotification(param, payload).subscribe((response: any): void => {
        if (response) {
          const responseData = response.data;
          this.loader = false;
          this.notificationList = responseData.rows;
          this.totalCount = responseData.count;
        }
      });
    }
  }

  public getUnReadNotification(): void {
    let params = {};
    if (!this.myAccount) {
      params = {
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
    }
    this.projectService.unReadCount(params).subscribe((res): void => {
      this.unReadCount = res.data;
    });
  }

  public markall(): void{
    const param: any = {
      ProjectId: this.ProjectId,
    };
    this.deliveryService.ReadallNotification(param).subscribe((response: any): void => {
      this.deliveryService.notificationRefreshCount(true);
      this.getNotification();
      this.getUnReadNotification();
    });
  }

  public setRead(data, i): void {
    if (!data?.DeliveryNotification[0]?.seen) {
      this.deliveryService.setReadNotification({
        id: data?.DeliveryNotification[0]?.id,
        ParentCompanyId: this.ParentCompanyId,
      }).subscribe((response: any): void => {
        if (response) {
          this.notificationList[i].DeliveryNotification[0].seen = true;
          this.deliveryService.updatedRefreshCount(true);
        }
      });
    }
  }

  public redirect(data): void {
    this.projectService.updatedProjectId(data.Project.id);
    localStorage.setItem('ProjectId', (data.Project.id));
    this.router.navigate(['/delivery-request']);
  }

  public clear(): void{
    this.showSearchbar = false;
    this.search = '';
    this.pageNo = 1;
    this.getNotification();
  }

  public getSearchNotification(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.search = data;
    this.pageNo = 1;
    this.getNotification();
  }

  public openModal1(template: TemplateRef<any>): void {
    this.modalRef = this.modalService.show(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-sm filter-popup custom-modal',
    });
  }


  public changePageSize(pageSize): void {
    this.pageSize = pageSize;
    this.getNotification();
  }

  public changePageNo(pageNo): void {
    this.pageNo = pageNo;
    this.getNotification();
  }
}
