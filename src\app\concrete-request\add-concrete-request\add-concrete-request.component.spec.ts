import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, UntypedFormBuilder } from '@angular/forms';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import { of, throwError } from 'rxjs';
import { AddConcreteRequestComponent } from './add-concrete-request.component';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';
import { MixpanelService } from '../../services/mixpanel.service';
import { NO_ERRORS_SCHEMA, Component } from '@angular/core';

// Create a test host component to avoid rendering the actual template
@Component({
  selector: 'app-test-host',
  template: '<div></div>'
})
class TestHostComponent extends AddConcreteRequestComponent {
  // This is a test host that extends the component we want to test
  // but doesn't render its template
}

describe('AddConcreteRequestComponent', () => {
  let component: TestHostComponent;
  let fixture: ComponentFixture<TestHostComponent>;
  let deliveryServiceMock: jest.Mocked<DeliveryService>;
  let projectServiceMock: jest.Mocked<ProjectService>;
  let toastrServiceMock: jest.Mocked<ToastrService>;
  let socketMock: jest.Mocked<Socket>;
  let mixpanelServiceMock: jest.Mocked<MixpanelService>;
  let modalServiceMock: jest.Mocked<BsModalService>;

  beforeEach(async () => {
    deliveryServiceMock = {
      createConcreteRequest: jest.fn(),
      searchNewMember: jest.fn(),
      updateConcreteRequestHistory: jest.fn(),
      loginUser: of({}),
      getCurrentConcreteRequestStatus: of({}),
    } as any;

    projectServiceMock = {
      projectParent: of({ ProjectId: 1, ParentCompanyId: 1 }),
    } as any;

    toastrServiceMock = {
      success: jest.fn(),
      error: jest.fn(),
    } as any;

    socketMock = {
      emit: jest.fn(),
    } as any;

    mixpanelServiceMock = {
      addMixpanelEvents: jest.fn(),
    } as any;

    modalServiceMock = {
      show: jest.fn(),
    } as any;

    await TestBed.configureTestingModule({
      declarations: [
        TestHostComponent
      ],
      imports: [ReactiveFormsModule],
      providers: [
        { provide: DeliveryService, useValue: deliveryServiceMock },
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: Socket, useValue: socketMock },
        { provide: MixpanelService, useValue: mixpanelServiceMock },
        { provide: BsModalService, useValue: modalServiceMock },
        { provide: BsModalRef, useValue: {} },
        UntypedFormBuilder,
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TestHostComponent);
    component = fixture.componentInstance;

    // Initialize the form
    component.concreteRequest = new UntypedFormBuilder().group({
      isPumpRequired: [false],
      isConcreteConfirmed: [null],
      concreteConfirmedOn: [null],
      description: [''],
      location: [''],
      responsiblePersons: [[]],
      concreteSupplier: [''],
      concretePlacementDate: [''],
      concretePlacementStart: [''],
      concretePlacementEnd: [''],
      ConcreteRequestId: [''],
      pumpOrderedDate: [''],
      pumpSize: [''],
      pumpLocation: [''],
      pumpWorkStart: [''],
      pumpWorkEnd: [''],
      isPumpConfirmed: [false],
      pumpConfirmedOn: [null],
      notes: [''],
      primerForPump: [''],
      mixDesign: [''],
      slump: [''],
      concreteQuantityOrdered: [''],
      concreteOrderNumber: [''],
      truckSpacingHours: [''],
      recurrence: ['Does Not Repeat'],
      repeatEveryCount: [''],
      repeatEveryType: [''],
      days: [[]],
      startTime: [''],
      endTime: [''],
      chosenDateOfMonth: [false],
      dateOfMonth: [''],
      monthlyRepeatType: [''],
      endDate: [''],
      TimeZoneId: [''],
      LocationId: [''],
      GateId: [''],
      EquipmentId: [''],
      originationAddress: [''],
      vehicleType: [''],
      originationAddressPump: [''],
      vehicleTypePump: ['']
    });

    // Set up component properties
    component.ProjectId = 1;
    component.ParentCompanyId = 1;

    // Mock methods to prevent them from being called
    component.getSelectedDate = jest.fn();
    component.getDropdownValues = jest.fn();

    // Skip lifecycle hooks
    jest.spyOn(component, 'ngAfterViewInit').mockImplementation(() => {});
    jest.spyOn(component, 'ngOnInit').mockImplementation(() => {});

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    expect(component.concreteRequest).toBeDefined();
    expect(component.concreteRequest.get('isPumpRequired').value).toBe(false);
  });

  it('should handle concrete confirmation change', () => {
    component.changeConcreteConfirmed(true);
    expect(component.concreteRequest.get('isConcreteConfirmed').value).toBe(true);
    expect(component.concreteRequest.get('concreteConfirmedOn').value).toBeDefined();

    component.changeConcreteConfirmed(false);
    expect(component.concreteRequest.get('isConcreteConfirmed').value).toBeNull();
    expect(component.concreteRequest.get('concreteConfirmedOn').value).toBeNull();
  });

  it('should create concrete request successfully', () => {
    const mockResponse = { message: 'Success' };
    deliveryServiceMock.createConcreteRequest.mockReturnValue(of(mockResponse));

    const payload = {
      location: 'Test Location',
      description: 'Test Description',
      concreteSupplier: 'Test Supplier',
    };

    component.createConcreteRequest(payload);

    expect(deliveryServiceMock.createConcreteRequest).toHaveBeenCalledWith(payload);
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Success', 'Success');
    expect(mixpanelServiceMock.addMixpanelEvents).toHaveBeenCalledWith('Created New Concrete Booking');
    expect(socketMock.emit).toHaveBeenCalledWith('ConcreteCreateHistory', mockResponse);
  });


  it('should reset form', () => {
    component.formSubmitted = true;
    component.submitted = true;

    component.formReset();

    expect(component.formSubmitted).toBe(false);
    expect(component.submitted).toBe(false);
  });

  it('should handle request autocomplete items', () => {
    const searchText = 'test';
    const mockResponse = { data: [] };
    deliveryServiceMock.searchNewMember.mockReturnValue(of(mockResponse));

    component.requestAutoEditcompleteItems(searchText).subscribe(response => {
      expect(response).toEqual(mockResponse);
    });

    expect(deliveryServiceMock.searchNewMember).toHaveBeenCalledWith({
      ProjectId: component.ProjectId,
      search: searchText,
      ParentCompanyId: component.ParentCompanyId,
    });
  });

  it('should handle focus', () => {
    component.focus();
    expect(component.isKeepOpen).toBe(true);
  });

  it('should handle focus lost', () => {
    component.focuslost();
    expect(component.isKeepOpen).toBe(false);
  });

  it('should handle create concrete request error - status code 400', () => {
    const mockError = {
      message: {
        statusCode: 400,
        details: [{ field: 'Test error message' }]
      }
    };
    deliveryServiceMock.createConcreteRequest.mockReturnValue(throwError(mockError));

    const payload = { location: 'Test Location' };
    component.createConcreteRequest(payload);

    expect(deliveryServiceMock.createConcreteRequest).toHaveBeenCalledWith(payload);
    expect(component.formSubmitted).toBe(false);
    expect(component.submitted).toBe(false);
  });

  it('should handle create concrete request error - no message', () => {
    const mockError = {};
    deliveryServiceMock.createConcreteRequest.mockReturnValue(throwError(mockError));

    const payload = { location: 'Test Location' };
    component.createConcreteRequest(payload);

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should handle create concrete request error - overlaps message', () => {
    const mockError = { message: 'Time slot overlaps with existing booking' };
    deliveryServiceMock.createConcreteRequest.mockReturnValue(throwError(mockError));

    const payload = { location: 'Test Location' };
    component.createConcreteRequest(payload);

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Time slot overlaps with existing booking', 'OOPS!');
  });

  it('should handle create concrete request error - generic error', () => {
    const mockError = { message: 'Generic error message' };
    deliveryServiceMock.createConcreteRequest.mockReturnValue(throwError(mockError));

    const payload = { location: 'Test Location' };
    component.createConcreteRequest(payload);

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Generic error message', 'OOPS!');
  });

  it('should handle pump confirmation change - true', () => {
    component.changePumpConfirmed(true);
    expect(component.concreteRequest.get('isPumpConfirmed').value).toBe(true);
    expect(component.concreteRequest.get('pumpConfirmedOn').value).toBeDefined();
  });

  it('should handle pump confirmation change - false', () => {
    component.changePumpConfirmed(false);
    expect(component.concreteRequest.get('isPumpConfirmed').value).toBeNull();
    expect(component.concreteRequest.get('pumpConfirmedOn').value).toBeNull();
  });

  it('should handle address change', () => {
    const mockAddress = { formatted_address: '123 Test Street, Test City' };
    component.handleAddressChange(mockAddress);
    expect(component.concreteRequest.get('originationAddress').value).toBe('123 Test Street, Test City');
  });

  it('should handle pump address change', () => {
    const mockAddress = { formatted_address: '456 Pump Street, Pump City' };
    component.handlePumpAddressChange(mockAddress);
    expect(component.concreteRequest.get('originationAddressPump').value).toBe('456 Pump Street, Pump City');
  });

  it('should handle delivery end time change detection', () => {
    component.NDRTimingChanged = false;
    component.deliveryEndTimeChangeDetection();
    expect(component.NDRTimingChanged).toBe(true);
  });

  it('should handle vehicle type selection', () => {
    const mockData = { id: 1 };
    component.vehicleTypeSelected(mockData);
    expect(component.selectedVehicleType).toBe('Medium and Heavy Duty Truck');
  });

  it('should handle pump vehicle type selection', () => {
    const mockData = { id: 2 };
    component.pumpVehicleTypeSelected(mockData);
    expect(component.selectedPumpVehicleType).toBe('Passenger Car');
  });

  it('should handle vehicle type selection with string id', () => {
    const mockData = { id: '3' };
    component.vehicleTypeSelected(mockData);
    expect(component.selectedVehicleType).toBe('Light Duty Truck');
  });

  it('should handle number only input - valid number', () => {
    const mockEvent = { which: 50, keyCode: 50 }; // '2'
    const result = component.numberOnly(mockEvent);
    expect(result).toBe(true);
  });

  it('should handle number only input - invalid character', () => {
    const mockEvent = { which: 65, keyCode: 65 }; // 'A'
    const result = component.numberOnly(mockEvent);
    expect(result).toBe(false);
  });

  it('should handle number only input - special keys', () => {
    const mockEvent = { which: 8, keyCode: 8 }; // Backspace
    const result = component.numberOnly(mockEvent);
    expect(result).toBe(true);
  });

  it('should handle quantity number only input - valid number', () => {
    const mockEvent = { which: 50, keyCode: 50 }; // '2'
    const result = component.QuantitynumberOnly(mockEvent);
    expect(result).toBe(true);
  });

  it('should handle quantity number only input - space', () => {
    const mockEvent = { which: 32, keyCode: 32 }; // Space
    const result = component.QuantitynumberOnly(mockEvent);
    expect(result).toBe(true);
  });

  it('should handle quantity number only input - decimal point', () => {
    const mockEvent = { which: 46, keyCode: 46 }; // Decimal point
    const result = component.QuantitynumberOnly(mockEvent);
    expect(result).toBe(true);
  });

  it('should handle quantity number only input - invalid character', () => {
    const mockEvent = { which: 65, keyCode: 65 }; // 'A'
    const result = component.QuantitynumberOnly(mockEvent);
    expect(result).toBe(false);
  });

  it('should handle change date', () => {
    component.modalLoader = false;
    const mockDate = new Date('2023-01-01 10:30:00');

    component.changeDate(mockDate);

    expect(component.NDRTimingChanged).toBe(true);
    expect(component.concreteRequest.get('concretePlacementEnd').value).toBeDefined();
  });

  it('should not handle change date when modal loader is active', () => {
    component.modalLoader = true;
    component.NDRTimingChanged = false;
    const mockDate = new Date('2023-01-01 10:30:00');

    component.changeDate(mockDate);

    expect(component.NDRTimingChanged).toBe(false);
  });

  it('should handle change date1', () => {
    component.modalLoader = false;
    const mockDate = new Date('2023-01-01 10:30:00');

    component.changeDate1(mockDate);

    expect(component.NDRTimingChanged).toBe(true);
    expect(component.concreteRequest.get('pumpWorkEnd').value).toBeDefined();
  });

  it('should not handle change date1 when modal loader is active', () => {
    component.modalLoader = true;
    component.NDRTimingChanged = false;
    const mockDate = new Date('2023-01-01 10:30:00');

    component.changeDate1(mockDate);

    expect(component.NDRTimingChanged).toBe(false);
  });

  it('should handle placement date', () => {
    const testDate = '01/15/2023';
    component.concreteRequest.get('concretePlacementDate').setValue(testDate);

    component.placementDate();

    expect(component.concreteRequest.get('pumpOrderedDate').value).toBe(testDate);
  });

  it('should check location duplication and remove duplicate', () => {
    component.locationList = [
      { location: 'Location 1' },
      { location: 'Location 2' },
      { location: 'Location 1' } // Duplicate
    ];

    const mockData = [
      { location: 'Location 1' },
      { location: 'Location 2' },
      { location: 'Location 1' }
    ];

    const initialLength = component.locationList.length;
    component.checkLocationDuplication(mockData);

    expect(component.locationList.length).toBeLessThan(initialLength);
  });

  it('should not check location duplication for empty data', () => {
    component.checkLocationDuplication(null);
    component.checkLocationDuplication([]);
    // Should not throw any errors
    expect(true).toBe(true);
  });

  it('should check mix design duplication and remove duplicate', () => {
    component.mixDesignList = [
      { mixDesign: 'Mix 1' },
      { mixDesign: 'Mix 2' },
      { mixDesign: 'Mix 1' } // Duplicate
    ];

    const mockData = [
      { mixDesign: 'Mix 1' },
      { mixDesign: 'Mix 2' },
      { mixDesign: 'Mix 1' }
    ];

    const initialLength = component.mixDesignList.length;
    component.checkMixDesignDuplication(mockData);

    expect(component.mixDesignList.length).toBeLessThan(initialLength);
  });

  it('should check pump size duplication and remove duplicate', () => {
    component.pumpSizeList = [
      { pumpSize: 'Small' },
      { pumpSize: 'Large' },
      { pumpSize: 'Small' } // Duplicate
    ];

    const mockData = [
      { pumpSize: 'Small' },
      { pumpSize: 'Large' },
      { pumpSize: 'Small' }
    ];

    const initialLength = component.pumpSizeList.length;
    component.checkPumpSizeDuplication(mockData);

    expect(component.pumpSizeList.length).toBeLessThan(initialLength);
  });

  it('should handle location selection', () => {
    component.locationDropdown = [
      {
        id: 1,
        locationPath: 'Location 1',
        GateId: [{ id: 1, name: 'Gate 1' }],
        EquipmentId: [{ id: 1, name: 'Equipment 1' }],
        TimeZoneId: [{ id: 1, location: 'UTC' }]
      }
    ];

    const mockData = { id: 1 };
    component.locationSelected(mockData);

    expect(component.selectedLocationId).toBe(1);
    expect(component.timeZone).toBe('UTC');
    expect(component.gateList).toEqual([{ id: 1, name: 'Gate 1' }]);
    expect(component.equipmentList).toEqual([{ id: 1, name: 'Equipment 1' }]);
  });

  it('should handle location selection without gates and equipment', () => {
    component.locationDropdown = [
      {
        id: 2,
        locationPath: 'Location 2'
      }
    ];

    const mockData = { id: 2 };
    component.locationSelected(mockData);

    expect(component.selectedLocationId).toBe(2);
    expect(component.gateList).toEqual([]);
    expect(component.equipmentList).toEqual([]);
  });

  it('should extract form data correctly', () => {
    const mockFormValue = {
      responsiblePersons: [{ id: 1 }, { id: 2 }],
      EquipmentId: [{ id: 1 }, { id: 2 }],
      concreteSupplier: [{ id: 1 }, { id: 2 }],
      mixDesign: [
        { id: 1, mixDesign: 'Mix 1' },
        { id: null, mixDesign: 'Custom Mix' }
      ],
      pumpSize: [
        { id: 1, pumpSize: 'Small' },
        { id: null, pumpSize: 'Custom Size' }
      ],
      TimeZoneId: [{ id: 1 }]
    };

    const result = component.extractFormData(mockFormValue);

    expect(result.responsiblePersons).toEqual([1, 2]);
    expect(result.equipments).toEqual([1, 2]);
    expect(result.concreteSupplier).toEqual([1, 2]);
    expect(result.mixDesign).toEqual([
      { id: 1, mixDesign: 'Mix 1', chosenFromDropdown: true },
      { id: null, mixDesign: 'Custom Mix', chosenFromDropdown: false }
    ]);
    expect(result.pumpSize).toEqual([
      { id: 1, pumpSize: 'Small', chosenFromDropdown: true },
      { id: null, pumpSize: 'Custom Size', chosenFromDropdown: false }
    ]);
    expect(result.timeZoneId).toBe(1);
  });

  it('should extract form data with empty arrays', () => {
    const mockFormValue = {
      responsiblePersons: [],
      EquipmentId: [],
      concreteSupplier: [],
      mixDesign: [],
      pumpSize: [],
      TimeZoneId: []
    };

    const result = component.extractFormData(mockFormValue);

    expect(result.responsiblePersons).toEqual([]);
    expect(result.equipments).toEqual([]);
    expect(result.concreteSupplier).toEqual([]);
    expect(result.mixDesign).toEqual([]);
    expect(result.pumpSize).toEqual([]);
    expect(result.timeZoneId).toBeNull();
  });

  it('should validate payload - same start and end time', () => {
    const mockPayload = {
      startPicker: '10:00',
      endPicker: '10:00'
    };

    const result = component.validatePayload(mockPayload);

    expect(result).toBe(false);
    expect(toastrServiceMock.error).toHaveBeenCalledWith(
      'Placement Start Time and Anticipated Completion Time should not be the same'
    );
  });

  it('should validate payload - start time greater than end time', () => {
    const mockPayload = {
      startPicker: '15:00',
      endPicker: '10:00'
    };

    const result = component.validatePayload(mockPayload);

    expect(result).toBe(false);
    expect(toastrServiceMock.error).toHaveBeenCalledWith(
      'Please enter Placement Start Time lesser than Anticipated Completion Time'
    );
  });

  it('should validate payload - invalid recurrence dates', () => {
    const mockPayload = {
      startPicker: '10:00',
      endPicker: '15:00',
      recurrence: 'Daily',
      concretePlacementStart: '2023-01-02',
      concretePlacementEnd: '2023-01-01'
    };

    const result = component.validatePayload(mockPayload);

    expect(result).toBe(false);
    expect(toastrServiceMock.error).toHaveBeenCalledWith(
      'Please enter End Date greater than Start Date'
    );
  });

  it('should validate payload - valid payload', () => {
    const mockPayload = {
      startPicker: '10:00',
      endPicker: '15:00',
      recurrence: 'Does Not Repeat'
    };

    const result = component.validatePayload(mockPayload);

    expect(result).toBe(true);
  });

  it('should sort week days correctly', () => {
    const mockData = ['Friday', 'Monday', 'Wednesday'];
    const result = component.sortWeekDays(mockData);

    expect(result).toEqual(['Monday', 'Wednesday', 'Friday']);
  });

  it('should handle empty week days array', () => {
    const result = component.sortWeekDays([]);
    expect(result).toBeUndefined();
  });

  it('should check string empty values - empty description', () => {
    const mockFormValue = {
      description: '   ',
      isPumpRequired: false,
      pumpLocation: 'Valid location'
    };

    const result = component.checkStringEmptyValues(mockFormValue);

    expect(result).toBe(true);
    expect(toastrServiceMock.error).toHaveBeenCalledWith('Please enter valid description', 'OOPS!');
  });

  it('should check string empty values - empty pump location when pump required', () => {
    const mockFormValue = {
      description: 'Valid description',
      isPumpRequired: true,
      pumpLocation: '   '
    };

    const result = component.checkStringEmptyValues(mockFormValue);

    expect(result).toBe(true);
    expect(toastrServiceMock.error).toHaveBeenCalledWith('Please enter valid pump location', 'OOPS!');
  });

  it('should check string empty values - valid values', () => {
    const mockFormValue = {
      description: 'Valid description',
      isPumpRequired: false,
      pumpLocation: 'Valid location'
    };

    const result = component.checkStringEmptyValues(mockFormValue);

    expect(result).toBe(false);
  });

  it('should convert start time correctly', () => {
    const mockDate = new Date('2023-01-01');
    const result = component.convertStart(mockDate, 10, 30);

    expect(result).toContain('2023');
    expect(typeof result).toBe('string');
  });

  it('should handle reset form with action no', () => {
    jest.spyOn(component, 'getDropdownValues').mockImplementation(() => {});

    component.resetForm('no');

    // Should not call getDropdownValues for 'no' action
    expect(component.getDropdownValues).not.toHaveBeenCalled();
  });

  it('should handle reset form with action yes', () => {
    component.submitted = true;
    component.formSubmitted = true;
    component.NDRTimingChanged = true;

    jest.spyOn(component, 'getDropdownValues').mockImplementation(() => {});

    component.resetForm('yes');

    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
    expect(component.NDRTimingChanged).toBe(false);
    expect(component.getDropdownValues).toHaveBeenCalled();
  });

  it('should handle selected booking type', () => {
    const mockType = 'New Delivery Booking';
    component.selectfunction = jest.fn();
    component.concreteRequest.get('concretePlacementDate').setValue('2023-01-01');

    component.selectedBookingtype(mockType);

    expect(component.selectedParams).toBe(mockType);
    expect(component.selectfunction).toHaveBeenCalledWith(mockType, '2023-01-01');
  });

  it('should not call selectfunction when type is falsy', () => {
    component.selectfunction = jest.fn();

    component.selectedBookingtype(null);

    expect(component.selectfunction).not.toHaveBeenCalled();
  });

  it('should handle get booking data', () => {
    component.concreteRequest.get('EquipmentId').setValue('equipment1');
    component.concreteRequest.get('LocationId').setValue([{ id: 1 }]);
    component.concreteRequest.get('GateId').setValue('gate1');
    component.timeZone = 'UTC';
    component.timeSlotComponent = {
      getEventNDR: jest.fn()
    } as any;

    component.getBookingData();

    expect(component.timeSlotComponent.getEventNDR).toHaveBeenCalledWith(
      'equipment1', 1, 'gate1', 'UTC', ''
    );
  });

  it('should not call getEventNDR when timeSlotComponent is null', () => {
    component.timeSlotComponent = null;

    component.getBookingData();

    // Should not throw any errors
    expect(true).toBe(true);
  });

  it('should handle select time', () => {
    const startStr = '2023-01-01T10:00:00';
    const endStr = '2023-01-01T11:00:00';

    jest.spyOn(component, 'placementDate').mockImplementation(() => {});
    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});

    component.selectTime(startStr, endStr);

    expect(component.concreteRequest.get('concretePlacementStart').value).toEqual(new Date(startStr));
    expect(component.concreteRequest.get('concretePlacementEnd').value).toEqual(new Date(endStr));
    expect(component.concreteRequest.get('concretePlacementDate').value).toEqual(new Date(startStr));
    expect(component.isTimeSlotChoosen).toBe(true);
    expect(component.placementDate).toHaveBeenCalled();
    expect(component.showMonthlyRecurrence).toHaveBeenCalled();
  });

  it('should handle get slots', () => {
    jest.spyOn(component, 'getBookingData').mockImplementation(() => {});

    component.getSlots();

    expect(component.getBookingData).toHaveBeenCalled();
  });

  it('should handle show error', () => {
    const mockError = {
      message: {
        details: [{ field: 'Test error message' }]
      }
    };

    component.showError(mockError);

    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
    expect(toastrServiceMock.error).toHaveBeenCalledWith('Test error message');
  });

  it('should handle close with form changes', () => {
    const mockTemplate = {} as any;
    component.concreteRequest.markAsTouched();
    component.concreteRequest.markAsDirty();

    jest.spyOn(component, 'openConfirmationModalPopup').mockImplementation(() => {});

    component.close(mockTemplate);

    expect(component.openConfirmationModalPopup).toHaveBeenCalledWith(mockTemplate);
  });

  it('should handle close with concrete supplier changes', () => {
    const mockTemplate = {} as any;
    component.concreteRequest.get('concreteSupplier').markAsDirty();
    component.concreteRequest.get('concreteSupplier').setValue(['supplier1']);

    jest.spyOn(component, 'openConfirmationModalPopup').mockImplementation(() => {});

    component.close(mockTemplate);

    expect(component.openConfirmationModalPopup).toHaveBeenCalledWith(mockTemplate);
  });

  it('should handle close with NDR timing changes', () => {
    const mockTemplate = {} as any;
    component.NDRTimingChanged = true;

    jest.spyOn(component, 'openConfirmationModalPopup').mockImplementation(() => {});

    component.close(mockTemplate);

    expect(component.openConfirmationModalPopup).toHaveBeenCalledWith(mockTemplate);
  });

  it('should handle close without changes', () => {
    const mockTemplate = {} as any;

    jest.spyOn(component, 'resetForm').mockImplementation(() => {});

    component.close(mockTemplate);

    expect(component.resetForm).toHaveBeenCalledWith('yes');
  });

  it('should handle onSubmit with invalid form', () => {
    component.concreteRequest.setErrors({ invalid: true });

    component.onSubmit();

    expect(component.submitted).toBe(true);
    expect(component.formSubmitted).toBe(false);
  });

  it('should handle onSubmit with pump timing validation error', () => {
    component.concreteRequest.patchValue({
      isPumpRequired: true,
      pumpWorkStart: '10:00',
      pumpWorkEnd: '10:00'
    });

    jest.spyOn(component, 'formReset').mockImplementation(() => {});

    component.onSubmit();

    expect(toastrServiceMock.error).toHaveBeenCalledWith(
      'Pump Show up Time and Completion Time should not be the same'
    );
    expect(component.formReset).toHaveBeenCalled();
  });

  it('should handle onSubmit with pump start time greater than end time', () => {
    component.concreteRequest.patchValue({
      isPumpRequired: true,
      pumpWorkStart: '15:00',
      pumpWorkEnd: '10:00'
    });

    jest.spyOn(component, 'formReset').mockImplementation(() => {});

    component.onSubmit();

    expect(toastrServiceMock.error).toHaveBeenCalledWith(
      'Please enter Pump Show up Time lesser than Pump Completion Time'
    );
    expect(component.formReset).toHaveBeenCalled();
  });

  it('should handle onSubmit with valid form', () => {
    component.concreteRequest.patchValue({
      concretePlacementDate: '2023-01-01',
      concretePlacementStart: '2023-01-01T10:00:00',
      concretePlacementEnd: '2023-01-01T11:00:00',
      endDate: '2023-01-02',
      recurrence: 'Does Not Repeat',
      isPumpRequired: false
    });

    jest.spyOn(component, 'createPlacement').mockImplementation(() => {});

    component.onSubmit();

    expect(component.submitted).toBe(true);
    expect(component.formSubmitted).toBe(true);
    expect(component.createPlacement).toHaveBeenCalled();
  });

  it('should handle createPlacement with missing responsible persons', () => {
    const mockFormValue = {
      responsiblePersons: []
    };

    jest.spyOn(component, 'formReset').mockImplementation(() => {});

    component.createPlacement(mockFormValue, '', '', '', '', '', '');

    expect(component.formReset).toHaveBeenCalled();
    expect(toastrServiceMock.error).toHaveBeenCalledWith('Responsible Person is required');
  });

  it('should handle createPlacement with empty string values', () => {
    const mockFormValue = {
      responsiblePersons: [{ id: 1 }],
      description: '   ',
      isPumpRequired: false
    };

    jest.spyOn(component, 'formReset').mockImplementation(() => {});
    jest.spyOn(component, 'checkStringEmptyValues').mockReturnValue(true);

    component.createPlacement(mockFormValue, '', '', '', '', '', '');

    expect(component.formReset).toHaveBeenCalled();
    expect(component.checkStringEmptyValues).toHaveBeenCalledWith(mockFormValue);
  });

  it('should handle createPlacement with valid data', () => {
    const mockFormValue = {
      responsiblePersons: [{ id: 1 }],
      description: 'Valid description',
      isPumpRequired: false,
      location: 'Test Location',
      notes: 'Test notes',
      concreteOrderNumber: '12345',
      truckSpacingHours: '2',
      slump: '5',
      concreteQuantityOrdered: '100',
      concreteConfirmedOn: null,
      pumpConfirmedOn: null,
      ConcreteRequestId: 1,
      recurrence: 'Does Not Repeat',
      chosenDateOfMonth: false,
      dateOfMonth: '',
      monthlyRepeatType: '',
      days: [],
      repeatEveryType: null,
      repeatEveryCount: null,
      originationAddress: 'Origin Address',
      originationAddressPump: 'Pump Origin',
      GateId: 'gate1',
      EquipmentId: [],
      concreteSupplier: [],
      mixDesign: [],
      pumpSize: [],
      TimeZoneId: []
    };

    component.selectedLocationId = 1;
    component.ProjectId = 1;
    component.ParentCompanyId = 1;
    component.selectedVehicleType = 'Truck';
    component.selectedPumpVehicleType = 'Pump Truck';

    jest.spyOn(component, 'extractFormData').mockReturnValue({
      responsiblePersons: [1],
      equipments: [],
      concreteSupplier: [],
      mixDesign: [],
      pumpSize: [],
      timeZoneId: null
    });
    jest.spyOn(component, 'checkStringEmptyValues').mockReturnValue(false);
    jest.spyOn(component, 'validatePayload').mockReturnValue(true);
    jest.spyOn(component, 'createConcreteRequest').mockImplementation(() => {});
    jest.spyOn(component, 'sortWeekDays').mockReturnValue([]);

    component.createPlacement(mockFormValue, '', '', '10:00', '11:00', '', '');

    expect(component.createConcreteRequest).toHaveBeenCalled();
  });

  it('should handle setDefaultDateAndTime with date', () => {
    const testDate = '2023-01-01';

    component.setDefaultDateAndTime(testDate);

    expect(component.placementStart).toEqual(new Date(testDate));
    expect(component.placementEnd).toEqual(new Date(testDate));
  });

  it('should handle setDefaultDateAndTime without date', () => {
    component.setDefaultDateAndTime(null);

    expect(component.concreteRequest.get('concretePlacementDate').value).toBeDefined();
    expect(component.concreteRequest.get('pumpOrderedDate').value).toBeDefined();
  });

  it('should handle getSelectedDate with Month view', () => {
    component.data = {
      date: '2023-01-01',
      currentView: 'Month'
    };

    jest.spyOn(component, 'setDefaultDateAndTime').mockImplementation(() => {});

    component.getSelectedDate();

    expect(component.concreteRequest.get('concretePlacementDate').value).toContain('01-01-2023');
    expect(component.setDefaultDateAndTime).toHaveBeenCalled();
  });

  it('should handle getSelectedDate with Week view', () => {
    component.data = {
      date: '2023-01-01',
      currentView: 'Week'
    };

    component.getSelectedDate();

    expect(component.concreteRequest.get('concretePlacementDate').value).toContain('01-01-2023');
    expect(component.concreteRequest.get('concretePlacementStart').value).toBeDefined();
    expect(component.concreteRequest.get('concretePlacementEnd').value).toBeDefined();
  });

  it('should handle getSelectedDate with Day view', () => {
    component.data = {
      date: '2023-01-01',
      currentView: 'Day'
    };

    component.getSelectedDate();

    expect(component.concreteRequest.get('concretePlacementDate').value).toContain('01-01-2023');
    expect(component.concreteRequest.get('pumpWorkStart').value).toBeDefined();
    expect(component.concreteRequest.get('pumpWorkEnd').value).toBeDefined();
  });

  it('should handle getSelectedDate without data', () => {
    component.data = null;

    jest.spyOn(component, 'setDefaultDateAndTime').mockImplementation(() => {});

    component.getSelectedDate();

    expect(component.setDefaultDateAndTime).toHaveBeenCalledWith(null);
  });

  it('should handle ngOnInit with selectedEventDate', () => {
    component.selectedEventDate = '2023-01-01';

    component.ngOnInit();

    expect(component.concreteRequest.get('concretePlacementDate').value).toBe('2023-01-01');
  });

  it('should handle ngOnInit without selectedEventDate', () => {
    component.selectedEventDate = null;

    jest.spyOn(component, 'getSelectedDate').mockImplementation(() => {});

    component.ngOnInit();

    expect(component.getSelectedDate).toHaveBeenCalled();
  });

  it('should handle getDropdownValues success', () => {
    const mockResponse = {
      data: {
        ConcreteRequestId: 123,
        locationDropdown: [{
          id: 1,
          EquipmentId: [{ id: 1, equipmentName: 'Equipment 1' }],
          gateDetails: [{ id: 1, gateName: 'Gate 1' }],
          TimeZoneId: [{ location: 'UTC' }],
          isDefault: true
        }],
        locationDetailsDropdown: [],
        concreteSupplierDropdown: [{ id: 1, companyName: 'Supplier 1' }],
        mixDesignDropdown: [],
        pumpSizeDropdown: []
      }
    };

    component.ProjectId = 1;
    component.ParentCompanyId = 1;

    deliveryServiceMock.getConcreteRequestDropdownData = jest.fn().mockReturnValue(of(mockResponse));

    component.getDropdownValues();

    expect(deliveryServiceMock.getConcreteRequestDropdownData).toHaveBeenCalledWith({
      ProjectId: 1,
      ParentCompanyId: 1
    });
    expect(component.ConcreteRequestId).toBe(123);
    expect(component.locationDropdown).toEqual(mockResponse.data.locationDropdown);
  });

  it('should handle getDropdownValues without ProjectId', () => {
    component.ProjectId = null;

    component.getDropdownValues();

    expect(component.modalLoader).toBe(true);
  });

  it('should handle form control value changes for pump required', () => {
    component.concreteRequestCreationForm();

    // Test when pump is required
    component.concreteRequest.get('isPumpRequired').setValue(true);

    expect(component.concreteRequest.get('pumpSize').hasError('required')).toBe(true);
    expect(component.concreteRequest.get('pumpLocation').hasError('required')).toBe(true);
  });

  it('should handle form control value changes for pump not required', () => {
    component.concreteRequestCreationForm();

    // Test when pump is not required
    component.concreteRequest.get('isPumpRequired').setValue(false);

    expect(component.concreteRequest.get('pumpSize').hasError('required')).toBe(false);
    expect(component.concreteRequest.get('pumpLocation').hasError('required')).toBe(false);
  });

  it('should handle form control value changes for repeat every type', () => {
    component.concreteRequestCreationForm();

    // Test when repeat every type is Week
    component.concreteRequest.get('repeatEveryType').setValue('Week');

    expect(component.concreteRequest.get('days').hasError('required')).toBe(true);
  });

  it('should handle form control value changes for monthly repeat', () => {
    component.concreteRequestCreationForm();

    // Test when repeat every type is Month and chosenDateOfMonth is 1
    component.concreteRequest.get('repeatEveryType').setValue('Month');
    component.concreteRequest.get('chosenDateOfMonth').setValue(1);

    expect(component.concreteRequest.get('dateOfMonth').hasError('required')).toBe(true);
  });

  it('should handle ngAfterViewInit', () => {
    jest.spyOn(component, 'getDropdownValues').mockImplementation(() => {});

    component.ngAfterViewInit();

    expect(component.getDropdownValues).toHaveBeenCalled();
  });

  it('should handle constructor initialization', () => {
    expect(component.selectedValue).toBe('Does Not Repeat');
    expect(component.concreteRequest).toBeDefined();
  });

  it('should handle constructor with project service subscription', () => {
    const mockProjectData = { ProjectId: 123, ParentCompanyId: 456 };
    const mockProjectService = {
      projectParent: of(mockProjectData)
    };

    jest.spyOn(component, 'getTimeZoneList').mockImplementation(() => {});

    // Test that component can handle project service data
    component.ProjectId = 123;
    component.ParentCompanyId = 456;

    expect(component.ProjectId).toBe(123);
    expect(component.ParentCompanyId).toBe(456);
  });

  it('should handle error cases in extractFormData', () => {
    const mockFormValue = {
      responsiblePersons: null,
      EquipmentId: null,
      concreteSupplier: null,
      mixDesign: null,
      pumpSize: null,
      TimeZoneId: null
    };

    const result = component.extractFormData(mockFormValue);

    expect(result.responsiblePersons).toEqual([]);
    expect(result.equipments).toEqual([]);
    expect(result.concreteSupplier).toEqual([]);
    expect(result.mixDesign).toEqual([]);
    expect(result.pumpSize).toEqual([]);
    expect(result.timeZoneId).toBeNull();
  });

  it('should handle mixDesign with matching id and mixDesign', () => {
    const mockFormValue = {
      responsiblePersons: [],
      EquipmentId: [],
      concreteSupplier: [],
      mixDesign: [{ id: 'Mix 1', mixDesign: 'Mix 1' }],
      pumpSize: [],
      TimeZoneId: []
    };

    const result = component.extractFormData(mockFormValue);

    expect(result.mixDesign).toEqual([
      { id: null, mixDesign: 'Mix 1', chosenFromDropdown: false }
    ]);
  });

  it('should handle pumpSize with matching id and pumpSize', () => {
    const mockFormValue = {
      responsiblePersons: [],
      EquipmentId: [],
      concreteSupplier: [],
      mixDesign: [],
      pumpSize: [{ id: 'Small', pumpSize: 'Small' }],
      TimeZoneId: []
    };

    const result = component.extractFormData(mockFormValue);

    expect(result.pumpSize).toEqual([
      { id: null, pumpSize: 'Small', chosenFromDropdown: false }
    ]);
  });

  it('should handle successful concrete request creation with all callbacks', () => {
    const mockResponse = { message: 'Success', data: { id: 1 } };
    deliveryServiceMock.createConcreteRequest.mockReturnValue(of(mockResponse));
    deliveryServiceMock.updateConcreteRequestHistory = jest.fn();

    const payload = { location: 'Test Location' };

    jest.spyOn(component, 'formReset').mockImplementation(() => {});
    jest.spyOn(component, 'resetForm').mockImplementation(() => {});

    component.createConcreteRequest(payload);

    expect(deliveryServiceMock.createConcreteRequest).toHaveBeenCalledWith(payload);
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Success', 'Success');
    expect(mixpanelServiceMock.addMixpanelEvents).toHaveBeenCalledWith('Created New Concrete Booking');
    expect(socketMock.emit).toHaveBeenCalledWith('ConcreteCreateHistory', mockResponse);
    expect(component.formReset).toHaveBeenCalled();
    expect(component.resetForm).toHaveBeenCalledWith('yes');
    expect(deliveryServiceMock.updateConcreteRequestHistory).toHaveBeenCalledWith(
      { status: true },
      'ConcreteCreateHistory'
    );
    expect(component.NDRTimingChanged).toBe(false);
  });

  // Test for changeConcreteConfirmed method
  it('should handle change concrete confirmed - true', () => {
    component.changeConcreteConfirmed(true);
    expect(component.concreteRequest.get('isConcreteConfirmed').value).toBe(true);
    expect(component.concreteRequest.get('concreteConfirmedOn').value).toBeDefined();
  });

  it('should handle change concrete confirmed - false', () => {
    component.changeConcreteConfirmed(false);
    expect(component.concreteRequest.get('isConcreteConfirmed').value).toBeNull();
    expect(component.concreteRequest.get('concreteConfirmedOn').value).toBeNull();
  });

  // Test for requestAutoEditcompleteItems method
  it('should handle request auto edit complete items', () => {
    component.ProjectId = 1;
    component.ParentCompanyId = 1;
    const mockResponse = { data: [] };
    deliveryServiceMock.searchNewMember = jest.fn().mockReturnValue(of(mockResponse));

    const result = component.requestAutoEditcompleteItems('test');

    expect(deliveryServiceMock.searchNewMember).toHaveBeenCalledWith({
      ProjectId: 1,
      search: 'test',
      ParentCompanyId: 1
    });
    expect(result).toBeDefined();
  });

  // Test for formReset method
  it('should handle form reset', () => {
    component.formSubmitted = true;
    component.submitted = true;

    component.formReset();

    expect(component.formSubmitted).toBe(false);
    expect(component.submitted).toBe(false);
  });

  // Test for closeDropdownEditPopup method
  it('should handle close dropdown edit popup', () => {
    const mockModalRef = { hide: jest.fn() };
    (component as any).modalRef1 = mockModalRef;

    component.closeDropdownEditPopup();

    expect(mockModalRef.hide).toHaveBeenCalled();
  });

  // Test for formControlValueChanged method
  it('should handle form control value changed for pump required', () => {
    component.concreteRequestCreationForm();

    // Test when pump is required
    component.concreteRequest.get('isPumpRequired').setValue(true);

    expect(component.concreteRequest.get('pumpSize').hasError('required')).toBe(true);
    expect(component.concreteRequest.get('pumpLocation').hasError('required')).toBe(true);
  });

  it('should handle form control value changed for pump not required', () => {
    component.concreteRequestCreationForm();

    // Test when pump is not required
    component.concreteRequest.get('isPumpRequired').setValue(false);

    expect(component.concreteRequest.get('pumpSize').hasError('required')).toBe(false);
    expect(component.concreteRequest.get('pumpLocation').hasError('required')).toBe(false);
  });

  // Test for formControlValueChanged1 method
  it('should handle form control value changed1 for Week', () => {
    component.concreteRequestCreationForm();

    component.concreteRequest.get('repeatEveryType').setValue('Week');

    expect(component.concreteRequest.get('days').hasError('required')).toBe(true);
  });

  it('should handle form control value changed1 for Month with chosenDateOfMonth 1', () => {
    component.concreteRequestCreationForm();

    component.concreteRequest.get('repeatEveryType').setValue('Month');
    component.concreteRequest.get('chosenDateOfMonth').setValue(1);

    expect(component.concreteRequest.get('dateOfMonth').hasError('required')).toBe(true);
  });

  // Test for setCurrentTiming method
  it('should handle set current timing', () => {
    jest.spyOn(component, 'changeMonthlyRecurrence').mockImplementation(() => {});

    component.setCurrentTiming();

    expect(component.startTime).toBeDefined();
    expect(component.endTime).toBeDefined();
    expect(component.changeMonthlyRecurrence).toHaveBeenCalled();
  });

  // Test for chooseRepeatEveryType method
  it('should handle choose repeat every type - Day', () => {
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.chooseRepeatEveryType('Day');

    expect(component.concreteRequest.get('recurrence').value).toBe('Daily');
    expect(component.isRepeatWithSingleRecurrence).toBe(true);
    expect(component.occurMessage).toHaveBeenCalled();
  });

  it('should handle choose repeat every type - Week', () => {
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.chooseRepeatEveryType('Week');

    expect(component.concreteRequest.get('recurrence').value).toBe('Weekly');
    expect(component.occurMessage).toHaveBeenCalled();
  });

  it('should handle choose repeat every type - Month', () => {
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.chooseRepeatEveryType('Month');

    expect(component.concreteRequest.get('recurrence').value).toBe('Monthly');
    expect(component.occurMessage).toHaveBeenCalled();
  });

  it('should handle choose repeat every type - Year', () => {
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.chooseRepeatEveryType('Year');

    expect(component.concreteRequest.get('recurrence').value).toBe('Yearly');
    expect(component.occurMessage).toHaveBeenCalled();
  });

  // Test for occurMessage method
  it('should handle occur message for Day', () => {
    component.concreteRequest.get('repeatEveryType').setValue('Day');

    component.occurMessage();

    expect(component.message).toBe('Occurs every day');
  });

  it('should handle occur message for Days with count 2', () => {
    component.concreteRequest.get('repeatEveryType').setValue('Days');
    component.concreteRequest.get('repeatEveryCount').setValue(2);

    component.occurMessage();

    expect(component.message).toBe('Occurs every other day');
  });

  it('should handle occur message for Days with count > 2', () => {
    component.concreteRequest.get('repeatEveryType').setValue('Days');
    component.concreteRequest.get('repeatEveryCount').setValue(3);

    component.occurMessage();

    expect(component.message).toBe('Occurs every 3 days');
  });

  // Test for checkRepeatType method
  it('should handle check repeat type for Month with chosenDateOfMonth 1', () => {
    component.concreteRequest.get('repeatEveryType').setValue('Month');
    component.concreteRequest.get('chosenDateOfMonth').setValue(1);
    component.monthlyDate = '15';

    component.checkRepeatType();

    expect(component.message).toBe('Occurs on day 15');
  });

  it('should handle check repeat type for Month with chosenDateOfMonth 2', () => {
    component.concreteRequest.get('repeatEveryType').setValue('Month');
    component.concreteRequest.get('chosenDateOfMonth').setValue(2);
    component.monthlyDayOfWeek = 'First Monday';

    component.checkRepeatType();

    expect(component.message).toBe('Occurs on the First Monday');
  });

  it('should handle check repeat type for Month with chosenDateOfMonth 3', () => {
    component.concreteRequest.get('repeatEveryType').setValue('Month');
    component.concreteRequest.get('chosenDateOfMonth').setValue(3);
    component.monthlyLastDayOfWeek = 'Last Monday';

    component.checkRepeatType();

    expect(component.message).toBe('Occurs on the Last Monday');
  });

  // Test for changeRecurrenceCount method
  it('should handle change recurrence count with positive value', () => {
    jest.spyOn(component, 'updateRecurrenceUIState').mockImplementation(() => {});
    jest.spyOn(component, 'updateRecurrenceFormValues').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.changeRecurrenceCount(2);

    expect(component.updateRecurrenceUIState).toHaveBeenCalledWith(2);
    expect(component.updateRecurrenceFormValues).toHaveBeenCalledWith(2);
    expect(component.occurMessage).toHaveBeenCalled();
  });

  it('should handle change recurrence count with negative value', () => {
    component.changeRecurrenceCount(-1);

    expect(component.concreteRequest.get('repeatEveryCount').value).toBe(1);
  });

  // Test for updateRecurrenceUIState method
  it('should handle update recurrence UI state for Daily with count 1', () => {
    component.concreteRequest.get('recurrence').setValue('Daily');

    component.updateRecurrenceUIState(1);

    expect(component.isRepeatWithSingleRecurrence).toBe(true);
    expect(component.isRepeatWithMultipleRecurrence).toBe(false);
    expect(component.showRecurrenceTypeDropdown).toBe(false);
  });

  it('should handle update recurrence UI state for Daily with count > 1', () => {
    component.concreteRequest.get('recurrence').setValue('Daily');

    component.updateRecurrenceUIState(2);

    expect(component.isRepeatWithSingleRecurrence).toBe(false);
    expect(component.isRepeatWithMultipleRecurrence).toBe(false);
    expect(component.showRecurrenceTypeDropdown).toBe(true);
  });

  it('should handle update recurrence UI state for Weekly with count > 1', () => {
    component.concreteRequest.get('recurrence').setValue('Weekly');

    component.updateRecurrenceUIState(2);

    expect(component.isRepeatWithSingleRecurrence).toBe(false);
    expect(component.isRepeatWithMultipleRecurrence).toBe(true);
    expect(component.showRecurrenceTypeDropdown).toBe(false);
  });

  // Test for updateRecurrenceFormValues method
  it('should handle update recurrence form values for Daily', () => {
    component.concreteRequest.get('recurrence').setValue('Daily');

    component.updateRecurrenceFormValues(1);

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Day');
  });

  it('should handle update recurrence form values for Daily with count > 1', () => {
    component.concreteRequest.get('recurrence').setValue('Daily');

    component.updateRecurrenceFormValues(2);

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Days');
  });

  it('should handle update recurrence form values for Weekly', () => {
    component.concreteRequest.get('recurrence').setValue('Weekly');

    component.updateRecurrenceFormValues(1);

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Week');
  });

  it('should handle update recurrence form values for Weekly with count > 1', () => {
    component.concreteRequest.get('recurrence').setValue('Weekly');

    component.updateRecurrenceFormValues(2);

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Weeks');
  });

  it('should handle update recurrence form values for Monthly', () => {
    jest.spyOn(component, 'changeMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});
    component.concreteRequest.get('recurrence').setValue('Monthly');

    component.updateRecurrenceFormValues(1);

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Month');
    expect(component.changeMonthlyRecurrence).toHaveBeenCalled();
    expect(component.showMonthlyRecurrence).toHaveBeenCalled();
  });

  it('should handle update recurrence form values for Yearly', () => {
    component.concreteRequest.get('recurrence').setValue('Yearly');

    component.updateRecurrenceFormValues(1);

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Year');
  });

  // Test for changeMonthlyRecurrence method
  it('should handle change monthly recurrence', () => {
    jest.spyOn(component, 'setMonthlyOrYearlyRecurrenceOption').mockImplementation(() => {});
    jest.spyOn(component, 'updateFormValidation').mockImplementation(() => {});
    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.changeMonthlyRecurrence();

    expect(component.setMonthlyOrYearlyRecurrenceOption).toHaveBeenCalled();
    expect(component.updateFormValidation).toHaveBeenCalled();
    expect(component.showMonthlyRecurrence).toHaveBeenCalled();
    expect(component.occurMessage).toHaveBeenCalled();
  });

  // Test for setMonthlyOrYearlyRecurrenceOption method
  it('should handle set monthly or yearly recurrence option for chosenDateOfMonth 1', () => {
    component.concreteRequest.get('chosenDateOfMonth').setValue(1);
    component.concreteRequest.get('concretePlacementDate').setValue('01/15/2023');

    component.setMonthlyOrYearlyRecurrenceOption();

    expect(component.concreteRequest.get('dateOfMonth').value).toBe('15');
    expect(component.concreteRequest.get('monthlyRepeatType').value).toBeNull();
  });

  it('should handle set monthly or yearly recurrence option for chosenDateOfMonth 2', () => {
    component.concreteRequest.get('chosenDateOfMonth').setValue(2);
    component.monthlyDayOfWeek = 'First Monday';

    component.setMonthlyOrYearlyRecurrenceOption();

    expect(component.concreteRequest.get('dateOfMonth').value).toBeNull();
    expect(component.concreteRequest.get('monthlyRepeatType').value).toBe('First Monday');
  });

  it('should handle set monthly or yearly recurrence option for chosenDateOfMonth 3', () => {
    component.concreteRequest.get('chosenDateOfMonth').setValue(3);
    component.monthlyLastDayOfWeek = 'Last Monday';

    component.setMonthlyOrYearlyRecurrenceOption();

    expect(component.concreteRequest.get('dateOfMonth').value).toBeNull();
    expect(component.concreteRequest.get('monthlyRepeatType').value).toBe('Last Monday');
  });

  // Test for updateFormValidation method
  it('should handle update form validation for chosenDateOfMonth 1', () => {
    component.concreteRequest.get('chosenDateOfMonth').setValue(1);

    component.updateFormValidation();

    expect(component.concreteRequest.get('dateOfMonth').hasError('required')).toBe(true);
    expect(component.concreteRequest.get('monthlyRepeatType').hasError('required')).toBe(false);
  });

  it('should handle update form validation for chosenDateOfMonth not 1', () => {
    component.concreteRequest.get('chosenDateOfMonth').setValue(2);

    component.updateFormValidation();

    expect(component.concreteRequest.get('dateOfMonth').hasError('required')).toBe(false);
    expect(component.concreteRequest.get('monthlyRepeatType').hasError('required')).toBe(true);
  });

  // Test for timeZoneSelected method
  it('should handle time zone selected', () => {
    (component as any).timezoneList = [
      { id: 1, location: 'UTC' },
      { id: 2, location: 'EST' }
    ];

    component.timeZoneSelected(1);

    expect(component.selectedTimeZoneValue).toEqual({ id: 1, location: 'UTC' });
  });

  // Test for getTimeZoneList method
  it('should handle get time zone list success', () => {
    const mockTimeZoneResponse = {
      data: [
        { id: 1, location: 'UTC' },
        { id: 2, location: 'EST' }
      ]
    };
    const mockProjectResponse = {
      data: { TimeZoneId: 1 }
    };

    component.ProjectId = 1;
    projectServiceMock.getTimeZoneList = jest.fn().mockReturnValue(of(mockTimeZoneResponse));
    projectServiceMock.getSingleProject = jest.fn().mockReturnValue(of(mockProjectResponse));

    component.getTimeZoneList();

    expect(projectServiceMock.getTimeZoneList).toHaveBeenCalled();
    expect(projectServiceMock.getSingleProject).toHaveBeenCalledWith({ ProjectId: 1 });
    expect(component.timezoneList).toEqual(mockTimeZoneResponse.data);
    expect(component.formSubmitted).toBe(false);
  });

  it('should handle get time zone list error with status code 400', () => {
    const mockError = {
      message: {
        statusCode: 400,
        details: [{ field: 'Test error' }]
      }
    };
    projectServiceMock.getTimeZoneList = jest.fn().mockReturnValue(throwError(mockError));
    jest.spyOn(component, 'showError').mockImplementation(() => {});

    component.getTimeZoneList();

    expect(component.showError).toHaveBeenCalledWith(mockError);
  });

  it('should handle get time zone list error without message', () => {
    const mockError = {};
    projectServiceMock.getTimeZoneList = jest.fn().mockReturnValue(throwError(mockError));

    component.getTimeZoneList();

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should handle get time zone list error with generic message', () => {
    const mockError = { message: 'Generic error' };
    projectServiceMock.getTimeZoneList = jest.fn().mockReturnValue(throwError(mockError));

    component.getTimeZoneList();

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Generic error', 'OOPS!');
  });

  // Test for getRepeatEveryType method
  it('should handle get repeat every type for Does Not Repeat', () => {
    component.getRepeatEveryType('Does Not Repeat');

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('');
  });

  it('should handle get repeat every type for Daily', () => {
    component.getRepeatEveryType('Daily');

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Day');
    expect(component.concreteRequest.get('repeatEveryCount').value).toBe(1);
  });

  it('should handle get repeat every type for Weekly', () => {
    component.getRepeatEveryType('Weekly');

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Week');
  });

  it('should handle get repeat every type for Monthly', () => {
    component.getRepeatEveryType('Monthly');

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Month');
  });

  it('should handle get repeat every type for Yearly', () => {
    component.getRepeatEveryType('Yearly');

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Year');
  });

  // Test for onRecurrenceSelect method
  it('should handle on recurrence select for Daily with count > 1', () => {
    component.concreteRequest.get('repeatEveryCount').setValue(2);
    jest.spyOn(component, 'getRepeatEveryType').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.onRecurrenceSelect('Daily');

    expect(component.selectedRecurrence).toBe('Daily');
    expect(component.isRepeatWithMultipleRecurrence).toBe(false);
    expect(component.isRepeatWithSingleRecurrence).toBe(false);
    expect(component.getRepeatEveryType).toHaveBeenCalledWith('Daily');
    expect(component.occurMessage).toHaveBeenCalled();
  });

  it('should handle on recurrence select for Weekly with count > 1', () => {
    component.concreteRequest.get('repeatEveryCount').setValue(2);
    jest.spyOn(component, 'getRepeatEveryType').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.onRecurrenceSelect('Weekly');

    expect(component.selectedRecurrence).toBe('Weekly');
    expect(component.isRepeatWithMultipleRecurrence).toBe(true);
    expect(component.isRepeatWithSingleRecurrence).toBe(false);
  });

  it('should handle on recurrence select for Weekly with count 1', () => {
    component.concreteRequest.get('repeatEveryCount').setValue(1);
    jest.spyOn(component, 'getRepeatEveryType').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.onRecurrenceSelect('Weekly');

    expect(component.isRepeatWithMultipleRecurrence).toBe(false);
    expect(component.isRepeatWithSingleRecurrence).toBe(true);
  });

  it('should handle on recurrence select for Daily with count 1', () => {
    component.concreteRequest.get('repeatEveryCount').setValue(1);
    jest.spyOn(component, 'getRepeatEveryType').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.onRecurrenceSelect('Daily');

    expect(component.isRepeatWithMultipleRecurrence).toBe(false);
    expect(component.isRepeatWithSingleRecurrence).toBe(true);
  });

  it('should handle on recurrence select for Monthly with count 1', () => {
    component.concreteRequest.get('repeatEveryCount').setValue(1);
    jest.spyOn(component, 'getRepeatEveryType').mockImplementation(() => {});
    jest.spyOn(component, 'changeMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.onRecurrenceSelect('Monthly');

    expect(component.concreteRequest.get('chosenDateOfMonth').value).toBe(1);
    expect(component.changeMonthlyRecurrence).toHaveBeenCalled();
    expect(component.showMonthlyRecurrence).toHaveBeenCalled();
    expect(component.isRepeatWithSingleRecurrence).toBe(true);
  });

  it('should handle on recurrence select for Monthly with count > 1', () => {
    component.concreteRequest.get('repeatEveryCount').setValue(2);
    jest.spyOn(component, 'getRepeatEveryType').mockImplementation(() => {});
    jest.spyOn(component, 'changeMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.onRecurrenceSelect('Monthly');

    expect(component.isRepeatWithMultipleRecurrence).toBe(true);
    expect(component.isRepeatWithSingleRecurrence).toBe(false);
  });

  // Test for showMonthlyRecurrence method
  it('should handle show monthly recurrence', () => {
    const mockDate = new Date('2023-01-15');
    component.concreteRequest.get('concretePlacementDate').setValue(mockDate);

    component.showMonthlyRecurrence();

    expect(component.monthlyDate).toBe('15');
    expect(component.monthlyDayOfWeek).toBeDefined();
    expect(component.monthlyLastDayOfWeek).toBeDefined();
  });

  // Test for placementDate method
  it('should handle placement date', () => {
    const mockDate = new Date('2023-01-15');
    component.concreteRequest.get('concretePlacementDate').setValue(mockDate);

    component.placementDate();

    expect((component as any).placementDate1).toEqual(mockDate);
  });

  // Test for convertStart method
  it('should handle convert start time', () => {
    const mockDate = new Date('2023-01-15T10:30:00');
    const mockStartTime = '10:30';
    const mockEndTime = '12:30';

    const result = component.convertStart(mockDate, mockStartTime, mockEndTime);

    expect(result).toBe('10:30');
  });

  // Test for selectTime method
  it('should handle select time', () => {
    const startStr = '2023-01-15T10:00:00';
    const endStr = '2023-01-15T12:00:00';
    jest.spyOn(component, 'placementDate').mockImplementation(() => {});
    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});

    component.selectTime(startStr, endStr);

    expect(component.isTimeSlotChoosen).toBe(true);
    expect(component.selectedTime).toBeDefined();
    expect(component.placementDate).toHaveBeenCalled();
    expect(component.showMonthlyRecurrence).toHaveBeenCalled();
  });

  // Test for getSelectedDate method
  it('should handle get selected date', () => {
    component.currentPage = 'Month';

    const result = component.getSelectedDate();

    expect(result).toBeDefined();
  });

  // Test for resetForm method
  it('should handle reset form with yes action', () => {
    jest.spyOn(component, 'concreteRequestCreationForm').mockImplementation(() => {});
    jest.spyOn(component, 'setDefaultDateAndTime').mockImplementation(() => {});

    component.resetForm('yes');

    expect(component.concreteRequestCreationForm).toHaveBeenCalled();
    expect(component.setDefaultDateAndTime).toHaveBeenCalled();
    expect(component.isKeepOpen).toBe(true);
  });

  it('should handle reset form with no action', () => {
    const mockModalRef = { hide: jest.fn() };
    (component as any).modalRef = mockModalRef;

    component.resetForm('no');

    expect(mockModalRef.hide).toHaveBeenCalled();
  });

  // Test for setDefaultDateAndTime method
  it('should handle set default date and time', () => {
    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});

    component.setDefaultDateAndTime(new Date());

    expect(component.showMonthlyRecurrence).toHaveBeenCalled();
    expect(component.concreteRequest.get('concretePlacementDate').value).toBeDefined();
  });

  // Test for openConfirmationModalPopup method
  it('should handle open confirmation modal popup', () => {
    const mockTemplate = {} as any;
    const mockModalRef = { hide: jest.fn() };
    modalServiceMock.show = jest.fn().mockReturnValue(mockModalRef);

    component.openConfirmationModalPopup(mockTemplate);

    expect(modalServiceMock.show).toHaveBeenCalledWith(mockTemplate);
    expect((component as any).modalRef1).toBe(mockModalRef);
  });

  // Test for showError method
  it('should handle show error with status code 400', () => {
    const mockError = {
      message: {
        statusCode: 400,
        details: [{ field: 'Test error' }]
      }
    };

    component.showError(mockError);

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Test error', 'OOPS!');
  });

  it('should handle show error with status code 422', () => {
    const mockError = {
      message: {
        statusCode: 422,
        details: [{ field: 'Validation error' }]
      }
    };

    component.showError(mockError);

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Validation error', 'OOPS!');
  });

  it('should handle show error with generic message', () => {
    const mockError = {
      message: 'Generic error message'
    };

    component.showError(mockError);

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Generic error message', 'OOPS!');
  });

  // Test for checkStringEmptyValues method
  it('should handle check string empty values - empty string', () => {
    const result = component.checkStringEmptyValues('');

    expect(result).toBe(null);
  });

  // Additional positive and negative test cases for 90% coverage

  // Test for getDropdownValues error handling
  it('should handle getDropdownValues error - status code 400', () => {
    const mockError = {
      message: {
        statusCode: 400,
        details: [{ field: 'Dropdown error' }]
      }
    };
    deliveryServiceMock.getConcreteRequestDropdownData = jest.fn().mockReturnValue(throwError(() => mockError));
    jest.spyOn(component, 'showError').mockImplementation(() => {});

    component.getDropdownValues();

    expect(component.showError).toHaveBeenCalledWith(mockError);
  });

  it('should handle getDropdownValues error - no message', () => {
    const mockError = {};
    deliveryServiceMock.getConcreteRequestDropdownData = jest.fn().mockReturnValue(throwError(() => mockError));

    component.getDropdownValues();

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should handle getDropdownValues error - generic error', () => {
    const mockError = { message: 'Generic dropdown error' };
    deliveryServiceMock.getConcreteRequestDropdownData = jest.fn().mockReturnValue(throwError(() => mockError));

    component.getDropdownValues();

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Generic dropdown error', 'OOPS!');
  });

  // Test for openConfirmationModalPopup
  it('should open confirmation modal popup', () => {
    const mockTemplate = {} as any;
    modalServiceMock.show = jest.fn().mockReturnValue({} as any);

    component.openConfirmationModalPopup(mockTemplate);

    expect(modalServiceMock.show).toHaveBeenCalledWith(mockTemplate, {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    });
  });

  // Test for timeZoneSelected
  it('should handle time zone selection', () => {
    component.timezoneList = [
      { id: 1, location: 'UTC' },
      { id: 2, location: 'EST' }
    ] as any;

    component.timeZoneSelected(1);

    expect(component.selectedTimeZoneValue).toEqual({ id: 1, location: 'UTC' });
  });

  // Test for getTimeZoneList success
  it('should handle getTimeZoneList success', () => {
    const mockTimeZoneResponse = {
      data: [
        { id: 1, location: 'UTC' },
        { id: 2, location: 'EST' }
      ]
    };
    const mockProjectResponse = {
      data: { TimeZoneId: 1 }
    };

    projectServiceMock.getTimeZoneList = jest.fn().mockReturnValue(of(mockTimeZoneResponse));
    projectServiceMock.getSingleProject = jest.fn().mockReturnValue(of(mockProjectResponse));
    component.ProjectId = 1;

    component.getTimeZoneList();

    expect(projectServiceMock.getTimeZoneList).toHaveBeenCalled();
    expect(projectServiceMock.getSingleProject).toHaveBeenCalledWith({ ProjectId: 1 });
    expect(component.timezoneList).toEqual(mockTimeZoneResponse.data);
  });

  // Test for getTimeZoneList error handling
  it('should handle getTimeZoneList error - status code 400', () => {
    const mockError = {
      message: {
        statusCode: 400,
        details: [{ field: 'TimeZone error' }]
      }
    };
    projectServiceMock.getTimeZoneList = jest.fn().mockReturnValue(throwError(() => mockError));
    jest.spyOn(component, 'showError').mockImplementation(() => {});

    component.getTimeZoneList();

    expect(component.showError).toHaveBeenCalledWith(mockError);
  });

  it('should handle getTimeZoneList error - no message', () => {
    const mockError = {};
    projectServiceMock.getTimeZoneList = jest.fn().mockReturnValue(throwError(() => mockError));

    component.getTimeZoneList();

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should handle getTimeZoneList error - generic error', () => {
    const mockError = { message: 'TimeZone generic error' };
    projectServiceMock.getTimeZoneList = jest.fn().mockReturnValue(throwError(() => mockError));

    component.getTimeZoneList();

    expect(toastrServiceMock.error).toHaveBeenCalledWith('TimeZone generic error', 'OOPS!');
  });

  // Test for onChange method - positive cases
  it('should handle onChange - add day when checked', () => {
    const mockEvent = {
      target: { checked: true, value: 'Tuesday' }
    };
    component.selectedRecurrence = 'Weekly';
    component.checkform = new UntypedFormBuilder().array([]);
    component.weekDays = [
      { value: 'Monday', checked: false, isDisabled: false },
      { value: 'Tuesday', checked: false, isDisabled: false }
    ];

    component.onChange(mockEvent);

    expect(component.checkform.length).toBe(1);
    expect(component.checkform.at(0).value).toBe('Tuesday');
  });

  // Test for getRepeatEveryType
  it('should handle getRepeatEveryType - Does Not Repeat', () => {
    component.getRepeatEveryType('Does Not Repeat');

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('');
  });

  it('should handle getRepeatEveryType - Daily', () => {
    component.getRepeatEveryType('Daily');

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Day');
    expect(component.concreteRequest.get('repeatEveryCount').value).toBe(1);
  });

  it('should handle getRepeatEveryType - Weekly', () => {
    component.getRepeatEveryType('Weekly');

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Week');
  });

  it('should handle getRepeatEveryType - Monthly', () => {
    component.getRepeatEveryType('Monthly');

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Month');
  });

  it('should handle getRepeatEveryType - Yearly', () => {
    component.getRepeatEveryType('Yearly');

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Year');
  });

  // Test for onRecurrenceSelect method
  it('should handle onRecurrenceSelect - Daily with count 1', () => {
    component.concreteRequest.get('repeatEveryCount').setValue(1);
    jest.spyOn(component, 'getRepeatEveryType').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.onRecurrenceSelect('Daily');

    expect(component.selectedRecurrence).toBe('Daily');
    expect(component.isRepeatWithSingleRecurrence).toBe(true);
    expect(component.isRepeatWithMultipleRecurrence).toBe(false);
    expect(component.getRepeatEveryType).toHaveBeenCalledWith('Daily');
    expect(component.occurMessage).toHaveBeenCalled();
  });

  it('should handle onRecurrenceSelect - Weekly with count > 1', () => {
    component.concreteRequest.get('repeatEveryCount').setValue(2);
    component.weekDays = [
      { value: 'Monday', checked: false, isDisabled: false },
      { value: 'Tuesday', checked: false, isDisabled: false }
    ];
    jest.spyOn(component, 'getRepeatEveryType').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.onRecurrenceSelect('Weekly');

    expect(component.selectedRecurrence).toBe('Weekly');
    expect(component.isRepeatWithMultipleRecurrence).toBe(true);
    expect(component.isRepeatWithSingleRecurrence).toBe(false);
  });

  it('should handle onRecurrenceSelect - Monthly with count 1', () => {
    component.concreteRequest.get('repeatEveryCount').setValue(1);
    jest.spyOn(component, 'getRepeatEveryType').mockImplementation(() => {});
    jest.spyOn(component, 'changeMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.onRecurrenceSelect('Monthly');

    expect(component.selectedRecurrence).toBe('Monthly');
    expect(component.isRepeatWithSingleRecurrence).toBe(true);
    expect(component.isRepeatWithMultipleRecurrence).toBe(false);
    expect(component.concreteRequest.get('chosenDateOfMonth').value).toBe(1);
    expect(component.changeMonthlyRecurrence).toHaveBeenCalled();
    expect(component.showMonthlyRecurrence).toHaveBeenCalled();
  });

  // Test for updateRecurrenceUIState method
  it('should handle updateRecurrenceUIState - Daily with count 1', () => {
    component.concreteRequest.get('recurrence').setValue('Daily');

    component.updateRecurrenceUIState(1);

    expect(component.isRepeatWithSingleRecurrence).toBe(true);
    expect(component.isRepeatWithMultipleRecurrence).toBe(false);
    expect(component.showRecurrenceTypeDropdown).toBe(false);
  });

  it('should handle updateRecurrenceUIState - Daily with count > 1', () => {
    component.concreteRequest.get('recurrence').setValue('Daily');

    component.updateRecurrenceUIState(2);

    expect(component.isRepeatWithSingleRecurrence).toBe(false);
    expect(component.isRepeatWithMultipleRecurrence).toBe(false);
    expect(component.showRecurrenceTypeDropdown).toBe(true);
  });

  it('should handle updateRecurrenceUIState - Weekly with count > 1', () => {
    component.concreteRequest.get('recurrence').setValue('Weekly');

    component.updateRecurrenceUIState(2);

    expect(component.isRepeatWithSingleRecurrence).toBe(false);
    expect(component.isRepeatWithMultipleRecurrence).toBe(true);
    expect(component.showRecurrenceTypeDropdown).toBe(false);
  });

  // Test for updateRecurrenceFormValues method
  it('should handle updateRecurrenceFormValues - Daily with count 1', () => {
    component.concreteRequest.get('recurrence').setValue('Daily');

    component.updateRecurrenceFormValues(1);

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Day');
  });

  it('should handle updateRecurrenceFormValues - Daily with count > 1', () => {
    component.concreteRequest.get('recurrence').setValue('Daily');

    component.updateRecurrenceFormValues(2);

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Days');
  });

  it('should handle updateRecurrenceFormValues - Weekly with count 1', () => {
    component.concreteRequest.get('recurrence').setValue('Weekly');

    component.updateRecurrenceFormValues(1);

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Week');
  });

  it('should handle updateRecurrenceFormValues - Weekly with count > 1', () => {
    component.concreteRequest.get('recurrence').setValue('Weekly');

    component.updateRecurrenceFormValues(2);

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Weeks');
  });

  it('should handle updateRecurrenceFormValues - Monthly with count 1', () => {
    component.concreteRequest.get('recurrence').setValue('Monthly');
    jest.spyOn(component, 'changeMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});

    component.updateRecurrenceFormValues(1);

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Month');
    expect(component.changeMonthlyRecurrence).toHaveBeenCalled();
    expect(component.showMonthlyRecurrence).toHaveBeenCalled();
  });

  it('should handle updateRecurrenceFormValues - Monthly with count > 1', () => {
    component.concreteRequest.get('recurrence').setValue('Monthly');
    jest.spyOn(component, 'changeMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});

    component.updateRecurrenceFormValues(2);

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Months');
    expect(component.changeMonthlyRecurrence).toHaveBeenCalled();
    expect(component.showMonthlyRecurrence).toHaveBeenCalled();
  });

  it('should handle updateRecurrenceFormValues - Yearly with count 1', () => {
    component.concreteRequest.get('recurrence').setValue('Yearly');

    component.updateRecurrenceFormValues(1);

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Year');
  });

  it('should handle updateRecurrenceFormValues - Yearly with count > 1', () => {
    component.concreteRequest.get('recurrence').setValue('Yearly');

    component.updateRecurrenceFormValues(2);

    expect(component.concreteRequest.get('repeatEveryType').value).toBe('Years');
  });

  // Test for changeMonthlyRecurrence method
  it('should handle changeMonthlyRecurrence', () => {
    jest.spyOn(component, 'setMonthlyOrYearlyRecurrenceOption').mockImplementation(() => {});
    jest.spyOn(component, 'updateFormValidation').mockImplementation(() => {});
    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.changeMonthlyRecurrence();

    expect(component.setMonthlyOrYearlyRecurrenceOption).toHaveBeenCalled();
    expect(component.updateFormValidation).toHaveBeenCalled();
    expect(component.showMonthlyRecurrence).toHaveBeenCalled();
    expect(component.occurMessage).toHaveBeenCalled();
  });

  // Test for setMonthlyOrYearlyRecurrenceOption method
  it('should handle setMonthlyOrYearlyRecurrenceOption - chosenDateOfMonth 1', () => {
    component.concreteRequest.get('chosenDateOfMonth').setValue(1);
    component.concreteRequest.get('concretePlacementDate').setValue('01/15/2023');

    component.setMonthlyOrYearlyRecurrenceOption();

    expect(component.concreteRequest.get('dateOfMonth').value).toBe('15');
    expect(component.concreteRequest.get('monthlyRepeatType').value).toBeNull();
  });

  it('should handle setMonthlyOrYearlyRecurrenceOption - chosenDateOfMonth 2', () => {
    component.concreteRequest.get('chosenDateOfMonth').setValue(2);
    component.monthlyDayOfWeek = 'First Monday';

    component.setMonthlyOrYearlyRecurrenceOption();

    expect(component.concreteRequest.get('dateOfMonth').value).toBeNull();
    expect(component.concreteRequest.get('monthlyRepeatType').value).toBe('First Monday');
  });

  it('should handle setMonthlyOrYearlyRecurrenceOption - chosenDateOfMonth 3', () => {
    component.concreteRequest.get('chosenDateOfMonth').setValue(3);
    component.monthlyLastDayOfWeek = 'Last Monday';

    component.setMonthlyOrYearlyRecurrenceOption();

    expect(component.concreteRequest.get('dateOfMonth').value).toBeNull();
    expect(component.concreteRequest.get('monthlyRepeatType').value).toBe('Last Monday');
  });

  // Test for showMonthlyRecurrence method
  it('should handle showMonthlyRecurrence with valid date', () => {
    component.concreteRequest.get('concretePlacementDate').setValue('01/15/2023');
    jest.spyOn(component, 'setMonthlyOrYearlyRecurrenceOption').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.showMonthlyRecurrence();

    expect(component.monthlyDate).toBe('15');
    expect(component.setMonthlyOrYearlyRecurrenceOption).toHaveBeenCalled();
    expect(component.occurMessage).toHaveBeenCalled();
  });

  it('should handle showMonthlyRecurrence with fourth week enabling extra option', () => {
    // Mock a date that falls on the 4th occurrence of a weekday in the month
    component.concreteRequest.get('concretePlacementDate').setValue('01/22/2023'); // 4th Sunday
    jest.spyOn(component, 'setMonthlyOrYearlyRecurrenceOption').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.showMonthlyRecurrence();

    expect(component.enableOption).toBe(true);
    expect(component.setMonthlyOrYearlyRecurrenceOption).toHaveBeenCalled();
  });

  it('should handle showMonthlyRecurrence without enableOption and chosenDateOfMonth 3', () => {
    component.concreteRequest.get('concretePlacementDate').setValue('01/08/2023'); // 2nd Sunday
    component.concreteRequest.get('chosenDateOfMonth').setValue(3);
    component.enableOption = false;
    jest.spyOn(component, 'setMonthlyOrYearlyRecurrenceOption').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.showMonthlyRecurrence();

    expect(component.concreteRequest.get('chosenDateOfMonth').value).toBe(2);
    expect(component.concreteRequest.get('dateOfMonth').value).toBeNull();
  });

  // Test for occurMessage method variations
  it('should handle occurMessage for Week recurrence', () => {
    component.concreteRequest.get('repeatEveryType').setValue('Week');
    component.concreteRequest.get('endDate').setValue('12/31/2023');
    component.weekDays = [
      { value: 'Monday', checked: true },
      { value: 'Wednesday', checked: true },
      { value: 'Friday', checked: false }
    ];

    component.occurMessage();

    expect(component.message).toContain('Occurs every Monday,Wednesday');
    expect(component.message).toContain('until December 31, 2023');
  });

  it('should handle occurMessage for Weeks with count 2', () => {
    component.concreteRequest.get('repeatEveryType').setValue('Weeks');
    component.concreteRequest.get('repeatEveryCount').setValue(2);
    component.concreteRequest.get('endDate').setValue('12/31/2023');
    component.weekDays = [
      { value: 'Monday', checked: true },
      { value: 'Tuesday', checked: false }
    ];

    component.occurMessage();

    expect(component.message).toContain('Occurs every other  Monday');
  });

  it('should handle occurMessage for Weeks with count > 2', () => {
    component.concreteRequest.get('repeatEveryType').setValue('Weeks');
    component.concreteRequest.get('repeatEveryCount').setValue(3);
    component.concreteRequest.get('endDate').setValue('12/31/2023');
    component.weekDays = [
      { value: 'Monday', checked: true },
      { value: 'Tuesday', checked: false }
    ];

    component.occurMessage();

    expect(component.message).toContain('Occurs every 3 weeks on Monday');
  });

  // Test for chooseRepeatEveryType method variations
  it('should handle chooseRepeatEveryType - Days', () => {
    component.weekDays = [
      { value: 'Monday', checked: false, isDisabled: false },
      { value: 'Tuesday', checked: false, isDisabled: false }
    ];
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.chooseRepeatEveryType('Days');

    expect(component.concreteRequest.get('recurrence').value).toBe('Daily');
    expect(component.showRecurrenceTypeDropdown).toBe(true);
    expect(component.occurMessage).toHaveBeenCalled();
  });

  it('should handle chooseRepeatEveryType - Weeks', () => {
    component.weekDays = [
      { value: 'Monday', checked: false, isDisabled: false },
      { value: 'Tuesday', checked: false, isDisabled: false }
    ];
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.chooseRepeatEveryType('Weeks');

    expect(component.concreteRequest.get('recurrence').value).toBe('Weekly');
    expect(component.isRepeatWithMultipleRecurrence).toBe(true);
    expect(component.isRepeatWithSingleRecurrence).toBe(false);
  });

  it('should handle chooseRepeatEveryType - Months', () => {
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.chooseRepeatEveryType('Months');

    expect(component.concreteRequest.get('recurrence').value).toBe('Monthly');
    expect(component.isRepeatWithMultipleRecurrence).toBe(true);
    expect(component.isRepeatWithSingleRecurrence).toBe(false);
  });

  it('should handle chooseRepeatEveryType - Years', () => {
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.chooseRepeatEveryType('Years');

    expect(component.concreteRequest.get('recurrence').value).toBe('Yearly');
    expect(component.isRepeatWithMultipleRecurrence).toBe(true);
    expect(component.isRepeatWithSingleRecurrence).toBe(false);
  });

  // Test for checkRepeatType method
  it('should handle checkRepeatType for Month with chosenDateOfMonth 1', () => {
    component.concreteRequest.get('repeatEveryType').setValue('Month');
    component.concreteRequest.get('chosenDateOfMonth').setValue(1);
    component.monthlyDate = '15';

    component.checkRepeatType();

    expect(component.message).toBe('Occurs on day 15');
  });

  it('should handle checkRepeatType for Months with chosenDateOfMonth 2', () => {
    component.concreteRequest.get('repeatEveryType').setValue('Months');
    component.concreteRequest.get('chosenDateOfMonth').setValue(2);
    component.monthlyDayOfWeek = 'First Monday';

    component.checkRepeatType();

    expect(component.message).toBe('Occurs on the First Monday');
  });

  it('should handle checkRepeatType for Year with chosenDateOfMonth 3', () => {
    component.concreteRequest.get('repeatEveryType').setValue('Year');
    component.concreteRequest.get('chosenDateOfMonth').setValue(3);
    component.monthlyLastDayOfWeek = 'Last Friday';

    component.checkRepeatType();

    expect(component.message).toBe('Occurs on the Last Friday');
  });

  it('should handle checkRepeatType for Years with chosenDateOfMonth 3', () => {
    component.concreteRequest.get('repeatEveryType').setValue('Years');
    component.concreteRequest.get('chosenDateOfMonth').setValue(3);
    component.monthlyLastDayOfWeek = 'Last Friday';

    component.checkRepeatType();

    expect(component.message).toBe('Occurs on the Last Friday');
  });

  // Test for changeRecurrenceCount method
  it('should handle changeRecurrenceCount with positive value', () => {
    jest.spyOn(component, 'updateRecurrenceUIState').mockImplementation(() => {});
    jest.spyOn(component, 'updateRecurrenceFormValues').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.changeRecurrenceCount(3);

    expect(component.updateRecurrenceUIState).toHaveBeenCalledWith(3);
    expect(component.updateRecurrenceFormValues).toHaveBeenCalledWith(3);
    expect(component.occurMessage).toHaveBeenCalled();
  });

  it('should handle changeRecurrenceCount with zero value', () => {
    component.changeRecurrenceCount(0);

    expect(component.concreteRequest.get('repeatEveryCount').value).toBe(1);
  });

  it('should handle changeRecurrenceCount with negative value', () => {
    component.changeRecurrenceCount(-5);

    expect(component.concreteRequest.get('repeatEveryCount').value).toBe(1);
  });

  // Test for resetForm method with modalRef1
  it('should handle resetForm with action no and modalRef1', () => {
    const mockModalRef1 = { hide: jest.fn() };
    (component as any).modalRef1 = mockModalRef1;

    component.resetForm('no');

    expect(mockModalRef1.hide).toHaveBeenCalled();
  });

  it('should handle resetForm with action yes and both modalRefs', () => {
    const mockModalRef = { hide: jest.fn() };
    const mockModalRef1 = { hide: jest.fn() };
    (component as any).modalRef = mockModalRef;
    (component as any).modalRef1 = mockModalRef1;
    jest.spyOn(component, 'getDropdownValues').mockImplementation(() => {});

    component.resetForm('yes');

    expect(mockModalRef1.hide).toHaveBeenCalled();
    expect(mockModalRef.hide).toHaveBeenCalled();
    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
    expect(component.NDRTimingChanged).toBe(false);
    expect(component.getDropdownValues).toHaveBeenCalled();
  });

  // Test for getDropdownValues with successful response and default location
  it('should handle getDropdownValues with successful response and default location', () => {
    const mockResponse = {
      data: {
        ConcreteRequestId: 123,
        locationDropdown: [
          {
            id: 1,
            locationPath: 'Location 1',
            EquipmentId: [{ id: 1, equipmentName: 'Equipment 1' }],
            gateDetails: [{ id: 1, gateName: 'Gate 1' }],
            TimeZoneId: [{ location: 'UTC' }],
            isDefault: true
          },
          {
            id: 2,
            locationPath: 'Location 2',
            isDefault: false
          }
        ],
        locationDetailsDropdown: [],
        concreteSupplierDropdown: [{ id: 1, companyName: 'Supplier 1' }],
        mixDesignDropdown: [],
        pumpSizeDropdown: []
      }
    };

    const mockLoginUser = {
      CompanyId: 1,
      id: 1,
      User: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      }
    };

    component.ProjectId = 1;
    component.ParentCompanyId = 1;
    deliveryServiceMock.getConcreteRequestDropdownData = jest.fn().mockReturnValue(of(mockResponse));
    deliveryServiceMock.loginUser = { value: mockLoginUser } as any;

    component.getDropdownValues();

    expect(deliveryServiceMock.getConcreteRequestDropdownData).toHaveBeenCalledWith({
      ProjectId: 1,
      ParentCompanyId: 1
    });
    expect(component.ConcreteRequestId).toBe(123);
    expect(component.locationDropdown).toEqual(mockResponse.data.locationDropdown);
    expect(component.selectedLocationId).toBe(1);
    expect(component.defaultLocationValue).toEqual([mockResponse.data.locationDropdown[0]]);
  });

  // Test for getDropdownValues with loginUser without lastName
  it('should handle getDropdownValues with loginUser without lastName', () => {
    const mockResponse = {
      data: {
        ConcreteRequestId: 123,
        locationDropdown: [],
        locationDetailsDropdown: [],
        concreteSupplierDropdown: [{ id: 1, companyName: 'Supplier 1' }],
        mixDesignDropdown: [],
        pumpSizeDropdown: []
      }
    };

    const mockLoginUser = {
      CompanyId: 1,
      id: 1,
      User: {
        firstName: 'John',
        lastName: null,
        email: '<EMAIL>'
      }
    };

    component.ProjectId = 1;
    component.ParentCompanyId = 1;
    deliveryServiceMock.getConcreteRequestDropdownData = jest.fn().mockReturnValue(of(mockResponse));
    deliveryServiceMock.loginUser = { value: mockLoginUser } as any;

    component.getDropdownValues();

    const expectedEmail = 'John (<EMAIL>)';
    const expectedMemberList = [{
      email: expectedEmail,
      id: 1,
      readonly: true
    }];

    expect(component.concreteRequest.get('responsiblePersons').value).toEqual(expectedMemberList);
  });

  // Test for setCurrentTiming method
  it('should handle setCurrentTiming with existing endDate', () => {
    component.concreteRequest.get('endDate').setValue('12/31/2023');
    jest.spyOn(component, 'changeMonthlyRecurrence').mockImplementation(() => {});

    component.setCurrentTiming();

    expect(component.startTime).toBeDefined();
    expect(component.endTime).toBeDefined();
    expect(component.changeMonthlyRecurrence).toHaveBeenCalled();
    // Should not change existing endDate
    expect(component.concreteRequest.get('endDate').value).toBe('12/31/2023');
  });

  // Test for updateFormValidation method
  it('should handle updateFormValidation with chosenDateOfMonth 1', () => {
    component.concreteRequest.get('chosenDateOfMonth').setValue(1);

    component.updateFormValidation();

    expect(component.concreteRequest.get('dateOfMonth').hasError('required')).toBe(true);
    expect(component.concreteRequest.get('monthlyRepeatType').hasError('required')).toBe(false);
  });

  it('should handle updateFormValidation with chosenDateOfMonth not 1', () => {
    component.concreteRequest.get('chosenDateOfMonth').setValue(2);

    component.updateFormValidation();

    expect(component.concreteRequest.get('dateOfMonth').hasError('required')).toBe(false);
    expect(component.concreteRequest.get('monthlyRepeatType').hasError('required')).toBe(true);
  });

  // Test for formControlValueChanged1 method with different values
  it('should handle formControlValueChanged1 with Week value', () => {
    component.concreteRequestCreationForm();

    component.concreteRequest.get('repeatEveryType').setValue('Week');

    expect(component.concreteRequest.get('days').hasError('required')).toBe(true);
  });

  it('should handle formControlValueChanged1 with Day value', () => {
    component.concreteRequestCreationForm();

    component.concreteRequest.get('repeatEveryType').setValue('Day');

    expect(component.concreteRequest.get('days').hasError('required')).toBe(true);
  });

  it('should handle formControlValueChanged1 with Weeks value', () => {
    component.concreteRequestCreationForm();

    component.concreteRequest.get('repeatEveryType').setValue('Weeks');

    expect(component.concreteRequest.get('days').hasError('required')).toBe(true);
  });

  it('should handle formControlValueChanged1 with Month value and chosenDateOfMonth 1', () => {
    component.concreteRequestCreationForm();
    component.concreteRequest.get('chosenDateOfMonth').setValue(1);

    component.concreteRequest.get('repeatEveryType').setValue('Month');

    expect(component.concreteRequest.get('dateOfMonth').hasError('required')).toBe(true);
    expect(component.concreteRequest.get('monthlyRepeatType').hasError('required')).toBe(false);
  });

  it('should handle formControlValueChanged1 with Months value and chosenDateOfMonth not 1', () => {
    component.concreteRequestCreationForm();
    component.concreteRequest.get('chosenDateOfMonth').setValue(2);

    component.concreteRequest.get('repeatEveryType').setValue('Months');

    expect(component.concreteRequest.get('dateOfMonth').hasError('required')).toBe(false);
    expect(component.concreteRequest.get('monthlyRepeatType').hasError('required')).toBe(true);
  });

  it('should handle formControlValueChanged1 with Year value and chosenDateOfMonth 1', () => {
    component.concreteRequestCreationForm();
    component.concreteRequest.get('chosenDateOfMonth').setValue(1);

    component.concreteRequest.get('repeatEveryType').setValue('Year');

    expect(component.concreteRequest.get('dateOfMonth').hasError('required')).toBe(true);
    expect(component.concreteRequest.get('monthlyRepeatType').hasError('required')).toBe(false);
  });

  it('should handle formControlValueChanged1 with Years value and chosenDateOfMonth not 1', () => {
    component.concreteRequestCreationForm();
    component.concreteRequest.get('chosenDateOfMonth').setValue(2);

    component.concreteRequest.get('repeatEveryType').setValue('Years');

    expect(component.concreteRequest.get('dateOfMonth').hasError('required')).toBe(false);
    expect(component.concreteRequest.get('monthlyRepeatType').hasError('required')).toBe(true);
  });

  // Test for onRecurrenceSelect with Daily and count > 1
  it('should handle onRecurrenceSelect - Daily with count > 1', () => {
    component.concreteRequest.get('repeatEveryCount').setValue(2);
    component.weekDays = [
      { value: 'Monday', checked: false, isDisabled: false },
      { value: 'Tuesday', checked: false, isDisabled: false }
    ];
    jest.spyOn(component, 'getRepeatEveryType').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.onRecurrenceSelect('Daily');

    expect(component.selectedRecurrence).toBe('Daily');
    expect(component.isRepeatWithMultipleRecurrence).toBe(false);
    expect(component.isRepeatWithSingleRecurrence).toBe(false);
    expect(component.checkform.length).toBe(2); // All weekdays should be added
  });

  it('should handle onRecurrenceSelect - Weekly with count 1', () => {
    component.concreteRequest.get('repeatEveryCount').setValue(1);
    component.weekDays = [
      { value: 'Monday', checked: false, isDisabled: false },
      { value: 'Tuesday', checked: false, isDisabled: false }
    ];
    jest.spyOn(component, 'getRepeatEveryType').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.onRecurrenceSelect('Weekly');

    expect(component.selectedRecurrence).toBe('Weekly');
    expect(component.isRepeatWithSingleRecurrence).toBe(true);
    expect(component.isRepeatWithMultipleRecurrence).toBe(false);
    expect(component.checkform.length).toBe(1); // Only Monday should be selected
    expect(component.checkform.at(0).value).toBe('Monday');
  });

  it('should handle onRecurrenceSelect - Monthly with count > 1', () => {
    component.concreteRequest.get('repeatEveryCount').setValue(2);
    jest.spyOn(component, 'getRepeatEveryType').mockImplementation(() => {});
    jest.spyOn(component, 'changeMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.onRecurrenceSelect('Monthly');

    expect(component.selectedRecurrence).toBe('Monthly');
    expect(component.isRepeatWithMultipleRecurrence).toBe(true);
    expect(component.isRepeatWithSingleRecurrence).toBe(false);
    expect(component.concreteRequest.get('chosenDateOfMonth').value).toBe(1);
  });

  it('should handle onRecurrenceSelect - Yearly with count 1', () => {
    component.concreteRequest.get('repeatEveryCount').setValue(1);
    jest.spyOn(component, 'getRepeatEveryType').mockImplementation(() => {});
    jest.spyOn(component, 'changeMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.onRecurrenceSelect('Yearly');

    expect(component.selectedRecurrence).toBe('Yearly');
    expect(component.isRepeatWithSingleRecurrence).toBe(true);
    expect(component.isRepeatWithMultipleRecurrence).toBe(false);
    expect(component.concreteRequest.get('chosenDateOfMonth').value).toBe(1);
  });

  it('should handle onRecurrenceSelect - Yearly with count > 1', () => {
    component.concreteRequest.get('repeatEveryCount').setValue(3);
    jest.spyOn(component, 'getRepeatEveryType').mockImplementation(() => {});
    jest.spyOn(component, 'changeMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.onRecurrenceSelect('Yearly');

    expect(component.selectedRecurrence).toBe('Yearly');
    expect(component.isRepeatWithMultipleRecurrence).toBe(true);
    expect(component.isRepeatWithSingleRecurrence).toBe(false);
    expect(component.concreteRequest.get('chosenDateOfMonth').value).toBe(1);
  });

  // Test for onChange method with different scenarios
  it('should handle onChange - enable all days when 2 controls exist', () => {
    const mockEvent = {
      target: { checked: true, value: 'Wednesday' }
    };
    component.selectedRecurrence = 'Weekly';
    component.checkform = new UntypedFormBuilder().array([
      new UntypedFormBuilder().control('Monday')
    ]);
    component.weekDays = [
      { value: 'Monday', checked: true, isDisabled: true },
      { value: 'Wednesday', checked: false, isDisabled: false }
    ];

    component.onChange(mockEvent);

    expect(component.checkform.length).toBe(2);
    // Should enable all days when we have 2 controls
    const mondayDay = component.weekDays.find(day => day.value === 'Monday');
    expect(mondayDay.isDisabled).toBe(false);
  });

  // Test for sortWeekDays with single day
  it('should handle sortWeekDays with single day', () => {
    const singleDay = ['Friday'];
    const result = component.sortWeekDays(singleDay);

    expect(result).toEqual(['Friday']);
  });

  // Test for checkStringEmptyValues with different scenarios
  it('should handle checkStringEmptyValues with valid description and pump not required', () => {
    const mockFormValue = {
      description: 'Valid description',
      isPumpRequired: false,
      pumpLocation: ''
    };

    const result = component.checkStringEmptyValues(mockFormValue);

    expect(result).toBe(false);
  });

  // Test for showError with different error structures
  it('should handle showError with array details', () => {
    const mockError = {
      message: {
        details: [{ field: 'Error message 1' }, { field: 'Error message 2' }]
      }
    };

    component.showError(mockError);

    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
    expect(toastrServiceMock.error).toHaveBeenCalledWith('Error message 1');
  });

  // Test for locationSelected with no matching location
  it('should handle locationSelected with no matching location', () => {
    component.locationDropdown = [
      { id: 1, locationPath: 'Location 1' },
      { id: 2, locationPath: 'Location 2' }
    ];

    component.locationSelected({ id: 999 });

    expect(component.selectedLocationId).toBeUndefined();
  });

  // Test for vehicleTypeSelected with no matching type
  it('should handle vehicleTypeSelected with no matching type', () => {
    component.vehicleTypeSelected({ id: 999 });

    expect(component.selectedVehicleType).toBeUndefined();
  });

  it('should handle pumpVehicleTypeSelected with no matching type', () => {
    component.pumpVehicleTypeSelected({ id: 999 });

    expect(component.selectedPumpVehicleType).toBeUndefined();
  });

  // Test for timeZoneSelected with no matching timezone
  it('should handle timeZoneSelected with no matching timezone', () => {
    component.timezoneList = [
      { id: 1, location: 'UTC' },
      { id: 2, location: 'EST' }
    ] as any;

    component.timeZoneSelected(999);

    expect(component.selectedTimeZoneValue).toBeUndefined();
  });

  // Test for getTimeZoneList without ProjectId
  it('should handle getTimeZoneList without ProjectId', () => {
    const mockTimeZoneResponse = { data: [] };
    projectServiceMock.getTimeZoneList = jest.fn().mockReturnValue(of(mockTimeZoneResponse));
    component.ProjectId = null;

    component.getTimeZoneList();

    expect(projectServiceMock.getTimeZoneList).toHaveBeenCalled();
    // Should not call getSingleProject without ProjectId
    expect(projectServiceMock.getSingleProject).not.toHaveBeenCalled();
  });

  // Test for close method with mixDesign changes
  it('should handle close with mixDesign changes', () => {
    const mockTemplate = {} as any;
    component.concreteRequest.get('mixDesign').markAsDirty();
    component.concreteRequest.get('mixDesign').setValue(['design1']);

    jest.spyOn(component, 'openConfirmationModalPopup').mockImplementation(() => {});

    component.close(mockTemplate);

    expect(component.openConfirmationModalPopup).toHaveBeenCalledWith(mockTemplate);
  });

  it('should handle close with pumpSize changes', () => {
    const mockTemplate = {} as any;
    component.concreteRequest.get('pumpSize').markAsDirty();
    component.concreteRequest.get('pumpSize').setValue(['size1']);

    jest.spyOn(component, 'openConfirmationModalPopup').mockImplementation(() => {});

    component.close(mockTemplate);

    expect(component.openConfirmationModalPopup).toHaveBeenCalledWith(mockTemplate);
  });
});
