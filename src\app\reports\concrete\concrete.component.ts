import moment from 'moment';

import { ToastrService } from 'ngx-toastr';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

import { Component, OnInit, TemplateRef, ElementRef, HostListener } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';

import { ReportsService } from '../../services/reports/reports.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';
import { SchedulerFormComponent } from '../scheduler-form/scheduler-form.component';
import { SaveReportFormComponent } from '../save-report-form/save-report-form.component';

@Component({
  selector: 'app-concrete',
  templateUrl: './concrete.component.html',
})
export class ConcreteComponent implements OnInit {
  public currentPageNo = 1;

  public pageSize = 25;

  public collection: any[] = [1, 2, 3, 4, 56, 7, 8, 9, 0, 1, 2, 2, 2, 2, 2, 2, 2];

  public perPage = 1;

  public filterCount = 0;

  public showSearchbar = false;

  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public modalRef2: BsModalRef;

  public modalRef3: BsModalRef;

  public modalRef4: BsModalRef;

  public scheduleFormModalRef: BsModalRef;

  public selectedRecurrence = 'Does Not Repeat';

  public autocompleteItemsAsObjects = [{ email: '<EMAIL>' }, { email: '<EMAIL>' }];

  public ProjectId: string | number;

  public sortColumn = 'concretePlacementStart';

  public sort = 'ASC';

  public filterForm: UntypedFormGroup;

  public memberList: any = [];

  public wholeStatus = ['Approved', 'Completed', 'Expired', 'Tentative'];


  public isEquipmentDropdownOpen: boolean = false;
  public isStatusDropdownOpen: boolean = false;
  public isGateDropdownOpen: boolean = false;
  public isCompanyDropdownOpen: boolean = false;
  public isLocationDropdownOpen: boolean = false;
  public isResponsibleDropdownOpen: boolean = false;
  public isDefineDropdownOpen: boolean = false;

  public selectedEquipment: any[] = [];
  public selectedGate: any[] = [];
  public selectedCompany: any[] = [];
  public selectedLocation: any[] = [];
  public selectedResponsible: any[] = [];
  public selectedDefine: any[] = [];

  // Custom Placeholder Strings
  public equipmentPlaceholder: string = 'Select Equipment';
  public gatePlaceholder: string = 'Select Gate';
  public companyPlaceholder: string = 'Select Concrete Supplier';
  public locationPlaceholder: string = 'Select Location';
  public statusPlaceholder: string = 'Select Status';
  public responsiblePlaceholder: string = 'Select Responsible Person';
  public definePlaceholder: string = 'Select DFOW';

  public statusList = [
    { name: 'Approved' },
    { name: 'Completed' },
    { name: 'Expired' },
    { name: 'Tentative' }
  ];


  selectedLocations: any = [];

  selectedStatus: any = [];

  selectedFilters: any;

  public loader = false;

  public concreteRequestList: any[];

  public ParentCompanyId: any;

  public pageNo = 1;

  public concreteRequestTotalCount = 0;

  public locationDropdown: [];

  public concreteSupplierDropdown: any[];

  public mixDesignDropdown: any[];

  public updatedMemberList: any = [];

  public exportType = ['EXCEL', 'CSV', 'PDF'];

  public scheduleData = {};

  public locationList: any = [];

  public tableHeaders: any = {
    id: { isActive: true, key: 'id', title: 'Id' },
    description: { isActive: true, key: 'description', title: 'Description' },
    date: { isActive: true, key: 'date', title: 'Date & Time' },
    status: { isActive: true, key: 'status', title: 'Status' },
    approvedby: { isActive: true, key: 'approvedby', title: 'Approved By' },
    company: { isActive: false, key: 'company', title: 'Concrete Supplier' },
    ordernumber: { isActive: false, key: 'orderNumber', title: 'Order Number' },
    slump: { isActive: false, key: 'slump', title: 'Slump' },
    truckspacing: { isActive: false, key: 'truckSpacing', title: 'Truck Spacing' },
    primer: { isActive: false, key: 'primer', title: 'Primer Ordered' },
    name: { isActive: false, key: 'name', title: 'Responsible Person' },
    quantity: { isActive: false, key: 'quantity', title: 'Quantity Ordered' },
    mixDesign: { isActive: false, key: 'mixDesign', title: 'Mix Design' },
    location: { isActive: false, key: 'location', title: 'Location' },
  };

  public exportForm: UntypedFormGroup;

  public formSubmitted = false;

  public seletedValue;

  public seletedValue1;

  public activeHeaders = [];

  public reportType = 'PDF';

  public reportName = 'Concrete Report';

  public exportSubmitted = false;

  public filterPayload: any;

  filteredLocationList: any = [];

  searchLocation: string = '';


  public constructor(
    private readonly modalService: BsModalService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly deliveryService: DeliveryService,
    private readonly projectService: ProjectService,
    public reportsService: ReportsService,
    private readonly toastr: ToastrService,
    private readonly eRef: ElementRef
  ) {
    this.seletedValue = 'PDF';
    this.seletedValue1 = 'Concrete Report';
    this.projectService.projectParent.subscribe((response19): void => {
      if (response19 !== undefined && response19 !== null && response19 !== '') {
        this.loader = true;
        this.concreteRequestList = [];
        this.ProjectId = response19.ProjectId;
        this.ParentCompanyId = response19.ParentCompanyId;
        this.filterDetailsForm();
        this.getConcreteReport();
        this.getFilterDropdown();
        this.exportDetailsForm();
        this.getMembers();
        this.getLocationsForNdrGrid();
      }
    });
    this.activeHeaders = Object.values(this.tableHeaders).filter(
      (element: { isActive: boolean }): boolean => element.isActive === true,
    );
  }

  @HostListener('document:click', ['$event'])
  public onDocumentClick(event: Event): void {
    const modalElement = document.getElementById('fiter-temp3'); // Your modal container ID

    // Check if the clicked element is inside the modal or a dropdown trigger
    if (
      modalElement?.contains(event.target as Node) ||
      (event.target as HTMLElement).closest('.custom-dropdown') ||
      (event.target as HTMLElement).closest('.dropdown-list')
    ) {
      return; // Do nothing if clicking inside the modal, dropdown trigger, or dropdown menu
    }

    // Click happened outside, close all dropdowns
    this.isEquipmentDropdownOpen = false;
    this.isStatusDropdownOpen = false;
    this.isGateDropdownOpen = false;
    this.isCompanyDropdownOpen = false;
    this.isLocationDropdownOpen = false;
    this.isResponsibleDropdownOpen = false;
    this.isDefineDropdownOpen = false;
  }

  public openModal(template: TemplateRef<any>): void {
    this.modalRef = this.modalService.show(template, {
      class: 'modal-export',
    });
  }

  public openModal1(template1: TemplateRef<any>): void {
    this.modalRef1 = this.modalService.show(template1, {
      class: 'modal-downloads',
    });
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.toggleDropdown(data);
    }
  }

  public openSchedulePopup(): void {
    this.scheduleFormModalRef = this.modalService.show(SchedulerFormComponent);
    this.scheduleFormModalRef.content.ProjectId = this.ProjectId;
    this.scheduleFormModalRef.content.ParentCompanyId = this.ParentCompanyId;
    this.scheduleFormModalRef.content.reportType = 'Concrete';
    this.scheduleFormModalRef.content.updatedMemberList = this.updatedMemberList;
    this.scheduleFormModalRef.content.exportType = this.exportType;
    this.scheduleFormModalRef.content.filterPayload = this.filterPayload;
    this.scheduleFormModalRef.content.selectedHeaders = Object.values(this.tableHeaders);
    this.scheduleFormModalRef.content.sort = this.sort;
    this.scheduleFormModalRef.content.sortByField = this.sortColumn;
  }

  public openModalSave(): void {
    this.filterPayload.selectedHeaders = Object.values(this.tableHeaders);
    const modalRef = this.modalService.show(SaveReportFormComponent, { class: 'modal-save' });
    modalRef.content.ProjectId = this.ProjectId;
    modalRef.content.reportType = 'Concrete';
    modalRef.content.updatedMemberList = this.updatedMemberList;
    modalRef.content.exportType = this.exportType;
    modalRef.content.filterPayload = this.filterPayload;
    modalRef.content.pageSize = this.pageSize;
    modalRef.content.pageNo = this.pageNo;
    modalRef.content.sortOrder = 'asc';
    modalRef.content.void = 0;
  }

  public openModal3(template3: TemplateRef<any>): void {
    this.modalRef3 = this.modalService.show(template3, {
      class: 'modal-sendto',
    });
  }

  public openModal4(template4: TemplateRef<any>): void {
    this.modalRef4 = this.modalService.show(template4, {
      class: ' report-filter-modal filter-popup report-filter custom-modal',
    });
  }

  public onRecurrenceSelect(value): void {
    this.selectedRecurrence = value;
  }

  public ngOnInit(): void {
    document.addEventListener('DOMContentLoaded', (): any => {
      const customTableResponsive = document.querySelector('.custom-table-responsive');
      const htmlElement = document.querySelector('html');
      if (customTableResponsive !== null) {
        htmlElement?.classList.add('custom-class');
      }
    });
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      descriptionFilter: [''],
      statusFilter: [''],
      dateFilter: [''],
      memberFilter: [''],
      gateFilter: [''],
      equipmentFilter: [''],
      defineFilter: [''],
      idFilter: [''],
      concreteSupplierFilter: [''],
      orderNumberFilter: [''],
      quantityFilter: [''],
      slumpFilter: [''],
      truckspacingFilter: [''],
      primerFilter: [''],
      locationFilter: [''],
    });
  }

  public sortByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getConcreteReport();
  }

  public getConcreteReport(): void {
    this.loader = true;
    this.concreteRequestList = [];
    const getConcreteRequestParam = {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.pageNo,
      void: 0,
    };
    let getConcreteRequestPayload: any = {};
    if (this.filterForm !== undefined) {
      const fromdate = this.filterForm.value.dateFilter
        ? moment(this.filterForm.value.dateFilter[0]).format('YYYY-MM-DD')
        : this.filterForm.value.dateFilter;
      const todate = this.filterForm.value.dateFilter
        ? moment(this.filterForm.value.dateFilter[1]).format('YYYY-MM-DD')
        : this.filterForm.value.dateFilter;
      getConcreteRequestPayload = {
        descriptionFilter: this.filterForm.value.descriptionFilter,
        orderNumberFilter: this.filterForm.value.orderNumberFilter,
        concreteSupplierFilter: this.filterForm.value.concreteSupplierFilter.length?this.filterForm.value.concreteSupplierFilter.map((item) => item):[],
        startdate: fromdate,
        enddate: todate,
        truckspacingFilter: this.filterForm.value.truckspacingFilter,
        slumpFilter: this.filterForm.value.slumpFilter,
        primerFilter: this.filterForm.value.primerFilter,
        quantityFilter: this.filterForm.value.quantityFilter,
        statusFilter: this.filterForm.value.statusFilter.length?this.filterForm.value.statusFilter.map((item) => item):[],
        memberFilter: this.filterForm.value.memberFilter.length?this.filterForm.value.memberFilter.map((item) => item):[],
        locationFilter: this.filterForm.value.locationFilter.length ? this.filterForm.value.locationFilter.map((item) => item) : [],
        idFilter: this.filterForm.value.idFilter?this.filterForm.value.idFilter: 0,
      };
    }
    getConcreteRequestPayload.sort = this.sort;
    getConcreteRequestPayload.sortByField = this.sortColumn;
    getConcreteRequestPayload.ParentCompanyId = this.ParentCompanyId;
    this.filterPayload = getConcreteRequestPayload;
    this.reportsService
      .concreteReports(getConcreteRequestParam, getConcreteRequestPayload)
      .subscribe((response: any): void => {
        if (response) {
          this.loader = false;
          this.concreteRequestList = response?.data.rows;
          this.concreteRequestTotalCount = response?.data?.count;
        }
      });
  }

  public changePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.getConcreteReport();
  }


  public toggleDropdown(type: string): void {
    const isCurrentlyOpen = this[`is${type.charAt(0).toUpperCase() + type.slice(1)}DropdownOpen`];
    this.isEquipmentDropdownOpen = false;
    this.isStatusDropdownOpen = false;
    this.isGateDropdownOpen = false;
    this.isCompanyDropdownOpen = false;
    this.isLocationDropdownOpen = false;
    this.isResponsibleDropdownOpen = false;
    this.isDefineDropdownOpen = false;
    this[`is${type.charAt(0).toUpperCase() + type.slice(1)}DropdownOpen`] = !isCurrentlyOpen;
  }

  public closeFilterPopup(){
    this.modalRef4?.hide()
    this.isEquipmentDropdownOpen = false;
    this.isStatusDropdownOpen = false;
    this.isGateDropdownOpen = false;
    this.isCompanyDropdownOpen = false;
    this.isLocationDropdownOpen = false;
    this.isResponsibleDropdownOpen = false;
    this.isDefineDropdownOpen = false;
  }

  public toggleSelection(item: any, type: string, event: Event): void {
    let selectedList: any[];
    let labelKey: string;
    let defaultPlaceholder: string;

    switch (type) {
      case 'equipment': selectedList = this.selectedEquipment; labelKey = 'equipmentName'; defaultPlaceholder = 'Select Equipment'; break;
      case 'gate': selectedList = this.selectedGate; labelKey = 'gateName'; defaultPlaceholder = 'Select Gate'; break;
      case 'company': selectedList = this.selectedCompany; labelKey = 'companyName'; defaultPlaceholder = 'Select Concrete Supplier'; break;
      case 'location': selectedList = this.selectedLocation; labelKey = 'locationPath'; defaultPlaceholder = 'Select Location'; break;
      case 'status': selectedList = this.selectedStatus; labelKey = 'name'; defaultPlaceholder = 'Select Status'; break;
      case 'responsible': selectedList = this.selectedResponsible; labelKey = 'User.email'; defaultPlaceholder = 'Select Responsible Person'; break;
      case 'define': selectedList = this.selectedDefine; labelKey = 'DFOW'; defaultPlaceholder = 'Select DFOW'; break;
      default: return;
    }

    if (type === 'location') {
      const getChildren = (parentId: number): any[] => {
        return this.locationList.filter(loc => loc.LocationId === parentId)
          .flatMap(child => [child, ...getChildren(child.id)]); // Recursively get all descendants
      };

      if (item.isDefault) {
        if (!selectedList.some(sel => sel.id === item.id)) {
          selectedList = [...this.locationList]; // Select all locations
        } else {
          selectedList = [];
        }
      } else {
        const allChildren = getChildren(item.id);
        const allChildIds = allChildren.map(child => child.id);

        const isAlreadySelected = selectedList.some(sel => sel.id === item.id);

        if (isAlreadySelected) {
          selectedList = selectedList.filter(sel => sel.id !== item.id && !allChildIds.includes(sel.id));
        } else {
          selectedList.push(item);
          allChildren.forEach(child => {
            if (!selectedList.some(sel => sel.id === child.id)) {
              selectedList.push(child);
            }
          });
        }
      }

      this.selectedLocation = [...selectedList];
    } else {
      selectedList = this.toggleGenericSelection(item, type, selectedList);
    }

    this.updatePlaceholder(type, selectedList, labelKey, defaultPlaceholder);
    this.selectOption(item, type, event);
  }

  public toggleGenericSelection(item: any, type: string, selectedList: any[]): any[] {
    const index = selectedList.findIndex((sel) => (type === 'status' ? sel.name === item.name : sel.id === item.id));

    if (index === -1) {
      selectedList.push(item);
    } else {
      selectedList.splice(index, 1);
    }
    return selectedList;
  }

  public updatePlaceholder(type: string, selectedList: any[], labelKey: string, defaultPlaceholder: string): void {
    let newPlaceholder = defaultPlaceholder;
    if (selectedList.length === 1) {
      newPlaceholder = selectedList[0][labelKey];
      if(type === 'responsible'){
        newPlaceholder = selectedList[0].User.email;
      }
      else if(type === 'status'){
        newPlaceholder = selectedList[0].name;
      }
    } else if (selectedList.length > 1) {
      newPlaceholder = `${selectedList[0][labelKey]} + ${selectedList.length - 1} more`;
      if(type === 'responsible'){
        newPlaceholder =  `${selectedList[0].User.email} + ${selectedList.length - 1} more`;
      }else if(type === 'status'){
        newPlaceholder =  `${selectedList[0].name} + ${selectedList.length - 1} more`;
      }
    }
    switch (type) {
      case 'equipment': this.equipmentPlaceholder = newPlaceholder; break;
      case 'gate': this.gatePlaceholder = newPlaceholder; break;
      case 'company': this.companyPlaceholder = newPlaceholder; break;
      case 'location': this.locationPlaceholder = newPlaceholder; break;
      case 'responsible': this.responsiblePlaceholder = newPlaceholder; break;
      case 'status': this.statusPlaceholder = newPlaceholder; break;
      case 'define': this.definePlaceholder = newPlaceholder; break;
    }
  }

  public isSelected(item: any, type: string): boolean {
    let selectedList: any[];
    switch (type) {
      case 'equipment': selectedList = this.selectedEquipment; break;
      case 'gate': selectedList = this.selectedGate; break;
      case 'company': selectedList = this.selectedCompany; break;
      case 'location': selectedList = this.selectedLocation; break;
      case 'responsible': selectedList = this.selectedResponsible; break;
      case 'status': selectedList = this.selectedStatus; break;
      case 'define': selectedList = this.selectedDefine; break;
      default: return false;
    }

    return selectedList.some(sel =>
      type === 'status' ? sel.name === item.name :
      sel.id === item.id
    );
  }

  public selectOption(item: any, type: string, event: Event): void {
    if (event) {
      event.stopPropagation();
    }

    let formKey;

    if (type === 'company') {
      formKey = 'concreteSupplier';
    } else if (type === 'responsible') {
      formKey = 'member';
    } else {
      formKey = type;
    }


    const formControl = this.filterForm.get(`${formKey}Filter`);
    if (!formControl) return;

    let selectedIds: number[] = formControl.value ?? [];

    if (!Array.isArray(selectedIds)) {
      selectedIds = [];
    }

    if (type === 'location') {
      selectedIds = this.handleLocationSelection(item, selectedIds);
    } else {
      selectedIds = this.handleGenericSelection(item, selectedIds, type);
    }

    formControl.setValue([...selectedIds]);
    console.log(this.filterForm.get(`${type}Filter`)?.value);
  }

  public handleLocationSelection(item: any, selectedIds: number[]): number[] {
    const getChildren = (parentId: number): any[] => this.locationList
      .filter((loc) => loc.LocationId === parentId)
      .flatMap((child) => [child, ...getChildren(child.id)]);

    if (item.isDefault) {
      return selectedIds.includes(item.id)
        ? []
        : this.locationList.map((loc) => loc.id);
    }

    const allChildren = getChildren(item.id);
    const allChildIds = allChildren.map((child) => child.id);
    const isAlreadySelected = selectedIds.includes(item.id);

    if (isAlreadySelected) {
      return selectedIds.filter((id) => id !== item.id && !allChildIds.includes(id));
    }

    const updated = new Set(selectedIds);
    updated.add(item.id);
    allChildIds.forEach((id) => updated.add(id));
    return Array.from(updated);
  }

  public handleGenericSelection(item: any, selectedIds: any[], type: string): any[] {
    const isNameType = type === 'status';
    const value = isNameType ? item.name : item.id;
    const index = selectedIds.indexOf(value);

    if (index === -1) {
      selectedIds.push(value);
    } else {
      selectedIds.splice(index, 1);
    }

    return selectedIds;
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
        this.updatedMemberList = this.memberList.map((item) => {
          return {
            ...item,
            UserEmail: item.User.email,
          };
        });
      }
    });
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('descriptionFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('dateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('concreteSupplierFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('memberFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('orderNumberFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('slumpFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('statusFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('truckspacingFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('idFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('quantityFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('locationFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('primerFilter').value !== '') {
      this.filterCount += 1;
    }
    this.pageNo = 1;
    this.getConcreteReport();
    this.closeFilterPopup()
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.pageNo = 1;
    this.gatePlaceholder = 'Select Gate';
    this.companyPlaceholder = 'Select Concrete Supplier';
    this.equipmentPlaceholder = 'Select Equipment';
    this.locationPlaceholder = 'Select Location';
    this.responsiblePlaceholder = 'Select Responsible Person';
    this.statusPlaceholder = 'Select Status';
    this.definePlaceholder = 'Select DFOW';
    this.filterDetailsForm();
    this.getConcreteReport();
    this.selectedEquipment = [];
    this.selectedGate = [];
    this.selectedCompany = [];
    this.selectedLocation = [];
    this.selectedResponsible = [];
    this.selectedDefine = [];
    this.selectedStatus = [];
  }

  public changePageNo(pageNo: number): void {
    this.pageNo = pageNo;
    this.getConcreteReport();
  }

  public getFilterDropdown(): any {
    this.loader = true;
    if (this.ProjectId) {
      const payload = {
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.deliveryService.getConcreteRequestDropdownData(payload).subscribe((response): void => {
        if (response.data) {
          this.locationDropdown = response.data.locationDropdown;
          this.concreteSupplierDropdown = response.data.concreteSupplierDropdown;
          this.mixDesignDropdown = response.data.mixDesignDropdown;
          this.loader = false;
        }
      });
    }
  }

  public handleInputChange(checkedvalue: string): void {
    const checkbox = document.getElementById(checkedvalue) as HTMLInputElement | null;
    if (checkbox.checked === true) {
      this.tableHeaders[checkedvalue].isActive = true;
    } else if (this.activeHeaders.length > 1) {
      this.tableHeaders[checkedvalue].isActive = false;
    }
    this.activeHeaders = Object.values(this.tableHeaders).filter(
      (element: { isActive: boolean }): boolean => element.isActive === true,
    );
  }

  public selectall(checkedvalue): void {
    const checkbox1 = document.getElementById(checkedvalue) as HTMLInputElement | null;
    if (checkbox1.checked === true) {
      this.activeHeaders = Object.values(this.tableHeaders).filter(
        (element: { isActive: boolean }): boolean => element.isActive === true,
      );
      const secondKey = Object.keys(this.tableHeaders);
      secondKey.forEach((key): void => {
        const checkbox = document.getElementById(key) as HTMLInputElement | null;
        checkbox.checked = true;
        this.tableHeaders[key].isActive = true;
      });
    } else {
      const secondKey = Object.keys(this.tableHeaders);
      secondKey.forEach((key): void => {
        const checkbox = document.getElementById(key) as HTMLInputElement | null;
        if (key === 'id' || key === 'description' || key === 'date') {
          this.tableHeaders[key].isActive = true;
        } else {
          checkbox.checked = false;
          this.tableHeaders[key].isActive = false;
        }
      });
      this.activeHeaders = Object.values(this.tableHeaders).filter(
        (element: { isActive: boolean }): boolean => element.isActive === true,
      );
    }
  }

  public exportDetailsForm(): void {
    this.exportForm = this.formBuilder.group({
      reportName: ['', Validators.required],
      reportType: ['', Validators.required],
    });
    this.exportForm.get('reportName').setValue(this.reportName);
    this.exportForm.get('reportType').setValue(this.reportType);
  }

  public onExport(): void {
    this.exportSubmitted = true;
    this.formSubmitted = true;
    if (this.exportForm.invalid) {
      this.exportSubmitted = false;
      return;
    }
    const getConcreteRequestParam = {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.pageNo,
      void: 0,
    };
    let getConcreteRequestPayload: any = {};
    if (this.filterForm !== undefined) {
      const formValue = this.filterForm.value;
      const mapIfExists = (field: any[]) => (field?.length ? field.map((item) => item) : []);
      const fromdate = this.filterForm.value.dateFilter
        ? moment(this.filterForm.value.dateFilter[0]).format('YYYY-MM-DD')
        : this.filterForm.value.dateFilter;
      const todate = this.filterForm.value.dateFilter
        ? moment(this.filterForm.value.dateFilter[1]).format('YYYY-MM-DD')
        : this.filterForm.value.dateFilter;
        getConcreteRequestPayload = {
          descriptionFilter: this.filterForm.value.descriptionFilter,
          orderNumberFilter: this.filterForm.value.orderNumberFilter,
          concreteSupplierFilter: mapIfExists(formValue.concreteSupplierFilter),
          startdate: fromdate,
          enddate: todate,
          truckspacingFilter: this.filterForm.value.truckspacingFilter,
          slumpFilter: this.filterForm.value.slumpFilter,
          primerFilter: this.filterForm.value.primerFilter,
          quantityFilter: this.filterForm.value.quantityFilter,
          statusFilter: mapIfExists(formValue.statusFilter),
          memberFilter: mapIfExists(formValue.memberFilter),
          locationFilter: mapIfExists(formValue.locationFilter),
          idFilter: formValue.idFilter ?? 0,
        };
    }
    if (this.sort === 'DESC' && this.sortColumn === ' id') {
      this.sort = 'ASC';
      this.sortColumn = 'concretePlacementStart';
    }
    getConcreteRequestPayload.sort = this.sort;
    getConcreteRequestPayload.sortByField = this.sortColumn;
    getConcreteRequestPayload.ParentCompanyId = this.ParentCompanyId;
    getConcreteRequestPayload.exportType = this.exportForm.get('reportType').value;
    getConcreteRequestPayload.reportName = this.exportForm.get('reportName').value;
    getConcreteRequestPayload.generatedDate = moment(new Date()).format('ddd, MMM DD YYYY');
    getConcreteRequestPayload.selectedHeaders = Object.values(this.tableHeaders);
    this.filterPayload = getConcreteRequestPayload;
    if (this.reportType === 'PDF' || this.reportType === 'CSV') {
      this.reportsService
        .exportConcreteRequest(getConcreteRequestParam, getConcreteRequestPayload)
        .subscribe((response: any): void => {
          if (response) {
            const link = document.createElement('a');
            link.setAttribute('target', '_self');
            link.setAttribute('href', response.data);
            document.body.appendChild(link);
            link.click();
            link.remove();
            this.toastr.success('Concrete Booking exported successfully');
            this.cancelexport();
          }
        });
    } else if (this.reportType === 'EXCEL') {
      this.reportsService
        .exportConcreteRequestInExcelFormat(getConcreteRequestParam, getConcreteRequestPayload)
        .subscribe((response: any): void => {
          if (response) {
            this.deliveryService.saveAsExcelFile(response, this.reportName);
            this.toastr.success('Concrete Booking exported successfully');
            this.cancelexport();
          }
        });
    }
  }

  public cancelexport(): void {
    this.modalRef.hide();
    this.exportSubmitted = false;
    this.exportForm.get('reportName').setValue('Concrete Report');
    this.exportForm.get('reportType').setValue('PDF');
  }

  public getLocationsForNdrGrid(): void {
    const getLocationsForNdrGridParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getLocations(getLocationsForNdrGridParams)
      .subscribe((getLocationsForNdrGridResponse: any): void => {
        if (getLocationsForNdrGridResponse) {
          this.locationList = getLocationsForNdrGridResponse.data;
          this.filteredLocationList = this.locationList
        }
      });
  }

  public changeLocationFilterOptionList(event){
    if(event.target.value === '') {
      this.filteredLocationList = this.locationList
    }
    this.filteredLocationList = this.filteredLocationList.filter(option =>
      option.locationPath.toLowerCase().includes(event.target.value.toLowerCase())
    );
  }
}
