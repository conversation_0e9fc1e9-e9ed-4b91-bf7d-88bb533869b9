import { Component, OnInit } from '@angular/core';
import { ProjectService } from 'src/app/services/profile/project.service';

@Component({
  selector: 'app-carbon-tracking',
  templateUrl: './carbon-tracking.component.html',
  styleUrls: ['./carbon-tracking.component.css']
})
export class CarbonTrackingComponent implements OnInit {
  view: [number, number] = [700, 400];

  activeEntries: any[] = [];
  chartData: any[] = [];
  groupedChartData: any[] = [];
  pieChartData: any[] = [];

  colorScheme = { domain: ['#5AA454', '#A10A28', '#C7B42C', '#7B68EE', '#FF5733'] };

  ProjectId: any;
  loader: boolean = false;
  totalCO2Emissions: number = 0;

  constructor(private readonly projectService: ProjectService) {
    this.projectService.projectId.subscribe((res): void => {
      this.ProjectId = res;
      if (res) {
        this.loader = true;
        this.getDashboardData(this.ProjectId);
      }
    });
  }

  ngOnInit(): void { /* */ }

  onActivate(event: any): void {
    this.activeEntries = [...this.chartData]; // Keep all bars visible
  }

  onDeactivate(event: any): void {
    this.activeEntries = [...this.chartData]; // Reset bars on hover out
  }

  formatXAxisTick(value: string): string {
    return value.length > 10 ? value.slice(0, 10) + '...' : value;
  }

  getDashboardData(ProjectId: any) {
    this.projectService.getDashboardData(ProjectId).subscribe((response: any): void => {
      if (!response.data) return;

      this.chartData = response.data.map(item => ({
        name: item.utilityType,
        value: parseFloat(item.totalCost || '0')
      }));

      // Ensure bars remain visible by setting activeEntries
      this.activeEntries = [...this.chartData];

      // Calculate total CO₂ emissions
      this.totalCO2Emissions = response.data.reduce((sum, item) => {
        return sum + (item.totalEmissions ? parseFloat(item.totalEmissions) : 0);
      }, 0);

      // Grouped data for cards
      this.groupedChartData = response.data.map(item => ({
        name: item.utilityType,
        co2Emission: item.totalEmissions ? parseFloat(item.totalEmissions) : 0
      }));

      // Pie Chart Data (CO₂ Emissions Breakdown)
      this.pieChartData = response.data
      .filter(item => item.totalEmissions) // Remove null values
      .map(item => ({
        name: item.utilityType,
        value: parseFloat(item.totalEmissions)
      }));

      this.loader = false; // Stop loading after fetching data
    });
  }

}
