import {
  Component, OnInit, TemplateRef, EventEmitter, Output, Input,
} from '@angular/core';
import { BsModalService, BsModalRef, ModalOptions } from 'ngx-bootstrap/modal';
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import moment from 'moment';
import { DeliveryService } from '../../../../services/profile/delivery.service';
import { ProjectService } from '../../../../services/profile/project.service';
import { EditDeliveryBookingComponent } from '../edit-delivery-booking/edit-delivery-booking.component';
import { ProjectSharingService } from '../../../../services/projectSharingService/project-sharing.service';
import { MixpanelService } from '../../../../services/mixpanel.service';

@Component({
  selector: 'app-guest-delivery-details',
  templateUrl: './guest-delivery-details.component.html',
  })
export class GuestDeliveryDetailsComponent implements OnInit {
  @Input() data: any;

  @Input() title: string;

  @Output() closeModalEvent = new EventEmitter<boolean>();

  public DeliveryRequestId: any;

  public statusChanged: boolean = false;

  public currentStatus = '';

  public modalRef1: BsModalRef;

  public currentTabId: number = 0;

  public loader = true;

  public NDRData: any = [];

  public toolTipContent = '';

  public ParentCompanyId: any;

  public ProjectId: any;

  public guestUserId: any;

  public files: NgxFileDropEntry[] = [];

  public fileData: any = [];

  public deleteUploadedFile = false;

  public uploadSubmitted = false;

  public formData: FormData;

  public fileArray: any = [];

  public commentList: any = [];

  public commentDetailsForm: UntypedFormGroup;

  public submitted = false;

  public formSubmitted = false;

  public historyList: any = [];

  public currentDeliverySaveItem: any = {};

  public modalRef: BsModalRef;

  public seriesOptions = [];

  public allRequestIsOpened = false;



  public constructor(
    private readonly modalService: BsModalService,
    private readonly toastr: ToastrService,
    public bsModalRef: BsModalRef,
    public deliveryService: DeliveryService,
    private readonly mixpanelService: MixpanelService,
    private readonly formBuilder: UntypedFormBuilder,
    public projectService: ProjectService,
    public projectSharingService: ProjectSharingService,
    private readonly option: ModalOptions,
  ) {
    this.ProjectId = +window.atob(localStorage.getItem('guestProjectId'));
    this.ParentCompanyId = +window.atob(localStorage.getItem('guestParentCompanyId'));
    this.guestUserId = +window.atob(localStorage.getItem('guestId'));
    this.formData = new FormData();

    this.commentForm();
  }

  public ngOnInit(): void {
    const stateValue = this.data;
    this.DeliveryRequestId = stateValue.id;
    this.getNDR();
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'file':
          this.removeFile(data, item);
          break;
        case 'auto':
          this.openEditModal(data, item);
          break;
        default:
          break;
      }
    }
  }

  public confirmationClose(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide(); // Hides the confirmation modal if the action is 'no'
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide(); // Hides the confirmation modal if it exists
      }
      this.bsModalRef.hide(); // Hides the main modal
      this.statusChanged = false; // Resets a status flag
      // Emit the event to inform the parent component to close the modal
      this.closeModalEvent.emit(true);
    }
  }


  public getNDR(): void {
    this.toolTipContent = '';
    const param = {
      DeliveryRequestId: this.DeliveryRequestId,
      ParentCompanyId: this.ParentCompanyId,
    };



    this.projectSharingService.guestGetNDRData(param).subscribe((res): void => {
      this.NDRData = res.data;
      this.fileArray = res.data.attachements;
      this.commentList = res.data.comments;
      this.historyList = res.data.history;
      this.historyList = this.historyList.filter((data: { type: string }) => data.type !== 'comment');
      this.currentDeliverySaveItem = res.data;
      this.currentDeliverySaveItem.edit = true;
      this.loader = false;
      if (this.NDRData.memberDetails.length > 3) {
        const slicedArray = this.NDRData?.memberDetails?.slice(3) || [];
        slicedArray.map((a): any => {
          if (a.Member.User.firstName) {
            this.toolTipContent += `${a.Member.User.firstName} ${a.Member.User.lastName}, `;
          } else {
            this.toolTipContent += `${a.Member.User.email}, `;
          }
        });
      }
    });
  }

  public getResponsiblePeople(object: { firstName: any; lastName: any }): any {
    if (object?.firstName && object?.lastName) {
      const string = `${object.firstName} ${object.lastName}`;
      const matches = string.match(/\b(\w)/g);
      const acronym = matches.join('').toUpperCase();
      return acronym;
    }
    return 'UU';
  }

  public dropped(files: NgxFileDropEntry[]): void {
    this.files = files;
    this.fileData.push(this.files);

    this.fileData.forEach((entryGroup: any[], groupIndex: number) => {
      entryGroup.forEach((entry, entryIndex) => {
        this.processFileEntry(entry, groupIndex, entryIndex);
      });
    });
  }

  public processFileEntry(
    entry: { relativePath: string; fileEntry: FileSystemFileEntry },
    groupIndex: number,
    entryIndex: number,
  ): void {
    const extension = this.getFileExtension(entry.relativePath);

    if (this.isSupportedExtension(extension)) {
      if (entry.fileEntry.isFile) {
        this.readFile(entry.fileEntry, extension, groupIndex, entryIndex);
      }
    } else {
      this.fileData.splice(groupIndex);
      this.toastr.error(
        'Please select a valid file. Supported file format (.jpg,.jpeg,.png,.pdf,.doc)',
        'OOPS!',
      );
    }
  }

  public getFileExtension(path: string): string {
    const parts = path.split('.');
    return parts[parts.length - 1].toLowerCase();
  }

  public isSupportedExtension(extension: string): boolean {
    return ['jpg', 'jpeg', 'png', 'pdf', 'doc'].includes(extension);
  }

  public readFile(
    fileEntry: FileSystemFileEntry,
    extension: string,
    groupIndex: number,
    entryIndex: number,
  ): void {
    fileEntry.file((file: File) => {
      const filesize = (file.size / 1_000_000).toFixed(4);

      if (+filesize > 2) {
        this.toastr.error('Please choose an attachment less than or equal to 2MB');
        this.fileData.splice(groupIndex);
        return;
      }

      if (this.fileData[groupIndex]) {
        const reader = new FileReader();
        this.fileData[groupIndex][entryIndex].extension = extension;

        reader.onload = () => {
          this.fileData[groupIndex][entryIndex].image = reader.result;
        };

        reader.readAsDataURL(file);
      }
    });
  }


  public fileOver(_event: any): void { /* */ }

  public fileLeave(_event: any): void { /* */ }


  public uploadData(): void {
    this.uploadSubmitted = true;
    this.formData = new FormData();
    this.fileData.forEach((element: any[], i: any): void => {
      element.forEach(
        (data: { fileEntry: FileSystemFileEntry; relativePath: any }, index: any): void => {
          const { fileEntry } = data;
          fileEntry.file((_file: File): void => {
            this.formData.append('attachement', _file, data.relativePath);
          });
        },
      );
    });
    const params = {
      DeliveryRequestId: this.DeliveryRequestId,
      ParentCompanyId: this.ParentCompanyId,
      userId: this.guestUserId,
    };
    this.projectSharingService.guestAddAttachement(params, this.formData).subscribe({
      next: (res): void => {
        this.fileData = [];
        this.uploadSubmitted = false;
        this.toastr.success(res.message, 'SUCCESS!');
        this.mixpanelService.addGuestUserMixpanelEvents('Attachment deleted against a Delivery Booking');
        this.getNDR();
      },
      error: (attachmentErr): void => {
        this.uploadSubmitted = false;
        if (attachmentErr.message?.statusCode === 400) {
          this.showError(attachmentErr);
        } else {
          this.toastr.error(attachmentErr.message, 'OOPS!');
        }
      },
    });
  }

  public removeFile(firstIndex: string | number, i: string | number): void {
    this.formData.delete(this.fileData[firstIndex][i].relativePath);
    this.fileData[firstIndex].splice(i);
    this.fileData.splice(firstIndex);
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }

  public commentForm(): void {
    this.commentDetailsForm = this.formBuilder.group(
      {
        comment: [
          '',
          Validators.compose([
            Validators.required,
          ]),
        ],
      },
    );
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    if (this.commentDetailsForm.invalid) {
      this.formSubmitted = false;

      return;
    }
    const formValue = this.commentDetailsForm.value;
    if (!this.checkStringEmptyValues(formValue)) {
      const payload = {
        comment: formValue.comment.trim(),
        DeliveryRequestId: this.DeliveryRequestId,
        ParentCompanyId: this.ParentCompanyId,
        userId: this.guestUserId,
      };
      this.projectSharingService.guestCreateComment(payload).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addGuestUserMixpanelEvents('Comment added against a Delivery Booking');
            this.submitted = false;
            this.formSubmitted = false;
            this.commentDetailsForm.reset();
            this.getNDR();
          }
        },
        error: (commentError): void => {
          this.commentDetailsForm.reset();
          if (commentError.message?.statusCode === 400) {
            this.showCommentError(commentError);
          } else {
            this.toastr.error(commentError.message, 'OOPS!');
            this.submitted = false;
          }
        },
      });
    } else {
      this.toastr.error('Please enter a valid comment.', 'OOPS!');
      this.submitted = false;
      this.formSubmitted = false;
    }
  }

  public checkStringEmptyValues(formValue: { comment: string }): boolean {
    if (formValue.comment.trim() === '') {
      return true;
    }
    return false;
  }

  public showCommentError(commentError: { message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] } }): void {
    let commentErrorMessage: any = '';
    commentErrorMessage = Object.values(commentError.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(commentErrorMessage);
  }

  public openEditModal(item, action): void {
    if (this.bsModalRef) {
      this.bsModalRef.hide();
    }
    const data = item;
    data.seriesOption = item?.recurrence && item?.recurrence?.recurrence !== 'Does Not Repeat' ? action : 1;
    data.DeliveryRequestId = this.DeliveryRequestId;
    data.recurrenceId = item?.recurrence ? item?.recurrence?.id : null;
    data.recurrenceEndDate = item?.recurrence
      ? item?.recurrence?.recurrenceEndDate
      : null;
    const initialState = {
      data,
      title: 'Modal with component',
    };
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(EditDeliveryBookingComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
      initialState,
    });
    this.modalRef.content.closeBtnName = 'Close';
    this.modalRef.content.seriesOption = item?.recurrence && item?.recurrence?.recurrence !== 'Does Not Repeat' ? action : 1;
    this.modalRef.content.recurrenceId = item?.recurrence ? item?.recurrence?.id : null;
    this.modalRef.content.recurrenceEndDate = item?.recurrence
      ? item?.recurrence?.recurrenceEndDate
      : null;
  }

  public changeRequestCollapse(data): void {
    this.initializeSeriesOption();
    if (!moment(data.deliveryStart).isAfter(moment())) {
      this.seriesOptions = this.seriesOptions.filter((object): any => {
        const seriesObject = object;
        if (seriesObject.option !== 1) {
          seriesObject.disabled = true;
        }
        return seriesObject;
      });
    }
    this.allRequestIsOpened = !this.allRequestIsOpened;
  }

  public initializeSeriesOption(): void {
    this.seriesOptions = [
      {
        option: 1,
        text: 'This event',
        disabled: false,
      },
      {
        option: 2,
        text: 'This and all following events',
        disabled: false,
      },

    ];
  }
}
