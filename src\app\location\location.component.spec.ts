import { ComponentFixture, TestBed } from '@angular/core/testing';
import { LocationComponent } from './location.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Title } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { LocationSettingsService } from '../services/location-settings/location-settings.service';
import { MixpanelService } from '../services/mixpanel.service';
import { of, throwError } from 'rxjs';
import { NgxPaginationModule } from 'ngx-pagination';
import { NO_ERRORS_SCHEMA, Pipe, PipeTransform } from '@angular/core';

// Create a mock for the paginate pipe
@Pipe({
  name: 'paginate'
})
class MockPaginatePipe implements PipeTransform {
  transform(value: any[], config: any): any[] {
    return value;
  }
}

describe('LocationComponent', () => {
  let component: LocationComponent;
  let fixture: ComponentFixture<LocationComponent>;
  let modalService: jest.Mocked<BsModalService>;
  let locationService: jest.Mocked<LocationSettingsService>;
  let toastrService: jest.Mocked<ToastrService>;
  let mixpanelService: jest.Mocked<MixpanelService>;
  let titleService: jest.Mocked<Title>;

  const mockLocationList = {
    data: {
      rows: [
        {
          id: 1,
          name: 'Location 1',
          LocationNotificationPreferences: [{ id: 1, MemberId: 1, ProjectId: 1, follow: false }],
          paths: []
        }
      ],
      count: 1,
      defaultLocation: {
        LocationNotificationPreferences: [{ id: 2, MemberId: 2, ProjectId: 1, follow: false }]
      }
    }
  };

  beforeEach(async () => {
    // Clear all mocks before each test
    jest.clearAllMocks();

    modalService = {
      show: jest.fn().mockReturnValue({
        hide: jest.fn(),
        setClass: jest.fn()
      })
    } as any;

    locationService = {
      getLocationList: jest.fn().mockReturnValue(of(mockLocationList)),
      updateMemberLocationPreference: jest.fn()
    } as any;

    toastrService = {
      success: jest.fn(),
      error: jest.fn()
    } as any;

    mixpanelService = {
      addMixpanelEvents: jest.fn()
    } as any;

    titleService = {
      setTitle: jest.fn()
    } as any;

    // Mock localStorage before TestBed configuration
    Storage.prototype.getItem = jest.fn((key) => {
      if (key === 'ProjectId') return '123';
      if (key === 'currentCompanyId') return '456';
      return null;
    });

    await TestBed.configureTestingModule({
      declarations: [
        LocationComponent,
        MockPaginatePipe // Add the mock pipe
      ],
      imports: [
        // NgxPaginationModule // We're using a mock pipe instead
      ],
      providers: [
        { provide: BsModalService, useValue: modalService },
        { provide: LocationSettingsService, useValue: locationService },
        { provide: ToastrService, useValue: toastrService },
        { provide: MixpanelService, useValue: mixpanelService },
        { provide: Title, useValue: titleService }
      ],
      schemas: [NO_ERRORS_SCHEMA] // Add this to ignore unknown elements and attributes
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(LocationComponent);
    component = fixture.componentInstance;

    // Don't call detectChanges yet to prevent ngOnInit from running
    // This allows us to spy on methods before they're called
  });

  it('should create', () => {
    fixture.detectChanges();
    expect(component).toBeTruthy();
  });

  it('should set title on initialization', () => {
    fixture.detectChanges();
    expect(titleService.setTitle).toHaveBeenCalledWith('Follo - Location');
  });

  it('should call getLocationList when initialized with ProjectId and ParentCompanyId', () => {
    // Mock localStorage to ensure ProjectId and ParentCompanyId are available
    jest.spyOn(localStorage, 'getItem').mockImplementation((key) => {
      if (key === 'ProjectId') return '123';
      if (key === 'currentCompanyId') return '456';
      return null;
    });

    // Create a new instance of the component to trigger the constructor
    const newComponent = new LocationComponent(
      modalService as any,
      locationService as any,
      toastrService as any,
      mixpanelService as any,
      titleService as any
    );

    // Verify the service method was called
    expect(locationService.getLocationList).toHaveBeenCalled();
  });

  it('should handle search location', () => {
    fixture.detectChanges();

    // Spy on the getLocationList method
    const spy = jest.spyOn(component, 'getLocationList');

    // Clear previous calls
    spy.mockClear();

    component.searchLocation('test');

    expect(component.showSearchbar).toBeTruthy();
    expect(component.search).toBe('test');
    expect(component.currentPageNo).toBe(1);
    expect(spy).toHaveBeenCalled();
  });

  it('should handle clear search', () => {
    fixture.detectChanges();

    // Setup
    component.showSearchbar = true;
    component.search = 'test';

    // Spy on the getLocationList method
    const spy = jest.spyOn(component, 'getLocationList');

    // Clear previous calls
    spy.mockClear();

    component.clear();

    expect(component.showSearchbar).toBeFalsy();
    expect(component.search).toBe('');
    expect(component.currentPageNo).toBe(1);
    expect(spy).toHaveBeenCalled();
  });

  it('should handle page number change', () => {
    fixture.detectChanges();

    // Spy on the getLocationList method
    const spy = jest.spyOn(component, 'getLocationList');

    // Clear previous calls
    spy.mockClear();

    component.changePageNo(2);

    expect(component.currentPageNo).toBe(2);
    expect(spy).toHaveBeenCalled();
  });

  it('should handle sort by field', () => {
    fixture.detectChanges();

    // Spy on the getLocationList method
    const spy = jest.spyOn(component, 'getLocationList');

    // Clear previous calls
    spy.mockClear();

    component.sortByField('name', 'ASC');

    expect(component.sortColumn).toBe('name');
    expect(component.sort).toBe('ASC');
    expect(spy).toHaveBeenCalled();
  });

  it('should handle successful location notification update', () => {
    fixture.detectChanges();

    const mockResponse = { message: 'Success' };
    locationService.updateMemberLocationPreference.mockReturnValue(of(mockResponse));

    // Initialize the array with a mock notification
    component.chosenLocationNotification = [{ id: 1, follow: true }];
    component.submitLocationNotification();

    expect(locationService.updateMemberLocationPreference).toHaveBeenCalled();
    expect(toastrService.success).toHaveBeenCalledWith('Success', 'Success');
    expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Updated Member Location Preference');
    expect(component.chosenLocationNotification).toEqual([]);
    expect(component.submitted).toBeFalsy();
  });

  it('should handle error in location notification update', () => {
    fixture.detectChanges();

    const mockError = { message: { statusCode: 400, details: [{ error: 'Bad Request' }] } };
    locationService.updateMemberLocationPreference.mockReturnValue(throwError(() => mockError));

    // Initialize the array with a mock notification
    component.chosenLocationNotification = [{ id: 1, follow: true }];
    component.submitLocationNotification();

    expect(toastrService.error).toHaveBeenCalled();
    expect(component.submitted).toBeFalsy();
    expect(component.chosenLocationNotification).toEqual([]);
  });

  it('should open modal with correct configuration', () => {
    fixture.detectChanges();

    const template = {} as any;
    component.openModal1(template);
    expect(modalService.show).toHaveBeenCalledWith(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-sm filter-popup custom-modal'
    });
  });

  it('should handle check instant value for default type', () => {
    fixture.detectChanges();

    // Set up the component with the necessary data
    const mockEvent = { target: { checked: true } };
    component.defaultLocationPath = mockLocationList.data.defaultLocation;
    component.locationList = mockLocationList.data.rows;

    // Initialize the array
    component.chosenLocationNotification = [];

    // Call the method
    component.checkInstantValue(null, mockEvent, 'default');

    // Check that at least one notification was added
    expect(component.chosenLocationNotification.length).toBeGreaterThan(0);

    // Verify the notification has the correct properties
    expect(component.chosenLocationNotification[0]).toEqual({
      id: component.defaultLocationPath.LocationNotificationPreferences[0].id,
      MemberId: component.defaultLocationPath.LocationNotificationPreferences[0].MemberId,
      ProjectId: component.defaultLocationPath.LocationNotificationPreferences[0].ProjectId,
      follow: true
    });
  });


  it('should handle check instant value for mainCategory type', () => {
    fixture.detectChanges();

    // Set up the component with the necessary data
    const mockEvent = { target: { checked: true } };
    const mockLocation = {
      LocationNotificationPreferences: [{
        id: 1,
        MemberId: 1,
        ProjectId: 1,
        follow: false
      }],
      paths: []
    };

    // Initialize the array
    component.chosenLocationNotification = [];

    // Spy on the handleMainCategoryFlow method
    const handleMainCategorySpy = jest.spyOn(component, 'handleMainCategoryFlow');

    // Call the method with mainCategory type
    component.checkInstantValue(mockLocation, mockEvent, 'mainCategory');

    // Check that handleMainCategoryFlow was called
    expect(handleMainCategorySpy).toHaveBeenCalledWith(mockLocation, mockEvent);

    // Check that at least one notification was added
    // This depends on the implementation of handleMainCategoryFlow
    expect(component.chosenLocationNotification.length).toBeGreaterThan(0);
  });

  it('should open modal popup with correct configuration', () => {
    fixture.detectChanges();

    const template = {} as any;
    component.openModalPopup(template);
    expect(modalService.show).toHaveBeenCalledWith(template, {
      keyboard: false,
      class: 'modal-md location-sent-popup modal-dialog-centered custom-modal'
    });
  });

  it('should handle page size change', () => {
    fixture.detectChanges();

    // Spy on the getLocationList method
    const spy = jest.spyOn(component, 'getLocationList');

    // Clear previous calls
    spy.mockClear();

    component.changePageSize(50);

    expect(component.pageSize).toBe(50);
    expect(component.currentPageNo).toBe(1);
    expect(spy).toHaveBeenCalled();
  });

  it('should handle check instant value for tier type', () => {
    fixture.detectChanges();

    // Set up the component with the necessary data
    const mockEvent = { target: { checked: true } };
    const mockTier = {
      LocationNotificationPreferences: [{
        id: 1,
        MemberId: 1,
        ProjectId: 1,
        follow: false
      }]
    };

    // Initialize the array
    component.chosenLocationNotification = [];

    // Call the method with tier type
    component.checkInstantValue(mockTier, mockEvent, 'tier');

    // Check that at least one notification was added
    expect(component.chosenLocationNotification.length).toBeGreaterThan(0);

    // Verify the notification has the correct properties
    expect(component.chosenLocationNotification[0]).toEqual({
      id: mockTier.LocationNotificationPreferences[0].id,
      MemberId: mockTier.LocationNotificationPreferences[0].MemberId,
      ProjectId: mockTier.LocationNotificationPreferences[0].ProjectId,
      follow: true
    });
  });

  // Additional positive test cases
  it('should call ngOnInit without errors', () => {
    fixture.detectChanges();
    expect(() => component.ngOnInit()).not.toThrow();
  });

  it('should handle getLocationList with all required parameters', () => {
    fixture.detectChanges();

    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.pageSize = 25;
    component.currentPageNo = 1;
    component.search = 'test';
    component.sort = 'ASC';
    component.sortColumn = 'name';

    const expectedParams = {
      ProjectId: '123',
      pageSize: 25,
      pageNo: 1,
      ParentCompanyId: '456',
      search: 'test',
      sort: 'ASC',
      sortByField: 'name'
    };

    component.getLocationList();

    expect(locationService.getLocationList).toHaveBeenCalledWith(expectedParams);
    expect(component.loader).toBeTruthy();
  });

  it('should handle successful getLocationList response', () => {
    fixture.detectChanges();

    const mockResponse = {
      data: {
        rows: [{ id: 1, name: 'Test Location' }],
        count: 1,
        defaultLocation: { id: 2, name: 'Default Location' }
      }
    };

    locationService.getLocationList.mockReturnValue(of(mockResponse));

    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.pageSize = 25;
    component.currentPageNo = 1;

    component.getLocationList();

    expect(component.locationList).toEqual(mockResponse.data.rows);
    expect(component.defaultLocationPath).toEqual(mockResponse.data.defaultLocation);
    expect(component.totalCount).toBe(1);
    expect(component.loader).toBeFalsy();
  });

  it('should handle getLocationList response without defaultLocation', () => {
    fixture.detectChanges();

    const mockResponse = {
      data: {
        rows: [{ id: 1, name: 'Test Location' }],
        count: 1
      }
    };

    locationService.getLocationList.mockReturnValue(of(mockResponse));

    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.pageSize = 25;
    component.currentPageNo = 1;

    component.getLocationList();

    expect(component.defaultLocationPath).toEqual({});
  });

  it('should handle searchLocation with empty string', () => {
    fixture.detectChanges();

    const spy = jest.spyOn(component, 'getLocationList');
    spy.mockClear();

    component.searchLocation('');

    expect(component.showSearchbar).toBeFalsy();
    expect(component.search).toBe('');
    expect(component.currentPageNo).toBe(1);
    expect(spy).toHaveBeenCalled();
  });

  it('should handle searchLocation with non-empty string', () => {
    fixture.detectChanges();

    const spy = jest.spyOn(component, 'getLocationList');
    spy.mockClear();

    component.searchLocation('test search');

    expect(component.showSearchbar).toBeTruthy();
    expect(component.search).toBe('test search');
    expect(component.currentPageNo).toBe(1);
    expect(spy).toHaveBeenCalled();
  });

  it('should handle changePageSize and reset currentPageNo', () => {
    fixture.detectChanges();

    const spy = jest.spyOn(component, 'getLocationList');
    spy.mockClear();

    component.currentPageNo = 5; // Set to non-default value
    component.changePageSize(50);

    expect(component.pageSize).toBe(50);
    expect(component.currentPageNo).toBe(1); // Should reset to 1
    expect(spy).toHaveBeenCalled();
  });

  it('should handle handleTierFlow with checked event', () => {
    fixture.detectChanges();

    const mockEvent = { target: { checked: true } };
    const mockData = {
      LocationNotificationPreferences: [{
        id: 1,
        MemberId: 1,
        ProjectId: 1,
        follow: false
      }]
    };

    component.chosenLocationNotification = [];
    const result = component.handleTierFlow(mockData, mockEvent);

    expect(component.chosenLocationNotification).toHaveLength(1);
    expect(component.chosenLocationNotification[0].follow).toBeTruthy();
    expect(result).toBe(mockData);
  });

  it('should handle handleTierFlow with unchecked event', () => {
    fixture.detectChanges();

    const mockEvent = { target: { checked: false } };
    const mockData = {
      LocationNotificationPreferences: [{
        id: 1,
        MemberId: 1,
        ProjectId: 1,
        follow: true
      }]
    };

    component.chosenLocationNotification = [];
    const result = component.handleTierFlow(mockData, mockEvent);

    expect(component.chosenLocationNotification).toHaveLength(1);
    expect(component.chosenLocationNotification[0].follow).toBeFalsy();
    expect(result).toBe(mockData);
  });

  it('should handle handleSubCategoryFlow with data and tiers', () => {
    fixture.detectChanges();

    const mockEvent = { target: { checked: true } };
    const mockData = {
      LocationNotificationPreferences: [{
        id: 1,
        MemberId: 1,
        ProjectId: 1,
        follow: false
      }],
      tier: [{
        LocationNotificationPreferences: [{
          id: 2,
          MemberId: 1,
          ProjectId: 1,
          follow: false
        }]
      }]
    };

    component.chosenLocationNotification = [];
    const result = component.handleSubCategoryFlow(mockData, mockEvent);

    expect(component.chosenLocationNotification).toHaveLength(2); // Main + tier
    expect(result).toBe(mockData);
  });

  it('should handle handleSubCategoryFlow without tiers', () => {
    fixture.detectChanges();

    const mockEvent = { target: { checked: true } };
    const mockData = {
      LocationNotificationPreferences: [{
        id: 1,
        MemberId: 1,
        ProjectId: 1,
        follow: false
      }],
      tier: []
    };

    component.chosenLocationNotification = [];
    const result = component.handleSubCategoryFlow(mockData, mockEvent);

    expect(component.chosenLocationNotification).toHaveLength(1); // Only main
    expect(result).toBe(mockData);
  });

  it('should handle handleToggleKeydown with Enter key', () => {
    fixture.detectChanges();

    const mockEvent = {
      key: 'Enter',
      preventDefault: jest.fn()
    } as any;

    const spy = jest.spyOn(component, 'sortByField');

    component.handleToggleKeydown(mockEvent, 'name', 'ASC');

    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(spy).toHaveBeenCalledWith('name', 'ASC');
  });

  it('should handle handleToggleKeydown with Space key', () => {
    fixture.detectChanges();

    const mockEvent = {
      key: ' ',
      preventDefault: jest.fn()
    } as any;

    const spy = jest.spyOn(component, 'sortByField');

    component.handleToggleKeydown(mockEvent, 'id', 'DESC');

    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(spy).toHaveBeenCalledWith('id', 'DESC');
  });

  it('should not handle handleToggleKeydown with other keys', () => {
    fixture.detectChanges();

    const mockEvent = {
      key: 'Tab',
      preventDefault: jest.fn()
    } as any;

    const spy = jest.spyOn(component, 'sortByField');

    component.handleToggleKeydown(mockEvent, 'name', 'ASC');

    expect(mockEvent.preventDefault).not.toHaveBeenCalled();
    expect(spy).not.toHaveBeenCalled();
  });

  it('should handle showError method', () => {
    fixture.detectChanges();

    const mockError = {
      message: {
        details: [{ error: 'Test error message' }]
      }
    };

    component.chosenLocationNotification = [{ id: 1, follow: true }];
    component.submitted = true;

    component.showError(mockError);

    expect(component.submitted).toBeFalsy();
    expect(component.chosenLocationNotification).toEqual([]);
    expect(toastrService.error).toHaveBeenCalledWith('Test error message');
  });

  // Negative test cases
  it('should not call getLocationList when ProjectId is missing in constructor', () => {
    // Mock localStorage to return null for ProjectId
    Storage.prototype.getItem = jest.fn((key) => {
      if (key === 'ProjectId') return null;
      if (key === 'currentCompanyId') return '456';
      return null;
    });

    const spy = jest.spyOn(locationService, 'getLocationList');
    spy.mockClear();

    // Create a new component instance
    const newComponent = new LocationComponent(
      modalService as any,
      locationService as any,
      toastrService as any,
      mixpanelService as any,
      titleService as any
    );

    expect(spy).not.toHaveBeenCalled();
  });

  it('should not call getLocationList when ParentCompanyId is missing in constructor', () => {
    // Mock localStorage to return null for currentCompanyId
    Storage.prototype.getItem = jest.fn((key) => {
      if (key === 'ProjectId') return '123';
      if (key === 'currentCompanyId') return null;
      return null;
    });

    const spy = jest.spyOn(locationService, 'getLocationList');
    spy.mockClear();

    // Create a new component instance
    const newComponent = new LocationComponent(
      modalService as any,
      locationService as any,
      toastrService as any,
      mixpanelService as any,
      titleService as any
    );

    expect(spy).not.toHaveBeenCalled();
  });

  it('should not call getLocationList when required parameters are missing', () => {
    fixture.detectChanges();

    const spy = jest.spyOn(locationService, 'getLocationList');
    spy.mockClear();

    // Set some parameters to null/undefined
    component.ProjectId = null;
    component.ParentCompanyId = '456';
    component.pageSize = 25;
    component.currentPageNo = 1;

    component.getLocationList();

    expect(spy).not.toHaveBeenCalled();
    expect(component.loader).toBeTruthy(); // Loader should still be set to true
  });

  it('should handle error in submitLocationNotification with statusCode 400', () => {
    fixture.detectChanges();

    const mockError = {
      message: {
        statusCode: 400,
        details: [{ error: 'Validation error' }]
      }
    };

    locationService.updateMemberLocationPreference.mockReturnValue(throwError(() => mockError));

    const showErrorSpy = jest.spyOn(component, 'showError');
    component.chosenLocationNotification = [{ id: 1, follow: true }];

    component.submitLocationNotification();

    expect(showErrorSpy).toHaveBeenCalledWith(mockError);
    expect(component.submitted).toBeFalsy();
  });

  it('should handle error in submitLocationNotification without message', () => {
    fixture.detectChanges();

    const mockError = {};

    locationService.updateMemberLocationPreference.mockReturnValue(throwError(() => mockError));

    component.chosenLocationNotification = [{ id: 1, follow: true }];

    component.submitLocationNotification();

    expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    expect(component.submitted).toBeFalsy();
  });

  it('should handle error in submitLocationNotification with generic message', () => {
    fixture.detectChanges();

    const mockError = {
      message: 'Generic error message'
    };

    locationService.updateMemberLocationPreference.mockReturnValue(throwError(() => mockError));

    component.chosenLocationNotification = [{ id: 1, follow: true }];

    component.submitLocationNotification();

    expect(toastrService.error).toHaveBeenCalledWith('Generic error message', 'OOPS!');
    expect(component.submitted).toBeFalsy();
  });

  it('should handle checkInstantValue with subCategory type', () => {
    fixture.detectChanges();

    const mockEvent = { target: { checked: true } };
    const mockData = {
      LocationNotificationPreferences: [{
        id: 1,
        MemberId: 1,
        ProjectId: 1,
        follow: false
      }]
    };

    component.chosenLocationNotification = [];
    const handleSubCategorySpy = jest.spyOn(component, 'handleSubCategoryFlow');

    component.checkInstantValue(mockData, mockEvent, 'subCategory');

    expect(handleSubCategorySpy).toHaveBeenCalledWith(mockData, mockEvent);
  });

  it('should handle checkInstantValue with unknown type', () => {
    fixture.detectChanges();

    const mockEvent = { target: { checked: true } };
    const mockData = {
      LocationNotificationPreferences: [{
        id: 1,
        MemberId: 1,
        ProjectId: 1,
        follow: false
      }]
    };

    component.chosenLocationNotification = [];
    const initialLength = component.chosenLocationNotification.length;

    // Call with an unknown type
    component.checkInstantValue(mockData, mockEvent, 'unknownType');

    // Should not add any notifications for unknown type
    expect(component.chosenLocationNotification.length).toBe(initialLength);
  });

  it('should handle checkInstantValue default type with empty locationList', () => {
    fixture.detectChanges();

    const mockEvent = { target: { checked: true } };
    component.defaultLocationPath = {
      LocationNotificationPreferences: [{
        id: 1,
        MemberId: 1,
        ProjectId: 1,
        follow: false
      }]
    };
    component.locationList = []; // Empty list
    component.chosenLocationNotification = [];

    component.checkInstantValue(null, mockEvent, 'default');

    // Should only add default location notification
    expect(component.chosenLocationNotification.length).toBe(1);
    expect(component.chosenLocationNotification[0].id).toBe(1);
  });

  it('should handle checkInstantValue default type without defaultLocationPath', () => {
    fixture.detectChanges();

    const mockEvent = { target: { checked: true } };
    component.defaultLocationPath = null;
    component.locationList = [{
      LocationNotificationPreferences: [{
        id: 2,
        MemberId: 1,
        ProjectId: 1,
        follow: false
      }],
      paths: []
    }];
    component.chosenLocationNotification = [];

    component.checkInstantValue(null, mockEvent, 'default');

    // Should only add location list notifications, not default
    expect(component.chosenLocationNotification.length).toBe(1);
    expect(component.chosenLocationNotification[0].id).toBe(2);
  });

  // Complex scenario tests
  it('should handle checkInstantValue default type with nested paths and tiers', () => {
    fixture.detectChanges();

    const mockEvent = { target: { checked: true } };
    component.defaultLocationPath = {
      LocationNotificationPreferences: [{
        id: 1,
        MemberId: 1,
        ProjectId: 1,
        follow: false
      }]
    };
    component.locationList = [{
      LocationNotificationPreferences: [{
        id: 2,
        MemberId: 1,
        ProjectId: 1,
        follow: false
      }],
      paths: [{
        LocationNotificationPreferences: [{
          id: 3,
          MemberId: 1,
          ProjectId: 1,
          follow: false
        }],
        tier: [{
          LocationNotificationPreferences: [{
            id: 4,
            MemberId: 1,
            ProjectId: 1,
            follow: false
          }]
        }]
      }]
    }];
    component.chosenLocationNotification = [];

    component.checkInstantValue(null, mockEvent, 'default');

    // Should add default + main location + subCategory + tier = 4 notifications
    expect(component.chosenLocationNotification.length).toBe(4);
    expect(component.chosenLocationNotification.map(n => n.id)).toEqual([1, 2, 3, 4]);
  });

  it('should handle handleMainCategoryFlow with nested paths and tiers', () => {
    fixture.detectChanges();

    const mockEvent = { target: { checked: true } };
    const mockData = {
      LocationNotificationPreferences: [{
        id: 1,
        MemberId: 1,
        ProjectId: 1,
        follow: false
      }],
      paths: [{
        LocationNotificationPreferences: [{
          id: 2,
          MemberId: 1,
          ProjectId: 1,
          follow: false
        }],
        tier: [{
          LocationNotificationPreferences: [{
            id: 3,
            MemberId: 1,
            ProjectId: 1,
            follow: false
          }]
        }]
      }]
    };

    component.chosenLocationNotification = [];
    const result = component.handleMainCategoryFlow(mockData, mockEvent);

    expect(component.chosenLocationNotification.length).toBe(3); // Main + subCategory + tier
    expect(mockData.LocationNotificationPreferences[0].follow).toBeTruthy();
    expect(mockData.paths[0].LocationNotificationPreferences[0].follow).toBeTruthy();
    expect(mockData.paths[0].tier[0].LocationNotificationPreferences[0].follow).toBeTruthy();
    expect(result).toBe(mockData);
  });

  it('should handle handleMainCategoryFlow with empty paths', () => {
    fixture.detectChanges();

    const mockEvent = { target: { checked: true } };
    const mockData = {
      LocationNotificationPreferences: [{
        id: 1,
        MemberId: 1,
        ProjectId: 1,
        follow: false
      }],
      paths: []
    };

    component.chosenLocationNotification = [];
    const result = component.handleMainCategoryFlow(mockData, mockEvent);

    expect(component.chosenLocationNotification.length).toBe(1); // Only main
    expect(result).toBe(mockData);
  });

  it('should handle handleMainCategoryFlow with null data', () => {
    fixture.detectChanges();

    const mockEvent = { target: { checked: true } };
    component.chosenLocationNotification = [];

    const result = component.handleMainCategoryFlow(null, mockEvent);

    expect(component.chosenLocationNotification.length).toBe(0);
    expect(result).toBeNull();
  });

  it('should handle handleSubCategoryFlow with null data', () => {
    fixture.detectChanges();

    const mockEvent = { target: { checked: true } };
    component.chosenLocationNotification = [];

    const result = component.handleSubCategoryFlow(null, mockEvent);

    expect(component.chosenLocationNotification.length).toBe(0);
    expect(result).toBeNull();
  });

  it('should handle submitLocationNotification with empty chosenLocationNotification', () => {
    fixture.detectChanges();

    const mockResponse = { message: 'Success' };
    locationService.updateMemberLocationPreference.mockReturnValue(of(mockResponse));

    component.chosenLocationNotification = [];
    component.submitLocationNotification();

    expect(locationService.updateMemberLocationPreference).toHaveBeenCalledWith({
      chosenMemberPreference: []
    });
    expect(component.submitted).toBeTruthy();
  });

  it('should handle getLocationList error scenario', () => {
    fixture.detectChanges();

    const mockError = new Error('Network error');
    locationService.getLocationList.mockReturnValue(throwError(() => mockError));

    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.pageSize = 25;
    component.currentPageNo = 1;

    // This should not throw an error, but the component should handle it gracefully
    expect(() => component.getLocationList()).not.toThrow();
  });

  it('should handle modal reference assignment in openModal1', () => {
    fixture.detectChanges();

    const template = {} as any;
    const mockModalRef = {
      hide: jest.fn(),
      setClass: jest.fn()
    };
    modalService.show.mockReturnValue(mockModalRef);

    component.openModal1(template);

    expect(component.modalRef).toBe(mockModalRef);
  });

  it('should handle modal reference assignment in openModalPopup', () => {
    fixture.detectChanges();

    const template = {} as any;
    const mockModalRef = {
      hide: jest.fn(),
      setClass: jest.fn()
    };
    modalService.show.mockReturnValue(mockModalRef);

    component.openModalPopup(template);

    expect(component.modalRef).toBe(mockModalRef);
  });

  it('should verify initial component state', () => {
    // Don't call detectChanges to test initial state
    expect(component.loader).toBeTruthy();
    expect(component.currentPageNo).toBe(1);
    expect(component.pageSize).toBe(25);
    expect(component.search).toBe('');
    expect(component.sort).toBe('DESC');
    expect(component.sortColumn).toBe('id');
    expect(component.locationList).toEqual([]);
    expect(component.totalCount).toBe(0);
    expect(component.showSearchbar).toBeFalsy();
    expect(component.chosenLocationNotification).toEqual([]);
    expect(component.submitted).toBeFalsy();
    expect(component.isOpened).toBeTruthy();
    expect(component.defaultLocationPath).toEqual({});
  });

  it('should handle multiple consecutive calls to getLocationList', () => {
    fixture.detectChanges();

    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.pageSize = 25;
    component.currentPageNo = 1;

    // Clear previous calls
    locationService.getLocationList.mockClear();

    // Call multiple times
    component.getLocationList();
    component.getLocationList();
    component.getLocationList();

    expect(locationService.getLocationList).toHaveBeenCalledTimes(3);
  });
});
