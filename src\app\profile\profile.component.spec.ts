import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ProfileComponent } from './profile.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { ProfileService } from '../services/profile/profile.service';
import { ProjectService } from '../services/profile/project.service';
import { of, throwError, BehaviorSubject } from 'rxjs';

describe('ProfileComponent', () => {
  let component: ProfileComponent;
  let fixture: ComponentFixture<ProfileComponent>;
  let modalService: jest.Mocked<BsModalService>;
  let titleService: jest.Mocked<Title>;
  let profileService: jest.Mocked<ProfileService>;
  let projectService: jest.Mocked<ProjectService>;
  let toastrService: jest.Mocked<ToastrService>;
  let router: jest.Mocked<Router>;

  beforeEach(async () => {
    modalService = {
      show: jest.fn(),
    } as any;

    titleService = {
      setTitle: jest.fn(),
    } as any;

    profileService = {
      updatedRefreshOverview: jest.fn(),
      uploadProfile: jest.fn(),
      updatedOverView: jest.fn(),
      overViewDetail: new BehaviorSubject({}),
    } as any;

    projectService = {
      projectParent: new BehaviorSubject({}),
      isProjectAccount: new BehaviorSubject(false),
      subStatus: new BehaviorSubject({}),
    } as any;

    toastrService = {
      success: jest.fn(),
      error: jest.fn(),
    } as any;

    router = {
      url: '/profile',
    } as any;

    await TestBed.configureTestingModule({
      declarations: [ProfileComponent],
      providers: [
        { provide: BsModalService, useValue: modalService },
        { provide: Title, useValue: titleService },
        { provide: ProfileService, useValue: profileService },
        { provide: ProjectService, useValue: projectService },
        { provide: ToastrService, useValue: toastrService },
        { provide: Router, useValue: router },
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ProfileComponent);
    component = fixture.componentInstance;
    component.userData = {
      User: {
        profilePic: ''
      }
    };
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set title on initialization', () => {
    expect(titleService.setTitle).toHaveBeenCalledWith('Follo - Profile');
  });

  it('should handle file upload with valid image', () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const mockResponse = { message: 'Success', data: { imageUrl: 'test-url' } };
    
    profileService.uploadProfile.mockReturnValue(of(mockResponse));
    
    component.fileChangeEvent([mockFile]);
    
    expect(profileService.uploadProfile).toHaveBeenCalled();
    expect(toastrService.success).toHaveBeenCalledWith('Success', 'Success');
    expect(component.userData.User.profilePic).toBe('test-url');
  });

  it('should handle file upload with invalid image', () => {
    const mockFile = new File(['test'], 'test.pdf', { type: 'application/pdf' });
    
    component.fileChangeEvent([mockFile]);
    
    expect(toastrService.error).toHaveBeenCalledWith(
      'Please upload *jpg,*jpeg,*png images.',
      'OOPS!'
    );
  });

  it('should handle file upload error', () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const mockError = { message: { statusCode: 400, details: [{ error: 'Invalid file' }] } };
    
    profileService.uploadProfile.mockReturnValue(throwError(() => mockError));
    
    component.fileChangeEvent([mockFile]);
    
    expect(toastrService.error).toHaveBeenCalled();
  });

  it('should open modal with correct configuration', () => {
    const mockTemplate = {} as any;
    const mockModalRef = {
      hide: jest.fn(),
      setClass: jest.fn(),
    } as unknown as BsModalRef;
    modalService.show.mockReturnValue(mockModalRef);

    component.openModal1(mockTemplate);

    expect(modalService.show).toHaveBeenCalledWith(
      mockTemplate,
      { backdrop: 'static', keyboard: false, class: 'modal-md thanks-popup custom-modal' }
    );
  });

  it('should update subscription status', () => {
    const mockSubStatus = { status: true };
    (projectService.subStatus as BehaviorSubject<any>).next(mockSubStatus);

    component.ngOnInit();

    expect(component.subscriptionStatus).toBe(true);
  });

  it('should handle project account subscription', () => {
    (projectService.isProjectAccount as BehaviorSubject<boolean>).next(true);

    component.ngOnInit();

    expect(component.isAccount).toBe(true);
  });

  it('should handle project parent data', () => {
    const mockProjectData = { ProjectId: 123, ParentCompanyId: 456 };
    (projectService.projectParent as BehaviorSubject<any>).next(mockProjectData);

    component.ngOnInit();

    expect(component.ProjectId).toBe(123);
    expect(component.ParentCompanyId).toBe(456);
  });

  it('should call updatedRefreshOverview when change is called', () => {
    component.change();
    expect(profileService.updatedRefreshOverview).toHaveBeenCalledWith(true);
  });
});
