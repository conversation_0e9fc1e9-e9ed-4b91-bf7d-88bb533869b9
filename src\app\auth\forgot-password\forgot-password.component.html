<section class="auth-content">
  <div class="auth-overlay d-flex align-items-start w-100">
    <div class="container-fluid">
      <div class="top-logo">
        <img
          src="./assets/images/logo.svg"
          routerLink="/login"
          width="100"
          class="c-pointer auth-logo my-5 mx-5"
          alt="Follo"
        />
      </div>
      <div class="row align-items-center fotgot-container">
        <div class="col-md-7 col-lg-4 offset-md-5 offset-lg-6 px-lg-0">
          <div class="card-container">
            <div class="card-body p20">
              <div class="row">
                <div class="col-md-12 col-lg-11 m-auto">
                  <div class="mb-4">
                    <h1 class="auth-title color-grey7 fs22 fw-bold cairo-regular my-2">
                      Forgot Password
                    </h1>
                    <p class="color-grey4 fs13 fw500">
                      Enter the email address associated with your account.
                    </p>
                  </div>
                  <form name="form" [formGroup]="forgotpasswordForm" (ngSubmit)="onSubmit()">
                    <div class="mb-4">
                      <div class="input-group">
                        <span class="input-group-text"
                          ><img src="./assets/images/mail.svg" class="form-icon" alt="Mail"
                        /></span>
                        <input
                          type="email"
                          class="form-control"
                          placeholder="Email address"
                          autocomplete="off"
                          name="email"
                          formControlName="email"
                          [ngClass]="{ 'is-invalid': submitted &amp;&amp; forgotpasswordForm.controls.email.errors }"
                        />
                        <span class="underline"></span>
                      </div>
                      <div
                        *ngIf="submitted &amp;&amp; forgotpasswordForm.controls.email.errors"
                        class="text-danger fs12"
                      >
                        <div *ngIf="forgotpasswordForm.controls.email.errors.required">
                          Email is required
                        </div>
                        <div *ngIf="forgotpasswordForm.controls.email.errors.pattern">
                          Please enter valid email
                        </div>
                      </div>
                    </div>
                    <button
                      type="submit"
                      class="btn btn-orange color-orange radius20 fs13 col-md-4 col-6 fw-bold cairo-regular w-100"
                      [disabled]="formSubmitted && forgotpasswordForm.valid"
                    >
                      <em
                        class="fa fa-spinner"
                        aria-hidden="true"
                        *ngIf="formSubmitted && forgotpasswordForm.valid"
                      ></em
                      >Submit
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<ng-template #resetPassword>
  <div class="modal-body">
    <div class="d-flex align-items-center">
      <img src="./assets/images/reset-tick.svg" class="me-2" alt="Reset Icon" />
      <p class="color-grey15 fs14 fw500 mb-0 fw-bold">
        Success! Your new password has been sent to your primary email address.
      </p>
    </div>
  </div>
</ng-template>
