import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Router } from '@angular/router';
import { ApiService } from '../api_base/api.service';

@Injectable({
  providedIn: 'root',
  })
export class ProfileService {
  public overViewDetail: BehaviorSubject<any> = new BehaviorSubject<any>({});

  public refreshData: BehaviorSubject<any> = new BehaviorSubject<any>({});


  public constructor(public router: Router, private readonly api: ApiService) {
    // constructor
  }

  public getOverView(params): Observable<any> {
    return this.api.get(`member/get_overview_detail/${params.ProjectId}/${params.ParentCompanyId}`);
  }

  public cancelSubscription(params): Observable<any> {
    return this.api.get(`payment/cancel_subscription/${params.ProjectId}`);
  }

  public updateProfile(data): Observable<any> {
    return this.api.post('member/update_profile', data);
  }

  public uploadProfile(data): Observable<any> {
    return this.api.requestImage('user/upload_profile/', data);
  }

  public updatedOverView(data): any {
    return this.overViewDetail.next(data);
  }

  public updatedRefreshOverview(data): any {
    return this.refreshData.next(data);
  }

  public getProjectList(params, payload): Observable<any> {
    return this.api.post(`project/get_projects_list/${params.pageSize}/${params.pageNo}/${params.ParentCompanyId}`, payload);
  }


  public getPlansAndProjects(params, payload): Observable<any> {
    return this.api.post(`project/get_plans_projects/${params.pageSize}/${params.pageNo}/${params.sort}/${params.sortByField}`, payload);
  }
}
