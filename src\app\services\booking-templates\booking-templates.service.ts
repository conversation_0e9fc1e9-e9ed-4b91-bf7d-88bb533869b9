import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject } from 'rxjs';
import { ApiService } from '../api_base/api.service';


@Injectable({
  providedIn: 'root',
})
export class BookingTemplatesService {
  public fetchTemplates = new BehaviorSubject<any>('');

  public constructor(private readonly api: ApiService) {
    // constructor
  }


  public getTemplates(queryPath, params): Observable<any> {
    return this.api.get(`templates/${queryPath.ProjectId}`, params);
  }

  public saveTemplate(payload): Observable<any> {
    return this.api.post('templates/', payload);
  }

  public editTemplate(payload): Observable<any> {
    return this.api.put('templates/', payload);
  }

  public deleteBookingTemplate(data, payload): Observable<any> {
    return this.api.post(`templates/${data.ProjectId}`, payload);
  }

  public updateTemplate(): any {
    return this.fetchTemplates.next(true);
  }

  public getTemplate(params): Observable<any> {
    return this.api.get('templates', params);
  }
}
