// /********* sidemenu ************/
.own-sidemenu-main {
  width: 230px;
  transition: 1s;
  position: fixed;
  z-index: 999;
  border: 0;
  top: 0;
  background: $white;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.25) !important;
  height: 100vh;

  .menu-txt {
    padding-left: 5px;
    font-weight: 400;
  }

  &.show {
    .menu-txt {
      animation: show-menu-txt 1s linear;
    }
  }

  .side-list-group {
    @extend .pt25;

    margin-top: 53px;
    background: $white;

    a {
      border-radius: 0;
      display: block;
      color: $grey42;
      text-decoration: none;

      @extend .fs12;
    }

    span {
      display: inline-block;
      word-break: break-word;
      max-width: 150px;
    }

    .list-group-item {
      border: 0;
      white-space: nowrap;
      border-radius: 0;

      @extend .pb3;
      @extend .pt3;
      @extend .pl12;
      @extend .mb15;
      @extend .ml1;

      color: $grey42;
      border-left: 3px solid transparent;

      .menu-icon {
        display: inline-block;

        img {
          height: 16px;
          width: 16px;
          margin: -9px 8px;
        }
      }

      .active-img {
        display: none;
      }

      &:hover,
      &.active {
        background: transparent;


        .menu-txt {
          color: $grey7;
          font-weight: 700;
        }

        .active-img {
          display: inline-block;
        }

        .inactive-img {
          display: none;
        }
      }

      &.active {

          &::before {
            content: "";
            position: absolute;
            left: -4px;
            top: 3px;
            bottom: 0;
            background: $orange;
            width: 5px;
            height: 45px;
            margin: auto;
            box-shadow: 3px 0 9px #f45e286c;
          }

        .menu-txt {
          color: $grey7;
          font-weight: 700;
        }
      }
    }

    .project-list {
      background: $grey9;
      margin: 10px;
      border-radius: 10px;

      .side-menu-li {
        color: $orange;
      }

      &:hover {
        background: $grey9;
        border-color: $grey9;
      }
    }

    .sub-menu {
      margin-bottom: 0;
      position: absolute;
      top: 145px;
      background: $white;
      width: 92%;
      z-index: 99999;

      @extend .pl0;

      left: 8px;
      box-shadow: 0 10px 10px #00000029;

      ul {
        @extend .pt15;
        @extend .pb20;
        @extend .pl25;

        list-style: none;
        line-height: 30px;

        li {
          &.active {
            a {
              @extend .color-orange;

              font-weight: 500;
            }
          }

          &:not(:last-child) {
            @extend .mb20;
          }
        }
      }
    }

    .sub-menu.project-list-menu {
      top: 195px;
      z-index: 99999;
    }
  }
  .list-group-item-submenu.active,
  .list-group-item.active {
    span {
      color: $dark-blue-2;
      font-weight: 600;
      .all-calendar-span {
        color: $grey42;
        font-weight: 400;
        &:hover {
          font-weight: 700;
        }
      }
    }
  }

  .add-pre-btn {
    &:hover {
      .plus {
        filter: brightness(0) invert(1);
      }
    }
  }
}

.dropdown-parent-list {
  .menu-txt {
    @extend .pr20;

    .arrow {
      vertical-align: middle;
      transition: 1s;
      cursor: pointer;

      &.rotateup {
        transform: rotate(-180deg);
        vertical-align: middle;
      }
    }
  }

  .footer-menu {
    cursor: default;
  }
}

.footer-arrow {
  margin-top: 7px;
  margin-right: 12px;
}

@keyframes show-menu-txt {
  from {
    opacity: 0;
    display: none;
  }

  to {
    opacity: 1;
    display: inline-block;
  }
}

@media (min-width: 768px) {
  .own-sidemenu-main {
    .list-group {
      margin-top: 0;
    }
  }
}

.custom-scroll {
  max-height: calc(100vh - 100px);
  overflow-y: auto;
  overflow-x: hidden;
}

.sub-menu.footer-point {
  cursor: pointer;
}

.settings-width {
  width: 155px;
}
.arrow
{
  height: 13px;
  width: 13px;
  margin-top: 3px;
}
.rotateright {
  transform: rotate(-90deg);
  vertical-align: middle;
}
.rotateup {
  transform: rotate(-180deg);
  vertical-align: middle;

}
.edit-btn-popover:has(.rotateup) {
  .arrow{
  margin-top: 6px !important;
  }
}
.sub-list-scroll {
  max-height: 150px;
  overflow-y: auto;
  .project-name-sublist{
    line-height: 20px;
    word-break: break-word;
    padding-bottom: 5px;
    padding-right: 10px;
  }

}
.company-name-lists{
  padding-right: 20px;
  height: calc(100vh - 460px);
  overflow-y: auto;
}

.add_calender-icon{
    width: 14px;
    margin-left: -25px;
}
// /********* sidemenu ************/
