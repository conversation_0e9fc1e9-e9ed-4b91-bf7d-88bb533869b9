import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DeliveriesComponent } from './deliveries.component';
import { BsModalService } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { ReportsService } from '../../services/reports/reports.service';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ToastrService } from 'ngx-toastr';
import { of, BehaviorSubject } from 'rxjs';
import { ElementRef, Pipe, PipeTransform, NO_ERRORS_SCHEMA } from '@angular/core';

@Pipe({
  name: 'paginate'
})
class MockPaginatePipe implements PipeTransform {
  transform(value: any[], config: any): any[] {
    return value;
  }
}

describe('DeliveriesComponent', () => {
  let component: DeliveriesComponent;
  let fixture: ComponentFixture<DeliveriesComponent>;
  let modalServiceSpy: jest.Mocked<BsModalService>;
  let reportsServiceSpy: jest.Mocked<ReportsService>;
  let projectServiceSpy: jest.Mocked<ProjectService>;
  let deliveryServiceSpy: jest.Mocked<DeliveryService>;
  let toastrServiceSpy: jest.Mocked<ToastrService>;

  beforeEach(async () => {
    const modalSpy = {
      show: jest.fn().mockReturnValue({
        hide: jest.fn(),
        content: {}
      })
    };
    const reportsSpy = {
      deliveryReports: jest.fn().mockReturnValue(of({
        data: { rows: [], count: 0 }
      })),
      exportDeliveryRequest: jest.fn().mockReturnValue(of({
        data: 'http://example.com/report.pdf'
      })),
      exportDeliveryRequestInExcelFormat: jest.fn().mockReturnValue(of({}))
    };
    const projectSpy = {
      projectParent: new BehaviorSubject({
        ProjectId: '123',
        ParentCompanyId: '456'
      }),
      gateList: jest.fn().mockReturnValue(of({
        data: [{ id: 1, gateName: 'Gate 1' }]
      })),
      listEquipment: jest.fn().mockReturnValue(of({
        data: [{ id: 1, equipmentName: 'Equipment 1' }]
      })),
      getDefinableWork: jest.fn().mockReturnValue(of({
        data: [{ id: 1, DFOW: 'DFOW 1' }]
      })),
      getCompanies: jest.fn().mockReturnValue(of({
        data: [{ id: 1, companyName: 'Company 1' }]
      })),
      getLocations: jest.fn().mockReturnValue(of({
        data: [{ id: 1, locationPath: 'Location 1' }]
      })),
      listAllMember: jest.fn().mockReturnValue(of({
        data: [{ id: 1, User: { email: '<EMAIL>' } }]
      }))
    };
    const deliverySpy = {
      saveAsExcelFile: jest.fn()
    };
    const toastrSpy = {
      success: jest.fn(),
      error: jest.fn()
    };

    await TestBed.configureTestingModule({
      declarations: [ DeliveriesComponent, MockPaginatePipe ],
      imports: [ ReactiveFormsModule ],
      providers: [
        UntypedFormBuilder,
        { provide: BsModalService, useValue: modalSpy },
        { provide: ReportsService, useValue: reportsSpy },
        { provide: ProjectService, useValue: projectSpy },
        { provide: DeliveryService, useValue: deliverySpy },
        { provide: ToastrService, useValue: toastrSpy },
        { provide: ElementRef, useValue: { nativeElement: document.createElement('div') } }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    })
    .compileComponents();

    modalServiceSpy = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    reportsServiceSpy = TestBed.inject(ReportsService) as jest.Mocked<ReportsService>;
    projectServiceSpy = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    deliveryServiceSpy = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    toastrServiceSpy = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DeliveriesComponent);
    component = fixture.componentInstance;

    // Mock delivery reports response
    reportsServiceSpy.deliveryReports.mockReturnValue(of({
      data: [],
      totalCount: 0
    }));

    // Initialize forms manually since we're not triggering the subscription
    component.filterDetailsForm();
    component.exportDetailsForm();

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Initialization', () => {
    it('should initialize with default values', () => {
      expect(component.currentPageNo).toBe(1);
      expect(component.perPage).toBe(1);
      expect(component.pageSize).toBe(25);
      expect(component.pageNo).toBe(1);
      // expect(component.loader).toBe(true);
      expect(component.sortColumn).toBe('deliveryStart');
      expect(component.sort).toBe('ASC');
      expect(component.filterCount).toBe(0);
      expect(component.selectedRecurrence).toBe('Does Not Repeat');
      expect(component.reportType).toBe('PDF');
      expect(component.reportName).toBe('Delivery Report');
    });

    it('should initialize dropdown states', () => {
      expect(component.isEquipmentDropdownOpen).toBe(false);
      expect(component.isStatusDropdownOpen).toBe(false);
      expect(component.isGateDropdownOpen).toBe(false);
      expect(component.isCompanyDropdownOpen).toBe(false);
      expect(component.isLocationDropdownOpen).toBe(false);
      expect(component.isResponsibleDropdownOpen).toBe(false);
      expect(component.isDefineDropdownOpen).toBe(false);
    });

    it('should initialize selected arrays', () => {
      expect(component.selectedEquipment).toEqual([]);
      expect(component.selectedGate).toEqual([]);
      expect(component.selectedCompany).toEqual([]);
      expect(component.selectedLocation).toEqual([]);
      expect(component.selectedResponsible).toEqual([]);
      expect(component.selectedDefine).toEqual([]);
      expect(component.selectedStatus).toEqual([]);
    });

    it('should initialize placeholders', () => {
      expect(component.equipmentPlaceholder).toBe('Select Equipment');
      expect(component.gatePlaceholder).toBe('Select Gate');
      expect(component.companyPlaceholder).toBe('Select Company');
      expect(component.locationPlaceholder).toBe('Select Location');
      expect(component.statusPlaceholder).toBe('Select Status');
      expect(component.responsiblePlaceholder).toBe('Select Responsible Person');
      expect(component.definePlaceholder).toBe('Select DFOW');
    });

    it('should initialize status list', () => {
      expect(component.wholeStatus).toEqual(['Approved', 'Declined', 'Delivered', 'Pending']);
      expect(component.statusList).toEqual([
        { name: 'Approved' },
        { name: 'Declined' },
        { name: 'Delivered' },
        { name: 'Pending' }
      ]);
    });

    it('should initialize table headers', () => {
      expect(component.tableHeaders.id.isActive).toBe(true);
      expect(component.tableHeaders.description.isActive).toBe(true);
      expect(component.tableHeaders.date.isActive).toBe(true);
      expect(component.tableHeaders.status.isActive).toBe(true);
      expect(component.tableHeaders.approvedby.isActive).toBe(true);
      expect(component.tableHeaders.equipment.isActive).toBe(false);
    });

    it('should initialize export type array', () => {
      expect(component.exportType).toEqual(['EXCEL', 'CSV', 'PDF']);
    });

    it('should initialize filter form', () => {
      expect(component.filterForm).toBeTruthy();
      expect(component.filterForm.get('companyFilter')).toBeTruthy();
      expect(component.filterForm.get('descriptionFilter')).toBeTruthy();
      expect(component.filterForm.get('statusFilter')).toBeTruthy();
      expect(component.filterForm.get('dateFilter')).toBeTruthy();
      expect(component.filterForm.get('memberFilter')).toBeTruthy();
      expect(component.filterForm.get('gateFilter')).toBeTruthy();
      expect(component.filterForm.get('equipmentFilter')).toBeTruthy();
      expect(component.filterForm.get('defineFilter')).toBeTruthy();
      expect(component.filterForm.get('idFilter')).toBeTruthy();
      expect(component.filterForm.get('pickFrom')).toBeTruthy();
      expect(component.filterForm.get('pickTo')).toBeTruthy();
      expect(component.filterForm.get('locationFilter')).toBeTruthy();
    });

    it('should initialize export form', () => {
      expect(component.exportForm).toBeTruthy();
      expect(component.exportForm.get('reportName')).toBeTruthy();
      expect(component.exportForm.get('reportType')).toBeTruthy();
      expect(component.exportForm.get('reportName').value).toBe('Delivery Report');
      expect(component.exportForm.get('reportType').value).toBe('PDF');
    });

    it('should initialize active headers from table headers', () => {
      const activeHeaders = Object.values(component.tableHeaders).filter(
        (element: any) => element.isActive === true
      );
      expect(component.activeHeaders.length).toBe(activeHeaders.length);
    });
  });

  describe('Modal Operations', () => {
    it('should open export modal', () => {
      const template = {} as any;
      component.openModal(template);
      expect(modalServiceSpy.show).toHaveBeenCalledWith(template, {
        class: 'modal-export'
      });
    });

    it('should open downloads modal', () => {
      const template = {} as any;
      component.openModal1(template);
      expect(modalServiceSpy.show).toHaveBeenCalledWith(template, {
        class: 'modal-downloads'
      });
    });

    it('should open send to modal', () => {
      const template = {} as any;
      component.openModal3(template);
      expect(modalServiceSpy.show).toHaveBeenCalledWith(template, {
        class: 'modal-sendto'
      });
    });

    it('should open filter modal', () => {
      const template = {} as any;
      component.openModal4(template);
      expect(modalServiceSpy.show).toHaveBeenCalledWith(template, {
        class: ' report-filter-modal filter-popup report-filter custom-modal'
      });
    });

    it('should open schedule popup', () => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.updatedMemberList = [{ id: 1, UserEmail: '<EMAIL>' }];
      component.filterPayload = { test: 'data' };

      component.openSchedulePopup();

      expect(modalServiceSpy.show).toHaveBeenCalled();
    });

    it('should open save modal', () => {
      component.ProjectId = '123';
      component.filterPayload = { test: 'data' };
      component.updatedMemberList = [{ id: 1, UserEmail: '<EMAIL>' }];
      component.pageSize = 25;
      component.pageNo = 1;

      component.openModalSave();

      expect(modalServiceSpy.show).toHaveBeenCalled();
    });

    it('should close filter popup', () => {
      component.modalRef4 = { hide: jest.fn() } as any;
      component.closeFilterPopup();

      expect(component.modalRef4.hide).toHaveBeenCalled();
      expect(component.isEquipmentDropdownOpen).toBe(false);
      expect(component.isStatusDropdownOpen).toBe(false);
    });
  });

  describe('Dropdown Functionality', () => {
    it('should toggle dropdowns correctly', () => {
      component.toggleDropdown('equipment');
      expect(component.isEquipmentDropdownOpen).toBe(true);

      component.toggleDropdown('equipment');
      expect(component.isEquipmentDropdownOpen).toBe(false);
    });

    it('should close all dropdowns when toggling a different one', () => {
      component.isEquipmentDropdownOpen = true;
      component.isStatusDropdownOpen = true;

      component.toggleDropdown('gate');

      expect(component.isEquipmentDropdownOpen).toBe(false);
      expect(component.isStatusDropdownOpen).toBe(false);
      expect(component.isGateDropdownOpen).toBe(true);
    });

    it('should handle document click outside dropdowns', () => {
      component.isEquipmentDropdownOpen = true;
      component.isStatusDropdownOpen = true;
      component.isGateDropdownOpen = true;

      const event = new MouseEvent('click');
      Object.defineProperty(event, 'target', {
        value: document.createElement('div')
      });

      component.onDocumentClick(event);

      expect(component.isEquipmentDropdownOpen).toBe(false);
      expect(component.isStatusDropdownOpen).toBe(false);
      expect(component.isGateDropdownOpen).toBe(false);
    });

  });

  describe('Pagination and Sorting', () => {
    it('should handle page size change', () => {
      const newPageSize = 50;
      jest.spyOn(component, 'getDeliveryReports');

      component.changePageSize(newPageSize);

      expect(component.pageSize).toBe(newPageSize);
      expect(component.getDeliveryReports).toHaveBeenCalled();
    });

    it('should handle page number change', () => {
      const newPageNo = 2;
      jest.spyOn(component, 'getDeliveryReports');

      component.changePageNo(newPageNo);

      expect(component.pageNo).toBe(newPageNo);
      expect(component.getDeliveryReports).toHaveBeenCalled();
    });

    it('should handle sort by field', () => {
      const fieldName = 'description';
      const sortType = 'DESC';
      jest.spyOn(component, 'getDeliveryReports');

      component.sortByField(fieldName, sortType);

      expect(component.sortColumn).toBe(fieldName);
      expect(component.sort).toBe(sortType);
      expect(component.getDeliveryReports).toHaveBeenCalled();
    });
  });

  describe('Filter Operations', () => {
    it('should handle filter submission with all fields', () => {
      jest.spyOn(component, 'getDeliveryReports');
      component.filterForm.patchValue({
        descriptionFilter: 'test description',
        dateFilter: ['2024-01-01', '2024-01-31'],
        companyFilter: [1, 2],
        memberFilter: [1],
        gateFilter: [1],
        equipmentFilter: [1],
        statusFilter: ['Approved'],
        defineFilter: [1],
        idFilter: 'DEL001',
        locationFilter: [1]
      });

      component.filterSubmit();

      // expect(component.formSubmitted).toBe(true);
      expect(component.filterCount).toBe(10);
      expect(component.pageNo).toBe(1);
      expect(component.getDeliveryReports).toHaveBeenCalled();
    });

    it('should handle filter submission with empty fields', () => {
      jest.spyOn(component, 'getDeliveryReports');
      component.filterForm.patchValue({
        descriptionFilter: '',
        dateFilter: '',
        companyFilter: '',
        memberFilter: '',
        gateFilter: '',
        equipmentFilter: '',
        statusFilter: '',
        defineFilter: '',
        idFilter: '',
        locationFilter: ''
      });

      component.filterSubmit();

      expect(component.filterCount).toBe(0);
      expect(component.getDeliveryReports).toHaveBeenCalled();
    });

    it('should close filter popup after submission', () => {
      component.modalRef4 = { hide: jest.fn() } as any;
      jest.spyOn(component, 'closeFilterPopup');
      jest.spyOn(component, 'getDeliveryReports');

      component.filterSubmit();

      expect(component.closeFilterPopup).toHaveBeenCalled();
    });

    it('should reset filters correctly', () => {
      jest.spyOn(component, 'getDeliveryReports');
      component.selectedEquipment = [{ id: 1, equipmentName: 'Equipment 1' }];
      component.selectedStatus = [{ name: 'Approved' }];
      component.filterCount = 5;
      component.filterForm.get('descriptionFilter').setValue('test description');

      component.resetFilter();

      expect(component.selectedEquipment).toEqual([]);
      expect(component.selectedStatus).toEqual([]);
      expect(component.selectedGate).toEqual([]);
      expect(component.selectedCompany).toEqual([]);
      expect(component.selectedLocation).toEqual([]);
      expect(component.selectedResponsible).toEqual([]);
      expect(component.selectedDefine).toEqual([]);
      expect(component.filterCount).toBe(0);
      expect(component.pageNo).toBe(1);
      expect(component.gatePlaceholder).toBe('Select Gate');
      expect(component.companyPlaceholder).toBe('Select Company');
      expect(component.equipmentPlaceholder).toBe('Select Equipment');
      expect(component.getDeliveryReports).toHaveBeenCalled();
    });

  });

  describe('Utility Methods', () => {
    it('should map filter array correctly', () => {
      const testArray = [1, 2, 3];
      const result = component.mapFilterArray(testArray);
      expect(result).toEqual([1, 2, 3]);
    });

    it('should return empty array for null input', () => {
      const result = component.mapFilterArray(null);
      expect(result).toEqual([]);
    });

    it('should return empty array for empty input', () => {
      const result = component.mapFilterArray([]);
      expect(result).toEqual([]);
    });

    it('should handle recurrence selection', () => {
      const value = 'Weekly';
      component.onRecurrenceSelect(value);
      expect(component.selectedRecurrence).toBe(value);
    });
  });

  describe('Selection and Toggle Functionality', () => {
    beforeEach(() => {
      component.equipmentList = [{ id: 1, equipmentName: 'Equipment 1' }];
      component.gateList = [{ id: 1, gateName: 'Gate 1' }];
      component.companyList = [{ id: 1, companyName: 'Company 1' }];
      component.locationList = [{ id: 1, locationPath: 'Location 1' }];
      component.memberList = [{ id: 1, User: { email: '<EMAIL>' } }];
      component.defineList = [{ id: 1, DFOW: 'DFOW 1' }];
      component.statusList = [{ name: 'Approved' }];
    });

    it('should toggle equipment selection', () => {
      const item = { id: 1, equipmentName: 'Equipment 1' };
      const event = new Event('click');

      component.toggleSelection(item, 'equipment', event);

      expect(component.selectedEquipment).toContain(item);
      expect(component.equipmentPlaceholder).toBe('Equipment 1');
    });

    it('should toggle gate selection', () => {
      const item = { id: 1, gateName: 'Gate 1' };
      const event = new Event('click');

      component.toggleSelection(item, 'gate', event);

      expect(component.selectedGate).toContain(item);
      expect(component.gatePlaceholder).toBe('Gate 1');
    });

    it('should toggle company selection', () => {
      const item = { id: 1, companyName: 'Company 1' };
      const event = new Event('click');

      component.toggleSelection(item, 'company', event);

      expect(component.selectedCompany).toContain(item);
      expect(component.companyPlaceholder).toBe('Company 1');
    });

    it('should toggle status selection', () => {
      const item = { name: 'Approved' };
      const event = new Event('click');

      component.toggleSelection(item, 'status', event);

      expect(component.selectedStatus).toContain(item);
      expect(component.statusPlaceholder).toBe('Approved');
    });

    it('should toggle responsible selection', () => {
      const item = { id: 1, User: { email: '<EMAIL>' } };
      const event = new Event('click');

      component.toggleSelection(item, 'responsible', event);

      expect(component.selectedResponsible).toContain(item);
      expect(component.responsiblePlaceholder).toBe('<EMAIL>');
    });

    it('should toggle define selection', () => {
      const item = { id: 1, DFOW: 'DFOW 1' };
      const event = new Event('click');

      component.toggleSelection(item, 'define', event);

      expect(component.selectedDefine).toContain(item);
      expect(component.definePlaceholder).toBe('DFOW 1');
    });

    it('should handle multiple selections with placeholder update', () => {
      const item1 = { id: 1, equipmentName: 'Equipment 1' };
      const item2 = { id: 2, equipmentName: 'Equipment 2' };
      const event = new Event('click');

      component.toggleSelection(item1, 'equipment', event);
      component.toggleSelection(item2, 'equipment', event);

      expect(component.selectedEquipment).toHaveLength(2);
      expect(component.equipmentPlaceholder).toBe('Equipment 1 + 1 more');
    });

    it('should remove item when already selected', () => {
      const item = { id: 1, equipmentName: 'Equipment 1' };
      const event = new Event('click');

      component.selectedEquipment = [item];
      component.toggleSelection(item, 'equipment', event);

      expect(component.selectedEquipment).toHaveLength(0);
      expect(component.equipmentPlaceholder).toBe('Select Equipment');
    });

    it('should check if item is selected', () => {
      const item = { id: 1, equipmentName: 'Equipment 1' };
      component.selectedEquipment = [item];

      const result = component.isSelected(item, 'equipment');

      expect(result).toBe(true);
    });

    it('should check if status item is selected', () => {
      const item = { name: 'Approved' };
      component.selectedStatus = [item];

      const result = component.isSelected(item, 'status');

      expect(result).toBe(true);
    });

    it('should return false for unselected item', () => {
      const item = { id: 1, equipmentName: 'Equipment 1' };

      const result = component.isSelected(item, 'equipment');

      expect(result).toBe(false);
    });

    it('should handle generic selection for non-status types', () => {
      const item = { id: 1, equipmentName: 'Equipment 1' };
      const selectedIds = [];

      const result = component.handleGenericSelection(item, selectedIds, 'equipment');

      expect(result).toContain(1);
    });

    it('should handle generic selection for status type', () => {
      const item = { name: 'Approved' };
      const selectedIds = [];

      const result = component.handleGenericSelection(item, selectedIds, 'status');

      expect(result).toContain('Approved');
    });

    it('should remove item from generic selection', () => {
      const item = { id: 1, equipmentName: 'Equipment 1' };
      const selectedIds = [1];

      const result = component.handleGenericSelection(item, selectedIds, 'equipment');

      expect(result).not.toContain(1);
    });
  });

  describe('Export Functionality', () => {
    beforeEach(() => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.pageSize = 25;
      component.pageNo = 1;
    });

    it('should handle PDF export successfully', () => {
      component.exportForm.patchValue({
        reportName: 'Test Report',
        reportType: 'PDF'
      });
      component.reportType = 'PDF';
      jest.spyOn(component, 'cancelexport');

      component.export();

      expect(reportsServiceSpy.exportDeliveryRequest).toHaveBeenCalled();
      expect(component.exportSubmitted).toBe(true);
      expect(component.formSubmitted).toBe(true);
    });

    it('should handle CSV export successfully', () => {
      component.exportForm.patchValue({
        reportName: 'Test Report',
        reportType: 'CSV'
      });
      component.reportType = 'CSV';

      component.export();

      expect(reportsServiceSpy.exportDeliveryRequest).toHaveBeenCalled();
    });

    it('should handle EXCEL export successfully', () => {
      component.exportForm.patchValue({
        reportName: 'Test Report',
        reportType: 'EXCEL'
      });
      component.reportType = 'EXCEL';

      component.export();

      expect(reportsServiceSpy.exportDeliveryRequestInExcelFormat).toHaveBeenCalled();
    });

    it('should not export if form is invalid', () => {
      component.exportForm.patchValue({
        reportName: '',
        reportType: ''
      });

      component.export();

      expect(component.exportSubmitted).toBe(false);
      expect(reportsServiceSpy.exportDeliveryRequest).not.toHaveBeenCalled();
    });

    it('should handle sort column adjustment for export', () => {
      component.sort = 'DESC';
      component.sortColumn = ' id';
      component.exportForm.patchValue({
        reportName: 'Test Report',
        reportType: 'PDF'
      });

      component.export();

      expect(component.sort).toBe('ASC');
      expect(component.sortColumn).toBe('deliveryStart');
    });

    it('should cancel export correctly', () => {
      component.modalRef = { hide: jest.fn() } as any;
      component.exportSubmitted = true;

      component.cancelexport();

      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(component.exportSubmitted).toBe(false);
      expect(component.exportForm.get('reportName').value).toBe('Delivery Report');
      expect(component.exportForm.get('reportType').value).toBe('PDF');
    });
  });

  describe('Table Operations', () => {
    it('should handle input change for table headers', () => {
      // Mock DOM element
      const mockCheckbox = document.createElement('input');
      mockCheckbox.type = 'checkbox';
      mockCheckbox.id = 'equipment';
      mockCheckbox.checked = true;
      document.body.appendChild(mockCheckbox);

      component.handleInputChange('equipment');

      expect(component.tableHeaders.equipment.isActive).toBe(true);

      // Cleanup
      document.body.removeChild(mockCheckbox);
    });

    it('should not deactivate header if only one active', () => {
      // Mock DOM element
      const mockCheckbox = document.createElement('input');
      mockCheckbox.type = 'checkbox';
      mockCheckbox.id = 'id';
      mockCheckbox.checked = false;
      document.body.appendChild(mockCheckbox);

      // Set only one active header
      component.activeHeaders = [component.tableHeaders.id];

      component.handleInputChange('id');

      expect(component.tableHeaders.id.isActive).toBe(true);

      // Cleanup
      document.body.removeChild(mockCheckbox);
    });

    it('should select all headers', () => {
      // Mock DOM elements
      const mockSelectAllCheckbox = document.createElement('input');
      mockSelectAllCheckbox.type = 'checkbox';
      mockSelectAllCheckbox.id = 'selectAll';
      mockSelectAllCheckbox.checked = true;
      document.body.appendChild(mockSelectAllCheckbox);

      Object.keys(component.tableHeaders).forEach(key => {
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.id = key;
        document.body.appendChild(checkbox);
      });

      component.selectall('selectAll');

      Object.keys(component.tableHeaders).forEach(key => {
        expect(component.tableHeaders[key].isActive).toBe(true);
        document.body.removeChild(document.getElementById(key));
      });

      // Cleanup
      document.body.removeChild(mockSelectAllCheckbox);
    });

    it('should unselect all headers except required ones', () => {
      // Mock DOM elements
      const mockSelectAllCheckbox = document.createElement('input');
      mockSelectAllCheckbox.type = 'checkbox';
      mockSelectAllCheckbox.id = 'selectAll';
      mockSelectAllCheckbox.checked = false;
      document.body.appendChild(mockSelectAllCheckbox);

      Object.keys(component.tableHeaders).forEach(key => {
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.id = key;
        document.body.appendChild(checkbox);
      });

      component.selectall('selectAll');

      expect(component.tableHeaders.id.isActive).toBe(true);
      expect(component.tableHeaders.description.isActive).toBe(true);
      expect(component.tableHeaders.date.isActive).toBe(true);
      expect(component.tableHeaders.equipment.isActive).toBe(false);

      Object.keys(component.tableHeaders).forEach(key => {
        document.body.removeChild(document.getElementById(key));
      });

      // Cleanup
      document.body.removeChild(mockSelectAllCheckbox);
    });
  });

  describe('Keyboard Event Handling', () => {
    it('should handle toggle keydown with Enter key', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'sortByField');

      component.handleToggleKeydown(event, 'description', 'ASC');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.sortByField).toHaveBeenCalledWith('description', 'ASC');
    });

    it('should handle toggle keydown with Space key', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'sortByField');

      component.handleToggleKeydown(event, 'date', 'DESC');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.sortByField).toHaveBeenCalledWith('date', 'DESC');
    });

    it('should handle down keydown with Enter key', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'toggleDropdown');

      component.handleDownKeydown(event, 'equipment');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.toggleDropdown).toHaveBeenCalledWith('equipment');
    });

    it('should handle down keydown with Space key', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'toggleDropdown');

      component.handleDownKeydown(event, 'status');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.toggleDropdown).toHaveBeenCalledWith('status');
    });

    it('should not handle other keys', () => {
      const event = new KeyboardEvent('keydown', { key: 'Tab' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'sortByField');

      component.handleToggleKeydown(event, 'description', 'ASC');

      expect(event.preventDefault).not.toHaveBeenCalled();
      expect(component.sortByField).not.toHaveBeenCalled();
    });
  });

  describe('Location Filtering', () => {
    beforeEach(() => {
      component.locationList = [
        { id: 1, locationPath: 'Location 1' },
        { id: 2, locationPath: 'Location 2' },
        { id: 3, locationPath: 'Another Place' }
      ];
      component.filteredLocationList = [...component.locationList];
    });

    it('should filter location list based on search', () => {
      const event = { target: { value: 'Location' } };

      component.changeLocationFilterOptionList(event);

      expect(component.filteredLocationList).toHaveLength(2);
      expect(component.filteredLocationList[0].locationPath).toBe('Location 1');
      expect(component.filteredLocationList[1].locationPath).toBe('Location 2');
    });

    it('should show all locations when search is empty', () => {
      const event = { target: { value: '' } };

      component.changeLocationFilterOptionList(event);

      expect(component.filteredLocationList).toEqual(component.locationList);
    });

    it('should handle case insensitive search', () => {
      const event = { target: { value: 'location' } };

      component.changeLocationFilterOptionList(event);

      expect(component.filteredLocationList).toHaveLength(2);
    });
  });

  describe('API Methods', () => {
    beforeEach(() => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
    });

    it('should get delivery reports successfully', () => {
      const mockResponse = {
        data: {
          rows: [{ id: 1, description: 'Test Delivery' }],
          count: 1
        }
      };
      reportsServiceSpy.deliveryReports.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getOverAllGateForNdrGrid');

      component.getDeliveryReports();

      expect(reportsServiceSpy.deliveryReports).toHaveBeenCalled();
      expect(component.deliveryList).toEqual(mockResponse.data.rows);
      expect(component.totalDeliveryRequestCount).toBe(mockResponse.data.count);
      expect(component.loader).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.getOverAllGateForNdrGrid).toHaveBeenCalled();
    });

    it('should get gate list successfully', () => {
      const mockResponse = { data: [{ id: 1, gateName: 'Gate 1' }] };
      projectServiceSpy.gateList.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getOverAllEquipmentForNdrGrid');

      component.getOverAllGateForNdrGrid();

      expect(projectServiceSpy.gateList).toHaveBeenCalledWith(
        expect.objectContaining({
          ProjectId: '123',
          ParentCompanyId: '456'
        }),
        { isFilter: true, showActivatedAlone: true }
      );
      expect(component.gateList).toEqual(mockResponse.data);
      expect(component.getOverAllEquipmentForNdrGrid).toHaveBeenCalled();
    });

    it('should get equipment list successfully', () => {
      const mockResponse = { data: [{ id: 1, equipmentName: 'Equipment 1' }] };
      projectServiceSpy.listEquipment.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getDefinableForNdrGrid');

      component.getOverAllEquipmentForNdrGrid();

      expect(projectServiceSpy.listEquipment).toHaveBeenCalledWith(
        expect.objectContaining({
          ProjectId: '123',
          ParentCompanyId: '456'
        }),
        { isFilter: true, showActivatedAlone: true }
      );
      expect(component.equipmentList).toEqual(mockResponse.data);
      expect(component.getDefinableForNdrGrid).toHaveBeenCalled();
    });

    it('should get definable work list successfully', () => {
      const mockResponse = { data: [{ id: 1, DFOW: 'DFOW 1' }] };
      projectServiceSpy.getDefinableWork.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getCompaniesForNdrGrid');

      component.getDefinableForNdrGrid();

      expect(projectServiceSpy.getDefinableWork).toHaveBeenCalledWith(
        expect.objectContaining({
          ProjectId: '123',
          ParentCompanyId: '456'
        })
      );
      expect(component.defineList).toEqual(mockResponse.data);
      expect(component.getCompaniesForNdrGrid).toHaveBeenCalled();
    });

    it('should get companies list successfully', () => {
      const mockResponse = { data: [{ id: 1, companyName: 'Company 1' }] };
      projectServiceSpy.getCompanies.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getLocationsForNdrGrid');

      component.getCompaniesForNdrGrid();

      expect(projectServiceSpy.getCompanies).toHaveBeenCalledWith(
        expect.objectContaining({
          ProjectId: '123',
          ParentCompanyId: '456'
        })
      );
      expect(component.companyList).toEqual(mockResponse.data);
      expect(component.getLocationsForNdrGrid).toHaveBeenCalled();
    });

    it('should get locations list successfully', () => {
      const mockResponse = { data: [{ id: 1, locationPath: 'Location 1' }] };
      projectServiceSpy.getLocations.mockReturnValue(of(mockResponse));

      component.getLocationsForNdrGrid();

      expect(projectServiceSpy.getLocations).toHaveBeenCalledWith(
        expect.objectContaining({
          ProjectId: '123',
          ParentCompanyId: '456'
        })
      );
      expect(component.locationList).toEqual(mockResponse.data);
      expect(component.filteredLocationList).toEqual(mockResponse.data);
    });

    it('should get members list successfully', () => {
      const mockResponse = {
        data: [{ id: 1, User: { email: '<EMAIL>' } }]
      };
      projectServiceSpy.listAllMember.mockReturnValue(of(mockResponse));

      component.getMembers();

      expect(projectServiceSpy.listAllMember).toHaveBeenCalledWith(
        expect.objectContaining({
          ProjectId: '123',
          ParentCompanyId: '456'
        })
      );
      expect(component.memberList).toEqual(mockResponse.data);
      expect(component.updatedMemberList).toEqual([
        { id: 1, User: { email: '<EMAIL>' }, UserEmail: '<EMAIL>' }
      ]);
    });
  });

  describe('Selection Option Methods', () => {
    beforeEach(() => {
      component.filterForm.patchValue({
        equipmentFilter: [],
        gateFilter: [],
        companyFilter: [],
        locationFilter: [],
        memberFilter: [],
        statusFilter: [],
        defineFilter: []
      });
    });

    it('should handle selectOption for equipment', () => {
      const item = { id: 1, equipmentName: 'Equipment 1' };
      const event = new Event('click');

      component.selectOption(item, 'equipment', event);

      expect(component.filterForm.get('equipmentFilter').value).toContain(1);
    });

    it('should handle selectOption for status', () => {
      const item = { name: 'Approved' };
      const event = new Event('click');

      component.selectOption(item, 'status', event);

      expect(component.filterForm.get('statusFilter').value).toContain('Approved');
    });

    it('should handle selectOption for responsible (member)', () => {
      const item = { id: 1, User: { email: '<EMAIL>' } };
      const event = new Event('click');

      component.selectOption(item, 'responsible', event);

      expect(component.filterForm.get('memberFilter').value).toContain(1);
    });

    it('should handle selectOption with null event', () => {
      const item = { id: 1, equipmentName: 'Equipment 1' };

      component.selectOption(item, 'equipment', null);

      expect(component.filterForm.get('equipmentFilter').value).toContain(1);
    });

    it('should handle selectOption with non-array form value', () => {
      const item = { id: 1, equipmentName: 'Equipment 1' };
      const event = new Event('click');
      component.filterForm.get('equipmentFilter').setValue('not-an-array');

      component.selectOption(item, 'equipment', event);

      expect(component.filterForm.get('equipmentFilter').value).toContain(1);
    });

    it('should return early for invalid form control', () => {
      const item = { id: 1, equipmentName: 'Equipment 1' };
      const event = new Event('click');

      component.selectOption(item, 'invalid-type', event);

      // Should not throw error and return early
      expect(true).toBe(true);
    });
  });

  describe('Update Placeholder Methods', () => {
    it('should update placeholder for responsible with multiple selections', () => {
      const selectedList = [
        { User: { email: '<EMAIL>' } },
        { User: { email: '<EMAIL>' } }
      ];

      component.updatePlaceholder('responsible', selectedList, 'User.email', 'Select Responsible Person');

      expect(component.responsiblePlaceholder).toBe('<EMAIL> + 1 more');
    });

    it('should update placeholder for status with multiple selections', () => {
      const selectedList = [
        { name: 'Approved' },
        { name: 'Pending' }
      ];

      component.updatePlaceholder('status', selectedList, 'name', 'Select Status');

      expect(component.statusPlaceholder).toBe('Approved + 1 more');
    });

    it('should update placeholder for single responsible selection', () => {
      const selectedList = [{ User: { email: '<EMAIL>' } }];

      component.updatePlaceholder('responsible', selectedList, 'User.email', 'Select Responsible Person');

      expect(component.responsiblePlaceholder).toBe('<EMAIL>');
    });

    it('should update placeholder for single status selection', () => {
      const selectedList = [{ name: 'Approved' }];

      component.updatePlaceholder('status', selectedList, 'name', 'Select Status');

      expect(component.statusPlaceholder).toBe('Approved');
    });
  });
});
