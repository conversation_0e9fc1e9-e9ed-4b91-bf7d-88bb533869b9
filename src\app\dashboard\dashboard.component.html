<ngx-loading [show]="loader" [config]="{ backdropBorderRadius: '3px' }"> </ngx-loading>

<section class="page-section">
  <div class="page-inner-content">
    <div class="top-header mb-3 pt-md-40px">
      <h1 class="fs26 fw-bold cairo-regular">Dashboard</h1>
    </div>
    <div class="row">
      <div
        class="col-md-3 col-sm-3 mb-2 mb-sm-4 mb-md-0"
        (click)="redirect('delivery-request')" (keydown)="handleDownKeydown($event, 'delivery-request','redirect')"
        style="cursor: pointer"
      >
        <div class="card dashboard-card card-style-dashboard br-10 border-lightorange">
          <div class="card-body row">
            <div class="d-flex px-0">
              <img
                src="./assets/images/delivery-dashboard.svg"
                width="40px"
                height="40px"
                class="c-pointer mb-2 ml5"
                alt="truck"
              />
              <div class="ms-2">
                <p class="color-grey31 fs12 fw600 cairo-regular mb-2">Deliveries</p>
                <h2 class="fs18 mb-0 color-grey32 fw-bold cairo-regular">
                  {{ dashData.deliveryRequest }}
                </h2>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="col-md-3 col-sm-3 mb-2 mb-sm-4 mb-md-0"
        (click)="redirect('companies')"  (keydown)="handleDownKeydown($event, 'companies','redirect')"
        style="cursor: pointer"
      >
        <div class="card dashboard-card card-style-dashboard br-10 border-lightorange">
          <div class="card-body row">
            <div class="d-flex px-0">
              <img
                src="./assets/images/dashboard_company_icon.svg"
                width="40px"
                height="40px"
                class="c-pointer mb-2 ml5"
                alt="company"
              />
              <div class="ms-2">
                <p class="color-grey31 fs12 fw600 mb-2 cairo-regular">Companies</p>
                <h2 class="fs18 mb-0 color-grey32 fw-bold cairo-regular">
                  {{ dashData.company }}
                </h2>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="col-md-3 col-sm-3 mb-2 mb-sm-4 mb-md-0"
        (click)="redirect('equipments')" (keydown)="handleDownKeydown($event, 'equipments','redirect')"
        style="cursor: pointer"
      >
        <div class="card dashboard-card card-style-dashboard br-10 border-lightorange">
          <div class="card-body row">
            <div class="d-flex px-0">
              <img
                src="./assets/images/dashboard_equipment_icon.svg"
                width="40px"
                height="40px"
                class="c-pointer mb-2 ml5"
                alt="teammembers"
              />
              <div class="ms-2">
                <p class="color-grey31 fs12 fw600 mb-2 cairo-regular">Equipment</p>
                <h2 class="fs18 mb-0 color-grey32 fw-bold cairo-regular">
                  {{ dashData.equipment }}
                </h2>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="col-md-3 col-sm-3 mb-2 mb-sm-4 mb-md-0"
        (click)="redirect('members')" (keydown)="handleDownKeydown($event, 'members','redirect')"
        style="cursor: pointer"
      >
        <div class="card dashboard-card card-style-dashboard br-10 border-lightorange">
          <div class="card-body row">
            <div class="d-flex px-0">
              <img
                src="./assets/images/members-dashboard.svg"
                width="40px"
                height="40px"
                class="c-pointer mb-2 ml5"
                alt="crane"
              />
              <div class="ms-2">
                <p class="color-grey31 fs12 fw600 mb-2 cairo-regular">Members</p>
                <h2 class="fs18 color-grey32 fw-bold cairo-regular mb-0">
                  {{ dashData.member }}
                </h2>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row dashboard-plan mx-1 my-4">
      <div class="col-lg-4 col-md-12 dashboard-card white-border br-10 bg-white">

        <div class="row mt-md-2 bg-white position-relative m-0" *ngIf="!fileUploaded">
       <div
          class="col-md-12 col-12 px-0 plan-content-bg plan-no-content-bg align-items-center text-center"
          *ngIf="isAdmin && isResponse"
        >
          <p class="fs12 m-0">
            <span class="d-block">No site plan available.</span>
            <span>Do you want to show the site plan for all your members</span>
          </p>
          <button
            type="submit"
            class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem mt-2"
            (click)="navigateProjectSettings()"
          >
            Click Here
          </button>
        </div>
          <div
            class="col-md-12 col-12 px-0 plan-content-bg plan-no-content-bg d-flex flex-column align-items-center text-center"
            *ngIf="!isAdmin && isResponse"
          >
            <p class="fs12">
              As of now, there hasn't been any update to the site plan by the admin
            </p>
          </div>
        </div>
        <div class="row position-relative pb-2 bg-white mx-0" *ngIf="fileUploaded">
          <div
            class="col-md-12 col-12 px-0 plan-content-bg plan-no-content-bg d-flex flex-column align-items-center text-center"
            *ngIf="sitePlanUploadStatus === 'failed'"
          >
            <p class="fs12">Site Plan updation failed</p>
          </div>
          <div
            class="col-md-12 col-12 px-0 plan-content-bg plan-no-content-bg d-flex flex-column align-items-center text-center"
            *ngIf="sitePlanUploadStatus === 'uploading'"
          >
            <p class="fs12">
              Kindly hold on for a moment as we're currently updating the site plan
            </p>
          </div>
          <div class="col-md-10 col-10 px-0" *ngIf="sitePlanUploadStatus === 'uploaded'">
            <div class="row">
              <h1 class="col-md-3 color-greyd fs12 fw700 py-2 px-2">Site Plan</h1>
              <div class="col-md-8 px-0 text-start d-flex my-1" *ngIf="arrayValues.length > 1 && isPdfPlan">
                <img
                  class="cursor-pointer h20 w20 my-1"
                  src="../../assets/images/arrow_back.svg"
                  alt="backarrow"
                  (click)="getPreviousValue()"  (keydown)="handleDownKeydown($event, '','previous')"
                />
                <div class="siteplan-pagenumbers">
                  <input
                  class="form-control fs12 color-grey8"
                  placeholder=""
                  (input)="getSitePlanAtIndex($event.target.value)"
                  type="text"
                  (keypress)="onlyNumbers($event)"
                  (focusout)="onFocusOut($event)"
                  [(ngModel)]="pageNo"
                />
                <span class="px-1">-</span>
                  <input class="form-control fs12 color-grey8"
                  placeholder=""
                  (input)="getSitePlanAtIndex($event.target.value)"
                  type="text"
                  [(ngModel)]="arrayValues.length" disabled/>
                </div>
                <img
                  class="cursor-pointer h20 w20 my-1"
                  src="../../assets/images/arrow_forward.svg"
                  alt="forwardarrow"
                  (click)="getNextValue()"  (keydown)="handleDownKeydown($event, '','next')"
                />
              </div>

            </div>
            <div class="plan-content-bg notexpanded-contentbg image-container d-flex justify-content-center border-0">
              <div class="spinner-border m-auto" *ngIf="sitePlanLoader"></div>
              <img *ngIf="!sitePlanLoader" [src]="selectedSitePlan" alt="data" class="w300 h260" #sitePlanImg />
            </div>
          </div>

          <div class="col-md-2 col-2 plans-icons-style pe-0 d-md-block d-none" *ngIf="sitePlanUploadStatus === 'uploaded'">
            <div class="mb-1 mt-3">
              <div>
                <img
                  src="../../assets/images/resize-icon.svg"
                  alt="resize"
                  (click)="toggleImageSize()"  (keydown)="handleDownKeydown($event, '','toggle')"
                  class="cursor-pointer"
                />
              </div>
              <div class="mt10">
                <img
                  src="../../assets/images/download-dashicon.svg"
                  alt="download"
                  (click)="downloadFile()"  (keydown)="handleDownKeydown($event, '','download')"
                  class="cursor-pointer"
                />
              </div>

              <div class="mt10">
                <img
                  src="../../assets/images/download-copy.svg"
                  alt="copy"
                  (click)="copyLink(logisticPlan)"  (keydown)="handleDownKeydown($event, logisticPlan,'copy')"
                  class="cursor-pointer"
                />
              </div>
            </div>
            <div class="">
              <img
                src="../../assets/images/logisticPlus.svg"
                alt="plus"
                class="w27 mt30 cursor-pointer"
                (click)="zoomIn()" (keydown)="handleDownKeydown($event, '','zoomin')"
              />
            </div>
            <div>
              <img
                src="../../assets/images/logisticMinus.svg"
                alt="minus"
                class="w27 mt7 cursor-pointer"
                (click)="zoomOut()"  (keydown)="handleDownKeydown($event, '','zoomout')"
              />
            </div>

          </div>
        </div>
        <div
          class="row mt-md-2 bg-white plan-content-bg-img desktop-thumbnail-images"
          *ngIf="isImageExpanded"
        >
          <div class="col-md-11 col-11 px-0">
            <div class="plan-content-bg bg-white">
              <div class="row">
                <h1 class="color-greyd fs12 fw700 py-2 px-2 col-md-2 mx-3">Site Plan</h1>
               <div
                  class="col-md-4 px-0 text-start my-auto d-flex"
                  *ngIf="arrayValues.length > 1 && isPdfPlan">
                  <img
                    class="cursor-pointer arrow-expandedview my-1"
                    src="../../assets/images/arrow_back.svg"
                    alt="backarrow"
                    (click)="getPreviousValue()"  (keydown)="handleDownKeydown($event, '','previous')"/>
                  <div class="d-flex siteplan-pagenumbers">
                    <input
                    class="form-control fs12 color-grey8"
                    placeholder=""
                    (input)="getSitePlanAtIndex($event.target.value)"
                    type="text"
                    (keypress)="onlyNumbers($event)"
                    (focusout)="onFocusOut($event)"
                    [(ngModel)]="pageNo"/>
                  <span class="px-1">-</span>
                    <input class="form-control fs12 color-grey8"
                    placeholder=""
                    (input)="getSitePlanAtIndex($event.target.value)"
                    type="text"
                    [(ngModel)]="arrayValues.length" disabled/>
                  </div>
                  <img
                    class="cursor-pointer arrow-expandedview my-1"
                    src="../../assets/images/arrow_forward.svg"
                    alt="forwardarrow"
                    (click)="getNextValue()" (keydown)="handleDownKeydown($event, '','next')"/>
                </div>


              </div>
              <div class="desktop-thumbnail-innerimage image-container2 d-flex justify-content-center">
                <div class="spinner-border m-auto" *ngIf="sitePlanLoader"></div>
                <img *ngIf="!sitePlanLoader" class="p-3" [src]="selectedSitePlan" alt="data" />
              </div>
            </div>
          </div>

          <div class="col-md-1 col-1 plans-icons-style">
            <div class="mb-2 mt30">
              <div>
                <img
                  src="../../assets/images/resize-icon.svg"
                  alt="resize"
                  (click)="toggleImageSize()" (keydown)="handleDownKeydown($event, '','toggle')"
                  class="cursor-pointer"
                />
              </div>
              <div class="mt10">
                <img
                  src="../../assets/images/download-dashicon.svg"
                  alt="download"
                  (click)="downloadFile()" (keydown)="handleDownKeydown($event, '','download')"
                  class="cursor-pointer"
                />
              </div>

              <div class="mt10">
                <img
                  src="../../assets/images/download-copy.svg"
                  alt="copy"
                  (click)="copyLink(logisticPlan)" (keydown)="handleDownKeydown($event, logisticPlan,'copy')"
                  class="cursor-pointer"
                />
              </div>
            </div>
            <div class="mt-3">
              <img
                src="../../assets/images/logisticPlus.svg"
                alt="plus"
                class="w27 mt30 cursor-pointer"
                (click)="zoomIn1()"  (keydown)="handleDownKeydown($event, '','zoomin1')"
              />
            </div>
            <div>
              <img
                src="../../assets/images/logisticMinus.svg"
                alt="minus"
                class="w27 mt7 cursor-pointer"
                (click)="zoomOut2()" (keydown)="handleDownKeydown($event, '','zoomout2')"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-8 col-md-12 mt-md-4 mt-lg-0 pe-0">
        <div class="card dashboard-card white-border br-10 mt-0 py-2">
          <div class="card-header d-md-flex d-block border-0 px-0 pb-0 mb-2">
            <div class="d-flex fs12 fw700 color-grey7">
              <div class="ms-4">
                <span class="dots delivery-dots me-2"></span>
                Delivery
              </div>
              <div class="ms-4"><span class="dots crane-dots me-2"></span>Crane</div>
              <div class="ms-4"><span class="dots concrete-dots me-2"></span>Concrete</div>
              <div class="ms-4"><span class="dots inspection-dots me-2"></span>Inspection</div>
            </div>
            <div class="col-md-4 col-6 ms-auto me-3 text-end">
              <select
                class="custom-select color-grey31 fs12 mobile-view-selectdashboard w185 custom-arrow"
                id="exampleFormControlSelect1"
                (change)="changeYear($event.target.value)"
                [(ngModel)]="yearValue"
              >
                <option value="previousyear">Previous Year</option>
                <option value="nextyear">Next Year</option>
                <option value="currentyear">Current Year</option>
              </select>
            </div>
          </div>
          <div class="card-body p-1">
            <app-bar-chart [baroptions]="barChartOne"></app-bar-chart>
          </div>
        </div>
      </div>
    </div>
    <div class="row mt-md-4 mt-2">
      <div class="col-12">
        <div class="card dashboard-card white-border br-10">
          <div class="card-body">
            <p class="color-grey31 fs12 fw600">Upcoming List</p>
            <div class="table-responsive rounded tab-grid">
              <table class="table table-custom mb-0" aria-describedby="dashboard-table">
                <thead>
                  <th scope="col" resizable></th>
                  <th scope="col" resizable>ID</th>
                  <th scope="col" resizable>Description</th>
                  <th scope="col" resizable>Responsible Company</th>
                  <th scope="col" resizable>Date and Time</th>
                </thead>
                <tbody *ngIf="upcomingList?.length > 0">
                  <tr
                    *ngFor="let data of upcomingList; let i = index"
                    class="c-pointer"
                    (click)="openCraneDetailView(data)" (keydown)="handleDownKeydown($event, data,'openview')"
                  >
                    <td>
                      <img
                        src="./assets/images/cranelift.png"
                        alt="cranelift"
                        *ngIf="
                          data.requestType === 'deliveryRequestWithCrane' ||
                          data.requestType === 'craneRequest'
                        "
                      />
                      <img
                        src="./assets/images/bluetruck.svg"
                        alt="bluetruck"
                        *ngIf="data.requestType === 'deliveryRequest'"
                      />
                      <img
                        src="./assets/images/concrete-request-icon.svg"
                        alt="bluetruck"
                        *ngIf="data.requestType === 'concreteRequest'"
                      />
                     <img
                      src="./assets/images/inspection-icon.svg"
                      alt="inspection"
                      *ngIf="
                      data.requestType === 'inspectionRequest' ||
                      data.requestType === 'inspectionRequestWithCrane'"
                      />
                    </td>
                    <td
                      *ngIf="
                        data.requestType === 'deliveryRequest' ||
                        data.requestType === 'deliveryRequestWithCrane'
                      "
                    >
                      {{ data.DeliveryId }}
                    </td>
                    <td *ngIf="data.requestType === 'craneRequest'">
                      {{ data.CraneRequestId }}
                    </td>
                    <td *ngIf="data.requestType === 'concreteRequest'">
                      {{ data.ConcreteRequestId }}
                    </td>
                    <td
                      *ngIf="
                        data.requestType === 'inspectionRequest' ||
                        data.requestType === 'inspectionRequestWithCrane'
                      "
                    >
                      {{ data.InspectionId }}
                    </td>
                    <td>
                      {{ data.description }}
                    </td>
                    <td *ngIf="data?.companyDetails && data.companyDetails?.length > 0">
                      <div *ngIf="data.companyDetails && data.companyDetails?.length > 0">
                        <p
                          class="color-grey15 fs12 mb-2 fw500"
                          *ngFor="let item of data.companyDetails"
                        >
                          {{ item?.Company?.companyName }}
                        </p>
                      </div>
                    </td>
                    <td
                      *ngIf="
                        data?.concreteSupplierDetails && data.concreteSupplierDetails?.length > 0
                      "
                    >
                      <div
                        *ngIf="
                          data.concreteSupplierDetails && data.concreteSupplierDetails?.length > 0
                        "
                      >
                        <p
                          class="color-grey15 fs12 mb-2 fw500"
                          *ngFor="let item of data.concreteSupplierDetails"
                        >
                          {{ item?.Company?.companyName }}
                        </p>
                      </div>
                    </td>
                    <td
                      *ngIf="
                        data.requestType === 'deliveryRequest' ||
                        data.requestType === 'deliveryRequestWithCrane'
                      "
                    >
                      {{ data.deliveryStart | date : 'medium' }}
                    </td>
                    <td *ngIf="data.requestType === 'craneRequest'">
                      {{ data.craneDeliveryStart | date : 'medium' }}
                    </td>
                    <td *ngIf="data.requestType === 'concreteRequest'">
                      {{ data.concretePlacementStart | date : 'medium' }}
                    </td>
                    <td
                      *ngIf="
                        data.requestType === 'inspectionRequest' ||
                        data.requestType === 'inspectionRequestWithCrane'
                      "
                    >
                      {{ data.inspectionStart | date : 'medium' }}
                    </td>
                  </tr>
                </tbody>
                <tr *ngIf="upcomingListLoader == true" id="loading1">
                  <td colspan="5" class="text-center">
                    <div class="fs18 fw-bold cairo-regular my-5 text-black">Loading...</div>
                  </td>
                </tr>
                <tr *ngIf="upcomingListLoader == false && upcomingList?.length == 0">
                  <td colspan="5" class="text-center">
                    <div class="fs18 fw-bold cairo-regular my-5 text-black">No Records Found</div>
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
