import { ComponentFixture, TestBed } from '@angular/core/testing';
import { GuestuserCranecalendarComponent } from './guestuser-cranecalendar.component';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ProjectService } from '../../../services/profile/project.service';
import { ProjectSharingService } from '../../../services';
import { UntypedFormBuilder } from '@angular/forms';
import { CalendarService } from '../../../services/profile/calendar.service';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { Title } from '@angular/platform-browser';
import { FullCalendarComponent } from '@fullcalendar/angular';
import { of, throwError } from 'rxjs';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import moment from 'moment';

describe('GuestuserCranecalendarComponent', () => {
  let component: GuestuserCranecalendarComponent;
  let fixture: ComponentFixture<GuestuserCranecalendarComponent>;
  let projectSharingService: jest.Mocked<ProjectSharingService>;
  let modalService: jest.Mocked<BsModalService>;
  let toastrService: jest.Mocked<ToastrService>;

  const mockProjectSharingService = {
    checkIsGuest: jest.fn().mockReturnValue(of({ data: { isGuestUser: false, id: 123 } })),
    guestGetDefinableWork: jest.fn().mockReturnValue(of({ data: [] })),
    guestGetCompanies: jest.fn().mockReturnValue(of({ data: [] })),
    guestGateList: jest.fn().mockReturnValue(of({ data: [] })),
    guestListCraneEquipment: jest.fn().mockReturnValue(of({ data: { rows: [] } })),
    guestGetLocations: jest.fn().mockReturnValue(of({ data: [] })),
    listAllMember: jest.fn().mockReturnValue(of({ data: [] })),
    guestGetEquipmentCraneRequest: jest.fn().mockReturnValue(of({
      data: {
        memberDetails: [],
        gateDetails: []
      }
    })),
    guestGetNDRData: jest.fn().mockReturnValue(of({
      data: {
        memberDetails: [],
        gateDetails: []
      }
    })),
    getDeliveryRequestWithCraneEquipmentType: jest.fn().mockReturnValue(of({
      data: [],
      lastId: { CraneRequestId: 1 },
      statusData: {
        statusColorCode: JSON.stringify([
          { status: 'approved', backgroundColor: '#28a745', fontColor: '#fff' },
          { status: 'pending', backgroundColor: '#ffc107', fontColor: '#000' },
          { status: 'delivered', backgroundColor: '#17a2b8', fontColor: '#fff' },
          { status: 'rejected', backgroundColor: '#dc3545', fontColor: '#fff' },
          { status: 'expired', backgroundColor: '#6c757d', fontColor: '#fff' }
        ]),
        useTextColorAsLegend: 'false',
        isDefaultColor: 'false'
      },
      cardData: {
        craneCard: JSON.stringify([
          { label: 'Description', line: 1, selected: true },
          { label: 'Responsible Company', line: 2, selected: true }
        ])
      }
    }))
  };

  const mockModalService = {
    show: jest.fn().mockReturnValue({
      content: {
        lastId: null,
        closeBtnName: '',
        seriesOption: 1,
        recurrenceId: null,
        recurrenceEndDate: null
      },
      hide: jest.fn()
    })
  };

  const mockToastrService = {
    error: jest.fn()
  };

  const mockDeliveryService = {
    updatedEditCraneRequestId: jest.fn(),
    updatedDeliveryId: jest.fn(),
    updatedCurrentStatus: jest.fn()
  };

  const mockRouter = {
    navigate: jest.fn()
  };

  const mockCalendarApi = {
    next: jest.fn(),
    prev: jest.fn(),
    prevYear: jest.fn(),
    nextYear: jest.fn(),
    changeView: jest.fn(),
    removeAllEventSources: jest.fn(),
    addEventSource: jest.fn(),
    currentData: {
      dateProfile: {
        activeRange: {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31')
        }
      }
    }
  };

  beforeEach(async () => {
    // Mock localStorage
    const mockLocalStorage = {
      getItem: jest.fn((key) => {
        const mockData = {
          'guestId': btoa('123'),
          'guestEmail': btoa('<EMAIL>'),
          'guestProjectId': btoa('456'),
          'guestParentCompanyId': btoa('789')
        };
        return mockData[key] || null;
      })
    };
    Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

    await TestBed.configureTestingModule({
      declarations: [ GuestuserCranecalendarComponent ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        { provide: BsModalRef, useValue: {} },
        { provide: BsModalService, useValue: mockModalService },
        { provide: ProjectService, useValue: {} },
        { provide: ProjectSharingService, useValue: mockProjectSharingService },
        { provide: UntypedFormBuilder, useValue: new UntypedFormBuilder() },
        { provide: CalendarService, useValue: {} },
        { provide: Router, useValue: mockRouter },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: Title, useValue: { setTitle: jest.fn() } }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(GuestuserCranecalendarComponent);
    component = fixture.componentInstance;

    // Mock calendar component
    component.calendarComponent1 = {
      getApi: jest.fn().mockReturnValue(mockCalendarApi)
    } as any;

    // Mock the methods called in constructor to prevent errors
    jest.spyOn(component, 'checkGuest').mockImplementation(() => {});
    jest.spyOn(component, 'getDeliveryRequestWithCraneEquipmentType').mockImplementation(() => {});

    // Initialize calendarApi
    component.setCalendar();

    projectSharingService = TestBed.inject(ProjectSharingService) as jest.Mocked<ProjectSharingService>;
    modalService = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('checkGuest', () => {
    it('should set isGuestUser to true when response contains guest user data', () => {
      const mockResponse = {
        data: {
          isGuestUser: true,
          id: 123
        }
      };
      mockProjectSharingService.checkIsGuest.mockReturnValue(of(mockResponse));

      component.checkGuest();

      expect(component.isGuestUser).toBe(true);
      expect(component.guestMemberId).toBe(123);
    });

    it('should set isGuestUser to false when response does not contain guest user data', () => {
      const mockResponse = {
        data: null
      };
      mockProjectSharingService.checkIsGuest.mockReturnValue(of(mockResponse));

      component.checkGuest();

      expect(component.isGuestUser).toBe(false);
    });

    it('should handle error and show toastr message', () => {
      // Restore the original method for this test
      jest.restoreAllMocks();
      jest.spyOn(component, 'getDeliveryRequestWithCraneEquipmentType').mockImplementation(() => {});

      mockProjectSharingService.checkIsGuest.mockReturnValue(throwError(() => new Error('Test error')));

      component.checkGuest();

      expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });
  });

  describe('Calendar Navigation', () => {
    it('should navigate to next period', () => {
      component.goNext();
      expect(mockCalendarApi.next).toHaveBeenCalled();
    });

    it('should navigate to previous period', () => {
      component.goPrev();
      expect(mockCalendarApi.prev).toHaveBeenCalled();
    });

    it('should change to month view', () => {
      component.goDayGridMonth();
      expect(mockCalendarApi.changeView).toHaveBeenCalledWith('dayGridMonth');
      expect(component.currentView).toBe('Month');
    });
  });

  describe('Modal Operations', () => {
    it('should not open add crane request modal for guest users', () => {
      component.isGuestUser = true;
      const dateArg = { dateStr: '2024-03-20' };

      component.openAddCraneRequestModal(dateArg);

      expect(mockModalService.show).not.toHaveBeenCalled();
    });

    it('should open add crane request modal for non-guest users', () => {
      component.isGuestUser = false;
      const dateArg = { dateStr: '2024-03-20' };

      component.openAddCraneRequestModal(dateArg);

      expect(mockModalService.show).toHaveBeenCalled();
    });
  });

  describe('Calendar Description', () => {
    it('should show calendar description popup with event data', () => {
      const mockEvent = {
        title: 'Test Event',
        extendedProps: {
          uniqueNumber: '123'
        }
      };
      const mockEventData = {
        description: 'Test Event',
        uniqueNumber: '123'
      };
      component.events = [mockEventData];
      component.deliveryRequestWithCraneEquipmentType = [mockEventData];

      component.calendarDescription({ event: mockEvent });

      expect(component.calendarDescriptionPopup).toBe(true);
      expect(component.viewEventData).toEqual(mockEventData);
    });

    it('should close calendar description', () => {
      component.calendarDescriptionPopup = true;
      component.descriptionPopup = true;
      component.viewEventData = 'some data';

      component.closeCalendarDescription();

      expect(component.calendarDescriptionPopup).toBe(false);
      expect(component.descriptionPopup).toBe(false);
      expect(component.viewEventData).toBe('');
    });
  });

  describe('Component Initialization', () => {
    it('should initialize with correct default values', () => {
      expect(component.events).toEqual([]);
      expect(component.voidSubmitted).toBe(false);
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.descriptionPopup).toBe(false);
      expect(component.calendarDescriptionPopup).toBe(false);
      expect(component.lastId).toBe(0);
      expect(component.loader).toBe(true);
      expect(component.search).toBe('');
      expect(component.filterCount).toBe(0);
      expect(component.showSearchbar).toBe(false);
      expect(component.currentView).toBe('Day');
      expect(component.monthlyEventloader).toBe(true);
      expect(component.allRequestIsOpened).toBe(false);
      expect(component.isGuestUser).toBe(false);
      expect(component.isGuestReq).toBe(false);
    });

    it('should set title and initialize form', () => {
      const titleService = TestBed.inject(Title);
      expect(titleService.setTitle).toHaveBeenCalledWith('Follo - Crane Calendar');
      expect(component.filterForm).toBeDefined();
    });

    it('should decode localStorage values correctly', () => {
      expect(component.id).toBe(123);
      expect(component.email).toBe('<EMAIL>');
      expect(component.ProjectId).toBe(456);
      expect(component.ParentCompanyId).toBe(789);
    });
  });

  describe('Keyboard Event Handling', () => {
    it('should handle Enter key for different actions', () => {
      const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(enterEvent, 'preventDefault');
      jest.spyOn(component, 'clear');
      jest.spyOn(component, 'openFilterModal');
      jest.spyOn(component, 'openIdModal');
      jest.spyOn(component, 'closeCalendarDescription');
      jest.spyOn(component, 'openEditModal');

      // Test 'show' type
      component.handleDownKeydown(enterEvent, 'data', 'item', 'show');
      expect(enterEvent.preventDefault).toHaveBeenCalled();
      expect(component.clear).toHaveBeenCalled();

      // Test 'filter' type
      component.handleDownKeydown(enterEvent, 'data', 'item', 'filter');
      expect(component.openFilterModal).toHaveBeenCalledWith('data');

      // Test 'modal' type
      component.handleDownKeydown(enterEvent, 'data', 'item', 'modal');
      expect(component.openIdModal).toHaveBeenCalledWith('data');

      // Test 'close' type
      component.handleDownKeydown(enterEvent, 'data', 'item', 'close');
      expect(component.closeCalendarDescription).toHaveBeenCalled();

      // Test 'edit' type
      component.handleDownKeydown(enterEvent, 'data', 'item', 'edit');
      expect(component.openEditModal).toHaveBeenCalledWith('data', 'item');
    });

    it('should handle Space key for different actions', () => {
      const spaceEvent = new KeyboardEvent('keydown', { key: ' ' });
      jest.spyOn(spaceEvent, 'preventDefault');
      jest.spyOn(component, 'clear');

      component.handleDownKeydown(spaceEvent, 'data', 'item', 'show');
      expect(spaceEvent.preventDefault).toHaveBeenCalled();
      expect(component.clear).toHaveBeenCalled();
    });

    it('should not handle other keys', () => {
      const otherEvent = new KeyboardEvent('keydown', { key: 'a' });
      jest.spyOn(otherEvent, 'preventDefault');
      jest.spyOn(component, 'clear');

      component.handleDownKeydown(otherEvent, 'data', 'item', 'show');
      expect(otherEvent.preventDefault).not.toHaveBeenCalled();
      expect(component.clear).not.toHaveBeenCalled();
    });

    it('should handle default case', () => {
      const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(enterEvent, 'preventDefault');

      component.handleDownKeydown(enterEvent, 'data', 'item', 'unknown');
      expect(enterEvent.preventDefault).toHaveBeenCalled();
    });
  });

  describe('Calendar Navigation Extended', () => {
    beforeEach(() => {
      jest.spyOn(component, 'closeDescription');
      jest.spyOn(component, 'setCalendar');
    });

    it('should navigate to previous year', () => {
      component.goPrevYear();
      expect(component.closeDescription).toHaveBeenCalled();
      expect(mockCalendarApi.prevYear).toHaveBeenCalled();
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should navigate to next year', () => {
      component.goNextYear();
      expect(component.closeDescription).toHaveBeenCalled();
      expect(mockCalendarApi.nextYear).toHaveBeenCalled();
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should change to week view', () => {
      component.goTimeGridWeekOrDay('timeGridWeek');
      expect(component.currentView).toBe('Week');
      expect(component.closeDescription).toHaveBeenCalled();
      expect(mockCalendarApi.changeView).toHaveBeenCalledWith('timeGridWeek');
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should change to day view', () => {
      component.goTimeGridWeekOrDay('timeGridDay');
      expect(component.currentView).toBe('Day');
      expect(component.closeDescription).toHaveBeenCalled();
      expect(mockCalendarApi.changeView).toHaveBeenCalledWith('timeGridDay');
      expect(component.setCalendar).toHaveBeenCalled();
    });
  });

  describe('Search and Filter Operations', () => {
    beforeEach(() => {
      jest.spyOn(component, 'getDeliveryRequestWithCraneEquipmentType');
    });

    it('should clear search and reset filters', () => {
      component.showSearchbar = true;
      component.search = 'test search';

      component.clear();

      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
      expect(component.getDeliveryRequestWithCraneEquipmentType).toHaveBeenCalled();
    });

    it('should handle search with data length > 0', () => {
      component.getSearchNDR('test');

      expect(component.showSearchbar).toBe(true);
      expect(component.search).toBe('test');
      expect(component.getDeliveryRequestWithCraneEquipmentType).toHaveBeenCalled();
    });

    it('should handle search with empty data', () => {
      component.getSearchNDR('');

      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
      expect(component.getDeliveryRequestWithCraneEquipmentType).toHaveBeenCalled();
    });

    it('should not show searchbar for data length 0', () => {
      component.getSearchNDR([]);

      expect(component.showSearchbar).toBe(false);
    });
  });

  describe('Utility Functions', () => {
    it('should get responsible people acronym with first and last name', () => {
      const person = { firstName: 'John', lastName: 'Doe' };
      const result = component.getResponsiblePeople(person);
      expect(result).toBe('JD');
    });

    it('should get responsible people acronym with only first name', () => {
      const person = { firstName: 'John', lastName: null };
      const result = component.getResponsiblePeople(person);
      expect(result).toBe('J');
    });

    it('should return default acronym for null object', () => {
      const result = component.getResponsiblePeople(null);
      expect(result).toBe('UU');
    });

    it('should return default acronym for empty object', () => {
      const person = { firstName: null, lastName: null };
      const result = component.getResponsiblePeople(person);
      expect(result).toBe('UU');
    });

    it('should close description popup', () => {
      component.descriptionPopup = true;
      component.closeDescription();
      expect(component.descriptionPopup).toBe(false);
    });

    it('should format date correctly', () => {
      const testDate = new Date('2024-03-20');
      const result = component.changeFormat(testDate);
      expect(result).toContain('2024');
      expect(result).toContain('03/20');
    });

    it('should handle null date in changeFormat', () => {
      const result = component.changeFormat(null);
      expect(result).toBeUndefined();
    });
  });

  describe('Series Options and Request Collapse', () => {
    beforeEach(() => {
      jest.spyOn(component, 'initializeSeriesOption');
    });

    it('should initialize series options', () => {
      component.initializeSeriesOption();

      expect(component.seriesOptions).toHaveLength(2);
      expect(component.seriesOptions[0]).toEqual({
        option: 1,
        text: 'This event',
        disabled: false,
      });
      expect(component.seriesOptions[1]).toEqual({
        option: 2,
        text: 'This and all following events',
        disabled: false,
      });
    });

    it('should change request collapse for future dates', () => {
      const futureDate = moment().add(1, 'day').toDate();
      const data = { craneDeliveryStart: futureDate };

      component.changeRequestCollapse(data);

      expect(component.initializeSeriesOption).toHaveBeenCalled();
      expect(component.allRequestIsOpened).toBe(true);
      expect(component.seriesOptions[0].disabled).toBe(false);
      expect(component.seriesOptions[1].disabled).toBe(false);
    });

    it('should change request collapse for past dates', () => {
      const pastDate = moment().subtract(1, 'day').toDate();
      const data = { craneDeliveryStart: pastDate };

      component.changeRequestCollapse(data);

      expect(component.initializeSeriesOption).toHaveBeenCalled();
      expect(component.allRequestIsOpened).toBe(true);
      expect(component.seriesOptions[0].disabled).toBe(false);
      expect(component.seriesOptions[1].disabled).toBe(true);
    });
  });

  describe('Modal Operations Extended', () => {
    it('should open modal with template', () => {
      const template = {} as any;
      component.openModal(template);

      expect(mockModalService.show).toHaveBeenCalledWith(template, {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      });
    });

    it('should close modal and reset form state', () => {
      component.submitted = true;
      component.formSubmitted = true;
      const mockModalRef = { hide: jest.fn() };
      (component as any).modalRef = mockModalRef;

      component.close();

      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should show error message', () => {
      const error = {
        message: {
          details: [{ error: 'Test error message' }]
        }
      };

      component.showError(error);

      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(mockToastrService.error).toHaveBeenCalledWith('Test error message');
    });
  });

  describe('Filter Operations', () => {
    beforeEach(() => {
      component.filterForm = new UntypedFormBuilder().group({
        descriptionFilter: [''],
        dateFilter: [''],
        companyFilter: [''],
        memberFilter: [''],
        gateFilter: [''],
        equipmentFilter: [''],
        locationFilter: [''],
        statusFilter: [''],
        pickFrom: [''],
        pickTo: ['']
      });
      jest.spyOn(component, 'getDeliveryRequestWithCraneEquipmentType');
    });

    it('should submit filter with all fields filled', () => {
      component.filterForm.patchValue({
        descriptionFilter: 'test',
        dateFilter: new Date(),
        companyFilter: '1',
        memberFilter: '1',
        gateFilter: '1',
        equipmentFilter: '1',
        locationFilter: '1',
        statusFilter: 'Pending',
        pickFrom: 'Location A',
        pickTo: 'Location B'
      });

      const mockModalRef = { hide: jest.fn() };
      (component as any).modalRef = mockModalRef;

      component.filterSubmit();

      expect(component.filterCount).toBe(10);
      expect(component.getDeliveryRequestWithCraneEquipmentType).toHaveBeenCalled();
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should reset filter', () => {
      component.filterCount = 5;
      component.search = 'test search';
      const mockModalRef = { hide: jest.fn() };
      (component as any).modalRef = mockModalRef;
      jest.spyOn(component, 'filterDetailsForm');

      component.resetFilter();

      expect(component.filterCount).toBe(0);
      expect(component.search).toBe('');
      expect(component.filterDetailsForm).toHaveBeenCalled();
      expect(component.getDeliveryRequestWithCraneEquipmentType).toHaveBeenCalled();
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should open filter modal and load data', () => {
      const template = {} as any;
      jest.spyOn(component, 'calendarGetOverAllGate');

      component.openFilterModal(template);

      expect(component.calendarGetOverAllGate).toHaveBeenCalled();
      expect(mockModalService.show).toHaveBeenCalledWith(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-sm filter-popup custom-modal'
      });
    });
  });

  describe('Data Loading Operations', () => {
    it('should get overall gate data', () => {
      component.modalLoader = false;
      jest.spyOn(component, 'calendarGetOverAllEquipment');

      component.calendarGetOverAllGate();

      expect(component.modalLoader).toBe(true);
      expect(mockProjectSharingService.guestGateList).toHaveBeenCalledWith(
        {
          ProjectId: component.ProjectId,
          pageSize: 0,
          pageNo: 0,
          ParentCompanyId: component.ParentCompanyId,
        },
        {
          isFilter: true,
          showActivatedAlone: true,
        }
      );
    });

    it('should get overall equipment data', () => {
      jest.spyOn(component, 'calendarGetCompany');

      component.calendarGetOverAllEquipment();

      expect(mockProjectSharingService.guestListCraneEquipment).toHaveBeenCalledWith(
        {
          ProjectId: component.ProjectId,
          pageSize: 0,
          pageNo: 0,
          ParentCompanyId: component.ParentCompanyId,
        },
        {
          showActivatedAlone: true,
        }
      );
    });

    it('should get company data', () => {
      component.companyList = [];
      jest.spyOn(component, 'calendarGetDefinable');

      component.calendarGetCompany();

      expect(mockProjectSharingService.guestGetCompanies).toHaveBeenCalledWith({
        ProjectId: component.ProjectId,
        ParentCompanyId: component.ParentCompanyId,
      });
    });

    it('should get definable work data', () => {
      jest.spyOn(component, 'getLocations');

      component.calendarGetDefinable();

      expect(mockProjectSharingService.guestGetDefinableWork).toHaveBeenCalledWith({
        ProjectId: component.ProjectId,
        ParentCompanyId: component.ParentCompanyId,
      });
    });

    it('should get locations data', () => {
      jest.spyOn(component, 'openContentModal');

      component.getLocations();

      expect(mockProjectSharingService.guestGetLocations).toHaveBeenCalledWith({
        ProjectId: component.ProjectId,
        ParentCompanyId: component.ParentCompanyId,
      });
    });

    it('should open content modal', () => {
      component.modalLoader = true;

      component.openContentModal();

      expect(component.modalLoader).toBe(false);
    });

    it('should get members data', () => {
      component.getMembers();

      expect(mockProjectSharingService.listAllMember).toHaveBeenCalledWith({
        ProjectId: component.ProjectId,
        ParentCompanyId: component.ParentCompanyId,
      });
    });
  });

  describe('Event Processing', () => {
    beforeEach(() => {
      component.deliveryRequestWithCraneEquipmentType = [
        {
          id: 1,
          CraneRequestId: 123,
          uniqueNumber: 'UN001',
          requestType: 'craneRequest',
          craneDeliveryStart: new Date(),
          craneDeliveryEnd: new Date(),
          approved_at: new Date(),
          memberDetails: [
            { Member: { id: 1 } },
            { Member: { id: 2 } },
            { Member: { id: 3 } },
            { Member: { id: 4, User: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' } } }
          ]
        },
        {
          id: 2,
          CraneRequestId: 124,
          requestType: 'deliveryRequestWithCrane',
          deliveryStart: new Date(),
          deliveryEnd: new Date(),
          approved_at: null,
          memberDetails: []
        }
      ];
      component.authUser = { RoleId: 1, id: 1 };
    });

    it('should process crane request event', () => {
      const arg = {
        event: {
          id: '123',
          extendedProps: {
            uniqueNumber: 'UN001'
          }
        }
      };
      jest.spyOn(component, 'getCraneRequest');
      jest.spyOn(component, 'openIdModal');

      component.processRequestEvent(arg);

      expect(component.eventData).toBeDefined();
      expect(component.calendarCurrentDeliveryIndex).toBe(0);
      expect(component.getCraneRequest).toHaveBeenCalledWith(arg);
      expect(component.openIdModal).toHaveBeenCalledWith(component.eventData);
    });

    it('should process delivery request with crane event', () => {
      const arg = {
        event: {
          id: '124',
          extendedProps: {}
        }
      };
      jest.spyOn(component, 'getNDR');
      jest.spyOn(component, 'openIdModal');

      component.processRequestEvent(arg);

      expect(component.eventData).toBeDefined();
      expect(component.calendarCurrentDeliveryIndex).toBe(1);
      expect(component.getNDR).toHaveBeenCalledWith(arg);
      expect(component.openIdModal).toHaveBeenCalledWith(component.eventData);
    });

    it('should process calendar event', () => {
      component.deliveryRequestWithCraneEquipmentType = [
        {
          id: 1,
          requestType: 'calendarEvent'
        }
      ];
      const arg = {
        event: {
          id: '1',
          extendedProps: {}
        }
      };
      jest.spyOn(component, 'calendarDescription');

      component.processRequestEvent(arg);

      expect(component.calendarDescription).toHaveBeenCalledWith(arg);
    });
  });

  describe('Request Detail Fetching', () => {
    beforeEach(() => {
      component.authUser = { RoleId: 4, id: 1 };
      component.eventData = { memberDetails: [] };
    });

    it('should get crane request details for authorized user', () => {
      const data = { event: { id: '123' } };
      const mockResponse = {
        data: {
          memberDetails: [{ Member: { id: 1 } }],
          gateDetails: []
        }
      };
      mockProjectSharingService.guestGetEquipmentCraneRequest.mockReturnValue(of(mockResponse));

      component.getCraneRequest(data);

      expect(mockProjectSharingService.guestGetEquipmentCraneRequest).toHaveBeenCalledWith({
        CraneRequestId: 123,
        ParentCompanyId: component.ParentCompanyId,
        ProjectId: component.ProjectId,
      });
    });

    it('should get NDR details', () => {
      component.deliveryRequestWithCraneEquipmentType = [
        { id: 1, CraneRequestId: 123 }
      ];
      const data = { event: { id: '123' } };
      const mockResponse = {
        data: {
          memberDetails: [{ Member: { id: 1 } }],
          gateDetails: []
        }
      };
      mockProjectSharingService.guestGetNDRData.mockReturnValue(of(mockResponse));

      component.getNDR(data);

      expect(mockProjectSharingService.guestGetNDRData).toHaveBeenCalledWith({
        DeliveryRequestId: 1,
        ParentCompanyId: component.ParentCompanyId,
      });
    });
  });

  describe('Delivery Description and Event Handling', () => {
    beforeEach(() => {
      component.isGuestUser = false;
      component.guestMemberId = 123;
    });

    it('should handle delivery description for non-guest user', () => {
      const arg = {
        event: {
          _def: {
            extendedProps: {
              isGuestRequest: false,
              requestType: 'craneRequest'
            }
          }
        }
      };
      jest.spyOn(component, 'processRequestEvent');

      component.deliveryDescription(arg);

      expect(component.descriptionPopup).toBe(false);
      expect(component.allRequestIsOpened).toBe(false);
      expect(component.calendarDescriptionPopup).toBe(false);
      expect(component.processRequestEvent).toHaveBeenCalledWith(arg);
    });

    it('should return early for guest user with non-guest request', () => {
      component.isGuestUser = true;
      const arg = {
        event: {
          _def: {
            extendedProps: {
              isGuestRequest: false,
              requestType: 'craneRequest'
            }
          }
        }
      };
      jest.spyOn(component, 'processRequestEvent');

      component.deliveryDescription(arg);

      expect(component.processRequestEvent).not.toHaveBeenCalled();
    });

    it('should return early for guest user with different creator', () => {
      component.isGuestUser = true;
      const arg = {
        event: {
          _def: {
            extendedProps: {
              isGuestRequest: true,
              creatorId: 456, // Different from guestMemberId
              requestType: 'craneRequest'
            }
          }
        }
      };
      jest.spyOn(component, 'processRequestEvent');

      component.deliveryDescription(arg);

      expect(component.processRequestEvent).not.toHaveBeenCalled();
    });

    it('should process calendar event for guest user', () => {
      component.isGuestUser = true;
      const arg = {
        event: {
          _def: {
            extendedProps: {
              requestType: 'calendarEvent'
            }
          }
        }
      };
      jest.spyOn(component, 'processRequestEvent');

      component.deliveryDescription(arg);

      expect(component.processRequestEvent).toHaveBeenCalledWith(arg);
    });

    it('should handle empty argument', () => {
      jest.spyOn(component, 'processRequestEvent');

      component.deliveryDescription({});

      expect(component.processRequestEvent).not.toHaveBeenCalled();
    });
  });

  describe('Edit Modal Operations', () => {
    beforeEach(() => {
      jest.spyOn(component, 'closeDescription');
      jest.spyOn(component, 'close');
    });

    it('should open edit modal for crane request', () => {
      const item = {
        isAssociatedWithDeliveryRequest: false,
        isAssociatedWithCraneRequest: false,
        CraneRequestId: 123,
        recurrence: null
      };
      const action = 1;

      component.openEditModal(item, action);

      expect(component.closeDescription).toHaveBeenCalled();
      expect(mockDeliveryService.updatedEditCraneRequestId).toHaveBeenCalledWith(123);
      expect(mockModalService.show).toHaveBeenCalled();
    });

    it('should open edit modal for delivery request', () => {
      const item = {
        isAssociatedWithDeliveryRequest: true,
        isAssociatedWithCraneRequest: false,
        id: 456,
        recurrence: {
          recurrence: 'daily',
          id: 789,
          recurrenceEndDate: new Date()
        }
      };
      const action = 2;

      component.openEditModal(item, action);

      expect(component.closeDescription).toHaveBeenCalled();
      expect(mockDeliveryService.updatedDeliveryId).toHaveBeenCalledWith(456);
      expect(mockModalService.show).toHaveBeenCalled();
    });

    it('should close existing modal before opening new one', () => {
      const mockModalRef = { hide: jest.fn() };
      (component as any).modalRef = mockModalRef;

      const item = {
        isAssociatedWithDeliveryRequest: false,
        isAssociatedWithCraneRequest: false,
        CraneRequestId: 123
      };

      component.openEditModal(item, 1);

      expect(component.close).toHaveBeenCalled();
    });
  });

  describe('ID Modal Operations', () => {
    it('should open ID modal for crane request', () => {
      component.eventData = {
        isAssociatedWithDeliveryRequest: false,
        isAssociatedWithCraneRequest: false
      };
      const item = {
        CraneRequestId: 123,
        ProjectId: 456,
        ParentCompanyId: 789
      };

      component.openIdModal(item);

      expect(mockModalService.show).toHaveBeenCalled();
    });

    it('should open ID modal for delivery request', () => {
      component.eventData = {
        isAssociatedWithDeliveryRequest: true,
        isAssociatedWithCraneRequest: false
      };
      const item = {
        id: 123,
        ProjectId: 456,
        ParentCompanyId: 789
      };

      component.openIdModal(item);

      expect(mockDeliveryService.updatedCurrentStatus).toHaveBeenCalledWith(123);
      expect(mockModalService.show).toHaveBeenCalled();
    });
  });

  describe('Occurrence Message Generation', () => {
    it('should generate message for daily occurrence', () => {
      const data = {
        repeatEveryType: 'Day',
        repeatEveryCount: 1,
        days: [],
        chosenDateOfMonth: false,
        dateOfMonth: null,
        monthlyRepeatType: null,
        endTime: new Date('2024-12-31')
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs every day until 12-31-2024');
    });

    it('should generate message for multiple days', () => {
      const data = {
        repeatEveryType: 'Days',
        repeatEveryCount: 3,
        days: [],
        chosenDateOfMonth: false,
        dateOfMonth: null,
        monthlyRepeatType: null,
        endTime: new Date('2024-12-31')
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs every 3 days until 12-31-2024');
    });

    it('should generate message for every other day', () => {
      const data = {
        repeatEveryType: 'Days',
        repeatEveryCount: 2,
        days: [],
        chosenDateOfMonth: false,
        dateOfMonth: null,
        monthlyRepeatType: null,
        endTime: new Date('2024-12-31')
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs every other day until 12-31-2024');
    });

    it('should generate message for weekly occurrence', () => {
      const data = {
        repeatEveryType: 'Week',
        repeatEveryCount: 1,
        days: ['Monday', 'Wednesday', 'Friday'],
        chosenDateOfMonth: false,
        dateOfMonth: null,
        monthlyRepeatType: null,
        endTime: new Date('2024-12-31')
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs every Monday,Wednesday,Friday until 12-31-2024');
    });

    it('should generate message for multiple weeks', () => {
      const data = {
        repeatEveryType: 'Weeks',
        repeatEveryCount: 3,
        days: ['Tuesday', 'Thursday'],
        chosenDateOfMonth: false,
        dateOfMonth: null,
        monthlyRepeatType: null,
        endTime: new Date('2024-12-31')
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs every 3 weeks on Tuesday,Thursday until 12-31-2024');
    });

    it('should generate message for every other week', () => {
      const data = {
        repeatEveryType: 'Weeks',
        repeatEveryCount: 2,
        days: ['Monday'],
        chosenDateOfMonth: false,
        dateOfMonth: null,
        monthlyRepeatType: null,
        endTime: new Date('2024-12-31')
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs every other  Monday until 12-31-2024');
    });

    it('should generate message for monthly occurrence with date', () => {
      const data = {
        repeatEveryType: 'Month',
        repeatEveryCount: 1,
        days: [],
        chosenDateOfMonth: true,
        dateOfMonth: 15,
        monthlyRepeatType: null,
        endTime: new Date('2024-12-31')
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs on day 15 until 12-31-2024');
    });

    it('should generate message for monthly occurrence with pattern', () => {
      const data = {
        repeatEveryType: 'Months',
        repeatEveryCount: 1,
        days: [],
        chosenDateOfMonth: false,
        dateOfMonth: null,
        monthlyRepeatType: 'first Monday',
        endTime: new Date('2024-12-31')
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs on the first Monday until 12-31-2024');
    });

    it('should generate message for yearly occurrence', () => {
      const data = {
        repeatEveryType: 'Year',
        repeatEveryCount: 1,
        days: [],
        chosenDateOfMonth: true,
        dateOfMonth: 25,
        monthlyRepeatType: null,
        endTime: new Date('2024-12-31')
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs on day 25 until 12-31-2024');
    });
  });

  describe('Value by Label Extraction', () => {
    const mockElement = {
      description: 'Test Description',
      companyDetails: [{ Company: { companyName: 'Test Company' } }],
      memberDetails: [{ Member: { User: { firstName: 'John', lastName: 'Doe' } } }],
      CraneRequestId: 'CR123',
      defineWorkDetails: [{ DeliverDefineWork: { DFOW: 'Test DFOW' } }],
      equipmentDetails: [{ Equipment: { equipmentName: 'Test Equipment' } }],
      pickUpLocation: 'Pickup Location',
      dropOffLocation: 'Drop Location'
    };

    it('should extract description', () => {
      const result = component.getValueByLabel('Description', mockElement);
      expect(result).toBe('Test Description');
    });

    it('should extract responsible company', () => {
      const result = component.getValueByLabel('Responsible Company', mockElement);
      expect(result).toBe('Test Company');
    });

    it('should extract responsible person', () => {
      const result = component.getValueByLabel('Responsible Person', mockElement);
      expect(result).toBe('John Doe');
    });

    it('should extract crane pick ID', () => {
      const result = component.getValueByLabel('Crane Pick ID', mockElement);
      expect(result).toBe('CR123');
    });

    it('should extract definable feature of work', () => {
      const result = component.getValueByLabel('Definable Feature Of Work', mockElement);
      expect(result).toBe('Test DFOW');
    });

    it('should extract equipment', () => {
      const result = component.getValueByLabel('Equipment', mockElement);
      expect(result).toBe('Test Equipment');
    });

    it('should extract picking from location', () => {
      const result = component.getValueByLabel('Picking From', mockElement);
      expect(result).toBe('Pickup Location');
    });

    it('should extract picking to location', () => {
      const result = component.getValueByLabel('Picking To', mockElement);
      expect(result).toBe('Drop Location');
    });

    it('should return null for unknown label', () => {
      const result = component.getValueByLabel('Unknown Label', mockElement);
      expect(result).toBeNull();
    });

    it('should handle missing data gracefully', () => {
      const emptyElement = { companyDetails: [] };
      const result = component.getValueByLabel('Responsible Company', emptyElement);
      expect(result).toBe('');
    });
  });

  describe('Lifecycle Hooks', () => {
    it('should call setCalendar and getMembers on ngAfterViewInit', () => {
      jest.spyOn(component, 'setCalendar');
      jest.spyOn(component, 'getMembers');

      component.ngAfterViewInit();

      expect(component.setCalendar).toHaveBeenCalled();
      expect(component.getMembers).toHaveBeenCalled();
    });
  });

  describe('Error Handling in Service Calls', () => {
    it('should handle error in calendarGetOverAllGate', () => {
      mockProjectSharingService.guestGateList.mockReturnValue(throwError(() => new Error('Gate list error')));
      jest.spyOn(console, 'error').mockImplementation(() => {});

      component.calendarGetOverAllGate();

      expect(mockProjectSharingService.guestGateList).toHaveBeenCalled();
    });

    it('should handle error in calendarGetOverAllEquipment', () => {
      mockProjectSharingService.guestListCraneEquipment.mockReturnValue(throwError(() => new Error('Equipment error')));
      jest.spyOn(console, 'error').mockImplementation(() => {});

      component.calendarGetOverAllEquipment();

      expect(mockProjectSharingService.guestListCraneEquipment).toHaveBeenCalled();
    });

    it('should handle error in calendarGetCompany', () => {
      mockProjectSharingService.guestGetCompanies.mockReturnValue(throwError(() => new Error('Company error')));
      jest.spyOn(console, 'error').mockImplementation(() => {});

      component.calendarGetCompany();

      expect(mockProjectSharingService.guestGetCompanies).toHaveBeenCalled();
    });

    it('should handle error in calendarGetDefinable', () => {
      mockProjectSharingService.guestGetDefinableWork.mockReturnValue(throwError(() => new Error('Definable error')));
      jest.spyOn(console, 'error').mockImplementation(() => {});

      component.calendarGetDefinable();

      expect(mockProjectSharingService.guestGetDefinableWork).toHaveBeenCalled();
    });

    it('should handle error in getLocations', () => {
      mockProjectSharingService.guestGetLocations.mockReturnValue(throwError(() => new Error('Locations error')));
      jest.spyOn(console, 'error').mockImplementation(() => {});

      component.getLocations();

      expect(mockProjectSharingService.guestGetLocations).toHaveBeenCalled();
    });

    it('should handle error in getMembers', () => {
      mockProjectSharingService.listAllMember.mockReturnValue(throwError(() => new Error('Members error')));
      jest.spyOn(console, 'error').mockImplementation(() => {});

      component.getMembers();

      expect(mockProjectSharingService.listAllMember).toHaveBeenCalled();
    });

    it('should handle error in getCraneRequest', () => {
      mockProjectSharingService.guestGetEquipmentCraneRequest.mockReturnValue(throwError(() => new Error('Crane request error')));
      jest.spyOn(console, 'error').mockImplementation(() => {});

      const data = { event: { id: '123' } };
      component.getCraneRequest(data);

      expect(mockProjectSharingService.guestGetEquipmentCraneRequest).toHaveBeenCalled();
    });

    it('should handle error in getNDR', () => {
      component.deliveryRequestWithCraneEquipmentType = [{ id: 1, CraneRequestId: 123 }];
      mockProjectSharingService.guestGetNDRData.mockReturnValue(throwError(() => new Error('NDR error')));
      jest.spyOn(console, 'error').mockImplementation(() => {});

      const data = { event: { id: '123' } };
      component.getNDR(data);

      expect(mockProjectSharingService.guestGetNDRData).toHaveBeenCalled();
    });

    it('should handle error in getDeliveryRequestWithCraneEquipmentType', () => {
      mockProjectSharingService.getDeliveryRequestWithCraneEquipmentType.mockReturnValue(throwError(() => new Error('Delivery request error')));
      jest.spyOn(console, 'error').mockImplementation(() => {});

      component.getDeliveryRequestWithCraneEquipmentType();

      expect(mockProjectSharingService.getDeliveryRequestWithCraneEquipmentType).toHaveBeenCalled();
      expect(component.loader).toBe(true);
    });
  });

  describe('Edge Cases and Negative Scenarios', () => {
    it('should handle null calendar API in setCalendar', () => {
      component.calendarComponent1 = {
        getApi: jest.fn().mockReturnValue(null)
      } as any;
      jest.spyOn(component, 'getDeliveryRequestWithCraneEquipmentType');

      component.setCalendar();

      expect(component.calendarApi).toBeNull();
      expect(component.getDeliveryRequestWithCraneEquipmentType).toHaveBeenCalled();
    });

    it('should handle undefined filterForm in getDeliveryRequestWithCraneEquipmentType', () => {
      component.filterForm = undefined;
      jest.spyOn(component, 'getDeliveryRequestWithCraneEquipmentType').mockImplementation(() => {});

      component.getDeliveryRequestWithCraneEquipmentType();

      expect(component.loader).toBe(true);
    });

    it('should handle missing localStorage values', () => {
      const mockLocalStorage = {
        getItem: jest.fn().mockReturnValue(null)
      };
      Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

      // Create new component instance to test constructor
      const newFixture = TestBed.createComponent(GuestuserCranecalendarComponent);
      const newComponent = newFixture.componentInstance;

      expect(newComponent.id).toBeNaN();
      expect(newComponent.email).toBe('');
      expect(newComponent.ProjectId).toBeNaN();
      expect(newComponent.ParentCompanyId).toBeNaN();
    });

    it('should handle empty events array in calendarDescription', () => {
      component.events = [];
      const arg = {
        event: {
          title: 'Test Event',
          extendedProps: { uniqueNumber: '123' }
        }
      };

      component.calendarDescription(arg);

      expect(component.viewEventData).toBeUndefined();
    });

    it('should handle missing event data in processRequestEvent', () => {
      component.deliveryRequestWithCraneEquipmentType = [];
      const arg = {
        event: {
          id: '999',
          extendedProps: { uniqueNumber: 'MISSING' }
        }
      };

      component.processRequestEvent(arg);

      expect(component.eventData).toBeUndefined();
    });

    it('should handle missing memberDetails in getCraneRequest', () => {
      component.authUser = { RoleId: 4, id: 1 };
      component.eventData = { memberDetails: [] };
      const mockResponse = {
        data: {
          memberDetails: [],
          gateDetails: []
        }
      };
      mockProjectSharingService.guestGetEquipmentCraneRequest.mockReturnValue(of(mockResponse));

      const data = { event: { id: '123' } };
      component.getCraneRequest(data);

      expect(component.eventData.edit).toBe(false);
      expect(component.toolTipContent).toBe('');
    });

    it('should handle missing User data in tooltip generation', () => {
      component.authUser = { RoleId: 1, id: 1 };
      component.eventData = {
        memberDetails: [
          { Member: { id: 1 } },
          { Member: { id: 2 } },
          { Member: { id: 3 } },
          { Member: { id: 4, User: { email: '<EMAIL>' } } }
        ]
      };
      const mockResponse = {
        data: {
          memberDetails: [],
          gateDetails: []
        }
      };
      mockProjectSharingService.guestGetEquipmentCraneRequest.mockReturnValue(of(mockResponse));

      const data = { event: { id: '123' } };
      component.getCraneRequest(data);

      expect(component.toolTipContent).toContain('<EMAIL>');
    });
  });

  describe('Complex Data Processing Scenarios', () => {
    beforeEach(() => {
      component.filterForm = new UntypedFormBuilder().group({
        descriptionFilter: [''],
        dateFilter: [''],
        companyFilter: [''],
        memberFilter: [''],
        gateFilter: [''],
        equipmentFilter: [''],
        locationFilter: [''],
        statusFilter: [''],
        pickFrom: [''],
        pickTo: ['']
      });
    });

    it('should process delivery request with crane equipment type with complex data', () => {
      const mockResponse = {
        data: [
          {
            id: 1,
            CraneRequestId: 123,
            uniqueNumber: 'UN001',
            requestType: 'craneRequest',
            craneDeliveryStart: new Date('2024-03-20T10:00:00'),
            craneDeliveryEnd: new Date('2024-03-20T12:00:00'),
            status: 'Approved',
            description: 'Test crane request',
            isCreatedByGuestUser: true,
            createdUserDetails: { id: 456 },
            companyDetails: [{ Company: { companyName: 'Test Company' } }],
            memberDetails: [{ Member: { User: { firstName: 'John', lastName: 'Doe' } } }]
          },
          {
            id: 2,
            CraneRequestId: 124,
            requestType: 'deliveryRequestWithCrane',
            deliveryStart: new Date('2024-03-21T14:00:00'),
            deliveryEnd: new Date('2024-03-21T16:00:00'),
            status: 'Pending',
            description: 'Test delivery request',
            isCreatedByGuestUser: false
          },
          {
            id: 3,
            requestType: 'calendarEvent',
            fromDate: new Date('2024-03-22T09:00:00'),
            toDate: new Date('2024-03-22T17:00:00'),
            description: 'Test calendar event',
            isAllDay: true
          }
        ],
        lastId: { CraneRequestId: 125 },
        statusData: {
          statusColorCode: JSON.stringify([
            { status: 'approved', backgroundColor: '#28a745', fontColor: '#fff' },
            { status: 'pending', backgroundColor: '#ffc107', fontColor: '#000' }
          ]),
          useTextColorAsLegend: 'false',
          isDefaultColor: 'false'
        },
        cardData: {
          craneCard: JSON.stringify([
            { label: 'Description', line: 1, selected: true },
            { label: 'Responsible Company', line: 2, selected: true }
          ])
        }
      };

      mockProjectSharingService.getDeliveryRequestWithCraneEquipmentType.mockReturnValue(of(mockResponse));
      component.Range = { start: new Date('2024-03-01'), end: new Date('2024-03-31') };

      component.getDeliveryRequestWithCraneEquipmentType();

      expect(component.loader).toBe(false);
      expect(component.deliveryRequestWithCraneEquipmentType).toHaveLength(3);
      expect(component.events).toHaveLength(3);
      expect(component.lastId).toBe(125);
    });

    it('should handle calendar event with all day flag', () => {
      const mockResponse = {
        data: [
          {
            id: 1,
            requestType: 'calendarEvent',
            fromDate: new Date('2024-03-22'),
            toDate: new Date('2024-03-22'),
            description: 'All day event',
            isAllDay: true
          }
        ],
        lastId: { CraneRequestId: 1 },
        statusData: {
          statusColorCode: JSON.stringify([]),
          useTextColorAsLegend: 'false',
          isDefaultColor: 'false'
        },
        cardData: {
          craneCard: JSON.stringify([])
        }
      };

      mockProjectSharingService.getDeliveryRequestWithCraneEquipmentType.mockReturnValue(of(mockResponse));
      component.Range = { start: new Date('2024-03-01'), end: new Date('2024-03-31') };

      component.getDeliveryRequestWithCraneEquipmentType();

      const calendarEvent = component.events.find(event => event.requestType === 'calendarEvent');
      expect(calendarEvent.allDay).toBe(true);
      expect(calendarEvent.allDaySlot).toBe(true);
    });

    it('should handle status color configuration with text color as legend', () => {
      const mockResponse = {
        data: [
          {
            id: 1,
            CraneRequestId: 123,
            requestType: 'craneRequest',
            craneDeliveryStart: new Date(),
            craneDeliveryEnd: new Date(),
            status: 'Delivered'
          }
        ],
        lastId: { CraneRequestId: 1 },
        statusData: {
          statusColorCode: JSON.stringify([
            { status: 'delivered', backgroundColor: '#17a2b8', fontColor: '#fff' }
          ]),
          useTextColorAsLegend: 'true',
          isDefaultColor: 'false'
        },
        cardData: {
          craneCard: JSON.stringify([])
        }
      };

      mockProjectSharingService.getDeliveryRequestWithCraneEquipmentType.mockReturnValue(of(mockResponse));
      component.Range = { start: new Date('2024-03-01'), end: new Date('2024-03-31') };

      component.getDeliveryRequestWithCraneEquipmentType();

      expect(component.delivered).toBe('#fff'); // Should use fontColor when useTextColorAsLegend is true
    });

    it('should handle default color configuration', () => {
      const mockResponse = {
        data: [
          {
            id: 1,
            CraneRequestId: 123,
            requestType: 'craneRequest',
            craneDeliveryStart: new Date(),
            craneDeliveryEnd: new Date(),
            status: 'Delivered'
          }
        ],
        lastId: { CraneRequestId: 1 },
        statusData: {
          statusColorCode: JSON.stringify([
            { status: 'delivered', backgroundColor: '#17a2b8', fontColor: '#fff' }
          ]),
          useTextColorAsLegend: 'false',
          isDefaultColor: 'true'
        },
        cardData: {
          craneCard: JSON.stringify([])
        }
      };

      mockProjectSharingService.getDeliveryRequestWithCraneEquipmentType.mockReturnValue(of(mockResponse));
      component.Range = { start: new Date('2024-03-01'), end: new Date('2024-03-31') };

      component.getDeliveryRequestWithCraneEquipmentType();

      expect(component.delivered).toBe('#17a2b8'); // Should use backgroundColor when isDefaultColor is true
    });
  });

  describe('Form Validation and Edge Cases', () => {
    beforeEach(() => {
      component.filterForm = new UntypedFormBuilder().group({
        descriptionFilter: [''],
        dateFilter: [''],
        companyFilter: [''],
        memberFilter: [''],
        gateFilter: [''],
        equipmentFilter: [''],
        locationFilter: [''],
        statusFilter: [''],
        pickFrom: [''],
        pickTo: ['']
      });
    });

    it('should handle filter form with date value', () => {
      const testDate = new Date('2024-03-20');
      component.filterForm.patchValue({
        dateFilter: testDate,
        descriptionFilter: 'test description'
      });
      jest.spyOn(component, 'getDeliveryRequestWithCraneEquipmentType').mockImplementation(() => {});

      component.filterSubmit();

      expect(component.filterCount).toBe(2);
    });

    it('should handle empty filter form values', () => {
      component.filterForm.patchValue({
        descriptionFilter: '',
        dateFilter: '',
        companyFilter: '',
        memberFilter: '',
        gateFilter: '',
        equipmentFilter: '',
        locationFilter: '',
        statusFilter: '',
        pickFrom: '',
        pickTo: ''
      });
      jest.spyOn(component, 'getDeliveryRequestWithCraneEquipmentType').mockImplementation(() => {});

      component.filterSubmit();

      expect(component.filterCount).toBe(0);
    });

    it('should handle null values in filter form', () => {
      component.filterForm.patchValue({
        descriptionFilter: null,
        dateFilter: null,
        companyFilter: null
      });
      jest.spyOn(component, 'getDeliveryRequestWithCraneEquipmentType').mockImplementation(() => {});

      component.filterSubmit();

      expect(component.filterCount).toBe(0);
    });
  });

  describe('Calendar API Interactions', () => {
    it('should handle calendar API operations with null API', () => {
      component.calendarApi = null;

      expect(() => component.goNext()).not.toThrow();
      expect(() => component.goPrev()).not.toThrow();
      expect(() => component.goNextYear()).not.toThrow();
      expect(() => component.goPrevYear()).not.toThrow();
    });

    it('should handle calendar API with missing methods', () => {
      component.calendarApi = {};

      expect(() => component.goNext()).not.toThrow();
      expect(() => component.goPrev()).not.toThrow();
    });

    it('should update events when calendar API is available', () => {
      component.calendarApi = mockCalendarApi;
      component.events = [{ id: 1, title: 'Test Event' }];

      component.getDeliveryRequestWithCraneEquipmentType();

      expect(mockCalendarApi.removeAllEventSources).toHaveBeenCalled();
      expect(mockCalendarApi.addEventSource).toHaveBeenCalled();
    });
  });

  describe('Responsible People Acronym Generation', () => {
    it('should handle person with only first name', () => {
      const person = { firstName: 'John', lastName: '' };
      const result = component.getResponsiblePeople(person);
      expect(result).toBe('J');
    });

    it('should handle person with only last name', () => {
      const person = { firstName: '', lastName: 'Doe' };
      const result = component.getResponsiblePeople(person);
      expect(result).toBe('UU');
    });

    it('should handle person with multiple names', () => {
      const person = { firstName: 'John Michael', lastName: 'Doe Smith' };
      const result = component.getResponsiblePeople(person);
      expect(result).toBe('JMDS');
    });

    it('should handle person with special characters', () => {
      const person = { firstName: 'Jean-Pierre', lastName: "O'Connor" };
      const result = component.getResponsiblePeople(person);
      expect(result).toBe('JPO');
    });

    it('should handle undefined person object', () => {
      const result = component.getResponsiblePeople(undefined);
      expect(result).toBe('UU');
    });
  });

  describe('Date Formatting and Utilities', () => {
    it('should format date correctly with different formats', () => {
      const testDate = new Date('2024-12-25T15:30:00');
      const result = component.changeFormat(testDate);
      expect(result).toContain('Tue');
      expect(result).toContain('12/25/2024');
    });

    it('should handle invalid date', () => {
      const result = component.changeFormat('invalid-date');
      expect(result).toBeUndefined();
    });

    it('should handle empty string date', () => {
      const result = component.changeFormat('');
      expect(result).toBeUndefined();
    });

    it('should handle zero timestamp', () => {
      const result = component.changeFormat(0);
      expect(result).toContain('1970');
    });
  });

  describe('Modal Reference Management', () => {
    it('should handle close when modalRef is null', () => {
      component.modalRef = null;

      expect(() => component.close()).not.toThrow();
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
    });

    it('should handle resetFilter when modalRef is null', () => {
      component.modalRef = null;
      jest.spyOn(component, 'filterDetailsForm');
      jest.spyOn(component, 'getDeliveryRequestWithCraneEquipmentType');

      expect(() => component.resetFilter()).toThrow();
    });

    it('should handle filterSubmit when modalRef is null', () => {
      component.modalRef = null;
      component.filterForm = new UntypedFormBuilder().group({
        descriptionFilter: ['test']
      });
      jest.spyOn(component, 'getDeliveryRequestWithCraneEquipmentType');

      expect(() => component.filterSubmit()).toThrow();
    });
  });

  describe('Error Message Handling', () => {
    it('should handle error with multiple details', () => {
      const error = {
        message: {
          details: [
            { error: 'First error', field: 'field1' },
            { error: 'Second error', field: 'field2' }
          ]
        }
      };

      component.showError(error);

      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(mockToastrService.error).toHaveBeenCalled();
    });

    it('should handle error with empty details array', () => {
      const error = {
        message: {
          details: []
        }
      };

      expect(() => component.showError(error)).toThrow();
    });

    it('should handle error with null message', () => {
      const error = {
        message: null
      };

      expect(() => component.showError(error)).toThrow();
    });
  });

  describe('Search Functionality Edge Cases', () => {
    it('should handle search with special characters', () => {
      jest.spyOn(component, 'getDeliveryRequestWithCraneEquipmentType');

      component.getSearchNDR('test@#$%^&*()');

      expect(component.showSearchbar).toBe(true);
      expect(component.search).toBe('test@#$%^&*()');
      expect(component.getDeliveryRequestWithCraneEquipmentType).toHaveBeenCalled();
    });

    it('should handle search with very long string', () => {
      const longString = 'a'.repeat(1000);
      jest.spyOn(component, 'getDeliveryRequestWithCraneEquipmentType');

      component.getSearchNDR(longString);

      expect(component.showSearchbar).toBe(true);
      expect(component.search).toBe(longString);
    });

    it('should handle search with whitespace only', () => {
      jest.spyOn(component, 'getDeliveryRequestWithCraneEquipmentType');

      component.getSearchNDR('   ');

      expect(component.showSearchbar).toBe(true);
      expect(component.search).toBe('   ');
    });

    it('should handle search with array input', () => {
      jest.spyOn(component, 'getDeliveryRequestWithCraneEquipmentType');

      component.getSearchNDR(['test', 'array']);

      expect(component.showSearchbar).toBe(true);
    });
  });
});
