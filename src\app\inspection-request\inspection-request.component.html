<section class="page-section pt-md-50px">
    <div class="page-inner-content">
      <div class="top-header my-3">
        <div class="row">
          <div class="col-md-8">
            <div class="top-btn">
              <ul class="list-group list-group-horizontal-md">
                <li class="list-group-item p0 border-0 bg-transparent me-md-4 mb-3 mb-md-0">
                  <button
                    class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular"
                    (click)="setGateEquipment()"
                    *ngIf="authUser?.User?.email != null && authUser?.User?.email != undefined"
                  >
                    Create New
                  </button>
                </li>
                <li class="list-group-item p0 border-0 bg-transparent me-md-4 mb-3 mb-md-0">
                  <button
                    class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular"
                    (click)="openEditMultipleModal(editMultipleinspectionRequest)"
                    *ngIf="authUser?.User?.email != null && authUser?.User?.email != undefined"
                    [disabled]="checkIfCurrentinspectionRequestRowSelected()"
                  >
                    Edit
                  </button>
                </li>
              </ul>
            </div>
          </div>
          <div class="col-md-4">
            <div class="top-filter">
              <ul class="list-group list-group-horizontal justify-content-end">
                <li class="list-group-item p0 border-0 bg-transparent me-2">
                  <div class="search-icon">
                    <input
                      class="form-control fs12 color-grey8"
                      [ngClass]="showSearchbar ? 'input-hover-disable' : 'input-search'"
                      placeholder="What are you looking for?"
                      (input)="getSearchNDR($event.target.value)"
                      [(ngModel)]="search"
                    />
                    <div class="icon">
                      <img
                        src="./assets/images/cross-close.svg"
                        *ngIf="showSearchbar"
                        (click)="clear()" (keydown)="handleDownKeydown($event, '','','clear')"
                        alt="close-cross"
                      />
                      <em class="fa fa-search fs12 color-grey8" *ngIf="!showSearchbar"></em>
                    </div>
                  </div>
                </li>
                <li class="list-group-item p0 border-0 bg-transparent me-2 position-relative">
                  <div class="filter-icon" (click)="openModal1(filter)" (keydown)="handleDownKeydown($event, filter,'','openMod')">
                    <img src="./assets/images/filter.svg" class="h-12px icon" alt="Filter" />
                  </div>
                  <div
                    class="bg-orange rounded-circle position-absolute text-white filter-count"
                    *ngIf="filterCount > 0"
                  >
                    <p class="m-0 text-center fs10">{{ filterCount }}</p>
                  </div>
                </li>
                <li class="list-group-item p0 border-0 bg-transparent">
                  <button
                    type="button"
                    (click)="redirect('void-list')"
                    class="btn btn-orange-dark2 px-3 fs10 fw-bold cairo-regular"
                  >
                    Void List
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div class="page-card bg-white rounded delivery-request-card">
        <span
          class="float-right fs12 me-3 p2 color-orange fw-bold"
          *ngIf="bulkNdrUploadInProgress"
        >
          <em class="fas fa-sync fa-spin" width="10px" alt="uploading"></em>
          Uploading
        </span>
        <tabset>
          <tab heading="Inspection Bookings" [active]="currentlySelectedTab === 'tab0'">
            <div class="table-responsive tab-grid">
              <table class="table table-custom request-resizable mb-0" aria-describedby="Emptable">
                <thead>
                  <th scope="col" *ngIf="inspectionList?.length > 0">
                    <div class="custom-control custom-checkbox text-black">
                      <input
                        type="checkbox"
                        class="custom-control-input"
                        id="tb2Data"
                        name="tb2Data"
                        (change)="selectAllCurrentinspectionRequestForEdit()"
                        [checked]="currentinspectionRequestSelectAll"
                      />
                      <label class="custom-control-label c-pointer fs12" for="tb2Data"></label>
                    </div>
                  </th>
                  <th scope="col" resizable>
                    ID
                    <span>
                      <img
                        src="./assets/images/down-chevron.svg"
                        alt="down-arrow"
                        class="h-10px ms-2"
                        (click)="sortByField('id', 'ASC')"
                        (keydown)="handleToggleKeydown($event, 'id', 'ASC')"
                        *ngIf="sortColumn !== 'id'"
                      />
                      <img
                        src="./assets/images/down-chevron.svg"
                        alt="down-arrow"
                        class="h-10px ms-2"
                        (click)="sortByField('id', 'ASC')"
                        (keydown)="handleToggleKeydown($event, 'id', 'ASC')"
                        *ngIf="sort === 'DESC' && sortColumn === 'id'"
                      />
                      <img
                        src="./assets/images/up-chevron.svg"
                        alt="up-arrow"
                        class="h-10px ms-2"
                        (click)="sortByField('id', 'DESC')"
                        (keydown)="handleToggleKeydown($event, 'id', 'DESC')"
                        *ngIf="sort === 'ASC' && sortColumn === 'id'"
                      />
                    </span>
                  </th>
                  <th scope="col" resizable>
                    Description
                    <span>
                      <img
                        src="./assets/images/down-chevron.svg"
                        alt="down-arrow"
                        class="h-10px ms-2"
                        (click)="sortByField('description', 'ASC')"
                        (keydown)="handleToggleKeydown($event, 'description', 'ASC')"
                        *ngIf="sortColumn !== 'description'"
                      />
                      <img
                        src="./assets/images/down-chevron.svg"
                        alt="down-arrow"
                        class="h-10px ms-2"
                        (click)="sortByField('description', 'ASC')"
                        (keydown)="handleToggleKeydown($event, 'description', 'ASC')"
                        *ngIf="sort === 'DESC' && sortColumn === 'description'"
                      />
                      <img
                        src="./assets/images/up-chevron.svg"
                        alt="up-arrow"
                        class="h-10px ms-2"
                        (click)="sortByField('description', 'DESC')"
                        (keydown)="handleToggleKeydown($event, 'description', 'DESC')"
                        *ngIf="sort === 'ASC' && sortColumn === 'description'"
                      />
                    </span>
                  </th>
                  <th scope="col" resizable>
                    Date and Time
                    <span>
                      <img
                        src="./assets/images/down-chevron.svg"
                        alt="down-arrow"
                        class="h-10px ms-2"
                        (click)="sortByField('inspectionStart', 'ASC')"
                        (keydown)="handleToggleKeydown($event, 'inspectionStart', 'ASC')"
                        *ngIf="sortColumn !== 'inspectionStart'"
                      />
                      <img
                        src="./assets/images/down-chevron.svg"
                        alt="down-arrow"
                        class="h-10px ms-2"
                        (click)="sortByField('inspectionStart', 'ASC')"
                        (keydown)="handleToggleKeydown($event, 'inspectionStart', 'ASC')"
                        *ngIf="sort === 'DESC' && sortColumn === 'inspectionStart'"
                      />
                      <img
                        src="./assets/images/up-chevron.svg"
                        alt="up-arrow"
                        class="h-10px ms-2"
                        (click)="sortByField('inspectionStart', 'DESC')"
                        (keydown)="handleToggleKeydown($event, 'inspectionStart', 'DESC')"
                        *ngIf="sort === 'ASC' && sortColumn === 'inspectionStart'"
                      />
                    </span>
                  </th>
                  <th scope="col" resizable>
                    Status
                    <span>
                      <img
                        src="./assets/images/down-chevron.svg"
                        alt="down-arrow"
                        class="h-10px ms-2"
                        (click)="sortByField('status', 'ASC')"
                        (keydown)="handleToggleKeydown($event, 'status', 'ASC')"
                        *ngIf="sortColumn !== 'status'"
                      />
                      <img
                        src="./assets/images/down-chevron.svg"
                        alt="down-arrow"
                        class="h-10px ms-2"
                        (click)="sortByField('status', 'ASC')"
                        (keydown)="handleToggleKeydown($event, 'status', 'ASC')"
                        *ngIf="sort === 'DESC' && sortColumn === 'status'"
                      />
                      <img
                        src="./assets/images/up-chevron.svg"
                        alt="up-arrow"
                        class="h-10px ms-2"
                        (click)="sortByField('status', 'DESC')"
                        (keydown)="handleToggleKeydown($event, 'status', 'DESC')"
                        *ngIf="sort === 'ASC' && sortColumn === 'status'"
                      />
                    </span>
                  </th>
                  <th scope="col" resizable>
                    Approved By
                    <span>
                      <img
                        src="./assets/images/down-chevron.svg"
                        alt="down-arrow"
                        class="h-10px ms-2"
                        (click)="sortByField('approvedUser', 'ASC')"
                        (keydown)="handleToggleKeydown($event, 'approvedUser', 'ASC')"
                        *ngIf="sortColumn !== 'approvedUser'"
                      />
                      <img
                        src="./assets/images/down-chevron.svg"
                        alt="down-arrow"
                        class="h-10px ms-2"
                        (click)="sortByField('approvedUser', 'ASC')"
                        (keydown)="handleToggleKeydown($event, 'approvedUser', 'ASC')"
                        *ngIf="sort === 'DESC' && sortColumn === 'approvedUser'"
                      />
                      <img
                        src="./assets/images/up-chevron.svg"
                        alt="up-arrow"
                        class="h-10px ms-2"
                        (click)="sortByField('approvedUser', 'DESC')"
                        (keydown)="handleToggleKeydown($event, 'approvedUser', 'DESC')"
                        *ngIf="sort === 'ASC' && sortColumn === 'approvedUser'"
                      />
                    </span>
                  </th>
                  <th scope="col" resizable>
                    Equipment
                    <span>
                      <img
                        src="./assets/images/down-chevron.svg"
                        alt="down-arrow"
                        class="h-10px ms-2"
                        (click)="sortByField('equipment', 'ASC')"
                        (keydown)="handleToggleKeydown($event, 'equipment', 'ASC')"
                        *ngIf="sortColumn !== 'equipment'"
                      />
                      <img
                        src="./assets/images/down-chevron.svg"
                        alt="down-arrow"
                        class="h-10px ms-2"
                        (click)="sortByField('equipment', 'ASC')"
                        (keydown)="handleToggleKeydown($event, 'equipment', 'ASC')"
                        *ngIf="sort === 'DESC' && sortColumn === 'equipment'"
                      />
                      <img
                        src="./assets/images/up-chevron.svg"
                        alt="up-arrow"
                        class="h-10px ms-2"
                        (click)="sortByField('equipment', 'DESC')"
                        (keydown)="handleToggleKeydown($event, 'equipment', 'DESC')"
                        *ngIf="sort === 'ASC' && sortColumn === 'equipment'"
                      />
                    </span>
                  </th>
                  <th scope="col" resizable>Action</th>
                </thead>
                <tbody>
                  <tr
                    *ngFor="
                      let item of inspectionList
                        | paginate
                          : {
                              id: 'pagination1',
                              itemsPerPage: pageSize,
                              currentPage: pageNo,
                              totalItems: totalCount
                            };
                      let i = index
                    "
                  >
                    <td>
                      <div class="custom-control custom-checkbox text-black">
                        <input
                          type="checkbox"
                          class="custom-control-input"
                          id="tb2Data2_{{ i }}"
                          name="tblData1"
                          [checked]="item.isChecked"
                          (change)="setSelectedCurrentinspectionRequestItem(i)"
                          [disabled]="!item.isAllowedToEdit"
                        />
                        <label
                          class="custom-control-label c-pointer fs12"
                          for="tb2Data2_{{ i }}"
                        ></label>
                      </div>
                    </td>
                    <td>
                      <span class="c-pointer" (click)="openIdModal(item, 'current')" (keydown)="handleDownKeydown($event, item, 'current','open')"
                        ><u>{{ item.InspectionId }}</u></span
                      >
                    </td>
                    <td class="w-300px">
                      <span class="c-pointer" (click)="openIdModal(item, 'current')" (keydown)="handleDownKeydown($event, item, 'current','open')"
                        ><u>{{ item.description }}</u></span
                      >
                    </td>
                    <td (click)="openIdModal(item, 'current')" (keydown)="handleDownKeydown($event, item, 'current','open')" class="c-pointer">
                      {{ item.inspectionStart | date : 'MMM dd, yyyy, hh:mm a' }}
                    </td>
                    <td (click)="openIdModal(item, 'current')" (keydown)="handleDownKeydown($event, item, 'current','open')" class="c-pointer">
                      <div class="status" [style.background-color]="approvedBackgroundColor" [style.color]="approvedFontColor" *ngIf="item.status == 'Approved' && item.inspectionStatus == null">
                        {{ item.status }}
                      </div>
                      <div class="status" [style.background-color]="'#0078d4'" [style.color]="'#EFF1EF'" *ngIf="item.status == 'Approved' && (item.inspectionStatus === 'Pass' || item.inspectionStatus === 'Fail') ">
                        <div *ngIf="item.inspectionStatus === 'Pass'">
                          <p><img src="./assets/images/pass-tick.svg" class="w12 h8" alt="tick"/><span class="ms-2">Completed</span></p>
                        </div>
                        <div *ngIf="item.inspectionStatus === 'Fail'">
                          <p><img src="./assets/images/cross-details.svg" class="w12 h8" alt="cross"/><span class="ms-2">Completed</span></p>
                        </div>
                      </div>
                      <div class="status" [style.background-color]="rejectedBackgroundColor" [style.color]="rejectedFontColor"  *ngIf="item.status == 'Declined'">
                        {{ item.status }}
                      </div>
                      <div class="status" [style.background-color]="pendingBackgroundColor" [style.color]="pendingFontColor"*ngIf="item.status == 'Pending'">
                        {{ item.status }}
                      </div>
                      <div class="status" [style.background-color]="expiredBackgroundColor" [style.color]="expiredFontColor" *ngIf="item.status == 'Expired'">
                        {{ item.status }}
                      </div>
                      <div class="status" [style.background-color]="deliveredBackgroundColor" [style.color]="deliveredFontColor" *ngIf="item.status == 'Delivered'">
                        <div *ngIf="item.inspectionStatus === 'Pass'">
                          <p><img src="./assets/images/pass-tick.svg" class="w10 h10" alt="tick"/><span class="ms-1">Completed</span></p>
                        </div>
                        <div *ngIf="item.inspectionStatus === 'Fail'">
                          <p><img src="./assets/images/cross-details.svg" class="w7 h7" alt="cross"/><span class="ms-1">Completed</span></p>
                        </div>
                      </div>
                    </td>
                    <td
                      (click)="openIdModal(item, 'current')" (keydown)="handleDownKeydown($event, item, 'current','open')"
                      class="c-pointer"
                      *ngIf="item.approverDetails?.User?.firstName"
                    >
                      {{ item.approverDetails?.User?.firstName }}
                      {{ item?.approverDetails?.User?.lastName }}
                    </td>
                    <td
                      (click)="openIdModal(item, 'current')" (keydown)="handleDownKeydown($event, item, 'current','open')"
                      class="c-pointer"
                      *ngIf="!item.approverDetails?.User?.firstName"
                    >
                      -
                    </td>
                    <td
                      (click)="openIdModal(item, 'current')" (keydown)="handleDownKeydown($event, item, 'current','open')"
                      class="c-pointer" *ngIf="item.equipmentDetails && item.equipmentDetails?.length > 0">
                      <p class="color-grey15 fs12 mb0 fw500" *ngFor="let item1 of item.equipmentDetails">
                        {{ item1?.Equipment?.equipmentName ? item1.Equipment.equipmentName : '-' }}

                      </p>
                      </td>
                    <td
                    (click)="openIdModal(item, 'current')" (keydown)="handleDownKeydown($event, item, 'current','open')"
                      class="color-grey15 fs12 mb0 fw500 c-pointer"
                      *ngIf="!item.equipmentDetails || item.equipmentDetails?.length === 0"
                    >
                      -
                    </td>
                    <td>
                      <ul class="list-inline mb-0" *ngIf="item.isAllowedToEdit">
                        <li
                          class="list-inline-item"
                          tooltip="Edit"
                          placement="top"
                          (click)="openEditModal(item, 'current')" (keydown)="handleDownKeydown($event, item, 'current','open')"
                        >
                          <a href="javascript:void(0)"
                            ><img
                              src="./assets/images/edit.svg"
                              alt="edit"
                              class="h-15px action-icon"
                          /></a>
                        </li>
                      </ul>
                    </td>
                  </tr>
                </tbody>
                <tr *ngIf="loader == true">
                  <td colspan="7" class="text-center">
                    <div class="fs18 fw-bold cairo-regular my-5 text-black">Loading...</div>
                  </td>
                </tr>
                <tr *ngIf="loader == false && inspectionList.length == 0">
                  <td colspan="7" class="text-center">
                    <div class="fs18 fw-bold cairo-regular my-5 text-black">
                      No Records Found
                    </div>
                  </td>
                </tr>
              </table>
            </div>
            <div
              class="tab-pagination px-2"
              id="tab-pagination3"
              *ngIf="loader == false && totalCount > 25"
            >
              <div class="row">
                <div class="col-md-2 align-items-center">
                  <ul class="list-inline my-3">
                    <li class="list-inline-item">
                      <label class="fs12 color-grey4" for="showEnt">Show entries</label>
                    </li>
                    <li class="list-inline-item">
                      <select id="showEnt"
                        class="w-auto form-control fs12 color-grey4 form-control-sm"
                        (change)="changePageSize($event.target.value)"
                        ngModel="{{ pageSize }}"
                      >
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                        <option value="150">150</option>
                      </select>
                    </li>
                  </ul>
                </div>
                <div class="col-md-8 text-center">
                  <div class="my-3 position-relative d-inline-block">
                    <pagination-controls
                      id="pagination1"
                      (pageChange)="changePageNo($event)"
                      previousLabel=""
                      nextLabel=""
                    >
                    </pagination-controls>
                  </div>
                </div>
              </div>
            </div>
          </tab>
        </tabset>
      </div>
    </div>
  </section>
  <!-- modal -->
  <div id="fiter-temp3">
    <!--Filter Modal-->
    <ng-template #filter>
      <div class="modal-header border-0 pb-0">
        <h3 class="fs14 fw-bold cairo-regular color-text7 my-0">Filter</h3>
        <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef.hide()">
          <span aria-hidden="true"
            ><img src="./assets/images/modal-close.svg" alt="Modal Close"
          /></span>
        </button>
      </div>
      <div class="modal-body">
        <div class="filter-content">
          <form
            class="custom-material-form"
            id="filter-form2"
            [formGroup]="filterForm"
            (ngSubmit)="filterSubmit()"
            novalidate
          >
            <div class="row">
              <div class="col-md-12">
                <div class="input-group mb-3">
                  <input
                    type="text"
                    class="form-control fs12 material-input"
                    placeholder="Desciription"
                    formControlName="descriptionFilter"
                  />
                    <span class="input-group-text">
                      <img src="./assets/images/search-icon.svg" alt="Search" />
                    </span>
                </div>
                <div class="input-group mb-3">
                  <input
                    class="form-control fs12 material-input"
                    #dp="bsDatepicker"
                    bsDatepicker
                    formControlName="dateFilter"
                    placeholder="Inspection Date"
                    [bsConfig]="{
                      isAnimated: true,
                      showWeekNumbers: false,
                      customTodayClass: 'today'
                    }"
                  />
                </div>

                <div class="form-group mobile-wrapper">
                  <select class="form-control fs12 material-input" formControlName="companyFilter">
                    <option value="" disabled selected hidden>Company</option>
                    <option
                      *ngFor="let item of companyList"
                      value="{{ item.id }}"
                      [ngValue]="item.id"
                    >
                      {{ item.companyName }}
                    </option>
                  </select>
                </div>
                <div class="form-group">
                  <select class="form-control fs12 material-input" formControlName="memberFilter">
                    <option value="" disabled selected hidden>Responsible Persons</option>
                    <ng-container *ngFor="let item of memberList">
                      <option *ngIf="item.status === 'pending' && !item.isGuestUser" value="{{ item.id }}">
                        {{ item.User?.email }}
                      </option>
                      <option *ngIf="item.status === 'pending' && item.isGuestUser" value="{{ item.id }}">
                        {{ item.User?.email }}(Guest)
                      </option>
                      <option *ngIf="item.status === 'completed'" value="{{ item.id }}">
                        {{ item.User?.firstName }}{{ item.User?.lastName }}
                      </option>
                    </ng-container>
                  </select>
                </div>
                <div class="form-group">
                  <select class="form-control fs12 material-input" formControlName="gateFilter">
                    <option value="" disabled selected hidden>Gate</option>
                    <option *ngFor="let item of gateList" value="{{ item.id }}" [ngValue]="item.id">
                      {{ item.gateName }}
                    </option>
                  </select>
                </div>
                <div class="form-group">
                  <select class="form-control fs12 material-input" formControlName="equipmentFilter">
                    <option value="" disabled selected hidden>Equipment</option>
                    <option
                      *ngFor="let item of equipmentList"
                      value="{{ item.id }}"
                      [ngValue]="item.id"
                    >
                      {{ item.equipmentName }}
                    </option>
                  </select>
                </div>
                <div class="form-group">
                  <select
                    class="form-control fs12 material-input"
                    id="locationFilter1"
                    formControlName="locationFilter"
                  >
                    <option value="" disabled selected hidden>Location</option>
                    <option
                      *ngFor="let item of locationList"
                      value="{{ item.locationPath }}"
                      [ngValue]="item.locationPath"
                    >
                      {{ item.locationPath }}
                    </option>
                  </select>
                </div>
                <div class="form-group">
                  <select class="form-control fs12 material-input" formControlName="statusFilter">
                    <option value="" disabled selected hidden>Status</option>
                    <option *ngFor="let item of wholeStatus" value="{{ item }}">
                      {{ item }}
                    </option>
                  </select>
                </div>
                <div class="input-group mb-3">
                  <input
                    type="text"
                    class="form-control fs12 material-input"
                    placeholder="Pick From"
                    formControlName="pickFrom"
                  />
                    <span class="input-group-text">
                      <img src="./assets/images/search-icon.svg" alt="Search" />
                    </span>
                </div>
                <div class="input-group mb-3">
                  <input
                    type="text"
                    class="form-control fs12 material-input"
                    placeholder="Pick To"
                    formControlName="pickTo"
                  />
                    <span class="input-group-text">
                      <img src="./assets/images/search-icon.svg" alt="Search" />
                    </span>
                </div>
                <div class="input-group mb-3">
                  <select
                    class="form-control fs12 material-input"
                    id="inspectionStatusFilter"
                    formControlName="inspectionStatusFilter"
                  >
                    <option value="" disabled selected hidden>Inspection Status</option>
                    <option *ngFor="let item of inspectionStatusList" value="{{ item }}">
                      {{ item === 'Pass' ? 'Passed' : 'Failed' }}
                    </option>
                  </select>
                </div>
                <div class="input-group mb-3">
                  <select
                    class="form-control fs12 material-input"
                    id="inspectionTypeFilter"
                    formControlName="inspectionTypeFilter"
                  >
                    <option value="" disabled selected hidden>Inspection Type</option>
                    <option *ngFor="let item of inspectionType" value="{{ item }}">
                      {{ item }}
                    </option>
                  </select>
                </div>
                <div class="row justify-content-end">
                  <button
                    class="btn btn-orange radius20 col-4 mt-2 fs12 fw-bold cairo-regular btn-block mx-1"
                    type="submit"
                  >
                    Apply
                  </button>
                  <button
                    class="btn btn-orange radius20 fs12 col-4 mt-2 fw-bold cairo-regular btn-block mx-1"
                    type="button"
                    (click)="resetFilter()"
                  >
                    Reset
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </ng-template>
    <!--Filter Modal-->
  </div>

  <ng-template #import>
    <div class="mx-2 mx-md-5 my-5 bulkupload">
      <button
        type="button"
        class="close upload-close"
        aria-label="Close"
        (click)="closeModal(cancelConfirmation)"
      >
        <span aria-hidden="true"
          ><img src="./assets/images/modal-close.svg" alt="Modal Close"
        /></span>
      </button>
      <h1 class="fw-bold cairo-regular fs20 text-center pb-4">
        Import Multiple inspection Bookings
      </h1>
      <ngx-file-drop dropZoneLabel="Drop files here" (onFileDrop)="dropped($event)" multiple="true">
        <ng-template ngx-file-drop-content-tmp let-openFileSelector="openFileSelector">
          <div class="bulkupload-content text-center" (click)="openFileSelector()" (keydown)="openFileSelector()">
            <img src="./assets/images/file.svg" alt="Excel" />
            <p class="fs14 fw600 mb3 pt10 color-grey7">Drag & Drop your file here</p>
            <p class="fs12 color-grey8 fw500 mb3">Or</p>
            <label class="color-blue4 fs14 mb10 fw500 text-underline" for="browse">Click here to browse</label>
          </div>
        </ng-template>
      </ngx-file-drop>
      <p class="fs10 color-grey8 fw500 my-2 text-right">*Supported format is .xlsx</p>

      <div class="row upload-table py-3 m-0" *ngFor="let item of files; let i = index">
        <div class="col-1 col-md-1 ps-0">
          <img src="./assets/images/xlsx.png" alt="attached-file" class="rounded upload-img" />
        </div>
        <div class="col-9 col-md-9">
          <h1 class="fs16 ms-4 color-grey7">{{ item.relativePath }}</h1>
          <p class="ms-4 mb-0 color-grey8">
            {{ todayDate | date : 'short' }}<span class="ms-3"></span>
          </p>
        </div>
        <div
          class="col-1 col-md-2 text-right d-flex justify-content-end pe-0"
          (click)="removeFile(i)" (keydown)="handleDownKeydown($event, i, '','remove')"
        >
          <img src="./assets/images/delete.svg" alt="delete" class="h-15px c-pointer" />
        </div>
      </div>
      <div class="text-center">
        <button
          class="btn btn-orange radius20 fs12 fw-bold cairo-regular my-4 px-5"
          type="submit"
          (click)="importinspectionRequest()"
          *ngIf="files.length > 0"
          [disabled]="importSubmitted"
        >
          <em class="fa fa-spinner" aria-hidden="true" *ngIf="importSubmitted"></em>
          Done
        </button>
        <p class="color-orange c-pointer fs12" (click)="download()" (keydown)="handleDownKeydown($event, '', '','download')">
          <u>Download sample template here</u>
        </p>
      </div>
    </div>
  </ng-template>

  <!--cancel confirmation Popup-->
  <div id="confirm-popup8">
    <ng-template #cancelConfirmation>
      <div class="modal-body">
        <div class="text-center my-4">
          <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
            Are you sure you want to cancel?
          </p>
          <button
            class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
            (click)="resetForm('no')"
          >
            No
          </button>
          <button
            class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
            (click)="resetForm('yes')"
          >
            Yes
          </button>
        </div>
      </div>
    </ng-template>
  </div>
  <!--cancel confirmation Popup-->

  <!-- Edit Multiple inspection Requests -->
  <ng-template #editMultipleinspectionRequest>
    <div class="modal-header">
      <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">
        <img src="./assets/images/inspection.svg" alt="inspection" class="me-2" />Edit inspection
        Booking
      </h1>
      <button
        type="button"
        class="close pull-right"
        aria-label="Close"
        (click)="closeEditMultiplePopup()"
      >
        <span aria-hidden="true"
          ><img src="./assets/images/modal-close.svg" alt="Modal Close"
        /></span>
      </button>
    </div>
    <div class="modal-body" *ngIf="!editModalLoader">
      <!-- Edit NDR -->
      <form
        name="form"
        class="custom-material-form"
        id="inspection-edit2"
        [formGroup]="deliverEditMultipleForm"
        (ngSubmit)="openConfirmationPopup(editMultipleConfirmationPopup)"
        novalidate
      >
        <div class="row">
          <div class="col-md-6">
            <span class="fs12">The Selected Booking Id are:</span><br />
            <span class="fs12" *ngIf="selectedinspectionRequestId">{{
              selectedinspectionRequestId
            }}</span>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <div class="form-group company-select" id="company-select3">
              <ng-multiselect-dropdown
                [placeholder]="'Responsible Company'"
                [settings]="newNdrCompanyDropdownSettings"
                [data]="companyList"
                formControlName="companyItems"
                (ngModelChange)="onEditSubmitForm('companies')"
              >
              </ng-multiselect-dropdown>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group company-select" id="company-select7">
              <ng-multiselect-dropdown
                [placeholder]="'Definable Feature Of Work (Scope)'"
                [settings]="newNdrDefinableDropdownSettings"
                [data]="defineListData"
                formControlName="defineItems"
                (ngModelChange)="onEditSubmitForm('dfow')"
              >
              </ng-multiselect-dropdown>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <ul
                class="follo-switch list-group list-group-horizontal justify-content-end mnb28"
                id="switch-control3"
              >
                <li class="fs12 list-group-item border-0 px-3 py-0">Escort Needed?</li>
                <li class="list-group-item border-0 px-0 py-0">
                  <ui-switch
                    switchColor="#fff"
                    defaultBoColor="#CECECE"
                    defaultBgColor="#CECECE"
                    formControlName="escort"
                    (ngModelChange)="onEditSubmitForm('escort')"
                  >
                  </ui-switch>
                </li>
              </ul>
              <label class="fs12 fw600" for="resPerson">Responsible Person</label>
              <tag-input id="resPerson"
                formControlName="person"
                [onlyFromAutocomplete]="true"
                [placeholder]="'Responsible Person'"
                [onTextChangeDebounce]="500"
                [identifyBy]="'id'"
                [displayBy]="'email'"
                class="tag-layout"
                (ngModelChange)="onEditSubmitForm('persons')"
              >
                <tag-input-dropdown
                  [showDropdownIfEmpty]="true"
                  [displayBy]="'email'"
                  [identifyBy]="'id'"
                  [autocompleteObservable]="requestAutocompleteItems"
                >
                  <ng-template let-item="item" let-index="index">
                    {{ item.email }}
                  </ng-template>
                </tag-input-dropdown>
              </tag-input>
            </div>
          </div>
          <div class="col-md-6">
            <div class="row">
              <div class="col-md-12 pe-md-0">
                <div class="input-group mb-3">
                  <input
                    class="form-control fs10 material-input"
                    #dp="bsDatepicker"
                    bsDatepicker
                    formControlName="inspectionDate"
                    placeholder="Inspection Date"
                    [bsConfig]="{
                      isAnimated: true,
                      showWeekNumbers: false,
                      customTodayClass: 'today'
                    }"
                    (ngModelChange)="onEditSubmitForm('inspectionDate')"
                  />
                    <span class="input-group-text">
                      <img
                        src="./assets/images/date.svg"
                        class="h-12px"
                        alt="Date"
                        (click)="dp.toggle()"
                        (keydown)="dp.toggle()"
                        [attr.aria-expanded]="dp.isOpen"
                      />
                    </span>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6 pe-md-0">
                <div class="input-group mb-3 delivery-time">
                  <label class="fs12 fw600" for="fromTime">From Time:</label>
                  <timepicker id="fromTime"
                    [formControlName]="'inspectionStart'"
                    (keypress)="numberOnly($event)"
                    (ngModelChange)="changeDate($event)"
                  >
                  </timepicker>
                </div>
              </div>
              <div class="col-md-6">
                <div class="input-group mb-3 delivery-time">
                  <label class="fs12 fw600" for="toTime">To Time:</label>
                  <timepicker id="toTime"
                    [formControlName]="'inspectionEnd'"
                    (keypress)="numberOnly($event)"
                    (ngModelChange)="inspectionEndTimeChangeDetection()"
                  ></timepicker>
                </div>
              </div>
            </div>

          </div>

        </div>
        <div class="row">
          <div class="col-md-6">
            <div class="form-group" *ngIf="selectedstatuslength < 2 && statustype[0] !== 'Delivered'">
              <div class="form-group">
                <label class="fs12 fw600" for="instype">Inspection Type</label>
                <select class="form-control fs12 material-input px-0" formControlName="inspectionType"  id="instype"
                  (change)="onChangeInspectionType($event.target.value)">
                  <option value="" disabled selected hidden>Select the inspection type</option>
                  <option *ngFor="let type of inspectionType" value="{{ type }}">
                    {{ type }}
                  </option>
                </select>
              </div>
              <div *ngIf="statustype[0] === 'Pending'">
                <select
                  class="form-control fs12 material-input px-2"
                  formControlName="status"
                  (ngModelChange)="onEditSubmitForm('status')"
                >
                  <option value="" disabled selected hidden>Status Change</option>
                  <option value="Approved">Approved</option>
                  <option value="Declined">Declined</option>
                </select>
              </div>
              <div *ngIf="statustype[0] === 'Expired'">
                <select
                  class="form-control fs12 material-input px-2"
                  formControlName="status"
                  (ngModelChange)="onEditSubmitForm('status')"
                >
                  <option value="" disabled selected hidden>Status Change</option>
                  <option value="Delivered">Delivered</option>
                </select>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <select
                class="form-control fs12 material-input px-2"
                formControlName="GateId"
                (ngModelChange)="onEditSubmitForm('gate')"
              >
                <option value="" disabled selected hidden>Gate</option>
                <option *ngFor="let item of gateList" [ngValue]="item.id">
                  {{ item.gateName }}
                </option>
              </select>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <div class="form-group company-select multiple-equipment-request mb-0" id="company-select5">
              <ng-multiselect-dropdown
            [placeholder]="'Equipment'"
            [settings]="equipmentDropdownSettings"
            [data]="equipmentList"
            formControlName="EquipmentId"
            (ngModelChange)="checkEquipmentType($event)"
            >
            </ng-multiselect-dropdown>
            </div>
          </div>
        </div>
        <div class="row my-2" *ngIf="craneEquipmentTypeChosen">
            <div class="col-md-6">
              <div class="form-group mb-0">
                <label class="fs11 fw600" for="pickFrom">Picking From<sup>*</sup></label>
                <textarea id="pickFrom"
                  class="form-control fs10 radius0"
                  placeholder="Picking From"
                  rows="2"
                  formControlName="cranePickUpLocation"
                  maxlength="150"
                ></textarea>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group mb-0">
                <label class="fs11 fw600" for="pickTo">Picking To<sup>*</sup></label>
                <textarea id="pickTo"
                  class="form-control fs10 radius0"
                  placeholder="Picking To"
                  rows="2"
                  formControlName="craneDropOffLocation"
                  maxlength="150"
                ></textarea>
              </div>
            </div>
        </div>

        <div class="row my-2">
          <div class="col-12 col-md-12">
            <div class="form-check">
              <input
                class="form-check-input c-pointer"
                name="subscribe"
                type="checkbox"
                formControlName="void"
                id="subscribe"
                (change)="handleInputChange($event.target.checked)"
                (ngModelChange)="onEditSubmitForm('void')"
              />
              <label class="form-check-label fs12 fw600 color-grey15" for="inlineCheckbox2">
                Mark as Void
              </label>
            </div>
          </div>
          <div class="note--styles">
            <strong>Note:</strong>
            <span class="note-insidetext"
              > All changes done in the following fields:Responsible Company,Responsible
              Person,DFOW,Gate,and Equipment will be updated to all selected bookings.</span
            >
          </div>
        </div>
        <div class="mt-4 mb30 text-center">
          <button
            class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3 px-2rem"
            type="button"
            (click)="closeEditMultiplePopup()"
          >
            Cancel
          </button>
          <button
            class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem"
          >
            <em class="fa fa-spinner" aria-hidden="true" *ngIf="editMultipleSubmitted"></em>Submit
          </button>
        </div>
      </form>
      <!-- Edit NDR -->
    </div>
    <div class="modal-body text-center" *ngIf="editModalLoader">Loading...</div>
  </ng-template>
  <!-- Edit Multiple inspection Requests -->

  <!--Edit Multiple inspection Request Confirmation Popup-->
  <div id="confirm-popup24">
    <ng-template #editMultipleConfirmationPopup>
      <div class="modal-body">
        <div class="text-center my-4">
          <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
            The changes you made to your fields will be applied to the selected inspection Booking(s).Do
            you really want to make these changes?
          </p>
          <button
            class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
            (click)="editMultipleConfirmation('no')"
          >
            No
          </button>
          <button
            class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
            (click)="editMultipleConfirmation('yes')"
          >
            Yes
          </button>
        </div>
      </div>
    </ng-template>
  </div>
  <!--Edit Multiple inspection Request Confirmation Popup-->
