import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ReportsService } from './reports.service';
import { ApiService } from '../api_base/api.service';
import { of } from 'rxjs';
import * as FileSaver from 'file-saver';
import * as XLSX from 'xlsx';

// Mock FileSaver
jest.mock('file-saver', () => ({
  saveAs: jest.fn()
}));

// Mock XLSX
jest.mock('xlsx', () => ({
  utils: {
    json_to_sheet: jest.fn().mockReturnValue({})
  },
  write: jest.fn().mockReturnValue(new ArrayBuffer(8))
}));

describe('ReportsService', () => {
  let service: ReportsService;
  let apiServiceMock: jest.Mocked<ApiService>;

  beforeEach(() => {
    // Reset mocks between tests
    jest.clearAllMocks();

    // Create mock for ApiService
    apiServiceMock = {
      post: jest.fn(),
      get: jest.fn(),
      getExcel1: jest.fn(),
      deleteMethod: jest.fn()
    } as unknown as jest.Mocked<ApiService>;

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        ReportsService,
        { provide: ApiService, useValue: apiServiceMock }
      ]
    });

    service = TestBed.inject(ReportsService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('API methods', () => {
    it('should call deliveryReports with correct parameters', () => {
      const mockParams = { ProjectId: 1, pageSize: 10, pageNo: 1, void: 0 };
      const mockPayload = { startDate: '2023-01-01' };
      const mockResponse = { data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      service.deliveryReports(mockParams, mockPayload).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `reports/delivery_request/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.void}`,
        mockPayload
      );
    });

    it('should call craneReports with correct parameters', () => {
      const mockParams = { ProjectId: 1, pageSize: 10, pageNo: 1, void: 0 };
      const mockPayload = { startDate: '2023-01-01' };
      const mockResponse = { data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      service.craneReports(mockParams, mockPayload).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `reports/crane_request/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.void}`,
        mockPayload
      );
    });

    it('should call concreteReports with correct parameters', () => {
      const mockParams = { ProjectId: 1, pageSize: 10, pageNo: 1, void: 0 };
      const mockPayload = { startDate: '2023-01-01' };
      const mockResponse = { data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      service.concreteReports(mockParams, mockPayload).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `reports/concrete_request/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.void}`,
        mockPayload
      );
    });

    it('should call exportDeliveryRequest with correct parameters', () => {
      const mockParams = { ProjectId: 1, pageSize: 10, pageNo: 1, void: 0 };
      const mockPayload = { startDate: '2023-01-01' };
      const mockResponse = { data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      service.exportDeliveryRequest(mockParams, mockPayload).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `reports/export_delivery_request/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.void}`,
        mockPayload
      );
    });

    it('should call saveReportDeliveryRequest with correct parameters', () => {
      const mockParams = { ProjectId: 1, pageSize: 10, pageNo: 1, void: 0 };
      const mockPayload = { startDate: '2023-01-01' };
      const mockResponse = { data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      service.saveReportDeliveryRequest(mockParams, mockPayload).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `reports/saved/export_delivery_request/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.void}`,
        mockPayload
      );
    });

    it('should call exportDeliveryRequestInExcelFormat with correct parameters', () => {
      const mockParams = { ProjectId: 1, pageSize: 10, pageNo: 1, void: 0 };
      const mockPayload = { startDate: '2023-01-01' };
      const mockResponse = { data: [] };

      apiServiceMock.getExcel1.mockReturnValue(of(mockResponse));

      service.exportDeliveryRequestInExcelFormat(mockParams, mockPayload).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(apiServiceMock.getExcel1).toHaveBeenCalledWith(
        `reports/export_delivery_request/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.void}`,
        mockPayload
      );
    });

    it('should call exportCraneRequest with correct parameters', () => {
      const mockParams = { ProjectId: 1, pageSize: 10, pageNo: 1, void: 0 };
      const mockPayload = { startDate: '2023-01-01' };
      const mockResponse = { data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      service.exportCraneRequest(mockParams, mockPayload).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `reports/export_crane_request/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.void}`,
        mockPayload
      );
    });

    it('should call saveReportCraneRequest with correct parameters', () => {
      const mockParams = { ProjectId: 1, pageSize: 10, pageNo: 1, void: 0 };
      const mockPayload = { startDate: '2023-01-01' };
      const mockResponse = { data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      service.saveReportCraneRequest(mockParams, mockPayload).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `reports/saved/export_crane_request/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.void}`,
        mockPayload
      );
    });

    it('should call getRecentReports with correct parameters', () => {
      const mockPayload = { projectId: 1 };
      const mockResponse = { data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      service.getRecentReports(mockPayload).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(apiServiceMock.get).toHaveBeenCalledWith('reports/recent-reports', mockPayload);
    });

    it('should call getSavedReports with correct parameters', () => {
      const mockPayload = { projectId: 1 };
      const mockResponse = { data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      service.getSavedReports(mockPayload).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(apiServiceMock.get).toHaveBeenCalledWith('reports/saved-reports', mockPayload);
    });

    it('should call getScheduledReports with correct parameters', () => {
      const mockPayload = { projectId: 1 };
      const mockResponse = { data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      service.getScheduledReports(mockPayload).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(apiServiceMock.get).toHaveBeenCalledWith('reports/schedule', mockPayload);
    });

    it('should call deleteScheduledReports with correct parameters', () => {
      const mockQueryParams = { id: 1 };
      const mockResponse = { success: true };

      apiServiceMock.deleteMethod.mockReturnValue(of(mockResponse));

      service.deleteScheduledReports(mockQueryParams).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(apiServiceMock.deleteMethod).toHaveBeenCalledWith('reports/schedule', mockQueryParams);
    });

    it('should call getTimeLineNames with correct parameters', () => {
      const mockResponse = { data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      service.getTimeLineNames().subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(apiServiceMock.get).toHaveBeenCalledWith('reports/scheduler-timeline-names');
    });

    it('should call runNowScheduledOrSavedReport with correct parameters', () => {
      const mockQueryParams = { id: 1, ProjectId: 123 };
      const mockResponse = { data: 'http://example.com/report.pdf' };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      service.runNowScheduledOrSavedReport(mockQueryParams).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(apiServiceMock.get).toHaveBeenCalledWith('reports/rerun', mockQueryParams);
    });
  });

  describe('File handling methods', () => {
    it('should call saveAsExcelFile with correct parameters', () => {
      const buffer = new ArrayBuffer(8);
      const fileName = 'test-file';

      service.saveAsExcelFile(buffer, fileName);

      expect(FileSaver.saveAs).toHaveBeenCalled();
      // Check that the first argument is a Blob
      expect((FileSaver.saveAs as jest.Mock).mock.calls[0][0] instanceof Blob).toBeTruthy();
      // Check that the filename contains the provided name
      expect((FileSaver.saveAs as jest.Mock).mock.calls[0][1]).toContain(fileName);
      expect((FileSaver.saveAs as jest.Mock).mock.calls[0][1]).toContain('.xlsx');
    });

    it('should call saveAsPdfFile with correct parameters', () => {
      const buffer = new ArrayBuffer(8);
      const fileName = 'test-file';

      // Reset the mock to ensure clean state
      (FileSaver.saveAs as jest.Mock).mockClear();

      service.saveAsPdfFile(buffer, fileName);

      expect(FileSaver.saveAs).toHaveBeenCalled();
      // Check that the first argument is a Blob
      expect((FileSaver.saveAs as jest.Mock).mock.calls[0][0] instanceof Blob).toBeTruthy();
      // Check that the filename contains the provided name and PDF extension
      expect((FileSaver.saveAs as jest.Mock).mock.calls[0][1]).toContain(fileName);
      expect((FileSaver.saveAs as jest.Mock).mock.calls[0][1]).toContain('.pdf');
    });

    it('should export data as Excel file', () => {
      // Spy on saveAsExcelFile method
      jest.spyOn(service, 'saveAsExcelFile').mockImplementation(() => {});

      const jsonData = [{ name: 'Test', value: 123 }];
      const fileName = 'test-export';

      service.exportAsExcelFile(jsonData, fileName);

      expect(service.saveAsExcelFile).toHaveBeenCalled();
      expect(XLSX.utils.json_to_sheet).toHaveBeenCalledWith(jsonData);
    });
  });

  describe('Inspection reports', () => {
    it('should call inspectionReports with correct parameters', () => {
      const mockParams = { ProjectId: 1, pageSize: 10, pageNo: 1, void: 0 };
      const mockPayload = { startDate: '2023-01-01' };
      const mockResponse = { data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      service.inspectionReports(mockParams, mockPayload).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `reports/inspection_request/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.void}`,
        mockPayload
      );
    });

    it('should call saveReportInspectionRequest with correct parameters', () => {
      const mockParams = { ProjectId: 1, pageSize: 10, pageNo: 1, void: 0 };
      const mockPayload = { startDate: '2023-01-01' };
      const mockResponse = { data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      service.saveReportInspectionRequest(mockParams, mockPayload).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `reports/saved/export_inspection_request/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.void}`,
        mockPayload
      );
    });

    it('should call exportInspectionReport with correct parameters', () => {
      const mockParams = { ProjectId: 1, pageSize: 10, pageNo: 1, void: 0 };
      const mockPayload = { startDate: '2023-01-01' };
      const mockResponse = { data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      service.exportInspectionReport(mockParams, mockPayload).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `reports/export_inspection_request/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.void}`,
        mockPayload
      );
    });
  });

  it('should export data to Excel', () => {
    const testData = [{ name: 'Test', value: 123 }];
    const service = TestBed.inject(ReportsService);

    // Use exportAsExcelFile instead of exportToExcel
    service.exportAsExcelFile(testData, 'test-file');

    expect(XLSX.utils.json_to_sheet).toHaveBeenCalledWith(testData);
    expect(FileSaver.saveAs).toHaveBeenCalled();
  });

  it('should handle saveReportHeatMap', () => {
    const apiService = TestBed.inject(ApiService);
    const service = TestBed.inject(ReportsService);

    const spy = jest.spyOn(apiService, 'getExcel1').mockReturnValue(of({ data: 'success' }));

    const params = { ProjectId: 1, pageSize: 10, pageNo: 1, void: 0, sortOrder: 'asc' };
    const requestData = {
      reportName: 'Test Report',
      outputFormat: 'PDF'
    };

    service.saveReportHeatMap(params, requestData);

    expect(spy).toHaveBeenCalledWith(
      "reports/saved/export_heat_map/1/10/1/0/asc",
      requestData
    );
  });

  it('should handle saveReportDeliveryRequest', () => {
    const apiService = TestBed.inject(ApiService);
    const service = TestBed.inject(ReportsService);

    const spy = jest.spyOn(apiService, 'post').mockReturnValue(of({ data: 'success' }));

    const params = { ProjectId: 1, pageSize: 10, pageNo: 1, void: 0 };
    const requestData = {
      reportName: 'Delivery Report',
      outputFormat: 'PDF'
    };

    service.saveReportDeliveryRequest(params, requestData);

    expect(spy).toHaveBeenCalledWith(
      "reports/saved/export_delivery_request/1/10/1/0",
      requestData
    );
  });

  it('should handle scheduling reports', () => {
    const apiService = TestBed.inject(ApiService);
    const service = TestBed.inject(ReportsService);

    const spy = jest.spyOn(apiService, 'post').mockReturnValue(of({ data: 'success' }));

    const params = { ProjectId: 123 };
    const requestData = {
      reportName: 'Scheduled Report',
      recurrence: 'Daily'
    };

    // Use scheduleHeatMapReports instead of scheduleReport
    service.scheduleHeatMapReports(params, requestData);

    expect(spy).toHaveBeenCalledWith(
      "reports/schedule/123",
      requestData
    );
  });
});
