<section class="page-section ">
    <div class="page-inner-content">
      <div class="top-header my-3">
        <div class="row px-md-4">
          <div class="col-md-8 col-7 px-0 ">
            <button class="btn  btn-white color-orange col-md-4 col-lg-3 col-sm-5 ms-3 ms-md-0 fs12 fw-bold cairo-regular w-100 "
                   (click)="addProjects()" *ngIf="accountAdmin">Add New Project</button>
          </div>
          <div class="col-md-4 col-5 my-auto">
            <div class="top-filter">
              <ul class="list-group list-group-horizontal justify-content-end">
                <li class="list-group-item p0 border-0 bg-transparent">
                  <div class="search-icon">
                    <input class="form-control fs12 color-grey8" [ngClass]="showSearchbar ? 'input-hover-disable':
                    'input-search'" placeholder="Search with Project Name, ID?" (input)="getSearchProject($event.target.value)" ([ngModel])="search" name="search">
                    <div class="icon">
                      <img src="./assets/images/cross-close.svg" *ngIf="showSearchbar" (click)="clear()" (keydown)="handleDownKeydown($event, '','search')"alt="close-cross"/>
                      <em class="fa fa-search fs12 color-grey8" *ngIf="!showSearchbar"></em>
                    </div>
                  </div>
                </li>
                <li class="list-group-item p0 border-0 bg-transparent me-2 position-relative" >
                  <div class="filter-icon" (click)="openModal1(filter)" (keydown)="handleDownKeydown($event, filter,'modal')"><img src="./assets/images/filter.svg" class="h-12px icon" alt="Filter"></div>
                  <div class="bg-orange rounded-circle position-absolute text-white filter-count" *ngIf="filterCount>0"><p class="m-0 text-center fs10">{{filterCount}}</p></div>
                </li>
              </ul>
             </div>
          </div>
        </div>
    </div>
    <div class="page-card project-card bg-white ">
        <div class="table-responsive rounded tab-grid">
            <table class="table table-custom mb-0" aria-describedby="Emptable">
              <thead>
                  <th scope="col" resizable>Project ID</th>
                  <th scope="col" resizable>Project Name</th>
                  <th scope="col" resizable>Project Admin</th>
                  <th scope="col" resizable>Start Date</th>
                  <th scope="col" resizable>End Date</th>

              </thead>
              <tbody *ngIf="loader==false && projectList?.length>0">
                  <tr *ngFor= "let item of projectList
                  | paginate: { itemsPerPage: pageSize, currentPage: pageNo, totalItems:totalCount }; let i = index" (click)="redirectProject(item)" class="c-pointer" (keydown)="handleDownKeydown($event, item,'project')">
                      <td>{{item.id}}</td>
                      <td>{{item.projectName}}</td>
                      <td>
                        <div *ngIf="item.adminData !== '' || item.adminData !== null ||item.adminData !== undefined">{{item.adminData}}</div>
                        <div *ngIf="item.adminData === '' || item.adminData === null ||item.adminData === undefined">---</div>
                      </td>
                      <td>{{item.startDate | date:'medium'}}</td>
                      <td>{{item.endDate | date:'medium'}}</td>
                  </tr>
                </tbody>
                  <tr *ngIf="loader==true" id="loading3">
                    <td colspan=8 class="text-center">
                      <div class="fs18 fw-bold cairo-regular my-5 text-black">
                        Loading...
                      </div>
                    </td>
                  </tr>
                  <tr *ngIf="loader==false && projectList?.length==0">
                    <td colspan=8 class="text-center">
                      <div class="fs18 fw-bold cairo-regular my-5 text-black">
                        No Records Found
                      </div>
                    </td>
                  </tr>


            </table>
          </div>

          <div class="tab-pagination px-2" id="tab-pagination10" *ngIf="loader==false && totalCount > 25">
           <div class="row">
             <div class="col-md-2 align-items-center">
               <ul class="list-inline my-3">
                 <li class="list-inline-item notify-pagination">
                   <label class="fs12 color-grey4" for="showEnt">Show entries</label>
                 </li>
                   <li class="list-inline-item">
                      <select id="showEnt" class="w-auto form-select fs12 color-grey4" (change)="changePageSize($event.target.value)" [ngModel]="pageSize">
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                        <option value="150">150</option>
                     </select>
                 </li>
               </ul>
              </div>
             <div class="col-md-8 text-center" >
               <div class="my-3 position-relative d-inline-block">
                <pagination-controls (pageChange)="changePageNo($event)" previousLabel="" nextLabel="">
                </pagination-controls>
               </div>
             </div>
           </div>
         </div>
        </div>
    </div>
    </section>
    <div id="filter-temp8">
    <ng-template #filter>
        <div class="modal-header border-0 pb-0">
          <h3 class="fs12 fw-bold cairo-regular color-text7 my-0">Filter</h3>
          <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef.hide()">
            <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close"></span>
          </button>
        </div>
        <div class="modal-body">
          <div class="filter-content">
            <form class="custom-material-form"  [formGroup]="filterForm" (ngSubmit)="filterSubmit()" >
              <div class="row">
                <div class="col-md-12">
                  <div class="input-group mb-3">
                    <input type="text" class="form-control fs12   material-input" placeholder="Project Id" formControlName="idFilter">
                      <span class="input-group-text">
                        <img src="./assets/images/search-icon.svg" alt="Search">
                      </span>
                  </div>
                  <div class="input-group mb-3">
                    <input type="text" class="form-control fs12  material-input" placeholder="Project Name" formControlName="nameFilter">
                      <span class="input-group-text">
                        <img src="./assets/images/search-icon.svg" alt="Search">
                      </span>
                  </div>
                  <div class="row justify-content-end">
                    <button class="btn btn-orange radius20 col-4 mt-2 fs12 fw-bold cairo-regular w-100 mx-1"
                    type="submit">Apply</button>
                    <button class="btn btn-orange radius20 fs12 col-4 mt-2 fw-bold cairo-regular w-100 mx-1"
                    type="button" (click)="resetFilter()">Reset</button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </ng-template>
</div>
