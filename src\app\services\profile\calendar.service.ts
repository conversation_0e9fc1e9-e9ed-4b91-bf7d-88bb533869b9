import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Router } from '@angular/router';
import { ApiService } from '../api_base/api.service';

@Injectable({
  providedIn: 'root',
  })
export class CalendarService {
  public getCalendarEventData: BehaviorSubject<any> = new BehaviorSubject<any>({});

  public constructor(private readonly api: ApiService, public router: Router) {
    // constructor
  }

  public updateCalendarEvents(data: boolean): any {
    return this.getCalendarEventData.next(data);
  }

  public addCalendarEvent(params, payload): Observable<any> {
    return this.api.postMethod('calendar_settings/add_event', payload, params);
  }

  public editCalendarEvent(id, params, payload): Observable<any> {
    return this.api.putMethod(`calendar_settings/edit_event/${id}`, payload, params);
  }

  public getEventData(id, params): Observable<any> {
    return this.api.get(`calendar_settings/get_event/${id}`, params);
  }

  public getCalendarEvents(payload): Observable<any> {
    return this.api.get('calendar_settings/get_calendar_events', payload);
  }

  public getCalendarMonthEvents(payload): Observable<any> {
    return this.api.get('calendar_settings/get_calendar_month_events', payload);
  }

  public getEventNDR(params, payload): Observable<any> {
    return this.api.post(`calendar/event_NDR/${params.ProjectId}/${params.void}`, payload);
  }

  public getInspectionEventNDR(params, payload): Observable<any> {
    return this.api.post(`calendar/get_inspection_request/${params.ProjectId}/${params.void}`, payload);
  }

  public  getAllCalendarEventNDR(params, payload): Observable<any> {
    return this.api.post(`calendar/get_all_calendar/${params.ProjectId}/${params.void}`, payload);
  }

  public getDeliveryRequestWithCraneEquipmentType(params, payload): Observable<any> {
    return this.api.post(
      `calendar/get_crane_associated_request/${params.ProjectId}/${params.void}`,
      payload,
    );
  }

  public getConcreteRequest(params, payload): Observable<any> {
    return this.api.post(
      `calendar/get_concrete_request/${params.ProjectId}/${params.void}`,
      payload,
    );
  }
}
