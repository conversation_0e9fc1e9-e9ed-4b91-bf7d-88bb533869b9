import { Component, OnInit, TemplateRef } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { ProfileService } from '../services/profile/profile.service';
import { ProjectService } from '../services/profile/project.service';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  })
export class ProfileComponent implements OnInit {
  public modalRef: BsModalRef;

  public userData: any = {};

  public ProjectId: any;

  public formData: any = {};

  public submitted = false;

  public url = '';

  public subscriptionStatus = false;

  public isAccount = false;

  public ParentCompanyId: any;

  public currentTabId: number = 0;

  public constructor(private readonly modalService: BsModalService,
    private readonly titleService: Title,
    private readonly profileService: ProfileService, public projectService: ProjectService,
    private readonly toastr: ToastrService, private readonly router: Router) {
    this.titleService.setTitle('Follo - Profile');
    this.projectService.projectParent.subscribe((response14): void => {
      if (response14 !== undefined && response14 !== null && response14 !== '') {
        this.ProjectId = response14.ProjectId;
        this.ParentCompanyId = response14.ParentCompanyId;
      }
    });
    this.projectService.isProjectAccount.subscribe((response): void => {
      this.isAccount = false;
      if (response !== undefined && response !== null && response !== '') {
        this.isAccount = response;
      }
    });
    this.profileService.overViewDetail.subscribe((res): void => {
      this.userData = res;
    });
  }

  public change(): void {
    this.profileService.updatedRefreshOverview(true);
  }

  public fileChangeEvent(files: { name: any }[]): void {
    this.submitted = true;
    const extension = files[0].name.split('.')[files[0].name.split('.').length - 1];
    const realExtension = extension.toLowerCase();
    if (realExtension === 'png' || realExtension === 'jpg' || realExtension === 'jpeg') {
      this.formData = new FormData();
      this.formData.append('profile', files[0], files[0].name);
      this.profileService.uploadProfile(this.formData).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.submitted = false;
            this.userData.User.profilePic = response.data.imageUrl;
            this.profileService.updatedOverView(this.userData);
          }
        },
        error: (updatedOverViewErr): void => {
          this.submitted = false;
          if (updatedOverViewErr.message?.statusCode === 400) {
            this.showError(updatedOverViewErr);
          } else if (!updatedOverViewErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(updatedOverViewErr.message, 'OOPS!');
            this.submitted = false;
          }
        },
      });
    } else {
      this.toastr.error('Please upload *jpg,*jpeg,*png images.', 'OOPS!');
    }
  }

  public showError(err: { message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] } }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.toastr.error(errorMessage);
  }

  public ngOnInit(): void {
    this.projectService.isProjectAccount.subscribe((subscribeResponse): void => {
      this.isAccount = false;
      if (subscribeResponse !== undefined && subscribeResponse !== null && subscribeResponse !== '') {
        this.isAccount = subscribeResponse;
      }
    });
    const urlData = this.router.url.split('/')[1];
    this.url = urlData;
    this.projectService.subStatus.subscribe((res): void => {
      if (res !== undefined && res !== '') {
        this.subscriptionStatus = res.status;
        if (this.modalRef) {
          this.modalRef.hide();
        }
      }
    });
  }

  public openModal1(template: TemplateRef<any>): void {
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-md thanks-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }
}
