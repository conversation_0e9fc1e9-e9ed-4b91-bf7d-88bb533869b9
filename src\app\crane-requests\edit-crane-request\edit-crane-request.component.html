<div class="modal-header">
  <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">
    <img src="./assets/images/delivery-pop.svg" alt="Delivery" class="me-2" />Edit Crane Pick
    Booking
  </h1>
  <button type="button" class="close ms-auto" aria-label="Close" (click)="close(cancelConfirmation)">
    <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close" /></span>
  </button>
</div>
<div class="modal-body newupdate-alignments taginput--heightfix craneedit" *ngIf="!modalLoader">
  <!-- Edit NDR -->
  <form name="form" class="custom-material-form add-concrete-material-form" id="delivery-edit2"
    [formGroup]="craneEditRequest" novalidate>
    <div class="row">
      <div class="col-md-6">
        <div class="form-group">
          <label class="fs12 fw600" for="description">Description<sup>*</sup></label>
          <textarea class="form-control fs11 radius0" placeholder="Enter Description" rows="2" id="description"
            formControlName="description" maxlength="150" (ngModelChange)="onEditSubmitForm()"></textarea>
          <div class="color-red" *ngIf="editSubmitted && craneEditRequest.get('description').errors">
            <small *ngIf="craneEditRequest.get('description').errors.required">*Description is Required.</small>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group">
          <label class="fs12 fw600" for="craneid">Crane Pick Booking ID</label>
          <input type="text" class="form-control fs10 color-orange fw500 material-input ps-3" placeholder=""  id="craneid"
            formControlName="CraneRequestId" disabled="disabled" />
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-6">
        <div class="form-group company-select" id="company-select3">
          <label class="fs12 fw600"  for="rescomp">Responsible Company<sup>*</sup></label>
          <ng-multiselect-dropdown [placeholder]="'Responsible Company*'" [settings]="editNdrCompanyDropdownSettings"  id="rescomp"
            [data]="companyList" formControlName="companies" (ngModelChange)="onEditSubmitForm()">
          </ng-multiselect-dropdown>
          <div class="color-red" *ngIf="editSubmitted && craneEditRequest.get('companies').errors">
            <small *ngIf="craneEditRequest.get('companies').errors.required">*Company is Required.</small>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group company-select" id="company-select7">
          <label class="fs12 fw600"  for="defwork">Definable Feature Of Work</label>
          <ng-multiselect-dropdown [placeholder]="'Definable Feature Of Work (Scope)*'"
            [settings]="editNdrDefinableDropdownSettings" [data]="definableFeatureOfWorkList"
            formControlName="definableFeatureOfWorks" (ngModelChange)="onEditSubmitForm()"  id="defwork">
          </ng-multiselect-dropdown>
          <div class="color-red" *ngIf="editSubmitted && craneEditRequest.get('definableFeatureOfWorks').errors">
            <small *ngIf="craneEditRequest.get('definableFeatureOfWorks').errors.required">*Definable Feature Of Work is
              Required.</small>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-md-12">
        <div class="form-group">
          <label class="fs12 fw600"  for="resperson">Responsible Person<sup>*</sup></label>
          <ul class="follo-switch list-group list-group-horizontal justify-content-end float-end newfixswitch"
            id="switch-control3">
            <li class="fs12 list-group-item border-0 pe-3 escort--text py-0">Escort Needed?*</li>
            <li class="list-group-item border-0 px-0 py-0">
              <ui-switch switchColor="#fff" defaultBoColor="#CECECE" defaultBgColor="#CECECE"
                formControlName="isEscortNeeded" (ngModelChange)="onEditSubmitForm()">
              </ui-switch>
            </li>
          </ul>

          <div class="w-100 float-start">
            <tag-input formControlName="responsiblePersons" [onlyFromAutocomplete]="true" [placeholder]="' '"
              [onTextChangeDebounce]="500" [identifyBy]="'id'" [displayBy]="'email'"
              class="tag-layout newdelivery-taglayout w-100" (ngModelChange)="onEditSubmitForm()"
              (ngModelChange)="gatecheck($event, 'Person')">
              <tag-input-dropdown [showDropdownIfEmpty]="true" [displayBy]="'email'" [identifyBy]="'id'"
                [autocompleteObservable]="requestAutoEditcompleteItems" [appendToBody]="false">
                <ng-template let-item="item" let-index="index">
                  <span class="fs10">{{ item.email }}</span>
                </ng-template>
              </tag-input-dropdown>
            </tag-input>
          </div>
          <small class="color-red" *ngIf="errmemberenable">Please select an active member,the existing member is
            deactivated.</small>
          <div class="color-red" *ngIf="editSubmitted && craneEditRequest.get('responsiblePersons').errors">
            <small *ngIf="craneEditRequest.get('responsiblePersons').errors.required">*Please choose Responsible
              person.</small>
          </div>
        </div>
      </div>
    </div>


    <div class="row">
      <div class="col-md-6 primary-tooltip">
        <div class="form-group timezone-formgroup">
          <label class="fs12 fw600"  for="location">Location<span class="color-red"><sup>*</sup></span>
            <div class="dot-border-info location-border-info tooltip location-tooltip">
              <span class="fw700 info-icon fs12">i</span>
              <span class="tooltiptext tooltiptext-info">Where will the materials/equipment be installed</span>
              <div class="arrow-down"></div>
            </div>
          </label>
          <ng-multiselect-dropdown [placeholder]="'Choose Location'" [settings]="editNdrLocationDropdownSettings"  id="location"
            [data]="locationList" (onSelect)="locationSelected($event)" formControlName="LocationId"
            (ngModelChange)="onEditSubmitForm()">
          </ng-multiselect-dropdown>
          <div class="color-red" *ngIf="editSubmitted && craneEditRequest.get('LocationId').errors">
            <small *ngIf="craneEditRequest.get('LocationId').errors.required">*Location is Required.</small>
          </div>
        </div>
      </div>

      <div class="col-md-6">
        <div class="w-100 float-start mt-2">
          <label class="fs12 fw600"  for="gate">Gate<sup>*</sup></label>
          <select id="gate"
            class="form-control fs12 material-input px-2"
            formControlName="GateId"
            (ngModelChange)="onEditSubmitForm()"
            (ngModelChange)="gatecheck($event, 'Gate')"
            (change)="getBookingData()"
          >
            <option
              *ngIf="
                currentEditItem &&
                currentEditItem?.gateDetails &&
                currentEditItem?.gateDetails[0]?.Gate?.id
              "
              [ngValue]="currentEditItem?.gateDetails[0]?.Gate?.id"
              disabled
              selected
              hidden
            >
              {{ currentEditItem?.gateDetails[0]?.Gate?.gateName }}
            </option>
            <option value="" disabled selected hidden>Gate<sup>*</sup></option>
            <option *ngFor="let item of gateList" [ngValue]="item.id">{{ item.gateName }}</option>
          </select>

          <div
            class="color-red"
            *ngIf="editSubmitted && craneEditRequest.get('GateId').errors"
          >
            <small *ngIf="craneEditRequest.get('GateId').errors.required">*Gate is Required.</small>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-3">

      <div class="col-md-6">
        <div class="form-group company-select equipment-select">
          <label class="fs12 fw600 mb-2"  for="equipment">Equipment<sup>*</sup></label>

          <ng-multiselect-dropdown [placeholder]="'Equipment'" [settings]="equipmentDropdownSettings"  id="equipment"
            [data]="equipmentList" formControlName="EquipmentId" (ngModelChange)="equipmentSelected($event)">
          </ng-multiselect-dropdown>

          <div class="color-red" *ngIf="editSubmitted && craneEditRequest.get('EquipmentId').errors">
            <small *ngIf="craneEditRequest.get('EquipmentId').errors.required">*Equipment is Required.</small>
          </div>
        </div>
      </div>

      <div class="col-md-6">
        <div class="row">
          <div class="col-md-12">
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label class="fs12 fw600"  for="pickfrom">Picking From<span class="color-red"><sup>*</sup></span></label>
                  <textarea class="form-control fs10 radius0" placeholder="Picking From" rows="2" id="pickfrom"
                    formControlName="pickUpLocation" maxlength="150"></textarea>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label class="fs12 fw600"  for="pickto">Picking To<span class="color-red"><sup>*</sup></span></label>
                  <textarea class="form-control fs10 radius0" placeholder="Picking To" rows="2"  id="pickto"
                    formControlName="dropOffLocation" maxlength="150"></textarea>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>




    <div class="row mt-3">
      <div class="col-md-12">
        <app-time-slot
          #timeSlotRef
          (selectTime)="selectTime($event.start, $event.end)"
          [selectedBookingDate]="craneEditRequest.get('deliveryDate').value"
          [timeZone]="timeZone"
          [isEditMode]="true"
          [selectedId]="craneEditRequest.get('id').value"
          [selectedBookingType]="'craneRequest'"
          [equipmentId] = "craneEditRequest.get('EquipmentId').value"
          [LocationId] = "craneEditRequest.get('LocationId').value"
          [gateId] = "craneEditRequest.get('GateId').value"
        >
        </app-time-slot>
      </div>
    </div>


    <div class="row mt-3 addcalendar-details" *ngIf="seriesOption !== 1">
      <div class="col-md-6">

        <div class="col-md-12 p-0">
          <label class="fs12 fw600 mb-0"  for="recurrence">Recurrence<sup>*</sup></label>
          <div class="form-group">
            <select class="form-control fs12 material-input px-0" formControlName="recurrence"  id="recurrence"
              (change)="onRecurrenceSelect($event.target.value)">
              <option *ngFor="let type of recurrence" value="{{ type.value }}">
                {{ type.value }}
              </option>
            </select>
          </div>
          <div class="row mt-2" *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'">
            <div class="col-md-12 mt-md-0"
              *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'">
              <label class="fs12 fw600 mb-0"  for="repevery">Repeat Every</label>
            </div>
            <div class="col-md-6 mt-md-0"
              *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'">
              <div class="form-group">
                <input type="text" formControlName="repeatEveryCount"  id="repevery" class="form-control fs12 material-input p-0"
                  (input)="changeRecurrenceCount($event.target.value)" min="1" />
              </div>
            </div>

            <div class="col-md-6 mt-md-0" *ngIf="isRepeatWithSingleRecurrence">
              <div class="form-group">
                <select class="form-control fs12 material-input px-2" formControlName="repeatEveryType"
                  (change)="chooseRepeatEveryType($event.target.value, null)">
                  <option value="" disabled selected hidden>Select Recurrence</option>
                  <option *ngFor="let type of repeatWithSingleRecurrence" value="{{ type.value }}">
                    {{ type.value }}
                  </option>
                </select>
              </div>
            </div>
            <div class="col-md-4 mt-md-0" *ngIf="isRepeatWithMultipleRecurrence || showRecurrenceTypeDropdown">
              <div class="form-group">
                <select class="form-control fs12 material-input px-2" formControlName="repeatEveryType"
                  (change)="chooseRepeatEveryType($event.target.value, null)">
                  <option value="" disabled selected hidden>Select Recurrence</option>
                  <option *ngFor="let type of repeatWithMultipleRecurrence" value="{{ type.value }}">
                    {{ type.value }}
                  </option>
                </select>
              </div>
            </div>
          </div>
          <div class="row addcalendar-displaydays">
            <div class="col-md-12 pt-0" *ngIf="
                (selectedRecurrence === 'Weekly' ||
                  isRepeatWithMultipleRecurrence ||
                  isRepeatWithSingleRecurrence) &&
                selectedRecurrence !== 'Monthly' &&
                selectedRecurrence !== 'Yearly'
              ">
              <ul class="displaylists ps-0 mb-0">
                <li *ngFor="let item of weekDays; let i = index" class="fs12 list-inline-item">
                  <input type="checkbox" [disabled]="item.isDisabled" [value]="item.value" class="d-none"
                    id="days-{{ i }}" (change)="onChange($event)" [checked]="item.checked" />
                  <label for="days-{{ i }}">{{ item.display }}</label>
                </li>
                <div class="color-red" *ngIf="submitted && craneEditRequest.controls['days'].errors">
                  <small *ngIf="craneEditRequest.controls['days'].errors.required">*Required
                  </small>
                </div>
              </ul>
            </div>
          </div>
          <div class="row" *ngIf="selectedRecurrence === 'Monthly' || selectedRecurrence === 'Yearly'"
            [ngClass]="selectedRecurrence === 'Yearly' ? 'recurrence-year' : ''">
            <div class="col-md-12 mt-md-0 ps-2 pt-0 recurrance-column-day">
              <div class="w-100 float-start">
                <div class="form-check">
                  <input class="form-check-input c-pointer" type="radio" formControlName="chosenDateOfMonth"
                    id="flexRadioDefault1" [value]="1" (change)="changeMonthlyRecurrence()" />
                  <label class="form-check-label fs12 color-orange" for="flexRadioDefault1">
                    On day {{ monthlyDate }}
                  </label>
                </div>
                <div class="form-check">
                  <input class="form-check-input c-pointer" type="radio" formControlName="chosenDateOfMonth"
                    id="flexRadioDefault2" [value]="2" (change)="changeMonthlyRecurrence()" />
                  <label class="form-check-label fs12 color-orange" for="flexRadioDefault2">
                    On the
                    {{ monthlyDayOfWeek }}
                    <p *ngIf="selectedRecurrence === 'Yearly'">
                      of
                      {{ craneEditRequest.get('deliveryDate').value | date : 'LLLL' }}
                    </p>
                  </label>
                </div>
                <div class="form-check" *ngIf="enableOption">
                  <input class="form-check-input c-pointer" type="radio" formControlName="chosenDateOfMonth"
                    id="flexRadioDefault3" [value]="3" (change)="changeMonthlyRecurrence()" />
                  <label class="form-check-label fs12 color-orange" for="flexRadioDefault3">
                    On the
                    {{ monthlyLastDayOfWeek }}
                    <p *ngIf="selectedRecurrence === 'Yearly'">
                      of
                      {{ craneEditRequest.get('deliveryDate').value | date : 'LLLL' }}
                    </p>
                  </label>
                </div>
              </div>
              <div>
                <div class="color-red" *ngIf="
                    submitted &&
                    (craneEditRequest.get('monthlyRepeatType')?.errors ||
                    craneEditRequest.get('dateOfMonth')?.errors)
                  ">
                  <small *ngIf="craneEditRequest.get('monthlyRepeatType')?.errors?.required">*required</small>
                  <small *ngIf="craneEditRequest.get('dateOfMonth')?.errors?.required">*required</small>
                </div>
              </div>
            </div>
          </div>
          <div class="row addcalendar-displaydays">
            <div class="col-md-12 mt-md-0 pb-0" *ngIf="message">
              <p class="fs12 color-grey11 mb-0">
                <span class="color-red fw-bold">*</span>
                {{ message }}
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="col-md-12 float-start">
          <div class="row mt-0" *ngIf="
              selectedRecurrence === 'Daily' ||
              selectedRecurrence === 'Monthly' ||
              selectedRecurrence === 'Yearly' ||
              selectedRecurrence === 'Weekly'
            ">
            <div class="col-md-12 p-0">
              <div class="form-group">
                <label class="fs12 fw600"  for="recenddate">Recurrence End Date<sup>*</sup></label>
                <div class="input-group mb-3">
                  <input class="form-control fs10 material-input" #dp="bsDatepicker" bsDatepicker  id="recenddate"
                    formControlName="recurrenceEndDate" placeholder="Recurrence End Date *"
                    [bsConfig]="{ isAnimated: true, showWeekNumbers: false, customTodayClass: 'today' }"
                    (ngModelChange)="showMonthlyRecurrence()" [minDate]="recurrenceMinDate" /><br />

                  <div class="color-red" *ngIf="
                      editSubmitted && craneEditRequest.get('recurrenceEndDate').errors
                    ">
                    <small *ngIf="craneEditRequest.get('recurrenceEndDate').errors.required">*Recurrence End Date is
                      Required.</small>
                  </div>

                  <span class="input-group-text">
                    <img src="./assets/images/date.svg" class="h-12px" alt="Date" (click)="dp.toggle()" (keydown)="dp.toggle()"
                      [attr.aria-expanded]="dp.isOpen" />
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>

    <div class="row">
      <div class="col-md-6">
        <div class="form-group">
          <label class="fs12 fw600" for="addnotes">Additional Notes</label>
          <textarea class="form-control fs12 min-h-65px" placeholder="" rows="2" formControlName="additionalNotes"  id="addnotes"
            (ngModelChange)="onEditSubmitForm()"></textarea>
        </div>
      </div>
      <div class="col-md-6">
        <p class="fs11 color-grey17 fw500 mt-5">
          The delivery needs to be approved by Project Administrator to be added to the calendar.
          Please click on submit to send the booking for approval.
        </p>
      </div>
    </div>
    <div class="mt-4 mb30 text-center">
      <button class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3 px-2rem" type="button"
        (click)="close(cancelConfirmation)">
        Cancel
      </button>
      <button class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem"
        [disabled]="formEditSubmitted && craneEditRequest.valid" (click)="onSubmit()">
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="formEditSubmitted && craneEditRequest.valid"></em>Submit
      </button>
    </div>
  </form>
  <!-- Edit NDR -->
</div>
<div class="modal-body text-center" *ngIf="modalLoader">Loading...</div>
<!--Confirmation Popup-->
<div id="confirm-popup6">
  <ng-template #cancelConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure you want to cancel?
        </p>
        <button class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetForm('no')">
          No
        </button>
        <button class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5" (click)="resetForm('yes')">
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
<!--Recurrence Edit Confirmation Popup-->
<div id="confirm-popup7">
  <ng-template #cancelRecurrence>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">If recurrence was edited may result in the loss of
          existing booking data</p>
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">Are you sure you want to continue?</p>
        <button class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="recurrenceSubmit('no')">
          No
        </button>
        <button class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="recurrenceSubmit('yes')">
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
