import { Component, OnInit } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { AuthService } from '../services';

@Component({
  selector: 'app-logout-alert',
  templateUrl: './logout-alert.component.html',
})
export class LogoutAlertComponent implements OnInit {
  public constructor(public bsModalRef: BsModalRef, private readonly authService: AuthService) {
    /**/
  }

  public ngOnInit(): void { /* */ }

  public logout() {
    this.authService.logout();
  }
}
