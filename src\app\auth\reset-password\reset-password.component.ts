import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Title } from '@angular/platform-browser';
import { AuthService } from '../../services/auth/auth.service';
import { CustomValidators } from '../../custom-validators';

@Component({
  selector: 'app-reset-password',
  templateUrl: './reset-password.component.html',
  })
export class ResetPasswordComponent implements OnInit {
  public togglePasswordOne = true;

  public PasswordOneType = 'password';

  public togglePasswordTwo = true;

  public PasswordTwoType = 'password';

  public resetpasswordForm: UntypedFormGroup;

  public submitted = false;

  public resetToken = '';

  public formSubmitted = false;

  public response;

  public passwordError: boolean = false;

  public constructor(public router: Router,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly titleService: Title,
    private readonly api: AuthService, private readonly toastr: ToastrService) {
    this.titleService.setTitle('Follo - Reset Password');
    const token = this.router.url.split('/')[2];
    this.resetToken = token;
    this.api.checkAuthentication();
    this.checkToken();
  }

  public checkToken(): void {
    this.api.checkToken(this.resetToken).subscribe({
      next: (response): void => {
        if (response) {
          this.response = response;
        }
      },
      error: (err): void => {
        this.toastr.error(err.message, 'OOPS!');
        this.router.navigate(['/login']);
      },
    });
  }

  public ngOnInit(): void {
    const nonWhitespaceRegExp = /\S/;

    this.resetpasswordForm = this.formBuilder.group( // NOSONAR
      {
        password: [
          // '',
          // Validators.compose([Validators.required,
          //   Validators.pattern(nonWhitespaceRegExp),
          //   Validators.pattern(regex),
          //   Validators.minLength(8)]),
          null,
          Validators.compose([
            Validators.required,
            Validators.pattern(nonWhitespaceRegExp),
            // check whether the entered password has a number
            CustomValidators.patternValidator(/\d/, {
              hasNumber: true,
            }),
            // check whether the entered password has upper case letter
            CustomValidators.patternValidator(/[A-Z]/, {
              hasCapitalCase: true,
            }),
            // check whether the entered password has a lower case letter
            CustomValidators.patternValidator(/[a-z]/, {
              hasSmallCase: true,
            }),
            // check whether the entered password has a special character
            CustomValidators.patternValidator(
              /[ !@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/,
              {
                hasSpecialCharacters: true,
              },
            ),
            Validators.minLength(8),
          ]),
        ],
        confirmPassword: [
          '',
          Validators.compose([
            Validators.required, Validators.pattern(nonWhitespaceRegExp)]),
        ],
      },
      {
        validator: this.MustMatch('password', 'confirmPassword'),
      },
    );
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.togglePass(data);
    }
  }

  public passwordValid(value): any {
    const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[#?!@$%^&*-])[A-Za-z\d#?!@$%^&*-]{8,30}$/;
    if (regex.exec(value.target.value) === null) {
      this.passwordError = true;
    } else {
      this.passwordError = false;
    }
  }

  public MustMatch(controlName: string, matchingControlName: string): any {
    return (formGroup: UntypedFormGroup): any => {
      const control = formGroup.controls[controlName];
      const matchingControl = formGroup.controls[matchingControlName];
      if (matchingControl.errors && !matchingControl.errors.mustMatch) {
        return;
      }
      if (control.value !== matchingControl.value) {
        matchingControl.setErrors({ mustMatch: true });
      } else {
        matchingControl.setErrors(null);
      }
    };
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    if (this.resetpasswordForm.invalid) {
      this.formSubmitted = false;
      return;
    }
    const payload: any = this.resetpasswordForm.value;
    delete payload.confirmPassword;
    this.api.resetPassword(payload, this.resetToken).subscribe({
      next: (response): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.submitted = false;
          this.formSubmitted = false;
          this.router.navigate(['/login']);
        }
      },
      error: (resetPasswordError): void => {
        if (resetPasswordError.message?.statusCode === 400) {
          this.showError(resetPasswordError);
          this.formSubmitted = false;
        } else if (!resetPasswordError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(resetPasswordError.message, 'OOPS!');
        }
        this.submitted = false;
        this.formSubmitted = false;
      },
    });
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }

  public togglePass(id): void {
    if (id === 1) {
      this.togglePasswordOne = !this.togglePasswordOne;

      if (this.togglePasswordOne === true) {
        this.PasswordOneType = 'password';
      } else {
        this.PasswordOneType = 'text';
      }
    } else {
      this.togglePasswordTwo = !this.togglePasswordTwo;

      if (this.togglePasswordTwo === true) {
        this.PasswordTwoType = 'password';
      } else {
        this.PasswordTwoType = 'text';
      }
    }
  }
}
