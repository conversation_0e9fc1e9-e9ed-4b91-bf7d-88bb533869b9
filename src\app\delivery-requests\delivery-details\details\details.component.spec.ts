import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { ModalModule, BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { of, throwError } from 'rxjs';

import { DetailsComponent } from './details.component';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';
import { ToastrService } from 'ngx-toastr';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('DetailsComponent', () => {
  let component: DetailsComponent;
  let fixture: ComponentFixture<DetailsComponent>;
  let deliveryServiceMock: any;
  let projectServiceMock: any;
  let toastrServiceMock: any;
  let modalServiceMock: any;

  beforeEach(async () => {
    // Create mocks for services
    deliveryServiceMock = {
      DeliveryRequestId: of('123'),
      loginUser: of({ id: 1, RoleId: 2 }),
      refresh: of(true),
      refresh1: of(true),
      getDeliveryRequestDetail: jest.fn(),
      getNDRData: jest.fn(),
      updateDeliveryStatus: jest.fn()
    };

    projectServiceMock = {
      ParentCompanyId: of('456'),
      projectParent: of({ ProjectId: '789', ParentCompanyId: '456' })
    };

    toastrServiceMock = {
      success: jest.fn(),
      error: jest.fn()
    };

    modalServiceMock = {
      show: jest.fn().mockReturnValue({ hide: jest.fn() }),
      onHidden: of(true)
    };

    await TestBed.configureTestingModule({
      declarations: [DetailsComponent],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        HttpClientTestingModule,
        RouterTestingModule,
        ModalModule.forRoot(),
      ],
      providers: [
        BsModalRef,
        { provide: DeliveryService, useValue: deliveryServiceMock },
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: BsModalService, useValue: modalServiceMock }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DetailsComponent);
    component = fixture.componentInstance;

    // Initialize required properties
    component.DeliveryRequestId = '123';
    component.ParentCompanyId = '456';

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.loader).toBeTruthy();
    expect(component.toolTipContent).toBe('');
  });

  it('should get NDR data successfully', () => {
    const mockNDRData = {
      data: {
        id: '123',
        status: 'Pending',
        memberDetails: [{ Member: { User: { firstName: 'John', lastName: 'Doe' } } }]
      }
    };

    deliveryServiceMock.getNDRData.mockReturnValue(of(mockNDRData));

    component.getNDR();

    expect(deliveryServiceMock.getNDRData).toHaveBeenCalledWith({
      DeliveryRequestId: '123',
      ParentCompanyId: '456'
    });
    expect(component.NDRData).toEqual(mockNDRData.data);
    expect(component.loader).toBeFalsy();
  });

  it('should get responsible people initials', () => {
    const person = { firstName: 'John', lastName: 'Doe' };
    const result = component.getResponsiblePeople(person);

    expect(result).toBe('JD');
  });

  it('should return UU for missing name', () => {
    const person = { firstName: null, lastName: null };
    const result = component.getResponsiblePeople(person);

    expect(result).toBe('UU');
  });

  it('should set tooltip content when getting NDR data with multiple members', () => {
    const mockNDRData = {
      data: {
        memberDetails: [
          { Member: { User: { firstName: 'John', lastName: 'Doe' } } },
          { Member: { User: { firstName: 'Jane', lastName: 'Smith' } } },
          { Member: { User: { firstName: 'Bob', lastName: 'Johnson' } } },
          { Member: { User: { firstName: 'Alice', lastName: 'Brown' } } }
        ]
      }
    };

    deliveryServiceMock.getNDRData.mockReturnValue(of(mockNDRData));

    component.getNDR();

    expect(component.toolTipContent).toContain('Alice Brown');
  });

  it('should not set tooltip content when getting NDR data with few members', () => {
    const mockNDRData = {
      data: {
        memberDetails: [
          { Member: { User: { firstName: 'John', lastName: 'Doe' } } },
          { Member: { User: { firstName: 'Jane', lastName: 'Smith' } } }
        ]
      }
    };

    deliveryServiceMock.getNDRData.mockReturnValue(of(mockNDRData));

    component.getNDR();

    expect(component.toolTipContent).toBe('');
  });

  it('should unsubscribe from all subscriptions on destroy', () => {
    const unsubscribeSpy = jest.spyOn(component['subscriptions'], 'unsubscribe');

    component.ngOnDestroy();

    expect(unsubscribeSpy).toHaveBeenCalled();
  });
});
