import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { HttpParams } from '@angular/common/http';

import { ApiService } from './api.service';
import { environment } from '../../../environments/environment';
import moment from 'moment';

describe('ApiService', (): void => {
  let service: ApiService;
  let httpMock: HttpTestingController;
  let localStorageMock: { [key: string]: string } = {};

  beforeEach((): void => {
    // Setup localStorage mock
    localStorageMock = {};

    // Mock localStorage methods
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn((key) => localStorageMock[key] || null),
        setItem: jest.fn((key, value) => {
          localStorageMock[key] = value;
        }),
        removeItem: jest.fn((key) => {
          delete localStorageMock[key];
        }),
        clear: jest.fn(() => {
          localStorageMock = {};
        })
      },
      writable: true
    });

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ApiService]
    });

    service = TestBed.inject(ApiService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
    jest.clearAllMocks();
  });

  it('should be created', (): void => {
    expect(service).toBeTruthy();
  });

  it('should initialize headers correctly', (): void => {
    expect(service.header.get('Content-Type')).toBe('application/json');
    expect(service.header.get('Cache-Control')).toBe('no-cache');
    expect(service.header.get('Pragma')).toBe('no-cache');
    expect(service.header.get('Access-Control-Allow-Origin')).toBe('*');
    expect(service.header.get('timezoneoffset')).toBe(`${moment().utcOffset()}`);
  });

  describe('setauthorization', (): void => {
    it('should set authorization header when token exists', (): void => {
      // Arrange
      const mockToken = 'test-token';
      localStorageMock['token'] = mockToken;

      // Act
      service.setauthorization();

      // Assert
      expect(service.header.get('Authorization')).toBe(`JWT ${mockToken}`);
      expect(localStorage.getItem).toHaveBeenCalledWith('token');
    });
  });

  describe('get', (): void => {
    it('should make a GET request with correct URL and headers', (): void => {
      // Arrange
      const path = '/test';
      const mockResponse = { data: 'test' };
      const params = new HttpParams().set('param1', 'value1');

      // Act
      service.get(path, params).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      // Assert
      const req = httpMock.expectOne(`${environment.apiBaseUrl}${path}?param1=value1`);
      expect(req.request.method).toBe('GET');
      expect(req.request.headers.get('Content-Type')).toBe('application/json');

      req.flush(mockResponse);
    });

    it('should handle errors correctly', (): void => {
      // Arrange
      const path = '/test';
      const mockError = { status: 404, statusText: 'Not Found' };

      // Act & Assert
      service.get(path).subscribe({
        next: () => fail('should have failed with 404 error'),
        error: (error) => {
          expect(error).toBeTruthy();
        }
      });

      // Simulate response
      const req = httpMock.expectOne(`${environment.apiBaseUrl}${path}`);
      req.flush('Not Found', mockError);
    });
  });

  describe('post', (): void => {
    it('should make a POST request with correct URL, body and headers', (): void => {
      // Arrange
      const path = '/test';
      const body = { name: 'test' };
      const mockResponse = { id: 1, name: 'test' };

      // Act
      service.post(path, body).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      // Assert
      const req = httpMock.expectOne(`${environment.apiBaseUrl}${path}`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(body);
      expect(req.request.headers.get('Content-Type')).toBe('application/json');

      req.flush(mockResponse);
    });
  });

  describe('put', (): void => {
    it('should make a PUT request with correct URL, body and headers', (): void => {
      // Arrange
      const path = '/test';
      const body = { id: 1, name: 'updated' };
      const mockResponse = { id: 1, name: 'updated' };

      // Act
      service.put(path, body).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      // Assert
      const req = httpMock.expectOne(`${environment.apiBaseUrl}${path}`);
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(body);
      expect(req.request.headers.get('Content-Type')).toBe('application/json');

      req.flush(mockResponse);
    });
  });

  describe('deleteMethod', (): void => {
    it('should make a DELETE request with correct URL and headers', (): void => {
      // Arrange
      const path = '/test';
      const params = new HttpParams().set('id', '1');
      const mockResponse = { success: true };

      // Act
      service.deleteMethod(path, params).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      // Assert
      const req = httpMock.expectOne(`${environment.apiBaseUrl}${path}?id=1`);
      expect(req.request.method).toBe('DELETE');
      expect(req.request.headers.get('Content-Type')).toBe('application/json');

      req.flush(mockResponse);
    });
  });

  describe('getExcel', (): void => {
    it('should make a GET request with correct URL, headers and responseType', (): void => {
      // Arrange
      const path = '/export';
      const mockBlob = new Blob(['test'], { type: 'application/vnd.ms-excel' });

      // Act
      service.getExcel(path).subscribe(response => {
        expect(response).toEqual(mockBlob);
      });

      // Assert
      const req = httpMock.expectOne(`${environment.apiBaseUrl}${path}`);
      expect(req.request.method).toBe('GET');
      expect(req.request.headers.get('Content-Type')).toBe('application/json');
      expect(req.request.responseType).toBe('blob');

      req.flush(mockBlob);
    });
  });

  describe('getExcel1', (): void => {
    it('should make a POST request with blob responseType when exportType is EXCEL', (): void => {
      // Arrange
      const path = '/export';
      const body = { exportType: 'EXCEL', data: 'test' };
      const mockBlob = new Blob(['test'], { type: 'application/vnd.ms-excel' });

      // Act
      service.getExcel1(path, body).subscribe(response => {
        expect(response).toEqual(mockBlob);
      });

      // Assert
      const req = httpMock.expectOne(`${environment.apiBaseUrl}${path}`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(body);
      expect(req.request.responseType).toBe('blob');

      req.flush(mockBlob);
    });

    it('should make a POST request with json responseType when exportType is not EXCEL', (): void => {
      // Arrange
      const path = '/export';
      const body = { exportType: 'PDF', data: 'test' };
      const mockResponse = { data: 'test' };

      // Act
      service.getExcel1(path, body).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      // Assert
      const req = httpMock.expectOne(`${environment.apiBaseUrl}${path}`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(body);
      expect(req.request.responseType).toBe('json');

      req.flush(mockResponse);
    });
  });

  describe('requestImage', (): void => {
    it('should make a POST request with correct headers for image upload', (): void => {
      // Arrange
      const path = '/upload';
      const data = new FormData();
      data.append('file', new Blob(['test']), 'test.jpg');
      const mockResponse = { url: 'https://example.com/image.jpg' };
      const mockToken = 'test-token';

      // Set token in localStorage mock
      localStorageMock['token'] = mockToken;

      // Act
      service.requestImage(path, data).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      // Assert
      const req = httpMock.expectOne(`${environment.apiBaseUrl}${path}`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(data);
      expect(req.request.headers.get('Authorization')).toBe(`JWT ${mockToken}`);

      req.flush(mockResponse);
    });
  });

  describe('bulkUploadLocation', (): void => {
    it('should make a POST request with correct headers for bulk upload', (): void => {
      // Arrange
      const path = '/bulk-upload';
      const data = new FormData();
      data.append('file', new Blob(['test']), 'locations.csv');
      const params = new HttpParams().set('type', 'location');
      const mockResponse = { success: true, count: 10 };
      const mockToken = 'test-token';

      // Set token in localStorage mock
      localStorageMock['token'] = mockToken;

      // Act
      service.bulkUploadLocation(path, data, params).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      // Assert
      const req = httpMock.expectOne(`${environment.apiBaseUrl}${path}?type=location`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(data);
      expect(req.request.headers.get('Authorization')).toBe(`JWT ${mockToken}`);

      req.flush(mockResponse);
    });
  });

  describe('formatErrors', (): void => {
    it('should return error.error from the formatErrors method', (done): void => {
      // Arrange
      const errorResponse = {
        error: { message: 'Test error' },
        status: 400,
        statusText: 'Bad Request'
      };

      // Act & Assert - We'll test this through the get method
      service.get('/error-test').subscribe({
        next: () => done.fail('Expected an error, not success'),
        error: (error) => {
          expect(error).toEqual(errorResponse.error);
          done();
        }
      });

      // Simulate error response
      const req = httpMock.expectOne(`${environment.apiBaseUrl}/error-test`);
      req.flush(errorResponse.error, errorResponse);
    });
  });
});
