/* eslint-disable max-lines-per-function */
import {
  Component, TemplateRef, OnInit, Input,
} from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import {
  UntypedFormBuilder, UntypedFormGroup, Validators, UntypedFormArray, UntypedFormControl,
} from '@angular/forms';
import { Observable } from 'rxjs';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import moment from 'moment';
import { Router } from '@angular/router';
import { MixpanelService } from '../../../../services/mixpanel.service';
import {
  weekDays,
  recurrence,
  repeatWithSingleRecurrence,
  repeatWithMultipleRecurrence,
} from '../../../../services/common';
import { ProjectSharingService } from '../../../../services/projectSharingService/project-sharing.service';

@Component({
  selector: 'app-guest-concrete-booking',
  templateUrl: './guest-concrete-booking.component.html',
})
export class GuestConcreteBookingComponent implements OnInit {
  @Input() data: any;

  @Input() title: string;

  public concreteRequest: UntypedFormGroup;

  public submitted = false;

  public dropdownSettings: any = {};

  public modalLoader = false;

  public placementStart: Date;

  public placementEnd: Date;

  public responsibleCompanySelectedItems = [];

  public concreteSupplierDropdownSettings: IDropdownSettings = {
    singleSelection: false,
    idField: 'id',
    textField: 'companyName',
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    itemsShowLimit: 6,
    allowSearchFilter: true,
  };

  public primerOrderedDropdown = [
    { id: 1, data: 'Yes' },
    { id: 2, data: 'No' },
  ];

  public isDisabled = true;

  public authUser: any = {};

  public bindData: any;

  public ProjectId: number;

  public ParentCompanyId: number;

  public locationDropdown: any[];

  public locationDetailsDropdown: any[];

  public concreteSupplierDropdown: any[];

  public mixDesignDropdown: [];

  public pumpSizeDropdown: [];

  public formSubmitted = false;

  public ConcreteRequestId: any;

  public companyList: any = [];

  public NDRTimingChanged = false;

  public isKeepOpen = false;

  public locationList = [];

  public mixDesignList = [];

  public pumpSizeList = [];

  public pumpWorkEnd: Date;

  public pumpWorkStart: Date;

  public placementEnd1: Date;

  public selectedRecurrence = 'Does Not Repeat';

  public recurrence = recurrence;

  public repeatWithSingleRecurrence = repeatWithSingleRecurrence;

  public repeatWithMultipleRecurrence = repeatWithMultipleRecurrence;

  public weekDays: any = weekDays;

  public isRepeatWithMultipleRecurrence = false;

  public isRepeatWithSingleRecurrence = false;

  public showRecurrenceTypeDropdown = false;

  public checkform: any = new UntypedFormArray([]);

  public message = '';

  public monthlyDate = '';

  public monthlyDayOfWeek = '';

  public monthlyLastDayOfWeek = '';

  public enableOption = false;

  public valueExists = [];

  public endTime: Date;

  public startTime: Date;

  public selectedValue;

  public selectedTimeZoneValue: any;

  public dropdownSettingsTimeZone: IDropdownSettings;

  public timeZoneValues: any;

  public timezoneList: [];

  public getSelectedTimeZone;

  public defaultValue;

  public recurrenceMinDate = new Date();

  public selectedLocationId: any;

  public defaultLocationValue: any;

  public locationDropdownSettings: IDropdownSettings;

  public guestUserId: any;

  public constructor(
    private readonly modalService: BsModalService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly projectSharingServices: ProjectSharingService,
    private readonly toastr: ToastrService,
    private readonly mixpanelService: MixpanelService,
    public socket: Socket,
    public router: Router,
    private modalRef1: BsModalRef,
  ) {
    this.selectedValue = this.recurrence[0].value;
    this.ProjectId = +window.atob(localStorage.getItem('guestProjectId'));
    this.ParentCompanyId = +window.atob(localStorage.getItem('guestParentCompanyId'));
    this.guestUserId = +window.atob(localStorage.getItem('guestId'));
    this.getTimeZoneList();
    this.concreteRequestCreationForm();
    if (this.selectedValue === 'Does Not Repeat') {
      this.concreteRequest.get('repeatEveryType').setValue('');
    } else {
      this.concreteRequest.get('repeatEveryCount').setValue(1);
    }
  }

  public ngAfterViewInit(): void {
    this.getDropdownValues();
  }

  public getDropdownValues(): any {
    this.modalLoader = true;
    if (this.ProjectId) {
      const payload = {
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.projectSharingServices
        .guestGetConcreteRequestDropdownData(payload)
        .subscribe((response): void => {
          if (response.data) {
            this.ConcreteRequestId = +response.data.ConcreteRequestId;
            this.concreteRequest.get('ConcreteRequestId').setValue(this.ConcreteRequestId);
            this.locationDropdown = response.data.locationDropdown;
            this.locationDetailsDropdown = response.data.locationDetailsDropdown;
            this.concreteSupplierDropdown = response.data.concreteSupplierDropdown;
            this.mixDesignDropdown = response.data.mixDesignDropdown;
            this.pumpSizeDropdown = response.data.pumpSizeDropdown;
          }
          this.locationDropdownSettings = {
            singleSelection: true,
            idField: 'id',
            textField: 'locationPath',
            allowSearchFilter: true,
            closeDropDownOnSelection: true,
          };
          if (this.locationDropdown.length > 0) {
            const getChosenLocation: any = this.locationDropdown.filter(
              (obj: any): any => obj.isDefault === true,
            );
            if (getChosenLocation) {
              this.concreteRequest.get('LocationId').patchValue(getChosenLocation);
              this.selectedLocationId = getChosenLocation[0]?.id;
              this.defaultLocationValue = getChosenLocation;
            }
          }
          const params = {
            ProjectId: this.ProjectId,
            ParentCompanyId: this.ParentCompanyId,
            id: this.guestUserId,
          };
          this.projectSharingServices.guestGetMemberRole(params).subscribe((res): void => {
            if (res !== undefined && res !== null && res !== '') {
              this.authUser = res.data;
              const loggedInUser = this.concreteSupplierDropdown.filter(
                (a): any => a.id === this.authUser.CompanyId,
              );
              this.responsibleCompanySelectedItems = loggedInUser;
              let email;
              if (this.authUser?.User?.lastName != null) {
                email = `${this.authUser.User.firstName} ${this.authUser?.User?.lastName} (${this.authUser.User.email})`;
              } else {
                email = `${this.authUser.User.firstName} (${this.authUser.User.email})`;
              }
              const newMemberList = [
                {
                  email,
                  id: this.authUser.id,
                  readonly: true,
                },
              ];
              this.concreteRequest.get('responsiblePersons').patchValue(newMemberList);
              this.modalLoader = false;
            }
          });
        });
    }
  }

  public deliveryEndTimeChangeDetection(): void {
    this.NDRTimingChanged = true;
  }

  public getSelectedDate(): void {
    const getData = this.data;
    if (getData) {
      if (getData.date && getData.currentView === 'Month') {
        this.concreteRequest
          .get('concretePlacementDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00'));
        this.concreteRequest
          .get('pumpOrderedDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00'));
        this.setDefaultDateAndTime(this.concreteRequest.get('concretePlacementDate').value);
      }
      if (
        (getData.date && getData.currentView === 'Week')
        || (getData.date && getData.currentView === 'Day')
      ) {
        this.concreteRequest
          .get('concretePlacementDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00:00'));
        this.concreteRequest
          .get('concretePlacementStart')
          .setValue(moment(getData.date, 'YYYY-MM-DD hh:mm:ss').format());
        this.concreteRequest
          .get('pumpOrderedDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00:00'));
        this.concreteRequest
          .get('concretePlacementEnd')
          .setValue(moment(getData.date, 'YYYY-MM-DD hh:mm:ss').add(30, 'minutes').format());
        this.concreteRequest
          .get('pumpWorkStart')
          .setValue(moment(getData.date, 'YYYY-MM-DD hh:mm:ss').format());
        this.concreteRequest
          .get('pumpWorkEnd')
          .setValue(moment(getData.date, 'YYYY-MM-DD hh:mm:ss').add(30, 'minutes').format());
      }
    } else {
      this.setDefaultDateAndTime(null);
    }
  }

  public setDefaultDateAndTime(date): void {
    const setStartTime = 7;
    this.placementStart = new Date();
    this.placementEnd = new Date();
    if (date) {
      this.placementStart = new Date(date);
      this.placementEnd = new Date(date);
    }
    if (!date) {
      const newDate = moment().format('MM/DD/YYYY');
      this.concreteRequest.get('concretePlacementDate').setValue(newDate);
      this.concreteRequest.get('pumpOrderedDate').setValue(newDate);
    }
    this.placementStart.setHours(setStartTime);
    this.placementStart.setMinutes(0);
    this.placementEnd.setHours(setStartTime + 1);
    this.placementEnd.setMinutes(0);
    this.pumpWorkStart = this.placementStart;
    this.pumpWorkEnd = this.placementEnd;
    this.concreteRequest.get('concretePlacementStart').setValue(this.placementStart);
    this.concreteRequest.get('concretePlacementEnd').setValue(this.placementEnd);
    this.concreteRequest.get('pumpWorkStart').setValue(this.pumpWorkStart);
    this.concreteRequest.get('pumpWorkEnd').setValue(this.pumpWorkEnd);
  }

  public ngOnInit(): void {
    this.getSelectedDate();
  }

  public close(template: TemplateRef<any>): void {
    if (
      (this.concreteRequest.touched && this.concreteRequest.dirty)
      || (this.concreteRequest.get('concreteSupplier').dirty
        && this.concreteRequest.get('concreteSupplier').value
        && this.concreteRequest.get('concreteSupplier').value.length > 0)
      || this.NDRTimingChanged
      || (this.concreteRequest.get('mixDesign').dirty
        && this.concreteRequest.get('mixDesign').value
        && this.concreteRequest.get('mixDesign').value.length > 0)
      || (this.concreteRequest.get('pumpSize').dirty
        && this.concreteRequest.get('pumpSize').value
        && this.concreteRequest.get('pumpSize').value.length > 0)
    ) {
      this.openConfirmationModalPopup(template);
    } else {
      this.resetForm('yes');
    }
  }

  public openConfirmationModalPopup(template): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public QuantitynumberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode === 32 || charCode === 46) {
      return true;
    }
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public changeDate(event: any): void {
    if (!this.modalLoader) {
      const startTime = new Date(event).getHours();
      const minutes = new Date(event).getMinutes();
      this.placementEnd = new Date();
      this.placementEnd.setHours(startTime + 1);
      this.placementEnd.setMinutes(minutes);
      this.concreteRequest.get('concretePlacementEnd').setValue(this.placementEnd);
      this.NDRTimingChanged = true;
    }
  }

  public changeDate1(event: any): void {
    if (!this.modalLoader) {
      const startTime = new Date(event).getHours();
      const minutes = new Date(event).getMinutes();
      this.placementEnd1 = new Date();
      this.placementEnd1.setHours(startTime + 1);
      this.placementEnd1.setMinutes(minutes);
      this.concreteRequest.get('pumpWorkEnd').setValue(this.placementEnd1);
      this.NDRTimingChanged = true;
    }
  }

  public createConcreteRequest(payload): void {
    this.projectSharingServices.guestCreateConcreteRequest(payload).subscribe({
      next:
      (response: any): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.mixpanelService.addGuestUserMixpanelEvents('Guest Created New Concrete Booking');
          this.formReset();
          this.concreteRequest.reset();
          this.resetForm('yes');
          this.NDRTimingChanged = false;
          this.isRequestToMember();
        }
      },
      error: (NDRCreateHistoryError): void => {
        this.formReset();
        this.NDRTimingChanged = false;
        if (NDRCreateHistoryError.message?.statusCode === 400) {
          this.showError(NDRCreateHistoryError);
        } else if (!NDRCreateHistoryError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else if (NDRCreateHistoryError.message.includes('overlaps')) {
          this.toastr.error(NDRCreateHistoryError.message, 'OOPS!');
        } else {
          this.toastr.error(NDRCreateHistoryError.message, 'OOPS!');
        }
      },
    });
  }

  public checkLocationDuplication(data): void {
    if (data && data.length >= 1) {
      const valueArr = data.map((item): any => item.location);
      if (valueArr && valueArr.length > 0) {
        const isDuplicate = valueArr.some((item, idx): any => valueArr.indexOf(item) !== idx);
        if (isDuplicate) {
          this.locationList.pop();
        }
      }
    }
  }

  public checkMixDesignDuplication(data): void {
    if (data && data.length >= 1) {
      const valueArr = data.map((item): any => item.mixDesign);
      if (valueArr && valueArr.length > 0) {
        const isDuplicate = valueArr.some((item, idx): any => valueArr.indexOf(item) !== idx);
        if (isDuplicate) {
          this.mixDesignList.pop();
        }
      }
    }
  }

  public checkPumpSizeDuplication(data): void {
    if (data && data.length >= 1) {
      const valueArr = data.map((item): any => item.pumpSize);
      if (valueArr && valueArr.length > 0) {
        const isDuplicate = valueArr.some((item, idx): any => valueArr.indexOf(item) !== idx);
        if (isDuplicate) {
          this.pumpSizeList.pop();
        }
      }
    }
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public placementDate(): void {
    this.concreteRequest
      .get('pumpOrderedDate')
      .setValue(this.concreteRequest.get('concretePlacementDate').value);
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    const persons = [];
    if (this.concreteRequest.invalid) {
      this.formSubmitted = false;
      return;
    }
    const formValue = this.concreteRequest.value;
    const newTimeZoneDetails = formValue.TimeZoneId;
    const placementDate = new Date(formValue.concretePlacementDate);
    const EndDate = new Date(formValue.endDate);
    const startHours = new Date(formValue.concretePlacementStart).getHours();
    const startMinutes = new Date(formValue.concretePlacementStart).getMinutes();
    const placementStart = this.convertStart(placementDate, startHours, startMinutes);
    const endHours = new Date(formValue.concretePlacementEnd).getHours();
    const endMinutes = new Date(formValue.concretePlacementEnd).getMinutes();
    const startPicker = moment(formValue.concretePlacementStart).format('HH:mm');
    const endPicker = moment(formValue.concretePlacementEnd).format('HH:mm');
    const weekStartDate = moment(formValue.concretePlacementDate).format('YYYY MM DD 00:00:00');
    const weekEndDate = formValue.recurrence !== 'Does Not Repeat'
      ? moment(formValue.endDate).format('YYYY MM DD 00:00:00')
      : moment(formValue.concretePlacementDate).format('YYYY MM DD 00:00:00');
    const placementEnd = formValue.recurrence !== 'Does Not Repeat'
      ? this.convertStart(EndDate, endHours, endMinutes)
      : this.convertStart(placementDate, endHours, endMinutes);
    if (formValue.isPumpRequired) {
      if (
        moment(formValue.pumpWorkStart).format('HH:mm')
        === moment(formValue.pumpWorkEnd).format('HH:mm')
      ) {
        this.toastr.error('Pump Show up Time and Completion Time should not be the same');
        this.formReset();
        return;
      }
      if (
        moment(formValue.pumpWorkStart).format('HH:mm')
        > moment(formValue.pumpWorkEnd).format('HH:mm')
      ) {
        this.toastr.error('Please enter Pump Show up Time lesser than Pump Completion Time');
        this.formReset();
        return;
      }
    }
    this.createPlacement({
      formValue,
      placementStart,
      placementEnd,
      responsbilePersonsData: persons,
      newTimeZoneDetails,
      startPicker,
      endPicker,
      weekStartDate,
      weekEndDate,
    });
  }

  public locationSelected(data): void {
    const getChosenLocation = this.locationDropdown.find((obj: any): any => +obj.id === +data.id);
    this.selectedLocationId = getChosenLocation.id;
  }

  public createPlacement(params: {
    formValue;
    placementStart;
    placementEnd;
    responsbilePersonsData;
    newTimeZoneDetails;
    startPicker;
    endPicker;
    weekStartDate;
    weekEndDate;
  }): void {
    const {
      formValue,
      placementStart,
      placementEnd,
      responsbilePersonsData,
      newTimeZoneDetails,
      startPicker,
      endPicker,
      weekStartDate,
      weekEndDate,
    } = params;

    const payload = this.preparePlacementData({
      formValue,
      placementStart,
      placementEnd,
      responsbilePersonsData,
      newTimeZoneDetails,
      startPicker,
      endPicker,
      weekStartDate,
      weekEndDate,
    });

    if (!payload) {
      this.formReset();
      return;
    }

    if (!this.validatePlacementPayload(payload)) {
      this.formReset();
      return;
    }

    this.createConcreteRequest(payload);
  }

  public preparePlacementData({
    formValue,
    placementStart,
    placementEnd,
    responsbilePersonsData,
    newTimeZoneDetails,
    startPicker,
    endPicker,
    weekStartDate,
    weekEndDate,
  }) {
    if (!formValue.responsiblePersons || formValue.responsiblePersons.length === 0) {
      this.toastr.error('Responsible Person is required');
      return null;
    }

    formValue.responsiblePersons.forEach((p) => responsbilePersonsData.push(p.id));

    const concreteSupplier = (formValue.concreteSupplier || []).map(s => s.id);
    const mixDesign = (formValue.mixDesign || []).map(md => ({
      id: Number(md.id) && md.id !== md.mixDesign ? md.id : null,
      mixDesign: md.mixDesign,
      chosenFromDropdown: Number(md.id) && md.id !== md.mixDesign,
    }));

    const pumpSize = (formValue.pumpSize || []).map(p => ({
      id: Number(p.id) && p.id !== p.pumpSize ? p.id : null,
      pumpSize: p.pumpSize,
      chosenFromDropdown: Number(p.id) && p.id !== p.pumpSize,
    }));

    if (newTimeZoneDetails && newTimeZoneDetails.length > 0 && formValue.TimeZoneId.length > 0) {
      formValue.TimeZoneId.forEach((tz) => {
        this.timeZoneValues = tz.id;
      });
    }

    if (this.checkStringEmptyValues(formValue)) {
      return null;
    }

    const isPumpRequired = formValue.isPumpRequired ?? false;
    const pumpWorkStart = isPumpRequired
      ? moment(formValue.pumpWorkStart).format('HH:mm')
      : null;
    const pumpWorkEnd = isPumpRequired
      ? moment(formValue.pumpWorkEnd).format('HH:mm')
      : null;

    const payload = {
      location: formValue.location,
      LocationId: this.selectedLocationId,
      description: formValue.description,
      ProjectId: this.ProjectId,
      userId: this.guestUserId,
      concreteSupplier,
      notes: formValue.notes,
      concretePlacementStart: placementStart,
      concretePlacementEnd: placementEnd,
      isPumpConfirmed: formValue.isPumpConfirmed ?? false,
      isPumpRequired,
      isConcreteConfirmed: formValue.isConcreteConfirmed ?? false,
      ParentCompanyId: this.ParentCompanyId,
      responsiblePersons: responsbilePersonsData,
      concreteOrderNumber: formValue.concreteOrderNumber,
      truckSpacingHours: formValue.truckSpacingHours,
      mixDesign,
      slump: formValue.slump,
      concreteQuantityOrdered: formValue.concreteQuantityOrdered,
      concreteConfirmedOn: formValue.concreteConfirmedOn,
      pumpSize: isPumpRequired ? pumpSize : [],
      pumpLocation: isPumpRequired ? formValue.pumpLocation : null,
      pumpOrderedDate: isPumpRequired
        ? moment(formValue.pumpOrderedDate).format('MM/DD/YYYY')
        : null,
      pumpWorkStart,
      pumpWorkEnd,
      pumpConfirmedOn: formValue.pumpConfirmedOn,
      requestType: 'concreteRequest',
      primerForPump: formValue.primerForPump,
      ConcreteRequestId: formValue.ConcreteRequestId,
      recurrence: formValue.recurrence,
      chosenDateOfMonth: formValue.chosenDateOfMonth === 1,
      dateOfMonth: formValue.dateOfMonth,
      monthlyRepeatType: formValue.monthlyRepeatType,
      days: formValue.days ? this.sortWeekDays(formValue.days) : [],
      repeatEveryType: formValue.repeatEveryType ?? null,
      repeatEveryCount: formValue.repeatEveryCount?.toString() ?? null,
      TimeZoneId: this.timeZoneValues,
      startPicker,
      endPicker,
    };

    if (payload.recurrence === 'Monthly' || payload.recurrence === 'Yearly') {
      payload.dateOfMonth = this.monthlyDate;
    }

    if (payload.recurrence) {
      payload.startPicker = startPicker;
      payload.endPicker = endPicker;
      payload.concretePlacementStart = weekStartDate;
      payload.concretePlacementEnd = weekEndDate;
    }

    return payload;
  }

  public validatePlacementPayload(payload): boolean {
    if (payload.startPicker === payload.endPicker) {
      this.toastr.error(
        'Placement Start Time and Anticipated Completion Time should not be the same',
      );
      return false;
    }

    if (payload.startPicker > payload.endPicker) {
      this.toastr.error(
        'Please enter Placement Start Time lesser than Anticipated Completion Time',
      );
      return false;
    }

    if (
      payload.recurrence !== 'Does Not Repeat'
      && new Date(payload.concretePlacementEnd) <= new Date(payload.concretePlacementStart)
    ) {
      this.toastr.error('Please enter End Date greater than Start Date');
      this.formSubmitted = false;
      this.submitted = false;
      return false;
    }

    return true;
  }


  public sortWeekDays(data: any[]): any {
    const order = {
      Sunday: 1,
      Monday: 2,
      Tuesday: 3,
      Wednesday: 4,
      Thursday: 5,
      Friday: 6,
      Saturday: 7,
    };

    if (data.length > 0) {
      return data.sort((a, b): any => order[a] - order[b]);
    }
  }

  public checkStringEmptyValues(formValue): boolean {
    if (formValue.description.trim() === '') {
      this.toastr.error('Please enter valid description', 'OOPS!');
      return true;
    }

    if (formValue.isPumpRequired && formValue.pumpLocation.trim() === '') {
      this.toastr.error('Please enter valid pump location', 'OOPS!');
      return true;
    }

    return false;
  }

  public formReset(): void {
    this.formSubmitted = false;
    this.submitted = false;
  }

  public convertStart(placementDate, startHours, startMinutes): string {
    const fullYear = placementDate.getFullYear();
    const fullMonth = placementDate.getMonth();
    const date = placementDate.getDate();
    const placementNewStart = new Date(fullYear, fullMonth, date, startHours, startMinutes);
    const placementStart = placementNewStart.toUTCString();
    return placementStart;
  }

  public resetForm(action): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.submitted = false;
      this.formSubmitted = false;
      this.concreteRequest.reset();
      this.NDRTimingChanged = false;
      this.getDropdownValues();
      this.router.navigate([window.atob(localStorage.getItem('url'))]);
    }
  }

  public focuslost(): void {
    this.isKeepOpen = false;
  }

  public focus(): void {
    this.isKeepOpen = true;
  }

  public concreteRequestCreationForm(): void {
    this.concreteRequest = this.formBuilder.group({
      ConcreteRequestId: ['', Validators.compose([Validators.required])],
      location: [''],
      responsiblePersons: ['', Validators.compose([Validators.required])],
      description: ['', Validators.compose([Validators.required])],
      concreteSupplier: ['', Validators.compose([Validators.required])],
      concreteOrderNumber: [''],
      truckSpacingHours: [''],
      notes: [''],
      primerForPump: [''],
      mixDesign: [''],
      slump: [''],
      concreteQuantityOrdered: [''],
      concreteConfirmedOn: [''],
      isConcreteConfirmed: ['', Validators.compose([Validators.required])],
      pumpSize: [''],
      pumpLocation: [''],
      pumpOrderedDate: [''],
      pumpWorkStart: [''],
      pumpWorkEnd: [''],
      pumpConfirmedOn: [''],
      isPumpConfirmed: [''],
      isPumpRequired: ['', Validators.compose([Validators.required])],
      concretePlacementDate: ['', Validators.compose([Validators.required])],
      concretePlacementStart: ['', Validators.compose([Validators.required])],
      concretePlacementEnd: ['', Validators.compose([Validators.required])],
      recurrence: [''],
      repeatEveryCount: [''],
      repeatEveryType: [''],
      days: new UntypedFormArray([]),
      startTime: [''],
      endTime: [''],
      chosenDateOfMonth: [false, ''],
      dateOfMonth: [''],
      monthlyRepeatType: [''],
      endDate: [''],
      TimeZoneId: ['', Validators.compose([Validators.required])],
      LocationId: ['', Validators.compose([Validators.required])],
    });
    this.concreteRequest.get('isPumpRequired').setValue(false);
    this.concreteRequest.get('isPumpConfirmed').setValue(false);
    this.concreteRequest.get('isConcreteConfirmed').setValue(false);

    this.concreteRequest.get('concretePlacementStart').setValue(this.placementStart);
    this.concreteRequest.get('concretePlacementEnd').setValue(this.placementEnd);
    this.concreteRequest.get('pumpWorkStart').setValue(this.pumpWorkStart);
    this.concreteRequest.get('pumpWorkEnd').setValue(this.pumpWorkEnd);
    this.concreteRequest.get('recurrence').setValue(this.selectedValue);
    this.formControlValueChanged();
    this.formControlValueChanged1();
    this.setCurrentTiming();
  }

  public formControlValueChanged(): void {
    const pumpSize = this.concreteRequest.get('pumpSize');
    const pumpLocation = this.concreteRequest.get('pumpLocation');
    const pumpOrderedDate = this.concreteRequest.get('pumpOrderedDate');
    const pumpWorkStart = this.concreteRequest.get('pumpWorkStart');
    const pumpWorkEnd = this.concreteRequest.get('pumpWorkEnd');
    this.concreteRequest.get('isPumpRequired').valueChanges.subscribe((value: boolean): void => {
      if (value === true) {
        this.concreteRequest.get('isPumpConfirmed').setValue(null);
        pumpSize.setValidators([Validators.required]);
        pumpLocation.setValidators([Validators.required]);
        pumpOrderedDate.setValidators([Validators.required]);
        pumpWorkStart.setValidators([Validators.required]);
        pumpWorkEnd.setValidators([Validators.required]);
      } else if (value === false || value === null) {
        pumpSize.clearValidators();
        pumpLocation.clearValidators();
        pumpOrderedDate.clearValidators();
        pumpWorkStart.clearValidators();
        pumpWorkEnd.clearValidators();
      }
      pumpSize.updateValueAndValidity();
      pumpLocation.updateValueAndValidity();
      pumpOrderedDate.updateValueAndValidity();
      pumpWorkStart.updateValueAndValidity();
      pumpWorkEnd.updateValueAndValidity();
    });
  }

  public requestAutoEditcompleteItems = (text: string): Observable<any> => {
    const param = {
      ProjectId: this.ProjectId,
      search: text,
      ParentCompanyId: this.ParentCompanyId,
    };
    return this.projectSharingServices.guestSearchNewMember(param);
  };

  public changeConcreteConfirmed(data): void {
    if (data) {
      this.concreteRequest.get('isConcreteConfirmed').setValue(true);
      this.concreteRequest.get('concreteConfirmedOn').setValue(new Date());
    } else {
      this.concreteRequest.get('isConcreteConfirmed').setValue(null);
      this.concreteRequest.get('concreteConfirmedOn').setValue(null);
    }
  }

  public changePumpConfirmed(data): void {
    if (data) {
      this.concreteRequest.get('isPumpConfirmed').setValue(true);
      this.concreteRequest.get('pumpConfirmedOn').setValue(new Date());
    } else {
      this.concreteRequest.get('isPumpConfirmed').setValue(null);
      this.concreteRequest.get('pumpConfirmedOn').setValue(null);
    }
  }

  public closeDropdownEditPopup(): void {
    this.modalRef1.hide();
  }

  public formControlValueChanged1(): void {
    this.concreteRequest.get('repeatEveryType').valueChanges.subscribe((value: string): void => {
      const days = this.concreteRequest.get('days');
      const chosenDateOfMonth = this.concreteRequest.get('chosenDateOfMonth');
      const dateOfMonth = this.concreteRequest.get('dateOfMonth');
      const monthlyRepeatType = this.concreteRequest.get('monthlyRepeatType');
      if (value === 'Week' || value === 'Day' || value === 'Weeks') {
        days.setValidators([Validators.required]);
      } else {
        days.clearValidators();
      }
      if (value === 'Month' || value === 'Months' || value === 'Year' || value === 'Years') {
        if (this.concreteRequest.get('chosenDateOfMonth').value === 1) {
          dateOfMonth.setValidators([Validators.required]);
          monthlyRepeatType.clearValidators();
        } else {
          monthlyRepeatType.setValidators([Validators.required]);
          dateOfMonth.clearValidators();
        }
      } else {
        chosenDateOfMonth.clearValidators();
        dateOfMonth.clearValidators();
        monthlyRepeatType.clearValidators();
      }
      chosenDateOfMonth.updateValueAndValidity();
      dateOfMonth.updateValueAndValidity();
      monthlyRepeatType.updateValueAndValidity();
      days.updateValueAndValidity();
    });
  }

  public setCurrentTiming(): void {
    const newDate = moment().format('MM-DD-YYYY');
    const hours = moment(new Date()).format('HH');
    this.startTime = new Date();
    this.startTime.setHours(+hours);
    this.startTime.setMinutes(0);
    this.endTime = new Date();
    this.endTime.setHours(+hours);
    this.endTime.setMinutes(30);
    if (!this.concreteRequest.get('endDate')?.value) {
      this.concreteRequest.get('endDate').setValue(newDate);
    }
    this.changeMonthlyRecurrence();
  }

  public chooseRepeatEveryType(value): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    if (value === 'Day' || value === 'Days') {
      this.concreteRequest.get('recurrence').setValue('Daily');
    }
    if (value === 'Week' || value === 'Weeks') {
      this.concreteRequest.get('recurrence').setValue('Weekly');
    }
    if (value === 'Month' || value === 'Months') {
      this.concreteRequest.get('recurrence').setValue('Monthly');
    }
    if (value === 'Year' || value === 'Years') {
      this.concreteRequest.get('recurrence').setValue('Yearly');
    }
    if (value === 'Day' || value === 'Days') {
      this.checkform = this.concreteRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day: any): any => {
        const dayObj = day;
        dayObj.checked = true;
        dayObj.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj.value));
        return dayObj;
      });
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.showRecurrenceTypeDropdown = true;
    }
    if (value === 'Week' || value === 'Weeks') {
      this.weekDays = this.weekDays.map((day15: any): void => {
        const dayObj15 = day15;
        if (dayObj15.value === 'Monday') {
          dayObj15.checked = true;
        } else {
          dayObj15.checked = false;
        }
        dayObj15.isDisabled = false;
        return dayObj15;
      });
      this.checkform = this.concreteRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (value === 'Day' || value === 'Week' || value === 'Month' || value === 'Year') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showRecurrenceTypeDropdown = false;
    }
    if (value === 'Days' || value === 'Weeks' || value === 'Months' || value === 'Years') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.showRecurrenceTypeDropdown = false;
    }
    if (this.concreteRequest.get('repeatEveryCount').value > 1) {
      this.showRecurrenceTypeDropdown = true;
      this.isRepeatWithMultipleRecurrence = false;
    }
    this.selectedRecurrence = this.concreteRequest.get('recurrence').value;
    this.occurMessage();
  }

  public occurMessage(): void {
    this.message = '';
    if (this.concreteRequest.get('repeatEveryType').value === 'Day') {
      this.message = 'Occurs every day';
    }
    if (this.concreteRequest.get('repeatEveryType').value === 'Days') {
      if (+this.concreteRequest.get('repeatEveryCount').value === 2) {
        this.message = 'Occurs every other day';
      } else {
        this.message = `Occurs every ${this.concreteRequest.get('repeatEveryCount').value} days`;
      }
    }
    if (this.concreteRequest.get('repeatEveryType').value === 'Week') {
      let weekDays = '';
      this.weekDays.map((dayObj1: any): any => {
        if (dayObj1.checked) {
          weekDays = `${weekDays + dayObj1.value},`;
        }
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message += `Occurs every ${weekDays}`;
    }
    if (this.concreteRequest.get('repeatEveryType').value === 'Weeks') {
      let weekDays = '';
      this.weekDays.map((dayObj2: any): any => {
        if (dayObj2.checked) {
          weekDays = `${weekDays + dayObj2.value},`;
        }
        return false;
      });
      if (+this.concreteRequest.get('repeatEveryCount').value === 2) {
        this.message = `Occurs every other ${weekDays}`;
      } else {
        this.message = `Occurs every ${
          this.concreteRequest.get('repeatEveryCount').value
        } weeks on ${weekDays}`;
      }
      weekDays = weekDays.replace(/,\s*$/, '');
    }

    this.getRepeatEveryType();

    if (this.message) {
      this.message += ` until ${moment(this.concreteRequest.get('endDate').value).format(
        'MMMM DD, YYYY',
      )}`;
    }
  }

  public getRepeatEveryType(): void {
    if (
      this.concreteRequest.get('repeatEveryType').value === 'Month'
      || this.concreteRequest.get('repeatEveryType').value === 'Months'
      || this.concreteRequest.get('repeatEveryType').value === 'Year'
      || this.concreteRequest.get('repeatEveryType').value === 'Years'
    ) {
      if (this.concreteRequest.get('chosenDateOfMonth').value === 1) {
        this.message = `Occurs on day ${this.monthlyDate}`;
      } else if (this.concreteRequest.get('chosenDateOfMonth').value === 2) {
        this.message = `Occurs on the ${this.monthlyDayOfWeek}`;
      } else {
        this.message = `Occurs on the ${this.monthlyLastDayOfWeek}`;
      }
    }
  }

  public changeRecurrenceCount(value): void {
    const count = +value;
    const recurrencedata = this.concreteRequest.get('recurrence').value;

    if (count < 1) {
      this.concreteRequest.get('repeatEveryCount').setValue(1);
      return;
    }

    const isSingle = count === 1;
    const isMultiple = count > 1;

    // Default flags
    this.isRepeatWithSingleRecurrence = false;
    this.isRepeatWithMultipleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;

    switch (recurrencedata) {
      case 'Daily':
        this.isRepeatWithSingleRecurrence = isSingle;
        this.showRecurrenceTypeDropdown = isMultiple;
        this.concreteRequest.get('repeatEveryType').setValue(isSingle ? 'Day' : 'Days');
        break;

      case 'Weekly':
        this.isRepeatWithSingleRecurrence = isSingle;
        this.isRepeatWithMultipleRecurrence = isMultiple;
        this.concreteRequest.get('repeatEveryType').setValue(isSingle ? 'Week' : 'Weeks');
        break;

      case 'Monthly':
        this.isRepeatWithMultipleRecurrence = isMultiple;
        this.isRepeatWithSingleRecurrence = isSingle;
        this.concreteRequest.get('repeatEveryType').setValue(isSingle ? 'Month' : 'Months');
        this.changeMonthlyRecurrence();
        this.showMonthlyRecurrence();
        break;

      case 'Yearly':
        this.isRepeatWithMultipleRecurrence = isMultiple;
        this.isRepeatWithSingleRecurrence = isSingle;
        this.concreteRequest.get('repeatEveryType').setValue(isSingle ? 'Year' : 'Years');
        break;

      default:
        break;
    }

    this.selectedRecurrence = recurrencedata;
    this.occurMessage();
  }

  public changeMonthlyRecurrence(): void {
    this.setMonthlyOrYearlyRecurrenceOption();
    this.updateFormValidation();
    this.showMonthlyRecurrence();
    this.occurMessage();
  }

  public setMonthlyOrYearlyRecurrenceOption(): void {
    if (this.concreteRequest.get('chosenDateOfMonth').value === 1) {
      this.concreteRequest
        .get('dateOfMonth')
        .setValue(moment(this.concreteRequest.get('concretePlacementDate').value).format('DD'));
      this.concreteRequest.get('monthlyRepeatType').setValue(null);
    } else if (this.concreteRequest.get('chosenDateOfMonth').value === 2) {
      this.concreteRequest.get('dateOfMonth').setValue(null);
      this.concreteRequest.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
    } else if (this.concreteRequest.get('chosenDateOfMonth').value === 3) {
      this.concreteRequest.get('dateOfMonth').setValue(null);
      this.concreteRequest.get('monthlyRepeatType').setValue(this.monthlyLastDayOfWeek);
    }
  }

  public showMonthlyRecurrence(): void {
    if (this.concreteRequest.get('concretePlacementDate').value) {
      const startDate = moment(this.concreteRequest.get('concretePlacementDate').value).format(
        'YYYY-MM',
      );
      const chosenDay = moment(this.concreteRequest.get('concretePlacementDate').value).format(
        'dddd',
      );
      this.monthlyDate = moment(this.concreteRequest.get('concretePlacementDate').value).format(
        'DD',
      );
      const day = moment(startDate, 'YYYY-MM').startOf('month').day(chosenDay);
      const getAllDays = [];
      if (day.date() > 7) day.add(7, 'd');
      const month = day.month();
      while (month === day.month()) {
        getAllDays.push(day.format('YYYY-MM-DD'));
        day.add(7, 'd');
      }
      let week;
      let extraOption;
      this.enableOption = false;
      getAllDays.forEach((element, i): void => {
        if (
          moment(this.concreteRequest.get('concretePlacementDate').value).format('YYYY-MM-DD')
          === moment(element).format('YYYY-MM-DD')
        ) {
          const number = i + 1;
          if (number === 1) {
            week = 'First';
          }
          if (number === 2) {
            week = 'Second';
          }
          if (number === 3) {
            week = 'Third';
          }
          if (number === 4) {
            this.enableOption = true;
            extraOption = 'Last';
            week = 'Fourth';
          }
          if (number === 5) {
            week = 'Last';
          }
          if (number === 6) {
            week = 'Last';
          }
        }
      });
      this.monthlyDayOfWeek = `${week} ${chosenDay}`;
      this.monthlyLastDayOfWeek = `${extraOption} ${chosenDay}`;
      if (!this.enableOption && this.concreteRequest.get('chosenDateOfMonth').value === 3) {
        this.concreteRequest.get('chosenDateOfMonth').setValue(2);
        this.concreteRequest.get('dateOfMonth').setValue(null);
        this.concreteRequest.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
      }
      this.setMonthlyOrYearlyRecurrenceOption();
      this.occurMessage();
    }
  }

  public updateFormValidation(): void {
    const chosenDateOfMonth = this.concreteRequest.get('chosenDateOfMonth');
    const dateOfMonth = this.concreteRequest.get('dateOfMonth');
    const monthlyRepeatType = this.concreteRequest.get('monthlyRepeatType');
    if (this.concreteRequest.get('chosenDateOfMonth').value === 1) {
      dateOfMonth.setValidators([Validators.required]);
      monthlyRepeatType.clearValidators();
    } else {
      monthlyRepeatType.setValidators([Validators.required]);
      dateOfMonth.clearValidators();
    }
    chosenDateOfMonth.updateValueAndValidity();
    dateOfMonth.updateValueAndValidity();
    monthlyRepeatType.updateValueAndValidity();
  }

  public onChange(event): void {
    this.checkform = this.concreteRequest.get('days') as UntypedFormArray;
    this.valueExists = this.checkform.controls.filter(
      (object): any => object.value === event.target.value,
    );
    if (event.target.checked) {
      this.checkform.push(new UntypedFormControl(event.target.value));
      this.weekDays = this.weekDays.map((day16: any): void => {
        const dayObj16 = day16;
        if (day16.value === event.target.value) {
          dayObj16.checked = true;
        }
        return dayObj16;
      });
      if (this.checkform.controls.length === 2) {
        this.weekDays = this.weekDays.map((day17: any): void => {
          const dayObj17 = day17;
          dayObj17.isDisabled = false;
          return dayObj17;
        });
      }
    } else if (this.selectedRecurrence === 'Weekly') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day18: any): void => {
            const dayObj18 = day18;
            if (dayObj18.value === event.target.value) {
              dayObj18.checked = false;
            }
            return dayObj18;
          });
        }
        if (this.checkform.controls.length === 1) {
          this.weekDays = this.weekDays.map((day19: any): void => {
            const dayObj19 = day19;
            if (dayObj19.value === this.checkform.controls[0].value) {
              dayObj19.isDisabled = true;
              dayObj19.checked = true;
            }
            return dayObj19;
          });
          return;
        }
        i += 1;
      });
    } else if (this.selectedRecurrence === 'Daily') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day): void => {
            const dayObj = day;
            if (dayObj.value === event.target.value) {
              dayObj.checked = false;
              dayObj.isDisabled = false;
            }
            return dayObj;
          });
          return;
        }
        i += 1;
      });
    }
    if (this.checkform.controls.length !== 7) {
      this.concreteRequest.get('recurrence').setValue('Weekly');
      if (+this.concreteRequest.get('repeatEveryCount').value === 1) {
        this.concreteRequest.get('repeatEveryType').setValue('Week');
      } else {
        this.concreteRequest.get('repeatEveryType').setValue('Weeks');
      }
      this.selectedRecurrence = this.concreteRequest.get('recurrence').value;
    }
    if (this.checkform.controls.length === 7) {
      this.concreteRequest.get('recurrence').setValue('Daily');
      if (+this.concreteRequest.get('repeatEveryCount').value === 1) {
        this.concreteRequest.get('repeatEveryType').setValue('Day');
      } else {
        this.concreteRequest.get('repeatEveryType').setValue('Days');
      }
      this.selectedRecurrence = this.concreteRequest.get('recurrence').value;
    }
    this.occurMessage();
  }

  public getConcreteValue(value) {
    if (value === 'Does Not Repeat') {
      this.concreteRequest.get('repeatEveryType').setValue('');
    } else {
      this.concreteRequest.get('repeatEveryCount').setValue(1);
    }
    if (value === 'Daily') {
      this.concreteRequest.get('repeatEveryType').setValue('Day');
    }
    if (value === 'Weekly') {
      this.concreteRequest.get('repeatEveryType').setValue('Week');
    }
    if (value === 'Monthly') {
      this.concreteRequest.get('repeatEveryType').setValue('Month');
    }
    if (value === 'Yearly') {
      this.concreteRequest.get('repeatEveryType').setValue('Year');
    }
  }

  public onRecurrenceSelect(value): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    this.selectedRecurrence = value;
    this.getConcreteValue(value);
    if (this.concreteRequest.get('repeatEveryCount').value > 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.checkform = this.concreteRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day11: any): void => {
        const dayObj11 = day11;
        dayObj11.checked = true;
        dayObj11.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj11.value));
        return dayObj11;
      });
    }
    if (this.concreteRequest.get('repeatEveryCount').value > 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.weekDays = this.weekDays.map((day12: any): void => {
        const dayObj12 = day12;
        if (dayObj12.value === 'Monday') {
          dayObj12.checked = true;
        } else {
          dayObj12.checked = false;
        }
        dayObj12.isDisabled = false;
        return dayObj12;
      });
      this.checkform = this.concreteRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.concreteRequest.get('repeatEveryCount').value === 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.weekDays = this.weekDays.map((day13: any): void => {
        const dayObj13 = day13;
        if (dayObj13.value === 'Monday') {
          dayObj13.checked = true;
        } else {
          dayObj13.checked = false;
        }
        dayObj13.isDisabled = false;
        return dayObj13;
      });
      this.checkform = this.concreteRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.concreteRequest.get('repeatEveryCount').value === 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.checkform = this.concreteRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day14: any): void => {
        const dayObj14 = day14;
        dayObj14.checked = true;
        dayObj14.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj14.value));
        return dayObj14;
      });
    }
    if (
      this.concreteRequest.get('repeatEveryCount').value === 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.concreteRequest.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showMonthlyRecurrence();
    }
    if (
      this.concreteRequest.get('repeatEveryCount').value > 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.concreteRequest.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.showMonthlyRecurrence();
    }
    this.occurMessage();
  }

  public timeZoneSelected(id): void {
    this.selectedTimeZoneValue = this.timezoneList.find((obj: any): any => +obj.id === +id);
  }

  public getTimeZoneList(): void {
    this.projectSharingServices.guestGetTimeZoneList().subscribe({
      next: (response: any): void => {
        this.formSubmitted = true;
        if (response) {
          const params = {
            ProjectId: this.ProjectId,
          };
          if (params.ProjectId) {
            this.projectSharingServices.guestGetSingleProject(params).subscribe((projectList: any): void => {
              if (projectList) {
                this.timezoneList = response.data;
                this.dropdownSettings = {
                  singleSelection: true,
                  idField: 'id',
                  textField: 'location',
                  allowSearchFilter: true,
                  closeDropDownOnSelection: true,
                };
                this.getSelectedTimeZone = this.timezoneList.filter(
                  (obj: any): any => +obj.id === +projectList.data.TimeZoneId,
                );
                this.defaultValue = this.getSelectedTimeZone;
                this.formSubmitted = false;
              }
            });
          }
        }
      },
      error: (getTimeZoneListErr): void => {
        if (getTimeZoneListErr.message?.statusCode === 400) {
          this.showError(getTimeZoneListErr);
        } else if (!getTimeZoneListErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(getTimeZoneListErr.message, 'OOPS!');
        }
      },
    });
  }

  public isRequestToMember() {
    const payload = {
      userId: this.guestUserId,
      ProjectId: this.ProjectId,
    };
    this.projectSharingServices.isRequestToMember(payload).subscribe({
      next: (response: any): void => {
        if (!response?.data.isRequestedToBeAMember && response?.data.status !== 'declined') {
          this.router.navigate(['/submit-book']);
        } else {
          this.router.navigate([window.atob(localStorage.getItem('url'))]);
        }
      },
      error: (error: any) => {
        console.error('Error occurred:', error);
        this.toastr.error('Try again later.!', 'Something went wrong.');
      },
    });
  }
}
