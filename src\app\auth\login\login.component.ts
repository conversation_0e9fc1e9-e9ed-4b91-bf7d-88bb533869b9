import { ToastrService } from 'ngx-toastr';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';

import { Component, OnInit, TemplateRef } from '@angular/core';
import { UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Title } from '@angular/platform-browser';

import { AuthService } from '../../services/auth/auth.service';
import { MixpanelService } from '../../services/mixpanel.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  })
export class LoginComponent implements OnInit {
  public loginForm: UntypedFormGroup;

  public submitted = false;

  public isShowPassword = false;

  public formSubmitted = false;

  public bsModalRef: BsModalRef;

  public rememberMeData = false;

  public modalRef: BsModalRef;

  public constructor(
    private readonly fb: UntypedFormBuilder,
    private readonly authService: AuthService,
    private readonly toastr: ToastrService,
    private readonly titleService: Title,
    private readonly modalService: BsModalService,
    private readonly mixpanelService: MixpanelService,
    private readonly router: Router,
  ) {
    this.titleService.setTitle('Follo - Login');
    this.createLoginForm();
    this.getStoredData();
  }

  public ngOnInit(): void {
    this.authService.checkAuthentication();
  }

  public handleToggleKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.showHide();
    }
  }

  public showHide(): void {
    this.isShowPassword = !this.isShowPassword;
  }

  public rememberMe(value: boolean): void {
    this.rememberMeData = value;
  }

  public login(): void {
    this.submitted = true;
    this.formSubmitted = true;
    if (this.loginForm.invalid) {
      this.formSubmitted = false;
      return;
    }
    if (this.loginForm.value.email) {
      this.loginForm.value.email = this.loginForm.value.email.trim();
      this.loginForm.value.password = this.loginForm.value.password.trim();
    }
    this.authService.login(this.loginForm.value).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success("Let's get to work!", 'Success!');
          this.mixpanelService.addMixpanelEvents('User LoggedIn');
          this.mixpanelService.updateUserProfile(response);
          const { userDetails } = response;
          localStorage.removeItem('basic');
          localStorage.removeItem('company');
          localStorage.removeItem('planid');
          localStorage.removeItem('interval');
          this.storeData();
          localStorage.setItem('token', response.token);
          this.submitted = false;
          this.formSubmitted = false;
          if (userDetails) {
            if (userDetails.isAccount) {
              this.router.navigate(['/dashboard']);
            } else {
              this.redirectCalendar();
            }
          } else {
            this.redirectCalendar();
          }
        }
      },
      error: (loginError): void => {
        if (loginError.message?.statusCode === 400) {
          this.showError(loginError);
        } else if (!loginError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(loginError.message);
        }
        this.submitted = false;
        this.formSubmitted = false;
      },
    });
  }

  public redirectCalendar(): void {
    this.router.navigate(['/all-calender']);
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }

  public openTermsModal(template: TemplateRef<any>): void {
    this.modalRef = this.modalService.show(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'primary-modal terms-conditions-modal',
    });
  }

  private getStoredData(): void {
    if (localStorage.getItem('remembermestored')) {
      this.rememberMeData = true;
      const decemail = window.atob(localStorage.getItem('email'));
      const decpassword = window.atob(localStorage.getItem('password'));
      this.loginForm.get('email').setValue(decemail);
      this.loginForm.get('password').setValue(decpassword);
    }
  }

  private storeData(): void {
    if (this.rememberMeData) {
      const encmail = window.btoa(this.loginForm.get('email').value);
      const encpassword = window.btoa(this.loginForm.get('password').value);
      localStorage.setItem('email', encmail);
      localStorage.setItem('password', encpassword);
      localStorage.setItem('remembermestored', JSON.stringify(this.rememberMeData));
    } else {
      localStorage.clear();
    }
  }

  private createLoginForm(): void {
    this.loginForm = this.fb.group({
      email: [
        '',
        Validators.compose([
          Validators.required,
          Validators.pattern("^[a-zA-Z0-9._%+'-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$"),
        ]),
      ],
      password: ['', Validators.compose([Validators.required])],
    });
  }
}
