import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { InspectionHistoryComponent } from './inspection-history.component';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';
import { of, Subject } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';

// Split into smaller test suites to handle the line count limit
describe('InspectionHistoryComponent - Creation and Basic State', () => {
  let component: InspectionHistoryComponent;
  let fixture: ComponentFixture<InspectionHistoryComponent>;
  let deliveryServiceSpy: jest.Mocked<DeliveryService>;

  const mockProjectData = {
    ProjectId: '123',
    ParentCompanyId: '456',
  };

  const mockHistoryData = {
    data: [
      { type: 'status', description: 'Status changed', createdAt: '2024-03-20' },
      { type: 'comment', description: 'Comment added', createdAt: '2024-03-20' },
      { type: 'attachment', description: 'File attached', createdAt: '2024-03-20' },
    ],
  };

  beforeEach(async () => {
    const deliverySpy = {
      getInspectionHistory: jest.fn(),
      InspectionRequestId: new Subject<string>(),
      inspectionUpdated: new Subject<void>(),
      inspectionUpdated1: new Subject<void>(),
    };

    const projectSpy = {
      projectParent: of(mockProjectData),
    };

    await TestBed.configureTestingModule({
      declarations: [InspectionHistoryComponent],
      providers: [
        { provide: DeliveryService, useValue: deliverySpy },
        { provide: ProjectService, useValue: projectSpy },
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();

    deliveryServiceSpy = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    fixture = TestBed.createComponent(InspectionHistoryComponent);
    component = fixture.componentInstance;
    deliveryServiceSpy.getInspectionHistory.mockReturnValue(of(mockHistoryData));
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.historyList).toEqual([]);
    expect(component.loader).toBe(true);
  });

  it('should subscribe to project data and update properties', () => {
    expect(component.ProjectId).toBe(mockProjectData.ProjectId);
    expect(component.ParentCompanyId).toBe(mockProjectData.ParentCompanyId);
  });
});

describe('InspectionHistoryComponent - History Data', () => {
  let component: InspectionHistoryComponent;
  let fixture: ComponentFixture<InspectionHistoryComponent>;
  let deliveryServiceSpy: jest.Mocked<DeliveryService>;

  const mockProjectData = {
    ProjectId: '123',
    ParentCompanyId: '456',
  };

  const mockHistoryData = {
    data: [
      { type: 'status', description: 'Status changed', createdAt: '2024-03-20' },
      { type: 'comment', description: 'Comment added', createdAt: '2024-03-20' },
      { type: 'attachment', description: 'File attached', createdAt: '2024-03-20' },
    ],
  };

  beforeEach(async () => {
    const deliverySpy = {
      getInspectionHistory: jest.fn(),
      InspectionRequestId: new Subject<string>(),
      inspectionUpdated: new Subject<void>(),
      inspectionUpdated1: new Subject<void>(),
    };

    const projectSpy = {
      projectParent: of(mockProjectData),
    };

    await TestBed.configureTestingModule({
      declarations: [InspectionHistoryComponent],
      providers: [
        { provide: DeliveryService, useValue: deliverySpy },
        { provide: ProjectService, useValue: projectSpy },
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();

    deliveryServiceSpy = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    fixture = TestBed.createComponent(InspectionHistoryComponent);
    component = fixture.componentInstance;
    deliveryServiceSpy.getInspectionHistory.mockReturnValue(of(mockHistoryData));
    fixture.detectChanges();
  });

  it('should fetch history data when inspection request ID is set', fakeAsync(() => {
    const inspectionRequestId = '789';
    deliveryServiceSpy.getInspectionHistory.mockReturnValue(of(mockHistoryData));

    (deliveryServiceSpy.InspectionRequestId as Subject<string>).next(inspectionRequestId);
    tick(100); // Wait for debounceTime
    fixture.detectChanges();

    expect(deliveryServiceSpy.getInspectionHistory).toHaveBeenCalledWith({
      inspectionRequestId,
      ParentCompanyId: mockProjectData.ParentCompanyId,
    });
  }));

  it('should filter out comments from history list', fakeAsync(() => {
    const inspectionRequestId = '789';
    deliveryServiceSpy.getInspectionHistory.mockReturnValue(of(mockHistoryData));

    (deliveryServiceSpy.InspectionRequestId as Subject<string>).next(inspectionRequestId);
    tick(100); // Wait for debounceTime
    fixture.detectChanges();

    expect(component.historyList.length).toBe(2); // Should exclude the comment type
    expect(component.historyList.every((item) => item.type !== 'comment')).toBe(true);
  }));

  it('should update loader state when fetching history', fakeAsync(() => {
    const inspectionRequestId = '789';
    deliveryServiceSpy.getInspectionHistory.mockReturnValue(of(mockHistoryData));

    (deliveryServiceSpy.InspectionRequestId as Subject<string>).next(inspectionRequestId);
    tick(100); // Wait for debounceTime
    fixture.detectChanges();

    expect(component.loader).toBe(false);
  }));
});

describe('InspectionHistoryComponent - Updates', () => {
  let component: InspectionHistoryComponent;
  let fixture: ComponentFixture<InspectionHistoryComponent>;
  let deliveryServiceSpy: jest.Mocked<DeliveryService>;

  const mockProjectData = {
    ProjectId: '123',
    ParentCompanyId: '456',
  };

  const mockHistoryData = {
    data: [
      { type: 'status', description: 'Status changed', createdAt: '2024-03-20' },
      { type: 'comment', description: 'Comment added', createdAt: '2024-03-20' },
      { type: 'attachment', description: 'File attached', createdAt: '2024-03-20' },
    ],
  };

  beforeEach(async () => {
    const deliverySpy = {
      getInspectionHistory: jest.fn(),
      InspectionRequestId: new Subject<string>(),
      inspectionUpdated: new Subject<void>(),
      inspectionUpdated1: new Subject<void>(),
    };

    const projectSpy = {
      projectParent: of(mockProjectData),
    };

    await TestBed.configureTestingModule({
      declarations: [InspectionHistoryComponent],
      providers: [
        { provide: DeliveryService, useValue: deliverySpy },
        { provide: ProjectService, useValue: projectSpy },
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();

    deliveryServiceSpy = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    fixture = TestBed.createComponent(InspectionHistoryComponent);
    component = fixture.componentInstance;
    deliveryServiceSpy.getInspectionHistory.mockReturnValue(of(mockHistoryData));
    fixture.detectChanges();
  });

  it('should handle inspection updates', fakeAsync(() => {
    const inspectionRequestId = '789';
    component.inspectionRequestId = inspectionRequestId;
    deliveryServiceSpy.getInspectionHistory.mockReturnValue(of(mockHistoryData));

    (deliveryServiceSpy.inspectionUpdated as Subject<void>).next();
    tick(100); // Wait for debounceTime
    fixture.detectChanges();

    expect(deliveryServiceSpy.getInspectionHistory).toHaveBeenCalled();
  }));

  it('should handle inspection updates 1', fakeAsync(() => {
    const inspectionRequestId = '789';
    component.inspectionRequestId = inspectionRequestId;
    deliveryServiceSpy.getInspectionHistory.mockReturnValue(of(mockHistoryData));

    (deliveryServiceSpy.inspectionUpdated1 as Subject<void>).next();
    tick(100); // Wait for debounceTime
    fixture.detectChanges();

    expect(deliveryServiceSpy.getInspectionHistory).toHaveBeenCalled();
  }));
});
