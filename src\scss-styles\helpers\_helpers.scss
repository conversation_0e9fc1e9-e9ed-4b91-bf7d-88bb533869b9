/********* margin & padding  **********/
$spaceamounts: (0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 13,14, 15, 16, 18, 20,22, 25,26, 27,28, 30, 32, 33,35, 37,40, 45, 50, 55,58, 59,  
60, 65, 70, 80,85,100,110,120,140,160,170,173,185,190,195,200,210,235,260,300,340,450,500,560,840);
$sides: (top, bottom, left, right, all);

@each $space in $spaceamounts {
  @each $side in $sides {
    @if $side == 'all' {
      .m#{$space} {
        margin: #{$space}px;
      }
      .ml#{$space} {
        margin-left
        : #{$space}px;
      }
      .mr#{$space} {
        margin-right
        : #{$space}px;
      }
      .mt#{$space} {
        margin-top
        : #{$space}px;
      }
      .mb#{$space} {
        margin-bottom
        : #{$space}px;
      }
      .p#{$space} {
        padding: #{$space}px;
      }
      .radius#{$space} {
        border-radius: #{$space}px;
      }
      .w#{$space} {
        width: #{$space}px;
      }
      .h#{$space} {
        height: #{$space}px;
      }
      .pl#{$space} {
        padding-left: #{$space}px;
      }
      .pb#{$space} {
        padding-bottom: #{$space}px;
      }
      .bt#{$space} {
        bottom: #{$space}px;
      }
      .lh#{$space} {
        line-height: #{$space}px;
      }
    }

    @else {
      .m#{str-slice($side, 0, 1)}#{$space} {
        margin-#{$side}: #{$space}px;
      }

      .p#{str-slice($side, 0, 1)}#{$space} {
        padding-#{$side}: #{$space}px;
      }
    }
  }
}

/********* margin & padding  **********/

.mt-minus-5 {
  margin-top: -5px;
}

.mb-4px {
  margin-bottom: 4px;
}

/********* Border radius ***********/

.br-10 {
  border-radius: 10px;
}

.br-30 {
  border-radius: 30px;
}

.outline-0 {
  outline: none;
}

/********* / Border radius ***********/



/********* width & height ***********/

.w-35px {
  width: 35px;
}

.w-26px {
  width: 26px;
}

.w-80 {
  width: 80%;
}

.h-12px {
  height: $value12;
}

.h-15px {
  height: $value15;
}

.h-20px {
  height: $value20;
}

.w-300px {
  width: $value300;
}
.w-155px{
  width: $value155;
}
.mw-200 {
  max-width: 200px;
}

.px-2rem {
  padding-left: $rem2;
  padding-right: $rem2;
}

.px-2-5rem {
  padding-left: $rem2-5;
  padding-right: $rem2-5;
}

.mnb28 {
  margin-bottom: $mnb28;
}

.min-h-65px {
  min-height: 65px;
}

.white-border {
  border: 1px solid $white;
}
.w-20{
  width: 20%;
}
.w-20px {
  width: $value20;
}

.w-30px {
  width: $value30;
}

.h-30px {
  height: $value30;
}

.w-40px {
  width: $value40;
}

.h-40px {
  height: $value40;
}
.w-100px {
  width: $value100;
}
.w-110px {
  width: $value110;
}
.h-10px {
  height: 10px;
}

/********* width & hbottomht ***********/

.border-white {
  border-color: $white;
}

.border-gray {
  border-color: $gray;
}

.c-pointer {
  cursor: pointer;
}

.op-90 {
  opacity: 0.9;
}

.op-32 {
  opacity: 0.32;
}
.lh-13 {
  line-height: 13px;
}
.lh-30 {
  line-height: 30px;
}
.lh-27
{
  line-height: 27px;
}
.no-wrap
{
  white-space: nowrap;
}
.mw-100px
{
  max-width: 100px;
}
.bl_white{
border-left: 1px solid $white;
}
.w10per{
width: 10%;
}
.mt-5rem{
  margin-top: -0.5rem !important;
  line-height: 12px;
}
.mt-3rem {
    margin-top: -0.3rem !important;
  }
  .mt-4rem {
    margin-top: -0.3rem !important;
  }

  .border-bottom-grey{
    border-bottom: 1px solid #bebebe !important;
  }

  .mt-29rem { 
    margin-top:2.9rem !important
   }

  .mt-07rem {
    margin-top: 0.7rem;
  }
.cursor-pointer{
  cursor: pointer;
}
.border-lightorange{
  border: 1px solid #FFCCB9;
}
.pale-red {
background-color: $red12;
}
.z-99 {
z-index: 99; 
}

.w-fit-content{
  width: fit-content;
}

.border-light-gray {
  border : 1px solid $light-grey8;
}

.border-left-green {
    border-left-color: $green10;
    border-left-width: 20px;
}
.border-bottom-grayb {
    border-bottom: 1px solid #dee2e6;
}