import { ComponentFixture, TestBed } from '@angular/core/testing';
import { InspectionComponent } from './inspection.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { ReportsService } from '../../services/reports/reports.service';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ToastrService } from 'ngx-toastr';
import { of, BehaviorSubject } from 'rxjs';
import { ElementRef, Pipe, PipeTransform } from '@angular/core';
import { SchedulerFormComponent } from '../scheduler-form/scheduler-form.component';
import { SaveReportFormComponent } from '../save-report-form/save-report-form.component';

@Pipe({
  name: 'paginate'
})
class MockPaginatePipe implements PipeTransform {
  transform(value: any[], config: any): any[] {
    return value;
  }
}

describe('InspectionComponent', () => {
  let component: InspectionComponent;
  let fixture: ComponentFixture<InspectionComponent>;
  let modalService: jest.Mocked<BsModalService>;
  let reportsService: jest.Mocked<ReportsService>;
  let projectService: jest.Mocked<ProjectService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let toastrService: jest.Mocked<ToastrService>;
  let mockModalRef: Partial<BsModalRef>;

  beforeEach(async () => {
    mockModalRef = {
      hide: jest.fn()
    };

    const modalServiceSpy = {
      show: jest.fn().mockReturnValue(mockModalRef)
    };
    const reportsServiceSpy = {
      inspectionReports: jest.fn(),
      exportInspectionReport: jest.fn().mockReturnValue(of({ data: 'test-url' })),
      exportInspectionRequestInExcelFormat: jest.fn().mockReturnValue(of(new Blob()))
    };
    const projectServiceSpy = {
      projectParent: new BehaviorSubject({
        ProjectId: '123',
        ParentCompanyId: '456'
      }),
      gateList: jest.fn().mockReturnValue(of({ data: [] })),
      listEquipment: jest.fn().mockReturnValue(of({ data: [] })),
      getDefinableWork: jest.fn().mockReturnValue(of({ data: [] })),
      getCompanies: jest.fn().mockReturnValue(of({ data: [] })),
      getLocations: jest.fn().mockReturnValue(of({ data: [] })),
      listAllMember: jest.fn().mockReturnValue(of({ data: [] }))
    };
    const deliveryServiceSpy = {
      saveAsExcelFile: jest.fn()
    };
    const toastrServiceSpy = {
      success: jest.fn(),
      error: jest.fn()
    };

    await TestBed.configureTestingModule({
      declarations: [InspectionComponent, MockPaginatePipe],
      imports: [ReactiveFormsModule],
      providers: [
        UntypedFormBuilder,
        { provide: BsModalService, useValue: modalServiceSpy },
        { provide: ReportsService, useValue: reportsServiceSpy },
        { provide: ProjectService, useValue: projectServiceSpy },
        { provide: DeliveryService, useValue: deliveryServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: ElementRef, useValue: { nativeElement: document.createElement('div') } }
      ]
    }).compileComponents();

    modalService = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    reportsService = TestBed.inject(ReportsService) as jest.Mocked<ReportsService>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(InspectionComponent);
    component = fixture.componentInstance;
    
    // Mock reports service response
    reportsService.inspectionReports.mockReturnValue(of({
      data: {
        rows: [],
        count: 0
      }
    }));

    // Initialize forms manually since they're initialized in constructor
    component.filterDetailsForm();
    component.exportDetailsForm();

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.currentPageNo).toBe(1);
    expect(component.perPage).toBe(1);
    expect(component.pageSize).toBe(25);
    expect(component.pageNo).toBe(1);
    expect(component.loader).toBe(true);
    expect(component.sortColumn).toBe('inspectionStart');
    expect(component.sort).toBe('ASC');
  });

  it('should initialize filter form', () => {
    expect(component.filterForm).toBeTruthy();
    expect(component.filterForm.get('companyFilter')).toBeTruthy();
    expect(component.filterForm.get('descriptionFilter')).toBeTruthy();
    expect(component.filterForm.get('statusFilter')).toBeTruthy();
    expect(component.filterForm.get('dateFilter')).toBeTruthy();
    expect(component.filterForm.get('memberFilter')).toBeTruthy();
    expect(component.filterForm.get('gateFilter')).toBeTruthy();
    expect(component.filterForm.get('equipmentFilter')).toBeTruthy();
    expect(component.filterForm.get('defineFilter')).toBeTruthy();
    expect(component.filterForm.get('idFilter')).toBeTruthy();
    expect(component.filterForm.get('inspectionStatusFilter')).toBeTruthy();
    expect(component.filterForm.get('inspectionTypeFilter')).toBeTruthy();
    expect(component.filterForm.get('locationFilter')).toBeTruthy();
  });

  it('should initialize export form', () => {
    expect(component.exportForm).toBeTruthy();
    expect(component.exportForm.get('reportType')).toBeTruthy();
    expect(component.exportForm.get('reportName')).toBeTruthy();
  });

  it('should toggle dropdowns correctly', () => {
    component.toggleDropdown('equipment');
    expect(component.isEquipmentDropdownOpen).toBe(true);
    
    component.toggleDropdown('equipment');
    expect(component.isEquipmentDropdownOpen).toBe(false);
  });

  it('should handle selection toggle correctly', () => {
    const item = { id: 1, name: 'Test' };
    const event = new MouseEvent('click');
    
    component.toggleSelection(item, 'equipment', event);
    expect(component.selectedEquipment).toContain(item);
    
    component.toggleSelection(item, 'equipment', event);
    expect(component.selectedEquipment).not.toContain(item);
  });

  it('should handle page size change', () => {
    const newPageSize = 50;
    component.changePageSize(newPageSize);
    expect(component.pageSize).toBe(newPageSize);
    expect(reportsService.inspectionReports).toHaveBeenCalled();
  });

  it('should handle page number change', () => {
    const newPageNo = 2;
    component.changePageNo(newPageNo);
    expect(component.pageNo).toBe(newPageNo);
    expect(reportsService.inspectionReports).toHaveBeenCalled();
  });

  it('should handle sort by field', () => {
    const fieldName = 'description';
    const sortType = 'DESC';
    
    component.sortByField(fieldName, sortType);
    expect(component.sortColumn).toBe(fieldName);
    expect(component.sort).toBe(sortType);
    expect(reportsService.inspectionReports).toHaveBeenCalled();
  });

  it('should reset filters correctly', () => {
    component.resetFilter();
    expect(component.selectedEquipment).toEqual([]);
    expect(component.selectedGate).toEqual([]);
    expect(component.selectedCompany).toEqual([]);
    expect(component.selectedLocation).toEqual([]);
    expect(component.selectedResponsible).toEqual([]);
    expect(component.selectedDefine).toEqual([]);
    expect(component.selectedInspectionType).toEqual([]);
    expect(component.selectedInspectionStatus).toEqual([]);
    expect(reportsService.inspectionReports).toHaveBeenCalled();
  });

  it('should handle document click outside dropdowns', () => {
    // Create a mock element with closest method
    const mockElement = document.createElement('div');
    mockElement.closest = jest.fn().mockReturnValue(null);

    // Create a mock event with the mock element as target
    const event = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      view: window
    });
    Object.defineProperty(event, 'target', { value: mockElement });

    component.onDocumentClick(event);
    
    expect(component.isEquipmentDropdownOpen).toBe(false);
    expect(component.isStatusDropdownOpen).toBe(false);
    expect(component.isGateDropdownOpen).toBe(false);
    expect(component.isCompanyDropdownOpen).toBe(false);
    expect(component.isLocationDropdownOpen).toBe(false);
    expect(component.isResponsibleDropdownOpen).toBe(false);
    expect(component.isDefineDropdownOpen).toBe(false);
    expect(component.isInspectionStatusDropdownOpen).toBe(false);
    expect(component.isInspectionTypeDropdownOpen).toBe(false);
  });

  it('should open modal correctly', () => {
    const template = {} as any;
    component.openModal(template);
    expect(modalService.show).toHaveBeenCalledWith(template, {
      class: 'modal-export'
    });
    expect(component.modalRef).toBe(mockModalRef);
  });

  it('should handle export form submission', () => {
    // First open the modal to set modalRef
    const template = {} as any;
    component.openModal(template);

    component.exportForm.patchValue({
      reportType: 'PDF',
      reportName: 'Test Report'
    });

    component.export();
    // expect(component.exportSubmitted).toBe(true);
    expect(component.modalRef).toBe(mockModalRef);
    expect(reportsService.exportInspectionReport).toHaveBeenCalled();
  });

  // Additional positive test cases
  it('should handle openModal1 correctly', () => {
    const template = {} as any;
    component.openModal1(template);
    expect(modalService.show).toHaveBeenCalledWith(template, {
      class: 'modal-downloads'
    });
  });

  it('should handle openModal3 correctly', () => {
    const template = {} as any;
    component.openModal3(template);
    expect(modalService.show).toHaveBeenCalledWith(template, {
      class: 'modal-sendto'
    });
  });

  it('should handle openModal4 correctly', () => {
    const template = {} as any;
    component.openModal4(template);
    expect(modalService.show).toHaveBeenCalledWith(template, {
      class: ' report-filter-modal filter-popup report-filter custom-modal'
    });
  });

  it('should handle keyboard events for toggle dropdown', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const spy = jest.spyOn(component, 'toggleDropdown');

    component.handleDropdownKeydown(event, 'equipment');
    expect(spy).toHaveBeenCalledWith('equipment');
  });

  it('should handle keyboard events for sort field', () => {
    const event = new KeyboardEvent('keydown', { key: ' ' });
    const spy = jest.spyOn(component, 'sortByField');

    component.handleToggleKeydown(event, 'description', 'ASC');
    expect(spy).toHaveBeenCalledWith('description', 'ASC');
  });

  it('should open schedule popup correctly', () => {
    component.openSchedulePopup();
    expect(modalService.show).toHaveBeenCalledWith(SchedulerFormComponent);
  });

  it('should close filter popup correctly', () => {
    component.modalRef4 = mockModalRef as BsModalRef;
    component.closeFilterPopup();
    expect(component.isEquipmentDropdownOpen).toBe(false);
    expect(component.isStatusDropdownOpen).toBe(false);
  });

  it('should handle filter submission correctly', () => {
    component.filterForm.patchValue({
      descriptionFilter: 'test',
      dateFilter: [new Date(), new Date()],
      companyFilter: ['1']
    });

    component.filterSubmit();
    expect(component.formSubmitted).toBe(true);
    expect(component.filterCount).toBeGreaterThan(0);
  });

  it('should handle input change for table headers', () => {
    // Mock DOM element
    const mockCheckbox = { checked: true } as HTMLInputElement;
    jest.spyOn(document, 'getElementById').mockReturnValue(mockCheckbox);

    component.handleInputChange('equipment');
    expect(component.tableHeaders.equipment.isActive).toBe(true);
  });

  it('should handle select all functionality', () => {
    const mockCheckbox = { checked: true } as HTMLInputElement;
    jest.spyOn(document, 'getElementById').mockReturnValue(mockCheckbox);

    component.selectall('selectAll');
    expect(component.tableHeaders.id.isActive).toBe(true);
  });

  it('should handle location filter change', () => {
    component.locationList = [
      { locationPath: 'Location 1' },
      { locationPath: 'Location 2' }
    ];

    const event = { target: { value: 'Location 1' } };
    component.changeLocationFilterOptionList(event);
    expect(component.filteredLocationList.length).toBe(1);
  });

  it('should handle recurrence selection', () => {
    component.onRecurrenceSelect('Weekly');
    expect(component.selectedRecurrence).toBe('Weekly');
  });

  it('should get location children', () => {
    component.getLocationChildren('Monthly');
    expect(component.selectedRecurrence).toBe('Monthly');
  });

  // Negative test cases
  it('should handle invalid export form submission', () => {
    component.exportForm.patchValue({
      reportType: '',
      reportName: ''
    });

    component.export();
    expect(component.exportSubmitted).toBe(false);
  });

  it('should handle empty filter arrays in mapFilterArray', () => {
    const result = component.mapFilterArray([]);
    expect(result).toEqual([]);
  });

  it('should handle null filter arrays in mapFilterArray', () => {
    const result = component.mapFilterArray(null);
    expect(result).toEqual([]);
  });

  it('should handle undefined filter arrays in mapFilterArray', () => {
    const result = component.mapFilterArray(undefined);
    expect(result).toEqual([]);
  });

  it('should handle invalid dropdown type in toggleDropdown', () => {
    component.toggleDropdown('invalid');
    expect(component.isEquipmentDropdownOpen).toBe(false);
  });

  it('should handle invalid selection type in toggleSelection', () => {
    const item = { id: 1, name: 'Test' };
    const event = new MouseEvent('click');

    component.toggleSelection(item, 'invalid', event);
    // Should not throw error and return early
    expect(component.selectedEquipment).toEqual([]);
  });

  it('should handle invalid type in isSelected', () => {
    const item = { id: 1, name: 'Test' };
    const result = component.isSelected(item, 'invalid');
    expect(result).toBe(false);
  });

  it('should handle missing form control in selectOption', () => {
    const item = { id: 1, name: 'Test' };
    const event = new MouseEvent('click');

    // Create a new form without the expected control
    const formBuilder = TestBed.inject(UntypedFormBuilder);
    component.filterForm = formBuilder.group({});

    component.selectOption(item, 'equipment', event);
    // Should not throw error
    expect(component.filterForm.get('equipmentFilter')).toBeNull();
  });

  it('should handle checkbox not found in handleInputChange', () => {
    jest.spyOn(document, 'getElementById').mockReturnValue(null);

    expect(() => component.handleInputChange('nonexistent')).toThrow();
  });

  it('should handle empty location list in changeLocationFilterOptionList', () => {
    component.locationList = [];
    component.filteredLocationList = [];

    const event = { target: { value: 'test' } };
    component.changeLocationFilterOptionList(event);
    expect(component.filteredLocationList).toEqual([]);
  });

  it('should handle reset filter with invalid form', () => {
    // Make form invalid
    component.filterForm.get('companyFilter').setErrors({ required: true });

    component.resetFilter();
    expect(component.formSubmitted).toBe(true);
    expect(component.filterCount).toBe(0);
  });

  // Additional comprehensive test cases
  it('should handle export with EXCEL type', () => {
    const template = {} as any;
    component.openModal(template);

    component.exportForm.patchValue({
      reportType: 'EXCEL',
      reportName: 'Test Excel Report'
    });

    component.reportType = 'EXCEL';
    component.export();
    expect(reportsService.exportInspectionRequestInExcelFormat).toHaveBeenCalled();
  });

  it('should handle cancelexport correctly', () => {
    const template = {} as any;
    component.openModal(template);

    component.cancelexport();
    expect(component.exportSubmitted).toBe(false);
    expect(component.exportForm.get('reportName').value).toBe('Inspection Report');
    expect(component.exportForm.get('reportType').value).toBe('PDF');
  });

  it('should handle buildRequestParams correctly', () => {
    component.ProjectId = '123';
    component.pageSize = 50;
    component.pageNo = 2;

    const params = component.buildRequestParams();
    expect(params.ProjectId).toBe('123');
    expect(params.pageSize).toBe(50);
    expect(params.pageNo).toBe(2);
    expect(params.void).toBe(0);
  });

  it('should handle buildFilterPayload without filterForm', () => {
    component.filterForm = null;
    component.sort = 'DESC';
    component.sortColumn = 'date';
    component.ParentCompanyId = '456';

    const payload = component.buildFilterPayload();
    expect(payload.sort).toBe('DESC');
    expect(payload.sortByField).toBe('date');
    expect(payload.ParentCompanyId).toBe('456');
    expect(payload.queuedNdr).toBe(false);
  });

  it('should handle toggleGenericSelection for adding item', () => {
    const item = { id: 1, name: 'Test' };
    const selectedList = [];

    const result = component.toggleGenericSelection(item, 'equipment', selectedList);
    expect(result).toContain(item);
  });

  it('should handle toggleGenericSelection for removing item', () => {
    const item = { id: 1, name: 'Test' };
    const selectedList = [item];

    const result = component.toggleGenericSelection(item, 'equipment', selectedList);
    expect(result).not.toContain(item);
  });

  it('should handle setSelectedListByType for all types', () => {
    const testList = [{ id: 1, name: 'Test' }];

    component.setSelectedListByType('equipment', testList);
    expect(component.selectedEquipment).toEqual(testList);

    component.setSelectedListByType('gate', testList);
    expect(component.selectedGate).toEqual(testList);

    component.setSelectedListByType('company', testList);
    expect(component.selectedCompany).toEqual(testList);

    component.setSelectedListByType('status', testList);
    expect(component.selectedStatus).toEqual(testList);
  });

  it('should handle updatePlaceholder for single item', () => {
    const selectedList = [{ equipmentName: 'Equipment 1' }];

    component.updatePlaceholder('equipment', selectedList, 'equipmentName', 'Select Equipment');
    expect(component.equipmentPlaceholder).toBe('Equipment 1');
  });

  it('should handle updatePlaceholder for multiple items', () => {
    const selectedList = [
      { equipmentName: 'Equipment 1' },
      { equipmentName: 'Equipment 2' },
      { equipmentName: 'Equipment 3' }
    ];

    component.updatePlaceholder('equipment', selectedList, 'equipmentName', 'Select Equipment');
    expect(component.equipmentPlaceholder).toBe('Equipment 1 + 2 more');
  });

  it('should handle updatePlaceholder for responsible type', () => {
    const selectedList = [{ User: { email: '<EMAIL>' } }];

    component.updatePlaceholder('responsible', selectedList, 'User.email', 'Select Responsible Person');
    expect(component.responsiblePlaceholder).toBe('<EMAIL>');
  });

  it('should handle isSelected for different types', () => {
    component.selectedEquipment = [{ id: 1, name: 'Test' }];
    component.selectedStatus = [{ name: 'Approved' }];

    expect(component.isSelected({ id: 1, name: 'Test' }, 'equipment')).toBe(true);
    expect(component.isSelected({ id: 2, name: 'Test2' }, 'equipment')).toBe(false);
    expect(component.isSelected({ name: 'Approved' }, 'status')).toBe(true);
    expect(component.isSelected({ name: 'Declined' }, 'status')).toBe(false);
  });

  it('should handle handleLocationSelection for default item', () => {
    const item = { id: 1, isDefault: true };
    component.locationList = [{ id: 1 }, { id: 2 }, { id: 3 }];

    const result = component.handleLocationSelection(item, []);
    expect(result).toEqual([1, 2, 3]);
  });

  it('should handle handleGenericSelection for name-based types', () => {
    const item = { name: 'Approved' };
    const selectedIds = [];

    const result = component.handleGenericSelection(item, selectedIds, 'status');
    expect(result).toContain('Approved');
  });

  it('should handle selectall with unchecked state', () => {
    const mockCheckbox = { checked: false } as HTMLInputElement;
    jest.spyOn(document, 'getElementById').mockReturnValue(mockCheckbox);

    component.selectall('selectAll');
    expect(component.tableHeaders.id.isActive).toBe(true);
    expect(component.tableHeaders.description.isActive).toBe(true);
    expect(component.tableHeaders.date.isActive).toBe(true);
  });

  it('should handle openModalSave correctly', () => {
    component.ProjectId = '123';
    component.filterPayload = { test: 'data' };

    component.openModalSave();
    expect(modalService.show).toHaveBeenCalled();
  });

  it('should handle location filter with empty search', () => {
    component.locationList = [
      { locationPath: 'Location 1' },
      { locationPath: 'Location 2' }
    ];

    const event = { target: { value: '' } };
    component.changeLocationFilterOptionList(event);
    expect(component.filteredLocationList).toEqual(component.locationList);
  });

  // Additional comprehensive test cases for better coverage

  // Test ngOnInit method
  it('should call ngOnInit without errors', () => {
    expect(() => component.ngOnInit()).not.toThrow();
  });

  // Test constructor subscription with null/undefined values
  it('should handle null project parent subscription', () => {
    const projectServiceSpy = TestBed.inject(ProjectService);
    projectServiceSpy.projectParent.next(null);
    expect(component.ProjectId).toBeUndefined();
  });

  it('should handle undefined project parent subscription', () => {
    const projectServiceSpy = TestBed.inject(ProjectService);
    projectServiceSpy.projectParent.next(undefined);
    expect(component.ProjectId).toBeUndefined();
  });

  it('should handle empty string project parent subscription', () => {
    const projectServiceSpy = TestBed.inject(ProjectService);
    projectServiceSpy.projectParent.next('');
    expect(component.ProjectId).toBeUndefined();
  });

  // Test getInspectionReports with error handling
  it('should handle getInspectionReports with null response', () => {
    reportsService.inspectionReports.mockReturnValue(of(null));
    component.getInspectionReports();
    expect(component.loader).toBe(false);
    expect(component.inspectionList).toEqual([]);
  });

  it('should handle getInspectionReports with undefined response', () => {
    reportsService.inspectionReports.mockReturnValue(of(undefined));
    component.getInspectionReports();
    expect(component.loader).toBe(false);
    expect(component.inspectionList).toEqual([]);
  });

  // Test service method calls in getOverAllGateForNdrGrid
  it('should call getOverAllGateForNdrGrid and chain subsequent calls', () => {
    const mockGateResponse = { data: [{ id: 1, gateName: 'Gate 1' }] };
    projectService.gateList.mockReturnValue(of(mockGateResponse));

    const equipmentSpy = jest.spyOn(component, 'getOverAllEquipmentForNdrGrid');
    component.getOverAllGateForNdrGrid();

    expect(projectService.gateList).toHaveBeenCalledWith(
      {
        ProjectId: component.ProjectId,
        pageSize: 0,
        pageNo: 0,
        ParentCompanyId: component.ParentCompanyId,
      },
      { isFilter: true, showActivatedAlone: true }
    );
    expect(component.gateList).toEqual(mockGateResponse.data);
    expect(equipmentSpy).toHaveBeenCalled();
  });

  // Test getOverAllEquipmentForNdrGrid
  it('should call getOverAllEquipmentForNdrGrid and chain subsequent calls', () => {
    const mockEquipmentResponse = { data: [{ id: 1, equipmentName: 'Equipment 1' }] };
    projectService.listEquipment.mockReturnValue(of(mockEquipmentResponse));

    const definableSpy = jest.spyOn(component, 'getDefinableForNdrGrid');
    component.getOverAllEquipmentForNdrGrid();

    expect(projectService.listEquipment).toHaveBeenCalledWith(
      {
        ProjectId: component.ProjectId,
        pageSize: 0,
        pageNo: 0,
        ParentCompanyId: component.ParentCompanyId,
      },
      {
        isFilter: true,
        showActivatedAlone: true,
      }
    );
    expect(component.equipmentList).toEqual(mockEquipmentResponse.data);
    expect(definableSpy).toHaveBeenCalled();
  });

  // Test getDefinableForNdrGrid with null response
  it('should handle getDefinableForNdrGrid with null response', () => {
    projectService.getDefinableWork.mockReturnValue(of(null));
    const companiesSpy = jest.spyOn(component, 'getCompaniesForNdrGrid');

    component.getDefinableForNdrGrid();
    expect(companiesSpy).not.toHaveBeenCalled();
  });

  // Test getDefinableForNdrGrid with valid response
  it('should call getDefinableForNdrGrid and chain subsequent calls', () => {
    const mockDefinableResponse = { data: [{ id: 1, DFOW: 'DFOW 1' }] };
    projectService.getDefinableWork.mockReturnValue(of(mockDefinableResponse));

    const companiesSpy = jest.spyOn(component, 'getCompaniesForNdrGrid');
    component.getDefinableForNdrGrid();

    expect(projectService.getDefinableWork).toHaveBeenCalledWith({
      ProjectId: component.ProjectId,
      ParentCompanyId: component.ParentCompanyId,
    });
    expect(component.defineList).toEqual(mockDefinableResponse.data);
    expect(companiesSpy).toHaveBeenCalled();
  });

  // Test getCompaniesForNdrGrid with null response
  it('should handle getCompaniesForNdrGrid with null response', () => {
    projectService.getCompanies.mockReturnValue(of(null));
    const locationsSpy = jest.spyOn(component, 'getLocationsForNdrGrid');

    component.getCompaniesForNdrGrid();
    expect(locationsSpy).not.toHaveBeenCalled();
  });

  // Test getCompaniesForNdrGrid with valid response
  it('should call getCompaniesForNdrGrid and chain subsequent calls', () => {
    const mockCompaniesResponse = { data: [{ id: 1, companyName: 'Company 1' }] };
    projectService.getCompanies.mockReturnValue(of(mockCompaniesResponse));

    const locationsSpy = jest.spyOn(component, 'getLocationsForNdrGrid');
    component.getCompaniesForNdrGrid();

    expect(projectService.getCompanies).toHaveBeenCalledWith({
      ProjectId: component.ProjectId,
      ParentCompanyId: component.ParentCompanyId,
    });
    expect(component.companyList).toEqual(mockCompaniesResponse.data);
    expect(locationsSpy).toHaveBeenCalled();
  });

  // Test getMembers with null response
  it('should handle getMembers with null response', () => {
    projectService.listAllMember.mockReturnValue(of(null));
    component.getMembers();
    expect(component.memberList).toEqual([]);
    expect(component.updatedMemberList).toEqual([]);
  });

  // Test getMembers with valid response
  it('should call getMembers and update member lists', () => {
    const mockMembersResponse = {
      data: [
        { id: 1, User: { email: '<EMAIL>' } },
        { id: 2, User: { email: '<EMAIL>' } }
      ]
    };
    projectService.listAllMember.mockReturnValue(of(mockMembersResponse));

    component.getMembers();

    expect(projectService.listAllMember).toHaveBeenCalledWith({
      ProjectId: component.ProjectId,
      ParentCompanyId: component.ParentCompanyId,
    });
    expect(component.memberList).toEqual(mockMembersResponse.data);
    expect(component.updatedMemberList).toEqual([
      { id: 1, User: { email: '<EMAIL>' }, UserEmail: '<EMAIL>' },
      { id: 2, User: { email: '<EMAIL>' }, UserEmail: '<EMAIL>' }
    ]);
  });

  // Test getLocationsForNdrGrid with null response
  it('should handle getLocationsForNdrGrid with null response', () => {
    projectService.getLocations.mockReturnValue(of(null));
    component.getLocationsForNdrGrid();
    expect(component.locationList).toEqual([]);
    expect(component.filteredLocationList).toEqual([]);
  });

  // Test getLocationsForNdrGrid with valid response
  it('should call getLocationsForNdrGrid and update location lists', () => {
    const mockLocationsResponse = {
      data: [
        { id: 1, locationPath: 'Location 1' },
        { id: 2, locationPath: 'Location 2' }
      ]
    };
    projectService.getLocations.mockReturnValue(of(mockLocationsResponse));

    component.getLocationsForNdrGrid();

    expect(projectService.getLocations).toHaveBeenCalledWith({
      ProjectId: component.ProjectId,
      ParentCompanyId: component.ParentCompanyId,
    });
    expect(component.locationList).toEqual(mockLocationsResponse.data);
    expect(component.filteredLocationList).toEqual(mockLocationsResponse.data);
  });

  // Test error handling in export method
  it('should handle export with invalid form and return early', () => {
    component.exportForm.patchValue({
      reportType: '',
      reportName: ''
    });
    component.exportForm.get('reportType').setErrors({ required: true });
    component.exportForm.get('reportName').setErrors({ required: true });

    component.export();
    expect(component.exportSubmitted).toBe(false);
    expect(reportsService.exportInspectionReport).not.toHaveBeenCalled();
  });

  // Test export with CSV type
  it('should handle export with CSV type', () => {
    const template = {} as any;
    component.openModal(template);

    component.exportForm.patchValue({
      reportType: 'CSV',
      reportName: 'Test CSV Report'
    });

    component.reportType = 'CSV';
    component.export();
    expect(reportsService.exportInspectionReport).toHaveBeenCalled();
  });

  // Test export with undefined filterForm
  it('should handle export with undefined filterForm', () => {
    const template = {} as any;
    component.openModal(template);

    component.filterForm = undefined;
    component.exportForm.patchValue({
      reportType: 'PDF',
      reportName: 'Test Report'
    });

    component.export();
    expect(reportsService.exportInspectionReport).toHaveBeenCalled();
  });

  // Test export with special sort conditions
  it('should handle export with special sort conditions', () => {
    const template = {} as any;
    component.openModal(template);

    component.sort = 'DESC';
    component.sortColumn = ' id';
    component.exportForm.patchValue({
      reportType: 'PDF',
      reportName: 'Test Report'
    });

    component.export();
    expect(component.sort).toBe('ASC');
    expect(component.sortColumn).toBe('inspectionStart');
  });

  // Test location selection with children
  it('should handle location selection with children correctly', () => {
    component.locationList = [
      { id: 1, LocationId: null, isDefault: false },
      { id: 2, LocationId: 1, isDefault: false },
      { id: 3, LocationId: 2, isDefault: false }
    ];

    const parentItem = { id: 1, LocationId: null, isDefault: false };
    const selectedIds = [];

    const result = component.handleLocationSelection(parentItem, selectedIds);
    expect(result).toContain(1);
    expect(result).toContain(2);
    expect(result).toContain(3);
  });

  // Test location selection removal with children
  it('should handle location selection removal with children', () => {
    component.locationList = [
      { id: 1, LocationId: null, isDefault: false },
      { id: 2, LocationId: 1, isDefault: false },
      { id: 3, LocationId: 2, isDefault: false }
    ];

    const parentItem = { id: 1, LocationId: null, isDefault: false };
    const selectedIds = [1, 2, 3, 4]; // Include item and children plus extra

    const result = component.handleLocationSelection(parentItem, selectedIds);
    expect(result).not.toContain(1);
    expect(result).not.toContain(2);
    expect(result).not.toContain(3);
    expect(result).toContain(4); // Should keep unrelated items
  });

  // Test toggleSelection with location type and children
  it('should handle toggleSelection with location type and children', () => {
    component.locationList = [
      { id: 1, LocationId: null, isDefault: false, locationPath: 'Location 1' },
      { id: 2, LocationId: 1, isDefault: false, locationPath: 'Location 2' },
      { id: 3, LocationId: 2, isDefault: false, locationPath: 'Location 3' }
    ];

    const parentItem = { id: 1, LocationId: null, isDefault: false, locationPath: 'Location 1' };
    const event = new MouseEvent('click');

    component.toggleSelection(parentItem, 'location', event);
    expect(component.selectedLocation).toContain(parentItem);
    expect(component.selectedLocation.length).toBeGreaterThan(1); // Should include children
  });

  // Test toggleSelection with location type and isDefault
  it('should handle toggleSelection with location type and isDefault true', () => {
    component.locationList = [
      { id: 1, LocationId: null, isDefault: true, locationPath: 'All Locations' },
      { id: 2, LocationId: null, isDefault: false, locationPath: 'Location 2' },
      { id: 3, LocationId: null, isDefault: false, locationPath: 'Location 3' }
    ];

    const defaultItem = { id: 1, LocationId: null, isDefault: true, locationPath: 'All Locations' };
    const event = new MouseEvent('click');

    component.toggleSelection(defaultItem, 'location', event);
    expect(component.selectedLocation.length).toBe(component.locationList.length);
  });

  // Test toggleSelection with location type and isDefault false (unselect all)
  it('should handle toggleSelection with location type and isDefault false to unselect all', () => {
    component.locationList = [
      { id: 1, LocationId: null, isDefault: true, locationPath: 'All Locations' },
      { id: 2, LocationId: null, isDefault: false, locationPath: 'Location 2' },
      { id: 3, LocationId: null, isDefault: false, locationPath: 'Location 3' }
    ];
    component.selectedLocation = [...component.locationList];

    const defaultItem = { id: 1, LocationId: null, isDefault: true, locationPath: 'All Locations' };
    const event = new MouseEvent('click');

    component.toggleSelection(defaultItem, 'location', event);
    expect(component.selectedLocation.length).toBe(0);
  });

  // Test updatePlaceholder with empty list
  it('should handle updatePlaceholder with empty list', () => {
    component.updatePlaceholder('equipment', [], 'equipmentName', 'Select Equipment');
    expect(component.equipmentPlaceholder).toBe('Select Equipment');
  });

  // Test updatePlaceholder with status type and multiple items
  it('should handle updatePlaceholder with status type and multiple items', () => {
    const selectedList = [
      { name: 'Approved' },
      { name: 'Pending' },
      { name: 'Declined' }
    ];

    component.updatePlaceholder('status', selectedList, 'name', 'Select Status');
    expect(component.statusPlaceholder).toBe('Approved + 2 more');
  });

  // Test updatePlaceholder with inspectionStatus type
  it('should handle updatePlaceholder with inspectionStatus type', () => {
    const selectedList = [{ name: 'Pass' }];

    component.updatePlaceholder('inspectionStatus', selectedList, 'name', 'Select Inspection Status');
    expect(component.inspectionStatusPlaceholder).toBe('Pass');
  });

  // Test updatePlaceholder with inspectionType type
  it('should handle updatePlaceholder with inspectionType type', () => {
    const selectedList = [{ name: 'Material' }, { name: 'Safety' }];

    component.updatePlaceholder('inspectionType', selectedList, 'name', 'Select Inspection Type');
    expect(component.inspectionTypePlaceholder).toBe('Material + 1 more');
  });

  // Test updatePlaceholder with define type
  it('should handle updatePlaceholder with define type', () => {
    const selectedList = [{ DFOW: 'DFOW 1' }];

    component.updatePlaceholder('define', selectedList, 'DFOW', 'Select DFOW');
    expect(component.definePlaceholder).toBe('DFOW 1');
  });

  // Test updatePlaceholder with gate type
  it('should handle updatePlaceholder with gate type', () => {
    const selectedList = [{ gateName: 'Gate 1' }, { gateName: 'Gate 2' }];

    component.updatePlaceholder('gate', selectedList, 'gateName', 'Select Gate');
    expect(component.gatePlaceholder).toBe('Gate 1 + 1 more');
  });

  // Test updatePlaceholder with company type
  it('should handle updatePlaceholder with company type', () => {
    const selectedList = [{ companyName: 'Company 1' }];

    component.updatePlaceholder('company', selectedList, 'companyName', 'Select Company');
    expect(component.companyPlaceholder).toBe('Company 1');
  });

  // Test updatePlaceholder with location type
  it('should handle updatePlaceholder with location type', () => {
    const selectedList = [{ locationPath: 'Location 1' }, { locationPath: 'Location 2' }];

    component.updatePlaceholder('location', selectedList, 'locationPath', 'Select Location');
    expect(component.locationPlaceholder).toBe('Location 1 + 1 more');
  });

  // Test error scenarios and edge cases
  it('should handle onDocumentClick with null target', () => {
    const event = new MouseEvent('click');
    Object.defineProperty(event, 'target', { value: null });

    expect(() => component.onDocumentClick(event)).not.toThrow();
  });

  it('should handle onDocumentClick when clicking inside modal', () => {
    const mockModalElement = document.createElement('div');
    mockModalElement.id = 'fiter-temp3';
    document.body.appendChild(mockModalElement);

    const mockTarget = document.createElement('div');
    mockModalElement.appendChild(mockTarget);

    const event = new MouseEvent('click');
    Object.defineProperty(event, 'target', { value: mockTarget });

    component.onDocumentClick(event);

    // Dropdowns should remain in their current state (not forced closed)
    // This tests the early return when clicking inside modal
    expect(true).toBe(true); // Test passes if no error thrown

    document.body.removeChild(mockModalElement);
  });

  it('should handle onDocumentClick when clicking on dropdown trigger', () => {
    const mockElement = document.createElement('div');
    mockElement.className = 'custom-dropdown';
    mockElement.closest = jest.fn().mockReturnValue(mockElement);

    const event = new MouseEvent('click');
    Object.defineProperty(event, 'target', { value: mockElement });

    component.onDocumentClick(event);

    // Should not close dropdowns when clicking on trigger
    expect(true).toBe(true); // Test passes if no error thrown
  });

  it('should handle keyboard events with space key', () => {
    const event = new KeyboardEvent('keydown', { key: ' ' });
    const spy = jest.spyOn(component, 'toggleDropdown');

    component.handleDropdownKeydown(event, 'equipment');
    expect(spy).toHaveBeenCalledWith('equipment');
  });

  it('should handle keyboard events with other keys (no action)', () => {
    const event = new KeyboardEvent('keydown', { key: 'Tab' });
    const spy = jest.spyOn(component, 'toggleDropdown');

    component.handleDropdownKeydown(event, 'equipment');
    expect(spy).not.toHaveBeenCalled();
  });

  it('should handle toggle keydown with space key', () => {
    const event = new KeyboardEvent('keydown', { key: ' ' });
    const spy = jest.spyOn(component, 'sortByField');

    component.handleToggleKeydown(event, 'description', 'DESC');
    expect(spy).toHaveBeenCalledWith('description', 'DESC');
  });

  it('should handle toggle keydown with other keys (no action)', () => {
    const event = new KeyboardEvent('keydown', { key: 'Escape' });
    const spy = jest.spyOn(component, 'sortByField');

    component.handleToggleKeydown(event, 'description', 'DESC');
    expect(spy).not.toHaveBeenCalled();
  });

  // Test openSchedulePopup with all properties
  it('should open schedule popup with all required properties', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.updatedMemberList = [{ id: 1, email: '<EMAIL>' }];
    component.exportType = ['PDF', 'EXCEL'];
    component.filterPayload = { test: 'data' };
    component.sort = 'DESC';
    component.sortColumn = 'date';

    const mockModalRef = {
      content: {} as any
    };
    modalService.show.mockReturnValue(mockModalRef as any);

    component.openSchedulePopup();

    expect(modalService.show).toHaveBeenCalledWith(SchedulerFormComponent);
    expect(mockModalRef.content.ProjectId).toBe('123');
    expect(mockModalRef.content.ParentCompanyId).toBe('456');
    expect(mockModalRef.content.reportType).toBe('Inspection');
    expect(mockModalRef.content.updatedMemberList).toEqual(component.updatedMemberList);
    expect(mockModalRef.content.exportType).toEqual(component.exportType);
    expect(mockModalRef.content.filterPayload).toEqual(component.filterPayload);
    expect(mockModalRef.content.sort).toBe('DESC');
    expect(mockModalRef.content.sortByField).toBe('date');
  });

  // Test openModalSave with all properties
  it('should open save modal with all required properties', () => {
    component.ProjectId = '123';
    component.updatedMemberList = [{ id: 1, email: '<EMAIL>' }];
    component.exportType = ['PDF', 'EXCEL'];
    component.filterPayload = { test: 'data' };
    component.pageSize = 50;
    component.pageNo = 2;

    const mockModalRef = {
      content: {} as any
    };
    modalService.show.mockReturnValue(mockModalRef as any);

    component.openModalSave();

    expect(modalService.show).toHaveBeenCalledWith(SaveReportFormComponent, { class: 'modal-save' });
    expect(mockModalRef.content.ProjectId).toBe('123');
    expect(mockModalRef.content.reportType).toBe('Inspection');
    expect(mockModalRef.content.updatedMemberList).toEqual(component.updatedMemberList);
    expect(mockModalRef.content.exportType).toEqual(component.exportType);
    expect(mockModalRef.content.pageSize).toBe(50);
    expect(mockModalRef.content.pageNo).toBe(2);
    expect(mockModalRef.content.sortOrder).toBe('asc');
    expect(mockModalRef.content.void).toBe(0);
  });

  // Test closeFilterPopup when modalRef4 is null
  it('should handle closeFilterPopup when modalRef4 is null', () => {
    component.modalRef4 = null;

    expect(() => component.closeFilterPopup()).not.toThrow();
    expect(component.isEquipmentDropdownOpen).toBe(false);
    expect(component.isStatusDropdownOpen).toBe(false);
  });

  // Test selectOption with null event
  it('should handle selectOption with null event', () => {
    const item = { id: 1, name: 'Test' };

    expect(() => component.selectOption(item, 'equipment', null)).not.toThrow();
  });

  // Test selectOption with missing form control
  it('should handle selectOption with missing form control', () => {
    const item = { id: 1, name: 'Test' };
    const event = new MouseEvent('click');

    // Create form without equipmentFilter
    const formBuilder = TestBed.inject(UntypedFormBuilder);
    component.filterForm = formBuilder.group({
      otherFilter: ['']
    });

    expect(() => component.selectOption(item, 'equipment', event)).not.toThrow();
  });

  // Test selectOption with non-array form value
  it('should handle selectOption with non-array form value', () => {
    const item = { id: 1, name: 'Test' };
    const event = new MouseEvent('click');

    component.filterForm.get('equipmentFilter').setValue('not-an-array');

    expect(() => component.selectOption(item, 'equipment', event)).not.toThrow();
  });
});
