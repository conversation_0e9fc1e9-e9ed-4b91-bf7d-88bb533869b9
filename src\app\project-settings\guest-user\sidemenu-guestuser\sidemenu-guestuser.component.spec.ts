import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { of } from 'rxjs';
import { SidemenuGuestuserComponent } from './sidemenu-guestuser.component';
import { ProjectSettingsService, ProjectSharingService } from '../../../services';

describe('SidemenuGuestuserComponent', () => {
  let component: SidemenuGuestuserComponent;
  let fixture: ComponentFixture<SidemenuGuestuserComponent>;
  let projectSettingsServiceSpy: jest.Mocked<ProjectSettingsService>;
  let projectSharingServiceSpy: jest.Mocked<ProjectSharingService>;
  let routerSpy: jest.Mocked<Router>;

  const mockProjectId = '123';
  const mockParentCompanyId = '456';
  const mockUrl = 'test-url';

  beforeEach(async () => {
    // Create spies for services
    projectSettingsServiceSpy = {
      getGuestProjectSettings: jest.fn().mockReturnValue(of({
        data: {
          projectSettings: {
            allowGuestToViewDeliveryCalendar: true,
            allowGuestToViewCraneCalendar: false,
            allowGuestToViewConcreteCalendar: true,
            isPublicWebsiteEnabled: true
          }
        }
      }))
    } as any;

    projectSharingServiceSpy = {} as any;
    
    routerSpy = {
      navigate: jest.fn()
    } as any;

    // Mock window.atob
    const originalAtob = window.atob;
    window.atob = jest.fn((str: string) => {
      if (str === btoa(mockProjectId)) return mockProjectId;
      if (str === btoa(mockParentCompanyId)) return mockParentCompanyId;
      if (str === btoa(mockUrl)) return mockUrl;
      return originalAtob(str);
    });

    // Mock localStorage
    jest.spyOn(Storage.prototype, 'getItem').mockImplementation((key: string) => {
      switch (key) {
        case 'guestProjectId':
          return btoa(mockProjectId);
        case 'guestParentCompanyId':
          return btoa(mockParentCompanyId);
        case 'url':
          return btoa(mockUrl);
        default:
          return null;
      }
    });

    await TestBed.configureTestingModule({
      declarations: [SidemenuGuestuserComponent],
      providers: [
        { provide: ProjectSettingsService, useValue: projectSettingsServiceSpy },
        { provide: ProjectSharingService, useValue: projectSharingServiceSpy },
        { provide: Router, useValue: routerSpy }
      ]
    }).compileComponents();
  });

  afterEach(() => {
    // Restore original window.atob
    window.atob = window.atob;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SidemenuGuestuserComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // it('should initialize with correct values from localStorage', () => {
  //   expect(component.ProjectId).toBe(123);
  //   expect(component.ParentCompanyId).toBe(456);
  // });

  it('should call getProjectSettings on initialization', () => {
    const mockResponse = {
      data: {
        projectSettings: {
          allowGuestToViewDeliveryCalendar: true,
          allowGuestToViewCraneCalendar: false,
          allowGuestToViewConcreteCalendar: true,
          isPublicWebsiteEnabled: true
        }
      }
    };

    projectSettingsServiceSpy.getGuestProjectSettings.mockReturnValue(of(mockResponse));
    
    // Create a new instance to ensure initialization
    fixture = TestBed.createComponent(SidemenuGuestuserComponent);
    component = fixture.componentInstance;

    // expect(projectSettingsServiceSpy.getGuestProjectSettings).toHaveBeenCalledWith({
    //   ProjectId: 123
    // });

    expect(component.showDeliveryCalendar).toBe(true);
    expect(component.showCraneCalendar).toBe(false);
    expect(component.showConcreteCalendar).toBe(true);
    expect(component.isPublicWebsiteEnabled).toBe(true);
  });

  it('should emit toggle event when closeSettingsCollapse is called', () => {
    const emitSpy = jest.spyOn(component.toggleEmitEvent, 'emit');
    component.closeSettingsCollapse();
    expect(emitSpy).toHaveBeenCalled();
  });

  it('should handle keyboard events correctly', () => {
    const closeSettingsSpy = jest.spyOn(component, 'closeSettingsCollapse');
    
    // Test Enter key
    const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
    component.handleToggleKeydown(enterEvent);
    expect(closeSettingsSpy).toHaveBeenCalled();

    // Test Space key
    const spaceEvent = new KeyboardEvent('keydown', { key: ' ' });
    component.handleToggleKeydown(spaceEvent);
    expect(closeSettingsSpy).toHaveBeenCalled();

    // Test other key
    const otherEvent = new KeyboardEvent('keydown', { key: 'A' });
    component.handleToggleKeydown(otherEvent);
    expect(closeSettingsSpy).toHaveBeenCalledTimes(2); // Should not increment
  });

  it('should handle error in getProjectSettings', () => {
    // Reset the mock to return null
    projectSettingsServiceSpy.getGuestProjectSettings.mockReturnValue(of(null));
    
    // Create a new instance to ensure initialization
    fixture = TestBed.createComponent(SidemenuGuestuserComponent);
    component = fixture.componentInstance;
    
    expect(component.showDeliveryCalendar).toBeUndefined();
    expect(component.showCraneCalendar).toBeUndefined();
    expect(component.showConcreteCalendar).toBeUndefined();
    expect(component.isPublicWebsiteEnabled).toBeUndefined();
  });
});
