/* eslint-disable no-lonely-if */
/* eslint-disable max-lines-per-function */
/* eslint-disable no-underscore-dangle */
import {
  Component, TemplateRef, ViewChild, AfterViewInit,
} from '@angular/core';
import { FullCalendarComponent } from '@fullcalendar/angular'; // useful for typechecking
import { Calendar, CalendarOptions } from '@fullcalendar/core'; // useful for typechecking
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import { Router } from '@angular/router';
import moment from 'moment';
import interactionPlugin from '@fullcalendar/interaction';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import { Title } from '@angular/platform-browser';
import { CalendarService } from '../services/profile/calendar.service';
import { ProjectService } from '../services/profile/project.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { NewDeliveryFormComponent } from '../delivery-requests/delivery-details/new-delivery-form/new-delivery-form.component';
import { EditDeliveryFormComponent } from '../delivery-requests/delivery-details/edit-delivery-form/edit-delivery-form.component';
import { DeliveryDetailsNewComponent } from '../delivery-requests/delivery-details/delivery-details-new/delivery-details-new.component';
import { MixpanelService } from '../services/mixpanel.service';

@Component({
  selector: 'app-calendar',
  templateUrl: './calendar.component.html',
})
export class CalendarComponent implements AfterViewInit {
  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public descriptionPopup = false;

  public events: any = [];

  public calendar: Calendar;

  public calendarApi: any;

  public ProjectId: any;

  public Range: any = {};

  public loader = true;

  public deliveryList: any = [];

  public search = '';

  public lastId: any = 0;

  public dropdownSettings: IDropdownSettings;

  public eventData: any = {};

  public calendarCurrentDeliveryIndex = -1;

  public showStatus = false;

  public authUser: any = {};

  public filterForm: UntypedFormGroup;

  public filterCount = 0;

  public definableDropdownSettings: IDropdownSettings;

  public statusValue: any = [];

  public currentStatus = '';

  public currentEditItem: any = {};

  public modalLoader = false;

  public companyList: any = [];

  public defineList: any = [];

  public voidSubmitted = false;

  public gateList: any = [];

  public equipmentList: any = [];

  public submitted = false;

  public formSubmitted = false;

  public memberList: any = [];

  public statusSubmitted = false;

  public deliveryId;

  public currentDeliverySaveItem: any = {};

  public ParentCompanyId;

  public showSearchbar = false;

  public calendarDescriptionPopup = false;

  public viewEventData: any;

  public message = '';

  public toolTipContent = '';

  public wholeStatus = ['Approved', 'Declined', 'Delivered', 'Pending'];

  public currentViewMonth: moment.MomentInput;

  public currentView = 'Month';

  public subscription: any;

  public subscription1: any;

  public monthlyEventloader = true;

  public monthEvents = [];

  public allRequestIsOpened = false;

  public seriesOptions = [];

  public approved: string;

  public pending: string;

  public rejected: string;

  public delivered: string;

  public expired: string;

  public locationList: any = [];

  @ViewChild('fullcalendar', { static: true }) public calendarComponent1: FullCalendarComponent;

  public calendarOptions: CalendarOptions = {
    selectable: true,
    initialView: 'dayGridMonth',
    plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin],
    aspectRatio: 2,
    slotEventOverlap: false,
    contentHeight: 'liquid',
    fixedWeekCount: false,
    expandRows: true,
    nowIndicator: true,
    moreLinkClick: 'popover',
    events: this.events,
    customButtons: {
      prev: {
        text: 'PREV',
        click: (): void => this.goPrev(),
      },
      next: {
        text: 'Next',
        click: (): void => this.goNext(),
      },
      prevYear: {
        text: 'PREV',
        click: (): void => this.goPrevYear(),
      },
      nextYear: {
        text: 'Next',
        click: (): void => this.goNextYear(),
      },
      timeGridWeek: {
        text: 'Week',
        click: (): void => this.goTimeGridWeekOrDay('timeGridWeek'),
      },
      timeGridDay: {
        text: 'Day',
        click: (): void => this.goTimeGridWeekOrDay('timeGridDay'),
      },
      dayGridMonth: {
        text: 'Month',
        click: (): void => this.goDayGridMonth(),
      },
    },
    showNonCurrentDates: false,
    headerToolbar: {
      right: '',
      center: 'prevYear,prev,title,next,nextYear',
      left: 'dayGridMonth,timeGridWeek,timeGridDay',
    },
    firstDay: 0,
    eventClick: (arg): void => {
      this.eventData = [];
      this.deliveryDescription(arg);
    },
    datesSet: (info): void => {
      this.currentViewMonth = info.view.title;
    },
    eventTimeFormat: {
      hour: 'numeric',
      minute: '2-digit',
      meridiem: 'short',
    },
    eventDidMount(info): void {
      const argument = info;
      const line1 = argument.event._def.extendedProps.description;
      const { line2 } = argument.event._def.extendedProps;
      const eventTitleElement: HTMLElement = argument.el.querySelector('.fc-event-title');
      eventTitleElement.style.backgroundColor = info.backgroundColor;
      eventTitleElement.style.color = info.textColor;
      eventTitleElement.style.borderLeft = `4px solid ${info.borderColor}`;
      if (argument.event._def.allDay) {
        eventTitleElement.innerHTML = `
        <div class="d-flex flex-column">
        <p class="m-0"> <img class="w10 h10" src="./assets/images/noun-event-alert.svg" class="form-icon" alt="allday" >  ${line1} </p>
        <small> ${line2} </small>
        </div>`;
      } else {
        // eslint-disable-next-line no-param-reassign
        eventTitleElement.innerHTML = `
          <div class="d-flex flex-column">
          <p class="m-0"> ${line1} </p>
          <small class="text-wrap"> ${line2} </small>
          </div>`;
      }
    },
    dayMaxEventRows: true, // for all non-TimeGrid views
    views: {
      timeGrid: {
        dayMaxEventRows: 2, // adjust to 6 only for timeGridWeek/timeGridDay
      },
      dayGridMonth: {
        allDaySlot: true,
      },
      timeGridWeek: {
        allDaySlot: true,
      },
      timeGridDay: {
        allDaySlot: true,
      },
    },
    dateClick: (info): void => {
      this.openAddDeliveryRequestModal(info);
    },
  };

  public constructor(
    public bsModalRef: BsModalRef,
    private readonly modalService: BsModalService,
    public calendarService: CalendarService,
    public projectService: ProjectService,
    private readonly formBuilder: UntypedFormBuilder,
    public deliveryService: DeliveryService,
    private readonly toastr: ToastrService,
    public router: Router,
    public socket: Socket,
    private readonly mixpanelService: MixpanelService,
    private readonly titleService: Title,
  ) {
    this.titleService.setTitle('Follo - Delivery Calendar');
    this.filterDetailsForm();
    this.deliveryService.refresh.subscribe((getEventNdrResponse): void => {
      if (
        getEventNdrResponse !== undefined
        && getEventNdrResponse !== null
        && getEventNdrResponse !== ''
      ) {
        this.getEventNDR();
      }
    });
    this.deliveryService.refresh1.subscribe((getEventNdrResponse): void => {
      if (
        getEventNdrResponse !== undefined
        && getEventNdrResponse !== null
        && getEventNdrResponse !== ''
      ) {
        this.getEventNDR();
      }
    });
    this.deliveryService.getCurrentStatus.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.deliveryId = res;
        this.getEventNDR();
      }
    });
  }

  public openAddDeliveryRequestModal(dateArg): void {
    const passData = {
      date: dateArg.dateStr,
      currentView: this.currentView,
    };
    const initialState = {
      data: passData,
      title: 'Modal with component',
    };
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(NewDeliveryFormComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
      initialState,
    });
    this.modalRef.content.lastId = this.lastId;
    this.modalRef.content.closeBtnName = 'Close';
  }

  public initializeSeriesOption(): void {
    this.seriesOptions = [
      {
        option: 1,
        text: 'This event',
        disabled: false,
      },
      {
        option: 2,
        text: 'This and all following events',
        disabled: false,
      },
      // {
      //   option: 3,
      //   text: 'All events in the series',
      //   disabled: false,
      // },
    ];
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'edit':
          this.openEditModal(data, item);
          break;
        case 'clear':
          this.clear();
          break;
        case 'filter':
          this.openFilterModal(data);
          break;
        case 'open':
          this.openIdModal(data);
          break;
        case 'close':
          this.closeCalendarDescription();
          break;
        default:
          break;
      }
    }
  }

  public changeRequestCollapse(data): void {
    this.initializeSeriesOption();
    if (!moment(data.deliveryStart).isAfter(moment())) {
      this.seriesOptions = this.seriesOptions.filter((object): any => {
        const seriesObject = object;
        if (seriesObject.option !== 1) {
          seriesObject.disabled = true;
        }
        return seriesObject;
      });
    }
    this.allRequestIsOpened = !this.allRequestIsOpened;
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.getEventNDR();
  }

  public getSearchNDR(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.search = data;
    this.getEventNDR();
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.search = '';
    this.filterDetailsForm();
    this.getEventNDR();
    this.modalRef.hide();
  }

  public calendarGetCompany(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getCompanies(params).subscribe((companyResponse: any): void => {
      if (companyResponse) {
        this.companyList = companyResponse.data;
        this.calendarGetDefinable();
        this.dropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'companyName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
      }
    });
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      companyFilter: [''],
      descriptionFilter: [''],
      statusFilter: [''],
      memberFilter: [''],
      gateFilter: [''],
      equipmentFilter: [''],
      locationFilter: [''],
      dateFilter: [''],
      pickFrom: [''],
      pickTo: [''],
    });
  }

  public ngAfterViewInit(): void {
    this.projectService.projectParent.subscribe((response): void => {
      if (response !== undefined && response !== null && response !== '') {
        this.ParentCompanyId = response.ParentCompanyId;
        this.ProjectId = response.ProjectId;
        this.getMembers();
        this.setCalendar();
      }
    });
  }

  public ngOnInit(): void {
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
        if (this.authUser.RoleId === 2) {
          this.statusValue = ['Approved', 'Declined'];
        } else if (this.authUser.RoleId === 3) {
          this.statusValue = ['Delivered'];
        }
      }
    });
  }

  public setCalendar(): void {
    this.calendarApi = this.calendarComponent1.getApi();
    this.Range = this.calendarApi?.currentData?.dateProfile?.activeRange;

    this.getEventNDR();
  }

  public selectStatus(status): void {
    this.currentStatus = status;
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('descriptionFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('dateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('companyFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('memberFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('gateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('equipmentFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('locationFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('statusFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('pickFrom').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('pickTo').value !== '') {
      this.filterCount += 1;
    }
    this.getEventNDR();
    this.modalRef.hide();
  }

  public getEventNDR(): void {
    this.loader = true;
    this.deliveryList = [];
    if (this.ProjectId && this.ParentCompanyId) {
      const filterParams = {
        ProjectId: this.ProjectId,
        void: 0,
      };
      let getEventNdrPayload: any = {};
      if (this.filterForm !== undefined) {
        const getEventNdrDateInFilterForm = this.filterForm.value.dateFilter
          ? moment(this.filterForm.value.dateFilter).format('YYYY-MM-DD')
          : this.filterForm.value.dateFilter;
        getEventNdrPayload = {
          companyFilter: +this.filterForm.value.companyFilter,
          descriptionFilter: this.filterForm.value.descriptionFilter,
          dateFilter: getEventNdrDateInFilterForm,
          statusFilter: this.filterForm.value.statusFilter,
          memberFilter: +this.filterForm.value.memberFilter,
          gateFilter: +this.filterForm.value.gateFilter,
          equipmentFilter: this.filterForm.value.equipmentFilter === null || this.filterForm.value.equipmentFilter === '' ? null : +this.filterForm.value.equipmentFilter,
          locationFilter: this.filterForm.value.locationFilter,
          pickFrom: this.filterForm.value.pickFrom,
          pickTo: this.filterForm.value.pickTo,
          ParentCompanyId: this.ParentCompanyId,
          search: this.search,
        };
      }
      getEventNdrPayload.search = this.search;
      getEventNdrPayload.start = moment(this.Range?.start).format('YYYY-MM-DD');
      getEventNdrPayload.end = moment(this.Range?.end).format('YYYY-MM-DD');
      getEventNdrPayload.filterCount = this.filterCount;
      getEventNdrPayload.calendarView = this.currentView;
      this.calendarService
        .getEventNDR(filterParams, getEventNdrPayload)
        .subscribe((NdrResponse: any): void => {
          if (NdrResponse) {
            this.loader = false;
            const responseData = NdrResponse.data;
            const statusData = JSON.parse(NdrResponse.statusData.statusColorCode);
            const cardData = JSON.parse(NdrResponse.cardData.deliveryCard);
            const UseTextColorAsLegendColor = JSON.parse(NdrResponse.statusData.useTextColorAsLegend);
            const isDefaultColor = JSON.parse(NdrResponse.statusData.isDefaultColor);
            const approved = statusData.find((item) => item.status === 'approved');
            const pending = statusData.find((item) => item.status === 'pending');
            const delivered = statusData.find((item) => item.status === 'delivered');
            const rejected = statusData.find((item) => item.status === 'rejected');
            const expired = statusData.find((item) => item.status === 'expired');
            if (UseTextColorAsLegendColor) {
              this.approved = approved.fontColor;
              this.pending = pending.fontColor;
              this.expired = expired.fontColor;
              this.rejected = rejected.fontColor;
              this.delivered = delivered.fontColor;
            } else {
              this.approved = approved.backgroundColor;
              this.pending = pending.backgroundColor;
              this.expired = expired.backgroundColor;
              this.rejected = rejected.backgroundColor;
              this.delivered = delivered.backgroundColor;
            }
            if (isDefaultColor) {
              this.delivered = delivered.backgroundColor;
            }
            this.lastId = NdrResponse.lastId;
            this.deliveryList = responseData;
            this.events = [];
            const previewSelected = cardData.filter((item) => item.selected);
            this.deliveryList.forEach((element): void => {
              let assignData: any = {
                description: '',
                title: '',
                start: '',
                end: '',
                id: '',
                uniqueNumber: '',
                companyName: '',
                allDay: false,
                allDaySlot: false,
                line2: '',
              };
              assignData.start = element.requestType === 'deliveryRequest'
                || element.requestType === 'deliveryRequestWithCrane'
                ? element.deliveryStart
                : element.fromDate;
              assignData.end = element.requestType === 'deliveryRequest'
                || element.requestType === 'deliveryRequestWithCrane'
                ? element.deliveryEnd
                : element.toDate;
              assignData.id = element.id;
              assignData.uniqueNumber = element.uniqueNumber;

              if (element.requestType === 'calendarEvent') {
                assignData.description = element.description;
                assignData.title = element.description;
              }

              if (element.requestType === 'calendarEvent' && element.isAllDay === true) {
                delete assignData.allDay;
                delete assignData.allDaySlot;
                assignData.allDay = true;
                assignData.allDaySlot = true;
              }

              if (
                element.requestType === 'deliveryRequest'
                || element.requestType === 'deliveryRequestWithCrane'
              ) {
                assignData = this.checkRequestType(previewSelected, assignData, element);
              }
              if (element.status === 'Pending') {
                assignData.color = pending.backgroundColor;
                assignData.textColor = pending.fontColor;
                assignData.borderColor = pending.fontColor;
                this.events.push(assignData);
              } else if (element.status === 'Approved') {
                assignData.color = approved.backgroundColor;
                assignData.textColor = approved.fontColor;
                assignData.borderColor = approved.fontColor;
                this.events.push(assignData);
              } else if (element.status === 'Declined') {
                assignData.color = rejected.backgroundColor;
                assignData.textColor = rejected.fontColor;
                assignData.borderColor = rejected.fontColor;
                this.events.push(assignData);
              } else if (element.status === 'Expired') {
                assignData.color = expired.backgroundColor;
                assignData.textColor = expired.fontColor;
                assignData.borderColor = expired.fontColor;
                this.events.push(assignData);
              } else if (element.status === 'Delivered') {
                assignData.color = delivered.backgroundColor;
                assignData.textColor = delivered.fontColor;
                assignData.borderColor = delivered.backgroundColor;
                this.events.push(assignData);
              } else if (!element.status) {
                assignData.className = 'calendar_event';
                this.events.push(assignData);
              }
            });
            this.calendarApi.removeAllEventSources();
            this.calendarApi.addEventSource(this.events);
          }
        });
    }
  }

  public checkRequestType(previewSelected, assignDataParam, element) {
    const assignData = { ...assignDataParam };

    const getValueByLabel = (label: string): string => {
      switch (label) {
        case 'Description':
          return element.description || '';

        case 'Responsible Company':
          return element?.companyDetails?.[0]?.Company?.companyName || '';

        case 'Responsible Person': {
          const user = element?.memberDetails?.[0]?.Member?.User;
          return user ? `${user.firstName} ${user.lastName}` : '';
        }

        case 'Gate':
          return element?.gateDetails?.[0]?.Gate?.gateName || '';

        case 'Delivery ID':
          return element.DeliveryId || '';

        case 'Definable Feature Of Work':
          return element.defineWorkDetails?.[0]?.DeliverDefineWork?.DFOW || '';

        case 'Equipment':
          return element.equipmentDetails?.[0]?.Equipment?.equipmentName || '';

        default:
          return '';
      }
    };

    previewSelected.forEach((item) => {
      const value = getValueByLabel(item.label);
      if (item.line === 1) {
        assignData.description = value;
      } else if (item.line === 2) {
        assignData.line2 = value;
      }
    });

    return assignData;
  }


  public goNext(): void {
    this.calendarApi.next();
    this.setCalendar();
  }

  public goTimeGridWeekOrDay(view): void {
    if (view === 'timeGridWeek') {
      this.currentView = 'Week';
    } else {
      this.currentView = 'Day';
    }
    this.closeDescription();
    this.calendarApi.changeView(view);
    this.setCalendar();
  }

  public goDayGridMonth(): void {
    this.currentView = 'Month';
    this.closeDescription();
    this.calendarApi.changeView('dayGridMonth');
    this.setCalendar();
  }

  public goPrev(): void {
    this.closeDescription();
    this.calendarApi.prev(); // call a method on the Calendar object
    this.setCalendar();
  }

  public goPrevYear(): void {
    this.closeDescription();
    this.calendarApi.prevYear(); // call a method on the Calendar object
    this.setCalendar();
  }

  public goNextYear(): void {
    this.closeDescription();
    this.calendarApi.nextYear(); // call a method on the Calendar object
    this.setCalendar();
  }

  public deliveryDescription(arg): void {
    this.eventData = [];
    this.allRequestIsOpened = false;
    this.calendarDescriptionPopup = false;
    this.descriptionPopup = false;
    if (Object.keys(arg).length !== 0) {
      let index;
      if (arg.event.extendedProps.uniqueNumber) {
        index = this.deliveryList.findIndex(
          (item: { id: any; uniqueNumber: any }): any => item.id === +arg.event.id && item.uniqueNumber === arg.event.extendedProps.uniqueNumber,
        );
      } else {
        index = this.deliveryList.findIndex((item): any => item.id === +arg.event.id);
      }
      this.eventData = this.deliveryList[index];
      this.calendarCurrentDeliveryIndex = index;
      if (
        this.eventData.requestType === 'deliveryRequest'
        || this.eventData.requestType === 'deliveryRequestWithCrane'
      ) {
        this.eventData.startDate = moment(
          this.deliveryList[this.calendarCurrentDeliveryIndex].deliveryStart,
        ).format('MM/DD/YYYY');
        if (this.eventData.approved_at !== null) {
          this.eventData.approvedAt = moment(this.eventData.approved_at).format('lll');
        }
        this.eventData.startTime = moment(
          this.deliveryList[this.calendarCurrentDeliveryIndex].deliveryStart,
        ).format('hh:mm A');
        this.eventData.endTime = moment(this.eventData.deliveryEnd).format('hh:mm A');
        this.getNDR(arg);
        this.calendarDescriptionPopup = false;
        this.openIdModal(this.eventData);
      } else if (this.eventData.requestType === 'calendarEvent') {
        this.calendarDescription(arg);
      }
    }
  }

  public closeCalendarDescription(): void {
    this.calendarDescriptionPopup = false;
    this.descriptionPopup = false;
    this.allRequestIsOpened = false;
    this.viewEventData = '';
  }

  public calendarDescription(arg): void {
    this.calendarDescriptionPopup = false;
    this.descriptionPopup = false;
    this.viewEventData = '';
    if (Object.keys(arg).length !== 0) {
      const index = this.events.findIndex(
        (item: any): any => item.description === arg.event.title
          && item.uniqueNumber === arg.event.extendedProps.uniqueNumber,
      );
      this.viewEventData = this.deliveryList[index];
      this.occurMessage(this.viewEventData);
      this.calendarDescriptionPopup = true;
    }
  }

  public occurMessage(data): void {
    this.message = 'Occurs every day';
    if (data.repeatEveryType === 'Day') {
      this.message = '';
      this.message = 'Occurs every day';
    }
    if (data.repeatEveryType === 'Days') {
      this.message = '';
      if (+data.repeatEveryCount === 2) {
        this.message = 'Occurs every other day';
      } else {
        this.message = `Occurs every ${data.repeatEveryCount} days`;
      }
    }
    if (data.repeatEveryType === 'Week') {
      this.message = '';
      let weekDays = '';
      data.days.forEach((day1: any): any => {
        weekDays = `${weekDays + day1},`;
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message += `Occurs every ${weekDays}`;
    }
    if (data.repeatEveryType === 'Weeks') {
      let weekDays = '';
      data.days.forEach((day1: any): any => {
        weekDays = `${weekDays + day1},`;
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message = '';
      if (+data.repeatEveryCount === 2) {
        this.message = `Occurs every other  ${weekDays}`;
      } else {
        this.message = `Occurs every ${data.repeatEveryCount} weeks on ${weekDays}`;
      }
    }
    if (
      data.repeatEveryType === 'Month'
      || data.repeatEveryType === 'Months'
      || data.repeatEveryType === 'Year'
      || data.repeatEveryType === 'Years'
    ) {
      if (data.chosenDateOfMonth) {
        this.message = `Occurs on day ${data.dateOfMonth}`;
      } else {
        this.message = `Occurs on the ${data.monthlyRepeatType}`;
      }
    }
    this.message += ` until ${moment(data.endTime).format('MM-DD-YYYY')}`;
  }

  public closeDescription(): void {
    this.descriptionPopup = false;
  }

  public changeFormat(fromDate: any): any {
    if (fromDate) {
      const dayFormat = moment(new Date(fromDate)).format('ddd MM/DD/YYYY');
      return dayFormat;
    }
  }

  public setStatus(item): void {
    this.deliveryId = -1;
    this.deliveryService.updatedDeliveryId(item.id);
    this.calendarCurrentDeliveryIndex = this.deliveryList.findIndex(
      (i): boolean => i.id === item.id,
    );
    this.currentDeliverySaveItem = this.deliveryList[this.calendarCurrentDeliveryIndex];
    this.currentDeliverySaveItem.edit = item.edit;
    const condition = item.status !== 'Expired' && item.status !== 'Delivered';
    if (
      (this.authUser.RoleId === 2 && condition)
      || (this.authUser.RoleId === 3 && item.status === 'Approved')
    ) {
      this.showStatus = true;
    } else {
      this.showStatus = false;
    }
  }

  public openIdModal(item): void {
    const data = item;
    data.ParentCompanyId = this.ParentCompanyId;
    this.deliveryService.updatedCurrentStatus(data.id);
    const initialState = {
      data,
      title: 'Modal with component',
    };
    this.modalRef = this.modalService.show(DeliveryDetailsNewComponent, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-delivery-popup custom-modal',
      initialState,
    });
    this.modalRef.content.closeBtnName = 'Close';
  }

  public moveToVoidList(): void {
    if (!this.voidSubmitted) {
      this.voidSubmitted = true;
      const calendarCurrentDeliveryItem = this.deliveryList[this.calendarCurrentDeliveryIndex];
      const addToVoidPayload = {
        DeliveryRequestId: calendarCurrentDeliveryItem.id,
        ProjectId: this.ProjectId,
      };
      this.deliveryService.createVoid(addToVoidPayload).subscribe({
        next: (createVoidResponse: any): void => {
          if (createVoidResponse) {
            this.toastr.success(createVoidResponse.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Delivery Booking Voided');
            this.voidSubmitted = false;
            this.router.navigate(['/void-list']);
            this.closeDescription();
            this.close();
          }
        },
        error: (addToVoidError): void => {
          this.voidSubmitted = false;
          if (addToVoidError.message?.statusCode === 400) {
            this.showError(addToVoidError);
          } else if (!addToVoidError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(addToVoidError.message, 'OOPS!');
          }
        },
      });
    }
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public calendarGetOverAllGate(): void {
    this.modalLoader = true;
    const calendarGetGateParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .gateList(calendarGetGateParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((calendarGetGateListResponse): void => {
        this.gateList = calendarGetGateListResponse.data;
        this.calendarGetOverAllEquipment();
      });
  }

  public calendarGetOverAllEquipment(): void {
    const calendarGetEquipmentParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listEquipment(calendarGetEquipmentParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((calendarGetEquipmentListParams): void => {
        this.equipmentList = calendarGetEquipmentListParams.data;
        this.calendarGetCompany();
      });
  }

  public calendarGetDefinable(): void {
    const calendarGetDefinableParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getDefinableWork(calendarGetDefinableParams)
      .subscribe((calendarGetDefinableListResponse: any): void => {
        if (calendarGetDefinableListResponse) {
          const { data } = calendarGetDefinableListResponse;
          this.defineList = data;
          this.definableDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'DFOW',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
          this.getLocations();
        }
      });
  }

  public getLocations(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getLocations(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.locationList = data;
        this.openContentModal();
      }
    });
  }

  public close(): void {
    this.submitted = false;
    this.formSubmitted = false;
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  public convertStart(deliveryDate, startHours, startMinutes): string {
    const fullYear = deliveryDate.getFullYear();
    const fullMonth = deliveryDate.getMonth();
    const date = deliveryDate.getDate();
    const deliveryNewStart = new Date(fullYear, fullMonth, date, startHours, startMinutes);
    const deliveryStart = deliveryNewStart.toUTCString();
    return deliveryStart;
  }

  public checkFutureDate(deliveryStart, deliveryEnd): boolean {
    const startDate = new Date(deliveryStart).getTime();
    const currentDate = new Date().getTime();
    const endDate = new Date(deliveryEnd).getTime();
    if (startDate > currentDate && endDate > currentDate) {
      if (startDate < endDate) {
        return true;
      }
      return false;
    }
    return false;
  }

  public getResponsiblePeople(object): any {
    if (object?.firstName && object?.lastName) {
      const string = `${object.firstName} ${object.lastName}`;
      const matches = string.match(/\b(\w)/g);
      const acronym = matches.join('').toUpperCase();
      return acronym;
    }
    return 'UU';
  }

  public getNDR(data): void {
    this.toolTipContent = '';
    const param = {
      DeliveryRequestId: +data.event.id,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.getNDRData(param).subscribe((res): void => {
      if (this.authUser.RoleId === 4 || this.authUser.RoleId === 3) {
        const newMember = res.data.memberDetails;
        const index = newMember.findIndex((i): boolean => i.Member.id === this.authUser.id);
        if (index !== -1) {
          this.eventData.edit = true;
        } else {
          this.eventData.edit = false;
        }
      } else {
        this.eventData.edit = true;
      }
      this.eventData.companyDetails = res.data.companyDetails;
      this.eventData.gateDetails = res.data.gateDetails;
      this.eventData.memberDetails = res.data.memberDetails;
      this.eventData.equipmentDetails = res.data.equipmentDetails;
      if (this.eventData.memberDetails.length > 3) {
        const slicedArray = this.eventData?.memberDetails?.slice(3) || [];
        slicedArray.map((a): any => {
          if (a.Member.User.firstName) {
            this.toolTipContent += `${a.Member.User.firstName} ${a.Member.User.lastName}, `;
          } else {
            this.toolTipContent += `${a.Member.User.email}, `;
          }
        });
      }
    });
  }

  public getEditValue(data): void {
    const param = {
      DeliveryRequestId: +data.id,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.getNDRData(param).subscribe((res): void => {
      const newMember = res.data.memberDetails;
      const index = newMember.findIndex((i): boolean => i.Member.id === this.authUser.id);
      if (index !== -1) {
        this.eventData.edit = true;
      } else {
        this.eventData.edit = false;
      }
      this.eventData.companyDetails = res.data.companyDetails;
      this.eventData.gateDetails = res.data.gateDetails;
      this.setStatus(this.eventData);
    });
  }

  public openAddNDRModal(): void {
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(NewDeliveryFormComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
    });
    this.modalRef.content.lastId = this.lastId;
    this.modalRef.content.closeBtnName = 'Close';
  }

  public openEditModal(item, action): void {
    this.closeDescription();
    if (this.modalRef) {
      this.close();
    }
    this.deliveryService.updatedDeliveryId(item.id);
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(EditDeliveryFormComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
    });
    this.modalRef.content.closeBtnName = 'Close';
    this.modalRef.content.seriesOption = item?.recurrence && item?.recurrence?.recurrence !== 'Does Not Repeat' ? action : 1;
    this.modalRef.content.recurrenceId = item?.recurrence ? item?.recurrence?.id : null;
    this.modalRef.content.recurrenceEndDate = item?.recurrence
      ? item?.recurrence?.recurrenceEndDate
      : null;
  }

  public openContentModal(): void {
    this.modalLoader = false;
  }

  public openDeleteModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template);
  }

  public openFilterModal(template: TemplateRef<any>): void {
    this.calendarGetOverAllGate();
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-sm filter-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }

  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public voidConfirmationResponse(action): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.moveToVoidList();
    }
  }
}
function getEventNDR() {
  throw new Error('Function not implemented.');
}
