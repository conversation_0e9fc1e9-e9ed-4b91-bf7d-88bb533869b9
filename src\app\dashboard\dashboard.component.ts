/* eslint-disable max-lines-per-function */
/* eslint-disable max-len */
import { Component, HostListener, OnInit } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { forkJoin } from 'rxjs';
import { DashboardService } from '../services/dashboard/dashboard.service';
import { ProjectService } from '../services/profile/project.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { DeliveryDetailsNewComponent } from '../delivery-requests/delivery-details/delivery-details-new/delivery-details-new.component';
import { CraneRequestDetailViewHeaderComponent } from '../crane-requests/crane-request-detail-view-header/crane-request-detail-view-header.component';
import { ProjectSettingsService } from '../services/project_settings/project-settings.service';
import { ProfileService } from '../services/profile/profile.service';
import { ConcreteDetailHeaderComponent } from '../concrete-request/concrete-detail-header/concrete-detail-header.component';
import { InspectionDetailsNewComponent } from '../inspection-request/inspection_details/inspection-details-new/inspection-details-new.component';
import { environment } from '../../environments/environment';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
})
export class DashboardComponent implements OnInit {
  public userData: any = [];

  public bsModalRef: BsModalRef;

  public dashData: any = {};

  public loader = true;

  public ProjectId: number;

  public modalRef: BsModalRef;

  public graphData: any = [];

  public craneGraphData: any = [];

  public concreteGraphData: any = [];

  public barChartOne: any = {};

  public barChartTwo: any = {};

  public barChartThree: any = {};

  public myAccount = false;

  public accountAdmin = false;

  public ParentCompanyId: number;

  public authUser: any = {};

  public parentCompanyList: any = [];

  public yearValue = 'currentyear';

  public upcomingList: any[];

  public upcomingListLoader = false;

  public filterYearWise: any = [];

  public filterYearWise1: any = [];

  public filterYearWise2: any = [];

  public filterYearWise4: any = [];

  public isImageExpanded = false;

  public logisticPlan: any;

  public logisticPlanExtension: any;

  public isPdfPlan = false;

  public fileUploaded = false;

  public formData: any = {};

  public roleId: number;

  public isAdmin: boolean;

  public isResponse = false;

  public scale = 1.0; // Initial scale

  public offsetX = 0; // Initial X offset

  public offsetY = 0; // Initial Y offset

  public isDragging = false;

  public startDragX = 0;

  public startDragY = 0;

  public maxScale = 3.0;

  public minScale = 1.0;

  public zoomLevel = 1;

  public arrayValues: any = [];

  public currentIndex: any = 0;

  public pageNo: any = 1;

  public selectedSitePlan: any;

  public showSearchbar = false;

  public search = null;

  public showSitePlan = true;

  public sitePlanLoader = false;

  public sitePlanUploadStatus: any;

  public inspectiponGraphData: any = [];

  public filterYearWise3: any = [];

  public constructor(
    public dashboardSerivce: DashboardService,
    public projectService: ProjectService,
    public deliveryService: DeliveryService,
    public projectSettingsService: ProjectSettingsService,
    public profileService: ProfileService,
    public router: Router,
    private readonly titleService: Title,
    private readonly modalService: BsModalService,
    private readonly toastr: ToastrService,
  ) {
    this.titleService.setTitle('Follo - Dashboard');

    // Add this subscription to listen for project selection events
    this.projectService.projectSelected.subscribe(() => {
      // Reset site plan expansion state when project changes
      this.isImageExpanded = false;
      this.scale = 1.0;
      this.offsetX = 0;
      this.offsetY = 0;
    });

    // Existing code...
    this.projectService.projectParent.subscribe((response1): void => {
      if (response1 !== undefined && response1 !== null && response1 !== '') {
        this.ProjectId = response1.ProjectId;
        this.ParentCompanyId = response1.ParentCompanyId;
        this.myAccount = false;
        this.loader = true;
        this.getMemberInfo();
        this.getDashboardData();
        this.getUpcomingList();
        this.getGraphDelivery();
        this.getLogisticPlan();

        // Reset site plan expansion state when project changes
        this.isImageExpanded = false;
        this.scale = 1.0;
        this.offsetX = 0;
        this.offsetY = 0;
      }
    });
    this.projectService.companyList.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.parentCompanyList = res;
      }
    });
    this.projectService.ParentCompanyId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ParentCompanyId = res;
        this.loader = true;
        this.getDashboardData();
        this.getUpcomingList();
        this.getGraphDelivery();
      }
    });
    this.projectService.clearProject.subscribe((res1): void => {
      const data = { ...res1 };
      if (data !== undefined && data !== null && data !== '') {
        if (data.status === true) {
          this.ProjectId = -1;
        }
      }
    });
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
        this.getDashboardData();
      }
    });
    this.projectService.isAccountAdmin.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.accountAdmin = res;
        if (!this.ProjectId || this.ProjectId === -1) {
          this.myAccount = true;
          if (this.ParentCompanyId && this.ParentCompanyId !== -1) {
            this.getDashboardData();
            this.getUpcomingList();
            this.getGraphDelivery();
          }
        }
      }
    });
    this.deliveryService.refresh.subscribe((upcomingList): void => {
      if (upcomingList !== undefined && upcomingList !== null && upcomingList !== '') {
        this.getUpcomingList();
        this.getGraphDelivery();
      }
    });
    this.deliveryService.refresh1.subscribe((upcomingList): void => {
      if (upcomingList !== undefined && upcomingList !== null && upcomingList !== '') {
        this.getUpcomingList();
        this.getGraphDelivery();
      }
    });
    this.deliveryService.inspectionUpdated.subscribe((upcomingList): void => {
      if (upcomingList !== undefined && upcomingList !== null && upcomingList !== '') {
        this.getUpcomingList();
        this.getGraphDelivery();
      }
    });
    this.deliveryService.inspectionUpdated1.subscribe((upcomingList): void => {
      if (upcomingList !== undefined && upcomingList !== null && upcomingList !== '') {
        this.getUpcomingList();
        this.getGraphDelivery();
      }
    });
    this.deliveryService.fetchData.subscribe((fetchData): void => {
      if (fetchData !== undefined && fetchData !== null && fetchData !== '') {
        this.getUpcomingList();
        this.getGraphDelivery();
      }
    });
    this.deliveryService.fetchData1.subscribe((fetchData): void => {
      if (fetchData !== undefined && fetchData !== null && fetchData !== '') {
        this.getUpcomingList();
        this.getGraphDelivery();
      }
    });
    this.projectService.siteplanUploaded.subscribe((res1): void => {
      this.getLogisticPlan();
    });
  }

  public ngOnInit(): void { /* */
  }

  @HostListener('mousedown', ['$event'])
  public onMouseDown(event: MouseEvent): void {
    if (this.scale > 1.0) {
      this.isDragging = true;
      this.startDragX = event.clientX - this.offsetX;
      this.startDragY = event.clientY - this.offsetY;
    }
  }

  @HostListener('mouseup', ['$event'])
  public onMouseUp(event: MouseEvent): void {
    this.isDragging = false;
  }

  public toggleImageSize(): void {
    this.isImageExpanded = !this.isImageExpanded;
  }

  public changeYear(yearValue: string): void {
    this.yearValue = yearValue;
    this.getGraphDelivery();
  }

  public redirect(data: any): void {
    if (!this.myAccount) {
      this.router.navigate([`/${data}`]);
    }
  }

  public getDashboardData(): void {
    const payload: any = {
      ParentCompanyId: this.ParentCompanyId,
      RoleId: this.authUser.RoleId,
    };
    if (!this.myAccount) {
      payload.ProjectId = this.ProjectId;
    }
    if (payload.RoleId && payload.ParentCompanyId && (this.myAccount || payload.ProjectId)) {
      this.dashboardSerivce.getDashboardData(payload).subscribe({
        next: (res): void => {
          this.dashData = res.data;
          this.loader = false;
        },
        error: (): void => {
          this.loader = false;
          this.toastr.error('Error fetching dashboard data');
        }
      });
    }
  }

  public getUpcomingList(): void {
    this.upcomingList = [];
    this.upcomingListLoader = true;
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    if (this.ProjectId && this.ParentCompanyId) {
      this.deliveryService.getUpcomingList(params).subscribe((res): void => {
        this.upcomingList = res?.data;
        this.upcomingListLoader = false;
      });
    }
  }

  public openCraneDetailView(data: {
    CraneRequestId: any;
    ProjectId: any;
    requestType: string;
    id: any;
    ConcreteRequestId: any;
  }): void {
    const payload = {
      id: data.CraneRequestId,
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    const initialState = {
      data: payload,
      title: 'Modal with component',
    };
    if (data.requestType === 'craneRequest') {
      this.bsModalRef = this.modalService.show(CraneRequestDetailViewHeaderComponent, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal',
        initialState,
      });
    }
    if (data.requestType === 'deliveryRequestWithCrane' || data.requestType === 'deliveryRequest') {
      delete payload.id;
      payload.id = data.id;
      this.bsModalRef = this.modalService.show(DeliveryDetailsNewComponent, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal',
        initialState,
      });
    }
    if (data.requestType === 'concreteRequest') {
      delete payload.id;
      payload.id = data.ConcreteRequestId;
      this.bsModalRef = this.modalService.show(ConcreteDetailHeaderComponent, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal',
        initialState,
      });
    }
    if (data.requestType === 'inspectionRequest' || data.requestType === 'inspectionRequestWithCrane') {
      delete payload.id;
      payload.id = data.id;
      this.bsModalRef = this.modalService.show(InspectionDetailsNewComponent, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal',
        initialState,
      });
    }
    this.bsModalRef.content.closeBtnName = 'Close';
  }

  public getGraphDelivery(): void {
    const params = {
      ParentCompanyId: this.ParentCompanyId,
    };
    let payload = {};
    if (!this.myAccount) {
      payload = {
        ProjectId: this.ProjectId,
      };
    }

    const monthList = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec',
    ];

    let fullYear = new Date().getFullYear();
    if (this.yearValue === 'nextyear') {
      fullYear += 1;
    } else if (this.yearValue === 'previousyear') {
      fullYear -= 1;
    }

    if (this.ProjectId && this.ParentCompanyId) {
      this.dashboardSerivce.getGraphDelivery(params, payload).subscribe((res): void => {
        this.graphData = res.data.count;
        const data: number[] = [];

        if (this.graphData?.length > 0) {
          monthList.forEach((element): void => {
            const filterYearWise = this.graphData.filter(
              (object): boolean => Number(object.year) === Number(fullYear),
            );
            const index = filterYearWise.findIndex(
              (item): boolean => item.shortmonth.toLowerCase() === element.toLowerCase(),
            );
            data.push(index === -1 ? 0 : filterYearWise[index].total);
          });
        }

        this.processGraphData(fullYear, monthList, params, payload, data);
      });
    }
  }

  public processGraphData(
    fullYear: number,
    monthList: string[],
    params: any,
    payload: any,
    data: number[]
  ): void {
    const extractMonthlyData = (graphData: any[], year: number, months: string[]): number[] => {
      const filtered = graphData.filter((item) => Number(item.year) === year);
      return months.map((month) => {
        const entry = filtered.find((item) => item.shortmonth.toLowerCase() === month.toLowerCase());
        return entry ? entry.total : 0;
      });
    };

    forkJoin({
      crane: this.dashboardSerivce.getCraneRequestGraphData(params, payload),
      concrete: this.dashboardSerivce.getConcreteRequestGraphData(params, payload),
      inspection: this.dashboardSerivce.getInspectionRequestGraphData(params, payload)
    }).subscribe(({ crane, concrete, inspection }) => {
      const craneData = crane?.data?.count || [];
      const concreteData = concrete?.data?.count || [];
      const inspectionData = inspection?.data?.count || [];

      const data1 = extractMonthlyData(craneData, fullYear, monthList);
      const data2 = extractMonthlyData(concreteData, fullYear, monthList);
      const data3 = extractMonthlyData(inspectionData, fullYear, monthList);

      this.barChartOne = {
        title: '',
        categories: monthList,
        data,
        data1,
        data2,
        data3,
      };
    });
  }


  public getLogisticPlan(): void {
    const params = {
      ProjectId: this.ProjectId,
    };
    if (this.ProjectId && this.ParentCompanyId) {
      this.projectSettingsService.getProjectSettings(params).subscribe((res): void => {
        const responseData = res?.data?.projectSettings;
        this.fileUploaded = responseData?.isPdfUploaded;
        this.logisticPlan = responseData?.projectLogisticPlanUrl;

        if (this.fileUploaded) {
          this.logisticPlan = responseData?.projectLogisticPlanUrl;
          this.logisticPlanExtension = responseData?.fileExtension;
          this.sitePlanUploadStatus = responseData?.sitePlanStatus;

          if (['png', 'jpg', 'jpeg'].includes(this.logisticPlanExtension)) {
            this.isPdfPlan = false;
            this.selectedSitePlan = this.logisticPlan;
          } else if (this.logisticPlanExtension === 'pdf') {
            this.isPdfPlan = true;
            this.arrayValues = responseData?.pdfToImageLinks;
            this.showSitePlan = this.arrayValues.length > 0;
            this.selectedSitePlan = this.arrayValues[0];
          }
        }
      });
    }
  }

  public downloadAndReuploadFile(uri: string, originalFileName: string): void {
    // Step 1: Download the file from the provided URI (proxy server)
    fetch(uri)
      .then(response => {
        if (!response.ok) {
          throw new Error('File download failed');
        }
        return response.blob();
      })
      .then(blob => {
        // Step 2: Rename the downloaded file to the original file name
        const file = new File([blob], originalFileName, { type: blob.type });

        // Step 3: Simulate file selection and call onPlanSelected with the renamed file
        this.onPlanSelected([file]);
      })
      .catch(error => {
        console.error('Error downloading and reuploading file:', error);
        this.toastr.error('File download or re-upload failed.');
      });
  }

  public downloadProxyFile(logisticPlan: string, fileName: string): void {
    const fileUrl = logisticPlan;  // S3 file URL
    const downloadUrl = `${environment.apiBaseUrl}project_settings/proxy_download?fileUrl=${encodeURIComponent(fileUrl)}`;

    // Call the function to download and re-upload the file
    this.downloadAndReuploadFile(downloadUrl, fileName);
  }

  public onPlanSelected(files: { name: string }[]): void {
    const extension = files[0].name.split('.').pop().toLowerCase();  // Get file extension

    if (['pdf', 'png', 'jpg', 'jpeg'].includes(extension)) {
      this.formData = new FormData();
      this.formData.append('projectPlan', files[0], files[0].name);  // Append the renamed file with the original name

      const data = {
        ProjectId: this.ProjectId,
      };

      // Call the service to upload the project logistic plan
      this.projectService.uploadProjectLogisticPlanUrl(data, this.formData).subscribe({
        next: (res): void => {
          if (res?.data?.fileUrl) {
            this.toastr.success(res.message);
            this.logisticPlan.nativeElement.value = null;
          } else {
            this.logisticPlan.nativeElement.value = null;
          }
        },
        error: (error): void => {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        },
      });
    } else {
      this.toastr.error('Only *pdf, *jpg, *png, *jpeg files are allowed');
    }
  }



  public downloadFile(): void {
    const uri = this.logisticPlan;
    const downloadLink = document.createElement('a');
    downloadLink.href = uri;
    downloadLink.setAttribute('download', '');
    downloadLink.target = 'downloadIframe';
    downloadLink.click();
  }

  public async copyLink(planUrl: string): Promise<void> {
    try {
      await navigator.clipboard.writeText(planUrl);
      this.toastr.success('Site plan link copied successfully');
    } catch (err) {
      this.toastr.error('Failed to copy the site plan link');
      console.error('Copy failed', err);
    }
  }

  public zoomIn(): void {
    if (this.scale < this.maxScale) {
      this.scale += 0.1;
      this.updateImageScale();
    }
  }

  public zoomOut(): void {
    if (this.scale > this.minScale) {
      this.scale -= 0.1;
      this.updateImageScale();
    }
  }

  public zoomIn1(): void {
    if (this.scale < this.maxScale) {
      this.scale += 0.1;
      this.updateImageScale2();
    }
  }

  public zoomOut2(): void {
    if (this.scale > this.minScale) {
      this.scale -= 0.1;
      this.updateImageScale2();
    }
  }

  public updateImageScale(): void {
    const imageElement = document.querySelector('.image-container img') as HTMLImageElement; // NOSONAR
    if (imageElement) {
      imageElement.style.transform = `scale(${this.scale})`;
    }
  }

  public updateImageScale2(): void {
    const imageElement = document.querySelector('.image-container2 img') as HTMLImageElement; // NOSONAR
    if (imageElement) {
      imageElement.style.transform = `scale(${this.scale})`;
    }
  }

  public zoomInPdf() {
    this.zoomLevel += 0.1; // Increase the zoom level
  }

  // Function to zoom out
  public zoomOutPdf(): void {
    if (this.zoomLevel > 0.2) {
      // Ensure zoom level doesn't go below 20%
      this.zoomLevel -= 0.1; // Decrease the zoom level
    }
  }

  public navigateProjectSettings(): void {
    this.router.navigate(['/project-settings']);
  }

  public getMemberInfo(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.isResponse = false;
    this.profileService.getOverView(param).subscribe((response: any): void => {
      if (response) {
        this.isResponse = true;
        this.userData = response.data;
        this.roleId = this.userData.RoleId;
        if (this.userData.RoleId !== 2 && this.userData.RoleId !== 1) {
          this.isAdmin = false;
        } else {
          this.isAdmin = true;
        }
      }
    });
  }

  public getNextValue(): void {
    this.sitePlanLoader = true;
    if (this.currentIndex >= this.arrayValues.length - 1) {
      this.currentIndex = 0;
      this.pageNo = 1;
    } else {
      this.currentIndex += 1;
      this.pageNo = this.currentIndex + 1;
    }
    this.selectedSitePlan = this.arrayValues[this.currentIndex];
    this.preloadImage(this.selectedSitePlan)
      .then((): void => {
        this.sitePlanLoader = false;
      })
      .catch((error): void => {
        this.sitePlanLoader = false;
      });
  }

  public preloadImage(url: string): Promise<void> {
    return new Promise<void>((resolve, reject): void => {
      const img = new Image();
      img.onload = (): void => {
        resolve();
      };
      img.onerror = (error): void => {
        reject(new Error('Image failed to load'));
      };
      img.src = url;
    });
  }

  public getPreviousValue(): void {
    this.sitePlanLoader = true;
    if (this.currentIndex <= 0) {
      this.currentIndex = this.arrayValues.length - 1;
      this.pageNo = this.currentIndex + 1;
    } else {
      this.currentIndex -= 1;
      this.pageNo = this.currentIndex + 1;
    }
    this.selectedSitePlan = this.arrayValues[this.currentIndex];
    this.preloadImage(this.selectedSitePlan)
      .then((): void => {
        this.sitePlanLoader = false;
      })
      .catch((error): void => {
        this.sitePlanLoader = false;
      });
  }

  public getSitePlanAtIndex(data: any): void {
    this.search = data;
    if (data.length > 0) {
      this.showSearchbar = true;
      if (data > 0 && data <= this.arrayValues.length) {
        this.currentIndex = data - 1;
        this.pageNo = this.currentIndex + 1;
        this.sitePlanLoader = true;
      }
    } else {
      this.sitePlanLoader = false;
      this.showSearchbar = false;
    }
    this.selectedSitePlan = this.arrayValues[this.currentIndex];
    this.preloadImage(this.selectedSitePlan)
      .then((): void => {
        this.sitePlanLoader = false;
      })
      .catch((error): void => {
        this.sitePlanLoader = false;
      });
  }

  public onlyNumbers(event: KeyboardEvent) {
    const charCode = event.which ? event.which : event.keyCode; // NOSONAR
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  onFocusOut(event: Event): void {
    this.pageNo = this.currentIndex + 1;
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = null;
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'redirect':
          this.redirect(data);
          break;
        case 'previous':
          this.getPreviousValue();
          break;
        case 'next':
          this.getNextValue();
          break;
        case 'toggle':
          this.toggleImageSize();
          break;
        case 'download':
          this.downloadFile();
          break;
        case 'copy':
          this.copyLink(data);
          break;
        case 'zoomin':
          this.zoomIn();
          break;
        case 'zoomout':
          this.zoomOut();
          break;
        case 'zoomin1':
          this.zoomIn1();
          break;
        case 'zoomout2':
          this.zoomOut2();
          break;
        default:
          break;
      }
    }
  }
}
