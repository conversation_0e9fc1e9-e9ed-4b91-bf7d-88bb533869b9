/* eslint-disable no-useless-escape */
/* eslint-disable max-lines-per-function */
import {
  Component,
  OnInit,
  ViewChild,
  TemplateRef,
  ElementRef,
  AfterViewInit,
  OnDestroy,
} from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Title } from '@angular/platform-browser';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { AuthService } from '../services/auth/auth.service';
import { countryCodes } from '../services/countryCodes';
import { countryNames } from '../services/countryNames';
import { ProjectService } from '../services/profile/project.service';
import { CustomValidators } from '../custom-validators';

declare let google: any;
@Component({
  selector: 'app-home-page',
  templateUrl: './home-page.component.html',
})
export class HomePageComponent implements OnInit, AfterViewInit, OnDestroy {

  public stepOne = false;

  public StepTwo = false;

  public passwordType = 'password';

  public confirmPasswordType = 'password';

  public StepThree = false;

  public basicDetailsForm: UntypedFormGroup;

  public basicSubmitted = false;

  public projectSubmitted = false;

  public companyDetailsForm: UntypedFormGroup;

  public homePageProjectDetailsForm: UntypedFormGroup;

  public companySubmitted = false;

  public formSubmitted = false;

  public loginToken = false;

  public urlValue = '';

  public countryCode = [];

  public ParentCompanyId;

  public memberId;

  public RoleId;

  public CompanyId;

  public ProjectId;

  public memberEmail;

  public loggedIn = false;

  public submitted = false;

  public togglePassword = false;

  public confirmTogglePassword = false;

  public phoneMask = '(*************';

  public companyFormSubmitted = false;

  public latitude = 38.897957;

  public longitude = -77.03656;

  public address: any;

  public location: string;

  public geoCoder: { geocode: (arg0: { location: { lat: any; lng: any } }, arg1: (results: any, status: any) => void) => void };

  public modalRef: BsModalRef;

  public zoom: number;

  public domainName: string;

  public _map: any;

  public alreadyExistingDomain: boolean;

  public modalRef1: BsModalRef;

  public defaultValue;

  public selectedValue: any;

  public getSelectedTimeZone;

  @ViewChild('threeSimpleSteps') public popupElement: TemplateRef<any>;

  public removeAlreadyInvitedMember: boolean;

  public requestInvitedLink: boolean;

  public passwordError = false;

  public dropdownSettings: IDropdownSettings;

  public constructor(
    private readonly modalService: BsModalService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly toastr: ToastrService,
    public projectService: ProjectService,
    private readonly authService: AuthService,
    private readonly route: ActivatedRoute,
    private readonly el: ElementRef,
    private readonly titleService: Title,
    public router: Router,
  ) {
    this.titleService.setTitle('Follo - Home');
    this.dropdownSettings = {
      singleSelection: true,
      idField: 'name',
      textField: 'countryDialCode',
      allowSearchFilter: false,
      closeDropDownOnSelection: true,
    };
    if (this.authService.loggeduserIn()) {
      this.loginToken = true;
    }

    this.getCountryCode();
  }

  // Get Current user's Location Coordinates
  public setCurrentLocation(): void {
    this.projectForm();
    this.geoCoder = new google.maps.Geocoder();
    if (this.homePageProjectDetailsForm?.get('projectLocation').value
    ) {
      this.mapLoader(
        this.homePageProjectDetailsForm.get('projectLocationLatitude').value,
        this.homePageProjectDetailsForm.get('projectLocationLongitude').value,
      );
    } else if ('geolocation' in navigator) {
      navigator.permissions
        .query({
          name: 'geolocation', // NOSONAR
        })
        .then((permission): any => {
          if (permission.state === 'granted') {
            navigator.geolocation.getCurrentPosition((position): void => { // NOSONAR
              if (
                position?.coords?.latitude
                && position?.coords?.longitude
              ) {
                this.latitude = position.coords.latitude;
                this.longitude = position.coords.longitude;
                this.mapLoader(this.latitude, this.longitude);
              }
            });
          } else {
            this.mapLoader(this.latitude, this.longitude);
          }
        });
    }
  }

  public handleProjectAddress(address: { geometry: { location: { lat: () => number; lng: () => number } }; formatted_address: any }): void {
    this.getAddress(
      address.geometry.location.lat(),
      address.geometry.location.lng(),
      address.formatted_address,
    );
    this.latitude = address.geometry.location.lat();
    this.longitude = address.geometry.location.lng();
  }

  public numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public passwordValid(value): any {
    const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[#?!@$%^&*-])[A-Za-z\d#?!@$%^&*-]{8,30}$/;
    if (regex.exec(value.target.value) === null) {
      this.passwordError = true;
    } else {
      this.passwordError = false;
    }
  }

  public alphaNum(event): boolean {
    const key = event.which ? event.which : event.keyCode;
    const firstCondition = key >= 65 && key <= 90;
    const secondCondition = key >= 97 && key <= 128;
    const thirdCondition = key === 32 || (key > 31 && (key < 48 || key > 57));
    if (
      key > 31
      && (key < 48 || key > 57)
      && !firstCondition
      && !secondCondition
      && !thirdCondition
    ) {
      return false;
    }

    return true;
  }

  public ngOnDestroy(): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  public mapLoader(latitude, longitude): void {
    this.geoCoder = new google.maps.Geocoder();
    this.setUserCurrentLocation(latitude, longitude);
  }

  // Get Current Location Coordinates
  public setUserCurrentLocation(latitude, longitude): void {
    const lat = parseFloat(latitude);
    const long = parseFloat(longitude);
    if (navigator.geolocation) {
      this.latitude = lat;
      this.longitude = long;
      this.zoom = 18;
      this.getAddress(this.latitude, this.longitude, null);
    }
  }

  public getAddress(latitude, longitude, address): void {
    if (address) {
      this.address = address;
      this.homePageProjectDetailsForm.get('projectLocation').setValue(this.address);
      this.homePageProjectDetailsForm.get('projectLocationLatitude').setValue(latitude);
      this.homePageProjectDetailsForm.get('projectLocationLongitude').setValue(longitude);
    } else {
      this.geoCoder.geocode(
        { location: { lat: latitude, lng: longitude } },
        (results, status): void => {
          if (status === 'OK') {
            if (results[0]) {
              this.address = results[0].formatted_address;
              this.homePageProjectDetailsForm.get('projectLocation').setValue(this.address);
              this.homePageProjectDetailsForm.get('projectLocationLatitude').setValue(latitude);
              this.homePageProjectDetailsForm.get('projectLocationLongitude').setValue(longitude);
            }
          }
        },
      );
    }
  }

  public alphaOnly(event): boolean {
    const key = event.keyCode;
    return (
      (key >= 65 && key <= 90)
      || (key >= 97 && key <= 128)
      || key === 8
      || key === 32
      || (key >= 48 && key <= 57)
      || (key > 31 && (key < 48 || key > 57))
    );
  }

  public handleProjectAddressChange(address): void {
    this.location = address.formatted_address;
    this.latitude = address.geometry.location.lat();
    this.longitude = address.geometry.location.lng();
  }

  // eslint-disable-next-line max-lines-per-function
  public parseAddressComponents(address): any {
    const components = {
      homePlace: '',
      homeStreetNumber: '',
      homeLocalityValue: '',
      homeSubLocality: '',
      homeCity: '',
      homeState: '',
      homeCountry: '',
      homeZipCode: '',
      homeZipCodeSuffix: '',
      homeSubLocalityLevel2: ''
    };

    const homeResultedAddress = address?.address_components || [];
    homeResultedAddress.forEach((value): void => {
      const types = value.types || [];
      types.forEach((typeValue): void => {
        switch (typeValue) {
          case 'sublocality_level_1':
            components.homeSubLocality = value?.long_name;
            break;
          case 'sublocality_level_2':
            components.homeSubLocalityLevel2 = value?.long_name;
            break;
          case 'street_number':
            components.homeStreetNumber = value?.long_name;
            break;
          case 'route':
            components.homePlace = value?.long_name;
            break;
          case 'administrative_area_level_2':
            components.homeCity = value?.long_name;
            break;
          case 'locality':
            components.homeLocalityValue = value?.long_name;
            break;
          case 'administrative_area_level_1':
            components.homeState = value?.long_name;
            break;
          case 'country':
            components.homeCountry = value?.long_name;
            break;
          case 'postal_code':
            components.homeZipCode = value?.long_name;
            break;
          case 'postal_code_suffix':
            components.homeZipCodeSuffix = value?.long_name;
            break;
          default:
            break;
        }
      });
    });

    return components;
  }

  public handleHomePageCompanyAddressChange(address): void {
    const {
      homePlace,
      homeStreetNumber,
      homeLocalityValue,
      homeSubLocality,
      homeCity,
      homeState,
      homeCountry,
      homeZipCode,
      homeZipCodeSuffix,
      homeSubLocalityLevel2,
    } = this.parseAddressComponents(address);

    let assignPlace = '';
    if (homeStreetNumber) assignPlace += `${homeStreetNumber} `;
    if (homePlace) assignPlace += `${homePlace} `;
    if (homeSubLocalityLevel2) assignPlace += `${homeSubLocalityLevel2} `;
    if (homeSubLocality) assignPlace += `${homeSubLocality} `;

    assignPlace = assignPlace.trim();

    this.companyDetailsForm.get('address').setValue(assignPlace || homeLocalityValue);
    this.companyDetailsForm.get('country').setValue(homeCountry);
    this.companyDetailsForm.get('state').setValue(homeState);
    this.companyDetailsForm.get('city').setValue(homeLocalityValue || homeCity);
    this.companyDetailsForm.get('zipCode').setValue(homeZipCode || homeZipCodeSuffix);
  }


  public passwordToggle(): void {
    this.togglePassword = !this.togglePassword;
  }

  public confirmPasswordToggle(): void {
    this.confirmTogglePassword = !this.confirmTogglePassword;
  }

  public handleDownKeydown(event: KeyboardEvent, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'password':
          this.passwordToggle();
          break;
        case 'cpassword':
          this.confirmPasswordToggle();
          break;
        default:
          break;
      }
    }
  }

  public modalClose(): void {
    this.alreadyExistingDomain = false;
    this.modalRef.hide();
    localStorage.removeItem('basic');
    localStorage.removeItem('company');
    localStorage.removeItem('project');
    this.basicDetailsForm.reset();
    this.companyDetailsForm.reset();
    this.homePageProjectDetailsForm.reset();
    this.basicDetailsForm.get('phoneCode').setValue('+1');
    this.changeMask(this.basicDetailsForm.value.phoneCode);
    const url = this.router.url.split('/')[1];
    if (url === 'register-project') {
      this.router.navigate(['/home']);
    }
  }

  public ngOnInit(): void {
    this.setCurrentLocation();
    if (this.projectService.checkCurrentDomain()) {
      this.router.navigate(['/login']);
    }
    const url = this.router.url.split('/');
    const memberId = url[2];
    const ParentCompanyId = url[3];
    const email = url[4];
    const domainName = url[5];
    this.domainName = domainName;
    this.memberId = memberId;
    this.ParentCompanyId = ParentCompanyId;
    this.memberEmail = email;
    this.basicPassForm();
    this.companyForm();
    if (this.memberId && ParentCompanyId) {
      const data = {
        memberId: this.memberId,
        ParentCompanyId,
        email: this.memberEmail,
        domainName: this.domainName,
        requestType: 0, // web - 0,app -1
      };
      this.authService.getUserDetail(data).subscribe({
        next: (response: any): void => {
          if (response) {
            const { memberDetail, userDetail } = response;
            const company = memberDetail.Company;
            if (memberDetail && memberDetail?.isDeleted === true) {
              this.router.navigate(['/login']);
              this.toastr.error('You were removed from the project!', 'OOPS!');
            } else if (memberDetail && memberDetail?.status === 'completed') {
              this.router.navigate(['/login']);
              this.toastr.info('You created username already', 'Please log in');
            } else if (memberDetail) {
              this.memberEmail = userDetail.email;
              this.RoleId = memberDetail.RoleId;
              this.ProjectId = memberDetail.ProjectId;
              this.basicDetailsForm.get('email').setValue(this.memberEmail);
              this.companyDetailsForm.get('companyName').setValue(company.companyName);
              this.companyDetailsForm.get('address').setValue(company.address);
              this.companyDetailsForm.get('secondAddress').setValue(company?.secondAddress);
              this.companyDetailsForm.get('city').setValue(company.city);
              this.companyDetailsForm.get('state').setValue(company.state);
              this.companyDetailsForm.get('country').setValue(company.country);
              this.companyDetailsForm.get('zipCode').setValue(company.zipCode);
              this.companyDetailsForm.get('website').setValue(company.website);
              this.companyDetailsForm.get('companyId').setValue(company.id);
              document.getElementById('myCheck').click();
            } else {
              this.router.navigate(['/home']);
            }
          }
        },
        error: (updateMemberErr): void => {
          this.submitted = false;
          this.formSubmitted = false;
          this.companySubmitted = false;
          if (updateMemberErr.message?.statusCode === 400) {
            this.showError(updateMemberErr);
          } else if (!updateMemberErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(updateMemberErr.message, 'OOPS!');
          }
          this.router.navigate(['/login']);
        },
      });
    }
    this.companyForm();
    this.projectForm();
  }

  public timeZoneSelected(id): void {
    const getSelected = this.countryCode.find((obj: any): any => +obj.id === +id);
    this.selectedValue = getSelected;
  }

  public ngAfterViewInit(): void {
    const url = this.router.url.split('/')[1];
    if (url === 'register-project') {
      if (!this.authService.loggeduserIn()) {
        const companyDetails = localStorage.getItem('company');
        const basicDetails = localStorage.getItem('basic');
        const companyCondition = companyDetails === undefined || companyDetails === null;
        const basicCondition = basicDetails === undefined || basicDetails === null;
        if (!basicCondition && !companyCondition) {
          this.urlValue = url;
          this.getStarted(this.popupElement);
        }
      }
    }
  }

  public getStarted(template): void {
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'home-popup' };
    if (this.loginToken) {
      this.router.navigate(['/dashboard']);
    } else if (this.urlValue === 'register-project') {
      this.stepOne = false;
      this.StepTwo = false;
      this.StepThree = true;
      this.modalRef = this.modalService.show(template, data);
    } else {
      this.stepOne = true;
      this.StepTwo = false;
      this.StepThree = false;
      let data1 = {};
      data1 = { backdrop: 'static', keyboard: false, class: 'home-popup' };
      this.modalRef = this.modalService.show(template, data1);
    }
  }

  public memberDetailsUpdate(): void {
    this.submitted = true;
    this.loggedIn = this.authService.loggeduserIn();
    if (!this.loggedIn) {
      const companyDetails = this.companyDetailsForm.value;
      const basicDetails = this.basicDetailsForm.value;
      const companyCondition = companyDetails === undefined || companyDetails === null;
      const basicCondition = basicDetails === undefined || basicDetails === null;

      if (basicCondition) {
        this.toastr.error('Please Fill the Basic Details', 'OOPS');
        this.router.navigate(['/home']);
      } else if (companyCondition) {
        this.toastr.error('Please Fill the Company Details', 'OOPS');
        this.router.navigate(['/home']);
      }
      const payload: any = {
        memberDetail: {
          firstName: companyDetails.fullName,
          lastName: companyDetails.lastName,
          ParentCompanyId: this.ParentCompanyId,
          password: basicDetails.password,
          email: basicDetails.email,
          phoneNumber: basicDetails.phoneNumber,
          phoneCode: basicDetails.phoneCode,
          id: this.memberId,
          ProjectId: this.ProjectId,
          RoleId: this.RoleId,
          status: 'completed',
          action: 'onboarding',
        },
      };
      payload.companyDetail = companyDetails;
      payload.domainName = this.domainName;

      this.projectService.updateMember(payload).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.submitted = false;
            this.companySubmitted = false;
            this.companyFormSubmitted = false;

            localStorage.removeItem('basic');
            localStorage.removeItem('company');
            localStorage.removeItem('project');
            localStorage.removeItem('interval');
            localStorage.removeItem('planid');
            this.router.navigate(['/login']);
          }
        },
        error: (updateMemberErr): void => {
          this.submitted = false;
          this.formSubmitted = false;
          this.companySubmitted = false;
          if (updateMemberErr.message?.statusCode === 400) {
            this.showError(updateMemberErr);
          } else if (!updateMemberErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(updateMemberErr.message, 'OOPS!');
          }
        },
      });
    }
  }

  public userResponse(action): void {
    if (action === 'yes') {
      this.removeAlreadyInvitedMemberLink();
    }
    if (action === 'no') {
      this.sendAlreadyInvitedLink();
    }
  }

  public removeAlreadyInvitedMemberLink(): void {
    this.removeAlreadyInvitedMember = true;
    const payload = {
      email: this.basicDetailsForm.value.email,
    };
    this.authService.removeAlreadyInvitedMember(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success(response.message);
          this.removeAlreadyInvitedMember = false;
          this.modalRef1.hide();
        }
      },
      error: (removeAlreadyInvitedMemberError): void => {
        this.removeAlreadyInvitedMember = false;
        if (removeAlreadyInvitedMemberError.message?.statusCode === 400) {
          this.showError(removeAlreadyInvitedMemberError);
        } else if (!removeAlreadyInvitedMemberError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(removeAlreadyInvitedMemberError.message, 'OOPS!');
        }
      },
    });
  }

  public sendAlreadyInvitedLink(): void {
    this.requestInvitedLink = true;
    const payload = {
      email: this.basicDetailsForm.value.email,
    };
    this.authService.requestInvitedLink(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success(response.message);
          this.requestInvitedLink = false;
          localStorage.removeItem('basic');
          this.basicDetailsForm.reset();
          this.basicDetailsForm.get('phoneCode').setValue('+1');
          this.modalRef1.hide();
          this.modalRef.hide();
        }
      },
      error: (sendAlreadyInvitedLinkError): void => {
        this.requestInvitedLink = false;
        if (sendAlreadyInvitedLinkError.message?.statusCode === 400) {
          this.showError(sendAlreadyInvitedLinkError);
        } else if (!sendAlreadyInvitedLinkError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(sendAlreadyInvitedLinkError.message, 'OOPS!');
        }
      },
    });
  }

  // eslint-disable-next-line max-lines-per-function
  public onSubmit(inputType, template: TemplateRef<any>): void {
    this.toastr.clear();
    if (inputType === 'basic') {
      this.alreadyExistingDomain = false;
      this.basicSubmitted = true;
      this.formSubmitted = true;
      if (this.basicDetailsForm.valid) {
        const formValue = this.basicDetailsForm.value;
        formValue.email = formValue.email.trim();
        if (!this.ParentCompanyId) {
          delete formValue.password;
          delete formValue.confirmPassword;
          this.authService.existEmail(formValue).subscribe({
            next: (response: any): void => {
              if (response) {
                localStorage.setItem('basic', JSON.stringify(this.basicDetailsForm.value));
                this.OpenStepTwo(response);
                this.basicSubmitted = false;
                this.formSubmitted = false;
              }
            },
            error: (existEmailErr): void => {
              this.hanldeSubmitError(existEmailErr, template);
            },
          });
        } else {
          localStorage.setItem('basic', JSON.stringify(this.basicDetailsForm.value));
          this.OpenStepTwo(null);
          this.basicSubmitted = false;
          this.formSubmitted = false;
        }
      } else {
        this.formSubmitted = false;
      }
    } else if (inputType === 'company') {
      this.checkProjectType();
    } else if (inputType === 'project') {
      this.checkProjectType();
    }
  }

  public checkProjectType() {
    const { projectName } = this.homePageProjectDetailsForm.value;
    if (/^\d+$/.test(projectName)) {
      this.toastr.error('Project cannot only be numbers.!');
      return;
    }
    const regex = /[a-zA-Z]/;
    if (!regex.test(projectName)) {
      this.toastr.error('Please enter valid Project Name.!');
      return;
    }
    this.projectSubmitted = true;
    if (this.homePageProjectDetailsForm.valid) {
      this.modalRef.hide();
      localStorage.setItem('project', JSON.stringify(this.homePageProjectDetailsForm.value));
      this.router.navigate(['/plans']);
    }
  }

  public checkCompanyType() {
    this.companySubmitted = true;
    this.companyFormSubmitted = true;
    if (this.companyDetailsForm.valid) {
      this.companyDetailsForm.value.isParent = true;
      localStorage.setItem('company', JSON.stringify(this.companyDetailsForm.value));
      if (this.ParentCompanyId) {
        this.memberDetailsUpdate();
        this.companySubmitted = false;
      } else {
        this.OpenStepThree();
        this.companySubmitted = false;
        this.companyFormSubmitted = false;
      }
    } else {
      this.companyFormSubmitted = false;
    }
  }

  public hanldeSubmitError(existEmailErr, template: TemplateRef<any>) {
    this.basicSubmitted = false;
    this.formSubmitted = false;
    if (existEmailErr?.status === 409) {
      this.toastr.error(existEmailErr.message);
      let data = {};
      data = {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      };
      this.modalRef1 = this.modalService.show(template, data);
    } else if (existEmailErr.message?.statusCode === 400) {
      this.showError(existEmailErr);
    } else if (!existEmailErr.message) {
      this.toastr.error('Try again later.!', 'Something went wrong.');
    } else {
      this.toastr.error(existEmailErr.message, 'OOPS!');
    }
  }

  public OpenStepTwo(response): void {
    if (response?.company && Object.keys(response.company).length > 0) {
      this.alreadyExistingDomain = true;
      this.companyDetailsForm
        .get('companyName')
        .setValue(response?.company?.Company[0].companyName);
      this.companyDetailsForm.get('companyId').setValue(response?.company?.Company[0].id);
      this.companyDetailsForm.get('address').setValue(response?.company?.Company[0].address);
      this.companyDetailsForm.get('city').setValue(response?.company?.Company[0].city);
      this.companyDetailsForm.get('state').setValue(response?.company?.Company[0].state);
      this.companyDetailsForm.get('country').setValue(response?.company?.Company[0].country);
      this.companyDetailsForm.get('zipCode').setValue(response?.company?.Company[0].zipCode);
    } else if (!this.ParentCompanyId) {
      this.companyDetailsForm.reset();
    }
    this.stepOne = false;
    this.StepTwo = true;
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }

  public changeMask(value): void {
    this.phoneMask = this.authService.checkDialCode(value);
  }

  public getCountryCode(): void {
    Object.keys(countryCodes).forEach((key): void => {
      const countryName = countryNames[key];
      this.countryCode.push({ countryDialCode: key, name: countryName });
    });
    this.countryCode.sort((a, b): number => (a.name > b.name ? 1 : -1));
  }

  public companyForm(): void {
    const reg = '(https?:\\/\\/(?:www\\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\\.[^\\s]{2,}|www\\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\\.[^\\s]{2,}|https?:\\/\\/(?:www\\.|(?!www))[a-zA-Z0-9]+\\.[^\\s]{2,}|www\\.[a-zA-Z0-9]+\\.[^\\s]{2,})';
    if (this.memberId !== undefined) {
      this.companyDetailsForm = this.formBuilder.group({
        fullName: ['', Validators.compose([Validators.required, Validators.minLength(3)])],
        lastName: ['', Validators.compose([Validators.required, Validators.minLength(3)])],
        companyId: [''],
        companyName: ['', Validators.compose([Validators.required, Validators.minLength(3)])],
        address: [''],
        secondAddress: [''],
        country: [''],
        state: [''],
        city: [''],
        website: [''],
        zipCode: [''],
      });
    }
    if (this.memberId === undefined) {
      this.companyDetailsForm = this.formBuilder.group({
        fullName: ['', Validators.compose([Validators.required, Validators.minLength(3)])],
        lastName: ['', Validators.compose([Validators.required, Validators.minLength(3)])],
        companyId: [''],
        companyName: ['', Validators.compose([Validators.required, Validators.minLength(3)])],
        address: [''],
        secondAddress: [''],
        country: [''],
        state: [''],
        city: [''],
        website: [
          '',
          Validators.compose([
            Validators.pattern(reg),
          ]),
        ],
        zipCode: [''],
      });
    }
    const companyDetails = localStorage.getItem('company');
    const companyInfo = JSON.parse(companyDetails);
    if (companyInfo !== undefined && companyInfo !== null) {
      this.companyDetailsForm.get('fullName').setValue(companyInfo.fullName);
      this.companyDetailsForm.get('lastName').setValue(companyInfo.lastName);
      this.companyDetailsForm.get('companyName').setValue(companyInfo.companyName);
    }
    if (this.memberId !== undefined && companyInfo !== undefined && companyInfo !== null) {
      this.companyDetailsForm.get('address').setValue(companyInfo.address);
      this.companyDetailsForm.get('secondAddress').setValue(companyInfo.secondAddress);
      this.companyDetailsForm.get('country').setValue(companyInfo.country);
      this.companyDetailsForm.get('state').setValue(companyInfo.state);
      this.companyDetailsForm.get('city').setValue(companyInfo.city);
      this.companyDetailsForm.get('website').setValue(companyInfo.website);
      this.companyDetailsForm.get('zipCode').setValue(companyInfo.zipCode);
    }
  }

  public basicPassForm(): void {
    const nonWhitespaceRegExp = /\S/;
    if (this.memberId === undefined) {
      this.basicDetailsForm = this.formBuilder.group({
        email: [
          '',
          Validators.compose([
            Validators.required,
            Validators.pattern("^[a-zA-Z0-9._%+'-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$"),
          ]),
        ],
        phoneNumber: ['', Validators.compose([Validators.required])],
        phoneCode: ['', Validators.compose([Validators.required])],
        password: [''],
        confirmPassword: [''],
      });
    }
    if (this.memberId !== undefined) {
      this.basicDetailsForm = this.formBuilder.group( // NOSONAR
        {
          email: [
            '',
            Validators.compose([
              Validators.required,
              Validators.pattern("^[a-zA-Z0-9._%+'-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$"),
            ]),
          ],
          phoneNumber: ['', Validators.compose([Validators.required])],
          phoneCode: ['', Validators.compose([Validators.required])],
          password: [
            null,
            Validators.compose([
              Validators.required,
              Validators.pattern(nonWhitespaceRegExp),
              // check whether the entered password has a number
              CustomValidators.patternValidator(/\d/, {
                hasNumber: true,
              }),
              // check whether the entered password has upper case letter
              CustomValidators.patternValidator(/[A-Z]/, {
                hasCapitalCase: true,
              }),
              // check whether the entered password has a lower case letter
              CustomValidators.patternValidator(/[a-z]/, {
                hasSmallCase: true,
              }),
              // check whether the entered password has a special character
              CustomValidators.patternValidator(/[ !@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/, {
                hasSpecialCharacters: true,
              }),
              Validators.minLength(8),
            ]),
          ],
          confirmPassword: [
            '',
            Validators.compose([Validators.required, Validators.pattern(nonWhitespaceRegExp)]),
          ],
        },
        {
          validator: this.MustMatch('password', 'confirmPassword'),
        },
      );
    }
    this.setBasicDetailsForm();
  }

  public setBasicDetailsForm(): void {
    const basicDetails = localStorage.getItem('basic');
    const basicInfo = JSON.parse(basicDetails);
    if (basicInfo !== undefined && basicInfo !== null) {
      this.basicDetailsForm.get('email').setValue(basicInfo.email);
      this.basicDetailsForm.get('phoneNumber').setValue(basicInfo.phoneNumber);
      this.basicDetailsForm.get('phoneCode').setValue(basicInfo.phoneCode);
    } else {
      this.basicDetailsForm.get('phoneCode').setValue('+1');
    }
    this.changeMask(this.basicDetailsForm.value.phoneCode);
  }

  public MustMatch(controlName: string, matchingControlName: string): any {
    return (formGroup: UntypedFormGroup): any => {
      const control = formGroup.controls[controlName];
      const matchingControl = formGroup.controls[matchingControlName];
      if (matchingControl.errors && !matchingControl.errors.mustMatch) {
        return;
      }
      if (control.value !== matchingControl.value) {
        matchingControl.setErrors({ mustMatch: true });
      } else {
        matchingControl.setErrors(null);
      }
    };
  }

  public projectForm(): void {
    this.homePageProjectDetailsForm = this.formBuilder.group({
      projectLocation: ['', Validators.compose([Validators.required])],
      projectName: ['', Validators.compose([Validators.required])],
      projectLocationLatitude: [''],
      projectLocationLongitude: [''],
    });
    const projectDetails = localStorage.getItem('project');
    const projectInfo = JSON.parse(projectDetails);
    if (projectInfo !== undefined && projectInfo !== null) {
      this.homePageProjectDetailsForm.get('projectName').setValue(projectInfo.projectName);
      this.homePageProjectDetailsForm.get('projectLocation').setValue(projectInfo.projectLocation);
      this.homePageProjectDetailsForm
        .get('projectLocationLatitude')
        .setValue(projectInfo.projectLocationLatitude);
      this.homePageProjectDetailsForm
        .get('projectLocationLongitude')
        .setValue(projectInfo.projectLocationLongitude);
    }
  }

  public prev(step): void {
    if (step === 2) {
      this.stepOne = true;
      this.StepTwo = false;
      this.StepThree = false;
    } else if (step === 3) {
      this.StepTwo = true;
      this.StepThree = false;
    }
  }

  public OpenStepThree(): void {
    this.StepTwo = false;
    this.StepThree = true;
  }

  public truncate(value: string, limit = 40, trail = '…'): string {
    let result = value || '';
    let limits = limit;
    if (value) {
      const words = value.split(/\s+/);
      if (words.length > Math.abs(limits)) {
        if (limits < 0) {
          limits *= -1;
          result = trail + words.slice(words.length - limits, words.length).join(' ');
        } else {
          result = words.slice(0, limits).join(' ') + trail;
        }
      }
    }

    return result;
  }

  public getLatLong(data: any) {
    this.getAddress(data.lat, data.lng, null);
  }
}
