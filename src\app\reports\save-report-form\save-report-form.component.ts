import { Component, Input, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ReportsService } from '../../services/reports/reports.service';

@Component({
  selector: 'app-save-report-form',
  templateUrl: './save-report-form.component.html',
  })
export class SaveReportFormComponent implements OnInit {
  @Input('ProjectId') public ProjectId: any;

  @Input('reportType') public reportType: any;

  @Input('updatedMemberList') public updatedMemberList: any;

  @Input('exportType') public exportType = [];

  @Input('filterPayload') public filterPayload: any;

  @Input('pageSize') public pageSize: any;

  @Input('pageNo') public pageNo: any;

  @Input('sortOrder') public sortOrder: any;

  @Input('void') public void: any;

  public formSubmitted: boolean = false;

  public submitted = false;

  public reportForm: UntypedFormGroup;

  public constructor(
    private readonly modalService: BsModalService,
    private readonly reportsService: ReportsService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly toastr: ToastrService,
    private readonly deliveryService: DeliveryService,
    private readonly modalRef: BsModalRef,
  ) {
    this.reportDetailsForm();
  }

  public ngOnInit(): void {
    this.reportDetailsForm();
  }

  public reportDetailsForm(): void {
    this.reportForm = this.formBuilder.group({
      reportName: ['', Validators.required],
      outputFormat: ['', Validators.compose([Validators.required])],
    });
    this.reportForm.get('outputFormat').setValue('PDF');
  }

  public reportSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    if (this.reportForm.invalid) {
      this.formSubmitted = false;
      return;
    }
    if (this.reportForm.valid) {
      this.saveReports();
    }
  }

  public saveReports(): void {
    const params = {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.pageNo,
      sortOrder: this.sortOrder,
      void: 0,
    };

    const payload: any = {};
    payload.reportName = this.reportForm.value.reportName ? this.reportForm.value.reportName : null;
    payload.exportType = this.reportForm.value.outputFormat
      ? this.reportForm.value.outputFormat
      : 'PDF';

    if (this.reportType === 'Heat Map') {
      this.heatMapPayload(params, payload);
    }
    if (this.reportType === 'Delivery') {
      this.deliveryPayload(params, payload);
    }
    if (this.reportType === 'Crane') {
      this.cranePayload(params, payload);
    }
    if (this.reportType === 'Concrete') {
      this.concretePayload(params, payload);
    }
    if (this.reportType === 'Weekly Calender') {
      this.weeklyPayload(params, payload);
    }
    if (this.reportType === 'Inspection') {
      this.inspectionPayload(params, payload);
    }
  }

  public concreateSavedReports(params, payload): void {
    this.formSubmitted = true;
    this.reportsService.saveReportConcreteRequest(params, payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.formSubmitted = false;
          if (response.message === 'No data found.') {
            this.toastr.error('No data found');
            this.saveReportClose();
            return;
          }
          this.toastr.success('Report saved successfully');
          this.saveReportClose();
        }
      },
      error: (reportError): void => {
        this.formSubmitted = false;
        this.toastr.error('Try again later.!', 'Something went wrong.');
      },
    });
  }

  public weeklySavedReports(params, payload): void {
    this.formSubmitted = true;
    this.deliveryService.saveReportWeeklyCalendarRequest(params, payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.formSubmitted = false;
          if (response.message === 'No data found.') {
            this.toastr.error('No data found');
            this.saveReportClose();
            return;
          }
          if (response.message.includes('Currently')) {
            this.toastr.error(response.message);
            this.saveReportClose();
            return;
          }
          this.toastr.success('Report saved successfully');
          this.saveReportClose();
        }
      },
      error: (reportError): void => {
        this.formSubmitted = false;
        this.toastr.error('Try again later.!', 'Something went wrong.');
      },
    });
  }

  public craneSavedReports(params, payload): void {
    this.formSubmitted = true;
    this.reportsService.saveReportCraneRequest(params, payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.formSubmitted = false;
          if (response.message === 'No data found.') {
            this.toastr.error('No data found');
            this.saveReportClose();
            return;
          }
          this.toastr.success('Report saved successfully');
          this.saveReportClose();
        }
      },
      error: (reportError): void => {
        this.formSubmitted = false;
        this.toastr.error('Try again later.!', 'Something went wrong.');
      },
    });
  }

  public deliverySavedReports(params, payload): void {
    this.formSubmitted = true;
    this.reportsService.saveReportDeliveryRequest(params, payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.formSubmitted = false;
          if (response.message === 'No data found.') {
            this.toastr.error('No data found');
            this.saveReportClose();
            return;
          }
          this.toastr.success('Report saved successfully');
          this.saveReportClose();
        }
      },
      error: (reportError): void => {
        this.formSubmitted = false;
        this.toastr.error('Try again later.!', 'Something went wrong.');
      },
    });
  }

  public heatMapSavedReports(params, payload): void {
    this.formSubmitted = true;
    if (payload.exportType === 'PDF') {
      this.reportsService.saveReportHeatMap(params, payload).subscribe({
        next: (response: any): void => {
          if (response) {
            this.formSubmitted = false;
            if (response.message === 'No data found.') {
              this.toastr.error('No data found');
              this.saveReportClose();
              return;
            }
            this.toastr.success('Report saved successfully');
            this.saveReportClose();
          }
        },
        error: (reportError): void => {
          this.formSubmitted = false;
          this.toastr.error('Try again later.!', 'Something went wrong.');
        },
      });
    }
  }

  public saveReportClose(): void {
    this.modalRef.hide();
  }

  public heatMapPayload(params, data): void {
    const payload: any = data;
    this.filterPayload.startTime = moment(this.filterPayload.startTime, 'HH:mm:ss').toDate();
    this.filterPayload.endTime = moment(this.filterPayload.endTime, 'HH:mm:ss').toDate();
    payload.timezone = this.filterPayload.timezone
      ? this.filterPayload.timezone
      : Intl.DateTimeFormat().resolvedOptions().timeZone;
    payload.companyFilter = this.filterPayload.companyFilter ? this.filterPayload.companyFilter : 0;
    payload.memberFilter = this.filterPayload.memberFilter ? this.filterPayload.memberFilter : 0;
    payload.defineFilter = this.filterPayload.defineFilter ? this.filterPayload.defineFilter : 0;
    payload.equipmentFilter = this.filterPayload.equipmentFilter
      ? this.filterPayload.equipmentFilter
      : 0;
    payload.gateFilter = this.filterPayload.gateFilter ? this.filterPayload.gateFilter : 0;
    payload.statusFilter = this.filterPayload.statusFilter ? this.filterPayload.statusFilter : '';
    payload.templateType = this.filterPayload.templateType;
    payload.startTime = this.filterPayload.startTime
      ? moment(this.filterPayload.startTime).format('HH:mm:00')
      : '00:00:00';
    payload.endTime = this.filterPayload.endTime
      ? moment(this.filterPayload.endTime).format('HH:mm:00')
      : '00:00:00';
    payload.ParentCompanyId = this.filterPayload.ParentCompanyId
      ? this.filterPayload.ParentCompanyId
      : 0;
    payload.endDate = this.filterPayload.endDate;
    payload.startDate = this.filterPayload.startDate;
    payload.generatedDate = moment(new Date()).format('ddd, MMM DD YYYY');
    payload.isDST = this.filterPayload.isDST;
    payload.typeFormat = this.filterPayload.typeFormat;
    this.heatMapSavedReports(params, payload);
  }

  public deliveryPayload(params, data): void {
    const payload: any = data;
    payload.companyFilter = this.filterPayload.companyFilter;
    payload.descriptionFilter = this.filterPayload.descriptionFilter;
    payload.statusFilter = this.filterPayload.statusFilter;
    payload.memberFilter = this.filterPayload.memberFilter;
    payload.gateFilter = this.filterPayload.gateFilter;
    payload.equipmentFilter = this.filterPayload.equipmentFilter;
    payload.defineFilter = this.filterPayload.defineFilter;
    payload.idFilter = this.filterPayload.idFilter;
    payload.startDate = this.filterPayload.startdate ? this.filterPayload.startdate : '';
    payload.endDate = this.filterPayload.enddate ? this.filterPayload.enddate : '';
    payload.sort = this.filterPayload.sort;
    payload.sortByField = this.filterPayload.sortByField;
    if (this.filterPayload.sort === 'DESC' && this.filterPayload.sortByField === ' id') {
      payload.sort = 'ASC';
      payload.sortByField = 'deliveryStart';
    }
    payload.ParentCompanyId = this.filterPayload.ParentCompanyId;
    payload.queuedNdr = this.filterPayload.queuedNdr;
    payload.generatedDate = moment(new Date()).format('ddd, MMM DD YYYY');
    payload.selectedHeaders = this.filterPayload.selectedHeaders;
    payload.timezone = this.filterPayload.timezone
      ? this.filterPayload.timezone
      : Intl.DateTimeFormat().resolvedOptions().timeZone;
    payload.locationFilter=this.filterPayload.locationFilter ? this.filterPayload.locationFilter:0;
    this.deliverySavedReports(params, payload);
  }

  public cranePayload(params, data): void {
    const payload: any = data;
    payload.companyFilter = this.filterPayload.companyFilter
      ? this.filterPayload.companyFilter
      : '';
    payload.descriptionFilter = this.filterPayload.descriptionFilter
      ? this.filterPayload.descriptionFilter
      : '';
    payload.statusFilter = this.filterPayload.statusFilter ? this.filterPayload.statusFilter : '';
    payload.memberFilter = this.filterPayload.memberFilter ? this.filterPayload.memberFilter : 0;
    payload.equipmentFilter = this.filterPayload.equipmentFilter
      ? this.filterPayload.equipmentFilter
      : '';
    payload.pickFrom = this.filterPayload.pickFrom;
    payload.pickTo = this.filterPayload.pickTo;
    payload.defineFilter = this.filterPayload.defineFilter ? this.filterPayload.defineFilter : 0;
    payload.startDate = this.filterPayload.startdate ? this.filterPayload.startdate : '';
    payload.endDate = this.filterPayload.enddate ? this.filterPayload.enddate : '';
    payload.idFilter = this.filterPayload.idFilter ? this.filterPayload.idFilter : 0;
    payload.gateFilter = this.filterPayload.gateFilter ? this.filterPayload.gateFilter : 0;
    payload.sort = this.filterPayload.sort;
    payload.sortByField = this.filterPayload.sortByField;
    if (this.filterPayload.sort === 'DESC' && this.filterPayload.sortByField === ' CraneRequestId') {
      payload.sort = 'ASC';
      payload.sortByField = 'datetime';
    }
    payload.ParentCompanyId = this.filterPayload.ParentCompanyId
      ? this.filterPayload.ParentCompanyId
      : 0;
    payload.generatedDate = this.filterPayload.generatedDate;
    payload.selectedHeaders = this.filterPayload.selectedHeaders;
    payload.timezone = this.filterPayload.timezone
      ? this.filterPayload.timezone
      : Intl.DateTimeFormat().resolvedOptions().timeZone;
    payload.locationFilter=this.filterPayload.locationFilter ? this.filterPayload.locationFilter:0;
    this.craneSavedReports(params, payload);
  }

  public concretePayload(params, data): void {
    const payload: any = data;

    // Simplify the assignment of filter values
    const getFilterValue = (filterName: string, defaultValue: any = '') => this.filterPayload[filterName] ?? defaultValue;

    // Set values in the payload
    payload.locationFilter = getFilterValue('locationFilter', 0);
    payload.descriptionFilter = getFilterValue('descriptionFilter', '');
    payload.statusFilter = getFilterValue('statusFilter', '');
    payload.orderNumberFilter = getFilterValue('orderNumberFilter', '');
    payload.mixDesignFilter = getFilterValue('mixDesignFilter', '');
    payload.memberFilter = getFilterValue('memberFilter', 0);
    payload.startDate = getFilterValue('startdate', '');
    payload.endDate = getFilterValue('enddate', '');
    payload.idFilter = getFilterValue('idFilter', 0);
    payload.truckspacingFilter = getFilterValue('truckspacingFilter', '');
    payload.slumpFilter = getFilterValue('slumpFilter', '');
    payload.primerFilter = getFilterValue('primerFilter', '');
    payload.quantityFilter = getFilterValue('quantityFilter', '');
    payload.ParentCompanyId = getFilterValue('ParentCompanyId', 0);
    payload.generatedDate = getFilterValue('generatedDate');
    payload.selectedHeaders = this.filterPayload.selectedHeaders;
    payload.equipmentFilter = getFilterValue('equipmentFilter', 0);
    payload.companyFilter = getFilterValue('concreteSupplierFilter', '');
    payload.gateFilter = getFilterValue('gateFilter', 0);
    payload.defineFilter = getFilterValue('defineFilter', 0);
    payload.timezone = getFilterValue('timezone', Intl.DateTimeFormat().resolvedOptions().timeZone);

    // Handle sorting logic separately
    this.handleSorting(payload);

    // Call the save report function
    this.concreateSavedReports(params, payload);
  }

  public handleSorting(payload: any): any {
    const updatedPayload = { ...payload };
    if (this.filterPayload.sort === 'DESC' && this.filterPayload.sortByField === 'id') {
      updatedPayload.sort = 'ASC';
      updatedPayload.sortByField = 'concretePlacementStart';
    } else {
      updatedPayload.sort = this.filterPayload.sort;
      updatedPayload.sortByField = this.filterPayload.sortByField;
    }
    return updatedPayload;
  }


  public weeklyPayload(params, data): void {
    const payload: any = data;
    payload.start = this.filterPayload.start;
    payload.end = this.filterPayload.end;
    payload.companyFilter = this.filterPayload.companyFilter ? this.filterPayload.companyFilter : 0;
    payload.startDate = this.filterPayload.startDate;
    payload.endDate = this.filterPayload.endDate;
    payload.statusFilter = this.filterPayload.statusFilter ? this.filterPayload.statusFilter : '';
    payload.memberFilter = this.filterPayload.memberFilter ? this.filterPayload.memberFilter : 0;
    payload.gateFilter = this.filterPayload.gateFilter ? this.filterPayload.gateFilter : 0;
    payload.equipmentFilter = this.filterPayload.equipmentFilter
      ? this.filterPayload.equipmentFilter
      : 0;
    payload.templateType = this.filterPayload.templateType;
    payload.defineFilter = this.filterPayload.defineFilter ? this.filterPayload.defineFilter : 0;
    payload.startTime = this.filterPayload.startTime ? this.filterPayload.startTime : '07:00:00';
    payload.endTime = this.filterPayload.endTime ? this.filterPayload.endTime : '17:00:00';
    payload.ParentCompanyId = this.filterPayload.ParentCompanyId;
    payload.eventStartTime = this.filterPayload.eventStartTime
      ? this.filterPayload.eventStartTime
      : '07:00:00';
    payload.eventEndTime = this.filterPayload.eventEndTime
      ? this.filterPayload.eventEndTime
      : '17:00:00';
    payload.generatedDate = this.filterPayload.generatedDate;
    payload.currentViewMonth = this.filterPayload.currentViewMonth;
    payload.timezone = this.filterPayload.timezone
      ? this.filterPayload.timezone
      : Intl.DateTimeFormat().resolvedOptions().timeZone;
    payload.isDST = this.filterPayload.isDST;
    payload.currentStart = this.filterPayload.currentStart;
    payload.currentEnd = this.filterPayload.currentEnd;
    payload.typeFormat = this.filterPayload.typeFormat;
    payload.locationFilter=this.filterPayload.locationFilter ? this.filterPayload.locationFilter:0;
    this.weeklySavedReports(params, payload);
  }

  public inspectionPayload(params, data): void {
    const payload: any = data;
    payload.companyFilter = this.filterPayload.companyFilter;
    payload.descriptionFilter = this.filterPayload.descriptionFilter;
    payload.statusFilter = this.filterPayload.statusFilter;
    payload.inspectionStatusFilter = this.filterPayload.inspectionStatusFilter;
    payload.inspectionTypeFilter = this.filterPayload.inspectionTypeFilter;
    payload.memberFilter = this.filterPayload.memberFilter;
    payload.gateFilter = this.filterPayload.gateFilter;
    payload.equipmentFilter = this.filterPayload.equipmentFilter;
    payload.defineFilter = this.filterPayload.defineFilter;
    payload.idFilter = this.filterPayload.idFilter;
    payload.startDate = this.filterPayload.startdate ? this.filterPayload.startdate : '';
    payload.endDate = this.filterPayload.enddate ? this.filterPayload.enddate : '';
    payload.sort = this.filterPayload.sort;
    payload.sortByField = this.filterPayload.sortByField;
    if (this.filterPayload.sort === 'DESC' && this.filterPayload.sortByField === ' id') {
      payload.sort = 'ASC';
      payload.sortByField = 'inspectionStart';
    }
    payload.ParentCompanyId = this.filterPayload.ParentCompanyId;
    payload.queuedNdr = this.filterPayload.queuedNdr;
    payload.generatedDate = moment(new Date()).format('ddd, MMM DD YYYY');
    payload.selectedHeaders = this.filterPayload.selectedHeaders;
    payload.timezone = this.filterPayload.timezone
      ? this.filterPayload.timezone
      : Intl.DateTimeFormat().resolvedOptions().timeZone;
    payload.locationFilter=this.filterPayload.locationFilter ? this.filterPayload.locationFilter:0;
    this.inspectionSavedReports(params, payload);
  }

  public inspectionSavedReports(params, payload): void {
    this.formSubmitted = true;
    this.reportsService.saveReportInspectionRequest(params, payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.formSubmitted = false;
          if (response.message === 'No data found.') {
            this.toastr.error('No data found');
            this.saveReportClose();
            return;
          }
          this.toastr.success('Report saved successfully');
          this.saveReportClose();
        }
      },
      error: (reportError): void => {
        this.formSubmitted = false;
        this.toastr.error('Try again later.!', 'Something went wrong.');
      },
    });
  }
}
