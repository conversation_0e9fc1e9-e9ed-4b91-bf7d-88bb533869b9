/* eslint-disable max-len */
/* eslint-disable max-lines-per-function */
/* eslint-disable @typescript-eslint/no-inferrable-types */
import { Component, OnInit, TemplateRef } from '@angular/core';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { Title } from '@angular/platform-browser';
import { Observable } from 'rxjs';
import { ProjectSettingsService } from '../../services/project_settings/project-settings.service';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';

@Component({
  selector: 'app-project-settings-tab',
  templateUrl: './project-settings-tab.component.html',
})
export class ProjectSettingsTabComponent implements OnInit {
  public modalRef?: BsModalRef;

  public ProjectId;

  public ParentCompanyId;

  public calendarDefaultColor: string = '#2c3593';

  public defaultStatusColor: string;

  public loader: boolean = false;

  public updateLoader: boolean;

  public deliveryWindowUnits = ['minutes', 'hours', 'days', 'months'];

  public deliveryCardLine1;

  public deliveryCardLine2;

  public craneCardLine1;

  public craneCardLine2;

  public concreteCardLine1;

  public concreteCardLine2;

  public projectSettingsFormData: UntypedFormGroup;

  public autoApproveFormData: UntypedFormGroup;

  public items = ['Sub Contractor', 'Project Admin'];

  public items1 = ['User1', 'User2'];

  public enabledMemberArray = [];

  public disabledMemberArray = [];

  public getautoApprovalMembers = [];

  public autoApproveLoader = false;

  public deliveryData;

  public concreteData;

  public craneData;

  public checkbox1: boolean = true;

  public inspectionCardLine1: any;

  public inspectionCardLine2: any;

  public inspectionData: any;
  public equipmentList: any  = [];
  public equipmentDropdownSettings: IDropdownSettings;
  public gateDropdownSettings: IDropdownSettings;
  public gateList: any = [];
  showData: boolean;

  public constructor(
    private readonly modalService: BsModalService,
    private readonly titleService: Title,
    public projectSettingsService: ProjectSettingsService,
    public projectService: ProjectService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly toastr: ToastrService,
    private readonly deliveryService: DeliveryService,
  ) {
    this.titleService.setTitle('Follo - Project Settings');
    this.projectService.projectParent.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ProjectId = res.ProjectId;
        this.ParentCompanyId = res.ParentCompanyId;
        this.setEquipment();
        this.setGate();
        this.projectSettingsForm();
        this.getProjectSettings();
        this.autoApproveForm();
      }
    });
  }

  public ngOnInit(): void {
    this.setEquipment();
    this.setGate();
    this.projectSettingsForm();
    this.getProjectSettings();
    this.autoApproveForm();
  }

  public statusColorStyle(): any {
    return {
      'border-radius': '0px 5px 5px 0px',
      padding: '5px 15px',
      'font-size': '12px',
      opacity: 1,
    };
  }

  public get approved(): any {
    const formData = this.projectSettingsFormData.value;
    return {
      ...this.statusColorStyle(),
      background: `${formData.approvedBackgroundColor} 0% 0% no-repeat padding-box`,
      color: `${formData.approvedFontColor}`,
      'border-left': `4px solid ${formData.approvedFontColor}`,
    };
  }

  public get pending(): any {
    const formData = this.projectSettingsFormData.value;
    return {
      ...this.statusColorStyle(),
      background: `${formData.pendingBackgroundColor} 0% 0% no-repeat padding-box`,
      color: `${formData.pendingFontColor}`,
      'border-left': `4px solid ${formData.pendingFontColor}`,
    };
  }

  public get delivered(): any {
    const formData = this.projectSettingsFormData.value;
    return {
      ...this.statusColorStyle(),
      background: `${formData.deliveredBackgroundColor} 0% 0% no-repeat padding-box`,
      color: `${formData.deliveredFontColor}`,
      'border-left': `4px solid ${formData.deliveredBackgroundColor}`,
    };
  }

  public get rejected(): any {
    const formData = this.projectSettingsFormData.value;
    return {
      ...this.statusColorStyle(),
      background: `${formData.rejectedBackgroundColor} 0% 0% no-repeat padding-box`,
      color: `${formData.rejectedFontColor}`,
      'border-left': `4px solid ${formData.rejectedFontColor}`,
    };
  }

  public get expired(): any {
    const formData = this.projectSettingsFormData.value;
    return {
      ...this.statusColorStyle(),
      background: `${formData.expiredBackgroundColor} 0% 0% no-repeat padding-box`,
      color: `${formData.expiredFontColor}`,
      'border-left': `4px solid ${formData.expiredFontColor}`,
    };
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item?: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (data) {
        case 'color':
          this.setDefault(data);
          break;
        case 'card':
          this.setDefault(data);
          break;
        case 'open':
          this.openModal(item);
          break;
        case 'remove':
          this.removeMemberFromAutoApproval(item);
          break;
        default:
          break;
      }
    }
  }

  public projectSettingsForm(): void {
    this.projectSettingsFormData = this.formBuilder.group({
      deliveryWindowTime: ['', Validators.required],
      deliveryWindowTimeUnit: ['', Validators.required],
      inspectionWindowTime: ['', Validators.required],
      inspectionWindowTimeUnit: ['', Validators.required],
      craneWindowTime: ['', Validators.required],
      craneWindowTimeUnit: ['', Validators.required],
      concreteWindowTime: ['', Validators.required],
      concreteWindowTimeUnit: ['', Validators.required],
      isAutoApprovalEnabled: ['', Validators.required],
      deliveryAllowOverlappingBooking: ['', Validators.required],
      deliveryAllowOverlappingCalenderEvents: ['', Validators.required],
      craneAllowOverlappingBooking: ['', Validators.required],
      craneAllowOverlappingCalenderEvents: ['', Validators.required],
      concreteAllowOverlappingBooking: ['', Validators.required],
      concreteAllowOverlappingCalenderEvents: ['', Validators.required],
      selectedConcreteLine1: ['', Validators.required],
      selectedConcreteLine2: ['', Validators.required],
      selectedCraneLine1: ['', Validators.required],
      selectedCraneLine2: ['', Validators.required],
      selectedDeliveryLine1: ['', Validators.required],
      selectedDeliveryLine2: ['', Validators.required],
      approvedBackgroundColor: ['', Validators.required],
      approvedFontColor: ['', Validators.required],
      deliveredBackgroundColor: ['', Validators.required],
      deliveredFontColor: ['', Validators.required],
      rejectedBackgroundColor: ['', Validators.required],
      rejectedFontColor: ['', Validators.required],
      pendingBackgroundColor: ['', Validators.required],
      pendingFontColor: ['', Validators.required],
      expiredBackgroundColor: ['', Validators.required],
      expiredFontColor: ['', Validators.required],
      isDefaultColor: ['', Validators.required],
      currentApprovedBackgroundColor: ['', Validators.required],
      currentApprovedFontColor: ['', Validators.required],
      currentDeliveredBackgroundColor: ['', Validators.required],
      currentDeliveredFontColor: ['', Validators.required],
      currentRejectedBackgroundColor: ['', Validators.required],
      currentRejectedFontColor: ['', Validators.required],
      currentPendingBackgroundColor: ['', Validators.required],
      currentPendingFontColor: ['', Validators.required],
      currentExpiredBackgroundColor: ['', Validators.required],
      currentExpiredFontColor: ['', Validators.required],
      inspectionAllowOverlappingBooking: ['', Validators.required],
      inspectionAllowOverlappingCalenderEvents: ['', Validators.required],
      selectedInspectionLine1: ['', Validators.required],
      selectedInspectionLine2: ['', Validators.required],
      workingWindowStart: ['', Validators.required],
      workingWindowEnd: ['', Validators.required],
      equipmentExceptions: ['', Validators.required],
      gateExceptions: ['', Validators.required],
    });
  }

  public setGate(): void {
    const params = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .gateList(params, { isFilter: true, showActivatedAlone: true })
      .subscribe((res): void => {
        this.gateList = res.data;
        this.gateDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'gateName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
      });
  }

  public onColorPickerChange(controlName: string, color: string): void {
    this.projectSettingsFormData.get(controlName).setValue(color);
  }

  public autoApproveForm(): void {
    this.autoApproveFormData = this.formBuilder.group({
      person: [''],
    });
  }


  public setEquipment(): void {
    if (this.equipmentList?.length === 0) {
      const newNdrGetEquipmentsParams = {
        ProjectId: this.ProjectId,
        pageSize: 0,
        pageNo: 0,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.projectService
        .listEquipment(newNdrGetEquipmentsParams, {
          isFilter: true,
          showActivatedAlone: true,
        })
        .subscribe((equipmentListResponseForNewNdr): void => {
          this.equipmentList = equipmentListResponseForNewNdr.data;
          this.equipmentDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'equipmentName',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
        });
    }
  }

  public cardSettings(type, data): any {
    const formData = this.projectSettingsFormData.value;
    const linePairs = {
      deliveryCardLine1: ['selectedDeliveryLine2', 'selectedDeliveryLine1'],
      deliveryCardLine2: ['selectedDeliveryLine1', 'selectedDeliveryLine2'],
      craneCardLine1: ['selectedCraneLine2', 'selectedCraneLine1'],
      craneCardLine2: ['selectedCraneLine1', 'selectedCraneLine2'],
      concreteCardLine1: ['selectedConcreteLine2', 'selectedConcreteLine1'],
      concreteCardLine2: ['selectedConcreteLine1', 'selectedConcreteLine2'],
      inspectionCardLine1: ['selectedInspectionLine2', 'selectedInspectionLine1'],
      inspectionCardLine2: ['selectedInspectionLine1', 'selectedInspectionLine2'],
    };
    const fields = linePairs[type];
    if (fields && data === formData[fields[0]]) {
      this.projectSettingsFormData.get(fields[0])?.setValue('');
    }
  }

  public getProjectSettings(): void {
    this.loader = true;
    if (this.ProjectId) {
      const params = {
        ProjectId: this.ProjectId,
      };
      this.projectSettingsService.getProjectSettings(params).subscribe({
        next: (res): void => {
          const responseData = res?.data?.projectSettings;
          this.checkbox1 = responseData.useTextColorAsLegend;

          // Safely parse JSON strings, handle cases where they might already be objects
          try {
            responseData.deliveryCard = typeof responseData.deliveryCard === 'string'
              ? JSON.parse(responseData.deliveryCard)
              : responseData.deliveryCard;
            responseData.craneCard = typeof responseData.craneCard === 'string'
              ? JSON.parse(responseData.craneCard)
              : responseData.craneCard;
            responseData.concreteCard = typeof responseData.concreteCard === 'string'
              ? JSON.parse(responseData.concreteCard)
              : responseData.concreteCard;
            responseData.inspectionCard = typeof responseData.inspectionCard === 'string'
              ? JSON.parse(responseData.inspectionCard)
              : responseData.inspectionCard;
          } catch (error) {
            console.error('Error parsing card data:', error);
            // Set default empty arrays if parsing fails
            responseData.deliveryCard = responseData.deliveryCard || [];
            responseData.craneCard = responseData.craneCard || [];
            responseData.concreteCard = responseData.concreteCard || [];
            responseData.inspectionCard = responseData.inspectionCard || [];
          }

          const statusColorCodeData = JSON.parse(responseData.statusColorCode);
          this.deliveryData = responseData.deliveryCard;
          this.craneData = responseData.craneCard;
          this.concreteData = responseData.concreteCard;
          this.inspectionData = responseData.inspectionCard;

          const equipmentKey = `equipmentExceptions`;
          const gateKey = `gateExceptions`;

          const equipmentExceptionIds = JSON.parse(responseData[equipmentKey] || '[]');
          const gateExceptionIds = JSON.parse(responseData[gateKey] || '[]');

          const selectedEquipment = this.equipmentList.filter(equipment =>
            equipmentExceptionIds.includes(equipment.id)
          );

          const selectedGate = this.gateList.filter(gate =>
            gateExceptionIds.includes(gate.id)
          );

          this.projectSettingsFormData.get(equipmentKey)?.setValue(selectedEquipment);
          this.projectSettingsFormData.get(gateKey)?.setValue(selectedGate);

        const checkDeliverySelectedTrue = [];
        this.deliveryData.forEach((item): any => {
          checkDeliverySelectedTrue.push({ label: item.label, line: 0, selected: false });
        });
        this.deliveryData = checkDeliverySelectedTrue;

        const checkCraneSelectedTrue = [];
        this.craneData.forEach((item): any => {
          checkCraneSelectedTrue.push({ label: item.label, line: 0, selected: false });
        });
        this.craneData = checkCraneSelectedTrue;

        const checkConcreteSelectedTrue = [];
        this.concreteData.forEach((item): any => {
          checkConcreteSelectedTrue.push({ label: item.label, line: 0, selected: false });
        });
        this.concreteData = checkConcreteSelectedTrue;

        const checkInspectionSelectedTrue = [];
        this.inspectionData.forEach((item): any => {
          checkInspectionSelectedTrue.push({ label: item.label, line: 0, selected: false });
        });
        this.inspectionData = checkInspectionSelectedTrue;

        this.statusColorCode(statusColorCodeData);

        if (responseData) {
          this.getCardData(responseData);
        }
          this.getautoApprovalMembers = res?.data?.autoApproveMembers;
          this.loader = false;
        },
        error: (error): void => {
          console.error('Error fetching project settings:', error);
          this.loader = false;
        }
      });
    }
  }

  public getCardData(responseData): void {
    this.showData = false;
    const updatedStart = new Date();
    updatedStart.setHours(responseData.workingWindowStartHours);
    updatedStart.setMinutes(responseData.workingWindowStartMinutes);
    updatedStart.setSeconds(0);

    const updatedEnd = new Date();
    updatedEnd.setHours(responseData.workingWindowEndHours);
    updatedEnd.setMinutes(responseData.workingWindowEndMinutes);
    updatedEnd.setSeconds(0);

    this.projectSettingsFormData.patchValue({
      workingWindowStart: updatedStart,
      workingWindowEnd: updatedEnd
    });


    this.projectSettingsFormData
      .get('deliveryWindowTime')
      .setValue(responseData.deliveryWindowTime);

    this.projectSettingsFormData
      .get('deliveryWindowTimeUnit')
      .setValue(responseData.deliveryWindowTimeUnit);
    this.projectSettingsFormData
      .get('inspectionWindowTime')
      .setValue(responseData.inspectionWindowTime);

    this.projectSettingsFormData
      .get('inspectionWindowTimeUnit')
      .setValue(responseData.inspectionWindowTimeUnit);

    this.projectSettingsFormData
      .get('craneWindowTime')
      .setValue(responseData.craneWindowTime);

    this.projectSettingsFormData
      .get('craneWindowTimeUnit')
      .setValue(responseData.craneWindowTimeUnit);

    this.projectSettingsFormData
      .get('concreteWindowTime')
      .setValue(responseData.concreteWindowTime);

    this.projectSettingsFormData
      .get('concreteWindowTimeUnit')
      .setValue(responseData.concreteWindowTimeUnit);

    this.projectSettingsFormData
      .get('isDefaultColor')
      .setValue(responseData.isDefaultColor);
    this.projectSettingsFormData
      .get('isAutoApprovalEnabled')
      .setValue(responseData.isAutoApprovalEnabled);
    this.projectSettingsFormData
      .get('deliveryAllowOverlappingBooking')
      .setValue(responseData.deliveryAllowOverlappingBooking);
    this.projectSettingsFormData
      .get('deliveryAllowOverlappingCalenderEvents')
      .setValue(responseData.deliveryAllowOverlappingCalenderEvents);
    this.projectSettingsFormData
      .get('craneAllowOverlappingBooking')
      .setValue(responseData.craneAllowOverlappingBooking);
    this.projectSettingsFormData
      .get('craneAllowOverlappingCalenderEvents')
      .setValue(responseData.craneAllowOverlappingCalenderEvents);
    this.projectSettingsFormData
      .get('concreteAllowOverlappingBooking')
      .setValue(responseData.concreteAllowOverlappingBooking);
    this.projectSettingsFormData
      .get('concreteAllowOverlappingCalenderEvents')
      .setValue(responseData.concreteAllowOverlappingCalenderEvents);
    this.projectSettingsFormData
      .get('inspectionAllowOverlappingBooking')
      .setValue(responseData.inspectionAllowOverlappingBooking);
    this.projectSettingsFormData
      .get('inspectionAllowOverlappingCalenderEvents')
      .setValue(responseData.inspectionAllowOverlappingCalenderEvents);

    this.deliveryCardLine1 = responseData.deliveryCard;
    this.deliveryCardLine2 = responseData.deliveryCard;
    this.craneCardLine1 = responseData.craneCard;
    this.craneCardLine2 = responseData.craneCard;
    this.concreteCardLine1 = responseData.concreteCard;
    this.concreteCardLine2 = responseData.concreteCard;
    this.inspectionCardLine1 = responseData.inspectionCard;
    this.inspectionCardLine2 = responseData.inspectionCard;

    const deliveryDataLine1 = responseData.deliveryCard.find(
      (item): any => item.line === 1 && item.selected,
    );
    const deliveryDataLine2 = responseData.deliveryCard.find(
      (item): any => item.line === 2 && item.selected,
    );
    const craneDataLine1 = responseData.craneCard.find(
      (item): any => item.line === 1 && item.selected,
    );
    const craneDataLine2 = responseData.craneCard.find(
      (item): any => item.line === 2 && item.selected,
    );
    const concreteDataLine1 = responseData.concreteCard.find(
      (item): any => item.line === 1 && item.selected,
    );
    const concreteDataLine2 = responseData.concreteCard.find(
      (item): any => item.line === 2 && item.selected,
    );
    const inspectionDataLine1 = responseData.inspectionCard.find(
      (item): any => item.line === 1 && item.selected,
    );
    const inspectionDataLine2 = responseData.inspectionCard.find(
      (item): any => item.line === 2 && item.selected,
    );
    setTimeout(() => {
       this.showData = true;
    }, 1000);

    this.projectSettingsFormData.get('selectedDeliveryLine1').setValue(deliveryDataLine1?.label);
    this.projectSettingsFormData.get('selectedDeliveryLine2').setValue(deliveryDataLine2?.label);

    this.projectSettingsFormData.get('selectedCraneLine1').setValue(craneDataLine1?.label);
    this.projectSettingsFormData.get('selectedCraneLine2').setValue(craneDataLine2?.label);

    this.projectSettingsFormData.get('selectedConcreteLine1').setValue(concreteDataLine1?.label);
    this.projectSettingsFormData.get('selectedConcreteLine2').setValue(concreteDataLine2?.label);

    this.projectSettingsFormData.get('selectedInspectionLine1').setValue(inspectionDataLine1?.label);
    this.projectSettingsFormData.get('selectedInspectionLine2').setValue(inspectionDataLine2?.label);
  }

  public numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      event.preventDefault();
      return false;
    }
    return true;
  }

  public statusColorCode(colorData): void {
    const formData = this.projectSettingsFormData;
    colorData.forEach((item): void => {
      switch (item.status) {
        case 'approved':
          formData.get('approvedBackgroundColor').setValue(item.backgroundColor);
          formData.get('approvedFontColor').setValue(item.fontColor);
          formData.get('currentApprovedBackgroundColor').setValue(item.backgroundColor);
          formData.get('currentApprovedFontColor').setValue(item.fontColor);
          break;

        case 'pending':
          formData.get('pendingBackgroundColor').setValue(item.backgroundColor);
          formData.get('pendingFontColor').setValue(item.fontColor);
          formData.get('currentPendingBackgroundColor').setValue(item.backgroundColor);
          formData.get('currentPendingFontColor').setValue(item.fontColor);
          break;

        case 'delivered':
          formData.get('deliveredBackgroundColor').setValue(item.backgroundColor);
          formData.get('deliveredFontColor').setValue(item.fontColor);
          formData.get('currentDeliveredBackgroundColor').setValue(item.backgroundColor);
          formData.get('currentDeliveredFontColor').setValue(item.fontColor);
          break;

        case 'rejected':
          formData.get('rejectedBackgroundColor').setValue(item.backgroundColor);
          formData.get('rejectedFontColor').setValue(item.fontColor);
          formData.get('currentRejectedBackgroundColor').setValue(item.backgroundColor);
          formData.get('currentRejectedFontColor').setValue(item.fontColor);
          break;

        case 'expired':
          formData.get('expiredBackgroundColor').setValue(item.backgroundColor);
          formData.get('expiredFontColor').setValue(item.fontColor);
          formData.get('currentExpiredBackgroundColor').setValue(item.backgroundColor);
          formData.get('currentExpiredFontColor').setValue(item.fontColor);
          break;

        default:
          break;
      }
    });
  }

  public setDefault(type): void {
    if (this.ProjectId) {
      let payload;
      if (type === 'color') {
        payload = {
          ProjectId: this.ProjectId,
          isDefaultColor: true,
        };
      } else {
        payload = {
          ProjectId: this.ProjectId,
          setDefaultCard: true,
        };
      }
      this.projectSettingsService.updateProjectSettings(payload).subscribe({
        next: (res): void => {
          if (res) {
            this.toastr.success(res.message, 'Success');
            this.getProjectSettings();
            this.closeAutoApproveModal();
          }
        },
        error: (error): void => {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        },
      });
    }
  }



  public validateData(): boolean {
    let validate = false;
    const formData = this.projectSettingsFormData.value;

    if (formData.selectedDeliveryLine1 === '' || formData.selectedDeliveryLine2 === '') {
      validate = true;
      this.toastr.error('Please choose a delivery card', 'OOPS!');
    }
    if (formData.selectedCraneLine1 === '' || formData.selectedCraneLine2 === '') {
      validate = true;
      this.toastr.error('Please choose a crane card', 'OOPS!');
    }
    if (formData.selectedConcreteLine1 === '' || formData.selectedConcreteLine2 === '') {
      validate = true;
      this.toastr.error('Please choose a concrete card', 'OOPS!');
    }
    if (formData.selectedInspectionLine1 === '' || formData.selectedInspectionLine2 === '') {
      validate = true;
      this.toastr.error('Please choose a inspection card', 'OOPS!');
    }

    const status = [];
    const checkCalendarStatus = [...status, this.calendarDefaultColor];
    const uniqueStatus = new Set(status);
    const checkCalendarUniqueStatus = new Set(checkCalendarStatus);

    if (uniqueStatus.size !== status.length) {
      validate = true;
      this.toastr.error('Please choose a unique color', 'OOPS!');
    }
    if (checkCalendarUniqueStatus.size !== checkCalendarStatus.length) {
      validate = true;
      this.toastr.error(
        'Please choose a distinct color that is not already used as a calendar event color',
        'OOPS!',
      );
    }
    return validate;
  }

  public statusColorCodePayload(): any {
    const formData = this.projectSettingsFormData.value;

    return JSON.stringify([
      {
        status: 'approved',
        fontColor: formData.approvedFontColor,
        backgroundColor: formData.approvedBackgroundColor,
      },
      {
        status: 'pending',
        fontColor: formData.pendingFontColor,
        backgroundColor: formData.pendingBackgroundColor,
      },
      {
        status: 'delivered',
        fontColor: formData.deliveredFontColor,
        backgroundColor: formData.deliveredBackgroundColor,
      },
      {
        status: 'rejected',
        fontColor: formData.rejectedFontColor,
        backgroundColor: formData.rejectedBackgroundColor,
      },
      {
        status: 'expired',
        fontColor: formData.expiredFontColor,
        backgroundColor: formData.expiredBackgroundColor,
      },
    ]);
  }

  public payloadCardData(): any {
    const formData = this.projectSettingsFormData.value;
    const deliveryData = [
      ...this.deliveryData.filter(
        (item): any => item.label !== formData.selectedDeliveryLine1
          && item.label !== formData.selectedDeliveryLine2,
      ),
      { label: formData.selectedDeliveryLine1, line: 1, selected: true },
      { label: formData.selectedDeliveryLine2, line: 2, selected: true },
    ];

    const craneData = [
      ...this.craneData.filter(
        (item): any => item.label !== formData.selectedCraneLine1 && item.label !== formData.selectedCraneLine2,
      ),
      { label: formData.selectedCraneLine1, line: 1, selected: true },
      { label: formData.selectedCraneLine2, line: 2, selected: true },
    ];

    const concreteData = [
      ...this.concreteData.filter(
        (item): any => item.label !== formData.selectedConcreteLine1
          && item.label !== formData.selectedConcreteLine2,
      ),
      { label: formData.selectedConcreteLine1, line: 1, selected: true },
      { label: formData.selectedConcreteLine2, line: 2, selected: true },
    ];

    const inspectionData = [
      ...this.inspectionData.filter(
        (item): any => item.label !== formData.selectedInspectionLine1
          && item.label !== formData.selectedInspectionLine2,
      ),
      { label: formData.selectedInspectionLine1, line: 1, selected: true },
      { label: formData.selectedInspectionLine2, line: 2, selected: true },
    ];
    return {
      deliveryData, craneData, concreteData, inspectionData,
    };
  }

  public submit(): void {
    const validateData = this.validateData();
    if (validateData) {
      return;
    }
    const payloadCarData = this.payloadCardData();
    this.checkStatusColorChange();
    this.updateLoader = true;
    if (this.ProjectId) {
      const payload: any = {
        ProjectId: this.ProjectId,
        workingWindowStartHours: new Date(this.projectSettingsFormData.value.workingWindowStart).getHours(),
        workingWindowStartMinutes: new Date(this.projectSettingsFormData.value.workingWindowStart).getMinutes(),
        workingWindowEndHours: new Date(this.projectSettingsFormData.value.workingWindowEnd).getHours(),
        workingWindowEndMinutes: new Date(this.projectSettingsFormData.value.workingWindowEnd).getMinutes(),
        deliveryWindowTime: this.projectSettingsFormData.value.deliveryWindowTime,
        deliveryWindowTimeUnit: this.projectSettingsFormData.value.deliveryWindowTimeUnit,
        inspectionWindowTime: this.projectSettingsFormData.value.inspectionWindowTime,
        inspectionWindowTimeUnit: this.projectSettingsFormData.value.inspectionWindowTimeUnit,
        craneWindowTime: this.projectSettingsFormData.value.craneWindowTime,
        craneWindowTimeUnit: this.projectSettingsFormData.value.craneWindowTimeUnit,
        concreteWindowTime: this.projectSettingsFormData.value.concreteWindowTime,
        concreteWindowTimeUnit: this.projectSettingsFormData.value.concreteWindowTimeUnit,
        isAutoApprovalEnabled: this.projectSettingsFormData.value.isAutoApprovalEnabled,
        deliveryAllowOverlappingBooking:
          this.projectSettingsFormData.value.deliveryAllowOverlappingBooking,
        deliveryAllowOverlappingCalenderEvents:
          this.projectSettingsFormData.value.deliveryAllowOverlappingCalenderEvents,
        craneAllowOverlappingBooking:
          this.projectSettingsFormData.value.craneAllowOverlappingBooking,
        craneAllowOverlappingCalenderEvents:
          this.projectSettingsFormData.value.craneAllowOverlappingCalenderEvents,
        concreteAllowOverlappingBooking:
          this.projectSettingsFormData.value.concreteAllowOverlappingBooking,
        concreteAllowOverlappingCalenderEvents:
          this.projectSettingsFormData.value.concreteAllowOverlappingCalenderEvents,
        inspectionAllowOverlappingBooking:
          this.projectSettingsFormData.value.inspectionAllowOverlappingBooking,
        inspectionAllowOverlappingCalenderEvents:
          this.projectSettingsFormData.value.inspectionAllowOverlappingCalenderEvents,
        setDefaultCard: false,
        isDefaultColor: this.projectSettingsFormData.value.isDefaultColor,
        gateExceptions: JSON.stringify(this.projectSettingsFormData.value.gateExceptions.map((item) => item.id)),
        equipmentExceptions: JSON.stringify(this.projectSettingsFormData.value.equipmentExceptions.map((item) => item.id)),
        statusColorCode: this.statusColorCodePayload(),
        deliveryCard: JSON.stringify(payloadCarData.deliveryData),
        craneCard: JSON.stringify(payloadCarData.craneData),
        concreteCard: JSON.stringify(payloadCarData.concreteData),
        inspectionCard: JSON.stringify(payloadCarData.inspectionData),
        useTextColorAsLegend: this.checkbox1,
      };
      if (this.disabledMemberArray && this.disabledMemberArray.length > 0) {
        payload.disabledUser = this.disabledMemberArray;
      }
      if (this.autoApproveFormData?.value?.person?.length > 0) {
        this.autoApproveFormData.value.person.forEach(async (element): Promise<void> => {
          this.enabledMemberArray.push(element.id);
        });
      }
      if (this.enabledMemberArray && this.enabledMemberArray.length > 0) {
        payload.enabledUser = this.enabledMemberArray;
      }
      this.projectSettingsService.updateProjectSettings(payload).subscribe({
        next:
        (res): void => {
          if (res) {
            this.updateLoader = false;
            this.toastr.success(res.message, 'Success');
            this.getProjectSettings();
            this.closeAutoApproveModal();
          }
        },
        error: (error): void => {
          this.updateLoader = false;
          this.toastr.error('Try again later.!', 'Something went wrong.');
        },
      });
    }
  }

  public checkStatusColorChange(): void {
    if (this.projectSettingsFormData.get('isDefaultColor').value) {
      if (this.projectSettingsFormData.get('approvedBackgroundColor').value !== this.projectSettingsFormData.get('currentApprovedBackgroundColor').value
      || this.projectSettingsFormData.get('approvedFontColor').value !== this.projectSettingsFormData.get('currentApprovedFontColor').value
      || this.projectSettingsFormData.get('deliveredBackgroundColor').value !== this.projectSettingsFormData.get('currentDeliveredBackgroundColor').value
       || this.projectSettingsFormData.get('deliveredFontColor').value !== this.projectSettingsFormData.get('currentDeliveredFontColor').value
       || this.projectSettingsFormData.get('rejectedBackgroundColor').value !== this.projectSettingsFormData.get('currentRejectedBackgroundColor').value
        || this.projectSettingsFormData.get('rejectedFontColor').value !== this.projectSettingsFormData.get('currentRejectedFontColor').value
         || this.projectSettingsFormData.get('pendingBackgroundColor').value !== this.projectSettingsFormData.get('currentPendingBackgroundColor').value
         || this.projectSettingsFormData.get('pendingFontColor').value !== this.projectSettingsFormData.get('currentPendingFontColor').value
         || this.projectSettingsFormData.get('expiredBackgroundColor').value !== this.projectSettingsFormData.get('currentExpiredBackgroundColor').value
         || this.projectSettingsFormData.get('expiredFontColor').value !== this.projectSettingsFormData.get('currentExpiredFontColor').value) {
        this.projectSettingsFormData.get('isDefaultColor').setValue(false);
      }
    }
  }

  public changeAutoApprovalOrCustom(action): void {
    if (action === 'auto-approve') {
      this.projectSettingsFormData.get('isAutoApprovalEnabled').setValue(true);
    } else if (action === 'custom') {
      this.projectSettingsFormData.get('isAutoApprovalEnabled').setValue(false);
    }
  }

  public requestAutocompleteItems = (text: string): Observable<any> => {
    const param = {
      ProjectId: this.ProjectId,
      search: text,
      ParentCompanyId: this.ParentCompanyId,
    };
    return this.deliveryService.searchNewMemberForAutoApprove(param);
  };

  public bookingSetting(type: any): void {
    if (type === 'delivery') {
      this.projectSettingsFormData
        .get('deliveryAllowOverlappingBooking')
        .setValue(!this.projectSettingsFormData.value.deliveryAllowOverlappingBooking);
    } else if (type === 'deliveryBooking') {
      this.projectSettingsFormData
        .get('deliveryAllowOverlappingCalenderEvents')
        .setValue(!this.projectSettingsFormData.value.deliveryAllowOverlappingCalenderEvents);
    } else if (type === 'crane') {
      this.projectSettingsFormData
        .get('craneAllowOverlappingBooking')
        .setValue(!this.projectSettingsFormData.value.craneAllowOverlappingBooking);
    } else if (type === 'craneBooking') {
      this.projectSettingsFormData
        .get('craneAllowOverlappingCalenderEvents')
        .setValue(!this.projectSettingsFormData.value.craneAllowOverlappingCalenderEvents);
    } else if (type === 'concrete') {
      this.projectSettingsFormData
        .get('concreteAllowOverlappingBooking')
        .setValue(!this.projectSettingsFormData.value.concreteAllowOverlappingBooking);
    } else if (type === 'concreteBooking') {
      this.projectSettingsFormData
        .get('concreteAllowOverlappingCalenderEvents')
        .setValue(!this.projectSettingsFormData.value.concreteAllowOverlappingCalenderEvents);
    } else if (type === 'inspection') {
      this.projectSettingsFormData
        .get('inspectionAllowOverlappingBooking')
        .setValue(!this.projectSettingsFormData.value.inspectionAllowOverlappingBooking);
    } else if (type === 'inspectionBooking') {
      this.projectSettingsFormData
        .get('inspectionAllowOverlappingCalenderEvents')
        .setValue(!this.projectSettingsFormData.value.inspectionAllowOverlappingCalenderEvents);
    }
  }

  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-md approve-popup modal-dialog-centered',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public closeAutoApproveModal(): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.autoApproveFormData.reset();
    this.enabledMemberArray = [];
    this.disabledMemberArray = [];
  }

  public removeMemberFromAutoApproval(memberId): void {
    this.disabledMemberArray.push(memberId);
    const index = this.getautoApprovalMembers.findIndex((obj): any => obj.id === memberId);
    if (index > -1) {
      this.getautoApprovalMembers.splice(index, 1);
    }
  }

  public chooseLegendColor(type: any): void {
    if (type) {
      this.checkbox1 = true;
    } else {
      this.checkbox1 = false;
    }
  }
}
