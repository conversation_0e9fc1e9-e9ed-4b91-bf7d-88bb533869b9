/* eslint-disable max-len */
/* eslint-disable max-lines-per-function */
import {
  Component, OnInit, TemplateRef, AfterViewInit, Input, ElementRef, ViewChild
} from '@angular/core';
import {
  UntypedFormBuilder, UntypedFormGroup, Validators, UntypedFormArray, UntypedFormControl,
} from '@angular/forms';
import { Observable } from 'rxjs';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import moment from 'moment';
import { Router } from '@angular/router';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';
import { MixpanelService } from '../../services/mixpanel.service';
import {
  weekDays,
  recurrence,
  repeatWithSingleRecurrence,
  repeatWithMultipleRecurrence,
} from '../../services/common';
import { TimeslotComponent } from 'src/app/layout/time-slot/time-slot.component';

@Component({
  selector: 'app-new-crane-request-creation-form',
  templateUrl: './new-crane-request-creation-form.component.html',
})
export class NewCraneRequestCreationFormComponent implements OnInit, AfterViewInit {
  @Input() data: any;

  @Input() title: string;

  @Input() currentPage: string;

  @Input() selectfunction: (type: any, date: any)=> void

  @Input() selectedEventDate: any;

  @Input() selectedParams: string;

  @ViewChild('timeSlotsContainer') timeSlotsContainer: ElementRef;

  @ViewChild('timeSlotRef') timeSlotComponent: TimeslotComponent;

  public bookingTypesList: any = ['New Delivery Booking','New Crane Booking' , 'New Concrete Booking', 'New Inspection Booking'];

  public selectedType = 'New Crane Booking';

  public craneRequest: UntypedFormGroup | null;

  public submitted = false;

  public formSubmitted = false;

  public modalLoader = false;

  public ProjectId: any;

  public ParentCompanyId: any;

  public durationisOpen = false; // Controls dropdown visibility

  public timeZone: any ;

  public weekDates : any = [];

  public minutes: number[] = [0 ,15, 30, 45, 60]; // Common minute intervals

  hours = Array.from({ length: 24 }, (_, i) => i)

  public selectedHour: number | null = null;

  public selectedMinute: number | null = 30;

  public selectedMinutes: any;

  public availableTimes = [];

  public selectedTime: string | null = null;

  public isAM = true;

  public selectedDate;

  public newNdrCompanyDropdownSettings: IDropdownSettings;

  public companyList: any = [];

  public locationList: any = [];

  public selectedLocationId: any;

  public locationDropdownSettings: IDropdownSettings;

  public lastId: any;

  public newNdrDefinableDropdownSettings: IDropdownSettings;

  public NDRTimingChanged = false;

  public equipmentList: any = [];

  public definableFeatureOfWorkList: any = [];

  public deliveryEnd: Date;

  public deliveryStart: Date;

  public loader = false;

  public todayDate = new Date();

  public authUser: any = {};

  public editSubmitted = false;

  public responsibleCompanySelectedItems = [];

  public selectedRecurrence = 'Does Not Repeat';

  public recurrence = recurrence;

  public repeatWithSingleRecurrence = repeatWithSingleRecurrence;

  public repeatWithMultipleRecurrence = repeatWithMultipleRecurrence;

  public weekDays: any = weekDays;

  public isRepeatWithMultipleRecurrence = false;

  public isRepeatWithSingleRecurrence = false;

  public showRecurrenceTypeDropdown = false;

  public checkform: any = new UntypedFormArray([]);

  public message = '';

  public monthlyDate = '';

  public monthlyDayOfWeek = '';

  public monthlyLastDayOfWeek = '';

  public enableOption = false;

  public valueExists = [];

  public endTime: Date;

  public startTime: Date;

  public selectedValue;

  public timezoneList: [];

  public selectedTimeZoneValue: any;

  public dropdownSettings: IDropdownSettings;

  public timeZoneValues: any;

  public getSelectedTimeZone;

  public defaultValue;

  public defaultLocationValue;

  public recurrenceMinDate = new Date();

  public defineListData: any;

  public equipmentDropdownSettings: IDropdownSettings;
  gateList: any[];
  endPickerTime: string;

  
  public isSlotsNull = false;
  isTimeSlotChoosen: boolean = false;

  public noEquipmentOption = { id: 0, equipmentName: 'No Equipment Needed' };

  public constructor(
    private readonly formBuilder: UntypedFormBuilder,
    private readonly modalRef: BsModalRef,
    private modalRef1: BsModalRef,
    private readonly deliveryService: DeliveryService,
    private readonly modalService: BsModalService,
    public projectService: ProjectService,
    private readonly toastr: ToastrService,
    public socket: Socket,
    public router: Router,
    private readonly mixpanelService: MixpanelService,
  ) {
    this.selectedValue = this.recurrence[0].value;
    this.projectService.projectParent.subscribe((response7): void => {
      if (response7 !== undefined && response7 !== null && response7 !== '') {
        this.loader = true;
        this.ProjectId = response7.ProjectId;
        this.ParentCompanyId = response7.ParentCompanyId;
        this.loader = true;
        this.getOverAllEquipmentInNewDelivery();
      }
    });
    this.craneRequestCreationForm();
  }

  public chooseRepeatEveryType(value): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    if (value === 'Day' || value === 'Days') {
      this.craneRequest.get('recurrence').setValue('Daily');
    }
    if (value === 'Week' || value === 'Weeks') {
      this.craneRequest.get('recurrence').setValue('Weekly');
    }
    if (value === 'Month' || value === 'Months') {
      this.craneRequest.get('recurrence').setValue('Monthly');
    }
    if (value === 'Year' || value === 'Years') {
      this.craneRequest.get('recurrence').setValue('Yearly');
    }
    if (value === 'Day' || value === 'Days') {
      this.checkform = this.craneRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day: any): any => {
        const dayObj = day;
        dayObj.checked = true;
        dayObj.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj.value));
        return dayObj;
      });
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.showRecurrenceTypeDropdown = true;
    }
    if (value === 'Week' || value === 'Weeks') {
      this.weekDays = this.weekDays.map((day15: any): void => {
        const dayObj15 = day15;
        if (dayObj15.value === 'Monday') {
          dayObj15.checked = true;
        } else {
          dayObj15.checked = false;
        }
        dayObj15.isDisabled = false;
        return dayObj15;
      });
      this.checkform = this.craneRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (value === 'Day' || value === 'Week' || value === 'Month' || value === 'Year') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showRecurrenceTypeDropdown = false;
    }
    if (value === 'Days' || value === 'Weeks' || value === 'Months' || value === 'Years') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.showRecurrenceTypeDropdown = false;
    }
    if (this.craneRequest.get('repeatEveryCount').value > 1) {
      this.showRecurrenceTypeDropdown = true;
      this.isRepeatWithMultipleRecurrence = false;
    }
    this.selectedRecurrence = this.craneRequest.get('recurrence').value;
    this.occurMessage();
  }

  public durationToggleDropdown() {
    this.durationisOpen = !this.durationisOpen;
  }

  public durationCloseDropdown() {
    this.durationisOpen = false;
  }

  getBookingData() {
    const equipmentId = this.craneRequest.get('EquipmentId').value;
    let hasNoEquipmentOption = false;
    let hasOtherEquipment = false;

    if (this.craneRequest.get('EquipmentId').value) {
      if(this.craneRequest.get('EquipmentId').value.length == this.equipmentList.length -1 && this.equipmentList[0].id == 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
      }
      if(this.craneRequest.get('EquipmentId').value.length != this.equipmentList.length && this.equipmentList[0].id != 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
      }
      if(this.craneRequest.get('EquipmentId').value.length == 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
      }
      // Check if "No Equipment Needed" (id = 0) is selected
      hasNoEquipmentOption = this.craneRequest.get('EquipmentId').value.some((item: any) => item.id === 0);

      // Check if other equipment is selected
      hasOtherEquipment = this.craneRequest.get('EquipmentId').value.some((item: any) => item.id !== 0);

      const previousSelection = this.craneRequest.get('EquipmentId').value || [];
      const previousHasOther = previousSelection.some((item: any) => item.id !== 0);

      // Rule 1: If "No Equipment Needed" is selected and other items are selected, keep only "No Equipment Needed"
      if (hasNoEquipmentOption && hasOtherEquipment && !previousHasOther) {
        this.toastr.warning('When "No Equipment Needed" is selected, other equipment options cannot be selected.', 'Warning');
        const noEquipmentOnly = this.craneRequest.get('EquipmentId').value.filter((item: any) => item.id === 0);
        this.craneRequest.get('EquipmentId').setValue(noEquipmentOnly);
        hasOtherEquipment = false;
      }

      // Rule 2: If other equipment is already selected and "No Equipment Needed" is now selected, remove it
      if (previousHasOther && hasNoEquipmentOption) {
        this.toastr.warning('When other equipment is selected, "No Equipment Needed" cannot be selected.', 'Warning');
        const filteredSelection = this.craneRequest.get('EquipmentId').value.filter((item: any) => item.id !== 0);
        this.craneRequest.get('EquipmentId').setValue(filteredSelection);
        hasNoEquipmentOption = false;
      }
  }

    const locationId = this.craneRequest.get('LocationId').value[0].id;
    const gateId = this.craneRequest.get('GateId').value;

    if (this.timeSlotComponent) {
      this.timeSlotComponent.getEventNDR(equipmentId, locationId, gateId, this.timeZone,'');
    }
  }

  public selectHour(hour: number) {
    this.selectedHour = hour;
    const totalMinutes = (this.selectedHour * 60) + this.selectedMinute;
    const deliveryStartValue = this.craneRequest.get('craneDeliveryStart')?.value;
    this.deliveryStart = new Date(deliveryStartValue);
    this.deliveryEnd = new Date(deliveryStartValue); // Start with the same date
    this.deliveryEnd.setMinutes(this.deliveryStart.getMinutes() + totalMinutes);

    this.craneRequest.get('craneDeliveryEnd').setValue(this.deliveryEnd)
    this.endPickerTime = moment(this.deliveryEnd).format('HH:mm');
    this.durationCloseDropdown()
    this.updateDropdownState();
    this.selectDuration(hour)
  }


  public selectMinute(minute: number) {
    this.selectedMinute = minute;
    const totalMinutes = (this.selectedHour * 60) + this.selectedMinute;
    const deliveryStartValue = this.craneRequest.get('craneDeliveryStart')?.value;
    this.deliveryStart = new Date(deliveryStartValue);
    this.deliveryEnd = new Date(deliveryStartValue); // Start with the same date
    this.deliveryEnd.setMinutes(this.deliveryStart.getMinutes() + totalMinutes);

    this.craneRequest.get('craneDeliveryEnd').setValue(this.deliveryEnd)
    this.endPickerTime = moment(this.deliveryEnd).format('HH:mm');
    this.durationCloseDropdown()
    this.updateDropdownState();
    this.selectDuration(minute)
  }

  public updateDropdownState() {
    // Close the dropdown after selecting both hour and minute
    if (this.selectedHour !== null && this.selectedMinutes !== null) {
      this.durationisOpen = false;
    }
  }

  public repeatEveryTypeMassage() {
    if (this.craneRequest.get('repeatEveryType').value === 'Day') {
      this.message = 'Occurs every day';
    }
    if (this.craneRequest.get('repeatEveryType').value === 'Days') {
      if (+this.craneRequest.get('repeatEveryCount').value === 2) {
        this.message = 'Occurs every other day';
      } else {
        this.message = `Occurs every ${this.craneRequest.get('repeatEveryCount').value} days`;
      }
    }
  }

  public occurMessage(): void {
    this.message = '';
    this.repeatEveryTypeMassage();
    if (this.craneRequest.get('repeatEveryType').value === 'Week') {
      let weekDays = '';
      this.weekDays.map((dayObj1: any): any => {
        if (dayObj1.checked) {
          weekDays = `${weekDays + dayObj1.value},`;
        }
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message += `Occurs every ${weekDays}`;
    }
    if (this.craneRequest.get('repeatEveryType').value === 'Weeks') {
      let weekDays = '';
      this.weekDays.map((dayObj2: any): any => {
        if (dayObj2.checked) {
          weekDays = `${weekDays + dayObj2.value},`;
        }
        return false;
      });
      if (+this.craneRequest.get('repeatEveryCount').value === 2) {
        this.message = `Occurs every other  ${weekDays}`;
      } else {
        this.message = `Occurs every ${
          this.craneRequest.get('repeatEveryCount').value
        } weeks on ${weekDays}`;
      }
      weekDays = weekDays.replace(/,\s*$/, '');
    }
    if (
      this.craneRequest.get('repeatEveryType').value === 'Month'
      || this.craneRequest.get('repeatEveryType').value === 'Months'
      || this.craneRequest.get('repeatEveryType').value === 'Year'
      || this.craneRequest.get('repeatEveryType').value === 'Years'
    ) {
      if (this.craneRequest.get('chosenDateOfMonth').value === 1) {
        this.message = `Occurs on day ${this.monthlyDate}`;
      } else if (this.craneRequest.get('chosenDateOfMonth').value === 2) {
        this.message = `Occurs on the ${this.monthlyDayOfWeek}`;
      } else {
        this.message = `Occurs on the ${this.monthlyLastDayOfWeek}`;
      }
    }
    if (this.message) {
      this.message += ` until ${moment(this.craneRequest.get('endDate').value).format(
        'MMMM DD, YYYY',
      )}`;
    }
  }

  public changeRecurrenceCount(value: number): void {
    if (value > 0) {
      const recurrencedata = this.craneRequest.get('recurrence').value;

      this.updateRecurrenceFlags(recurrencedata, value);
      this.updateRepeatEveryType(recurrencedata, value);

      this.selectedRecurrence = recurrencedata;
      this.occurMessage();
    } else if (value < 0) {
      this.craneRequest.get('repeatEveryCount').setValue(1);
    }
  }

  public updateRecurrenceFlags(recurrencedata: string, value: number): void {
    const isSingle = +value === 1;

    if (recurrencedata === 'Daily') {
      this.isRepeatWithSingleRecurrence = isSingle;
      this.isRepeatWithMultipleRecurrence = false;
      this.showRecurrenceTypeDropdown = !isSingle;
    }

    if (recurrencedata === 'Weekly') {
      this.isRepeatWithSingleRecurrence = isSingle;
      this.isRepeatWithMultipleRecurrence = !isSingle;
      this.showRecurrenceTypeDropdown = false;
    }

    if (recurrencedata === 'Monthly' || recurrencedata === 'Yearly') {
      this.isRepeatWithSingleRecurrence = isSingle;
      this.isRepeatWithMultipleRecurrence = !isSingle;
      this.showRecurrenceTypeDropdown = false;
    }
  }

  public updateRepeatEveryType(recurrencedata: string, value: number): void {
    const repeatEveryTypeControl = this.craneRequest.get('repeatEveryType');

    switch (recurrencedata) {
      case 'Daily':
        repeatEveryTypeControl.setValue(value > 1 ? 'Days' : 'Day');
        break;

      case 'Weekly':
        repeatEveryTypeControl.setValue(value > 1 ? 'Weeks' : 'Week');
        break;

      case 'Monthly':
        repeatEveryTypeControl.setValue(value > 1 ? 'Months' : 'Month');
        this.changeMonthlyRecurrence();
        this.showMonthlyRecurrence();
        break;

      case 'Yearly':
        repeatEveryTypeControl.setValue(value > 1 ? 'Years' : 'Year');
        break;

      default:
        break;
    }
  }

  public changeMonthlyRecurrence(): void {
    this.setMonthlyOrYearlyRecurrenceOption();
    this.updateFormValidation();
    this.showMonthlyRecurrence();
    this.occurMessage();
  }

  public setMonthlyOrYearlyRecurrenceOption(): void {
    if (this.craneRequest.get('chosenDateOfMonth').value === 1) {
      this.craneRequest
        .get('dateOfMonth')
        .setValue(moment(this.craneRequest.get('deliveryDate').value).format('DD'));
      this.craneRequest.get('monthlyRepeatType').setValue(null);
    } else if (this.craneRequest.get('chosenDateOfMonth').value === 2) {
      this.craneRequest.get('dateOfMonth').setValue(null);
      this.craneRequest.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
    } else if (this.craneRequest.get('chosenDateOfMonth').value === 3) {
      this.craneRequest.get('dateOfMonth').setValue(null);
      this.craneRequest.get('monthlyRepeatType').setValue(this.monthlyLastDayOfWeek);
    }
  }

  public showMonthlyRecurrence(): void {
    if (this.craneRequest.get('deliveryDate').value) {
      this.generateWeekDates(moment(this.craneRequest.get('deliveryDate').value).format('YYYY-MM-DD'))
      const startDate = moment(this.craneRequest.get('deliveryDate').value).format('YYYY-MM');
      const chosenDay = moment(this.craneRequest.get('deliveryDate').value).format('dddd');
      this.monthlyDate = moment(this.craneRequest.get('deliveryDate').value).format('DD');
      const day = moment(startDate, 'YYYY-MM').startOf('month').day(chosenDay);
      const getAllDays = [];
      if (day.date() > 7) day.add(7, 'd');
      const month = day.month();
      while (month === day.month()) {
        getAllDays.push(day.format('YYYY-MM-DD'));
        day.add(7, 'd');
      }
      let week;
      let extraOption;
      this.enableOption = false;
      getAllDays.forEach((element, i): void => {
        if (
          moment(this.craneRequest.get('deliveryDate').value).format('YYYY-MM-DD')
          === moment(element).format('YYYY-MM-DD')
        ) {
          const number = i + 1;
          if (number === 1) {
            week = 'First';
          }
          if (number === 2) {
            week = 'Second';
          }
          if (number === 3) {
            week = 'Third';
          }
          if (number === 4) {
            this.enableOption = true;
            extraOption = 'Last';
            week = 'Fourth';
          }
          if (number === 5) {
            week = 'Last';
          }
          if (number === 6) {
            week = 'Last';
          }
        }
      });
      this.monthlyDayOfWeek = `${week} ${chosenDay}`;
      this.monthlyLastDayOfWeek = `${extraOption} ${chosenDay}`;
      if (!this.enableOption && this.craneRequest.get('chosenDateOfMonth').value === 3) {
        this.craneRequest.get('chosenDateOfMonth').setValue(2);
        this.craneRequest.get('dateOfMonth').setValue(null);
        this.craneRequest.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
      }
      this.setMonthlyOrYearlyRecurrenceOption();
      this.occurMessage();
      if(this.craneRequest.get('endDate').value){
        this.updateWeeklyDates()
      }
    }
  }

  public updateWeeklyDates() {
    const deliveryEnd = this.craneRequest.get('endDate').value;

    if (deliveryEnd) {
      let endDate = moment(deliveryEnd, 'YYYY-MM-DD');

      let currentDate;
      if (this.weekDates.length > 0) {
        currentDate = moment(this.weekDates[0].fullDate, 'YYYY-MM-DD');
      } else {
        currentDate = moment();
      }

      while (currentDate.isSameOrBefore(endDate)) {
        const exists = this.weekDates.some(weekDate => weekDate.fullDate === currentDate.format('YYYY-MM-DD'));

        if (!exists) {
          this.weekDates.push({
            name: currentDate.format('ddd'),
            date: currentDate.format('DD'),
            fullDate: currentDate.format('YYYY-MM-DD'),
          });
        }

        currentDate.add(1, 'day');
      }
    }
  }

  public updateFormValidation(): void {
    const chosenDateOfMonth = this.craneRequest.get('chosenDateOfMonth');
    const dateOfMonth = this.craneRequest.get('dateOfMonth');
    const monthlyRepeatType = this.craneRequest.get('monthlyRepeatType');
    if (this.craneRequest.get('chosenDateOfMonth').value === 1) {
      dateOfMonth.setValidators([Validators.required]);
      monthlyRepeatType.clearValidators();
    } else {
      monthlyRepeatType.setValidators([Validators.required]);
      dateOfMonth.clearValidators();
    }
    chosenDateOfMonth.updateValueAndValidity();
    dateOfMonth.updateValueAndValidity();
    monthlyRepeatType.updateValueAndValidity();
  }

  public onChange(event): void {
    this.checkform = this.craneRequest.get('days') as UntypedFormArray;
    this.valueExists = this.checkform.controls.filter(
      (object): any => object.value === event.target.value,
    );
    if (event.target.checked) {
      this.checkform.push(new UntypedFormControl(event.target.value));
      this.weekDays = this.weekDays.map((day16: any): void => {
        const dayObj16 = day16;
        if (day16.value === event.target.value) {
          dayObj16.checked = true;
        }
        return dayObj16;
      });
      if (this.checkform.controls.length === 2) {
        this.weekDays = this.weekDays.map((day17: any): void => {
          const dayObj17 = day17;
          dayObj17.isDisabled = false;
          return dayObj17;
        });
      }
    } else if (this.selectedRecurrence === 'Weekly') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day18: any): void => {
            const dayObj18 = day18;
            if (dayObj18.value === event.target.value) {
              dayObj18.checked = false;
            }
            return dayObj18;
          });
        }
        if (this.checkform.controls.length === 1) {
          this.weekDays = this.weekDays.map((day19: any): void => {
            const dayObj19 = day19;
            if (dayObj19.value === this.checkform.controls[0].value) {
              dayObj19.isDisabled = true;
              dayObj19.checked = true;
            }
            return dayObj19;
          });
          return;
        }
        i += 1;
      });
    } else if (this.selectedRecurrence === 'Daily') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day): void => {
            const dayObj = day;
            if (dayObj.value === event.target.value) {
              dayObj.checked = false;
              dayObj.isDisabled = false;
            }
            return dayObj;
          });
          return;
        }
        i += 1;
      });
    }
    if (this.checkform.controls.length !== 7) {
      this.craneRequest.get('recurrence').setValue('Weekly');
      if (+this.craneRequest.get('repeatEveryCount').value === 1) {
        this.craneRequest.get('repeatEveryType').setValue('Week');
      } else {
        this.craneRequest.get('repeatEveryType').setValue('Weeks');
      }
      this.selectedRecurrence = this.craneRequest.get('recurrence').value;
    }
    if (this.checkform.controls.length === 7) {
      this.craneRequest.get('recurrence').setValue('Daily');
      if (+this.craneRequest.get('repeatEveryCount').value === 1) {
        this.craneRequest.get('repeatEveryType').setValue('Day');
      } else {
        this.craneRequest.get('repeatEveryType').setValue('Days');
      }
      this.selectedRecurrence = this.craneRequest.get('recurrence').value;
    }
    this.occurMessage();
  }

  public repeatEveryTypeMessage(value) {
    if (value === 'Does Not Repeat') {
      this.craneRequest.get('repeatEveryType').setValue('');
    } else {
      this.craneRequest.get('repeatEveryCount').setValue(1);
    }
    if (value === 'Daily') {
      this.craneRequest.get('repeatEveryType').setValue('Day');
    }
    if (value === 'Weekly') {
      this.craneRequest.get('repeatEveryType').setValue('Week');
    }
    if (value === 'Monthly') {
      this.craneRequest.get('repeatEveryType').setValue('Month');
    }
    if (value === 'Yearly') {
      this.craneRequest.get('repeatEveryType').setValue('Year');
    }
  }

  public onRecurrenceSelect(value): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    this.selectedRecurrence = value;
    this.repeatEveryTypeMessage(value);
    if (this.craneRequest.get('repeatEveryCount').value > 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.checkform = this.craneRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day11: any): void => {
        const dayObj11 = day11;
        dayObj11.checked = true;
        dayObj11.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj11.value));
        return dayObj11;
      });
    }
    if (this.craneRequest.get('repeatEveryCount').value > 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.weekDays = this.weekDays.map((day12: any): void => {
        const dayObj12 = day12;
        if (dayObj12.value === 'Monday') {
          dayObj12.checked = true;
        } else {
          dayObj12.checked = false;
        }
        dayObj12.isDisabled = false;
        return dayObj12;
      });
      this.checkform = this.craneRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.craneRequest.get('repeatEveryCount').value === 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.weekDays = this.weekDays.map((day13: any): void => {
        const dayObj13 = day13;
        if (dayObj13.value === 'Monday') {
          dayObj13.checked = true;
        } else {
          dayObj13.checked = false;
        }
        dayObj13.isDisabled = false;
        return dayObj13;
      });
      this.checkform = this.craneRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.craneRequest.get('repeatEveryCount').value === 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.checkform = this.craneRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day14: any): void => {
        const dayObj14 = day14;
        dayObj14.checked = true;
        dayObj14.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj14.value));
        return dayObj14;
      });
    }
    if (
      this.craneRequest.get('repeatEveryCount').value === 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.craneRequest.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showMonthlyRecurrence();
    }
    if (
      this.craneRequest.get('repeatEveryCount').value > 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.craneRequest.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.showMonthlyRecurrence();
    }
    this.occurMessage();
  }

  public setDefaultPerson(): void {
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
        let email: string;
        if (this.authUser.User.lastName != null) {
          email = `${this.authUser.User.firstName} ${this.authUser?.User?.lastName} (${this.authUser.User.email})`;
        } else {
          email = `${this.authUser.User.firstName} (${this.authUser.User.email})`;
        }
        const newMemberList = [
          {
            email,
            id: this.authUser.id,
            readonly: true,
          },
        ];
        this.craneRequest.get('responsiblePersons').patchValue(newMemberList);
      }
    });
  }

  public ngAfterViewInit(): void {
    this.getLastCraneRequestId();
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
        this.setDefaultPerson();
        this.getTimeZoneList();
      }
    });
  }

  public getSelectedDate(): void {
    const getData = this.data;
    if (getData) {
      if (getData.date && getData.currentView === 'Month') {
        this.craneRequest
          .get('deliveryDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00'));
        this.craneRequest
          .get('craneDeliveryStart')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00'));
        this.setDefaultDateAndTime(this.craneRequest.get('deliveryDate').value);
      }
      if (
        (getData.date && getData.currentView === 'Week')
        || (getData.date && getData.currentView === 'Day')
      ) {
        this.craneRequest
          .get('deliveryDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00:00'));
        this.craneRequest
          .get('craneDeliveryStart')
          .setValue(moment(getData.date, 'YYYY-MM-DD hh:mm:ss').format());
        this.craneRequest
          .get('craneDeliveryEnd')
          .setValue(moment(getData.date, 'YYYY-MM-DD hh:mm:ss').add(30, 'minutes').format());
      }
    } else {
      this.setDefaultDateAndTime(null);
    }
  }

  public getLastCraneRequestId(): void {
    this.modalLoader = true;
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getLastCraneRequestId(params).subscribe((response): void => {
      this.lastId = response.lastId?.CraneRequestId;
      this.modalLoader = false;
    });
  }

  public getOverAllEquipmentInNewDelivery(): void {
    const newNdrGetEquipmentsParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listCraneEquipment(newNdrGetEquipmentsParams, {
        showActivatedAlone: true,
      })
      .subscribe((equipmentListResponseForNewNdr): void => {
        this.equipmentList = [this.noEquipmentOption, ...equipmentListResponseForNewNdr.data.rows];
        this.equipmentDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'equipmentName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
        this.newNdrgetCompanies();
      });
  }

  public newNdrgetCompanies(): void {
    const newNdrGetCompaniesParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getCompanies(newNdrGetCompaniesParams)
      .subscribe((companiesResponseForNewNdr: any): void => {
        if (companiesResponseForNewNdr) {
          this.companyList = companiesResponseForNewNdr.data;
          const loggedInUser = this.companyList.filter(
            (a: { id: any }): any => a.id === this.authUser.CompanyId,
          );
          this.responsibleCompanySelectedItems = loggedInUser;
          this.getDefinable();
          this.newNdrCompanyDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'companyName',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
        }
      });
  }

  public onItemSelect(): any {}

  public getDefinable(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getDefinableWork(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.definableFeatureOfWorkList = data;
        this.newNdrDefinableDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'DFOW',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          allowSearchFilter: true,
        };
        this.getLocations();
      }
    });
  }

  public getLocations(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getLocations(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.locationList = data;
        this.gateList = data[0].gateDetails;
        this.equipmentList = (data[0]?.EquipmentId || []).filter(
          ({ PresetEquipmentType }) => PresetEquipmentType?.isCraneType,
        );
        this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
        this.timeZone = data[0].TimeZoneId?data[0].TimeZoneId[0].location :''
        this.locationDropdownSettings = {
          singleSelection: true,
          idField: 'id',
          textField: 'locationPath',
          allowSearchFilter: true,
          closeDropDownOnSelection: true,
        };
        this.setDefaultLocationPath();
      }
    });
  }

  public getSlots(){
    this.getBookingData()
  }

  public setDefaultLocationPath(): void {
    if (this.locationList.length > 0) {
      const getChosenLocation: any = this.locationList.filter(
        (obj: any): any => obj.isDefault === true,
      );
      if (getChosenLocation) {
        this.craneRequest.get('LocationId').patchValue({
          id: getChosenLocation[0]?.id,
          locationPath: getChosenLocation[0]?.locationPath,
          isDefault: getChosenLocation[0]?.isDefault,
        });
        this.selectedLocationId = getChosenLocation[0]?.id;
        this.defaultLocationValue = getChosenLocation;
      }
    }
    this.closeModalPopup();
  }

  public closeModalPopup(): void {
    this.modalLoader = false;
  }

  public setDefaultDateAndTime(date): void {
    this.deliveryStart = new Date();
    this.deliveryEnd = new Date();
    if (date) {
      this.deliveryStart = new Date(date);
      this.deliveryEnd = new Date(date);
    }
  }

  public ngOnInit(): void {
    this.craneRequest.get('deliveryDate').setValue(this.selectedEventDate?this.selectedEventDate:moment(this.data.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00'))
    this.craneRequest.get('craneDeliveryStart').setValue(this.selectedEventDate?this.selectedEventDate:moment(this.data.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00'))
    this.router.events.subscribe((e): void => {
      this.modalRef.hide();
    });
  }

  public generateWeekDates(selectedDate: Date | string): void {
    this.weekDates = [];

    for (let i = 0; i < 5; i++) {
      const nextDate = moment(selectedDate).add(i, 'days'); // Get the next 5 dates

      const dayName = nextDate.format('ddd'); // Get short day name like 'Wed'
      const day = nextDate.format('DD'); // Extract the day (e.g., '05')
      const fullDate = nextDate.format('YYYY-MM-DD'); // Full date like '2024-11-06'

      this.weekDates.push({
        name: dayName,
        date: day,
        fullDate: fullDate,
      });
    }
    this.selectedDate = this.weekDates[0]
  }


  public selectDate(day: any) {
    this.selectedDate = day;
    this.craneRequest
    .get('deliveryDate')
    .setValue(moment(this.selectedDate.fullDate).format('MM/DD/YYYY'));
    this.getAvailableSlots(day.fullDate)
  }

  public getAvailableSlots(date){
    const payload = {
      date,
      equipmentId: this.craneRequest.get('EquipmentId').value.map((data:any)=>{
        return data.id
      }),
      timeZone: this.timeZone?this.timeZone:'',
      duration: this.selectedMinutes?this.selectedMinutes : '',
      GateId: this.craneRequest.get('GateId').value ? this.craneRequest.get('GateId').value : '',
      LocationId: this.craneRequest.get('LocationId').value[0].id,
      bookingType : 'crane'
    }
      this.deliveryService.getAvailableTimeSlots(this.ProjectId,payload).subscribe((res)=>{
        if (res !== undefined && res !== null && res !== '') {
          this.availableTimes = res.slots
        }
        if(res.slots.AM.length === 0 && res.slots.PM.length === 0){
          this.isSlotsNull = true
        } else{
          this.isSlotsNull = false
        }
      })
  }

  public requestAutocompleteItems = (text: string): Observable<any> => {
    const param = {
      ProjectId: this.ProjectId,
      search: text,
      ParentCompanyId: this.ParentCompanyId,
    };
    return this.deliveryService.searchNewMember(param);
  };

  public selectAM() {
    this.isAM = true;
  }

  public selectPM() {
    this.isAM = false;
  }


  public selectTime(startStr: string, endStr: string) {
    const startDate = new Date(startStr);
    const endDate = new Date(endStr);

    this.craneRequest.patchValue({
      craneDeliveryStart: startDate,
      craneDeliveryEnd: endDate,
      deliveryDate: startDate
    });

    this.isTimeSlotChoosen = true

    this.selectedTime = `${startDate.toLocaleTimeString()} - ${endDate.toLocaleTimeString()}`;
  }

  public scrollToTime(index: number) {
    const container = this.timeSlotsContainer.nativeElement;
    const buttons = container.querySelectorAll('button');

    if (buttons[index]) {
      const selectedButton = buttons[index];
      container.scrollTop = selectedButton.offsetTop - container.offsetTop;
    }
  }

  public numberOnly(event: { which: any; keyCode: any }): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public deliveryEndTimeChangeDetection(): void {
    this.NDRTimingChanged = true;
  }

  public changeDate(event: any): void {
    if (!this.modalLoader) {
      const startTime = new Date(event).getHours();
      const minutes = new Date(event).getMinutes();
      this.deliveryEnd = new Date();
      this.deliveryEnd.setHours(startTime + 1);
      this.deliveryEnd.setMinutes(minutes);
      this.craneRequest.get('craneDeliveryEnd').setValue(this.deliveryEnd);
      this.NDRTimingChanged = true;
    }
  }

  public selectDuration(event){
    this.selectedMinutes = (this.selectedHour * 60) + this.selectedMinute
    const initialDeliveryDate =  moment(this.craneRequest.get('craneDeliveryStart').value).format('YYYY-MM-DD');
    this.getAvailableSlots(initialDeliveryDate);
  }

  public close(template: TemplateRef<any>): void {
    if (
      (this.craneRequest.touched && this.craneRequest.dirty)
      || (this.craneRequest.get('definableFeatureOfWorks').dirty
        && this.craneRequest.get('definableFeatureOfWorks').value
        && this.craneRequest.get('definableFeatureOfWorks').value.length > 0)
      || (this.craneRequest.get('companies').dirty
        && this.craneRequest.get('companies').value
        && this.craneRequest.get('companies').value.length > 0)
      || (this.craneRequest.get('EquipmentId').dirty
        && this.craneRequest.get('EquipmentId').value
        && this.craneRequest.get('EquipmentId').value.length > 0)
      || this.NDRTimingChanged
    ) {
      this.openConfirmationModalPopup(template);
    } else {
      this.resetForm('yes');
    }
  }

  public openConfirmationModalPopup(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.craneRequest.reset();
      this.setDefaultPerson();
      this.submitted = false;
      this.formSubmitted = false;
      this.editSubmitted = false;
      this.modalRef.hide();
      this.NDRTimingChanged = false;
    }
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    const companies = [];
    const persons = [];
    const define = [];
    const equipments = [];
    const gates = [];
    if (this.craneRequest.invalid) {
      this.formSubmitted = false;
      return;
    }
    const formValue = this.craneRequest.value;
    if (formValue.EquipmentId.length <= 0) {
      this.toastr.error('Equipment is required');
      this.formSubmitted = false;
      return;
    }
    const newTimeZoneDetails = formValue.TimeZoneId;
    const deliveryDate = new Date(formValue.deliveryDate);
    const EndDate = new Date(formValue.endDate);
    const startHours = new Date(formValue.craneDeliveryStart).getHours();
    const startMinutes = new Date(formValue.craneDeliveryStart).getMinutes();
    const deliveryStart = this.convertStart(deliveryDate, startHours, startMinutes);
    const endHours = new Date(formValue.craneDeliveryEnd).getHours();
    const endMinutes = new Date(formValue.craneDeliveryEnd).getMinutes();
    const startPicker = moment(formValue.craneDeliveryStart).format('HH:mm');
    const endPicker = moment(formValue.craneDeliveryEnd).format('HH:mm');
    this.endPickerTime = moment(formValue.craneDeliveryEnd).format('HH:mm');
    const weekStartDate = moment(formValue.deliveryDate).format('YYYY MM DD 00:00:00');
    const weekEndDate = formValue.recurrence !== 'Does Not Repeat'
      ? moment(formValue.endDate).format('YYYY MM DD 00:00:00')
      : moment(formValue.deliveryDate).format('YYYY MM DD 00:00:00');
    const deliveryEnd = formValue.recurrence !== 'Does Not Repeat'
      ? this.convertStart(EndDate, endHours, endMinutes)
      : this.convertStart(deliveryDate, endHours, endMinutes);

    this.createDelivery({
      newNdrFormValue: formValue,
      deliveryStart,
      deliveryEnd,
      companies,
      responsbilePersonsData: persons,
      definableFeatureOfWorkData: define,
      newTimeZoneDetails,
      startPicker,
      endPicker,
      weekStartDate,
      weekEndDate,
      equipments,
    });
  }

  public convertStart(deliveryDate: Date, startHours: number, startMinutes: number): string {
    const fullYear = deliveryDate.getFullYear();
    const fullMonth = deliveryDate.getMonth();
    const date = deliveryDate.getDate();
    const deliveryNewStart = new Date(fullYear, fullMonth, date, startHours, startMinutes);
    const deliveryStart = deliveryNewStart.toUTCString();
    return deliveryStart;
  }

  public checkNewDeliveryStartEnd(
    newDeliveryStart: string | number | Date,
    newDeliveryEnd: string | number | Date,
  ): boolean {
    const newStartDate = new Date(newDeliveryStart).getTime();
    const newEndDate = new Date(newDeliveryEnd).getTime();
    if (newStartDate < newEndDate) {
      return true;
    }
    return false;
  }

  public constructPayload({
    newNdrFormValue,
    companies,
    equipments,
    deliveryStart,
    deliveryEnd,
    responsbilePersonsData,
    definableFeatureOfWorkData,
    startPicker,
    endPicker,
  }) {
    const escortCondition = newNdrFormValue.isEscortNeeded === null
                  || newNdrFormValue.isEscortNeeded === undefined;
    const payload = {
      description: newNdrFormValue.description,
      companies,
      isEscortNeeded: escortCondition ? false : newNdrFormValue.isEscortNeeded,
      ProjectId: this.ProjectId,
      additionalNotes: newNdrFormValue.additionalNotes,
      EquipmentId: equipments,
      GateId: newNdrFormValue.GateId,
      LocationId: this.selectedLocationId,
      craneDeliveryStart: deliveryStart,
      craneDeliveryEnd: deliveryEnd,
      ParentCompanyId: this.ParentCompanyId,
      responsiblePersons: responsbilePersonsData,
      definableFeatureOfWorks: definableFeatureOfWorkData,
      isAssociatedWithDeliveryRequest: false,
      pickUpLocation: newNdrFormValue.pickUpLocation,
      dropOffLocation: newNdrFormValue.dropOffLocation,
      recurrence: newNdrFormValue.recurrence,
      chosenDateOfMonth: newNdrFormValue.chosenDateOfMonth === 1,
      dateOfMonth: newNdrFormValue.dateOfMonth,
      monthlyRepeatType: newNdrFormValue.monthlyRepeatType,
      days: newNdrFormValue.days ? this.sortWeekDays(newNdrFormValue.days) : [],
      repeatEveryType: newNdrFormValue.repeatEveryType
        ? newNdrFormValue.repeatEveryType
        : null,
      repeatEveryCount: newNdrFormValue.repeatEveryCount
        ? newNdrFormValue.repeatEveryCount.toString()
        : null,
      TimeZoneId: this.timeZoneValues,
      startPicker,
      endPicker,
      chosenDateOfMonthValue: newNdrFormValue.chosenDateOfMonth !== false ? newNdrFormValue.chosenDateOfMonth : 1,

    };
    return payload;
  }

  public createDelivery(params: {
    newNdrFormValue: any;
    deliveryStart: string;
    deliveryEnd: string;
    companies: any[];
    responsbilePersonsData: any[];
    definableFeatureOfWorkData: any[];
    newTimeZoneDetails: any;
    startPicker: any;
    endPicker: any;
    weekStartDate: any;
    weekEndDate: any;
    equipments: any[];
  }): void {
    const success = this.prepareDeliveryData(params);
    if (!success) return;
    const {
      newNdrFormValue,
      companies,
      responsbilePersonsData,
      definableFeatureOfWorkData,
      deliveryStart,
      deliveryEnd,
      startPicker,
      endPicker,
      weekStartDate,
      weekEndDate,
      equipments,
    } = params;
    if (!this.checkStringEmptyValues(newNdrFormValue)) {
      const payload = this.constructPayload({
        newNdrFormValue,
        companies,
        equipments,
        deliveryStart,
        deliveryEnd,
        responsbilePersonsData,
        definableFeatureOfWorkData,
        startPicker,
        endPicker,
      });

      if (payload.recurrence === 'Monthly' || payload.recurrence === 'Yearly') {
        payload.dateOfMonth = this.monthlyDate;
      }

      if (payload.recurrence) {
        payload.startPicker = startPicker;
        payload.endPicker = endPicker;
        payload.craneDeliveryStart = weekStartDate;
        payload.craneDeliveryEnd = weekEndDate;

        if (!this.validateRecurrencePayload(payload)) {
          return;
        }
      }
      this.createNDR(payload);
    } else {
      this.formReset();
    }
  }

  public validateRecurrencePayload(payload) {
    if (payload.startPicker === payload.endPicker) {
      this.showErrorMessage('Delivery Start time and End time should not be the same');
      return false;
    }

    if (payload.startPicker > payload.endPicker) {
      this.showErrorMessage('Please enter From Time lesser than To Time');
      return false;
    }

    if (
      payload.recurrence !== 'Does Not Repeat'
      && new Date(payload.craneDeliveryEnd) <= new Date(payload.craneDeliveryStart)
    ) {
      this.showErrorMessage('Please enter End Date greater than Start Date');
      return false;
    }

    return true;
  }

  public showErrorMessage(message: string) {
    this.toastr.error(message);
    this.formSubmitted = false;
    this.submitted = false;
  }

  public prepareDeliveryData(params: {
    newNdrFormValue: any;
    companies: any[];
    responsbilePersonsData: any[];
    definableFeatureOfWorkData: any[];
    newTimeZoneDetails: any;
    equipments: any[];
  }): boolean {
    const {
      newNdrFormValue,
      companies,
      responsbilePersonsData,
      definableFeatureOfWorkData,
      newTimeZoneDetails,
      equipments,
    } = params;

    const newNdrCompanyDetails = newNdrFormValue.companies;
    const newNdrPersonDetails = newNdrFormValue.responsiblePersons;
    const newNdrDefinableDetails = newNdrFormValue.definableFeatureOfWorks;

    if (!newNdrCompanyDetails || newNdrCompanyDetails.length === 0) {
      this.formReset();
      this.toastr.error('Responsible Company is required');
      return false;
    }

    if (!newNdrPersonDetails || newNdrPersonDetails.length === 0) {
      this.formReset();
      this.toastr.error('Responsible Person is required');
      return false;
    }

    newNdrCompanyDetails.forEach((element: { id: any }) => companies.push(element.id));
    newNdrPersonDetails.forEach((element: { id: any }) => responsbilePersonsData.push(element.id));

    if (newNdrDefinableDetails?.length > 0) {
      newNdrDefinableDetails.forEach(
        (element: { id: any }) => definableFeatureOfWorkData.push(element.id),
      );
    }

    if (newTimeZoneDetails?.length > 0 && newNdrFormValue.TimeZoneId?.length > 0) {
      newNdrFormValue.TimeZoneId.forEach((element: { id: any }) => {
        this.timeZoneValues = element.id;
      });
    }

    if (newNdrFormValue.EquipmentId?.length > 0) {
      newNdrFormValue.EquipmentId.forEach((element: { id: any }) => {
        equipments.push(element.id);
      });
    }

    return true;
  }

  public checkStringEmptyValues(formValue: { description: string; notes: string }): boolean {
    if (formValue.description.trim() === '') {
      this.toastr.error('Please Enter valid Company Name.', 'OOPS!');
      return true;
    }
    if (formValue.notes) {
      if (formValue.notes.trim() === '') {
        this.toastr.error('Please Enter valid address.', 'OOPS!');
        return true;
      }
    }
    return false;
  }

  public sortWeekDays(data: any[]): any {
    const order = {
      Sunday: 1,
      Monday: 2,
      Tuesday: 3,
      Wednesday: 4,
      Thursday: 5,
      Friday: 6,
      Saturday: 7,
    };

    if (data.length > 0) {
      return data.sort((a, b): any => order[a] - order[b]);
    }
  }

  public createNDR(payload: {
    description: any;
    companies: any;
    isEscortNeeded: any;
    ProjectId: any;
    additionalNotes: any;
    EquipmentId: any;
    LocationId: any;
    craneDeliveryStart: any;
    craneDeliveryEnd: any;
    ParentCompanyId: any;
    responsiblePersons: any;
    definableFeatureOfWorks: any;
    isAssociatedWithDeliveryRequest: boolean;
    pickUpLocation: any;
    dropOffLocation: any;
    recurrence: any;
    chosenDateOfMonth: any;
    dateOfMonth: any;
    monthlyRepeatType: any;
    days: any;
    repeatEveryType: any;
    repeatEveryCount: any;
  }): void {
    this.deliveryService.createCraneRequest(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.mixpanelService.addMixpanelEvents('Created New Crane Booking');
          this.socket.emit('CraneCreateHistory', response);
          this.formReset();
          this.craneRequest.reset();
          this.deliveryService.updateCraneRequestHistory({ status: true }, 'CraneCreateHistory');
          this.resetForm('yes');
          this.NDRTimingChanged = false;
        }
      },
      error: (NDRCreateHistoryError): void => {
        this.formReset();
        this.NDRTimingChanged = false;
        if (NDRCreateHistoryError.message?.statusCode === 400) {
          this.showError(NDRCreateHistoryError);
        } else if (!NDRCreateHistoryError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else if (NDRCreateHistoryError.message.includes('overlaps')) {
          this.toastr.error(NDRCreateHistoryError.message, 'Alert!');
        } else {
          this.toastr.error(NDRCreateHistoryError.message, 'OOPS!');
        }
      },
    });
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public formReset(): void {
    this.formSubmitted = false;
    this.submitted = false;
  }

  public craneRequestCreationForm(): void {
    this.craneRequest = this.formBuilder.group({
      EquipmentId: [this.formBuilder.array([])],
      LocationId: ['', Validators.compose([Validators.required])],
      GateId: ['', Validators.compose([Validators.required])],
      additionalNotes: [''],
      responsiblePersons: ['', Validators.compose([Validators.required])],
      description: ['', Validators.compose([Validators.required])],
      deliveryDate: ['', Validators.compose([Validators.required])],
      craneDeliveryStart: ['', Validators.compose([Validators.required])],
      craneDeliveryEnd: ['', Validators.compose([Validators.required])],
      isEscortNeeded: [false],
      companies: [this.formBuilder.array([])],
      definableFeatureOfWorks: [this.formBuilder.array([])],
      pickUpLocation: ['', Validators.compose([Validators.required])],
      dropOffLocation: ['', Validators.compose([Validators.required])],
      isAssociatedWithDeliveryRequest: [false, Validators.compose([Validators.required])],
      recurrence: [''],
      repeatEveryCount: [''],
      repeatEveryType: [''],
      days: new UntypedFormArray([]),
      chosenDateOfMonth: [false, ''],
      dateOfMonth: [''],
      monthlyRepeatType: [''],
      endDate: [''],
      TimeZoneId: [this.formBuilder.array([]), Validators.compose([Validators.required])],
    });

    this.craneRequest.get('isEscortNeeded').setValue(false);
    const newDate = moment().format('MM/DD/YYYY');
    this.craneRequest.get('deliveryDate').setValue(newDate);
    this.craneRequest.get('craneDeliveryStart').setValue(this.deliveryStart);
    this.craneRequest.get('craneDeliveryEnd').setValue(this.deliveryEnd);
    this.craneRequest.get('recurrence').setValue(this.selectedValue);
    this.formControlValueChanged();
    this.setCurrentTiming();
    if (this.selectedValue === 'Does Not Repeat') {
      this.craneRequest.get('repeatEveryType').setValue('');
    } else {
      this.craneRequest.get('repeatEveryCount').setValue(1);
    }
  }

  public formControlValueChanged(): void {
    this.craneRequest.get('repeatEveryType').valueChanges.subscribe((value: string): void => {
      const days = this.craneRequest.get('days');
      const chosenDateOfMonth = this.craneRequest.get('chosenDateOfMonth');
      const dateOfMonth = this.craneRequest.get('dateOfMonth');
      const monthlyRepeatType = this.craneRequest.get('monthlyRepeatType');
      if (value === 'Week' || value === 'Day' || value === 'Weeks') {
        days.setValidators([Validators.required]);
      } else {
        days.clearValidators();
      }
      if (value === 'Month' || value === 'Months' || value === 'Year' || value === 'Years') {
        if (this.craneRequest.get('chosenDateOfMonth').value === 1) {
          dateOfMonth.setValidators([Validators.required]);
          monthlyRepeatType.clearValidators();
        } else {
          monthlyRepeatType.setValidators([Validators.required]);
          dateOfMonth.clearValidators();
        }
      } else {
        chosenDateOfMonth.clearValidators();
        dateOfMonth.clearValidators();
        monthlyRepeatType.clearValidators();
      }
      chosenDateOfMonth.updateValueAndValidity();
      dateOfMonth.updateValueAndValidity();
      monthlyRepeatType.updateValueAndValidity();
      days.updateValueAndValidity();
    });
  }

  public selectedBookingtype(type): void {
    if(type){
      this.selectedParams = type
      this.selectfunction(this.selectedParams , this.craneRequest.get('deliveryDate').value)
    }
  }

  public setCurrentTiming(): void {
    const newDate = moment().format('MM-DD-YYYY');
    const hours = moment(new Date()).format('HH');
    this.startTime = new Date();
    this.startTime.setHours(+hours);
    this.startTime.setMinutes(0);
    this.endTime = new Date();
    this.endTime.setHours(+hours);
    this.endTime.setMinutes(30);
    if (!this.craneRequest.get('endDate')?.value) {
      this.craneRequest.get('endDate').setValue(newDate);
    }
    this.changeMonthlyRecurrence();
  }

  public timeZoneSelected(id): void {
    this.selectedTimeZoneValue = this.timezoneList.find((obj: any): any => +obj.id === +id);
  }

  public getTimeZoneList(): void {
    this.projectService.getTimeZoneList().subscribe({
      next: (response: any): void => {
        this.loader = true;
        if (response) {
          const params = {
            ProjectId: this.ProjectId,
          };
          if (params.ProjectId) {
            this.projectService.getSingleProject(params).subscribe((projectList: any): void => {
              if (projectList) {
                this.timezoneList = response.data;
                this.dropdownSettings = {
                  singleSelection: true,
                  idField: 'id',
                  textField: 'location',
                  allowSearchFilter: true,
                  closeDropDownOnSelection: true,
                };
                this.getSelectedTimeZone = this.timezoneList.filter(
                  (obj: any): any => +obj.id === +projectList.data.TimeZoneId,
                );
                this.craneRequest.get('TimeZoneId').patchValue(this.getSelectedTimeZone);
                this.defaultValue = this.getSelectedTimeZone;
                this.loader = false;
              }
            });
          }
        }
      },
      error: (getTimeZoneListErr): void => {
        if (getTimeZoneListErr.message?.statusCode === 400) {
          this.showError(getTimeZoneListErr);
        } else if (!getTimeZoneListErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(getTimeZoneListErr.message, 'OOPS!');
        }
      },
    });
  }

  public locationSelected(data): void {
    const getChosenLocation = this.locationList.find((obj: any): any => +obj.id === +data.id);
    if (getChosenLocation) {
      this.craneRequest.get('GateId').setValue('');
      this.craneRequest
        .get('EquipmentId')
        .setValue('');
      this.gateList = []
      this.equipmentList = []
      if(getChosenLocation.gateDetails){
        this.gateList = getChosenLocation.gateDetails;
        this.equipmentList = [this.noEquipmentOption, ...getChosenLocation.EquipmentId];
        this.timeZone = getChosenLocation.TimeZoneId[0].location
      }
    }
    this.selectedLocationId = getChosenLocation?.id;
  }

}
