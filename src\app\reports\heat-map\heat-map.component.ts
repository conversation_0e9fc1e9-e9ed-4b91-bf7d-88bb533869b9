import moment from 'moment';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Component, OnInit, TemplateRef } from '@angular/core';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import * as _ from 'lodash';
import { ToastrService } from 'ngx-toastr';
import { ReportsService } from '../../services/reports/reports.service';
import { ProjectService } from '../../services/profile/project.service';
import { SchedulerFormComponent } from '../scheduler-form/scheduler-form.component';
import { SaveReportFormComponent } from '../save-report-form/save-report-form.component';

@Component({
  selector: 'app-heatmap',
  templateUrl: './heat-map.component.html',
})
export class HeatmapComponent implements OnInit {
  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public modalRef2: BsModalRef;

  public scheduleFormModalRef: BsModalRef;

  public filterForm: UntypedFormGroup;

  public pageSize = 10;

  public currentPageNo = 1;

  public pageNo = 1;

  public sortOrder = 'asc';

  public items = [
    { display: 'Pizza', value: 1 },
    { display: 'Pasta', value: 2 },
    { display: 'Parmesan', value: 3 },
  ];

  public ProjectId: any;

  public heatMapReportList: any[];

  public defineListData: any;

  public defaultRequestType: any;

  public formSubmitted: boolean;

  public filterCount = 0;

  public ParentCompanyId: number;

  public newRequestTypeDropdownSettings: IDropdownSettings;

  public equipmentList: any = [];

  public companyList: any = [];

  public defineList: any = [];

  public totalListCount: number = 0;

  public memberList: any = [];

  public gateList: any = [];

  public wholeStatus = ['Approved', 'Declined', 'Delivered', 'Pending', 'Expired'];

  public selectedArray = [];

  public loader = true;

  public deliveryStart: Date;

  public deliveryEnd: Date;

  public exportSubmitted = false;

  public reportType = 'PDF';

  public reportName = 'Heat Map Report';

  public exportForm: UntypedFormGroup;

  public updatedMemberList: any = [];

  public exportType: any = ['PDF'];

  public scheduleData = {};

  public filterPayload: any;

  public locationList: any = [];

  public constructor(
    private readonly modalService: BsModalService,
    public projectService: ProjectService,
    private readonly reportsService: ReportsService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly toastr: ToastrService,
  ) {
    this.filterDetailsForm();
    this.exportDetailsForm();
    this.setDefaultDateAndTime();
    this.setDefaultDateRangePicker();
    this.defaultFilterSettings();
    this.projectService.projectParent.subscribe((response19): void => {
      if (response19 !== undefined && response19 !== null && response19 !== '') {
        this.ProjectId = response19.ProjectId;
        this.ParentCompanyId = response19.ParentCompanyId;
        this.heatMapReports();
      }
    });
  }

  public handleToggleKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortData();
    }
  }

  public changePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.pageNo = 1;
    this.heatMapReports();
  }

  public changePageNo(pageNo: number): void {
    this.pageNo = pageNo;
    this.heatMapReports();
  }

  public ngAfterViewInit(): void {
    this.projectService.projectParent.subscribe((response): void => {
      if (response !== undefined && response !== null && response !== '') {
        this.ParentCompanyId = response.ParentCompanyId;
        this.ProjectId = response.ProjectId;
        this.getMembers();
        this.getCompaniesForNdrGrid();
        this.getOverAllGateForNdrGrid();
      }
    });
  }

  public openModal(template: TemplateRef<any>): void {
    this.modalRef = this.modalService.show(template, {
      class: 'heatmap-modal',
    });
  }

  public openModalExport(template: TemplateRef<any>): void {
    this.modalRef2 = this.modalService.show(template, {
      class: 'reportexport-modal radius20',
    });
  }

  public openModalSave(): void {
    const modalRef = this.modalService.show(SaveReportFormComponent, { class: 'modal-save' });
    modalRef.content.ProjectId = this.ProjectId;
    modalRef.content.reportType = 'Heat Map';
    modalRef.content.updatedMemberList = this.updatedMemberList;
    modalRef.content.exportType = this.exportType;
    modalRef.content.filterPayload = this.filterPayload;
    modalRef.content.pageSize = this.pageSize;
    modalRef.content.pageNo = this.pageNo;
    modalRef.content.sortOrder = this.sortOrder;
    modalRef.content.void = 0;
  }

  public openSchedulePopup(): void {
    this.scheduleFormModalRef = this.modalService.show(SchedulerFormComponent);
    this.scheduleFormModalRef.content.ProjectId = this.ProjectId;
    this.scheduleFormModalRef.content.ParentCompanyId = this.ParentCompanyId;
    this.scheduleFormModalRef.content.reportType = 'Heat Map';
    this.scheduleFormModalRef.content.updatedMemberList = this.updatedMemberList;
    this.scheduleFormModalRef.content.exportType = this.exportType;
    this.scheduleFormModalRef.content.filterPayload = this.filterPayload;
    this.scheduleFormModalRef.content.sort = this.sortOrder;
  }

  public ngOnInit(): void { /* */ }

  public resetFilter(): void {
    if (this.filterForm.invalid) {
      this.formSubmitted = true;
    }
    this.modalRef.hide();
    this.filterForm.reset();
    this.filterDetailsForm();
    this.filterCount = 0;
    this.filterForm.get('templateType').setValue(this.defaultRequestType);
    this.setDefaultDateAndTime();
    this.setDefaultDateRangePicker();
    this.defaultFilterSettings();
    this.heatMapReports();
  }

  public numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public getOverAllEquipmentForNdrGrid(): void {
    const getOverAllEquipmentForNdrGridParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listEquipment(getOverAllEquipmentForNdrGridParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((getOverAllEquipmentForNdrGridResponse): void => {
        this.equipmentList = getOverAllEquipmentForNdrGridResponse.data;
        this.getDefinableForNdrGrid();
      });
  }

  public getDefinableForNdrGrid(): void {
    const getDefinableForNdrGridParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getDefinableWork(getDefinableForNdrGridParams)
      .subscribe((getDefinableForNdrGridResponse: any): void => {
        if (getDefinableForNdrGridResponse) {
          const { data } = getDefinableForNdrGridResponse;
          this.defineList = data;
          this.getCompaniesForNdrGrid();
        }
      });
  }

  public getCompaniesForNdrGrid(): void {
    const getCompaniesForNdrGridParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getCompanies(getCompaniesForNdrGridParams)
      .subscribe((getCompaniesForNdrGridResponse: any): void => {
        if (getCompaniesForNdrGridResponse) {
          this.companyList = getCompaniesForNdrGridResponse.data;
          this.getLocationsForNdrGrid();
        }
      });
  }

  public filterSubmit(): void {
    this.formSubmitted = true;
    this.filterCount = 0;
    if (this.filterForm.get('dateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('companyFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('memberFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('gateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('equipmentFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('statusFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('defineFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('startTime').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('templateType').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('locationFilter').value !== '') {
      this.filterCount += 1;
    }
    this.heatMapReports();
    this.modalRef.hide();
    this.formSubmitted = false;
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      templateType: [''],
      startTime: [''],
      endTime: [''],
      companyFilter: [''],
      descriptionFilter: [''],
      statusFilter: [''],
      dateFilter: [''],
      memberFilter: [''],
      gateFilter: [''],
      equipmentFilter: [''],
      defineFilter: [''],
      locationFilter: [''],
    });

    this.defineListData = [
      { id: 0, name: 'Delivery' },
      { id: 1, name: 'Crane' },
      { id: 2, name: 'Concrete' },
      { id: 3, name: 'Inspection' },
    ];
    this.defaultRequestType = this.defineListData.filter(
      (a: { name: any }): any => a.name === 'Delivery',
    );

    this.newRequestTypeDropdownSettings = {
      singleSelection: false,
      idField: 'id',
      textField: 'name',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 6,
      allowSearchFilter: false,
    };
    this.filterForm.get('templateType').setValue(this.defaultRequestType);
  }

  public heatMapReports(): void {
    this.heatMapReportList = [];
    this.selectedArray = [];

    if (!this.hasSelectedTemplateType()) {
      this.showTemplateTypeError();
      return;
    }

    this.loader = true;
    const params = this.buildParams();
    const payload = this.buildPayload();

    if (this.isSingleDayWithInvalidTime(payload)) {
      this.loader = false;
      this.formSubmitted = false;
      return;
    }

    this.selectedArray.push(payload);
    this.filterPayload = payload;
    this.getHeatMapReports(params, payload);
  }

  public hasSelectedTemplateType(): boolean {
    return this.filterForm.value.templateType.length > 0;
  }

  public showTemplateTypeError(): void {
    this.formSubmitted = false;
    this.loader = false;
    this.toastr.error('Please choose any one of the booking type');
  }

  public buildParams(): any {
    return {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.pageNo,
      sortOrder: this.sortOrder,
      void: 0,
    };
  }

  public buildPayload(): any {
    const formValue = this.filterForm.value;
    const startDate = formValue.dateFilter?.[0]
      ? moment(formValue.dateFilter[0]).format('YYYY-MM-DD')
      : formValue.dateFilter;
    const endDate = formValue.dateFilter?.[1]
      ? moment(formValue.dateFilter[1]).format('YYYY-MM-DD')
      : formValue.dateFilter;

    return {
      companyFilter: +formValue.companyFilter,
      startDate,
      endDate,
      statusFilter: formValue.statusFilter || '',
      memberFilter: +formValue.memberFilter || 0,
      gateFilter: +formValue.gateFilter || 0,
      equipmentFilter: +formValue.equipmentFilter === null || formValue.equipmentFilter === '' ? null : +formValue.equipmentFilter,
      templateType: formValue.templateType,
      defineFilter: +formValue.defineFilter || 0,
      startTime: formValue.startTime ? moment(formValue.startTime).format('HH:mm') : '07:00',
      endTime: formValue.endTime ? moment(formValue.endTime).format('HH:mm') : '17:00',
      ParentCompanyId: this.ParentCompanyId,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      eventStartTime: formValue.startTime,
      eventEndTime: formValue.endTime,
      locationFilter: +formValue.locationFilter,
    };
  }

  public isSingleDayWithInvalidTime(payload: any): boolean {
    if (
      payload.startDate
      && payload.endDate
      && payload.startDate === payload.endDate
    ) {
      if (payload.eventStartTime === payload.eventEndTime) {
        this.toastr.error('Time Range Start time and End time should not be the same');
        return true;
      }
      if (payload.eventStartTime > payload.eventEndTime) {
        this.toastr.error('Please enter Time Range From Time lesser than To Time');
        return true;
      }
    }
    return false;
  }


  public getHeatMapReports(params, payload): void {
    this.reportsService.heatMapReports(params, payload).subscribe({
      next: (response: any): void => {
        this.loader = false;
        if (response) {
          if (response?.data) {
            this.heatMapReportList = Object.entries(response.data.result).map(
              ([date, { timeslots, totalCount }]: [
                string,
                { timeslots: {}; totalCount: number },
              ]): any => ({
                date,
                timeslots: Object.values(timeslots),
                totalCount,
              }),
            );
            this.totalListCount = response.data.count;
          }
        }
      },
      error: (error): void => {
        this.loader = false;
        this.toastr.error('Error fetching heat map reports');
      }
    });
  }

  public sortData(): void {
    if (this.sortOrder === 'asc') {
      this.sortOrder = 'desc';
    } else {
      this.sortOrder = 'asc';
    }
    this.heatMapReports();
  }

  public getOpacity(item: number): string {
    const maxCount = _.max(
      _.flatMap(_.map(_.values(this.heatMapReportList), 'timeslots'), _.values),
    );
    const max = maxCount;
    const opacity = item === 0 ? '#e3e2e3' : `rgba(244, 94, 40, ${item / max})`;

    return opacity.toString();
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
        this.updatedMemberList = this.memberList.map((item): any => {
          return {
            ...item,
            UserEmail: item.User.email,
          };
        });
      }
    });
  }

  public getOverAllGateForNdrGrid(): void {
    const getOverAllGateForNdrGridParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .gateList(getOverAllGateForNdrGridParams, { isFilter: true, showActivatedAlone: true })
      .subscribe((getOverAllGateForNdrGridResponse): void => {
        this.gateList = getOverAllGateForNdrGridResponse.data;
        this.getOverAllEquipmentForNdrGrid();
      });
  }

  public closeIcon(): void {
    this.modalRef.hide();
    if (!this.selectedArray) return;

    const selected = this.selectedArray[0];
    this.setFormValue('templateType', selected.templateType);
    this.setConditionalValue('companyFilter', selected.companyFilter);
    this.setConditionalValue('defineFilter', selected.defineFilter);
    this.setConditionalValue('equipmentFilter', selected.equipmentFilter);
    this.setConditionalValue('gateFilter', selected.gateFilter);
    this.setConditionalValue('memberFilter', selected.memberFilter);
    this.setFormValue('statusFilter', selected.statusFilter || '', true);
    this.setFormValue('startTime', selected.eventStartTime || '', true);
    this.setFormValue('endTime', selected.eventEndTime || '', true);
  }

  public setFormValue(controlName: string, value: any, silent = false): void {
    this.filterForm.get(controlName).setValue(value, silent ? { emitEvent: false } : undefined);
  }

  public setConditionalValue(controlName: string, value: number): void {
    const controlValue = value > 0 ? value : '';
    this.setFormValue(controlName, controlValue, value <= 0);
  }

  public defaultFilterSettings(): void {
    this.filterForm.get('templateType').setValue(this.defaultRequestType);
    if (this.filterForm.get('startTime').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('templateType').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('dateFilter').value !== '') {
      this.filterCount += 1;
    }
  }

  public setDefaultDateAndTime(): void {
    const setStartTime = 7;
    this.deliveryStart = new Date();
    this.deliveryEnd = new Date();
    this.deliveryStart.setHours(setStartTime);
    this.deliveryStart.setMinutes(0);
    this.deliveryEnd.setHours(setStartTime + 10);
    this.deliveryEnd.setMinutes(0);
    this.filterForm.get('startTime').setValue(this.deliveryStart);
    this.filterForm.get('endTime').setValue(this.deliveryEnd);
  }

  public setDefaultDateRangePicker(): void {
    const start = moment();
    const beginWeek = start.startOf('week').toDate();
    const newDate = start.startOf('week').clone().add(6, 'days').endOf('day')
      .toDate();
    this.filterForm.controls.dateFilter.setValue([beginWeek, newDate]);
  }

  public exportDetailsForm(): void {
    this.exportForm = this.formBuilder.group({
      reportName: ['', Validators.required],
      reportType: ['', Validators.required],
    });
    this.exportForm.get('reportName').setValue(this.reportName);
    this.exportForm.get('reportType').setValue(this.reportType);
  }

  public cancelExport(): void {
    this.modalRef2.hide();
    this.exportSubmitted = false;
    this.exportForm.get('reportName').setValue('Heat Map Report');
    this.exportForm.get('reportType').setValue('PDF');
  }

  public export(): void {
    this.exportSubmitted = true;
    this.formSubmitted = true;

    if (this.exportForm.invalid) {
      this.exportSubmitted = false;
      return;
    }

    // Create heat map parameters
    const getHeatMapParam = this.buildParams();

    // Prepare heat map payload
    const getHeatMapPayload = this.createHeatMapPayload();

    // Check if templateType is selected
    if (getHeatMapPayload) {
      this.loader = true;
      getHeatMapPayload.exportType = this.exportForm.get('reportType').value;
      getHeatMapPayload.reportName = this.exportForm.get('reportName').value;
      getHeatMapPayload.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      getHeatMapPayload.isDST = moment(new Date()).isDST();
      getHeatMapPayload.generatedDate = moment(new Date()).format('ddd, MMM DD YYYY');

      if (getHeatMapPayload.startDate && getHeatMapPayload.endDate) {
        getHeatMapPayload.typeFormat = 'export';
      }

      this.filterPayload = getHeatMapPayload;
      this.payloadExportService(getHeatMapParam, getHeatMapPayload);
    } else {
      this.loader = false;
      this.exportSubmitted = false;
      this.toastr.error('Please choose any one of the booking type');
    }
  }

  public createHeatMapPayload(): any {
    if (this.filterForm.value.templateType.length === 0) {
      return null;
    }

    return {
      companyFilter: +this.filterForm.value.companyFilter,
      startDate: this.formatDate(this.filterForm.value.dateFilter ? this.filterForm.value.dateFilter[0] : null),
      endDate: this.formatDate(this.filterForm.value.dateFilter ? this.filterForm.value.dateFilter[1] : null),
      statusFilter: this.filterForm.value.statusFilter || 0,
      memberFilter: +this.filterForm.value.memberFilter || 0,
      gateFilter: +this.filterForm.value.gateFilter || 0,
      equipmentFilter: +this.filterForm.value.equipmentFilter || 0,
      templateType: this.filterForm.value.templateType,
      defineFilter: +this.filterForm.value.defineFilter || 0,
      startTime: this.formatTime(this.filterForm.value.startTime),
      endTime: this.formatTime(this.filterForm.value.endTime),
      ParentCompanyId: this.ParentCompanyId,
    };
  }

  public formatDate(date: any): string {
    return date ? moment(date).format('YYYY-MM-DD') : '';
  }

  public formatTime(time: any): string {
    return time ? moment(time).format('HH:mm:00') : '';
  }


  public payloadExportService(getHeatMapParam, getHeatMapPayload): void {
    if (getHeatMapPayload.exportType === 'EXCEL') {
      this.reportsService
        .exportHeatMap(getHeatMapParam, getHeatMapPayload)
        .subscribe((response: any): void => {
          if (response) {
            this.reportsService.saveAsExcelFile(response, this.exportForm.get('reportName').value);
            this.loader = false;
            this.toastr.success('Heat Map exported successfully');
            this.cancelExport();
          }
        });
    } else {
      this.reportsService
        .exportHeatMap(getHeatMapParam, getHeatMapPayload)
        .subscribe((response: any): void => {
          if (response) {
            this.loader = false;
            this.exportSubmitted = false;
            if (response.message === 'No data found.') {
              this.toastr.error('No data found');
              this.cancelExport();
              return;
            }
            const link = document.createElement('a');
            link.setAttribute('target', '_self');
            link.setAttribute('href', response.data);
            document.body.appendChild(link);
            link.click();
            link.remove();
            this.toastr.success('Heat Map exported successfully');
            this.cancelExport();
          }
        });
    }
  }

  public getLocationsForNdrGrid(): void {
    const getLocationsForNdrGridParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getLocations(getLocationsForNdrGridParams)
      .subscribe((getLocationsForNdrGridResponse: any): void => {
        if (getLocationsForNdrGridResponse) {
          this.locationList = getLocationsForNdrGridResponse.data;
        }
      });
  }
}
