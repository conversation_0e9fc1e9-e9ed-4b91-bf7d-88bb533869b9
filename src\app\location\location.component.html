<section class="page-section location-layout">
  <div class="page-inner-content">
    <div class="top-header mb-3">
      <div class="row pt-md-15px align-items-center">
        <div class="col-md-8">
          <h1 class="fs16 mb-0 text-black fw-bold">Locations</h1>
        </div>
        <div class="col-md-4">
          <div class="top-filter">
            <ul class="list-group list-group-horizontal justify-content-end">
              <li class="list-group-item p0 border-0 bg-transparent me-2">
                <div class="search-icon">
                  <input
                    class="form-control fs12 color-grey8"
                    [ngClass]="showSearchbar ? 'input-hover-disable' : 'input-search'"
                    placeholder="What you are looking for?"
                    (input)="searchLocation($event.target.value)"
                    [(ngModel)]="search"
                  />
                  <div class="icon">
                    <img
                      src="./assets/images/cross-close.svg"
                      *ngIf="showSearchbar"
                      (click)="clear()"
                      (keydown) ="clear()"
                      alt="close-cross"
                    />
                    <em class="fa fa-search fs12 color-grey8" *ngIf="!showSearchbar"></em>
                  </div>
                </div>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent">
                <button
                  type="button"
                  class="btn btn-orange-dark2 px-4 pt7 pb7 radius20 fs12"
                  (click)="submitLocationNotification()"
                  [disabled]="chosenLocationNotification.length === 0"
                >
                  <em class="fa fa-spinner" aria-hidden="true" *ngIf="submitted"></em>
                  Save
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="page-card bg-white rounded settings-comapanies-table">
      <div class="table-responsive rounded tab-grid companies-tab location-table">
        <div class="notification-setting-lists" >
          <div class="row fw700 fs12 py-3 bg-grey m-0 px-0">
            <div class="col-md-6 col-6 ps-3">
              Location Path
              <span>
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('locationPath', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'locationPath', 'ASC')"
                  *ngIf="sortColumn !== 'locationPath'"
                />
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('locationPath', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'locationPath', 'ASC')"
                  *ngIf="sort === 'DESC' && sortColumn === 'locationPath'"
                />
                <img
                  src="./assets/images/up-chevron.svg"
                  alt="up-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('locationPath', 'DESC')"
                  (keydown)="handleToggleKeydown($event, 'locationPath', 'DESC')"
                  *ngIf="sort === 'ASC' && sortColumn === 'locationPath'"
                />
              </span>
            </div>
            <div class="col-md-6 col-6 ps-3 text-center">
              <span class="ms-4"> Follow</span>
            </div>
          </div>
          <div *ngIf="!loader">
            <div class="p-0 bg-white">
              <ul class="list-group list-group-horizontal w-100 bg-white">
                <li
                  class="list-group-item border-0 col-md-6 bg-transparent location-setting-category"
                >
                  <span class="ps-3 fw700 fs12">{{ defaultLocationPath.locationName }}</span>
                </li>
                <li class="list-group-item border-0 col-md-6 bg-transparent text-center custom-checkbox">
                  <div class="form-check text-center ps-0">
                    <input class="form-check-input float-none ms-4" type="checkbox"
                    id="emailtblData1"
                      name="emailtblData1"
                      [checked]="defaultLocationPath?.LocationNotificationPreferences[0]?.follow"
                      (change)="checkInstantValue(defaultLocationPath, $event,'default')"/>
                    <label class="form-check-label c-pointer fs12"
                    for="emailtblData1">
                    </label>
                  </div>
                </li>
              </ul>
            </div>
            <div class="ps-5">
              <accordion
                class="main-accordion location-accordion p-0"
                *ngFor="
                  let item of locationList
                    | paginate
                      : {
                          itemsPerPage: pageSize,
                          currentPage: currentPageNo,
                          totalItems: totalCount
                        };
                  let i = index
                "
              >
                <accordion-group>
                  <div class="d-flex shadow-none p-0" accordion-heading>
                    <ul class="list-group list-group-horizontal w-100 bg-grey4 p-0">
                      <li
                        class="list-group-item border-0 col-md-6 bg-transparent location-setting-category"
                      >
                        <span>
                          <img
                            [ngClass]="{ rotateright: isOpened }"
                            src="./assets/images/down-arrow.svg"
                            alt="Down Arrow"
                            class="arrow eye-cursor"
                            *ngIf="item?.paths?.length > 0"
                          />
                        </span>
                        <span class="fs12 mx-2">
                          {{ item.locationName }}
                        </span>
                      </li>
                      <li class="list-group-item border-0 col-md-6 bg-transparent custom-checkbox">
                        <div class="form-check text-center ps-0">
                          <input class="form-check-input float-none ms-0" type="checkbox"
                          id="emailtblData2+'item.id'+{{ i }}"
                          name="emailtblData2+'item.id'+{{ i }}"
                          [checked]="item?.LocationNotificationPreferences[0]?.follow"
                          (change)="checkInstantValue(item, $event, 'mainCategory')"/>
                          <label class="form-check-label c-pointer fs12"
                          for="emailtblData2+'item.id'+{{ i }}">
                          </label>
                        </div>
                      </li>
                    </ul>
                  </div>
                  <div>
                    <ul class="list-group list-group-horizontal w-100">
                      <accordion *ngIf="item?.paths?.length > 0" class="w-100">
                        <accordion-group *ngFor="let path of item?.paths;let j=index">
                          <div class="d-flex" accordion-heading>
                            <ul class="list-group list-group-horizontal w-100 bg-grey">
                              <li class="list-group-item border-0 w-50 col-md-6 bg-transparent">
                                <span class="ms-4">
                                  <span>
                                    <img
                                      [ngClass]="{ rotateright: isOpened }"
                                      src="./assets/images/down-arrow.svg"
                                      alt="Down Arrow"
                                      class="arrow eye-cursor"
                                      *ngIf="path?.tier?.length > 0"
                                    />
                                  </span>
                                  <span
                                    class="fw400 fs12 notification-span-desktop notification-span-mobile ms-0"
                                  >
                                    {{ path.locationName }}
                                  </span>

                                </span>
                              </li>
                              <li class="list-group-item border-0 w-50 col-md-6 bg-transparent text-center custom-checkbox">
                                <div class="form-check text-center ps-0">
                                  <input class="form-check-input float-none ms-0" type="checkbox"
                                  id="emailtblData3+'item.path.id'+{{ i }}+subCat{{j}}"
                                  name="emailtblData3+'item.path.id'+{{ i }}+subCat{{j}}"
                                  [checked]="path?.LocationNotificationPreferences[0]?.follow"
                                  (change)="checkInstantValue(path, $event,'subCategory')">
                                  <label class="form-check-label c-pointer fs12"
                                  for="emailtblData3+'item.path.id'+{{ i }}+subCat{{j}}">
                                  </label>
                                </div>
                              </li>
                              <li class="list-group-item border-0 w-50 col-md-5 bg-transparent text-end px-0">
                                <span
                                  class="fw400 fs12 notification-span-desktop notification-span-mobile ps-0 ms-0 me-1"
                                >

                                </span>
                              </li>
                            </ul>
                          </div>
                          <div *ngIf="path?.tier?.length > 0">
                            <ul
                              class="list-group list-group-horizontal w-100"
                              *ngFor="let tier of path?.tier;let k=index"
                            >
                              <li class="list-group-item border-0 w-50 col-md-6">
                                <span
                                  class="fw400 fs12 notification-span-desktop notification-span-mobile ms-3 col-md-6 px-5"
                                >
                                  {{ tier.locationName }}
                                </span>
                              </li>
                              <li class="list-group-item border-0 w-50 col-md-6 text-center custom-checkbox">

                                <div class="form-check text-center ps-0">
                                  <input class="form-check-input float-none ms-0" type="checkbox"
                                  id="emailtblData4+'path.tier'+{{ i }}+subCat{{j}}+'tier'{{k}}"
                                  name="emailtblData4+'path.tier'+{{ i }}+subCat{{j}}+'tier'{{k}}"
                                  [checked]="tier?.LocationNotificationPreferences[0]?.follow"
                                  (change)="checkInstantValue(tier, $event,'tier')">
                                  <label class="form-check-label c-pointer fs12"
                                  for="emailtblData4+'path.tier'+{{ i }}+subCat{{j}}+'tier'{{k}}"> &nbsp;
                                  </label>
                                </div>
                              </li>
                              <li class="list-group-item border-0 w-50 col-md-5 text-end">
                                <span
                                  class="fw400 fs12 notification-span-desktop notification-span-mobile me-3"
                                >

                                </span>
                              </li>
                            </ul>
                          </div>
                        </accordion-group>
                      </accordion>
                    </ul>
                  </div>
                </accordion-group>
              </accordion>
            </div>
          </div>
          <div class="row" *ngIf="loader">
            <div class="col-12 text-center">
              <div class="fs18 fw-bold cairo-regular my-5 text-black">Loading...</div>
            </div>
          </div>
        </div>
        <span class="fs10 font-weight"
          >Note: <img src="./assets/images/note_icon.svg" alt="i-icon" /> These preferences are
          applicable for this page only</span
        >
      </div>
      <div
        class="tab-pagination px-2"
        id="tab-pagination2"
        *ngIf="loader == false && totalCount > 25"
      >
        <div class="row">
          <div class="col-md-2 align-items-center">
            <ul class="list-inline my-3">
              <li class="list-inline-item notify-pagination">
                <label class="fs12 color-grey4" for="showEnt">Show entries</label>
              </li>
              <li class="list-inline-item">
                <select id="showEnt"
                  class="w-auto form-select fs12 color-grey4"
                  (change)="changePageSize($event.target.value)"
                  [ngModel]="pageSize"
                >
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                  <option value="150">150</option>
                </select>
              </li>
            </ul>
          </div>
          <div class="col-md-8 text-center">
            <div class="my-3 position-relative d-inline-block">
              <pagination-controls
                (pageChange)="changePageNo($event)"
                previousLabel=""
                nextLabel=""
              >
              </pagination-controls>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
