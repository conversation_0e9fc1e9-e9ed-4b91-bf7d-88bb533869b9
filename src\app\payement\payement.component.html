<section class="payment-content">
    <div class="container-fluid">
       <div class="row">
          <div class="row w-100">
            <div class="col-6 col-md-6 col-lg-6 text-left">
               <img src="./assets/images/logo.svg" routerLink="/" class="c-pointer top-logo  mt-md-5 mb-md-3 mx-md-5" alt="Follo">
             </div>
             <div class="col-6 col-md-6 col-lg-6 text-end">
               <button class="btn btn-orange fs14 fw-bold cairo-regular shadow-none mt-md-5 mb-md-3 mx-md-4" (click)="redirect()">Back</button>
             </div>
          </div>
          <div class="col-md-12 col-lg-7 offset-lg-3 payment-col">
            <div class="mb-4 text-center">
               <h1 class="fs26 font-weight-normal mb-3">Let's get started!</h1>
               <h2 class="color-grey21 fs20">
                  You have selected the project plan
               </h2>
            </div>
            <hr/>
            <form name="form" [formGroup]="paymentCardDetailsForm" id="payment-form1" (ngSubmit)="onSubmit()"  novalidate>
               <div class="row align-items-center">
                  <div class="col-md-3 col-8 col-sm-5 billed-col ps-md-0">
                     <div class="form-group mb-0">
                        <select class="form-control custom-select-arrow border-0 fs18 color-grey22" formControlName="interval" (change)="selectPlan($event.target.value)" >
                          <option value="month">Billed Monthly</option>
                           <option value="year">Billed Annual</option>
                        </select>
                     </div>
                  </div>
                  <div class="col-md-9 col-4 col-sm-7 price-col text-end">
                     <h3 class="fs24 mb-0 color-grey22 fw-bold cairo-regular">${{(currentPlan.stripeAmount)/100}}</h3>
                     <p class="fs14 color-grey22 mb-0">/{{interval}}</p>
                  </div>
               </div>
               <hr>
               <div class="row">
                  <div class="col-md-12">
                     <div class="form-group payment-group">
                        <label class="fs14 color-grey23 mb-0" for="cardName">Name on card</label>
                        <input class="form-control" placeholder="" formControlName='name' (keypress)="alphaOnly($event)" id="cardName">
                        <div class="color-red" *ngIf="submitted && paymentCardDetailsForm.get('name').errors">
                           <small *ngIf="paymentCardDetailsForm.get('name').errors.required">*Enter Name</small>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="row">
                  <div class="col-md-6 pe-md-1">
                     <div class="form-group payment-group">
                        <label class="fs14 color-grey23 mb-0" for="cardNum">Card number</label>
                        <input class="form-control" placeholder="" formControlName='number'  (keypress)="numberOnly($event)" mask="{{cardMask}}" id="cardNum">
                        <div class="color-red" *ngIf="submitted && paymentCardDetailsForm.get('number').errors" >
                           <small *ngIf="paymentCardDetailsForm.get('number').errors.required">*Enter Number</small>
                           <small *ngIf="!paymentCardDetailsForm.get('number').errors.required && !paymentCardDetailsForm.get('number').valid">*Enter Valid Card Number</small>
                        </div>
                     </div>
                  </div>
                  <div class="col-md-6 payment-group">
                     <div class="row">
                        <div class="col-md-8 col-8">
                           <label class="fs14 color-grey23 mb-0" id="payment-exp" for="expDate">Expiry date (MM YYYY)</label>
                           <div class="row">
                              <div class="col-md-6 col-6 pe-1">
                                 <div class="form-group">
                                    <select class="custom-select month pe-0"  id="expDate" formControlName="exp_month" [ngClass]="{ 'is-invalid': submitted && paymentCardDetailsForm.get('exp_month').invalid}"
                                    required>
                                    <option selected hidden value="">MM</option>
                                    <ng-container *ngFor="let month of paymentCardMonths">
                                      <option value="{{month}}">{{month}}</option>
                                    </ng-container>
                                  </select>
                                  <div class="color-red" *ngIf="submitted && paymentCardDetailsForm.get('exp_month').errors">
                                    <small class="d-block pt-1 error-text" *ngIf="paymentCardDetailsForm.get('exp_month').errors.required">*Choose Expiry Month</small>
                                 </div>

                                 </div>
                              </div>
                              <div class="col-md-6 col-6 pe-1">
                                 <div class="form-group">
                                    <select class="custom-select year pe-0" formControlName="exp_year" [ngClass]="{ 'is-invalid': submitted && paymentCardDetailsForm.get('exp_year').invalid}"
                                    required>
                                    <option selected hidden value="">YYYY</option>
                                    <ng-container *ngFor="let year of paymentCardyears">
                                      <option value="{{year}}">{{year}}</option>
                                    </ng-container>
                                  </select>
                                  <div class="color-red" *ngIf="submitted && paymentCardDetailsForm.get('exp_year').errors">
                                    <small class="d-block pt-1 error-text" *ngIf="paymentCardDetailsForm.get('exp_year').errors.required">*Choose Expiry Year</small>
                                 </div>

                                 </div>
                              </div>
                           </div>
                        </div>
                        <div class="col-md-4 col-4">
                           <label class="fs14 color-grey23 mb-0" for="paymentCvv" id="payment-cvv">(CVC/CVV)</label>
                           <div class="form-group">
                              <input class="form-control" placeholder="" formControlName='cvc' maxlength="3" id="paymentCvv">
                              <div class="color-red" *ngIf="submitted && paymentCardDetailsForm.get('cvc').errors" (keypress)="numberOnly($event)">
                                 <small *ngIf="paymentCardDetailsForm.get('cvc').errors.required">*Enter cvc</small>
                              </div>
                           </div>
                        </div>
                     </div>
               </div>
               </div>
               <div class="row">
                  <div class="col-md-6 pe-md-1">
                     <div class="form-group payment-group">
                        <label class="fs14 color-grey23 mb-0" for="country" >Country</label>
                        <select class="custom-select form-control material-input fs13 color-grey23"  formControlName ="country" id="country" >
                           <option value="" disabled selected hidden>Country</option>
                           <option *ngFor="let item of countryList"  value={{item.countryName}} >{{item.countryName}}</option>
                       </select>
                        <div class="color-red" *ngIf="submitted && paymentCardDetailsForm.get('country').errors">
                           <small *ngIf="paymentCardDetailsForm.get('country').errors.required">*Enter country</small>
                        </div>
                     </div>
                  </div>
                  <div class="col-md-6">
                     <div class="form-group payment-group">
                        <label class="fs14 color-grey23 mb-0" for="zipcode">Zip code</label>
                        <input class="form-control" placeholder="" formControlName='zipCode' id="zipcode">
                        <div class="color-red" *ngIf="submitted && paymentCardDetailsForm.get('zipCode').errors" (keypress)="allowAlphaNumeric($event)">
                           <small *ngIf="paymentCardDetailsForm.get('zipCode').errors.required">*Enter zip code</small>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="my-3 text-center payment-group">
                  <a class="btn btn-grey-light color-dark-grey radius20 fs12 me-4  mt-2 fw-bold cairo-regular px-2rem" (click)="reset()">Cancel</a>
                  <button class="btn btn-orange-dark radius20 fs12  mt-2 fw-bold cairo-regular px-5" type="submit"[disabled]="formSubmitted && paymentCardDetailsForm.valid"><em class="fa fa-spinner" aria-hidden="true" *ngIf="formSubmitted && paymentCardDetailsForm.valid"></em> Pay</button>
               </div>
            </form>
       </div>
    </div>
</div>
</section>
<ng-template #thanks>
   <div class="modal-body">
      <div class="text-center my-4 col-md-11 mx-auto px-4">
         <div class="success-tick">
            <em class="fa fa-check fa-2x text-black tick-icon"></em>
         </div>
         <p class="color-grey4 fs12 mb-5">Thanks for getting started with Follo! We have sent you an email with your login information.  </p>
         <p class="color-grey4 fs12 mb-5"> Please visit your inbox to continue with your Follo account setup. Thank you again!</p>
         <button class="btn btn-orange radius20 fs12 color-orange fw-bold cairo-regular px-5" (click)="modalRef.hide()">Close</button>
      </div>
</div>
 </ng-template>
 <ng-template #projectsuccess>
   <div class="modal-body">
      <div class="text-center my-4 col-md-11 mx-auto">
         <div class="success-tick">
            <em class="fa fa-check fa-2x text-black tick-icon"></em>
         </div>
         <p class="color-grey4 fs12 mb-5"> Thank you for subscribing to Follo!
             </p>
             <p class="color-grey4 fs12 mb-5">We appreciate your business.</p>
         <p class="color-grey4 fs12 mb-5"> Please click on close to access your project. </p>
         <button class="btn btn-orange radius20 fs12 color-orange fw-bold cairo-regular px-5" (click)="modalRef.hide()">Close</button>
      </div>
</div>
 </ng-template>
 <ng-template #refresh>
   <div class="modal-body">
      <div class="spinner-border my-3 mx-auto">
      </div>
      <div class="text-center my-4 col-md-11 mx-auto">
        *Processing your payment. Please do not refresh or reload the browser.
      </div>
</div>
 </ng-template>
