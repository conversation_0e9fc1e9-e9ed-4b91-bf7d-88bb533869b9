<section class="auth-content">
  <div class="auth-overlay d-flex align-items-start w-100">
    <div class="container-fluid">
      <div class="top-logo">
        <a href="https://www.folloit.com/" target="_blank" rel="noopener">
          <img
            src="./assets/images/logo.svg"
            width="100"
            class="c-pointer auth-logo m-5"
            alt="Follo"
        /></a>
      </div>
      <div class="row align-items-center login-container">
        <div class="col-md-7 col-lg-4 offset-md-5 offset-lg-6 px-lg-0">
          <div class="card-container">
            <div class="card-body p20">
              <div class="row">
                <div class="col-md-12 col-lg-11 m-auto">
                  <div class="d-flex align-items-center">
                    <img
                      src="./assets/images/down-arrow.svg"
                      alt="Down Arrow"
                      class="rotateleft mb-1"
                    />
                    <a
                      href="javascript:void(0)"
                      routerLink="/home"
                      class="c-pointer fs12 fw500 color-orange pb-1"
                      ><u>Back to Home Page</u></a
                    >
                  </div>
                  <div class="mb-4">
                    <h1 class="color-grey7 fs22 fw-bold cairo-regular my-2">Login</h1>
                    <p class="color-grey4 fs14 fw500">Sign in with your credentials</p>
                  </div>
                  <form name="form" [formGroup]="loginForm" (ngSubmit)="login()" novalidate>
                    <div class="mb-4">
                      <div class="input-group">
                        <span class="input-group-text"
                          ><img
                            src="./assets/images/mail.svg"
                            width="14"
                            class="form-icon"
                            alt="Mail"
                        /></span>
                        <input
                          type="email"
                          class="form-control"
                          placeholder="Email address"
                          autocomplete="off"
                          formControlName="email"
                        />
                        <span class="underline"></span>
                      </div>
                      <div class="color-red" *ngIf="submitted && loginForm.get('email').errors">
                        <small *ngIf="loginForm.get('email').errors.required"
                          >Enter email address</small
                        >
                        <small *ngIf="loginForm.get('email').errors.pattern"
                          >Enter valid email address</small
                        >
                      </div>
                    </div>
                    <div class="mb-4">
                      <div class="input-group">
                        <span class="input-group-text"
                          ><img
                            src="./assets/images/password.svg"
                            width="12"
                            class="lock-icon"
                            alt="Password"
                        /></span>
                        <input
                          type="{{ isShowPassword ? 'text' : 'password' }}"
                          class="form-control"
                          name="password"
                          placeholder="Password"
                          formControlName="password"
                          autocomplete="on"
                        />
                        <span class="underline"></span>
                        <span class="input-group-text c-pointer" (click)="showHide()"  (keydown)="handleToggleKeydown($event)"
                          ><em
                            class="fas color-grey4 fs12"
                            [ngClass]="{
                              'fa-eye-slash': !isShowPassword,
                              'fa-eye': isShowPassword
                            }"
                          ></em
                        ></span>
                      </div>

                      <div class="color-red" *ngIf="submitted && loginForm.get('password').errors">
                        <small *ngIf="loginForm.get('password').errors.required"
                          >Enter password</small
                        >
                      </div>
                    </div>
                    <div class="row mt-3">
                      <div class="col-sm-6 col-md-4">
                        <button
                          class="btn btn-orange color-orange radius20 fs13 fw-bold cairo-regular w-100"
                          type="submit"
                          [disabled]="formSubmitted && loginForm.valid"
                        >
                          <em
                            class="fa fa-spinner"
                            aria-hidden="true"
                            *ngIf="formSubmitted && loginForm.valid"
                          ></em>
                          Login
                        </button>
                      </div>
                      <div class="col-sm-6 col-md-8 mt-2 mt-sm-0">
                        <a
                          href="javascript:void(0)"
                          routerLink="/forgot-password"
                          class="c-pointer text-black float-end fs12 fw500 color-orange forgot-link"
                          >Forgot Password?</a
                        >
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <ng-template #termsModal>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-11 col-11 text-center">
              <h4 class="fs16 fw-bold color-text7 my-1">Terms and Conditions</h4>
            </div>
            <div class="col-md-1 col-1">
              <button
                type="button"
                class="close ms-auto"
                aria-label="Close"
                (click)="modalRef.hide()"
              >
                <span aria-hidden="true"
                  ><img src="./assets/images/modal-close.svg" alt="Modal Close"
                /></span>
              </button>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12 col-12 ps-4 pe-5 pt-4 pb-2">
              <div class="form-group terms-modal-content">
                <p class="fs14 lh20 color-grey7">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                  incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
                  exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute
                  irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                  pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui
                  officia deserunt mollit anim id est laborum.
                </p>
                <p class="fs14 lh20 color-grey7">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                  incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
                  exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute
                  irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                  pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui
                  officia deserunt mollit anim id est laborum.
                </p>
                <p class="fs14 lh20 color-grey7">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                  incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
                  exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute
                  irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                  pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui
                  officia deserunt mollit anim id est laborum.
                </p>
                <p class="fs14 lh20 color-grey7">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                  incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
                  exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute
                  irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                  pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui
                  officia deserunt mollit anim id est laborum.
                </p>
                <p class="fs14 lh20 color-grey7">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                  incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
                  exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute
                  irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                  pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui
                  officia deserunt mollit anim id est laborum.
                </p>
                <p class="fs14 lh20 color-grey7">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
                  incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
                  exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute
                  irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla
                  pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui
                  officia deserunt mollit anim id est laborum.
                </p>
                <div></div>
              </div>
              <div class="text-center py-0">
                <button class="btn btn-orange-dark fs12 fw-bold mt-3 px-2 radius20">
                  I have read and accept the terms and conditions
                </button>
              </div>
              <div class="text-center py-0">
                <button
                  class="btn bg-transparent color-dark-grey radius20 fs12 mb-0 mt-2 me-3 fw-bold px-5"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      </ng-template>
    </div>
  </div>
</section>
