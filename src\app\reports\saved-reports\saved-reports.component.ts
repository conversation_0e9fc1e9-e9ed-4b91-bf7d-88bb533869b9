import {
  Component, OnInit, Input, TemplateRef,
} from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import moment from 'moment';
import { ReportsService } from '../../services/reports/reports.service';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';

@Component({
  selector: 'app-saved-reports',
  templateUrl: './saved-reports.component.html',
  })
export class SavedReportsComponent implements OnInit {
  @Input() public filterValues: any;

  @Input() public getSearchText: any;

  public totalSavedCount = 0;

  public pageSize = 25;

  public pageNo = 1;

  public loader = true;

  public savedReports: any = [];

  public ProjectId: string | number;

  public sortColumn = 'id';

  public sort = 'DESC';

  public deleteSavedReportData: any = {};

  public modalRef: BsModalRef;

  public deleteSavedReportSubmitted = false;

  public authUser: any = {};

  public constructor(
    private readonly reportsService: ReportsService,
    public projectService: ProjectService,
    private readonly modalService: BsModalService,
    private readonly toastr: ToastrService,
    private readonly deliveryService: DeliveryService,
  ) {
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
      }
    });
    this.projectService.projectParent.subscribe((response19): void => {
      if (response19 !== undefined && response19 !== null && response19 !== '') {
        this.ProjectId = response19.ProjectId;
        this.getSavedReports();
      }
    });
  }

  public ngOnInit(): void { /* */ }


  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'download':
          this.downloadSavedReport(data, item);
          break;
        case 'delete':
          this.openDeleteModal(data, item);
          break;
        case 'auto':
          this.autoDownloadReportWithRunOption(data);
          break;
        default:
          break;
      }
    }
  }

  public changePageNo(pageNo: number): void {
    this.pageNo = pageNo;
    this.getSavedReports();
  }

  public getSavedReports(): void {
    this.loader = true;
    this.savedReports = [];

    if (!this.ProjectId) return;

    const payload = this.buildReportPayload();

    this.reportsService.getSavedReports(payload).subscribe({
      next: (response: any): void => {
        this.loader = false;
        if (response?.data) {
          this.savedReports = response.data.scheduledReports || [];
          this.totalSavedCount = response.data.count || 0;
        } else {
          this.savedReports = [];
          this.totalSavedCount = 0;
        }
      },
      error: (error: any): void => {
        this.loader = false;
        this.savedReports = [];
        this.totalSavedCount = 0;
        this.toastr.error(error.message || 'Try again later.!', 'OOPS!');
      }
    });
  }

  public buildReportPayload(): any {
    const payload: any = {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.pageNo,
      sort: this.sort,
      sortByField: this.sortColumn,
      isSaved: true,
      search: this.getSearchText || '',
    };

    if (!this.filterValues) return payload;

    const {
      createdUserId, reportName, templateType, lastRun,
    } = this.filterValues;

    if (createdUserId) payload.createdUserId = createdUserId;
    if (reportName) payload.reportName = reportName;
    if (templateType) payload.templateType = templateType;
    if (lastRun) {
      payload.lastRun = moment(lastRun).format('YYYY-MM-DD');
      payload.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    }

    return payload;
  }

  public ngOnChanges(): void {
    this.getSavedReports();
  }

  public changePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.getSavedReports();
  }

  public sortByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getSavedReports();
  }

  public openDeleteModal(deleteData: any, template: TemplateRef<any>): void {
    this.deleteSavedReportData = {};
    this.deleteSavedReportData = deleteData;
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-md new-gate-popup custom-modal',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public downloadSavedReport(data, action): void {
    if (data && data === 'No data found' && action === 'reRun') {
      this.toastr.info('There are no events available within the scheduled date range');
    } else if ((data && action === 'reRun') || (data?.s3_url && action === 'lastRun')) {
      const link = document.createElement('a');
      link.setAttribute('target', '_self');
      if (action === 'reRun') {
        link.setAttribute('href', data);
      } else {
        link.setAttribute('href', data.s3_url);
      }
      document.body.appendChild(link);
      link.click();
      link.remove();
      this.toastr.success('Report downloaded successfully');
    } else {
      this.toastr.info('There are no events available within the scheduled date range');
    }
  }

  public deleteSavedReport(): void {
    if (!this.deleteSavedReportData?.id) {
      this.toastr.error('Invalid report data', 'Error');
      return;
    }

    this.deleteSavedReportSubmitted = true;
    this.reportsService
      .deleteScheduledReports({
        id: this.deleteSavedReportData.id,
        ProjectId: this.ProjectId,
        // ParentCompanyId: this.ParentCompanyId,
      })
      .subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.getSavedReports();
            this.deleteSavedReportData = {};
            this.deleteSavedReportSubmitted = false;
            this.modalRef.hide();
          }
        },
        error: (deleteConcreteRequestError): void => {
          this.deleteSavedReportSubmitted = false;
          if (deleteConcreteRequestError.message?.statusCode === 400) {
            this.showError(deleteConcreteRequestError);
          } else if (!deleteConcreteRequestError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deleteConcreteRequestError.message, 'OOPS!');
          }
        },
      });
  }


  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }

  public resetAndClose(): void {
    this.modalRef.hide();
    this.deleteSavedReportData = {};
  }

  public autoDownloadReportWithRunOption(data): void {
    const selectedRow = data;
    selectedRow.runNowLoader = true;
    this.reportsService
      .runNowScheduledOrSavedReport({
        id: data.id,
        ProjectId: this.ProjectId,
      })
      .subscribe({
        next: (response: any): void => {
          if (response) {
            selectedRow.runNowLoader = false;
            this.downloadSavedReport(response.data, 'reRun');
          }
        },
        error: (runNowSavedReportError): void => {
          selectedRow.runNowLoader = false;
          if (runNowSavedReportError.message?.statusCode === 400) {
            this.showError(runNowSavedReportError);
          } else if (!runNowSavedReportError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(runNowSavedReportError.message, 'OOPS!');
          }
        },
      });
  }
}
