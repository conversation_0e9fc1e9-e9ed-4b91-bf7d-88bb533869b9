import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SubmitBookComponent } from './submit-book.component';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { ProjectSharingService } from '../../../services/projectSharingService/project-sharing.service';
import { of, throwError } from 'rxjs';

describe('SubmitBookComponent', () => {
  let component: SubmitBookComponent;
  let fixture: ComponentFixture<SubmitBookComponent>;
  let routerMock: jest.Mocked<Router>;
  let projectSharingServiceMock: jest.Mocked<ProjectSharingService>;
  let toastrMock: jest.Mocked<ToastrService>;

  beforeEach(async () => {
    routerMock = {
      navigate: jest.fn()
    } as any;

    projectSharingServiceMock = {
      updateGuestMember: jest.fn()
    } as any;

    toastrMock = {
      error: jest.fn()
    } as any;

    // Mock localStorage
    const mockLocalStorage = {
      'guestProjectId': btoa('123'),
      'guestParentCompanyId': btoa('456'),
      'guestId': btoa('789'),
      'url': btoa('/some-url')
    };

    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn((key: string) => mockLocalStorage[key] || null)
      },
      writable: true
    });

    await TestBed.configureTestingModule({
      declarations: [ SubmitBookComponent ],
      providers: [
        { provide: Router, useValue: routerMock },
        { provide: ProjectSharingService, useValue: projectSharingServiceMock },
        { provide: ToastrService, useValue: toastrMock }
      ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SubmitBookComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct values from localStorage', () => {
    expect(component.ProjectId).toBe(123);
    expect(component.ParentCompanyId).toBe(456);
    expect(component.guestUserId).toBe(789);
  });

  describe('submit method', () => {
    it('should navigate to stored URL when type is "no"', () => {
      component.submit('no');
      expect(routerMock.navigate).toHaveBeenCalledWith(['/some-url']);
    });

    it('should call updateGuestMember and navigate to invite-link on success', () => {
      projectSharingServiceMock.updateGuestMember.mockReturnValue(of(true));
      
      component.submit('yes');
      
      expect(projectSharingServiceMock.updateGuestMember).toHaveBeenCalledWith({
        ProjectId: 123,
        userId: 789
      });
      expect(routerMock.navigate).toHaveBeenCalledWith(['/invite-link']);
    });

    it('should show error toast when updateGuestMember fails', () => {
      projectSharingServiceMock.updateGuestMember.mockReturnValue(throwError(() => new Error('API Error')));
      
      component.submit('yes');
      
      expect(toastrMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });
  });
});
