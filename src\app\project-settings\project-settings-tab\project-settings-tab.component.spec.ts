import { ProjectSettingsTabComponent } from './project-settings-tab.component';
import { UntypedFormBuilder } from '@angular/forms';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Title } from '@angular/platform-browser';
import { of, throwError } from 'rxjs';

describe('ProjectSettingsTabComponent', () => {
  let component: ProjectSettingsTabComponent;
  let modalService: any;
  let titleService: any;
  let projectSettingsService: any;
  let projectService: any;
  let formBuilder: UntypedFormBuilder;
  let toastr: any;
  let deliveryService: any;

  beforeEach(() => {
    modalService = { show: jest.fn(), hide: jest.fn() };
    titleService = { setTitle: jest.fn() };
    projectSettingsService = {
      getProjectSettings: jest.fn(),
      updateProjectSettings: jest.fn()
    };
    projectService = {
      projectParent: of({ ProjectId: 1, ParentCompanyId: 2 }),
      gateList: jest.fn().mockReturnValue(of({ data: [{ id: 1, gateName: 'Gate1' }] })),
      listEquipment: jest.fn().mockReturnValue(of({ data: [{ id: 1, equipmentName: 'Equip1' }] }))
    };
    formBuilder = new UntypedFormBuilder();
    toastr = { success: jest.fn(), error: jest.fn() };
    deliveryService = { searchNewMemberForAutoApprove: jest.fn().mockReturnValue(of([])) };

    component = new ProjectSettingsTabComponent(
      modalService,
      titleService,
      projectSettingsService,
      projectService,
      formBuilder,
      toastr,
      deliveryService
    );
    component.ProjectId = 1;
    component.ParentCompanyId = 2;
    component.equipmentList = [{ id: 1, equipmentName: 'Equip1' }];
    component.gateList = [{ id: 1, gateName: 'Gate1' }];
    component.projectSettingsForm();
    component.autoApproveForm();
    component.deliveryData = [{ label: 'A', line: 1, selected: false }, { label: 'B', line: 2, selected: false }];
    component.craneData = [{ label: 'C', line: 1, selected: false }, { label: 'D', line: 2, selected: false }];
    component.concreteData = [{ label: 'E', line: 1, selected: false }, { label: 'F', line: 2, selected: false }];
    component.inspectionData = [{ label: 'G', line: 1, selected: false }, { label: 'H', line: 2, selected: false }];
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call ngOnInit and initialize forms', () => {
    jest.spyOn(component, 'setEquipment');
    jest.spyOn(component, 'setGate');
    jest.spyOn(component, 'projectSettingsForm');
    jest.spyOn(component, 'getProjectSettings');
    jest.spyOn(component, 'autoApproveForm');
    component.ngOnInit();
    expect(component.setEquipment).toHaveBeenCalled();
    expect(component.setGate).toHaveBeenCalled();
    expect(component.projectSettingsForm).toHaveBeenCalled();
    expect(component.getProjectSettings).toHaveBeenCalled();
    expect(component.autoApproveForm).toHaveBeenCalled();
  });

  it('should return statusColorStyle', () => {
    expect(component.statusColorStyle()).toEqual({
      'border-radius': '0px 5px 5px 0px',
      padding: '5px 15px',
      'font-size': '12px',
      opacity: 1,
    });
  });

  it('should return approved, pending, delivered, rejected, expired styles', () => {
    component.projectSettingsFormData.patchValue({
      approvedBackgroundColor: '#fff',
      approvedFontColor: '#000',
      pendingBackgroundColor: '#111',
      pendingFontColor: '#222',
      deliveredBackgroundColor: '#333',
      deliveredFontColor: '#444',
      rejectedBackgroundColor: '#555',
      rejectedFontColor: '#666',
      expiredBackgroundColor: '#777',
      expiredFontColor: '#888',
    });
    expect(component.approved.background).toContain('#fff');
    expect(component.pending.background).toContain('#111');
    expect(component.delivered.background).toContain('#333');
    expect(component.rejected.background).toContain('#555');
    expect(component.expired.background).toContain('#777');
  });

  it('should handle handleToggleKeydown for color, card, open, remove', () => {
    jest.spyOn(component, 'setDefault');
    jest.spyOn(component, 'openModal');
    jest.spyOn(component, 'removeMemberFromAutoApproval');
    const event = { key: 'Enter', preventDefault: jest.fn() } as any;
    component.handleToggleKeydown(event, 'color');
    expect(component.setDefault).toHaveBeenCalledWith('color');
    component.handleToggleKeydown(event, 'card');
    expect(component.setDefault).toHaveBeenCalledWith('card');
    component.handleToggleKeydown(event, 'open', 'item');
    expect(component.openModal).toHaveBeenCalledWith('item');
    component.handleToggleKeydown(event, 'remove', 'item');
    expect(component.removeMemberFromAutoApproval).toHaveBeenCalledWith('item');
    event.key = 'a';
    component.handleToggleKeydown(event, 'color'); // should not call anything
  });

  it('should call setGate and set gateList and gateDropdownSettings', () => {
    component.setGate();
    expect(component.gateList.length).toBeGreaterThan(0);
    expect(component.gateDropdownSettings).toBeDefined();
  });

  it('should call onColorPickerChange', () => {
    component.onColorPickerChange('approvedBackgroundColor', '#123');
    expect(component.projectSettingsFormData.get('approvedBackgroundColor').value).toBe('#123');
  });

  it('should call autoApproveForm', () => {
    component.autoApproveForm();
    expect(component.autoApproveFormData).toBeDefined();
  });

  it('should call setEquipment and set equipmentList and equipmentDropdownSettings', () => {
    component.equipmentList = [];
    component.setEquipment();
    expect(component.equipmentDropdownSettings).toBeDefined();
  });

  it('should call cardSettings and clear value if needed', () => {
    component.projectSettingsFormData.patchValue({ selectedDeliveryLine2: 'A', selectedDeliveryLine1: 'B' });
    component.cardSettings('deliveryCardLine1', 'A');
    expect(component.projectSettingsFormData.get('selectedDeliveryLine2').value).toBe('');
  });

  it('should call getProjectSettings and handle success', () => {
    const mockRes = {
      data: {
        projectSettings: {
          useTextColorAsLegend: true,
          deliveryCard: JSON.stringify([{ label: 'A', line: 1, selected: false }]),
          craneCard: JSON.stringify([{ label: 'C', line: 1, selected: false }]),
          concreteCard: JSON.stringify([{ label: 'E', line: 1, selected: false }]),
          inspectionCard: JSON.stringify([{ label: 'G', line: 1, selected: false }]),
          statusColorCode: JSON.stringify([
            { status: 'approved', backgroundColor: '#fff', fontColor: '#000' },
            { status: 'pending', backgroundColor: '#111', fontColor: '#222' },
            { status: 'delivered', backgroundColor: '#333', fontColor: '#444' },
            { status: 'rejected', backgroundColor: '#555', fontColor: '#666' },
            { status: 'expired', backgroundColor: '#777', fontColor: '#888' },
          ]),
          equipmentExceptions: JSON.stringify([1]),
          gateExceptions: JSON.stringify([1]),
          workingWindowStartHours: 1,
          workingWindowStartMinutes: 2,
          workingWindowEndHours: 3,
          workingWindowEndMinutes: 4,
          deliveryWindowTime: 1,
          deliveryWindowTimeUnit: 'hours',
          inspectionWindowTime: 2,
          inspectionWindowTimeUnit: 'hours',
          craneWindowTime: 3,
          craneWindowTimeUnit: 'hours',
          concreteWindowTime: 4,
          concreteWindowTimeUnit: 'hours',
          isDefaultColor: true,
          isAutoApprovalEnabled: true,
          deliveryAllowOverlappingBooking: true,
          deliveryAllowOverlappingCalenderEvents: true,
          craneAllowOverlappingBooking: true,
          craneAllowOverlappingCalenderEvents: true,
          concreteAllowOverlappingBooking: true,
          concreteAllowOverlappingCalenderEvents: true,
          inspectionAllowOverlappingBooking: true,
          inspectionAllowOverlappingCalenderEvents: true,
        },
        autoApproveMembers: [],
      },
    };
    projectSettingsService.getProjectSettings.mockReturnValue(of(mockRes));
    component.getProjectSettings();
    expect(component.loader).toBe(false);
    expect(component.checkbox1).toBe(true);
  });

  it('should handle getProjectSettings error', () => {
    projectSettingsService.getProjectSettings.mockReturnValue(throwError(() => new Error('fail')));
    component.getProjectSettings();
    expect(component.loader).toBe(false);
  });

  it('should call getCardData and patch values', () => {
    const responseData = {
      workingWindowStartHours: 1,
      workingWindowStartMinutes: 2,
      workingWindowEndHours: 3,
      workingWindowEndMinutes: 4,
      deliveryWindowTime: 1,
      deliveryWindowTimeUnit: 'hours',
      inspectionWindowTime: 2,
      inspectionWindowTimeUnit: 'hours',
      craneWindowTime: 3,
      craneWindowTimeUnit: 'hours',
      concreteWindowTime: 4,
      concreteWindowTimeUnit: 'hours',
      isDefaultColor: true,
      isAutoApprovalEnabled: true,
      deliveryAllowOverlappingBooking: true,
      deliveryAllowOverlappingCalenderEvents: true,
      craneAllowOverlappingBooking: true,
      craneAllowOverlappingCalenderEvents: true,
      concreteAllowOverlappingBooking: true,
      concreteAllowOverlappingCalenderEvents: true,
      inspectionAllowOverlappingBooking: true,
      inspectionAllowOverlappingCalenderEvents: true,
      deliveryCard: [{ label: 'A', line: 1, selected: true }],
      craneCard: [{ label: 'C', line: 1, selected: true }],
      concreteCard: [{ label: 'E', line: 1, selected: true }],
      inspectionCard: [{ label: 'G', line: 1, selected: true }],
    };
    component.getCardData(responseData);
    expect(component.showData).toBe(false);
  });

  it('should allow only numbers in numberOnly', () => {
    const event = { which: 48, keyCode: 48, preventDefault: jest.fn() };
    expect(component.numberOnly(event)).toBe(true);
    event.which = 65;
    expect(component.numberOnly(event)).toBe(false);
  });

  it('should call statusColorCode and set values', () => {
    const colorData = [
      { status: 'approved', backgroundColor: '#fff', fontColor: '#000' },
      { status: 'pending', backgroundColor: '#111', fontColor: '#222' },
      { status: 'delivered', backgroundColor: '#333', fontColor: '#444' },
      { status: 'rejected', backgroundColor: '#555', fontColor: '#666' },
      { status: 'expired', backgroundColor: '#777', fontColor: '#888' },
    ];
    component.statusColorCode(colorData);
    expect(component.projectSettingsFormData.get('approvedBackgroundColor').value).toBe('#fff');
    expect(component.projectSettingsFormData.get('pendingBackgroundColor').value).toBe('#111');
    expect(component.projectSettingsFormData.get('deliveredBackgroundColor').value).toBe('#333');
    expect(component.projectSettingsFormData.get('rejectedBackgroundColor').value).toBe('#555');
    expect(component.projectSettingsFormData.get('expiredBackgroundColor').value).toBe('#777');
  });

  it('should call setDefault for color and card', () => {
    projectSettingsService.updateProjectSettings.mockReturnValue(of({ message: 'ok' }));
    jest.spyOn(component, 'getProjectSettings');
    jest.spyOn(component, 'closeAutoApproveModal');
    component.setDefault('color');
    expect(toastr.success).toHaveBeenCalled();
    component.setDefault('card');
    expect(toastr.success).toHaveBeenCalled();
  });

  it('should handle setDefault error', () => {
    projectSettingsService.updateProjectSettings.mockReturnValue(throwError(() => new Error('fail')));
    component.setDefault('color');
    expect(toastr.error).toHaveBeenCalled();
  });

  it('should call validateData and return true for missing fields', () => {
    component.projectSettingsFormData.patchValue({
      selectedDeliveryLine1: '',
      selectedDeliveryLine2: '',
      selectedCraneLine1: '',
      selectedCraneLine2: '',
      selectedConcreteLine1: '',
      selectedConcreteLine2: '',
      selectedInspectionLine1: '',
      selectedInspectionLine2: '',
    });
    expect(component.validateData()).toBe(true);
  });

  it('should call validateData and return false for all fields filled', () => {
    component.projectSettingsFormData.patchValue({
      selectedDeliveryLine1: 'A',
      selectedDeliveryLine2: 'B',
      selectedCraneLine1: 'C',
      selectedCraneLine2: 'D',
      selectedConcreteLine1: 'E',
      selectedConcreteLine2: 'F',
      selectedInspectionLine1: 'G',
      selectedInspectionLine2: 'H',
    });
    expect(component.validateData()).toBe(false);
  });

  it('should call statusColorCodePayload', () => {
    component.projectSettingsFormData.patchValue({
      approvedFontColor: '#000',
      approvedBackgroundColor: '#fff',
      pendingFontColor: '#222',
      pendingBackgroundColor: '#111',
      deliveredFontColor: '#444',
      deliveredBackgroundColor: '#333',
      rejectedFontColor: '#666',
      rejectedBackgroundColor: '#555',
      expiredFontColor: '#888',
      expiredBackgroundColor: '#777',
    });
    const payload = component.statusColorCodePayload();
    expect(payload).toContain('approved');
    expect(payload).toContain('pending');
    expect(payload).toContain('delivered');
    expect(payload).toContain('rejected');
    expect(payload).toContain('expired');
  });

  it('should call payloadCardData', () => {
    component.projectSettingsFormData.patchValue({
      selectedDeliveryLine1: 'A',
      selectedDeliveryLine2: 'B',
      selectedCraneLine1: 'C',
      selectedCraneLine2: 'D',
      selectedConcreteLine1: 'E',
      selectedConcreteLine2: 'F',
      selectedInspectionLine1: 'G',
      selectedInspectionLine2: 'H',
    });
    const payload = component.payloadCardData();
    expect(payload.deliveryData.length).toBe(2);
    expect(payload.craneData.length).toBe(2);
    expect(payload.concreteData.length).toBe(2);
    expect(payload.inspectionData.length).toBe(2);
  });

  it('should call submit and handle validation', () => {
    jest.spyOn(component, 'validateData').mockReturnValue(true);
    component.submit();
    expect(component.validateData).toHaveBeenCalled();
  });

  it('should call submit and updateProjectSettings', () => {
    jest.spyOn(component, 'validateData').mockReturnValue(false);
    jest.spyOn(component, 'payloadCardData');
    jest.spyOn(component, 'checkStatusColorChange');
    projectSettingsService.updateProjectSettings.mockReturnValue(of({ message: 'ok' }));
    component.projectSettingsFormData.patchValue({
      workingWindowStart: new Date(),
      workingWindowEnd: new Date(),
      deliveryWindowTime: 1,
      deliveryWindowTimeUnit: 'hours',
      inspectionWindowTime: 2,
      inspectionWindowTimeUnit: 'hours',
      craneWindowTime: 3,
      craneWindowTimeUnit: 'hours',
      concreteWindowTime: 4,
      concreteWindowTimeUnit: 'hours',
      isAutoApprovalEnabled: true,
      deliveryAllowOverlappingBooking: true,
      deliveryAllowOverlappingCalenderEvents: true,
      craneAllowOverlappingBooking: true,
      craneAllowOverlappingCalenderEvents: true,
      concreteAllowOverlappingBooking: true,
      concreteAllowOverlappingCalenderEvents: true,
      inspectionAllowOverlappingBooking: true,
      inspectionAllowOverlappingCalenderEvents: true,
      isDefaultColor: true,
      gateExceptions: [{ id: 1 }],
      equipmentExceptions: [{ id: 1 }],
    });
    component.autoApproveFormData.patchValue({ person: [] });
    component.submit();
    expect(toastr.success).toHaveBeenCalled();
  });

  it('should handle submit error', () => {
    jest.spyOn(component, 'validateData').mockReturnValue(false);
    jest.spyOn(component, 'payloadCardData');
    jest.spyOn(component, 'checkStatusColorChange');
    projectSettingsService.updateProjectSettings.mockReturnValue(throwError(() => new Error('fail')));
    component.projectSettingsFormData.patchValue({
      workingWindowStart: new Date(),
      workingWindowEnd: new Date(),
      deliveryWindowTime: 1,
      deliveryWindowTimeUnit: 'hours',
      inspectionWindowTime: 2,
      inspectionWindowTimeUnit: 'hours',
      craneWindowTime: 3,
      craneWindowTimeUnit: 'hours',
      concreteWindowTime: 4,
      concreteWindowTimeUnit: 'hours',
      isAutoApprovalEnabled: true,
      deliveryAllowOverlappingBooking: true,
      deliveryAllowOverlappingCalenderEvents: true,
      craneAllowOverlappingBooking: true,
      craneAllowOverlappingCalenderEvents: true,
      concreteAllowOverlappingBooking: true,
      concreteAllowOverlappingCalenderEvents: true,
      inspectionAllowOverlappingBooking: true,
      inspectionAllowOverlappingCalenderEvents: true,
      isDefaultColor: true,
      gateExceptions: [{ id: 1 }],
      equipmentExceptions: [{ id: 1 }],
    });
    component.autoApproveFormData.patchValue({ person: [] });
    component.submit();
    expect(toastr.error).toHaveBeenCalled();
  });

  it('should call checkStatusColorChange and set isDefaultColor to false if changed', () => {
    component.projectSettingsFormData.patchValue({
      isDefaultColor: true,
      approvedBackgroundColor: 'a',
      currentApprovedBackgroundColor: 'b',
      approvedFontColor: 'a',
      currentApprovedFontColor: 'b',
      deliveredBackgroundColor: 'a',
      currentDeliveredBackgroundColor: 'b',
      deliveredFontColor: 'a',
      currentDeliveredFontColor: 'b',
      rejectedBackgroundColor: 'a',
      currentRejectedBackgroundColor: 'b',
      rejectedFontColor: 'a',
      currentRejectedFontColor: 'b',
      pendingBackgroundColor: 'a',
      currentPendingBackgroundColor: 'b',
      pendingFontColor: 'a',
      currentPendingFontColor: 'b',
      expiredBackgroundColor: 'a',
      currentExpiredBackgroundColor: 'b',
      expiredFontColor: 'a',
      currentExpiredFontColor: 'b',
    });
    component.checkStatusColorChange();
    expect(component.projectSettingsFormData.get('isDefaultColor').value).toBe(false);
  });

  it('should call changeAutoApprovalOrCustom', () => {
    component.changeAutoApprovalOrCustom('auto-approve');
    expect(component.projectSettingsFormData.get('isAutoApprovalEnabled').value).toBe(true);
    component.changeAutoApprovalOrCustom('custom');
    expect(component.projectSettingsFormData.get('isAutoApprovalEnabled').value).toBe(false);
  });

  it('should call requestAutocompleteItems', (done) => {
    deliveryService.searchNewMemberForAutoApprove.mockReturnValue(of([{ id: 1 }]));
    component.requestAutocompleteItems('test').subscribe((res) => {
      expect(res).toEqual([{ id: 1 }]);
      done();
    });
  });

  it('should call bookingSetting for all types', () => {
    component.projectSettingsFormData.patchValue({
      deliveryAllowOverlappingBooking: false,
      deliveryAllowOverlappingCalenderEvents: false,
      craneAllowOverlappingBooking: false,
      craneAllowOverlappingCalenderEvents: false,
      concreteAllowOverlappingBooking: false,
      concreteAllowOverlappingCalenderEvents: false,
      inspectionAllowOverlappingBooking: false,
      inspectionAllowOverlappingCalenderEvents: false,
    });
    component.bookingSetting('delivery');
    expect(component.projectSettingsFormData.value.deliveryAllowOverlappingBooking).toBe(true);
    component.bookingSetting('deliveryBooking');
    expect(component.projectSettingsFormData.value.deliveryAllowOverlappingCalenderEvents).toBe(true);
    component.bookingSetting('crane');
    expect(component.projectSettingsFormData.value.craneAllowOverlappingBooking).toBe(true);
    component.bookingSetting('craneBooking');
    expect(component.projectSettingsFormData.value.craneAllowOverlappingCalenderEvents).toBe(true);
    component.bookingSetting('concrete');
    expect(component.projectSettingsFormData.value.concreteAllowOverlappingBooking).toBe(true);
    component.bookingSetting('concreteBooking');
    expect(component.projectSettingsFormData.value.concreteAllowOverlappingCalenderEvents).toBe(true);
    component.bookingSetting('inspection');
    expect(component.projectSettingsFormData.value.inspectionAllowOverlappingBooking).toBe(true);
    component.bookingSetting('inspectionBooking');
    expect(component.projectSettingsFormData.value.inspectionAllowOverlappingCalenderEvents).toBe(true);
  });

  it('should call openModal', () => {
    const template = {} as any;
    component.openModal(template);
    expect(modalService.show).toHaveBeenCalled();
  });

  it('should call closeAutoApproveModal', () => {
    component.modalRef = { hide: jest.fn() } as any;
    component.closeAutoApproveModal();
    expect(component.enabledMemberArray.length).toBe(0);
    expect(component.disabledMemberArray.length).toBe(0);
  });

  it('should call removeMemberFromAutoApproval', () => {
    component.getautoApprovalMembers = [{ id: 1 }, { id: 2 }];
    component.removeMemberFromAutoApproval(1);
    expect(component.disabledMemberArray).toContain(1);
    expect(component.getautoApprovalMembers.length).toBe(1);
  });

  it('should call chooseLegendColor', () => {
    component.chooseLegendColor(true);
    expect(component.checkbox1).toBe(true);
    component.chooseLegendColor(false);
    expect(component.checkbox1).toBe(false);
  });
});
