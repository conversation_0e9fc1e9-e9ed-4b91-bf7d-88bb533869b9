<section class="report--section page-section report-mobilefix">

  <div class="row">
    <div class="col-md-6 tabheader">
      <ul class="nav nav-tabs">
        <li class="li-position" [class]="isTemplate ? 'active' : ''">
          <a (click)="inTemplateClick()">Templates </a>
        </li>
        <li class="li-position" [class]="isScheduled ? 'active' : ''">
          <a (click)="inScheduledClick()">Scheduled</a>
        </li>
        <li class="li-position" [class]="isSaved ? 'active' : ''">
          <a (click)="inSavedClick()">Saved</a>
        </li>
        <li class="li-position" [class]="isRecent ? 'active' : ''">
          <a (click)="inRecentClick()">Recent</a>
        </li>
      </ul>
    </div>
    <div class="col-md-6" *ngIf="!isTemplate">
      <div class="page-inner-content position-relative">
        <div class="top-header calendar-filter position-md-absolute">
          <div class="top-filter">
            <ul class="list-group list-group-horizontal justify-content-end">
              <li class="list-group-item p0 border-0 bg-transparent me-2">
                <div class="search-icon">
                  <input
                    class="form-control fs12 color-grey8"
                    [ngClass]="showSearchbar ? 'input-hover-disable' : 'input-search'"
                    placeholder="What you are looking for?"
                    (input)="getSearch($event.target.value)"
                    [(ngModel)]="search"
                  />
                  <div class="icon">
                    <img
                      src="./assets/images/cross-close.svg"
                      *ngIf="showSearchbar"
                      (click)="clear()"
                      (keydown)="handleDownKeydown($event, '','clear')"
                      alt="close-cross"
                    />
                    <em class="fa fa-search fs12 color-grey8" *ngIf="!showSearchbar"></em>
                  </div>
                </div>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent me-2 position-relative">
                <div class="filter-icon" (click)="openModal(filter)"  (keydown)="handleDownKeydown($event, filter,'modal')">
                  <img src="./assets/images/filter.svg" class="h-12px icon" alt="Filter" />
                </div>
                <div
                  class="bg-orange rounded-circle position-absolute text-white filter-count"
                  *ngIf="filterCount > 0"
                >
                  <p class="m-0 text-center fs10">{{ filterCount }}</p>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="tab-content">
    <div *ngIf="isTemplate" class="content--div mt-3">
      <div class="row">
        <div class="col-md-4 ps-0 mb-3">
          <div class="card c-pointer" routerLink="/delivery-report">
            <img src="./assets/images/deliveries-thumbnail.png" alt="img" />
          </div>
        </div>
        <div class="col-md-4 ps-0 mb-3">
          <div class="card c-pointer" routerLink="/crane-report">
            <img src="./assets/images/crane-thumbnail.png" alt="img" />
          </div>
        </div>
        <div class="col-md-4 ps-0 mb-3">
          <div class="card c-pointer" routerLink="/concrete-report">
            <img src="./assets/images/concrete-thumbnail.png" alt="img" />
          </div>
        </div>
        <div class="col-md-4 ps-0 mb-3">
          <div class="card c-pointer" routerLink="/inspection-report">
            <img src="./assets/images/inspection-thumbnail.png" alt="img" />
          </div>
        </div>
        <div class="col-md-4 ps-0 mb-3">
          <div class="card c-pointer" routerLink="/reports-heatmap">
            <img src="./assets/images/heat-map-thumbnail.png" alt="img" />
          </div>
        </div>
        <div class="col-md-4 ps-0 mb-3">
          <div class="card c-pointer" routerLink="/reports-weeklycalendar">
            <img src="./assets/images/weekly-calendar-thumbnail.png" alt="img" />
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="isScheduled" class="content--div mt-3">
      <app-scheduled-reports
        [filterValues]="filterFormValues"
        [getSearchText]="search"
      ></app-scheduled-reports>
    </div>
    <div *ngIf="isSaved" class="content--div mt-3">
      <app-saved-reports
        [filterValues]="filterFormValues"
        [getSearchText]="search"
      ></app-saved-reports>
    </div>
    <div *ngIf="isRecent" class="content--div mt-3">
      <app-recent-reports
        [filterValues]="filterFormValues"
        [getSearchText]="search"
      ></app-recent-reports>
    </div>
  </div>

</section>

<!--Filter Modal-->
<div id="filter-temp6">
  <!--Filter Modal-->
  <ng-template #filter>
    <div class="modal-header border-0 pb-0">
      <h3 class="fs12 fw-bold cairo-regular color-text7 my-0">Filter</h3>
      <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true"
          ><img src="./assets/images/modal-close.svg" alt="Modal Close"
        /></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="filter-content">
        <form class="custom-material-form" [formGroup]="filterForm" (ngSubmit)="filterSubmit()">
          <div class="row">
            <div class="col-md-12">
              <div class="input-group mb-3">
                <select class="form-control fs12 material-input" formControlName="createdUserId">
                  <option value="" disabled selected hidden>Author</option>
                  <option
                    *ngFor="let item of memberList"
                    value="{{ item.UserId }}"
                    [ngValue]="item.UserId"
                  >
                    {{ item.author }}
                  </option>
                </select>
              </div>
              <div class="input-group mb-3">
                <input
                  type="text"
                  class="form-control fs12 material-input"
                  placeholder="Report Name"
                  formControlName="reportName"
                />
                  <span class="input-group-text">
                    <img src="./assets/images/search-icon.svg" alt="Search" />
                  </span>
              </div>
              <div class="input-group mb-3">
                <select class="form-control fs12 material-input" formControlName="templateType">
                  <option value="" disabled selected hidden>Template Type</option>
                  <option
                    *ngFor="let item of templateTypeArray"
                    value="{{ item }}"
                    [ngValue]="item"
                  >
                    {{ item }}
                  </option>
                </select>
              </div>
              <div class="input-group mb-3">
                <input
                  class="form-control fs12 material-input"
                  #dp="bsDatepicker"
                  bsDatepicker
                  formControlName="lastRun"
                  placeholder="Last Scheduled Run"
                  [bsConfig]="{
                    isAnimated: true,
                    showWeekNumbers: false,
                    customTodayClass: 'today'
                  }"
                />
              </div>
              <div class="row justify-content-end">
                <button
                  class="btn btn-orange radius20 col-4 mt-2 fs12 fw-bold cairo-regular mx-1"
                  type="submit"
                >
                  Apply
                </button>
                <button
                  class="btn btn-orange radius20 fs12 col-4 mt-2 fw-bold cairo-regular mx-1"
                  type="button"
                  (click)="resetFilter()"
                >
                  Reset
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
</div>
<!--Filter Modal-->
