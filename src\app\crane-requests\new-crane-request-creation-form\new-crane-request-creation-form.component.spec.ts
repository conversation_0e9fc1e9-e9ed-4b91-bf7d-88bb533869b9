import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import { Router } from '@angular/router';
import { MixpanelService } from '../../services/mixpanel.service';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { NewCraneRequestCreationFormComponent } from './new-crane-request-creation-form.component';
import { of, throwError } from 'rxjs';
import moment from 'moment';

describe('NewCraneRequestCreationFormComponent', () => {
  let component: NewCraneRequestCreationFormComponent;
  let fixture: ComponentFixture<NewCraneRequestCreationFormComponent>;
  let formBuilder: UntypedFormBuilder;
  let modalRef: BsModalRef;
  let modalService: BsModalService;
  let deliveryService: DeliveryService;
  let projectService: ProjectService;
  let toastr: ToastrService;
  let socket: Socket;
  let router: Router;
  let mixpanelService: MixpanelService;

  const mockProjectService = {
    projectParent: of({ ProjectId: '123', ParentCompanyId: '456' }),
    listCraneEquipment: jest.fn().mockReturnValue(of({ data: { rows: [{ id: '1', equipmentName: 'Crane 1' }] } })),
    getCompanies: jest.fn().mockReturnValue(of({ data: [{ id: '1', companyName: 'Company 1' }] })),
    getDefinableWork: jest.fn().mockReturnValue(of({ data: [{ id: '1', DFOW: 'Work 1' }] })),
    getLocations: jest.fn().mockReturnValue(of({
      data: [{
        id: '1',
        locationPath: 'Location 1',
        isDefault: true,
        gateDetails: [{ id: '1', gateName: 'Gate 1' }],
        EquipmentId: [{ id: '1', PresetEquipmentType: { isCraneType: true } }],
        TimeZoneId: [{ id: '1', location: 'UTC' }]
      }]
    })),
    getLastCraneRequestId: jest.fn().mockReturnValue(of({ lastId: { CraneRequestId: '123' } })),
    getTimeZoneList: jest.fn().mockReturnValue(of({ data: [{ id: '1', location: 'UTC' }] })),
    getSingleProject: jest.fn().mockReturnValue(of({ data: { TimeZoneId: '1' } }))
  };

  const mockDeliveryService = {
    searchNewMember: jest.fn().mockReturnValue(of([])),
    createCraneRequest: jest.fn().mockReturnValue(of({ message: 'Success' })),
    updateCraneRequestHistory: jest.fn(),
    loginUser: of({
      id: '1',
      CompanyId: '1',
      User: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>'
      }
    }),
    getAvailableTimeSlots: jest.fn().mockReturnValue(of({
      slots: {
        AM: [{ start: '09:00', end: '10:00' }],
        PM: [{ start: '14:00', end: '15:00' }]
      }
    }))
  };

  const mockToastr = {
    success: jest.fn(),
    error: jest.fn()
  };

  const mockSocket = {
    emit: jest.fn()
  };

  const mockMixpanelService = {
    addMixpanelEvents: jest.fn()
  };

  const mockRouter = {
    navigate: jest.fn(),
    events: of({})
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [NewCraneRequestCreationFormComponent],
      imports: [ReactiveFormsModule],
      providers: [
        UntypedFormBuilder,
        { provide: BsModalRef, useValue: { hide: jest.fn() } },
        { provide: BsModalService, useValue: { show: jest.fn() } },
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: ProjectService, useValue: mockProjectService },
        { provide: ToastrService, useValue: mockToastr },
        { provide: Socket, useValue: mockSocket },
        { provide: Router, useValue: mockRouter },
        { provide: MixpanelService, useValue: mockMixpanelService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(NewCraneRequestCreationFormComponent);
    component = fixture.componentInstance;

    // Set up component inputs
    component.data = { date: '2024-03-20', currentView: 'Month' };
    component.title = 'Test Title';
    component.currentPage = 'test';
    component.selectedEventDate = null;
    component.selectedParams = '';
    component.selectfunction = jest.fn();

    formBuilder = TestBed.inject(UntypedFormBuilder);
    modalRef = TestBed.inject(BsModalRef);
    modalService = TestBed.inject(BsModalService);
    deliveryService = TestBed.inject(DeliveryService);
    projectService = TestBed.inject(ProjectService);
    toastr = TestBed.inject(ToastrService);
    socket = TestBed.inject(Socket);
    router = TestBed.inject(Router);
    mixpanelService = TestBed.inject(MixpanelService);

    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    component.craneRequestCreationForm();
    expect(component.craneRequest).toBeTruthy();
    expect(component.craneRequest.get('isEscortNeeded').value).toBe(false);
    expect(component.craneRequest.get('deliveryDate').value).toBeTruthy();
  });

  it('should handle form submission with valid data', () => {
    component.craneRequestCreationForm();
    const formValue = {
      EquipmentId: [{ id: '1' }],
      LocationId: 'loc1',
      GateId: 'gate1',
      additionalNotes: 'Test notes',
      responsiblePersons: [{ id: '1' }],
      description: 'Test description',
      deliveryDate: '2024-03-20',
      craneDeliveryStart: '2024-03-20T10:00:00',
      craneDeliveryEnd: '2024-03-20T11:00:00',
      isEscortNeeded: false,
      companies: [{ id: '1' }],
      definableFeatureOfWorks: [{ id: '1' }],
      pickUpLocation: 'Location A',
      dropOffLocation: 'Location B',
      TimeZoneId: [{ id: 'UTC' }]
    };

    component.craneRequest.patchValue(formValue);
    component.onSubmit();

    expect(mockDeliveryService.createCraneRequest).toHaveBeenCalled();
    expect(mockToastr.success).toHaveBeenCalledWith('Success', 'Success');
    expect(mockMixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Created New Crane Booking');
  });

  it('should handle form submission with empty equipment', () => {
    component.craneRequestCreationForm();

    // Set up a valid form but with empty equipment
    const formValue = {
      EquipmentId: [],
      LocationId: 'loc1',
      GateId: 'gate1',
      additionalNotes: 'Test notes',
      responsiblePersons: [{ id: '1' }],
      description: 'Test description',
      deliveryDate: '2024-03-20',
      craneDeliveryStart: '2024-03-20T10:00:00',
      craneDeliveryEnd: '2024-03-20T11:00:00',
      isEscortNeeded: false,
      companies: [{ id: '1' }],
      definableFeatureOfWorks: [{ id: '1' }],
      pickUpLocation: 'Location A',
      dropOffLocation: 'Location B',
      TimeZoneId: [{ id: 'UTC' }]
    };

    component.craneRequest.patchValue(formValue);

    // Mock the checkStringEmptyValues method to return false
    component.checkStringEmptyValues = jest.fn().mockReturnValue(false);

    // Mock the validateRecurrencePayload method to return true
    component.validateRecurrencePayload = jest.fn().mockReturnValue(true);

    // Reset the createCraneRequest mock
    mockDeliveryService.createCraneRequest.mockClear();

    component.onSubmit();

    expect(mockToastr.error).toHaveBeenCalledWith('Equipment is required');
    expect(mockDeliveryService.createCraneRequest).not.toHaveBeenCalled();
  });

  it('should handle API error during form submission', () => {
    mockDeliveryService.createCraneRequest.mockReturnValueOnce(throwError(() => ({
      message: {
        statusCode: 400,
        details: [{ field: 'Test error message' }]
      }
    })));

    component.craneRequestCreationForm();
    const formValue = {
      EquipmentId: [{ id: '1' }],
      LocationId: 'loc1',
      GateId: 'gate1',
      additionalNotes: 'Test notes',
      responsiblePersons: [{ id: '1' }],
      description: 'Test description',
      deliveryDate: '2024-03-20',
      craneDeliveryStart: '2024-03-20T10:00:00',
      craneDeliveryEnd: '2024-03-20T11:00:00',
      isEscortNeeded: false,
      companies: [{ id: '1' }],
      definableFeatureOfWorks: [{ id: '1' }],
      pickUpLocation: 'Location A',
      dropOffLocation: 'Location B',
      TimeZoneId: [{ id: 'UTC' }]
    };

    component.craneRequest.patchValue(formValue);

    // Mock the checkStringEmptyValues method to return false
    component.checkStringEmptyValues = jest.fn().mockReturnValue(false);

    // Mock the validateRecurrencePayload method to return true
    component.validateRecurrencePayload = jest.fn().mockReturnValue(true);

    component.onSubmit();

    expect(mockToastr.error).toHaveBeenCalled();
  });

  it('should reset form after successful submission', () => {
    component.craneRequestCreationForm();
    const formValue = {
      EquipmentId: [{ id: '1' }],
      LocationId: 'loc1',
      GateId: 'gate1',
      additionalNotes: 'Test notes',
      responsiblePersons: [{ id: '1' }],
      description: 'Test description',
      deliveryDate: '2024-03-20',
      craneDeliveryStart: '2024-03-20T10:00:00',
      craneDeliveryEnd: '2024-03-20T11:00:00',
      isEscortNeeded: false,
      companies: [{ id: '1' }],
      definableFeatureOfWorks: [{ id: '1' }],
      pickUpLocation: 'Location A',
      dropOffLocation: 'Location B',
      TimeZoneId: [{ id: 'UTC' }]
    };

    component.craneRequest.patchValue(formValue);
    component.onSubmit();

    expect(component.formSubmitted).toBe(false);
    expect(component.submitted).toBe(false);
  });

  it('should handle AM/PM selection', () => {
    component.selectAM();
    expect(component.isAM).toBe(true);
  });

  it('should load equipment list on initialization', () => {
    component.getOverAllEquipmentInNewDelivery();
    expect(mockProjectService.listCraneEquipment).toHaveBeenCalled();
  });

  it('should load companies on initialization', () => {
    component.newNdrgetCompanies();
    expect(mockProjectService.getCompanies).toHaveBeenCalled();
  });

  it('should load definable work on initialization', () => {
    component.getDefinable();
    expect(mockProjectService.getDefinableWork).toHaveBeenCalled();
  });

  describe('Lifecycle Methods', () => {
    it('should initialize component with ngOnInit', () => {
      component.ngOnInit();
      expect(component.craneRequest.get('deliveryDate').value).toBeTruthy();
      expect(component.craneRequest.get('craneDeliveryStart').value).toBeTruthy();
    });

    it('should handle ngAfterViewInit', () => {
      const getLastCraneRequestIdSpy = jest.spyOn(component, 'getLastCraneRequestId');
      component.ngAfterViewInit();
      expect(getLastCraneRequestIdSpy).toHaveBeenCalled();
    });

    it('should get selected date with Month view', () => {
      component.data = { date: '2024-03-20', currentView: 'Month' };
      component.getSelectedDate();
      expect(component.craneRequest.get('deliveryDate').value).toContain('03-20-2024');
    });

    it('should get selected date with Week view', () => {
      component.data = { date: '2024-03-20', currentView: 'Week' };
      component.getSelectedDate();
      expect(component.craneRequest.get('deliveryDate').value).toBeTruthy();
    });

    it('should get selected date with Day view', () => {
      component.data = { date: '2024-03-20', currentView: 'Day' };
      component.getSelectedDate();
      expect(component.craneRequest.get('deliveryDate').value).toBeTruthy();
    });

    it('should handle getSelectedDate with no data', () => {
      component.data = null;
      const setDefaultDateAndTimeSpy = jest.spyOn(component, 'setDefaultDateAndTime');
      component.getSelectedDate();
      expect(setDefaultDateAndTimeSpy).toHaveBeenCalledWith(null);
    });
  });

  describe('Form Validation', () => {
    beforeEach(() => {
      component.craneRequestCreationForm();
    });

    it('should validate required fields', () => {
      const form = component.craneRequest;
      expect(form.get('LocationId').hasError('required')).toBe(true);
      expect(form.get('GateId').hasError('required')).toBe(true);
      expect(form.get('responsiblePersons').hasError('required')).toBe(true);
      expect(form.get('description').hasError('required')).toBe(true);
    });

    it('should handle form submission with invalid form', () => {
      component.onSubmit();
      expect(component.formSubmitted).toBe(false);
      expect(mockDeliveryService.createCraneRequest).not.toHaveBeenCalled();
    });

    it('should handle form submission with missing responsible company', () => {
      const formValue = {
        EquipmentId: [{ id: '1' }],
        LocationId: 'loc1',
        GateId: 'gate1',
        companies: [],
        responsiblePersons: [{ id: '1' }],
        description: 'Test',
        deliveryDate: '2024-03-20',
        craneDeliveryStart: '2024-03-20T10:00:00',
        craneDeliveryEnd: '2024-03-20T11:00:00',
        TimeZoneId: [{ id: 'UTC' }],
        pickUpLocation: 'Location A',
        dropOffLocation: 'Location B'
      };

      component.craneRequest.patchValue(formValue);
      component.checkStringEmptyValues = jest.fn().mockReturnValue(false);
      component.onSubmit();

      expect(mockToastr.error).toHaveBeenCalledWith('Responsible Company is required');
    });
  });

  describe('Time and Duration Selection', () => {
    beforeEach(() => {
      component.craneRequestCreationForm();
    });

    it('should handle PM selection', () => {
      component.selectPM();
      expect(component.isAM).toBe(false);
    });

    it('should select hour and update duration', () => {
      // Set up form with required values
      component.craneRequest.get('EquipmentId').setValue([{ id: '1' }]);
      component.craneRequest.get('LocationId').setValue([{ id: '1' }]);
      component.craneRequest.get('craneDeliveryStart').setValue('2024-03-20T10:00:00');

      component.selectedMinute = 30;
      component.selectHour(2);
      expect(component.selectedHour).toBe(2);
      expect(component.craneRequest.get('craneDeliveryEnd').value).toBeTruthy();
    });

    it('should select minute and update duration', () => {
      // Set up form with required values
      component.craneRequest.get('EquipmentId').setValue([{ id: '1' }]);
      component.craneRequest.get('LocationId').setValue([{ id: '1' }]);
      component.craneRequest.get('craneDeliveryStart').setValue('2024-03-20T10:00:00');

      component.selectedHour = 1;
      component.selectMinute(45);
      expect(component.selectedMinute).toBe(45);
      expect(component.craneRequest.get('craneDeliveryEnd').value).toBeTruthy();
    });

    it('should toggle duration dropdown', () => {
      component.durationToggleDropdown();
      expect(component.durationisOpen).toBe(true);
      component.durationToggleDropdown();
      expect(component.durationisOpen).toBe(false);
    });

    it('should close duration dropdown', () => {
      component.durationisOpen = true;
      component.durationCloseDropdown();
      expect(component.durationisOpen).toBe(false);
    });

    it('should select time slot', () => {
      const startStr = '2024-03-20T10:00:00';
      const endStr = '2024-03-20T11:00:00';
      component.selectTime(startStr, endStr);

      expect(component.isTimeSlotChoosen).toBe(true);
      expect(component.selectedTime).toBeTruthy();
    });

    it('should handle change date', () => {
      const testDate = new Date('2024-03-20T10:00:00');
      component.changeDate(testDate);
      expect(component.NDRTimingChanged).toBe(true);
    });

    it('should handle delivery end time change detection', () => {
      component.deliveryEndTimeChangeDetection();
      expect(component.NDRTimingChanged).toBe(true);
    });
  });

  describe('Recurrence Logic', () => {
    beforeEach(() => {
      component.craneRequestCreationForm();
    });

    it('should handle daily recurrence selection', () => {
      component.onRecurrenceSelect('Daily');
      expect(component.selectedRecurrence).toBe('Daily');
      expect(component.craneRequest.get('repeatEveryType').value).toBe('Day');
    });

    it('should handle weekly recurrence selection', () => {
      component.onRecurrenceSelect('Weekly');
      expect(component.selectedRecurrence).toBe('Weekly');
      expect(component.craneRequest.get('repeatEveryType').value).toBe('Week');
    });

    it('should handle monthly recurrence selection', () => {
      component.onRecurrenceSelect('Monthly');
      expect(component.selectedRecurrence).toBe('Monthly');
      expect(component.craneRequest.get('repeatEveryType').value).toBe('Month');
    });

    it('should handle yearly recurrence selection', () => {
      component.onRecurrenceSelect('Yearly');
      expect(component.selectedRecurrence).toBe('Yearly');
      expect(component.craneRequest.get('repeatEveryType').value).toBe('Year');
    });

    it('should handle "Does Not Repeat" selection', () => {
      component.onRecurrenceSelect('Does Not Repeat');
      expect(component.selectedRecurrence).toBe('Does Not Repeat');
      expect(component.craneRequest.get('repeatEveryType').value).toBe('');
    });

    it('should handle repeat every type change for Day', () => {
      component.chooseRepeatEveryType('Day');
      expect(component.craneRequest.get('recurrence').value).toBe('Daily');
      expect(component.isRepeatWithSingleRecurrence).toBe(true);
    });

    it('should handle repeat every type change for Days', () => {
      component.chooseRepeatEveryType('Days');
      expect(component.craneRequest.get('recurrence').value).toBe('Daily');
      expect(component.isRepeatWithMultipleRecurrence).toBe(true);
    });

    it('should handle repeat every type change for Week', () => {
      component.chooseRepeatEveryType('Week');
      expect(component.craneRequest.get('recurrence').value).toBe('Weekly');
      expect(component.isRepeatWithSingleRecurrence).toBe(true);
    });

    it('should handle repeat every type change for Weeks', () => {
      component.chooseRepeatEveryType('Weeks');
      expect(component.craneRequest.get('recurrence').value).toBe('Weekly');
      expect(component.isRepeatWithMultipleRecurrence).toBe(true);
    });

    it('should handle repeat every type change for Month', () => {
      component.chooseRepeatEveryType('Month');
      expect(component.craneRequest.get('recurrence').value).toBe('Monthly');
      expect(component.isRepeatWithSingleRecurrence).toBe(true);
    });

    it('should handle repeat every type change for Year', () => {
      component.chooseRepeatEveryType('Year');
      expect(component.craneRequest.get('recurrence').value).toBe('Yearly');
      expect(component.isRepeatWithSingleRecurrence).toBe(true);
    });

    it('should handle recurrence count change with positive value', () => {
      component.craneRequest.get('recurrence').setValue('Daily');
      component.changeRecurrenceCount(2);
      expect(component.craneRequest.get('repeatEveryType').value).toBe('Days');
    });

    it('should handle recurrence count change with negative value', () => {
      component.changeRecurrenceCount(-1);
      expect(component.craneRequest.get('repeatEveryCount').value).toBe(1);
    });

    it('should handle monthly recurrence change', () => {
      component.craneRequest.get('chosenDateOfMonth').setValue(1);
      component.craneRequest.get('deliveryDate').setValue('03/20/2024');
      component.changeMonthlyRecurrence();
      expect(component.craneRequest.get('dateOfMonth').value).toBeTruthy();
    });

    it('should handle onChange for checkbox events', () => {
      const mockEvent = {
        target: { checked: true, value: 'Monday' }
      };
      component.selectedRecurrence = 'Weekly';
      component.onChange(mockEvent);
      expect(component.checkform.length).toBeGreaterThan(0);
    });
  });

  describe('Data Loading and API Calls', () => {
    beforeEach(() => {
      component.craneRequestCreationForm();
    });

    it('should get last crane request ID', () => {
      component.getLastCraneRequestId();
      expect(mockProjectService.getLastCraneRequestId).toHaveBeenCalled();
      expect(component.modalLoader).toBe(false);
    });

    it('should get locations and set default', () => {
      component.getLocations();
      expect(mockProjectService.getLocations).toHaveBeenCalled();
    });

    it('should handle location selection', () => {
      component.locationList = [{
        id: '1',
        gateDetails: [{ id: '1' }],
        EquipmentId: [{ id: '1' }],
        TimeZoneId: [{ location: 'UTC' }]
      }];

      component.locationSelected({ id: '1' });
      expect(component.selectedLocationId).toBe('1');
      expect(component.craneRequest.get('GateId').value).toBe('');
    });

    it('should get available time slots', () => {
      component.craneRequest.get('EquipmentId').setValue([{ id: '1' }]);
      component.craneRequest.get('LocationId').setValue([{ id: '1' }]);
      component.timeZone = 'UTC';

      component.getAvailableSlots('2024-03-20');
      expect(mockDeliveryService.getAvailableTimeSlots).toHaveBeenCalled();
    });

    it('should handle empty time slots', () => {
      mockDeliveryService.getAvailableTimeSlots.mockReturnValueOnce(of({
        slots: { AM: [], PM: [] }
      }));

      component.craneRequest.get('EquipmentId').setValue([{ id: '1' }]);
      component.craneRequest.get('LocationId').setValue([{ id: '1' }]);
      component.getAvailableSlots('2024-03-20');
      expect(component.isSlotsNull).toBe(true);
    });

    it('should get timezone list', () => {
      component.getTimeZoneList();
      expect(mockProjectService.getTimeZoneList).toHaveBeenCalled();
    });

    it('should handle timezone selection', () => {
      (component as any).timezoneList = [{ id: '1', location: 'UTC' }];
      component.timeZoneSelected('1');
      expect(component.selectedTimeZoneValue).toEqual({ id: '1', location: 'UTC' });
    });
  });

  describe('Utility Methods', () => {
    beforeEach(() => {
      component.craneRequestCreationForm();
    });

    it('should validate number only input', () => {
      const validEvent = { which: 50, keyCode: 50 }; // '2'
      const invalidEvent = { which: 65, keyCode: 65 }; // 'A'

      expect(component.numberOnly(validEvent)).toBe(true);
      expect(component.numberOnly(invalidEvent)).toBe(false);
    });

    it('should check string empty values with valid description', () => {
      const formValue = { description: 'Valid description', notes: 'Valid notes' };
      expect(component.checkStringEmptyValues(formValue)).toBe(false);
    });

    it('should check string empty values with empty description', () => {
      const formValue = { description: '   ', notes: 'Valid notes' };
      expect(component.checkStringEmptyValues(formValue)).toBe(true);
      expect(mockToastr.error).toHaveBeenCalledWith('Please Enter valid Company Name.', 'OOPS!');
    });

    it('should check string empty values with empty notes', () => {
      const formValue = { description: 'Valid description', notes: '   ' };
      expect(component.checkStringEmptyValues(formValue)).toBe(true);
      expect(mockToastr.error).toHaveBeenCalledWith('Please Enter valid address.', 'OOPS!');
    });

    it('should sort week days correctly', () => {
      const unsortedDays = ['Wednesday', 'Monday', 'Friday'];
      const sortedDays = component.sortWeekDays(unsortedDays);
      expect(sortedDays).toEqual(['Monday', 'Wednesday', 'Friday']);
    });

    it('should handle empty array in sortWeekDays', () => {
      const result = component.sortWeekDays([]);
      expect(result).toBeUndefined();
    });

    it('should convert start time correctly', () => {
      const date = new Date('2024-03-20');
      const result = component.convertStart(date, 10, 30);
      expect(result).toContain('GMT');
    });

    it('should check new delivery start end times', () => {
      const startTime = '2024-03-20T10:00:00';
      const endTime = '2024-03-20T11:00:00';
      expect(component.checkNewDeliveryStartEnd(startTime, endTime)).toBe(true);

      const invalidEndTime = '2024-03-20T09:00:00';
      expect(component.checkNewDeliveryStartEnd(startTime, invalidEndTime)).toBe(false);
    });

    it('should set default date and time', () => {
      const testDate = '2024-03-20';
      component.setDefaultDateAndTime(testDate);
      expect(component.deliveryStart).toBeInstanceOf(Date);
      expect(component.deliveryEnd).toBeInstanceOf(Date);
    });

    it('should set default date and time with null', () => {
      component.setDefaultDateAndTime(null);
      expect(component.deliveryStart).toBeInstanceOf(Date);
      expect(component.deliveryEnd).toBeInstanceOf(Date);
    });

    it('should generate week dates correctly', () => {
      const selectedDate = '2024-03-20';
      component.generateWeekDates(selectedDate);
      expect(component.weekDates).toHaveLength(5);
      expect(component.selectedDate).toEqual(component.weekDates[0]);
    });

    it('should select date and update form', () => {
      const day = { name: 'Wed', date: '20', fullDate: '2024-03-20' };

      // Set up form with required values
      component.craneRequest.get('EquipmentId').setValue([{ id: '1' }]);
      component.craneRequest.get('LocationId').setValue([{ id: '1' }]);

      const getAvailableSlotsSpy = jest.spyOn(component, 'getAvailableSlots');
      component.selectDate(day);

      expect(component.selectedDate).toEqual(day);
      expect(component.craneRequest.get('deliveryDate').value).toContain('03/20/2024');
      expect(getAvailableSlotsSpy).toHaveBeenCalledWith('2024-03-20');
    });

    it('should handle selectDuration', () => {
      // Set up form with required values
      component.craneRequest.get('EquipmentId').setValue([{ id: '1' }]);
      component.craneRequest.get('LocationId').setValue([{ id: '1' }]);
      component.craneRequest.get('craneDeliveryStart').setValue('2024-03-20T10:00:00');

      component.selectedHour = 2;
      component.selectedMinute = 30;
      const getAvailableSlotsSpy = jest.spyOn(component, 'getAvailableSlots');

      component.selectDuration('test');
      expect(component.selectedMinutes).toBe(150); // 2*60 + 30
      expect(getAvailableSlotsSpy).toHaveBeenCalled();
    });

    it('should scroll to time slot', () => {
      // Mock DOM elements
      const mockContainer = {
        querySelectorAll: jest.fn().mockReturnValue([
          { offsetTop: 100 }
        ]),
        offsetTop: 50
      };
      component.timeSlotsContainer = { nativeElement: mockContainer } as any;

      component.scrollToTime(0);
      expect(mockContainer.querySelectorAll).toHaveBeenCalledWith('button');
    });
  });

  describe('Modal and Form Operations', () => {
    beforeEach(() => {
      component.craneRequestCreationForm();
    });

    it('should close modal popup', () => {
      component.modalLoader = true;
      component.closeModalPopup();
      expect(component.modalLoader).toBe(false);
    });

    it('should open confirmation modal popup', () => {
      const template = {} as any;
      component.openConfirmationModalPopup(template);
      expect(modalService.show).toHaveBeenCalledWith(template, expect.any(Object));
    });

    it('should reset form with "yes" action', () => {
      component.submitted = true;
      component.formSubmitted = true;
      component.editSubmitted = true;
      component.NDRTimingChanged = true;

      component.resetForm('yes');

      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(component.NDRTimingChanged).toBe(false);
      expect(modalRef.hide).toHaveBeenCalled();
    });

    it('should reset form with "no" action', () => {
      (component as any).modalRef1 = { hide: jest.fn() } as any;
      component.resetForm('no');
      expect((component as any).modalRef1.hide).toHaveBeenCalled();
    });

    it('should handle close with dirty form', () => {
      const template = {} as any;
      component.craneRequest.markAsDirty();
      component.craneRequest.markAsTouched();
      const openConfirmationModalPopupSpy = jest.spyOn(component, 'openConfirmationModalPopup');

      component.close(template);
      expect(openConfirmationModalPopupSpy).toHaveBeenCalledWith(template);
    });

    it('should handle close with clean form', () => {
      const template = {} as any;
      const resetFormSpy = jest.spyOn(component, 'resetForm');

      component.close(template);
      expect(resetFormSpy).toHaveBeenCalledWith('yes');
    });

    it('should handle selected booking type', () => {
      component.selectedBookingtype('New Delivery Booking');
      expect(component.selectedParams).toBe('New Delivery Booking');
      expect(component.selectfunction).toHaveBeenCalledWith('New Delivery Booking', expect.any(String));
    });

    it('should handle onItemSelect', () => {
      const result = component.onItemSelect();
      expect(result).toBeUndefined();
    });

    it('should handle getSlots', () => {
      // Set up form with required values
      component.craneRequest.get('EquipmentId').setValue([{ id: '1' }]);
      component.craneRequest.get('LocationId').setValue([{ id: '1' }]);
      component.craneRequest.get('GateId').setValue('gate1');

      const getBookingDataSpy = jest.spyOn(component, 'getBookingData');
      component.getSlots();
      expect(getBookingDataSpy).toHaveBeenCalled();
    });

    it('should handle getBookingData', () => {
      component.craneRequest.get('EquipmentId').setValue([{ id: '1' }]);
      component.craneRequest.get('LocationId').setValue([{ id: '1' }]);
      component.craneRequest.get('GateId').setValue('gate1');
      component.timeZone = 'UTC';
      component.timeSlotComponent = { getEventNDR: jest.fn() } as any;

      component.getBookingData();
      expect(component.timeSlotComponent.getEventNDR).toHaveBeenCalledWith(
        [{ id: '1' }], '1', 'gate1', 'UTC', ''
      );
    });
  });

  describe('Error Handling and Edge Cases', () => {
    beforeEach(() => {
      component.craneRequestCreationForm();
    });

    it('should handle API error in createNDR', () => {
      const errorResponse = {
        message: {
          statusCode: 400,
          details: [{ field: 'error message' }]
        }
      };

      mockDeliveryService.createCraneRequest.mockReturnValueOnce(throwError(() => errorResponse));

      const payload = {
        description: 'test',
        companies: [],
        isEscortNeeded: false,
        ProjectId: '123',
        additionalNotes: '',
        EquipmentId: [],
        LocationId: '',
        craneDeliveryStart: '',
        craneDeliveryEnd: '',
        ParentCompanyId: '456',
        responsiblePersons: [],
        definableFeatureOfWorks: [],
        isAssociatedWithDeliveryRequest: false,
        pickUpLocation: '',
        dropOffLocation: '',
        recurrence: '',
        chosenDateOfMonth: false,
        dateOfMonth: '',
        monthlyRepeatType: '',
        days: [],
        repeatEveryType: '',
        repeatEveryCount: ''
      };

      component.createNDR(payload);
      expect(component.formSubmitted).toBe(false);
      expect(component.submitted).toBe(false);
    });

    it('should handle API error without message in createNDR', () => {
      mockDeliveryService.createCraneRequest.mockReturnValueOnce(throwError(() => ({})));

      const payload = {
        description: 'test',
        companies: [],
        isEscortNeeded: false,
        ProjectId: '123',
        additionalNotes: '',
        EquipmentId: [],
        LocationId: '',
        craneDeliveryStart: '',
        craneDeliveryEnd: '',
        ParentCompanyId: '456',
        responsiblePersons: [],
        definableFeatureOfWorks: [],
        isAssociatedWithDeliveryRequest: false,
        pickUpLocation: '',
        dropOffLocation: '',
        recurrence: '',
        chosenDateOfMonth: false,
        dateOfMonth: '',
        monthlyRepeatType: '',
        days: [],
        repeatEveryType: '',
        repeatEveryCount: ''
      };

      component.createNDR(payload);
      expect(mockToastr.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should handle overlap error in createNDR', () => {
      const errorResponse = { message: 'Time slot overlaps with existing booking' };
      mockDeliveryService.createCraneRequest.mockReturnValueOnce(throwError(() => errorResponse));

      const payload = {
        description: 'test',
        companies: [],
        isEscortNeeded: false,
        ProjectId: '123',
        additionalNotes: '',
        EquipmentId: [],
        LocationId: '',
        craneDeliveryStart: '',
        craneDeliveryEnd: '',
        ParentCompanyId: '456',
        responsiblePersons: [],
        definableFeatureOfWorks: [],
        isAssociatedWithDeliveryRequest: false,
        pickUpLocation: '',
        dropOffLocation: '',
        recurrence: '',
        chosenDateOfMonth: false,
        dateOfMonth: '',
        monthlyRepeatType: '',
        days: [],
        repeatEveryType: '',
        repeatEveryCount: ''
      };

      component.createNDR(payload);
      expect(mockToastr.error).toHaveBeenCalledWith('Time slot overlaps with existing booking', 'Alert!');
    });

    it('should validate recurrence payload with same start and end time', () => {
      const payload = { startPicker: '10:00', endPicker: '10:00' };
      const result = component.validateRecurrencePayload(payload);
      expect(result).toBe(false);
      expect(mockToastr.error).toHaveBeenCalledWith('Delivery Start time and End time should not be the same');
    });

    it('should validate recurrence payload with start time greater than end time', () => {
      const payload = { startPicker: '11:00', endPicker: '10:00' };
      const result = component.validateRecurrencePayload(payload);
      expect(result).toBe(false);
      expect(mockToastr.error).toHaveBeenCalledWith('Please enter From Time lesser than To Time');
    });

    it('should validate recurrence payload with invalid date range', () => {
      const payload = {
        startPicker: '10:00',
        endPicker: '11:00',
        recurrence: 'Daily',
        craneDeliveryStart: '2024-03-21',
        craneDeliveryEnd: '2024-03-20'
      };
      const result = component.validateRecurrencePayload(payload);
      expect(result).toBe(false);
      expect(mockToastr.error).toHaveBeenCalledWith('Please enter End Date greater than Start Date');
    });

    it('should show error message and reset form state', () => {
      component.formSubmitted = true;
      component.submitted = true;
      component.showErrorMessage('Test error');

      expect(mockToastr.error).toHaveBeenCalledWith('Test error');
      expect(component.formSubmitted).toBe(false);
      expect(component.submitted).toBe(false);
    });

    it('should handle formReset', () => {
      component.formSubmitted = true;
      component.submitted = true;
      component.formReset();

      expect(component.formSubmitted).toBe(false);
      expect(component.submitted).toBe(false);
    });

    it('should handle showError with error details', () => {
      const error = {
        message: {
          details: [{ field: 'error message' }]
        }
      };

      component.showError(error);
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(mockToastr.error).toHaveBeenCalledWith(['error message']);
    });
  });
});
