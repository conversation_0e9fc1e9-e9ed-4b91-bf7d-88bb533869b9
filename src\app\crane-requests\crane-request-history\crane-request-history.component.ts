import { Component, OnDestroy, OnInit } from '@angular/core';
import {
  debounceTime, filter, merge, Subscription, tap,
} from 'rxjs';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';

@Component({
  selector: 'app-crane-request-history',
  templateUrl: './crane-request-history.component.html',
})
export class CraneRequestHistoryComponent implements OnInit, OnDestroy {
  public historyList: any = [];

  public CraneRequestId: any;

  public loader = true;

  public ProjectId: any;

  public ParentCompanyId: any;

  private readonly subscription: Subscription = new Subscription();

  public constructor(
    private readonly deliveryService: DeliveryService,
    public projectService: ProjectService,
  ) {
    this.projectService.projectParent.subscribe((response5): void => {
      if (response5 !== undefined && response5 !== null && response5 !== '') {
        this.loader = true;
        this.ProjectId = response5.ProjectId;
        this.ParentCompanyId = response5.ParentCompanyId;
        this.loader = true;
      }
    });
  }

  public ngOnInit(): void {
    const historyTrigger$ = merge(
      this.deliveryService.EditCraneRequestId.pipe(
        tap((id) => {
          this.CraneRequestId = id;
        }),
      ),
      this.deliveryService.fetchData,
      this.deliveryService.fetchData1,
    ).pipe(
      debounceTime(100),
      filter(() => !!this.CraneRequestId),
    );

    this.subscription.add(
      historyTrigger$.subscribe(() => {
        this.getHistory();
      }),
    );
  }

  public getHistory(): void {
    this.loader = true;
    const param = {
      CraneRequestId: this.CraneRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    this.deliveryService.getCraneRequestHistory(param).subscribe((res): void => {
      this.historyList = res.data;
      // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
      this.historyList = this.historyList.filter((data) => data.type !== 'comment');

      this.loader = false;
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
