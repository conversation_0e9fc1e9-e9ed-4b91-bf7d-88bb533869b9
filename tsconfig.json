{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "sourceMap": true,
    "declaration": false,
    "module": "es2020",
    "moduleResolution": "node",
    "experimentalDecorators": true,
    "target": "ES2022",
    "typeRoots": [
      "node_modules/@types"
    ],
    "types": ["google.maps"],
    "lib": [
      "ES2022",
      "dom"
    ],
    "strict": false,
    "useDefineForClassFields": false,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
  },
  "angularCompilerOptions": {
    "fullTemplateTypeCheck": true,
    "strictInjectionParameters": true,
    "skipLibCheck": true
  }
}
