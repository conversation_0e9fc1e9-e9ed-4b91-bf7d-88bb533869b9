import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ResizableComponent } from './resizable.component';
import { Component, ViewChild } from '@angular/core';
import { By } from '@angular/platform-browser';
import { ResizableModule } from './resizable.module';

// Create a test host component to test the ResizableComponent
@Component({
  template: `<th resizable #resizableComponent>Test Header</th>`
})
class TestHostComponent {
  @ViewChild('resizableComponent', { static: true }) resizableComponent!: ResizableComponent;
}

describe('ResizableComponent', () => {
  let hostComponent: TestHostComponent;
  let hostFixture: ComponentFixture<TestHostComponent>;
  let resizableComponent: ResizableComponent;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TestHostComponent],
      imports: [ResizableModule]
    }).compileComponents();

    hostFixture = TestBed.createComponent(TestHostComponent);
    hostComponent = hostFixture.componentInstance;
    hostFixture.detectChanges();
    resizableComponent = hostComponent.resizableComponent;
  });

  it('should create', () => {
    expect(resizableComponent).toBeTruthy();
  });

  it('should initialize with width as null', () => {
    expect(resizableComponent.width).toBeNull();
  });

  it('should update width when onResize is called', () => {
    // Initial width should be null
    expect(resizableComponent.width).toBeNull();

    // Call onResize with a test width
    const testWidth = 150;
    resizableComponent.onResize(testWidth);

    // Width should be updated
    expect(resizableComponent.width).toBe(testWidth);
  });

  it('should have the correct host binding for width style', () => {
    // Set width through onResize
    const testWidth = 200;
    resizableComponent.onResize(testWidth);
    hostFixture.detectChanges();

    // Get the th element
    const thElement = hostFixture.debugElement.query(By.css('th')).nativeElement;

    // Check if the width style is applied correctly
    expect(thElement.style.width).toBe(`${testWidth}px`);
  });

  it('should render the content inside the component', () => {
    const element = hostFixture.nativeElement;
    expect(element.textContent).toContain('Test Header');
  });

  it('should have a wrapper div with content and bar', () => {
    const wrapperDiv = hostFixture.debugElement.query(By.css('.wrapper'));
    expect(wrapperDiv).toBeTruthy();

    const contentDiv = hostFixture.debugElement.query(By.css('.content'));
    expect(contentDiv).toBeTruthy();

    const barDiv = hostFixture.debugElement.query(By.css('.bar'));
    expect(barDiv).toBeTruthy();
  });

  it('should have the resizable directive on the bar element', () => {
    const barElement = hostFixture.debugElement.query(By.css('.bar'));

    // Check if the resizable directive is applied to the bar element
    // This is a bit tricky to test directly, but we can check if the element exists
    expect(barElement).toBeTruthy();
  });

  it('should handle multiple resize operations', () => {
    // First resize
    resizableComponent.onResize(100);
    expect(resizableComponent.width).toBe(100);

    // Second resize
    resizableComponent.onResize(150);
    expect(resizableComponent.width).toBe(150);

    // Third resize
    resizableComponent.onResize(50);
    expect(resizableComponent.width).toBe(50);
  });

  it('should handle resize with zero value', () => {
    resizableComponent.onResize(0);
    expect(resizableComponent.width).toBe(0);
  });

  it('should handle resize with negative value', () => {
    // Assuming the component should handle negative values
    // (though in practice this might not be desirable)
    resizableComponent.onResize(-50);
    expect(resizableComponent.width).toBe(-50);
  });
});
