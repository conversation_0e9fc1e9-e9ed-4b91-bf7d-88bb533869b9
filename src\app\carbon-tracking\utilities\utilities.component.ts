import { Component, TemplateRef, ViewChild } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { Title } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { ProjectService } from '../../services/profile/project.service';
import { AuthService } from '../../services/auth/auth.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { MixpanelService } from '../../services/mixpanel.service';
import { UtilitesDetailsComponent } from './utilites-details/utilites-details.component';
import { GoogleMap } from '@angular/google-maps';


@Component({
  selector: 'app-utilities',
  templateUrl: './utilities.component.html',
  })
export class UtilitiesLogComponent {

  @ViewChild(GoogleMap, { static: false }) map!: GoogleMap;


  public files: NgxFileDropEntry[] = [];

  public currentPageNo = 1;

  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public modalRef2: BsModalRef;

  public ProjectId: any;

  public utilitiesLogList: any = [];

  public memberList: any = [];

  public submitted = false;

  public formSubmitted = false;

  public formEditSubmitted = false;

  public fileData : any = [];

  public formData: FormData;

  public editSubmitted = false;

  public editIndex: any;

  public currentDeleteId: any;

  public deleteSubmitted = false;

  public loader = true;

  public fileUploadloader = false;

  public pageSize = 25;

  public pageNo = 1;

  public totalCount = 0;

  public utilitiesDetailsForm: UntypedFormGroup;

  public equipmentEditForm: UntypedFormGroup;

  public selectAll = false;

  public deleteIndex: any = [];

  public remove = false;

  public lastId: any = 0;

  public filterCount = 0;

  public equipmentList = [];

  public companiesList = [];

  public filterForm: UntypedFormGroup;

  public search = '';

  public equipmentTypeList: any = [];

  public utilityUnitsList = ['kWh', 'Therms','Gallons','CCF'];

  public utilityTypeList = ['Electricity', 'Water' , 'Natural Gas','Fuel'];

  public modalLoader = false;

  public ParentCompanyId: any;

  public sort = 'DESC';

  public sortColumn = 'id';

  public presetEquipmentTypeList = [];

  public showSearchbar = false;

  public authUser: any = {};

  public getMappedRequestLoader: boolean = false;

  public mappedRequestList: any[];

  public craneEquipmentDropdownList: any;

  public nonCraneEquipmentDropdownList: any;

  public deactivateEquipmentLoader: boolean;

  public geoCoder: any;

  public equipmentData: any;
  zipcode: any;

  public constructor(
    private readonly modalService: BsModalService,
    public projectService: ProjectService,
    private readonly titleService: Title,
    private readonly mixpanelService: MixpanelService,
    private readonly deliveryService: DeliveryService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly authService: AuthService,
    private readonly toastr: ToastrService,
  ) {
    this.titleService.setTitle('Follo - Equipment');
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
      }
    });
    this.projectService.projectId.subscribe((res): void => {
      this.ProjectId = res;
      this.getZipCode(res);
      if (res !== undefined && res !== null && res !== '') {
        this.loader = true;
        this.getUtilities();
      }
    });
    this.projectService.ParentCompanyId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ParentCompanyId = res;
        this.getUtilities();
      }
    });
    this.equipmentForm();
    this.editEquipmentForm();
    this.filterDetailsForm();
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'toggle':
          this.toggleChildVisibility(data);
          break;
        case 'remove':
          this.removeFile(data, item);
          break;
        case 'clear':
          this.clear();
          break;
        case 'filter':
          this.openFilterModal(data);
          break;
        default:
          break;
      }
    }
  }

  getZipCode(projectId: string): void {
    if (projectId !== null) {
      this.projectService.getSingleProject({ ProjectId: projectId }).subscribe(response => {
        this.geoCoder = new google.maps.Geocoder();

        if (!this.geoCoder) {
          console.error('Google Maps Geocoder is not initialized.');
          return;
        }

        this.geoCoder.geocode({ address: response.data.projectLocation }, (results, status) => {
          if (status === 'OK' && results.length > 0) {
            const addressComponents = results[0].address_components;
            const zipComponent = addressComponents.find(component =>
              component.types.includes('postal_code')
            );

            if (zipComponent) {
              console.log('ZIP Code:', zipComponent.long_name);
              this.zipcode = zipComponent.long_name
            } else {
              console.log('ZIP Code not found');
            }
          } else {
            console.error('Geocode was not successful for the following reason:', status);
          }
        });
      })
    }
  }

  public getMembers(): void {
    this.modalLoader = true;
    const param = {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.pageNo,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getRegisteredMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
        this.modalLoader = false;
      }
    });
  }

  public sortByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getUtilities();
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.pageNo = 1;
    this.getUtilities();
  }

  public dropped(files: NgxFileDropEntry[]): void {
    this.files = files;
    this.fileData.push(this.files);

    const uploadParams = {
      projectId: this.ProjectId,
      zipcode: this.zipcode,
    };

    const validPdfFiles = this.extractValidPdfFiles(this.fileData);

    validPdfFiles.forEach(({ file, indexPath }) => {
      this.processPdfFile(file, indexPath, uploadParams);
    });

    this.prepareFormData(this.fileData);
  }

  public extractValidPdfFiles(
    fileGroups: any[][],
  ): { file: FileSystemFileEntry; indexPath: [number, number] }[] {
    const validFiles = [];

    fileGroups.forEach((group, i) => {
      group.forEach((data, index) => {
        const extension = data.relativePath.split('.').pop()?.toLowerCase();
        if (extension === 'pdf') {
          if (data.fileEntry.isFile) {
            validFiles.push({ file: data.fileEntry, indexPath: [i, index] });
          }
        } else {
          this.fileData.splice(i);
          this.toastr.error(
            'Please select a valid file. Supported file format (.jpg,.jpeg,.png,.pdf,.doc)',
            'OOPS!',
          );
        }
      });
    });

    return validFiles;
  }

  public processPdfFile(
    fileEntry: FileSystemFileEntry,
    [i, index]: [number, number],
    uploadParams: any,
  ): void {
    const formData = new FormData();
    fileEntry.file((_file: File) => {
      this.fileData[i][index].extension = 'pdf';
      const reader = new FileReader();
      reader.readAsDataURL(_file);
      reader.onload = () => {
        this.fileData[i][index].image = reader.result;
      };

      formData.append('utilities', _file);
      this.loader = true;

      this.projectService.utilitiesUpload(uploadParams, formData).subscribe((response: any) => {
        this.loader = false;
        if (response) {
          this.populateUtilityDetails(response.data['Utility Invoice'], response.invoice_URL);
        }
      });
    });
  }

  public populateUtilityDetails(utilityDetails: any, invoiceUrl: string): void {
    this.utilitiesDetailsForm.get('bill_url').setValue(invoiceUrl);
    this.utilitiesDetailsForm.get('utilityType').setValue(utilityDetails['Utility type']);
    this.utilitiesDetailsForm.get('utilityUnits').setValue(utilityDetails['Utility Units']);

    if (utilityDetails['quantity used']) {
      this.utilitiesDetailsForm.get('details').setValue(utilityDetails['quantity used']);
    } else {
      const totalQuantity = utilityDetails.Items.reduce(
        (sum: number, item: any) => sum + Number(item.Quantity), 0,
      );
      this.utilitiesDetailsForm.get('details').setValue(totalQuantity);
    }

    const cost = utilityDetails['Utility Cost'] || utilityDetails['Total Cost'];
    this.utilitiesDetailsForm.get('bill_amount').setValue(cost);
    this.utilitiesDetailsForm.get('item_details').setValue(utilityDetails.Items);

    const periodKey = utilityDetails['Utlity Billing Period'] || utilityDetails['Utility Billing Period'];
    if (periodKey) {
      const billingDate = new Date(periodKey.split('-')[0]);
      const month = billingDate.toLocaleString('default', { month: 'long' });
      const year = billingDate.getFullYear();
      this.utilitiesDetailsForm.get('logMonth').setValue(`${month} ${year}`);
    }
  }

  public prepareFormData(fileGroups: any[][]): void {
    this.formData = new FormData();

    fileGroups.forEach((group) => {
      group.forEach((data) => {
        const { fileEntry } = data;
        fileEntry.file((_file: File) => {
          this.utilitiesDetailsForm.get('bill_name').setValue(_file.name);
          this.formData.append('attachement', _file, data.relativePath);
        });
      });
    });
  }


  public openDetailsModal(item): void {
    const initialState =
    {
      data: item,
      title: 'Modal with component',
    };
    this.modalRef2 = this.modalService.show(UtilitesDetailsComponent, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-delivery-popup custom-modal',
      initialState,
    });
    this.modalRef2.content.closeBtnName = 'Close';
  }

  toggleChildVisibility(utility: any) {
    utility.isExpanded = !utility.isExpanded;
  }

  public getSearchEquipment(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.pageNo = 1;
    this.search = data;
    this.getUtilities();
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.search = '';
    this.pageNo = 1;
    this.filterDetailsForm();
    this.getUtilities();
    this.modalRef.hide();
    this.utilitiesDetailsForm.reset();
  }

  public fileOver(_event: any): void { /* */ }

  public fileLeave(_event: any): void { /* */ }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.utilitiesDetailsForm.reset();
      this.submitted = false;
      this.formSubmitted = false;
      this.editSubmitted = false;
      this.formEditSubmitted = false;
      this.modalRef.hide();
    }
  }

  public close(template: TemplateRef<any>, form: string): void {
    this.fileData = [];
    if (form === 'deactivateEquipment') {
      this.modalRef.hide();
      this.getUtilities();
    }
    if (form === 'newequipment') {
      if (this.utilitiesDetailsForm.dirty) {
        this.closeModalPopup(template);
      } else {
        this.resetForm('yes');
      }
    } else if (this.equipmentEditForm.dirty) {
      this.closeModalPopup(template);
    } else {
      this.resetForm('yes');
    }
  }

  public closeModalPopup(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public getUtilities(): void {
    this.loader = true;
    if (this.ProjectId && this.ParentCompanyId) {
      const param = {
        ProjectId: this.ProjectId,
        pageSize: this.pageSize,
        pageNo: this.pageNo,
        ParentCompanyId: this.ParentCompanyId,
      };
      let payload: any = {};
      if (this.filterForm !== undefined) {
        payload = {
          idFilter: +this.filterForm.value.idFilter,
          nameFilter: this.filterForm.value.nameFilter,
          companyNameFilter: this.filterForm.value.companyNameFilter,
          typeFilter: this.filterForm.value.typeFilter,
          memberFilter: +this.filterForm.value.memberFilter,
          search: this.search,
        };
      }
      payload.search = this.search;
      payload.ParentCompanyId = this.ParentCompanyId;
      payload.sort = this.sort;
      payload.sortByField = this.sortColumn;
      payload.isFilter = false;
      payload.showActivatedAlone = false;
      payload.projectId = this.ProjectId;
      this.projectService.listUtilitiesLog(param, payload).subscribe((response: any): void => {
        if (response) {
          const responseData = response.data;
          this.loader = false;
          this.utilitiesLogList = responseData.rows;
          this.totalCount = responseData.count;
        }
      });
    }
  }

  public switchEquipment(data, equipmentId): void {
    const equipmentObject = data;
    if (+data.equipmentDetails[0].Equipment.id !== +equipmentId) {
      equipmentObject.changedEquipmentId = +equipmentId;
    } else {
      equipmentObject.changedEquipmentId = null;
    }
    return equipmentObject;
  }

  public onStatusChange(event: Event) {
    const isChecked = (event.target as HTMLInputElement).checked;
    console.log('Switch status:', isChecked);  // Use this to debug or perform any action
  }


  public deactiveEquipment(): void {
    this.deactivateEquipmentLoader = true;
    let filterChangedEquipmentData = [];
    filterChangedEquipmentData = this.mappedRequestList.filter(
      (obj): any => obj.changedEquipmentId && obj.changedEquipmentId !== null,
    );
    if (
      filterChangedEquipmentData
      && filterChangedEquipmentData.length > 0
      && this.mappedRequestList.length === filterChangedEquipmentData.length
    ) {
      const payload = {
        id: this.equipmentData.id,
        equipmentSwitchedRequests: filterChangedEquipmentData,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.projectService.deactiveEquipment(payload).subscribe({
        next: (response): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.getUtilities();
            filterChangedEquipmentData = [];
            this.modalRef.hide();
            this.equipmentData = '';
            this.deactivateEquipmentLoader = false;
          }
        },
        error: (deactivateGateErr): void => {
          if (deactivateGateErr.message?.statusCode === 400) {
            this.showError(deactivateGateErr);
          } else if (!deactivateGateErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deactivateGateErr.message, 'OOPS!');
          }
        },
      });
    } else if (this.mappedRequestList.length === 0) {
      const payload = {
        id: this.equipmentData.id,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.projectService.deactiveEquipment(payload).subscribe({
        next: (response): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.getUtilities();
            this.modalRef.hide();
            this.equipmentData = '';
            this.deactivateEquipmentLoader = false;
          }
        },
        error: (deactivateGateErr): void => {
          if (deactivateGateErr.message?.statusCode === 400) {
            this.showError(deactivateGateErr);
          } else if (!deactivateGateErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deactivateGateErr.message, 'OOPS!');
          }
        },
      });
    } else {
      this.deactivateEquipmentLoader = false;
    }
  }

  public changePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.getUtilities();
  }

  public changePageNo(pageNo: number): void {
    this.currentPageNo = pageNo;
    this.getUtilities();
  }

  public equipmentForm(): void {
    this.utilitiesDetailsForm = this.formBuilder.group({
      logMonth: ['', Validators.compose([Validators.required])],
      utilityType: [''],
      details: ['', Validators.compose([Validators.required])],
      utilityUnits: [''],
      bill_url: [''],
      bill_name: [''],
      bill_amount: ['' , Validators.compose([Validators.required])],
      item_details: [''],
    });
  }

  public getEquipmentsList(): void {
    this.loader = true;
    if (this.ProjectId && this.ParentCompanyId) {
      const param = {
        ProjectId: this.ProjectId,
        pageSize: this.pageSize,
        pageNo: this.pageNo,
        ParentCompanyId: this.ParentCompanyId,
      };
      let payload: any = {};
      if (this.filterForm !== undefined) {
        payload = {
          idFilter: +this.filterForm.value.idFilter,
          nameFilter: this.filterForm.value.nameFilter,
          companyNameFilter: this.filterForm.value.companyNameFilter,
          typeFilter: this.filterForm.value.typeFilter,
          memberFilter: +this.filterForm.value.memberFilter,
          search: this.search,
        };
      }
      payload.search = this.search;
      payload.ParentCompanyId = this.ParentCompanyId;
      payload.sort = this.sort;
      payload.sortByField = this.sortColumn;
      payload.isFilter = false;
      payload.showActivatedAlone = false;
      this.projectService.listEquipment(param, payload).subscribe((response: any): void => {
        if (response) {
          const responseData = response.data;
          this.lastId = response.lastId;
          this.loader = false;
          this.equipmentList = responseData.rows;
        }
      });
    }
  }

  public getCompanies(): void {
    this.loader = true;
    const param = {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.currentPageNo,
      ParentCompanyId: this.ParentCompanyId,
    };
    let payload: any = {};
    payload.search = this.search;
    payload.sort = this.sort;
    payload.sortByField = this.sortColumn;
    payload.inviteMember = false;
    this.projectService.listCompany(param, payload).subscribe((response: any): void => {
      if (response) {
        const responseData = response.data;
        this.loader = false;
        this.companiesList = responseData.rows;
      }
    });
  }




  public activateEquipment(data): void {
    const payload = {
      id: data.id,
      equipmentName: data.equipmentName.trim(),
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      isActive: true,
    };
    this.projectService.editEquipment(payload).subscribe({
      next: (response): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.getUtilities();
        }
      },
      error: (activateEquipmentErr): void => {
        if (activateEquipmentErr.message?.statusCode === 400) {
          this.showError(activateEquipmentErr);
        } else if (!activateEquipmentErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(activateEquipmentErr.message, 'OOPS!');
        }
      },
    });
  }

  public editEquipmentForm(): void {
    this.equipmentEditForm = this.formBuilder.group({
      equipmentName: ['', Validators.compose([Validators.required])],
      EquipmentType: ['', Validators.compose([Validators.required])],
      id: [''],
      equipmentAutoId: [''],
      controlledBy: ['', Validators.compose([Validators.required])],
    });
  }

  public checkStringEmptyValues(formValue: { EquipmentName: string }): boolean {
    return false;
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    if (this.utilitiesDetailsForm.invalid) {
      this.formSubmitted = false;
      return;
    }
    const formValue = this.utilitiesDetailsForm.value;
    if (!this.checkStringEmptyValues(formValue)) {
      const payload = {
        logMonth: formValue.logMonth,
        utilityType: formValue.utilityType,
        details: formValue.details,
        utilityUnits: formValue.utilityUnits,
        bill_url: formValue.bill_url,
        bill_name: formValue.bill_name,
        bill_amount: formValue.bill_amount,
        item_details: formValue.item_details,
        invoice_url : formValue.invoice_url,
        projectId: this.ProjectId
      };
      this.projectService.addUtilitiesLog(payload).subscribe({
        next: (response: any): void => {
          if (response) {
            this.fileData = [];
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Added Utilities');
            this.utilitiesDetailsForm.reset();
            this.submitted = false;
            this.formSubmitted = false;
            this.resetForm('yes');
            this.getUtilities();
            this.modalRef.hide();
          }
        },
        error: (addEquipmentError): void => {
          this.submitted = false;
          this.formSubmitted = false;
          if (addEquipmentError.message?.statusCode === 400) {
            this.showError(addEquipmentError);
            this.formSubmitted = false;
          } else if (!addEquipmentError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(addEquipmentError.message, 'OOPS!');
          }
        },
      });
    } else {
      this.toastr.error('Please Enter valid Equipment Name/Type.', 'OOPS!');
      this.submitted = false;
      this.formSubmitted = false;
    }
  }

  public onEditSubmit(): void {
    this.editSubmitted = true;
    this.formEditSubmitted = true;
    if (this.equipmentEditForm.invalid) {
      this.formEditSubmitted = false;
      return;
    }
    const formValue = this.equipmentEditForm.value;
    if (!this.checkStringEmptyValues(formValue)) {
      const payload = {
        equipmentName: formValue.equipmentName.trim(),
        PresetEquipmentTypeId: +formValue.EquipmentType,
        controlledBy: formValue.controlledBy,
        id: formValue.id,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.projectService.editEquipment(payload).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Edited  Equipment');
            this.editSubmitted = false;
            this.formEditSubmitted = false;
            this.equipmentEditForm.reset();
            this.getUtilities();
            this.modalRef.hide();
          }
        },
        error: (editEquipmentError): void => {
          this.submitted = false;
          this.formEditSubmitted = false;
          if (editEquipmentError.message?.statusCode === 400) {
            this.showError(editEquipmentError);
            this.formEditSubmitted = false;
          } else if (!editEquipmentError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(editEquipmentError.message, 'OOPS!');
          }
        },
      });
    } else {
      this.toastr.error('Please Enter valid Equipment Name/Type.', 'OOPS!');
      this.submitted = false;
      this.formEditSubmitted = false;
    }
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.editSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public openModal(template: TemplateRef<any>): void {
    this.getMembers();
    this.getEquipmentsList();
    this.getCompanies();
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-delivery-popup custom-modal',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public resetAndClose(): void {
    this.utilitiesDetailsForm.reset();
    this.submitted = false;
    this.formSubmitted = false;
    this.editSubmitted = false;
    this.formEditSubmitted = false;
    this.modalRef.hide();
  }

  public openDeleteModal(index: number, template: TemplateRef<any>): void {
    if (index !== -1) {
      this.deleteIndex[0] = this.utilitiesLogList[index].id;
      this.currentDeleteId = index;
      this.remove = false;
    } else if (index === -1) {
      this.remove = true;
    }
    this.openModal(template);
  }

  public removeFile(firstIndex: string | number, i: string | number): void {
    this.formData.delete(this.fileData[firstIndex][i].relativePath);
    this.fileData[firstIndex].splice(i);
    this.fileData.splice(firstIndex);
  }

  public checkSelectedRow(): boolean {
    if (this.selectAll) {
      return false;
    }
    const index = this.utilitiesLogList.findIndex(
      (item: { isChecked: boolean }): boolean => item.isChecked === true,
    );
    if (index !== -1) {
      return false;
    }
    return true;
  }

  public setSelectedItem(index: string | number): void {
    this.utilitiesLogList[index].isChecked = !this.utilitiesLogList[index].isChecked;
  }

  public openFilterModal(template: TemplateRef<any>): void {
    this.getMembers();
    this.modalRef = this.modalService.show(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-sm filter-popup custom-modal',
    });
  }

  public deleteEquipment(): void {
    this.deleteSubmitted = true;
    this.projectService
      .deleteEquipment({
        id: this.deleteIndex,
        ProjectId: this.ProjectId,
        isSelectAll: this.selectAll,
        ParentCompanyId: this.ParentCompanyId,
      })
      .subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Deleted  Equipment');
            this.getUtilities();
            this.deleteSubmitted = false;
            this.modalRef.hide();
          }
        },
        error: (deleteEquipmentError): void => {
          this.deleteSubmitted = false;
          this.getUtilities();
          if (deleteEquipmentError.message?.statusCode === 400) {
            this.showError(deleteEquipmentError);
          } else if (!deleteEquipmentError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deleteEquipmentError.message, 'OOPS!');
          }
        },
      });
  }

  public selectAllEquipmentsData(): void {
    this.selectAll = !this.selectAll;
    if (this.selectAll) {
      this.utilitiesLogList.map((obj: any, index: string | number): void => {
        this.utilitiesLogList[index].isChecked = true;
        return null;
      });
    } else {
      this.utilitiesLogList.map((obj: any, index: string | number): void => {
        this.utilitiesLogList[index].isChecked = false;
        return null;
      });
    }
  }

  public alphaNumericForEquipments(event: { which: any; keyCode: any }): boolean {
    const key = event.which ? event.which : event.keyCode;
    const condition1 = key >= 65 && key <= 90;
    const condition2 = key >= 97 && key <= 128;
    const condition3 = key === 8 || key === 32;
    if (condition3) {
      return true;
    }
    if (key > 31 && (key < 48 || key > 57) && !condition1 && !condition2) {
      return false;
    }
    return true;
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('idFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('nameFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('companyNameFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('typeFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('memberFilter').value !== '') {
      this.filterCount += 1;
    }
    this.pageNo = 1;
    this.getUtilities();
    this.modalRef.hide();
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      idFilter: [''],
      nameFilter: [''],
      companyNameFilter: [''],
      typeFilter: [''],
      memberFilter: [''],
    });
  }

  public removeItem(): void {
    this.deleteSubmitted = true;
    if (this.selectAll) {
      this.deleteEquipment();
    } else {
      this.utilitiesLogList.forEach((element: { isChecked: any; id: any }): void => {
        if (element.isChecked) {
          this.deleteIndex.push(element.id);
        }
      });
      this.deleteEquipment();
    }
  }

  public openEditModal(index: string | number, template: TemplateRef<any>): void {
    this.editIndex = index;
    this.equipmentEditForm.get('id').setValue(this.utilitiesLogList[index].id);
    this.equipmentEditForm
      .get('equipmentAutoId')
      .setValue(this.utilitiesLogList[index].equipmentAutoId);
    this.equipmentEditForm.get('equipmentName').setValue(this.utilitiesLogList[index].equipmentName);
    this.equipmentEditForm
      .get('EquipmentType')
      .setValue(this.utilitiesLogList[index].PresetEquipmentType?.id);
    this.equipmentEditForm.get('controlledBy').setValue(this.utilitiesLogList[index].controlledBy);
    this.openModal(template);
  }
}
function elseif(arg0: boolean) {
  throw new Error('Function not implemented.');
}
