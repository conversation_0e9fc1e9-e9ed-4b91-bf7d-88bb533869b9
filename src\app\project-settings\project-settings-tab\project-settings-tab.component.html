<div class="fs18 fw-bold cairo-regular my-5 text-black text-center" *ngIf="loader">
  Loading...
</div>
<section class="page-section pt-md-50px py-0 px-4">
  <div class="page-inner-content">
    <div *ngIf="!loader" class="project-settings-tab pt-0">
      <div class="max-container">
        <form
          name="form"
          class="custom-material-form"
          [formGroup]="projectSettingsFormData"
          novalidate
        >
          <div class="d-flex justify-content-between align-items-center mb-0">
            <div>
              <h1 class="section-heading fw-bold">
                Auto Approve Deliveries
                <span
                  class="add-label color-orange c-pointer"
                  (click)="openModal(approveModal)" (keydown)="handleToggleKeydown($event, 'open', approveModal)"
                  *ngIf="!projectSettingsFormData.value.isAutoApprovalEnabled"
                  >+ Add</span
                >
              </h1>
              <p class="info-content mt-3 fs12">
                Use this setting to automatically approve all items or items from particular
                companies or users on your project
              </p>
            </div>
            <div>
              <button
                type="button"
                class="btn btn-orange-dark rounded-pill px-3 py-1 fs12 mb-5"
                (click)="submit()"
                [disabled]="updateLoader && projectSettingsFormData.valid"
              >
                <em
                  class="fa fa-spinner"
                  aria-hidden="true"
                  *ngIf="updateLoader && projectSettingsFormData.valid"
                ></em>
                <span class="text-white">Save</span>
              </button>
            </div>
          </div>
          <div class="switches-container mb-0">
            <input
              type="radio"
              id="switchAutoApprove"
              name="switchApprove"
              value="Auto-approve All"
              (change)="changeAutoApprovalOrCustom('auto-approve')"
              [checked]="projectSettingsFormData.value.isAutoApprovalEnabled"
            />
            <input
              type="radio"
              id="switchCustom"
              name="switchApprove"
              value="Custom"
              [checked]="!projectSettingsFormData.value.isAutoApprovalEnabled"
              (change)="changeAutoApprovalOrCustom('custom')"
            />
            <label for="switchAutoApprove">Auto-approve All</label>
            <label for="switchCustom">Custom</label>
            <div class="switch-wrapper">
              <div class="switch d-block border-0">
                <div>Auto-approve All</div>
                <div>Custom</div>
              </div>
            </div>
          </div>
          <div class="tag-list my-3" *ngIf="!projectSettingsFormData.value.isAutoApprovalEnabled">
            <span class="selected-tag" *ngFor="let item of getautoApprovalMembers"
              >{{ item?.User?.firstName }} {{ item?.User?.lastName }} ( {{ item?.User?.email }} )
              <span class="close-tag" (click)="removeMemberFromAutoApproval(item?.id)" (keydown)="handleToggleKeydown($event,'remove', item?.id)"
                >x</span
              ></span
            >
          </div>
          <h1 class="section-heading fw-bold mt-4 mb-3">Default calendar colors</h1>
          <button
            type="button"
            class="btn btn-orange-outline set-default-outline color-orange rounded-pill px-3 py-1 fs12 mb-0"
          >
            <span class="fw-bold" (click)="setDefault('color')"  (keydown)="handleToggleKeydown($event, 'color')">Reset to default</span>
          </button>
          <div class="table-responsive project-settings-table custom-radio-option">
            <table aria-describedby="table-project-settings" class="table">
              <thead>
                <tr>
                  <th scope="col" class="fs12 ps-0">Status</th>
                  <th scope="col" class="fs12 ps-0">
                    <ul class="list-group list-group-horizontal-md projectsettings-listgroupitems">
                      <li class="list-group-item pt-4">
                        <div class="form-check form-check-inline me-0">
                          <input class="form-check-input" type="radio" name="customRadioInline1"  id="customRadioInline1"
                          [checked]="checkbox1" (change)="chooseLegendColor(true)">
                          <label class="form-check-label" for="customRadioInline1">Text Color</label>
                        </div>
                      </li>
                      <li class="list-group-item">
                        <div class="primary-tooltip">
                          <div
                            class="dot-border-info location-border-info tooltip project-settings-tooltip">
                            <span class="fw700 info-icon fs12">i</span>
                            <span class="tooltiptext tooltiptext-info settings-tooltiptext-info">
                              Use this color as Legend color for Calendars</span>
                          </div>
                        </div>
                      </li>
                    </ul>
                  </th>
                  <th scope="col" class="fs12 ps-0">
                    <ul class="list-group list-group-horizontal-md projectsettings-listgroupitems">
                      <li class="list-group-item pt-4">
                        <div class="form-check form-check-inline me-0">
                          <input class="form-check-input" type="radio" name="customRadioInline2"  id="customRadioInline2"
                          [checked]="!checkbox1" (change)="chooseLegendColor(false)">
                          <label class="custom-control-label align-items-center" for="customRadioInline2">Background Color</label>
                        </div>
                      </li>
                      <li class="list-group-item">
                        <div class="primary-tooltip">
                          <div
                            class="dot-border-info location-border-info tooltip project-settings-tooltip">
                            <span class="fw700 info-icon fs12">i</span>
                            <span class="tooltiptext tooltiptext-info settings-tooltiptext-info">
                              Use this color as Legend color for Calendars</span>
                          </div>
                        </div>
                      </li>
                    </ul>
                  </th>
                  <th scope="col" class="fs12 ps-0">Preview</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <th scope="row" class="fw600 fs12 ps-0">Approved</th>
                  <td class="pt-2">
                    <div>
                      <input
                        (colorPickerChange)="onColorPickerChange('approvedFontColor', $event)"
                        [style.background]="projectSettingsFormData.get('approvedFontColor').value"
                        [colorPicker]="projectSettingsFormData.get('approvedFontColor').value"
                        class="s-box"
                      />
                    </div>
                  </td>
                  <td class="pt-2">
                    <div>
                      <input
                        (colorPickerChange)="onColorPickerChange('approvedBackgroundColor', $event)"
                        [style.background]="
                          projectSettingsFormData.get('approvedBackgroundColor').value"
                        [colorPicker]="projectSettingsFormData.get('approvedBackgroundColor').value"
                        class="s-box bg-box"
                      />
                    </div>
                  </td>
                  <td class="ps-0 py-1">
                    <span class="fw700" [ngStyle]="approved">Delivery name</span>
                  </td>
                </tr>
                <tr>
                  <th scope="row" class="fw600 fs12 ps-0">In Review</th>
                  <td class="pt-2">
                    <div>
                      <input
                        (colorPickerChange)="onColorPickerChange('pendingFontColor', $event)"
                        [style.background]="projectSettingsFormData.get('pendingFontColor').value"
                        [colorPicker]="projectSettingsFormData.get('pendingFontColor').value"
                        class="s-box"
                      />
                    </div>
                  </td>
                  <td class="pt-2">
                    <div>
                      <input
                        (colorPickerChange)="onColorPickerChange('pendingBackgroundColor', $event)"
                        [style.background]="
                          projectSettingsFormData.get('pendingBackgroundColor').value
                        "
                        [colorPicker]="projectSettingsFormData.get('pendingBackgroundColor').value"
                        class="s-box bg-box"
                      />
                    </div>
                  </td>
                  <td class="ps-0 py-1">
                    <p class="fw700 mb-0 w-fit-content" [ngStyle]="pending">Delivery name</p>
                  </td>
                </tr>
                <tr>
                  <th scope="row" class="fw600 fs12 ps-0">Delivered/Completed</th>
                  <td class="pt-2">
                    <div>
                      <input
                        (colorPickerChange)="onColorPickerChange('deliveredFontColor', $event)"
                        [style.background]="projectSettingsFormData.get('deliveredFontColor').value"
                        [colorPicker]="projectSettingsFormData.get('deliveredFontColor').value"
                        class="s-box"
                      />
                    </div>
                  </td>
                  <td class="pt-2">
                    <div>
                      <input
                        (colorPickerChange)="
                          onColorPickerChange('deliveredBackgroundColor', $event)
                        "
                        [style.background]="
                          projectSettingsFormData.get('deliveredBackgroundColor').value
                        "
                        [colorPicker]="
                          projectSettingsFormData.get('deliveredBackgroundColor').value
                        "
                        class="s-box bg-box"
                      />
                    </div>
                  </td>
                  <td class="ps-0 py-1">
                    <p class="fw700 mb-0 w-fit-content" [ngStyle]="delivered">Delivery name</p>
                  </td>
                </tr>
                <tr>
                  <th scope="row" class="fw600 fs12 ps-0">Rejected</th>
                  <td class="pt-2">
                    <div>
                      <input
                        (colorPickerChange)="onColorPickerChange('rejectedFontColor', $event)"
                        [style.background]="projectSettingsFormData.get('rejectedFontColor').value"
                        [colorPicker]="projectSettingsFormData.get('rejectedFontColor').value"
                        class="s-box"
                      />
                    </div>
                  </td>
                  <td class="pt-2">
                    <div>
                      <input
                        (colorPickerChange)="onColorPickerChange('rejectedBackgroundColor', $event)"
                        [style.background]="
                          projectSettingsFormData.get('rejectedBackgroundColor').value
                        "
                        [colorPicker]="projectSettingsFormData.get('rejectedBackgroundColor').value"
                        class="s-box bg-box"
                      />
                    </div>
                  </td>
                  <td class="ps-0 py-1">
                    <p class="fw700 mb-0 w-fit-content" [ngStyle]="rejected">Delivery name</p>
                  </td>
                </tr>
                <tr>
                  <th scope="row" class="fw600 fs12 ps-0">Expired</th>
                  <td class="pt-2">
                    <div>
                      <input
                        (colorPickerChange)="onColorPickerChange('expiredFontColor', $event)"
                        [style.background]="projectSettingsFormData.get('expiredFontColor').value"
                        [colorPicker]="projectSettingsFormData.get('expiredFontColor').value"
                        class="s-box"
                      />
                    </div>
                  </td>
                  <td class="pt-2">
                    <div>
                      <input
                        (colorPickerChange)="onColorPickerChange('expiredBackgroundColor', $event)"
                        [style.background]="
                          projectSettingsFormData.get('expiredBackgroundColor').value
                        "
                        [colorPicker]="projectSettingsFormData.get('expiredBackgroundColor').value"
                        class="s-box bg-box"
                      />
                    </div>
                  </td>
                  <td class="ps-0 py-1">
                    <p class="fw700 mb-0 w-fit-content" [ngStyle]="expired">Delivery name</p>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <div class="row">
            <h1 class="section-heading fw-bold mb-3">
              Default Working Window Hours
            </h1>
            <p class="fs14 fw600">
              Based on the working hours selection, the calendar will be shown when booking create/edit.
            </p>
            <div class="col-md-12 timezone-flex mt-4">
              <div class="input-group mb-3 delivery-time">
                <label class="fs12 fw600" for="fromTme">From Time:</label>
                <timepicker [formControlName]="'workingWindowStart'" id="fromTme"></timepicker>
                </div>

              <div class="input-group mb-3 delivery-time">
                <label class="fs12 fw600" for="toTme">To Time:</label>
                <timepicker [formControlName]="'workingWindowEnd'" for="toTme"></timepicker>
              </div>
            </div>
          </div>


          <div class="d-flex justify-content-between align-items-center mb-0">
            <div>
              <h1 class="section-heading fw-bold mb-3">
                Default acceptable delivery window
              </h1>
              <p class="info-content mt-3 fs12">
                Please use this setting to pick the desired minimum time to allow your project team
                to add bookings to the Calendar (For example: 2 hours)
              </p>
              <div class="custom-material-form d-flex mb-2 mt-2">
                <div class="form-group me-3">
                  <input
                    type="text"
                    class="form-control material-input color-grey7 w-100px bg-transparent"
                    formControlName="deliveryWindowTime"
                    (keypress)="numberOnly($event)"
                  />
                </div>
                <div class="form-group">
                  <select
                    class="form-control material-input color-grey7 w-110px"
                    formControlName="deliveryWindowTimeUnit"
                  >
                    <option *ngFor="let item of deliveryWindowUnits" [value]="item">
                      {{ item }}
                    </option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="d-flex justify-content-between align-items-center mb-0">
            <div>
              <h1 class="section-heading fw-bold mb-3">
                Default acceptable Crane window
              </h1>
              <p class="info-content mt-3 fs12">
                Please use this setting to pick the desired minimum time to allow your project team
                to add bookings to the Crane Calendar (For example: 2 hours)
              </p>
              <div class="custom-material-form d-flex mb-2 mt-2">
                <div class="form-group me-3">
                  <input
                    type="text"
                    class="form-control material-input color-grey7 w-100px bg-transparent"
                    formControlName="craneWindowTime"
                    (keypress)="numberOnly($event)"
                  />
                </div>
                <div class="form-group">
                  <select
                    class="form-control material-input color-grey7 w-110px"
                    formControlName="craneWindowTimeUnit"
                  >
                    <option *ngFor="let item of deliveryWindowUnits" [value]="item">
                      {{ item }}
                    </option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="d-flex justify-content-between align-items-center mb-0">
            <div>
              <h1 class="section-heading fw-bold mb-3">
                Default acceptable Concrete window
              </h1>
              <p class="info-content mt-3 fs12">
                Please use this setting to pick the desired minimum time to allow your project team
                to add bookings to the Concrete Calendar (For example: 2 hours)
              </p>
              <div class="custom-material-form d-flex mb-2 mt-2">
                <div class="form-group me-3">
                  <input
                    type="text"
                    class="form-control material-input color-grey7 w-100px bg-transparent"
                    formControlName="concreteWindowTime"
                    (keypress)="numberOnly($event)"
                  />
                </div>
                <div class="form-group">
                  <select
                    class="form-control material-input color-grey7 w-110px"
                    formControlName="concreteWindowTimeUnit"
                  >
                    <option *ngFor="let item of deliveryWindowUnits" [value]="item">
                      {{ item }}
                    </option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <div class="d-flex justify-content-between align-items-center mb-0">
            <div>
              <h1 class="section-heading fw-bold mb-3">
                Default acceptable Inspection window
              </h1>
              <p class="info-content mt-3 fs12">
                Please use this setting to pick the desired minimum time to allow your project team
                to add bookings to the Inspection Calendar (For example: 2 hours)
              </p>
              <div class="custom-material-form d-flex mb-2 mt-2">
                <div class="form-group me-3">
                  <input
                    type="text"
                    class="form-control material-input color-grey7 w-100px bg-transparent"
                    formControlName="inspectionWindowTime"
                    (keypress)="numberOnly($event)"
                  />
                </div>
                <div class="form-group">
                  <select
                    class="form-control material-input color-grey7 w-110px"
                    formControlName="inspectionWindowTimeUnit"
                  >
                    <option *ngFor="let item of deliveryWindowUnits" [value]="item">
                      {{ item }}
                    </option>
                  </select>
                </div>
              </div>
            </div>
          </div>
          <h1 class="section-heading fw-bold mb-0">
            Default calendar card configurations
          </h1>
          <div>
            <button
              type="button"
              class="btn btn-orange-outline set-default-outline color-orange rounded-pill px-3 py-1 fs12 my-2 mt-3"
            >
              <span class="fw-bold" (click)="setDefault('card')" (keydown)="handleToggleKeydown($event, 'card')">Reset to default</span>
            </button>
          </div>
          <label class="color-grey7 fw700 pt-3 pb-0 mb-0" for="delivery">Delivery</label>
          <div class="project-setting-lists row">
            <div class="col-md-4 col-12 custom-material-form pt-2">
              <div class="form-group card-form-group ps-0 pt-1"  *ngIf = "showData">
                <span class="fw400 fs12">Card Title</span>
                <select id="delivery"
                  class="form-control material-input fs12 color-grey7 mt-1"
                  formControlName="selectedDeliveryLine1"
                  (change)="cardSettings('deliveryCardLine1', $event.target.value)"
                >
                  <option *ngFor="let item of deliveryCardLine1" [value]="item.label">
                    {{ item.label }}
                  </option>
                </select>
              </div>
            </div>
            <div class="col-md-4 col-12 custom-material-form linecol-project-setting pt-2">
              <div class="form-group card-form-group ps-0 ms-4 md-ms-0 pt-1"  *ngIf = "showData">
                <span class="fw400 fs12">Line 2</span>
                <select
                  class="form-control material-input fs12 color-grey7 mt-1"
                  formControlName="selectedDeliveryLine2"
                  (change)="cardSettings('deliveryCardLine2', $event.target.value)"
                >
                  <option *ngFor="let item of deliveryCardLine2" [value]="item.label">
                    {{ item.label }}
                  </option>
                </select>
              </div>
            </div>
            <div class="col-md-4 col-12 custom-material-form ps-0">
              <div class="form-group ps-md-5 ps-2 projectmobile-listitem ms-0 pt-1">
                <span class="fw400 fs12">Preview</span>

                <div class="form-group-border mt-0">
                  <div class="d-flex">
                    <div class="form-group-greenborder"></div>
                    <div>
                      <p class="mb-0 fs12 fw400 ps-2 pt-1">
                        {{ projectSettingsFormData.get('selectedDeliveryLine1').value }}
                      </p>
                      <p class="mb-0 fs12 fw400 ps-2 pt-0 pb-1">
                        {{ projectSettingsFormData.get('selectedDeliveryLine2').value }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <label class="color-grey7 fw700 pt-0 pb-0 mb-0" for="crane">Crane</label>
          <div class="project-setting-lists row">
            <div class="col-md-4 col-12 custom-material-form pt-2">
              <div class="form-group card-form-group ps-0 pt-1"  *ngIf = "showData">
                <span class="fw400 fs12">Card Title</span>
                <select id="crane"
                  class="form-control material-input fs12 color-grey7 mt-1"
                  formControlName="selectedCraneLine1"
                  (change)="cardSettings('craneCardLine1', $event.target.value)"
                >
                  <option *ngFor="let item of craneCardLine1" [value]="item.label">
                    {{ item.label }}
                  </option>
                </select>
              </div>
            </div>
            <div class="col-md-4 col-12 custom-material-form linecol-project-setting pt-2">
              <div class="form-group card-form-group ps-0 ms-4 md-ms-0 pt-1" *ngIf = "showData">
                <span class="fw400 fs12">Line 2</span>
                <select
                  class="form-control material-input fs12 color-grey7 mt-1"
                  formControlName="selectedCraneLine2"
                  (change)="cardSettings('craneCardLine2', $event.target.value)"
                >
                  <option *ngFor="let item of craneCardLine2" [value]="item.label">
                    {{ item.label }}
                  </option>
                </select>
              </div>
            </div>
            <div class="col-md-4 col-12 custom-material-form ps-0">
              <div class="form-group ps-md-5 ps-2 projectmobile-listitem ms-0 pt-1">
                <span class="fw400 fs12">Preview</span>

                <div class="form-group-border mt-0">
                  <div class="d-flex">
                    <div class="form-group-greenborder"></div>
                    <div>
                      <p class="mb-0 fs12 fw400 ps-2 pt-1">
                        {{ projectSettingsFormData.get('selectedCraneLine1').value }}
                      </p>
                      <p class="mb-0 fs12 fw400 ps-2 pt-0 pb-1">
                        {{ projectSettingsFormData.get('selectedCraneLine2').value }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <label class="color-grey7 fw700 pt-0 pb-0 mb-0" for="concrete">Concrete</label>
          <div class="project-setting-lists row">
            <div class="col-md-4 col-12 custom-material-form pt-2">
              <div class="form-group card-form-group ps-0 pt-1" *ngIf = "showData">
                <span class="fw400 fs12">Card Title</span>
                <select id="concrete"
                  class="form-control material-input fs12 color-grey7 mt-1"
                  formControlName="selectedConcreteLine1"
                  (change)="cardSettings('concreteCardLine1', $event.target.value)"
                >
                  <option *ngFor="let item of concreteCardLine1" [value]="item.label">
                    {{ item.label }}
                  </option>
                </select>
              </div>
            </div>
            <div class="col-md-4 col-12 custom-material-form linecol-project-setting pt-2">
              <div class="form-group card-form-group ps-0 ms-4 md-ms-0 pt-1" *ngIf = "showData">
                <span class="fw400 fs12">Line 2</span>
                <select
                  class="form-control material-input fs12 color-grey7 mt-1"
                  formControlName="selectedConcreteLine2"
                  (change)="cardSettings('concreteCardLine2', $event.target.value)"
                >
                  <option *ngFor="let item of concreteCardLine2" [value]="item.label">
                    {{ item.label }}
                  </option>
                </select>
              </div>
            </div>
            <div class="col-md-4 col-12 custom-material-form ps-0">
              <div class="form-group ps-md-5 ps-2 projectmobile-listitem ms-0 pt-1">
                <span class="fw400 fs12">Preview</span>

                <div class="form-group-border mt-0">
                  <div class="d-flex">
                    <div class="form-group-greenborder"></div>
                    <div>
                      <p class="mb-0 fs12 fw400 ps-2 pt-1">
                        {{ projectSettingsFormData.get('selectedConcreteLine1').value }}
                      </p>
                      <p class="mb-0 fs12 fw400 ps-2 pt-0 pb-1">
                        {{ projectSettingsFormData.get('selectedConcreteLine2').value }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <label class="color-grey7 fw700 pt-3 pb-0 mb-0" for="inspection" >Inspection</label>
          <div class="project-setting-lists row">
            <div class="col-md-4 col-12 custom-material-form pt-2">
              <div class="form-group card-form-group ps-0 pt-1" *ngIf = "showData">
                <span class="fw400 fs12">Card Title</span>
                <select id="inspection"
                  class="form-control material-input fs12 color-grey7 mt-1"
                  formControlName="selectedInspectionLine1"
                  (change)="cardSettings('inspectionCardLine1', $event.target.value)"
                >
                  <option *ngFor="let item of inspectionCardLine1" [value]="item.label">
                    {{ item.label }}
                  </option>
                </select>
              </div>
            </div>
            <div class="col-md-4 col-12 custom-material-form linecol-project-setting pt-2">
              <div class="form-group card-form-group ps-0 ms-4 md-ms-0 pt-1" *ngIf = "showData">
                <span class="fw400 fs12">Line 2</span>
                <select
                  class="form-control material-input fs12 color-grey7 mt-1"
                  formControlName="selectedInspectionLine2"
                  (change)="cardSettings('inspectionCardLine2', $event.target.value)"
                >
                  <option *ngFor="let item of inspectionCardLine2" [value]="item.label">
                    {{ item.label }}
                  </option>
                </select>
              </div>
            </div>
            <div class="col-md-4 col-12 custom-material-form ps-0">
              <div class="form-group ps-md-5 ps-2 projectmobile-listitem ms-0 pt-1">
                <span class="fw400 fs12">Preview</span>

                <div class="form-group-border mt-0">
                  <div class="d-flex">
                    <div class="form-group-greenborder"></div>
                    <div>
                      <p class="mb-0 fs12 fw400 ps-2 pt-1">
                        {{ projectSettingsFormData.get('selectedInspectionLine1').value }}
                      </p>
                      <p class="mb-0 fs12 fw400 ps-2 pt-0 pb-1">
                        {{ projectSettingsFormData.get('selectedInspectionLine2').value }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <h1 class="section-heading fw-bold mb-0 mt-2">Default booking settings</h1>
          <div class="exception-section mt-3">
            <p class="section-title">
              Resource Constraints<br />
                <span class="note-text"
                >Note: <img src="./assets/images/note_icon.svg" alt="i-icon" /> Selecting resources below will prevent overlapping within the same time slot.</span
              >
            </p>
            <div class="exception-form-row d-flex align-items-start gap-4">
              <!-- Gate -->
              <div class="form-group my-0 exception-form-group company-select equipment-select">
                <label class="exception-form-label fs12 fw600 mb12 equipment-label"  for="equipment">
                  Gate<sup>*</sup>
                  <span class="dot-border-info tooltip custom-tooltip-margin">
                    <span class="fw700 info-icon">i</span>
                    <span class="tooltiptext">
                      Selecting gate from this dropdown will prevent double booking the selected gate during the same time as a previous booking.
                    </span>
                    <div class="arrow-down"></div>
                  </span>
                </label>
                <ng-multiselect-dropdown
                  [placeholder]="'Gate'"
                  [settings]="gateDropdownSettings"
                  [data]="gateList"
                  formControlName="gateExceptions"
                ></ng-multiselect-dropdown>
              </div>

              <!-- Equipment -->
              <div class="form-group my-0 exception-form-group company-select equipment-select">
                <label class="exception-form-label fs12 fw600 mb12 equipment-label" for="equipment">
                  Equipment<sup>*</sup>
                  <span class="dot-border-info tooltip custom-tooltip-margin">
                    <span class="fw700 info-icon">i</span>
                    <span class="tooltiptext">
                      Selecting equipment from this dropdown will prevent double booking the selected equipment during the same time as a previous booking.
                    </span>
                    <div class="arrow-down"></div>
                  </span>
                </label>
                <ng-multiselect-dropdown
                  [placeholder]="'Equipment'"
                  [settings]="equipmentDropdownSettings"
                  [data]="equipmentList"
                  formControlName="equipmentExceptions"
                ></ng-multiselect-dropdown>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>

<ng-template #approveModal>
  <div class="modal-header justify-content-center border-0">
    <h4 class="modal-title text-center fw-bold">Auto Approve Items</h4>
  </div>
  <div class="modal-body newupdate-alignments taginput--heightfix">
    <div class="addcalendar-details">
      <form
        name="form"
        class="custom-material-form add-concrete-material-form project-setting-form"
        [formGroup]="autoApproveFormData"
        novalidate
      >
        <div class="form-group taginput-height scheduler-form-input">
          <label class="fs12 fw600 m-0 color-grey11" for="memAdd">Type in the box below to add </label>
        <div>
          <tag-input id="memAdd"
            formControlName="person"
            [onlyFromAutocomplete]="true"
            [placeholder]="'Search for a member'"
            [secondaryPlaceholder]="'Search for a member'"
            [onTextChangeDebounce]="500"
            class="tag-layout newdelivery-taglayout schedule-map auto-approve-map mx-0"
            [identifyBy]="'id'"
            [displayBy]="'email'"
          >
            <tag-input-dropdown
              [appendToBody] = "false"
              [showDropdownIfEmpty]="false"
              [displayBy]="'email'"
              [identifyBy]="'id'"
              [autocompleteObservable]="requestAutocompleteItems"
              class="custom-tag-dropdown"
            >
              <ng-template let-item="item" let-index="index" class="ms-2">
                <div class="tag-input-sample scheduler-form-taginput fs12 ms-0 py-0">
                  {{ item.email }}
                </div>
              </ng-template>
            </tag-input-dropdown>
          </tag-input>
        </div>
        </div>
        <div class="text-center mt-4">
          <button
            class="btn btn-grey color-dark-grey radius20 fs12 mb-3 me-3 fw-bold cairo-regular px-5"
            (click)="closeAutoApproveModal()"
          >
            Cancel
          </button>
          <button
            class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular mb-3 px-5"
            (click)="submit()"
          >
            <em class="fa fa-spinner" aria-hidden="true" *ngIf="updateLoader"></em>
            Submit
          </button>
        </div>
      </form>
    </div>
  </div>
</ng-template>

<ng-template #popTemplate>
  <div>
    <p class="fs12 fw400">
      This option will control if your project team can make bookings on holidays or special events
      controlled through calendar settings
    </p>
  </div>
</ng-template>
