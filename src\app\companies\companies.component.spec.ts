import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CompaniesComponent } from './companies.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, ReactiveFormsModule, FormsModule, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { ProjectService } from '../services/profile/project.service';
import { AuthService } from '../services/auth/auth.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { MixpanelService } from '../services/mixpanel.service';
import { of, throwError } from 'rxjs';
import { TemplateRef } from '@angular/core';
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'paginate'
})
class MockPaginatePipe implements PipeTransform {
  transform(value: any[], config: any): any[] {
    return value;
  }
}

describe('CompaniesComponent', () => {
  let component: CompaniesComponent;
  let fixture: ComponentFixture<CompaniesComponent>;
  let modalService: BsModalService;
  let projectService: ProjectService;
  let toastrService: ToastrService;
  let formBuilder: UntypedFormBuilder;

  const mockProjectService = {
    userData: of({ RoleId: 2 }),
    companyFileUpload: of({ status: 'companyUploadDone' }),
    projectParent: of({ ProjectId: 1, ParentCompanyId: 1 }),
    getDefinableWork: jest.fn().mockReturnValue(of({ data: [] })),
    listCompany: jest.fn().mockReturnValue(of({ data: { rows: [], count: 0 } })),
    uploadBulkNdrFile: jest.fn(),
    uploadBulkCompanyFile: jest.fn(),
    addCompany: jest.fn().mockReturnValue(of({ success: true })),
    editCompany: jest.fn().mockReturnValue(of({ success: true })),
    deleteCompany: jest.fn().mockReturnValue(of({ success: true })),
    checkExistCompany: jest.fn().mockReturnValue(of({ status: 200 })),
    companyLogoUpload: jest.fn().mockReturnValue(of({ data: [{ Location: 'test-logo-url' }] })),
    getProject: jest.fn().mockReturnValue(of({ data: [{ id: 1, projectName: 'Test Project' }] }))
  };

  const mockToastrService = {
    success: jest.fn(),
    error: jest.fn()
  };

  const mockModalRef = {
    hide: jest.fn(),
    setClass: jest.fn()
  } as unknown as BsModalRef<any>;

  const mockModalService = {
    show: jest.fn().mockReturnValue(mockModalRef)
  };

  const mockAuthService = {
    // Add any auth service methods if needed
  };

  const mockDeliveryService = {
    importCompanyTemplate: jest.fn().mockReturnValue(of(new Blob())),
    importCompany: jest.fn().mockReturnValue(of({ status: 201 }))
  };

  const mockMixpanelService = {
    addMixpanelEvents: jest.fn()
  };

  const mockActivatedRoute = {
    // Add any route properties if needed
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        CompaniesComponent,
        MockPaginatePipe
      ],
      imports: [
        ReactiveFormsModule,
        FormsModule
      ],
      providers: [
        { provide: BsModalService, useValue: mockModalService },
        { provide: ProjectService, useValue: mockProjectService },
        { provide: ToastrService, useValue: mockToastrService },
        UntypedFormBuilder,
        { provide: Title, useValue: { setTitle: jest.fn() } },
        { provide: AuthService, useValue: mockAuthService },
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: MixpanelService, useValue: mockMixpanelService },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CompaniesComponent);
    component = fixture.componentInstance;
    modalService = TestBed.inject(BsModalService);
    projectService = TestBed.inject(ProjectService);
    toastrService = TestBed.inject(ToastrService);
    formBuilder = TestBed.inject(UntypedFormBuilder);
    
    // Initialize component state
    component.companiesList = [];
    component.loader = true;
    component.isAdmin = true;
    component.modalRef = mockModalRef;
    component.currentDeleteId = 1;
    component.deleteIndex = [1];
    component.ProjectId = 1;
    component.ParentCompanyId = 1;
    
    // Initialize forms
    component.companyDetailsForm = formBuilder.group({
      companyName: ['', Validators.required],
      zipCode: [''],
      selectedItems: [[]],
      country: [''],
      state: [''],
      city: ['']
    });
    
    component.companyEditForm = formBuilder.group({
      companyName: ['', Validators.required],
      zipCode: [''],
      selectedItems: [[]],
      id: [''],
      country: [''],
      state: [''],
      city: ['']
    });
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.currentPageNo).toBe(1);
    expect(component.pageSize).toBe(25);
    expect(component.loader).toBe(true);
    expect(component.isAdmin).toBe(true);
  });

  it('should initialize forms', () => {
    expect(component.companyDetailsForm).toBeTruthy();
    expect(component.companyEditForm).toBeTruthy();
    expect(component.filterForm).toBeTruthy();
  });

  describe('getCompanies', () => {
    it('should fetch companies successfully', () => {
      const mockResponse = { 
        data: { 
          rows: [{ id: 1, name: 'Test Company' }], 
          count: 1 
        } 
      };
      mockProjectService.listCompany.mockReturnValue(of(mockResponse));

      component.getCompanies();

      expect(projectService.listCompany).toHaveBeenCalled();
      expect(component.companiesList).toEqual(mockResponse.data.rows);
      expect(component.totalCount).toBe(mockResponse.data.count);
      expect(component.loader).toBe(false);
    });

    it('should handle error when fetching companies', () => {
      mockProjectService.listCompany.mockReturnValue(throwError(() => new Error('API Error')));

      component.getCompanies();

      // Need to wait for the error to be handled
      setTimeout(() => {
        expect(component.loader).toBe(false);
        expect(toastrService.error).toHaveBeenCalled();
      });
    });
  });

  describe('Form Validation', () => {
    it('should validate company name is required', () => {
      const form = component.companyDetailsForm;
      const companyNameControl = form.get('companyName');

      companyNameControl.setValue('');
      expect(companyNameControl.valid).toBeFalsy();
      expect(companyNameControl.errors.required).toBeTruthy();
    });

    it('should validate zip code format', () => {
      const form = component.companyDetailsForm;
      const zipCodeControl = form.get('zipCode');

      zipCodeControl.setValue('12345');
      expect(zipCodeControl.valid).toBeTruthy();

      zipCodeControl.setValue('abc');
      expect(zipCodeControl.valid).toBeTruthy(); // Zip code is optional in the form
    });
  });

  describe('Modal Operations', () => {
    it('should open modal', () => {
      const template = {} as TemplateRef<any>;
      component.openModal(template);

      expect(modalService.show).toHaveBeenCalled();
    });

    it('should close modal', () => {
      const template = {} as TemplateRef<any>;
      component.submitted = true;
      component.companyDetailsForm.markAsPristine();
      component.companyDetailsForm.markAsUntouched();

      component.close(template, 'newCompany');

      expect(mockModalRef.hide).toHaveBeenCalled();
      expect(component.submitted).toBe(false);
    });
  });

  describe('Company Operations', () => {
    it('should add company successfully', () => {
      const mockCompany = { companyName: 'Test Company', zipCode: '12345' };
      mockProjectService.addCompany.mockReturnValue(of({ success: true }));

      component.companyDetailsForm.patchValue(mockCompany);
      component.onSubmit();

      expect(projectService.addCompany).toHaveBeenCalled();
      expect(toastrService.success).toHaveBeenCalled();
    });

    it('should edit company successfully', () => {
      const mockCompany = { companyName: 'Updated Company', zipCode: '12345', id: 1 };
      mockProjectService.editCompany.mockReturnValue(of({ success: true }));

      component.companyEditForm.patchValue(mockCompany);
      component.editedCompanyId = 1;
      component.checkCompanyNameDuplication({} as TemplateRef<any>, 'editCompany');

      expect(projectService.editCompany).toHaveBeenCalled();
      expect(toastrService.success).toHaveBeenCalled();
    });

    it('should delete company successfully', () => {
      const mockCompanyId = 1;
      mockProjectService.deleteCompany.mockReturnValue(of({ success: true }));

      component.currentDeleteId = mockCompanyId;
      component.deleteIndex = [mockCompanyId];
      component.deleteCompany();

      expect(projectService.deleteCompany).toHaveBeenCalledWith({ 
        id: [mockCompanyId], 
        isSelectAll: false, 
        ProjectId: 1, 
        ParentCompanyId: 1 
      });
      expect(toastrService.success).toHaveBeenCalled();
    });
  });

  describe('Filter Operations', () => {
    it('should apply filters', () => {
      const mockFilters = { companyFilter: 'test', dfowFilter: 'dfow1' };
      mockProjectService.listCompany.mockReturnValue(of({ data: { rows: [], count: 0 } }));
      component.filterForm.patchValue(mockFilters);

      component.filterSubmit();

      expect(component.filterCount).toBe(2);
      expect(component.pageNo).toBe(1);
      expect(projectService.listCompany).toHaveBeenCalled();
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should reset filters', () => {
      mockProjectService.listCompany.mockReturnValue(of({ data: { rows: [], count: 0 } }));
      component.filterCount = 2;
      component.search = 'test';

      component.resetFilter();

      expect(component.filterCount).toBe(0);
      expect(component.search).toBe('');
      expect(component.pageNo).toBe(1);
      expect(projectService.listCompany).toHaveBeenCalled();
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should apply filters with only company filter', () => {
      const mockFilters = { companyFilter: 'test', dfowFilter: '' };
      mockProjectService.listCompany.mockReturnValue(of({ data: { rows: [], count: 0 } }));
      component.filterForm.patchValue(mockFilters);

      component.filterSubmit();

      expect(component.filterCount).toBe(1);
    });

    it('should apply filters with only dfow filter', () => {
      const mockFilters = { companyFilter: '', dfowFilter: 'dfow1' };
      mockProjectService.listCompany.mockReturnValue(of({ data: { rows: [], count: 0 } }));
      component.filterForm.patchValue(mockFilters);

      component.filterSubmit();

      expect(component.filterCount).toBe(1);
    });

    it('should apply filters with no filters', () => {
      const mockFilters = { companyFilter: '', dfowFilter: '' };
      mockProjectService.listCompany.mockReturnValue(of({ data: { rows: [], count: 0 } }));
      component.filterForm.patchValue(mockFilters);

      component.filterSubmit();

      expect(component.filterCount).toBe(0);
    });
  });

  describe('Search Operations', () => {
    it('should search companies with valid data', () => {
      mockProjectService.listCompany.mockReturnValue(of({ data: { rows: [], count: 0 } }));

      component.getSearchCompany('test company');

      expect(component.showSearchbar).toBe(true);
      expect(component.search).toBe('test company');
      expect(component.pageNo).toBe(1);
      expect(projectService.listCompany).toHaveBeenCalled();
    });

    it('should hide searchbar when search data is empty', () => {
      mockProjectService.listCompany.mockReturnValue(of({ data: { rows: [], count: 0 } }));

      component.getSearchCompany('');

      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
    });

    it('should clear search', () => {
      mockProjectService.listCompany.mockReturnValue(of({ data: { rows: [], count: 0 } }));
      component.showSearchbar = true;
      component.search = 'test';

      component.clear();

      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
      expect(component.pageNo).toBe(1);
      expect(projectService.listCompany).toHaveBeenCalled();
    });
  });

  describe('Pagination Operations', () => {
    it('should change page size', () => {
      mockProjectService.listCompany.mockReturnValue(of({ data: { rows: [], count: 0 } }));

      component.changePageSizeOfCompanyGrid(50);

      expect(component.pageSize).toBe(50);
      expect(projectService.listCompany).toHaveBeenCalled();
    });

    it('should change page number', () => {
      mockProjectService.listCompany.mockReturnValue(of({ data: { rows: [], count: 0 } }));

      component.changePageNo(2);

      expect(component.currentPageNo).toBe(2);
      expect(projectService.listCompany).toHaveBeenCalled();
    });
  });

  describe('Sorting Operations', () => {
    it('should sort by field', () => {
      mockProjectService.listCompany.mockReturnValue(of({ data: { rows: [], count: 0 } }));

      component.sortByField('companyName', 'ASC');

      expect(component.sortColumn).toBe('companyName');
      expect(component.sort).toBe('ASC');
      expect(projectService.listCompany).toHaveBeenCalled();
    });

    it('should handle toggle keydown for sorting', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      mockProjectService.listCompany.mockReturnValue(of({ data: { rows: [], count: 0 } }));

      component.handleToggleKeydown(mockEvent, 'companyName', 'DESC');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.sortColumn).toBe('companyName');
      expect(component.sort).toBe('DESC');
    });

    it('should handle space key for sorting', () => {
      const mockEvent = { key: ' ', preventDefault: jest.fn() } as any;
      mockProjectService.listCompany.mockReturnValue(of({ data: { rows: [], count: 0 } }));

      component.handleToggleKeydown(mockEvent, 'id', 'ASC');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.sortColumn).toBe('id');
      expect(component.sort).toBe('ASC');
    });

    it('should not handle other keys for sorting', () => {
      const mockEvent = { key: 'Tab', preventDefault: jest.fn() } as any;

      component.handleToggleKeydown(mockEvent, 'companyName', 'DESC');

      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
    });
  });

  describe('Input Validation', () => {
    it('should validate alphanumeric characters for company name', () => {
      // Valid characters
      expect(component.alphaNumericOnlyForCompanyName({ keyCode: 65 })).toBe(true); // A
      expect(component.alphaNumericOnlyForCompanyName({ keyCode: 97 })).toBe(true); // a
      expect(component.alphaNumericOnlyForCompanyName({ keyCode: 48 })).toBe(true); // 0
      expect(component.alphaNumericOnlyForCompanyName({ keyCode: 32 })).toBe(true); // space
      expect(component.alphaNumericOnlyForCompanyName({ keyCode: 8 })).toBe(true); // backspace

      // Invalid characters
      expect(component.alphaNumericOnlyForCompanyName({ keyCode: 33 })).toBe(true); // !
      expect(component.alphaNumericOnlyForCompanyName({ keyCode: 64 })).toBe(true); // @
    });

    it('should validate numbers only for zip code', () => {
      // Valid numbers
      expect(component.numberOnlyForCompanyZipCode({ which: 48 })).toBe(true); // 0
      expect(component.numberOnlyForCompanyZipCode({ which: 57 })).toBe(true); // 9
      expect(component.numberOnlyForCompanyZipCode({ keyCode: 49 })).toBe(true); // 1

      // Invalid characters
      expect(component.numberOnlyForCompanyZipCode({ which: 65 })).toBe(false); // A
      expect(component.numberOnlyForCompanyZipCode({ which: 33 })).toBe(false); // !
    });

    it('should validate alphanumeric for zip code', () => {
      // Valid characters
      expect(component.alphaNumForZipCode({ which: 48 })).toBe(true); // 0
      expect(component.alphaNumForZipCode({ which: 65 })).toBe(true); // A
      expect(component.alphaNumForZipCode({ which: 97 })).toBe(true); // a

      // Invalid characters
      expect(component.alphaNumForZipCode({ which: 33 })).toBe(false); // !
      expect(component.alphaNumForZipCode({ which: 64 })).toBe(false); // @
    });

    it('should check string empty values - valid company name', () => {
      const formValue = { companyName: 'Test Company', scope: 'Test scope' };

      const result = component.checkStringEmptyValues(formValue);

      expect(result).toBe(false);
    });

    it('should check string empty values - empty company name', () => {
      const formValue = { companyName: '   ', scope: 'Test scope' };

      const result = component.checkStringEmptyValues(formValue);

      expect(result).toBe(true);
      expect(toastrService.error).toHaveBeenCalledWith('Please Enter valid Company Name.', 'OOPS!');
    });

    it('should check string empty values - empty scope', () => {
      const formValue = { companyName: 'Test Company', scope: '   ' };

      const result = component.checkStringEmptyValues(formValue);

      expect(result).toBe(true);
      expect(toastrService.error).toHaveBeenCalledWith('Please Enter valid Notes.', 'OOPS!');
    });

    it('should check string empty values - null scope', () => {
      const formValue = { companyName: 'Test Company', scope: null };

      const result = component.checkStringEmptyValues(formValue);

      expect(result).toBe(false);
    });
  });

  describe('File Upload Operations', () => {
    it('should handle valid image file upload', () => {
      const mockFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
      const mockEvent = { target: { files: [mockFile] } };
      const mockReader = {
        readAsDataURL: jest.fn(),
        onload: null,
        result: 'data:image/jpeg;base64,test'
      };

      // Mock FileReader
      (window as any).FileReader = jest.fn(() => mockReader) as any;

      component.onFileChangeEvent(mockEvent);

      expect(component.file).toBe(mockFile);
      expect(component.companyLogo).toBe(mockFile);
      expect(mockReader.readAsDataURL).toHaveBeenCalledWith(mockFile);

      // Simulate onload
      mockReader.onload({} as any);
      expect(component.companyLogo.logo).toBe('data:image/jpeg;base64,test');
    });

    it('should handle invalid file upload', () => {
      const mockFile = new File([''], 'test.txt', { type: 'text/plain' });
      const mockEvent = { target: { files: [mockFile] } };

      component.onFileChangeEvent(mockEvent);

      expect(toastrService.error).toHaveBeenCalledWith('Please uploade .jpeg,.jpg,.png file.', 'OOPS!');
    });

    it('should handle valid edit file upload', () => {
      const mockFile = new File([''], 'test.png', { type: 'image/png' });
      const mockEvent = { target: { files: [mockFile] } };
      const mockReader = {
        readAsDataURL: jest.fn(),
        onload: null,
        result: 'data:image/png;base64,test'
      };

      (window as any).FileReader = jest.fn(() => mockReader) as any;

      component.onEditFileChangeEvent(mockEvent);

      expect(component.editFile).toBe(mockFile);
      expect(component.editCompanyLogo).toBe(mockFile);
      expect(mockReader.readAsDataURL).toHaveBeenCalledWith(mockFile);
    });

    it('should handle invalid edit file upload', () => {
      const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });
      const mockEvent = { target: { files: [mockFile] } };

      component.onEditFileChangeEvent(mockEvent);

      expect(toastrService.error).toHaveBeenCalledWith('Please uploade .jpeg,.jpg,.png file.', 'OOPS!');
    });
  });

  describe('Company Selection Operations', () => {
    beforeEach(() => {
      component.companiesList = [
        { id: 1, companyName: 'Company 1', isChecked: false },
        { id: 2, companyName: 'Company 2', isChecked: false }
      ];
    });

    it('should select all companies', () => {
      component.selectAll = false;

      component.selectAllCompaniesData();

      expect(component.selectAll).toBe(true);
      expect(component.companiesList[0].isChecked).toBe(true);
      expect(component.companiesList[1].isChecked).toBe(true);
    });

    it('should deselect all companies', () => {
      component.selectAll = true;
      component.companiesList[0].isChecked = true;
      component.companiesList[1].isChecked = true;

      component.selectAllCompaniesData();

      expect(component.selectAll).toBe(false);
      expect(component.companiesList[0].isChecked).toBe(false);
      expect(component.companiesList[1].isChecked).toBe(false);
    });

    it('should set selected item', () => {
      component.setSelectedItem(0);

      expect(component.companiesList[0].isChecked).toBe(true);
    });

    it('should unset selected item', () => {
      component.companiesList[0].isChecked = true;

      component.setSelectedItem(0);

      expect(component.companiesList[0].isChecked).toBe(false);
    });

    it('should check if any row is selected - with selectAll true', () => {
      component.selectAll = true;

      const result = component.checkSelectedRow();

      expect(result).toBe(false);
    });

    it('should check if any row is selected - with items checked', () => {
      component.selectAll = false;
      component.companiesList[0].isChecked = true;

      const result = component.checkSelectedRow();

      expect(result).toBe(false);
    });

    it('should check if any row is selected - no items checked', () => {
      component.selectAll = false;

      const result = component.checkSelectedRow();

      expect(result).toBe(true);
    });
  });

  describe('Company Deletion Operations', () => {
    beforeEach(() => {
      component.companiesList = [
        { id: 1, companyName: 'Company 1', isChecked: true },
        { id: 2, companyName: 'Company 2', isChecked: false }
      ];
      component.deleteIndex = [];
    });

    it('should remove selected items with selectAll true', () => {
      component.selectAll = true;
      mockProjectService.deleteCompany.mockReturnValue(of({ success: true, message: 'Deleted successfully' }));

      component.removeItem();

      expect(component.deleteSubmitted).toBe(true);
      expect(projectService.deleteCompany).toHaveBeenCalled();
    });

    it('should remove selected items with individual selection', () => {
      component.selectAll = false;
      mockProjectService.deleteCompany.mockReturnValue(of({ success: true, message: 'Deleted successfully' }));

      component.removeItem();

      expect(component.deleteSubmitted).toBe(true);
      expect(component.deleteIndex).toContain(1);
      expect(projectService.deleteCompany).toHaveBeenCalled();
    });

    it('should handle delete company error', () => {
      mockProjectService.deleteCompany.mockReturnValue(throwError(() => ({ message: { statusCode: 400, details: [{ error: 'Delete failed' }] } })));

      component.deleteCompany();

      expect(component.deleteSubmitted).toBe(false);
      expect(projectService.listCompany).toHaveBeenCalled();
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should handle delete company error without message', () => {
      mockProjectService.deleteCompany.mockReturnValue(throwError(() => ({ message: null })));

      component.deleteCompany();

      expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should handle delete company generic error', () => {
      mockProjectService.deleteCompany.mockReturnValue(throwError(() => ({ message: 'Generic error' })));

      component.deleteCompany();

      expect(toastrService.error).toHaveBeenCalledWith('Generic error', 'OOPS!');
      expect(mockModalRef.hide).toHaveBeenCalled();
    });
  });

  describe('Modal Operations Extended', () => {
    it('should open import modal', () => {
      const template = {} as TemplateRef<any>;

      component.openImportModal(template);

      expect(modalService.show).toHaveBeenCalledWith(template, {
        backdrop: 'static',
        class: 'modal-lg custom-modal'
      });
    });

    it('should open modal1 for filters', () => {
      const template = {} as TemplateRef<any>;

      component.openModal1(template);

      expect(component.modalLoader).toBe(true);
      expect(modalService.show).toHaveBeenCalledWith(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-sm filter-popup custom-modal'
      });
    });

    it('should open company name duplication confirmation popup', () => {
      const template = {} as TemplateRef<any>;

      component.openCompanyNameDuplicationConfirmationPopup(template);

      expect(modalService.show).toHaveBeenCalledWith(template, {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
      });
    });

    it('should open modal popup', () => {
      const template = {} as TemplateRef<any>;

      component.openModalPopup(template);

      expect(modalService.show).toHaveBeenCalledWith(template, {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
      });
    });
  });

  describe('Company Name Duplication', () => {
    it('should handle company name confirmation - no action', () => {
      component.companyNameConfirmationToAdd('no');

      expect(component.modalRef2.hide).toHaveBeenCalled();
    });

    it('should handle company name confirmation - yes action with add company', () => {
      component.addCompanyPopupSelected = true;
      jest.spyOn(component, 'onSubmit');

      component.companyNameConfirmationToAdd('yes');

      expect(component.modalRef2.hide).toHaveBeenCalled();
      expect(component.onSubmit).toHaveBeenCalled();
    });

    it('should handle company name confirmation - yes action with edit company', () => {
      component.addCompanyPopupSelected = false;
      jest.spyOn(component, 'onEditSubmit');

      component.companyNameConfirmationToAdd('yes');

      expect(component.modalRef2.hide).toHaveBeenCalled();
      expect(component.onEditSubmit).toHaveBeenCalled();
    });

    it('should check company name duplication for add company - valid form', () => {
      component.companyDetailsForm.patchValue({ companyName: 'Test Company' });
      mockProjectService.checkExistCompany.mockReturnValue(of({ status: 200 }));
      jest.spyOn(component, 'onSubmit');

      component.checkCompanyNameDuplication({} as TemplateRef<any>, 'addCompany');

      expect(component.submitted).toBe(true);
      expect(component.addCompanyPopupSelected).toBe(true);
      expect(component.onSubmit).toHaveBeenCalled();
    });

    it('should check company name duplication for edit company - valid form', () => {
      component.companyEditForm.patchValue({ companyName: 'Test Company' });
      component.editedCompanyId = 1;
      mockProjectService.checkExistCompany.mockReturnValue(of({ status: 200 }));
      jest.spyOn(component, 'onEditSubmit');

      component.checkCompanyNameDuplication({} as TemplateRef<any>, 'editCompany');

      expect(component.submitted).toBe(true);
      expect(component.addCompanyPopupSelected).toBe(false);
      expect(component.onEditSubmit).toHaveBeenCalled();
    });

    it('should handle company name duplication error', () => {
      component.companyDetailsForm.patchValue({ companyName: 'Test Company' });
      mockProjectService.checkExistCompany.mockReturnValue(throwError(() => ({ message: { statusCode: 400, details: [{ error: 'Duplication error' }] } })));

      component.checkCompanyNameDuplication({} as TemplateRef<any>, 'addCompany');

      expect(component.formSubmitted).toBe(false);
      expect(component.submitted).toBe(false);
    });

    it('should check company exists with duplicates', () => {
      const existCompany = {
        data: {
          existInProject: [{ companyName: 'Existing Company' }],
          sameAsParentCompany: { companyName: 'Parent Company' }
        }
      };
      jest.spyOn(component, 'openCompanyNameDuplicationConfirmationPopup');

      component.checkCompanyExists(existCompany, {} as TemplateRef<any>, 'addCompany');

      expect(component.existCompanyName).toBe('Existing Company, Parent Company');
      expect(component.openCompanyNameDuplicationConfirmationPopup).toHaveBeenCalled();
    });
  });

  describe('Address Handling', () => {
    it('should handle company address change for add form', () => {
      const mockAddress = {
        address_components: [
          { types: ['street_number'], long_name: '123' },
          { types: ['route'], long_name: 'Main St' },
          { types: ['locality'], long_name: 'Test City' },
          { types: ['administrative_area_level_1'], long_name: 'Test State' },
          { types: ['country'], long_name: 'Test Country' },
          { types: ['postal_code'], long_name: '12345' }
        ]
      };

      component.handleCompanyAddressChange(mockAddress, 'add');

      expect(component.companyDetailsForm.get('address').value).toBe('123 Main St');
      expect(component.companyDetailsForm.get('city').value).toBe('Test City');
      expect(component.companyDetailsForm.get('state').value).toBe('Test State');
      expect(component.companyDetailsForm.get('country').value).toBe('Test Country');
      expect(component.companyDetailsForm.get('zipCode').value).toBe('12345');
    });

    it('should handle company address change for edit form', () => {
      const mockAddress = {
        address_components: [
          { types: ['locality'], long_name: 'Edit City' },
          { types: ['administrative_area_level_1'], long_name: 'Edit State' },
          { types: ['country'], long_name: 'Edit Country' }
        ]
      };

      component.handleCompanyAddressChange(mockAddress, 'edit');

      expect(component.companyEditForm.get('city').value).toBe('Edit City');
      expect(component.companyEditForm.get('state').value).toBe('Edit State');
      expect(component.companyEditForm.get('country').value).toBe('Edit Country');
    });
  });

  describe('Form Reset and Close Operations', () => {
    it('should reset and close modal', () => {
      component.resetAndClose();

      expect(mockModalRef.hide).toHaveBeenCalled();
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.editFormSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(component.companyLogo).toEqual({});
      expect(component.editBeforeDFOW).toEqual([]);
      expect(component.companiesFiles).toEqual([]);
    });

    it('should handle reset form - no action', () => {
      component.resetForm('no');

      expect(component.modalRef1.hide).toHaveBeenCalled();
    });

    it('should handle reset form - yes action', () => {
      component.resetForm('yes');

      expect(component.modalRef1.hide).toHaveBeenCalled();
      expect(mockModalRef.hide).toHaveBeenCalled();
      expect(component.submitted).toBe(false);
      expect(component.companyLogo).toEqual({});
    });

    it('should close new company form with changes', () => {
      component.companyDetailsForm.markAsDirty();
      component.companyDetailsForm.markAsTouched();
      jest.spyOn(component, 'openModalPopup');

      component.close({} as TemplateRef<any>, 'newCompany');

      expect(component.openModalPopup).toHaveBeenCalled();
    });

    it('should close new company form without changes', () => {
      component.companyDetailsForm.markAsPristine();
      component.companyDetailsForm.markAsUntouched();
      jest.spyOn(component, 'resetAndClose');

      component.close({} as TemplateRef<any>, 'newCompany');

      expect(component.resetAndClose).toHaveBeenCalled();
    });

    it('should close edit company form with changes', () => {
      component.companyEditForm.markAsDirty();
      component.companyEditForm.markAsTouched();
      jest.spyOn(component, 'openModalPopup');

      component.close({} as TemplateRef<any>, 'editCompany');

      expect(component.openModalPopup).toHaveBeenCalled();
    });

    it('should close import companies form', () => {
      jest.spyOn(component, 'ImportMultipleCompaniesCloseConfirmation');

      component.close({} as TemplateRef<any>, 'importCompanies');

      expect(component.ImportMultipleCompaniesCloseConfirmation).toHaveBeenCalled();
    });
  });

  describe('Keyboard Event Handlers', () => {
    it('should handle down keydown for clear action', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      jest.spyOn(component, 'clear');

      component.handleDownKeydown(mockEvent, null, null, 'clear');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.clear).toHaveBeenCalled();
    });

    it('should handle down keydown for filter action', () => {
      const mockEvent = { key: ' ', preventDefault: jest.fn() } as any;
      const mockTemplate = {} as TemplateRef<any>;
      jest.spyOn(component, 'openModal1');

      component.handleDownKeydown(mockEvent, mockTemplate, null, 'filter');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.openModal1).toHaveBeenCalledWith(mockTemplate);
    });

    it('should handle down keydown for edit action', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      jest.spyOn(component, 'openEditModal');

      component.handleDownKeydown(mockEvent, 0, {} as TemplateRef<any>, 'edit');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.openEditModal).toHaveBeenCalledWith(0, {} as TemplateRef<any>);
    });

    it('should handle down keydown for delete action', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      jest.spyOn(component, 'openDeleteModal');

      component.handleDownKeydown(mockEvent, 0, {} as TemplateRef<any>, 'delete');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.openDeleteModal).toHaveBeenCalledWith(0, {} as TemplateRef<any>);
    });

    it('should handle down keydown for remove action', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      jest.spyOn(component, 'removeFile');

      component.handleDownKeydown(mockEvent, 0, null, 'remove');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.removeFile).toHaveBeenCalledWith(0);
    });

    it('should not handle down keydown for invalid key', () => {
      const mockEvent = { key: 'Tab', preventDefault: jest.fn() } as any;

      component.handleDownKeydown(mockEvent, null, null, 'clear');

      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should show error with details', () => {
      const mockError = {
        message: {
          details: [{ error: 'Test error message' }]
        }
      };

      component.showError(mockError);

      expect(component.submitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.editFormSubmitted).toBe(false);
      expect(toastrService.error).toHaveBeenCalledWith('Test error message');
    });

    it('should handle add company error', () => {
      const mockCompany = { companyName: 'Test Company', selectedItems: [] };
      component.companyDetailsForm.patchValue(mockCompany);
      mockProjectService.addCompany.mockReturnValue(throwError(() => ({ message: { statusCode: 400, details: [{ error: 'Add failed' }] } })));

      component.addCompanyToProject(mockCompany);

      expect(component.formSubmitted).toBe(false);
      expect(component.submitted).toBe(false);
    });

    it('should handle edit company error', () => {
      const mockCompany = { companyName: 'Test Company', selectedItems: [] };
      mockProjectService.editCompany.mockReturnValue(throwError(() => ({ message: { statusCode: 400, details: [{ error: 'Edit failed' }] } })));

      component.editCompany(mockCompany);

      expect(component.editSubmitted).toBe(false);
      expect(component.editFormSubmitted).toBe(false);
    });

    it('should handle getCompanies error', () => {
      mockProjectService.listCompany.mockReturnValue(throwError(() => new Error('API Error')));

      component.getCompanies();

      // Error handling is asynchronous, so we need to wait
      setTimeout(() => {
        expect(component.loader).toBe(false);
      });
    });
  });

  describe('Constructor and Initialization', () => {
    it('should set title on construction', () => {
      const titleService = TestBed.inject(Title);
      expect(titleService.setTitle).toHaveBeenCalledWith('Follo - Companies');
    });

    it('should handle userData subscription with admin role', () => {
      const mockUserData = { RoleId: 2 };
      mockProjectService.userData = of(mockUserData);

      // Create new component instance to test constructor
      const newFixture = TestBed.createComponent(CompaniesComponent);
      const newComponent = newFixture.componentInstance;

      expect(newComponent.userData).toEqual(mockUserData);
      expect(newComponent.isAdmin).toBe(true);
    });

    it('should handle userData subscription with non-admin role', () => {
      const mockUserData = { RoleId: 4 };
      mockProjectService.userData = of(mockUserData);

      const newFixture = TestBed.createComponent(CompaniesComponent);
      const newComponent = newFixture.componentInstance;

      expect(newComponent.isAdmin).toBe(false);
    });

    it('should handle companyFileUpload subscription - uploading', () => {
      const mockUploadData = { status: 'companiesUploading' };
      mockProjectService.companyFileUpload = of(mockUploadData);

      const newFixture = TestBed.createComponent(CompaniesComponent);
      const newComponent = newFixture.componentInstance;

      expect(newComponent.bulkCompanyUploadInProgress).toBe(true);
    });

    it('should handle companyFileUpload subscription - upload done', () => {
      const mockUploadData = { status: 'companyUploadDone' };
      mockProjectService.companyFileUpload = of(mockUploadData);
      jest.spyOn(mockProjectService, 'uploadBulkNdrFile');

      const newFixture = TestBed.createComponent(CompaniesComponent);
      const newComponent = newFixture.componentInstance;

      expect(newComponent.bulkCompanyUploadInProgress).toBe(false);
      expect(newComponent.sortColumn).toBe('id');
      expect(mockProjectService.uploadBulkNdrFile).toHaveBeenCalledWith({ status: '' });
    });

    it('should handle projectParent subscription', () => {
      const mockProjectData = { ProjectId: 123, ParentCompanyId: 456 };
      mockProjectService.projectParent = of(mockProjectData);

      const newFixture = TestBed.createComponent(CompaniesComponent);
      const newComponent = newFixture.componentInstance;

      expect(newComponent.ProjectId).toBe(123);
      expect(newComponent.ParentCompanyId).toBe(456);
      expect(newComponent.loader).toBe(true);
      expect(newComponent.defineList).toEqual([]);
    });
  });

  describe('Additional Methods', () => {
    it('should get definable work', () => {
      const mockResponse = { data: [{ id: 1, DFOW: 'Test DFOW' }] };
      mockProjectService.getDefinableWork.mockReturnValue(of(mockResponse));

      component.getDefinable();

      expect(projectService.getDefinableWork).toHaveBeenCalledWith({
        ProjectId: component.ProjectId,
        ParentCompanyId: component.ParentCompanyId
      });
      expect(component.defineList).toEqual(mockResponse.data);
      expect(component.modalLoader).toBe(false);
      expect(component.dropdownSettings).toBeDefined();
    });

    it('should open delete modal for specific index', () => {
      component.companiesList = [{ id: 1, companyName: 'Test' }];
      jest.spyOn(component, 'openModal');

      component.openDeleteModal(0, {} as TemplateRef<any>);

      expect(component.deleteIndex[0]).toBe(1);
      expect(component.currentDeleteId).toBe(0);
      expect(component.remove).toBe(false);
      expect(component.openModal).toHaveBeenCalled();
    });

    it('should open delete modal for all items', () => {
      jest.spyOn(component, 'openModal');

      component.openDeleteModal(-1, {} as TemplateRef<any>);

      expect(component.remove).toBe(true);
      expect(component.openModal).toHaveBeenCalled();
    });

    it('should remove file from companiesFiles', () => {
      component.companiesFiles = [
        { relativePath: 'file1.xlsx' } as any,
        { relativePath: 'file2.xlsx' } as any
      ];

      component.removeFile(0);

      expect(component.companiesFiles.length).toBe(1);
    });

    it('should set edit form with company data', () => {
      const mockCompany = {
        id: 1,
        companyName: 'Test Company',
        scope: 'Test Scope',
        address: 'Test Address',
        secondAddress: 'Test Second Address',
        country: 'Test Country',
        state: 'Test State',
        city: 'Test City',
        zipCode: '12345',
        website: 'http://test.com',
        logo: 'test-logo.jpg',
        defineEditData: [
          { DeliverDefineWork: { id: 1, DFOW: 'Test DFOW' } }
        ]
      };
      jest.spyOn(component, 'openModal');

      component.setEditForm(mockCompany, {} as TemplateRef<any>, false);

      expect(component.editedCompanyId).toBe(1);
      expect(component.companyEditForm.get('companyName').value).toBe('Test Company');
      expect(component.editLogo).toBe('test-logo.jpg');
      expect(component.openModal).toHaveBeenCalled();
    });

    it('should open edit modal for specific company', () => {
      component.companiesList = [{ id: 1, companyName: 'Test' }];
      jest.spyOn(component, 'setEditForm');

      component.openEditModal(0, {} as TemplateRef<any>);

      expect(component.editIndex).toBe(0);
      expect(component.setEditForm).toHaveBeenCalledWith(component.companiesList[0], {} as TemplateRef<any>, false);
    });

    it('should open edit modal for parent company', () => {
      component.parentCompany = { id: 1, companyName: 'Parent' };
      jest.spyOn(component, 'setEditForm');

      component.openEditModal(-1, {} as TemplateRef<any>);

      expect(component.setEditForm).toHaveBeenCalledWith(component.parentCompany, {} as TemplateRef<any>, true);
    });

    it('should check if arrays are different by property', () => {
      const array1 = [{ DFOW: 'Test1' }, { DFOW: 'Test2' }];
      const array2 = [{ DFOW: 'Test1' }, { DFOW: 'Test3' }];

      const result = component.areDifferentByProperty(array1, array2, 'DFOW');

      expect(result).toBe(true);
      expect(component.arrayValue).toContain('Test1');
      expect(component.arrayValue).toContain('Test2');
      expect(component.arrayValue).toContain('Test3');
    });

    it('should set definable and open modal', () => {
      jest.spyOn(component, 'openModal');

      component.setDefinable({} as TemplateRef<any>);

      expect(component.companyEditForm.get('selectedItems').value).toBe('');
      expect(component.openModal).toHaveBeenCalled();
    });
  });

  describe('File Import Operations', () => {
    it('should handle company import file dropped', () => {
      const mockFiles = [
        { name: 'companies.xlsx', type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }
      ] as any;
      const mockEvent = { dataTransfer: { files: mockFiles } } as any;

      component.companyImportFiledropped(mockEvent);

      expect(component.companiesFiles).toEqual(mockFiles);
    });

    it('should handle invalid file type in import', () => {
      const mockFiles = [
        { name: 'companies.txt', type: 'text/plain' }
      ] as any;
      const mockEvent = { dataTransfer: { files: mockFiles } } as any;

      component.companyImportFiledropped(mockEvent);

      expect(toastrService.error).toHaveBeenCalledWith('Please upload .xlsx file only.', 'OOPS!');
    });

    it('should import data successfully', () => {
      component.companiesFiles = [{ name: 'test.xlsx' }] as any;
      mockDeliveryService.importCompany.mockReturnValue(of({ status: 201 }));

      component.importData();

      expect(mockDeliveryService.importCompany).toHaveBeenCalled();
      expect(toastrService.success).toHaveBeenCalledWith('Companies imported successfully.', 'Success!');
    });

    it('should handle import data error', () => {
      component.companiesFiles = [{ name: 'test.xlsx' }] as any;
      mockDeliveryService.importCompany.mockReturnValue(throwError(() => ({ message: { details: [{ error: 'Import failed' }] } })));

      component.importData();

      expect(toastrService.error).toHaveBeenCalledWith('Import failed');
    });

    it('should download template', () => {
      mockDeliveryService.importCompanyTemplate.mockReturnValue(of(new Blob()));
      const mockLink = { click: jest.fn(), href: '', download: '' };
      jest.spyOn(document, 'createElement').mockReturnValue(mockLink as any);
      jest.spyOn(document.body, 'appendChild').mockImplementation();
      jest.spyOn(document.body, 'removeChild').mockImplementation();

      component.download();

      expect(mockDeliveryService.importCompanyTemplate).toHaveBeenCalled();
      expect(mockLink.click).toHaveBeenCalled();
    });

    it('should handle download template error', () => {
      mockDeliveryService.importCompanyTemplate.mockReturnValue(throwError(() => new Error('Download failed')));

      component.download();

      expect(toastrService.error).toHaveBeenCalledWith('Something went wrong while downloading template.', 'OOPS!');
    });

    it('should handle import multiple companies close confirmation - no', () => {
      jest.spyOn(component, 'openModalPopup');

      component.ImportMultipleCompaniesCloseConfirmation({} as TemplateRef<any>);

      expect(component.openModalPopup).toHaveBeenCalled();
    });

    it('should handle import close with no changes', () => {
      component.companiesFiles = [];
      jest.spyOn(component, 'resetAndClose');

      component.close({} as TemplateRef<any>, 'importCompanies');

      expect(component.resetAndClose).toHaveBeenCalled();
    });
  });

  describe('Form Validation Edge Cases', () => {
    it('should validate zip code with alphanumeric input', () => {
      expect(component.alphaNumForZipCode({ which: 65 })).toBe(true); // A
      expect(component.alphaNumForZipCode({ which: 48 })).toBe(true); // 0
      expect(component.alphaNumForZipCode({ which: 33 })).toBe(false); // !
    });

    it('should validate company name input', () => {
      expect(component.alphaNumericOnlyForCompanyName({ keyCode: 65 })).toBe(true); // A
      expect(component.alphaNumericOnlyForCompanyName({ keyCode: 48 })).toBe(true); // 0
      expect(component.alphaNumericOnlyForCompanyName({ keyCode: 32 })).toBe(true); // space
    });

    it('should validate number only input for zip code', () => {
      expect(component.numberOnlyForCompanyZipCode({ which: 48 })).toBe(true); // 0
      expect(component.numberOnlyForCompanyZipCode({ which: 57 })).toBe(true); // 9
      expect(component.numberOnlyForCompanyZipCode({ which: 65 })).toBe(false); // A
    });
  });

  describe('Additional Edge Cases', () => {
    it('should handle onSubmit with invalid form', () => {
      component.companyDetailsForm.patchValue({ companyName: '' });
      component.companyDetailsForm.get('companyName').markAsTouched();

      component.onSubmit();

      expect(component.submitted).toBe(true);
      expect(projectService.addCompany).not.toHaveBeenCalled();
    });

    it('should handle onEditSubmit with invalid form', () => {
      component.companyEditForm.patchValue({ companyName: '' });
      component.companyEditForm.get('companyName').markAsTouched();

      component.onEditSubmit();

      expect(component.editSubmitted).toBe(true);
      expect(projectService.editCompany).not.toHaveBeenCalled();
    });

    it('should handle company name duplication with invalid form', () => {
      component.companyDetailsForm.patchValue({ companyName: '' });

      component.checkCompanyNameDuplication({} as TemplateRef<any>, 'addCompany');

      expect(component.submitted).toBe(true);
      expect(projectService.checkExistCompany).not.toHaveBeenCalled();
    });

    it('should handle checkCompanyExists with no duplicates', () => {
      const existCompany = {
        data: {
          existInProject: [],
          sameAsParentCompany: null
        }
      };
      jest.spyOn(component, 'onSubmit');

      component.checkCompanyExists(existCompany, {} as TemplateRef<any>, 'addCompany');

      expect(component.onSubmit).toHaveBeenCalled();
    });

    it('should handle arrays that are not different by property', () => {
      const array1 = [{ DFOW: 'Test1' }, { DFOW: 'Test2' }];
      const array2 = [{ DFOW: 'Test1' }, { DFOW: 'Test2' }];

      const result = component.areDifferentByProperty(array1, array2, 'DFOW');

      expect(result).toBe(false);
    });

    it('should handle address change with missing components', () => {
      const mockAddress = {
        address_components: [
          { types: ['locality'], long_name: 'Test City' }
        ]
      };

      component.handleCompanyAddressChange(mockAddress, 'add');

      expect(component.companyDetailsForm.get('city').value).toBe('Test City');
      expect(component.companyDetailsForm.get('address').value).toBe('');
    });

    it('should handle file upload with no files', () => {
      const mockEvent = { target: { files: [] } };

      component.onFileChangeEvent(mockEvent);

      expect(component.file).toBeUndefined();
    });

    it('should handle edit file upload with no files', () => {
      const mockEvent = { target: { files: [] } };

      component.onEditFileChangeEvent(mockEvent);

      expect(component.editFile).toBeUndefined();
    });

    it('should handle removeItem with no selected items', () => {
      component.selectAll = false;
      component.companiesList = [
        { id: 1, companyName: 'Company 1', isChecked: false },
        { id: 2, companyName: 'Company 2', isChecked: false }
      ];

      component.removeItem();

      expect(component.deleteIndex).toEqual([]);
    });

    it('should handle successful company addition with logo upload', () => {
      const mockCompany = { companyName: 'Test Company', zipCode: '12345' };
      component.companyDetailsForm.patchValue(mockCompany);
      component.companyLogo = { logo: 'base64-logo-data' };
      component.file = new File([''], 'logo.jpg', { type: 'image/jpeg' });

      mockProjectService.addCompany.mockReturnValue(of({ success: true, data: { id: 1 } }));
      mockProjectService.companyLogoUpload.mockReturnValue(of({ data: [{ Location: 'logo-url' }] }));

      component.onSubmit();

      expect(projectService.addCompany).toHaveBeenCalled();
      expect(projectService.companyLogoUpload).toHaveBeenCalled();
    });

    it('should handle successful company edit with logo upload', () => {
      const mockCompany = { companyName: 'Updated Company', zipCode: '12345' };
      component.companyEditForm.patchValue(mockCompany);
      component.editCompanyLogo = { logo: 'base64-logo-data' };
      component.editFile = new File([''], 'logo.jpg', { type: 'image/jpeg' });
      component.editedCompanyId = 1;

      mockProjectService.editCompany.mockReturnValue(of({ success: true }));
      mockProjectService.companyLogoUpload.mockReturnValue(of({ data: [{ Location: 'logo-url' }] }));

      component.onEditSubmit();

      expect(projectService.editCompany).toHaveBeenCalled();
      expect(projectService.companyLogoUpload).toHaveBeenCalled();
    });
  });
});
