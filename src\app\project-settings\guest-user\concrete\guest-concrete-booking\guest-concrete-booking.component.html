
<div class="modal-header p-3 border-bottom-grayb">
    <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">
      <img src="./assets/images/add-concrete.svg" alt="Delivery" class="me-2" />New Concrete Booking
    </h1>

  </div>
  <div class="modal-body newupdate-alignments taginput--heightfix p-3" *ngIf="!modalLoader">
    <div class="addcalendar-details">
      <form
        name="form"
        class="custom-material-form add-concrete-material-form"
        [formGroup]="concreteRequest"
        novalidate
      >
        <div class="row">
          <div class="col-md-6">
            <div class="form-group mb-0">
              <label class="fs12 fw600 mb-0" for="description"
                >Description
                <span class="color-red">
                  <sup>*</sup>
                </span>
              </label>
              <textarea  id="description"
                class="form-control fs11 radius0 mt-1"
                rows="2"
                maxlength="150"
                formControlName="description"
              ></textarea>
              <div class="color-red" *ngIf="submitted && concreteRequest.get('description').errors">
                <small *ngIf="concreteRequest.get('description').errors.required"
                  >*Description is Required.</small
                >
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group pt-0 mt-0">
              <label class="fs12 fw600 mb-0"  for="conID">Concrete Booking ID</label>
              <input   id="conID"
                type="text"
                class="form-control fs11 material-input p-0 h-25 mt-2 ps-2 ms-2"
                disabled="disabled"
                value="{{ concreteRequest.get('ConcreteRequestId').value }}"
              />
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6 primary-tooltip">
            <div class="form-group mb-0 mt-2 timezone-formgroup timezone-buttonpadding">
              <label class="fs12 fw600" for="location">Location<span class="color-red"><sup>*</sup></span>
                <div class="dot-border-info location-border-info tooltip location-tooltip">
                  <span class="fw700 info-icon fs12">i</span>
                  <span class="tooltiptext tooltiptext-info"
                    >Where will the materials/equipment be installed</span
                  >
                  <div class="arrow-down"></div>
                </div></label>
              <ng-multiselect-dropdown  id="location"
              [placeholder]="'Choose Location'"
              [settings]="locationDropdownSettings"
              [data]="locationDropdown"
              (onSelect)="locationSelected($event)"
              formControlName="LocationId"
              [(ngModel)]="defaultLocationValue"
            >
            </ng-multiselect-dropdown>
            <div class="color-red" *ngIf="submitted && concreteRequest.get('LocationId').errors">
              <small *ngIf="concreteRequest.get('LocationId').errors.required"
                >*Location is Required.</small
              >
            </div>
            </div>
          </div>
          <div class="col-md-6 pt-4 ps-3 additional-location-detail">
            <div class="floating-concrete mt-3">
              <div class="form-group floating-label">
                <input id="addlocation"
                  class="floating-input form-control fs12 px-0"
                  type="text"
                  placeholder=" "
                  formControlName="location"
                />
                <label  for="addlocation" class="fs12 fw600 m-0 color-grey11"
                  >Additional Location Details
                </label>

              </div>
            </div>
          </div>

        </div>
        <div class="row">
          <div class="col-md-6">
          <div class="form-group taginput-height">
            <label class="fs12 fw600" for="resPerson"
              >Responsible Person <span class="color-red"><sup>*</sup></span></label
            >
            <tag-input id="resPerson"
              [onlyFromAutocomplete]="true"
              [placeholder]="' '"
              secondaryPlaceholder=" "
              formControlName="responsiblePersons"
              [onTextChangeDebounce]="500"
              class="tag-layout"
              [identifyBy]="'id'"
              [displayBy]="'email'"
            >
              <tag-input-dropdown
                [showDropdownIfEmpty]="false"
                [displayBy]="'email'"
                [identifyBy]="'id'"
                [autocompleteObservable]="requestAutoEditcompleteItems"
                [appendToBody]="false"
              >
                <ng-template let-item="item" let-index="index">
                  {{ item.email }}
                </ng-template>
              </tag-input-dropdown>
            </tag-input>
            <div
              class="color-red"
              *ngIf="submitted && concreteRequest.get('responsiblePersons').errors"
            >
              <small>*Please choose Responsible person.</small>
            </div>
          </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <label class="fs12 fw600 mb-0" for="date">Date:</label>
            <div class="input-group mx-0 pt-2">
              <input id="date"
                class="form-control fs12 ps-0 fw500 material-input"
                #dp1="bsDatepicker"
                bsDatepicker
                [bsConfig]="{
                  isAnimated: true,
                  showWeekNumbers: false,
                  customTodayClass: 'today'
                }"
                formControlName="concretePlacementDate"
                (ngModelChange)="placementDate(); showMonthlyRecurrence()"
              />
                <span class="input-group-text">
                  <img
                    src="./assets/images/date.svg"
                    class="h-12px"
                    alt="Date"
                    (click)="dp1.toggle()"
                    [attr.aria-expanded]="dp1.isOpen"
                    (keydown)="dp1.toggle()"
                  />
                </span>
            </div>
            <div
              class="color-red"
              *ngIf="submitted && concreteRequest.get('concretePlacementDate').errors"
            >
              <small *ngIf="concreteRequest.get('concretePlacementDate').errors.required"
                >*Date is Required.</small
              >
            </div>
          </div>
          <div class="col-md-6">
            <div class="timezone-flex pt-2">
              <div class="input-group mb-0 delivery-time">
                <label class="fs12 fw600" for="starttime">Placement Start Time</label>
                <timepicker id="starttime"
                  [formControlName]="'concretePlacementStart'"
                  (ngModelChange)="changeDate($event)"
                  (keypress)="numberOnly($event)"
                  class="mt-2"
                >
                </timepicker>
              </div>
              <div
                class="color-red"
                *ngIf="submitted && concreteRequest.get('concretePlacementStart').errors"
              >
                <small *ngIf="concreteRequest.get('concretePlacementStart').errors.required"
                  >*Placement Start Time is Required</small
                >
              </div>
              <div class="input-group mb-0 delivery-time">
                <label class="fs12 fw600" for="antComDate">Anticipated Completion Time</label>
                <timepicker id="antComDate"
                  [formControlName]="'concretePlacementEnd'"
                  class="mt-2"
                  (ngModelChange)="deliveryEndTimeChangeDetection()"
                  (keypress)="numberOnly($event)"
                >
                </timepicker>
              </div>
              <div
                class="color-red"
                *ngIf="submitted && concreteRequest.get('concretePlacementEnd').errors"
              >
                <small *ngIf="concreteRequest.get('concretePlacementEnd').errors.required"
                  >*Anticipated Completion Time is Required</small
                >
              </div>
            </div>
          </div>
        </div>
        <div class="row my-2">
          <div class="col-md-6">
        <div class="form-group mb-0 mt-2 timezone-formgroup timezone-buttonpadding">
          <label class="fs12 fw600" for="timezone">Time Zone</label>

          <ng-multiselect-dropdown id="timezone"
            [placeholder]="'Choose TimeZone'"
            [settings]="dropdownSettings"
            [data]="timezoneList"
            (change)="timeZoneSelected($event.target.value)"
            formControlName="TimeZoneId"
            [(ngModel)]="defaultValue"
          >
          </ng-multiselect-dropdown>
          <div class="color-red" *ngIf="submitted && concreteRequest.get('TimeZoneId').errors">
            <small *ngIf="concreteRequest.get('TimeZoneId').errors.required"
              >*TimeZone is Required.</small
            >
          </div>
        </div>
      </div>
    </div>

        <div class="row py-2">
          <div class="col-md-3 concrete-header">
            <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Concrete Details</h1>
          </div>
          <div class="col-md-9">
            <div class="line-1"></div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6 pt-0 add-concrete-dropdown">
            <div class="form-group guest-company-select mb-0" id="company-select8">
              <label class="fs12 fw600 m-0 pb-0" for="conSup">Concrete Supplier <span class="color-red">
                <sup>*</sup>
              </span></label>
              <ng-multiselect-dropdown id="conSup"
                [placeholder]="'Select'"
                [settings]="concreteSupplierDropdownSettings"
                [data]="concreteSupplierDropdown"
                formControlName="concreteSupplier"
                [(ngModel)]="responsibleCompanySelectedItems"
                class="mt-0"
              >
              </ng-multiselect-dropdown>
              <div
                class="color-red"
                *ngIf="submitted && concreteRequest.get('concreteSupplier').errors"
              >
                <small *ngIf="concreteRequest.get('concreteSupplier').errors.required"
                  >*Concrete Supplier is Required.</small
                >
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group customised-tag-input mb-0">
              <label class="fs12 fw600 m-0" for="mixDes"
                >Mix Design</label
              >
              <tag-input  id="mixDes"
                [onlyFromAutocomplete]="false"
                [placeholder]="' '"
                secondaryPlaceholder=" "
                formControlName="mixDesign"
                [(ngModel)]="mixDesignList"
                (ngModelChange)="checkMixDesignDuplication($event)"
                [onTextChangeDebounce]="500"
                class="tag-layout mix-design"
                [identifyBy]="'id'"
                [displayBy]="'mixDesign'"
              >
                <tag-input-dropdown
                  [showDropdownIfEmpty]="false"
                  [displayBy]="'mixDesign'"
                  [identifyBy]="'id'"
                  [autocompleteItems]="mixDesignDropdown"
                  [appendToBody]="false"
                >
                  <ng-template let-item="item" let-index="index">
                    <div class="tag-input-sample fs12">
                      {{ item.mixDesign }}
                    </div>
                  </ng-template>
                </tag-input-dropdown>
              </tag-input>
              <p class="color-grey11 fs12 mb-0">*Type and press enter to create the mix design tag</p>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <div class="floating-concrete mt-3">
              <div class="form-group floating-label">
                <input  id="order"
                  class="floating-input form-control fs12 px-0"
                  type="text"
                  placeholder=" "
                  formControlName="concreteOrderNumber"
                  (keypress)="numberOnly($event)"
                />
                <label class="fs12 fw600 m-0 color-grey11" for="order"
                  >Order Number
                </label>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="floating-concrete mt-3">
              <div class="form-group floating-label">
                <input id="slump"
                  class="floating-input form-control fs12 px-0"
                  type="text"
                  placeholder=" "
                  formControlName="slump"
                />
                <label class="fs12 fw600 m-0 color-grey11" for="slump"
                  >Slump
                </label>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <div class="floating-concrete mt-3">
              <div class="form-group floating-label">
                <input  id="truckSpac"
                  class="floating-input form-control fs12 px-0"
                  type="text"
                  placeholder=" "
                  formControlName="truckSpacingHours"
                />

                <label class="fs12 fw600 m-0 color-grey11" for="truckSpac"
                  >Truck Spacing
                </label>

              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="floating-concrete mt-3">
              <div class="form-group floating-label">
                <input  id="qnt"
                  class="floating-input form-control fs12 px-0"
                  type="text"
                  placeholder=" "
                  formControlName="concreteQuantityOrdered"
                />
                <label class="fs12 fw600 m-0 color-grey11"  for="qnt"
                  >Quantity Ordered (CY)
                </label>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <div class="floating-concrete mt-3">
              <div class="form-group floating-label">
                <input  id="primeOrder"
                  class="floating-input form-control fs12 px-0"
                  type="text"
                  placeholder=" "
                  formControlName="primerForPump"
                />
                <label class="fs12 fw600 m-0 color-grey11"  for="primeOrder"
                  >Primer ordered for the Pump
                  <span class="color-red"></span>
                </label>

              </div>
            </div>
          </div>
          <div class="col-md-6 mt-2">
            <ul
              class="small-switch list-group list-group-horizontal justify-content-start mnb28 mb-1"
              id="switch-control4"
            >
              <span class="fs12 my-auto">Concrete Confirmed</span>
              <li class="fs12 list-group-item border-0 p-0 color-grey11">
                <ui-switch
                  switchColor="#fff"
                  defaultBoColor="#CECECE"
                  defaultBgColor="#CECECE"
                  formControlName="isConcreteConfirmed"
                  (change)="changeConcreteConfirmed($event)"
                  class="ms-2 small-switch"
                >
                </ui-switch>
              </li>
            </ul>
            <div
              class="fs12 fw700 text-black mt-1"
              *ngIf="concreteRequest.get('isConcreteConfirmed').value === true"
            >
              Confirmed on
              {{ concreteRequest.get('concreteConfirmedOn').value | date : 'medium' }}
            </div>
            <div
              class="color-red"
              *ngIf="submitted && concreteRequest.get('isConcreteConfirmed').errors"
            >
              <small *ngIf="concreteRequest.get('isConcreteConfirmed').errors.required"
                >*Concrete Confirmed is Required.</small
              >
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group mt-0 guest-company-select">
              <label class="fs12 fw600"  for="recurrence">Recurrence<sup>*</sup></label>
              <select  id="recurrence"
                class="form-control select-dropdown fs12 material-input px-1"
                formControlName="recurrence"
                (change)="onRecurrenceSelect($event.target.value)"
              >
                <option value="" disabled selected hidden>
                  Select Recurrence
                  <span class="color-red">
                    <sup>*</sup>
                  </span>
                </option>
                <option *ngFor="let type of recurrence" value="{{ type.value }}">
                  {{ type.value }}
                </option>
              </select>
              <div class="color-red" *ngIf="submitted && concreteRequest.get('recurrence').errors">
                <small *ngIf="concreteRequest.get('recurrence').errors.required"
                  >*Recurrence is required</small
                >
              </div>
            </div>
            <div
              class="row"
              *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
            >
              <div
                class="col-md-12 mt-md-0"
                *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
              >
                <label class="fs12 fw600"  for="repeatEvery">Repeat Every</label>
              </div>
              <div
                class="col-md-6 mt-md-0"
                *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
              >
                <div class="form-group">
                  <input  id="repeatEvery"
                    type="text"
                    formControlName="repeatEveryCount"
                    class="form-control fs12 material-input p-0"
                    (input)="changeRecurrenceCount($event.target.value)"
                    min="1"
                  />
                </div>
              </div>
              <div class="col-md-6 mt-md-0" *ngIf="isRepeatWithSingleRecurrence">
                <div class="form-group">
                  <select
                    class="form-control select-dropdown fs12 material-input px-2"
                    formControlName="repeatEveryType"
                    (change)="chooseRepeatEveryType($event.target.value)"
                  >
                    <option value="" disabled selected hidden>Select Recurrence</option>
                    <option *ngFor="let type of repeatWithSingleRecurrence" value="{{ type.value }}">
                      {{ type.value }}
                    </option>
                  </select>
                </div>
              </div>
              <div
                class="col-md-6 mt-md-0"
                *ngIf="isRepeatWithMultipleRecurrence || showRecurrenceTypeDropdown"
              >
                <div class="form-group">
                  <select
                    class="form-control select-dropdown fs12 material-input px-2"
                    formControlName="repeatEveryType"
                    (change)="chooseRepeatEveryType($event.target.value)"
                  >
                    <option value="" disabled selected hidden>Select Recurrence</option>
                    <option
                      *ngFor="let type of repeatWithMultipleRecurrence"
                      value="{{ type.value }}"
                    >
                      {{ type.value }}
                    </option>
                  </select>
                </div>
              </div>
            </div>
            <div class="row addcalendar-displaydays ml3">
              <div
                class="col-md-12 ps-0 pt-0"
                *ngIf="
                  (selectedRecurrence === 'Weekly' ||
                    isRepeatWithMultipleRecurrence ||
                    isRepeatWithSingleRecurrence) &&
                  selectedRecurrence !== 'Monthly' &&
                  selectedRecurrence !== 'Yearly'
                "
              >
                <ul class="displaylists ps-0">
                  <li *ngFor="let item of weekDays; let i = index" class="fs12 list-inline-item">
                    <input
                      type="checkbox"
                      [disabled]="item.isDisabled"
                      [value]="item.value"
                      class="d-none"
                      id="days-{{ i }}"
                      (change)="onChange($event)"
                      [checked]="item.checked"
                    />
                    <label for="days-{{ i }}">{{ item.display }}</label>
                  </li>
                  <div class="color-red" *ngIf="submitted && concreteRequest.controls['days'].errors">
                    <small *ngIf="concreteRequest.controls['days'].errors.required">*Required </small>
                  </div>
                </ul>
              </div>
            </div>
            <div
              class="row ml3"
              *ngIf="selectedRecurrence === 'Monthly' || selectedRecurrence === 'Yearly'"
            >
              <div class="col-md-8 mt-md-0 p-0">
                <div class="form-check">
                  <input
                    class="form-check-input c-pointer"
                    type="radio"
                    formControlName="chosenDateOfMonth"
                    id="flexRadioDefault1"
                    [value]="1"
                    (change)="changeMonthlyRecurrence()"
                  />
                  <label class="form-check-label fs12 color-orange" for="flexRadioDefault1">
                    On day {{ monthlyDate }}
                  </label>
                </div>
                <div class="form-check">
                  <input
                    class="form-check-input c-pointer"
                    type="radio"
                    formControlName="chosenDateOfMonth"
                    id="flexRadioDefault2"
                    [value]="2"
                    (change)="changeMonthlyRecurrence()"
                  />
                  <label class="form-check-label fs12 color-orange" for="flexRadioDefault2">
                    On the {{ monthlyDayOfWeek }}
                    <p *ngIf="selectedRecurrence === 'Yearly'">
                      of
                      {{ concreteRequest.get('concretePlacementDate').value | date : 'LLLL' }}
                    </p>
                  </label>
                </div>
                <div class="form-check" *ngIf="enableOption">
                  <input
                    class="form-check-input c-pointer"
                    type="radio"
                    formControlName="chosenDateOfMonth"
                    id="flexRadioDefault3"
                    [value]="3"
                    (change)="changeMonthlyRecurrence()"
                  />
                  <label class="form-check-label fs12 color-orange" for="flexRadioDefault3">
                    On the
                    {{ monthlyLastDayOfWeek }}
                    <p *ngIf="selectedRecurrence === 'Yearly'">
                      of
                      {{ concreteRequest.get('concretePlacementDate').value | date : 'LLLL' }}
                    </p>
                  </label>
                </div>
                <div>
                  <div
                    class="color-red"
                    *ngIf="
                      submitted &&
                      (concreteRequest.get('monthlyRepeatType')?.errors ||
                        concreteRequest.get('dateOfMonth')?.errors)
                    "
                  >
                    <small *ngIf="concreteRequest.get('monthlyRepeatType')?.errors?.required"
                      >*required</small
                    >
                    <small *ngIf="concreteRequest.get('dateOfMonth')?.errors?.required"
                      >*required</small
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div
              class="row"
              *ngIf="
                selectedRecurrence === 'Daily' ||
                selectedRecurrence === 'Monthly' ||
                selectedRecurrence === 'Yearly' ||
                selectedRecurrence === 'Weekly'
              "
            >
              <div class="col-md-12 float-start mt-0">
                <label class="fs12 fw600" for="endDate">End Date</label>
                <div class="input-group mb-3">
                  <input  id="endDate"
                    class="form-control fs12 fw500 material-input"
                    #dp="bsDatepicker"
                    bsDatepicker
                    formControlName="endDate"
                    [minDate]="recurrenceMinDate"
                    placement="top"
                    [minDate]="recurrenceMinDate"
                    placeholder="End Date *"
                    [bsConfig]="{
                      isAnimated: true,
                      showWeekNumbers: false,
                      customTodayClass: 'today'
                    }"
                    (ngModelChange)="showMonthlyRecurrence()"
                  />
                    <span class="input-group-text">
                      <img
                        src="./assets/images/date.svg"
                        class="h-12px"
                        alt="Date"
                        (click)="dp.toggle()"
                        (keydown)="dp.toggle()"
                        [attr.aria-expanded]="dp.isOpen"
                      />
                    </span>
                </div>
              </div>
            </div>
            <div class="row addcalendar-displaydays">
              <div class="col-md-12 mt-md-0 pb-0" *ngIf="message">
                <p class="fs12 color-grey11">
                  <span class="color-red fw-bold">*</span>
                  {{ message }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div class="row py-2">
          <div class="col-md-2 pump-header">
            <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Pump Details</h1>
          </div>
          <div class="col-md-9">
            <div class="line-1 line-2"></div>
          </div>
        </div>
        <div class="row pb-2">
          <div class="col-md-6">
            <ul
              class="small-switch list-group list-group-horizontal justify-content-start mnb28"
              id="switch-control4"
            >
              <span class="fs12 fw600">Pump Required</span>
              <li class="fs12 list-group-item border-0 p-0 color-grey11">
                <ui-switch
                  switchColor="#fff"
                  defaultBoColor="#CECECE"
                  defaultBgColor="#CECECE"
                  formControlName="isPumpConfirmed"
                  class="ms-2"
                  formControlName="isPumpRequired"
                >
                </ui-switch>
              </li>
            </ul>
          </div>
        </div>

        <div
          class="row pt-4 align-items-end"
          *ngIf="concreteRequest.get('isPumpRequired').value === false ? false : true"
        >
          <div class="col-md-6">
            <div class="form-group customised-tag-input mb-0">
              <label class="fs12 fw600 m-0" for="pumpsize">Pump Size</label>
              <tag-input id="pumpsize"
                [onlyFromAutocomplete]="false"
                [placeholder]="' '"
                formControlName="pumpSize"
                secondaryPlaceholder=" "
                [(ngModel)]="pumpSizeList"
                (ngModelChange)="checkPumpSizeDuplication($event)"
                [onTextChangeDebounce]="500"
                class="tag-layout"
                [identifyBy]="'id'"
                [displayBy]="'pumpSize'"
              >
                <tag-input-dropdown
                  [showDropdownIfEmpty]="false"
                  [displayBy]="'pumpSize'"
                  [identifyBy]="'id'"
                  [autocompleteItems]="pumpSizeDropdown"
                  [appendToBody]="false"
                >
                  <ng-template let-item="item" let-index="index">
                    <div class="tag-input-sample fs12">
                      {{ item.pumpSize }}
                    </div>
                  </ng-template>
                </tag-input-dropdown>
              </tag-input>
              <p class="color-grey11 fs12 mb-0">*Type and press enter to create the pump size tag</p>
              <div class="color-red" *ngIf="submitted && concreteRequest.get('pumpSize').errors">
                <small>*Pump Size is Required</small>
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <label class="fs12 fw600 m-0 mb-2" for="pumporder">Pump Ordered</label>
            <div class="input-group mb-3">
              <input id="pumporder"
                class="form-control fs12 ps-0 fw500 material-input"
                #dp="bsDatepicker"
                bsDatepicker
                placeholder="Pump Ordered"
                [bsConfig]="{
                  isAnimated: true,
                  showWeekNumbers: false,
                  customTodayClass: 'today'
                }"
                formControlName="pumpOrderedDate"
              />
                <span class="input-group-text">
                  <img
                    src="./assets/images/date.svg"
                    class="h-12px"
                    alt="Date"
                    (click)="dp.toggle()"
                    (keydown)="dp.toggle()"
                    [attr.aria-expanded]="dp.isOpen"
                  />
                </span>
            </div>
            <div class="color-red" *ngIf="submitted && concreteRequest.get('pumpOrderedDate').errors">
              <small *ngIf="concreteRequest.get('pumpOrderedDate').errors.required"
                >*Pump Ordered is Required.</small
              >
            </div>
          </div>
        </div>

        <div class="row" *ngIf="concreteRequest.get('isPumpRequired').value === false ? false : true">
          <div class="col-md-6 mt-0">
            <div class="floating-concreted mt-3">
              <div class="form-group mb-0">
                <label class="fs12 fw600 m-0" for="pumplocation">Pump Location</label>
                <input id="pumplocation"
                  class="floating-input form-control fs12 px-0"
                  type="text"
                  placeholder=" "
                  formControlName="pumpLocation"
                />
              </div>
            </div>
            <div class="color-red" *ngIf="submitted && concreteRequest.get('pumpLocation').errors">
              <small *ngIf="concreteRequest.get('pumpLocation').errors.required"
                >*Pump Location is Required.</small
              >
            </div>
          </div>

          <div class="col-md-6">
            <div class="form-group">
              <div class="timezone-flex pt-0">
                <div class="input-group mb-0 delivery-time">
                  <label class="fs12 fw600"  for="pumpwork">Pump Show up Time</label>
                  <timepicker  id="pumpwork"
                    [formControlName]="'pumpWorkStart'"
                    (ngModelChange)="changeDate1($event)"
                    (keypress)="numberOnly($event)"
                    class="mt-2"
                  >
                  </timepicker>
                </div>
                <div
                  class="color-red"
                  *ngIf="submitted && concreteRequest.get('pumpWorkStart').errors"
                >
                  <small *ngIf="concreteRequest.get('pumpWorkStart').errors.required"
                    >*Pump Show up Time is Required</small
                  >
                </div>
                <div class="input-group mb-0 delivery-time">
                  <label class="fs12 fw600"  for="pumpcom">Pump Completion Time</label>
                  <timepicker  id="pumpcom"
                    [formControlName]="'pumpWorkEnd'"
                    class="mt-2"
                    (ngModelChange)="deliveryEndTimeChangeDetection()"
                    (keypress)="numberOnly($event)"
                  >
                  </timepicker>
                </div>
                <div class="color-red" *ngIf="submitted && concreteRequest.get('pumpWorkEnd').errors">
                  <small *ngIf="concreteRequest.get('pumpWorkEnd').errors.required"
                    >*Pump Completion Time is Required</small
                  >
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
          class="row pb-2 ms-0 mb-0"
          *ngIf="concreteRequest.get('isPumpRequired').value === false ? false : true"
        >
          <div class="col-md-6 ps-0">
            <ul
              class="small-switch list-group list-group-horizontal justify-content-start mnb28 mb-2"
              id="switch-control4"
            >
              <span class="fs12 my-auto">Pump Confirmed</span>
              <li class="fs12 list-group-item border-0 p-0 color-grey11">
                <ui-switch
                  switchColor="#fff"
                  defaultBoColor="#CECECE"
                  defaultBgColor="#CECECE"
                  formControlName="isPumpConfirmed"
                  (change)="changePumpConfirmed($event)"
                  class="ms-2"
                >
                </ui-switch>
              </li>
            </ul>
            <div *ngIf="concreteRequest.get('pumpConfirmedOn').value">
              <p class="fs12 fw700 text-black ng-star-inserted mb-0">
                Confirmed on
                {{ concreteRequest.get('pumpConfirmedOn').value | date : 'medium' }}
              </p>
            </div>
          </div>
        </div>

        <div class="row pb-2 ms-0 mb-0 mt-2">
          <div class="col-md-12 pl0 ps-0">
            <label class="fs12 fw600"  for="notes">Notes</label>
            <textarea  id="notes"
              class="form-control fs11 radius0 mt-1"
              rows="2"
              formControlName="notes"
            ></textarea>
          </div>
        </div>
        <div class="modal-footer my-1 border-0 justify-content-center add-calendar-footer">
          <div class="mt-0 mb15 text-center">
            <button
              class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3 px-2rem"
              type="button"
              (click)="close(cancelConfirmation)"
            >
              Cancel
            </button>
            <button
              class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem"
              (click)="onSubmit()"
              [disabled]="formSubmitted && concreteRequest.valid"
            >
              <em
                class="fa fa-spinner"
                aria-hidden="true"
                *ngIf="formSubmitted && concreteRequest.valid"
              ></em>
              Submit
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
  <div class="modal-body text-center" *ngIf="modalLoader">Loading...</div>

  <!--Confirmation Popup-->
  <div id="confirm-popup7">
    <ng-template #cancelConfirmation>
      <div class="modal-body">
        <div class="text-center my-4">
          <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
            Are you sure you want to cancel?
          </p>
          <button
            class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
            (click)="resetForm('no')"
          >
            No
          </button>
          <button
            class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
            (click)="resetForm('yes')"
          >
            Yes
          </button>
        </div>
      </div>
    </ng-template>
  </div>
