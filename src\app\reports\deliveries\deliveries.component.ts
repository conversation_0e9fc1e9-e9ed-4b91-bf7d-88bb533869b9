import moment from 'moment';
import { ToastrService } from 'ngx-toastr';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Component, OnInit, TemplateRef , ElementRef, HostListener} from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ReportsService } from '../../services/reports/reports.service';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { SchedulerFormComponent } from '../scheduler-form/scheduler-form.component';
import { SaveReportFormComponent } from '../save-report-form/save-report-form.component';

@Component({
  selector: 'app-deliveries',
  templateUrl: './deliveries.component.html',
  })
export class DeliveriesComponent implements OnInit {
  public currentPageNo = 1;

  public collection: any[] = [1, 2, 3, 4, 56, 7, 8, 9, 0, 1, 2, 2, 2, 2, 2, 2, 2];

  public perPage = 1;

  public filterCount = 0;

  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public modalRef2: BsModalRef;

  public modalRef3: BsModalRef;

  public modalRef4: BsModalRef;

  public scheduleFormModalRef: BsModalRef;

  public selectedRecurrence = 'Does Not Repeat';

  public isEquipmentDropdownOpen: boolean = false;
  public isStatusDropdownOpen: boolean = false;
  public isGateDropdownOpen: boolean = false;
  public isCompanyDropdownOpen: boolean = false;
  public isLocationDropdownOpen: boolean = false;
  public isResponsibleDropdownOpen: boolean = false;
  public isDefineDropdownOpen: boolean = false;

  public selectedEquipment: any[] = [];
  public selectedGate: any[] = [];
  public selectedCompany: any[] = [];
  public selectedLocation: any[] = [];
  public selectedResponsible: any[] = [];
  public selectedDefine: any[] = [];

  // Custom Placeholder Strings
  public equipmentPlaceholder: string = 'Select Equipment';
  public gatePlaceholder: string = 'Select Gate';
  public companyPlaceholder: string = 'Select Company';
  public locationPlaceholder: string = 'Select Location';
  public statusPlaceholder: string = 'Select Status';
  public responsiblePlaceholder: string = 'Select Responsible Person';
  public definePlaceholder: string = 'Select DFOW';


  public todayDate = new Date();

  public autocompleteItemsAsObjects = [{ email: '<EMAIL>' }, { email: '<EMAIL>' }];

  public deliveryList: any = [];

  public ProjectId: string | number;

  public pageSize = 25;

  public pageNo = 1;

  public loader = true;

  public filterForm: UntypedFormGroup;

  public search = '';

  public ParentCompanyId: any;

  public sortColumn = 'deliveryStart';

  public sort = 'ASC';

  public totalDeliveryRequestCount = 0;

  public defineListData: any = [];

  public gateList: any = [];

  public equipmentList: any = [];

  public companyList: any = [];

  public defineList: any = [];

  public wholeStatus = ['Approved', 'Declined', 'Delivered', 'Pending'];

  public statusList = [
    { name: 'Approved' },
    { name: 'Declined' },
    { name: 'Delivered' },
    { name: 'Pending' }
  ];

  public memberList: any = [];

  public locationList: any = [];

  public tableHeaders: any = {
    id: { isActive: true, key: 'id', title: 'Id' },
    description: { isActive: true, key: 'description', title: 'Description' },
    date: { isActive: true, key: 'date', title: 'Date & Time' },
    status: { isActive: true, key: 'status', title: 'Status' },
    approvedby: { isActive: true, key: 'approvedby', title: 'Approved By' },
    equipment: { isActive: false, key: 'equipment', title: 'Equipment' },
    dfow: { isActive: false, key: 'dfow', title: 'Definable Feature of Work' },
    gate: { isActive: false, key: 'gate', title: 'Gate' },
    company: { isActive: false, key: 'company', title: 'Responsible Company' },
    name: { isActive: false, key: 'name', title: 'Responsible Person' },
    location: { isActive: false, key: 'location', title: 'Location' },
  };

  public editedtable: any;

  public exportForm: UntypedFormGroup;

  public exportSubmitted = false;

  public formSubmitted = false;

  public reportType = 'PDF';

  public reportName = 'Delivery Report';

  public activeHeaders = [];

  public updatedMemberList: any = [];

  public exportType = ['EXCEL', 'CSV', 'PDF'];

  public scheduleData = {};

  public filterPayload: any;

  selectedLocations: any = [];

  selectedStatus: any = [];

  selectedFilters: any;

  searchLocation: string = '';

  filteredLocationList: any = [];

  public constructor(
    private readonly modalService: BsModalService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly reportsService: ReportsService,
    public projectService: ProjectService,
    public deliveryService: DeliveryService,
    private readonly toastr: ToastrService,
    private readonly eRef: ElementRef
  ) {
    this.projectService.projectParent.subscribe((response19): void => {
      if (response19 !== undefined && response19 !== null && response19 !== '') {
        this.ProjectId = response19.ProjectId;
        this.ParentCompanyId = response19.ParentCompanyId;
        this.getDeliveryReports();
        this.filterDetailsForm();
        this.exportDetailsForm();
        this.getMembers();
        this.filterSubmit();
      }
    });
    this.activeHeaders = Object.values(this.tableHeaders).filter(
      (element: { isActive: boolean }): boolean => element.isActive === true,
    );
  }

  @HostListener('document:click', ['$event'])
  public onDocumentClick(event: Event): void {
    const modalElement = document.getElementById('fiter-temp3'); // Your modal container ID

    // Check if the clicked element is inside the modal or a dropdown trigger
    if (
      modalElement?.contains(event.target as Node) ||
      (event.target as HTMLElement).closest('.custom-dropdown') ||
      (event.target as HTMLElement).closest('.dropdown-list')
    ) {
      return; // Do nothing if clicking inside the modal, dropdown trigger, or dropdown menu
    }

    // Click happened outside, close all dropdowns
    this.isEquipmentDropdownOpen = false;
    this.isStatusDropdownOpen = false;
    this.isGateDropdownOpen = false;
    this.isCompanyDropdownOpen = false;
    this.isLocationDropdownOpen = false;
    this.isResponsibleDropdownOpen = false;
    this.isDefineDropdownOpen = false;
  }



  public openModal(template: TemplateRef<any>): void {
    this.modalRef = this.modalService.show(template, {
      class: 'modal-export',
    });
  }

  public openModal1(template1: TemplateRef<any>): void {
    this.modalRef1 = this.modalService.show(template1, {
      class: 'modal-downloads',
    });
  }

  public openSchedulePopup(): void {
    this.scheduleFormModalRef = this.modalService.show(SchedulerFormComponent);
    this.scheduleFormModalRef.content.ProjectId = this.ProjectId;
    this.scheduleFormModalRef.content.ParentCompanyId = this.ParentCompanyId;
    this.scheduleFormModalRef.content.reportType = 'Delivery';
    this.scheduleFormModalRef.content.updatedMemberList = this.updatedMemberList;
    this.scheduleFormModalRef.content.exportType = this.exportType;
    this.scheduleFormModalRef.content.filterPayload = this.filterPayload;
    this.scheduleFormModalRef.content.selectedHeaders = Object.values(this.tableHeaders);
    this.scheduleFormModalRef.content.sort = this.sort;
    this.scheduleFormModalRef.content.sortByField = this.sortColumn;
  }

  public openModalSave(): void {
    this.filterPayload.selectedHeaders = Object.values(this.tableHeaders);
    const modalRef = this.modalService.show(SaveReportFormComponent, { class: 'modal-save' });
    modalRef.content.ProjectId = this.ProjectId;
    modalRef.content.reportType = 'Delivery';
    modalRef.content.updatedMemberList = this.updatedMemberList;
    modalRef.content.exportType = this.exportType;
    modalRef.content.filterPayload = this.filterPayload;
    modalRef.content.pageSize = this.pageSize;
    modalRef.content.pageNo = this.pageNo;
    modalRef.content.sortOrder = 'asc';
    modalRef.content.void = 0;
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.toggleDropdown(data);
    }
  }

  public toggleDropdown(type: string): void {
    const isCurrentlyOpen = this[`is${type.charAt(0).toUpperCase() + type.slice(1)}DropdownOpen`];
    this.isEquipmentDropdownOpen = false;
    this.isStatusDropdownOpen = false;
    this.isGateDropdownOpen = false;
    this.isCompanyDropdownOpen = false;
    this.isLocationDropdownOpen = false;
    this.isResponsibleDropdownOpen = false;
    this.isDefineDropdownOpen = false;
    this[`is${type.charAt(0).toUpperCase() + type.slice(1)}DropdownOpen`] = !isCurrentlyOpen;
  }

  public closeFilterPopup() {
    this.modalRef4?.hide()
    this.isEquipmentDropdownOpen = false;
    this.isStatusDropdownOpen = false;
    this.isGateDropdownOpen = false;
    this.isCompanyDropdownOpen = false;
    this.isLocationDropdownOpen = false;
    this.isResponsibleDropdownOpen = false;
    this.isDefineDropdownOpen = false;
  }

  public toggleSelection(item: any, type: string, event: Event): void {
    let selectedList: any[];
    let labelKey: string;
    let defaultPlaceholder: string;

    switch (type) {
      case 'equipment': selectedList = this.selectedEquipment; labelKey = 'equipmentName'; defaultPlaceholder = 'Select Equipment'; break;
      case 'gate': selectedList = this.selectedGate; labelKey = 'gateName'; defaultPlaceholder = 'Select Gate'; break;
      case 'company': selectedList = this.selectedCompany; labelKey = 'companyName'; defaultPlaceholder = 'Select Company'; break;
      case 'location': selectedList = this.selectedLocation; labelKey = 'locationPath'; defaultPlaceholder = 'Select Location'; break;
      case 'status': selectedList = this.selectedStatus; labelKey = 'name'; defaultPlaceholder = 'Select Status'; break;
      case 'define': selectedList = this.selectedDefine; labelKey = 'DFOW'; defaultPlaceholder = 'Select DFOW'; break;
      case 'responsible': selectedList = this.selectedResponsible; labelKey = 'User.email'; defaultPlaceholder = 'Select Responsible Person'; break;
      default: return;
    }

    if (type === 'location') {
      const getChildren = (parentId: number): any[] => this.locationList.filter((loc) => loc.LocationId === parentId)
        .flatMap((child) => [child, ...getChildren(child.id)]); // Recursively get all descendants

      if (item.isDefault) {
        // Select all locations if isDefault is true
        if (!selectedList.some((sel) => sel.id === item.id)) {
          selectedList = [...this.locationList]; // Select all locations
        } else {
          // Unselect all locations if isDefault is unselected
          selectedList = [];
        }
      } else {
        const allChildren = getChildren(item.id);
        const allChildIds = allChildren.map((child) => child.id);

        const isAlreadySelected = selectedList.some((sel) => sel.id === item.id);

        if (isAlreadySelected) {
          // Remove the selected item and all its children
          selectedList = selectedList.filter(sel => sel.id !== item.id && !allChildIds.includes(sel.id));
        } else {
          // Add the selected item and all its children if not already in the list
          selectedList.push(item);
          allChildren.forEach((child) => {
            if (!selectedList.some((sel) => sel.id === child.id)) {
              selectedList.push(child);
            }
          });
        }
      }

      this.selectedLocation = [...selectedList];
    } else {
      selectedList = this.toggleGenericSelection(item, type, selectedList);
    }

    this.updatePlaceholder(type, selectedList, labelKey, defaultPlaceholder);
    this.selectOption(item, type, event);
  }

  public toggleGenericSelection(item: any, type: string, selectedList: any[]): any[] {
    const index = selectedList.findIndex((sel) => (type === 'status' ? sel.name === item.name : sel.id === item.id));

    if (index === -1) {
      selectedList.push(item);
    } else {
      selectedList.splice(index, 1);
    }
    return selectedList;
  }

  public updatePlaceholder(type: string, selectedList: any[], labelKey: string, defaultPlaceholder: string): void {
    let newPlaceholder = defaultPlaceholder;
    if (selectedList.length === 1) {
      newPlaceholder = selectedList[0][labelKey];
      if(type === 'responsible'){
        newPlaceholder = selectedList[0].User.email;
      }
      else if(type === 'status'){
        newPlaceholder = selectedList[0].name;
      }
    } else if (selectedList.length > 1) {
      newPlaceholder = `${selectedList[0][labelKey]} + ${selectedList.length - 1} more`;
      if(type === 'responsible'){
        newPlaceholder =  `${selectedList[0].User.email} + ${selectedList.length - 1} more`;
      }else if(type === 'status'){
        newPlaceholder =  `${selectedList[0].name} + ${selectedList.length - 1} more`;
      }
    }
    switch (type) {
      case 'equipment': this.equipmentPlaceholder = newPlaceholder; break;
      case 'gate': this.gatePlaceholder = newPlaceholder; break;
      case 'company': this.companyPlaceholder = newPlaceholder; break;
      case 'location': this.locationPlaceholder = newPlaceholder; break;
      case 'responsible': this.responsiblePlaceholder = newPlaceholder; break;
      case 'define': this.definePlaceholder = newPlaceholder; break;
      case 'status': this.statusPlaceholder = newPlaceholder; break;
    }
  }

  public isSelected(item: any, type: string): boolean {
    let selectedList: any[];
    switch (type) {
      case 'equipment': selectedList = this.selectedEquipment; break;
      case 'gate': selectedList = this.selectedGate; break;
      case 'company': selectedList = this.selectedCompany; break;
      case 'location': selectedList = this.selectedLocation; break;
      case 'responsible': selectedList = this.selectedResponsible; break;
      case 'define': selectedList = this.selectedDefine; break;
      case 'status': selectedList = this.selectedStatus; break;
      default: return false;
    }

    return selectedList.some(sel =>
      type === 'status' ? sel.name === item.name :
      sel.id === item.id
    );
  }

  public selectOption(item: any, type: string, event: Event): void {
    if (event) {
      event.stopPropagation();
    }

    const formKey = type === 'responsible' ? 'memberFilter' : `${type}Filter`;
    const formControl = this.filterForm.get(formKey);
    if (!formControl) return;

    let selectedIds: number[] = formControl.value ?? [];

    if (!Array.isArray(selectedIds)) {
      selectedIds = [];
    }

    if (type === 'location') {
      selectedIds = this.handleLocationSelection(item, selectedIds);
    } else {
      selectedIds = this.handleGenericSelection(item, selectedIds, type);
    }

    formControl.setValue([...selectedIds]);
    console.log(this.filterForm.get(`${type}Filter`)?.value);
  }

  public handleLocationSelection(item: any, selectedIds: number[]): number[] {
    const getChildren = (parentId: number): any[] => this.locationList
      .filter((loc) => loc.LocationId === parentId)
      .flatMap((child) => [child, ...getChildren(child.id)]);

    if (item.isDefault) {
      return selectedIds.includes(item.id)
        ? []
        : this.locationList.map((loc) => loc.id);
    }

    const allChildren = getChildren(item.id);
    const allChildIds = allChildren.map((child) => child.id);
    const isAlreadySelected = selectedIds.includes(item.id);

    if (isAlreadySelected) {
      return selectedIds.filter((id) => id !== item.id && !allChildIds.includes(id));
    }

    const updated = new Set(selectedIds);
    updated.add(item.id);
    allChildIds.forEach((id) => updated.add(id));
    return Array.from(updated);
  }

  public handleGenericSelection(item: any, selectedIds: any[], type: string): any[] {
    const isNameType = type === 'status';
    const value = isNameType ? item.name : item.id;
    const index = selectedIds.indexOf(value);

    if (index === -1) {
      selectedIds.push(value);
    } else {
      selectedIds.splice(index, 1);
    }

    return selectedIds;
  }


  public openModal3(template3: TemplateRef<any>): void {
    this.modalRef3 = this.modalService.show(template3, {
      class: 'modal-sendto',
    });
  }

  public openModal4(template4: TemplateRef<any>): void {
    this.modalRef4 = this.modalService.show(template4, {
      class: ' report-filter-modal filter-popup report-filter custom-modal',
    });
  }

  public onRecurrenceSelect(value): void {
    this.selectedRecurrence = value;
  }

  public ngOnInit(): void { /* */ }

  public mapFilterArray(arr: any[]): any[] {
    return Array.isArray(arr) && arr.length ? arr.map((item) => item) : [];
  }

  public getDeliveryReports(): void {
    this.loader = true;
    this.deliveryList = [];
    const getDeliveryRequestParam = {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.pageNo,
      void: 0,
    };
    let getDeliveryRequestPayload: any = {};
    if (this.filterForm !== undefined) {
      const { value } = this.filterForm;

      getDeliveryRequestPayload = {
        companyFilter: this.mapFilterArray(value.companyFilter),
        descriptionFilter: this.filterForm.value.descriptionFilter,
        statusFilter: this.mapFilterArray(value.statusFilter),
        memberFilter: this.mapFilterArray(value.memberFilter),
        gateFilter: this.mapFilterArray(value.gateFilter),
        equipmentFilter: this.mapFilterArray(value.equipmentFilter),
        locationFilter: this.mapFilterArray(value.locationFilter),
        defineFilter: this.mapFilterArray(value.defineFilter),
        idFilter: value.idFilter ?? 0,
        startdate: this.filterForm.value.dateFilter
          ? moment(this.filterForm.value.dateFilter[0]).format('YYYY-MM-DD')
          : this.filterForm.value.dateFilter,
        enddate: this.filterForm.value.dateFilter
          ? moment(this.filterForm.value.dateFilter[1]).format('YYYY-MM-DD')
          : this.filterForm.value.dateFilter,
      };
    }
    getDeliveryRequestPayload.sort = this.sort;
    getDeliveryRequestPayload.sortByField = this.sortColumn;
    getDeliveryRequestPayload.ParentCompanyId = this.ParentCompanyId;
    getDeliveryRequestPayload.queuedNdr = false;
    this.filterPayload = getDeliveryRequestPayload;
    this.reportsService
      .deliveryReports(getDeliveryRequestParam, getDeliveryRequestPayload)
      .subscribe((response: any): void => {
        if (response) {
          this.formSubmitted = false;
          const responseData = response.data;
          this.loader = false;
          this.deliveryList = responseData.rows;
          this.totalDeliveryRequestCount = responseData.count;
          this.getOverAllGateForNdrGrid();
        }
      });
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      companyFilter: [''],
      descriptionFilter: [''],
      statusFilter: [''],
      dateFilter: [''],
      memberFilter: [''],
      gateFilter: [''],
      equipmentFilter: [''],
      defineFilter: [''],
      idFilter: [''],
      pickFrom: [''],
      pickTo: [''],
      locationFilter: [''],
    });
  }

  public exportDetailsForm(): void {
    this.exportForm = this.formBuilder.group({
      reportName: ['', Validators.required],
      reportType: ['', Validators.required],
    });
    this.exportForm.get('reportName').setValue(this.reportName);
    this.exportForm.get('reportType').setValue(this.reportType);
  }

  public filterSubmit(): void {
    this.formSubmitted = true;
    this.filterCount = 0;
    if (this.filterForm.get('descriptionFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('dateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('companyFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('memberFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('gateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('equipmentFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('statusFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('defineFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('idFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('locationFilter').value !== '') {
      this.filterCount += 1;
    }
    this.pageNo = 1;
    this.getDeliveryReports();
    if (this.modalRef4) {
      this.closeFilterPopup()
    }
  }

  public sortByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getDeliveryReports();
  }

  public changePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.getDeliveryReports();
  }

  public getOverAllGateForNdrGrid(): void {
    const getOverAllGateForNdrGridParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .gateList(getOverAllGateForNdrGridParams, { isFilter: true, showActivatedAlone: true })
      .subscribe((getOverAllGateForNdrGridResponse): void => {
        this.gateList = getOverAllGateForNdrGridResponse.data;
        this.getOverAllEquipmentForNdrGrid();
      });
  }

  public getOverAllEquipmentForNdrGrid(): void {
    const getOverAllEquipmentForNdrGridParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listEquipment(getOverAllEquipmentForNdrGridParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((getOverAllEquipmentForNdrGridResponse): void => {
        this.equipmentList = getOverAllEquipmentForNdrGridResponse.data;
        this.getDefinableForNdrGrid();
      });
  }

  public getDefinableForNdrGrid(): void {
    const getDefinableForNdrGridParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getDefinableWork(getDefinableForNdrGridParams)
      .subscribe((getDefinableForNdrGridResponse: any): void => {
        if (getDefinableForNdrGridResponse) {
          const { data } = getDefinableForNdrGridResponse;
          this.defineList = data;
          this.getCompaniesForNdrGrid();
        }
      });
  }

  public getCompaniesForNdrGrid(): void {
    const getCompaniesForNdrGridParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getCompanies(getCompaniesForNdrGridParams)
      .subscribe((getCompaniesForNdrGridResponse: any): void => {
        if (getCompaniesForNdrGridResponse) {
          this.companyList = getCompaniesForNdrGridResponse.data;
          this.getLocationsForNdrGrid();
        }
      });
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
        this.updatedMemberList = this.memberList.map((item) => ({
          ...item,
          UserEmail: item.User.email,
        }));
      }
    });
  }

  public resetFilter(): void {
    if (this.filterForm.invalid) {
      this.formSubmitted = true;
    }
    this.filterCount = 0;
    this.filterForm.reset();
    this.pageNo = 1;
    this.gatePlaceholder = 'Select Gate';
    this.companyPlaceholder = 'Select Company';
    this.equipmentPlaceholder = 'Select Equipment';
    this.locationPlaceholder = 'Select Location';
    this.responsiblePlaceholder = 'Select Responsible Person';
    this.statusPlaceholder = 'Select Status';
    this.definePlaceholder = 'Select DFOW';
    this.filterDetailsForm();
    this.getDeliveryReports();
    this.selectedEquipment = [];
    this.selectedGate = [];
    this.selectedCompany = [];
    this.selectedLocation = [];
    this.selectedResponsible = [];
    this.selectedDefine = [];
    this.selectedStatus = [];
  }

  public changePageNo(pageNo: number): void {
    this.pageNo = pageNo;
    this.getDeliveryReports();
  }

  public handleInputChange(checkedvalue: string): void {
    const checkbox = document.getElementById(checkedvalue) as HTMLInputElement | null;
    if (checkbox.checked === true) {
      this.tableHeaders[checkedvalue].isActive = true;
    } else if (this.activeHeaders.length > 1) {
      this.tableHeaders[checkedvalue].isActive = false;
    }
    this.activeHeaders = Object.values(this.tableHeaders).filter(
      (element: { isActive: boolean }): boolean => element.isActive === true,
    );
  }

  public selectall(checkedvalue): void {
    const checkbox1 = document.getElementById(checkedvalue) as HTMLInputElement | null;
    if (checkbox1.checked === true) {
      this.activeHeaders = Object.values(this.tableHeaders).filter(
        (element: { isActive: boolean }): boolean => element.isActive === true,
      );
      const secondKey = Object.keys(this.tableHeaders);
      secondKey.forEach((key): void => {
        const checkbox = document.getElementById(key) as HTMLInputElement | null;
        checkbox.checked = true;
        this.tableHeaders[key].isActive = true;
      });
    } else {
      const secondKey = Object.keys(this.tableHeaders);
      secondKey.forEach((key): void => {
        const checkbox = document.getElementById(key) as HTMLInputElement | null;
        if (key === 'id' || key === 'description' || key === 'date') {
          this.tableHeaders[key].isActive = true;
        } else {
          checkbox.checked = false;
          this.tableHeaders[key].isActive = false;
        }
      });
      this.activeHeaders = Object.values(this.tableHeaders).filter(
        (element: { isActive: boolean }): boolean => element.isActive === true,
      );
    }
  }

  public export(): void {
    this.exportSubmitted = true;
    this.formSubmitted = true;
    if (this.exportForm.invalid) {
      this.exportSubmitted = false;
      return;
    }
    const getDeliveryRequestParam = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      pageSize: this.pageSize,
      pageNo: this.pageNo,
      void: 0,
    };
    let getDeliveryRequestPayload: any = {};
    if (this.filterForm !== undefined) {
      const formValue = this.filterForm.value;
      const mapIfExists = (field: any[]) => (field?.length ? field.map((item) => item) : []);
      const getDate = (index: number) => (formValue.dateFilter ? moment(formValue.dateFilter[index]).format('YYYY-MM-DD') : formValue.dateFilter);

      getDeliveryRequestPayload = {
        companyFilter: mapIfExists(formValue.companyFilter),
        descriptionFilter: formValue.descriptionFilter,
        statusFilter: mapIfExists(formValue.statusFilter),
        memberFilter: mapIfExists(formValue.memberFilter),
        gateFilter: mapIfExists(formValue.gateFilter),
        equipmentFilter: mapIfExists(formValue.equipmentFilter),
        locationFilter: mapIfExists(formValue.locationFilter),
        defineFilter: mapIfExists(formValue.defineFilter),
        idFilter: formValue.idFilter ?? 0,
        startdate: getDate(0),
        enddate: getDate(1),
      };
    }
    if (this.sort === 'DESC' && this.sortColumn === ' id') {
      this.sort = 'ASC';
      this.sortColumn = 'deliveryStart';
    }
    getDeliveryRequestPayload.sort = this.sort;
    getDeliveryRequestPayload.sortByField = this.sortColumn;
    getDeliveryRequestPayload.ParentCompanyId = this.ParentCompanyId;
    getDeliveryRequestPayload.queuedNdr = false;
    getDeliveryRequestPayload.exportType = this.exportForm.get('reportType').value;
    getDeliveryRequestPayload.reportName = this.exportForm.get('reportName').value;
    getDeliveryRequestPayload.generatedDate = moment(new Date()).format('ddd, MMM DD YYYY');
    getDeliveryRequestPayload.selectedHeaders = Object.values(this.tableHeaders);
    this.filterPayload = getDeliveryRequestPayload;
    if (this.reportType === 'PDF' || this.reportType === 'CSV') {
      this.reportsService
        .exportDeliveryRequest(getDeliveryRequestParam, getDeliveryRequestPayload)
        .subscribe((response: any): void => {
          if (response) {
            const link = document.createElement('a');
            link.setAttribute('target', '_self');
            link.setAttribute('href', response.data);
            document.body.appendChild(link);
            link.click();
            link.remove();
            this.toastr.success('Delivery Booking exported successfully');
            this.cancelexport();
          }
        });
    } else if (this.reportType === 'EXCEL') {
      this.reportsService
        .exportDeliveryRequestInExcelFormat(getDeliveryRequestParam, getDeliveryRequestPayload)
        .subscribe((response: any): void => {
          if (response) {
            this.deliveryService.saveAsExcelFile(response, this.exportForm.get('reportName').value);
            this.toastr.success('Delivery Booking exported successfully');
            this.cancelexport();
          }
        });
    }
  }

  public cancelexport(): void {
    this.modalRef.hide();
    this.exportSubmitted = false;
    this.exportForm.get('reportName').setValue('Delivery Report');
    this.exportForm.get('reportType').setValue('PDF');
  }

  public getLocationsForNdrGrid(): void {
    const getLocationsForNdrGridParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getLocations(getLocationsForNdrGridParams)
      .subscribe((getLocationsForNdrGridResponse: any): void => {
        if (getLocationsForNdrGridResponse) {
          this.locationList = getLocationsForNdrGridResponse.data;
          this.filteredLocationList = this.locationList
        }
      });
  }



  public changeLocationFilterOptionList(event){
    if(event.target.value === '') {
      this.filteredLocationList = this.locationList
    }
    this.filteredLocationList = this.filteredLocationList.filter(option =>
      option.locationPath.toLowerCase().includes(event.target.value.toLowerCase())
    );
  }
}
