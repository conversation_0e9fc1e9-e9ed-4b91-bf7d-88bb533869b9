import {
  AfterViewInit, Component, On<PERSON><PERSON>roy, OnInit,
} from '@angular/core';
import { filter, debounceTime } from 'rxjs/operators';
import { merge, Subscription } from 'rxjs';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';

@Component({
  selector: 'app-details',
  templateUrl: './details.component.html',
})
export class DetailsComponent implements AfterViewInit, OnInit, OnDestroy {
  public NDRData: any = [];

  public DeliveryRequestId: any;

  public loader = true;

  public ParentCompanyId: any;

  public toolTipContent = '';

  private readonly subscriptions = new Subscription();

  public constructor(private readonly deliveryService: DeliveryService,
    public projectService: ProjectService) {
    this.projectService.projectParent.subscribe((response3): void => {
      if (response3 !== undefined && response3 !== null && response3 !== '') {
        this.ParentCompanyId = response3.ParentCompanyId;
      }
    });
  }

  public ngOnInit(): void {
    const deliveryIdSub = this.deliveryService.DeliveryRequestId.pipe(
      filter((id) => id !== null && id !== undefined && id !== ''),
    ).subscribe((id) => {
      this.DeliveryRequestId = id;

      const refreshSub = merge(
        this.deliveryService.refresh,
        this.deliveryService.refresh1,
      ).pipe(
        debounceTime(200),
        filter(() => !!this.DeliveryRequestId),
      ).subscribe(() => {
        this.loader = true;
        this.getNDR();
      });

      // Add the inner subscription
      this.subscriptions.add(refreshSub);
    });

    // Add the outer subscription
    this.subscriptions.add(deliveryIdSub);
  }

  public ngAfterViewInit(): void {
    /* */
  }

  public getResponsiblePeople(object: { firstName: any; lastName: any }): any {
    if (object?.firstName && object?.lastName) {
      const string = `${object.firstName} ${object.lastName}`;
      const matches = string.match(/\b(\w)/g);
      const acronym = matches.join('').toUpperCase();
      return acronym;
    }
    return 'UU';
  }

  public getNDR(): void {
    this.toolTipContent = '';
    const param = {
      DeliveryRequestId: this.DeliveryRequestId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.getNDRData(param).subscribe((res): void => {
      this.NDRData = res.data;
      if(this.NDRData.equipmentDetails.length && !this.NDRData.equipmentDetails[0].Equipment){
        this.NDRData.equipmentDetails[0].Equipment = { id: 0, equipmentName: 'No Equipment Needed' }
      }
      this.loader = false;
      if (this.NDRData.memberDetails.length > 3) {
        const slicedArray = this.NDRData?.memberDetails?.slice(3) || [];
        slicedArray.map((a): any => {
          if (a.Member.User.firstName) {
            this.toolTipContent += `${a.Member.User.firstName} ${a.Member.User.lastName}, `;
          } else {
            this.toolTipContent += `${a.Member.User.email}, `;
          }
        });
      }
    });
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }
}
