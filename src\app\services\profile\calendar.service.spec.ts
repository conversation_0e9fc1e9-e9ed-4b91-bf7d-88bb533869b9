import { TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CalendarService } from './calendar.service';
import { ApiService } from '../api_base/api.service';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';

describe('CalendarService', (): void => {
  let service: CalendarService;
  let apiServiceMock: jest.Mocked<ApiService>;
  let routerMock: jest.Mocked<Router>;

  beforeEach((): void => {
    // Create mocks
    apiServiceMock = {
      postMethod: jest.fn(),
      putMethod: jest.fn(),
      get: jest.fn(),
      post: jest.fn()
    } as unknown as jest.Mocked<ApiService>;

    routerMock = {
      navigate: jest.fn()
    } as unknown as jest.Mocked<Router>;

    TestBed.configureTestingModule({
      imports: [
        RouterTestingModule.withRoutes([]),
        HttpClientTestingModule,
      ],
      providers: [
        CalendarService,
        { provide: ApiService, useValue: apiServiceMock },
        { provide: Router, useValue: routerMock }
      ]
    });

    service = TestBed.inject(CalendarService);
  });

  it('should be created', (): void => {
    expect(service).toBeTruthy();
  });

  describe('updateCalendarEvents', (): void => {
    it('should update the BehaviorSubject with new data', (): void => {
      // Arrange
      const testData = true;
      let result: any;

      // Subscribe to the BehaviorSubject
      service.getCalendarEventData.subscribe(data => {
        result = data;
      });

      // Act
      service.updateCalendarEvents(testData);

      // Assert
      expect(result).toBe(testData);
    });
  });

  describe('addCalendarEvent', (): void => {
    it('should call postMethod with correct parameters', (): void => {
      // Arrange
      const mockParams = { ProjectId: '123' };
      const mockPayload = {
        description: 'Test Event',
        startDate: '2023-01-01',
        endDate: '2023-01-02'
      };
      const mockResponse = { success: true };

      apiServiceMock.postMethod.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.addCalendarEvent(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.postMethod).toHaveBeenCalledWith('calendar_settings/add_event', mockPayload, mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle error response', (): void => {
      // Arrange
      const mockParams = { ProjectId: '123' };
      const mockPayload = {
        description: 'Test Event',
        startDate: '2023-01-01',
        endDate: '2023-01-02',
      };
      const mockError = new Error('API Error');

      apiServiceMock.postMethod.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.addCalendarEvent(mockParams, mockPayload).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        },
      });
    });
  });

  describe('editCalendarEvent', (): void => {
    it('should call putMethod with correct parameters', (): void => {
      // Arrange
      const mockId = 123;
      const mockParams = { ProjectId: '123' };
      const mockPayload = {
        description: 'Updated Event',
        startDate: '2023-01-01',
        endDate: '2023-01-02'
      };
      const mockResponse = { success: true };

      apiServiceMock.putMethod.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.editCalendarEvent(mockId, mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.putMethod).toHaveBeenCalledWith(`calendar_settings/edit_event/${mockId}`, mockPayload, mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle error response', (): void => {
      // Arrange
      const mockId = 123;
      const mockParams = { ProjectId: '123' };
      const mockPayload = {
        description: 'Updated Event',
        startDate: '2023-01-01',
        endDate: '2023-01-02',
      };
      const mockError = new Error('API Error');

      apiServiceMock.putMethod.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.editCalendarEvent(mockId, mockParams, mockPayload).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        },
      });
    });
  });

  describe('getEventData', (): void => {
    it('should call get with correct parameters', (): void => {
      // Arrange
      const mockId = 123;
      const mockParams = { ProjectId: '123' };
      const mockResponse = {
        event: {
          id: 123,
          description: 'Test Event',
          startDate: '2023-01-01',
          endDate: '2023-01-02'
        }
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getEventData(mockId, mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`calendar_settings/get_event/${mockId}`, mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle error response', (): void => {
      // Arrange
      const mockId = 123;
      const mockParams = { ProjectId: '123' };
      const mockError = new Error('API Error');

      apiServiceMock.get.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.getEventData(mockId, mockParams).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        },
      });
    });
  });

  describe('getCalendarEvents', (): void => {
    it('should call get with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        startDate: '2023-01-01',
        endDate: '2023-01-31'
      };
      const mockResponse = {
        events: [
          {
            id: 123,
            description: 'Test Event',
            startDate: '2023-01-01',
            endDate: '2023-01-02'
          }
        ]
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getCalendarEvents(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith('calendar_settings/get_calendar_events', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should handle error response', (): void => {
      // Arrange
      const mockPayload = {
        startDate: '2023-01-01',
        endDate: '2023-01-31',
      };
      const mockError = new Error('API Error');

      apiServiceMock.get.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.getCalendarEvents(mockPayload).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        },
      });
    });
  });

  describe('getCalendarMonthEvents', (): void => {
    it('should call get with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        month: 1,
        year: 2023
      };
      const mockResponse = {
        events: [
          {
            id: 123,
            description: 'Test Event',
            startDate: '2023-01-01',
            endDate: '2023-01-02'
          }
        ]
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getCalendarMonthEvents(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith('calendar_settings/get_calendar_month_events', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should handle error response', (): void => {
      // Arrange
      const mockPayload = {
        month: 1,
        year: 2023,
      };
      const mockError = new Error('API Error');

      apiServiceMock.get.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.getCalendarMonthEvents(mockPayload).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        },
      });
    });
  });

  describe('getEventNDR', (): void => {
    it('should call post with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: '123',
        void: 'all'
      };
      const mockPayload = {
        startDate: '2023-01-01',
        endDate: '2023-01-31'
      };
      const mockResponse = {
        events: [
          {
            id: 123,
            description: 'Test Event',
            startDate: '2023-01-01',
            endDate: '2023-01-02'
          }
        ]
      };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getEventNDR(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(`calendar/event_NDR/${mockParams.ProjectId}/${mockParams.void}`, mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should handle error response', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: '123',
        void: 'all',
      };
      const mockPayload = {
        startDate: '2023-01-01',
        endDate: '2023-01-31',
      };
      const mockError = new Error('API Error');

      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.getEventNDR(mockParams, mockPayload).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        },
      });
    });
  });

  describe('getInspectionEventNDR', (): void => {
    it('should call post with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: '123',
        void: 'all'
      };
      const mockPayload = {
        startDate: '2023-01-01',
        endDate: '2023-01-31'
      };
      const mockResponse = {
        events: [
          {
            id: 123,
            description: 'Inspection Event',
            startDate: '2023-01-01',
            endDate: '2023-01-02'
          }
        ]
      };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getInspectionEventNDR(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(`calendar/get_inspection_request/${mockParams.ProjectId}/${mockParams.void}`, mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should handle error response', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: '123',
        void: 'all',
      };
      const mockPayload = {
        startDate: '2023-01-01',
        endDate: '2023-01-31',
      };
      const mockError = new Error('API Error');

      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.getInspectionEventNDR(mockParams, mockPayload).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        },
      });
    });
  });

  describe('getAllCalendarEventNDR', (): void => {
    it('should call post with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: '123',
        void: 'all'
      };
      const mockPayload = {
        startDate: '2023-01-01',
        endDate: '2023-01-31'
      };
      const mockResponse = {
        events: [
          {
            id: 123,
            description: 'All Calendar Event',
            startDate: '2023-01-01',
            endDate: '2023-01-02'
          }
        ]
      };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getAllCalendarEventNDR(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(`calendar/get_all_calendar/${mockParams.ProjectId}/${mockParams.void}`, mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should handle error response', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: '123',
        void: 'all',
      };
      const mockPayload = {
        startDate: '2023-01-01',
        endDate: '2023-01-31',
      };
      const mockError = new Error('API Error');

      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.getAllCalendarEventNDR(mockParams, mockPayload).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        },
      });
    });
  });

  describe('getDeliveryRequestWithCraneEquipmentType', (): void => {
    it('should call post with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: '123',
        void: 'all',
      };
      const mockPayload = {
        startDate: '2023-01-01',
        endDate: '2023-01-31',
      };
      const mockResponse = {
        events: [
          {
            id: 123,
            description: 'Crane Equipment Event',
            startDate: '2023-01-01',
            endDate: '2023-01-02',
          },
        ],
      };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getDeliveryRequestWithCraneEquipmentType(mockParams, mockPayload).subscribe((response) => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `calendar/get_crane_associated_request/${mockParams.ProjectId}/${mockParams.void}`,
        mockPayload,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle error response', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: '123',
        void: 'all',
      };
      const mockPayload = {
        startDate: '2023-01-01',
        endDate: '2023-01-31',
      };
      const mockError = new Error('API Error');

      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.getDeliveryRequestWithCraneEquipmentType(mockParams, mockPayload).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        },
      });
    });
  });

  describe('getConcreteRequest', (): void => {
    it('should call post with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: '123',
        void: 'all',
      };
      const mockPayload = {
        startDate: '2023-01-01',
        endDate: '2023-01-31',
      };
      const mockResponse = {
        events: [
          {
            id: 123,
            description: 'Concrete Request Event',
            startDate: '2023-01-01',
            endDate: '2023-01-02',
          },
        ],
      };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getConcreteRequest(mockParams, mockPayload).subscribe((response) => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `calendar/get_concrete_request/${mockParams.ProjectId}/${mockParams.void}`,
        mockPayload,
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle error response', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: '123',
        void: 'all',
      };
      const mockPayload = {
        startDate: '2023-01-01',
        endDate: '2023-01-31',
      };
      const mockError = new Error('API Error');

      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.getConcreteRequest(mockParams, mockPayload).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        },
      });
    });
  });
});
