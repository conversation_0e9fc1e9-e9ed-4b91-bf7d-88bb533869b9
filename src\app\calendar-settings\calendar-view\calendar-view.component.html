<ngx-loading [show]="loader" [config]="{ backdropBorderRadius: '3px' }"> </ngx-loading>
<section class="page-section pb-0 pt-2">
  <div class="page-inner-content position-relative" id="myCheck">
    <div class="top-header calendar-filter calendarsettings-header position-md-absolute">
      <div class="row text-end pt-md-40px">
        <div class="col-md-12">
          <div class="top-filter">
            <ul class="list-group list-group-horizontal justify-content-end">
              <li class="list-group-item p0 border-0 bg-transparent me-2">
                <div class="search-icon">
                  <input
                    class="form-control fs12 color-grey8"
                    [ngClass]="showSearchbar ? 'input-hover-disable' : 'input-search'"
                    placeholder="What are you looking for?"
                    (input)="getSearch($event.target.value)"
                    [(ngModel)]="search"
                  />
                  <div class="icon">
                    <img
                      src="./assets/images/cross-close.svg"
                      *ngIf="showSearchbar"
                      (click)="clear()" (keydown)="handleDownKeydown($event, '', '','clear')"
                      alt="close-cross"
                    />
                    <em class="fa fa-search fs12 color-grey8" *ngIf="!showSearchbar"></em>
                  </div>
                </div>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent">
                <button
                  type="button"
                  class="btn btn-orange-dark2 px-4 pt7 pb7 radius20 fs12"
                  (click)="openAddCalendarEventModal()"
                >
                  <em class="fa fa-plus me-1"></em> Add
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="row custom-full-calendar custom-full-calendarsetting">
      <div
        class="calendar-fulldetails"
        [ngClass]="currentView === 'Month' ? 'col-md-9' : 'col-md-12'"
      >
        <full-calendar
          [options]="calendarOptions"
          class="primary-full-calendar"
          id="calendar"
          #fullcalendar
        ></full-calendar>
        <h2 class="color-grey7 fs12 fw600 mt-3">Legend</h2>
        <div class="d-flex align-items-center mb-2 mb-md-0 flex-wrap flex-md-nowrap">
          <img
            src="./assets/images/noun-event-alert.svg"
            class="form-icon w10 h10 me-2"
            alt="allday"
          />
          <p class="mb-0 color-grey7 fs12 fw600">All day event</p>
        </div>
      </div>
      <div class="col-md-3 mt-5 calendar-eventdetails" *ngIf="currentView === 'Month'">
        <div
          class="fw-bold text-center mt-2"
          *ngIf="!monthlyEventloader && monthEvents?.length === 0"
        >
          No Events
        </div>
        <div class="fw-bold text-center mt-2" *ngIf="monthlyEventloader">Loading...</div>
        <div *ngIf="monthEvents?.length > 0">
          <div *ngFor="let eventData of monthEvents">
            <div class="p-3 mt-3 fs14 color-grey7 fw700">
              {{ eventData.day }}
            </div>
            <div *ngFor="let event of eventData.events" class="mt-1 d-flex">
              <div
                placement="left"
                [adaptivePosition]="false"
                [container]="'body'"
                [popover]="popTemplate"
                popoverTitle=""
                containerClass="popupcalendarsettings"
                popover-trigger="outsideClick"
                [outsideClick]="true"
                class="event-table bg-grayed-out-1 eye-cursor p-0"
                (click)="showPopover(event)" (keydown)="handleDownKeydown($event, event, event,'show')"
              >
                <div class="d-flex p-0">
                  <div class="w60 p-0 color-grayed-out fs9 px-1">
                    <p class="mb-0" *ngIf="!event?.isAllDay">
                      {{ event.startTime | date : 'shortTime' }}
                    </p>
                    <p class="mb-0" *ngIf="!event?.isAllDay">{{ event.durationInMinutes }} mins</p>
                    <p class="mb-0" *ngIf="event?.isAllDay">All Day</p>
                  </div>
                  <div class="w160 p-0 text-black fw-bold fs9 fw700 my-auto px-0">
                    {{ event.description }}
                  </div>
                </div>
              </div>
              <div class="text-end bg-grayed-out-1" (click)="openEditModal(event)" (keydown)="handleDownKeydown($event, event, '','edit')">
                <img src="../assets/images/edit-popup.svg" alt="edi" class="h-15 mb-2" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<div class="popup-calendarsettings">
  <ng-template #popTemplate>
    <div class="card border-0 sample">
      <div class="card-body p-0">
        <div class="d-flex">
          <p
            class="fs14 fw-bold cairo-regular color-grayed-out-1 px-0 pb-0 w-75 mb-0 ps-4"
          >
            {{ viewEventData?.description }}
          </p>
          <span class="ps-4 d-inline-flex justify-content-end float-end">
            <span
              class="list-group-item border-0 p-0 px-2 eye-cursor"
              (click)="openEditModal(viewEventData)" (keydown)="handleDownKeydown($event, viewEventData, '','edit')"
            >
              <img src="../assets/images/edit-details.svg" alt="edit" />
            </span>
            <span
              aria-hidden="true"
              class="eye-cursor" (keydown)="handleDownKeydown($event, viewEventData, deleteList,'delete')"
              (click)="openDeleteModal(viewEventData, deleteList)"
            >
              <img src="./assets/images/delete-popup.svg" alt="Cross" />
            </span>
          </span>
        </div>
        <div class="d-flex">
          <span><img src="../assets/images/settings-time.svg" alt="clock-time" /></span>
          <p class="color-grey14 fs12 px-2 my-auto cairo-regular fw-bold">
            {{ changeFormat(viewEventData?.fromDate) }}
            <span *ngIf="!viewEventData?.isAllDay">
              {{ viewEventData?.startTime | date : 'shortTime' }} -
              {{ viewEventData?.endTime | date : 'shortTime' }}
            </span>
            <span *ngIf="viewEventData?.isAllDay"> - All Day </span>
          </p>
        </div>
        <div class="d-flex flex-column">
          <p
            class="color-grey14 fs10 px-4 ms-0 delivery-content cairo-regular fw-bold mb-0"
          >
            {{ viewEventData?.timeZoneLocation }}
          </p>
          <p
            class="color-grey14 fs10 px-4 ms-0 delivery-content cairo-regular fw-bold mb-0"
          >
            {{ message }}
          </p>
        </div>
      </div>
    </div>
  </ng-template>
</div>
<div
  class="card calendarsetting-description p-2"
  [ngClass]="descriptionPopup == true ? 'delivery-show' : 'deliver-hide'"
>
  <span class="d-inline-flex">
    <p class="fs14 fw-bold cairo-regular color-blue1 w-75 mb-0 ps-4">
      {{ viewEventData?.description }}
    </p>
    <span class="d-inline-flex justify-content-end float-end">
      <span
        class="list-group-item border-0 p-0 px-1 eye-cursor"
        (click)="openEditModal(viewEventData)"  (keydown)="handleDownKeydown($event, viewEventData, '','edit')"
      >
        <img src="./assets/images/edit-details.svg" alt="Cross" />
      </span>
      <span
        aria-hidden="true"
        class="list-group-item border-0 p-0 px-1 eye-cursor"
        (click)="openDeleteModal(viewEventData, deleteList)"   (keydown)="handleDownKeydown($event, viewEventData, deleteList,'delete')"
      >
        <img src="./assets/images/delete-popup.svg" alt="Cross" />
      </span>
      <span aria-hidden="true" class="px-1 close eye-cursor" (click)="closeDescription()"  (keydown)="handleDownKeydown($event, '','','close')">
        <img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </span>
  </span>
  <div class="card-body p-0 delivery-details-content">
    <div class="d-flex justify-content-between">
      <p class="color-grey14 fs12 pt-2 mb-0">
        <span>
          <img src="../assets/images/settings-time.svg" alt="clock-time" />
        </span>
        <strong class="ps-2">
          {{ changeFormat(viewEventData?.fromDate) }}
          <span *ngIf="!viewEventData?.isAllDay">
            {{ viewEventData?.fromDate | date : 'shortTime' }} -
            {{ viewEventData?.toDate | date : 'shortTime' }}
          </span>
          <span *ngIf="viewEventData?.isAllDay"> - All Day </span>
        </strong>
      </p>
    </div>
    <p class="color-grey14 fs12 pl28 mb-0 pt-1">
      <strong class="fs10">
        {{ viewEventData?.timeZoneLocation }}
      </strong>
    </p>
    <p class="color-grey14 fs12 pl28 pt-1 mb-2">
      <strong class="fs10">
        {{ message }}
      </strong>
    </p>
  </div>
</div>

<ng-template #deleteList>
  <div class="modal-body">
    <div class="text-center my-4">
      <p class="color-grey15 fs14 fw-bold montserrat-semibold mb-5">
        Are you sure you want to delete ?
      </p>
      <button
        class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold montserrat-semibold px-5"
        (click)="resetAndClose()"
      >
        No
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold montserrat-semibold px-5"
        type="submit"
        (click)="deleteCalendarEvent()"
        [disabled]="deleteSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="deleteSubmitted"></em>Yes
      </button>
    </div>
  </div>
</ng-template>
