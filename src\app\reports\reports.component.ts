import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import {
  Component, OnInit, TemplateRef,
} from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { ProjectService } from '../services/profile/project.service';

@Component({
  selector: 'app-reports',
  templateUrl: './reports.component.html',
  })
export class ReportsComponent implements OnInit {
  public ProjectId: string | number;

  public ParentCompanyId: string | number;

  public filterFormValues: any = null;

  public currentPageNo = 1;

  public pageSize = 25;

  public filterCount = 0;

  public isTemplate: boolean = true;

  public isScheduled: boolean = false;

  public isSaved: boolean = false;

  public isRecent: boolean = false;

  public modalRef: BsModalRef;

  public showSearchbar = false;

  public search = '';

  public filterForm: UntypedFormGroup;

  public memberList = [];

  public templateTypeArray = ['Delivery', 'Crane', 'Concrete', 'Heat Map', 'Weekly Calendar'];

  public constructor(
    private readonly modalService: BsModalService,
    public formBuilder: UntypedFormBuilder,
    public projectService: ProjectService,
  ) {
    this.projectService.projectParent.subscribe((response19): void => {
      if (response19 !== undefined && response19 !== null && response19 !== '') {
        this.ProjectId = response19.ProjectId;
        this.ParentCompanyId = response19.ParentCompanyId;
        this.filterDetailsForm();
        this.getMembers();
      }
    });
  }

  public ngOnInit(): void { /* */ }

  public handleDownKeydown(event: KeyboardEvent, data: any, type: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'clear':
          this.clear();
          break;
        case 'modal':
          this.openModal(data);
          break;
        default:
          break;
      }
    }
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    if (this.ProjectId && this.ParentCompanyId) {
      this.projectService.listAllMember(param).subscribe((response: any): void => {
        if (response) {
          const memberList = response.data;
          this.memberList = memberList
            .map((element): any => {
              if (element.RoleId === 2) {
                const data = {
                  UserId: element.UserId,
                  author: `${element.User.firstName} ${element.User.lastName}`,
                  UserEmail: element.User.email,
                };
                return data;
              }
              return false;
            })
            .filter((element) => element !== false);
        }
      });
    }
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      createdUserId: [''],
      reportName: [''],
      templateType: [''],
      lastRun: [''],
      nextRun: [''],
    });
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('createdUserId').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('reportName').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('templateType').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('lastRun').value !== '') {
      this.filterCount += 1;
    }
    this.filterFormValues = this.filterForm.value;
    this.modalRef.hide();
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.search = '';
    this.filterDetailsForm();
    this.filterFormValues = null;
    this.modalRef.hide();
  }

  public getSearch(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.search = data;
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
  }

  public inTemplateClick(): void {
    this.isTemplate = true;
    this.isScheduled = false;
    this.isSaved = false;
    this.isRecent = false;
  }

  public inScheduledClick(): void {
    this.isTemplate = false;
    this.isScheduled = true;
    this.isSaved = false;
    this.isRecent = false;
  }

  public inSavedClick(): void {
    this.isTemplate = false;
    this.isScheduled = false;
    this.isSaved = true;
    this.isRecent = false;
  }

  public inRecentClick(): void {
    this.isTemplate = false;
    this.isScheduled = false;
    this.isSaved = false;
    this.isRecent = true;
  }

  public changePageNo(pageNo: number): void {
    this.currentPageNo = pageNo;
  }

  public openDeleteModal(template: TemplateRef<any>): void {
    this.modalRef = this.modalService.show(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'delete-modal custom-modal',
    });
  }

  public openModal(template: TemplateRef<any>): void {
    this.modalRef = this.modalService.show(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-sm filter-popup custom-modal',
    });
  }
}
