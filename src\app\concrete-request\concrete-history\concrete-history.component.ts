import { Component, OnDestroy, OnInit } from '@angular/core';
import {
  debounceTime, filter, merge, Subscription, tap,
} from 'rxjs';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';

@Component({
  selector: 'app-concrete-history',
  templateUrl: './concrete-history.component.html',
})
export class ConcreteHistoryComponent implements OnInit, OnDestroy {
  public historyList: any = [];

  public ConcreteRequestId: any;

  public loader = true;

  public ProjectId: any;

  public ParentCompanyId: any;

  public concreteRequest: any;

  private readonly subscription: Subscription = new Subscription();

  public constructor(
    private readonly deliveryService: DeliveryService,
    public projectService: ProjectService,
  ) {
    this.projectService.projectParent.subscribe((response5): void => {
      if (response5 !== undefined && response5 !== null && response5 !== '') {
        this.loader = true;
        this.ProjectId = response5.ProjectId;
        this.ParentCompanyId = response5.ParentCompanyId;
        this.loader = true;
      }
    });
  }

  public ngOnInit(): void {
    const historyTrigger$ = merge(
      this.deliveryService.EditConcreteRequestId.pipe(
        tap((id) => {
          this.ConcreteRequestId = id;
        }),
      ),
      this.deliveryService.fetchConcreteData,
      this.deliveryService.fetchConcreteData1,
    ).pipe(
      debounceTime(100),
      filter(() => !!this.ConcreteRequestId),
    );

    this.subscription.add(
      historyTrigger$.subscribe(() => {
        this.getHistory();
      }),
    );
  }

  public selectStatus(data: any): void {
    this.deliveryService.completeConcreteRequestStatus(data);
  }

  public getHistory(): void {
    this.loader = true;
    const param = {
      ConcreteRequestId: this.ConcreteRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    this.deliveryService.getConcreteRequestHistory(param).subscribe((res): void => {
      this.historyList = res.data;
      // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
      this.historyList = this.historyList.filter(
        (data: { type: string }) => data.type !== 'comment',
      );
      this.concreteRequest = res.concreteRequest;
      this.loader = false;
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
