import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { of } from 'rxjs';
import { FooterComponent } from './footer.component';
import { AuthService } from '../../services/auth/auth.service';
import { ProjectService } from '../../services/profile/project.service';
import { ProfileService } from '../../services/profile/profile.service';
import { DeliveryService } from '../../services/profile/delivery.service';

describe('FooterComponent', () => {
  let component: FooterComponent;
  let fixture: ComponentFixture<FooterComponent>;
  let authServiceMock: jest.Mocked<AuthService>;
  let profileServiceMock: jest.Mocked<ProfileService>;
  let projectServiceMock: jest.Mocked<ProjectService>;
  let deliveryServiceMock: jest.Mocked<DeliveryService>;
  let routerMock: jest.Mocked<Router>;

  beforeEach(async () => {
    authServiceMock = {
      logout: jest.fn(),
      getUser: jest.fn().mockReturnValue(of({ User: { firstName: 'John', lastName: 'Doe' } })),
    } as any;

    profileServiceMock = {
      overViewDetail: of({ User: { firstName: 'John', lastName: 'Doe', profilePic: 'pic.jpg' } }),
      getOverView: jest.fn().mockReturnValue(of({ data: { User: { firstName: 'John', lastName: 'Doe', profilePic: 'pic.jpg' } } })),
    } as any;

    projectServiceMock = {
      projectParent: of({ ProjectId: '123', ParentCompanyId: '456' }),
    } as any;

    deliveryServiceMock = {
      footerDropdownStatus: jest.fn(),
    } as any;

    routerMock = {} as any;

    await TestBed.configureTestingModule({
      declarations: [FooterComponent],
      providers: [
        { provide: AuthService, useValue: authServiceMock },
        { provide: ProfileService, useValue: profileServiceMock },
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: DeliveryService, useValue: deliveryServiceMock },
        { provide: Router, useValue: routerMock },
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FooterComponent);
    component = fixture.componentInstance;
    component.currentUser = {};
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    component.currentUser = {};
    expect(component.isCollapsed).toBe(true);
    expect(component.currentUser).toEqual({});
  });

  it('should call getAuthUser on init', () => {
    const getAuthUserSpy = jest.spyOn(component, 'getAuthUser');
    component.ngOnInit();
    expect(getAuthUserSpy).toHaveBeenCalled();
  });

  it('should handle logout correctly', () => {
    window.Intercom = jest.fn();
    component.logout();
    expect(authServiceMock.logout).toHaveBeenCalled();
    expect(window.Intercom).toHaveBeenCalledWith('shutdown');
  });

  it('should toggle settings collapse state', () => {
    expect(component.isCollapsed).toBe(true);
    component.changeSettingsCollapse();
    expect(component.isCollapsed).toBe(false);
    expect(deliveryServiceMock.footerDropdownStatus).toHaveBeenCalledWith(true);

    component.changeSettingsCollapse();
    expect(component.isCollapsed).toBe(true);
    expect(deliveryServiceMock.footerDropdownStatus).toHaveBeenCalledWith(false);
  });

  it('should handle keyboard events correctly', () => {
    const logoutSpy = jest.spyOn(component, 'logout');
    const changeSettingsSpy = jest.spyOn(component, 'changeSettingsCollapse');

    const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
    const spaceEvent = new KeyboardEvent('keydown', { key: ' ' });
    const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' });

    component.handleDownKeydown(enterEvent, 'logout');
    expect(logoutSpy).toHaveBeenCalled();

    component.handleDownKeydown(spaceEvent, 'change');
    expect(changeSettingsSpy).toHaveBeenCalled();

    component.handleDownKeydown(escapeEvent, 'change');
    expect(changeSettingsSpy).toHaveBeenCalledTimes(1);
  });

  it('should update user data from profile service subscription', () => {
    component.getOverView();
    fixture.detectChanges();

    expect(component.currentUser.firstName).toBe('John Doe');
    expect(component.currentUser.profilePic).toBe('pic.jpg');
  });

  it('should update user data from project service subscription', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.getOverView();

    expect(component.ProjectId).toBe('123');
    expect(component.ParentCompanyId).toBe('456');
  });

  it('should call getOverView when project data is received', () => {
    const getOverViewSpy = jest.spyOn(component, 'getOverView');

    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.getOverView();

    expect(getOverViewSpy).toHaveBeenCalled();
  });
});
