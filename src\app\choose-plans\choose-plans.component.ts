/* eslint-disable max-lines-per-function */
import {
  Component, OnInit, ViewChild, AfterViewInit, TemplateRef,
} from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Router, ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import { AuthService } from '../services/auth/auth.service';
import { ProjectService } from '../services/profile/project.service';
import { ProjectComponent } from '../project/project.component';
import { MixpanelService } from '../services/mixpanel.service';

@Component({
  selector: 'app-choose-plans',
  templateUrl: './choose-plans.component.html',
  })
export class ChoosePlansComponent implements OnInit, AfterViewInit {
  public planList = [];

  public billed = false;

  public submitted = false;

  public formSubmitted = false;

  public selectedIndex: any;

  public modalRef: BsModalRef;

  public selectedPlanId: number;

  public loggedIn = false;

  public currentUser: any = {};

  public urlData = '';

  public upgradePlans = false;

  public upgradeProjectId: number;

  public projectList: any = {};

  public loader = true;

  public customerId: any;

  public newProjectCretedWithProjectPlan: any;

  @ViewChild('thanks') public successPopup: TemplateRef<any>;

  @ViewChild('projectsuccess') public projectSuccessPopup: TemplateRef<any>;

  @ViewChild('refresh') public refreshModal: TemplateRef<any>;

  public constructor(
    private readonly router: Router,
    public socket: Socket,
    public route: ActivatedRoute,
    private readonly toastr: ToastrService,
    public bsModalRef: BsModalRef,
    private readonly authService: AuthService,
    private readonly projectService: ProjectService,
    private readonly mixpanelService: MixpanelService,
    private readonly modalService: BsModalService,
  ) {
    this.getloginData();
  }

  public getloginData() {
    this.loggedIn = this.authService.loggeduserIn();
    if (!this.loggedIn) {
      const companyDetails = localStorage.getItem('company');
      const basicDetails = localStorage.getItem('basic');
      const projectDetails = localStorage.getItem('project');
      const companyCondition = companyDetails === undefined || companyDetails === null;
      const basicCondition = basicDetails === undefined || basicDetails === null;
      const projectCondition = projectDetails === undefined || projectDetails === null;
      if (basicCondition) {
        this.toastr.error('Please Fill the Basic Details', 'OOPS');
        this.router.navigate(['/home']);
      } else if (companyCondition) {
        this.toastr.error('Please Fill the Company Details', 'OOPS');
        this.router.navigate(['/home']);
      } else if (projectCondition) {
        this.toastr.error('Please Fill the Project Details', 'OOPS');
        this.router.navigate(['/home']);
      }
      this.setPlans();
    }
  }

  public handleDownKeydown(event: KeyboardEvent, item: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.highLightPlan(item);
    }
  }

  public setPlans(): void {
    const planId = localStorage.getItem('planid');
    if (planId !== undefined && planId !== null) {
      if (localStorage.getItem('interval') === 'year') {
        this.getPlans('yearly');
        this.billed = true;
      } else {
        this.getPlans('monthly');
      }
      this.selectedPlanId = +planId;
    } else {
      this.getPlans('monthly');
    }
  }

  public ngAfterViewInit(): void {
    const urlValue = this.router.url.split('/');
    const data = urlValue[1] || '';
    this.urlData = data.split('?')[0]; // Remove query parameters from urlData
    if (this.router.url.includes('?')) {
      if (this.loggedIn) {
        if (data.split('?')[0] === 'upgradeplans') {
          this.setPlans();
          this.route.queryParams.subscribe((params): any => {
            this.customerId = params.customerId;
            this.upgradeNewPlans();
          });
        } else {
          this.setPlans();
          this.route.queryParams.subscribe((params): any => {
            this.customerId = params.customerId;
            this.createProject1();
          });
        }
      } else {
        this.route.queryParams.subscribe((params): any => {
          this.customerId = params.customerId;
          this.registerUser1();
        });
      }
    } else if (data !== 'upgradeplans') {
      this.checkUpgradePlan();
    } else if (this.loggedIn && data === 'upgradeplans') {
      this.upgradePlanData(urlValue);
    }
  }


  public checkUpgradePlan() {
    if (this.loggedIn) {
      this.getAuthUser();
      const newProjectDetails = localStorage.getItem('newproject');
      const firstCondition = newProjectDetails === undefined;
      const secondCondition = newProjectDetails === null;
      const newProjectCondition = firstCondition || secondCondition;
      if (newProjectCondition) {
        if (this.checkProject()) {
          this.toastr.error('Please Fill the project Details.', 'OOPS!');
          this.openModal();
        } else {
          this.router.navigate(['/dashboard']);
        }
      } else {
        this.bsModalRef.hide();
      }
      this.setPlans();
    }
  }

  public upgradePlanData(urlValue) {
    this.upgradePlans = true;
    const projectId = urlValue[2];
    this.upgradeProjectId = +projectId;
    const params = {
      ProjectId: projectId,
    };
    if (params.ProjectId) {
      this.projectService.getSingleProject(params).subscribe({
        next: (projectList: any): void => {
          if (projectList) {
            this.projectList = projectList.data;
            if (!this.projectList) {
              this.toastr.error('Invalid access', 'OOPS!');
              this.router.navigate(['/dashboard']);
            } else {
              this.selectedPlanId = +this.projectList.PlanId;
              if (this.projectList.stripePlan.stripePlanName === 'yearly') {
                this.getUpgradePlans('yearly');
                this.billed = true;
              } else {
                this.getUpgradePlans('monthly');
              }
            }
          }
        },
        error: (err): void => {
          this.toastr.error(err.message, 'OOPS!');
          this.router.navigate(['/home']);
        },
      });
    }
  }

  public checkProject(): boolean {
    const projectId = localStorage.getItem('ProjectId');
    if (projectId === undefined || projectId === null) {
      return false;
    }
    return true;
  }

  public getAuthUser(): void {
    this.authService.getUser().subscribe((response: any): void => {
      this.currentUser = response;
    });
  }

  public ngOnInit(): void {
    this.toastr.clear();
    this.projectService.newProjectCreatedWithProjectPlan.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.newProjectCretedWithProjectPlan = res;
        this.createProject1();
      }
    });
  }

  public createProject1(): void {
    this.authService.getUser().subscribe((response: any): void => {
      this.currentUser = response;
      const projectData = JSON.parse(localStorage.getItem('newproject'));
      const payload = {
        firstName: this.currentUser.firstName,
        phoneNumber: this.currentUser.phoneNumber,
        email: this.currentUser.email,
        projectLocation: projectData.projectLocation,
        projectLocationLatitude: projectData.projectLocationLatitude,
        projectLocationLongitude: projectData.projectLocationLongitude,
        projectName: projectData.projectName,
        PlanId: +localStorage.getItem('planid'),
        stripeCustomerId: this.customerId,
        ParentCompanyId: projectData.ParentCompanyId,
      };
      this.openModal1(this.refreshModal);
      this.projectService.createProject(payload).subscribe({
        next: (response1: any): void => {
          if (response1) {
            this.mixpanelService.addMixpanelEvents('New Project Created');
            localStorage.removeItem('newSubscriptionCreated');
            this.modalRef.hide();
            this.toastr.success(response1.message, 'Success');
            this.submitted = false;
            this.formSubmitted = false;
            localStorage.removeItem('newproject');
            localStorage.removeItem('interval');
            localStorage.removeItem('planid');
            this.openModal1(this.projectSuccessPopup);
            this.router.navigate(['/dashboard']);
          }
        },
        error: (err): void => {
          localStorage.removeItem('newSubscriptionCreated');
          this.throwError(err);
        },
      });
    });
  }

  public registerUser1(): void {
    this.openModal1(this.refreshModal);
    const data = {
      basicDetails: JSON.parse(localStorage.getItem('basic')),
      companyDetails: JSON.parse(localStorage.getItem('company')),
      projectDetails: JSON.parse(localStorage.getItem('project')),
      planData: { id: +localStorage.getItem('planid') },
      stripeCustomerId: this.customerId,
    };
    this.authService.register(data).subscribe({
      next: (response: any): void => {
        if (response) {
          this.mixpanelService.createUserProfile(data);
          this.toastr.success(response.message, 'Success');
          this.modalRef.hide();
          this.submitted = false;
          this.formSubmitted = false;
          localStorage.removeItem('basic');
          localStorage.removeItem('company');
          localStorage.removeItem('project');
          localStorage.removeItem('interval');
          localStorage.removeItem('planid');
          this.openModal1(this.successPopup);
          this.router.navigate(['/login']);
        }
      },
      error: (err): void => {
        this.throwError(err);
      },
    });
  }

  public throwError(paymentError): void {
    this.modalRef.hide();
    if (paymentError.message?.statusCode === 400) {
      this.showError(paymentError);
      this.formSubmitted = false;
    } else if (!paymentError.message) {
      this.toastr.error('Try again later.!', 'Something went wrong.');
    } else {
      this.toastr.error(paymentError.message, 'OOPS!');
      this.submitted = false;
      this.formSubmitted = false;
    }
  }

  public getPlans(interval): void {
    this.loader = true;
    this.authService.getPlans(interval).subscribe({
      next: (planlist: any): void => {
        if (planlist) {
          this.planList = planlist.response;
          this.loader = false;
        }
      },
      error: (err): void => {
        this.toastr.error(err.message, 'OOPS!');
        this.router.navigate(['/home']);
      },
    });
  }

  public getUpgradePlans(interval): void {
    this.loader = true;
    this.authService.getUpgradePlans(interval).subscribe({
      next: (upgradeplanlist: any): void => {
        if (upgradeplanlist) {
          this.planList = upgradeplanlist.response;
          this.loader = false;
        }
      },
      error: (err): void => {
        this.toastr.error(err.message, 'OOPS!');
        this.router.navigate(['/home']);
      },
    });
  }

  public upgradeNewPlans(): void {
    this.upgradePlans = true;
    const urlValue = this.router.url.split('?');
    const getProjectId = urlValue[0].split('/');
    const projectId = getProjectId[2];
    this.upgradeProjectId = +projectId;
    this.openModal1(this.refreshModal);
    const data = {
      PlanId: +localStorage.getItem('upgradePlanId'),
      ProjectId: this.upgradeProjectId,
      stripeCustomerId: this.customerId,
    };
    this.projectService.upgradeProjectPlans(data).subscribe({
      next: (response: any): void => {
        if (response) {
          this.modalRef.hide();
          this.toastr.success(response.message, 'Success');
          this.submitted = false;
          this.formSubmitted = false;
          localStorage.removeItem('upgradePlanId');
          localStorage.removeItem('upgradeInterval');
          this.openModal1(this.projectSuccessPopup);
          this.router.navigate(['/dashboard']);
        }
      },
      error: (upgradeNewPlansErr): void => {
        this.modalRef.hide();
        this.submitted = false;
        this.formSubmitted = false;
        if (upgradeNewPlansErr.message?.statusCode === 400) {
          this.showError(upgradeNewPlansErr);
          this.formSubmitted = false;
        } else if (!upgradeNewPlansErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(upgradeNewPlansErr.message, 'OOPS!');
        }
      },
    });
  }

  public checkout(planData): void {
    const payload = {
      planData,
      url: this.router.url,
    };
    this.projectService.checkout(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          window.open(response.data.url, '_blank');
        }
      },
      error: (getTimeZoneListErr): void => {
        if (getTimeZoneListErr.message?.statusCode === 400) {
          this.showError(getTimeZoneListErr);
        } else if (!getTimeZoneListErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(getTimeZoneListErr.message, 'OOPS!');
        }
      },
    });
  }

  public checkoutSession(planData): void {
    const basicDetails = JSON.parse(localStorage.getItem('basic'));
    const companyDetails = JSON.parse(localStorage.getItem('company'));
    const payload = {
      planData,
      url: this.router.url,
      email: basicDetails.email,
      name: `${companyDetails.fullName} ${companyDetails.lastName}`,
      phone: `${basicDetails.phoneCode} ${basicDetails.phoneNumber}`,
    };
    this.projectService.checkoutSession(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          window.open(response.data.url, '_self');
        }
      },
      error: (getTimeZoneListErr): void => {
        if (getTimeZoneListErr.message?.statusCode === 400) {
          this.showError(getTimeZoneListErr);
        } else if (!getTimeZoneListErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(getTimeZoneListErr.message, 'OOPS!');
        }
      },
    });
  }

  public registerUser(data): void {
    if (!this.loggedIn) {
      this.authService.register(data).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.createUserProfile(data);
            this.submitted = false;
            this.formSubmitted = false;
            localStorage.removeItem('basic');
            localStorage.removeItem('company');
            localStorage.removeItem('project');
            localStorage.removeItem('interval');
            localStorage.removeItem('planid');
            this.modalRef = this.modalService.show(this.successPopup, {
              class: 'modal-l model-height home-popup',
            });
            this.router.navigate(['/login']);
          }
        },
        error: (SelectedPlanError): void => {
          this.submitted = false;
          this.formSubmitted = false;
          if (SelectedPlanError.message?.statusCode === 400) {
            this.showError(SelectedPlanError);
          } else if (!SelectedPlanError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(SelectedPlanError.message, 'OOPS!');
          }
        },
      });
    }
  }

  public createNewProjectAfterLoggedIn(planData): void {
    const projectDataString = localStorage.getItem('newproject');
    if (!projectDataString) {
      return;
    }

    const projectData = JSON.parse(projectDataString);
    if (!projectData) {
      return;
    }

    const payload = {
      firstName: this.currentUser.firstName,
      phoneNumber: this.currentUser.phoneNumber,
      email: this.currentUser.email,
      projectLocation: projectData.projectLocation,
      projectLocationLatitude: projectData.projectLocationLatitude,
      projectLocationLongitude: projectData.projectLocationLongitude,
      projectName: projectData.projectName,
      PlanId: planData.id,
      existcard: false,
      ParentCompanyId: projectData.ParentCompanyId,
    };
    this.createProject(payload);
  }

  public SelectedPlan(index): void {
    // Handle edge cases
    if (!this.planList || this.planList.length === 0 || index < 0 || index >= this.planList.length) {
      return;
    }

    this.selectedIndex = index;
    this.submitted = true;
    this.formSubmitted = true;
    const planData = this.planList[index];

    if (!planData?.id) {
      return;
    }

    this.selectedPlanId = +planData.id;
    this.toastr.clear();

    if (planData.Plan?.planType?.toLowerCase() === 'project plan') {
      this.setProjectPlan(planData);
    } else if (planData.Plan?.planType?.toLowerCase() === 'enterprise plan') {
      this.toastr.success('Please Contact Sales team.', 'Success');
      this.submitted = false;
      this.formSubmitted = false;
    } else {
      const data = {
        basicDetails: JSON.parse(localStorage.getItem('basic')),
        companyDetails: JSON.parse(localStorage.getItem('company')),
        projectDetails: JSON.parse(localStorage.getItem('project')),
        planData: { id: planData.id },
      };
      if (!this.loggedIn) {
        this.registerUser(data);
      }
      if (!this.upgradePlans) {
        this.createNewProjectAfterLoggedIn(planData);
      }
    }
  }

  public setProjectPlan(planData): void {
    this.submitted = false;
    this.formSubmitted = false;
    if (!this.upgradePlans) {
      localStorage.setItem('planid', planData.id);
      if (this.billed) {
        localStorage.setItem('interval', 'year');
      } else {
        localStorage.setItem('interval', 'month');
      }
      if (this.loggedIn) {
        this.checkout(planData);
      } else {
        this.checkoutSession(planData);
      }
    } else {
      localStorage.setItem('upgradePlanId', planData.id);
      if (this.billed) {
        localStorage.setItem('upgradeInterval', 'year');
      } else {
        localStorage.setItem('upgradeInterval', 'month');
      }
      if (this.loggedIn) {
        this.checkout(planData);
      } else {
        this.checkoutSession(planData);
      }
    }
  }

  public upgradeToNewPlans(planData): void {
    const data = {
      PlanId: planData.id,
      existcard: false,
      ProjectId: this.upgradeProjectId,
    };
    this.projectService.upgradeProjectPlans(data).subscribe({
      next: (response: any): void => {
        if (response) {
          if (this.modalRef) {
            this.modalRef.hide();
          }
          this.toastr.success(response.message, 'Success');
          this.submitted = false;
          this.formSubmitted = false;
          localStorage.removeItem('upgradePlanId');
          localStorage.removeItem('upgradeInterval');
          this.modalRef = this.modalService.show(this.projectSuccessPopup, {
            class: 'modal-l model-height home-popup',
          });
          this.router.navigate(['/dashboard']);
        }
      },
      error: (upgradeNewPlansError): void => {
        if (this.modalRef) {
          this.modalRef.hide();
        }
        this.submitted = false;
        this.formSubmitted = false;
        if (upgradeNewPlansError.message?.statusCode === 400) {
          this.showError(upgradeNewPlansError);
          this.formSubmitted = false;
        } else if (!upgradeNewPlansError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(upgradeNewPlansError.message, 'OOPS!');
        }
      },
    });
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public createProject(payload): void {
    this.projectService.createProject(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');

          this.submitted = false;
          this.formSubmitted = false;
          localStorage.removeItem('newproject');
          localStorage.removeItem('interval');
          localStorage.removeItem('planid');
          this.modalRef = this.modalService.show(this.projectSuccessPopup, {
            class: 'modal-l model-height home-popup',
          });
          this.router.navigate(['/dashboard']);
        }
      },
      error: (createProjectError): void => {
        if (createProjectError.message?.statusCode === 400) {
          this.showError(createProjectError);
        } else if (!createProjectError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(createProjectError.message, 'OOPS!');
          this.submitted = false;
          this.formSubmitted = false;
        }
      },
    });
  }

  public checkChange(): void {
    if (this.urlData !== 'upgradeplans') {
      if (this.billed) {
        this.getPlans('yearly');
      } else {
        this.getPlans('monthly');
      }
    } else if (this.urlData === 'upgradeplans') {
      if (this.billed) {
        this.getUpgradePlans('yearly');
      } else {
        this.getUpgradePlans('monthly');
      }
    }
  }

  public highLightPlan(data): void {
    this.selectedPlanId = data.id;
  }

  public openModal1(template: TemplateRef<any>): void {
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-md thanks-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }

  public openModal(): void {
    this.bsModalRef = this.modalService.show(ProjectComponent, {
      backdrop: 'static',
      keyboard: false,
      class: 'custom-modal',
    });
    this.bsModalRef.content.closeBtnName = 'Close';
  }

  public redirect(data): void {
    if (this.loggedIn && !this.upgradePlans) {
      this.openModal();
    } else {
      this.router.navigate([`/${data}`]);
    }
  }
}
