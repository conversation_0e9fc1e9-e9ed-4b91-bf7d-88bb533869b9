import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { Title } from '@angular/platform-browser';
import { RouterTestingModule } from '@angular/router/testing';
import { NgxPaginationModule } from 'ngx-pagination';
import { ModalModule, BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { of, throwError } from 'rxjs';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { UtilitiesLogComponent } from './utilities.component';
import { AuthGuard } from '../../auth.guard';
import { ProjectService } from '../../services/profile/project.service';
import { AuthService } from '../../services/auth/auth.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { MixpanelService } from '../../services/mixpanel.service';

describe('UtilitiesLogComponent', () => {
  let component: UtilitiesLogComponent;
  let fixture: ComponentFixture<UtilitiesLogComponent>;
  let projectService: jest.Mocked<ProjectService>;
  let modalService: jest.Mocked<BsModalService>;
  let toastrService: jest.Mocked<ToastrService>;
  let authService: jest.Mocked<AuthService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let mixpanelService: jest.Mocked<MixpanelService>;
  let mockModalRef: BsModalRef;

  const mockProjectId = '123';
  const mockParentCompanyId = '456';

  const mockResponse = {
    data: {
      rows: [{ id: 1, bill_name: 'Test Bill' }],
      count: 1,
    },
  };

  beforeEach(async () => {
    mockModalRef = {
      hide: jest.fn(),
      setClass: jest.fn(),
    } as any;

    // Create mock services
    projectService = {
      projectId: of(mockProjectId),
      ParentCompanyId: of(mockParentCompanyId),
      listUtilitiesLog: jest.fn().mockReturnValue(of(mockResponse)),
      addUtilitiesLog: jest.fn().mockReturnValue(of({ message: 'Success' })),
      getRegisteredMember: jest.fn().mockReturnValue(of({ data: [] })),
      listEquipment: jest.fn().mockReturnValue(of({ data: { rows: [] }, lastId: 1 })),
      listCompany: jest.fn().mockReturnValue(of({ data: { rows: [] } })),
      getEquipmentMappedRequests: jest.fn(),
      editEquipment: jest.fn().mockReturnValue(of({ message: 'Equipment updated' })),
      deleteEquipment: jest.fn().mockReturnValue(of({ message: 'Equipment deleted' })),
      deactiveEquipment: jest.fn().mockReturnValue(of({ message: 'Equipment deactivated' })),
      utilitiesUpload: jest.fn().mockReturnValue(of({ data: { 'Utility Invoice': {} }, invoice_URL: 'test-url' })),
      getSingleProject: jest.fn().mockReturnValue(of({ data: { projectLocation: 'Test Location' } })),
    } as any;

    modalService = {
      show: jest.fn().mockReturnValue(mockModalRef),
    } as any;

    toastrService = {
      success: jest.fn(),
      error: jest.fn(),
    } as any;

    authService = {} as any;
    deliveryService = {
      loginUser: of({}),
    } as any;
    mixpanelService = {
      addMixpanelEvents: jest.fn(),
    } as any;

    await TestBed.configureTestingModule({
      declarations: [UtilitiesLogComponent],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        RouterTestingModule,
        HttpClientTestingModule,
        NgxPaginationModule,
        ModalModule.forRoot(),
        ToastrModule.forRoot(),
      ],
      providers: [
        { provide: ProjectService, useValue: projectService },
        { provide: BsModalService, useValue: modalService },
        { provide: ToastrService, useValue: toastrService },
        { provide: AuthService, useValue: authService },
        { provide: DeliveryService, useValue: deliveryService },
        { provide: MixpanelService, useValue: mixpanelService },
        { provide: Title, useValue: { setTitle: jest.fn() } },
        AuthGuard,
        BsModalRef,
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(UtilitiesLogComponent);
    component = fixture.componentInstance;
    component.ProjectId = mockProjectId;
    component.ParentCompanyId = mockParentCompanyId;
    component.modalRef = mockModalRef;
    fixture.detectChanges();
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.currentPageNo).toBe(1);
      expect(component.pageSize).toBe(25);
      expect(component.utilitiesLogList).toEqual([]);
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(component.deleteSubmitted).toBe(false);
      expect(component.selectAll).toBe(false);
      expect(component.filterCount).toBe(0);
      expect(component.search).toBe('');
      expect(component.sort).toBe('DESC');
      expect(component.sortColumn).toBe('id');
    });

    it('should initialize forms', () => {
      expect(component.utilitiesDetailsForm).toBeDefined();
      expect(component.equipmentEditForm).toBeDefined();
      expect(component.filterForm).toBeDefined();
    });

    it('should set title on initialization', () => {
      const titleService = TestBed.inject(Title);
      expect(titleService.setTitle).toHaveBeenCalledWith('Follo - Equipment');
    });

    it('should subscribe to loginUser', () => {
      expect(component.authUser).toBeDefined();
    });

    it('should subscribe to projectId and call getUtilities', () => {
      expect(component.ProjectId).toBe(mockProjectId);
      expect(projectService.listUtilitiesLog).toHaveBeenCalled();
    });

    it('should subscribe to ParentCompanyId and call getUtilities', () => {
      expect(component.ParentCompanyId).toBe(mockParentCompanyId);
      expect(projectService.listUtilitiesLog).toHaveBeenCalled();
    });
  });

  describe('Data Loading', () => {
    it('should fetch utilities data successfully', () => {
      component.getUtilities();

      expect(projectService.listUtilitiesLog).toHaveBeenCalled();
      expect(component.utilitiesLogList).toEqual(mockResponse.data.rows);
      expect(component.totalCount).toBe(mockResponse.data.count);
      expect(component.loader).toBe(false);
    });

    it('should handle error when fetching utilities', () => {
      projectService.listUtilitiesLog.mockReturnValueOnce(throwError(() => new Error('API Error')));

      component.getUtilities();

      expect(projectService.listUtilitiesLog).toHaveBeenCalled();
      expect(component.loader).toBe(true);
    });

    it('should not fetch utilities when ProjectId or ParentCompanyId is missing', () => {
      component.ProjectId = null;
      component.ParentCompanyId = null;
      projectService.listUtilitiesLog.mockClear();

      component.getUtilities();

      expect(projectService.listUtilitiesLog).not.toHaveBeenCalled();
    });

    it('should fetch members successfully', () => {
      const mockMembersResponse = { data: [{ id: 1, name: 'Test Member' }] };
      projectService.getRegisteredMember.mockReturnValue(of(mockMembersResponse));

      component.getMembers();

      expect(projectService.getRegisteredMember).toHaveBeenCalledWith({
        ProjectId: mockProjectId,
        pageSize: component.pageSize,
        pageNo: component.pageNo,
        ParentCompanyId: mockParentCompanyId,
      });
      expect(component.memberList).toEqual(mockMembersResponse.data);
      expect(component.modalLoader).toBe(false);
    });

    it('should fetch equipment list successfully', () => {
      const mockEquipmentResponse = { data: { rows: [{ id: 1, name: 'Test Equipment' }] }, lastId: 1 };
      projectService.listEquipment.mockReturnValue(of(mockEquipmentResponse));

      component.getEquipmentsList();

      expect(projectService.listEquipment).toHaveBeenCalled();
      expect(component.equipmentList).toEqual(mockEquipmentResponse.data.rows);
      expect(component.lastId).toBe(mockEquipmentResponse.lastId);
      expect(component.loader).toBe(false);
    });

    it('should fetch companies successfully', () => {
      const mockCompaniesResponse = { data: { rows: [{ id: 1, name: 'Test Company' }] } };
      projectService.listCompany.mockReturnValue(of(mockCompaniesResponse));

      component.getCompanies();

      expect(projectService.listCompany).toHaveBeenCalled();
      expect(component.companiesList).toEqual(mockCompaniesResponse.data.rows);
      expect(component.loader).toBe(false);
    });
  });

  describe('Keyboard Event Handlers', () => {
    it('should handle toggle keydown with Enter key', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'sortByField').mockImplementation();

      component.handleToggleKeydown(event, 'testField', 'ASC');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.sortByField).toHaveBeenCalledWith('testField', 'ASC');
    });

    it('should handle toggle keydown with Space key', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'sortByField').mockImplementation();

      component.handleToggleKeydown(event, 'testField', 'DESC');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.sortByField).toHaveBeenCalledWith('testField', 'DESC');
    });

    it('should handle down keydown with toggle action', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      const mockData = { isExpanded: false };
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'toggleChildVisibility').mockImplementation();

      component.handleDownKeydown(event, mockData, null, 'toggle');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.toggleChildVisibility).toHaveBeenCalledWith(mockData);
    });

    it('should handle down keydown with remove action', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'removeFile').mockImplementation();

      component.handleDownKeydown(event, 'data', 'item', 'remove');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.removeFile).toHaveBeenCalledWith('data', 'item');
    });

    it('should handle down keydown with clear action', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'clear').mockImplementation();

      component.handleDownKeydown(event, null, null, 'clear');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.clear).toHaveBeenCalled();
    });

    it('should handle down keydown with filter action', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      const template = {} as any;
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'openFilterModal').mockImplementation();

      component.handleDownKeydown(event, template, null, 'filter');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.openFilterModal).toHaveBeenCalledWith(template);
    });

    it('should not handle keydown for other keys', () => {
      const event = new KeyboardEvent('keydown', { key: 'a' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'sortByField').mockImplementation();

      component.handleToggleKeydown(event, 'testField', 'ASC');

      expect(event.preventDefault).not.toHaveBeenCalled();
      expect(component.sortByField).not.toHaveBeenCalled();
    });
  });

  describe('Modal Operations', () => {
    it('should open modal', () => {
      const template = {} as any;
      component.openModal(template);

      expect(modalService.show).toHaveBeenCalledWith(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal',
      });
      expect(projectService.getRegisteredMember).toHaveBeenCalled();
      expect(projectService.listEquipment).toHaveBeenCalled();
      expect(projectService.listCompany).toHaveBeenCalled();
    });

    it('should open filter modal', () => {
      const template = {} as any;
      component.openFilterModal(template);

      expect(modalService.show).toHaveBeenCalledWith(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-sm filter-popup custom-modal',
      });
      expect(projectService.getRegisteredMember).toHaveBeenCalled();
    });

    it('should open details modal', () => {
      const mockItem = { id: 1, name: 'Test Item' };
      component.openDetailsModal(mockItem);

      expect(modalService.show).toHaveBeenCalled();
    });

    it('should reset and close modal', () => {
      component.submitted = true;
      component.formSubmitted = true;
      component.editSubmitted = true;
      component.formEditSubmitted = true;

      component.resetAndClose();

      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should open delete modal for specific index', () => {
      const template = {} as any;
      component.utilitiesLogList = [{ id: 1 }, { id: 2 }];
      jest.spyOn(component, 'openModal').mockImplementation();

      component.openDeleteModal(0, template);

      expect(component.deleteIndex[0]).toBe(1);
      expect(component.currentDeleteId).toBe(0);
      expect(component.remove).toBe(false);
      expect(component.openModal).toHaveBeenCalledWith(template);
    });

    it('should open delete modal for select all', () => {
      const template = {} as any;
      jest.spyOn(component, 'openModal').mockImplementation();

      component.openDeleteModal(-1, template);

      expect(component.remove).toBe(true);
      expect(component.openModal).toHaveBeenCalledWith(template);
    });

    it('should open edit modal and populate form', () => {
      const template = {} as any;
      component.utilitiesLogList = [{
        id: 1,
        equipmentAutoId: 2,
        equipmentName: 'Test Equipment',
        PresetEquipmentType: { id: 3 },
        controlledBy: 'Test User'
      }];
      jest.spyOn(component, 'openModal').mockImplementation();

      component.openEditModal(0, template);

      expect(component.editIndex).toBe(0);
      expect(component.equipmentEditForm.get('id').value).toBe(1);
      expect(component.equipmentEditForm.get('equipmentAutoId').value).toBe(2);
      expect(component.equipmentEditForm.get('equipmentName').value).toBe('Test Equipment');
      expect(component.equipmentEditForm.get('EquipmentType').value).toBe(3);
      expect(component.equipmentEditForm.get('controlledBy').value).toBe('Test User');
      expect(component.openModal).toHaveBeenCalledWith(template);
    });
  });

  describe('Filter Operations', () => {
    it('should apply filters', () => {
      component.modalRef = mockModalRef;
      component.filterForm.patchValue({
        idFilter: '1',
        nameFilter: 'Test',
        companyNameFilter: 'Company',
        typeFilter: 'Type',
        memberFilter: '1',
      });

      component.filterSubmit();

      expect(component.filterCount).toBe(5);
      expect(component.pageNo).toBe(1);
      expect(projectService.listUtilitiesLog).toHaveBeenCalled();
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should apply filters with some empty fields', () => {
      component.modalRef = mockModalRef;
      component.filterForm.patchValue({
        idFilter: '1',
        nameFilter: '',
        companyNameFilter: 'Company',
        typeFilter: '',
        memberFilter: '1',
      });

      component.filterSubmit();

      expect(component.filterCount).toBe(3);
      expect(component.pageNo).toBe(1);
      expect(projectService.listUtilitiesLog).toHaveBeenCalled();
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should reset filters', () => {
      component.filterCount = 5;
      component.search = 'test';
      component.pageNo = 2;
      component.modalRef = mockModalRef;

      component.resetFilter();

      expect(component.filterCount).toBe(0);
      expect(component.search).toBe('');
      expect(component.pageNo).toBe(1);
      expect(mockModalRef.hide).toHaveBeenCalled();
    });
  });

  describe('Search and Sorting', () => {
    it('should sort by field', () => {
      jest.spyOn(component, 'getUtilities').mockImplementation();

      component.sortByField('name', 'ASC');

      expect(component.sortColumn).toBe('name');
      expect(component.sort).toBe('ASC');
      expect(component.getUtilities).toHaveBeenCalled();
    });

    it('should clear search and filters', () => {
      component.showSearchbar = true;
      component.search = 'test search';
      component.pageNo = 3;
      jest.spyOn(component, 'getUtilities').mockImplementation();

      component.clear();

      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
      expect(component.pageNo).toBe(1);
      expect(component.getUtilities).toHaveBeenCalled();
    });

    it('should handle search with data', () => {
      jest.spyOn(component, 'getUtilities').mockImplementation();

      component.getSearchEquipment('test search');

      expect(component.showSearchbar).toBe(true);
      expect(component.pageNo).toBe(1);
      expect(component.search).toBe('test search');
      expect(component.getUtilities).toHaveBeenCalled();
    });

    it('should handle search with empty data', () => {
      component.showSearchbar = true;
      jest.spyOn(component, 'getUtilities').mockImplementation();

      component.getSearchEquipment('');

      expect(component.showSearchbar).toBe(false);
      expect(component.pageNo).toBe(1);
      expect(component.search).toBe('');
      expect(component.getUtilities).toHaveBeenCalled();
    });
  });

  describe('Pagination', () => {
    it('should change page size', () => {
      jest.spyOn(component, 'getUtilities').mockImplementation();

      component.changePageSize(50);

      expect(component.pageSize).toBe(50);
      expect(component.getUtilities).toHaveBeenCalled();
    });

    it('should change page number', () => {
      jest.spyOn(component, 'getUtilities').mockImplementation();

      component.changePageNo(3);

      expect(component.currentPageNo).toBe(3);
      expect(component.getUtilities).toHaveBeenCalled();
    });
  });

  describe('Form Operations', () => {
    beforeEach(() => {
      component.utilitiesDetailsForm.patchValue({
        logMonth: 'January 2024',
        utilityType: 'Electricity',
        details: '100',
        utilityUnits: 'kWh',
        bill_url: 'test-url',
        bill_name: 'test-bill.pdf',
        bill_amount: '150',
        item_details: []
      });
    });

    it('should submit form successfully', () => {
      component.onSubmit();

      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(true);
      expect(projectService.addUtilitiesLog).toHaveBeenCalled();
    });

    it('should not submit invalid form', () => {
      component.utilitiesDetailsForm.patchValue({
        logMonth: '',
        details: '',
        bill_amount: ''
      });

      component.onSubmit();

      expect(component.submitted).toBe(true);
      expect(component.formSubmitted).toBe(false);
      expect(projectService.addUtilitiesLog).not.toHaveBeenCalled();
    });

    it('should handle form submission success', () => {
      jest.spyOn(component, 'getUtilities').mockImplementation();

      component.onSubmit();

      expect(toastrService.success).toHaveBeenCalledWith('Success', 'Success');
      expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Added Utilities');
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should handle form submission error', () => {
      const mockError = { message: 'Submission failed' };
      projectService.addUtilitiesLog.mockReturnValue(throwError(() => mockError));

      component.onSubmit();

      expect(toastrService.error).toHaveBeenCalledWith('Submission failed', 'OOPS!');
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
    });

    it('should handle form submission error with status code 400', () => {
      const mockError = {
        message: {
          statusCode: 400,
          details: [{ field: 'Validation error' }]
        }
      };
      projectService.addUtilitiesLog.mockReturnValue(throwError(() => mockError));
      jest.spyOn(component, 'showError').mockImplementation();

      component.onSubmit();

      expect(component.showError).toHaveBeenCalledWith(mockError);
      expect(component.formSubmitted).toBe(false);
    });
  });

  describe('Edit Form Operations', () => {
    beforeEach(() => {
      component.equipmentEditForm.patchValue({
        id: 1,
        equipmentName: 'Test Equipment',
        EquipmentType: 2,
        controlledBy: 'Test User'
      });
    });

    it('should submit edit form successfully', () => {
      component.onEditSubmit();

      expect(component.editSubmitted).toBe(false);
      expect(component.formEditSubmitted).toBe(true);
      expect(projectService.editEquipment).toHaveBeenCalled();
    });

    it('should not submit invalid edit form', () => {
      component.equipmentEditForm.patchValue({
        equipmentName: '',
        EquipmentType: '',
        controlledBy: ''
      });

      component.onEditSubmit();

      expect(component.editSubmitted).toBe(true);
      expect(component.formEditSubmitted).toBe(false);
      expect(projectService.editEquipment).not.toHaveBeenCalled();
    });

    it('should handle edit form submission success', () => {
      jest.spyOn(component, 'getUtilities').mockImplementation();

      component.onEditSubmit();

      expect(toastrService.success).toHaveBeenCalledWith('Equipment updated', 'Success');
      expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Edited  Equipment');
      expect(component.editSubmitted).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should handle edit form submission error', () => {
      const mockError = { message: 'Edit failed' };
      projectService.editEquipment.mockReturnValue(throwError(() => mockError));

      component.onEditSubmit();

      expect(toastrService.error).toHaveBeenCalledWith('Edit failed', 'OOPS!');
      expect(component.submitted).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
    });
  });

  describe('Delete Operations', () => {
    it('should delete equipment successfully', () => {
      component.deleteIndex = [1, 2];
      component.selectAll = false;
      jest.spyOn(component, 'getUtilities').mockImplementation();

      component.deleteEquipment();

      expect(component.deleteSubmitted).toBe(false);
      expect(projectService.deleteEquipment).toHaveBeenCalledWith({
        id: [1, 2],
        ProjectId: mockProjectId,
        isSelectAll: false,
        ParentCompanyId: mockParentCompanyId,
      });
      expect(toastrService.success).toHaveBeenCalledWith('Equipment deleted', 'Success');
      expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Deleted  Equipment');
      expect(component.getUtilities).toHaveBeenCalled();
      expect(component.deleteSubmitted).toBe(false);
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should handle delete equipment error', () => {
      const mockError = { message: 'Delete failed' };
      projectService.deleteEquipment.mockReturnValue(throwError(() => mockError));
      jest.spyOn(component, 'getUtilities').mockImplementation();

      component.deleteEquipment();

      expect(toastrService.error).toHaveBeenCalledWith('Delete failed', 'OOPS!');
      expect(component.deleteSubmitted).toBe(false);
      expect(component.getUtilities).toHaveBeenCalled();
    });

    it('should remove selected items when selectAll is true', () => {
      component.selectAll = true;
      jest.spyOn(component, 'deleteEquipment').mockImplementation();

      component.removeItem();

      expect(component.deleteSubmitted).toBe(true);
      expect(component.deleteEquipment).toHaveBeenCalled();
    });

    it('should remove selected items when selectAll is false', () => {
      component.selectAll = false;
      component.utilitiesLogList = [
        { id: 1, isChecked: true },
        { id: 2, isChecked: false },
        { id: 3, isChecked: true }
      ];
      jest.spyOn(component, 'deleteEquipment').mockImplementation();

      component.removeItem();

      expect(component.deleteIndex).toEqual([1, 3]);
      expect(component.deleteEquipment).toHaveBeenCalled();
    });
  });

  describe('Selection Operations', () => {
    it('should check selected row when selectAll is true', () => {
      component.selectAll = true;

      const result = component.checkSelectedRow();

      expect(result).toBe(false);
    });

    it('should check selected row when items are checked', () => {
      component.selectAll = false;
      component.utilitiesLogList = [
        { isChecked: true },
        { isChecked: false }
      ];

      const result = component.checkSelectedRow();

      expect(result).toBe(false);
    });

    it('should check selected row when no items are checked', () => {
      component.selectAll = false;
      component.utilitiesLogList = [
        { isChecked: false },
        { isChecked: false }
      ];

      const result = component.checkSelectedRow();

      expect(result).toBe(true);
    });

    it('should set selected item', () => {
      component.utilitiesLogList = [
        { isChecked: false },
        { isChecked: false }
      ];

      component.setSelectedItem(0);

      expect(component.utilitiesLogList[0].isChecked).toBe(true);
    });

    it('should select all utilities', () => {
      component.selectAll = false;
      component.utilitiesLogList = [
        { isChecked: false },
        { isChecked: false }
      ];

      component.selectAllEquipmentsData();

      expect(component.selectAll).toBe(true);
      expect(component.utilitiesLogList[0].isChecked).toBe(true);
      expect(component.utilitiesLogList[1].isChecked).toBe(true);
    });

    it('should deselect all utilities', () => {
      component.selectAll = true;
      component.utilitiesLogList = [
        { isChecked: true },
        { isChecked: true }
      ];

      component.selectAllEquipmentsData();

      expect(component.selectAll).toBe(false);
      expect(component.utilitiesLogList[0].isChecked).toBe(false);
      expect(component.utilitiesLogList[1].isChecked).toBe(false);
    });
  });

  describe('File Operations', () => {
    it('should toggle child visibility', () => {
      const utility = { isExpanded: false };

      component.toggleChildVisibility(utility);

      expect(utility.isExpanded).toBe(true);
    });

    it('should handle file over event', () => {
      const event = {};
      expect(() => component.fileOver(event)).not.toThrow();
    });

    it('should handle file leave event', () => {
      const event = {};
      expect(() => component.fileLeave(event)).not.toThrow();
    });

    it('should remove file from formData and fileData', () => {
      component.formData = new FormData();
      component.formData.append('test', 'value');
      component.fileData = [[{ relativePath: 'test.pdf' }]];

      component.removeFile(0, 0);

      expect(component.fileData[0]).toHaveLength(0);
    });

    it('should handle status change', () => {
      const event = { target: { checked: true } } as any;
      jest.spyOn(console, 'log').mockImplementation();

      component.onStatusChange(event);

      expect(console.log).toHaveBeenCalledWith('Switch status:', true);
    });
  });

  describe('Form Reset and Close Operations', () => {
    it('should reset form with action "no"', () => {
      component.modalRef1 = mockModalRef;

      component.resetForm('no');

      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should reset form with action "yes"', () => {
      component.modalRef1 = mockModalRef;
      component.submitted = true;
      component.formSubmitted = true;
      component.editSubmitted = true;
      component.formEditSubmitted = true;

      component.resetForm('yes');

      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should close modal with deactivateEquipment form', () => {
      jest.spyOn(component, 'getUtilities').mockImplementation();

      component.close({} as any, 'deactivateEquipment');

      expect(component.fileData).toEqual([]);
      expect(mockModalRef.hide).toHaveBeenCalled();
      expect(component.getUtilities).toHaveBeenCalled();
    });

    it('should close modal with dirty newequipment form', () => {
      const template = {} as any;
      component.utilitiesDetailsForm.markAsDirty();
      jest.spyOn(component, 'closeModalPopup').mockImplementation();

      component.close(template, 'newequipment');

      expect(component.fileData).toEqual([]);
      expect(component.closeModalPopup).toHaveBeenCalledWith(template);
    });

    it('should close modal with clean newequipment form', () => {
      const template = {} as any;
      jest.spyOn(component, 'resetForm').mockImplementation();

      component.close(template, 'newequipment');

      expect(component.fileData).toEqual([]);
      expect(component.resetForm).toHaveBeenCalledWith('yes');
    });

    it('should close modal popup', () => {
      const template = {} as any;

      component.closeModalPopup(template);

      expect(modalService.show).toHaveBeenCalledWith(template, {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      });
    });
  });

  describe('Utility Functions', () => {
    it('should validate alphanumeric input - valid characters', () => {
      const validEvents = [
        { which: 65, keyCode: 65 }, // A
        { which: 90, keyCode: 90 }, // Z
        { which: 97, keyCode: 97 }, // a
        { which: 122, keyCode: 122 }, // z
        { which: 48, keyCode: 48 }, // 0
        { which: 57, keyCode: 57 }, // 9
        { which: 8, keyCode: 8 }, // Backspace
        { which: 32, keyCode: 32 } // Space
      ];

      validEvents.forEach(event => {
        expect(component.alphaNumericForEquipments(event)).toBe(true);
      });
    });

    it('should validate alphanumeric input - invalid characters', () => {
      const invalidEvents = [
        { which: 33, keyCode: 33 }, // !
        { which: 64, keyCode: 64 }, // @
        { which: 35, keyCode: 35 } // #
      ];

      invalidEvents.forEach(event => {
        expect(component.alphaNumericForEquipments(event)).toBe(false);
      });
    });

    it('should show error correctly', () => {
      const mockError = {
        message: {
          details: [{ field: 'Error message' }]
        }
      };

      component.showError(mockError);

      expect(component.submitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(toastrService.error).toHaveBeenCalledWith('Error message');
    });

    it('should check string empty values', () => {
      const result = component.checkStringEmptyValues({ EquipmentName: 'Test' });
      expect(result).toBe(false);
    });

    it('should switch equipment correctly', () => {
      const mockData: any = {
        equipmentDetails: [{ Equipment: { id: 1 } }]
      };

      component.switchEquipment(mockData, 2);

      expect(mockData.changedEquipmentId).toBe(2);
    });

    it('should not switch equipment when same id', () => {
      const mockData: any = {
        equipmentDetails: [{ Equipment: { id: 1 } }]
      };

      component.switchEquipment(mockData, 1);

      expect(mockData.changedEquipmentId).toBeNull();
    });
  });

  describe('Equipment Operations', () => {
    it('should activate equipment successfully', () => {
      const mockData = { id: 1, equipmentName: 'Test Equipment' };
      jest.spyOn(component, 'getUtilities').mockImplementation();

      component.activateEquipment(mockData);

      expect(projectService.editEquipment).toHaveBeenCalledWith({
        id: 1,
        equipmentName: 'Test Equipment',
        ProjectId: mockProjectId,
        ParentCompanyId: mockParentCompanyId,
        isActive: true,
      });
      expect(toastrService.success).toHaveBeenCalledWith('Equipment updated', 'Success');
      expect(component.getUtilities).toHaveBeenCalled();
    });

    it('should handle activate equipment error', () => {
      const mockData = { id: 1, equipmentName: 'Test Equipment' };
      const mockError = { message: 'Activation failed' };
      projectService.editEquipment.mockReturnValue(throwError(() => mockError));

      component.activateEquipment(mockData);

      expect(toastrService.error).toHaveBeenCalledWith('Activation failed', 'OOPS!');
    });
  });

  describe('Deactivate Equipment', () => {
    beforeEach(() => {
      component.ProjectId = mockProjectId;
      component.ParentCompanyId = mockParentCompanyId;
      component.equipmentData = { id: 1 };
    });

    it('should deactivate equipment with changed equipment data', () => {
      component.mappedRequestList = [
        { changedEquipmentId: 2 },
        { changedEquipmentId: 3 }
      ];
      jest.spyOn(component, 'getUtilities').mockImplementation();

      component.deactiveEquipment();

      expect(component.deactivateEquipmentLoader).toBe(true);
      expect(projectService.deactiveEquipment).toHaveBeenCalledWith({
        id: 1,
        equipmentSwitchedRequests: [
          { changedEquipmentId: 2 },
          { changedEquipmentId: 3 }
        ],
        ProjectId: mockProjectId,
        ParentCompanyId: mockParentCompanyId,
      });
      expect(toastrService.success).toHaveBeenCalledWith('Equipment deactivated', 'Success');
    });

    it('should deactivate equipment with empty mapped request list', () => {
      component.mappedRequestList = [];
      jest.spyOn(component, 'getUtilities').mockImplementation();

      component.deactiveEquipment();

      expect(projectService.deactiveEquipment).toHaveBeenCalledWith({
        id: 1,
        ProjectId: mockProjectId,
        ParentCompanyId: mockParentCompanyId,
      });
      expect(toastrService.success).toHaveBeenCalledWith('Equipment deactivated', 'Success');
    });

    it('should handle deactivate equipment error', () => {
      component.mappedRequestList = [];
      const mockError = { message: 'Deactivation failed' };
      projectService.deactiveEquipment.mockReturnValue(throwError(() => mockError));

      component.deactiveEquipment();

      expect(toastrService.error).toHaveBeenCalledWith('Deactivation failed', 'OOPS!');
    });

    it('should not deactivate when conditions are not met', () => {
      component.mappedRequestList = [
        { changedEquipmentId: null },
        { changedEquipmentId: 2 }
      ];

      component.deactiveEquipment();

      expect(component.deactivateEquipmentLoader).toBe(false);
      expect(projectService.deactiveEquipment).not.toHaveBeenCalled();
    });
  });
});
