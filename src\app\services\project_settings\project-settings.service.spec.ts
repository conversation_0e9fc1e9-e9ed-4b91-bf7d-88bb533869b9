import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { ProjectSettingsService } from './project-settings.service';
import { ApiService } from '../api_base/api.service';
import { of, throwError } from 'rxjs';

describe('ProjectSettingsService', (): void => {
  let service: ProjectSettingsService;
  let apiServiceMock: jest.Mocked<ApiService>;

  beforeEach((): void => {
    // Create mock for ApiService
    apiServiceMock = {
      get: jest.fn(),
      put: jest.fn()
    } as unknown as jest.Mocked<ApiService>;

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        ProjectSettingsService,
        { provide: ApiService, useValue: apiServiceMock }
      ]
    });

    service = TestBed.inject(ProjectSettingsService);
  });

  it('should be created', (): void => {
    expect(service).toBeTruthy();
  });

  describe('getProjectSettings', (): void => {
    it('should call get with correct parameters', (): void => {
      // Arrange
      const mockParams = { ProjectId: '123', ParentCompanyId: '456' };
      const mockResponse = {
        data: {
          projectSettings: {
            deliveryWindowTime: 30,
            deliveryWindowTimeUnit: 'minutes',
            approvedBackgroundColor: '#2c3593',
            pendingBackgroundColor: '#ffc107',
            rejectedBackgroundColor: '#dc3545'
          }
        }
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getProjectSettings(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith('project_settings/get_projectDetails', mockParams);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('updateProjectSettings', (): void => {
    it('should call put with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        ProjectId: '123',
        deliveryWindowTime: 30,
        deliveryWindowTimeUnit: 'minutes',
        approvedBackgroundColor: '#2c3593',
        pendingBackgroundColor: '#ffc107',
        rejectedBackgroundColor: '#dc3545',
        selectedDeliveryLine1: 'Line 1',
        selectedDeliveryLine2: 'Line 2',
        selectedCraneLine1: 'Line 1',
        selectedCraneLine2: 'Line 2',
        selectedConcreteLine1: 'Line 1',
        selectedConcreteLine2: 'Line 2',
        selectedInspectionLine1: 'Line 1',
        selectedInspectionLine2: 'Line 2'
      };

      const mockResponse = {
        success: true,
        message: 'Project settings updated successfully'
      };

      apiServiceMock.put.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateProjectSettings(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.put).toHaveBeenCalledWith('project_settings', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getGuestProjectSettings', (): void => {
    it('should call get with correct parameters', (): void => {
      // Arrange
      const mockParams = { ProjectId: '123', ParentCompanyId: '456', guestUserId: '789' };
      const mockResponse = {
        data: {
          projectSettings: {
            deliveryWindowTime: 30,
            deliveryWindowTimeUnit: 'minutes',
            approvedBackgroundColor: '#2c3593',
            pendingBackgroundColor: '#ffc107',
            rejectedBackgroundColor: '#dc3545',
            allowGuestToViewDeliveryCalendar: true,
            allowGuestToViewCraneCalendar: true,
            allowGuestToViewConcreteCalendar: true
          }
        }
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getGuestProjectSettings(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith('project_settings/get_guest_projectDetails', mockParams);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('API service interaction', (): void => {
    it('should use the ApiService for all HTTP requests', (): void => {
      // Verify that the service is using the ApiService for HTTP requests
      expect(apiServiceMock.get).toBeDefined();
      expect(apiServiceMock.put).toBeDefined();
    });
  });

  describe('Error handling', (): void => {
    it('should propagate errors from the API service', (done): void => {
      // Arrange
      const mockParams = { ProjectId: '123', ParentCompanyId: '456' };
      const mockError = 'API Error';

      apiServiceMock.get.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.getProjectSettings(mockParams).subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(mockError);
          done();
        }
      });
    });
  });

  describe('Service dependencies', (): void => {
    it('should have ApiService injected', (): void => {
      // This test verifies that the ApiService is properly injected
      expect(TestBed.inject(ApiService)).toBeDefined();
    });
  });
});
