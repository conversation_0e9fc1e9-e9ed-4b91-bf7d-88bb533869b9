<ngx-loading [show]="loader" [config]="{ backdropBorderRadius: '3px' }"> </ngx-loading>
<section class="reportsgrid page-section">
  <div class="page-inner-content pb-0">
    <div class="top-header my-0">
      <div class="row mx-0 pt-md-40px">
        <div class="col-md-3">
          <ul class="list-group list-group-horizontal">
            <li class="list-group-item p0 border-0 bg-transparent">
              <button
                type="button"
                class="btn btn-orange px-2 py-1 radius20 fs12 fw-bold"
                routerLink="/reports"
              >
                <em class="fa fa-chevron-left me-1"></em> Back
              </button>
            </li>
            <li class="list-group-item p0 border-0 bg-transparent">
              <h1 class="fs14 fw-bold ps-2 pt7 mb-0">Heat Map</h1>
            </li>
          </ul>
        </div>

        <div class="col-md-9">
          <div class="top-filter">
            <ul class="list-group list-group-horizontal justify-content-end">
              <li class="list-group-item p0 border-0 bg-transparent me-2 me-md-0">
                <a
                  class="float-end topaction--styles position-relative ml20 mb-d-flex"
                  [ngClass]="{ 'btn-orange radius30': filterCount > 0 }"
                  (click)="openModal(template)"
                >
                  <img src="../../../assets/images/filterreport.svg" alt="view" class="mx-1" />

                  <span class="textalign fw700">Filter</span>
                  <span
                    class="bg-orange filter-count-report rounded-circle position-absolute text-white filter-count"
                    *ngIf="filterCount > 0"
                  >
                    {{ filterCount }}
                  </span>
                </a>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent">
                <button
                  type="button"
                  class="btn btn-transparent-gray px-2 py-1 radius20 fs12 ml20"
                  (click)="openSchedulePopup()"
                >
                  <img
                    src="../../../assets/images/schedule_report.svg"
                    alt="schedule"
                    class="mx-1"
                  />
                  <span class="color-grey7 fw700">Schedule</span>
                </button>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent">
                <button
                  type="button"
                  class="btn btn-transparent-gray px-2 py-1 radius20 fs12 ml20"
                  (click)="openModalExport(templateexport)"
                >
                  <img src="../../../assets/images/download_report.svg" alt="export" class="mx-1" />
                  <span class="color-grey7 fw700">Export</span>
                </button>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent">
                <button
                  type="button"
                  (click)="openModalSave()"
                  class="btn btn-transparent-gray px-2 py-1 radius20 fs12 ml20"
                >
                  <img src="../../../assets/images/save_report.svg" alt="save" class="mx-1" />
                  <span class="color-grey7 fw700">Save</span>
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="report-page-content table-responsive p-3 mt-3">
      <table
        aria-describedby="Memtable"
        style="
          background: #fff;
          width: 100%;
          margin: 0 auto;
          border-collapse: collapse;
          text-align: left;
        "
      >
        <thead style="background-color: #f5f5f5; color: #000; font-size: 12px">
          <tr>
            <th style="padding: 15px; color: #f45e28; min-width: 100px" (click)="sortData()" (keydown)="handleToggleKeydown($event)">
              Date/Time
              <span *ngIf="sortOrder === 'asc'">▲</span>
              <span *ngIf="sortOrder === 'desc'">▼</span>
            </th>
            <th>01 AM</th>
            <th>02 AM</th>
            <th>03 AM</th>
            <th>04 AM</th>
            <th>05 AM</th>
            <th>06 AM</th>
            <th>07 AM</th>
            <th>08 AM</th>
            <th>09 AM</th>
            <th>10 AM</th>
            <th>11 AM</th>
            <th>12 PM</th>
            <th>01 PM</th>
            <th>02 PM</th>
            <th>03 PM</th>
            <th>04 PM</th>
            <th>05 PM</th>
            <th>06 PM</th>
            <th>07 PM</th>
            <th>08 PM</th>
            <th>09 PM</th>
            <th>10 PM</th>
            <th>11 PM</th>
            <th>12 AM</th>
            <th style="color: #f45e28; min-width: 100px; text-align: center">Total</th>
          </tr>
        </thead>
        <tbody style="font-family: 'Cairo', sans-serif; color: #5b5b5b">
          <tr
            style="font-size: 14px; border-top: 5px solid #fff; border-bottom: 5px solid #fff"
            *ngFor="
              let data of heatMapReportList
                | paginate
                  : {
                      id: 'pagination1',
                      itemsPerPage: pageSize,
                      currentPage: pageNo,
                      totalItems: totalListCount
                    }
            "
          >
            <td style="padding: 12px">{{ data.date }}</td>
            <td
              [style.opacity]="getOpacity(item)"
              [style.background-color]="getOpacity(item)"
              style="text-align: center; color: #000"
              *ngFor="let item of data.timeslots"
            >
              {{ item }}
            </td>
            <td style="background-color: #f0f0f0; text-align: center; color: #000">
              {{ data.totalCount }}
            </td>
          </tr>
        </tbody>
      </table>
      <div *ngIf="totalListCount == 0" style="padding: 30px; text-align: center">
        <p class="fs18 fw-bold cairo-regular my-5 text-black">No Records Found</p>
      </div>
    </div>
  </div>

  <div *ngIf="totalListCount > 0" class="tab-pagination px-2">
    <div class="row">
      <div class="col-md-3 align-items-center">
        <ul class="list-inline my-3 showentries">
          <li class="list-inline-item notify-pagination">
            <label class="fs12 color-grey4 float-start" for="showEnt">Show entries</label>
          </li>
          <li class="list-inline-item dropdown--counts">
            <select id="showEnt"
              class="w-auto form-select fs12 color-grey4 h30"
              (change)="changePageSize($event.target.value)"
              ngModel="{{ pageSize }}"
            >
              <option value="10">10</option>
              <option value="50">50</option>
              <option value="100">100</option>
              <option value="150">150</option>
            </select>
          </li>
        </ul>
      </div>
      <div class="col-md-8 text-center">
        <div class="my-3 position-relative d-inline-block">
          <pagination-controls
            id="pagination1"
            #pagination
            (pageChange)="changePageNo($event)"
            previousLabel=""
            nextLabel=""
          >
          </pagination-controls>
        </div>
      </div>
    </div>
  </div>
</section>

<!--Filter Popup Start-->
<ng-template #template>
  <div class="modal-header">
    <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Filter</h1>
    <button type="button" class="close ms-auto" aria-label="Close" (click)="closeIcon()">
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body py-0">
    <div class="report-heatmap-details">
      <form
        name="exportform"
        class="custom-material-form add-concrete-material-form heatmap-schedule-form"
        id="filter-form2"
        [formGroup]="filterForm"
        (ngSubmit)="filterSubmit()"
        novalidate
      >
        <div class="row mx-2">
          <div class="col-md-6 p-0">
            <div class="form-group company-select" id="company-select7">
              <label class="fs12 fw600 mt15" for="bookType">Booking Type </label>
              <ng-multiselect-dropdown  id="bookType"
                [placeholder]="'Booking Type'"
                [settings]="newRequestTypeDropdownSettings"
                [data]="defineListData"
                formControlName="templateType"
              >
              </ng-multiselect-dropdown>
            </div>
          </div>
          <div class="col-md-6 padding-0px">
            <label class="fs12 fw600 mb-0 mt-3"  for="timeRan">Time Range</label>
            <div class="timezone-flex pt-0">
              <div class="input-group mb-0 delivery-time weeklycalendar-time">
                <timepicker  for="timeRan"
                  [formControlName]="'startTime'"
                  (keypress)="numberOnly($event)"
                  class="mt-0 Weeklycalendar-picker"
                >
                </timepicker>
              </div>
              <div class="color-red"></div>
              <div class="input-group mb-0 delivery-time weeklycalendar-time week-end-time">
                <timepicker
                  [formControlName]="'endTime'"
                  (keypress)="numberOnly($event)"
                  class="Weeklycalendar-picker"
                >
                </timepicker>
              </div>
            </div>
          </div>
        </div>
        <div class="row mx-2">
          <div class="col-md-6 px-0">
            <div class="input-group mt-0">
              <input
                type="text"
                formControlName="dateFilter"
                placeholder="Date Range Picker"
                class="form-control"
                bsDaterangepicker
                [bsConfig]="{
                  isAnimated: true,
                  showWeekNumbers: false,
                  displayMonths: 1,
                  customTodayClass: 'today'
                }"
              />
                <span class="input-group-text">
                  <img
                    src="../../../assets/images/range_icon.svg"
                    class="range-image"
                    alt="range"
                  />
                </span>
            </div>
          </div>
          <div class="col-md-6 padding-0px">
            <div class="form-group mt2">
              <select
                class="form-control fs12 material-input"
                id="equipmentFilter"
                [formControlName]="'equipmentFilter'"
              >
                <option value="" disabled selected hidden>Equipment</option>
                <option
                  *ngFor="let item of equipmentList"
                  value="{{ item.id }}"
                  [ngValue]="item.id"
                >
                  {{ item.equipmentName }}
                </option>
              </select>
            </div>
          </div>
        </div>
        <div class="row mx-2">
          <div class="col-md-6 px-0">
            <div class="form-group">
              <select
                class="form-control fs12 material-input"
                id="defineFilter"
                [formControlName]="'defineFilter'"
              >
                <option value="" disabled selected hidden>Definable Feature of Work</option>
                <option *ngFor="let item of defineList" value="{{ item.id }}" [ngValue]="item.id">
                  {{ item.DFOW }}
                </option>
              </select>
            </div>
          </div>
          <div class="col-md-6 paddingleft-timezone padding-0px">
            <div class="form-group">
              <select
                class="form-control fs12 material-input"
                id="statusFilter"
                [formControlName]="'statusFilter'"
              >
                <option value="" disabled selected hidden>Status</option>
                <option *ngFor="let item of wholeStatus" value="{{ item }}" class="ps-4">
                  {{ item }}
                </option>
              </select>
            </div>
          </div>
        </div>

        <div class="row mx-2">
          <div class="col-md-6 responsibleperson-padding-filter padding-0px">
            <div class="form-group">
              <select
                class="form-control fs12 material-input"
                id="responsiblePersonFilter"
                [formControlName]="'memberFilter'"
              >
                <option value="" disabled selected hidden>Responsible Person</option>
                <ng-container *ngFor="let item of memberList">
                  <option *ngIf="item.status === 'pending' && !item.isGuestUser" value="{{ item.id }}">
                    {{ item.User?.email }}
                  </option>
                  <option *ngIf="item.status === 'pending' && item.isGuestUser" value="{{ item.id }}">
                    {{ item.User?.email }}(Guest)
                  </option>
                  <option *ngIf="item.status === 'completed'" value="{{ item.id }}">
                    {{ item.User?.firstName }} {{ item.User?.lastName }}
                  </option>
                </ng-container>
              </select>
            </div>
          </div>
          <div class="col-md-6 padding-0px">
            <div class="form-group">
              <select
                class="form-control fs12 material-input"
                id="companyFilter"
                [formControlName]="'companyFilter'"
              >
                <option value="" disabled selected hidden>Responsible Company</option>
                <option *ngFor="let item of companyList" value="{{ item.id }}" [ngValue]="item.id">
                  {{ item.companyName }}
                </option>
              </select>
            </div>
          </div>
        </div>
        <div class="row mx-2">
          <div class="col-md-6 px-0">
            <div class="form-group">
              <select
                class="form-control fs12 material-input"
                id="gateFilter"
                [formControlName]="'gateFilter'"
              >
                <option value="" disabled selected hidden>Gate</option>
                <option *ngFor="let item of gateList" value="{{ item.id }}" [ngValue]="item.id">
                  {{ item.gateName }}
                </option>
              </select>
            </div>
          </div>
          <div class="col-md-6 padding-0px">
            <div class="form-group">
              <select
                class="form-control fs12 material-input"
                id="locationFilter"
                [formControlName]="'locationFilter'"
              >
                <option value="" disabled selected hidden>Location</option>
                <option
                  *ngFor="let item of locationList"
                  value="{{ item.id }}"
                  [ngValue]="item.id"
                >
                  {{ item.locationPath }}
                </option>
              </select>
            </div>
          </div>
        </div>
        <div class="modal-footer border-0 justify-content-center add-calendar-footer">
          <div class="mt-0 mb15 text-center">
            <button
              class="btn btn-orange color-black radius20 fs12 fw-bold me-3 px-2rem iconremove"
              type="button"
              (click)="resetFilter()"
            >
              Reset
            </button>
            <button
              type="submit"
              class="btn btn-orange color-orange radius20 fs12 fw-bold me-3 px-2rem iconremove"
            >
              <em class="fa fa-spinner" aria-hidden="true" *ngIf="formSubmitted"></em>
              Apply
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</ng-template>
<!--Filter Popup Start-->

<!--Export Popup Start-->
<ng-template #templateexport>
  <div class="modal-header">
    <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Heat Map Export</h1>
    <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef2?.hide()">
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body">
    <div class="addcalendar-details">
      <form
        name="exportform"
        class="custom-material-form add-concrete-material-form"
        [formGroup]="exportForm"
      >
        <div class="row">
          <div class="col-md-12">
            <div class="floating-concrete mt-3">
              <div class="form-group floating-label">
                <input id="reportNam"
                  class="floating-input form-control fs12 px-0"
                  type="text"
                  placeholder=""
                  formControlName="reportName"
                  [(ngModel)]="reportName"
                />
                <div
                  class="color-red fs12"
                  *ngIf="formSubmitted && exportForm.get('reportName').errors"
                >
                  <small *ngIf="exportForm.get('reportName').errors.required"
                    >*Report Name is Required.</small
                  >
                </div>
                <label class="fs12 fw600 m-0 color-grey11 schedule-form-label"  for="reportNam"
                  >Report Name
                  <span class="color-red">
                    <sup>*</sup>
                  </span></label
                >
              </div>
            </div>
          </div>
          <div class="col-md-12">
            <div class="form-group pt-0 mt-0">
              <label class="fs12 fw600 m-0 color-grey11" for="outFor"
                >Output format
                <span class="color-red">
                  <sup>*</sup>
                </span></label
              >
              <select id="outFor"
                class="form-control fs12 material-input px-1 color-grey11"
                formControlName="reportType"
              >
                <option value="PDF">PDF</option>
              </select>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
  <div class="modal-footer border-0 justify-content-center add-calendar-footer">
    <div class="mt-0 mb15 text-center">
      <button
        class="btn btn-grey color-dark-grey radius20 fs12 fw-bold me-3 px-2rem"
        type="button"
        (click)="cancelExport()"
      >
        Cancel
      </button>
      <button
        type="button"
        (click)="export()"
        class="btn btn-orange color-orange radius20 fs12 fw-bold me-3 px-2rem iconremove"
        [disabled]="exportSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="exportSubmitted"></em>
        Download
      </button>
    </div>
  </div>
</ng-template>
<!--Export Popup End-->
