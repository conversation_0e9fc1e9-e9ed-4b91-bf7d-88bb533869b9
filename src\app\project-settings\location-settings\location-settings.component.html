<section class="page-section pt-2">
  <div class="page-inner-content">
    <div class="top-header mb-3">
      <div class="row pt-md-15px">
        <div class="col-md-8">
          <div class="top-btn">
            <ul class="list-group list-group-horizontal">
              <li class="list-group-item p0 border-0 bg-transparent me-4">
                <button
                  class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular"
                  (click)="openModal(null, addLocation, 'add')"
                >
                  Add Location
                </button>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent me-4">
                <button
                  class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular px-3"
                  (click)="openImportModal(import)"
                >
                  Import
                </button>
              </li>
            </ul>
          </div>
        </div>
        <div class="col-md-4">
          <div class="top-filter">
            <ul class="list-group list-group-horizontal justify-content-end">
              <li class="list-group-item p0 border-0 bg-transparent me-2">
                <div class="search-icon">
                  <input
                    class="form-control fs12 color-grey8"
                    [ngClass]="showSearchbar ? 'input-hover-disable' : 'input-search'"
                    placeholder="What you are looking for?"
                    (input)="searchLocation($event.target.value)"
                    [(ngModel)]="search"
                  />
                  <div class="icon">
                    <img
                      src="./assets/images/cross-close.svg"
                      *ngIf="showSearchbar"
                      (click)="clear()"
                      alt="close-cross"
                      (keydown)="handleDownKeydown($event, '', '','', 'clear')"
                    />
                    <em class="fa fa-search fs12 color-grey8" *ngIf="!showSearchbar"></em>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="notification-page-card bg-white rounded border-0">
      <div class="row fw700 fs12 py-3 bg-grey m-0 px-0">
        <div class="col-md-2 col-2 ps-3">
          Location Path
          <span>
            <img
              src="./assets/images/down-chevron.svg"
              alt="down-arrow"
              class="h-10px ms-2"
              (click)="sortByField('locationName', 'ASC')"
              (keydown)="handleToggleKeydown($event, 'locationName', 'ASC')"
              *ngIf="sortColumn !== 'locationName'"
            />
            <img
              src="./assets/images/down-chevron.svg"
              alt="down-arrow"
              class="h-10px ms-2"
              (click)="sortByField('locationName', 'ASC')"
              (keydown)="handleToggleKeydown($event, 'locationName', 'ASC')"
              *ngIf="sort === 'DESC' && sortColumn === 'locationName'"
            />
            <img
              src="./assets/images/up-chevron.svg"
              alt="up-arrow"
              class="h-10px ms-2"
              (click)="sortByField('locationName', 'DESC')"
              (keydown)="handleToggleKeydown($event, 'locationName', 'DESC')"
              *ngIf="sort === 'ASC' && sortColumn === 'locationName'"
            />
          </span>
        </div>
        <div class="col-md-2 col-2">
          Gates
          <span>
            <img
              src="./assets/images/down-chevron.svg"
              alt="down-arrow"
              class="h-10px ms-2"
              (click)="sortByField('locationName', 'ASC')"
              (keydown)="handleToggleKeydown($event, 'locationName', 'ASC')"
              *ngIf="sortColumn !== 'locationName'"
            />
            <img
              src="./assets/images/down-chevron.svg"
              alt="down-arrow"
              class="h-10px ms-2"
              (click)="sortByField('locationName', 'ASC')"
              (keydown)="handleToggleKeydown($event, 'locationName', 'ASC')"
              *ngIf="sort === 'DESC' && sortColumn === 'locationName'"
            />
            <img
              src="./assets/images/up-chevron.svg"
              alt="up-arrow"
              class="h-10px ms-2"
              (keydown)="handleToggleKeydown($event, 'locationName', 'DESC')"
              (click)="sortByField('locationName', 'DESC')"
              *ngIf="sort === 'ASC' && sortColumn === 'locationName'"
            />
          </span>
        </div>
        <div class="col-md-3 col-3">
          Equipment
          <span>
            <img
              src="./assets/images/down-chevron.svg"
              alt="down-arrow"
              class="h-10px ms-2"
              (keydown)="handleToggleKeydown($event, 'locationName', 'ASC')"
              (click)="sortByField('locationName', 'ASC')"
              *ngIf="sortColumn !== 'locationName'"
            />
            <img
              src="./assets/images/down-chevron.svg"
              alt="down-arrow"
              class="h-10px ms-2"
              (keydown)="handleToggleKeydown($event, 'locationName', 'ASC')"
              (click)="sortByField('locationName', 'ASC')"
              *ngIf="sort === 'DESC' && sortColumn === 'locationName'"
            />
            <img
              src="./assets/images/up-chevron.svg"
              alt="up-arrow"
              class="h-10px ms-2"
              (keydown)="handleToggleKeydown($event, 'locationName', 'DESC')"
              (click)="sortByField('locationName', 'DESC')"
              *ngIf="sort === 'ASC' && sortColumn === 'locationName'"
            />
          </span>
        </div>
        <div class="col-md-3 col-3">
          Notes
          <span>
            <img
              src="./assets/images/down-chevron.svg"
              alt="down-arrow"
              class="h-10px ms-2"
              (keydown)="handleToggleKeydown($event, 'notes', 'ASC')"
              (click)="sortByField('notes', 'ASC')"
              *ngIf="sortColumn !== 'notes'"
            />
            <img
              src="./assets/images/down-chevron.svg"
              alt="down-arrow"
              class="h-10px ms-2"
              (click)="sortByField('notes', 'ASC')"
              (keydown)="handleToggleKeydown($event, 'notes', 'ASC')"
              *ngIf="sort === 'DESC' && sortColumn === 'notes'"
            />
            <img
              src="./assets/images/up-chevron.svg"
              alt="up-arrow"
              class="h-10px ms-2"
              (click)="sortByField('notes', 'DESC')"
              (keydown)="handleToggleKeydown($event, 'notes', 'DESC')"
              *ngIf="sort === 'ASC' && sortColumn === 'notes'"
            />
          </span>
        </div>
        <div class="col-md-2 col-2 text-left"><span class="mx-0 ms-2">Action</span></div>
      </div>

      <div class="notification-setting-lists location-settings-lists" *ngIf="!loader">
        <div class="p-0 bg-white">
          <ul class="list-group list-group-horizontal w-100 bg-white">
            <li class="list-group-item border-0 col-md-2 bg-transparent location-setting-category">
              <span class="ps-3 fw700 fs12">{{ defaultLocationPath.locationName }}</span>
            </li>
            <li
            class="list-group-item border-0 col-md-2 bg-transparent"
            *ngIf="defaultLocationPath.gateDetails"
          >
            <span class="ps-3 fw400 fs12">{{ getGateNames(defaultLocationPath) }}</span>
          </li>
          <li
            class="list-group-item border-0 col-md-2 bg-transparent px-3"
            *ngIf="!defaultLocationPath.gateDetails"
          >
            <span class="ps-0 ms-0 fw400 fs12">-</span>
          </li>
            <li
            class="list-group-item border-0 col-md-3 bg-transparent px-3"
            *ngIf="!defaultLocationPath.EquipmentId"
          >
            <span class="ps-0 ms-0 fw400 fs12">-</span>
          </li>
            <li
            class="list-group-item border-0 col-md-3 bg-transparent px-3"
            *ngIf="defaultLocationPath.EquipmentId"
          >
            <span class="ps-0 ms-0 fw400 fs12">{{ getEquipmentNames(defaultLocationPath) }}</span>
          </li>

          <li
            class="list-group-item border-0 col-md-3 col-3 bg-transparent px-3"
            *ngIf="defaultLocationPath.notes"
          >
            <span class="ps-0 ms-0 fw400 fs12">{{ defaultLocationPath.notes }} </span>
          </li>
          <li
            class="list-group-item border-0 col-md-3 col-3 bg-transparent px-3"
            *ngIf="!defaultLocationPath.notes"
          >
            <span class="ps-0 ms-0 fw400 fs12">-</span>
          </li>
            <li class="list-group-item border-0 col-md-2 col-2 bg-transparent">
              <span
                class="fw400 fs12 notification-span-desktop notification-span-mobile ms-0 ps-0"
              >
                <img
                  src="./assets/images/edit.svg"
                  alt="edit"
                  class="h-15px"
                  (click)="openModal(defaultLocationPath, addLocation, 'edit')"
                  (keydown)="handleDownKeydown($event, defaultLocationPath, addLocation,'edit', 'open1')"
                />
              </span>
            </li>
          </ul>
        </div>
        <div class="ps-5">
          <accordion
            class="main-accordion"
            *ngFor="
              let item of locations
                | paginate
                  : {
                      itemsPerPage: pageSize,
                      currentPage: currentPageNo,
                      totalItems: totalCount
                    };
              let i = index
            "
          >
            <accordion-group>
              <div class="d-flex shadow-none" accordion-heading>
                <ul class="list-group list-group-horizontal w-100 bg-grey4">
                  <li
                    class="list-group-item border-0 col-md-2 bg-transparent location-setting-category"
                  >
                    <span *ngIf="item?.paths?.length > 0">
                      <img
                        [ngClass]="{ rotateright: isOpened }"
                        src="./assets/images/down-arrow.svg"
                        alt="Down Arrow"
                        class="arrow eye-cursor"
                      />
                    </span>
                    <span class="ps-3 fw400 fs12">{{ item.locationName }}</span>
                  </li>
                  <li
                    class="list-group-item border-0 col-md-2 bg-transparent"
                    *ngIf="item.gateDetails"
                  >
                <span class="ps-3 fw400 fs12">{{ getGateNames(item) }}</span>
                  </li>
                  <li
                    class="list-group-item border-0 col-md-2 bg-transparent px-3"
                    *ngIf="!item.gateDetails"
                  >
                    <span class="ps-0 ms-0 fw400 fs12">-</span>
                  </li>
                  <li
                    class="list-group-item border-0 col-md-3 bg-transparent"
                    *ngIf="item.EquipmentId"
                  >
                    <span *ngIf="item?.EquipmentId?.length > 0">

                    </span>
                    <span class="ps-3 fw400 fs12">{{ getEquipmentNames(item) }}</span>
                  </li>
                  <li
                    class="list-group-item border-0 col-md-3 bg-transparent px-3"
                    *ngIf="!item.EquipmentId"
                  >
                    <span class="ps-0 ms-0 fw400 fs12">-</span>
                  </li>
                  <li
                    class="list-group-item border-0 col-md-3 col-3 bg-transparent px-3"
                    *ngIf="item.notes"
                  >
                    <span class="ps-0 ms-0 fw400 fs12">{{ item.notes }} </span>
                  </li>
                  <li
                    class="list-group-item border-0 col-md-3 col-3 bg-transparent px-3"
                    *ngIf="!item.notes"
                  >
                    <span class="ps-0 ms-0 fw400 fs12">-</span>
                  </li>
                  <li class="list-group-item border-0 col-md-2 col-2 bg-transparent">
                    <span
                      class="fw400 fs12 notification-span-desktop notification-span-mobile ms-0 ps-0"
                    >
                      <img
                        src="./assets/images/edit.svg"
                        alt="edit"
                        class="h-15px"
                        (click)="openModal(item, addLocation, 'edit')"
                        (keydown)="handleDownKeydown($event, item, addLocation,'edit', 'open')"
                      />
                      <img
                        src="./assets/images/delete.svg"
                        alt="delete"
                        class="h-15px mx-3"
                        (click)="openDeleteModal(item, deleteList)"
                        (keydown)="handleDownKeydown($event, item, deleteList,'', 'delete')"
                      />
                    </span>
                  </li>
                </ul>
              </div>
              <accordion *ngIf="item?.paths?.length > 0">
                <accordion-group *ngFor="let path of item?.paths">
                  <div class="d-flex" accordion-heading>
                    <ul class="list-group list-group-horizontal w-100 bg-grey">
                      <li class="list-group-item border-0 w-50 col-md-6 bg-transparent">
                        <span class="ms-4">
                          <span *ngIf="path?.tier?.length > 0">
                            <img
                              [ngClass]="{ rotateright: isOpened }"
                              src="./assets/images/down-arrow.svg"
                              alt="Down Arrow"
                              class="arrow eye-cursor"
                            />
                          </span>
                          <span
                            class="fw400 fs12 notification-span-desktop notification-span-mobile ms-0"
                          >
                            {{ path.locationName }}
                          </span>
                        </span>
                      </li>
                      <li
                        class="list-group-item border-0 w-50 col-md-5 text-end px-0 bg-transparent"
                      >
                        <span
                          class="fw400 fs12 notification-span-desktop notification-span-mobile ps-0 ms-0 me-1"
                        >
                        </span>
                      </li>
                    </ul>
                  </div>
                  <div *ngIf="path?.tier?.length > 0">
                    <ul
                      class="list-group list-group-horizontal w-100 bg-white"
                      *ngFor="let tier of path?.tier"
                    >
                      <li class="list-group-item border-0 w-50 col-md-6">
                        <span
                          class="fw400 fs12 notification-span-desktop notification-span-mobile ms-3 col-md-6 px-5"
                        >
                          {{ tier.locationName }}
                        </span>
                      </li>

                      <li class="list-group-item border-0 w-50 col-md-5 text-end">
                        <span
                          class="fw400 fs12 notification-span-desktop notification-span-mobile me-3"
                        >
                        </span>
                      </li>
                    </ul>
                  </div>
                </accordion-group>
              </accordion>
            </accordion-group>
          </accordion>
        </div>
        <table aria-describedby="locationTable" style="text-align: center; width: 100%">
          <th scope="col"></th>
          <tr *ngIf="loader == false && locations.length == 0 && search !== ''">
            <td colspan="100">
              <div class="fs18 fw-bold cairo-regular my-5 text-black">No Records Found</div>
            </td>
          </tr>
          <tr *ngIf="loader == false && locations.length == 0 && search === ''">
            <td colspan="100">
              <div class="fs14 cairo-regular my-5 text-black">
                Click on the <span class="fs14 fw-bold">Add Location</span> button to map new
                locations under
                <span class="fs14 fw-bold">{{ defaultLocationPath.locationName }}</span>
              </div>
            </td>
          </tr>
        </table>
        <div
          class="tab-pagination px-2"
          id="tab-pagination6"
          *ngIf="loader == false && totalCount > 5"
        >
          <div class="row">
            <div class="col-md-3 align-items-center">
              <ul class="list-inline my-3">
                <li class="list-inline-item notify-pagination">
                  <label class="fs12 color-grey4" for="shwEnt">Show entries</label>
                </li>
                <li class="list-inline-item">
                  <select  id="shwEnt"
                  class="w-auto form-select fs12 color-grey4"
                    (change)="changePageSize($event.target.value)"
                    [ngModel]="pageSize"
                  >
                    <option value="5">5</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                    <option value="150">150</option>
                  </select>
                </li>
              </ul>
            </div>
            <div class="col-md-8 text-center">
              <div class="my-3 position-relative d-inline-block">
                <pagination-controls
                  (pageChange)="changePageNo($event)"
                  previousLabel=""
                  nextLabel=""
                >
                </pagination-controls>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="fs18 fw-bold cairo-regular my-5 text-black text-center" *ngIf="loader">
      Loading...
    </div>
  </div>
</section>

<!-- Import  -->
<ng-template #import>
  <div class="mx-2 mx-md-5 my-5 bulkupload">
    <button type="button" class="close upload-close bg-transparent border-0" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
    <h1 class="fw-bold cairo-regular fs20 text-center pb-4">Import file</h1>
    <ngx-file-drop
      dropZoneLabel="Drop files here"
      multiple="false"
      (onFileDrop)="dfowImportFiledropped($event)"
    >
      <ng-template ngx-file-drop-content-tmp let-openFileSelector="openFileSelector">
        <div class="bulkupload-content text-center" (click)="openFileSelector()" (keydown)="openFileSelector()">
          <img src="./assets/images/file.svg" alt="Excel" />
          <p class="fs14 fw600 mb3 pt10 color-grey7">Drag & Drop your file here</p>
          <p class="fs12 color-grey8 fw500 mb3">Or</p>
          <label for="browse" class="color-blue4 fs14 mb10 fw500 text-underline">Click here to browse</label>
        </div>
      </ng-template>
    </ngx-file-drop>
    <p class="fs10 color-grey8 fw500 my-2 text-end">*Supported formats are .xlsx</p>
    <div class="row upload-table py-3 m-0" *ngFor="let item of this.locationFiles; let i = index">
      <div class="col-1 col-md-1 ps-0">
        <img src="./assets/images/xlsx.png" alt="attached-file" class="rounded upload-img" />
      </div>
      <div class="col-9 col-md-9">
        <h1 class="fs16 ms-4 color-grey7">{{ item.relativePath }}</h1>
        <p class="ms-4 mb-0 color-grey8">
          {{ todayDate | date : 'short' }}<span class="ms-3"></span>
        </p>
      </div>
      <div class="col-1 col-md-2 text-end d-flex justify-content-end pe-0" (click)="removeFile(i)" (keydown)="handleDownKeydown($event, i, '','', 'remove')">
        <img src="./assets/images/delete.svg" alt="delete" class="h-15px c-pointer" />
      </div>
    </div>
    <div class="text-center">
      <button
        class="btn btn-orange radius20 fs12 fw-bold cairo-regular my-4 px-5"
        type="submit"
        (click)="importData()"
        *ngIf="locationFiles.length > 0"
        [disabled]="importSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="importSubmitted"></em>
        Done
      </button>
      <p class="color-orange c-pointer fs12" (click)="download()" (keydown)="handleDownKeydown($event, '', '','', 'download')">
        <u>Download sample template here</u>
      </p>
    </div>
  </div>
</ng-template>
<!-- Import  -->

<ng-template #addLocation>
  <div class="modal-header text-center justify-content-center">
    <h4 class="fs14 fw-bold cairo-regular color-text7 my-1">{{ actionType }} Location</h4>
    <button
      type="button"
      class="close position-absolute"
      aria-label="Close"
      (click)="close(cancelConfirmation)"
    >
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body px-md-0">
    <form name="form" class="custom-material-form" novalidate [formGroup]="locationForm" novalidate>
      <div class="row px-4">
        <div class="col-md-12 mb-2 d-flex">
          <div class="form-group w-100">
            <input
              type="text"
              class="form-control fs12 material-input"
              placeholder="Category*"
              formControlName="mainCategory"
            />
            <div class="color-red" *ngIf="addLoader && locationForm.get('mainCategory').errors">
              <small *ngIf="locationForm.get('mainCategory').errors.required"
                >Category is required</small
              >
            </div>
          </div>
        </div>
      </div>

      <div class="addLocationModal">
        <div formArrayName="paths">
          <ng-container
            *ngFor="let controls of getSubLocationPaths(locationForm); let i = index"
            [formGroupName]="i"
          >
            <div class="row px-4" *ngIf="controls.value.isDeleted === 0">
              <div class="col-md-12 mb-2 mt20 d-flex">
                <div class="form-group w-100">
                  <input type="text" class="form-control fs12 material-input" placeholder="Sub Category"
                    formControlName="subCategory" />
                </div>
                <ng-container>
                  <img src="assets/images/add.svg" alt="plus" class="c-pointer ms-4" (click)="addSubCategory()" (keydown)="handleDownKeydown($event, '', '','', 'addSub')" />
                </ng-container>
                <img src="assets/images/minus-circle.svg" alt="plus" class="c-pointer ms-4" (click)="removeSubCategory(i)" (keydown)="handleDownKeydown($event, i, '','', 'removeSub')"/>
              </div>
            </div>
            <div class="row px-5">
              <ng-container formArrayName="tier">
                <ng-container
                  *ngFor="let tierControls of getTierPaths(controls); let j = index"
                  [formGroupName]="j"
                >
                  <div class="col-md-12 mb-2 d-flex" *ngIf="tierControls.value.isDeleted === 0">
                    <div class="form-group w-100">
                      <input
                        type="text"
                        class="form-control fs12 material-input"
                        placeholder="Tier Name"
                        formControlName="tier"
                      />
                    </div>
                    <img
                      src="assets/images/add.svg"
                      alt="plus"
                      class="c-pointer ms-4"
                      (click)="addTier(i)" (keydown)="handleDownKeydown($event, i, '','', 'addTir')"
                    />
                    <img
                      src="assets/images/minus-circle.svg"
                      alt="plus"
                      class="c-pointer ms-4"
                      (click)="removeTier(i, j)" (keydown)="handleDownKeydown($event, i, j, '', 'removeTi')"
                    />
                  </div>
                </ng-container>
              </ng-container>
              <div class="col-md-12 mb-2 text-left" *ngIf="showAddTierButton[i]">
                <button
                  class="btn btn-orange-dark radius20 fs12 mb-3 me-md-3 fw-bold cairo-regular px-3"
                  type="button"
                  (click)="addTier(i)"
                >
                  Add Tier
                </button>
              </div>
            </div>
          </ng-container>
          <div class="col-md-12 mb-2 text-left" *ngIf="subCatCount !== 1">
            <button
              class="btn btn-orange-dark radius20 fs12 mb-3 me-md-3 fw-bold cairo-regular px-3"
              type="button"
              (click)="addSubCategory()"
            >
              Add SubCategory
            </button>
          </div>

        </div>

        <div class="form-group ml25 my-0 company-select equipment-select inspection-select custom-col">
          <label class="fs12 fw600 mt-2" for="gate">Gate<sup>*</sup></label>
          <ng-multiselect-dropdown [placeholder]="'Gate'" [settings]="gateDropdownSettings" [data]="gateList"
          formControlName="GateId"  id="gate">
        </ng-multiselect-dropdown>
          <div class="color-red" *ngIf="addLoader && locationForm.get('GateId').errors">
            <small *ngIf="locationForm.get('GateId').errors.required"
              >*Gate is Required.</small
            >
          </div>
        </div>
      <div class="form-group ml25 my-0 company-select equipment-select inspection-select custom-col">
        <label class="fs12 fw600 mt-2 mb8" for="equipment">Equipment<sup>*</sup></label>
        <ng-multiselect-dropdown [placeholder]="'Equipment'" [settings]="equipmentDropdownSettings" [data]="equipmentList"
          formControlName="EquipmentId" id="equipment">
        </ng-multiselect-dropdown>
        <div
        class="color-red"
        *ngIf="addLoader && locationForm.get('EquipmentId').errors"
      >
        <small *ngIf="locationForm.get('EquipmentId').errors.required"
          >*Equipment is Required.</small
        >
      </div>
      </div>
      <div class="form-group ml25 mb-0 timezone-formgroup custom-col">
        <label class="fs12 fw600 mb8" for="timeZn"> Time Zone</label>
        <ng-multiselect-dropdown id="timeZn"
          [placeholder]="'Choose TimeZone'"
          [settings]="dropdownSettings"
          [data]="timezoneList"
          formControlName="TimeZoneId"
        >
        </ng-multiselect-dropdown>
        <div class="color-red" *ngIf="addLoader && locationForm.get('TimeZoneId').errors">
          <small *ngIf="locationForm.get('TimeZoneId').errors.required"
            >*TimeZone is Required.</small
          >
        </div>
      </div>

        <div class="custom-col">
          <div class="row ps-4 pe-0 location-settings-textarea">
            <div class="col-md-12 pe-0">
              <div class="form-group w-100">
                <label class="font-weight fs12 fw600 mt-2" for="notes">Notes</label
                ><textarea id="notes"
                  placeholder=""
                  class="form-control fs12 ng-pristine ng-valid ng-touched"
                  formControlName="notes"
                ></textarea>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="text-center mt-3 mb-4">
        <button
          class="btn btn-grey color-dark-grey radius20 fs12 mb-3 me-md-3 fw-bold cairo-regular px-5"
          type="button"
          (click)="close(cancelConfirmation)"
        >
          Cancel
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular mb-3 px-5"
          [disabled]="addLoader"
          (click)="submitLocation(actionType)"
        >
          <em class="fa fa-spinner" aria-hidden="true" *ngIf="addLoader"></em>Submit
        </button>
      </div>
    </form>
  </div>
</ng-template>

<ng-template #deleteList>
  <div class="modal-body">
    <div class="text-center my-4">
      <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">Are you sure you want to delete ?</p>
      <button
        class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
        (click)="resetAndClose()"
      >
        No
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
        (click)="deleteLocation()"
        [disabled]="deleteSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="deleteSubmitted"></em>yes
      </button>
    </div>
  </div>
</ng-template>

<!--Confirmation Popup-->
<div id="confirm-popup7">
  <ng-template #cancelConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">Are you sure you want to cancel?</p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetForm('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="resetForm('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
