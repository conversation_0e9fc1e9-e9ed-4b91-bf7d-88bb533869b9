import { Component, Input, OnInit, TemplateRef } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators, UntypedFormArray, UntypedFormControl } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Observable } from 'rxjs';
import moment from 'moment';
import { Router } from '@angular/router';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { Socket } from 'ngx-socket-io';
import { DeliveryService } from '../../../../services/profile/delivery.service';
import { ProjectService } from '../../../../services/profile/project.service';
import { MixpanelService } from '../../../../services/mixpanel.service';
import { ProjectSharingService } from 'src/app/services';
import {
  weekDays,
  recurrence,
  repeatWithSingleRecurrence,
  repeatWithMultipleRecurrence,
} from '../../../../services/common';

interface CreateDeliveryParams {
  newNdrFormValue: {
    companyItems: any[];
    person: any;
    defineItems: any[];
    escort: any;
    description: any;
    GateId: any;
    notes: any;
    EquipmentId: any[];
    LocationId: any;
    isAssociatedWithCraneRequest: any;
    cranePickUpLocation: string;
    craneDropOffLocation: string;
    CraneRequestId: string;
    recurrence: string;
    chosenDateOfMonth: any;
    dateOfMonth: any;
    monthlyRepeatType: any;
    days: any;
    repeatEveryType: any;
    repeatEveryCount: any;
    endDate: any;
    TimeZoneId: any;
  };
  deliveryStart: Date;
  deliveryEnd: Date;
  companies: any[];
  persons: any[];
  define: any[];
  newTimeZoneDetails: any;
  startPicker: any;
  endPicker: any;
  weekStartDate: any;
  weekEndDate: any;
  equipments: any[];
}

@Component({
  selector: 'app-guest-delivery-booking',
  templateUrl: './guest-delivery-booking.component.html',
})
export class GuestDeliveryBookingComponent implements OnInit {
  @Input() data: any;

  @Input() title: string;
  public ProjectId: any;

  public submitted = false;

  public escort = false;

  public formSubmitted = false;

  public editSubmitted = false;

  public modalLoader = false;

  public deliverDetailsForm: UntypedFormGroup;

  public authUser: any = {};

  public loader = false;

  public gateList: any = [];

  public equipmentList: any = [];

  public defineList: any = [];

  public locationList: any = [];

  public newNdrCompanyDropdownSettings: IDropdownSettings;

  public companyList: any = [];

  public lastId: any = {};

  public ParentCompanyId: any;

  public newNdrDefinableDropdownSettings: IDropdownSettings;

  public deliveryEnd: Date;

  public deliveryStart: Date;

  public todayDate = new Date();

  public NDRTimingChanged: boolean = false;

  public craneEquipmentTypeChosen: boolean = false;

  public craneRequestLastId = null;

  public selectedRecurrence = 'Does Not Repeat';

  public recurrence = recurrence;

  public repeatWithSingleRecurrence = repeatWithSingleRecurrence;

  public repeatWithMultipleRecurrence = repeatWithMultipleRecurrence;

  public weekDays: any = weekDays;

  public isRepeatWithMultipleRecurrence = false;

  public isRepeatWithSingleRecurrence = false;

  public showRecurrenceTypeDropdown = false;

  public checkform: any = new UntypedFormArray([]);

  public message = '';

  public monthlyDate = '';

  public monthlyDayOfWeek = '';

  public monthlyLastDayOfWeek = '';

  public enableOption = false;

  public valueExists = [];

  public endTime: Date;

  public startTime: Date;

  public selectedValue;

  public selectedTimeZoneValue: any;

  public selectedLocationId: any;

  public dropdownSettings: IDropdownSettings;

  public locationDropdownSettings: IDropdownSettings;

  public timeZoneValues: any;

  public timezoneList: [];

  public getSelectedTimeZone;

  public recurrenceMinDate = new Date();

  public guestUserId:any;

  public responsibleCompanySelectedItems = [];

  public defaultTimeZone:any;

  public defaultLocation:any;

  public newRequestTypeDropdownSettings: IDropdownSettings;

  public equipmentDropdownSettings: IDropdownSettings;

  public noEquipmentOption = { id: 0, equipmentName: 'No Equipment Needed' };

  public constructor(
    private readonly formBuilder: UntypedFormBuilder,
    public socket: Socket,
    private readonly toastr: ToastrService,
    public router: Router,
    private readonly mixpanelService: MixpanelService,
    public modalRef: BsModalRef,
    public modalRef1: BsModalRef,
    private readonly modalService: BsModalService,
    private readonly deliveryService: DeliveryService,
    public projectService: ProjectService,
    private readonly ProjectSharingServices: ProjectSharingService,
  ) {
    this.selectedValue = this.recurrence[0].value;
    this.ProjectId= +window.atob(localStorage.getItem('guestProjectId'));
    this.ParentCompanyId= +window.atob(localStorage.getItem('guestParentCompanyId'));
    this.guestUserId = +window.atob(localStorage.getItem('guestId'));
    if(this.ProjectId && this.ParentCompanyId){
      this.getOverAllGateInNewDelivery();
    }
    this.deliverForm();
    this.getTimeZoneList();
    if (this.selectedValue === 'Does Not Repeat') {
      this.deliverDetailsForm.get('repeatEveryType').setValue('');
    } else {
      this.deliverDetailsForm.get('repeatEveryCount').setValue(1);
    }
  }

  public ngOnInit(): void {
    this.getSelectedDate();
  }

  public setDefaultDateAndTime(date): void {
    const setStartTime = 7;
    this.deliveryStart = new Date();
    this.deliveryEnd = new Date();
    if (date) {
      this.deliveryStart = new Date(date);
      this.deliveryEnd = new Date(date);
    }
    this.deliveryStart.setHours(setStartTime);
    this.deliveryStart.setMinutes(0);
    this.deliveryEnd.setHours(setStartTime + 1);
    this.deliveryEnd.setMinutes(0);
    this.deliverDetailsForm.get('deliveryStart').setValue(this.deliveryStart);
    this.deliverDetailsForm.get('deliveryEnd').setValue(this.deliveryEnd);
  }

  public ngAfterViewInit(): void {
    this.getLastCraneRequestId();
    this.setDefaultPerson();
  }

  public getSelectedDate(): void {
    const getData = this.data;
    if (getData) {
      if (getData.date && getData.currentView === 'Month') {
        this.deliverDetailsForm
          .get('deliveryDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00'));
        this.setDefaultDateAndTime(this.deliverDetailsForm.get('deliveryDate').value);
      }
      if (
        (getData.date && getData.currentView === 'Week') ||
        (getData.date && getData.currentView === 'Day')
      ) {
        this.deliverDetailsForm
          .get('deliveryDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00:00'));
        this.deliverDetailsForm
          .get('deliveryStart')
          .setValue(moment(getData.date, 'YYYY-MM-DD hh:mm:ss').format());
        this.deliverDetailsForm
          .get('deliveryEnd')
          .setValue(moment(getData.date, 'YYYY-MM-DD hh:mm:ss').add(30, 'minutes').format());
      }
    } else {
      this.setDefaultDateAndTime(null);
    }
  }

  public getLastCraneRequestId(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices.guestGetLastCraneRequestId(params).subscribe((response): void => {
      this.craneRequestLastId = response.lastId?.CraneRequestId;
      this.lastId = response.lastId;
      this.deliverDetailsForm.get('CraneRequestId').setValue(response.lastId?.CraneRequestId);
    });
  }

  public getOverAllGateInNewDelivery(): void {
    this.modalLoader = true;
    const params = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices
      .guestGateList(params, { isFilter: true, showActivatedAlone: true })
      .subscribe((res): void => {
        this.gateList = res.data;
        this.getOverAllEquipmentInNewDelivery();
      });
  }

  public getOverAllEquipmentInNewDelivery(): void {
    const newNdrGetEquipmentsParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices
      .guestListEquipment(newNdrGetEquipmentsParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((equipmentListResponseForNewNdr): void => {
        this.equipmentList = [this.noEquipmentOption, ...equipmentListResponseForNewNdr.data];
        this.equipmentDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'equipmentName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
        this.newNdrgetCompanies();
      });
  }

  public newNdrgetCompanies(): void {
    const newNdrGetCompaniesParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices
      .guestGetCompanies(newNdrGetCompaniesParams)
      .subscribe((companiesResponseForNewNdr: any): void => {
        if (companiesResponseForNewNdr) {
          this.companyList = companiesResponseForNewNdr.data;
          const loggedInUser = this.companyList.filter(
            (a: { id: any }): any => a.id === this.authUser.CompanyId,
          );
          this.responsibleCompanySelectedItems = loggedInUser;
          this.getDefinable();
          this.newNdrCompanyDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'companyName',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
        }
      });
  }

  public getDefinable(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices.guestGetDefinableWork(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.defineList = data;
        this.newNdrDefinableDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'DFOW',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
        this.getLocations();
      }
    });
  }

  public getLocations(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices.guestGetLocations(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.locationList = data;
        this.locationDropdownSettings = {
          singleSelection: true,
          idField: 'id',
          textField: 'locationPath',
          allowSearchFilter: true,
          closeDropDownOnSelection: true,
        };
        this.setDefaultLocationPath();
      }
    });
  }

  public requestAutocompleteItems = (text: string): Observable<any> => {
    const param = {
      ProjectId: this.ProjectId,
      search: text,
      ParentCompanyId: this.ParentCompanyId,
    };
    return this.ProjectSharingServices.guestSearchNewMember(param);
  };

  public closeModalPopup(): void {
    this.modalLoader = false;
  }

  public deliverForm(): void {
    this.deliverDetailsForm = this.formBuilder.group({
      LocationId: ['', Validators.compose([Validators.required])],
      EquipmentId: [this.formBuilder.array([])],
      GateId: ['', Validators.compose([Validators.required])],
      notes: [''],
      CraneRequestId: [''],
      person: ['', Validators.compose([Validators.required])],
      description: ['', Validators.compose([Validators.required])],
      deliveryDate: ['', Validators.compose([Validators.required])],
      deliveryStart: ['', Validators.compose([Validators.required])],
      deliveryEnd: ['', Validators.compose([Validators.required])],
      escort: [false],
      companyItems: [this.formBuilder.array([])],
      defineItems: [this.formBuilder.array([])],
      cranePickUpLocation: [''],
      craneDropOffLocation: [''],
      isAssociatedWithCraneRequest: [false, Validators.compose([Validators.required])],
      recurrence: [''],
      repeatEveryCount: [''],
      repeatEveryType: [''],
      days: new UntypedFormArray([]),
      chosenDateOfMonth: [false, ''],
      dateOfMonth: [''],
      monthlyRepeatType: [''],
      endDate: [''],
      TimeZoneId: ['', Validators.compose([Validators.required])],
    });
    this.deliverDetailsForm.get('escort').setValue(false);
    const newDate = moment().format('MM/DD/YYYY');
    this.deliverDetailsForm.get('deliveryDate').setValue(newDate);
    this.deliverDetailsForm.get('deliveryStart').setValue(this.deliveryStart);
    this.deliverDetailsForm.get('deliveryEnd').setValue(this.deliveryEnd);
    this.deliverDetailsForm.get('recurrence').setValue(this.selectedValue);

    this.formControlValueChanged();
    this.setCurrentTiming();
  }

  public convertStart(deliveryDate: Date, startHours: number, startMinutes: number): Date {
    const fullYear = deliveryDate.getFullYear();
    const fullMonth = deliveryDate.getMonth();
    const date = deliveryDate.getDate();
    const deliveryNewStart = new Date(fullYear, fullMonth, date, startHours, startMinutes);
    return deliveryNewStart;
  }

  public setDefaultLocationPath(): void {
    if (this.locationList.length > 0) {
      const getChosenLocation: any = this.locationList.filter(
        (obj: any): any => obj.isDefault === true,
      );
      if (getChosenLocation) {
        this.selectedLocationId = getChosenLocation[0]?.id;
        this.defaultLocation = getChosenLocation;
      }
    }
    this.closeModalPopup();
  }

  public numberOnly(event: { which: any; keyCode: any }): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    const companies = [];
    const persons = [];
    const define = [];
    const equipments = [];
    if (this.deliverDetailsForm.invalid) {
      this.formSubmitted = false;
      return;
    }
    if (this.deliverDetailsForm.get('isAssociatedWithCraneRequest').value) {
      if (
        !this.deliverDetailsForm.get('cranePickUpLocation').value ||
        !this.deliverDetailsForm.get('craneDropOffLocation').value
      ) {
        this.formSubmitted = false;
        this.toastr.error('Please enter Picking From and Picking To');
        return;
      }
    }
    const formValue = this.deliverDetailsForm.value;
    if(formValue.EquipmentId.length<=0){
      this.toastr.error('Equipment is required');
      this.formSubmitted = false;
      return;
    }
    const newTimeZoneDetails = formValue.TimeZoneId;
    const deliveryDate = new Date(formValue.deliveryDate);
    const EndDate = new Date(formValue.endDate);
    const startNewDate = new Date(formValue.deliveryStart);
    const startHours = startNewDate.getHours();
    const startMinutes = startNewDate.getMinutes();
    const deliveryStart = this.convertStart(deliveryDate, startHours, startMinutes);
    const endNewDate = new Date(formValue.deliveryEnd);
    const endHours = endNewDate.getHours();
    const endMinutes = endNewDate.getMinutes();
    const startPicker = moment(formValue.deliveryStart).format('HH:mm');
    const endPicker = moment(formValue.deliveryEnd).format('HH:mm');
    const weekStartDate = moment(formValue.deliveryDate).format('YYYY MM DD 00:00:00');
    const weekEndDate =
      formValue.recurrence !== 'Does Not Repeat'
        ? moment(formValue.endDate).format('YYYY MM DD 00:00:00')
        : moment(formValue.deliveryDate).format('YYYY MM DD 00:00:00');
    const deliveryEnd =
      formValue.recurrence !== 'Does Not Repeat'
        ? this.convertStart(EndDate, endHours, endMinutes)
        : this.convertStart(deliveryDate, endHours, endMinutes);
    this.createDelivery({
      newNdrFormValue: formValue,
      deliveryStart,
      deliveryEnd,
      companies,
      persons,
      define,
      newTimeZoneDetails,
      startPicker,
      endPicker,
      weekStartDate,
      weekEndDate,
      equipments,
    });
  }

  public checkEquipmentType(value: any): void {
    let count = 0;
        let hasNoEquipmentOption = false;
    let hasOtherEquipment = false;
    if(value) {
      if(value.length == this.equipmentList.length -1 && this.equipmentList[0].id == 0) {
      this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
      }
      if(value.length != this.equipmentList.length && this.equipmentList[0].id != 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
      }
      if(value.length == 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
      }
      // Check if "No Equipment Needed" (id = 0) is selected
      hasNoEquipmentOption = value.some((item: any) => item.id === 0);

      // Check if other equipment is selected
      hasOtherEquipment = value.some((item: any) => item.id !== 0);

      const previousSelection = this.deliverDetailsForm.get('EquipmentId').value || [];
      const previousHasOther = previousSelection.some((item: any) => item.id !== 0);

      // Rule 1: If "No Equipment Needed" is selected and other items are selected, keep only "No Equipment Needed"
      if (hasNoEquipmentOption && hasOtherEquipment && !previousHasOther) {
        this.toastr.warning('When "No Equipment Needed" is selected, other equipment options cannot be selected.', 'Warning');
        const noEquipmentOnly = value.filter((item: any) => item.id === 0);
        this.deliverDetailsForm.get('EquipmentId').setValue(noEquipmentOnly);
        value = noEquipmentOnly;
        hasOtherEquipment = false;
      }

      // Rule 2: If other equipment is already selected and "No Equipment Needed" is now selected, remove it
      if (previousHasOther && hasNoEquipmentOption) {
        this.toastr.warning('When other equipment is selected, "No Equipment Needed" cannot be selected.', 'Warning');
        const filteredSelection = value.filter((item: any) => item.id !== 0);
        this.deliverDetailsForm.get('EquipmentId').setValue(filteredSelection);
        value = filteredSelection;
        hasNoEquipmentOption = false;
      }
    }
    for (const val of value) {
      const findEquipmentType = this.equipmentList.find((item: { id: any }) => item.id === val.id);
      if (findEquipmentType) {
        this.craneEquipmentTypeChosen = findEquipmentType.PresetEquipmentType.isCraneType;
        if (this.craneEquipmentTypeChosen) {
          this.deliverDetailsForm
            .get('isAssociatedWithCraneRequest')
            .setValue(true);
          break;
        } else {
          this.deliverDetailsForm
            .get('isAssociatedWithCraneRequest')
            .setValue(false);
        }
      }
    }

      if(count>0){
        this.getLastCraneRequestId();
      }
      if(value.length === 0){
        this.craneEquipmentTypeChosen = false;
      }
  }

  // eslint-disable-next-line max-lines-per-function
  public createDelivery(params: CreateDeliveryParams): void {
    const {
      startPicker,
      endPicker,
      weekStartDate,
      weekEndDate,
    } = params;

    const isValid = this.validateAndPrepareInputs(params);
    if (!isValid) return;

    const payload = this.buildDeliveryPayload(params);
    if (!payload) return;

    if (payload.recurrence) {
      payload.startPicker = startPicker;
      payload.endPicker = endPicker;
      payload.deliveryStart = weekStartDate;
      payload.deliveryEnd = weekEndDate;

      if (payload.startPicker === payload.endPicker) {
        this.toastr.error('Delivery Start time and End time should not be the same');
        this.formSubmitted = false;
        this.submitted = false;
        return;
      }

      if (payload.startPicker > payload.endPicker) {
        this.toastr.error('Please enter From Time lesser than To Time');
        this.formSubmitted = false;
        this.submitted = false;
        return;
      }

      if (
        payload.recurrence !== 'Does Not Repeat' &&
        new Date(payload.deliveryEnd) <= new Date(payload.deliveryStart)
      ) {
        this.toastr.error('Please enter End Date greater than Start Date');
        this.formSubmitted = false;
        this.submitted = false;
        return;
      }
    }

    this.createNDR(payload);
  }

  public buildDeliveryPayload(params: CreateDeliveryParams): any {
    const {
      newNdrFormValue,
      deliveryStart,
      deliveryEnd,
      companies,
      persons,
      define,
      equipments,
      startPicker,
      endPicker,
    } = params;

    if (this.checkStringEmptyValues(newNdrFormValue)) {
      this.formReset();
      return null;
    }

    const escortCondition = newNdrFormValue.escort === null || newNdrFormValue.escort === undefined;

    const payload: any = {
      description: newNdrFormValue.description,
      companies,
      escort: escortCondition ? false : newNdrFormValue.escort,
      ProjectId: this.ProjectId,
      userId: this.guestUserId,
      GateId: newNdrFormValue.GateId,
      notes: newNdrFormValue.notes,
      EquipmentId: equipments,
      deliveryStart,
      deliveryEnd,
      ParentCompanyId: this.ParentCompanyId,
      persons,
      define,
      isAssociatedWithCraneRequest: newNdrFormValue.isAssociatedWithCraneRequest,
      cranePickUpLocation: '',
      craneDropOffLocation: '',
      CraneRequestId: '',
      requestType: 'deliveryRequest',
      LocationId: this.selectedLocationId,
      recurrence: newNdrFormValue.recurrence,
      chosenDateOfMonth: newNdrFormValue.chosenDateOfMonth === 1,
      dateOfMonth: newNdrFormValue.dateOfMonth,
      monthlyRepeatType: newNdrFormValue.monthlyRepeatType,
      days: newNdrFormValue.days ? this.sortWeekDays(newNdrFormValue.days) : [],
      repeatEveryType: newNdrFormValue.repeatEveryType || null,
      repeatEveryCount: newNdrFormValue.repeatEveryCount?.toString() || null,
      TimeZoneId: this.timeZoneValues,
      startPicker,
      endPicker,
    };

    if (payload.isAssociatedWithCraneRequest) {
      payload.cranePickUpLocation = newNdrFormValue.cranePickUpLocation.trim();
      payload.craneDropOffLocation = newNdrFormValue.craneDropOffLocation.trim();
      payload.CraneRequestId = newNdrFormValue.CraneRequestId;
      payload.requestType = 'deliveryRequestWithCrane';
    }

    if (payload.recurrence === 'Monthly' || payload.recurrence === 'Yearly') {
      payload.dateOfMonth = this.monthlyDate;
    }

    return payload;
  }


  public validateAndPrepareInputs(params: CreateDeliveryParams): boolean {
    const {
      newNdrFormValue,
      companies,
      persons,
      define,
      newTimeZoneDetails,
      equipments,
    } = params;

    const {
      companyItems,
      person,
      defineItems,
      TimeZoneId,
      EquipmentId,
    } = newNdrFormValue;

    if (!companyItems || companyItems.length === 0) {
      this.formReset();
      this.toastr.error('Responsible Company is required');
      return false;
    }

    if (!person || person.length === 0) {
      this.formReset();
      this.toastr.error('Responsible Person is required');
      return false;
    }

    companyItems.forEach((element: { id: any }) => companies.push(element.id));
    person.forEach((element: { id: any }) => persons.push(element.id));

    if (defineItems && defineItems.length > 0) {
      defineItems.forEach((element: { id: any }) => define.push(element.id));
    }

    if (TimeZoneId && TimeZoneId.length > 0 && newTimeZoneDetails?.length > 0) {
      TimeZoneId.forEach((element: { id: any }) => {
        this.timeZoneValues = element.id;
      });
    }

    if (EquipmentId && EquipmentId.length > 0) {
      EquipmentId.forEach((element: { id: any }) => equipments.push(element.id));
    }

    return true;
  }


  public sortWeekDays(data: any[]): any {
    const order = {
      Sunday: 1,
      Monday: 2,
      Tuesday: 3,
      Wednesday: 4,
      Thursday: 5,
      Friday: 6,
      Saturday: 7,
    };

    if (data.length > 0) {
      return data.sort((a, b): any => order[a] - order[b]);
    }
  }

  public createNDR(payload: {
    description: any;
    companies: any;
    escort: any;
    ProjectId: any;
    userId: any;
    GateId: any;
    notes: any;
    EquipmentId: any;
    LocationId: any;
    deliveryStart: any;
    deliveryEnd: any;
    ParentCompanyId: any;
    persons: any;
    define: any;
    isAssociatedWithCraneRequest: any;
    cranePickUpLocation: string;
    craneDropOffLocation: string;
    CraneRequestId: string;
    requestType: string;
    recurrence: any;
    chosenDateOfMonth: any;
    dateOfMonth: any;
    monthlyRepeatType: any;
    days: any;
    repeatEveryType: any;
    repeatEveryCount: any;
  }): void {
    this.ProjectSharingServices.guestCreateNDR(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success(response.message, 'Guest Delivery Booking Created Successfully.');
          this.mixpanelService.addGuestUserMixpanelEvents('Guest Created New Delivery Booking');
          this.formReset();
          this.deliverDetailsForm.reset();
          this.resetForm('yes');
          this.NDRTimingChanged = false;
          this.isRequestToMember();
        }
      },
      error: (NDRCreateHistoryError): void => {
        this.formReset();
        this.NDRTimingChanged = false;
        if (NDRCreateHistoryError.message?.statusCode === 400) {
          this.showError(NDRCreateHistoryError);
        } else if (!NDRCreateHistoryError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else if (NDRCreateHistoryError.message.includes('overlaps')) {
          this.toastr.error(NDRCreateHistoryError.message, 'OOPS!');
        } else {
          this.toastr.error(NDRCreateHistoryError.message, 'OOPS!');
        }
      },
    });
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      this.modalRef1.hide();
      this.deliverDetailsForm.reset();
      this.setDefaultPerson();
      this.deliverDetailsForm.get('GateId').setValue('');
      this.submitted = false;
      this.formSubmitted = false;
      this.editSubmitted = false;
      this.NDRTimingChanged = false;
      this.router.navigate([window.atob(localStorage.getItem('url'))]);
    }
  }

  public close(template: TemplateRef<any>): void {
    if (
      (this.deliverDetailsForm.touched && this.deliverDetailsForm.dirty) ||
      (this.deliverDetailsForm.get('defineItems').dirty &&
        this.deliverDetailsForm.get('defineItems').value &&
        this.deliverDetailsForm.get('defineItems').value.length > 0) ||
      (this.deliverDetailsForm.get('companyItems').dirty &&
        this.deliverDetailsForm.get('companyItems').value &&
        this.deliverDetailsForm.get('companyItems').value.length > 0) ||
      (this.deliverDetailsForm.get('EquipmentId').dirty &&
        this.deliverDetailsForm.get('EquipmentId').value &&
        this.deliverDetailsForm.get('EquipmentId').value.length > 0) ||
      this.NDRTimingChanged
    ) {
      this.openConfirmationModalPopup(template);
    } else {
      this.resetForm('yes');
    }
  }

  public deliveryEndTimeChangeDetection(): void {
    this.NDRTimingChanged = true;
  }

  public openConfirmationModalPopup(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public changeDate(event: any): void {
    if (!this.modalLoader) {
      const startTime = new Date(event).getHours();
      const minutes = new Date(event).getMinutes();
      this.deliveryEnd = new Date();
      this.deliveryEnd.setHours(startTime + 1);
      this.deliveryEnd.setMinutes(minutes);
      this.deliverDetailsForm.get('deliveryEnd').setValue(this.deliveryEnd);
      this.NDRTimingChanged = true;
    }
  }

  public setDefaultPerson(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      id: this.guestUserId,
    };
    this.ProjectSharingServices.guestGetMemberRole(params).subscribe((res): void => {
      this.authUser = res.data;
      let email: string;
      if (this.authUser.User.lastName != null) {
        email = `${this.authUser.User.firstName} ${this.authUser?.User?.lastName} (${this.authUser.User.email})`;
      } else {
        email = `${this.authUser.User.firstName} (${this.authUser.User.email})`;
      }
      const newMemberList = [
        {
          email,
          id: this.authUser.id,
          readonly: true,
        },
      ];
      this.deliverDetailsForm.get('person').patchValue(newMemberList);
    });
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public formReset(): void {
    this.formSubmitted = false;
    this.submitted = false;
  }

  public checkStringEmptyValues(formValue: { description: string; notes: string }): boolean {
    if (formValue.description.trim() === '') {
      this.toastr.error('Please Enter valid Company Name.', 'OOPS!');
      return true;
    }
    if (formValue.notes) {
      if (formValue.notes.trim() === '') {
        this.toastr.error('Please Enter valid address.', 'OOPS!');
        return true;
      }
    }
    return false;
  }

  public formControlValueChanged(): void {
    this.deliverDetailsForm.get('repeatEveryType').valueChanges.subscribe((value: string): void => {
      const days = this.deliverDetailsForm.get('days');
      const chosenDateOfMonth = this.deliverDetailsForm.get('chosenDateOfMonth');
      const dateOfMonth = this.deliverDetailsForm.get('dateOfMonth');
      const monthlyRepeatType = this.deliverDetailsForm.get('monthlyRepeatType');
      if (value === 'Week' || value === 'Day' || value === 'Weeks') {
        days.setValidators([Validators.required]);
      } else {
        days.clearValidators();
      }
      if (value === 'Month' || value === 'Months' || value === 'Year' || value === 'Years') {
        if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 1) {
          dateOfMonth.setValidators([Validators.required]);
          monthlyRepeatType.clearValidators();
        } else {
          monthlyRepeatType.setValidators([Validators.required]);
          dateOfMonth.clearValidators();
        }
      } else {
        chosenDateOfMonth.clearValidators();
        dateOfMonth.clearValidators();
        monthlyRepeatType.clearValidators();
      }
      chosenDateOfMonth.updateValueAndValidity();
      dateOfMonth.updateValueAndValidity();
      monthlyRepeatType.updateValueAndValidity();
      days.updateValueAndValidity();
    });
  }

  public setCurrentTiming(): void {
    const newDate = moment().format('MM-DD-YYYY');
    const hours = moment(new Date()).format('HH');
    this.startTime = new Date();
    this.startTime.setHours(+hours);
    this.startTime.setMinutes(0);
    this.endTime = new Date();
    this.endTime.setHours(+hours);
    this.endTime.setMinutes(30);
    if (!this.deliverDetailsForm.get('endDate')?.value) {
      this.deliverDetailsForm.get('endDate').setValue(newDate);
    }
    this.changeMonthlyRecurrence();
  }

  public chooseRepeatEveryType(value): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    if (value === 'Day' || value === 'Days') {
      this.deliverDetailsForm.get('recurrence').setValue('Daily');
    }
    if (value === 'Week' || value === 'Weeks') {
      this.deliverDetailsForm.get('recurrence').setValue('Weekly');
    }
    if (value === 'Month' || value === 'Months') {
      this.deliverDetailsForm.get('recurrence').setValue('Monthly');
    }
    if (value === 'Year' || value === 'Years') {
      this.deliverDetailsForm.get('recurrence').setValue('Yearly');
    }
    if (value === 'Day' || value === 'Days') {
      this.checkform = this.deliverDetailsForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day: any): any => {
        const dayObj = day;
        dayObj.checked = true;
        dayObj.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj.value));
        return dayObj;
      });
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.showRecurrenceTypeDropdown = true;
    }
    if (value === 'Week' || value === 'Weeks') {
      this.weekDays = this.weekDays.map((day15: any): void => {
        const dayObj15 = day15;
        if (dayObj15.value === 'Monday') {
          dayObj15.checked = true;
        } else {
          dayObj15.checked = false;
        }
        dayObj15.isDisabled = false;
        return dayObj15;
      });
      this.checkform = this.deliverDetailsForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (value === 'Day' || value === 'Week' || value === 'Month' || value === 'Year') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showRecurrenceTypeDropdown = false;
    }
    if (value === 'Days' || value === 'Weeks' || value === 'Months' || value === 'Years') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.showRecurrenceTypeDropdown = false;
    }
    if (this.deliverDetailsForm.get('repeatEveryCount').value > 1) {
      this.showRecurrenceTypeDropdown = true;
      this.isRepeatWithMultipleRecurrence = false;
    }
    this.selectedRecurrence = this.deliverDetailsForm.get('recurrence').value;
    this.occurMessage();
  }

  public occurMessage(): void {
    this.message = '';
    const repeatType = this.deliverDetailsForm.get('repeatEveryType').value;
    const repeatCount = +this.deliverDetailsForm.get('repeatEveryCount').value;
    const recurrenceEndDate = this.deliverDetailsForm.get('recurrence').value
      ? ` until ${moment(this.deliverDetailsForm.get('endDate').value).format('MMMM DD, YYYY')}` : '';

    switch (repeatType) {
      case 'Day':
        this.message = 'Occurs every day';
        break;
      case 'Days':
        this.message = this.getDaysMessage(repeatCount);
        break;
      case 'Week':
        this.message = this.getWeeklyMessage();
        break;
      case 'Weeks':
        this.message = this.getWeeklyCountMessage(repeatCount);
        break;
      case 'Month':
      case 'Months':
        this.message = this.getMonthlyMessage();
        break;
      case 'Year':
      case 'Years':
        this.message = this.getYearlyMessage();
        break;
      default:
        this.message = this.getYearlyMessage();
    }

    if (this.message) {
      this.message += recurrenceEndDate;
    }
  }

  public getDaysMessage(repeatCount: number): string {
    if (repeatCount === 2) {
      return 'Occurs every other day';
    }
    return `Occurs every ${repeatCount} days`;
  }

  public getWeeklyMessage(): string {
    const weekDays = this.getCheckedDays();
    return `Occurs every ${weekDays}`;
  }

  public getWeeklyCountMessage(repeatCount: number): string {
    const weekDays = this.getCheckedDays();
    if (repeatCount === 2) {
      return `Occurs every other ${weekDays}`;
    }
    return `Occurs every ${repeatCount} weeks on ${weekDays}`;
  }

  public getMonthlyMessage(): string {
    if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 1) {
      return `Occurs on day ${this.monthlyDate}`;
    }
    if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 2) {
      return `Occurs on the ${this.monthlyDayOfWeek}`;
    }
    return `Occurs on the ${this.monthlyLastDayOfWeek}`;
  }

  public getYearlyMessage(): string {
    if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 1) {
      return `Occurs on ${moment(this.deliverDetailsForm.get('startDate').value).format('MMMM')} ${this.monthlyDate}`;
    }
    if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 2) {
      return `Occurs every year on the ${this.monthlyDayOfWeek}`;
    }
    return `Occurs every year on the ${this.monthlyLastDayOfWeek}`;
  }

  public getCheckedDays(): string {
    let weekDays = '';
    this.weekDays.forEach((dayObj: any) => {
      if (dayObj.checked) {
        weekDays = `${weekDays + dayObj.value},`;
      }
    });
    return weekDays.replace(/,\s*$/, '');
  }

  public handleRecurrenceType(value): void {
    const recurrenceValue = this.deliverDetailsForm.get('recurrence').value;
    const isSingleRecurrence = +value === 1;

    if (recurrenceValue === 'Daily' || recurrenceValue === 'Weekly' || recurrenceValue === 'Monthly' || recurrenceValue === 'Yearly') {
      this.isRepeatWithMultipleRecurrence = !isSingleRecurrence;
      this.isRepeatWithSingleRecurrence = isSingleRecurrence;
      this.showRecurrenceTypeDropdown = !isSingleRecurrence;
    }
  }

  public handleRepeatEveryType(value): void {
    const recurrenceValue = this.deliverDetailsForm.get('recurrence').value;
    const isSingleRecurrence = +value === 1;

    if (recurrenceValue === 'Daily') {
      this.deliverDetailsForm.get('repeatEveryType').setValue(isSingleRecurrence ? 'Day' : 'Days');
    } else if (recurrenceValue === 'Weekly') {
      this.deliverDetailsForm.get('repeatEveryType').setValue(isSingleRecurrence ? 'Week' : 'Weeks');
    } else if (recurrenceValue === 'Monthly') {
      this.deliverDetailsForm.get('repeatEveryType').setValue(isSingleRecurrence ? 'Month' : 'Months');
      this.changeMonthlyRecurrence();
      this.showMonthlyRecurrence();
    } else if (recurrenceValue === 'Yearly') {
      this.deliverDetailsForm.get('repeatEveryType').setValue(isSingleRecurrence ? 'Year' : 'Years');
    }
  }

  public changeRecurrenceCount(value): void {
    if (value > 0) {
      this.handleRecurrenceType(value);
      this.handleRepeatEveryType(value);
      this.occurMessage();
    } else if (value < 0) {
      this.deliverDetailsForm.get('repeatEveryCount').setValue(1);
    }
  }

  public changeMonthlyRecurrence(): void {
    this.setMonthlyOrYearlyRecurrenceOption();
    this.updateFormValidation();
    this.showMonthlyRecurrence();
    this.occurMessage();
  }

  public setMonthlyOrYearlyRecurrenceOption(): void {
    if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 1) {
      this.deliverDetailsForm
        .get('dateOfMonth')
        .setValue(moment(this.deliverDetailsForm.get('deliveryDate').value).format('DD'));
      this.deliverDetailsForm.get('monthlyRepeatType').setValue(null);
    } else if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 2) {
      this.deliverDetailsForm.get('dateOfMonth').setValue(null);
      this.deliverDetailsForm.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
    } else if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 3) {
      this.deliverDetailsForm.get('dateOfMonth').setValue(null);
      this.deliverDetailsForm.get('monthlyRepeatType').setValue(this.monthlyLastDayOfWeek);
    }
  }

  public showMonthlyRecurrence(): void {
    if (this.deliverDetailsForm.get('deliveryDate').value) {
      const startDate = moment(this.deliverDetailsForm.get('deliveryDate').value).format('YYYY-MM');
      const chosenDay = moment(this.deliverDetailsForm.get('deliveryDate').value).format('dddd');
      this.monthlyDate = moment(this.deliverDetailsForm.get('deliveryDate').value).format('DD');
      const day = moment(startDate, 'YYYY-MM').startOf('month').day(chosenDay);
      const getAllDays = [];
      if (day.date() > 7) day.add(7, 'd');
      const month = day.month();
      while (month === day.month()) {
        getAllDays.push(day.format('YYYY-MM-DD'));
        day.add(7, 'd');
      }
      let week;
      let extraOption;
      this.enableOption = false;
      getAllDays.forEach((element, i): void => {
        if (
          moment(this.deliverDetailsForm.get('deliveryDate').value).format('YYYY-MM-DD') ===
          moment(element).format('YYYY-MM-DD')
        ) {
          const number = i + 1;
          if (number === 1) {
            week = 'First';
          }
          if (number === 2) {
            week = 'Second';
          }
          if (number === 3) {
            week = 'Third';
          }
          if (number === 4) {
            this.enableOption = true;
            extraOption = 'Last';
            week = 'Fourth';
          }
          if (number === 5) {
            week = 'Last';
          }
          if (number === 6) {
            week = 'Last';
          }
        }
      });
      this.monthlyDayOfWeek = `${week} ${chosenDay}`;
      this.monthlyLastDayOfWeek = `${extraOption} ${chosenDay}`;
      if (!this.enableOption && this.deliverDetailsForm.get('chosenDateOfMonth').value === 3) {
        this.deliverDetailsForm.get('chosenDateOfMonth').setValue(2);
        this.deliverDetailsForm.get('dateOfMonth').setValue(null);
        this.deliverDetailsForm.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
      }
      this.setMonthlyOrYearlyRecurrenceOption();
      this.occurMessage();
    }
  }

  public updateFormValidation(): void {
    const chosenDateOfMonth = this.deliverDetailsForm.get('chosenDateOfMonth');
    const dateOfMonth = this.deliverDetailsForm.get('dateOfMonth');
    const monthlyRepeatType = this.deliverDetailsForm.get('monthlyRepeatType');
    if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 1) {
      dateOfMonth.setValidators([Validators.required]);
      monthlyRepeatType.clearValidators();
    } else {
      monthlyRepeatType.setValidators([Validators.required]);
      dateOfMonth.clearValidators();
    }
    chosenDateOfMonth.updateValueAndValidity();
    dateOfMonth.updateValueAndValidity();
    monthlyRepeatType.updateValueAndValidity();
  }

  public onChange(event): void {
    this.checkform = this.deliverDetailsForm.get('days') as UntypedFormArray;
    this.valueExists = this.checkform.controls.filter(
      (object): any => object.value === event.target.value,
    );
    if (event.target.checked) {
      this.checkform.push(new UntypedFormControl(event.target.value));
      this.weekDays = this.weekDays.map((day16: any): void => {
        const dayObj16 = day16;
        if (day16.value === event.target.value) {
          dayObj16.checked = true;
        }
        return dayObj16;
      });
      if (this.checkform.controls.length === 2) {
        this.weekDays = this.weekDays.map((day17: any): void => {
          const dayObj17 = day17;
          dayObj17.isDisabled = false;
          return dayObj17;
        });
      }
    } else if (this.selectedRecurrence === 'Weekly') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day18: any): void => {
            const dayObj18 = day18;
            if (dayObj18.value === event.target.value) {
              dayObj18.checked = false;
            }
            return dayObj18;
          });
        }
        if (this.checkform.controls.length === 1) {
          this.weekDays = this.weekDays.map((day19: any): void => {
            const dayObj19 = day19;
            if (dayObj19.value === this.checkform.controls[0].value) {
              dayObj19.isDisabled = true;
              dayObj19.checked = true;
            }
            return dayObj19;
          });
          return;
        }
        i += 1;
      });
    } else if (this.selectedRecurrence === 'Daily') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day): void => {
            const dayObj = day;
            if (dayObj.value === event.target.value) {
              dayObj.checked = false;
              dayObj.isDisabled = false;
            }
            return dayObj;
          });
          return;
        }
        i += 1;
      });
    }
    if (this.checkform.controls.length !== 7) {
      this.deliverDetailsForm.get('recurrence').setValue('Weekly');
      if (+this.deliverDetailsForm.get('repeatEveryCount').value === 1) {
        this.deliverDetailsForm.get('repeatEveryType').setValue('Week');
      } else {
        this.deliverDetailsForm.get('repeatEveryType').setValue('Weeks');
      }
      this.selectedRecurrence = this.deliverDetailsForm.get('recurrence').value;
    }
    if (this.checkform.controls.length === 7) {
      this.deliverDetailsForm.get('recurrence').setValue('Daily');
      if (+this.deliverDetailsForm.get('repeatEveryCount').value === 1) {
        this.deliverDetailsForm.get('repeatEveryType').setValue('Day');
      } else {
        this.deliverDetailsForm.get('repeatEveryType').setValue('Days');
      }
      this.selectedRecurrence = this.deliverDetailsForm.get('recurrence').value;
    }
    this.occurMessage();
  }

  public checkValidation(value) {
    if (value === 'Does Not Repeat') {
      this.deliverDetailsForm.get('repeatEveryType').setValue('');
    } else {
      this.deliverDetailsForm.get('repeatEveryCount').setValue(1);
    }
    if (value === 'Daily') {
      this.deliverDetailsForm.get('repeatEveryType').setValue('Day');
    }
    if (value === 'Weekly') {
      this.deliverDetailsForm.get('repeatEveryType').setValue('Week');
    }
    if (value === 'Monthly') {
      this.deliverDetailsForm.get('repeatEveryType').setValue('Month');
    }
    if (value === 'Yearly') {
      this.deliverDetailsForm.get('repeatEveryType').setValue('Year');
    }
  }

  public onRecurrenceSelect(value): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    this.selectedRecurrence = value;
    this.checkEquipmentType(value);
    if (this.deliverDetailsForm.get('repeatEveryCount').value > 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.checkform = this.deliverDetailsForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day11: any): void => {
        const dayObj11 = day11;
        dayObj11.checked = true;
        dayObj11.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj11.value));
        return dayObj11;
      });
    }
    if (this.deliverDetailsForm.get('repeatEveryCount').value > 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.weekDays = this.weekDays.map((day12: any): void => {
        const dayObj12 = day12;
        if (dayObj12.value === 'Monday') {
          dayObj12.checked = true;
        } else {
          dayObj12.checked = false;
        }
        dayObj12.isDisabled = false;
        return dayObj12;
      });
      this.checkform = this.deliverDetailsForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.deliverDetailsForm.get('repeatEveryCount').value === 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.weekDays = this.weekDays.map((day13: any): void => {
        const dayObj13 = day13;
        if (dayObj13.value === 'Monday') {
          dayObj13.checked = true;
        } else {
          dayObj13.checked = false;
        }
        dayObj13.isDisabled = false;
        return dayObj13;
      });
      this.checkform = this.deliverDetailsForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.deliverDetailsForm.get('repeatEveryCount').value === 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.checkform = this.deliverDetailsForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day14: any): void => {
        const dayObj14 = day14;
        dayObj14.checked = true;
        dayObj14.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj14.value));
        return dayObj14;
      });
    }
    if (
      this.deliverDetailsForm.get('repeatEveryCount').value === 1 &&
      (value === 'Monthly' || value === 'Yearly')
    ) {
      this.deliverDetailsForm.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showMonthlyRecurrence();
    }
    if (
      this.deliverDetailsForm.get('repeatEveryCount').value > 1 &&
      (value === 'Monthly' || value === 'Yearly')
    ) {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.deliverDetailsForm.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.showMonthlyRecurrence();
    }
    this.occurMessage();
  }

  public timeZoneSelected(id): void {
    this.selectedTimeZoneValue = this.timezoneList.find((obj: any): any => +obj.id === +id);
  }

  public locationSelected(data): void {
    const getChosenLocation = this.locationList.find((obj: any): any => +obj.id === +data.id);
    this.selectedLocationId = getChosenLocation?.id;
  }

  public getTimeZoneList(): void {
    this.ProjectSharingServices.guestGetTimeZoneList().subscribe({
      next: (response: any): void => {
        this.loader = true;
        if (response) {
          const params = {
            ProjectId: this.ProjectId,
          };
          if (params.ProjectId) {
            this.ProjectSharingServices.guestGetSingleProject(params).subscribe((projectList: any): void => {
              if (projectList) {
                this.timezoneList = response.data;
                this.dropdownSettings = {
                  singleSelection: true,
                  idField: 'id',
                  textField: 'location',
                  allowSearchFilter: true,
                  closeDropDownOnSelection: true,
                };
                this.getSelectedTimeZone = this.timezoneList.filter(
                  (obj: any): any => +obj.id === +projectList.data.TimeZoneId,
                );
                this.defaultTimeZone = this.getSelectedTimeZone;
                this.loader = false;
              }
            });
          }
        }
      },
      error: (getTimeZoneListErr): void => {
        if (getTimeZoneListErr.message?.statusCode === 400) {
          this.showError(getTimeZoneListErr);
        } else if (!getTimeZoneListErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(getTimeZoneListErr.message, 'OOPS!');
        }
      },
    });
  }

  public isRequestToMember() {
    const payload = {
      userId: this.guestUserId,
      ProjectId: this.ProjectId,
    };
    this.ProjectSharingServices.isRequestToMember(payload).subscribe({
      next: (response: any): void => {
        if (!response?.data.isRequestedToBeAMember && response?.data.status !== 'declined') {
          this.router.navigate(['/submit-book']);
        } else {
          this.router.navigate([window.atob(localStorage.getItem('url'))]);
        }
      },
      error: (error: any) => {
        console.error('Error occurred:', error);
        this.toastr.error('Try again later.!', 'Something went wrong.');
      },
    });
  }
}
