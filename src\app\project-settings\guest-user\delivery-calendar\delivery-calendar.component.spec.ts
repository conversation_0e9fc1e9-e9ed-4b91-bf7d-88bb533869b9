import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { UntypedFormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FullCalendarComponent } from '@fullcalendar/angular';
import { BsModalService } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import moment from 'moment';
import { of, throwError } from 'rxjs';

import { DeliveryCalendarComponent } from './delivery-calendar.component';
import { CalendarService } from '../../../services/profile/calendar.service';
import { ProjectService } from '../../../services/profile/project.service';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectSharingService } from '../../../services';
import { MixpanelService } from '../../../services/mixpanel.service';

// Mock FullCalendarComponent
@Component({
  selector: 'full-calendar',
  template: ''
})
class MockFullCalendarComponent {
  getApi() {
    return {
      currentData: {
        dateProfile: {
          activeRange: {
            start: '2024-03-01',
            end: '2024-03-31'
          }
        }
      },
      prev: jest.fn(),
      next: jest.fn(),
      prevYear: jest.fn(),
      nextYear: jest.fn(),
      removeAllEventSources: jest.fn(),
      addEventSource: jest.fn()
    };
  }
}

describe('DeliveryCalendarComponent', () => {
  let component: DeliveryCalendarComponent;
  let fixture: ComponentFixture<DeliveryCalendarComponent>;
  let modalServiceMock: jest.Mocked<BsModalService>;
  let calendarServiceMock: jest.Mocked<CalendarService>;
  let projectServiceMock: jest.Mocked<ProjectService>;
  let deliveryServiceMock: jest.Mocked<DeliveryService>;
  let projectSharingServiceMock: jest.Mocked<ProjectSharingService>;
  let toastrServiceMock: jest.Mocked<ToastrService>;
  let routerMock: jest.Mocked<Router>;
  let mixpanelServiceMock: jest.Mocked<MixpanelService>;
  let titleServiceMock: jest.Mocked<Title>;

  beforeEach(async () => {
    const mocks = {
      modalService: {
        show: jest.fn()
      },
      calendarService: {
        getEvents: jest.fn()
      },
      projectService: {
        getProject: jest.fn()
      },
      deliveryService: {
        updatedDeliveryId: jest.fn()
      },
      projectSharingService: {
        getCompanyList: jest.fn(),
        guestGetDefinableWork: jest.fn(),
        getLocations: jest.fn(),
        getEventNDR: jest.fn(),
        checkIsGuest: jest.fn().mockReturnValue(of({ data: { id: 1, isGuestUser: true } })),
        listAllMember: jest.fn().mockReturnValue(of({ data: [] })),
        guestGetMemberRole: jest.fn().mockReturnValue(of({})),
        guestGateList: jest.fn().mockReturnValue(of({ data: [] })),
        guestListEquipment: jest.fn().mockReturnValue(of({ data: [] })),
        guestGetNDRData: jest.fn().mockReturnValue(of({ data: {} }))
      },
      toastr: {
        success: jest.fn(),
        error: jest.fn()
      },
      router: {
        navigate: jest.fn()
      },
      mixpanel: {
        track: jest.fn()
      },
      title: {
        setTitle: jest.fn()
      }
    };

    await TestBed.configureTestingModule({
      declarations: [DeliveryCalendarComponent, MockFullCalendarComponent],
      imports: [FormsModule, ReactiveFormsModule],
      providers: [
        { provide: BsModalService, useValue: mocks.modalService },
        { provide: CalendarService, useValue: mocks.calendarService },
        { provide: ProjectService, useValue: mocks.projectService },
        { provide: DeliveryService, useValue: mocks.deliveryService },
        { provide: ProjectSharingService, useValue: mocks.projectSharingService },
        { provide: ToastrService, useValue: mocks.toastr },
        { provide: Router, useValue: mocks.router },
        { provide: MixpanelService, useValue: mocks.mixpanel },
        { provide: Title, useValue: mocks.title },
        UntypedFormBuilder
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    })
    .overrideComponent(DeliveryCalendarComponent, {
      set: {
        providers: [
          { provide: FullCalendarComponent, useClass: MockFullCalendarComponent }
        ]
      }
    })
    .compileComponents();

    modalServiceMock = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    calendarServiceMock = TestBed.inject(CalendarService) as jest.Mocked<CalendarService>;
    projectServiceMock = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    deliveryServiceMock = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    projectSharingServiceMock = TestBed.inject(ProjectSharingService) as jest.Mocked<ProjectSharingService>;
    toastrServiceMock = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    routerMock = TestBed.inject(Router) as jest.Mocked<Router>;
    mixpanelServiceMock = TestBed.inject(MixpanelService) as jest.Mocked<MixpanelService>;
    titleServiceMock = TestBed.inject(Title) as jest.Mocked<Title>;

    // Mock localStorage
    Storage.prototype.getItem = jest.fn((key) => {
      const mockData: { [key: string]: string } = {
        'guestId': btoa('1'),
        'guestEmail': btoa('<EMAIL>'),
        'guestProjectId': btoa('123'),
        'guestParentCompanyId': btoa('456')
      };
      return mockData[key];
    });
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DeliveryCalendarComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct title', () => {
    expect(titleServiceMock.setTitle).toHaveBeenCalledWith('Follo - Guest Delivery Calendar');
  });


  it('should initialize filter form', () => {
    expect(component.filterForm).toBeTruthy();
    expect(component.filterForm.get('companyFilter')).toBeTruthy();
    expect(component.filterForm.get('descriptionFilter')).toBeTruthy();
    expect(component.filterForm.get('statusFilter')).toBeTruthy();
  });

  it('should handle search input', () => {
    const searchValue = 'test search';
    component.getSearchNDR(searchValue);
    expect(component.search).toBe(searchValue);
    expect(component.showSearchbar).toBe(true);
  });

  it('should clear search', () => {
    component.search = 'test';
    component.showSearchbar = true;
    component.clear();
    expect(component.search).toBe('');
    expect(component.showSearchbar).toBe(false);
  });



  it('should handle calendar event click', () => {
    const mockEvent = {
      event: {
        title: 'Test Event',
        extendedProps: {
          uniqueNumber: '123'
        }
      }
    };
    component.events = [{
      description: 'Test Event',
      uniqueNumber: '123'
    }];
    component.deliveryList = [{
      description: 'Test Event',
      uniqueNumber: '123',
      repeatEveryType: 'Day'
    }];

    component.calendarDescription(mockEvent);
    expect(component.calendarDescriptionPopup).toBe(true);
    expect(component.viewEventData).toBeTruthy();
  });

  it('should format occurrence message for daily events', () => {
    const mockData = {
      repeatEveryType: 'Day',
      endTime: '2024-03-31'
    };
    component.occurMessage(mockData);
    expect(component.message).toBe('Occurs every day until 03-31-2024');
  });

  it('should format occurrence message for weekly events', () => {
    const mockData = {
      repeatEveryType: 'Week',
      days: ['Monday', 'Wednesday', 'Friday'],
      endTime: '2024-03-31'
    };
    component.occurMessage(mockData);
    expect(component.message).toBe('Occurs every Monday,Wednesday,Friday until 03-31-2024');
  });

  it('should open filter modal', () => {
    const mockTemplate = {} as any;
    component.openFilterModal(mockTemplate);
    expect(modalServiceMock.show).toHaveBeenCalled();
  });

  // Additional positive test cases
  describe('Calendar Navigation', () => {
    beforeEach(() => {
      component.calendarApi = {
        next: jest.fn(),
        prev: jest.fn(),
        prevYear: jest.fn(),
        nextYear: jest.fn(),
        changeView: jest.fn(),
        removeAllEventSources: jest.fn(),
        addEventSource: jest.fn(),
        currentData: {
          dateProfile: {
            activeRange: {
              start: '2024-03-01',
              end: '2024-03-31'
            }
          }
        }
      };
      jest.spyOn(component, 'setCalendar').mockImplementation(() => {});
    });

    it('should navigate to next period', () => {
      component.goNext();
      expect(component.calendarApi.next).toHaveBeenCalled();
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should navigate to previous period', () => {
      component.goPrev();
      expect(component.calendarApi.prev).toHaveBeenCalled();
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should navigate to previous year', () => {
      component.goPrevYear();
      expect(component.calendarApi.prevYear).toHaveBeenCalled();
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should navigate to next year', () => {
      component.goNextYear();
      expect(component.calendarApi.nextYear).toHaveBeenCalled();
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should change to week view', () => {
      jest.spyOn(component, 'closeDescription').mockImplementation(() => {});
      component.goTimeGridWeekOrDay('timeGridWeek');
      expect(component.currentView).toBe('Week');
      expect(component.calendarApi.changeView).toHaveBeenCalledWith('timeGridWeek');
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should change to day view', () => {
      jest.spyOn(component, 'closeDescription').mockImplementation(() => {});
      component.goTimeGridWeekOrDay('timeGridDay');
      expect(component.currentView).toBe('Day');
      expect(component.calendarApi.changeView).toHaveBeenCalledWith('timeGridDay');
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should change to month view', () => {
      jest.spyOn(component, 'closeDescription').mockImplementation(() => {});
      component.goDayGridMonth();
      expect(component.currentView).toBe('Month');
      expect(component.calendarApi.changeView).toHaveBeenCalledWith('dayGridMonth');
      expect(component.setCalendar).toHaveBeenCalled();
    });
  });

  describe('Search Functionality', () => {
    it('should handle empty search input', () => {
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});
      component.getSearchNDR('');
      expect(component.search).toBe('');
      expect(component.showSearchbar).toBe(false);
      expect(component.getEventNDR).toHaveBeenCalled();
    });

    it('should handle non-empty search input', () => {
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});
      const searchValue = 'test delivery';
      component.getSearchNDR(searchValue);
      expect(component.search).toBe(searchValue);
      expect(component.showSearchbar).toBe(true);
      expect(component.getEventNDR).toHaveBeenCalled();
    });

    it('should clear search and reset state', () => {
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});
      component.search = 'existing search';
      component.showSearchbar = true;
      component.clear();
      expect(component.search).toBe('');
      expect(component.showSearchbar).toBe(false);
      expect(component.getEventNDR).toHaveBeenCalled();
    });
  });

  describe('Filter Functionality', () => {
    beforeEach(() => {
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});
    });

    it('should submit filter with description', () => {
      component.filterForm.patchValue({ descriptionFilter: 'test description' });
      component.filterSubmit();
      expect(component.filterCount).toBe(1);
      expect(component.getEventNDR).toHaveBeenCalled();
      expect(modalServiceMock.show).toHaveBeenCalled();
    });

    it('should submit filter with multiple criteria', () => {
      component.filterForm.patchValue({
        descriptionFilter: 'test',
        companyFilter: '1',
        statusFilter: 'Approved',
        dateFilter: '2024-03-15'
      });
      component.filterSubmit();
      expect(component.filterCount).toBe(4);
      expect(component.getEventNDR).toHaveBeenCalled();
    });

    it('should reset filter form', () => {
      component.filterForm.patchValue({
        descriptionFilter: 'test',
        companyFilter: '1'
      });
      component.filterCount = 2;
      component.search = 'test search';

      jest.spyOn(component.filterForm, 'reset');
      jest.spyOn(component, 'filterDetailsForm').mockImplementation(() => {});

      component.resetFilter();

      expect(component.filterCount).toBe(0);
      expect(component.search).toBe('');
      expect(component.filterForm.reset).toHaveBeenCalled();
      expect(component.filterDetailsForm).toHaveBeenCalled();
      expect(component.getEventNDR).toHaveBeenCalled();
      expect(modalServiceMock.show).toHaveBeenCalled();
    });
  });

  describe('Event Handling', () => {
    beforeEach(() => {
      component.deliveryList = [
        {
          id: 1,
          uniqueNumber: '123',
          description: 'Test Event',
          requestType: 'deliveryRequest',
          deliveryStart: '2024-03-15T10:00:00',
          deliveryEnd: '2024-03-15T12:00:00',
          status: 'Approved'
        },
        {
          id: 2,
          uniqueNumber: '456',
          description: 'Calendar Event',
          requestType: 'calendarEvent',
          fromDate: '2024-03-16T09:00:00',
          toDate: '2024-03-16T11:00:00'
        }
      ];
    });

    it('should handle delivery request event click', () => {
      const mockEvent = {
        event: {
          id: '1',
          title: 'Test Event',
          extendedProps: {
            uniqueNumber: '123',
            isGuestRequest: true,
            creatorId: 1,
            guestMemberId: 1,
            requestType: 'deliveryRequest'
          }
        }
      };

      component.isGuestUser = true;
      jest.spyOn(component, 'getDateDetails').mockImplementation(() => {});

      component.deliveryDescription(mockEvent);

      expect(component.eventData).toEqual(component.deliveryList[0]);
      expect(component.calendarCurrentDeliveryIndex).toBe(0);
      expect(component.getDateDetails).toHaveBeenCalled();
    });

    it('should handle calendar event click', () => {
      const mockEvent = {
        event: {
          id: '2',
          title: 'Calendar Event',
          extendedProps: {
            uniqueNumber: '456',
            requestType: 'calendarEvent'
          }
        }
      };

      jest.spyOn(component, 'calendarDescription').mockImplementation(() => {});

      component.deliveryDescription(mockEvent);

      expect(component.eventData).toEqual(component.deliveryList[1]);
      expect(component.calendarDescription).toHaveBeenCalled();
    });

    it('should not handle event for non-guest user on guest request', () => {
      const mockEvent = {
        event: {
          id: '1',
          extendedProps: {
            isGuestRequest: true,
            creatorId: 2,
            guestMemberId: 1,
            requestType: 'deliveryRequest'
          }
        }
      };

      component.isGuestUser = true;
      component.deliveryDescription(mockEvent);

      // Should return early without processing
      expect(component.eventData).toEqual([]);
    });

    it('should close calendar description', () => {
      component.calendarDescriptionPopup = true;
      component.descriptionPopup = true;
      component.allRequestIsOpened = true;
      component.viewEventData = 'test data';

      component.closeCalendarDescription();

      expect(component.calendarDescriptionPopup).toBe(false);
      expect(component.descriptionPopup).toBe(false);
      expect(component.allRequestIsOpened).toBe(false);
      expect(component.viewEventData).toBe('');
    });

    it('should close description popup', () => {
      component.descriptionPopup = true;
      component.closeDescription();
      expect(component.descriptionPopup).toBe(false);
    });
  });

  describe('Occurrence Message Formatting', () => {
    it('should format daily occurrence message', () => {
      const data = { repeatEveryType: 'Day', endTime: '2024-03-31' };
      component.occurMessage(data);
      expect(component.message).toBe('Occurs every day until 03-31-2024');
    });

    it('should format every other day occurrence message', () => {
      const data = { repeatEveryType: 'Days', repeatEveryCount: 2, endTime: '2024-03-31' };
      component.occurMessage(data);
      expect(component.message).toBe('Occurs every other day until 03-31-2024');
    });

    it('should format multiple days occurrence message', () => {
      const data = { repeatEveryType: 'Days', repeatEveryCount: 3, endTime: '2024-03-31' };
      component.occurMessage(data);
      expect(component.message).toBe('Occurs every 3 days until 03-31-2024');
    });

    it('should format weekly occurrence message', () => {
      const data = {
        repeatEveryType: 'Week',
        days: ['Monday', 'Wednesday', 'Friday'],
        endTime: '2024-03-31'
      };
      component.occurMessage(data);
      expect(component.message).toBe('Occurs every Monday,Wednesday,Friday until 03-31-2024');
    });

    it('should format every other week occurrence message', () => {
      const data = {
        repeatEveryType: 'Weeks',
        repeatEveryCount: 2,
        days: ['Monday', 'Friday'],
        endTime: '2024-03-31'
      };
      component.occurMessage(data);
      expect(component.message).toBe('Occurs every other  Monday,Friday until 03-31-2024');
    });

    it('should format multiple weeks occurrence message', () => {
      const data = {
        repeatEveryType: 'Weeks',
        repeatEveryCount: 3,
        days: ['Tuesday'],
        endTime: '2024-03-31'
      };
      component.occurMessage(data);
      expect(component.message).toBe('Occurs every 3 weeks on Tuesday until 03-31-2024');
    });

    it('should format monthly occurrence with chosen date', () => {
      const data = {
        repeatEveryType: 'Month',
        chosenDateOfMonth: true,
        dateOfMonth: 15,
        endTime: '2024-03-31'
      };
      component.occurMessage(data);
      expect(component.message).toBe('Occurs on day 15 until 03-31-2024');
    });

    it('should format monthly occurrence with repeat type', () => {
      const data = {
        repeatEveryType: 'Month',
        chosenDateOfMonth: false,
        monthlyRepeatType: 'first Monday',
        endTime: '2024-03-31'
      };
      component.occurMessage(data);
      expect(component.message).toBe('Occurs on the first Monday until 03-31-2024');
    });

    it('should handle occurMessage with missing days array for Week', () => {
      const data = {
        repeatEveryType: 'Week',
        endTime: '2024-03-31'
      };
      component.occurMessage(data);
      expect(component.message).toBe('Occurs every until 03-31-2024');
    });

    it('should handle occurMessage with missing days array for Weeks', () => {
      const data = {
        repeatEveryType: 'Weeks',
        repeatEveryCount: 2,
        endTime: '2024-03-31'
      };
      component.occurMessage(data);
      expect(component.message).toBe('Occurs every other  until 03-31-2024');
    });

    it('should handle occurMessage with missing repeatEveryCount for Days', () => {
      const data = {
        repeatEveryType: 'Days',
        endTime: '2024-03-31'
      };
      component.occurMessage(data);
      expect(component.message).toBe('Occurs every undefined days until 03-31-2024');
    });

    it('should handle occurMessage with missing repeatEveryCount for Weeks', () => {
      const data = {
        repeatEveryType: 'Weeks',
        days: ['Monday'],
        endTime: '2024-03-31'
      };
      component.occurMessage(data);
      expect(component.message).toBe('Occurs every undefined weeks on Monday until 03-31-2024');
    });

    it('should handle occurMessage with missing endTime', () => {
      const data = {
        repeatEveryType: 'Day'
      };
      component.occurMessage(data);
      expect(component.message).toBe('Occurs every day until Invalid date');
    });

    it('should handle occurMessage with missing chosenDateOfMonth and monthlyRepeatType', () => {
      const data = {
        repeatEveryType: 'Month',
        endTime: '2024-03-31'
      };
      component.occurMessage(data);
      expect(component.message).toBe('Occurs on the undefined until 03-31-2024');
    });

    it('should handle occurMessage with unknown repeatEveryType', () => {
      const data = {
        repeatEveryType: 'Unknown',
        endTime: '2024-03-31'
      };
      component.occurMessage(data);
      expect(component.message).toBe(' until 03-31-2024');
    });
  });

  describe('Negative Test Cases', () => {
    it('should handle checkGuest error', () => {
      const errorResponse = { error: 'Network error' };
      projectSharingServiceMock.checkIsGuest.mockReturnValue(throwError(() => errorResponse));

      jest.spyOn(console, 'error').mockImplementation(() => {});

      component.checkGuest();

      expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
      expect(console.error).toHaveBeenCalledWith('Error occurred:', errorResponse);
    });

    it('should handle checkGuest with no data', () => {
      projectSharingServiceMock.checkIsGuest.mockReturnValue(of({ data: null }));

      component.checkGuest();

      expect(component.isGuestUser).toBe(false);
    });

    it('should handle empty search gracefully', () => {
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});

      component.getSearchNDR('');

      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
    });

    it('should handle buildGetEventNdrPayload with no filterForm', () => {
      component.filterForm = null;

      const result = component.buildGetEventNdrPayload();

      expect(result).toEqual({});
    });

    it('should handle deliveryDescription with empty event object', () => {
      component.deliveryDescription({});

      expect(component.eventData).toEqual([]);
    });

    it('should handle calendarDescription with empty event object', () => {
      component.calendarDescription({});

      expect(component.calendarDescriptionPopup).toBe(false);
      expect(component.viewEventData).toBe('');
    });
  });

  describe('Utility Functions', () => {
    it('should format date correctly', () => {
      const testDate = '2024-03-15T10:00:00';
      const result = component.changeFormat(testDate);
      expect(result).toBe('Fri 03/15/2024');
    });

    it('should handle null date in changeFormat', () => {
      const result = component.changeFormat(null);
      expect(result).toBeUndefined();
    });

    it('should get responsible people initials with first and last name', () => {
      const user = { firstName: 'John', lastName: 'Doe' };
      const result = component.getResponsiblePeople(user);
      expect(result).toBe('JD');
    });

    it('should return default initials when no name provided', () => {
      const user = {};
      const result = component.getResponsiblePeople(user);
      expect(result).toBe('UU');
    });

    it('should handle null user object', () => {
      const result = component.getResponsiblePeople(null);
      expect(result).toBe('UU');
    });
  });

  describe('Keyboard Event Handling', () => {
    it('should handle Enter key for clear action', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'clear').mockImplementation(() => {});

      component.handleDownKeydown(event, '', '', 'clear');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.clear).toHaveBeenCalled();
    });

    it('should handle Space key for openFilter action', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      const mockData = {};
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'openFilterModal').mockImplementation(() => {});

      component.handleDownKeydown(event, mockData, '', 'openFilter');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.openFilterModal).toHaveBeenCalledWith(mockData);
    });

    it('should handle Enter key for open action', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      const mockData = {};
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'openIdModal').mockImplementation(() => {});

      component.handleDownKeydown(event, mockData, '', 'open');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.openIdModal).toHaveBeenCalledWith(mockData);
    });

    it('should handle Enter key for close action', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'closeCalendarDescription').mockImplementation(() => {});

      component.handleDownKeydown(event, '', '', 'close');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.closeCalendarDescription).toHaveBeenCalled();
    });

    it('should handle Enter key for edit action', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      const mockData = {};
      const mockItem = {};
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'openEditModal').mockImplementation(() => {});

      component.handleDownKeydown(event, mockData, mockItem, 'edit');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.openEditModal).toHaveBeenCalledWith(mockData, mockItem);
    });

    it('should not handle unsupported key', () => {
      const event = new KeyboardEvent('keydown', { key: 'Tab' });
      jest.spyOn(event, 'preventDefault');

      component.handleDownKeydown(event, '', '', 'clear');

      expect(event.preventDefault).not.toHaveBeenCalled();
    });

    it('should not handle unsupported action type', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');

      component.handleDownKeydown(event, '', '', 'unsupported');

      expect(event.preventDefault).toHaveBeenCalled();
      // Should not throw error
    });
  });

  describe('Data Processing and Value Extraction', () => {
    it('should get value by label for Description', () => {
      const element = { description: 'Test Description' };
      const result = component.getValueByLabel('Description', element);
      expect(result).toBe('Test Description');
    });

    it('should get value by label for Responsible Company', () => {
      const element = {
        companyDetails: [{ Company: { companyName: 'Test Company' } }]
      };
      const result = component.getValueByLabel('Responsible Company', element);
      expect(result).toBe('Test Company');
    });

    it('should get value by label for Responsible Person', () => {
      const element = {
        memberDetails: [{ Member: { User: { firstName: 'John', lastName: 'Doe' } } }]
      };
      const result = component.getValueByLabel('Responsible Person', element);
      expect(result).toBe('John Doe');
    });

    it('should get value by label for Gate', () => {
      const element = {
        gateDetails: [{ Gate: { gateName: 'Gate A' } }]
      };
      const result = component.getValueByLabel('Gate', element);
      expect(result).toBe('Gate A');
    });

    it('should get value by label for Delivery ID', () => {
      const element = { DeliveryId: 'DEL123' };
      const result = component.getValueByLabel('Delivery ID', element);
      expect(result).toBe('DEL123');
    });

    it('should get value by label for Definable Feature Of Work', () => {
      const element = {
        defineWorkDetails: [{ DeliverDefineWork: { DFOW: 'Test Work' } }]
      };
      const result = component.getValueByLabel('Definable Feature Of Work', element);
      expect(result).toBe('Test Work');
    });

    it('should get value by label for Equipment', () => {
      const element = {
        equipmentDetails: [{ Equipment: { equipmentName: 'Crane' } }]
      };
      const result = component.getValueByLabel('Equipment', element);
      expect(result).toBe('Crane');
    });

    it('should return empty string for unknown label', () => {
      const element = {};
      const result = component.getValueByLabel('Unknown Label', element);
      expect(result).toBe('');
    });

    it('should handle missing nested properties gracefully', () => {
      const element = { companyDetails: [] };
      const result = component.getValueByLabel('Responsible Company', element);
      expect(result).toBe('');
    });

    it('should handle undefined element in getValueByLabel', () => {
      const result = component.getValueByLabel('Description', undefined);
      expect(result).toBe('');
    });

    it('should handle null element in getValueByLabel', () => {
      const result = component.getValueByLabel('Description', null);
      expect(result).toBe('');
    });

    it('should handle missing nested properties for Responsible Person', () => {
      const element = { memberDetails: [{}] };
      const result = component.getValueByLabel('Responsible Person', element);
      expect(result).toBe('');
    });

    it('should handle missing nested properties for Gate', () => {
      const element = { gateDetails: [{}] };
      const result = component.getValueByLabel('Gate', element);
      expect(result).toBe('');
    });

    it('should handle missing nested properties for Definable Feature Of Work', () => {
      const element = { defineWorkDetails: [{}] };
      const result = component.getValueByLabel('Definable Feature Of Work', element);
      expect(result).toBe('');
    });

    it('should handle missing nested properties for Equipment', () => {
      const element = { equipmentDetails: [{}] };
      const result = component.getValueByLabel('Equipment', element);
      expect(result).toBe('');
    });
  });

  describe('Series Options and Request Collapse', () => {
    it('should initialize series options', () => {
      component.initializeSeriesOption();

      expect(component.seriesOptions).toHaveLength(2);
      expect(component.seriesOptions[0]).toEqual({
        option: 1,
        text: 'This event',
        disabled: false
      });
      expect(component.seriesOptions[1]).toEqual({
        option: 2,
        text: 'This and all following events',
        disabled: false
      });
    });

    it('should handle request collapse for future events', () => {
      const futureDate = moment().add(1, 'day').format();
      const data = { deliveryStart: futureDate };

      jest.spyOn(component, 'initializeSeriesOption').mockImplementation(() => {
        component.seriesOptions = [
          { option: 1, text: 'This event', disabled: false },
          { option: 2, text: 'This and all following events', disabled: false }
        ];
      });

      component.changeRequestCollapse(data);

      expect(component.allRequestIsOpened).toBe(true);
      expect(component.seriesOptions[1].disabled).toBe(false);
    });

    it('should handle request collapse for past events', () => {
      const pastDate = moment().subtract(1, 'day').format();
      const data = { deliveryStart: pastDate };

      jest.spyOn(component, 'initializeSeriesOption').mockImplementation(() => {
        component.seriesOptions = [
          { option: 1, text: 'This event', disabled: false },
          { option: 2, text: 'This and all following events', disabled: false }
        ];
      });

      component.changeRequestCollapse(data);

      expect(component.allRequestIsOpened).toBe(true);
      expect(component.seriesOptions[1].disabled).toBe(true);
    });
  });

  describe('Modal Operations', () => {
    it('should open ID modal with correct configuration', () => {
      const mockItem = { id: 1, description: 'Test Item' };
      component.isGuestUser = true;
      component.ParentCompanyId = 123;

      component.openIdModal(mockItem);

      expect(modalServiceMock.show).toHaveBeenCalledWith(
        expect.any(Function), // GuestDeliveryDetailsComponent
        expect.objectContaining({
          backdrop: 'static',
          keyboard: false,
          class: 'modal-lg new-delivery-popup custom-modal',
          initialState: expect.objectContaining({
            data: expect.objectContaining({
              id: 1,
              description: 'Test Item',
              isGuestUser: true,
              ParentCompanyId: 123
            })
          })
        })
      );
    });

    it('should handle openIdModal with missing properties', () => {
      const mockItem = {};
      component.isGuestUser = false;
      component.ParentCompanyId = undefined;
      expect(() => component.openIdModal(mockItem)).not.toThrow();
      expect(modalServiceMock.show).toHaveBeenCalled();
    });

    it('should handle openEditModal with missing properties', () => {
      const mockItem = {};
      const mockAction = undefined;
      component.modalRef = undefined;
      jest.spyOn(component, 'closeDescription').mockImplementation(() => {});
      jest.spyOn(component, 'close').mockImplementation(() => {});
      expect(() => component.openEditModal(mockItem, mockAction)).not.toThrow();
      expect(deliveryServiceMock.updatedDeliveryId).toHaveBeenCalled();
      expect(modalServiceMock.show).toHaveBeenCalled();
    });

    it('should handle openEditModal with modalRef as null', () => {
      const mockItem = { id: 1 };
      const mockAction = 'edit';
      component.modalRef = null;
      jest.spyOn(component, 'closeDescription').mockImplementation(() => {});
      jest.spyOn(component, 'close').mockImplementation(() => {});
      expect(() => component.openEditModal(mockItem, mockAction)).not.toThrow();
      expect(deliveryServiceMock.updatedDeliveryId).toHaveBeenCalledWith(1);
      expect(modalServiceMock.show).toHaveBeenCalled();
    });

    it('should close modal and reset state', () => {
      component.submitted = true;
      component.formSubmitted = true;
      component.modalRef = { hide: jest.fn() } as any;

      component.close();

      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should handle close when modalRef is null', () => {
      component.modalRef = null;
      component.submitted = true;
      component.formSubmitted = true;

      expect(() => component.close()).not.toThrow();
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
    });

    it('should handle close when modalRef.hide throws error', () => {
      component.modalRef = { hide: jest.fn(() => { throw new Error('Hide error'); }) } as any;
      component.submitted = true;
      component.formSubmitted = true;
      expect(() => component.close()).not.toThrow();
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
    });

    it('should handle getDateDetails with empty deliveryList', () => {
      component.deliveryList = [];
      expect(() => component.getDateDetails({})).not.toThrow();
    });

    it('should handle getDateDetails with missing deliveryStart and deliveryEnd', () => {
      component.deliveryList = [{}];
      component.calendarCurrentDeliveryIndex = 0;
      expect(() => component.getDateDetails({})).not.toThrow();
    });

    it('should handle getNDR with missing event.id and ParentCompanyId', () => {
      const data = { event: {} };
      component.ParentCompanyId = undefined;
      expect(() => component.getNDR(data)).not.toThrow();
    });

    it('should handle getNDR with null API response', () => {
      const data = { event: { id: 1 } };
      component.ParentCompanyId = 1;
      projectSharingServiceMock.guestGetNDRData.mockReturnValue(of(null));
      expect(() => component.getNDR(data)).not.toThrow();
    });
  });

  describe('AfterViewInit Lifecycle', () => {
    it('should call required methods on ngAfterViewInit', () => {
      jest.spyOn(component, 'setCalendar').mockImplementation(() => {});
      jest.spyOn(component, 'getMembers').mockImplementation(() => {});

      component.ProjectId = 123;
      component.ParentCompanyId = 456;
      component.guestUserId = 789;

      component.ngAfterViewInit();

      expect(component.setCalendar).toHaveBeenCalled();
      expect(component.getMembers).toHaveBeenCalled();
      expect(projectSharingServiceMock.guestGetMemberRole).toHaveBeenCalledWith({
        ProjectId: 123,
        ParentCompanyId: 456,
        id: 789
      });
    });
  });

  describe('Form, Filter, and Data Processing Coverage', () => {
    it('should initialize filterDetailsForm with all controls', () => {
      component.filterDetailsForm();
      expect(component.filterForm.get('companyFilter')).toBeTruthy();
      expect(component.filterForm.get('descriptionFilter')).toBeTruthy();
      expect(component.filterForm.get('statusFilter')).toBeTruthy();
      expect(component.filterForm.get('memberFilter')).toBeTruthy();
      expect(component.filterForm.get('gateFilter')).toBeTruthy();
      expect(component.filterForm.get('equipmentFilter')).toBeTruthy();
      expect(component.filterForm.get('locationFilter')).toBeTruthy();
      expect(component.filterForm.get('dateFilter')).toBeTruthy();
      expect(component.filterForm.get('pickFrom')).toBeTruthy();
      expect(component.filterForm.get('pickTo')).toBeTruthy();
    });

    it('should call ngAfterViewInit with and without calendarComponent1', () => {
      component.calendarComponent1 = {
        getApi: jest.fn().mockReturnValue({
          currentData: { dateProfile: { activeRange: { start: '2024-01-01', end: '2024-01-31' } } },
          removeAllEventSources: jest.fn(),
          addEventSource: jest.fn()
        })
      } as any;
      component.ProjectId = 1;
      component.ParentCompanyId = 2;
      component.guestUserId = 3;
      jest.spyOn(component, 'setCalendar').mockImplementation(() => {});
      jest.spyOn(component, 'getMembers').mockImplementation(() => {});
      component.ngAfterViewInit();
      expect(component.setCalendar).toHaveBeenCalled();
      expect(component.getMembers).toHaveBeenCalled();
      // Now test with calendarComponent1 undefined
      component.calendarComponent1 = undefined;
      expect(() => component.ngAfterViewInit()).not.toThrow();
    });

    it('should call setCalendar with and without calendarComponent1', () => {
      component.calendarComponent1 = {
        getApi: jest.fn().mockReturnValue({
          currentData: { dateProfile: { activeRange: { start: '2024-01-01', end: '2024-01-31' } } },
          removeAllEventSources: jest.fn(),
          addEventSource: jest.fn()
        })
      } as any;
      expect(() => component.setCalendar()).not.toThrow();
      component.calendarComponent1 = undefined;
      expect(() => component.setCalendar()).not.toThrow();
    });

    it('should call filterSubmit for all filter fields and handle modalRef undefined', () => {
      component.filterForm.patchValue({
        companyFilter: '1',
        descriptionFilter: 'desc',
        statusFilter: 'Approved',
        memberFilter: '2',
        gateFilter: '3',
        equipmentFilter: '4',
        locationFilter: '5',
        dateFilter: '2024-01-01',
        pickFrom: '2024-01-01',
        pickTo: '2024-01-02'
      });
      component.modalRef = undefined;
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});
      expect(() => component.filterSubmit()).not.toThrow();
      expect(component.filterCount).toBeGreaterThan(0);
    });

    it('should call filterSubmit and handle modalRef.hide throwing', () => {
      component.filterForm.patchValue({ descriptionFilter: 'desc' });
      component.modalRef = { hide: jest.fn(() => { throw new Error('hide error'); }) } as any;
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});
      expect(() => component.filterSubmit()).not.toThrow();
    });

    it('should call buildGetEventNdrPayload with and without filterForm', () => {
      component.filterForm = undefined;
      expect(component.buildGetEventNdrPayload()).toEqual({});
      component.filterDetailsForm();
      component.filterForm.patchValue({ dateFilter: '2024-01-01' });
      component.Range = { start: '2024-01-01', end: '2024-01-31' };
      component.filterCount = 2;
      component.currentView = 'Day';
      component.id = 1;
      component.ParentCompanyId = 2;
      component.search = 'search';
      const payload = component.buildGetEventNdrPayload();
      expect(payload).not.toEqual({});
      if (payload && typeof payload === 'object') {
        const p = payload as any;
        expect(p.dateFilter).toBe('2024-01-01');
        expect(p.filterCount).toBe(2);
        expect(p.calendarView).toBe('Day');
      }
    });

    it('should call getMembers with and without response.data and error', () => {
      projectSharingServiceMock.listAllMember.mockReturnValueOnce(of({ data: [{ id: 1 }] }));
      component.ProjectId = 1;
      component.ParentCompanyId = 2;
      expect(() => component.getMembers()).not.toThrow();
      projectSharingServiceMock.listAllMember.mockReturnValueOnce(of({}));
      expect(() => component.getMembers()).not.toThrow();
      projectSharingServiceMock.listAllMember.mockReturnValueOnce(throwError(() => new Error('fail')));
      expect(() => component.getMembers()).not.toThrow();
    });

    it('should call clear with and without showSearchbar set', () => {
      component.showSearchbar = true;
      component.search = 'test';
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});
      expect(() => component.clear()).not.toThrow();
      component.showSearchbar = false;
      expect(() => component.clear()).not.toThrow();
    });

    it('should call getSearchNDR with empty and non-empty data', () => {
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});
      expect(() => component.getSearchNDR('')).not.toThrow();
      expect(() => component.getSearchNDR('search')).not.toThrow();
    });

    it('should call resetFilter with and without modalRef, and modalRef.hide throwing', () => {
      component.modalRef = { hide: jest.fn() } as any;
      jest.spyOn(component, 'filterDetailsForm').mockImplementation(() => {});
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});
      expect(() => component.resetFilter()).not.toThrow();
      component.modalRef = undefined;
      expect(() => component.resetFilter()).not.toThrow();
      component.modalRef = { hide: jest.fn(() => { throw new Error('hide error'); }) } as any;
      expect(() => component.resetFilter()).not.toThrow();
    });

    it('should call getDeliveryListDetails for all requestType/status combinations', () => {
      component.deliveryList = [
        { requestType: 'calendarEvent', isAllDay: true, status: 'Delivered', description: 'desc', id: 1, uniqueNumber: 'u1', deliveryStart: '2024-01-01', deliveryEnd: '2024-01-01', createdUserDetails: { id: 1 } },
        { requestType: 'deliveryRequest', status: 'Pending', id: 2, uniqueNumber: 'u2', deliveryStart: '2024-01-01', deliveryEnd: '2024-01-01', createdUserDetails: { id: 2 } },
        { requestType: 'deliveryRequestWithCrane', status: 'Approved', id: 3, uniqueNumber: 'u3', deliveryStart: '2024-01-01', deliveryEnd: '2024-01-01', createdUserDetails: { id: 3 } },
        { requestType: 'calendarEvent', status: undefined, id: 4, uniqueNumber: 'u4', deliveryStart: '2024-01-01', deliveryEnd: '2024-01-01', createdUserDetails: { id: 4 } }
      ];
      const approved = { backgroundColor: 'green', fontColor: 'white', status: 'approved' };
      const pending = { backgroundColor: 'yellow', fontColor: 'black', status: 'pending' };
      const rejected = { backgroundColor: 'red', fontColor: 'white', status: 'rejected' };
      const expired = { backgroundColor: 'gray', fontColor: 'white', status: 'expired' };
      const delivered = { backgroundColor: 'blue', fontColor: 'white', status: 'delivered' };
      const cardData = [
        { label: 'Description', line: 1, selected: true },
        { label: 'Responsible Company', line: 2, selected: true }
      ];
      expect(() => component.getDeliveryListDetails(approved, pending, rejected, expired, delivered, cardData)).not.toThrow();
    });

    it('should call getAssignedPayload for all requestType values and missing properties', () => {
      const element1 = { requestType: 'deliveryRequest', deliveryStart: '2024-01-01', deliveryEnd: '2024-01-01', id: 1, uniqueNumber: 'u1', isCreatedByGuestUser: true, createdUserDetails: { id: 1 } };
      const element2 = { requestType: 'calendarEvent', fromDate: '2024-01-01', toDate: '2024-01-01', id: 2, uniqueNumber: 'u2', isCreatedByGuestUser: false, createdUserDetails: {} };
      expect(component.getAssignedPayload(element1)).toBeTruthy();
      expect(component.getAssignedPayload(element2)).toBeTruthy();
      expect(component.getAssignedPayload({})).toBeTruthy();
    });
  });

  describe('Service and Calendar/Modal Edge Cases', () => {
    it('should handle calendarGetCompany success', () => {
      projectSharingServiceMock.getCompanyList.mockReturnValue(of({ companyList: [{ id: 1, companyName: 'Test' }] }));
      jest.spyOn(component, 'calendarGetDefinable').mockImplementation(() => {});
      component.calendarGetCompany();
      expect(component.companyList).toEqual([{ id: 1, companyName: 'Test' }]);
      expect(component.calendarGetDefinable).toHaveBeenCalled();
    });

    it('should handle calendarGetCompany error', () => {
      projectSharingServiceMock.getCompanyList.mockReturnValue(throwError(() => new Error('fail')));
      expect(() => component.calendarGetCompany()).not.toThrow();
    });

    it('should handle calendarGetDefinable success', () => {
      projectSharingServiceMock.guestGetDefinableWork.mockReturnValue(of({ data: [{ id: 1, DFOW: 'Work' }] }));
      jest.spyOn(component, 'getLocations').mockImplementation(() => {});
      component.calendarGetDefinable();
      expect(component.defineList).toEqual([{ id: 1, DFOW: 'Work' }]);
      expect(component.getLocations).toHaveBeenCalled();
    });

    it('should handle calendarGetDefinable error', () => {
      projectSharingServiceMock.guestGetDefinableWork.mockReturnValue(throwError(() => new Error('fail')));
      expect(() => component.calendarGetDefinable()).not.toThrow();
    });

    it('should handle getLocations success', () => {
      projectSharingServiceMock.getLocations.mockReturnValue(of({ data: [{ id: 1, name: 'Loc' }] }));
      jest.spyOn(component, 'openContentModal').mockImplementation(() => {});
      component.getLocations();
      expect(component.locationList).toEqual([{ id: 1, name: 'Loc' }]);
      expect(component.openContentModal).toHaveBeenCalled();
    });

    it('should handle getLocations error', () => {
      projectSharingServiceMock.getLocations.mockReturnValue(throwError(() => new Error('fail')));
      expect(() => component.getLocations()).not.toThrow();
    });

    it('should handle calendarGetOverAllGate success', () => {
      projectSharingServiceMock.guestGateList.mockReturnValue(of({ data: [{ id: 1, name: 'Gate' }] }));
      jest.spyOn(component, 'calendarGetOverAllEquipment').mockImplementation(() => {});
      component.calendarGetOverAllGate();
      expect(component.gateList).toEqual([{ id: 1, name: 'Gate' }]);
      expect(component.calendarGetOverAllEquipment).toHaveBeenCalled();
    });

    it('should handle calendarGetOverAllGate error', () => {
      projectSharingServiceMock.guestGateList.mockReturnValue(throwError(() => new Error('fail')));
      expect(() => component.calendarGetOverAllGate()).not.toThrow();
    });

    it('should handle calendarGetOverAllEquipment success', () => {
      projectSharingServiceMock.guestListEquipment.mockReturnValue(of({ data: [{ id: 1, name: 'Equip' }] }));
      jest.spyOn(component, 'calendarGetCompany').mockImplementation(() => {});
      component.calendarGetOverAllEquipment();
      expect(component.equipmentList).toEqual([{ id: 1, name: 'Equip' }]);
      expect(component.calendarGetCompany).toHaveBeenCalled();
    });

    it('should handle calendarGetOverAllEquipment error', () => {
      projectSharingServiceMock.guestListEquipment.mockReturnValue(throwError(() => new Error('fail')));
      expect(() => component.calendarGetOverAllEquipment()).not.toThrow();
    });

    it('should handle openContentModal', () => {
      component.modalLoader = true;
      component.openContentModal();
      expect(component.modalLoader).toBe(false);
    });

    it('should not throw if calendarApi is undefined in goNext', () => {
      component.calendarApi = undefined;
      expect(() => component.goNext()).not.toThrow();
    });
    it('should not throw if calendarApi is undefined in goPrev', () => {
      component.calendarApi = undefined;
      expect(() => component.goPrev()).not.toThrow();
    });
    it('should not throw if calendarApi is undefined in goPrevYear', () => {
      component.calendarApi = undefined;
      expect(() => component.goPrevYear()).not.toThrow();
    });
    it('should not throw if calendarApi is undefined in goNextYear', () => {
      component.calendarApi = undefined;
      expect(() => component.goNextYear()).not.toThrow();
    });
    it('should not throw if calendarApi is undefined in goTimeGridWeekOrDay', () => {
      component.calendarApi = undefined;
      expect(() => component.goTimeGridWeekOrDay('timeGridWeek')).not.toThrow();
    });
    it('should not throw if calendarApi is undefined in goDayGridMonth', () => {
      component.calendarApi = undefined;
      expect(() => component.goDayGridMonth()).not.toThrow();
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle getEventNDR when ProjectId and ParentCompanyId are not set', () => {
      component.ProjectId = null;
      component.ParentCompanyId = null;
      component.loader = false;
      component.getEventNDR();
      expect(component.loader).toBe(true);
      expect(component.deliveryList).toEqual([]);
      expect(projectSharingServiceMock.getEventNDR).not.toHaveBeenCalled();
    });

    it('should handle setCalendar when calendarComponent1 is not available', () => {
      component.calendarComponent1 = null;
      expect(() => component.setCalendar()).not.toThrow();
    });

    it('should handle openEditModal with existing modalRef', () => {
      const mockItem = { id: 1 };
      const mockAction = 'edit';
      component.modalRef = { hide: jest.fn() } as any;
      jest.spyOn(component, 'closeDescription').mockImplementation(() => {});
      jest.spyOn(component, 'close').mockImplementation(() => {});
      component.openEditModal(mockItem, mockAction);
      expect(component.closeDescription).toHaveBeenCalled();
      expect(component.close).toHaveBeenCalled();
      expect(deliveryServiceMock.updatedDeliveryId).toHaveBeenCalledWith(1);
      expect(modalServiceMock.show).toHaveBeenCalled();
    });

    it('should handle getValueByLabel for all supported labels', () => {
      const mockElement = {
        description: 'Test Description',
        companyDetails: [{ Company: { companyName: 'Test Company' } }],
        memberDetails: [{ Member: { User: { firstName: 'John', lastName: 'Doe' } } }],
        gateDetails: [{ Gate: { gateName: 'Gate A' } }],
        DeliveryId: 'DEL123',
        defineWorkDetails: [{ DeliverDefineWork: { DFOW: 'Test Work' } }],
        equipmentDetails: [{ Equipment: { equipmentName: 'Crane' } }]
      };

      expect(component.getValueByLabel('Description', mockElement)).toBe('Test Description');
      expect(component.getValueByLabel('Responsible Company', mockElement)).toBe('Test Company');
      expect(component.getValueByLabel('Responsible Person', mockElement)).toBe('John Doe');
      expect(component.getValueByLabel('Gate', mockElement)).toBe('Gate A');
      expect(component.getValueByLabel('Delivery ID', mockElement)).toBe('DEL123');
      expect(component.getValueByLabel('Definable Feature Of Work', mockElement)).toBe('Test Work');
      expect(component.getValueByLabel('Equipment', mockElement)).toBe('Crane');
    });

    it('should handle changeFormat with null date', () => {
      expect(component.changeFormat(null)).toBeUndefined();
    });

    it('should handle getResponsiblePeople with null user', () => {
      expect(component.getResponsiblePeople(null)).toBe('UU');
    });

    it('should handle getResponsiblePeople with empty user object', () => {
      expect(component.getResponsiblePeople({})).toBe('UU');
    });

    it('should handle getResponsiblePeople with partial user data', () => {
      expect(component.getResponsiblePeople({ firstName: 'John' })).toBe('JU');
      expect(component.getResponsiblePeople({ lastName: 'Doe' })).toBe('UD');
    });

    it('should handle filterDetailsForm', () => {
      component.filterDetailsForm();
      expect(component.filterForm.get('companyFilter')).toBeTruthy();
      expect(component.filterForm.get('descriptionFilter')).toBeTruthy();
      expect(component.filterForm.get('statusFilter')).toBeTruthy();
    });

    it('should handle checkGuest with error response', () => {
      const errorResponse = { error: 'Network error' };
      projectSharingServiceMock.checkIsGuest.mockReturnValue(throwError(() => errorResponse));
      jest.spyOn(console, 'error').mockImplementation(() => {});
      component.checkGuest();
      expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
      expect(console.error).toHaveBeenCalledWith('Error occurred:', errorResponse);
    });

    it('should handle checkGuest with no data', () => {
      projectSharingServiceMock.checkIsGuest.mockReturnValue(of({ data: null }));
      component.checkGuest();
      expect(component.isGuestUser).toBe(false);
    });
  });

  describe('Additional Coverage Tests', () => {
    it('should handle getEventNDR with search and filters', () => {
      component.search = 'test';
      component.filterForm.patchValue({
        descriptionFilter: 'test',
        companyFilter: '1',
        statusFilter: 'Approved'
      });
      projectSharingServiceMock.getEventNDR.mockReturnValue(of({ data: [] }));
      component.getEventNDR();
      expect(projectSharingServiceMock.getEventNDR).toHaveBeenCalled();
    });

    it('should handle getEventNDR with date range', () => {
      component.calendarApi = {
        currentData: {
          dateProfile: {
            activeRange: {
              start: '2024-03-01',
              end: '2024-03-31'
            }
          }
        }
      };
      projectSharingServiceMock.getEventNDR.mockReturnValue(of({ data: [] }));
      component.getEventNDR();
      expect(projectSharingServiceMock.getEventNDR).toHaveBeenCalled();
    });

    it('should handle getMembers with error', () => {
      projectSharingServiceMock.listAllMember.mockReturnValue(throwError(() => new Error('API Error')));
      component.getMembers();
      expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should handle getMembers with empty response', () => {
      projectSharingServiceMock.listAllMember.mockReturnValue(of({ data: [] }));
      component.getMembers();
      expect(component.memberList).toEqual([]);
    });

    it('should handle getDateDetails with invalid dates', () => {
      const mockData = {
        deliveryStart: 'invalid-date',
        deliveryEnd: 'invalid-date',
        requestType: 'deliveryRequest'
      };
      component.getDateDetails(mockData);
      expect(component.descriptionPopup).toBe(true);
    });

    it('should handle openFilterModal with existing modalRef', () => {
      const mockTemplate = {} as any;
      component.modalRef = { hide: jest.fn() } as any;
      component.openFilterModal(mockTemplate);
      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(modalServiceMock.show).toHaveBeenCalled();
    });

    it('should handle filterSubmit with empty form', () => {
      component.filterForm.reset();
      component.filterSubmit();
      expect(component.filterCount).toBe(0);
    });

    it('should handle resetFilter with empty form', () => {
      component.filterForm.reset();
      component.resetFilter();
      expect(component.filterCount).toBe(0);
      expect(component.search).toBe('');
    });

    it('should handle deliveryDescription with invalid event', () => {
      const mockEvent = {
        event: {
          id: 'invalid',
          extendedProps: {}
        }
      };
      component.deliveryDescription(mockEvent);
      expect(component.eventData).toEqual([]);
    });

    it('should handle calendarDescription with invalid event', () => {
      const mockEvent = {
        event: {
          id: 'invalid',
          extendedProps: {}
        }
      };
      component.calendarDescription(mockEvent);
      expect(component.calendarDescriptionPopup).toBe(false);
      expect(component.viewEventData).toBe('');
    });

    it('should handle occurMessage with invalid data', () => {
      const mockData = {
        repeatEveryType: 'Invalid',
        endTime: 'invalid-date'
      };
      component.occurMessage(mockData);
      expect(component.message).toBe('');
    });

    it('should handle handleDownKeydown with invalid action', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      component.handleDownKeydown(event, '', '', 'invalid-action');
      expect(event.preventDefault).toHaveBeenCalled();
    });

    it('should handle getValueByLabel with invalid label', () => {
      const mockElement = {
        description: 'Test Description'
      };
      const result = component.getValueByLabel('Invalid Label', mockElement);
      expect(result).toBe('');
    });

    it('should handle changeFormat with invalid date', () => {
      const result = component.changeFormat('invalid-date');
      expect(result).toBeUndefined();
    });

    it('should handle getResponsiblePeople with invalid data', () => {
      const result = component.getResponsiblePeople({ firstName: null, lastName: null });
      expect(result).toBe('UU');
    });

    it('should handle initializeSeriesOption with existing options', () => {
      component.seriesOptions = [
        { option: 1, text: 'Existing Option', disabled: false }
      ];
      component.initializeSeriesOption();
      expect(component.seriesOptions).toHaveLength(2);
    });

    it('should handle changeRequestCollapse with invalid date', () => {
      component.changeRequestCollapse({ deliveryStart: 'invalid-date' });
      expect(component.allRequestIsOpened).toBe(true);
    });

    it('should handle close with error', () => {
      component.modalRef = { hide: jest.fn(() => { throw new Error('Close error'); }) } as any;
      component.close();
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
    });

    it('should handle ngAfterViewInit with error', () => {
      component.ProjectId = null;
      component.ParentCompanyId = null;
      component.guestUserId = null;
      jest.spyOn(component, 'setCalendar').mockImplementation(() => { throw new Error('Setup error'); });
      component.ngAfterViewInit();
      expect(component.setCalendar).toHaveBeenCalled();
    });
  });
});
