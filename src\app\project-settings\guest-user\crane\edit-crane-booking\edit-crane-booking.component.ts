/* eslint-disable max-lines-per-function */
import {
  Component, TemplateRef, OnInit,
  Input,
} from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { BsModalService, BsModalRef, ModalOptions } from 'ngx-bootstrap/modal';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import moment from 'moment';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { Socket } from 'ngx-socket-io';
import { DeliveryService } from '../../../../services/profile/delivery.service';
import { MixpanelService } from '../../../../services/mixpanel.service';
import { ProjectSettingsService } from '../../../../services/project_settings/project-settings.service';
import { ProjectSharingService } from '../../../../services/projectSharingService/project-sharing.service';

type DateInput = string | number | Date;

@Component({
  selector: 'app-edit-crane-booking',
  templateUrl: './edit-crane-booking.component.html',
})
export class EditCraneBookingComponent implements OnInit {
  @Input() data: any;

  @Input() title: string;

  public craneEditRequest: UntypedFormGroup;

  public ProjectId: any;

  public submitted = false;

  public escort = false;

  public formSubmitted = false;

  public editSubmitted = false;

  public modalLoader = false;

  public authUser: any = {};

  public loader = false;

  public gateList: any = [];

  public equipmentList: any = [];

  public definableFeatureOfWorkList: any = [];

  public editNdrCompanyDropdownSettings: IDropdownSettings;

  public companyList: any = [];

  public lastId: any = {};

  public formEditSubmitted = false;

  public editNdrDefinableDropdownSettings: IDropdownSettings;

  public currentEditItem: any = {};

  public craneRequestId: any;

  public editProjectId: any;

  public ParentCompanyId: any;

  public deliveryEnd: Date;

  public deliveryStart: Date;

  public NDRTimingChanged = false;

  public editBeforeDFOW = [];

  public editBeforeCompany = [];

  public array3Value = [];

  public formEdited = true;

  public errequipmentenable = false;

  public errmemberenable = false;

  public memberList: any = [];

  public deliveryWindowTime;

  public deliveryWindowTimeUnit;

  public seriesOption: number;

  public recurrenceId: number;

  public recurrenceEndDate;

  public isDisabledDate = false;

  public minDateOfrecurrenceEndDate;

  public selectedLocationId: any;

  public getChosenLocation: any;

  public editNdrLocationDropdownSettings: IDropdownSettings;

  public locationList: any = [];

  public guestUserId: any;

  public editBeforeEquipment = [];

  public equipmentDropdownSettings: IDropdownSettings;

  public noEquipmentOption = { id: 0, equipmentName: 'No Equipment Needed' };

  public constructor(
    private readonly formBuilder: UntypedFormBuilder,
    public socket: Socket,
    private readonly toastr: ToastrService,
    public router: Router,
    public modalRef: BsModalRef,
    private readonly modalService: BsModalService,
    public modalRef1: BsModalRef,
    private readonly mixpanelService: MixpanelService,
    private readonly deliveryService: DeliveryService,
    public projectSettingsService: ProjectSettingsService,
    public projectSharingService: ProjectSharingService,
    private readonly option: ModalOptions,
  ) {
    this.ProjectId = +window.atob(localStorage.getItem('guestProjectId'));
    this.ParentCompanyId = +window.atob(localStorage.getItem('guestParentCompanyId'));
    this.guestUserId = +window.atob(localStorage.getItem('guestId'));
    this.editCraneRequestForm();
    this.getMembers();
    this.getProjectSettings();
  }

  public ngOnInit(): void {
    const stateValue = this.data;
    this.craneRequestId = stateValue.id;
    this.seriesOption = stateValue.seriesOption;
  }

  public ngAfterViewInit(): void {
    this.seriesOption = this.modalRef?.content?.seriesOption;
    this.isDisabledDate = this.seriesOption !== 1;
    this.getOverAllGate();
  }

  public getProjectSettings(): void {
    if (this.ProjectId) {
      const params = {
        ProjectId: this.ProjectId,
      };
      this.projectSettingsService.getGuestProjectSettings(params).subscribe((res): void => {
        const responseData = res.data;
        this.deliveryWindowTime = responseData.deliveryWindowTime;
        this.deliveryWindowTimeUnit = responseData.deliveryWindowTimeUnit;
      });
    }
  }

  public getOverAllGate(): void {
    this.modalLoader = true;
    const params = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService
      .guestGateList(params, { isFilter: true, showActivatedAlone: true })
      .subscribe((res): void => {
        this.gateList = res.data;
        this.getOverAllEquipmentforEditNdr();
      });
  }

  public getOverAllEquipmentforEditNdr(): void {
    const editNdrGetEquipmentParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService
      .guestListCraneEquipment(editNdrGetEquipmentParams, {
        showActivatedAlone: true,
      })
      .subscribe((editNdrEquipmentResponse): void => {
        this.equipmentList =  [this.noEquipmentOption, ...editNdrEquipmentResponse.data.rows];
        this.equipmentDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'equipmentName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
        this.getCompaniesForEditNdr();
      });
  }

  public getCompaniesForEditNdr(): void {
    const getCompaniesForEditNdrParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService
      .guestGetCompanies(getCompaniesForEditNdrParams)
      .subscribe((getCompaniesForEditNdrResponse: any): void => {
        if (getCompaniesForEditNdrResponse) {
          this.companyList = getCompaniesForEditNdrResponse.data;
          this.getDefinableForEditNdr();
          this.editNdrCompanyDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'companyName',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
        }
      });
  }

  public getDefinableForEditNdr(): void {
    const getDefinableForEditNdrParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService
      .guestGetDefinableWork(getDefinableForEditNdrParams)
      .subscribe((getDefinableForEditNdrResponse: any): void => {
        if (getDefinableForEditNdrResponse) {
          const { data } = getDefinableForEditNdrResponse;
          this.definableFeatureOfWorkList = data;
          this.editNdrDefinableDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'DFOW',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            allowSearchFilter: true,
          };
          this.getLocationForEditNdr();
        }
      });
  }

  public getLocationForEditNdr(): void {
    const getLocationForEditNdrParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService
      .guestGetLocations(getLocationForEditNdrParams)
      .subscribe((getLocationForEditNdrResponse: any): void => {
        if (getLocationForEditNdrResponse) {
          const { data } = getLocationForEditNdrResponse;
          this.locationList = data;
          this.editNdrLocationDropdownSettings = {
            singleSelection: true,
            idField: 'id',
            textField: 'locationPath',
            allowSearchFilter: true,
            closeDropDownOnSelection: true,
          };
          this.getEquipmentCraneRequest();
        }
      });
  }

  public getEquipmentCraneRequest(): void {
    this.loader = true;
    const param = {
      CraneRequestId: this.craneRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    this.projectSharingService.guestGetEquipmentCraneRequest(param).subscribe((res): void => {
      this.currentEditItem = res.data;
      this.craneEditRequest.get('id').setValue(this.currentEditItem.id);
      this.craneEditRequest.get('CraneRequestId').setValue(this.currentEditItem.CraneRequestId);
      this.craneEditRequest.get('description').setValue(this.currentEditItem.description);
      this.craneEditRequest
        .get('deliveryDate')
        .setValue(moment(this.currentEditItem.craneDeliveryStart).format('MM/DD/YYYY'));
      this.craneEditRequest
        .get('craneDeliveryStart')
        .setValue(new Date(this.currentEditItem.craneDeliveryStart));
      this.craneEditRequest
        .get('craneDeliveryEnd')
        .setValue(new Date(this.currentEditItem.craneDeliveryEnd));
      this.craneEditRequest.get('recurrenceId').setValue(this.currentEditItem?.recurrence?.id);
      this.craneEditRequest
        .get('recurrence')
        .setValue(this.currentEditItem?.recurrence?.recurrence);
      if (this.currentEditItem?.recurrence?.recurrenceEndDate) {
        this.craneEditRequest
          .get('recurrenceEndDate')
          .setValue(
            moment(this.currentEditItem?.recurrence?.recurrenceEndDate).format('MM/DD/YYYY'),
          );
      }
      this.minDateOfrecurrenceEndDate = new Date(
        this.currentEditItem?.recurrence?.recurrenceEndDate,
      );
      this.craneEditRequest.get('isEscortNeeded').setValue(this.currentEditItem.escort);
      this.craneEditRequest.get('additionalNotes').setValue(this.currentEditItem.additionalNotes);
      this.setlocation();
      this.craneEditRequest.get('pickUpLocation').setValue(this.currentEditItem.pickUpLocation);
      this.craneEditRequest.get('dropOffLocation').setValue(this.currentEditItem.dropOffLocation);
      this.craneEditRequest
        .get('isAssociatedWithDeliveryRequest')
        .setValue(this.currentEditItem.isAssociatedWithDeliveryRequest);
      this.setCompany();
      this.setDefine();
      this.setMember();
      this.openContentModal();
      this.seriesOption = this.modalRef?.content?.seriesOption;
      this.isDisabledDate = this.seriesOption !== 1;
    });
  }

  public openContentModal(): void {
    this.modalLoader = false;
  }

  public locationSelected(data): void {
    this.getChosenLocation = this.locationList.filter((obj: any): any => +obj.id === +data.id);
    this.selectedLocationId = this.getChosenLocation[0]?.id;
  }

  public setlocation(): void {
    if (this.currentEditItem.location) {
      this.getChosenLocation = [];
      const data = {
        id: this.currentEditItem.location.id,
        locationPath: this.currentEditItem.location.locationPath,
      };
      this.getChosenLocation.push(data);
      this.craneEditRequest.get('LocationId').patchValue(this.getChosenLocation);
      this.selectedLocationId = this.getChosenLocation[0]?.id;
    }
  }

  public setMember(): void {
    const { memberDetails } = this.currentEditItem;
    const newMemberList = [];
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      id: this.guestUserId,
    };
    this.projectSharingService.guestGetMemberRole(params).subscribe((res): void => {
      this.authUser = res.data;
      if (memberDetails !== undefined) {
        memberDetails.forEach(
          (element: {
            Member: { User: { firstName: any; lastName: any; email: any }; id: any };
          }): void => {
            let email: string;
            if (element.Member?.User?.firstName != null) {
              email = `${element.Member?.User?.firstName} ${element.Member?.User?.lastName} (${element.Member?.User?.email})`;
            } else {
              email = `(${element.Member?.User?.email})`;
            }
            const data: any = {
              email,
              id: element.Member?.id,
            };
            if (element.Member?.User?.email === this.authUser?.User?.email) {
              data.readonly = true;
            }
            if (element.Member?.User?.email) {
              newMemberList.push(data);
            }
          },
        );
      }
    });
    this.craneEditRequest.get('responsiblePersons').patchValue(newMemberList);
  }

  public setDefine(): void {
    const { defineWorkDetails } = this.currentEditItem;
    const newDefineList = [];
    if (defineWorkDetails !== undefined) {
      defineWorkDetails.forEach((element: { DeliverDefineWork: { id: any; DFOW: any } }): void => {
        const data = {
          id: element.DeliverDefineWork.id,
          DFOW: element.DeliverDefineWork.DFOW,
        };
        newDefineList.push(data);
      });
    }
    this.editBeforeDFOW = newDefineList;
    this.craneEditRequest.get('definableFeatureOfWorks').patchValue(newDefineList);
  }

  public setCompany(): void {
    const { companyDetails } = this.currentEditItem;
    const newCompanyList = [];
    if (companyDetails !== undefined) {
      companyDetails.forEach((element: { Company: { id: any; companyName: any } }): void => {
        const data = {
          id: element.Company.id,
          companyName: element.Company.companyName,
        };
        newCompanyList.push(data);
      });
    }
    this.editBeforeCompany = newCompanyList;
    this.craneEditRequest.get('companies').patchValue(newCompanyList);
  }

  public editCraneRequestForm(): void {
    this.craneEditRequest = this.formBuilder.group({
      id: [''],
      EquipmentId: [this.formBuilder.array([])],
      LocationId: ['', Validators.compose([Validators.required])],
      additionalNotes: [''],
      CraneRequestId: [''],
      responsiblePersons: [''],
      description: ['', Validators.compose([Validators.required])],
      deliveryDate: ['', Validators.compose([Validators.required])],
      craneDeliveryStart: ['', Validators.compose([Validators.required])],
      craneDeliveryEnd: ['', Validators.compose([Validators.required])],
      isEscortNeeded: [false],
      companies: [this.formBuilder.array([])],
      definableFeatureOfWorks: [this.formBuilder.array([])],
      pickUpLocation: [''],
      dropOffLocation: [''],
      isAssociatedWithDeliveryRequest: [false, Validators.compose([Validators.required])],
      recurrenceId: [''],
      recurrence: [''],
      recurrenceEndDate: [''],
    });
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.formEditSubmitted = false;
      this.editSubmitted = false;
      this.modalRef.hide();
      this.NDRTimingChanged = false;
    }
  }

  public onEditSubmitForm(): void {
    if (this.craneEditRequest.dirty || this.NDRTimingChanged) {
      this.formEdited = false;
    }
  }

  public convertStart(deliveryDate: Date, startHours: number, startMinutes: number): Date {
    const fullYear = deliveryDate.getFullYear();
    const fullMonth = deliveryDate.getMonth();
    const date = deliveryDate.getDate();
    const deliveryNewStart = new Date(fullYear, fullMonth, date, startHours, startMinutes);
    return deliveryNewStart;
  }

  public checkEditDeliveryFutureDate(
    editDeliveryStart: DateInput,
    editDeliveryEnd: DateInput,
  ): boolean {
    const editStartDate = moment(new Date(editDeliveryStart));
    const editCurrentDate = moment()
      .clone()
      .add(this.deliveryWindowTime, this.deliveryWindowTimeUnit);
    const editEndDate = moment(new Date(editDeliveryEnd));
    if (editStartDate.isAfter(editCurrentDate) && editEndDate.isAfter(editCurrentDate)) {
      return true;
    }
    return false;
  }

  public throwError(action: string): void {
    this.editSubmitted = false;
    this.formEditSubmitted = false;
    if (action === 'time error') {
      this.toastr.error('Please Enter Start time Lesser than End time');
    } else {
      this.toastr.error(
        'Booking not allowed to edit. Please contact the project administrator to edit this booking',
      );
    }
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }

  public gatecheck(value, menuname): void {
    if (menuname === 'Person') {
      const result = this.memberList.filter((o) => value.some(({ id }) => o.id === id));
      if (value.length !== result.length) {
        this.errmemberenable = true;
      } else {
        this.errmemberenable = false;
      }
    }
  }

  public getIndexValue(formValue) {
    const arr1 = this.memberList;
    const arr2 = formValue.responsiblePersons;
    let index2: number;
    const result = arr1.filter((o) => arr2.some(({ id }) => o.id === id));

    if (formValue.responsiblePersons.length !== result.length) {
      index2 = -1;
    } else {
      index2 = 0;
    }
    return index2;
  }

  public onEditSubmit(): void {
    this.editSubmitted = true;
    const companies = [];
    const persons = [];
    const define = [];
    const equipments = [];
    const formValue = this.craneEditRequest.value;
    if (formValue.EquipmentId.length <= 0) {
      this.toastr.error('Equipment is required');
      this.formEditSubmitted = false;
      return;
    }
    const deliveryDate = new Date(formValue.deliveryDate);
    const startHours = new Date(formValue.craneDeliveryStart).getHours();
    const startMinutes = new Date(formValue.craneDeliveryStart).getMinutes();
    const deliveryStart = this.convertStart(deliveryDate, startHours, startMinutes);
    const endHours = new Date(formValue.craneDeliveryEnd).getHours();
    const endMinutes = new Date(formValue.craneDeliveryEnd).getMinutes();
    const deliveryEnd = this.convertStart(deliveryDate, endHours, endMinutes);
    if (this.craneEditRequest.invalid) {
      this.formEditSubmitted = false;
      return;
    }
    if (this.checkEditDeliveryFutureDate(deliveryStart, deliveryEnd)) {
      const index2 = this.getIndexValue(formValue);
      if (index2 === -1) {
        if (index2 === -1) {
          this.errmemberenable = true;
        }

        this.formEditSubmitted = false;
      } else {
        if (index2 === -1) {
          this.errmemberenable = false;
        }
        this.checkRequestStatusAndUserRole(
          formValue,
          deliveryStart,
          deliveryEnd,
          companies,
          persons,
          define,
          equipments,
        );
      }
    } else {
      this.checkRequestStatusAndUserRole(
        formValue,
        deliveryStart,
        deliveryEnd,
        companies,
        persons,
        define,
        equipments,
      );
    }
  }

  public checkRequestStatusAndUserRole(
    formValue,
    deliveryStart,
    deliveryEnd,
    companies,
    persons,
    define,
    equipments,
  ): void {
    if (this.currentEditItem.status === 'Completed' && this.authUser.RoleId !== 2) {
      if (
        moment(this.currentEditItem.craneDeliveryStart).format('MM/DD/YYYY')
            !== moment(this.craneEditRequest.get('deliveryDate').value).format('MM/DD/YYYY')
          || new Date(this.currentEditItem.craneDeliveryStart).getTime()
            !== new Date(this.craneEditRequest.get('craneDeliveryStart').value).getTime()
          || new Date(this.currentEditItem.craneDeliveryEnd).getTime()
            !== new Date(this.craneEditRequest.get('craneDeliveryEnd').value).getTime()
      ) {
        this.toastr.error('You are not allowed to change the date/time ');
        this.formEditSubmitted = false;
        return;
      }
    }
    if (this.currentEditItem.status === 'Approved' && this.authUser.RoleId !== 2) {
      if (
        moment(this.currentEditItem.deliveryDate).format('MM/DD/YYYY')
            !== moment(this.craneEditRequest.get('deliveryDate').value).format('MM/DD/YYYY')
          || new Date(this.currentEditItem.craneDeliveryStart).getTime()
            !== new Date(this.craneEditRequest.get('craneDeliveryStart').value).getTime()
          || new Date(this.currentEditItem.craneDeliveryEnd).getTime()
            !== new Date(this.craneEditRequest.get('craneDeliveryEnd').value).getTime()
      ) {
        if (!this.checkEditDeliveryFutureDate(deliveryStart, deliveryEnd)) {
          this.throwError('error');
          this.formEditSubmitted = false;
          return;
        }
      }
    }
    this.updateCraneDelivery(
      formValue,
      deliveryStart,
      deliveryEnd,
      companies,
      persons,
      define,
      equipments,
    );
  }

  public checkStartEnd(
    deliveryStart: DateInput,
    deliveryEnd: DateInput,
  ): boolean {
    const startDate = new Date(deliveryStart).getTime();
    const endDate = new Date(deliveryEnd).getTime();
    if (startDate < endDate) {
      return true;
    }
    return false;
  }

  public checkEditDeliveryStringEmptyValues(formValue: {
    description: string;
    additionalNotes: string;
  }): boolean {
    if (formValue.description.trim() === '') {
      this.toastr.error('Please Enter valid description.', 'OOPS!');
      return true;
    }
    if (formValue.additionalNotes) {
      if (formValue.additionalNotes.trim() === '') {
        this.toastr.error('Please Enter valid notes.', 'OOPS!');
        return true;
      }
    }
    return false;
  }

  public formReset(): void {
    this.formEditSubmitted = false;
    this.editSubmitted = false;
  }

  public close(template: TemplateRef<any>): void {
    if (
      this.craneEditRequest.touched
        || this.NDRTimingChanged
        || (this.craneEditRequest.get('definableFeatureOfWorks').dirty
          && this.craneEditRequest.get('definableFeatureOfWorks').value
          && this.craneEditRequest.get('definableFeatureOfWorks').value.length > 0
          && this.areDifferentByProperty(
            this.editBeforeDFOW,
            this.craneEditRequest.get('definableFeatureOfWorks').value,
            'DFOW',
          ))
        || (this.craneEditRequest.get('companies').dirty
          && this.craneEditRequest.get('companies').value
          && this.craneEditRequest.get('companies').value.length > 0
          && this.areDifferentByProperty(
            this.editBeforeCompany,
            this.craneEditRequest.get('companies').value,
            'companyName',
          ))
          || (this.craneEditRequest.get('EquipmentId').dirty
          && this.craneEditRequest.get('EquipmentId').value
          && this.craneEditRequest.get('EquipmentId').value.length > 0
          && this.areDifferentByProperty(
            this.editBeforeEquipment,
            this.craneEditRequest.get('EquipmentId').value,
            'equipmentName',
          ))
    ) {
      this.openConfirmationModalPopupForEditNDR(template);
    } else {
      this.resetForm('yes');
    }
  }

  public changeDate(event: any): void {
    if (!this.modalLoader) {
      const startTime = new Date(event).getHours();
      const minutes = new Date(event).getMinutes();
      this.deliveryEnd = new Date();
      this.deliveryEnd.setHours(startTime + 1);
      this.deliveryEnd.setMinutes(minutes);
      this.craneEditRequest.get('craneDeliveryEnd').setValue(this.deliveryEnd);
      this.NDRTimingChanged = true;
    }
    this.onEditSubmitForm();
  }

  public openConfirmationModalPopupForEditNDR(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public requestAutoEditcompleteItems = (text: string): Observable<any> => {
    const param = {
      ProjectId: this.ProjectId,
      search: text,
      ParentCompanyId: this.ParentCompanyId,
    };
    return this.projectSharingService.guestSearchNewMember(param);
  };

  public deliveryEndTimeChangeDetection(): void {
    this.NDRTimingChanged = true;
    this.onEditSubmitForm();
  }

  public numberOnly(event: { which: any; keyCode: any }): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public areDifferentByProperty(a: any[], b: any[], prop: string): boolean {
    const array1 = a.map((x: { [x: string]: any }): any => x[prop]);
    const array2 = b.map((x: { [x: string]: any }): any => x[prop]);
    this.array3Value = array1.concat(array2);
    this.array3Value = [...new Set([...array1, ...array2])];
    return this.array3Value.length !== array1.length;
  }

  public editCraneRequestSuccess(response: { message: string }): void {
    this.toastr.success(response.message, 'Success');
    this.formReset();
    this.closeForm();
    this.NDRTimingChanged = false;
    this.formEditSubmitted = false;
  }

  public editCraneRequest(payload: any): void {
    this.formEditSubmitted = true;
    this.projectSharingService.guestEditCraneRequest(payload).subscribe({
      next: (editNdrResponse: any): void => {
        if (editNdrResponse) {
          this.editCraneRequestSuccess(editNdrResponse);
          this.mixpanelService.addGuestUserMixpanelEvents('Guest User Edited Crane Booking');
        }
      },
      error: (editNDRHistoryError): void => {
        this.formReset();
        this.NDRTimingChanged = false;
        if (editNDRHistoryError.message?.statusCode === 400) {
          this.showError(editNDRHistoryError);
        } else if (!editNDRHistoryError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(editNDRHistoryError.message, 'OOPS!');
        }
      },
    });
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public updateCraneDelivery(
    editCraneRequestFormValue,
    deliveryStart: Date,
    deliveryEnd: Date,
    companies: any[],
    responsbilePersonsData: any[],
    definableFeatureOfWorkData: any[],
    equipments: any[],
  ): void {
    if (!this.checkStartEnd(deliveryStart, deliveryEnd)) {
      this.throwError('time error');
      return;
    }

    const companyDetails = editCraneRequestFormValue.companies;
    const personDetails = editCraneRequestFormValue.responsiblePersons;

    if (!companyDetails || companyDetails.length === 0) {
      this.formReset();
      this.toastr.error('Responsible Company is required');
      return;
    }

    if (!personDetails || personDetails.length === 0) {
      this.formReset();
      this.toastr.error('Responsible Person is required');
      return;
    }

    companyDetails.forEach((el: { id: any }) => companies.push(el.id));
    personDetails.forEach((el: { id: any }) => responsbilePersonsData.push(el.id));

    const definableDetails = editCraneRequestFormValue.definableFeatureOfWorks;
    if (definableDetails && definableDetails.length > 0) {
      definableDetails.forEach((el: { id: any }) => definableFeatureOfWorkData.push(el.id));
    }

    if (editCraneRequestFormValue.EquipmentId?.length > 0) {
      editCraneRequestFormValue.EquipmentId.forEach((el: { id: any }) => equipments.push(el.id));
    }

    if (this.checkEditDeliveryStringEmptyValues(editCraneRequestFormValue)) {
      this.formReset();
      return;
    }

    const payload = this.buildEditCraneRequestPayload(
      editCraneRequestFormValue,
      deliveryStart,
      deliveryEnd,
      companies,
      responsbilePersonsData,
      definableFeatureOfWorkData,
      equipments,
    );

    this.editCraneRequest(payload);
  }

  public buildEditCraneRequestPayload(
    formValue: any,
    deliveryStart: Date,
    deliveryEnd: Date,
    companies: any[],
    responsiblePersons: any[],
    definableFeatureOfWorks: any[],
    equipments: any[],
  ): any {
    const escortCondition = formValue.isEscortNeeded === null
      || formValue.isEscortNeeded === undefined
      || formValue.escort === '';

    return {
      id: formValue.id,
      description: formValue.description,
      companies,
      isEscortNeeded: escortCondition ? false : formValue.isEscortNeeded,
      ProjectId: this.ProjectId,
      userId: this.guestUserId,
      additionalNotes: formValue.additionalNotes,
      EquipmentId: equipments,
      LocationId: this.selectedLocationId,
      craneDeliveryStart: deliveryStart,
      craneDeliveryEnd: deliveryEnd,
      ParentCompanyId: this.ParentCompanyId,
      responsiblePersons,
      definableFeatureOfWorks,
      isAssociatedWithDeliveryRequest: false,
      pickUpLocation: formValue.pickUpLocation,
      dropOffLocation: formValue.dropOffLocation,
      seriesOption: this.seriesOption,
      recurrenceId: formValue.recurrenceId,
      recurrenceEndDate: formValue.recurrenceEndDate
        ? moment(formValue.recurrenceEndDate).format('YYYY-MM-DD')
        : null,
      recurrenceSeriesStartDate: moment(formValue.craneDeliveryStart).format('YYYY-MM-DD'),
      recurrenceSeriesEndDate: moment(formValue.craneDeliveryEnd).format('YYYY-MM-DD'),
      previousSeriesRecurrenceEndDate: moment(formValue.craneDeliveryStart)
        .add(-1, 'days')
        .format('YYYY-MM-DD'),
      nextSeriesRecurrenceStartDate: moment(formValue.craneDeliveryStart)
        .add(1, 'days')
        .format('YYYY-MM-DD'),
      deliveryStartTime: moment(formValue.craneDeliveryStart).format('HH:mm'),
      deliveryEndTime: moment(formValue.craneDeliveryEnd).format('HH:mm'),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    };
  }


  public closeForm(): void {
    if (this.modalRef1) {
      this.modalRef1.hide();
    }
    this.formEditSubmitted = false;
    this.editSubmitted = false;
    this.modalRef.hide();
    this.NDRTimingChanged = false;
    this.router.navigate(['/guest-crane-calendar']);
  }

  public setEquipment(): void {
    const { equipmentDetails } = this.currentEditItem;
     if(equipmentDetails.length && !equipmentDetails[0].Equipment){
      equipmentDetails[0].Equipment = { id: 0, equipmentName: 'No Equipment Needed' }
    }
    const newEquipmentList = [];
    if (equipmentDetails !== undefined) {
      equipmentDetails.forEach((element: { Equipment: { id: any; equipmentName: any } }): void => {
        const data = {
          id: element.Equipment.id,
          equipmentName: element.Equipment?.equipmentName,
        };
        newEquipmentList.push(data);
      });
    }
    this.editBeforeEquipment = newEquipmentList;
    this.craneEditRequest.get('EquipmentId').patchValue(newEquipmentList);
  }

 public equipmentSelected(value: any): void {
    // console.log(value);
    if(value) {
      if(value.length == this.equipmentList.length -1 && this.equipmentList[0].id == 0) {
          this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        }
        if(value.length != this.equipmentList.length && this.equipmentList[0].id != 0) {
          this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
          this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
        }
        if(value.length == 0) {
          this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
          this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
        }
        let hasNoEquipmentOption = false;
        let hasOtherEquipment = false;
        // Check if "No Equipment Needed" (id = 0) is selected
        hasNoEquipmentOption = value.some((item: any) => item.id === 0);

        // Check if other equipment is selected
        hasOtherEquipment = value.some((item: any) => item.id !== 0);

        const previousSelection = this.craneEditRequest.get('EquipmentId').value || [];
        const previousHasOther = previousSelection.some((item: any) => item.id !== 0);

        // Rule 1: If "No Equipment Needed" is selected and other items are selected, keep only "No Equipment Needed"
        if (hasNoEquipmentOption && hasOtherEquipment && !previousHasOther) {
          this.toastr.warning('When "No Equipment Needed" is selected, other equipment options cannot be selected.', 'Warning');
          const noEquipmentOnly = value.filter((item: any) => item.id === 0);
          this.craneEditRequest.get('EquipmentId').setValue(noEquipmentOnly);
          value = noEquipmentOnly;
          hasOtherEquipment = false;
        }

        // Rule 2: If other equipment is already selected and "No Equipment Needed" is now selected, remove it
        if (previousHasOther && hasNoEquipmentOption) {
          this.toastr.warning('When other equipment is selected, "No Equipment Needed" cannot be selected.', 'Warning');
          const filteredSelection = value.filter((item: any) => item.id !== 0);
          this.craneEditRequest.get('EquipmentId').setValue(filteredSelection);
          value = filteredSelection;
          hasNoEquipmentOption = false;
        }
      }
   }
}
