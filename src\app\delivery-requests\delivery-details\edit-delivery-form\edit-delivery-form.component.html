<div class="modal-header">
  <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">
    <img src="./assets/images/delivery-pop.svg" alt="Delivery" class="me-2" />Edit Delivery Booking
  </h1>
  <button
    type="button"
    class="close ms-auto"
    aria-label="Close"
    (click)="close(cancelConfirmation)"
  >
    <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close" /></span>
  </button>
</div>
<div class="modal-body newupdate-alignments taginput--heightfix" *ngIf="!modalLoader">
  <!-- Edit NDR -->
  <form
    name="form"
    class="custom-material-form add-concrete-material-form"
    id="delivery-edit2"
    [formGroup]="deliverEditForm"
    novalidate
  >
    <div class="row">
      <div class="col-md-6">
        <div class="form-group">
          <label class="fs12 fw600" for="description">Description<sup>*</sup></label>
          <textarea id="description"
            class="form-control fs11 radius0"
            placeholder="Enter Description"
            rows="2"
            formControlName="description"
            maxlength="150"
            (ngModelChange)="onEditSubmitForm('')"
          ></textarea>
          <div class="color-red" *ngIf="editSubmitted && deliverEditForm.get('description').errors">
            <small *ngIf="deliverEditForm.get('description').errors.required"
              >*Description is Required.</small
            >
          </div>
        </div>
      </div>
      <div class="col-md-6" *ngIf="!craneEquipmentTypeChosen">
        <div class="form-group">
          <label class="fs12 fw600" for="deliveryid">Delivery ID</label>
          <input  id="deliveryid"
            type="text"
            class="form-control fs10 color-orange fw500 material-input ps-3"
            placeholder=""
            formControlName="DeliveryId"
            disabled="disabled"
          />
        </div>
      </div>
      <div class="col-md-3" *ngIf="craneEquipmentTypeChosen">
        <div class="form-group">
          <label class="fs12 fw600"  for="delivery-id">Delivery ID</label>
          <input  id="delivery-id"
            type="text"
            class="form-control fs10 color-orange fw500 material-input ps-3"
            placeholder=""
            formControlName="DeliveryId"
            disabled="disabled"
          />
        </div>
      </div>
      <div class="col-md-3" *ngIf="craneEquipmentTypeChosen">
        <div class="form-group">
          <label class="fs12 fw600"  for="craneid">Crane Pick Request ID</label>
          <input  id="craneid"
            type="text"
            class="form-control fs10 color-orange fw500 material-input ps-3"
            placeholder=""
            formControlName="CraneRequestId"
            disabled="disabled"
          />
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-6">
        <div class="form-group company-select" id="company-select3">
          <label class="fs12 fw600"  for="rescom">Responsible Company<sup>*</sup></label>
          <ng-multiselect-dropdown  id="rescom"
            [placeholder]="'Responsible Company*'"
            [settings]="editNdrCompanyDropdownSettings"
            [data]="companyList"
            formControlName="companyItems"
            (ngModelChange)="onEditSubmitForm('')"
          >
          </ng-multiselect-dropdown>
          <div
            class="color-red"
            *ngIf="editSubmitted && !saveQueuedNDR && deliverEditForm.get('companyItems').errors"
          >
            <small *ngIf="deliverEditForm.get('companyItems').errors.required"
              >*Company is Required.</small
            >
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group company-select" id="company-select7">
          <label class="fs12 fw600"  for="defwork">Definable Feature Of Work</label>
          <ng-multiselect-dropdown  id="defwork"
            [placeholder]="'Definable Feature Of Work (Scope)'"
            [settings]="editNdrDefinableDropdownSettings"
            [data]="defineList"
            formControlName="defineItems"
            (ngModelChange)="onEditSubmitForm('')"
          >
          </ng-multiselect-dropdown>
          <div
            class="color-red"
            *ngIf="editSubmitted && !saveQueuedNDR && deliverEditForm.get('defineItems').errors"
          >
            <small *ngIf="deliverEditForm.get('defineItems').errors.required"
              >*Definable Feature Of Work is Required.</small
            >
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-md-12">
        <div class="form-group taginput-height">
          <label class="fs12 fw600"  for="resperson">Responsible Person<sup>*</sup></label>
          <ul
            class="follo-switch list-group list-group-horizontal justify-content-end float-end newfixswitch"
            id="switch-control3"
          >
            <li class="fs12 list-group-item border-0 escort--text pe-3 py-0">
              Escort Needed?<sup>*</sup>
            </li>
            <li class="list-group-item border-0 px-0 py-0">
              <ui-switch
                switchColor="#fff"
                defaultBoColor="#CECECE"
                defaultBgColor="#CECECE"
                formControlName="escort"
                (ngModelChange)="onEditSubmitForm('')"
              >
              </ui-switch>
            </li>
          </ul>
          <div class="w-100 float-start">
            <tag-input
              formControlName="person"
              [onlyFromAutocomplete]="true"
              [placeholder]="' '"
              [onTextChangeDebounce]="500"
              [identifyBy]="'id'"
              [displayBy]="'email'"
              class="tag-layout newdelivery-taglayout w-100"
              (ngModelChange)="onEditSubmitForm('')"
              (ngModelChange)="gatecheck($event, 'Person')"
            >
              <tag-input-dropdown
                [showDropdownIfEmpty]="true"
                [displayBy]="'email'"
                [identifyBy]="'id'"
                [autocompleteObservable]="requestAutoEditcompleteItems"
                [appendToBody]="false"
              >
                <ng-template let-item="item" let-index="index">
                  <span class="fs10">{{ item.email }}</span>
                </ng-template>
              </tag-input-dropdown>
            </tag-input>
          </div>
          <small class="color-red" *ngIf="errmemberenable"
            >Please select an active member,the existing member is deactivated.</small
          >
          <div
            class="color-red"
            *ngIf="editSubmitted && !saveQueuedNDR && deliverEditForm.get('person').errors"
          >
            <small *ngIf="deliverEditForm.get('person').errors.required"
              >*Please choose Responsible person.</small
            >
          </div>
        </div>
      </div>
    </div>

        <!--Gate Equipment-->
        <div class="row mt-3">
          <div class="col-md-6 primary-tooltip">
            <div class="form-group timezone-formgroup">
              <label class="fs12 fw600"  for="location"
                >Location<sup>*</sup>
                <div class="dot-border-info location-border-info tooltip location-tooltip">
                  <span class="fw700 info-icon fs12">i</span>
                  <span class="tooltiptext tooltiptext-info"
                    >Where will the materials/equipment be installed</span
                  >
                  <div class="arrow-down"></div></div
              ></label>
              <ng-multiselect-dropdown  id="location"
                [placeholder]="'Choose Location'"
                [settings]="editNdrLocationDropdownSettings"
                [data]="locationList"
                (onSelect)="locationSelected($event)"
                formControlName="LocationId"
                (ngModelChange)="onEditSubmitForm('')"
              >
              </ng-multiselect-dropdown>
              <div class="color-red" *ngIf="submitted && deliverEditForm.get('LocationId').errors">
                <small *ngIf="deliverEditForm.get('LocationId').errors.required"
                  >*Location is Required.</small
                >
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="w-100 float-start mt-2">
              <label class="fs12 fw600" for="gate">Gate<sup>*</sup></label>
              <select  id="gate"
                class="form-control fs12 material-input px-2"
                formControlName="GateId"
                (ngModelChange)="onEditSubmitForm('')"
                (ngModelChange)="gatecheck($event, 'Gate')"
                (change)="getBookingData()"
              >
                <option
                  *ngIf="
                    currentEditItem &&
                    currentEditItem?.gateDetails &&
                    currentEditItem?.gateDetails[0]?.Gate?.id
                  "
                  [ngValue]="currentEditItem?.gateDetails[0]?.Gate?.id"
                  disabled
                  selected
                  hidden
                >
                  {{ currentEditItem?.gateDetails[0]?.Gate?.gateName }}
                </option>
                <option value="" disabled selected hidden>Gate<sup>*</sup></option>
                <option *ngFor="let item of gateList" [ngValue]="item.id">{{ item.gateName }}</option>
              </select>

              <small class="color-red" *ngIf="errtextenable"
                >Please select an active gate,the existing gate is deactivated.</small
              >

              <div
                class="color-red"
                *ngIf="editSubmitted && !saveQueuedNDR && deliverEditForm.get('GateId').errors"
              >
                <small *ngIf="deliverEditForm.get('GateId').errors.required">*Gate is Required.</small>
              </div>
            </div>
          </div>
        </div>


        <div class="row">
          <div class="col-md-6">
            <div class="form-group company-select editdelivery-equipment-select">
              <label class="fs12 fw600"  for="equipment">Equipment<sup>*</sup></label>
              <ng-multiselect-dropdown id="equipment"
                [placeholder]="'Equipment'"
                [settings]="equipmentDropdownSettings"
                [data]="equipmentList"
                formControlName="EquipmentId"
                (ngModelChange)="onEditSubmitForm($event)"
              >
              </ng-multiselect-dropdown>

              <div
                class="color-red"
                *ngIf="editSubmitted && !saveQueuedNDR && deliverEditForm.get('EquipmentId').errors"
              >
                <small *ngIf="deliverEditForm.get('EquipmentId').errors.required"
                  >*Equipment is Required.</small
                >
              </div>
            </div>
          </div>

        </div>

        <div class="row mt-3">
          <div class="col-md-12">
            <app-time-slot
              #timeSlotRef
              (selectTime)="selectTime($event.start, $event.end)"
              [selectedBookingDate]="deliverEditForm.get('deliveryDate').value"
              [timeZone]="timeZone"
              [isEditMode]="true"
              [selectedId]="deliverEditForm.get('id').value"
              [selectedBookingType]="'deliveryRequest'"
              [equipmentId] = "deliverEditForm.get('EquipmentId').value"
              [LocationId] = "deliverEditForm.get('LocationId').value"
              [gateId] = "deliverEditForm.get('GateId').value"
            >
            </app-time-slot>
          </div>
        </div>

      <div class="row mt-3">
        <div class="col-md-6">
          <div class="row my-2" *ngIf="craneEquipmentTypeChosen">

            <div class="col-md-6">
              <div class="form-group">
                <label class="fs12 fw600" for="pickfrom">Picking From<sup>*</sup></label>
                <textarea class="form-control fs10 radius0" placeholder="Picking From" rows="2"  id="pickfrom"
                  formControlName="cranePickUpLocation" maxlength="150" (ngModelChange)="onEditSubmitForm('')"></textarea>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label class="fs12 fw600"  for="pickto">Picking To<sup>*</sup></label>
                <textarea class="form-control fs10 radius0" placeholder="Picking To" rows="2"  id="pickto"
                  formControlName="craneDropOffLocation" maxlength="150" (ngModelChange)="onEditSubmitForm('')"></textarea>
              </div>
            </div>
          </div>
        </div>
      </div>

    <div class="row">
      <div class="row mt-3 addcalendar-details">
        <div class="col-md-6"  *ngIf="seriesOption !== 1">
            <label class="fs12 fw600 mb-0" for="recurrance">Recurrence<sup>*</sup></label>
            <div class="form-group">
              <select  id="recurrance"
                class="form-control fs12 material-input px-0"
                formControlName="recurrence"
                (change)="onRecurrenceSelect($event.target.value)"
              >
                <option *ngFor="let type of recurrence" value="{{ type.value }}">
                  {{ type.value }}
                </option>
              </select>
            </div>
            <div
              class="row mt-2"
              *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
            >
              <div
                class="col-md-12 mt-md-0"
                *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
              >
                <label class="fs12 fw600 mb-0"  for="repeat">Repeat Every</label>
              </div>
              <div
                class="col-md-6 mt-md-0"
                *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
              >
                <div class="form-group">
                  <input  id="repeat"
                    type="text"
                    formControlName="repeatEveryCount"
                    class="form-control fs12 material-input p-0"
                    (input)="changeRecurrenceCount($event.target.value)"
                    min="1"
                  />
                </div>
              </div>

              <div class="col-md-6 mt-md-0" *ngIf="isRepeatWithSingleRecurrence">
                <div class="form-group">
                  <select
                    class="form-control fs12 material-input px-2"
                    formControlName="repeatEveryType"
                    (change)="chooseRepeatEveryType($event.target.value, null)"
                  >
                    <option value="" disabled selected hidden>Select Recurrence</option>
                    <option
                      *ngFor="let type of repeatWithSingleRecurrence"
                      value="{{ type.value }}"
                    >
                      {{ type.value }}
                    </option>
                  </select>
                </div>
              </div>
              <div
                class="col-md-4 mt-md-0"
                *ngIf="isRepeatWithMultipleRecurrence || showRecurrenceTypeDropdown"
              >
                <div class="form-group">
                  <select
                    class="form-control fs12 material-input px-2"
                    formControlName="repeatEveryType"
                    (change)="chooseRepeatEveryType($event.target.value, null)"
                  >
                    <option value="" disabled selected hidden>Select Recurrence</option>
                    <option
                      *ngFor="let type of repeatWithMultipleRecurrence"
                      value="{{ type.value }}"
                    >
                      {{ type.value }}
                    </option>
                  </select>
                </div>
              </div>
            </div>
            <div class="row addcalendar-displaydays">
              <div
                class="col-md-12 pt-0"
                *ngIf="
                  (selectedRecurrence === 'Weekly' ||
                    isRepeatWithMultipleRecurrence ||
                    isRepeatWithSingleRecurrence) &&
                  selectedRecurrence !== 'Monthly' &&
                  selectedRecurrence !== 'Yearly'
                "
              >
                <ul class="displaylists ps-0 mb-0">
                  <li *ngFor="let item of weekDays; let i = index" class="fs12 list-inline-item">
                    <input
                      type="checkbox"
                      [disabled]="item.isDisabled"
                      [value]="item.value"
                      class="d-none"
                      id="days-{{ i }}"
                      (change)="onChange($event)"
                      [checked]="item.checked"
                    />
                    <label for="days-{{ i }}">{{ item.display }}</label>
                  </li>
                  <div
                    class="color-red"
                    *ngIf="submitted && deliverEditForm.controls['days'].errors"
                  >
                    <small *ngIf="deliverEditForm.controls['days'].errors.required"
                      >*Required
                    </small>
                  </div>
                </ul>
              </div>
            </div>
            <div
              class="row"
              *ngIf="selectedRecurrence === 'Monthly' || selectedRecurrence === 'Yearly'"
              [ngClass]="selectedRecurrence === 'Yearly' ? 'recurrence-year' : ''"
            >
              <div class="col-md-12 mt-md-0 ps-2 pt-0 recurrance-column-day">
                <div class="w-100 float-start">
                  <div class="form-check">
                    <input
                      class="form-check-input c-pointer"
                      type="radio"
                      formControlName="chosenDateOfMonth"
                      id="flexRadioDefault1"
                      [value]="1"
                      (change)="changeMonthlyRecurrence()"
                    />
                    <label class="form-check-label fs12 color-orange" for="flexRadioDefault1">
                      On day {{ monthlyDate }}
                    </label>
                  </div>
                  <div class="form-check">
                    <input
                      class="form-check-input c-pointer"
                      type="radio"
                      formControlName="chosenDateOfMonth"
                      id="flexRadioDefault2"
                      [value]="2"
                      (change)="changeMonthlyRecurrence()"
                    />
                    <label class="form-check-label fs12 color-orange" for="flexRadioDefault2">
                      On the
                      {{ monthlyDayOfWeek }}
                      <p *ngIf="selectedRecurrence === 'Yearly'">
                        of
                        {{ deliverEditForm.get('deliveryDate').value | date : 'LLLL' }}
                      </p>
                    </label>
                  </div>
                  <div class="form-check" *ngIf="enableOption">
                    <input
                      class="form-check-input c-pointer"
                      type="radio"
                      formControlName="chosenDateOfMonth"
                      id="flexRadioDefault3"
                      [value]="3"
                      (change)="changeMonthlyRecurrence()"
                    />
                    <label class="form-check-label fs12 color-orange" for="flexRadioDefault3">
                      On the
                      {{ monthlyLastDayOfWeek }}
                      <p *ngIf="selectedRecurrence === 'Yearly'">
                        of
                        {{ deliverEditForm.get('deliveryDate').value | date : 'LLLL' }}
                      </p>
                    </label>
                  </div>
                </div>
                <div>
                  <div
                    class="color-red"
                    *ngIf="
                      submitted &&
                      (deliverEditForm.get('monthlyRepeatType')?.errors ||
                      deliverEditForm.get('dateOfMonth')?.errors)
                    "
                  >
                    <small *ngIf="deliverEditForm.get('monthlyRepeatType')?.errors?.required"
                      >*required</small
                    >
                    <small *ngIf="deliverEditForm.get('dateOfMonth')?.errors?.required"
                      >*required</small
                    >
                  </div>
                </div>
              </div>
            </div>
            <div class="row addcalendar-displaydays">
              <div class="col-md-12 mt-md-0 pb-0" *ngIf="message">
                <p class="fs12 color-grey11 mb-0">
                  <span class="color-red fw-bold">*</span>
                  {{ message }}
                </p>
              </div>
            </div>
          </div>
        <div class="col-md-6">
          <div class="col-md-12 float-start">
            <div
              class="row mt-0"
              *ngIf="
                selectedRecurrence === 'Daily' ||
                selectedRecurrence === 'Monthly' ||
                selectedRecurrence === 'Yearly' ||
                selectedRecurrence === 'Weekly'
              "
            >
              <div class="col-md-12 p-0">
                <div class="form-group ms-3 w-100">
                  <label class="fs12 fw600" for="recenddate">Recurrence End Date<sup>*</sup></label>
                  <div class="input-group mb-3">
                    <input  id="recenddate"
                      class="form-control fs10 material-input"
                      #dp="bsDatepicker"
                      bsDatepicker
                      formControlName="recurrenceEndDate"
                      placeholder="Recurrence End Date *"
                      [bsConfig]="{ isAnimated: true, showWeekNumbers: false, customTodayClass: 'today' }"
                      (ngModelChange)="showMonthlyRecurrence()"
                      [minDate]="recurrenceMinDate"
                    /><br />

                    <div
                      class="color-red"
                      *ngIf="
                        editSubmitted && !saveQueuedNDR && deliverEditForm.get('recurrenceEndDate').errors
                      "
                    >
                      <small *ngIf="deliverEditForm.get('recurrenceEndDate').errors.required"
                        >*Recurrence End Date is Required.</small
                      >
                    </div>

                      <span class="input-group-text">
                        <img
                          src="./assets/images/date.svg"
                          class="h-12px"
                          alt="Date"
                          (click)="dp.toggle()"
                          (keydown)="dp.toggle()"
                          [attr.aria-expanded]="dp.isOpen"
                        />
                      </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-2">
      <div class="col-md-12">
        <div class="form-group">
          <label class="fs12 fw600" for="addnotes">Additional Notes</label>
          <textarea  id="addnotes"
            class="form-control fs12 min-h-65px"
            placeholder=""
            rows="2"
            formControlName="notes"
          ></textarea>
        </div>
        <p class="fs11 color-grey17 fw500 mt-2">
          The delivery needs to be approved by Project Administrator to be added to the calendar.
          Please click on submit to send the booking for approval.
        </p>
      </div>
    </div>
    <label class="fs12 fw600"  for="carbtrac">Carbon Tracking</label>
    <div class="row mb-3 mx-0 py-2 border-light-gray border-left-green">
      <div class="col-md-6">
        <div class="w-100 float-start">
          <label class="fs12 fw600"  for="orgadd">Origination Address</label>
          <div class="border-bottom-grey">
            <input  id="orgadd"
              type="text"
              class="form-control fs12 material-input border-0 px-0 mt6"
              formControlName="originationAddress"
              placeholder="Origination Address"
              (ngModelChange)="onEditSubmitForm('')"
              ngx-gp-autocomplete
              (onAddressChange)="handleAddressChange($event)"
              #placesRef="ngx-places"
            />
          </div>
        </div>
      </div>
      <div class="col-md-6 primary-tooltip">
        <div class="form-group timezone-formgroup vehicleType-formgroup">
          <label class="fs12 fw600" for="vehtype">Vehicle Type </label>
          <ng-multiselect-dropdown  id="vehtype"
            [placeholder]="'Choose Vehicle Type'"
            [settings]="vehicleTypeDropdownSettings"
            [data]="vehicleTypes"
            (onSelect)="vehicleTypeSelected($event)"
            formControlName="vehicleType"
            (ngModelChange)="onEditSubmitForm('')"
          >
          </ng-multiselect-dropdown>
        </div>
      </div>
    </div>
    <div class="mt-4 mb30 text-center editdelivery-booking-btn">
      <button
        class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3 px-2rem"
        type="button"
        (click)="close(cancelConfirmation)"
      >
        Cancel
      </button>
      <button
        *ngIf="isQueuedNDR"
        class="btn btn-green1 radius20 fs12 fw-bold cairo-regular me-3 px-2-5rem"
        (click)="onSubmit('save')"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="saveQueuedNDR"></em>Save
      </button>
      <button
        *ngIf="!isQueuedNDR"
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem"
        [disabled]="(formEditSubmitted && deliverEditForm.valid) || formEdited"
        (click)="onSubmit('submitCurrentNDR')"
      >
        <em
          class="fa fa-spinner"
          aria-hidden="true"
          *ngIf="formEditSubmitted && deliverEditForm.valid"
        ></em
        >Submit
      </button>
      <button
        *ngIf="isQueuedNDR"
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem"
        [disabled]="formEditSubmitted && deliverEditForm.valid"
        (click)="onSubmit('submitQueuedNDR')"
      >
        <em
          class="fa fa-spinner"
          aria-hidden="true"
          *ngIf="formEditSubmitted && deliverEditForm.valid"
        ></em
        >Submit
      </button>
    </div>
  </form>
  <!-- Edit NDR -->
</div>
<div class="modal-body text-center" *ngIf="modalLoader">Loading...</div>
<!--Confirmation Popup-->
<div id="confirm-popup6">
  <ng-template #cancelConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">Are you sure you want to cancel?</p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetForm('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="resetForm('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
<!--Recurrence Edit Confirmation Popup-->
<div id="confirm-popup7">
  <ng-template #cancelRecurrence>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">If recurrence was edited may result in the loss of existing booking data</p>
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">Are you sure you want to continue?</p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="recurrenceSubmit('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="recurrenceSubmit('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
