import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { MixpanelService } from './mixpanel.service';
import { ApiService } from './api_base/api.service';
import { of } from 'rxjs';
import mixpanel from 'mixpanel-browser';
import { environment } from '../../environments/environment';

declare const global: {
  window: Window;
};

// Mock mixpanel-browser
jest.mock('mixpanel-browser', () => ({
  init: jest.fn(),
  identify: jest.fn(),
  track: jest.fn(),
  people: {
    set: jest.fn()
  }
}));

describe('MixpanelService', (): void => {
  let service: MixpanelService;
  let apiServiceMock: jest.Mocked<ApiService>;

  // Mock environment
  const originalEnvironment = { ...environment };

  beforeEach((): void => {
    // Create mock for ApiService
    apiServiceMock = {
      post: jest.fn()
    } as unknown as jest.Mocked<ApiService>;

    // Mock localStorage
    Storage.prototype.getItem = jest.fn();

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        MixpanelService,
        { provide: ApiService, useValue: apiServiceMock }
      ]
    });

    service = TestBed.inject(MixpanelService);

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Restore environment
    Object.assign(environment, originalEnvironment);

    // Restore localStorage
    jest.restoreAllMocks();
  });

  it('should be created', (): void => {
    expect(service).toBeTruthy();
  });

  describe('addMixpanelEvents', (): void => {
    it('should not track events when not in production environment', (): void => {
      // Arrange
      environment.environmentName = 'dev';

      // Act
      service.addMixpanelEvents('Test Event');

      // Assert
      expect(mixpanel.track).not.toHaveBeenCalled();
      expect(apiServiceMock.post).not.toHaveBeenCalled();
    });

    it('should track events with minimal data when ProjectId is not available', (): void => {
      // Arrange
      environment.environmentName = 'prod';
      (Storage.prototype.getItem as jest.Mock).mockReturnValue(null);

      // Act
      service.addMixpanelEvents('Test Event');

      // Assert
      expect(mixpanel.track).toHaveBeenCalledWith('Test Event', {
        Date: expect.any(String)
      });
      expect(apiServiceMock.post).not.toHaveBeenCalled();
    });

  });

  describe('getMemberData', (): void => {
    it('should call API with correct parameters', (): void => {
      // Arrange
      const payload = { ProjectId: '123' };
      const mockResponse = { data: { User: { email: '<EMAIL>' } } };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getMemberData(payload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('member/get_member_data', payload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('guestGetMemberData', (): void => {
    it('should call API with correct parameters', (): void => {
      // Arrange
      const payload = { ProjectId: 123, id: 456 };
      const mockResponse = { data: { User: { email: '<EMAIL>' } } };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.guestGetMemberData(payload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('guest_user/get_member_data', payload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('updateUserProfile', (): void => {
    it('should not update profile when not in production environment', (): void => {
      // Arrange
      environment.environmentName = 'dev';
      const userData = {
        userDetails: {
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          phoneCode: '+1',
          phoneNumber: '1234567890'
        }
      };

      // Act
      service.updateUserProfile(userData);

      // Assert
      expect(mixpanel.identify).not.toHaveBeenCalled();
      expect(mixpanel.people.set).not.toHaveBeenCalled();
    });

    it('should update profile with correct data when in production environment', (): void => {
      // Arrange
      environment.environmentName = 'prod';
      const userData = {
        userDetails: {
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          phoneCode: '+1',
          phoneNumber: '1234567890'
        }
      };

      // Mock timezone
      const mockTimezone = 'America/New_York';
      const originalDateTimeFormat = Intl.DateTimeFormat;
      Intl.DateTimeFormat = jest.fn().mockImplementation(() => ({
        resolvedOptions: () => ({
          timeZone: mockTimezone
        })
      })) as any;

      // Act
      service.updateUserProfile(userData);

      // Assert
      expect(mixpanel.identify).toHaveBeenCalledWith('<EMAIL>');
      expect(mixpanel.people.set).toHaveBeenCalledWith({
        $first_name: 'John',
        $last_name: 'Doe',
        $name: 'John Doe',
        $email: '<EMAIL>',
        $phone: '*************',
        $timezone: mockTimezone,
        $created: expect.any(String)
      });

      // Restore original
      Intl.DateTimeFormat = originalDateTimeFormat;
    });
  });

  describe('createUserProfile', (): void => {
    it('should not create profile when not in production environment', (): void => {
      // Arrange
      environment.environmentName = 'dev';
      const userData = {
        basicDetails: {
          email: '<EMAIL>',
          phoneCode: '+1',
          phoneNumber: '1234567890'
        },
        companyDetails: {
          fullName: 'John',
          lastName: 'Doe',
          city: 'New York'
        }
      };

      // Act
      service.createUserProfile(userData);

      // Assert
      expect(mixpanel.identify).not.toHaveBeenCalled();
      expect(mixpanel.people.set).not.toHaveBeenCalled();
    });

    it('should create profile with correct data when in production environment', (): void => {
      // Arrange
      environment.environmentName = 'prod';
      const userData = {
        basicDetails: {
          email: '<EMAIL>',
          phoneCode: '+1',
          phoneNumber: '1234567890'
        },
        companyDetails: {
          fullName: 'John',
          lastName: 'Doe',
          city: 'New York'
        }
      };

      // Mock timezone
      const mockTimezone = 'America/New_York';
      const originalDateTimeFormat = Intl.DateTimeFormat;
      Intl.DateTimeFormat = jest.fn().mockImplementation(() => ({
        resolvedOptions: () => ({
          timeZone: mockTimezone
        })
      })) as any;

      // Act
      service.createUserProfile(userData);

      // Assert
      expect(mixpanel.identify).toHaveBeenCalledWith('<EMAIL>');
      expect(mixpanel.people.set).toHaveBeenCalledWith({
        $first_name: 'John',
        $last_name: 'Doe',
        $name: 'John Doe',
        $email: '<EMAIL>',
        $phone: '*************',
        $city: 'New York',
        $timezone: mockTimezone,
        $created: expect.any(String)
      });

      // Restore original
      Intl.DateTimeFormat = originalDateTimeFormat;
    });
  });

  describe('addGuestUserMixpanelEvents', (): void => {
    it('should not track events when not in production environment', (): void => {
      // Arrange
      environment.environmentName = 'dev';

      // Act
      service.addGuestUserMixpanelEvents('Test Event');

      // Assert
      expect(mixpanel.track).not.toHaveBeenCalled();
      expect(apiServiceMock.post).not.toHaveBeenCalled();
    });

    it('should track events with minimal data when guest project ID is not available', (done): void => {
      // Arrange
      environment.environmentName = 'prod';
      (Storage.prototype.getItem as jest.Mock).mockImplementation((key) => {
        if (key === 'guestProjectId') return null;
        return null;
      });

      // Mock window.atob to avoid errors
      const originalAtob = window.atob;
      window.atob = jest.fn().mockReturnValue('');

      // Act
      service.addGuestUserMixpanelEvents('Test Event');

      // Assert
      setTimeout(() => {
        expect(mixpanel.track).toHaveBeenCalledWith('Test Event', {
          Date: expect.any(String)
        });

        // Restore original atob
        window.atob = originalAtob;
        done();
      }, 0);
    });

  });
});
















