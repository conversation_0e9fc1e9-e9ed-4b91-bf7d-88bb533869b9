/* eslint-disable max-lines-per-function */
import {
  Component, Input, OnInit, TemplateRef,
} from '@angular/core';
import {
  UntypedFormArray, UntypedFormBuilder, UntypedFormGroup, Validators, UntypedFormControl,
} from '@angular/forms';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import moment from 'moment';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { ProjectService } from '../../services/profile/project.service';
import { CalendarService } from '../../services/profile/calendar.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import {
  weekDays, recurrence, repeatWithMultipleRecurrence, repeatWithSingleRecurrence,
} from '../../services/common';

@Component({
  selector: 'app-add-calendar-event',
  templateUrl: './add-calendar-event.component.html',
})
export class AddCalendarEventComponent implements OnInit {
  @Input() data: any;

  @Input() title: string;

  public timezoneList: [];

  public selectedValue: any;

  public calendarEvent: UntypedFormGroup;

  public locationDropdownSettings: IDropdownSettings;

  public equipmentDropdownSettings: IDropdownSettings;

  public gateDropdownSettings: IDropdownSettings;

  public submitted = false;

  public formSubmitted = false;

  public ProjectId: any;

  public ParentCompanyId: any;

  public loader = false;

  public modalLoader = false;

  public endTime: Date;

  public startTime: Date;

  public selectedRecurrence = 'Does Not Repeat';

  public isAllDayChosen = false;

  public todayDate = new Date();

  public checkform: any = new UntypedFormArray([]);

  public monthlyLastDayOfWeek = '';

  public enableOption = false;

  public recurrence = recurrence;

  public repeatWithSingleRecurrence = repeatWithSingleRecurrence;

  public repeatWithMultipleRecurrence = repeatWithMultipleRecurrence;

  public weekDays: any = weekDays;

  public dateArg: any;

  public isRepeatWithMultipleRecurrence = false;

  public isRepeatWithSingleRecurrence = false;

  public valueExists = [];

  public showRecurrenceTypeDropdown = false;

  public message = '';

  public monthlyDayOfWeek = '';

  public yearlyMonth = '';

  public monthlyDate = '';

  public authUser: any = {};

  public dropdownSettings: IDropdownSettings;

  public timezonevalues: any;

  public defaultRecurrence;

  public getSelectedTimeZone;

  public noEquipmentOption = { id: 0, equipmentName: 'No Equipment Needed' };

  public defaultValue;
  locationList: any;
  gateList: any;
  equipmentList: any;
  timeZone: any;
  selectedLocationId: any;

  public constructor(
    private readonly formBuilder: UntypedFormBuilder,
    private readonly modalRef: BsModalRef,
    private modalRef1: BsModalRef,
    private readonly modalService: BsModalService,
    public readonly projectService: ProjectService,
    private readonly toastr: ToastrService,
    public calendarService: CalendarService,
    private readonly deliveryService: DeliveryService,
  ) {
    this.defaultRecurrence = this.recurrence[0].value;
    this.projectService.projectParent.subscribe((response7): void => {
      if (response7 !== undefined && response7 !== null && response7 !== '') {
        this.loader = true;
        this.ProjectId = response7.ProjectId;
        this.ParentCompanyId = response7.ParentCompanyId;
        this.loader = true;
      }
    });
    this.getTimeZoneList();
    this.addCalendarEvent();

    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
      }
    });
    if (this.defaultRecurrence === 'Does Not Repeat') {
      this.calendarEvent.get('repeatEveryType').setValue('');
    } else {
      this.calendarEvent.get('repeatEveryCount').setValue(1);
    }
  }

  public ngOnInit(): void {
    this.getSelectedDate();
    this.getLocations();
  }

  public toggleAllDay(data): void {
    if (data) {
      this.isAllDayChosen = true;
      this.resetTimer();
    } else {
      this.isAllDayChosen = false;
      this.setCurrentTiming();
    }
  }

  public timeZoneSelected(id): void {
    const getSelected = this.timezoneList.find((obj: any): any => +obj.id === +id);
    this.selectedValue = getSelected;
  }


  public getLocations(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getLocations(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.locationList = data;
        if(data[0].gateDetails){
          this.gateList = data[0].gateDetails
        }else{
          this.gateList = []
        }
        this.equipmentList = [this.noEquipmentOption, ...( data[0].EquipmentId?data[0].EquipmentId:[])];

        this.equipmentDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'equipmentName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
        this.gateDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'gateName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
        this.locationDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'locationPath',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
      }
    });
  }

  public getTimeZoneList(): void {
    this.projectService.getTimeZoneList().subscribe({
      next: (response: any): void => {
        this.loader = true;
        if (response) {
          const params = {
            ProjectId: this.ProjectId,
          };
          if (params.ProjectId) {
            this.projectService.getSingleProject(params).subscribe((projectList: any): void => {
              if (projectList) {
                this.timezoneList = response.data;
                this.dropdownSettings = {
                  singleSelection: true,
                  idField: 'id',
                  textField: 'location',
                  allowSearchFilter: true,
                  closeDropDownOnSelection: true,
                };
                this.getSelectedTimeZone = this.timezoneList.filter(
                  (obj: any): any => +obj.id === +projectList.data.TimeZoneId,
                );
                this.defaultValue = this.getSelectedTimeZone;
                this.loader = false;
              }
            });
          }
        }
      },
      error: (getTimeZoneListErr): void => {
        if (getTimeZoneListErr.message?.statusCode === 400) {
          this.showError(getTimeZoneListErr);
        } else if (!getTimeZoneListErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(getTimeZoneListErr.message, 'OOPS!');
        }
      },
    });
  }

  public getSelectedDate(): void {
    const getData = this.data;
    if (getData) {
      if (getData.date && getData.currentView === 'Month') {
        this.calendarEvent
          .get('fromDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00'));
        this.calendarEvent
          .get('toDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00'));
        this.calendarEvent
          .get('endDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00'));
      }
      if (getData.date && getData.currentView === 'Week') {
        this.calendarEvent
          .get('fromDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00:00'));
        this.calendarEvent
          .get('toDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00:00'));
        this.calendarEvent
          .get('startTime')
          .setValue(moment(getData.date, 'YYYY-MM-DD hh:mm:ss').format('MM-DD-YYYY hh:mm:ss'));
        this.calendarEvent
          .get('endTime')
          .setValue(moment(getData.date, 'YYYY-MM-DD hh:mm:ss').add(30, 'minutes').format());
        this.calendarEvent
          .get('endDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00'));
      }
      if (getData.date && getData.currentView === 'Day') {
        this.calendarEvent
          .get('fromDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00'));
        this.calendarEvent
          .get('toDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00'));
        this.calendarEvent
          .get('startTime')
          .setValue(moment(getData.date, 'YYYY-MM-DD hh:mm:ss').format('MM-DD-YYYY hh:mm:ss'));
        this.calendarEvent
          .get('endTime')
          .setValue(moment(getData.date, 'YYYY-MM-DD hh:mm:ss').add(30, 'minutes').format());
        this.calendarEvent
          .get('endDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00'));
      }
    }
  }

  public onRecurrenceSelect(value): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    this.selectedRecurrence = value;
    this.setFormValue(value);

    if (this.calendarEvent.get('repeatEveryCount').value > 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.checkform = this.calendarEvent.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day11: any): void => {
        const dayObj11 = day11;
        dayObj11.checked = true;
        dayObj11.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj11.value));
        return dayObj11;
      });
    }
    if (this.calendarEvent.get('repeatEveryCount').value > 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.weekDays = this.weekDays.map((day12: any): void => {
        const dayObj12 = day12;
        if (dayObj12.value === 'Monday') {
          dayObj12.checked = true;
        } else {
          dayObj12.checked = false;
        }
        dayObj12.isDisabled = false;
        return dayObj12;
      });
      this.checkform = this.calendarEvent.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.calendarEvent.get('repeatEveryCount').value === 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.weekDays = this.weekDays.map((day13: any): void => {
        const dayObj13 = day13;
        if (dayObj13.value === 'Monday') {
          dayObj13.checked = true;
        } else {
          dayObj13.checked = false;
        }
        dayObj13.isDisabled = false;
        return dayObj13;
      });
      this.checkform = this.calendarEvent.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.calendarEvent.get('repeatEveryCount').value === 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.checkform = this.calendarEvent.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day14: any): void => {
        const dayObj14 = day14;
        dayObj14.checked = true;
        dayObj14.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj14.value));
        return dayObj14;
      });
    }
    if (
      this.calendarEvent.get('repeatEveryCount').value === 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.calendarEvent.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showMonthlyRecurrence();
    }
    if (
      this.calendarEvent.get('repeatEveryCount').value > 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.calendarEvent.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.showMonthlyRecurrence();
    }
    this.occurMessage();
  }

  public setFormValue(value) {
    if (value === 'Does Not Repeat') {
      this.calendarEvent.get('repeatEveryType').setValue('');
    } else {
      this.calendarEvent.get('repeatEveryCount').setValue(1);
    }
    if (value === 'Daily') {
      this.calendarEvent.get('repeatEveryType').setValue('Day');
    }
    if (value === 'Weekly') {
      this.calendarEvent.get('repeatEveryType').setValue('Week');
    }
    if (value === 'Monthly') {
      this.calendarEvent.get('repeatEveryType').setValue('Month');
    }
    if (value === 'Yearly') {
      this.calendarEvent.get('repeatEveryType').setValue('Year');
    }
  }

  public showMonthlyRecurrence(): void {
    if (this.calendarEvent.get('fromDate').value) {
      const startDate = moment(this.calendarEvent.get('fromDate').value).format('YYYY-MM');
      const chosenDay = moment(this.calendarEvent.get('fromDate').value).format('dddd');
      this.monthlyDate = moment(this.calendarEvent.get('fromDate').value).format('DD');
      const day = moment(startDate, 'YYYY-MM').startOf('month').day(chosenDay);
      const getAllDays = [];
      if (day.date() > 7) day.add(7, 'd');
      const month = day.month();
      while (month === day.month()) {
        getAllDays.push(day.format('YYYY-MM-DD'));
        day.add(7, 'd');
      }
      let week;
      let extraOption;
      this.enableOption = false;
      getAllDays.forEach((element, i): void => {
        if (
          moment(this.calendarEvent.get('fromDate').value).format('YYYY-MM-DD')
          === moment(element).format('YYYY-MM-DD')
        ) {
          const number = i + 1;
          if (number === 1) {
            week = 'First';
          }
          if (number === 2) {
            week = 'Second';
          }
          if (number === 3) {
            week = 'Third';
          }
          if (number === 4) {
            this.enableOption = true;
            extraOption = 'Last';
            week = 'Fourth';
          }
          if (number === 5) {
            week = 'Last';
          }
          if (number === 6) {
            week = 'Last';
          }
        }
      });
      this.monthlyDayOfWeek = `${week} ${chosenDay}`;
      this.monthlyLastDayOfWeek = `${extraOption} ${chosenDay}`;
      if (!this.enableOption && this.calendarEvent.get('chosenDateOfMonth').value === 3) {
        this.calendarEvent.get('chosenDateOfMonth').setValue(2);
        this.calendarEvent.get('dateOfMonth').setValue(null);
        this.calendarEvent.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
      }
      this.setMonthlyOrYearlyRecurrenceOption();
      this.occurMessage();
    }
  }

  public changeMonthlyRecurrence(): void {
    this.setMonthlyOrYearlyRecurrenceOption();
    this.updateFormValidation();
    this.showMonthlyRecurrence();
    this.occurMessage();
  }

  public setMonthlyOrYearlyRecurrenceOption(): void {
    if (this.calendarEvent.get('chosenDateOfMonth').value === 1) {
      this.calendarEvent
        .get('dateOfMonth')
        .setValue(moment(this.calendarEvent.get('fromDate').value).format('DD'));
      this.calendarEvent.get('monthlyRepeatType').setValue(null);
    } else if (this.calendarEvent.get('chosenDateOfMonth').value === 2) {
      this.calendarEvent.get('dateOfMonth').setValue(null);
      this.calendarEvent.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
    } else if (this.calendarEvent.get('chosenDateOfMonth').value === 3) {
      this.calendarEvent.get('dateOfMonth').setValue(null);
      this.calendarEvent.get('monthlyRepeatType').setValue(this.monthlyLastDayOfWeek);
    }
  }

  public chooseRepeatEveryType(value): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    if (value === 'Day' || value === 'Days') {
      this.calendarEvent.get('recurrence').setValue('Daily');
    }
    if (value === 'Week' || value === 'Weeks') {
      this.calendarEvent.get('recurrence').setValue('Weekly');
    }
    if (value === 'Month' || value === 'Months') {
      this.calendarEvent.get('recurrence').setValue('Monthly');
    }
    if (value === 'Year' || value === 'Years') {
      this.calendarEvent.get('recurrence').setValue('Yearly');
    }
    if (value === 'Day' || value === 'Days') {
      this.checkform = this.calendarEvent.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day: any): any => {
        const dayObj = day;
        dayObj.checked = true;
        dayObj.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj.value));
        return dayObj;
      });
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.showRecurrenceTypeDropdown = true;
    }
    if (value === 'Week' || value === 'Weeks') {
      this.weekDays = this.weekDays.map((day15: any): void => {
        const dayObj15 = day15;
        if (dayObj15.value === 'Monday') {
          dayObj15.checked = true;
        } else {
          dayObj15.checked = false;
        }
        dayObj15.isDisabled = false;
        return dayObj15;
      });
      this.checkform = this.calendarEvent.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (value === 'Day' || value === 'Week' || value === 'Month' || value === 'Year') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showRecurrenceTypeDropdown = false;
    }
    if (value === 'Days' || value === 'Weeks' || value === 'Months' || value === 'Years') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.showRecurrenceTypeDropdown = false;
    }
    if (this.calendarEvent.get('repeatEveryCount').value > 1) {
      this.showRecurrenceTypeDropdown = true;
      this.isRepeatWithMultipleRecurrence = false;
    }
    this.selectedRecurrence = this.calendarEvent.get('recurrence').value;
    this.occurMessage();
  }

  public onChange(event): void {
    this.checkform = this.calendarEvent.get('days') as UntypedFormArray;
    this.valueExists = this.checkform.controls.filter(
      (object): any => object.value === event.target.value,
    );
    if (event.target.checked) {
      this.checkform.push(new UntypedFormControl(event.target.value));
      this.weekDays = this.weekDays.map((day16: any): void => {
        const dayObj16 = day16;
        if (day16.value === event.target.value) {
          dayObj16.checked = true;
        }
        return dayObj16;
      });
      if (this.checkform.controls.length === 2) {
        this.weekDays = this.weekDays.map((day17: any): void => {
          const dayObj17 = day17;
          dayObj17.isDisabled = false;
          return dayObj17;
        });
      }
    } else if (this.selectedRecurrence === 'Weekly') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day18: any): void => {
            const dayObj18 = day18;
            if (dayObj18.value === event.target.value) {
              dayObj18.checked = false;
            }
            return dayObj18;
          });
        }
        if (this.checkform.controls.length === 1) {
          this.weekDays = this.weekDays.map((day19: any): void => {
            const dayObj19 = day19;
            if (dayObj19.value === this.checkform.controls[0].value) {
              dayObj19.isDisabled = true;
              dayObj19.checked = true;
            }
            return dayObj19;
          });
          return;
        }
        i += 1;
      });
    } else if (this.selectedRecurrence === 'Daily') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day): void => {
            const dayObj = day;
            if (dayObj.value === event.target.value) {
              dayObj.checked = false;
              dayObj.isDisabled = false;
            }
            return dayObj;
          });
          return;
        }
        i += 1;
      });
    }
    if (this.checkform.controls.length !== 7) {
      this.calendarEvent.get('recurrence').setValue('Weekly');
      if (+this.calendarEvent.get('repeatEveryCount').value === 1) {
        this.calendarEvent.get('repeatEveryType').setValue('Week');
      } else {
        this.calendarEvent.get('repeatEveryType').setValue('Weeks');
      }
      this.selectedRecurrence = this.calendarEvent.get('recurrence').value;
    }
    if (this.checkform.controls.length === 7) {
      this.calendarEvent.get('recurrence').setValue('Daily');
      if (+this.calendarEvent.get('repeatEveryCount').value === 1) {
        this.calendarEvent.get('repeatEveryType').setValue('Day');
      } else {
        this.calendarEvent.get('repeatEveryType').setValue('Days');
      }
      this.selectedRecurrence = this.calendarEvent.get('recurrence').value;
    }
    this.occurMessage();
  }

  public changeRecurrenceCount(value): void {
    const recurrencedata = this.calendarEvent.get('recurrence').value;
    const count = +value;

    if (count <= 0) {
      this.calendarEvent.get('repeatEveryCount').setValue(1);
      return;
    }

    this.selectedRecurrence = recurrencedata;

    // Set recurrence flags
    this.isRepeatWithSingleRecurrence = count === 1;
    this.isRepeatWithMultipleRecurrence = count > 1;
    this.showRecurrenceTypeDropdown = recurrencedata === 'Daily' && count > 1;

    let repeatEveryType = '';

    switch (recurrencedata) {
      case 'Daily':
        repeatEveryType = count === 1 ? 'Day' : 'Days';
        break;
      case 'Weekly':
        repeatEveryType = count === 1 ? 'Week' : 'Weeks';
        break;
      case 'Monthly':
        repeatEveryType = count === 1 ? 'Month' : 'Months';
        this.changeMonthlyRecurrence();
        this.showMonthlyRecurrence();
        break;
      case 'Yearly':
        repeatEveryType = count === 1 ? 'Year' : 'Years';
        break;
      default:
        break;
    }

    this.calendarEvent.get('repeatEveryType').setValue(repeatEveryType);
    this.occurMessage();
  }


  public occurMessage(): void {
    this.message = this.generateRepeatMessage();
    this.appendEndDateMessage();
  }

  public generateRepeatMessage(): string {
    const type = this.calendarEvent.get('repeatEveryType').value;
    const count = +this.calendarEvent.get('repeatEveryCount').value;
    let message = '';

    switch (type) {
      case 'Day':
        message = 'Occurs every day';
        break;
      case 'Days':
        message = count === 2
          ? 'Occurs every other day'
          : `Occurs every ${count} days`;
        break;
      case 'Week':
      case 'Weeks': {
        const weekDaysData = this.weekDays
          .filter((day: any) => day.checked)
          .map((day: any) => day.value)
          .join(', ');

        if (type === 'Week') {
          message = `Occurs every ${weekDaysData}`;
        } else {
          message = count === 2
            ? `Occurs every other ${weekDaysData}`
            : `Occurs every ${count} weeks on ${weekDaysData}`;
        }
        break;
      }
      case 'Month':
      case 'Months':
      case 'Year':
      case 'Years': {
        const choice = this.calendarEvent.get('chosenDateOfMonth').value;
        if (choice === 1) {
          message = `Occurs on day ${this.monthlyDate}`;
        } else if (choice === 2) {
          message = `Occurs on the ${this.monthlyDayOfWeek}`;
        } else {
          message = `Occurs on the ${this.monthlyLastDayOfWeek}`;
        }
        break;
      }
      default:
        break;
    }

    return message;
  }

  public appendEndDateMessage(): void {
    const recurrencedata = this.calendarEvent.get('recurrence').value;
    if (!this.message) return;

    if (recurrencedata === 'Daily') {
      this.message += ` until ${moment(this.calendarEvent.get('toDate').value).format('MMMM DD, YYYY')}`;
    } else if (['Weekly', 'Monthly', 'Yearly'].includes(recurrencedata)) {
      this.message += ` until ${moment(this.calendarEvent.get('endDate').value).format('MMMM DD, YYYY')}`;
    }
  }


  public numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public close(template: TemplateRef<any>): void {
    if (this.calendarEvent.touched && this.calendarEvent.dirty) {
      this.openConfirmationModalPopup(template);
    } else {
      this.resetForm('yes');
    }
  }

  public openConfirmationModalPopup(template): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public resetForm(action): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.submitted = false;
      this.formSubmitted = false;
      this.modalRef.hide();
      this.selectedRecurrence = 'Does Not Repeat';
      this.calendarEvent.reset();
    }
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;

    if (this.calendarEvent.invalid) {
      this.formSubmitted = false;
      return;
    }

    if (
      !this.calendarEvent.get('isApplicableToDelivery').value &&
      !this.calendarEvent.get('isApplicableToCrane').value &&
      !this.calendarEvent.get('isApplicableToConcrete').value &&
      !this.calendarEvent.get('isApplicableToInspection').value
    ) {
      this.formSubmitted = false;
      return;
    }

    if (!this.calendarEvent.value.isAllDay) {
      const startTime = new Date(this.calendarEvent.value.startTime).getTime() / 1000;
      const endTime = new Date(this.calendarEvent.value.endTime).getTime() / 1000;
      if (startTime === endTime) {
        this.toastr.error('Start time and End time cannot be the same');
        this.formSubmitted = false;
        return;
      }
      if (endTime < startTime) {
        this.toastr.error('End time must be greater than Start time');
        this.formSubmitted = false;
        return;
      }
    }

    const today = moment().format('MM-DD-YYYY');

    const checkDateValidity = (from, to): boolean => {
      const fromDate = moment(from).format('MM-DD-YYYY');
      const toDate = moment(to).format('MM-DD-YYYY');
      if (new Date(fromDate).getTime() / 1000 > new Date(toDate).getTime() / 1000) {
        this.toastr.error('End Date must be greater than Start Date.!');
        return false;
      }
      if (
        new Date(fromDate).getTime() / 1000 <= new Date(today).getTime() / 1000 ||
        new Date(toDate).getTime() / 1000 <= new Date(today).getTime() / 1000
      ) {
        this.toastr.error('Please select Future Date.!');
        return false;
      }
      return true;
    };

    const formValue = this.calendarEvent.value;

    if (formValue.fromDate && formValue.toDate
      && !checkDateValidity(formValue.fromDate, formValue.toDate)) {
      this.formSubmitted = false;
      return;
    }

    if (
      formValue.recurrence !== 'Daily'
      && formValue.recurrence !== 'Does Not Repeat'
      && formValue.fromDate
      && formValue.endDate
      && !checkDateValidity(formValue.fromDate, formValue.endDate)
    ) {
      this.formSubmitted = false;
      return;
    }

    this.processValidForm(formValue);
  }

  public processValidForm(formValue: any): void {
    const newtimezoneDetails = formValue.TimeZoneId;

    if (newtimezoneDetails?.length > 0) {
      newtimezoneDetails.forEach((element: { id: any }): void => {
        this.timezonevalues = element.id;
      });
    }

    let equipments : any = [];

      if (formValue.EquipmentId.length > 0) {
        formValue.EquipmentId.forEach((element: { id: any }): void => {
          equipments.push(element.id);
        });
      }


      let gate : any = [];

      if (formValue.GateId.length > 0) {
        formValue.GateId.forEach((element: { id: any }): void => {
          gate.push(element.id);
        });
      }


      let locations : any = [];

      if (formValue.LocationId.length > 0) {
        formValue.LocationId.forEach((element: { id: any }): void => {
          locations.push(element.id);
        });
      }


    const payload = {
      description: formValue.description,
      fromDate: moment(formValue.fromDate).format('YYYY MM DD 00:00:00'),
      toDate: moment(formValue.toDate).format('YYYY MM DD 00:00:00'),
      startTime: moment(formValue.startTime).format('HH:mm'),
      endTime: moment(formValue.endTime).format('HH:mm'),
      TimeZoneId: this.timezonevalues,
      GateId: gate,
      LocationId: locations,
      EquipmentId: equipments,
      isAllDay: formValue.isAllDay,
      isApplicableToDelivery: formValue.isApplicableToDelivery,
      isApplicableToCrane: formValue.isApplicableToCrane,
      isApplicableToConcrete: formValue.isApplicableToConcrete,
      isApplicableToInspection: formValue.isApplicableToInspection,
      recurrence: formValue.recurrence,
      chosenDateOfMonth: formValue.chosenDateOfMonth === 1,
      dateOfMonth: formValue.dateOfMonth,
      monthlyRepeatType: formValue.monthlyRepeatType,
      days: formValue.days ? this.sortWeekDays(formValue.days) : [],
      repeatEveryType: formValue.repeatEveryType || null,
      repeatEveryCount: formValue.repeatEveryCount ? formValue.repeatEveryCount.toString() : null,
      createdBy: this.authUser.RoleId,
      endDate: moment(formValue.toDate).format('YYYY MM DD 00:00:00'),
    };

    if (payload.isAllDay) {
      payload.startTime = '00:00';
      payload.endTime = '00:00';
    }

    if (['Monthly', 'Yearly'].includes(payload.recurrence)) {
      payload.dateOfMonth = this.monthlyDate;
    }

    if (['Monthly', 'Yearly', 'Weekly'].includes(payload.recurrence)) {
      payload.endDate = moment(formValue.endDate).format('YYYY MM DD 00:00:00');
    }

    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };

    this.calendarService.addCalendarEvent(params, payload).subscribe({
      next: (response): void => {
        if (response) {
          this.modalRef.hide();
          this.toastr.success(response.message, 'Success');
          this.selectedRecurrence = 'Does Not Repeat';
          this.calendarService.updateCalendarEvents(true);
          this.submitted = false;
          this.formSubmitted = false;
          this.calendarEvent.reset();
        }
      },
      error: (addCalendarEventError): void => {
        if (addCalendarEventError.message?.statusCode === 400) {
          this.showError(addCalendarEventError);
        } else if (!addCalendarEventError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(addCalendarEventError.message, 'OOPS!');
        }
        this.submitted = false;
        this.formSubmitted = false;
      },
    });
  }


  public sortWeekDays(data: any[]): any {
    const order = {
      Sunday: 1,
      Monday: 2,
      Tuesday: 3,
      Wednesday: 4,
      Thursday: 5,
      Friday: 6,
      Saturday: 7,
    };

    if (data.length > 0) {
      return data.sort((a, b): any => order[a] - order[b]);
    }
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public resetTimer(): void {
    this.startTime = new Date();
    this.startTime.setHours(0);
    this.startTime.setMinutes(0);
    this.endTime = new Date();
    this.endTime.setHours(0);
    this.endTime.setMinutes(0);
    this.calendarEvent.get('startTime').setValue(this.startTime);
    this.calendarEvent.get('endTime').setValue(this.endTime);
  }

  public addCalendarEvent(): void {
    this.calendarEvent = this.formBuilder.group({
      description: ['', Validators.compose([Validators.required])],
      fromDate: ['', Validators.compose([Validators.required])],
      toDate: ['', Validators.compose([Validators.required])],
      GateId: [''],
      LocationId: [''],
      EquipmentId: [''],
      endDate: [''],
      isAllDay: [false, Validators.compose([Validators.required])],
      isApplicableToDelivery: [false, Validators.compose([Validators.required])],
      isApplicableToCrane: [false, Validators.compose([Validators.required])],
      isApplicableToConcrete: [false, Validators.compose([Validators.required])],
      isApplicableToInspection: [false, Validators.compose([Validators.required])],
      recurrence: ['', Validators.compose([Validators.required])],
      repeatEveryCount: [''],
      repeatEveryType: [''],
      days: new UntypedFormArray([]),
      startTime: [''],
      endTime: [''],
      chosenDateOfMonth: [false, ''],
      dateOfMonth: [''],
      monthlyRepeatType: [''],
      TimeZoneId: ['', Validators.compose([Validators.required])],
    });
    this.formControlValueChanged();
    this.setCurrentTiming();
    const startTime = this.calendarEvent.get('startTime');
    const endTime = this.calendarEvent.get('endTime');
    this.calendarEvent.get('recurrence').setValue(this.defaultRecurrence);
    if (this.calendarEvent.get('isAllDay').value === false) {
      startTime.setValidators([Validators.required]);
      endTime.setValidators([Validators.required]);
    } else if (this.calendarEvent.get('isAllDay').value === true) {
      startTime.clearValidators();
      endTime.clearValidators();
    }
    startTime.updateValueAndValidity();
    endTime.updateValueAndValidity();
  }

  public setCurrentTiming(): void {
    const newDate = moment().format('MM-DD-YYYY');
    const hours = moment(new Date()).format('HH');
    this.startTime = new Date();
    this.startTime.setHours(+hours);
    this.startTime.setMinutes(0);
    this.endTime = new Date();
    this.endTime.setHours(+hours);
    this.endTime.setMinutes(30);
    this.calendarEvent.get('startTime').setValue(this.startTime);
    this.calendarEvent.get('endTime').setValue(this.endTime);
    if (!this.calendarEvent.get('fromData')?.value) {
      this.calendarEvent.get('fromDate').setValue(newDate);
    }
    if (!this.calendarEvent.get('toDate')?.value) {
      this.calendarEvent.get('toDate').setValue(newDate);
    }
    if (!this.calendarEvent.get('endDate')?.value) {
      this.calendarEvent.get('endDate').setValue(newDate);
    }
    this.changeMonthlyRecurrence();
  }

  public updateFormValidation(): void {
    const chosenDateOfMonth = this.calendarEvent.get('chosenDateOfMonth');
    const dateOfMonth = this.calendarEvent.get('dateOfMonth');
    const monthlyRepeatType = this.calendarEvent.get('monthlyRepeatType');
    if (this.calendarEvent.get('chosenDateOfMonth').value === 1) {
      dateOfMonth.setValidators([Validators.required]);
      monthlyRepeatType.clearValidators();
    } else {
      monthlyRepeatType.setValidators([Validators.required]);
      dateOfMonth.clearValidators();
    }
    chosenDateOfMonth.updateValueAndValidity();
    dateOfMonth.updateValueAndValidity();
    monthlyRepeatType.updateValueAndValidity();
  }


  public locationSelected(data: { id: string | number }): void {
    const getChosenLocation = this.locationList.find((obj: any): any => +obj.id === +data.id);
    if (getChosenLocation) {
      this.calendarEvent.get('GateId').setValue('');
      this.calendarEvent
        .get('EquipmentId')
        .setValue('');
      this.gateList = []
      this.equipmentList = []
      if(getChosenLocation.gateDetails){
        this.gateList = getChosenLocation.gateDetails;
        this.equipmentList =  [this.noEquipmentOption, ...getChosenLocation.EquipmentId];
        this.timeZone = getChosenLocation.TimeZoneId[0].location
      }
    }
    this.selectedLocationId = getChosenLocation?.id;
  }


  public formControlValueChanged(): void {
    const startTime = this.calendarEvent.get('startTime');
    const endTime = this.calendarEvent.get('endTime');
    this.calendarEvent.get('isAllDay').valueChanges.subscribe((value: boolean): void => {
      if (value === false) {
        startTime.setValidators([Validators.required]);
        endTime.setValidators([Validators.required]);
      } else if (value === true) {
        startTime.clearValidators();
        endTime.clearValidators();
      }
      startTime.updateValueAndValidity();
      endTime.updateValueAndValidity();
    });
    this.calendarEvent.get('repeatEveryType').valueChanges.subscribe((value: string): void => {
      const days = this.calendarEvent.get('days');
      const chosenDateOfMonth = this.calendarEvent.get('chosenDateOfMonth');
      const dateOfMonth = this.calendarEvent.get('dateOfMonth');
      const monthlyRepeatType = this.calendarEvent.get('monthlyRepeatType');
      if (value === 'Week' || value === 'Day' || value === 'Weeks') {
        days.setValidators([Validators.required]);
      } else {
        days.clearValidators();
      }
      if (value === 'Month' || value === 'Months' || value === 'Year' || value === 'Years') {
        if (this.calendarEvent.get('chosenDateOfMonth').value === 1) {
          dateOfMonth.setValidators([Validators.required]);
          monthlyRepeatType.clearValidators();
        } else {
          monthlyRepeatType.setValidators([Validators.required]);
          dateOfMonth.clearValidators();
        }
      } else {
        chosenDateOfMonth.clearValidators();
        dateOfMonth.clearValidators();
        monthlyRepeatType.clearValidators();
      }
      chosenDateOfMonth.updateValueAndValidity();
      dateOfMonth.updateValueAndValidity();
      monthlyRepeatType.updateValueAndValidity();
      days.updateValueAndValidity();
    });
  }

  public checkEquipmentType(value: any[]): void {
    let hasNoEquipmentOption = false;
    let hasOtherEquipment = false;
    if (value) {
      if(value.length == this.equipmentList.length -1 && this.equipmentList[0].id == 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
      }
      if(value.length != this.equipmentList.length && this.equipmentList[0].id != 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
      }
      if(value.length == 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
      }
      // Check if "No Equipment Needed" (id = 0) is selected
      hasNoEquipmentOption = value.some((item: any) => item.id === 0);

      // Check if other equipment is selected
      hasOtherEquipment = value.some((item: any) => item.id !== 0);

      const previousSelection = this.calendarEvent.get('EquipmentId').value || [];
      const previousHasOther = previousSelection.some((item: any) => item.id !== 0);

      // Rule 1: If "No Equipment Needed" is selected and other items are selected, keep only "No Equipment Needed"
      if (hasNoEquipmentOption && hasOtherEquipment && !previousHasOther) {
        this.toastr.warning('When "No Equipment Needed" is selected, other equipment options cannot be selected.', 'Warning');
        const noEquipmentOnly = value.filter((item: any) => item.id === 0);
        this.calendarEvent.get('EquipmentId').setValue(noEquipmentOnly);
        value = noEquipmentOnly;
        hasOtherEquipment = false;
      }

      // Rule 2: If other equipment is already selected and "No Equipment Needed" is now selected, remove it
      if (previousHasOther && hasNoEquipmentOption) {
        this.toastr.warning('When other equipment is selected, "No Equipment Needed" cannot be selected.', 'Warning');
        const filteredSelection = value.filter((item: any) => item.id !== 0);
        this.calendarEvent.get('EquipmentId').setValue(filteredSelection);
        value = filteredSelection;
        hasNoEquipmentOption = false;
      }
    }

  }
}
