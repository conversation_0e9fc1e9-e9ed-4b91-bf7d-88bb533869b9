import {
  Component, Input, OnInit, TemplateRef,
} from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import {
  UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators,
} from '@angular/forms';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import moment from 'moment';
import { Observable, of } from 'rxjs';
import { ReportsService } from '../../services/reports/reports.service';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import {
  weekDays, recurrence, repeatWithSingleRecurrence, repeatWithMultipleRecurrence,
} from '../../services/common';

@Component({
  selector: 'app-scheduler-form',
  templateUrl: './scheduler-form.component.html',
})
export class SchedulerFormComponent implements OnInit {
  @Input('ProjectId') public ProjectId: any;

  @Input('ParentCompanyId') public ParentCompanyId: any;

  @Input('reportType') public reportType: any;

  @Input('updatedMemberList') public updatedMemberList: any;

  @Input('exportType') public exportType = [];

  @Input('filterPayload') public filterPayload: any;

  @Input('selectedHeaders') public selectedHeaders: any;

  @Input('sort') public sort: any;

  @Input('sortByField') public sortByField: any;

  public scheduleForm: UntypedFormGroup;

  public selectedRecurrence = 'Does Not Repeat';

  public recurrenceData = ['Does Not Repeat', 'Daily', 'Weekly', 'Monthly', 'Yearly'];

  public bsInlineValue = new Date();

  public bsInlineRangeValue: Date[];

  public maxDate = new Date();

  public scheduleSendTo: IDropdownSettings = {
    singleSelection: false,
    idField: 'id',
    textField: 'UserEmail',
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    itemsShowLimit: 6,
    allowSearchFilter: true,
  };

  public currentMonth: string;

  public showRecurrenceTypeDropdown = false;

  public isRepeatWithMultipleRecurrence = false;

  public isRepeatWithSingleRecurrence = false;

  public authUser: any = {};

  public message = '';

  public monthlyDayOfWeek = '';

  public yearlyMonth = '';

  public monthlyDate = '';

  public monthlyLastDayOfWeek = '';

  public recurrence = recurrence;

  public repeatWithSingleRecurrence = repeatWithSingleRecurrence;

  public repeatWithMultipleRecurrence = repeatWithMultipleRecurrence;

  public weekDays: any = weekDays;

  public enableOption = false;

  public defaultRecurrence;

  public endTime: Date;

  public startTime: any;

  public checkform: any = new UntypedFormArray([]);

  public valueExists = [];

  public week = 'first';

  public chosenDay = 'SUN';

  public extraOption;

  public outputFormat = 'PDF';

  public defaultFormat: string;

  public formSubmitted = false;

  public submitted = false;

  public chosenMonthNumber: string;

  public modalRef?: BsModalRef;

  public timeSlot = [];

  public order = 0;

  public timelineId = 0;

  public constructor(
    private readonly modalService: BsModalService,
    private readonly reportsService: ReportsService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly toastr: ToastrService,
    public modalRef1: BsModalRef,
    private readonly projectService: ProjectService,
    private readonly deliveryService: DeliveryService,
  ) {
    this.projectService.projectParent.subscribe((response19): void => {
      if (response19 !== undefined && response19 !== null && response19 !== '') {
        this.ProjectId = response19.ProjectId;
        this.ParentCompanyId = response19.ParentCompanyId;
        this.setDefaultPerson();
        this.getTimeLine();
      }
    });
    this.defaultRecurrence = this.recurrence[0].value;
    this.bsInlineRangeValue = [this.bsInlineValue, this.bsInlineValue];
  }

  public ngOnInit(): void {
    this.scheduleDetailsForm();
    if (this.defaultRecurrence === 'Does Not Repeat') {
      this.scheduleForm.get('repeatEveryType').setValue('');
    } else {
      this.scheduleForm.get('repeatEveryCount').setValue(1);
    }
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.openModal(data);
    }
  }

  public scheduleDetailsForm(): void {
    this.scheduleForm = this.formBuilder.group({
      reportName: ['', Validators.compose([Validators.required])],
      startDate: ['', Validators.compose([Validators.required])],
      sendTo: ['', Validators.compose([Validators.required])],
      subject: ['', Validators.compose([Validators.required])],
      message: ['', Validators.compose([Validators.required])],
      outputFormat: ['', Validators.compose([Validators.required])],
      endDate: ['', Validators.compose([Validators.required])],
      recurrence: ['', Validators.compose([Validators.required])],
      repeatEveryCount: [''],
      repeatEveryType: [''],
      chosenDateOfMonth: [false, ''],
      repeatMonthDate: [''],
      days: new UntypedFormArray([]),
      startTime: [''],
      endTime: [''],
      dateOfMonth: [''],
      monthlyRepeatType: [''],
    });
    this.formControlValueChanged();
    this.setCurrentTiming();
    this.scheduleForm.get('outputFormat').setValue(this.outputFormat);
    this.scheduleForm.get('recurrence').setValue(this.defaultRecurrence);
    this.scheduleForm.get('repeatMonthDate').setValue(1);
    this.scheduleForm.get('startTime').setValue(new Date());
  }

  public scheduleSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    if (this.scheduleForm.invalid) {
      this.formSubmitted = false;
      return;
    }
    if (this.scheduleForm.valid) {
      this.scheduleReports();
    }
  }

  public buildRepeatEveryPayload(): any {
    const recurrenceEndDate = this.scheduleForm.value.endDate
      ? moment(this.scheduleForm.value.endDate).format('YYYY-MM-DD')
      : null;

    const repeatEveryCount = Number(this.scheduleForm.value.repeatEveryCount);

    let specificDays;

    if (this.scheduleForm.value.chosenDateOfMonth === 1) {
      specificDays = { day: Number(this.monthlyDate) };
    } else if (this.scheduleForm.value.chosenDateOfMonth === 2) {
      specificDays = {
        order: this.week.toLocaleLowerCase(),
        specificDay: this.chosenDay.slice(0, 3).toUpperCase(),
      };
    }

    switch (this.selectedRecurrence) {
      case 'Daily':
        return { day: repeatEveryCount, recurrenceEndDate };
      case 'Weekly':
        return {
          day: repeatEveryCount,
          specificDays: this.weekDays.filter((obj) => obj.checked).map((obj) => obj.data),
          recurrenceEndDate,
        };
      case 'Monthly':
        return {
          day: repeatEveryCount,
          options: this.scheduleForm.value.chosenDateOfMonth === 1 ? 'on_day' : 'on_the',
          specificDays,
          recurrenceEndDate,
        };
      case 'Yearly':
        if (specificDays) specificDays.month = Number(this.chosenMonthNumber);
        return {
          options: this.scheduleForm.value.chosenDateOfMonth === 1 ? 'on_day' : 'on_the',
          specificDays,
          recurrenceEndDate,
        };
      default:
        return '';
    }
  }

  public scheduleReports(): void {
    const reportPayload: any = this.scheduleRecurrencePayload();
    const payload: any = this.heatMapOrWeeklyCalendarPayload(reportPayload);

    payload.dateRangeId = this.timelineId;
    payload.customStartDate = moment(this.bsInlineRangeValue[0]).format('YYYY-MM-DD');
    payload.customEndDate = moment(this.bsInlineRangeValue[1]).format('YYYY-MM-DD');

    const params = { ProjectId: this.ProjectId };

    if (this.selectedRecurrence === 'Does Not Repeat') {
      payload.repeatEvery = '';
    } else {
      payload.repeatEvery = this.buildRepeatEveryPayload();
    }

    this.createScheduledReports(params, payload);
  }


  public setDefaultPerson(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.getMemberRole(params).subscribe((res): void => {
      const { email } = res.data.User;
      const newMemberList = [
        {
          UserEmail: email,
          id: res.data.id,
        },
      ];
      this.scheduleForm.get('sendTo').patchValue(newMemberList);
    });
  }

  public createScheduledReports(params, payload): void {
    this.reportsService.scheduleHeatMapReports(params, payload).subscribe({
      next: (response: any): void => {
        if (response) {
          if (response.data?.message?.includes('Currently')) {
            this.modalRef1.hide();
            this.toastr.error(response.data.message);
            this.formSubmitted = false;
            return;
          }
          this.modalRef1.hide();
          this.toastr.success('Reports scheduled successfully');
          this.formSubmitted = false;
        } else {
          this.toastr.error('Something went wrong');
          this.formSubmitted = false;
        }
      },
      error: (scheduleError): void => {
        this.showError(scheduleError);
        if (scheduleError.message?.statusCode === 400) {
          this.showError(scheduleError);
        } else if (!scheduleError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(scheduleError.message, 'OOPS!');
        }
        this.formSubmitted = false;
      },
    });

    this.formSubmitted = false;
  }


  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public scheduleRecurrencePayload(): any {
    const basePayload = this.createBasePayload();
    const adjustedPayload = this.adjustForReportType(basePayload);
    const filteredPayload = this.applyFiltersAndTimezone(adjustedPayload);

    return filteredPayload;
  }

  public createBasePayload(): any {
    const {
      reportName: reportNameForm,
      outputFormat: outputFormatForm,
      recurrence: recurrenceForm,
      subject: subjectForm,
      message: messageForm,
      idFilter: idFilterForm,
    } = this.scheduleForm.value;

    const {
      timezone: timezoneFilter,
      defineFilter,
      gateFilter,
      memberFilter,
      statusFilter,
      inspectionStatusFilter,
      inspectionTypeFilter,
      pickFrom,
      primerFilter,
      slumpFilter,
      quantityFilter,
      truckspacingFilter,
      orderNumberFilter,
      pickTo,
      templateType,
      descriptionFilter,
      locationFilter,
    } = this.filterPayload;

    return {
      reportName: reportNameForm ?? null,
      reportType: this.reportType ?? null,
      outputFormat: outputFormatForm ?? 'PDF',
      runReportAt: this.getRunReportAt(),
      recurrence: recurrenceForm ?? null,
      sendTo: this.getSendTo(),
      subject: subjectForm ?? '',
      message: messageForm ?? '',
      timezone: timezoneFilter ?? Intl.DateTimeFormat().resolvedOptions().timeZone,
      companyId: 0,
      defineId: defineFilter ?? 0,
      gateId: gateFilter ?? 0,
      memberId: memberFilter ?? 0,
      status: statusFilter ?? '',
      inspectionStatus: inspectionStatusFilter ?? '',
      inspectionType: inspectionTypeFilter ?? '',
      pickFrom: pickFrom ?? '',
      primerFilter: primerFilter ?? '',
      slumpFilter: slumpFilter ?? '',
      quantityFilter: quantityFilter ?? '',
      truckspacingFilter: truckspacingFilter ?? '',
      orderNumberFilter: orderNumberFilter ?? '',
      pickTo: pickTo ?? '',
      idFilter: idFilterForm ?? 0,
      templateType: templateType ?? [],
      descriptionFilter: descriptionFilter ?? '',
      queuedNdr: false,
      selectedHeaders: this.selectedHeaders,
      parentCompanyId: this.ParentCompanyId,
      sort: this.sort,
      sortByField: this.sortByField,
      locationFilter: locationFilter ?? 0,
    };
  }

  public getRunReportAt(): string | null {
    if (this.scheduleForm.value.startDate && this.scheduleForm.value.startTime) {
      return `${moment(this.scheduleForm.value.startDate, 'MM-DD-YYYY').format('YYYY-MM-DD')} ${moment(
        this.scheduleForm.value.startTime,
      ).format('HH:mm')}`;
    }
    return null;
  }

  public getSendTo(): string[] | null {
    return this.scheduleForm.value.sendTo ? this.scheduleForm.value.sendTo.map((obj): any => obj.UserEmail) : null;
  }

  public adjustForReportType(payload: any): any {
    let adjustedPayload = { ...payload };
    const isDescSortById = this.sort === 'DESC' && this.sortByField === 'id';
    switch (this.reportType) {
      case 'Delivery':
        if (isDescSortById) {
          adjustedPayload.sort = 'ASC';
          adjustedPayload.sortByField = 'deliveryStart';
        }
        break;
      case 'Inspection':
        if (isDescSortById) {
          adjustedPayload.sort = 'ASC';
          adjustedPayload.sortByField = 'inspectionStart';
        }
        break;
      case 'Crane':
        adjustedPayload = this.getCraneFilters(adjustedPayload);
        break;
      case 'Concrete':
        adjustedPayload = this.getConcreteFilters(adjustedPayload);
        break;
      default:
        adjustedPayload = this.getDefaultFilters(adjustedPayload);
        break;
    }
    return adjustedPayload;
  }

  public getCraneFilters(payload: any): any {
    const updated = { ...payload };

    updated.equipmentFilter = this.filterPayload.equipmentFilter || '';
    updated.companyFilter = this.filterPayload.companyFilter || '';
    updated.equipmentId = 0;
    updated.companyId = 0;

    if (this.sort === 'DESC' && this.sortByField === 'CraneRequestId') {
      updated.sort = 'ASC';
      updated.sortByField = 'datetime';
    }

    return updated;
  }

  public getConcreteFilters(payload: any): any {
    const updated = { ...payload };

    updated.equipmentId = 0;
    updated.companyId = 0;
    updated.companyFilter = this.filterPayload.companyFilter || '';

    if (this.sort === 'DESC' && this.sortByField === 'id') {
      updated.sort = 'ASC';
      updated.sortByField = 'concretePlacementStart';
    }
    return updated;
  }

  public getDefaultFilters(payload: any): any {
    const updated = { ...payload };
    updated.equipmentId = this.filterPayload.equipmentFilter || 0;
    updated.companyId = this.filterPayload.companyFilter || 0;
    return updated;
  }


  public applyFiltersAndTimezone(payload: any): any {
    return payload;
  }


  public heatMapOrWeeklyCalendarPayload(data): void {
    const payload = { ...data };

    // Common logic for handling start/end dates and selectedHeaders
    this.setCommonPayloadProperties(payload);

    // Special handling for Heat Map and Weekly Calendar report types
    if (this.reportType === 'Heat Map' || this.reportType === 'Weekly Calendar') {
      this.setHeatMapOrWeeklyCalendarProperties(payload);
    }

    // Special handling for Weekly Calendar
    if (this.reportType === 'Weekly Calendar') {
      this.setWeeklyCalendarTime(payload);
    }

    return payload;
  }

  public setCommonPayloadProperties(payload): void {
    if (this.reportType !== 'Heat Map' && this.reportType !== 'Weekly Calendar') {
      return {
        ...payload,
        selectedHeaders: this.selectedHeaders,
        startDate: this.filterPayload.startdate
          ? moment(this.filterPayload.startdate).format('YYYY-MM-DD')
          : '',
        endDate: this.filterPayload.enddate
          ? moment(this.filterPayload.enddate).format('YYYY-MM-DD')
          : '',
      };
    }
    return payload;
  }

  public setHeatMapOrWeeklyCalendarProperties(payload): void {
    return {
      ...payload,
      selectedHeaders: [],
      startDate: this.filterPayload.startDate
        ? moment(this.filterPayload.startDate).format('YYYY-MM-DD')
        : '',
      endDate: this.filterPayload.endDate
        ? moment(this.filterPayload.endDate).format('YYYY-MM-DD')
        : '',
      startTime: this.filterPayload.startTime ? this.filterPayload.startTime : '07:00',
      endTime: this.filterPayload.endTime ? this.filterPayload.endTime : '17:00',
    };
  }

  public setWeeklyCalendarTime(payload): void {
    return {
      ...payload,
      startTime: this.filterPayload.startTime
        ? moment(this.filterPayload.startTime).format('HH:mm')
        : '07:00',
      endTime: this.filterPayload.endTime
        ? moment(this.filterPayload.endTime).format('HH:mm')
        : '17:00',
    };
  }

  public onRecurrenceSelect(value): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    this.selectedRecurrence = value;
    if (value === 'Does Not Repeat') {
      this.scheduleForm.get('repeatEveryType').setValue('');
    } else {
      this.scheduleForm.get('repeatEveryCount').setValue(1);
    }
    if (value === 'Daily') {
      this.scheduleForm.get('repeatEveryType').setValue('Day');
    }
    if (value === 'Weekly') {
      this.scheduleForm.get('repeatEveryType').setValue('Week');
    }
    if (value === 'Monthly') {
      this.scheduleForm.get('repeatEveryType').setValue('Month');
    }
    if (value === 'Yearly') {
      this.scheduleForm.get('repeatEveryType').setValue('Year');
    }
    this.recurrenceRepeatCountDaily(value);
    this.recurrenceRepeatCountWeekly(value);
    this.recurrenceRepeatCountMonthlyOrYearly(value);
    this.occurMessage();
  }

  public recurrenceRepeatCountDaily(value): void {
    if (this.scheduleForm.get('repeatEveryCount').value > 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.checkform = this.scheduleForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day11: any): void => {
        const dayObj11 = day11;
        dayObj11.checked = true;
        dayObj11.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj11.value));
        return dayObj11;
      });
    }
    if (this.scheduleForm.get('repeatEveryCount').value === 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.checkform = this.scheduleForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day14: any): void => {
        const dayObj14 = day14;
        dayObj14.checked = true;
        dayObj14.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj14.value));
        return dayObj14;
      });
    }
  }

  public recurrenceRepeatCountWeekly(value): void {
    if (this.scheduleForm.get('repeatEveryCount').value > 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.weekDays = this.weekDays.map((day12: any): void => {
        const dayObj12 = day12;
        if (dayObj12.value === 'Monday') {
          dayObj12.checked = true;
        } else {
          dayObj12.checked = false;
        }
        dayObj12.isDisabled = false;
        return dayObj12;
      });
      this.checkform = this.scheduleForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.scheduleForm.get('repeatEveryCount').value === 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.weekDays = this.weekDays.map((day13: any): void => {
        const dayObj13 = day13;
        if (dayObj13.value === 'Monday') {
          dayObj13.checked = true;
        } else {
          dayObj13.checked = false;
        }
        dayObj13.isDisabled = false;
        return dayObj13;
      });
      this.checkform = this.scheduleForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
  }

  public recurrenceRepeatCountMonthlyOrYearly(value): void {
    if (
      this.scheduleForm.get('repeatEveryCount').value === 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.scheduleForm.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showMonthlyRecurrence();
    }
    if (
      this.scheduleForm.get('repeatEveryCount').value > 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.scheduleForm.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.showMonthlyRecurrence();
    }
  }

  public scheduleClose(): void {
    this.modalRef1.hide();
  }

  public changeRecurrenceCount(value): void {
    if (value > 0) {
      this.handleRecurrenceType(value);
      this.handleRepeatEveryType(value);
      this.occurMessage();
    } else if (value < 0) {
      this.scheduleForm.get('repeatEveryCount').setValue(1);
    }
  }

  public handleRecurrenceType(value): void {
    const recurrenceValue = this.scheduleForm.get('recurrence').value;
    const isSingleRecurrence = +value === 1;

    if (recurrenceValue === 'Daily' || recurrenceValue === 'Weekly' || recurrenceValue === 'Monthly' || recurrenceValue === 'Yearly') {
      this.isRepeatWithMultipleRecurrence = !isSingleRecurrence;
      this.isRepeatWithSingleRecurrence = isSingleRecurrence;
      this.showRecurrenceTypeDropdown = !isSingleRecurrence;
    }
  }

  public handleRepeatEveryType(value): void {
    const recurrenceValue = this.scheduleForm.get('recurrence').value;
    const isSingleRecurrence = +value === 1;

    if (recurrenceValue === 'Daily') {
      this.scheduleForm.get('repeatEveryType').setValue(isSingleRecurrence ? 'Day' : 'Days');
    } else if (recurrenceValue === 'Weekly') {
      this.scheduleForm.get('repeatEveryType').setValue(isSingleRecurrence ? 'Week' : 'Weeks');
    } else if (recurrenceValue === 'Monthly') {
      this.scheduleForm.get('repeatEveryType').setValue(isSingleRecurrence ? 'Month' : 'Months');
      this.changeMonthlyRecurrence();
      this.showMonthlyRecurrence();
    } else if (recurrenceValue === 'Yearly') {
      this.scheduleForm.get('repeatEveryType').setValue(isSingleRecurrence ? 'Year' : 'Years');
    }
  }


  public occurMessage(): void {
    this.message = '';
    const repeatType = this.scheduleForm.get('repeatEveryType').value;
    const repeatCount = +this.scheduleForm.get('repeatEveryCount').value;
    const recurrenceEndDate = this.scheduleForm.get('recurrence').value
      ? ` until ${moment(this.scheduleForm.get('endDate').value).format('MMMM DD, YYYY')}` : '';

    switch (repeatType) {
      case 'Day':
        this.message = 'Occurs every day';
        break;
      case 'Days':
        this.message = this.getDaysMessage(repeatCount);
        break;
      case 'Week':
        this.message = this.getWeeklyMessage();
        break;
      case 'Weeks':
        this.message = this.getWeeklyCountMessage(repeatCount);
        break;
      case 'Month':
      case 'Months':
        this.message = this.getMonthlyMessage();
        break;
      case 'Year':
      case 'Years':
        this.message = this.getYearlyMessage();
        break;
      default:
        this.message = this.getYearlyMessage();
    }

    if (this.message) {
      this.message += recurrenceEndDate;
    }
  }

  public getDaysMessage(repeatCount: number): string {
    if (repeatCount === 2) {
      return 'Occurs every other day';
    }
    return `Occurs every ${repeatCount} days`;
  }

  public getWeeklyMessage(): string {
    const weekDays = this.getCheckedDays();
    return `Occurs every ${weekDays}`;
  }

  public getWeeklyCountMessage(repeatCount: number): string {
    const weekDays = this.getCheckedDays();
    if (repeatCount === 2) {
      return `Occurs every other ${weekDays}`;
    }
    return `Occurs every ${repeatCount} weeks on ${weekDays}`;
  }

  public getMonthlyMessage(): string {
    if (this.scheduleForm.get('chosenDateOfMonth').value === 1) {
      return `Occurs on day ${this.monthlyDate}`;
    }
    if (this.scheduleForm.get('chosenDateOfMonth').value === 2) {
      return `Occurs on the ${this.monthlyDayOfWeek}`;
    }
    return `Occurs on the ${this.monthlyLastDayOfWeek}`;
  }

  public getYearlyMessage(): string {
    if (this.scheduleForm.get('chosenDateOfMonth').value === 1) {
      return `Occurs on ${moment(this.scheduleForm.get('startDate').value).format('MMMM')} ${this.monthlyDate}`;
    }
    if (this.scheduleForm.get('chosenDateOfMonth').value === 2) {
      return `Occurs every year on the ${this.monthlyDayOfWeek}`;
    }
    return `Occurs every year on the ${this.monthlyLastDayOfWeek}`;
  }

  public getCheckedDays(): string {
    let weekDays = '';
    this.weekDays.forEach((dayObj: any) => {
      if (dayObj.checked) {
        weekDays = `${weekDays + dayObj.value},`;
      }
    });
    return weekDays.replace(/,\s*$/, '');
  }


  public changeMonthlyRecurrence(): void {
    this.setMonthlyOrYearlyRecurrenceOption();
    this.updateFormValidation();
    this.showMonthlyRecurrence();
    this.occurMessage();
  }

  public showMonthlyRecurrence(): void {
    if (this.scheduleForm.get('startDate').value) {
      const startDate = moment(this.scheduleForm.get('startDate').value).format('YYYY-MM');
      const chosenDay = moment(this.scheduleForm.get('startDate').value).format('dddd');
      this.monthlyDate = moment(this.scheduleForm.get('startDate').value).format('DD');
      this.chosenMonthNumber = moment(this.scheduleForm.get('startDate').value).format('M');
      const day = moment(startDate, 'YYYY-MM').startOf('month').day(chosenDay);
      const getAllDays = [];
      if (day.date() > 7) day.add(7, 'd');
      const month = day.month();
      while (month === day.month()) {
        getAllDays.push(day.format('YYYY-MM-DD'));
        day.add(7, 'd');
      }
      let week;
      let extraOption;
      this.enableOption = false;
      getAllDays.forEach((element, i): void => {
        if (
          moment(this.scheduleForm.get('startDate').value).format('YYYY-MM-DD')
          === moment(element).format('YYYY-MM-DD')
        ) {
          const number = i + 1;
          if (number === 1) {
            week = 'First';
          }
          if (number === 2) {
            week = 'Second';
          }
          if (number === 3) {
            week = 'Third';
          }
          if (number === 4) {
            this.enableOption = true;
            extraOption = 'Last';
            week = 'Fourth';
          }
          if (number === 5) {
            week = 'Last';
          }
          if (number === 6) {
            week = 'Last';
          }
        }
      });

      this.monthlyDayOfWeek = `${week} ${chosenDay}`;
      this.monthlyLastDayOfWeek = `${extraOption} ${chosenDay}`;
      this.week = week;
      this.chosenDay = chosenDay;
      this.extraOption = extraOption;

      if (!this.enableOption && this.scheduleForm.get('chosenDateOfMonth').value === 3) {
        this.scheduleForm.get('chosenDateOfMonth').setValue(2);
        this.scheduleForm.get('dateOfMonth').setValue(null);
        this.scheduleForm.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
      }
      this.setMonthlyOrYearlyRecurrenceOption();
      this.occurMessage();
      const time = this.timeSlot.filter((item): any => item.order === this.order);
      if (time.length) {
        this.changeTimeRange(time[0]);
      }
    }
  }

  public setMonthlyOrYearlyRecurrenceOption(): void {
    if (this.scheduleForm.get('chosenDateOfMonth').value === 1) {
      this.scheduleForm
        .get('dateOfMonth')
        .setValue(moment(this.scheduleForm.get('startDate').value).format('DD'));
      this.scheduleForm.get('monthlyRepeatType').setValue(null);
    } else if (this.scheduleForm.get('chosenDateOfMonth').value === 2) {
      this.scheduleForm.get('dateOfMonth').setValue(null);
      this.scheduleForm.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
    } else if (this.scheduleForm.get('chosenDateOfMonth').value === 3) {
      this.scheduleForm.get('dateOfMonth').setValue(null);
      this.scheduleForm.get('monthlyRepeatType').setValue(this.monthlyLastDayOfWeek);
    }
  }

  public updateFormValidation(): void {
    const chosenDateOfMonth = this.scheduleForm.get('chosenDateOfMonth');
    const dateOfMonth = this.scheduleForm.get('dateOfMonth');
    const monthlyRepeatType = this.scheduleForm.get('monthlyRepeatType');
    const repeatEveryCount = this.scheduleForm.get('repeatEveryCount');
    if (this.scheduleForm.get('chosenDateOfMonth').value === 1) {
      dateOfMonth.setValidators([Validators.required]);
      monthlyRepeatType.clearValidators();
    } else {
      monthlyRepeatType.setValidators([Validators.required]);
      dateOfMonth.clearValidators();
    }
    if (
      this.scheduleForm.get('recurrence').value === 'Daily'
      || this.scheduleForm.get('recurrence').value === 'Weekly'
      || this.scheduleForm.get('recurrence').value === 'Monthly'
    ) {
      repeatEveryCount.setValidators([Validators.required]);
    } else {
      repeatEveryCount.clearValidators();
    }
    chosenDateOfMonth.updateValueAndValidity();
    dateOfMonth.updateValueAndValidity();
    monthlyRepeatType.updateValueAndValidity();
    repeatEveryCount.updateValueAndValidity();
  }

  public formControlValueChanged(): void {
    this.scheduleForm.get('repeatEveryType').valueChanges.subscribe((value: string): void => {
      const days = this.scheduleForm.get('days');
      const chosenDateOfMonth = this.scheduleForm.get('chosenDateOfMonth');
      const dateOfMonth = this.scheduleForm.get('dateOfMonth');
      const monthlyRepeatType = this.scheduleForm.get('monthlyRepeatType');
      if (value === 'Week' || value === 'Day' || value === 'Weeks') {
        days.setValidators([Validators.required]);
      } else {
        days.clearValidators();
      }
      if (value === 'Month' || value === 'Months' || value === 'Year' || value === 'Years') {
        if (this.scheduleForm.get('chosenDateOfMonth').value === 1) {
          dateOfMonth.setValidators([Validators.required]);
          monthlyRepeatType.clearValidators();
        } else {
          monthlyRepeatType.setValidators([Validators.required]);
          dateOfMonth.clearValidators();
        }
      } else {
        chosenDateOfMonth.clearValidators();
        dateOfMonth.clearValidators();
        monthlyRepeatType.clearValidators();
      }
      chosenDateOfMonth.updateValueAndValidity();
      dateOfMonth.updateValueAndValidity();
      monthlyRepeatType.updateValueAndValidity();
      days.updateValueAndValidity();
    });
  }

  public setCurrentTiming(): void {
    const newDate = moment().format('MM-DD-YYYY');
    const hours = moment(new Date()).format('HH');
    this.startTime = new Date();
    this.startTime.setHours(+hours);
    this.startTime.setMinutes(0);
    this.scheduleForm.get('startTime').setValue(this.startTime);
    if (!this.scheduleForm.get('endDate')?.value) {
      this.scheduleForm.get('endDate').setValue(newDate);
    }
    if (!this.scheduleForm.get('startDate')?.value) {
      this.scheduleForm.get('startDate').setValue(newDate);
    }
    this.changeMonthlyRecurrence();
  }

  public onChange(event): void {
    this.checkform = this.scheduleForm.get('days') as UntypedFormArray;
    this.valueExists = this.checkform.controls.filter(
      (object): any => object.value === event.target.value,
    );
    if (event.target.checked) {
      this.checkform.push(new UntypedFormControl(event.target.value));
      this.weekDays = this.weekDays.map((day16: any): void => {
        const dayObj16 = day16;
        if (day16.value === event.target.value) {
          dayObj16.checked = true;
        }
        return dayObj16;
      });
      if (this.checkform.controls.length === 2) {
        this.weekDays = this.weekDays.map((day17: any): void => {
          const dayObj17 = day17;
          dayObj17.isDisabled = false;
          return dayObj17;
        });
      }
    } else if (this.selectedRecurrence === 'Weekly') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day18: any): void => {
            const dayObj18 = day18;
            if (dayObj18.value === event.target.value) {
              dayObj18.checked = false;
            }
            return dayObj18;
          });
        }
        if (this.checkform.controls.length === 1) {
          this.weekDays = this.weekDays.map((day19: any): void => {
            const dayObj19 = day19;
            if (dayObj19.value === this.checkform.controls[0].value) {
              dayObj19.isDisabled = true;
              dayObj19.checked = true;
            }
            return dayObj19;
          });
          return;
        }
        i += 1;
      });
    } else if (this.selectedRecurrence === 'Daily') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day): void => {
            const dayObj = day;
            if (dayObj.value === event.target.value) {
              dayObj.checked = false;
              dayObj.isDisabled = false;
            }
            return dayObj;
          });
          return;
        }
        i += 1;
      });
    }
    this.recurrenceDailyOrWeeklyDays();
    this.occurMessage();
  }

  public recurrenceDailyOrWeeklyDays(): void {
    if (this.checkform.controls.length !== 7) {
      this.scheduleForm.get('recurrence').setValue('Weekly');
      if (+this.scheduleForm.get('repeatEveryCount').value === 1) {
        this.scheduleForm.get('repeatEveryType').setValue('Week');
      } else {
        this.scheduleForm.get('repeatEveryType').setValue('Weeks');
      }
      this.selectedRecurrence = this.scheduleForm.get('recurrence').value;
    }
    if (this.checkform.controls.length === 7) {
      this.scheduleForm.get('recurrence').setValue('Daily');
      if (+this.scheduleForm.get('repeatEveryCount').value === 1) {
        this.scheduleForm.get('repeatEveryType').setValue('Day');
      } else {
        this.scheduleForm.get('repeatEveryType').setValue('Days');
      }
      this.selectedRecurrence = this.scheduleForm.get('recurrence').value;
    }
  }

  public chooseRepeatEveryType(value): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    if (value === 'Day' || value === 'Days') {
      this.scheduleForm.get('recurrence').setValue('Daily');
    }
    if (value === 'Week' || value === 'Weeks') {
      this.scheduleForm.get('recurrence').setValue('Weekly');
    }
    if (value === 'Month' || value === 'Months') {
      this.scheduleForm.get('recurrence').setValue('Monthly');
    }
    if (value === 'Year' || value === 'Years') {
      this.scheduleForm.get('recurrence').setValue('Yearly');
    }
    if (value === 'Day' || value === 'Days') {
      this.checkform = this.scheduleForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day: any): any => {
        const dayObj = day;
        dayObj.checked = true;
        dayObj.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj.value));
        return dayObj;
      });
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.showRecurrenceTypeDropdown = true;
    }
    if (value === 'Week' || value === 'Weeks') {
      this.weekDays = this.weekDays.map((day15: any): void => {
        const dayObj15 = day15;
        if (dayObj15.value === 'Monday') {
          dayObj15.checked = true;
        } else {
          dayObj15.checked = false;
        }
        dayObj15.isDisabled = false;
        return dayObj15;
      });
      this.checkform = this.scheduleForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (value === 'Day' || value === 'Week' || value === 'Month' || value === 'Year') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showRecurrenceTypeDropdown = false;
    }
    if (value === 'Days' || value === 'Weeks' || value === 'Months' || value === 'Years') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.showRecurrenceTypeDropdown = false;
    }
    if (this.scheduleForm.get('repeatEveryCount').value > 1) {
      this.showRecurrenceTypeDropdown = true;
      this.isRepeatWithMultipleRecurrence = false;
    }
    this.selectedRecurrence = this.scheduleForm.get('recurrence').value;
    this.occurMessage();
  }

  public onWeekSlot(event): void {
    this.week = event;
    this.showMonthlyRecurrence();
  }

  public onDaySlot(event): void {
    this.chosenDay = event;
    this.showMonthlyRecurrence();
  }

  public requestAutocompleteItems = (text: string): Observable<any> => {
    const filteredSuggestions = this.updatedMemberList.filter((item): any => item.UserEmail.toLowerCase().includes(text.toLowerCase()));
    return of(filteredSuggestions);
  };

  public openModal(template: TemplateRef<any>): void {
    this.modalRef = this.modalService.show(template, {
      backdrop: 'static',
      class: 'custom-modal modal-dialog-centered date-range-modal',
    });
  }


  public getTimeLine(): void {
    this.reportsService.getTimeLineNames().subscribe({
      next: (response: any): void => {
        this.timeSlot = response ? response.data : [];
      },
      error: (timeError): void => {
        this.toastr.error(timeError.message, 'OOPS!');
      },
    });
  }

  public changeTimeRange(time: any): void {
    const timeRange = time.timelineName;
    this.maxDate = new Date();
    this.bsInlineValue = new Date();
    this.order = time.order;
    this.timelineId = time.id;
    switch (timeRange) {
      case 'Today':
        this.bsInlineRangeValue = [this.bsInlineValue, this.bsInlineValue];
        break;
      case 'Last 7 days':
        this.maxDate.setDate(this.maxDate.getDate() - 7);
        this.bsInlineRangeValue = [this.maxDate, this.bsInlineValue];
        break;
      case 'Last 15 days':
        this.maxDate.setDate(this.maxDate.getDate() - 15);
        this.bsInlineRangeValue = [this.maxDate, this.bsInlineValue];
        break;
      case 'Next 7 days':
        this.maxDate.setDate(this.maxDate.getDate() + 7);
        this.bsInlineRangeValue = [this.bsInlineValue, this.maxDate];
        break;
      case 'Next 15 days':
        this.maxDate.setDate(this.maxDate.getDate() + 15);
        this.bsInlineRangeValue = [this.bsInlineValue, this.maxDate];
        break;
      case 'This Month':
        this.bsInlineValue = moment().startOf('month').toDate();
        this.maxDate = moment().endOf('month').toDate();
        this.bsInlineRangeValue = [this.bsInlineValue, this.maxDate];
        break;
      case 'Next Month':
        this.bsInlineValue = moment().add(1, 'month').startOf('month').toDate();
        this.maxDate = moment().add(1, 'month').endOf('month').toDate();
        this.bsInlineRangeValue = [this.bsInlineValue, this.maxDate];
        break;
      case 'Custom':
        break;
      default:
        this.bsInlineRangeValue = [this.bsInlineValue, this.bsInlineValue];
        break;
    }
  }

  public changeDateRange(date: any): void {
    this.bsInlineRangeValue = date;
    this.bsInlineValue = new Date(this.bsInlineRangeValue[0]);
    this.maxDate = new Date(this.bsInlineRangeValue[1]);
  }
}
