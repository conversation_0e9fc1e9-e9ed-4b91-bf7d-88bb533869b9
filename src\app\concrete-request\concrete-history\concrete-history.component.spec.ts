import { ComponentFixture } from '@angular/core/testing';
import { of, Subject } from 'rxjs';
import { ConcreteHistoryComponent } from './concrete-history.component';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';

describe('ConcreteHistoryComponent', () => {
  let component: ConcreteHistoryComponent;
  let fixture: ComponentFixture<ConcreteHistoryComponent>;
  let deliveryServiceMock: jest.Mocked<DeliveryService>;
  let projectServiceMock: jest.Mocked<ProjectService>;

  const mockProjectData = {
    ProjectId: '123',
    ParentCompanyId: '456'
  };

  const mockHistoryData = {
    data: [
      { type: 'status', description: 'Status changed', createdAt: '2024-03-20' },
      { type: 'comment', description: 'Comment added', createdAt: '2024-03-20' }
    ],
    concreteRequest: { id: '789', status: 'pending' }
  };

  beforeEach(() => {
    // Create mock services
    deliveryServiceMock = {
      EditConcreteRequestId: new Subject(),
      fetchConcreteData: new Subject(),
      fetchConcreteData1: new Subject(),
      completeConcreteRequestStatus: jest.fn(),
      getConcreteRequestHistory: jest.fn().mockReturnValue(of(mockHistoryData))
    } as any;

    projectServiceMock = {
      projectParent: of(mockProjectData)
    } as any;

    // Create component instance
    component = new ConcreteHistoryComponent(
      deliveryServiceMock,
      projectServiceMock
    );
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.historyList).toEqual([]);
    expect(component.loader).toBe(true);
  });

  it('should subscribe to project service and update project data', () => {
    component.ngOnInit();
    expect(component.ProjectId).toBe(mockProjectData.ProjectId);
    expect(component.ParentCompanyId).toBe(mockProjectData.ParentCompanyId);
  });

  it('should filter out comments from history list', () => {
    component.getHistory();
    expect(component.historyList.length).toBe(1);
    expect(component.historyList[0].type).toBe('status');
  });

  it('should call completeConcreteRequestStatus when selectStatus is called', () => {
    const mockData = { id: '123', status: 'completed' };
    component.selectStatus(mockData);
    expect(deliveryServiceMock.completeConcreteRequestStatus).toHaveBeenCalledWith(mockData);
  });

  it('should unsubscribe on destroy', () => {
    const unsubscribeSpy = jest.spyOn(component['subscription'], 'unsubscribe');
    component.ngOnDestroy();
    expect(unsubscribeSpy).toHaveBeenCalled();
  });
});
