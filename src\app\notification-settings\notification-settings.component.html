<section class="page-section pt-md-50px">
  <div class="page-inner-content">
    <div class="top-header row my-3">
      <div class="col-md-5 col-5">
        <h1 class="fs26 fw-bold cairo-regular">Notifications Preferences</h1>
      </div>
      <div class="col-md-7 col-7 text-end">
        <button
          type="button"
          class="btn btn-orange-dark2 px-3 fs10 fw-bold cairo-regular"
          (click)="onSubmit()"
          [disabled]="submitted && notificationForm.valid"
        >
          <em
            class="fa fa-spinner"
            aria-hidden="true"
            *ngIf="submitted && notificationForm.valid"
          ></em>
          Save Changes
        </button>
      </div>
    </div>
    <div class="row ps-3">
      <div class="col-md-6 ps-0">
        <p class="fs13">
          We'll send info that's relevant to you. You can choose what you'd like to get from us and
          we should send it.
        </p>
      </div>
    </div>
    <div
      class="notification-page-card bg-white rounded border-0"
      *ngIf="!loader && notificationListLength > 0"
    >
      <div class="row fw700 fs12 p-3 bg-grey m-0">
        <div class="col-md-6 col-6 ps-5">Activity Type</div>
        <div class="col-md-3 col-3">Instant</div>
        <div class="col-md-3 col-3">Daily Digest</div>
      </div>
      <div class="notification-setting-lists">
        <accordion [closeOthers]="oneAtATime">
          <accordion-group [isOpen]="isFirstOpen" (isOpenChange)="inAppNotification($event)">
            <div class="d-flex shadow-none" accordion-heading>
              <ul class="list-group list-group-horizontal w-100">
                <li class="list-group-item border-0 w-50 bg-transparent">
                  <span>
                    <img
                      src="./assets/images/dropdown-arrow.svg"
                      alt="Dropdown Arrow"
                      class="arrow float-start eye-cursor"
                      *ngIf="isAppNotificationExpanded"
                    />
                    <img
                      src="./assets/images/dropright-arrow.svg"
                      alt="Dropright Arrow"
                      class="arrow float-start eye-cursor"
                      *ngIf="!isAppNotificationExpanded"
                    />
                  </span>
                  <span class="ps-4 ms-1 fw400 fs12">In App/Push Notifications</span>
                </li>
                <li class="list-group-item border-0 w-25 ps-4 bg-transparent notification-intermediate-btn">
                  <div class="form-check text-start ps-0">
                    <input
                      class="form-check-input float-none ms-0"
                      type="checkbox"
                      id="tblData1"
                      name="tblData1"
                      [indeterminate]="isAllInAppInstantSelected === 1"
                      [checked]="isAllInAppInstantSelected === 2"
                      disabled
                    />
                    <label class="form-check-label c-pointer fs12" for="tblData1"> </label>
                  </div>
                </li>
                <li class="list-group-item border-0 w-25 ps-4 bg-transparent notification-intermediate-btn">
                  <div class="form-check notify-setting-chkbtn text-start ps-0">
                    <input
                      class="form-check-input float-none ms-0"
                      type="checkbox"
                      id="tblData2"
                      name="tblData2"
                      [indeterminate]="isAllInAppDailyDigestSelected === 1"
                      [checked]="isAllInAppDailyDigestSelected === 2"
                      disabled
                    />
                    <label class="form-check-label c-pointer fs12" for="tblData2"> &nbsp;</label>
                  </div>
                </li>
              </ul>
            </div>
            <div
              class="d-flex"
              *ngFor="let data of notificationList.inAppNotification; let i = index"
            >
              <ul class="list-group list-group-horizontal w-100">
                <li class="list-group-item border-0 w-50 ps-4">
                  <span class="fw400 fs12 notification-span-desktop notification-span-mobile">{{
                    data.NotificationPreferenceItem.description
                  }}</span>
                </li>
                <li class="list-group-item border-0 w-25 ps-4 notify-checkbox">
                  <div class="form-check text-start ps-0">
                    <input
                      class="form-check-input float-none ms-0"
                      type="checkbox"
                      id="tblData1+'data.NotificationPreferenceItem.id'+{{ i }}"
                      name="tblData1+'data.NotificationPreferenceItem.id'+{{ i }}"
                      [checked]="data.instant"
                      (change)="checkInstantValue(data, $event, 'inApp')"
                    />
                    <label class="form-check-label c-pointer fs12" for="tblData1+'data.NotificationPreferenceItem.id'+{{ i }}">&nbsp; </label>
                  </div>

                </li>
              </ul>
            </div>
          </accordion-group>
          <accordion-group (isOpenChange)="emailNotification($event)">
            <div class="d-flex shadow-none" accordion-heading>
              <ul class="list-group list-group-horizontal w-100">
                <li class="list-group-item bg-transparent border-0 w-50">
                  <span>
                    <img
                      src="./assets/images/dropdown-arrow.svg"
                      alt="Dropdown Arrow"
                      class="arrow float-start eye-cursor"
                      *ngIf="isEmailNotificationExpanded"
                    />
                    <img
                      src="./assets/images/dropright-arrow.svg"
                      alt="Dropright Arrow"
                      class="arrow float-start eye-cursor"
                      *ngIf="!isEmailNotificationExpanded"
                    />
                  </span>
                  <span class="ps-4 ms-1 fw400 fs12">Email Notifications</span>
                </li>
                <li class="list-group-item border-0 bg-transparent w-25 ps-4 notification-intermediate-btn">
                  <div class="form-check text-start ps-0">
                    <input
                      class="form-check-input float-none ms-0"
                      type="checkbox"
                      id="emailtblData1"
                      name="emailtblData1"
                      [indeterminate]="isAllEmailInstantSelected === 1"
                      [checked]="isAllEmailInstantSelected === 2"
                      disabled
                    />
                    <label class="form-check-label c-pointer fs12" for="emailtblData1"> </label>
                  </div>
                </li>
                <li class="list-group-item border-0 bg-transparent w-25 ps-4">
                  <div class="form-check text-start ps-0">
                    <input
                      class="form-check-input float-none ms-0"
                      type="checkbox"
                      id="emailtblData2"
                      name="emailtblData2"
                      [indeterminate]="isAllEmailInstantSelected === 1"
                      [checked]="isAllEmailInstantSelected === 2"
                      disabled
                    />
                    <label class="form-check-label c-pointer fs12" for="emailtblData2"> &nbsp;</label>
                  </div>
                </li>
              </ul>
            </div>
            <div
              class="d-flex"
              *ngFor="let data of notificationList.emailNotification; let j = index"
            >
              <ul class="list-group list-group-horizontal w-100">
                <li class="list-group-item border-0 bg-transparent w-50">
                  <span class="fw400 fs12 notification-span-desktop notification-span-mobile">{{
                    data.NotificationPreferenceItem.description
                  }}</span>
                </li>
                <li class="list-group-item border-0 bg-transparent w-25 ps-4 notify-checkbox">
                  <div class="form-check text-start ps-0">
                    <input
                      class="form-check-input float-none ms-0"
                      type="checkbox"
                      id="emailtblData1+'data.NotificationPreferenceItem.id'+{{ j }}"
                      name="emailtblData1+'data.NotificationPreferenceItem.id'+{{ j }}"
                      [checked]="data.instant"
                      (change)="checkInstantValue(data, $event, 'email')"
                    />
                    <label
                      class="form-check-label c-pointer fs12"
                      for="emailtblData1+'data.NotificationPreferenceItem.id'+{{ j }}"
                    >&nbsp;
                    </label>
                  </div>
                </li>
                <li class="list-group-item border-0 bg-transparent w-25 ps-4 notify-checkbox">
                  <div class="form-check text-start ps-0">
                    <input
                      class="form-check-input float-none ms-0"
                      type="checkbox"
                      id="emailtblData2+'data.NotificationPreferenceItem.id'+{{ j }}"
                      name="emailtblData2+'data.NotificationPreferenceItem.id'+{{ j }}"
                      [checked]="data.dailyDigest"
                      (change)="checkDailyDigestValue(data, $event, 'email')"
                    />
                    <label
                      class="form-check-label c-pointer fs12"
                      for="emailtblData2+'data.NotificationPreferenceItem.id'+{{ j }}"
                    >&nbsp;
                    </label>
                  </div>
                </li>
              </ul>
            </div>
          </accordion-group>
        </accordion>
      </div>
      <form name="form" [formGroup]="notificationForm" novalidate class="custom-material-form ps-4">
        <h2 class="fs12 my-3 fw700">Daily Digest Time</h2>
        <div class="row">
          <div class="col-md-2">
            <div class="form-group">
              <select class="form-control fs12 material-input px-2" formControlName="time">
                <option value="01:00">01:00</option>
                <option value="02:00">02:00</option>
                <option value="03:00">03:00</option>
                <option value="04:00">04:00</option>
                <option value="05:00">05:00</option>
                <option value="06:00">06:00</option>
                <option value="07:00">07:00</option>
                <option value="08:00">08:00</option>
                <option value="09:00">09:00</option>
                <option value="10:00">10:00</option>
                <option value="11:00">11:00</option>
                <option value="12:00">12:00</option>
              </select>
              <div class="color-red" *ngIf="submitted && notificationForm.get('time').errors">
                <small *ngIf="notificationForm.get('time').errors.required">*required.</small>
              </div>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <select class="form-control fs12 material-input px-2" formControlName="timeFormat">
                <option value="AM">AM</option>
                <option value="PM">PM</option>
              </select>
              <div class="color-red" *ngIf="submitted && notificationForm.get('timeFormat').errors">
                <small *ngIf="notificationForm.get('timeFormat').errors.required">*required.</small>
              </div>
            </div>
          </div>
          <div class="col-md-8">
            <div class="form-group">
              <select class="form-control fs12 material-input px-2" formControlName="TimeZoneId">
                <option value="" disabled selected hidden>Choose TimeZone</option>
                <option *ngFor="let type of timezoneList" value="{{ type.id }}">
                  {{ type.location }}
                </option>
              </select>
              <div class="color-red" *ngIf="submitted && notificationForm.get('TimeZoneId').errors">
                <small *ngIf="notificationForm.get('TimeZoneId').errors.required">*required.</small>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
    <div class="row" *ngIf="loader">
      <div class="col-12 text-center">
        <div class="fs18 fw-bold cairo-regular my-5 text-black">Loading...</div>
      </div>
    </div>
    <div class="row" *ngIf="!loader && notificationListLength === 0">
      <div class="col-12 text-center">
        <div class="fs18 fw-bold cairo-regular my-5 text-black">No Records Found</div>
      </div>
    </div>
  </div>
</section>
