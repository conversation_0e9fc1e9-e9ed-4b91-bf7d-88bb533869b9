import { Observable } from 'rxjs';

import { Injectable } from '@angular/core';

import * as FileSaver from 'file-saver';
import * as XLSX from 'xlsx';
import { ApiService } from '../api_base/api.service';

const EXCEL_TYPE = 'application/vnd.openxmlformats';
const PDF_TYPE = 'application/pdf';
const EXCEL_EXTENSION = '.xlsx';
const PDF_EXTENSION = '.pdf';
@Injectable({
  providedIn: 'root',
  })
export class ReportsService {
  // public scheduleData=  new BehaviorSubject({});
  public constructor(private readonly api: ApiService) {}

  public deliveryReports(params, payload): Observable<any> {
    return this.api.post(
      `reports/delivery_request/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public craneReports(params, payload): Observable<any> {
    return this.api.post(
      `reports/crane_request/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public concreteReports(params, payload): Observable<any> {
    return this.api.post(
      `reports/concrete_request/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public exportDeliveryRequest(params, payload): Observable<any> {
    return this.api.post(
      `reports/export_delivery_request/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public saveReportDeliveryRequest(params, payload): Observable<any> {
    return this.api.post(
      `reports/saved/export_delivery_request/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public exportDeliveryRequestInExcelFormat(params, payload): Observable<any> {
    return this.api.getExcel1(
      `reports/export_delivery_request/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public exportCraneRequest(params, payload): Observable<any> {
    return this.api.post(
      `reports/export_crane_request/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public saveReportCraneRequest(params, payload): Observable<any> {
    return this.api.post(
      `reports/saved/export_crane_request/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public exportCraneRequestInExcelFormat(params, payload): Observable<any> {
    return this.api.getExcel1(
      `reports/export_crane_request/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public exportConcreteRequest(params, payload): Observable<any> {
    return this.api.post(
      `reports/export_concrete_request/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public saveReportConcreteRequest(params, payload): Observable<any> {
    return this.api.post(
      `reports/saved/export_concrete_request/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public exportConcreteRequestInExcelFormat(params, payload): Observable<any> {
    return this.api.getExcel1(
      `reports/export_concrete_request/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public heatMapReports(params, payload): Observable<any> {
    return this.api.post(
      `reports/heat_map/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}/${params.sortOrder}`,
      payload,
    );
  }

  public exportHeatMap(params, payload): Observable<any> {
    return this.api.getExcel1(
      `reports/export_heat_map/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}/${params.sortOrder}`,
      payload,
    );
  }

  public saveReportHeatMap(params, payload): Observable<any> {
    return this.api.getExcel1(
      `reports/saved/export_heat_map/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}/${params.sortOrder}`,
      payload,
    );
  }

  public scheduleHeatMapReports(params, payload): Observable<any> {
    return this.api.post(`reports/schedule/${params.ProjectId}`, payload);
  }

  public exportAsExcelFile(json: any[], excelFileName: string): void {
    const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(json);
    const workbook: XLSX.WorkBook = { Sheets: { data: worksheet }, SheetNames: ['data'] };
    const excelBuffer: any = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    this.saveAsExcelFile(excelBuffer, excelFileName);
  }

  public saveAsExcelFile(buffer: any, fileName: string): void {
    const data: Blob = new Blob([buffer], { type: EXCEL_TYPE });
    FileSaver.saveAs(data, `${fileName}_${new Date().getTime()}${EXCEL_EXTENSION}`);
  }

  public saveAsPdfFile(buffer: any, fileName: string): void {
    const data: Blob = new Blob([buffer], { type: PDF_TYPE });
    FileSaver.saveAs(data, `${fileName}_${new Date().getTime()}${PDF_EXTENSION}`);
  }

  public getScheduledReports(payload): Observable<any> {
    return this.api.get('reports/schedule', payload);
  }

  public getRecentReports(payload): Observable<any> {
    return this.api.get('reports/recent-reports', payload);
  }

  public getSavedReports(payload): Observable<any> {
    return this.api.get('reports/saved-reports', payload);
  }

  public deleteScheduledReports(queryParams): Observable<any> {
    return this.api.deleteMethod('reports/schedule', queryParams);
  }

  public runNowScheduledOrSavedReport(queryParams): Observable<any> {
    return this.api.get('reports/rerun', queryParams);
  }

  public getTimeLineNames(): Observable<any> {
    return this.api.get('reports/scheduler-timeline-names');
  }

  public inspectionReports(params, payload): Observable<any> {
    return this.api.post(
      `reports/inspection_request/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public saveReportInspectionRequest(params, payload): Observable<any> {
    return this.api.post(
      `reports/saved/export_inspection_request/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public exportInspectionReport(params, payload): Observable<any> {
    return this.api.post(
      `reports/export_inspection_request/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public exportInspectionRequestInExcelFormat(params, payload): Observable<any> {
    return this.api.getExcel1(
      `reports/export_inspection_request/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }
}
