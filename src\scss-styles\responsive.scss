@media (min-width: 769px) {
  .page-content {
    padding-left: 0px;
    padding-top: 0;

    &.show_pagecontent {
      padding-left: 230px;
    }
  }
  .own-sidemenu-main .list-group {
    margin-top: 30px;
  }
}

@media (min-width: 768px) {
  .navbar-toggler {
    display: none;
  }

  .collapse {
    &.navbar-collapse {
      display: flex;
      flex-basis: auto;
      flex-direction: row-reverse;
    }
  }
  .notification-header-desktop {
    font-size: 26px;
  }
  .markread-padding-desktop {
    padding-left: 3rem;
    padding-right: 3rem;
  }
  .notification-span-desktop {
    margin-left: 1.5rem;
    padding-left: 1rem;
  }
  .dfow-filter-button {
    margin-right: 160px;
  }
  .timezone-flex {
    display: flex;
  }
  app-scheduler-form {
    width: 800px;
    background: #fff;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 20px;
  }
  .week-end-time {
    margin-left: 1rem;
  }
  .paddingleft-timezone {
    padding-left: 1rem;
  }
  .responsibleperson-padding-filter {
    padding: 0px;
  }
}
@media (max-width: 768px) {
  .line-1,
  .line-2 {
    display: none;
  }
  .admin-layout .own-sidemenu-main.closed,
  .timezone-flex {
    display: block;
  }
}
@media (max-width: 767px) {
  .timezone-formgroup .multiselect-dropdown .dropdown-btn {
    font-size: 10px;
  }
  .md-ml-0 {
    margin-left: 0px !important;
  }
  .md-ml-4 {
    margin-left: 4px !important;
  }
  .project-setting-layout .project-settings-tab .dot-border-info {
    width: 18px;
  }
  .linecol-project-setting {
    padding-left: 15px !important;
  }
  .project-setting-layout .project-settings-tab .tooltip .tooltiptext {
    width: 100px!important;
    left: -75px;
  }
  app-scheduler-form {
    width: 300px;
    background: #fff;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 20px;
  }
  .padding-0px {
    padding: 0px;
  }
  .select-status-input {
    width: 100px;
  }
  .ngx-pagination li {
    height: 34px;
    width: 34px;
  }
  .me-md-0 {
    margin-right: 0px !important;
  }
  .plans-edit-closeicon {
    margin-right: 5px !important;
  }
  .mb-d-flex {
    display: inline-flex;
    margin-top: 10px;
  }
  .week-end-time {
    margin-left: 0rem;
  }
  .custom-full-calendar .fc .fc-event-title {
    font-size: 9px !important;
  }
  .fc .fc-daygrid-day-bottom {
    font-size: 0.45em !important;
  }
  .dflowheader-btn {
    margin-left: 10px;
  }
  .page-section .page-inner-content .top-header .top-filter {
    .search-icon:hover .input-search,
    .search-icon .input-hover-disable {
      width: 160px;
      font-size: 10px;
      padding: 16px;
    }
  }
  .mobile-view-selectdashboard {
    width: 7rem;
    font-size: 10px;
  }
  .paddingleft-timezone {
    padding-left: 0px !important;
  }
  .welcome_modal .country-code {
    width: 15%;
  }
  .membermobile-email-text {
    margin-left: 10px !important;
  }
  .modal:has(.home-popup) {
    overflow: auto !important;
  }
  .deactivate-modal .content-deactivate {
    padding: 0px;
  }
  .dash-close-icon {
    padding: 0px !important;
  }
  .dashboard-content-para {
    font-size: 10px;
  }
  .new-update-modal {
    width: 365px;
  }
  .terms-conditions-modal {
    .modal-body {
      padding: 0.5rem;
    }
  }
  .page-dashboard-header .dashboard-content {
    padding: 5px;
  }

  .add-calendarevent {
    width: 100% !important;
  }
  .line-1,
  .line-2 {
    display: none;
  }
  .popupcalendarsettings {
    position: absolute;
    left: 88% !important;
    width: 168px;
    overflow: auto;
  }
  .navbar {
    padding: 20px;

    .nav {
      margin-bottom: 10px;

      .nav-item {
        width: 100%;
      }
    }

    .navbar-toggler {
      position: absolute;
      right: 30px;
    }

    .main-menu {
      display: none;
    }

    .navbar-brand {
      width: 210px;
    }

    .mobile-view-nav {
      .main-menu {
        display: block;
        position: fixed;
        left: 0;
        top: 60px;
        width: 100%;
        height: 100vh;
        background-color: $white;
        z-index: 10001;
        transition: all 1s;
      }
    }
  }

  .home-content {
    .inner-content {
      padding: 15px;
    }
  }

  .page-content {
    width: 100%;
    padding-left: 0;
    padding-top: 64px;
  }

  .auth-content {
    background: $white;

    .top-logo {
      display: block;

      .auth-logo {
        margin: 30px auto !important;
        display: block;
      }
    }
  }

  .admin-layout {
    .own-sidemenu-main {
      &.show {
        display: none;
      }

      &.closed {
        display: block;
      }

      .side-list-group {
        margin-top: 36px;

        .sub-menu {
          top: 150px;
        }
      }

      .footer-side {
        .foot-list-group {
          .sub-menu {
            bottom: 85px;
          }
        }
      }
    }

    .navbar {
      background: $white;
      padding: 10px;
      border-radius: 0;

      .menu-button-bars {
        .bar-line {
          background: $orange;
        }
      }
    }

    .table-custom {
      th,
      td {
        white-space: nowrap;
      }
      th {
        .wrapper {
          .bar {
            display: none;
          }
        }
      }
    }
  }

  .page-section {
    .top-header {
      .top-btn {
        margin-bottom: 15px;
      }
    }
  }

  .ngx-pagination li:last-child {
    right: -40px;
  }

  .ngx-pagination li:first-child {
    left: -40px;
  }

  .payment-content {
    background: $white;
    padding-top: 15px;

    .top-logo {
      display: block;
      margin: 0 auto;
    }

    .payment-col {
      margin-top: 15px;
    }

    .custom-select.is-invalid {
      background: none;
    }

    .error-text {
      font-size: 70%;
    }
  }

  .pricing-bg {
    padding-top: 20px;
    padding-bottom: 20px;

    .plan-card {
      .plan-column {
        max-width: 100%;
        flex: 100%;
        margin-bottom: 20px;
      }
    }
  }

  .over-view-form {
    .over-view {
      padding: 10px;
    }

    .over-view.left-form {
      padding-bottom: 0;
    }

    .over-view.right-form {
      padding-top: 0;
    }
  }

  .settings-content {
    .settings-tab {
      .profile-content {
        .upload-profile {
          margin: 0 auto;
        }
      }

      .profile-name {
        width: 100%;
        text-align: center;
      }
    }
  }

  .settings-content.edit-profile {
    padding: 0;
  }

  .page-section.plan-profile-filter {
    padding: 15px;
  }

  /** DFOW-content **/
  .dfow-content {
    ul.upload-btn {
      display: block;
      width: 100%;

      li.list-group-item {
        display: inline-block;
        padding: 5px 15px;
        margin: 4px;

        button {
          width: 100%;
        }
      }
    }
    ul.p-0 {
      list-style-type: none;
      margin: 0;
      padding: 0;
    }

    ul.filter-list {
      justify-content: center;
    }
  }
  .side-menu-item {
    display: flex;
    align-items: center;
    padding-left: 30px;
  }

  .side-menu-item input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
  }

  .responsive-font-validation {
    font-size: 12px;
  }

  .notification-header-mobile {
    font-size: 13px;
  }
  .notification-listitem {
    margin-bottom: 10px;
  }
  .markread-padding-mobile {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  section.report--section li.li-position.active a::before {
    top: 30px;
  }
  .notification-span-mobile {
    margin-left: 0px;
    padding-left: 0px;
  }
  .filtermodal-mobile-view .modal-content {
    max-width: 400px;
    margin-top: 100px;
  }
  .dfow-filter-button {
    margin-right: 160px;
  }
  .mobile-display-block {
    display: block;
  }
  .modal-dialog.heatmap-modal .modal-content {
    width: 300px;
    border-radius: 20px;
  }
  .date-range-modal {
    &.modal-dialog {
      max-width: 100%;
      .date-range-footer {
        width: 100%;
        padding: 0px 20px;
      }
      .bs-datepicker {
        .bs-datepicker-body {
          min-width: 60vw;
        }
      }
    }
  }
  .custom-vertical-tab {
    .nav-pills {
      width: 175px;
    }
    .tab-content {
      width: calc(100% - 175px);
    }
  }
  .modal .modal-content .row .company-select .multiselect-dropdown .dropdown-btn .selected-item {
    font-size: 9px !important;
  }
  .ml-md-9px {
    margin-left: 9px !important;
  }
  .custom-full-calendar
    .fc.fc-media-screen
    .fc-scrollgrid-section
    .fc-daygrid-day.fc-day.fc-day-today
    .fc-daygrid-day-number {
    height: 21px;
    width: 24px;
    line-height: 20px;
    font-size: 12px;
  }
  .fc .fc-more-popover {
    left: 12rem !important;
    transform: translate(-50%, -50px);
  }

  .calendarsetting-description {
    left: 90px;
    right: 22px;
  }
  .reset-success-card-container {
    width: 100%;
  }
  .projectmobile-listitem {
    margin-left: 0px !important;
  }
  .project-settings-table .preview-tab {
    font-size: 8px;
    padding: 0px;
  }
  .project-settings-table {
    width: 100%;
    margin-bottom: 15px;
    td,th{
      white-space: nowrap;
    }
  }
  .project-setting-layout {
    .project-settings-tab{
      padding: 10px 0px;
      .info-content{
        margin-bottom: 15px;
      }
    }
    .custom-secondary-tab {
      .tab-container {
        .nav-tabs{
          padding: 0px 15px;
           .nav-item{
            &:not(:last-child){
              margin-right: 10px;
            }
           }
        }
      }
    }
  }
  .tooltip.active .tooltiptext {
    visibility: visible;
  }
  .tooltip .tooltiptext {
    visibility: hidden;
  }

  .view-logistic-button{
    margin-left: 14px;
   }

   .enter-detail-customform .enter-detail-heading{
    margin-right: 0px;
   }
   .dropdown-country-code {
    .timezone-formgroup .multiselect-dropdown .dropdown-btn {
      padding: 5px 0px !important;
    }}
   .dashboard-plan .desktop-thumbnail-images{
    width: auto;
    height: 260px;
  }
  .dashboard-plan .desktop-thumbnail-innerimage{
    width: auto;
    height: 260px;
  }
  .projectsettings-listgroupitems .primary-tooltip .tooltip.project-settings-tooltip {
    display: none;
  }
  .custom-radio-option {
    overflow-x: auto;
  }
  .schedule-map.auto-approve-map .ng2-tag-input {
    max-height: 190px;
}
}

@media (max-width: 580px) {
  .project-setting-layout {
    .project-settings-tab {
      .add-label,
      .reset-label {
        margin-left: 0;
        display: block;
      }
    }
  }
}
@media (min-width: 1024px) {
  .pl-xl-10 {
    padding-left: 30px;
  }

  .page-content {
    &.show_pagecontent {
      padding-left: 230px;
    }
  }
  .dash-close-icon {
    padding: 0px;
  }
}
@media (max-width: 1024px) {
  .recurrence-column {
    margin-top: 20px !important;
  }
}
@media (min-width: 576px) {
  .billing-modal {
    margin: 7rem auto;
  }
  .custom-modal.report-filter-modal {
    max-width: 600px;
    margin-top: 75px;
  }
  .custom-modal {
    .modal-content {
      box-shadow: 0 10px 10px #00000029;
      border-radius: 20px;
      padding: 0 15px;
    }

    .modal-header {
      padding: 15px 0 15px;

      .close {
        outline: none;

        &:focus {
          outline: none;
        }
      }
    }
  }

  .reset-popup {
    .modal-content {
      border-radius: 40px;
    }
  }

  .home-popup {
    .modal-content {
      border-radius: 20px;

      .close {
        outline: none;

        &:focus {
          outline: none;
        }
      }
    }
  }

  .add-project-popup {
    .modal-content {
      border-radius: 20px;

      .close {
        outline: none;

        &:focus {
          outline: none;
        }
      }
    }
  }

  .filter-popup {
    margin: 1.75rem 50px 0 auto;
  }
  .schedule-map.auto-approve-map .ng2-tag-input {
    max-height: 190px;
}
}

@media (min-width: 993px) and (max-width: 1023px) {
  .pt-md-0px {
    padding-top: 0px !important;
  }
  .report-mobilefix {
    margin-top: 45px;
  }
  .mobile-display-block {
    display: block !important;
  }

  .custom-full-calendar .fc .fc-event-title {
    font-size: 9px !important;
  }
  .fc .fc-daygrid-day-bottom {
    font-size: 0.65em !important;
  }
  .mobile-view-selectdashboard {
    width: 6rem;
    font-size: 10px;
  }
  .admin-layout {
    .own-sidemenu-main {
      &.closed {
        display: none;
      }

      &.show {
        display: block;
      }
    }
    .page-content {
      padding-top: 0px;
      padding-left: 250px;
    }
  }

  .timezone-flex {
    display: none !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .report-mobilefix {
    margin-top: 45px;
  }
  .admin-layout {
    .page-content {
      padding-top: 50px;
      padding-left: 0;
    }

    .navbar {
      background: $white;
      padding: 10px;

      .menu-button-bars {
        .bar-line {
          background: $orange;
        }
      }
    }

    .own-sidemenu-main {
      &.closed {
        display: block;
      }

      &.show {
        display: none;
      }

      .side-list-group {
        margin-top: 36px;

        .closed {
          .side-logo {
            display: none;
          }
        }
      }
    }

    .closed_page {
      padding-left: 0;
    }

    .table-custom {
      th,
      td {
        white-space: nowrap;
      }
    }
  }

  .new-delivery-popup,
  .members-popup {
    max-width: 650px;
  }

  .payment-content {
    background: $white;

    .custom-select.is-invalid {
      background: none;
    }

    .error-text {
      font-size: 70%;
    }
  }

  .pricing-bg {
    padding-top: 20px;
    padding-bottom: 20px;

    .plan-card {
      .plan-column {
        flex: 0 0 42.333333%;
        max-width: 42.333333%;
        margin-bottom: 20px;
      }
    }
  }
}
@media (min-width: 320px) and (max-width: 420px) {
  .fixTableHead {
    overflow-x: auto;
  }
  .deactivate-select-control .form-control {
    width: 200px;
  }
}

@media (min-width: 320px) and (max-width: 360px) {
  .equipmentTableHead {
    overflow-x: auto;
  }
}

@media (min-width: 993px) and (max-width: 1199px) {
  .mobile-view-selectdashboard {
    width: 6rem;
    font-size: 10px;
  }
  .custom-full-calendar
    .fc.fc-media-screen
    .fc-header-toolbar
    .fc-button-group
    .fc-button.fc-button-primary {
    width: 60px;
  }
  .custom-full-calendar .fc.fc-media-screen .fc-header-toolbar {
    width: 50%;
  }
  .payment-content {
    .payment-col {
      margin-left: 28%;

      .billed-col {
        flex: 0 0 41.666667%;
        max-width: 41.666667%;
      }

      .price-col {
        flex: 0 0 58.333333%;
        max-width: 58.333333%;
      }

      .custom-select.is-invalid {
        background: none;
      }
    }

    .error-text {
      font-size: 70%;
    }
  }

  .plan-card {
    .plan-column {
      flex: 0 0 30%;
      max-width: 30%;
      margin-bottom: 20px;
    }
  }
  .project-settings-table {
    width: 100%;
}
}

@media (max-width: 480px) {
  //Notification card

  .notification-card {
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 95%;
  }
}

@media (max-width: 575px) {
  .billing {
    .plan-heading {
      text-align: center;
    }

    .my-plan-row {
      text-align: center;
    }

    .billing-button {
      width: 40%;
    }
  }
}

@media (min-width: 768px) and (max-width: 769px) {
  .delivery-time {
    .btn.btn-default {
      right: -15px;
      position: absolute;
      bottom: 27px;
    }
  }
}

@media (min-width: 1300px) {
  .table-custom {
    th,
    td {
      white-space: pre-wrap;
      word-break: break-word;
    }
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .report-mobilefix {
    margin-top: 45px;
  }
  .ml-md-9px {
    margin-left: 9px !important;
  }
  .project-setting-layout .project-settings-tab .dot-border-info {
    width: 18px;
  }
  .report-backbtn-header {
    width: 100px;
  }
  .table-custom {
    th,
    td {
      white-space: nowrap;
    }
    th {
      .wrapper {
        .bar {
          display: none;
        }
      }
    }
  }
  app-scheduler-form {
    width: 700px;
    background: $white;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 20px;
  }
  .homepage-complete-details {
    height: calc(100vh - 300px);
  }
  .members-mobilenumber-input {
    margin-left: 20px;
  }
  .timeslot-modal{
    height: 60%;
  }
  .member-no-arrow {
    height: 28px;
  }
  .admin-layout .own-sidemenu-main.closed,
  .timezone-flex {
    display: block;
  }
  .custom-full-calendar
    .fc.fc-media-screen
    .fc-header-toolbar
    .fc-button-group
    .fc-button.fc-button-primary {
    width: 60px;
  }
  .custom-full-calendar .fc.fc-media-screen .fc-header-toolbar {
    width: 50%;
  }
  .delivery-date-recurrence {
    margin-top: 27px;
  }
  .modal:has(.home-popup) {
    overflow: auto !important;
  }
  .members-invite-modal {
    padding: 5px !important;
  }
  .select-member-option {
    padding: 0px !important;
  }
  .auth-content {
    background-size: 1030px;
    background-color: $white;
  }
  .login-container {
    height: calc(100vh - 370px);
  }
  section.report--section li.li-position.active a::before {
    top: 30px;
  }
  .calendarsettings-header {
    margin-top: 10px;
  }
  .notification-span-mobile {
    margin-left: 0px;
    padding-left: 0px;
  }
  .modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
  }
  .modal-dialog.heatmap-modal .modal-content {
    width: 600px;
    border-radius: 20px;
  }
  .week-end-time {
    margin-left: 0rem;
  }
  .timezone-formgroup .multiselect-dropdown .dropdown-btn {
    font-size: 10px;
  }
  .equipmentTableHead {
    overflow-x: auto;
  }
  .modal .modal-content .row .company-select .multiselect-dropdown .dropdown-btn .selected-item {
    font-size: 9px !important;
  }
  .mobile-display-block {
    display: block !important;
  }
  .select-status-input {
    width: 100px;
  }
  .overview-mobile-number {
    margin-right: 0px !important;
  }

  .enter-detail-customform .enter-detail-heading{
    margin-right: 195px;
   }
   .project-settings-table {
    width: 80%;
}
}

@media (min-width: 1600px) {
  .max-container {
    max-width: 1200px;
  }
}
@media (min-width: 991px) {
  .admin-layout {
    .own-sidemenu-main {
      &.closed {
        display: none;
      }

      &.show {
        display: block;
      }
    }
  }
  .page-section .page-inner-content .top-header .top-filter .search-icon:hover .input-search {
    width: 165px;
    padding-left: 4px;
    font-size: 11px;
  }
}

@media (min-width: 991px) and (max-width: 993px) {
  .pt-md-15px {
    padding-top: 15px !important;
  }
  .pt-md-40px {
    padding-top: 40px !important;
  }
  .pt-md-50px {
    padding-top: 50px !important;
  }
  .mobile-view-selectdashboard {
    width: 6rem;
    font-size: 10px;
  }
}


