import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BookingTemplatesService } from './booking-templates.service';
import { ApiService } from '../api_base/api.service';
import { BehaviorSubject, of, throwError } from 'rxjs';

describe('BookingTemplatesService', () => {
  let service: BookingTemplatesService;
  let apiServiceMock: jest.Mocked<ApiService>;

  beforeEach(() => {
    // Create mock for ApiService
    apiServiceMock = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn()
    } as unknown as jest.Mocked<ApiService>;

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        BookingTemplatesService,
        { provide: ApiService, useValue: apiServiceMock }
      ]
    });

    service = TestBed.inject(BookingTemplatesService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should initialize fetchTemplates as BehaviorSubject', () => {
    expect(service.fetchTemplates).toBeInstanceOf(BehaviorSubject);
  });

  describe('getTemplates', () => {
    it('should call get with correct parameters', () => {
      // Arrange
      const mockQueryPath = { ProjectId: '123' };
      const mockParams = {
        pageSize: 10,
        pageNo: 1,
        search: '',
        sort: 'DESC',
        sortColumn: 'id'
      };

      const mockResponse = {
        data: {
          rows: [
            { id: 1, name: 'Template 1', description: 'Description 1' },
            { id: 2, name: 'Template 2', description: 'Description 2' }
          ],
          count: 2
        }
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getTemplates(mockQueryPath, mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`templates/${mockQueryPath.ProjectId}`, mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty params', () => {
      // Arrange
      const mockQueryPath = { ProjectId: '123' };
      const mockParams = {};
      const mockResponse = { data: { rows: [], count: 0 } };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getTemplates(mockQueryPath, mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`templates/${mockQueryPath.ProjectId}`, mockParams);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('saveTemplate', () => {
    it('should call post with correct parameters', () => {
      // Arrange
      const mockPayload = {
        ProjectId: '123',
        name: 'New Template',
        description: 'New Template Description',
        fields: [
          { name: 'field1', value: 'value1' },
          { name: 'field2', value: 'value2' }
        ]
      };

      const mockResponse = {
        success: true,
        message: 'Template saved successfully',
        data: { id: 3, ...mockPayload }
      };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.saveTemplate(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('templates/', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('editTemplate', () => {
    it('should call put with correct parameters', () => {
      // Arrange
      const mockPayload = {
        id: 1,
        ProjectId: '123',
        name: 'Updated Template',
        description: 'Updated Template Description',
        fields: [
          { name: 'field1', value: 'updated_value1' },
          { name: 'field2', value: 'updated_value2' }
        ]
      };

      const mockResponse = {
        success: true,
        message: 'Template updated successfully',
        data: mockPayload
      };

      apiServiceMock.put.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.editTemplate(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.put).toHaveBeenCalledWith('templates/', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('deleteBookingTemplate', () => {
    it('should call post with correct parameters', () => {
      // Arrange
      const mockData = { ProjectId: '123' };
      const mockPayload = { id: 1 };

      const mockResponse = {
        success: true,
        message: 'Template deleted successfully'
      };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.deleteBookingTemplate(mockData, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(`templates/${mockData.ProjectId}`, mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('updateTemplate', () => {
    it('should call next on fetchTemplates BehaviorSubject', () => {
      // Arrange
      jest.spyOn(service.fetchTemplates, 'next');

      // Act
      service.updateTemplate();

      // Assert
      expect(service.fetchTemplates.next).toHaveBeenCalledWith(true);
    });
  });

  describe('Error handling', () => {
    it('should propagate errors from the API service in getTemplates', (done) => {
      // Arrange
      const mockQueryPath = { ProjectId: '123' };
      const mockParams = {};
      const mockError = 'API Error';

      apiServiceMock.get.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.getTemplates(mockQueryPath, mockParams).subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(mockError);
          done();
        }
      });
    });

    it('should propagate errors from the API service in saveTemplate', (done) => {
      // Arrange
      const mockPayload = { name: 'Test Template' };
      const mockError = 'API Error';

      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.saveTemplate(mockPayload).subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(mockError);
          done();
        }
      });
    });

    it('should propagate errors from the API service in editTemplate', (done) => {
      // Arrange
      const mockPayload = { id: 1, name: 'Updated Template' };
      const mockError = 'API Error';

      apiServiceMock.put.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.editTemplate(mockPayload).subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(mockError);
          done();
        }
      });
    });

    it('should propagate errors from the API service in deleteBookingTemplate', (done) => {
      // Arrange
      const mockData = { ProjectId: '123' };
      const mockPayload = { id: 1 };
      const mockError = 'API Error';

      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.deleteBookingTemplate(mockData, mockPayload).subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(mockError);
          done();
        }
      });
    });
  });

  describe('Service dependencies', () => {
    it('should have ApiService injected', () => {
      // This test verifies that the ApiService is properly injected
      expect(TestBed.inject(ApiService)).toBeDefined();
    });
  });

  describe('Integration with TemplateGridComponent', () => {
    it('should provide data in the format expected by TemplateGridComponent', () => {
      // Arrange
      const mockQueryPath = { ProjectId: '123' };
      const mockParams = {
        pageSize: 10,
        pageNo: 1
      };

      const mockResponse = {
        data: {
          rows: [
            {
              id: 1,
              name: 'Template 1',
              description: 'Description 1',
              createdAt: '2023-01-01T00:00:00.000Z',
              updatedAt: '2023-01-02T00:00:00.000Z'
            }
          ],
          count: 1
        }
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getTemplates(mockQueryPath, mockParams).subscribe(response => {
        result = response;
      });

      // Assert - verify the structure matches what's expected in the component
      expect(result.data).toHaveProperty('rows');
      expect(result.data).toHaveProperty('count');
      expect(result.data.rows[0]).toHaveProperty('id');
      expect(result.data.rows[0]).toHaveProperty('name');
      expect(result.data.rows[0]).toHaveProperty('description');
      expect(result.data.rows[0]).toHaveProperty('createdAt');
      expect(result.data.rows[0]).toHaveProperty('updatedAt');
    });
  });
});
