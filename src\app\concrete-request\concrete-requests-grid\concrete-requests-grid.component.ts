/* eslint-disable max-lines-per-function */
import { Component, OnInit, TemplateRef } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ActivatedRoute, Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import { Observable } from 'rxjs';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { ConcreteDetailHeaderComponent } from '../concrete-detail-header/concrete-detail-header.component';
import { AddConcreteRequestComponent } from '../add-concrete-request/add-concrete-request.component';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { EditConcreteRequestsComponent } from '../edit-concrete-requests/edit-concrete-requests.component';

type DateInput = string | number | Date;

@Component({
  selector: 'app-concrete-requests-grid',
  templateUrl: './concrete-requests-grid.component.html',
})
export class ConcreteRequestsGridComponent implements OnInit {
  public showSearchbar = false;

  public modalLoader = false;

  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public filterCount = 0;

  public ProjectId: any;

  public ParentCompanyId: any;

  public concreteRequestList: any[];

  public requestlist: any[];

  public concreteRequestPageSize = 25;

  public concreteRequestPageNo = 1;

  public concreteRequestTotalCount = 0;

  public concreteLoader: boolean;

  public currentPageNo = 1;

  public userData = [];

  public loader = true;

  public pageSize = 25;

  public pageNo = 1;

  public totalCount = 0;

  public currentTemplate: TemplateRef<any>;

  public statusValue: any = [];

  public currentDeliveryIndex: any = {};

  public currentEditItem: any = {};

  public currentStatus = '';

  public search = '';

  public filterForm: UntypedFormGroup;

  public memberList: any = [];

  public wholeStatus = ['Approved', 'Completed', 'Expired', 'Tentative'];

  public modalRef2: BsModalRef;

  public locationDropdown: [];

  public locationDetailsDropdown: any[];

  public concreteSupplierDropdown: any[];

  public mixDesignDropdown: any[];

  public concreteRequestLastId: any = 0;

  public currentlySelectedTab = 'tab0';

  public deleteConcreteRequestSubmitted = false;

  public deleteRequestData: any = {};

  public sort = 'DESC';

  public sortColumn = 'id';

  public param1: any;

  public param2: any;

  public url: any;

  public selectedConcreteRequestId = '';

  public concreteEditMultipleForm: UntypedFormGroup;

  public authUser: any = {};

  public selectedConcreteRequestIdForMultipleEdit = [];

  public selectedConcreteListStatus = '';

  public selectedstatuslength: number;

  public statustype: any;

  public editModalLoader = false;

  public editMultipleSubmitted = false;

  public editedFields = '';

  public deliveryStart: string | Date;

  public deliveryEnd: string | Date;

  public NDRTimingChanged = false;

  public formEdited = false;

  public submitted = false;

  public formSubmitted = false;

  public currentDeliveryRequestSelectAll = false;

  public companyEdited = false;

  public responsiblePersonEdited = false;

  public deliveryDateEdited = false;

  public formEditSubmitted = false;

  public companyList: any = [];

  public newNdrCompanyDropdownSettings: IDropdownSettings;

  public voidvalue = false;

  public voidEdited = false;

  public statusEdited = false;

  public locationEdited = false;

  public concreteEdited = false;

  public pumpEdited = false;

  public approvedBackgroundColor: string;

  public approvedFontColor: string;

  public rejectedBackgroundColor: string;

  public rejectedFontColor: string;

  public deliveredBackgroundColor: string;

  public deliveredFontColor: string;

  public pendingBackgroundColor: string;

  public pendingFontColor: string;

  public expiredBackgroundColor: string;

  public expiredFontColor: string;

  public constructor(
    private readonly modalService: BsModalService,
    public projectService: ProjectService,
    private readonly formBuilder: UntypedFormBuilder,
    public router: Router,
    public socket: Socket,
    private readonly toastr: ToastrService,
    private readonly deliveryService: DeliveryService,
    private readonly titleService: Title,
    private readonly route: ActivatedRoute,
  ) {
    this.titleService.setTitle('Follo - Concrete Bookings');
    this.projectService.projectParent.subscribe((response19): void => {
      if (response19 !== undefined && response19 !== null && response19 !== '') {
        this.loader = true;
        this.concreteRequestList = [];
        this.ProjectId = response19.ProjectId;
        this.ParentCompanyId = response19.ParentCompanyId;
        this.getConcreteRequest();
        this.newNdrgetCompanies();
        this.getMembers();
      }
      this.filterDetailsForm();
      this.getFilterDropdown();
    });
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
        if (this.authUser.RoleId === 2 || this.authUser.RoleId === 1) {
          this.statusValue = ['Approved', 'Declined'];
        } else if (this.authUser.RoleId === 3) {
          this.statusValue = ['Delivered', 'Approved'];
        }
      }
    });
  }

  public getCompaniesForNdrGrid(): void {
    const getCompaniesForNdrGridParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getCompanies(getCompaniesForNdrGridParams)
      .subscribe((getCompaniesForNdrGridResponse: any): void => {
        if (getCompaniesForNdrGridResponse) {
          this.companyList = getCompaniesForNdrGridResponse.data;
        }
      });
  }

  public newNdrgetCompanies(): void {
    const newNdrGetCompaniesParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getCompanies(newNdrGetCompaniesParams)
      .subscribe((companiesResponseForNewNdr: any): void => {
        if (companiesResponseForNewNdr) {
          this.companyList = companiesResponseForNewNdr.data;
          this.newNdrCompanyDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'companyName',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
        }
      });
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.pageNo = 1;
    this.getConcreteRequest();
  }

  public changeConcretePageSize(pageSize: number): void {
    this.concreteRequestPageSize = pageSize;
    this.getConcreteRequest();
  }

  public openAddModal(): void {
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(AddConcreteRequestComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
    });
  }

  public openDeleteModal(deleteData: any, template: TemplateRef<any>): void {
    this.deleteRequestData = {};
    this.deleteRequestData = deleteData;
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-md new-gate-popup custom-modal',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public resetAndClose(): void {
    this.modalRef.hide();
    this.deleteRequestData = {};
  }

  public deleteConcreteRequest(): void {
    this.deleteConcreteRequestSubmitted = true;
    this.deliveryService
      .deleteConcreteRequest({
        id: this.deleteRequestData.id,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      })
      .subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.getConcreteRequest();
            this.deleteRequestData = {};
            this.deleteConcreteRequestSubmitted = false;
            this.modalRef.hide();
          }
        },
        error: (deleteConcreteRequestError): void => {
          this.deleteConcreteRequestSubmitted = false;
          if (deleteConcreteRequestError.message?.statusCode === 400) {
            this.showError(deleteConcreteRequestError);
          } else if (!deleteConcreteRequestError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deleteConcreteRequestError.message, 'OOPS!');
          }
        },
      });
  }

  public getFilterDropdown(): any {
    this.modalLoader = true;
    if (this.ProjectId) {
      const payload = {
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.deliveryService.getConcreteRequestDropdownData(payload).subscribe((response): void => {
        if (response.data) {
          this.locationDropdown = response.data.locationDropdown;
          this.locationDetailsDropdown = response.data.locationDetailsDropdown;
          this.concreteSupplierDropdown = response.data.concreteSupplierDropdown;
          this.mixDesignDropdown = response.data.mixDesignDropdown;
          this.modalLoader = false;
        }
      });
    }
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }

  public openEditModal(item, ndrState: any): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(EditConcreteRequestsComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
    });
    this.deliveryService.updatedEditConcreteRequestId(+item.ConcreteRequestId);
    this.modalRef.content.closeBtnName = 'Close';
    this.modalRef.content.seriesOption = 1;
    this.modalRef.content.recurrenceId = item?.recurrence ? item?.recurrence?.id : null;
    this.modalRef.content.recurrenceEndDate = item?.recurrence
      ? item?.recurrence?.recurrenceEndDate
      : null;
  }

  public ngOnInit(): void {
    this.router.events.subscribe((e): void => {
      if (this.modalRef) {
        this.modalRef.hide();
      }
    });
    this.deliveryService.fetchConcreteData.subscribe((getNdrResponse): void => {
      if (getNdrResponse !== undefined && getNdrResponse !== null && getNdrResponse !== '') {
        this.getConcreteRequest();
      }
    });
  }

  public handleInputChange(event): void {
    this.voidvalue = false;
    const checkbox = document.getElementById('subscribe') as HTMLInputElement | null;
    if (checkbox.checked === true) {
      this.voidvalue = true;
    } else {
      this.voidvalue = false;
    }
  }

  public redirect(path: any): void {
    this.router.navigate([`/${path}`]);
  }

  public openFilterModal(template: TemplateRef<any>): void {
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-sm filter-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }

  public changeConcreteRequestPageNo(pageNo: number): void {
    this.concreteRequestPageNo = pageNo;
    this.getConcreteRequest();
  }

  public openIdModal(item: { ConcreteRequestId: any; ProjectId: any }): void {
    const className = 'modal-lg new-delivery-popup custom-modal';
    const newPayload = {
      id: item.ConcreteRequestId,
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    const initialState = {
      data: newPayload,
      title: 'Modal with component',
    };
    this.modalRef1 = this.modalService.show(ConcreteDetailHeaderComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
      initialState,
    });
    this.modalRef1.content.closeBtnName = 'Close';
  }

  public getConcreteRequest(): void {
    this.concreteLoader = true;
    this.concreteRequestList = [];
    const getConcreteRequestParam = {
      ProjectId: this.ProjectId,
      pageSize: this.concreteRequestPageSize,
      pageNo: this.concreteRequestPageNo,
      void: 0,
    };
    let getConcreteRequestPayload: any = {};
    if (this.filterForm !== undefined) {
      getConcreteRequestPayload = {
        locationFilter: this.filterForm.value.locationFilter,
        locationPathFilter: this.filterForm.value.locationPathFilter,
        descriptionFilter: this.filterForm.value.descriptionFilter,
        statusFilter: this.filterForm.value.statusFilter,
        orderNumberFilter: this.filterForm.value.orderNumberFilter,
        concreteSupplierFilter: this.filterForm.value.concreteSupplierFilter,
        mixDesignFilter: this.filterForm.value.mixDesignFilter,
      };
    }
    getConcreteRequestPayload.search = this.search;
    getConcreteRequestPayload.sort = this.sort;
    getConcreteRequestPayload.sortByField = this.sortColumn;
    getConcreteRequestPayload.ParentCompanyId = this.ParentCompanyId;
    this.deliveryService
      .getConcreteRequestList(getConcreteRequestParam, getConcreteRequestPayload)
      .subscribe((response: any): void => {
        if (response) {
          const statusCode = JSON.parse(response?.statusData.statusColorCode);

          const approved = statusCode.find((item) => item.status === 'approved');
          const pending = statusCode.find((item) => item.status === 'pending');
          const delivered = statusCode.find((item) => item.status === 'delivered');
          const rejected = statusCode.find((item) => item.status === 'rejected');
          const expired = statusCode.find((item) => item.status === 'expired');

          this.approvedBackgroundColor = approved.backgroundColor;
          this.approvedFontColor = approved.fontColor;
          this.rejectedBackgroundColor = rejected.backgroundColor;
          this.rejectedFontColor = rejected.fontColor;
          this.expiredBackgroundColor = expired.backgroundColor;
          this.expiredFontColor = expired.fontColor;
          this.deliveredBackgroundColor = delivered.backgroundColor;
          this.deliveredFontColor = delivered.fontColor;
          this.pendingBackgroundColor = pending.backgroundColor;
          this.pendingFontColor = pending.fontColor;



          this.concreteRequestLastId = response?.lastId?.ConcreteRequestId;
          this.concreteLoader = false;
          this.concreteRequestList = response?.data?.rows;
          this.concreteRequestList.forEach((item: any, indexValue: string | number): void => {
            if (this.authUser.RoleId === 4 || this.authUser.RoleId === 3) {
              const responsibleMemberArray = item.memberDetails;
              const index = responsibleMemberArray.findIndex(
                (i: { Member: { User: any } }): boolean => i.Member.User.id === this.authUser.UserId,
              );
              if (index !== -1) {
                this.concreteRequestList[indexValue].isAllowedToEdit = true;
              } else {
                this.concreteRequestList[indexValue].isAllowedToEdit = false;
              }
            } else {
              this.concreteRequestList[indexValue].isAllowedToEdit = true;
            }
            return null;
          });
          this.concreteRequestTotalCount = response?.data?.count;
          const isTokenExists = localStorage.getItem('token');
          if (isTokenExists) {
            this.route.queryParams.subscribe((params): any => {
              this.param1 = params.requestId;
              this.param2 = params.memberId;
              this.url = this.router.url.split('?');
              this.router.navigateByUrl(this.url[0]);
              if (this.param1 && this.param2) {
                const object = {
                  encryptedRequestId: this.param1,
                  encryptedMemberId: this.param2,
                  ProjectId: +localStorage.getItem('ProjectId'),
                  ParentCompanyId: +localStorage.getItem('currentCompanyId'),
                };
                this.deliveryService.decryption(object).subscribe((res): void => {
                  if (res?.data) {
                    const requestId = res.data.decryptedRequestId;
                    const newPayload = {
                      id: +requestId,
                      ProjectId: +localStorage.getItem('ProjectId'),
                      ParentCompanyId: +localStorage.getItem('currentCompanyId'),
                    };
                    if (newPayload.ProjectId > 0 && newPayload.ParentCompanyId > 0) {
                      const initialState = {
                        data: newPayload,
                        title: 'Modal with component',
                      };
                      this.modalRef = this.modalService.show(ConcreteDetailHeaderComponent, {
                        backdrop: 'static',
                        keyboard: false,
                        class: 'modal-lg new-delivery-popup custom-modal',
                        initialState,
                      });
                    }
                  }
                });
              }
            });
          } else {
            this.router.navigate(['/login']);
          }
        }
      });
  }

  public sortConcreteRequestByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getConcreteRequest();
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.search = '';
    this.pageNo = 1;
    this.filterDetailsForm();
    this.getConcreteRequest();
    this.modalRef.hide();
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('descriptionFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('locationFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('locationPathFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('orderNumberFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('concreteSupplierFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('statusFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('mixDesignFilter').value !== '') {
      this.filterCount += 1;
    }
    this.pageNo = 1;
    this.getConcreteRequest();
    this.modalRef.hide();
  }

  public getSearch(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.pageNo = 1;
    this.search = data;
    this.getConcreteRequest();
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      descriptionFilter: [''],
      locationFilter: [''],
      locationPathFilter: [''],
      concreteSupplierFilter: [''],
      orderNumberFilter: [''],
      statusFilter: [''],
      mixDesignFilter: [''],
    });
  }

  public openEditMultipleModal(template: TemplateRef<any>): void {
    this.concreteForm();
    this.setDefaultPerson();
    this.currentTemplate = template;
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-delivery-popup custom-modal edit-multiple-modal',
    };
    this.modalRef = this.modalService.show(this.currentTemplate, data);
    this.selectedConcreteRequestId = this.selectedConcreteRequestId.replace(/,\s*$/, '');
  }

  public concreteForm(): void {
    this.concreteEditMultipleForm = this.formBuilder.group({
      person: [''],
      deliveryDate: [''],
      deliveryStart: [''],
      deliveryEnd: [''],
      escort: [false],
      companyItems: [this.formBuilder.array([])],
      status: [''],
      isConcreteConfirmed: [''],
      concreteConfirmedOn: [''],
      isPumpConfirmed: [''],
      pumpConfirmedOn: [''],
      location: [''],
      void: [''],
    });
  }

  public changeConcreteConfirmed(data): void {
    if (data) {
      this.concreteEditMultipleForm.get('isConcreteConfirmed').setValue(true);
      this.concreteEditMultipleForm.get('concreteConfirmedOn').setValue(new Date());
    } else {
      this.concreteEditMultipleForm.get('isConcreteConfirmed').setValue(null);
      this.concreteEditMultipleForm.get('concreteConfirmedOn').setValue(null);
    }
  }

  public changePumpConfirmed(data): void {
    if (data) {
      this.concreteEditMultipleForm.get('isPumpConfirmed').setValue(true);
      this.concreteEditMultipleForm.get('pumpConfirmedOn').setValue(new Date());
    } else {
      this.concreteEditMultipleForm.get('isPumpConfirmed').setValue(null);
      this.concreteEditMultipleForm.get('pumpConfirmedOn').setValue(null);
    }
  }

  public setDefaultPerson(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.getMemberRole(params).subscribe((res): void => {
      this.authUser = res.data;
      this.deliveryService.updateLoginUser(this.authUser);
      let email: string;
      if (this.authUser.User.lastName != null) {
        email = `${this.authUser.User.firstName} ${this.authUser?.User?.lastName} (${this.authUser.User.email})`;
      } else {
        email = `${this.authUser.User.firstName} (${this.authUser.User.email})`;
      }
      const newMemberList = [
        {
          email,
          id: this.authUser.id,
          readonly: true,
        },
      ];
      this.concreteEditMultipleForm.get('person').patchValue(newMemberList);
    });
  }

  public editMultipleConfirmation(action: string): void {
    if (action === 'no') {
      this.modalRef2.hide();
    } else {
      this.modalRef2.hide();
      this.onEditMultipleSubmit();
    }
  }

  public onEditMultipleSubmit(): void {
    const deliveryDate = new Date(this.concreteEditMultipleForm.get('deliveryDate').value);
    const startHours = new Date(
      this.concreteEditMultipleForm.get('deliveryStart').value,
    ).getHours();
    const startMinutes = new Date(
      this.concreteEditMultipleForm.get('deliveryStart').value,
    ).getMinutes();
    this.deliveryStart = this.convertStart(deliveryDate, startHours, startMinutes);
    const endHours = new Date(this.concreteEditMultipleForm.get('deliveryEnd').value).getHours();
    const endMinutes = new Date(
      this.concreteEditMultipleForm.get('deliveryEnd').value,
    ).getMinutes();
    this.deliveryEnd = this.convertStart(deliveryDate, endHours, endMinutes);
    if (this.deliveryDateEdited) {
      if (this.checkNewDeliveryStartEndSame(this.deliveryStart, this.deliveryEnd)) {
        this.toastr.error('Delivery Start time and End time should not be the same');
        return;
      }
      if (!this.checkStartEnd(this.deliveryStart, this.deliveryEnd)) {
        this.toastr.error('Please Enter Start time Lesser than End time');
        return;
      }
      if (!this.checkEditDeliveryFutureDate(this.deliveryStart, this.deliveryEnd)) {
        this.toastr.error('Please Enter Future Date.');
        return;
      }
    }
    const formValue = this.concreteEditMultipleForm.value;

    const isConcreteConfirmed = formValue.isConcreteConfirmed === null || formValue.isConcreteConfirmed === undefined;
    const isPumpConfirmed = formValue.isPumpConfirmed === null || formValue.isPumpConfirmed === undefined;
    const payload: any = {
      concreteRequestIds: this.selectedConcreteRequestIdForMultipleEdit,
      deliveryStart: this.deliveryStart,
      deliveryEnd: this.deliveryEnd,
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      editedFields: this.editedFields,
      status: this.concreteEditMultipleForm.get('status').value,
      isPumpConfirmed: isPumpConfirmed ? false : formValue.isPumpConfirmed,
      pumpConfirmedOn: formValue.pumpConfirmedOn,
      isConcreteConfirmed: isConcreteConfirmed ? false : formValue.isConcreteConfirmed,
      concreteConfirmedOn: formValue.concreteConfirmedOn,
      void: this.voidvalue,
      location: this.concreteEditMultipleForm.get('location').value,
    };
    if (
      this.concreteEditMultipleForm.get('companyItems').value
      && this.concreteEditMultipleForm.get('companyItems').value.length > 0
    ) {
      const companies = [];
      this.concreteEditMultipleForm
        .get('companyItems')
        .value.forEach((element: { id: any }): void => {
          companies.push(element.id);
        });
      payload.companies = companies;
    }
    if (
      this.concreteEditMultipleForm.get('person').value
      && this.concreteEditMultipleForm.get('person').value.length > 0
    ) {
      const persons = [];
      this.concreteEditMultipleForm.get('person').value.forEach((element: { id: any }): void => {
        persons.push(element.id);
      });
      payload.persons = persons;
    }
    this.editMultipleSubmitted = true;
    this.deliveryService.updateConcreteRequest(payload).subscribe({
      next: (editNdrResponse: any): void => {
        if (editNdrResponse) {
          this.editNDRSuccess(editNdrResponse);
        }
      },
      error: (editNDRHistoryError): void => {
        this.showErrorMessage(editNDRHistoryError);
      },
    });
  }

  public convertStart(deliveryDate: Date, startHours: number, startMinutes: number): string {
    const fullYear = deliveryDate.getFullYear();
    const fullMonth = deliveryDate.getMonth();
    const date = deliveryDate.getDate();
    const deliveryNewStart = new Date(fullYear, fullMonth, date, startHours, startMinutes);
    const deliveryStart = deliveryNewStart.toUTCString();
    return deliveryStart;
  }

  public checkNewDeliveryStartEndSame(
    newDeliveryStartTime: DateInput,
    newDeliveryEndTime: DateInput,
  ): boolean {
    const startDate = new Date(newDeliveryStartTime).getTime();
    const endDate = new Date(newDeliveryEndTime).getTime();
    if (startDate === endDate) {
      return true;
    }
    return false;
  }

  public checkStartEnd(
    deliveryStart: DateInput,
    deliveryEnd: DateInput,
  ): boolean {
    const startDate = new Date(deliveryStart).getTime();
    const endDate = new Date(deliveryEnd).getTime();
    if (startDate < endDate) {
      return true;
    }
    return false;
  }

  public checkEditDeliveryFutureDate(
    editDeliveryStart: DateInput,
    editDeliveryEnd: DateInput,
  ): boolean {
    const editStartDate = new Date(editDeliveryStart).getTime();
    const editCurrentDate = new Date().getTime();
    const editEndDate = new Date(editDeliveryEnd).getTime();
    if (editStartDate > editCurrentDate && editEndDate > editCurrentDate) {
      return true;
    }
    return false;
  }

  public showErrorMessage(editNDRHistoryError): void {
    this.concreteForm();
    this.editMultipleSubmitted = false;
    this.currentDeliveryRequestSelectAll = false;
    this.modalRef.hide();
    this.NDRTimingChanged = false;
    this.companyEdited = false;
    this.responsiblePersonEdited = false;
    this.deliveryDateEdited = false;
    this.voidEdited = false;
    this.statusEdited = false;
    this.locationEdited = false;
    this.pumpEdited = false;
    this.concreteEdited = false;
    if (editNDRHistoryError.message?.statusCode === 400) {
      this.showError(editNDRHistoryError);
    } else if (!editNDRHistoryError.message) {
      this.toastr.error('Try again later.!', 'Something went wrong.');
    } else {
      this.toastr.error(editNDRHistoryError.message, 'OOPS!');
    }
  }

  public editNDRSuccess(response: { message: string }): void {
    this.toastr.success(response.message, 'Success');
    this.socket.emit('ConcreteCreateHistory', response);
    this.concreteForm();
    this.deliveryService.updateConcreteRequestHistory({ status: true }, 'ConcreteCreateHistory');
    this.NDRTimingChanged = false;
    this.companyEdited = false;
    this.responsiblePersonEdited = false;
    this.deliveryDateEdited = false;
    this.voidEdited = false;
    this.statusEdited = false;
    this.locationEdited = false;
    this.pumpEdited = false;
    this.concreteEdited = false;
    this.modalRef.hide();
    this.currentDeliveryRequestSelectAll = false;
    this.editMultipleSubmitted = false;
    this.voidvalue = false;
    this.selectedConcreteRequestId = '';
  }

  public selectAllCurrentDeliveryRequestForEdit(): void {
    this.selectedConcreteRequestId = '';
    this.selectedConcreteListStatus = '';
    this.selectedConcreteRequestIdForMultipleEdit = [];
    this.currentDeliveryRequestSelectAll = !this.currentDeliveryRequestSelectAll;
    if (this.currentDeliveryRequestSelectAll) {
      this.concreteRequestList.forEach((obj, element): void => {
        if (obj.isAllowedToEdit) {
          this.concreteRequestList[element].isChecked = true;
          this.selectedConcreteRequestId += `${this.concreteRequestList[element].ConcreteRequestId},`;
          this.selectedConcreteListStatus += `${this.concreteRequestList[element].status},`;
          this.selectedConcreteRequestIdForMultipleEdit.push(this.concreteRequestList[element].id);
        } else {
          this.concreteRequestList[element].isChecked = false;
        }
        return null;
      });
      this.selectedConcreteRequestId = this.selectedConcreteRequestId.replace(/,\s*$/, '');
      const output = this.selectedConcreteListStatus.split(',');
      const arr = output.filter((item): any => item);
      const unique = arr.filter((item, i, ar): any => ar.indexOf(item) === i);
      this.statustype = unique;
      this.selectedstatuslength = unique.length;
    } else {
      this.concreteRequestList.forEach((obj, element): void => {
        this.concreteRequestList[element].isChecked = false;
      });
    }
  }

  public setSelectedCurrentDeliveryRequestItem(index: string | number): void {
    this.concreteRequestList[index].isChecked = !this.concreteRequestList[index].isChecked;
    if (this.concreteRequestList[index].isChecked) {
      this.selectedConcreteRequestId += `${this.concreteRequestList[index].ConcreteRequestId},`;
      this.selectedConcreteRequestIdForMultipleEdit.push(this.concreteRequestList[index].id);
      this.selectedConcreteListStatus += `${this.concreteRequestList[index].status},`;
      const output = this.selectedConcreteListStatus.split(',');
      const arr = output.filter((item): any => item);
      const unique = arr.filter((item, i, ar): any => ar.indexOf(item) === i);
      this.statustype = unique;
      this.selectedstatuslength = unique.length;
    } else {
      const selectedIds = this.selectedConcreteRequestId.replace(
        `${this.concreteRequestList[index].ConcreteRequestId},`,
        '',
      );
      this.selectedConcreteRequestId = '';
      this.selectedConcreteRequestId = selectedIds;
      const ifDeliveryIdExists = this.selectedConcreteRequestIdForMultipleEdit.indexOf(
        this.concreteRequestList[index].id,
      );
      if (ifDeliveryIdExists > -1) {
        this.selectedConcreteRequestIdForMultipleEdit.splice(ifDeliveryIdExists, 1);
      }
    }
  }

  public checkIfCurrentDeliveryRequestRowSelected(): boolean {
    if (this.currentDeliveryRequestSelectAll) {
      return false;
    }
    const indexFind = this.concreteRequestList.findIndex(
      (item: { isChecked: boolean }): boolean => item.isChecked === true,
    );
    if (indexFind !== -1) {
      return false;
    }
    return true;
  }

  public closeEditMultiplePopup(): void {
    this.formEditSubmitted = false;
    this.modalRef.hide();
    this.editModalLoader = false;
    this.selectedConcreteRequestId = '';
    this.selectedConcreteListStatus = '';
    this.concreteRequestList.forEach((element): void => {
      this.concreteRequestList[element].isChecked = false;
    });
    this.currentDeliveryRequestSelectAll = false;
    this.selectedConcreteRequestIdForMultipleEdit = [];
    this.editMultipleSubmitted = false;
    this.companyEdited = false;
    this.responsiblePersonEdited = false;
    this.deliveryDateEdited = false;
    this.voidEdited = false;
    this.statusEdited = false;
    this.locationEdited = false;
    this.pumpEdited = false;
    this.concreteEdited = false;
  }

  public numberOnly(event: { which: any; keyCode: any }): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public deliveryEndTimeChangeDetection(): void {
    this.NDRTimingChanged = true;
  }

  public changeDate(event: any): void {
    if (!this.editModalLoader) {
      const startTime = new Date(event).getHours();
      const minutes = new Date(event).getMinutes();
      this.deliveryEnd = new Date();
      this.deliveryEnd.setHours(startTime + 1);
      this.deliveryEnd.setMinutes(minutes);
      this.concreteEditMultipleForm.get('deliveryEnd').setValue(this.deliveryEnd);
      this.NDRTimingChanged = true;
    }
  }

  public requestAutocompleteItems = (text: string): Observable<any> => {
    const param = {
      ProjectId: this.ProjectId,
      search: text,
      ParentCompanyId: this.ParentCompanyId,
    };
    return this.deliveryService.searchNewMember(param);
  };

  public onEditSubmitForm(fieldName: string): void {
    if (fieldName === 'companies') {
      this.companyEdited = true;
    }
    if (fieldName === 'persons') {
      this.responsiblePersonEdited = true;
    }
    if (fieldName === 'deliveryDate') {
      if (this.concreteEditMultipleForm.get('deliveryDate').value) {
        this.setDefaultDeliveryTime();
        this.deliveryDateEdited = true;
      } else {
        this.deliveryDateEdited = false;
        this.concreteEditMultipleForm.get('deliveryStart').setValue('');
        this.concreteEditMultipleForm.get('deliveryEnd').setValue('');
      }
    }
    if (fieldName === 'location') {
      this.locationEdited = true;
    }
    if (fieldName === 'status') {
      this.statusEdited = true;
    }
    if (fieldName === 'void') {
      this.voidEdited = true;
    }
    if (fieldName === 'pump') {
      this.pumpEdited = true;
    }
    if (fieldName === 'concrete') {
      this.concreteEdited = true;
    }
  }

  public setDefaultDeliveryTime(): void {
    const setStartTime = 7;
    this.deliveryStart = new Date();
    this.deliveryStart.setHours(setStartTime);
    this.deliveryStart.setMinutes(0);
    this.deliveryEnd = new Date();
    this.deliveryEnd.setHours(setStartTime + 1);
    this.deliveryEnd.setMinutes(0);
    this.concreteEditMultipleForm.get('deliveryStart').setValue(this.deliveryStart);
    this.concreteEditMultipleForm.get('deliveryEnd').setValue(this.deliveryEnd);
  }

  public openConfirmationPopup(template: TemplateRef<any>): void {
    const formValues = this.concreteEditMultipleForm.value;

    const editChecks = [
      {
        edited: this.companyEdited, value: formValues.companyItems, condition: (v: any) => v?.length > 0, label: 'Responsible Company',
      },
      {
        edited: this.responsiblePersonEdited, value: formValues.person, condition: (v: any) => v?.length > 1, label: 'Responsible Person',
      },
      {
        edited: this.deliveryDateEdited, value: formValues.deliveryDate, condition: Boolean, label: 'Delivery Date',
      },
      {
        edited: this.statusEdited, value: formValues.status, condition: Boolean, label: 'Status',
      },
      {
        edited: this.voidEdited, value: formValues.void, condition: Boolean, label: 'Void',
      },
      {
        edited: this.locationEdited, value: formValues.location, condition: Boolean, label: 'Location',
      },
      {
        edited: this.pumpEdited, value: formValues.isPumpConfirmed, condition: Boolean, label: 'Pump confirmed',
      },
      {
        edited: this.concreteEdited, value: formValues.isConcreteConfirmed, condition: Boolean, label: 'Concrete confirmed',
      },
    ];

    this.editedFields = editChecks.reduce(
      (acc, {
        edited, value, condition, label,
      }) => (edited && condition(value) ? [...acc, label] : acc),
      [] as string[],
    ).join(', ');

    if (this.editedFields) {
      const data = {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      };
      this.modalRef2 = this.modalService.show(template, data);
    }
  }


  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortConcreteRequestByField(data, item);
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'clear':
          this.clear();
          break;
        case 'filter':
          this.openFilterModal(data);
          break;
        case 'open':
          this.openIdModal(data);
          break;
        case 'edit':
          this.openEditModal(data, item);
          break;
        case 'delete':
          this.openDeleteModal(data, item);
          break;
        default:
          break;
      }
    }
  }
}
