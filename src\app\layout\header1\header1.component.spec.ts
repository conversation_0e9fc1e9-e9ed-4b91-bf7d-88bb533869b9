import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Header1Component } from './header1.component';

describe('Header1Component', () => {
  let component: Header1Component;
  let fixture: ComponentFixture<Header1Component>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [Header1Component]
    }).compileComponents();

    fixture = TestBed.createComponent(Header1Component);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('navbar functionality', () => {
    it('should toggle navbarOpen state when toggleNavbar is called', () => {
      expect(component.navbarOpen).toBeFalsy();
      component.toggleNavbar();
      expect(component.navbarOpen).toBeTruthy();
      component.toggleNavbar();
      expect(component.navbarOpen).toBeFalsy();
    });
  });

  describe('sidebar functionality', () => {
    it('should toggle shouldShow state and emit sidemenuToggle event', () => {
      const emitSpy = jest.spyOn(component['sidemenuToggle'], 'emit');
      
      expect(component.shouldShow).toBeUndefined();
      component.sidebarToggle();
      expect(component.shouldShow).toBeTruthy();
      expect(emitSpy).toHaveBeenCalledWith(true);

      component.sidebarToggle();
      expect(component.shouldShow).toBeFalsy();
      expect(emitSpy).toHaveBeenCalledWith(false);
    });
  });

  describe('keyboard event handling', () => {
    it('should call sidebarToggle when Enter key is pressed', () => {
      const sidebarToggleSpy = jest.spyOn(component, 'sidebarToggle');
      const event = new KeyboardEvent('keydown', { key: 'Enter' });

      component.handleToggleKeydown(event);
      expect(sidebarToggleSpy).toHaveBeenCalled();
    });

    it('should call sidebarToggle when Space key is pressed', () => {
      const sidebarToggleSpy = jest.spyOn(component, 'sidebarToggle');
      const event = new KeyboardEvent('keydown', { key: ' ' });

      component.handleToggleKeydown(event);
      expect(sidebarToggleSpy).toHaveBeenCalled();
    });

    it('should not call sidebarToggle for other keys', () => {
      const sidebarToggleSpy = jest.spyOn(component, 'sidebarToggle');
      const event = new KeyboardEvent('keydown', { key: 'A' });

      component.handleToggleKeydown(event);
      expect(sidebarToggleSpy).not.toHaveBeenCalled();
    });

    it('should prevent default behavior for Enter and Space keys', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      const preventDefaultSpy = jest.spyOn(event, 'preventDefault');

      component.handleToggleKeydown(event);
      expect(preventDefaultSpy).toHaveBeenCalled();
    });
  });
});
