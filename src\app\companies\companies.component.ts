/* eslint-disable max-lines-per-function */
import { Component, TemplateRef, OnInit } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute } from '@angular/router';
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { Title } from '@angular/platform-browser';
import { ProjectService } from '../services/profile/project.service';
import { AuthService } from '../services/auth/auth.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { MixpanelService } from '../services/mixpanel.service';

@Component({
  selector: 'app-companies',
  templateUrl: './companies.component.html',
  })
export class CompaniesComponent implements OnInit {
  public currentPageNo = 1;

  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public modalRef2: BsModalRef;

  public ProjectId: string | number;

  public companiesList: any = [];

  public memberList: any = [];

  public submitted = false;

  public editSubmitted = false;

  public editIndex: any;

  public selectedItems: any = [];

  public editFormSubmitted = false;

  public dropdownSettings: IDropdownSettings;

  public parentCompany: any = {};

  public defineList: any = [];

  public filterCount = 0;

  public editSelectedItems: any = [];

  public formSubmitted = false;

  public currentDeleteId: any;

  public deleteSubmitted = false;

  public loader = true;

  public pageSize = 25;

  public pageNo = 1;

  public totalCount = 0;

  public editLogo = '';

  public definableWorkId: any = [];

  public companyDetailsForm: UntypedFormGroup;

  public companyEditForm: UntypedFormGroup;

  public selectAll = false;

  public deleteIndex: any = [];

  public remove = false;

  public filterForm: UntypedFormGroup;

  public search = '';

  public companyLogo: any = {};

  public file: File = null;

  public editFile: File = null;

  public editCompanyLogo: any = {};

  public modalLoader = false;

  public ParentCompanyId = -1;

  public userData: any = {};

  public isAdmin = false;

  public editBeforeDFOW = [];

  public arrayValue: any = [];

  public existCompanyName: string;

  public addCompanyPopupSelected = false;

  public sort = 'DESC';

  public sortColumn = 'id';

  public editedCompanyId;

  public showSearchbar = false;

  public companiesFiles: NgxFileDropEntry[] = [];

  public formData: any = [];

  public importSubmitted = false;

  public todayDate = new Date();

  public bulkCompanyUploadInProgress = false;

  public newCompanyTriedToAdd: string = '';

  public constructor(
    private readonly modalService: BsModalService,
    public projectService: ProjectService,
    private readonly titleService: Title,
    public deliveryService: DeliveryService,
    private readonly mixpanelService: MixpanelService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly authService: AuthService,
    private readonly route: ActivatedRoute,
    private readonly toastr: ToastrService,
  ) {
    this.titleService.setTitle('Follo - Companies');
    this.projectService.userData.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.userData = res;
        if (
          this.userData.RoleId === 2
          || this.userData.RoleId === 1
          || this.userData.RoleId === 3
        ) {
          this.isAdmin = true;
        }
      }
    });
    this.projectService.companyFileUpload.subscribe((res): void => {
      if (res && res.status === 'companiesUploading') {
        this.bulkCompanyUploadInProgress = true;
      }
      if (res && res.status === 'companyUploadDone') {
        this.projectService.uploadBulkNdrFile({ status: '' });
        this.bulkCompanyUploadInProgress = false;
        this.sortColumn = 'id';
        this.getCompanies();
      }
    });
    this.projectService.projectParent.subscribe((response18): void => {
      this.ProjectId = response18.ProjectId;
      this.ParentCompanyId = response18.ParentCompanyId;
      if (response18 !== undefined && response18 !== null && response18 !== '') {
        this.loader = true;
        this.getCompanies();
        this.defineList = [];
      }
    });
    this.companyForm();
    this.editCompanyForm();
    this.filterDetailsForm();
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.search = '';
    this.pageNo = 1;
    this.filterDetailsForm();
    this.getCompanies();
    this.modalRef.hide();
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      companyFilter: [''],
      dfowFilter: [''],
    });
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('companyFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('dfowFilter').value !== '') {
      this.filterCount += 1;
    }
    this.pageNo = 1;
    this.getCompanies();
    this.modalRef.hide();
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.pageNo = 1;
    this.getCompanies();
  }

  public getSearchCompany(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.pageNo = 1;
    this.search = data;
    this.getCompanies();
  }

  public ngOnInit(): void { /* */ }

  public openImportModal(template: TemplateRef<any>): void {
    this.modalRef = this.modalService.show(template, {
      backdrop: 'static',
      class: 'modal-lg custom-modal',
    });
  }

  public getDefinable(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getDefinableWork(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.defineList = data;
        this.dropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'DFOW',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
        this.modalLoader = false;
      }
    });
  }

  public getCompanies(): void {
    this.loader = true;
    const param = {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.currentPageNo,
      ParentCompanyId: this.ParentCompanyId,
    };
    let payload: any = {};
    if (this.filterForm !== undefined) {
      payload = {
        companyFilter: this.filterForm.value.companyFilter,
        dfowFilter: +this.filterForm.value.dfowFilter,
        search: this.search,
      };
    }
    payload.search = this.search;
    payload.sort = this.sort;
    payload.sortByField = this.sortColumn;
    payload.inviteMember = false;
    this.projectService.listCompany(param, payload).subscribe((response: any): void => {
      if (response) {
        const responseData = response.data;
        this.loader = false;
        this.companiesList = responseData.rows;
        if (this.selectAll && this.companiesList.length > 0) {
          this.companiesList.map((obj, index): void => {
            this.companiesList[index].isChecked = true;
            return null;
          });
        } else {
          this.selectAll = false;
          this.companiesList.map((obj, index): void => {
            this.companiesList[index].isChecked = false;
            return null;
          });
        }
        this.totalCount = responseData.count;
        this.parentCompany = response?.parentCompany ? response?.parentCompany[0] : [];
      }
    });
  }

  public alphaNumericOnlyForCompanyName(event): boolean {
    const keyPressedForCompanyName = event.keyCode;
    return (
      (keyPressedForCompanyName >= 65 && keyPressedForCompanyName <= 90)
      || (keyPressedForCompanyName >= 97 && keyPressedForCompanyName <= 128)
      || keyPressedForCompanyName === 8
      || keyPressedForCompanyName === 32
      || (keyPressedForCompanyName >= 48 && keyPressedForCompanyName <= 57)
      || (keyPressedForCompanyName > 31
        && (keyPressedForCompanyName < 48 || keyPressedForCompanyName > 57))
    );
  }

  public numberOnlyForCompanyZipCode(event): boolean {
    const zipCode = event.which ? event.which : event.keyCode;
    if (zipCode > 31 && (zipCode < 48 || zipCode > 57)) {
      return false;
    }
    return true;
  }

  public changePageSizeOfCompanyGrid(pageSize): void {
    this.pageSize = pageSize;
    this.getCompanies();
  }

  public changePageNo(pageNo): void {
    this.currentPageNo = pageNo;
    this.getCompanies();
  }

  public companyForm(): void {
    const reg = '(https?:\\/\\/(?:www\\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\\.[^\\s]{2,}|www\\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\\.[^\\s]{2,}|https?:\\/\\/(?:www\\.|(?!www))[a-zA-Z0-9]+\\.[^\\s]{2,}|www\\.[a-zA-Z0-9]+\\.[^\\s]{2,})';
    this.companyDetailsForm = this.formBuilder.group({
      companyName: ['', Validators.required],
      scope: [''],
      website: [
        '',
        Validators.compose([
          // Validators.required,
          Validators.pattern(reg),
        ]),
      ],
      address: [''],
      selectedItems: [this.formBuilder.array([])],
      secondAddress: [''],
      country: [''],
      city: [''],
      state: [''],
      zipCode: [''],
    });
  }

  public onFileChangeEvent(event): void {
    const reader = new FileReader();
    const data = event.target.files[0];
    this.file = data;
    const relativePath = data.name.split('.');
    const extension = relativePath[relativePath.length - 1];
    if (extension === 'jpg' || extension === 'png' || extension === 'jpeg') {
      this.companyLogo = data;
      reader.readAsDataURL(data);
      reader.onload = (_event): void => {
        this.companyLogo.logo = reader.result;
      };
    } else {
      this.toastr.error('Please uploade .jpeg,.jpg,.png file.', 'OOPS!');
    }
  }

  public onEditFileChangeEvent(event): void {
    const reader = new FileReader();
    const data = event.target.files[0];
    this.editFile = data;
    const relativePath = data.name.split('.');
    const extension = relativePath[relativePath.length - 1];
    if (extension === 'jpg' || extension === 'png' || extension === 'jpeg') {
      this.editCompanyLogo = data;
      reader.readAsDataURL(data);
      reader.onload = (_event): void => {
        this.editCompanyLogo.logo = reader.result;
      };
    } else {
      this.toastr.error('Please uploade .jpeg,.jpg,.png file.', 'OOPS!');
    }
  }

  public editCompanyForm(): void {
    const reg = '(https?:\\/\\/(?:www\\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\\.[^\\s]{2,}|www\\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\\.[^\\s]{2,}|https?:\\/\\/(?:www\\.|(?!www))[a-zA-Z0-9]+\\.[^\\s]{2,}|www\\.[a-zA-Z0-9]+\\.[^\\s]{2,})';
    this.companyEditForm = this.formBuilder.group({
      id: ['', Validators.compose([Validators.required])],
      companyName: ['', Validators.compose([Validators.required])],
      scope: [''],
      selectedItems: [this.formBuilder.array([])],
      website: [
        '',
        Validators.compose([
          // Validators.required,
          Validators.pattern(reg),
        ]),
      ],
      address: [''],
      secondAddress: [''],
      country: [''],
      city: [''],
      state: [''],
      zipCode: [''],
    });
  }

  public checkStringEmptyValues(formValue): boolean {
    if (formValue.companyName.trim() === '') {
      this.toastr.error('Please Enter valid Company Name.', 'OOPS!');
      return true;
    }
    if (formValue.scope && formValue.scope !== null && formValue.scope !== undefined) {
      if (formValue.scope.trim() === '') {
        this.toastr.error('Please Enter valid Notes.', 'OOPS!');
        return true;
      }
    }
    return false;
  }

  public alphaNumForZipCode(event): boolean {
    const zipCodeKey = event.which ? event.which : event.keyCode;
    const firstCondition = zipCodeKey >= 65 && zipCodeKey <= 90;
    const secondCondition = zipCodeKey >= 97 && zipCodeKey <= 128;
    if (
      zipCodeKey > 31
      && (zipCodeKey < 48 || zipCodeKey > 57)
      && !firstCondition
      && !secondCondition
    ) {
      return false;
    }

    return true;
  }

  public companyNameConfirmationToAdd(action): void {
    if (action === 'no') {
      this.modalRef2.hide();
    } else {
      this.modalRef2.hide();
      if (this.addCompanyPopupSelected) {
        this.onSubmit();
      } else {
        this.onEditSubmit();
      }
    }
  }

  public sortByField(fieldName, sortType): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getCompanies();
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    this.definableWorkId = [];
    if (this.companyDetailsForm.invalid) {
      this.formSubmitted = false;
      return;
    }
    const formValue = this.companyDetailsForm.value;
    const trimValue = formValue.secondAddress;
    const condition = formValue.secondAddress ? trimValue.trim() : formValue.secondAddress;
    if (
      this.companyDetailsForm.valid
      && formValue.selectedItems
      && formValue.selectedItems.length > 0
    ) {
      formValue.selectedItems.forEach((element): void => {
        this.definableWorkId.push(element.id);
      });
    }

    if (!this.checkStringEmptyValues(formValue)) {
      const payload: any = {
        companyName: formValue.companyName.trim(),
        scope: formValue.scope ? formValue.scope.trim() : formValue.scope,
        website: formValue?.website,
        country: formValue.country,
        city: formValue.city,
        secondAddress: condition,
        definableWorkId: this.definableWorkId,
        state: formValue.state,
        address: formValue.address,
        isParent: false,
        zipCode: formValue.zipCode,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      const formData = new FormData();
      if (this.file != null) {
        const params = {
          ProjectId: this.ProjectId,
          ParentCompanyId: this.ParentCompanyId,
        };
        formData.append('logo', this.file, this.file.name);
        this.projectService.companyLogoUpload(params, formData).subscribe((response: any): void => {
          payload.logo = response.data[0].Location;
          this.addCompanyToProject(payload);
        });
      } else {
        this.addCompanyToProject(payload);
      }
    } else {
      this.formSubmitted = false;
      this.submitted = false;
    }
  }

  public download(): any {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.importCompanyTemplate(params).subscribe((res: any): void => {
      this.projectService.getProject().subscribe({
        next: (response): any => {
          const project = response.data.filter((data): {} => +data.id === +this.ProjectId);
          const fileName = `${project[0].projectName}_${project[0].id}_${new Date().getTime()}`;
          const downloadURL = window.URL.createObjectURL(res);
          const link = document.createElement('a');
          link.href = downloadURL;
          link.download = `${fileName}.xlsx`;
          link.click();
        },
        error: (downloadCompanyError): void => {
          if (downloadCompanyError.message?.statusCode === 400) {
            this.showError(downloadCompanyError);
          } else if (!downloadCompanyError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(downloadCompanyError.message, 'OOPS!');
          }
        },
      });
    });
  }

  public checkCompanyNameDuplication(template, action): any {
    this.submitted = true;
    if (action === 'addCompany') {
      if (this.companyDetailsForm.invalid) {
        this.formSubmitted = false;
        return;
      }
    }
    if (action === 'editCompany') {
      if (this.companyEditForm.invalid) {
        this.editFormSubmitted = false;
        return;
      }
    }
    this.addCompanyPopupSelected = false;
    this.newCompanyTriedToAdd = '';
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    let payload;
    if (action === 'addCompany') {
      this.addCompanyPopupSelected = true;
      payload = this.companyDetailsForm.value;
      this.newCompanyTriedToAdd = this.companyDetailsForm.value.companyName;
    }
    if (action === 'editCompany') {
      this.addCompanyPopupSelected = false;
      payload = this.companyEditForm.value;
      payload.id = this.editedCompanyId;
      this.newCompanyTriedToAdd = this.companyEditForm.value.companyName;
    }
    this.projectService.checkExistCompany(params, payload).subscribe({
      next: (existCompany: any): void => {
        this.existCompanyName = '';
        if (existCompany && existCompany.status === 422) {
          this.checkCompanyExists(existCompany, template, action)
        } else {
          if (action === 'addCompany') {
            this.onSubmit();
          }
          if (action === 'editCompany') {
            this.onEditSubmit();
          }
        }
      },
      error: (checkCompanyNameDuplicationError): void => {
        this.formSubmitted = false;
        this.submitted = false;
        if (checkCompanyNameDuplicationError.message?.statusCode === 400) {
          this.showError(checkCompanyNameDuplicationError);
        } else if (!checkCompanyNameDuplicationError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(checkCompanyNameDuplicationError.message, 'OOPS!');
        }
      },
    });
  }

  public checkCompanyExists(existCompany, template, action) {
    const { existInProject = [], sameAsParentCompany } = existCompany.data;
    const newCompanyArray = [...existInProject];

    if (sameAsParentCompany) {
      newCompanyArray.push(sameAsParentCompany);
    }
    const uniqueCompanies = newCompanyArray.reduce((acc, company) => {
      if (!acc.find((item) => item.companyName === company.companyName)) {
        acc.push(company);
      }
      return acc;
    }, []);

    if (uniqueCompanies.length > 0) {
      this.existCompanyName = uniqueCompanies.map((c) => c.companyName).join(', ');
      this.openCompanyNameDuplicationConfirmationPopup(template);
    } else if (action === 'addCompany') {
      this.onSubmit();
    } else if (action === 'editCompany') {
      this.onEditSubmit();
    }
  }

  public addCompanyToProject(payload): void {
    this.projectService.addCompany(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.mixpanelService.addMixpanelEvents('Added Company');
          this.submitted = false;
          this.formSubmitted = false;
          this.companyLogo = {};
          this.file = null;
          this.companyDetailsForm.reset();
          this.getCompanies();
          this.resetAndClose();
        }
      },
      error: (createCompanyError): void => {
        this.formSubmitted = false;
        this.submitted = false;
        if (createCompanyError.message?.statusCode === 400) {
          this.showError(createCompanyError);
        } else if (!createCompanyError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(createCompanyError.message, 'OOPS!');
        }
      },
    });
  }

  public onEditSubmit(): void {
    this.editSubmitted = true;
    this.definableWorkId = [];
    this.editFormSubmitted = true;
    if (this.companyEditForm.invalid) {
      this.editFormSubmitted = false;
      return;
    }
    const formValue = this.companyEditForm.value;
    const trimValue = formValue.secondAddress;
    const condition = formValue.secondAddress ? trimValue.trim() : formValue.secondAddress;
    if (this.companyEditForm.valid) {
      formValue.selectedItems.forEach((element): void => {
        this.definableWorkId.push(element.id);
      });
    }
    if (!this.checkStringEmptyValues(formValue)) {
      const payload: any = {
        id: formValue.id,
        companyName: formValue.companyName.trim(),
        scope: formValue.scope ? formValue.scope.trim() : formValue.scope,
        website: formValue?.website,
        country: formValue.country,
        secondAddress: condition,
        definableWorkId: this.definableWorkId,
        city: formValue.city,
        state: formValue.state,
        address: formValue.address,
        zipCode: formValue.zipCode,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      const formData = new FormData();
      if (this.editFile != null) {
        const params = {
          ProjectId: this.ProjectId,
          ParentCompanyId: this.ParentCompanyId,
        };
        formData.append('logo', this.editFile, this.editFile.name);
        this.projectService.companyLogoUpload(params, formData).subscribe((response: any): void => {
          payload.logo = response.data[0].Location;
          this.editCompany(payload);
        });
      } else {
        this.editCompany(payload);
      }
    } else {
      this.toastr.error('Please Enter valid Company Name/Note.', 'OOPS!');
      this.editSubmitted = false;
      this.editFormSubmitted = false;
    }
  }

  public editCompany(payload): void {
    this.projectService.editCompany(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.mixpanelService.addMixpanelEvents('Edited Company');
          this.editSubmitted = false;
          this.editFormSubmitted = false;
          this.editCompanyLogo = {};
          this.editFile = null;
          this.companyEditForm.reset();
          this.getCompanies();
          this.modalRef.hide();
          this.editedCompanyId = '';
        }
      },
      error: (editCompanyError): void => {
        this.editSubmitted = false;
        this.editFormSubmitted = false;
        if (editCompanyError.message?.statusCode === 400) {
          this.showError(editCompanyError);
        } else if (!editCompanyError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(editCompanyError.message, 'OOPS!');
        }
      },
    });
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.editSubmitted = false;
    this.formSubmitted = false;
    this.editFormSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public openModal(template: TemplateRef<any>): void {
    this.modalLoader = true;
    this.getDefinable();
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-concrete-popup custom-modal',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public openModal1(template: TemplateRef<any>): void {
    this.modalLoader = true;
    this.getDefinable();
    this.modalRef = this.modalService.show(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-sm filter-popup custom-modal',
    });
  }

  public openCompanyNameDuplicationConfirmationPopup(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef2 = this.modalService.show(template, data);
  }

  public resetAndClose(): void {
    this.modalRef.hide();
    this.companyDetailsForm.reset();
    this.companyDetailsForm.get('country').setValue('');
    this.companyDetailsForm.get('state').setValue('');
    this.companyDetailsForm.get('city').setValue('');
    this.submitted = false;
    this.formSubmitted = false;
    this.editFormSubmitted = false;
    this.editSubmitted = false;
    this.companyLogo = {};
    this.companyEditForm.reset();
    this.editBeforeDFOW = [];
    this.companiesFiles = [];
  }

  public ImportMultipleCompaniesCloseConfirmation(template: TemplateRef<any>): void {
    if (this.companiesFiles.length > 0) {
      let data = {};
      data = {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      };
      this.modalRef1 = this.modalService.show(template, data);
    } else {
      this.resetAndClose();
    }
  }

  public resetForm(action): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      this.modalRef1.hide();
      this.companyDetailsForm.reset();
      this.companyEditForm.reset();
      this.companyDetailsForm.get('country').setValue('');
      this.companyDetailsForm.get('state').setValue('');
      this.companyDetailsForm.get('city').setValue('');
      this.submitted = false;
      this.formSubmitted = false;
      this.editFormSubmitted = false;
      this.editSubmitted = false;
      this.modalRef.hide();
      this.companyLogo = {};
      this.editBeforeDFOW = [];
      this.companiesFiles = [];
    }
  }

  public openDeleteModal(index, template: TemplateRef<any>): void {
    if (index !== -1) {
      this.deleteIndex[0] = this.companiesList[index].id;
      this.currentDeleteId = index;
      this.remove = false;
    } else if (index === -1) {
      this.remove = true;
    }
    this.openModal(template);
  }

  public removeFile(i): void {
    this.companiesFiles.splice(i);
  }

  public companyImportFiledropped(files: NgxFileDropEntry[]): void {
    if (files.length === 1) {
      this.companiesFiles = files;
      this.companiesFiles.forEach((companyElement, i): void => {
        const relativePath = companyElement.relativePath.split('.');
        const extension = relativePath[relativePath.length - 1];
        if (extension === 'xlsx' || extension === 'xls' || extension === 'csv') {
          if (companyElement.fileEntry.isFile) {
            const fileEntry = companyElement.fileEntry as FileSystemFileEntry;
            fileEntry.file((_file: File): void => {
              this.formData = new FormData();
              this.formData.append('company', _file, companyElement.relativePath);
            });
          }
        } else {
          this.companiesFiles.splice(i);
          this.toastr.error(
            'Please select a valid file. Supported file format (.xlsx,.xls,.csv)',
            'OOPS!',
          );
        }
      });
    } else {
      this.toastr.error('Please import single file', 'OOPS!');
    }
  }

  public importData(): void {
    this.importSubmitted = true;
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.importCompany(params, this.formData).subscribe({
      next: (res): void => {
        if (res.status === 201) {
          this.projectService.uploadBulkCompanyFile({ status: 'companiesUploading' });
          this.companiesFiles = [];
          this.importSubmitted = false;
          this.modalRef.hide();
        }
      },
      error: (importDataError): void => {
        this.importSubmitted = false;
        if (importDataError.message?.statusCode === 400) {
          this.showError(importDataError);
        } else if (!importDataError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(importDataError.message, 'OOPS!');
        }
      },
    });
  }

  public close(template: TemplateRef<any>, form): void {
    if (form === 'newCompany') {
      if (
        (this.companyDetailsForm.touched && this.companyDetailsForm.dirty)
        || (this.companyDetailsForm.get('selectedItems').dirty
          && this.companyDetailsForm.get('selectedItems').value
          && this.companyDetailsForm.get('selectedItems').value.length > 0)
      ) {
        this.openModalPopup(template);
      } else {
        this.resetAndClose();
      }
    }
    if (form === 'editCompany') {
      if (
        (this.companyEditForm.touched && this.companyEditForm.dirty)
        || (this.companyEditForm.get('selectedItems').dirty
          && this.companyEditForm.get('selectedItems').value
          && this.companyEditForm.get('selectedItems').value.length > 0
          && this.areDifferentByProperty(
            this.editBeforeDFOW,
            this.companyEditForm.get('selectedItems').value,
            'DFOW',
          ))
      ) {
        this.openModalPopup(template);
      } else {
        this.resetAndClose();
      }
    }
    if (form === 'importCompanies') {
      this.ImportMultipleCompaniesCloseConfirmation(template);
    }
  }

  public openModalPopup(template): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public checkSelectedRow(): boolean {
    if (this.selectAll) {
      return false;
    }
    const index = this.companiesList.findIndex((item): boolean => item.isChecked === true);
    if (index !== -1) {
      return false;
    }
    return true;
  }

  public setSelectedItem(index): void {
    this.companiesList[index].isChecked = !this.companiesList[index].isChecked;
  }

  public deleteCompany(): void {
    this.deleteSubmitted = true;
    this.projectService
      .deleteCompany({
        id: this.deleteIndex,
        ProjectId: this.ProjectId,
        isSelectAll: this.selectAll,
        ParentCompanyId: this.ParentCompanyId,
      })
      .subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Deleted Company');
            this.getCompanies();
            this.deleteSubmitted = false;
            this.deleteIndex = [];
            this.modalRef.hide();
          }
        },
        error: (deleteCompanyError): void => {
          this.deleteSubmitted = false;
          this.getCompanies();
          if (deleteCompanyError.message?.statusCode === 400) {
            this.showError(deleteCompanyError);
            this.modalRef.hide();
          } else if (!deleteCompanyError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deleteCompanyError.message, 'OOPS!');
            this.modalRef.hide();
          }
        },
      });
  }

  // eslint-disable-next-line max-lines-per-function
  public handleCompanyAddressChange(address, action): void {
    const components = address?.address_components || [];
    const addressMap: { [key: string]: string } = {};

    components.forEach((component) => {
      component.types.forEach((type) => {
        addressMap[type] = component.long_name;
      });
    });


    const streetNumber = addressMap.street_number;
    const { route } = addressMap;
    const subLocality1 = addressMap.sublocality_level_1;
    const subLocality2 = addressMap.sublocality_level_2;
    const city = addressMap.administrative_area_level_2;
    const { locality } = addressMap;
    const state = addressMap.administrative_area_level_1;
    const { country } = addressMap;
    const zipCode = addressMap.postal_code;
    const zipSuffix = addressMap.postal_code_suffix;

    const addressParts = [streetNumber, route, subLocality2, subLocality1].filter(Boolean);
    const fullAddress = addressParts.join(' ');

    const targetForm = action === 'add' ? this.companyDetailsForm : this.companyEditForm;

    targetForm.get('address')?.setValue(fullAddress || locality);
    targetForm.get('country')?.setValue(country);
    targetForm.get('state')?.setValue(state);
    targetForm.get('city')?.setValue(locality || city);
    targetForm.get('zipCode')?.setValue(zipCode || zipSuffix);
  }


  public selectAllCompaniesData(): void {
    this.selectAll = !this.selectAll;
    if (this.selectAll) {
      this.companiesList.map((obj, index): void => {
        this.companiesList[index].isChecked = true;
        return null;
      });
    } else {
      this.companiesList.map((obj, index): void => {
        this.companiesList[index].isChecked = false;
        return null;
      });
    }
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'clear':
          this.clear();
          break;
        case 'filter':
          this.openModal1(data);
          break;
        case 'edit':
          this.openEditModal(data, item);
          break;
        case 'delete':
          this.openDeleteModal(data, item);
          break;
        case 'remove':
          this.removeFile(data);
          break;
        default:
          break;
      }
    }
  }


  public removeItem(): void {
    this.deleteSubmitted = true;
    if (this.selectAll) {
      this.deleteCompany();
    } else {
      this.companiesList.forEach((element): void => {
        if (element.isChecked) {
          this.deleteIndex.push(element.id);
        }
      });
      this.deleteCompany();
    }
  }

  public setEditForm(input, template: TemplateRef<any>, isParentCompany): void {
    this.editBeforeDFOW = [];
    this.editedCompanyId = input.id;
    this.companyEditForm.get('id').setValue(input.id);
    this.companyEditForm.get('companyName').setValue(input.companyName);
    this.companyEditForm.get('scope').setValue(input.scope);
    this.companyEditForm.get('address').setValue(input.address);
    this.companyEditForm.get('secondAddress').setValue(input.secondAddress);
    this.companyEditForm.get('country').setValue(input.country);
    this.companyEditForm.get('state').setValue(input.state);
    this.companyEditForm.get('city').setValue(input.city);
    this.companyEditForm.get('zipCode').setValue(input.zipCode);
    this.companyEditForm.get('website').setValue(input.website);
    this.editLogo = input.logo;
    let currentDefine;
    if (isParentCompany) {
      currentDefine = input.define;
    } else {
      currentDefine = input.defineEditData;
    }
    const define = [];
    if (currentDefine !== undefined) {
      currentDefine.forEach((element): void => {
        const data = {
          id: element.DeliverDefineWork.id,
          DFOW: element.DeliverDefineWork.DFOW,
        };
        define.push(data);
      });
    }
    this.companyEditForm.get('selectedItems').patchValue(define);
    this.editBeforeDFOW = define;
    this.openModal(template);
  }

  public areDifferentByProperty(a, b, prop): boolean {
    const array1 = a.map((x): any => x[prop]);
    const array2 = b.map((x): any => x[prop]);
    this.arrayValue = array1.concat(array2);
    this.arrayValue = [...new Set([...array1, ...array2])];
    return this.arrayValue.length !== array1.length;
  }

  public setDefinable(template: TemplateRef<any>): void {
    this.companyEditForm.get('selectedItems').setValue('');
    this.openModal(template);
  }

  public openEditModal(index, template: TemplateRef<any>): void {
    if (index !== -1) {
      this.editIndex = index;
      this.setEditForm(this.companiesList[index], template, false);
    } else {
      this.setEditForm(this.parentCompany, template, true);
    }
  }
}
