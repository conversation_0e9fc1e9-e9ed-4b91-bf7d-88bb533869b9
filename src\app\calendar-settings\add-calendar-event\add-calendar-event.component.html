<div class="modal-header border-0">
  <h1 class="fs14 fw-bold cairo-regular color-text7 my-1 text-center w-100 addnew-event">
    Add New Event
  </h1>
  <button
    type="button"
    class="close ms-auto"
    aria-label="Close"
    (click)="close(cancelConfirmation)"
  >
    <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close" /></span>
  </button>
</div>
<div class="modal-body pt-0 pb-0 addcalendar-newevent">
  <div class="addcalendar-details" *ngIf="!loader">
    <form
      name="form"
      class="custom-material-form addnewevent-popupscrollfix"
      id="deliverydetails-form3"
      [formGroup]="calendarEvent"
      novalidate
    >
      <div class="row">
        <div class="col-md-12">
          <div class="form-group">
            <label class="fs14 color-orange m-0" for = "description">Description</label>
            <input id = "description"
              type="text"
              formControlName="description"
              class="form-control fs12 material-input p-0"
              placeholder="Description"
            />
            <div class="color-red" *ngIf="submitted && calendarEvent.get('description').errors">
              <small *ngIf="calendarEvent.get('description').errors.required"
                >*Description is Required.</small
              >
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="row">
            <div class="col-md-12 pe-md-0">
              <label class="fs14 color-orange mb6" for = "fmDate">From Date</label>
              <div class="input-group mb-3">
                <input id = "fmDate"
                  class="form-control fs12 fw500 material-input"
                  #dp="bsDatepicker"
                  bsDatepicker
                  formControlName="fromDate"
                  placeholder="Start Date *"
                  [bsConfig]="{
                    isAnimated: true,
                    showWeekNumbers: false,
                    customTodayClass: 'today'
                  }"
                  (ngModelChange)="showMonthlyRecurrence()"
                />
                  <span class="input-group-text pe-1">
                    <img
                      src="./assets/images/date.svg"
                      class="h-12px"
                      alt="Date"
                      (click)="dp.toggle()" (keydown)="dp.toggle()"
                      [attr.aria-expanded]="dp.isOpen"
                    />
                  </span>
              </div>
            </div>
            <div class="row">
              <div class="col-md-12 mb-3">
                <div
                  class="input-group mb-3 color-red"
                  *ngIf="submitted && calendarEvent.get('fromDate').errors"
                >
                  <small *ngIf="calendarEvent.get('fromDate').errors.required"
                    >*From Date is Required.</small
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="row">
            <div class="col-md-12 pe-md-0">
              <div class="input-group mb-3 delivery-time">
                <label class="fs14 color-orange" for = "stTime">Start Time</label>
                <timepicker  id = "stTime"
                  [formControlName]="'startTime'"
                  (keypress)="numberOnly($event)"
                  [disabled]="isAllDayChosen"
                >
                </timepicker>
                <div class="color-red" *ngIf="submitted && calendarEvent.get('startTime').errors">
                  <small *ngIf="calendarEvent.get('startTime').errors.required"
                    >*Start time is Required.</small
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="row">
            <div class="col-md-12 pe-md-0">
              <label class="fs14 color-orange mb6" for = "tDate">To Date</label>
              <div class="input-group mb-3">
                <input id = "tDate"
                  class="form-control fs12 fw500 material-input"
                  #dp="bsDatepicker"
                  bsDatepicker
                  formControlName="toDate"
                  placeholder="To Date *"
                  [bsConfig]="{
                    isAnimated: true,
                    showWeekNumbers: false,
                    customTodayClass: 'today'
                  }"
                  (ngModelChange)="showMonthlyRecurrence()"
                />
                  <span class="input-group-text pe-1">
                    <img
                      src="./assets/images/date.svg"
                      class="h-12px"
                      alt="Date"
                      (click)="dp.toggle()"  (keydown)="dp.toggle()"
                      [attr.aria-expanded]="dp.isOpen"
                    />
                  </span>
              </div>
            </div>
            <div class="row">
              <div class="col-md-12 mb-3">
                <div
                  class="input-group mb-3 color-red"
                  *ngIf="submitted && calendarEvent.get('toDate').errors"
                >
                  <small *ngIf="calendarEvent.get('toDate').errors.required"
                    >*To Date is Required.</small
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="row">
            <div class="col-md-12 pe-md-0">
              <div class="input-group mb-3 delivery-time">
                <label class="fs14 color-orange" for = "edTime">End Time</label>
                <timepicker id = "edTime"
                  [formControlName]="'endTime'"
                  (keypress)="numberOnly($event)"
                  [disabled]="isAllDayChosen"
                >
                </timepicker>
                <div class="color-red" *ngIf="submitted && calendarEvent.get('endTime').errors">
                  <small *ngIf="calendarEvent.get('endTime').errors.required"
                    >*End time is Required.</small
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="form-group mb-4 timezone-formgroup">
        <label class="fs14 color-orange" for = "tzone">Time Zone</label>
        <ng-multiselect-dropdown  id = "tzone"
          [placeholder]="'Choose TimeZone'"
          [settings]="dropdownSettings"
          [data]="timezoneList"
          (change)="timeZoneSelected($event.target.value)"
          formControlName="TimeZoneId"
          [(ngModel)]="defaultValue"
        >
        </ng-multiselect-dropdown>
        <div class="color-red" *ngIf="submitted && calendarEvent.get('TimeZoneId').errors">
          <small *ngIf="calendarEvent.get('TimeZoneId').errors.required"
            >*TimeZone is Required.</small
          >
        </div>
      </div>

      <div class="row mt-3">
        <div class="col-md-6">
          <div class="form-group my-0 company-select equipment-select">
            <label class="fs12 fw600 mb12 equipment-label" for="location">Location<span>*</span></label>
            <ng-multiselect-dropdown [placeholder]="'Choose Location'" [settings]="locationDropdownSettings"
              [data]="locationList" (onSelect)="locationSelected($event)" formControlName="LocationId">
            </ng-multiselect-dropdown>
          </div>
          <div class="color-red" *ngIf="submitted && calendarEvent.get('LocationId').errors">
            <small *ngIf="calendarEvent.get('LocationId').errors.required"
              >*Location is Required.</small
            >
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group my-0 company-select equipment-select">
            <label class="fs12 fw600 mb12 equipment-label" for="gate">Gate<span>*</span></label>
            <ng-multiselect-dropdown [placeholder]="'Choose Gate'" [settings]="gateDropdownSettings"
              [data]="gateList" formControlName="GateId">
            </ng-multiselect-dropdown>
          </div>
          <div class="color-red" *ngIf="submitted && calendarEvent.get('GateId').errors">
            <small *ngIf="calendarEvent.get('GateId').errors.required"
              >*Gate is Required.</small
            >
          </div>
        </div>

      </div>

      <div class="row">
        <div class="col-md-6">
          <div class="form-group my-0 company-select equipment-select">
            <label class="fs12 fw600 mb12 equipment-label"  for="equipment">Equipment<span>*</span></label>
            <ng-multiselect-dropdown
              [placeholder]="'Equipment'"
              [settings]="equipmentDropdownSettings"
              [data]="equipmentList"
              formControlName="EquipmentId" (ngModelChange)="checkEquipmentType($event)"
            >
            </ng-multiselect-dropdown>
          </div>
          <div class="color-red" *ngIf="submitted && calendarEvent.get('EquipmentId').errors">
            <small *ngIf="calendarEvent.get('EquipmentId').errors.required"
              >*Equipment is Required.</small
            >
          </div>
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-md-6 p-0 ms-3">
          <div class="form-group">
            <ul
              class="follo-switch list-group list-group-horizontal justify-content-start mnb28"
              id="switch-control4"
            >
              <li class="list-group-item border-0 px-0 py-0">
                <ui-switch
                  switchColor="#fff"
                  defaultBoColor="#CECECE"
                  defaultBgColor="#CECECE"
                  formControlName="isAllDay"
                  (change)="toggleAllDay($event)"
                >
                </ui-switch>
              </li>
              <li class="fs14 list-group-item border-0 px-3 py-0 color-orange">All Day</li>
            </ul>
          </div>
        </div>
      </div>
      <div class="row mt-3">
        <div class="col-md-12">
          <div class="form-group mb-0">
            <label class="fs12 color-grey8 mb-2" for = "switch-control4">Event Applicable to</label>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-4 p-0 ps-2">
          <div class="form-group mb-0">
            <ul
              class="follo-switch list-group list-group-horizontal justify-content-start"
              id="switch-control4"
            >
              <li class="list-group-item border-0 px-1 py-0">
                <ui-switch
                  switchColor="#fff"
                  defaultBoColor="#CECECE"
                  defaultBgColor="#CECECE"
                  formControlName="isApplicableToDelivery"
                >
                </ui-switch>
              </li>
              <li class="fs14 list-group-item border-0 px-3 py-0 color-orange">Delivery</li>
            </ul>
            <div
              class="color-red ps-3"
              *ngIf="
                submitted &&
                !calendarEvent.get('isApplicableToDelivery').value &&
                !calendarEvent.get('isApplicableToCrane').value &&
                !calendarEvent.get('isApplicableToConcrete').value &&
                !calendarEvent.get('isApplicableToInspection').value
              "
            >
              <small>*Please select applicable calendar(s)</small>
            </div>
          </div>
        </div>
        <div class="col-md-4 ps-2">
          <div class="form-group mb-0">
            <ul
              class="follo-switch list-group list-group-horizontal justify-content-start"
              id="switch-control4"
            >
              <li class="list-group-item border-0 px-0 py-0">
                <ui-switch
                  switchColor="#fff"
                  defaultBoColor="#CECECE"
                  defaultBgColor="#CECECE"
                  formControlName="isApplicableToCrane"
                >
                </ui-switch>
              </li>
              <li class="fs14 list-group-item border-0 px-3 py-0 color-orange">Crane</li>
            </ul>
          </div>
        </div>
        <div class="col-md-4 ps-2">
          <div class="form-group mb-0">
            <ul
              class="follo-switch list-group list-group-horizontal justify-content-start"
              id="switch-control4"
            >
              <li class="list-group-item border-0 px-0 py-0">
                <ui-switch
                  switchColor="#fff"
                  defaultBoColor="#CECECE"
                  defaultBgColor="#CECECE"
                  formControlName="isApplicableToConcrete"
                >
                </ui-switch>
              </li>
              <li class="fs14 list-group-item border-0 px-3 py-0 color-orange">Concrete</li>
            </ul>
          </div>
        </div>
        <div class="col-md-4 p-0 ps-2">
          <div class="form-group mb-0">
            <ul
              class="follo-switch list-group list-group-horizontal justify-content-start"
              id="switch-control5"
            >
              <li class="list-group-item border-0 px-1 py-0">
                <ui-switch
                  switchColor="#fff"
                  defaultBoColor="#CECECE"
                  defaultBgColor="#CECECE"
                  formControlName="isApplicableToInspection"
                >
                </ui-switch>
              </li>
              <li class="fs14 list-group-item border-0 px-3 py-0 color-orange">Inspection</li>
            </ul>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12 pt-3">
          <label class="fs12" for = "recrnce">Recurrence</label>
          <div class="form-group">
            <select id = "recrnce"
              class="form-control fs12 material-input px-2"
              formControlName="recurrence"
              (change)="onRecurrenceSelect($event.target.value)"
            >
              <option value="" disabled selected hidden>Select Recurrence</option>
              <option *ngFor="let type of recurrence" value="{{ type.value }}">
                {{ type.value }}
              </option>
            </select>
            <div class="color-red" *ngIf="submitted && calendarEvent.get('recurrence').errors">
              <small *ngIf="calendarEvent.get('recurrence').errors.required"
                >*Recurrence is required</small
              >
            </div>
          </div>
        </div>
      </div>
      <div
        class="row"
        *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
      >
        <div
          class="col-md-12 mt-md-0"
          *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
        >
          <label class="fs14 color-orange" for="repEvry">Repeat Every</label>
        </div>
        <div
          class="col-md-4 mt-md-0"
          *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
        >
          <div class="form-group">
            <input for="repEvry"
              type="text"
              formControlName="repeatEveryCount"
              class="form-control fs12 material-input p-0"
              (input)="changeRecurrenceCount($event.target.value)"
              min="1"
            />
          </div>
        </div>
        <div class="col-md-4 mt-md-0" *ngIf="isRepeatWithSingleRecurrence">
          <div class="form-group">
            <select
              class="form-control fs12 material-input px-2"
              formControlName="repeatEveryType"
              (change)="chooseRepeatEveryType($event.target.value)"
            >
              <option value="" disabled selected hidden>Select Recurrence</option>
              <option *ngFor="let type of repeatWithSingleRecurrence" value="{{ type.value }}">
                {{ type.value }}
              </option>
            </select>
          </div>
        </div>
        <div
          class="col-md-4 mt-md-0"
          *ngIf="isRepeatWithMultipleRecurrence || showRecurrenceTypeDropdown"
        >
          <div class="form-group">
            <select
              class="form-control fs12 material-input px-2"
              formControlName="repeatEveryType"
              (change)="chooseRepeatEveryType($event.target.value)"
            >
              <option value="" disabled selected hidden>Select Recurrence</option>
              <option *ngFor="let type of repeatWithMultipleRecurrence" value="{{ type.value }}">
                {{ type.value }}
              </option>
            </select>
          </div>
        </div>
      </div>
      <div class="row addcalendar-displaydays">
        <div
          class="col-md-12 pt-0"
          *ngIf="
            (selectedRecurrence === 'Weekly' ||
              isRepeatWithMultipleRecurrence ||
              isRepeatWithSingleRecurrence) &&
            selectedRecurrence !== 'Monthly' &&
            selectedRecurrence !== 'Yearly'
          "
        >
          <ul class="displaylists ps-0">
            <li *ngFor="let item of weekDays; let i = index" class="fs12 list-inline-item">
              <input
                type="checkbox"
                [disabled]="item.isDisabled"
                [value]="item.value"
                class="d-none"
                id="days-{{ i }}"
                (change)="onChange($event)"
                [checked]="item.checked"
              />
              <label for="days-{{ i }}">{{ item.display }}</label>
            </li>
            <div class="color-red" *ngIf="submitted && calendarEvent.controls['days'].errors">
              <small *ngIf="calendarEvent.controls['days'].errors.required">*Required </small>
            </div>
          </ul>
        </div>
      </div>
      <div class="row" *ngIf="selectedRecurrence === 'Monthly' || selectedRecurrence === 'Yearly'">
        <div class="col-md-8 mt-md-0 ps-3">
          <div class="form-check">
            <input
              class="form-check-input c-pointer"
              type="radio"
              formControlName="chosenDateOfMonth"
              id="flexRadioDefault1"
              [value]="1"
              (change)="changeMonthlyRecurrence()"
            />
            <label class="form-check-label fs14 color-orange" for="flexRadioDefault1">
              On day {{ monthlyDate }}
            </label>
          </div>
          <div class="form-check">
            <input
              class="form-check-input c-pointer"
              type="radio"
              formControlName="chosenDateOfMonth"
              id="flexRadioDefault2"
              [value]="2"
              (change)="changeMonthlyRecurrence()"
            />
            <label class="form-check-label fs14 color-orange" for="flexRadioDefault2">
              On the {{ monthlyDayOfWeek }}
              <p *ngIf="selectedRecurrence === 'Yearly'">
                of {{ calendarEvent.get('fromDate').value | date : 'LLLL' }}
              </p>
            </label>
          </div>
          <div class="form-check" *ngIf="enableOption">
            <input
              class="form-check-input c-pointer"
              type="radio"
              formControlName="chosenDateOfMonth"
              id="flexRadioDefault3"
              [value]="3"
              (change)="changeMonthlyRecurrence()"
            />
            <label class="form-check-label fs14 color-orange" for="flexRadioDefault3">
              On the {{ monthlyLastDayOfWeek }}
              <p *ngIf="selectedRecurrence === 'Yearly'">
                of {{ calendarEvent.get('fromDate').value | date : 'LLLL' }}
              </p>
            </label>
          </div>
          <div class="form-check">
            <div
              class="color-red"
              *ngIf="
                submitted &&
                (calendarEvent.get('monthlyRepeatType')?.errors ||
                  calendarEvent.get('dateOfMonth')?.errors)
              "
            >
              <small *ngIf="calendarEvent.get('monthlyRepeatType')?.errors?.required"
                >*required</small
              >
              <small *ngIf="calendarEvent.get('dateOfMonth')?.errors?.required">*required</small>
            </div>
          </div>
        </div>
      </div>
      <div
        class="row"
        *ngIf="
          selectedRecurrence === 'Monthly' ||
          selectedRecurrence === 'Yearly' ||
          selectedRecurrence === 'Weekly'
        "
      >
        <div class="col-md-12 pe-md-0">
          <label class="fs14 color-orange" for="recEndDate">Recurrence End Date</label>
          <div class="input-group mb-3">
            <input id = "recEndDate"
              class="form-control fs12 fw500 material-input"
              #dp="bsDatepicker"
              bsDatepicker
              formControlName="endDate"
              placement="top"
              placeholder="End Date *"
              [bsConfig]="{ isAnimated: true, showWeekNumbers: false, customTodayClass: 'today' }"
              (ngModelChange)="showMonthlyRecurrence()"
            />
              <span class="input-group-text">
                <img
                  src="./assets/images/date.svg"
                  class="h-12px p-0 me-3"
                  alt="Date"
                  (click)="dp.toggle()"  (keydown)="dp.toggle()"
                  [attr.aria-expanded]="dp.isOpen"
                />
              </span>
          </div>
        </div>
      </div>
      <div class="row addcalendar-displaydays">
        <div class="col-md-12 mt-md-0 pb-0" *ngIf="message">
          <p class="fs12 color-grey11">
            <span class="color-red fw-bold">*</span>
            {{ message }}
          </p>
        </div>
      </div>
    </form>
  </div>
  <div class="addcalendar-details fw-bold text-center" *ngIf="loader">Loading...</div>
</div>
<div class="modal-footer border-0 justify-content-center add-calendar-footer p-0" *ngIf="!loader">
  <div class="mt-0 mb15 text-center">
    <button
      class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3 px-2rem"
      type="button"
      (click)="close(cancelConfirmation)"
    >
      Cancel
    </button>
    <button
      class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem"
      (click)="onSubmit()"
      [disabled]="formSubmitted && calendarEvent.valid"
    >
      <em
        class="fa fa-spinner"
        aria-hidden="true"
        *ngIf="formSubmitted && calendarEvent.valid"
      ></em>
      Submit
    </button>
  </div>
</div>

<!--Confirmation Popup-->
<div id="confirm-popup7">
  <ng-template #cancelConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure you want to cancel?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetForm('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="resetForm('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
