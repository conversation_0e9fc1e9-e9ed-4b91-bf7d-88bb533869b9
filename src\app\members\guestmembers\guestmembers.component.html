<section class="page-section">
  <div class="page-inner-content members-content">
    <div class="top-header mb-3 pt-0">
      <div class="row">
        <div class="col-md-12">
          <div class="top-filter">
            <ul class="list-group list-group-horizontal justify-content-end">
              <li class="list-group-item p0 border-0 bg-transparent me-2">
                <div class="search-icon">
                  <input
                    class="form-control fs12 color-grey8"
                    [ngClass]="showSearchbar ? 'input-hover-disable' : 'input-search'"
                    placeholder="What are you looking for?"
                    (input)="getSearchMember($event.target.value)"
                    [(ngModel)]="search"
                  />
                  <div class="icon">
                    <img
                      src="./assets/images/cross-close.svg"
                      *ngIf="showSearchbar"
                      (click)="clear()"
                      (keydown) = "clear()"
                      alt="close-cross"
                    />
                    <em class="fa fa-search fs12 color-grey8" *ngIf="!showSearchbar"></em>
                  </div>
                </div>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent me-2 position-relative">
                <div class="filter-icon" (click)="openModal1(filter)" (keydown)="handleDownKeydown($event, filter,'','open')">
                  <img src="./assets/images/filter.svg" class="h-12px icon" alt="Filter" />
                </div>
                <div
                  class="bg-orange rounded-circle position-absolute text-white filter-count"
                  *ngIf="filterCount > 0"
                >
                  <p class="m-0 text-center fs12">1</p>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="page-card bg-white rounded">
      <div class="table-responsive rounded tab-grid">
        <table
          class="table table-custom mb-0 members-table guest-members-table"
          aria-describedby="Memtable"
        >
          <thead>

            <th scope="col" resizable>
              Name
              <span>
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('firstName', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'firstName', 'ASC')"
                  *ngIf="sortColumn !== 'firstName'"
                />
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('firstName', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'firstName', 'ASC')"
                  *ngIf="sort === 'DESC' && sortColumn === 'firstName'"
                />
                <img
                  src="./assets/images/up-chevron.svg"
                  alt="up-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('firstName', 'DESC')"
                  (keydown)="handleToggleKeydown($event, 'firstName', 'DESC')"
                  *ngIf="sort === 'ASC' && sortColumn === 'firstName'"
                />
              </span>
            </th>
            <th scope="col" resizable>
              Company
              <span>
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('Company', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'Company', 'ASC')"
                  *ngIf="sortColumn !== 'Company'"
                />
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('Company', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'Company', 'ASC')"
                  *ngIf="sort === 'DESC' && sortColumn === 'Company'"
                />
                <img
                  src="./assets/images/up-chevron.svg"
                  alt="up-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('Company', 'DESC')"
                  (keydown)="handleToggleKeydown($event, 'Company', 'DESC')"
                  *ngIf="sort === 'ASC' && sortColumn === 'Company'"
                />
              </span>
            </th>
            <th scope="col" resizable>
              Email
              <span>
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('email', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'email', 'ASC')"
                  *ngIf="sortColumn !== 'email'"
                />
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('email', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'email', 'ASC')"
                  *ngIf="sort === 'DESC' && sortColumn === 'email'"
                />
                <img
                  src="./assets/images/up-chevron.svg"
                  alt="up-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('email', 'DESC')"
                  (keydown)="handleToggleKeydown($event, 'email', 'DESC')"
                  *ngIf="sort === 'ASC' && sortColumn === 'email'"
                />
              </span>
            </th>
            <th scope="col" resizable>
              Phone No
              <span>
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('phoneNumber', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'phoneNumber', 'ASC')"
                  *ngIf="sortColumn !== 'phoneNumber'"
                />
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('phoneNumber', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'phoneNumber', 'ASC')"
                  *ngIf="sort === 'DESC' && sortColumn === 'phoneNumber'"
                />
                <img
                  src="./assets/images/up-chevron.svg"
                  alt="up-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('phoneNumber', 'DESC')"
                  (keydown)="handleToggleKeydown($event, 'phoneNumber', 'DESC')"
                  *ngIf="sort === 'ASC' && sortColumn === 'phoneNumber'"
                />
              </span>
            </th>
            <th scope="col" resizable>
              Role
              <span> </span>
            </th>
            <th scope="col" resizable>Status</th>
          </thead>
          <tbody *ngIf="loader == false && memberList.length > 0">
            <tr
              *ngFor="
                let item of memberList
                  | paginate
                    : {
                        itemsPerPage: pageSize,
                        currentPage: currentPageNo,
                        totalItems: totalCount
                      };
                let i = index
              "
            >
              <td>
                <span> {{ item?.User?.firstName }} {{ item?.User?.lastName }} </span>
                <span *ngIf="!item.User?.firstName">-</span>
              </td>
              <td>
                <div class="d-flex">
                  <div class="my-auto">
                    {{ item.Company?.companyName }}
                  </div>
                </div>
                <span *ngIf="!item.Company">-</span>
              </td>
              <td>
                <span *ngIf="item.User?.email">{{ item.User?.email }}</span>
                <span *ngIf="!item.User?.email">-</span>
              </td>
              <td>
                <span> {{ item.phoneCode }} {{ item.phoneNumber }} </span>
                <span *ngIf="!item.phoneCode && !item.phoneNumber">-</span>
              </td>
              <td>
                <select
                class="w150 form-select fs12 color-grey4 h35"
                *ngIf="item.status !== 'declined'"
                  #roleDropdown
                  (change)="onRoleChange(item, roleDropdown.value)"
              >
              <option value="" disabled selected hidden>Select Role</option>
                  <option *ngFor="let role of roleList" [value]="role.id">
                    {{ role.roleName }}
                  </option>
              </select>
                <span *ngIf="item.status === 'declined'">-</span>
              </td>
              <td class="text-left">

                <span class="color-red1 px-2 radius100 pale-red" *ngIf="item.status === 'declined'"
                  >Declined</span
                >
                <div *ngIf="!addGuestLoader">
                <img
                class="c-pointer"
                  src="../../../assets/images/Accept.svg"
                  alt="approved"
                  *ngIf="item.status !== 'declined'"
                  (click)="addGuestAsMember(item)"
                  (keydown)="handleDownKeydown($event, item,'','approved')"
                />
                <img
                  class="ms-2 c-pointer"
                  src="../../../assets/images/Reject.svg"
                  alt="declined"
                  *ngIf="item.status !== 'declined'"
                  (click)="confirmationDecline(cancelConfirmationPopup, item)"
                  (keydown)="handleDownKeydown($event,cancelConfirmationPopup, item,'declined')"
                />
              </div>
              <span>
                <em class="fas fa-sync fa-spin" *ngIf="addGuestLoader" width="10px" alt="updating"></em>
              </span>
              </td>
            </tr>
          </tbody>
          <tr *ngIf="loader == true">
            <td colspan="8" class="text-center">
              <div class="fs18 fw-bold cairo-regular my-5 text-black">Loading...</div>
            </td>
          </tr>
          <tr *ngIf="loader == false && memberList.length == 0">
            <td colspan="8" class="text-center">
              <div class="fs18 fw-bold cairo-regular my-5 text-black">
                No Records Found
              </div>
            </td>
          </tr>
        </table>
      </div>
      <div
        class="tab-pagination px-2"
        id="tab-pagination7"
        *ngIf="loader == false && totalCount > 25"
      >
        <div class="row">
          <div class="col-md-2 align-items-center">
            <ul class="list-inline my-3">
              <li class="list-inline-item notify-pagination">
                <label class="fs12 color-grey4" for="showEnt">Show entries</label>
              </li>
              <li class="list-inline-item">
                <select  id="showEnt"
                class="w-auto form-select fs12 color-grey4"
                  (change)="changePageSize($event.target.value)"
                  [ngModel]="pageSize"
                >
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                  <option value="150">150</option>
                </select>
              </li>
            </ul>
          </div>
          <div class="col-md-8 text-center">
            <div class="my-3 position-relative d-inline-block">
              <pagination-controls
                (pageChange)="changePageNo($event)"
                previousLabel=""
                nextLabel=""
              >
              </pagination-controls>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<div id="fiter-temp5">
  <!--Filter Modal-->
  <ng-template #filter>
    <div class="modal-header border-0 pb-0">
      <h4 class="fs14 fw-bold cairo-regular color-text7 my-0">Filter</h4>
      <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true"
          ><img src="./assets/images/modal-close.svg" alt="Modal Close"
        /></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="filter-content">
        <form class="custom-material-form" [formGroup]="filterForm" (ngSubmit)="filterSubmit()">
          <div class="row">
            <div class="col-md-12">
              <div class="input-group mb-3">
                <input
                  type="text"
                  class="form-control fs12 material-input"
                  placeholder="Name"
                  formControlName="nameFilter"
                />
                  <span class="input-group-text">
                    <img src="./assets/images/search-icon.svg" alt="Search" />
                  </span>
              </div>
              <div class="form-group">
                <ngx-select-dropdown
                  class="fs12"
                  [formControl]="filterForm.controls['companyFilter']"
                  [multiple]="false"
                  [config]="config"
                  [options]="companyListForFilter"
                ></ngx-select-dropdown>
              </div>

              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="statusFilter">
                  <option value="" disabled selected hidden>Status</option>
                  <option *ngFor="let item of wholeStatus" value="{{ item }}">{{ item }}</option>
                </select>
              </div>
              <div class="row justify-content-end">
                <button
                  class="btn btn-orange radius20 col-4 mt-2 fs12 fw-bold cairo-regular mx-1"
                  type="submit"
                >
                  Apply
                </button>
                <button
                  class="btn btn-orange radius20 fs12 col-4 mt-2 fw-bold cairo-regular mx-1"
                  type="button"
                  (click)="resetFilter()"
                >
                  Reset
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
  <!--Filter Modal-->
</div>

<!--Confirmation Popup-->
<div id="confirm-popup5">
  <ng-template #cancelConfirmationPopup>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure you want to Decline Request?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetForm('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="resetForm('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>

<!-- Invite Member -->
