import { Component, OnInit, TemplateRef } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';

@Component({
  selector: 'app-mycards',
  templateUrl: './mycards.component.html',
  })
export class MycardsComponent implements OnInit {
  public modalRef: BsModalRef;

  public constructor(private readonly modalService: BsModalService) {
    // constructor
  }

  public ngOnInit(): void { /* */ }

  public openCardModal(template: TemplateRef<any>): void {
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-lg custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.openCardModal(data);
    }
  }
}
