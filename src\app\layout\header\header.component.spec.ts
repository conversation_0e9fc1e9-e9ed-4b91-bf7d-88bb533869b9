import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HeaderComponent } from './header.component';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth/auth.service';
import { RouterTestingModule } from '@angular/router/testing';

describe('HeaderComponent', () => {
  let component: HeaderComponent;
  let fixture: ComponentFixture<HeaderComponent>;
  let router: Router;
  let authService: AuthService;

  const mockAuthService = {
    contentLogout: jest.fn(),
    loggeduserIn: jest.fn()
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [HeaderComponent],
      imports: [RouterTestingModule],
      providers: [
        { provide: AuthService, useValue: mockAuthService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(HeaderComponent);
    component = fixture.componentInstance;
    router = TestBed.inject(Router);
    authService = TestBed.inject(AuthService);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('constructor', () => {
    it('should set loginToken to false when memberId and ParentCompanyId are present', () => {
      jest.spyOn(router, 'url', 'get').mockReturnValue('/some/path/123/456');
      component = new HeaderComponent(router, authService);
      expect(component.loginToken).toBeFalsy();
      expect(authService.contentLogout).toHaveBeenCalled();
    });

    it('should set loginToken to true when user is logged in', () => {
      jest.spyOn(router, 'url', 'get').mockReturnValue('/some/path');
      mockAuthService.loggeduserIn.mockReturnValue(true);
      component = new HeaderComponent(router, authService);
      expect(component.loginToken).toBeTruthy();
    });
  });

  describe('toggleNavbar', () => {
    it('should toggle navbarOpen state', () => {
      expect(component.navbarOpen).toBeFalsy();
      component.toggleNavbar();
      expect(component.navbarOpen).toBeTruthy();
      component.toggleNavbar();
      expect(component.navbarOpen).toBeFalsy();
    });
  });

  describe('sidebarToggle', () => {
    it('should toggle shouldShow state', () => {
      expect(component.shouldShow).toBeUndefined();
      component.sidebarToggle();
      expect(component.shouldShow).toBeTruthy();
      component.sidebarToggle();
      expect(component.shouldShow).toBeFalsy();
    });
  });

  describe('redirect', () => {
    it('should navigate to the specified path', () => {
      const navigateSpy = jest.spyOn(router, 'navigate');
      const path = 'test-path';
      
      component.redirect(path);
      
      expect(navigateSpy).toHaveBeenCalledWith([`/${path}`]);
    });
  });

  describe('mobilenNavOpen', () => {
    it('should toggle mobilenavshow state', () => {
      expect(component.mobilenavshow).toBeUndefined();
      component.mobilenNavOpen();
      expect(component.mobilenavshow).toBeTruthy();
      component.mobilenNavOpen();
      expect(component.mobilenavshow).toBeFalsy();
    });
  });
});
