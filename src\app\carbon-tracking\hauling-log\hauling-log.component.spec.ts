import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { NgxPaginationModule } from 'ngx-pagination';
import { ModalModule, BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { HaulingLogComponent } from './hauling-log.component';
import { ProjectService } from '../../services/profile/project.service';
import { AuthService } from '../../services/auth/auth.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { MixpanelService } from '../../services/mixpanel.service';
import { Title } from '@angular/platform-browser';
import { of, throwError } from 'rxjs';

const mockHaulingLogData = {
  data: {
    rows: [
      {
        id: 1,
        companyName: 'Test Company',
        importOrExportName: 'Test Import',
        materialType: 'Concrete',
        quantity: '100',
        noOfTrips: '5',
        haulingOriginLocation: 'Origin',
        haulingDestinationLocation: 'Destination',
        equipmentName: 'Test Equipment',
        isChecked: false,
      },
    ],
    count: 1,
  },
};

describe('HaulingLogComponent', () => {
  let component: HaulingLogComponent;
  let fixture: ComponentFixture<HaulingLogComponent>;
  let projectService: jest.Mocked<ProjectService>;
  let authService: jest.Mocked<AuthService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let modalService: jest.Mocked<BsModalService>;
  let toastrService: jest.Mocked<ToastrService>;
  let mockModalRef: BsModalRef;

  const mockProjectService = {
    projectId: of('123'),
    ParentCompanyId: of('456'),
    getEquipments: jest.fn(),
    deleteEquipment: jest.fn().mockReturnValue(of({ message: 'Equipment deleted successfully' })),
    activateEquipment: jest.fn(),
    deactiveEquipment: jest.fn().mockReturnValue(of({ message: 'Equipment deactivated successfully' })),
    listHaulingLog: jest.fn().mockReturnValue(of(mockHaulingLogData)),
    addHaulingLog: jest.fn().mockReturnValue(of({ message: 'Hauling log added successfully' })),
    editEquipment: jest.fn().mockReturnValue(of({ message: 'Equipment updated successfully' })),
    getEquipmentMappedRequests: jest.fn().mockReturnValue(of({
      data: {
        mappedRequest: [],
        equipments: {
          craneEquipments: [],
          nonCraneEquipments: []
        }
      }
    })),
  };

  const mockAuthService = {
    getAuthUser: jest.fn(),
  };

  const mockDeliveryService = {
    loginUser: of({ id: 1, name: 'Test User' }),
  };

  const mockMixpanelService = {
    track: jest.fn(),
    addMixpanelEvents: jest.fn(),
  };

  beforeEach(async () => {
    mockModalRef = {
      hide: jest.fn(),
      setClass: jest.fn(),
    } as any;

    await TestBed.configureTestingModule({
      declarations: [HaulingLogComponent],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        RouterTestingModule,
        HttpClientTestingModule,
        NgxPaginationModule,
        ModalModule.forRoot(),
        ToastrModule.forRoot(),
      ],
      providers: [
        { provide: ProjectService, useValue: mockProjectService },
        { provide: AuthService, useValue: mockAuthService },
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: MixpanelService, useValue: mockMixpanelService },
        { provide: Title, useValue: { setTitle: jest.fn() } },
        { provide: BsModalService, useValue: { show: jest.fn().mockReturnValue(mockModalRef) } },
        { provide: ToastrService, useValue: {
          success: jest.fn(),
          error: jest.fn(),
          info: jest.fn(),
          warning: jest.fn()
        } },
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();

    fixture = TestBed.createComponent(HaulingLogComponent);
    component = fixture.componentInstance;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    modalService = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;

    component.equipmentlogList = [mockHaulingLogData.data.rows[0]];
    component.modalRef = mockModalRef;
    fixture.detectChanges();
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.currentPageNo).toBe(1);
      expect(component.pageSize).toBe(25);
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(component.deleteSubmitted).toBe(false);
      expect(component.selectAll).toBe(false);
      expect(component.filterCount).toBe(0);
      expect(component.search).toBe('');
      expect(component.sort).toBe('DESC');
      expect(component.sortColumn).toBe('id');
      expect(component.equipmentlogList).toEqual([mockHaulingLogData.data.rows[0]]);
    });

    it('should initialize forms', () => {
      expect(component.haulingLogDetailsForm).toBeDefined();
      expect(component.equipmentEditForm).toBeDefined();
      expect(component.filterForm).toBeDefined();
    });

    it('should set title on initialization', () => {
      const titleService = TestBed.inject(Title);
      expect(titleService.setTitle).toHaveBeenCalledWith('Follo - Equipment');
    });

    it('should subscribe to loginUser', () => {
      expect(component.authUser).toBeDefined();
    });

    it('should subscribe to projectId and call getEquipments', () => {
      expect(component.ProjectId).toBe('123');
      expect(projectService.listHaulingLog).toHaveBeenCalled();
    });

    it('should subscribe to ParentCompanyId and call getEquipments', () => {
      expect(component.ParentCompanyId).toBe('456');
      expect(projectService.listHaulingLog).toHaveBeenCalled();
    });
  });

  describe('Data Loading', () => {
    it('should fetch hauling logs successfully', () => {
      component.getEquipments();

      expect(projectService.listHaulingLog).toHaveBeenCalled();
      expect(component.equipmentlogList).toEqual(mockHaulingLogData.data.rows);
      expect(component.totalCount).toBe(mockHaulingLogData.data.count);
      expect(component.loader).toBe(false);
    });

    it('should handle error when fetching hauling logs', () => {
      projectService.listHaulingLog.mockReturnValueOnce(throwError(() => new Error('API Error')));

      component.getEquipments();

      expect(projectService.listHaulingLog).toHaveBeenCalled();
      expect(component.loader).toBe(true);
    });

    it('should not fetch hauling logs when ProjectId or ParentCompanyId is missing', () => {
      component.ProjectId = null;
      component.ParentCompanyId = null;
      projectService.listHaulingLog.mockClear();

      component.getEquipments();

      expect(projectService.listHaulingLog).not.toHaveBeenCalled();
    });
  });

  describe('Keyboard Event Handlers', () => {
    it('should handle toggle keydown with Enter key', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'sortByField').mockImplementation();

      component.handleToggleKeydown(event, 'testField', 'ASC');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.sortByField).toHaveBeenCalledWith('testField', 'ASC');
    });

    it('should handle toggle keydown with Space key', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'sortByField').mockImplementation();

      component.handleToggleKeydown(event, 'testField', 'DESC');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.sortByField).toHaveBeenCalledWith('testField', 'DESC');
    });

    it('should handle down keydown with clear action', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'clear').mockImplementation();

      component.handleDownKeydown(event, null, null, 'clear');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.clear).toHaveBeenCalled();
    });

    it('should handle down keydown with filter action', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      const template = {} as any;
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'openFilterModal').mockImplementation();

      component.handleDownKeydown(event, template, null, 'filter');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.openFilterModal).toHaveBeenCalledWith(template);
    });

    it('should handle down keydown with delete action', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      const template = {} as any;
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'openDeleteModal').mockImplementation();

      component.handleDownKeydown(event, 0, template, 'delete');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.openDeleteModal).toHaveBeenCalledWith(0, template);
    });

    it('should not handle keydown for other keys', () => {
      const event = new KeyboardEvent('keydown', { key: 'a' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'sortByField').mockImplementation();

      component.handleToggleKeydown(event, 'testField', 'ASC');

      expect(event.preventDefault).not.toHaveBeenCalled();
      expect(component.sortByField).not.toHaveBeenCalled();
    });
  });

  describe('Search and Filter Operations', () => {
    it('should handle search equipment with data', () => {
      const searchTerm = 'test';
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.getSearchEquipment(searchTerm);

      expect(component.showSearchbar).toBe(true);
      expect(component.pageNo).toBe(1);
      expect(component.search).toBe(searchTerm);
      expect(component.getEquipments).toHaveBeenCalled();
    });

    it('should handle search equipment with empty data', () => {
      component.showSearchbar = true;
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.getSearchEquipment('');

      expect(component.showSearchbar).toBe(false);
      expect(component.pageNo).toBe(1);
      expect(component.search).toBe('');
      expect(component.getEquipments).toHaveBeenCalled();
    });

    it('should sort by field', () => {
      const fieldName = 'name';
      const sortType = 'ASC';
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.sortByField(fieldName, sortType);

      expect(component.sortColumn).toBe(fieldName);
      expect(component.sort).toBe(sortType);
      expect(component.getEquipments).toHaveBeenCalled();
    });

    it('should clear search and filters', () => {
      component.showSearchbar = true;
      component.search = 'test search';
      component.pageNo = 3;
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.clear();

      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
      expect(component.pageNo).toBe(1);
      expect(component.getEquipments).toHaveBeenCalled();
    });

    it('should reset filter form', () => {
      component.modalRef = mockModalRef;
      component.filterCount = 5;
      component.search = 'test';
      component.pageNo = 2;
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.resetFilter();

      expect(component.filterCount).toBe(0);
      expect(component.search).toBe('');
      expect(component.pageNo).toBe(1);
      expect(component.getEquipments).toHaveBeenCalled();
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should submit filter with all fields filled', () => {
      component.filterForm.patchValue({
        idFilter: '1',
        nameFilter: 'Test',
        companyNameFilter: 'Company',
        typeFilter: 'Type',
        memberFilter: '2'
      });
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.filterSubmit();

      expect(component.filterCount).toBe(5);
      expect(component.pageNo).toBe(1);
      expect(component.getEquipments).toHaveBeenCalled();
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should submit filter with some fields empty', () => {
      component.filterForm.patchValue({
        idFilter: '',
        nameFilter: 'Test',
        companyNameFilter: '',
        typeFilter: 'Type',
        memberFilter: ''
      });
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.filterSubmit();

      expect(component.filterCount).toBe(2);
      expect(component.getEquipments).toHaveBeenCalled();
    });
  });

  describe('Pagination', () => {
    it('should change page size', () => {
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.changePageSize(50);

      expect(component.pageSize).toBe(50);
      expect(component.getEquipments).toHaveBeenCalled();
    });

    it('should change page number', () => {
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.changePageNo(3);

      expect(component.currentPageNo).toBe(3);
      expect(component.getEquipments).toHaveBeenCalled();
    });
  });

  describe('Form Operations', () => {
    beforeEach(() => {
      component.haulingLogDetailsForm.patchValue({
        companyName: 'Test Company',
        importOrExportName: 'Test Import',
        materialType: 'Concrete',
        quantity: '100',
        noOfTrips: '5',
        haulingOriginLocation: 'Origin',
        haulingDestinationLocation: 'Destination',
        unitValue: 'Tons',
      });
      // Reset mocks
      projectService.addHaulingLog.mockClear();
      toastrService.success.mockClear();
      toastrService.error.mockClear();
    });

    it('should submit form successfully', () => {
      jest.spyOn(component, 'getEquipments').mockImplementation();
      jest.spyOn(component, 'resetForm').mockImplementation();

      // Make sure the form is valid
      component.haulingLogDetailsForm.patchValue({
        companyName: 'Test Company',
        importOrExportName: 'Test Import',
        materialType: 'Test Material',
        quantity: 100,
        noOfTrips: 5,
        haulingOriginLocation: 'Origin',
        haulingDestinationLocation: 'Destination',
        unitValue: 10,
        unitType: 'kg'
      });

      component.onSubmit();

      // After the observable completes synchronously, they should be false
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(projectService.addHaulingLog).toHaveBeenCalled();
      expect(toastrService.success).toHaveBeenCalledWith('Hauling log added successfully', 'Success');
    });

    it('should not submit invalid form', () => {
      // Make form invalid by setting required fields to empty and marking as invalid
      component.haulingLogDetailsForm.patchValue({
        companyName: '',
        importOrExportName: '',
        materialType: ''
      });
      component.haulingLogDetailsForm.get('companyName').setErrors({ required: true });
      component.haulingLogDetailsForm.get('importOrExportName').setErrors({ required: true });
      component.haulingLogDetailsForm.get('materialType').setErrors({ required: true });

      component.onSubmit();

      expect(component.submitted).toBe(true);
      expect(component.formSubmitted).toBe(false);
      expect(projectService.addHaulingLog).not.toHaveBeenCalled();
    });

    it('should handle form submission success', () => {
      jest.spyOn(component, 'getEquipments').mockImplementation();
      jest.spyOn(component, 'resetForm').mockImplementation();

      component.onSubmit();

      expect(toastrService.success).toHaveBeenCalledWith('Hauling log added successfully', 'Success');
      expect(mockMixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Added Equipment');
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should handle form submission error', () => {
      const mockError = { message: 'Submission failed' };
      projectService.addHaulingLog.mockReturnValue(throwError(() => mockError));

      component.onSubmit();

      expect(toastrService.error).toHaveBeenCalledWith('Submission failed', 'OOPS!');
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
    });

    it('should handle form submission error with status code 400', () => {
      const mockError = {
        message: {
          statusCode: 400,
          details: [{ field: 'Validation error' }]
        }
      };
      projectService.addHaulingLog.mockReturnValue(throwError(() => mockError));
      jest.spyOn(component, 'showError').mockImplementation();

      component.onSubmit();

      expect(component.showError).toHaveBeenCalledWith(mockError);
      expect(component.formSubmitted).toBe(false);
    });

    it('should handle invalid equipment name/type', () => {
      jest.spyOn(component, 'checkStringEmptyValues').mockReturnValue(true);

      component.onSubmit();

      expect(toastrService.error).toHaveBeenCalledWith('Please Enter valid Equipment Name/Type.', 'OOPS!');
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
    });
  });

  describe('Edit Form Operations', () => {
    beforeEach(() => {
      component.equipmentEditForm.patchValue({
        id: 1,
        equipmentName: 'Test Equipment',
        EquipmentType: 2,
        controlledBy: 'Test User'
      });
      // Reset mocks
      projectService.editEquipment.mockClear();
      toastrService.success.mockClear();
      toastrService.error.mockClear();
    });

    it('should submit edit form successfully', () => {
      jest.spyOn(component, 'getEquipments').mockImplementation();

      // Make sure the form is valid
      component.equipmentEditForm.patchValue({
        equipmentName: 'Test Equipment',
        EquipmentType: 1,
        controlledBy: 'Test User',
        id: 1,
        equipmentAutoId: 1
      });

      component.onEditSubmit();

      // After the observable completes synchronously, they should be false
      expect(component.editSubmitted).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
      expect(projectService.editEquipment).toHaveBeenCalled();
      expect(toastrService.success).toHaveBeenCalledWith('Equipment updated successfully', 'Success');
    });

    it('should not submit invalid edit form', () => {
      // Make form invalid by setting required fields to empty and marking as invalid
      component.equipmentEditForm.patchValue({
        equipmentName: '',
        EquipmentType: '',
        controlledBy: ''
      });
      component.equipmentEditForm.get('equipmentName').setErrors({ required: true });
      component.equipmentEditForm.get('EquipmentType').setErrors({ required: true });
      component.equipmentEditForm.get('controlledBy').setErrors({ required: true });

      component.onEditSubmit();

      expect(component.editSubmitted).toBe(true);
      expect(component.formEditSubmitted).toBe(false);
      expect(projectService.editEquipment).not.toHaveBeenCalled();
    });

    it('should handle edit form submission success', () => {
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.onEditSubmit();

      expect(toastrService.success).toHaveBeenCalledWith('Equipment updated successfully', 'Success');
      expect(mockMixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Edited  Equipment');
      expect(component.editSubmitted).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should handle edit form submission error', () => {
      const mockError = { message: 'Edit failed' };
      projectService.editEquipment.mockReturnValue(throwError(() => mockError));

      component.onEditSubmit();

      expect(toastrService.error).toHaveBeenCalledWith('Edit failed', 'OOPS!');
      expect(component.submitted).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
    });

    it('should handle invalid equipment name/type in edit', () => {
      jest.spyOn(component, 'checkStringEmptyValues').mockReturnValue(true);

      component.onEditSubmit();

      expect(toastrService.error).toHaveBeenCalledWith('Please Enter valid Equipment Name/Type.', 'OOPS!');
      expect(component.submitted).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
    });
  });

  describe('Modal Operations', () => {
    it('should open modal with correct configuration', () => {
      const mockTemplate = {} as any;

      component.openModal(mockTemplate);

      expect(modalService.show).toHaveBeenCalledWith(mockTemplate, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal',
      });
    });

    it('should open filter modal with correct configuration', () => {
      const mockTemplate = {} as any;

      component.openFilterModal(mockTemplate);

      expect(modalService.show).toHaveBeenCalledWith(mockTemplate, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-sm filter-popup custom-modal',
      });
    });

    it('should reset and close modal', () => {
      component.submitted = true;
      component.formSubmitted = true;
      component.editSubmitted = true;
      component.formEditSubmitted = true;

      component.resetAndClose();

      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should open delete modal for specific index', () => {
      const mockTemplate = {} as any;
      component.equipmentlogList = [{ id: 1 }, { id: 2 }];
      jest.spyOn(component, 'openModal').mockImplementation();

      component.openDeleteModal(0, mockTemplate);

      expect(component.deleteIndex[0]).toBe(1);
      expect(component.currentDeleteId).toBe(0);
      expect(component.remove).toBe(false);
      expect(component.openModal).toHaveBeenCalledWith(mockTemplate);
    });

    it('should open delete modal for select all', () => {
      const mockTemplate = {} as any;
      jest.spyOn(component, 'openModal').mockImplementation();

      component.openDeleteModal(-1, mockTemplate);

      expect(component.remove).toBe(true);
      expect(component.openModal).toHaveBeenCalledWith(mockTemplate);
    });

    it('should open edit modal and populate form', () => {
      const mockTemplate = {} as any;
      component.equipmentlogList = [{
        id: 1,
        equipmentAutoId: 2,
        equipmentName: 'Test Equipment',
        PresetEquipmentType: { id: 3 },
        controlledBy: 'Test User'
      }];
      jest.spyOn(component, 'openModal').mockImplementation();

      component.openEditModal(0, mockTemplate);

      expect(component.editIndex).toBe(0);
      expect(component.equipmentEditForm.get('id').value).toBe(1);
      expect(component.equipmentEditForm.get('equipmentAutoId').value).toBe(2);
      expect(component.equipmentEditForm.get('equipmentName').value).toBe('Test Equipment');
      expect(component.equipmentEditForm.get('EquipmentType').value).toBe(3);
      expect(component.equipmentEditForm.get('controlledBy').value).toBe('Test User');
      expect(component.openModal).toHaveBeenCalledWith(mockTemplate);
    });

    it('should open deactivate modal for deactivation', () => {
      const mockTemplate = {} as any;
      const mockData = { equipmentAutoId: 1, equipmentName: 'Test Equipment' };

      component.openDeactivateModal(mockTemplate, mockData, false);

      expect(component.equipmentData).toBe(mockData);
      expect(modalService.show).toHaveBeenCalledWith(mockTemplate, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-md new-gate-popup custom-modal deactivate-modal',
      });
      expect(projectService.getEquipmentMappedRequests).toHaveBeenCalled();
    });

    it('should open deactivate modal for activation', () => {
      const mockTemplate = {} as any;
      const mockData = { id: 1, equipmentName: 'Test Equipment' };
      jest.spyOn(component, 'activateEquipment').mockImplementation();

      component.openDeactivateModal(mockTemplate, mockData, true);

      expect(component.activateEquipment).toHaveBeenCalledWith(mockData);
    });
  });

  describe('Form Reset and Close Operations', () => {
    it('should reset form with action "no"', () => {
      component.modalRef1 = mockModalRef;

      component.resetForm('no');

      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should reset form with action "yes"', () => {
      component.modalRef1 = mockModalRef;
      component.submitted = true;
      component.formSubmitted = true;
      component.editSubmitted = true;
      component.formEditSubmitted = true;

      component.resetForm('yes');

      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should close modal with deactivateEquipment form', () => {
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.close({} as any, 'deactivateEquipment');

      expect(mockModalRef.hide).toHaveBeenCalled();
      expect(component.getEquipments).toHaveBeenCalled();
    });

    it('should close modal with dirty newequipment form', () => {
      const template = {} as any;
      component.haulingLogDetailsForm.markAsDirty();
      jest.spyOn(component, 'closeModalPopup').mockImplementation();

      component.close(template, 'newequipment');

      expect(component.closeModalPopup).toHaveBeenCalledWith(template);
    });

    it('should close modal with clean newequipment form', () => {
      const template = {} as any;
      jest.spyOn(component, 'resetForm').mockImplementation();

      component.close(template, 'newequipment');

      expect(component.resetForm).toHaveBeenCalledWith('yes');
    });

    it('should close modal with dirty equipment edit form', () => {
      const template = {} as any;
      component.equipmentEditForm.markAsDirty();
      jest.spyOn(component, 'closeModalPopup').mockImplementation();

      component.close(template, 'other');

      expect(component.closeModalPopup).toHaveBeenCalledWith(template);
    });

    it('should close modal popup', () => {
      const template = {} as any;

      component.closeModalPopup(template);

      expect(modalService.show).toHaveBeenCalledWith(template, {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      });
    });
  });

  describe('Delete Operations', () => {
    beforeEach(() => {
      // Reset mocks
      projectService.deleteEquipment.mockClear();
      toastrService.success.mockClear();
      toastrService.error.mockClear();
      component.deleteIndex = [];
    });

    it('should delete equipment successfully', () => {
      component.deleteIndex = [1, 2];
      component.selectAll = false;
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.deleteEquipment();

      expect(projectService.deleteEquipment).toHaveBeenCalledWith({
        id: [1, 2],
        ProjectId: '123',
        isSelectAll: false,
        ParentCompanyId: '456',
      });
      expect(toastrService.success).toHaveBeenCalledWith('Equipment deleted successfully', 'Success');
      expect(mockMixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Deleted  Equipment');
      expect(component.getEquipments).toHaveBeenCalled();
      expect(component.deleteSubmitted).toBe(false);
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should handle delete equipment error', () => {
      const mockError = { message: 'Delete failed' };
      projectService.deleteEquipment.mockReturnValue(throwError(() => mockError));
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.deleteEquipment();

      expect(toastrService.error).toHaveBeenCalledWith('Delete failed', 'OOPS!');
      expect(component.deleteSubmitted).toBe(false);
      expect(component.getEquipments).toHaveBeenCalled();
    });

    it('should remove selected items when selectAll is true', () => {
      component.selectAll = true;
      jest.spyOn(component, 'deleteEquipment').mockImplementation();

      component.removeItem();

      expect(component.deleteSubmitted).toBe(true);
      expect(component.deleteEquipment).toHaveBeenCalled();
    });

    it('should remove selected items when selectAll is false', () => {
      component.selectAll = false;
      component.equipmentlogList = [
        { id: 1, isChecked: true },
        { id: 2, isChecked: false },
        { id: 3, isChecked: true }
      ];
      jest.spyOn(component, 'deleteEquipment').mockImplementation();

      component.removeItem();

      expect(component.deleteIndex).toEqual([1, 3]);
      expect(component.deleteEquipment).toHaveBeenCalled();
    });
  });

  describe('Selection Operations', () => {
    it('should check selected row when selectAll is true', () => {
      component.selectAll = true;

      const result = component.checkSelectedRow();

      expect(result).toBe(false);
    });

    it('should check selected row when items are checked', () => {
      component.selectAll = false;
      component.equipmentlogList = [
        { isChecked: true },
        { isChecked: false }
      ];

      const result = component.checkSelectedRow();

      expect(result).toBe(false);
    });

    it('should check selected row when no items are checked', () => {
      component.selectAll = false;
      component.equipmentlogList = [
        { isChecked: false },
        { isChecked: false }
      ];

      const result = component.checkSelectedRow();

      expect(result).toBe(true);
    });

    it('should set selected item', () => {
      component.equipmentlogList = [
        { isChecked: false },
        { isChecked: false }
      ];

      component.setSelectedItem(0);

      expect(component.equipmentlogList[0].isChecked).toBe(true);
    });

    it('should select all equipment data', () => {
      component.selectAll = false;
      component.equipmentlogList = [
        { isChecked: false },
        { isChecked: false }
      ];

      component.selectAllEquipmentsData();

      expect(component.selectAll).toBe(true);
      expect(component.equipmentlogList[0].isChecked).toBe(true);
      expect(component.equipmentlogList[1].isChecked).toBe(true);
    });

    it('should deselect all equipment data', () => {
      component.selectAll = true;
      component.equipmentlogList = [
        { isChecked: true },
        { isChecked: true }
      ];

      component.selectAllEquipmentsData();

      expect(component.selectAll).toBe(false);
      expect(component.equipmentlogList[0].isChecked).toBe(false);
      expect(component.equipmentlogList[1].isChecked).toBe(false);
    });
  });

  describe('Equipment Operations', () => {
    beforeEach(() => {
      // Reset mocks
      projectService.editEquipment.mockClear();
      toastrService.success.mockClear();
      toastrService.error.mockClear();
    });

    it('should activate equipment successfully', () => {
      const mockData = { id: 1, equipmentName: 'Test Equipment' };
      jest.spyOn(component, 'getEquipments').mockImplementation();

      // Ensure the mock returns a response
      projectService.editEquipment.mockReturnValue(of({ message: 'Equipment updated successfully' }));

      component.activateEquipment(mockData);

      expect(projectService.editEquipment).toHaveBeenCalledWith({
        id: 1,
        equipmentName: 'Test Equipment',
        ProjectId: '123',
        ParentCompanyId: '456',
        isActive: true,
      });
      expect(toastrService.success).toHaveBeenCalledWith('Equipment updated successfully', 'Success');
      expect(component.getEquipments).toHaveBeenCalled();
    });

    it('should handle activate equipment error', () => {
      const mockData = { id: 1, equipmentName: 'Test Equipment' };
      const mockError = { message: 'Activation failed' };
      projectService.editEquipment.mockReturnValue(throwError(() => mockError));

      component.activateEquipment(mockData);

      expect(toastrService.error).toHaveBeenCalledWith('Activation failed', 'OOPS!');
    });

    it('should handle activate equipment error with status code 400', () => {
      const mockData = { id: 1, equipmentName: 'Test Equipment' };
      const mockError = {
        message: {
          statusCode: 400,
          details: [{ field: 'Validation error' }]
        }
      };
      projectService.editEquipment.mockReturnValue(throwError(() => mockError));
      jest.spyOn(component, 'showError').mockImplementation();

      component.activateEquipment(mockData);

      expect(component.showError).toHaveBeenCalledWith(mockError);
    });

    it('should switch equipment correctly', () => {
      const mockData: any = {
        equipmentDetails: [{ Equipment: { id: 1 } }]
      };

      component.switchEquipment(mockData, 2);

      expect(mockData.changedEquipmentId).toBe(2);
    });

    it('should not switch equipment when same id', () => {
      const mockData: any = {
        equipmentDetails: [{ Equipment: { id: 1 } }]
      };

      component.switchEquipment(mockData, 1);

      expect(mockData.changedEquipmentId).toBeNull();
    });

    it('should handle status change', () => {
      const event = { target: { checked: true } } as any;
      jest.spyOn(console, 'log').mockImplementation();

      component.onStatusChange(event);

      expect(console.log).toHaveBeenCalledWith('Switch status:', true);
    });
  });

  describe('Utility Functions', () => {
    it('should validate alphanumeric input - valid characters', () => {
      const validEvents = [
        { which: 65, keyCode: 65 }, // A
        { which: 90, keyCode: 90 }, // Z
        { which: 97, keyCode: 97 }, // a
        { which: 122, keyCode: 122 }, // z
        { which: 48, keyCode: 48 }, // 0
        { which: 57, keyCode: 57 }, // 9
        { which: 8, keyCode: 8 }, // Backspace
        { which: 32, keyCode: 32 } // Space
      ];

      validEvents.forEach(event => {
        expect(component.alphaNumericForEquipments(event)).toBe(true);
      });
    });

    it('should validate alphanumeric input - invalid characters', () => {
      const invalidEvents = [
        { which: 33, keyCode: 33 }, // !
        { which: 64, keyCode: 64 }, // @
        { which: 35, keyCode: 35 } // #
      ];

      invalidEvents.forEach(event => {
        expect(component.alphaNumericForEquipments(event)).toBe(false);
      });
    });

    it('should show error correctly', () => {
      const mockError = {
        message: {
          details: [{ field: 'Error message' }]
        }
      };
      toastrService.error.mockClear();

      component.showError(mockError);

      expect(component.submitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(toastrService.error).toHaveBeenCalledWith(['Error message']);
    });

    it('should check string empty values', () => {
      const result = component.checkStringEmptyValues({ EquipmentName: 'Test' });
      expect(result).toBe(false);
    });
  });

  describe('Deactivate Equipment', () => {
    beforeEach(() => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.equipmentData = { id: 1 };
      // Reset mocks
      projectService.deactiveEquipment.mockClear();
      toastrService.success.mockClear();
      toastrService.error.mockClear();
    });

    it('should deactivate equipment with changed equipment data', () => {
      component.mappedRequestList = [
        { changedEquipmentId: 2 },
        { changedEquipmentId: 3 }
      ];
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.deactiveEquipment();

      // After observable completes synchronously, loader should be false
      expect(component.deactivateEquipmentLoader).toBe(false);
      expect(projectService.deactiveEquipment).toHaveBeenCalledWith({
        id: 1,
        equipmentSwitchedRequests: [
          { changedEquipmentId: 2 },
          { changedEquipmentId: 3 }
        ],
        ProjectId: '123',
        ParentCompanyId: '456',
      });
      expect(toastrService.success).toHaveBeenCalledWith('Equipment deactivated successfully', 'Success');
    });

    it('should deactivate equipment with empty mapped request list', () => {
      component.mappedRequestList = [];
      jest.spyOn(component, 'getEquipments').mockImplementation();

      component.deactiveEquipment();

      expect(projectService.deactiveEquipment).toHaveBeenCalledWith({
        id: 1,
        ProjectId: '123',
        ParentCompanyId: '456',
      });
      expect(toastrService.success).toHaveBeenCalledWith('Equipment deactivated successfully', 'Success');
    });

    it('should handle deactivate equipment error', () => {
      component.mappedRequestList = [];
      const mockError = { message: 'Deactivation failed' };
      projectService.deactiveEquipment.mockReturnValue(throwError(() => mockError));

      component.deactiveEquipment();

      expect(toastrService.error).toHaveBeenCalledWith('Deactivation failed', 'OOPS!');
    });

    it('should not deactivate when conditions are not met', () => {
      component.mappedRequestList = [
        { changedEquipmentId: null },
        { changedEquipmentId: 2 }
      ];
      projectService.deactiveEquipment.mockClear();

      component.deactiveEquipment();

      // The loader should be set to true initially, but then set to false when conditions aren't met
      expect(component.deactivateEquipmentLoader).toBe(false);
      expect(projectService.deactiveEquipment).not.toHaveBeenCalled();
    });
  });
});
