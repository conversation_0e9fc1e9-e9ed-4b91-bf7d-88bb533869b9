/* eslint-disable no-unneeded-ternary */
/* eslint-disable max-lines-per-function */
import {
  Component, OnInit, Output, EventEmitter,
} from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Router } from '@angular/router';
import { Socket } from 'ngx-socket-io';
import { ToastrService } from 'ngx-toastr';
import { take } from 'rxjs/operators';
import { NewinspectionFormComponent } from 'src/app/inspection-request/inspection_details/new-inspection-form/new-inspection-form.component';
import { NewCraneRequestCreationFormComponent } from 'src/app/crane-requests/new-crane-request-creation-form/new-crane-request-creation-form.component';
import { NewDeliveryFormComponent } from 'src/app/delivery-requests/delivery-details/new-delivery-form/new-delivery-form.component';
import { AddConcreteRequestComponent } from 'src/app/concrete-request/add-concrete-request/add-concrete-request.component';
import { ProjectService } from '../../services/profile/project.service';
import { ProfileService } from '../../services/profile/profile.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { AuthService } from '../../services/auth/auth.service';
import { ProjectComponent } from '../../project/project.component';
import { ProjectSettingsService } from '../../services/project_settings/project-settings.service';

@Component({
  selector: 'app-sidemenu',
  templateUrl: './sidemenu.component.html',
  })
export class SidemenuComponent implements OnInit {
  public isCollapsed = true;

  public isOpened = true;

  public projectList = [];

  public currentIndex = 0;

  public modalRef: BsModalRef;

  public userData: any = {};

  public urlList = [
    'calendar',
    'delivery-request',
    'profile',
    'notification',
    'void-list',
    'dashboard',
    'members',
    'companies',
  ];

  public menuList = ['members', 'companies', 'gates', 'equipments', 'dfow', 'notification'];

  public accountUrlList = ['billing', 'projects', 'profile'];

  public roleId: any;

  public ProjectId: number;

  public url = '';

  public exist = false;

  public authUser: any = {};

  public myAccount = false;

  public accountAdmin = false;

  public companyList: any = [];

  public currentCompanyId: number;

  public unReadCount = 0;

  public isCompanyCollapsed = true;

  public currentCompanyIndex = 0;

  public loader = true;

  public projectAccount = false;

  public isProject = false;

  public shouldShow: boolean;

  public allRequestIsOpened = true;

  public allCalendarIsOpened = false;

  public allCarbonIsOpened = true;

  public refreshRate: any;

  public selectedTypesCount: any;

  public inspectionLastId: any;

  public deliveryLastId: any;

  public craneLastId: any;

  public concreteLastId: any;

  @Output() private readonly sidemenuToggle = new EventEmitter<boolean>();

  public constructor(
    public router: Router,
    public projectService: ProjectService,
    private readonly modalService: BsModalService,
    public bsModalRef: BsModalRef,
    public socket: Socket,
    private readonly toastr: ToastrService,
    public authService: AuthService,
    public profileService: ProfileService,
    public deliveryService: DeliveryService,
    public projectSettingsService: ProjectSettingsService,
  ) {
    this.checkCurrentDomain();
    this.projectService.projectParent.subscribe((response10): void => {
      this.ProjectId = response10.ProjectId;
      this.isCollapsed = true;
      if (response10 !== undefined && response10 !== null && response10 !== '') {
        this.getMemberInfo();
        this.getProjectInfo();
        this.getLoginUser();
        this.setProject();
        this.getUnReadNotification();
        this.checkURL();
        this.getProjectSharingSettings();
      }
    });
    this.projectService.accountProjectParent.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ProjectId = res.ProjectId;
        this.getMemberInfo();
      }
    });
    this.projectService.refreshProject.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.getParentCompany();
      }
    });
    this.deliveryService.refresh.subscribe((getNdrResponse6): void => {
      if (getNdrResponse6 !== undefined && getNdrResponse6 !== null && getNdrResponse6 !== '') {
        this.getUnReadNotification();
      }
    });
    this.deliveryService.refresh1.subscribe((getNdrResponse6): void => {
      if (getNdrResponse6 !== undefined && getNdrResponse6 !== null && getNdrResponse6 !== '') {
        this.getUnReadNotification();
      }
    });
    this.deliveryService.fetchData.subscribe((getRequestResponse6): void => {
      if (
        getRequestResponse6 !== undefined
        && getRequestResponse6 !== null
        && getRequestResponse6 !== ''
      ) {
        this.getUnReadNotification();
      }
    });
    this.deliveryService.fetchData1.subscribe((getRequestResponse6): void => {
      if (
        getRequestResponse6 !== undefined
        && getRequestResponse6 !== null
        && getRequestResponse6 !== ''
      ) {
        this.getUnReadNotification();
      }
    });
    this.deliveryService.inspectionUpdated.subscribe((getRequestResponse6): void => {
      if (
        getRequestResponse6 !== undefined
        && getRequestResponse6 !== null
        && getRequestResponse6 !== ''
      ) {
        this.getUnReadNotification();
      }
    });
    this.deliveryService.inspectionUpdated1.subscribe((getRequestResponse6): void => {
      if (
        getRequestResponse6 !== undefined
        && getRequestResponse6 !== null
        && getRequestResponse6 !== ''
      ) {
        this.getUnReadNotification();
      }
    });
    this.deliveryService.fetchConcreteData.subscribe((getRequestResponse6): void => {
      if (
        getRequestResponse6 !== undefined
        && getRequestResponse6 !== null
        && getRequestResponse6 !== ''
      ) {
        this.getUnReadNotification();
      }
    });
    this.deliveryService.fetchConcreteData1.subscribe((getRequestResponse6): void => {
      if (
        getRequestResponse6 !== undefined
        && getRequestResponse6 !== null
        && getRequestResponse6 !== ''
      ) {
        this.getUnReadNotification();
      }
    });
    this.deliveryService.refreshCount.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        if (this.unReadCount > 0) {
          this.unReadCount -= 1;
        }
      }
    });
    this.deliveryService.refreshnotifyCount.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        if (this.unReadCount > 0) {
          this.unReadCount = 0;
        }
      }
    });
    this.projectService.isAccountAdmin.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.myAccount = true;
        this.isProject = false;
        this.ProjectId = -1;
      }
    });
    this.projectService.isProject.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.myAccount = false;
        this.isProject = true;
        this.getUnReadNotification();
      }
    });
    this.projectService.projectNameInSideMenu.subscribe((projectId): void => {
      if (projectId !== undefined && projectId !== null && projectId !== '') {
        this.getProjects(+localStorage.getItem('currentCompanyId'));
      }
    });
    this.getParentCompany();
    this.deliveryService.footerDropdown.subscribe((res): void => {
      if (res && !this.isOpened) {
        this.isOpened = true;
      }
      if (res && !this.allRequestIsOpened) {
        this.allRequestIsOpened = true;
      }
      if (res && !this.allCalendarIsOpened) {
        this.allCalendarIsOpened = true;
      }
      if (res && !this.allCarbonIsOpened) {
        this.allCarbonIsOpened = true;
      }
    });
  }

  public getParentCompany(): void {
    this.projectService.getAccountCompany().subscribe((res): void => {
      this.companyList = res.data;
      this.currentCompanyId = this.companyList[0].id;
      if (localStorage.getItem('currentCompanyId')) {
        this.currentCompanyId = +localStorage.getItem('currentCompanyId');
        this.currentCompanyIndex = this.companyList.findIndex(
          (company: { id: any }): any => company.id === this.currentCompanyId,
        );
      }
      localStorage.setItem('currentCompanyId', this.currentCompanyId.toString());
      this.projectService.updateParentCompanyId(this.currentCompanyId);
      this.projectService.updateCompanyList(this.companyList);
      this.getProjects(this.currentCompanyId);
    });
  }

  public getUnReadNotification(): void {
    let params = {};
    if (!this.myAccount) {
      params = {
        ProjectId: this.ProjectId,
        ParentCompanyId: this.currentCompanyId,
      };
    }
    this.projectService.unReadCount(params).subscribe((res): void => {
      this.unReadCount = res.data;
    });
  }

  public checkCurrentDomain(): void {
    this.authService.getUser().subscribe((res): void => {
      if (res.isAccount) {
        this.accountAdmin = true;
        this.loader = false;
        if (!this.ProjectId || this.ProjectId === -1) {
          this.myAccount = true;
          this.projectService.updateMyAccount(true);
          this.projectService.updateAccountAdmin(true);
        }
        this.checkURL();
        this.getUnReadNotification();
      }
    });
  }

  public checkURL(): boolean {
    const urlData = this.router.url.split('/')[1];
    this.url = urlData;
    const index = this.accountUrlList.findIndex((item): boolean => item === this.url);
    if (index === -1 && this.myAccount) {
      this.router.navigate(['/dashboard']);
      return true;
    }
    return false;
  }

  public setProject(): void {
    const index = this.projectList.findIndex((item): boolean => item.id === +this.ProjectId);
    if (index !== -1) {
      this.currentIndex = index;
      this.projectService.setProject(true);
    }
  }

  public selectCompany(index: number): void {
    this.projectService.updateParentCompanyId(this.companyList[index].id);
    this.currentCompanyIndex = index;
    this.projectList = [];
    this.currentCompanyId = this.companyList[index].id;
    localStorage.removeItem('currentCompanyId');
    localStorage.removeItem('ProjectId');
    localStorage.setItem('currentCompanyId', this.currentCompanyId.toString());
    this.router.navigate(['/dashboard']);
    this.getProjects(this.currentCompanyId);
    this.isCompanyCollapsed = true;
  }

  public openAddNDRModal(type): void {
    const className = 'modal-lg new-delivery-popup custom-modal';
    if(type === 'inspection'){
      this.modalRef = this.modalService.show(NewinspectionFormComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
      });
      this.modalRef.content.lastId = this.inspectionLastId;
      this.modalRef.content.closeBtnName = 'Close';
    } else if(type === 'crane'){
      this.modalRef = this.modalService.show(NewCraneRequestCreationFormComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
      });
      this.modalRef.content.lastId = this.craneLastId;
      this.modalRef.content.closeBtnName = 'Close';
    } else if(type === 'delivery'){
      this.modalRef = this.modalService.show(NewDeliveryFormComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
      });
      this.modalRef.content.lastId = this.deliveryLastId;
      this.modalRef.content.closeBtnName = 'Close';
    } else if(type === 'concrete'){
      this.modalRef = this.modalService.show(AddConcreteRequestComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
      });
    }
  }

  public getLoginUser(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.currentCompanyId,
    };
    this.deliveryService.getMemberRole(params).subscribe((res): void => {
      this.authUser = res.data;
      this.deliveryService.updateLoginUser(this.authUser);
    });
  }

  public getMemberInfo(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.currentCompanyId,
    };
    this.profileService.getOverView(param).subscribe((response: any): void => {
      if (response) {
        this.userData = response.data;
        this.projectService.updatedUserData(this.userData);
        this.roleId = this.userData.RoleId;
        const urlData = this.router.url.split('/')[1];
        this.url = urlData;
        if (this.userData.RoleId !== 2 && this.userData.RoleId !== 1) {
          const index = this.urlList.findIndex((item): boolean => item === this.url);
          if (index === -1) {
            /* */
          }
        } else {
          const index = this.menuList.findIndex((item): boolean => item === this.url);
          if (index !== -1) {
            this.isOpened = false;
          }
        }
      }
    });
  }

  public changeSettingsCollapse(): void {
    this.isOpened = !this.isOpened;
  }

  public closeSettingsCollapse(): void {
    this.isOpened = true;
  }


  public changeAllBookingSettingsCollapse(): void {
    this.allRequestIsOpened = !this.allRequestIsOpened;
  }

  public changeCarbonCollapse(): void {
    this.allCarbonIsOpened = !this.allCarbonIsOpened;
  }

  public closeAllbookingSettingsCollapse(): void {
    this.allRequestIsOpened = true;
  }

  public onCheckboxClicked(type: string, isChecked: boolean) {
    this.deliveryService.AllCalendarPermanentRespData$.pipe(take(1)).subscribe((permanentData) => {
      this.inspectionLastId = permanentData.inspectionData.lastId.InspectionId
      this.deliveryLastId = permanentData.deliveryData.lastId.DeliveryId
      this.craneLastId = permanentData.craneData.lastId.CraneRequestId
      this.deliveryService.AllCalendarRespData$.pipe(take(1)).subscribe((tempData) => {
        // Deep copy tempData to avoid modifying permanentData
        const clonedTempData = JSON.parse(JSON.stringify(tempData));
        const selectedTypes = [...this.deliveryService.selectedBookingTypes.value];

        if (isChecked) {
          if (!clonedTempData[type]) {
            clonedTempData[type] = JSON.parse(JSON.stringify(permanentData[type])); // Deep copy the specific type
          }
          if (!selectedTypes.includes(type)) {
            selectedTypes.push(type);
          }
        } else {
          delete clonedTempData[type];
          const index = selectedTypes.indexOf(type);
          if (index > -1) {
            selectedTypes.splice(index, 1);
          }
        }

        this.deliveryService.selectedBookingTypes.next(selectedTypes);
        this.deliveryService.updateAllCalendarRespData(clonedTempData);
        this.deliveryService.dataChanged.next(true);
        this.selectedTypesCount = selectedTypes.length
      });
    });
  }


  // public onCheckboxClicked(type: string, isChecked: boolean) {
  //   this.deliveryService.AllCalendarPermanentRespData$.pipe(take(1)).subscribe((permanentData) => {
  //     this.deliveryService.AllCalendarRespData$.pipe(take(1)).subscribe((tempData) => {
  //       // Deep copy tempData to avoid modifying permanentData
  //       const clonedTempData = JSON.parse(JSON.stringify(tempData));
  //       const currentCheckedTypes = this.deliveryService.selectedBookingTypes.getValue();

  //       if (isChecked) {
  //         if (!clonedTempData[type]) {
  //           clonedTempData[type] = JSON.parse(JSON.stringify(permanentData[type])); // Deep copy the specific type
  //           this.deliveryService.updateAllCalendarRespData(clonedTempData);
  //           this.deliveryService.dataChanged.next(true);
  //           this.deliveryService.selectedBookingTypes.next([...currentCheckedTypes, type]);
  //         }
  //       } else {
  //         delete clonedTempData[type];
  //         this.deliveryService.updateAllCalendarRespData(clonedTempData);
  //         this.deliveryService.dataChanged.next(true);
  //         this.deliveryService.selectedBookingTypes.next(currentCheckedTypes.filter(t => t !== type));
  //       }
  //     });
  //   });
  // }


  public changeRequestCollapse(): void {
    this.allRequestIsOpened = !this.allRequestIsOpened;
  }

  public changeAllCalendarCollapse(): void {
    this.allCalendarIsOpened = !this.allCalendarIsOpened;
  }

  public changeAllCarbonCollapse(): void {
    this.allCarbonIsOpened = !this.allCarbonIsOpened;
  }

  public accountPortal(): void {
    this.myAccount = !this.myAccount;
    this.isCollapsed = !this.isCollapsed;
    this.getProjects(this.currentCompanyId);
    this.projectService.updateAccountAdmin(true);
    if (this.myAccount) {
      this.projectService.updateMyAccount(true);
    }
    this.checkURL();
  }

  public myAccountPortal(): void {
    this.projectService.updateClearProject({ status: true });
    this.accountPortal();
  }

  public getProjectInfo(): void {
    const params = {
      ProjectId: this.ProjectId,
    };
    const urlData = this.router.url.split('/')[1];
    this.url = urlData;
    if (params.ProjectId) {
      this.projectService.getSingleProject(params).subscribe((projectList: any): void => {
        if (projectList) {
          if (projectList.data) {
            if (
              (projectList.data.status === 'overdue'
                || projectList.data.status === 'trialoverdue')
              && this.url !== 'profile'
            ) {
              this.router.navigate(['/profile']);
            }
          }
        }
      });
    }
  }

  public getProjects(CompanyId: number): void {
    this.projectList = [];
    this.projectService.getAccountProject(CompanyId).subscribe((response): void => {
      response.data.forEach((projectObject): void => {
        projectObject.memberDetails.forEach((element): void => {
          if (element.memberProjectStatus === 'active') {
            this.projectList.push(projectObject);
          }
        });
      });
      if (this.projectList.length === 0) {
        this.authService.logout();
      }
      let projectValue: { isAccount: boolean; id: string };
      if (localStorage.getItem('ProjectId')) {
        this.loader = false;
        const selectedProject = this.projectList.filter(
          (projectData): any => projectData.id === +localStorage.getItem('ProjectId'),
        );
        // eslint-disable-next-line prefer-destructuring
        projectValue = selectedProject[0];
      } else {
        // eslint-disable-next-line prefer-destructuring
        projectValue = this.projectList[0];
      }
      this.projectAccount = projectValue.isAccount;
      this.projectService.updateProjectAccount(this.projectAccount);
      this.currentIndex = 0;
      const newData = { ProjectId: projectValue.id, ParentCompanyId: CompanyId };
      if (!this.myAccount) {
        this.projectService.updateProjectParent(newData);
      }
      this.projectService.updatedProjectId(projectValue.id);
      localStorage.setItem('ProjectId', projectValue.id);
      this.loader = false;
    });
  }

  public changeProjectCollapse(): void {
    this.isCollapsed = !this.isCollapsed;
  }

  public redirect(path: any): void {
    this.router.navigate([`/${path}`]);
  }

  public addProjects(): void {
    this.isCollapsed = true;
    this.bsModalRef = this.modalService.show(ProjectComponent, {
      backdrop: 'static',
      keyboard: false,
      class: 'custom-modal',
    });
    this.bsModalRef.content.closeBtnName = 'Close';
  }

  public sidebarToggle(): void {
    this.shouldShow = !this.shouldShow;
    this.sidemenuToggle.emit(this.shouldShow);
  }

  public ngOnInit(): void {
    this.deliveryService.selectedBookingTypes.next(['deliveryData', 'craneData', 'concreteData', 'inspectionData']);
    this.selectedTypesCount = 4
  }

  public selectProject(index: any): void {
    const { id } = this.projectList[index];
    this.checkMemberAccess(id, index);
  }

  public checkMemberAccess(projectId: any, index: number): any {
    const params = {
      ProjectId: projectId,
      ParentCompanyId: this.currentCompanyId,
    };
    this.deliveryService.getMemberRole(params).subscribe((res): void => {
      const responseData = res.data;
      if (responseData.memberProjectStatus === 'active') {
        this.projectService.updatedProjectId(this.projectList[index].id);
        const { id } = this.projectList[index];
        const { isAccount } = this.projectList[index];
        this.projectService.updateProjectAccount(isAccount);
        const newData = { ProjectId: id, ParentCompanyId: this.currentCompanyId };
        this.projectService.updateProjectParent(newData);
        this.myAccount = false;
        this.getProjectInfo();
        localStorage.setItem('ProjectId', this.projectList[index].id);
        this.getLoginUser();
        this.currentIndex = index;
        this.isCollapsed = true;
        // Notify that a project was selected
        this.projectService.notifyProjectSelected();
        this.router.navigate(['/dashboard']);
      } else {
        this.toastr.error('You were deactivated from this project.Please contact super admin');
      }
    });
  }

  public getProjectSharingSettings(): void {
    if (this.ProjectId) {
      const params = {
        ProjectId: this.ProjectId,
      };
      this.projectSettingsService.getProjectSettings(params).subscribe((res): void => {
        const responseData = res?.data?.projectSettings;
        if (responseData) {
          this.refreshRate = +responseData.autoRefreshRateInMinutes;
          this.setupPageRefresh();
        }
      });
    }
  }

  public setupPageRefresh(): void {
    setInterval(() => {
      location.reload();
    }, this.refreshRate * 60 * 1000);
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'select':
          this.selectCompany(data);
          break;
        case 'changeproject':
          this.changeProjectCollapse();
          break;
        case 'account':
          this.myAccountPortal();
          break;
        case 'selectpro':
          this.selectProject(data);
          break;
        case 'close':
          this.closeSettingsCollapse();
          break;
        case 'open':
          this.openAddNDRModal(data);
          break;
        case 'change':
          this.changeRequestCollapse();
          break;
        case 'changeall':
          this.changeAllCarbonCollapse();
          break;
        case 'changeset':
          this.changeSettingsCollapse();
          break;
        default:
          break;
      }
    }
  }
}
