import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RecentReportsComponent } from './recent-reports.component';
import { ReportsService } from '../../services/reports/reports.service';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { of, throwError, Subject } from 'rxjs';
import { FormsModule } from '@angular/forms';
import { PaginationModule } from 'ngx-bootstrap/pagination';
import { NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { CommonModule } from '@angular/common';
import moment from 'moment';

describe('RecentReportsComponent', () => {
  let component: RecentReportsComponent;
  let fixture: ComponentFixture<RecentReportsComponent>;
  let reportsServiceMock: jest.Mocked<ReportsService>;
  let projectServiceMock: any;
  let deliveryServiceMock: any;
  let modalServiceMock: jest.Mocked<BsModalService>;
  let toastrServiceMock: jest.Mocked<ToastrService>;
  let projectSubject: Subject<any>;
  let deliverySubject: Subject<any>;

  beforeEach(async () => {
    // Create subjects for observables
    projectSubject = new Subject();
    deliverySubject = new Subject();

    reportsServiceMock = {
      getRecentReports: jest.fn().mockReturnValue(of({ data: { scheduledReports: [], count: 0 } })),
      runNowScheduledOrSavedReport: jest.fn(),
      deleteScheduledReports: jest.fn(),
    } as any;

    projectServiceMock = {
      projectParent: projectSubject.asObservable(),
    };

    deliveryServiceMock = {
      loginUser: deliverySubject.asObservable(),
    };

    modalServiceMock = {
      show: jest.fn().mockReturnValue({ hide: jest.fn() }),
    } as any;

    toastrServiceMock = {
      success: jest.fn(),
      error: jest.fn(),
      info: jest.fn(),
    } as any;

    await TestBed.configureTestingModule({
      declarations: [RecentReportsComponent],
      imports: [
        CommonModule,
        FormsModule,
        PaginationModule.forRoot(),
        TooltipModule.forRoot(),
        NoopAnimationsModule
      ],
      providers: [
        { provide: ReportsService, useValue: reportsServiceMock },
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: DeliveryService, useValue: deliveryServiceMock },
        { provide: BsModalService, useValue: modalServiceMock },
        { provide: ToastrService, useValue: toastrServiceMock },
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(RecentReportsComponent);
    component = fixture.componentInstance;
    // Don't trigger detectChanges yet to control when subscriptions are triggered
  });

  afterEach(() => {
    projectSubject.complete();
    deliverySubject.complete();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.pageSize).toBe(25);
    expect(component.pageNo).toBe(1);
    expect(component.loader).toBe(true);
    expect(component.recentReports).toEqual([]);
    expect(component.sortColumn).toBe('id');
    expect(component.sort).toBe('DESC');
    expect(component.totalRecentCount).toBe(0);
    expect(component.deleteRecentReportSubmitted).toBe(false);
  });

  describe('Constructor Subscriptions', () => {
    it('should handle delivery service subscription with valid user data', () => {
      const userData = { UserId: '456', RoleId: 2 };

      fixture.detectChanges();
      deliverySubject.next(userData);

      expect(component.authUser).toEqual(userData);
    });

    it('should handle delivery service subscription with null/undefined/empty values', () => {
      fixture.detectChanges();

      // Test with null
      deliverySubject.next(null);
      expect(component.authUser).toEqual({});

      // Test with undefined
      deliverySubject.next(undefined);
      expect(component.authUser).toEqual({});

      // Test with empty string
      deliverySubject.next('');
      expect(component.authUser).toEqual({});
    });

    it('should handle project service subscription with valid project data', () => {
      const projectData = { ProjectId: '123' };
      jest.spyOn(component, 'getRecentReports');

      fixture.detectChanges();
      projectSubject.next(projectData);

      expect(component.ProjectId).toBe('123');
      expect(component.getRecentReports).toHaveBeenCalled();
    });

    it('should handle project service subscription with null/undefined/empty values', () => {
      jest.spyOn(component, 'getRecentReports');

      fixture.detectChanges();

      // Test with null
      projectSubject.next(null);
      expect(component.getRecentReports).not.toHaveBeenCalled();

      // Test with undefined
      projectSubject.next(undefined);
      expect(component.getRecentReports).not.toHaveBeenCalled();

      // Test with empty string
      projectSubject.next('');
      expect(component.getRecentReports).not.toHaveBeenCalled();
    });
  });

  describe('getRecentReports', () => {
    beforeEach(() => {
      component.ProjectId = '123';
      fixture.detectChanges();
    });

    it('should get recent reports when ProjectId is available', () => {
      const mockResponse = {
        data: {
          scheduledReports: [{ id: 1, reportName: 'Test Report' }],
          count: 1,
        },
      };

      reportsServiceMock.getRecentReports.mockReturnValue(of(mockResponse));

      component.getRecentReports();

      expect(reportsServiceMock.getRecentReports).toHaveBeenCalled();
      expect(component.recentReports).toEqual(mockResponse.data.scheduledReports);
      expect(component.totalRecentCount).toBe(mockResponse.data.count);
      expect(component.loader).toBe(false);
    });

    it('should not get recent reports when ProjectId is not available', () => {
      reportsServiceMock.getRecentReports.mockClear();
      component.ProjectId = null;

      component.getRecentReports();

      expect(reportsServiceMock.getRecentReports).not.toHaveBeenCalled();
    });

    it('should handle API error when getting recent reports', () => {
      const mockError = new Error('API Error');
      reportsServiceMock.getRecentReports.mockReturnValue(throwError(() => mockError));

      component.getRecentReports();

      expect(component.loader).toBe(true); // Loader should be set to true initially
      expect(component.recentReports).toEqual([]); // Should be reset to empty array
    });

    it('should handle null response from API', () => {
      reportsServiceMock.getRecentReports.mockReturnValue(of(null));

      component.getRecentReports();

      expect(component.loader).toBe(false);
      expect(component.recentReports).toEqual([]);
    });

    it('should build payload with search text', () => {
      component.getSearchText = 'test search';
      const mockResponse = { data: { scheduledReports: [], count: 0 } };
      reportsServiceMock.getRecentReports.mockReturnValue(of(mockResponse));

      component.getRecentReports();

      expect(reportsServiceMock.getRecentReports).toHaveBeenCalledWith(
        expect.objectContaining({
          search: 'test search'
        })
      );
    });

    it('should build payload with empty search when getSearchText is null', () => {
      component.getSearchText = null;
      const mockResponse = { data: { scheduledReports: [], count: 0 } };
      reportsServiceMock.getRecentReports.mockReturnValue(of(mockResponse));

      component.getRecentReports();

      expect(reportsServiceMock.getRecentReports).toHaveBeenCalledWith(
        expect.objectContaining({
          search: ''
        })
      );
    });
  });

  describe('buildFilterPayload', () => {
    it('should return base payload when no filters are provided', () => {
      const basePayload = { ProjectId: '123', pageSize: 25 };
      component.filterValues = null;

      const result = component.buildFilterPayload(basePayload);

      expect(result).toEqual(basePayload);
    });

    it('should add filters to base payload when filters are provided', () => {
      const basePayload = { ProjectId: '123', pageSize: 25 };
      const testDate = new Date('2023-01-01');
      component.filterValues = {
        createdUserId: 'user123',
        reportName: 'Test Report',
        templateType: 'delivery',
        lastRun: testDate
      };

      const result = component.buildFilterPayload(basePayload);

      expect(result).toEqual({
        ...basePayload,
        createdUserId: 'user123',
        reportName: 'Test Report',
        templateType: 'delivery',
        lastRun: moment(testDate).format('YYYY-MM-DD'),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      });
    });

    it('should only add non-empty filter values', () => {
      const basePayload = { ProjectId: '123', pageSize: 25 };
      component.filterValues = {
        createdUserId: 'user123',
        reportName: '',
        templateType: null,
        lastRun: undefined
      };

      const result = component.buildFilterPayload(basePayload);

      expect(result).toEqual({
        ...basePayload,
        createdUserId: 'user123'
      });
    });
  });

  describe('Page and Sort Operations', () => {
    beforeEach(() => {
      component.ProjectId = '123';
      fixture.detectChanges();
    });

    it('should handle page change', () => {
      const newPage = 2;
      jest.spyOn(component, 'getRecentReports');

      component.changePageNo(newPage);

      expect(component.pageNo).toBe(newPage);
      expect(component.getRecentReports).toHaveBeenCalled();
    });

    it('should handle page size change', () => {
      const newPageSize = 50;
      jest.spyOn(component, 'getRecentReports');

      component.changePageSize(newPageSize);

      expect(component.pageSize).toBe(newPageSize);
      expect(component.getRecentReports).toHaveBeenCalled();
    });

    it('should handle sorting', () => {
      const fieldName = 'reportName';
      const sortType = 'ASC';
      jest.spyOn(component, 'getRecentReports');

      component.sortByField(fieldName, sortType);

      expect(component.sortColumn).toBe(fieldName);
      expect(component.sort).toBe(sortType);
      expect(component.getRecentReports).toHaveBeenCalled();
    });

    it('should call getRecentReports on ngOnChanges', () => {
      jest.spyOn(component, 'getRecentReports');

      component.ngOnChanges();

      expect(component.getRecentReports).toHaveBeenCalled();
    });
  });

  describe('Modal Operations', () => {
    it('should open delete modal', () => {
      const mockTemplate = {} as any;
      const mockData = { id: 1, reportName: 'Test Report' };
      const mockModalRef = { hide: jest.fn() } as any;

      modalServiceMock.show.mockReturnValue(mockModalRef);

      component.openDeleteModal(mockData, mockTemplate);

      expect(component.deleteRecentReportData).toEqual(mockData);
      expect(modalServiceMock.show).toHaveBeenCalledWith(mockTemplate, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal',
      });
      expect(component.modalRef).toBe(mockModalRef);
    });

    it('should reset and close modal', () => {
      component.modalRef = { hide: jest.fn() } as any;
      component.deleteRecentReportData = { id: 1, reportName: 'Test' };

      component.resetAndClose();

      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(component.deleteRecentReportData).toEqual({});
    });
  });

  describe('Delete Report Operations', () => {
    beforeEach(() => {
      component.ProjectId = '123';
      component.deleteRecentReportData = { id: 1 };
      component.modalRef = { hide: jest.fn() } as any;
      fixture.detectChanges();
    });

    it('should delete recent report successfully', () => {
      const mockResponse = { message: 'Report deleted successfully' };
      reportsServiceMock.deleteScheduledReports.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getRecentReports');

      component.deleteRecentReport();

      expect(reportsServiceMock.deleteScheduledReports).toHaveBeenCalledWith({
        id: 1,
        ProjectId: '123',
      });
      expect(toastrServiceMock.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
      expect(component.getRecentReports).toHaveBeenCalled();
      expect(component.deleteRecentReportData).toEqual({});
      expect(component.deleteRecentReportSubmitted).toBe(false);
      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should handle delete report error with statusCode 400', () => {
      const mockError = {
        message: {
          statusCode: 400,
          details: [{ field: 'error message' }]
        }
      };
      reportsServiceMock.deleteScheduledReports.mockReturnValue(throwError(() => mockError));
      jest.spyOn(component, 'showError');

      component.deleteRecentReport();

      expect(component.deleteRecentReportSubmitted).toBe(false);
      expect(component.showError).toHaveBeenCalledWith(mockError);
    });

    it('should handle delete report error without message', () => {
      const mockError = {};
      reportsServiceMock.deleteScheduledReports.mockReturnValue(throwError(() => mockError));

      component.deleteRecentReport();

      expect(component.deleteRecentReportSubmitted).toBe(false);
      expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should handle delete report error with message', () => {
      const mockError = { message: 'Custom error message' };
      reportsServiceMock.deleteScheduledReports.mockReturnValue(throwError(() => mockError));

      component.deleteRecentReport();

      expect(component.deleteRecentReportSubmitted).toBe(false);
      expect(toastrServiceMock.error).toHaveBeenCalledWith(mockError.message, 'OOPS!');
    });
  });








});

// Isolated unit tests for RecentReportsComponent methods (without TestBed)
describe('RecentReportsComponent - Isolated Unit Tests', () => {
  let testComponent: RecentReportsComponent;
  let mockProjectService: any;
  let mockDeliveryService: any;
  let mockReportsService: any;
  let mockModalService: any;
  let mockToastrService: any;

  beforeEach(() => {
    // Create mocks that don't trigger subscriptions
    mockProjectService = {
      projectParent: {
        subscribe: jest.fn()
      }
    };
    mockDeliveryService = {
      loginUser: {
        subscribe: jest.fn()
      }
    };
    mockReportsService = {
      getRecentReports: jest.fn().mockReturnValue(of({ data: { scheduledReports: [], count: 0 } })),
      deleteScheduledReports: jest.fn().mockReturnValue(of({ message: 'Success' })),
      runNowScheduledOrSavedReport: jest.fn().mockReturnValue(of({ data: 'https://example.com/report.pdf' }))
    };
    mockModalService = {
      show: jest.fn().mockReturnValue({
        hide: jest.fn(),
        setClass: jest.fn()
      })
    };
    mockToastrService = {
      success: jest.fn(),
      error: jest.fn(),
      info: jest.fn()
    };

    // Create component instance and manually set up properties to avoid constructor subscriptions
    testComponent = Object.create(RecentReportsComponent.prototype);
    testComponent.projectService = mockProjectService;
    (testComponent as any).reportsService = mockReportsService;
    (testComponent as any).modalService = mockModalService;
    (testComponent as any).toastr = mockToastrService;
    (testComponent as any).deliveryService = mockDeliveryService;

    // Initialize component properties
    testComponent.pageSize = 25;
    testComponent.pageNo = 1;
    testComponent.loader = true;
    testComponent.recentReports = [];
    testComponent.sortColumn = 'id';
    testComponent.sort = 'DESC';
    testComponent.totalRecentCount = 0;
    testComponent.deleteRecentReportSubmitted = false;
    testComponent.authUser = {};
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Download Report Operations', () => {
    let createElementSpy: jest.SpyInstance;
    let appendChildSpy: jest.SpyInstance;
    let mockLink: any;

    beforeEach(() => {
      mockLink = {
        setAttribute: jest.fn(),
        click: jest.fn(),
        remove: jest.fn(),
      };
      createElementSpy = jest.spyOn(document, 'createElement').mockReturnValue(mockLink);
      appendChildSpy = jest.spyOn(document.body, 'appendChild').mockImplementation((node: Node) => node);
    });

    afterEach(() => {
      createElementSpy.mockRestore();
      appendChildSpy.mockRestore();
    });

    it('should download recent report with reRun action', () => {
      const mockData = 'http://example.com/report.pdf';
      const mockAction = 'reRun';

      testComponent.downloadRecentReport(mockData, mockAction);

      expect(createElementSpy).toHaveBeenCalledWith('a');
      expect(mockLink.setAttribute).toHaveBeenCalledWith('target', '_self');
      expect(mockLink.setAttribute).toHaveBeenCalledWith('href', mockData);
      expect(appendChildSpy).toHaveBeenCalledWith(mockLink);
      expect(mockLink.click).toHaveBeenCalled();
      expect(mockLink.remove).toHaveBeenCalled();
      expect(mockToastrService.success).toHaveBeenCalledWith('Report downloaded successfully');
    });

    it('should show info message when no s3_url for lastRun action', () => {
      const mockData = { id: 1 };
      const mockAction = 'lastRun';

      testComponent.downloadRecentReport(mockData, mockAction);

      expect(createElementSpy).not.toHaveBeenCalled();
      expect(mockToastrService.info).toHaveBeenCalledWith('There are no events available within the scheduled date range');
    });

    it('should show info message when no data for reRun action', () => {
      const mockData = null;
      const mockAction = 'reRun';

      testComponent.downloadRecentReport(mockData, mockAction);

      expect(createElementSpy).not.toHaveBeenCalled();
      expect(mockToastrService.info).toHaveBeenCalledWith('There are no events available within the scheduled date range');
    });
  });

  describe('Auto Download Report Operations', () => {
    beforeEach(() => {
      testComponent.ProjectId = '123';
    });

    it('should auto download report successfully', () => {
      const mockData = { id: 1, runNowLoader: false };
      const mockResponse = { data: 'http://example.com/report.pdf' };
      mockReportsService.runNowScheduledOrSavedReport.mockReturnValue(of(mockResponse));
      jest.spyOn(testComponent, 'downloadRecentReport').mockImplementation(() => {});

      testComponent.autoDownloadReportWithRunOption(mockData);

      expect(mockReportsService.runNowScheduledOrSavedReport).toHaveBeenCalledWith({
        id: 1,
        ProjectId: '123',
      });
      expect(testComponent.downloadRecentReport).toHaveBeenCalledWith(mockResponse.data, 'reRun');
      expect(mockData.runNowLoader).toBe(false);
    });

    it('should handle auto download error with statusCode 400', () => {
      const mockData = { id: 1, runNowLoader: false };
      const mockError = {
        message: {
          statusCode: 400,
          details: [{ field: 'error message' }]
        }
      };
      mockReportsService.runNowScheduledOrSavedReport.mockReturnValue(throwError(() => mockError));
      jest.spyOn(testComponent, 'showError').mockImplementation(() => {});

      testComponent.autoDownloadReportWithRunOption(mockData);

      expect(testComponent.showError).toHaveBeenCalledWith(mockError);
      expect(mockData.runNowLoader).toBe(false);
    });

    it('should handle auto download error without message', () => {
      const mockData = { id: 1, runNowLoader: false };
      const mockError = {};
      mockReportsService.runNowScheduledOrSavedReport.mockReturnValue(throwError(() => mockError));

      testComponent.autoDownloadReportWithRunOption(mockData);

      expect(mockToastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
      expect(mockData.runNowLoader).toBe(false);
    });

    it('should handle auto download error with message', () => {
      const mockData = { id: 1, runNowLoader: false };
      const mockError = { message: 'Custom error message' };
      mockReportsService.runNowScheduledOrSavedReport.mockReturnValue(throwError(() => mockError));

      testComponent.autoDownloadReportWithRunOption(mockData);

      expect(mockToastrService.error).toHaveBeenCalledWith(mockError.message, 'OOPS!');
      expect(mockData.runNowLoader).toBe(false);
    });
  });

  describe('Keyboard Event Handlers', () => {
    describe('handleToggleKeydown', () => {
      it('should call sortByField when Enter key is pressed', () => {
        const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
        const data = 'reportName';
        const item = 'ASC';
        jest.spyOn(testComponent, 'sortByField').mockImplementation(() => {});

        testComponent.handleToggleKeydown(mockEvent, data, item);

        expect(mockEvent.preventDefault).toHaveBeenCalled();
        expect(testComponent.sortByField).toHaveBeenCalledWith(data, item);
      });

      it('should call sortByField when Space key is pressed', () => {
        const mockEvent = { key: ' ', preventDefault: jest.fn() } as any;
        const data = 'reportName';
        const item = 'ASC';
        jest.spyOn(testComponent, 'sortByField').mockImplementation(() => {});

        testComponent.handleToggleKeydown(mockEvent, data, item);

        expect(mockEvent.preventDefault).toHaveBeenCalled();
        expect(testComponent.sortByField).toHaveBeenCalledWith(data, item);
      });

      it('should not call sortByField for other keys', () => {
        const mockEvent = { key: 'Tab', preventDefault: jest.fn() } as any;
        const data = 'reportName';
        const item = 'ASC';
        jest.spyOn(testComponent, 'sortByField').mockImplementation(() => {});

        testComponent.handleToggleKeydown(mockEvent, data, item);

        expect(mockEvent.preventDefault).not.toHaveBeenCalled();
        expect(testComponent.sortByField).not.toHaveBeenCalled();
      });
    });

    describe('handleDownKeydown', () => {
      it('should call downloadRecentReport when Enter key is pressed with download type', () => {
        const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
        const data = { id: 1 };
        const item = 'lastRun';
        const type = 'download';
        jest.spyOn(testComponent, 'downloadRecentReport').mockImplementation(() => {});

        testComponent.handleDownKeydown(mockEvent, data, item, type);

        expect(mockEvent.preventDefault).toHaveBeenCalled();
        expect(testComponent.downloadRecentReport).toHaveBeenCalledWith(data, item);
      });

      it('should call openDeleteModal when Space key is pressed with delete type', () => {
        const mockEvent = { key: ' ', preventDefault: jest.fn() } as any;
        const data = { id: 1 };
        const item = 'template';
        const type = 'delete';
        jest.spyOn(testComponent, 'openDeleteModal').mockImplementation(() => {});

        testComponent.handleDownKeydown(mockEvent, data, item, type);

        expect(mockEvent.preventDefault).toHaveBeenCalled();
        expect(testComponent.openDeleteModal).toHaveBeenCalledWith(data, item);
      });

      it('should call autoDownloadReportWithRunOption when Enter key is pressed with auto type', () => {
        const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
        const data = { id: 1 };
        const item = 'item';
        const type = 'auto';
        jest.spyOn(testComponent, 'autoDownloadReportWithRunOption').mockImplementation(() => {});

        testComponent.handleDownKeydown(mockEvent, data, item, type);

        expect(mockEvent.preventDefault).toHaveBeenCalled();
        expect(testComponent.autoDownloadReportWithRunOption).toHaveBeenCalledWith(data);
      });

      it('should handle default case without calling any method', () => {
        const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
        const data = { id: 1 };
        const item = 'item';
        const type = 'unknown';
        jest.spyOn(testComponent, 'downloadRecentReport').mockImplementation(() => {});
        jest.spyOn(testComponent, 'openDeleteModal').mockImplementation(() => {});
        jest.spyOn(testComponent, 'autoDownloadReportWithRunOption').mockImplementation(() => {});

        testComponent.handleDownKeydown(mockEvent, data, item, type);

        expect(mockEvent.preventDefault).toHaveBeenCalled();
        expect(testComponent.downloadRecentReport).not.toHaveBeenCalled();
        expect(testComponent.openDeleteModal).not.toHaveBeenCalled();
        expect(testComponent.autoDownloadReportWithRunOption).not.toHaveBeenCalled();
      });

      it('should not call any method for other keys', () => {
        const mockEvent = { key: 'Tab', preventDefault: jest.fn() } as any;
        const data = { id: 1 };
        const item = 'lastRun';
        const type = 'download';
        jest.spyOn(testComponent, 'downloadRecentReport').mockImplementation(() => {});

        testComponent.handleDownKeydown(mockEvent, data, item, type);

        expect(mockEvent.preventDefault).not.toHaveBeenCalled();
        expect(testComponent.downloadRecentReport).not.toHaveBeenCalled();
      });
    });
  });

  describe('Error Handling', () => {
    it('should show error message from error details', () => {
      const mockError = {
        message: {
          details: [{ field: 'Test error message' }]
        }
      };

      testComponent.showError(mockError);

      expect(mockToastrService.error).toHaveBeenCalledWith(['Test error message']);
    });

    it('should handle error with multiple detail values', () => {
      const mockError = {
        message: {
          details: [{ field1: 'Error 1', field2: 'Error 2' }]
        }
      };

      testComponent.showError(mockError);

      expect(mockToastrService.error).toHaveBeenCalledWith(['Error 1', 'Error 2']);
    });
  });

  describe('ngOnInit', () => {
    it('should be defined and not throw error', () => {
      expect(() => testComponent.ngOnInit()).not.toThrow();
    });
  });
});
