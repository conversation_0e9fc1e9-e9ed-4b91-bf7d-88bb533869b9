import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BsModalRef } from 'ngx-bootstrap/modal';

import { UtilitesDetailsComponent } from './utilites-details.component';

describe('UtilitesDetailsComponent', () => {
  let component: UtilitesDetailsComponent;
  let fixture: ComponentFixture<UtilitesDetailsComponent>;

  beforeEach(() => {
    const mockData = {
      bill_name: 'Test Bill',
      utilityType: 'Electricity',
      bill_amount: '100',
      utilityUnits: 'kWh',
      child: [],
    };

    TestBed.configureTestingModule({
      declarations: [UtilitesDetailsComponent],
      providers: [BsModalRef],
    });

    fixture = TestBed.createComponent(UtilitesDetailsComponent);
    component = fixture.componentInstance;
    component.data = mockData; // Set the mock data
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
