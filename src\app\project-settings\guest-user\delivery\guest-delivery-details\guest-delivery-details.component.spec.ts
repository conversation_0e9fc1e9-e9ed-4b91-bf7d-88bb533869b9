import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UntypedFormBuilder } from '@angular/forms';
import { BsModalService, BsModalRef, ModalOptions } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { of, throwError } from 'rxjs';
import { EventEmitter } from '@angular/core';
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { DeliveryService } from '../../../../services/profile/delivery.service';
import { ProjectService } from '../../../../services/profile/project.service';
import { ProjectSharingService } from '../../../../services/projectSharingService/project-sharing.service';
import { MixpanelService } from '../../../../services/mixpanel.service';
import { GuestDeliveryDetailsComponent } from './guest-delivery-details.component';

describe('GuestDeliveryDetailsComponent', () => {
  let component: GuestDeliveryDetailsComponent;
  let fixture: ComponentFixture<GuestDeliveryDetailsComponent>;
  let toastrService: jest.Mocked<ToastrService>;
  let projectSharingService: jest.Mocked<ProjectSharingService>;
  let modalService: jest.Mocked<BsModalService>;

  const mockNDRData = {
    data: {
      memberDetails: [],
      attachements: [],
      comments: [],
      history: [],
    },
  };

  beforeEach(async () => {
    const modalServiceSpy = {
      show: jest.fn().mockReturnValue({ content: {} }),
    };
    const toastrServiceSpy = {
      success: jest.fn(),
      error: jest.fn(),
    };
    const deliveryServiceSpy = {};
    const projectServiceSpy = {};
    const projectSharingServiceSpy = {
      guestGetNDRData: jest.fn().mockReturnValue(of(mockNDRData)),
    };
    const mixpanelServiceSpy = {};

    await TestBed.configureTestingModule({
      declarations: [GuestDeliveryDetailsComponent],
      providers: [
        { provide: BsModalService, useValue: modalServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: DeliveryService, useValue: deliveryServiceSpy },
        { provide: ProjectService, useValue: projectServiceSpy },
        { provide: ProjectSharingService, useValue: projectSharingServiceSpy },
        { provide: MixpanelService, useValue: mixpanelServiceSpy },
        UntypedFormBuilder,
        BsModalRef,
        ModalOptions,
      ],
    }).compileComponents();

    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    projectSharingService = TestBed.inject(ProjectSharingService) as jest.Mocked<ProjectSharingService>;
    modalService = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
  });

  beforeEach(() => {
    // Mock localStorage
    jest.spyOn(Storage.prototype, 'getItem').mockImplementation((key: string) => {
      if (key === 'guestProjectId') return btoa('123');
      if (key === 'guestParentCompanyId') return btoa('456');
      if (key === 'guestId') return btoa('789');
      return null;
    });

    fixture = TestBed.createComponent(GuestDeliveryDetailsComponent);
    component = fixture.componentInstance;
    component.data = { id: 1 };

    // Initialize required properties
    component.fileData = [[]];
    component.formData = new FormData();
    component.loader = false;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });


  it('should get NDR data on init', () => {
    const expectedParams = {
      DeliveryRequestId: 1,
      ParentCompanyId: parseInt(atob(localStorage.getItem('guestParentCompanyId') || ''), 10),
    };

    component.ngOnInit();

    expect(projectSharingService.guestGetNDRData).toHaveBeenCalledWith(expectedParams);
    expect(component.loader).toBe(false);
  });

  it('should handle NDR data error', () => {
    projectSharingService.guestGetNDRData.mockReturnValueOnce(throwError(() => new Error('API Error')));

    component.ngOnInit();

    expect(component.loader).toBe(false);
  });

  it('should get responsible people initials', () => {
    const person = { firstName: 'John', lastName: 'Doe' };
    expect(component.getResponsiblePeople(person)).toBe('JD');
  });

  it('should return UU for missing name', () => {
    const person = { firstName: null, lastName: null };
    expect(component.getResponsiblePeople(person)).toBe('UU');
  });

  it('should handle file drop with valid file', () => {
    const mockFileEntry = {
      name: 'test.jpg',
      isFile: true,
      isDirectory: false,
      file: (callback: (file: File) => void) => {
        callback(new File([], 'test.jpg', { type: 'image/jpeg' }));
      },
    } as FileSystemFileEntry;

    const mockFile: NgxFileDropEntry = {
      relativePath: 'test.jpg',
      fileEntry: mockFileEntry,
    };

    component.dropped([mockFile]);

    // Wait for async file processing
    setTimeout(() => {
      expect(component.files.length).toBe(1);
    }, 0);
  });

  it('should handle file drop with invalid file extension', () => {
    const mockFileEntry = {
      name: 'test.exe',
      isFile: true,
      isDirectory: false,
    } as FileSystemFileEntry;

    const mockFile: NgxFileDropEntry = {
      relativePath: 'test.exe',
      fileEntry: mockFileEntry,
    };

    component.dropped([mockFile]);

    expect(toastrService.error).toHaveBeenCalledWith(
      'Please select a valid file. Supported file format (.jpg,.jpeg,.png,.pdf,.doc)',
      'OOPS!',
    );
  });


  it('should reset form and close modal', () => {
    const mockModalRef = { hide: jest.fn() };
    component.modalRef1 = mockModalRef as any;
    component.bsModalRef = mockModalRef as any;
    component.closeModalEvent = new EventEmitter<boolean>();
    component.statusChanged = true;

    jest.spyOn(component.closeModalEvent, 'emit');

    component.resetForm('yes');

    expect(mockModalRef.hide).toHaveBeenCalled();
    expect(component.statusChanged).toBe(false);
    expect(component.closeModalEvent.emit).toHaveBeenCalledWith(true);
  });

  it('should handle keyboard events for file removal', () => {
    const mockEvent = { key: 'Enter', preventDefault: jest.fn() };
    component.fileData = [[{ relativePath: 'test.jpg' }]];
    jest.spyOn(component, 'removeFile');

    component.handleDownKeydown(mockEvent as any, 0, 0, 'file');

    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(component.removeFile).toHaveBeenCalledWith(0, 0);
  });

  it('should handle keyboard events for edit modal', () => {
    const mockEvent = { key: 'Enter', preventDefault: jest.fn() };
    const mockModalRef = { content: {} };
    modalService.show.mockReturnValue(mockModalRef as any);

    component.handleDownKeydown(mockEvent as any, {}, {}, 'auto');

    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(modalService.show).toHaveBeenCalled();
  });

  // Additional positive test cases
  it('should handle space key for file removal', () => {
    const mockEvent = { key: ' ', preventDefault: jest.fn() };
    component.fileData = [[{ relativePath: 'test.jpg' }]];
    jest.spyOn(component, 'removeFile');

    component.handleDownKeydown(mockEvent as any, 0, 0, 'file');

    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(component.removeFile).toHaveBeenCalledWith(0, 0);
  });

  it('should handle space key for edit modal', () => {
    const mockEvent = { key: ' ', preventDefault: jest.fn() };
    const mockModalRef = { content: {} };
    modalService.show.mockReturnValue(mockModalRef as any);

    component.handleDownKeydown(mockEvent as any, {}, {}, 'auto');

    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(modalService.show).toHaveBeenCalled();
  });

  it('should not handle keyboard events for unsupported keys', () => {
    const mockEvent = { key: 'Tab', preventDefault: jest.fn() };
    jest.spyOn(component, 'removeFile');

    component.handleDownKeydown(mockEvent as any, 0, 0, 'file');

    expect(mockEvent.preventDefault).not.toHaveBeenCalled();
    expect(component.removeFile).not.toHaveBeenCalled();
  });

  it('should handle default case in keyboard events', () => {
    const mockEvent = { key: 'Enter', preventDefault: jest.fn() };

    component.handleDownKeydown(mockEvent as any, {}, {}, 'unknown');

    expect(mockEvent.preventDefault).toHaveBeenCalled();
  });

  it('should open confirmation modal', () => {
    const mockTemplate = {} as any;
    const mockModalRef = { hide: jest.fn() };
    modalService.show.mockReturnValue(mockModalRef as any);

    component.confirmationClose(mockTemplate);

    expect(modalService.show).toHaveBeenCalledWith(mockTemplate, {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    });
    expect(component.modalRef1).toBe(mockModalRef);
  });

  it('should reset form with "no" action', () => {
    const mockModalRef = { hide: jest.fn() };
    component.modalRef1 = mockModalRef as any;

    component.resetForm('no');

    expect(mockModalRef.hide).toHaveBeenCalled();
  });

  it('should handle getNDR success with member details > 3', () => {
    const mockNDRDataWithMembers = {
      data: {
        memberDetails: [
          { Member: { User: { firstName: 'John', lastName: 'Doe' } } },
          { Member: { User: { firstName: 'Jane', lastName: 'Smith' } } },
          { Member: { User: { firstName: 'Bob', lastName: 'Johnson' } } },
          { Member: { User: { firstName: 'Alice', lastName: 'Brown' } } },
          { Member: { User: { email: '<EMAIL>' } } },
        ],
        attachements: [],
        comments: [],
        history: [
          { type: 'comment' },
          { type: 'status_change' },
        ],
      },
    };

    projectSharingService.guestGetNDRData.mockReturnValue(of(mockNDRDataWithMembers));

    component.ngOnInit();

    expect(component.NDRData).toBe(mockNDRDataWithMembers.data);
    expect(component.fileArray).toEqual(mockNDRDataWithMembers.data.attachements);
    expect(component.commentList).toEqual(mockNDRDataWithMembers.data.comments);
    expect(component.historyList).toEqual([{ type: 'status_change' }]);
    expect(component.currentDeliverySaveItem.edit).toBe(true);
    expect(component.toolTipContent).toContain('Alice Brown');
    expect(component.toolTipContent).toContain('<EMAIL>');
  });

  it('should get file extension correctly', () => {
    expect(component.getFileExtension('test.jpg')).toBe('jpg');
    expect(component.getFileExtension('document.pdf')).toBe('pdf');
    expect(component.getFileExtension('file.name.with.dots.png')).toBe('png');
  });

  it('should check supported extensions', () => {
    expect(component.isSupportedExtension('jpg')).toBe(true);
    expect(component.isSupportedExtension('jpeg')).toBe(true);
    expect(component.isSupportedExtension('png')).toBe(true);
    expect(component.isSupportedExtension('pdf')).toBe(true);
    expect(component.isSupportedExtension('doc')).toBe(true);
    expect(component.isSupportedExtension('exe')).toBe(false);
    expect(component.isSupportedExtension('txt')).toBe(false);
  });

  it('should handle file drop with directory entry', () => {
    const mockFileEntry = {
      name: 'test-folder',
      isFile: false,
      isDirectory: true,
      file: jest.fn(),
    } as unknown as FileSystemFileEntry;

    const mockFile: NgxFileDropEntry = {
      relativePath: 'test-folder',
      fileEntry: mockFileEntry,
    };

    component.dropped([mockFile]);

    expect(component.files.length).toBe(1);
    expect(component.fileData.length).toBe(1);
  });

  it('should handle file size validation - file too large', () => {
    const mockFileEntry = {
      name: 'large-file.jpg',
      isFile: true,
      isDirectory: false,
      file: (callback: (file: File) => void) => {
        // Create a mock file that's larger than 2MB
        const largeFile = new File([''], 'large-file.jpg', { type: 'image/jpeg' });
        Object.defineProperty(largeFile, 'size', { value: 3000000 }); // 3MB
        callback(largeFile);
      },
    } as FileSystemFileEntry;

    component.fileData = [[{}]];
    component.readFile(mockFileEntry, 'jpg', 0, 0);

    setTimeout(() => {
      expect(toastrService.error).toHaveBeenCalledWith('Please choose an attachment less than or equal to 2MB');
      expect(component.fileData.length).toBe(0);
    }, 0);
  });

  it('should handle file reading successfully', () => {
    const mockFileEntry = {
      name: 'test.jpg',
      isFile: true,
      isDirectory: false,
      file: (callback: (file: File) => void) => {
        const file = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });
        Object.defineProperty(file, 'size', { value: 1000000 }); // 1MB
        callback(file);
      },
    } as FileSystemFileEntry;

    component.fileData = [[{}]];
    component.readFile(mockFileEntry, 'jpg', 0, 0);

    setTimeout(() => {
      expect(component.fileData[0][0].extension).toBe('jpg');
      expect(component.fileData[0][0].image).toBeDefined();
    }, 0);
  });

  it('should handle fileOver event', () => {
    const mockEvent = { preventDefault: jest.fn() };
    expect(() => component.fileOver(mockEvent)).not.toThrow();
  });

  it('should handle fileLeave event', () => {
    const mockEvent = { preventDefault: jest.fn() };
    expect(() => component.fileLeave(mockEvent)).not.toThrow();
  });

  // Upload functionality tests
  it('should upload data successfully', () => {
    const mockFormData = new FormData();
    const mockFileEntry = {
      file: jest.fn((callback) => {
        callback(new File(['test'], 'test.jpg', { type: 'image/jpeg' }));
      }),
    };

    component.fileData = [[{ fileEntry: mockFileEntry, relativePath: 'test.jpg' }]];
    component.formData = mockFormData;

    const mockResponse = { message: 'Upload successful' };
    projectSharingService.guestAddAttachement = jest.fn().mockReturnValue(of(mockResponse));
    jest.spyOn(component, 'getNDR');

    component.uploadData();

    expect(component.uploadSubmitted).toBe(true);
    expect(projectSharingService.guestAddAttachement).toHaveBeenCalled();
  });

  it('should handle upload error with status code 400', () => {
    const mockError = {
      message: {
        statusCode: 400,
        details: [{ field: 'error message' }],
      },
    };

    component.fileData = [[{ fileEntry: { file: jest.fn() }, relativePath: 'test.jpg' }]];
    projectSharingService.guestAddAttachement = jest.fn().mockReturnValue(throwError(() => mockError));
    jest.spyOn(component, 'showError');

    component.uploadData();

    expect(component.uploadSubmitted).toBe(false);
    expect(component.showError).toHaveBeenCalledWith(mockError);
  });

  it('should handle upload error with other status codes', () => {
    const mockError = { message: 'General error' };

    component.fileData = [[{ fileEntry: { file: jest.fn() }, relativePath: 'test.jpg' }]];
    projectSharingService.guestAddAttachement = jest.fn().mockReturnValue(throwError(() => mockError));

    component.uploadData();

    expect(component.uploadSubmitted).toBe(false);
    expect(toastrService.error).toHaveBeenCalledWith(mockError.message, 'OOPS!');
  });

  it('should remove file correctly', () => {
    component.fileData = [['file1', 'file2'], ['file3']];
    component.formData = new FormData();
    component.formData.append('test', 'value');

    component.removeFile(0, 1);

    expect(component.fileData[0].length).toBe(1);
    expect(component.fileData.length).toBe(1);
  });

  it('should show error message correctly', () => {
    const mockError = {
      message: {
        details: [{ field: 'error message' }],
      },
    };

    component.showError(mockError);

    expect(toastrService.error).toHaveBeenCalledWith('error message');
  });

  // Comment form tests
  it('should initialize comment form', () => {
    component.commentForm();

    expect(component.commentDetailsForm).toBeDefined();
    expect(component.commentDetailsForm.get('comment')).toBeDefined();
    expect(component.commentDetailsForm.get('comment')?.hasError('required')).toBe(true);
  });

  it('should submit comment successfully', () => {
    const mockResponse = { message: 'Comment added successfully' };
    projectSharingService.guestCreateComment = jest.fn().mockReturnValue(of(mockResponse));
    jest.spyOn(component, 'getNDR');

    component.commentDetailsForm.patchValue({ comment: 'Test comment' });

    component.onSubmit();

    expect(component.submitted).toBe(true);
    expect(component.formSubmitted).toBe(true);
    expect(projectSharingService.guestCreateComment).toHaveBeenCalledWith({
      comment: 'Test comment',
      DeliveryRequestId: component.DeliveryRequestId,
      ParentCompanyId: component.ParentCompanyId,
      userId: component.guestUserId,
    });
  });

  it('should handle invalid comment form submission', () => {
    component.commentDetailsForm.patchValue({ comment: '' });

    component.onSubmit();

    expect(component.submitted).toBe(true);
    expect(component.formSubmitted).toBe(false);
  });

  it('should handle empty comment submission', () => {
    component.commentDetailsForm.patchValue({ comment: '   ' });

    component.onSubmit();

    expect(toastrService.error).toHaveBeenCalledWith('Please enter a valid comment.', 'OOPS!');
    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
  });

  it('should handle comment submission error with status code 400', () => {
    const mockError = {
      message: {
        statusCode: 400,
        details: [{ field: 'comment error' }],
      },
    };

    projectSharingService.guestCreateComment = jest.fn().mockReturnValue(throwError(() => mockError));
    jest.spyOn(component, 'showCommentError');
    component.commentDetailsForm.patchValue({ comment: 'Test comment' });

    component.onSubmit();

    expect(component.showCommentError).toHaveBeenCalledWith(mockError);
  });

  it('should handle comment submission error with other status codes', () => {
    const mockError = { message: 'General comment error' };

    projectSharingService.guestCreateComment = jest.fn().mockReturnValue(throwError(() => mockError));
    component.commentDetailsForm.patchValue({ comment: 'Test comment' });

    component.onSubmit();

    expect(toastrService.error).toHaveBeenCalledWith(mockError.message, 'OOPS!');
    expect(component.submitted).toBe(false);
  });

  it('should check string empty values correctly', () => {
    expect(component.checkStringEmptyValues({ comment: '' })).toBe(true);
    expect(component.checkStringEmptyValues({ comment: '   ' })).toBe(true);
    expect(component.checkStringEmptyValues({ comment: 'valid comment' })).toBe(false);
  });

  it('should show comment error correctly', () => {
    const mockError = {
      message: {
        details: [{ field: 'comment error message' }],
      },
    };

    component.showCommentError(mockError);

    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
    expect(toastrService.error).toHaveBeenCalledWith('comment error message');
  });

  // Modal and edit functionality tests
  it('should open edit modal correctly', () => {
    const mockItem = {
      id: 1,
      recurrence: {
        id: 123,
        recurrence: 'Weekly',
        recurrenceEndDate: '2024-12-31',
      },
    };
    const mockAction = 2;
    const mockModalRef = {
      content: {
        closeBtnName: '',
        seriesOption: 0,
        recurrenceId: null,
        recurrenceEndDate: null,
      },
    };

    modalService.show.mockReturnValue(mockModalRef as any);
    component.bsModalRef = { hide: jest.fn() } as any;

    component.openEditModal(mockItem, mockAction);

    expect(component.bsModalRef.hide).toHaveBeenCalled();
    expect(modalService.show).toHaveBeenCalled();
    expect(mockModalRef.content.closeBtnName).toBe('Close');
    expect(mockModalRef.content.seriesOption).toBe(mockAction);
    expect(mockModalRef.content.recurrenceId).toBe(123);
  });

  it('should open edit modal for non-recurring item', () => {
    const mockItem = {
      id: 1,
      recurrence: null,
    };
    const mockAction = 1;
    const mockModalRef = {
      content: {
        closeBtnName: '',
        seriesOption: 0,
        recurrenceId: null,
        recurrenceEndDate: null,
      },
    };

    modalService.show.mockReturnValue(mockModalRef as any);
    component.bsModalRef = { hide: jest.fn() } as any;

    component.openEditModal(mockItem, mockAction);

    expect(mockModalRef.content.seriesOption).toBe(1);
    expect(mockModalRef.content.recurrenceId).toBe(null);
    expect(mockModalRef.content.recurrenceEndDate).toBe(null);
  });

  it('should open edit modal for "Does Not Repeat" recurrence', () => {
    const mockItem = {
      id: 1,
      recurrence: {
        id: 123,
        recurrence: 'Does Not Repeat',
        recurrenceEndDate: '2024-12-31',
      },
    };
    const mockAction = 2;
    const mockModalRef = {
      content: {
        closeBtnName: '',
        seriesOption: 0,
        recurrenceId: null,
        recurrenceEndDate: null,
      },
    };

    modalService.show.mockReturnValue(mockModalRef as any);
    component.bsModalRef = { hide: jest.fn() } as any;

    component.openEditModal(mockItem, mockAction);

    expect(mockModalRef.content.seriesOption).toBe(1);
  });

  it('should change request collapse and initialize series options', () => {
    const mockData = {
      deliveryStart: '2025-01-01T10:00:00Z', // Future date
    };

    jest.spyOn(component, 'initializeSeriesOption');

    component.changeRequestCollapse(mockData);

    expect(component.initializeSeriesOption).toHaveBeenCalled();
    expect(component.allRequestIsOpened).toBe(true);
  });

  it('should change request collapse with past date and disable options', () => {
    const mockData = {
      deliveryStart: '2020-01-01T10:00:00Z', // Past date
    };

    jest.spyOn(component, 'initializeSeriesOption');

    component.changeRequestCollapse(mockData);

    expect(component.initializeSeriesOption).toHaveBeenCalled();
    expect(component.seriesOptions.length).toBe(2);
    expect(component.seriesOptions[1].disabled).toBe(true);
  });

  it('should initialize series options correctly', () => {
    component.initializeSeriesOption();

    expect(component.seriesOptions).toEqual([
      {
        option: 1,
        text: 'This event',
        disabled: false,
      },
      {
        option: 2,
        text: 'This and all following events',
        disabled: false,
      },
    ]);
  });

  // Constructor and initialization tests
  it('should initialize component properties in constructor', () => {
    // Mock localStorage for constructor
    jest.spyOn(Storage.prototype, 'getItem').mockImplementation((key: string) => {
      if (key === 'guestProjectId') return btoa('123');
      if (key === 'guestParentCompanyId') return btoa('456');
      if (key === 'guestId') return btoa('789');
      return null;
    });

    const newComponent = new GuestDeliveryDetailsComponent(
      modalService,
      toastrService,
      TestBed.inject(BsModalRef),
      TestBed.inject(DeliveryService),
      TestBed.inject(MixpanelService),
      TestBed.inject(UntypedFormBuilder),
      TestBed.inject(ProjectService),
      projectSharingService,
      TestBed.inject(ModalOptions),
    );

    expect(newComponent.ProjectId).toBe(123);
    expect(newComponent.ParentCompanyId).toBe(456);
    expect(newComponent.guestUserId).toBe(789);
    expect(newComponent.formData).toBeInstanceOf(FormData);
  });

  // Negative test cases
  it('should handle getNDR error gracefully', () => {
    const mockError = new Error('Network error');
    projectSharingService.guestGetNDRData.mockReturnValue(throwError(() => mockError));

    component.ngOnInit();

    expect(component.loader).toBe(false);
  });

  it('should handle file reading when fileData group does not exist', () => {
    const mockFileEntry = {
      name: 'test.jpg',
      isFile: true,
      isDirectory: false,
      file: (callback: (file: File) => void) => {
        const file = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });
        Object.defineProperty(file, 'size', { value: 1000000 }); // 1MB
        callback(file);
      },
    } as FileSystemFileEntry;

    component.fileData = []; // Empty fileData

    expect(() => component.readFile(mockFileEntry, 'jpg', 0, 0)).not.toThrow();
  });

  it('should handle processFileEntry with unsupported file', () => {
    const mockEntry = {
      relativePath: 'test.exe',
      fileEntry: {
        isFile: true,
      } as FileSystemFileEntry,
    };

    component.fileData = [[]];
    jest.spyOn(component, 'getFileExtension').mockReturnValue('exe');
    jest.spyOn(component, 'isSupportedExtension').mockReturnValue(false);

    component.processFileEntry(mockEntry, 0, 0);

    expect(toastrService.error).toHaveBeenCalledWith(
      'Please select a valid file. Supported file format (.jpg,.jpeg,.png,.pdf,.doc)',
      'OOPS!',
    );
    expect(component.fileData.length).toBe(0);
  });

  it('should handle openEditModal when bsModalRef is null', () => {
    const mockItem = { id: 1, recurrence: null };
    const mockAction = 1;
    const mockModalRef = { content: {} };

    modalService.show.mockReturnValue(mockModalRef as any);
    component.bsModalRef = null;

    expect(() => component.openEditModal(mockItem, mockAction)).not.toThrow();
  });

  it('should handle resetForm when modalRef1 is null', () => {
    component.modalRef1 = null;
    component.bsModalRef = { hide: jest.fn() } as any;
    component.closeModalEvent = new EventEmitter<boolean>();
    jest.spyOn(component.closeModalEvent, 'emit');

    component.resetForm('yes');

    expect(component.bsModalRef.hide).toHaveBeenCalled();
    expect(component.closeModalEvent.emit).toHaveBeenCalledWith(true);
  });
});
