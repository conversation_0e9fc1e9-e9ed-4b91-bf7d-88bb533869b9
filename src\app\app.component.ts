import { Component, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { BnNgIdleService } from 'bn-ng-idle';
import { AuthService } from './services';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
})
export class AppComponent implements OnDestroy {
  public title = 'Admin Dashboard';

  private channel: BroadcastChannel;

  // private readonly tabId: string = Math.random().toString(36).substring(2);

  private readonly tabId: string = Array.from(crypto.getRandomValues(new Uint8Array(16)))
    .map((byte) => byte.toString(36))
    .join('');

  private readonly tabStatusMap: Map<string, boolean> = new Map();

  private isIdle = false;

  public constructor(
    private readonly ngZone: NgZone,
    private readonly bnIdle: BnNgIdleService,
    private readonly authService: AuthService,
  ) {
    // DI only – no init logic needed here
  }

  public ngOnInit(): void {
    this.channel = new BroadcastChannel('app-activity');

    this.listenToActivityChannel();
    this.registerUserActivityEvents();

    this.ngZone.runOutsideAngular(() => {
      setTimeout(() => {
        this.ngZone.runTask(() => {});
      }, 10000);
    });

    this.bnIdle.startWatching(14400).subscribe((isTimedOut: boolean) => {
      if (isTimedOut) {
        this.isIdle = true;
        this.channel.postMessage({ type: 'idle', tabId: this.tabId });
        this.checkAndHandleLogout();
      }
    });
  }

  public ngOnDestroy(): void {
    this.channel?.close();
  }

  private listenToActivityChannel(): void {
    this.channel.onmessage = (event) => {
      const msg = event.data;

      if (msg.type === 'activity') {
        this.tabStatusMap.set(msg.tabId, false);
      } else if (msg.type === 'idle') {
        this.tabStatusMap.set(msg.tabId, true);
        this.checkAndHandleLogout();
      } else if (msg.type === 'logout') {
        this.authService.logout();
        this.bnIdle.stopTimer();
      }
    };
  }

  private registerUserActivityEvents(): void {
    const activityHandler = () => {
      if (this.isIdle) {
        this.bnIdle.resetTimer();
      }
      this.isIdle = false;
      this.channel.postMessage({ type: 'activity', tabId: this.tabId });
    };

    window.addEventListener('mousemove', activityHandler);
    window.addEventListener('keydown', activityHandler);
  }

  private checkAndHandleLogout(): void {
    this.tabStatusMap.set(this.tabId, this.isIdle);

    const allIdle = Array.from(this.tabStatusMap.values()).every((status) => status === true);
    if (allIdle) {
      this.channel.postMessage({ type: 'logout' });
      this.authService.logout();
      this.bnIdle.stopTimer();
    }
  }
}
