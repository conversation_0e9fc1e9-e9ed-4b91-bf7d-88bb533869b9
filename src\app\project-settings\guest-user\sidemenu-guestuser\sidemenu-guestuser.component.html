<aside class="sidemenu-main">
  <div class="side-logo d-none d-md-none d-lg-flex mx-auto mt-5 mb-0 ps-4">
    <img src="./assets/images/logo.svg" alt="Follo" class="img-fluid" />
  </div>
  <div>
    <ul class="list-group side-list-group mt-1">
      <li class="list-group-item">
        <span class="fs16 fw-bold color-black ms-2">Calendar</span>
      </li>
      <li class="list-group-item p0 border-0 bg-transparent">
        <a class="backicon btn btn-orange pe-0" (click)="homePage()">
        Home
        </a>
        </li>
      <!-- Dropdown Menu -->
      <li class="list-group-item" *ngIf="isPublicWebsiteEnabled && showDeliveryCalendar" routerLinkActive="active" (click)="closeSettingsCollapse()" (keydown)="handleToggleKeydown($event)">
        <a routerLink="/guest-delivery-calendar">
          <span class="menu-txt side-menu-li ms-2">Delivery Calendar</span>
        </a>
      </li>
      <li class="list-group-item" *ngIf="isPublicWebsiteEnabled && showCraneCalendar"  routerLinkActive="active" (click)="closeSettingsCollapse()"  (keydown)="handleToggleKeydown($event)">
        <a routerLink="/guest-crane-calendar">
          <span class="menu-txt side-menu-li ms-2">Crane Calendar</span>
        </a>
      </li>
      <li class="list-group-item" *ngIf="isPublicWebsiteEnabled && showConcreteCalendar" routerLinkActive="active"  (click)="closeSettingsCollapse()"  (keydown)="handleToggleKeydown($event)">
        <a routerLink="/guest-concrete-calendar">
          <span class="menu-txt side-menu-li ms-2">Concrete Calendar</span>
        </a>
      </li>
    </ul>
  </div>
</aside>
