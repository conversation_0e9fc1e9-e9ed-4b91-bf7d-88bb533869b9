import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { Router } from '@angular/router';
import { ApiService } from '../api_base/api.service';

@Injectable({
  providedIn: 'root',
  })
export class ProjectService {
  public projectId: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public userData: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public subStatus: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public isProject: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public isAccountAdmin: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public ParentCompanyId: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public refreshProject: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public projectParent: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public accountProjectParent: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public companyList: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public isMyAccount: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public isProjectAccount: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public clearProject: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public fileUpload: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public companyFileUpload: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public projectNameInSideMenu: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public newProjectCreatedWithProjectPlan: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public siteplanUploaded: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public projectDetails = this.projectId.asObservable();

  public projectSelected = new Subject<any>();

  public constructor(private readonly api: ApiService, public router: Router) {
  }

  public getProjects(): any {
    if (localStorage.getItem('ProjectId') === undefined || localStorage.getItem('ProjectId') === null) {
      this.getProject().subscribe((response): void => {
        const projectValue = response.data[0];
        this.updatedProjectId(projectValue.id);
      });
    } else {
      this.updatedProjectId(localStorage.getItem('ProjectId'));
    }
  }

  public updateCompanyList(data): void {
    return this.companyList.next(data);
  }

  public updateProjectAccount(data): void {
    return this.isProjectAccount.next(data);
  }

  public refreshProjectStatus(data): void {
    return this.refreshProject.next(data);
  }

  public uploadBulkNdrFile(data): void {
    return this.fileUpload.next(data);
  }

  public checkIfNewProjectCreatedWithProjectPlan(data): void {
    return this.newProjectCreatedWithProjectPlan.next(data);
  }

  public uploadBulkCompanyFile(data): void {
    return this.companyFileUpload.next(data);
  }

  public checkCurrentDomain(): boolean {
    const origin = window.location.href;
    if (origin && origin.indexOf('//') >= 0) {
      const splittedOrigin = origin.split('//');
      if (splittedOrigin[1].indexOf('invite') > -1) {
        return false;
      }
      if (splittedOrigin[1].indexOf('localhost') > -1) {
        return false;
      }
      const splittedDomainOrigin = splittedOrigin[1].split('.')[0];
      let domainLength;
      if (splittedDomainOrigin) {
        domainLength = splittedDomainOrigin.split('-');
        if (domainLength.length === 2) {
          if (domainLength[0].indexOf('invite') > -1) {
            return false;
          }
          return true;
        }
        return false;
      }

      return false;
    }
    return false;
  }

  public createAccountProject(payload): Observable<any> {
    return this.api.post('project/create_account_project', payload);
  }

  public updatedProjectId(data): any {
    return this.projectId.next(data);
  }

  public updatedProjectNameInSideMenu(data): any {
    return this.projectNameInSideMenu.next(data);
  }

  public updateClearProject(data): any {
    setTimeout((): void => {
      this.clearProject.next(null);
    }, 100);
    return this.clearProject.next(data);
  }

  public updateProjectParent(data): any {
    return this.projectParent.next(data);
  }

  public updateAccountProjectParent(data): any {
    return this.accountProjectParent.next(data);
  }

  public updateAccountAdmin(data): any {
    return this.isAccountAdmin.next(data);
  }

  public updateMyAccount(data): any {
    return this.isMyAccount.next(data);
  }

  public updateParentCompanyId(data): any {
    return this.ParentCompanyId.next(data);
  }

  public updatedSubStatus(data): any {
    return this.subStatus.next(data);
  }

  public setProject(data): any {
    setTimeout((): void => {
      this.isProject.next(null);
    }, 100);
    return this.isProject.next(data);
  }

  public updatedUserData(data): any {
    return this.userData.next(data);
  }

  public updateDashboardSiteplan(data): any {
    return this.siteplanUploaded.next(data);
  }

  public getProject(): Observable<any> {
    return this.api.get('project/get_project');
  }

  public getAccountProject(companyId): Observable<any> {
    return this.api.get(`project/get_account_projects/${companyId}`);
  }

  public getTimeZoneList(): Observable<any> {
    return this.api.get('timezone_list/get_timezone_list');
  }

  public createProject(data): Observable<any> {
    const payload = data;
    payload.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    return this.api.post('project/create_project', payload);
  }

  public applyOverRide(data): Observable<any> {
    return this.api.post('overRide/apply_override', data);
  }

  public deleteGate(payload): Observable<any> {
    return this.api.post('gates/delete_gates', payload);
  }

  public getMappedRequests(payload): Observable<any> {
    return this.api.post('gates/get_mapped_requests', payload);
  }

  public deactivateGate(payload): Observable<any> {
    return this.api.post('gates/deactivate_gate', payload);
  }

  public deactiveEquipment(payload): Observable<any> {
    return this.api.post('equipment/deactivate_equipment', payload);
  }

  public getEquipmentMappedRequests(payload): Observable<any> {
    return this.api.post('equipment/get_mapped_requests', payload);
  }

  public deactivateMember(payload): Observable<any> {
    return this.api.post('member/deactivate_member', payload);
  }

  public activateMember(payload): Observable<any> {
    return this.api.post('member/activate_member', payload);
  }

  public getMemberMappedRequests(payload): Observable<any> {
    return this.api.post('member/get_mapped_requests', payload);
  }

  public listMember(params, payload): Observable<any> {
    return this.api.post(`member/list_member/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.ParentCompanyId}`, payload);
  }

  public listAllMember(params): Observable<any> {
    return this.api.get(`member/list_all_member/${params.ProjectId}/${params.ParentCompanyId}`);
  }

  public searchAllMember(params): Observable<any> {
    return this.api.get(`member/search_all_member/${params.ProjectId}/${params.search}/${params.ParentCompanyId}`);
  }

  public listEquipmentLog(params, payload): Observable<any> {
    return this.api.get(`equipmentLog/equipmentlog_list/${params.pageSize}/${params.pageNo}`, payload);
  }

  public listWasteLog(params, payload): Observable<any> {
    return this.api.get(`wasteLog/wastelog_list/${params.pageSize}/${params.pageNo}`, payload);
  }

  public listHaulingLog(params, payload): Observable<any> {
    return this.api.get(`haulingLog/haulinglog_list/${params.pageSize}/${params.pageNo}`, payload);
  }

  public listUtilitiesLog(params, payload): Observable<any> {
    return this.api.get(`utilities/Utilities_list/${params.pageSize}/${params.pageNo}`, payload);
  }

  public listEquipment(params, payload): Observable<any> {
    return this.api.post(`equipment/equipment_list/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.ParentCompanyId}`, payload);
  }

  public listCraneEquipment(params, payload): Observable<any> {
    const p = payload;
    return this.api.post(`equipment/crane_equipment_list/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.ParentCompanyId}`, p);
  }

  public getLastDevlieryRequestId(params): Observable<any> {
    return this.api.get(`delivery/get_last_delivery_request_id/${params.ProjectId}/${params.ParentCompanyId}`);
  }

  public getLastCraneRequestId(params): Observable<any> {
    return this.api.get(`crane_request/get_last_crane_request_id/${params.ProjectId}/${params.ParentCompanyId}`);
  }

  public upgradeProjectPlans(data): Observable<any> {
    return this.api.post('project/upgrade_plan', data);
  }

  public getSingleProject(params): Observable<any> {
    return this.api.get(`project/get_single_project/${params.ProjectId}`);
  }

  public getProjectSubscription(params): Observable<any> {
    return this.api.get(`project/get_project_profile/${params.ProjectId}`);
  }

  public getAccountCompany(): Observable<any> {
    return this.api.get('project/get_accounts_company');
  }

  public deleteCalendarEvent(id, params, payload): Observable<any> {
    return this.api.putMethod(`calendar_settings/delete_event/${id}`, payload, params);
  }

  public listCompany(params, payload): Observable<any> {
    const p = payload;
    return this.api.post(`company/get_companies/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.ParentCompanyId}`, p);
  }

  public addCompany(payload): Observable<any> {
    return this.api.post('company/add_company', payload);
  }

  public checkExistCompany(params, payload): Observable<any> {
    return this.api.post(`company/check_exist_company/${params.ProjectId}/${params.ParentCompanyId}`, payload);
  }

  public companyLogoUpload(params, payload): Observable<any> {
    return this.api.requestImage(`company/upload_logo/${params.ProjectId}/${params.ParentCompanyId}`, payload);
  }

  public utilitiesUpload(params, payload): Observable<any> {
    return this.api.requestImage(`carbon_emission/upload_utilities/${params.zipcode}/${params.projectId}`, payload);
  }

  public editCompany(payload): Observable<any> {
    return this.api.post('company/edit_company', payload);
  }

  public listEquipmentType(params): Observable<any> {
    return this.api.get(`equipment/equipment_type_list/${params.ProjectId}`);
  }

  public getOnboardingInviteLink(payload): Observable<any> {
    return this.api.post('member/get_onboarding_invite_link', payload);
  }

  public listPresetEquipmentType(): Observable<any> {
    return this.api.get('equipment/preset_equipment_type_list');
  }

  public deleteCompany(payload): Observable<any> {
    return this.api.post('company/delete_company', payload);
  }


  public addEquipment(payload): Observable<any> {
    return this.api.post('equipment/add_equipment', payload);
  }

  public addEquipmentLog(payload): Observable<any> {
    return this.api.post('equipmentLog/add_equipmentlog', payload);
  }

  public addWasteLog(payload): Observable<any> {
    return this.api.post('wasteLog/add_wastelog', payload);
  }

  public addHaulingLog(payload): Observable<any> {
    return this.api.post('haulingLog/add_haulinglog', payload);
  }

  public addUtilitiesLog(payload): Observable<any> {
    return this.api.post('utilities/add_Utilities', payload);
  }

  public getDashboardData(ProjectId): Observable<any> {
    return this.api.get(`carbon_emission/get_dashboard_data/${ProjectId}`);
  }

  public editEquipment(payload): Observable<any> {
    return this.api.post('equipment/update_equipment', payload);
  }

  public editProjectDetail(payload): Observable<any> {
    return this.api.post('project/edit_project', payload);
  }

  public deleteEquipment(payload): Observable<any> {
    return this.api.post('equipment/delete_equipments', payload);
  }


  public existProject(payload): Observable<any> {
    return this.api.post('project/exist_project', payload);
  }

  public addGate(payload): Observable<any> {
    return this.api.post('gates/add_gates', payload);
  }

  public updateGate(payload): Observable<any> {
    return this.api.post('gates/update_gates', payload);
  }

  public inviteMembers(payload): Observable<any> {
    return this.api.post('member/invite_member', payload);
  }

  public editMember(payload): Observable<any> {
    return this.api.post('member/edit_member', payload);
  }

  public updateMember(payload): Observable<any> {
    return this.api.post('member/update_member', payload);
  }


  public resendInviteLink(data): Observable<any> {
    return this.api.post('member/resend_invite_link', data);
  }

  public deleteMember(payload): Observable<any> {
    return this.api.post('member/delete_member', payload);
  }

  public getRoles(): Observable<any> {
    return this.api.get('member/get_roles');
  }

  public getCompanies(params): Observable<any> {
    return this.api.get(`company/get_newcompanies/${params.ProjectId}/${params.ParentCompanyId}`);
  }


  public getDefinableWork(params): Observable<any> {
    return this.api.get(`company/get_definable_work/${params.ProjectId}/${params.ParentCompanyId}`);
  }

  public getLocations(params): Observable<any> {
    return this.api.get('location/get_locations', params);
  }

  public gateList(params, payload): Observable<any> {
    return this.api.post(`gates/gate_list/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.ParentCompanyId}`, payload);
  }

  public unReadCount(params): Observable<any> {
    return this.api.get(`notification/unread_count/?ProjectId=${params.ProjectId}&ParentCompanyId=${params.ParentCompanyId}`);
  }

  public upgradePlan(): Observable<any> {
    return this.api.get('payment/get_session_url');
  }

  public checkout(payload): Observable<any> {
    return this.api.post('payment/checkout', payload);
  }

  public checkoutSession(payload): Observable<any> {
    return this.api.post('payment/create_checkout_session', payload);
  }

  public versionUpdate(payload): Observable<any> {
    return this.api.post('notification/version_update', payload);
  }

  public exportreport(params, payload): Observable<any> {
    return this.api.getExcel1(`delivery_reports/exportreports/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`, payload);
  }

  public updateProjectSharingSettings(payload): Observable<any> {
    return this.api.put('project/project_sharing_settings', payload);
  }

  public uploadProjectLogisticPlanUrl(params, payload): Observable<any> {
    return this.api.requestImage(`project/upload_logistic_plan/${params.ProjectId}`, payload);
  }

  public decodeProjectDetailUrl(payload): Observable<any> {
    return this.api.put('project/decode_project_detail_url', payload);
  }

  public updateLogisticPlanSettings(payload): Observable<any> {
    return this.api.put('project/update_dashboard_logistic_plan', payload);
  }

  public getRegisteredMember(params): Observable<any> {
    return this.api.get(`member/list_registered_members/${params.ProjectId}/${params.ParentCompanyId}`);
  }

  public listGuestMembers(params, payload): Observable<any> {
    return this.api.post(`member/list_guest_member/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.ParentCompanyId}`, payload);
  }

  public notifyProjectSelected(): void {
    this.projectSelected.next(true);
  }
}
