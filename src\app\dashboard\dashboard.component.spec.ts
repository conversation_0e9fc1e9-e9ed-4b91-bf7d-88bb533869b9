import { DashboardComponent } from './dashboard.component';
import { of, throwError } from 'rxjs';

// Mocks for all services and dependencies
const dashboardServiceMock = {
  getDashboardData: jest.fn(() => of({ data: {} })),
  getGraphDelivery: jest.fn(() => of({ data: { count: [] } })),
  getCraneRequestGraphData: jest.fn(() => of({ data: { count: [] } })),
  getConcreteRequestGraphData: jest.fn(() => of({ data: { count: [] } })),
  getInspectionRequestGraphData: jest.fn(() => of({ data: { count: [] } })),
};
const projectServiceMock = {
  projectSelected: of({}),
  projectParent: of({ ProjectId: 1, ParentCompanyId: 2 }),
  companyList: of([1, 2]),
  ParentCompanyId: of(2),
  clearProject: of({ status: true }),
  uploadProjectLogisticPlanUrl: jest.fn(() => of({ data: { fileUrl: 'url' }, message: 'success' })),
  siteplanUploaded: of({}),
  isAccountAdmin: of(true),
};
const deliveryServiceMock = {
  loginUser: of({ RoleId: 1 }),
  getUpcomingList: jest.fn(() => of({ data: [] })),
  refresh: of({}),
  refresh1: of({}),
  inspectionUpdated: of({}),
  inspectionUpdated1: of({}),
  fetchData: of({}),
  fetchData1: of({}),
};
const projectSettingsServiceMock = {
  getProjectSettings: jest.fn(() => of({ data: { projectSettings: { isPdfUploaded: true, projectLogisticPlanUrl: 'url', fileExtension: 'pdf', sitePlanStatus: 'status', pdfToImageLinks: ['img1', 'img2'] } } })),
};
const profileServiceMock = {
  getOverView: jest.fn(() => of({ data: { RoleId: 1 } })),
};
const routerMock = { navigate: jest.fn() };
const titleServiceMock = { setTitle: jest.fn() };
const modalServiceMock = { show: jest.fn(() => ({ content: {}, hide: jest.fn() })) };
const toastrMock = { success: jest.fn(), error: jest.fn() };

// Helper for simulating DOM
let createElementSpy: jest.SpyInstance;
let querySelectorSpy: jest.SpyInstance;
let clipboardWriteTextSpy: jest.SpyInstance;

beforeAll(() => {
  createElementSpy = jest.spyOn(document, 'createElement').mockImplementation(() => ({
    click: jest.fn(),
    setAttribute: jest.fn(),
  }) as any);
  querySelectorSpy = jest.spyOn(document, 'querySelector').mockImplementation(() => ({ style: { transform: '' } }) as any);
  if (!navigator.clipboard) {
    (navigator as any).clipboard = {};
  }
  clipboardWriteTextSpy = jest.spyOn(navigator.clipboard, 'writeText').mockImplementation(() => Promise.resolve());
});
afterAll(() => {
  createElementSpy.mockRestore();
  querySelectorSpy.mockRestore();
  clipboardWriteTextSpy.mockRestore();
});

function createComponent() {
  return new DashboardComponent(
    dashboardServiceMock as any,
    projectServiceMock as any,
    deliveryServiceMock as any,
    projectSettingsServiceMock as any,
    profileServiceMock as any,
    routerMock as any,
    titleServiceMock as any,
    modalServiceMock as any,
    toastrMock as any
  );
}

describe('DashboardComponent', () => {
  let component: DashboardComponent;

  beforeEach(() => {
    component = createComponent();
    component.ProjectId = 1;
    component.ParentCompanyId = 2;
    component.authUser = { RoleId: 1 };
    component.myAccount = false;
    component.logisticPlan = { nativeElement: { value: null } };
    component.arrayValues = ['img1', 'img2'];
    component.currentIndex = 0;
    component.selectedSitePlan = 'img1';
    component.pageNo = 1;
    component.sitePlanLoader = false;
    component.isImageExpanded = false;
    component.scale = 1.0;
    component.offsetX = 0;
    component.offsetY = 0;
    component.zoomLevel = 1;
    component.barChartOne = {};
    component.barChartTwo = {};
    component.barChartThree = {};
    component.yearValue = 'currentyear';
    component.formData = {};
    component.roleId = 1;
    component.isAdmin = true;
    component.isResponse = false;
    component.showSearchbar = false;
    component.search = null;
    component.showSitePlan = true;
    component.sitePlanUploadStatus = null;
    component.inspectiponGraphData = [];
    component.filterYearWise3 = [];
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should toggle image size', () => {
    component.isImageExpanded = false;
    component.toggleImageSize();
    expect(component.isImageExpanded).toBe(true);
  });

  it('should change year and call getGraphDelivery', () => {
    const spy = jest.spyOn(component, 'getGraphDelivery');
    component.changeYear('nextyear');
    expect(component.yearValue).toBe('nextyear');
    expect(spy).toHaveBeenCalled();
  });

  it('should redirect if not myAccount', () => {
    component.myAccount = false;
    component.redirect('test');
    expect(routerMock.navigate).toHaveBeenCalledWith(['/test']);
  });

  it('should not redirect if myAccount', () => {
    component.myAccount = true;
    component.redirect('test');
    expect(routerMock.navigate).not.toHaveBeenCalledWith(['/test']);
  });

  it('should call getDashboardData and handle success', () => {
    component.myAccount = false;
    component.ProjectId = 1;
    component.ParentCompanyId = 2;
    component.authUser = { RoleId: 1 };
    component.getDashboardData();
    expect(component.loader).toBe(false);
  });

  it('should call getDashboardData and handle error', () => {
    dashboardServiceMock.getDashboardData.mockReturnValueOnce(throwError(() => new Error('fail')));
    component.myAccount = false;
    component.ProjectId = 1;
    component.ParentCompanyId = 2;
    component.authUser = { RoleId: 1 };
    component.getDashboardData();
    expect(toastrMock.error).toHaveBeenCalled();
  });

  it('should call getUpcomingList', () => {
    component.ProjectId = 1;
    component.ParentCompanyId = 2;
    component.getUpcomingList();
    expect(component.upcomingListLoader).toBe(false);
  });

  it('should openCraneDetailView for craneRequest', () => {
    const data = { CraneRequestId: 1, ProjectId: 1, requestType: 'craneRequest', id: 1, ConcreteRequestId: 1 };
    component.openCraneDetailView(data);
    expect(modalServiceMock.show).toHaveBeenCalled();
  });

  it('should openCraneDetailView for deliveryRequest', () => {
    const data = { CraneRequestId: 1, ProjectId: 1, requestType: 'deliveryRequest', id: 1, ConcreteRequestId: 1 };
    component.openCraneDetailView(data);
    expect(modalServiceMock.show).toHaveBeenCalled();
  });

  it('should openCraneDetailView for concreteRequest', () => {
    const data = { CraneRequestId: 1, ProjectId: 1, requestType: 'concreteRequest', id: 1, ConcreteRequestId: 1 };
    component.openCraneDetailView(data);
    expect(modalServiceMock.show).toHaveBeenCalled();
  });

  it('should openCraneDetailView for inspectionRequest', () => {
    const data = { CraneRequestId: 1, ProjectId: 1, requestType: 'inspectionRequest', id: 1, ConcreteRequestId: 1 };
    component.openCraneDetailView(data);
    expect(modalServiceMock.show).toHaveBeenCalled();
  });

  it('should call getGraphDelivery and processGraphData', () => {
    component.ProjectId = 1;
    component.ParentCompanyId = 2;
    component.getGraphDelivery();
    expect(component.barChartOne).toBeDefined();
  });

  it('should call getLogisticPlan and handle pdf', () => {
    component.ProjectId = 1;
    component.ParentCompanyId = 2;
    component.getLogisticPlan();
    expect(component.isPdfPlan).toBe(true);
    expect(component.selectedSitePlan).toBe('img1');
  });

  it('should call downloadAndReuploadFile and handle error', async () => {
    (globalThis as any).fetch = jest.fn(() => Promise.resolve({ ok: false }));
    await component.downloadAndReuploadFile('uri', 'file.pdf');
    expect(toastrMock.error).toHaveBeenCalled();
  });

  it('should call downloadAndReuploadFile and handle success', async () => {
    const blob = new Blob(['test'], { type: 'application/pdf' });
    (globalThis as any).fetch = jest.fn(() => Promise.resolve({ ok: true, blob: () => Promise.resolve(blob) }));
    const fileSpy = jest.spyOn(window, 'File').mockImplementation(() => blob as any);
    const onPlanSelectedSpy = jest.spyOn(component, 'onPlanSelected').mockImplementation();
    await component.downloadAndReuploadFile('uri', 'file.pdf');
    expect(onPlanSelectedSpy).toHaveBeenCalled();
    fileSpy.mockRestore();
  });

  it('should call downloadProxyFile', () => {
    const spy = jest.spyOn(component, 'downloadAndReuploadFile').mockImplementation();
    component.downloadProxyFile('logisticPlan', 'file.pdf');
    expect(spy).toHaveBeenCalled();
  });

  it('should call onPlanSelected for valid file', () => {
    const file = { name: 'test.pdf' };
    component.ProjectId = 1;
    component.formData = { append: jest.fn() };
    component.projectService = projectServiceMock as any;
    component.logisticPlan = { nativeElement: { value: null } };
    component.onPlanSelected([file]);
    expect(toastrMock.success).toHaveBeenCalled();
  });

  it('should call onPlanSelected for invalid file', () => {
    const file = { name: 'test.txt' };
    component.onPlanSelected([file]);
    expect(toastrMock.error).toHaveBeenCalled();
  });

  it('should call downloadFile', () => {
    component.logisticPlan = 'uri';
    component.downloadFile();
    expect(document.createElement).toHaveBeenCalledWith('a');
  });

  it('should call copyLink and handle success', async () => {
    await component.copyLink('url');
    expect(toastrMock.success).toHaveBeenCalled();
  });

  it('should call copyLink and handle error', async () => {
    navigator.clipboard.writeText = jest.fn(() => Promise.reject('fail'));
    await component.copyLink('url');
    expect(toastrMock.error).toHaveBeenCalled();
  });

  it('should zoom in and out', () => {
    component.scale = 1.0;
    component.maxScale = 2.0;
    component.zoomIn();
    expect(component.scale).toBeGreaterThan(1.0);
    component.minScale = 1.0;
    component.zoomOut();
    expect(component.scale).toBeLessThanOrEqual(1.0);
  });

  it('should zoom in1 and out2', () => {
    component.scale = 1.0;
    component.maxScale = 2.0;
    component.zoomIn1();
    expect(component.scale).toBeGreaterThan(1.0);
    component.minScale = 1.0;
    component.zoomOut2();
    expect(component.scale).toBeLessThanOrEqual(1.0);
  });

  it('should updateImageScale', () => {
    component.scale = 2.0;
    component.updateImageScale();
    expect(document.querySelector).toHaveBeenCalled();
  });

  it('should updateImageScale2', () => {
    component.scale = 2.0;
    component.updateImageScale2();
    expect(document.querySelector).toHaveBeenCalled();
  });

  it('should zoomInPdf and zoomOutPdf', () => {
    component.zoomLevel = 1;
    component.zoomInPdf();
    expect(component.zoomLevel).toBeGreaterThan(1);
    component.zoomOutPdf();
    expect(component.zoomLevel).toBeLessThanOrEqual(1.1);
  });

  it('should navigateProjectSettings', () => {
    component.navigateProjectSettings();
    expect(routerMock.navigate).toHaveBeenCalledWith(['/project-settings']);
  });

  it('should getMemberInfo and set isAdmin', () => {
    component.ProjectId = 1;
    component.ParentCompanyId = 2;
    component.profileService = profileServiceMock as any;
    component.getMemberInfo();
    expect(component.isAdmin).toBe(true);
  });

  it('should getNextValue and preload image', async () => {
    component.arrayValues = ['img1', 'img2'];
    component.currentIndex = 0;
    component.selectedSitePlan = 'img1';
    component.preloadImage = jest.fn(() => Promise.resolve());
    await component.getNextValue();
    expect(component.sitePlanLoader).toBe(false);
  });

  it('should getPreviousValue and preload image', async () => {
    component.arrayValues = ['img1', 'img2'];
    component.currentIndex = 1;
    component.selectedSitePlan = 'img2';
    component.preloadImage = jest.fn(() => Promise.resolve());
    await component.getPreviousValue();
    expect(component.sitePlanLoader).toBe(false);
  });

  it('should getSitePlanAtIndex and preload image', async () => {
    component.arrayValues = ['img1', 'img2'];
    component.currentIndex = 0;
    component.selectedSitePlan = 'img1';
    component.preloadImage = jest.fn(() => Promise.resolve());
    await component.getSitePlanAtIndex(1);
    expect(component.sitePlanLoader).toBe(false);
  });

  it('should only allow numbers', () => {
    const event = { which: 48, keyCode: 48 } as KeyboardEvent;
    expect(component.onlyNumbers(event)).toBe(true);
    const event2 = { which: 65, keyCode: 65 } as KeyboardEvent;
    expect(component.onlyNumbers(event2)).toBe(false);
  });

  it('should handle onFocusOut', () => {
    component.currentIndex = 2;
    component.onFocusOut({} as Event);
    expect(component.pageNo).toBe(3);
  });

  it('should clear search', () => {
    component.showSearchbar = true;
    component.search = 'test';
    component.clear();
    expect(component.showSearchbar).toBe(false);
    expect(component.search).toBe(null);
  });

  it('should handle handleDownKeydown for redirect', () => {
    const event = { key: 'Enter', preventDefault: jest.fn() } as any;
    const spy = jest.spyOn(component, 'redirect');
    component.handleDownKeydown(event, 'data', 'redirect');
    expect(spy).toHaveBeenCalled();
  });

  it('should handle handleDownKeydown for previous', () => {
    const event = { key: ' ', preventDefault: jest.fn() } as any;
    const spy = jest.spyOn(component, 'getPreviousValue').mockImplementation();
    component.handleDownKeydown(event, 'data', 'previous');
    expect(spy).toHaveBeenCalled();
  });

  it('should handle handleDownKeydown for next', () => {
    const event = { key: 'Enter', preventDefault: jest.fn() } as any;
    const spy = jest.spyOn(component, 'getNextValue').mockImplementation();
    component.handleDownKeydown(event, 'data', 'next');
    expect(spy).toHaveBeenCalled();
  });

  it('should handle handleDownKeydown for toggle', () => {
    const event = { key: ' ', preventDefault: jest.fn() } as any;
    const spy = jest.spyOn(component, 'toggleImageSize').mockImplementation();
    component.handleDownKeydown(event, 'data', 'toggle');
    expect(spy).toHaveBeenCalled();
  });

  it('should handle handleDownKeydown for download', () => {
    const event = { key: 'Enter', preventDefault: jest.fn() } as any;
    const spy = jest.spyOn(component, 'downloadFile').mockImplementation();
    component.handleDownKeydown(event, 'data', 'download');
    expect(spy).toHaveBeenCalled();
  });

  it('should handle handleDownKeydown for copy', () => {
    const event = { key: ' ', preventDefault: jest.fn() } as any;
    const spy = jest.spyOn(component, 'copyLink').mockImplementation();
    component.handleDownKeydown(event, 'data', 'copy');
    expect(spy).toHaveBeenCalled();
  });

  it('should handle handleDownKeydown for zoomin', () => {
    const event = { key: 'Enter', preventDefault: jest.fn() } as any;
    const spy = jest.spyOn(component, 'zoomIn').mockImplementation();
    component.handleDownKeydown(event, 'data', 'zoomin');
    expect(spy).toHaveBeenCalled();
  });

  it('should handle handleDownKeydown for zoomout', () => {
    const event = { key: ' ', preventDefault: jest.fn() } as any;
    const spy = jest.spyOn(component, 'zoomOut').mockImplementation();
    component.handleDownKeydown(event, 'data', 'zoomout');
    expect(spy).toHaveBeenCalled();
  });

  it('should handle handleDownKeydown for zoomin1', () => {
    const event = { key: 'Enter', preventDefault: jest.fn() } as any;
    const spy = jest.spyOn(component, 'zoomIn1').mockImplementation();
    component.handleDownKeydown(event, 'data', 'zoomin1');
    expect(spy).toHaveBeenCalled();
  });

  it('should handle handleDownKeydown for zoomout2', () => {
    const event = { key: ' ', preventDefault: jest.fn() } as any;
    const spy = jest.spyOn(component, 'zoomOut2').mockImplementation();
    component.handleDownKeydown(event, 'data', 'zoomout2');
    expect(spy).toHaveBeenCalled();
  });

  it('should handle handleDownKeydown for default', () => {
    const event = { key: 'Enter', preventDefault: jest.fn() } as any;
    component.handleDownKeydown(event, 'data', 'unknown');
    expect(event.preventDefault).toHaveBeenCalled();
  });

  it('should handle onMouseDown and onMouseUp', () => {
    component.scale = 2.0;
    const event = { clientX: 10, clientY: 20 } as MouseEvent;
    component.onMouseDown(event);
    expect(component.isDragging).toBe(true);
    component.onMouseUp(event);
    expect(component.isDragging).toBe(false);
  });
});
