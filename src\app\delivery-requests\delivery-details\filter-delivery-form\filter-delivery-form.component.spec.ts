import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ModalModule, BsModalRef } from 'ngx-bootstrap/modal';
import { FilterDeliveryFormComponent } from './filter-delivery-form.component';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';
import { of } from 'rxjs';

describe('FilterDeliveryFormComponent', () => {
  let component: FilterDeliveryFormComponent;
  let fixture: ComponentFixture<FilterDeliveryFormComponent>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let projectService: jest.Mocked<ProjectService>;
  let modalRef: jest.Mocked<BsModalRef>;

  const mockProjectService = {
    projectId: of('123'),
    projectParent: of({ ProjectId: '123', ParentCompanyId: '456' }),
    accountProjectParent: of({ ProjectId: '123', ParentCompanyId: '456' }),
    listEquipment: jest.fn().mockReturnValue(of({ data: [] })),
    getCompanies: jest.fn().mockReturnValue(of({ data: [] }))
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [FilterDeliveryFormComponent],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        HttpClientTestingModule,
        RouterTestingModule,
        ModalModule.forRoot(),
      ],
      providers: [
        { provide: BsModalRef, useValue: { hide: jest.fn() } },
        { provide: DeliveryService, useValue: { } },
        { provide: ProjectService, useValue: mockProjectService }
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(FilterDeliveryFormComponent);
    component = fixture.componentInstance;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    modalRef = TestBed.inject(BsModalRef) as jest.Mocked<BsModalRef>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize filter form with empty values', () => {
    expect(component.filterForm).toBeTruthy();
    expect(component.filterForm.get('companyFilter')?.value).toBe('');
    expect(component.filterForm.get('descriptionFilter')?.value).toBe('');
    expect(component.filterForm.get('statusFilter')?.value).toBe('');
    expect(component.filterForm.get('memberFilter')?.value).toBe('');
    expect(component.filterForm.get('gateFilter')?.value).toBe('');
    expect(component.filterForm.get('equipmentFilter')?.value).toBe('');
  });

  it('should update ProjectId and ParentCompanyId when projectParent emits', () => {
    expect(component.ProjectId).toBe('123');
    expect(component.ParentCompanyId).toBe('456');
  });

  it('should reset form and hide modal when resetFilter is called', () => {
    // Set some values in the form
    component.filterForm.patchValue({
      companyFilter: '1',
      descriptionFilter: 'test',
      statusFilter: 'Approved'
    });

    // Call resetFilter
    component.resetFilter();

    // Check if form is reset
    expect(component.filterForm.get('companyFilter')?.value).toBe('');
    expect(component.filterForm.get('descriptionFilter')?.value).toBe('');
    expect(component.filterForm.get('statusFilter')?.value).toBe('');

    // Check if modal is hidden
    expect(modalRef.hide).toHaveBeenCalled();
  });

  it('should hide modal when filterSubmit is called', () => {
    component.filterSubmit();
    expect(modalRef.hide).toHaveBeenCalled();
  });

  it('should get equipment list when getOverAllEquipment is called', () => {
    const mockEquipmentData = { data: [{ id: 1, equipmentName: 'Test Equipment' }] };
    projectService.listEquipment.mockReturnValue(of(mockEquipmentData));

    component.getOverAllEquipment();

    expect(projectService.listEquipment).toHaveBeenCalledWith({
      ProjectId: component.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: component.ParentCompanyId
    }, { isFilter: true, showActivatedAlone: true });
    expect(component.equipmentList).toEqual(mockEquipmentData.data);
  });

  it('should get company list when getCompany is called', () => {
    const mockCompanyData = { data: [{ id: 1, companyName: 'Test Company' }] };
    projectService.getCompanies.mockReturnValue(of(mockCompanyData));

    component.getCompany();

    expect(projectService.getCompanies).toHaveBeenCalledWith({
      ProjectId: component.ProjectId
    });
    expect(component.companyList).toEqual(mockCompanyData.data);
  });
});
