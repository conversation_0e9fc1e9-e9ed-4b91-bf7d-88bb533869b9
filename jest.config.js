module.exports = {
  preset: 'jest-preset-angular',
  setupFilesAfterEnv: ['<rootDir>/setup-jest.ts'],
  testEnvironment: 'jsdom',
  globals: {
    'ts-jest': {
      tsconfig: '<rootDir>/src/tsconfig.spec.json',
      stringifyContentPathRegex: '\\.html$',
    },
  },
  transform: {
    '^.+\\.(ts|js|mjs|html)$': ['jest-preset-angular', {
      tsconfig: '<rootDir>/src/tsconfig.spec.json',
      stringifyContentPathRegex: '\\.html$',
      useESM: true,
    }],
  },
  transformIgnorePatterns: [
    'node_modules/(?!(@angular|@fullcalendar|preact|rxjs|bn-ng-idle|ngx-bootstrap|ngx-toastr|ngx-socket-io|ng-multiselect-dropdown|angular-user-idle|ngx-pagination|ngx-file-drop|ngx-chips|ng2-material-dropdown|ngx-mask)/)'
  ],
  moduleFileExtensions: ['ts', 'html', 'js', 'json', 'mjs'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^src/(.*)$': '<rootDir>/src/$1',
    '^src/app/(.*)$': '<rootDir>/src/app/$1'
  },
  testMatch: ['**/+(*.)+(spec).+(ts)'],
  collectCoverage: true,
  coverageDirectory: 'coverage',
  collectCoverageFrom: [
    "src/app/**/*.ts",
    "!src/app/**/*.spec.ts",
    "!src/main.ts",
    "!src/environments/**"
  ],
};
