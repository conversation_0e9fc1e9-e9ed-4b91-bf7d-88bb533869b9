import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { ProjectService } from '../services/profile/project.service';

@Component({
  selector: 'app-override-request',
  templateUrl: './override-request.component.html',

  })
export class OverrideRequestComponent implements OnInit {
  public overRideDetailsForm: UntypedFormGroup;

  public ProjectId: any;

  public submitted = false;

  public formSubmitted = false;


  public constructor(
    public projectService: ProjectService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly toastr: ToastrService,
  ) {
    this.projectService.projectId.subscribe((res): void => {
      this.ProjectId = res;
    });
    this.overRideForm();
  }

  public ngOnInit(): void { /* */ }


  public overRideForm(): void {
    this.overRideDetailsForm = this.formBuilder.group(
      {
        comment: [
          '',
          Validators.compose([
            Validators.required,
          ]),
        ],
      },
    );
  }

  public checkStringEmptyValues(formValue): boolean {
    if (formValue.comment.trim() === '') {
      return true;
    }
    return false;
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    if (this.overRideDetailsForm.invalid) {
      this.formSubmitted = false;

      return;
    }
    const formValue = this.overRideDetailsForm.value;
    if (!this.checkStringEmptyValues(formValue)) {
      const payload = {
        comment: formValue.comment.trim(),
        ProjectId: this.ProjectId,
      };
      this.projectService.applyOverRide(payload).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.submitted = false;
            this.formSubmitted = false;
            this.overRideDetailsForm.reset();
            this.overRideDetailsForm.reset();
          }
        },
        error: (overRideDetailsFormError): void => {
          this.overRideDetailsForm.reset();
          if (overRideDetailsFormError.message?.statusCode === 400) {
            this.overRideShowError(overRideDetailsFormError);
          } else {
            this.toastr.error(overRideDetailsFormError.message, 'OOPS!');
            this.submitted = false;
          }
        },
      });
    } else {
      this.toastr.error('Please enter valid comment!', 'OOPS!');
      this.submitted = false;
      this.formSubmitted = false;
    }
  }

  public overRideShowError(err): void {
    let overRideErrorMessage: any = '';
    overRideErrorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(overRideErrorMessage);
  }
}
