import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AppLayoutComponent } from './layout/app-layout/app-layout.component';
import { MainLayoutComponent } from './layout/main-layout/main-layout.component';
import { FullCalenderComponent } from './layout/full-calender/full-calender.component';
import { TimeslotComponent } from './layout/time-slot/time-slot.component';
import { LoginComponent } from './auth/login/login.component';
import { ForgotPasswordComponent } from './auth/forgot-password/forgot-password.component';
import { ResetPasswordComponent } from './auth/reset-password/reset-password.component';
import { DashboardComponent } from './dashboard/dashboard.component';
import { HomePageComponent } from './home-page/home-page.component';
import { DeliveryRequestsComponent } from './delivery-requests/delivery-requests.component';
import { InspectionRequestComponent } from './inspection-request/inspection-request.component'
import { CalendarComponent } from './calendar/calendar.component';
import { MembersComponent } from './members/members.component';
import { GatesComponent } from './gates/gates.component';
import { EquipmentsComponent } from './equipments/equipments.component';
import { EquipmentLogComponent } from './carbon-tracking/equipment-log/equipment-log.component';
import { WasteLogComponent } from './carbon-tracking/waste-log/waste-log.component';
import { UtilitiesLogComponent } from './carbon-tracking/utilities/utilities.component';
import { HaulingLogComponent } from './carbon-tracking/hauling-log/hauling-log.component';
import { ChoosePlansComponent } from './choose-plans/choose-plans.component';
import { PayementComponent } from './payement/payement.component';
import { SettingsComponent } from './settings/settings.component';
import { CompaniesComponent } from './companies/companies.component';
import { AuthGuard } from './auth.guard';
import { ProfileComponent } from './profile/profile.component';
import { DFOWComponent } from './dfow/dfow.component';
import { VoidListComponent } from './void-list/void-list.component';
import { NotificationComponent } from './notification/notification.component';
import { ProjectAccountAdminComponent } from './project-account-admin/project-account-admin.component';
import { BillingComponent } from './billing/billing.component';
import { CraneCalendarComponent } from './crane-calendar/crane-calendar.component';
import { InspectionCalendarComponent } from './inspection-calender/inspection-calender.component';
import { AllCalendarComponent } from './all-calender/all-calender.component';
import { CraneRequestGridComponent } from './crane-requests/crane-request-grid/crane-request-grid.component';
import { QueuedDeliveryRequestComponent } from './delivery-requests/queued-delivery-request/queued-delivery-request.component';
import { CalendarViewComponent } from './calendar-settings/calendar-view/calendar-view.component';
import { NotificationSettingsComponent } from './notification-settings/notification-settings.component';
import { ConcreteCalendarComponent } from './concrete-request/concrete-calendar/concrete-calendar.component';
import { ConcreteRequestsGridComponent } from './concrete-request/concrete-requests-grid/concrete-requests-grid.component';
import { ReportsComponent } from './reports/reports.component';
import { DeliveriesComponent } from './reports/deliveries/deliveries.component';
import { CraneComponent } from './reports/crane/crane.component';
import { ConcreteComponent } from './reports/concrete/concrete.component';
import { HeatmapComponent } from './reports/heat-map/heat-map.component';
import { WeeklycalendarComponent } from './reports/weekly-calendar/weekly-calendar.component';
import { ProjectSettingsComponent } from './project-settings/project-settings.component';
import { FullCalendarRenderComponent } from './full-calendar-render/full-calendar-render.component';
import { SubmitBookComponent } from './project-settings/guest-user/submit-book/submit-book.component';
import { InviteLinkComponent } from './project-settings/guest-user/invite-link/invite-link.component';
import { DeliveryCalendarComponent } from './project-settings/guest-user/delivery-calendar/delivery-calendar.component';
import { EnterDetailsComponent } from './project-settings/guest-user/enter-details/enter-details.component';
import { GuestuserCalendarComponent } from './project-settings/guest-user/guestuser-calendar/guestuser-calendar.component';
import { GuestuserCranecalendarComponent } from './project-settings/guest-user/guestuser-cranecalendar/guestuser-cranecalendar.component';
import { GuestuserConcretecalendarComponent } from './project-settings/guest-user/guestuser-concretecalendar/guestuser-concretecalendar.component';
import { ProjectDetailComponent } from './project-settings/guest-user/project-detail/project-detail.component';
import { LocationComponent } from './location/location.component';
import { GuestDeliveryBookingComponent } from './project-settings/guest-user/delivery/guest-delivery-booking/guest-delivery-booking.component';
import { GuestCraneBookingComponent } from './project-settings/guest-user/crane/guest-crane-booking/guest-crane-booking.component';
import { GuestConcreteBookingComponent } from './project-settings/guest-user/concrete/guest-concrete-booking/guest-concrete-booking.component';
import { EditDeliveryBookingComponent } from './project-settings/guest-user/delivery/edit-delivery-booking/edit-delivery-booking.component';
import { EditCraneBookingComponent } from './project-settings/guest-user/crane/edit-crane-booking/edit-crane-booking.component';
import { EditConcreteBookingComponent } from './project-settings/guest-user/concrete/edit-concrete-booking/edit-concrete-booking.component';
import { MembersSettingsComponent } from './members/members-settings/members-settings.component';
import { GuestmembersComponent } from './members/guestmembers/guestmembers.component';
import { TemplateGridComponent } from './booking-templates/templates-grid/template-grid.component';
import { InspectionComponent } from './reports/inspection/inspection.component';
import { CarbonTrackingComponent } from './carbon-tracking/carbon-tracking/carbon-tracking.component';


export const routes: Routes = [
  {
    path: '', redirectTo: '/home', pathMatch: 'full', title: 'Home',
  },
  { path: 'login', component: LoginComponent, title: 'Login' },
  { path: 'forgot-password', component: ForgotPasswordComponent, title: 'ForgotPassword' },
  { path: 'reset-password/:id', component: ResetPasswordComponent, title: 'ResetPassword' },
  { path: 'payment', component: PayementComponent, title: 'Payment' },
  { path: 'account_admin_payment', component: PayementComponent, title: 'Account Admin Payment' },
  { path: 'upgradepayment/:id', component: PayementComponent, title: 'Upgrade Payment' },
  // { path: 'thankyou', component: ThankYouComponent },
  { path: 'plans', component: ChoosePlansComponent, title: 'Choose Plans' },
  {
    path: '',
    component: MainLayoutComponent,
    title: 'MainLayout',
    children: [
      { path: 'home', component: HomePageComponent, title: 'Home' },
      {
        path: 'invite-member/:memberId/:ParentCompanyId/:email/:domainName',
        component: HomePageComponent,
        title: 'Invite Member',
      },
      { path: 'plans', component: ChoosePlansComponent, title: 'Choose Plans' },
      { path: 'upgradeplans/:id', component: ChoosePlansComponent, title: 'Upgrade Plans' },
      { path: 'register-project', component: HomePageComponent, title: 'Register Project' },
    ],
  },
  {
    path: '',
    component: FullCalenderComponent,
    title: 'FullCalender',
    children: [{ path: 'fc/:projectId', component: FullCalendarRenderComponent, title: 'FullCalender' }],
  },
  {
    path: '',
    component: InviteLinkComponent,
    title: 'Invite Link',
    children: [{ path: 'invite-link', component: InviteLinkComponent, title: 'Invite Link' }],
  },
  {
    path: '',
    component: ProjectDetailComponent,
    title: 'Project Detail',
    children: [
      { path: 'project-detail/:projectRandomString', component: ProjectDetailComponent, title: 'Project Detail' },
    ],
  },
  {
    path: '',
    component: SubmitBookComponent,
    title: 'Submit Book',
    children: [{ path: 'submit-book', component: SubmitBookComponent, title: 'Submit Book' }],
  },
  {
    path: '',
    component: GuestuserCalendarComponent,
    title: 'Guestuser Calendar',
    children: [
      { path: 'guest-delivery-calendar', component: DeliveryCalendarComponent, title: 'Guestuser Delivery Calendar' },
      { path: 'guest-crane-calendar', component: GuestuserCranecalendarComponent, title: 'Guestuser Crane Calendar' },
      { path: 'guest-concrete-calendar', component: GuestuserConcretecalendarComponent, title: 'Guestuser Concrete Calendar' },
    ],
  },

  {
    path: '',
    component: EnterDetailsComponent,
    title: 'Enter Details',
    children: [{ path: 'enter-details', component: EnterDetailsComponent, title: 'Enter Details' }],
  },
  {
    path: '',
    component: GuestDeliveryBookingComponent,
    title: 'Guest Delivery Booking',
    children: [{ path: 'guest-delivery-booking', component: GuestDeliveryBookingComponent, title: 'Guest Delivery Booking' }],
  },
  {
    path: '',
    component: GuestCraneBookingComponent,
    title: 'Guest Crane Booking',
    children: [{ path: 'guest-crane-booking', component: GuestCraneBookingComponent, title: 'Guest Crane Booking' }],
  },
  {
    path: '',
    component: GuestConcreteBookingComponent,
    title: 'Guest Concrete Booking',
    children: [{ path: 'guest-concrete-booking', component: GuestConcreteBookingComponent, title: 'Guest Concrete Booking' }],
  },
  {
    path: '',
    component: EditDeliveryBookingComponent,
    title: 'Guest Edit Delivery Booking',
    children: [{ path: 'guest-edit-delivery-booking', component: EditDeliveryBookingComponent, title: 'Guest Edit Delivery Booking' }],
  },
  {
    path: '',
    component: EditCraneBookingComponent,
    title: 'Guest Edit Crane Booking',
    children: [{ path: 'guest-edit-crane-booking', component: EditCraneBookingComponent, title: 'Guest Edit Crane Booking' }],
  },
  {
    path: '',
    component: EditConcreteBookingComponent,
    title: 'Guest Edit Concrete Booking',
    children: [{ path: 'guest-edit-concrete-booking', component: EditConcreteBookingComponent, title: 'Guest Edit Concrete Booking' }],
  },
  {
    path: '',
    component: GuestmembersComponent,
    title: 'Guest Members',
    children: [{ path: 'guest-members', component: GuestmembersComponent, title: 'Guest Members' }],
  },
  {
    path: '',
    component: AppLayoutComponent,
    title: 'AppLayout',
    children: [
      {
        path: 'dashboard', component: DashboardComponent, canActivate: [AuthGuard], title: 'Dashboard',
      },
      {
        path: 'projects', component: ProjectAccountAdminComponent, canActivate: [AuthGuard], title: 'Projects',
      },
      {
        path: 'billing', component: BillingComponent, canActivate: [AuthGuard], title: 'Billing',
      },
      {
        path: 'delivery-request', component: DeliveryRequestsComponent, canActivate: [AuthGuard], title: 'Delivery Request',
      },
      { path : 'inspection-request' , component: InspectionRequestComponent , canActivate: [AuthGuard], title: 'Inspection Request'},
      {
        path: 'detail-delivery-request/:deliveryId',
        component: DeliveryRequestsComponent,
        canActivate: [AuthGuard],
        title: 'Delivery Request Detail',
      },
      {
        path: 'calendar', component: CalendarComponent, canActivate: [AuthGuard], title: 'Calendar',
      },
      {
        path: 'members', component: MembersComponent, canActivate: [AuthGuard], title: 'Members',
      },
      {
        path: 'members-settings', component: MembersSettingsComponent, canActivate: [AuthGuard], title: 'Members Settings',
      },
      {
        path: 'gates', component: GatesComponent, canActivate: [AuthGuard], title: 'Gates',
      },
      {
        path: 'equipments', component: EquipmentsComponent, canActivate: [AuthGuard], title: 'Equipments',
      },
      {
        path: 'carbon-dashboard', component: CarbonTrackingComponent, canActivate: [AuthGuard], title: 'Carbon Dashboard',
      },
      {
        path: 'equipment-log', component: EquipmentLogComponent, canActivate: [AuthGuard], title: 'Equipment Log',
      },
      {
        path: 'waste-log', component: WasteLogComponent, canActivate: [AuthGuard], title: 'Waste Log',
      },
      {
        path: 'utilities-log', component: UtilitiesLogComponent, canActivate: [AuthGuard], title: 'Utilities Log',
      },
      {
        path: 'hauling-log', component: HaulingLogComponent, canActivate: [AuthGuard], title: 'Hauling Log',
      },
      {
        path: 'settings', component: SettingsComponent, canActivate: [AuthGuard], title: 'Settings',
      },
      {
        path: 'companies', component: CompaniesComponent, canActivate: [AuthGuard], title: 'Companies',
      },
      {
        path: 'profile', component: ProfileComponent, canActivate: [AuthGuard], title: 'Profile',
      },
      {
        path: 'dfow', component: DFOWComponent, canActivate: [AuthGuard], title: 'DFOW',
      },
      {
        path: 'project-settings', component: ProjectSettingsComponent, canActivate: [AuthGuard], title: 'ProjectSettings',
      },
      {
        path: 'location', component: LocationComponent, canActivate: [AuthGuard], title: 'Locations',
      },
      {
        path: 'void-list', component: VoidListComponent, canActivate: [AuthGuard], title: 'Void List',
      },
      {
        path: 'notification', component: NotificationComponent, canActivate: [AuthGuard], title: 'Notifications',
      },
      {
        path: 'crane-calendar', component: CraneCalendarComponent, canActivate: [AuthGuard], title: 'Crane Calendar',
      },
      { path: 'inspection-calender', component: InspectionCalendarComponent, canActivate: [AuthGuard], title: 'Inspection Calender' },
      { path: 'all-calender', component: AllCalendarComponent, canActivate: [AuthGuard], title: 'All Calendar'  },
      {
        path: 'crane-request', component: CraneRequestGridComponent, canActivate: [AuthGuard], title: 'Crane Request',
      },
      {
        path: 'queued-delivery-request',
        component: QueuedDeliveryRequestComponent,
        canActivate: [AuthGuard],
        title: 'ueued Delivery Request',
      },
      {
        path: 'calendar-settings', component: CalendarViewComponent, canActivate: [AuthGuard], title: 'Calendar Settings',
      },
      {
        path: 'notification-settings',
        component: NotificationSettingsComponent,
        canActivate: [AuthGuard],
        title: 'Notification Settings',
      },
      {
        path: 'concrete-calendar', component: ConcreteCalendarComponent, canActivate: [AuthGuard], title: 'Concrete Calendar',
      },
      {
        path: 'concrete-request',
        component: ConcreteRequestsGridComponent,
        canActivate: [AuthGuard],
        title: 'Concrete Request',
      },
      {
        path: 'reports', component: ReportsComponent, canActivate: [AuthGuard], title: 'Reports',
      },
      {
        path: 'delivery-report', component: DeliveriesComponent, canActivate: [AuthGuard], title: 'Delivery Report',
      },
      {
        path: 'crane-report', component: CraneComponent, canActivate: [AuthGuard], title: 'Crane Report',
      },
      {
        path: 'concrete-report', component: ConcreteComponent, canActivate: [AuthGuard], title: 'Concrete Report',
      },
      {
        path: 'reports-heatmap', component: HeatmapComponent, canActivate: [AuthGuard], title: 'Heatmap Reports',
      },
      {
        path: 'templates', component: TemplateGridComponent, canActivate: [AuthGuard], title: 'Templates',
      },
      {
        path: 'reports-weeklycalendar',
        component: WeeklycalendarComponent,
        canActivate: [AuthGuard],
        title: 'Weekly Calendar',
      },
      {
        path: 'inspection-report', component: InspectionComponent, canActivate: [AuthGuard], title: 'Inspection Report',
      },
      {
        path: 'time-slot', component: TimeslotComponent, canActivate: [AuthGuard], title: 'Time Slot',
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
