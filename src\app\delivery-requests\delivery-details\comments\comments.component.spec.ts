import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { SocketIoModule, SocketIoConfig, Socket } from 'ngx-socket-io';
import { CommentsComponent } from './comments.component';
import { environment } from '../../../../environments/environment';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';
import { MixpanelService } from '../../../services/mixpanel.service';
import { of, throwError } from 'rxjs';

const config: SocketIoConfig = { url: environment.apiSocketUrl, options: {} };

describe('CommentsComponent', () => {
  let component: CommentsComponent;
  let fixture: ComponentFixture<CommentsComponent>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let projectService: jest.Mocked<ProjectService>;
  let toastrService: jest.Mocked<ToastrService>;
  let socket: jest.Mocked<Socket>;
  let mixpanelService: jest.Mocked<MixpanelService>;

  const mockDeliveryService = {
    DeliveryRequestId: of('123'),
    refresh1: of({}),
    getDeliveryRequestComment: jest.fn(),
    createComment: jest.fn(),
    updatedHistory1: jest.fn(),
    loginUser: of({ id: 1, RoleId: 2 })
  };

  const mockProjectService = {
    ParentCompanyId: of('456'),
    projectParent: of({ ProjectId: '789', ParentCompanyId: '456' })
  };

  const mockSocket = {
    emit: jest.fn()
  };

  const mockToastrService = {
    success: jest.fn(),
    error: jest.fn()
  };

  const mockMixpanelService = {
    addMixpanelEvents: jest.fn()
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [CommentsComponent],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        HttpClientTestingModule,
        RouterTestingModule,
        ToastrModule.forRoot(),
        SocketIoModule.forRoot(config),
      ],
      providers: [
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: ProjectService, useValue: mockProjectService },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: Socket, useValue: mockSocket },
        { provide: MixpanelService, useValue: mockMixpanelService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CommentsComponent);
    component = fixture.componentInstance;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    socket = TestBed.inject(Socket) as jest.Mocked<Socket>;
    mixpanelService = TestBed.inject(MixpanelService) as jest.Mocked<MixpanelService>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.commentList).toEqual([]);
    expect(component.loader).toBeTruthy();
    expect(component.submitted).toBeFalsy();
    expect(component.formSubmitted).toBeFalsy();
  });

  it('should initialize form with required validation', () => {
    expect(component.commentDetailsForm.get('comment')).toBeTruthy();
    expect(component.commentDetailsForm.get('comment').errors).toBeTruthy();
    expect(component.commentDetailsForm.get('comment').errors.required).toBeTruthy();
  });

  it('should get comment history when delivery request ID is available', () => {
    const mockComments = {
      data: {
        rows: [
          { id: 1, comment: 'Test comment 1' },
          { id: 2, comment: 'Test comment 2' }
        ]
      }
    };
    mockDeliveryService.getDeliveryRequestComment.mockReturnValue(of(mockComments));

    component.getHistory();

    expect(mockDeliveryService.getDeliveryRequestComment).toHaveBeenCalledWith({
      DeliveryRequestId: '123',
      ParentCompanyId: '456',
      ProjectId: '789'
    });
    expect(component.commentList).toEqual(mockComments.data.rows);
    expect(component.loader).toBeFalsy();
  });

  it('should submit comment successfully', () => {
    const mockResponse = { message: 'Comment added successfully' };
    mockDeliveryService.createComment.mockReturnValue(of(mockResponse));
    component.commentDetailsForm.patchValue({ comment: 'Test comment' });

    component.onSubmit();

    expect(mockDeliveryService.createComment).toHaveBeenCalledWith({
      comment: 'Test comment',
      DeliveryRequestId: '123',
      ParentCompanyId: '456'
    });
    expect(mockToastrService.success).toHaveBeenCalledWith('Comment added successfully', 'Success');
    expect(mockMixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Comment added against a Delivery Booking');
    expect(mockSocket.emit).toHaveBeenCalledWith('commentHistory', mockResponse);
    expect(component.commentDetailsForm.get('comment').value).toBeNull();
    expect(component.submitted).toBeFalsy();
    expect(component.formSubmitted).toBeFalsy();
  });

  it('should handle empty comment submission', () => {
    component.commentDetailsForm.patchValue({ comment: '   ' });
    component.onSubmit();

    expect(mockToastrService.error).toHaveBeenCalledWith('Please enter a valid comment.', 'OOPS!');
    expect(component.submitted).toBeFalsy();
    expect(component.formSubmitted).toBeFalsy();
  });


  it('should check string empty values correctly', () => {
    expect(component.checkStringEmptyValues({ comment: '   ' })).toBeTruthy();
    expect(component.checkStringEmptyValues({ comment: 'test' })).toBeFalsy();
  });

  it('should unsubscribe on destroy', () => {
    const unsubscribeSpy = jest.spyOn(component['subscription'], 'unsubscribe');
    component.ngOnDestroy();
    expect(unsubscribeSpy).toHaveBeenCalled();
  });
});
