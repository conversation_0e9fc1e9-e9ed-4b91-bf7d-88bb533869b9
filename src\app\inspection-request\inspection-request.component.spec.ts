import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { of, Subject } from 'rxjs';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { NgxPaginationModule } from 'ngx-pagination';
import { NgxFileDropModule, NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import moment from 'moment';
import { ProjectService } from '../services/profile/project.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { InspectionRequestComponent } from './inspection-request.component';
import { inspectionType } from '../services/common';

describe('InspectionRequestComponent', () => {
  let component: InspectionRequestComponent;
  let fixture: ComponentFixture<InspectionRequestComponent>;
  let toastr: ToastrService;
  let formBuilder: FormBuilder;
  let modalService: BsModalService;

  // Create subjects for testing subscriptions
  const projectParentSubject = new Subject();
  const parentCompanyIdSubject = new Subject();
  const fileUploadSubject = new Subject();
  const loginUserSubject = new Subject();
  const inspectionCurrentStatusSubject = new Subject();
  const inspectionUpdatedSubject = new Subject();

  const mockProjectService = {
    projectParent: projectParentSubject.asObservable(),
    ParentCompanyId: parentCompanyIdSubject.asObservable(),
    fileUpload: fileUploadSubject.asObservable(),
    uploadBulkNdrFile: jest.fn(),
    gateList: jest.fn().mockReturnValue(of({ data: [] })),
    listEquipment: jest.fn().mockReturnValue(of({ data: [] })),
    getCompanies: jest.fn().mockReturnValue(of({ data: [] })),
    getDefinableWork: jest.fn().mockReturnValue(of({ data: [] })),
    getLocations: jest.fn().mockReturnValue(of({ data: [] })),
    listAllMember: jest.fn().mockReturnValue(of({ data: [] })),
    getProject: jest.fn().mockReturnValue(of({ data: [{ id: '123', projectName: 'Test Project' }] })),
  };

  const mockDeliveryService = {
    getOverAllGateForNdrGrid: jest.fn().mockReturnValue(of([])),
    getOverAllEquipmentForNdrGrid: jest.fn().mockReturnValue(of([])),
    getDefinableForNdrGrid: jest.fn().mockReturnValue(of([])),
    getLocations: jest.fn().mockReturnValue(of([])),
    getCompaniesForNdrGrid: jest.fn().mockReturnValue(of([])),
    getMembers: jest.fn().mockReturnValue(of([])),
    getinspectionRequest: jest.fn().mockReturnValue(of({ data: [], count: 0 })),
    listInspectionNDR: jest.fn().mockReturnValue(of({
      data: { rows: [], count: 0 },
      lastId: 0,
      statusData: { statusColorCode: JSON.stringify([
        { status: 'approved', backgroundColor: '#green', fontColor: '#white' },
        { status: 'pending', backgroundColor: '#yellow', fontColor: '#black' },
        { status: 'delivered', backgroundColor: '#blue', fontColor: '#white' },
        { status: 'rejected', backgroundColor: '#red', fontColor: '#white' },
        { status: 'expired', backgroundColor: '#gray', fontColor: '#white' }
      ]) }
    })),
    loginUser: loginUserSubject.asObservable(),
    getInspectionCurrentStatus: inspectionCurrentStatusSubject.asObservable(),
    inspectionUpdated: inspectionUpdatedSubject.asObservable(),
    updateStateOfInspectionNDR: jest.fn(),
    decryption: jest.fn().mockReturnValue(of({})),
    importBulkNDR: jest.fn().mockReturnValue(of({})),
    importBulkNDRTemplate: jest.fn().mockReturnValue(of(new Blob())),
    updateInspectionRequest: jest.fn().mockReturnValue(of({ message: 'Success' })),
    updatedInspectionId: jest.fn(),
    updatedEditCraneRequestId: jest.fn(),
    updatedInspectionHistory: jest.fn(),
    getMemberRole: jest.fn().mockReturnValue(of({ data: { id: '123', User: { firstName: 'Test', email: '<EMAIL>' } } })),
    updateLoginUser: jest.fn(),
    searchNewMember: jest.fn().mockReturnValue(of([])),
  };

  const mockModalRef = {
    hide: jest.fn(),
    content: { closeBtnName: 'Close' },
    setClass: jest.fn(),
  };

  const dropdownSettings = {
    singleSelection: false,
    idField: 'id',
    textField: 'name',
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    itemsShowLimit: 6,
    allowSearchFilter: true,
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [InspectionRequestComponent],
      imports: [
        ReactiveFormsModule,
        FormsModule,
        NgMultiSelectDropDownModule.forRoot(),
        BsDatepickerModule.forRoot(),
        NgxPaginationModule,
        NgxFileDropModule,
      ],
      providers: [
        FormBuilder,
        { provide: BsModalService, useValue: { show: jest.fn().mockReturnValue(mockModalRef) } },
        { provide: BsModalRef, useValue: mockModalRef },
        { provide: Router, useValue: { navigate: jest.fn(), events: of({}) } },
        { provide: ActivatedRoute, useValue: { queryParams: of({}) } },
        { provide: ToastrService, useValue: { success: jest.fn(), error: jest.fn() } },
        { provide: Socket, useValue: { on: jest.fn() } },
        { provide: Title, useValue: { setTitle: jest.fn() } },
        { provide: ProjectService, useValue: mockProjectService },
        { provide: DeliveryService, useValue: mockDeliveryService },
      ],
    }).compileComponents();

    formBuilder = TestBed.inject(FormBuilder);
    modalService = TestBed.inject(BsModalService);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(InspectionRequestComponent);
    component = fixture.componentInstance;
    toastr = TestBed.inject(ToastrService);

    // Initialize the form
    component.filterForm = formBuilder.group({
      companyFilter: [''],
      descriptionFilter: [''],
      statusFilter: [''],
      dateFilter: [''],
      memberFilter: [''],
      gateFilter: [''],
      equipmentFilter: [''],
      locationFilter: [''],
      inspectionStatusFilter: [''],
      inspectionTypeFilter: [''],
      pickFrom: [''],
      pickTo: [''],
    });

    // Initialize deliverEditMultipleForm
    component.deliverEditMultipleForm = formBuilder.group({
      escort: [''],
      GateId: [''],
      inspectionType: [''],
      inspectionStart: [''],
      inspectionEnd: [''],
      inspectionLocation: [''],
      inspectionDescription: [''],
      inspectionStatus: [''],
      status: [''],
    });

    // Mock data
    component.inspectionList = [];
    component.totalCount = 0;
    component.pageSize = 25;
    component.pageNo = 1;
    component.files = [];
    component.inspectionType = inspectionType;
    component.inspectionStatusList = ['Pass', 'Fail'];
    component.todayDate = new Date('2024-03-20');
    component.newNdrCompanyDropdownSettings = dropdownSettings;
    component.newNdrDefinableDropdownSettings = dropdownSettings;
    component.equipmentDropdownSettings = dropdownSettings;
    component.modalRef = mockModalRef;
    component.selectedinspectionListStatus = '';
    component.formSubmitted = false;
    component.editMultipleSubmitted = false;
    component.ParentCompanyId = '456';
    component.editedFields = 'status';
    component.voidvalue = false;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.currentPageNo).toBe(1);
    expect(component.pageSize).toBe(25);
    expect(component.pageNo).toBe(1);
    expect(component.totalCount).toBe(0);
    expect(component.loader).toBe(true);
  });

  it('should set title on initialization', () => {
    const titleService = TestBed.inject(Title);
    expect(titleService.setTitle).toHaveBeenCalledWith('Follo - inspection Bookings');
  });

  it('should handle sort by field', () => {
    component.sortByField('id', 'ASC');
    expect(component.sortColumn).toBe('id');
    expect(component.sort).toBe('ASC');
  });

  it('should reset filter form', () => {
    component.resetFilter();
    expect(component.filterCount).toBe(0);
    expect(component.search).toBe('');
  });

  it('should handle page size change', () => {
    component.changePageSize(50);
    expect(component.pageSize).toBe(50);
  });

  it('should handle page number change', () => {
    component.changePageNo(2);
    expect(component.pageNo).toBe(2);
  });

  it('should check if inspection dates are valid', () => {
    const startDate = new Date('2024-03-20T10:00:00');
    const endDate = new Date('2024-03-20T11:00:00');

    const result = component.checkStartEnd(startDate, endDate);
    expect(result).toBe(true);
  });

  it('should validate number only input', () => {
    const event = { which: 48, keyCode: 48 }; // '0' key
    const result = component.numberOnly(event);
    expect(result).toBe(true);
  });

  it('should handle inspection type change', () => {
    const type = 'Material';
    component.deliverEditMultipleForm.patchValue({ inspectionType: '' });
    component.onChangeInspectionType(type);
    expect(component.deliverEditMultipleForm.get('inspectionType').value).toBe(type);
  });

  it('should handle equipment type check', () => {
    component.equipmentList = [
      { id: 1, PresetEquipmentType: { isCraneType: true } },
    ];
    const event = [{ id: 1 }];
    component.checkEquipmentType(event);
    expect(component.craneEquipmentTypeChosen).toBe(true);
    expect(component.isAssociatedWithCraneRequest).toBe(true);
  });

  it('should handle form submission', () => {
    component.formSubmitted = false;
    component.deliverForm();
    component.formSubmitted = true;
    expect(component.formSubmitted).toBe(true);
  });


  it('should show error message', () => {
    const error = { message: { details: [{ message: 'Test error' }] } };
    component.showError(error);
    expect(toastr.error).toHaveBeenCalled();
  });

  it('should close modal', () => {
    component.close();
    expect(mockModalRef.hide).toHaveBeenCalled();
  });

  it('should handle file drop', () => {
    const mockFile = new File(['test'], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const mockFileEntry: Partial<FileSystemFileEntry> = {
      name: 'test.xlsx',
      isFile: true,
      isDirectory: false,
      file: <T>(callback: (file: File) => T) => callback(mockFile),
    };
    const mockDropEntry: NgxFileDropEntry = {
      relativePath: 'test.xlsx',
      fileEntry: mockFileEntry as FileSystemFileEntry,
    };

    component.dropped([mockDropEntry]);
    expect(component.files.length).toBe(1);
  });

  it('should handle file drop with invalid file type', () => {
    const mockFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    const mockFileEntry: Partial<FileSystemFileEntry> = {
      name: 'test.txt',
      isFile: true,
      isDirectory: false,
      file: <T>(callback: (file: File) => T) => callback(mockFile),
    };
    const mockDropEntry: NgxFileDropEntry = {
      relativePath: 'test.txt',
      fileEntry: mockFileEntry as FileSystemFileEntry,
    };

    component.dropped([mockDropEntry]);
    expect(component.files.length).toBe(0);
    expect(toastr.error).toHaveBeenCalledWith('Please select a valid file. Supported file format (.xlsx)', 'OOPS!');
  });

  it('should handle multiple file drop', () => {
    const mockFile1 = new File(['test1'], 'test1.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const mockFile2 = new File(['test2'], 'test2.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const mockFileEntry1: Partial<FileSystemFileEntry> = {
      name: 'test1.xlsx',
      isFile: true,
      isDirectory: false,
      file: <T>(callback: (file: File) => T) => callback(mockFile1),
    };
    const mockFileEntry2: Partial<FileSystemFileEntry> = {
      name: 'test2.xlsx',
      isFile: true,
      isDirectory: false,
      file: <T>(callback: (file: File) => T) => callback(mockFile2),
    };
    const mockDropEntry1: NgxFileDropEntry = {
      relativePath: 'test1.xlsx',
      fileEntry: mockFileEntry1 as FileSystemFileEntry,
    };
    const mockDropEntry2: NgxFileDropEntry = {
      relativePath: 'test2.xlsx',
      fileEntry: mockFileEntry2 as FileSystemFileEntry,
    };

    component.dropped([mockDropEntry1, mockDropEntry2]);
    expect(toastr.error).toHaveBeenCalledWith('Please import single file', 'OOPS!');
  });

  it('should handle file import', () => {
    const mockFile = new File(['test'], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const mockFileEntry: Partial<FileSystemFileEntry> = {
      name: 'test.xlsx',
      isFile: true,
      isDirectory: false,
      file: <T>(callback: (file: File) => T) => callback(mockFile),
    };
    const mockDropEntry: NgxFileDropEntry = {
      relativePath: 'test.xlsx',
      fileEntry: mockFileEntry as FileSystemFileEntry,
    };

    component.dropped([mockDropEntry]);
    component.importinspectionRequest();

    expect(mockDeliveryService.importBulkNDR).toHaveBeenCalled();
  });

  // Additional positive test cases for constructor subscriptions
  it('should handle projectParent subscription with valid data', () => {
    const mockProjectData = { ProjectId: '123', ParentCompanyId: '456' };
    const getinspectionRequestSpy = jest.spyOn(component, 'getinspectionRequest').mockImplementation();
    const getOverAllGateInNewinspectionSpy = jest.spyOn(component, 'getOverAllGateInNewinspection').mockImplementation();
    const getMembersSpy = jest.spyOn(component, 'getMembers').mockImplementation();

    // Simulate the subscription trigger
    projectParentSubject.next(mockProjectData);

    expect(component.ProjectId).toBe('123');
    expect(component.ParentCompanyId).toBe('456');
    expect(getinspectionRequestSpy).toHaveBeenCalled();
    expect(getOverAllGateInNewinspectionSpy).toHaveBeenCalled();
    expect(getMembersSpy).toHaveBeenCalled();
  });

  it('should handle ParentCompanyId subscription', () => {
    parentCompanyIdSubject.next('789');
    expect(component.ParentCompanyId).toBe('789');
  });

  it('should handle loginUser subscription with role 2', () => {
    const mockUser = { RoleId: 2, User: { email: '<EMAIL>' }, id: '123' };

    loginUserSubject.next(mockUser);

    expect(component.authUser).toEqual(mockUser);
    expect(component.statusValue).toEqual(['Approved', 'Declined']);
  });

  it('should handle loginUser subscription with role 3', () => {
    const mockUser = { RoleId: 3, User: { email: '<EMAIL>' }, id: '123' };

    loginUserSubject.next(mockUser);

    expect(component.authUser).toEqual(mockUser);
    expect(component.statusValue).toEqual(['Delivered', 'Approved']);
  });

  it('should handle getInspectionCurrentStatus subscription', () => {
    const mockInspectionId = '456';
    const getinspectionRequestSpy = jest.spyOn(component, 'getinspectionRequest').mockImplementation();

    inspectionCurrentStatusSubject.next(mockInspectionId);

    expect(component.inspectionId).toBe(mockInspectionId);
    expect(getinspectionRequestSpy).toHaveBeenCalled();
  });

  it('should handle fileUpload subscription with uploading status', () => {
    fileUploadSubject.next({ status: 'uploading' });

    expect(component.bulkNdrUploadInProgress).toBe(true);
  });

  it('should handle fileUpload subscription with uploadDone status', () => {
    const getinspectionRequestSpy = jest.spyOn(component, 'getinspectionRequest').mockImplementation();

    fileUploadSubject.next({ status: 'uploadDone' });

    expect(component.bulkNdrUploadInProgress).toBe(false);
    expect(mockProjectService.uploadBulkNdrFile).toHaveBeenCalledWith({ status: '' });
    expect(getinspectionRequestSpy).toHaveBeenCalled();
  });

  // ngOnInit tests
  it('should handle router events and hide modal', () => {
    component.modalRef = mockModalRef;

    component.ngOnInit();

    expect(mockModalRef.hide).toHaveBeenCalled();
  });

  it('should handle inspectionUpdated subscription', () => {
    const getinspectionRequestSpy = jest.spyOn(component, 'getinspectionRequest').mockImplementation();

    component.ngOnInit();
    inspectionUpdatedSubject.next('updated');

    expect(getinspectionRequestSpy).toHaveBeenCalled();
  });

  // Keyboard event handlers
  it('should handle toggle keydown with Enter key', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const sortByFieldSpy = jest.spyOn(component, 'sortByField').mockImplementation();

    component.handleToggleKeydown(event, 'id', 'ASC');

    expect(sortByFieldSpy).toHaveBeenCalledWith('id', 'ASC');
  });

  it('should handle toggle keydown with Space key', () => {
    const event = new KeyboardEvent('keydown', { key: ' ' });
    const sortByFieldSpy = jest.spyOn(component, 'sortByField').mockImplementation();

    component.handleToggleKeydown(event, 'name', 'DESC');

    expect(sortByFieldSpy).toHaveBeenCalledWith('name', 'DESC');
  });

  it('should handle down keydown with clear type', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const clearSpy = jest.spyOn(component, 'clear').mockImplementation();

    component.handleDownKeydown(event, null, null, 'clear');

    expect(clearSpy).toHaveBeenCalled();
  });

  it('should handle down keydown with openMod type', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const openModal1Spy = jest.spyOn(component, 'openModal1').mockImplementation();

    component.handleDownKeydown(event, 'template', null, 'openMod');

    expect(openModal1Spy).toHaveBeenCalledWith('template');
  });

  it('should handle down keydown with open type', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const openIdModalSpy = jest.spyOn(component, 'openIdModal').mockImplementation();

    component.handleDownKeydown(event, 'data', 'item', 'open');

    expect(openIdModalSpy).toHaveBeenCalledWith('data', 'item');
  });

  it('should handle down keydown with remove type', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const removeFileSpy = jest.spyOn(component, 'removeFile').mockImplementation();

    component.handleDownKeydown(event, 1, null, 'remove');

    expect(removeFileSpy).toHaveBeenCalledWith(1);
  });

  it('should handle down keydown with download type', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const downloadSpy = jest.spyOn(component, 'download').mockImplementation();

    component.handleDownKeydown(event, null, null, 'download');

    expect(downloadSpy).toHaveBeenCalled();
  });

  // Form operations tests
  it('should handle filter submit with all filters', () => {
    component.filterForm.patchValue({
      descriptionFilter: 'test description',
      dateFilter: new Date('2024-03-20'),
      companyFilter: '1',
      memberFilter: '2',
      gateFilter: '3',
      equipmentFilter: '4',
      locationFilter: 'test location',
      inspectionStatusFilter: 'Pass',
      inspectionTypeFilter: 'Material',
      statusFilter: 'Approved',
      pickFrom: new Date('2024-03-20'),
      pickTo: new Date('2024-03-21')
    });

    const getinspectionRequestSpy = jest.spyOn(component, 'getinspectionRequest').mockImplementation();

    component.filterSubmit();

    expect(component.filterCount).toBe(11);
    expect(component.pageNo).toBe(1);
    expect(getinspectionRequestSpy).toHaveBeenCalled();
    expect(mockModalRef.hide).toHaveBeenCalled();
  });

  it('should handle search with valid data', () => {
    const getinspectionRequestSpy = jest.spyOn(component, 'getinspectionRequest').mockImplementation();

    component.getSearchNDR('test search');

    expect(component.showSearchbar).toBe(true);
    expect(component.pageNo).toBe(1);
    expect(component.search).toBe('test search');
    expect(getinspectionRequestSpy).toHaveBeenCalled();
  });

  it('should handle search with empty data', () => {
    const getinspectionRequestSpy = jest.spyOn(component, 'getinspectionRequest').mockImplementation();

    component.getSearchNDR('');

    expect(component.showSearchbar).toBe(false);
    expect(component.pageNo).toBe(1);
    expect(component.search).toBe('');
    expect(getinspectionRequestSpy).toHaveBeenCalled();
  });

  it('should select status', () => {
    component.selectStatus('Approved');
    expect(component.currentStatus).toBe('Approved');
  });

  it('should redirect to path', () => {
    const router = TestBed.inject(Router);
    component.redirect('test-path');
    expect(router.navigate).toHaveBeenCalledWith(['/test-path']);
  });

  // Data fetching methods
  it('should get inspection request data', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.pageSize = 25;
    component.pageNo = 1;
    component.search = 'test';
    component.sort = 'ASC';
    component.sortColumn = 'id';

    component.getinspectionRequest();

    expect(component.loader).toBe(true);
    expect(component.inspectionList).toEqual([]);
    expect(mockDeliveryService.listInspectionNDR).toHaveBeenCalled();
  });

  it('should handle input change for void checkbox', () => {
    // Mock DOM element
    const mockCheckbox = { checked: true };
    jest.spyOn(document, 'getElementById').mockReturnValue(mockCheckbox as any);

    component.handleInputChange({});

    expect(component.voidvalue).toBe(true);
  });

  it('should handle input change for void checkbox unchecked', () => {
    // Mock DOM element
    const mockCheckbox = { checked: false };
    jest.spyOn(document, 'getElementById').mockReturnValue(mockCheckbox as any);

    component.handleInputChange({});

    expect(component.voidvalue).toBe(false);
  });

  // Modal operations
  it('should open modal with template', () => {
    const template = {} as any;
    component.openModal(template);

    expect(component.currentTemplate).toBe(template);
    expect(modalService.show).toHaveBeenCalledWith(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-inspection-popup custom-modal'
    });
  });

  it('should close popup content modal', () => {
    component.modalLoader = true;
    component.closePopupContentModal();
    expect(component.modalLoader).toBe(false);
  });

  // Validation methods
  it('should validate start date is before end date', () => {
    const startDate = new Date('2024-03-20T10:00:00');
    const endDate = new Date('2024-03-20T11:00:00');

    const result = component.checkStartEnd(startDate, endDate);
    expect(result).toBe(true);
  });

  it('should validate start date is not before end date', () => {
    const startDate = new Date('2024-03-20T11:00:00');
    const endDate = new Date('2024-03-20T10:00:00');

    const result = component.checkStartEnd(startDate, endDate);
    expect(result).toBe(false);
  });

  it('should validate future dates for edit inspection', () => {
    const futureStart = new Date(Date.now() + 86400000); // tomorrow
    const futureEnd = new Date(Date.now() + 172800000); // day after tomorrow

    const result = component.checkEditinspectionFutureDate(futureStart, futureEnd);
    expect(result).toBe(true);
  });

  it('should validate past dates for edit inspection', () => {
    const pastStart = new Date(Date.now() - 86400000); // yesterday
    const pastEnd = new Date(Date.now() - 43200000); // 12 hours ago

    const result = component.checkEditinspectionFutureDate(pastStart, pastEnd);
    expect(result).toBe(false);
  });

  it('should check if inspection start and end times are same', () => {
    const sameTime = new Date('2024-03-20T10:00:00');

    const result = component.checkNewinspectionStartEndSame(sameTime, sameTime);
    expect(result).toBe(true);
  });

  it('should check if inspection start and end times are different', () => {
    const startTime = new Date('2024-03-20T10:00:00');
    const endTime = new Date('2024-03-20T11:00:00');

    const result = component.checkNewinspectionStartEndSame(startTime, endTime);
    expect(result).toBe(false);
  });

  // Negative test cases
  it('should handle invalid number input', () => {
    const event = { which: 65, keyCode: 65 }; // 'A' key
    const result = component.numberOnly(event);
    expect(result).toBe(false);
  });

  it('should handle special keys in number input', () => {
    const event = { which: 8, keyCode: 8 }; // Backspace key
    const result = component.numberOnly(event);
    expect(result).toBe(true);
  });

  it('should handle equipment type check with empty array', () => {
    component.checkEquipmentType([]);
    expect(component.craneEquipmentTypeChosen).toBe(false);
  });

  it('should handle equipment type check with non-crane equipment', () => {
    component.equipmentList = [
      { id: 1, PresetEquipmentType: { isCraneType: false } },
    ];
    const event = [{ id: 1 }];
    component.checkEquipmentType(event);
    expect(component.craneEquipmentTypeChosen).toBe(false);
    expect(component.isAssociatedWithCraneRequest).toBe(false);
  });

  it('should handle error with missing message details', () => {
    const error = { message: { details: [] } };
    component.showError(error);
    expect(toastr.error).toHaveBeenCalled();
  });

  it('should handle toggle keydown with other keys', () => {
    const event = new KeyboardEvent('keydown', { key: 'Tab' });
    const sortByFieldSpy = jest.spyOn(component, 'sortByField').mockImplementation();

    component.handleToggleKeydown(event, 'id', 'ASC');

    expect(sortByFieldSpy).not.toHaveBeenCalled();
  });

  it('should handle down keydown with default case', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const clearSpy = jest.spyOn(component, 'clear').mockImplementation();

    component.handleDownKeydown(event, null, null, 'unknown');

    expect(clearSpy).not.toHaveBeenCalled();
  });

  // File operations negative cases
  it('should handle file drop with directory entry', () => {
    const mockDirectoryEntry = {
      name: 'test-folder',
      isFile: false,
      isDirectory: true,
    };
    const mockDropEntry: NgxFileDropEntry = {
      relativePath: 'test-folder',
      fileEntry: mockDirectoryEntry as unknown as FileSystemFileEntry,
    };

    component.dropped([mockDropEntry]);
    expect(component.files.length).toBe(0);
  });

  it('should handle import inspection request with error', () => {
    const mockFile = new File(['test'], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    component.formData = new FormData();
    component.formData.append('inspection_request', mockFile, 'test.xlsx');
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    // Mock error response
    mockDeliveryService.importBulkNDR.mockReturnValue(of().pipe(() => {
      throw { message: { statusCode: 400, details: [{ message: 'Import error' }] } };
    }));

    component.importinspectionRequest();

    expect(component.importSubmitted).toBe(true);
  });

  it('should handle download with error', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    // Mock error response
    mockProjectService.getProject.mockReturnValue(of().pipe(() => {
      throw { message: { statusCode: 400, details: [{ message: 'Download error' }] } };
    }));

    component.download();

    expect(mockDeliveryService.importBulkNDRTemplate).toHaveBeenCalled();
  });

  // Modal operations negative cases
  it('should handle reset form with no action', () => {
    component.modalRef1 = mockModalRef;
    component.resetForm('no');
    expect(mockModalRef.hide).toHaveBeenCalled();
  });

  it('should handle reset form with yes action', () => {
    component.modalRef1 = mockModalRef;
    component.modalRef = mockModalRef;
    component.files = [{ relativePath: 'test.xlsx' } as NgxFileDropEntry];

    component.resetForm('yes');

    expect(mockModalRef.hide).toHaveBeenCalledTimes(2);
    expect(component.files).toEqual([]);
  });

  it('should handle close modal with files', () => {
    component.files = [{ relativePath: 'test.xlsx' } as NgxFileDropEntry];
    const template = {} as any;

    component.closeModal(template);

    expect(modalService.show).toHaveBeenCalledWith(template, {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
    });
  });

  it('should handle close modal without files', () => {
    component.files = [];
    const resetFormSpy = jest.spyOn(component, 'resetForm').mockImplementation();
    const template = {} as any;

    component.closeModal(template);

    expect(resetFormSpy).toHaveBeenCalledWith('yes');
  });

  // Edit operations
  it('should handle edit submit form for different field types', () => {
    component.onEditSubmitForm('companies');
    expect(component.companyEdited).toBe(true);

    component.onEditSubmitForm('dfow');
    expect(component.dfowEdited).toBe(true);

    component.onEditSubmitForm('escort');
    expect(component.escortEdited).toBe(true);

    component.onEditSubmitForm('persons');
    expect(component.responsiblePersonEdited).toBe(true);

    component.onEditSubmitForm('gate');
    expect(component.gateEdited).toBe(true);

    component.onEditSubmitForm('void');
    expect(component.voidEdited).toBe(true);

    component.onEditSubmitForm('status');
    expect(component.statusEdited).toBe(true);
  });

  it('should handle edit submit form for inspection date with value', () => {
    component.deliverEditMultipleForm.patchValue({ inspectionDate: new Date() });
    const setDefaultinspectionTimeSpy = jest.spyOn(component, 'setDefaultinspectionTime').mockImplementation();

    component.onEditSubmitForm('inspectionDate');

    expect(component.inspectionDateEdited).toBe(true);
    expect(setDefaultinspectionTimeSpy).toHaveBeenCalled();
  });

  it('should handle edit submit form for inspection date without value', () => {
    component.deliverEditMultipleForm.patchValue({ inspectionDate: null });

    component.onEditSubmitForm('inspectionDate');

    expect(component.inspectionDateEdited).toBe(false);
    expect(component.deliverEditMultipleForm.get('inspectionStart').value).toBe('');
    expect(component.deliverEditMultipleForm.get('inspectionEnd').value).toBe('');
  });

  it('should set default inspection time', () => {
    component.setDefaultinspectionTime();

    expect(component.inspectionStart).toBeDefined();
    expect(component.inspectionEnd).toBeDefined();
    expect(component.deliverEditMultipleForm.get('inspectionStart').value).toBeDefined();
    expect(component.deliverEditMultipleForm.get('inspectionEnd').value).toBeDefined();
  });

  it('should convert start time correctly', () => {
    const inspectionDate = new Date('2024-03-20');
    const result = component.convertStart(inspectionDate, 10, 30);

    expect(result).toContain('2024');
    expect(typeof result).toBe('string');
  });

  // Additional comprehensive test cases
  it('should handle change date', () => {
    component.editModalLoader = false;
    const testDate = new Date('2024-03-20T10:00:00');

    component.changeDate(testDate);

    expect(component.inspectionEnd).toBeDefined();
    expect(component.NDRTimingChanged).toBe(true);
  });

  it('should handle inspection end time change detection', () => {
    component.inspectionEndTimeChangeDetection();
    expect(component.NDRTimingChanged).toBe(true);
  });

  it('should handle remove file', () => {
    component.files = [
      { relativePath: 'test1.xlsx' } as NgxFileDropEntry,
      { relativePath: 'test2.xlsx' } as NgxFileDropEntry
    ];

    component.removeFile(0);

    expect(component.files.length).toBe(1);
    expect(component.files[0].relativePath).toBe('test2.xlsx');
  });

  it('should handle set status with role 2 and valid status', () => {
    component.authUser = { RoleId: 2 };
    const item = { id: 1, status: 'Pending' };
    component.inspectionList = [{ id: 1, status: 'Pending' }];

    component.setStatus(item);

    expect(component.inspectionId).toBe(-1);
    expect(component.currentinspectionIndex).toBe(0);
    expect(component.showStatus).toBe(true);
  });

  it('should handle set status with role 3 and approved status', () => {
    component.authUser = { RoleId: 3 };
    const item = { id: 1, status: 'Approved' };
    component.inspectionList = [{ id: 1, status: 'Approved' }];

    component.setStatus(item);

    expect(component.showStatus).toBe(true);
  });

  it('should handle set status with expired status', () => {
    component.authUser = { RoleId: 2 };
    const item = { id: 1, status: 'Expired' };
    component.inspectionList = [{ id: 1, status: 'Expired' }];

    component.setStatus(item);

    expect(component.showStatus).toBe(false);
  });

  it('should handle open id modal', () => {
    const item = { id: 1, ProjectId: '123' };
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    component.openIdModal(item, 'current');

    expect(mockDeliveryService.updateStateOfInspectionNDR).toHaveBeenCalledWith('current');
    expect(modalService.show).toHaveBeenCalled();
  });

  it('should handle open edit modal for crane request', () => {
    const item = {
      id: 1,
      CraneRequestId: 123,
      isAssociatedWithinspectionRequest: false,
      isAssociatedWithCraneRequest: false,
      recurrence: null
    };

    component.openEditModal(item, 'crane');

    expect(mockDeliveryService.updateStateOfInspectionNDR).toHaveBeenCalledWith('crane');
    expect(mockDeliveryService.updatedEditCraneRequestId).toHaveBeenCalledWith(123);
  });

  it('should handle open edit modal for inspection request', () => {
    const item = {
      id: 1,
      isAssociatedWithinspectionRequest: true,
      recurrence: { id: 1, recurrenceEndDate: '2024-12-31' }
    };

    component.openEditModal(item, 'edit');

    expect(mockDeliveryService.updateStateOfInspectionNDR).toHaveBeenCalledWith('edit');
    expect(mockDeliveryService.updatedInspectionId).toHaveBeenCalledWith(1);
  });

  it('should handle set gate equipment', () => {
    component.lastId = 123;

    component.setGateEquipment();

    expect(modalService.show).toHaveBeenCalled();
  });

  it('should handle open modal1', () => {
    const template = {} as any;
    const getOverAllGateForNdrGridSpy = jest.spyOn(component, 'getOverAllGateForNdrGrid').mockImplementation();

    component.openModal1(template);

    expect(getOverAllGateForNdrGridSpy).toHaveBeenCalled();
    expect(modalService.show).toHaveBeenCalledWith(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-sm filter-popup custom-modal'
    });
  });

  it('should handle select all current inspection request for edit', () => {
    component.inspectionList = [
      { id: 1, inspectionId: 'INS001', status: 'Pending', isAllowedToEdit: true, isChecked: false },
      { id: 2, inspectionId: 'INS002', status: 'Approved', isAllowedToEdit: false, isChecked: false }
    ];

    component.selectAllCurrentinspectionRequestForEdit();

    expect(component.currentinspectionRequestSelectAll).toBe(true);
    expect(component.inspectionList[0].isChecked).toBe(true);
    expect(component.inspectionList[1].isChecked).toBe(false);
    expect(component.selectedinspectionRequestIdForMultipleEdit.length).toBe(1);
  });

  it('should handle unselect all current inspection request for edit', () => {
    component.currentinspectionRequestSelectAll = true;
    component.inspectionList = [
      { id: 1, inspectionId: 'INS001', status: 'Pending', isAllowedToEdit: true, isChecked: true }
    ];

    component.selectAllCurrentinspectionRequestForEdit();

    expect(component.currentinspectionRequestSelectAll).toBe(false);
    expect(component.inspectionList[0].isChecked).toBe(false);
  });

  it('should check if current inspection request row selected returns false when select all is true', () => {
    component.currentinspectionRequestSelectAll = true;

    const result = component.checkIfCurrentinspectionRequestRowSelected();

    expect(result).toBe(false);
  });

  it('should check if current inspection request row selected returns false when item is checked', () => {
    component.currentinspectionRequestSelectAll = false;
    component.inspectionList = [{ isChecked: true }];

    const result = component.checkIfCurrentinspectionRequestRowSelected();

    expect(result).toBe(false);
  });

  it('should check if current inspection request row selected returns true when no item is checked', () => {
    component.currentinspectionRequestSelectAll = false;
    component.inspectionList = [{ isChecked: false }];

    const result = component.checkIfCurrentinspectionRequestRowSelected();

    expect(result).toBe(true);
  });

  it('should set selected current inspection request item', () => {
    component.inspectionList = [
      { id: 1, InspectionId: 'INS001', status: 'Pending', isChecked: false }
    ];
    component.selectedinspectionRequestId = '';
    component.selectedinspectionListStatus = '';
    component.selectedinspectionRequestIdForMultipleEdit = [];

    component.setSelectedCurrentinspectionRequestItem(0);

    expect(component.inspectionList[0].isChecked).toBe(true);
    expect(component.selectedinspectionRequestId).toContain('INS001');
    expect(component.selectedinspectionRequestIdForMultipleEdit).toContain(1);
  });

  it('should unset selected current inspection request item', () => {
    component.inspectionList = [
      { id: 1, InspectionId: 'INS001', status: 'Pending', isChecked: true }
    ];
    component.selectedinspectionRequestId = 'INS001,';
    component.selectedinspectionRequestIdForMultipleEdit = [1];

    component.setSelectedCurrentinspectionRequestItem(0);

    expect(component.inspectionList[0].isChecked).toBe(false);
    expect(component.selectedinspectionRequestId).toBe('');
    expect(component.selectedinspectionRequestIdForMultipleEdit).toEqual([]);
  });

  // Additional comprehensive test cases for missing coverage
  it('should handle getOverAllGateForNdrGrid', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    component.getOverAllGateForNdrGrid();

    expect(component.modalLoader).toBe(true);
    expect(mockProjectService.gateList).toHaveBeenCalledWith(
      { ProjectId: '123', pageSize: 0, pageNo: 0, ParentCompanyId: '456' },
      { isFilter: true, showActivatedAlone: true }
    );
  });

  it('should handle getOverAllEquipmentForNdrGrid', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    component.getOverAllEquipmentForNdrGrid();

    expect(mockProjectService.listEquipment).toHaveBeenCalledWith(
      { ProjectId: '123', pageSize: 0, pageNo: 0, ParentCompanyId: '456' },
      { isFilter: true, showActivatedAlone: true }
    );
  });

  it('should handle getDefinableForNdrGrid', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    component.getDefinableForNdrGrid();

    expect(mockProjectService.getDefinableWork).toHaveBeenCalledWith({
      ProjectId: '123',
      ParentCompanyId: '456'
    });
  });

  it('should handle getLocations', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    component.getLocations();

    expect(mockProjectService.getLocations).toHaveBeenCalledWith({
      ProjectId: '123',
      ParentCompanyId: '456'
    });
  });

  it('should handle getCompaniesForNdrGrid', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    component.getCompaniesForNdrGrid();

    expect(mockProjectService.getCompanies).toHaveBeenCalledWith({
      ProjectId: '123',
      ParentCompanyId: '456'
    });
  });

  it('should handle getMembers', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    component.getMembers();

    expect(mockProjectService.listAllMember).toHaveBeenCalledWith({
      ProjectId: '123',
      ParentCompanyId: '456'
    });
  });

  it('should handle getOverAllGateInNewinspection', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    component.getOverAllGateInNewinspection();

    expect(component.modalLoader).toBe(true);
    expect(mockProjectService.gateList).toHaveBeenCalled();
  });

  it('should handle getOverAllEquipmentInNewinspection', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    component.getOverAllEquipmentInNewinspection();

    expect(mockProjectService.listEquipment).toHaveBeenCalled();
  });

  it('should handle newNdrgetCompanies', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    component.newNdrgetCompanies();

    expect(mockProjectService.getCompanies).toHaveBeenCalledWith({
      ProjectId: '123',
      ParentCompanyId: '456'
    });
  });

  it('should handle getDefinable', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    component.getDefinable();

    expect(mockProjectService.getDefinableWork).toHaveBeenCalledWith({
      ProjectId: '123',
      ParentCompanyId: '456'
    });
  });

  it('should handle setDefaultPerson', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    component.setDefaultPerson();

    expect(mockDeliveryService.getMemberRole).toHaveBeenCalledWith({
      ProjectId: '123',
      ParentCompanyId: '456'
    });
  });

  it('should handle requestAutocompleteItems', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    const searchText = 'test';

    component.requestAutocompleteItems(searchText);

    expect(mockDeliveryService.searchNewMember).toHaveBeenCalledWith({
      ProjectId: '123',
      search: 'test',
      ParentCompanyId: '456'
    });
  });

  it('should handle openEditMultipleModal', () => {
    const template = {} as any;
    const deliverFormSpy = jest.spyOn(component, 'deliverForm').mockImplementation();
    const setDefaultPersonSpy = jest.spyOn(component, 'setDefaultPerson').mockImplementation();
    component.selectedinspectionRequestId = 'INS001,INS002,';

    component.openEditMultipleModal(template);

    expect(deliverFormSpy).toHaveBeenCalled();
    expect(setDefaultPersonSpy).toHaveBeenCalled();
    expect(modalService.show).toHaveBeenCalledWith(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-inspection-popup custom-modal edit-multiple-modal'
    });
  });

  it('should handle closeEditMultiplePopup', () => {
    component.modalRef = mockModalRef;
    component.formEditSubmitted = true;
    component.editModalLoader = true;

    component.closeEditMultiplePopup();

    expect(component.formEditSubmitted).toBe(false);
    expect(component.editModalLoader).toBe(false);
    expect(mockModalRef.hide).toHaveBeenCalled();
  });

  // More comprehensive test cases for higher coverage
  it('should handle showError with valid error details', () => {
    const error = {
      message: {
        details: [{ field: 'Test error message', code: 'ERROR_001' }]
      }
    };

    component.showError(error);

    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
    expect(toastr.error).toHaveBeenCalled();
  });

  it('should handle filterDetailsForm', () => {
    component.filterDetailsForm();

    expect(component.filterForm).toBeDefined();
    expect(component.filterForm.get('companyFilter')).toBeDefined();
    expect(component.filterForm.get('descriptionFilter')).toBeDefined();
    expect(component.filterForm.get('statusFilter')).toBeDefined();
  });

  it('should handle deliverForm', () => {
    component.deliverForm();

    expect(component.deliverEditMultipleForm).toBeDefined();
    expect(component.deliverEditMultipleForm.get('EquipmentId')).toBeDefined();
    expect(component.deliverEditMultipleForm.get('GateId')).toBeDefined();
    expect(component.deliverEditMultipleForm.get('person')).toBeDefined();
  });

  it('should handle onChangeInspectionType', () => {
    component.deliverForm();
    const inspectionType = 'Material';

    component.onChangeInspectionType(inspectionType);

    expect(component.deliverEditMultipleForm.get('inspectionType').value).toBe('Material');
  });

  it('should handle constructPayload with all fields', () => {
    component.deliverForm();
    component.selectedinspectionRequestIdForMultipleEdit = [1, 2, 3];
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.editedFields = 'companies,persons,gate';
    component.inspectionStart = new Date();
    component.inspectionEnd = new Date();
    component.voidvalue = true;
    component.isAssociatedWithCraneRequest = true;

    // Set form values
    component.deliverEditMultipleForm.patchValue({
      escort: true,
      GateId: '1',
      inspectionType: 'Material',
      status: 'Approved',
      companyItems: [{ id: 1 }, { id: 2 }],
      person: [{ id: 1 }, { id: 2 }],
      defineItems: [{ id: 1 }],
      EquipmentId: [{ id: 1 }],
      cranePickUpLocation: 'Location A',
      craneDropOffLocation: 'Location B'
    });

    const payload = component.constructPayload();

    expect(payload.InspectionRequestIds).toEqual([1, 2, 3]);
    expect(payload.escort).toBe(true);
    expect(payload.GateId).toBe('1');
    expect(payload.inspectionType).toBe('Material');
    expect(payload.status).toBe('Approved');
    expect(payload.void).toBe(true);
    expect(payload.companies).toEqual([1, 2]);
    expect(payload.persons).toEqual([1, 2]);
    expect(payload.define).toEqual([1]);
    expect(payload.EquipmentId).toEqual([1]);
    expect(payload.isAssociatedWithCraneRequest).toBe(true);
    expect(payload.cranePickUpLocation).toBe('Location A');
    expect(payload.craneDropOffLocation).toBe('Location B');
  });

  it('should handle constructPayload without crane request', () => {
    component.deliverForm();
    component.selectedinspectionRequestIdForMultipleEdit = [1];
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.editedFields = 'status';
    component.isAssociatedWithCraneRequest = false;

    const payload = component.constructPayload();

    expect(payload.isAssociatedWithCraneRequest).toBe(false);
    expect(payload.cranePickUpLocation).toBe(null);
    expect(payload.craneDropOffLocation).toBe(null);
  });

  it('should handle openConfirmationPopup with edited fields', () => {
    component.deliverForm();
    const template = {} as any;

    // Set edited flags
    component.companyEdited = true;
    component.dfowEdited = true;
    component.escortEdited = true;
    component.responsiblePersonEdited = true;
    component.gateEdited = true;
    component.equipmentEdited = true;
    component.inspectionDateEdited = true;
    component.voidEdited = true;
    component.statusEdited = true;

    // Set form values that would trigger field inclusion
    component.deliverEditMultipleForm.patchValue({
      companyItems: [{ id: 1 }],
      defineItems: [{ id: 1 }],
      escort: true,
      person: [{ id: 1 }, { id: 2 }], // length > 1
      GateId: '1',
      EquipmentId: [{ id: 1 }],
      inspectionDate: new Date(),
      void: 'true',
      status: 'Approved'
    });

    component.openConfirmationPopup(template);

    expect(component.editedFields).toContain('Responsible Company');
    expect(component.editedFields).toContain('Definable Feature Of Work');
    expect(component.editedFields).toContain('Escort');
    expect(component.editedFields).toContain('Responsible Person');
    expect(component.editedFields).toContain('Gate');
    expect(component.editedFields).toContain('Equipment');
    expect(component.editedFields).toContain('inspection Date');
    expect(component.editedFields).toContain('Void');
    expect(component.editedFields).toContain('Status');
    expect(modalService.show).toHaveBeenCalledWith(template, {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
    });
  });

  it('should handle openConfirmationPopup with no edited fields', () => {
    component.deliverForm();
    const template = {} as any;

    // Reset all edited flags
    component.companyEdited = false;
    component.dfowEdited = false;
    component.escortEdited = false;
    component.responsiblePersonEdited = false;
    component.gateEdited = false;
    component.equipmentEdited = false;
    component.inspectionDateEdited = false;
    component.voidEdited = false;
    component.statusEdited = false;

    component.openConfirmationPopup(template);

    expect(component.editedFields).toBe('');
    expect(modalService.show).not.toHaveBeenCalled();
  });

  it('should handle editMultipleConfirmation with no action', () => {
    component.modalRef2 = mockModalRef;

    component.editMultipleConfirmation('no');

    expect(mockModalRef.hide).toHaveBeenCalled();
  });

  it('should handle editMultipleConfirmation with yes action', () => {
    component.modalRef2 = mockModalRef;
    const onEditMultipleSubmitSpy = jest.spyOn(component, 'onEditMultipleSubmit').mockImplementation();

    component.editMultipleConfirmation('yes');

    expect(mockModalRef.hide).toHaveBeenCalled();
    expect(onEditMultipleSubmitSpy).toHaveBeenCalled();
  });

  it('should handle showErrorMessage with status code 400', () => {
    const error = {
      message: {
        statusCode: 400,
        details: [{ field: 'Test error' }]
      }
    };
    const showErrorSpy = jest.spyOn(component, 'showError').mockImplementation();

    component.showErrorMessage(error);

    expect(component.editMultipleSubmitted).toBe(false);
    expect(showErrorSpy).toHaveBeenCalledWith(error);
  });

  it('should handle showErrorMessage without message', () => {
    const error = {};

    component.showErrorMessage(error);

    expect(component.editMultipleSubmitted).toBe(false);
    expect(toastr.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should handle showErrorMessage with generic message', () => {
    const error = { message: 'Generic error message' };

    component.showErrorMessage(error);

    expect(component.editMultipleSubmitted).toBe(false);
    expect(toastr.error).toHaveBeenCalledWith('Generic error message', 'OOPS!');
  });

  // Final comprehensive test cases for 90% coverage
  it('should handle editNDRSuccess', () => {
    component.modalRef = mockModalRef;
    component.deliverForm();
    const response = { message: 'Success message' };
    const deliverFormSpy = jest.spyOn(component, 'deliverForm').mockImplementation();

    component.editNDRSuccess(response);

    expect(toastr.success).toHaveBeenCalledWith('Success message', 'Success');
    expect(mockDeliveryService.updatedInspectionHistory).toHaveBeenCalledWith({ status: true }, 'NDREditHistory');
    expect(component.NDRTimingChanged).toBe(false);
    expect(component.companyEdited).toBe(false);
    expect(component.dfowEdited).toBe(false);
    expect(component.escortEdited).toBe(false);
    expect(component.responsiblePersonEdited).toBe(false);
    expect(component.gateEdited).toBe(false);
    expect(component.equipmentEdited).toBe(false);
    expect(component.voidEdited).toBe(false);
    expect(component.statusEdited).toBe(false);
    expect(component.inspectionDateEdited).toBe(false);
    expect(mockModalRef.hide).toHaveBeenCalled();
    expect(component.currentinspectionRequestSelectAll).toBe(false);
    expect(component.editMultipleSubmitted).toBe(false);
    expect(component.voidvalue).toBe(false);
    expect(component.selectedinspectionRequestId).toBe('');
    expect(component.craneEquipmentTypeChosen).toBe(false);
    expect(component.isAssociatedWithCraneRequest).toBe(false);
    expect(component.selectedinspectionRequestIdForMultipleEdit).toEqual([]);
  });

  it('should handle checkEquipmentType with crane equipment', () => {
    component.equipmentList = [
      { id: 1, PresetEquipmentType: { isCraneType: true } },
      { id: 2, PresetEquipmentType: { isCraneType: false } }
    ];
    const event = [{ id: 1 }, { id: 2 }];

    component.checkEquipmentType(event);

    expect(component.equipmentEdited).toBe(true);
    expect(component.craneEquipmentTypeChosen).toBe(true);
    expect(component.isAssociatedWithCraneRequest).toBe(true);
  });

  it('should handle checkEquipmentType with mixed equipment types', () => {
    component.equipmentList = [
      { id: 1, PresetEquipmentType: { isCraneType: false } },
      { id: 2, PresetEquipmentType: { isCraneType: true } }
    ];
    const event = [{ id: 1 }, { id: 2 }];

    component.checkEquipmentType(event);

    expect(component.equipmentEdited).toBe(true);
    expect(component.isAssociatedWithCraneRequest).toBe(true);
  });

  it('should handle onEditMultipleSubmit with inspection date validation errors', () => {
    component.deliverForm();
    component.inspectionDateEdited = true;
    component.deliverEditMultipleForm.patchValue({
      inspectionDate: new Date('2024-03-20'),
      inspectionStart: new Date('2024-03-20T10:00:00'),
      inspectionEnd: new Date('2024-03-20T10:00:00') // Same time
    });

    component.onEditMultipleSubmit();

    expect(toastr.error).toHaveBeenCalledWith('inspection Start time and End time should not be the same');
  });

  it('should handle onEditMultipleSubmit with invalid time order', () => {
    component.deliverForm();
    component.inspectionDateEdited = true;
    component.deliverEditMultipleForm.patchValue({
      inspectionDate: new Date('2024-03-20'),
      inspectionStart: new Date('2024-03-20T11:00:00'),
      inspectionEnd: new Date('2024-03-20T10:00:00') // End before start
    });

    component.onEditMultipleSubmit();

    expect(toastr.error).toHaveBeenCalledWith('Please Enter Start time Lesser than End time');
  });

  it('should handle onEditMultipleSubmit with past date', () => {
    component.deliverForm();
    component.inspectionDateEdited = true;
    const pastDate = new Date(Date.now() - 86400000); // Yesterday
    component.deliverEditMultipleForm.patchValue({
      inspectionDate: pastDate,
      inspectionStart: pastDate,
      inspectionEnd: new Date(pastDate.getTime() + 3600000) // 1 hour later
    });

    component.onEditMultipleSubmit();

    expect(toastr.error).toHaveBeenCalledWith('Please Enter Future Date.');
  });

  it('should handle onEditMultipleSubmit with crane request missing locations', () => {
    component.deliverForm();
    component.isAssociatedWithCraneRequest = true;
    component.inspectionDateEdited = false;
    component.deliverEditMultipleForm.patchValue({
      cranePickUpLocation: '',
      craneDropOffLocation: ''
    });

    component.onEditMultipleSubmit();

    expect(component.formSubmitted).toBe(false);
    expect(toastr.error).toHaveBeenCalledWith('Please enter Picking From and Picking To');
  });

  it('should handle onEditMultipleSubmit successful submission', () => {
    component.deliverForm();
    component.inspectionDateEdited = false;
    component.isAssociatedWithCraneRequest = false;
    component.selectedinspectionRequestIdForMultipleEdit = [1];
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.editedFields = 'status';

    const constructPayloadSpy = jest.spyOn(component, 'constructPayload').mockReturnValue({
      InspectionRequestIds: [1],
      ProjectId: '123',
      ParentCompanyId: '456'
    });

    component.onEditMultipleSubmit();

    expect(component.editMultipleSubmitted).toBe(true);
    expect(mockDeliveryService.updateInspectionRequest).toHaveBeenCalled();
  });

  it('should handle getinspectionRequest with filter form undefined', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.pageSize = 25;
    component.pageNo = 1;
    component.search = 'test';
    component.sort = 'ASC';
    component.sortColumn = 'id';
    component.filterForm = undefined;

    component.getinspectionRequest();

    expect(component.loader).toBe(true);
    expect(component.inspectionList).toEqual([]);
    expect(mockDeliveryService.listInspectionNDR).toHaveBeenCalled();
  });

  it('should handle getinspectionRequest with status filter Completed', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.filterForm.patchValue({ statusFilter: 'Completed' });

    component.getinspectionRequest();

    expect(mockDeliveryService.listInspectionNDR).toHaveBeenCalled();
  });

  it('should handle getinspectionRequest with date filter', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.filterForm.patchValue({
      dateFilter: new Date('2024-03-20'),
      descriptionFilter: 'test description'
    });

    component.getinspectionRequest();

    expect(mockDeliveryService.listInspectionNDR).toHaveBeenCalled();
  });

  it('should handle getinspectionRequest response with user role 4', () => {
    component.authUser = { RoleId: 4, id: '123' };
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    // Mock the response to trigger the role-based logic
    const mockResponse = {
      data: {
        rows: [
          {
            memberDetails: [
              { Member: { id: '123' } }, // User is in member details
              { Member: { id: '456' } }
            ]
          },
          {
            memberDetails: [
              { Member: { id: '456' } } // User is not in member details
            ]
          }
        ],
        count: 2
      },
      lastId: 100,
      statusData: {
        statusColorCode: JSON.stringify([
          { status: 'approved', backgroundColor: '#green', fontColor: '#white' },
          { status: 'pending', backgroundColor: '#yellow', fontColor: '#black' },
          { status: 'delivered', backgroundColor: '#blue', fontColor: '#white' },
          { status: 'rejected', backgroundColor: '#red', fontColor: '#white' },
          { status: 'expired', backgroundColor: '#gray', fontColor: '#white' }
        ])
      }
    };

    mockDeliveryService.listInspectionNDR.mockReturnValue(of(mockResponse));

    component.getinspectionRequest();

    expect(component.inspectionList[0].isAllowedToEdit).toBe(true);
    expect(component.inspectionList[1].isAllowedToEdit).toBe(false);
  });

  it('should handle getinspectionRequest response with user role 1', () => {
    component.authUser = { RoleId: 1, id: '123' };
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    const mockResponse = {
      data: {
        rows: [
          { memberDetails: [{ Member: { id: '456' } }] }
        ],
        count: 1
      },
      lastId: 100,
      statusData: {
        statusColorCode: JSON.stringify([
          { status: 'approved', backgroundColor: '#green', fontColor: '#white' },
          { status: 'pending', backgroundColor: '#yellow', fontColor: '#black' },
          { status: 'delivered', backgroundColor: '#blue', fontColor: '#white' },
          { status: 'rejected', backgroundColor: '#red', fontColor: '#white' },
          { status: 'expired', backgroundColor: '#gray', fontColor: '#white' }
        ])
      }
    };

    mockDeliveryService.listInspectionNDR.mockReturnValue(of(mockResponse));

    component.getinspectionRequest();

    expect(component.inspectionList[0].isAllowedToEdit).toBe(true);
  });
});
