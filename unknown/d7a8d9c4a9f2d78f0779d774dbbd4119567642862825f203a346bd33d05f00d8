import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HomePageMapComponent } from './home-page-map.component';
import { GoogleMapsModule } from '@angular/google-maps';
import { SimpleChange } from '@angular/core';
import { setupGoogleMapsMock, cleanupGoogleMapsMock } from '../../testing/google-maps-mock';

describe('HomePageMapComponent', () => {
  let component: HomePageMapComponent;
  let fixture: ComponentFixture<HomePageMapComponent>;

  beforeAll(() => {
    setupGoogleMapsMock();
  });

  afterAll(() => {
    cleanupGoogleMapsMock();
  });

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [HomePageMapComponent],
      imports: [GoogleMapsModule]
    }).compileComponents();

    fixture = TestBed.createComponent(HomePageMapComponent);
    component = fixture.componentInstance;

    // Mock the map instance
    component.map = {
      panTo: jest.fn()
    } as any;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.latitude).toBe(38.897957);
    expect(component.longitude).toBe(-77.036560);
    expect(component.mapOptions.center).toEqual({ lat: 38.897957, lng: -77.036560 });
    expect(component.marker.position).toEqual({ lat: 38.897957, lng: -77.036560 });
  });

  it('should update map when input values change', () => {
    const newLatitude = 40.7128;
    const newLongitude = -74.0060;

    const changes = {
      emitLatitude: new SimpleChange(null, newLatitude, true),
      emitLongitude: new SimpleChange(null, newLongitude, true)
    };

    component.updateMap(changes.emitLatitude, changes.emitLongitude);

    expect(component.latitude).toBe(newLatitude);
    expect(component.longitude).toBe(newLongitude);
    expect(component.mapOptions.center).toEqual({ lat: newLatitude, lng: newLongitude });
    expect(component.marker.position).toEqual({ lat: newLatitude, lng: newLongitude });
  });

  it('should handle marker drag end event', () => {
    const mockEvent = {
      latLng: {
        lat: () => 40.7128,
        lng: () => -74.0060
      }
    } as google.maps.MapMouseEvent;

    const emitSpy = jest.spyOn(component.latLongProps, 'emit');
    const panToSpy = jest.spyOn(component.map, 'panTo');

    component.markerDragEnd(mockEvent);

    expect(component.latitude).toBe(40.7128);
    expect(component.longitude).toBe(-74.0060);
    expect(component.mapOptions.center).toEqual({ lat: 40.7128, lng: -74.0060 });
    expect(component.marker.position).toEqual({ lat: 40.7128, lng: -74.0060 });
    expect(emitSpy).toHaveBeenCalledWith({ lat: 40.7128, lng: -74.0060 });
    expect(panToSpy).toHaveBeenCalledWith({ lat: 40.7128, lng: -74.0060 });
  });

  it('should not update position when marker drag event has no latLng', () => {
    const mockEvent = {
      latLng: null
    } as google.maps.MapMouseEvent;

    const emitSpy = jest.spyOn(component.latLongProps, 'emit');
    const panToSpy = jest.spyOn(component.map, 'panTo');

    component.markerDragEnd(mockEvent);

    expect(emitSpy).not.toHaveBeenCalled();
    expect(panToSpy).not.toHaveBeenCalled();
  });

  it('should call panTo in ngAfterViewInit', () => {
    const panToSpy = jest.spyOn(component.map, 'panTo');
    component.ngAfterViewInit();
    expect(panToSpy).toHaveBeenCalledWith(component.marker.position);
  });
});
