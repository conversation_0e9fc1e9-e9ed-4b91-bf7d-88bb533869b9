/* eslint-disable max-lines-per-function */
/* eslint-disable max-len */
import { Component, TemplateRef, OnInit } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import moment from 'moment';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { Title } from '@angular/platform-browser';
import { Observable } from 'rxjs';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { EditCraneRequestComponent } from '../edit-crane-request/edit-crane-request.component';
import { NewCraneRequestCreationFormComponent } from '../new-crane-request-creation-form/new-crane-request-creation-form.component';
import { EditDeliveryFormComponent } from '../../delivery-requests/delivery-details/edit-delivery-form/edit-delivery-form.component';
import { DeliveryDetailsNewComponent } from '../../delivery-requests/delivery-details/delivery-details-new/delivery-details-new.component';
import { CraneRequestDetailViewHeaderComponent } from '../crane-request-detail-view-header/crane-request-detail-view-header.component';

type DateInput = string | number | Date;

@Component({
  selector: 'app-crane-request-grid',
  templateUrl: './crane-request-grid.component.html',
})
export class CraneRequestGridComponent implements OnInit {
  public currentPageNo = 1;

  public userData = [];

  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public ProjectId: any;

  public loader = true;

  public pageSize = 25;

  public pageNo = 1;

  public totalCount = 0;

  public lastId: any = 0;

  public deliveryList: any = [];

  public companyList: any = [];

  public defineList: any = [];

  public defineListData: any = [];

  public equipmentList: any = [];

  public currentTemplate: TemplateRef<any>;

  public modalLoader = false;

  public filterCount = 0;

  public authUser: any = {};

  public statusValue: any = [];

  public currentDeliveryIndex: any = {};

  public currentEditItem: any = {};

  public currentStatus = '';

  public search = '';

  public filterForm: UntypedFormGroup;

  public memberList: any = [];

  public wholeStatus = ['Approved', 'Completed', 'Declined', 'Delivered', 'Pending'];

  public ParentCompanyId: any;

  public newNdrDefinableDropdownSettings: IDropdownSettings;

  public newNdrCompanyDropdownSettings: IDropdownSettings;

  public modalRef2: BsModalRef;

  public craneLoader: boolean;

  public craneRequestList: any[];

  public craneRequestPageSize = 25;

  public craneRequestPageNo = 1;

  public craneRequestTotalCount = 0;

  public craneRequestLastId: any = 0;

  public currentlySelectedTab = 'tab0';

  public showSearchbar = false;

  public param1: any;

  public param2: any;

  public url: any;

  public sortColumn = 'CraneRequestId';

  public sort = 'DESC';

  public currentDeliveryRequestSelectAll = false;

  public selectedCraneRequestId = '';

  public selectedCraneRequestIdForMultipleEdit = [];

  public selectedCraneListStatus = '';

  public selectedstatuslength: number;

  public statustype: any;

  public editModalLoader = false;

  public editMultipleSubmitted = false;

  public companyEdited = false;

  public dfowEdited = false;

  public escortEdited = false;

  public responsiblePersonEdited = false;

  public equipmentEdited = false;

  public deliveryDateEdited = false;

  public formEditSubmitted = false;

  public craneEditMultipleForm: UntypedFormGroup;

  public editedFields = '';

  public deliveryStart: string | Date;

  public deliveryEnd: string | Date;

  public NDRTimingChanged = false;

  public formEdited = false;

  public submitted = false;

  public formSubmitted = false;

  public voidvalue = false;

  public voidEdited = false;

  public statusEdited = false;

  public approvedBackgroundColor: string;

  public approvedFontColor: string;

  public rejectedBackgroundColor: string;

  public rejectedFontColor: string;

  public deliveredBackgroundColor: string;

  public deliveredFontColor: string;

  public pendingBackgroundColor: string;

  public pendingFontColor: string;

  public expiredBackgroundColor: string;

  public expiredFontColor: string;

  public locationList: any = [];

  public equipmentDropdownSettings: IDropdownSettings;

  public selectedBookingsRequestType = [];

  public equipmentSelected = false;

  public noEquipmentOption = { id: 0, equipmentName: 'No Equipment Needed' };

  public constructor(
    private readonly modalService: BsModalService,
    public projectService: ProjectService,
    private readonly formBuilder: UntypedFormBuilder,
    public router: Router,
    public socket: Socket,
    private readonly toastr: ToastrService,
    private readonly deliveryService: DeliveryService,
    private readonly titleService: Title,
    private readonly route: ActivatedRoute,
  ) {
    this.titleService.setTitle('Follo - Crane Requests');
    /**/
    this.projectService.projectParent.subscribe((response19): void => {
      if (response19 !== undefined && response19 !== null && response19 !== '') {
        this.loader = true;
        this.deliveryList = [];
        this.ProjectId = response19.ProjectId;
        this.ParentCompanyId = response19.ParentCompanyId;
        this.getCraneRequest();
        this.getMembers();
        this.getOverAllEquipmentInNewDelivery();
      }
    });
    this.projectService.ParentCompanyId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ParentCompanyId = res;
      }
    });
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
        if (this.authUser.RoleId === 2 || this.authUser.RoleId === 1) {
          this.statusValue = ['Approved', 'Declined'];
        } else if (this.authUser.RoleId === 3) {
          this.statusValue = ['Delivered', 'Approved'];
        }
      }
    });
    this.filterDetailsForm();
  }

  public handleInputChange(event): void {
    this.voidvalue = false;
    const checkbox = document.getElementById('subscribe') as HTMLInputElement | null;
    if (checkbox.checked === true) {
      this.voidvalue = true;
    } else {
      this.voidvalue = false;
    }
  }

  public getOverAllEquipmentInNewDelivery(): void {
    const newNdrGetEquipmentsParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listCraneEquipment(newNdrGetEquipmentsParams, {
        showActivatedAlone: true,
      })
      .subscribe((equipmentListResponseForNewNdr): void => {
        this.equipmentList = [this.noEquipmentOption, ...equipmentListResponseForNewNdr.data.rows];
        this.equipmentDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'equipmentName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
        this.newNdrgetCompanies();
      });
  }

  public openModal1(template: TemplateRef<any>): void {
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-sm filter-popup custom-modal',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public openNewCraneRequest(): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.deliveryService.updateStateOfNDR('crane');
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(NewCraneRequestCreationFormComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
    });
    this.modalRef.content.closeBtnName = 'Close';
  }

  public openIdModal(
    item: { requestType: string; CraneRequestId: any; ProjectId: any; id: any },
    ndrStatus: any,
  ): void {
    this.deliveryService.updateStateOfNDR(ndrStatus);
    if (item.requestType === 'craneRequest') {
      const newPayload = {
        id: item.CraneRequestId,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      const initialState = {
        data: newPayload,
        title: 'Modal with component',
      };
      this.modalRef = this.modalService.show(CraneRequestDetailViewHeaderComponent, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal',
        initialState,
      });
    } else {
      const newPayload = {
        id: item.id,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      const initialState = {
        data: newPayload,
        title: 'Modal with component',
      };
      this.modalRef = this.modalService.show(DeliveryDetailsNewComponent, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal',
        initialState,
      });
    }
    this.modalRef.content.closeBtnName = 'Close';
  }

  public openEditModal(item, ndrState: string): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.deliveryService.updateStateOfNDR(ndrState);
    const className = 'modal-lg new-delivery-popup custom-modal';
    if (
      ndrState === 'crane'
      && !item.isAssociatedWithDeliveryRequest
      && !item.isAssociatedWithCraneRequest
    ) {
      this.deliveryService.updatedEditCraneRequestId(+item.CraneRequestId);
      this.modalRef = this.modalService.show(EditCraneRequestComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
      });
    } else {
      this.deliveryService.updatedDeliveryId(item.id);
      this.modalRef = this.modalService.show(EditDeliveryFormComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
      });
    }
    this.modalRef.content.closeBtnName = 'Close';
    this.modalRef.content.seriesOption = 1;
    this.modalRef.content.recurrenceId = item?.recurrence ? item?.recurrence?.id : null;
    this.modalRef.content.recurrenceEndDate = item?.recurrence
      ? item?.recurrence?.recurrenceEndDate
      : null;
  }

  public newNdrgetCompanies(): void {
    const newNdrGetCompaniesParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getCompanies(newNdrGetCompaniesParams)
      .subscribe((companiesResponseForNewNdr: any): void => {
        if (companiesResponseForNewNdr) {
          this.companyList = companiesResponseForNewNdr.data;
          this.getDefinable();
          this.newNdrCompanyDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'companyName',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
        }
      });
  }

  public getDefinable(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getDefinableWork(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.getLocations();
        this.defineListData = data;
        this.newNdrDefinableDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'DFOW',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          allowSearchFilter: true,
        };
      }
    });
  }

  public getLocations(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getLocations(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.locationList = data;
      }
    });
  }

  public ngOnInit(): void {
    this.router.events.subscribe((e): void => {
      if (this.modalRef) {
        this.modalRef.hide();
      }
    });
    this.deliveryService.fetchData.subscribe((getNdrResponse): void => {
      if (getNdrResponse !== undefined && getNdrResponse !== null && getNdrResponse !== '') {
        this.getCraneRequest();
      }
    });
  }

  public sortByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getCraneRequest();
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.search = '';
    this.pageNo = 1;
    this.filterDetailsForm();
    this.getCraneRequest();
    this.modalRef.hide();
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.modalRef.hide();
    }
  }

  public changeCraneRequestPageNo(pageNo: number): void {
    this.craneRequestPageNo = pageNo;
    this.getCraneRequest();
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.pageNo = 1;
    this.getCraneRequest();
  }

  public getSearch(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.pageNo = 1;
    this.search = data;
    this.getCraneRequest();
  }

  public changeCranePageSize(pageSize: number): void {
    this.craneRequestPageSize = pageSize;
    this.getCraneRequest();
  }

  public selectStatus(status: string): void {
    this.currentStatus = status;
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('descriptionFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('dateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('companyFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('memberFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('equipmentFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('locationFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('statusFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('pickFrom').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('pickTo').value !== '') {
      this.filterCount += 1;
    }
    this.pageNo = 1;
    this.getCraneRequest();
    this.modalRef.hide();
  }

  public redirect(path: any): void {
    this.router.navigate([`/${path}`]);
  }

  public getOverAllEquipmentForNdrGrid(): void {
    const getOverAllEquipmentForNdrGridParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listEquipment(getOverAllEquipmentForNdrGridParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((getOverAllEquipmentForNdrGridResponse): void => {
        this.equipmentList = [this.noEquipmentOption, ...getOverAllEquipmentForNdrGridResponse.data];
        this.getCompaniesForNdrGrid();
      });
  }

  public getDefinableForNdrGrid(): void {
    const getDefinableForNdrGridParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getDefinableWork(getDefinableForNdrGridParams)
      .subscribe((getDefinableForNdrGridResponse: any): void => {
        if (getDefinableForNdrGridResponse) {
          const { data } = getDefinableForNdrGridResponse;
          this.defineList = data;
        }
      });
  }

  public getCompaniesForNdrGrid(): void {
    const getCompaniesForNdrGridParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getCompanies(getCompaniesForNdrGridParams)
      .subscribe((getCompaniesForNdrGridResponse: any): void => {
        if (getCompaniesForNdrGridResponse) {
          this.companyList = getCompaniesForNdrGridResponse.data;
          this.getDefinableForNdrGrid();
        }
      });
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      companyFilter: [''],
      descriptionFilter: [''],
      statusFilter: [''],
      dateFilter: [''],
      memberFilter: [''],
      equipmentFilter: [''],
      locationFilter: [''],
      pickFrom: [''],
      pickTo: [''],
    });
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }

  public getCraneRequest(): void {
    this.craneLoader = true;
    this.craneRequestList = [];
    const getCraneRequestRequestParam = {
      ProjectId: this.ProjectId,
      pageSize: this.craneRequestPageSize,
      pageNo: this.craneRequestPageNo,
      void: 0,
    };
    let getCraneRequestPayload: any = {};
    if (this.filterForm !== undefined) {
      const getDeliveryRequestDateInFilterForm = this.filterForm.value.dateFilter
        ? moment(this.filterForm.value.dateFilter).format('YYYY-MM-DD')
        : this.filterForm.value.dateFilter;
      getCraneRequestPayload = {
        companyFilter: +this.filterForm.value.companyFilter,
        descriptionFilter: this.filterForm.value.descriptionFilter,
        dateFilter: getDeliveryRequestDateInFilterForm,
        statusFilter: this.filterForm.value.statusFilter,
        memberFilter: +this.filterForm.value.memberFilter,
        equipmentFilter: this.filterForm.value.equipmentFilter === null || this.filterForm.value.equipmentFilter === '' ? null : +this.filterForm.value.equipmentFilter,
        locationFilter: this.filterForm.value.locationFilter,
        pickFrom: this.filterForm.value.pickFrom,
        pickTo: this.filterForm.value.pickTo,
        search: this.search,
      };
    }
    getCraneRequestPayload.search = this.search;
    getCraneRequestPayload.sort = this.sort;
    getCraneRequestPayload.sortByField = this.sortColumn;
    getCraneRequestPayload.ParentCompanyId = this.ParentCompanyId;
    this.deliveryService
      .getCraneRequestList(getCraneRequestRequestParam, getCraneRequestPayload)
      .subscribe((response: any): void => {
        if (response) {
          const responseData = response.data;
          this.craneRequestLastId = response.lastId;
          const statusCode = JSON.parse(response?.statusData.statusColorCode);

          const approved = statusCode.find((item) => item.status === 'approved');
          const pending = statusCode.find((item) => item.status === 'pending');
          const delivered = statusCode.find((item) => item.status === 'delivered');
          const rejected = statusCode.find((item) => item.status === 'rejected');
          const expired = statusCode.find((item) => item.status === 'expired');

          this.approvedBackgroundColor = approved.backgroundColor;
          this.approvedFontColor = approved.fontColor;
          this.rejectedBackgroundColor = rejected.backgroundColor;
          this.rejectedFontColor = rejected.fontColor;
          this.expiredBackgroundColor = expired.backgroundColor;
          this.expiredFontColor = expired.fontColor;
          this.deliveredBackgroundColor = delivered.backgroundColor;
          this.deliveredFontColor = delivered.fontColor;
          this.pendingBackgroundColor = pending.backgroundColor;
          this.pendingFontColor = pending.fontColor;

          this.craneLoader = false;
          this.craneRequestList = responseData.rows;
          this.craneRequestList.forEach((item: any, indexValue: string | number): void => {
            if (this.authUser.RoleId === 4 || this.authUser.RoleId === 3) {
              const responsibleMemberArray = item.memberDetails;
              const index = responsibleMemberArray.findIndex(
                (i: { Member: { User: any } }): boolean => i.Member.User.id === this.authUser.UserId,
              );
              if (index !== -1) {
                this.craneRequestList[indexValue].isAllowedToEdit = true;
              } else {
                this.craneRequestList[indexValue].isAllowedToEdit = false;
              }
            } else {
              this.craneRequestList[indexValue].isAllowedToEdit = true;
            }
            return null;
          });
          this.craneRequestTotalCount = responseData.count;
          const isTokenExists = localStorage.getItem('token');
          if (isTokenExists) {
            this.route.queryParams.subscribe((params): any => {
              this.param1 = params.requestId;
              this.param2 = params.memberId;
              this.url = this.router.url.split('?');
              this.router.navigateByUrl(this.url[0]);
              if (this.param1 && this.param2) {
                const object = {
                  encryptedRequestId: this.param1,
                  encryptedMemberId: this.param2,
                  ProjectId: +localStorage.getItem('ProjectId'),
                  ParentCompanyId: +localStorage.getItem('currentCompanyId'),
                };
                this.deliveryService.decryption(object).subscribe((res): void => {
                  if (res?.data) {
                    const requestId = res.data.decryptedRequestId;
                    const newPayload = {
                      id: +requestId,
                      ProjectId: +localStorage.getItem('ProjectId'),
                      ParentCompanyId: +localStorage.getItem('currentCompanyId'),
                    };
                    if (newPayload.ProjectId > 0 && newPayload.ParentCompanyId > 0) {
                      const initialState = {
                        data: newPayload,
                        title: 'Modal with component',
                      };
                      this.modalRef = this.modalService.show(
                        CraneRequestDetailViewHeaderComponent,
                        {
                          backdrop: 'static',
                          keyboard: false,
                          class: 'modal-lg new-delivery-popup custom-modal',
                          initialState,
                        },
                      );
                    }
                  }
                });
              }
            });
          } else {
            this.router.navigate(['/login']);
          }
        }
      });
  }

  public selectAllCurrentDeliveryRequestForEdit(): void {
    this.selectedCraneRequestId = '';
    this.selectedCraneListStatus = '';
    this.selectedCraneRequestIdForMultipleEdit = [];
    this.selectedBookingsRequestType = [];
    this.currentDeliveryRequestSelectAll = !this.currentDeliveryRequestSelectAll;
    if (this.currentDeliveryRequestSelectAll) {
      this.craneRequestList.forEach((obj, element): void => {
        if (obj.isAllowedToEdit) {
          this.craneRequestList[element].isChecked = true;
          this.selectedCraneRequestId += `${this.craneRequestList[element].CraneRequestId},`;
          this.selectedCraneListStatus += `${this.craneRequestList[element].status},`;
          this.selectedCraneRequestIdForMultipleEdit.push(this.craneRequestList[element].id);
        } else {
          this.craneRequestList[element].isChecked = false;
        }
        return null;
      });
      this.selectedCraneRequestId = this.selectedCraneRequestId.replace(/,\s*$/, '');
      const output = this.selectedCraneListStatus.split(',');
      const arr = output.filter((item): any => item);
      const unique = arr.filter((item, i, ar): any => ar.indexOf(item) === i);
      this.statustype = unique;
      this.selectedstatuslength = unique.length;
    } else {
      this.craneRequestList.forEach((obj, element): void => {
        this.craneRequestList[element].isChecked = false;
      });
    }
  }

  public setSelectedCurrentDeliveryRequestItem(index: string | number): void {
    this.craneRequestList[index].isChecked = !this.craneRequestList[index].isChecked;
    if (this.craneRequestList[index].isChecked) {
      this.selectedCraneRequestId += `${this.craneRequestList[index].CraneRequestId},`;
      this.selectedCraneRequestIdForMultipleEdit.push(this.craneRequestList[index].id);
      this.selectedCraneListStatus += `${this.craneRequestList[index].status},`;
      const output = this.selectedCraneListStatus.split(',');
      const arr = output.filter((item): any => item);
      const unique = arr.filter((item, i, ar): any => ar.indexOf(item) === i);
      this.statustype = unique;
      this.selectedstatuslength = unique.length;
    } else {
      const selectedIds = this.selectedCraneRequestId.replace(
        `${this.craneRequestList[index].CraneRequestId},`,
        '',
      );
      this.selectedCraneRequestId = '';
      this.selectedCraneRequestId = selectedIds;
      const ifDeliveryIdExists = this.selectedCraneRequestIdForMultipleEdit.indexOf(
        this.craneRequestList[index].id,
      );
      if (ifDeliveryIdExists > -1) {
        this.selectedCraneRequestIdForMultipleEdit.splice(ifDeliveryIdExists, 1);
      }
    }
  }

  public checkIfCurrentDeliveryRequestRowSelected(): boolean {
    if (this.currentDeliveryRequestSelectAll) {
      return false;
    }
    const indexFind = this.craneRequestList.findIndex(
      (item: { isChecked: boolean }): boolean => item.isChecked === true,
    );
    if (indexFind !== -1) {
      return false;
    }
    return true;
  }

  public closeEditMultiplePopup(): void {
    this.formEditSubmitted = false;
    this.modalRef.hide();
    this.editModalLoader = false;
    this.selectedCraneRequestId = '';
    this.selectedCraneListStatus = '';
    this.craneRequestList.forEach((element): void => {
      element.isChecked = false;
    });
    this.currentDeliveryRequestSelectAll = false;
    this.selectedCraneRequestIdForMultipleEdit = [];
    this.selectedBookingsRequestType = [];
    this.editMultipleSubmitted = false;
    this.companyEdited = false;
    this.dfowEdited = false;
    this.escortEdited = false;
    this.responsiblePersonEdited = false;
    this.equipmentEdited = false;
    this.voidEdited = false;
    this.statusEdited = false;
    this.deliveryDateEdited = false;
    this.craneEditMultipleForm.reset();
    this.selectedBookingsRequestType = [];
    this.equipmentSelected = false;
  }

  public craneForm(): void {
    this.craneEditMultipleForm = this.formBuilder.group({
      EquipmentId: [this.formBuilder.array([])],
      person: [''],
      deliveryDate: [''],
      deliveryStart: [''],
      deliveryEnd: [''],
      escort: [false],
      companyItems: [this.formBuilder.array([])],
      defineItems: [this.formBuilder.array([])],
      status: [''],
      void: [''],
      cranePickUpLocation: [''],
      craneDropOffLocation: [''],
    });
  }

  public onEditSubmitForm(fieldName: string): void {
    if (fieldName === 'companies') {
      this.companyEdited = true;
    }
    if (fieldName === 'dfow') {
      this.dfowEdited = true;
    }
    if (fieldName === 'escort') {
      this.escortEdited = true;
    }
    if (fieldName === 'persons') {
      this.responsiblePersonEdited = true;
    }
    if (fieldName === 'equipment') {
      this.equipmentEdited = true;
    }
    if (fieldName === 'deliveryDate') {
      if (this.craneEditMultipleForm.get('deliveryDate').value) {
        this.setDefaultDeliveryTime();
        this.deliveryDateEdited = true;
      } else {
        this.deliveryDateEdited = false;
        this.craneEditMultipleForm.get('deliveryStart').setValue('');
        this.craneEditMultipleForm.get('deliveryEnd').setValue('');
      }
    }
    if (fieldName === 'void') {
      this.voidEdited = true;
    }
    if (fieldName === 'status') {
      this.statusEdited = true;
    }
  }

  public setDefaultDeliveryTime(): void {
    const setStartTime = 7;
    this.deliveryStart = new Date();
    this.deliveryStart.setHours(setStartTime);
    this.deliveryStart.setMinutes(0);
    this.deliveryEnd = new Date();
    this.deliveryEnd.setHours(setStartTime + 1);
    this.deliveryEnd.setMinutes(0);
    this.craneEditMultipleForm.get('deliveryStart').setValue(this.deliveryStart);
    this.craneEditMultipleForm.get('deliveryEnd').setValue(this.deliveryEnd);
  }

  public numberOnly(event: { which: any; keyCode: any }): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public deliveryEndTimeChangeDetection(): void {
    this.NDRTimingChanged = true;
  }

  public changeDate(event: any): void {
    if (!this.editModalLoader) {
      const startTime = new Date(event).getHours();
      const minutes = new Date(event).getMinutes();
      this.deliveryEnd = new Date();
      this.deliveryEnd.setHours(startTime + 1);
      this.deliveryEnd.setMinutes(minutes);
      this.craneEditMultipleForm.get('deliveryEnd').setValue(this.deliveryEnd);
      this.NDRTimingChanged = true;
    }
  }

  public requestAutocompleteItems = (text: string): Observable<any> => {
    const param = {
      ProjectId: this.ProjectId,
      search: text,
      ParentCompanyId: this.ParentCompanyId,
    };
    return this.deliveryService.searchNewMember(param);
  };

  public editMultipleConfirmation(action: string): void {
    if (action === 'no') {
      this.modalRef2.hide();
    } else {
      this.modalRef2.hide();
      this.onEditMultipleSubmit();
    }
  }

  public openConfirmationPopup(template: TemplateRef<any>): void {
    this.editedFields = '';

    const fieldChecks = [
      {
        edited: this.companyEdited,
        formControl: 'companyItems',
        condition: (val: any) => val?.length > 0,
        label: 'Responsible Company',
      },
      {
        edited: this.dfowEdited,
        formControl: 'defineItems',
        condition: (val: any) => val?.length > 0,
        label: 'Definable Feature Of Work',
      },
      {
        edited: this.escortEdited,
        formControl: 'escort',
        condition: (val: any) => val === true,
        label: 'Escort',
      },
      {
        edited: this.responsiblePersonEdited,
        formControl: 'person',
        condition: (val: any) => val?.length > 1,
        label: 'Responsible Person',
      },
      {
        edited: this.equipmentEdited,
        formControl: 'EquipmentId',
        condition: (val: any) => val?.length > 0,
        label: 'Equipment',
      },
      {
        edited: this.deliveryDateEdited,
        formControl: 'deliveryDate',
        condition: (val: any) => !!val,
        label: 'Delivery Date',
      },
      {
        edited: this.voidEdited,
        formControl: 'void',
        condition: (val: any) => !!val,
        label: 'voidoid',
      },
      {
        edited: this.statusEdited,
        formControl: 'status',
        condition: (val: any) => !!val,
        label: 'Status',
      },
    ];

    this.editedFields = fieldChecks
      .filter(({ edited, formControl, condition }) => edited && condition(this.craneEditMultipleForm.get(formControl)?.value))
      .map(({ label }) => label)
      .join(', ');

    if (this.editedFields) {
      const data = {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      };
      this.modalRef2 = this.modalService.show(template, data);
    }
  }


  public openEditMultipleModal(template: TemplateRef<any>): void {
    this.craneForm();
    this.setDefaultPerson();
    this.currentTemplate = template;
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-delivery-popup custom-modal edit-multiple-modal',
    };
    this.modalRef = this.modalService.show(this.currentTemplate, data);
    this.selectedCraneRequestId = this.selectedCraneRequestId.replace(/,\s*$/, '');
  }

  public setDefaultPerson(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.getMemberRole(params).subscribe((res): void => {
      this.authUser = res.data;
      this.deliveryService.updateLoginUser(this.authUser);
      let email: string;
      if (this.authUser.User.lastName != null) {
        email = `${this.authUser.User.firstName} ${this.authUser?.User?.lastName} (${this.authUser.User.email})`;
      } else {
        email = `${this.authUser.User.firstName} (${this.authUser.User.email})`;
      }
      const newMemberList = [
        {
          email,
          id: this.authUser.id,
          readonly: true,
        },
      ];
      this.craneEditMultipleForm.get('person').patchValue(newMemberList);
    });
  }

  public onEditMultipleSubmit(): void {
    if (!this.validateAndPrepareDeliveryData()) return;
    const payload = this.constructEditMultiplePayload();
    this.submitEditMultiplePayload(payload);
  }


  public validateAndPrepareDeliveryData(): boolean {
    if (this.deliveryDateEdited) {
      const deliveryDate = new Date(this.craneEditMultipleForm.get('deliveryDate').value);
      const startHours = new Date(this.craneEditMultipleForm.get('deliveryStart').value).getHours();
      const startMinutes = new Date(this.craneEditMultipleForm.get('deliveryStart').value).getMinutes();
      this.deliveryStart = this.convertStart(deliveryDate, startHours, startMinutes);

      const endHours = new Date(this.craneEditMultipleForm.get('deliveryEnd').value).getHours();
      const endMinutes = new Date(this.craneEditMultipleForm.get('deliveryEnd').value).getMinutes();
      this.deliveryEnd = this.convertStart(deliveryDate, endHours, endMinutes);

      if (this.checkNewDeliveryStartEndSame(this.deliveryStart, this.deliveryEnd)) {
        this.toastr.error('Delivery Start time and End time should not be the same');
        return false;
      }

      if (!this.checkStartEnd(this.deliveryStart, this.deliveryEnd)) {
        this.toastr.error('Please Enter Start time Lesser than End time');
        return false;
      }

      if (!this.checkEditDeliveryFutureDate(this.deliveryStart, this.deliveryEnd)) {
        this.toastr.error('Please Enter Future Date.');
        return false;
      }
    } else {
      this.deliveryStart = null;
      this.deliveryEnd = null;
    }

    if (
      this.equipmentEdited &&
      this.craneEditMultipleForm.get('EquipmentId').value?.length > 1 &&
      (!this.craneEditMultipleForm.get('cranePickUpLocation').value ||
        !this.craneEditMultipleForm.get('craneDropOffLocation').value)
    ) {
      this.formSubmitted = false;
      this.toastr.error('Please enter Picking From and Picking To');
      return false;
    }

    // Build selected request types
    if (this.selectedCraneRequestIdForMultipleEdit.length > 0) {
      this.selectedBookingsRequestType = [];
      this.selectedCraneRequestIdForMultipleEdit.forEach((selectedId) => {
        const request = this.craneRequestList.find((req) => req.id === selectedId);
        if (request) {
          this.selectedBookingsRequestType.push({
            id: request.id,
            requestType: request.requestType,
          });
        }
      });
    }

    return true;
  }


  public constructEditMultiplePayload(): any {
    const form = this.craneEditMultipleForm;
    const payload: any = {
      craneRequestIds: this.selectedCraneRequestIdForMultipleEdit,
      escort: form.get('escort').value,
      deliveryStart: this.deliveryStart,
      deliveryEnd: this.deliveryEnd,
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      editedFields: this.editedFields,
      status: form.get('status').value,
      void: this.voidvalue,
      selectedBookingTypes: this.selectedBookingsRequestType,
      companies: null,
      persons: null,
      define: null,
      EquipmentId: null,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      cranePickUpLocation: form.get('cranePickUpLocation').value?.trim(),
      craneDropOffLocation: form.get('craneDropOffLocation').value?.trim(),
      CraneRequestId: this.craneRequestLastId.CraneRequestId,
    };

    if (form.get('companyItems').value?.length > 0) {
      payload.companies = form.get('companyItems').value.map((item: { id: any }) => item.id);
    }

    if (form.get('person').value?.length > 0) {
      payload.persons = form.get('person').value.map((item: { id: any }) => item.id);
    }

    if (form.get('defineItems').value?.length > 0) {
      payload.define = form.get('defineItems').value.map((item: { id: any }) => item.id);
    }

    if (form.get('EquipmentId').value?.length > 0) {
      payload.EquipmentId = form.get('EquipmentId').value.map((item: { id: any }) => item.id);
    }

    return payload;
  }

  public submitEditMultiplePayload(payload: any): void {
    this.editMultipleSubmitted = true;
    this.deliveryService.updateCraneRequest(payload).subscribe({
      next: (response) => this.editNDRSuccess(response),
      error: (error) => this.showErrorMessage(error),
    });
  }


  public convertStart(deliveryDate: Date, startHours: number, startMinutes: number): string {
    const fullYear = deliveryDate.getFullYear();
    const fullMonth = deliveryDate.getMonth();
    const date = deliveryDate.getDate();
    const deliveryNewStart = new Date(fullYear, fullMonth, date, startHours, startMinutes);
    const deliveryStart = deliveryNewStart.toUTCString();
    return deliveryStart;
  }

  public checkNewDeliveryStartEndSame(
    newDeliveryStartTime: DateInput,
    newDeliveryEndTime: DateInput,
  ): boolean {
    const startDate = new Date(newDeliveryStartTime).getTime();
    const endDate = new Date(newDeliveryEndTime).getTime();
    if (startDate === endDate) {
      return true;
    }
    return false;
  }

  public checkStartEnd(
    deliveryStart: DateInput,
    deliveryEnd: DateInput,
  ): boolean {
    const startDate = new Date(deliveryStart).getTime();
    const endDate = new Date(deliveryEnd).getTime();
    if (startDate < endDate) {
      return true;
    }
    return false;
  }

  public checkEditDeliveryFutureDate(
    editDeliveryStart: DateInput,
    editDeliveryEnd: DateInput,
  ): boolean {
    const editStartDate = new Date(editDeliveryStart).getTime();
    const editCurrentDate = new Date().getTime();
    const editEndDate = new Date(editDeliveryEnd).getTime();
    if (editStartDate > editCurrentDate && editEndDate > editCurrentDate) {
      return true;
    }
    return false;
  }

  public showErrorMessage(editNDRHistoryError): void {
    this.editMultipleSubmitted = false;
    this.currentDeliveryRequestSelectAll = false;
    this.modalRef.hide();
    this.NDRTimingChanged = false;
    this.companyEdited = false;
    this.dfowEdited = false;
    this.escortEdited = false;
    this.responsiblePersonEdited = false;
    this.equipmentEdited = false;
    this.statusEdited = false;
    this.voidEdited = false;
    this.deliveryDateEdited = false;
    this.selectedBookingsRequestType = [];
    this.selectedCraneRequestIdForMultipleEdit = [];
    this.craneRequestList.forEach((element): void => {
      element.isChecked = false;
    });
    if (editNDRHistoryError.message?.statusCode === 400) {
      this.showError(editNDRHistoryError);
    } else if (!editNDRHistoryError.message) {
      this.toastr.error('Try again later.!', 'Something went wrong.');
    } else {
      this.toastr.error(editNDRHistoryError.message, 'OOPS!');
    }
  }

  public editNDRSuccess(response: { message: string }): void {
    this.toastr.success(response.message, 'Success');
    this.socket.emit('CraneEditHistory', response);
    this.craneForm();
    this.deliveryService.updateCraneRequestHistory({ status: true }, 'CraneEditHistory');
    this.NDRTimingChanged = false;
    this.companyEdited = false;
    this.dfowEdited = false;
    this.escortEdited = false;
    this.responsiblePersonEdited = false;
    this.equipmentEdited = false;
    this.statusEdited = false;
    this.voidEdited = false;
    this.deliveryDateEdited = false;
    this.modalRef.hide();
    this.currentDeliveryRequestSelectAll = false;
    this.editMultipleSubmitted = false;
    this.voidvalue = false;
    this.selectedCraneRequestId = '';
    this.craneEditMultipleForm.reset();
    this.selectedBookingsRequestType = [];
    this.selectedCraneRequestIdForMultipleEdit = [];
    this.craneRequestList.forEach((element): void => {
      element.isChecked = false;
    });
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public EquipmentSelected(event: any): void {
    if (event.length > 0) {
      this.equipmentSelected = true;
      this.equipmentEdited = true;
    } else {
      this.equipmentSelected = false;
      this.equipmentEdited = false;
    }
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'clear':
          this.clear();
          break;
        case 'filter':
          this.openModal1(data);
          break;
        case 'open':
          this.openIdModal(data, item);
          break;
        case 'edit':
          this.openEditModal(data, item);
          break;
        default:
          break;
      }
    }
  }
}
