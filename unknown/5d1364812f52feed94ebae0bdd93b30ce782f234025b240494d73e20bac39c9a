import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { of, Subject } from 'rxjs';
import { HistoryComponent } from './history.component';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';

const setupMocks = () => {
  const mockDeliveryService = {
    DeliveryRequestId: new Subject<any>(),
    refresh: new Subject<any>(),
    refresh1: new Subject<any>(),
    getHistory: jest.fn(),
  };

  const mockProjectService = {
    projectParent: new Subject<any>(),
  };

  return { mockDeliveryService, mockProjectService };
};

describe('HistoryComponent - Basic Setup', () => {
  let component: HistoryComponent;
  let fixture: ComponentFixture<HistoryComponent>;
  const { mockDeliveryService, mockProjectService } = setupMocks();

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [HistoryComponent],
      imports: [HttpClientTestingModule, RouterTestingModule],
      providers: [
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: ProjectService, useValue: mockProjectService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(HistoryComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.historyList).toEqual([]);
    expect(component.loader).toBe(true);
  });

  it('should unsubscribe from subscriptions on destroy', () => {
    const unsubscribeSpy = jest.spyOn(component['subscription'], 'unsubscribe');
    component.ngOnDestroy();
    expect(unsubscribeSpy).toHaveBeenCalled();
  });
});

describe('HistoryComponent - Project Updates', () => {
  let component: HistoryComponent;
  let fixture: ComponentFixture<HistoryComponent>;
  let projectService: jest.Mocked<ProjectService>;
  const { mockDeliveryService, mockProjectService } = setupMocks();

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [HistoryComponent],
      imports: [HttpClientTestingModule, RouterTestingModule],
      providers: [
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: ProjectService, useValue: mockProjectService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(HistoryComponent);
    component = fixture.componentInstance;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
  });

  it('should update project details when projectParent emits', fakeAsync(() => {
    const mockProjectData = {
      ProjectId: '123',
      ParentCompanyId: '456',
    };

    projectService.projectParent.next(mockProjectData);
    tick(100);

    expect(component.ProjectId).toBe(mockProjectData.ProjectId);
    expect(component.ParentCompanyId).toBe(mockProjectData.ParentCompanyId);
    expect(component.loader).toBe(true);
  }));
});

describe('HistoryComponent - History Updates', () => {
  let component: HistoryComponent;
  let fixture: ComponentFixture<HistoryComponent>;
  let deliveryService: jest.Mocked<DeliveryService>;
  const { mockDeliveryService, mockProjectService } = setupMocks();

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [HistoryComponent],
      imports: [HttpClientTestingModule, RouterTestingModule],
      providers: [
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: ProjectService, useValue: mockProjectService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(HistoryComponent);
    component = fixture.componentInstance;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
  });

  it('should fetch history when DeliveryRequestId changes', fakeAsync(() => {
    const mockHistoryData = {
      data: [
        { type: 'status_change', description: 'Status changed to Approved' },
        { type: 'comment', description: 'Added a comment' },
      ],
    };

    mockDeliveryService.getHistory.mockReturnValue(of(mockHistoryData));
    component.ParentCompanyId = '456';
    const mockDeliveryRequestId = '789';

    deliveryService.DeliveryRequestId.next(mockDeliveryRequestId);
    tick(100);

    expect(component.DeliveryRequestId).toBe(mockDeliveryRequestId);
    expect(deliveryService.getHistory).toHaveBeenCalledWith({
      DeliveryRequestId: mockDeliveryRequestId,
      ParentCompanyId: '456',
    });
  }));

  it('should filter out comments from history list', fakeAsync(() => {
    const mockHistoryData = {
      data: [
        { type: 'status_change', description: 'Status changed to Approved' },
        { type: 'comment', description: 'Added a comment' },
        { type: 'attachment', description: 'File uploaded' },
      ],
    };

    mockDeliveryService.getHistory.mockReturnValue(of(mockHistoryData));
    component.ParentCompanyId = '456';
    const mockDeliveryRequestId = '789';

    deliveryService.DeliveryRequestId.next(mockDeliveryRequestId);
    tick(100);

    expect(component.historyList.length).toBe(2);
    expect(component.historyList.every((item: any) => item.type !== 'comment')).toBe(true);
  }));
});

describe('HistoryComponent - Refresh Events', () => {
  let component: HistoryComponent;
  let fixture: ComponentFixture<HistoryComponent>;
  let deliveryService: jest.Mocked<DeliveryService>;
  const { mockDeliveryService, mockProjectService } = setupMocks();

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [HistoryComponent],
      imports: [HttpClientTestingModule, RouterTestingModule],
      providers: [
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: ProjectService, useValue: mockProjectService },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(HistoryComponent);
    component = fixture.componentInstance;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
  });

  it('should handle refresh events', fakeAsync(() => {
    const mockHistoryData = {
      data: [{ type: 'status_change', description: 'Status changed to Approved' }],
    };

    mockDeliveryService.getHistory.mockReturnValue(of(mockHistoryData));
    component.ParentCompanyId = '456';
    component.DeliveryRequestId = '789';

    deliveryService.refresh.next({});
    tick(100);

    expect(deliveryService.getHistory).toHaveBeenCalled();
    expect(component.loader).toBe(false);
  }));

  it('should handle refresh1 events', fakeAsync(() => {
    const mockHistoryData = {
      data: [{ type: 'status_change', description: 'Status changed to Approved' }],
    };

    mockDeliveryService.getHistory.mockReturnValue(of(mockHistoryData));
    component.ParentCompanyId = '456';
    component.DeliveryRequestId = '789';

    deliveryService.refresh1.next({});
    tick(100);

    expect(deliveryService.getHistory).toHaveBeenCalled();
    expect(component.loader).toBe(false);
  }));
});
