import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { HomePageComponent } from './home-page.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Title } from '@angular/platform-browser';
import { AuthService } from '../services/auth/auth.service';
import { ProjectService } from '../services/profile/project.service';
import { ElementRef } from '@angular/core';
import { of, throwError } from 'rxjs';
import { NavigationEnd } from '@angular/router';
import { Subject } from 'rxjs';

// Mock Google Maps API
// Note: Window.google is already declared in global.d.ts

const mockGoogleMaps = {
  maps: {
    Geocoder: jest.fn().mockImplementation(() => ({
      geocode: jest.fn()
    }))
  }
};

Object.defineProperty(window, 'google', {
  value: mockGoogleMaps,
  writable: true
});

// Mock Geolocation API
const mockGeolocation = {
  getCurrentPosition: jest.fn().mockImplementation((success) => {
    success({
      coords: {
        latitude: 38.897957,
        longitude: -77.03656
      }
    });
  })
};

Object.defineProperty(navigator, 'geolocation', {
  value: mockGeolocation,
  writable: true
});

// Mock Permissions API
const mockPermissions = {
  query: jest.fn().mockImplementation(() => 
    Promise.resolve({ state: 'granted' })
  )
};

Object.defineProperty(navigator, 'permissions', {
  value: mockPermissions,
  writable: true
});

describe('HomePageComponent', () => {
  let component: HomePageComponent;
  let fixture: ComponentFixture<HomePageComponent>;
  let modalService: jest.Mocked<BsModalService>;
  let formBuilder: UntypedFormBuilder;
  let toastrService: jest.Mocked<ToastrService>;
  let projectService: jest.Mocked<ProjectService>;
  let authService: jest.Mocked<AuthService>;
  let router: jest.Mocked<Router>;
  let titleService: jest.Mocked<Title>;

  // Global localStorage mock
  const localStorageMock = {
    getItem: jest.fn().mockReturnValue(null),
    setItem: jest.fn(),
    removeItem: jest.fn()
  };

  beforeEach(async () => {
    // Set up localStorage mock
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true
    });
    const modalServiceMock = {
      show: jest.fn()
    };
    const toastrServiceMock = {
      success: jest.fn(),
      error: jest.fn(),
      clear: jest.fn()
    };
    const projectServiceMock = {
      createProject: jest.fn().mockReturnValue(of({ message: 'Success' })),
      updateMember: jest.fn().mockReturnValue(of({ message: 'Success' })),
      checkCurrentDomain: jest.fn().mockReturnValue(false)
    };
    const authServiceMock = {
      loggeduserIn: jest.fn().mockReturnValue(false),
      register: jest.fn(),
      login: jest.fn(),
      checkDialCode: jest.fn().mockReturnValue('(*************'),
      existEmail: jest.fn().mockReturnValue(of(true)),
      getUserDetail: jest.fn().mockReturnValue(of({
        memberDetail: {
          id: 1,
          isDeleted: false,
          status: 'pending',
          RoleId: 1,
          ProjectId: 1,
          Company: {
            id: 1,
            companyName: 'Test Company',
            address: 'Test Address',
            city: 'Test City',
            state: 'Test State',
            country: 'Test Country',
            zipCode: '12345',
            website: 'https://test.com'
          }
        },
        userDetail: {
          email: '<EMAIL>'
        }
      })),
      removeAlreadyInvitedMember: jest.fn().mockReturnValue(of({ message: 'Success' })),
      requestInvitedLink: jest.fn().mockReturnValue(of({ message: 'Success' }))
    };
    const routerMock = {
      navigate: jest.fn(),
      url: '/home'
    };
    const titleServiceMock = {
      setTitle: jest.fn()
    };

    await TestBed.configureTestingModule({
      declarations: [HomePageComponent],
      imports: [ReactiveFormsModule],
      providers: [
        { provide: BsModalService, useValue: modalServiceMock },
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: AuthService, useValue: authServiceMock },
        { provide: Router, useValue: routerMock },
        { provide: Title, useValue: titleServiceMock },
        UntypedFormBuilder,
        {
          provide: ActivatedRoute,
          useValue: {
            queryParams: of({}),
            snapshot: {
              queryParams: {}
            }
          }
        },
        {
          provide: ElementRef,
          useValue: {
            nativeElement: document.createElement('div')
          }
        }
      ]
    }).compileComponents();

    modalService = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    formBuilder = TestBed.inject(UntypedFormBuilder);
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    authService = TestBed.inject(AuthService) as jest.Mocked<AuthService>;
    router = TestBed.inject(Router) as jest.Mocked<Router>;
    titleService = TestBed.inject(Title) as jest.Mocked<Title>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(HomePageComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set title on initialization', () => {
    expect(titleService.setTitle).toHaveBeenCalledWith('Follo - Home');
  });

  it('should initialize forms', () => {
    expect(component.basicDetailsForm).toBeDefined();
    expect(component.companyDetailsForm).toBeDefined();
    expect(component.homePageProjectDetailsForm).toBeDefined();
  });

  describe('Form Validation', () => {
    it('should validate password format', () => {
      const validPassword = 'Test123!@#';
      const invalidPassword = 'weak';

      component.passwordValid({ target: { value: validPassword } });
      expect(component.passwordError).toBe(false);

      component.passwordValid({ target: { value: invalidPassword } });
      expect(component.passwordError).toBe(true);
    });

    it('should validate numeric input', () => {
      const numberEvent = { which: 48 }; // '0' key
      const letterEvent = { which: 65 }; // 'A' key

      expect(component.numberOnly(numberEvent)).toBe(true);
      expect(component.numberOnly(letterEvent)).toBe(false);
    });

    it('should validate alphanumeric input', () => {
      const letterEvent = { which: 65 }; // 'A' key
      const numberEvent = { which: 48 }; // '0' key
      const specialCharEvent = { which: 33 }; // '!' key
      const spaceEvent = { which: 32 }; // space key

      expect(component.alphaNum(letterEvent)).toBe(true);
      expect(component.alphaNum(numberEvent)).toBe(true);
      expect(component.alphaNum(specialCharEvent)).toBe(true); // Special chars are allowed
      expect(component.alphaNum(spaceEvent)).toBe(true); // Space is allowed
    });
  });

  describe('Password Toggle', () => {
    it('should toggle password visibility', () => {
      component.passwordType = 'password';
      component.togglePassword = false;
      component.passwordToggle();
      expect(component.togglePassword).toBe(true);
      expect(component.passwordType).toBe('password');

      component.passwordToggle();
      expect(component.togglePassword).toBe(false);
      expect(component.passwordType).toBe('password');
    });

    it('should toggle confirm password visibility', () => {
      component.confirmPasswordType = 'password';
      component.confirmTogglePassword = false;
      component.confirmPasswordToggle();
      expect(component.confirmTogglePassword).toBe(true);
      expect(component.confirmPasswordType).toBe('password');

      component.confirmPasswordToggle();
      expect(component.confirmTogglePassword).toBe(false);
      expect(component.confirmPasswordType).toBe('password');
    });
  });

  describe('Form Submission', () => {
    it('should handle successful form submission', fakeAsync(() => {
      const mockResponse = { success: true };
      authService.register.mockReturnValue(of(mockResponse));
      authService.existEmail.mockReturnValue(of(true));
      
      // Set up the form
      component.basicDetailsForm = formBuilder.group({
        email: ['<EMAIL>', [Validators.required, Validators.email]],
        phoneNumber: ['1234567890', [Validators.required]],
        phoneCode: ['+1', [Validators.required]],
        password: ['Test123!@#', [Validators.required]],
        confirmPassword: ['Test123!@#', [Validators.required]]
      });

      // Set initial state
      component.stepOne = true;
      component.StepTwo = false;
      component.basicSubmitted = false;
      component.formSubmitted = false;

      // Submit the form
      component.onSubmit('basic', null);
      tick();

      expect(authService.existEmail).toHaveBeenCalled();
      expect(component.stepOne).toBe(false);
      expect(component.StepTwo).toBe(true);
    }));

    it('should handle form submission error', fakeAsync(() => {
      const mockError = { error: { message: 'Error occurred' } };
      authService.existEmail.mockReturnValue(throwError(() => mockError));
      
      // Set up the form
      component.basicDetailsForm = formBuilder.group({
        email: ['<EMAIL>', [Validators.required, Validators.email]],
        phoneNumber: ['1234567890', [Validators.required]],
        phoneCode: ['+1', [Validators.required]],
        password: ['Test123!@#', [Validators.required]],
        confirmPassword: ['Test123!@#', [Validators.required]]
      });

      // Set initial state
      component.stepOne = true;
      component.StepTwo = false;
      component.basicSubmitted = false;
      component.formSubmitted = false;

      // Submit the form
      component.onSubmit('basic', null);
      tick();

      expect(toastrService.error).toHaveBeenCalled();
    }));
  });

  describe('Location Services', () => {
    it('should set current location', () => {
      const mockGeolocation = {
        getCurrentPosition: jest.fn().mockImplementation((success) => {
          success({
            coords: {
              latitude: 38.897957,
              longitude: -77.03656
            }
          });
        })
      };
      Object.defineProperty(navigator, 'geolocation', {
        value: mockGeolocation
      });

      component.setCurrentLocation();
      expect(component.latitude).toBe(38.897957);
      expect(component.longitude).toBe(-77.03656);
    });
  });

  describe('Modal Operations', () => {
    it('should open modal', () => {
      const template = {} as any;
      component.getStarted(template);
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should close modal', () => {
      component.modalRef = { hide: jest.fn() } as any;
      component.modalClose();
      expect(component.modalRef.hide).toHaveBeenCalled();
    });
  });

  describe('Navigation', () => {
    it('should navigate to dashboard on successful login', () => {
      // Set up the component state
      component.loggedIn = true;
      component.loginToken = true;
      component.stepOne = true;
      component.StepTwo = false;
      component.StepThree = false;
      
      // Mock the auth service
      authService.loggeduserIn.mockReturnValue(true);
      
      // Mock localStorage
      const localStorageMock = {
        getItem: jest.fn().mockReturnValue(JSON.stringify({ email: '<EMAIL>' })),
        setItem: jest.fn(),
        removeItem: jest.fn()
      };
      Object.defineProperty(window, 'localStorage', { value: localStorageMock });
      
      // Mock the modal service
      const mockModalRef = {
        hide: jest.fn(),
        setClass: jest.fn()
      } as unknown as BsModalRef;
      modalService.show.mockReturnValue(mockModalRef);
      
      // Call getStarted with a mock template
      const mockTemplate = {} as any;
      component.getStarted(mockTemplate);
      
      // Verify navigation
      expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
    });

    it('should navigate to dashboard when loginToken is true', () => {
      // Set up the component state
      component.loginToken = true;
      
      // Call getStarted with a mock template
      const mockTemplate = {} as any;
      component.getStarted(mockTemplate);
      
      // Verify navigation
      expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
    });
  });

  describe('Component Initialization', () => {
    it('should initialize with default values', () => {
      expect(component.stepOne).toBe(false);
      expect(component.StepTwo).toBe(false);
      expect(component.StepThree).toBe(false);
      expect(component.passwordType).toBe('password');
      expect(component.confirmPasswordType).toBe('password');
      expect(component.basicSubmitted).toBe(false);
      expect(component.projectSubmitted).toBe(false);
      expect(component.companySubmitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.loginToken).toBe(false);
      expect(component.loggedIn).toBe(false);
      expect(component.submitted).toBe(false);
      expect(component.togglePassword).toBe(false);
      expect(component.confirmTogglePassword).toBe(false);
      expect(component.phoneMask).toBe('(*************');
      expect(component.latitude).toBe(38.897957);
      expect(component.longitude).toBe(-77.03656);
    });

    it('should initialize dropdown settings', () => {
      expect(component.dropdownSettings).toEqual({
        singleSelection: true,
        idField: 'name',
        textField: 'countryDialCode',
        allowSearchFilter: false,
        closeDropDownOnSelection: true,
      });
    });

    it('should get country codes on initialization', () => {
      expect(component.countryCode.length).toBeGreaterThan(0);
      expect(component.countryCode[0]).toHaveProperty('countryDialCode');
      expect(component.countryCode[0]).toHaveProperty('name');
    });

    it('should set login token when user is logged in', () => {
      authService.loggeduserIn.mockReturnValue(true);
      const newComponent = new HomePageComponent(
        modalService,
        formBuilder,
        toastrService,
        projectService,
        authService,
        TestBed.inject(ActivatedRoute),
        TestBed.inject(ElementRef),
        titleService,
        router
      );
      expect(newComponent.loginToken).toBe(true);
    });
  });

  describe('Input Validation Methods', () => {
    it('should validate alpha only input', () => {
      const letterEvent = { keyCode: 65 }; // 'A' key
      const numberEvent = { keyCode: 48 }; // '0' key
      const backspaceEvent = { keyCode: 8 }; // backspace
      const spaceEvent = { keyCode: 32 }; // space
      const specialCharEvent = { keyCode: 33 }; // '!' key

      expect(component.alphaOnly(letterEvent)).toBe(true);
      expect(component.alphaOnly(numberEvent)).toBe(true);
      expect(component.alphaOnly(backspaceEvent)).toBe(true);
      expect(component.alphaOnly(spaceEvent)).toBe(true);
      expect(component.alphaOnly(specialCharEvent)).toBe(true);
    });

    it('should validate alphanumeric input with edge cases', () => {
      const deleteEvent = { which: 46 }; // delete key
      const tabEvent = { which: 9 }; // tab key
      const enterEvent = { which: 13 }; // enter key

      expect(component.alphaNum(deleteEvent)).toBe(true);
      expect(component.alphaNum(tabEvent)).toBe(true);
      expect(component.alphaNum(enterEvent)).toBe(true);
    });

    it('should validate number only input with edge cases', () => {
      const backspaceEvent = { which: 8 }; // backspace
      const deleteEvent = { which: 46 }; // delete
      const tabEvent = { which: 9 }; // tab
      const enterEvent = { which: 13 }; // enter

      expect(component.numberOnly(backspaceEvent)).toBe(true);
      expect(component.numberOnly(deleteEvent)).toBe(false);
      expect(component.numberOnly(tabEvent)).toBe(false);
      expect(component.numberOnly(enterEvent)).toBe(false);
    });
  });

  describe('Keyboard Event Handling', () => {
    it('should handle down keydown for password toggle', () => {
      const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
      const spaceEvent = new KeyboardEvent('keydown', { key: ' ' });
      const otherEvent = new KeyboardEvent('keydown', { key: 'a' });

      jest.spyOn(enterEvent, 'preventDefault');
      jest.spyOn(spaceEvent, 'preventDefault');
      jest.spyOn(component, 'passwordToggle');

      component.handleDownKeydown(enterEvent, 'password');
      expect(enterEvent.preventDefault).toHaveBeenCalled();
      expect(component.passwordToggle).toHaveBeenCalled();

      component.handleDownKeydown(spaceEvent, 'password');
      expect(spaceEvent.preventDefault).toHaveBeenCalled();

      component.handleDownKeydown(otherEvent, 'password');
      // Should not prevent default for other keys
    });

    it('should handle down keydown for confirm password toggle', () => {
      const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(enterEvent, 'preventDefault');
      jest.spyOn(component, 'confirmPasswordToggle');

      component.handleDownKeydown(enterEvent, 'cpassword');
      expect(enterEvent.preventDefault).toHaveBeenCalled();
      expect(component.confirmPasswordToggle).toHaveBeenCalled();
    });

    it('should handle down keydown for unknown type', () => {
      const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(enterEvent, 'preventDefault');

      component.handleDownKeydown(enterEvent, 'unknown');
      expect(enterEvent.preventDefault).toHaveBeenCalled();
    });
  });

  describe('Address and Location Handling', () => {
    beforeEach(() => {
      // Mock Google Maps Geocoder
      const mockGeocoder = {
        geocode: jest.fn().mockImplementation((request, callback) => {
          callback([{ formatted_address: 'Test Address' }], 'OK');
        })
      };
      component.geoCoder = mockGeocoder;
    });

    it('should handle project address change', () => {
      const mockAddress = {
        geometry: {
          location: {
            lat: () => 40.7128,
            lng: () => -74.0060
          }
        },
        formatted_address: 'New York, NY, USA'
      };

      jest.spyOn(component, 'getAddress');

      component.handleProjectAddress(mockAddress);

      expect(component.getAddress).toHaveBeenCalledWith(40.7128, -74.0060, 'New York, NY, USA');
      expect(component.latitude).toBe(40.7128);
      expect(component.longitude).toBe(-74.0060);
    });

    it('should handle project address change from input', () => {
      const mockAddress = {
        formatted_address: 'Test Location',
        geometry: {
          location: {
            lat: () => 41.8781,
            lng: () => -87.6298
          }
        }
      };

      component.handleProjectAddressChange(mockAddress);

      expect(component.location).toBe('Test Location');
      expect(component.latitude).toBe(41.8781);
      expect(component.longitude).toBe(-87.6298);
    });

    it('should get address with provided address', () => {
      component.homePageProjectDetailsForm = formBuilder.group({
        projectLocation: [''],
        projectLocationLatitude: [''],
        projectLocationLongitude: ['']
      });

      const latitude = 40.7128;
      const longitude = -74.0060;
      const address = 'Test Address';

      component.getAddress(latitude, longitude, address);

      expect(component.address).toBe(address);
      expect(component.homePageProjectDetailsForm.get('projectLocation').value).toBe(address);
      expect(component.homePageProjectDetailsForm.get('projectLocationLatitude').value).toBe(latitude);
      expect(component.homePageProjectDetailsForm.get('projectLocationLongitude').value).toBe(longitude);
    });

    it('should get address without provided address using geocoder', () => {
      component.homePageProjectDetailsForm = formBuilder.group({
        projectLocation: [''],
        projectLocationLatitude: [''],
        projectLocationLongitude: ['']
      });

      const latitude = 40.7128;
      const longitude = -74.0060;

      component.getAddress(latitude, longitude, null);

      expect(component.geoCoder.geocode).toHaveBeenCalledWith(
        { location: { lat: latitude, lng: longitude } },
        expect.any(Function)
      );
    });

    it('should get lat/lng from data', () => {
      jest.spyOn(component, 'getAddress');
      const data = { lat: 34.0522, lng: -118.2437 };

      component.getLatLong(data);

      expect(component.getAddress).toHaveBeenCalledWith(34.0522, -118.2437, null);
    });
  });

  describe('Address Components Parsing', () => {
    it('should parse address components correctly', () => {
      const mockAddress = {
        address_components: [
          { types: ['street_number'], long_name: '123' },
          { types: ['route'], long_name: 'Main St' },
          { types: ['locality'], long_name: 'Anytown' },
          { types: ['administrative_area_level_1'], long_name: 'CA' },
          { types: ['country'], long_name: 'United States' },
          { types: ['postal_code'], long_name: '90210' },
          { types: ['sublocality_level_1'], long_name: 'Downtown' },
          { types: ['sublocality_level_2'], long_name: 'Central' },
          { types: ['administrative_area_level_2'], long_name: 'Los Angeles County' },
          { types: ['postal_code_suffix'], long_name: '1234' }
        ]
      };

      const result = component.parseAddressComponents(mockAddress);

      expect(result.homeStreetNumber).toBe('123');
      expect(result.homePlace).toBe('Main St');
      expect(result.homeLocalityValue).toBe('Anytown');
      expect(result.homeState).toBe('CA');
      expect(result.homeCountry).toBe('United States');
      expect(result.homeZipCode).toBe('90210');
      expect(result.homeSubLocality).toBe('Downtown');
      expect(result.homeSubLocalityLevel2).toBe('Central');
      expect(result.homeCity).toBe('Los Angeles County');
      expect(result.homeZipCodeSuffix).toBe('1234');
    });

    it('should handle empty address components', () => {
      const mockAddress = { address_components: [] };
      const result = component.parseAddressComponents(mockAddress);

      expect(result.homeStreetNumber).toBe('');
      expect(result.homePlace).toBe('');
      expect(result.homeLocalityValue).toBe('');
    });

    it('should handle address without address_components', () => {
      const mockAddress = {};
      const result = component.parseAddressComponents(mockAddress);

      expect(result.homeStreetNumber).toBe('');
      expect(result.homePlace).toBe('');
      expect(result.homeLocalityValue).toBe('');
    });
  });

  describe('Company Address Handling', () => {
    beforeEach(() => {
      component.companyDetailsForm = formBuilder.group({
        address: [''],
        country: [''],
        state: [''],
        city: [''],
        zipCode: ['']
      });
    });

    it('should handle company address change with all components', () => {
      const mockAddress = {
        address_components: [
          { types: ['street_number'], long_name: '456' },
          { types: ['route'], long_name: 'Business Ave' },
          { types: ['locality'], long_name: 'Business City' },
          { types: ['administrative_area_level_1'], long_name: 'NY' },
          { types: ['country'], long_name: 'United States' },
          { types: ['postal_code'], long_name: '10001' },
          { types: ['sublocality_level_1'], long_name: 'Midtown' },
          { types: ['sublocality_level_2'], long_name: 'East' }
        ]
      };

      jest.spyOn(component, 'parseAddressComponents').mockReturnValue({
        homePlace: 'Business Ave',
        homeStreetNumber: '456',
        homeLocalityValue: 'Business City',
        homeSubLocality: 'Midtown',
        homeCity: 'Business City',
        homeState: 'NY',
        homeCountry: 'United States',
        homeZipCode: '10001',
        homeZipCodeSuffix: '',
        homeSubLocalityLevel2: 'East'
      });

      component.handleHomePageCompanyAddressChange(mockAddress);

      expect(component.companyDetailsForm.get('address').value).toBe('456 Business Ave East Midtown');
      expect(component.companyDetailsForm.get('country').value).toBe('United States');
      expect(component.companyDetailsForm.get('state').value).toBe('NY');
      expect(component.companyDetailsForm.get('city').value).toBe('Business City');
      expect(component.companyDetailsForm.get('zipCode').value).toBe('10001');
    });

    it('should handle company address change with minimal components', () => {
      const mockAddress = {
        address_components: [
          { types: ['locality'], long_name: 'Simple City' },
          { types: ['administrative_area_level_1'], long_name: 'TX' },
          { types: ['country'], long_name: 'United States' }
        ]
      };

      jest.spyOn(component, 'parseAddressComponents').mockReturnValue({
        homePlace: '',
        homeStreetNumber: '',
        homeLocalityValue: 'Simple City',
        homeSubLocality: '',
        homeCity: '',
        homeState: 'TX',
        homeCountry: 'United States',
        homeZipCode: '',
        homeZipCodeSuffix: '',
        homeSubLocalityLevel2: ''
      });

      component.handleHomePageCompanyAddressChange(mockAddress);

      expect(component.companyDetailsForm.get('address').value).toBe('Simple City');
      expect(component.companyDetailsForm.get('country').value).toBe('United States');
      expect(component.companyDetailsForm.get('state').value).toBe('TX');
      expect(component.companyDetailsForm.get('city').value).toBe('Simple City');
    });
  });

  describe('Form Operations and Validation', () => {
    beforeEach(() => {
      // Mock localStorage
      const localStorageMock = {
        getItem: jest.fn().mockReturnValue(null),
        setItem: jest.fn(),
        removeItem: jest.fn()
      };
      Object.defineProperty(window, 'localStorage', { value: localStorageMock });
    });

    it('should handle company form submission', () => {
      component.companyDetailsForm = formBuilder.group({
        fullName: ['John', [Validators.required, Validators.minLength(3)]],
        lastName: ['Doe', [Validators.required, Validators.minLength(3)]],
        companyName: ['Test Company', [Validators.required, Validators.minLength(3)]],
        address: ['123 Test St'],
        city: ['Test City'],
        state: ['Test State'],
        country: ['Test Country'],
        zipCode: ['12345'],
        website: ['https://test.com']
      });

      jest.spyOn(component, 'memberDetailsUpdate').mockImplementation(() => {});
      component.ParentCompanyId = '123';

      component.checkCompanyType();

      expect(component.companySubmitted).toBe(true);
      expect(component.companyFormSubmitted).toBe(true);
      expect(localStorage.setItem).toHaveBeenCalledWith('company', expect.any(String));
      expect(component.memberDetailsUpdate).toHaveBeenCalled();
    });

    it('should handle project form submission with valid project name', () => {
      // Mock localStorage to return null for project details
      localStorage.getItem = jest.fn().mockReturnValue(null);

      component.homePageProjectDetailsForm = formBuilder.group({
        projectName: ['Valid Project Name', [Validators.required]],
        projectLocation: ['Test Location', [Validators.required]],
        projectLocationLatitude: ['40.7128'],
        projectLocationLongitude: ['-74.0060']
      });

      component.modalRef = { hide: jest.fn() } as any;

      component.checkProjectType();

      expect(component.projectSubmitted).toBe(true);
      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(localStorage.setItem).toHaveBeenCalledWith('project', expect.any(String));
      expect(router.navigate).toHaveBeenCalledWith(['/plans']);
    });

    it('should reject project name with only numbers', () => {
      // Mock localStorage to return null for project details
      localStorage.getItem = jest.fn().mockReturnValue(null);

      component.homePageProjectDetailsForm = formBuilder.group({
        projectName: ['12345', [Validators.required]]
      });

      component.checkProjectType();

      expect(toastrService.error).toHaveBeenCalledWith('Project cannot only be numbers.!');
    });

    it('should reject project name without letters', () => {
      // Mock localStorage to return null for project details
      localStorage.getItem = jest.fn().mockReturnValue(null);

      component.homePageProjectDetailsForm = formBuilder.group({
        projectName: ['!@#$%', [Validators.required]]
      });

      component.checkProjectType();

      expect(toastrService.error).toHaveBeenCalledWith('Please enter valid Project Name.!');
    });

    it('should handle invalid company form submission', () => {
      component.companyDetailsForm = formBuilder.group({
        fullName: ['', [Validators.required, Validators.minLength(3)]],
        lastName: ['', [Validators.required, Validators.minLength(3)]],
        companyName: ['', [Validators.required, Validators.minLength(3)]]
      });

      component.checkCompanyType();

      expect(component.companySubmitted).toBe(true);
      expect(component.companyFormSubmitted).toBe(false);
    });
  });

  describe('Modal and Navigation Operations', () => {
    beforeEach(() => {
      const localStorageMock = {
        getItem: jest.fn().mockReturnValue(null),
        setItem: jest.fn(),
        removeItem: jest.fn()
      };
      Object.defineProperty(window, 'localStorage', { value: localStorageMock });
    });

    it('should close modal and reset forms', () => {
      component.modalRef = { hide: jest.fn() } as any;
      component.basicDetailsForm = formBuilder.group({
        email: ['<EMAIL>'],
        phoneCode: ['+1']
      });
      component.companyDetailsForm = formBuilder.group({
        companyName: ['Test Company']
      });
      component.homePageProjectDetailsForm = formBuilder.group({
        projectName: ['Test Project']
      });

      jest.spyOn(component, 'changeMask');
      Object.defineProperty(router, 'url', { value: '/register-project/test', writable: true });

      component.modalClose();

      expect(component.alreadyExistingDomain).toBe(false);
      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(localStorage.removeItem).toHaveBeenCalledWith('basic');
      expect(localStorage.removeItem).toHaveBeenCalledWith('company');
      expect(localStorage.removeItem).toHaveBeenCalledWith('project');
      expect(component.basicDetailsForm.get('phoneCode').value).toBe('+1');
      expect(component.changeMask).toHaveBeenCalledWith('+1');
      expect(router.navigate).toHaveBeenCalledWith(['/home']);
    });

    it('should open modal for register-project URL', () => {
      const template = {} as any;
      component.urlValue = 'register-project';
      component.loginToken = false;

      const mockModalRef = { hide: jest.fn() } as any;
      modalService.show.mockReturnValue(mockModalRef);

      component.getStarted(template);

      expect(component.stepOne).toBe(false);
      expect(component.StepTwo).toBe(false);
      expect(component.StepThree).toBe(true);
      expect(modalService.show).toHaveBeenCalledWith(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'home-popup'
      });
    });

    it('should open modal for normal flow', () => {
      const template = {} as any;
      component.loginToken = false;
      component.urlValue = '';

      const mockModalRef = { hide: jest.fn() } as any;
      modalService.show.mockReturnValue(mockModalRef);

      component.getStarted(template);

      expect(component.stepOne).toBe(true);
      expect(component.StepTwo).toBe(false);
      expect(component.StepThree).toBe(false);
      expect(modalService.show).toHaveBeenCalledWith(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'home-popup'
      });
    });

    it('should handle step navigation - previous to step 2', () => {
      component.prev(2);

      expect(component.stepOne).toBe(true);
      expect(component.StepTwo).toBe(false);
      expect(component.StepThree).toBe(false);
    });

    it('should handle step navigation - previous to step 3', () => {
      component.prev(3);

      expect(component.StepTwo).toBe(true);
      expect(component.StepThree).toBe(false);
    });

    it('should open step three', () => {
      component.OpenStepThree();

      expect(component.StepTwo).toBe(false);
      expect(component.StepThree).toBe(true);
    });
  });

  describe('User Management Operations', () => {
    it('should handle user response - yes', () => {
      jest.spyOn(component, 'removeAlreadyInvitedMemberLink');

      component.userResponse('yes');

      expect(component.removeAlreadyInvitedMemberLink).toHaveBeenCalled();
    });

    it('should handle user response - no', () => {
      jest.spyOn(component, 'sendAlreadyInvitedLink');

      component.userResponse('no');

      expect(component.sendAlreadyInvitedLink).toHaveBeenCalled();
    });

    it('should remove already invited member', fakeAsync(() => {
      component.basicDetailsForm = formBuilder.group({
        email: ['<EMAIL>']
      });
      component.modalRef1 = { hide: jest.fn() } as any;

      component.removeAlreadyInvitedMemberLink();
      tick();

      expect(component.removeAlreadyInvitedMember).toBe(true);
      expect(authService.removeAlreadyInvitedMember).toHaveBeenCalledWith({
        email: '<EMAIL>'
      });
      expect(toastrService.success).toHaveBeenCalledWith('Success');
      expect(component.removeAlreadyInvitedMember).toBe(false);
      expect(component.modalRef1.hide).toHaveBeenCalled();
    }));

    it('should send already invited link', fakeAsync(() => {
      component.basicDetailsForm = formBuilder.group({
        email: ['<EMAIL>']
      });
      component.modalRef1 = { hide: jest.fn() } as any;
      component.modalRef = { hide: jest.fn() } as any;

      const localStorageMock = {
        removeItem: jest.fn()
      };
      Object.defineProperty(window, 'localStorage', { value: localStorageMock });

      component.sendAlreadyInvitedLink();
      tick();

      expect(component.requestInvitedLink).toBe(true);
      expect(authService.requestInvitedLink).toHaveBeenCalledWith({
        email: '<EMAIL>'
      });
      expect(toastrService.success).toHaveBeenCalledWith('Success');
      expect(component.requestInvitedLink).toBe(false);
      expect(localStorage.removeItem).toHaveBeenCalledWith('basic');
      expect(component.modalRef1.hide).toHaveBeenCalled();
      expect(component.modalRef.hide).toHaveBeenCalled();
    }));
  });

  describe('Lifecycle and Initialization', () => {
    beforeEach(() => {
      const localStorageMock = {
        getItem: jest.fn().mockReturnValue(null),
        setItem: jest.fn(),
        removeItem: jest.fn()
      };
      Object.defineProperty(window, 'localStorage', { value: localStorageMock });
    });

    it('should handle ngOnInit with member details', fakeAsync(() => {
      Object.defineProperty(router, 'url', { value: '/register-project/123/456/<EMAIL>/testdomain', writable: true });
      jest.spyOn(component, 'setCurrentLocation');
      jest.spyOn(component, 'basicPassForm');
      jest.spyOn(component, 'companyForm');
      jest.spyOn(component, 'projectForm');

      // Mock DOM element
      const mockElement = { click: jest.fn() };
      jest.spyOn(document, 'getElementById').mockReturnValue(mockElement as any);

      component.ngOnInit();
      tick();

      expect(component.setCurrentLocation).toHaveBeenCalled();
      expect(component.memberId).toBe('123');
      expect(component.ParentCompanyId).toBe('456');
      expect(component.memberEmail).toBe('<EMAIL>');
      expect(component.domainName).toBe('testdomain');
      expect(authService.getUserDetail).toHaveBeenCalled();
      expect(mockElement.click).toHaveBeenCalled();
    }));

    it('should handle ngOnInit when user is deleted', fakeAsync(() => {
      Object.defineProperty(router, 'url', { value: '/register-project/123/456/<EMAIL>/testdomain', writable: true });
      authService.getUserDetail.mockReturnValue(of({
        memberDetail: {
          isDeleted: true
        }
      }));

      component.ngOnInit();
      tick();

      expect(router.navigate).toHaveBeenCalledWith(['/login']);
      expect(toastrService.error).toHaveBeenCalledWith('You were removed from the project!', 'OOPS!');
    }));

    it('should handle ngOnInit when user already completed', fakeAsync(() => {
      Object.defineProperty(router, 'url', { value: '/register-project/123/456/<EMAIL>/testdomain', writable: true });
      authService.getUserDetail.mockReturnValue(of({
        memberDetail: {
          status: 'completed'
        }
      }));

      component.ngOnInit();
      tick();

      expect(router.navigate).toHaveBeenCalledWith(['/login']);
      expect(toastrService.info).toHaveBeenCalledWith('You created username already', 'Please log in');
    }));

    it('should handle ngOnInit error', fakeAsync(() => {
      Object.defineProperty(router, 'url', { value: '/register-project/123/456/<EMAIL>/testdomain', writable: true });
      const mockError = {
        message: {
          statusCode: 400,
          details: [{ error: 'Test error' }]
        }
      };
      authService.getUserDetail.mockReturnValue(throwError(() => mockError));
      jest.spyOn(component, 'showError');

      component.ngOnInit();
      tick();

      expect(component.showError).toHaveBeenCalledWith(mockError);
      expect(router.navigate).toHaveBeenCalledWith(['/login']);
    }));

    it('should handle ngAfterViewInit for register-project URL', () => {
      Object.defineProperty(router, 'url', { value: '/register-project', writable: true });
      authService.loggeduserIn.mockReturnValue(false);

      const localStorageMock = {
        getItem: jest.fn().mockReturnValue('{"test": "data"}')
      };
      Object.defineProperty(window, 'localStorage', { value: localStorageMock });

      jest.spyOn(component, 'getStarted');

      component.ngAfterViewInit();

      expect(component.urlValue).toBe('register-project');
      expect(component.getStarted).toHaveBeenCalled();
    });

    it('should handle ngOnDestroy', () => {
      component.modalRef = { hide: jest.fn() } as any;

      component.ngOnDestroy();

      expect(component.modalRef.hide).toHaveBeenCalled();
    });
  });

  describe('Form Initialization and Management', () => {
    beforeEach(() => {
      const localStorageMock = {
        getItem: jest.fn().mockReturnValue(null),
        setItem: jest.fn(),
        removeItem: jest.fn()
      };
      Object.defineProperty(window, 'localStorage', { value: localStorageMock });
    });

    it('should initialize basic form for new user', () => {
      component.memberId = undefined;

      component.basicPassForm();

      expect(component.basicDetailsForm).toBeDefined();
      expect(component.basicDetailsForm.get('email')).toBeDefined();
      expect(component.basicDetailsForm.get('phoneNumber')).toBeDefined();
      expect(component.basicDetailsForm.get('phoneCode')).toBeDefined();
      expect(component.basicDetailsForm.get('password')).toBeDefined();
      expect(component.basicDetailsForm.get('confirmPassword')).toBeDefined();
    });

    it('should initialize basic form for existing member', () => {
      component.memberId = '123';

      component.basicPassForm();

      expect(component.basicDetailsForm).toBeDefined();
      expect(component.basicDetailsForm.get('password').hasError).toBeDefined();
    });

    it('should set basic details form from localStorage', () => {
      const mockBasicInfo = {
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        phoneCode: '+1'
      };
      localStorage.getItem = jest.fn().mockReturnValue(JSON.stringify(mockBasicInfo));
      jest.spyOn(component, 'changeMask');

      component.setBasicDetailsForm();

      expect(component.basicDetailsForm.get('email').value).toBe('<EMAIL>');
      expect(component.basicDetailsForm.get('phoneNumber').value).toBe('1234567890');
      expect(component.basicDetailsForm.get('phoneCode').value).toBe('+1');
      expect(component.changeMask).toHaveBeenCalledWith('+1');
    });

    it('should initialize company form for new user', () => {
      component.memberId = undefined;

      component.companyForm();

      expect(component.companyDetailsForm).toBeDefined();
      expect(component.companyDetailsForm.get('website').hasError).toBeDefined();
    });

    it('should initialize company form for existing member', () => {
      component.memberId = '123';

      component.companyForm();

      expect(component.companyDetailsForm).toBeDefined();
      expect(component.companyDetailsForm.get('website')).toBeDefined();
    });

    it('should initialize project form', () => {
      component.projectForm();

      expect(component.homePageProjectDetailsForm).toBeDefined();
      expect(component.homePageProjectDetailsForm.get('projectLocation')).toBeDefined();
      expect(component.homePageProjectDetailsForm.get('projectName')).toBeDefined();
      expect(component.homePageProjectDetailsForm.get('projectLocationLatitude')).toBeDefined();
      expect(component.homePageProjectDetailsForm.get('projectLocationLongitude')).toBeDefined();
    });

    it('should validate MustMatch validator', () => {
      const formGroup = formBuilder.group({
        password: ['test123'],
        confirmPassword: ['test123']
      });

      const validator = component.MustMatch('password', 'confirmPassword');
      const result = validator(formGroup);

      expect(result).toBeUndefined();
      expect(formGroup.get('confirmPassword').errors).toBeNull();
    });

    it('should invalidate MustMatch validator for non-matching passwords', () => {
      const formGroup = formBuilder.group({
        password: ['test123'],
        confirmPassword: ['different']
      });

      const validator = component.MustMatch('password', 'confirmPassword');
      validator(formGroup);

      expect(formGroup.get('confirmPassword').errors).toEqual({ mustMatch: true });
    });
  });

  describe('Utility Methods', () => {
    it('should change phone mask', () => {
      const phoneCode = '+44';
      authService.checkDialCode.mockReturnValue('(*************');

      component.changeMask(phoneCode);

      expect(authService.checkDialCode).toHaveBeenCalledWith(phoneCode);
      expect(component.phoneMask).toBe('(*************');
    });

    it('should handle timezone selection', () => {
      component.countryCode = [
        { id: 1, name: 'US', countryDialCode: '+1' },
        { id: 2, name: 'UK', countryDialCode: '+44' }
      ];

      component.timeZoneSelected(1);

      expect(component.selectedValue).toEqual({ id: 1, name: 'US', countryDialCode: '+1' });
    });

    it('should truncate text correctly', () => {
      const longText = 'This is a very long text that should be truncated';
      const result = component.truncate(longText, 5);

      expect(result).toBe('This is a very long…');
    });

    it('should truncate text with negative limit', () => {
      const longText = 'This is a very long text that should be truncated';
      const result = component.truncate(longText, -3);

      expect(result).toBe('…should be truncated');
    });

    it('should return original text if within limit', () => {
      const shortText = 'Short text';
      const result = component.truncate(shortText, 20);

      expect(result).toBe('Short text');
    });

    it('should handle empty text', () => {
      const result = component.truncate('', 10);

      expect(result).toBe('');
    });

    it('should handle null text', () => {
      const result = component.truncate(null, 10);

      expect(result).toBe('');
    });

    it('should show error correctly', () => {
      const mockError = {
        message: {
          details: [{ error: 'Test error message' }]
        }
      };

      component.showError(mockError);

      expect(toastrService.error).toHaveBeenCalledWith('Test error message');
    });
  });

  describe('Location and Map Operations', () => {
    it('should set user current location', () => {
      jest.spyOn(component, 'getAddress');

      component.setUserCurrentLocation('40.7128', '-74.0060');

      expect(component.latitude).toBe(40.7128);
      expect(component.longitude).toBe(-74.0060);
      expect(component.zoom).toBe(18);
      expect(component.getAddress).toHaveBeenCalledWith(40.7128, -74.0060, null);
    });

    it('should handle map loader', () => {
      jest.spyOn(component, 'setUserCurrentLocation');

      component.mapLoader(40.7128, -74.0060);

      expect(component.geoCoder).toBeDefined();
      expect(component.setUserCurrentLocation).toHaveBeenCalledWith(40.7128, -74.0060);
    });

    it('should set current location with existing project location', () => {
      component.homePageProjectDetailsForm = formBuilder.group({
        projectLocation: ['Existing Location'],
        projectLocationLatitude: ['40.7128'],
        projectLocationLongitude: ['-74.0060']
      });

      jest.spyOn(component, 'projectForm');
      jest.spyOn(component, 'mapLoader');

      component.setCurrentLocation();

      expect(component.projectForm).toHaveBeenCalled();
      expect(component.mapLoader).toHaveBeenCalledWith('40.7128', '-74.0060');
    });

    it('should set current location with geolocation permission granted', fakeAsync(() => {
      component.homePageProjectDetailsForm = formBuilder.group({
        projectLocation: ['']
      });

      jest.spyOn(component, 'projectForm');
      jest.spyOn(component, 'mapLoader');

      // Mock permissions API
      const mockPermissions = {
        query: jest.fn().mockResolvedValue({ state: 'granted' })
      };
      Object.defineProperty(navigator, 'permissions', { value: mockPermissions });

      component.setCurrentLocation();
      tick();

      expect(component.projectForm).toHaveBeenCalled();
      expect(component.mapLoader).toHaveBeenCalled();
    }));

    it('should set current location with geolocation permission denied', fakeAsync(() => {
      component.homePageProjectDetailsForm = formBuilder.group({
        projectLocation: ['']
      });

      jest.spyOn(component, 'projectForm');
      jest.spyOn(component, 'mapLoader');

      // Mock permissions API
      const mockPermissions = {
        query: jest.fn().mockResolvedValue({ state: 'denied' })
      };
      Object.defineProperty(navigator, 'permissions', { value: mockPermissions });

      component.setCurrentLocation();
      tick();

      expect(component.projectForm).toHaveBeenCalled();
      expect(component.mapLoader).toHaveBeenCalledWith(38.897957, -77.03656);
    }));
  });

  describe('Additional Method Coverage', () => {
    beforeEach(() => {
      const localStorageMock = {
        getItem: jest.fn().mockReturnValue(null),
        setItem: jest.fn(),
        removeItem: jest.fn()
      };
      Object.defineProperty(window, 'localStorage', { value: localStorageMock });
    });

    it('should handle OpenStepTwo with company response', () => {
      component.companyDetailsForm = formBuilder.group({
        companyName: [''],
        companyId: [''],
        address: [''],
        city: [''],
        state: [''],
        country: [''],
        zipCode: ['']
      });

      const mockResponse = {
        company: {
          Company: [{
            companyName: 'Test Company',
            id: '123',
            address: 'Test Address',
            city: 'Test City',
            state: 'Test State',
            country: 'Test Country',
            zipCode: '12345'
          }]
        }
      };

      component.OpenStepTwo(mockResponse);

      expect(component.alreadyExistingDomain).toBe(true);
      expect(component.stepOne).toBe(false);
      expect(component.StepTwo).toBe(true);
      expect(component.companyDetailsForm.get('companyName').value).toBe('Test Company');
      expect(component.companyDetailsForm.get('companyId').value).toBe('123');
    });

    it('should handle OpenStepTwo without company response', () => {
      component.companyDetailsForm = formBuilder.group({
        companyName: ['Initial Value'],
        companyId: ['Initial ID']
      });
      component.ParentCompanyId = null;

      const mockResponse = {};

      jest.spyOn(component.companyDetailsForm, 'reset');

      component.OpenStepTwo(mockResponse);

      expect(component.alreadyExistingDomain).toBe(false);
      expect(component.stepOne).toBe(false);
      expect(component.StepTwo).toBe(true);
      expect(component.companyDetailsForm.reset).toHaveBeenCalled();
    });

    it('should handle OpenStepTwo with ParentCompanyId', () => {
      component.companyDetailsForm = formBuilder.group({
        companyName: [''],
        companyId: ['']
      });
      component.ParentCompanyId = '456';

      const mockResponse = {};

      jest.spyOn(component.companyDetailsForm, 'reset');

      component.OpenStepTwo(mockResponse);

      expect(component.companyDetailsForm.reset).not.toHaveBeenCalled();
      expect(component.stepOne).toBe(false);
      expect(component.StepTwo).toBe(true);
    });

    it('should handle getCountryCode method', () => {
      // Clear existing country codes
      component.countryCode = [];

      component.getCountryCode();

      expect(component.countryCode.length).toBeGreaterThan(0);
      expect(component.countryCode[0]).toHaveProperty('countryDialCode');
      expect(component.countryCode[0]).toHaveProperty('name');
      // Check if sorted alphabetically by name
      const names = component.countryCode.map(c => c.name);
      const sortedNames = [...names].sort();
      expect(names).toEqual(sortedNames);
    });

    it('should handle company form initialization for existing member', () => {
      component.memberId = '123';

      component.companyForm();

      expect(component.companyDetailsForm).toBeDefined();
      expect(component.companyDetailsForm.get('fullName')).toBeDefined();
      expect(component.companyDetailsForm.get('lastName')).toBeDefined();
      expect(component.companyDetailsForm.get('companyName')).toBeDefined();
      expect(component.companyDetailsForm.get('website')).toBeDefined();
    });

    it('should handle company form initialization for new user', () => {
      component.memberId = undefined;

      component.companyForm();

      expect(component.companyDetailsForm).toBeDefined();
      expect(component.companyDetailsForm.get('fullName')).toBeDefined();
      expect(component.companyDetailsForm.get('lastName')).toBeDefined();
      expect(component.companyDetailsForm.get('companyName')).toBeDefined();
      expect(component.companyDetailsForm.get('website')).toBeDefined();
    });
  });

  describe('Form Submission Edge Cases', () => {
    it('should handle onSubmit with invalid basic form', () => {
      component.basicDetailsForm = formBuilder.group({
        email: ['', [Validators.required, Validators.email]],
        phoneNumber: ['', [Validators.required]],
        phoneCode: ['+1', [Validators.required]],
        password: ['', [Validators.required]],
        confirmPassword: ['', [Validators.required]]
      });

      component.onSubmit('basic', null);

      expect(component.basicSubmitted).toBe(true);
      expect(component.formSubmitted).toBe(true);
    });

    it('should handle onSubmit with ParentCompanyId', () => {
      component.basicDetailsForm = formBuilder.group({
        email: ['<EMAIL>', [Validators.required, Validators.email]],
        phoneNumber: ['1234567890', [Validators.required]],
        phoneCode: ['+1', [Validators.required]],
        password: ['Test123!@#', [Validators.required]],
        confirmPassword: ['Test123!@#', [Validators.required]]
      });
      component.ParentCompanyId = '123';

      jest.spyOn(component, 'memberDetailsUpdate').mockImplementation(() => {});

      component.onSubmit('basic', null);

      expect(component.memberDetailsUpdate).toHaveBeenCalled();
    });

    it('should handle memberDetailsUpdate with missing basic details', () => {
      component.basicDetailsForm = formBuilder.group({});
      component.companyDetailsForm = formBuilder.group({
        companyName: ['Test Company']
      });

      component.memberDetailsUpdate();

      expect(toastrService.error).toHaveBeenCalledWith('Please Fill the Basic Details', 'OOPS');
      expect(router.navigate).toHaveBeenCalledWith(['/home']);
    });

    it('should handle memberDetailsUpdate with missing company details', () => {
      component.basicDetailsForm = formBuilder.group({
        email: ['<EMAIL>']
      });
      component.companyDetailsForm = formBuilder.group({});

      component.memberDetailsUpdate();

      expect(toastrService.error).toHaveBeenCalledWith('Please Fill the Company Details', 'OOPS');
      expect(router.navigate).toHaveBeenCalledWith(['/home']);
    });

    it('should handle onSubmit with existEmail error', fakeAsync(() => {
      const mockError = { error: { message: 'Email already exists' } };
      authService.existEmail.mockReturnValue(throwError(() => mockError));

      component.basicDetailsForm = formBuilder.group({
        email: ['<EMAIL>', [Validators.required, Validators.email]],
        phoneNumber: ['1234567890', [Validators.required]],
        phoneCode: ['+1', [Validators.required]],
        password: ['Test123!@#', [Validators.required]],
        confirmPassword: ['Test123!@#', [Validators.required]]
      });

      component.onSubmit('basic', null);
      tick();

      expect(toastrService.error).toHaveBeenCalled();
    }));

    it('should handle checkCompanyType without ParentCompanyId', () => {
      component.companyDetailsForm = formBuilder.group({
        fullName: ['John', [Validators.required, Validators.minLength(3)]],
        lastName: ['Doe', [Validators.required, Validators.minLength(3)]],
        companyName: ['Test Company', [Validators.required, Validators.minLength(3)]],
        address: ['123 Test St'],
        city: ['Test City'],
        state: ['Test State'],
        country: ['Test Country'],
        zipCode: ['12345'],
        website: ['https://test.com']
      });

      component.ParentCompanyId = null;
      jest.spyOn(component, 'OpenStepThree').mockImplementation(() => {});

      component.checkCompanyType();

      expect(component.companySubmitted).toBe(true);
      expect(component.companyFormSubmitted).toBe(false);
      expect(component.OpenStepThree).toHaveBeenCalled();
    });

    it('should handle geocoder error in getAddress', () => {
      const mockGeocoder = {
        geocode: jest.fn().mockImplementation((request, callback) => {
          callback(null, 'ERROR');
        })
      };
      component.geoCoder = mockGeocoder;
      component.homePageProjectDetailsForm = formBuilder.group({
        projectLocation: [''],
        projectLocationLatitude: [''],
        projectLocationLongitude: ['']
      });

      const latitude = 40.7128;
      const longitude = -74.0060;

      component.getAddress(latitude, longitude, null);

      expect(mockGeocoder.geocode).toHaveBeenCalled();
    });

    it('should handle ngOnDestroy without modalRef', () => {
      component.modalRef = null;

      expect(() => component.ngOnDestroy()).not.toThrow();
    });

    it('should handle sendAlreadyInvitedLink error', fakeAsync(() => {
      const mockError = { error: { message: 'Network error' } };
      authService.requestInvitedLink.mockReturnValue(throwError(() => mockError));

      component.basicDetailsForm = formBuilder.group({
        email: ['<EMAIL>']
      });
      component.modalRef1 = { hide: jest.fn() } as any;
      component.modalRef = { hide: jest.fn() } as any;

      component.sendAlreadyInvitedLink();
      tick();

      expect(component.requestInvitedLink).toBe(false);
      expect(toastrService.error).toHaveBeenCalled();
    }));

    it('should handle removeAlreadyInvitedMemberLink error', fakeAsync(() => {
      const mockError = { error: { message: 'Network error' } };
      authService.removeAlreadyInvitedMember.mockReturnValue(throwError(() => mockError));

      component.basicDetailsForm = formBuilder.group({
        email: ['<EMAIL>']
      });
      component.modalRef1 = { hide: jest.fn() } as any;

      component.removeAlreadyInvitedMemberLink();
      tick();

      expect(component.removeAlreadyInvitedMember).toBe(false);
      expect(toastrService.error).toHaveBeenCalled();
    }));
  });
});
