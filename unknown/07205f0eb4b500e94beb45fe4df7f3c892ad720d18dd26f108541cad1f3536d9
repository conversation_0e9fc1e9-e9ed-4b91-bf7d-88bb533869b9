import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { Socket, SocketIoModule, SocketIoConfig } from 'ngx-socket-io';
import { CraneRequestCommentComponent } from './crane-request-comment.component';
import { environment } from '../../../environments/environment';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';
import { MixpanelService } from '../../services/mixpanel.service';
import { of, throwError } from 'rxjs';

const config: SocketIoConfig = { url: environment.apiSocketUrl, options: {} };

describe('CraneRequestCommentComponent', () => {
  let component: CraneRequestCommentComponent;
  let fixture: ComponentFixture<CraneRequestCommentComponent>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let projectService: jest.Mocked<ProjectService>;
  let toastrService: jest.Mocked<ToastrService>;
  let socket: jest.Mocked<Socket>;
  let mixpanelService: jest.Mocked<MixpanelService>;

  const mockDeliveryService = {
    EditCraneRequestId: of(123),
    getCraneRequestComment: jest.fn(),
    createCraneRequestComment: jest.fn(),
    updateCraneRequestHistory1: jest.fn()
  };

  const mockProjectService = {
    ParentCompanyId: of(456),
    projectParent: of({ ProjectId: 789, ParentCompanyId: 456 })
  };

  const mockSocket = {
    emit: jest.fn()
  };

  const mockToastrService = {
    success: jest.fn(),
    error: jest.fn()
  };

  const mockMixpanelService = {
    addMixpanelEvents: jest.fn()
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [CraneRequestCommentComponent],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        HttpClientTestingModule,
        RouterTestingModule,
        ToastrModule.forRoot(),
        SocketIoModule.forRoot(config),
      ],
      providers: [
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: ProjectService, useValue: mockProjectService },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: Socket, useValue: mockSocket },
        { provide: MixpanelService, useValue: mockMixpanelService }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(CraneRequestCommentComponent);
    component = fixture.componentInstance;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    socket = TestBed.inject(Socket) as jest.Mocked<Socket>;
    mixpanelService = TestBed.inject(MixpanelService) as jest.Mocked<MixpanelService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.commentList).toEqual([]);
    expect(component.loader).toBe(true);
    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
  });

  it('should initialize form with required validators', () => {
    expect(component.commentDetailsForm.get('comment').valid).toBeFalsy();
    expect(component.commentDetailsForm.get('comment').errors.required).toBeTruthy();
  });

  it('should get history when EditCraneRequestId changes', () => {
    const mockResponse = { data: { rows: [{ id: 1, comment: 'Test comment' }] } };
    mockDeliveryService.getCraneRequestComment.mockReturnValue(of(mockResponse));

    component.ngOnInit();
    fixture.detectChanges();

    expect(mockDeliveryService.getCraneRequestComment).toHaveBeenCalledWith({
      CraneRequestId: 123,
      ParentCompanyId: 456,
      ProjectId: 789
    });
    expect(component.commentList).toEqual(mockResponse.data.rows);
    expect(component.loader).toBe(false);
  });

  it('should handle empty comment submission', () => {
    component.onSubmit();
    expect(component.submitted).toBe(true);
    expect(component.formSubmitted).toBe(true);
    expect(mockToastrService.error).toHaveBeenCalledWith('Please enter a valid comment.', 'OOPS!');
  });


  it('should handle comment submission error', () => {
    const error = { message: { statusCode: 400, details: [{ message: 'Invalid comment' }] } };
    mockDeliveryService.createCraneRequestComment.mockReturnValue(throwError(() => error));

    component.commentDetailsForm.patchValue({ comment: 'Test comment' });
    component.onSubmit();

    expect(mockToastrService.error).toHaveBeenCalledWith('Invalid comment');
    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
  });

  it('should check for empty string values', () => {
    expect(component.checkStringEmptyValues({ comment: '   ' })).toBe(true);
    expect(component.checkStringEmptyValues({ comment: 'test' })).toBe(false);
  });

  it('should unsubscribe on destroy', () => {
    const unsubscribeSpy = jest.spyOn(component['subscription'], 'unsubscribe');
    component.ngOnDestroy();
    expect(unsubscribeSpy).toHaveBeenCalled();
  });
});
