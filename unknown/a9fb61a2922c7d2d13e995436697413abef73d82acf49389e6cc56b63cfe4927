import { Component, OnDestroy, OnInit } from '@angular/core';
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import {
  debounceTime, filter, merge, Subscription,
} from 'rxjs';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';
import { AuthService } from '../../services/auth/auth.service';
import { MixpanelService } from '../../services/mixpanel.service';

@Component({
  selector: 'app-crane-request-attachment',
  templateUrl: './crane-request-attachment.component.html',
})
export class CraneRequestAttachmentComponent implements OnInit, OnDestroy {
  public files: NgxFileDropEntry[] = [];

  public fileData: any = [];

  public todayDate: Date;

  public formData: FormData;

  public fileArray: any = [];

  public CraneRequestId: any;

  public uploadSubmitted = false;

  public deleteUploadedFile = false;

  public ParentCompanyId: any;

  public ProjectId: any;

  public currentUser: any = {};

  public authUser: any = {};

  public loader = false;

  public readonly subscription: Subscription = new Subscription();

  public constructor(
    private readonly toastr: ToastrService,
    public projectService: ProjectService,
    private readonly mixpanelService: MixpanelService,
    private readonly deliveryService: DeliveryService,
    public socket: Socket,
    private readonly authService: AuthService,
  ) {
    this.todayDate = new Date();
    this.formData = new FormData();
    this.deliveryService.EditCraneRequestId.subscribe((getDeliveryRequestIdResponse): void => {
      this.CraneRequestId = getDeliveryRequestIdResponse;
    });
    this.getAuthUser();
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
      }
    });
    this.projectService.ParentCompanyId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ParentCompanyId = res;
      }
    });
  }

  public getAuthUser(): void {
    this.authService.getUser().subscribe((response: any): void => {
      this.currentUser = response;
    });
  }

  public dropped(files: NgxFileDropEntry[]): void {
    this.files = files;
    this.fileData.push(this.files);

    this.fileData.forEach((entryGroup: any[], i: number) => {
      entryGroup.forEach(
        (entry: { relativePath: string; fileEntry: FileSystemFileEntry }, index: number) => {
          const extension = this.getFileExtension(entry.relativePath);

          if (!this.isValidExtension(extension)) {
            this.toastr.error(
              'Please select a valid file. Supported file format (.jpg,.jpeg,.png,.pdf,.doc)',
              'OOPS!',
            );
            this.fileData.splice(i, 1);
            return;
          }

          if (entry.fileEntry.isFile) {
            this.processFile(entry.fileEntry, i, index, extension);
          }
        },
      );
    });
  }

  public getFileExtension(path: string): string {
    const parts = path.split('.');
    return parts[parts.length - 1].toLowerCase();
  }

  public isValidExtension(ext: string): boolean {
    return ['jpg', 'jpeg', 'png', 'pdf', 'doc'].includes(ext);
  }

  public processFile(
    fileEntry: FileSystemFileEntry,
    groupIndex: number,
    fileIndex: number,
    extension: string,
  ): void {
    fileEntry.file((file: File) => {
      const filesizeMB = +((file.size / 1_000_000).toFixed(4));

      if (filesizeMB > 2) {
        this.toastr.error('Please choose a attachment less than or equal to 2MB');
        this.fileData.splice(groupIndex, 1);
        return;
      }

      const reader = new FileReader();
      reader.onload = () => {
        if (this.fileData[groupIndex]) {
          this.fileData[groupIndex][fileIndex].image = reader.result;
        }
      };

      if (this.fileData[groupIndex]) {
        this.fileData[groupIndex][fileIndex].extension = extension;
        reader.readAsDataURL(file);
      }
    });
  }

  public removeExistingFile(item: { id: any }): void {
    this.loader = true;
    this.deleteUploadedFile = true;
    const params = {
      id: item.id,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    this.deliveryService.removeCraneRequestAttachement(params).subscribe({
      next: (res): void => {
        if (res) {
          this.fileData = [];
          this.toastr.success(res.message, 'Success');
          this.mixpanelService.addMixpanelEvents('Attachment deleted against a Crane Booking');
          this.deleteUploadedFile = false;
          this.deliveryService.updateCraneRequestHistory1(
            { status: true },
            'CraneAttachmentDeleteHistory',
          );
          this.socket.emit('CraneAttachmentDeleteHistory', res);
        }
      },
      error: (removeExistingFileError): void => {
        if (removeExistingFileError.message?.statusCode === 400) {
          this.showError(removeExistingFileError);
        } else if (!removeExistingFileError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(removeExistingFileError.message, 'OOPS!');
        }
      },
    });
    this.loader = false;
  }

  public clickAndDisable(link: any): void {
    const getData = link;
    // disable subsequent clicks
    getData.onclick = (event: { preventDefault: () => void }): void => {
      event.preventDefault();
    };
  }

  public uploadData(): void {
    this.uploadSubmitted = true;
    const hasFormData = (formData: FormData): boolean => {
      let hasData = false;

      formData.forEach(() => {
        hasData = true;
      });

      return hasData;
    };

    if (hasFormData(this.formData)) {
      const params = {
        CraneRequestId: this.CraneRequestId,
        ParentCompanyId: this.ParentCompanyId,
        ProjectId: this.ProjectId,
      };
      this.deliveryService.addCraneRequestAttachment(params, this.formData).subscribe({
        next: (res): void => {
          this.fileData = [];
          this.uploadSubmitted = false;
          this.toastr.success(res.message, 'SUCCESS!');
          this.mixpanelService.addMixpanelEvents('Attachment added against a Crane Booking');
          this.socket.emit('CraneApproveHistory', res);
          this.deliveryService.updateCraneRequestHistory1({ status: true }, 'CraneApproveHistory');
        },
        error: (attachmentErr): void => {
          this.uploadSubmitted = false;
          if (attachmentErr.message?.statusCode === 400) {
            this.showError(attachmentErr);
          } else {
            this.toastr.error(attachmentErr.message, 'OOPS!');
          }
        },
      });
    }
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }

  public removeFile(firstIndex: string | number, i: string | number): void {
    if (this.fileData[firstIndex]?.[i]) {
      this.formData.delete(this.fileData[firstIndex][i].relativePath);
      this.fileData[firstIndex].splice(i, 1);
      if (this.fileData[firstIndex].length === 0) {
        this.fileData.splice(firstIndex, 1);
      }
    }
  }

  public getAttachements(): void {
    const params = {
      CraneRequestId: this.CraneRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    this.deliveryService.getCraneRequestAttachements(params).subscribe((res): void => {
      this.fileArray = res.data;
    });
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.removeFile(data, item);
    }
  }

  public fileOver(_event: any): void { /* */ }

  public fileLeave(_event: any): void { /* */ }

  public ngOnInit(): void {
    this.projectService.projectParent.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ProjectId = res.ProjectId;
        this.ParentCompanyId = res.ParentCompanyId;
        this.getAttachements();
      }
    });
    const attachmentTrigger$ = merge(
      this.deliveryService.fetchData,
      this.deliveryService.fetchData1,
    ).pipe(
      debounceTime(100),
      filter(() => !!this.CraneRequestId),
    );

    this.subscription.add(
      attachmentTrigger$.subscribe(() => {
        this.getAttachements();
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
