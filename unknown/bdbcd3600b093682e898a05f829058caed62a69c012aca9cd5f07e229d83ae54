<div class="modal-header">
  <h2 class="fs14 fw-bold cairo-regular color-text7 my-1">
    <img src="./assets/images/delivery-pop.svg" alt="Delivery" class="me-2" />Crane Booking Details
  </h2>
  <ul
    class="list-group list-group-horizontal text-center c-pointer position-absolute details-control d-none d-md-flex d-lg-flex align-items-center"
    *ngIf="!void && show"
  >
    <li class="list-group-item border-0 ctl-list py-0 px-3">
      <form class="custom-material-form">
        <div class="form-group mb-0">
          <select
            class="form-control fs12 material-input select-status-input pe-5"
            (change)="selectStatus($event.target.value)"
            *ngIf="showStatus && statusValue.length > 1"
          >
            <option value="" disabled selected hidden>Select Status*</option>
            <option *ngFor="let item of statusValue">{{ item }}</option>
          </select>
          <div class="form-check custom-checkbox deliver-check text-black"
          *ngIf="showStatus && statusValue.length === 1">
            <input class="form-check-input custom-control-input" type="checkbox" value="" id="deliver" name="deliver" (change)="eventCheck($event)">
            <label class="form-check-label custom-control-label c-pointer fs12 fw-bold cairo-regular" for="deliver">
              Delivered
            </label>
          </div>

        </div>
      </form>
    </li>
    <li
      class="list-group-item border-0 ctl-list py-0 px-3"
      *ngIf="showStatus && (statusValue.length > 1 || statusValue.length === 1)"
      (click)="saveStatus()" (keydown)="handleDownKeydown($event, '', '','save')"
    >
      <img src="./assets/images/save-details.svg" alt="save" />
      <p class="fs12 color-grey7 fw600 mb0">
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="statusSubmitted"></em>Save
      </p>
    </li>
  </ul>
  <button
    type="button"
    class="close ms-auto"
    aria-label="Close"
    (click)="confirmationClose(cancelConfirmationPopup)">
    <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close" /></span>
  </button>
</div>
<div class="modal-body pt-0 px-0 pb-4">
  <div class="delivery-details-content" id="delivery-content3">
    <ul
      class="list-group list-group-horizontal details-control bottom-align-control d-flex d-md-none d-lg-none text-center"
    >
      <li class="list-group-item border-0 ctl-list py-0 px-3">
        <form class="custom-material-form">
          <div class="form-group mb-0 mt-2">
            <select
              class="form-control fs12 material-input select-status-input pe-5"
              id="detail-status"
              (change)="selectStatus($event.target.value)"
              *ngIf="showStatus"
            >
              <option value="" disabled selected hidden>Select Status*</option>
              <option *ngFor="let item of statusValue">{{ item }}</option>
            </select>
            <p class="status-approved" *ngIf="!showStatus && currentDeliverySaveItem">
              {{ currentDeliverySaveItem?.status }}
            </p>
          </div>
        </form>
      </li>
      <li
        class="list-group-item border-0 ctl-list py-0 px-3"
        *ngIf="showStatus"
        (click)="saveStatus()" (keydown)="handleDownKeydown($event, '', '','save')"
      >
        <img src="./assets/images/save-details.svg" alt="save" />
        <p class="fs12 color-grey7 fw600 mb0">
          <em class="fa fa-spinner" aria-hidden="true" *ngIf="statusSubmitted"></em>Save
        </p>
      </li>
      <li
        class="list-group-item border-0 ctl-list py-0 px-3 c-pointer"
        (click)="openEditModal(currentDeliverySaveItem, null)" (keydown)="handleDownKeydown($event, currentDeliverySaveItem, null,'edit')"
      >
        <img src="./assets/images/edit-details.svg" alt="Cross" />
        <p class="fs12 color-grey7 fw600 mb0">Edit</p>
      </li>
      <li
        class="list-group-item border-0 ctl-list py-0 px-2 c-pointer"
        *ngIf="ProjectId"
        (click)="openModal(voidNdrConfirmation)" (keydown)="handleDownKeydown($event, voidNdrConfirmation,'','open')"
      >
        <img src="./assets/images/cross-details.svg" alt="Void" />
        <p class="fs12 color-grey7 fw600 mb0">Void</p>
      </li>
    </ul>
    <tabset>
      <tab heading="Details" id="tab1" active="active" [active]="currentTabId === 0">
        <app-crane-request-detail-view-content></app-crane-request-detail-view-content>
      </tab>
      <tab heading="Attachments" [active]="currentTabId === 1">
        <app-crane-request-attachment></app-crane-request-attachment>
      </tab>
      <tab heading="Comments" [active]="currentTabId === 2">
        <app-crane-request-comment></app-crane-request-comment>
      </tab>
      <tab heading="History" [active]="currentTabId === 3">
        <app-crane-request-history></app-crane-request-history>
      </tab>
    </tabset>
  </div>
  <div class="text-center mt-4 mb-4">
    <button
      class="status-approved buttonstyle--new m-0"
      *ngIf="!showStatus && currentDeliverySaveItem?.status == 'Approved'"
    >
      {{ currentDeliverySaveItem.status }}
    </button>
    <button
      class="status-pending buttonstyle--new m-0"
      *ngIf="!showStatus && currentDeliverySaveItem?.status == 'Pending'"
    >
      {{ currentDeliverySaveItem.status }}
    </button>
    <button
      class="status-expired buttonstyle--new m-0"
      *ngIf="!showStatus && currentDeliverySaveItem?.status == 'Expired'"
    >
      {{ currentDeliverySaveItem.status }}
    </button>
    <button
      class="status-declined buttonstyle--new m-0"
      *ngIf="!showStatus && currentDeliverySaveItem?.status == 'Declined'"
    >
      {{ currentDeliverySaveItem.status }}
    </button>
    <button
      class="status-delivered buttonstyle--new m-0"
      *ngIf="
        !showStatus &&
        (currentDeliverySaveItem?.status === 'Completed' ||
          currentDeliverySaveItem?.status === 'Delivered')
      "
      (click)="revertstatus(statusConfirmationPopup)"
    >
      <em class="fa fa-spinner" aria-hidden="true" *ngIf="statusSubmitted"></em>Completed
    </button>
    <button
      class="fs12 cairo-regular btn btn-grey-light radius20 px-5 mx-2"
      (click)="openModal(voidNdrConfirmation)"
      *ngIf="ProjectId && currentDeliverySaveItem.edit"
      (onclick)="clickAndDisable(this)"
    >
      void
    </button>
    <button
      class="fs12 cairo-regular btn btn-green radius20 px-5 mx-2 edit-btn-popover"
      (click)="openEditModal(currentDeliverySaveItem, null)"
      *ngIf="
        (ProjectId && currentDeliverySaveItem?.edit && !currentDeliverySaveItem?.recurrence) ||
        (ProjectId &&
          currentDeliverySaveItem?.edit &&
          currentDeliverySaveItem?.recurrence &&
          currentDeliverySaveItem?.recurrence?.recurrence === 'Does Not Repeat')
      "
    >
      Edit
    </button>
    <button
      class="fs12 cairo-regular btn btn-green radius20 px-5 mx-2 edit-btn-popover"
      *ngIf="
        ProjectId &&
        currentDeliverySaveItem?.edit &&
        currentDeliverySaveItem?.recurrence &&
        currentDeliverySaveItem?.recurrence?.recurrence !== 'Does Not Repeat'
      "
      (click)="changeRequestCollapse(currentDeliverySaveItem)"
      [popover]="popTemplate"
      popoverTitle=""
      placement="top"
    >
      Edit
      <img
        src="./assets/images/green-downarrow.svg"
        alt="Down Arrow"
        class="arrow float-end eye-cursor mb-1 ms-2 mt-0"
        [ngClass]="{ rotateup: allRequestIsOpened }"
        containerClass="editcustomClass"
      />
    </button>
  </div>
</div>

<!--Confirmation Popup-->
<div id="confirm-popup5">
  <ng-template #cancelConfirmationPopup>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure you want to cancel?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetForm('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="resetForm('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>

<!--void delivery request confirmation Popup-->
<div id="confirm-popup9">
  <ng-template #voidNdrConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure you want to Void?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="voidConfirmationResponse('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="voidConfirmationResponse('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
<!--cancel confirmation Popup-->
<div id="confirm-popup6">
  <ng-template #statusConfirmationPopup>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Do you want to revert the status from Completed to approved for this booking?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="statusupdate('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="statusupdate('yes')"
        >
          <p class="fs12 color-grey7 fw600 mb0">
            <em class="fa fa-spinner" aria-hidden="true" *ngIf="statusSubmitted"></em>Yes
          </p>
        </button>
      </div>
    </div>
  </ng-template>
</div>
<div id="confirm-popup7">
  <ng-template #GateConfirmationPopup>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure? The gate mapped to the booking is deactivated, do you still want to deliver
          the booking?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="gatestatus('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="gatestatus('yes')"
        >
          <p class="fs12 color-grey7 fw600 mb0">
            <em class="fa fa-spinner" aria-hidden="true" *ngIf="gatesubmit"></em>Yes
          </p>
        </button>
      </div>
    </div>
  </ng-template>
</div>

<div id="confirm-popup8">
  <ng-template #equipmentConfirmationPopup>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure? The equipment mapped to the booking is deactivated, do you still want to
          deliver the booking?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="gatestatus('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="gatestatus('yes')"
        >
          <p class="fs12 color-grey7 fw600 mb0">
            <em class="fa fa-spinner" aria-hidden="true" *ngIf="gatesubmit"></em>Yes
          </p>
        </button>
      </div>
    </div>
  </ng-template>
</div>

<div id="confirm-popup10">
  <ng-template #personConfirmationPopup>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure? The responsible person mapped to the booking is deactivated, do you still
          want to deliver the booking?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="gatestatus('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="gatestatus('yes')"
        >
          <p class="fs12 color-grey7 fw600 mb0">
            <em class="fa fa-spinner" aria-hidden="true" *ngIf="gatesubmit"></em>Yes
          </p>
        </button>
      </div>
    </div>
  </ng-template>
</div>

<div id="confirm-popup11">
  <ng-template #overallConfirmationPopup>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure? The {{ textvalue }} mapped to the booking is deactivated, do you still want
          to deliver the booking?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="gatestatus('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="gatestatus('yes')"
        >
          <p class="fs12 color-grey7 fw600 mb0">
            <em class="fa fa-spinner" aria-hidden="true" *ngIf="gatesubmit"></em>Yes
          </p>
        </button>
      </div>
    </div>
  </ng-template>
</div>
<ng-template #popTemplate>
  <ul class="list-group c-pointer event-selection-popup my-2 w190">
    <li
      class="list-group-item border-0 p-1 cairo-regular c-pointer fw700 fs12"
      *ngFor="let data of seriesOptions"
      [ngClass]="{ disabled: data.disabled }"
      [tabindex]="data.disabled ? '-1' : '0'"
      (click)="openEditModal(currentDeliverySaveItem, data?.option)" (keydown)="handleDownKeydown($event, currentDeliverySaveItem, data?.option,'edit')"
    >
      <span class="ps-2">{{ data?.text }}</span>
    </li>
  </ul>
</ng-template>
