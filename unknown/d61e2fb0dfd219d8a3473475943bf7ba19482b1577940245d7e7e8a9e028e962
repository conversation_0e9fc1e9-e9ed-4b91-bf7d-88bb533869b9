import { Component, TemplateRef, OnInit } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import moment from 'moment';
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { Title } from '@angular/platform-browser';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { EditDeliveryFormComponent } from '../delivery-details/edit-delivery-form/edit-delivery-form.component';
import { DeliveryDetailsNewComponent } from '../delivery-details/delivery-details-new/delivery-details-new.component';

@Component({
  selector: 'app-queued-delivery-request',
  templateUrl: './queued-delivery-request.component.html',
  })
export class QueuedDeliveryRequestComponent implements OnInit {
  public userData = [];

  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public ProjectId: string | number;

  public loader = true;

  public pageSize = 25;

  public queuedNDRPageSize = 25;

  public pageNo = 1;

  public queuedNDRPageNo = 1;

  public totalCount = 0;

  public queuedNDRTotalCount = 0;

  public lastId: any = 0;

  public queuedDeliveryList: any = [];

  public queuedDeliveryRequestSelectAll = false;

  public companyList: any = [];

  public defineList: any = [];

  public defineListData: any = [];

  public gateList: any = [];

  public equipmentList: any = [];

  public currentTemplate: TemplateRef<any>;

  public autocompleteItemsAsObjects: any = [];

  public submitted = false;

  public escort = false;

  public formSubmitted = false;

  public editSubmitted = false;

  public modalLoader = false;

  public filterCount = 0;

  public formEditSubmitted = false;

  public authUser: any = {};

  public statusValue: any = [];

  public search = '';

  public filterForm: UntypedFormGroup;

  public memberList: any = [];

  public wholeStatus = ['Approved', 'Declined', 'Delivered', 'Pending'];

  public voidSubmitted = false;

  public currentDeliverySaveItem: any = {};

  public deliveryId: any;

  public ParentCompanyId: any;

  public importSubmitted = false;

  public files: NgxFileDropEntry[] = [];

  public formData: any = [];

  public todayDate = new Date();

  public data1: { name: any; values: any }[];

  public workbookData: any;

  public queuedNdrLoader = true;

  public deleteQueuedDeliveryIndex: any = [];

  public currentQueuedDeliveryDeleteId: any;

  public removeQueuedDelivery = false;

  public deleteDeliveryRequestSubmitted = false;

  public bulkNdrUploadInProgress = false;

  public currentlySelectedTab: string = 'tab1';

  public editModalLoader = false;

  public newNdrDefinableDropdownSettings: IDropdownSettings;

  public newNdrCompanyDropdownSettings: IDropdownSettings;

  public modalRef2: BsModalRef;

  public showSearchbar = false;

  public removeLoader = false;

  public sortColumn = 'id';

  public sort = 'DESC';

  public approvedBackgroundColor: string;

  public approvedFontColor: string;

  public rejectedBackgroundColor: string;

  public rejectedFontColor: string;

  public deliveredBackgroundColor: string;

  public deliveredFontColor: string;

  public pendingBackgroundColor: string;

  public pendingFontColor: string;

  public expiredBackgroundColor: string;

  public expiredFontColor: string;

  public locationList: any = [];

  public noEquipmentOption = { id: 0, equipmentName: 'No Equipment Needed' };

  public constructor(
    private readonly modalService: BsModalService,
    public projectService: ProjectService,
    private readonly formBuilder: UntypedFormBuilder,
    public router: Router,
    public socket: Socket,
    private readonly toastr: ToastrService,
    private readonly deliveryService: DeliveryService,
    private readonly titleService: Title,
  ) {
    this.titleService.setTitle('Follo - Queued Delivery Bookings');
    /**/
    this.projectService.projectParent.subscribe((response19): void => {
      if (response19 !== undefined && response19 !== null && response19 !== '') {
        this.loader = true;
        this.queuedNdrLoader = true;
        this.ProjectId = response19.ProjectId;
        this.ParentCompanyId = response19.ParentCompanyId;
        this.getQueuedDeliveryRequest();
        this.getMembers();
      }
    });
    this.projectService.ParentCompanyId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ParentCompanyId = res;
      }
    });
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
        if (this.authUser.RoleId === 2 || this.authUser.RoleId === 1) {
          this.statusValue = ['Approved', 'Declined'];
        } else if (this.authUser.RoleId === 3) {
          this.statusValue = ['Delivered', 'Approved'];
        }
      }
    });
    this.deliveryService.getCurrentStatus.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.deliveryId = res;
        this.getQueuedDeliveryRequest();
      }
    });
    this.projectService.fileUpload.subscribe((res): void => {
      if (res && res.status === 'uploading') {
        this.bulkNdrUploadInProgress = true;
      }
      if (res && res.status === 'uploadDone') {
        this.projectService.uploadBulkNdrFile({ status: '' });
        this.bulkNdrUploadInProgress = false;
        this.getQueuedDeliveryRequest();
      }
    });
    this.filterDetailsForm();
  }

  public ngOnInit(): void {
    this.router.events.subscribe((e): void => {
      if (this.modalRef) {
        this.modalRef.hide();
      }
    });
    this.deliveryService.refresh.subscribe((getNdrResponse): void => {
      if (getNdrResponse !== undefined && getNdrResponse !== null && getNdrResponse !== '') {
        this.getQueuedDeliveryRequest();
      }
    });
    this.deliveryService.refresh1.subscribe((getNdrResponse): void => {
      if (getNdrResponse !== undefined && getNdrResponse !== null && getNdrResponse !== '') {
        this.getQueuedDeliveryRequest();
      }
    });
  }

  public sortByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getQueuedDeliveryRequest();
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'clear':
          this.clear();
          break;
        case 'filter':
          this.openModal1(data);
          break;
        case 'open':
          this.openIdModal(data, item);
          break;
        case 'edit':
          this.openEditModal(data, item);
          break;
        case 'queue':
          this.openQueuedDeleteModal(data, item);
          break;
        default:
          break;
      }
    }
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.search = '';
    this.pageNo = 1;
    this.filterDetailsForm();
    this.getQueuedDeliveryRequest();
    this.modalRef.hide();
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('descriptionFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('dateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('companyFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('memberFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('gateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('equipmentFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('locationFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('statusFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('pickFrom').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('pickTo').value !== '') {
      this.filterCount += 1;
    }
    this.pageNo = 1;
    this.getQueuedDeliveryRequest();
    this.modalRef.hide();
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.pageNo = 1;
    this.getQueuedDeliveryRequest();
  }

  public getSearchNDR(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.pageNo = 1;
    this.search = data;
    this.getQueuedDeliveryRequest();
  }

  public redirect(path: any): void {
    this.router.navigate([`/${path}`]);
  }

  public getOverAllGateForNdrGrid(): void {
    this.modalLoader = true;
    const getOverAllGateForNdrGridParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .gateList(getOverAllGateForNdrGridParams, { isFilter: true, showActivatedAlone: true })
      .subscribe((getOverAllGateForNdrGridResponse): void => {
        this.gateList = getOverAllGateForNdrGridResponse.data;
        this.getOverAllEquipmentForNdrGrid();
      });
  }

  public getOverAllEquipmentForNdrGrid(): void {
    const getOverAllEquipmentForNdrGridParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listEquipment(getOverAllEquipmentForNdrGridParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((getOverAllEquipmentForNdrGridResponse): void => {
        this.equipmentList = [this.noEquipmentOption, ...getOverAllEquipmentForNdrGridResponse.data];
        this.getCompaniesForNdrGrid();
      });
  }

  public getDefinableForNdrGrid(): void {
    const getDefinableForNdrGridParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getDefinableWork(getDefinableForNdrGridParams)
      .subscribe((getDefinableForNdrGridResponse: any): void => {
        if (getDefinableForNdrGridResponse) {
          const { data } = getDefinableForNdrGridResponse;
          this.defineList = data;
          this.getLocations();
        }
      });
  }

  public getLocations(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getLocations(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.locationList = data;
        this.closePopupContentModal();
      }
    });
  }

  public close(): void {
    this.submitted = false;
    this.formSubmitted = false;
    this.deleteDeliveryRequestSubmitted = false;
    this.editSubmitted = false;
    this.formEditSubmitted = false;
    this.modalRef.hide();
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public getCompaniesForNdrGrid(): void {
    const getCompaniesForNdrGridParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getCompanies(getCompaniesForNdrGridParams)
      .subscribe((getCompaniesForNdrGridResponse: any): void => {
        if (getCompaniesForNdrGridResponse) {
          this.companyList = getCompaniesForNdrGridResponse.data;
          this.getDefinableForNdrGrid();
        }
      });
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      companyFilter: [''],
      descriptionFilter: [''],
      statusFilter: [''],
      dateFilter: [''],
      memberFilter: [''],
      gateFilter: [''],
      equipmentFilter: [''],
      locationFilter: [''],
      pickFrom: [''],
      pickTo: [''],
    });
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getRegisteredMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }

  public getQueuedDeliveryRequest(): void {
    this.queuedNdrLoader = true;
    this.queuedDeliveryList = [];
    const getQueuedDeliveryRequestParams = {
      ProjectId: this.ProjectId,
      pageSize: this.queuedNDRPageSize,
      pageNo: this.queuedNDRPageNo,
      void: 0,
    };
    let getQueuedDeliveryRequestPayload: any = {};
    if (this.filterForm !== undefined) {
      const dateInFilterForm = this.filterForm.value.dateFilter
        ? moment(this.filterForm.value.dateFilter).format('YYYY-MM-DD')
        : this.filterForm.value.dateFilter;
      getQueuedDeliveryRequestPayload = {
        companyFilter: +this.filterForm.value.companyFilter,
        descriptionFilter: this.filterForm.value.descriptionFilter,
        dateFilter: dateInFilterForm,
        statusFilter: this.filterForm.value.statusFilter,
        memberFilter: +this.filterForm.value.memberFilter,
        gateFilter: +this.filterForm.value.gateFilter,
        equipmentFilter: this.filterForm.value.equipmentFilter === null || this.filterForm.value.equipmentFilter === '' ? null : +this.filterForm.value.equipmentFilter,
        locationFilter: this.filterForm.value.locationFilter,
        pickFrom: this.filterForm.value.pickFrom,
        pickTo: this.filterForm.value.pickTo,
        search: this.search,
      };
    }
    getQueuedDeliveryRequestPayload.search = this.search;
    getQueuedDeliveryRequestPayload.sort = this.sort;
    getQueuedDeliveryRequestPayload.sortByField = this.sortColumn;
    getQueuedDeliveryRequestPayload.ParentCompanyId = this.ParentCompanyId;
    getQueuedDeliveryRequestPayload.queuedNdr = true;
    this.deliveryService
      .listNDR(getQueuedDeliveryRequestParams, getQueuedDeliveryRequestPayload)
      .subscribe((response: any): void => {
        if (response) {
          const responseData = response.data;
          this.lastId = response.lastId;
          const statusCode = JSON.parse(response?.statusData.statusColorCode);

          const approved = statusCode.find((item) => item.status === 'approved');
          const pending = statusCode.find((item) => item.status === 'pending');
          const delivered = statusCode.find((item) => item.status === 'delivered');
          const rejected = statusCode.find((item) => item.status === 'rejected');
          const expired = statusCode.find((item) => item.status === 'expired');

          this.approvedBackgroundColor = approved.backgroundColor;
          this.approvedFontColor = approved.fontColor;
          this.rejectedBackgroundColor = rejected.backgroundColor;
          this.rejectedFontColor = rejected.fontColor;
          this.expiredBackgroundColor = expired.backgroundColor;
          this.expiredFontColor = expired.fontColor;
          this.deliveredBackgroundColor = delivered.backgroundColor;
          this.deliveredFontColor = delivered.fontColor;
          this.pendingBackgroundColor = pending.backgroundColor;
          this.pendingFontColor = pending.fontColor;

          this.queuedNdrLoader = false;
          this.queuedDeliveryList = responseData.rows;
          this.queuedNDRTotalCount = responseData.count;
        }
      });
  }

  public selectAllQueuedDeliveryRequest(): void {
    this.queuedDeliveryRequestSelectAll = !this.queuedDeliveryRequestSelectAll;
    if (this.queuedDeliveryRequestSelectAll) {
      this.queuedDeliveryList.map((obj: any, index: string | number): void => {
        this.queuedDeliveryList[index].isChecked = true;
        return null;
      });
    } else {
      this.queuedDeliveryList.map((obj: any, index: string | number): void => {
        this.queuedDeliveryList[index].isChecked = false;
        return null;
      });
    }
  }

  public openQueuedDeleteModal(index: number, template: TemplateRef<any>): void {
    if (index !== -1) {
      this.deleteQueuedDeliveryIndex[0] = this.queuedDeliveryList[index].id;
      this.currentQueuedDeliveryDeleteId = index;
      this.removeQueuedDelivery = false;
    } else if (index === -1) {
      this.removeQueuedDelivery = true;
    }
    this.openModal(template);
  }

  public checkIfQueuedDeliveryRequestRowSelected(): boolean {
    if (this.queuedDeliveryRequestSelectAll) {
      return false;
    }
    const indexFind = this.queuedDeliveryList.findIndex(
      (item: { isChecked: boolean }): boolean => item.isChecked === true,
    );
    if (indexFind !== -1) {
      return false;
    }
    return true;
  }

  public setSelectedQueuedDeliveryRequestItem(index: string | number): void {
    this.queuedDeliveryList[index].isChecked = !this.queuedDeliveryList[index].isChecked;
  }

  public removeDeliveryRequestItem(): void {
    this.deleteDeliveryRequestSubmitted = true;
    if (this.queuedDeliveryRequestSelectAll) {
      this.deleteQueuedDeliveryRequest();
    } else {
      this.queuedDeliveryList.forEach((element: { isChecked: any; id: any }): void => {
        if (element.isChecked) {
          this.deleteQueuedDeliveryIndex.push(element.id);
        }
      });
      this.deleteQueuedDeliveryRequest();
    }
  }

  public deleteQueuedDeliveryRequest(): void {
    this.deleteDeliveryRequestSubmitted = true;
    this.deliveryService
      .deleteQueuedNdr({
        id: this.deleteQueuedDeliveryIndex,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
        queuedDeliveryRequestSelectAll: this.queuedDeliveryRequestSelectAll,
      })
      .subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.getQueuedDeliveryRequest();
            this.deleteDeliveryRequestSubmitted = false;
            this.queuedDeliveryRequestSelectAll = false;
            this.modalRef.hide();
          }
        },
        error: (deleteQueuedDeliveryRequestError): void => {
          this.deleteDeliveryRequestSubmitted = false;
          if (deleteQueuedDeliveryRequestError.message?.statusCode === 400) {
            this.showError(deleteQueuedDeliveryRequestError);
          } else if (!deleteQueuedDeliveryRequestError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deleteQueuedDeliveryRequestError.message, 'OOPS!');
          }
        },
      });
  }

  public openModal(template: TemplateRef<any>): void {
    this.currentTemplate = template;
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-delivery-popup custom-modal',
    };
    this.modalRef = this.modalService.show(this.currentTemplate, data);
  }

  public closePopupContentModal(): void {
    this.modalLoader = false;
  }

  public openIdModal(item: { id: any; ProjectId: any }, ndrStatus: any): void {
    this.deliveryService.updateStateOfNDR(ndrStatus);
    const newPayload = {
      id: item.id,
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    const initialState = {
      data: newPayload,
      title: 'Modal with component',
    };
    this.modalRef = this.modalService.show(DeliveryDetailsNewComponent, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-delivery-popup custom-modal',
      initialState,
    });
    this.modalRef.content.closeBtnName = 'Close';
  }

  public openEditModal(item: any, ndrState: any): void {
    if (this.modalRef) {
      this.close();
    }
    this.deliveryService.updateStateOfNDR(ndrState);
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.deliveryService.updatedDeliveryId(item.id);
    this.modalRef = this.modalService.show(EditDeliveryFormComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
    });
    this.modalRef.content.closeBtnName = 'Close';
    this.modalRef.content.seriesOption = 1;
    this.modalRef.content.recurrenceId = item?.recurrence ? item?.recurrence?.id : null;
    this.modalRef.content.recurrenceEndDate = item?.recurrence
      ? item?.recurrence?.recurrenceEndDate
      : null;
  }

  public changeQueuedPageSize(pageSize: number): void {
    this.queuedNDRPageSize = pageSize;
    this.getQueuedDeliveryRequest();
  }

  public openModal1(template: TemplateRef<any>): void {
    this.getOverAllGateForNdrGrid();
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-sm filter-popup custom-modal',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public changeQueuedPageNo(pageNo: number): void {
    this.queuedNDRPageNo = pageNo;
    this.getQueuedDeliveryRequest();
  }

  public dropped(files: NgxFileDropEntry[]): void {
    if (files.length === 1) {
      this.files = files;
      this.files.forEach((element, i): void => {
        const relativePath = element.relativePath.split('.');
        const extension = relativePath[relativePath.length - 1];
        if (extension === 'xlsx') {
          if (element.fileEntry.isFile) {
            const fileEntry = element.fileEntry as FileSystemFileEntry;
            fileEntry.file((_file: File): void => {
              this.formData = new FormData();
              this.formData.append('delivery_request', _file, element.relativePath);
            });
          }
        } else {
          this.files.splice(i, 1);
          this.toastr.error('Please select a valid file. Supported file format (.xlsx)', 'OOPS!');
        }
      });
    } else {
      this.toastr.error('Please import single file', 'OOPS!');
    }
  }

  public importDeliveryRequest(): void {
    this.importSubmitted = true;
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.importBulkNDR(params, this.formData).subscribe({
      next: (res): void => {
        this.projectService.uploadBulkNdrFile({ status: 'uploading' });
        this.files = [];
        this.importSubmitted = false;
        this.modalRef.hide();
      },
      error: (importDeliveryRequestError): void => {
        this.importSubmitted = false;
        if (importDeliveryRequestError.message?.statusCode === 400) {
          this.showError(importDeliveryRequestError);
        } else if (!importDeliveryRequestError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(importDeliveryRequestError.message, 'OOPS!');
        }
      },
    });
  }

  public download(): any {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };

    this.deliveryService.importBulkNDRTemplate(params).subscribe((res: any): void => {
      this.projectService.getProject().subscribe({
        next: (response): any => {
          const project = response.data.filter(
            (data: { id: string | number }): {} => +data.id === +this.ProjectId,
          );
          const fileName = `${project[0].projectName}_${project[0].id}_${new Date().getTime()}`;
          const downloadURL = window.URL.createObjectURL(res);
          const link = document.createElement('a');
          link.href = downloadURL;
          link.download = `${fileName}.xlsx`;
          link.click();
        },
        error: (downloadDeliveryRequestError): void => {
          if (downloadDeliveryRequestError.message?.statusCode === 400) {
            this.showError(downloadDeliveryRequestError);
          } else if (!downloadDeliveryRequestError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(downloadDeliveryRequestError.message, 'OOPS!');
          }
        },
      });
    });
  }

  public removeFile(i: number): void {
    this.removeLoader = true;
    this.files.splice(i, 1);
    this.removeLoader = false;
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.modalRef.hide();
      this.files = [];
    }
  }

  public closeModal(template: TemplateRef<any>): void {
    if (this.files.length > 0) {
      let data = {};
      data = {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      };
      this.modalRef1 = this.modalService.show(template, data);
    } else {
      this.resetForm('yes');
    }
  }
}
