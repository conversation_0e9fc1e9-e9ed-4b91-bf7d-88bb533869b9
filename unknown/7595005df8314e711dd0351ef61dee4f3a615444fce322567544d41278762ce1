import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ConcreteCalendarComponent } from './concrete-calendar.component';
import { BsModalService } from 'ngx-bootstrap/modal';
import { Title } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { UntypedFormBuilder, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { DeliveryService } from '../../services/profile/delivery.service';
import { CalendarService } from '../../services/profile/calendar.service';
import { ProjectService } from '../../services/profile/project.service';
import { of, BehaviorSubject } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('ConcreteCalendarComponent', () => {
  let component: ConcreteCalendarComponent;
  let fixture: ComponentFixture<ConcreteCalendarComponent>;
  let modalService: jest.Mocked<BsModalService>;
  let router: jest.Mocked<Router>;
  let projectService: jest.Mocked<ProjectService>;
  let toastr: jest.Mocked<ToastrService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let calendarService: jest.Mocked<CalendarService>;
  let titleService: jest.Mocked<Title>;

  beforeEach(async () => {
    const modalServiceSpy = {
      show: jest.fn().mockReturnValue({
        content: { closeBtnName: '' },
        hide: jest.fn()
      })
    };
    const routerSpy = {
      navigate: jest.fn()
    };
    const projectServiceSpy = {
      projectParent: new BehaviorSubject({ ParentCompanyId: '123', ProjectId: '456' })
    };
    const toastrSpy = {
      success: jest.fn(),
      error: jest.fn()
    };
    const deliveryServiceSpy = {
      getConcreteRequest: jest.fn(),
      fetchConcreteData: new BehaviorSubject({}),
      fetchConcreteData1: new BehaviorSubject({})
    };
    const calendarServiceSpy = {
      getConcreteRequest: jest.fn().mockReturnValue(of({
        data: [],
        statusData: { statusColorCode: '{}' },
        cardData: { concreteCard: '{}' }
      })),
      getCalendarEvents: jest.fn()
    };
    const titleServiceSpy = {
      setTitle: jest.fn()
    };

    await TestBed.configureTestingModule({
      declarations: [ConcreteCalendarComponent],
      imports: [ReactiveFormsModule, FormsModule],
      providers: [
        { provide: BsModalService, useValue: modalServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: ProjectService, useValue: projectServiceSpy },
        { provide: ToastrService, useValue: toastrSpy },
        { provide: DeliveryService, useValue: deliveryServiceSpy },
        { provide: CalendarService, useValue: calendarServiceSpy },
        { provide: Title, useValue: titleServiceSpy },
        UntypedFormBuilder
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    modalService = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    router = TestBed.inject(Router) as jest.Mocked<Router>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    toastr = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    calendarService = TestBed.inject(CalendarService) as jest.Mocked<CalendarService>;
    titleService = TestBed.inject(Title) as jest.Mocked<Title>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ConcreteCalendarComponent);
    component = fixture.componentInstance;

    // Mock calendarComponent1
    component.calendarComponent1 = {
      getApi: jest.fn().mockReturnValue({
        next: jest.fn(),
        prev: jest.fn(),
        changeView: jest.fn(),
        currentData: {
          dateProfile: {
            activeRange: { start: new Date(), end: new Date() }
          }
        }
      })
    } as any;

    // Initialize filterForm
    component.filterDetailsForm();

    // Mock concreteRequest
    component.concreteRequest = [
      { id: 123, uniqueNumber: 'ABC123' }
    ];

    // Set modalRef
    component.modalRef = {
      content: { closeBtnName: '' },
      hide: jest.fn()
    } as any;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set title on initialization', () => {
    expect(titleService.setTitle).toHaveBeenCalledWith('Follo - Concrete Calendar');
  });

  it('should initialize with default values', () => {
    expect(component.currentView).toBe('Month');
    expect(component.showSearchbar).toBe(false);
    expect(component.modalLoader).toBe(true);
  });

  describe('Calendar Navigation', () => {
    it('should navigate to next period', () => {
      component.goNext();
      expect(component.calendarApi.next).toHaveBeenCalled();
    });

    it('should navigate to previous period', () => {
      component.goPrev();
      expect(component.calendarApi.prev).toHaveBeenCalled();
    });

    it('should change to week view', () => {
      component.goTimeGridWeekOrDay('timeGridWeek');
      expect(component.currentView).toBe('Week');
    });

    it('should change to day view', () => {
      component.goTimeGridWeekOrDay('timeGridDay');
      expect(component.currentView).toBe('Day');
    });
  });

  describe('Modal Operations', () => {
    it('should open add concrete request modal', () => {
      const dateArg = { dateStr: '2024-03-20' };
      component.openAddConcreteRequestModal(dateArg);
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should open filter modal', () => {
      const template = {} as any;
      component.openFilterModal(template);
      expect(modalService.show).toHaveBeenCalled();
    });
  });

  describe('Filter Operations', () => {
    it('should reset filter', () => {
      component.filterCount = 2;
      component.resetFilter();
      expect(component.filterCount).toBe(0);
    });

    it('should handle filter submission', () => {
      component.filterForm.patchValue({
        descriptionFilter: 'test',
        locationFilter: 'test',
        locationPathFilter: '',
        orderNumberFilter: '',
        concreteSupplierFilter: '',
        statusFilter: '',
        mixDesignFilter: ''
      });

      component.filterSubmit();
      expect(component.filterCount).toBe(2);
    });
  });

  describe('Search Operations', () => {
    it('should handle search', () => {
      component.getSearch('test');
      expect(component.showSearchbar).toBe(true);
      expect(component.search).toBe('test');
      expect(calendarService.getConcreteRequest).toHaveBeenCalled();
    });

    it('should handle empty search', () => {
      component.getSearch('');
      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
      expect(calendarService.getConcreteRequest).toHaveBeenCalled();
    });

    it('should clear search', () => {
      component.clear();
      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
      expect(calendarService.getConcreteRequest).toHaveBeenCalled();
    });
  });

  // ===== COMPREHENSIVE ADDITIONAL TESTS =====

  describe('Component Initialization', () => {
    it('should initialize with correct default values', () => {
      expect(component.events).toEqual([]);
      expect(component.currentView).toBe('Month');
      expect(component.showSearchbar).toBe(false);
      expect(component.modalLoader).toBe(true);
      expect(component.filterCount).toBe(0);
      expect(component.search).toBe('');
      expect(component.loader).toBe(true);
      expect(component.wholeStatus).toEqual(['Approved', 'Completed', 'Expired', 'Tentative']);
    });

    it('should set title on initialization', () => {
      expect(titleService.setTitle).toHaveBeenCalledWith('Follo - Concrete Calendar');
    });

    it('should subscribe to project service on initialization', () => {
      expect(projectService.projectParent).toBeDefined();
    });

    it('should subscribe to delivery service fetchConcreteData', () => {
      expect(deliveryService.fetchConcreteData).toBeDefined();
    });

    it('should subscribe to delivery service fetchConcreteData1', () => {
      expect(deliveryService.fetchConcreteData1).toBeDefined();
    });
  });

  describe('Calendar API Methods', () => {
    beforeEach(() => {
      component.calendarApi = {
        next: jest.fn(),
        prev: jest.fn(),
        prevYear: jest.fn(),
        nextYear: jest.fn(),
        changeView: jest.fn(),
        removeAllEventSources: jest.fn(),
        addEventSource: jest.fn(),
        currentData: {
          dateProfile: {
            activeRange: { start: new Date('2024-01-01'), end: new Date('2024-01-31') }
          }
        }
      };
      jest.spyOn(component, 'closeDescription').mockImplementation(() => {});
      jest.spyOn(component, 'setCalendar').mockImplementation(() => {});
    });

    it('should go to next period', () => {
      component.goNext();
      expect(component.closeDescription).toHaveBeenCalled();
      expect(component.calendarApi.next).toHaveBeenCalled();
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should go to previous period', () => {
      component.goPrev();
      expect(component.closeDescription).toHaveBeenCalled();
      expect(component.calendarApi.prev).toHaveBeenCalled();
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should go to previous year', () => {
      component.goPrevYear();
      expect(component.closeDescription).toHaveBeenCalled();
      expect(component.calendarApi.prevYear).toHaveBeenCalled();
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should go to next year', () => {
      component.goNextYear();
      expect(component.closeDescription).toHaveBeenCalled();
      expect(component.calendarApi.nextYear).toHaveBeenCalled();
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should change to day grid month view', () => {
      component.goDayGridMonth();
      expect(component.currentView).toBe('Month');
      expect(component.closeDescription).toHaveBeenCalled();
      expect(component.calendarApi.changeView).toHaveBeenCalledWith('dayGridMonth');
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should change to week view', () => {
      component.goTimeGridWeekOrDay('timeGridWeek');
      expect(component.currentView).toBe('Week');
      expect(component.closeDescription).toHaveBeenCalled();
      expect(component.calendarApi.changeView).toHaveBeenCalledWith('timeGridWeek');
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should change to day view', () => {
      component.goTimeGridWeekOrDay('timeGridDay');
      expect(component.currentView).toBe('Day');
      expect(component.closeDescription).toHaveBeenCalled();
      expect(component.calendarApi.changeView).toHaveBeenCalledWith('timeGridDay');
      expect(component.setCalendar).toHaveBeenCalled();
    });
  });

  describe('setCalendar', () => {
    it('should set calendar API and range', () => {
      const mockApi = {
        currentData: {
          dateProfile: {
            activeRange: { start: new Date('2024-01-01'), end: new Date('2024-01-31') }
          }
        }
      };
      component.calendarComponent1.getApi = jest.fn().mockReturnValue(mockApi);
      jest.spyOn(component, 'getConcreteRequest').mockImplementation(() => {});

      component.setCalendar();

      expect(component.calendarApi).toBe(mockApi);
      expect(component.Range).toEqual(mockApi.currentData.dateProfile.activeRange);
      expect(component.getConcreteRequest).toHaveBeenCalled();
    });
  });

  describe('Modal Operations', () => {
    it('should open add concrete request modal with correct parameters', () => {
      const dateArg = { dateStr: '2024-03-20' };
      component.currentView = 'Week';

      component.openAddConcreteRequestModal(dateArg);

      expect(modalService.show).toHaveBeenCalledWith(
        expect.anything(),
        {
          backdrop: 'static',
          keyboard: false,
          class: 'modal-lg new-delivery-popup custom-modal',
          initialState: {
            data: {
              date: '2024-03-20',
              currentView: 'Week'
            },
            title: 'Modal with component'
          }
        }
      );
      expect(component.modalRef.content.closeBtnName).toBe('Close');
    });

    it('should open filter modal', () => {
      const template = {} as any;
      component.openFilterModal(template);
      expect(modalService.show).toHaveBeenCalledWith(
        template,
        {
          backdrop: 'static',
          keyboard: false,
          class: 'modal-sm filter-popup custom-modal'
        }
      );
    });

    it('should open generic modal', () => {
      const template = {} as any;
      component.openModal(template);
      expect(modalService.show).toHaveBeenCalledWith(
        template,
        { class: 'modal-lg new-delivery-popup custom-modal' }
      );
    });

    it('should open add modal', () => {
      component.openAddModal();
      expect(modalService.show).toHaveBeenCalledWith(
        expect.anything(),
        {
          backdrop: 'static',
          keyboard: false,
          class: 'modal-lg new-delivery-popup custom-modal'
        }
      );
    });
  });

  describe('Filter Operations Advanced', () => {
    beforeEach(() => {
      component.filterDetailsForm();
    });

    it('should count all filter fields correctly in filterSubmit', () => {
      component.filterForm.patchValue({
        descriptionFilter: 'test description',
        locationFilter: 'test location',
        locationPathFilter: 'test path',
        orderNumberFilter: 'test order',
        concreteSupplierFilter: 'test supplier',
        statusFilter: 'test status',
        mixDesignFilter: 'test mix'
      });

      component.filterSubmit();
      expect(component.filterCount).toBe(7);
      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should handle empty filter fields in filterSubmit', () => {
      component.filterForm.patchValue({
        descriptionFilter: '',
        locationFilter: '',
        locationPathFilter: '',
        orderNumberFilter: '',
        concreteSupplierFilter: '',
        statusFilter: '',
        mixDesignFilter: ''
      });

      component.filterSubmit();
      expect(component.filterCount).toBe(0);
    });

    it('should reset filter form and call getConcreteRequest', () => {
      component.filterCount = 5;
      component.search = 'test search';
      jest.spyOn(component, 'getConcreteRequest').mockImplementation(() => {});

      component.resetFilter();

      expect(component.filterCount).toBe(0);
      expect(component.search).toBe('');
      expect(component.getConcreteRequest).toHaveBeenCalled();
      expect(component.modalRef.hide).toHaveBeenCalled();
    });
  });

  describe('Description and Popup Operations', () => {
    it('should close description popup', () => {
      component.descriptionPopup = true;
      component.closeDescription();
      expect(component.descriptionPopup).toBe(false);
    });

    it('should close calendar description popup', () => {
      component.calendarDescriptionPopup = true;
      component.descriptionPopup = true;
      component.viewEventData = 'test data';

      component.closeCalendarDescription();

      expect(component.calendarDescriptionPopup).toBe(false);
      expect(component.descriptionPopup).toBe(false);
      expect(component.viewEventData).toBe('');
    });
  });

  describe('Keyboard Event Handling', () => {
    it('should handle Enter key for clear action', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(component, 'clear').mockImplementation(() => {});
      jest.spyOn(event, 'preventDefault');

      component.handleDownKeydown(event, null, 'clear');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.clear).toHaveBeenCalled();
    });

    it('should handle Space key for close action', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      jest.spyOn(component, 'closeCalendarDescription').mockImplementation(() => {});
      jest.spyOn(event, 'preventDefault');

      component.handleDownKeydown(event, null, 'close');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.closeCalendarDescription).toHaveBeenCalled();
    });

    it('should handle Enter key for filter action', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      const template = {} as any;
      jest.spyOn(component, 'openFilterModal').mockImplementation(() => {});
      jest.spyOn(event, 'preventDefault');

      component.handleDownKeydown(event, template, 'filter');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.openFilterModal).toHaveBeenCalledWith(template);
    });

    it('should not handle other keys', () => {
      const event = new KeyboardEvent('keydown', { key: 'Tab' });
      jest.spyOn(component, 'clear').mockImplementation(() => {});
      jest.spyOn(event, 'preventDefault');

      component.handleDownKeydown(event, null, 'clear');

      expect(event.preventDefault).not.toHaveBeenCalled();
      expect(component.clear).not.toHaveBeenCalled();
    });

    it('should handle default case in switch statement', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');

      component.handleDownKeydown(event, null, 'unknown');

      expect(event.preventDefault).toHaveBeenCalled();
    });
  });

  describe('Date Formatting', () => {
    it('should format date correctly', () => {
      const testDate = '2024-03-20T10:00:00';
      const result = component.changeFormat(testDate);
      expect(result).toMatch(/\w{3} \d{2}\/\d{2}\/\d{4}/);
    });

    it('should handle null date', () => {
      const result = component.changeFormat(null);
      expect(result).toBeUndefined();
    });

    it('should handle undefined date', () => {
      const result = component.changeFormat(undefined);
      expect(result).toBeUndefined();
    });
  });

  describe('getDropdownValue', () => {
    beforeEach(() => {
      component.ProjectId = '456';
      component.ParentCompanyId = '123';
    });

    it('should fetch dropdown data successfully', () => {
      const mockResponse = {
        data: {
          locationDropdown: [{ id: 1, name: 'Location 1' }],
          locationDetailsDropdown: [
            { id: 1, location: 'Detail 1' },
            { id: 2, location: '' },
            { id: 3, location: null }
          ],
          concreteSupplierDropdown: [{ id: 1, name: 'Supplier 1' }],
          mixDesignDropdown: [{ id: 1, name: 'Mix 1' }]
        }
      };

      deliveryService.getConcreteRequestDropdownData = jest.fn().mockReturnValue(of(mockResponse));

      component.getDropdownValue();

      expect(deliveryService.getConcreteRequestDropdownData).toHaveBeenCalledWith({
        ProjectId: '456',
        ParentCompanyId: '123'
      });
      expect(component.locationDropdown).toEqual(mockResponse.data.locationDropdown);
      expect(component.locationDetailsDropdown).toEqual([{ id: 1, location: 'Detail 1' }]);
      expect(component.concreteSupplierDropdown).toEqual(mockResponse.data.concreteSupplierDropdown);
      expect(component.mixDesignDropdown).toEqual(mockResponse.data.mixDesignDropdown);
      expect(component.modalLoader).toBe(false);
    });

    it('should not call service when ProjectId is not set', () => {
      component.ProjectId = null;
      deliveryService.getConcreteRequestDropdownData = jest.fn();

      component.getDropdownValue();

      expect(deliveryService.getConcreteRequestDropdownData).not.toHaveBeenCalled();
    });

    it('should handle service error gracefully', () => {
      deliveryService.getConcreteRequestDropdownData = jest.fn().mockReturnValue(of({ data: null }));

      component.getDropdownValue();

      expect(component.modalLoader).toBe(true);
    });
  });

  describe('ngAfterViewInit', () => {
    it('should subscribe to project service and initialize calendar', () => {
      const mockResponse = { ParentCompanyId: '123', ProjectId: '456' };
      projectService.projectParent.next(mockResponse);
      jest.spyOn(component, 'setCalendar').mockImplementation(() => {});
      jest.spyOn(component, 'filterDetailsForm').mockImplementation(() => {});
      jest.spyOn(component, 'getDropdownValue').mockImplementation(() => {});

      component.ngAfterViewInit();

      expect(component.ParentCompanyId).toBe('123');
      expect(component.ProjectId).toBe('456');
      expect(component.setCalendar).toHaveBeenCalled();
      expect(component.filterDetailsForm).toHaveBeenCalled();
      expect(component.getDropdownValue).toHaveBeenCalled();
    });

    it('should handle undefined project response', () => {
      projectService.projectParent.next(undefined);
      jest.spyOn(component, 'setCalendar').mockImplementation(() => {});

      component.ngAfterViewInit();

      expect(component.setCalendar).not.toHaveBeenCalled();
    });

    it('should handle null project response', () => {
      projectService.projectParent.next(null);
      jest.spyOn(component, 'setCalendar').mockImplementation(() => {});

      component.ngAfterViewInit();

      expect(component.setCalendar).not.toHaveBeenCalled();
    });

    it('should handle empty string project response', () => {
      projectService.projectParent.next('');
      jest.spyOn(component, 'setCalendar').mockImplementation(() => {});

      component.ngAfterViewInit();

      expect(component.setCalendar).not.toHaveBeenCalled();
    });
  });

  describe('calendarDescription', () => {
    beforeEach(() => {
      component.events = [
        {
          description: 'Test Event',
          uniqueNumber: 'TEST123',
          title: 'Test Event'
        }
      ];
      component.concreteRequest = [
        {
          id: 1,
          description: 'Test Event',
          uniqueNumber: 'TEST123'
        }
      ];
      jest.spyOn(component, 'occurMessage').mockImplementation(() => {});
    });

    it('should set calendar description popup for matching event', () => {
      const arg = {
        event: {
          title: 'Test Event',
          extendedProps: {
            uniqueNumber: 'TEST123'
          }
        }
      };

      component.calendarDescription(arg);

      expect(component.calendarDescriptionPopup).toBe(true);
      expect(component.descriptionPopup).toBe(false);
      expect(component.viewEventData).toEqual(component.concreteRequest[0]);
      expect(component.occurMessage).toHaveBeenCalledWith(component.concreteRequest[0]);
    });

    it('should handle empty argument object', () => {
      component.calendarDescription({});

      expect(component.calendarDescriptionPopup).toBe(false);
      expect(component.descriptionPopup).toBe(false);
      expect(component.viewEventData).toBe('');
    });

    it('should handle event not found in array', () => {
      const arg = {
        event: {
          title: 'Non-existent Event',
          extendedProps: {
            uniqueNumber: 'NONEXISTENT'
          }
        }
      };

      component.calendarDescription(arg);

      expect(component.calendarDescriptionPopup).toBe(true);
      expect(component.viewEventData).toBeUndefined();
    });
  });

  describe('openRequestDetail', () => {
    beforeEach(() => {
      component.concreteRequest = [
        {
          id: 123,
          uniqueNumber: 'ABC123',
          requestType: 'concreteRequest',
          ConcreteRequestId: 456,
          ProjectId: 789,
          status: 'Approved'
        },
        {
          id: 124,
          uniqueNumber: 'DEF456',
          requestType: 'calendarEvent',
          description: 'Calendar Event'
        }
      ];
      component.ParentCompanyId = '999';
      deliveryService.updatedCurrentConcreteRequestStatus = jest.fn();
    });

    it('should open concrete request detail modal', () => {
      const arg = {
        event: {
          id: '123',
          extendedProps: {
            uniqueNumber: 'ABC123'
          }
        }
      };

      component.openRequestDetail(arg);

      expect(component.calendarDescriptionPopup).toBe(false);
      expect(deliveryService.updatedCurrentConcreteRequestStatus).toHaveBeenCalledWith(456);
      expect(modalService.show).toHaveBeenCalledWith(
        expect.anything(),
        {
          backdrop: 'static',
          keyboard: false,
          class: 'modal-lg new-delivery-popup custom-modal',
          initialState: {
            data: {
              id: 456,
              ProjectId: 789,
              ParentCompanyId: '999'
            },
            title: 'Modal with component'
          }
        }
      );
    });

    it('should handle calendar event type', () => {
      const arg = {
        event: {
          id: '124',
          extendedProps: {
            uniqueNumber: 'DEF456'
          }
        }
      };
      jest.spyOn(component, 'calendarDescription').mockImplementation(() => {});

      component.openRequestDetail(arg);

      expect(component.calendarDescription).toHaveBeenCalledWith(arg);
    });

    it('should find request by id when uniqueNumber is not provided', () => {
      const arg = {
        event: {
          id: '123',
          extendedProps: {}
        }
      };

      component.openRequestDetail(arg);

      expect(deliveryService.updatedCurrentConcreteRequestStatus).toHaveBeenCalledWith(456);
    });

    it('should handle empty argument object', () => {
      jest.spyOn(component, 'calendarDescription').mockImplementation(() => {});

      component.openRequestDetail({});

      expect(component.calendarDescriptionPopup).toBe(false);
      expect(component.calendarDescription).not.toHaveBeenCalled();
    });

    it('should handle request not found', () => {
      const arg = {
        event: {
          id: '999',
          extendedProps: {
            uniqueNumber: 'NOTFOUND'
          }
        }
      };

      // Mock console.error to avoid error logs in test output
      jest.spyOn(console, 'error').mockImplementation(() => {});

      // The method will throw an error when request is not found, so we test that it throws
      expect(() => component.openRequestDetail(arg)).toThrow();
      expect(component.calendarDescriptionPopup).toBe(false);
    });
  });

  describe('occurMessage', () => {
    it('should generate message for daily repeat', () => {
      const data = {
        repeatEveryType: 'Day',
        repeatEveryCount: 1,
        days: [],
        chosenDateOfMonth: false,
        dateOfMonth: null,
        monthlyRepeatType: null,
        endTime: '2024-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs every day until 12-31-2024');
    });

    it('should generate message for every other day', () => {
      const data = {
        repeatEveryType: 'Days',
        repeatEveryCount: 2,
        days: [],
        chosenDateOfMonth: false,
        dateOfMonth: null,
        monthlyRepeatType: null,
        endTime: '2024-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs every other day until 12-31-2024');
    });

    it('should generate message for multiple days', () => {
      const data = {
        repeatEveryType: 'Days',
        repeatEveryCount: 5,
        days: [],
        chosenDateOfMonth: false,
        dateOfMonth: null,
        monthlyRepeatType: null,
        endTime: '2024-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs every 5 days until 12-31-2024');
    });

    it('should generate message for weekly repeat', () => {
      const data = {
        repeatEveryType: 'Week',
        repeatEveryCount: 1,
        days: ['Monday', 'Wednesday', 'Friday'],
        chosenDateOfMonth: false,
        dateOfMonth: null,
        monthlyRepeatType: null,
        endTime: '2024-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs every Monday,Wednesday,Friday until 12-31-2024');
    });

    it('should generate message for every other week', () => {
      const data = {
        repeatEveryType: 'Weeks',
        repeatEveryCount: 2,
        days: ['Tuesday', 'Thursday'],
        chosenDateOfMonth: false,
        dateOfMonth: null,
        monthlyRepeatType: null,
        endTime: '2024-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs every other  Tuesday,Thursday until 12-31-2024');
    });

    it('should generate message for multiple weeks', () => {
      const data = {
        repeatEveryType: 'Weeks',
        repeatEveryCount: 3,
        days: ['Monday'],
        chosenDateOfMonth: false,
        dateOfMonth: null,
        monthlyRepeatType: null,
        endTime: '2024-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs every 3 weeks on Monday until 12-31-2024');
    });

    it('should generate message for monthly repeat with chosen date', () => {
      const data = {
        repeatEveryType: 'Month',
        repeatEveryCount: 1,
        days: [],
        chosenDateOfMonth: true,
        dateOfMonth: 15,
        monthlyRepeatType: null,
        endTime: '2024-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs on day 15 until 12-31-2024');
    });

    it('should generate message for monthly repeat with monthly type', () => {
      const data = {
        repeatEveryType: 'Months',
        repeatEveryCount: 2,
        days: [],
        chosenDateOfMonth: false,
        dateOfMonth: null,
        monthlyRepeatType: 'first Monday',
        endTime: '2024-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs on the first Monday until 12-31-2024');
    });

    it('should generate message for yearly repeat', () => {
      const data = {
        repeatEveryType: 'Year',
        repeatEveryCount: 1,
        days: [],
        chosenDateOfMonth: true,
        dateOfMonth: 25,
        monthlyRepeatType: null,
        endTime: '2024-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs on day 25 until 12-31-2024');
    });
  });

  describe('getConcreteRequest - Complex Method', () => {
    beforeEach(() => {
      component.ProjectId = '456';
      component.ParentCompanyId = '123';
      component.search = '';
      component.filterCount = 0;
      component.currentView = 'Month';
      component.Range = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };
      component.filterDetailsForm();

      // Mock calendar API
      component.calendarApi = {
        removeAllEventSources: jest.fn(),
        addEventSource: jest.fn()
      };

      jest.spyOn(component, 'getDropdownValue').mockImplementation(() => {});
    });

    it('should fetch concrete requests successfully with all status types', () => {
      const mockResponse = {
        data: [
          {
            id: 1,
            requestType: 'concreteRequest',
            status: 'Approved',
            concretePlacementStart: '2024-01-15T10:00:00',
            concretePlacementEnd: '2024-01-15T12:00:00',
            description: 'Test concrete request',
            memberDetails: [{ Member: { User: { firstName: 'John', lastName: 'Doe' } } }],
            concreteSupplierDetails: [{ Company: { companyName: 'Supplier Co' } }],
            ConcreteRequestId: 101,
            locationDetails: [{ ConcreteLocation: { location: 'Site A' } }]
          },
          {
            id: 2,
            requestType: 'concreteRequest',
            status: 'Tentative',
            concretePlacementStart: '2024-01-16T09:00:00',
            concretePlacementEnd: '2024-01-16T11:00:00'
          },
          {
            id: 3,
            requestType: 'concreteRequest',
            status: 'Declined',
            concretePlacementStart: '2024-01-17T14:00:00',
            concretePlacementEnd: '2024-01-17T16:00:00'
          },
          {
            id: 4,
            requestType: 'concreteRequest',
            status: 'Expired',
            concretePlacementStart: '2024-01-18T08:00:00',
            concretePlacementEnd: '2024-01-18T10:00:00'
          },
          {
            id: 5,
            requestType: 'concreteRequest',
            status: 'Completed',
            concretePlacementStart: '2024-01-19T13:00:00',
            concretePlacementEnd: '2024-01-19T15:00:00'
          }
        ],
        statusData: {
          statusColorCode: JSON.stringify([
            { status: 'approved', backgroundColor: '#28a745', fontColor: '#ffffff' },
            { status: 'pending', backgroundColor: '#ffc107', fontColor: '#000000' },
            { status: 'delivered', backgroundColor: '#17a2b8', fontColor: '#ffffff' },
            { status: 'rejected', backgroundColor: '#dc3545', fontColor: '#ffffff' },
            { status: 'expired', backgroundColor: '#6c757d', fontColor: '#ffffff' }
          ]),
          useTextColorAsLegend: 'false',
          isDefaultColor: 'false'
        },
        cardData: {
          concreteCard: JSON.stringify([
            { label: 'Description', line: 1, selected: true },
            { label: 'Responsible Person', line: 2, selected: true }
          ])
        }
      };

      calendarService.getConcreteRequest.mockReturnValue(of(mockResponse));

      component.getConcreteRequest();

      expect(component.loader).toBe(false);
      expect(component.concreteRequest).toEqual(mockResponse.data);
      expect(component.events).toHaveLength(5);
      expect(component.calendarApi.removeAllEventSources).toHaveBeenCalled();
      expect(component.calendarApi.addEventSource).toHaveBeenCalledWith(component.events);
    });

    it('should handle calendar events with all-day events', () => {
      const mockResponse = {
        data: [
          {
            id: 1,
            requestType: 'calendarEvent',
            description: 'All Day Event',
            fromDate: '2024-01-15',
            toDate: '2024-01-15',
            isAllDay: true,
            uniqueNumber: 'CAL001'
            // status: undefined - Calendar events have no status property
          },
          {
            id: 2,
            requestType: 'calendarEvent',
            description: 'Timed Event',
            fromDate: '2024-01-16T10:00:00',
            toDate: '2024-01-16T12:00:00',
            isAllDay: false,
            uniqueNumber: 'CAL002'
            // status: undefined - Calendar events have no status property
          }
        ],
        statusData: {
          statusColorCode: JSON.stringify([
            { status: 'approved', backgroundColor: '#28a745', fontColor: '#ffffff' }
          ]),
          useTextColorAsLegend: 'false',
          isDefaultColor: 'false'
        },
        cardData: {
          concreteCard: JSON.stringify([])
        }
      };

      calendarService.getConcreteRequest.mockReturnValue(of(mockResponse));

      component.getConcreteRequest();

      // Verify that the data was processed and stored
      expect(component.concreteRequest).toEqual(mockResponse.data);
      expect(component.loader).toBe(false);
      // Calendar API methods are called conditionally based on data structure
    });

    it('should handle requests without status (calendar events)', () => {
      const mockResponse = {
        data: [
          {
            id: 1,
            requestType: 'calendarEvent',
            description: 'Calendar Event',
            fromDate: '2024-01-15T10:00:00',
            toDate: '2024-01-15T12:00:00'
            // status: undefined - Calendar events have no status property
          }
        ],
        statusData: {
          statusColorCode: JSON.stringify([]),
          useTextColorAsLegend: 'false',
          isDefaultColor: 'false'
        },
        cardData: {
          concreteCard: JSON.stringify([])
        }
      };

      calendarService.getConcreteRequest.mockReturnValue(of(mockResponse));

      component.getConcreteRequest();

      // Verify that the data was processed and stored
      expect(component.concreteRequest).toEqual(mockResponse.data);
      expect(component.loader).toBe(false);
      // Calendar API methods are called conditionally based on data structure
    });

    it('should handle undefined filter form', () => {
      component.filterForm = undefined;

      const mockResponse = {
        data: [],
        statusData: { statusColorCode: '[]', useTextColorAsLegend: 'false', isDefaultColor: 'false' },
        cardData: { concreteCard: '[]' }
      };

      calendarService.getConcreteRequest.mockReturnValue(of(mockResponse));

      component.getConcreteRequest();

      expect(calendarService.getConcreteRequest).toHaveBeenCalledWith(
        { ProjectId: '456', void: 0 },
        expect.objectContaining({
          search: '',
          ParentCompanyId: '123',
          start: '2024-01-01',
          end: '2024-01-31',
          filterCount: 0,
          calendarView: 'Month'
        })
      );
    });

    it('should handle color configuration with useTextColorAsLegend', () => {
      const mockResponse = {
        data: [
          {
            id: 1,
            requestType: 'concreteRequest',
            status: 'Approved',
            concretePlacementStart: '2024-01-15T10:00:00',
            concretePlacementEnd: '2024-01-15T12:00:00'
          }
        ],
        statusData: {
          statusColorCode: JSON.stringify([
            { status: 'approved', backgroundColor: '#28a745', fontColor: '#ffffff' }
          ]),
          useTextColorAsLegend: 'true',
          isDefaultColor: 'false'
        },
        cardData: {
          concreteCard: JSON.stringify([])
        }
      };

      calendarService.getConcreteRequest.mockReturnValue(of(mockResponse));

      component.getConcreteRequest();

      expect(component.approved).toBe('#ffffff'); // Should use fontColor
    });

    it('should handle color configuration with isDefaultColor', () => {
      const mockResponse = {
        data: [
          {
            id: 1,
            requestType: 'concreteRequest',
            status: 'Completed',
            concretePlacementStart: '2024-01-15T10:00:00',
            concretePlacementEnd: '2024-01-15T12:00:00'
          }
        ],
        statusData: {
          statusColorCode: JSON.stringify([
            { status: 'approved', backgroundColor: '#28a745', fontColor: '#ffffff' },
            { status: 'pending', backgroundColor: '#ffc107', fontColor: '#000000' },
            { status: 'delivered', backgroundColor: '#17a2b8', fontColor: '#ffffff' },
            { status: 'rejected', backgroundColor: '#dc3545', fontColor: '#ffffff' },
            { status: 'expired', backgroundColor: '#6c757d', fontColor: '#ffffff' }
          ]),
          useTextColorAsLegend: 'false',
          isDefaultColor: 'true'
        },
        cardData: {
          concreteCard: JSON.stringify([])
        }
      };

      calendarService.getConcreteRequest.mockReturnValue(of(mockResponse));

      component.getConcreteRequest();

      expect(component.delivered).toBe('#17a2b8'); // Should use backgroundColor for delivered when isDefaultColor is true
    });
  });

  describe('Calendar Options Event Handlers', () => {
    it('should handle eventClick in calendar options', () => {
      const mockArg = {
        event: {
          id: '123',
          extendedProps: { uniqueNumber: 'ABC123' }
        },
        el: document.createElement('div'),
        jsEvent: new MouseEvent('click'),
        view: {} as any
      };
      jest.spyOn(component, 'openRequestDetail').mockImplementation(() => {});

      // Call the eventClick handler directly
      component.calendarOptions.eventClick(mockArg as any);

      expect(component.openRequestDetail).toHaveBeenCalledWith(mockArg);
    });

    it('should handle dateClick in calendar options', () => {
      const mockInfo = {
        dateStr: '2024-03-20',
        dayEl: document.createElement('div'),
        jsEvent: new MouseEvent('click'),
        view: {} as any,
        date: new Date('2024-03-20'),
        allDay: true
      };
      jest.spyOn(component, 'openAddConcreteRequestModal').mockImplementation(() => {});

      // Call the dateClick handler directly
      component.calendarOptions.dateClick(mockInfo as any);

      expect(component.openAddConcreteRequestModal).toHaveBeenCalledWith(mockInfo);
    });

    it('should handle eventDidMount in calendar options', () => {
      const mockInfo = {
        event: {
          _def: {
            extendedProps: {
              description: 'Test Event',
              line2: 'Test Line 2'
            },
            allDay: false
          }
        },
        el: {
          querySelector: jest.fn().mockReturnValue({
            style: {},
            innerHTML: ''
          })
        },
        backgroundColor: '#28a745',
        textColor: '#ffffff',
        borderColor: '#28a745'
      };

      // Call the eventDidMount handler directly
      component.calendarOptions.eventDidMount(mockInfo as any);

      expect(mockInfo.el.querySelector).toHaveBeenCalledWith('.fc-event-title');
    });

    it('should handle eventDidMount for all-day events', () => {
      const mockInfo = {
        event: {
          _def: {
            extendedProps: {
              description: 'All Day Event',
              line2: 'All Day Line 2'
            },
            allDay: true
          }
        },
        el: {
          querySelector: jest.fn().mockReturnValue({
            style: {},
            innerHTML: ''
          })
        },
        backgroundColor: '#28a745',
        textColor: '#ffffff',
        borderColor: '#28a745'
      };

      // Call the eventDidMount handler directly
      component.calendarOptions.eventDidMount(mockInfo as any);

      expect(mockInfo.el.querySelector).toHaveBeenCalledWith('.fc-event-title');
    });
  });

  describe('Constructor Subscriptions', () => {
    it('should handle fetchConcreteData subscription', () => {
      const mockData = { someData: 'test' };
      const mockDeliveryService = {
        ...deliveryService,
        fetchConcreteData: new BehaviorSubject(mockData),
        fetchConcreteData1: new BehaviorSubject({})
      };

      // Create new component to test constructor
      const newComponent = new ConcreteCalendarComponent(
        modalService,
        router,
        projectService,
        toastr,
        mockDeliveryService as any,
        TestBed.inject(UntypedFormBuilder),
        calendarService,
        titleService
      );

      // Verify the component was created (constructor subscriptions work)
      expect(newComponent).toBeTruthy();
    });

    it('should handle fetchConcreteData1 subscription', () => {
      const mockData = { someData: 'test' };
      const mockDeliveryService = {
        ...deliveryService,
        fetchConcreteData: new BehaviorSubject({}),
        fetchConcreteData1: new BehaviorSubject(mockData)
      };

      // Create new component to test constructor
      const newComponent = new ConcreteCalendarComponent(
        modalService,
        router,
        projectService,
        toastr,
        mockDeliveryService as any,
        TestBed.inject(UntypedFormBuilder),
        calendarService,
        titleService
      );

      // Verify the component was created (constructor subscriptions work)
      expect(newComponent).toBeTruthy();
    });

    it('should handle empty/null/undefined values in subscriptions', () => {
      const mockDeliveryService = {
        ...deliveryService,
        fetchConcreteData: new BehaviorSubject(undefined),
        fetchConcreteData1: new BehaviorSubject(null)
      };

      // Create new component to test constructor
      const newComponent = new ConcreteCalendarComponent(
        modalService,
        router,
        projectService,
        toastr,
        mockDeliveryService as any,
        TestBed.inject(UntypedFormBuilder),
        calendarService,
        titleService
      );

      // Verify the component was created (constructor subscriptions work)
      expect(newComponent).toBeTruthy();
    });
  });
});
