<div class="mx-2 mx-md-5 my-5 bulkupload">
  <ngx-file-drop
    dropZoneLabel="Drop files here"
    (onFileDrop)="dropped($event)"
    (onFileOver)="fileOver($event)"
    (onFileLeave)="fileLeave($event)"
    multiple="true"
  >
    <ng-template ngx-file-drop-content-tmp let-openFileSelector="openFileSelector">
      <div class="bulkupload-content text-center" (click)="openFileSelector()" (keydown)="openFileSelector()">
        <img src="./assets/images/file.svg" alt="Excel" id="browse" />
        <p class="fs14 fw600 mb3 pt10 color-grey7">Drag & Drop your file here</p>
        <p class="fs12 color-grey8 fw500 mb3">Or</p>
        <label class="color-blue4 fs14 mb10 fw500 text-underline" for="browse">Click here to browse</label>
      </div>
    </ng-template>
  </ngx-file-drop>
  <p class="fs10 color-grey8 fw500 my-2 text-right">
    *Supported formats are .jpg, .png, .doc and .pdf
  </p>
  <div *ngFor="let data of fileData; let firstIndex = index">
    <div class="row upload-table py-3 m-0" *ngFor="let item of data; let i = index">
      <div class="col-1 col-md-1 ps-0">
        <img
          src="{{ item.image }}"
          alt="attached-file"
          class="rounded upload-img"
          *ngIf="item.extension != 'pdf' && item.extension != 'doc'"
        />
        <img
          src="./assets/images/pdf-icon.png"
          alt="attached-file"
          class="rounded upload-img"
          *ngIf="item.extension === 'pdf'"
        />
        <img
          src="./assets/images/doc-icon.png"
          alt="attached-file"
          class="rounded upload-img"
          *ngIf="item.extension === 'doc'"
        />
      </div>
      <div class="col-9 col-md-9">
        <h1 class="fs16 ms-4 color-grey7">{{ item?.relativePath }}</h1>
        <p class="ms-4 mb-0 color-grey8"><span class="ms-3"></span></p>
      </div>
      <div
        class="col-1 col-md-2 text-right d-flex justify-content-end pe-0"
        (click)="removeFile(firstIndex, i)" (keydown)="handleDownKeydown($event, firstIndex,i,'remove')"
      >
        <img src="./assets/images/delete.svg" alt="delete" class="h-15px c-pointer" />
      </div>
    </div>
  </div>
  <div class="text-center">
    <button
      class="btn btn-orange radius20 fs12 fw-bold cairo-regular my-4 px-5 spin-btn"
      type="submit"
      (click)="uploadData()"
      *ngIf="fileData[0]?.length > 0"
      [disabled]="uploadSubmitted"
    >
      <em class="fa fa-spinner" aria-hidden="true" *ngIf="uploadSubmitted"></em>
      Done
    </button>
  </div>

  <h5 class="fw600 fs14" *ngIf="fileArray.length > 0">Uploaded Documents</h5>
  <div
    class="row align-items-center upload-table py-3 m-0"
    *ngFor="let item of fileArray; let i = index"
    [ngClass]="fileArray.length - 1 == i ? '' : 'upload-layout'"
  >
    <div class="col-1 col-md-1 ps-0">
      <a href="{{ item.attachement }}">
        <img
          src="{{ item.attachement }}"
          alt="attached-file"
          class="rounded upload-img"
          *ngIf="item.extension != 'pdf' && item.extension != 'doc'"
        />
        <img
          src="./assets/images/pdf-icon.png"
          alt="attached-file"
          class="rounded upload-img"
          *ngIf="item.extension === 'pdf'"
        />
        <img
          src="./assets/images/doc-icon.png"
          alt="attached-file"
          class="rounded upload-img"
          *ngIf="item.extension === 'doc'"
        />
      </a>
    </div>
    <div class="col-9 col-md-9">
      <h1 class="fs16 ms-4 color-grey7">
        <a href="{{ item.attachement }}">{{ item.filename }}</a>
      </h1>
      <p class="ms-4 mb-0 color-grey8">
        {{ item.createdAt | date : 'medium' }}<span class="ms-3"></span>
      </p>
    </div>
    <div
      class="col-1 col-md-2 text-right d-flex justify-content-end pe-0"
      *ngIf="authUser.RoleId !== 4"
    >
      <button
        class="bg-white border-0 outline-0"
        (click)="removeExistingFile(item)"
        [disabled]="deleteUploadedFile"
      >
        <img src="./assets/images/delete.svg" alt="delete" class="h-15px c-pointer" />
        <em
        class="fa fa-spinner ms-2"
        aria-hidden="true"
        *ngIf="loader"
      ></em
      >
      </button>
    </div>
  </div>
</div>
