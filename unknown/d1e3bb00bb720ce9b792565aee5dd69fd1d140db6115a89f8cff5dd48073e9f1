/* eslint-disable no-lonely-if */
/* eslint-disable no-nested-ternary */
/* eslint-disable max-lines-per-function */
/* eslint-disable no-underscore-dangle */
import { Component, ViewChild, TemplateRef } from '@angular/core';
import { FullCalendarComponent } from '@fullcalendar/angular';
import { CalendarOptions } from '@fullcalendar/core';
import interactionPlugin from '@fullcalendar/interaction';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import moment from 'moment';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { Title } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { NewCraneRequestCreationFormComponent } from '../crane-requests/new-crane-request-creation-form/new-crane-request-creation-form.component';
import { ProjectService } from '../services/profile/project.service';
import { CalendarService } from '../services/profile/calendar.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { CraneRequestDetailViewHeaderComponent } from '../crane-requests/crane-request-detail-view-header/crane-request-detail-view-header.component';
import { EditCraneRequestComponent } from '../crane-requests/edit-crane-request/edit-crane-request.component';
import { EditDeliveryFormComponent } from '../delivery-requests/delivery-details/edit-delivery-form/edit-delivery-form.component';
import { DeliveryDetailsNewComponent } from '../delivery-requests/delivery-details/delivery-details-new/delivery-details-new.component';
import { MixpanelService } from '../services/mixpanel.service';

@Component({
  selector: 'app-crane-calendar',
  templateUrl: './crane-calendar.component.html',
})
export class CraneCalendarComponent {
  public events: any = [];

  public voidSubmitted = false;

  public submitted = false;

  public formSubmitted = false;

  public descriptionPopup = false;

  public calendarDescriptionPopup = false;

  public viewEventData: any;

  public message = '';

  public calendarApi: any;

  public Range: any = {};

  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public lastId: any = 0;

  public loader = true;

  public eventData: any = {};

  public deliveryRequestWithCraneEquipmentType: any[];

  public ProjectId: any;

  public filterForm: UntypedFormGroup;

  public search = '';

  public craneRequestId: any;

  public calendarCurrentDeliveryIndex: string | number;

  public authUser: any = {};

  public ParentCompanyId: any;

  public companyList: any = [];

  public defineList: any = [];

  public gateList: any = [];

  public equipmentList: any = [];

  public modalLoader = false;

  public dropdownSettings: IDropdownSettings;

  public definableDropdownSettings: IDropdownSettings;

  public filterCount = 0;

  public wholeStatus = ['Approved', 'Completed', 'Declined', 'Delivered', 'Pending'];

  public memberList: any = [];

  public statusValue: any = [];

  public showSearchbar = false;

  public toolTipContent = '';

  public currentViewMonth: moment.MomentInput;

  public currentView = 'Month';

  public monthlyEventloader = true;

  public monthEvents = [];

  public subscription: any;

  public subscription1: any;

  public seriesOptions = [];

  public allRequestIsOpened = false;

  public approved: string;

  public pending: string;

  public rejected: string;

  public delivered: string;

  public expired: string;

  public locationList: any = [];

  @ViewChild('fullcalendar', { static: true }) public calendarComponent1: FullCalendarComponent;

  public calendarOptions: CalendarOptions = {
    selectable: true,
    initialView: 'dayGridMonth',
    plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin],
    aspectRatio: 2,
    expandRows: true,
    nowIndicator: true,
    dayMaxEvents: true,
    contentHeight: 'liquid',
    fixedWeekCount: false,
    slotEventOverlap: false,
    events: this.events,
    eventDidMount(info): void {
      const argument = info;
      const line1 = argument.event._def.extendedProps.description;
      const { line2 } = argument.event._def.extendedProps;
      const eventTitleElement: HTMLElement = argument.el.querySelector('.fc-event-title');
      eventTitleElement.style.backgroundColor = info.backgroundColor;
      eventTitleElement.style.color = info.textColor;
      eventTitleElement.style.borderLeft = `4px solid ${info.borderColor}`;
      if (argument.event._def.allDay) {
        eventTitleElement.innerHTML = `
        <div class="d-flex flex-column">
        <p class="m-0"> <img  class="w10 h10" src="./assets/images/noun-event-alert.svg" class="form-icon" alt="allday" >  ${line1} </p>
        <small> ${line2}</small>
        </div>`;
      } else {
        // eslint-disable-next-line no-param-reassign
        eventTitleElement.innerHTML = `
        <div class="d-flex flex-column">
        <p class="m-0"> ${line1} </p>
        <small> ${line2}</small>
        </div>`;
      }
    },
    customButtons: {
      prev: {
        text: 'PREV',
        click: (): void => this.goPrev(),
      },
      next: {
        text: 'Next',
        click: (): void => this.goNext(),
      },
      prevYear: {
        text: 'PREV',
        click: (): void => this.goPrevYear(),
      },
      nextYear: {
        text: 'Next',
        click: (): void => this.goNextYear(),
      },
      timeGridWeek: {
        text: 'Week',
        click: (): void => this.goTimeGridWeekOrDay('timeGridWeek'),
      },
      timeGridDay: {
        text: 'Day',
        click: (): void => this.goTimeGridWeekOrDay('timeGridDay'),
      },
      dayGridMonth: {
        text: 'Month',
        click: (): void => this.goDayGridMonth(),
      },
    },
    showNonCurrentDates: false,
    headerToolbar: {
      right: '',
      center: 'prevYear,prev,title,next,nextYear',
      left: 'dayGridMonth,timeGridWeek,timeGridDay',
    },
    firstDay: 0,
    eventClick: (arg): void => {
      this.deliveryDescription(arg);
    },
    datesSet: (info): void => {
      this.currentViewMonth = info.view.title;
    },
    eventTimeFormat: {
      hour: 'numeric',
      minute: '2-digit',
      meridiem: 'short',
    },
    views: {
      dayGridMonth: {
        allDaySlot: true,
      },
      timeGridWeek: {
        allDaySlot: true,
      },
      timeGridDay: {
        allDaySlot: true,
      },
    },
    dateClick: (info): void => {
      this.openAddCraneRequestModal(info);
    },
  };

  public constructor(
    public bsModalRef: BsModalRef,
    private readonly modalService: BsModalService,
    private readonly mixpanelService: MixpanelService,
    public projectService: ProjectService,
    private readonly formBuilder: UntypedFormBuilder,
    public calendarService: CalendarService,
    private readonly router: Router,
    private readonly toastr: ToastrService,
    public deliveryService: DeliveryService,
    private readonly titleService: Title,
  ) {
    this.titleService.setTitle('Follo - Crane Calendar');
    this.filterDetailsForm();
    this.deliveryService.fetchData.subscribe((getEventNDR): void => {
      if (getEventNDR !== undefined && getEventNDR !== null && getEventNDR !== '') {
        this.getDeliveryRequestWithCraneEquipmentType();
      }
    });
    this.deliveryService.fetchData1.subscribe((getEventNDR): void => {
      if (getEventNDR !== undefined && getEventNDR !== null && getEventNDR !== '') {
        this.getDeliveryRequestWithCraneEquipmentType();
      }
    });
    this.deliveryService.getCurrentCraneRequestStatus.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.craneRequestId = res;
        this.getDeliveryRequestWithCraneEquipmentType();
      }
    });
  }

  public openAddCraneRequestModal(dateArg): void {
    const passData = {
      date: dateArg.dateStr,
      currentView: this.currentView,
    };
    const initialState = {
      data: passData,
      title: 'Modal with component',
    };
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(NewCraneRequestCreationFormComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
      initialState,
    });
    this.modalRef.content.lastId = this.lastId;
    this.modalRef.content.closeBtnName = 'Close';
  }

  public goNext(): void {
    this.closeDescription();
    this.calendarApi.next();
    this.setCalendar();
  }

  public goTimeGridWeekOrDay(view: string): void {
    if (view === 'timeGridWeek') {
      this.currentView = 'Week';
    } else {
      this.currentView = 'Day';
    }
    this.closeDescription();
    this.calendarApi.changeView(view);
    this.setCalendar();
  }

  public changeRequestCollapse(data): void {
    this.initializeSeriesOption();
    if (!moment(data.craneDeliveryStart).isAfter(moment())) {
      this.seriesOptions = this.seriesOptions.filter((object): any => {
        const seriesObject = object;
        if (seriesObject.option !== 1) {
          seriesObject.disabled = true;
        }
        return seriesObject;
      });
    }
    this.allRequestIsOpened = !this.allRequestIsOpened;
  }

  public goPrevYear(): void {
    this.closeDescription();
    this.calendarApi.prevYear(); // call a method on the Calendar object
    this.setCalendar();
  }

  public initializeSeriesOption(): void {
    this.seriesOptions = [
      {
        option: 1,
        text: 'This event',
        disabled: false,
      },
      {
        option: 2,
        text: 'This and all following events',
        disabled: false,
      },
    ];
  }

  public goNextYear(): void {
    this.closeDescription();
    this.calendarApi.nextYear(); // call a method on the Calendar object
    this.setCalendar();
  }

  public goDayGridMonth(): void {
    this.currentView = 'Month';
    this.closeDescription();
    this.calendarApi.changeView('dayGridMonth');
    this.setCalendar();
  }

  public goPrev(): void {
    this.closeDescription();
    this.calendarApi.prev(); // call a method on the Calendar object
    this.setCalendar();
  }

  public closeDescription(): void {
    this.descriptionPopup = false;
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.getDeliveryRequestWithCraneEquipmentType();
  }

  public getSearchNDR(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    if (data.length >= 1) {
      this.search = data;
      this.getDeliveryRequestWithCraneEquipmentType();
    }
    if (data.length === 0) {
      this.search = '';
      this.getDeliveryRequestWithCraneEquipmentType();
    }
  }

  public setCalendar(): void {
    this.calendarApi = this.calendarComponent1.getApi();
    this.Range = this.calendarApi?.currentData?.dateProfile?.activeRange;
    this.getDeliveryRequestWithCraneEquipmentType();
  }

  public getResponsiblePeople(object: { firstName: any; lastName: any }): any {
    if (object?.firstName && object?.lastName) {
      const string = `${object.firstName} ${object.lastName}`;
      const matches = string.match(/\b(\w)/g);
      const acronym = matches.join('').toUpperCase();
      return acronym;
    }
    return 'UU';
  }

  public deliveryDescription(arg): void {
    this.descriptionPopup = false;
    this.allRequestIsOpened = false;
    this.calendarDescriptionPopup = false;
    this.eventData = {};
    if (Object.keys(arg).length !== 0) {
      let index: number;
      if (arg.event.extendedProps.uniqueNumber) {
        index = this.deliveryRequestWithCraneEquipmentType.findIndex(
          (item: { id: any; uniqueNumber: any }): any => item.id === +arg.event.id && item.uniqueNumber === arg.event.extendedProps.uniqueNumber,
        );
      } else {
        index = this.deliveryRequestWithCraneEquipmentType.findIndex(
          (item): any => item.CraneRequestId === +arg.event.id,
        );
      }
      this.eventData = this.deliveryRequestWithCraneEquipmentType[index];
      if (this.eventData.requestType === 'craneRequest') {
        this.calendarCurrentDeliveryIndex = index;
        this.eventData.startDate = moment(
          this.deliveryRequestWithCraneEquipmentType[this.calendarCurrentDeliveryIndex]
            .craneDeliveryStart,
        ).format('MM/DD/YYYY');
        if (this.eventData.approved_at !== null) {
          this.eventData.approvedAt = moment(this.eventData.approved_at).format('lll');
        }
        this.eventData.startTime = moment(
          this.deliveryRequestWithCraneEquipmentType[this.calendarCurrentDeliveryIndex]
            .craneDeliveryStart,
        ).format('hh:mm A');
        this.eventData.endTime = moment(this.eventData.craneDeliveryEnd).format('hh:mm A');
        this.getCraneRequest(arg);
        this.openIdModal(this.eventData);
      } else if (this.eventData.requestType === 'deliveryRequestWithCrane') {
        this.calendarCurrentDeliveryIndex = index;
        this.eventData.startDate = moment(
          this.deliveryRequestWithCraneEquipmentType[this.calendarCurrentDeliveryIndex]
            .deliveryStart,
        ).format('MM/DD/YYYY');
        if (this.eventData.approved_at !== null) {
          this.eventData.approvedAt = moment(this.eventData.approved_at).format('lll');
        }
        this.eventData.startTime = moment(
          this.deliveryRequestWithCraneEquipmentType[this.calendarCurrentDeliveryIndex]
            .deliveryStart,
        ).format('hh:mm A');
        this.eventData.endTime = moment(this.eventData.deliveryEnd).format('hh:mm A');
        this.getNDR(arg);
        this.openIdModal(this.eventData);
      } else {
        this.calendarDescription(arg);
      }
    }
  }

  public getCraneRequest(data: { event: { id: string | number } }): void {
    const param = {
      CraneRequestId: +data.event.id,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    this.deliveryService.getEquipmentCraneRequest(param).subscribe((res): void => {
      if (this.authUser.RoleId === 4 || this.authUser.RoleId === 3) {
        const newMember = res.data.memberDetails;
        const index = newMember.findIndex(
          (i: { Member: { id: any } }): boolean => i.Member.id === this.authUser.id,
        );
        if (index !== -1) {
          this.eventData.edit = true;
        } else {
          this.eventData.edit = false;
        }
      } else {
        this.eventData.edit = true;
      }
      this.eventData.gateDetails = res.data.gateDetails;
      this.toolTipContent = '';
      if (this.eventData.memberDetails.length > 3) {
        const slicedArray = this.eventData.memberDetails.slice(3) || [];
        slicedArray.map(
          (a: { Member: { User: { firstName: any; lastName: any; email: any } } }): any => {
            if (a.Member.User.firstName) {
              this.toolTipContent += `${a.Member.User.firstName} ${a.Member.User.lastName}, `;
            } else {
              this.toolTipContent += `${a.Member.User.email}, `;
            }
          },
        );
      }
    });
  }

  public getNDR(data: { event: { id: string | number } }): void {
    const index = this.deliveryRequestWithCraneEquipmentType.findIndex(
      (item): boolean => item.CraneRequestId === +data.event.id,
    );
    const clickedEventData = this.deliveryRequestWithCraneEquipmentType[index];
    const param = {
      DeliveryRequestId: clickedEventData.id,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.getNDRData(param).subscribe((res): void => {
      if (this.authUser.RoleId === 4 || this.authUser.RoleId === 3) {
        const newMember = res.data.memberDetails;
        const index1 = newMember.findIndex(
          (i: { Member: { id: any } }): boolean => i.Member.id === this.authUser.id,
        );
        if (index1 !== -1) {
          this.eventData.edit = true;
        } else {
          this.eventData.edit = false;
        }
      } else {
        this.eventData.edit = true;
      }
      this.eventData.gateDetails = res.data?.gateDetails;
      this.toolTipContent = '';
      if (this.eventData.memberDetails.length > 3) {
        const slicedArray = this.eventData?.memberDetails?.slice(3) || [];
        slicedArray.map(
          (a: { Member: { User: { firstName: any; lastName: any; email: any } } }): any => {
            if (a.Member.User.firstName) {
              this.toolTipContent += `${a.Member.User.firstName} ${a.Member.User.lastName}, `;
            } else {
              this.toolTipContent += `${a.Member.User.email}, `;
            }
          },
        );
      }
    });
  }

  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public openEditModal(item, action): void {
    this.closeDescription();
    if (this.modalRef) {
      this.close();
    }
    if (!item.isAssociatedWithDeliveryRequest && !item.isAssociatedWithCraneRequest) {
      this.deliveryService.updatedEditCraneRequestId(+item.CraneRequestId);
      const className = 'modal-lg new-delivery-popup custom-modal';
      this.modalRef = this.modalService.show(EditCraneRequestComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
      });
    } else {
      this.deliveryService.updatedDeliveryId(item.id);
      const className = 'modal-lg new-delivery-popup custom-modal';
      this.modalRef = this.modalService.show(EditDeliveryFormComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
      });
    }
    this.modalRef.content.closeBtnName = 'Close';
    this.modalRef.content.seriesOption = item?.recurrence && item?.recurrence?.recurrence !== 'Does Not Repeat' ? action : 1;
    this.modalRef.content.recurrenceId = item?.recurrence ? item?.recurrence?.id : null;
    this.modalRef.content.recurrenceEndDate = item?.recurrence
      ? item?.recurrence?.recurrenceEndDate
      : null;
  }

  public openAddNDRModal(): void {
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(NewCraneRequestCreationFormComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
    });
    this.modalRef.content.lastId = this.lastId;
    this.modalRef.content.closeBtnName = 'Close';
  }

  public openFilterModal(template: TemplateRef<any>): void {
    this.calendarGetOverAllGate();
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-sm filter-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }

  public calendarGetOverAllGate(): void {
    this.modalLoader = true;
    const calendarGetGateParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .gateList(calendarGetGateParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((calendarGetGateListResponse): void => {
        this.gateList = calendarGetGateListResponse.data;
        this.calendarGetOverAllEquipment();
      });
  }

  public calendarGetOverAllEquipment(): void {
    const calendarGetEquipmentParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listCraneEquipment(calendarGetEquipmentParams, {
        showActivatedAlone: true,
      })
      .subscribe((calendarGetEquipmentListParams): void => {
        this.equipmentList = calendarGetEquipmentListParams.data.rows;
        this.calendarGetCompany();
      });
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.search = '';
    this.filterDetailsForm();
    this.getDeliveryRequestWithCraneEquipmentType();
    this.modalRef.hide();
  }

  public calendarGetDefinable(): void {
    const calendarGetDefinableParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getDefinableWork(calendarGetDefinableParams)
      .subscribe((calendarGetDefinableListResponse: any): void => {
        if (calendarGetDefinableListResponse) {
          const { data } = calendarGetDefinableListResponse;
          this.defineList = data;
          this.definableDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'DFOW',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
          this.getLocations();
        }
      });
  }

  public getLocations(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getLocations(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.locationList = data;
        this.openContentModal();
      }
    });
  }

  public openContentModal(): void {
    this.modalLoader = false;
  }

  public calendarGetCompany(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getCompanies(params).subscribe((companyResponse: any): void => {
      if (companyResponse) {
        if (this.companyList.length === 0) {
          this.companyList = companyResponse.data;
        }
        this.calendarGetDefinable();
        this.dropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'companyName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
      }
    });
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('descriptionFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('dateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('companyFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('memberFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('gateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('equipmentFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('locationFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('statusFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('pickFrom').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('pickTo').value !== '') {
      this.filterCount += 1;
    }
    this.getDeliveryRequestWithCraneEquipmentType();
    this.modalRef.hide();
  }

  public ngAfterViewInit(): void {
    this.projectService.projectParent.subscribe((response): void => {
      if (response !== undefined && response !== null && response !== '') {
        this.ParentCompanyId = response.ParentCompanyId;
        this.ProjectId = response.ProjectId;
        this.getMembers();
        this.setCalendar();
      }
    });
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
        if (this.authUser.RoleId === 2) {
          this.statusValue = ['Approved', 'Declined'];
        } else if (this.authUser.RoleId === 3) {
          this.statusValue = ['Completed'];
        }
      }
    });
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }

  // eslint-disable-next-line max-lines-per-function
  public getDeliveryRequestWithCraneEquipmentType(): void {
    this.loader = true;
    this.deliveryRequestWithCraneEquipmentType = [];

    const { filterParams, payload } = this.buildPayloadAndFilterParams();

    this.calendarService
      .getDeliveryRequestWithCraneEquipmentType(filterParams, payload)
      .subscribe((response: any): void => {
        if (response) {
          this.parseDeliveryRequestResponse(response);
        }
        this.loader = false;
      });
  }

  public createEventObject(
    element: any,
    previewSelected: any[],
    craneEquipmentName: string,
    statusMap: any,
  ): any {
    let startDate;
    let endDate;
    let requestId;

    if (element.requestType === 'craneRequest') {
      startDate = element.craneDeliveryStart;
      endDate = element.craneDeliveryEnd;
      requestId = element.CraneRequestId;
    } else if (element.requestType === 'deliveryRequestWithCrane') {
      startDate = element.deliveryStart;
      endDate = element.deliveryEnd;
      requestId = element.CraneRequestId;
    } else {
      startDate = element.fromDate;
      endDate = element.toDate;
      requestId = element.id;
    }

    const assignData: any = {
      description: '',
      title: '',
      start: startDate,
      end: endDate,
      id: requestId,
      uniqueNumber: element.uniqueNumber,
      companyName: '',
      allDay: false,
      allDaySlot: false,
      line2: '',
    };

    if (element.requestType === 'calendarEvent') {
      assignData.description = element.description;
      assignData.title = element.description;
      if (element.isAllDay) {
        assignData.allDay = true;
        assignData.allDaySlot = true;
      }
    }

    if (['craneRequest', 'deliveryRequestWithCrane'].includes(element.requestType)) {
      previewSelected.forEach((item) => {
        const line = item.line === 1 ? 'description' : 'line2';
        switch (item.label) {
          case 'Description':
            assignData[line] = element.description;
            break;
          case 'Responsible Company':
            assignData[line] = element?.companyDetails?.[0]?.Company?.companyName || '';
            break;
          case 'Responsible Person':
            assignData[line] = `${element?.memberDetails?.[0]?.Member?.User?.firstName} ${element?.memberDetails?.[0]?.Member?.User?.lastName}`;
            break;
          case 'Crane Pick ID':
            assignData[line] = element.CraneRequestId;
            break;
          case 'Definable Feature Of Work':
            assignData[line] = element.defineWorkDetails?.[0]?.DeliverDefineWork?.DFOW || '';
            break;
          case 'Equipment':
            assignData[line] = craneEquipmentName;
            break;
          case 'Picking From':
            assignData[line] = element.requestType === 'craneRequest' ? element.pickUpLocation : element.cranePickUpLocation;
            break;
          case 'Picking To':
            assignData[line] = element.requestType === 'craneRequest' ? element.dropOffLocation : element.craneDropOffLocation;
            break;

          default:
            break;
        }
      });
    }

    const status = element.status?.toLowerCase();
    if (status && statusMap[status]) {
      assignData.color = statusMap[status].backgroundColor;
      assignData.textColor = statusMap[status].fontColor;
      assignData.borderColor = statusMap[status].fontColor;
    } else {
      assignData.className = 'calendar_event';
    }

    return assignData;
  }

  public parseDeliveryRequestResponse(response: any): void {
    const responseData = response.data;
    const statusData = JSON.parse(response.statusData.statusColorCode);
    const cardData = JSON.parse(response.cardData.craneCard);
    const useTextColor = JSON.parse(response.statusData.useTextColorAsLegend);
    const isDefaultColor = JSON.parse(response.statusData.isDefaultColor);

    const statusMap = {
      approved: statusData.find((s) => s.status === 'approved'),
      pending: statusData.find((s) => s.status === 'pending'),
      delivered: statusData.find((s) => s.status === 'delivered'),
      rejected: statusData.find((s) => s.status === 'rejected'),
      expired: statusData.find((s) => s.status === 'expired'),
    };

    this.approved = useTextColor
      ? statusMap.approved.fontColor
      : statusMap.approved.backgroundColor;
    this.pending = useTextColor ? statusMap.pending.fontColor : statusMap.pending.backgroundColor;
    this.expired = useTextColor ? statusMap.expired.fontColor : statusMap.expired.backgroundColor;
    this.rejected = useTextColor
      ? statusMap.rejected.fontColor
      : statusMap.rejected.backgroundColor;
    this.delivered = useTextColor
      ? statusMap.delivered.fontColor
      : statusMap.delivered.backgroundColor;

    if (isDefaultColor) {
      this.delivered = statusMap.delivered.backgroundColor;
    }

    this.lastId = response.lastId.CraneRequestId;
    this.deliveryRequestWithCraneEquipmentType = responseData;
    this.events = [];

    const previewSelected = cardData.filter((item) => item.selected);

    responseData.forEach((element) => {
      let craneEquipmentNameForCard = '';
      element.equipmentDetails?.some((detail) => {
        if (detail.Equipment?.PresetEquipmentType?.isCraneType !== undefined) {
          craneEquipmentNameForCard = detail.Equipment.equipmentName;
          return true;
        }
        return false;
      });

      const assignData = this.createEventObject(
        element,
        previewSelected,
        craneEquipmentNameForCard,
        statusMap,
      );
      this.events.push(assignData);
    });

    this.calendarApi.removeAllEventSources();
    this.calendarApi.addEventSource(this.events);
  }

  public buildPayloadAndFilterParams(): { filterParams: any; payload: any } {
    const filterParams = {
      ProjectId: this.ProjectId,
      void: 0,
    };

    const payload: any = {
      ParentCompanyId: this.ParentCompanyId,
      search: this.search,
      start: moment(this.Range?.start).format('YYYY-MM-DD'),
      end: moment(this.Range?.end).format('YYYY-MM-DD'),
      filterCount: this.filterCount,
      calendarView: this.currentView,
    };

    if (this.filterForm) {
      const formValue = this.filterForm.value;
      payload.companyFilter = +formValue.companyFilter;
      payload.descriptionFilter = formValue.descriptionFilter;
      payload.dateFilter = formValue.dateFilter
        ? moment(formValue.dateFilter).format('YYYY-MM-DD')
        : formValue.dateFilter;
      payload.statusFilter = formValue.statusFilter;
      payload.memberFilter = +formValue.memberFilter;
      payload.gateFilter = +formValue.gateFilter;
      payload.equipmentFilter = formValue.equipmentFilter === null || formValue.equipmentFilter === '' ? null : +formValue.equipmentFilter;
      payload.locationFilter = formValue.locationFilter;
      payload.pickFrom = formValue.pickFrom;
      payload.pickTo = formValue.pickTo;
    }

    return { filterParams, payload };
  }



  public moveToVoidList(): void {
    if (!this.voidSubmitted) {
      this.voidSubmitted = true;
      const calendarCurrentDeliveryItem = this.deliveryRequestWithCraneEquipmentType[this.calendarCurrentDeliveryIndex];
      if (
        !calendarCurrentDeliveryItem.isAssociatedWithDeliveryRequest
        && !calendarCurrentDeliveryItem.isAssociatedWithCraneRequest
      ) {
        const addToVoidPayload = {
          CraneRequestId: calendarCurrentDeliveryItem.id,
          ProjectId: this.ProjectId,
          ParentCompanyId: this.ParentCompanyId,
        };
        this.moveCraneRequestToVoid(addToVoidPayload);
      } else {
        const addToVoidPayload = {
          DeliveryRequestId: calendarCurrentDeliveryItem.id,
          ProjectId: this.ProjectId,
        };
        this.moveDeliveryRequestToVoid(addToVoidPayload);
      }
    }
  }

  public moveDeliveryRequestToVoid(addToVoidPayload: {
    DeliveryRequestId: any;
    ProjectId: any;
  }): void {
    this.deliveryService.createVoid(addToVoidPayload).subscribe({
      next: (createVoidResponse: any): void => {
        if (createVoidResponse) {
          this.toastr.success(createVoidResponse.message, 'Success');
          this.mixpanelService.addMixpanelEvents('Delivery Booking Voided');
          this.voidSubmitted = false;
          this.router.navigate(['/void-list']);
          this.closeDescription();
          this.close();
        }
      },
      error: (addToVoidError): void => {
        this.voidSubmitted = false;
        if (addToVoidError.message?.statusCode === 400) {
          this.showError(addToVoidError);
        } else if (!addToVoidError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(addToVoidError.message, 'OOPS!');
        }
      },
    });
  }

  public moveCraneRequestToVoid(data: {
    CraneRequestId: any;
    ProjectId: any;
    ParentCompanyId: any;
  }): void {
    this.deliveryService.addCraneRequestToVoid(data).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.router.navigate(['/void-list']);
          this.voidSubmitted = false;
          this.deliveryService.updateCraneRequestHistory({ status: true }, 'AddedToVoid');
          this.mixpanelService.addMixpanelEvents('Crane Booking Voided');
          if (this.bsModalRef) {
            this.bsModalRef.hide();
          }
        }
      },
      error: (createVoidError): void => {
        this.voidSubmitted = false;
        if (createVoidError.message?.statusCode === 400) {
          this.showError(createVoidError);
        } else if (!createVoidError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(createVoidError.message, 'OOPS!');
        }
      },
    });
  }

  public close(): void {
    this.submitted = false;
    this.formSubmitted = false;
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'filter':
          this.openFilterModal(data);
          break;
        case 'open':
          this.openIdModal(data);
          break;
        case 'close':
          this.closeCalendarDescription();
          break;
        case 'edit':
          this.openEditModal(data, item);
          break;
        case 'clear':
          this.clear();
          break;
        default:
          break;
      }
    }
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public voidConfirmationResponse(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.moveToVoidList();
    }
  }

  public openIdModal(item): void {
    const data = item;
    data.ParentCompanyId = this.ParentCompanyId;
    if (
      !this.eventData.isAssociatedWithDeliveryRequest
      && !this.eventData.isAssociatedWithCraneRequest
    ) {
      const newPayload = {
        id: item.CraneRequestId,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.deliveryService.updatedCurrentCraneRequestStatus(data.CraneRequestId);
      const initialState = {
        data: newPayload,
        title: 'Modal with component',
      };
      this.modalRef = this.modalService.show(CraneRequestDetailViewHeaderComponent, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal',
        initialState,
      });
    } else {
      const newPayload = {
        id: item.id,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.deliveryService.updatedCurrentStatus(data.id);
      const initialState = {
        data: newPayload,
        title: 'Modal with component',
      };
      this.modalRef = this.modalService.show(DeliveryDetailsNewComponent, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal',
        initialState,
      });
    }
    this.modalRef.content.closeBtnName = 'Close';
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      companyFilter: [''],
      descriptionFilter: [''],
      statusFilter: [''],
      memberFilter: [''],
      gateFilter: [''],
      equipmentFilter: [''],
      locationFilter: [''],
      dateFilter: [''],
      pickFrom: [''],
      pickTo: [''],
    });
  }

  public calendarDescription(arg: { event?: any }): void {
    this.calendarDescriptionPopup = false;
    this.descriptionPopup = false;
    this.viewEventData = '';
    if (Object.keys(arg).length !== 0) {
      const index = this.events.findIndex(
        (item: any): any => item.description === arg.event.title
          && item.uniqueNumber === arg.event.extendedProps.uniqueNumber,
      );
      this.viewEventData = this.deliveryRequestWithCraneEquipmentType[index];
      this.occurMessage(this.viewEventData);
      this.calendarDescriptionPopup = true;
    }
  }

  public occurMessage(data: {
    repeatEveryType: string;
    repeatEveryCount: string | number;
    days: any[];
    chosenDateOfMonth: any;
    dateOfMonth: any;
    monthlyRepeatType: any;
    endTime: moment.MomentInput;
  }): void {
    this.message = 'Occurs every day';
    if (data.repeatEveryType === 'Day') {
      this.message = '';
      this.message = 'Occurs every day';
    }
    if (data.repeatEveryType === 'Days') {
      this.message = '';
      if (+data.repeatEveryCount === 2) {
        this.message = 'Occurs every other day';
      } else {
        this.message = `Occurs every ${data.repeatEveryCount} days`;
      }
    }
    if (data.repeatEveryType === 'Week') {
      this.message = '';
      let weekDays = '';
      data.days.forEach((day1: any): any => {
        weekDays = `${weekDays + day1},`;
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message += `Occurs every ${weekDays}`;
    }
    if (data.repeatEveryType === 'Weeks') {
      let weekDays = '';
      data.days.forEach((day1: any): any => {
        weekDays = `${weekDays + day1},`;
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message = '';
      if (+data.repeatEveryCount === 2) {
        this.message = `Occurs every other  ${weekDays}`;
      } else {
        this.message = `Occurs every ${data.repeatEveryCount} weeks on ${weekDays}`;
      }
    }
    if (
      data.repeatEveryType === 'Month'
      || data.repeatEveryType === 'Months'
      || data.repeatEveryType === 'Year'
      || data.repeatEveryType === 'Years'
    ) {
      if (data.chosenDateOfMonth) {
        this.message = `Occurs on day ${data.dateOfMonth}`;
      } else {
        this.message = `Occurs on the ${data.monthlyRepeatType}`;
      }
    }
    this.message += ` until ${moment(data.endTime).format('MM-DD-YYYY')}`;
  }

  public closeCalendarDescription(): void {
    this.calendarDescriptionPopup = false;
    this.descriptionPopup = false;
    this.viewEventData = '';
  }

  public changeFormat(fromDate: any): any {
    if (fromDate) {
      const dayFormat = moment(new Date(fromDate)).format('ddd MM/DD/YYYY');
      return dayFormat;
    }
  }
}
