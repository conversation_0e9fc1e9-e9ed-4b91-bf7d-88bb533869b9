import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EquipmentsComponent } from './equipments.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { ProjectService } from '../services/profile/project.service';
import { AuthService } from '../services/auth/auth.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { MixpanelService } from '../services/mixpanel.service';
import { of, throwError, BehaviorSubject } from 'rxjs';

describe('EquipmentsComponent', () => {
  let component: EquipmentsComponent;
  let fixture: ComponentFixture<EquipmentsComponent>;
  let modalService: jest.Mocked<BsModalService>;
  let projectService: jest.Mocked<ProjectService>;
  let titleService: jest.Mocked<Title>;
  let mixpanelService: jest.Mocked<MixpanelService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let toastr: jest.Mocked<ToastrService>;
  let authService: jest.Mocked<AuthService>;
  let modalRef: jest.Mocked<BsModalRef>;

  beforeEach(async () => {
    const modalRefMock = {
      hide: jest.fn()
    } as unknown as jest.Mocked<BsModalRef>;

    const modalServiceMock = {
      show: jest.fn().mockReturnValue(modalRefMock)
    } as unknown as jest.Mocked<BsModalService>;

    const projectServiceMock = {
      getRegisteredMember: jest.fn(),
      listEquipment: jest.fn(),
      addEquipment: jest.fn(),
      updateEquipment: jest.fn(),
      deleteEquipment: jest.fn(),
      deactivateEquipment: jest.fn(),
      activateEquipment: jest.fn(),
      editEquipment: jest.fn(),
      deactiveEquipment: jest.fn(),
      getEquipmentMappedRequests: jest.fn(),
      listPresetEquipmentType: jest.fn().mockReturnValue(of({ data: [] })),
      projectId: new BehaviorSubject('123'),
      ParentCompanyId: new BehaviorSubject('456')
    } as unknown as jest.Mocked<ProjectService>;

    const titleServiceMock = {
      setTitle: jest.fn()
    } as unknown as jest.Mocked<Title>;

    const mixpanelServiceMock = {
      track: jest.fn(),
      addMixpanelEvents: jest.fn()
    } as unknown as jest.Mocked<MixpanelService>;

    const deliveryServiceMock = {
      loginUser: of({ id: 1, name: 'Test User' })
    } as unknown as jest.Mocked<DeliveryService>;

    const toastrMock = {
      success: jest.fn(),
      error: jest.fn()
    } as unknown as jest.Mocked<ToastrService>;

    const authServiceMock = {
      getUser: jest.fn()
    } as unknown as jest.Mocked<AuthService>;

    await TestBed.configureTestingModule({
      declarations: [EquipmentsComponent],
      imports: [
        ReactiveFormsModule,
        FormsModule
      ],
      providers: [
        { provide: BsModalService, useValue: modalServiceMock },
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: Title, useValue: titleServiceMock },
        { provide: MixpanelService, useValue: mixpanelServiceMock },
        { provide: DeliveryService, useValue: deliveryServiceMock },
        { provide: ToastrService, useValue: toastrMock },
        { provide: AuthService, useValue: authServiceMock },
        UntypedFormBuilder
      ]
    }).compileComponents();

    modalService = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    titleService = TestBed.inject(Title) as jest.Mocked<Title>;
    mixpanelService = TestBed.inject(MixpanelService) as jest.Mocked<MixpanelService>;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    toastr = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    authService = TestBed.inject(AuthService) as jest.Mocked<AuthService>;
    modalRef = modalService.show({} as any) as jest.Mocked<BsModalRef>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(EquipmentsComponent);
    component = fixture.componentInstance;
    
    // Setup default spy responses
    projectService.listEquipment.mockReturnValue(of({ data: [], totalCount: 0 }));
    projectService.getRegisteredMember.mockReturnValue(of({ data: [] }));
    
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.currentPageNo).toBe(1);
    expect(component.pageSize).toBe(25);
    expect(component.pageNo).toBe(1);
    expect(component.loader).toBeTruthy();
    expect(component.equipmentList).toEqual([]);
  });

  it('should set title on initialization', () => {
    expect(titleService.setTitle).toHaveBeenCalledWith('Follo - Equipment');
  });

  it('should get equipments when project ID is available', () => {
    expect(projectService.listEquipment).toHaveBeenCalled();
  });

  describe('Form Handling', () => {
    it('should initialize equipment form with required fields', () => {
      expect(component.equipmentDetailsForm).toBeTruthy();
      expect(component.equipmentDetailsForm.get('equipmentName')).toBeTruthy();
      expect(component.equipmentDetailsForm.get('equipmentType')).toBeTruthy();
    });

    it('should validate equipment form', () => {
      const form = component.equipmentDetailsForm;
      expect(form.valid).toBeFalsy();
      
      form.controls['equipmentName'].setValue('Test Equipment');
      form.controls['equipmentType'].setValue('Type1');
      form.controls['controlledBy'].setValue('User1');
      
      expect(form.valid).toBeTruthy();
    });
  });

  describe('Equipment Management', () => {
    it('should handle equipment search', () => {
      const searchTerm = 'test';
      component.getSearchEquipment(searchTerm);
      expect(component.search).toBe(searchTerm);
      expect(component.pageNo).toBe(1);
      expect(projectService.listEquipment).toHaveBeenCalled();
    });

    it('should handle sorting', () => {
      component.sortByField('name', 'ASC');
      expect(component.sortColumn).toBe('name');
      expect(component.sort).toBe('ASC');
      expect(projectService.listEquipment).toHaveBeenCalled();
    });

    it('should handle pagination', () => {
      component.changePageSize(50);
      expect(component.pageSize).toBe(50);
      expect(projectService.listEquipment).toHaveBeenCalled();

      component.pageNo = 2; // Set page number directly
      component.changePageNo(2);
      expect(component.pageNo).toBe(2);
      expect(projectService.listEquipment).toHaveBeenCalled();
    });
  });

  describe('Modal Operations', () => {
    it('should open modal', () => {
      const template = {} as any;
      component.openModal(template);
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should handle modal close', () => {
      const template = {} as any;
      component.modalRef = modalRef; // Set the modalRef
      component.close(template, 'newequipment');
      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
      expect(modalRef.hide).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors', () => {
      const error = { message: { details: [{ message: 'Error occurred' }] } };
      component.showError(error);
      expect(toastr.error).toHaveBeenCalled();
    });

    it('should handle equipment fetch error', () => {
      projectService.listEquipment.mockReturnValue(throwError(() => new Error('API Error')));
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.getEquipments();
      // Error handling is implicit in the component
    });
  });

  describe('Form Validation', () => {
    it('should validate alphanumeric input', () => {
      const event = { which: 65, keyCode: 65 }; // 'A' key
      expect(component.alphaNumericForEquipments(event)).toBeTruthy();

      const invalidEvent = { which: 33, keyCode: 33 }; // '!' key
      expect(component.alphaNumericForEquipments(invalidEvent)).toBeFalsy();
    });

    it('should validate backspace and space keys', () => {
      const backspaceEvent = { which: 8, keyCode: 8 }; // Backspace
      expect(component.alphaNumericForEquipments(backspaceEvent)).toBeTruthy();

      const spaceEvent = { which: 32, keyCode: 32 }; // Space
      expect(component.alphaNumericForEquipments(spaceEvent)).toBeTruthy();
    });

    it('should validate numeric input', () => {
      const numericEvent = { which: 50, keyCode: 50 }; // '2' key
      expect(component.alphaNumericForEquipments(numericEvent)).toBeTruthy();
    });

    it('should check for empty string values', () => {
      const formValue = { equipmentName: '   ' };
      expect(component.checkStringEmptyValues(formValue)).toBeTruthy();

      const validFormValue = { equipmentName: 'Test Equipment' };
      expect(component.checkStringEmptyValues(validFormValue)).toBeFalsy();
    });
  });

  describe('Initialization Methods', () => {
    it('should initialize preset equipment types', () => {
      const mockResponse = { data: [{ id: 1, equipmentType: 'Crane' }] };
      projectService.listPresetEquipmentType.mockReturnValue(of(mockResponse));

      component.listPresetEquipmentType();

      expect(projectService.listPresetEquipmentType).toHaveBeenCalled();
      expect(component.presetEquipmentTypeList).toEqual(mockResponse.data);
      expect(component.equipmentTypeList).toEqual(mockResponse.data);
    });

    it('should initialize equipment form', () => {
      component.equipmentForm();

      expect(component.equipmentDetailsForm).toBeTruthy();
      expect(component.equipmentDetailsForm.get('equipmentName')).toBeTruthy();
      expect(component.equipmentDetailsForm.get('equipmentType')).toBeTruthy();
      expect(component.equipmentDetailsForm.get('controlledBy')).toBeTruthy();
    });

    it('should initialize edit equipment form', () => {
      component.editEquipmentForm();

      expect(component.equipmentEditForm).toBeTruthy();
      expect(component.equipmentEditForm.get('equipmentName')).toBeTruthy();
      expect(component.equipmentEditForm.get('equipmentType')).toBeTruthy();
      expect(component.equipmentEditForm.get('controlledBy')).toBeTruthy();
      expect(component.equipmentEditForm.get('id')).toBeTruthy();
      expect(component.equipmentEditForm.get('equipmentAutoId')).toBeTruthy();
    });

    it('should initialize filter form', () => {
      component.filterDetailsForm();

      expect(component.filterForm).toBeTruthy();
      expect(component.filterForm.get('idFilter')).toBeTruthy();
      expect(component.filterForm.get('nameFilter')).toBeTruthy();
      expect(component.filterForm.get('companyNameFilter')).toBeTruthy();
      expect(component.filterForm.get('typeFilter')).toBeTruthy();
      expect(component.filterForm.get('memberFilter')).toBeTruthy();
    });
  });

  describe('Equipment CRUD Operations', () => {
    beforeEach(() => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.equipmentDetailsForm.patchValue({
        equipmentName: 'Test Equipment',
        equipmentType: '1',
        controlledBy: 'Test User'
      });
    });

    describe('Add Equipment', () => {
      it('should add equipment successfully', () => {
        const mockResponse = { message: 'Equipment added successfully' };
        projectService.addEquipment.mockReturnValue(of(mockResponse));

        component.onSubmit();

        expect(component.submitted).toBeTruthy();
        expect(component.formSubmitted).toBeTruthy();
        expect(projectService.addEquipment).toHaveBeenCalledWith({
          equipmentName: 'Test Equipment',
          PresetEquipmentTypeId: 1,
          controlledBy: 'Test User',
          ProjectId: '123',
          ParentCompanyId: '456'
        });
        expect(toastr.success).toHaveBeenCalledWith('Equipment added successfully', 'Success');
        expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Added Equipment');
      });

      it('should handle add equipment form validation errors', () => {
        component.equipmentDetailsForm.patchValue({
          equipmentName: '',
          equipmentType: '',
          controlledBy: ''
        });

        component.onSubmit();

        expect(component.submitted).toBeTruthy();
        expect(component.formSubmitted).toBeFalsy();
        expect(projectService.addEquipment).not.toHaveBeenCalled();
      });

      it('should handle add equipment with empty string values', () => {
        component.equipmentDetailsForm.patchValue({
          equipmentName: '   ',
          equipmentType: '1',
          controlledBy: 'Test User'
        });

        component.onSubmit();

        expect(toastr.error).toHaveBeenCalledWith('Please Enter valid Equipment Name/Type.', 'OOPS!');
        expect(component.submitted).toBeFalsy();
        expect(component.formSubmitted).toBeFalsy();
      });

      it('should handle add equipment API error with status 400', () => {
        const mockError = {
          message: {
            statusCode: 400,
            details: [{ message: 'Equipment already exists' }]
          }
        };
        projectService.addEquipment.mockReturnValue(throwError(() => mockError));

        component.onSubmit();

        expect(component.submitted).toBeFalsy();
        expect(component.formSubmitted).toBeFalsy();
        expect(toastr.error).toHaveBeenCalled();
      });

      it('should handle add equipment API error without message', () => {
        const mockError = {};
        projectService.addEquipment.mockReturnValue(throwError(() => mockError));

        component.onSubmit();

        expect(toastr.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
      });

      it('should handle add equipment API error with general message', () => {
        const mockError = { message: 'General error' };
        projectService.addEquipment.mockReturnValue(throwError(() => mockError));

        component.onSubmit();

        expect(toastr.error).toHaveBeenCalledWith('General error', 'OOPS!');
      });
    });

    describe('Edit Equipment', () => {
      beforeEach(() => {
        component.equipmentEditForm.patchValue({
          id: '1',
          equipmentName: 'Updated Equipment',
          equipmentType: '2',
          controlledBy: 'Updated User'
        });
      });

      it('should edit equipment successfully', () => {
        const mockResponse = { message: 'Equipment updated successfully' };
        projectService.editEquipment.mockReturnValue(of(mockResponse));

        component.onEditSubmit();

        expect(component.editSubmitted).toBeTruthy();
        expect(component.formEditSubmitted).toBeTruthy();
        expect(projectService.editEquipment).toHaveBeenCalledWith({
          equipmentName: 'Updated Equipment',
          PresetEquipmentTypeId: 2,
          controlledBy: 'Updated User',
          id: '1',
          ProjectId: '123',
          ParentCompanyId: '456'
        });
        expect(toastr.success).toHaveBeenCalledWith('Equipment updated successfully', 'Success');
        expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Edited  Equipment');
      });

      it('should handle edit equipment form validation errors', () => {
        component.equipmentEditForm.patchValue({
          equipmentName: '',
          equipmentType: '',
          controlledBy: ''
        });

        component.onEditSubmit();

        expect(component.editSubmitted).toBeTruthy();
        expect(component.formEditSubmitted).toBeFalsy();
        expect(projectService.editEquipment).not.toHaveBeenCalled();
      });

      it('should handle edit equipment with empty string values', () => {
        component.equipmentEditForm.patchValue({
          equipmentName: '   ',
          equipmentType: '1',
          controlledBy: 'Test User'
        });

        component.onEditSubmit();

        expect(toastr.error).toHaveBeenCalledWith('Please Enter valid Equipment Name/Type.', 'OOPS!');
        expect(component.submitted).toBeFalsy();
        expect(component.formEditSubmitted).toBeFalsy();
      });

      it('should handle edit equipment API error', () => {
        const mockError = {
          message: {
            statusCode: 400,
            details: [{ message: 'Update failed' }]
          }
        };
        projectService.editEquipment.mockReturnValue(throwError(() => mockError));

        component.onEditSubmit();

        expect(component.submitted).toBeFalsy();
        expect(component.formEditSubmitted).toBeFalsy();
      });
    });

    describe('Delete Equipment', () => {
      beforeEach(() => {
        component.ProjectId = '123';
        component.ParentCompanyId = '456';
        component.deleteIndex = [1, 2];
      });

      it('should delete equipment successfully', () => {
        const mockResponse = { message: 'Equipment deleted successfully' };
        projectService.deleteEquipment.mockReturnValue(of(mockResponse));

        component.deleteEquipment();

        expect(component.deleteSubmitted).toBeTruthy();
        expect(projectService.deleteEquipment).toHaveBeenCalledWith({
          id: [1, 2],
          ProjectId: '123',
          isSelectAll: false,
          ParentCompanyId: '456'
        });
        expect(toastr.success).toHaveBeenCalledWith('Equipment deleted successfully', 'Success');
        expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Deleted  Equipment');
      });

      it('should handle delete equipment API error', () => {
        const mockError = {
          message: {
            statusCode: 400,
            details: [{ message: 'Delete failed' }]
          }
        };
        projectService.deleteEquipment.mockReturnValue(throwError(() => mockError));

        component.deleteEquipment();

        expect(component.deleteSubmitted).toBeFalsy();
      });

      it('should remove selected items', () => {
        component.equipmentList = [
          { id: 1, isChecked: true },
          { id: 2, isChecked: false },
          { id: 3, isChecked: true }
        ];
        component.selectAll = false;
        const mockResponse = { message: 'Equipment deleted successfully' };
        projectService.deleteEquipment.mockReturnValue(of(mockResponse));

        component.removeItem();

        expect(component.deleteSubmitted).toBeTruthy();
        expect(component.deleteIndex).toContain(1);
        expect(component.deleteIndex).toContain(3);
        expect(component.deleteIndex).not.toContain(2);
      });

      it('should remove all items when selectAll is true', () => {
        component.selectAll = true;
        const mockResponse = { message: 'All equipment deleted successfully' };
        projectService.deleteEquipment.mockReturnValue(of(mockResponse));

        component.removeItem();

        expect(component.deleteSubmitted).toBeTruthy();
      });
    });
  });

  describe('Modal Operations Extended', () => {
    beforeEach(() => {
      component.equipmentList = [
        {
          id: 1,
          equipmentAutoId: 'EQ001',
          equipmentName: 'Test Equipment',
          PresetEquipmentType: { id: 1 },
          controlledBy: 'Test User'
        }
      ];
    });

    it('should open edit modal with equipment data', () => {
      const template = {} as any;

      component.openEditModal(0, template);

      expect(component.editIndex).toBe(0);
      expect(component.equipmentEditForm.get('id').value).toBe(1);
      expect(component.equipmentEditForm.get('equipmentAutoId').value).toBe('EQ001');
      expect(component.equipmentEditForm.get('equipmentName').value).toBe('Test Equipment');
      expect(component.equipmentEditForm.get('equipmentType').value).toBe(1);
      expect(component.equipmentEditForm.get('controlledBy').value).toBe('Test User');
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should open delete modal for single item', () => {
      const template = {} as any;

      component.openDeleteModal(0, template);

      expect(component.deleteIndex[0]).toBe(1);
      expect(component.currentDeleteId).toBe(0);
      expect(component.remove).toBeFalsy();
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should open delete modal for multiple items', () => {
      const template = {} as any;

      component.openDeleteModal(-1, template);

      expect(component.remove).toBeTruthy();
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should open filter modal', () => {
      const template = {} as any;
      projectService.getRegisteredMember.mockReturnValue(of({ data: [] }));

      component.openFilterModal(template);

      expect(modalService.show).toHaveBeenCalled();
      expect(projectService.getRegisteredMember).toHaveBeenCalled();
    });

    it('should close modal popup', () => {
      const template = {} as any;

      component.closeModalPopup(template);

      expect(modalService.show).toHaveBeenCalled();
    });

    it('should reset and close modal', () => {
      component.modalRef = modalRef;
      component.submitted = true;
      component.formSubmitted = true;
      component.editSubmitted = true;

      component.resetAndClose();

      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
      expect(component.editSubmitted).toBeFalsy();
      expect(modalRef.hide).toHaveBeenCalled();
    });

    it('should handle close with dirty form', () => {
      const template = {} as any;
      component.equipmentDetailsForm.markAsDirty();

      component.close(template, 'newequipment');

      expect(modalService.show).toHaveBeenCalled();
    });

    it('should handle close with clean form', () => {
      const template = {} as any;
      component.modalRef = modalRef;

      component.close(template, 'newequipment');

      expect(modalRef.hide).toHaveBeenCalled();
    });

    it('should handle close for deactivate equipment', () => {
      const template = {} as any;
      component.modalRef = modalRef;
      projectService.listEquipment.mockReturnValue(of({ data: [], totalCount: 0 }));

      component.close(template, 'deactivateEquipment');

      expect(modalRef.hide).toHaveBeenCalled();
      expect(projectService.listEquipment).toHaveBeenCalled();
    });
  });

  describe('Selection Functionality', () => {
    beforeEach(() => {
      component.equipmentList = [
        { id: 1, isChecked: false },
        { id: 2, isChecked: false },
        { id: 3, isChecked: false }
      ];
    });

    it('should select all equipments', () => {
      component.selectAll = false;

      component.selectAllEquipmentsData();

      expect(component.selectAll).toBeTruthy();
      expect(component.equipmentList[0].isChecked).toBeTruthy();
      expect(component.equipmentList[1].isChecked).toBeTruthy();
      expect(component.equipmentList[2].isChecked).toBeTruthy();
    });

    it('should deselect all equipments', () => {
      component.selectAll = true;
      component.equipmentList.forEach(item => item.isChecked = true);

      component.selectAllEquipmentsData();

      expect(component.selectAll).toBeFalsy();
      expect(component.equipmentList[0].isChecked).toBeFalsy();
      expect(component.equipmentList[1].isChecked).toBeFalsy();
      expect(component.equipmentList[2].isChecked).toBeFalsy();
    });

    it('should toggle individual item selection', () => {
      component.setSelectedItem(0);

      expect(component.equipmentList[0].isChecked).toBeTruthy();

      component.setSelectedItem(0);

      expect(component.equipmentList[0].isChecked).toBeFalsy();
    });

    it('should check if any row is selected - none selected', () => {
      component.selectAll = false;

      const result = component.checkSelectedRow();

      expect(result).toBeTruthy();
    });

    it('should check if any row is selected - some selected', () => {
      component.selectAll = false;
      component.equipmentList[0].isChecked = true;

      const result = component.checkSelectedRow();

      expect(result).toBeFalsy();
    });

    it('should check if any row is selected - select all enabled', () => {
      component.selectAll = true;

      const result = component.checkSelectedRow();

      expect(result).toBeFalsy();
    });
  });

  describe('Filter Operations', () => {
    beforeEach(() => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.modalRef = modalRef;
      projectService.listEquipment.mockReturnValue(of({ data: [], totalCount: 0 }));
    });

    it('should submit filter with all fields', () => {
      component.filterForm.patchValue({
        idFilter: '1',
        nameFilter: 'Test',
        companyNameFilter: 'Company',
        typeFilter: 'Crane',
        memberFilter: '2'
      });

      component.filterSubmit();

      expect(component.filterCount).toBe(5);
      expect(component.pageNo).toBe(1);
      expect(projectService.listEquipment).toHaveBeenCalled();
      expect(modalRef.hide).toHaveBeenCalled();
    });

    it('should submit filter with partial fields', () => {
      component.filterForm.patchValue({
        idFilter: '',
        nameFilter: 'Test',
        companyNameFilter: '',
        typeFilter: 'Crane',
        memberFilter: ''
      });

      component.filterSubmit();

      expect(component.filterCount).toBe(2);
    });

    it('should reset filter', () => {
      component.filterCount = 5;
      component.search = 'test search';
      component.pageNo = 3;

      component.resetFilter();

      expect(component.filterCount).toBe(0);
      expect(component.search).toBe('');
      expect(component.pageNo).toBe(1);
      expect(modalRef.hide).toHaveBeenCalled();
      expect(projectService.listEquipment).toHaveBeenCalled();
    });

    it('should reset form with action no', () => {
      component.modalRef1 = modalRef;

      component.resetForm('no');

      expect(modalRef.hide).toHaveBeenCalled();
    });

    it('should reset form with action yes', () => {
      component.modalRef = modalRef;
      component.submitted = true;
      component.formSubmitted = true;
      component.editSubmitted = true;
      component.formEditSubmitted = true;

      component.resetForm('yes');

      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
      expect(component.editSubmitted).toBeFalsy();
      expect(component.formEditSubmitted).toBeFalsy();
      expect(modalRef.hide).toHaveBeenCalled();
    });
  });

  describe('Keyboard Event Handlers', () => {
    beforeEach(() => {
      projectService.listEquipment.mockReturnValue(of({ data: [], totalCount: 0 }));
    });

    it('should handle toggle keydown for sorting', () => {
      const event = { key: 'Enter', preventDefault: jest.fn() } as any;

      component.handleToggleKeydown(event, 'name', 'ASC');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.sortColumn).toBe('name');
      expect(component.sort).toBe('ASC');
      expect(projectService.listEquipment).toHaveBeenCalled();
    });

    it('should handle toggle keydown with space key', () => {
      const event = { key: ' ', preventDefault: jest.fn() } as any;

      component.handleToggleKeydown(event, 'id', 'DESC');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.sortColumn).toBe('id');
      expect(component.sort).toBe('DESC');
    });

    it('should handle down keydown for delete action', () => {
      const event = { key: 'Enter', preventDefault: jest.fn() } as any;
      const template = {} as any;

      component.handleDownKeydown(event, 0, template, 'delete');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should handle down keydown for edit action', () => {
      const event = { key: 'Enter', preventDefault: jest.fn() } as any;
      const template = {} as any;
      component.equipmentList = [{ id: 1, equipmentAutoId: 'EQ001', equipmentName: 'Test', PresetEquipmentType: { id: 1 }, controlledBy: 'User' }];

      component.handleDownKeydown(event, 0, template, 'edit');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should handle down keydown for clear action', () => {
      const event = { key: 'Enter', preventDefault: jest.fn() } as any;
      component.showSearchbar = true;
      component.search = 'test';

      component.handleDownKeydown(event, null, null, 'clear');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.showSearchbar).toBeFalsy();
      expect(component.search).toBe('');
    });

    it('should handle down keydown for filter action', () => {
      const event = { key: 'Enter', preventDefault: jest.fn() } as any;
      const template = {} as any;
      projectService.getRegisteredMember.mockReturnValue(of({ data: [] }));

      component.handleDownKeydown(event, template, null, 'filter');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should handle down keydown for unknown action', () => {
      const event = { key: 'Enter', preventDefault: jest.fn() } as any;

      component.handleDownKeydown(event, null, null, 'unknown');

      expect(event.preventDefault).toHaveBeenCalled();
    });

    it('should not handle keydown for other keys', () => {
      const event = { key: 'Tab', preventDefault: jest.fn() } as any;

      component.handleToggleKeydown(event, 'name', 'ASC');

      expect(event.preventDefault).not.toHaveBeenCalled();
    });
  });

  describe('Equipment Activation/Deactivation', () => {
    beforeEach(() => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
    });

    it('should open deactivate modal and fetch mapped requests', () => {
      const template = {} as any;
      const equipmentData = { id: 1, equipmentAutoId: 'EQ001', equipmentName: 'Test Equipment' };
      const mockResponse = {
        data: {
          mappedRequest: [{ id: 1, name: 'Request 1' }],
          equipments: {
            craneEquipments: [{ id: 1, name: 'Crane 1' }],
            nonCraneEquipments: [{ id: 2, name: 'Equipment 2' }]
          }
        }
      };
      projectService.getEquipmentMappedRequests.mockReturnValue(of(mockResponse));

      component.openDeactivateModal(template, equipmentData, false);

      expect(component.equipmentData).toBe(equipmentData);
      expect(modalService.show).toHaveBeenCalled();
      expect(component.getMappedRequestLoader).toBeTruthy();
      expect(projectService.getEquipmentMappedRequests).toHaveBeenCalledWith({
        ProjectId: '123',
        id: 'EQ001',
        equipmentName: 'Test Equipment'
      });
      expect(component.mappedRequestList).toEqual(mockResponse.data.mappedRequest);
      expect(component.craneEquipmentDropdownList).toEqual(mockResponse.data.equipments.craneEquipments);
      expect(component.nonCraneEquipmentDropdownList).toEqual(mockResponse.data.equipments.nonCraneEquipments);
      expect(component.getMappedRequestLoader).toBeFalsy();
    });

    it('should activate equipment when event is true', () => {
      const equipmentData = { id: 1, equipmentName: 'Test Equipment' };
      const mockResponse = { message: 'Equipment activated successfully' };
      projectService.editEquipment.mockReturnValue(of(mockResponse));

      component.openDeactivateModal(null, equipmentData, true);

      expect(projectService.editEquipment).toHaveBeenCalledWith({
        id: 1,
        equipmentName: 'Test Equipment',
        ProjectId: '123',
        ParentCompanyId: '456',
        isActive: true
      });
      expect(toastr.success).toHaveBeenCalledWith('Equipment activated successfully', 'Success');
    });

    it('should handle activate equipment API error', () => {
      const equipmentData = { id: 1, equipmentName: 'Test Equipment' };
      const mockError = { message: { statusCode: 400, details: [{ message: 'Activation failed' }] } };
      projectService.editEquipment.mockReturnValue(throwError(() => mockError));

      component.openDeactivateModal(null, equipmentData, true);

      expect(toastr.error).toHaveBeenCalled();
    });

    it('should switch equipment assignment', () => {
      const requestData: any = {
        equipmentDetails: [{ Equipment: { id: 1 } }]
      };

      component.switchEquipment(requestData, 2);

      expect(requestData.changedEquipmentId).toBe(2);
    });

    it('should not switch equipment when same equipment selected', () => {
      const requestData: any = {
        equipmentDetails: [{ Equipment: { id: 1 } }]
      };

      component.switchEquipment(requestData, 1);

      expect(requestData.changedEquipmentId).toBeNull();
    });

    describe('Deactivate Equipment', () => {
      beforeEach(() => {
        component.equipmentData = { id: 1 };
        component.modalRef = modalRef;
        projectService.listEquipment.mockReturnValue(of({ data: [], totalCount: 0 }));
      });

      it('should deactivate equipment with switched requests', () => {
        component.mappedRequestList = [
          { id: 1, changedEquipmentId: 2 },
          { id: 2, changedEquipmentId: 3 }
        ];
        const mockResponse = { message: 'Equipment deactivated successfully' };
        projectService.deactiveEquipment.mockReturnValue(of(mockResponse));

        component.deactiveEquipment();

        expect(component.deactivateEquipmentLoader).toBeTruthy();
        expect(projectService.deactiveEquipment).toHaveBeenCalledWith({
          id: 1,
          equipmentSwitchedRequests: component.mappedRequestList,
          ProjectId: '123',
          ParentCompanyId: '456'
        });
        expect(toastr.success).toHaveBeenCalledWith('Equipment deactivated successfully', 'Success');
        expect(component.deactivateEquipmentLoader).toBeFalsy();
        expect(modalRef.hide).toHaveBeenCalled();
      });

      it('should deactivate equipment with no mapped requests', () => {
        component.mappedRequestList = [];
        const mockResponse = { message: 'Equipment deactivated successfully' };
        projectService.deactiveEquipment.mockReturnValue(of(mockResponse));

        component.deactiveEquipment();

        expect(projectService.deactiveEquipment).toHaveBeenCalledWith({
          id: 1,
          ProjectId: '123',
          ParentCompanyId: '456'
        });
        expect(toastr.success).toHaveBeenCalledWith('Equipment deactivated successfully', 'Success');
        expect(component.deactivateEquipmentLoader).toBeFalsy();
      });

      it('should handle deactivate equipment with partial switched requests', () => {
        component.mappedRequestList = [
          { id: 1, changedEquipmentId: 2 },
          { id: 2, changedEquipmentId: null }
        ];

        component.deactiveEquipment();

        expect(component.deactivateEquipmentLoader).toBeFalsy();
      });

      it('should handle deactivate equipment API error with status 400', () => {
        component.mappedRequestList = [];
        const mockError = { message: { statusCode: 400, details: [{ message: 'Deactivation failed' }] } };
        projectService.deactiveEquipment.mockReturnValue(throwError(() => mockError));

        component.deactiveEquipment();

        expect(toastr.error).toHaveBeenCalled();
      });

      it('should handle deactivate equipment API error without message', () => {
        component.mappedRequestList = [];
        const mockError = {};
        projectService.deactiveEquipment.mockReturnValue(throwError(() => mockError));

        component.deactiveEquipment();

        expect(toastr.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
      });

      it('should handle deactivate equipment API error with general message', () => {
        component.mappedRequestList = [];
        const mockError = { message: 'General deactivation error' };
        projectService.deactiveEquipment.mockReturnValue(throwError(() => mockError));

        component.deactiveEquipment();

        expect(toastr.error).toHaveBeenCalledWith('General deactivation error', 'OOPS!');
      });
    });
  });

  describe('Additional Edge Cases', () => {
    it('should handle getMembers API call', () => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      const mockResponse = { data: [{ id: 1, name: 'Member 1' }] };
      projectService.getRegisteredMember.mockReturnValue(of(mockResponse));

      component.getMembers();

      expect(component.modalLoader).toBeTruthy();
      expect(projectService.getRegisteredMember).toHaveBeenCalledWith({
        ProjectId: '123',
        pageSize: 25,
        pageNo: 1,
        ParentCompanyId: '456'
      });
      expect(component.memberList).toEqual(mockResponse.data);
      expect(component.modalLoader).toBeFalsy();
    });

    it('should handle getEquipments with selectAll enabled', () => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.selectAll = true;
      const mockResponse = {
        data: {
          rows: [{ id: 1, isChecked: false }, { id: 2, isChecked: false }],
          count: 2
        },
        lastId: 10
      };
      projectService.listEquipment.mockReturnValue(of(mockResponse));

      component.getEquipments();

      expect(component.loader).toBeTruthy();
      expect(component.equipmentList[0].isChecked).toBeTruthy();
      expect(component.equipmentList[1].isChecked).toBeTruthy();
      expect(component.totalCount).toBe(2);
      expect(component.lastId).toBe(10);
      expect(component.loader).toBeFalsy();
    });

    it('should handle getEquipments without project IDs', () => {
      component.ProjectId = null;
      component.ParentCompanyId = null;

      component.getEquipments();

      expect(projectService.listEquipment).not.toHaveBeenCalled();
    });

    it('should handle close with dirty edit form', () => {
      const template = {} as any;
      component.equipmentEditForm.markAsDirty();

      component.close(template, 'edit');

      expect(modalService.show).toHaveBeenCalled();
    });

    it('should handle resetForm with modalRef1 undefined', () => {
      component.modalRef1 = undefined;
      component.modalRef = modalRef;

      component.resetForm('yes');

      expect(modalRef.hide).toHaveBeenCalled();
    });
  });
});
