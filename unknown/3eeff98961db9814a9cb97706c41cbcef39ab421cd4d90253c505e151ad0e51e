/* eslint-disable max-len */
/* eslint-disable @typescript-eslint/camelcase */
/* eslint-disable max-lines-per-function */
import {
  Component, OnInit, TemplateRef, AfterViewInit, Input, ElementRef, ViewChild,
} from '@angular/core';
import {
  FormBuilder, FormGroup, Validators, FormArray, FormControl,
} from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Observable } from 'rxjs';
import moment from 'moment';
import { Router } from '@angular/router';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { Socket } from 'ngx-socket-io';
import { TimeslotComponent } from 'src/app/layout/time-slot/time-slot.component';
import { BookingTemplatesService } from '../../../services/booking-templates/booking-templates.service';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';
import { MixpanelService } from '../../../services/mixpanel.service';

import {
  weekDays, recurrence, repeatWithSingleRecurrence, repeatWithMultipleRecurrence, inspectionType,
} from '../../../services/common';

@Component({
  selector: 'app-new-inspection-form',
  templateUrl: './new-inspection-form.component.html',
})
export class NewinspectionFormComponent implements OnInit, AfterViewInit {
  @Input() data: any;

  @Input() title: string;

  @Input() currentPage: string;

  @Input() selectfunction: (type: any) => void;

  @Input() selectedParams: string;

  @Input() selectedEventDate: any;

  @ViewChild('timeSlotsContainer') timeSlotsContainer: ElementRef;

  @ViewChild('timeSlotRef') timeSlotComponent: TimeslotComponent;


  public bookingTypesList: any = ['New Delivery Booking', 'New Crane Booking', 'New Concrete Booking', 'New Inspection Booking'];

  public selectedType = 'New Inspection Booking';

  public ProjectId: any;

  public submitted = false;

  public escort = false;

  public formSubmitted = false;

  public editSubmitted = false;

  public modalLoader = false;

  public deliverDetailsForm: FormGroup;

  public durationisOpen = false;

  public authUser: any = {};

  public loader = false;

  public timeZone: any;

  public gateList: any = [];

  public equipmentList: any = [];

  public defineList: any = [];

  public locationList: any = [];

  public weekDates: any = [];

  hours = Array.from({ length: 24 }, (_, i) => i);

  public selectedHour: number | null = null;

  public selectedMinute: number | null = 30;

  public minutes: number[] = [15, 30, 45, 60]; // Common minute intervals

  public selectedMinutes: any;

  public availableTimes = [];

  public isSlotsNull = false;

  public selectedTime: string | null = null;

  public isAM = true;

  public selectedDate;

  public newNdrCompanyDropdownSettings: IDropdownSettings;

  public companyList: any = [];

  public lastId: any = {};

  public ParentCompanyId: any;

  public newNdrDefinableDropdownSettings: IDropdownSettings;

  public inspectionEnd: Date;

  public inspectionStart: Date;

  public todayDate = new Date();

  public NDRTimingChanged = false;

  public craneEquipmentTypeChosen = false;

  public craneRequestLastId = null;

  public selectedRecurrence = 'Does Not Repeat';

  public recurrence = recurrence;

  public inspectionType = inspectionType;

  public repeatWithSingleRecurrence = repeatWithSingleRecurrence;

  public repeatWithMultipleRecurrence = repeatWithMultipleRecurrence;

  public weekDays: any = weekDays;

  public isRepeatWithMultipleRecurrence = false;

  public isRepeatWithSingleRecurrence = false;

  public showRecurrenceTypeDropdown = false;

  public checkform: any = new FormArray([]);

  public message = '';

  public monthlyDate = '';

  public monthlyDayOfWeek = '';

  public monthlyLastDayOfWeek = '';

  public enableOption = false;

  public valueExists = [];

  public endTime: Date;

  public startTime: Date;

  public selectedValue: string;

  public selectedTimeZoneValue: any;

  public selectedLocationId: any;

  public dropdownSettings: IDropdownSettings;

  public locationDropdownSettings: IDropdownSettings;

  public timeZoneValues: any;

  public timezoneList: [];

  public getSelectedTimeZone: never[];

  public recurrenceMinDate = new Date();

  public newRequestTypeDropdownSettings: IDropdownSettings;

  public equipmentDropdownSettings: IDropdownSettings;

  public templateList: any = [];

  public selectedTemplate: any;

  public templateDropdownSettings: any;

  public disableSaveAsTemplate = false;

  public memberList: any;

  public templateName: any = '';

  public isBookingTemplate: any = false;

  endPickerTime: string;

  public noEquipmentOption = { id: 0, equipmentName: 'No Equipment Needed' };

  public durationToggleDropdown() {
    this.durationisOpen = !this.durationisOpen;
  }

  public durationCloseDropdown() {
    this.durationisOpen = false;
  }

  public selectHour(hour: number) {
    this.selectedHour = hour;
    const totalMinutes = (this.selectedHour * 60) + this.selectedMinute;
    const inspectionStartValue = this.deliverDetailsForm.get('inspectionStart')?.value;
    this.inspectionStart = new Date(inspectionStartValue);
    this.inspectionEnd = new Date(inspectionStartValue); // Start with the same date
    this.inspectionEnd.setMinutes(this.inspectionStart.getMinutes() + totalMinutes);

    this.deliverDetailsForm.get('inspectionEnd')?.setValue(this.inspectionEnd);
    this.endPickerTime = moment(this.inspectionEnd).format('HH:mm');
    this.durationCloseDropdown();
    this.updateDropdownState();
    this.selectDuration(hour);
  }

  getBookingData() {
    const equipmentId = this.deliverDetailsForm.get('EquipmentId').value;
    const locationId = this.deliverDetailsForm.get('LocationId').value[0].id;
    const gateId = this.deliverDetailsForm.get('GateId').value;

    if (this.timeSlotComponent) {
      this.timeSlotComponent.getEventNDR(equipmentId, locationId, gateId, this.timeZone, '');
    }
  }



  public selectMinute(minute: number) {
    this.selectedMinute = minute;
    const totalMinutes = (this.selectedHour * 60) + this.selectedMinute;
    const inspectionStartValue = this.deliverDetailsForm.get('inspectionStart')?.value;
    this.inspectionStart = new Date(inspectionStartValue);
    this.inspectionEnd = new Date(inspectionStartValue); // Start with the same date
    this.inspectionEnd.setMinutes(this.inspectionStart.getMinutes() + totalMinutes);

    this.deliverDetailsForm.get('inspectionEnd')?.setValue(this.inspectionEnd);
    this.endPickerTime = moment(this.inspectionEnd).format('HH:mm');
    this.durationCloseDropdown();
    this.updateDropdownState();
    this.selectDuration(minute);
  }

  public updateDropdownState() {
    // Close the dropdown after selecting both hour and minute
    if (this.selectedHour !== null && this.selectedMinutes !== null) {
      this.durationisOpen = false;
    }
  }

  public selectedVehicleType: any;

  public vehicleTypes = [
    { id: 1, type: 'Medium and Heavy Duty Truck' },
    { id: 2, type: 'Passenger Car' },
    { id: 3, type: 'Light Duty Truck' },
  ];

  public constructor(
    private readonly formBuilder: FormBuilder,
    public socket: Socket,
    private readonly toastr: ToastrService,
    public router: Router,
    private readonly mixpanelService: MixpanelService,
    public modalRef: BsModalRef,
    public modalRef1: BsModalRef,
    private readonly modalService: BsModalService,
    private readonly DeliveryService: DeliveryService,
    public projectService: ProjectService,
    public bookingTemplatesService: BookingTemplatesService,
    public modalRef2: BsModalRef,
  ) {
    this.selectedValue = this.recurrence[0].value;
    this.projectService.projectParent.subscribe((response7): void => {
      if (response7 !== undefined && response7 !== null && response7 !== '') {
        this.loader = true;
        this.ProjectId = response7.ProjectId;
        this.ParentCompanyId = response7.ParentCompanyId;
        this.loader = true;
        this.getOverAllGateInNewinspection();
        this.getTemplates();
      }
    });
    this.deliverForm();
    this.getTimeZoneList();
    if (this.selectedValue === 'Does Not Repeat') {
      this.deliverDetailsForm.get('repeatEveryType').setValue('');
    } else {
      this.deliverDetailsForm.get('repeatEveryCount').setValue(1);
    }
  }

  public ngOnInit(): void {
    this.lastId = this.modalRef?.content?.lastId;
    this.deliverDetailsForm.get('inspectionDate').setValue(this.selectedEventDate);
    this.getSelectedDate();
    const initialDeliveryDate = moment(this.deliverDetailsForm.get('inspectionDate').value).format('YYYY-MM-DD');
    this.generateWeekDates(moment(this.deliverDetailsForm.get('inspectionDate').value).format('YYYY-MM-DD'));
    this.getAvailableSlots(initialDeliveryDate);
    this.router.events.subscribe((e): void => {
      this.modalRef.hide();
    });
  }

  public generateWeekDates(selectedDate: Date | string): void {
    this.weekDates = [];

    for (let i = 0; i < 5; i++) {
      const nextDate = moment(selectedDate).add(i, 'days'); // Get the next 5 dates

      const dayName = nextDate.format('ddd'); // Get short day name like 'Wed'
      const day = nextDate.format('DD'); // Extract the day (e.g., '05')
      const fullDate = nextDate.format('YYYY-MM-DD'); // Full date like '2024-11-06'

      this.weekDates.push({
        name: dayName,
        date: day,
        fullDate,
      });
    }
    this.selectedDate = this.weekDates[0];
  }


  public selectDate(day: any) {
    this.selectedDate = day;
    this.deliverDetailsForm
      .get('inspectionDate')
      .setValue(moment(this.selectedDate.fullDate).format('MM/DD/YYYY'));
    this.getAvailableSlots(day.fullDate);
  }

  public getAvailableSlots(date) {
    const payload = {
      date,
      equipmentId: this.deliverDetailsForm.get('EquipmentId').value.map((data:any)=>{
        return data.id
      }),
      timeZone: this.timeZone?this.timeZone:'',
      duration: this.selectedMinutes?this.selectedMinutes : '',
      GateId: this.deliverDetailsForm.get('GateId').value ? this.deliverDetailsForm.get('GateId').value : '',
      LocationId: this.deliverDetailsForm.get('LocationId').value[0].id,
      bookingType : 'inspection'
    }
      this.DeliveryService.getAvailableTimeSlots(this.ProjectId,payload).subscribe((res)=>{
        if (res !== undefined && res !== null && res !== '') {
          this.availableTimes = res.slots;
          if (res.slots.AM.length === 0 && res.slots.PM.length === 0) {
            this.isSlotsNull = true;
          } else {
            this.isSlotsNull = false;
          }
        }
      })
  }

  public setDefaultDateAndTime(date: string | number | Date): void {
    this.inspectionStart = new Date();
    this.inspectionEnd = new Date();
    if (date) {
      this.inspectionStart = new Date(date);
      this.inspectionEnd = new Date(date);
    }
  }


  public selectTime(startStr: string, endStr: string) {
    const startDate = new Date(startStr);
    const endDate = new Date(endStr);

    this.deliverDetailsForm.patchValue({
      inspectionStart: startDate,
      inspectionEnd: endDate,
      inspectionDate: startDate,
    });

    this.selectedTime = `${startDate.toLocaleTimeString()} - ${endDate.toLocaleTimeString()}`;
  }


  public scrollToTime(index: number) {
    const container = this.timeSlotsContainer.nativeElement;
    const buttons = container.querySelectorAll('button');

    if (buttons[index]) {
      const selectedButton = buttons[index];
      container.scrollTop = selectedButton.offsetTop - container.offsetTop;
    }
  }

  public ngAfterViewInit(): void {
    this.lastId = this.modalRef?.content?.lastId;
    const stateValue = this.data;
    this.isBookingTemplate = stateValue?.bookingTemplate;
    this.getLastCraneRequestId();
    this.DeliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
        this.setDefaultPerson();
      }
    });
  }

  public vehicleTypeSelected(data: { id: string | number }): void {
    const getVehicleType = this.vehicleTypes.find((obj: any): any => +obj.id === +data.id);
    this.selectedVehicleType = getVehicleType?.type;
  }

  public selectDuration(event) {
    this.selectedMinutes = (this.selectedHour * 60) + this.selectedMinute;
    const initialDeliveryDate = moment(this.deliverDetailsForm.get('inspectionDate').value).format('YYYY-MM-DD');
    this.getAvailableSlots(initialDeliveryDate);
  }

  public setVehicleType(): void {
    if (this.selectedTemplate.vehicleType) {
      const vehicleTypeChosen = [];
      const getVehicleChosen = this.vehicleTypes.find((obj: any): any => obj.type === this.selectedTemplate.vehicleType);
      const data = {
        id: getVehicleChosen.id,
        type: getVehicleChosen.type,
      };
      vehicleTypeChosen.push(data);
      this.deliverDetailsForm.get('vehicleType').patchValue(vehicleTypeChosen);
      this.selectedVehicleType = getVehicleChosen?.type;
    }
  }

  public getSelectedDate(): void {
    const getData = this.data;
    if (getData) {
      if (getData.date && getData.currentView === 'Month') {
        this.deliverDetailsForm
          .get('inspectionDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00'));
        this.setDefaultDateAndTime(this.deliverDetailsForm.get('inspectionDate').value);
      }
      if (
        (getData.date && getData.currentView === 'Week')
        || (getData.date && getData.currentView === 'Day')
      ) {
        this.deliverDetailsForm
          .get('inspectionDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00:00'));
        this.deliverDetailsForm
          .get('inspectionStart')
          .setValue(moment(getData.date, 'YYYY-MM-DD hh:mm:ss').format());
        this.deliverDetailsForm
          .get('inspectionEnd')
          .setValue(moment(getData.date, 'YYYY-MM-DD hh:mm:ss').add(30, 'minutes').format());
      }
    } else {
      this.setDefaultDateAndTime(null);
    }
  }

  public getLastCraneRequestId(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getLastCraneRequestId(params).subscribe((response): void => {
      this.craneRequestLastId = response.lastId?.CraneRequestId;
      this.deliverDetailsForm.get('CraneRequestId').setValue(response.lastId?.CraneRequestId);
    });
  }

  public vehicleTypeDropdownSettings: IDropdownSettings = {
    singleSelection: true,
    idField: 'id',
    textField: 'type',
    allowSearchFilter: true,
    closeDropDownOnSelection: true,
  };

  public getOverAllGateInNewinspection(): void {
    this.modalLoader = true;
    const params = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .gateList(params, { isFilter: true, showActivatedAlone: true })
      .subscribe((res): void => {
        this.getOverAllEquipmentInNewinspection();
      });
  }

  public getOverAllEquipmentInNewinspection(): void {
    const newNdrGetEquipmentsParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listEquipment(newNdrGetEquipmentsParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((equipmentListResponseForNewNdr): void => {
        this.equipmentDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'equipmentName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
        this.newNdrgetCompanies();
      });
  }

  public newNdrgetCompanies(): void {
    const newNdrGetCompaniesParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getCompanies(newNdrGetCompaniesParams)
      .subscribe((companiesResponseForNewNdr: any): void => {
        if (companiesResponseForNewNdr) {
          this.companyList = companiesResponseForNewNdr.data;
          const loggedInUser = this.companyList.filter(
            (a: { id: any }): any => a.id === this.authUser.CompanyId,
          );
          this.deliverDetailsForm.get('companyItems').setValue(loggedInUser);
          this.getDefinable();
          this.newNdrCompanyDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'companyName',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
        }
      });
  }

  public getDefinable(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getDefinableWork(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.defineList = data;
        this.newNdrDefinableDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'DFOW',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          allowSearchFilter: true,
        };
        this.getLocations();
      }
    });
  }

  public getLocations(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getLocations(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.gateList = data[0].gateDetails ?? [];
        this.equipmentList = [this.noEquipmentOption, ...( data[0].EquipmentId?data[0].EquipmentId:[])];
        this.timeZone = data[0].TimeZoneId ? data[0].TimeZoneId[0].location : '';
        this.locationDropdownSettings = {
          singleSelection: true,
          idField: 'id',
          textField: 'locationPath',
          allowSearchFilter: true,
          closeDropDownOnSelection: true,
        };
        this.setDefaultLocationPath();
      }
    });
  }

  public requestAutocompleteItems = (text: string): Observable<any> => {
    const param = {
      ProjectId: this.ProjectId,
      search: text,
      ParentCompanyId: this.ParentCompanyId,
    };
    return this.DeliveryService.searchNewMember(param);
  };

  public closeModalPopup(): void {
    this.modalLoader = false;
  }

  public selectAM() {
    this.isAM = true;
  }

  public selectPM() {
    this.isAM = false;
  }



  public deliverForm(): void {
    this.deliverDetailsForm = this.formBuilder.group({
      LocationId: ['', Validators.compose([Validators.required])],
      EquipmentId: [this.formBuilder.array([])],
      GateId: ['', Validators.compose([Validators.required])],
      notes: [''],
      CraneRequestId: [''],
      person: ['', Validators.compose([Validators.required])],
      description: ['', Validators.compose([Validators.required])],
      inspectionDate: ['', Validators.compose([Validators.required])],
      inspectionStart: ['', Validators.compose([Validators.required])],
      inspectionEnd: ['', Validators.compose([Validators.required])],
      escort: [false],
      companyItems: [this.formBuilder.array([])],
      defineItems: [this.formBuilder.array([])],
      cranePickUpLocation: [''],
      craneDropOffLocation: [''],
      isAssociatedWithCraneRequest: [false, Validators.compose([Validators.required])],
      recurrence: [''],
      inspectionType: ['', Validators.compose([Validators.required])],
      repeatEveryCount: [''],
      repeatEveryType: [''],
      days: new FormArray([]),
      chosenDateOfMonth: [false, ''],
      dateOfMonth: [''],
      monthlyRepeatType: [''],
      endDate: [''],
      templateName: [''],
      TimeZoneId: ['', Validators.compose([Validators.required])],
      originationAddress: [''],
      vehicleType: [''],
    });
    this.deliverDetailsForm.get('escort').setValue(false);
    const newDate = moment().format('MM/DD/YYYY');
    this.deliverDetailsForm.get('inspectionDate').setValue(newDate);
    this.deliverDetailsForm.get('inspectionStart').setValue(this.inspectionStart);
    this.deliverDetailsForm.get('inspectionEnd').setValue(this.inspectionEnd);
    this.deliverDetailsForm.get('recurrence').setValue(this.selectedValue);

    this.formControlValueChanged();
    this.setCurrentTiming();
    if (this.isBookingTemplate) {
      const templateName = this.deliverDetailsForm.get('templateName');
      templateName.setValidators([Validators.required]);
    }
  }

  public onChangeInspectionType(type: any): void {
    this.deliverDetailsForm.get('inspectionType').setValue(type);
  }

  public convertStart(inspectionDate: Date, startHours: number, startMinutes: number): Date {
    const fullYear = inspectionDate.getFullYear();
    const fullMonth = inspectionDate.getMonth();
    const date = inspectionDate.getDate();
    const inspectionNewStart = new Date(fullYear, fullMonth, date, startHours, startMinutes);
    return inspectionNewStart;
  }

  public setDefaultLocationPath(): void {
    if (this.locationList.length > 0) {
      const getChosenLocation: any = this.locationList.filter((obj: any): any => obj.isDefault === true);
      if (getChosenLocation) {
        this.deliverDetailsForm.get('LocationId').patchValue(getChosenLocation);
        this.selectedLocationId = getChosenLocation[0]?.id;
      }
    }
    this.closeModalPopup();
  }

  public numberOnly(event: { which: any; keyCode: any }): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }


  public selectedBookingtype(type, template): void {
    this.selectedParams = type;
    if (this.deliverDetailsForm.touched && this.deliverDetailsForm.dirty) {
      this.openChangeConfirm(template);
    } else if (type) {
      this.selectfunction(this.selectedParams);
    }
  }

  public openChangeConfirm(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md changeConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }


  public closeChangeConfirm(confirmed) {
    if (confirmed === 'yes') {
      this.modalRef1.hide();
      this.selectfunction(this.selectedParams);
    }
    this.modalRef1.hide();
  }

  public handleAddressChange(address): void {
    this.deliverDetailsForm.get('originationAddress').setValue(address.formatted_address);
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    const companies = [];
    const persons = [];
    const define = [];
    const equipments = [];
    if (this.deliverDetailsForm.invalid) {
      this.formSubmitted = false;
      return;
    }
    if (this.deliverDetailsForm.get('isAssociatedWithCraneRequest').value) {
      if (
        !this.deliverDetailsForm.get('cranePickUpLocation').value
        || !this.deliverDetailsForm.get('craneDropOffLocation').value
      ) {
        this.formSubmitted = false;
        this.toastr.error('Please enter Picking From and Picking To');
        return;
      }
    }
    const formValue = this.deliverDetailsForm.value;
    if (formValue.EquipmentId.length <= 0) {
      this.toastr.error('Equipment is required');
      this.formSubmitted = false;
      return;
    }

    const newTimeZoneDetails = formValue.TimeZoneId;
    const inspectionDate = new Date(formValue.inspectionDate);
    const EndDate = new Date(formValue.endDate);
    const startNewDate = new Date(formValue.inspectionStart);
    const startHours = startNewDate.getHours();
    const startMinutes = startNewDate.getMinutes();
    const inspectionStart = this.convertStart(inspectionDate, startHours, startMinutes);
    const endNewDate = new Date(formValue.inspectionEnd);
    const endHours = endNewDate.getHours();
    const endMinutes = endNewDate.getMinutes();
    const startPicker = moment(formValue.inspectionStart).format('HH:mm');
    const endPicker = moment(formValue.inspectionEnd).format('HH:mm');
    this.endPickerTime = moment(formValue.inspectionEnd).format('HH:mm');
    const weekStartDate = moment(formValue.inspectionDate).format('YYYY MM DD 00:00:00');
    const weekEndDate = formValue.recurrence !== 'Does Not Repeat'
      ? moment(formValue.endDate).format('YYYY MM DD 00:00:00')
      : moment(formValue.inspectionDate).format('YYYY MM DD 00:00:00');
    const inspectionEnd = formValue.recurrence !== 'Does Not Repeat'
      ? this.convertStart(EndDate, endHours, endMinutes)
      : this.convertStart(inspectionDate, endHours, endMinutes);
    this.createinspection({
      newNdrFormValue: formValue,
      inspectionStart,
      inspectionEnd,
      companies,
      persons,
      define,
      newTimeZoneDetails,
      startPicker,
      endPicker,
      weekStartDate,
      weekEndDate,
      equipments,
    });
  }

  public checkEquipmentType(value: any): void {
    let findEquipmentType: any;
    let count = 0;
    let hasNoEquipmentOption = false;
    let hasOtherEquipment = false;
    if (value) {
               if(value.length == this.equipmentList.length -1 && this.equipmentList[0].id == 0) {
      this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
    }
    if(value.length != this.equipmentList.length && this.equipmentList[0].id != 0) {
      this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
      this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
    }
    if(value.length == 0) {
      this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
      this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
    }
    // Check if "No Equipment Needed" (id = 0) is selected
    hasNoEquipmentOption = value.some((item: any) => item.id === 0);

    // Check if other equipment is selected
    hasOtherEquipment = value.some((item: any) => item.id !== 0);

    const previousSelection = this.deliverDetailsForm.get('EquipmentId').value || [];
    const previousHasOther = previousSelection.some((item: any) => item.id !== 0);

    // Rule 1: If "No Equipment Needed" is selected and other items are selected, keep only "No Equipment Needed"
    if (hasNoEquipmentOption && hasOtherEquipment && !previousHasOther) {
      this.toastr.warning('When "No Equipment Needed" is selected, other equipment options cannot be selected.', 'Warning');
      const noEquipmentOnly = value.filter((item: any) => item.id === 0);
      this.deliverDetailsForm.get('EquipmentId').setValue(noEquipmentOnly);
      value = noEquipmentOnly;
      hasOtherEquipment = false;
    }

    // Rule 2: If other equipment is already selected and "No Equipment Needed" is now selected, remove it
    if (previousHasOther && hasNoEquipmentOption) {
      this.toastr.warning('When other equipment is selected, "No Equipment Needed" cannot be selected.', 'Warning');
      const filteredSelection = value.filter((item: any) => item.id !== 0);
      this.deliverDetailsForm.get('EquipmentId').setValue(filteredSelection);
      value = filteredSelection;
      hasNoEquipmentOption = false;
    }
      for (const item of value) {
        findEquipmentType = this.equipmentList.find((equipment: { id: any }) => equipment.id === item.id);
        if (findEquipmentType) {
          this.craneEquipmentTypeChosen = findEquipmentType.PresetEquipmentType?.isCraneType;
          if (this.craneEquipmentTypeChosen) {
            count++;
            this.deliverDetailsForm
              .get('isAssociatedWithCraneRequest')
              .setValue(true);
            break;
          } else {
            this.deliverDetailsForm
              .get('isAssociatedWithCraneRequest')
              .setValue(false);
          }
        }
      }
      this.checkEquipmentData(count, value, findEquipmentType)
    }
    this.getBookingData();
  }

  public checkEquipmentData(count, value, findEquipmentType) {
    if (count > 0) {
      this.getLastCraneRequestId();
    }
    if (value.length === 0) {
      this.craneEquipmentTypeChosen = false;
    }
    if (findEquipmentType) {
      const initialDeliveryDate = moment(this.deliverDetailsForm.get('inspectionDate').value).format('YYYY-MM-DD');
      this.getAvailableSlots(initialDeliveryDate);
    }
  }

  public createinspection(params: {
    newNdrFormValue: any;
    inspectionStart: Date;
    inspectionEnd: Date;
    companies: any[];
    persons: any[];
    define: any[];
    newTimeZoneDetails: any;
    startPicker: any;
    endPicker: any;
    weekStartDate: any;
    weekEndDate: any;
    equipments: any[];
  }): void {
    const {
      newNdrFormValue,
      inspectionStart,
      inspectionEnd,
      companies,
      persons,
      define,
      newTimeZoneDetails,
      startPicker,
      endPicker,
      weekStartDate,
      weekEndDate,
      equipments,
    } = params;

    const isValid = this.prepareInspectionData({
      newNdrFormValue,
      companies,
      persons,
      define,
      newTimeZoneDetails,
      equipments,
    });

    if (!isValid) return;

    if (this.checkStringEmptyValues(newNdrFormValue)) {
      this.formReset();
      return;
    }

    const payload = this.constructPayload({
      newNdrFormValue,
      inspectionStart,
      inspectionEnd,
      companies,
      persons,
      define,
      equipments,
      startPicker,
      endPicker,
    });

    if (payload.recurrence === 'Monthly' || payload.recurrence === 'Yearly') {
      payload.dateOfMonth = this.monthlyDate;
    }

    if (payload.recurrence) {
      payload.startPicker = startPicker;
      payload.endPicker = endPicker;
      payload.inspectionStart = weekStartDate;
      payload.inspectionEnd = weekEndDate;

      if (!this.validateRecurrencePayload(payload)) {
        return;
      }
    }

    this.createNDR(payload);
  }

  public prepareInspectionData(params: {
    newNdrFormValue: any;
    companies: any[];
    persons: any[];
    define: any[];
    newTimeZoneDetails: any;
    equipments: any[];
  }): boolean {
    const {
      newNdrFormValue,
      companies,
      persons,
      define,
      newTimeZoneDetails,
      equipments,
    } = params;

    const {
      companyItems, person, defineItems, TimeZoneId, EquipmentId,
    } = newNdrFormValue;

    if (!companyItems || companyItems.length === 0) {
      this.toastr.error('Responsible Company is required');
      this.formReset();
      return false;
    }

    if (!person || person.length === 0) {
      this.toastr.error('Responsible Person is required');
      this.formReset();
      return false;
    }

    companyItems.forEach((c: { id: any }) => companies.push(c.id));
    person.forEach((p: { id: any }) => persons.push(p.id));
    if (defineItems && defineItems.length > 0) {
      defineItems.forEach((d: { id: any }) => define.push(d.id));
    }

    if (newTimeZoneDetails && newTimeZoneDetails.length > 0 && TimeZoneId?.length > 0) {
      TimeZoneId.forEach((tz: { id: any }) => {
        this.timeZoneValues = tz.id;
      });
    }

    if (EquipmentId && EquipmentId.length > 0) {
      EquipmentId.forEach((e: { id: any }) => equipments.push(e.id));
    }

    return true;
  }


  public constructPayload({
    newNdrFormValue, inspectionStart, inspectionEnd, companies, persons, define, equipments, startPicker, endPicker,
  }) {
    const escortCondition = newNdrFormValue.escort === null || newNdrFormValue.escort === undefined;
    const payload = {
      description: newNdrFormValue.description,
      companies,
      escort: escortCondition ? false : newNdrFormValue.escort,
      ProjectId: this.ProjectId,
      GateId: newNdrFormValue.GateId,
      notes: newNdrFormValue.notes,
      EquipmentId: equipments,
      inspectionStart,
      inspectionEnd,
      ParentCompanyId: this.ParentCompanyId,
      persons,
      define,
      isAssociatedWithCraneRequest: newNdrFormValue.isAssociatedWithCraneRequest,
      cranePickUpLocation: '',
      craneDropOffLocation: '',
      CraneRequestId: '',
      requestType: 'inspectionRequest',
      LocationId: this.selectedLocationId,
      recurrence: newNdrFormValue.recurrence,
      chosenDateOfMonthValue: newNdrFormValue.chosenDateOfMonth !== false ? newNdrFormValue.chosenDateOfMonth : 1,
      chosenDateOfMonth: newNdrFormValue.chosenDateOfMonth === 1,
      dateOfMonth: newNdrFormValue.dateOfMonth,
      monthlyRepeatType: newNdrFormValue.monthlyRepeatType,
      days: newNdrFormValue.days ? this.sortWeekDays(newNdrFormValue.days) : [],
      repeatEveryType: newNdrFormValue.repeatEveryType
        ? newNdrFormValue.repeatEveryType
        : null,
      repeatEveryCount: newNdrFormValue.repeatEveryCount
        ? newNdrFormValue.repeatEveryCount.toString()
        : null,
      TimeZoneId: this.timeZoneValues,
      startPicker,
      endPicker,
      inspectionType: newNdrFormValue.inspectionType,
      originationAddress: newNdrFormValue.originationAddress,
      vehicleType: this.selectedVehicleType,
    };
    if (payload.isAssociatedWithCraneRequest) {
      payload.cranePickUpLocation = newNdrFormValue.cranePickUpLocation.trim();
      payload.craneDropOffLocation = newNdrFormValue.craneDropOffLocation.trim();
      payload.CraneRequestId = newNdrFormValue.CraneRequestId;
      payload.requestType = 'inspectionRequest';
    } else {
      payload.cranePickUpLocation = null;
      payload.craneDropOffLocation = null;
      payload.CraneRequestId = null;
      payload.requestType = 'inspectionRequest';
    }

    return payload;
  }

  public validateRecurrencePayload(payload) {
    if (payload.startPicker === payload.endPicker) {
      this.showErrorMessage('inspection Start time and End time should not be the same');
      return false;
    }

    if (payload.startPicker > payload.endPicker) {
      this.showErrorMessage('Please enter From Time lesser than To Time');
      return false;
    }

    if (
      payload.recurrence !== 'Does Not Repeat'
      && new Date(payload.craneDeliveryEnd) <= new Date(payload.craneDeliveryStart)
    ) {
      this.showErrorMessage('Please enter End Date greater than Start Date');
      return false;
    }

    return true;
  }

  public showErrorMessage(message: string) {
    this.toastr.error(message);
    this.formSubmitted = false;
    this.submitted = false;
  }

  public sortWeekDays(data: any[]): any {
    const order = {
      Sunday: 1,
      Monday: 2,
      Tuesday: 3,
      Wednesday: 4,
      Thursday: 5,
      Friday: 6,
      Saturday: 7,
    };

    if (data.length > 0) {
      return data.sort((a, b): any => order[a] - order[b]);
    }
  }

  public createNDR(payload: {
    description: any;
    companies: any;
    escort: any;
    ProjectId: any;
    GateId: any;
    notes: any;
    EquipmentId: any;
    LocationId: any;
    inspectionStart: any;
    inspectionEnd: any;
    ParentCompanyId: any;
    persons: any;
    define: any;
    isAssociatedWithCraneRequest: any;
    cranePickUpLocation: string;
    craneDropOffLocation: string;
    CraneRequestId: string;
    requestType: string;
    recurrence: any;
    chosenDateOfMonth: any;
    dateOfMonth: any;
    monthlyRepeatType: any;
    days: any;
    repeatEveryType: any;
    repeatEveryCount: any;
    inspectionType: any;
    originationAddress: any;
    vehicleType: any;
  }): void {
    this.DeliveryService.createInspectionNDR(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.socket.emit('NDRCreateHistory', response);
          this.mixpanelService.addMixpanelEvents('Created New inspection Booking');
          this.formReset();
          this.deliverDetailsForm.reset();
          this.DeliveryService.updatedInspectionHistory({ status: true }, 'NDRCreateHistory');
          this.resetForm('yes');
          this.NDRTimingChanged = false;
          this.disableSaveAsTemplate = false;
        }
      },
      error: (NDRCreateHistoryError): void => {
        this.formReset();
        this.NDRTimingChanged = false;
        if (NDRCreateHistoryError.message?.statusCode === 400) {
          this.showError(NDRCreateHistoryError);
        } else if (!NDRCreateHistoryError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else if (NDRCreateHistoryError.message.includes('overlaps')) {
          this.toastr.error(NDRCreateHistoryError.message, 'OOPS!');
        } else {
          this.toastr.error(NDRCreateHistoryError.message, 'OOPS!');
        }
      },
    });
  }

  public closeTemplateNamePopup(action: string): void {
    if (action === 'no') {
      this.modalRef2.hide();
    } else {
      this.modalRef2.hide();
      this.saveAsTemplate();
    }
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.deliverDetailsForm.reset();
      this.setDefaultPerson();
      this.deliverDetailsForm.get('GateId').setValue('');
      this.submitted = false;
      this.formSubmitted = false;
      this.editSubmitted = false;
      this.modalRef.hide();
      this.NDRTimingChanged = false;
      this.templateName = '';
    }
  }

  public close(template: TemplateRef<any>): void {
    if (
      (this.deliverDetailsForm.touched && this.deliverDetailsForm.dirty)
      || (this.deliverDetailsForm.get('defineItems').dirty
        && this.deliverDetailsForm.get('defineItems').value
        && this.deliverDetailsForm.get('defineItems').value.length > 0)
      || (this.deliverDetailsForm.get('companyItems').dirty
        && this.deliverDetailsForm.get('companyItems').value
        && this.deliverDetailsForm.get('companyItems').value.length > 0)
        || (this.deliverDetailsForm.get('EquipmentId').dirty
        && this.deliverDetailsForm.get('EquipmentId').value
        && this.deliverDetailsForm.get('EquipmentId').value.length > 0)
      || this.NDRTimingChanged
    ) {
      this.openConfirmationModalPopup(template);
    } else {
      this.resetForm('yes');
    }
  }

  public inspectionEndTimeChangeDetection(): void {
    this.NDRTimingChanged = true;
  }

  public openConfirmationModalPopup(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public changeDate(event: any): void {
    if (!this.modalLoader) {
      const startTime = new Date(event).getHours();
      const minutes = new Date(event).getMinutes();
      this.inspectionEnd = new Date();
      this.inspectionEnd.setHours(startTime + 1);
      this.inspectionEnd.setMinutes(minutes);
      this.deliverDetailsForm.get('inspectionEnd').setValue(this.inspectionEnd);
      this.NDRTimingChanged = true;
    }
  }

  public setDefaultPerson(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.DeliveryService.getMemberRole(params).subscribe((res): void => {
      this.authUser = res.data;
      let email: string;
      if (this.authUser.User.lastName != null) {
        email = `${this.authUser.User.firstName} ${this.authUser?.User?.lastName} (${this.authUser.User.email})`;
      } else {
        email = `${this.authUser.User.firstName} (${this.authUser.User.email})`;
      }
      const newMemberList = [
        {
          email,
          id: this.authUser.id,
          readonly: true,
        },
      ];
      this.deliverDetailsForm.get('person').patchValue(newMemberList);
    });
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public formReset(): void {
    this.formSubmitted = false;
    this.submitted = false;
  }

  public checkStringEmptyValues(formValue: { description: string; notes: string }): boolean {
    if (formValue.description.trim() === '') {
      this.toastr.error('Please Enter valid Company Name.', 'OOPS!');
      return true;
    }
    if (formValue.notes) {
      if (formValue.notes.trim() === '') {
        this.toastr.error('Please Enter valid address.', 'OOPS!');
        return true;
      }
    }
    return false;
  }

  public formControlValueChanged(): void {
    this.deliverDetailsForm.get('repeatEveryType').valueChanges.subscribe((value: string): void => {
      const days = this.deliverDetailsForm.get('days');
      const chosenDateOfMonth = this.deliverDetailsForm.get('chosenDateOfMonth');
      const dateOfMonth = this.deliverDetailsForm.get('dateOfMonth');
      const monthlyRepeatType = this.deliverDetailsForm.get('monthlyRepeatType');
      if (value === 'Week' || value === 'Day' || value === 'Weeks') {
        days.setValidators([Validators.required]);
      } else {
        days.clearValidators();
      }
      if (value === 'Month' || value === 'Months' || value === 'Year' || value === 'Years') {
        if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 1) {
          dateOfMonth.setValidators([Validators.required]);
          monthlyRepeatType.clearValidators();
        } else {
          monthlyRepeatType.setValidators([Validators.required]);
          dateOfMonth.clearValidators();
        }
      } else {
        chosenDateOfMonth.clearValidators();
        dateOfMonth.clearValidators();
        monthlyRepeatType.clearValidators();
      }
      chosenDateOfMonth.updateValueAndValidity();
      dateOfMonth.updateValueAndValidity();
      monthlyRepeatType.updateValueAndValidity();
      days.updateValueAndValidity();
    });
  }

  public setCurrentTiming(): void {
    const newDate = moment().format('MM-DD-YYYY');
    const hours = moment(new Date()).format('HH');
    this.startTime = new Date();
    this.startTime.setHours(+hours);
    this.startTime.setMinutes(0);
    this.endTime = new Date();
    this.endTime.setHours(+hours);
    this.endTime.setMinutes(30);
    if (!this.deliverDetailsForm.get('endDate')?.value) {
      this.deliverDetailsForm.get('endDate').setValue(newDate);
    }
    this.changeMonthlyRecurrence();
  }

  public chooseRepeatEveryType(value: string, eventDetail: any): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    if (value === 'Day' || value === 'Days') {
      this.deliverDetailsForm.get('recurrence').setValue('Daily');
    }
    if (value === 'Week' || value === 'Weeks') {
      this.deliverDetailsForm.get('recurrence').setValue('Weekly');
    }
    if (value === 'Month' || value === 'Months') {
      this.deliverDetailsForm.get('recurrence').setValue('Monthly');
    }
    if (value === 'Year' || value === 'Years') {
      this.deliverDetailsForm.get('recurrence').setValue('Yearly');
    }
    if (value === 'Day' || value === 'Days') {
      this.checkform = this.deliverDetailsForm.get('days') as FormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day: any): any => {
        const dayObj = day;
        dayObj.checked = true;
        dayObj.isDisabled = false;
        this.checkform.push(new FormControl(dayObj.value));
        return dayObj;
      });
    }
    if (value === 'Week' || value === 'Weeks') {
      if (eventDetail?.days?.length > 0) {
        this.checkform = this.deliverDetailsForm.get('days') as FormArray;
        this.checkform.controls = [];
        this.weekDays = this.weekDays.map((day1: any): void => {
          const dayObj1 = day1;
          if (eventDetail.days.includes(dayObj1.value)) {
            dayObj1.checked = true;
            this.checkform.push(new FormControl(dayObj1.value));
          } else {
            dayObj1.checked = false;
          }
          dayObj1.isDisabled = false;
          return dayObj1;
        });
      } else {
        this.weekDays = this.weekDays.map((day2: any): void => {
          const dayObj2 = day2;
          if (dayObj2.value === 'Monday') {
            dayObj2.checked = true;
          } else {
            dayObj2.checked = false;
          }
          dayObj2.isDisabled = false;
          return dayObj2;
        });
        this.checkform = this.deliverDetailsForm.get('days') as FormArray;
        this.checkform.controls = [];
        this.checkform.push(new FormControl('Monday'));
      }
    }
    if (value === 'Day' || value === 'Week' || value === 'Month' || value === 'Year') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showRecurrenceTypeDropdown = false;
    }
    if (value === 'Days' || value === 'Weeks' || value === 'Months' || value === 'Years') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.showRecurrenceTypeDropdown = false;
    }
    if (this.deliverDetailsForm.get('repeatEveryCount').value > 1) {
      this.showRecurrenceTypeDropdown = true;
      this.isRepeatWithMultipleRecurrence = false;
    }
    this.selectedRecurrence = this.deliverDetailsForm.get('recurrence').value;
    this.occurMessage();
  }

  public repeatEveryTypeMessage() {
    if (this.deliverDetailsForm.get('repeatEveryType').value === 'Day') {
      this.message = 'Occurs every day';
    }
    if (this.deliverDetailsForm.get('repeatEveryType').value === 'Days') {
      if (+this.deliverDetailsForm.get('repeatEveryCount').value === 2) {
        this.message = 'Occurs every other day';
      } else {
        this.message = `Occurs every ${this.deliverDetailsForm.get('repeatEveryCount').value} days`;
      }
    }
  }

  public occurMessage(): void {
    this.message = '';
    this.repeatEveryTypeMessage();
    if (this.deliverDetailsForm.get('repeatEveryType').value === 'Week') {
      let weekDays = '';
      this.weekDays.map((dayObj1: any): any => {
        if (dayObj1.checked) {
          weekDays = `${weekDays + dayObj1.value},`;
        }
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message += `Occurs every ${weekDays}`;
    }
    if (this.deliverDetailsForm.get('repeatEveryType').value === 'Weeks') {
      let weekDays = '';
      this.weekDays.map((dayObj2: any): any => {
        if (dayObj2.checked) {
          weekDays = `${weekDays + dayObj2.value},`;
        }
        return false;
      });
      if (+this.deliverDetailsForm.get('repeatEveryCount').value === 2) {
        this.message = `Occurs every other  ${weekDays}`;
      } else {
        this.message = `Occurs every ${
          this.deliverDetailsForm.get('repeatEveryCount').value
        } weeks on ${weekDays}`;
      }
      weekDays = weekDays.replace(/,\s*$/, '');
    }
    if (
      this.deliverDetailsForm.get('repeatEveryType').value === 'Month'
      || this.deliverDetailsForm.get('repeatEveryType').value === 'Months'
      || this.deliverDetailsForm.get('repeatEveryType').value === 'Year'
      || this.deliverDetailsForm.get('repeatEveryType').value === 'Years'
    ) {
      if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 1) {
        this.message = `Occurs on day ${this.monthlyDate}`;
      } else if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 2) {
        this.message = `Occurs on the ${this.monthlyDayOfWeek}`;
      } else {
        this.message = `Occurs on the ${this.monthlyLastDayOfWeek}`;
      }
    }
    if (this.message) {
      this.message += ` until ${moment(this.deliverDetailsForm.get('endDate').value).format(
        'MMMM DD, YYYY',
      )}`;
    }
  }

  public changeRecurrenceCount(value: number): void {
    if (value > 0) {
      const recurrencedata = this.deliverDetailsForm.get('recurrence').value;

      this.updateRecurrenceFlags(recurrencedata, value);
      this.updateRepeatEveryType(recurrencedata, value);

      this.selectedRecurrence = recurrencedata;
      this.occurMessage();
    } else if (value < 0) {
      this.deliverDetailsForm.get('repeatEveryCount').setValue(1);
    }
  }

  public updateRecurrenceFlags(recurrencedata: string, value: number): void {
    const isSingle = +value === 1;

    if (recurrencedata === 'Daily') {
      this.isRepeatWithSingleRecurrence = isSingle;
      this.isRepeatWithMultipleRecurrence = false;
      this.showRecurrenceTypeDropdown = !isSingle;
    }

    if (recurrencedata === 'Weekly') {
      this.isRepeatWithSingleRecurrence = isSingle;
      this.isRepeatWithMultipleRecurrence = !isSingle;
      this.showRecurrenceTypeDropdown = false;
    }

    if (recurrencedata === 'Monthly' || recurrencedata === 'Yearly') {
      this.isRepeatWithSingleRecurrence = isSingle;
      this.isRepeatWithMultipleRecurrence = !isSingle;
      this.showRecurrenceTypeDropdown = false;
    }
  }

  public updateRepeatEveryType(recurrencedata: string, value: number): void {
    const repeatEveryTypeControl = this.deliverDetailsForm.get('repeatEveryType');

    switch (recurrencedata) {
      case 'Daily':
        repeatEveryTypeControl.setValue(value > 1 ? 'Days' : 'Day');
        break;

      case 'Weekly':
        repeatEveryTypeControl.setValue(value > 1 ? 'Weeks' : 'Week');
        break;

      case 'Monthly':
        repeatEveryTypeControl.setValue(value > 1 ? 'Months' : 'Month');
        this.changeMonthlyRecurrence();
        this.showMonthlyRecurrence();
        break;

      case 'Yearly':
        repeatEveryTypeControl.setValue(value > 1 ? 'Years' : 'Year');
        break;

      default:
        break;
    }
  }


  public changeMonthlyRecurrence(): void {
    this.setMonthlyOrYearlyRecurrenceOption();
    this.updateFormValidation();
    this.showMonthlyRecurrence();
    this.occurMessage();
  }

  public setMonthlyOrYearlyRecurrenceOption(): void {
    if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 1) {
      this.deliverDetailsForm
        .get('dateOfMonth')
        .setValue(moment(this.deliverDetailsForm.get('inspectionDate').value).format('DD'));
      this.deliverDetailsForm.get('monthlyRepeatType').setValue(null);
    } else if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 2) {
      this.deliverDetailsForm.get('dateOfMonth').setValue(null);
      this.deliverDetailsForm.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
    } else if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 3) {
      this.deliverDetailsForm.get('dateOfMonth').setValue(null);
      this.deliverDetailsForm.get('monthlyRepeatType').setValue(this.monthlyLastDayOfWeek);
    }
  }

  public showMonthlyRecurrence(): void {
    if (this.deliverDetailsForm.get('inspectionDate').value) {
      this.generateWeekDates(moment(this.deliverDetailsForm.get('inspectionDate').value).format('YYYY-MM-DD'));
      const startDate = moment(this.deliverDetailsForm.get('inspectionDate').value).format('YYYY-MM');
      const chosenDay = moment(this.deliverDetailsForm.get('inspectionDate').value).format('dddd');
      this.monthlyDate = moment(this.deliverDetailsForm.get('inspectionDate').value).format('DD');
      const day = moment(startDate, 'YYYY-MM').startOf('month').day(chosenDay);
      const getAllDays = [];
      if (day.date() > 7) day.add(7, 'd');
      const month = day.month();
      while (month === day.month()) {
        getAllDays.push(day.format('YYYY-MM-DD'));
        day.add(7, 'd');
      }
      let week: string;
      let extraOption: string;
      this.enableOption = false;
      getAllDays.forEach((element, i): void => {
        if (
          moment(this.deliverDetailsForm.get('inspectionDate').value).format('YYYY-MM-DD')
          === moment(element).format('YYYY-MM-DD')
        ) {
          const number = i + 1;
          if (number === 1) {
            week = 'First';
          }
          if (number === 2) {
            week = 'Second';
          }
          if (number === 3) {
            week = 'Third';
          }
          if (number === 4) {
            this.enableOption = true;
            extraOption = 'Last';
            week = 'Fourth';
          }
          if (number === 5) {
            week = 'Last';
          }
          if (number === 6) {
            week = 'Last';
          }
        }
      });
      this.monthlyDayOfWeek = `${week} ${chosenDay}`;
      this.monthlyLastDayOfWeek = `${extraOption} ${chosenDay}`;
      if (!this.enableOption && this.deliverDetailsForm.get('chosenDateOfMonth').value === 3) {
        this.deliverDetailsForm.get('chosenDateOfMonth').setValue(2);
        this.deliverDetailsForm.get('dateOfMonth').setValue(null);
        this.deliverDetailsForm.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
      }
      this.setMonthlyOrYearlyRecurrenceOption();
      this.occurMessage();
      if (this.deliverDetailsForm.get('endDate').value) {
        this.updateWeeklyDates();
      }
    }
  }

  public updateWeeklyDates() {
    const deliveryEnd = this.deliverDetailsForm.get('endDate').value;

    if (deliveryEnd) {
      const endDate = moment(deliveryEnd, 'YYYY-MM-DD');

      let currentDate;
      if (this.weekDates.length > 0) {
        currentDate = moment(this.weekDates[0].fullDate, 'YYYY-MM-DD');
      } else {
        currentDate = moment();
      }

      while (currentDate.isSameOrBefore(endDate)) {
        const exists = this.weekDates.some((weekDate) => weekDate.fullDate === currentDate.format('YYYY-MM-DD'));

        if (!exists) {
          this.weekDates.push({
            name: currentDate.format('ddd'),
            date: currentDate.format('DD'),
            fullDate: currentDate.format('YYYY-MM-DD'),
          });
        }

        currentDate.add(1, 'day');
      }
    }
  }



  public updateFormValidation(): void {
    const chosenDateOfMonth = this.deliverDetailsForm.get('chosenDateOfMonth');
    const dateOfMonth = this.deliverDetailsForm.get('dateOfMonth');
    const monthlyRepeatType = this.deliverDetailsForm.get('monthlyRepeatType');
    if (this.deliverDetailsForm.get('chosenDateOfMonth').value === 1) {
      dateOfMonth.setValidators([Validators.required]);
      monthlyRepeatType.clearValidators();
    } else {
      monthlyRepeatType.setValidators([Validators.required]);
      dateOfMonth.clearValidators();
    }
    chosenDateOfMonth.updateValueAndValidity();
    dateOfMonth.updateValueAndValidity();
    monthlyRepeatType.updateValueAndValidity();
  }

  public onChange(event: { target: { value: any; checked: any } }): void {
    this.checkform = this.deliverDetailsForm.get('days') as FormArray;
    this.valueExists = this.checkform.controls.filter(
      (object: { value: any }): any => object.value === event.target.value,
    );
    if (event.target.checked) {
      this.checkform.push(new FormControl(event.target.value));
      this.weekDays = this.weekDays.map((day16: any): void => {
        const dayObj16 = day16;
        if (day16.value === event.target.value) {
          dayObj16.checked = true;
        }
        return dayObj16;
      });
      if (this.checkform.controls.length === 2) {
        this.weekDays = this.weekDays.map((day17: any): void => {
          const dayObj17 = day17;
          dayObj17.isDisabled = false;
          return dayObj17;
        });
      }
    } else if (this.selectedRecurrence === 'Weekly') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: FormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day18: any): void => {
            const dayObj18 = day18;
            if (dayObj18.value === event.target.value) {
              dayObj18.checked = false;
            }
            return dayObj18;
          });
        }
        if (this.checkform.controls.length === 1) {
          this.weekDays = this.weekDays.map((day19: any): void => {
            const dayObj19 = day19;
            if (dayObj19.value === this.checkform.controls[0].value) {
              dayObj19.isDisabled = true;
              dayObj19.checked = true;
            }
            return dayObj19;
          });
          return;
        }
        i += 1;
      });
    } else if (this.selectedRecurrence === 'Daily') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: FormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day: any): void => {
            const dayObj = day;
            if (dayObj.value === event.target.value) {
              dayObj.checked = false;
              dayObj.isDisabled = false;
            }
            return dayObj;
          });
          return;
        }
        i += 1;
      });
    }
    if (this.checkform.controls.length !== 7) {
      this.deliverDetailsForm.get('recurrence').setValue('Weekly');
      if (+this.deliverDetailsForm.get('repeatEveryCount').value === 1) {
        this.deliverDetailsForm.get('repeatEveryType').setValue('Week');
      } else {
        this.deliverDetailsForm.get('repeatEveryType').setValue('Weeks');
      }
      this.selectedRecurrence = this.deliverDetailsForm.get('recurrence').value;
    }
    if (this.checkform.controls.length === 7) {
      this.deliverDetailsForm.get('recurrence').setValue('Daily');
      if (+this.deliverDetailsForm.get('repeatEveryCount').value === 1) {
        this.deliverDetailsForm.get('repeatEveryType').setValue('Day');
      } else {
        this.deliverDetailsForm.get('repeatEveryType').setValue('Days');
      }
      this.selectedRecurrence = this.deliverDetailsForm.get('recurrence').value;
    }
    this.occurMessage();
  }

  public getRepeatEveryType(value) {
    if (value === 'Does Not Repeat') {
      this.deliverDetailsForm.get('repeatEveryType').setValue('');
    } else {
      this.deliverDetailsForm.get('repeatEveryCount').setValue(1);
    }
    if (value === 'Daily') {
      this.deliverDetailsForm.get('repeatEveryType').setValue('Day');
    }
    if (value === 'Weekly') {
      this.deliverDetailsForm.get('repeatEveryType').setValue('Week');
    }
    if (value === 'Monthly') {
      this.deliverDetailsForm.get('repeatEveryType').setValue('Month');
    }
    if (value === 'Yearly') {
      this.deliverDetailsForm.get('repeatEveryType').setValue('Year');
    }
  }

  public onRecurrenceSelect(value: string): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    this.selectedRecurrence = value;
    this.getRepeatEveryType(value);
    if (this.deliverDetailsForm.get('repeatEveryCount').value > 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.checkform = this.deliverDetailsForm.get('days') as FormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day11: any): void => {
        const dayObj11 = day11;
        dayObj11.checked = true;
        dayObj11.isDisabled = false;
        this.checkform.push(new FormControl(dayObj11.value));
        return dayObj11;
      });
    }
    if (this.deliverDetailsForm.get('repeatEveryCount').value > 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.weekDays = this.weekDays.map((day12: any): void => {
        const dayObj12 = day12;
        if (dayObj12.value === 'Monday') {
          dayObj12.checked = true;
        } else {
          dayObj12.checked = false;
        }
        dayObj12.isDisabled = false;
        return dayObj12;
      });
      this.checkform = this.deliverDetailsForm.get('days') as FormArray;
      this.checkform.controls = [];
      this.checkform.push(new FormControl('Monday'));
    }
    if (this.deliverDetailsForm.get('repeatEveryCount').value === 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.weekDays = this.weekDays.map((day13: any): void => {
        const dayObj13 = day13;
        if (dayObj13.value === 'Monday') {
          dayObj13.checked = true;
        } else {
          dayObj13.checked = false;
        }
        dayObj13.isDisabled = false;
        return dayObj13;
      });
      this.checkform = this.deliverDetailsForm.get('days') as FormArray;
      this.checkform.controls = [];
      this.checkform.push(new FormControl('Monday'));
    }
    if (this.deliverDetailsForm.get('repeatEveryCount').value === 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.checkform = this.deliverDetailsForm.get('days') as FormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day14: any): void => {
        const dayObj14 = day14;
        dayObj14.checked = true;
        dayObj14.isDisabled = false;
        this.checkform.push(new FormControl(dayObj14.value));
        return dayObj14;
      });
    }
    if (
      this.deliverDetailsForm.get('repeatEveryCount').value === 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.deliverDetailsForm.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showMonthlyRecurrence();
    }
    if (
      this.deliverDetailsForm.get('repeatEveryCount').value > 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.deliverDetailsForm.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.showMonthlyRecurrence();
    }
    this.occurMessage();
  }

  public timeZoneSelected(id: string | number): void {
    this.selectedTimeZoneValue = this.timezoneList.find((obj: any): any => +obj.id === +id);
  }

  public locationSelected(data: { id: string | number }): void {
    const getChosenLocation = this.locationList.find((obj: any): any => +obj.id === +data.id);
    if (getChosenLocation) {
      this.deliverDetailsForm.get('GateId').setValue('');
      this.deliverDetailsForm
        .get('EquipmentId')
        .setValue('');
      this.gateList = [];
      this.equipmentList = [];
      if (getChosenLocation.gateDetails) {
        this.gateList = getChosenLocation.gateDetails;
        this.equipmentList = [this.noEquipmentOption, ...getChosenLocation.EquipmentId];
        this.timeZone = getChosenLocation.TimeZoneId[0].location;
      }
      this.selectedLocationId = getChosenLocation?.id;
    }
  }

  public getTimeZoneList(): void {
    this.projectService.getTimeZoneList().subscribe({
      next: (response: any): void => {
        this.loader = true;
        if (response) {
          const params = {
            ProjectId: this.ProjectId,
          };
          if (params.ProjectId) {
            this.projectService.getSingleProject(params).subscribe((projectList: any): void => {
              if (projectList) {
                this.timezoneList = response.data;
                this.dropdownSettings = {
                  singleSelection: true,
                  idField: 'id',
                  textField: 'location',
                  allowSearchFilter: true,
                  closeDropDownOnSelection: true,
                };
                this.getSelectedTimeZone = this.timezoneList.filter(
                  (obj: any): any => +obj.id === +projectList.data.TimeZoneId,
                );
                this.deliverDetailsForm.get('TimeZoneId').patchValue(this.getSelectedTimeZone);
                this.loader = false;
              }
            });
          }
        }
      },
      error: (getTimeZoneListErr): void => {
        if (getTimeZoneListErr.message?.statusCode === 400) {
          this.showError(getTimeZoneListErr);
        } else if (!getTimeZoneListErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(getTimeZoneListErr.message, 'OOPS!');
        }
      },
    });
  }

  public saveAsTemplatePopup(template: TemplateRef<any>): void {
    const templateName = this.deliverDetailsForm.get('templateName');
    templateName.setValidators([Validators.required]);
    if (!this.selectedTemplate) {
      let data = {};
      data = {
        keyboard: false,
        class: 'modal-md openTemplateName-popup modal-dialog-centered custom-modal',
      };
      this.modalRef2 = this.modalService.show(template, data);
    }
  }


  public saveTemplatePayload({
    formValue,
    companies,
    persons,
    define,
    equipments,
    inspectionStart,
    inspectionEnd,
    escortCondition,
  }) {
    const payload = {
      template_name: this.templateName,
      description: formValue.description,
      responsible_company: JSON.stringify(companies),
      responsible_person: JSON.stringify(persons),
      date: inspectionStart,
      from_time: inspectionStart,
      to_time: inspectionEnd,
      time_zone: this.timeZoneValues,
      location: this.selectedLocationId,
      notes: formValue.notes,
      picking_from: '',
      picking_to: '',
      is_escort_needed: escortCondition ? false : formValue.escort,
      dfow: JSON.stringify(define),
      equipment: JSON.stringify(equipments),
      gate: formValue.GateId,
      isAssociatedWithCraneRequest: formValue.isAssociatedWithCraneRequest,
      recurrence: JSON.stringify({
        recurrence: formValue.recurrence,
        dateOfMonth: formValue.recurrence === 'Monthly' || formValue.recurrence === 'Yearly' ? this.monthlyDate : formValue.dateOfMonth,
        monthlyRepeatType: formValue.monthlyRepeatType,
        repeatEveryCount: formValue.repeatEveryCount
          ? formValue.repeatEveryCount.toString()
          : null,
        requestType: formValue.isAssociatedWithCraneRequest ? 'inspectionRequestWithCrane' : 'inspectionRequest',
        repeatEveryType: formValue.repeatEveryType
          ? formValue.repeatEveryType
          : null,
        chosenDateOfMonth: formValue.chosenDateOfMonth === 1,
      }),
      template_type: 'inspection',
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };

    if (payload.isAssociatedWithCraneRequest) {
      payload.picking_from = formValue.cranePickUpLocation.trim();
      payload.picking_to = formValue.craneDropOffLocation.trim();
    } else {
      payload.picking_from = null;
      payload.picking_to = null;
    }
    return payload;
  }

  public constructFormData() {
    const formValue = this.deliverDetailsForm.value;
    const companies = Array.isArray(formValue.companyItems)
      ? formValue.companyItems.map((item: { id: any }) => item.id)
      : [];

    const persons = Array.isArray(formValue.person)
      ? formValue.person.map((item: { id: any }) => item.id)
      : [];

    const define = Array.isArray(formValue.defineItems)
      ? formValue.defineItems.map((item: { id: any }) => item.id)
      : [];

    const equipments = Array.isArray(formValue.EquipmentId)
      ? formValue.EquipmentId.map((item: { id: any }) => item.id)
      : [];

    const escortCondition = formValue.escort === null || formValue.escort === undefined;

    return {
      companies,
      persons,
      define,
      equipments,
      escortCondition,
    };
  }

  public saveAsTemplate(): void {
    this.submitted = true;
    this.formSubmitted = true;
    if (this.deliverDetailsForm.invalid) {
      this.formSubmitted = false;
      return;
    }
    const formValue = this.deliverDetailsForm.value;
    const {
      companies, persons, define, equipments, escortCondition,
    } = this.constructFormData();
    const newTimeZoneDetails = formValue.TimeZoneId;
    const inspectionDate = new Date(formValue.inspectionDate);
    const EndDate = new Date(formValue.endDate);
    const startNewDate = new Date(formValue.inspectionStart);
    const startHours = startNewDate.getHours();
    const startMinutes = startNewDate.getMinutes();
    const inspectionStart = this.convertStart(inspectionDate, startHours, startMinutes);
    const endNewDate = new Date(formValue.inspectionEnd);
    const endHours = endNewDate.getHours();
    const endMinutes = endNewDate.getMinutes();
    const startPicker = moment(formValue.inspectionStart).format('HH:mm');
    const endPicker = moment(formValue.inspectionEnd).format('HH:mm');
    const inspectionEnd = formValue.recurrence !== 'Does Not Repeat'
      ? this.convertStart(EndDate, endHours, endMinutes)
      : this.convertStart(inspectionDate, endHours, endMinutes);
    if (
      newTimeZoneDetails !== null
      && newTimeZoneDetails.length > 0
      && formValue.TimeZoneId.length > 0
    ) {
      formValue.TimeZoneId.forEach((element: { id: any }): void => {
        this.timeZoneValues = element.id;
      });
    }
    if (this.deliverDetailsForm.get('isAssociatedWithCraneRequest').value) {
      if (
        !this.deliverDetailsForm.get('cranePickUpLocation').value
        || !this.deliverDetailsForm.get('craneDropOffLocation').value
      ) {
        this.formSubmitted = false;
        this.toastr.error('Please enter Picking From and Picking To');
        return;
      }
    }
    const payload = this.saveTemplatePayload({
      formValue,
      companies,
      persons,
      define,
      equipments,
      inspectionStart,
      inspectionEnd,
      escortCondition,
    });

    if (payload.recurrence) {
      if (startPicker === endPicker) {
        this.toastr.error('inspection Start time and End time should not be the same');
        this.formSubmitted = false;
        this.submitted = false;
        return;
      }
      if (startPicker > endPicker) {
        this.toastr.error('Please enter From Time lesser than To Time');
        this.formSubmitted = false;
        this.submitted = false;
        return;
      }
      if (
        payload.recurrence !== 'Does Not Repeat'
        && !moment(inspectionEnd).isAfter(moment(inspectionStart))
      ) {
        this.toastr.error('Please enter End Date greater than Start Date');
        this.formSubmitted = false;
        this.submitted = false;
        return;
      }
    }
    this.bookingTemplatesService.saveTemplate(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success('Booking Template Saved Successfully', 'Success');
          this.mixpanelService.addMixpanelEvents('Created inspection Booking Template');
          this.formReset();
          this.deliverDetailsForm.reset();
          this.resetForm('yes');
          this.NDRTimingChanged = false;
        }
      },
      error: (NDRCreateHistoryError): void => {
        this.formReset();
        this.NDRTimingChanged = false;
        this.handleTemplateError(NDRCreateHistoryError);
      },
    });
  }

  public handleTemplateError(NDRCreateHistoryError) {
    if (NDRCreateHistoryError.message?.statusCode === 400) {
      this.showError(NDRCreateHistoryError);
    } else if (!NDRCreateHistoryError.message) {
      this.toastr.error('Try again later.!', 'Something went wrong.');
    } else if (NDRCreateHistoryError.message.includes('overlaps')) {
      this.toastr.error(NDRCreateHistoryError.message, 'OOPS!');
    } else {
      this.toastr.error(NDRCreateHistoryError.message.split(': ')[1], 'OOPS!');
    }
  }

  public template(object): void {
    const getSelected = this.templateList.find((obj: any): any => +obj.id === +object.id);
    this.selectedTemplate = getSelected;
    this.disableSaveAsTemplate = true;
    this.setBookingTemplateData(this.selectedTemplate);
  }

  public getTemplates(): void {
    this.loader = true;
    this.templateDropdownSettings = {
      singleSelection: true,
      idField: 'id',
      textField: 'template_name',
      allowSearchFilter: true,
      closeDropDownOnSelection: true,
    };
    if (this.ProjectId) {
      const params = {
        ParentCompanyId: this.ParentCompanyId,
        isDropdown: true,
      };
      const queryPath = {
        ProjectId: this.ProjectId,
        isDropdown: true,
      };
      this.bookingTemplatesService.getTemplates(queryPath, params).subscribe((res): void => {
        this.templateList = res.data.rows;
        this.loader = false;
      });
    }
  }

  public setBookingTemplateData(data): void {
    this.loader = true;
    this.deliverDetailsForm.get('description').setValue(this.selectedTemplate.description);
    this.deliverDetailsForm
      .get('inspectionDate')
      .setValue(moment(this.selectedTemplate.date).format('MM/DD/YYYY'));
    this.deliverDetailsForm
      .get('inspectionStart')
      .setValue(new Date(this.selectedTemplate.from_time));
    this.deliverDetailsForm.get('inspectionEnd').setValue(new Date(this.selectedTemplate.to_time));
    this.deliverDetailsForm.get('escort').setValue(this.selectedTemplate.is_escort_needed);
    this.deliverDetailsForm.get('notes').setValue(this.selectedTemplate.notes);
    this.deliverDetailsForm.get('GateId').setValue(this.selectedTemplate.gate);
    this.deliverDetailsForm
      .get('EquipmentId')
      .setValue(JSON.parse(this.selectedTemplate.equipment));
    const { isAssociatedWithCraneRequest } = this.selectedTemplate;
    if (isAssociatedWithCraneRequest) {
      this.deliverDetailsForm
        .get('isAssociatedWithCraneRequest')
        .setValue(this.selectedTemplate.isAssociatedWithCraneRequest);
    } else {
      this.deliverDetailsForm.get('isAssociatedWithCraneRequest').setValue(false);
    }
    this.deliverDetailsForm
      .get('cranePickUpLocation')
      .setValue(this.selectedTemplate.picking_from);
    this.deliverDetailsForm
      .get('craneDropOffLocation')
      .setValue(this.selectedTemplate.picking_to);
    if (this.deliverDetailsForm.get('isAssociatedWithCraneRequest').value) {
      this.craneEquipmentTypeChosen = true;
    }
    this.setlocation();
    this.setCompany();
    this.setEquipment();
    this.setDefine();
    this.setMember();
    this.setTimeZone();
    this.setRecurrence();
  }

  public setCompany(): void {
    const companyDetails = JSON.parse(this.selectedTemplate.responsible_company);
    const selectedCompanies = this.companyList.filter((company): any => companyDetails.includes(company.id));
    this.deliverDetailsForm.get('companyItems').patchValue(selectedCompanies);
  }

  public setMember(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
        const memberDetails = JSON.parse(this.selectedTemplate.responsible_person);
        const selectedMembers = this.memberList.filter((member): any => memberDetails.includes(member.id));
        const newMemberList = [];
        selectedMembers.forEach(
          (element: any): void => {
            let email: string;
            if (element?.User?.firstName != null && element?.isGuestUser === false) {
              email = `${element?.User?.firstName} ${element?.User?.lastName} (${element?.User?.email})`;
            } else if (element?.User?.firstName != null && element?.isGuestUser === true) {
              email = `${element?.User?.firstName} ${element?.User?.lastName} (${element?.User?.email} - Guest)`;
            } else if (element?.User?.firstName == null && element?.isGuestUser === false) {
              email = `(${element?.User?.email})`;
            } else if (element?.User?.firstName == null && element?.isGuestUser === true) {
              email = `(${element?.User?.email} - Guest)`;
            }
            const data: any = {
              email,
              id: element?.id,
            };
            if (element?.User?.email === this.authUser?.User?.email) {
              data.readonly = true;
            }
            if (element?.User?.email) {
              newMemberList.push(data);
            }
          },
        );
        this.deliverDetailsForm.get('person').patchValue(newMemberList);
      }
    });
  }

  public setEquipment(): void {
    const equipmentDetails = JSON.parse(this.selectedTemplate.equipment);
    const selectedEquipment = this.equipmentList.filter((equipment): any => equipmentDetails.includes(equipment.id));
    this.deliverDetailsForm.get('EquipmentId').patchValue(selectedEquipment);
  }

  public setDefine(): void {
    const defineWorkDetails = JSON.parse(this.selectedTemplate.dfow);
    const selectedDfow = this.defineList.filter((dfow): any => defineWorkDetails.includes(dfow.id));
    this.deliverDetailsForm.get('defineItems').patchValue(selectedDfow);
  }

  public setlocation(): void {
    const { location } = this.selectedTemplate;
    const selectedLocation = this.locationList.filter((loc): any => location === loc.id);
    this.deliverDetailsForm.get('LocationId').patchValue(selectedLocation);
  }

  public setTimeZone(): void {
    this.getSelectedTimeZone = this.timezoneList.filter(
      (obj: any): any => +obj.id === +this.selectedTemplate.time_zone,
    );
    this.deliverDetailsForm.get('TimeZoneId').patchValue(this.getSelectedTimeZone);
  }

  public setRecurrence(): void {
    if (this.selectedTemplate.recurrence) {
      const recurrenceDetails = JSON.parse(this.selectedTemplate.recurrence);
      this.deliverDetailsForm.get('recurrence').setValue(recurrenceDetails?.recurrence);
      this.deliverDetailsForm.get('dateOfMonth').setValue(recurrenceDetails?.dateOfMonth);
      this.deliverDetailsForm.get('monthlyRepeatType').setValue(recurrenceDetails?.monthlyRepeatType);
      this.deliverDetailsForm.get('repeatEveryCount').setValue(recurrenceDetails?.repeatEveryCount);
      this.deliverDetailsForm.get('repeatEveryType').setValue(recurrenceDetails?.repeatEveryType);
      if (recurrenceDetails.chosenDateOfMonth) {
        this.deliverDetailsForm.get('chosenDateOfMonth').setValue(1);
      } else {
        this.showMonthlyRecurrence();
        if (this.deliverDetailsForm.get('monthlyRepeatType').value === this.monthlyDayOfWeek) {
          this.deliverDetailsForm.get('chosenDateOfMonth').setValue(2);
        }
        if (this.deliverDetailsForm.get('monthlyRepeatType').value === this.monthlyLastDayOfWeek) {
          this.deliverDetailsForm.get('chosenDateOfMonth').setValue(3);
        }
      }
      this.changeRecurrenceCount(+recurrenceDetails.repeatEveryCount);
      this.deliverDetailsForm
        .get('endDate')
        .setValue(
          moment(recurrenceDetails?.endDate).format('MM/DD/YYYY'),
        );
      this.chooseRepeatEveryType(recurrenceDetails.repeatEveryType, recurrenceDetails);
    }
  }
}
