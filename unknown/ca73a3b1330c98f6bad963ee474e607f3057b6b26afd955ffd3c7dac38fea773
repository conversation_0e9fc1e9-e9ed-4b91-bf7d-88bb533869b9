import { of, Subscription, throwError, BehaviorSubject } from 'rxjs';
import { ComponentFixture } from '@angular/core/testing';
import { UntypedFormBuilder } from '@angular/forms';
import { Router } from '@angular/router';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Socket } from 'ngx-socket-io';
import { ToastrService } from 'ngx-toastr';
import { TemplateRef } from '@angular/core';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';
import { MixpanelService } from '../../services/mixpanel.service';
import { ConcreteDetailHeaderComponent } from './concrete-detail-header.component';

describe('ConcreteDetailHeaderComponent', () => {
  let component: ConcreteDetailHeaderComponent;
  let fixture: ComponentFixture<ConcreteDetailHeaderComponent>;
  let modalServiceMock: jest.Mocked<BsModalService>;
  let deliveryServiceMock: jest.Mocked<DeliveryService>;
  let projectServiceMock: jest.Mocked<ProjectService>;
  let socketMock: jest.Mocked<Socket>;
  let routerMock: jest.Mocked<Router>;
  let toastrMock: jest.Mocked<ToastrService>;
  let mixpanelServiceMock: jest.Mocked<MixpanelService>;
  let mockSubscription: Subscription;

  beforeEach(() => {
    mockSubscription = new Subscription();

    modalServiceMock = {
      show: jest.fn(),
    } as any;

    deliveryServiceMock = {
      // Start with undefined to prevent initial getConcreteRequest call
      loginUser: new BehaviorSubject(undefined),
      getCurrentConcreteRequestStatus: new BehaviorSubject(789),
      getConcreteRequestDetail: jest.fn().mockReturnValue(of({
        data: {
          memberDetails: [],
          status: 'Approved',
        },
      })),
      updatedEditConcreteRequestId: jest.fn(),
      showFooterButtons: new BehaviorSubject(true),
      completeConcreteRequestStatus: jest.fn(),
      updateConcreteRequestStatus: jest.fn().mockReturnValue(of({ message: 'Success' })),
      addConcreteRequestToVoid: jest.fn().mockReturnValue(of({ message: 'Success' })),
      updatedHistory: jest.fn(),
      updateConcreteRequestHistory: jest.fn(),
    } as any;

    projectServiceMock = {
      projectParent: new BehaviorSubject({ ProjectId: 123, ParentCompanyId: 456 }),
      getRegisteredMember: jest.fn().mockReturnValue(of({ data: [] })),
      updateAccountProjectParent: jest.fn(),
    } as any;

    socketMock = {
      on: jest.fn(),
      emit: jest.fn(),
    } as any;

    routerMock = {
      events: of({}),
    } as any;

    toastrMock = {
      success: jest.fn(),
      error: jest.fn(),
      clear: jest.fn(),
    } as any;

    mixpanelServiceMock = {
      track: jest.fn(),
      addMixpanelEvents: jest.fn(),
    } as any;

    // Mock the subscription that would be created in the constructor
    jest.spyOn(deliveryServiceMock.getCurrentConcreteRequestStatus, 'subscribe')
      .mockReturnValue(mockSubscription);

    component = new ConcreteDetailHeaderComponent(
      modalServiceMock,
      { hide: jest.fn() } as any,
      deliveryServiceMock,
      socketMock,
      { hide: jest.fn() } as any,
      mixpanelServiceMock,
      new UntypedFormBuilder(),
      routerMock,
      toastrMock,
      projectServiceMock,
    );
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    // Reset values that might have been changed in constructor
    component.show = false;
    component.void = false;
    component.showStatus = false;
    component.currentStatus = '';
    component.currentTabId = 0;

    expect(component.currentTabId).toBe(0);
    expect(component.statusValue).toEqual(['Approved', 'Declined']);
    expect(component.currentStatus).toBe('');
    expect(component.showStatus).toBe(false);
    expect(component.show).toBe(false);
    expect(component.void).toBe(false);
  });

  it('should set status when selectStatus is called', () => {
    component.selectStatus('Approved');
    expect(component.currentStatus).toBe('Approved');
    expect(component.statusChanged).toBe(true);
  });

  it('should handle onSelect for Details tab', () => {
    component.authUser = { RoleId: 4 };
    component.currentDeliverySaveItem = {
      createdUserDetails: { RoleId: 4 },
      status: 'Approved',
    };

    component.onSelect({ heading: 'Details' });
    expect(component.showSaveButton).toBe(true);
  });

  it('should initialize series options correctly', () => {
    component.initializeSeriesOption();
    expect(component.seriesOptions).toEqual([
      {
        option: 1,
        text: 'This event',
        disabled: false,
      },
      {
        option: 2,
        text: 'This and all following events',
        disabled: false,
      },
    ]);
  });

  it('should get concrete request details', () => {
    const mockResponse = {
      data: {
        id: 1,
        status: 'Approved',
        ParentCompanyId: 456,
        ProjectId: 123,
        memberDetails: [],
      },
    };
    deliveryServiceMock.getConcreteRequestDetail.mockReturnValue(of(mockResponse));

    component.getConcreteRequest();
    expect(deliveryServiceMock.getConcreteRequestDetail).toHaveBeenCalled();
    expect(component.currentDeliverySaveItem).toEqual(mockResponse.data);
  });

  it('should handle socket events', () => {
    // Set up required data before ngOnInit
    component.data = {
      ParentCompanyId: 456,
      ProjectId: 123,
      id: 789,
    };

    socketMock.on.mockImplementation((event, callback) => {
      if (event === 'getConcreteCommentHistory') {
        callback({ message: 'Concrete Booking Comment added successfully.' });
      }
    });

    component.ngOnInit();
    expect(component.currentTabId).toBe(2);
  });

  it('should clean up subscriptions on destroy', () => {
    const unsubscribeSpy = jest.spyOn(mockSubscription, 'unsubscribe');
    component.ngOnDestroy();
    expect(unsubscribeSpy).toHaveBeenCalled();
  });

  // ============ POSITIVE TEST CASES ============

  describe('Constructor and Initialization - Positive Cases', () => {
    it('should initialize with loginUser subscription when user is defined', () => {
      const mockUser = { id: 1, RoleId: 2, name: 'Test User' };

      // Create a fresh delivery service mock with the user already set
      const freshDeliveryServiceMock = {
        loginUser: new BehaviorSubject(mockUser),
        getCurrentConcreteRequestStatus: new BehaviorSubject(789),
        getConcreteRequestDetail: jest.fn().mockReturnValue(of({
          data: {
            memberDetails: [],
            status: 'Approved',
          },
        })),
        updatedEditConcreteRequestId: jest.fn(),
        showFooterButtons: new BehaviorSubject(true),
        completeConcreteRequestStatus: jest.fn(),
      } as any;

      const freshProjectServiceMock = {
        projectParent: new BehaviorSubject({ ProjectId: 123, ParentCompanyId: 456 }),
        getRegisteredMember: jest.fn().mockReturnValue(of({ data: [] })),
      } as any;

      // Create new component to test constructor
      const newComponent = new ConcreteDetailHeaderComponent(
        modalServiceMock,
        { hide: jest.fn() } as any,
        freshDeliveryServiceMock,
        socketMock,
        { hide: jest.fn() } as any,
        mixpanelServiceMock,
        new UntypedFormBuilder(),
        routerMock,
        toastrMock,
        freshProjectServiceMock,
      );

      expect(newComponent.authUser).toEqual(mockUser);
      expect(freshDeliveryServiceMock.getConcreteRequestDetail).toHaveBeenCalled();
    });

    it('should handle projectParent subscription correctly', () => {
      const mockProject = { ProjectId: 456, ParentCompanyId: 789 };

      const freshProjectServiceMock = {
        projectParent: new BehaviorSubject(mockProject),
        getRegisteredMember: jest.fn().mockReturnValue(of({ data: [] })),
      } as any;

      const freshDeliveryServiceMock = {
        loginUser: new BehaviorSubject(undefined),
        getCurrentConcreteRequestStatus: new BehaviorSubject(undefined),
        getConcreteRequestDetail: jest.fn().mockReturnValue(of({
          data: {
            memberDetails: [],
            status: 'Approved',
          },
        })),
        updatedEditConcreteRequestId: jest.fn(),
        showFooterButtons: new BehaviorSubject(true),
        completeConcreteRequestStatus: jest.fn(),
      } as any;

      const newComponent = new ConcreteDetailHeaderComponent(
        modalServiceMock,
        { hide: jest.fn() } as any,
        freshDeliveryServiceMock,
        socketMock,
        { hide: jest.fn() } as any,
        mixpanelServiceMock,
        new UntypedFormBuilder(),
        routerMock,
        toastrMock,
        freshProjectServiceMock,
      );

      expect(newComponent.ProjectId).toBe(456);
      expect(newComponent.ParentCompanyId).toBe(789);
    });

    it('should subscribe to getCurrentConcreteRequestStatus', () => {
      const mockStatus = 789;

      const freshDeliveryServiceMock = {
        loginUser: new BehaviorSubject(undefined),
        getCurrentConcreteRequestStatus: new BehaviorSubject(mockStatus),
        getConcreteRequestDetail: jest.fn().mockReturnValue(of({
          data: {
            memberDetails: [],
            status: 'Approved',
          },
        })),
        updatedEditConcreteRequestId: jest.fn(),
        showFooterButtons: new BehaviorSubject(true),
        completeConcreteRequestStatus: jest.fn(),
      } as any;

      const freshProjectServiceMock = {
        projectParent: new BehaviorSubject({ ProjectId: 123, ParentCompanyId: 456 }),
        getRegisteredMember: jest.fn().mockReturnValue(of({ data: [] })),
      } as any;

      const newComponent = new ConcreteDetailHeaderComponent(
        modalServiceMock,
        { hide: jest.fn() } as any,
        freshDeliveryServiceMock,
        socketMock,
        { hide: jest.fn() } as any,
        mixpanelServiceMock,
        new UntypedFormBuilder(),
        routerMock,
        toastrMock,
        freshProjectServiceMock,
      );

      expect(newComponent.ConcreteRequestId).toBe(mockStatus);
    });
  });

  describe('ngOnInit - Positive Cases', () => {
    beforeEach(() => {
      component.data = {
        ParentCompanyId: 456,
        ProjectId: 123,
        id: 789,
      };
    });

    it('should handle getConcreteAttachmentDeleteHistory socket event', () => {
      socketMock.on.mockImplementation((event, callback) => {
        if (event === 'getConcreteAttachmentDeleteHistory') {
          callback({ message: 'Concrete Booking Attachment Deleted Successfully.' });
        }
      });

      component.ngOnInit();
      expect(component.currentTabId).toBe(1);
    });

    it('should set currentTabId to 0 initially', () => {
      component.ngOnInit();
      expect(component.currentTabId).toBe(0);
    });

    it('should subscribe to router events and hide modal', () => {
      const hideSpy = jest.spyOn(component.bsModalRef, 'hide');
      component.ngOnInit();
      expect(hideSpy).toHaveBeenCalled();
    });
  });

  describe('onSelect Method - Positive Cases', () => {
    beforeEach(() => {
      component.currentDeliverySaveItem = {
        createdUserDetails: { RoleId: 4 },
        status: 'Approved',
      };
    });

    it('should show save button for role 4 user with matching role and Details tab', () => {
      component.authUser = { RoleId: 4 };
      component.onSelect({ heading: 'Details' });

      expect(component.currentTabHeading).toBe('Details');
      expect(component.showSaveButton).toBe(true);
    });

    it('should hide save button for role 4 user with non-matching role and Details tab', () => {
      component.authUser = { RoleId: 4 };
      component.currentDeliverySaveItem.createdUserDetails.RoleId = 2;

      component.onSelect({ heading: 'Details' });
      expect(component.showSaveButton).toBe(false);
    });

    it('should hide save button for non-Details tab', () => {
      component.authUser = { RoleId: 4 };
      component.onSelect({ heading: 'Comments' });

      expect(component.currentTabHeading).toBe('Comments');
      expect(component.showSaveButton).toBe(false);
    });

    it('should handle Expired status correctly', () => {
      component.authUser = { RoleId: 4 };
      component.currentDeliverySaveItem.status = 'Expired';

      component.onSelect({ heading: 'Details' });
      expect(component.showSaveButton).toBe(true);
    });
  });

  describe('selectStatus Method - Positive Cases', () => {
    it('should set status to Approved', () => {
      component.selectStatus('Approved');
      expect(component.currentStatus).toBe('Approved');
      expect(component.statusChanged).toBe(true);
    });

    it('should set status to Declined', () => {
      component.selectStatus('Declined');
      expect(component.currentStatus).toBe('Declined');
      expect(component.statusChanged).toBe(true);
    });

    it('should handle custom status values', () => {
      component.selectStatus('Pending');
      expect(component.currentStatus).toBe('Pending');
      expect(component.statusChanged).toBe(true);
    });
  });

  describe('changeRequestCollapse Method - Positive Cases', () => {
    it('should toggle allRequestIsOpened and initialize series options', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);

      const mockData = {
        concretePlacementStart: futureDate.toISOString(),
      };

      component.allRequestIsOpened = false;
      component.changeRequestCollapse(mockData);

      expect(component.allRequestIsOpened).toBe(true);
      expect(component.seriesOptions).toHaveLength(2);
      expect(component.seriesOptions[0].disabled).toBe(false);
      expect(component.seriesOptions[1].disabled).toBe(false);
    });

    it('should disable first option for past dates', () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);

      const mockData = {
        concretePlacementStart: pastDate.toISOString(),
      };

      component.changeRequestCollapse(mockData);

      expect(component.seriesOptions[1].disabled).toBe(true);
    });
  });

  describe('getConcreteRequest Method - Positive Cases', () => {
    beforeEach(() => {
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
      component.ConcreteRequestId = 789;
      component.authUser = { id: 1, RoleId: 2 };
    });

    it('should fetch concrete request details successfully', () => {
      const mockResponse = {
        data: {
          id: 789,
          status: 'Tentative',
          memberDetails: [{ id: 1, name: 'Member 1', Member: { id: 1 } }],
        },
      };
      deliveryServiceMock.getConcreteRequestDetail.mockReturnValue(of(mockResponse));

      component.getConcreteRequest();

      expect(deliveryServiceMock.getConcreteRequestDetail).toHaveBeenCalledWith({
        ConcreteRequestId: 789,
        ParentCompanyId: 456,
        ProjectId: 123,
      });
      expect(component.currentDeliverySaveItem).toEqual({
        ...mockResponse.data,
        edit: true, // For role 2, edit is set to true
      });
      expect(component.void).toBe(false);
      expect(component.show).toBe(true); // show is set to true after checkRoleStatus
    });

    it('should show status dropdown for Tentative status and role 2', () => {
      const mockResponse = {
        data: { status: 'Tentative' },
      };
      deliveryServiceMock.getConcreteRequestDetail.mockReturnValue(of(mockResponse));

      component.getConcreteRequest();
      expect(component.showStatus).toBe(true);
    });

    it('should show save button for role 2 with Approved status and Details tab', () => {
      const mockResponse = {
        data: { status: 'Approved' },
      };
      deliveryServiceMock.getConcreteRequestDetail.mockReturnValue(of(mockResponse));
      component.currentTabHeading = 'Details';

      component.getConcreteRequest();
      expect(component.showSaveButton).toBe(true);
    });

    it('should not call getConcreteRequestDetail when ProjectId or ParentCompanyId is missing', () => {
      component.ProjectId = null;
      component.ParentCompanyId = 456;
      component.ConcreteRequestId = 789;

      component.getConcreteRequest();
      expect(deliveryServiceMock.getConcreteRequestDetail).not.toHaveBeenCalled();
    });
  });

  describe('getMembers Method - Positive Cases', () => {
    beforeEach(() => {
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
    });

    it('should fetch members successfully', () => {
      const mockResponse = {
        data: [
          { id: 1, name: 'Member 1', role: 'Admin' },
          { id: 2, name: 'Member 2', role: 'User' },
        ],
      };
      projectServiceMock.getRegisteredMember.mockReturnValue(of(mockResponse));

      component.getMembers();

      expect(projectServiceMock.getRegisteredMember).toHaveBeenCalledWith({
        ProjectId: 123,
        ParentCompanyId: 456,
      });
      expect(component.memberList).toEqual(mockResponse.data);
    });
  });

  // ============ NEGATIVE TEST CASES ============

  describe('Constructor - Negative Cases', () => {
    it('should handle undefined loginUser gracefully', () => {
      deliveryServiceMock.loginUser.next(undefined);
      const getConcreteRequestSpy = jest.spyOn(component, 'getConcreteRequest');

      const newComponent = new ConcreteDetailHeaderComponent(
        modalServiceMock,
        {} as BsModalRef,
        deliveryServiceMock,
        socketMock,
        {} as BsModalRef,
        mixpanelServiceMock,
        new UntypedFormBuilder(),
        routerMock,
        toastrMock,
        projectServiceMock,
      );

      expect(getConcreteRequestSpy).not.toHaveBeenCalled();
    });

    it('should handle null loginUser gracefully', () => {
      deliveryServiceMock.loginUser.next(null);
      const getConcreteRequestSpy = jest.spyOn(component, 'getConcreteRequest');

      const newComponent = new ConcreteDetailHeaderComponent(
        modalServiceMock,
        {} as BsModalRef,
        deliveryServiceMock,
        socketMock,
        {} as BsModalRef,
        mixpanelServiceMock,
        new UntypedFormBuilder(),
        routerMock,
        toastrMock,
        projectServiceMock,
      );

      expect(getConcreteRequestSpy).not.toHaveBeenCalled();
    });

    it('should handle empty string loginUser gracefully', () => {
      deliveryServiceMock.loginUser.next('');
      const getConcreteRequestSpy = jest.spyOn(component, 'getConcreteRequest');

      const newComponent = new ConcreteDetailHeaderComponent(
        modalServiceMock,
        {} as BsModalRef,
        deliveryServiceMock,
        socketMock,
        {} as BsModalRef,
        mixpanelServiceMock,
        new UntypedFormBuilder(),
        routerMock,
        toastrMock,
        projectServiceMock,
      );

      expect(getConcreteRequestSpy).not.toHaveBeenCalled();
    });
  });

  describe('onSelect Method - Negative Cases', () => {
    it('should handle missing currentDeliverySaveItem gracefully', () => {
      component.authUser = { RoleId: 4 };
      component.currentDeliverySaveItem = null;

      expect(() => {
        component.onSelect({ heading: 'Details' });
      }).not.toThrow();
      expect(component.currentTabHeading).toBe('Details');
    });

    it('should handle missing createdUserDetails gracefully', () => {
      component.authUser = { RoleId: 4 };
      component.currentDeliverySaveItem = {
        status: 'Approved',
      };

      expect(() => {
        component.onSelect({ heading: 'Details' });
      }).not.toThrow();
      expect(component.showSaveButton).toBe(false);
    });

    it('should handle invalid role values', () => {
      component.authUser = { RoleId: 999 };
      component.currentDeliverySaveItem = {
        createdUserDetails: { RoleId: 4 },
        status: 'Approved',
      };

      component.onSelect({ heading: 'Details' });
      expect(component.showSaveButton).toBe(true); // For non-role 4 users with Details tab and Approved status
    });

    it('should handle missing authUser', () => {
      component.authUser = null;
      component.currentDeliverySaveItem = {
        createdUserDetails: { RoleId: 4 },
        status: 'Approved',
      };

      expect(() => {
        component.onSelect({ heading: 'Details' });
      }).not.toThrow();
    });
  });

  describe('changeRequestCollapse Method - Negative Cases', () => {
    it('should handle missing concretePlacementStart gracefully', () => {
      const mockData = {};

      expect(() => {
        component.changeRequestCollapse(mockData);
      }).not.toThrow();
      expect(component.allRequestIsOpened).toBe(true);
    });

    it('should handle invalid date format', () => {
      const mockData = {
        concretePlacementStart: 'invalid-date',
      };

      expect(() => {
        component.changeRequestCollapse(mockData);
      }).not.toThrow();
    });

    it('should handle null data parameter', () => {
      expect(() => {
        component.changeRequestCollapse(null);
      }).not.toThrow();
    });
  });

  describe('getConcreteRequest Method - Negative Cases', () => {
    beforeEach(() => {
      component.authUser = { id: 1, RoleId: 2 };
    });

    it('should handle API error gracefully', () => {
      const errorResponse = { error: 'Network error', status: 500 };
      deliveryServiceMock.getConcreteRequestDetail.mockReturnValue(throwError(errorResponse));
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
      component.ConcreteRequestId = 789;

      expect(() => {
        component.getConcreteRequest();
      }).not.toThrow();
    });

    it('should not make API call when ProjectId is missing', () => {
      component.ProjectId = null;
      component.ParentCompanyId = 456;
      component.ConcreteRequestId = 789;

      component.getConcreteRequest();
      expect(deliveryServiceMock.getConcreteRequestDetail).not.toHaveBeenCalled();
    });

    it('should not make API call when ParentCompanyId is missing', () => {
      component.ProjectId = 123;
      component.ParentCompanyId = null;
      component.ConcreteRequestId = 789;

      component.getConcreteRequest();
      expect(deliveryServiceMock.getConcreteRequestDetail).not.toHaveBeenCalled();
    });

    it('should handle empty response data', () => {
      const mockResponse = { data: null };
      deliveryServiceMock.getConcreteRequestDetail.mockReturnValue(of(mockResponse));
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
      component.ConcreteRequestId = 789;

      component.getConcreteRequest();
      expect(component.currentDeliverySaveItem).toEqual({ edit: false });
    });
  });

  describe('getMembers Method - Negative Cases', () => {
    beforeEach(() => {
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
    });

    it('should handle API error gracefully', () => {
      const errorResponse = { error: 'Network error', status: 500 };
      projectServiceMock.getRegisteredMember.mockReturnValue(throwError(errorResponse));

      expect(() => {
        component.getMembers();
      }).not.toThrow();
    });

    it('should handle empty response', () => {
      const mockResponse = { data: null };
      projectServiceMock.getRegisteredMember.mockReturnValue(of(mockResponse));

      component.getMembers();
      expect(component.memberList).toBeNull();
    });

    it('should handle undefined response', () => {
      projectServiceMock.getRegisteredMember.mockReturnValue(of(undefined));

      component.getMembers();
      // memberList should remain unchanged
    });
  });

  describe('selectStatus Method - Negative Cases', () => {
    it('should handle null status gracefully', () => {
      expect(() => {
        component.selectStatus(null);
      }).not.toThrow();
      expect(component.currentStatus).toBeNull();
      expect(component.statusChanged).toBe(true);
    });

    it('should handle undefined status gracefully', () => {
      expect(() => {
        component.selectStatus(undefined);
      }).not.toThrow();
      expect(component.currentStatus).toBeUndefined();
      expect(component.statusChanged).toBe(true);
    });

    it('should handle empty string status', () => {
      component.selectStatus('');
      expect(component.currentStatus).toBe('');
      expect(component.statusChanged).toBe(true);
    });
  });

  describe('Socket Events - Negative Cases', () => {
    beforeEach(() => {
      component.data = {
        ParentCompanyId: 456,
        ProjectId: 123,
        id: 789,
      };
    });

    it('should handle socket event with wrong message', () => {
      socketMock.on.mockImplementation((event, callback) => {
        if (event === 'getConcreteCommentHistory') {
          callback({ message: 'Wrong message' });
        }
      });

      const initialTabId = component.currentTabId;
      component.ngOnInit();
      expect(component.currentTabId).toBe(0); // Should remain 0, not change to 2
    });

    it('should handle socket event with missing message', () => {
      socketMock.on.mockImplementation((event, callback) => {
        if (event === 'getConcreteCommentHistory') {
          callback({});
        }
      });

      component.ngOnInit();
      expect(component.currentTabId).toBe(0);
    });

    it('should handle socket event with null response', () => {
      socketMock.on.mockImplementation((event, callback) => {
        if (event === 'getConcreteCommentHistory') {
          callback(null);
        }
      });

      expect(() => {
        component.ngOnInit();
      }).not.toThrow();
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle missing data input gracefully', () => {
      component.data = null;
      expect(() => {
        component.ngOnInit();
      }).not.toThrow();
    });

    it('should handle undefined data input gracefully', () => {
      component.data = undefined;
      expect(() => {
        component.ngOnInit();
      }).not.toThrow();
    });

    it('should handle partial data input', () => {
      component.data = { ProjectId: 123 }; // Missing ParentCompanyId and id
      expect(() => {
        component.ngOnInit();
      }).not.toThrow();
    });

    it('should handle ngOnDestroy when subscription is null', () => {
      // Create component without subscription
      const newComponent = new ConcreteDetailHeaderComponent(
        modalServiceMock,
        {} as BsModalRef,
        deliveryServiceMock,
        socketMock,
        {} as BsModalRef,
        mixpanelServiceMock,
        new UntypedFormBuilder(),
        routerMock,
        toastrMock,
        projectServiceMock,
      );

      // Manually set subscription to null
      (newComponent as any).concreteRequestSubscription = null;

      expect(() => {
        newComponent.ngOnDestroy();
      }).not.toThrow();
    });

    it('should handle initializeSeriesOption multiple calls', () => {
      component.initializeSeriesOption();
      const firstCall = [...component.seriesOptions];

      component.initializeSeriesOption();
      const secondCall = [...component.seriesOptions];

      expect(firstCall).toEqual(secondCall);
      expect(component.seriesOptions).toHaveLength(2);
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete workflow with valid data', () => {
      // Setup
      component.authUser = { id: 1, RoleId: 2 };
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
      component.ConcreteRequestId = 789;

      const mockResponse = {
        data: {
          id: 789,
          status: 'Tentative',
          memberDetails: [],
        },
      };
      deliveryServiceMock.getConcreteRequestDetail.mockReturnValue(of(mockResponse));

      // Execute
      component.getConcreteRequest();
      component.selectStatus('Approved');
      component.onSelect({ heading: 'Details' });

      // Verify
      expect(component.currentDeliverySaveItem.status).toBe('Tentative');
      expect(component.currentStatus).toBe('Approved');
      expect(component.statusChanged).toBe(true);
      expect(component.currentTabHeading).toBe('Details');
    });

    it('should handle workflow with missing permissions', () => {
      // Setup user without proper role
      component.authUser = { id: 1, RoleId: 5 };
      component.currentDeliverySaveItem = {
        createdUserDetails: { RoleId: 2 },
        status: 'Approved',
      };

      // Execute
      component.onSelect({ heading: 'Details' });

      // Verify permissions are respected - for non-role 4 users with Details tab and Approved status, showSaveButton is true
      expect(component.showSaveButton).toBe(true);
    });
  });

  // ============ ADDITIONAL COMPREHENSIVE TEST CASES ============

  describe('setStatus Method - Comprehensive Tests', () => {
    it('should set status correctly with valid data', () => {
      const mockData = {
        ParentCompanyId: 456,
        ProjectId: 123,
        id: 789,
      };
      component.data = mockData;
      const updateSpy = jest.spyOn(deliveryServiceMock, 'updatedEditConcreteRequestId');
      const getRequestSpy = jest.spyOn(component, 'getConcreteRequest');

      component.setStatus();

      expect(component.ParentCompanyId).toBe(456);
      expect(component.ProjectId).toBe(123);
      expect(component.ConcreteRequestId).toBe(789);
      expect(updateSpy).toHaveBeenCalledWith(789);
      expect(getRequestSpy).toHaveBeenCalled();
    });

    it('should handle ConcreteRequestId of -1', () => {
      const mockData = {
        ParentCompanyId: 456,
        ProjectId: 123,
        id: -1,
      };
      component.data = mockData;
      const updateSpy = jest.spyOn(deliveryServiceMock, 'updatedEditConcreteRequestId');
      const getRequestSpy = jest.spyOn(component, 'getConcreteRequest');

      component.setStatus();

      expect(component.ConcreteRequestId).toBe(-1);
      expect(updateSpy).not.toHaveBeenCalled();
      expect(getRequestSpy).not.toHaveBeenCalled();
    });

    it('should handle undefined ConcreteRequestId', () => {
      const mockData = {
        ParentCompanyId: 456,
        ProjectId: 123,
        id: undefined,
      };
      component.data = mockData;
      const updateSpy = jest.spyOn(deliveryServiceMock, 'updatedEditConcreteRequestId');
      const getRequestSpy = jest.spyOn(component, 'getConcreteRequest');

      component.setStatus();

      expect(component.ConcreteRequestId).toBeUndefined();
      expect(updateSpy).not.toHaveBeenCalled();
      expect(getRequestSpy).not.toHaveBeenCalled();
    });

    it('should handle null data gracefully', () => {
      component.data = null;

      expect(() => {
        component.setStatus();
      }).not.toThrow();
    });
  });

  describe('checkRoleStatus Method - Comprehensive Tests', () => {
    beforeEach(() => {
      component.authUser = { id: 1, RoleId: 4 };
    });

    it('should set edit to true for role 4 user found in memberDetails', () => {
      const mockResponse = {
        data: {
          memberDetails: [
            { Member: { id: 1 } },
            { Member: { id: 2 } },
          ],
        },
      };

      component.checkRoleStatus(mockResponse, 1);

      expect(component.currentDeliverySaveItem.edit).toBe(true);
      expect(component.show).toBe(true);
    });

    it('should set edit to false for role 4 user not found in memberDetails', () => {
      const mockResponse = {
        data: {
          memberDetails: [
            { Member: { id: 2 } },
            { Member: { id: 3 } },
          ],
        },
      };

      component.checkRoleStatus(mockResponse, 1);

      expect(component.currentDeliverySaveItem.edit).toBe(false);
      expect(component.show).toBe(true);
    });

    it('should set edit to true for role 3 user found in memberDetails', () => {
      component.authUser.RoleId = 3;
      const mockResponse = {
        data: {
          memberDetails: [
            { Member: { id: 1 } },
          ],
        },
      };

      component.checkRoleStatus(mockResponse, 1);

      expect(component.currentDeliverySaveItem.edit).toBe(true);
    });

    it('should set edit to true for role 2 user (non-role 3/4)', () => {
      component.authUser.RoleId = 2;
      const mockResponse = {
        data: {
          memberDetails: [],
        },
      };

      component.checkRoleStatus(mockResponse, 1);

      expect(component.currentDeliverySaveItem.edit).toBe(true);
    });

    it('should set void to true when user is in voidList', () => {
      const mockResponse = {
        data: {
          memberDetails: [],
          voidList: [
            { MemberId: 1 },
            { MemberId: 2 },
          ],
        },
      };
      component.currentDeliverySaveItem = { voidList: mockResponse.data.voidList };

      component.checkRoleStatus(mockResponse, 1);

      expect(component.void).toBe(true);
    });

    it('should not set void when user is not in voidList', () => {
      const mockResponse = {
        data: {
          memberDetails: [],
          voidList: [
            { MemberId: 2 },
            { MemberId: 3 },
          ],
        },
      };
      component.currentDeliverySaveItem = { voidList: mockResponse.data.voidList };

      component.checkRoleStatus(mockResponse, 1);

      expect(component.void).toBe(false);
    });

    it('should handle missing voidList gracefully', () => {
      const mockResponse = {
        data: {
          memberDetails: [],
        },
      };
      component.currentDeliverySaveItem = {};

      expect(() => {
        component.checkRoleStatus(mockResponse, 1);
      }).not.toThrow();
      expect(component.void).toBe(false);
    });
  });

  describe('checkRole4 Method - Comprehensive Tests', () => {
    beforeEach(() => {
      component.authUser = { UserId: 1, RoleId: 4 };
      component.currentTabHeading = 'Details';
    });

    it('should show save button for matching user and role with Approved status', () => {
      const mockItem = {
        createdUserDetails: {
          User: { id: 1 },
          RoleId: 4,
        },
      };
      component.currentDeliverySaveItem = { status: 'Approved' };

      component.checkRole4(mockItem);

      expect(component.showSaveButton).toBe(true);
    });

    it('should hide save button for matching user but different role', () => {
      const mockItem = {
        createdUserDetails: {
          User: { id: 1 },
          RoleId: 2,
        },
      };
      component.currentDeliverySaveItem = { status: 'Approved', createdUserDetails: { RoleId: 2 } };

      component.checkRole4(mockItem);

      expect(component.showSaveButton).toBe(false);
    });

    it('should handle different user ID', () => {
      const mockItem = {
        createdUserDetails: {
          User: { id: 2 },
          RoleId: 4,
        },
      };
      component.currentDeliverySaveItem = { status: 'Approved' };

      component.checkRole4(mockItem);

      expect(component.showSaveButton).toBe(false);
    });
  });

  describe('closeModal Method - Tests', () => {
    it('should hide modal and reset properties', () => {
      const hideSpy = jest.spyOn(component.modalRef, 'hide');
      component.currentTabHeading = 'Details';
      component.showSaveButton = true;

      component.closeModal();

      expect(hideSpy).toHaveBeenCalled();
      expect(component.currentTabHeading).toBe('');
      expect(component.showSaveButton).toBe(false);
    });
  });

  describe('openEditModal Method - Tests', () => {
    it('should open edit modal with recurrence data', () => {
      const mockItem = {
        recurrence: {
          id: 123,
          recurrence: 'Weekly',
          recurrenceEndDate: '2024-12-31',
        },
      };
      const hideSpy = jest.spyOn(component.modalRef, 'hide');
      const showSpy = jest.spyOn(modalServiceMock, 'show').mockReturnValue({
        content: {},
        hide: jest.fn(),
      } as any);
      const updateSpy = jest.spyOn(deliveryServiceMock, 'updatedEditConcreteRequestId');

      component.ConcreteRequestId = 789;
      component.openEditModal(mockItem, 2);

      expect(hideSpy).toHaveBeenCalled();
      expect(showSpy).toHaveBeenCalled();
      expect(updateSpy).toHaveBeenCalledWith(789);
    });

    it('should open edit modal without recurrence data', () => {
      const mockItem = {};
      const showSpy = jest.spyOn(modalServiceMock, 'show').mockReturnValue({
        content: {},
        hide: jest.fn(),
      } as any);

      component.openEditModal(mockItem, 1);

      expect(showSpy).toHaveBeenCalled();
    });
  });

  describe('clickAndDisable Method - Tests', () => {
    it('should disable click events on link', () => {
      const mockLink = {
        onclick: null,
      };
      const mockEvent = {
        preventDefault: jest.fn(),
      };

      component.clickAndDisable(mockLink);

      // Simulate the onclick being called
      mockLink.onclick(mockEvent);

      expect(mockEvent.preventDefault).toHaveBeenCalled();
    });
  });

  describe('Modal Methods - Tests', () => {
    it('should open modal with correct configuration', () => {
      const mockTemplate = {} as TemplateRef<any>;
      const showSpy = jest.spyOn(modalServiceMock, 'show').mockReturnValue({} as any);

      component.openModal(mockTemplate);

      expect(showSpy).toHaveBeenCalledWith(mockTemplate, {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      });
    });

    it('should open modal1 with correct configuration', () => {
      const mockTemplate = {} as TemplateRef<any>;
      const showSpy = jest.spyOn(modalServiceMock, 'show').mockReturnValue({} as any);

      component.openModal1(mockTemplate);

      expect(showSpy).toHaveBeenCalledWith(mockTemplate, {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      });
    });

    it('should open modal2 with correct configuration', () => {
      const mockTemplate = {} as TemplateRef<any>;
      const showSpy = jest.spyOn(modalServiceMock, 'show').mockReturnValue({} as any);

      component.openModal2(mockTemplate);

      expect(showSpy).toHaveBeenCalledWith(mockTemplate, {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      });
    });

    it('should open modal3 and hide modal1', () => {
      const mockTemplate = {} as TemplateRef<any>;
      component.modalRef1 = { hide: jest.fn() } as any;
      const showSpy = jest.spyOn(modalServiceMock, 'show').mockReturnValue({} as any);
      const hideSpy = jest.spyOn(component.modalRef1, 'hide');

      component.openModal3(mockTemplate);

      expect(showSpy).toHaveBeenCalledWith(mockTemplate, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-md thanks-popup custom-modal',
      });
      expect(hideSpy).toHaveBeenCalled();
    });
  });

  describe('ngAfterViewInit Method - Tests', () => {
    it('should subscribe to showFooterButtons and set showButtons to true', () => {
      deliveryServiceMock.showFooterButtons.next(true);

      component.ngAfterViewInit();

      expect(component.showButtons).toBe(true);
    });

    it('should subscribe to showFooterButtons and set showButtons to false', () => {
      deliveryServiceMock.showFooterButtons.next(false);

      component.ngAfterViewInit();

      expect(component.showButtons).toBe(false);
    });
  });

  describe('otherDetailsForm Method - Tests', () => {
    it('should initialize form with empty values', () => {
      component.otherDetailsForm();

      expect(component.otherForm).toBeDefined();
      expect(component.otherForm.get('hoursToCompletePlacement')?.value).toBe('');
      expect(component.otherForm.get('minutesToCompletePlacement')?.value).toBe('');
      expect(component.otherForm.get('cubicYardsTotal')?.value).toBe('');
    });
  });

  describe('handleDownKeydown Method - Tests', () => {
    it('should call openEditModal on Enter key for edit type', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      const mockData = { id: 1 };
      const mockItem = { option: 1 };

      // Mock the modalService.show to return a proper modal reference
      modalServiceMock.show.mockReturnValue({
        content: {
          closeBtnName: '',
          seriesOption: 1,
          recurrenceId: null,
          recurrenceEndDate: null,
        },
        hide: jest.fn(),
      } as any);

      const openEditModalSpy = jest.spyOn(component, 'openEditModal');

      component.handleDownKeydown(mockEvent, mockData, mockItem, 'edit');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(openEditModalSpy).toHaveBeenCalledWith(mockData, mockItem);
    });

    it('should call savestatus1 on Space key for role type', () => {
      const mockEvent = { key: ' ', preventDefault: jest.fn() } as any;
      component.currentStatus = 'Approved'; // Set a status to avoid error
      component.modalRef = { hide: jest.fn() } as any;
      component.currentDeliverySaveItem = {
        id: 123,
        memberDetails: [{ Member: { id: 1 } }],
      };
      component.memberList = [{ id: 1 }];
      component.ParentCompanyId = 456;
      const savestatus1Spy = jest.spyOn(component, 'savestatus1');

      component.handleDownKeydown(mockEvent, {}, {}, 'role');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(savestatus1Spy).toHaveBeenCalled();
    });

    it('should not call any method for unknown type', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      const openEditModalSpy = jest.spyOn(component, 'openEditModal');
      const savestatus1Spy = jest.spyOn(component, 'savestatus1');

      component.handleDownKeydown(mockEvent, {}, {}, 'unknown');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(openEditModalSpy).not.toHaveBeenCalled();
      expect(savestatus1Spy).not.toHaveBeenCalled();
    });

    it('should not call any method for non-Enter/Space keys', () => {
      const mockEvent = { key: 'Tab', preventDefault: jest.fn() } as any;
      const openEditModalSpy = jest.spyOn(component, 'openEditModal');
      const savestatus1Spy = jest.spyOn(component, 'savestatus1');

      component.handleDownKeydown(mockEvent, {}, {}, 'edit');

      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      expect(openEditModalSpy).not.toHaveBeenCalled();
      expect(savestatus1Spy).not.toHaveBeenCalled();
    });
  });

  describe('revertstatus Method - Tests', () => {
    it('should open modal for role 1 user', () => {
      const mockTemplate = {} as TemplateRef<any>;
      component.authUser = { RoleId: 1 };
      const showSpy = jest.spyOn(modalServiceMock, 'show').mockReturnValue({} as any);

      component.revertstatus(mockTemplate);

      expect(showSpy).toHaveBeenCalledWith(mockTemplate, {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      });
    });

    it('should open modal for role 2 user', () => {
      const mockTemplate = {} as TemplateRef<any>;
      component.authUser = { RoleId: 2 };
      const showSpy = jest.spyOn(modalServiceMock, 'show').mockReturnValue({} as any);

      component.revertstatus(mockTemplate);

      expect(showSpy).toHaveBeenCalledWith(mockTemplate, {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      });
    });

    it('should not open modal for other roles', () => {
      const mockTemplate = {} as TemplateRef<any>;
      component.authUser = { RoleId: 3 };
      const showSpy = jest.spyOn(modalServiceMock, 'show');

      component.revertstatus(mockTemplate);

      expect(showSpy).not.toHaveBeenCalled();
    });
  });

  describe('statusupdate Method - Tests', () => {
    beforeEach(() => {
      component.currentDeliverySaveItem = { id: 123 };
      component.ParentCompanyId = 456;
      component.modalRef2 = { hide: jest.fn() } as any;
    });

    it('should hide modal when action is no', () => {
      const hideSpy = jest.spyOn(component.modalRef2, 'hide');

      component.statusupdate('no');

      expect(hideSpy).toHaveBeenCalled();
    });

    it('should update status to Approved when action is yes', () => {
      const mockResponse = { message: 'Status updated successfully' };
      deliveryServiceMock.updateConcreteRequestStatus.mockReturnValue(of(mockResponse));
      const hideSpy = jest.spyOn(component.modalRef2, 'hide');
      const toastrSpy = jest.spyOn(toastrMock, 'success');
      const socketSpy = jest.spyOn(socketMock, 'emit');
      const updateHistorySpy = jest.spyOn(deliveryServiceMock, 'updateConcreteRequestHistory');
      const completeStatusSpy = jest.spyOn(deliveryServiceMock, 'completeConcreteRequestStatus');

      component.statusupdate('yes');

      expect(hideSpy).toHaveBeenCalled();
      expect(deliveryServiceMock.updateConcreteRequestStatus).toHaveBeenCalledWith({
        id: 123,
        status: 'Approved',
        ParentCompanyId: 456,
        statuschange: 'Reverted',
      });
      expect(toastrSpy).toHaveBeenCalledWith('Status Reverted Successfully', 'Success');
      expect(socketSpy).toHaveBeenCalledWith('ConcreteApproveHistory', mockResponse);
      expect(updateHistorySpy).toHaveBeenCalledWith({ status: true }, 'ConcreteApproveHistory');
      expect(completeStatusSpy).toHaveBeenCalledWith(false);
      expect(component.showStatus).toBe(false);
      expect(component.statusSubmitted).toBe(false);
      expect(component.statusChanged).toBe(false);
    });
  });

  describe('voidConfirmationResponse Method - Tests', () => {
    beforeEach(() => {
      component.modalRef2 = { hide: jest.fn() } as any;
    });

    it('should hide modal when action is no', () => {
      const hideSpy = jest.spyOn(component.modalRef2, 'hide');

      component.voidConfirmationResponse('no');

      expect(hideSpy).toHaveBeenCalled();
    });

    it('should call addToVoid when action is yes', () => {
      const hideSpy = jest.spyOn(component.modalRef2, 'hide');
      const addToVoidSpy = jest.spyOn(component, 'addToVoid');

      component.voidConfirmationResponse('yes');

      expect(hideSpy).toHaveBeenCalled();
      expect(addToVoidSpy).toHaveBeenCalled();
    });
  });

  describe('showError Method - Tests', () => {
    it('should display error message from error details', () => {
      const mockError = {
        message: {
          details: [
            { field: 'Error message here' }
          ]
        }
      };
      const toastrSpy = jest.spyOn(toastrMock, 'error');

      component.showError(mockError);

      expect(toastrSpy).toHaveBeenCalledWith(['Error message here']);
    });
  });

  describe('addToVoid Method - Tests', () => {
    beforeEach(() => {
      component.currentDeliverySaveItem = { id: 123 };
      component.ProjectId = 456;
      component.ParentCompanyId = 789;
      component.bsModalRef = { hide: jest.fn() } as any;
    });

    it('should add concrete request to void successfully', () => {
      const mockResponse = { message: 'Added to void successfully' };
      deliveryServiceMock.addConcreteRequestToVoid.mockReturnValue(of(mockResponse));
      const updateProjectSpy = jest.spyOn(projectServiceMock, 'updateAccountProjectParent');
      const toastrSpy = jest.spyOn(toastrMock, 'success');
      const updateHistorySpy = jest.spyOn(deliveryServiceMock, 'updatedHistory');
      const updateConcreteHistorySpy = jest.spyOn(deliveryServiceMock, 'updateConcreteRequestHistory');
      const mixpanelSpy = jest.spyOn(mixpanelServiceMock, 'addMixpanelEvents');
      const hideSpy = jest.spyOn(component.bsModalRef, 'hide');

      component.addToVoid();

      expect(updateProjectSpy).toHaveBeenCalledWith({ ProjectId: 456, ParentCompanyId: 789 });
      expect(deliveryServiceMock.addConcreteRequestToVoid).toHaveBeenCalledWith({
        ConcreteRequestId: 123,
        ProjectId: 456,
        ParentCompanyId: 789,
      });
      expect(toastrSpy).toHaveBeenCalledWith('Added to void successfully', 'Success');
      expect(updateHistorySpy).toHaveBeenCalledWith({ status: true }, 'AddedToVoid');
      expect(updateConcreteHistorySpy).toHaveBeenCalledWith({ status: true }, 'AddedToVoid');
      expect(mixpanelSpy).toHaveBeenCalledWith('Concrete Booking Voided');
      expect(hideSpy).toHaveBeenCalled();
      expect(component.voidSubmitted).toBe(false);
    });

    it('should handle error when adding to void', () => {
      const mockError = { message: 'Error occurred' };
      deliveryServiceMock.addConcreteRequestToVoid.mockReturnValue(throwError(mockError));
      const toastrSpy = jest.spyOn(toastrMock, 'error');

      component.addToVoid();

      expect(toastrSpy).toHaveBeenCalledWith('Error occurred', 'OOPS!');
      expect(component.voidSubmitted).toBe(false);
    });

    it('should handle error with status code 400', () => {
      const mockError = {
        message: {
          statusCode: 400,
          details: [{ field: 'Validation error' }]
        }
      };
      deliveryServiceMock.addConcreteRequestToVoid.mockReturnValue(throwError(mockError));
      const showErrorSpy = jest.spyOn(component, 'showError');

      component.addToVoid();

      expect(showErrorSpy).toHaveBeenCalledWith(mockError);
      expect(component.voidSubmitted).toBe(false);
    });

    it('should not call API when already submitted', () => {
      component.voidSubmitted = true;

      component.addToVoid();

      expect(deliveryServiceMock.addConcreteRequestToVoid).not.toHaveBeenCalled();
    });
  });

  describe('gatestatus Method - Tests', () => {
    beforeEach(() => {
      component.currentDeliverySaveItem = { id: 123 };
      component.ParentCompanyId = 456;
      component.modalRef = { hide: jest.fn() } as any;
      component.modalRef1 = { hide: jest.fn() } as any;
      component.otherForm = new UntypedFormBuilder().group({
        hoursToCompletePlacement: ['2'],
        minutesToCompletePlacement: ['30'],
        cubicYardsTotal: ['100'],
      });
    });

    it('should hide modal when action is no', () => {
      const hideSpy = jest.spyOn(component.modalRef, 'hide');
      const hideModalSpy = jest.spyOn(component.bsModalRef, 'hide');
      const showSpy = jest.spyOn(modalServiceMock, 'show').mockReturnValue({
        content: { closeBtnName: '' },
      } as any);

      component.gatestatus('no');

      expect(hideSpy).toHaveBeenCalled();
      expect(hideModalSpy).toHaveBeenCalled();
      expect(showSpy).toHaveBeenCalled();
    });

    it('should update status to Completed when action is yes', () => {
      const mockResponse = { message: 'Status updated successfully' };
      deliveryServiceMock.updateConcreteRequestStatus.mockReturnValue(of(mockResponse));
      const toastrSpy = jest.spyOn(toastrMock, 'success');
      const socketSpy = jest.spyOn(socketMock, 'emit');
      const updateHistorySpy = jest.spyOn(deliveryServiceMock, 'updateConcreteRequestHistory');
      const completeStatusSpy = jest.spyOn(deliveryServiceMock, 'completeConcreteRequestStatus');
      const mixpanelSpy = jest.spyOn(mixpanelServiceMock, 'addMixpanelEvents');
      const hideSpy = jest.spyOn(component.modalRef, 'hide');
      const hideModal1Spy = jest.spyOn(component.modalRef1, 'hide');

      component.gatestatus('yes');


      expect(deliveryServiceMock.updateConcreteRequestStatus).toHaveBeenCalledWith({
        id: 123,
        status: 'Completed',
        ParentCompanyId: 456,
        hoursToCompletePlacement: '2',
        minutesToCompletePlacement: '30',
        cubicYardsTotal: '100',
      });
      expect(toastrSpy).toHaveBeenCalledWith('Status updated successfully', 'Success');
      expect(socketSpy).toHaveBeenCalledWith('ConcreteApproveHistory', mockResponse);
      expect(updateHistorySpy).toHaveBeenCalledWith({ status: true }, 'ConcreteApproveHistory');
      expect(completeStatusSpy).toHaveBeenCalledWith(false);
      expect(mixpanelSpy).toHaveBeenCalledWith('Completed Concrete Booking');
      expect(hideSpy).toHaveBeenCalled();
      expect(hideModal1Spy).toHaveBeenCalled();
      expect(component.gatesubmit).toBe(false);
      expect(component.statusSubmitted).toBe(false);
      expect(component.skipped).toBe(false);
      expect(component.currentDeliverySaveItem.status).toBe('Completed');
      expect(component.statusChanged).toBe(false);
      expect(component.showButtons).toBe(false);
      expect(component.showSaveButton).toBe(false);
    });

    it('should handle error with status code 400', () => {
      const mockError = {
        message: {
          statusCode: 400,
          details: [{ field: 'Validation error' }]
        }
      };
      deliveryServiceMock.updateConcreteRequestStatus.mockReturnValue(throwError(mockError));
      const showErrorSpy = jest.spyOn(component, 'showError');
      const completeStatusSpy = jest.spyOn(deliveryServiceMock, 'completeConcreteRequestStatus');

      component.gatestatus('yes');

      expect(showErrorSpy).toHaveBeenCalledWith(mockError);
      expect(completeStatusSpy).toHaveBeenCalledWith(false);
      expect(component.statusSubmitted).toBe(false);
      expect(component.skipped).toBe(false);
      expect(component.statusChanged).toBe(false);
      expect(component.showButtons).toBe(false);
      expect(component.showSaveButton).toBe(false);
    });

    it('should handle generic error', () => {
      const mockError = { message: 'Generic error' };
      deliveryServiceMock.updateConcreteRequestStatus.mockReturnValue(throwError(mockError));
      const toastrSpy = jest.spyOn(toastrMock, 'error');
      const hideSpy = jest.spyOn(component.modalRef, 'hide');
      const hideModal1Spy = jest.spyOn(component.modalRef1, 'hide');

      component.gatestatus('yes');

      expect(toastrSpy).toHaveBeenCalledWith('Generic error', 'OOPS!');
      expect(hideSpy).toHaveBeenCalled();
      expect(hideModal1Spy).toHaveBeenCalled();
    });
  });

  describe('savestatus1 Method - Tests', () => {
    beforeEach(() => {
      component.currentDeliverySaveItem = {
        id: 123,
        memberDetails: [
          { Member: { id: 1 } },
          { Member: { id: 2 } }
        ]
      };
      component.ParentCompanyId = 456;
      component.memberList = [
        { id: 1 },
        { id: 2 }
      ];
      component.modalRef = { hide: jest.fn() } as any;
      component.personModal = {} as TemplateRef<any>;
    });

    it('should show error when no status is chosen', () => {
      component.currentStatus = '';
      const toastrSpy = jest.spyOn(toastrMock, 'error');
      const clearSpy = jest.spyOn(toastrMock, 'clear');
      const hideSpy = jest.spyOn(component.modalRef, 'hide');

      component.savestatus1();

      expect(clearSpy).toHaveBeenCalled();
      expect(toastrSpy).toHaveBeenCalledWith('No status chosen to save');
      expect(hideSpy).toHaveBeenCalled();
    });

    it('should update status successfully when all members are registered', () => {
      component.currentStatus = 'Approved';
      const mockResponse = { message: 'Status updated successfully' };
      deliveryServiceMock.updateConcreteRequestStatus.mockReturnValue(of(mockResponse));
      const toastrSpy = jest.spyOn(toastrMock, 'success');
      const socketSpy = jest.spyOn(socketMock, 'emit');
      const updateHistorySpy = jest.spyOn(deliveryServiceMock, 'updateConcreteRequestHistory');
      const completeStatusSpy = jest.spyOn(deliveryServiceMock, 'completeConcreteRequestStatus');
      const mixpanelSpy = jest.spyOn(mixpanelServiceMock, 'addMixpanelEvents');

      component.savestatus1();


      expect(deliveryServiceMock.updateConcreteRequestStatus).toHaveBeenCalledWith({
        id: 123,
        status: 'Approved',
        ParentCompanyId: 456,
      });
      expect(toastrSpy).toHaveBeenCalledWith('Status updated successfully', 'Success');
      expect(socketSpy).toHaveBeenCalledWith('ConcreteApproveHistory', mockResponse);
      expect(updateHistorySpy).toHaveBeenCalledWith({ status: true }, 'ConcreteApproveHistory');
      expect(completeStatusSpy).toHaveBeenCalledWith(false);
      expect(mixpanelSpy).toHaveBeenCalledWith('Approved Concrete Booking');
      expect(component.showStatus).toBe(false);
      expect(component.statusSubmitted).toBe(false);
      expect(component.currentDeliverySaveItem.status).toBe('Approved');
      expect(component.statusChanged).toBe(false);
    });

    it('should open person confirmation modal when not all members are registered', () => {
      component.currentStatus = 'Approved';
      component.memberList = [{ id: 1 }]; // Missing member with id 2
      component.modalRef1 = { hide: jest.fn() } as any;
      modalServiceMock.show.mockReturnValue({
        content: {},
        hide: jest.fn(),
      } as any);
      const openModal3Spy = jest.spyOn(component, 'openModal3');

      component.savestatus1();

      expect(openModal3Spy).toHaveBeenCalledWith(component.personModal);
    });

    it('should not submit when already submitted', () => {
      component.statusSubmitted = true;
      component.currentStatus = 'Approved';

      component.savestatus1();

      expect(deliveryServiceMock.updateConcreteRequestStatus).not.toHaveBeenCalled();
    });

    it('should handle error in savestatus1', () => {
      component.currentStatus = 'Approved';
      const mockError = { message: { statusCode: 400, details: [{ field: 'Error' }] } };
      deliveryServiceMock.updateConcreteRequestStatus.mockReturnValue(throwError(() => mockError));
      const showErrorSpy = jest.spyOn(component, 'showError');

      component.savestatus1();

      expect(showErrorSpy).toHaveBeenCalledWith(mockError);
      expect(component.statusSubmitted).toBe(false);
      expect(component.statusChanged).toBe(false);
    });
  });

  // ============ ADDITIONAL COMPREHENSIVE TEST CASES FOR 90% COVERAGE ============

  describe('saveStatus Method - Comprehensive Tests', () => {
    beforeEach(() => {
      component.currentDeliverySaveItem = {
        id: 123,
        memberDetails: [{ Member: { id: 1 } }],
      };
      component.memberList = [{ id: 1 }];
      component.ParentCompanyId = 456;
      component.modalRef = { hide: jest.fn() } as any;
      component.modalRef1 = { hide: jest.fn() } as any;
      component.otherForm = new UntypedFormBuilder().group({
        hoursToCompletePlacement: ['2'],
        minutesToCompletePlacement: ['30'],
        cubicYardsTotal: ['100'],
      });
    });

    it('should handle skip action successfully', () => {
      const mockResponse = { message: 'Status updated successfully' };
      deliveryServiceMock.updateConcreteRequestStatus.mockReturnValue(of(mockResponse));
      const toastrSpy = jest.spyOn(toastrMock, 'success');
      const socketSpy = jest.spyOn(socketMock, 'emit');
      const updateHistorySpy = jest.spyOn(deliveryServiceMock, 'updateConcreteRequestHistory');
      const completeStatusSpy = jest.spyOn(deliveryServiceMock, 'completeConcreteRequestStatus');
      const mixpanelSpy = jest.spyOn(mixpanelServiceMock, 'addMixpanelEvents');

      component.saveStatus('skip');


      expect(deliveryServiceMock.updateConcreteRequestStatus).toHaveBeenCalledWith({
        id: 123,
        status: 'Completed',
        ParentCompanyId: 456,
        hoursToCompletePlacement: '2',
        minutesToCompletePlacement: '30',
        cubicYardsTotal: '100',
      });
      expect(toastrSpy).toHaveBeenCalledWith('Status updated successfully', 'Success');
      expect(socketSpy).toHaveBeenCalledWith('ConcreteApproveHistory', mockResponse);
      expect(updateHistorySpy).toHaveBeenCalledWith({ status: true }, 'ConcreteApproveHistory');
      expect(completeStatusSpy).toHaveBeenCalledWith(false);
      expect(mixpanelSpy).toHaveBeenCalledWith('Completed Concrete Booking');
      expect(component.statusSubmitted).toBe(false);
      expect(component.skipped).toBe(false);
      expect(component.statusChanged).toBe(false);
    });

    it('should handle submit action successfully', () => {
      const mockResponse = { message: 'Status updated successfully' };
      deliveryServiceMock.updateConcreteRequestStatus.mockReturnValue(of(mockResponse));

      component.saveStatus('submit');


      expect(deliveryServiceMock.updateConcreteRequestStatus).toHaveBeenCalled();
    });

    it('should open person modal when not all members are registered', () => {
      component.memberList = [{ id: 1 }];
      component.currentDeliverySaveItem.memberDetails = [
        { Member: { id: 1 } },
        { Member: { id: 2 } },
      ];
      modalServiceMock.show.mockReturnValue({
        content: {},
        hide: jest.fn(),
      } as any);
      component.modalRef1 = { hide: jest.fn() } as any;
      const openModal3Spy = jest.spyOn(component, 'openModal3');

      component.saveStatus('submit');

      expect(openModal3Spy).toHaveBeenCalled();
    });

    it('should handle error in saveStatus', () => {
      const mockError = { message: { statusCode: 400, details: [{ field: 'Error' }] } };
      deliveryServiceMock.updateConcreteRequestStatus.mockReturnValue(throwError(() => mockError));
      const showErrorSpy = jest.spyOn(component, 'showError');

      component.saveStatus('submit');

      expect(showErrorSpy).toHaveBeenCalledWith(mockError);
      expect(component.statusSubmitted).toBe(false);
      expect(component.skipped).toBe(false);
      expect(component.statusChanged).toBe(false);
    });

    it('should not submit when already submitted or skipped', () => {
      component.statusSubmitted = true;
      component.skipped = true;

      component.saveStatus('submit');

      expect(deliveryServiceMock.updateConcreteRequestStatus).not.toHaveBeenCalled();
    });
  });

  describe('Socket Event Tests - Additional Coverage', () => {
    beforeEach(() => {
      component.data = {
        ParentCompanyId: 456,
        ProjectId: 123,
        id: 789,
      };
    });

    it('should handle getConcreteApproveHistory socket event', () => {
      socketMock.on.mockImplementation((event, callback) => {
        if (event === 'getConcreteApproveHistory') {
          callback({ message: 'Uploaded Successfully.' });
        }
      });

      component.ngOnInit();
      expect(component.currentTabId).toBe(1);
    });

    it('should not change tab for unrecognized socket messages', () => {
      socketMock.on.mockImplementation((event, callback) => {
        if (event === 'getConcreteApproveHistory') {
          callback({ message: 'Unknown message' });
        }
      });

      component.ngOnInit();
      expect(component.currentTabId).toBe(0);
    });
  });

  describe('Additional Method Coverage Tests', () => {
    it('should handle checkRole4 with valid data and different user', () => {
      component.authUser = { UserId: 1, RoleId: 4 };
      component.currentTabHeading = 'Details';
      component.currentDeliverySaveItem = { status: 'Approved' };

      const mockItem = {
        createdUserDetails: {
          User: { id: 2 }, // Different user ID
          RoleId: 4,
        },
      };

      component.checkRole4(mockItem);
      expect(component.showSaveButton).toBe(false);
    });

    it('should handle checkRole4 with matching user but different role', () => {
      component.authUser = { UserId: 1, RoleId: 4 };
      component.currentTabHeading = 'Details';
      component.currentDeliverySaveItem = {
        status: 'Approved',
        createdUserDetails: { RoleId: 2 }
      };

      const mockItem = {
        createdUserDetails: {
          User: { id: 1 }, // Same user ID
          RoleId: 2, // Different role
        },
      };

      component.checkRole4(mockItem);
      expect(component.showSaveButton).toBe(false);
    });

    it('should handle getConcreteRequest with role 3 user', () => {
      component.authUser = { id: 1, RoleId: 3 };
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
      component.ConcreteRequestId = 789;
      component.currentTabHeading = 'Details';

      const mockResponse = {
        data: {
          id: 789,
          status: 'Approved',
          memberDetails: [{ Member: { id: 1 } }],
        },
      };
      deliveryServiceMock.getConcreteRequestDetail.mockReturnValue(of(mockResponse));

      component.getConcreteRequest();

      expect(component.showSaveButton).toBe(true);
    });

    it('should handle openEditModal with modalRef already existing', () => {
      component.modalRef = { hide: jest.fn() } as any;
      modalServiceMock.show.mockReturnValue({
        content: {
          closeBtnName: '',
          seriesOption: 1,
          recurrenceId: null,
          recurrenceEndDate: null,
        },
        hide: jest.fn(),
      } as any);

      const mockItem = {
        recurrence: {
          id: 123,
          recurrence: 'Weekly',
          recurrenceEndDate: '2024-12-31',
        },
      };

      const hideSpy = jest.spyOn(component.modalRef, 'hide');
      component.ConcreteRequestId = 789;

      component.openEditModal(mockItem, 2);

      expect(hideSpy).toHaveBeenCalled();
      expect(modalServiceMock.show).toHaveBeenCalled();
    });
  });
});
