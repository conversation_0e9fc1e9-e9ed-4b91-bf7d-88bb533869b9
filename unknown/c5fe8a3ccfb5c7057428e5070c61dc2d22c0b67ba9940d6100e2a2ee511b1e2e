import {
  Component, OnInit, TemplateRef, ViewChild, Input,
} from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { Socket } from 'ngx-socket-io';
import moment from 'moment';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';
import { EditinspectionFormComponent } from '../edit-inspection-form/edit-inspection-form.component';

@Component({
  selector: 'app-inspection-details-new',
  templateUrl: './inspection-details-new.component.html',
})
export class InspectionDetailsNewComponent implements OnInit {
  @Input() data: any;

  @Input() title: string;

  public showStatus = false;

  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public modalRef2: BsModalRef;

  public modalRef3: BsModalRef;

  public inspectionRequestId: number;

  public currentinspectionSaveItem: any = {};

  public currentStatus = '';

  public currentInspectionStatus = '';

  public statusSubmitted = false;

  public inspectionStatusChanged : boolean = false;

  public voidSubmitted = false;

  public inspectionStatusValue : any;

  public currentInspectionStatusValue : any;

  public isInspectionStatusUpdated : boolean = false;

  public inspectionStatus : any;

  public ProjectId: any;

  public void = false;

  public authUser: any = {};

  public statusValue: any = [];

  public ParentCompanyId: any;

  public myAccount = false;

  public accountAdmin = false;

  public show = false;

  public currentTabId: number = 0;

  public statusChanged: boolean = false;

  public isQueuedNDR = false;

  public gateList: any = [];

  public equipmentList: any = [];

  public gatesubmit = false;

  public memberList: any = [];

  public textvalue: any;

  public allRequestIsOpened = false;

  public noEquipmentOption = { id: 0, equipmentName: 'No Equipment Needed' };


  @ViewChild('GateConfirmationPopup') public gateModal: TemplateRef<any>;

  @ViewChild('equipmentConfirmationPopup') public equipmentModal: TemplateRef<any>;

  @ViewChild('personConfirmationPopup') public personModal: TemplateRef<any>;

  @ViewChild('overallConfirmationPopup') public overallmodal: TemplateRef<any>;

  public seriesOptions = [];

  public constructor(
    private readonly modalService: BsModalService,
    public bsModalRef: BsModalRef,
    public DeliveryService: DeliveryService,
    public socket: Socket,
    public router: Router,
    public toastr: ToastrService,
    public projectService: ProjectService,
  ) {
    this.DeliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
        if (this.authUser.RoleId === 2 || this.authUser.RoleId === 1) {
          this.statusValue = ['Approved', 'Declined'];
          this.inspectionStatusValue = ['Pass' , 'Fail'];
        } else if (this.authUser.RoleId === 3) {
          this.statusValue = ['Delivered'];
        }
      }
    });
    this.projectService.projectParent.subscribe((response20): void => {
      if (response20 !== undefined && response20 !== null && response20 !== '') {
        this.ProjectId = response20.ProjectId;
        this.ParentCompanyId = response20.ParentCompanyId;
        this.getOverAllGate();
      }
    });
    this.DeliveryService.getInspectionCurrentStatus.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.inspectionRequestId = res;
        this.getNDR();
      }
    });
    this.DeliveryService.inspectionUpdated.subscribe((getNdrResponse3): void => {
      this.currentTabId = 0;

      if (getNdrResponse3 !== undefined && getNdrResponse3 !== null && getNdrResponse3 !== '') {
        this.getNDR();
      }
    });
    this.projectService.isMyAccount.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.myAccount = true;
      }
    });
    this.projectService.isProject.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.myAccount = false;
        this.accountAdmin = false;
      }
    });
    this.getMembers();
    this.getOverAllEquipmentforEditNdr();
  }

  public selectInspectionStatus(status: string): void {
    this.currentInspectionStatus = status;
    this.inspectionStatusChanged = true;
  }

  public selectStatus(status: string): void {
    this.currentStatus = status;
    this.statusChanged = true;
  }

  public eventCheck(event: { target: { checked: any } }): void {
    if (event.target.checked) {
      this.currentStatus = 'Delivered';
      this.statusChanged = true;
    } else {
      this.currentStatus = '';
    }
  }

  public clickAndDisable(link: any): void {
    // disable subsequent clicks
    const clickDisable = link;
    clickDisable.onclick = (event: { preventDefault: () => void }): void => {
      event.preventDefault();
    };
  }

  public changeRequestCollapse(data): void {
    this.initializeSeriesOption();
    if (!moment(data.inspectionStart).isAfter(moment())) {
      this.seriesOptions = this.seriesOptions.filter((object): any => {
        const seriesObject = object;
        if (seriesObject.option !== 1) {
          seriesObject.disabled = true;
        }
        return seriesObject;
      });
    }
    this.allRequestIsOpened = !this.allRequestIsOpened;
  }

  public getOverAllGate(): void {
    const params = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .gateList(params, { isFilter: true, showActivatedAlone: true })
      .subscribe((res): void => {
        this.gateList = res.data;
      });
  }

  public getOverAllEquipmentforEditNdr(): void {
    const editNdrGetEquipmentParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listEquipment(editNdrGetEquipmentParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((editNdrEquipmentResponse): void => {
        this.equipmentList = [this.noEquipmentOption, ...editNdrEquipmentResponse.data];
      });
  }

  public openModal2(template: TemplateRef<any>): void {
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-md thanks-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }

  public saveStatus(): void {
    if (!this.currentStatus) {
      this.toastr.clear();
      this.toastr.error('No status chosen to save');
      this.modalRef.hide();
      return;
    }
    if (!this.statusSubmitted) {
      this.statusSubmitted = true;
      if (this.statusValue.length === 1) {
        if (this.modalRef) {
          this.modalRef.hide();
        }
      }
      const data = {
        id: this.currentinspectionSaveItem.id,
        status: this.currentStatus,
        ParentCompanyId: this.ParentCompanyId,
      };
      const gateid = this.currentinspectionSaveItem.gateDetails[0]?.Gate?.id;
      const equipmentid = this.currentinspectionSaveItem.equipmentDetails[0]?.Equipment?.id;
      const index = this.gateList.findIndex((i): boolean => i.id === gateid);
      const index1 = this.equipmentList.findIndex((i): boolean => i.id === equipmentid);
      const arrytest = [];
      for (const member of this.currentinspectionSaveItem.memberDetails) {
        arrytest.push(member.Member);
      }
      this.openModalIndex(data, index, index1, arrytest);
    }
  }

  public openModalIndex(data, index, index1, arrytest) {
    const arr1 = this.memberList;
    const arr2 = arrytest;
    let index2: number;
    const result = arr1.filter((o) => arr2.some(({ id }) => o.id === id));
    if (arrytest.length !== result.length) {
      index2 = -1;
    } else {
      index2 = 0;
    }
    if (index === -1 && index1 === -1 && index2 === -1) {
      this.openModal2(this.overallmodal);
      this.textvalue = 'gate,equipment,member';
    } else if (index === -1 && index1 === -1) {
      this.openModal2(this.overallmodal);
      this.textvalue = 'gate,equipment';
    } else if (index1 === -1 && index2 === -1) {
      this.openModal2(this.overallmodal);
      this.textvalue = 'equipment,member';
    } else if (index2 === -1 && index === -1) {
      this.openModal2(this.overallmodal);
      this.textvalue = 'member,gate';
    } else if (index === -1) {
      this.openModal2(this.gateModal);
    } else if (index1 === -1) {
      this.openModal2(this.equipmentModal);
    } else if (index2 === -1) {
      this.openModal2(this.personModal);
    } else {
      this.DeliveryService.updateInspectionStatus(data).subscribe({
        next: (updateStatusResponse2: any): void => {
          if (updateStatusResponse2) {
            this.toastr.success(updateStatusResponse2.message, 'Success');
            this.socket.emit('InspectionNDRApproveHistory', updateStatusResponse2);
            this.DeliveryService.updatedInspectionHistory({ status: true }, 'NDRApproveHistory');
            this.statusSubmitted = false;
            const updateStatusResponse2Condition = data.status !== 'Expired'
            && data.status !== 'Delivered'
            && data.status !== 'Declined';
            if (
              (this.authUser.RoleId === 2 && updateStatusResponse2Condition) ||
              (this.authUser.RoleId === 3 && data.status === 'Approved')
            ) {
              this.showStatus = true;
            } else {
              this.showStatus = false;
            }
            this.currentinspectionSaveItem.status = this.currentStatus;
            this.statusChanged = false;
            this.inspectionStatusChanged = false;

          }
        },
        error: (newNDRCreateErr): void => {
          this.statusSubmitted = false;
          this.statusChanged = false;
          if (newNDRCreateErr.message?.statusCode === 400) {
            this.showError(newNDRCreateErr);
          } else if (!newNDRCreateErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(newNDRCreateErr.message, 'OOPS!');
            this.modalRef.hide();
          }
        },
      });
    }
  }

  public updateInspectionStatus(): void {
    this.gatesubmit = true;
    if (!this.statusSubmitted) {
      this.statusSubmitted = true;
      if (this.statusValue.length === 1) {
        if (this.modalRef) {
          this.modalRef.hide();
        }
      }
      const data = {
        id: this.currentinspectionSaveItem.id,
        status: this.currentinspectionSaveItem.status,
        inspectionStatus: this.currentInspectionStatus,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.DeliveryService.updateInspectionStatus(data).subscribe({
        next: (updateStatusResponse2: any): void => {
          if (updateStatusResponse2) {
            this.gatesubmit = false;
            this.toastr.success(updateStatusResponse2.message, 'Success');
            this.socket.emit('InspectionNDRApproveHistory', updateStatusResponse2);
            this.DeliveryService.updatedInspectionHistory({ status: true }, 'NDRApproveHistory');
            this.statusSubmitted = false;
            const updateStatusResponse2Condition =
            data.inspectionStatus !== 'Pass' &&
            data.inspectionStatus !== 'Fail'
            if (
              (this.authUser.RoleId === 2 && updateStatusResponse2Condition) ||
              (this.authUser.RoleId === 3 && data.inspectionStatus === 'Pass')
            ) {
              this.showStatus = true;
            } else {
              this.showStatus = false;
            }
          }
        },
        error: (newNDRCreateErr): void => {
          this.statusSubmitted = false;
          this.statusChanged = false;
          if (newNDRCreateErr.message?.statusCode === 400) {
            this.showError(newNDRCreateErr);
          } else if (!newNDRCreateErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(newNDRCreateErr.message, 'OOPS!');
            this.modalRef.hide();
          }
        },
      });
    }
  }

  public gatestatus(action: string): void {
    this.gatesubmit = false;
    if (action === 'yes') {
      this.gatesubmit = true;
      const data = {
        id: this.currentinspectionSaveItem.id,
        status: this.currentStatus,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.DeliveryService.updateInspectionStatus(data).subscribe({
        next: (updateStatusResponse2: any): void => {
          if (updateStatusResponse2) {
            this.gatesubmit = false;
            this.modalRef.hide();
            this.toastr.success(updateStatusResponse2.message, 'Success');
            this.socket.emit('InspectionNDRApproveHistory', updateStatusResponse2);
            this.DeliveryService.updatedInspectionHistory({ status: true }, 'NDRApproveHistory');
            this.DeliveryService.updateCraneRequestHistory({ status: true }, 'historyUpdate');
            this.statusSubmitted = false;
            const updateStatusResponse2Condition =
            data.status !== 'Expired' &&
            data.status !== 'Delivered' &&
            data.status !== 'Declined';
            if (
              (this.authUser.RoleId === 2 && updateStatusResponse2Condition) ||
              (this.authUser.RoleId === 3 && data.status === 'Approved')
            ) {
              this.showStatus = true;
            } else {
              this.showStatus = false;
            }
            this.currentinspectionSaveItem.status = this.currentStatus;
            this.statusChanged = false;
          }
        },
        error: (newNDRCreateErr): void => {
          this.statusSubmitted = false;
          this.statusChanged = false;
          if (newNDRCreateErr.message?.statusCode === 400) {
            this.showError(newNDRCreateErr);
          } else if (!newNDRCreateErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(newNDRCreateErr.message, 'OOPS!');
            this.modalRef.hide();
          }
        },
      });
    } else {
      if (this.modalRef) {
        this.modalRef.hide();
      }
      this.bsModalRef.hide();
      const className = 'modal-lg new-inspection-popup custom-modal';
      this.modalRef3 = this.modalService.show(EditinspectionFormComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
      });
      this.modalRef3.content.closeBtnName = 'Close';
      this.modalRef.content.seriesOption = 1;
      this.modalRef.content.recurrenceId = null;
      this.modalRef.content.recurrenceEndDate = null;
    }
  }

  public ngOnInit(): void {
    this.projectService.projectParent.subscribe((response20): void => {
      if (response20 !== undefined && response20 !== null && response20 !== '') {
        this.ProjectId = response20.ProjectId;
        this.ParentCompanyId = response20.ParentCompanyId;
      }
    });
    this.setStatus();
    this.currentTabId = 0;
    this.router.events.subscribe((e): void => {
      this.bsModalRef.hide();
    });
    this.socket.on('getInspectionCommentHistory', (commenthistoryresponse: any): void => {
      if (commenthistoryresponse.message) {
        this.currentTabId = 2;
      }
    });
    this.socket.on('getInspectionAttachmentHistory', (attachmentdeletehistoryresponse: any): void => {
      if (attachmentdeletehistoryresponse.message) {
        this.currentTabId = 1;
      }
    });
    this.socket.on('getNDRApproveHistory', (NDRapprovehistoryresponse: any): void => {
      if (NDRapprovehistoryresponse.message) {
        this.currentTabId = 1;
      }
    });
    this.DeliveryService.isQueuedInspectionNDR.subscribe((res): void => {
      if (res === 'queued') {
        this.isQueuedNDR = true;
      }
    });
  }

  public navigateToAttachment(): void {
    this.currentTabId = 1;
    this.modalRef.hide();
  }

  public setStatus(): void {
    const inspectionData = this.data;
    this.ParentCompanyId = inspectionData.ParentCompanyId;
    this.inspectionRequestId = inspectionData.id;
    if (this.inspectionRequestId !== -1 && this.inspectionRequestId !== undefined) {
      this.DeliveryService.updatedInspectionId(this.inspectionRequestId);
      this.getNDR();
    }
  }

  public getNDR(): void {
    const param = {
      inspectionRequestId: this.inspectionRequestId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.void = false;
    this.show = false;
    this.currentTabId = 0;
    this.DeliveryService.getInspectionNDRData(param).subscribe((res): void => {
      this.currentinspectionSaveItem = res.data;
      this.currentinspectionSaveItem.edit = false;
      if (this.authUser.RoleId === 4 || this.authUser.RoleId === 3) {
        const newMember = res.data.memberDetails;
        const index = newMember.findIndex(
          (i: { Member: { id: any } }): boolean => i.Member.id === this.authUser.id,
        );
        if (index !== -1) {
          this.currentinspectionSaveItem.edit = true;
        } else {
          this.currentinspectionSaveItem.edit = false;
        }
      } else {
        this.currentinspectionSaveItem.edit = true;
      }
      const item = this.currentinspectionSaveItem;
      const authId = this.authUser.id;
      const voidData = this.currentinspectionSaveItem.voidList;
      if (item.inspectionStatus) {
        this.currentInspectionStatusValue = item.inspectionStatus;
      }
      const voidIndex = voidData.findIndex(
        (i: { MemberId: any }): boolean => i.MemberId === authId,
      );
      if (voidIndex !== -1) {
        this.void = true;
      }
      this.show = true;
      this.checkRoleData(item);
    });
  }

  public checkRoleData(item) {
    const condition2 = this.authUser.RoleId === 3 && item.status === 'Approved';
    const condition1 = item.status === 'Pending' && (this.authUser.RoleId === 2 || this.authUser.RoleId === 1);
    if (condition1 || condition2) {
      this.showStatus = true;
    }
    if (
      item.createdUserDetails.RoleId === 4
        && (item.status === 'Approved' || item.status === 'Expired')
    ) {
      const loggedInuserId = this.authUser.UserId;
      const NDRCreatedUserId = item.createdUserDetails.User.id;
      if (
        this.authUser.RoleId === 2
        || this.authUser.RoleId === 3
          || +loggedInuserId === +NDRCreatedUserId
      ) {
        this.showStatus = true;
        this.statusValue = ['Delivered'];
      }
    } else if (
      (item.createdUserDetails.RoleId === 2 || item.createdUserDetails.RoleId === 3)
      && (item.status === 'Approved' || item.status === 'Expired')
    ) {
      if (this.authUser.RoleId === 2 || this.authUser.RoleId === 3) {
        this.showStatus = true;
        this.statusValue = ['Delivered'];
      }
    }
  }

  public openEditModal(item, action): void {
    if (this.bsModalRef) {
      this.bsModalRef.hide();
    }
    this.DeliveryService.updatedInspectionId(item.id);
    if (this.ProjectId) {
      const data = { ProjectId: this.ProjectId, ParentCompanyId: this.ParentCompanyId };
      this.projectService.updateAccountProjectParent(data);
    }
    const className = 'modal-lg new-inspection-popup custom-modal';
    this.modalRef = this.modalService.show(EditinspectionFormComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
      initialState: {
        seriesoption: item?.recurrence && item?.recurrence?.recurrence !== 'Does Not Repeat' ? action : 1,
        recurrenceId: item?.recurrence?.id || null,
        recurrenceEndDate: item?.recurrence?.recurrenceEndDate || null,
      },
    });
    this.modalRef.content.closeBtnName = 'Close';
    this.modalRef.content.seriesOption = item?.recurrence && item?.recurrence?.recurrence !== 'Does Not Repeat' ? action : 1;
    this.modalRef.content.recurrenceId = item?.recurrence ? item?.recurrence?.id : null;
    this.modalRef.content.recurrenceEndDate = item?.recurrence
      ? item?.recurrence?.recurrenceEndDate
      : null;
  }

  public addToVoid(): void {
    const newData = { ProjectId: this.ProjectId, ParentCompanyId: this.ParentCompanyId };
    this.projectService.updateAccountProjectParent(newData);
    if (!this.voidSubmitted) {
      this.voidSubmitted = true;
      const currentinspectionItem = this.currentinspectionSaveItem;
      const data = {
        InspectionRequestId: currentinspectionItem.id,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.DeliveryService.createInspectionVoid(data).subscribe({
        next: (response: any): void => {
          if (response.status === 201) {
            this.toastr.success(response.message, 'Success');
            this.voidSubmitted = false;
            this.DeliveryService.updatedInspectionHistory({ status: true }, 'AddedToVoid');
            this.DeliveryService.updateCraneRequestHistory({ status: true }, 'AddedToVoid');
            if (this.bsModalRef) {
              this.bsModalRef.hide();
            }
          }
        },
        error: (createVoidError): void => {
          this.voidSubmitted = false;
          if (createVoidError.message?.statusCode === 400) {
            this.showError(createVoidError);
          } else if (!createVoidError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(createVoidError.message, 'OOPS!');
          }
        },
      });
    }
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }

  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public confirmationClose(template: TemplateRef<any>): void {
    if (this.statusChanged && this.currentStatus) {
      this.openConfirmationModalPopupForDetailsNDR(template);
    } else {
      this.resetForm('yes');
    }
  }

  public openConfirmationModalPopupForDetailsNDR(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.bsModalRef.hide();
      this.statusChanged = false;
    }
  }

  public voidConfirmationResponse(action: string): void {
    if (action === 'no') {
      this.modalRef.hide();
    } else {
      if (this.modalRef) {
        this.modalRef.hide();
      }
      this.addToVoid();
    }
  }

  public revertstatus(template: TemplateRef<any>): void {
    if (this.authUser.RoleId === 1 || this.authUser.RoleId === 2) {
      let data = {};
      data = {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      };
      this.modalRef2 = this.modalService.show(template, data);
    }
  }

  public statusupdate(action: string): void {
    if (action === 'no') {
      this.modalRef2.hide();
    } else {
      this.statusSubmitted = true;
      if (this.modalRef2) {
        this.modalRef2.hide();
      }
      const testdata = {
        id: this.currentinspectionSaveItem.id,
        status: 'Approved',
        inspectionStatus: null,
        ParentCompanyId: this.ParentCompanyId,
        statuschange: 'Reverted',
      };
      this.DeliveryService.updateInspectionStatus(testdata).subscribe((updateStatusResponse2: any): void => {
        if (updateStatusResponse2) {
          this.statusSubmitted = false;
          this.toastr.success('Status Reverted Successfully', 'Success');
          this.socket.emit('InspectionNDRApproveHistory', updateStatusResponse2);
          this.DeliveryService.updatedInspectionHistory({ status: true }, 'NDRApproveHistory');
          this.DeliveryService.updateCraneRequestHistory({ status: true }, 'historyUpdate');
          this.currentinspectionSaveItem.status = this.currentStatus;
          this.statusChanged = false;
          this.modalRef2.hide();
        }
      });
    }
  }

  public initializeSeriesOption(): void {
    this.seriesOptions = [
      {
        option: 1,
        text: 'This event',
        disabled: false,
      },
      {
        option: 2,
        text: 'This and all following events',
        disabled: false,
      },
      // {
      //   option: 3,
      //   text: 'All events in the series',
      //   disabled: false,
      // },
    ];
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'update':
          this.updateInspectionStatus();
          break;
        case 'save':
          this.saveStatus();
          break;
        case 'edit':
          this.openEditModal(data, item);
          break;
        case 'open':
          this.openModal(data);
          break;
        default:
          break;
      }
    }
  }
}
