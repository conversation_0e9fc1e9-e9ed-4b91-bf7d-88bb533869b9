import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule, FormArray, FormControl } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Router } from '@angular/router';
import { Socket } from 'ngx-socket-io';
import { of, Subject, throwError } from 'rxjs';
import { NewinspectionFormComponent } from './new-inspection-form.component';
import { BookingTemplatesService } from '../../../services/booking-templates/booking-templates.service';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';
import { MixpanelService } from '../../../services/mixpanel.service';
import moment from 'moment';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('NewinspectionFormComponent', () => {
  let component: NewinspectionFormComponent;
  let fixture: ComponentFixture<NewinspectionFormComponent>;
  let toastrService: jest.Mocked<ToastrService>;
  let projectService: jest.Mocked<ProjectService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let bookingTemplatesService: jest.Mocked<BookingTemplatesService>;
  let mixpanelService: jest.Mocked<MixpanelService>;
  let modalService: jest.Mocked<BsModalService>;
  let router: jest.Mocked<Router>;
  let socket: jest.Mocked<Socket>;
  let modalRef: jest.Mocked<BsModalRef>;

  beforeEach(async () => {
    // Create comprehensive mocks
    toastrService = {
      success: jest.fn(),
      error: jest.fn(),
      warning: jest.fn(),
      info: jest.fn()
    } as any;

    projectService = {
      getTimeZoneList: jest.fn().mockReturnValue(of({ data: [] })),
      getSingleProject: jest.fn().mockReturnValue(of({ data: {} })),
      getLocations: jest.fn().mockReturnValue(of({ data: [{ gateDetails: [], EquipmentId: [], TimeZoneId: [] }] })),
      gateList: jest.fn().mockReturnValue(of({ data: [] })),
      listEquipment: jest.fn().mockReturnValue(of({ data: [] })),
      getLastCraneRequestId: jest.fn().mockReturnValue(of({ lastId: { CraneRequestId: 'CR123' } })),
      getCompanies: jest.fn().mockReturnValue(of({ data: [] })),
      getDefinableWork: jest.fn().mockReturnValue(of({ data: [] })),
      projectParent: of({ ProjectId: '123', ParentCompanyId: '456' })
    } as any;

    deliveryService = {
      createInspectionNDR: jest.fn().mockReturnValue(of({ message: 'Success' })),
      getAvailableTimeSlots: jest.fn().mockReturnValue(of({ slots: { AM: [], PM: [] } })),
      loginUser: of({ id: 'user123', CompanyId: '1' }),
      searchNewMember: jest.fn().mockReturnValue(of({ data: [] })),
      getMemberRole: jest.fn().mockReturnValue(of({ data: { User: { firstName: 'Test', email: '<EMAIL>' }, id: 1 } })),
      updatedInspectionHistory: jest.fn()
    } as any;

    bookingTemplatesService = {
      getTemplates: jest.fn().mockReturnValue(of([])),
      saveTemplate: jest.fn().mockReturnValue(of({}))
    } as any;

    mixpanelService = {
      addMixpanelEvents: jest.fn()
    } as any;

    modalService = {
      show: jest.fn().mockReturnValue({ hide: jest.fn() })
    } as any;

    router = {
      events: of(),
      navigate: jest.fn()
    } as any;

    socket = {
      emit: jest.fn(),
      on: jest.fn()
    } as any;

    modalRef = {
      hide: jest.fn(),
      content: { lastId: 'test123' }
    } as any;

    await TestBed.configureTestingModule({
      declarations: [NewinspectionFormComponent],
      imports: [ReactiveFormsModule],
      providers: [
        FormBuilder,
        { provide: ToastrService, useValue: toastrService },
        { provide: ProjectService, useValue: projectService },
        { provide: DeliveryService, useValue: deliveryService },
        { provide: BookingTemplatesService, useValue: bookingTemplatesService },
        { provide: BsModalService, useValue: modalService },
        { provide: Router, useValue: router },
        { provide: Socket, useValue: socket },
        { provide: MixpanelService, useValue: mixpanelService },
        { provide: BsModalRef, useValue: modalRef }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(NewinspectionFormComponent);
    component = fixture.componentInstance;

    // Setup component properties
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.selectedEventDate = new Date('2024-12-01');
    component.data = { date: '2024-12-01', currentView: 'Month' };

    // Mock all methods that are called during initialization to prevent errors
    jest.spyOn(component, 'deliverForm').mockImplementation(() => {});
    jest.spyOn(component, 'getSelectedDate').mockImplementation(() => {});
    jest.spyOn(component, 'generateWeekDates').mockImplementation(() => {});
    jest.spyOn(component, 'getAvailableSlots').mockImplementation(() => {});
    jest.spyOn(component, 'getLastCraneRequestId').mockImplementation(() => {});
    jest.spyOn(component, 'setDefaultPerson').mockImplementation(() => {});
    jest.spyOn(component, 'getOverAllGateInNewinspection').mockImplementation(() => {});
    jest.spyOn(component, 'getTemplates').mockImplementation(() => {});
    jest.spyOn(component, 'getTimeZoneList').mockImplementation(() => {});
    jest.spyOn(component, 'setCurrentTiming').mockImplementation(() => {});
    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'getBookingData').mockImplementation(() => {});
    jest.spyOn(component, 'checkEquipmentData').mockImplementation(() => {});
    jest.spyOn(component, 'checkEquipmentType').mockImplementation(() => {});

    // Initialize arrays to prevent undefined errors
    component.equipmentList = [];
    component.locationList = [];
    component.gateList = [];
    component.companyList = [];
    component.defineList = [];
    component.timezoneList = [];
    component.templateList = [];
    component.weekDates = [];
    component.availableTimes = { AM: [], PM: [] } as any;
    component.vehicleTypes = [
      { id: 1, type: 'Medium and Heavy Duty Truck' },
      { id: 2, type: 'Passenger Car' },
      { id: 3, type: 'Light Duty Truck' }
    ];

    // Mock modal references
    component.modalRef = { hide: jest.fn(), content: { lastId: 'test123' } } as any;
    component.modalRef1 = { hide: jest.fn() } as any;
    component.modalRef2 = { hide: jest.fn() } as any;

    // Initialize form properly
    component.deliverDetailsForm = component['formBuilder'].group({
      LocationId: [[]],
      EquipmentId: [[]],
      GateId: [''],
      notes: [''],
      CraneRequestId: [''],
      person: [[]],
      description: [''],
      inspectionDate: [new Date()],
      inspectionStart: [new Date()],
      inspectionEnd: [new Date()],
      escort: [false],
      companyItems: [[]],
      defineItems: [[]],
      cranePickUpLocation: [''],
      craneDropOffLocation: [''],
      isAssociatedWithCraneRequest: [false],
      recurrence: ['Does Not Repeat'],
      inspectionType: [''],
      repeatEveryCount: [1],
      repeatEveryType: [''],
      days: component['formBuilder'].array([]),
      chosenDateOfMonth: [1],
      dateOfMonth: [''],
      monthlyRepeatType: [''],
      endDate: [''],
      templateName: [''],
      TimeZoneId: [[]],
      originationAddress: [''],
      vehicleType: ['']
    });

    fixture.detectChanges();
  });

  // ===== BASIC COMPONENT TESTS =====
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.selectedType).toBe('New Inspection Booking');
    expect(component.submitted).toBe(false);
    expect(component.escort).toBe(false);
    expect(component.formSubmitted).toBe(false);
    expect(component.selectedRecurrence).toBe('Does Not Repeat');
    expect(component.isAM).toBe(true);
  });

  it('should initialize form with required controls', () => {
    expect(component.deliverDetailsForm).toBeTruthy();
    expect(component.deliverDetailsForm.get('inspectionStart')).toBeTruthy();
    expect(component.deliverDetailsForm.get('inspectionEnd')).toBeTruthy();
    expect(component.deliverDetailsForm.get('description')).toBeTruthy();
    expect(component.deliverDetailsForm.get('inspectionType')).toBeTruthy();
  });

  // ===== TIME AND DURATION TESTS =====
  it('should handle hour selection', () => {
    component.selectedMinute = 30;
    component.selectHour(2);
    expect(component.selectedHour).toBe(2);
    expect(component.durationisOpen).toBe(false);
  });

  it('should handle minute selection', () => {
    component.selectedHour = 2;
    component.selectMinute(45);
    expect(component.selectedMinute).toBe(45);
    expect(component.durationisOpen).toBe(false);
  });

  it('should toggle duration dropdown', () => {
    component.durationisOpen = false;
    component.durationToggleDropdown();
    expect(component.durationisOpen).toBe(true);
    
    component.durationToggleDropdown();
    expect(component.durationisOpen).toBe(false);
  });

  it('should close duration dropdown', () => {
    component.durationisOpen = true;
    component.durationCloseDropdown();
    expect(component.durationisOpen).toBe(false);
  });

  it('should handle AM/PM selection', () => {
    component.selectAM();
    expect(component.isAM).toBe(true);

    component.selectPM();
    expect(component.isAM).toBe(false);
  });

  // ===== DATE MANAGEMENT TESTS =====
  it('should generate week dates correctly', () => {
    const testDate = '2024-12-01';
    // Restore the original method for this test
    (component.generateWeekDates as jest.Mock).mockRestore();
    component.generateWeekDates(testDate);

    expect(component.weekDates).toHaveLength(5);
    expect(component.weekDates[0]).toHaveProperty('name');
    expect(component.weekDates[0]).toHaveProperty('date');
    expect(component.weekDates[0]).toHaveProperty('fullDate');
    expect(component.selectedDate).toEqual(component.weekDates[0]);
  });

  it('should handle date selection', () => {
    const testDay = {
      name: 'Mon',
      date: '01',
      fullDate: '2024-12-01'
    };
    jest.spyOn(component, 'getAvailableSlots').mockImplementation(() => {});

    component.selectDate(testDay);

    expect(component.selectedDate).toEqual(testDay);
    expect(component.deliverDetailsForm.get('inspectionDate').value).toBe('12/01/2024');
    expect(component.getAvailableSlots).toHaveBeenCalledWith('2024-12-01');
  });

  it('should set default date and time with valid date', () => {
    const testDate = new Date('2024-12-01');
    component.setDefaultDateAndTime(testDate);

    expect(component.inspectionStart).toEqual(testDate);
    expect(component.inspectionEnd).toEqual(testDate);
  });

  it('should set default date and time with null date', () => {
    component.setDefaultDateAndTime(null);

    expect(component.inspectionStart).toBeInstanceOf(Date);
    expect(component.inspectionEnd).toBeInstanceOf(Date);
  });

  it('should handle selectTime correctly', () => {
    const startStr = '2024-12-01T10:00:00';
    const endStr = '2024-12-01T11:00:00';

    component.selectTime(startStr, endStr);

    expect(component.deliverDetailsForm.get('inspectionStart').value).toBeInstanceOf(Date);
    expect(component.deliverDetailsForm.get('inspectionEnd').value).toBeInstanceOf(Date);
    expect(component.selectedTime).toContain('10:00');
  });

  it('should handle convertStart correctly', () => {
    const testDate = new Date('2024-12-01');
    const startHours = 10;
    const startMinutes = 30;

    const result = component.convertStart(testDate, startHours, startMinutes);

    expect(result).toBeInstanceOf(Date);
    expect(result.getHours()).toBe(startHours);
    expect(result.getMinutes()).toBe(startMinutes);
  });

  // ===== EQUIPMENT AND LOCATION TESTS =====
  it('should check equipment type for crane equipment', () => {
    component.equipmentList = [
      {
        id: 1,
        PresetEquipmentType: { isCraneType: true }
      }
    ];

    // Setup form values to prevent errors
    component.deliverDetailsForm.patchValue({
      LocationId: [{ id: 'loc1' }],
      GateId: 'gate1'
    });

    // Restore the original method for this test
    (component.checkEquipmentType as jest.Mock).mockRestore();
    component.checkEquipmentType([{ id: 1 }]);

    expect(component.craneEquipmentTypeChosen).toBe(true);
  });

  it('should check equipment type for non-crane equipment', () => {
    component.equipmentList = [
      {
        id: 1,
        PresetEquipmentType: { isCraneType: false }
      }
    ];

    // Setup form values to prevent errors
    component.deliverDetailsForm.patchValue({
      LocationId: [{ id: 'loc1' }],
      GateId: 'gate1'
    });

    // Restore the original method for this test
    (component.checkEquipmentType as jest.Mock).mockRestore();
    component.checkEquipmentType([{ id: 1 }]);

    expect(component.craneEquipmentTypeChosen).toBe(false);
  });

  it('should handle vehicle type selection', () => {
    component.vehicleTypes = [
      { id: 1, type: 'Medium and Heavy Duty Truck' },
      { id: 2, type: 'Passenger Car' }
    ];

    component.vehicleTypeSelected({ id: 1 });

    expect(component.selectedVehicleType).toBe('Medium and Heavy Duty Truck');
  });

  // ===== SERVICE INTEGRATION TESTS =====
  it('should handle getAvailableSlots successfully', () => {
    const mockResponse = {
      slots: { AM: ['09:00', '10:00'], PM: ['14:00', '15:00'] }
    };
    deliveryService.getAvailableTimeSlots.mockReturnValue(of(mockResponse));

    component.deliverDetailsForm.patchValue({
      EquipmentId: [{ id: '1' }],
      GateId: 'gate1',
      LocationId: [{ id: 'loc1' }]
    });
    component.timeZone = 'America/New_York';
    component.selectedMinutes = 60;

    // Restore the original method for this test
    (component.getAvailableSlots as jest.Mock).mockRestore();
    component.getAvailableSlots('2024-12-01');

    expect(deliveryService.getAvailableTimeSlots).toHaveBeenCalledWith('123', {
      date: '2024-12-01',
      equipmentId: ['1'],
      timeZone: 'America/New_York',
      duration: 60,
      GateId: 'gate1',
      LocationId: 'loc1',
      bookingType: 'inspection'
    });
    expect(component.availableTimes).toEqual(mockResponse.slots);
    expect(component.isSlotsNull).toBeFalsy();
  });

  it('should handle getAvailableSlots with no slots', () => {
    const mockResponse = {
      slots: { AM: [], PM: [] }
    };
    deliveryService.getAvailableTimeSlots.mockReturnValue(of(mockResponse));

    component.deliverDetailsForm.patchValue({
      EquipmentId: [{ id: '1' }],
      GateId: 'gate1',
      LocationId: [{ id: 'loc1' }]
    });

    // Restore the original method for this test
    (component.getAvailableSlots as jest.Mock).mockRestore();
    component.getAvailableSlots('2024-12-01');

    expect(component.isSlotsNull).toBeTruthy();
  });

  // ===== FORM VALIDATION TESTS =====
  it('should validate required fields before submission', () => {
    component.deliverDetailsForm.patchValue({
      description: '',
      LocationId: [],
      EquipmentId: [],
      person: [],
      companyItems: []
    });

    jest.spyOn(component, 'checkStringEmptyValues').mockReturnValue(true);
    jest.spyOn(component, 'formReset').mockImplementation(() => {});
    jest.spyOn(component, 'createinspection').mockImplementation(() => {});

    // Make form invalid to trigger early return
    component.deliverDetailsForm.get('description').setErrors({ required: true });

    component.onSubmit();

    expect(component.submitted).toBe(true);
    expect(component.formSubmitted).toBe(false);
  });

  it('should handle successful form submission', () => {
    component.deliverDetailsForm.patchValue({
      description: 'Test Description',
      LocationId: [{ id: 1 }],
      EquipmentId: [{ id: 1 }],
      person: [{ id: 1 }],
      companyItems: [{ id: 1 }],
      inspectionType: 'Standard',
      inspectionStart: new Date(),
      inspectionEnd: new Date(),
      inspectionDate: new Date(),
      isAssociatedWithCraneRequest: false,
      TimeZoneId: [{ id: 1 }]
    });

    jest.spyOn(component, 'checkStringEmptyValues').mockReturnValue(false);
    jest.spyOn(component, 'createinspection').mockImplementation(() => {});

    component.onSubmit();

    expect(component.submitted).toBe(true);
    expect(component.createinspection).toHaveBeenCalled();
  });

  // ===== RECURRENCE AND TEMPLATE TESTS =====
  it('should handle recurrence count change', () => {
    jest.spyOn(component, 'updateRecurrenceFlags').mockImplementation(() => {});
    jest.spyOn(component, 'updateRepeatEveryType').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.changeRecurrenceCount(3);

    expect(component.updateRecurrenceFlags).toHaveBeenCalledWith(component.selectedRecurrence, 3);
    expect(component.updateRepeatEveryType).toHaveBeenCalledWith(component.selectedRecurrence, 3);
    expect(component.occurMessage).toHaveBeenCalled();
  });

  it('should handle monthly recurrence change', () => {
    jest.spyOn(component, 'setMonthlyOrYearlyRecurrenceOption').mockImplementation(() => {});
    jest.spyOn(component, 'updateFormValidation').mockImplementation(() => {});
    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.changeMonthlyRecurrence();

    expect(component.setMonthlyOrYearlyRecurrenceOption).toHaveBeenCalled();
    expect(component.updateFormValidation).toHaveBeenCalled();
    expect(component.showMonthlyRecurrence).toHaveBeenCalled();
    expect(component.occurMessage).toHaveBeenCalled();
  });

  it('should handle inspection type change', () => {
    const inspectionType = 'Safety';
    component.onChangeInspectionType(inspectionType);

    expect(component.deliverDetailsForm.get('inspectionType').value).toBe(inspectionType);
  });

  // ===== MODAL AND UI INTERACTION TESTS =====
  it('should handle modal popup close', () => {
    component.modalLoader = true;
    component.closeModalPopup();
    expect(component.modalLoader).toBe(false);
  });

  it('should handle form reset', () => {
    component.submitted = true;
    component.formSubmitted = true;

    component.formReset();

    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
  });

  it('should handle address change', () => {
    const mockAddress = { formatted_address: '123 Test St, Test City, TC 12345' };
    component.handleAddressChange(mockAddress);

    expect(component.deliverDetailsForm.get('originationAddress').value).toBe(mockAddress.formatted_address);
  });

  // ===== ERROR HANDLING TESTS =====
  it('should handle error messages', () => {
    const errorMessage = 'Test error message';
    component.showErrorMessage(errorMessage);

    expect(toastrService.error).toHaveBeenCalledWith(errorMessage);
    expect(component.formSubmitted).toBe(false);
    expect(component.submitted).toBe(false);
  });

  it('should handle show error with details', () => {
    const mockError = {
      message: {
        details: [{ field: 'error message' }]
      }
    };

    component.showError(mockError);

    expect(toastrService.error).toHaveBeenCalledWith(['error message']);
    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
  });

  // ===== ADDITIONAL SERVICE INTEGRATION TESTS =====
  it('should handle getLastCraneRequestId successfully', () => {
    const mockResponse = { lastId: { CraneRequestId: 'CRANE123' } };
    projectService.getLastCraneRequestId.mockReturnValue(of(mockResponse));

    // Restore the original method for this test
    (component.getLastCraneRequestId as jest.Mock).mockRestore();
    component.getLastCraneRequestId();

    expect(projectService.getLastCraneRequestId).toHaveBeenCalledWith({
      ProjectId: '123',
      ParentCompanyId: '456'
    });
    expect(component.craneRequestLastId).toBe('CRANE123');
    expect(component.deliverDetailsForm.get('CraneRequestId').value).toBe('CRANE123');
  });

  it('should handle setDefaultPerson successfully', () => {
    const mockResponse = {
      data: {
        User: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
        id: 123
      }
    };
    deliveryService.getMemberRole.mockReturnValue(of(mockResponse));

    // Restore the original method for this test
    (component.setDefaultPerson as jest.Mock).mockRestore();
    component.setDefaultPerson();

    expect(deliveryService.getMemberRole).toHaveBeenCalledWith({
      ProjectId: '123',
      ParentCompanyId: '456'
    });
    expect(component.deliverDetailsForm.get('person').value).toEqual([{
      email: 'John Doe (<EMAIL>)',
      id: 123,
      readonly: true
    }]);
  });

  it('should handle setDefaultPerson with no lastName', () => {
    const mockResponse = {
      data: {
        User: { firstName: 'John', lastName: null, email: '<EMAIL>' },
        id: 123
      }
    };
    deliveryService.getMemberRole.mockReturnValue(of(mockResponse));

    // Restore the original method for this test
    (component.setDefaultPerson as jest.Mock).mockRestore();
    component.setDefaultPerson();

    expect(component.deliverDetailsForm.get('person').value).toEqual([{
      email: 'John (<EMAIL>)',
      id: 123,
      readonly: true
    }]);
  });

  it('should handle requestAutocompleteItems', () => {
    const mockResponse = { data: [] };
    deliveryService.searchNewMember.mockReturnValue(of(mockResponse));

    const result = component.requestAutocompleteItems('test');

    expect(deliveryService.searchNewMember).toHaveBeenCalledWith({
      ProjectId: '123',
      search: 'test',
      ParentCompanyId: '456'
    });
    expect(result).toBeDefined();
  });

  // ===== UTILITY AND HELPER TESTS =====
  it('should handle numberOnly validation', () => {
    const validEvent = { which: 53, keyCode: 53 }; // '5'
    const invalidEvent = { which: 65, keyCode: 65 }; // 'A'

    expect(component.numberOnly(validEvent)).toBe(true);
    expect(component.numberOnly(invalidEvent)).toBe(false);
  });

  it('should handle checkStringEmptyValues', () => {
    const validFormValue = { description: 'Valid description', notes: 'Valid notes' };
    const invalidFormValue1 = { description: '   ', notes: 'Valid notes' };
    const invalidFormValue2 = { description: 'Valid description', notes: '   ' };

    expect(component.checkStringEmptyValues(validFormValue)).toBe(false);
    expect(component.checkStringEmptyValues(invalidFormValue1)).toBe(true);
    expect(component.checkStringEmptyValues(invalidFormValue2)).toBe(true);
  });

  it('should handle sortWeekDays', () => {
    const unsortedDays = ['Friday', 'Monday', 'Wednesday'];
    const sortedDays = component.sortWeekDays(unsortedDays);

    expect(sortedDays).toEqual(['Monday', 'Wednesday', 'Friday']);
  });

  it('should handle sortWeekDays with empty array', () => {
    const result = component.sortWeekDays([]);
    expect(result).toBeUndefined();
  });

  it('should handle selectDuration', () => {
    component.selectedHour = 2;
    component.selectedMinute = 30;
    jest.spyOn(component, 'getAvailableSlots').mockImplementation(() => {});

    component.selectDuration(60);

    expect(component.selectedMinutes).toBe(150); // (2 * 60) + 30
    expect(component.getAvailableSlots).toHaveBeenCalled();
  });

  it('should handle changeDate', () => {
    const mockEvent = new Date('2024-12-01T10:00:00');
    component.modalLoader = false;

    component.changeDate(mockEvent);

    expect(component.inspectionEnd).toBeInstanceOf(Date);
    expect(component.NDRTimingChanged).toBe(true);
  });

  it('should handle inspectionEndTimeChangeDetection', () => {
    component.NDRTimingChanged = false;
    component.inspectionEndTimeChangeDetection();
    expect(component.NDRTimingChanged).toBe(true);
  });

  it('should handle updateDropdownState', () => {
    component.selectedHour = 2;
    component.selectedMinutes = 30;
    component.durationisOpen = true;

    component.updateDropdownState();

    expect(component.durationisOpen).toBe(false);
  });

  it('should handle createNDR success', () => {
    const mockPayload = {
      description: 'Test',
      companies: ['1'],
      escort: false,
      ProjectId: '123',
      GateId: 'gate1',
      notes: 'Test notes',
      EquipmentId: ['1'],
      LocationId: 'loc1',
      inspectionStart: new Date(),
      inspectionEnd: new Date(),
      ParentCompanyId: '456',
      persons: ['1'],
      define: ['1'],
      requestType: 'inspectionRequest',
      timeZone: 'UTC',
      isAssociatedWithCraneRequest: false,
      cranePickUpLocation: null,
      craneDropOffLocation: null,
      CraneRequestId: null,
      recurrence: 'Does Not Repeat',
      chosenDateOfMonth: 1,
      chosenDateOfMonthValue: 1,
      dateOfMonth: '',
      monthlyRepeatType: '',
      days: [],
      repeatEveryType: '',
      repeatEveryCount: 1,
      inspectionType: 'Standard',
      originationAddress: '',
      vehicleType: ''
    };
    deliveryService.createInspectionNDR.mockReturnValue(of({ message: 'Success' }));
    jest.spyOn(component, 'formReset').mockImplementation(() => {});
    jest.spyOn(component, 'resetForm').mockImplementation(() => {});

    component.createNDR(mockPayload);

    expect(deliveryService.createInspectionNDR).toHaveBeenCalledWith(mockPayload);
    expect(toastrService.success).toHaveBeenCalledWith('Success', 'Success');
    expect(socket.emit).toHaveBeenCalledWith('NDRCreateHistory', { message: 'Success' });
    expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Created New inspection Booking');
  });

  it('should handle createNDR error', () => {
    const mockPayload = {
      description: 'Test',
      companies: ['1'],
      escort: false,
      ProjectId: '123',
      GateId: 'gate1',
      notes: 'Test notes',
      EquipmentId: ['1'],
      LocationId: 'loc1',
      inspectionStart: new Date(),
      inspectionEnd: new Date(),
      ParentCompanyId: '456',
      persons: ['1'],
      define: ['1'],
      requestType: 'inspectionRequest',
      timeZone: 'UTC',
      isAssociatedWithCraneRequest: false,
      cranePickUpLocation: null,
      craneDropOffLocation: null,
      CraneRequestId: null,
      recurrence: 'Does Not Repeat',
      chosenDateOfMonth: 1,
      chosenDateOfMonthValue: 1,
      dateOfMonth: '',
      monthlyRepeatType: '',
      days: [],
      repeatEveryType: '',
      repeatEveryCount: 1,
      inspectionType: 'Standard',
      originationAddress: '',
      vehicleType: ''
    };
    const mockError = { message: { statusCode: 400 } };
    deliveryService.createInspectionNDR.mockReturnValue(throwError(() => mockError));
    jest.spyOn(component, 'formReset').mockImplementation(() => {});
    jest.spyOn(component, 'showError').mockImplementation(() => {});

    component.createNDR(mockPayload);

    expect(component.showError).toHaveBeenCalledWith(mockError);
    expect(component.formReset).toHaveBeenCalled();
  });

  it('should handle createNDR error without statusCode', () => {
    const mockPayload = {
      description: 'Test',
      companies: ['1'],
      escort: false,
      ProjectId: '123',
      GateId: 'gate1',
      notes: 'Test notes',
      EquipmentId: ['1'],
      LocationId: 'loc1',
      inspectionStart: new Date(),
      inspectionEnd: new Date(),
      ParentCompanyId: '456',
      persons: ['1'],
      define: ['1'],
      requestType: 'inspectionRequest',
      timeZone: 'UTC',
      isAssociatedWithCraneRequest: false,
      cranePickUpLocation: null,
      craneDropOffLocation: null,
      CraneRequestId: null,
      recurrence: 'Does Not Repeat',
      chosenDateOfMonth: 1,
      chosenDateOfMonthValue: 1,
      dateOfMonth: '',
      monthlyRepeatType: '',
      days: [],
      repeatEveryType: '',
      repeatEveryCount: 1,
      inspectionType: 'Standard',
      originationAddress: '',
      vehicleType: ''
    };
    const mockError = { message: 'Simple error' };
    deliveryService.createInspectionNDR.mockReturnValue(throwError(() => mockError));
    jest.spyOn(component, 'formReset').mockImplementation(() => {});

    component.createNDR(mockPayload);

    expect(toastrService.error).toHaveBeenCalledWith('Simple error', 'OOPS!');
    expect(component.formReset).toHaveBeenCalled();
  });

  // ===== LIFECYCLE AND INITIALIZATION TESTS =====
  it('should handle ngOnInit correctly', () => {
    component.modalRef = { content: { lastId: 'test123' } } as any;
    component.selectedEventDate = new Date('2024-12-01');
    jest.spyOn(component, 'getSelectedDate').mockImplementation(() => {});
    jest.spyOn(component, 'generateWeekDates').mockImplementation(() => {});
    jest.spyOn(component, 'getAvailableSlots').mockImplementation(() => {});

    component.ngOnInit();

    expect(component.lastId).toBe('test123');
    expect(component.deliverDetailsForm.get('inspectionDate').value).toEqual(component.selectedEventDate);
    expect(component.getSelectedDate).toHaveBeenCalled();
    expect(component.generateWeekDates).toHaveBeenCalled();
    expect(component.getAvailableSlots).toHaveBeenCalled();
  });

  it('should handle ngAfterViewInit with booking template', () => {
    component.data = { bookingTemplate: true };
    jest.spyOn(component, 'getLastCraneRequestId').mockImplementation(() => {});
    jest.spyOn(component, 'setDefaultPerson').mockImplementation(() => {});
    component.authUser = { id: '1', name: 'Test User' };

    component.ngAfterViewInit();

    expect(component.isBookingTemplate).toBeTruthy();
    expect(component.getLastCraneRequestId).toHaveBeenCalled();
  });

  it('should handle ngAfterViewInit without data', () => {
    component.data = null;
    jest.spyOn(component, 'getLastCraneRequestId').mockImplementation(() => {});

    component.ngAfterViewInit();

    expect(component.isBookingTemplate).toBeFalsy();
    expect(component.getLastCraneRequestId).toHaveBeenCalled();
  });

  // ===== ADDITIONAL COVERAGE TESTS =====
  it('should handle getOverAllGateInNewinspection', () => {
    jest.spyOn(component, 'getOverAllEquipmentInNewinspection').mockImplementation(() => {});

    // Restore the original method for this test
    (component.getOverAllGateInNewinspection as jest.Mock).mockRestore();
    component.getOverAllGateInNewinspection();

    expect(projectService.gateList).toHaveBeenCalledWith(
      {
        ProjectId: '123',
        pageSize: 0,
        pageNo: 0,
        ParentCompanyId: '456'
      },
      { isFilter: true, showActivatedAlone: true }
    );
    expect(component.getOverAllEquipmentInNewinspection).toHaveBeenCalled();
    expect(component.modalLoader).toBeTruthy();
  });

  it('should handle getOverAllEquipmentInNewinspection', () => {
    jest.spyOn(component, 'newNdrgetCompanies').mockImplementation(() => {});

    component.getOverAllEquipmentInNewinspection();

    expect(projectService.listEquipment).toHaveBeenCalledWith(
      {
        ProjectId: '123',
        pageSize: 0,
        pageNo: 0,
        ParentCompanyId: '456'
      },
      { isFilter: true, showActivatedAlone: true }
    );
    expect(component.newNdrgetCompanies).toHaveBeenCalled();
  });

  it('should handle newNdrgetCompanies successfully', () => {
    const mockResponse = {
      data: [
        { id: '1', companyName: 'Company 1' },
        { id: '2', companyName: 'Company 2' }
      ]
    };
    component.authUser = { CompanyId: '1' };
    projectService.getCompanies.mockReturnValue(of(mockResponse));
    jest.spyOn(component, 'getDefinable').mockImplementation(() => {});

    component.newNdrgetCompanies();

    expect(projectService.getCompanies).toHaveBeenCalledWith({
      ProjectId: '123',
      ParentCompanyId: '456'
    });
    expect(component.companyList).toEqual(mockResponse.data);
    expect(component.getDefinable).toHaveBeenCalled();
  });

  it('should handle getDefinable successfully', () => {
    const mockResponse = {
      data: [
        { id: '1', DFOW: 'Define Work 1' },
        { id: '2', DFOW: 'Define Work 2' }
      ]
    };
    projectService.getDefinableWork.mockReturnValue(of(mockResponse));
    jest.spyOn(component, 'getLocations').mockImplementation(() => {});

    component.getDefinable();

    expect(projectService.getDefinableWork).toHaveBeenCalledWith({
      ProjectId: '123',
      ParentCompanyId: '456'
    });
    expect(component.defineList).toEqual(mockResponse.data);
    expect(component.getLocations).toHaveBeenCalled();
  });

  it('should handle getLocations successfully with gate details', () => {
    const mockResponse = {
      data: [
        {
          id: '1',
          locationPath: 'Location 1',
          gateDetails: [{ id: '1', gateName: 'Gate 1' }],
          EquipmentId: [{ id: '1', equipmentName: 'Equipment 1' }],
          TimeZoneId: [{ location: 'America/New_York' }]
        }
      ]
    };
    projectService.getLocations.mockReturnValue(of(mockResponse));
    jest.spyOn(component, 'setDefaultLocationPath').mockImplementation(() => {});

    component.getLocations();

    expect(projectService.getLocations).toHaveBeenCalledWith({
      ProjectId: '123',
      ParentCompanyId: '456'
    });
    expect(component.gateList).toEqual(mockResponse.data[0].gateDetails);
    expect(component.equipmentList).toEqual(mockResponse.data[0].EquipmentId);
    expect(component.timeZone).toBe('America/New_York');
    expect(component.setDefaultLocationPath).toHaveBeenCalled();
  });

  it('should handle getLocations successfully without gate details', () => {
    const mockResponse = {
      data: [
        {
          id: '1',
          locationPath: 'Location 1',
          gateDetails: null,
          EquipmentId: null,
          TimeZoneId: null
        }
      ]
    };
    projectService.getLocations.mockReturnValue(of(mockResponse));

    component.getLocations();

    expect(component.gateList).toEqual([]);
    expect(component.equipmentList).toEqual([]);
    expect(component.timeZone).toBe('');
  });

  // ===== ADDITIONAL COMPREHENSIVE TESTS FOR 90% COVERAGE =====

  // ===== TEMPLATE FUNCTIONALITY TESTS =====
  it('should handle template selection', () => {
    const mockTemplate = {
      id: 1,
      template_name: 'Test Template',
      description: 'Test Description',
      equipment: JSON.stringify([{ id: 1 }])
    };
    component.templateList = [mockTemplate];
    jest.spyOn(component, 'setBookingTemplateData').mockImplementation(() => {});

    component.template({ id: 1 });

    expect(component.selectedTemplate).toEqual(mockTemplate);
    expect(component.disableSaveAsTemplate).toBe(true);
    expect(component.setBookingTemplateData).toHaveBeenCalledWith(mockTemplate);
  });

  it('should handle getTemplates successfully', () => {
    const mockResponse = {
      data: {
        rows: [
          { id: 1, template_name: 'Template 1' },
          { id: 2, template_name: 'Template 2' }
        ]
      }
    };
    bookingTemplatesService.getTemplates.mockReturnValue(of(mockResponse));

    // Restore the original method for this test
    (component.getTemplates as jest.Mock).mockRestore();
    component.getTemplates();

    expect(bookingTemplatesService.getTemplates).toHaveBeenCalled();
    expect(component.templateList).toEqual(mockResponse.data.rows);
    expect(component.loader).toBe(false);
  });

  it('should handle saveAsTemplatePopup', () => {
    const mockTemplate = {} as any;
    component.selectedTemplate = null;

    component.saveAsTemplatePopup(mockTemplate);

    expect(modalService.show).toHaveBeenCalledWith(mockTemplate, {
      keyboard: false,
      class: 'modal-md openTemplateName-popup modal-dialog-centered custom-modal'
    });
  });

  it('should handle closeTemplateNamePopup with no action', () => {
    component.closeTemplateNamePopup('no');
    expect(component.modalRef2.hide).toHaveBeenCalled();
  });

  it('should handle closeTemplateNamePopup with yes action', () => {
    jest.spyOn(component, 'saveAsTemplate').mockImplementation(() => {});
    component.closeTemplateNamePopup('yes');
    expect(component.modalRef2.hide).toHaveBeenCalled();
    expect(component.saveAsTemplate).toHaveBeenCalled();
  });

  // ===== RECURRENCE FUNCTIONALITY TESTS =====
  it('should handle onRecurrenceSelect for Daily', () => {
    jest.spyOn(component, 'getRepeatEveryType').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});
    component.deliverDetailsForm.patchValue({ repeatEveryCount: 1 });

    component.onRecurrenceSelect('Daily');

    expect(component.selectedRecurrence).toBe('Daily');
    expect(component.getRepeatEveryType).toHaveBeenCalledWith('Daily');
    expect(component.occurMessage).toHaveBeenCalled();
  });

  it('should handle onRecurrenceSelect for Weekly with multiple count', () => {
    jest.spyOn(component, 'getRepeatEveryType').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});
    component.deliverDetailsForm.patchValue({ repeatEveryCount: 2 });

    component.onRecurrenceSelect('Weekly');

    expect(component.selectedRecurrence).toBe('Weekly');
    expect(component.isRepeatWithMultipleRecurrence).toBe(true);
    expect(component.isRepeatWithSingleRecurrence).toBe(false);
  });

  it('should handle getRepeatEveryType for different recurrence types', () => {
    component.getRepeatEveryType('Does Not Repeat');
    expect(component.deliverDetailsForm.get('repeatEveryType').value).toBe('');

    component.getRepeatEveryType('Daily');
    expect(component.deliverDetailsForm.get('repeatEveryType').value).toBe('Day');

    component.getRepeatEveryType('Weekly');
    expect(component.deliverDetailsForm.get('repeatEveryType').value).toBe('Week');

    component.getRepeatEveryType('Monthly');
    expect(component.deliverDetailsForm.get('repeatEveryType').value).toBe('Month');

    component.getRepeatEveryType('Yearly');
    expect(component.deliverDetailsForm.get('repeatEveryType').value).toBe('Year');
  });

  it('should handle updateRecurrenceFlags for Daily recurrence', () => {
    component.updateRecurrenceFlags('Daily', 1);
    expect(component.isRepeatWithSingleRecurrence).toBe(true);
    expect(component.isRepeatWithMultipleRecurrence).toBe(false);
    expect(component.showRecurrenceTypeDropdown).toBe(false);

    component.updateRecurrenceFlags('Daily', 2);
    expect(component.isRepeatWithSingleRecurrence).toBe(false);
    expect(component.showRecurrenceTypeDropdown).toBe(true);
  });

  it('should handle updateRepeatEveryType for different recurrence types', () => {
    jest.spyOn(component, 'changeMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});

    component.updateRepeatEveryType('Daily', 1);
    expect(component.deliverDetailsForm.get('repeatEveryType').value).toBe('Day');

    component.updateRepeatEveryType('Daily', 2);
    expect(component.deliverDetailsForm.get('repeatEveryType').value).toBe('Days');

    component.updateRepeatEveryType('Weekly', 1);
    expect(component.deliverDetailsForm.get('repeatEveryType').value).toBe('Week');

    component.updateRepeatEveryType('Monthly', 1);
    expect(component.deliverDetailsForm.get('repeatEveryType').value).toBe('Month');
    expect(component.changeMonthlyRecurrence).toHaveBeenCalled();

    component.updateRepeatEveryType('Yearly', 2);
    expect(component.deliverDetailsForm.get('repeatEveryType').value).toBe('Years');
  });

  // ===== LOCATION AND TIMEZONE TESTS =====
  it('should handle locationSelected', () => {
    component.locationList = [
      {
        id: 1,
        gateDetails: [{ id: 1, gateName: 'Gate 1' }],
        EquipmentId: [{ id: 1, equipmentName: 'Equipment 1' }],
        TimeZoneId: [{ location: 'America/New_York' }]
      }
    ];

    component.locationSelected({ id: 1 });

    expect(component.deliverDetailsForm.get('GateId').value).toBe('');
    expect(component.deliverDetailsForm.get('EquipmentId').value).toBe('');
    expect(component.gateList).toEqual([{ id: 1, gateName: 'Gate 1' }]);
    expect(component.equipmentList).toEqual([{ id: 1, equipmentName: 'Equipment 1' }]);
    expect(component.timeZone).toBe('America/New_York');
    expect(component.selectedLocationId).toBe(1);
  });

  it('should handle timeZoneSelected', () => {
    component.timezoneList = [
      { id: 1, location: 'America/New_York' },
      { id: 2, location: 'America/Los_Angeles' }
    ] as any;

    component.timeZoneSelected(1);

    expect(component.selectedTimeZoneValue).toEqual({ id: 1, location: 'America/New_York' });
  });

  it('should handle getTimeZoneList successfully', () => {
    const mockTimeZoneResponse = {
      data: [
        { id: 1, location: 'America/New_York' },
        { id: 2, location: 'America/Los_Angeles' }
      ]
    };
    const mockProjectResponse = {
      data: { TimeZoneId: 1 }
    };
    projectService.getTimeZoneList.mockReturnValue(of(mockTimeZoneResponse));
    projectService.getSingleProject.mockReturnValue(of(mockProjectResponse));

    // Restore the original method for this test
    (component.getTimeZoneList as jest.Mock).mockRestore();
    component.getTimeZoneList();

    expect(projectService.getTimeZoneList).toHaveBeenCalled();
    expect(component.timezoneList).toEqual(mockTimeZoneResponse.data);
    expect(component.loader).toBe(false);
  });

  it('should handle getTimeZoneList error', () => {
    const mockError = { message: { statusCode: 400 } };
    projectService.getTimeZoneList.mockReturnValue(throwError(() => mockError));
    jest.spyOn(component, 'showError').mockImplementation(() => {});

    // Restore the original method for this test
    (component.getTimeZoneList as jest.Mock).mockRestore();
    component.getTimeZoneList();

    expect(component.showError).toHaveBeenCalledWith(mockError);
  });

  // ===== FORM CONTROL AND VALIDATION TESTS =====
  it('should handle onChange for checkbox events', () => {
    const mockEvent = {
      target: { value: 'Monday', checked: true }
    };
    component.checkform = component.deliverDetailsForm.get('days') as any;
    component.valueExists = [];

    component.onChange(mockEvent);

    expect(component.checkform.length).toBeGreaterThan(0);
  });

  it('should handle onChange for unchecking Weekly recurrence', () => {
    const mockEvent = {
      target: { value: 'Monday', checked: false }
    };
    component.selectedRecurrence = 'Weekly';
    component.checkform = component.deliverDetailsForm.get('days') as any;
    component.checkform.push(component['formBuilder'].control('Monday'));
    component.checkform.push(component['formBuilder'].control('Tuesday'));

    component.onChange(mockEvent);

    expect(component.checkform.length).toBe(1);
  });

  it('should handle setDefaultLocationPath with default location', () => {
    component.locationList = [
      { id: 1, isDefault: true, locationPath: 'Default Location' }
    ];
    jest.spyOn(component, 'closeModalPopup').mockImplementation(() => {});

    component.setDefaultLocationPath();

    expect(component.deliverDetailsForm.get('LocationId').value).toEqual([
      { id: 1, isDefault: true, locationPath: 'Default Location' }
    ]);
    expect(component.selectedLocationId).toBe(1);
    expect(component.closeModalPopup).toHaveBeenCalled();
  });

  it('should handle scrollToTime', () => {
    const mockContainer = {
      querySelectorAll: jest.fn().mockReturnValue([
        { offsetTop: 100 },
        { offsetTop: 200 }
      ]),
      offsetTop: 50,
      scrollTop: 0
    };
    component.timeSlotsContainer = { nativeElement: mockContainer } as any;

    component.scrollToTime(1);

    expect(mockContainer.scrollTop).toBe(150); // 200 - 50
  });

  // ===== UTILITY METHOD TESTS =====
  it('should handle setVehicleType', () => {
    component.selectedTemplate = {
      vehicleType: 'Medium and Heavy Duty Truck'
    };
    component.vehicleTypes = [
      { id: 1, type: 'Medium and Heavy Duty Truck' },
      { id: 2, type: 'Passenger Car' }
    ];

    component.setVehicleType();

    expect(component.deliverDetailsForm.get('vehicleType').value).toEqual([
      { id: 1, type: 'Medium and Heavy Duty Truck' }
    ]);
    expect(component.selectedVehicleType).toBe('Medium and Heavy Duty Truck');
  });

  it('should handle selectedBookingtype with dirty form', () => {
    const mockTemplate = {} as any;
    component.deliverDetailsForm.markAsTouched();
    component.deliverDetailsForm.markAsDirty();
    jest.spyOn(component, 'openChangeConfirm').mockImplementation(() => {});

    component.selectedBookingtype('delivery', mockTemplate);

    expect(component.selectedParams).toBe('delivery');
    expect(component.openChangeConfirm).toHaveBeenCalledWith(mockTemplate);
  });

  it('should handle selectedBookingtype with clean form', () => {
    const mockTemplate = {} as any;
    component.selectfunction = jest.fn();

    component.selectedBookingtype('delivery', mockTemplate);

    expect(component.selectedParams).toBe('delivery');
    expect(component.selectfunction).toHaveBeenCalledWith('delivery');
  });

  it('should handle closeChangeConfirm with yes', () => {
    component.selectfunction = jest.fn();
    component.selectedParams = 'delivery';

    component.closeChangeConfirm('yes');

    expect(component.modalRef1.hide).toHaveBeenCalled();
    expect(component.selectfunction).toHaveBeenCalledWith('delivery');
  });

  it('should handle closeChangeConfirm with no', () => {
    component.closeChangeConfirm('no');
    expect(component.modalRef1.hide).toHaveBeenCalled();
  });

  // ===== TEMPLATE SETTING METHODS TESTS =====
  it('should handle setBookingTemplateData', () => {
    component.selectedTemplate = {
      description: 'Test Description',
      date: '2024-12-01',
      from_time: '2024-12-01T10:00:00',
      to_time: '2024-12-01T11:00:00',
      is_escort_needed: true,
      notes: 'Test Notes',
      gate: 'gate1',
      equipment: JSON.stringify([{ id: 1 }]),
      isAssociatedWithCraneRequest: true,
      picking_from: 'Location A',
      picking_to: 'Location B'
    };

    jest.spyOn(component, 'setlocation').mockImplementation(() => {});
    jest.spyOn(component, 'setCompany').mockImplementation(() => {});
    jest.spyOn(component, 'setEquipment').mockImplementation(() => {});
    jest.spyOn(component, 'setDefine').mockImplementation(() => {});
    jest.spyOn(component, 'setMember').mockImplementation(() => {});
    jest.spyOn(component, 'setTimeZone').mockImplementation(() => {});
    jest.spyOn(component, 'setRecurrence').mockImplementation(() => {});

    component.setBookingTemplateData(component.selectedTemplate);

    expect(component.deliverDetailsForm.get('description').value).toBe('Test Description');
    expect(component.deliverDetailsForm.get('escort').value).toBe(true);
    expect(component.deliverDetailsForm.get('notes').value).toBe('Test Notes');
    expect(component.deliverDetailsForm.get('GateId').value).toBe('gate1');
    expect(component.deliverDetailsForm.get('isAssociatedWithCraneRequest').value).toBe(true);
    expect(component.craneEquipmentTypeChosen).toBe(true);
    expect(component.setlocation).toHaveBeenCalled();
    expect(component.setCompany).toHaveBeenCalled();
    expect(component.setEquipment).toHaveBeenCalled();
    expect(component.setDefine).toHaveBeenCalled();
    expect(component.setMember).toHaveBeenCalled();
    expect(component.setTimeZone).toHaveBeenCalled();
    expect(component.setRecurrence).toHaveBeenCalled();
  });

  it('should handle setCompany', () => {
    component.selectedTemplate = {
      responsible_company: JSON.stringify([1, 2])
    };
    component.companyList = [
      { id: 1, companyName: 'Company 1' },
      { id: 2, companyName: 'Company 2' },
      { id: 3, companyName: 'Company 3' }
    ];

    component.setCompany();

    expect(component.deliverDetailsForm.get('companyItems').value).toEqual([
      { id: 1, companyName: 'Company 1' },
      { id: 2, companyName: 'Company 2' }
    ]);
  });

  it('should handle setEquipment', () => {
    component.selectedTemplate = {
      equipment: JSON.stringify([1, 2])
    };
    component.equipmentList = [
      { id: 1, equipmentName: 'Equipment 1' },
      { id: 2, equipmentName: 'Equipment 2' },
      { id: 3, equipmentName: 'Equipment 3' }
    ];

    component.setEquipment();

    expect(component.deliverDetailsForm.get('EquipmentId').value).toEqual([
      { id: 1, equipmentName: 'Equipment 1' },
      { id: 2, equipmentName: 'Equipment 2' }
    ]);
  });

  it('should handle setDefine', () => {
    component.selectedTemplate = {
      dfow: JSON.stringify([1, 2])
    };
    component.defineList = [
      { id: 1, DFOW: 'Define Work 1' },
      { id: 2, DFOW: 'Define Work 2' },
      { id: 3, DFOW: 'Define Work 3' }
    ];

    component.setDefine();

    expect(component.deliverDetailsForm.get('defineItems').value).toEqual([
      { id: 1, DFOW: 'Define Work 1' },
      { id: 2, DFOW: 'Define Work 2' }
    ]);
  });

  it('should handle setlocation', () => {
    component.selectedTemplate = {
      location: 1
    };
    component.locationList = [
      { id: 1, locationPath: 'Location 1' },
      { id: 2, locationPath: 'Location 2' }
    ];

    component.setlocation();

    expect(component.deliverDetailsForm.get('LocationId').value).toEqual([
      { id: 1, locationPath: 'Location 1' }
    ]);
  });

  it('should handle setTimeZone', () => {
    component.selectedTemplate = {
      time_zone: 1
    };
    component.timezoneList = [
      { id: 1, location: 'America/New_York' },
      { id: 2, location: 'America/Los_Angeles' }
    ] as any;

    component.setTimeZone();

    expect(component.deliverDetailsForm.get('TimeZoneId').value).toEqual([
      { id: 1, location: 'America/New_York' }
    ]);
  });

  it('should handle setMember with guest user', () => {
    const mockResponse = {
      data: [
        {
          id: 1,
          User: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
          isGuestUser: true
        },
        {
          id: 2,
          User: { firstName: null, lastName: null, email: '<EMAIL>' },
          isGuestUser: false
        }
      ]
    };
    component.selectedTemplate = {
      responsible_person: JSON.stringify([1, 2])
    };
    component.authUser = { User: { email: '<EMAIL>' } };

    // Mock the service method
    const mockListAllMember = jest.fn().mockReturnValue(of(mockResponse));
    projectService.listAllMember = mockListAllMember;

    component.setMember();

    expect(mockListAllMember).toHaveBeenCalledWith({
      ProjectId: '123',
      ParentCompanyId: '456'
    });
  });

  // ===== SAVE AS TEMPLATE TESTS =====
  it('should handle saveAsTemplate with invalid form', () => {
    component.deliverDetailsForm.get('description').setErrors({ required: true });

    component.saveAsTemplate();

    expect(component.submitted).toBe(true);
    expect(component.formSubmitted).toBe(false);
  });

  it('should handle saveAsTemplate with crane request validation error', () => {
    component.deliverDetailsForm.patchValue({
      description: 'Test',
      isAssociatedWithCraneRequest: true,
      cranePickUpLocation: '',
      craneDropOffLocation: ''
    });

    component.saveAsTemplate();

    expect(toastrService.error).toHaveBeenCalledWith('Please enter Picking From and Picking To');
    expect(component.formSubmitted).toBe(false);
  });

  it('should handle saveAsTemplate successfully', () => {
    const mockResponse = { message: 'Template saved successfully' };
    bookingTemplatesService.saveTemplate.mockReturnValue(of(mockResponse));
    jest.spyOn(component, 'constructFormData').mockReturnValue({
      companies: [1],
      persons: [1],
      define: [1],
      equipments: [1],
      escortCondition: false
    });
    jest.spyOn(component, 'saveTemplatePayload').mockReturnValue({
      template_name: 'Test Template',
      description: 'Test Description'
    } as any);
    jest.spyOn(component, 'formReset').mockImplementation(() => {});
    jest.spyOn(component, 'resetForm').mockImplementation(() => {});

    component.deliverDetailsForm.patchValue({
      description: 'Test Description',
      TimeZoneId: [{ id: 1 }],
      inspectionDate: new Date(),
      inspectionStart: new Date(),
      inspectionEnd: new Date(),
      endDate: new Date(),
      isAssociatedWithCraneRequest: false
    });

    component.saveAsTemplate();

    expect(bookingTemplatesService.saveTemplate).toHaveBeenCalled();
    expect(toastrService.success).toHaveBeenCalledWith('Booking Template Saved Successfully', 'Success');
    expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Created inspection Booking Template');
  });

  it('should handle saveAsTemplate error', () => {
    const mockError = { message: { statusCode: 400 } };
    bookingTemplatesService.saveTemplate.mockReturnValue(throwError(() => mockError));
    jest.spyOn(component, 'constructFormData').mockReturnValue({
      companies: [1],
      persons: [1],
      define: [1],
      equipments: [1],
      escortCondition: false
    });

    // Create a spy for handleTemplateError that actually calls the real method
    const handleTemplateErrorSpy = jest.spyOn(component, 'handleTemplateError');
    jest.spyOn(component, 'formReset').mockImplementation(() => {});

    component.deliverDetailsForm.patchValue({
      description: 'Test Description',
      TimeZoneId: [{ id: 1 }],
      inspectionDate: new Date(),
      inspectionStart: new Date(),
      inspectionEnd: new Date(),
      endDate: new Date(),
      isAssociatedWithCraneRequest: false
    });

    component.saveAsTemplate();

    expect(handleTemplateErrorSpy).toHaveBeenCalledWith(mockError);
    expect(component.formReset).toHaveBeenCalled();
  });

  // ===== ADDITIONAL UTILITY AND EDGE CASE TESTS =====
  it('should handle constructFormData', () => {
    component.deliverDetailsForm.patchValue({
      companyItems: [{ id: 1 }, { id: 2 }],
      person: [{ id: 1 }],
      defineItems: [{ id: 1 }],
      EquipmentId: [{ id: 1 }],
      escort: null
    });

    const result = component.constructFormData();

    expect(result.companies).toEqual([1, 2]);
    expect(result.persons).toEqual([1]);
    expect(result.define).toEqual([1]);
    expect(result.equipments).toEqual([1]);
    expect(result.escortCondition).toBe(true);
  });

  it('should handle saveTemplatePayload', () => {
    component.templateName = 'Test Template';
    component.timeZoneValues = 1;
    component.selectedLocationId = 1;
    component.monthlyDate = '15';

    const mockParams = {
      formValue: {
        description: 'Test Description',
        notes: 'Test Notes',
        GateId: 'gate1',
        isAssociatedWithCraneRequest: false,
        recurrence: 'Monthly',
        dateOfMonth: '10',
        monthlyRepeatType: 'First Monday',
        repeatEveryCount: 1,
        repeatEveryType: 'Month',
        chosenDateOfMonth: 1,
        cranePickUpLocation: '',
        craneDropOffLocation: ''
      },
      companies: [1],
      persons: [1],
      define: [1],
      equipments: [1],
      inspectionStart: new Date(),
      inspectionEnd: new Date(),
      escortCondition: false
    };

    const result = component.saveTemplatePayload(mockParams);

    expect(result.template_name).toBe('Test Template');
    expect(result.description).toBe('Test Description');
    expect(result.template_type).toBe('inspection');
    expect(result.ProjectId).toBe('123');
    expect(result.ParentCompanyId).toBe('456');
  });

  it('should handle handleTemplateError with different error types', () => {
    // Test with statusCode 400
    const error400 = { message: { statusCode: 400 } };
    jest.spyOn(component, 'showError').mockImplementation(() => {});
    component.handleTemplateError(error400);
    expect(component.showError).toHaveBeenCalledWith(error400);

    // Test with no message
    const errorNoMessage = { message: null };
    component.handleTemplateError(errorNoMessage);
    expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');

    // Test with overlaps message
    const errorOverlaps = { message: 'Time slot overlaps with existing booking' };
    component.handleTemplateError(errorOverlaps);
    expect(toastrService.error).toHaveBeenCalledWith('Time slot overlaps with existing booking', 'OOPS!');

    // Test with other message
    const errorOther = { message: 'Error: Something went wrong' };
    component.handleTemplateError(errorOther);
    expect(toastrService.error).toHaveBeenCalledWith('Something went wrong', 'OOPS!');
  });

  it('should handle setRecurrence with recurrence details', () => {
    component.selectedTemplate = {
      recurrence: JSON.stringify({
        recurrence: 'Monthly',
        dateOfMonth: '15',
        monthlyRepeatType: 'First Monday',
        repeatEveryCount: '2',
        repeatEveryType: 'Months',
        chosenDateOfMonth: true,
        endDate: '2024-12-31'
      })
    };
    component.monthlyDayOfWeek = 'First Monday';
    component.monthlyLastDayOfWeek = 'Last Monday';

    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'changeRecurrenceCount').mockImplementation(() => {});
    jest.spyOn(component, 'chooseRepeatEveryType').mockImplementation(() => {});

    component.setRecurrence();

    expect(component.deliverDetailsForm.get('recurrence').value).toBe('Monthly');
    expect(component.deliverDetailsForm.get('dateOfMonth').value).toBe('15');
    expect(component.deliverDetailsForm.get('monthlyRepeatType').value).toBe('First Monday');
    expect(component.deliverDetailsForm.get('repeatEveryCount').value).toBe('2');
    expect(component.deliverDetailsForm.get('repeatEveryType').value).toBe('Months');
    expect(component.deliverDetailsForm.get('chosenDateOfMonth').value).toBe(1);
    expect(component.changeRecurrenceCount).toHaveBeenCalledWith(2);
    expect(component.chooseRepeatEveryType).toHaveBeenCalled();
  });

  it('should handle setRecurrence with different monthly repeat types', () => {
    component.selectedTemplate = {
      recurrence: JSON.stringify({
        recurrence: 'Monthly',
        monthlyRepeatType: 'Last Monday',
        chosenDateOfMonth: false
      })
    };
    component.monthlyDayOfWeek = 'First Monday';
    component.monthlyLastDayOfWeek = 'Last Monday';

    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});
    jest.spyOn(component, 'changeRecurrenceCount').mockImplementation(() => {});
    jest.spyOn(component, 'chooseRepeatEveryType').mockImplementation(() => {});

    component.setRecurrence();

    expect(component.deliverDetailsForm.get('chosenDateOfMonth').value).toBe(3);
  });

  // ===== COMPLEX BUSINESS LOGIC TESTS =====
  it('should handle prepareInspectionData with missing company items', () => {
    const params = {
      newNdrFormValue: {
        companyItems: [],
        person: [{ id: 1 }],
        defineItems: [{ id: 1 }],
        TimeZoneId: [{ id: 1 }],
        EquipmentId: [{ id: 1 }]
      },
      companies: [],
      persons: [],
      define: [],
      newTimeZoneDetails: [],
      equipments: []
    };

    jest.spyOn(component, 'formReset').mockImplementation(() => {});

    const result = component.prepareInspectionData(params);

    expect(result).toBe(false);
    expect(toastrService.error).toHaveBeenCalledWith('Responsible Company is required');
    expect(component.formReset).toHaveBeenCalled();
  });

  it('should handle prepareInspectionData with missing person', () => {
    const params = {
      newNdrFormValue: {
        companyItems: [{ id: 1 }],
        person: [],
        defineItems: [{ id: 1 }],
        TimeZoneId: [{ id: 1 }],
        EquipmentId: [{ id: 1 }]
      },
      companies: [],
      persons: [],
      define: [],
      newTimeZoneDetails: [],
      equipments: []
    };

    jest.spyOn(component, 'formReset').mockImplementation(() => {});

    const result = component.prepareInspectionData(params);

    expect(result).toBe(false);
    expect(toastrService.error).toHaveBeenCalledWith('Responsible Person is required');
    expect(component.formReset).toHaveBeenCalled();
  });

  it('should handle prepareInspectionData successfully', () => {
    const params = {
      newNdrFormValue: {
        companyItems: [{ id: 1 }],
        person: [{ id: 1 }],
        defineItems: [{ id: 1 }],
        TimeZoneId: [{ id: 1 }],
        EquipmentId: [{ id: 1 }]
      },
      companies: [],
      persons: [],
      define: [],
      newTimeZoneDetails: [{ id: 1 }],
      equipments: []
    };

    const result = component.prepareInspectionData(params);

    expect(result).toBe(true);
    expect(params.companies).toEqual([1]);
    expect(params.persons).toEqual([1]);
    expect(params.define).toEqual([1]);
    expect(params.equipments).toEqual([1]);
    expect(component.timeZoneValues).toBe(1);
  });

  it('should handle constructPayload', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.selectedLocationId = 'loc1';
    component.timeZoneValues = 1;
    component.selectedVehicleType = 'Truck';
    jest.spyOn(component, 'sortWeekDays').mockReturnValue(['Monday', 'Tuesday']);

    const params = {
      newNdrFormValue: {
        description: 'Test Description',
        escort: null,
        GateId: 'gate1',
        notes: 'Test Notes',
        isAssociatedWithCraneRequest: true,
        cranePickUpLocation: '  Location A  ',
        craneDropOffLocation: '  Location B  ',
        CraneRequestId: 'CR123',
        recurrence: 'Weekly',
        chosenDateOfMonth: 1,
        dateOfMonth: '15',
        monthlyRepeatType: 'First Monday',
        days: ['Monday', 'Tuesday'],
        repeatEveryType: 'Week',
        repeatEveryCount: 1,
        inspectionType: 'Standard',
        originationAddress: '123 Main St'
      },
      inspectionStart: new Date(),
      inspectionEnd: new Date(),
      companies: [1],
      persons: [1],
      define: [1],
      equipments: [1],
      startPicker: '10:00',
      endPicker: '11:00'
    };

    const result = component.constructPayload(params);

    expect(result.description).toBe('Test Description');
    expect(result.escort).toBe(false);
    expect(result.ProjectId).toBe('123');
    expect(result.ParentCompanyId).toBe('456');
    expect(result.isAssociatedWithCraneRequest).toBe(true);
    expect(result.cranePickUpLocation).toBe('Location A');
    expect(result.craneDropOffLocation).toBe('Location B');
    expect(result.CraneRequestId).toBe('CR123');
    expect(result.requestType).toBe('inspectionRequest');
    expect(result.LocationId).toBe('loc1');
    expect(result.vehicleType).toBe('Truck');
  });

  it('should handle constructPayload with non-crane request', () => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.selectedLocationId = 'loc1';
    component.timeZoneValues = 1;

    const params = {
      newNdrFormValue: {
        description: 'Test Description',
        escort: false,
        GateId: 'gate1',
        notes: 'Test Notes',
        isAssociatedWithCraneRequest: false,
        recurrence: 'Does Not Repeat',
        chosenDateOfMonth: false,
        dateOfMonth: '',
        monthlyRepeatType: '',
        days: [],
        repeatEveryType: null,
        repeatEveryCount: null,
        inspectionType: 'Standard',
        originationAddress: ''
      },
      inspectionStart: new Date(),
      inspectionEnd: new Date(),
      companies: [1],
      persons: [1],
      define: [1],
      equipments: [1],
      startPicker: '10:00',
      endPicker: '11:00'
    };

    const result = component.constructPayload(params);

    expect(result.isAssociatedWithCraneRequest).toBe(false);
    expect(result.cranePickUpLocation).toBe(null);
    expect(result.craneDropOffLocation).toBe(null);
    expect(result.CraneRequestId).toBe(null);
    expect(result.chosenDateOfMonth).toBe(false);
    expect(result.repeatEveryType).toBe(null);
    expect(result.repeatEveryCount).toBe(null);
  });

  // ===== VALIDATION AND ERROR HANDLING TESTS =====
  it('should handle validateRecurrencePayload with same start and end time', () => {
    const payload = {
      startPicker: '10:00',
      endPicker: '10:00'
    };
    jest.spyOn(component, 'showErrorMessage').mockImplementation(() => {});

    const result = component.validateRecurrencePayload(payload);

    expect(result).toBe(false);
    expect(component.showErrorMessage).toHaveBeenCalledWith('inspection Start time and End time should not be the same');
  });

  it('should handle validateRecurrencePayload with start time greater than end time', () => {
    const payload = {
      startPicker: '11:00',
      endPicker: '10:00'
    };
    jest.spyOn(component, 'showErrorMessage').mockImplementation(() => {});

    const result = component.validateRecurrencePayload(payload);

    expect(result).toBe(false);
    expect(component.showErrorMessage).toHaveBeenCalledWith('Please enter From Time lesser than To Time');
  });

  it('should handle validateRecurrencePayload with invalid date range', () => {
    const payload = {
      startPicker: '10:00',
      endPicker: '11:00',
      recurrence: 'Weekly',
      craneDeliveryStart: '2024-12-01',
      craneDeliveryEnd: '2024-11-30'
    };
    jest.spyOn(component, 'showErrorMessage').mockImplementation(() => {});

    const result = component.validateRecurrencePayload(payload);

    expect(result).toBe(false);
    expect(component.showErrorMessage).toHaveBeenCalledWith('Please enter End Date greater than Start Date');
  });

  it('should handle validateRecurrencePayload successfully', () => {
    const payload = {
      startPicker: '10:00',
      endPicker: '11:00',
      recurrence: 'Does Not Repeat'
    };

    const result = component.validateRecurrencePayload(payload);

    expect(result).toBe(true);
  });

  // ===== MONTHLY RECURRENCE TESTS =====
  it('should handle showMonthlyRecurrence', () => {
    component.deliverDetailsForm.patchValue({
      inspectionDate: '2024-12-01',
      endDate: '2024-12-31'
    });
    jest.spyOn(component, 'generateWeekDates').mockImplementation(() => {});
    jest.spyOn(component, 'setMonthlyOrYearlyRecurrenceOption').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});
    jest.spyOn(component, 'updateWeeklyDates').mockImplementation(() => {});

    // Restore the original method for this test
    (component.showMonthlyRecurrence as jest.Mock).mockRestore();
    component.showMonthlyRecurrence();

    expect(component.generateWeekDates).toHaveBeenCalled();
    expect(component.setMonthlyOrYearlyRecurrenceOption).toHaveBeenCalled();
    expect(component.occurMessage).toHaveBeenCalled();
    expect(component.updateWeeklyDates).toHaveBeenCalled();
  });

  it('should handle setMonthlyOrYearlyRecurrenceOption for different choices', () => {
    component.deliverDetailsForm.patchValue({
      inspectionDate: '2024-12-01',
      chosenDateOfMonth: 1
    });
    component.monthlyDayOfWeek = 'First Sunday';
    component.monthlyLastDayOfWeek = 'Last Sunday';

    component.setMonthlyOrYearlyRecurrenceOption();
    expect(component.deliverDetailsForm.get('monthlyRepeatType').value).toBe(null);

    component.deliverDetailsForm.patchValue({ chosenDateOfMonth: 2 });
    component.setMonthlyOrYearlyRecurrenceOption();
    expect(component.deliverDetailsForm.get('monthlyRepeatType').value).toBe('First Sunday');

    component.deliverDetailsForm.patchValue({ chosenDateOfMonth: 3 });
    component.setMonthlyOrYearlyRecurrenceOption();
    expect(component.deliverDetailsForm.get('monthlyRepeatType').value).toBe('Last Sunday');
  });

  it('should handle updateWeeklyDates', () => {
    component.deliverDetailsForm.patchValue({
      endDate: '2024-12-05'
    });
    component.weekDates = [
      { name: 'Sun', date: '01', fullDate: '2024-12-01' }
    ];

    component.updateWeeklyDates();

    expect(component.weekDates.length).toBeGreaterThan(1);
  });

  it('should handle updateWeeklyDates with empty weekDates', () => {
    component.deliverDetailsForm.patchValue({
      endDate: '2024-12-05',
      inspectionDate: '2024-12-01'
    });
    component.weekDates = [];

    component.updateWeeklyDates();

    // The method should generate weekDates when empty
    expect(component.weekDates.length).toBeGreaterThanOrEqual(0);
  });

  it('should handle updateFormValidation', () => {
    component.deliverDetailsForm.patchValue({ chosenDateOfMonth: 1 });

    component.updateFormValidation();

    const dateOfMonth = component.deliverDetailsForm.get('dateOfMonth');
    const monthlyRepeatType = component.deliverDetailsForm.get('monthlyRepeatType');

    expect(dateOfMonth.hasError('required')).toBe(true);
    expect(monthlyRepeatType.hasError('required')).toBe(false);

    component.deliverDetailsForm.patchValue({ chosenDateOfMonth: 2 });
    component.updateFormValidation();

    expect(dateOfMonth.hasError('required')).toBe(false);
    expect(monthlyRepeatType.hasError('required')).toBe(true);
  });

  // ===== OCCURRENCE MESSAGE TESTS =====
  it('should handle repeatEveryTypeMessage for different types', () => {
    component.deliverDetailsForm.patchValue({
      repeatEveryType: 'Day',
      repeatEveryCount: 1
    });
    component.repeatEveryTypeMessage();
    expect(component.message).toBe('Occurs every day');

    component.deliverDetailsForm.patchValue({
      repeatEveryType: 'Days',
      repeatEveryCount: 2
    });
    component.repeatEveryTypeMessage();
    expect(component.message).toBe('Occurs every other day');

    component.deliverDetailsForm.patchValue({
      repeatEveryType: 'Days',
      repeatEveryCount: 3
    });
    component.repeatEveryTypeMessage();
    expect(component.message).toBe('Occurs every 3 days');
  });

  it('should handle occurMessage for weekly recurrence', () => {
    component.deliverDetailsForm.patchValue({
      repeatEveryType: 'Week',
      endDate: '2024-12-31'
    });
    component.weekDays = [
      { value: 'Monday', checked: true },
      { value: 'Tuesday', checked: true },
      { value: 'Wednesday', checked: false }
    ];

    component.occurMessage();

    expect(component.message).toContain('Occurs every Monday,Tuesday');
    expect(component.message).toContain('until December 31, 2024');
  });

  it('should handle occurMessage for monthly recurrence', () => {
    component.deliverDetailsForm.patchValue({
      repeatEveryType: 'Month',
      chosenDateOfMonth: 1,
      endDate: '2024-12-31'
    });
    component.monthlyDate = '15';

    component.occurMessage();

    expect(component.message).toContain('Occurs on day 15');
    expect(component.message).toContain('until December 31, 2024');
  });

  // ===== CHOOSE REPEAT EVERY TYPE TESTS =====
  it('should handle chooseRepeatEveryType for Day', () => {
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});
    component.weekDays = [
      { value: 'Monday', checked: false },
      { value: 'Tuesday', checked: false }
    ];

    component.chooseRepeatEveryType('Day', null);

    expect(component.deliverDetailsForm.get('recurrence').value).toBe('Daily');
    expect(component.isRepeatWithSingleRecurrence).toBe(true);
    expect(component.isRepeatWithMultipleRecurrence).toBe(false);
    expect(component.weekDays.every((day: any) => day.checked)).toBe(true);
  });

  it('should handle chooseRepeatEveryType for Week with existing days', () => {
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});
    const eventDetail = { days: ['Monday', 'Wednesday'] };
    component.weekDays = [
      { value: 'Monday', checked: false },
      { value: 'Tuesday', checked: false },
      { value: 'Wednesday', checked: false }
    ];

    component.chooseRepeatEveryType('Week', eventDetail);

    expect(component.deliverDetailsForm.get('recurrence').value).toBe('Weekly');
    const mondayDay = component.weekDays.find((day: any) => day.value === 'Monday');
    const wednesdayDay = component.weekDays.find((day: any) => day.value === 'Wednesday');
    const tuesdayDay = component.weekDays.find((day: any) => day.value === 'Tuesday');

    expect(mondayDay.checked).toBe(true);
    expect(wednesdayDay.checked).toBe(true);
    expect(tuesdayDay.checked).toBe(false);
  });

  it('should handle chooseRepeatEveryType for Months', () => {
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});
    component.deliverDetailsForm.patchValue({ repeatEveryCount: 1 });

    component.chooseRepeatEveryType('Month', null);

    expect(component.deliverDetailsForm.get('recurrence').value).toBe('Monthly');
    expect(component.isRepeatWithSingleRecurrence).toBe(true);
    expect(component.isRepeatWithMultipleRecurrence).toBe(false);
  });

  // ===== CLOSE AND RESET TESTS =====
  it('should handle close with dirty form', () => {
    const mockTemplate = {} as any;
    component.deliverDetailsForm.markAsTouched();
    component.deliverDetailsForm.markAsDirty();
    jest.spyOn(component, 'openConfirmationModalPopup').mockImplementation(() => {});

    component.close(mockTemplate);

    expect(component.openConfirmationModalPopup).toHaveBeenCalledWith(mockTemplate);
  });

  it('should handle close with clean form', () => {
    const mockTemplate = {} as any;
    jest.spyOn(component, 'resetForm').mockImplementation(() => {});

    component.close(mockTemplate);

    expect(component.resetForm).toHaveBeenCalledWith('yes');
  });

  it('should handle openConfirmationModalPopup', () => {
    const mockTemplate = {} as any;

    component.openConfirmationModalPopup(mockTemplate);

    expect(modalService.show).toHaveBeenCalledWith(mockTemplate, {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
    });
  });

  it('should handle resetForm with no action', () => {
    component.resetForm('no');
    expect(component.modalRef1.hide).toHaveBeenCalled();
  });

  it('should handle resetForm with yes action', () => {
    jest.spyOn(component, 'setDefaultPerson').mockImplementation(() => {});
    component.templateName = 'Test Template';

    component.resetForm('yes');

    expect(component.deliverDetailsForm.get('GateId').value).toBe('');
    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
    expect(component.editSubmitted).toBe(false);
    expect(component.modalRef.hide).toHaveBeenCalled();
    expect(component.NDRTimingChanged).toBe(false);
    expect(component.templateName).toBe('');
    expect(component.setDefaultPerson).toHaveBeenCalled();
  });

  // ===== ADDITIONAL EDGE CASE TESTS FOR 90%+ COVERAGE =====
  it('should handle createinspection with valid payload', () => {
    const mockPayload = {
      newNdrFormValue: { description: 'Test Description' },
      inspectionStart: new Date(),
      inspectionEnd: new Date(),
      companies: [1],
      persons: [1],
      define: [1],
      newTimeZoneDetails: { id: 1 },
      startPicker: '10:00',
      endPicker: '11:00',
      weekStartDate: '2024-12-01',
      weekEndDate: '2024-12-07',
      equipments: [1]
    };
    const mockResponse = { message: 'Success' };
    deliveryService.createInspectionNDR.mockReturnValue(of(mockResponse));
    jest.spyOn(component, 'formReset').mockImplementation(() => {});

    component.createinspection(mockPayload);

    expect(deliveryService.createInspectionNDR).toHaveBeenCalledWith(mockPayload);
    expect(toastrService.success).toHaveBeenCalledWith('inspection Request Created Successfully', 'Success');
    expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Created inspection Request');
    expect(component.formReset).toHaveBeenCalled();
  });

  it('should handle createinspection error', () => {
    const mockPayload = {
      newNdrFormValue: { description: 'Test' },
      inspectionStart: new Date(),
      inspectionEnd: new Date(),
      companies: [],
      persons: [],
      define: [],
      newTimeZoneDetails: null,
      startPicker: '10:00',
      endPicker: '11:00',
      weekStartDate: '2024-12-01',
      weekEndDate: '2024-12-07',
      equipments: []
    };
    const mockError = { message: { statusCode: 400 } };
    deliveryService.createInspectionNDR.mockReturnValue(throwError(() => mockError));
    jest.spyOn(component, 'showError').mockImplementation(() => {});
    jest.spyOn(component, 'formReset').mockImplementation(() => {});

    component.createinspection(mockPayload);

    expect(component.showError).toHaveBeenCalledWith(mockError);
    expect(component.formReset).toHaveBeenCalled();
  });

  it('should handle showErrorMessage', () => {
    const message = 'Test error message';
    component.showErrorMessage(message);

    expect(toastrService.error).toHaveBeenCalledWith(message);
    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
  });

  it('should handle sortWeekDays with mixed case days', () => {
    const days = ['wednesday', 'MONDAY', 'Friday'];
    const result = component.sortWeekDays(days);

    expect(result).toEqual(['Monday', 'Wednesday', 'Friday']);
  });

  it('should handle sortWeekDays with duplicate days', () => {
    const days = ['Monday', 'Monday', 'Tuesday'];
    const result = component.sortWeekDays(days);

    expect(result).toEqual(['Monday', 'Tuesday']);
  });

  it('should handle changeRecurrenceCount with different values', () => {
    jest.spyOn(component, 'updateRecurrenceFlags').mockImplementation(() => {});
    jest.spyOn(component, 'updateRepeatEveryType').mockImplementation(() => {});
    jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

    component.selectedRecurrence = 'Daily';
    component.changeRecurrenceCount(3);

    expect(component.updateRecurrenceFlags).toHaveBeenCalledWith('Daily', 3);
    expect(component.updateRepeatEveryType).toHaveBeenCalledWith('Daily', 3);
    expect(component.occurMessage).toHaveBeenCalled();
  });

  it('should handle changeMonthlyRecurrence', () => {
    jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});

    component.changeMonthlyRecurrence();

    expect(component.showMonthlyRecurrence).toHaveBeenCalled();
  });

  it('should handle inspectionType change', () => {
    const inspectionType = 'Standard';
    component.deliverDetailsForm.patchValue({ inspectionType: inspectionType });

    expect(component.deliverDetailsForm.get('inspectionType').value).toBe(inspectionType);
  });

  it('should handle closeModalPopup', () => {
    jest.spyOn(component, 'locationSelected').mockImplementation(() => {});
    component.selectedLocationId = 1;

    component.closeModalPopup();

    expect(component.modalRef.hide).toHaveBeenCalled();
    expect(component.locationSelected).toHaveBeenCalledWith({ id: 1 });
  });

  it('should handle formReset', () => {
    component.formReset();

    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
    expect(component.editSubmitted).toBe(false);
  });

  it('should handle address change through form', () => {
    const address = '123 Main St';
    component.deliverDetailsForm.patchValue({ originationAddress: address });

    expect(component.deliverDetailsForm.get('originationAddress').value).toBe(address);
  });

  it('should handle numberOnly with valid input', () => {
    const mockEvent = {
      which: 49, // '1'
      keyCode: 49,
      preventDefault: jest.fn()
    };

    const result = component.numberOnly(mockEvent);

    expect(result).toBe(true);
    expect(mockEvent.preventDefault).not.toHaveBeenCalled();
  });

  it('should handle numberOnly with invalid input', () => {
    const mockEvent = {
      which: 65, // 'A'
      keyCode: 65,
      preventDefault: jest.fn()
    };

    const result = component.numberOnly(mockEvent);

    expect(result).toBe(false);
    expect(mockEvent.preventDefault).toHaveBeenCalled();
  });

  it('should handle checkStringEmptyValues with empty values', () => {
    component.deliverDetailsForm.patchValue({
      description: '',
      notes: '   ',
      cranePickUpLocation: null
    });

    const result = component.checkStringEmptyValues({ description: '', notes: '   ' });

    expect(result).toBe(true);
  });

  it('should handle checkStringEmptyValues with valid values', () => {
    component.deliverDetailsForm.patchValue({
      description: 'Valid description',
      notes: 'Valid notes',
      cranePickUpLocation: 'Valid location'
    });

    const result = component.checkStringEmptyValues({ description: 'Valid description', notes: 'Valid notes' });

    expect(result).toBe(false);
  });

  it('should handle selectDuration', () => {
    const duration = 120;
    jest.spyOn(component, 'getAvailableSlots').mockImplementation(() => {});

    component.selectDuration(duration);

    expect(component.selectedMinutes).toBe(duration);
    expect(component.getAvailableSlots).toHaveBeenCalled();
  });

  it('should handle changeDate', () => {
    const date = '2024-12-15';
    jest.spyOn(component, 'getAvailableSlots').mockImplementation(() => {});

    component.changeDate(date);

    expect(component.getAvailableSlots).toHaveBeenCalledWith(date);
  });

  it('should handle inspectionEndTimeChangeDetection', () => {
    jest.spyOn(component, 'getAvailableSlots').mockImplementation(() => {});
    component.deliverDetailsForm.patchValue({
      inspectionDate: '2024-12-01'
    });

    component.inspectionEndTimeChangeDetection();

    expect(component.NDRTimingChanged).toBe(true);
    expect(component.getAvailableSlots).toHaveBeenCalledWith('2024-12-01');
  });

  it('should handle updateDropdownState', () => {
    // Test that the method can be called without errors
    expect(() => component.updateDropdownState()).not.toThrow();
  });
});
