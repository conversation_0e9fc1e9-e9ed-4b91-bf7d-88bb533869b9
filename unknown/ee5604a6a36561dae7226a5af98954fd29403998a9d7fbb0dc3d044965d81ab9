import { Component, TemplateRef } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { ProjectService } from '../services/profile/project.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { MixpanelService } from '../services/mixpanel.service';

@Component({
  selector: 'app-gates',
  templateUrl: './gates.component.html',
  })
export class GatesComponent {
  public currentPageNo = 1;

  public pageSize = 25;

  public userData = [];

  public gateForm: UntypedFormGroup;

  public gateEditForm: UntypedFormGroup;

  public deactiveForm: UntypedFormGroup;

  public submitted = false;

  public formSubmitted = false;

  public formEditSubmitted = false;

  public editSubmitted = false;

  public ProjectId = '';

  public deleteRecord: any = {};

  public deleteSubmitted = false;

  public selectAll = false;

  public deleteIndex: any = [];

  public remove = false;

  public gateList = [];

  public editIndex = 0;

  public totalCount = 0;

  public loader = true;

  public currentDeleteId: any;

  public lastId: any = 0;

  public search = '';

  public ParentCompanyId: any;

  public sort = 'DESC';

  public sortColumn = 'id';

  public showSearchbar = false;

  public authUser: any = {};

  public deliveryGateRequestList = [];

  public deliveryGateList = [];

  public deactiveLoader: boolean;

  public selectedValue = 0;

  public arr = [];

  public payloadvalues: any;

  public getMappedRequestLoader: boolean = false;

  public gateData: any;

  public deactivateGateLoader: boolean;

  public constructor(
    private readonly modalService: BsModalService,
    public projectService: ProjectService,
    public modalRef: BsModalRef,
    private readonly titleService: Title,
    public modalRef1: BsModalRef,
    private readonly mixpanelService: MixpanelService,
    private readonly deliveryService: DeliveryService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly toastr: ToastrService,
  ) {
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
      }
    });

    this.titleService.setTitle('Follo - Gates');
    this.projectService.projectId.subscribe((res): void => {
      this.ProjectId = res;
      if (res !== undefined && res !== null && res !== '') {
        this.loader = true;
        this.getGateList();
      }
    });
    this.projectService.ParentCompanyId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ParentCompanyId = res;
        this.getGateList();
      }
    });
    this.gateNewForm();
    this.gateEditFormGroup();
  }

  public allowAlphaNumericForGateName(event: { which: any; keyCode: any }): boolean {
    const keyCodeNumber = event.which ? event.which : event.keyCode;
    const capsAlphabetCondition1 = keyCodeNumber >= 65 && keyCodeNumber <= 90;
    const smallAlphabetCondition2 = keyCodeNumber >= 97 && keyCodeNumber <= 128;
    const third = keyCodeNumber === 8 || keyCodeNumber === 32;
    if (third) {
      return true;
    }
    if (
      keyCodeNumber > 31
      && (keyCodeNumber < 48 || keyCodeNumber > 57)
      && !capsAlphabetCondition1
      && !smallAlphabetCondition2
    ) {
      return false;
    }

    return true;
  }

  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-md new-gate-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'edit':
          this.openEditModal(data, item);
          break;
        case 'clear':
          this.clear();
          break;
        default:
          break;
      }
    }
  }

  public openDeactivateModal(template: TemplateRef<any>, data, event): void {
    if (!event) {
      this.gateData = data;
      this.modalRef = this.modalService.show(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-md new-gate-popup custom-modal deactivate-modal',
      });
      this.deliveryGateRequestList = [];
      this.getMappedRequestLoader = true;
      const payload = {
        ProjectId: this.ProjectId,
        id: data.gateAutoId,
        gateName: data.gateName,
      };
      this.projectService.getMappedRequests(payload).subscribe((response: any): void => {
        if (response) {
          this.deliveryGateRequestList = response.data.mappedRequest;
          this.deliveryGateList = response.data.gates;
          this.getMappedRequestLoader = false;
        }
      });
    }
    if (event) {
      this.activateGate(data);
    }
  }

  public activateGate(data): void {
    this.gateData = data;
    const payload = {
      id: data.id,
      gateName: data.gateName.trim(),
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      isActive: true,
    };
    this.projectService.updateGate(payload).subscribe({
      next: (response): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.getGateList();
        }
      },
      error: (activateGateErr): void => {
        if (activateGateErr.message?.statusCode === 400) {
          this.showError(activateGateErr);
        } else if (!activateGateErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(activateGateErr.message, 'OOPS!');
        }
      },
    });
  }

  public switchGate(data, gateId): void {
    const gateObject = data;
    if (+data.gateDetails[0].Gate.id !== +gateId) {
      gateObject.changedGateId = +gateId;
    } else {
      gateObject.changedGateId = null;
    }
    return gateObject;
  }

  public deactivateGate(): void {
    this.deactivateGateLoader = true;
    let filterChangedGateData = [];
    filterChangedGateData = this.deliveryGateRequestList.filter(
      (obj): any => obj.changedGateId && obj.changedGateId !== null,
    );
    if (
      filterChangedGateData
      && filterChangedGateData.length > 0
      && this.deliveryGateRequestList.length === filterChangedGateData.length
    ) {
      const payload = {
        id: this.gateData.id,
        gateSwitchedRequests: filterChangedGateData,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.projectService.deactivateGate(payload).subscribe({
        next: (response): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.getGateList();
            filterChangedGateData = [];
            this.modalRef.hide();
            this.deactivateGateLoader = false;
          }
        },
        error: (deactivateGateErr): void => {
          if (deactivateGateErr.message?.statusCode === 400) {
            this.showError(deactivateGateErr);
          } else if (!deactivateGateErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deactivateGateErr.message, 'OOPS!');
          }
        },
      });
    } else if (this.deliveryGateRequestList.length === 0) {
      this.deactivateGateLoader = true;
      const payload = {
        id: this.gateData.id,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.projectService.deactivateGate(payload).subscribe({
        next: (response): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.getGateList();
            this.modalRef.hide();
            this.deactivateGateLoader = false;
          }
        },
        error: (deactivateGateErr): void => {
          if (deactivateGateErr.message?.statusCode === 400) {
            this.showError(deactivateGateErr);
          } else if (!deactivateGateErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deactivateGateErr.message, 'OOPS!');
          }
        },
      });
    } else {
      this.deactivateGateLoader = false;
    }
  }

  public openEditModal(template: TemplateRef<any>, index: number): void {
    this.editIndex = index;
    this.gateEditForm.get('gateName').setValue(this.gateList[index].gateName);
    this.gateEditForm.get('id').setValue(this.gateList[index].id);
    this.gateEditForm.get('gateAutoId').setValue(this.gateList[index].gateAutoId);
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-md new-gate-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }

  public changePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.getGateList();
  }

  public changePageNo(pageNo: number): void {
    this.currentPageNo = pageNo;
    this.getGateList();
  }

  public openDeleteModal(index: number, template: TemplateRef<any>): void {
    if (index !== -1) {
      this.deleteIndex[0] = this.gateList[index].id;
      this.currentDeleteId = index;
      this.remove = false;
    } else if (index === -1) {
      this.remove = true;
    }
    this.openModal(template);
  }

  public checkSelectedRow(): boolean {
    if (this.selectAll) {
      return false;
    }
    const index = this.gateList.findIndex((item): boolean => item.isChecked === true);
    if (index !== -1) {
      return false;
    }
    return true;
  }

  public selectAllGatesData(): void {
    this.selectAll = !this.selectAll;
    if (this.selectAll) {
      this.gateList.forEach((obj, index): void => {
        this.gateList[index].isChecked = true;
        return null;
      });
    } else {
      this.gateList.forEach((obj, index): void => {
        this.gateList[index].isChecked = false;
        return null;
      });
    }
  }

  public removeItem(): void {
    this.deleteSubmitted = true;
    if (this.selectAll) {
      this.deleteGateDetail();
    } else {
      this.gateList.forEach((element): void => {
        if (element.isChecked) {
          this.deleteIndex.push(element.id);
        }
      });
      this.deleteGateDetail();
    }
  }

  public setSelectedItem(index: string | number): void {
    this.gateList[index].isChecked = !this.gateList[index].isChecked;
  }

  public deleteGateDetail(): void {
    this.deleteSubmitted = true;
    this.projectService
      .deleteGate({
        id: this.deleteIndex,
        ProjectId: this.ProjectId,
        isSelectAll: this.selectAll,
        ParentCompanyId: this.ParentCompanyId,
      })
      .subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Deleted Gate');
            this.getGateList();
            this.deleteSubmitted = false;
            this.modalRef.hide();
          }
        },
        error: (deleteGateDetailError): void => {
          this.deleteSubmitted = false;
          this.getGateList();
          if (deleteGateDetailError.message?.statusCode === 400) {
            this.showError(deleteGateDetailError);
          } else if (!deleteGateDetailError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deleteGateDetailError.message, 'OOPS!');
          }
        },
      });
  }

  public gateNewForm(): void {
    this.gateForm = this.formBuilder.group({
      gateName: ['', Validators.required],
    });
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.editSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public gateEditFormGroup(): void {
    this.gateEditForm = this.formBuilder.group({
      id: ['', Validators.required],
      gateName: ['', Validators.required],
      gateAutoId: [''],
    });
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    if (this.gateForm.invalid) {
      this.formSubmitted = false;
      return;
    }
    const payload = {
      gateName: this.gateForm.value.gateName,
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    if (!this.checkStringEmptyValues(this.gateForm.value)) {
      this.projectService.addGate(payload).subscribe({
        next: (response): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Added Gate');
            this.getGateList();
            this.gateForm.reset();
            this.submitted = false;
            this.formSubmitted = false;
            this.modalRef.hide();
          }
        },
        error: (addGateErr): void => {
          if (addGateErr.message?.statusCode === 400) {
            this.showError(addGateErr);
          } else if (!addGateErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(addGateErr.message, 'OOPS!');
          }
          this.submitted = false;
          this.formSubmitted = false;
        },
      });
    } else {
      this.toastr.error('Please Enter valid Gate Name.', 'OOPS!');
      this.submitted = false;
      this.formSubmitted = false;
    }
  }

  public editSubmit(): void {
    this.editSubmitted = true;
    this.formEditSubmitted = true;
    if (this.gateEditForm.invalid) {
      this.formEditSubmitted = false;
      return;
    }
    if (!this.checkStringEmptyValues(this.gateEditForm.value)) {
      const payload = {
        id: this.gateEditForm.value.id,
        gateName: this.gateEditForm.value.gateName.trim(),
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.projectService.updateGate(payload).subscribe({
        next: (response): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Edited Gate');
            this.getGateList();
            this.editSubmitted = false;
            this.formEditSubmitted = false;
            this.modalRef.hide();
          }
        },
        error: (updateGateErr): void => {
          if (updateGateErr.message?.statusCode === 400) {
            this.showError(updateGateErr);
          } else if (!updateGateErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(updateGateErr.message, 'OOPS!');
          }
          this.editSubmitted = false;
          this.formEditSubmitted = false;
        },
      });
    } else {
      this.toastr.error('Please Enter valid Gate Name.', 'OOPS!');
      this.editSubmitted = false;
      this.formEditSubmitted = false;
    }
  }

  public close(template: TemplateRef<any>, form: string): void {
    if (form === 'addGate') {
      if (this.gateForm.dirty) {
        this.openModalPopup(template);
      } else {
        this.resetForm('yes');
      }
    }
    if (form === 'editGate') {
      if (this.gateEditForm.dirty) {
        this.openModalPopup(template);
      } else {
        this.resetForm('yes');
      }
    }
    if (form === 'deactivateGate') {
      this.modalRef.hide();
      this.getGateList();
    }
  }

  public openModalPopup(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public resetAndClose(): void {
    this.gateForm.reset();
    this.submitted = false;
    this.formSubmitted = false;
    this.editSubmitted = false;
    this.formEditSubmitted = false;
    this.modalRef.hide();
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    }
    if (action === 'yes') {
      this.modalRef1.hide();
      this.gateForm.reset();
      this.submitted = false;
      this.formSubmitted = false;
      this.editSubmitted = false;
      this.formEditSubmitted = false;
      this.modalRef.hide();
    }
  }

  public alphaOnly(event: { keyCode: any }): boolean {
    const key = event.keyCode;
    return (key >= 65 && key <= 90) || (key >= 97 && key <= 128) || key === 8 || key === 32;
  }

  public checkStringEmptyValues(formValue: { gateName: string }): boolean {
    if (formValue.gateName.trim() === '') {
      return true;
    }
    return false;
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.currentPageNo = 1;
    this.getGateList();
  }

  public searchGate(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.search = data;
    this.currentPageNo = 1;
    this.getGateList();
  }

  public sortByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getGateList();
  }

  public getGateList(): void {
    this.loader = true;
    if (this.ProjectId && this.ParentCompanyId && this.pageSize && this.currentPageNo) {
      const params = {
        ProjectId: this.ProjectId,
        pageSize: this.pageSize,
        pageNo: this.currentPageNo,
        ParentCompanyId: this.ParentCompanyId,
      };
      const payload = {
        search: this.search,
        sort: this.sort,
        sortByField: this.sortColumn,
        isFilter: false,
        showActivatedAlone: false,
      };
      this.projectService.gateList(params, payload).subscribe((res): void => {
        const responseData = res.data;
        this.lastId = res.lastId;
        this.gateList = responseData.rows;
        if (this.selectAll && this.gateList.length > 0) {
          this.gateList.forEach((obj, index): void => {
            this.gateList[index].isChecked = true;
            return null;
          });
        } else {
          this.selectAll = false;
          this.gateList.forEach((obj, index): void => {
            this.gateList[index].isChecked = false;
            return null;
          });
        }
        this.totalCount = responseData.count;
        this.loader = false;
      });
    }
  }
}
