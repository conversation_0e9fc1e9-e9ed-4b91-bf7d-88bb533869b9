import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CraneRequestDetailViewContentComponent } from './crane-request-detail-view-content.component';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { of, Subject } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('CraneRequestDetailViewContentComponent', () => {
  let component: CraneRequestDetailViewContentComponent;
  let fixture: ComponentFixture<CraneRequestDetailViewContentComponent>;
  let deliveryServiceMock: jest.Mocked<DeliveryService>;
  let projectServiceMock: jest.Mocked<ProjectService>;

  const mockProjectData = {
    ParentCompanyId: 123,
    ProjectId: 456
  };

  const mockCraneRequestId = '789';
  const mockNDRData = {
    data: [
      { firstName: '<PERSON>', lastName: 'Doe' },
      { firstName: 'Jane', lastName: '<PERSON>' }
    ]
  };

  beforeEach(async () => {
    deliveryServiceMock = {
      EditCraneRequestId: new Subject(),
      fetchData: new Subject(),
      fetchData1: new Subject(),
      getEquipmentCraneRequest: jest.fn().mockReturnValue(of(mockNDRData))
    } as any;

    projectServiceMock = {
      projectParent: of(mockProjectData)
    } as any;

    await TestBed.configureTestingModule({
      declarations: [CraneRequestDetailViewContentComponent],
      providers: [
        { provide: DeliveryService, useValue: deliveryServiceMock },
        { provide: ProjectService, useValue: projectServiceMock }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    fixture = TestBed.createComponent(CraneRequestDetailViewContentComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.NDRData).toEqual([]);
    expect(component.loader).toBe(true);
  });

  it('should set ParentCompanyId and ProjectId from projectService', () => {
    expect(component.ParentCompanyId).toBe(mockProjectData.ParentCompanyId);
    expect(component.ProjectId).toBe(mockProjectData.ProjectId);
  });

  it('should set CraneRequestId when EditCraneRequestId emits', () => {
    (deliveryServiceMock.EditCraneRequestId as Subject<string>).next(mockCraneRequestId);
    expect(component.CraneRequestId).toBe(mockCraneRequestId);
  });

  it('should get responsible people initials correctly', () => {
    const person = { firstName: 'John', lastName: 'Doe' };
    expect(component.getResponsiblePeople(person)).toBe('JD');
  });

  it('should return UU for invalid person data', () => {
    expect(component.getResponsiblePeople(null)).toBe('UU');
    expect(component.getResponsiblePeople({})).toBe('UU');
  });

  it('should call getNDR when fetchData emits and CraneRequestId exists', () => {
    const getNDRSpy = jest.spyOn(component, 'getNDR');
    component.CraneRequestId = mockCraneRequestId;
    
    (deliveryServiceMock.fetchData as Subject<void>).next();
    
    expect(getNDRSpy).toHaveBeenCalled();
  });

  it('should not call getNDR when fetchData emits and CraneRequestId does not exist', () => {
    const getNDRSpy = jest.spyOn(component, 'getNDR');
    component.CraneRequestId = null;
    
    (deliveryServiceMock.fetchData as Subject<void>).next();
    
    expect(getNDRSpy).not.toHaveBeenCalled();
  });

  it('should fetch NDR data when getNDR is called with valid parameters', () => {
    component.CraneRequestId = mockCraneRequestId;
    component.ParentCompanyId = mockProjectData.ParentCompanyId;
    component.ProjectId = mockProjectData.ProjectId;

    component.getNDR();

    expect(deliveryServiceMock.getEquipmentCraneRequest).toHaveBeenCalledWith({
      CraneRequestId: mockCraneRequestId,
      ParentCompanyId: mockProjectData.ParentCompanyId,
      ProjectId: mockProjectData.ProjectId
    });
    expect(component.NDRData).toEqual(mockNDRData.data);
    expect(component.loader).toBe(false);
  });

  it('should not fetch NDR data when ProjectId or ParentCompanyId is missing', () => {
    component.CraneRequestId = mockCraneRequestId;
    component.ParentCompanyId = null;
    component.ProjectId = null;

    component.getNDR();

    expect(deliveryServiceMock.getEquipmentCraneRequest).not.toHaveBeenCalled();
  });

  it('should unsubscribe from subscriptions on destroy', () => {
    const unsubscribeSpy = jest.spyOn(component['subscriptions'], 'unsubscribe');
    component.ngOnDestroy();
    expect(unsubscribeSpy).toHaveBeenCalled();
  });
});
