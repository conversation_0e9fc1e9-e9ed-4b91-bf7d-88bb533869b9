/* eslint-disable max-lines-per-function */
import {
  Component, ViewChildren, QueryList, OnInit,
} from '@angular/core';
import { FullCalendarComponent } from '@fullcalendar/angular';
import { CalendarOptions } from '@fullcalendar/core';
import { Router, UrlTree } from '@angular/router';
import moment from 'moment';
import { CalendarService } from '../services/profile/calendar.service';
import { DeliveryService } from '../services/profile/delivery.service';
import interactionPlugin from '@fullcalendar/interaction';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
@Component({
  selector: 'app-full-calendar-render',
  templateUrl: './full-calendar-render.component.html',
  })
export class FullCalendarRenderComponent implements OnInit {
  public currentViewMonth: moment.MomentInput;

  public events: any = [];

  public eventsId: number[] = [];

  public weekKeys: any = [];

  public weekKeyObj: any = [];

  public calendarOptionsArray: CalendarOptions[] = [];

  public calendarOptions: CalendarOptions = {
    selectable: false,
    initialView: 'timeGridWeek',
    plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin],
    aspectRatio: 2,
    slotEventOverlap: false,
    contentHeight: 'liquid',
    fixedWeekCount: false,
    expandRows: true,
    nowIndicator: true,
    moreLinkClick: 'popover',
    slotMinTime: '07:00:00', // set the earliest time slots will be rendered to 7am
    slotMaxTime: '17:00:00', // set the latest time slots will be rendered to 5pm
    events: this.events,
    showNonCurrentDates: false,
    headerToolbar: {
      right: '',
      center: 'title',
      left: '',
    },
    firstDay: 0,
    datesSet: (info): void => {
      this.currentViewMonth = info.view.title;
    },
    eventDidMount(info): void {
      const argument = info;
      const deliveryRequestDescription = argument.event._def.extendedProps.description;
      const fromTimeDuration = argument.event._def.extendedProps.fromTime;
      const toTimeDuration = argument.event._def.extendedProps.toTime;
      const requestType1 = argument.event._def.extendedProps.listType;
      if (requestType1 === 'deliveryRequest' || requestType1 === 'deliveryRequestWithCrane') {
        argument.el.querySelector('.fc-event-title').innerHTML = `
          <div class="d-flex flex-column">
          <p class="m-0"> <img src="./assets/images/sidemenu-icons/delivery.svg"  style="color:green;"class="w10 h10" alt="image"/> ${deliveryRequestDescription}   </p>
          <small class="text-wrap"> ${fromTimeDuration}  - ${toTimeDuration}</small>
          </div>`;
      }
      if (requestType1 === 'craneRequest' || requestType1 === 'CraneEquipment') {
        argument.el.querySelector('.fc-event-title').innerHTML = `
          <div class="d-flex flex-column">
          <p class="m-0">  <img src="./assets/images/cranesvg.svg" class="w10 h10" alt="image"/> ${deliveryRequestDescription}  </p>
          <small class="text-wrap"> ${fromTimeDuration}  - ${toTimeDuration}</small>
          </div>`;
      }
      if (requestType1 === 'concreteRequest') {
        argument.el.querySelector('.fc-event-title').innerHTML = `
          <div class="d-flex flex-column">
          <p class="m-0"><img src="./assets/images/sidemenu-icons/concretesvg.svg" class="w10 h10" alt="image"/>  ${deliveryRequestDescription} </p>
          <small class="text-wrap"> ${fromTimeDuration}  - ${toTimeDuration}</small>
          </div>`;
      }
      if (argument.event._def.allDay) {
        argument.el.querySelector('.fc-event-title').innerHTML = `
        <div class="d-flex flex-column">
        <p class="m-0"> <img class="w10 h10" src="./assets/images/noun-event-alert.svg" class="form-icon" alt="allday" >  ${deliveryRequestDescription} </p>
        <small class="text-wrap"> ${fromTimeDuration}  - ${toTimeDuration}</small>
        </div>`;
      }
      if (requestType1 === 'calendarEvent') {
        argument.el.querySelector('.fc-event-title').innerHTML = `
        <div class="d-flex flex-column">
        <p class="m-0"> <img class="w10 h10" src="./assets/images/sidemenu-icons/calendarsettings.svg" class="form-icon" alt="allday" > ${deliveryRequestDescription}  </p>
        <small class="text-wrap"> ${fromTimeDuration}  - ${toTimeDuration}</small>
        </div>`;
      }
      if (requestType1 === 'inspectionRequest' || requestType1 === 'inspectionRequestWithCrane') {
        argument.el.querySelector('.fc-event-title').innerHTML = `
          <div class="d-flex flex-column">
          <p class="m-0"> <img src="./assets/images/sidemenu-icons/inspection2.png"  style="color:green;"class="w10 h10" alt="image"/> ${deliveryRequestDescription}   </p>
          <small class="text-wrap"> ${fromTimeDuration}  - ${toTimeDuration}</small>
          </div>`;
      }
    },
    eventTimeFormat: {
      hour: 'numeric',
      minute: '2-digit',
      meridiem: 'short',
    },
    dayMaxEventRows: true, // for all non-TimeGrid views
    views: {
      timeGrid: {
        dayMaxEventRows: 2, // adjust to 6 only for timeGridWeek/timeGridDay
      },
      dayGridMonth: {
        allDaySlot: true,
      },
      timeGridWeek: {
        allDaySlot: true,
      },
      timeGridDay: {
        allDaySlot: true,
      },
    },
  };

  @ViewChildren('fullcalendar') public calendarComponents: QueryList<FullCalendarComponent>;

  constructor(
    public router: Router,
    public calendarService: CalendarService,
    private readonly deliveryService: DeliveryService,
  ) {}

  getEvent() {
    // If any filters available pass it as payload
    const urlTree: UrlTree = this.router.parseUrl(this.router.url);
    const projectId: number = parseInt(urlTree.root.children.primary.segments[1].path);
    const queryStart: string = urlTree.queryParams.start;
    const queryEnd: string = urlTree.queryParams.end;
    const userID: number = parseInt(urlTree.queryParams.user);
    const queryStartTime: string = urlTree.queryParams.start_time;
    const queryEndTime: string = urlTree.queryParams.end_time;
    const queryEventStart: string = urlTree.queryParams.event_start;
    const queryEventEnd: string = urlTree.queryParams.event_end;
    const queryParent: number = parseInt(urlTree.queryParams.parent);
    const queryCompany: number = parseInt(urlTree.queryParams.company);
    const queryEquip: number = parseInt(urlTree.queryParams.equip);
    const queryGate: number = parseInt(urlTree.queryParams.gate);
    const queryMember: number = parseInt(urlTree.queryParams.member);
    const querydefine: number = parseInt(urlTree.queryParams.define);
    const queryStatus: string = urlTree.queryParams.status;
    const queryLocation: string = urlTree.queryParams.location;
    const queryTemplate: any = urlTree.queryParams.template.split(',').map((d: string) => {
      switch (d) {
        case '0':
          return { id: 0, name: 'Delivery' };
        case '1':
          return { id: 1, name: 'Crane' };
        case '2':
          return { id: 2, name: 'Concrete' };
        case '3':
          return { id: 3, name: 'Calendar Events' };
        case '4':
          return { id: 4, name: 'Inspection' };
      }
    });
    const queryTimezone: string = urlTree.queryParams.timezone;
    const queryDst: boolean = urlTree.queryParams.dst;

    this.calendarOptions = {
      ...this.calendarOptions,
      slotMinTime: queryEventStart,
      slotMaxTime: queryEventEnd,
    };

    // Get start date
    const start: any = new Date(queryStart);
    // Find start day
    const startDay = start.getDay();
    // Subtract start day to get beginning of the week
    const beginWeek = new Date(start - startDay * 60 * 60 * 24 * 1000);
    const givenDate = new Date(queryEnd);

    // Get the day of the week for the given date
    const dayOfWeek = givenDate.getDay() + 1;

    // Subtract the number of days until the next Saturday
    const daysUntilSaturday = 7 - dayOfWeek;

    // Add the number of days until the next Saturday to the given date
    const nextSaturday = new Date(givenDate);

    nextSaturday.setDate(givenDate.getDate() + daysUntilSaturday);
    // Find difference in dates
    const diff = nextSaturday.getTime() - beginWeek.getTime();
    // Calculate number of full weeks
    const weeks = Math.ceil(diff / (7 * 24 * 60 * 60 * 1000));
    const temp = moment.utc(beginWeek).toISOString();
    const weekObj = Array.from({ length: weeks }, () => ({ startDate: '', endDate: '', value: 0 }));
    const [firstWeek] = weekObj;
    firstWeek.startDate = temp;
    firstWeek.endDate = moment.utc(temp).add(6, 'days').toISOString();
    weekObj.forEach((week, x) => {
      if (x === 0) {
        return; // skip first week, which has already been initialized
      }
      const prevWeek = weekObj[x - 1];
      week.startDate = moment.utc(prevWeek.endDate).add(1, 'day').toISOString();
      week.endDate = moment.utc(week.startDate).add(6, 'days').toISOString();
    });

    const filterParams = {
      ProjectId: projectId,
      void: 0,
    };
    // Get these parameters from URL
    const payload = {
      noAuth: { id: userID, domainName: '' },
      start: moment.utc(queryStart).format('YYYY-MM-DD'),
      end: moment.utc(queryEnd).format('YYYY-MM-DD'),
      companyFilter: queryCompany,
      startDate: '',
      endDate: '',
      statusFilter: queryStatus,
      memberFilter: queryMember,
      gateFilter: queryGate,
      equipmentFilter: queryEquip,
      templateType: queryTemplate,
      defineFilter: querydefine,
      startTime: queryStartTime,
      endTime: queryEndTime,
      ParentCompanyId: queryParent,
      eventStartTime: queryEventStart,
      eventEndTime: queryEventEnd,
      currentViewMonth: 'Week',
      timezone: queryTimezone,
      isDST: queryDst,
      locationFilter: queryLocation,
    };
    this.events = [];
    this.deliveryService
      // .weeklyDeliveryList(filterParams, payload)
      .noAuthWeeklyDeliveryList(filterParams, payload)
      .subscribe((NdrResponse: any): void => {
        if (NdrResponse) {
          const responseData = NdrResponse.data.rows;
          responseData.forEach((element): void => {
            const assignData: any = {
              description: '',
              title: '',
              id: '',
              start: '',
              end: '',
              fromTime: '',
              toTime: '',
              listType: '',
              uniqueNumber: '',
              allDay: false,
              allDaySlot: false,
            };
            assignData.title = element.description;
            assignData.description = element.description;
            assignData.id = element.id;
            assignData.uniqueNumber = element.uniqueNumber;
            // RequestType Logics
            switch (element.requestType) {
              case 'craneRequest':
                assignData.start = element.craneDeliveryStart;
                assignData.end = element.craneDeliveryEnd;
                assignData.fromTime = moment(element.craneDeliveryStart).format('hh:mm A');
                assignData.toTime = moment(element.craneDeliveryEnd).format('hh:mm A');
                break;
              case 'concreteRequest':
                assignData.start = element.concretePlacementStart;
                assignData.end = element.concretePlacementEnd;
                assignData.fromTime = moment(element.concretePlacementStart).format('hh:mm A');
                assignData.toTime = moment(element.concretePlacementEnd).format('hh:mm A');
                break;
              case 'deliveryRequest':
              case 'deliveryRequestWithCrane':
                assignData.start = element.deliveryStart;
                assignData.end = element.deliveryEnd;
                assignData.fromTime = moment(element.deliveryStart).format('hh:mm A');
                assignData.toTime = moment(element.deliveryEnd).format('hh:mm A');
                break;
              case 'calendarEvent':
                assignData.start = element.fromDate;
                assignData.end = element.toDate;
                assignData.fromTime = moment(element.fromDate).format('hh:mm A');
                assignData.toTime = moment(element.toDate).format('hh:mm A');
                if (element.isAllDay === true) {
                  delete assignData.allDay;
                  delete assignData.allDaySlot;
                  assignData.allDay = true;
                  assignData.allDaySlot = true;
                }
                break;
              case 'inspectionRequest':
              case 'inspectionRequestWithCrane':
                assignData.start = element.inspectionStart;
                assignData.end = element.inspectionEnd;
                assignData.fromTime = moment(element.inspectionStart).format('hh:mm A');
                assignData.toTime = moment(element.inspectionEnd).format('hh:mm A');
                break;
            }
            assignData.listType = element.requestType;
            // Status Logic
            switch (element.status) {
              case 'Pending':
              case 'Tentative':
                assignData.className = 'orange_event';
                break;
              case 'Approved':
                assignData.className = 'green_event';
                break;
              case 'Declined':
                assignData.className = 'red_event';
                break;
              case 'Expired':
                assignData.className = 'grey_event';
                break;
              case 'Delivered':
              case 'Completed':
                assignData.className = 'full_blue_event_weekly';
                break;
              default:
                assignData.className = 'calendar_event';
                break;
            }
            this.events.push(assignData);
          });
        }

        // Find data exists inbetween start and end dates
        const weekKeys = Object.keys(weekObj);

        this.events.forEach((event) => {
          const checkDate = moment(event.start);
          weekKeys.some((key) => {
            if (checkDate.isBetween(weekObj[key].startDate, weekObj[key].endDate)) {
              weekObj[key].value += 1;
              return true; // break the loop
            }
            return false;
          });
        });
        // If don't exist data for a week, remove it.
        for (let y = 0; y < weekKeys.length; y++) {
          if (weekObj[y].value === 0) {
            delete weekObj[y]; // NOSONAR
          }
        }
        // Render component if data existed weeks
        this.weekKeys = Object.keys(weekObj);
        this.weekKeyObj = weekObj;
        for (let y = 0; y < this.weekKeys.length; y++) {
          this.eventsId.push(y);
        }
        // Patch code for after component view without lifecycle.
        setTimeout(() => {
          this.calendarComponents.toArray().forEach((component: any, index) => {
            // Set the 'i' value dynamically
            component.i = index;
            const componentApi = component.getApi();
            componentApi.gotoDate(
              moment.utc(this.weekKeyObj[this.weekKeys[index]].startDate).format('YYYY-MM-DD'),
            );
            componentApi.removeAllEventSources();
            componentApi.addEventSource(this.events);
          });
        }, 0);
      });
  }

  ngOnInit() {
    this.getEvent();
  }
}
