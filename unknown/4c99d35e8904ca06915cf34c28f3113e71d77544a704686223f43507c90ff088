import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FullCalendarRenderComponent } from './full-calendar-render.component';
import { Router, UrlTree } from '@angular/router';
import { of } from 'rxjs';
import { FullCalendarModule } from '@fullcalendar/angular';
import { RouterTestingModule } from '@angular/router/testing';
import { CalendarService } from '../services/profile/calendar.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { DatesSetArg, EventMountArg } from '@fullcalendar/core';

describe('FullCalendarRenderComponent', () => {
  let component: FullCalendarRenderComponent;
  let fixture: ComponentFixture<FullCalendarRenderComponent>;
  let router: jest.Mocked<Router>;
  let calendarService: jest.Mocked<CalendarService>;
  let deliveryService: jest.Mocked<DeliveryService>;

  beforeEach(async () => {
    // Create a mock URL tree with the required structure
    const mockUrlTree = {
      root: {
        children: {
          primary: {
            segments: [
              { path: 'projects' },
              { path: '123' }  // Mock project ID
            ]
          }
        }
      },
      queryParams: {
        start: '2024-01-01',
        end: '2024-01-31',
        user: '456',
        start_time: '08:00',
        end_time: '17:00',
        event_start: '07:00:00',
        event_end: '17:00:00',
        template: '0,1,2,3,4',
        timezone: 'UTC',
        dst: false
      }
    } as unknown as UrlTree;

    const routerMock = {
      parseUrl: jest.fn().mockReturnValue(mockUrlTree),
      url: '/projects/123?start=2024-01-01&end=2024-01-31&user=456'
    } as unknown as jest.Mocked<Router>;

    const calendarServiceMock = {
      getEvents: jest.fn().mockReturnValue(of({ data: [] }))
    } as unknown as jest.Mocked<CalendarService>;

    const deliveryServiceMock = {
      noAuthWeeklyDeliveryList: jest.fn().mockReturnValue(of({ data: { rows: [] } }))
    } as unknown as jest.Mocked<DeliveryService>;

    await TestBed.configureTestingModule({
      declarations: [FullCalendarRenderComponent],
      imports: [
        RouterTestingModule,
        FullCalendarModule
      ],
      providers: [
        { provide: Router, useValue: routerMock },
        { provide: CalendarService, useValue: calendarServiceMock },
        { provide: DeliveryService, useValue: deliveryServiceMock }
      ]
    }).compileComponents();

    router = TestBed.inject(Router) as jest.Mocked<Router>;
    calendarService = TestBed.inject(CalendarService) as jest.Mocked<CalendarService>;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FullCalendarRenderComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default calendar options', () => {
    expect(component.calendarOptions).toBeDefined();
    expect(component.calendarOptions.initialView).toBe('timeGridWeek');
  });

  it('should handle datesSet event', () => {
    const mockInfo: DatesSetArg = {
      view: {
        title: 'January 2024',
        type: 'timeGridWeek',
        currentStart: new Date(),
        currentEnd: new Date(),
        calendar: {} as any,
        activeStart: new Date(),
        activeEnd: new Date(),
        getOption: () => ({})
      },
      start: new Date(),
      end: new Date(),
      startStr: '2024-01-01',
      endStr: '2024-01-31',
      timeZone: 'UTC'
    };
    component.calendarOptions.datesSet(mockInfo);
    expect(component.currentViewMonth).toBe('January 2024');
  });

  it('should get events with correct parameters', () => {
    component.getEvent();
    expect(deliveryService.noAuthWeeklyDeliveryList).toHaveBeenCalled();
  });

  it('should handle event mounting with different request types', () => {
    const mockEventInfo: EventMountArg = {
      el: {
        querySelector: jest.fn().mockReturnValue({
          innerHTML: ''
        })
      } as any,
      event: {
        _def: {
          extendedProps: {
            description: 'Test Event',
            fromTime: '09:00',
            toTime: '10:00',
            listType: 'deliveryRequest'
          },
          allDay: false
        }
      } as any,
      timeText: '',
      backgroundColor: '',
      borderColor: '',
      textColor: '',
      isStart: true,
      isEnd: true,
      isMirror: false,
      isPast: false,
      isFuture: false,
      isToday: false,
      isSelected: false,
      isDraggable: false,
      isStartResizable: false,
      isEndResizable: false,
      isDragging: false,
      isResizing: false,
      view: {} as any
    };
    
    component.calendarOptions.eventDidMount(mockEventInfo);
    expect(mockEventInfo.el.querySelector).toHaveBeenCalledWith('.fc-event-title');
  });

  it('should handle all-day events', () => {
    const mockEventInfo: EventMountArg = {
      el: {
        querySelector: jest.fn().mockReturnValue({
          innerHTML: ''
        })
      } as any,
      event: {
        _def: {
          extendedProps: {
            description: 'All Day Event',
            fromTime: '00:00',
            toTime: '23:59',
            listType: 'calendarEvent'
          },
          allDay: true
        }
      } as any,
      timeText: '',
      backgroundColor: '',
      borderColor: '',
      textColor: '',
      isStart: true,
      isEnd: true,
      isMirror: false,
      isPast: false,
      isFuture: false,
      isToday: false,
      isSelected: false,
      isDraggable: false,
      isStartResizable: false,
      isEndResizable: false,
      isDragging: false,
      isResizing: false,
      view: {} as any
    };
    
    component.calendarOptions.eventDidMount(mockEventInfo);
    expect(mockEventInfo.el.querySelector).toHaveBeenCalledWith('.fc-event-title');
  });

  it('should update calendar options when getting events', () => {
    component.getEvent();
    expect(component.calendarOptions.slotMinTime).toBe('07:00:00');
    expect(component.calendarOptions.slotMaxTime).toBe('17:00:00');
  });

  // Test ngOnInit
  it('should call getEvent on ngOnInit', () => {
    const getEventSpy = jest.spyOn(component, 'getEvent');
    component.ngOnInit();
    expect(getEventSpy).toHaveBeenCalled();
  });

  // Test different request types in eventDidMount
  it('should handle craneRequest event type', () => {
    const mockEventInfo: EventMountArg = {
      el: {
        querySelector: jest.fn().mockReturnValue({
          innerHTML: ''
        })
      } as any,
      event: {
        _def: {
          extendedProps: {
            description: 'Crane Event',
            fromTime: '09:00',
            toTime: '10:00',
            listType: 'craneRequest'
          },
          allDay: false
        }
      } as any,
      timeText: '',
      backgroundColor: '',
      borderColor: '',
      textColor: '',
      isStart: true,
      isEnd: true,
      isMirror: false,
      isPast: false,
      isFuture: false,
      isToday: false,
      isSelected: false,
      isDraggable: false,
      isStartResizable: false,
      isEndResizable: false,
      isDragging: false,
      isResizing: false,
      view: {} as any
    };

    component.calendarOptions.eventDidMount(mockEventInfo);
    expect(mockEventInfo.el.querySelector).toHaveBeenCalledWith('.fc-event-title');
  });

  it('should handle CraneEquipment event type', () => {
    const mockEventInfo: EventMountArg = {
      el: {
        querySelector: jest.fn().mockReturnValue({
          innerHTML: ''
        })
      } as any,
      event: {
        _def: {
          extendedProps: {
            description: 'Crane Equipment Event',
            fromTime: '09:00',
            toTime: '10:00',
            listType: 'CraneEquipment'
          },
          allDay: false
        }
      } as any,
      timeText: '',
      backgroundColor: '',
      borderColor: '',
      textColor: '',
      isStart: true,
      isEnd: true,
      isMirror: false,
      isPast: false,
      isFuture: false,
      isToday: false,
      isSelected: false,
      isDraggable: false,
      isStartResizable: false,
      isEndResizable: false,
      isDragging: false,
      isResizing: false,
      view: {} as any
    };

    component.calendarOptions.eventDidMount(mockEventInfo);
    expect(mockEventInfo.el.querySelector).toHaveBeenCalledWith('.fc-event-title');
  });

  it('should handle concreteRequest event type', () => {
    const mockEventInfo: EventMountArg = {
      el: {
        querySelector: jest.fn().mockReturnValue({
          innerHTML: ''
        })
      } as any,
      event: {
        _def: {
          extendedProps: {
            description: 'Concrete Event',
            fromTime: '09:00',
            toTime: '10:00',
            listType: 'concreteRequest'
          },
          allDay: false
        }
      } as any,
      timeText: '',
      backgroundColor: '',
      borderColor: '',
      textColor: '',
      isStart: true,
      isEnd: true,
      isMirror: false,
      isPast: false,
      isFuture: false,
      isToday: false,
      isSelected: false,
      isDraggable: false,
      isStartResizable: false,
      isEndResizable: false,
      isDragging: false,
      isResizing: false,
      view: {} as any
    };

    component.calendarOptions.eventDidMount(mockEventInfo);
    expect(mockEventInfo.el.querySelector).toHaveBeenCalledWith('.fc-event-title');
  });

  it('should handle inspectionRequest event type', () => {
    const mockEventInfo: EventMountArg = {
      el: {
        querySelector: jest.fn().mockReturnValue({
          innerHTML: ''
        })
      } as any,
      event: {
        _def: {
          extendedProps: {
            description: 'Inspection Event',
            fromTime: '09:00',
            toTime: '10:00',
            listType: 'inspectionRequest'
          },
          allDay: false
        }
      } as any,
      timeText: '',
      backgroundColor: '',
      borderColor: '',
      textColor: '',
      isStart: true,
      isEnd: true,
      isMirror: false,
      isPast: false,
      isFuture: false,
      isToday: false,
      isSelected: false,
      isDraggable: false,
      isStartResizable: false,
      isEndResizable: false,
      isDragging: false,
      isResizing: false,
      view: {} as any
    };

    component.calendarOptions.eventDidMount(mockEventInfo);
    expect(mockEventInfo.el.querySelector).toHaveBeenCalledWith('.fc-event-title');
  });

  it('should handle inspectionRequestWithCrane event type', () => {
    const mockEventInfo: EventMountArg = {
      el: {
        querySelector: jest.fn().mockReturnValue({
          innerHTML: ''
        })
      } as any,
      event: {
        _def: {
          extendedProps: {
            description: 'Inspection with Crane Event',
            fromTime: '09:00',
            toTime: '10:00',
            listType: 'inspectionRequestWithCrane'
          },
          allDay: false
        }
      } as any,
      timeText: '',
      backgroundColor: '',
      borderColor: '',
      textColor: '',
      isStart: true,
      isEnd: true,
      isMirror: false,
      isPast: false,
      isFuture: false,
      isToday: false,
      isSelected: false,
      isDraggable: false,
      isStartResizable: false,
      isEndResizable: false,
      isDragging: false,
      isResizing: false,
      view: {} as any
    };

    component.calendarOptions.eventDidMount(mockEventInfo);
    expect(mockEventInfo.el.querySelector).toHaveBeenCalledWith('.fc-event-title');
  });

  // Test edge cases and error scenarios
  it('should handle event with null querySelector result', () => {
    const mockEventInfo: EventMountArg = {
      el: {
        querySelector: jest.fn().mockReturnValue(null)
      } as any,
      event: {
        _def: {
          extendedProps: {
            description: 'Test Event',
            fromTime: '09:00',
            toTime: '10:00',
            listType: 'deliveryRequest'
          },
          allDay: false
        }
      } as any,
      timeText: '',
      backgroundColor: '',
      borderColor: '',
      textColor: '',
      isStart: true,
      isEnd: true,
      isMirror: false,
      isPast: false,
      isFuture: false,
      isToday: false,
      isSelected: false,
      isDraggable: false,
      isStartResizable: false,
      isEndResizable: false,
      isDragging: false,
      isResizing: false,
      view: {} as any
    };

    expect(() => component.calendarOptions.eventDidMount(mockEventInfo)).not.toThrow();
  });

  it('should handle unknown event type', () => {
    const mockEventInfo: EventMountArg = {
      el: {
        querySelector: jest.fn().mockReturnValue({
          innerHTML: ''
        })
      } as any,
      event: {
        _def: {
          extendedProps: {
            description: 'Unknown Event',
            fromTime: '09:00',
            toTime: '10:00',
            listType: 'unknownType'
          },
          allDay: false
        }
      } as any,
      timeText: '',
      backgroundColor: '',
      borderColor: '',
      textColor: '',
      isStart: true,
      isEnd: true,
      isMirror: false,
      isPast: false,
      isFuture: false,
      isToday: false,
      isSelected: false,
      isDraggable: false,
      isStartResizable: false,
      isEndResizable: false,
      isDragging: false,
      isResizing: false,
      view: {} as any
    };

    component.calendarOptions.eventDidMount(mockEventInfo);
    expect(mockEventInfo.el.querySelector).toHaveBeenCalledWith('.fc-event-title');
  });

  // Test component properties initialization
  it('should initialize component properties correctly', () => {
    expect(component.events).toEqual([]);
    expect(component.eventsId).toEqual([]);
    expect(component.weekKeys).toEqual([]);
    expect(component.weekKeyObj).toEqual([]);
    expect(component.calendarOptionsArray).toEqual([]);
  });

  it('should have correct calendar options configuration', () => {
    expect(component.calendarOptions.selectable).toBe(false);
    expect(component.calendarOptions.initialView).toBe('timeGridWeek');
    expect(component.calendarOptions.aspectRatio).toBe(2);
    expect(component.calendarOptions.slotEventOverlap).toBe(false);
    expect(component.calendarOptions.contentHeight).toBe('liquid');
    expect(component.calendarOptions.fixedWeekCount).toBe(false);
    expect(component.calendarOptions.expandRows).toBe(true);
    expect(component.calendarOptions.nowIndicator).toBe(true);
    expect(component.calendarOptions.moreLinkClick).toBe('popover');
    expect(component.calendarOptions.showNonCurrentDates).toBe(false);
    expect(component.calendarOptions.firstDay).toBe(0);
  });

  // Test getEvent method with different response data
  it('should process delivery service response with different request types', () => {
    const mockResponseData = {
      data: {
        rows: [
          {
            id: 1,
            description: 'Delivery Event',
            requestType: 'deliveryRequest',
            deliveryStart: '2024-01-01T09:00:00Z',
            deliveryEnd: '2024-01-01T10:00:00Z',
            status: 'Pending',
            uniqueNumber: 'DEL001'
          },
          {
            id: 2,
            description: 'Crane Event',
            requestType: 'craneRequest',
            craneDeliveryStart: '2024-01-01T11:00:00Z',
            craneDeliveryEnd: '2024-01-01T12:00:00Z',
            status: 'Approved',
            uniqueNumber: 'CRN001'
          },
          {
            id: 3,
            description: 'Concrete Event',
            requestType: 'concreteRequest',
            concretePlacementStart: '2024-01-01T13:00:00Z',
            concretePlacementEnd: '2024-01-01T14:00:00Z',
            status: 'Declined',
            uniqueNumber: 'CON001'
          }
        ]
      }
    };

    deliveryService.noAuthWeeklyDeliveryList.mockReturnValue(of(mockResponseData));

    component.getEvent();

    expect(deliveryService.noAuthWeeklyDeliveryList).toHaveBeenCalled();
    expect(component.events.length).toBe(3);
    expect(component.events[0].listType).toBe('deliveryRequest');
    expect(component.events[1].listType).toBe('craneRequest');
    expect(component.events[2].listType).toBe('concreteRequest');
  });

  it('should handle calendar events with all-day flag', () => {
    const mockResponseData = {
      data: {
        rows: [
          {
            id: 1,
            description: 'All Day Calendar Event',
            requestType: 'calendarEvent',
            fromDate: '2024-01-01T00:00:00Z',
            toDate: '2024-01-01T23:59:59Z',
            status: 'Approved',
            uniqueNumber: 'CAL001',
            isAllDay: true
          }
        ]
      }
    };

    deliveryService.noAuthWeeklyDeliveryList.mockReturnValue(of(mockResponseData));

    component.getEvent();

    expect(component.events[0].allDay).toBe(true);
    expect(component.events[0].allDaySlot).toBe(true);
  });

  it('should handle inspection requests', () => {
    const mockResponseData = {
      data: {
        rows: [
          {
            id: 1,
            description: 'Inspection Event',
            requestType: 'inspectionRequest',
            inspectionStart: '2024-01-01T09:00:00Z',
            inspectionEnd: '2024-01-01T10:00:00Z',
            status: 'Pending',
            uniqueNumber: 'INS001'
          },
          {
            id: 2,
            description: 'Inspection with Crane Event',
            requestType: 'inspectionRequestWithCrane',
            inspectionStart: '2024-01-01T11:00:00Z',
            inspectionEnd: '2024-01-01T12:00:00Z',
            status: 'Approved',
            uniqueNumber: 'INS002'
          }
        ]
      }
    };

    deliveryService.noAuthWeeklyDeliveryList.mockReturnValue(of(mockResponseData));

    component.getEvent();

    expect(component.events.length).toBe(2);
    expect(component.events[0].listType).toBe('inspectionRequest');
    expect(component.events[1].listType).toBe('inspectionRequestWithCrane');
  });

  it('should assign correct CSS classes based on status', () => {
    const mockResponseData = {
      data: {
        rows: [
          { id: 1, description: 'Event 1', requestType: 'deliveryRequest', deliveryStart: '2024-01-01T09:00:00Z', deliveryEnd: '2024-01-01T10:00:00Z', status: 'Pending', uniqueNumber: 'E001' },
          { id: 2, description: 'Event 2', requestType: 'deliveryRequest', deliveryStart: '2024-01-01T09:00:00Z', deliveryEnd: '2024-01-01T10:00:00Z', status: 'Tentative', uniqueNumber: 'E002' },
          { id: 3, description: 'Event 3', requestType: 'deliveryRequest', deliveryStart: '2024-01-01T09:00:00Z', deliveryEnd: '2024-01-01T10:00:00Z', status: 'Approved', uniqueNumber: 'E003' },
          { id: 4, description: 'Event 4', requestType: 'deliveryRequest', deliveryStart: '2024-01-01T09:00:00Z', deliveryEnd: '2024-01-01T10:00:00Z', status: 'Declined', uniqueNumber: 'E004' },
          { id: 5, description: 'Event 5', requestType: 'deliveryRequest', deliveryStart: '2024-01-01T09:00:00Z', deliveryEnd: '2024-01-01T10:00:00Z', status: 'Expired', uniqueNumber: 'E005' },
          { id: 6, description: 'Event 6', requestType: 'deliveryRequest', deliveryStart: '2024-01-01T09:00:00Z', deliveryEnd: '2024-01-01T10:00:00Z', status: 'Delivered', uniqueNumber: 'E006' },
          { id: 7, description: 'Event 7', requestType: 'deliveryRequest', deliveryStart: '2024-01-01T09:00:00Z', deliveryEnd: '2024-01-01T10:00:00Z', status: 'Completed', uniqueNumber: 'E007' },
          { id: 8, description: 'Event 8', requestType: 'deliveryRequest', deliveryStart: '2024-01-01T09:00:00Z', deliveryEnd: '2024-01-01T10:00:00Z', status: 'Unknown', uniqueNumber: 'E008' }
        ]
      }
    };

    deliveryService.noAuthWeeklyDeliveryList.mockReturnValue(of(mockResponseData));

    component.getEvent();

    expect(component.events[0].className).toBe('orange_event'); // Pending
    expect(component.events[1].className).toBe('orange_event'); // Tentative
    expect(component.events[2].className).toBe('green_event'); // Approved
    expect(component.events[3].className).toBe('red_event'); // Declined
    expect(component.events[4].className).toBe('grey_event'); // Expired
    expect(component.events[5].className).toBe('full_blue_event_weekly'); // Delivered
    expect(component.events[6].className).toBe('full_blue_event_weekly'); // Completed
    expect(component.events[7].className).toBe('calendar_event'); // Unknown/Default
  });

  // Test error scenarios and edge cases
  it('should handle empty response data', () => {
    const mockResponseData = {
      data: {
        rows: []
      }
    };

    deliveryService.noAuthWeeklyDeliveryList.mockReturnValue(of(mockResponseData));

    component.getEvent();

    expect(component.events.length).toBe(0);
  });

  it('should handle null response data', () => {
    deliveryService.noAuthWeeklyDeliveryList.mockReturnValue(of(null));

    expect(() => component.getEvent()).not.toThrow();
  });

  it('should handle response without data property', () => {
    const mockResponseData = {};

    deliveryService.noAuthWeeklyDeliveryList.mockReturnValue(of(mockResponseData));

    expect(() => component.getEvent()).not.toThrow();
  });

  it('should handle deliveryRequestWithCrane request type', () => {
    const mockResponseData = {
      data: {
        rows: [
          {
            id: 1,
            description: 'Delivery with Crane Event',
            requestType: 'deliveryRequestWithCrane',
            deliveryStart: '2024-01-01T09:00:00Z',
            deliveryEnd: '2024-01-01T10:00:00Z',
            status: 'Pending',
            uniqueNumber: 'DWC001'
          }
        ]
      }
    };

    deliveryService.noAuthWeeklyDeliveryList.mockReturnValue(of(mockResponseData));

    component.getEvent();

    expect(component.events[0].listType).toBe('deliveryRequestWithCrane');
  });

  it('should handle calendar events without all-day flag', () => {
    const mockResponseData = {
      data: {
        rows: [
          {
            id: 1,
            description: 'Regular Calendar Event',
            requestType: 'calendarEvent',
            fromDate: '2024-01-01T09:00:00Z',
            toDate: '2024-01-01T10:00:00Z',
            status: 'Approved',
            uniqueNumber: 'CAL002',
            isAllDay: false
          }
        ]
      }
    };

    deliveryService.noAuthWeeklyDeliveryList.mockReturnValue(of(mockResponseData));

    component.getEvent();

    expect(component.events[0].allDay).toBe(false);
    expect(component.events[0].allDaySlot).toBe(false);
  });

  // Test URL parsing with different query parameters
  it('should handle URL with missing query parameters', () => {
    const mockUrlTree = {
      root: {
        children: {
          primary: {
            segments: [
              { path: 'projects' },
              { path: '123' }
            ]
          }
        }
      },
      queryParams: {
        start: '2024-01-01',
        end: '2024-01-31',
        user: '456',
        start_time: '08:00',
        end_time: '17:00',
        event_start: '07:00:00',
        event_end: '17:00:00',
        template: '0',
        timezone: 'UTC',
        dst: false
      }
    } as unknown as UrlTree;

    router.parseUrl.mockReturnValue(mockUrlTree);

    expect(() => component.getEvent()).not.toThrow();
  });

  it('should handle template parsing with single value', () => {
    const mockUrlTree = {
      root: {
        children: {
          primary: {
            segments: [
              { path: 'projects' },
              { path: '123' }
            ]
          }
        }
      },
      queryParams: {
        start: '2024-01-01',
        end: '2024-01-31',
        user: '456',
        start_time: '08:00',
        end_time: '17:00',
        event_start: '07:00:00',
        event_end: '17:00:00',
        template: '1',
        timezone: 'UTC',
        dst: false
      }
    } as unknown as UrlTree;

    router.parseUrl.mockReturnValue(mockUrlTree);

    expect(() => component.getEvent()).not.toThrow();
  });

  it('should handle template parsing with multiple values', () => {
    const mockUrlTree = {
      root: {
        children: {
          primary: {
            segments: [
              { path: 'projects' },
              { path: '123' }
            ]
          }
        }
      },
      queryParams: {
        start: '2024-01-01',
        end: '2024-01-31',
        user: '456',
        start_time: '08:00',
        end_time: '17:00',
        event_start: '07:00:00',
        event_end: '17:00:00',
        template: '2,3,4',
        timezone: 'UTC',
        dst: false
      }
    } as unknown as UrlTree;

    router.parseUrl.mockReturnValue(mockUrlTree);

    expect(() => component.getEvent()).not.toThrow();
  });

  // Test calendar component interaction
  it('should handle calendar components array manipulation', (done) => {
    const mockResponseData = {
      data: {
        rows: [
          {
            id: 1,
            description: 'Test Event',
            requestType: 'deliveryRequest',
            deliveryStart: '2024-01-01T09:00:00Z',
            deliveryEnd: '2024-01-01T10:00:00Z',
            status: 'Pending',
            uniqueNumber: 'TEST001'
          }
        ]
      }
    };

    const mockCalendarComponent = {
      i: 0,
      getApi: jest.fn().mockReturnValue({
        gotoDate: jest.fn(),
        removeAllEventSources: jest.fn(),
        addEventSource: jest.fn()
      })
    };

    // Mock the QueryList
    component.calendarComponents = {
      toArray: jest.fn().mockReturnValue([mockCalendarComponent])
    } as any;

    deliveryService.noAuthWeeklyDeliveryList.mockReturnValue(of(mockResponseData));

    component.getEvent();

    // Wait for setTimeout to execute
    setTimeout(() => {
      expect(mockCalendarComponent.getApi).toHaveBeenCalled();
      expect(mockCalendarComponent.getApi().gotoDate).toHaveBeenCalled();
      expect(mockCalendarComponent.getApi().removeAllEventSources).toHaveBeenCalled();
      expect(mockCalendarComponent.getApi().addEventSource).toHaveBeenCalled();
      done();
    }, 10);
  });

  // Test week calculation logic
  it('should calculate weeks correctly for date range', () => {
    const mockResponseData = {
      data: {
        rows: [
          {
            id: 1,
            description: 'Test Event',
            requestType: 'deliveryRequest',
            deliveryStart: '2024-01-01T09:00:00Z',
            deliveryEnd: '2024-01-01T10:00:00Z',
            status: 'Pending',
            uniqueNumber: 'TEST001'
          }
        ]
      }
    };

    deliveryService.noAuthWeeklyDeliveryList.mockReturnValue(of(mockResponseData));

    component.getEvent();

    expect(component.weekKeys).toBeDefined();
    expect(component.weekKeyObj).toBeDefined();
    expect(component.eventsId).toBeDefined();
  });

  // Test negative scenarios
  it('should handle service error gracefully', () => {
    const errorResponse = new Error('Service error');
    deliveryService.noAuthWeeklyDeliveryList.mockReturnValue(of(errorResponse));

    expect(() => component.getEvent()).not.toThrow();
  });

  it('should handle malformed event data', () => {
    const mockResponseData = {
      data: {
        rows: [
          {
            // Missing required fields
            id: 1,
            requestType: 'deliveryRequest'
            // Missing description, dates, status, uniqueNumber
          }
        ]
      }
    };

    deliveryService.noAuthWeeklyDeliveryList.mockReturnValue(of(mockResponseData));

    expect(() => component.getEvent()).not.toThrow();
  });

  // Test ViewChildren property
  it('should have ViewChildren property defined', () => {
    expect(component.calendarComponents).toBeDefined();
  });

  // Test constructor injection
  it('should inject services correctly', () => {
    expect(component.router).toBeDefined();
    expect(component.calendarService).toBeDefined();
  });

  // Test calendar options event time format
  it('should have correct event time format configuration', () => {
    expect(component.calendarOptions.eventTimeFormat).toEqual({
      hour: 'numeric',
      minute: '2-digit',
      meridiem: 'short'
    });
  });

  // Test calendar options views configuration
  it('should have correct views configuration', () => {
    expect(component.calendarOptions.views).toBeDefined();
    expect(component.calendarOptions.views.timeGrid.dayMaxEventRows).toBe(2);
    expect(component.calendarOptions.views.dayGridMonth.allDaySlot).toBe(true);
    expect(component.calendarOptions.views.timeGridWeek.allDaySlot).toBe(true);
    expect(component.calendarOptions.views.timeGridDay.allDaySlot).toBe(true);
  });

  // Test header toolbar configuration
  it('should have correct header toolbar configuration', () => {
    expect(component.calendarOptions.headerToolbar).toEqual({
      right: '',
      center: 'title',
      left: ''
    });
  });
});
