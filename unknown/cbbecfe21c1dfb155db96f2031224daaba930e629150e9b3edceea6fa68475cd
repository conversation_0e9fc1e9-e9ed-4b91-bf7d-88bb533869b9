import { ComponentFixture, TestBed } from '@angular/core/testing';
import { InspectionDetailsComponent } from './details.component';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';
import { BehaviorSubject, of } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('InspectionDetailsComponent', () => {
  let component: InspectionDetailsComponent;
  let fixture: ComponentFixture<InspectionDetailsComponent>;
  let deliveryServiceMock: any;
  let projectServiceMock: any;

  const mockProjectParent = new BehaviorSubject<any>({ ParentCompanyId: '123' });
  const mockInspectionRequestId = new BehaviorSubject<string>('456');
  const mockInspectionUpdated = new BehaviorSubject<any>({});
  const mockInspectionUpdated1 = new BehaviorSubject<any>({});

  beforeEach(async () => {
    deliveryServiceMock = {
      InspectionRequestId: mockInspectionRequestId,
      inspectionUpdated: mockInspectionUpdated,
      inspectionUpdated1: mockInspectionUpdated1,
      getInspectionNDRData: jest.fn()
    };

    projectServiceMock = {
      projectParent: mockProjectParent
    };

    await TestBed.configureTestingModule({
      declarations: [InspectionDetailsComponent],
      providers: [
        { provide: DeliveryService, useValue: deliveryServiceMock },
        { provide: ProjectService, useValue: projectServiceMock }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(InspectionDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.NDRData).toEqual([]);
    expect(component.loader).toBeTruthy();
    expect(component.toolTipContent).toBe('');
  });

  it('should set ParentCompanyId from projectService subscription', () => {
    expect(component.ParentCompanyId).toBe('123');
  });

  it('should handle getResponsiblePeople with valid input', () => {
    const input = { firstName: 'John', lastName: 'Doe' };
    expect(component.getResponsiblePeople(input)).toBe('JD');
  });

  it('should handle getResponsiblePeople with invalid input', () => {
    const input = { firstName: undefined, lastName: undefined };
    expect(component.getResponsiblePeople(input)).toBe('UU');
  });

  it('should call getNDR when inspectionRequestId is received', () => {
    const getNDRSpy = jest.spyOn(component, 'getNDR');
    mockInspectionRequestId.next('789');
    expect(getNDRSpy).toHaveBeenCalled();
  });

  it('should update NDRData and toolTipContent when getNDR is called', () => {
    const mockResponse = {
      data: {
        memberDetails: [
          { Member: { User: { firstName: 'John', lastName: 'Doe' } } },
          { Member: { User: { firstName: 'Jane', lastName: 'Smith' } } },
          { Member: { User: { firstName: 'Bob', lastName: 'Johnson' } } },
          { Member: { User: { firstName: 'Alice', lastName: 'Brown' } } }
        ]
      }
    };

    deliveryServiceMock.getInspectionNDRData.mockReturnValue(of(mockResponse));
    component.inspectionRequestId = '123';
    component.ParentCompanyId = '456';
    component.getNDR();

    expect(component.NDRData).toEqual(mockResponse.data);
    expect(component.loader).toBeFalsy();
    expect(component.toolTipContent).toContain('Alice Brown');
  });

  it('should handle getNDR with empty memberDetails', () => {
    const mockResponse = {
      data: {
        memberDetails: []
      }
    };

    deliveryServiceMock.getInspectionNDRData.mockReturnValue(of(mockResponse));
    component.inspectionRequestId = '123';
    component.ParentCompanyId = '456';
    component.getNDR();

    expect(component.NDRData).toEqual(mockResponse.data);
    expect(component.loader).toBeFalsy();
    expect(component.toolTipContent).toBe('');
  });

  it('should unsubscribe from all subscriptions on destroy', () => {
    const unsubscribeSpy = jest.spyOn(component['subscriptions'], 'unsubscribe');
    component.ngOnDestroy();
    expect(unsubscribeSpy).toHaveBeenCalled();
  });
});
