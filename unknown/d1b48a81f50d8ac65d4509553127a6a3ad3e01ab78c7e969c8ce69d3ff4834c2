import { Component } from '@angular/core';
import {
  debounceTime, filter, merge, Subscription, tap,
} from 'rxjs';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';

@Component({
  selector: 'app-inspection-history',
  templateUrl: './inspection-history.component.html',
})
export class InspectionHistoryComponent {
  public historyList: any = [];

  public inspectionRequestId: any;

  public loader = true;

  public ProjectId: any;

  public ParentCompanyId: any;

  private readonly subscription: Subscription = new Subscription();


  public constructor(private readonly DeliveryService: DeliveryService,
    public projectService: ProjectService) {
    this.projectService.projectParent.subscribe((response5): void => {
      if (response5 !== undefined && response5 !== null && response5 !== '') {
        this.loader = true;
        this.ProjectId = response5.ProjectId;
        this.ParentCompanyId = response5.ParentCompanyId;
        this.loader = true;
      }
    });
    const historyTrigger$ = merge(
      this.DeliveryService.InspectionRequestId.pipe(
        tap((id) => {
          this.inspectionRequestId = id;
        }),
      ),
      this.DeliveryService.inspectionUpdated,
      this.DeliveryService.inspectionUpdated1,
    ).pipe(
      debounceTime(100),
      filter(() => !!this.inspectionRequestId),
    );

    this.subscription.add(
      historyTrigger$.subscribe(() => {
        this.getHistory();
      }),
    );
  }

  public getHistory(): void {
    this.loader = true;
    const param = {
      inspectionRequestId: this.inspectionRequestId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.DeliveryService.getInspectionHistory(param).subscribe((res): void => {
      this.historyList = res.data;
      // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
      this.historyList = this.historyList.filter((data: { type: string; }) => data.type !== 'comment');

      this.loader = false;
    });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
