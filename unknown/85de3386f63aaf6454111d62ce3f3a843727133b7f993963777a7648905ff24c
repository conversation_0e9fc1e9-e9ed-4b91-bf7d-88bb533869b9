import { Component, OnInit } from '@angular/core';
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import {
  debounceTime, filter, merge, Subscription, tap,
} from 'rxjs';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';
import { AuthService } from '../../services/auth/auth.service';
import { MixpanelService } from '../../services/mixpanel.service';

@Component({
  selector: 'app-concrete-attachments',
  templateUrl: './concrete-attachments.component.html',
})
export class ConcreteAttachmentsComponent implements OnInit {
  public files: NgxFileDropEntry[] = [];

  public fileData: any = [];

  public todayDate: Date;

  public formData: FormData;

  public fileArray: any = [];

  public ConcreteRequestId: any;

  public uploadSubmitted = false;

  public deleteUploadedFile = false;

  public ParentCompanyId: any;

  public ProjectId: any;

  public currentUser: any = {};

  public authUser: any = {};

  public concreteRequest: any;

  public loader = false;

  private readonly subscription: Subscription = new Subscription();

  public constructor(
    private readonly toastr: ToastrService,
    public projectService: ProjectService,
    private readonly deliveryService: DeliveryService,
    public socket: Socket,
    private readonly authService: AuthService,
    private readonly mixpanelService: MixpanelService,
  ) {
    this.todayDate = new Date();
    this.formData = new FormData();
    this.deliveryService.EditConcreteRequestId.subscribe((getDeliveryRequestIdResponse): void => {
      this.ConcreteRequestId = getDeliveryRequestIdResponse;
    });
    this.getAuthUser();
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
      }
    });
    this.projectService.ParentCompanyId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ParentCompanyId = res;
      }
    });
  }

  public ngOnInit(): void {
    const attachmentTrigger$ = merge(
      this.projectService.projectParent.pipe(
        tap((res) => {
          if (res) {
            this.ProjectId = res.ProjectId;
            this.ParentCompanyId = res.ParentCompanyId;
          }
        }),
        filter((res) => !!res),
      ),
      this.deliveryService.fetchConcreteData,
      this.deliveryService.fetchConcreteData1,
    ).pipe(
      debounceTime(100),
      filter(() => !!this.ConcreteRequestId),
    );

    this.subscription.add(
      attachmentTrigger$.subscribe(() => {
        this.getAttachments();
      }),
    );
  }

  public getAuthUser(): void {
    this.authService.getUser().subscribe((response: any): void => {
      this.currentUser = response;
    });
  }

  public async dropped(files: NgxFileDropEntry[]): Promise<void> {
    this.files = files;
    this.fileData.push(this.files);

    // Wait for fileDataConvert to complete
    await this.fileDataConvert(this.fileData);

    // Now append files to FormData
    this.fileData.forEach((element: any[], i: any): void => {
      element.forEach(
        (data: { fileEntry: FileSystemFileEntry; relativePath: any }, index: any): void => {
          const { fileEntry } = data;
          fileEntry.file((_file: File): void => {
            this.formData.append('attachment', _file, data.relativePath);
          });
        },
      );
    });
  }

  public async fileDataConvert(fileData: any[]): Promise<void[]> {
    const isValidExtension = (extension: string): boolean => ['jpg', 'png', 'jpeg', 'pdf', 'doc'].includes(extension);

    const getFileExtension = (path: string): string => path.split('.').pop()?.toLowerCase() || '';

    const readFileAsDataURL = (file: File): Promise<string | ArrayBuffer | null> => new Promise(
      (resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result);
        reader.onerror = (event) => {
          reject(new Error(event?.target?.error?.message || 'File reading error'));
        };
        reader.readAsDataURL(file);
      },
    );

    const processFile = async (
      data: { relativePath: string; fileEntry: FileSystemFileEntry },
      i: number,
      index: number,
    ): Promise<void> => {
      const extension = getFileExtension(data.relativePath);

      if (!isValidExtension(extension)) {
        this.toastr.error(
          'Please select a valid file. Supported formats: .jpg, .jpeg, .png, .pdf, .doc',
          'OOPS!',
        );
        this.fileData.splice(i, 1);
        throw new Error('Invalid file format');
      }

      if (!data.fileEntry.isFile) return;

      const file = await new Promise<File>((resolve) => {
        data.fileEntry.file((files: File) => {
          resolve(files);
        });
      });

      const fileSizeMB = +(file.size / 1000000).toFixed(4);
      if (fileSizeMB > 2) {
        this.toastr.error('Please choose a file less than or equal to 2MB');
        this.fileData.splice(i, 1);
        throw new Error('File size exceeds limit');
      }

      const fileContent = await readFileAsDataURL(file);
      if (this.fileData[i]) {
        this.fileData[i][index].extension = extension;
        this.fileData[i][index].image = fileContent;
      }
    };

    const allPromises: Promise<void>[] = [];

    fileData.forEach((element, i) => {
      element.forEach((data, index) => {
        allPromises.push(processFile(data, i, index));
      });
    });

    return Promise.all(allPromises);
  }


  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.removeFile(data, item);
    }
  }


  public removeExistingFile(item: { id: any }): void {
    this.loader = true;
    this.deleteUploadedFile = true;
    const params = {
      id: item.id,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    this.deliveryService.removeConcreteRequestAttachment(params).subscribe({
      next: (res): void => {
        if (res) {
          this.fileData = [];
          this.toastr.success(res.message, 'Success');
          this.mixpanelService.addMixpanelEvents('Attachment deleted against a Concrete Booking');
          this.deleteUploadedFile = false;
          this.deliveryService.updateConcreteRequestHistory1(
            { status: true },
            'ConcreteAttachmentDeleteHistory',
          );
          this.socket.emit('ConcreteAttachmentDeleteHistory', res);
        }
      },
      error: (removeExistingFileError): void => {
        if (removeExistingFileError.message?.statusCode === 400) {
          this.showError(removeExistingFileError);
        } else if (!removeExistingFileError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(removeExistingFileError.message, 'OOPS!');
        }
      },
    });
    this.loader = false;
  }

  public clickAndDisable(link: any): void {
    const getData = link;
    // disable subsequent clicks
    getData.onclick = (event: { preventDefault: () => void }): void => {
      event.preventDefault();
    };
  }

  public uploadData(): void {
    this.uploadSubmitted = true;
    const hasFormData = (formData: FormData): boolean => {
      let hasData = false;

      formData.forEach(() => {
        hasData = true;
      });

      return hasData;
    };
    if (hasFormData(this.formData)) {
      const params = {
        ConcreteRequestId: this.ConcreteRequestId,
        ParentCompanyId: this.ParentCompanyId,
        ProjectId: this.ProjectId,
      };
      this.deliveryService.addConcreteRequestAttachment(params, this.formData).subscribe({
        next: (res): void => {
          this.fileData = [];
          this.uploadSubmitted = false;
          this.toastr.success(res.message, 'SUCCESS!');
          this.mixpanelService.addMixpanelEvents('Attachment added against a Concrete Booking');
          this.socket.emit('ConcreteApproveHistory', res);
          this.deliveryService.updateConcreteRequestHistory1(
            { status: true },
            'ConcreteApproveHistory',
          );
          this.getAttachments();
        },
        error: (attachmentErr): void => {
          this.uploadSubmitted = false;
          if (attachmentErr.message?.statusCode === 400) {
            this.showError(attachmentErr);
          } else {
            this.toastr.error(attachmentErr.message, 'OOPS!');
          }
        },
      });
    }
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }

  public removeFile(firstIndex: string | number, i: string | number): void {
    if (this.fileData[firstIndex]?.[i]) {
      this.formData.delete(this.fileData[firstIndex][i].relativePath);
      this.fileData[firstIndex].splice(i, 1);
      if (this.fileData[firstIndex].length === 0) {
        this.fileData.splice(firstIndex, 1);
      }
    }
  }

  public getAttachments(): void {
    const params = {
      ConcreteRequestId: this.ConcreteRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    this.deliveryService.getConcreteRequestAttachments(params).subscribe((res): void => {
      this.fileArray = res.data;
      this.concreteRequest = res.concreteRequest;
    });
  }

  public selectStatus(data: any): void {
    this.deliveryService.completeConcreteRequestStatus(data);
  }

  public fileOver(_event: any): void { /* */ }

  public fileLeave(_event: any): void { /* */ }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
