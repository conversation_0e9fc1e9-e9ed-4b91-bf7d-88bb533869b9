import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ConcreteAttachmentsComponent } from './concrete-attachments.component';
import { ToastrService } from 'ngx-toastr';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { Socket } from 'ngx-socket-io';
import { AuthService } from '../../services/auth/auth.service';
import { MixpanelService } from '../../services/mixpanel.service';
import { of, throwError, BehaviorSubject } from 'rxjs';

declare const global: any;
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';

describe('ConcreteAttachmentsComponent', () => {
  let component: ConcreteAttachmentsComponent;
  let fixture: ComponentFixture<ConcreteAttachmentsComponent>;
  let toastrService: jest.Mocked<ToastrService>;
  let projectService: jest.Mocked<ProjectService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let socket: jest.Mocked<Socket>;
  let authService: jest.Mocked<AuthService>;
  let mixpanelService: jest.Mocked<MixpanelService>;

  beforeEach(async () => {
    const toastrSpy = {
      success: jest.fn(),
      error: jest.fn(),
    };

    const projectServiceSpy = {
      projectParent: new BehaviorSubject({ ProjectId: '123', ParentCompanyId: '456' }),
      ParentCompanyId: new BehaviorSubject('456'),
    };

    const deliveryServiceSpy = {
      removeConcreteRequestAttachment: jest.fn(),
      updateConcreteRequestHistory1: jest.fn(),
      addConcreteRequestAttachment: jest.fn(),
      getConcreteRequestAttachments: jest.fn(),
      completeConcreteRequestStatus: jest.fn(),
      EditConcreteRequestId: new BehaviorSubject('789'),
      loginUser: new BehaviorSubject({ id: '1', name: 'Test User' }),
      fetchConcreteData: new BehaviorSubject({}),
      fetchConcreteData1: new BehaviorSubject({}),
    };

    const socketSpy = {
      emit: jest.fn(),
    };

    const authServiceSpy = {
      getUser: jest.fn().mockReturnValue(of({ id: '1', name: 'Test User' })),
    };

    const mixpanelServiceSpy = {
      addMixpanelEvents: jest.fn(),
    };

    await TestBed.configureTestingModule({
      declarations: [ConcreteAttachmentsComponent],
      providers: [
        { provide: ToastrService, useValue: toastrSpy },
        { provide: ProjectService, useValue: projectServiceSpy },
        { provide: DeliveryService, useValue: deliveryServiceSpy },
        { provide: Socket, useValue: socketSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: MixpanelService, useValue: mixpanelServiceSpy }
      ]
    }).compileComponents();

    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    socket = TestBed.inject(Socket) as jest.Mocked<Socket>;
    authService = TestBed.inject(AuthService) as jest.Mocked<AuthService>;
    mixpanelService = TestBed.inject(MixpanelService) as jest.Mocked<MixpanelService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ConcreteAttachmentsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.files).toEqual([]);
    expect(component.fileData).toEqual([]);
    expect(component.uploadSubmitted).toBe(false);
    expect(component.deleteUploadedFile).toBe(false);
    expect(component.loader).toBe(false);
  });

  it('should get auth user on init', () => {
    const mockUser = { id: '1', name: 'Test User' };
    authService.getUser.mockReturnValue(of(mockUser));
    component.getAuthUser();
    expect(component.currentUser).toEqual(mockUser);
  });

  describe('file handling', () => {
    it('should handle dropped files', async () => {
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      const mockFileEntry = {
        file: (callback: (file: File) => void) => callback(mockFile),
        isFile: true
      } as FileSystemFileEntry;

      const mockDropEntry: NgxFileDropEntry = {
        fileEntry: mockFileEntry,
        relativePath: 'test.jpg'
      };

      await component.dropped([mockDropEntry]);
      expect(component.files).toEqual([mockDropEntry]);
      expect(component.fileData.length).toBeGreaterThan(0);
    });

    it('should reject invalid file extensions', async () => {
      const mockFile = new File(['test'], 'test.exe', { type: 'application/exe' });
      const mockFileEntry = {
        file: (callback: (file: File) => void) => callback(mockFile),
        isFile: true,
      } as FileSystemFileEntry;

      const mockDropEntry: NgxFileDropEntry = {
        fileEntry: mockFileEntry,
        relativePath: 'test.exe',
      };

      await expect(component.dropped([mockDropEntry])).rejects.toThrow('Invalid file format');
      expect(toastrService.error).toHaveBeenCalledWith(
        'Please select a valid file. Supported formats: .jpg, .jpeg, .png, .pdf, .doc',
        'OOPS!',
      );
    });
  });

  describe('removeExistingFile', () => {
    it('should successfully remove an existing file', () => {
      const mockResponse = { message: 'File removed successfully' };
      deliveryService.removeConcreteRequestAttachment.mockReturnValue(of(mockResponse));

      component.removeExistingFile({ id: '123' });

      expect(deliveryService.removeConcreteRequestAttachment).toHaveBeenCalled();
      expect(toastrService.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
      expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Attachment deleted against a Concrete Booking');
      expect(socket.emit).toHaveBeenCalledWith('ConcreteAttachmentDeleteHistory', mockResponse);
    });

    it('should handle error when removing file', () => {
      const mockError = { message: 'Error message' };
      deliveryService.removeConcreteRequestAttachment.mockReturnValue(throwError(() => mockError));

      component.removeExistingFile({ id: '123' });

      expect(toastrService.error).toHaveBeenCalledWith(mockError.message, 'OOPS!');
    });
  });

  describe('handleToggleKeydown', () => {
    it('should call removeFile on Enter key', () => {
      // Setup mock data structure
      component.fileData = [
        [{ relativePath: 'test.jpg' }]
      ];
      component.formData = new FormData();
      const removeFileSpy = jest.spyOn(component, 'removeFile');
      const event = new KeyboardEvent('keydown', { key: 'Enter' });

      component.handleToggleKeydown(event, 0, 0);

      expect(removeFileSpy).toHaveBeenCalledWith(0, 0);
    });

    it('should call removeFile on Space key', () => {
      // Setup mock data structure
      component.fileData = [
        [{ relativePath: 'test.jpg' }]
      ];
      component.formData = new FormData();
      const removeFileSpy = jest.spyOn(component, 'removeFile');
      const event = new KeyboardEvent('keydown', { key: ' ' });

      component.handleToggleKeydown(event, 0, 0);

      expect(removeFileSpy).toHaveBeenCalledWith(0, 0);
    });
  });

  it('should unsubscribe on destroy', () => {
    const unsubscribeSpy = jest.spyOn(component['subscription'], 'unsubscribe');
    component.ngOnDestroy();
    expect(unsubscribeSpy).toHaveBeenCalled();
  });

  describe('Constructor subscriptions', () => {
    it('should set ConcreteRequestId from EditConcreteRequestId subscription', () => {
      expect(component.ConcreteRequestId).toBe('789');
    });

    it('should set authUser from loginUser subscription', () => {
      const mockUser = { id: '2', name: 'Login User' };
      deliveryService.loginUser.next(mockUser);

      expect(component.authUser).toEqual(mockUser);
    });

    it('should set ParentCompanyId from projectService subscription', () => {
      const mockCompanyId = '999';
      projectService.ParentCompanyId.next(mockCompanyId);

      expect(component.ParentCompanyId).toBe(mockCompanyId);
    });

    it('should handle undefined/null/empty values in loginUser subscription', () => {
      deliveryService.loginUser.next(undefined);

      expect(component.authUser).toEqual({});
    });

    it('should handle undefined/null/empty values in ParentCompanyId subscription', () => {
      projectService.ParentCompanyId.next(null);

      expect(component.ParentCompanyId).toBeUndefined();
    });
  });

  describe('ngOnInit', () => {
    it('should set up attachment trigger subscription', () => {
      const getAttachmentsSpy = jest.spyOn(component, 'getAttachments');
      component.ConcreteRequestId = '123';

      component.ngOnInit();

      // Trigger the observable
      projectService.projectParent.next({ ProjectId: '456', ParentCompanyId: '789' });

      setTimeout(() => {
        expect(component.ProjectId).toBe('456');
        expect(component.ParentCompanyId).toBe('789');
        expect(getAttachmentsSpy).toHaveBeenCalled();
      }, 150);
    });

    it('should not call getAttachments if ConcreteRequestId is not set', () => {
      const getAttachmentsSpy = jest.spyOn(component, 'getAttachments');
      component.ConcreteRequestId = null;

      component.ngOnInit();

      setTimeout(() => {
        expect(getAttachmentsSpy).not.toHaveBeenCalled();
      }, 150);
    });

    it('should handle null projectParent response', () => {
      component.ConcreteRequestId = '123';
      projectService.projectParent.next(null);

      component.ngOnInit();

      expect(component.ProjectId).toBeUndefined();
      expect(component.ParentCompanyId).toBeUndefined();
    });
  });

  describe('fileDataConvert', () => {
    it('should reject files larger than 2MB', async () => {
      const largeFile = new File(['x'.repeat(3000000)], 'large.jpg', { type: 'image/jpeg' });
      const mockFileEntry = {
        file: (callback: (file: File) => void) => callback(largeFile),
        isFile: true
      } as FileSystemFileEntry;

      const mockDropEntry: NgxFileDropEntry = {
        fileEntry: mockFileEntry,
        relativePath: 'large.jpg'
      };

      component.fileData = [[mockDropEntry]];

      await expect(component.fileDataConvert(component.fileData)).rejects.toThrow('File size exceeds limit');
      expect(toastrService.error).toHaveBeenCalledWith('Please choose a file less than or equal to 2MB');
    });

    it('should handle file reading errors', async () => {
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      const mockFileEntry = {
        file: (callback: (file: File) => void) => callback(mockFile),
        isFile: true
      } as FileSystemFileEntry;

      const mockDropEntry: NgxFileDropEntry = {
        fileEntry: mockFileEntry,
        relativePath: 'test.jpg'
      };

      component.fileData = [[mockDropEntry]];

      // Mock FileReader to simulate error
      const originalFileReader = global.FileReader;
      global.FileReader = jest.fn(() => ({
        readAsDataURL: jest.fn(),
        onload: null,
        onerror: null,
        result: null
      })) as any;

      const mockReader = new FileReader();
      jest.spyOn(mockReader, 'readAsDataURL').mockImplementation(() => {
        setTimeout(() => {
          if (mockReader.onerror) {
            mockReader.onerror({ target: { error: { message: 'Read error' } } } as any);
          }
        }, 0);
      });

      try {
        await component.fileDataConvert(component.fileData);
      } catch (error) {
        expect(error.message).toBe('Read error');
      }

      global.FileReader = originalFileReader;
    });

    it('should skip non-file entries', async () => {
      const mockFileEntry = {
        isFile: false
      } as unknown as FileSystemFileEntry;

      const mockDropEntry: NgxFileDropEntry = {
        fileEntry: mockFileEntry,
        relativePath: 'test.jpg'
      };

      component.fileData = [[mockDropEntry]];

      const result = await component.fileDataConvert(component.fileData);
      expect(result).toEqual([undefined]);
    });

    it('should handle valid file extensions', async () => {
      const validExtensions = ['jpg', 'jpeg', 'png', 'pdf', 'doc'];

      for (const ext of validExtensions) {
        const mockFile = new File(['test'], `test.${ext}`, { type: `image/${ext}` });
        const mockFileEntry = {
          file: (callback: (file: File) => void) => callback(mockFile),
          isFile: true
        } as FileSystemFileEntry;

        const mockDropEntry: NgxFileDropEntry = {
          fileEntry: mockFileEntry,
          relativePath: `test.${ext}`
        };

        component.fileData = [[mockDropEntry]];

        await expect(component.fileDataConvert(component.fileData)).resolves.not.toThrow();
      }
    });
  });

  describe('uploadData', () => {
    beforeEach(() => {
      component.formData = new FormData();
      component.ConcreteRequestId = '123';
      component.ParentCompanyId = '456';
      component.ProjectId = '789';
    });

    it('should successfully upload data', () => {
      component.formData.append('test', 'data');
      const mockResponse = { message: 'Upload successful' };
      deliveryService.addConcreteRequestAttachment.mockReturnValue(of(mockResponse));
      const getAttachmentsSpy = jest.spyOn(component, 'getAttachments');

      component.uploadData();

      expect(component.uploadSubmitted).toBe(true);
      expect(deliveryService.addConcreteRequestAttachment).toHaveBeenCalledWith(
        {
          ConcreteRequestId: '123',
          ParentCompanyId: '456',
          ProjectId: '789'
        },
        component.formData
      );
      expect(component.fileData).toEqual([]);
      expect(component.uploadSubmitted).toBe(false);
      expect(toastrService.success).toHaveBeenCalledWith(mockResponse.message, 'SUCCESS!');
      expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Attachment added against a Concrete Booking');
      expect(socket.emit).toHaveBeenCalledWith('ConcreteApproveHistory', mockResponse);
      expect(deliveryService.updateConcreteRequestHistory1).toHaveBeenCalledWith(
        { status: true },
        'ConcreteApproveHistory'
      );
      expect(getAttachmentsSpy).toHaveBeenCalled();
    });

    it('should handle upload error with status code 400', () => {
      component.formData.append('test', 'data');
      const mockError = {
        message: {
          statusCode: 400,
          details: [{ field: 'Invalid field' }]
        }
      };
      deliveryService.addConcreteRequestAttachment.mockReturnValue(throwError(() => mockError));
      const showErrorSpy = jest.spyOn(component, 'showError');

      component.uploadData();

      expect(component.uploadSubmitted).toBe(false);
      expect(showErrorSpy).toHaveBeenCalledWith(mockError);
    });

    it('should handle general upload error', () => {
      component.formData.append('test', 'data');
      const mockError = { message: 'General error' };
      deliveryService.addConcreteRequestAttachment.mockReturnValue(throwError(() => mockError));

      component.uploadData();

      expect(component.uploadSubmitted).toBe(false);
      expect(toastrService.error).toHaveBeenCalledWith(mockError.message, 'OOPS!');
    });

    it('should not upload if formData is empty', () => {
      component.uploadData();

      expect(deliveryService.addConcreteRequestAttachment).not.toHaveBeenCalled();
    });
  });

  describe('showError', () => {
    it('should display error message from details', () => {
      const mockError = {
        message: {
          details: [{ field: 'Invalid field value' }]
        }
      };

      component.showError(mockError);

      expect(toastrService.error).toHaveBeenCalledWith('Invalid field value');
    });

    it('should handle multiple error details', () => {
      const mockError = {
        message: {
          details: [{ field1: 'Error 1', field2: 'Error 2' }]
        }
      };

      component.showError(mockError);

      expect(toastrService.error).toHaveBeenCalledWith('Error 1,Error 2');
    });
  });

  describe('removeFile', () => {
    beforeEach(() => {
      component.formData = new FormData();
      component.fileData = [
        [{ relativePath: 'file1.jpg' }, { relativePath: 'file2.jpg' }],
        [{ relativePath: 'file3.jpg' }]
      ];
    });

    it('should remove file from fileData and formData', () => {
      const deleteFormDataSpy = jest.spyOn(component.formData, 'delete');

      component.removeFile(0, 1);

      expect(deleteFormDataSpy).toHaveBeenCalledWith('file2.jpg');
      expect(component.fileData[0]).toHaveLength(1);
      expect(component.fileData[0][0].relativePath).toBe('file1.jpg');
    });

    it('should remove entire array if last file in group', () => {
      component.removeFile(1, 0);

      expect(component.fileData).toHaveLength(1);
      expect(component.fileData[0]).toHaveLength(2);
    });

    it('should handle invalid indices gracefully', () => {
      const originalLength = component.fileData.length;

      component.removeFile(5, 0);
      component.removeFile(0, 5);

      expect(component.fileData).toHaveLength(originalLength);
    });
  });

  describe('getAttachments', () => {
    beforeEach(() => {
      component.ConcreteRequestId = '123';
      component.ParentCompanyId = '456';
      component.ProjectId = '789';
    });

    it('should fetch attachments successfully', () => {
      const mockResponse = {
        data: [{ id: 1, name: 'file1.jpg' }],
        concreteRequest: { id: '123', status: 'pending' }
      };
      deliveryService.getConcreteRequestAttachments.mockReturnValue(of(mockResponse));

      component.getAttachments();

      expect(deliveryService.getConcreteRequestAttachments).toHaveBeenCalledWith({
        ConcreteRequestId: '123',
        ParentCompanyId: '456',
        ProjectId: '789'
      });
      expect(component.fileArray).toEqual(mockResponse.data);
      expect(component.concreteRequest).toEqual(mockResponse.concreteRequest);
    });

    it('should handle getAttachments error', () => {
      deliveryService.getConcreteRequestAttachments.mockReturnValue(throwError(() => new Error('Fetch error')));

      expect(() => component.getAttachments()).not.toThrow();
    });
  });

  describe('selectStatus', () => {
    it('should call deliveryService.completeConcreteRequestStatus', () => {
      const mockData = { status: 'completed' };

      component.selectStatus(mockData);

      expect(deliveryService.completeConcreteRequestStatus).toHaveBeenCalledWith(mockData);
    });
  });

  describe('clickAndDisable', () => {
    it('should disable onclick for the provided link', () => {
      const mockLink = {
        onclick: jest.fn()
      };

      component.clickAndDisable(mockLink);

      // Simulate a click event
      const mockEvent = { preventDefault: jest.fn() };
      mockLink.onclick(mockEvent);

      expect(mockEvent.preventDefault).toHaveBeenCalled();
    });
  });

  describe('fileOver and fileLeave', () => {
    it('should handle fileOver event', () => {
      const mockEvent = { type: 'dragover' };

      expect(() => component.fileOver(mockEvent)).not.toThrow();
    });

    it('should handle fileLeave event', () => {
      const mockEvent = { type: 'dragleave' };

      expect(() => component.fileLeave(mockEvent)).not.toThrow();
    });
  });

  describe('Additional edge cases and negative scenarios', () => {
    describe('removeExistingFile error scenarios', () => {
      it('should handle error with status code 400', () => {
        const mockError = {
          message: {
            statusCode: 400,
            details: [{ field: 'Invalid field' }]
          }
        };
        deliveryService.removeConcreteRequestAttachment.mockReturnValue(throwError(() => mockError));
        const showErrorSpy = jest.spyOn(component, 'showError');

        component.removeExistingFile({ id: '123' });

        expect(showErrorSpy).toHaveBeenCalledWith(mockError);
        expect(component.loader).toBe(false);
      });

      it('should handle error without message', () => {
        const mockError = {};
        deliveryService.removeConcreteRequestAttachment.mockReturnValue(throwError(() => mockError));

        component.removeExistingFile({ id: '123' });

        expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
        expect(component.loader).toBe(false);
      });

      it('should set loader states correctly', () => {
        const mockResponse = { message: 'File removed successfully' };
        deliveryService.removeConcreteRequestAttachment.mockReturnValue(of(mockResponse));

        component.removeExistingFile({ id: '123' });

        expect(component.loader).toBe(true);
        expect(component.deleteUploadedFile).toBe(true);
        // After completion
        expect(component.deleteUploadedFile).toBe(false);
        expect(component.loader).toBe(false);
      });
    });

    describe('handleToggleKeydown edge cases', () => {
      it('should not call removeFile for other keys', () => {
        component.fileData = [[{ relativePath: 'test.jpg' }]];
        component.formData = new FormData();
        const removeFileSpy = jest.spyOn(component, 'removeFile');
        const event = new KeyboardEvent('keydown', { key: 'Tab' });

        component.handleToggleKeydown(event, 0, 0);

        expect(removeFileSpy).not.toHaveBeenCalled();
      });

      it('should prevent default for Enter and Space keys', () => {
        component.fileData = [[{ relativePath: 'test.jpg' }]];
        component.formData = new FormData();
        const event = new KeyboardEvent('keydown', { key: 'Enter' });
        const preventDefaultSpy = jest.spyOn(event, 'preventDefault');

        component.handleToggleKeydown(event, 0, 0);

        expect(preventDefaultSpy).toHaveBeenCalled();
      });
    });

    describe('dropped method edge cases', () => {
      it('should handle empty files array', async () => {
        await component.dropped([]);

        expect(component.files).toEqual([]);
        expect(component.fileData).toEqual([[]]);
      });

      it('should handle file entry without file method', async () => {
        const mockFileEntry = {
          isFile: true
        } as any;

        const mockDropEntry: NgxFileDropEntry = {
          fileEntry: mockFileEntry,
          relativePath: 'test.jpg'
        };

        // This should not throw an error
        await expect(component.dropped([mockDropEntry])).resolves.not.toThrow();
      });
    });

    describe('fileDataConvert edge cases', () => {
      it('should handle file without extension', async () => {
        const mockFile = new File(['test'], 'testfile', { type: 'text/plain' });
        const mockFileEntry = {
          file: (callback: (file: File) => void) => callback(mockFile),
          isFile: true
        } as FileSystemFileEntry;

        const mockDropEntry: NgxFileDropEntry = {
          fileEntry: mockFileEntry,
          relativePath: 'testfile'
        };

        component.fileData = [[mockDropEntry]];

        await expect(component.fileDataConvert(component.fileData)).rejects.toThrow('Invalid file format');
      });

      it('should handle uppercase file extensions', async () => {
        const mockFile = new File(['test'], 'test.JPG', { type: 'image/jpeg' });
        const mockFileEntry = {
          file: (callback: (file: File) => void) => callback(mockFile),
          isFile: true
        } as FileSystemFileEntry;

        const mockDropEntry: NgxFileDropEntry = {
          fileEntry: mockFileEntry,
          relativePath: 'test.JPG'
        };

        component.fileData = [[mockDropEntry]];

        await expect(component.fileDataConvert(component.fileData)).resolves.not.toThrow();
      });

      it('should handle file exactly at 2MB limit', async () => {
        const exactSize = new File(['x'.repeat(2000000)], 'exact.jpg', { type: 'image/jpeg' });
        const mockFileEntry = {
          file: (callback: (file: File) => void) => callback(exactSize),
          isFile: true
        } as FileSystemFileEntry;

        const mockDropEntry: NgxFileDropEntry = {
          fileEntry: mockFileEntry,
          relativePath: 'exact.jpg'
        };

        component.fileData = [[mockDropEntry]];

        await expect(component.fileDataConvert(component.fileData)).resolves.not.toThrow();
      });

      it('should handle missing fileData array element', async () => {
        const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
        const mockFileEntry = {
          file: (callback: (file: File) => void) => callback(mockFile),
          isFile: true
        } as FileSystemFileEntry;

        const mockDropEntry: NgxFileDropEntry = {
          fileEntry: mockFileEntry,
          relativePath: 'test.jpg'
        };

        component.fileData = []; // Empty array

        const result = await component.fileDataConvert([[mockDropEntry]]);
        expect(result).toBeDefined();
      });
    });

    describe('Constructor edge cases', () => {
      it('should handle empty string values in subscriptions', () => {
        deliveryService.loginUser.next('');
        projectService.ParentCompanyId.next('');

        expect(component.authUser).toEqual({});
        expect(component.ParentCompanyId).toBeUndefined();
      });
    });

    describe('Integration scenarios', () => {
      it('should handle complete file upload workflow', async () => {
        // Setup
        const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
        const mockFileEntry = {
          file: (callback: (file: File) => void) => callback(mockFile),
          isFile: true
        } as FileSystemFileEntry;

        const mockDropEntry: NgxFileDropEntry = {
          fileEntry: mockFileEntry,
          relativePath: 'test.jpg'
        };

        const uploadResponse = { message: 'Upload successful' };
        deliveryService.addConcreteRequestAttachment.mockReturnValue(of(uploadResponse));
        const getAttachmentsSpy = jest.spyOn(component, 'getAttachments');

        // Execute workflow
        await component.dropped([mockDropEntry]);
        component.uploadData();

        // Verify
        expect(component.files).toEqual([mockDropEntry]);
        expect(deliveryService.addConcreteRequestAttachment).toHaveBeenCalled();
        expect(getAttachmentsSpy).toHaveBeenCalled();
      });

      it('should handle file removal workflow', () => {
        // Setup
        component.fileData = [
          [{ relativePath: 'file1.jpg' }, { relativePath: 'file2.jpg' }]
        ];
        component.formData = new FormData();
        component.formData.append('attachment', new File(['test'], 'file2.jpg'), 'file2.jpg');

        // Execute
        component.removeFile(0, 1);

        // Verify
        expect(component.fileData[0]).toHaveLength(1);
        expect(component.fileData[0][0].relativePath).toBe('file1.jpg');
      });
    });
  });
});
