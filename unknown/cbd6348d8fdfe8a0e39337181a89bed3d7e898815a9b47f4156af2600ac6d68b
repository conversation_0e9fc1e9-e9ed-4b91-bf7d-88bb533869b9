<section class="page-section pt-md-50px">
  <div class="page-inner-content">
    <div class="top-header my-3">
      <div class="row">
        <div class="col-md-8">
          <div class="top-btn">
            <ul class="list-group list-group-horizontal-md">
              <li class="list-group-item p0 border-0 bg-transparent me-md-4 mb-3 mb-md-0">
                <button
                  class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular"
                  (click)="openModal(import)"
                  *ngIf="authUser?.User?.email != null && authUser?.User?.email != undefined"
                >
                  Import Multiple
                </button>
              </li>
              <li
                class="list-group-item p0 border-0 bg-transparent"
                *ngIf="currentlySelectedTab === 'tab1'"
              >
                <button
                  class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular"
                  (click)="openQueuedDeleteModal(-1, queuedDeleteModal)"
                  [disabled]="checkIfQueuedDeliveryRequestRowSelected()"
                >
                  Delete
                </button>
              </li>
            </ul>
          </div>
        </div>
        <div class="col-md-4">
          <div class="top-filter">
            <ul class="list-group list-group-horizontal justify-content-end">
              <li class="list-group-item p0 border-0 bg-transparent me-2">
                <div class="search-icon">
                  <input
                    class="form-control fs12 color-grey8"
                    [ngClass]="showSearchbar ? 'input-hover-disable' : 'input-search'"
                    placeholder="What are you looking for?"
                    (input)="getSearchNDR($event.target.value)"
                    [(ngModel)]="search"
                  />
                  <div class="icon">
                    <img
                      src="./assets/images/cross-close.svg"
                      *ngIf="showSearchbar"
                      (click)="clear()" (keydown)="handleDownKeydown($event, '', '','clear')"
                      alt="close-cross"
                    />
                    <em class="fa fa-search fs12 color-grey8" *ngIf="!showSearchbar"></em>
                  </div>
                </div>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent me-2 position-relative">
                <div class="filter-icon" (click)="openModal1(filter)" (keydown)="handleDownKeydown($event, filter, '','filter')">
                  <img src="./assets/images/filter.svg" class="h-12px icon" alt="Filter" />
                </div>
                <div
                  class="bg-orange rounded-circle position-absolute text-white filter-count"
                  *ngIf="filterCount > 0"
                >
                  <p class="m-0 text-center fs10">{{ filterCount }}</p>
                </div>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent">
                <button
                  type="button"
                  (click)="redirect('void-list')"
                  class="btn btn-orange-dark2 px-3 fs10 fw-bold cairo-regular"
                >
                  Void List
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="page-card bg-white rounded delivery-request-card">
      <span
        class="float-end fs12 me-3 p2 color-orange fw-bold"
        *ngIf="bulkNdrUploadInProgress"
      >
        <em class="fas fa-sync fa-spin" width="10px" alt="uploading"></em>
        Uploading
      </span>
      <tabset>
        <tab heading="Queued Delivery Bookings" [active]="currentlySelectedTab === 'tab1'">
          <div class="table-responsive tab-grid">
            <table class="table table-custom request-resizable mb-0" aria-describedby="Emptable">
              <thead>
                <th scope="col" *ngIf="queuedDeliveryList?.length > 0">
                  <div class="custom-control custom-checkbox text-black">
                    <input
                      type="checkbox"
                      class="custom-control-input"
                      id="tblData"
                      name="tblData"
                      (change)="selectAllQueuedDeliveryRequest()"
                      [checked]="queuedDeliveryRequestSelectAll"
                    />
                    <label class="custom-control-label c-pointer fs12" for="tblData"></label>
                  </div>
                </th>
                <th scope="col" resizable>
                  ID
                  <span>
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('id', 'ASC')" (keydown)="handleToggleKeydown($event, 'id', 'ASC')"
                      *ngIf="sortColumn !== 'id'"
                    />
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('id', 'ASC')"  (keydown)="handleToggleKeydown($event, 'id', 'ASC')"
                      *ngIf="sort === 'DESC' && sortColumn === 'id'"
                    />
                    <img
                      src="./assets/images/up-chevron.svg"
                      alt="up-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('id', 'DESC')"  (keydown)="handleToggleKeydown($event, 'id', 'DESC')"
                      *ngIf="sort === 'ASC' && sortColumn === 'id'"
                    />
                  </span>
                </th>
                <th scope="col" resizable>
                  Description
                  <span>
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('description', 'ASC')"  (keydown)="handleToggleKeydown($event, 'description', 'ASC')"
                      *ngIf="sortColumn !== 'description'"
                    />
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('description', 'ASC')"  (keydown)="handleToggleKeydown($event, 'description', 'ASC')"
                      *ngIf="sort === 'DESC' && sortColumn === 'description'"
                    />
                    <img
                      src="./assets/images/up-chevron.svg"
                      alt="up-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('description', 'DESC')"  (keydown)="handleToggleKeydown($event, 'description', 'DESC')"
                      *ngIf="sort === 'ASC' && sortColumn === 'description'"
                    />
                  </span>
                </th>
                <th scope="col" resizable>Date and Time</th>
                <th scope="col" resizable>
                  Status
                  <span>
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('status', 'ASC')"  (keydown)="handleToggleKeydown($event, 'status', 'ASC')"
                      *ngIf="sortColumn !== 'status'"
                    />
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('status', 'ASC')" (keydown)="handleToggleKeydown($event, 'status', 'ASC')"
                      *ngIf="sort === 'DESC' && sortColumn === 'status'"
                    />
                    <img
                      src="./assets/images/up-chevron.svg"
                      alt="up-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('status', 'DESC')" (keydown)="handleToggleKeydown($event, 'status', 'DESC')"
                      *ngIf="sort === 'ASC' && sortColumn === 'status'"
                    />
                  </span>
                </th>
                <th scope="col" resizable>
                  Approved By
                  <span>
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('approvedUser', 'ASC')" (keydown)="handleToggleKeydown($event, 'approvedUser', 'ASC')"
                      *ngIf="sortColumn !== 'approvedUser'"
                    />
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('approvedUser', 'ASC')" (keydown)="handleToggleKeydown($event, 'approvedUser', 'ASC')"
                      *ngIf="sort === 'DESC' && sortColumn === 'approvedUser'"
                    />
                    <img
                      src="./assets/images/up-chevron.svg"
                      alt="up-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('approvedUser', 'DESC')" (keydown)="handleToggleKeydown($event, 'approvedUser', 'DESC')"
                      *ngIf="sort === 'ASC' && sortColumn === 'approvedUser'"
                    />
                  </span>
                </th>
                <th scope="col" resizable>
                  Equipment
                  <span>
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('equipment', 'ASC')" (keydown)="handleToggleKeydown($event, 'equipment', 'ASC')"
                      *ngIf="sortColumn !== 'equipment'"
                    />
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('equipment', 'ASC')" (keydown)="handleToggleKeydown($event, 'equipment', 'ASC')"
                      *ngIf="sort === 'DESC' && sortColumn === 'equipment'"
                    />
                    <img
                      src="./assets/images/up-chevron.svg"
                      alt="up-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('equipment', 'DESC')" (keydown)="handleToggleKeydown($event, 'equipment', 'DESC')"
                      *ngIf="sort === 'ASC' && sortColumn === 'equipment'"
                    />
                  </span>
                </th>
                <th scope="col" resizable>Action</th>
              </thead>
              <tbody>
                <tr
                  *ngFor="
                    let item of queuedDeliveryList
                      | paginate
                        : {
                            id: 'pagination2',
                            itemsPerPage: queuedNDRPageSize,
                            currentPage: queuedNDRPageNo,
                            totalItems: queuedNDRTotalCount
                          };
                    let j = index
                  "
                  [ngStyle]="{
                    'background-color':
                      item.isAllDetailsFilled == false ? 'rgba(255, 0, 0, 0.5)' : 'white'
                  }"
                >
                  <td>
                    <div class="custom-control custom-checkbox text-black">
                      <input
                        type="checkbox"
                        class="custom-control-input"
                        id="tblData1_{{ j }}"
                        name="tblData1"
                        [checked]="item.isChecked"
                        (change)="setSelectedQueuedDeliveryRequestItem(j)"
                      />
                      <label
                        class="custom-control-label c-pointer fs12"
                        for="tblData1_{{ j }}"
                      ></label>
                    </div>
                  </td>
                  <td>
                    <span class="c-pointer" (click)="openIdModal(item, 'queued')" (keydown)="handleDownKeydown($event, item, 'queued','open')"
                      ><u>{{ item.DeliveryId }}</u></span
                    >
                  </td>
                  <td class="w-300px">
                    <span class="c-pointer" (click)="openIdModal(item, 'queued')" (keydown)="handleDownKeydown($event, item, 'queued','open')"
                      ><u>{{ item.description }}</u></span
                    >
                  </td>
                  <td
                    (click)="openIdModal(item, 'queued')" (keydown)="handleDownKeydown($event, item, 'queued','open')"
                    class="c-pointer"
                    *ngIf="item.deliveryStart"
                  >
                    {{ item.deliveryStart | date : 'medium' }}
                  </td>
                  <td
                    (click)="openIdModal(item, 'queued')" (keydown)="handleDownKeydown($event, item, 'queued','open')"
                    class="c-pointer"
                    *ngIf="!item.deliveryStart"
                  >
                    -
                  </td>
                  <td (click)="openIdModal(item, 'queued')" class="c-pointer" (keydown)="handleDownKeydown($event, item, 'queued','open')">
                    <div class="status" [style.background-color]="approvedBackgroundColor" [style.color]="approvedFontColor" *ngIf="item.status == 'Approved'">
                      {{ item.status }}
                    </div>
                    <div class="status" [style.background-color]="rejectedBackgroundColor" [style.color]="rejectedFontColor"  *ngIf="item.status == 'Declined'">
                      {{ item.status }}
                    </div>
                    <div class="status" [style.background-color]="pendingBackgroundColor" [style.color]="pendingFontColor"*ngIf="item.status == 'Pending'">
                      {{ item.status }}
                    </div>
                    <div class="status" [style.background-color]="expiredBackgroundColor" [style.color]="expiredFontColor" *ngIf="item.status == 'Expired'">
                      {{ item.status }}
                    </div>
                    <div class="status" [style.background-color]="deliveredBackgroundColor" [style.color]="deliveredFontColor" *ngIf="item.status == 'Delivered'">
                      {{ item.status }}
                    </div>
                 </td>
                  <td
                    (click)="openIdModal(item, 'queued')" (keydown)="handleDownKeydown($event, item, 'queued','open')"
                    class="c-pointer"
                    *ngIf="item.approverDetails?.User?.firstName"
                  >
                    {{ item.approverDetails?.User?.firstName }}
                    {{ item?.approverDetails?.User?.lastName }}
                  </td>
                  <td
                    (click)="openIdModal(item, 'queued')" (keydown)="handleDownKeydown($event, item, 'queued','open')"
                    class="c-pointer"
                    *ngIf="!item.approverDetails?.User?.firstName"
                  >
                    -
                  </td>
                  <td
                    (click)="openIdModal(item, 'queued')" (keydown)="handleDownKeydown($event, item, 'queued','open')"
                    class="c-pointer"
                    *ngIf="item.equipmentDetails?.length === 0"
                  >
                    -
                  </td>
                  <td
                    (click)="openIdModal(item, 'queued')" (keydown)="handleDownKeydown($event, item, 'queued','open')"
                    class="c-pointer"
                    *ngIf="item.equipmentDetails?.length > 0"
                  >
                    {{ item.equipmentDetails[0]?.Equipment?.equipmentName ? item.equipmentDetails[0]?.Equipment?.equipmentName : '-' }}
                  </td>
                  <td>
                    <ul class="list-inline mb-0">
                      <li
                        class="list-inline-item"
                        tooltip="Edit"
                        placement="top"
                        (click)="openEditModal(item, 'queued')" (keydown)="handleDownKeydown($event, item, 'queued','edit')"
                      >
                        <a href="javascript:void(0)"
                          ><img
                            src="./assets/images/edit.svg"
                            alt="edit"
                            class="h-15px action-icon"
                        /></a>
                      </li>
                      <li
                        class="list-inline-item mx-2"
                        tooltip="Delete"
                        placement="top"
                        (click)="openQueuedDeleteModal(j, queuedDeleteModal)" (keydown)="handleDownKeydown($event, j, queuedDeleteModal,'queue')"
                      >
                        <img src="./assets/images/delete.svg" alt="delete" class="h-15px" />
                      </li>
                    </ul>
                  </td>
                </tr>
              </tbody>
              <tr *ngIf="queuedNdrLoader == true">
                <td colspan="7" class="text-center">
                  <div class="fs18 fw-bold cairo-regular my-5 text-black">Loading...</div>
                </td>
              </tr>
              <tr *ngIf="queuedNdrLoader == false && queuedDeliveryList.length == 0">
                <td colspan="7" class="text-center">
                  <div class="fs18 fw-bold cairo-regular my-5 text-black">
                    No Records Found
                  </div>
                </td>
              </tr>
            </table>
          </div>
          <div
            class="tab-pagination px-2"
            id="tab-pagination4"
            *ngIf="queuedNdrLoader == false && queuedNDRTotalCount > 25"
          >
            <div class="row">
              <div class="col-md-2 align-items-center">
                <ul class="list-inline my-3">
                  <li class="list-inline-item notify-pagination">
                    <label class="fs12 color-grey4" for="showEnt">Show entries</label>
                  </li>
                  <li class="list-inline-item">
                    <select id="showEnt"
                    class="w-auto form-select fs12 color-grey4"
                      (change)="changeQueuedPageSize($event.target.value)"
                      ngModel="{{ queuedNDRPageSize }}"
                    >
                      <option value="25">25</option>
                      <option value="50">50</option>
                      <option value="100">100</option>
                      <option value="150">150</option>
                    </select>
                  </li>
                </ul>
              </div>
              <div class="col-md-8 text-center">
                <div class="my-3 position-relative d-inline-block">
                  <pagination-controls
                    id="pagination2"
                    (pageChange)="changeQueuedPageNo($event)"
                    previousLabel=""
                    nextLabel=""
                  >
                  </pagination-controls>
                </div>
              </div>
            </div>
          </div>
        </tab>
      </tabset>
    </div>
  </div>
</section>
<!-- modal -->
<div id="fiter-temp3">
  <!--Filter Modal-->
  <ng-template #filter>
    <div class="modal-header border-0 pb-0">
      <h3 class="fs14 fw-bold cairo-regular color-text7 my-0">Filter</h3>
      <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true"
          ><img src="./assets/images/modal-close.svg" alt="Modal Close"
        /></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="filter-content">
        <form
          class="custom-material-form"
          id="filter-form2"
          [formGroup]="filterForm"
          (ngSubmit)="filterSubmit()"
          novalidate
        >
          <div class="row">
            <div class="col-md-12">
              <div class="input-group mb-3">
                <input
                  type="text"
                  class="form-control fs12 material-input"
                  placeholder="Desciription"
                  formControlName="descriptionFilter"
                />
                  <span class="input-group-text">
                    <img src="./assets/images/search-icon.svg" alt="Search" />
                  </span>
              </div>
              <div class="input-group mb-3">
                <input
                  class="form-control fs12 material-input"
                  #dp="bsDatepicker"
                  bsDatepicker
                  formControlName="dateFilter"
                  placeholder="Delivery Date"
                  [bsConfig]="{
                    isAnimated: true,
                    showWeekNumbers: false,
                    customTodayClass: 'today'
                  }"
                />
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="companyFilter">
                  <option value="" disabled selected hidden>Company</option>
                  <option
                    *ngFor="let item of companyList"
                    value="{{ item.id }}"
                    [ngValue]="item.id"
                  >
                    {{ item.companyName }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="memberFilter">
                  <option value="" disabled selected hidden>Responsible Persons</option>
                  <ng-container *ngFor="let item of memberList">
                    <option *ngIf="item.status === 'pending'" value="{{ item.id }}">
                      {{ item.User?.email }}
                    </option>
                    <option *ngIf="item.status === 'completed'" value="{{ item.id }}">
                      {{ item.User?.firstName }}{{ item.User?.lastName }}
                    </option>
                  </ng-container>
                </select>
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="gateFilter">
                  <option value="" disabled selected hidden>Gate</option>
                  <option *ngFor="let item of gateList" value="{{ item.id }}" [ngValue]="item.id">
                    {{ item.gateName }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="equipmentFilter">
                  <option value="" disabled selected hidden>Equipment</option>
                  <option
                    *ngFor="let item of equipmentList"
                    value="{{ item.id }}"
                    [ngValue]="item.id"
                  >
                    {{ item.equipmentName }}
                  </option>
                </select>
              </div>
                <div class="form-group">
                <select
                  class="form-control fs12 material-input"
                  id="locationFilter1"
                  formControlName="locationFilter"
                >
                  <option value="" disabled selected hidden>Location</option>
                  <option
                    *ngFor="let item of locationList"
                    value="{{ item.locationPath }}"
                    [ngValue]="item.locationPath"
                  >
                    {{ item.locationPath }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="statusFilter">
                  <option value="" disabled selected hidden>Status</option>
                  <option *ngFor="let item of wholeStatus" value="{{ item }}">{{ item }}</option>
                </select>
              </div>
              <div class="input-group mb-3">
                <input
                  type="text"
                  class="form-control fs12 material-input"
                  placeholder="Pick From"
                  formControlName="pickFrom"
                />
                  <span class="input-group-text">
                    <img src="./assets/images/search-icon.svg" alt="Search" />
                  </span>
              </div>
              <div class="input-group mb-3">
                <input
                  type="text"
                  class="form-control fs12 material-input"
                  placeholder="Pick To"
                  formControlName="pickTo"
                />
                  <span class="input-group-text">
                    <img src="./assets/images/search-icon.svg" alt="Search" />
                  </span>
              </div>
              <div class="row justify-content-end">
                <button
                  class="btn btn-orange radius20 col-4 mt-2 fs12 fw-bold cairo-regular mx-1"
                  type="submit"
                >
                  Apply
                </button>
                <button
                  class="btn btn-orange radius20 fs12 col-4 mt-2 fw-bold cairo-regular mx-1"
                  type="button"
                  (click)="resetFilter()"
                >
                  Reset
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
  <!--Filter Modal-->
</div>

<ng-template #import>
  <div class="mx-2 mx-md-5 my-5 bulkupload">
    <button
      type="button"
      class="close upload-close border-0 bg-transparent"
      aria-label="Close"
      (click)="closeModal(cancelConfirmation)"
    >
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close ms-auto"
      /></span>
    </button>
    <h1 class="fw-bold cairo-regular fs20 text-center pb-4">
      Import Multiple Delivery Bookings
    </h1>
    <ngx-file-drop dropZoneLabel="Drop files here" (onFileDrop)="dropped($event)" multiple="true">
      <ng-template ngx-file-drop-content-tmp let-openFileSelector="openFileSelector">
        <div class="bulkupload-content text-center" (click)="openFileSelector()" (keydown)="openFileSelector()">
          <img src="./assets/images/file.svg" alt="Excel" />
          <p class="fs14 fw600 mb3 pt10 color-grey7">Drag & Drop your file here</p>
          <p class="fs12 color-grey8 fw500 mb3">Or</p>
          <label class="color-blue4 fs14 mb10 fw500 text-underline" for="browse">Click here to browse</label>
        </div>
      </ng-template>
    </ngx-file-drop>
    <p class="fs10 color-grey8 fw500 my-2 text-end">*Supported format is .xlsx</p>

    <div class="row upload-table py-3 m-0" *ngFor="let item of files; let i = index">
      <div class="col-1 col-md-1 ps-0">
        <img src="./assets/images/xlsx.png" alt="attached-file" class="rounded upload-img" />
      </div>
      <div class="col-9 col-md-9">
        <h1 class="fs16 ms-4 color-grey7">{{ item.relativePath }}</h1>
        <p class="ms-4 mb-0 color-grey8">
          {{ todayDate | date : 'short' }}<span class="ms-3"></span>
        </p>
      </div>
      <div
        class="col-1 col-md-2 text-end d-flex justify-content-end pe-0"
        (click)="removeFile(i)"  (keydown)="handleDownKeydown($event, i,'','remove')"
      >
        <img src="./assets/images/delete.svg" alt="delete" class="h-15px c-pointer" />
        <em
        class="fa fa-spinner ms-2"
        aria-hidden="true"
        *ngIf="loader"
      ></em
      >
      </div>
    </div>
    <div class="text-center">
      <button
        class="btn btn-orange radius20 fs12 fw-bold cairo-regular my-4 px-5 spin-btn"
        type="submit"
        (click)="importDeliveryRequest()"
        *ngIf="files.length > 0"
        [disabled]="importSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="importSubmitted"></em>
        Done
      </button>
      <p class="color-orange c-pointer fs12" (click)="download()"  (keydown)="handleDownKeydown($event, '','','download')">
        <u>Download sample template here</u>
      </p>
    </div>
  </div>
</ng-template>

<!--cancel confirmation Popup-->
<div id="confirm-popup8">
  <ng-template #cancelConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure you want to cancel?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetForm('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="resetForm('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
<!--cancel confirmation Popup-->

<!--Delete Modal-->
<ng-template #queuedDeleteModal>
  <div class="modal-body">
    <div class="text-center my-4" *ngIf="!removeQueuedDelivery">
      <p
        class="color-grey15 fs14 fw-bold cairo-regular mb-5"
        *ngIf="queuedDeliveryList[currentQueuedDeliveryDeleteId]?.description"
      >
        Are you sure you want to delete '{{
          queuedDeliveryList[currentQueuedDeliveryDeleteId]?.description | titlecase
        }}'?
      </p>
      <p
        class="color-grey15 fs14 fw-bold cairo-regular mb-5"
        *ngIf="!queuedDeliveryList[currentQueuedDeliveryDeleteId]?.description"
      >
        Are you sure you want to delete '{{
          queuedDeliveryList[currentQueuedDeliveryDeleteId]?.User?.email
        }}'?
      </p>
      <button
        class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
        (click)="close()"
      >
        No
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
        type="submit"
        (click)="deleteQueuedDeliveryRequest()"
        [disabled]="deleteDeliveryRequestSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="deleteDeliveryRequestSubmitted"></em>yes
      </button>
    </div>
    <div class="text-center my-4" id="remove-popup5" *ngIf="removeQueuedDelivery">
      <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
        Are you sure you want to delete?
      </p>
      <button
        class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
        (click)="close()"
      >
        No
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
        type="submit"
        (click)="removeDeliveryRequestItem()"
        [disabled]="deleteDeliveryRequestSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="deleteDeliveryRequestSubmitted"></em>Yes
      </button>
    </div>
  </div>
</ng-template>
<!--Remove Modal-->
