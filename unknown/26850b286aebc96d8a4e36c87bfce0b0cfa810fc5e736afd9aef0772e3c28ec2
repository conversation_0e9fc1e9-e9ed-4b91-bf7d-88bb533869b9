<div class="comments-form my-3 mx-3">
  <ul class="list-group radius0">
    <li
      class="list-group-item px-0 py-2 d-flex align-content-end border-start-0 border-end-0 border-top-0"
      *ngFor="let item of commentList"
    >
      <img
        src="./assets/images/default-user.svg"
        *ngIf="!item.Member?.User?.profilePic"
        alt="user-profile"
        class="rounded-circle h-30px w-30px"
      />
      <img
        src="{{ item.Member?.User?.profilePic }}"
        *ngIf="item.Member?.User?.profilePic"
        alt="user-profile"
        class="rounded-circle h-30px w-30px"
      />
      <div class="ps-3">
        <p class="color-grey4 fs12 fw600 mb-1">
          {{ item?.Member?.User?.firstName }} {{ item?.Member?.User?.lastName }}
        </p>
        <p class="color-grey4 fs10 mb-1">{{ item.comment }}</p>
        <p class="color-grey8 fs10 mb-0">{{ item.createdAt | date : 'medium' }}</p>
      </div>
    </li>
  </ul>
  <form
    class="custom-material-form"
    [formGroup]="commentDetailsForm"
    (ngSubmit)="onSubmit()"
    novalidate
  >
    <div class="row">
      <div class="col-md-9">
        <div class="form-group">
          <label class="fs12 fw500" for="comments">Enter your comments here</label>
          <textarea  id="comments"
            class="form-control fs12"
            formControlName="comment"
            placeholder=""
            rows="3"
            maxlength="150"
          ></textarea>
          <div class="color-red" *ngIf="submitted && commentDetailsForm.get('comment').errors">
            <small *ngIf="commentDetailsForm.get('comment').errors.required"
              >*Comment is required.</small
            >
          </div>
        </div>
        <div class="d-flex justify-content-end">
          <button
            class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem"
            type="submit"
            [disabled]="formSubmitted && commentDetailsForm.valid"
          >
            <em
              class="fa fa-spinner"
              aria-hidden="true"
              *ngIf="formSubmitted && commentDetailsForm.valid"
            ></em
            >Submit
          </button>
        </div>
      </div>
    </div>
  </form>
</div>
