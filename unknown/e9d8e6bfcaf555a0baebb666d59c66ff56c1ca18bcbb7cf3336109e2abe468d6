import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CraneCalendarComponent } from './crane-calendar.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Title } from '@angular/platform-browser';
import { ProjectService } from '../services/profile/project.service';
import { CalendarService } from '../services/profile/calendar.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { MixpanelService } from '../services/mixpanel.service';
import { of, throwError, BehaviorSubject } from 'rxjs';
import { FullCalendarComponent } from '@fullcalendar/angular';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import moment from 'moment';

describe('CraneCalendarComponent', () => {
  let component: CraneCalendarComponent;
  let fixture: ComponentFixture<CraneCalendarComponent>;
  let modalService: jest.Mocked<BsModalService>;
  let projectService: jest.Mocked<ProjectService>;
  let calendarService: jest.Mocked<CalendarService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let toastrService: jest.Mocked<ToastrService>;
  let router: jest.Mocked<Router>;
  let titleService: jest.Mocked<Title>;
  let mixpanelService: jest.Mocked<MixpanelService>;
  let formBuilder: UntypedFormBuilder;

  beforeEach(async () => {
    const modalServiceMock = {
      show: jest.fn().mockReturnValue({
        content: {
          lastId: 0,
          closeBtnName: 'Close'
        },
        hide: jest.fn()
      })
    };

    const projectServiceMock = {
      getCraneList: jest.fn(),
      projectId: of('123'),
      ParentCompanyId: of('456'),
      projectParent: new BehaviorSubject({ ParentCompanyId: '456', ProjectId: '123' }),
      gateList: jest.fn().mockReturnValue(of({ data: [] })),
      listCraneEquipment: jest.fn().mockReturnValue(of({ data: { rows: [] } })),
      getCompanies: jest.fn().mockReturnValue(of({ data: [] })),
      getDefinableWork: jest.fn().mockReturnValue(of({ data: [] })),
      getLocations: jest.fn().mockReturnValue(of({ data: [] })),
      listAllMember: jest.fn().mockReturnValue(of({ data: [] }))
    };

    const calendarServiceMock = {
      getCraneCalendar: jest.fn(),
      getDeliveryRequestWithCraneEquipmentType: jest.fn().mockReturnValue(of({
        data: [],
        statusData: { statusColorCode: '[]', useTextColorAsLegend: 'false', isDefaultColor: 'false' },
        cardData: { craneCard: '[]' },
        lastId: { CraneRequestId: 1 }
      }))
    };

    const deliveryServiceMock = {
      loginUser: of({ id: 1, name: 'Test User', RoleId: 1 }),
      fetchData: new BehaviorSubject(''),
      fetchData1: new BehaviorSubject(''),
      getCurrentCraneRequestStatus: new BehaviorSubject(''),
      listNDR: jest.fn(),
      getEquipmentCraneRequest: jest.fn().mockReturnValue(of({ data: { memberDetails: [], gateDetails: [] } })),
      getNDRData: jest.fn().mockReturnValue(of({ data: { memberDetails: [], gateDetails: [] } })),
      createVoid: jest.fn().mockReturnValue(of({ message: 'Success' })),
      addCraneRequestToVoid: jest.fn().mockReturnValue(of({ message: 'Success' })),
      updatedEditCraneRequestId: jest.fn(),
      updatedDeliveryId: jest.fn(),
      updatedCurrentStatus: jest.fn(),
      updatedCurrentCraneRequestStatus: jest.fn(),
      updateCraneRequestHistory: jest.fn()
    };

    const toastrServiceMock = {
      success: jest.fn(),
      error: jest.fn()
    };

    const routerMock = {
      navigate: jest.fn()
    };

    const titleServiceMock = {
      setTitle: jest.fn()
    };

    const mixpanelServiceMock = {
      track: jest.fn()
    };

    await TestBed.configureTestingModule({
      declarations: [CraneCalendarComponent],
      imports: [FormsModule, ReactiveFormsModule],
      providers: [
        { provide: BsModalService, useValue: modalServiceMock },
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: CalendarService, useValue: calendarServiceMock },
        { provide: DeliveryService, useValue: deliveryServiceMock },
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: Router, useValue: routerMock },
        { provide: Title, useValue: titleServiceMock },
        { provide: MixpanelService, useValue: mixpanelServiceMock },
        { provide: BsModalRef, useValue: { hide: jest.fn() } },
        UntypedFormBuilder
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
    }).compileComponents();

    modalService = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    calendarService = TestBed.inject(CalendarService) as jest.Mocked<CalendarService>;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    router = TestBed.inject(Router) as jest.Mocked<Router>;
    titleService = TestBed.inject(Title) as jest.Mocked<Title>;
    mixpanelService = TestBed.inject(MixpanelService) as jest.Mocked<MixpanelService>;
    formBuilder = TestBed.inject(UntypedFormBuilder);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CraneCalendarComponent);
    component = fixture.componentInstance;
    
    // Mock the calendar component and set calendarApi
    const calendarApi = {
      next: jest.fn(),
      prev: jest.fn(),
      nextYear: jest.fn(),
      prevYear: jest.fn(),
      changeView: jest.fn(),
      removeAllEventSources: jest.fn(),
      addEventSource: jest.fn(),
      currentData: {
        dateProfile: {
          activeRange: {
            start: new Date('2024-01-01'),
            end: new Date('2024-01-31')
          }
        }
      }
    };
    
    component.calendarComponent1 = {
      getApi: () => calendarApi
    } as any;
    
    component.calendarApi = calendarApi;

    // Initialize filter form
    component.filterForm = formBuilder.group({
      search: [''],
      status: [[]],
      companyFilter: [''],
      descriptionFilter: [''],
      dateFilter: [''],
      statusFilter: [[]],
      memberFilter: [''],
      gateFilter: [''],
      equipmentFilter: [[]],
      locationFilter: [[]],
      pickFrom: [''],
      pickTo: ['']
    });

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set title on initialization', () => {
    expect(titleService.setTitle).toHaveBeenCalledWith('Follo - Crane Calendar');
  });

  it('should initialize filter form', () => {
    expect(component.filterForm).toBeTruthy();
  });

  describe('Calendar Navigation', () => {
    it('should handle next navigation', () => {
      component.goNext();
      expect(component.calendarApi.next).toHaveBeenCalled();
    });

    it('should handle previous navigation', () => {
      component.goPrev();
      expect(component.calendarApi.prev).toHaveBeenCalled();
    });

    it('should handle next year navigation', () => {
      component.goNextYear();
      expect(component.calendarApi.nextYear).toHaveBeenCalled();
    });

    it('should handle previous year navigation', () => {
      component.goPrevYear();
      expect(component.calendarApi.prevYear).toHaveBeenCalled();
    });
  });

  describe('Event Handling', () => {
    it('should handle calendar description', () => {
      const mockEvent = {
        event: {
          id: '123',
          title: 'Test Description',
          extendedProps: {
            uniqueNumber: '456'
          }
        }
      };
      component.events = [{
        description: 'Test Description',
        uniqueNumber: '456'
      }];
      component.deliveryRequestWithCraneEquipmentType = [{
        id: 123,
        uniqueNumber: '456',
        repeatEveryType: 'Day',
        repeatEveryCount: '1',
        days: [],
        chosenDateOfMonth: false,
        dateOfMonth: '',
        monthlyRepeatType: '',
        endTime: new Date(),
        description: 'Test Description',
        status: 'Approved',
        memberDetails: [],
        companyDetails: [],
        defineWorkDetails: []
      }];
      component.calendarDescription(mockEvent);
      expect(component.viewEventData).toBeTruthy();
      expect(component.calendarDescriptionPopup).toBeTruthy();
    });

    it('should handle open add crane request modal', () => {
      const mockDateArg = { date: new Date() };
      component.openAddCraneRequestModal(mockDateArg);
      expect(modalService.show).toHaveBeenCalled();
    });
  });

  describe('Service Interactions', () => {
    it('should fetch delivery requests with crane equipment type', () => {
      component.getDeliveryRequestWithCraneEquipmentType();
      expect(calendarService.getDeliveryRequestWithCraneEquipmentType).toHaveBeenCalled();
    });

    // it('should handle error when fetching delivery requests', () => {
    //   calendarService.getDeliveryRequestWithCraneEquipmentType.mockReturnValue(throwError(() => ({
    //     message: {
    //       details: [{ message: 'Test Error' }]
    //     }
    //   })));
    //   component.getDeliveryRequestWithCraneEquipmentType();
    //   expect(toastrService.error).toHaveBeenCalled();
    // });
  });

  describe('Filter Operations', () => {
    // it('should reset filter', () => {
    //   component.modalRef = { hide: jest.fn() } as any;
    //   component.filterForm.patchValue({ search: 'test' });
    //   component.resetFilter();
    //   expect(component.filterCount).toBe(0);
    //   expect(component.filterForm.get('search').value).toBe('');
    //   expect(component.modalRef.hide).toHaveBeenCalled();
    // });

    it('should handle filter submission', () => {
      component.modalRef = { hide: jest.fn() } as any;
      const mockFilterData = {
        search: 'test',
        status: ['Approved'],
        companyFilter: '1',
        descriptionFilter: 'test',
        dateFilter: new Date(),
        statusFilter: ['Approved'],
        memberFilter: '1',
        gateFilter: '1',
        equipmentFilter: ['1'],
        locationFilter: ['1'],
        pickFrom: 'test',
        pickTo: 'test'
      };
      component.filterForm.patchValue(mockFilterData);
      component.filterSubmit();
      expect(component.filterCount).toBeGreaterThan(0);
      expect(component.modalRef.hide).toHaveBeenCalled();
    });
  });

  describe('Modal Operations', () => {
    it('should open modal', () => {
      const mockTemplate = {} as any;
      component.openModal(mockTemplate);
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should close modal', () => {
      component.close();
      expect(component.modalRef).toBeFalsy();
    });

    it('should open add NDR modal', () => {
      component.openAddNDRModal();
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should open filter modal', () => {
      const mockTemplate = {} as any;
      component.openFilterModal(mockTemplate);
      expect(modalService.show).toHaveBeenCalled();
      expect(projectService.gateList).toHaveBeenCalled();
    });

    it('should open edit modal for crane request', () => {
      const mockItem = {
        isAssociatedWithDeliveryRequest: false,
        isAssociatedWithCraneRequest: false,
        CraneRequestId: 123,
        recurrence: null
      };
      component.openEditModal(mockItem, 1);
      expect(deliveryService.updatedEditCraneRequestId).toHaveBeenCalledWith(123);
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should open edit modal for delivery request', () => {
      const mockItem = {
        isAssociatedWithDeliveryRequest: true,
        isAssociatedWithCraneRequest: false,
        id: 456,
        recurrence: { id: 1, recurrenceEndDate: '2024-12-31', recurrence: 'Daily' }
      };
      component.openEditModal(mockItem, 2);
      expect(deliveryService.updatedDeliveryId).toHaveBeenCalledWith(456);
      expect(modalService.show).toHaveBeenCalled();
    });
  });

  describe('Calendar View Changes', () => {
    it('should change to time grid week view', () => {
      component.goTimeGridWeekOrDay('timeGridWeek');
      expect(component.currentView).toBe('Week');
      expect(component.calendarApi.changeView).toHaveBeenCalledWith('timeGridWeek');
    });

    it('should change to time grid day view', () => {
      component.goTimeGridWeekOrDay('timeGridDay');
      expect(component.currentView).toBe('Day');
      expect(component.calendarApi.changeView).toHaveBeenCalledWith('timeGridDay');
    });

    it('should change to day grid month view', () => {
      component.goDayGridMonth();
      expect(component.currentView).toBe('Month');
      expect(component.calendarApi.changeView).toHaveBeenCalledWith('dayGridMonth');
    });
  });

  describe('Search and Clear Operations', () => {
    it('should handle search with data', () => {
      const searchData = 'test search';
      component.getSearchNDR(searchData);
      expect(component.showSearchbar).toBeTruthy();
      expect(component.search).toBe(searchData);
      expect(calendarService.getDeliveryRequestWithCraneEquipmentType).toHaveBeenCalled();
    });

    it('should handle search with empty data', () => {
      component.getSearchNDR('');
      expect(component.showSearchbar).toBeFalsy();
      expect(component.search).toBe('');
      expect(calendarService.getDeliveryRequestWithCraneEquipmentType).toHaveBeenCalled();
    });

    it('should clear search', () => {
      component.showSearchbar = true;
      component.search = 'test';
      component.clear();
      expect(component.showSearchbar).toBeFalsy();
      expect(component.search).toBe('');
      expect(calendarService.getDeliveryRequestWithCraneEquipmentType).toHaveBeenCalled();
    });
  });

  describe('Utility Methods', () => {
    it('should get responsible people acronym', () => {
      const person = { firstName: 'John', lastName: 'Doe' };
      const result = component.getResponsiblePeople(person);
      expect(result).toBe('JD');
    });

    it('should return UU for undefined person', () => {
      const result = component.getResponsiblePeople(undefined);
      expect(result).toBe('UU');
    });

    it('should return UU for person without names', () => {
      const person = { firstName: null, lastName: null };
      const result = component.getResponsiblePeople(person);
      expect(result).toBe('UU');
    });

    it('should format date correctly', () => {
      const testDate = new Date('2024-01-15');
      const result = component.changeFormat(testDate);
      expect(result).toContain('01/15/2024');
    });

    it('should return undefined for null date', () => {
      const result = component.changeFormat(null);
      expect(result).toBeUndefined();
    });
  });

  describe('Series Options and Request Collapse', () => {
    it('should initialize series options', () => {
      component.initializeSeriesOption();
      expect(component.seriesOptions).toHaveLength(2);
      expect(component.seriesOptions[0].option).toBe(1);
      expect(component.seriesOptions[1].option).toBe(2);
    });

    it('should change request collapse for future date', () => {
      const futureDate = moment().add(1, 'day').toISOString();
      const mockData = { craneDeliveryStart: futureDate };
      component.changeRequestCollapse(mockData);
      expect(component.allRequestIsOpened).toBeTruthy();
    });

    it('should change request collapse for past date', () => {
      const pastDate = moment().subtract(1, 'day').toISOString();
      const mockData = { craneDeliveryStart: pastDate };
      component.changeRequestCollapse(mockData);
      expect(component.allRequestIsOpened).toBeTruthy();
    });
  });

  describe('Data Processing Methods', () => {
    it('should create event object for crane request', () => {
      const element = {
        requestType: 'craneRequest',
        craneDeliveryStart: '2024-01-15T10:00:00Z',
        craneDeliveryEnd: '2024-01-15T12:00:00Z',
        CraneRequestId: 123,
        uniqueNumber: 'CR001',
        description: 'Test crane request',
        status: 'approved',
        companyDetails: [{ Company: { companyName: 'Test Company' } }],
        memberDetails: [{ Member: { User: { firstName: 'John', lastName: 'Doe' } } }],
        defineWorkDetails: [{ DeliverDefineWork: { DFOW: 'Test Work' } }],
        pickUpLocation: 'Location A',
        dropOffLocation: 'Location B'
      };
      const previewSelected = [
        { line: 1, label: 'Description', selected: true },
        { line: 2, label: 'Responsible Company', selected: true }
      ];
      const craneEquipmentName = 'Test Crane';
      const statusMap = {
        approved: { backgroundColor: '#green', fontColor: '#white' }
      };

      const result = component.createEventObject(element, previewSelected, craneEquipmentName, statusMap);

      expect(result.id).toBe(123);
      expect(result.description).toBe('Test crane request');
      expect(result.start).toBe('2024-01-15T10:00:00Z');
      expect(result.end).toBe('2024-01-15T12:00:00Z');
      expect(result.color).toBe('#green');
    });

    it('should create event object for delivery request with crane', () => {
      const element = {
        requestType: 'deliveryRequestWithCrane',
        deliveryStart: '2024-01-15T10:00:00Z',
        deliveryEnd: '2024-01-15T12:00:00Z',
        CraneRequestId: 456,
        uniqueNumber: 'DR001',
        status: 'pending',
        cranePickUpLocation: 'Location C',
        craneDropOffLocation: 'Location D'
      };
      const previewSelected = [
        { line: 1, label: 'Crane Pick ID', selected: true },
        { line: 2, label: 'Picking From', selected: true }
      ];
      const statusMap = {
        pending: { backgroundColor: '#yellow', fontColor: '#black' }
      };

      const result = component.createEventObject(element, previewSelected, '', statusMap);

      expect(result.id).toBe(456);
      expect(result.start).toBe('2024-01-15T10:00:00Z');
      expect(result.color).toBe('#yellow');
    });

    it('should create event object for calendar event', () => {
      const element = {
        requestType: 'calendarEvent',
        fromDate: '2024-01-15T10:00:00Z',
        toDate: '2024-01-15T12:00:00Z',
        id: 789,
        uniqueNumber: 'CE001',
        description: 'Test calendar event',
        isAllDay: true
      };

      const result = component.createEventObject(element, [], '', {});

      expect(result.id).toBe(789);
      expect(result.description).toBe('Test calendar event');
      expect(result.allDay).toBeTruthy();
      expect(result.allDaySlot).toBeTruthy();
    });
  });

  describe('Occur Message Generation', () => {
    it('should generate daily occurrence message', () => {
      const data = {
        repeatEveryType: 'Day',
        repeatEveryCount: '1',
        days: [],
        chosenDateOfMonth: false,
        dateOfMonth: '',
        monthlyRepeatType: '',
        endTime: '2024-12-31'
      };
      component.occurMessage(data);
      expect(component.message).toContain('Occurs every day');
      expect(component.message).toContain('until 12-31-2024');
    });

    it('should generate every other day occurrence message', () => {
      const data = {
        repeatEveryType: 'Days',
        repeatEveryCount: '2',
        days: [],
        chosenDateOfMonth: false,
        dateOfMonth: '',
        monthlyRepeatType: '',
        endTime: '2024-12-31'
      };
      component.occurMessage(data);
      expect(component.message).toContain('Occurs every other day');
    });

    it('should generate weekly occurrence message', () => {
      const data = {
        repeatEveryType: 'Week',
        repeatEveryCount: '1',
        days: ['Monday', 'Wednesday', 'Friday'],
        chosenDateOfMonth: false,
        dateOfMonth: '',
        monthlyRepeatType: '',
        endTime: '2024-12-31'
      };
      component.occurMessage(data);
      expect(component.message).toContain('Occurs every Monday,Wednesday,Friday');
    });

    it('should generate monthly occurrence message with date', () => {
      const data = {
        repeatEveryType: 'Month',
        repeatEveryCount: '1',
        days: [],
        chosenDateOfMonth: true,
        dateOfMonth: '15',
        monthlyRepeatType: '',
        endTime: '2024-12-31'
      };
      component.occurMessage(data);
      expect(component.message).toContain('Occurs on day 15');
    });

    it('should generate monthly occurrence message with repeat type', () => {
      const data = {
        repeatEveryType: 'Month',
        repeatEveryCount: '1',
        days: [],
        chosenDateOfMonth: false,
        dateOfMonth: '',
        monthlyRepeatType: 'first Monday',
        endTime: '2024-12-31'
      };
      component.occurMessage(data);
      expect(component.message).toContain('Occurs on the first Monday');
    });
  });

  describe('Description and Event Handling', () => {
    beforeEach(() => {
      component.events = [
        {
          description: 'Test Event',
          uniqueNumber: 'TE001'
        }
      ];
      component.deliveryRequestWithCraneEquipmentType = [
        {
          id: 1,
          description: 'Test Event',
          uniqueNumber: 'TE001',
          repeatEveryType: 'Day',
          repeatEveryCount: '1',
          days: [],
          chosenDateOfMonth: false,
          dateOfMonth: '',
          monthlyRepeatType: '',
          endTime: '2024-12-31'
        }
      ];
    });

    it('should handle calendar description', () => {
      const mockEvent = {
        event: {
          title: 'Test Event',
          extendedProps: {
            uniqueNumber: 'TE001'
          }
        }
      };
      component.calendarDescription(mockEvent);
      expect(component.calendarDescriptionPopup).toBeTruthy();
      expect(component.viewEventData).toBeTruthy();
    });

    it('should close calendar description', () => {
      component.calendarDescriptionPopup = true;
      component.descriptionPopup = true;
      component.viewEventData = 'test';
      component.closeCalendarDescription();
      expect(component.calendarDescriptionPopup).toBeFalsy();
      expect(component.descriptionPopup).toBeFalsy();
      expect(component.viewEventData).toBe('');
    });

    it('should close description', () => {
      component.descriptionPopup = true;
      component.closeDescription();
      expect(component.descriptionPopup).toBeFalsy();
    });
  });

  describe('Void Operations', () => {
    beforeEach(() => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.calendarCurrentDeliveryIndex = 0;
      component.deliveryRequestWithCraneEquipmentType = [
        {
          id: 1,
          isAssociatedWithDeliveryRequest: false,
          isAssociatedWithCraneRequest: false
        }
      ];
    });

    it('should move crane request to void list', () => {
      // Setup test data
      component.voidSubmitted = false;
      component.calendarCurrentDeliveryIndex = 0;
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.deliveryRequestWithCraneEquipmentType = [
        {
          id: 1,
          isAssociatedWithDeliveryRequest: false,
          isAssociatedWithCraneRequest: false
        }
      ];

      // Spy on the moveCraneRequestToVoid method
      const moveCraneRequestToVoidSpy = jest.spyOn(component, 'moveCraneRequestToVoid').mockImplementation(() => {});

      // Call the method
      component.moveToVoidList();

      // Verify that the method was called with correct parameters
      expect(moveCraneRequestToVoidSpy).toHaveBeenCalledWith({
        CraneRequestId: 1,
        ProjectId: '123',
        ParentCompanyId: '456'
      });

      // Restore the spy
      moveCraneRequestToVoidSpy.mockRestore();
    });

    it('should move delivery request to void list', () => {
      component.deliveryRequestWithCraneEquipmentType[0].isAssociatedWithDeliveryRequest = true;
      component.voidSubmitted = false;
      component.moveToVoidList();
      expect(component.voidSubmitted).toBeTruthy();
      expect(deliveryService.createVoid).toHaveBeenCalled();
    });

    it('should not move to void if already submitted', () => {
      component.voidSubmitted = true;
      component.moveToVoidList();
      expect(deliveryService.addCraneRequestToVoid).not.toHaveBeenCalled();
      expect(deliveryService.createVoid).not.toHaveBeenCalled();
    });

    it('should handle void confirmation response - no', () => {
      component.modalRef1 = { hide: jest.fn() } as any;
      component.voidConfirmationResponse('no');
      expect(component.modalRef1.hide).toHaveBeenCalled();
    });

    it('should handle void confirmation response - yes', () => {
      component.modalRef1 = { hide: jest.fn() } as any;
      component.voidSubmitted = false;
      component.voidConfirmationResponse('yes');
      expect(component.modalRef1.hide).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should show error message', () => {
      const mockError = {
        message: {
          details: [{ error: 'Test error message' }]
        }
      };
      component.showError(mockError);
      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
      expect(toastrService.error).toHaveBeenCalled();
    });

    it('should handle delivery request void error', () => {
      const mockError = {
        message: { statusCode: 400, details: [{ error: 'Test error' }] }
      };
      deliveryService.createVoid.mockReturnValue(throwError(() => mockError));

      component.moveDeliveryRequestToVoid({ DeliveryRequestId: 1, ProjectId: '123' });

      expect(component.voidSubmitted).toBeFalsy();
    });

    it('should handle crane request void error', () => {
      const mockError = {
        message: { statusCode: 400, details: [{ error: 'Test error' }] }
      };
      deliveryService.addCraneRequestToVoid.mockReturnValue(throwError(() => mockError));

      component.moveCraneRequestToVoid({ CraneRequestId: 1, ProjectId: '123', ParentCompanyId: '456' });

      expect(component.voidSubmitted).toBeFalsy();
    });
  });

  describe('Keyboard Event Handling', () => {
    it('should handle Enter key for filter', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      const mockTemplate = {} as any;
      component.handleDownKeydown(mockEvent, mockTemplate, null, 'filter');
      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(projectService.gateList).toHaveBeenCalled();
    });

    it('should handle Space key for open', () => {
      const mockEvent = { key: ' ', preventDefault: jest.fn() } as any;
      const mockData = { id: 1 };
      component.ParentCompanyId = '456';
      component.ProjectId = '123';
      component.eventData = { isAssociatedWithDeliveryRequest: false, isAssociatedWithCraneRequest: false };
      component.handleDownKeydown(mockEvent, mockData, null, 'open');
      expect(mockEvent.preventDefault).toHaveBeenCalled();
    });

    it('should handle Enter key for close', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      component.handleDownKeydown(mockEvent, null, null, 'close');
      expect(mockEvent.preventDefault).toHaveBeenCalled();
    });

    it('should handle Space key for edit', () => {
      const mockEvent = { key: ' ', preventDefault: jest.fn() } as any;
      const mockData = { isAssociatedWithDeliveryRequest: false, isAssociatedWithCraneRequest: false };
      component.handleDownKeydown(mockEvent, mockData, 1, 'edit');
      expect(mockEvent.preventDefault).toHaveBeenCalled();
    });

    it('should handle Enter key for clear', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      component.handleDownKeydown(mockEvent, null, null, 'clear');
      expect(mockEvent.preventDefault).toHaveBeenCalled();
    });

    it('should not handle other keys', () => {
      const mockEvent = { key: 'Tab', preventDefault: jest.fn() } as any;
      component.handleDownKeydown(mockEvent, null, null, 'filter');
      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
    });
  });

  describe('Lifecycle Methods', () => {
    it('should handle ngAfterViewInit', () => {
      component.ngAfterViewInit();
      expect(projectService.listAllMember).toHaveBeenCalled();
    });

    it('should get members', () => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.getMembers();
      expect(projectService.listAllMember).toHaveBeenCalledWith({
        ProjectId: '123',
        ParentCompanyId: '456'
      });
    });

    it('should set calendar', () => {
      component.setCalendar();
      expect(component.calendarApi).toBeTruthy();
      expect(component.Range).toBeTruthy();
      expect(calendarService.getDeliveryRequestWithCraneEquipmentType).toHaveBeenCalled();
    });
  });

  describe('Complex Event Handling', () => {
    beforeEach(() => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.authUser = { id: 1, RoleId: 1 };
      component.deliveryRequestWithCraneEquipmentType = [
        {
          id: 1,
          CraneRequestId: 123,
          requestType: 'craneRequest',
          craneDeliveryStart: '2024-01-15T10:00:00Z',
          craneDeliveryEnd: '2024-01-15T12:00:00Z',
          approved_at: '2024-01-15T09:00:00Z',
          memberDetails: [
            { Member: { User: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' }, id: 1 } },
            { Member: { User: { firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>' }, id: 2 } },
            { Member: { User: { firstName: 'Bob', lastName: 'Johnson', email: '<EMAIL>' }, id: 3 } },
            { Member: { User: { firstName: 'Alice', lastName: 'Brown', email: '<EMAIL>' }, id: 4 } }
          ]
        },
        {
          id: 2,
          CraneRequestId: 456,
          requestType: 'deliveryRequestWithCrane',
          deliveryStart: '2024-01-16T10:00:00Z',
          deliveryEnd: '2024-01-16T12:00:00Z',
          approved_at: null
        }
      ];
    });

    it('should handle delivery description for crane request', () => {
      const mockEvent = {
        event: {
          id: '123',
          extendedProps: {}
        }
      };
      component.deliveryDescription(mockEvent);
      expect(deliveryService.getEquipmentCraneRequest).toHaveBeenCalled();
    });

    it('should handle delivery description for delivery request with crane', () => {
      const mockEvent = {
        event: {
          id: '456',
          extendedProps: {}
        }
      };
      component.deliveryDescription(mockEvent);
      expect(deliveryService.getNDRData).toHaveBeenCalled();
    });

    it('should get crane request details', () => {
      const mockData = { event: { id: '123' } };
      component.getCraneRequest(mockData);
      expect(deliveryService.getEquipmentCraneRequest).toHaveBeenCalledWith({
        CraneRequestId: 123,
        ParentCompanyId: '456',
        ProjectId: '123'
      });
    });

    it('should get NDR details', () => {
      const mockData = { event: { id: '456' } };
      component.getNDR(mockData);
      expect(deliveryService.getNDRData).toHaveBeenCalled();
    });

    it('should open ID modal for crane request', () => {
      const mockItem = {
        CraneRequestId: 123,
        id: 1
      };
      component.eventData = {
        isAssociatedWithDeliveryRequest: false,
        isAssociatedWithCraneRequest: false
      };
      component.openIdModal(mockItem);
      expect(deliveryService.updatedCurrentCraneRequestStatus).toHaveBeenCalledWith(123);
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should open ID modal for delivery request', () => {
      const mockItem = {
        id: 456
      };
      component.eventData = {
        isAssociatedWithDeliveryRequest: true,
        isAssociatedWithCraneRequest: false
      };
      component.openIdModal(mockItem);
      expect(deliveryService.updatedCurrentStatus).toHaveBeenCalledWith(456);
      expect(modalService.show).toHaveBeenCalled();
    });
  });

  describe('Filter Operations Advanced', () => {
    beforeEach(() => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.modalRef = { hide: jest.fn() } as any;
    });

    it('should get overall gate list', () => {
      // Ensure modalLoader starts as false
      component.modalLoader = false;

      // Spy on the calendarGetOverAllEquipment method to prevent it from being called
      const calendarGetOverAllEquipmentSpy = jest.spyOn(component, 'calendarGetOverAllEquipment').mockImplementation(() => {});

      component.calendarGetOverAllGate();

      expect(component.modalLoader).toBeTruthy();
      expect(projectService.gateList).toHaveBeenCalledWith(
        {
          ProjectId: '123',
          pageSize: 0,
          pageNo: 0,
          ParentCompanyId: '456'
        },
        {
          isFilter: true,
          showActivatedAlone: true
        }
      );

      // Restore the spy
      calendarGetOverAllEquipmentSpy.mockRestore();
    });

    it('should get overall equipment list', () => {
      component.calendarGetOverAllEquipment();
      expect(projectService.listCraneEquipment).toHaveBeenCalledWith(
        {
          ProjectId: '123',
          pageSize: 0,
          pageNo: 0,
          ParentCompanyId: '456'
        },
        {
          showActivatedAlone: true
        }
      );
    });

    it('should get company list', () => {
      component.calendarGetCompany();
      expect(projectService.getCompanies).toHaveBeenCalledWith({
        ProjectId: '123',
        ParentCompanyId: '456'
      });
    });

    it('should get definable work list', () => {
      component.calendarGetDefinable();
      expect(projectService.getDefinableWork).toHaveBeenCalledWith({
        ProjectId: '123',
        ParentCompanyId: '456'
      });
    });

    it('should get locations', () => {
      component.getLocations();
      expect(projectService.getLocations).toHaveBeenCalledWith({
        ProjectId: '123',
        ParentCompanyId: '456'
      });
    });

    it('should open content modal', () => {
      component.modalLoader = true;
      component.openContentModal();
      expect(component.modalLoader).toBeFalsy();
    });
  });

  describe('Build Payload and Filter Params', () => {
    beforeEach(() => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.search = 'test search';
      component.filterCount = 5;
      component.currentView = 'Month';
      component.Range = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };
    });

    it('should build payload and filter params', () => {
      const result = component.buildPayloadAndFilterParams();

      expect(result.filterParams).toEqual({
        ProjectId: '123',
        void: 0
      });

      expect(result.payload.ParentCompanyId).toBe('456');
      expect(result.payload.search).toBe('test search');
      expect(result.payload.filterCount).toBe(5);
      expect(result.payload.calendarView).toBe('Month');
    });

    it('should build payload with form values', () => {
      component.filterForm.patchValue({
        companyFilter: '1',
        descriptionFilter: 'test desc',
        dateFilter: new Date('2024-01-15'),
        statusFilter: ['Approved'],
        memberFilter: '2',
        gateFilter: '3',
        equipmentFilter: ['4'],
        locationFilter: ['5'],
        pickFrom: 'Location A',
        pickTo: 'Location B'
      });

      const result = component.buildPayloadAndFilterParams();

      expect(result.payload.companyFilter).toBe(1);
      expect(result.payload.descriptionFilter).toBe('test desc');
      expect(result.payload.statusFilter).toEqual(['Approved']);
      expect(result.payload.memberFilter).toBe(2);
      expect(result.payload.gateFilter).toBe(3);
      expect(result.payload.equipmentFilter).toEqual(['4']);
      expect(result.payload.locationFilter).toEqual(['5']);
      expect(result.payload.pickFrom).toBe('Location A');
      expect(result.payload.pickTo).toBe('Location B');
    });
  });

  describe('Parse Delivery Request Response', () => {
    it('should parse delivery request response correctly', () => {
      const mockResponse = {
        data: [
          {
            requestType: 'craneRequest',
            CraneRequestId: 123,
            craneDeliveryStart: '2024-01-15T10:00:00Z',
            craneDeliveryEnd: '2024-01-15T12:00:00Z',
            status: 'approved',
            equipmentDetails: [
              {
                Equipment: {
                  equipmentName: 'Test Crane',
                  PresetEquipmentType: { isCraneType: true }
                }
              }
            ]
          }
        ],
        statusData: {
          statusColorCode: JSON.stringify([
            { status: 'approved', backgroundColor: '#green', fontColor: '#white' },
            { status: 'pending', backgroundColor: '#yellow', fontColor: '#black' },
            { status: 'delivered', backgroundColor: '#blue', fontColor: '#white' },
            { status: 'rejected', backgroundColor: '#red', fontColor: '#white' },
            { status: 'expired', backgroundColor: '#gray', fontColor: '#white' }
          ]),
          useTextColorAsLegend: 'false',
          isDefaultColor: 'false'
        },
        cardData: {
          craneCard: JSON.stringify([
            { line: 1, label: 'Description', selected: true }
          ])
        },
        lastId: { CraneRequestId: 999 }
      };

      component.parseDeliveryRequestResponse(mockResponse);

      expect(component.lastId).toBe(999);
      expect(component.deliveryRequestWithCraneEquipmentType).toHaveLength(1);
      expect(component.events).toHaveLength(1);
      expect(component.approved).toBe('#green');
      expect(component.pending).toBe('#yellow');
      expect(component.calendarApi.removeAllEventSources).toHaveBeenCalled();
      expect(component.calendarApi.addEventSource).toHaveBeenCalled();
    });

    it('should handle use text color as legend', () => {
      const mockResponse = {
        data: [],
        statusData: {
          statusColorCode: JSON.stringify([
            { status: 'approved', backgroundColor: '#green', fontColor: '#white' },
            { status: 'pending', backgroundColor: '#yellow', fontColor: '#black' },
            { status: 'delivered', backgroundColor: '#blue', fontColor: '#white' },
            { status: 'rejected', backgroundColor: '#red', fontColor: '#white' },
            { status: 'expired', backgroundColor: '#gray', fontColor: '#white' }
          ]),
          useTextColorAsLegend: 'true',
          isDefaultColor: 'false'
        },
        cardData: { craneCard: '[]' },
        lastId: { CraneRequestId: 1 }
      };

      component.parseDeliveryRequestResponse(mockResponse);
      expect(component.approved).toBe('#white');
      expect(component.pending).toBe('#black');
      expect(component.delivered).toBe('#white');
      expect(component.rejected).toBe('#white');
      expect(component.expired).toBe('#white');
    });
  });
});
