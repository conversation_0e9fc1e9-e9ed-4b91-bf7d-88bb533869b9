import { Component, OnDestroy } from '@angular/core';
import {
  debounceTime, merge, Subscription, tap,
} from 'rxjs';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';

@Component({
  selector: 'app-history',
  templateUrl: './history.component.html',
})
export class HistoryComponent implements OnDestroy {
  public historyList: any = [];

  public DeliveryRequestId: any;

  public loader = true;

  public ProjectId: any;

  public ParentCompanyId: any;

  private readonly subscription: Subscription = new Subscription();

  public constructor(
    private readonly deliveryService: DeliveryService,
    public projectService: ProjectService,
  ) {
    this.projectService.projectParent.subscribe((response5): void => {
      if (response5 !== undefined && response5 !== null && response5 !== '') {
        this.loader = true;
        this.ProjectId = response5.ProjectId;
        this.ParentCompanyId = response5.ParentCompanyId;
        this.loader = true;
      }
    });

    const mergedStreams$ = merge(
      this.deliveryService.DeliveryRequestId.pipe(
        tap((val) => {
          this.DeliveryRequestId = val;
        }),
      ),
      this.deliveryService.refresh,
      this.deliveryService.refresh1,
    ).pipe(
      debounceTime(100),
    );

    this.subscription.add(
      mergedStreams$.subscribe(() => {
        this.getHistory();
      }),
    );
  }

  public getHistory(): void {
    this.loader = true;
    const param = {
      DeliveryRequestId: this.DeliveryRequestId,
      ParentCompanyId: this.ParentCompanyId,
    };

    const historySub = this.deliveryService.getHistory(param);
    this.subscription.add(
      historySub.subscribe((res): void => {
        this.historyList = res.data;
        this.historyList = this.historyList.filter((data: { type: string }) => data.type !== 'comment');
        this.loader = false;
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
