<section class="page-section pt-md-50px">
  <div class="page-inner-content">
    <div class="top-header my-3">
      <div class="row">
        <div class="col-md-8">
          <div class="top-btn">
            <ul class="list-group list-group-horizontal-md">
              <li class="list-group-item p0 border-0 bg-transparent me-md-4 mb-3 mb-md-0">
                <button
                  class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular"
                  (click)="openAddModal()"
                >
                  Create New
                </button>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent me-md-4 mb-3 mb-md-0">
                <button
                  class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular"
                  (click)="openEditMultipleModal(editMultipleDeliveryRequest)"
                  *ngIf="authUser?.User?.email != null && authUser?.User?.email != undefined"
                  [disabled]="checkIfCurrentDeliveryRequestRowSelected()"
                >
                  Edit
                </button>
              </li>
            </ul>
          </div>
        </div>
        <div class="col-md-4">
          <div class="top-filter">
            <ul class="list-group list-group-horizontal justify-content-end">
              <li class="list-group-item p0 border-0 bg-transparent me-2">
                <div class="search-icon">
                  <input
                    class="form-control fs12 color-grey8"
                    [ngClass]="showSearchbar ? 'input-hover-disable' : 'input-search'"
                    placeholder="What are you looking for?"
                    (input)="getSearch($event.target.value)"
                    [(ngModel)]="search"
                  />
                  <div class="icon">
                    <img
                      src="./assets/images/cross-close.svg"
                      *ngIf="showSearchbar"
                      (click)="clear()" (keydown)="handleDownKeydown($event, '', '','clear')"
                      alt="close-cross"
                    />
                    <em class="fa fa-search fs12 color-grey8" *ngIf="!showSearchbar"></em>
                  </div>
                </div>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent me-2 position-relative">
                <div class="filter-icon" (click)="openFilterModal(filter)"  (keydown)="handleDownKeydown($event, filter, '','filter')">
                  <img src="./assets/images/filter.svg" class="h-12px icon" alt="Filter" />
                </div>
                <div
                  class="bg-orange rounded-circle position-absolute text-white filter-count"
                  *ngIf="filterCount > 0"
                >
                  <p class="m-0 text-center fs10">{{ filterCount }}</p>
                </div>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent">
                <button
                  type="button"
                  class="btn btn-orange-dark2 px-3 fs10 fw-bold cairo-regular"
                  (click)="redirect('void-list')"
                >
                  Void List
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="page-card bg-white rounded delivery-request-card">
      <tabset>
        <tab heading="Concrete Bookings">
          <div class="table-responsive tab-grid">
            <table
              class="table table-custom request-resizable concrete-request-status mb-0"
              aria-describedby="Emptable"
            >
              <thead>
                <th scope="col" *ngIf="concreteRequestList?.length > 0">
                  <div class="custom-control custom-checkbox text-black">
                    <input
                      type="checkbox"
                      class="custom-control-input"
                      id="tb2Data"
                      name="tb2Data"
                      (change)="selectAllCurrentDeliveryRequestForEdit()"
                      [checked]="currentDeliveryRequestSelectAll"
                    />
                    <label class="custom-control-label c-pointer fs12" for="tb2Data"></label>
                  </div>
                </th>
                <th scope="col" resizable>
                  ID
                  <span>
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortConcreteRequestByField('id', 'ASC')"
                      (keydown)="handleToggleKeydown($event, 'id', 'ASC')"
                      *ngIf="sortColumn !== 'id'"
                    />
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortConcreteRequestByField('id', 'ASC')"
                      (keydown)="handleToggleKeydown($event, 'id', 'ASC')"
                      *ngIf="sort === 'DESC' && sortColumn === 'id'"
                    />
                    <img
                      src="./assets/images/up-chevron.svg"
                      alt="up-arrow"
                      class="h-10px ms-2"
                      (click)="sortConcreteRequestByField('id', 'DESC')"
                      (keydown)="handleToggleKeydown($event, 'id', 'DESC')"
                      *ngIf="sort === 'ASC' && sortColumn === 'id'"
                    />
                  </span>
                </th>
                <th scope="col" resizable>
                  Description
                  <span>
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortConcreteRequestByField('description', 'ASC')"
                      (keydown)="handleToggleKeydown($event, 'description', 'ASC')"
                      *ngIf="sortColumn !== 'description'"
                    />
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortConcreteRequestByField('description', 'ASC')"
                      (keydown)="handleToggleKeydown($event, 'description', 'ASC')"
                      *ngIf="sort === 'DESC' && sortColumn === 'description'"
                    />
                    <img
                      src="./assets/images/up-chevron.svg"
                      alt="up-arrow"
                      class="h-10px ms-2"
                      (click)="sortConcreteRequestByField('description', 'DESC')"  (keydown)="handleToggleKeydown($event, 'description', 'DESC')"
                      *ngIf="sort === 'ASC' && sortColumn === 'description'"
                    />
                  </span>
                </th>
                <th scope="col" resizable>
                  Date and Time
                  <span>
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortConcreteRequestByField('concretePlacementStart', 'ASC')"  (keydown)="handleToggleKeydown($event, 'concretePlacementStart', 'ASC')"
                      *ngIf="sortColumn !== 'concretePlacementStart'"
                    />
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortConcreteRequestByField('concretePlacementStart', 'ASC')"
                      (keydown)="handleToggleKeydown($event, 'concretePlacementStart', 'ASC')"
                      *ngIf="sort === 'DESC' && sortColumn === 'concretePlacementStart'"
                    />
                    <img
                      src="./assets/images/up-chevron.svg"
                      alt="up-arrow"
                      class="h-10px ms-2"
                      (click)="sortConcreteRequestByField('concretePlacementStart', 'DESC')"
                      (keydown)="handleToggleKeydown($event, 'concretePlacementStart', 'DESC')"
                      *ngIf="sort === 'ASC' && sortColumn === 'concretePlacementStart'"
                    />
                  </span>
                </th>
                <th scope="col" resizable>
                  Status
                  <span>
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortConcreteRequestByField('status', 'ASC')"
                      (keydown)="handleToggleKeydown($event, 'status', 'ASC')"
                      *ngIf="sortColumn !== 'status'"
                    />
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortConcreteRequestByField('status', 'ASC')"
                      (keydown)="handleToggleKeydown($event, 'status', 'ASC')"
                      *ngIf="sort === 'DESC' && sortColumn === 'status'"
                    />
                    <img
                      src="./assets/images/up-chevron.svg"
                      alt="up-arrow"
                      class="h-10px ms-2"
                      (click)="sortConcreteRequestByField('status', 'DESC')"
                      (keydown)="handleToggleKeydown($event, 'status', 'DESC')"
                      *ngIf="sort === 'ASC' && sortColumn === 'status'"
                    />
                  </span>
                </th>
                <th scope="col" resizable>
                  Concrete Supplier
                  <span>
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortConcreteRequestByField('company', 'ASC')"
                      (keydown)="handleToggleKeydown($event, 'company', 'ASC')"
                      *ngIf="sortColumn !== 'company'"
                    />
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortConcreteRequestByField('company', 'ASC')"
                      (keydown)="handleToggleKeydown($event, 'company', 'ASC')"
                      *ngIf="sort === 'DESC' && sortColumn === 'company'"
                    />
                    <img
                      src="./assets/images/up-chevron.svg"
                      alt="up-arrow"
                      class="h-10px ms-2"
                      (click)="sortConcreteRequestByField('company', 'DESC')"
                      (keydown)="handleToggleKeydown($event, 'company', 'DESC')"
                      *ngIf="sort === 'ASC' && sortColumn === 'company'"
                    />
                  </span>
                </th>
                <th scope="col" resizable>
                  Pump Size
                  <span>
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortConcreteRequestByField('pumpsize', 'ASC')"
                      (keydown)="handleToggleKeydown($event, 'pumpsize', 'ASC')"
                      *ngIf="sortColumn !== 'pumpsize'"
                    />
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortConcreteRequestByField('pumpsize', 'ASC')"
                      (keydown)="handleToggleKeydown($event, 'pumpsize', 'ASC')"
                      *ngIf="sort === 'DESC' && sortColumn === 'pumpsize'"
                    />
                    <img
                      src="./assets/images/up-chevron.svg"
                      alt="up-arrow"
                      class="h-10px ms-2"
                      (click)="sortConcreteRequestByField('pumpsize', 'DESC')"  (keydown)="handleToggleKeydown($event, 'pumpsize', 'DESC')"
                      *ngIf="sort === 'ASC' && sortColumn === 'pumpsize'"
                    />
                  </span>
                </th>
                <th scope="col" resizable>Action</th>
              </thead>
              <tbody>
                <tr
                  *ngFor="
                    let item of concreteRequestList
                      | paginate
                        : {
                            id: 'pagination3',
                            itemsPerPage: concreteRequestPageSize,
                            currentPage: concreteRequestPageNo,
                            totalItems: concreteRequestTotalCount
                          };
                    let j = index
                  "
                >
                  <td>
                    <div class="custom-control custom-checkbox text-black">
                      <input
                        type="checkbox"
                        class="custom-control-input"
                        id="tb2Data2_{{ j }}"
                        name="tblData1"
                        [checked]="item.isChecked"
                        [disabled]="!item.isAllowedToEdit"
                        (change)="setSelectedCurrentDeliveryRequestItem(j)"
                      />
                      <label
                        class="custom-control-label c-pointer fs12"
                        for="tb2Data2_{{ j }}"
                      ></label>
                    </div>
                  </td>
                  <td>
                    <span class="c-pointer" (click)="openIdModal(item)" (keydown)="handleDownKeydown($event, item, '','open')">{{
                      item?.ConcreteRequestId
                    }}</span>
                  </td>
                  <td class="w170 c-pointer" (click)="openIdModal(item)"  (keydown)="handleDownKeydown($event, item, '','open')">
                    <p class="pumpsize-gridoption mb-0">{{ item?.description }}</p>
                  </td>
                  <td class="c-pointer w160" (click)="openIdModal(item)"  (keydown)="handleDownKeydown($event, item, '','open')">
                    {{ item?.concretePlacementStart | date : 'MMM dd, yyyy, hh:mm a' }}
                  </td>
                  <td class="c-pointer fs10 w160 no-wrap" (click)="openIdModal(item)"  (keydown)="handleDownKeydown($event, item, '','open')">
                    <div class="status" [style.background-color]="approvedBackgroundColor" [style.color]="approvedFontColor" *ngIf="item.status == 'Approved'">
                      {{ item.status }}
                    </div>
                    <div class="status" [style.background-color]="rejectedBackgroundColor" [style.color]="rejectedFontColor"  *ngIf="item.status == 'Declined'">
                      {{ item.status }}
                    </div>
                    <div class="status" [style.background-color]="pendingBackgroundColor" [style.color]="pendingFontColor"*ngIf="item.status == 'Tentative'">
                      {{ item.status }}
                    </div>
                    <div class="status" [style.background-color]="expiredBackgroundColor" [style.color]="expiredFontColor" *ngIf="item.status == 'Expired'">
                      {{ item.status }}
                    </div>
                    <div class="status" [style.background-color]="deliveredBackgroundColor" [style.color]="deliveredFontColor" *ngIf="item.status == 'Completed'">
                      {{ item.status }}
                    </div>
                  </td>
                  <td
                    *ngIf="item.concreteSupplierDetails && item.concreteSupplierDetails?.length > 0"
                  >
                    <div
                      class="d-inline-block color-grey15 fs13 mb-2 fw500 c-pointer"
                      *ngFor="let data of item?.concreteSupplierDetails; let isLast = last"
                      (click)="openIdModal(item)"  (keydown)="handleDownKeydown($event, item, '','open')"
                    >
                      {{ data?.Company?.companyName }}{{ isLast ? '' : ',' }}
                    </div>
                  </td>
                  <td
                    *ngIf="
                      !item.concreteSupplierDetails || item.concreteSupplierDetails?.length === 0
                    "
                  >
                    <p class="d-inline-block color-grey15 fs13 mb-2 fw500">-</p>
                  </td>
                  <td
                    *ngIf="item?.pumpSizeDetails && item?.pumpSizeDetails?.length > 0"
                    class="mw-100px"
                  >
                    <div
                      class="d-inline-block color-grey15 fs13 fw500 c-pointer"
                      *ngFor="let data of item?.pumpSizeDetails; let isLast = last"
                      (click)="openIdModal(item)"  (keydown)="handleDownKeydown($event, item, '','open')"
                    >
                      <p class="pumpsize-gridoption mb-0">
                        {{ data?.ConcretePumpSize?.pumpSize }}{{ isLast ? '' : ',' }}
                      </p>
                    </div>
                  </td>
                  <td *ngIf="!item?.pumpSizeDetails || item?.pumpSizeDetails?.length === 0">-</td>
                  <td class="p-0">
                    <ul class="list-inline mb-0" *ngIf="item.isAllowedToEdit">
                      <li
                        class="list-inline-item me-0"
                        tooltip="Edit"
                        placement="top"
                        (click)="openEditModal(item, 'concrete')"  (keydown)="handleDownKeydown($event, item, 'concrete','edit')"
                      >
                        <a href="javascript:void(0)"
                          ><img
                            src="./assets/images/edit.svg"
                            alt="edit"
                            class="h-15px action-icon ms-3"
                          />
                        </a>
                      </li>
                      <li
                        class="list-inline-item ms-1"
                        tooltip="Delete"
                        placement="top"
                        (click)="openDeleteModal(item, deleteList)"  (keydown)="handleDownKeydown($event, item,deleteList,'delete')"
                      >
                        <a href="javascript:void(0)"
                          ><img
                            src="./assets/images/delete.svg"
                            alt="edit"
                            class="h-15px action-icon"
                          />
                        </a>
                      </li>
                    </ul>
                  </td>
                </tr>
              </tbody>
              <tr *ngIf="concreteLoader == true">
                <td colspan="7" class="text-center">
                  <div class="fs18 fw-bold cairo-regular my-5 text-black">Loading...</div>
                </td>
              </tr>
              <tr *ngIf="concreteLoader == false && concreteRequestList?.length == 0">
                <td colspan="7" class="text-center">
                  <div class="fs18 fw-bold cairo-regular my-5 text-black">
                    No Records Found
                  </div>
                </td>
              </tr>
            </table>
          </div>
          <div
            class="tab-pagination px-2"
            id="tab-pagination4"
            *ngIf="concreteLoader == false && concreteRequestTotalCount > 25"
          >
            <div class="row">
              <div class="col-md-2 align-items-center">
                <ul class="list-inline my-3">
                  <li class="list-inline-item">
                    <label class="fs12 color-grey4" for="showEnt">Show entries</label>
                  </li>
                  <li class="list-inline-item notify-pagination">
                    <select  id="showEnt"
                      class="w-auto form-select fs12 color-grey4"
                      (change)="changeConcretePageSize($event.target.value)"
                      ngModel="{{ concreteRequestPageSize }}"
                    >
                      <option value="25">25</option>
                      <option value="50">50</option>
                      <option value="100">100</option>
                      <option value="150">150</option>
                    </select>
                  </li>
                </ul>
              </div>
              <div class="col-md-8 text-center">
                <div class="my-3 position-relative d-inline-block">
                  <pagination-controls
                    id="pagination3"
                    (pageChange)="changeConcreteRequestPageNo($event)"
                    previousLabel=""
                    nextLabel=""
                  >
                  </pagination-controls>
                </div>
              </div>
            </div>
          </div>
        </tab>
      </tabset>
    </div>
  </div>
</section>
<!--Filter Modal-->
<div id="fiter-temp1">
  <ng-template #filter>
    <div class="modal-header border-0 pb-0">
      <h3 class="fs14 fw-bold cairo-regular color-text7 my-0">Filter</h3>
      <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true"
          ><img src="./assets/images/modal-close.svg" alt="Modal Close"
        /></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="filter-content" *ngIf="!modalLoader">
        <form
          class="custom-material-form"
          id="filter-form1"
          [formGroup]="filterForm"
          (ngSubmit)="filterSubmit()"
          novalidate
        >
          <div class="row">
            <div class="col-md-12">
              <div class="input-group mb-3">
                <input
                  type="text"
                  class="form-control fs12 material-input"
                  placeholder="Description"
                  formControlName="descriptionFilter"
                />
                  <span class="input-group-text">
                    <img src="./assets/images/search-icon.svg" alt="Search" />
                  </span>
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="locationFilter">
                  <option value="" disabled selected hidden>Select Location Detail</option>
                  <option *ngFor="let item of locationDetailsDropdown" value="{{ item.location }}">
                    {{ item.location }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="locationPathFilter">
                  <option value="" disabled selected hidden>Select Location</option>
                  <option *ngFor="let item of locationDropdown" value="{{ item.locationPath }}">
                    {{ item.locationPath }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <select
                  class="form-control fs12 material-input"
                  formControlName="concreteSupplierFilter"
                >
                  <option value="" disabled selected hidden>Select Concrete Supplier</option>
                  <option
                    *ngFor="let item of concreteSupplierDropdown"
                    value="{{ item.companyName }}"
                  >
                    {{ item.companyName }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="mixDesignFilter">
                  <option value="" disabled selected hidden>Select Mix Design</option>
                  <option *ngFor="let item of mixDesignDropdown" value="{{ item.mixDesign }}">
                    {{ item.mixDesign }}
                  </option>
                </select>
              </div>
              <div class="input-group mb-3">
                <input
                  type="text"
                  class="form-control fs12 material-input"
                  placeholder="Order Number"
                  formControlName="orderNumberFilter"
                />
                  <span class="input-group-text">
                    <img src="./assets/images/search-icon.svg" alt="Search" />
                  </span>
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="statusFilter">
                  <option value="" disabled selected hidden>Select Status</option>
                  <option *ngFor="let item of wholeStatus" value="{{ item }}">{{ item }}</option>
                </select>
              </div>
              <div class="row justify-content-end">
                <button
                  class="btn btn-orange radius20 col-4 mt-2 fs12 fw-bold cairo-regular mx-1"
                  type="submit"
                >
                  Apply
                </button>
                <button
                  class="btn btn-orange radius20 fs12 col-4 mt-2 fw-bold cairo-regular mx-1"
                  type="button"
                  (click)="resetFilter()"
                >
                  Reset
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="filter-content text-center" *ngIf="modalLoader">Loading...</div>
    </div>
  </ng-template>
</div>
<!--Filter Modal-->

<!--cancel confirmation Popup-->
<div id="confirm-popup8">
  <ng-template #cancelConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure you want to cancel?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
<!--cancel confirmation Popup-->

<ng-template #deleteList>
  <div class="modal-body">
    <div class="text-center my-4">
      <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
        Are you sure you want to delete ?
      </p>
      <button
        class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
        (click)="resetAndClose()"
      >
        No
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
        type="submit"
        (click)="deleteConcreteRequest()"
        [disabled]="deleteConcreteRequestSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="deleteConcreteRequestSubmitted"></em>Yes
      </button>
    </div>
  </div>
</ng-template>
<!-- Edit Multiple Delivery Requests -->
<ng-template #editMultipleDeliveryRequest>
  <div class="modal-header">
    <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">
      <img src="./assets/images/delivery-pop.svg" alt="Delivery" class="me-2" />Edit Concrete
      Booking
    </h1>
    <button
      type="button"
      class="close ms-auto"
      aria-label="Close"
      (click)="closeEditMultiplePopup()"
    >
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body" *ngIf="!editModalLoader">
    <!-- Edit NDR -->
    <form
      name="form"
      class="custom-material-form"
      id="delivery-edit2"
      [formGroup]="concreteEditMultipleForm"
      (ngSubmit)="openConfirmationPopup(editMultipleConfirmationPopup)"
      novalidate
    >
      <div class="row">
        <div class="col-md-6">
          <span class="fs12">The Selected Concrete Id are:</span><br />
          <span class="fs12" *ngIf="selectedConcreteRequestId">{{
            selectedConcreteRequestId
          }}</span>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="form-group company-select" id="company-select3">
            <ng-multiselect-dropdown
              [placeholder]="'Responsible Company'"
              [settings]="newNdrCompanyDropdownSettings"
              [data]="companyList"
              formControlName="companyItems"
              (ngModelChange)="onEditSubmitForm('companies')"
            >
            </ng-multiselect-dropdown>
          </div>
        </div>
        <div class="col-md-6">
          <div class="row">
            <div class="col-md-12 pe-md-0">
              <div class="input-group mb-3">
                <input
                  class="form-control fs10 material-input"
                  #dp="bsDatepicker"
                  bsDatepicker
                  formControlName="deliveryDate"
                  placeholder="Delivery Date "
                  [bsConfig]="{
                    isAnimated: true,
                    showWeekNumbers: false,
                    customTodayClass: 'today'
                  }"
                  (ngModelChange)="onEditSubmitForm('deliveryDate')"
                />
                  <span class="input-group-text">
                    <img
                      src="./assets/images/date.svg"
                      class="h-12px"
                      alt="Date"
                      (click)="dp.toggle()" (keydown)="dp.toggle()"
                      [attr.aria-expanded]="dp.isOpen"
                    />
                  </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label class="fs12 fw600"  for="resperson">Responsible Person</label>
            <tag-input  id="resperson"
              formControlName="person"
              [onlyFromAutocomplete]="true"
              [placeholder]="'Responsible Person '"
              [onTextChangeDebounce]="500"
              [identifyBy]="'id'"
              [displayBy]="'email'"
              class="tag-layout"
              (ngModelChange)="onEditSubmitForm('persons')"
            >
              <tag-input-dropdown
                [showDropdownIfEmpty]="true"
                [displayBy]="'email'"
                [identifyBy]="'id'"
                [autocompleteObservable]="requestAutocompleteItems"
              >
                <ng-template let-item="item" let-index="index">
                  {{ item.email }}
                </ng-template>
              </tag-input-dropdown>
            </tag-input>
          </div>
          <div class="form-group" *ngIf="selectedstatuslength < 2 && statustype[0] !== 'Delivered'">
            <div *ngIf="statustype[0] === 'Approved'">
              <select
                class="form-control fs12 material-input px-2"
                formControlName="status"
                (ngModelChange)="onEditSubmitForm('status')"
              >
                <option value="" disabled selected hidden>Status Change</option>
                <option value="Completed">Completed</option>
              </select>
            </div>
            <div *ngIf="statustype[0] === 'Tentative'">
              <select
                class="form-control fs12 material-input px-2"
                formControlName="status"
                (ngModelChange)="onEditSubmitForm('status')"
              >
                <option value="" disabled selected hidden>Status Change</option>
                <option value="Approved">Approved</option>
              </select>
            </div>
            <div *ngIf="statustype[0] === 'Declined'">
              <select
                class="form-control fs12 material-input px-2"
                formControlName="status"
                (ngModelChange)="onEditSubmitForm('status')"
              >
                <option value="" disabled selected hidden>Status Change</option>
                <option value="Completed">Completed</option>
              </select>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group mb-0 taginput-height location-tag-height mt-2">
            <label class="fs12 fw600 color-grey11" for="location">Location<span class="color-red"> </span></label>
            <tag-input  id="location"
              [onlyFromAutocomplete]="false"
              formControlName="location"
              [placeholder]="' '"
              secondaryPlaceholder=" "
              [onTextChangeDebounce]="500"
              class="tag-layout"
              [identifyBy]="'id'"
              [displayBy]="'location'"
              (ngModelChange)="onEditSubmitForm('location')"
            >
              <tag-input-dropdown
                [showDropdownIfEmpty]="false"
                [displayBy]="'location'"
                [identifyBy]="'id'"
                [autocompleteItems]="locationDropdown"
                [appendToBody]="false"
              >
                <ng-template let-item="item" let-index="index">
                  <div class="tag-input-sample fs12 ms-2">location</div>
                </ng-template>
              </tag-input-dropdown>
            </tag-input>
            <p class="color-grey11 fs12 mb-0">*Type and press enter to create the location tag</p>
          </div>
          <div class="row">
            <div class="col-md-6 pe-md-0">
              <div class="input-group mb-2 delivery-time">
                <label class="fs12 fw600"  for="fromtime">From Time:</label>
                <timepicker id="fromtime"
                  [formControlName]="'deliveryStart'"
                  (keypress)="numberOnly($event)"
                  (ngModelChange)="changeDate($event)"
                >
                </timepicker>
              </div>
            </div>
            <div class="col-md-6 pe-md-0">
              <div class="input-group mb-2 delivery-time">
                <label class="fs12 fw600" for="totime">To Time:</label>
                <timepicker  id="totime"
                  [formControlName]="'deliveryEnd'"
                  (keypress)="numberOnly($event)"
                  (ngModelChange)="deliveryEndTimeChangeDetection()"
                ></timepicker>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-6">
          <div class="row">
            <div class="col-md-6">
              <ul
                class="small-switch list-group list-group-horizontal justify-content-start mnb28 mb-0"
                id="switch-control4"
              >
                <span class="fs12 color-grey11 my-auto">Concrete Confirmed</span>
                <li class="fs12 list-group-item border-0 p-0 color-grey11">
                  <ui-switch
                    switchColor="#fff"
                    defaultBoColor="#CECECE"
                    defaultBgColor="#CECECE"
                    formControlName="isConcreteConfirmed"
                    (change)="changeConcreteConfirmed($event)"
                    (ngModelChange)="onEditSubmitForm('concrete')"
                    class="ms-2 small-switch"
                  >
                  </ui-switch>
                </li>
              </ul>
              <div
                class="fs12 fw700 text-black mt-1"
                *ngIf="concreteEditMultipleForm.get('isConcreteConfirmed').value === true"
              >
                Confirmed on
                {{ concreteEditMultipleForm.get('concreteConfirmedOn').value | date : 'medium' }}
              </div>
              <div
                class="color-red"
                *ngIf="submitted && concreteEditMultipleForm.get('isConcreteConfirmed').errors"
              >
                <small *ngIf="concreteEditMultipleForm.get('isConcreteConfirmed').errors.required"
                  >*Concrete Confirmed is Required.
                </small>
              </div>
            </div>
            <div class="col-md-6">
              <ul
                class="small-switch list-group list-group-horizontal justify-content-start mnb28 mb-0"
                id="switch-control4"
              >
                <span class="fs12 color-grey11 my-auto">Pump Confirmed</span>
                <li class="fs12 list-group-item border-0 p-0 color-grey11">
                  <ui-switch
                    switchColor="#fff"
                    defaultBoColor="#CECECE"
                    defaultBgColor="#CECECE"
                    formControlName="isPumpConfirmed"
                    (change)="changePumpConfirmed($event)"
                    (ngModelChange)="onEditSubmitForm('pump')"
                    class="ms-2"
                  >
                  </ui-switch>
                </li>
              </ul>
              <div *ngIf="concreteEditMultipleForm.get('pumpConfirmedOn').value">
                <p class="fs12 fw700 text-black ng-star-inserted mb-0">
                  Confirmed on
                  {{ concreteEditMultipleForm.get('pumpConfirmedOn').value | date : 'medium' }}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-check">
            <input
              class="form-check-input c-pointer"
              name="subscribe"
              type="checkbox"
              formControlName="void"
              id="subscribe"
              (change)="handleInputChange($event.target.checked)"
              (ngModelChange)="onEditSubmitForm('void')"
            />
            <label class="form-check-label fs12 fw600 color-grey15" for="inlineCheckbox1">
              Mark as Void
            </label>
          </div>
        </div>
      </div>
      <div class="note--styles">
        <strong>Note:</strong>
        <span class="note-insidetext"
          >All changes done in the following fields:Responsible Company,Responsible Person will be
          updated to all selected bookings.</span
        >
      </div>
      <div class="row">
        <div class="col-md-6"></div>
      </div>
      <div class="mt-4 mb30 text-center">
        <button
          class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3 px-2rem"
          type="button"
          (click)="closeEditMultiplePopup()"
        >
          Cancel
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem"
        >
          <em class="fa fa-spinner" aria-hidden="true" *ngIf="editMultipleSubmitted"></em>Submit
        </button>
      </div>
    </form>
    <!-- Edit NDR -->
  </div>
  <div class="modal-body text-center" *ngIf="editModalLoader">Loading...</div>
</ng-template>
<!-- Edit Multiple Delivery Bookings -->

<!--Edit Multiple Delivery Booking Confirmation Popup-->
<div id="confirm-popup24">
  <ng-template #editMultipleConfirmationPopup>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          The changes you made to your fields will be applied to the selected Concrete Booking(s).Do
          you really want to make these changes?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="editMultipleConfirmation('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="editMultipleConfirmation('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
<!--Edit Multiple Delivery Booking Confirmation Popup-->
