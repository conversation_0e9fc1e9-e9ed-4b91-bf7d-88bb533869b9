import { Component, OnDestroy, OnInit } from '@angular/core';
import {
  filter, merge, Subscription, tap,
} from 'rxjs';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';

@Component({
  selector: 'app-crane-request-detail-view-content',
  templateUrl: './crane-request-detail-view-content.component.html',
})
export class CraneRequestDetailViewContentComponent implements OnInit, OnDestroy {
  public NDRData: any = [];

  public CraneRequestId: any;

  public loader = true;

  public ParentCompanyId: number;

  public ProjectId: number;

  private readonly subscriptions: Subscription = new Subscription();

  public constructor(
    private readonly deliveryService: DeliveryService,
    public projectService: ProjectService,
  ) {
    this.projectService.projectParent.subscribe((response3): void => {
      if (response3 !== undefined && response3 !== null && response3 !== '') {
        this.ParentCompanyId = response3.ParentCompanyId;
        this.ProjectId = response3.ProjectId;
      }
    });
  }

  public ngOnInit(): void {
    this.deliveryService.EditCraneRequestId.subscribe((getDeliveryRequestIdResponse3): void => {
      this.CraneRequestId = getDeliveryRequestIdResponse3;
    });

    const mergedFetch = merge(
      this.deliveryService.fetchData,
      this.deliveryService.fetchData1,
    );

    this.subscriptions.add(
      mergedFetch
        .pipe(
          filter(() => !!this.CraneRequestId),
          tap(() => {
            this.loader = true;
          }),
        )
        .subscribe(() => {
          this.getNDR();
        }),
    );
  }

  public getResponsiblePeople(object): any {
    if (object?.firstName && object?.lastName) {
      const string = `${object.firstName} ${object.lastName}`;
      const matches = string.match(/\b(\w)/g);
      const acronym = matches.join('').toUpperCase();
      return acronym;
    }
    return 'UU';
  }

  public getNDR(): void {
    const param = {
      CraneRequestId: this.CraneRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    if (this.ProjectId && this.ParentCompanyId) {
      this.deliveryService.getEquipmentCraneRequest(param).subscribe((res): void => {
        this.NDRData = res.data;
        this.loader = false;
      });
    }
  }


  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
}
