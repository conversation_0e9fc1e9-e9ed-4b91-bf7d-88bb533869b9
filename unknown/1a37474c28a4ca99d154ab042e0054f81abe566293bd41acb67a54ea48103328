<section class="page-section">
  <div class="page-inner-content dfow-content">
    <div class="top-header my-3">
      <div class="row pt-md-40px">
        <div class="col-md-8 col-lg-8">
          <div>
            <ul class="list-group list-group-horizontal upload-btn">
              <li class="list-group-item p0 border-0 bg-transparent me-0 me-md-4">
                <button
                  class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular px-3"
                  (click)="openModal(import)"
                >
                  Import
                </button>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent me-0 me-md-4">
                <button
                  class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular px-3"
                  (click)="exportDFOW()"
                  *ngIf="defineList.length > 0"
                >
                  Export
                </button>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent me-0 me-md-4">
                <button
                  class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular px-3 dflowheader-btn"
                  (click)="addRow()"
                >
                  Add Row
                </button>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent me-md-4">
                <button
                  class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular px-3 dflowheader-btn"
                  (click)="openDeleteModal(-1, deleteList)"
                  [disabled]="checkSelectedRow()"
                >
                  Remove
                </button>
              </li>
            </ul>
          </div>
        </div>
        <div class="col-md-4 col-lg-4">
          <div class="top-filter">
            <ul class="list-group list-group-horizontal filter-list">
              <li
                class="list-group-item p0 border-0 bg-transparent dfow-filter-button"
                *ngIf="addRecord || editRecord"
              >
                <button
                  class="btn btn-grey radius20 fs12 mb-3 me-3 fw-bold cairo-regular px-4 px-md-5"
                  type="button"
                  (click)="resetChanges()"
                >
                  Cancel
                </button>
                <button
                  class="btn btn-orange radius20 fs12 fw-bold cairo-regular mb-3 px-4 px-md-5"
                  type="submit"
                  (click)="saveChanges()"
                >
                  <em class="fa fa-spinner" aria-hidden="true" *ngIf="saveSubmitted"></em>
                  Save
                </button>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent">
                <div class="search-icon">
                  <input
                    class="form-control fs12 color-grey8"
                    [ngClass]="showSearchbar ? 'input-hover-disable' : 'input-search'"
                    placeholder="What are you looking for?"
                    (input)="searchDFOW($event.target.value)"
                    [(ngModel)]="search"
                  />
                  <div class="icon">
                    <img
                      src="./assets/images/cross-close.svg"
                      *ngIf="showSearchbar"
                      (click)="clear()" (keydown)="handleDownKeydown($event, '','','clear')"
                      alt="close-cross"
                    />
                    <em class="fa fa-search fs12 color-grey8" *ngIf="!showSearchbar"></em>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="page-card bg-white rounded">
      <div class="table-responsive rounded tab-grid">
        <table class="table table-custom mb-0" aria-describedby="Memtable">
          <thead>
            <th scope="col" *ngIf="defineList.length > 0">
              <div class="custom-checkbox form-check text-center">
                <input
                  class="form-check-input float-none ms-0"
                  type="checkbox"
                  [checked]="selectAll"
                  (change)="selectAllDFOWData()"
                  id="tblData"
                  name="tblData"
                />
                <label class="form-check-label c-pointer fs12" for="tblData"> </label>
              </div>
            </th>
            <th scope="col" class="text-left" resizable>
              ID
              <span>
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('id', 'ASC')" (keydown)="handleToggleKeydown($event, 'id', 'ASC')"
                  *ngIf="sortColumn !== 'id'"
                />
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('id', 'ASC')"  (keydown)="handleToggleKeydown($event, 'id', 'ASC')"
                  *ngIf="sort === 'DESC' && sortColumn === 'id'"
                />
                <img
                  src="./assets/images/up-chevron.svg"
                  alt="up-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('id', 'DESC')"  (keydown)="handleToggleKeydown($event, 'id', 'DESC')"
                  *ngIf="sort === 'ASC' && sortColumn === 'id'"
                />
              </span>
            </th>
            <th scope="col" class="text-left" resizable>
              Specification Section
              <span>
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('Specification', 'ASC')"  (keydown)="handleToggleKeydown($event, 'Specification', 'ASC')"
                  *ngIf="sortColumn !== 'Specification'"
                />
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('Specification', 'ASC')" (keydown)="handleToggleKeydown($event, 'Specification', 'ASC')"
                  *ngIf="sort === 'DESC' && sortColumn === 'Specification'"
                />
                <img
                  src="./assets/images/up-chevron.svg"
                  alt="up-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('Specification', 'DESC')" (keydown)="handleToggleKeydown($event, 'Specification', 'DESC')"
                  *ngIf="sort === 'ASC' && sortColumn === 'Specification'"
                />
              </span>
            </th>
            <th scope="col" class="text-left" resizable>
              Definable Feature Of Work
              <span>
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('DFOW', 'ASC')" (keydown)="handleToggleKeydown($event, 'DFOW', 'ASC')"
                  *ngIf="sortColumn !== 'DFOW'"
                />
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('DFOW', 'ASC')" (keydown)="handleToggleKeydown($event, 'DFOW', 'ASC')"
                  *ngIf="sort === 'DESC' && sortColumn === 'DFOW'"
                />
                <img
                  src="./assets/images/up-chevron.svg"
                  alt="up-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('DFOW', 'DESC')" (keydown)="handleToggleKeydown($event, 'DFOW', 'DESC')"
                  *ngIf="sort === 'ASC' && sortColumn === 'DFOW'"
                />
              </span>
            </th>
            <th scope="col" class="text-left" resizable>Action</th>
          </thead>
          <tbody *ngIf="loader == false && defineList.length > 0">
            <tr
              *ngFor="
                let item of defineList
                  | paginate
                    : { itemsPerPage: pageSize, currentPage: pageNo, totalItems: totalCount };
                let i = index
              "
            >
              <td class="text-left custom-checkbox">
                <div class="form-check text-center ps-0">
                  <input
                    class="form-check-input float-none ms-0"
                    type="checkbox"
                    [checked]="item.isChecked"
                    (change)="setSelectedItem(i)"
                    id="tblData1_{{ i }}"
                    name="tblData1"
                  />
                  <label class="form-check-label c-pointer fs12" for="tblData1_{{ i }}"> </label>
                </div>
              </td>
              <td
                class="text-left"
                [ngStyle]="{ 'background-color': getDuplicate(item.id) ? 'red' : '' }"
              >
                {{ item.autoId }}
              </td>
              <td class="text-left">
                <p class="mb-0">
                  <input
                    type="text"
                    *ngIf="item.Specification"
                    class="p-0 fs12 dfow-input w-100 text-left"
                    value="{{ item.Specification }}"
                    (change)="enterSpecification(i, $event.target.value)"
                  />
                  <input
                    type="text"
                    *ngIf="!item.Specification"
                    class="p-0 fs12 dfow-input w-100 text-left"
                    value="-"
                    (change)="enterSpecification(i, $event.target.value)"
                  />
                </p>
              </td>
              <td class="text-left">
                <input
                  type="text"
                  class="p-0 fs12 dfow-input w-100 text-left"
                  value="{{ item.DFOW }}"
                  (input)="enterContent(i, $event.target.value)"
                />
              </td>
              <td class="text-left">
                <ul class="list-inline mb-0 text-left">
                  <li
                    class="list-inline-item mx-2 c-pointer"
                    tooltip="Delete"
                    placement="top"
                    (click)="openDeleteModal(i, deleteList)"  (keydown)="handleDownKeydown($event, i, deleteList,'delete')"
                  >
                    <img src="./assets/images/delete.svg" alt="delete" class="h-15px" />
                  </li>
                </ul>
              </td>
            </tr>
          </tbody>
          <tr *ngIf="loader == true">
            <td colspan="7" class="text-center">
              <div class="fs18 fw-bold cairo-regular my-5 text-black text-center">Loading...</div>
            </td>
          </tr>
          <tr *ngIf="loader == false && defineList.length == 0">
            <td colspan="7" class="text-center">
              <div class="fs18 fw-bold cairo-regular my-5 text-black">No Records Found</div>
            </td>
          </tr>
        </table>
      </div>
      <div
        class="tab-pagination px-2"
        id="tab-pagination4"
        *ngIf="loader == false && totalCount > 25"
      >
        <div class="row">
          <div class="col-md-2 align-items-center">
            <ul class="list-inline my-3">
              <li class="list-inline-item notify-pagination">
                <label class="fs12 color-grey4" for="shwEnt">Show entries</label>
              </li>
              <li class="list-inline-item">
                <select id="shwEnt"
                  class="w-auto form-select fs12 color-grey4"
                  (change)="changePageSize($event.target.value)"
                  [ngModel]="pageSize"
                >
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                  <option value="150">150</option>
                </select>
              </li>
            </ul>
          </div>
          <div class="col-md-8 text-center">
            <div class="my-3 position-relative d-inline-block">
              <pagination-controls
                (pageChange)="changePageNo($event)"
                previousLabel=""
                nextLabel=""
              >
              </pagination-controls>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<ng-template #import>
  <div class="mx-2 mx-md-5 my-5 bulkupload">
    <button
      type="button"
      class="close upload-close ms-auto bg-transparent border-0"
      aria-label="Close"
      (click)="close(cancelConfirmation)"
    >
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
    <h1 class="fw-bold cairo-regular fs20 text-center pb-4">
      Import Definable Feature Of Work List
    </h1>
    <ngx-file-drop
      dropZoneLabel="Drop files here"
      (onFileDrop)="dfowImportFiledropped($event)"
      multiple="true"
    >
      <ng-template ngx-file-drop-content-tmp let-openFileSelector="openFileSelector">
        <div class="bulkupload-content text-center" (click)="openFileSelector()" (keydown)="openFileSelector()">
          <img src="./assets/images/file.svg" alt="Excel" />
          <p class="fs14 fw600 mb3 pt10 color-grey7">Drag & Drop your file here</p>
          <p class="fs12 color-grey8 fw500 mb3">Or</p>
          <label class="color-blue4 fs14 mb10 fw500 text-underline" for="browse">Click here to browse</label>
        </div>
      </ng-template>
    </ngx-file-drop>
    <p class="fs10 color-grey8 fw500 my-2 text-end">*Supported formats are .xlsx, .xls and .csv</p>
    <div class="row upload-table py-3 m-0" *ngFor="let item of dfowFiles; let i = index">
      <div class="col-1 col-md-1 ps-0">
        <img src="./assets/images/xlsx.png" alt="attached-file" class="rounded upload-img" />
      </div>
      <div class="col-9 col-md-9">
        <h1 class="fs16 ms-4 color-grey7">{{ item.relativePath }}</h1>
        <p class="ms-4 mb-0 color-grey8">
          {{ todayDate | date : 'short' }}<span class="ms-3"></span>
        </p>
      </div>
      <div class="col-1 col-md-2 text-end d-flex justify-content-end pe-0" (click)="removeFile(i)" (keydown)="handleDownKeydown($event, i, '','remove')">
        <img src="./assets/images/delete.svg" alt="delete" class="h-15px c-pointer" />
      </div>
    </div>
    <div class="text-center">
      <button
        class="btn btn-orange radius20 fs12 fw-bold cairo-regular my-4 px-5"
        type="submit"
        (click)="importData()"
        *ngIf="dfowFiles.length > 0"
        [disabled]="importSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="importSubmitted"></em>
        Done
      </button>
      <p class="color-orange c-pointer fs12" (click)="download()" (keydown)="handleDownKeydown($event, '', '','download')">
        <u>Download sample template here</u>
      </p>
    </div>
  </div>
</ng-template>

<!--Remove Modal-->
<ng-template #deleteList>
  <div class="modal-body">
    <div class="text-center my-4" *ngIf="!remove">
      <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
        Are you sure you want to delete '{{ defineList[currentDeleteId]?.DFOW | titlecase }}'?
      </p>
      <button
        class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
        (click)="resetAndClose()"
      >
        No
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
        type="submit"
        (click)="deleteDefinable()"
        [disabled]="deleteSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="deleteSubmitted"></em>yes
      </button>
    </div>
    <div class="text-center my-4" id="remove-popup2" *ngIf="remove">
      <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">Are you sure you want to delete?</p>
      <button
        class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
        (click)="resetAndClose()"
      >
        No
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
        type="submit"
        (click)="removeItem()"
        [disabled]="deleteSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="deleteSubmitted"></em> yes
      </button>
    </div>
  </div>
</ng-template>

<!--Confirmation Popup-->
<div id="confirm-popup8">
  <ng-template #cancelConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">Are you sure you want to cancel?</p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetForm('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="resetForm('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
