import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';


@Component({
  selector: 'app-filter-inspection-form',
  templateUrl: './filter-inspection-form.component.html',
})
export class FilterinspectionFormComponent implements OnInit {
  public filterForm: FormGroup;

  public ProjectId: any;

  public gateList: any = [];

  public equipmentList: any = [];

  public defineList: any = [];

  public companyList: any = [];

  public memberList: any = [];

  public wholeStatus = ['Approved', 'Declined', 'Delivered', 'Pending'];

  public ParentCompanyId: any;

  public noEquipmentOption = { id: 0, equipmentName: 'No Equipment Needed' };

  public constructor(private readonly formBuilder: FormBuilder,
    public modalRef: BsModalRef,
    private readonly DeliveryService: DeliveryService, public projectService: ProjectService) {
    this.projectService.projectId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ProjectId = res;
        this.getOverAllGate();
        this.getMembers();
      }
    });
    this.projectService.projectParent.subscribe((response4): void => {
      this.updateProjectAndParentCompany(response4);
    });
    this.projectService.accountProjectParent.subscribe((res): void => {
      this.updateProjectAndParentCompany(res);
    });

    this.filterDetailsForm();
  }

  public updateProjectAndParentCompany(res: any): void{
    if (res !== undefined && res !== null && res !== '') {
      this.ProjectId = res.ProjectId;
      this.ParentCompanyId = res.ParentCompanyId;
    }
  }

  public getOverAllGate(): void {
    const params = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
    };
    this.projectService.gateList(params, { isFilter: true, showActivatedAlone: true }).subscribe((res): void => {
      this.gateList = res.data;
      this.getOverAllEquipment();
    });
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }



  public getOverAllEquipment(): void {
    const params = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listEquipment(params, { isFilter: true, showActivatedAlone: true, }).subscribe((res): void => {
      this.equipmentList =  [this.noEquipmentOption, ...res.data];
      this.getCompany();
    });
  }

  public getCompany(): void {
    const params = {
      ProjectId: this.ProjectId,
    };
    this.projectService.getCompanies(params).subscribe((response: any): void => {
      if (response) {
        this.companyList = response.data;
      }
    });
  }


  public ngOnInit(): void { /* */
  }

  public resetFilter(): void {
    this.filterForm.reset();
    this.filterDetailsForm();
    this.modalRef.hide();
  }

  public filterSubmit(): void {
    this.modalRef.hide();
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group(
      {

        companyFilter: [
          '',
        ],
        descriptionFilter: [
          '',
        ],
        statusFilter: [
          '',
        ],
        memberFilter: [
          '',
        ],
        gateFilter: [
          '',
        ],
        equipmentFilter: [
          '',
        ],

      },
    );
  }
}
