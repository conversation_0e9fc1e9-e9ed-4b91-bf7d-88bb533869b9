import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Form<PERSON>uilder, ReactiveFormsModule } from '@angular/forms';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { BehaviorSubject, of } from 'rxjs';
import { FilterinspectionFormComponent } from './filter-inspection-form.component';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';

describe('FilterinspectionFormComponent', () => {
  let component: FilterinspectionFormComponent;
  let fixture: ComponentFixture<FilterinspectionFormComponent>;
  let projectServiceMock: jest.Mocked<ProjectService>;
  let deliveryServiceMock: jest.Mocked<DeliveryService>;
  let modalRefMock: jest.Mocked<BsModalRef>;

  beforeEach(async () => {
    projectServiceMock = {
      gateList: jest.fn(),
      listAllMember: jest.fn(),
      listEquipment: jest.fn(),
      getCompanies: jest.fn(),
      projectId: new BehaviorSubject('test-project-id'),
      projectParent: new BehaviorSubject({ ProjectId: 'test-project-id', ParentCompanyId: 'test-parent-id' }),
      accountProjectParent: new BehaviorSubject({ ProjectId: 'test-project-id', ParentCompanyId: 'test-parent-id' })
    } as any;

    deliveryServiceMock = {} as any;
    modalRefMock = { hide: jest.fn() } as any;

    await TestBed.configureTestingModule({
      declarations: [FilterinspectionFormComponent],
      imports: [ReactiveFormsModule],
      providers: [
        FormBuilder,
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: DeliveryService, useValue: deliveryServiceMock },
        { provide: BsModalRef, useValue: modalRefMock }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    // Setup mock responses before component creation
    projectServiceMock.gateList.mockReturnValue(of({ data: [{ id: 1, name: 'Gate 1' }] }));
    projectServiceMock.listAllMember.mockReturnValue(of({ data: [{ id: 1, name: 'Member 1' }] }));
    projectServiceMock.listEquipment.mockReturnValue(of({ data: [{ id: 1, name: 'Equipment 1' }] }));
    projectServiceMock.getCompanies.mockReturnValue(of({ data: [{ id: 1, name: 'Company 1' }] }));

    fixture = TestBed.createComponent(FilterinspectionFormComponent);
    component = fixture.componentInstance;

    // Manually call the methods that would be triggered by subscriptions
    component.ProjectId = 'test-project-id';
    component.ParentCompanyId = 'test-parent-id';

    // Spy on the methods to ensure they're called
    jest.spyOn(component, 'getOverAllGate').mockImplementation(() => {
      component.gateList = [{ id: 1, name: 'Gate 1' }];
      component.getOverAllEquipment();
    });

    jest.spyOn(component, 'getOverAllEquipment').mockImplementation(() => {
      component.equipmentList = [{ id: 1, name: 'Equipment 1' }];
      component.getCompany();
    });

    jest.spyOn(component, 'getCompany').mockImplementation(() => {
      component.companyList = [{ id: 1, name: 'Company 1' }];
    });

    jest.spyOn(component, 'getMembers').mockImplementation(() => {
      component.memberList = [{ id: 1, name: 'Member 1' }];
    });

    // Call filterDetailsForm to initialize the form
    component.filterDetailsForm();

    // Manually trigger the methods that would be called in the constructor
    component.getOverAllGate();
    component.getMembers();

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with empty form controls', () => {
    expect(component.filterForm.get('companyFilter')).toBeTruthy();
    expect(component.filterForm.get('descriptionFilter')).toBeTruthy();
    expect(component.filterForm.get('statusFilter')).toBeTruthy();
    expect(component.filterForm.get('memberFilter')).toBeTruthy();
    expect(component.filterForm.get('gateFilter')).toBeTruthy();
    expect(component.filterForm.get('equipmentFilter')).toBeTruthy();
  });

  it('should update ProjectId and ParentCompanyId when projectParent is received', () => {
    expect(component.ProjectId).toBe('test-project-id');
    expect(component.ParentCompanyId).toBe('test-parent-id');
  });

  it('should reset form and hide modal when resetFilter is called', () => {
    component.filterForm.patchValue({
      companyFilter: 'test',
      descriptionFilter: 'test'
    });

    component.resetFilter();

    expect(component.filterForm.get('companyFilter')?.value).toBe('');
    expect(component.filterForm.get('descriptionFilter')?.value).toBe('');
    expect(modalRefMock.hide).toHaveBeenCalled();
  });

  it('should hide modal when filterSubmit is called', () => {
    component.filterSubmit();
    expect(modalRefMock.hide).toHaveBeenCalled();
  });

  it('should have correct status options', () => {
    expect(component.wholeStatus).toEqual(['Approved', 'Declined', 'Delivered', 'Pending']);
  });

  it('should populate gateList after getOverAllGate is called', () => {
    expect(component.gateList).toEqual([{ id: 1, name: 'Gate 1' }]);
  });

  it('should populate memberList after getMembers is called', () => {
    expect(component.memberList).toEqual([{ id: 1, name: 'Member 1' }]);
  });

  it('should populate equipmentList after getOverAllEquipment is called', () => {
    expect(component.equipmentList).toEqual([{ id: 1, name: 'Equipment 1' }]);
  });

  it('should populate companyList after getCompany is called', () => {
    expect(component.companyList).toEqual([{ id: 1, name: 'Company 1' }]);
  });
});
