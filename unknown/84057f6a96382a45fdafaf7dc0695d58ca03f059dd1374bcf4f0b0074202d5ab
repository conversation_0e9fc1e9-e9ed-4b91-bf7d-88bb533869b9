<div class="home-content bg-white">
  <div class="container-fluid">
    <div class="inner-content">
      <div class="row align-items-center">
        <div class="col-lg-6 col-md-6">
          <div class="content-left py-5 text-center text-md-start">
            <span class="content-heading">
              <span class="content-heading-sub">Reimagine</span> the jobsite delivery boards
            </span>
            <p class="fs15 text-black fw600 col-md-10 p0">
              Leverage the power of the web and mobile applications to change the way you manage job
              site deliveries!
            </p>
            <button
              id="myCheck"
              (click)="getStarted(threeSimpleSteps)"
              class="btn btn-orange-outline btn-hover-disabled fs14 color-orange px-2 my-3 radius20 cursor-pointer"
            >
              Get Started
              <span class="get-arrow">
                <img
                  src="./assets/images/get-arrow.svg"
                  class="ms-2"
                  width="100%"
                  height="100%"
                  alt="Get Arrow"
                />
              </span>
            </button>
          </div>
        </div>
        <div class="col-lg-6 col-md-6 p-0">
          <img
            src="/assets/images/construction.svg"
            width="100%"
            height="100%"
            alt="construction"
          />
        </div>
      </div>
    </div>
  </div>
</div>

<ng-template #threeSimpleSteps>
  <div class="modal-header px-3 py-1 border-0">
    <button type="button" class="close ms-auto" aria-label="Close" (click)="modalClose()">
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body pt-0">
    <div class="row custom-material-form">
      <div *ngIf="stepOne" class="col-md-12">
        <div class="mb-4 text-center">
          <h1 class="auth-title color-grey7 fs20 fw-bold cairo-regular my-2">
            Welcome to Follo
          </h1>
          <p class="color-grey4 fs14 fw500">Get started in three simple steps</p>
        </div>
        <form
          name="form"
          [formGroup]="basicDetailsForm"
          (ngSubmit)="onSubmit('basic', userResponseToRegister)"
          novalidate
          autocomplete="off"

          [ngClass]="{
            'basic-detail-form-error': ParentCompanyId,
            'basic-detail-form': !ParentCompanyId
          }"

        >
          <div class="form-group mb-4">
            <label class="color-orange fs13 mb-2" for="email">Enter Your Work Email</label>
            <input  id="email"
              type="email"
              class="form-control material-input fs13"
              placeholder="Email Address"
              formControlName="email"
              autocomplete="email"
            />

            <div class="color-red" *ngIf="basicSubmitted && basicDetailsForm.get('email').errors">
              <small *ngIf="basicDetailsForm.get('email').errors.required"
                >*Email address is Required</small
              >
              <small *ngIf="basicDetailsForm.get('email').errors.pattern"
                >Enter valid email address</small
              >
            </div>
          </div>

          <div class="form-group welcome_modal mb-4">
            <label class="color-orange fs13 mb-2"  for="mobile">Enter Your Mobile Number</label>
            <div class="form-group mb-3 timezone-formgroup homepage-formgroup d-flex">
              <ng-multiselect-dropdown  id="mobile"
                [placeholder]="'+1'"
                [settings]="dropdownSettings"
                [data]="countryCode"
                class="country-code"
              >
              </ng-multiselect-dropdown>
              <div
                class="color-red"
                *ngIf="basicSubmitted && basicDetailsForm.get('phoneCode').errors"
              >
                <small *ngIf="basicDetailsForm.get('phoneCode').errors.required"
                  >Enter Phone code</small
                >
              </div>
              <input
                type="text"
                class="form-control material-input phonenumber-input fs13"
                name="mobile"
                formControlName="phoneNumber"
                placeholder="Mobile Number"
                (keypress)="numberOnly($event)"
                mask="{{ phoneMask }}"
                autocomplete="on"
              />
            </div>

            <div
              class="color-red"
              *ngIf="basicSubmitted && basicDetailsForm.get('phoneNumber').errors"
            >
              <small *ngIf="basicDetailsForm.get('phoneNumber').errors.required"
                >* Phone Number Required</small
              >
              <small
                *ngIf="
                  !basicDetailsForm.get('phoneNumber').errors.required &&
                  basicDetailsForm.get('phoneNumber').errors
                "
                >*Enter Valid Phone Number</small
              >
            </div>
          </div>

          <div class="form-group welcome_modal mb-4" *ngIf="ParentCompanyId">
            <label class="color-orange fs13"  for="password">Enter Your Password</label>
            <div class="input-group">
                <span class="input-group-text py-1">
                  <img
                    src="./assets/images/noun_password_1439121.svg"
                    class="form-icon me-2"
                    width="14"
                    alt="password"
                  />
                </span>
              <input id="password"
                type="{{ togglePassword ? 'text' : 'password' }}"
                class="form-control material-input fs12"
                placeholder="Password"
                formControlName="password"
                autocomplete="on"
                type="search"
                (keyup)="passwordValid($event)"
              />
                <span class="input-group-text" (click)="passwordToggle()" (keydown)="handleDownKeydown($event, 'password')"
                  ><em
                    class="fas color-grey4 fs12 eye-cursor"
                    [ngClass]="{ 'fa-eye-slash': !togglePassword, 'fa-eye': togglePassword }"
                  ></em
                ></span>
            </div>
            <div
              class="color-red"
              *ngIf="basicSubmitted && basicDetailsForm.get('password')?.errors"
            >
              <small *ngIf="basicDetailsForm.get('password')?.errors.required"
                >*Password is required</small
              >
            </div>
            <div *ngIf="basicDetailsForm.get('password')?.value?.length > 0">
              <div>
              <label for="pass1"
                class="col ps-0"
                [ngClass]="
                  basicDetailsForm.controls['password'].hasError('required') ||
                  basicDetailsForm.controls['password'].hasError('hasNumber')
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                <p class="d-flex mb-0">
                  <span
                    ><img id="pass1"
                      alt="tick"
                      [src]="
                        basicDetailsForm.controls['password'].hasError('required') ||
                        basicDetailsForm.controls['password'].hasError('hasNumber')
                          ? './assets/images/red-tick.svg'
                          : './assets/images/green-tick.svg'
                      "
                  /></span>
                  <small class="d-block my-1 ms-3 responsive-font-validation"
                    >At least 1 number</small
                  >
                </p>
              </label>
            </div>
            <div>
              <label  for="pass2"
                class="col ps-0"
                [ngClass]="
                  basicDetailsForm.controls['password'].hasError('required') ||
                  basicDetailsForm.controls['password'].hasError('minlength')
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                <p class="d-flex mb-0">
                  <span
                    ><img id="pass2"
                      alt="tick"
                      [src]="
                        basicDetailsForm.controls['password'].hasError('required') ||
                        basicDetailsForm.controls['password'].hasError('minlength')
                          ? './assets/images/red-tick.svg'
                          : './assets/images/green-tick.svg'
                      "
                  /></span>
                  <small class="d-block my-1 ms-3 responsive-font-validation"
                    >Password must contain at least 8 characters</small
                  >
                </p>
              </label>
            </div>
            <div>
              <label for="pass3"
                class="col ps-0"
                [ngClass]="
                  basicDetailsForm.controls['password'].hasError('required') ||
                  basicDetailsForm.controls['password'].hasError('hasCapitalCase')
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                <p class="d-flex mb-0">
                  <span
                    ><img id="pass3"
                      alt="tick"
                      [src]="
                        basicDetailsForm.controls['password'].hasError('required') ||
                        basicDetailsForm.controls['password'].hasError('hasCapitalCase')
                          ? './assets/images/red-tick.svg'
                          : './assets/images/green-tick.svg'
                      "
                  /></span>
                  <small class="d-block my-1 ms-3 responsive-font-validation"
                    >At least 1 uppercase letter</small
                  >
                </p>
              </label>
            </div>
            <div>
              <label for="pass4"
                class="col ps-0"
                [ngClass]="
                  basicDetailsForm.controls['password'].hasError('required') ||
                  basicDetailsForm.controls['password'].hasError('hasSmallCase')
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                <p class="d-flex mb-0">
                  <span
                    ><img id="pass4"
                      alt="tick"
                      [src]="
                        basicDetailsForm.controls['password'].hasError('required') ||
                        basicDetailsForm.controls['password'].hasError('hasSmallCase')
                          ? './assets/images/red-tick.svg'
                          : './assets/images/green-tick.svg'
                      "
                  /></span>
                  <small class="d-block my-1 ms-3 responsive-font-validation"
                    >At least 1 lowercase letter</small
                  >
                </p>
              </label>
            </div>
            <div>
              <label for="pass5"
                class="col ps-0"
                [ngClass]="
                  basicDetailsForm.controls['password'].hasError('required') ||
                  basicDetailsForm.controls['password'].hasError('hasSpecialCharacters')
                    ? 'text-danger'
                    : 'text-success'
                "
              >
                <p class="d-flex mb-0">
                  <span
                    ><img id="pass5"
                      alt="tick"
                      [src]="
                        basicDetailsForm.controls['password'].hasError('required') ||
                        basicDetailsForm.controls['password'].hasError('hasSpecialCharacters')
                          ? './assets/images/red-tick.svg'
                          : './assets/images/green-tick.svg'
                      "
                  /></span>
                  <small class="d-block my-1 ms-3 responsive-font-validation"
                    >At least 1 special character(!#_$%*)</small
                  >
                </p>
              </label>
            </div>
            </div>
          </div>
          <div class="form-group welcome_modal mb-4" *ngIf="ParentCompanyId">
            <label class="color-orange fs13" for="confirm">Confirm Password</label>
            <div class="input-group">
                <span class="input-group-text py-1">
                  <img
                    src="./assets/images/noun_password_1439121.svg"
                    class="form-icon me-2"
                    width="14"
                    alt="password"
                  />
                </span>
              <input  id="confirm"
                type="{{ confirmTogglePassword ? 'text' : 'password' }}"
                class="form-control material-input fs12"
                placeholder="Confirm Password"
                formControlName="confirmPassword"
                autocomplete="off"
              />
                <span class="input-group-text" (click)="confirmPasswordToggle()" (keydown)="handleDownKeydown($event, 'cpassword')"
                  ><em
                    class="fas color-grey4 fs12 eye-cursor"
                    [ngClass]="{
                      'fa-eye-slash': !confirmTogglePassword,
                      'fa-eye': confirmTogglePassword
                    }"
                  ></em
                ></span>
            </div>
            <div
              class="color-red"
              *ngIf="basicSubmitted && basicDetailsForm.get('confirmPassword').errors"
            >
              <small *ngIf="basicDetailsForm.get('confirmPassword').errors.required"
                >*Confirm Password is required.</small
              >
              <small *ngIf="basicDetailsForm.get('confirmPassword').errors.mustMatch">
                *Passwords must match</small
              >
            </div>
          </div>

          <button
            class="btn btn-orange color-orange radius20 fs13 fw-bold cairo-regular w-100"
            type="submit"
          >
            <em
              class="fa fa-spinner"
              aria-hidden="true"
              *ngIf="formSubmitted && basicDetailsForm.valid"
            ></em>
            Continue
          </button>
        </form>
      </div>

      <div *ngIf="StepTwo" class="col-md-12">
        <div class="mb-4 text-center">
          <h1 class="auth-title color-grey7 fs20 fw-bold cairo-regular my-2">
            Complete your basic details
          </h1>
        </div>
        <form
          name="form"
          [formGroup]="companyDetailsForm"
          novalidate
          (ngSubmit)="onSubmit('company', userResponseToRegister)"
          autocomplete="off"
          class="homepage-complete-details"
        >
          <div class="row">
            <div class="col-md-12">
              <div class="form-group mb-4">
                <label  for="fname" class="color-orange fs13">Enter Your First Name</label>
                <input  id="fname"
                  type="text"
                  class="form-control material-input fs13"
                  placeholder="First Name"
                  formControlName="fullName"
                  (keypress)="alphaOnly($event)"
                  autocomplete="off"
                />
                <div
                  class="color-red"
                  *ngIf="companySubmitted && companyDetailsForm.get('fullName').errors"
                >
                  <small *ngIf="companyDetailsForm.get('fullName').errors.required"
                    >*First Name is required.</small
                  >
                  <small *ngIf="companyDetailsForm.get('fullName').errors.minlength"
                    >* First Name should contains minimum 3 characters.</small
                  >
                </div>
              </div>

              <div class="form-group mb-4">
                <label  for="lname" class="color-orange fs13">Enter Your Last Name</label>
                <input  id="lname"
                  type="text"
                  class="form-control material-input fs13"
                  placeholder="Last Name"
                  formControlName="lastName"
                  (keypress)="alphaOnly($event)"
                  autocomplete="off"
                />
                <div
                  class="color-red"
                  *ngIf="companySubmitted && companyDetailsForm.get('lastName').errors"
                >
                  <small *ngIf="companyDetailsForm.get('lastName').errors.required"
                    >*Last Name is required.</small
                  >
                  <small *ngIf="companyDetailsForm.get('lastName').errors.minlength"
                    >* Last Name should contains minimum 3 characters.</small
                  >
                </div>
              </div>

              <div class="form-group mb-4">
                <label  for="company" class="color-orange fs13">Enter Your Company Name</label>
                <input  id="company"
                  type="text"
                  class="form-control material-input fs13"
                  placeholder="Company Name"
                  formControlName="companyName"
                  (keypress)="alphaOnly($event)"
                  autocomplete="off"
                  *ngIf="ParentCompanyId || alreadyExistingDomain"
                  readonly
                />
                <input
                  type="text"
                  class="form-control material-input fs13"
                  placeholder="Company Name"
                  formControlName="companyName"
                  (keypress)="alphaOnly($event)"
                  autocomplete="off"
                  *ngIf="!ParentCompanyId && !alreadyExistingDomain"
                />
                <div
                  class="color-red"
                  *ngIf="companySubmitted && companyDetailsForm.get('companyName').errors"
                >
                  <small *ngIf="companyDetailsForm.get('companyName').errors.required"
                    >*Company Name is required.</small
                  >
                  <small *ngIf="companyDetailsForm.get('companyName').errors.minlength"
                    >* Company Name should contains minimum 3 characters.</small
                  >
                </div>
              </div>
              <div class="form-group mb-4">
                <input
                  type="text"
                  class="form-control material-input fs13"
                  placeholder="Company Address"
                  ngx-gp-autocomplete
                  (onAddressChange)="handleHomePageCompanyAddressChange($event)"
                  #placesRef="ngx-places"
                  formControlName="address"
                  (keypress)="alphaOnly($event)"
                  *ngIf="!ParentCompanyId && !alreadyExistingDomain"
                />
              </div>
              <div class="form-group mb-4">
                <input
                  type="text"
                  class="form-control material-input fs13"
                  placeholder="Address Line 2"
                  formControlName="secondAddress"
                  *ngIf="!ParentCompanyId && !alreadyExistingDomain"
                />
              </div>
              <div class="form-group mb-4">
                <input
                  type="text"
                  class="form-control material-input fs13"
                  placeholder="City"
                  formControlName="city"
                  *ngIf="!ParentCompanyId && !alreadyExistingDomain"
                />
              </div>
              <div class="form-group mb-4">
                <input
                  type="text"
                  class="form-control material-input fs13"
                  placeholder="State"
                  formControlName="state"
                  *ngIf="!ParentCompanyId && !alreadyExistingDomain"
                />
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <div class="form-group mb-4">
                <input
                  type="text"
                  class="form-control material-input fs13"
                  placeholder="Country"
                  formControlName="country"
                  *ngIf="!ParentCompanyId && !alreadyExistingDomain"
                />
              </div>
              <div class="row">
                <div class="col-md-12">
                  <div class="form-group mb-4">
                    <input
                      type="text"
                      class="form-control material-input fs13"
                      formControlName="zipCode"
                      placeholder="Zip code"
                      (keypress)="alphaNum($event)"
                      *ngIf="!ParentCompanyId && !alreadyExistingDomain"
                    />
                  </div>
                </div>
              </div>
              <div class="form-group mb-4">
                <input
                  type="text"
                  class="form-control material-input fs13"
                  formControlName="website"
                  placeholder="Website"
                  *ngIf="!ParentCompanyId && !alreadyExistingDomain"
                />
                <div
                  class="color-red"
                  *ngIf="companySubmitted && companyDetailsForm.get('website').errors"
                >
                  <small *ngIf="companyDetailsForm.get('website').errors.pattern"
                    >*Enter Valid Website Url</small
                  >
                </div>
              </div>
            </div>
          </div>

          <div class="text-center mb-0">
            <button
              class="btn btn-grey-light color-dark-grey me-3 px-2rem radius20 fs13 fw-bold cairo-regular"
              (click)="prev(2)"
            >
              Previous
            </button>
            <button
              *ngIf="!ParentCompanyId"
              class="btn btn-orange color-orange radius20 px-4 fs13 fw-bold cairo-regular"
            >
              <em
                class="fa fa-spinner"
                aria-hidden="true"
                *ngIf="companyFormSubmitted && companyDetailsForm.valid"
              ></em>
              Continue
            </button>
            <button
              *ngIf="ParentCompanyId"
              class="btn btn-orange color-orange radius20 px-4 fs13 fw-bold cairo-regular"
              [disabled]="companyFormSubmitted && companyDetailsForm.valid"
            >
              <em
                class="fa fa-spinner"
                aria-hidden="true"
                *ngIf="companyFormSubmitted && companyDetailsForm.valid"
              ></em>
              Submit
            </button>
          </div>
        </form>
      </div>

      <div *ngIf="StepThree" class="col-md-12">
        <div class="mb-4 text-center">
          <h1 class="auth-title color-grey7 fs20 fw-bold cairo-regular my-2">
            Project Details
          </h1>
        </div>
        <form
          name="form"
          [formGroup]="homePageProjectDetailsForm"
          (ngSubmit)="onSubmit('project', userResponseToRegister)"
          novalidate
          autocomplete="off"
        >
          <div class="row">
            <div class="col-md-12">
              <div class="form-group mb-4">
                <label class="color-orange fs13"  for="proname" >Enter Your Project Name</label>
                <div class="input-group">
                  <input  id="proname"
                    type="text"
                    class="form-control material-input fs14"
                    placeholder="Project Name"
                    formControlName="projectName"
                    (keypress)="alphaNum($event)"
                  />
                </div>
                <div
                  class="color-red"
                  *ngIf="projectSubmitted && homePageProjectDetailsForm.get('projectName').errors"
                >
                  <small *ngIf="homePageProjectDetailsForm.get('projectName').errors.required"
                    >*Project Name is required.</small
                  >
                </div>
              </div>
              <div class="mb-4 project-details-map">
                <label class="color-orange fs13 pb-2"  for="location">Select the Location</label>
                <app-home-page-map [emitLatitude]="latitude" [emitLongitude]="longitude"  (latLongProps)="getLatLong($event)"></app-home-page-map>
              </div>
              <div class="form-group mb-4">
                <label class="color-orange fs13"  for="proadd">Project Address</label>
                <div class="input-group">
                  <input for="proadd"
                    type="text"
                    class="form-control material-input fs14"
                    formControlName="projectLocation"
                    ngx-gp-autocomplete
                    (onAddressChange)="handleProjectAddress($event)"
                    #placesRef="ngx-places"
                  />
                </div>
                <div
                  class="row color-red px-3 py-1"
                  *ngIf="
                    projectSubmitted && homePageProjectDetailsForm.get('projectLocation').errors
                  "
                >
                  <small *ngIf="homePageProjectDetailsForm.get('projectLocation').errors.required"
                    >*Project Location is required.</small
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="text-center mb-5">
            <button
              class="btn btn-grey-light color-dark-grey me-2 px-2rem radius20 fs13 fw-bold cairo-regular"
              (click)="prev(3)"
            >
              Previous
            </button>
            <button
              class="btn btn-orange color-orange radius20 px-5 fs13 fw-bold cairo-regular"
            >
              Next
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</ng-template>

<!--Confirmation Popup-->
<div id="confirm-popup9">
  <ng-template #userResponseToRegister>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          1. Click 'Yes' to remove you from the existing project. You will then have the option to
          create a new project/account.
          <br />
          2. Click 'No' to receive the link that was previously sent by the existing project.
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="userResponse('yes')"
        >
          <em class="fa fa-spinner" aria-hidden="true" *ngIf="removeAlreadyInvitedMember"></em>Yes
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="userResponse('no')"
        >
          <em class="fa fa-spinner" aria-hidden="true" *ngIf="requestInvitedLink"></em>
          No
        </button>
      </div>
    </div>
  </ng-template>
</div>
