import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import {
  debounceTime, filter, merge, Subscription,
} from 'rxjs';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';
import { MixpanelService } from '../../services/mixpanel.service';

@Component({
  selector: 'app-crane-request-comment',
  templateUrl: './crane-request-comment.component.html',
})
export class CraneRequestCommentComponent implements OnInit, OnDestroy {
  public commentDetailsForm: UntypedFormGroup;

  public ProjectId: any;

  public commentList: any = [];

  public loader = true;

  public submitted = false;

  public formSubmitted = false;

  public CraneRequestId: any;

  public ParentCompanyId: any;

  private readonly subscription: Subscription = new Subscription();

  public constructor(
    public deliveryService: DeliveryService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly mixpanelService: MixpanelService,
    private readonly toastr: ToastrService,
    public socket: Socket,
    public projectService: ProjectService,
  ) {
    this.commentForm();
  }

  public ngOnInit(): void {
    this.subscription.add(
      this.deliveryService.EditCraneRequestId.subscribe((id): void => {
        this.CraneRequestId = id;
        this.getHistory();
      }),
    );

    this.subscription.add(
      this.projectService.ParentCompanyId.subscribe((id): void => {
        if (id !== undefined && id !== null && id !== '') {
          this.ParentCompanyId = id;
        }
      }),
    );

    this.subscription.add(
      this.projectService.projectParent.subscribe((response): void => {
        if (response !== undefined && response !== null && response !== '') {
          this.ProjectId = response.ProjectId;
          this.ParentCompanyId = response.ParentCompanyId;
        }
      }),
    );

    const historyTrigger$ = merge(
      this.deliveryService.fetchData,
      this.deliveryService.fetchData1,
    ).pipe(
      debounceTime(100),
      filter(() => !!this.CraneRequestId),
    );

    this.subscription.add(
      historyTrigger$.subscribe(() => {
        this.getHistory();
      }),
    );
  }

  public getHistory(): void {
    if (!this.CraneRequestId || !this.ParentCompanyId || !this.ProjectId) {
      return;
    }

    this.loader = true;
    const param = {
      CraneRequestId: this.CraneRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    this.deliveryService.getCraneRequestComment(param).subscribe((res): void => {
      if (res?.data?.rows) {
        this.commentList = res.data.rows;
      }
      this.loader = false;
    });
  }

  public commentForm(): void {
    this.commentDetailsForm = this.formBuilder.group({
      comment: ['', Validators.compose([Validators.required])],
    });
  }

  public checkStringEmptyValues(formValue: { comment: string }): boolean {
    return !formValue.comment || formValue.comment.trim() === '';
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;

    if (this.commentDetailsForm.invalid
      || this.checkStringEmptyValues(this.commentDetailsForm.value)) {
      this.toastr.error('Please enter a valid comment.', 'OOPS!');
      return;
    }

    const formValue = this.commentDetailsForm.value;
    const payload = {
      comment: formValue.comment.trim(),
      CraneRequestId: this.CraneRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };

    this.deliveryService.createCraneRequestComment(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.mixpanelService.addMixpanelEvents('Comment added against a Crane Booking');
          this.socket.emit('craneCommentHistory', response);
          this.submitted = false;
          this.formSubmitted = false;
          this.deliveryService.updateCraneRequestHistory1({ status: true }, 'craneCommentHistory');
          this.commentDetailsForm.reset();
          this.commentDetailsForm.patchValue({ comment: '' });
        }
      },
      error: (commentError): void => {
        this.commentDetailsForm.reset();
        this.commentDetailsForm.patchValue({ comment: '' });
        if (commentError.message?.statusCode === 400) {
          this.showCommentError(commentError);
        } else {
          this.toastr.error(commentError.message, 'OOPS!');
          this.submitted = false;
          this.formSubmitted = false;
        }
      },
    });
  }

  public showCommentError(commentError: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let commentErrorMessage: any = '';
    const [firstDetail] = commentError.message.details;
    if (Array.isArray(firstDetail)) {
      const [firstMessage] = firstDetail;
      commentErrorMessage = firstMessage;
    } else {
      const [firstValue] = Object.values(firstDetail);
      commentErrorMessage = firstValue;
    }
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(commentErrorMessage);
  }

  public ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
