<ngx-loading [show]="loader" [config]="{ backdropBorderRadius: '3px' }"> </ngx-loading>

<section class="page-section pt-md-50px">
  <div class="page-inner-content position-relative">
    <div class="top-header calendar-filter position-absolute">
      <div class="row text-right">
        <div class="col-md-12">
          <div class="top-filter">
            <ul class="list-group list-group-horizontal justify-content-end">
              <li class="list-group-item p0 border-0 bg-transparent me-2">
                <div class="search-icon">
                  <input
                    class="form-control fs12 color-grey8"
                    [ngClass]="showSearchbar ? 'input-hover-disable' : 'input-search'"
                    placeholder="What you are looking for?"
                    (input)="getSearchNDR($event.target.value)"
                    [(ngModel)]="search"
                  />
                  <div class="icon">
                    <img
                      src="./assets/images/cross-close.svg"
                      *ngIf="showSearchbar"
                      (click)="clear()" (keydown) = "clear()"
                      alt="close-cross"
                    />
                    <em class="fa fa-search fs12 color-grey8" *ngIf="!showSearchbar"></em>
                  </div>
                </div>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent me-2 position-relative">
                <div class="filter-icon" (click)="openFilterModal(filter)" (keydown)="handleDownKeydown($event, filter,'','filter')">
                  <img src="./assets/images/filter.svg" class="h-12px icon" alt="Filter" />
                </div>
                <div
                  class="bg-orange rounded-circle position-absolute text-white filter-count"
                  *ngIf="filterCount > 0"
                >
                  <p class="m-0 text-center fs10">{{ filterCount }}</p>
                </div>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent">
                <button
                  type="button"
                  class="btn btn-orange-dark2 px-4 pt7 pb7 radius5 fs12"
                  (click)="openAddNDRModal()"
                  *ngIf="authUser?.User?.email != null && authUser?.User?.email != undefined"
                >
                  <em class="fa fa-plus me-1"></em> Add
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="delivery-calendar custom-full-calendar">
      <full-calendar
        [options]="calendarOptions"
        class="primary-full-calendar"
        id="calendar"
        #fullcalendar
      >
      </full-calendar>

      <h2 class="color-grey7 fs12 fw600 mt-3">Legend</h2>
      <div class="d-flex flex-wrap flex-md-nowrap">
        <div class="d-flex align-items-center me-3 mb-2 mb-md-0">
          <div class="w10 h10 radius10 me-2" [style.backgroundColor]="approved"></div>
          <p class="mb-0 color-grey7 fs12 fw600">Approved</p>
        </div>
        <div class="d-flex align-items-center me-3 mb-2 mb-md-0">
          <div class="w10 h10 radius10 me-2" [style.backgroundColor]="pending"></div>
          <p class="mb-0 color-grey7 fs12 fw600">In Review</p>
        </div>
        <div class="d-flex align-items-center me-3 mb-2 mb-md-0">
          <div class="w10 h10 radius10 me-2" [style.backgroundColor]="delivered"></div>
          <p class="mb-0 color-grey7 fs12 fw600">Completed</p>
        </div>
        <div class="d-flex align-items-center me-3 mb-2 mb-md-0">
          <div class="w10 h10 radius10 me-2" [style.backgroundColor]="rejected"></div>
          <p class="mb-0 color-grey7 fs12 fw600">Rejected</p>
        </div>
        <div class="d-flex align-items-center me-3 mb-2 mb-md-0">
          <div class="w10 h10 radius10 me-2" [style.backgroundColor]="expired"></div>
          <p class="mb-0 color-grey7 fs12 fw600">Expired</p>
        </div>
        <div class="d-flex align-items-center me-3 mb-2 mb-md-0">
          <div class="w10 h10 radius10 bg-grayed-out me-2"></div>
          <p class="mb-0 color-grey7 fs12 fw600">Calendar Event</p>
        </div>
        <div class="d-flex align-items-center mb-2 mb-md-0 flex-wrap flex-md-nowrap">
          <img
            src="./assets/images/noun-event-alert.svg"
            class="form-icon w10 h10 me-2"
            alt="allday"
          />
          <p class="mb-0 color-grey7 fs12 fw600">All day calendar event</p>
        </div>
      </div>
    </div>
  </div>
</section>

<ng-template #warning>
  <div class="modal-body">
    <div class="text-center mt-4">
      <img src="assets/images/noun_Alert.svg" alt="alert" class="mb-4" />
      <p class="fs13 color-dark-grey">
        Your subscription has been expired. Please upgrade your plan.
      </p>
    </div>
    <div class="text-center mt-4 mb-4">
      <button
        class="btn btn-grey color-dark-grey radius20 fs12 mb-3 me-3 fw-bold cairo-regular px-5"
        (click)="modalRef.hide()"
      >
        Cancel
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular mb-3 px-5"
        type="submit"
      >
        <em class="fa fa-spinner" aria-hidden="true"></em>Submit
      </button>
    </div>
  </div>
</ng-template>

<div
  class="card delivery-description"
  [ngClass]="descriptionPopup == true ? 'delivery-show' : 'deliver-hide '"
>
  <div class="card-header bg-transparent border-0 p-0 py-2 d-flex justify-content-between">
    <h2 class="fs14 fw-bold cairo-regular color-blue1 text-capitalize">
      {{ eventData?.description }}
    </h2>
    <span class="d-inline-flex justify-content-end">
      <span class="list-group-item border-0 p-0 px-2" (click)="openIdModal(eventData)" (keydown)="handleDownKeydown($event, eventData,'','open')">
        <em class="fas fa-expand-alt c-pointer color-grey8"></em>
      </span>
      <button
        type="button"
        class="close text-right color-grey8"
        aria-label="Close"
        (click)="closeDescription()"
      >
        <span aria-hidden="true"
          ><img src="./assets/images/modal-close.svg" alt="Modal Close"
        /></span>
      </button>
    </span>
  </div>
  <div class="card-body p-0 delivery-details-content">
    <div class="row mb-3 pb-3 bottom-line">
      <div class="col-6">
        <!--delivery time-->
        <ul class="list-group">
          <li class="list-group-item ps-0 border-0 pt-1 pb-0">
            <p class="color-grey14 fs11 mb-1">Time & Date</p>
          </li>
          <li class="list-group-item ps-0 border-0 pt-1 pb-0">
            <p class="mb-1 color-grey14 fs11">
              <strong class="fw600"
                >{{ eventData?.startDate }} ({{ eventData?.startTime }} -
                {{ eventData?.endTime }})</strong
              >
            </p>
          </li>
        </ul>
      </div>
      <div class="col-6">
        <!--Gate-->
        <ul class="list-group" *ngIf="eventData?.gateDetails?.length > 0">
          <li class="list-group-item ps-0 border-0 pt-1 pb-0">
            <p class="color-grey14 fs11 mb-1">Gate</p>
          </li>
          <li class="list-group-item ps-0 border-0 pt-1 pb-0">
            <p class="color-grey14 fs11 mb-1" *ngFor="let data of eventData?.gateDetails">
              <strong class="fw600">{{ data?.Gate.gateName }}</strong>
            </p>
          </li>
        </ul>
      </div>
    </div>
    <div class="row mb-3 pb-3 bottom-line">
      <div class="col-6">
        <ul class="list-group">
          <li class="list-group-item ps-0 border-0 pt-1 pb-0">
            <p class="mb-2 color-grey14 fs11">Responsible Company</p>
          </li>
          <li class="list-group-item ps-0 border-0 pt-1 pb-0">
            <div *ngIf="eventData?.companyDetails && eventData?.companyDetails?.length > 0">
              <p
                class="mb-1 color-grey14 fs11"
                *ngFor="let item of eventData.companyDetails | slice : 0 : 3; let i = index"
              >
                <strong class="fw600 d-flex align-items-center">
                  <span class="lh-13">{{ item?.Company?.companyName }}</span>
                </strong>
              </p>
            </div>
          </li>
        </ul>
      </div>
      <div class="col-6">
        <ul class="list-group">
          <li class="list-group-item ps-0 border-0 pt-1 pb-0">
            <p class="mb-2 color-grey14 fs11">Responsible Person</p>
          </li>
          <li class="list-group-item ps-0 border-0 pt-1 pb-0">
            <div *ngIf="eventData?.memberDetails && eventData?.memberDetails?.length > 0">
              <p
                class="bg-grey14 rounded-circle w30 h30 text-center d-inline-block me-2 text-white fs10 fw700 lh-30 eye-cursor"
                *ngFor="
                  let item of eventData.memberDetails | slice : 0 : 3;
                  let i = index;
                  let isLast = last
                "
              >
                <span
                  *ngIf="item?.Member?.User?.firstName"
                  tooltip="{{ item?.Member?.User?.firstName }} {{ item?.Member?.User?.lastName }}"
                  placement="top"
                >
                  {{ getResponsiblePeople(item?.Member?.User) }}
                </span>
                <span
                  *ngIf="!item?.Member?.User?.firstName"
                  tooltip="{{ item?.Member?.User?.email }}"
                  placement="top"
                >
                  {{ getResponsiblePeople(item?.Member?.User) }}
                </span>
              </p>
              <p
                class="bg-grey14 rounded-circle w30 h30 text-center d-inline-block me-2 text-white fs14 lh-30 eye-cursor"
                *ngIf="eventData?.memberDetails?.length > 3"
              >
                <span tooltip="{{ toolTipContent }}" placement="top">
                  +{{ eventData?.memberDetails?.length - 3 }}
                </span>
              </p>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div class="row mb-3 pb-3 bottom-line">
      <div class="col-6">
        <!--Equipment-->
        <ul class="list-group">
          <li class="list-group-item ps-0 border-0 pt-1 pb-0">
            <p class="mb-1 color-grey14 fs11">Equipment</p>
          </li>
          <li class="list-group-item ps-0 border-0 pt-1 pb-0">
            <p class="mb-1 color-grey14 fs11" *ngFor="let data of eventData?.equipmentDetails">
              <strong class="fw600">{{ data?.Equipment.equipmentName }}</strong>
            </p>
          </li>
        </ul>
      </div>
      <div class="col-6">
        <ul class="list-group">
          <li class="list-group-item ps-0 border-0 pt-1 pb-0">
            <p class="mb-1 color-grey14 fs11">Vehicle Detail</p>
          </li>
          <li class="list-group-item ps-0 border-0 pt-1 pb-0">
            <p class="mb-1 color-grey14 fs11" *ngIf="eventData?.vehicleDetails">
              <strong class="fw600">{{ eventData.vehicleDetails }}</strong>
            </p>
            <p class="mb-1 color-grey14 fs11" *ngIf="!eventData?.vehicleDetails">
              <strong class="fw600">-</strong>
            </p>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <div class="card-footer bg-transparent border-0 text-center p-0">
    <button
      class="fs12 cairo-regular btn btn-grey-light radius20 px-5 mx-2"
      (click)="openModal(voidNdrConfirmation)"
      *ngIf="eventData?.edit"
    >
      void
    </button>
    <button
      class="fs12 cairo-regular btn btn-green radius20 px-5 mx-2"
      (click)="openEditModal(eventData, null)"
      *ngIf="
        (eventData?.edit && !eventData?.recurrence) ||
        (eventData?.edit &&
          eventData?.recurrence &&
          eventData?.recurrence?.recurrence === 'Does Not Repeat')
      "
    >
      Edit
    </button>
    <button
      class="fs12 cairo-regular btn btn-green radius20 px-5 mx-2 edit-btn-popover"
      *ngIf="
        eventData?.edit &&
        eventData?.recurrence &&
        eventData?.recurrence?.recurrence !== 'Does Not Repeat'
      "
      (click)="changeRequestCollapse(eventData)"
      [popover]="popTemplate"
      popoverTitle=""
      placement="top"
    >
      Edit
      <img
        src="./assets/images/green-downarrow.svg"
        alt="Down Arrow"
        class="arrow float-right eye-cursor mb-1 ms-2 mt-0"
        [ngClass]="{ rotateup: allRequestIsOpened }"
        containerClass="editcustomClass"
      />
    </button>
  </div>
</div>

<!--Filter Modal-->
<div id="fiter-temp1">
  <ng-template #filter>
    <div class="modal-header border-0 pb-0">
      <h3 class="fs14 fw-bold cairo-regular color-text7 my-0">Filter</h3>
      <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true"
          ><img src="./assets/images/modal-close.svg" alt="Modal Close"
        /></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="filter-content">
        <form
          class="custom-material-form"
          id="filter-form1"
          [formGroup]="filterForm"
          (ngSubmit)="filterSubmit()"
          novalidate
        >
          <div class="row">
            <div class="col-md-12">
              <div class="input-group mb-3">
                <input
                  type="text"
                  class="form-control fs12 material-input"
                  placeholder="Description"
                  formControlName="descriptionFilter"
                />
                  <span class="input-group-text">
                    <img src="./assets/images/search-icon.svg" alt="Search" />
                  </span>
              </div>
              <div class="input-group mb-3">
                <input
                  class="form-control fs12 material-input"
                  #dp="bsDatepicker"
                  bsDatepicker
                  formControlName="dateFilter"
                  placeholder="Inspection Date"
                  [bsConfig]="{
                    isAnimated: true,
                    showWeekNumbers: false,
                    customTodayClass: 'today'
                  }"
                />
              </div>
              <div class="form-group">
                <select
                  class="form-control fs12 material-input mobile-wrapper"
                  id="companyFilter2"
                  formControlName="companyFilter"
                >
                  <option value="" disabled selected hidden>Company</option>
                  <option *ngFor="let item of companyList" value="{{ item.id }}">
                    {{ item.companyName }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <select
                  class="form-control fs12 material-input"
                  id="memberFilter1"
                  formControlName="memberFilter"
                >
                  <option value="" disabled selected hidden>Responsible Person</option>
                  <ng-container *ngFor="let item of memberList">
                    <option *ngIf="item.status === 'pending' && !item.isGuestUser" value="{{ item.id }}">
                      {{ item.User?.email }}
                    </option>
                    <option *ngIf="item.status === 'pending' && item.isGuestUser" value="{{ item.id }}">
                      {{ item.User?.email }} (Guest)
                    </option>

                    <option *ngIf="item.status === 'completed'" value="{{ item.id }}">
                      {{ item.User?.firstName }}{{ item.User?.lastName }}
                    </option>
                  </ng-container>
                </select>
              </div>
              <div class="form-group">
                <select
                  class="form-control fs12 material-input"
                  id="gateFilter1"
                  formControlName="gateFilter"
                >
                  <option value="" disabled selected hidden>Gate</option>
                  <option *ngFor="let item of gateList" value="{{ item.id }}" [ngValue]="item.id">
                    {{ item.gateName }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <select
                  class="form-control fs12 material-input"
                  id="equipmentFilter1"
                  formControlName="equipmentFilter"
                >
                  <option value="" disabled selected hidden>Equipment</option>
                  <option
                    *ngFor="let item of equipmentList"
                    value="{{ item.id }}"
                    [ngValue]="item.id"
                  >
                    {{ item.equipmentName }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <select
                  class="form-control fs12 material-input"
                  id="locationFilter1"
                  formControlName="locationFilter"
                >
                  <option value="" disabled selected hidden>Location</option>
                  <option
                    *ngFor="let item of locationList"
                    value="{{ item.locationPath }}"
                    [ngValue]="item.locationPath"
                  >
                    {{ item.locationPath }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <select
                  class="form-control fs12 material-input"
                  id="statusFilter1"
                  formControlName="statusFilter"
                >
                  <option value="" disabled selected hidden>Status</option>
                  <option *ngFor="let item of wholeStatus" value="{{ item }}">
                    {{ item }}
                  </option>
                </select>
              </div>
              <div class="input-group mb-3">
                <input
                  type="text"
                  class="form-control fs12 material-input"
                  placeholder="Pick From"
                  formControlName="pickFrom"
                />
                  <span class="input-group-text">
                    <img src="./assets/images/search-icon.svg" alt="Search" />
                  </span>
              </div>
              <div class="input-group mb-3">
                <input
                  type="text"
                  class="form-control fs12 material-input"
                  placeholder="Pick To"
                  formControlName="pickTo"
                />
                  <span class="input-group-text">
                    <img src="./assets/images/search-icon.svg" alt="Search" />
                  </span>
                </div>
                <div class="form-group">
                  <select
                    class="form-control fs12 material-input"
                    id="inspectionStatusFilter"
                    formControlName="inspectionStatusFilter"
                  >
                    <option value="" disabled selected hidden>Inspection Status</option>
                    <option *ngFor="let item of inspectionStatusList" value="{{ item }}">
                      {{ item === 'Pass' ? 'Passed' : 'Failed' }}
                    </option>
                  </select>
                </div>
                <div class="form-group">
                  <select
                    class="form-control fs12 material-input"
                    id="inspectionTypeFilter"
                    formControlName="inspectionTypeFilter"
                  >
                    <option value="" disabled selected hidden>Inspection Type</option>
                    <option *ngFor="let item of inspectionTypeList" value="{{ item }}">
                      {{ item }}
                    </option>
                  </select>
                </div>
              <div class="row justify-content-end">
                <button
                  class="btn btn-orange radius20 col-4 mt-2 fs12 fw-bold cairo-regular btn-block mx-1"
                  type="submit"
                >
                  Apply
                </button>
                <button
                  class="btn btn-orange radius20 fs12 col-4 mt-2 fw-bold cairo-regular btn-block mx-1"
                  type="button"
                  (click)="resetFilter()"
                >
                  Reset
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
</div>
<!--Filter Modal-->

<!--void delivery request confirmation Popup-->
<div id="confirm-popup9">
  <ng-template #voidNdrConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure you want to Void?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="voidConfirmationResponse('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="voidConfirmationResponse('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
<!--cancel confirmation Popup-->

<div
  class="card calendarsetting-description p-2"
  [ngClass]="calendarDescriptionPopup == true ? 'delivery-show' : 'deliver-hide'"
>
  <span class="d-inline-flex">
    <p class="fs14 fw-bold cairo-regular color-blue1 w-75 mb-0 ps-4">
      {{ viewEventData?.description }}
    </p>
    <span class="d-inline-flex justify-content-end float-right pl100">
      <span aria-hidden="true" class="px-1 close eye-cursor" (click)="closeCalendarDescription()" (keydown)="handleDownKeydown($event, '','','close')">
        <img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </span>
  </span>
  <div class="card-body p-0 delivery-details-content">
    <div class="d-flex justify-content-between">
      <p class="color-grey14 fs12 pt-2">
        <span>
          <img src="../assets/images/settings-time.svg" alt="clock-time" />
        </span>
        <strong class="ps-2">
          {{ changeFormat(viewEventData?.fromDate) }}
          <span *ngIf="!viewEventData?.isAllDay">
            {{ viewEventData?.fromDate | date : 'shortTime' }} -
            {{ viewEventData?.toDate | date : 'shortTime' }}
          </span>
          <span *ngIf="viewEventData?.isAllDay"> - All Day </span>
        </strong>
      </p>
    </div>
    <p class="color-grey14 fs12 pl28">
      <strong class="fs10">
        {{ viewEventData?.timeZoneLocation }}
      </strong>
    </p>
    <p class="color-grey14 fs12 pl28">
      <strong class="fs10">
        {{ message }}
      </strong>
    </p>
  </div>
</div>

<ng-template #popTemplate>
  <ul class="list-group c-pointer event-selection-popup my-2 w190">
    <li
      class="list-group-item border-0 p-1 cairo-regular c-pointer fw700 fs12"
      *ngFor="let data of seriesOptions"
      [ngClass]="{ disabled: data.disabled }"
      [tabindex]="data.disabled ? '-1' : '0'"
      (click)="openEditModal(eventData, data?.option)" (keydown)="handleDownKeydown($event, eventData,data?.option,'edit')"
    >
      <span class="ps-2">{{ data?.text }}</span>
    </li>
  </ul>
</ng-template>
