import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of, Subject } from 'rxjs';
import { ConcreteDetailContentComponent } from './concrete-detail-content.component';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';

describe('ConcreteDetailContentComponent', () => {
  let component: ConcreteDetailContentComponent;
  let fixture: ComponentFixture<ConcreteDetailContentComponent>;
  let deliveryServiceMock: jest.Mocked<DeliveryService>;
  let projectServiceMock: jest.Mocked<ProjectService>;

  const mockConcreteRequest = {
    description: 'Test Description',
    location: {
      locationPath: 'Test Location Path',
    },
    locationDetails: [
      {
        ConcreteLocation: {
          location: 'Test Location Detail',
        },
      },
    ],
    ConcreteRequestId: '123',
    concretePlacementStart: new Date(),
    concretePlacementEnd: new Date(),
    gateDetails: [
      {
        Gate: {
          gateName: 'Test Gate',
        },
      },
    ],
    equipmentDetails: [
      {
        Equipment: {
          equipmentName: 'Test Equipment',
        },
      },
    ],
    concreteSupplierDetails: [
      {
        Company: {
          companyName: 'Test Supplier',
        },
      },
    ],
    mixDesignDetails: [
      {
        ConcreteMixDesign: {
          mixDesign: 'Test Mix Design',
        },
      },
    ],
    concreteOrderNumber: 'ORDER123',
    slump: 'Test Slump',
    truckSpacingHours: '2 hours',
    status: 'In Progress',
    concreteQuantityOrdered: '100 yards',
    primerForPump: 'Yes',
    isConcreteConfirmed: true,
    concreteConfirmedOn: new Date(),
    OriginationAddress: 'Test Origin Address',
    vehicleType: 'Test Vehicle Type',
    pumpSizeDetails: [
      {
        ConcretePumpSize: {
          pumpSize: 'Test Pump Size',
        },
      },
    ],
    pumpOrderedDate: new Date(),
    pumpLocation: 'Test Pump Location',
    pumpWorkStart: new Date(),
    pumpWorkEnd: new Date(),
    isPumpConfirmed: true,
    pumpConfirmedOn: new Date(),
    hoursToCompletePlacement: 2,
    minutesToCompletePlacement: 30,
    cubicYardsTotal: '95 yards',
    OriginationAddressPump: 'Test Pump Origin Address',
    vehicleTypePump: 'Test Pump Vehicle Type',
    notes: 'Test Notes',
  };

  beforeEach(async () => {
    deliveryServiceMock = {
      showFooterButtonsVisible: jest.fn(),
      EditConcreteRequestId: new Subject(),
      fetchConcreteData: new Subject(),
      fetchConcreteData1: new Subject(),
      getConcreteRequestDetail: jest.fn().mockReturnValue(of({ data: mockConcreteRequest })),
    } as any;

    projectServiceMock = {
      projectParent: new Subject(),
    } as any;

    await TestBed.configureTestingModule({
      declarations: [ConcreteDetailContentComponent],
      providers: [
        { provide: DeliveryService, useValue: deliveryServiceMock },
        { provide: ProjectService, useValue: projectServiceMock },
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ConcreteDetailContentComponent);
    component = fixture.componentInstance;
    component.concreteRequest = mockConcreteRequest;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    component.concreteRequest = undefined;
    expect(component.loader).toBeFalsy();
    expect(component.ConcreteRequestId).toBeUndefined();
    expect(component.ParentCompanyId).toBeUndefined();
    expect(component.ProjectId).toBeUndefined();
    expect(component.concreteRequest).toBeUndefined();
  });

  it('should call showFooterButtonsVisible with false on construction', () => {
    expect(deliveryServiceMock.showFooterButtonsVisible).toHaveBeenCalledWith(false);
  });

  it('should update ParentCompanyId and ProjectId when projectParent emits', () => {
    const mockProject = {
      ParentCompanyId: '123',
      ProjectId: '456',
    };
    projectServiceMock.projectParent.next(mockProject);
    expect(component.ParentCompanyId).toBe('123');
    expect(component.ProjectId).toBe('456');
  });

  it('should get responsible people initials correctly', () => {
    const mockPerson = {
      firstName: 'John',
      lastName: 'Doe',
    };
    expect(component.getResponsiblePeople(mockPerson)).toBe('JD');
  });

  it('should return UU for invalid person object', () => {
    expect(component.getResponsiblePeople(null)).toBe('UU');
    expect(component.getResponsiblePeople({})).toBe('UU');
  });

  it('should get concrete request when EditConcreteRequestId emits', () => {
    component.ParentCompanyId = '123';
    component.ProjectId = '456';
    deliveryServiceMock.EditConcreteRequestId.next('789');

    expect(deliveryServiceMock.getConcreteRequestDetail).toHaveBeenCalledWith({
      ConcreteRequestId: '789',
      ParentCompanyId: '123',
      ProjectId: '456',
    });
  });

  it('should not get concrete request when ProjectId or ParentCompanyId is missing', () => {
    component.ParentCompanyId = null;
    component.ProjectId = '456';
    deliveryServiceMock.EditConcreteRequestId.next('789');

    expect(deliveryServiceMock.getConcreteRequestDetail).not.toHaveBeenCalled();
  });

  it('should handle concrete request response correctly', () => {
    component.ParentCompanyId = '123';
    component.ProjectId = '456';
    component.getConcreteRequest();

    expect(component.concreteRequest).toEqual(mockConcreteRequest);
    expect(component.loader).toBeFalsy();
    expect(deliveryServiceMock.showFooterButtonsVisible).toHaveBeenCalledWith(true);
  });

  it('should unsubscribe on destroy', () => {
    const nextSpy = jest.spyOn(component['unsubscribe$'], 'next');
    const completeSpy = jest.spyOn(component['unsubscribe$'], 'complete');

    component.ngOnDestroy();

    expect(nextSpy).toHaveBeenCalled();
    expect(completeSpy).toHaveBeenCalled();
  });
});
