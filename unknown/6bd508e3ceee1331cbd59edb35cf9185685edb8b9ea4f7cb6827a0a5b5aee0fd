<div class="delivery-ddetails-content my-3 mx-2 text-center" *ngIf="loader">
  <div class="fs18 fw-bold cairo-regular my-5 text-black">Loading...</div>
</div>
<div class="delivery-ddetails-content my-3 mx-2" *ngIf="!loader">
  <div class="row px-2 mt-2">
    <div class="col-md-12 px-2">
      <h4 class="color-grey14 fs12 fw700">Description</h4>
      <p class="color-grey14 fs12 font-weight-normal">
        {{ concreteRequest?.description }}
      </p>
    </div>
  </div>
  <div class="row px-2 mt-2">
    <div class="col-md-4 my-2 px-2">
      <h4 class="color-grey14 fs12 fw700">Location</h4>
      <div *ngIf="concreteRequest?.location">
        <p class="color-grey14 fs12 font-weight-normal">
          {{ concreteRequest?.location?.locationPath }}
        </p>
      </div>
      <p
        class="color-grey14 fs13 mb-2 fw500"
        *ngIf="!concreteRequest?.location"
      >
        -
      </p>
    </div>
    <div class="col-md-4 my-2 px-2">
      <h4 class="color-grey14 fs12 fw700">Concrete ID</h4>
      <p class="color-grey14 fs12 mb-2 fw500">{{ concreteRequest?.ConcreteRequestId }}</p>
    </div>
    <div class="col-md-4 my-2 px-2">
      <h4 class="color-grey14 fs12 fw700">Responsible Person</h4>
      <div *ngIf="concreteRequest?.memberDetails && concreteRequest?.memberDetails?.length > 0">
        <p
          class="bg-grey14 rounded-circle w30 h30 text-center d-inline-block me-2 text-white fs10 fw700 lh-30 eye-cursor"
          *ngFor="let item of concreteRequest?.memberDetails?.slice(0, 3); let i = index"
        >
          <span
            *ngIf="item?.Member?.User?.firstName && item?.Member?.isGuestUser === false"
            tooltip="{{ item?.Member?.User?.firstName }} {{ item?.Member?.User?.lastName }}"
            placement="top"
          >
            {{ getResponsiblePeople(item?.Member?.User) }}
          </span>
          <span
            *ngIf="item?.Member?.User?.firstName && item?.Member?.isGuestUser === true"
            tooltip="{{ item?.Member?.User?.firstName }} {{ item?.Member?.User?.lastName }} (Guest)"
            placement="top"
          >
            {{ getResponsiblePeople(item?.Member?.User) }}
          </span>
          <span
            *ngIf="!item?.Member?.User?.firstName && item?.Member?.isGuestUser === false"
            tooltip="{{ item?.Member?.User?.email }}"
            placement="top"
          >
            {{ getResponsiblePeople(item?.Member?.User) }}
          </span>
          <span
            *ngIf="!item?.Member?.User?.firstName && item?.Member?.isGuestUser === true"
            tooltip="{{ item?.Member?.User?.email }} (Guest)"
            placement="top"
          >
            {{ getResponsiblePeople(item?.Member?.User) }}
          </span>
        </p>
      </div>
    </div>
  </div>
  <div class="row px-2 mt-2">
    <div class="col-md-4 my-2 px-2">
      <h4 class="color-grey14 fs12 fw700">Additional Location Details</h4>
      <div *ngIf="concreteRequest?.locationDetails">
        <p
          class="color-grey14 fs12 mb-2 fw500"
          *ngFor="let item of concreteRequest?.locationDetails"
        >
        {{ item?.ConcreteLocation?.location ? item.ConcreteLocation.location : '-' }}
        </p>
      </div>
      <p
        class="color-grey14 fs13 mb-2 fw500"
        *ngIf="!concreteRequest?.locationDetails"
      >
        -
      </p>
    </div>
    <div class="col-md-4 my-2 px-2">
      <h4 class="color-grey14 fs12 fw700">Time</h4>
      <div>
        <p class="color-grey14 fs12 mb-2 fw500">
          {{ concreteRequest.concretePlacementStart | date : 'shortTime' }} - {{ concreteRequest.concretePlacementEnd | date : 'shortTime' }}
        </p>
      </div>
    </div>
    <div class="col-md-4 my-2 px-2">
      <h4 class="color-grey14 fs12 fw700">Date</h4>
      <div>
        <p class="color-grey14 fs12 mb-2 fw500">
          {{ concreteRequest.concretePlacementStart | date : 'MM/dd/yyyy' }}
        </p>
      </div>
    </div>
  </div>

  <div class="row px-2 mt-2">
    <div class="col-md-4 col-lg-4">
      <h4 class="color-grey14 fs12 mb5 font-weight-normal">Gate</h4>
      <div *ngIf="concreteRequest.gateDetails && concreteRequest.gateDetails?.length > 0">
        <p class="color-grey15 fs12 mb-2 fw500" *ngFor="let item of concreteRequest.gateDetails">
          {{ item?.Gate?.gateName }}
        </p>
      </div>
      <p
        class="color-grey15 fs12 mb-2 fw500"
        *ngIf="!concreteRequest.gateDetails || concreteRequest.gateDetails?.length === 0"
      >
        -
      </p>
    </div>
    <div class="col-md-4 col-lg-4">
      <h4 class="color-grey14 fs12 mb5 font-weight-normal">Equipment</h4>
      <div *ngIf="concreteRequest.equipmentDetails && concreteRequest.equipmentDetails?.length > 0">
        <p class="color-grey15 fs12 mb0 fw500" *ngFor="let item of concreteRequest.equipmentDetails">
          {{ item?.Equipment?.equipmentName }}
        </p>
      </div>
      <p
        class="color-grey15 fs12 mb0 fw500"
        *ngIf="!concreteRequest.equipmentDetails || concreteRequest.equipmentDetails?.length === 0"
      >
        -
      </p>
    </div>
  </div>


  <div class="row">
    <div class="col-md-3">
      <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Concrete Details</h1>
    </div>
    <div class="col-md-9 line-1"></div>
  </div>
  <div class="row px-2 mt-2">
    <div class="col-md-4 my-0 px-2">
      <h4 class="color-grey14 fs12 fw700">Concrete Supplier</h4>
      <div
        *ngIf="
          concreteRequest?.concreteSupplierDetails &&
          concreteRequest?.concreteSupplierDetails?.length > 0
        "
      >
        <p
          class="color-grey14 fs12 mb-2 fw500"
          *ngFor="let item of concreteRequest?.concreteSupplierDetails"
        >
          {{ item?.Company?.companyName }}
        </p>
      </div>
      <p
        class="color-grey14 fs12 mb-2 fw500"
        *ngIf="
          !concreteRequest?.concreteSupplierDetails ||
          concreteRequest?.concreteSupplierDetails?.length === 0
        "
      >
        -
      </p>
    </div>
    <div class="col-md-4 my-0 px-2">
      <h4 class="color-grey14 fs12 fw700">Mix Design</h4>
      <div
        *ngIf="concreteRequest?.mixDesignDetails && concreteRequest?.mixDesignDetails?.length > 0"
      >
        <p
          class="color-grey14 fs12 mb-2 font-weight-normal"
          *ngFor="let item of concreteRequest?.mixDesignDetails"
        >
          {{ item?.ConcreteMixDesign?.mixDesign }}
        </p>
      </div>
      <p
        class="color-grey14 fs13 mb-2 fw500"
        *ngIf="
          !concreteRequest?.mixDesignDetails || concreteRequest?.mixDesignDetails?.length === 0
        "
      >
        -
      </p>
    </div>
    <div class="col-md-4 my-0 px-2">
      <h4 class="color-grey14 fs12 fw700">Order Number</h4>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="concreteRequest?.concreteOrderNumber">
        {{ concreteRequest?.concreteOrderNumber }}
      </p>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="!concreteRequest?.concreteOrderNumber">
        -
      </p>
    </div>
  </div>
  <div class="row px-2 my-0">
    <div class="col-md-4 mt-4 px-2">
      <h4 class="color-grey14 fs12 fw700">Slump</h4>
      <p class="color-grey15 fs12 fw500" *ngIf="concreteRequest?.slump">{{ concreteRequest?.slump }}</p>
      <p class="color-grey15 fs12 fw500"*ngIf="!concreteRequest?.slump">-</p>
    </div>
    <div class="col-md-4 mt-4 px-2">
      <h4 class="color-grey14 fs12 fw700">Truck Spacing</h4>
      <p class="color-grey14 fs12 font-weight-normal"*ngIf="concreteRequest?.truckSpacingHours">{{ concreteRequest?.truckSpacingHours }}</p>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="!concreteRequest?.truckSpacingHours">-</p>
    </div>
    <div class="col-md-4 mt-4 px-2">
      <h4 class="color-grey14 fs12 fw700">Concrete Request Status</h4>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="concreteRequest?.status">{{ concreteRequest?.status }}</p>
      <p class="color-grey14 fs12 font-weight-normal"*ngIf="!concreteRequest?.status">-</p>
    </div>
  </div>
  <div class="row px-2 my-0">
    <div class="col-md-4 my-4 px-2">
      <h4 class="color-grey14 fs12 fw700">Quantity Ordered</h4>
      <p class="color-grey15 fs12 fw500" *ngIf="concreteRequest?.concreteQuantityOrdered">{{ concreteRequest?.concreteQuantityOrdered }}</p>
      <p class="color-grey15 fs12 fw500" *ngIf="!concreteRequest?.concreteQuantityOrdered">-</p>
    </div>
    <div class="col-md-4 my-4 px-2">
      <h4 class="color-grey14 fs12 fw700">Primer ordered for the pump</h4>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="concreteRequest?.primerForPump">{{ concreteRequest?.primerForPump }}</p>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="!concreteRequest?.primerForPump">-</p>
    </div>
    <div class="col-md-4 my-4 px-2">
      <h4 class="color-grey14 fs12 fw700">Concrete Confirmed</h4>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="concreteRequest?.isConcreteConfirmed">
        Confirmed on {{ concreteRequest?.concreteConfirmedOn | date : 'medium' }}
      </p>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="!concreteRequest?.isConcreteConfirmed">
        -
      </p>
    </div>
  </div>
  <div class="row px-2 my-0">
    <div class="col-md-4 my-4 px-2">
      <h4 class="color-grey14 fs12 fw700">Origination Address</h4>
      <p class="color-grey15 fs12 fw500" *ngIf="concreteRequest?.OriginationAddress">{{ concreteRequest?.OriginationAddress }}</p>
      <p class="color-grey15 fs12 fw500" *ngIf="!concreteRequest?.OriginationAddress">-</p>
    </div>
    <div class="col-md-4 my-4 px-2">
      <h4 class="color-grey14 fs12 fw700">Vehicle Type</h4>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="concreteRequest?.vehicleType">{{ concreteRequest?.vehicleType }}</p>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="!concreteRequest?.vehicleType">-</p>
    </div>
  </div>
  <div class="row">
    <div class="col-md-3">
      <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Pump Details</h1>
    </div>
    <div class="col-md-9 line-1 line-2"></div>
  </div>
  <div class="row px-2 my-0">
    <div class="col-md-4 my-4 px-2">
      <h4 class="color-grey14 fs12 fw700">Pump Size</h4>
      <div *ngIf="concreteRequest?.pumpSizeDetails && concreteRequest?.pumpSizeDetails?.length > 0">
        <p
          class="color-grey15 fs12 mb-2 fw500"
          *ngFor="let item of concreteRequest?.pumpSizeDetails"
        >
          {{ item?.ConcretePumpSize?.pumpSize }}
        </p>
      </div>
      <p
        class="color-grey15 fs13 mb-2 fw500"
        *ngIf="!concreteRequest?.pumpSizeDetails || concreteRequest?.pumpSizeDetails?.length === 0"
      >
        -
      </p>
    </div>
    <div class="col-md-4 my-4 px-2">
      <h4 class="color-grey14 fs12 fw700">Pump Ordered Date</h4>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="concreteRequest?.pumpOrderedDate">
        Ordered on {{ concreteRequest?.pumpOrderedDate | date : 'MM/dd/yyyy' }}
      </p>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="!concreteRequest?.pumpOrderedDate">
        -
      </p>
    </div>
    <div class="col-md-4 my-4 px-2">
      <h4 class="color-grey14 fs12 fw700">Pump Location</h4>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="concreteRequest?.pumpLocation">
        {{ concreteRequest?.pumpLocation }}
      </p>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="!concreteRequest?.pumpLocation">-</p>
    </div>
  </div>
  <div class="row px-2 my-0">
    <div class="col-md-4 my-4 px-2">
      <h4 class="color-grey14 fs12 fw700">Pump Show Up Time</h4>
      <p class="color-grey15 fs12 fw500" *ngIf="concreteRequest?.pumpWorkStart">
        {{ concreteRequest.pumpWorkStart | date : 'shortTime' }}
      </p>
      <p class="color-grey15 fs12 fw500" *ngIf="!concreteRequest?.pumpWorkStart">-</p>
    </div>
    <div class="col-md-4 my-4 px-2">
      <h4 class="color-grey14 fs12 fw700">Pump Completion Time</h4>
      <p class="color-grey15 fs12 fw500" *ngIf="concreteRequest?.pumpWorkEnd">
        {{ concreteRequest.pumpWorkEnd | date : 'shortTime' }}
      </p>
      <p class="color-grey15 fs12 fw500" *ngIf="!concreteRequest?.pumpWorkEnd">-</p>
    </div>
    <div class="col-md-4 my-4 px-2">
      <h4 class="color-grey14 fs12 fw700">Pump Confirmed</h4>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="concreteRequest?.isPumpConfirmed">
        Confirmed on {{ concreteRequest?.pumpConfirmedOn | date : 'medium' }}
      </p>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="!concreteRequest?.isPumpConfirmed">
        -
      </p>
    </div>
  </div>
  <div class="row" *ngIf="concreteRequest?.status === 'Completed'">
    <div class="col-md-3">
      <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Other Details</h1>
    </div>
    <div class="col-md-9 line-1"></div>
  </div>
  <div class="row px-2 my-0" *ngIf="concreteRequest?.status === 'Completed'">
    <div class="col-md-4 my-4 px-2">
      <h4 class="color-grey14 fs12 fw700">Completion Time</h4>
      <p class="color-grey15 fs12 fw500">
        <span *ngIf="concreteRequest?.hoursToCompletePlacement">
          {{ concreteRequest?.hoursToCompletePlacement }} Hours
        </span>
        <span *ngIf="!concreteRequest?.hoursToCompletePlacement"> - Hours </span>
        <span *ngIf="concreteRequest?.minutesToCompletePlacement">
          {{ concreteRequest?.minutesToCompletePlacement }} Minutes
        </span>
        <span *ngIf="!concreteRequest?.minutesToCompletePlacement"> - Minutes </span>
      </p>
    </div>
    <div class="col-md-4 my-4 px-2">
      <h4 class="color-grey14 fs12 fw700">Total Cubic Yards Placed</h4>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="concreteRequest?.cubicYardsTotal">
        {{ concreteRequest?.cubicYardsTotal }}
      </p>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="!concreteRequest?.cubicYardsTotal">
        -
      </p>
    </div>
  </div>
  <div class="row px-2 my-0">
    <div class="col-md-4 my-4 px-2">
      <h4 class="color-grey14 fs12 fw700">Origination Address</h4>
      <p class="color-grey15 fs12 fw500" *ngIf="concreteRequest?.OriginationAddressPump">{{ concreteRequest?.OriginationAddressPump }}</p>
      <p class="color-grey15 fs12 fw500" *ngIf="!concreteRequest?.OriginationAddressPump">-</p>
    </div>
    <div class="col-md-4 my-4 px-2">
      <h4 class="color-grey14 fs12 fw700">Vehicle Type</h4>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="concreteRequest?.vehicleTypePump">{{ concreteRequest?.vehicleTypePump }}</p>
      <p class="color-grey14 fs12 font-weight-normal" *ngIf="!concreteRequest?.vehicleTypePump">-</p>
    </div>
  </div>
  <div class="row" *ngIf="concreteRequest?.notes">
    <div class="col-md-12 my-4 px-2">
      <h4 class="color-grey14 fs12 fw700">Notes</h4>
      <p class="color-grey15 fs12 fw500">
        {{ concreteRequest?.notes }}
      </p>
    </div>
  </div>
</div>
