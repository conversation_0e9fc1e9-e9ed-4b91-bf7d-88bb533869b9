<ngx-loading [show]="loader" [config]="{ backdropBorderRadius: '3px' }"> </ngx-loading>
<section class="page-section pt-md-50px">
  <div class="page-inner-content position-relative">
    <div class="top-header calendar-filter position-absolute">
      <div class="row text-end">
        <div class="col-md-12">
          <div class="top-filter">
            <ul class="list-group list-group-horizontal justify-content-end">
              <li class="list-group-item p0 border-0 bg-transparent me-2">
                <div class="search-icon">
                  <input
                    class="form-control fs12 color-grey8"
                    [ngClass]="showSearchbar ? 'input-hover-disable' : 'input-search'"
                    placeholder="What are you looking for?"
                    (input)="getSearch($event.target.value)"
                    [(ngModel)]="search"
                  />
                  <div class="icon">
                    <img
                      src="./assets/images/cross-close.svg"
                      *ngIf="showSearchbar"
                      (click)="clear()" (keydown)="handleDownKeydown($event, '','clear')"
                      alt="close-cross"
                    />
                    <em class="fa fa-search fs12 color-grey8" *ngIf="!showSearchbar"></em>
                  </div>
                </div>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent me-2 position-relative">
                <div class="filter-icon" (click)="openFilterModal(filter)" (keydown)="handleDownKeydown($event, filter,'filter')">
                  <img src="./assets/images/filter.svg" class="h-12px icon" alt="Filter" />
                </div>
                <div
                  class="bg-orange rounded-circle position-absolute text-white filter-count"
                  *ngIf="filterCount > 0"
                >
                  <p class="m-0 text-center fs10">{{ filterCount }}</p>
                </div>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent">
                <button
                  type="button"
                  class="btn btn-orange-dark2 px-4 pt7 pb7 radius5 fs12"
                  (click)="openAddModal()"
                >
                  <em class="fa fa-plus me-1"></em> Add
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="delivery-calendar custom-full-calendar crane-calendar">
      <full-calendar
        [options]="calendarOptions"
        class="primary-full-calendar eye-cursor"
        id="calendar"
        #fullcalendar
      >
      </full-calendar>
      <h2 class="color-grey7 fs12 fw600 mt-3">Legend</h2>
      <div class="d-flex flex-wrap flex-md-nowrap">
        <div class="d-flex align-items-center me-3 mb-2 mb-md-0">
          <div class="w10 h10 radius10 me-2" [style.backgroundColor]="approved"></div>
          <p class="mb-0 color-grey7 fs12 fw600">Approved</p>
        </div>
        <div class="d-flex align-items-center me-3 mb-2 mb-md-0">
          <div class="w10 h10 radius10 me-2" [style.backgroundColor]="delivered"></div>
          <p class="mb-0 color-grey7 fs12 fw600">Completed</p>
        </div>
        <div class="d-flex align-items-center me-3 mb-2 mb-md-0">
          <div class="w10 h10 radius10 me-2" [style.backgroundColor]="rejected"></div>
          <p class="mb-0 color-grey7 fs12 fw600">Rejected</p>
        </div>
        <div class="d-flex align-items-center me-3 mb-2 mb-md-0">
          <div class="w10 h10 radius10 me-2" [style.backgroundColor]="expired"></div>
          <p class="mb-0 color-grey7 fs12 fw600">Expired</p>
        </div>
        <div class="d-flex align-items-center me-3 mb-2 mb-md-0">
          <div class="w10 h10 radius10 me-2" [style.backgroundColor]="pending"></div>
          <p class="mb-0 color-grey7 fs12 fw600">Tentative</p>
        </div>
        <div class="d-flex align-items-center me-3 mb-2 mb-md-0">
          <div class="w10 h10 radius10 bg-grayed-out me-2"></div>
          <p class="mb-0 color-grey7 fs12 fw600">Calendar Event</p>
        </div>
        <div class="d-flex align-items-center mb-2 mb-md-0 flex-wrap flex-md-nowrap">
          <img
            src="./assets/images/noun-event-alert.svg"
            class="form-icon w10 h10 me-2"
            alt="allday"
          />
          <p class="mb-0 color-grey7 fs12 fw600">All day calendar event</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!--Filter Modal-->
<div id="fiter-temp1">
  <ng-template #filter>
    <div class="modal-header border-0 pb-0">
      <h3 class="fs14 fw-bold cairo-regular color-text7 my-0">Filter</h3>
      <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true"
          ><img src="./assets/images/modal-close.svg" alt="Modal Close"
        /></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="filter-content" *ngIf="!modalLoader">
        <form
          class="custom-material-form"
          id="filter-form1"
          [formGroup]="filterForm"
          (ngSubmit)="filterSubmit()"
          novalidate
        >
          <div class="row">
            <div class="col-md-12">
              <div class="input-group mb-3">
                <input
                  type="text"
                  class="form-control fs12 material-input"
                  placeholder="Description"
                  formControlName="descriptionFilter"
                />
                <span class="input-group-text">
                  <img src="./assets/images/search-icon.svg" alt="Search" />
                </span>
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="locationFilter">
                  <option value="" disabled selected hidden>Select Location Detail</option>
                  <option *ngFor="let item of locationDetailsDropdown" value="{{ item.location }}">
                    {{ item.location }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <select
                  class="form-control fs12 material-input"
                  id="equipmentFilter1"
                  formControlName="locationPathFilter"
                >
                  <option value="" disabled selected hidden>Location</option>
                  <option
                    *ngFor="let item of locationDropdown"
                    value="{{ item.locationPath }}"
                    [ngValue]="item.locationPath"
                  >
                    {{ item.locationPath }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="mixDesignFilter">
                  <option value="" disabled selected hidden>Select Mix Design</option>
                  <option *ngFor="let item of mixDesignDropdown" value="{{ item.mixDesign }}">
                    {{ item.mixDesign }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <select
                  class="form-control fs12 material-input"
                  formControlName="concreteSupplierFilter"
                >
                  <option value="" disabled selected hidden>Select Concrete Supplier</option>
                  <option
                    *ngFor="let item of concreteSupplierDropdown"
                    value="{{ item.companyName }}"
                  >
                    {{ item.companyName }}
                  </option>
                </select>
              </div>
              <div class="input-group mb-3">
                <input
                  type="text"
                  class="form-control fs12 material-input"
                  placeholder="Order Number"
                  formControlName="orderNumberFilter"
                />
                <span class="input-group-text">
                  <img src="./assets/images/search-icon.svg" alt="Search" />
                </span>
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="statusFilter">
                  <option value="" disabled selected hidden>Select Status</option>
                  <option *ngFor="let item of wholeStatus" value="{{ item }}">{{ item }}</option>
                </select>
              </div>
              <div class="row justify-content-end">
                <button
                  class="btn btn-orange radius20 col-4 mt-2 fs12 fw-bold cairo-regular mx-1"
                  type="submit"
                >
                  Apply
                </button>
                <button
                  class="btn btn-orange radius20 fs12 col-4 mt-2 fw-bold cairo-regular mx-1"
                  type="button"
                  (click)="resetFilter()"
                >
                  Reset
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="filter-content text-center" *ngIf="modalLoader">Loading...</div>
    </div>
  </ng-template>
</div>
<!--Filter Modal-->
<div
  class="card calendarsetting-description p-2"
  [ngClass]="calendarDescriptionPopup == true ? 'delivery-show' : 'deliver-hide'"
>
  <span class="d-inline-flex">
    <p class="fs14 fw-bold cairo-regular color-blue1 w-75 mb-0 ps-4">
      {{ viewEventData?.description }}
    </p>
    <span class="d-inline-flex justify-content-end float-end pl100">
      <span aria-hidden="true" class="px-1 close eye-cursor" (click)="closeCalendarDescription()" (keydown)="handleDownKeydown($event, '','close')">
        <img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </span>
  </span>
  <div class="card-body p-0 delivery-details-content">
    <div class="d-flex justify-content-between">
      <p class="color-grey14 fs12 pt-2">
        <span>
          <img src="../assets/images/settings-time.svg" alt="clock-time" />
        </span>
        <strong class="ps-2">
          {{ changeFormat(viewEventData?.fromDate) }}
          <span *ngIf="!viewEventData?.isAllDay">
            {{ viewEventData?.fromDate | date : 'shortTime' }} -
            {{ viewEventData?.toDate | date : 'shortTime' }}
          </span>
          <span *ngIf="viewEventData?.isAllDay"> - All Day </span>
        </strong>
      </p>
    </div>
    <p class="color-grey14 fs12 pl28">
      <strong class="fs10">
        {{ viewEventData?.timeZoneLocation }}
      </strong>
    </p>
    <p class="color-grey14 fs12 pl28">
      <strong class="fs10">
        {{ message }}
      </strong>
    </p>
  </div>
</div>
