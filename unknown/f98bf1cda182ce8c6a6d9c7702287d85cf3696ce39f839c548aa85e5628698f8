import { Component, OnDestroy } from '@angular/core';
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import {
  debounceTime, filter, merge, Subscription, tap,
} from 'rxjs';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';
import { AuthService } from '../../../services/auth/auth.service';
import { MixpanelService } from '../../../services/mixpanel.service';

@Component({
  selector: 'app-inspection-attachments',
  templateUrl: './attachments.component.html',
})
export class InspectionAttachmentsComponent implements OnDestroy {
  public files: NgxFileDropEntry[] = [];

  public fileData: any = [];

  public todayDate: Date;

  public formData: FormData;

  public fileArray: any = [];

  public inspectionRequestId: any;

  public uploadSubmitted = false;

  public deleteUploadedFile = false;

  public ParentCompanyId: any;

  public currentUser: any = {};

  public authUser: any = {};

  public loader = false;

  private readonly subscription: Subscription = new Subscription();

  public constructor(
    private readonly toastr: ToastrService,
    public projectService: ProjectService,
    private readonly mixpanelService: MixpanelService,
    private readonly DeliveryService: DeliveryService,
    public socket: Socket,
    private readonly authService: AuthService,
  ) {
    this.todayDate = new Date();
    this.formData = new FormData();
    this.DeliveryService.InspectionRequestId.subscribe((getinspectionRequestIdResponse): void => {
      this.inspectionRequestId = getinspectionRequestIdResponse;
    });
    this.getAuthUser();
    this.DeliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
      }
    });
    const attachmentTrigger$ = merge(
      this.projectService.ParentCompanyId.pipe(
        tap((res) => {
          this.ParentCompanyId = res;
        }),
        filter((res) => !!res),
      ),
      this.DeliveryService.inspectionUpdated1,
    ).pipe(
      debounceTime(100),
      filter(() => !!this.inspectionRequestId),
    );

    this.subscription.add(
      attachmentTrigger$.subscribe(() => {
        this.getAttachements();
      }),
    );
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (type === 'remove') {
        this.removeFile(data, item);
      }
    }
  }

  public getAuthUser(): void {
    this.authService.getUser().subscribe((response: any): void => {
      this.currentUser = response;
    });
  }

  public dropped(files: NgxFileDropEntry[]): void {
    this.files = files;
    this.fileData.push(this.files);

    this.fileData.forEach((entryGroup: any[], i: number) => {
      entryGroup.forEach(
        (entry: { relativePath: string; fileEntry: FileSystemFileEntry }, index: number) => {
          const extension = this.getFileExtension(entry.relativePath);

          if (!this.isValidExtension(extension)) {
            this.toastr.error(
              'Please select a valid file. Supported file format (.jpg,.jpeg,.png,.pdf,.doc)',
              'OOPS!',
            );
            this.fileData.splice(i, 1);
            return;
          }

          if (entry.fileEntry.isFile) {
            this.processFile(entry.fileEntry, i, index, extension);
          }
        },
      );
    });
  }

  public getFileExtension(path: string): string {
    const parts = path.split('.');
    return parts[parts.length - 1].toLowerCase();
  }

  public isValidExtension(ext: string): boolean {
    return ['jpg', 'jpeg', 'png', 'pdf', 'doc'].includes(ext);
  }

  public processFile(
    fileEntry: FileSystemFileEntry,
    groupIndex: number,
    fileIndex: number,
    extension: string,
  ): void {
    fileEntry.file((file: File) => {
      const filesizeMB = +((file.size / 1_000_000).toFixed(4));

      if (filesizeMB > 2) {
        this.toastr.error('Please choose a attachment less than or equal to 2MB');
        this.fileData.splice(groupIndex, 1);
        return;
      }

      const reader = new FileReader();
      reader.onload = () => {
        if (this.fileData[groupIndex]) {
          this.fileData[groupIndex][fileIndex].image = reader.result;
        }
      };

      if (this.fileData[groupIndex]) {
        this.fileData[groupIndex][fileIndex].extension = extension;
        reader.readAsDataURL(file);
      }
    });
  }


  public removeExistingFile(item: { id: any }): void {
    this.loader = true;
    this.deleteUploadedFile = true;
    const params = {
      id: item.id,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.DeliveryService.removeInspectionAttachement(params).subscribe({
      next: (res): void => {
        if (res) {
          this.fileData = [];
          this.toastr.success(res.message, 'Success');
          this.mixpanelService.addMixpanelEvents('Attachment deleted against a inspection Booking ');
          this.deleteUploadedFile = false;
          this.DeliveryService.updatedInspectionHistory1({ status: true }, 'AttachmentDeleteHistory');
          this.socket.emit('InspectionAttachmentHistory', res);
        }
      },
      error: (removeExistingFileError): void => {
        if (removeExistingFileError.message?.statusCode === 400) {
          this.showError(removeExistingFileError);
        } else if (!removeExistingFileError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(removeExistingFileError.message, 'OOPS!');
        }
      },
    });
    this.loader = false;
  }

  public clickAndDisable(link: any): void {
    const getData = link;
    // disable subsequent clicks
    getData.onclick = (event: { preventDefault: () => void }): void => {
      event.preventDefault();
    };
  }

  public uploadData(): void {
    this.uploadSubmitted = true;
    const hasFormData = (formData: FormData): boolean => {
      let hasData = false;

      formData.forEach(() => {
        hasData = true;
      });

      return hasData;
    };
    if (hasFormData(this.formData)) {
      const params = {
        InspectionRequestId: this.inspectionRequestId,
        ParentCompanyId: this.ParentCompanyId,
      };

      this.DeliveryService.inspectionAttachement(params, this.formData).subscribe({
        next: (res): void => {
          this.fileData = [];
          this.uploadSubmitted = false;
          this.toastr.success(res.message, 'SUCCESS!');
          this.mixpanelService.addMixpanelEvents('Attachment added against a inspection Booking');
          this.socket.emit('InspectionAttachmentHistory', res);
          this.DeliveryService.updatedInspectionHistory1({ status: true }, 'NDRApproveHistory');
          this.getAttachements();
        },
        error: (attachmentErr): void => {
          this.uploadSubmitted = false;
          if (attachmentErr.message?.statusCode === 400) {
            this.showError(attachmentErr);
          } else {
            this.toastr.error(attachmentErr.message, 'OOPS!');
          }
        },
      });
    }
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }

  public removeFile(firstIndex: string | number, i: string | number): void {
    this.formData.delete(this.fileData[firstIndex][i].relativePath);
    this.fileData[firstIndex].splice(i);
    this.fileData.splice(firstIndex);
  }

  public getAttachements(): void {
    const params = {
      InspectionRequestId: this.inspectionRequestId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.DeliveryService.getInspectionAttachement(params).subscribe((res): void => {
      this.fileArray = res.data;
    });
  }

  public fileOver(_event: any): void { /* */ }

  public fileLeave(_event: any): void { /* */ }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }
}
