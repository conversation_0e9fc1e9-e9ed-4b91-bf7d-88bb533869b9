import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { SocketIoModule, SocketIoConfig, Socket } from 'ngx-socket-io';
import { NgxFileDropModule, NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { of, throwError } from 'rxjs';
import { CraneRequestAttachmentComponent } from './crane-request-attachment.component';
import { environment } from '../../../environments/environment';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';
import { AuthService } from '../../services/auth/auth.service';
import { MixpanelService } from '../../services/mixpanel.service';

const config: SocketIoConfig = { url: environment.apiSocketUrl, options: {} };

describe('CraneRequestAttachmentComponent', () => {
  let component: CraneRequestAttachmentComponent;
  let fixture: ComponentFixture<CraneRequestAttachmentComponent>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let projectService: jest.Mocked<ProjectService>;
  let authService: jest.Mocked<AuthService>;
  let toastrService: jest.Mocked<ToastrService>;
  let socket: jest.Mocked<Socket>;
  let mixpanelService: jest.Mocked<MixpanelService>;

  const mockUser = { id: 1, name: 'Test User' };

  const mockDeliveryService = {
    EditCraneRequestId: of('123'),
    loginUser: of({ RoleId: 1 }),
    removeCraneRequestAttachement: jest.fn(),
    addCraneRequestAttachment: jest.fn(),
    getCraneRequestAttachements: jest.fn(),
    updateCraneRequestHistory1: jest.fn(),
    fetchData: of({}),
    fetchData1: of({}),
  };

  const mockProjectService = {
    ParentCompanyId: of('456'),
    projectParent: of({ ProjectId: '789', ParentCompanyId: '456' }),
  };

  const mockAuthService = {
    getUser: jest.fn().mockReturnValue(of(mockUser)),
  };

  const mockToastrService = {
    success: jest.fn(),
    error: jest.fn(),
  };

  const mockSocket = {
    emit: jest.fn(),
    on: jest.fn(),
  };

  const mockMixpanelService = {
    addMixpanelEvents: jest.fn(),
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [CraneRequestAttachmentComponent],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        HttpClientTestingModule,
        RouterTestingModule,
        ToastrModule.forRoot(),
        SocketIoModule.forRoot(config),
        NgxFileDropModule,
      ],
      providers: [
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: ProjectService, useValue: mockProjectService },
        { provide: AuthService, useValue: mockAuthService },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: Socket, useValue: mockSocket },
        { provide: MixpanelService, useValue: mockMixpanelService },
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();

    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    authService = TestBed.inject(AuthService) as jest.Mocked<AuthService>;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    socket = TestBed.inject(Socket) as jest.Mocked<Socket>;
    mixpanelService = TestBed.inject(MixpanelService) as jest.Mocked<MixpanelService>;

    fixture = TestBed.createComponent(CraneRequestAttachmentComponent);
    component = fixture.componentInstance;

    // Clear all mock calls before each test
    jest.clearAllMocks();

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.files).toEqual([]);
    expect(component.fileData).toEqual([]);
    expect(component.formData).toBeInstanceOf(FormData);
    expect(component.uploadSubmitted).toBeFalsy();
    expect(component.deleteUploadedFile).toBeFalsy();
    expect(component.loader).toBeFalsy();
  });

  it('should get auth user on init', () => {
    component.getAuthUser();
    expect(component.currentUser).toEqual(mockUser);
  });

  it('should handle file drop with valid file', async () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile),
      isFile: true,
    } as FileSystemFileEntry;

    const mockDropEntry: NgxFileDropEntry = {
      fileEntry: mockFileEntry,
      relativePath: 'test.jpg',
    };

    component.dropped([mockDropEntry]);
    await fixture.whenStable();

    expect(component.files).toEqual([mockDropEntry]);
    expect(component.fileData.length).toBeGreaterThan(0);
  });

  // it('should reject invalid file extensions', async () => {
  //   const mockFile = new File(['test'], 'test.exe', { type: 'application/exe' });
  //   const mockFileEntry = {
  //     file: (callback: (file: File) => void) => callback(mockFile),
  //     isFile: true,
  //   } as FileSystemFileEntry;

  //   const mockDropEntry: NgxFileDropEntry = {
  //     fileEntry: mockFileEntry,
  //     relativePath: 'test.exe',
  //   };

  //   component.dropped([mockDropEntry]);
  //   await fixture.whenStable();

  //   expect(toastrService.error).toHaveBeenCalledWith(
  //     'Please select a valid file. Supported file format (.jpg,.jpeg,.png,.pdf,.doc)',
  //     'OOPS!',
  //   );
  // });

  it('should handle file removal', () => {
    const mockFile = { id: '123' };
    mockDeliveryService.removeCraneRequestAttachement.mockReturnValue(of({ message: 'Success' }));

    component.removeExistingFile(mockFile);

    expect(mockDeliveryService.removeCraneRequestAttachement).toHaveBeenCalled();
    expect(toastrService.success).toHaveBeenCalledWith('Success', 'Success');
    expect(mockMixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Attachment deleted against a Crane Booking');
  });

  it('should handle file upload', () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    component.formData.append('file', mockFile);
    mockDeliveryService.addCraneRequestAttachment.mockReturnValue(of({ message: 'Uploaded Successfully.' }));

    component.uploadData();

    expect(mockDeliveryService.addCraneRequestAttachment).toHaveBeenCalled();
    expect(toastrService.success).toHaveBeenCalledWith('Uploaded Successfully.', 'SUCCESS!');
    expect(mockMixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Attachment added against a Crane Booking');
  });

  it('should handle file upload error', () => {
    const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    component.formData.append('file', mockFile);
    mockDeliveryService.addCraneRequestAttachment.mockReturnValue(
      throwError(() => new Error('Upload failed'))
    );

    component.uploadData();

    expect(mockDeliveryService.addCraneRequestAttachment).toHaveBeenCalled();
    expect(toastrService.error).toHaveBeenCalled();
  });

  it('should get attachments on init', () => {
    const mockAttachments = { data: [{ id: 1, filename: 'test.jpg' }] };
    mockDeliveryService.getCraneRequestAttachements.mockReturnValue(of(mockAttachments));

    component.ngOnInit();

    expect(mockDeliveryService.getCraneRequestAttachements).toHaveBeenCalled();
    expect(component.fileArray).toEqual(mockAttachments.data);
  });

  it('should unsubscribe on destroy', () => {
    const unsubscribeSpy = jest.spyOn(component.subscription, 'unsubscribe');
    component.ngOnDestroy();
    expect(unsubscribeSpy).toHaveBeenCalled();
  });

  it('should handle file drop events', () => {
    const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });
    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile),
      isFile: true,
      isDirectory: false
    } as FileSystemFileEntry;

    const mockDropEntry: NgxFileDropEntry[] = [{
      relativePath: 'test.jpg',
      fileEntry: mockFileEntry
    }];

    component.dropped(mockDropEntry);

    // Verify files array is updated
    expect(component.files).toEqual(mockDropEntry);

    // Verify fileData array is updated
    expect(component.fileData.length).toBeGreaterThan(0);

    // Since the component doesn't directly add to formData in the dropped method,
    // we shouldn't check formData.has('file') here
  });

  it('should handle fileOver event', () => {
    const event = {};
    component.fileOver(event);
    // Just verifying the method can be called without errors
    expect(component).toBeTruthy();
  });

  it('should handle fileLeave event', () => {
    const event = {};
    component.fileLeave(event);
    // Just verifying the method can be called without errors
    expect(component).toBeTruthy();
  });

  // ===== ADDITIONAL COMPREHENSIVE TEST CASES =====

  describe('File Extension Validation', () => {
    it('should validate all supported file extensions', () => {
      const validExtensions = ['jpg', 'jpeg', 'png', 'pdf', 'doc'];
      validExtensions.forEach(ext => {
        expect(component.isValidExtension(ext)).toBeTruthy();
      });
    });

    it('should reject invalid file extensions', () => {
      const invalidExtensions = ['exe', 'txt', 'zip', 'rar', 'mp4', 'docx', 'xlsx'];
      invalidExtensions.forEach(ext => {
        expect(component.isValidExtension(ext)).toBeFalsy();
      });
    });

    it('should extract file extension correctly', () => {
      expect(component.getFileExtension('test.jpg')).toBe('jpg');
      expect(component.getFileExtension('document.PDF')).toBe('pdf');
      expect(component.getFileExtension('file.name.with.dots.png')).toBe('png');
      expect(component.getFileExtension('noextension')).toBe('noextension');
    });
  });

  describe('File Drop Validation - Negative Cases', () => {
    it('should reject files with invalid extensions and show error', async () => {
      const mockFile = new File(['test'], 'test.exe', { type: 'application/exe' });
      const mockFileEntry = {
        file: (callback: (file: File) => void) => callback(mockFile),
        isFile: true,
      } as FileSystemFileEntry;

      const mockDropEntry: NgxFileDropEntry = {
        fileEntry: mockFileEntry,
        relativePath: 'test.exe',
      };

      component.dropped([mockDropEntry]);
      await fixture.whenStable();

      expect(toastrService.error).toHaveBeenCalledWith(
        'Please select a valid file. Supported file format (.jpg,.jpeg,.png,.pdf,.doc)',
        'OOPS!',
      );
    });

    it('should handle directory entries gracefully', async () => {
      const mockDirectoryEntry = {
        isFile: false,
        isDirectory: true,
      } as any;

      const mockDropEntry: NgxFileDropEntry = {
        fileEntry: mockDirectoryEntry,
        relativePath: 'folder/',
      };

      component.dropped([mockDropEntry]);
      await fixture.whenStable();

      // Directory entries with invalid extensions (like '/') get removed from fileData
      // The component treats 'folder/' as having extension '/' which is invalid
      expect(component.fileData.length).toBe(0);
      expect(toastrService.error).toHaveBeenCalledWith(
        'Please select a valid file. Supported file format (.jpg,.jpeg,.png,.pdf,.doc)',
        'OOPS!',
      );
    });
  });

  describe('File Size Validation', () => {
    it('should accept files under 2MB', async () => {
      const smallFileSize = 1 * 1000 * 1000; // 1MB
      const mockFile = new File(['x'.repeat(smallFileSize)], 'test.jpg', { type: 'image/jpeg' });

      const mockFileEntry = {
        file: (callback: (file: File) => void) => callback(mockFile),
        isFile: true,
      } as FileSystemFileEntry;

      component.fileData = [[{ relativePath: 'test.jpg' }]];
      component.processFile(mockFileEntry, 0, 0, 'jpg');

      await fixture.whenStable();
      expect(toastrService.error).not.toHaveBeenCalledWith('Please choose a attachment less than or equal to 2MB');
    });

    it('should reject files over 2MB and show error', async () => {
      const largeFileSize = 3 * 1000 * 1000; // 3MB
      const mockFile = new File(['x'.repeat(largeFileSize)], 'large.jpg', { type: 'image/jpeg' });

      const mockFileEntry = {
        file: (callback: (file: File) => void) => callback(mockFile),
        isFile: true,
      } as FileSystemFileEntry;

      component.fileData = [[{ relativePath: 'large.jpg' }]];

      component.processFile(mockFileEntry, 0, 0, 'jpg');

      await fixture.whenStable();
      expect(toastrService.error).toHaveBeenCalledWith('Please choose a attachment less than or equal to 2MB');
    });

    it('should handle exactly 2MB files', async () => {
      // Clear any previous calls
      jest.clearAllMocks();

      const exactFileSize = 2 * 1000 * 1000; // Exactly 2MB
      const mockFile = new File(['x'.repeat(exactFileSize)], 'exact.jpg', { type: 'image/jpeg' });

      const mockFileEntry = {
        file: (callback: (file: File) => void) => callback(mockFile),
        isFile: true,
      } as FileSystemFileEntry;

      component.fileData = [[{ relativePath: 'exact.jpg' }]];
      component.processFile(mockFileEntry, 0, 0, 'jpg');

      await fixture.whenStable();
      expect(toastrService.error).not.toHaveBeenCalledWith('Please choose a attachment less than or equal to 2MB');
    });
  });

  describe('Multiple File Handling', () => {
    it('should handle multiple files dropped at once', async () => {
      const mockFile1 = new File(['test1'], 'test1.jpg', { type: 'image/jpeg' });
      const mockFile2 = new File(['test2'], 'test2.png', { type: 'image/png' });

      const mockFileEntry1 = {
        file: (callback: (file: File) => void) => callback(mockFile1),
        isFile: true,
      } as FileSystemFileEntry;

      const mockFileEntry2 = {
        file: (callback: (file: File) => void) => callback(mockFile2),
        isFile: true,
      } as FileSystemFileEntry;

      const mockDropEntries: NgxFileDropEntry[] = [
        { fileEntry: mockFileEntry1, relativePath: 'test1.jpg' },
        { fileEntry: mockFileEntry2, relativePath: 'test2.png' }
      ];

      component.dropped(mockDropEntries);
      await fixture.whenStable();

      expect(component.files).toEqual(mockDropEntries);
      expect(component.fileData.length).toBeGreaterThan(0);
    });

    it('should handle mixed valid and invalid files', async () => {
      const validFile = new File(['valid'], 'valid.jpg', { type: 'image/jpeg' });
      const invalidFile = new File(['invalid'], 'invalid.exe', { type: 'application/exe' });

      const validFileEntry = {
        file: (callback: (file: File) => void) => callback(validFile),
        isFile: true,
      } as FileSystemFileEntry;

      const invalidFileEntry = {
        file: (callback: (file: File) => void) => callback(invalidFile),
        isFile: true,
      } as FileSystemFileEntry;

      const mockDropEntries: NgxFileDropEntry[] = [
        { fileEntry: validFileEntry, relativePath: 'valid.jpg' },
        { fileEntry: invalidFileEntry, relativePath: 'invalid.exe' }
      ];

      component.dropped(mockDropEntries);
      await fixture.whenStable();

      expect(toastrService.error).toHaveBeenCalledWith(
        'Please select a valid file. Supported file format (.jpg,.jpeg,.png,.pdf,.doc)',
        'OOPS!',
      );
    });
  });

  describe('File Removal', () => {
    beforeEach(() => {
      component.fileData = [
        [
          { relativePath: 'file1.jpg', extension: 'jpg' },
          { relativePath: 'file2.png', extension: 'png' }
        ],
        [
          { relativePath: 'file3.pdf', extension: 'pdf' }
        ]
      ];
      component.formData = new FormData();
      component.formData.append('file1.jpg', new File(['test'], 'file1.jpg'));
      component.formData.append('file2.png', new File(['test'], 'file2.png'));
    });

    it('should remove specific file from fileData and formData', () => {
      component.removeFile(0, 1); // Remove second file from first group

      expect(component.fileData[0].length).toBe(1);
      expect(component.fileData[0][0].relativePath).toBe('file1.jpg');
    });

    it('should remove entire group when last file is removed', () => {
      component.removeFile(1, 0); // Remove only file from second group

      expect(component.fileData.length).toBe(1);
      expect(component.fileData[0].length).toBe(2);
    });

    it('should handle removal of non-existent file gracefully', () => {
      const initialLength = component.fileData.length;
      component.removeFile(99, 99); // Non-existent indices

      expect(component.fileData.length).toBe(initialLength);
    });

    it('should handle removal when fileData is empty', () => {
      component.fileData = [];
      expect(() => component.removeFile(0, 0)).not.toThrow();
    });
  });

  describe('Keyboard Navigation', () => {
    it('should handle Enter key for file removal', () => {
      const removeFileSpy = jest.spyOn(component, 'removeFile');
      const mockEvent = new KeyboardEvent('keydown', { key: 'Enter' });
      const preventDefaultSpy = jest.spyOn(mockEvent, 'preventDefault');

      component.handleToggleKeydown(mockEvent, 0, 1);

      expect(preventDefaultSpy).toHaveBeenCalled();
      expect(removeFileSpy).toHaveBeenCalledWith(0, 1);
    });

    it('should handle Space key for file removal', () => {
      const removeFileSpy = jest.spyOn(component, 'removeFile');
      const mockEvent = new KeyboardEvent('keydown', { key: ' ' });
      const preventDefaultSpy = jest.spyOn(mockEvent, 'preventDefault');

      component.handleToggleKeydown(mockEvent, 0, 1);

      expect(preventDefaultSpy).toHaveBeenCalled();
      expect(removeFileSpy).toHaveBeenCalledWith(0, 1);
    });

    it('should ignore other keys', () => {
      const removeFileSpy = jest.spyOn(component, 'removeFile');
      const mockEvent = new KeyboardEvent('keydown', { key: 'Tab' });
      const preventDefaultSpy = jest.spyOn(mockEvent, 'preventDefault');

      component.handleToggleKeydown(mockEvent, 0, 1);

      expect(preventDefaultSpy).not.toHaveBeenCalled();
      expect(removeFileSpy).not.toHaveBeenCalled();
    });
  });

  describe('API Error Handling', () => {
    it('should handle removeExistingFile with 400 error', () => {
      const mockFile = { id: '123' };
      const mockError = {
        message: {
          statusCode: 400,
          details: [{ field: 'Invalid request' }]
        }
      };

      mockDeliveryService.removeCraneRequestAttachement.mockReturnValue(throwError(() => mockError));
      const showErrorSpy = jest.spyOn(component, 'showError');

      component.removeExistingFile(mockFile);

      expect(showErrorSpy).toHaveBeenCalledWith(mockError);
    });

    it('should handle removeExistingFile with network error', () => {
      const mockFile = { id: '123' };
      const mockError = { message: null };

      mockDeliveryService.removeCraneRequestAttachement.mockReturnValue(throwError(() => mockError));

      component.removeExistingFile(mockFile);

      expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should handle removeExistingFile with generic error', () => {
      const mockFile = { id: '123' };
      const mockError = { message: 'Generic error message' };

      mockDeliveryService.removeCraneRequestAttachement.mockReturnValue(throwError(() => mockError));

      component.removeExistingFile(mockFile);

      expect(toastrService.error).toHaveBeenCalledWith('Generic error message', 'OOPS!');
    });

    it('should handle uploadData with 400 error', () => {
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      component.formData.append('file', mockFile);

      const mockError = {
        message: {
          statusCode: 400,
          details: [{ field: 'Invalid upload' }]
        }
      };

      mockDeliveryService.addCraneRequestAttachment.mockReturnValue(throwError(() => mockError));
      const showErrorSpy = jest.spyOn(component, 'showError');

      component.uploadData();

      expect(showErrorSpy).toHaveBeenCalledWith(mockError);
      expect(component.uploadSubmitted).toBeFalsy();
    });

    it('should handle uploadData with generic error', () => {
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      component.formData.append('file', mockFile);

      const mockError = { message: 'Upload failed' };

      mockDeliveryService.addCraneRequestAttachment.mockReturnValue(throwError(() => mockError));

      component.uploadData();

      expect(toastrService.error).toHaveBeenCalledWith('Upload failed', 'OOPS!');
      expect(component.uploadSubmitted).toBeFalsy();
    });
  });

  describe('Upload Data Validation', () => {
    it('should not upload when formData is empty', () => {
      // Clear any previous calls
      jest.clearAllMocks();

      component.formData = new FormData(); // Empty FormData

      component.uploadData();

      expect(mockDeliveryService.addCraneRequestAttachment).not.toHaveBeenCalled();
      expect(component.uploadSubmitted).toBeTruthy(); // Still sets to true initially
    });

    it('should upload when formData has files', () => {
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      component.formData.append('file', mockFile);
      mockDeliveryService.addCraneRequestAttachment.mockReturnValue(of({ message: 'Success' }));

      component.uploadData();

      expect(mockDeliveryService.addCraneRequestAttachment).toHaveBeenCalled();
      expect(component.uploadSubmitted).toBeFalsy(); // Reset after success
    });

    it('should set correct parameters for upload', () => {
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      component.formData.append('file', mockFile);
      component.CraneRequestId = 'crane123';
      component.ParentCompanyId = 'company456';
      component.ProjectId = 'project789';

      mockDeliveryService.addCraneRequestAttachment.mockReturnValue(of({ message: 'Success' }));

      component.uploadData();

      expect(mockDeliveryService.addCraneRequestAttachment).toHaveBeenCalledWith(
        {
          CraneRequestId: 'crane123',
          ParentCompanyId: 'company456',
          ProjectId: 'project789'
        },
        component.formData
      );
    });
  });

  describe('Error Display', () => {

    it('should handle error with multiple details', () => {
      const mockError = {
        message: {
          details: [{ field1: 'Error 1', field2: 'Error 2' }]
        }
      };

      component.showError(mockError);

      expect(toastrService.error).toHaveBeenCalled();
    });
  });

  describe('Socket and Service Integration', () => {
    it('should emit socket events on successful file removal', () => {
      const mockFile = { id: '123' };
      const mockResponse = { message: 'Success', data: 'test' };
      mockDeliveryService.removeCraneRequestAttachement.mockReturnValue(of(mockResponse));

      component.removeExistingFile(mockFile);

      expect(mockSocket.emit).toHaveBeenCalledWith('CraneAttachmentDeleteHistory', mockResponse);
      expect(mockDeliveryService.updateCraneRequestHistory1).toHaveBeenCalledWith(
        { status: true },
        'CraneAttachmentDeleteHistory'
      );
    });

    it('should emit socket events on successful file upload', () => {
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      component.formData.append('file', mockFile);
      const mockResponse = { message: 'Success', data: 'test' };
      mockDeliveryService.addCraneRequestAttachment.mockReturnValue(of(mockResponse));

      component.uploadData();

      expect(mockSocket.emit).toHaveBeenCalledWith('CraneApproveHistory', mockResponse);
      expect(mockDeliveryService.updateCraneRequestHistory1).toHaveBeenCalledWith(
        { status: true },
        'CraneApproveHistory'
      );
    });

    it('should handle getAttachements API call', () => {
      const mockAttachments = { data: [{ id: 1, filename: 'test.jpg' }] };
      mockDeliveryService.getCraneRequestAttachements.mockReturnValue(of(mockAttachments));

      component.CraneRequestId = 'crane123';
      component.ParentCompanyId = 'company456';
      component.ProjectId = 'project789';

      component.getAttachements();

      expect(mockDeliveryService.getCraneRequestAttachements).toHaveBeenCalledWith({
        CraneRequestId: 'crane123',
        ParentCompanyId: 'company456',
        ProjectId: 'project789'
      });
      expect(component.fileArray).toEqual(mockAttachments.data);
    });
  });

  describe('Component Lifecycle and Subscriptions', () => {
    it('should handle projectParent subscription with valid data', () => {
      // Clear any previous calls
      jest.clearAllMocks();

      const mockProjectData = { ProjectId: 'proj123', ParentCompanyId: 'comp456' };
      const getAttachementsSpy = jest.spyOn(component, 'getAttachements').mockImplementation(() => {});

      // Mock the getCraneRequestAttachements to return a proper observable
      mockDeliveryService.getCraneRequestAttachements.mockReturnValue(of({ data: [] }));

      // Simulate the subscription
      mockProjectService.projectParent = of(mockProjectData);
      component.ngOnInit();

      expect(component.ProjectId).toBe('proj123');
      expect(component.ParentCompanyId).toBe('comp456');
      expect(getAttachementsSpy).toHaveBeenCalled();
    });

    it('should handle attachment trigger subscription', () => {
      // Clear any previous calls
      jest.clearAllMocks();

      const getAttachementsSpy = jest.spyOn(component, 'getAttachements').mockImplementation(() => {});
      component.CraneRequestId = 'crane123';

      // Trigger the merged observable
      mockDeliveryService.fetchData = of({ trigger: true });
      component.ngOnInit();

      expect(getAttachementsSpy).toHaveBeenCalled();
    });
  });

  describe('Edge Cases and Boundary Conditions', () => {

    it('should handle processFile when fileData group does not exist', async () => {
      // Clear any previous calls
      jest.clearAllMocks();

      // Mock the getCraneRequestAttachements to return a proper observable
      mockDeliveryService.getCraneRequestAttachements.mockReturnValue(of({ data: [] }));

      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      const mockFileEntry = {
        file: (callback: (file: File) => void) => callback(mockFile),
        isFile: true,
      } as FileSystemFileEntry;

      // Don't initialize fileData for the group
      component.fileData = [];

      expect(() => {
        component.processFile(mockFileEntry, 0, 0, 'jpg');
      }).not.toThrow();
    });

    it('should handle clickAndDisable method', () => {
      const mockLink = {
        onclick: null
      };

      component.clickAndDisable(mockLink);

      expect(mockLink.onclick).toBeDefined();

      // Test the onclick function
      const mockEvent = { preventDefault: jest.fn() };
      mockLink.onclick(mockEvent);
      expect(mockEvent.preventDefault).toHaveBeenCalled();
    });

    it('should handle removeExistingFile with falsy response', () => {
      // Clear any previous calls
      jest.clearAllMocks();

      const mockFile = { id: '123' };
      mockDeliveryService.removeCraneRequestAttachement.mockReturnValue(of(null));

      component.removeExistingFile(mockFile);

      expect(component.loader).toBeFalsy();
      expect(toastrService.success).not.toHaveBeenCalled();
    });

    it('should initialize with correct default values', () => {
      const newComponent = new CraneRequestAttachmentComponent(
        toastrService,
        projectService,
        mixpanelService,
        deliveryService,
        socket,
        authService
      );

      expect(newComponent.files).toEqual([]);
      expect(newComponent.fileData).toEqual([]);
      expect(newComponent.formData).toBeInstanceOf(FormData);
      expect(newComponent.uploadSubmitted).toBeFalsy();
      expect(newComponent.deleteUploadedFile).toBeFalsy();
      expect(newComponent.loader).toBeFalsy();
      expect(newComponent.todayDate).toBeInstanceOf(Date);
    });

    it('should handle file extension edge cases', () => {
      expect(component.getFileExtension('')).toBe('');
      expect(component.getFileExtension('.')).toBe('');
      expect(component.getFileExtension('file.')).toBe('');
      expect(component.getFileExtension('.hidden')).toBe('hidden');
    });

    it('should handle case sensitivity in file extensions', () => {
      expect(component.getFileExtension('test.JPG')).toBe('jpg');
      expect(component.getFileExtension('test.PDF')).toBe('pdf');
      expect(component.getFileExtension('test.DOC')).toBe('doc');
      expect(component.getFileExtension('test.JPEG')).toBe('jpeg');
      expect(component.getFileExtension('test.PNG')).toBe('png');
    });

    it('should handle very large file names', () => {
      const longFileName = 'a'.repeat(1000) + '.jpg';
      expect(component.getFileExtension(longFileName)).toBe('jpg');
      expect(component.isValidExtension(component.getFileExtension(longFileName))).toBeTruthy();
    });

    it('should handle files with multiple extensions', () => {
      expect(component.getFileExtension('archive.tar.gz')).toBe('gz');
      expect(component.getFileExtension('backup.sql.zip')).toBe('zip');
      expect(component.getFileExtension('image.backup.jpg')).toBe('jpg');
    });
  });

  describe('Performance and Memory', () => {
    it('should properly clean up subscriptions on destroy', () => {
      const subscription = component.subscription;
      const unsubscribeSpy = jest.spyOn(subscription, 'unsubscribe');

      component.ngOnDestroy();

      expect(unsubscribeSpy).toHaveBeenCalled();
    });

    it('should handle multiple rapid file drops', async () => {
      const createMockFile = (name: string) => new File(['test'], name, { type: 'image/jpeg' });
      const createMockEntry = (file: File): NgxFileDropEntry => ({
        fileEntry: {
          file: (callback: (file: File) => void) => callback(file),
          isFile: true,
        } as FileSystemFileEntry,
        relativePath: file.name
      });

      const files = Array.from({ length: 10 }, (_, i) => createMockFile(`test${i}.jpg`));
      const entries = files.map(createMockEntry);

      // Drop files rapidly
      for (const entry of entries) {
        component.dropped([entry]);
      }

      await fixture.whenStable();

      expect(component.fileData.length).toBe(10);
    });

    it('should handle FormData operations efficiently', () => {
      const formData = new FormData();

      // Add multiple files
      for (let i = 0; i < 100; i++) {
        formData.append(`file${i}`, new File(['test'], `test${i}.jpg`));
      }

      component.formData = formData;

      // Test the hasFormData function indirectly through uploadData
      mockDeliveryService.addCraneRequestAttachment.mockReturnValue(of({ message: 'Success' }));

      component.uploadData();

      expect(mockDeliveryService.addCraneRequestAttachment).toHaveBeenCalled();
    });
  });
});
