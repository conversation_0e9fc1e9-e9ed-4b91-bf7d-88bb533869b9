import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';

@Component({
  selector: 'app-inspection-details',
  templateUrl: './details.component.html',
})
export class InspectionDetailsComponent implements OnInit, OnDestroy {
  public NDRData: any = [];

  public inspectionRequestId: any;

  public loader = true;

  public ParentCompanyId: any;

  public toolTipContent = '';

  private readonly subscriptions: Subscription = new Subscription();


  public constructor(private readonly DeliveryService: DeliveryService,
    public projectService: ProjectService) {
    this.projectService.projectParent.subscribe((response3): void => {
      if (response3 !== undefined && response3 !== null && response3 !== '') {
        this.ParentCompanyId = response3.ParentCompanyId;
      }
    });
  }


  ngOnInit(): void {
    const sub1 = this.DeliveryService.InspectionRequestId.subscribe((getinspectionRequestIdResponse3): void => {
      if (getinspectionRequestIdResponse3 !== undefined && getinspectionRequestIdResponse3 !== null && getinspectionRequestIdResponse3 !== '') {
        this.inspectionRequestId = getinspectionRequestIdResponse3;
        this.getNDR();
      }
    });

    const sub2 = this.DeliveryService.inspectionUpdated.subscribe((getNdrResponses4): void => {
      if (getNdrResponses4 !== undefined && getNdrResponses4 !== null && getNdrResponses4 !== '') {
        this.loader = true;
        this.getNDR();
      }
    });

    const sub3 = this.DeliveryService.inspectionUpdated1.subscribe((getNdrResponses4): void => {
      this.loader = true;
      if (this.inspectionRequestId !== null && this.inspectionRequestId !== undefined && this.inspectionRequestId !== '') {
        this.getNDR();
      }
    });

    this.subscriptions.add(sub1);
    this.subscriptions.add(sub2);
    this.subscriptions.add(sub3);
  }

  public getResponsiblePeople(object: { firstName: any; lastName: any }): any {
    if (object?.firstName && object?.lastName) {
      const string = `${object.firstName} ${object.lastName}`;
      const matches = string.match(/\b(\w)/g);
      const acronym = matches.join('').toUpperCase();
      return acronym;
    }
    return 'UU';
  }

  public getNDR(): void {
    this.toolTipContent = '';
    const param = {
      inspectionRequestId: this.inspectionRequestId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.DeliveryService.getInspectionNDRData(param).subscribe((res): void => {
      this.NDRData = res.data;
      if(this.NDRData.equipmentDetails.length && !this.NDRData.equipmentDetails[0].Equipment){
        this.NDRData.equipmentDetails[0].Equipment = { id: 0, equipmentName: 'No Equipment Needed' }
      }
      this.loader = false;
      if (this.NDRData.memberDetails.length > 3) {
        const slicedArray = this.NDRData.memberDetails.slice(3);
        slicedArray.map((a): any => {
          if (a.Member.User.firstName) {
            this.toolTipContent += `${a.Member.User.firstName} ${a.Member.User.lastName}, `;
          } else {
            this.toolTipContent += `${a.Member.User.email}, `;
          }
        });
      }
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
}
