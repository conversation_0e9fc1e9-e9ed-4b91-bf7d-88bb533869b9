﻿import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, UntypedFormBuilder } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Socket } from 'ngx-socket-io';
import { of, throwError } from 'rxjs';
import { NO_ERRORS_SCHEMA, Component, Directive, Input, Output, EventEmitter, TemplateRef } from '@angular/core';

import { EditinspectionFormComponent } from './edit-inspection-form.component';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';
import { MixpanelService } from '../../../services/mixpanel.service';
import { ProjectSettingsService } from '../../../services/project_settings/project-settings.service';

// Mock ngx-places directive
@Directive({
  selector: '[ngx-places]'
})
class MockNgxPlacesDirective {
  @Input() options: any;
  @Output() onAddressChange = new EventEmitter<any>();
}

// Mock ng-multiselect-dropdown component
@Component({
  selector: 'ng-multiselect-dropdown',
  template: '<div></div>'
})
class MockMultiselectDropdownComponent {
  @Input() data: any;
  @Input() settings: any;
  @Input() placeholder: string;
  @Output() onSelect = new EventEmitter<any>();
  @Output() onDeSelect = new EventEmitter<any>();
}

// Mock ng-autocomplete component
@Component({
  selector: 'ng-autocomplete',
  template: '<div></div>'
})
class MockAutocompleteComponent {
  @Input() data: any;
  @Input() searchKeyword: string;
  @Input() placeholder: string;
  @Input() historyIdentifier: string;
  @Input() historyHeading: string;
  @Input() notFoundText: string;
  @Input() isLoading: boolean;
  @Input() debounceTime: number;
  @Input() disabled: boolean;
  @Output() selected = new EventEmitter<any>();
  @Output() inputChanged = new EventEmitter<any>();
  @Output() inputFocused = new EventEmitter<any>();
  @Output() inputCleared = new EventEmitter<any>();
}

// Mock bs-datepicker directive
@Directive({
  selector: '[bsDatepicker]'
})
class MockBsDatepickerDirective {
  @Input() bsConfig: any;
  @Input() minDate: any;
  @Input() maxDate: any;
  @Input() isDisabled: boolean;
}

// Mock timepicker component
@Component({
  selector: 'timepicker',
  template: '<div></div>'
})
class MockTimepickerComponent {
  @Input() ngModel: any;
  @Input() hourStep: number;
  @Input() minuteStep: number;
  @Input() showMeridian: boolean;
  @Input() readonlyInput: boolean;
  @Input() mousewheel: boolean;
  @Input() arrowkeys: boolean;
  @Input() showSpinners: boolean;
  @Input() min: any;
  @Input() max: any;
  @Output() ngModelChange = new EventEmitter<any>();
}

// Mock services
const mockDeliveryService = {
  getInspectionNDRData: jest.fn().mockReturnValue(of({ data: {} })),
  getMemberRole: jest.fn().mockReturnValue(of({ data: {} })),
  getAvailableTimeSlots: jest.fn().mockReturnValue(of({ slots: [] })),
  editInspectionNDR: jest.fn().mockReturnValue(of({})),
  editQueuedInspectionNDRForm: jest.fn().mockReturnValue(of({})),
  submitQueuedNDR: jest.fn().mockReturnValue(of({})),
  searchNewMember: jest.fn().mockReturnValue(of([])),
  projectParent: of({}),
  accountProjectParent: of({}),
  InspectionRequestId: of('test-id'),
  loginUser: of({}),
  isQueuedInspectionNDR: of(''),
  updatedInspectionHistory: jest.fn(),
  updateCraneRequestHistory: jest.fn()
};

const mockProjectService = {
  gateList: jest.fn().mockReturnValue(of({ data: [] })),
  listAllMember: jest.fn().mockReturnValue(of({ data: [] })),
  getLastCraneRequestId: jest.fn().mockReturnValue(of({ lastId: {} })),
  listEquipment: jest.fn().mockReturnValue(of({ data: [] })),
  getCompanies: jest.fn().mockReturnValue(of({ data: [] })),
  getDefinableWork: jest.fn().mockReturnValue(of({ data: [] })),
  getLocations: jest.fn().mockReturnValue(of({ data: [] })),
  projectParent: of({}),
  accountProjectParent: of({})
};

const mockToastrService = {
  success: jest.fn(),
  error: jest.fn()
};

const mockRouter = {
  navigate: jest.fn(),
  events: of({})
};

const mockModalService = {
  show: jest.fn().mockReturnValue({})
};

const mockModalRef = {
  hide: jest.fn(),
  content: {},
  setClass: jest.fn()
};

const mockSocket = {
  emit: jest.fn()
};

const mockMixpanelService = {
  addMixpanelEvents: jest.fn()
};

const mockProjectSettingsService = {
  getProjectSettings: jest.fn().mockReturnValue(of({}))
};

describe('EditinspectionFormComponent', () => {
  let component: EditinspectionFormComponent;
  let fixture: ComponentFixture<EditinspectionFormComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        EditinspectionFormComponent,
        MockNgxPlacesDirective,
        MockMultiselectDropdownComponent,
        MockAutocompleteComponent,
        MockBsDatepickerDirective,
        MockTimepickerComponent
      ],
      imports: [ReactiveFormsModule],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        UntypedFormBuilder,
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: ProjectService, useValue: mockProjectService },
        { provide: MixpanelService, useValue: mockMixpanelService },
        { provide: ProjectSettingsService, useValue: mockProjectSettingsService },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: Router, useValue: mockRouter },
        { provide: BsModalService, useValue: mockModalService },
        { provide: BsModalRef, useValue: mockModalRef },
        { provide: Socket, useValue: mockSocket }
      ]
    })
    .overrideComponent(EditinspectionFormComponent, {
      set: {
        template: '<div>Mock Template for Testing</div>'
      }
    })
    .compileComponents();

    fixture = TestBed.createComponent(EditinspectionFormComponent);
    component = fixture.componentInstance;

    // Initialize form properly
    const formBuilder = TestBed.inject(UntypedFormBuilder);
    component.deliverEditForm = formBuilder.group({
      id: ['test-id'],
      InspectionId: ['INS-001'],
      CraneRequestId: [''],
      description: ['Test description', []],
      inspectionType: [''],
      InspectionType: [''],
      inspectionStart: [new Date()],
      inspectionEnd: [new Date()],
      inspectionDate: [new Date()],
      deliveryDate: [new Date()],
      endDate: [new Date()],
      escort: [false],
      vehicleDetails: [''],
      notes: ['Test notes'],
      recurrenceId: [''],
      recurrence: ['Does Not Repeat'],
      repeatEveryCount: [1],
      repeatEveryType: [''],
      chosenDateOfMonth: [1],
      dateOfMonth: [''],
      monthlyRepeatType: [''],
      recurrenceEndDate: [''],
      originationAddress: [''],
      vehicleType: [[]],
      person: [[]],
      companyItems: [[]],
      defineItems: [[]],
      EquipmentId: [[]],
      LocationId: [[]],
      GateId: [''],
      isAssociatedWithCraneRequest: [false],
      cranePickUpLocation: [''],
      craneDropOffLocation: [''],
      days: formBuilder.array([])
    });

    // Mock component methods
    component.editDetailsForm = jest.fn();
    component.getMembers = jest.fn();
    component.getProjectSettings = jest.fn();
    component.selectDuration = jest.fn();
    component.onEditSubmitForm = jest.fn();
    component.getAvailableSlots = jest.fn();
    component.getNDR = jest.fn();
    component.getOverAllGate = jest.fn();
    component.getOverAllEquipmentforEditNdr = jest.fn();

    // Set up component properties
    component.ProjectId = 'test-project-id';
    component.ParentCompanyId = 'test-parent-company-id';
    component.InspectionId = 'test-inspection-id';
    component.authUser = { id: 'test-user-id', User: { email: '<EMAIL>' }, RoleId: 1 };
    component.gateList = [{ id: 1, gateName: 'Gate 1' }];
    component.equipmentList = [{ id: 1, equipmentName: 'Equipment 1', PresetEquipmentType: { isCraneType: false } }];
    component.locationList = [{ id: 1, locationPath: 'Location 1' }];
    component.companyList = [{ id: 1, companyName: 'Company 1' }];
    component.defineList = [{ id: 1, DFOW: 'DFOW 1' }];
    component.memberList = [{ id: 1, User: { email: '<EMAIL>' } }];

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.submitted).toBe(false);
    expect(component.escort).toBe(false);
    expect(component.formSubmitted).toBe(false);
    expect(component.editSubmitted).toBe(false);
    // modalLoader might be true initially, so let's check its actual value
    expect(typeof component.modalLoader).toBe('boolean');
    expect(component.loader).toBe(true); // loader is set to true in getProjectIdAndParentCompanyId
  });

  it('should have vehicle types defined', () => {
    expect(component.vehicleTypes).toEqual([
      { id: 1, type: 'Medium and Heavy Duty Truck' },
      { id: 2, type: 'Passenger Car' },
      { id: 3, type: 'Light Duty Truck' }
    ]);
  });

  it('should toggle duration dropdown', () => {
    component.durationisOpen = false;
    component.durationToggleDropdown();
    expect(component.durationisOpen).toBe(true);

    component.durationToggleDropdown();
    expect(component.durationisOpen).toBe(false);
  });

  it('should close duration dropdown', () => {
    component.durationisOpen = true;
    component.durationCloseDropdown();
    expect(component.durationisOpen).toBe(false);
  });

  it('should select vehicle type correctly', () => {
    const vehicleData = { id: 1 };
    component.vehicleTypeSelected(vehicleData);
    expect(component.selectedVehicleType).toBe('Medium and Heavy Duty Truck');
  });

  it('should convert start time correctly', () => {
    const inspectionDate = new Date('2024-01-01');
    const result = component.convertStart(inspectionDate, 10, 30);
    expect(result.getHours()).toBe(10);
    expect(result.getMinutes()).toBe(30);
  });

  it('should validate number input correctly', () => {
    // Test numeric input
    const numericEvent = { which: 49, keyCode: 49 }; // '1'
    expect(component.numberOnly(numericEvent)).toBe(true);

    // Test non-numeric input
    const nonNumericEvent = { which: 65, keyCode: 65 }; // 'A'
    expect(component.numberOnly(nonNumericEvent)).toBe(false);
  });

  it('should set AM/PM correctly', () => {
    component.selectAM();
    expect(component.isAM).toBe(true);

    component.selectPM();
    expect(component.isAM).toBe(false);
  });

  // Test form initialization and validation
  it('should initialize form with proper validators', () => {
    expect(component.deliverEditForm).toBeDefined();
    expect(component.deliverEditForm.get('description')).toBeDefined();
    expect(component.deliverEditForm.get('person')).toBeDefined();
  });

  it('should handle project ID and parent company ID', () => {
    const response = { ProjectId: 'new-project-id', ParentCompanyId: 'new-parent-id' };
    component.getProjectIdAndParentCompanyId(response);

    expect(component.ProjectId).toBe('new-project-id');
    expect(component.ParentCompanyId).toBe('new-parent-id');
    expect(component.loader).toBe(true);
  });

  it('should not set project ID for undefined response', () => {
    const originalProjectId = component.ProjectId;
    component.getProjectIdAndParentCompanyId(undefined);
    expect(component.ProjectId).toBe(originalProjectId);
  });

  // Test time selection methods
  it('should select hour correctly', () => {
    component.selectedMinute = 30;
    component.deliverEditForm.get('inspectionStart').setValue(new Date('2024-01-01T10:00:00'));

    component.selectHour(2);

    expect(component.selectedHour).toBe(2);
    expect(component.selectDuration).toHaveBeenCalledWith(2);
  });

  it('should select minute correctly', () => {
    component.selectedHour = 1;
    component.deliverEditForm.get('inspectionStart').setValue(new Date('2024-01-01T10:00:00'));

    component.selectMinute(45);

    expect(component.selectedMinute).toBe(45);
    expect(component.selectDuration).toHaveBeenCalledWith(45);
  });

  it('should update dropdown state when both hour and minute are selected', () => {
    component.selectedHour = 1;
    component.selectedMinutes = 30;

    component.updateDropdownState();

    expect(component.durationisOpen).toBe(false);
  });

  // Test vehicle type selection
  it('should handle vehicle type selection with different IDs', () => {
    const vehicleData = { id: 2 };
    component.vehicleTypeSelected(vehicleData);
    expect(component.selectedVehicleType).toBe('Passenger Car');
  });

  it('should handle vehicle type selection with string ID', () => {
    const vehicleData = { id: '3' };
    component.vehicleTypeSelected(vehicleData);
    expect(component.selectedVehicleType).toBe('Light Duty Truck');
  });

  // Test number validation edge cases
  it('should allow special keys in number validation', () => {
    const backspaceEvent = { which: 8, keyCode: 8 }; // Backspace
    expect(component.numberOnly(backspaceEvent)).toBe(true);

    const tabEvent = { which: 9, keyCode: 9 }; // Tab
    expect(component.numberOnly(tabEvent)).toBe(true);
  });

  it('should reject letters in number validation', () => {
    const letterEvent = { which: 97, keyCode: 97 }; // 'a'
    expect(component.numberOnly(letterEvent)).toBe(false);
  });

  // Test date conversion with different times
  it('should convert start time with different hours and minutes', () => {
    const inspectionDate = new Date('2024-01-01');
    const result = component.convertStart(inspectionDate, 14, 45);

    expect(result.getHours()).toBe(14);
    expect(result.getMinutes()).toBe(45);
    expect(result.getFullYear()).toBe(2024);
    expect(result.getMonth()).toBe(0); // January
    expect(result.getDate()).toBe(1);
  });

  // Test inspection type change
  it('should change inspection type and call onEditSubmitForm', () => {
    const inspectionType = 'Type A';
    // Add the InspectionType control to the form
    component.deliverEditForm.addControl('InspectionType', TestBed.inject(UntypedFormBuilder).control(''));

    component.onChangeInspectionType(inspectionType);

    expect(component.deliverEditForm.get('InspectionType').value).toBe(inspectionType);
    expect(component.onEditSubmitForm).toHaveBeenCalledWith(null);
  });

  // Test default person setting
  it('should set default person correctly', () => {
    component.authUser = {
      id: 'user-123',
      User: { email: '<EMAIL>' }
    };

    component.setDefaultPerson();

    const personValue = component.deliverEditForm.get('person').value;
    expect(personValue).toEqual([{
      email: '<EMAIL>',
      id: 'user-123',
      readonly: true
    }]);
  });

  // Test time selection
  it('should select time and update form', () => {
    const startStr = '2024-01-01T10:00:00';
    const endStr = '2024-01-01T11:00:00';

    component.selectTime(startStr, endStr);

    expect(component.deliverEditForm.get('inspectionStart').value).toEqual(new Date(startStr));
    expect(component.deliverEditForm.get('inspectionEnd').value).toEqual(new Date(endStr));
    expect(component.NDRTimingChanged).toBe(true);
    expect(component.onEditSubmitForm).toHaveBeenCalledWith(null);
  });

  // Test scroll to time
  it('should scroll to time slot', () => {
    const mockContainer = {
      nativeElement: {
        querySelectorAll: jest.fn().mockReturnValue([
          { offsetTop: 100 },
          { offsetTop: 200 }
        ]),
        offsetTop: 50,
        scrollTop: 0
      }
    };
    component.timeSlotsContainer = mockContainer as any;

    component.scrollToTime(1);

    expect(mockContainer.nativeElement.scrollTop).toBe(150); // 200 - 50
  });

  // Test date generation
  it('should generate week dates correctly', () => {
    const selectedDate = '2024-01-01';
    component.generateWeekDates(selectedDate);

    expect(component.weekDates).toHaveLength(5);
    expect(component.weekDates[0].fullDate).toBe('2024-01-01');
    expect(component.selectedDate).toBe(component.weekDates[0]);
  });

  it('should select date and get available slots', () => {
    const day = { name: 'Wed', date: '05', fullDate: '2024-01-05' };
    component.selectDate(day);

    expect(component.selectedDate).toBe(day);
    expect(component.getAvailableSlots).toHaveBeenCalledWith('2024-01-05');
  });

  // Test location selection
  it('should handle location selection', () => {
    const locationData = { id: 1 };
    component.locationList = [
      {
        id: 1,
        locationPath: 'Test Location',
        gateDetails: [{ id: 1, gateName: 'Gate 1' }],
        EquipmentId: [{ id: 1, equipmentName: 'Equipment 1' }],
        TimeZoneId: [{ location: 'America/New_York' }]
      }
    ];

    component.locationSelected(locationData);

    expect(component.selectedLocationId).toBe(1);
    expect(component.timeZone).toBe('America/New_York');
    expect(component.gateList).toEqual([{ id: 1, gateName: 'Gate 1' }]);
  });

  it('should handle location selection without equipment', () => {
    const locationData = { id: 1 };
    component.locationList = [
      {
        id: 1,
        locationPath: 'Test Location',
        gateDetails: [],
        EquipmentId: null,
        TimeZoneId: [{ location: 'America/New_York' }]
      }
    ];

    component.locationSelected(locationData);

    expect(component.deliverEditForm.get('EquipmentId').value).toEqual([]);
  });

  // Test error handling methods
  it('should throw time error', () => {
    component.throwError('time error');

    expect(mockToastrService.error).toHaveBeenCalledWith('Please Enter Start time Lesser than End time');
    expect(component.editSubmitted).toBe(false);
    expect(component.formEditSubmitted).toBe(false);
    expect(component.saveQueuedNDR).toBe(false);
  });

  it('should throw future date error', () => {
    component.throwError('future date');

    expect(mockToastrService.error).toHaveBeenCalledWith('Please Enter Future Date.');
  });

  // Test form reset
  it('should reset form flags', () => {
    component.formEditSubmitted = true;
    component.editSubmitted = true;

    component.formReset();

    expect(component.formEditSubmitted).toBe(false);
    expect(component.editSubmitted).toBe(false);
  });

  // Test string validation
  it('should validate empty description', () => {
    const formValue = { description: '   ', notes: 'Valid notes' };
    const result = component.checkEditinspectionStringEmptyValues(formValue);

    expect(result).toBe(true);
    expect(mockToastrService.error).toHaveBeenCalledWith('Please Enter valid description.', 'OOPS!');
  });

  it('should validate empty notes', () => {
    const formValue = { description: 'Valid description', notes: '   ' };
    const result = component.checkEditinspectionStringEmptyValues(formValue);

    expect(result).toBe(true);
    expect(mockToastrService.error).toHaveBeenCalledWith('Please Enter valid notes.', 'OOPS!');
  });

  it('should pass validation for valid strings', () => {
    const formValue = { description: 'Valid description', notes: 'Valid notes' };
    const result = component.checkEditinspectionStringEmptyValues(formValue);

    expect(result).toBe(false);
  });

  it('should pass validation when notes is null', () => {
    const formValue = { description: 'Valid description', notes: null };
    const result = component.checkEditinspectionStringEmptyValues(formValue);

    expect(result).toBe(false);
  });

  // Test array comparison method
  it('should detect different arrays by property', () => {
    const array1 = [{ id: 1, name: 'A' }, { id: 2, name: 'B' }];
    const array2 = [{ id: 1, name: 'A' }, { id: 3, name: 'C' }];

    const result = component.areDifferentByProperty(array1, array2, 'id');

    expect(result).toBe(true);
    expect(component.array3Value).toEqual([1, 2, 3]);
  });

  it('should detect same arrays by property', () => {
    const array1 = [{ id: 1, name: 'A' }, { id: 2, name: 'B' }];
    const array2 = [{ id: 1, name: 'A' }, { id: 2, name: 'B' }];

    const result = component.areDifferentByProperty(array1, array2, 'id');

    expect(result).toBe(false);
  });

  // Test date checking
  it('should check date and set inspection start', () => {
    const data = { target: { value: 30 } };
    const originalDate = component.deliverEditForm.get('inspectionStart').value;

    component.checkDate(data);

    expect(component.deliverEditForm.get('inspectionStart').value).not.toBe(originalDate);
  });

  it('should not change date for values less than 25', () => {
    const data = { target: { value: 20 } };
    const originalDate = component.deliverEditForm.get('inspectionStart').value;

    component.checkDate(data);

    expect(component.deliverEditForm.get('inspectionStart').value).toBe(originalDate);
  });

  // Test address change handling
  it('should handle address change', () => {
    const address = { formatted_address: '123 Test Street, Test City' };

    component.handleAddressChange(address);

    expect(component.deliverEditForm.get('originationAddress').value).toBe('123 Test Street, Test City');
  });

  // Test inspection end time change detection
  it('should detect inspection end time change', () => {
    component.inspectionEndTimeChangeDetection();

    expect(component.NDRTimingChanged).toBe(true);
    expect(component.onEditSubmitForm).toHaveBeenCalledWith(null);
  });

  // Test change date method
  it('should change date and update end time', () => {
    component.modalLoader = false;
    const event = new Date('2024-01-01T10:30:00');

    component.changeDate(event);

    expect(component.NDRTimingChanged).toBe(true);
    expect(component.onEditSubmitForm).toHaveBeenCalledWith(null);
  });

  it('should not change date when modal loader is active', () => {
    component.modalLoader = true;
    component.NDRTimingChanged = false;
    const event = new Date('2024-01-01T10:30:00');

    component.changeDate(event);

    expect(component.onEditSubmitForm).toHaveBeenCalledWith(null);
  });

  // Test gate checking functionality
  it('should validate gate selection', () => {
    component.gateList = [{ id: 1, gateName: 'Gate 1' }, { id: 2, gateName: 'Gate 2' }];
    component.errtextenable = true;

    component.gatecheck(1, 'Gate');

    expect(component.errtextenable).toBe(false);
  });

  it('should validate equipment selection', () => {
    component.equipmentList = [{ id: 1, equipmentName: 'Equipment 1' }];
    component.errequipmentenable = true;

    component.gatecheck(1, 'Equipment');

    expect(component.errequipmentenable).toBe(false);
  });

  it('should validate person selection with valid members', () => {
    component.memberList = [{ id: 1 }, { id: 2 }];
    const value = [{ id: 1 }, { id: 2 }];

    component.gatecheck(value, 'Person');

    expect(component.errmemberenable).toBe(false);
  });

  it('should validate person selection with invalid members', () => {
    component.memberList = [{ id: 1 }];
    const value = [{ id: 1 }, { id: 3 }]; // id 3 doesn't exist in memberList

    component.gatecheck(value, 'Person');

    expect(component.errmemberenable).toBe(true);
  });

  // Test form submission validation
  it('should handle form submission with save action', () => {
    component.onEditSubmit('save');

    expect(component.saveQueuedNDR).toBe(true);
    expect(component.editSubmitted).toBe(true);
  });

  it('should handle form submission with submit action', () => {
    // Set up form with required equipment
    component.deliverEditForm.get('EquipmentId').setValue([{ id: 1 }]);

    component.onEditSubmit('submit');

    expect(component.formEditSubmitted).toBe(true);
    expect(component.editSubmitted).toBe(true);
  });

  // Test equipment validation in form submission
  it('should show error when no equipment selected', () => {
    component.deliverEditForm.get('EquipmentId').setValue([]);
    component.onEditSubmit('submit');

    expect(mockToastrService.error).toHaveBeenCalledWith('Equipment is required');
    expect(component.formEditSubmitted).toBe(false);
  });

  // Test preprocessing inspection data
  it('should preprocess inspection data successfully', () => {
    const formValue = {
      companyItems: [{ id: 1 }],
      person: [{ id: 1 }],
      defineItems: [{ id: 1 }],
      EquipmentId: [{ id: 1 }]
    };
    const companies = [];
    const persons = [];
    const define = [];
    const equipments = [];

    // Mock the string validation to return false (valid)
    component.checkEditinspectionStringEmptyValues = jest.fn().mockReturnValue(false);

    const result = component.preprocessInspectionData(formValue, companies, persons, define, equipments);

    expect(result).toBe(true);
    expect(companies).toEqual([1]);
    expect(persons).toEqual([1]);
    expect(define).toEqual([1]);
    expect(equipments).toEqual([1]);
  });

  it('should fail preprocessing when no companies provided', () => {
    const formValue = {
      companyItems: [],
      person: [{ id: 1 }],
      defineItems: [{ id: 1 }],
      EquipmentId: [{ id: 1 }]
    };

    const result = component.preprocessInspectionData(formValue, [], [], [], []);

    expect(result).toBe(false);
    expect(mockToastrService.error).toHaveBeenCalledWith('Responsible Company is required');
  });

  it('should fail preprocessing when no persons provided', () => {
    const formValue = {
      companyItems: [{ id: 1 }],
      person: [],
      defineItems: [{ id: 1 }],
      EquipmentId: [{ id: 1 }]
    };

    const result = component.preprocessInspectionData(formValue, [], [], [], []);

    expect(result).toBe(false);
    expect(mockToastrService.error).toHaveBeenCalledWith('Responsible Person is required');
  });

  // Test show error method
  it('should show error message from error object', () => {
    // Reset the mock to clear previous calls
    mockToastrService.error.mockClear();

    const error = {
      message: {
        details: [{ errorField: 'Test error message' }]
      }
    };

    component.showError(error);

    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
    expect(mockToastrService.error).toHaveBeenCalledWith(['Test error message']);
  });

  // Test edit NDR success
  it('should handle edit NDR success', () => {
    const response = { message: 'Success message' };
    component.resetForm = jest.fn();

    component.editNDRSuccess(response);

    expect(mockToastrService.success).toHaveBeenCalledWith('Success message', 'Success');
    expect(mockSocket.emit).toHaveBeenCalledWith('NDREditHistory', response);
    expect(mockMixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Edited New inspection Booking');
    expect(component.NDRTimingChanged).toBe(false);
  });

  // Test reset form method
  it('should reset form with no action', () => {
    component.modalRef1 = mockModalRef;
    component.resetForm('no');

    expect(mockModalRef.hide).toHaveBeenCalled();
  });

  it('should reset form with yes action', () => {
    component.modalRef1 = mockModalRef;
    component.modalRef = mockModalRef;
    component.formEditSubmitted = true;
    component.editSubmitted = true;
    component.saveQueuedNDR = true;
    component.NDRTimingChanged = true;

    component.resetForm('yes');

    expect(component.formEditSubmitted).toBe(false);
    expect(component.editSubmitted).toBe(false);
    expect(component.saveQueuedNDR).toBe(false);
    expect(component.NDRTimingChanged).toBe(false);
  });

  // Test close method with form changes
  it('should open confirmation modal when form is touched', () => {
    const template = { elementRef: { nativeElement: document.createElement('div') } } as TemplateRef<any>;
    component.deliverEditForm.markAsTouched();
    component.openConfirmationModalPopupForEditNDR = jest.fn();

    component.close(template);

    expect(component.openConfirmationModalPopupForEditNDR).toHaveBeenCalledWith(template);
  });

  it('should reset form directly when no changes', () => {
    const template = { elementRef: { nativeElement: document.createElement('div') } } as TemplateRef<any>;
    component.deliverEditForm.markAsUntouched();
    component.NDRTimingChanged = false;
    component.resetForm = jest.fn();

    component.close(template);

    expect(component.resetForm).toHaveBeenCalledWith('yes');
  });

  // Test confirmation modal popup
  it('should open confirmation modal popup', () => {
    const template = { elementRef: { nativeElement: document.createElement('div') } } as TemplateRef<any>;

    component.openConfirmationModalPopupForEditNDR(template);

    expect(mockModalService.show).toHaveBeenCalledWith(template, {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
    });
  });

  // Test ngOnInit
  it('should initialize component on ngOnInit', () => {
    component.modalRef = { content: { seriesOption: 2 } } as any;
    component.ngOnInit();

    expect(component.seriesOption).toBe(2);
    expect(component.isDisabledDate).toBe(true);
  });

  it('should use seriesoption input when modalRef content is not available', () => {
    component.modalRef = { content: null } as any;
    component.seriesoption = 3;
    component.ngOnInit();

    expect(component.seriesOption).toBe(3);
  });

  // Test vehicle type dropdown settings
  it('should have correct vehicle type dropdown settings', () => {
    expect(component.vehicleTypeDropdownSettings).toEqual({
      singleSelection: true,
      idField: 'id',
      textField: 'type',
      allowSearchFilter: true,
      closeDropDownOnSelection: true
    });
  });

  // Test request autocomplete items
  it('should return autocomplete observable', () => {
    const text = 'test';
    const result = component.requestAutoEditcompleteItems(text);

    expect(mockDeliveryService.searchNewMember).toHaveBeenCalledWith({
      ProjectId: component.ProjectId,
      search: text,
      ParentCompanyId: component.ParentCompanyId
    });
    expect(result).toBeDefined();
  });

  // Test setCompany method
  it('should set company data correctly', () => {
    component.currentEditItem = {
      companyDetails: [
        { Company: { id: 1, companyName: 'Company 1' } },
        { Company: { id: 2, companyName: 'Company 2' } }
      ]
    };

    component.setCompany();

    expect(component.editBeforeCompany).toEqual([
      { id: 1, companyName: 'Company 1' },
      { id: 2, companyName: 'Company 2' }
    ]);
    expect(component.deliverEditForm.get('companyItems').value).toEqual([
      { id: 1, companyName: 'Company 1' },
      { id: 2, companyName: 'Company 2' }
    ]);
  });

  it('should handle undefined company details', () => {
    component.currentEditItem = { companyDetails: undefined };

    component.setCompany();

    expect(component.editBeforeCompany).toEqual([]);
  });

  // Test setDefine method
  it('should set define data correctly', () => {
    component.currentEditItem = {
      defineWorkDetails: [
        { DeliverDefineWork: { id: 1, DFOW: 'DFOW 1' } },
        { DeliverDefineWork: { id: 2, DFOW: 'DFOW 2' } }
      ]
    };

    component.setDefine();

    expect(component.editBeforeDFOW).toEqual([
      { id: 1, DFOW: 'DFOW 1' },
      { id: 2, DFOW: 'DFOW 2' }
    ]);
    expect(component.deliverEditForm.get('defineItems').value).toEqual([
      { id: 1, DFOW: 'DFOW 1' },
      { id: 2, DFOW: 'DFOW 2' }
    ]);
  });

  // Test setVehicleType method
  it('should set vehicle type correctly', () => {
    component.currentEditItem = { vehicleType: 'Passenger Car' };

    component.setVehicleType();

    expect(component.selectedVehicleType).toBe('Passenger Car');
    expect(component.deliverEditForm.get('vehicleType').value).toEqual([
      { id: 2, type: 'Passenger Car' }
    ]);
  });

  it('should handle missing vehicle type', () => {
    component.currentEditItem = { vehicleType: null };

    component.setVehicleType();

    expect(component.selectedVehicleType).toBeUndefined();
  });

  // Test setlocation method
  it('should set location correctly', () => {
    component.currentEditItem = {
      location: {
        id: 1,
        locationPath: 'Test Location',
        TimeZoneId: [{ location: 'America/New_York' }]
      }
    };

    component.setlocation();

    expect(component.selectedLocationId).toBe(1);
    expect(component.timeZone).toBe('America/New_York');
    expect(component.deliverEditForm.get('LocationId').value).toEqual([
      { id: 1, locationPath: 'Test Location' }
    ]);
  });

  it('should handle location without timezone', () => {
    component.currentEditItem = {
      location: {
        id: 1,
        locationPath: 'Test Location',
        TimeZoneId: null
      }
    };

    component.setlocation();

    expect(component.timeZone).toBe(' ');
  });

  // Test setMember method
  it('should set member data correctly', () => {
    component.currentEditItem = {
      memberDetails: [
        { Member: { User: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' }, id: 1, isGuestUser: false } },
        { Member: { User: { firstName: null, lastName: null, email: '<EMAIL>' }, id: 2, isGuestUser: true } }
      ]
    };
    component.authUser = { User: { email: '<EMAIL>' } };

    mockDeliveryService.getMemberRole.mockReturnValue(of({ data: component.authUser }));

    component.setMember();

    expect(mockDeliveryService.getMemberRole).toHaveBeenCalled();
  });

  // Test setEquipment method
  it('should set equipment data correctly', () => {
    component.currentEditItem = {
      equipmentDetails: [
        { Equipment: { id: 1, equipmentName: 'Equipment 1' } },
        { Equipment: { id: 2, equipmentName: 'Equipment 2' } }
      ]
    };

    component.setEquipment();

    expect(component.editBeforeEquipment).toEqual([
      { id: 1, equipmentName: 'Equipment 1' },
      { id: 2, equipmentName: 'Equipment 2' }
    ]);
    expect(component.deliverEditForm.get('EquipmentId').value).toEqual([
      { id: 1, equipmentName: 'Equipment 1' },
      { id: 2, equipmentName: 'Equipment 2' }
    ]);
  });

  it('should handle undefined equipment details', () => {
    component.currentEditItem = { equipmentDetails: undefined };

    component.setEquipment();

    expect(component.editBeforeEquipment).toEqual([]);
  });

  // Test checkStartEnd method
  it('should return true when start time is before end time', () => {
    const startTime = new Date('2024-01-01T10:00:00');
    const endTime = new Date('2024-01-01T11:00:00');

    const result = component.checkStartEnd(startTime, endTime);

    expect(result).toBe(true);
  });

  it('should return false when start time is after end time', () => {
    const startTime = new Date('2024-01-01T11:00:00');
    const endTime = new Date('2024-01-01T10:00:00');

    const result = component.checkStartEnd(startTime, endTime);

    expect(result).toBe(false);
  });

  it('should return false when start time equals end time', () => {
    const startTime = new Date('2024-01-01T10:00:00');
    const endTime = new Date('2024-01-01T10:00:00');

    const result = component.checkStartEnd(startTime, endTime);

    expect(result).toBe(false);
  });

  // Test checkEditinspectionFutureDate method
  it('should return true for future dates', () => {
    component.inspectionWindowTime = 1;
    component.inspectionWindowTimeUnit = 'hours';

    const futureStart = new Date(Date.now() + 2 * 60 * 60 * 1000); // 2 hours from now
    const futureEnd = new Date(Date.now() + 3 * 60 * 60 * 1000); // 3 hours from now

    const result = component.checkEditinspectionFutureDate(futureStart, futureEnd, 'submit');

    expect(result).toBe(true);
  });

  it('should return true for save action regardless of date', () => {
    const pastStart = new Date(Date.now() - 2 * 60 * 60 * 1000); // 2 hours ago
    const pastEnd = new Date(Date.now() - 1 * 60 * 60 * 1000); // 1 hour ago

    const result = component.checkEditinspectionFutureDate(pastStart, pastEnd, 'save');

    expect(result).toBe(true);
  });

  it('should return false for past dates with submit action', () => {
    component.inspectionWindowTime = 1;
    component.inspectionWindowTimeUnit = 'hours';

    const pastStart = new Date(Date.now() - 2 * 60 * 60 * 1000); // 2 hours ago
    const pastEnd = new Date(Date.now() - 1 * 60 * 60 * 1000); // 1 hour ago

    const result = component.checkEditinspectionFutureDate(pastStart, pastEnd, 'submit');

    expect(result).toBe(false);
  });

  // Test getProjectSettings method
  it('should get project settings when ProjectId exists', () => {
    component.ProjectId = 'test-project-id';
    const mockResponse = {
      data: {
        inspectionWindowTime: 2,
        inspectionWindowTimeUnit: 'hours'
      }
    };
    mockProjectSettingsService.getProjectSettings.mockReturnValue(of(mockResponse));

    // Remove the mock to test the actual method
    component.getProjectSettings = EditinspectionFormComponent.prototype.getProjectSettings;
    component.getProjectSettings();

    expect(mockProjectSettingsService.getProjectSettings).toHaveBeenCalledWith({
      ProjectId: 'test-project-id'
    });
    expect(component.inspectionWindowTime).toBe(2);
    expect(component.inspectionWindowTimeUnit).toBe('hours');
  });

  it('should not call service when ProjectId is null', () => {
    component.ProjectId = null;
    // Reset the mock call count
    mockProjectSettingsService.getProjectSettings.mockClear();
    // Remove the mock to test the actual method
    component.getProjectSettings = EditinspectionFormComponent.prototype.getProjectSettings;

    component.getProjectSettings();

    expect(mockProjectSettingsService.getProjectSettings).not.toHaveBeenCalled();
  });

  // Test getOverAllEquipmentforEditNdr method
  it('should get equipment list and set dropdown settings', () => {
    const mockEquipmentResponse = {
      data: [
        { id: 1, equipmentName: 'Equipment 1' },
        { id: 2, equipmentName: 'Equipment 2' }
      ]
    };

    // Mock the missing method
    component.getCompaniesForEditNdr = jest.fn();
    mockProjectService.listEquipment.mockReturnValue(of(mockEquipmentResponse));

    // Remove the mock to test the actual method
    component.getOverAllEquipmentforEditNdr = EditinspectionFormComponent.prototype.getOverAllEquipmentforEditNdr;
    component.getOverAllEquipmentforEditNdr();

    expect(mockProjectService.listEquipment).toHaveBeenCalledWith(
      {
        ProjectId: component.ProjectId,
        pageSize: 0,
        pageNo: 0,
        ParentCompanyId: component.ParentCompanyId
      },
      {
        isFilter: true,
        showActivatedAlone: true
      }
    );
    expect(component.equipmentList).toEqual(mockEquipmentResponse.data);
    expect(component.equipmentDropdownSettings).toBeDefined();
    expect(component.getCompaniesForEditNdr).toHaveBeenCalled();
  });

  it('should initialize form controls correctly', () => {
    expect(component.deliverEditForm.get('id')).toBeTruthy();
    expect(component.deliverEditForm.get('InspectionId')).toBeTruthy();
    expect(component.deliverEditForm.get('description')).toBeTruthy();
    expect(component.deliverEditForm.get('inspectionType')).toBeTruthy();
  });

  it('should handle inspection type change', () => {
    const type = 'Standard';
    component.onChangeInspectionType(type);
    expect(component.deliverEditForm.get('InspectionType').value).toBe(type);
  });

  it('should handle duration selection', () => {
    component.selectedHour = 1;
    component.selectedMinute = 30;
    // Remove the mock to test the actual method
    component.selectDuration = EditinspectionFormComponent.prototype.selectDuration;
    component.getAvailableSlots = jest.fn();
    component.selectDuration(30);
    expect(component.selectedMinutes).toBe(90); // 1 hour * 60 + 30 minutes
  });

  it('should handle vehicle type selection', () => {
    const data = { id: 1 };
    component.vehicleTypeSelected(data);
    expect(component.selectedVehicleType).toBe('Medium and Heavy Duty Truck');
  });

  it('should handle time selection', () => {
    const startStr = '2024-01-01T09:00:00';
    const endStr = '2024-01-01T10:00:00';
    component.selectTime(startStr, endStr);
    expect(component.deliverEditForm.get('inspectionStart').value).toEqual(new Date(startStr));
    expect(component.deliverEditForm.get('inspectionEnd').value).toEqual(new Date(endStr));
  });

  it('should handle form submission', () => {
    const formValue = [{ id: 1, PresetEquipmentType: { isCraneType: false } }];
    component.equipmentList = [{ id: 1, PresetEquipmentType: { isCraneType: false } }];
    // Remove the mock to test the actual method
    component.onEditSubmitForm = EditinspectionFormComponent.prototype.onEditSubmitForm;
    component.onEditSubmitForm(formValue);
    expect(component.craneEquipmentTypeChosen).toBe(false);
  });

  it('should handle recurrence selection', () => {
    const value = 'Daily';
    // Remove the mock to test the actual method
    component.onRecurrenceSelect = EditinspectionFormComponent.prototype.onRecurrenceSelect;
    component.onRecurrenceSelect(value);
    expect(component.selectedRecurrence).toBe(value);
    expect(component.isRepeatWithSingleRecurrence).toBeTruthy();
  });

  it('should handle recurrence count change', () => {
    const value = 2;
    // Remove the mock to test the actual method
    component.changeRecurrenceCount = EditinspectionFormComponent.prototype.changeRecurrenceCount;
    component.updateRecurrenceUIState = jest.fn();
    component.updateRecurrenceFormValues = jest.fn();
    component.occurMessage = jest.fn();
    component.changeRecurrenceCount(value);
    expect(component.updateRecurrenceUIState).toHaveBeenCalledWith(value);
    expect(component.updateRecurrenceFormValues).toHaveBeenCalledWith(value);
  });

  it('should handle monthly recurrence change', () => {
    // Remove the mock to test the actual method
    component.changeMonthlyRecurrence = EditinspectionFormComponent.prototype.changeMonthlyRecurrence;
    component.setMonthlyOrYearlyRecurrenceOption = jest.fn();
    component.updateFormValidation = jest.fn();
    component.showMonthlyRecurrence = jest.fn();
    component.occurMessage = jest.fn();
    component.changeMonthlyRecurrence();
    expect(component.setMonthlyOrYearlyRecurrenceOption).toHaveBeenCalled();
    expect(component.updateFormValidation).toHaveBeenCalled();
    expect(component.showMonthlyRecurrence).toHaveBeenCalled();
  });

  it('should handle form control value changes', () => {
    component.formControlValueChanged();
    expect(component.formEdited).toBeTruthy();
  });

  it('should handle address change', () => {
    const address = { formatted_address: '123 Test St' };
    component.handleAddressChange(address);
    expect(component.deliverEditForm.get('originationAddress').value).toBe(address.formatted_address);
  });

  it('should handle date change', () => {
    const event = new Date('2024-03-20T10:30:00');
    // Remove the mock to test the actual method
    component.changeDate = EditinspectionFormComponent.prototype.changeDate;
    component.changeDate(event);
    expect(component.deliverEditForm.get('inspectionEnd').value).toBeTruthy();
  });

  it('should handle form reset', () => {
    // Remove the mock to test the actual method
    component.formReset = EditinspectionFormComponent.prototype.formReset;
    component.formReset();
    expect(component.formSubmitted).toBeFalsy();
    expect(component.editSubmitted).toBeFalsy();
  });

  it('should handle error display', () => {
    const err = {
      message: {
        details: [{ message: 'Test error' }]
      }
    };
    component.showError(err);
    expect(mockToastrService.error).toHaveBeenCalled();
  });

  it('should handle project settings retrieval', () => {
    component.getProjectSettings();
    expect(mockProjectSettingsService.getProjectSettings).toHaveBeenCalled();
  });

  it('should handle equipment retrieval', () => {
    component.getOverAllEquipmentforEditNdr();
    expect(mockProjectService.listEquipment).toHaveBeenCalled();
  });

  it('should handle location retrieval', () => {
    // Remove the mock to test the actual method
    component.getLocationForEditNdr = EditinspectionFormComponent.prototype.getLocationForEditNdr;
    component.getLocationForEditNdr();
    expect(mockProjectService.getLocations).toHaveBeenCalled();
  });

  it('should handle company retrieval', () => {
    component.getCompaniesForEditNdr();
    expect(mockProjectService.listAllMember).toHaveBeenCalled();
  });

  it('should handle definable retrieval', () => {
    component.getDefinableForEditNdr();
    expect(mockProjectService.listAllMember).toHaveBeenCalled();
  });

  it('should handle member retrieval', () => {
    component.getMembers();
    expect(mockDeliveryService.getMemberRole).toHaveBeenCalled();
  });

  it('should handle gate retrieval', () => {
    // Remove the mock to test the actual method
    component.getOverAllGate = EditinspectionFormComponent.prototype.getOverAllGate;
    component.getOverAllGate();
    expect(mockProjectService.gateList).toHaveBeenCalled();
  });

  it('should handle NDR retrieval', () => {
    // Remove the mock to test the actual method
    component.getNDR = EditinspectionFormComponent.prototype.getNDR;
    component.getNDR();
    expect(mockDeliveryService.getInspectionNDRData).toHaveBeenCalled();
  });

  it('should handle available slots retrieval', () => {
    const date = new Date();
    // Remove the mock to test the actual method
    component.getAvailableSlots = EditinspectionFormComponent.prototype.getAvailableSlots;
    component.getAvailableSlots(date);
    expect(mockDeliveryService.getAvailableTimeSlots).toHaveBeenCalled();
  });

  it('should handle form validation', () => {
    component.updateFormValidation();
    expect(component.deliverEditForm.valid).toBeTruthy();
  });

  it('should handle recurrence UI state update', () => {
    const value = 1;
    component.updateRecurrenceUIState(value);
    expect(component.showRecurrenceTypeDropdown).toBeTruthy();
  });

  it('should handle recurrence form values update', () => {
    const value = 1;
    component.updateRecurrenceFormValues(value);
    expect(component.deliverEditForm.get('repeatEveryCount').value).toBe(value);
  });

  it('should handle repeat every type selection', () => {
    const value = 'Days';
    const eventDetail = { target: { value } };
    component.chooseRepeatEveryType(value, eventDetail);
    expect(component.deliverEditForm.get('repeatEveryType').value).toBe(value);
  });

  it('should handle weekly dates update', () => {
    component.updateWeeklyDates();
    expect(component.weekDates.length).toBeGreaterThan(0);
  });

  it('should handle form submission with action', () => {
    const action = 'submit';
    component.onSubmit(action);
    expect(component.formSubmitted).toBeTruthy();
  });

  it('should handle open recurrence popup', () => {
    component.openRecurrencePopup();
    expect(mockModalService.show).toHaveBeenCalled();
  });

  it('should handle recurrence submit', () => {
    const action = 'test';
    const result = component.recurrenceSubmit(action);
    expect(result).toBeTruthy();
  });

  it('should handle form control value changes for recurrence', () => {
    component.formControlValueChanged();
    expect(component.recurrenceEdited).toBeTruthy();
  });

  it('should handle set repeat', () => {
    const data = { value: 'Daily' };
    component.setRepeat(data);
    expect(component.selectedRecurrence).toBe(data.value);
  });

  it('should handle sort week days', () => {
    const data = [
      { day: 'Monday', value: 1 },
      { day: 'Tuesday', value: 2 }
    ];
    const result = component.sortWeekDays(data);
    expect(result[0].value).toBe(1);
  });

  it('should handle inspection end time change detection', () => {
    component.inspectionEndTimeChangeDetection();
    expect(component.NDRTimingChanged).toBeTruthy();
  });

  it('should handle edit details form', () => {
    component.editDetailsForm();
    expect(component.formEdited).toBeTruthy();
  });

  it('should handle set equipment', () => {
    component.setEquipment();
    expect(component.equipmentList.length).toBeGreaterThan(0);
  });

  it('should handle set repeat every type message', () => {
    const value = 'Days';
    component.setRepeatEveryTypeMessage(value);
    expect(component.deliverEditForm.get('repeatEveryType').value).toBe(value);
  });

  it('should handle set monthly or yearly recurrence option', () => {
    component.setMonthlyOrYearlyRecurrenceOption();
    expect(component.monthlyDate).toBeTruthy();
  });

  it('should handle show monthly recurrence', () => {
    component.showMonthlyRecurrence();
    expect(component.monthlyDate).toBeTruthy();
  });

  it('should handle occur message', () => {
    component.occurMessage();
    expect(component.deliverEditForm.get('recurrenceEndDate').value).toBeTruthy();
  });

  it('should handle onChange event', () => {
    const event = { target: { value: 'test', checked: true } };
    component.onChange(event);
    expect(component.deliverEditForm.get('escort').value).toBeTruthy();
  });

  it('should handle set location', () => {
    component.setlocation();
    expect(component.locationList.length).toBeGreaterThan(0);
  });

  it('should handle set company', () => {
    component.setCompany();
    expect(component.companyList.length).toBeGreaterThan(0);
  });

  it('should handle set define', () => {
    component.setDefine();
    expect(component.defineList.length).toBeGreaterThan(0);
  });

  it('should handle set member', () => {
    component.setMember();
    expect(component.memberList.length).toBeGreaterThan(0);
  });

  it('should handle set vehicle type', () => {
    component.setVehicleType();
    expect(component.vehicleTypes.length).toBeGreaterThan(0);
  });

  it('should handle check date', () => {
    const event = { target: { value: 1 } };
    const result = component.checkDate(event);
    expect(result).toBeTruthy();
  });

  it('should handle number only input', () => {
    const event = { which: 48, keyCode: 48 };
    const result = component.numberOnly(event);
    expect(result).toBeTruthy();
  });

  it('should handle get last crane request id', () => {
    component.getLastCraneRequestId();
    expect(mockProjectService.getLastCraneRequestId).toHaveBeenCalled();
  });

  it('should handle gate check', () => {
    const value = 'test';
    const menuname = 'test';
    component.gatecheck(value, menuname);
    expect(component.deliverEditForm.get('GateId').value).toBe(value);
  });

  it('should handle index based submit', () => {
    const params = {
      formValue: {},
      inspectionStart: new Date(),
      inspectionEnd: new Date(),
      companies: [],
      persons: [],
      define: [],
      action: 'test',
      equipments: []
    };
    component.indexBasedSubmit(params);
    expect(mockDeliveryService.editInspectionNDR).toHaveBeenCalled();
  });

  it('should handle check request status and user role', () => {
    const params = {
      formValue: {},
      inspectionStart: new Date(),
      inspectionEnd: new Date(),
      companies: [],
      persons: [],
      define: [],
      action: 'test',
      equipments: []
    };
    component.checkRequestStatusAndUserRole(params);
    expect(mockDeliveryService.editInspectionNDR).toHaveBeenCalled();
  });

  it('should handle update queued inspection', () => {
    const updateQueuedNdrFormValue = {};
    const inspectionStart = new Date();
    const inspectionEnd = new Date();
    const companies = [];
    const persons = [];
    const define = [];
    const equipments = [];
    component.updateQueuedinspection(
      updateQueuedNdrFormValue,
      inspectionStart,
      inspectionEnd,
      companies,
      persons,
      define,
      equipments
    );
    expect(mockDeliveryService.editQueuedInspectionNDRForm).toHaveBeenCalled();
  });

  it('should handle prepare queued NDR payload', () => {
    const formValue = {};
    const inspectionStart = new Date();
    const inspectionEnd = new Date();
    const companies = [];
    const persons = [];
    const define = [];
    const equipments = [];
    const result = component.prepareQueuedNdrPayload(
      formValue,
      inspectionStart,
      inspectionEnd,
      companies,
      persons,
      define,
      equipments
    );
    expect(result).toBeTruthy();
  });

  it('should handle location selected', () => {
    const data = { id: 1 };
    component.locationSelected(data);
    expect(component.selectedLocationId).toBe(data.id);
  });

  it('should handle update inspection', () => {
    const params = {
      editNdrFormValue: {},
      inspectionStart: new Date(),
      inspectionEnd: new Date(),
      companies: [],
      persons: [],
      define: [],
      action: 'test',
      equipments: []
    };
    component.updateinspection(params);
    expect(mockDeliveryService.editInspectionNDR).toHaveBeenCalled();
  });

  it('should handle preprocess inspection data', () => {
    const formValue = {};
    const companies = [];
    const persons = [];
    const define = [];
    const equipments = [];
    const result = component.preprocessInspectionData(
      formValue,
      companies,
      persons,
      define,
      equipments
    );
    expect(result).toBeTruthy();
  });

  it('should handle submit inspection', () => {
    const params = {
      formValue: {},
      inspectionStart: new Date(),
      inspectionEnd: new Date(),
      companies: [],
      persons: [],
      define: [],
      equipments: [],
      action: 'test'
    };
    component.submitInspection(params);
    expect(mockDeliveryService.editInspectionNDR).toHaveBeenCalled();
  });

  it('should handle select AM', () => {
    component.selectAM();
    expect(component.isAM).toBeTruthy();
  });

  it('should handle select PM', () => {
    component.selectPM();
    expect(component.isAM).toBeFalsy();
  });

  it('should handle throw error', () => {
    const action = 'test';
    component.throwError(action);
    expect(mockToastrService.error).toHaveBeenCalled();
  });

  it('should handle check edit inspection string empty values', () => {
    const formValue = {
      description: '',
      notes: ''
    };
    const result = component.checkEditinspectionStringEmptyValues(formValue);
    expect(result).toBeTruthy();
  });

  it('should handle edit queued NDR', () => {
    const payload = {};
    component.editQueuedNDR(payload);
    expect(mockDeliveryService.editQueuedInspectionNDRForm).toHaveBeenCalled();
  });

  it('should handle editQueuedNDR error with statusCode 400', () => {
    const payload = {};
    mockDeliveryService.editQueuedInspectionNDRForm.mockReturnValueOnce({
      subscribe: ({ next, error }) => error({ message: { statusCode: 400, details: [{ error: 'err' }] } })
    });
    component.editQueuedNDR(payload);
    expect(mockToastrService.error).toHaveBeenCalled();
  });

  it('should handle editQueuedNDR error with no message', () => {
    const payload = {};
    mockDeliveryService.editQueuedInspectionNDRForm.mockReturnValueOnce({
      subscribe: ({ next, error }) => error({})
    });
    component.editQueuedNDR(payload);
    expect(mockToastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should handle editQueuedNDR error with message but not statusCode 400', () => {
    const payload = {};
    mockDeliveryService.editQueuedInspectionNDRForm.mockReturnValueOnce({
      subscribe: ({ next, error }) => error({ message: 'Some error' })
    });
    component.editQueuedNDR(payload);
    expect(mockToastrService.error).toHaveBeenCalledWith('Some error', 'OOPS!');
  });

  it('should handle edit NDR success', () => {
    const response = { message: 'Success' };
    component.editNDRSuccess(response);
    expect(mockToastrService.success).toHaveBeenCalled();
  });

  it('should handle submit queued NDR', () => {
    const payload = {};
    component.submitQueuedNDR(payload);
    expect(mockDeliveryService.submitQueuedNDR).toHaveBeenCalled();
  });

  it('should handle submitQueuedNDR error with statusCode 400', () => {
    const payload = {};
    mockDeliveryService.submitQueuedNDR.mockReturnValueOnce({
      subscribe: ({ next, error }) => error({ message: { statusCode: 400, details: [{ error: 'err' }] } })
    });
    component.submitQueuedNDR(payload);
    expect(mockToastrService.error).toHaveBeenCalled();
  });

  it('should handle submitQueuedNDR error with no message', () => {
    const payload = {};
    mockDeliveryService.submitQueuedNDR.mockReturnValueOnce({
      subscribe: ({ next, error }) => error({})
    });
    component.submitQueuedNDR(payload);
    expect(mockToastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should handle submitQueuedNDR error with message but not statusCode 400', () => {
    const payload = {};
    mockDeliveryService.submitQueuedNDR.mockReturnValueOnce({
      subscribe: ({ next, error }) => error({ message: 'Some error' })
    });
    component.submitQueuedNDR(payload);
    expect(mockToastrService.error).toHaveBeenCalledWith('Some error', 'OOPS!');
  });

  it('should handle edit NDR', () => {
    const payload = {};
    component.editNDR(payload);
    expect(mockDeliveryService.editInspectionNDR).toHaveBeenCalled();
  });

  // Additional tests for showError edge cases
  it('should handle showError with missing details', () => {
    const err = { message: { details: [] } };
    component.showError(err);
    expect(mockToastrService.error).toHaveBeenCalled();
  });

  it('should handle showError with malformed details', () => {
    const err = { message: { details: [{}] } };
    component.showError(err);
    expect(mockToastrService.error).toHaveBeenCalled();
  });

  it('should handle showError with no message property', () => {
    const err = {};
    // Should not throw
    expect(() => component.showError(err as any)).not.toThrow();
  });

  it('should handle showError with message.details not an array', () => {
    const err = { message: { details: 'not-an-array' } };
    expect(() => component.showError(err as any)).not.toThrow();
    expect(mockToastrService.error).toHaveBeenCalled();
  });

  it('should handle showError with message.details undefined', () => {
    const err = { message: { } };
    expect(() => component.showError(err as any)).not.toThrow();
    expect(mockToastrService.error).toHaveBeenCalled();
  });

  it('should handle showError with message.details as array with empty object', () => {
    const err = { message: { details: [{}] } };
    expect(() => component.showError(err as any)).not.toThrow();
    expect(mockToastrService.error).toHaveBeenCalled();
  });

  // Edge cases for error callback in editQueuedNDR
  it('should handle editQueuedNDR error with message as string', () => {
    const payload = {};
    mockDeliveryService.editQueuedInspectionNDRForm.mockReturnValueOnce({
      subscribe: ({ next, error }) => error({ message: 'error string' })
    });
    component.editQueuedNDR(payload);
    expect(mockToastrService.error).toHaveBeenCalledWith('error string', 'OOPS!');
  });

  it('should handle editQueuedNDR error with message as null', () => {
    const payload = {};
    mockDeliveryService.editQueuedInspectionNDRForm.mockReturnValueOnce({
      subscribe: ({ next, error }) => error({ message: null })
    });
    component.editQueuedNDR(payload);
    expect(mockToastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should handle editQueuedNDR error with message.details as empty array', () => {
    const payload = {};
    mockDeliveryService.editQueuedInspectionNDRForm.mockReturnValueOnce({
      subscribe: ({ next, error }) => error({ message: { statusCode: 400, details: [] } })
    });
    component.editQueuedNDR(payload);
    expect(mockToastrService.error).toHaveBeenCalled();
  });

  // Edge cases for error callback in submitQueuedNDR
  it('should handle submitQueuedNDR error with message as string', () => {
    const payload = {};
    mockDeliveryService.submitQueuedNDR.mockReturnValueOnce({
      subscribe: ({ next, error }) => error({ message: 'error string' })
    });
    component.submitQueuedNDR(payload);
    expect(mockToastrService.error).toHaveBeenCalledWith('error string', 'OOPS!');
  });

  it('should handle submitQueuedNDR error with message as null', () => {
    const payload = {};
    mockDeliveryService.submitQueuedNDR.mockReturnValueOnce({
      subscribe: ({ next, error }) => error({ message: null })
    });
    component.submitQueuedNDR(payload);
    expect(mockToastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should handle submitQueuedNDR error with message.details as empty array', () => {
    const payload = {};
    mockDeliveryService.submitQueuedNDR.mockReturnValueOnce({
      subscribe: ({ next, error }) => error({ message: { statusCode: 400, details: [] } })
    });
    component.submitQueuedNDR(payload);
    expect(mockToastrService.error).toHaveBeenCalled();
  });

  // Edge cases for error callback in editNDR
  it('should handle editNDR error with message as string', () => {
    const payload = {};
    mockDeliveryService.editInspectionNDR.mockReturnValueOnce({
      subscribe: ({ next, error }) => error({ message: 'error string' })
    });
    component.editNDR(payload);
    expect(mockToastrService.error).toHaveBeenCalledWith('error string', 'OOPS!');
  });

  it('should handle editNDR error with message as null', () => {
    const payload = {};
    mockDeliveryService.editInspectionNDR.mockReturnValueOnce({
      subscribe: ({ next, error }) => error({ message: null })
    });
    component.editNDR(payload);
    expect(mockToastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should handle editNDR error with message.details as empty array', () => {
    const payload = {};
    mockDeliveryService.editInspectionNDR.mockReturnValueOnce({
      subscribe: ({ next, error }) => error({ message: { statusCode: 400, details: [] } })
    });
    component.editNDR(payload);
    expect(mockToastrService.error).toHaveBeenCalled();
  });

  // Additional tests for resetForm edge cases
  it('should reset form when modalRef1 is not set', () => {
    component.modalRef1 = undefined;
    component.modalRef = mockModalRef;
    component.formEditSubmitted = true;
    component.editSubmitted = true;
    component.saveQueuedNDR = true;
    component.NDRTimingChanged = true;
    component.resetForm('yes');
    expect(component.formEditSubmitted).toBe(false);
    expect(component.editSubmitted).toBe(false);
    expect(component.saveQueuedNDR).toBe(false);
    expect(component.NDRTimingChanged).toBe(false);
  });

  it('should reset form with action no and modalRef1 not set', () => {
    component.modalRef1 = undefined;
    expect(() => component.resetForm('no')).not.toThrow();
  });

  it('should handle editNDR error with statusCode 400', () => {
    const payload = {};
    mockDeliveryService.editInspectionNDR.mockReturnValueOnce({
      subscribe: ({ next, error }) => error({ message: { statusCode: 400, details: [{ error: 'err' }] } })
    });
    component.editNDR(payload);
    expect(mockToastrService.error).toHaveBeenCalled();
  });

  it('should handle editNDR error with no message', () => {
    const payload = {};
    mockDeliveryService.editInspectionNDR.mockReturnValueOnce({
      subscribe: ({ next, error }) => error({})
    });
    component.editNDR(payload);
    expect(mockToastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should handle editNDR error with message but not statusCode 400', () => {
    const payload = {};
    mockDeliveryService.editInspectionNDR.mockReturnValueOnce({
      subscribe: ({ next, error }) => error({ message: 'Some error' })
    });
    component.editNDR(payload);
    expect(mockToastrService.error).toHaveBeenCalledWith('Some error', 'OOPS!');
  });

  it('should handle close modal', () => {
    const template = { elementRef: { nativeElement: document.createElement('div') } } as TemplateRef<any>;
    component.close(template);
    expect(mockModalRef.hide).toHaveBeenCalled();
  });

  it('should handle open confirmation modal popup for edit NDR', () => {
    const template = { elementRef: { nativeElement: document.createElement('div') } } as TemplateRef<any>;
    component.openConfirmationModalPopupForEditNDR(template);
    expect(mockModalService.show).toHaveBeenCalled();
  });

  it('should handle reset form', () => {
    const action = 'test';
    component.resetForm(action);
    expect(component.formSubmitted).toBeFalsy();
  });

  it('should handle check edit inspection future date', () => {
    const editinspectionStart = new Date();
    const editinspectionEnd = new Date();
    const action = 'test';
    const result = component.checkEditinspectionFutureDate(
      editinspectionStart,
      editinspectionEnd,
      action
    );
    expect(result).toBeTruthy();
  });

  it('should handle check start end', () => {
    const inspectionStart = new Date();
    const inspectionEnd = new Date();
    const result = component.checkStartEnd(inspectionStart, inspectionEnd);
    expect(result).toBeTruthy();
  });

  it('should handle open content modal', () => {
    component.openContentModal();
    expect(mockModalService.show).toHaveBeenCalled();
  });

  it('should handle edit details form', () => {
    component.editDetailsForm();
    expect(component.formEdited).toBeTruthy();
  });


});
