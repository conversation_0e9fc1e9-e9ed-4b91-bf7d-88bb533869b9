<div class="modal-header">
  <div class="form-group d-flex align-items-center">
    <img *ngIf="currentPage === 'allCalendar'" src="./assets/images/noun_request.svg" alt="inspection" class="me-2" />
    <div *ngIf="currentPage === 'allCalendar'">
      <select class="styled-select" [(ngModel)]="selectedType"
        (change)="selectedBookingtype($event.target.value , changeConfirmation)">
        <option class="styled-option" *ngFor="let item of bookingTypesList" [value]="item">
          {{ item }}
        </option>
      </select>
    </div>
  </div>
  <h1 *ngIf="currentPage !== 'allCalendar'" class="styled-h1">
    <img src="./assets/images/noun_request.svg" alt="inspection" class="me-2" />New Inspection
    Booking
  </h1>


  <button type="button" class="close ms-auto" aria-label="Close" (click)="close(cancelConfirmation)">
    <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close" /></span>
  </button>
</div>
<div class="modal-body newupdate-alignments taginput--heightfix" *ngIf="!modalLoader">
  <div class="addcalendar-details">
    <form name="form" class="custom-material-form add-concrete-material-form" id="inspectiondetails-form3"
      [formGroup]="deliverDetailsForm" novalidate>
      <div class="row" *ngIf="isBookingTemplate">
        <div class="col-md-12">
          <div class="border-bottom-grey mb-3">
            <label class="fs12 fw600" for="tempname">Template Name<sup>*</sup></label>
            <input type="text" class="form-control fs12 material-input border-0 px-0" formControlName="templateName"  id="tempname"
              placeholder="Template Name" />
            <div class="color-red" *ngIf="submitted && deliverDetailsForm.get('templateName').errors">
              <small *ngIf="deliverDetailsForm.get('templateName').errors.required">*Template Name is Required.</small>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 float-left">
          <div class="form-group">
            <label class="fs12 fw600"  for="des">Description<sup>*</sup></label>
            <textarea class="form-control fs11 radius0" placeholder="Enter Description" rows="2"
              formControlName="description" maxlength="150"></textarea>
            <div class="color-red" *ngIf="submitted && deliverDetailsForm.get('description').errors"  for="des">
              <small *ngIf="deliverDetailsForm.get('description').errors.required">*Description is Required.</small>
            </div>
          </div>
        </div>
        <div class="col-md-6 float-left" *ngIf="!craneEquipmentTypeChosen">
          <div class="form-group mb-0">
            <label class="fs12 fw600"  for="insid">Inspection ID</label>
            <input type="text" class="form-control fs10 color-orange fw500 material-input ps-3" placeholder=""  id="insid"
              value="{{ lastId?.InspectionId }}" disabled="disabled" />
          </div>
        </div>
        <div class="col-md-3 float-left" *ngIf="craneEquipmentTypeChosen">
          <div class="form-group mb-0">
            <label class="fs12 fw600"  for="insid">Inspection ID</label>
            <input type="text" class="form-control fs10 color-orange fw500 material-input ps-3" placeholder="" id="insid"
              value="{{ lastId?.InspectionId }}" disabled="disabled" />
          </div>
        </div>
        <div class="col-md-3 float-left" *ngIf="craneEquipmentTypeChosen">
          <div class="form-group mb-0">
            <label class="fs12 fw600"  for="craneid">Crane Pick Booking ID</label>
            <input type="text" class="form-control fs10 color-orange fw500 material-input ps-3" placeholder=""
              value="{{ craneRequestLastId }}" disabled="disabled"  id="craneid"/>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6 float-left">
          <div class="form-group company-select" id="company-select4">
            <label class="fs12 fw600"  for="rescomp">Responsible Company<sup>*</sup></label>
            <ng-multiselect-dropdown [placeholder]="'Responsible Company*'" [settings]="newNdrCompanyDropdownSettings"
              [data]="companyList" formControlName="companyItems"  id="rescomp">
            </ng-multiselect-dropdown>
            <div class="color-red" *ngIf="submitted && deliverDetailsForm.get('companyItems').errors">
              <small *ngIf="deliverDetailsForm.get('companyItems').errors.required">*Company is Required.</small>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group company-select inspection-form-dflow" id="company-select8">
            <label class="fs12 fw600"  for="defwork">Definable Feature Of Work</label>
            <ng-multiselect-dropdown [placeholder]="'Definable Feature Of Work (Scope)'"  id="defwork"
              [settings]="newNdrDefinableDropdownSettings" [data]="defineList" formControlName="defineItems">
            </ng-multiselect-dropdown>
            <div class="color-red" *ngIf="submitted && deliverDetailsForm.get('defineItems').errors">
              <small *ngIf="deliverDetailsForm.get('defineItems').errors.required">*Definable Feature Of Work is
                Required.</small>
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-12">
          <div class="form-group mb14">
            <label class="fs12 fw600"  for="resperson">Responsible Person<sup>*</sup></label>
            <ul class="follo-switch list-group list-group-horizontal justify-content-end float-right newfixswitch"
              id="switch-control4">
              <li class="fs12 list-group-item border-0 p-0 pe-3 escort--text py-0 float-left">
                Escort Needed?<sup>*</sup>
              </li>
              <li class="list-group-item border-0 px-0 py-0">
                <ui-switch switchColor="#fff" defaultBoColor="#CECECE" defaultBgColor="#CECECE"
                  formControlName="escort"  id="resperson">
                </ui-switch>
              </li>
            </ul>

            <div class="float-start w-100">
              <tag-input formControlName="person" [onlyFromAutocomplete]="true" [placeholder]="' '"
                [onTextChangeDebounce]="500" class="tag-layout newdelivery-taglayout w-100" [identifyBy]="'id'"
                [displayBy]="'email'">
                <tag-input-dropdown [showDropdownIfEmpty]="false" [displayBy]="'email'" [identifyBy]="'id'"
                  [autocompleteObservable]="requestAutocompleteItems" [appendToBody]="false">
                  <ng-template let-item="item" let-index="index">
                    <span class="fs10">{{ item.email }}</span>
                  </ng-template>
                </tag-input-dropdown>
              </tag-input>
            </div>
            <div class="color-red" *ngIf="submitted && deliverDetailsForm.get('person').errors">
              <small *ngIf="deliverDetailsForm.get('person').errors.required">*Please choose Responsible person.</small>
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6 primary-tooltip">
          <div class="form-group mb-0 timezone-formgroup mt-2">
            <label class="fs12 fw600"  for="location">Location<sup>*</sup>
              <div class="dot-border-info location-border-info tooltip location-tooltip">
                <span class="fw700 info-icon fs12">i</span>
                <span class="tooltiptext tooltiptext-info">Where will the materials/equipment be installed</span>
                <div class="arrow-down"></div>
              </div>
            </label>
            <ng-multiselect-dropdown  id="location" [placeholder]="'Choose Location'" [settings]="locationDropdownSettings"
              [data]="locationList" (onSelect)="locationSelected($event)" formControlName="LocationId">
            </ng-multiselect-dropdown>
            <div class="color-red" *ngIf="submitted && deliverDetailsForm.get('LocationId').errors">
              <small *ngIf="deliverDetailsForm.get('LocationId').errors.required">*Location is Required.</small>
            </div>
          </div>
        </div>
        <div class="col-md-6 primary-tooltip">
          <div class="form-group">
            <label class="fs12 fw600 mt-3"  for="gate">Gate<sup>*</sup></label>
            <select class="form-control fs12 material-input px-0 mt-2" formControlName="GateId" (change)="getBookingData()"  id="gate">
              <option value="" disabled selected hidden>Gate<sup>*</sup></option>
              <option *ngFor="let item of gateList" value="{{ item.id }}">
                {{ item.gateName }}
              </option>
            </select>
            <div class="color-red" *ngIf="submitted && deliverDetailsForm.get('GateId').errors">
              <small *ngIf="deliverDetailsForm.get('GateId').errors.required">*Gate is Required.</small>
            </div>
          </div>
        </div>
      </div>


      <div class="row mb-0">
        <div class="col-md-6">
          <div class="form-group my-0 company-select equipment-select inspection-select">
            <label class="fs12 fw600" for="equipment">Equipment<sup>*</sup></label>
            <ng-multiselect-dropdown [placeholder]="'Equipment'" [settings]="equipmentDropdownSettings"  id="equipment"
              [data]="equipmentList" formControlName="EquipmentId" (ngModelChange)="checkEquipmentType($event)">
            </ng-multiselect-dropdown>

            <div class="color-red" *ngIf="submitted && deliverDetailsForm.get('EquipmentId').errors">
              <small *ngIf="deliverDetailsForm.get('EquipmentId').errors.required">*Equipment is Required.</small>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="row my-2" *ngIf="craneEquipmentTypeChosen">
            <div class="col-md-6 float-left">
              <div class="form-group mb-0">
                <label class="fs11 fw600" for="pickfrom">Picking From<sup>*</sup></label>
                <textarea class="form-control fs10 radius0"  id="pickfrom" placeholder="Picking From" rows="2"
                  formControlName="cranePickUpLocation" maxlength="150"></textarea>
              </div>
            </div>
            <div class="col-md-6 float-left">
              <div class="form-group mb-0">
                <label class="fs11 fw600"  for="pickto">Picking To<sup>*</sup></label>
                <textarea class="form-control fs10 radius0"  id="pickto" placeholder="Picking To" rows="2"
                  formControlName="craneDropOffLocation" maxlength="150"></textarea>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="row mt-3">
        <div class="color-red" *ngIf="submitted && deliverDetailsForm.get('inspectionStart').errors">
          <small *ngIf="deliverDetailsForm.get('inspectionStart').errors.required">*Please select a time slot..</small>
        </div>
        <div class="col-md-12">
          <app-time-slot
            #timeSlotRef
            (selectTime)="selectTime($event.start, $event.end)"
            [selectedBookingDate]="deliverDetailsForm.get('inspectionDate').value"
            [timeZone]="timeZone"
            [equipmentId] = "deliverDetailsForm.get('EquipmentId').value"
            [LocationId] = "deliverDetailsForm.get('LocationId').value"
            [selectedBookingType]="'inspectionRequest'"
          >
          </app-time-slot>

        </div>
      </div>

      <div class="row mt-3">
        <div class="col-md-6">
          <!-- recurrance code start -->

          <label class="fs12 fw600 mb-0"  for="recurrence">Recurrence<sup>*</sup></label>
          <div class="form-group">
            <select class="form-control fs12 material-input px-0" formControlName="recurrence" id="recurrence"
              (change)="onRecurrenceSelect($event.target.value)">
              <option *ngFor="let type of recurrence" value="{{ type.value }}">
                {{ type.value }}
              </option>
            </select>
          </div>
          <div class="row mt-2" *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'">
            <div class="col-md-12 mt-md-0"
              *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'">
              <label class="fs12 fw600 mb-0"  for="repevery">Repeat Every</label>
            </div>
            <div class="col-md-6 mt-md-0"
              *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'">
              <div class="form-group">
                <input type="text" formControlName="repeatEveryCount"   id="repevery" class="form-control fs12 material-input p-0"
                  (input)="changeRecurrenceCount($event.target.value)" min="1" />
              </div>
            </div>

            <div class="col-md-6 mt-md-0" *ngIf="isRepeatWithSingleRecurrence">
              <div class="form-group">
                <select class="form-control fs12 material-input px-2" formControlName="repeatEveryType"
                  (change)="chooseRepeatEveryType($event.target.value, null)">
                  <option value="" disabled selected hidden>Select Recurrence</option>
                  <option *ngFor="let type of repeatWithSingleRecurrence" value="{{ type.value }}">
                    {{ type.value }}
                  </option>
                </select>
              </div>
            </div>
            <div class="col-md-4 mt-md-0" *ngIf="isRepeatWithMultipleRecurrence || showRecurrenceTypeDropdown">
              <div class="form-group">
                <select class="form-control fs12 material-input px-2" formControlName="repeatEveryType"
                  (change)="chooseRepeatEveryType($event.target.value, null)">
                  <option value="" disabled selected hidden>Select Recurrence</option>
                  <option *ngFor="let type of repeatWithMultipleRecurrence" value="{{ type.value }}">
                    {{ type.value }}
                  </option>
                </select>
              </div>
            </div>
          </div>
          <div class="row addcalendar-displaydays">
            <div class="col-md-12 pt-0" *ngIf="
                  (selectedRecurrence === 'Weekly' ||
                    isRepeatWithMultipleRecurrence ||
                    isRepeatWithSingleRecurrence) &&
                  selectedRecurrence !== 'Monthly' &&
                  selectedRecurrence !== 'Yearly'
                ">
              <ul class="displaylists ps-0 mb-0">
                <li *ngFor="let item of weekDays; let i = index" class="fs12 list-inline-item">
                  <input type="checkbox" [disabled]="item.isDisabled" [value]="item.value" class="d-none"
                    id="days-{{ i }}" (change)="onChange($event)" [checked]="item.checked" />
                  <label for="days-{{ i }}">{{ item.display }}</label>
                </li>
                <div class="color-red" *ngIf="submitted && deliverDetailsForm.controls['days'].errors">
                  <small *ngIf="deliverDetailsForm.controls['days'].errors.required">*Required
                  </small>
                </div>
              </ul>
            </div>
          </div>
          <div class="row" *ngIf="selectedRecurrence === 'Monthly' || selectedRecurrence === 'Yearly'"
            [ngClass]="selectedRecurrence === 'Yearly' ? 'recurrence-year' : ''">
            <div class="col-md-12 mt-md-0 ps-2 pt-0 recurrance-column-day">
              <div class="w-100 float-left">
                <div class="form-check">
                  <input class="form-check-input c-pointer" type="radio" formControlName="chosenDateOfMonth"
                    id="flexRadioDefault1" [value]="1" (change)="changeMonthlyRecurrence()" />
                  <label class="form-check-label fs12 color-orange" for="flexRadioDefault1">
                    On day {{ monthlyDate }}
                  </label>
                </div>
                <div class="form-check">
                  <input class="form-check-input c-pointer" type="radio" formControlName="chosenDateOfMonth"
                    id="flexRadioDefault2" [value]="2" (change)="changeMonthlyRecurrence()" />
                  <label class="form-check-label fs12 color-orange" for="flexRadioDefault2">
                    On the
                    {{ monthlyDayOfWeek }}
                    <p *ngIf="selectedRecurrence === 'Yearly'">
                      of
                      {{ deliverDetailsForm.get('inspectionDate').value | date : 'LLLL' }}
                    </p>
                  </label>
                </div>
                <div class="form-check" *ngIf="enableOption">
                  <input class="form-check-input c-pointer" type="radio" formControlName="chosenDateOfMonth"
                    id="flexRadioDefault3" [value]="3" (change)="changeMonthlyRecurrence()" />
                  <label class="form-check-label fs12 color-orange" for="flexRadioDefault3">
                    On the
                    {{ monthlyLastDayOfWeek }}
                    <p *ngIf="selectedRecurrence === 'Yearly'">
                      of
                      {{ deliverDetailsForm.get('inspectionDate').value | date : 'LLLL' }}
                    </p>
                  </label>
                </div>
              </div>
              <div>
                <div class="color-red" *ngIf="
                      submitted &&
                      (deliverDetailsForm.get('monthlyRepeatType')?.errors ||
                        deliverDetailsForm.get('dateOfMonth')?.errors)
                    ">
                  <small *ngIf="deliverDetailsForm.get('monthlyRepeatType')?.errors?.required">*required</small>
                  <small *ngIf="deliverDetailsForm.get('dateOfMonth')?.errors?.required">*required</small>
                </div>
              </div>
            </div>
          </div>
          <div class="row addcalendar-displaydays">
            <div class="col-md-12 mt-md-0 pb-0" *ngIf="message">
              <p class="fs12 color-grey11 mb-0">
                <span class="color-red fw-bold">*</span>
                {{ message }}
              </p>
            </div>
          </div>

        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label class="fs12 fw600" for="instype">Inspection Type<sup>*</sup></label>
            <select class="form-control fs12 material-input px-0"  id="instype" formControlName="inspectionType"
              (change)="onChangeInspectionType($event.target.value)">
              <option value="" disabled selected hidden>Select the inspection type</option>
              <option *ngFor="let type of inspectionType" value="{{ type }}">
                {{ type }}
              </option>
            </select>
          </div>
          <div class="color-red" *ngIf="submitted && deliverDetailsForm.get('inspectionType').errors">
            <small *ngIf="deliverDetailsForm.get('inspectionType').errors.required">
              *InspectionType is Required.
            </small>
          </div>

          <div class="row mt-2 mx-0" *ngIf="
                    selectedRecurrence === 'Daily' ||
                    selectedRecurrence === 'Monthly' ||
                    selectedRecurrence === 'Yearly' ||
                    selectedRecurrence === 'Weekly'
                  ">
            <div class="col-md-12 p-0">
              <label class="fs12 fw600" for="enddate">End Date<sup>*</sup></label>
              <div class="input-group mb-3">
                <input class="form-control fs12 fw500 material-input"  id="enddate" #dp="bsDatepicker" bsDatepicker
                  formControlName="endDate" [minDate]="recurrenceMinDate" placement="top" placeholder="End Date *"
                  [bsConfig]="{
                          isAnimated: true,
                          showWeekNumbers: false,
                          customTodayClass: 'today'
                        }" (ngModelChange)="showMonthlyRecurrence()" />
                <span class="input-group-text">
                  <img src="./assets/images/date.svg" class="h-12px" alt="Date" (click)="dp.toggle()" (keydown)="dp.toggle()"
                    [attr.aria-expanded]="dp.isOpen" />
                </span>
              </div>
            </div>
          </div>
        </div>

      </div>

      <div>
        <div class="form-group">
          <label class="fs12 fw600" for="addnotes">Additional Notes</label>
          <textarea class="form-control fs12 min-h-65px" placeholder="" rows="2" formControlName="notes"  id="addnotes"></textarea>
          <div class="color-red" *ngIf="submitted && deliverDetailsForm.get('notes').errors">
            <small *ngIf="deliverDetailsForm.get('notes').errors.required">*Notes is Required.</small>
          </div>
        </div>
      </div>
      <label class="fs12 fw600"  for="cartrac">Carbon Tracking</label>
      <div class="row mb-0 mx-0 pb-3 border-light-gray border-left-green">
        <div class="col-md-6">
          <div class="form-group my-0 company-select equipment-select">
            <label class="fs12 fw600 mt10" for="orgAdd">Origination Address</label>
            <div class="border-bottom-grey">
              <input type="text" class="form-control fs12 material-input border-0 px-0 mt6"  id="orgAdd"
                formControlName="originationAddress" placeholder="Origination Address" ngx-gp-autocomplete
                (onAddressChange)="handleAddressChange($event)" #placesRef="ngx-places" />
            </div>
          </div>
        </div>
        <div class="col-md-6 primary-tooltip">
          <div class="form-group mb-0 timezone-formgroup vehicleType-formgroup mt5">
            <label class="fs12 fw600"  for="vehtype">Vehicle Type</label>
            <ng-multiselect-dropdown [placeholder]="'Choose Vehicle Type'" [settings]="vehicleTypeDropdownSettings"
              [data]="vehicleTypes" (onSelect)="vehicleTypeSelected($event)" formControlName="vehicleType"   id="vehtype">
            </ng-multiselect-dropdown>

          </div>
        </div>
      </div>
      <div class="mb-4 mt35 modal-footers text-center">

        <button class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3 px-2rem" type="button"
          (click)="close(cancelConfirmation)">
          Cancel
        </button>
        <button class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem" (click)="onSubmit()"
          [disabled]="formSubmitted && deliverDetailsForm.valid">
          <em class="fa fa-spinner" aria-hidden="true" *ngIf="formSubmitted && deliverDetailsForm.valid"></em>Submit
        </button>

      </div>
    </form>
  </div>
</div>
<div class="modal-body text-center" *ngIf="modalLoader">Loading...</div>
<!--Confirmation Popup-->
<div id="confirm-popup7">
  <ng-template #cancelConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure you want to cancel?
        </p>
        <button class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetForm('no')">
          No
        </button>
        <button class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5" (click)="resetForm('yes')">
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
<!-- Template Name Pop-up -->
<div id="confirm-popup8">
  <ng-template #openTemplateName>
    <div class="modal-body template-confirm-modal px-0">
      <div class="row text-center m-0 border-bottom-grey pb-2">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-0 text-start col-md-11 col-11 px-0">
          Template
        </p>
        <button type="button" class="close border-0 bg-transparent col-md-1 col-1" aria-label="Close"
          (click)="closeTemplateNamePopup('no')">
          <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close" /></span>
        </button>
      </div>
      <div>
        <div class="form-group w450 mt-3 mb-4">
          <label class="fs12 mb-0" for="tempname">Template Name</label>
          <div class="border-bottom-grey">
            <input type="text" class="form-control fs12 material-input border-0 px-0 w450 fw700"  id="tempname"
              [(ngModel)]="templateName" />
            <div class="color-red" *ngIf="submitted && deliverDetailsForm.get('templateName').errors">
              <small *ngIf="deliverDetailsForm.get('templateName').errors.required">*Template Name is Required.</small>
            </div>
          </div>
        </div>
      </div>
      <div class="text-center">
        <button class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="closeTemplateNamePopup('no')">
          Cancel
        </button>
        <button class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="closeTemplateNamePopup('yes')" [disabled]="formSubmitted && deliverDetailsForm.valid">
          <em class="fa fa-spinner" aria-hidden="true" *ngIf="formSubmitted && deliverDetailsForm.valid"></em>
          Submit
        </button>
      </div>
    </div>
  </ng-template>
</div>

<div id="confirm-popup9">
  <ng-template #changeConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure you want to change the booking type?
        </p>
        <button class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetForm('no')">
          No
        </button>
        <button class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="closeChangeConfirm('yes')">
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
