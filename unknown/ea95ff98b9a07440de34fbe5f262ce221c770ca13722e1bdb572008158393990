/* eslint-disable max-lines-per-function */
import { Component, TemplateRef, OnInit } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import moment from 'moment';
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { Observable } from 'rxjs';
import { Title } from '@angular/platform-browser';
import { ProjectService } from '../services/profile/project.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { EditCraneRequestComponent } from '../crane-requests/edit-crane-request/edit-crane-request.component';
import { InspectionDetailsNewComponent } from './inspection_details/inspection-details-new/inspection-details-new.component';
import { NewinspectionFormComponent } from './inspection_details/new-inspection-form/new-inspection-form.component';
import { EditinspectionFormComponent } from './inspection_details/edit-inspection-form/edit-inspection-form.component';
import {
  inspectionType,
} from '../services/common';

type DateInput = string | number | Date;

@Component({
  selector: 'app-inspection-request',
  templateUrl: './inspection-request.component.html',
})
export class InspectionRequestComponent implements OnInit {
  public currentPageNo = 1;

  public userData = [];

  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public ProjectId: string | number;

  public loader = true;

  public pageSize = 25;

  public pageNo = 1;

  public totalCount = 0;

  public lastId: any = 0;

  public inspectionList: any = [];

  public companyList: any = [];

  public defineList: any = [];

  public defineListData: any = [];

  public gateList: any = [];

  public equipmentList: any = [];

  public currentTemplate: TemplateRef<any>;

  public autocompleteItemsAsObjects: any = [];

  public submitted = false;

  public escort = false;

  public formSubmitted = false;

  public editSubmitted = false;

  public editMultipleSubmitted = false;

  public modalLoader = false;

  public filterCount = 0;

  public formEditSubmitted = false;

  public authUser: any = {};

  public statusValue: any = [];

  public currentinspectionIndex: any = {};

  public currentEditItem: any = {};

  public currentStatus = '';

  public statusSubmitted = false;

  public showStatus = false;

  public search = '';

  public filterForm: FormGroup;

  public memberList: any = [];

  public wholeStatus = ['Approved', 'Declined', 'Completed', 'Pending'];

  public voidSubmitted = false;

  public currentinspectionSaveItem: any = {};

  public inspectionId: number;

  public ParentCompanyId: any;

  public importSubmitted = false;

  public files: NgxFileDropEntry[] = [];

  public formData: any = [];

  public todayDate = new Date();

  public data1: { name: any; values: any }[];

  public workbookData: any;

  public deleteinspectionRequestSubmitted = false;

  public bulkNdrUploadInProgress = false;

  public currentlySelectedTab: string = 'tab0';

  public currentinspectionRequestSelectAll = false;

  public editModalLoader = false;

  public deliverEditMultipleForm: FormGroup;

  public newNdrDefinableDropdownSettings: IDropdownSettings;

  public newNdrCompanyDropdownSettings: IDropdownSettings;

  public selectedinspectionRequestId = '';

  public selectedinspectionRequestIdForMultipleEdit = [];

  public NDRTimingChanged: boolean = false;

  public formEdited = false;

  public inspectionStart: string | Date;

  public inspectionEnd: string | Date;

  public modalRef2: BsModalRef;

  public companyEdited: boolean = false;

  public dfowEdited: boolean = false;

  public escortEdited: boolean = false;

  public responsiblePersonEdited: boolean = false;

  public gateEdited: boolean = false;

  public equipmentEdited: boolean = false;

  public voidEdited: boolean = false;

  public statusEdited: boolean = false;

  public inspectionDateEdited: boolean = false;

  public editedFields = '';

  public showSearchbar = false;

  public param1: any;

  public param2: any;

  public url: any;

  public sortColumn = 'id';

  public sort = 'DESC';

  public selectedinspectionListStatus = '';

  public selectedstatuslength: number;

  public statustype: any;

  public voidvalue = false;

  public approvedBackgroundColor: string;

  public approvedFontColor: string;

  public rejectedBackgroundColor: string;

  public rejectedFontColor: string;

  public deliveredBackgroundColor: string;

  public deliveredFontColor: string;

  public pendingBackgroundColor: string;

  public pendingFontColor: string;

  public expiredBackgroundColor: string;

  public expiredFontColor: string;

  public locationList: any = [];

  public equipmentDropdownSettings: IDropdownSettings;

  public craneEquipmentTypeChosen: boolean = false;

  public isAssociatedWithCraneRequest: boolean = false;

  public inspectionStatusList: any = ['Pass' , 'Fail'];

  public inspectionType = inspectionType

  public noEquipmentOption = { id: 0, equipmentName: 'No Equipment Needed' };

  public constructor(
    private readonly modalService: BsModalService,
    public projectService: ProjectService,
    private readonly formBuilder: FormBuilder,
    public router: Router,
    public socket: Socket,
    private readonly toastr: ToastrService,
    private readonly DeliveryService: DeliveryService,
    private readonly titleService: Title,
    private readonly route: ActivatedRoute,
  ) {
    this.titleService.setTitle('Follo - inspection Bookings');
    /**/
    this.projectService.projectParent.subscribe((response19): void => {
      if (response19 !== undefined && response19 !== null && response19 !== '') {
        this.loader = true;
        this.inspectionList = [];
        this.ProjectId = response19.ProjectId;
        this.ParentCompanyId = response19.ParentCompanyId;
        this.getinspectionRequest();
        this.getOverAllGateInNewinspection();
        this.getMembers();
      }
    });
    this.projectService.ParentCompanyId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ParentCompanyId = res;
      }
    });
    this.DeliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
        if (this.authUser.RoleId === 2 || this.authUser.RoleId === 1) {
          this.statusValue = ['Approved', 'Declined'];
        } else if (this.authUser.RoleId === 3) {
          this.statusValue = ['Delivered', 'Approved'];
        }
      }
    });
    this.DeliveryService.getInspectionCurrentStatus.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.inspectionId = res;
        this.getinspectionRequest();
      }
    });
    this.projectService.fileUpload.subscribe((res): void => {
      if (res && res.status === 'uploading') {
        this.bulkNdrUploadInProgress = true;
      }
      if (res && res.status === 'uploadDone') {
        this.projectService.uploadBulkNdrFile({ status: '' });
        this.bulkNdrUploadInProgress = false;
        this.getinspectionRequest();
      }
    });
    this.filterDetailsForm();
  }

  public ngOnInit(): void {
    this.router.events.subscribe((e): void => {
      if (this.modalRef) {
        this.modalRef.hide();
      }
    });
    this.DeliveryService.inspectionUpdated.subscribe((getNdrResponse): void => {
      if (getNdrResponse !== undefined && getNdrResponse !== null && getNdrResponse !== '') {
        this.getinspectionRequest();
      }
    });
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'clear':
          this.clear();
          break;
        case 'openMod':
          this.openModal1(data);
          break;
        case 'open':
          this.openIdModal(data, item);
          break;
        case 'remove':
          this.removeFile(data);
          break;
        case 'download':
          this.download();
          break;
        default:
          break;
      }
    }
  }

  public handleInputChange(event): void {
    this.voidvalue = false;
    const checkbox = document.getElementById('subscribe') as HTMLInputElement | null;
    if (checkbox.checked === true) {
      this.voidvalue = true;
    } else {
      this.voidvalue = false;
    }
  }

  public sortByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getinspectionRequest();
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.search = '';
    this.pageNo = 1;
    this.filterDetailsForm();
    this.getinspectionRequest();
    this.modalRef.hide();
  }

  public selectStatus(status: string): void {
    this.currentStatus = status;
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('descriptionFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('dateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('companyFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('memberFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('gateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('equipmentFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('locationFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('inspectionStatusFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('inspectionTypeFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('statusFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('pickFrom').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('pickTo').value !== '') {
      this.filterCount += 1;
    }
    this.pageNo = 1;
    this.getinspectionRequest();
    this.modalRef.hide();
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.pageNo = 1;
    this.getinspectionRequest();
  }

  public getSearchNDR(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.pageNo = 1;
    this.search = data;
    this.getinspectionRequest();
  }

  public redirect(path: any): void {
    this.router.navigate([`/${path}`]);
  }

  public getOverAllGateForNdrGrid(): void {
    this.modalLoader = true;
    const getOverAllGateForNdrGridParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .gateList(getOverAllGateForNdrGridParams, { isFilter: true, showActivatedAlone: true })
      .subscribe((getOverAllGateForNdrGridResponse): void => {
        this.gateList = getOverAllGateForNdrGridResponse.data;
        this.getOverAllEquipmentForNdrGrid();
      });
  }

  public getOverAllEquipmentForNdrGrid(): void {
    const getOverAllEquipmentForNdrGridParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listEquipment(getOverAllEquipmentForNdrGridParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((getOverAllEquipmentForNdrGridResponse): void => {
        this.equipmentList = [this.noEquipmentOption, ...getOverAllEquipmentForNdrGridResponse.data];
        this.getCompaniesForNdrGrid();
      });
  }

  public getDefinableForNdrGrid(): void {
    const getDefinableForNdrGridParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getDefinableWork(getDefinableForNdrGridParams)
      .subscribe((getDefinableForNdrGridResponse: any): void => {
        if (getDefinableForNdrGridResponse) {
          const { data } = getDefinableForNdrGridResponse;
          this.defineList = data;
          this.getLocations();
        }
      });
  }

  public getLocations(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getLocations(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.locationList = data;
        this.closePopupContentModal();
      }
    });
  }

  public close(): void {
    this.submitted = false;
    this.formSubmitted = false;
    this.deleteinspectionRequestSubmitted = false;
    this.editSubmitted = false;
    this.formEditSubmitted = false;
    this.modalRef.hide();
  }

  public closeEditMultiplePopup(): void {
    this.formEditSubmitted = false;
    this.modalRef.hide();
    this.editModalLoader = false;
    this.selectedinspectionRequestId = '';
    this.selectedinspectionListStatus = '';
    this.inspectionList.map((obj: any, index: string | number): void => {
      this.inspectionList[index].isChecked = false;
      return null;
    });
    this.currentinspectionRequestSelectAll = false;
    this.selectedinspectionRequestIdForMultipleEdit = [];
    this.editMultipleSubmitted = false;
    this.companyEdited = false;
    this.dfowEdited = false;
    this.escortEdited = false;
    this.responsiblePersonEdited = false;
    this.gateEdited = false;
    this.equipmentEdited = false;
    this.voidEdited = false;
    this.statusEdited = false;
    this.inspectionDateEdited = false;
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public getCompaniesForNdrGrid(): void {
    const getCompaniesForNdrGridParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getCompanies(getCompaniesForNdrGridParams)
      .subscribe((getCompaniesForNdrGridResponse: any): void => {
        if (getCompaniesForNdrGridResponse) {
          this.companyList = getCompaniesForNdrGridResponse.data;
          this.getDefinableForNdrGrid();
        }
      });
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      companyFilter: [''],
      descriptionFilter: [''],
      statusFilter: [''],
      dateFilter: [''],
      memberFilter: [''],
      gateFilter: [''],
      equipmentFilter: [''],
      locationFilter: [''],
      inspectionStatusFilter: [''],
      inspectionTypeFilter: [''],
      pickFrom: [''],
      pickTo: [''],
    });
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }

  public getinspectionRequest(): void {
    this.loader = true;
    this.inspectionList = [];
    const getinspectionRequestParam = {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.pageNo,
      void: 0,
    };
    let getinspectionRequestPayload: any = {};
    if (this.filterForm !== undefined) {
      const getinspectionRequestDateInFilterForm = this.filterForm.value.dateFilter
        ? moment(this.filterForm.value.dateFilter).format('YYYY-MM-DD')
        : this.filterForm.value.dateFilter;
      if (this.filterForm.value.statusFilter === 'Completed') {
        this.filterForm.value.statusFilter = 'Delivered';
      }
      getinspectionRequestPayload = {
        companyFilter: +this.filterForm.value.companyFilter,
        descriptionFilter: this.filterForm.value.descriptionFilter,
        dateFilter: getinspectionRequestDateInFilterForm,
        statusFilter: this.filterForm.value.statusFilter,
        memberFilter: +this.filterForm.value.memberFilter,
        gateFilter: +this.filterForm.value.gateFilter,
        equipmentFilter: this.filterForm.value.equipmentFilter === null || this.filterForm.value.equipmentFilter === '' ? null : +this.filterForm.value.equipmentFilter,
        locationFilter: this.filterForm.value.locationFilter,
        inspectionStatusFilter: this.filterForm.value.inspectionStatusFilter,
        inspectionTypeFilter: this.filterForm.value.inspectionTypeFilter,
        pickFrom: this.filterForm.value.pickFrom,
        pickTo: this.filterForm.value.pickTo,
        search: this.search,
      };
    }
    getinspectionRequestPayload.search = this.search;
    getinspectionRequestPayload.sort = this.sort;
    getinspectionRequestPayload.sortByField = this.sortColumn;
    getinspectionRequestPayload.ParentCompanyId = this.ParentCompanyId;
    getinspectionRequestPayload.queuedNdr = false;
    this.DeliveryService
      .listInspectionNDR(getinspectionRequestParam, getinspectionRequestPayload)
      .subscribe((response: any): void => {
        if (response) {
          const responseData = response.data;
          this.lastId = response.lastId;
          const statusCode = JSON.parse(response?.statusData.statusColorCode);

          const approved = statusCode.find((item) => item.status === 'approved');
          const pending = statusCode.find((item) => item.status === 'pending');
          const delivered = statusCode.find((item) => item.status === 'delivered');
          const rejected = statusCode.find((item) => item.status === 'rejected');
          const expired = statusCode.find((item) => item.status === 'expired');

          this.approvedBackgroundColor = approved.backgroundColor;
          this.approvedFontColor = approved.fontColor;
          this.rejectedBackgroundColor = rejected.backgroundColor;
          this.rejectedFontColor = rejected.fontColor;
          this.expiredBackgroundColor = expired.backgroundColor;
          this.expiredFontColor = expired.fontColor;
          this.deliveredBackgroundColor = delivered.backgroundColor;
          this.deliveredFontColor = delivered.fontColor;
          this.pendingBackgroundColor = pending.backgroundColor;
          this.pendingFontColor = pending.fontColor;


          this.loader = false;
          this.inspectionList = responseData.rows;
          this.inspectionList.map(
            (item: { memberDetails: any }, indexValue: string | number): void => {
              if (this.authUser.RoleId === 4  || this.authUser.RoleId === 3) {
                const responsibleMemberArray = item.memberDetails;
                const index = responsibleMemberArray.findIndex(
                  (i: { Member: { id: any } }): boolean => i.Member.id === this.authUser.id,
                );
                if (index !== -1) {
                  this.inspectionList[indexValue].isAllowedToEdit = true;
                } else {
                  this.inspectionList[indexValue].isAllowedToEdit = false;
                }
              } else {
                this.inspectionList[indexValue].isAllowedToEdit = true;
              }
              return null;
            },
          );
          this.totalCount = responseData.count;
          const isTokenExists = localStorage.getItem('token');
          if (isTokenExists) {
            this.route.queryParams.subscribe((params): any => {
              this.param1 = params.requestId;
              this.param2 = params.memberId;
              this.url = this.router.url.split('?');
              this.router.navigateByUrl(this.url[0]);
              if (this.param1 && this.param2) {
                const object = {
                  encryptedRequestId: this.param1,
                  encryptedMemberId: this.param2,
                  ProjectId: +localStorage.getItem('ProjectId'),
                  ParentCompanyId: +localStorage.getItem('currentCompanyId'),
                };
                this.DeliveryService.decryption(object).subscribe((res): void => {
                  if (res?.data) {
                    this.param1 = '';
                    this.param2 = '';
                    this.url = '';
                    const requestId = res.data.decryptedRequestId;
                    this.DeliveryService.updateStateOfInspectionNDR('current');
                    const newPayload = {
                      id: +requestId,
                      ProjectId: +localStorage.getItem('ProjectId'),
                      ParentCompanyId: +localStorage.getItem('currentCompanyId'),
                    };
                    if (newPayload.ProjectId > 0 && newPayload.ParentCompanyId > 0) {
                      const initialState = {
                        data: newPayload,
                        title: 'Modal with component',
                      };
                      this.modalRef = this.modalService.show(InspectionDetailsNewComponent, {
                        backdrop: 'static',
                        keyboard: false,
                        class: 'modal-lg new-inspection-popup custom-modal',
                        initialState,
                      });
                      this.modalRef.content.closeBtnName = 'Close';
                    }
                  }
                });
              }
            });
          } else {
            this.router.navigate(['/login']);
          }
        }
      });
  }

  public selectAllCurrentinspectionRequestForEdit(): void {
    this.selectedinspectionRequestId = '';
    this.selectedinspectionListStatus = '';
    this.selectedinspectionRequestIdForMultipleEdit = [];
    this.currentinspectionRequestSelectAll = !this.currentinspectionRequestSelectAll;
    if (this.currentinspectionRequestSelectAll) {
      this.inspectionList.map((obj: any, index: string | number): void => {
        if (obj.isAllowedToEdit) {
          this.inspectionList[index].isChecked = true;
          this.selectedinspectionRequestId += `${this.inspectionList[index].inspectionId},`;
          this.selectedinspectionListStatus += `${this.inspectionList[index].status},`;
          this.selectedinspectionRequestIdForMultipleEdit.push(this.inspectionList[index].id);
        } else {
          this.inspectionList[index].isChecked = false;
        }
        return null;
      });
      this.selectedinspectionRequestId = this.selectedinspectionRequestId.replace(/,\s*$/, '');
      const output = this.selectedinspectionListStatus.split(',');
      const arr = output.filter((item) => item);
      const unique = arr.filter((item, i, ar) => ar.indexOf(item) === i);
      this.statustype = unique;
      this.selectedstatuslength = unique.length;
    } else {
      this.inspectionList.map((obj: any, index: string | number): void => {
        this.inspectionList[index].isChecked = false;
        return null;
      });
    }
  }

  public checkIfCurrentinspectionRequestRowSelected(): boolean {
    if (this.currentinspectionRequestSelectAll) {
      return false;
    }
    const indexFind = this.inspectionList.findIndex(
      (item: { isChecked: boolean }): boolean => item.isChecked === true,
    );

    if (indexFind !== -1) {
      return false;
    }
    return true;
  }

  public setSelectedCurrentinspectionRequestItem(index: string | number): void {
    this.inspectionList[index].isChecked = !this.inspectionList[index].isChecked;
    if (this.inspectionList[index].isChecked) {
      this.selectedinspectionRequestId += `${this.inspectionList[index].InspectionId},`;
      this.selectedinspectionRequestIdForMultipleEdit.push(this.inspectionList[index].id);
      this.selectedinspectionListStatus += `${this.inspectionList[index].status},`;
      const output = this.selectedinspectionListStatus.split(',');
      const arr = output.filter((item) => item);
      const unique = arr.filter((item, i, ar) => ar.indexOf(item) === i);
      this.statustype = unique;
      this.selectedstatuslength = unique.length;
    } else {
      const selectedIds = this.selectedinspectionRequestId.replace(
        `${this.inspectionList[index].InspectionId},`,
        '',
      );
      this.selectedinspectionRequestId = '';
      this.selectedinspectionRequestId = selectedIds;
      const ifinspectionIdExists = this.selectedinspectionRequestIdForMultipleEdit.indexOf(
        this.inspectionList[index].id,
      );
      if (ifinspectionIdExists > -1) {
        this.selectedinspectionRequestIdForMultipleEdit.splice(ifinspectionIdExists, 1);
      }
    }
  }

  public setGateEquipment(): void {
    const className = 'modal-lg new-inspection-popup custom-modal';
    this.modalRef = this.modalService.show(NewinspectionFormComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
    });
    this.modalRef.content.lastId = this.lastId;
    this.modalRef.content.closeBtnName = 'Close';
  }

  public openModal(template: TemplateRef<any>): void {
    this.currentTemplate = template;
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-inspection-popup custom-modal',
    };
    this.modalRef = this.modalService.show(this.currentTemplate, data);
  }

  public closePopupContentModal(): void {
    this.modalLoader = false;
  }

  public setStatus(item: { id: any; status: string }): void {
    this.inspectionId = -1;
    this.DeliveryService.updateInspectionRequest(item.id);
    this.currentinspectionIndex = this.inspectionList.findIndex(
      (i: { id: any }): boolean => i.id === item.id,
    );

    this.currentinspectionSaveItem = this.inspectionList[this.currentinspectionIndex];
    const condition = item.status !== 'Expired' && item.status !== 'Delivered';
    if (
      (this.authUser.RoleId === 2 && condition)
      || (this.authUser.RoleId === 3 && item.status === 'Approved')
    ) {
      this.showStatus = true;
    } else {
      this.showStatus = false;
    }
  }

  public openIdModal(item: { id: any; ProjectId: any }, ndrStatus: any): void {
    this.DeliveryService.updateStateOfInspectionNDR(ndrStatus);
    const newPayload = {
      id: item.id,
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    const initialState = {
      data: newPayload,
      title: 'Modal with component',
    };
    this.modalRef = this.modalService.show(InspectionDetailsNewComponent, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-inspection-popup custom-modal',
      initialState,
    });
    this.modalRef.content.closeBtnName = 'Close';
  }

  public openEditModal(item, ndrState: string): void {
    if (this.modalRef) {
      this.close();
    }
    this.DeliveryService.updateStateOfInspectionNDR(ndrState);
    const className = 'modal-lg new-inspection-popup custom-modal';
    if (
      ndrState === 'crane'
      && !item.isAssociatedWithinspectionRequest
      && !item.isAssociatedWithCraneRequest
    ) {
      this.DeliveryService.updatedEditCraneRequestId(+item.CraneRequestId);
      this.modalRef = this.modalService.show(EditCraneRequestComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
      });
    } else {
      this.DeliveryService.updatedInspectionId(item.id);
      this.modalRef = this.modalService.show(EditinspectionFormComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
      });
    }

    this.modalRef.content.closeBtnName = 'Close';
    this.modalRef.content.seriesOption = 1;
    this.modalRef.content.recurrenceId = item?.recurrence ? item?.recurrence?.id : null;
    this.modalRef.content.recurrenceEndDate = item?.recurrence
      ? item?.recurrence?.recurrenceEndDate
      : null;
  }

  public changePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.getinspectionRequest();
  }

  public openModal1(template: TemplateRef<any>): void {
    this.getOverAllGateForNdrGrid();
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-sm filter-popup custom-modal',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public changePageNo(pageNo: number): void {
    this.pageNo = pageNo;
    this.getinspectionRequest();
  }

  public dropped(files: NgxFileDropEntry[]): void {
    if (files.length === 1) {
      this.files = files;
      this.files.forEach((element, i): void => {
        const relativePath = element.relativePath.split('.');
        const extension = relativePath[relativePath.length - 1];
        if (extension === 'xlsx') {
          if (element.fileEntry.isFile) {
            const fileEntry = element.fileEntry as FileSystemFileEntry;
            fileEntry.file((_file: File): void => {
              this.formData = new FormData();
              this.formData.append('inspection_request', _file, element.relativePath);
            });
          }
        } else {
          this.files.splice(i, 1);
          this.toastr.error('Please select a valid file. Supported file format (.xlsx)', 'OOPS!');
        }
      });
    } else {
      this.toastr.error('Please import single file', 'OOPS!');
    }
  }

  public importinspectionRequest(): void {
    this.importSubmitted = true;
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.DeliveryService.importBulkNDR(params, this.formData).subscribe({
      next: (res): void => {
        this.projectService.uploadBulkNdrFile({ status: 'uploading' });
        this.files = [];
        this.importSubmitted = false;
        this.modalRef.hide();
      },
      error: (importinspectionRequestError): void => {
        this.importSubmitted = false;
        if (importinspectionRequestError.message?.statusCode === 400) {
          this.showError(importinspectionRequestError);
        } else if (!importinspectionRequestError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(importinspectionRequestError.message, 'OOPS!');
        }
      },
    });
  }

  public download(): any {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };

    this.DeliveryService.importBulkNDRTemplate(params).subscribe((res: any): void => {
      this.projectService.getProject().subscribe({
        next: (response): any => {
          const project = response.data.filter(
            (data: { id: string | number }): {} => +data.id === +this.ProjectId,
          );
          const fileName = `${project[0].projectName}_${project[0].id}_${new Date().getTime()}`;
          const downloadURL = window.URL.createObjectURL(res);
          const link = document.createElement('a');
          link.href = downloadURL;
          link.download = `${fileName}.xlsx`;
          link.click();
        },
        error: (downloadinspectionRequestError): void => {
          if (downloadinspectionRequestError.message?.statusCode === 400) {
            this.showError(downloadinspectionRequestError);
          } else if (!downloadinspectionRequestError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(downloadinspectionRequestError.message, 'OOPS!');
          }
        },
      });
    });
  }

  public removeFile(i: number): void {
    this.files.splice(i, 1);
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.modalRef.hide();
      this.files = [];
    }
  }

  public closeModal(template: TemplateRef<any>): void {
    if (this.files.length > 0) {
      let data = {};
      data = {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      };
      this.modalRef1 = this.modalService.show(template, data);
    } else {
      this.resetForm('yes');
    }
  }

  public openEditMultipleModal(template: TemplateRef<any>): void {
    this.deliverForm();
    this.setDefaultPerson();
    this.currentTemplate = template;
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-inspection-popup custom-modal edit-multiple-modal',
    };
    this.modalRef = this.modalService.show(this.currentTemplate, data);
    this.selectedinspectionRequestId = this.selectedinspectionRequestId.replace(/,\s*$/, '');
  }

  public deliverForm(): void {
    this.deliverEditMultipleForm = this.formBuilder.group({
      EquipmentId: [this.formBuilder.array([])],
      GateId: [''],
      person: [''],
      inspectionDate: [''],
      inspectionStart: [''],
      inspectionEnd: [''],
      escort: [false],
      companyItems: [this.formBuilder.array([])],
      defineItems: [this.formBuilder.array([])],
      status: [''],
      void: [''],
      cranePickUpLocation: [''],
      craneDropOffLocation: [''],
      inspectionType: ['']
    });
  }

  public getOverAllGateInNewinspection(): void {
    this.modalLoader = true;
    const params = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .gateList(params, { isFilter: true, showActivatedAlone: true })
      .subscribe((res): void => {
        this.gateList = res.data;
        this.getOverAllEquipmentInNewinspection();
      });
  }

  public getOverAllEquipmentInNewinspection(): void {
    const newNdrGetEquipmentsParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listEquipment(newNdrGetEquipmentsParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((equipmentListResponseForNewNdr): void => {
        this.equipmentList = [this.noEquipmentOption, ...equipmentListResponseForNewNdr.data];
        this.equipmentDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'equipmentName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
        this.newNdrgetCompanies();
      });
  }

  public newNdrgetCompanies(): void {
    const newNdrGetCompaniesParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getCompanies(newNdrGetCompaniesParams)
      .subscribe((companiesResponseForNewNdr: any): void => {
        if (companiesResponseForNewNdr) {
          this.companyList = companiesResponseForNewNdr.data;
          this.getDefinable();
          this.newNdrCompanyDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'companyName',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
        }
      });
  }

  public onEditSubmitForm(fieldName: string): void {
    if (fieldName === 'companies') {
      this.companyEdited = true;
    }
    if (fieldName === 'dfow') {
      this.dfowEdited = true;
    }
    if (fieldName === 'escort') {
      this.escortEdited = true;
    }
    if (fieldName === 'persons') {
      this.responsiblePersonEdited = true;
    }
    if (fieldName === 'gate') {
      this.gateEdited = true;
    }
    if (fieldName === 'inspectionDate') {
      if (this.deliverEditMultipleForm.get('inspectionDate').value) {
        this.setDefaultinspectionTime();
        this.inspectionDateEdited = true;
      } else {
        this.inspectionDateEdited = false;
        this.deliverEditMultipleForm.get('inspectionStart').setValue('');
        this.deliverEditMultipleForm.get('inspectionEnd').setValue('');
      }
    }
    if (fieldName === 'void') {
      this.voidEdited = true;
    }
    if (fieldName === 'status') {
      this.statusEdited = true;
    }
  }

  public setDefaultinspectionTime(): void {
    const setStartTime = 7;
    this.inspectionStart = new Date();
    this.inspectionStart.setHours(setStartTime);
    this.inspectionStart.setMinutes(0);
    this.inspectionEnd = new Date();
    this.inspectionEnd.setHours(setStartTime + 1);
    this.inspectionEnd.setMinutes(0);
    this.deliverEditMultipleForm.get('inspectionStart').setValue(this.inspectionStart);
    this.deliverEditMultipleForm.get('inspectionEnd').setValue(this.inspectionEnd);
  }

  public convertStart(inspectionDate: Date, startHours: number, startMinutes: number): string {
    const fullYear = inspectionDate.getFullYear();
    const fullMonth = inspectionDate.getMonth();
    const date = inspectionDate.getDate();
    const inspectionNewStart = new Date(fullYear, fullMonth, date, startHours, startMinutes);
    const inspectionStart = inspectionNewStart.toUTCString();
    return inspectionStart;
  }

  public checkNewinspectionStartEndSame(
    newinspectionStartTime: DateInput,
    newinspectionEndTime: DateInput,
  ): boolean {
    const startDate = new Date(newinspectionStartTime).getTime();
    const endDate = new Date(newinspectionEndTime).getTime();
    if (startDate === endDate) {
      return true;
    }
    return false;
  }

  public setDefaultPerson(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.DeliveryService.getMemberRole(params).subscribe((res): void => {
      this.authUser = res.data;
      this.DeliveryService.updateLoginUser(this.authUser);
      let email: string;
      if (this.authUser.User.lastName != null) {
        email = `${this.authUser.User.firstName} ${this.authUser?.User?.lastName} (${this.authUser.User.email})`;
      } else {
        email = `${this.authUser.User.firstName} (${this.authUser.User.email})`;
      }
      const newMemberList = [
        {
          email,
          id: this.authUser.id,
          readonly: true,
        },
      ];
      this.deliverEditMultipleForm.get('person').patchValue(newMemberList);
    });
  }

  public getDefinable(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getDefinableWork(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.defineListData = data;
        this.newNdrDefinableDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'DFOW',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
      }
    });
  }

  public requestAutocompleteItems = (text: string): Observable<any> => {
    const param = {
      ProjectId: this.ProjectId,
      search: text,
      ParentCompanyId: this.ParentCompanyId,
    };
    return this.DeliveryService.searchNewMember(param);
  };

  public numberOnly(event: { which: any; keyCode: any }): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public changeDate(event: any): void {
    if (!this.editModalLoader) {
      const startTime = new Date(event).getHours();
      const minutes = new Date(event).getMinutes();
      this.inspectionEnd = new Date();
      this.inspectionEnd.setHours(startTime + 1);
      this.inspectionEnd.setMinutes(minutes);
      this.deliverEditMultipleForm.get('inspectionEnd').setValue(this.inspectionEnd);
      this.NDRTimingChanged = true;
    }
  }

  public inspectionEndTimeChangeDetection(): void {
    this.NDRTimingChanged = true;
  }

  public openConfirmationPopup(template: TemplateRef<any>): void {
    this.editedFields = '';

    const appendField = (
      condition: boolean,
      formControlPath: string,
      fieldName: string,
      valueCheck?: (value: any) => boolean,
    ) => {
      const value = this.deliverEditMultipleForm.get(formControlPath)?.value;
      if (condition && value && (!valueCheck || valueCheck(value))) {
        this.editedFields += `${fieldName},`;
      }
    };

    appendField(this.companyEdited, 'companyItems', 'Responsible Company', (value) => value.length > 0);
    appendField(this.dfowEdited, 'defineItems', 'Definable Feature Of Work', (value) => value.length > 0);
    appendField(this.escortEdited, 'escort', 'Escort', (value) => value === true);
    appendField(this.responsiblePersonEdited, 'person', 'Responsible Person', (value) => value.length > 1);
    appendField(this.gateEdited, 'GateId', 'Gate');
    appendField(this.equipmentEdited, 'EquipmentId', 'Equipment', (value) => value.length > 0);
    appendField(this.inspectionDateEdited, 'inspectionDate', 'inspection Date');
    appendField(this.voidEdited, 'void', 'Void');
    appendField(this.statusEdited, 'status', 'Status');

    this.editedFields = this.editedFields.replace(/,\s*$/, '');

    if (this.editedFields) {
      const data = {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      };
      this.modalRef2 = this.modalService.show(template, data);
    }
  }


  public editMultipleConfirmation(action: string): void {
    if (action === 'no') {
      this.modalRef2.hide();
    } else {
      this.modalRef2.hide();
      this.onEditMultipleSubmit();
    }
  }

  public checkStartEnd(
    inspectionStart: DateInput,
    inspectionEnd: DateInput,
  ): boolean {
    const startDate = new Date(inspectionStart).getTime();
    const endDate = new Date(inspectionEnd).getTime();
    if (startDate < endDate) {
      return true;
    }
    return false;
  }

  public checkEditinspectionFutureDate(
    editinspectionStart: DateInput,
    editinspectionEnd: DateInput,
  ): boolean {
    const editStartDate = new Date(editinspectionStart).getTime();
    const editCurrentDate = new Date().getTime();
    const editEndDate = new Date(editinspectionEnd).getTime();
    if (editStartDate > editCurrentDate && editEndDate > editCurrentDate) {
      return true;
    }
    return false;
  }

  public onChangeInspectionType(type: any): void {
    this.deliverEditMultipleForm.get('inspectionType').setValue(type);
  }

  public constructPayload() {
    const payload: any = {
      InspectionRequestIds: this.selectedinspectionRequestIdForMultipleEdit,
      escort: this.deliverEditMultipleForm.get('escort').value,
      GateId: this.deliverEditMultipleForm.get('GateId').value,
      inspectionType: this.deliverEditMultipleForm.get('inspectionType').value,
      inspectionStart: this.inspectionStart,
      inspectionEnd: this.inspectionEnd,
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      editedFields: this.editedFields,
      status: this.deliverEditMultipleForm.get('status').value,
      void: this.voidvalue,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      companies: null,
      persons: null,
      define: null,
      EquipmentId: null,
    };
    if (
      this.deliverEditMultipleForm.get('companyItems').value
      && this.deliverEditMultipleForm.get('companyItems').value.length > 0
    ) {
      const companies = [];
      this.deliverEditMultipleForm
        .get('companyItems')
        .value.forEach((element: { id: any }): void => {
          companies.push(element.id);
        });
      payload.companies = companies;
    }
    if (
      this.deliverEditMultipleForm.get('person').value
      && this.deliverEditMultipleForm.get('person').value.length > 0
    ) {
      const persons = [];
      this.deliverEditMultipleForm.get('person').value.forEach((element: { id: any }): void => {
        persons.push(element.id);
      });
      payload.persons = persons;
    }
    if (
      this.deliverEditMultipleForm.get('defineItems').value
      && this.deliverEditMultipleForm.get('defineItems').value.length > 0
    ) {
      const define = [];
      this.deliverEditMultipleForm
        .get('defineItems')
        .value.forEach((element: { id: any }): void => {
          define.push(element.id);
        });
      payload.define = define;
    }
    if (
      this.deliverEditMultipleForm.get('EquipmentId').value
      && this.deliverEditMultipleForm.get('EquipmentId').value.length > 0
    ) {
      const equipments = [];
      this.deliverEditMultipleForm
        .get('EquipmentId')
        .value.forEach((element: { id: any }): void => {
          equipments.push(element.id);
        });
      payload.EquipmentId = equipments;
    }
    if (this.isAssociatedWithCraneRequest) {
      payload.cranePickUpLocation = this.deliverEditMultipleForm.get('cranePickUpLocation').value.trim();
      payload.craneDropOffLocation = this.deliverEditMultipleForm.get('craneDropOffLocation').value.trim();
      payload.isAssociatedWithCraneRequest = true;
    } else {
      payload.cranePickUpLocation = null;
      payload.craneDropOffLocation = null;
      payload.isAssociatedWithCraneRequest = false;
    }

    return payload;
  }

  public onEditMultipleSubmit(): void {
    if (this.inspectionDateEdited) {
    const inspectionDate = new Date(this.deliverEditMultipleForm.get('inspectionDate').value);
    const startHours = new Date(this.deliverEditMultipleForm.get('inspectionStart').value).getHours();
    const startMinutes = new Date(
      this.deliverEditMultipleForm.get('inspectionStart').value,
    ).getMinutes();
    this.inspectionStart = this.convertStart(inspectionDate, startHours, startMinutes);
    const endHours = new Date(this.deliverEditMultipleForm.get('inspectionEnd').value).getHours();
    const endMinutes = new Date(this.deliverEditMultipleForm.get('inspectionEnd').value).getMinutes();
    this.inspectionEnd = this.convertStart(inspectionDate, endHours, endMinutes);
      if (this.checkNewinspectionStartEndSame(this.inspectionStart, this.inspectionEnd)) {
        this.toastr.error('inspection Start time and End time should not be the same');
        return;
      }
      if (!this.checkStartEnd(this.inspectionStart, this.inspectionEnd)) {
        this.toastr.error('Please Enter Start time Lesser than End time');
        return;
      }
      if (!this.checkEditinspectionFutureDate(this.inspectionStart, this.inspectionEnd)) {
        this.toastr.error('Please Enter Future Date.');
        return;
      }
    }else{
      this.inspectionStart=null;
      this.inspectionEnd=null;
    }
    if (this.isAssociatedWithCraneRequest) {
      if (
        !this.deliverEditMultipleForm.get('cranePickUpLocation').value ||
        !this.deliverEditMultipleForm.get('craneDropOffLocation').value
      ) {
        this.formSubmitted = false;
        this.toastr.error('Please enter Picking From and Picking To');
        return;
      }
    }
    const payload = this.constructPayload();
    this.editMultipleSubmitted = true;
    this.DeliveryService.updateInspectionRequest(payload).subscribe({
      next: (editNdrResponse: any): void => {
        if (editNdrResponse) {
          this.editNDRSuccess(editNdrResponse);
        }
      },
      error: (editNDRHistoryError): void => {
        this.showErrorMessage(editNDRHistoryError);
      },
    });
  }

  public showErrorMessage(editNDRHistoryError): void {
    this.editMultipleSubmitted = false;
    if (editNDRHistoryError.message?.statusCode === 400) {
      this.showError(editNDRHistoryError);
    } else if (!editNDRHistoryError.message) {
      this.toastr.error('Try again later.!', 'Something went wrong.');
    } else {
      this.toastr.error(editNDRHistoryError.message, 'OOPS!');
    }
  }

  public editNDRSuccess(response: { message: string }): void {
    this.toastr.success(response.message, 'Success');
    this.socket.emit('NDREditHistory', response);
    this.deliverForm();
    this.DeliveryService.updatedInspectionHistory({ status: true }, 'NDREditHistory');
    this.NDRTimingChanged = false;
    this.companyEdited = false;
    this.dfowEdited = false;
    this.escortEdited = false;
    this.responsiblePersonEdited = false;
    this.gateEdited = false;
    this.equipmentEdited = false;
    this.voidEdited = false;
    this.statusEdited = false;
    this.inspectionDateEdited = false;
    this.modalRef.hide();
    this.currentinspectionRequestSelectAll = false;
    this.editMultipleSubmitted = false;
    this.voidvalue = false;
    this.selectedinspectionRequestId = '';
    this.craneEquipmentTypeChosen = false;
    this.isAssociatedWithCraneRequest = false;
    this.deliverEditMultipleForm.reset();
    this.selectedinspectionRequestIdForMultipleEdit = [];
  }

  public checkEquipmentType(event: any): void {
    let findEquipmentType: any;

    if (event && event.length > 0) {
      this.equipmentEdited = true;
      const hasCraneType = event.some((item) => {
        findEquipmentType = this.equipmentList.find((equipment) => equipment.id === item.id);
        if (findEquipmentType) {
          this.craneEquipmentTypeChosen = findEquipmentType.PresetEquipmentType.isCraneType;
          return this.craneEquipmentTypeChosen; // stops iteration if true
        }
        return false;
      });
      this.isAssociatedWithCraneRequest = hasCraneType;
    } else {
      this.craneEquipmentTypeChosen = false;
    }
  }
}
