import {
  Component, EventEmitter, Input, OnChanges, Output, SimpleChanges, ViewChild,
} from '@angular/core';
import { GoogleMap } from '@angular/google-maps';

@Component({
  selector: 'app-home-page-map',
  templateUrl: './home-page-map.component.html',
})
export class HomePageMapComponent implements OnChanges {
  @ViewChild(GoogleMap, { static: false }) map!: GoogleMap;

  @Input() addressMarker: any;

  @Input() emitLatitude: any;

  @Input() emitLongitude: any;

  @Output() latLongProps: EventEmitter<any> = new EventEmitter();


  public latitude = 38.897957;

  public longitude = -77.036560;

  public mapOptions: google.maps.MapOptions = {
    center: { lat: this.latitude, lng: this.longitude },
    zoomControl: true,
    mapTypeControl: true,
    streetViewControl: false,
    fullscreenControl: true,
    draggable: true,
    zoom: 18,
    mapTypeId: google.maps.MapTypeId.SATELLITE,
  };

  public marker = {
    position: { lat: this.latitude, lng: this.longitude },
    options: {
      animation: google.maps.Animation.DROP,
      draggable: true,
    },
  };

  ngOnChanges(changes: SimpleChanges): void {
    this.updateMap(changes.emitLatitude, changes.emitLongitude);
  }

  public updateMap(data1: any, data2: any): void {
    this.latitude = data1.currentValue;
    this.longitude = data2.currentValue;
    this.mapOptions.center = {
      lat: this.latitude,
      lng: this.longitude,
    };
    this.marker.position = {
      lat: this.latitude,
      lng: this.longitude,
    };
    this.map.panTo(this.marker.position);
  }

  public markerDragEnd(event: google.maps.MapMouseEvent) {
    if (event.latLng) {
      this.latitude = event.latLng.lat();
      this.longitude = event.latLng.lng();
      this.mapOptions.center = {
        lat: this.latitude,
        lng: this.longitude,
      };
      this.marker.position = {
        lat: this.latitude,
        lng: this.longitude,
      };
      this.latLongProps.emit(this.marker.position);
      this.map.panTo(this.marker.position);
    }
  }

  ngAfterViewInit() {
    this.map.panTo(this.marker.position);
  }
}
