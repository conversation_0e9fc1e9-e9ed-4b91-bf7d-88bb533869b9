import { ComponentFixture, TestBed } from '@angular/core/testing';
import { InspectionDetailsNewComponent } from './inspection-details-new.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Router, NavigationEnd } from '@angular/router';
import { Socket } from 'ngx-socket-io';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';
import { of, BehaviorSubject, throwError } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('InspectionDetailsNewComponent', () => {
  let component: InspectionDetailsNewComponent;
  let fixture: ComponentFixture<InspectionDetailsNewComponent>;
  let modalServiceSpy: jest.Mocked<BsModalService>;
  let deliveryServiceSpy: jest.Mocked<DeliveryService>;
  let projectServiceSpy: jest.Mocked<ProjectService>;
  let toastrServiceSpy: jest.Mocked<ToastrService>;
  let routerSpy: jest.Mocked<Router>;
  let socketSpy: jest.Mocked<Socket>;
  let getInspectionNDRDataMock: jest.Mock;

  beforeEach(async () => {
    const modalSpy = {
      show: jest.fn(),
    };
    getInspectionNDRDataMock = jest.fn().mockReturnValue(of({ data: {} }));
    const deliverySpy = {
      loginUser: new BehaviorSubject({ RoleId: 2 }),
      getInspectionCurrentStatus: new BehaviorSubject(1),
      inspectionUpdated: new BehaviorSubject(true),
      getInspectionNDRData: getInspectionNDRDataMock,
      updatedInspectionId: jest.fn(),
      isQueuedInspectionNDR: new BehaviorSubject(''),
      updatedInspectionCurrentStatus: jest.fn(),
    };
    const projectSpy = {
      projectParent: new BehaviorSubject({ ProjectId: 1, ParentCompanyId: 1 }),
      gateList: jest.fn().mockReturnValue(of({ data: [] })),
      listEquipment: jest.fn().mockReturnValue(of({ data: [] })),
      listAllMember: jest.fn().mockReturnValue(of({ data: [] })),
      isMyAccount: new BehaviorSubject(true),
      isProject: new BehaviorSubject(true),
      isAccountAdmin: new BehaviorSubject(false),
      updateAccountProjectParent: jest.fn(),
    };
    const toastrSpy = {
      clear: jest.fn(),
      error: jest.fn(),
      success: jest.fn(),
    };
    const router = {
      navigate: jest.fn(),
      events: new BehaviorSubject({}),
    };
    const socket = {
      on: jest.fn(),
      emit: jest.fn(),
    };

    await TestBed.configureTestingModule({
      declarations: [InspectionDetailsNewComponent],
      providers: [
        { provide: BsModalService, useValue: modalSpy },
        { provide: DeliveryService, useValue: deliverySpy },
        { provide: ProjectService, useValue: projectSpy },
        { provide: ToastrService, useValue: toastrSpy },
        { provide: Router, useValue: router },
        { provide: Socket, useValue: socket },
        { provide: BsModalRef, useValue: { hide: jest.fn() } },
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    modalServiceSpy = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    deliveryServiceSpy = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    projectServiceSpy = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    toastrServiceSpy = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    routerSpy = TestBed.inject(Router) as jest.Mocked<Router>;
    socketSpy = TestBed.inject(Socket) as jest.Mocked<Socket>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(InspectionDetailsNewComponent);
    component = fixture.componentInstance;

    // Setup component data
    component.data = {
      inspectionStart: new Date('2024-12-01T10:00:00'),
      ParentCompanyId: '456',
      ProjectId: '123',
      id: 1,
    };

    // Setup component properties
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.authUser = {
      id: 'user123',
      RoleId: 2,
      UserId: 'user123',
      name: 'Test User'
    };
    component.inspectionRequestId = 1;
    component.currentinspectionSaveItem = {
      id: 1,
      status: 'Pending',
      inspectionStatus: 'Pass',
      memberDetails: [
        { Member: { id: 'user123', name: 'Test User' } }
      ],
      gateDetails: [
        { Gate: { id: 'gate1', name: 'Gate 1' } }
      ],
      equipmentDetails: [
        { Equipment: { id: 'eq1', name: 'Equipment 1' } }
      ],
      voidList: [],
      createdUserDetails: {
        RoleId: 4,
        User: { id: 'user123' }
      },
      recurrence: {
        id: 'rec1',
        recurrence: 'Daily',
        recurrenceEndDate: '2024-12-31'
      }
    };

    // Setup lists
    component.gateList = [
      { id: 'gate1', name: 'Gate 1' }
    ];
    component.equipmentList = [
      { id: 'eq1', name: 'Equipment 1' }
    ];
    component.memberList = [
      { id: 'user123', name: 'Test User' }
    ];

    // Setup modal refs
    component.modalRef = { hide: jest.fn() } as any;
    component.modalRef1 = { hide: jest.fn() } as any;
    component.modalRef2 = { hide: jest.fn() } as any;
    component.modalRef3 = { hide: jest.fn() } as any;

    fixture.detectChanges();
  });

  const setupInspectionData = (customData = {}) => {
    const defaultData = {
      memberDetails: [
        { Member: { id: 'user123', name: 'Test User' } }
      ],
      voidList: [],
      inspectionStatus: 'Pass',
      status: 'Pending',
      createdUserDetails: {
        RoleId: 4,
        User: { id: 'user123' }
      },
      edit: false,
      ...customData
    };

    getInspectionNDRDataMock.mockReturnValue(of({
      data: defaultData
    }));

    return defaultData;
  };

  // ===== BASIC COMPONENT TESTS =====

  it('should create', () => {
    setupInspectionData();
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.showStatus).toBeFalsy();
    expect(component.statusSubmitted).toBeFalsy();
    expect(component.voidSubmitted).toBeFalsy();
    expect(component.void).toBeFalsy();
    expect(component.show).toBeFalsy();
    expect(component.currentTabId).toBe(0);
    expect(component.statusChanged).toBeFalsy();
    expect(component.inspectionStatusChanged).toBeFalsy();
    expect(component.isQueuedNDR).toBeFalsy();
    expect(component.allRequestIsOpened).toBeFalsy();
  });

  it('should set status values based on user role - Role 2', () => {
    component.authUser = { RoleId: 2 };
    setupInspectionData();
    expect(component.statusValue).toEqual(['Approved', 'Declined']);
    expect(component.inspectionStatusValue).toEqual(['Pass', 'Fail']);
  });

  it('should set status values based on user role - Role 1', () => {
    component.authUser = { RoleId: 1 };
    setupInspectionData();
    expect(component.statusValue).toEqual(['Approved', 'Declined']);
    expect(component.inspectionStatusValue).toEqual(['Pass', 'Fail']);
  });

  it('should set status values based on user role - Role 3', () => {
    component.authUser = { RoleId: 3 };
    setupInspectionData();
    expect(component.statusValue).toEqual(['Delivered']);
  });

  // ===== STATUS SELECTION TESTS =====

  describe('selectInspectionStatus', () => {
    it('should update currentInspectionStatus and set inspectionStatusChanged to true', () => {
      setupInspectionData();
      component.selectInspectionStatus('Pass');
      expect(component.currentInspectionStatus).toBe('Pass');
      expect(component.inspectionStatusChanged).toBeTruthy();
    });

    it('should handle Fail status', () => {
      setupInspectionData();
      component.selectInspectionStatus('Fail');
      expect(component.currentInspectionStatus).toBe('Fail');
      expect(component.inspectionStatusChanged).toBeTruthy();
    });
  });

  describe('selectStatus', () => {
    it('should update currentStatus and set statusChanged to true', () => {
      setupInspectionData();
      component.selectStatus('Approved');
      expect(component.currentStatus).toBe('Approved');
      expect(component.statusChanged).toBeTruthy();
    });

    it('should handle Declined status', () => {
      setupInspectionData();
      component.selectStatus('Declined');
      expect(component.currentStatus).toBe('Declined');
      expect(component.statusChanged).toBeTruthy();
    });

    it('should handle Delivered status', () => {
      setupInspectionData();
      component.selectStatus('Delivered');
      expect(component.currentStatus).toBe('Delivered');
      expect(component.statusChanged).toBeTruthy();
    });
  });

  describe('eventCheck', () => {
    it('should set currentStatus to Delivered when checked', () => {
      setupInspectionData();
      component.eventCheck({ target: { checked: true } });
      expect(component.currentStatus).toBe('Delivered');
      expect(component.statusChanged).toBeTruthy();
    });

    it('should clear currentStatus when unchecked', () => {
      setupInspectionData();
      component.eventCheck({ target: { checked: false } });
      expect(component.currentStatus).toBe('');
    });
  });

  // ===== UTILITY TESTS =====

  describe('clickAndDisable', () => {
    it('should disable subsequent clicks on link', () => {
      const mockLink = {
        onclick: null
      };

      component.clickAndDisable(mockLink);

      expect(mockLink.onclick).toBeDefined();

      // Test the onclick function
      const mockEvent = { preventDefault: jest.fn() };
      mockLink.onclick(mockEvent);
      expect(mockEvent.preventDefault).toHaveBeenCalled();
    });
  });

  describe('changeRequestCollapse', () => {
    it('should initialize series options and toggle allRequestIsOpened', () => {
      const mockData = {
        inspectionStart: new Date('2025-01-01T10:00:00') // Future date
      };

      component.allRequestIsOpened = false;
      component.changeRequestCollapse(mockData);

      expect(component.allRequestIsOpened).toBeTruthy();
      expect(component.seriesOptions).toHaveLength(2);
      expect(component.seriesOptions[0]).toEqual({
        option: 1,
        text: 'This event',
        disabled: false
      });
    });

    it('should disable series options for past dates', () => {
      const mockData = {
        inspectionStart: new Date('2020-01-01T10:00:00') // Past date
      };

      component.changeRequestCollapse(mockData);

      expect(component.seriesOptions[1].disabled).toBeTruthy();
    });
  });

  describe('initializeSeriesOption', () => {
    it('should initialize series options correctly', () => {
      component.initializeSeriesOption();

      expect(component.seriesOptions).toHaveLength(2);
      expect(component.seriesOptions[0]).toEqual({
        option: 1,
        text: 'This event',
        disabled: false
      });
      expect(component.seriesOptions[1]).toEqual({
        option: 2,
        text: 'This and all following events',
        disabled: false
      });
    });
  });

  // ===== SERVICE INTEGRATION TESTS =====

  describe('getOverAllGate', () => {
    it('should call projectService.gateList with correct parameters', () => {
      setupInspectionData();
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.getOverAllGate();
      expect(projectServiceSpy.gateList).toHaveBeenCalledWith(
        {
          ProjectId: '123',
          pageSize: 0,
          pageNo: 0,
          ParentCompanyId: '456',
        },
        { isFilter: true, showActivatedAlone: true },
      );
    });

    it('should update gateList on successful response', () => {
      const mockGates = [{ id: 'gate1', name: 'Gate 1' }];
      projectServiceSpy.gateList.mockReturnValue(of({ data: mockGates }));

      component.getOverAllGate();

      expect(component.gateList).toEqual(mockGates);
    });
  });

  describe('getOverAllEquipmentforEditNdr', () => {
    it('should call projectService.listEquipment with correct parameters', () => {
      setupInspectionData();
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.getOverAllEquipmentforEditNdr();
      expect(projectServiceSpy.listEquipment).toHaveBeenCalledWith(
        {
          ProjectId: '123',
          pageSize: 0,
          pageNo: 0,
          ParentCompanyId: '456',
        },
        { isFilter: true, showActivatedAlone: true },
      );
    });

    it('should update equipmentList on successful response', () => {
      const mockEquipment = [{ id: 'eq1', name: 'Equipment 1' }];
      projectServiceSpy.listEquipment.mockReturnValue(of({ data: mockEquipment }));

      component.getOverAllEquipmentforEditNdr();

      expect(component.equipmentList).toEqual(mockEquipment);
    });
  });

  describe('getMembers', () => {
    it('should call projectService.listAllMember with correct parameters', () => {
      setupInspectionData();
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.getMembers();
      expect(projectServiceSpy.listAllMember).toHaveBeenCalledWith({
        ProjectId: '123',
        ParentCompanyId: '456',
      });
    });

    it('should update memberList on successful response', () => {
      const mockMembers = [{ id: 'user1', name: 'User 1' }];
      projectServiceSpy.listAllMember.mockReturnValue(of({ data: mockMembers }));

      component.getMembers();

      expect(component.memberList).toEqual(mockMembers);
    });
  });

  describe('getNDR', () => {
    it('should call getInspectionNDRData with correct parameters', () => {
      component.inspectionRequestId = 1;
      component.ParentCompanyId = '456';

      component.getNDR();

      expect(deliveryServiceSpy.getInspectionNDRData).toHaveBeenCalledWith({
        inspectionRequestId: 1,
        ParentCompanyId: '456'
      });
    });

    it('should update component properties on successful response', () => {
      const mockData = setupInspectionData({
        inspectionStatus: 'Pass',
        memberDetails: [{ Member: { id: 'user123' } }],
        voidList: []
      });

      component.authUser = { id: 'user123', RoleId: 4 };
      component.getNDR();

      expect(component.currentinspectionSaveItem).toEqual(mockData);
      expect(component.void).toBeFalsy();
      expect(component.show).toBeTruthy();
      expect(component.currentTabId).toBe(0);
      expect(component.currentInspectionStatusValue).toBe('Pass');
    });

    it('should set void to true if user is in voidList', () => {
      setupInspectionData({
        voidList: [{ MemberId: 'user123' }]
      });

      component.authUser = { id: 'user123', RoleId: 4 };
      component.getNDR();

      expect(component.void).toBeTruthy();
    });

    it('should set edit permission for role 4 users in memberDetails', () => {
      setupInspectionData({
        memberDetails: [{ Member: { id: 'user123' } }]
      });

      component.authUser = { id: 'user123', RoleId: 4 };
      component.getNDR();

      expect(component.currentinspectionSaveItem.edit).toBeTruthy();
    });

    it('should not set edit permission for role 4 users not in memberDetails', () => {
      setupInspectionData({
        memberDetails: [{ Member: { id: 'otheruser' } }]
      });

      component.authUser = { id: 'user123', RoleId: 4 };
      component.getNDR();

      expect(component.currentinspectionSaveItem.edit).toBeFalsy();
    });

    it('should set edit permission for role 3 users', () => {
      setupInspectionData();

      component.authUser = { id: 'user123', RoleId: 3 };
      component.getNDR();

      expect(component.currentinspectionSaveItem.edit).toBeTruthy();
    });

    it('should set edit permission for role 2 users', () => {
      setupInspectionData();

      component.authUser = { id: 'user123', RoleId: 2 };
      component.getNDR();

      expect(component.currentinspectionSaveItem.edit).toBeTruthy();
    });

    it('should set edit permission for role 1 users', () => {
      setupInspectionData();

      component.authUser = { id: 'user123', RoleId: 1 };
      component.getNDR();

      expect(component.currentinspectionSaveItem.edit).toBeTruthy();
    });
  });

  // ===== MODAL TESTS =====

  describe('openModal2', () => {
    it('should call modalService.show with correct parameters', () => {
      setupInspectionData();
      const template = {} as any;
      component.openModal2(template);
      expect(modalServiceSpy.show).toHaveBeenCalledWith(
        template,
        { backdrop: 'static', keyboard: false, class: 'modal-md thanks-popup custom-modal' },
      );
    });

    it('should set modalRef from service response', () => {
      const mockModalRef = { hide: jest.fn() };
      modalServiceSpy.show.mockReturnValue(mockModalRef as any);

      const template = {} as any;
      component.openModal2(template);

      expect(component.modalRef).toBe(mockModalRef);
    });
  });

  describe('openModal', () => {
    it('should call modalService.show with correct parameters', () => {
      const template = {} as any;
      component.openModal(template);

      expect(modalServiceSpy.show).toHaveBeenCalledWith(
        template,
        {
          keyboard: false,
          class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
        }
      );
    });
  });

  describe('openConfirmationModalPopupForDetailsNDR', () => {
    it('should call modalService.show with correct parameters', () => {
      const template = {} as any;
      component.openConfirmationModalPopupForDetailsNDR(template);

      expect(modalServiceSpy.show).toHaveBeenCalledWith(
        template,
        {
          keyboard: false,
          class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
        }
      );
    });

    it('should set modalRef1 from service response', () => {
      const mockModalRef = { hide: jest.fn() };
      modalServiceSpy.show.mockReturnValue(mockModalRef as any);

      const template = {} as any;
      component.openConfirmationModalPopupForDetailsNDR(template);

      expect(component.modalRef1).toBe(mockModalRef);
    });
  });

  // ===== STATUS UPDATE TESTS =====

  describe('saveStatus', () => {
    beforeEach(() => {
      component.currentinspectionSaveItem = {
        id: 1,
        gateDetails: [{ Gate: { id: 'gate1' } }],
        equipmentDetails: [{ Equipment: { id: 'eq1' } }],
        memberDetails: [{ Member: { id: 'user123', name: 'Test User' } }]
      };
      component.gateList = [{ id: 'gate1', name: 'Gate 1' }];
      component.equipmentList = [{ id: 'eq1', name: 'Equipment 1' }];
      component.memberList = [{ id: 'user123', name: 'Test User' }];
      component.ParentCompanyId = '456';
    });

    it('should show error toast when no status is chosen', () => {
      component.currentStatus = '';
      component.modalRef = { hide: jest.fn() } as any;

      component.saveStatus();

      expect(toastrServiceSpy.clear).toHaveBeenCalled();
      expect(toastrServiceSpy.error).toHaveBeenCalledWith('No status chosen to save');
      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should prevent multiple submissions', () => {
      component.currentStatus = 'Approved';
      component.statusSubmitted = true;

      component.saveStatus();

      expect(deliveryServiceSpy.updateInspectionStatus).not.toHaveBeenCalled();
    });

    it('should hide modal for single status value', () => {
      component.currentStatus = 'Delivered';
      component.statusValue = ['Delivered'];
      component.modalRef = { hide: jest.fn() } as any;

      component.saveStatus();

      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should call openModalIndex with correct parameters', () => {
      component.currentStatus = 'Approved';
      component.statusSubmitted = false;
      jest.spyOn(component, 'openModalIndex').mockImplementation(() => {});

      component.saveStatus();

      expect(component.statusSubmitted).toBeTruthy();
      expect(component.openModalIndex).toHaveBeenCalledWith(
        {
          id: 1,
          status: 'Approved',
          ParentCompanyId: '456'
        },
        0, // gate index
        0, // equipment index
        [{ id: 'user123', name: 'Test User' }] // member array
      );
    });
  });

  describe('updateInspectionStatus', () => {
    beforeEach(() => {
      component.currentinspectionSaveItem = {
        id: 1,
        status: 'Approved'
      };
      component.currentInspectionStatus = 'Pass';
      component.ParentCompanyId = '456';
      component.authUser = { RoleId: 2 };
      deliveryServiceSpy.updateInspectionStatus = jest.fn().mockReturnValue(of({ message: 'Success' }));
    });

    it('should set gatesubmit to true and call updateInspectionStatus', () => {
      component.statusSubmitted = false;

      component.updateInspectionStatus();

      expect(component.gatesubmit).toBeTruthy();
      expect(component.statusSubmitted).toBeTruthy();
      expect(deliveryServiceSpy.updateInspectionStatus).toHaveBeenCalledWith({
        id: 1,
        status: 'Approved',
        inspectionStatus: 'Pass',
        ParentCompanyId: '456'
      });
    });

    it('should handle successful response', () => {
      const mockResponse = { message: 'Status updated successfully' };
      deliveryServiceSpy.updateInspectionStatus.mockReturnValue(of(mockResponse));

      component.updateInspectionStatus();

      expect(component.gatesubmit).toBeFalsy();
      expect(toastrServiceSpy.success).toHaveBeenCalledWith('Status updated successfully', 'Success');
      expect(socketSpy.emit).toHaveBeenCalledWith('InspectionNDRApproveHistory', mockResponse);
      expect(deliveryServiceSpy.updatedInspectionHistory).toHaveBeenCalledWith({ status: true }, 'NDRApproveHistory');
      expect(component.statusSubmitted).toBeFalsy();
    });

    it('should prevent multiple submissions', () => {
      component.statusSubmitted = true;

      component.updateInspectionStatus();

      expect(deliveryServiceSpy.updateInspectionStatus).not.toHaveBeenCalled();
    });

    it('should hide modal for single status value', () => {
      component.statusValue = ['Delivered'];
      component.modalRef = { hide: jest.fn() } as any;

      component.updateInspectionStatus();

      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should handle error response with status code 400', () => {
      const errorResponse = { message: { statusCode: 400, details: [{ error: 'Bad request' }] } };
      deliveryServiceSpy.updateInspectionStatus.mockReturnValue(throwError(errorResponse));
      jest.spyOn(component, 'showError').mockImplementation(() => {});

      component.updateInspectionStatus();

      expect(component.statusSubmitted).toBeFalsy();
      expect(component.statusChanged).toBeFalsy();
      expect(component.showError).toHaveBeenCalledWith(errorResponse);
    });

    it('should handle error response without message', () => {
      const errorResponse = {};
      deliveryServiceSpy.updateInspectionStatus.mockReturnValue(throwError(errorResponse));

      component.updateInspectionStatus();

      expect(toastrServiceSpy.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should handle error response with message', () => {
      const errorResponse = { message: 'Custom error message' };
      deliveryServiceSpy.updateInspectionStatus.mockReturnValue(throwError(errorResponse));
      component.modalRef = { hide: jest.fn() } as any;

      component.updateInspectionStatus();

      expect(toastrServiceSpy.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
      expect(component.modalRef.hide).toHaveBeenCalled();
    });
  });

  describe('gatestatus', () => {
    beforeEach(() => {
      component.currentinspectionSaveItem = { id: 1 };
      component.currentStatus = 'Approved';
      component.ParentCompanyId = '456';
      component.authUser = { RoleId: 2 };
      deliveryServiceSpy.updateInspectionStatus = jest.fn().mockReturnValue(of({ message: 'Success' }));
    });

    it('should handle "yes" action and update status', () => {
      component.gatestatus('yes');

      expect(component.gatesubmit).toBeTruthy();
      expect(deliveryServiceSpy.updateInspectionStatus).toHaveBeenCalledWith({
        id: 1,
        status: 'Approved',
        ParentCompanyId: '456'
      });
    });

    it('should handle successful status update', () => {
      const mockResponse = { message: 'Status updated successfully' };
      deliveryServiceSpy.updateInspectionStatus.mockReturnValue(of(mockResponse));
      component.modalRef = { hide: jest.fn() } as any;

      component.gatestatus('yes');

      expect(component.gatesubmit).toBeFalsy();
      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(toastrServiceSpy.success).toHaveBeenCalledWith('Status updated successfully', 'Success');
      expect(socketSpy.emit).toHaveBeenCalledWith('InspectionNDRApproveHistory', mockResponse);
      expect(deliveryServiceSpy.updatedInspectionHistory).toHaveBeenCalledWith({ status: true }, 'NDRApproveHistory');
      expect(deliveryServiceSpy.updateCraneRequestHistory).toHaveBeenCalledWith({ status: true }, 'historyUpdate');
      expect(component.statusSubmitted).toBeFalsy();
      expect(component.currentinspectionSaveItem.status).toBe('Approved');
      expect(component.statusChanged).toBeFalsy();
    });

    it('should handle "no" action and open edit modal', () => {
      component.modalRef = { hide: jest.fn(), content: {} } as any;
      component.bsModalRef = { hide: jest.fn() } as any;
      const mockEditModalRef = {
        content: {
          closeBtnName: '',
          seriesOption: 1,
          recurrenceId: null,
          recurrenceEndDate: null
        }
      };
      modalServiceSpy.show.mockReturnValue(mockEditModalRef as any);

      component.gatestatus('no');

      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(component.bsModalRef.hide).toHaveBeenCalled();
      expect(modalServiceSpy.show).toHaveBeenCalledWith(
        expect.anything(),
        {
          backdrop: 'static',
          keyboard: false,
          class: 'modal-lg new-inspection-popup custom-modal'
        }
      );
      expect(component.modalRef3).toBe(mockEditModalRef);
    });

    it('should set gatesubmit to false for "no" action', () => {
      component.gatesubmit = true;
      component.modalRef = { hide: jest.fn(), content: {} } as any;
      component.bsModalRef = { hide: jest.fn() } as any;

      component.gatestatus('no');

      expect(component.gatesubmit).toBeFalsy();
    });
  });

  // ===== ADDITIONAL COMPLEX METHOD TESTS =====

  describe('checkRoleData', () => {
    beforeEach(() => {
      component.authUser = { RoleId: 2, UserId: 'user123' };
    });

    it('should set showStatus for role 2 with pending status', () => {
      const item = { status: 'Pending' };

      component.checkRoleData(item);

      expect(component.showStatus).toBeTruthy();
    });

    it('should set showStatus for role 1 with pending status', () => {
      component.authUser = { RoleId: 1 };
      const item = { status: 'Pending' };

      component.checkRoleData(item);

      expect(component.showStatus).toBeTruthy();
    });

    it('should set showStatus for role 3 with approved status', () => {
      component.authUser = { RoleId: 3 };
      const item = { status: 'Approved' };

      component.checkRoleData(item);

      expect(component.showStatus).toBeTruthy();
    });

    it('should handle role 4 creator with approved status', () => {
      component.authUser = { RoleId: 2, UserId: 'user123' };
      const item = {
        createdUserDetails: {
          RoleId: 4,
          User: { id: 'user123' }
        },
        status: 'Approved'
      };

      component.checkRoleData(item);

      expect(component.showStatus).toBeTruthy();
      expect(component.statusValue).toEqual(['Delivered']);
    });

    it('should handle role 4 creator with expired status', () => {
      component.authUser = { RoleId: 3, UserId: 'user123' };
      const item = {
        createdUserDetails: {
          RoleId: 4,
          User: { id: 'user123' }
        },
        status: 'Expired'
      };

      component.checkRoleData(item);

      expect(component.showStatus).toBeTruthy();
      expect(component.statusValue).toEqual(['Delivered']);
    });

    it('should handle role 2/3 creator with approved status', () => {
      component.authUser = { RoleId: 2 };
      const item = {
        createdUserDetails: {
          RoleId: 2,
          User: { id: 'user456' }
        },
        status: 'Approved'
      };

      component.checkRoleData(item);

      expect(component.showStatus).toBeTruthy();
      expect(component.statusValue).toEqual(['Delivered']);
    });

    it('should handle role 3 creator with expired status', () => {
      component.authUser = { RoleId: 3 };
      const item = {
        createdUserDetails: {
          RoleId: 3,
          User: { id: 'user456' }
        },
        status: 'Expired'
      };

      component.checkRoleData(item);

      expect(component.showStatus).toBeTruthy();
      expect(component.statusValue).toEqual(['Delivered']);
    });
  });

  describe('openEditModal', () => {
    beforeEach(() => {
      component.bsModalRef = { hide: jest.fn() } as any;
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      deliveryServiceSpy.updatedInspectionId = jest.fn();
      projectServiceSpy.updateAccountProjectParent = jest.fn();
    });

    it('should hide current modal and open edit modal', () => {
      const item = { id: 1 };
      const action = 1;
      const mockModalRef = {
        content: {
          closeBtnName: '',
          seriesOption: 1,
          recurrenceId: null,
          recurrenceEndDate: null
        }
      };
      modalServiceSpy.show.mockReturnValue(mockModalRef as any);

      component.openEditModal(item, action);

      expect(component.bsModalRef.hide).toHaveBeenCalled();
      expect(deliveryServiceSpy.updatedInspectionId).toHaveBeenCalledWith(1);
      expect(projectServiceSpy.updateAccountProjectParent).toHaveBeenCalledWith({
        ProjectId: '123',
        ParentCompanyId: '456'
      });
      expect(modalServiceSpy.show).toHaveBeenCalledWith(
        expect.anything(),
        {
          backdrop: 'static',
          keyboard: false,
          class: 'modal-lg new-inspection-popup custom-modal',
          initialState: {
            seriesoption: 1,
            recurrenceId: null,
            recurrenceEndDate: null
          }
        }
      );
    });

    it('should handle item with recurrence', () => {
      const item = {
        id: 1,
        recurrence: {
          id: 'rec1',
          recurrence: 'Daily',
          recurrenceEndDate: '2024-12-31'
        }
      };
      const action = 2;

      component.openEditModal(item, action);

      expect(modalServiceSpy.show).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          initialState: {
            seriesoption: 2,
            recurrenceId: 'rec1',
            recurrenceEndDate: '2024-12-31'
          }
        })
      );
    });

    it('should handle item without recurrence', () => {
      const item = { id: 1 };
      const action = 1;

      component.openEditModal(item, action);

      expect(modalServiceSpy.show).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          initialState: {
            seriesoption: 1,
            recurrenceId: null,
            recurrenceEndDate: null
          }
        })
      );
    });

    it('should handle item with "Does Not Repeat" recurrence', () => {
      const item = {
        id: 1,
        recurrence: {
          id: 'rec1',
          recurrence: 'Does Not Repeat',
          recurrenceEndDate: null
        }
      };
      const action = 2;

      component.openEditModal(item, action);

      expect(modalServiceSpy.show).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          initialState: {
            seriesoption: 1,
            recurrenceId: null,
            recurrenceEndDate: null
          }
        })
      );
    });
  });

  describe('addToVoid', () => {
    beforeEach(() => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.currentinspectionSaveItem = { id: 1 };
      component.bsModalRef = { hide: jest.fn() } as any;
      deliveryServiceSpy.createInspectionVoid = jest.fn().mockReturnValue(of({ status: 201, message: 'Success' }));
      projectServiceSpy.updateAccountProjectParent = jest.fn();
    });

    it('should create void and handle success', () => {
      component.voidSubmitted = false;

      component.addToVoid();

      expect(component.voidSubmitted).toBeTruthy();
      expect(projectServiceSpy.updateAccountProjectParent).toHaveBeenCalledWith({
        ProjectId: '123',
        ParentCompanyId: '456'
      });
      expect(deliveryServiceSpy.createInspectionVoid).toHaveBeenCalledWith({
        InspectionRequestId: 1,
        ProjectId: '123',
        ParentCompanyId: '456'
      });
    });

    it('should handle successful void creation', () => {
      const mockResponse = { status: 201, message: 'Added to void successfully' };
      deliveryServiceSpy.createInspectionVoid.mockReturnValue(of(mockResponse));

      component.addToVoid();

      expect(toastrServiceSpy.success).toHaveBeenCalledWith('Added to void successfully', 'Success');
      expect(component.voidSubmitted).toBeFalsy();
      expect(deliveryServiceSpy.updatedInspectionHistory).toHaveBeenCalledWith({ status: true }, 'AddedToVoid');
      expect(deliveryServiceSpy.updateCraneRequestHistory).toHaveBeenCalledWith({ status: true }, 'AddedToVoid');
      expect(component.bsModalRef.hide).toHaveBeenCalled();
    });

    it('should prevent multiple submissions', () => {
      component.voidSubmitted = true;

      component.addToVoid();

      expect(deliveryServiceSpy.createInspectionVoid).not.toHaveBeenCalled();
    });

    it('should handle error with status code 400', () => {
      const errorResponse = { message: { statusCode: 400, details: [{ error: 'Bad request' }] } };
      deliveryServiceSpy.createInspectionVoid.mockReturnValue(throwError(errorResponse));
      jest.spyOn(component, 'showError').mockImplementation(() => {});

      component.addToVoid();

      expect(component.voidSubmitted).toBeFalsy();
      expect(component.showError).toHaveBeenCalledWith(errorResponse);
    });

    it('should handle error without message', () => {
      const errorResponse = {};
      deliveryServiceSpy.createInspectionVoid.mockReturnValue(throwError(errorResponse));

      component.addToVoid();

      expect(component.voidSubmitted).toBeFalsy();
      expect(toastrServiceSpy.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should handle error with message', () => {
      const errorResponse = { message: 'Custom error message' };
      deliveryServiceSpy.createInspectionVoid.mockReturnValue(throwError(errorResponse));

      component.addToVoid();

      expect(component.voidSubmitted).toBeFalsy();
      expect(toastrServiceSpy.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
    });
  });

  // ===== UTILITY AND HELPER METHOD TESTS =====

  describe('showError', () => {
    it('should display error message from error details', () => {
      const errorObj = {
        message: {
          details: [{ field: 'Test error message' }]
        }
      };

      component.showError(errorObj);

      expect(toastrServiceSpy.error).toHaveBeenCalledWith('Test error message');
    });

    it('should handle multiple error details', () => {
      const errorObj = {
        message: {
          details: [{ field1: 'Error 1', field2: 'Error 2' }]
        }
      };

      component.showError(errorObj);

      expect(toastrServiceSpy.error).toHaveBeenCalledWith('Error 1');
    });
  });

  describe('confirmationClose', () => {
    it('should open confirmation modal when status changed', () => {
      component.statusChanged = true;
      component.currentStatus = 'Approved';
      const template = {} as any;
      jest.spyOn(component, 'openConfirmationModalPopupForDetailsNDR').mockImplementation(() => {});

      component.confirmationClose(template);

      expect(component.openConfirmationModalPopupForDetailsNDR).toHaveBeenCalledWith(template);
    });

    it('should reset form when no status changed', () => {
      component.statusChanged = false;
      const template = {} as any;
      jest.spyOn(component, 'resetForm').mockImplementation(() => {});

      component.confirmationClose(template);

      expect(component.resetForm).toHaveBeenCalledWith('yes');
    });

    it('should reset form when status changed but no current status', () => {
      component.statusChanged = true;
      component.currentStatus = '';
      const template = {} as any;
      jest.spyOn(component, 'resetForm').mockImplementation(() => {});

      component.confirmationClose(template);

      expect(component.resetForm).toHaveBeenCalledWith('yes');
    });
  });

  describe('resetForm', () => {
    beforeEach(() => {
      component.modalRef1 = { hide: jest.fn() } as any;
      component.bsModalRef = { hide: jest.fn() } as any;
    });

    it('should hide modalRef1 for "no" action', () => {
      component.resetForm('no');

      expect(component.modalRef1.hide).toHaveBeenCalled();
    });

    it('should hide modals and reset status for "yes" action', () => {
      component.statusChanged = true;

      component.resetForm('yes');

      expect(component.modalRef1.hide).toHaveBeenCalled();
      expect(component.bsModalRef.hide).toHaveBeenCalled();
      expect(component.statusChanged).toBeFalsy();
    });

    it('should handle missing modalRef1 for "yes" action', () => {
      component.modalRef1 = null;
      component.statusChanged = true;

      component.resetForm('yes');

      expect(component.bsModalRef.hide).toHaveBeenCalled();
      expect(component.statusChanged).toBeFalsy();
    });
  });

  describe('voidConfirmationResponse', () => {
    beforeEach(() => {
      component.modalRef = { hide: jest.fn() } as any;
      jest.spyOn(component, 'addToVoid').mockImplementation(() => {});
    });

    it('should hide modal for "no" action', () => {
      component.voidConfirmationResponse('no');

      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(component.addToVoid).not.toHaveBeenCalled();
    });

    it('should hide modal and add to void for "yes" action', () => {
      component.voidConfirmationResponse('yes');

      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(component.addToVoid).toHaveBeenCalled();
    });
  });

  describe('revertstatus', () => {
    beforeEach(() => {
      component.modalRef2 = { hide: jest.fn() } as any;
    });

    it('should open modal for role 1 user', () => {
      component.authUser = { RoleId: 1 };
      const template = {} as any;
      const mockModalRef = { hide: jest.fn() };
      modalServiceSpy.show.mockReturnValue(mockModalRef as any);

      component.revertstatus(template);

      expect(modalServiceSpy.show).toHaveBeenCalledWith(
        template,
        {
          keyboard: false,
          class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
        }
      );
      expect(component.modalRef2).toBe(mockModalRef);
    });

    it('should open modal for role 2 user', () => {
      component.authUser = { RoleId: 2 };
      const template = {} as any;

      component.revertstatus(template);

      expect(modalServiceSpy.show).toHaveBeenCalled();
    });

    it('should not open modal for other roles', () => {
      component.authUser = { RoleId: 3 };
      const template = {} as any;
      modalServiceSpy.show.mockClear();

      component.revertstatus(template);

      expect(modalServiceSpy.show).not.toHaveBeenCalled();
    });
  });

  describe('statusupdate', () => {
    beforeEach(() => {
      component.modalRef2 = { hide: jest.fn() } as any;
      component.currentinspectionSaveItem = { id: 1 };
      component.ParentCompanyId = '456';
      component.currentStatus = 'Approved';
      deliveryServiceSpy.updateInspectionStatus = jest.fn().mockReturnValue(of({ message: 'Success' }));
    });

    it('should hide modal for "no" action', () => {
      component.statusupdate('no');

      expect(component.modalRef2.hide).toHaveBeenCalled();
      expect(deliveryServiceSpy.updateInspectionStatus).not.toHaveBeenCalled();
    });

    it('should update status for "yes" action', () => {
      component.statusupdate('yes');

      expect(component.statusSubmitted).toBeTruthy();
      expect(component.modalRef2.hide).toHaveBeenCalled();
      expect(deliveryServiceSpy.updateInspectionStatus).toHaveBeenCalledWith({
        id: 1,
        status: 'Approved',
        inspectionStatus: null,
        ParentCompanyId: '456',
        statuschange: 'Reverted'
      });
    });

    it('should handle successful status revert', () => {
      const mockResponse = { message: 'Status reverted successfully' };
      deliveryServiceSpy.updateInspectionStatus.mockReturnValue(of(mockResponse));

      component.statusupdate('yes');

      expect(component.statusSubmitted).toBeFalsy();
      expect(toastrServiceSpy.success).toHaveBeenCalledWith('Status Reverted Successfully', 'Success');
      expect(socketSpy.emit).toHaveBeenCalledWith('InspectionNDRApproveHistory', mockResponse);
      expect(deliveryServiceSpy.updatedInspectionHistory).toHaveBeenCalledWith({ status: true }, 'NDRApproveHistory');
      expect(deliveryServiceSpy.updateCraneRequestHistory).toHaveBeenCalledWith({ status: true }, 'historyUpdate');
      expect(component.currentinspectionSaveItem.status).toBe('Approved');
      expect(component.statusChanged).toBeFalsy();
      expect(component.modalRef2.hide).toHaveBeenCalled();
    });
  });

  describe('navigateToAttachment', () => {
    beforeEach(() => {
      component.modalRef = { hide: jest.fn() } as any;
    });

    it('should set currentTabId to 1 and hide modal', () => {
      component.navigateToAttachment();

      expect(component.currentTabId).toBe(1);
      expect(component.modalRef.hide).toHaveBeenCalled();
    });
  });

  describe('setStatus', () => {
    it('should set component properties from data', () => {
      component.data = {
        ParentCompanyId: '456',
        ProjectId: '123',
        id: 5
      };

      component.setStatus();

      expect(component.ParentCompanyId).toBe('456');
      expect(component.ProjectId).toBe('123');
      expect(component.inspectionRequestId).toBe(5);
      expect(deliveryServiceSpy.updatedInspectionId).toHaveBeenCalledWith(5);
    });

    it('should handle undefined inspectionRequestId', () => {
      component.data = {
        ParentCompanyId: '456',
        ProjectId: '123',
        id: undefined
      };
      jest.spyOn(component, 'getNDR').mockImplementation(() => {});

      component.setStatus();

      expect(component.getNDR).not.toHaveBeenCalled();
    });

    it('should handle -1 inspectionRequestId', () => {
      component.data = {
        ParentCompanyId: '456',
        ProjectId: '123',
        id: -1
      };
      jest.spyOn(component, 'getNDR').mockImplementation(() => {});

      component.setStatus();

      expect(component.getNDR).not.toHaveBeenCalled();
    });
  });

  describe('handleDownKeydown', () => {
    it('should call updateInspectionStatus for Enter key with update type', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'updateInspectionStatus').mockImplementation(() => {});

      component.handleDownKeydown(event, {}, {}, 'update');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.updateInspectionStatus).toHaveBeenCalled();
    });

    it('should call saveStatus for Space key with save type', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'saveStatus').mockImplementation(() => {});

      component.handleDownKeydown(event, {}, {}, 'save');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.saveStatus).toHaveBeenCalled();
    });

    it('should call openEditModal for Enter key with edit type', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      const data = { id: 1 };
      const item = { action: 'edit' };
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'openEditModal').mockImplementation(() => {});

      component.handleDownKeydown(event, data, item, 'edit');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.openEditModal).toHaveBeenCalledWith(data, item);
    });

    it('should call openModal for Enter key with open type', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      const data = { template: 'test' };
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'openModal').mockImplementation(() => {});

      component.handleDownKeydown(event, data, {}, 'open');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.openModal).toHaveBeenCalledWith(data);
    });

    it('should not call any method for unknown type', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'updateInspectionStatus').mockImplementation(() => {});
      jest.spyOn(component, 'saveStatus').mockImplementation(() => {});

      component.handleDownKeydown(event, {}, {}, 'unknown');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.updateInspectionStatus).not.toHaveBeenCalled();
      expect(component.saveStatus).not.toHaveBeenCalled();
    });

    it('should not call preventDefault for non-Enter/Space keys', () => {
      const event = new KeyboardEvent('keydown', { key: 'Tab' });
      jest.spyOn(event, 'preventDefault');

      component.handleDownKeydown(event, {}, {}, 'update');

      expect(event.preventDefault).not.toHaveBeenCalled();
    });
  });
});