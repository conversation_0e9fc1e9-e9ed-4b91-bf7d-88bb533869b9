import { Component, OnInit, TemplateRef } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { Title } from '@angular/platform-browser';
import { DeliveryService } from '../services/profile/delivery.service';
import { ProjectService } from '../services/profile/project.service';
import { MixpanelService } from '../services/mixpanel.service';

@Component({
  selector: 'app-dfow',
  templateUrl: './dfow.component.html',
  })
export class DFOWComponent implements OnInit {
  public pageSize = 25;

  public pageNo = 1;

  public userData = [];

  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public toggleInput = false;

  public dfowFiles: NgxFileDropEntry[] = [];

  public formData: any = [];

  public fileArray: any = [];

  public ProjectId: any;

  public loader = false;

  public sort = 'ASC';

  public search = '';

  public export = 0;

  public currentDeleteId: any;

  public deleteSubmitted = false;

  public deleteIndex: any = [];

  public submitted = false;

  public editSubmitted = false;

  public remove = false;

  public totalCount = 0;

  public selectAll = false;

  public defineList: any = [];

  public title = 'DFOW_Import_Template';

  public todayDate = new Date();

  public importSubmitted = false;

  public addRecord = false;

  public editRecord = false;

  public saveSubmitted = false;

  public addData = [];

  public duplicateData: any = [];

  public lastId = 0;

  public ParentCompanyId: any;

  public sortColumn = 'DFOW';

  public showSearchbar = false;

  public authUser: any = {};

  public formSubmitted = false;

  public constructor(
    private readonly modalService: BsModalService,
    private readonly titleService: Title,
    private readonly mixpanelService: MixpanelService,
    public deliveryService: DeliveryService,
    private readonly toastr: ToastrService,
    public projectService: ProjectService,
  ) {
    this.titleService.setTitle('Follo - Definable Feature of Work');
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
      }
    });
    this.projectService.projectParent.subscribe((response6): void => {
      if (response6 !== undefined && response6 !== null && response6 !== '') {
        this.ProjectId = response6.ProjectId;
        this.ParentCompanyId = response6.ParentCompanyId;
        this.getDefinable();
      }
    });
  }

  public ngOnInit(): void { /* */ }

  public openModal(template: TemplateRef<any>): void {
    this.modalRef = this.modalService.show(template, {
      backdrop: 'static',
      class: 'modal-lg custom-modal',
    });
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'delete':
          this.openDeleteModal(data, item);
          break;
        case 'remove':
          this.removeFile(data);
          break;
        case 'clear':
          this.clear();
          break;
        default:
          break;
      }
    }
  }

  public close(template: TemplateRef<any>): void {
    if (this.dfowFiles.length > 0) {
      let data = {};
      data = {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      };
      this.modalRef1 = this.modalService.show(template, data);
    } else {
      this.resetForm('yes');
    }
  }

  public resetAndClose(): void {
    this.modalRef.hide();
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.modalRef.hide();
      this.dfowFiles = [];
    }
  }

  public changePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.pageNo = 1;
    this.getDefinable();
  }

  public changePageNo(pageNo: number): void {
    this.pageNo = pageNo;
    this.getDefinable();
  }

  public setSelectedItem(index: string | number): void {
    this.defineList[index].isChecked = !this.defineList[index].isChecked;
  }

  public dfowImportFiledropped(files: NgxFileDropEntry[]): void {
    if (files.length === 1) {
      this.dfowFiles = files;
      this.dfowFiles.forEach((dfowElement, i): void => {
        const relativePath = dfowElement.relativePath.split('.');
        const extension = relativePath[relativePath.length - 1];
        if (extension === 'xlsx' || extension === 'xls' || extension === 'csv') {
          if (dfowElement.fileEntry.isFile) {
            const fileEntry = dfowElement.fileEntry as FileSystemFileEntry;
            fileEntry.file((_file: File): void => {
              this.formData = new FormData();
              this.formData.append('definable', _file, dfowElement.relativePath);
            });
          }
        } else {
          this.dfowFiles.splice(i);
          this.toastr.error(
            'Please select a valid file. Supported file format (.xlsx,.xls,.csv)',
            'OOPS!',
          );
        }
      });
    } else {
      this.toastr.error('Please import single file', 'OOPS!');
    }
  }

  public removeFile(i: number): void {
    this.dfowFiles.splice(i);
  }

  public addRow(): void {
    this.addRecord = true;
    if (this.defineList.length === 0) {
      this.defineList.push({
        autoId: 1,
        DFOW: '',
        Specification: '',
        addRow: true,
      });
    } else {
      const maxObj = this.defineList.reduce((i: { autoId: number }, j: { autoId: number }): any => (i.autoId > j.autoId ? i : j));
      this.defineList.push({
        autoId: maxObj.autoId + 1,
        DFOW: '',
        Specification: '',
        addRow: true,
      });
    }
  }

  public importData(): void {
    this.importSubmitted = true;
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.importDefinable(params, this.formData).subscribe({
      next: (res): void => {
        this.toastr.success(res.message, 'SUCCESS!');
        this.mixpanelService.addMixpanelEvents('Added Definable Feature Of Work');
        this.dfowFiles = [];
        this.getDefinable();
        this.importSubmitted = false;
        this.modalRef.hide();
      },
      error: (importDataError): void => {
        this.importSubmitted = false;
        if (importDataError.message?.statusCode === 400) {
          this.showError(importDataError);
        } else if (!importDataError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(importDataError.message, 'OOPS!');
        }
      },
    });
  }

  public enterContent(i: string | number, input: any): void {
    this.editRecord = true;
    this.defineList[i].DFOW = input;
  }

  public enterSpecification(i: string | number, input: any): void {
    this.editRecord = true;
    this.defineList[i].Specification = input;
  }

  public getDefinable(): void {
    this.loader = true;
    this.defineList = [];
    const params = {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.pageNo,
      sort: this.sort,
      sortByField: this.sortColumn,
      export: this.export,
    };
    const payload = {
      search: this.search,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.getDefinable(params, payload).subscribe({
      next: (res): void => {
        const responseData = res.data;
        this.lastId = res.lastId.id;
        this.defineList = responseData.rows;
        this.cancelChanges();
        this.totalCount = responseData.count;
        if (this.selectAll && this.defineList.length > 0) {
          this.defineList.forEach((obj: any, index: string | number): void => {
            this.defineList[index].isChecked = true;
            return null;
          });
        } else {
          this.selectAll = false;
          this.defineList.forEach((obj: any, index: string | number): void => {
            this.defineList[index].isChecked = false;
            return null;
          });
        }
        this.loader = false;
      },
      error: (err): void => {
        this.loader = false;
        if (err.message?.statusCode === 400) {
          this.showError(err);
        } else if (!err.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(err.message, 'OOPS!');
        }
      }
    });
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.getDefinable();
  }

  public searchDFOW(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.search = data;
    this.getDefinable();
  }

  public inputEdit(): void {
    this.toggleInput = !this.toggleInput;
  }

  public exportDFOW(): void {
    const params = {
      ProjectId: this.ProjectId,
      sort: this.sort,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.exportDefinable(params).subscribe((res): void => {
      this.deliveryService.saveAsExcelFile(res, 'DFOW_Export');
    });
  }

  public sortByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getDefinable();
  }

  public resetChanges(): void {
    this.pageNo = 1;
    this.search = '';
    this.cancelChanges();
    this.getDefinable();
  }

  public cancelChanges(): void {
    this.addRecord = false;
    this.editRecord = false;
    this.duplicateData = [];
  }

  public saveChanges(): void {
    this.saveSubmitted = true;
    const params = {
      ProjectId: this.ProjectId,
    };
    let newDfowArray: any;
    let editData: { editData: any; ParentCompanyId: number };
    if (this.addRecord) {
      newDfowArray = this.defineList.filter((data: {}): {} => {
        if (!('id' in data)) {
          return data;
        }
        return false;
      });
      editData = {
        editData: newDfowArray,
        ParentCompanyId: this.ParentCompanyId,
      };
    } else {
      editData = { editData: this.defineList, ParentCompanyId: this.ParentCompanyId };
    }
    this.deliveryService.updateDefinable(params, editData).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.mixpanelService.addMixpanelEvents('Edited Definable Feature Of Work');
          this.duplicateData = [];
          this.getDefinable();
          this.saveSubmitted = false;
          this.addRecord = false;
          this.editRecord = false;
        }
      },
      error: (updateDefinableError): void => {
        this.saveSubmitted = false;
        if (updateDefinableError.message?.statusCode === 400) {
          this.showError(updateDefinableError);
        } else if (!updateDefinableError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else if (updateDefinableError.data !== undefined) {
          this.duplicateData = updateDefinableError.data;
          this.toastr.error(updateDefinableError.message, 'OOPS!');
        } else {
          this.toastr.error(updateDefinableError.message, 'OOPS!');
        }
      },
    });
  }

  public getDuplicate(id: any): boolean {
    const index = this.duplicateData.findIndex((item: { id: any }): boolean => item.id === id);
    if (index !== -1) {
      return true;
    }
    return false;
  }

  public openDeleteModal(index: number, template: TemplateRef<any>): void {
    if (index !== -1) {
      if (this.defineList[index].addRow) {
        this.defineList.splice(index, 1);
      } else {
        this.deleteIndex[0] = this.defineList[index].id;
        this.currentDeleteId = index;
        this.remove = false;
        this.openModal(template);
      }
    } else {
      this.remove = true;
      this.openModal(template);
    }
  }

  public checkSelectedRow(): boolean {
    if (this.selectAll) {
      return false;
    }
    const index = this.defineList.findIndex(
      (item: { isChecked: boolean }): boolean => item.isChecked === true,
    );
    if (index !== -1) {
      return false;
    }
    return true;
  }

  public removeItem(): void {
    this.deleteSubmitted = true;
    if (this.selectAll) {
      this.deleteDefinable();
    } else {
      this.defineList.forEach((element: { isChecked: any; id: any }): void => {
        if (element.isChecked) {
          this.deleteIndex.push(element.id);
        }
      });
      this.deleteDefinable();
    }
  }

  public deleteDefinable(): void {
    this.deleteSubmitted = true;
    this.deliveryService
      .deleteDefinable({
        deleteData: this.deleteIndex,
        ProjectId: this.ProjectId,
        isSelectAll: this.selectAll,
        ParentCompanyId: this.ParentCompanyId,
      })
      .subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Deleted Definable Feature Of Work');
            this.getDefinable();
            this.deleteSubmitted = false;
            this.modalRef.hide();
          }
        },
        error: (deleteDefinableError): void => {
          this.deleteSubmitted = false;
          this.getDefinable();
          if (deleteDefinableError.message?.statusCode === 400) {
            this.showError(deleteDefinableError);
          } else if (!deleteDefinableError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deleteDefinableError.message, 'OOPS!');
          }
        },
      });
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.editSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public selectAllDFOWData(): void {
    this.selectAll = !this.selectAll;
    if (this.selectAll) {
      this.defineList.map((obj: any, index: string | number): void => {
        this.defineList[index].isChecked = true;
        return null;
      });
    } else {
      this.defineList.map((obj: any, index: string | number): void => {
        this.defineList[index].isChecked = false;
        return null;
      });
    }
  }

  public download(): void {
    const excel = [
      {
        id: 1,
        Specification: '12 23 45',
        DFOW: 'First',
      },
      {
        id: 2,
        Specification: '56 45 89',
        DFOW: 'Second',
      },
      {
        id: 3,
        Specification: '58 44 75',
        DFOW: 'Third',
      },
    ];
    this.exportExcel(excel);
  }

  public exportExcel(data: any[]): void {
    this.export = 0;
    this.deliveryService.exportAsExcelFile(data, this.title);
  }
}
