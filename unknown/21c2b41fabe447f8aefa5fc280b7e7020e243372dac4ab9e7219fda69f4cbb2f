<section class="page-section">
  <div class="page-inner-content">
    <div class="row pt-md-15px">
      <div class="col-md-12 px-3">
        <div class="top-header my-3">
          <div class="row">
            <div class="col-md-8">
              <div class="top-btn">
                <ul class="list-group list-group-horizontal">
                  <li class="list-group-item p0 border-0 bg-transparent me-4">
                    <button
                      class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular"
                      (click)="openModal(newMemeber)"
                    >
                      Add New Gate
                    </button>
                  </li>
                  <li class="list-group-item p0 border-0 bg-transparent">
                    <button
                      class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular"
                      (click)="openDeleteModal(-1, deleteList)"
                      [disabled]="checkSelectedRow()"
                    >
                      Remove
                    </button>
                  </li>
                </ul>
              </div>
            </div>
            <div class="col-md-4">
              <div class="top-filter">
                <ul class="list-group list-group-horizontal justify-content-end">
                  <li class="list-group-item p0 border-0 bg-transparent">
                    <div class="search-icon">
                      <input
                        class="form-control fs12 color-grey8"
                        [ngClass]="showSearchbar ? 'input-hover-disable' : 'input-search'"
                        placeholder="What you are looking for?"
                        (input)="searchGate($event.target.value)"
                        [(ngModel)]="search"
                      />
                      <div class="icon">
                        <img
                          src="./assets/images/cross-close.svg"
                          *ngIf="showSearchbar"
                          (click)="clear()"  (keydown)="handleDownKeydown($event, '', '','clear')"
                          alt="close-cross"
                        />
                        <em class="fa fa-search fs12 color-grey8" *ngIf="!showSearchbar"></em>
                      </div>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div class="page-card bg-white rounded">
          <div class="table-responsive rounded tab-grid">
            <table class="table table-custom gates-resizable mb-0" aria-describedby="Memtable">
              <thead>
                <th scope="col" *ngIf="gateList.length > 0">
                  <div class="custom-checkbox form-check">
                    <input class="form-check-input float-none ms-0" type="checkbox"
                    (change)="selectAllGatesData()"
                    [checked]="selectAll"
                    id="tblData"
                    name="tblData">
                    <label class="form-check-label c-pointer fs12" for="tblData">
                    </label>
                  </div>
                </th>
                <th scope="col" class="text-center" resizable>
                  ID
                  <span>
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('id', 'ASC')"  (keydown)="handleToggleKeydown($event, 'id', 'ASC')"
                      *ngIf="sortColumn !== 'id'"
                    />
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('id', 'ASC')" (keydown)="handleToggleKeydown($event, 'id', 'ASC')"
                      *ngIf="sort === 'DESC' && sortColumn === 'id'"
                    />
                    <img
                      src="./assets/images/up-chevron.svg"
                      alt="up-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('id', 'DESC')" (keydown)="handleToggleKeydown($event, 'id', 'DESC')"
                      *ngIf="sort === 'ASC' && sortColumn === 'id'"
                    />
                  </span>
                </th>
                <th scope="col" class="text-center" resizable>
                  Gate Name
                  <span>
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('gateName', 'ASC')" (keydown)="handleToggleKeydown($event, 'gateName', 'ASC')"
                      *ngIf="sortColumn !== 'gateName'"
                    />
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('gateName', 'ASC')" (keydown)="handleToggleKeydown($event, 'gateName', 'ASC')"
                      *ngIf="sort === 'DESC' && sortColumn === 'gateName'"
                    />
                    <img
                      src="./assets/images/up-chevron.svg"
                      alt="up-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('gateName', 'DESC')" (keydown)="handleToggleKeydown($event, 'gateName', 'DESC')"
                      *ngIf="sort === 'ASC' && sortColumn === 'gateName'"
                    />
                  </span>
                </th>
                <th
                  scope="col"
                  *ngIf="authUser.RoleId === 1 || authUser.RoleId === 2"
                  class="text-center"
                  resizable
                >
                  Activate / Deactivate
                </th>
                <th scope="col" class="text-center" resizable>Action</th>
              </thead>
              <tbody *ngIf="gateList.length > 0 && loader == false">
                <tr
                  *ngFor="
                    let data of gateList
                      | paginate
                        : {
                            itemsPerPage: pageSize,
                            currentPage: currentPageNo,
                            totalItems: totalCount
                          };
                    let i = index
                  "
                >
                  <td class="custom-checkbox">
                    <div class="form-check text-start ps-0">
                      <input class="form-check-input float-none ms-0" type="checkbox"
                      [checked]="data.isChecked"
                      (change)="setSelectedItem(i)"
                      id="tblData1_{{ i }}"
                      name="tblData1">
                      <label class="form-check-label c-pointer fs12" for="tblData1_{{ i }}">
                      </label>
                    </div>
                  </td>
                  <td class="text-center">{{ data.gateAutoId }}</td>
                  <td class="text-center">
                    {{ data.gateName }}
                  </td>
                  <td class="text-center" *ngIf="authUser.RoleId === 1 || authUser.RoleId === 2">
                    <div class="form-group">
                      <ul
                        class="small-switch list-group list-group-horizontal justify-content-center"
                        id="switch-control4"
                      >
                        <li class="list-group-item border-0 px-0 py-0 bg-transparent">
                          <ui-switch
                            switchColor="#fff"
                            defaultBoColor="#CECECE"
                            defaultBgColor="#CECECE"
                            (change)="openDeactivateModal(deactivateMemeber, data, $event)"
                            [checked]="data?.isActive"
                          >
                          </ui-switch>
                        </li>
                      </ul>
                    </div>
                  </td>
                  <td class="text-center">
                    <ul class="list-inline mb-0 ms-2">
                      <li
                        class="list-inline-item"
                        tooltip="Edit"
                        placement="top"
                        (click)="openEditModal(editMemeber, i)"  (keydown)="handleDownKeydown($event, editMemeber, i,'edit')"
                      >
                        <a href="javascript:void(0)"
                          ><img src="./assets/images/edit.svg" alt="edit" class="h-15px"
                        /></a>
                      </li>
                      <li class="list-inline-item mx-2" tooltip="Delete" placement="top">
                        <a href="javascript:void(0)" (click)="openDeleteModal(i, deleteList)"
                          ><img src="./assets/images/delete.svg" alt="delete" class="h-15px"
                        /></a>
                      </li>
                    </ul>
                  </td>
                </tr>
              </tbody>
              <tr *ngIf="loader == true" id="loading2">
                <td colspan="3" class="text-center">
                  <div class="fs18 fw-bold cairo-regular my-5 text-black">Loading...</div>
                </td>
              </tr>
              <tr *ngIf="loader == false && gateList.length == 0">
                <td colspan="3" class="text-center">
                  <div class="fs18 fw-bold cairo-regular my-5 text-black">
                    No Records Found
                  </div>
                </td>
              </tr>
            </table>
          </div>
          <div
            class="tab-pagination px-2"
            id="tab-pagination6"
            *ngIf="loader == false && totalCount > 25"
          >
            <div class="row">
              <div class="col-md-3 align-items-center">
                <ul class="list-inline my-3">
                  <li class="list-inline-item notify-pagination">
                    <label class="fs12 color-grey4" for="showEnt">Show entries</label>
                  </li>
                  <li class="list-inline-item">
                    <select id="showEnt"
                      class="w-auto form-select fs12 color-grey4"
                      (change)="changePageSize($event.target.value)"
                      [ngModel]="pageSize"
                    >
                      <option value="25">25</option>
                      <option value="50">50</option>
                      <option value="100">100</option>
                      <option value="150">150</option>
                    </select>
                  </li>
                </ul>
              </div>
              <div class="col-md-8 text-center">
                <div class="my-3 position-relative d-inline-block">
                  <pagination-controls
                    (pageChange)="changePageNo($event)"
                    previousLabel=""
                    nextLabel=""
                  >
                  </pagination-controls>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- modal -->
<ng-template #newMemeber>
  <div class="modal-header">
    <h4 class="fs14 fw-bold cairo-regular color-text7 my-1">
      <img src="./assets/images/gate.svg" alt="Gate" class="me-2" />New Gate
    </h4>
    <button
      type="button"
      class="close ms-auto"
      aria-label="Close"
      (click)="close(cancelConfirmation, 'addGate')"
    >
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body">
    <form
      name="form"
      class="custom-material-form"
      [formGroup]="gateForm"
      (ngSubmit)="onSubmit()"
      novalidate
    >
      <div class="row">
        <div class="col-md-12">
          <div class="form-group mb-4">
            <label class="fs12 fw600" for="gateId">Gate ID</label>
            <input  id="gateId"
              type="text"
              class="form-control fs12 color-orange material-input px-2"
              placeholder=""
              value="{{ lastId?.id }}"
              disabled="disabled"
            />
          </div>
          <div class="form-group mb-4">
            <input
              type="text"
              class="form-control fs12 material-input"
              placeholder="Gate Name*"
              formControlName="gateName"
              (keypress)="allowAlphaNumericForGateName($event)"
            />
            <div class="color-red" *ngIf="submitted && gateForm.get('gateName').errors">
              <small *ngIf="gateForm.get('gateName').errors.required">Enter Gate Name</small>
            </div>
          </div>
        </div>
      </div>
      <div class="text-center">
        <button
          class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular mb-3 px-2rem me-2"
          (click)="close(cancelConfirmation, 'addGate')"
          type="button"
        >
          Cancel
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular mb-3 px-2rem"
          type="submit"
          [disabled]="formSubmitted && gateForm.valid"
        >
          <em class="fa fa-spinner" aria-hidden="true" *ngIf="formSubmitted && gateForm.valid"></em
          >Submit
        </button>
      </div>
    </form>
  </div>
</ng-template>

<ng-template #editMemeber>
  <div class="modal-header">
    <h4 class="fs14 fw-bold cairo-regular color-text7 my-1">
      <img src="./assets/images/gate.svg" alt="Gate" class="me-2" />Edit Gate
    </h4>
    <button
      type="button"
      class="close ms-auto"
      aria-label="Close"
      (click)="close(cancelConfirmation, 'editGate')"
    >
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body">
    <form
      name="form"
      class="custom-material-form"
      [formGroup]="gateEditForm"
      (ngSubmit)="editSubmit()"
      novalidate
    >
      <div class="row">
        <div class="col-md-12">
          <div class="form-group mb-4">
            <label class="fs12 fw600" for="gate">Gate ID</label>
            <input id="gate"
              type="text"
              class="form-control fs12 color-orange material-input px-2"
              placeholder=""
              value="{{ gateList[editIndex]?.gateAutoId }}"
              disabled="disabled"
            />
          </div>
          <div class="form-group mb-4">
            <input
              type="text"
              class="form-control fs12 material-input"
              placeholder="Gate Name*"
              formControlName="gateName"
              (keypress)="allowAlphaNumericForGateName($event)"
            />
            <div class="color-red" *ngIf="editSubmitted && gateEditForm.get('gateName').errors">
              <small *ngIf="gateEditForm.get('gateName').errors.required"
                >*Gate Name is Required</small
              >
            </div>
          </div>
        </div>
      </div>
      <div class="text-center">
        <button
          class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular mb-3 px-2rem me-2"
          (click)="close(cancelConfirmation, 'editGate')"
          type="button"
        >
          Cancel
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular mb-3 px-2rem"
          type="submit"
          [disabled]="formEditSubmitted && gateEditForm.valid"
        >
          <em
            class="fa fa-spinner"
            aria-hidden="true"
            *ngIf="formEditSubmitted && gateEditForm.valid"
          ></em
          >Update
        </button>
      </div>
    </form>
  </div>
</ng-template>

<ng-template #deleteList>
  <div class="modal-body">
    <div class="text-center my-4" *ngIf="!remove">
      <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
        Are you sure you want to delete '{{ gateList[currentDeleteId]?.gateName | titlecase }}'?
      </p>
      <button
        class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
        (click)="resetAndClose()"
      >
        No
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
        type="submit"
        (click)="deleteGateDetail()"
        [disabled]="deleteSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="deleteSubmitted"></em>Yes
      </button>
    </div>
    <div class="text-center my-4" id="remove-popup4" *ngIf="remove">
      <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
        Are you sure you want to delete?
      </p>
      <button
        class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
        (click)="resetAndClose()"
      >
        No
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
        type="submit"
        (click)="removeItem()"
        [disabled]="deleteSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="deleteSubmitted"></em>Yes
      </button>
    </div>
  </div>
</ng-template>

<ng-template #deactivateMemeber>
  <div class="modal-header">
    <h4 class="fs14 fw-bold cairo-regular color-text7 my-1">
      <img src="./assets/images/gate.svg" alt="Gate" class="me-2" />
      Deactivate Gate
    </h4>
    <button
      type="button"
      class="close ms-auto"
      aria-label="Close"
      (click)="close(cancelConfirmation, 'deactivateGate')"
    >
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body" *ngIf="!getMappedRequestLoader">
    <div class="row">
      <div class="col-md-12">
        <div class="form-group">
          <p class="text-center fw-bold fs14">
            Are you sure you want to deactivate this Gate?
          </p>
          <div *ngIf="deliveryGateRequestList.length > 0">
            <p class="text-center fs13 content-deactivate">
              This gate is associated with the following items. If applicable, please switch the
              gate assignment for the respective items.
            </p>
            <div class="table-responsive rounded tab-grid fixTableHead">
              <table class="table table-custom mb-0 members-table" aria-describedby="Memtable">
                <thead>
                  <tr class="member-table-row">
                    <th scope="col" resizable>Delivery ID and Description</th>
                    <th scope="col" resizable>Date and Time</th>
                    <th scope="col" resizable>Gates</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let item of deliveryGateRequestList">
                    <td>
                      <p class="mb-0">{{ item.DeliveryId }}-{{ item.description }}</p>
                    </td>
                    <td>{{ item.deliveryStart | date : 'medium' }}</td>
                    <td class="deactivate-select-control">
                      <select
                        class="form-control fs12 material-input ps-2"
                        (change)="switchGate(item, $event.target.value)"
                      >
                        <option
                          *ngFor="let itemvalue of deliveryGateList"
                          value="{{ itemvalue.id }}"
                          [ngValue]="itemvalue?.id"
                          [selected]="item?.gateDetails[0]?.Gate?.id == itemvalue?.id"
                        >
                          {{ itemvalue.gateName }}
                        </option>
                      </select>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <p class="text-center fw-bold fs12">
              *Note: If you need to deactivate this gate, you will have to switch the gate for all
              the future bookings listed above
            </p>
          </div>
        </div>
      </div>
    </div>
    <div class="text-center py-4">
      <button
        class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular mb-3 px-2rem me-2"
        (click)="close(cancelConfirmation, 'deactivateGate')"
        type="button"
      >
        Cancel
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular mb-3 px-2rem"
        [disabled]="deactivateGateLoader"
        (click)="deactivateGate()"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="deactivateGateLoader"></em>Confirm
      </button>
    </div>
  </div>
  <div class="modal-body fw-bold text-center text-black" *ngIf="getMappedRequestLoader">
    Loading...
  </div>
</ng-template>
<!-- modal -->

<!--Confirmation Popup-->
<div id="confirm-popup11">
  <ng-template #cancelConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure you want to cancel?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetForm('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="resetForm('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
