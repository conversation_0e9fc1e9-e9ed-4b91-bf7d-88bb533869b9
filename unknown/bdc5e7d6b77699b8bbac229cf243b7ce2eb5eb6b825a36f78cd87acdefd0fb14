import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ConcreteRequestsGridComponent } from './concrete-requests-grid.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Router, ActivatedRoute } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { UntypedFormBuilder } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { of, throwError } from 'rxjs';
import { NgxPaginationModule } from 'ngx-pagination';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('ConcreteRequestsGridComponent', () => {
  let component: ConcreteRequestsGridComponent;
  let fixture: ComponentFixture<ConcreteRequestsGridComponent>;
  let modalService: jest.Mocked<BsModalService>;
  let projectService: jest.Mocked<ProjectService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let router: jest.Mocked<Router>;
  let toastr: jest.Mocked<ToastrService>;
  let modalRef: BsModalRef;

  const mockProjectData = {
    ProjectId: 1,
    ParentCompanyId: 1
  };

  const mockConcreteRequestList = {
    data: {
      rows: [
        {
          ConcreteRequestId: 1,
          id: 1,
          description: 'Test Request',
          status: 'Approved',
          isAllowedToEdit: true,
          memberDetails: [
            { Member: { User: { id: 1, firstName: 'John', lastName: 'Doe', email: '<EMAIL>' } } }
          ],
          recurrence: {
            id: 1,
            recurrenceEndDate: new Date()
          }
        },
        {
          ConcreteRequestId: 2,
          id: 2,
          description: 'Test Request 2',
          status: 'Pending',
          isAllowedToEdit: false,
          memberDetails: []
        }
      ],
      count: 2
    },
    statusData: {
      statusColorCode: JSON.stringify([
        { status: 'approved', backgroundColor: '#28a745', fontColor: '#fff' },
        { status: 'pending', backgroundColor: '#ffc107', fontColor: '#000' },
        { status: 'delivered', backgroundColor: '#17a2b8', fontColor: '#fff' },
        { status: 'rejected', backgroundColor: '#dc3545', fontColor: '#fff' },
        { status: 'expired', backgroundColor: '#6c757d', fontColor: '#fff' }
      ])
    },
    lastId: {
      ConcreteRequestId: 2
    }
  };

  beforeEach(async () => {
    modalRef = { hide: jest.fn() } as any;

    modalService = {
      show: jest.fn().mockReturnValue(modalRef),
    } as any;

    projectService = {
      projectParent: of(mockProjectData),
      getCompanies: jest.fn().mockReturnValue(of({ data: [
        { id: 1, companyName: 'Test Company 1' },
        { id: 2, companyName: 'Test Company 2' }
      ] })),
      listAllMember: jest.fn().mockReturnValue(of({ data: [
        { id: 1, User: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' } },
        { id: 2, User: { firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>' } }
      ] }))
    } as any;

    deliveryService = {
      loginUser: of({ RoleId: 1, UserId: 1, User: { firstName: 'Test', lastName: 'User', email: '<EMAIL>' }, id: 1 }),
      getConcreteRequestList: jest.fn().mockReturnValue(of(mockConcreteRequestList)),
      searchNewMember: jest.fn().mockReturnValue(of({ data: [] })),
      fetchConcreteData: of({}),
      updatedEditConcreteRequestId: jest.fn(),
      updateConcreteRequestHistory: jest.fn(),
      deleteConcreteRequest: jest.fn().mockReturnValue(of({ message: 'Request deleted successfully' })),
      getConcreteRequestDropdownData: jest.fn().mockReturnValue(of({
        data: {
          locationDropdown: [{ id: 1, name: 'Location 1' }],
          locationDetailsDropdown: [{ id: 1, name: 'Location Detail 1' }],
          concreteSupplierDropdown: [{ id: 1, name: 'Supplier 1' }],
          mixDesignDropdown: [{ id: 1, name: 'Mix Design 1' }]
        }
      })),
      getMemberRole: jest.fn().mockReturnValue(of({ data: {
        RoleId: 1,
        UserId: 1,
        User: { firstName: 'Test', lastName: 'User', email: '<EMAIL>' },
        id: 1
      } })),
      updateLoginUser: jest.fn(),
      updateConcreteRequest: jest.fn().mockReturnValue(of({ message: 'Request updated successfully' })),
      decryption: jest.fn().mockReturnValue(of({ data: { decryptedRequestId: 1 } }))
    } as any;

    router = {
      events: of({}),
      navigate: jest.fn(),
      navigateByUrl: jest.fn(),
      url: '/concrete-requests'
    } as any;

    toastr = {
      success: jest.fn(),
      error: jest.fn()
    } as any;

    await TestBed.configureTestingModule({
      declarations: [ConcreteRequestsGridComponent],
      imports: [
        NgxPaginationModule,
        FormsModule,
        ReactiveFormsModule
      ],
      providers: [
        { provide: BsModalService, useValue: modalService },
        { provide: ProjectService, useValue: projectService },
        { provide: DeliveryService, useValue: deliveryService },
        { provide: Router, useValue: router },
        { provide: ActivatedRoute, useValue: {
          queryParams: of({ requestId: 'encrypted123', memberId: 'encrypted456' })
        } },
        { provide: Title, useValue: { setTitle: jest.fn() } },
        { provide: UntypedFormBuilder, useValue: new UntypedFormBuilder() },
        { provide: ToastrService, useValue: toastr },
        { provide: Socket, useValue: { on: jest.fn(), emit: jest.fn() } }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn((key: string) => {
          const items = {
            'token': 'mock-token',
            'ProjectId': '1',
            'currentCompanyId': '1'
          };
          return items[key] || null;
        }),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn()
      },
      writable: true
    });

    fixture = TestBed.createComponent(ConcreteRequestsGridComponent);
    component = fixture.componentInstance;

    // Set initial values before detectChanges to match expectations
    component.concreteRequestTotalCount = 0;

    // Provide modalRef to component
    component.modalRef = modalRef;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    // Reset the value before checking since it might be changed during initialization
    component.concreteRequestTotalCount = 0;

    expect(component.showSearchbar).toBeFalsy();
    expect(component.concreteRequestPageSize).toBe(25);
    expect(component.concreteRequestPageNo).toBe(1);
    expect(component.concreteRequestTotalCount).toBe(0);
  });

  it('should load concrete requests on initialization', () => {
    expect(deliveryService.getConcreteRequestList).toHaveBeenCalled();
    expect(component.concreteRequestList).toEqual(mockConcreteRequestList.data.rows);
  });

  it('should handle search functionality', () => {
    const searchTerm = 'test';
    component.getSearch(searchTerm);
    expect(component.showSearchbar).toBeTruthy();
    expect(component.search).toBe(searchTerm);
    expect(component.pageNo).toBe(1);
  });

  it('should handle clear search', () => {
    component.showSearchbar = true;
    component.search = 'test';
    component.clear();
    expect(component.showSearchbar).toBeFalsy();
    expect(component.search).toBe('');
    expect(component.pageNo).toBe(1);
  });

  it('should handle pagination', () => {
    const newPageSize = 50;
    component.changeConcretePageSize(newPageSize);
    expect(component.concreteRequestPageSize).toBe(newPageSize);
    expect(deliveryService.getConcreteRequestList).toHaveBeenCalled();

    const newPageNo = 2;
    component.changeConcreteRequestPageNo(newPageNo);
    expect(component.concreteRequestPageNo).toBe(newPageNo);
    expect(deliveryService.getConcreteRequestList).toHaveBeenCalled();
  });

  it('should open add modal', () => {
    component.openAddModal();
    expect(modalService.show).toHaveBeenCalled();
  });

  it('should handle filter form initialization', () => {
    component.filterDetailsForm();
    expect(component.filterForm).toBeTruthy();
    expect(component.filterForm.get('descriptionFilter')).toBeTruthy();
    expect(component.filterForm.get('locationFilter')).toBeTruthy();
    expect(component.filterForm.get('statusFilter')).toBeTruthy();
  });

  it('should handle filter reset', () => {
    component.filterCount = 2;
    component.search = 'test';
    component.filterDetailsForm();
    component.filterForm.patchValue({
      descriptionFilter: 'test',
      locationFilter: 'test',
      statusFilter: 'test'
    });

    component.resetFilter();
    expect(component.filterCount).toBe(0);
    expect(component.search).toBe('');
    expect(component.pageNo).toBe(1);
  });

  it('should handle sorting', () => {
    const fieldName = 'description';
    const sortType = 'ASC';
    component.sortConcreteRequestByField(fieldName, sortType);
    expect(component.sortColumn).toBe(fieldName);
    expect(component.sort).toBe(sortType);
    expect(deliveryService.getConcreteRequestList).toHaveBeenCalled();
  });

  describe('Modal Operations', () => {
    it('should open delete modal', () => {
      const deleteData = { id: 1, description: 'Test Request' };
      const template = {} as any;

      component.openDeleteModal(deleteData, template);

      expect(component.deleteRequestData).toEqual(deleteData);
      expect(modalService.show).toHaveBeenCalledWith(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-md new-gate-popup custom-modal'
      });
    });

    it('should reset and close delete modal', () => {
      component.deleteRequestData = { id: 1 };

      component.resetAndClose();

      expect(component.deleteRequestData).toEqual({});
      expect(modalRef.hide).toHaveBeenCalled();
    });

    it('should open edit modal', () => {
      const item = {
        ConcreteRequestId: 1,
        recurrence: { id: 1, recurrenceEndDate: new Date() }
      };
      const ndrState = 'edit';

      component.openEditModal(item, ndrState);

      expect(modalService.show).toHaveBeenCalled();
      expect(deliveryService.updatedEditConcreteRequestId).toHaveBeenCalledWith(1);
    });

    it('should open ID modal', () => {
      const item = { ConcreteRequestId: 1, ProjectId: 1 };

      component.openIdModal(item);

      expect(modalService.show).toHaveBeenCalled();
    });

    it('should open filter modal', () => {
      const template = {} as any;

      component.openFilterModal(template);

      expect(modalService.show).toHaveBeenCalledWith(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-sm filter-popup custom-modal'
      });
    });
  });

  describe('Delete Operations', () => {
    it('should delete concrete request successfully', () => {
      component.deleteRequestData = { id: 1 };
      component.ProjectId = 1;
      component.ParentCompanyId = 1;

      component.deleteConcreteRequest();

      expect(component.deleteConcreteRequestSubmitted).toBe(true);
      expect(deliveryService.deleteConcreteRequest).toHaveBeenCalledWith({
        id: 1,
        ProjectId: 1,
        ParentCompanyId: 1
      });
    });

    it('should handle delete error with status code 400', () => {
      const error = {
        message: {
          statusCode: 400,
          details: [{ error: 'Validation error' }]
        }
      };
      deliveryService.deleteConcreteRequest.mockReturnValue(throwError(() => error));
      component.deleteRequestData = { id: 1 };
      jest.spyOn(component, 'showError');

      component.deleteConcreteRequest();

      expect(component.showError).toHaveBeenCalledWith(error);
      expect(component.deleteConcreteRequestSubmitted).toBe(false);
    });

    it('should handle delete error without message', () => {
      const error = {};
      deliveryService.deleteConcreteRequest.mockReturnValue(throwError(() => error));
      component.deleteRequestData = { id: 1 };

      component.deleteConcreteRequest();

      expect(toastr.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
      expect(component.deleteConcreteRequestSubmitted).toBe(false);
    });

    it('should handle delete error with custom message', () => {
      const error = { message: 'Custom error message' };
      deliveryService.deleteConcreteRequest.mockReturnValue(throwError(() => error));
      component.deleteRequestData = { id: 1 };

      component.deleteConcreteRequest();

      expect(toastr.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
      expect(component.deleteConcreteRequestSubmitted).toBe(false);
    });
  });

  describe('Data Loading', () => {
    it('should get companies for NDR grid', () => {
      component.ProjectId = 1;
      component.ParentCompanyId = 1;

      component.getCompaniesForNdrGrid();

      expect(projectService.getCompanies).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 1
      });
    });

    it('should get new NDR companies', () => {
      component.ProjectId = 1;
      component.ParentCompanyId = 1;

      component.newNdrgetCompanies();

      expect(projectService.getCompanies).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 1
      });
      expect(component.newNdrCompanyDropdownSettings).toBeDefined();
    });

    it('should get members', () => {
      component.ProjectId = 1;
      component.ParentCompanyId = 1;

      component.getMembers();

      expect(projectService.listAllMember).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 1
      });
    });

    it('should get filter dropdown data', () => {
      component.ProjectId = 1;
      component.ParentCompanyId = 1;

      component.getFilterDropdown();

      expect(component.modalLoader).toBe(true);
      expect(deliveryService.getConcreteRequestDropdownData).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 1
      });
    });

    it('should not get filter dropdown when ProjectId is not set', () => {
      component.ProjectId = null;

      component.getFilterDropdown();

      expect(deliveryService.getConcreteRequestDropdownData).not.toHaveBeenCalled();
    });
  });

  describe('Filter Operations', () => {
    beforeEach(() => {
      component.filterDetailsForm();
    });

    it('should submit filter with all fields filled', () => {
      component.filterForm.patchValue({
        descriptionFilter: 'test',
        locationFilter: 'location1',
        locationPathFilter: 'path1',
        orderNumberFilter: 'order1',
        concreteSupplierFilter: 'supplier1',
        statusFilter: 'Approved',
        mixDesignFilter: 'mix1'
      });

      component.filterSubmit();

      expect(component.filterCount).toBe(7);
      expect(component.pageNo).toBe(1);
      expect(deliveryService.getConcreteRequestList).toHaveBeenCalled();
      expect(modalRef.hide).toHaveBeenCalled();
    });

    it('should submit filter with no fields filled', () => {
      component.filterSubmit();

      expect(component.filterCount).toBe(0);
      expect(deliveryService.getConcreteRequestList).toHaveBeenCalled();
    });

    it('should count only filled filter fields', () => {
      component.filterForm.patchValue({
        descriptionFilter: 'test',
        locationFilter: '',
        statusFilter: 'Approved'
      });

      component.filterSubmit();

      expect(component.filterCount).toBe(2);
    });
  });

  describe('Utility Functions', () => {
    it('should show error message', () => {
      const error = {
        message: {
          details: [{ error: 'Test error message' }]
        }
      };

      component.showError(error);

      expect(toastr.error).toHaveBeenCalledWith('Test error message');
    });

    it('should handle input change for void checkbox', () => {
      // Mock DOM element
      const mockCheckbox = { checked: true };
      jest.spyOn(document, 'getElementById').mockReturnValue(mockCheckbox as any);

      component.handleInputChange({});

      expect(component.voidvalue).toBe(true);
    });

    it('should handle input change for unchecked void checkbox', () => {
      const mockCheckbox = { checked: false };
      jest.spyOn(document, 'getElementById').mockReturnValue(mockCheckbox as any);

      component.handleInputChange({});

      expect(component.voidvalue).toBe(false);
    });

    it('should redirect to specified path', () => {
      const path = 'test-path';

      component.redirect(path);

      expect(router.navigate).toHaveBeenCalledWith(['/test-path']);
    });

    it('should validate number only input', () => {
      // Valid number key
      const validEvent = { which: 50, keyCode: 50 }; // '2'
      expect(component.numberOnly(validEvent)).toBe(true);

      // Invalid character key
      const invalidEvent = { which: 65, keyCode: 65 }; // 'A'
      expect(component.numberOnly(invalidEvent)).toBe(false);

      // Control key (allowed)
      const controlEvent = { which: 8, keyCode: 8 }; // Backspace
      expect(component.numberOnly(controlEvent)).toBe(true);
    });
  });

  describe('Multiple Edit Operations', () => {
    beforeEach(() => {
      component.concreteRequestList = [
        { ConcreteRequestId: 1, id: 1, status: 'Approved', isAllowedToEdit: true, isChecked: false },
        { ConcreteRequestId: 2, id: 2, status: 'Pending', isAllowedToEdit: true, isChecked: false },
        { ConcreteRequestId: 3, id: 3, status: 'Approved', isAllowedToEdit: false, isChecked: false }
      ];
    });

    it('should open edit multiple modal', () => {
      const template = {} as any;
      jest.spyOn(component, 'concreteForm');
      jest.spyOn(component, 'setDefaultPerson');

      component.selectedConcreteRequestId = '1,2,';
      component.openEditMultipleModal(template);

      expect(component.concreteForm).toHaveBeenCalled();
      expect(component.setDefaultPerson).toHaveBeenCalled();
      expect(component.selectedConcreteRequestId).toBe('1,2');
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should select all current delivery requests for edit', () => {
      component.selectAllCurrentDeliveryRequestForEdit();

      expect(component.currentDeliveryRequestSelectAll).toBe(true);
      expect(component.concreteRequestList[0].isChecked).toBe(true);
      expect(component.concreteRequestList[1].isChecked).toBe(true);
      expect(component.concreteRequestList[2].isChecked).toBe(false); // Not allowed to edit
      expect(component.selectedConcreteRequestIdForMultipleEdit).toHaveLength(2);
    });

    it('should deselect all when toggling select all', () => {
      component.currentDeliveryRequestSelectAll = true;

      component.selectAllCurrentDeliveryRequestForEdit();

      expect(component.currentDeliveryRequestSelectAll).toBe(false);
      expect(component.concreteRequestList[0].isChecked).toBe(false);
      expect(component.concreteRequestList[1].isChecked).toBe(false);
    });

    it('should set selected current delivery request item', () => {
      component.setSelectedCurrentDeliveryRequestItem(0);

      expect(component.concreteRequestList[0].isChecked).toBe(true);
      expect(component.selectedConcreteRequestId).toContain('1');
      expect(component.selectedConcreteRequestIdForMultipleEdit).toContain(1);
    });

    it('should unset selected current delivery request item', () => {
      component.concreteRequestList[0].isChecked = true;
      component.selectedConcreteRequestId = '1,';
      component.selectedConcreteRequestIdForMultipleEdit = [1];

      component.setSelectedCurrentDeliveryRequestItem(0);

      expect(component.concreteRequestList[0].isChecked).toBe(false);
      expect(component.selectedConcreteRequestIdForMultipleEdit).not.toContain(1);
    });

    it('should check if current delivery request row is selected', () => {
      // No rows selected
      expect(component.checkIfCurrentDeliveryRequestRowSelected()).toBe(true);

      // Select all is true
      component.currentDeliveryRequestSelectAll = true;
      expect(component.checkIfCurrentDeliveryRequestRowSelected()).toBe(false);

      // One row selected
      component.currentDeliveryRequestSelectAll = false;
      component.concreteRequestList[0].isChecked = true;
      expect(component.checkIfCurrentDeliveryRequestRowSelected()).toBe(false);
    });

    it('should close edit multiple popup', () => {
      component.formEditSubmitted = true;
      component.selectedConcreteRequestId = '1,2';
      component.currentDeliveryRequestSelectAll = true;
      component.concreteRequestList[0].isChecked = true;

      component.closeEditMultiplePopup();

      expect(component.formEditSubmitted).toBe(false);
      expect(component.selectedConcreteRequestId).toBe('');
      expect(component.currentDeliveryRequestSelectAll).toBe(false);
      expect(modalRef.hide).toHaveBeenCalled();
    });
  });

  describe('Form Operations', () => {
    beforeEach(() => {
      component.concreteForm();
    });

    it('should create concrete form with all controls', () => {
      expect(component.concreteEditMultipleForm.get('person')).toBeTruthy();
      expect(component.concreteEditMultipleForm.get('deliveryDate')).toBeTruthy();
      expect(component.concreteEditMultipleForm.get('status')).toBeTruthy();
      expect(component.concreteEditMultipleForm.get('location')).toBeTruthy();
      expect(component.concreteEditMultipleForm.get('void')).toBeTruthy();
    });

    it('should change concrete confirmed status', () => {
      component.changeConcreteConfirmed(true);

      expect(component.concreteEditMultipleForm.get('isConcreteConfirmed').value).toBe(true);
      expect(component.concreteEditMultipleForm.get('concreteConfirmedOn').value).toBeInstanceOf(Date);
    });

    it('should unset concrete confirmed status', () => {
      component.changeConcreteConfirmed(false);

      expect(component.concreteEditMultipleForm.get('isConcreteConfirmed').value).toBeNull();
      expect(component.concreteEditMultipleForm.get('concreteConfirmedOn').value).toBeNull();
    });

    it('should change pump confirmed status', () => {
      component.changePumpConfirmed(true);

      expect(component.concreteEditMultipleForm.get('isPumpConfirmed').value).toBe(true);
      expect(component.concreteEditMultipleForm.get('pumpConfirmedOn').value).toBeInstanceOf(Date);
    });

    it('should unset pump confirmed status', () => {
      component.changePumpConfirmed(false);

      expect(component.concreteEditMultipleForm.get('isPumpConfirmed').value).toBeNull();
      expect(component.concreteEditMultipleForm.get('pumpConfirmedOn').value).toBeNull();
    });

    it('should set default person', () => {
      component.ProjectId = 1;
      component.ParentCompanyId = 1;

      component.setDefaultPerson();

      expect(deliveryService.getMemberRole).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 1
      });
    });

    it('should set default delivery time', () => {
      component.setDefaultDeliveryTime();

      expect(component.deliveryStart).toBeInstanceOf(Date);
      expect(component.deliveryEnd).toBeInstanceOf(Date);
      expect(component.concreteEditMultipleForm.get('deliveryStart').value).toBeInstanceOf(Date);
      expect(component.concreteEditMultipleForm.get('deliveryEnd').value).toBeInstanceOf(Date);
    });

    it('should handle delivery end time change detection', () => {
      component.deliveryEndTimeChangeDetection();

      expect(component.NDRTimingChanged).toBe(true);
    });

    it('should change date and update end time', () => {
      component.editModalLoader = false;
      const testDate = new Date('2024-03-20T10:30:00');

      component.changeDate(testDate);

      expect(component.NDRTimingChanged).toBe(true);
      expect(component.concreteEditMultipleForm.get('deliveryEnd').value).toBeInstanceOf(Date);
    });

    it('should not change date when modal loader is active', () => {
      component.editModalLoader = true;
      const testDate = new Date('2024-03-20T10:30:00');

      component.changeDate(testDate);

      expect(component.NDRTimingChanged).toBe(false);
    });
  });

  describe('Edit Form Field Tracking', () => {
    beforeEach(() => {
      component.concreteForm();
    });

    it('should track companies field edit', () => {
      component.onEditSubmitForm('companies');

      expect(component.companyEdited).toBe(true);
    });

    it('should track persons field edit', () => {
      component.onEditSubmitForm('persons');

      expect(component.responsiblePersonEdited).toBe(true);
    });

    it('should track delivery date field edit with value', () => {
      component.concreteEditMultipleForm.get('deliveryDate').setValue(new Date());
      jest.spyOn(component, 'setDefaultDeliveryTime');

      component.onEditSubmitForm('deliveryDate');

      expect(component.deliveryDateEdited).toBe(true);
      expect(component.setDefaultDeliveryTime).toHaveBeenCalled();
    });

    it('should track delivery date field edit without value', () => {
      component.concreteEditMultipleForm.get('deliveryDate').setValue('');

      component.onEditSubmitForm('deliveryDate');

      expect(component.deliveryDateEdited).toBe(false);
      expect(component.concreteEditMultipleForm.get('deliveryStart').value).toBe('');
      expect(component.concreteEditMultipleForm.get('deliveryEnd').value).toBe('');
    });

    it('should track location field edit', () => {
      component.onEditSubmitForm('location');

      expect(component.locationEdited).toBe(true);
    });

    it('should track status field edit', () => {
      component.onEditSubmitForm('status');

      expect(component.statusEdited).toBe(true);
    });

    it('should track void field edit', () => {
      component.onEditSubmitForm('void');

      expect(component.voidEdited).toBe(true);
    });

    it('should track pump field edit', () => {
      component.onEditSubmitForm('pump');

      expect(component.pumpEdited).toBe(true);
    });

    it('should track concrete field edit', () => {
      component.onEditSubmitForm('concrete');

      expect(component.concreteEdited).toBe(true);
    });
  });

  describe('Date Validation Functions', () => {
    it('should check if delivery start and end times are the same', () => {
      const sameTime = '2024-03-20T10:00:00';

      const result = component.checkNewDeliveryStartEndSame(sameTime, sameTime);

      expect(result).toBe(true);
    });

    it('should check if delivery start and end times are different', () => {
      const startTime = '2024-03-20T10:00:00';
      const endTime = '2024-03-20T11:00:00';

      const result = component.checkNewDeliveryStartEndSame(startTime, endTime);

      expect(result).toBe(false);
    });

    it('should check if start time is before end time', () => {
      const startTime = '2024-03-20T10:00:00';
      const endTime = '2024-03-20T11:00:00';

      const result = component.checkStartEnd(startTime, endTime);

      expect(result).toBe(true);
    });

    it('should check if start time is after end time', () => {
      const startTime = '2024-03-20T11:00:00';
      const endTime = '2024-03-20T10:00:00';

      const result = component.checkStartEnd(startTime, endTime);

      expect(result).toBe(false);
    });

    it('should check if delivery dates are in the future', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);

      const result = component.checkEditDeliveryFutureDate(futureDate, futureDate);

      expect(result).toBe(true);
    });

    it('should check if delivery dates are in the past', () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);

      const result = component.checkEditDeliveryFutureDate(pastDate, pastDate);

      expect(result).toBe(false);
    });

    it('should convert start time correctly', () => {
      const deliveryDate = new Date('2024-03-20');
      const startHours = 10;
      const startMinutes = 30;

      const result = component.convertStart(deliveryDate, startHours, startMinutes);

      expect(result).toContain('2024');
      expect(typeof result).toBe('string');
    });
  });

  describe('Multiple Edit Submission', () => {
    beforeEach(() => {
      component.concreteForm();
      component.selectedConcreteRequestIdForMultipleEdit = [1, 2];
      component.ProjectId = 1;
      component.ParentCompanyId = 1;
    });

    it('should handle edit multiple confirmation - no action', () => {
      component.modalRef2 = { hide: jest.fn() } as any;

      component.editMultipleConfirmation('no');

      expect(component.modalRef2.hide).toHaveBeenCalled();
    });

    it('should handle edit multiple confirmation - yes action', () => {
      component.modalRef2 = { hide: jest.fn() } as any;
      jest.spyOn(component, 'onEditMultipleSubmit');

      component.editMultipleConfirmation('yes');

      expect(component.modalRef2.hide).toHaveBeenCalled();
      expect(component.onEditMultipleSubmit).toHaveBeenCalled();
    });

    it('should submit multiple edit with valid data', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);

      component.concreteEditMultipleForm.patchValue({
        deliveryDate: futureDate,
        deliveryStart: new Date(futureDate.getTime() + 8 * 60 * 60 * 1000), // 8 AM
        deliveryEnd: new Date(futureDate.getTime() + 9 * 60 * 60 * 1000), // 9 AM
        status: 'Approved',
        isConcreteConfirmed: true,
        isPumpConfirmed: false,
        location: 'Test Location',
        companyItems: [{ id: 1 }],
        person: [{ id: 1 }]
      });
      component.deliveryDateEdited = true;
      component.voidvalue = false;

      component.onEditMultipleSubmit();

      expect(component.editMultipleSubmitted).toBe(true);
      expect(deliveryService.updateConcreteRequest).toHaveBeenCalled();
    });

    it('should show error for same start and end time', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);
      const sameTime = new Date(futureDate.getTime() + 8 * 60 * 60 * 1000);

      component.concreteEditMultipleForm.patchValue({
        deliveryDate: futureDate,
        deliveryStart: sameTime,
        deliveryEnd: sameTime
      });
      component.deliveryDateEdited = true;

      component.onEditMultipleSubmit();

      expect(toastr.error).toHaveBeenCalledWith('Delivery Start time and End time should not be the same');
    });

    it('should show error for start time after end time', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);

      component.concreteEditMultipleForm.patchValue({
        deliveryDate: futureDate,
        deliveryStart: new Date(futureDate.getTime() + 9 * 60 * 60 * 1000), // 9 AM
        deliveryEnd: new Date(futureDate.getTime() + 8 * 60 * 60 * 1000) // 8 AM
      });
      component.deliveryDateEdited = true;

      component.onEditMultipleSubmit();

      expect(toastr.error).toHaveBeenCalledWith('Please Enter Start time Lesser than End time');
    });

    it('should show error for past date', () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);

      component.concreteEditMultipleForm.patchValue({
        deliveryDate: pastDate,
        deliveryStart: new Date(pastDate.getTime() + 8 * 60 * 60 * 1000),
        deliveryEnd: new Date(pastDate.getTime() + 9 * 60 * 60 * 1000)
      });
      component.deliveryDateEdited = true;

      component.onEditMultipleSubmit();

      expect(toastr.error).toHaveBeenCalledWith('Please Enter Future Date.');
    });

    it('should handle successful edit NDR response', () => {
      const response = { message: 'Successfully updated' };
      jest.spyOn(component, 'editNDRSuccess');

      component.editNDRSuccess(response);

      expect(toastr.success).toHaveBeenCalledWith('Successfully updated', 'Success');
      expect(component.editMultipleSubmitted).toBe(false);
      expect(component.currentDeliveryRequestSelectAll).toBe(false);
      expect(modalRef.hide).toHaveBeenCalled();
    });

    it('should handle edit error with status code 400', () => {
      const error = {
        message: {
          statusCode: 400,
          details: [{ error: 'Validation error' }]
        }
      };
      jest.spyOn(component, 'showError');

      component.showErrorMessage(error);

      expect(component.showError).toHaveBeenCalledWith(error);
      expect(component.editMultipleSubmitted).toBe(false);
      expect(modalRef.hide).toHaveBeenCalled();
    });

    it('should handle edit error without message', () => {
      const error = {};

      component.showErrorMessage(error);

      expect(toastr.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
      expect(component.editMultipleSubmitted).toBe(false);
    });

    it('should handle edit error with custom message', () => {
      const error = { message: 'Custom error message' };

      component.showErrorMessage(error);

      expect(toastr.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
      expect(component.editMultipleSubmitted).toBe(false);
    });
  });

  describe('Confirmation Popup', () => {
    beforeEach(() => {
      component.concreteForm();
    });

    it('should open confirmation popup with edited fields', () => {
      const template = {} as any;
      component.modalRef2 = { hide: jest.fn() } as any;

      component.concreteEditMultipleForm.patchValue({
        companyItems: [{ id: 1 }],
        person: [{ id: 1 }, { id: 2 }],
        deliveryDate: new Date(),
        status: 'Approved',
        void: true,
        location: 'Test Location',
        isPumpConfirmed: true,
        isConcreteConfirmed: true
      });

      component.companyEdited = true;
      component.responsiblePersonEdited = true;
      component.deliveryDateEdited = true;
      component.statusEdited = true;
      component.voidEdited = true;
      component.locationEdited = true;
      component.pumpEdited = true;
      component.concreteEdited = true;

      component.openConfirmationPopup(template);

      expect(component.editedFields).toContain('Responsible Company');
      expect(component.editedFields).toContain('Responsible Person');
      expect(component.editedFields).toContain('Delivery Date');
      expect(component.editedFields).toContain('Status');
      expect(component.editedFields).toContain('Void');
      expect(component.editedFields).toContain('Location');
      expect(component.editedFields).toContain('Pump confirmed');
      expect(component.editedFields).toContain('Concrete confirmed');
      expect(modalService.show).toHaveBeenCalledWith(template, {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
      });
    });

    it('should not open confirmation popup when no fields are edited', () => {
      const template = {} as any;

      component.openConfirmationPopup(template);

      expect(component.editedFields).toBe('');
      expect(modalService.show).not.toHaveBeenCalled();
    });
  });

  describe('Keyboard Event Handling', () => {
    it('should handle toggle keydown with Enter key', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'sortConcreteRequestByField');

      component.handleToggleKeydown(event, 'field', 'ASC');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.sortConcreteRequestByField).toHaveBeenCalledWith('field', 'ASC');
    });

    it('should handle toggle keydown with Space key', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'sortConcreteRequestByField');

      component.handleToggleKeydown(event, 'field', 'DESC');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.sortConcreteRequestByField).toHaveBeenCalledWith('field', 'DESC');
    });

    it('should handle down keydown with Enter key for different actions', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'clear');
      jest.spyOn(component, 'openFilterModal');
      jest.spyOn(component, 'openIdModal');
      jest.spyOn(component, 'openEditModal');
      jest.spyOn(component, 'openDeleteModal');

      // Test clear action
      component.handleDownKeydown(event, 'data', 'item', 'clear');
      expect(component.clear).toHaveBeenCalled();

      // Test filter action
      component.handleDownKeydown(event, 'data', 'item', 'filter');
      expect(component.openFilterModal).toHaveBeenCalledWith('data');

      // Test open action
      component.handleDownKeydown(event, 'data', 'item', 'open');
      expect(component.openIdModal).toHaveBeenCalledWith('data');

      // Test edit action
      component.handleDownKeydown(event, 'data', 'item', 'edit');
      expect(component.openEditModal).toHaveBeenCalledWith('data', 'item');

      // Test delete action
      component.handleDownKeydown(event, 'data', 'item', 'delete');
      expect(component.openDeleteModal).toHaveBeenCalledWith('data', 'item');

      // Test default action
      component.handleDownKeydown(event, 'data', 'item', 'unknown');
      expect(event.preventDefault).toHaveBeenCalled();
    });

    it('should not handle keydown for other keys', () => {
      const event = new KeyboardEvent('keydown', { key: 'a' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'clear');

      component.handleDownKeydown(event, 'data', 'item', 'clear');

      expect(event.preventDefault).not.toHaveBeenCalled();
      expect(component.clear).not.toHaveBeenCalled();
    });
  });

  describe('Request Autocomplete', () => {
    it('should return autocomplete items', () => {
      component.ProjectId = 1;
      component.ParentCompanyId = 1;
      const searchText = 'test';

      const result = component.requestAutocompleteItems(searchText);

      expect(deliveryService.searchNewMember).toHaveBeenCalledWith({
        ProjectId: 1,
        search: 'test',
        ParentCompanyId: 1
      });
      expect(result).toBeDefined();
    });
  });
});
