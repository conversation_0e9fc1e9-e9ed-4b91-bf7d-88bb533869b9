import { ComponentFixture } from '@angular/core/testing';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { Socket } from 'ngx-socket-io';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';
import { MixpanelService } from '../../services/mixpanel.service';
import { CraneRequestDetailViewHeaderComponent } from './crane-request-detail-view-header.component';
import { Observable, of, BehaviorSubject, throwError } from 'rxjs';
import moment from 'moment';

describe('CraneRequestDetailViewHeaderComponent', () => {
  let component: CraneRequestDetailViewHeaderComponent;
  let fixture: ComponentFixture<CraneRequestDetailViewHeaderComponent>;
  let modalService: jest.Mocked<BsModalService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let projectService: jest.Mocked<ProjectService>;
  let toastrService: jest.Mocked<ToastrService>;
  let router: jest.Mocked<Router>;
  let socket: jest.Mocked<Socket>;
  let mixpanelService: jest.Mocked<MixpanelService>;
  let bsModalRef: jest.Mocked<BsModalRef>;

  beforeEach(() => {
    modalService = {
      show: jest.fn().mockReturnValue({
        content: {
          closeBtnName: '',
          seriesOption: 1,
          recurrenceId: null,
          recurrenceEndDate: null
        }
      }),
    } as any;

    bsModalRef = {
      hide: jest.fn(),
    } as any;

    deliveryService = {
      loginUser: new BehaviorSubject({ RoleId: 2, id: 1, UserId: 1 }),
      getCurrentCraneRequestStatus: new BehaviorSubject(123),
      fetchData: new BehaviorSubject({}),
      fetchData1: new BehaviorSubject({}),
      updateCraneRequestStatus: jest.fn().mockReturnValue(of({ message: 'Success' })),
      updateCraneRequestHistory: jest.fn(),
      updatedEditCraneRequestId: jest.fn(),
      getEquipmentCraneRequest: jest.fn().mockReturnValue(of({
        data: {
          id: 123,
          status: 'Pending',
          CraneRequestId: 123,
          memberDetails: [{ Member: { id: 1 } }],
          equipmentDetails: [{ Equipment: { id: 1 } }],
          createdUserDetails: { RoleId: 4, User: { id: 1 } },
          voidList: [],
          recurrence: null
        }
      })),
      addCraneRequestToVoid: jest.fn().mockReturnValue(of({ message: 'Voided successfully' })),
      updatedHistory: jest.fn(),
    } as any;

    projectService = {
      projectParent: new BehaviorSubject({ ProjectId: 456, ParentCompanyId: 789 }),
      isMyAccount: new BehaviorSubject({}),
      isProject: new BehaviorSubject({}),
      listAllMember: jest.fn().mockReturnValue(of({ data: [{ id: 1, name: 'Test Member' }] })),
      listEquipment: jest.fn().mockReturnValue(of({ data: [{ id: 1, name: 'Test Equipment' }] })),
      updateAccountProjectParent: jest.fn(),
    } as any;

    toastrService = {
      success: jest.fn(),
      error: jest.fn(),
      clear: jest.fn(),
    } as any;

    router = {
      navigate: jest.fn(),
      events: new BehaviorSubject({}),
    } as any;

    socket = {
      emit: jest.fn(),
      on: jest.fn(),
    } as any;

    mixpanelService = {
      addMixpanelEvents: jest.fn(),
    } as any;

    component = new CraneRequestDetailViewHeaderComponent(
      modalService,
      bsModalRef,
      deliveryService,
      socket,
      mixpanelService,
      router,
      toastrService,
      projectService,
    );
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.showStatus).toBe(true); // Default is true based on component
    expect(component.currentStatus).toBe('');
    expect(component.statusSubmitted).toBe(false);
    expect(component.voidSubmitted).toBe(false);
    expect(component.void).toBe(false);
    expect(component.show).toBe(true);
    expect(component.currentTabId).toBe(0);
    expect(component.statusChanged).toBe(false);
    expect(component.allRequestIsOpened).toBe(false);
  });

  describe('Constructor Subscriptions', () => {
    it('should handle loginUser subscription with RoleId 2', () => {
      const userData = { RoleId: 2, id: 1, UserId: 1 };
      deliveryService.loginUser.next(userData);

      expect(component.authUser).toEqual(userData);
      expect(component.statusValue).toEqual(['Approved', 'Declined']);
    });

    it('should handle loginUser subscription with RoleId 3', () => {
      const userData = { RoleId: 3, id: 1, UserId: 1 };
      deliveryService.loginUser.next(userData);

      expect(component.authUser).toEqual(userData);
      expect(component.statusValue).toEqual(['Completed']);
    });

    it('should handle loginUser subscription with RoleId 1', () => {
      const userData = { RoleId: 1, id: 1, UserId: 1 };
      deliveryService.loginUser.next(userData);

      expect(component.authUser).toEqual(userData);
      expect(component.statusValue).toEqual(['Approved', 'Declined']);
    });

    it('should handle projectParent subscription', () => {
      const projectData = { ProjectId: 456, ParentCompanyId: 789 };
      projectService.projectParent.next(projectData);

      expect(component.ProjectId).toBe(456);
      expect(component.ParentCompanyId).toBe(789);
    });

    it('should handle getCurrentCraneRequestStatus subscription', () => {
      jest.spyOn(component, 'getCraneRequest').mockImplementation(() => {});

      deliveryService.getCurrentCraneRequestStatus.next(123);

      expect(component.CraneRequestId).toBe(123);
      expect(component.getCraneRequest).toHaveBeenCalled();
    });

    it('should handle fetchData subscription', () => {
      jest.spyOn(component, 'getCraneRequest').mockImplementation(() => {});

      deliveryService.fetchData.next({ data: 'test' });

      expect(component.currentTabId).toBe(0);
      expect(component.getCraneRequest).toHaveBeenCalled();
    });

    it('should handle fetchData1 subscription', () => {
      jest.spyOn(component, 'getCraneRequest').mockImplementation(() => {});

      deliveryService.fetchData1.next({ data: 'test' });

      expect(component.currentTabId).toBe(0);
      expect(component.getCraneRequest).toHaveBeenCalled();
    });

    it('should handle isMyAccount subscription', () => {
      projectService.isMyAccount.next({ account: true });

      expect(component.myAccount).toBe(true);
    });

    it('should handle isProject subscription', () => {
      component.myAccount = true;
      component.accountAdmin = true;

      projectService.isProject.next({ project: true });

      expect(component.myAccount).toBe(false);
      expect(component.accountAdmin).toBe(false);
    });

    it('should call getOverAllEquipmentforEditNdr and getMembers in constructor', () => {
      jest.spyOn(component, 'getOverAllEquipmentforEditNdr').mockImplementation(() => {});
      jest.spyOn(component, 'getMembers').mockImplementation(() => {});

      // Create new component to test constructor calls
      const newComponent = new CraneRequestDetailViewHeaderComponent(
        modalService,
        bsModalRef,
        deliveryService,
        socket,
        mixpanelService,
        router,
        toastrService,
        projectService,
      );

      expect(newComponent).toBeTruthy();
    });
  });

  describe('selectStatus', () => {
    it('should update currentStatus and set statusChanged to true', () => {
      component.selectStatus('Approved');
      expect(component.currentStatus).toBe('Approved');
      expect(component.statusChanged).toBe(true);
    });
  });

  describe('eventCheck', () => {
    it('should set currentStatus to Completed when checked', () => {
      component.eventCheck({ target: { checked: true } });
      expect(component.currentStatus).toBe('Completed');
      expect(component.statusChanged).toBe(true);
    });

    it('should clear currentStatus when unchecked', () => {
      component.eventCheck({ target: { checked: false } });
      expect(component.currentStatus).toBe('');
    });
  });

  describe('clickAndDisable', () => {
    it('should disable subsequent clicks on link', () => {
      const mockLink = {
        onclick: null
      };

      component.clickAndDisable(mockLink);

      expect(mockLink.onclick).toBeDefined();

      // Test the onclick function
      const mockEvent = { preventDefault: jest.fn() };
      mockLink.onclick(mockEvent);
      expect(mockEvent.preventDefault).toHaveBeenCalled();
    });
  });

  describe('getMembers', () => {
    beforeEach(() => {
      component.ProjectId = 456;
      component.ParentCompanyId = 789;
    });

    it('should fetch and set member list', () => {
      const mockResponse = { data: [{ id: 1, name: 'Test Member' }] };
      projectService.listAllMember.mockReturnValue(of(mockResponse));

      component.getMembers();

      expect(projectService.listAllMember).toHaveBeenCalledWith({
        ProjectId: 456,
        ParentCompanyId: 789
      });
      expect(component.memberList).toEqual(mockResponse.data);
    });

    it('should handle empty response', () => {
      projectService.listAllMember.mockReturnValue(of({ data: null }));

      component.getMembers();

      expect(component.memberList).toEqual(null);
    });
  });

  describe('getOverAllEquipmentforEditNdr', () => {
    beforeEach(() => {
      component.ProjectId = 456;
      component.ParentCompanyId = 789;
    });

    it('should fetch and set equipment list', () => {
      const mockResponse = { data: [{ id: 1, name: 'Test Equipment' }] };
      projectService.listEquipment.mockReturnValue(of(mockResponse));

      component.getOverAllEquipmentforEditNdr();

      expect(projectService.listEquipment).toHaveBeenCalledWith(
        {
          ProjectId: 456,
          pageSize: 0,
          pageNo: 0,
          ParentCompanyId: 789
        },
        {
          isFilter: true,
          showActivatedAlone: true
        }
      );
      expect(component.equipmentList).toEqual(mockResponse.data);
    });
  });

  describe('openModal1', () => {
    it('should open modal with correct configuration', () => {
      const template = {} as any;
      const expectedConfig = {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-md thanks-popup custom-modal',
      };

      component.openModal1(template);
      expect(modalService.show).toHaveBeenCalledWith(template, expectedConfig);
    });
  });

  describe('changeRequestCollapse', () => {
    beforeEach(() => {
      jest.spyOn(component, 'initializeSeriesOption').mockImplementation(() => {
        component.seriesOptions = [
          { option: 1, text: 'This event', disabled: false },
          { option: 2, text: 'This and all following events', disabled: false }
        ];
      });
    });

    it('should toggle allRequestIsOpened and initialize series options', () => {
      const futureDate = moment().add(1, 'day').format();
      const data = { craneDeliveryStart: futureDate };

      component.allRequestIsOpened = false;
      component.changeRequestCollapse(data);

      expect(component.initializeSeriesOption).toHaveBeenCalled();
      expect(component.allRequestIsOpened).toBe(true);
    });

    it('should disable series options for past dates', () => {
      const pastDate = moment().subtract(1, 'day').format();
      const data = { craneDeliveryStart: pastDate };

      component.changeRequestCollapse(data);

      expect(component.seriesOptions[1].disabled).toBe(true);
      expect(component.seriesOptions[0].disabled).toBe(false);
    });
  });

  describe('initializeSeriesOption', () => {
    it('should initialize series options correctly', () => {
      component.initializeSeriesOption();

      expect(component.seriesOptions).toEqual([
        { option: 1, text: 'This event', disabled: false },
        { option: 2, text: 'This and all following events', disabled: false }
      ]);
    });
  });

  describe('indexBasedItem', () => {
    beforeEach(() => {
      component.memberList = [
        { id: 1, name: 'Member 1' },
        { id: 2, name: 'Member 2' },
        { id: 3, name: 'Member 3' }
      ];
    });

    it('should return 0 when all items are found', () => {
      const testArray = [{ id: 1 }, { id: 2 }];
      const result = component.indexBasedItem(testArray);
      expect(result).toBe(0);
    });

    it('should return -1 when not all items are found', () => {
      const testArray = [{ id: 1 }, { id: 99 }];
      const result = component.indexBasedItem(testArray);
      expect(result).toBe(-1);
    });

    it('should return 0 for empty array', () => {
      const testArray = [];
      const result = component.indexBasedItem(testArray);
      expect(result).toBe(0);
    });
  });

  describe('showError', () => {
    it('should display error message from error object', () => {
      const error = {
        message: {
          details: [{ field: 'Test error message' }]
        }
      };

      component.showError(error);

      expect(toastrService.error).toHaveBeenCalledWith(['Test error message']);
    });
  });

  describe('openModal', () => {
    it('should open modal with correct configuration', () => {
      const template = {} as any;
      const expectedConfig = {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      };

      component.openModal(template);
      expect(modalService.show).toHaveBeenCalledWith(template, expectedConfig);
    });
  });

  describe('navigateToAttachment', () => {
    it('should set currentTabId to 1 and hide modal', () => {
      component.modalRef = { hide: jest.fn() } as any;

      component.navigateToAttachment();

      expect(component.currentTabId).toBe(1);
      expect(component.modalRef.hide).toHaveBeenCalled();
    });
  });

  describe('setStatus', () => {
    it('should set component properties from data and call getCraneRequest', () => {
      const mockData = {
        ParentCompanyId: 123,
        ProjectId: 456,
        id: 789
      };
      component.data = mockData;
      jest.spyOn(component, 'getCraneRequest').mockImplementation(() => {});

      component.setStatus();

      expect(component.ParentCompanyId).toBe(123);
      expect(component.ProjectId).toBe(456);
      expect(component.CraneRequestId).toBe(789);
      expect(deliveryService.updatedEditCraneRequestId).toHaveBeenCalledWith(789);
      expect(component.getCraneRequest).toHaveBeenCalled();
    });

    it('should not call getCraneRequest when CraneRequestId is -1', () => {
      const mockData = {
        ParentCompanyId: 123,
        ProjectId: 456,
        id: -1
      };
      component.data = mockData;
      jest.spyOn(component, 'getCraneRequest').mockImplementation(() => {});

      component.setStatus();

      expect(component.getCraneRequest).not.toHaveBeenCalled();
    });

    it('should not call getCraneRequest when CraneRequestId is undefined', () => {
      const mockData = {
        ParentCompanyId: 123,
        ProjectId: 456,
        id: undefined
      };
      component.data = mockData;
      jest.spyOn(component, 'getCraneRequest').mockImplementation(() => {});

      component.setStatus();

      expect(component.getCraneRequest).not.toHaveBeenCalled();
    });
  });

  describe('handleDownKeydown', () => {
    it('should handle Enter key for open action', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      const data = {} as any;
      jest.spyOn(component, 'openModal').mockImplementation(() => {});
      jest.spyOn(event, 'preventDefault');

      component.handleDownKeydown(event, data, null, 'open');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.openModal).toHaveBeenCalledWith(data);
    });

    it('should handle Space key for edit action', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      const data = {} as any;
      const item = {} as any;
      jest.spyOn(component, 'openEditModal').mockImplementation(() => {});
      jest.spyOn(event, 'preventDefault');

      component.handleDownKeydown(event, data, item, 'edit');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.openEditModal).toHaveBeenCalledWith(data, item);
    });

    it('should handle Enter key for save action', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(component, 'saveStatus').mockImplementation(() => {});
      jest.spyOn(event, 'preventDefault');

      component.handleDownKeydown(event, null, null, 'save');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.saveStatus).toHaveBeenCalled();
    });

    it('should not handle other keys', () => {
      const event = new KeyboardEvent('keydown', { key: 'Tab' });
      jest.spyOn(component, 'openModal').mockImplementation(() => {});
      jest.spyOn(event, 'preventDefault');

      component.handleDownKeydown(event, null, null, 'open');

      expect(event.preventDefault).not.toHaveBeenCalled();
      expect(component.openModal).not.toHaveBeenCalled();
    });

    it('should handle default case in switch statement', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');

      component.handleDownKeydown(event, null, null, 'unknown');

      expect(event.preventDefault).toHaveBeenCalled();
    });
  });

  describe('gatestatus', () => {
    beforeEach(() => {
      component.currentDeliverySaveItem = { id: 123 };
      component.currentStatus = 'Approved';
      component.ParentCompanyId = 789;
      component.authUser = { RoleId: 2, id: 1, UserId: 1 };
      component.modalRef = { hide: jest.fn(), content: {} } as any;
    });

    it('should update status when action is yes', () => {
      const mockResponse = { message: 'Success' };
      deliveryService.updateCraneRequestStatus.mockReturnValue(of(mockResponse));

      component.gatestatus('yes');

      expect(deliveryService.updateCraneRequestStatus).toHaveBeenCalledWith({
        id: 123,
        status: 'Approved',
        ParentCompanyId: 789
      });
      expect(toastrService.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
      expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Approved Crane Booking');
      expect(socket.emit).toHaveBeenCalledWith('CraneApproveHistory', mockResponse);
    });

    it('should handle error with statusCode 400', () => {
      const mockError = { message: { statusCode: 400, details: [{ field: 'Error message' }] } };
      deliveryService.updateCraneRequestStatus.mockReturnValue(throwError(mockError));
      jest.spyOn(component, 'showError').mockImplementation(() => {});

      component.gatestatus('yes');

      expect(component.showError).toHaveBeenCalledWith(mockError);
      expect(component.statusSubmitted).toBe(false);
      expect(component.statusChanged).toBe(false);
    });

    it('should handle error without message', () => {
      const mockError = {};
      deliveryService.updateCraneRequestStatus.mockReturnValue(throwError(mockError));

      component.gatestatus('yes');

      expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should handle error with message', () => {
      const mockError = { message: 'Custom error message' };
      deliveryService.updateCraneRequestStatus.mockReturnValue(throwError(mockError));

      component.gatestatus('yes');

      expect(toastrService.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should open edit modal when action is no', () => {
      component.gatestatus('no');

      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(bsModalRef.hide).toHaveBeenCalled();
      expect(modalService.show).toHaveBeenCalled();
    });
  });

  describe('saveStatus', () => {
    beforeEach(() => {
      component.currentDeliverySaveItem = {
        id: 123,
        equipmentDetails: [{ Equipment: { id: 1 } }],
        memberDetails: [{ Member: { id: 1 } }]
      };
      component.currentStatus = 'Approved';
      component.ParentCompanyId = 789;
      component.authUser = { RoleId: 2, id: 1, UserId: 1 };
      component.equipmentList = [{ id: 1, name: 'Equipment 1' }];
      component.memberList = [{ id: 1, name: 'Member 1' }];
      component.modalRef = { hide: jest.fn() } as any;
    });

    it('should show error when no status is chosen', () => {
      component.currentStatus = '';

      component.saveStatus();

      expect(toastrService.clear).toHaveBeenCalled();
      expect(toastrService.error).toHaveBeenCalledWith('No status chosen to save');
      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should update status successfully when equipment and member are found', () => {
      const mockResponse = { message: 'Success' };
      deliveryService.updateCraneRequestStatus.mockReturnValue(of(mockResponse));

      component.saveStatus();

      expect(deliveryService.updateCraneRequestStatus).toHaveBeenCalledWith({
        id: 123,
        status: 'Approved',
        ParentCompanyId: 789
      });
      expect(toastrService.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
    });

    it('should open equipment modal when equipment not found', () => {
      component.equipmentList = [{ id: 999, name: 'Other Equipment' }];
      jest.spyOn(component, 'openModal1').mockImplementation(() => {});

      component.saveStatus();

      expect(component.openModal1).toHaveBeenCalled();
    });

    it('should open person modal when member not found', () => {
      component.memberList = [{ id: 999, name: 'Other Member' }];
      jest.spyOn(component, 'openModal1').mockImplementation(() => {});

      component.saveStatus();

      expect(component.openModal1).toHaveBeenCalled();
    });

    it('should open overall modal when both equipment and member not found', () => {
      component.equipmentList = [{ id: 999, name: 'Other Equipment' }];
      component.memberList = [{ id: 999, name: 'Other Member' }];
      jest.spyOn(component, 'openModal1').mockImplementation(() => {});

      component.saveStatus();

      expect(component.openModal1).toHaveBeenCalled();
      expect(component.textvalue).toBe('equipment,member');
    });

    it('should not proceed when already submitted', () => {
      component.statusSubmitted = true;

      component.saveStatus();

      expect(deliveryService.updateCraneRequestStatus).not.toHaveBeenCalled();
    });

    it('should hide modal when statusValue length is 1', () => {
      component.statusValue = ['Completed'];
      const mockResponse = { message: 'Success' };
      deliveryService.updateCraneRequestStatus.mockReturnValue(of(mockResponse));

      component.saveStatus();

      expect(component.modalRef.hide).toHaveBeenCalled();
    });
  });

  describe('addToVoid', () => {
    beforeEach(() => {
      component.currentDeliverySaveItem = { id: 123 };
      component.ProjectId = 456;
      component.ParentCompanyId = 789;
    });

    it('should add to void successfully', () => {
      const mockResponse = { message: 'Voided successfully' };
      deliveryService.addCraneRequestToVoid.mockReturnValue(of(mockResponse));

      component.addToVoid();

      expect(projectService.updateAccountProjectParent).toHaveBeenCalledWith({
        ProjectId: 456,
        ParentCompanyId: 789
      });
      expect(deliveryService.addCraneRequestToVoid).toHaveBeenCalledWith({
        CraneRequestId: 123,
        ProjectId: 456,
        ParentCompanyId: 789
      });
      expect(toastrService.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
      expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Crane Booking Voided');
    });

    it('should handle error with statusCode 400', () => {
      const mockError = { message: { statusCode: 400, details: [{ field: 'Error message' }] } };
      deliveryService.addCraneRequestToVoid.mockReturnValue(throwError(mockError));
      jest.spyOn(component, 'showError').mockImplementation(() => {});

      component.addToVoid();

      expect(component.showError).toHaveBeenCalledWith(mockError);
      expect(component.voidSubmitted).toBe(false);
    });

    it('should handle error without message', () => {
      const mockError = {};
      deliveryService.addCraneRequestToVoid.mockReturnValue(throwError(mockError));

      component.addToVoid();

      expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should not proceed when already submitted', () => {
      component.voidSubmitted = true;

      component.addToVoid();

      expect(deliveryService.addCraneRequestToVoid).not.toHaveBeenCalled();
    });
  });

  describe('getCraneRequest', () => {
    beforeEach(() => {
      component.CraneRequestId = 123;
      component.ProjectId = 456;
      component.ParentCompanyId = 789;
      component.authUser = { RoleId: 2, id: 1, UserId: 1 };
    });

    it('should fetch crane request and set properties for role 2', () => {
      const mockResponse = {
        data: {
          id: 123,
          status: 'Pending',
          CraneRequestId: 123,
          memberDetails: [{ Member: { id: 1 } }],
          equipmentDetails: [{ Equipment: { id: 1 } }],
          createdUserDetails: { RoleId: 4, User: { id: 1 } },
          voidList: [],
          recurrence: null
        }
      };
      deliveryService.getEquipmentCraneRequest.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'checkRoleBasedItem').mockImplementation(() => {});

      component.getCraneRequest();

      expect(deliveryService.getEquipmentCraneRequest).toHaveBeenCalledWith({
        CraneRequestId: 123,
        ParentCompanyId: 789,
        ProjectId: 456
      });
      expect(component.currentDeliverySaveItem).toEqual(mockResponse.data);
      expect(component.showStatus).toBe(true);
      expect(component.show).toBe(true);
      expect(component.void).toBe(false);
    });

    it('should handle role 4 user with matching member', () => {
      component.authUser = { RoleId: 4, id: 1, UserId: 1 };
      const mockResponse = {
        data: {
          id: 123,
          status: 'Pending',
          memberDetails: [{ Member: { id: 1 } }],
          equipmentDetails: [{ Equipment: { id: 1 } }],
          createdUserDetails: { RoleId: 4, User: { id: 1 } },
          voidList: []
        }
      };
      deliveryService.getEquipmentCraneRequest.mockReturnValue(of(mockResponse));

      component.getCraneRequest();

      expect(component.currentDeliverySaveItem.edit).toBe(true);
    });

    it('should handle role 4 user without matching member', () => {
      component.authUser = { RoleId: 4, id: 999, UserId: 999 };
      const mockResponse = {
        data: {
          id: 123,
          status: 'Pending',
          memberDetails: [{ Member: { id: 1 } }],
          equipmentDetails: [{ Equipment: { id: 1 } }],
          createdUserDetails: { RoleId: 4, User: { id: 1 } },
          voidList: []
        }
      };
      deliveryService.getEquipmentCraneRequest.mockReturnValue(of(mockResponse));

      component.getCraneRequest();

      expect(component.currentDeliverySaveItem.edit).toBe(false);
    });

    it('should handle void list with matching user', () => {
      const mockResponse = {
        data: {
          id: 123,
          status: 'Pending',
          memberDetails: [{ Member: { id: 1 } }],
          equipmentDetails: [{ Equipment: { id: 1 } }],
          createdUserDetails: { RoleId: 4, User: { id: 1 } },
          voidList: [{ MemberId: 1 }]
        }
      };
      deliveryService.getEquipmentCraneRequest.mockReturnValue(of(mockResponse));

      component.getCraneRequest();

      expect(component.void).toBe(true);
    });

    it('should handle role 3 with approved status', () => {
      component.authUser = { RoleId: 3, id: 1, UserId: 1 };
      const mockResponse = {
        data: {
          id: 123,
          status: 'Approved',
          memberDetails: [{ Member: { id: 1 } }],
          equipmentDetails: [{ Equipment: { id: 1 } }],
          createdUserDetails: { RoleId: 4, User: { id: 1 } },
          voidList: []
        }
      };
      deliveryService.getEquipmentCraneRequest.mockReturnValue(of(mockResponse));

      component.getCraneRequest();

      expect(component.showStatus).toBe(true);
    });
  });

  describe('checkRoleBasedItem', () => {
    beforeEach(() => {
      component.authUser = { RoleId: 2, id: 1, UserId: 1 };
    });

    it('should set showStatus for role 4 created item with approved status', () => {
      const item = {
        createdUserDetails: { RoleId: 4, User: { id: 1 } },
        status: 'Approved'
      };

      component.checkRoleBasedItem(item);

      expect(component.showStatus).toBe(true);
      expect(component.statusValue).toEqual(['Completed']);
    });

    it('should set showStatus for role 4 created item with expired status', () => {
      const item = {
        createdUserDetails: { RoleId: 4, User: { id: 1 } },
        status: 'Expired'
      };

      component.checkRoleBasedItem(item);

      expect(component.showStatus).toBe(true);
      expect(component.statusValue).toEqual(['Completed']);
    });

    it('should set showStatus for role 2/3 created item with approved status', () => {
      const item = {
        createdUserDetails: { RoleId: 2, User: { id: 1 } },
        status: 'Approved'
      };

      component.checkRoleBasedItem(item);

      expect(component.showStatus).toBe(true);
      expect(component.statusValue).toEqual(['Completed']);
    });

    it('should not set showStatus for role 4 user when not creator', () => {
      component.authUser = { RoleId: 4, id: 999, UserId: 999 };
      component.showStatus = false; // Set initial state
      const item = {
        createdUserDetails: { RoleId: 4, User: { id: 1 } },
        status: 'Approved'
      };

      component.checkRoleBasedItem(item);

      expect(component.showStatus).toBe(false);
    });
  });

  describe('openEditModal', () => {
    beforeEach(() => {
      component.ProjectId = 456;
      component.ParentCompanyId = 789;
    });

    it('should open edit modal for item without recurrence', () => {
      const item = {
        CraneRequestId: 123,
        recurrence: null
      };

      component.openEditModal(item, null);

      expect(bsModalRef.hide).toHaveBeenCalled();
      expect(deliveryService.updatedEditCraneRequestId).toHaveBeenCalledWith(123);
      expect(projectService.updateAccountProjectParent).toHaveBeenCalledWith({
        ProjectId: 456,
        ParentCompanyId: 789
      });
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should open edit modal for item with recurrence', () => {
      const item = {
        CraneRequestId: 123,
        recurrence: {
          id: 456,
          recurrence: 'Daily',
          recurrenceEndDate: '2024-12-31'
        }
      };

      component.openEditModal(item, 2);

      expect(modalService.show).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          initialState: {
            seriesoption: 2,
            recurrenceId: 456,
            recurrenceEndDate: '2024-12-31'
          }
        })
      );
    });

    it('should handle item with "Does Not Repeat" recurrence', () => {
      const item = {
        CraneRequestId: 123,
        recurrence: {
          id: 456,
          recurrence: 'Does Not Repeat',
          recurrenceEndDate: '2024-12-31'
        }
      };

      component.openEditModal(item, 2);

      expect(modalService.show).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          initialState: {
            seriesoption: 1,
            recurrenceId: 456,
            recurrenceEndDate: '2024-12-31'
          }
        })
      );
    });
  });

  describe('ngOnInit', () => {
    it('should set up socket listeners and router subscription', () => {
      jest.spyOn(component, 'setStatus').mockImplementation(() => {});

      component.ngOnInit();

      expect(component.setStatus).toHaveBeenCalled();
      expect(component.currentTabId).toBe(0);
      expect(socket.on).toHaveBeenCalledWith('getCraneCommentHistory', expect.any(Function));
      expect(socket.on).toHaveBeenCalledWith('getCraneAttachmentDeleteHistory', expect.any(Function));
      expect(socket.on).toHaveBeenCalledWith('getCraneApproveHistory', expect.any(Function));
    });

    it('should handle socket events correctly', () => {
      jest.spyOn(component, 'setStatus').mockImplementation(() => {});

      // Mock socket.on to call the callback immediately
      socket.on.mockImplementation((event, callback) => {
        if (event === 'getCraneCommentHistory') {
          callback({ message: 'Crane Booking Comment added successfully.' });
        } else if (event === 'getCraneAttachmentDeleteHistory') {
          callback({ message: 'Crane Booking Attachment Deleted Successfully.' });
        } else if (event === 'getCraneApproveHistory') {
          callback({ message: 'Uploaded Successfully.' });
        }
      });

      component.ngOnInit();

      expect(component.currentTabId).toBe(1); // Last event sets it to 1
    });
  });

  describe('confirmationClose', () => {
    it('should open confirmation modal when status changed and current status exists', () => {
      component.statusChanged = true;
      component.currentStatus = 'Approved';
      const template = {} as any;
      jest.spyOn(component, 'openConfirmationModalPopupForDetailsNDR').mockImplementation(() => {});

      component.confirmationClose(template);

      expect(component.openConfirmationModalPopupForDetailsNDR).toHaveBeenCalledWith(template);
    });

    it('should reset form when no status changes', () => {
      component.statusChanged = false;
      jest.spyOn(component, 'resetForm').mockImplementation(() => {});

      component.confirmationClose({} as any);

      expect(component.resetForm).toHaveBeenCalledWith('yes');
    });
  });

  describe('openConfirmationModalPopupForDetailsNDR', () => {
    it('should open confirmation modal with correct configuration', () => {
      const template = {} as any;
      const expectedConfig = {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      };

      component.openConfirmationModalPopupForDetailsNDR(template);

      expect(modalService.show).toHaveBeenCalledWith(template, expectedConfig);
    });
  });

  describe('resetForm', () => {
    beforeEach(() => {
      component.modalRef1 = { hide: jest.fn() } as any;
    });

    it('should hide modal when action is no', () => {
      component.resetForm('no');

      expect(component.modalRef1.hide).toHaveBeenCalled();
    });

    it('should hide modals and reset status when action is yes', () => {
      component.statusChanged = true;

      component.resetForm('yes');

      expect(component.modalRef1.hide).toHaveBeenCalled();
      expect(bsModalRef.hide).toHaveBeenCalled();
      expect(component.statusChanged).toBe(false);
    });

    it('should handle when modalRef1 is null', () => {
      component.modalRef1 = null;

      expect(() => component.resetForm('yes')).not.toThrow();
      expect(bsModalRef.hide).toHaveBeenCalled();
    });
  });

  describe('voidConfirmationResponse', () => {
    beforeEach(() => {
      component.modalRef = { hide: jest.fn() } as any;
    });

    it('should hide modal when action is no', () => {
      component.voidConfirmationResponse('no');

      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should call addToVoid when action is yes', () => {
      jest.spyOn(component, 'addToVoid').mockImplementation(() => {});

      component.voidConfirmationResponse('yes');

      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(component.addToVoid).toHaveBeenCalled();
    });

    it('should handle when modalRef is null', () => {
      component.modalRef = null;
      jest.spyOn(component, 'addToVoid').mockImplementation(() => {});

      expect(() => component.voidConfirmationResponse('yes')).not.toThrow();
      expect(component.addToVoid).toHaveBeenCalled();
    });
  });

  describe('revertstatus', () => {
    it('should open modal for role 1 user', () => {
      component.authUser = { RoleId: 1, id: 1, UserId: 1 };
      const template = {} as any;
      const expectedConfig = {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      };

      component.revertstatus(template);

      expect(modalService.show).toHaveBeenCalledWith(template, expectedConfig);
    });

    it('should open modal for role 2 user', () => {
      component.authUser = { RoleId: 2, id: 1, UserId: 1 };
      const template = {} as any;

      component.revertstatus(template);

      expect(modalService.show).toHaveBeenCalled();
    });

    it('should not open modal for other roles', () => {
      component.authUser = { RoleId: 3, id: 1, UserId: 1 };
      const template = {} as any;
      modalService.show.mockClear();

      component.revertstatus(template);

      expect(modalService.show).not.toHaveBeenCalled();
    });
  });

  describe('statusupdate', () => {
    beforeEach(() => {
      component.currentDeliverySaveItem = { id: 123 };
      component.ParentCompanyId = 789;
      component.modalRef2 = { hide: jest.fn() } as any;
    });

    it('should hide modal when action is no', () => {
      component.statusupdate('no');

      expect(component.modalRef2.hide).toHaveBeenCalled();
    });

    it('should update status to Approved when action is yes', () => {
      const mockResponse = { message: 'Status updated' };
      deliveryService.updateCraneRequestStatus.mockReturnValue(of(mockResponse));

      component.statusupdate('yes');

      expect(component.modalRef2.hide).toHaveBeenCalled();
      expect(deliveryService.updateCraneRequestStatus).toHaveBeenCalledWith({
        id: 123,
        status: 'Approved',
        ParentCompanyId: 789,
        statuschange: 'Reverted'
      });
      expect(toastrService.success).toHaveBeenCalledWith('Status Reverted Successfully', 'Success');
      expect(socket.emit).toHaveBeenCalledWith('CraneApproveHistory', mockResponse);
    });

    it('should handle when modalRef2 is null', () => {
      component.modalRef2 = null;
      const mockResponse = { message: 'Status updated' };
      deliveryService.updateCraneRequestStatus.mockReturnValue(of(mockResponse));

      expect(() => component.statusupdate('yes')).not.toThrow();
      expect(deliveryService.updateCraneRequestStatus).toHaveBeenCalled();
    });
  });

  describe('Edge Cases and Error Scenarios', () => {
    it('should handle undefined/null values in constructor subscriptions', () => {
      deliveryService.loginUser.next(undefined);
      deliveryService.loginUser.next(null);
      deliveryService.loginUser.next('');

      projectService.projectParent.next(undefined);
      projectService.projectParent.next(null);
      projectService.projectParent.next('');

      deliveryService.getCurrentCraneRequestStatus.next(undefined);
      deliveryService.getCurrentCraneRequestStatus.next(null);
      deliveryService.getCurrentCraneRequestStatus.next('');

      deliveryService.fetchData.next(undefined);
      deliveryService.fetchData.next(null);
      deliveryService.fetchData.next('');

      deliveryService.fetchData1.next(undefined);
      deliveryService.fetchData1.next(null);
      deliveryService.fetchData1.next('');

      projectService.isMyAccount.next(undefined);
      projectService.isMyAccount.next(null);
      projectService.isMyAccount.next('');

      projectService.isProject.next(undefined);
      projectService.isProject.next(null);
      projectService.isProject.next('');

      // Should not throw errors
      expect(component).toBeTruthy();
    });

    it('should handle missing equipment details in saveStatus', () => {
      component.currentDeliverySaveItem = {
        id: 123,
        equipmentDetails: [],
        memberDetails: [{ Member: { id: 1 } }]
      };
      component.currentStatus = 'Approved';
      component.equipmentList = [];
      jest.spyOn(component, 'openModal1').mockImplementation(() => {});

      component.saveStatus();

      expect(component.openModal1).toHaveBeenCalled();
    });

    it('should handle missing member details in saveStatus', () => {
      component.currentDeliverySaveItem = {
        id: 123,
        equipmentDetails: [{ Equipment: { id: 1 } }],
        memberDetails: [{ Member: { id: 999 } }] // Member not in memberList
      };
      component.currentStatus = 'Approved';
      component.equipmentList = [{ id: 1, name: 'Equipment 1' }]; // Equipment found
      component.memberList = [{ id: 1, name: 'Member 1' }]; // Different member
      jest.spyOn(component, 'openModal1').mockImplementation(() => {});

      component.saveStatus();

      expect(component.openModal1).toHaveBeenCalled();
    });
  });

  describe('ngOnDestroy', () => {
    it('should unsubscribe from all subscriptions', () => {
      const unsubscribeSpy = jest.spyOn(component['subscriptions'], 'unsubscribe');
      component.ngOnDestroy();
      expect(unsubscribeSpy).toHaveBeenCalled();
    });
  });
});
