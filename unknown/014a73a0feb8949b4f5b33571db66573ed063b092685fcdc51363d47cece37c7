<div class="modal-header border-0 pb-0">
  <h3 class="fs14 fw-bold cairo-regular color-text7 my-0">Filter</h3>
  <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef.hide()">
    <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close" /></span>
  </button>
</div>
<div class="modal-body">
  <div class="filter-content">
    <form
      class="custom-material-form"
      id="filter-form3"
      [formGroup]="filterForm"
      (ngSubmit)="filterSubmit()"
      novalidate
    >
      <div class="row">
        <div class="col-md-12">
          <div class="input-group mb-3">
            <input
              type="text"
              class="form-control fs12 material-input"
              placeholder="Desciription"
              formControlName="descriptionFilter"
            />
              <span class="input-group-text">
                <img src="./assets/images/search-icon.svg" alt="Search" />
              </span>
          </div>
          <div class="form-group">
            <select
              class="form-control fs12 material-input"
              id="companyFilter3"
              formControlName="companyFilter"
            >
              <option value="" disabled selected hidden>Company</option>
              <option *ngFor="let item of companyList" value="{{ item.id }}">
                {{ item.companyName }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <select class="form-control fs12 material-input" formControlName="memberFilter">
              <option value="" disabled selected hidden>*Responsible Person</option>
              <ng-container *ngFor="let item of memberList">
              <option  *ngIf="item.status === 'pending' && !item.isGuestUser" [value]="item.id">
                {{ item.User?.email }}
              </option>
              <option  *ngIf="item.status === 'pending' && item.isGuestUser" [value]="item.id">
                {{ item.User?.email }}(Guest)
              </option>
            </ng-container>
            </select>
          </div>
          <div class="form-group">
            <select
              class="form-control fs12 material-input"
              id="gateFilter"
              formControlName="gateFilter"
            >
              <option value="" disabled selected hidden>Gate</option>
              <option *ngFor="let item of gateList" value="{{ item.id }}">
                {{ item.gateName }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <select
              class="form-control fs12 material-input"
              id="equipmentFilter"
              formControlName="equipmentFilter"
            >
              <option value="" disabled selected hidden>Equipment</option>
              <option *ngFor="let item of equipmentList" value="{{ item.id }}">
                {{ item.equipmentName }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <select
              class="form-control fs12 material-input"
              id="statusFilter"
              formControlName="statusFilter"
            >
              <option value="" disabled selected hidden>Status</option>
              <option *ngFor="let item of wholeStatus" value="{{ item }}">{{ item }}</option>
            </select>
          </div>
          <div class="row justify-content-end">
            <button
              class="btn btn-orange radius20 col-4 mt-2 fs12 fw-bold cairo-regular btn-block mx-1"
              type="submit"
            >
              Apply
            </button>
            <button
              class="btn btn-orange radius20 fs12 col-4 mt-2 fw-bold cairo-regular btn-block mx-1"
              type="button"
              (click)="resetFilter()"
            >
              Reset
            </button>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
