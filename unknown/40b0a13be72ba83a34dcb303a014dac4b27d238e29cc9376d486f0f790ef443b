/* eslint-disable max-lines-per-function */
import {
  Component, Input, OnD<PERSON>roy, OnInit, TemplateRef, ViewChild,
} from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { Socket } from 'ngx-socket-io';
import moment from 'moment';
import { Subscription } from 'rxjs';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';
import { EditCraneRequestComponent } from '../edit-crane-request/edit-crane-request.component';
import { MixpanelService } from '../../services/mixpanel.service';

@Component({
  selector: 'app-crane-request-detail-view-header',
  templateUrl: './crane-request-detail-view-header.component.html',
})
export class CraneRequestDetailViewHeaderComponent implements OnInit, OnDestroy {
  @Input() data: any;

  @Input() title: string;

  public showStatus = false;

  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public modalRef2: BsModalRef;

  public modalRef3: BsModalRef;

  public CraneRequestId: number;

  public currentDeliverySaveItem: any = {};

  public currentStatus = '';

  public statusSubmitted = false;

  public voidSubmitted = false;

  public ProjectId: any;

  public void = false;

  public authUser: any = {};

  public statusValue: any = [];

  public ParentCompanyId: any;

  public myAccount = false;

  public accountAdmin = false;

  public show = true;

  public currentTabId = 0;

  public statusChanged = false;

  public memberList: any = [];

  public equipmentList: any = [];

  public gatesubmit = false;

  public textvalue: any;

  public allRequestIsOpened = false;

  @ViewChild('GateConfirmationPopup') public gateModal: TemplateRef<any>;

  @ViewChild('equipmentConfirmationPopup') public equipmentModal: TemplateRef<any>;

  @ViewChild('personConfirmationPopup') public personModal: TemplateRef<any>;

  @ViewChild('overallConfirmationPopup') public overallmodal: TemplateRef<any>;

  public seriesOptions = [];

  private readonly subscriptions: Subscription = new Subscription();


  public constructor(
    private readonly modalService: BsModalService,
    public bsModalRef: BsModalRef,
    public deliveryService: DeliveryService,
    public socket: Socket,
    private readonly mixpanelService: MixpanelService,
    public router: Router,
    public toastr: ToastrService,
    public projectService: ProjectService,
  ) {
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
        if (this.authUser.RoleId === 2 || this.authUser.RoleId === 1) {
          this.statusValue = ['Approved', 'Declined'];
        } else if (this.authUser.RoleId === 3) {
          this.statusValue = ['Completed'];
        }
      }
    });
    this.projectService.projectParent.subscribe((projectResponse20): void => {
      if (
        projectResponse20 !== undefined
        && projectResponse20 !== null
        && projectResponse20 !== ''
      ) {
        this.ProjectId = projectResponse20.ProjectId;
        this.ParentCompanyId = projectResponse20.ParentCompanyId;
      }
    });
    const sub1 = this.deliveryService.getCurrentCraneRequestStatus.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.CraneRequestId = res;
        this.getCraneRequest();
      }
    });

    const sub2 = this.deliveryService.fetchData.subscribe((getNdrResponse3): void => {
      this.currentTabId = 0;
      if (getNdrResponse3 !== undefined && getNdrResponse3 !== null && getNdrResponse3 !== '') {
        this.getCraneRequest();
      }
    });

    const sub3 = this.deliveryService.fetchData1.subscribe((getNdrResponse3): void => {
      this.currentTabId = 0;
      if (getNdrResponse3 !== undefined && getNdrResponse3 !== null && getNdrResponse3 !== '') {
        this.getCraneRequest();
      }
    });

    this.subscriptions.add(sub1);
    this.subscriptions.add(sub2);
    this.subscriptions.add(sub3);
    this.projectService.isMyAccount.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.myAccount = true;
      }
    });
    this.projectService.isProject.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.myAccount = false;
        this.accountAdmin = false;
      }
    });
    this.getOverAllEquipmentforEditNdr();
    this.getMembers();
  }

  public changeRequestCollapse(data): void {
    this.initializeSeriesOption();
    if (!moment(data.craneDeliveryStart).isAfter(moment())) {
      this.seriesOptions = this.seriesOptions.filter((object): any => {
        const seriesObject = object;
        if (seriesObject.option !== 1) {
          seriesObject.disabled = true;
        }
        return seriesObject;
      });
    }
    this.allRequestIsOpened = !this.allRequestIsOpened;
  }

  public selectStatus(status: string): void {
    this.currentStatus = status;
    this.statusChanged = true;
  }

  public eventCheck(event: { target: { checked: any } }): void {
    if (event.target.checked) {
      this.currentStatus = 'Completed';
      this.statusChanged = true;
    } else {
      this.currentStatus = '';
    }
  }

  public clickAndDisable(link: any): void {
    // disable subsequent clicks
    const clickDisable = link;
    clickDisable.onclick = (event: { preventDefault: () => void }): void => {
      event.preventDefault();
    };
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }

  public getOverAllEquipmentforEditNdr(): void {
    const editNdrGetEquipmentParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listEquipment(editNdrGetEquipmentParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((editNdrEquipmentResponse): void => {
        this.equipmentList = editNdrEquipmentResponse.data;
      });
  }

  public openModal1(template: TemplateRef<any>): void {
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-md thanks-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }

  public gatestatus(action: string): void {
    this.gatesubmit = false;
    if (action === 'yes') {
      this.gatesubmit = true;
      const data = {
        id: this.currentDeliverySaveItem.id,
        status: this.currentStatus,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.deliveryService.updateCraneRequestStatus(data).subscribe({
        next: (updateStatusResponse2: any): void => {
          if (updateStatusResponse2) {
            this.gatesubmit = false;
            this.modalRef.hide();
            this.toastr.success(updateStatusResponse2.message, 'Success');
            this.mixpanelService.addMixpanelEvents(`${this.currentStatus} Crane Booking`);
            this.socket.emit('CraneApproveHistory', updateStatusResponse2);
            this.deliveryService.updateCraneRequestHistory({ status: true }, 'CraneApproveHistory');
            this.statusSubmitted = false;
            const updateStatusResponse2Condition = data.status !== 'Expired'
              && data.status !== 'Completed'
              && data.status !== 'Declined';
            if (
              (this.authUser.RoleId === 2 && updateStatusResponse2Condition)
              || (this.authUser.RoleId === 3 && data.status === 'Approved')
            ) {
              this.showStatus = true;
            } else {
              this.showStatus = false;
            }
            this.currentDeliverySaveItem.status = this.currentStatus;
            this.statusChanged = false;
          }
        },
        error: (newNDRCreateErr): void => {
          this.statusSubmitted = false;
          this.statusChanged = false;
          if (newNDRCreateErr.message?.statusCode === 400) {
            this.showError(newNDRCreateErr);
          } else if (!newNDRCreateErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(newNDRCreateErr.message, 'OOPS!');
            this.modalRef.hide();
          }
        },
      });
    } else {
      if (this.modalRef) {
        this.modalRef.hide();
      }
      this.bsModalRef.hide();
      const className = 'modal-lg new-delivery-popup custom-modal';
      this.modalRef3 = this.modalService.show(EditCraneRequestComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
      });
      this.modalRef3.content.closeBtnName = 'Close';
      this.modalRef.content.seriesOption = 1;
      this.modalRef.content.recurrenceId = null;
      this.modalRef.content.recurrenceEndDate = null;
    }
  }

  public indexBasedItem(arrytest) {
    const arr1 = this.memberList;
    const arr2 = arrytest;
    let index2: number;
    const result = arr1.filter((o) => arr2.some(({ id }) => o.id === id));
    if (arrytest.length !== result.length) {
      index2 = -1;
    } else {
      index2 = 0;
    }
    return index2;
  }

  public saveStatus(): void {
    if (!this.currentStatus) {
      this.toastr.clear();
      this.toastr.error('No status chosen to save');
      this.modalRef.hide();
      return;
    }
    if (!this.statusSubmitted) {
      this.statusSubmitted = true;
      if (this.statusValue.length === 1) {
        if (this.modalRef) {
          this.modalRef.hide();
        }
      }
      const data = {
        id: this.currentDeliverySaveItem.id,
        status: this.currentStatus,
        ParentCompanyId: this.ParentCompanyId,
      };
      const equipmentid = this.currentDeliverySaveItem.equipmentDetails[0]?.Equipment?.id;
      const index1 = this.equipmentList.findIndex((i): boolean => i.id === equipmentid);
      const arrytest = this.currentDeliverySaveItem.memberDetails.map((detail) => detail.Member);

      const index2 = this.indexBasedItem(arrytest);
      if (index1 === -1 && index2 === -1) {
        this.openModal1(this.overallmodal);
        this.textvalue = 'equipment,member';
      } else if (index1 === -1) {
        this.openModal1(this.equipmentModal);
      } else if (index2 === -1) {
        this.openModal1(this.personModal);
      } else {
        this.deliveryService.updateCraneRequestStatus(data).subscribe({
          next: (updateStatusResponse2: any): void => {
            if (updateStatusResponse2) {
              this.toastr.success(updateStatusResponse2.message, 'Success');
              this.mixpanelService.addMixpanelEvents(`${this.currentStatus} Crane Booking`);
              this.socket.emit('CraneApproveHistory', updateStatusResponse2);
              this.deliveryService.updateCraneRequestHistory(
                { status: true },
                'CraneApproveHistory',
              );
              this.statusSubmitted = false;
              const updateStatusResponse2Condition = data.status !== 'Expired'
                && data.status !== 'Completed'
                && data.status !== 'Declined';
              if (
                (this.authUser.RoleId === 2 && updateStatusResponse2Condition)
                || (this.authUser.RoleId === 3 && data.status === 'Approved')
              ) {
                this.showStatus = true;
              } else {
                this.showStatus = false;
              }
              this.currentDeliverySaveItem.status = this.currentStatus;
              this.statusChanged = false;
            }
          },
          error: (newNDRCreateErr): void => {
            this.statusSubmitted = false;
            this.statusChanged = false;
            if (newNDRCreateErr.message?.statusCode === 400) {
              this.showError(newNDRCreateErr);
            } else if (!newNDRCreateErr.message) {
              this.toastr.error('Try again later.!', 'Something went wrong.');
            } else {
              this.toastr.error(newNDRCreateErr.message, 'OOPS!');
              this.modalRef.hide();
            }
          },
        });
      }
    }
  }

  public ngOnInit(): void {
    this.setStatus();
    this.currentTabId = 0;
    this.router.events.subscribe((e): void => {
      this.bsModalRef.hide();
    });
    this.socket.on('getCraneCommentHistory', (commenthistoryresponse: any): void => {
      if (commenthistoryresponse.message === 'Crane Booking Comment added successfully.') {
        this.currentTabId = 2;
      }
    });
    this.socket.on(
      'getCraneAttachmentDeleteHistory',
      (attachmentdeletehistoryresponse: any): void => {
        if (
          attachmentdeletehistoryresponse.message
          === 'Crane Booking Attachment Deleted Successfully.'
        ) {
          this.currentTabId = 1;
        }
      },
    );
    this.socket.on('getCraneApproveHistory', (NDRapprovehistoryresponse: any): void => {
      if (NDRapprovehistoryresponse.message === 'Uploaded Successfully.') {
        this.currentTabId = 1;
      }
    });
  }

  public navigateToAttachment(): void {
    this.currentTabId = 1;
    this.modalRef.hide();
  }

  public setStatus(): void {
    const deliveryData = this.data;
    this.ParentCompanyId = deliveryData.ParentCompanyId;
    this.ProjectId = deliveryData.ProjectId;
    this.CraneRequestId = deliveryData.id;
    if (this.CraneRequestId !== -1 && this.CraneRequestId !== undefined) {
      this.deliveryService.updatedEditCraneRequestId(this.CraneRequestId);
      this.getCraneRequest();
    }
  }

  public getCraneRequest(): void {
    this.projectService.projectParent.subscribe((getCurrentProjectResponse20): void => {
      if (
        getCurrentProjectResponse20 !== undefined
        && getCurrentProjectResponse20 !== null
        && getCurrentProjectResponse20 !== ''
      ) {
        this.ProjectId = getCurrentProjectResponse20.ProjectId;
        this.ParentCompanyId = getCurrentProjectResponse20.ParentCompanyId;
      }
    });
    const param = {
      CraneRequestId: this.CraneRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    this.currentTabId = 0;
    this.void = false;
    this.show = false;
    this.deliveryService.getEquipmentCraneRequest(param).subscribe((res): void => {
      this.currentDeliverySaveItem = res.data;
      this.currentDeliverySaveItem.edit = false;
      if (this.authUser.RoleId === 4 || this.authUser.RoleId === 3) {
        const newMember = res.data.memberDetails;
        const index = newMember.findIndex(
          (i: { Member: { id: any } }): boolean => i.Member.id === this.authUser.id,
        );
        if (index !== -1) {
          this.currentDeliverySaveItem.edit = true;
        } else {
          this.currentDeliverySaveItem.edit = false;
        }
      } else {
        this.currentDeliverySaveItem.edit = true;
      }
      const item = this.currentDeliverySaveItem;
      const authId = this.authUser.id;
      const voidData = this.currentDeliverySaveItem.voidList;
      if (voidData) {
        const voidIndex = voidData?.findIndex(
          (i: { MemberId: any }): boolean => i.MemberId === authId,
        );
        if (voidIndex !== -1) {
          this.void = true;
        }
      }
      this.show = true;
      const condition2 = this.authUser.RoleId === 3 && item.status === 'Approved';
      const condition1 = item.status === 'Pending' && (this.authUser.RoleId === 2 || this.authUser.RoleId === 1);
      if (condition1 || condition2) {
        this.showStatus = true;
      }
      this.checkRoleBasedItem(item);
    });
  }

  public checkRoleBasedItem(item) {
    if (
      item.createdUserDetails.RoleId === 4
      && (item.status === 'Approved' || item.status === 'Expired')
    ) {
      const loggedInuserId = this.authUser.UserId;
      const NDRCreatedUserId = item.createdUserDetails.User.id;
      if (
        this.authUser.RoleId === 2
        || this.authUser.RoleId === 3
        || +loggedInuserId === +NDRCreatedUserId
      ) {
        this.showStatus = true;
        this.statusValue = ['Completed'];
      }
    } else if (
      (item.createdUserDetails.RoleId === 2 || item.createdUserDetails.RoleId === 3)
      && (item.status === 'Approved' || item.status === 'Expired')
    ) {
      if (this.authUser.RoleId === 2 || this.authUser.RoleId === 3) {
        this.showStatus = true;
        this.statusValue = ['Completed'];
      }
    }
  }

  public openEditModal(item, action): void {
    if (this.bsModalRef) {
      this.bsModalRef.hide();
    }
    this.deliveryService.updatedEditCraneRequestId(+item.CraneRequestId);
    if (this.ProjectId) {
      const data = { ProjectId: this.ProjectId, ParentCompanyId: this.ParentCompanyId };
      this.projectService.updateAccountProjectParent(data);
    }
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(EditCraneRequestComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
      initialState: {
        seriesoption: item?.recurrence && item?.recurrence?.recurrence !== 'Does Not Repeat' ? action : 1,
        recurrenceId: item?.recurrence?.id || null,
        recurrenceEndDate: item?.recurrence?.recurrenceEndDate || null,
      },
    });
    this.modalRef.content.closeBtnName = 'Close';
    this.modalRef.content.seriesOption = item?.recurrence && item?.recurrence?.recurrence !== 'Does Not Repeat' ? action : 1;
    this.modalRef.content.recurrenceId = item?.recurrence ? item?.recurrence?.id : null;
    this.modalRef.content.recurrenceEndDate = item?.recurrence
      ? item?.recurrence?.recurrenceEndDate
      : null;
  }

  public addToVoid(): void {
    const newData = { ProjectId: this.ProjectId, ParentCompanyId: this.ParentCompanyId };
    this.projectService.updateAccountProjectParent(newData);
    if (!this.voidSubmitted) {
      this.voidSubmitted = true;
      const currentDeliveryItem = this.currentDeliverySaveItem;
      const data = {
        CraneRequestId: currentDeliveryItem.id,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.deliveryService.addCraneRequestToVoid(data).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.voidSubmitted = false;
            this.deliveryService.updatedHistory({ status: true }, 'AddedToVoid');
            this.deliveryService.updateCraneRequestHistory({ status: true }, 'AddedToVoid');
            this.mixpanelService.addMixpanelEvents('Crane Booking Voided');
            if (this.bsModalRef) {
              this.bsModalRef.hide();
            }
          }
        },
        error: (createVoidError): void => {
          this.voidSubmitted = false;
          if (createVoidError.message?.statusCode === 400) {
            this.showError(createVoidError);
          } else if (!createVoidError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(createVoidError.message, 'OOPS!');
          }
        },
      });
    }
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }

  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public confirmationClose(template: TemplateRef<any>): void {
    if (this.statusChanged && this.currentStatus) {
      this.openConfirmationModalPopupForDetailsNDR(template);
    } else {
      this.resetForm('yes');
      this.subscriptions.unsubscribe();
    }
  }

  public openConfirmationModalPopupForDetailsNDR(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.bsModalRef.hide();
      this.statusChanged = false;
    }
  }

  public voidConfirmationResponse(action: string): void {
    if (action === 'no') {
      this.modalRef.hide();
    } else {
      if (this.modalRef) {
        this.modalRef.hide();
      }
      this.addToVoid();
    }
  }

  public revertstatus(template: TemplateRef<any>): void {
    if (this.authUser.RoleId === 1 || this.authUser.RoleId === 2) {
      let data = {};
      data = {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      };
      this.modalRef2 = this.modalService.show(template, data);
    }
  }

  public statusupdate(action: string): void {
    if (action === 'no') {
      this.modalRef2.hide();
    } else {
      this.statusSubmitted = true;
      if (this.modalRef2) {
        this.modalRef2.hide();
      }
      const data = {
        id: this.currentDeliverySaveItem.id,
        status: 'Approved',
        ParentCompanyId: this.ParentCompanyId,
        statuschange: 'Reverted',
      };
      this.deliveryService
        .updateCraneRequestStatus(data)
        .subscribe((updateStatusResponse2: any): void => {
          if (updateStatusResponse2) {
            this.statusSubmitted = false;
            this.toastr.success('Status Reverted Successfully', 'Success');
            this.socket.emit('CraneApproveHistory', updateStatusResponse2);
            this.deliveryService.updateCraneRequestHistory({ status: true }, 'CraneApproveHistory');
            this.modalRef2.hide();
          }
        });
    }
  }

  public initializeSeriesOption(): void {
    this.seriesOptions = [
      {
        option: 1,
        text: 'This event',
        disabled: false,
      },
      {
        option: 2,
        text: 'This and all following events',
        disabled: false,
      },
    ];
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'open':
          this.openModal(data);
          break;
        case 'edit':
          this.openEditModal(data, item);
          break;
        case 'save':
          this.saveStatus();
          break;
        default:
          break;
      }
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
}
