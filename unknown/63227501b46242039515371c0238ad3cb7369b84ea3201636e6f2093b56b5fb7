<div class="modal-header">
  <div class="form-group d-flex align-items-center">
    <img *ngIf="currentPage === 'allCalendar'" src="./assets/images/noun_request.svg" alt="inspection" class="me-2" />
    <div *ngIf="currentPage === 'allCalendar'">
      <select
        class="styled-select"
        [(ngModel)]="selectedType"
        (change)="selectedBookingtype($event.target.value)"
      >
        <option class="styled-option" *ngFor="let item of bookingTypesList" [value]="item">
          {{ item }}
        </option>
      </select>
    </div>
  </div>
  <h1 *ngIf="currentPage !== 'allCalendar'" class="styled-h1">
    <img src="./assets/images/noun_request.svg" alt="inspection" class="me-2" />New Concrete
    Booking
  </h1>
  <div class="d-flex justify-content-between align-items-center ms-auto">
    <button type="button" class="close" aria-label="Close" (click)="close(cancelConfirmation)">
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
</div>
<div class="modal-body newupdate-alignments taginput--heightfix" *ngIf="!modalLoader">
  <div class="addcalendar-details">
    <form
      name="form"
      class="custom-material-form add-concrete-material-form"
      [formGroup]="concreteRequest"
      novalidate
    >
      <div class="row">
        <div class="col-md-6">
          <div class="form-group mb-0">
            <label class="fs12 fw600 mb-0" for="description"
              >Description
              <span class="color-red">
                <sup>*</sup>
              </span>
            </label>
            <textarea id="description"
              class="form-control fs11 radius0 mt-1"
              rows="2"
              maxlength="150"
              formControlName="description"
            ></textarea>
            <div class="color-red" *ngIf="submitted && concreteRequest.get('description').errors">
              <small *ngIf="concreteRequest.get('description').errors.required"
                >*Description is Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group pt-0 mt-0">
            <label class="fs12 fw600 mb-0" for="concreteid">Concrete Booking ID</label>
            <input  id="concreteid"
              type="text"
              class="form-control fs11 material-input p-0 h-25 mt-2 ps-2 ms-2"
              disabled="disabled"
              value="{{ concreteRequest.get('ConcreteRequestId').value }}"
            />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 primary-tooltip">
          <div class="form-group mb-0 mt-2 timezone-formgroup timezone-buttonpadding">
            <label class="fs12 fw600"  for="location"
              >Location<span class="color-red"><sup>*</sup></span>
              <div class="dot-border-info location-border-info tooltip location-tooltip">
                <span class="fw700 info-icon fs12">i</span>
                <span class="tooltiptext tooltiptext-info"
                  >Where will the materials/equipment be installed</span
                >
                <div class="arrow-down"></div></div
            ></label>
            <ng-multiselect-dropdown  id="location"
              [placeholder]="'Choose Location'"
              [settings]="locationDropdownSettings"
              [data]="locationDropdown"
              (onSelect)="locationSelected($event)"
              formControlName="LocationId"
              [(ngModel)]="defaultLocationValue"
            >
            </ng-multiselect-dropdown>
            <div class="color-red" *ngIf="submitted && concreteRequest.get('LocationId').errors">
              <small *ngIf="concreteRequest.get('LocationId').errors.required"
                >*Location is Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6 pt-4 ps-3 additional-location-detail">
          <div class="floating-concrete mt-2">
            <div class="form-group floating-label">
              <input
                class="floating-input form-control fs12 px-0"
                type="text"
                placeholder=" "  id="addlocation"
                formControlName="location"
              />
              <label class="fs12 fw600 m-0 color-grey11"  for="addlocation"
                >Additional Location Details
              </label>
            </div>
          </div>
        </div>
      </div>

      <div class="row mt-3">
        <div class="col-md-6">
          <div class="form-group">
            <label class="fs12 fw600 mt-2" for="gate">Gate<sup>*</sup></label>
            <select class="form-control fs12 material-input px-0 mt-1" formControlName="GateId" (change)="getSlots()" id="gate">
              <option value="" disabled selected hidden>Gate<sup>*</sup></option>
              <option *ngFor="let item of gateList" value="{{ item?.id }}">
                {{ item.gateName }}
              </option>
            </select>
            <div class="color-red" *ngIf="submitted && concreteRequest.get('GateId').errors">
              <small *ngIf="concreteRequest.get('GateId').errors.required">*Gate is Required.</small>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group mb-0 company-select equipment-select">
            <label class="fs12 fw600 mb-2" for="equipment">Equipment<sup>*</sup></label>
            <ng-multiselect-dropdown  id="equipment"
            [placeholder]="'Equipment'"
            [settings]="equipmentDropdownSettings"
            [data]="equipmentList"
            formControlName="EquipmentId"
            (ngModelChange)="getSlots()"
            >
          </ng-multiselect-dropdown>
            <div class="color-red" *ngIf="submitted && concreteRequest.get('EquipmentId').errors">
              <small *ngIf="concreteRequest.get('EquipmentId').errors.required"
                >*Equipment is Required.</small
              >
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-12">
          <div class="form-group taginput-height">
            <label class="fs12 fw600"  for="resperson"
              >Responsible Person <span class="color-red"><sup>*</sup></span></label
            >
            <tag-input  id="resperson"
              [onlyFromAutocomplete]="true"
              [placeholder]="' '"
              secondaryPlaceholder=" "
              formControlName="responsiblePersons"
              [onTextChangeDebounce]="500"
              class="tag-layout"
              [identifyBy]="'id'"
              [displayBy]="'email'"
            >
              <tag-input-dropdown
                [showDropdownIfEmpty]="false"
                [displayBy]="'email'"
                [identifyBy]="'id'"
                [autocompleteObservable]="requestAutoEditcompleteItems"
                [appendToBody]="false"
              >
                <ng-template let-item="item" let-index="index">
                  {{ item.email }}
                </ng-template>
              </tag-input-dropdown>
            </tag-input>
            <div
              class="color-red"
              *ngIf="submitted && concreteRequest.get('responsiblePersons').errors"
            >
              <small>*Please choose Responsible person.</small>
            </div>
          </div>
        </div>
      </div>

      <div class="row mt-3">
        <div
        class="color-red"
        *ngIf="submitted && !isTimeSlotChoosen"
      >
        <small *ngIf="submitted && !isTimeSlotChoosen"
          >*Please select a time slot..</small
        >
      </div>
        <div class="col-md-12">
          <app-time-slot
            #timeSlotRef
            (selectTime)="selectTime($event.start, $event.end)"
            [selectedBookingDate]="concreteRequest.get('concretePlacementDate').value"
            [timeZone]="timeZone"
            [selectedBookingType]="'concreteRequest'"
            [equipmentId] = "concreteRequest.get('EquipmentId').value"
            [LocationId] = "concreteRequest.get('LocationId').value"
          >
          </app-time-slot>
        </div>
      </div>


      <div class="row py-2">
        <div class="col-md-3 concrete-header">
          <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Concrete Details</h1>
        </div>
        <div class="col-md-9">
          <div class="line-1"></div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 pt-0 add-concrete-dropdown">
          <div class="form-group company-select mb-0" id="company-select8">
            <label class="fs12 fw600 m-0 pb-0" for="concretesup"
              >Concrete Supplier
              <span class="color-red">
                <sup>*</sup>
              </span></label
            >
            <ng-multiselect-dropdown id="concretesup"
              [placeholder]="'Select'"
              [settings]="concreteSupplierDropdownSettings"
              [data]="concreteSupplierDropdown"
              formControlName="concreteSupplier"
              [(ngModel)]="responsibleCompanySelectedItems"
              class="mt-0"
            >
            </ng-multiselect-dropdown>
            <div
              class="color-red"
              *ngIf="submitted && concreteRequest.get('concreteSupplier').errors"
            >
              <small *ngIf="concreteRequest.get('concreteSupplier').errors.required"
                >*Concrete Supplier is Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group customised-tag-input mb-0">
            <label class="fs12 fw600 m-0" for="mixdesign">Mix Design</label>
            <tag-input  id="mixdesign"
              [onlyFromAutocomplete]="false"
              [placeholder]="' '"
              secondaryPlaceholder=" "
              formControlName="mixDesign"
              [(ngModel)]="mixDesignList"
              (ngModelChange)="checkMixDesignDuplication($event)"
              [onTextChangeDebounce]="500"
              class="tag-layout mix-design"
              [identifyBy]="'id'"
              [displayBy]="'mixDesign'"
            >
              <tag-input-dropdown
                [showDropdownIfEmpty]="false"
                [displayBy]="'mixDesign'"
                [identifyBy]="'id'"
                [autocompleteItems]="mixDesignDropdown"
                [appendToBody]="false"
              >
                <ng-template let-item="item" let-index="index">
                  <div class="tag-input-sample fs12">
                    {{ item.mixDesign }}
                  </div>
                </ng-template>
              </tag-input-dropdown>
            </tag-input>
            <p class="color-grey11 fs12 mb-0">*Type and press enter to create the mix design tag</p>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="floating-concrete mt-3">
            <div class="form-group floating-label">
              <input id="ordernumber"
                class="floating-input form-control fs12 px-0"
                type="text"
                placeholder=" "
                formControlName="concreteOrderNumber"
                (keypress)="numberOnly($event)"
              />
              <label class="fs12 fw600 m-0 color-grey11"  for="ordernumber">Order Number </label>

            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="floating-concrete mt-3">
            <div class="form-group floating-label">
              <input
                class="floating-input form-control fs12 px-0"
                type="text"
                placeholder=" "
                formControlName="slump"  id="slump"
              />
              <label class="fs12 fw600 m-0 color-grey11" for="slump">Slump </label>

            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="floating-concrete mt-3">
            <div class="form-group floating-label">
              <input
                class="floating-input form-control fs12 px-0"
                type="text"
                placeholder=" "
                formControlName="truckSpacingHours"   id="truck"
              />

              <label class="fs12 fw600 m-0 color-grey11"  for="truck">Truck Spacing </label>

            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="floating-concrete mt-3">
            <div class="form-group floating-label">
              <input
                class="floating-input form-control fs12 px-0"
                type="text"
                placeholder=" "
                formControlName="concreteQuantityOrdered"  id="quantity"
              />
              <label class="fs12 fw600 m-0 color-grey11" for="quantity">Quantity Ordered (CY) </label>

            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-8">
          <div class="floating-concrete mt-3">
            <div class="form-group floating-label">
              <input id="pump"
                class="floating-input form-control fs12 px-0"
                type="text"
                placeholder=" "
                formControlName="primerForPump"
              />
              <label class="fs12 fw600 m-0 color-grey11" for="pump"
                >Primer ordered for the Pump
              </label>
            </div>
          </div>
        </div>
        <div class="col-md-4 mt-2">
          <ul
            class="small-switch list-group list-group-horizontal justify-content-start mnb28 mb-1"
            id="switch-control4"
          >
            <span class="fs12 my-auto">Concrete Confirmed</span>
            <li class="fs12 list-group-item border-0 p-0 color-grey11">
              <ui-switch
                switchColor="#fff"
                defaultBoColor="#CECECE"
                defaultBgColor="#CECECE"
                formControlName="isConcreteConfirmed"
                (change)="changeConcreteConfirmed($event)"
                class="ms-2 small-switch"
              >
              </ui-switch>
            </li>
          </ul>
          <div
            class="fs12 fw700 text-black mt-1"
            *ngIf="concreteRequest.get('isConcreteConfirmed').value === true"
          >
            Confirmed on
            {{ concreteRequest.get('concreteConfirmedOn').value | date : 'medium' }}
          </div>
          <div
            class="color-red"
            *ngIf="submitted && concreteRequest.get('isConcreteConfirmed').errors"
          >
            <small *ngIf="concreteRequest.get('isConcreteConfirmed').errors.required"
              >*Concrete Confirmed is Required.</small
            >
          </div>
        </div>
        <div class="col-md-12">
          <div class="form-group mt-0">
            <label class="fs12 fw600" for="recurrence">Recurrence<sup>*</sup></label>
            <select  id="recurrence"
              class="form-control fs12 material-input px-1"
              formControlName="recurrence"
              (change)="onRecurrenceSelect($event.target.value)"
            >
              <option value="" disabled selected hidden>
                Select Recurrence
                <span class="color-red">
                  <sup>*</sup>
                </span>
              </option>
              <option *ngFor="let type of recurrence" value="{{ type.value }}">
                {{ type.value }}
              </option>
            </select>
            <div class="color-red" *ngIf="submitted && concreteRequest.get('recurrence').errors">
              <small *ngIf="concreteRequest.get('recurrence').errors.required"
                >*Recurrence is required</small
              >
            </div>
          </div>
          <div
            class="row"
            *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
          >
            <div
              class="col-md-12 mt-md-0"
              *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
            >
              <label class="fs12 fw600"  for="repeatevery">Repeat Every</label>
            </div>
            <div
              class="col-md-6 mt-md-0"
              *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
            >
              <div class="form-group">
                <input  id="repeatevery"
                  type="text"
                  formControlName="repeatEveryCount"
                  class="form-control fs12 material-input p-0"
                  (input)="changeRecurrenceCount($event.target.value)"
                  min="1"
                />
              </div>
            </div>
            <div class="col-md-6 mt-md-0" *ngIf="isRepeatWithSingleRecurrence">
              <div class="form-group">
                <select
                  class="form-control fs12 material-input px-2"
                  formControlName="repeatEveryType"
                  (change)="chooseRepeatEveryType($event.target.value)"
                >
                  <option value="" disabled selected hidden>Select Recurrence</option>
                  <option *ngFor="let type of repeatWithSingleRecurrence" value="{{ type.value }}">
                    {{ type.value }}
                  </option>
                </select>
              </div>
            </div>
            <div
              class="col-md-6 mt-md-0"
              *ngIf="isRepeatWithMultipleRecurrence || showRecurrenceTypeDropdown"
            >
              <div class="form-group">
                <select
                  class="form-control fs12 material-input px-2"
                  formControlName="repeatEveryType"
                  (change)="chooseRepeatEveryType($event.target.value)"
                >
                  <option value="" disabled selected hidden>Select Recurrence</option>
                  <option
                    *ngFor="let type of repeatWithMultipleRecurrence"
                    value="{{ type.value }}"
                  >
                    {{ type.value }}
                  </option>
                </select>
              </div>
            </div>
          </div>
          <div class="row addcalendar-displaydays ml3">
            <div
              class="col-md-12 ps-0 pt-0"
              *ngIf="
                (selectedRecurrence === 'Weekly' ||
                  isRepeatWithMultipleRecurrence ||
                  isRepeatWithSingleRecurrence) &&
                selectedRecurrence !== 'Monthly' &&
                selectedRecurrence !== 'Yearly'
              "
            >
              <ul class="displaylists ps-0">
                <li *ngFor="let item of weekDays; let i = index" class="fs12 list-inline-item">
                  <input
                    type="checkbox"
                    [disabled]="item.isDisabled"
                    [value]="item.value"
                    class="d-none"
                    id="days-{{ i }}"
                    (change)="onChange($event)"
                    [checked]="item.checked"
                  />
                  <label for="days-{{ i }}">{{ item.display }}</label>
                </li>
                <div class="color-red" *ngIf="submitted && concreteRequest.controls['days'].errors">
                  <small *ngIf="concreteRequest.controls['days'].errors.required">*Required </small>
                </div>
              </ul>
            </div>
          </div>
          <div
            class="row ml3"
            *ngIf="selectedRecurrence === 'Monthly' || selectedRecurrence === 'Yearly'"
          >
            <div class="col-md-8 mt-md-0 p-0">
              <div class="form-check">
                <input
                  class="form-check-input c-pointer"
                  type="radio"
                  formControlName="chosenDateOfMonth"
                  id="flexRadioDefault1"
                  [value]="1"
                  (change)="changeMonthlyRecurrence()"
                />
                <label class="form-check-label fs12 color-orange" for="flexRadioDefault1">
                  On day {{ monthlyDate }}
                </label>
              </div>
              <div class="form-check">
                <input
                  class="form-check-input c-pointer"
                  type="radio"
                  formControlName="chosenDateOfMonth"
                  id="flexRadioDefault2"
                  [value]="2"
                  (change)="changeMonthlyRecurrence()"
                />
                <label class="form-check-label fs12 color-orange" for="flexRadioDefault2">
                  On the {{ monthlyDayOfWeek }}
                  <p *ngIf="selectedRecurrence === 'Yearly'">
                    of
                    {{ concreteRequest.get('concretePlacementDate').value | date : 'LLLL' }}
                  </p>
                </label>
              </div>
              <div class="form-check" *ngIf="enableOption">
                <input
                  class="form-check-input c-pointer"
                  type="radio"
                  formControlName="chosenDateOfMonth"
                  id="flexRadioDefault3"
                  [value]="3"
                  (change)="changeMonthlyRecurrence()"
                />
                <label class="form-check-label fs12 color-orange" for="flexRadioDefault3">
                  On the
                  {{ monthlyLastDayOfWeek }}
                  <p *ngIf="selectedRecurrence === 'Yearly'">
                    of
                    {{ concreteRequest.get('concretePlacementDate').value | date : 'LLLL' }}
                  </p>
                </label>
              </div>
              <div>
                <div
                  class="color-red"
                  *ngIf="
                    submitted &&
                    (concreteRequest.get('monthlyRepeatType')?.errors ||
                      concreteRequest.get('dateOfMonth')?.errors)
                  "
                >
                  <small *ngIf="concreteRequest.get('monthlyRepeatType')?.errors?.required"
                    >*required</small
                  >
                  <small *ngIf="concreteRequest.get('dateOfMonth')?.errors?.required"
                    >*required</small
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div
            class="row"
            *ngIf="
              selectedRecurrence === 'Daily' ||
              selectedRecurrence === 'Monthly' ||
              selectedRecurrence === 'Yearly' ||
              selectedRecurrence === 'Weekly'
            "
          >
            <div class="col-md-12 float-start mt-0">
              <label class="fs12 fw600"  for="endate">End Date</label>
              <div class="input-group mb-3">
                <input  id="endate"
                  class="form-control fs12 fw500 material-input"
                  #dp="bsDatepicker"
                  bsDatepicker
                  formControlName="endDate"
                  [minDate]="recurrenceMinDate"
                  placement="top"
                  [minDate]="recurrenceMinDate"
                  placeholder="End Date *"
                  [bsConfig]="{
                    isAnimated: true,
                    showWeekNumbers: false,
                    customTodayClass: 'today'
                  }"
                  (ngModelChange)="showMonthlyRecurrence()"
                />
                  <span class="input-group-text">
                    <img
                      src="./assets/images/date.svg"
                      class="h-12px"
                      alt="Date"
                      (click)="dp.toggle()"  (keydown)="dp.toggle()"
                      [attr.aria-expanded]="dp.isOpen"
                    />
                  </span>
              </div>
            </div>
          </div>
          <div class="row addcalendar-displaydays">
            <div class="col-md-12 mt-md-0 pb-0" *ngIf="message">
              <p class="fs12 color-grey11">
                <span class="color-red fw-bold">*</span>
                {{ message }}
              </p>
            </div>
          </div>
        </div>
        <div class="col-md-12">
          <label class="fs12 fw600" for="carbtracking">Carbon Tracking</label>
          <div class="row mb-0 mx-0 pb-3 border-light-gray border-left-green">
            <div class="col-md-6">
              <div class="form-group my-0 company-select equipment-select">
                <label class="fs12 fw600 mt10" for="orgadd">Origination Address</label>
                <div class="border-bottom-grey">
                  <input id="orgadd"
                    type="text"
                    class="form-control fs12 material-input border-0 px-0 mt6"
                    formControlName="originationAddress"
                    placeholder="Origination Address"
                    ngx-gp-autocomplete
                    (onAddressChange)="handleAddressChange($event)"
                    #placesRef="ngx-places"
                  />
                </div>
              </div>
            </div>
            <div class="col-md-6 primary-tooltip">
              <div class="form-group mb-0 timezone-formgroup vehicleType-formgroup mt5">
                <label class="fs12 fw600" for="vehtype">Vehicle Type</label>
                <ng-multiselect-dropdown  id="vehtype"
                  [placeholder]="'Choose Vehicle Type'"
                  [settings]="vehicleTypeDropdownSettings"
                  [data]="vehicleTypes"
                  (onSelect)="vehicleTypeSelected($event)"
                  formControlName="vehicleType"
                >
                </ng-multiselect-dropdown>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="row py-2">
        <div class="col-md-2 pump-header">
          <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Pump Details</h1>
        </div>
        <div class="col-md-9">
          <div class="line-1 line-2"></div>
        </div>
      </div>
      <div class="row pb-2">
        <div class="col-md-6">
          <ul
            class="small-switch list-group list-group-horizontal justify-content-start mnb28"
            id="switch-control4"
          >
            <span class="fs12 fw600">Pump Required</span>
            <li class="fs12 list-group-item border-0 p-0 color-grey11">
              <ui-switch
                switchColor="#fff"
                defaultBoColor="#CECECE"
                defaultBgColor="#CECECE"
                formControlName="isPumpConfirmed"
                class="ms-2"
                formControlName="isPumpRequired"
              >
              </ui-switch>
            </li>
          </ul>
        </div>
      </div>

      <div
        class="row pt-4 align-items-end"
        *ngIf="concreteRequest.get('isPumpRequired').value === false ? false : true"
      >
        <div class="col-md-6">
          <div class="form-group customised-tag-input mb-0">
              <label class="fs12 fw600 m-0"  for="pumpsize">Pump Size<sup>*</sup></label>
            <tag-input  id="pumpsize"
              [onlyFromAutocomplete]="false"
              [placeholder]="' '"
              formControlName="pumpSize"
              secondaryPlaceholder=" "
              [(ngModel)]="pumpSizeList"
              (ngModelChange)="checkPumpSizeDuplication($event)"
              [onTextChangeDebounce]="500"
              class="tag-layout"
              [identifyBy]="'id'"
              [displayBy]="'pumpSize'"
            >
              <tag-input-dropdown
                [showDropdownIfEmpty]="false"
                [displayBy]="'pumpSize'"
                [identifyBy]="'id'"
                [autocompleteItems]="pumpSizeDropdown"
                [appendToBody]="false"
              >
                <ng-template let-item="item" let-index="index">
                  <div class="tag-input-sample fs12">
                    {{ item.pumpSize }}
                  </div>
                </ng-template>
              </tag-input-dropdown>
            </tag-input>
            <p class="color-grey11 fs12 mb-0">*Type and press enter to create the pump size tag</p>
            <div class="color-red" *ngIf="submitted && concreteRequest.get('pumpSize').errors">
              <small>*Pump Size is Required</small>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <label class="fs12 fw600 m-0 mb-2" for="pumporder">Pump Ordered<sup>*</sup></label>
          <div class="input-group mb-3">
            <input  id="pumporder"
              class="form-control fs12 ps-0 fw500 material-input"
              #dp="bsDatepicker"
              bsDatepicker
              placeholder="Pump Ordered"
              [bsConfig]="{
                isAnimated: true,
                showWeekNumbers: false,
                customTodayClass: 'today'
              }"
              formControlName="pumpOrderedDate"
            />
              <span class="input-group-text">
                <img
                  src="./assets/images/date.svg"
                  class="h-12px"
                  alt="Date"
                  (click)="dp.toggle()"  (keydown)="dp.toggle()"
                  [attr.aria-expanded]="dp.isOpen"
                />
              </span>
          </div>
          <div class="color-red" *ngIf="submitted && concreteRequest.get('pumpOrderedDate').errors">
            <small *ngIf="concreteRequest.get('pumpOrderedDate').errors.required"
              >*Pump Ordered is Required.</small
            >
          </div>
        </div>
      </div>

      <div class="row" *ngIf="concreteRequest.get('isPumpRequired').value === false ? false : true">
        <div class="col-md-6 mt-0">
          <div class="floating-concreted mt-3">
            <div class="form-group mb-0">
              <label class="fs12 fw600 m-0 mb-2" for="pumploc">Pump Location<sup>*</sup></label>
              <input  id="pumploc"
                class="floating-input form-control fs12 px-0"
                type="text"
                placeholder=" "
                formControlName="pumpLocation"
              />
            </div>
          </div>
          <div class="color-red" *ngIf="submitted && concreteRequest.get('pumpLocation').errors">
            <small *ngIf="concreteRequest.get('pumpLocation').errors.required"
              >*Pump Location is Required.</small
            >
          </div>
        </div>

        <div class="col-md-6">
          <div class="form-group">
            <div class="timezone-flex pt-0">
              <div class="input-group mb-0 delivery-time">
                <label class="fs12 fw600" for="pumpshow">Pump Show up Time</label>
                <timepicker id="pumpshow"
                  [formControlName]="'pumpWorkStart'"
                  (ngModelChange)="changeDate1($event)"
                  (keypress)="numberOnly($event)"
                  class="mt-2"
                >
                </timepicker>
              </div>
              <div
                class="color-red"
                *ngIf="submitted && concreteRequest.get('pumpWorkStart').errors"
              >
                <small *ngIf="concreteRequest.get('pumpWorkStart').errors.required"
                  >*Pump Show up Time is Required</small
                >
              </div>
              <div class="input-group mb-0 delivery-time">
                <label class="fs12 fw600" for="pumpcompletiontime">Pump Completion Time</label>
                <timepicker id="pumpcompletiontime"
                  [formControlName]="'pumpWorkEnd'"
                  class="mt-2"
                  (ngModelChange)="deliveryEndTimeChangeDetection()"
                  (keypress)="numberOnly($event)"
                >
                </timepicker>
              </div>
              <div class="color-red" *ngIf="submitted && concreteRequest.get('pumpWorkEnd').errors">
                <small *ngIf="concreteRequest.get('pumpWorkEnd').errors.required"
                  >*Pump Completion Time is Required</small
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="mb10" *ngIf="concreteRequest.get('isPumpRequired').value === false ? false : true">
        <label class="fs12 fw600" for="carbtrac">Carbon Tracking</label>
        <div class="row mb-0 mx-0 pb-3 border-light-gray border-left-green">
          <div class="col-md-6">
            <div class="form-group my-0 company-select equipment-select">
              <label class="fs12 fw600 mt10" for="origadd">Origination Address</label>
              <div class="border-bottom-grey">
                <input id="origadd"
                  type="text"
                  class="form-control fs12 material-input border-0 px-0 mt6"
                  formControlName="originationAddressPump"
                  placeholder="Origination Address"
                  ngx-gp-autocomplete
                  (onAddressChange)="handlePumpAddressChange($event)"
                  #placesRef="ngx-places"
                />
              </div>
            </div>
          </div>
          <div class="col-md-6 primary-tooltip">
            <div class="form-group mb-0 timezone-formgroup vehicleType-formgroup mt5">
              <label class="fs12 fw600" for="vehtype">Vehicle Type</label>
              <ng-multiselect-dropdown id="vehtype"
                [placeholder]="'Choose Vehicle Type'"
                [settings]="vehicleTypeDropdownSettings"
                [data]="vehicleTypes"
                (onSelect)="pumpVehicleTypeSelected($event)"
                formControlName="vehicleTypePump"
              >
              </ng-multiselect-dropdown>
            </div>
          </div>
        </div>
      </div>
      <div
        class="row pb-2 ms-0 mb-0"
        *ngIf="concreteRequest.get('isPumpRequired').value === false ? false : true"
      >
        <div class="col-md-6 ps-0">
          <ul
            class="small-switch list-group list-group-horizontal justify-content-start mnb28 mb-2"
            id="switch-control4"
          >
            <span class="fs12 my-auto">Pump Confirmed</span>
            <li class="fs12 list-group-item border-0 p-0 color-grey11">
              <ui-switch
                switchColor="#fff"
                defaultBoColor="#CECECE"
                defaultBgColor="#CECECE"
                formControlName="isPumpConfirmed"
                (change)="changePumpConfirmed($event)"
                class="ms-2"
              >
              </ui-switch>
            </li>
          </ul>
          <div *ngIf="concreteRequest.get('pumpConfirmedOn').value">
            <p class="fs12 fw700 text-black ng-star-inserted mb-0">
              Confirmed on
              {{ concreteRequest.get('pumpConfirmedOn').value | date : 'medium' }}
            </p>
          </div>
        </div>
      </div>

      <div class="row pb-2 ms-0 mb-0 mt-2">
        <div class="col-md-12 pl0 ps-0">
          <label class="fs12 fw600" for="notes">Notes</label>
          <textarea id="notes"
            class="form-control fs11 radius0 mt-1"
            rows="2"
            formControlName="notes"
          ></textarea>
        </div>
      </div>

      <div class="modal-footer my-1 border-0 justify-content-center add-calendar-footer">
        <div class="mt-0 mb15 text-center">
          <button
            class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3 px-2rem"
            type="button"
            (click)="close(cancelConfirmation)"
          >
            Cancel
          </button>
          <button
            class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem"
            (click)="onSubmit()"
            [disabled]="formSubmitted && concreteRequest.valid"
          >
            <em
              class="fa fa-spinner"
              aria-hidden="true"
              *ngIf="formSubmitted && concreteRequest.valid"
            ></em>
            Submit
          </button>
        </div>
      </div>
    </form>
  </div>
</div>
<div class="modal-body text-center" *ngIf="modalLoader">Loading...</div>

<!--Confirmation Popup-->
<div id="confirm-popup7">
  <ng-template #cancelConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">Are you sure you want to cancel?</p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetForm('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="resetForm('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
