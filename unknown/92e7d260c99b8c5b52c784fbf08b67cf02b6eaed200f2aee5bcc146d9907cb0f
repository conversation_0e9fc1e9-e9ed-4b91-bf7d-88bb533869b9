/* eslint-disable @typescript-eslint/no-inferrable-types */
/* eslint-disable max-len */
/* eslint-disable max-lines-per-function */
/* eslint-disable no-param-reassign */
import {
  Component,
  TemplateRef,
  OnInit,
  AfterViewInit,
  ViewChild,
  Input,
  ElementRef,
  OnDestroy,
} from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import {
  UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators,
} from '@angular/forms';
import { Observable, Subscription } from 'rxjs';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import moment from 'moment';
import { Router } from '@angular/router';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { hoursDropdown } from '../../services/hoursDropdown';
import { minutesDropdown } from '../../services/minutesDropdown';
import { MixpanelService } from '../../services/mixpanel.service';
import { ProjectSettingsService } from '../../services/project_settings/project-settings.service';
import {
  weekDays, editRecurrence, repeatWithSingleRecurrence, repeatWithMultipleRecurrence,
} from '../../services/common';
import { TimeslotComponent } from 'src/app/layout/time-slot/time-slot.component';

@Component({
  selector: 'app-edit-concrete-requests',
  templateUrl: './edit-concrete-requests.component.html',
})
export class EditConcreteRequestsComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('cancelRecurrence') cancelRecurrenceTemplate: TemplateRef<any>;

  @ViewChild('timeSlotsContainer') timeSlotsContainer: ElementRef;

  @ViewChild('timeSlotRef') timeSlotComponent: TimeslotComponent;

  @Input() seriesoption: number;

  public concreteRequest: UntypedFormGroup;

  public submitted = false;

  public dropdownSettings: any = {};

  public modalLoader = false;

  public placementStart: Date;

  public placementEnd: Date;

  public concreteRequestId: any;

  public array3Value = [];

  public editBeforeCompany = [];

  public editBeforeLocation = [];

  public editBeforePumpsize = [];

  public editBeforeMixDesigns = [];

  public pumpWorkEnd: Date;

  public pumpWorkStart: Date;

  public hoursDropdown = hoursDropdown;

  public minutesDropdown = minutesDropdown;

  public equipmentDropdownSettings: IDropdownSettings = {
    singleSelection: false,
    idField: 'id',
    textField: 'equipmentName',
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    itemsShowLimit: 6,
    allowSearchFilter: true,
  };

  public concreteSupplierDropdownSettings: IDropdownSettings = {
    singleSelection: false,
    idField: 'id',
    textField: 'companyName',
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    itemsShowLimit: 6,
    allowSearchFilter: true,
  };

  public isDisabled = true;

  public authUser: any = {};

  public bindData: any;

  public ProjectId: number;

  public ParentCompanyId: number;

  public locationDropdown: any = [];

  public concreteSupplierDropdown: [];

  public mixDesignDropdown: [];

  public pumpSizeDropdown: [];

  public formSubmitted = false;

  public loader: boolean = false;

  public currentEditItem: any = {};

  public NDRTimingChanged: boolean = false;

  public formEdited = true;

  public locationList = [];

  public mixDesignList = [];

  public pumpSizeList = [];

  public primerOrderedDropdown = [
    { id: 1, data: 'Yes' },
    { id: 2, data: 'No' },
  ];

  public errmemberenable: boolean = false;

  public memberList: any = [];

  public deliveryWindowTime;

  public deliveryWindowTimeUnit;

  public seriesOption: number;

  public recurrenceId: number;

  public recurrenceEndDate;

  public isDisabledDate: boolean = false;

  public minDateOfrecurrenceEndDate;

  public selectedLocationId: any;

  public vehicleTypeChosen: any;

  public locationDropdownSettings: IDropdownSettings = {
    singleSelection: true,
    idField: 'id',
    textField: 'locationPath',
    allowSearchFilter: true,
    closeDropDownOnSelection: true,
  };

  public selectedVehicleType: any;

  public selectedPumpVehicleType: any;

  public vehicleTypes = [
    { id: 1, type: 'Medium and Heavy Duty Truck' },
    { id: 2, type: 'Passenger Car' },
    { id: 3, type: 'Light Duty Truck' },
  ];

  public vehicleTypeDropdownSettings: IDropdownSettings = {
    singleSelection: true,
    idField: 'id',
    textField: 'type',
    allowSearchFilter: true,
    closeDropDownOnSelection: true,
  };

  public getChosenLocation: any;

  public locationDetailsDropdown: any[];

  public checkform: any = new UntypedFormArray([]);

  public enableOption = false;

  public message = '';

  public valueExists = [];

  public recurrence = editRecurrence;

  public selectedRecurrence = 'Does Not Repeat';

  public timeZone: any ;

  public isRepeatWithSingleRecurrence = false;

  public isRepeatWithMultipleRecurrence = false;

  public showRecurrenceTypeDropdown = false;

  public weekDays: any = weekDays;

  public monthlyDayOfWeek = '';

  public monthlyDate = '';

  public monthlyLastDayOfWeek = '';

  public repeatWithSingleRecurrence = repeatWithSingleRecurrence;

  public repeatWithMultipleRecurrence = repeatWithMultipleRecurrence;

  public recurrenceMinDate = new Date();

  public daysEdited = false;

  public valueEdited = false;

  public previousRecurrence: any;

  public previousEndDate: Date;

  public previousRepeatEveryCount: any;

  public previousRepeatEveryType: any;

  public previousChosenDateOfMonth: any;

  public previousDays: any;

  public previousDateOfMonth: any;

  public previousMonthlyRepeatType: any;

  public previousChosenDateOfMonthValue: any;

  public payloadDays: any = [];

  public recurrenceEdited = false;
  concretePlacementEnd: Date;
  endPickerTime: string;
  isAM: any;
  selectedTime: string;
  equipmentList: any;
  gateList: any;
  editBeforeEquipment: any[];
  public isEditMode: boolean = false;
   public noEquipmentOption = { id: 0, equipmentName: 'No Equipment Needed' };

  private subscriptions: Subscription[] = [];

  public constructor(
    private readonly modalService: BsModalService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly deliveryService: DeliveryService,
    private readonly projectService: ProjectService,
    private readonly modalRef: BsModalRef,
    private readonly toastr: ToastrService,
    public socket: Socket,
    private readonly mixpanelService: MixpanelService,
    public router: Router,
    private modalRef1: BsModalRef,
    public modalRef2: BsModalRef,
    public projectSettingsService: ProjectSettingsService,
  ) {
    this.projectService.projectParent.subscribe((response): any => {
      this.ProjectId = response.ProjectId;
      this.ParentCompanyId = response.ParentCompanyId;
    });
    this.concreteRequestCreationForm();
    this.getDropdownValues();
    this.getMembers();
    this.getProjectSettings();
  }

  public getDropdownValues(): any {
    this.modalLoader = true;
    if (this.ProjectId) {
      const payload = {
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.deliveryService.getConcreteRequestDropdownData(payload).subscribe((response): void => {
        if (response.data) {
          this.locationDropdown = response.data.locationDropdown;
          this.locationDetailsDropdown = response.data.locationDetailsDropdown;
            this.equipmentList = [this.noEquipmentOption, ...(response.data.locationDropdown[0].EquipmentId)];
          this.gateList = response.data.locationDropdown[0].gateDetails;
          this.timeZone = response.data.locationDropdown[0].TimeZoneId?response.data.locationDropdown[0].TimeZoneId[0].location:''
          this.concreteSupplierDropdown = response.data.concreteSupplierDropdown;
          this.mixDesignDropdown = response.data.mixDesignDropdown;
          this.pumpSizeDropdown = response.data.pumpSizeDropdown;
        }
      });
    }
  }

  public handleAddressChange(address): void {
    this.concreteRequest.get('originationAddress').setValue(address.formatted_address);
  }

  public handlePumpAddressChange(address): void {
    this.concreteRequest.get('originationAddressPump').setValue(address.formatted_address);
  }

  public ngOnInit(): void {
    const setStartTime = 7;
    this.placementStart = new Date();
    this.placementStart.setHours(setStartTime);
    this.placementStart.setMinutes(0);
    this.placementEnd = new Date();
    this.placementEnd.setHours(setStartTime + 1);
    this.placementEnd.setMinutes(0);
    this.pumpWorkStart = this.placementStart;
    this.pumpWorkEnd = this.placementEnd;
    this.concreteRequest.get('concretePlacementStart').setValue(this.placementStart);
    this.concreteRequest.get('concretePlacementEnd').setValue(this.placementEnd);
    this.concreteRequest.get('pumpWorkStart').setValue(this.pumpWorkStart);
    this.concreteRequest.get('pumpWorkEnd').setValue(this.pumpWorkEnd);

    // Move subscription setup here to ensure it's set up before modal opens
    const editRequestSubscription = this.deliveryService.EditConcreteRequestId.subscribe(
      (getEditConcreteRequestIdResponse4): void => {
        this.concreteRequestId = getEditConcreteRequestIdResponse4;
        if (this.concreteRequestId) {
          this.getConcreteRequestData();
        }
      },
    );
    const loginUserSubscription = this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
      }
    });

    this.subscriptions.push(editRequestSubscription, loginUserSubscription);
  }

  public ngOnDestroy(): void {
    this.subscriptions.forEach(subscription => subscription.unsubscribe());
  }

  public locationSelected(data): void {
    this.getChosenLocation = this.locationDropdown.filter((obj: any): any => +obj.id === +data.id);
    if (this.getChosenLocation) {
      this.gateList = []
      this.gateList = this.getChosenLocation[0].gateDetails;
      this.equipmentList = []
     this.equipmentList = [this.noEquipmentOption, ...(this.getChosenLocation[0].EquipmentId ? this.getChosenLocation[0].EquipmentId : [])];
      if (this.equipmentList.length) {
        let newEquipmentList = []
        this.equipmentList.forEach((element) => {
          const equipmentdata = {
            id: element.id,
            equipmentName: element.equipmentName,
          };
          newEquipmentList.push(equipmentdata);
        });
        this.editBeforeEquipment = newEquipmentList;
        // Only set equipment values if we're not in edit mode or if no equipment is already selected
        const currentEquipmentValue = this.concreteRequest.get('EquipmentId').value;
        if (!this.isEditMode && (!currentEquipmentValue || currentEquipmentValue.length === 0)) {
          this.concreteRequest.get('EquipmentId').patchValue(newEquipmentList);
        }
      } else {
        // Only clear equipment if we're not in edit mode
        const currentEquipmentValue = this.concreteRequest.get('EquipmentId').value;
        if (!this.isEditMode && (!currentEquipmentValue || currentEquipmentValue.length === 0)) {
          this.concreteRequest.get('EquipmentId').patchValue([]);
        }
      }
      this.timeZone = this.getChosenLocation[0].TimeZoneId?this.getChosenLocation[0].TimeZoneId[0].location:''
    }
    this.selectedLocationId = this.getChosenLocation[0]?.id;
  }

  public vehicleTypeSelected(data: any): void {
    const vehicleTypeChosen = this.vehicleTypes.find((obj: any): any => +obj.id === +data.id);
    this.selectedVehicleType = vehicleTypeChosen?.type;
  }

  public pumpVehicleTypeSelected(data: any): void {
    const vehicleTypeChosen = this.vehicleTypes.find((obj: any): any => +obj.id === +data.id);
    this.selectedPumpVehicleType = vehicleTypeChosen?.type;
  }

  public getProjectSettings(): void {
    if (this.ProjectId) {
      const params = {
        ProjectId: this.ProjectId,
      };
      this.projectSettingsService.getProjectSettings(params).subscribe((res): void => {
        const responseData = res.data;
        this.deliveryWindowTime = responseData.deliveryWindowTime;
        this.deliveryWindowTimeUnit = responseData.deliveryWindowTimeUnit;
      });
    }
  }


  public setEquipment(): void {
    const { equipmentDetails } = this.currentEditItem;
      if(equipmentDetails.length && !equipmentDetails[0].Equipment){
      equipmentDetails[0].Equipment = { id: 0, equipmentName: 'No Equipment Needed' }
    }
    const newEquipmentList = [];
    if (equipmentDetails !== undefined && equipmentDetails.length > 0) {
      equipmentDetails.forEach((element: { Equipment: { id: any; equipmentName: any } }): void => {
        if (element.Equipment && element.Equipment.id !== undefined && element.Equipment.id !== null && element.Equipment.equipmentName) {
          const data = {
            id: element.Equipment.id,
            equipmentName: element.Equipment.equipmentName,
          };
          newEquipmentList.push(data);
        }
      });
    }
    this.editBeforeEquipment = newEquipmentList;
    // Only set value if we have equipment data and the form control exists
    if (newEquipmentList.length > 0 && this.concreteRequest.get('EquipmentId')) {
      // Use setValue instead of patchValue to ensure proper initialization
      this.concreteRequest.get('EquipmentId').setValue(newEquipmentList);
    }
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }

  getBookingData() {
    const equipmentId = this.concreteRequest.get('EquipmentId').value;
    const locationId = this.concreteRequest.get('LocationId').value[0].id;
    const gateId = this.concreteRequest.get('GateId').value;

    if (this.timeSlotComponent) {
      this.timeSlotComponent.getEventNDR(equipmentId, locationId, gateId, this.timeZone,'');
    }
  }

  public equipmentSelected(value: any): void {
    if(value) {
      if(value.length == this.equipmentList.length -1 && this.equipmentList[0].id == 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
      }
      if(value.length != this.equipmentList.length && this.equipmentList[0].id != 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
      }
      if(value.length == 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
      }
      
      let hasNoEquipmentOption = false;
      let hasOtherEquipment = false;
      
      // Check if "No Equipment Needed" (id = 0) is selected
      hasNoEquipmentOption = value.some((item: any) => item.id === 0);

      // Check if other equipment is selected
      hasOtherEquipment = value.some((item: any) => item.id !== 0);

      const previousSelection = this.concreteRequest.get('EquipmentId').value || [];
      const previousHasOther = previousSelection.some((item: any) => item.id !== 0);

      // Rule 1: If "No Equipment Needed" is selected and other items are selected, keep only "No Equipment Needed"
      if (hasNoEquipmentOption && hasOtherEquipment && !previousHasOther) {
        this.toastr.warning('When "No Equipment Needed" is selected, other equipment options cannot be selected.', 'Warning');
        const noEquipmentOnly = value.filter((item: any) => item.id === 0);
        this.concreteRequest.get('EquipmentId').setValue(noEquipmentOnly);
        value = noEquipmentOnly;
        hasOtherEquipment = false;
      }

      // Rule 2: If other equipment is already selected and "No Equipment Needed" is now selected, remove it
      if (previousHasOther && hasNoEquipmentOption) {
        this.toastr.warning('When other equipment is selected, "No Equipment Needed" cannot be selected.', 'Warning');
        const filteredSelection = value.filter((item: any) => item.id !== 0);
        this.concreteRequest.get('EquipmentId').setValue(filteredSelection);
        value = filteredSelection;
        hasNoEquipmentOption = false;
      }
    }
    
    this.onEditSubmitForm();
    this.getSlots();
  }

  public getSlots() {
    this.getBookingData()
  }

  public areDifferentByProperty(a, b, prop): boolean {
    const array1 = a.map((x): any => x[prop]);
    const array2 = b.map((x): any => x[prop]);
    this.array3Value = array1.concat(array2);
    this.array3Value = [...new Set([...array1, ...array2])];
    return this.array3Value.length !== array1.length;
  }

  public close(template: TemplateRef<any>): void {
    if (
      this.concreteRequest.touched
      || this.NDRTimingChanged
      || (this.concreteRequest.get('concreteSupplier').dirty
        && this.concreteRequest.get('concreteSupplier').value
        && this.concreteRequest.get('concreteSupplier').value.length > 0
        && this.areDifferentByProperty(
          this.editBeforeCompany,
          this.concreteRequest.get('concreteSupplier').value,
          'concreteSupplier',
        ))

      || (this.concreteRequest.get('pumpSize').dirty
        && this.concreteRequest.get('pumpSize').value
        && this.concreteRequest.get('pumpSize').value.length > 0
        && this.areDifferentByProperty(
          this.editBeforePumpsize,
          this.concreteRequest.get('pumpSize').value,
          'pumpSize',
        ))
      || (this.concreteRequest.get('mixDesign').dirty
        && this.concreteRequest.get('mixDesign').value
        && this.concreteRequest.get('mixDesign').value.length > 0
        && this.areDifferentByProperty(
          this.editBeforeMixDesigns,
          this.concreteRequest.get('mixDesign').value,
          'mixDesign',
        ))
    ) {
      this.openConfirmationModalPopupForEditConcreteRequest(template);
    } else {
      this.resetForm('yes');
    }
  }

  public openConfirmationModalPopupForEditConcreteRequest(template): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public QuantitynumberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode === 32 || charCode === 46) {
      return true;
    }
    if (charCode > 31 && (charCode < 48 || charCode > 57 || charCode !== 32 || charCode !== 46)) {
      return false;
    }
    return true;
  }

  public checkLocationDuplication(data): void {
    if (data && data.length >= 1) {
      const valueArr = data.map((item): any => item.location);
      const isDuplicate = valueArr.some((item, idx): any => valueArr.indexOf(item) !== idx);
      if (isDuplicate) {
        this.locationList.pop();
      }
    }
  }

  public checkMixDesignDuplication(data): void {
    if (data && data.length >= 1) {
      const valueArr = data.map((item): any => item.mixDesign);
      const isDuplicate = valueArr.some((item, idx): any => valueArr.indexOf(item) !== idx);
      if (isDuplicate) {
        this.mixDesignList.pop();
      }
    }
  }

  public checkPumpSizeDuplication(data): void {
    if (data && data.length >= 1) {
      const valueArr = data.map((item): any => item.pumpSize);
      const isDuplicate = valueArr.some((item, idx): any => valueArr.indexOf(item) !== idx);
      if (isDuplicate) {
        this.pumpSizeList.pop();
      }
    }
  }

  public changeDate(event: any): void {
    if (!this.modalLoader) {
      const startTime = new Date(event).getHours();
      const minutes = new Date(event).getMinutes();
      this.placementEnd = new Date();
      this.placementEnd.setHours(startTime + 1);
      this.placementEnd.setMinutes(minutes);
      this.concreteRequest.get('concretePlacementEnd').setValue(this.placementEnd);
      this.NDRTimingChanged = true;
    }
    this.onEditSubmitForm();
  }

  public changeDate1(event: any): void {
    if (!this.modalLoader) {
      const startTime = new Date(event).getHours();
      const minutes = new Date(event).getMinutes();
      this.pumpWorkEnd = new Date();
      this.pumpWorkEnd.setHours(startTime + 1);
      this.pumpWorkEnd.setMinutes(minutes);
      this.concreteRequest.get('pumpWorkEnd').setValue(this.pumpWorkEnd);
      this.NDRTimingChanged = true;
    }
    this.onEditSubmitForm();
  }

  public createConcreteRequest(payload): void {
    this.deliveryService.editConcreteRequest(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.mixpanelService.addMixpanelEvents('Edited Concrete Booking');
          this.socket.emit('ConcreteEditHistory', response);
          this.formReset();
          this.concreteRequest.reset();
          this.deliveryService.updateConcreteRequestHistory(
            { status: true },
            'ConcreteEditHistory',
          );
          this.resetForm('yes');
          this.NDRTimingChanged = false;
          this.formEdited = false;
        }
      },
      error: (NDRCreateHistoryError): void => {
        this.formReset();
        this.NDRTimingChanged = false;
        this.formEdited = false;
        if (NDRCreateHistoryError.message?.statusCode === 400) {
          this.showError(NDRCreateHistoryError);
        } else if (!NDRCreateHistoryError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(NDRCreateHistoryError.message, 'OOPS!');
        }
      },
    });
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public onEditSubmitForm(): void {
    if (this.concreteRequest.dirty || this.NDRTimingChanged || this.daysEdited) {
      this.formEdited = false;
      this.valueEdited = true;
    }
  }

  public gatecheck(value, menuname): void {
    if (menuname === 'Person') {
      const result = this.memberList.filter((o): any => value?.some(({ id }): any => o.id === id));
      if (value && value.length !== result.length) {
        this.errmemberenable = true;
      } else {
        this.errmemberenable = false;
      }
    }
  }

  public selectTime(startStr: string, endStr: string) {
    const startDate = new Date(startStr);
    const endDate = new Date(endStr);

    this.concreteRequest.patchValue({
      concretePlacementStart: startDate,
      concretePlacementEnd: endDate,
      concretePlacementDate: startDate
    });

    this.selectedTime = `${startDate.toLocaleTimeString()} - ${endDate.toLocaleTimeString()}`;
    this.NDRTimingChanged = true;
    this.onEditSubmitForm();
  }

  public getIndexData(formValue) {
    const arr1 = this.memberList;
    const arr2 = formValue.responsiblePersons;
    let index2: number;
    const result = arr1.filter((o) => arr2.some(({ id }) => o.id === id));
    if (formValue.responsiblePersons.length !== result.length) {
      index2 = -1;
    } else {
      index2 = 0;
    }

    return index2;
  }


  public onEditSubmit(): void {
    if (this.concreteRequest.dirty || this.NDRTimingChanged) {
      this.formEdited = false;
    }
    this.submitted = true;
    this.formSubmitted = true;
    const persons = [];
    if (this.concreteRequest.invalid) {
      this.formSubmitted = false;
      return;
    }
    const formValue = this.concreteRequest.value;
    const placementDate = new Date(formValue.concretePlacementDate);
    const startHours = new Date(formValue.concretePlacementStart).getHours();
    const startMinutes = new Date(formValue.concretePlacementStart).getMinutes();
    const placementStart = this.convertStart(placementDate, startHours, startMinutes);
    const endHours = new Date(formValue.concretePlacementEnd).getHours();
    const endMinutes = new Date(formValue.concretePlacementEnd).getMinutes();
    const placementEnd = this.convertStart(placementDate, endHours, endMinutes);
    const placementDate1 = new Date(formValue.pumpOrderedDate);
    const startHours1 = new Date(formValue.pumpWorkStart).getHours();
    const startMinutes1 = new Date(formValue.pumpWorkStart).getMinutes();
    const placementStart1 = this.convertStart(placementDate1, startHours1, startMinutes1);
    const endHours1 = new Date(formValue.pumpWorkEnd).getHours();
    const endMinutes1 = new Date(formValue.pumpWorkEnd).getMinutes();
    const placementEnd1 = this.convertStart(placementDate1, endHours1, endMinutes1);
    if (this.checkConcreteRequestCreateFutureDate(placementStart, placementEnd)) {
      const index2 = this.getIndexData(formValue);
      if (index2 === -1) {
        if (index2 === -1) {
          this.errmemberenable = true;
        }
        this.formSubmitted = false;
      } else {
        if (index2 === -1) {
          this.errmemberenable = false;
        }
        this.checkRequestStatusAndUserRole(
          formValue,
          placementStart,
          placementEnd,
          persons,
          placementStart1,
          placementEnd1,
        );
      }
    } else {
      this.checkRequestStatusAndUserRole(
        formValue,
        placementStart,
        placementEnd,
        persons,
        placementStart1,
        placementEnd1,
      );
    }
  }

  public checkRequestStatusAndUserRole(
    formValue,
    placementStart,
    placementEnd,
    persons,
    placementStart1,
    placementEnd1,
  ): void {
    const isValid = this.validateRequestAndPermissions(
      formValue,
      placementStart,
      placementEnd,
      placementStart1,
      placementEnd1
    );

    if (!isValid) return;

    this.handlePlacementCreation(
      formValue,
      placementStart,
      placementEnd,
      persons,
      placementStart1,
      placementEnd1
    );
  }

  public validateRequestAndPermissions(
    formValue,
    placementStart,
    placementEnd,
    placementStart1,
    placementEnd1
  ): boolean {
    if (this.checkNewPlacementStartEndSame(placementStart, placementEnd)) {
      this.toastr.error('Placement start time and End time should not be the same');
      this.formSubmitted = false;
      return false;
    }

    if (formValue.isPumpRequired && this.checkNewPlacementStartEndSame(placementStart1, placementEnd1)) {
      this.toastr.error('Pump Start time and End time should not be the same');
      this.formSubmitted = false;
      return false;
    }

    if (this.currentEditItem.status === 'Completed' && this.authUser.RoleId !== 2) {
      const placementDateChanged = moment(this.currentEditItem.concretePlacementStart).format('MM/DD/YYYY')
        !== moment(this.concreteRequest.get('concretePlacementDate').value).format('MM/DD/YYYY');

      const startChanged = new Date(this.currentEditItem.concretePlacementStart).getTime()
        !== new Date(this.concreteRequest.get('concretePlacementStart').value).getTime();

      const endChanged = new Date(this.currentEditItem.concretePlacementEnd).getTime()
        !== new Date(this.concreteRequest.get('concretePlacementEnd').value).getTime();

      if (placementDateChanged || startChanged || endChanged) {
        this.toastr.error('You are not allowed to change the date/time ');
        this.formSubmitted = false;
        return false;
      }
    }

    if (
      this.currentEditItem.status === 'Approved'
      && this.authUser.RoleId !== 2
      && this.authUser.RoleId !== 3
    ) {
      const placementDateChanged = moment(this.currentEditItem.concretePlacementDate).format('MM/DD/YYYY')
        !== moment(this.concreteRequest.get('concretePlacementDate').value).format('MM/DD/YYYY');

      const startChanged = new Date(this.currentEditItem.concretePlacementStart).getTime()
        !== new Date(this.concreteRequest.get('concretePlacementStart').value).getTime();

      const endChanged = new Date(this.currentEditItem.concretePlacementEnd).getTime()
        !== new Date(this.concreteRequest.get('concretePlacementEnd').value).getTime();

      if (placementDateChanged || startChanged || endChanged) {
        if (!this.checkConcreteRequestCreateFutureDate(placementStart, placementEnd)) {
          this.submitted = false;
          this.formSubmitted = false;
          this.toastr.error(
            'Booking not allowed to edit. Please contact the project administrator to edit this booking',
          );
          return false;
        }
      }
    }

    return true;
  }

  public handlePlacementCreation(
    formValue,
    placementStart,
    placementEnd,
    persons,
    placementStart1,
    placementEnd1,
  ): void {
    const role = this.authUser.RoleId;

    if (role === 1 || role === 2 || role === 3) {
      this.createPlacement(formValue, placementStart, placementEnd, persons, placementStart1, placementEnd1);
    } else {
      if (formValue.isPumpRequired && !this.checkConcreteRequestCreateFutureDate(placementStart1, placementEnd1)) {
        this.submitted = false;
        this.formSubmitted = false;
        this.toastr.error('Please Enter Valid Pump Ordered Date.');
        return;
      }

      if (this.checkConcreteRequestCreateFutureDate(placementStart, placementEnd)) {
        this.createPlacement(formValue, placementStart, placementEnd, persons, placementStart1, placementEnd1);
      } else {
        this.toastr.error(
          'Booking not allowed to edit. Please contact the project administrator to edit this booking',
        );
        this.submitted = false;
        this.formSubmitted = false;
      }
    }
  }


  public setVehicleType(): void {
    if (this.currentEditItem.vehicleType) {
      this.vehicleTypeChosen = [];
      const getVehicleChosen = this.vehicleTypes.find((obj: any): any => obj.type === this.currentEditItem.vehicleType);
      const data = {
        id: getVehicleChosen.id,
        type: getVehicleChosen.type,
      };
      this.vehicleTypeChosen.push(data);
      this.concreteRequest.get('vehicleType').patchValue(this.vehicleTypeChosen);
      this.selectedVehicleType = getVehicleChosen?.type;
    }
  }

  public setPumpVehicleType(): void {
    if (this.currentEditItem.vehicleTypePump) {
      this.vehicleTypeChosen = [];
      const getVehicleChosen = this.vehicleTypes.find((obj: any): any => obj.type === this.currentEditItem.vehicleTypePump);
      const data = {
        id: getVehicleChosen.id,
        type: getVehicleChosen.type,
      };
      this.vehicleTypeChosen.push(data);
      this.concreteRequest.get('vehicleTypePump').patchValue(this.vehicleTypeChosen);
      this.selectedPumpVehicleType = getVehicleChosen?.type;
    }
  }

  public checkNewPlacementStartEnd(newDeliveryStart, newDeliveryEnd): boolean {
    const newStartDate = new Date(newDeliveryStart).getTime();
    const newEndDate = new Date(newDeliveryEnd).getTime();
    if (newStartDate < newEndDate) {
      return true;
    }
    return false;
  }

  public createPlacement(
    formValue,
    placementStart,
    placementEnd,
    responsbilePersonsData,
    placementStart1,
    placementEnd1
  ): void {
    if (!this.validatePlacementTimes(formValue, placementStart, placementEnd, placementStart1, placementEnd1)) {
      return;
    }

    const {
      concreteSupplier,
      equipments,
      mixDesign,
      pumpSize,
      isValid,
    } = this.prepareFormData(formValue, responsbilePersonsData);

    if (!isValid) {
      return;
    }

    this.submitConcreteRequest({
      formValue,
      placementStart,
      placementEnd,
      placementStart1,
      placementEnd1,
      responsbilePersonsData,
      concreteSupplier,
      equipments,
      mixDesign,
      pumpSize,
    });
  }

  public validatePlacementTimes(formValue, placementStart, placementEnd, placementStart1, placementEnd1): boolean {
    if (!this.checkNewPlacementStartEnd(placementStart, placementEnd)) {
      this.submitted = false;
      this.formSubmitted = false;
      this.toastr.error('Please enter placement start time lesser than end time');
      return false;
    }

    if (formValue.isPumpRequired && !this.checkNewPlacementStartEnd(placementStart1, placementEnd1)) {
      this.submitted = false;
      this.formSubmitted = false;
      this.toastr.error('Please enter pump start time lesser than end time');
      return false;
    }

    return true;
  }

  public prepareFormData(formValue, responsbilePersonsData): {
    concreteSupplier: any[];
    equipments: any[];
    mixDesign: any[];
    pumpSize: any[];
    isValid: boolean;
  } {
    const concreteSupplier = [];
    const equipments = [];
    const mixDesign = [];
    const pumpSize = [];

    if (!formValue.responsiblePersons || formValue.responsiblePersons.length === 0) {
      this.formReset();
      this.toastr.error('Responsible Person is required');
      return {
        concreteSupplier, equipments, mixDesign, pumpSize, isValid: false
      };
    }

    formValue.responsiblePersons.forEach((el) => responsbilePersonsData.push(el.id));
    formValue.concreteSupplier?.forEach((el) => concreteSupplier.push(el.id));
    formValue.EquipmentId?.forEach((el) => equipments.push(el.id));

    formValue.mixDesign?.forEach((el) => {
      mixDesign.push(Number(el.id) && el.id !== el.mixDesign
        ? { id: el.id, mixDesign: el.mixDesign, chosenFromDropdown: true }
        : { id: null, mixDesign: el.mixDesign, chosenFromDropdown: false });
    });

    formValue.pumpSize?.forEach((el) => {
      pumpSize.push(Number(el.id) && el.id !== el.pumpSize
        ? { id: el.id, pumpSize: el.pumpSize, chosenFromDropdown: true }
        : { id: null, pumpSize: el.pumpSize, chosenFromDropdown: false });
    });

    if (this.checkStringEmptyValues(formValue)) {
      this.formReset();
      return {
        concreteSupplier, equipments, mixDesign, pumpSize, isValid: false,
      };
    }

    return {
      concreteSupplier, equipments, mixDesign, pumpSize, isValid: true,
    };
  }

  public submitConcreteRequest({
    formValue,
    placementStart,
    placementEnd,
    placementStart1,
    placementEnd1,
    responsbilePersonsData,
    concreteSupplier,
    equipments,
    mixDesign,
    pumpSize,
  }): void {
    const isPumpConfirmed = formValue.isPumpConfirmed ?? false;
    const isPumpRequired = formValue.isPumpRequired ?? false;
    const isConcreteConfirmed = formValue.isConcreteConfirmed ?? false;
    const pumpRequired = this.concreteRequest.get('isPumpRequired').value === true;

    const payload = {
      id: formValue.id,
      location: formValue.location,
      LocationDetailId: formValue.LocationDetailId,
      description: formValue.description,
      LocationId: this.selectedLocationId,
      GateId: formValue.GateId,
      EquipmentId: equipments,
      ProjectId: this.ProjectId,
      concreteSupplier,
      notes: formValue.notes,
      concretePlacementStart: placementStart,
      concretePlacementEnd: placementEnd,
      isPumpConfirmed: pumpRequired ? isPumpConfirmed : false,
      isPumpRequired,
      isConcreteConfirmed,
      ParentCompanyId: this.ParentCompanyId,
      responsiblePersons: responsbilePersonsData,
      concreteOrderNumber: formValue.concreteOrderNumber,
      truckSpacingHours: formValue.truckSpacingHours,
      mixDesign,
      slump: formValue.slump,
      concreteQuantityOrdered: formValue.concreteQuantityOrdered,
      concreteConfirmedOn: formValue.concreteConfirmedOn,
      pumpSize: pumpRequired ? pumpSize : [],
      pumpLocation: pumpRequired ? formValue.pumpLocation : null,
      pumpOrderedDate: pumpRequired ? formValue.pumpOrderedDate : null,
      pumpWorkStart: pumpRequired ? placementStart1 : null,
      pumpWorkEnd: pumpRequired ? placementEnd1 : null,
      pumpConfirmedOn: formValue.pumpConfirmedOn,
      cubicYardsTotal: formValue.cubicYardsTotal,
      hoursToCompletePlacement: formValue.hoursToCompletePlacement,
      minutesToCompletePlacement: formValue.minutesToCompletePlacement,
      requestType: 'concreteRequest',
      primerForPump: formValue.primerForPump,
      ConcreteRequestId: formValue.ConcreteRequestId,
      seriesOption: this.seriesOption,
      recurrenceId: formValue.recurrenceId,
      recurrenceEndDate: formValue.recurrenceEndDate
        ? moment(formValue.recurrenceEndDate).format('YYYY-MM-DD')
        : null,
      recurrenceSeriesStartDate: moment(formValue.concretePlacementStart).format('YYYY-MM-DD'),
      recurrenceSeriesEndDate: moment(formValue.concretePlacementEnd).format('YYYY-MM-DD'),
      previousSeriesRecurrenceEndDate: moment(formValue.concretePlacementStart).add(-1, 'days').format('YYYY-MM-DD'),
      nextSeriesRecurrenceStartDate: moment(formValue.concretePlacementStart).add(1, 'days').format('YYYY-MM-DD'),
      deliveryStartTime: moment(formValue.concretePlacementStart).format('HH:mm'),
      deliveryEndTime: moment(formValue.concretePlacementEnd).format('HH:mm'),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      originationAddress: formValue.originationAddress,
      vehicleType: this.selectedVehicleType,
      originationAddressPump: formValue.originationAddressPump,
      vehicleTypePump: this.selectedPumpVehicleType,
      recurrence: formValue.recurrence,
      chosenDateOfMonthValue: formValue.chosenDateOfMonth !== false ? formValue.chosenDateOfMonth : 1,
      chosenDateOfMonth: formValue.chosenDateOfMonth === 1,
      dateOfMonth: formValue.dateOfMonth,
      monthlyRepeatType: formValue.monthlyRepeatType,
      days: this.payloadDays,
      repeatEveryType: formValue.repeatEveryType || null,
      repeatEveryCount: formValue.repeatEveryCount?.toString() || null,
      recurrenceEdited: this.recurrenceEdited,
    };

    this.createConcreteRequest(payload);
  }



  public checkStringEmptyValues(formValue): boolean {
    if (formValue.description.trim() === '') {
      this.toastr.error('Please enter valid description', 'OOPS!');
      return true;
    }

    if (formValue.isPumpRequired && formValue.pumpLocation.trim() === '') {
      this.toastr.error('Please enter valid pump location', 'OOPS!');
      return true;
    }
    if (formValue.cubicYardsTotal && formValue.cubicYardsTotal.trim() === '') {
      this.toastr.error('Please enter valid number of cubic yards', 'OOPS!');
      return true;
    }
    return false;
  }

  public throwEndError(): void {
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error('Please Enter Start time Lesser than End time');
  }

  public formReset(): void {
    this.formSubmitted = false;
    this.submitted = false;
  }

  public checkConcreteRequestCreateFutureDate(start, end): boolean {
    const newDeliveryStartDate = moment(new Date(start));
    const newDeliveryCurrentDate = moment()
      .clone()
      .add(this.deliveryWindowTime, this.deliveryWindowTimeUnit);
    const newDeliveryEndDate = moment(new Date(end));
    if (
      newDeliveryStartDate.isAfter(newDeliveryCurrentDate)
      && newDeliveryEndDate.isAfter(newDeliveryCurrentDate)
    ) {
      return true;
    }
    return false;
  }

  public convertStart(placementDate, startHours, startMinutes): string {
    const fullYear = placementDate.getFullYear();
    const fullMonth = placementDate.getMonth();
    const date = placementDate.getDate();
    const placementNewStart = new Date(fullYear, fullMonth, date, startHours, startMinutes);
    const placementStart = placementNewStart.toUTCString();
    return placementStart;
  }

  public checkNewPlacementStartEndSame(newPlacementStartTime, newPlacementEndTime): boolean {
    const startDate = new Date(newPlacementStartTime).getTime();
    const endDate = new Date(newPlacementEndTime).getTime();
    if (startDate === endDate) {
      return true;
    }
    return false;
  }

  public resetForm(action): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      if (this.modalRef) {
        this.modalRef.hide();
      }
      this.concreteRequest.reset();
      this.NDRTimingChanged = false;
      this.formSubmitted = false;
      this.submitted = false;
      this.formEdited = true;
      this.isEditMode = false;
      this.getDropdownValues();
    }
  }

  public concreteRequestCreationForm(): any {
    this.concreteRequest = this.formBuilder.group({
      id: [''],
      ConcreteRequestId: [''],
      location: [''],
      LocationId: ['', Validators.compose([Validators.required])],
      EquipmentId: ['', Validators.compose([Validators.required])],
      GateId: ['', Validators.compose([Validators.required])],
      LocationDetailId: ['', Validators.compose([Validators.required])],
      responsiblePersons: [''],
      description: ['', Validators.compose([Validators.required])],
      concreteSupplier: ['', Validators.compose([Validators.required])],
      concreteOrderNumber: [''],
      truckSpacingHours: [''],
      notes: [''],
      primerForPump: [''],
      mixDesign: [''],
      slump: [''],
      concreteQuantityOrdered: [''],
      concreteConfirmedOn: [''],
      isConcreteConfirmed: ['', Validators.compose([Validators.required])],
      pumpSize: [''],
      pumpLocation: [''],
      pumpOrderedDate: [''],
      pumpWorkStart: [''],
      pumpWorkEnd: [''],
      pumpConfirmedOn: [''],
      isPumpConfirmed: [''],
      isPumpRequired: ['', Validators.compose([Validators.required])],
      cubicYardsTotal: [''],
      hoursToCompletePlacement: [''],
      minutesToCompletePlacement: [''],
      concretePlacementDate: ['', Validators.compose([Validators.required])],
      concretePlacementStart: ['', Validators.compose([Validators.required])],
      concretePlacementEnd: ['', Validators.compose([Validators.required])],
      recurrenceId: [''],
      recurrence: [''],
      recurrenceEndDate: [''],
      originationAddress: [''],
      vehicleType: [''],
      originationAddressPump: [''],
      vehicleTypePump: [''],
      repeatEveryCount: [''],
      repeatEveryType: [''],
      days: new UntypedFormArray([]),
      chosenDateOfMonth: [false, ''],
      dateOfMonth: [''],
      monthlyRepeatType: [''],
      endDate: [''],
    });
    this.formControlValueChanged();
    this.formControlValueChanged1();
  }

  public requestAutoEditcompleteItems = (text: string): Observable<any> => {
    const param = {
      ProjectId: this.ProjectId,
      search: text,
      ParentCompanyId: this.ParentCompanyId,
    };
    return this.deliveryService.searchNewMember(param);
  };

  public formControlValueChanged(): any {
    const pumpSize = this.concreteRequest.get('pumpSize');
    const pumpLocation = this.concreteRequest.get('pumpLocation');
    const pumpOrderedDate = this.concreteRequest.get('pumpOrderedDate');
    const pumpWorkStart = this.concreteRequest.get('pumpWorkStart');
    const pumpWorkEnd = this.concreteRequest.get('pumpWorkEnd');
    this.concreteRequest.get('isPumpRequired').valueChanges.subscribe((value: boolean): any => {
      if (value === true) {
        pumpSize.setValidators([Validators.required]);
        pumpLocation.setValidators([Validators.required]);
        pumpOrderedDate.setValidators([Validators.required]);
        pumpWorkStart.setValidators([Validators.required]);
        pumpWorkEnd.setValidators([Validators.required]);
        this.concreteRequest.get('pumpWorkStart').setValue(new Date(this.pumpWorkStart));
        this.concreteRequest.get('pumpWorkEnd').setValue(new Date(this.pumpWorkEnd));
        this.concreteRequest
          .get('pumpOrderedDate')
          .setValue(
            moment(this.concreteRequest.get('concretePlacementDate').value).format('MM/DD/YYYY'),
          );
      } else if (value === false || value === null) {
        pumpSize.clearValidators();
        pumpLocation.clearValidators();
        pumpOrderedDate.clearValidators();
        pumpWorkStart.clearValidators();
        pumpWorkEnd.clearValidators();
      }
      pumpSize.updateValueAndValidity();
      pumpLocation.updateValueAndValidity();
      pumpOrderedDate.updateValueAndValidity();
      pumpWorkStart.updateValueAndValidity();
      pumpWorkEnd.updateValueAndValidity();
    });
    this.concreteRequest.get('concretePlacementDate').valueChanges.subscribe((value: any): void => {
      this.concreteRequest
        .get('pumpOrderedDate')
        .setValue(
          moment(this.concreteRequest.get('concretePlacementDate').value).format('MM/DD/YYYY'),
        );
    });
  }

  public ngAfterViewInit(): void {
    this.seriesOption = this.modalRef?.content?.seriesOption?this.modalRef.content.seriesOption:this.seriesoption;
    this.isDisabledDate = this.seriesOption !== 1;
  }

  public getConcreteRequestData(): void {
    this.loader = true;
    this.currentEditItem = {};
    this.isEditMode = true;
    const param = {
      ConcreteRequestId: this.concreteRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    this.deliveryService.getConcreteRequestDetail(param).subscribe((res): void => {
      this.currentEditItem = res.data;
      this.concreteRequest.get('id').setValue(this.currentEditItem.id);
      this.concreteRequest
        .get('ConcreteRequestId')
        .setValue(this.currentEditItem.ConcreteRequestId);
      this.concreteRequest.get('description').setValue(this.currentEditItem.description);
      this.concreteRequest.get('LocationDetailId').setValue(this.currentEditItem.locationDetails[0].ConcreteLocation?.id);
      this.concreteRequest.get('location').setValue(this.currentEditItem.locationDetails[0].ConcreteLocation?.location);
      this.concreteRequest.get('notes').setValue(this.currentEditItem.notes);
      this.concreteRequest
        .get('isPumpConfirmed')
        .setValue(
          this.currentEditItem.isPumpConfirmed ? this.currentEditItem.isPumpConfirmed : null,
        );
      this.concreteRequest
        .get('concretePlacementDate')
        .setValue(moment(this.currentEditItem.concretePlacementStart).format('MM/DD/YYYY'));
      this.concreteRequest
        .get('concretePlacementStart')
        .setValue(new Date(this.currentEditItem.concretePlacementStart));
      this.concreteRequest
        .get('concretePlacementEnd')
        .setValue(new Date(this.currentEditItem.concretePlacementEnd));
      this.concreteRequest.get('isPumpRequired').setValue(this.currentEditItem.isPumpRequired);
      this.concreteRequest
        .get('isConcreteConfirmed')
        .setValue(this.currentEditItem.isConcreteConfirmed);
      this.concreteRequest
        .get('concreteOrderNumber')
        .setValue(this.currentEditItem.concreteOrderNumber);
      this.concreteRequest
        .get('truckSpacingHours')
        .setValue(this.currentEditItem.truckSpacingHours);
      this.concreteRequest.get('mixDesign').setValue(this.currentEditItem.mixDesign);
      this.concreteRequest.get('slump').setValue(this.currentEditItem.slump);
      this.concreteRequest
        .get('concreteQuantityOrdered')
        .setValue(this.currentEditItem.concreteQuantityOrdered);
      this.concreteRequest.get('primerForPump').setValue(this.currentEditItem.primerForPump);
      this.concreteRequest
        .get('concreteConfirmedOn')
        .setValue(this.currentEditItem.concreteConfirmedOn);
      this.concreteRequest.get('pumpSize').setValue(this.currentEditItem.pumpSize);
      this.concreteRequest.get('pumpLocation').setValue(this.currentEditItem.pumpLocation);
      this.concreteRequest
        .get('pumpOrderedDate')
        .setValue(moment(this.currentEditItem.pumpOrderedDate).format('MM/DD/YYYY'));
      this.concreteRequest
        .get('pumpWorkStart')
        .setValue(new Date(this.currentEditItem.pumpWorkStart));
      this.concreteRequest.get('pumpWorkEnd').setValue(new Date(this.currentEditItem.pumpWorkEnd));
      this.concreteRequest.get('pumpConfirmedOn').setValue(this.currentEditItem.pumpConfirmedOn);
      this.concreteRequest.get('cubicYardsTotal').setValue(this.currentEditItem.cubicYardsTotal);
      this.concreteRequest
        .get('hoursToCompletePlacement')
        .setValue(this.currentEditItem.hoursToCompletePlacement);
      this.concreteRequest
        .get('minutesToCompletePlacement')
        .setValue(this.currentEditItem.minutesToCompletePlacement);
      this.concreteRequest.get('recurrenceId').setValue(this.currentEditItem?.recurrence?.id);
      this.concreteRequest.get('GateId').setValue(this.currentEditItem.gateDetails[0]?.Gate?.id);
      this.concreteRequest.get('recurrence').setValue(this.currentEditItem?.recurrence?.recurrence);
      this.concreteRequest.get('originationAddress').setValue(this.currentEditItem?.OriginationAddress);
      this.concreteRequest.get('originationAddressPump').setValue(this.currentEditItem?.OriginationAddressPump);

      if (this.currentEditItem?.recurrence?.recurrenceEndDate) {
        this.concreteRequest
          .get('recurrenceEndDate')
          .setValue(
            new Date(this.currentEditItem?.recurrence?.recurrenceEndDate),
          );
      }
      this.minDateOfrecurrenceEndDate = new Date(
        this.currentEditItem?.recurrence?.recurrenceEndDate,
      );
      this.previousEndDate = this.concreteRequest.get('concretePlacementEnd').value;
      this.previousRecurrence = this.concreteRequest.get('recurrence').value;
      this.selectedRecurrence = this.concreteRequest.get('recurrence').value;
      this.concreteRequest.get('repeatEveryCount').setValue(+this.currentEditItem?.recurrence?.repeatEveryCount);
      this.previousRepeatEveryCount = this.concreteRequest.get('repeatEveryCount').value;
      this.concreteRequest.get('repeatEveryType').setValue(this.currentEditItem?.recurrence?.repeatEveryType);
      this.previousRepeatEveryType = this.concreteRequest.get('repeatEveryType').value;
      this.concreteRequest.get('chosenDateOfMonth').setValue(this.currentEditItem?.recurrence?.chosenDateOfMonthValue);
      this.previousChosenDateOfMonth = this.currentEditItem?.recurrence?.chosenDateOfMonth;
      // eslint-disable-next-line max-len
      this.previousChosenDateOfMonthValue = this.currentEditItem?.recurrence?.chosenDateOfMonthValue;
      this.concreteRequest.get('dateOfMonth').setValue(this.currentEditItem?.recurrence?.dateOfMonth);
      this.previousDateOfMonth = this.concreteRequest.get('dateOfMonth').value;
      this.concreteRequest.get('monthlyRepeatType').setValue(this.currentEditItem?.recurrence?.monthlyRepeatType);
      this.previousMonthlyRepeatType = this.concreteRequest.get('monthlyRepeatType').value;
      this.previousDays = this.currentEditItem?.recurrence?.days;
      this.setDropdownValues();
      this.setVehicleType();
      this.setEquipment();
      this.setPumpVehicleType();
      this.setRepeat(this.currentEditItem?.recurrence);
      this.seriesOption = this.modalRef?.content?.seriesOption?this.modalRef.content.seriesOption:this.seriesoption;
      this.getBookingData()

    });
  }

  public openContentModal(): void {
    this.modalLoader = false;
  }

  public setDropdownValues(): void {
    const {
      memberDetails,
      location,
      concreteSupplierDetails,
      pumpSizeDetails,
      mixDesignDetails,
    } = this.currentEditItem;
    const newMemberList = [];
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.getMemberRole(params).subscribe((res): void => {
      this.authUser = res.data;
      if (memberDetails !== undefined) {
        memberDetails.forEach(
          (element: {
            Member: { User: { firstName: any; lastName: any; email: any }; id: any; isGuestUser: any };
          }): void => {
            let email: string;
            if (element.Member?.User?.firstName != null) {
              const { firstName, lastName, email: userEmail } = element.Member.User;
              const isGuest = element.Member.isGuestUser;

              if (isGuest) {
                email = `${firstName} ${lastName} (${userEmail} - Guest)`;
              } else {
                email = `${firstName} ${lastName} (${userEmail})`;
              }
            }
            const data: any = {
              email,
              id: element.Member?.id,
            };
            if (element.Member?.User?.email === this.authUser?.User?.email) {
              data.readonly = true;
            }
            if (element.Member?.User?.email) {
              newMemberList.push(data);
            }
          },
        );
      }
    });
    this.concreteRequest.get('responsiblePersons').patchValue(newMemberList);

    if (location) {
      this.getChosenLocation = [];
      this.getChosenLocation.push({
        id: location.id,
        locationPath: location.locationPath,
      });
      this.concreteRequest.get('LocationId').setValue(this.getChosenLocation);
      this.selectedLocationId = location.id;
    }
    const mixDesignTags = [];
    mixDesignDetails.forEach((element): void => {
      if (element?.ConcreteMixDesign?.mixDesign) {
        mixDesignTags.push({
          id: element.ConcreteMixDesign.id,
          mixDesign: element.ConcreteMixDesign.mixDesign,
        });
      }
    });
    this.editBeforeMixDesigns = mixDesignTags;
    this.concreteRequest.get('mixDesign').patchValue(mixDesignTags);
    this.mixDesignList = mixDesignTags;
    const pumpSizeTags = [];
    pumpSizeDetails.forEach((element): void => {
      if (element?.ConcretePumpSize?.pumpSize) {
        pumpSizeTags.push({
          id: element.ConcretePumpSize.id,
          pumpSize: element.ConcretePumpSize.pumpSize,
        });
      }
    });
    this.editBeforePumpsize = pumpSizeTags;
    this.concreteRequest.get('pumpSize').patchValue(pumpSizeTags);
    this.pumpSizeList = pumpSizeTags;
    const newCompanyList = [];
    concreteSupplierDetails.forEach((element): void => {
      const data = {
        id: element.Company.id,
        companyName: element.Company.companyName,
      };
      newCompanyList.push(data);
    });
    this.editBeforeCompany = newCompanyList;
    this.concreteRequest.get('concreteSupplier').patchValue(newCompanyList);
    this.openContentModal();
    this.seriesOption = this.modalRef?.content?.seriesOption;
    this.isDisabledDate = this.seriesOption !== 1;
  }

  public deliveryEndTimeChangeDetection(): void {
    this.NDRTimingChanged = true;
    this.onEditSubmitForm();
  }

  public changeConcreteConfirmed(data): void {
    if (data) {
      this.concreteRequest.get('isConcreteConfirmed').setValue(true);
      this.concreteRequest.get('concreteConfirmedOn').setValue(new Date());
    } else {
      this.concreteRequest.get('isConcreteConfirmed').setValue(null);
      this.concreteRequest.get('concreteConfirmedOn').setValue(null);
    }
  }

  public changePumpConfirmed(data): void {
    if (data) {
      this.concreteRequest.get('isPumpConfirmed').setValue(true);
      this.concreteRequest.get('pumpConfirmedOn').setValue(new Date());
    } else {
      this.concreteRequest.get('isPumpConfirmed').setValue(null);
      this.concreteRequest.get('pumpConfirmedOn').setValue(null);
    }
  }

  public repeatEveryMessage(value) {
    if (value === 'Does Not Repeat') {
      this.concreteRequest.get('repeatEveryType').setValue('');
    } else {
      this.concreteRequest.get('repeatEveryCount').setValue(1);
    }
    if (value === 'Daily') {
      this.concreteRequest.get('repeatEveryType').setValue('Day');
    }
    if (value === 'Weekly') {
      this.concreteRequest.get('repeatEveryType').setValue('Week');
    }
    if (value === 'Monthly') {
      this.concreteRequest.get('repeatEveryType').setValue('Month');
    }
    if (value === 'Yearly') {
      this.concreteRequest.get('repeatEveryType').setValue('Year');
    }
  }

  public onRecurrenceSelect(value: string): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    this.selectedRecurrence = value;

    if (this.concreteRequest.get('repeatEveryCount').value > 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.checkform = this.concreteRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day11: any): void => {
        const dayObj11 = day11;
        dayObj11.checked = true;
        dayObj11.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj11.value));
        return dayObj11;
      });
    }
    if (this.concreteRequest.get('repeatEveryCount').value > 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.weekDays = this.weekDays.map((day12: any): void => {
        const dayObj12 = day12;
        if (dayObj12.value === 'Monday') {
          dayObj12.checked = true;
        } else {
          dayObj12.checked = false;
        }
        dayObj12.isDisabled = false;
        return dayObj12;
      });
      this.checkform = this.concreteRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.concreteRequest.get('repeatEveryCount').value === 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.weekDays = this.weekDays.map((day13: any): void => {
        const dayObj13 = day13;
        if (dayObj13.value === 'Monday') {
          dayObj13.checked = true;
        } else {
          dayObj13.checked = false;
        }
        dayObj13.isDisabled = false;
        return dayObj13;
      });
      this.checkform = this.concreteRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.concreteRequest.get('repeatEveryCount').value === 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.checkform = this.concreteRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day14: any): void => {
        const dayObj14 = day14;
        dayObj14.checked = true;
        dayObj14.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj14.value));
        return dayObj14;
      });
    }
    if (
      this.concreteRequest.get('repeatEveryCount').value === 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.concreteRequest.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showMonthlyRecurrence();
    }
    if (
      this.concreteRequest.get('repeatEveryCount').value > 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.concreteRequest.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.showMonthlyRecurrence();
    }
    this.occurMessage();
    this.onEditSubmitForm();
  }

  public changeRecurrenceCount(value): void {
    if (value > 0) {
      this.updateRecurrenceUIState(value);
      this.updateRecurrenceFormValues(value);
      this.selectedRecurrence = this.concreteRequest.get('recurrence').value;
      this.occurMessage();
    } else if (value < 0) {
      this.concreteRequest.get('repeatEveryCount').setValue(1);
    }
  }

  public updateRecurrenceUIState(value: number): void {
    const recurrencedata = this.concreteRequest.get('recurrence').value;

    if (recurrencedata === 'Daily' || recurrencedata === 'Weekly' || recurrencedata === 'Monthly' || recurrencedata === 'Yearly') {
      if (+value === 1) {
        this.isRepeatWithSingleRecurrence = true;
        this.isRepeatWithMultipleRecurrence = false;
        this.showRecurrenceTypeDropdown = false;
      } else {
        this.isRepeatWithSingleRecurrence = false;
        this.isRepeatWithMultipleRecurrence = recurrencedata !== 'Daily';
        this.showRecurrenceTypeDropdown = recurrencedata === 'Daily';
      }
    }
  }


  public updateRecurrenceFormValues(value: number): void {
    const recurrencedata = this.concreteRequest.get('recurrence').value;

    switch (recurrencedata) {
      case 'Daily':
        this.concreteRequest.get('repeatEveryType').setValue(+value > 1 ? 'Days' : 'Day');
        break;

      case 'Weekly':
        this.concreteRequest.get('repeatEveryType').setValue(+value > 1 ? 'Weeks' : 'Week');
        break;

      case 'Monthly':
        this.concreteRequest.get('repeatEveryType').setValue(+value > 1 ? 'Months' : 'Month');
        this.changeMonthlyRecurrence();
        this.showMonthlyRecurrence();
        break;

      case 'Yearly':
        this.concreteRequest.get('repeatEveryType').setValue(+value > 1 ? 'Years' : 'Year');
        break;

      default:
        break;
    }
  }

  public chooseRepeatEveryType(value: string, eventDetail: any): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    if (value === 'Day' || value === 'Days') {
      this.concreteRequest.get('recurrence').setValue('Daily');
    }
    if (value === 'Week' || value === 'Weeks') {
      this.concreteRequest.get('recurrence').setValue('Weekly');
    }
    if (value === 'Month' || value === 'Months') {
      this.concreteRequest.get('recurrence').setValue('Monthly');
    }
    if (value === 'Year' || value === 'Years') {
      this.concreteRequest.get('recurrence').setValue('Yearly');
    }
    if (value === 'Day' || value === 'Days') {
      this.checkform = this.concreteRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day: any): any => {
        const dayObj = day;
        dayObj.checked = true;
        dayObj.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj.value));
        return dayObj;
      });
    }
    if (value === 'Week' || value === 'Weeks') {
      if (eventDetail?.days?.length > 0) {
        this.checkform = this.concreteRequest.get('days') as UntypedFormArray;
        this.checkform.controls = [];
        this.weekDays = this.weekDays.map((day1: any): void => {
          const dayObj1 = day1;
          if (eventDetail.days.includes(dayObj1.value)) {
            dayObj1.checked = true;
            this.checkform.push(new UntypedFormControl(dayObj1.value));
          } else {
            dayObj1.checked = false;
          }
          dayObj1.isDisabled = false;
          return dayObj1;
        });
      } else {
        this.weekDays = this.weekDays.map((day2: any): void => {
          const dayObj2 = day2;
          if (dayObj2.value === 'Monday') {
            dayObj2.checked = true;
          } else {
            dayObj2.checked = false;
          }
          dayObj2.isDisabled = false;
          return dayObj2;
        });
        this.checkform = this.concreteRequest.get('days') as UntypedFormArray;
        this.checkform.controls = [];
        this.checkform.push(new UntypedFormControl('Monday'));
      }
    }
    if (value === 'Day' || value === 'Week' || value === 'Month' || value === 'Year') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showRecurrenceTypeDropdown = false;
    }
    if (value === 'Days' || value === 'Weeks' || value === 'Months' || value === 'Years') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.showRecurrenceTypeDropdown = false;
    }
    if (this.concreteRequest.get('repeatEveryCount').value > 1) {
      this.showRecurrenceTypeDropdown = true;
      this.isRepeatWithMultipleRecurrence = false;
    }
    this.selectedRecurrence = this.concreteRequest.get('recurrence').value;
    this.occurMessage();
    this.onEditSubmitForm();
  }

  public changeMonthlyRecurrence(): void {
    this.setMonthlyOrYearlyRecurrenceOption();
    this.updateFormValidation();
    this.showMonthlyRecurrence();
    this.occurMessage();
    this.onEditSubmitForm();
  }

  public setMonthlyOrYearlyRecurrenceOption(): void {
    if (this.concreteRequest.get('chosenDateOfMonth').value === 1) {
      this.concreteRequest
        .get('dateOfMonth')
        .setValue(moment(this.concreteRequest.get('concretePlacementDate').value).format('DD'));
      this.concreteRequest.get('monthlyRepeatType').setValue(null);
    } else if (this.concreteRequest.get('chosenDateOfMonth').value === 2) {
      this.concreteRequest.get('dateOfMonth').setValue(null);
      this.concreteRequest.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
    } else if (this.concreteRequest.get('chosenDateOfMonth').value === 3) {
      this.concreteRequest.get('dateOfMonth').setValue(null);
      this.concreteRequest.get('monthlyRepeatType').setValue(this.monthlyLastDayOfWeek);
    }
  }

  public updateFormValidation(): void {
    const chosenDateOfMonth = this.concreteRequest.get('chosenDateOfMonth');
    const dateOfMonth = this.concreteRequest.get('dateOfMonth');
    const monthlyRepeatType = this.concreteRequest.get('monthlyRepeatType');
    if (this.concreteRequest.get('chosenDateOfMonth').value === 1) {
      dateOfMonth.setValidators([Validators.required]);
      monthlyRepeatType.clearValidators();
    } else {
      monthlyRepeatType.setValidators([Validators.required]);
      dateOfMonth.clearValidators();
    }
    chosenDateOfMonth.updateValueAndValidity();
    dateOfMonth.updateValueAndValidity();
    monthlyRepeatType.updateValueAndValidity();
  }

  public showMonthlyRecurrence(): void {
    if (this.concreteRequest.get('concretePlacementDate').value) {
      const startDate = moment(this.concreteRequest.get('concretePlacementDate').value).format('YYYY-MM');
      const chosenDay = moment(this.concreteRequest.get('concretePlacementDate').value).format('dddd');
      this.monthlyDate = moment(this.concreteRequest.get('concretePlacementDate').value).format('DD');
      const day = moment(startDate, 'YYYY-MM').startOf('month').day(chosenDay);
      const getAllDays = [];
      if (day.date() > 7) day.add(7, 'd');
      const month = day.month();
      while (month === day.month()) {
        getAllDays.push(day.format('YYYY-MM-DD'));
        day.add(7, 'd');
      }
      let week: string;
      let extraOption: string;
      this.enableOption = false;
      getAllDays.forEach((element, i): void => {
        if (
          moment(this.concreteRequest.get('concretePlacementDate').value).format('YYYY-MM-DD')
          === moment(element).format('YYYY-MM-DD')
        ) {
          const number = i + 1;
          if (number === 1) {
            week = 'First';
          }
          if (number === 2) {
            week = 'Second';
          }
          if (number === 3) {
            week = 'Third';
          }
          if (number === 4) {
            this.enableOption = true;
            extraOption = 'Last';
            week = 'Fourth';
          }
          if (number === 5) {
            week = 'Last';
          }
          if (number === 6) {
            week = 'Last';
          }
        }
      });
      this.monthlyDayOfWeek = `${week} ${chosenDay}`;
      this.monthlyLastDayOfWeek = `${extraOption} ${chosenDay}`;
      if (!this.enableOption && this.concreteRequest.get('chosenDateOfMonth').value === 3) {
        this.concreteRequest.get('chosenDateOfMonth').setValue(2);
        this.concreteRequest.get('dateOfMonth').setValue(null);
        this.concreteRequest.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
      }
      this.setMonthlyOrYearlyRecurrenceOption();
      this.occurMessage();
      this.onEditSubmitForm();
    }
  }


  public repeatOccurMessage() {
    if (this.concreteRequest.get('repeatEveryType').value === 'Day') {
      this.message = 'Occurs every day';
    }
    if (this.concreteRequest.get('repeatEveryType').value === 'Days') {
      if (+this.concreteRequest.get('repeatEveryCount').value === 2) {
        this.message = 'Occurs every other day';
      } else {
        this.message = `Occurs every ${this.concreteRequest.get('repeatEveryCount').value} days`;
      }
    }
  }

  public occurMessage(): void {
    this.message = '';
    this.repeatOccurMessage();
    if (this.concreteRequest.get('repeatEveryType').value === 'Week') {
      let weekDays = '';
      this.weekDays.map((dayObj1: any): any => {
        if (dayObj1.checked) {
          weekDays = `${weekDays + dayObj1.value},`;
        }
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message += `Occurs every ${weekDays}`;
    }
    if (this.concreteRequest.get('repeatEveryType').value === 'Weeks') {
      let weekDays = '';
      this.weekDays.map((dayObj2: any): any => {
        if (dayObj2.checked) {
          weekDays = `${weekDays + dayObj2.value},`;
        }
        return false;
      });
      if (+this.concreteRequest.get('repeatEveryCount').value === 2) {
        this.message = `Occurs every other  ${weekDays}`;
      } else {
        this.message = `Occurs every ${
          this.concreteRequest.get('repeatEveryCount').value
        } weeks on ${weekDays}`;
      }
      weekDays = weekDays.replace(/,\s*$/, '');
    }
    if (
      this.concreteRequest.get('repeatEveryType').value === 'Month'
      || this.concreteRequest.get('repeatEveryType').value === 'Months'
      || this.concreteRequest.get('repeatEveryType').value === 'Year'
      || this.concreteRequest.get('repeatEveryType').value === 'Years'
    ) {
      if (this.concreteRequest.get('chosenDateOfMonth').value === 1) {
        this.message = `Occurs on day ${this.monthlyDate}`;
      } else if (this.concreteRequest.get('chosenDateOfMonth').value === 2) {
        this.message = `Occurs on the ${this.monthlyDayOfWeek}`;
      } else {
        this.message = `Occurs on the ${this.monthlyLastDayOfWeek}`;
      }
    }
    if (this.message) {
      this.message += ` until ${moment(this.concreteRequest.get('recurrenceEndDate').value).format(
        'MMMM DD, YYYY',
      )}`;
    }
  }

  public onChange(event: { target: { value: any; checked: any } }): void {
    this.checkform = this.concreteRequest.get('days') as UntypedFormArray;
    this.daysEdited = true;
    this.valueExists = this.checkform.controls.filter(
      (object: { value: any }): any => object.value === event.target.value,
    );
    if (event.target.checked) {
      this.checkform.push(new UntypedFormControl(event.target.value));
      this.weekDays = this.weekDays.map((day16: any): void => {
        const dayObj16 = day16;
        if (day16.value === event.target.value) {
          dayObj16.checked = true;
        }
        return dayObj16;
      });
      if (this.checkform.controls.length === 2) {
        this.weekDays = this.weekDays.map((day17: any): void => {
          const dayObj17 = day17;
          dayObj17.isDisabled = false;
          return dayObj17;
        });
      }
    } else if (this.selectedRecurrence === 'Weekly') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day18: any): void => {
            const dayObj18 = day18;
            if (dayObj18.value === event.target.value) {
              dayObj18.checked = false;
            }
            return dayObj18;
          });
        }
        if (this.checkform.controls.length === 1) {
          this.weekDays = this.weekDays.map((day19: any): void => {
            const dayObj19 = day19;
            if (dayObj19.value === this.checkform.controls[0].value) {
              dayObj19.isDisabled = true;
              dayObj19.checked = true;
            }
            return dayObj19;
          });
          return;
        }
        i += 1;
      });
    } else if (this.selectedRecurrence === 'Daily') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day: any): void => {
            const dayObj = day;
            if (dayObj.value === event.target.value) {
              dayObj.checked = false;
              dayObj.isDisabled = false;
            }
            return dayObj;
          });
          return;
        }
        i += 1;
      });
    }
    if (this.checkform.controls.length !== 7) {
      this.concreteRequest.get('recurrence').setValue('Weekly');
      if (+this.concreteRequest.get('repeatEveryCount').value === 1) {
        this.concreteRequest.get('repeatEveryType').setValue('Week');
      } else {
        this.concreteRequest.get('repeatEveryType').setValue('Weeks');
      }
      this.selectedRecurrence = this.concreteRequest.get('recurrence').value;
    }
    if (this.checkform.controls.length === 7) {
      this.concreteRequest.get('recurrence').setValue('Daily');
      if (+this.concreteRequest.get('repeatEveryCount').value === 1) {
        this.concreteRequest.get('repeatEveryType').setValue('Day');
      } else {
        this.concreteRequest.get('repeatEveryType').setValue('Days');
      }
      this.selectedRecurrence = this.concreteRequest.get('recurrence').value;
    }
    this.occurMessage();
    this.onEditSubmitForm();
  }

  public formControlValueChanged1(): void {
    this.concreteRequest.get('repeatEveryType').valueChanges.subscribe((value: string): void => {
      const days = this.concreteRequest.get('days');
      const chosenDateOfMonth = this.concreteRequest.get('chosenDateOfMonth');
      const dateOfMonth = this.concreteRequest.get('dateOfMonth');
      const monthlyRepeatType = this.concreteRequest.get('monthlyRepeatType');
      if (value === 'Week' || value === 'Day' || value === 'Weeks') {
        days.setValidators([Validators.required]);
      } else {
        days.clearValidators();
      }
      if (value === 'Month' || value === 'Months' || value === 'Year' || value === 'Years') {
        if (this.concreteRequest.get('chosenDateOfMonth').value === 1) {
          dateOfMonth.setValidators([Validators.required]);
          monthlyRepeatType.clearValidators();
        } else {
          monthlyRepeatType.setValidators([Validators.required]);
          dateOfMonth.clearValidators();
        }
      } else {
        chosenDateOfMonth.clearValidators();
        dateOfMonth.clearValidators();
        monthlyRepeatType.clearValidators();
      }
      chosenDateOfMonth.updateValueAndValidity();
      dateOfMonth.updateValueAndValidity();
      monthlyRepeatType.updateValueAndValidity();
      days.updateValueAndValidity();
    });
  }

  public setRepeat(data: any): void {
    const setValue = this.concreteRequest.get('recurrence').value;
    const selectedDays = data.days;
    if (this.concreteRequest.get('repeatEveryCount').value > 1 && setValue === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.checkform = this.concreteRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day11: any): void => {
        const dayObj11 = day11;
        dayObj11.checked = true;
        dayObj11.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj11.value));
        return dayObj11;
      });
    }
    if (this.concreteRequest.get('repeatEveryCount').value > 1 && setValue === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.checkform = this.concreteRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day13: any): any => {
        if (selectedDays.includes(day13.value)) {
          day13.checked = true;
        } else {
          day13.checked = false;
        }
        day13.isDisabled = false;
        this.checkform.push(new UntypedFormControl(day13.value));
        return day13;
      });
    }
    if (this.concreteRequest.get('repeatEveryCount').value === 1 && setValue === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.checkform = this.concreteRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day13: any): any => {
        // Check if the day13.value is in the days array
        if (selectedDays.includes(day13.value)) {
          day13.checked = true;
        } else {
          day13.checked = false;
        }
        day13.isDisabled = false;
        this.checkform.push(new UntypedFormControl(day13.value));
        return day13;
      });
    }
    if (this.concreteRequest.get('repeatEveryCount').value === 1 && setValue === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.checkform = this.concreteRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day14: any): void => {
        const dayObj14 = day14;
        dayObj14.checked = true;
        dayObj14.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj14.value));
        return dayObj14;
      });
    }
    if (
      this.concreteRequest.get('repeatEveryCount').value === 1
      && (setValue === 'Monthly' || setValue === 'Yearly')
    ) {
      this.changeMonthlyRecurrence();
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showMonthlyRecurrence();
    }
    if (
      this.concreteRequest.get('repeatEveryCount').value > 1
      && (setValue === 'Monthly' || setValue === 'Yearly')
    ) {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.changeMonthlyRecurrence();
      this.showMonthlyRecurrence();
    }
    this.occurMessage();
  }

  public sortWeekDays(data: any[]): any {
    const order = {
      Sunday: 1,
      Monday: 2,
      Tuesday: 3,
      Wednesday: 4,
      Thursday: 5,
      Friday: 6,
      Saturday: 7,
    };

    if (data.length > 0) {
      return data.sort((a, b): any => order[a] - order[b]);
    }
  }

  public onSubmit(): void {
    const formValue = this.concreteRequest.value;
    if (this.concreteRequest.get('recurrence').value === 'Weekly' || this.concreteRequest.get('recurrence').value === 'Daily') {
      this.weekDays.forEach((day11) => {
        if (day11.checked === true) {
          this.payloadDays.push(day11.value);
        }
      });
    } else {
      this.payloadDays = [];
    }
    if (
      formValue.recurrence !== this.previousRecurrence
      || this.previousRepeatEveryCount !== formValue.repeatEveryCount
      || this.previousRepeatEveryType !== formValue.repeatEveryType
      || JSON.stringify(this.previousDays) !== JSON.stringify(this.payloadDays)
      || this.previousDateOfMonth !== formValue.dateOfMonth
      || this.previousMonthlyRepeatType !== formValue.monthlyRepeatType
      || this.daysEdited
    ) {
      this.recurrenceEdited = true;
    } else {
      this.recurrenceEdited = false;
    }
    this.openRecurrencePopup();
  }

  public openRecurrencePopup(): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef2 = this.modalService.show(this.cancelRecurrenceTemplate, data);
  }

  public recurrenceSubmit(action: string): any {
    if (action === 'no') {
      this.modalRef2.hide();
    } else {
      if (this.modalRef2) {
        this.modalRef2.hide();
      }
      this.onEditSubmit();
    }
  }
}
