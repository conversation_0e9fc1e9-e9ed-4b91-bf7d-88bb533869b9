version: '2'

services:
  follo-it-dev:
    image: '382649735211.dkr.ecr.us-east-1.amazonaws.com/folloit-frontend:dev'
    container_name: 'follo-it-dev'
    ports:
      - '8201:8201'
    logging:
      driver: 'json-file'
      options:
        max-size: '40k'
        max-file: '20'
    restart: always

  follo-it-test:
    image: '382649735211.dkr.ecr.us-east-1.amazonaws.com/folloit-frontend:test'
    container_name: 'follo-it-test'
    ports:
      - '8201:8201'
    logging:
      driver: 'json-file'
      options:
        max-size: '40k'
        max-file: '20'
    restart: always

  follo-it-staging:
    image: '382649735211.dkr.ecr.us-east-1.amazonaws.com/folloit-frontend:staging'
    container_name: 'follo-it-staging'
    ports:
      - '8201:8201'
    logging:
      driver: 'json-file'
      options:
        max-size: '40k'
        max-file: '20'
    restart: always

  follo-it-prod:
    image: '382649735211.dkr.ecr.us-east-1.amazonaws.com/folloit-frontend:prod'
    container_name: 'follo-it-prod'
    ports:
        - '8201:8201'
    logging:
      driver: 'json-file'
      options:
        max-size: '40k'
        max-file: '20'
    restart: always   
     
