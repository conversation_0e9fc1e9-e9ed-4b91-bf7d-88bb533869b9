import { Component, OnInit, TemplateRef } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Router } from '@angular/router';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { ProfileService } from '../services/profile/profile.service';
import { ProjectService } from '../services/profile/project.service';
import { AuthService } from '../services/auth/auth.service';
import { ProjectComponent } from '../project/project.component';

@Component({
  selector: 'app-project-account-admin',
  templateUrl: './project-account-admin.component.html',
  })
export class ProjectAccountAdminComponent implements OnInit {
  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public userData: any = [];

  public pageNo = 1;

  public pageSize = 25;

  public serialNo = 0;

  public totalCount = 0;

  public currentPageNo = 1;

  public projectList: any = [];

  public loader = true;

  public myAccount = false;

  public accountAdmin = false;

  public filterForm: UntypedFormGroup;

  public filterCount = 0;

  public search = '';

  public ParentCompanyId;

  public showSearchbar = false;

  public constructor(private readonly modalService: BsModalService,
    public bsModalRef: BsModalRef,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly profileService: ProfileService, public projectService: ProjectService,
    public authService: AuthService,
    private readonly toastr: ToastrService, public router: Router) {
    this.projectService.refreshProject.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.getProjects();
      }
    });
    this.projectService.projectParent.subscribe((response16): void => {
      if (response16 !== undefined && response16 !== null && response16 !== '') {
        this.ParentCompanyId = response16.ParentCompanyId;
      }
    });
    this.projectService.ParentCompanyId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ParentCompanyId = res;
        this.getProjects();
      }
    });
    this.filterDetailsForm();
  }

  public ngOnInit(): void { /* */ }

  public handleDownKeydown(event: KeyboardEvent, data: any, type: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'filter':
          this.openModal1(data);
          break;
        case 'search':
          this.clear();
          break;
        case 'project':
          this.redirectProject(data);
          break;
        default:
          break;
      }
    }
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group(
      {

        idFilter: [
          '',
        ],
        nameFilter: [
          '',
        ],
      },
    );
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.search = '';
    this.pageNo = 1;
    this.filterDetailsForm();
    this.getProjects();
    this.modalRef.hide();
  }

  public checkCurrentDomain(): void {
    if (this.projectService.checkCurrentDomain()) {
      this.myAccount = true;
      if (this.myAccount) {
        this.authService.getUser().subscribe((res): void => {
          if (res.isAccount) {
            this.accountAdmin = true;
          }
        });
      }
    }
  }

  public addProjects(): void {
    this.bsModalRef = this.modalService.show(ProjectComponent, { backdrop: 'static', keyboard: false, class: 'custom-modal' });
    this.bsModalRef.content.closeBtnName = 'Close';
  }

  public openModal1(template: TemplateRef<any>): void {
    this.modalRef = this.modalService.show(template, { backdrop: 'static', keyboard: false, class: 'modal-sm filter-popup custom-modal' });
  }

  public redirectProject(item): void {
    const newData = { ProjectId: item.id, ParentCompanyId: item.ParentCompanyId };
    this.projectService.updateProjectParent(newData);
    this.projectService.setProject(true);
    this.router.navigate(['/calendar']);
  }

  public clear(): void{
    this.showSearchbar = false;
    this.search = '';
    this.pageNo = 1;
    this.getProjects();
  }

  public getSearchProject(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.pageNo = 1;
    this.search = data;
    this.getProjects();
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('idFilter').value !== '') { this.filterCount += 1; }
    if (this.filterForm.get('nameFilter').value !== '') { this.filterCount += 1; }
    this.pageNo = 1;
    this.getProjects();
    this.modalRef.hide();
  }

  public getProjects(): void {
    this.loader = true;
    const param: any = {
      pageNo: this.pageNo,
      pageSize: this.pageSize,
    };
    let payload: any = {};
    if (this.filterForm !== undefined) {
      payload = {
        idFilter: +this.filterForm.value.idFilter,
        nameFilter: this.filterForm.value.nameFilter,
        search: this.search,
      };
    }
    payload.search = this.search;
    param.ParentCompanyId = this.ParentCompanyId;
    this.profileService.getProjectList(param, payload).subscribe((response: any): void => {
      if (response) {
        const responseData = response.data;
        this.projectList = responseData.rows;
        this.converToString();
        this.totalCount = responseData.count;
        this.loader = false;
      }
    });
  }

  public converToString(): void {
    this.projectList.forEach((element, index): void => {
      const item = element;
      const projectAdmin = item.projectAdminDetails;
      if (projectAdmin && Array.isArray(projectAdmin)) {
        this.projectList[index].adminData = projectAdmin.map((obj): string => obj.User?.firstName).join(', ');
      } else {
        this.projectList[index].adminData = '';
      }
    });
  }


  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-md  projects-modal custom-modal' };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public changePageSize(pageSize): void {
    this.pageNo = 1;
    this.pageSize = pageSize;
    this.getProjects();
  }

  public changePageNo(pageNo): void {
    this.pageNo = pageNo;
    this.getProjects();
  }
}
