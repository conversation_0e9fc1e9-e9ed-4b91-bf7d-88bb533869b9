import { Component, DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ResizableDirective } from './resizable.directive';

// Create a test host component
@Component({
  template: `<div resizable (resizable)="onResize($event)"></div>`
})
class TestHostComponent {
  resizedWidth: number | null = null;

  onResize(width: number): void {
    this.resizedWidth = width;
  }
}

describe('ResizableDirective', () => {
  let component: TestHostComponent;
  let fixture: ComponentFixture<TestHostComponent>;
  let divElement: DebugElement;
  let directive: ResizableDirective;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ResizableDirective, TestHostComponent],
      providers: [
        { provide: Document, useValue: document }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(TestHostComponent);
    component = fixture.componentInstance;
    divElement = fixture.debugElement.query(By.css('div'));
    directive = divElement.injector.get(ResizableDirective);
    fixture.detectChanges();
  });

  it('should create an instance', () => {
    expect(directive).toBeTruthy();
  });

  it('should have resizable output defined', () => {
    expect(directive.resizable).toBeDefined();
  });

  it('should update component width when onResize is called', () => {
    // Create a test width
    const testWidth = 250;

    // Call the component's handler directly
    component.onResize(testWidth);

    // Check if the width was updated
    expect(component.resizedWidth).toBe(testWidth);
  });
});

