import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Socket } from 'ngx-socket-io';
import { ToastrService } from 'ngx-toastr';
import { of, Subject, BehaviorSubject } from 'rxjs';
import { SidemenuComponent } from './sidemenu.component';
import { ProjectService } from '../../services/profile/project.service';
import { ProfileService } from '../../services/profile/profile.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { AuthService } from '../../services/auth/auth.service';
import { ProjectSettingsService } from '../../services/project_settings/project-settings.service';

describe('SidemenuComponent', () => {
  let component: SidemenuComponent;
  let fixture: ComponentFixture<SidemenuComponent>;
  let router: jest.Mocked<Router>;
  let modalService: jest.Mocked<BsModalService>;
  let socket: jest.Mocked<Socket>;
  let toastr: jest.Mocked<ToastrService>;
  let projectService: jest.Mocked<ProjectService>;
  let profileService: jest.Mocked<ProfileService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let authService: jest.Mocked<AuthService>;
  let projectSettingsService: jest.Mocked<ProjectSettingsService>;

  beforeEach(async () => {
    const routerMock = {
      navigate: jest.fn(),
      url: '/dashboard',
    } as unknown as jest.Mocked<Router>;

    // Make url property writable for tests
    Object.defineProperty(routerMock, 'url', {
      writable: true,
      value: '/dashboard'
    });

    const modalServiceMock = {
      show: jest.fn(),
    } as unknown as jest.Mocked<BsModalService>;

    const socketMock = {
      on: jest.fn(),
      emit: jest.fn(),
    } as unknown as jest.Mocked<Socket>;

    const toastrMock = {
      success: jest.fn(),
      error: jest.fn(),
    } as unknown as jest.Mocked<ToastrService>;

    const projectServiceMock = {
      getProjects: jest.fn(),
      getProject: jest.fn(),
      getProjectSharingSettings: jest.fn(),
      projectParent: of({ ProjectId: 1 }),
      accountProjectParent: of({ ProjectId: 1 }),
      refreshProject: of({}),
      isAccountAdmin: of(false),
      isProject: of(true),
      projectNameInSideMenu: of(1),
      getAccountCompany: jest.fn().mockReturnValue(of({ data: [] })),
      updateParentCompanyId: jest.fn(),
      updateCompanyList: jest.fn(),
      updateProjectParent: jest.fn(),
      updateMyAccount: jest.fn(),
      updateAccountAdmin: jest.fn(),
      updatedProjectId: jest.fn(),
      updateProjectAccount: jest.fn(),
      getSingleProject: jest.fn().mockReturnValue(of({ data: {} })),
      getAccountProject: jest.fn().mockReturnValue(of({ data: [] })),
      updateClearProject: jest.fn(),
      unReadCount: jest.fn().mockReturnValue(of({ data: 0 })),
    } as unknown as jest.Mocked<ProjectService>;

    const profileServiceMock = {
      getUserInfo: jest.fn(),
      getOverView: jest.fn().mockReturnValue(of({ data: { RoleId: 1 } })),
    } as unknown as jest.Mocked<ProfileService>;

    const deliveryServiceMock = {
      setReadNotification: jest.fn().mockReturnValue(of(0)),
      refresh: of({}),
      refresh1: of({}),
      fetchData: of({}),
      fetchData1: of({}),
      inspectionUpdated: of({}),
      inspectionUpdated1: of({}),
      fetchConcreteData: of({}),
      fetchConcreteData1: of({}),
      refreshCount: of({}),
      refreshnotifyCount: of({}),
      footerDropdown: of({}),
      getMemberRole: jest.fn().mockReturnValue(of({ data: { memberProjectStatus: 'active' } })),
      updateLoginUser: jest.fn(),
      selectedBookingTypes: new Subject(),
      AllCalendarPermanentRespData$: new BehaviorSubject({
        inspectionData: { lastId: { InspectionId: 1 } },
        deliveryData: { lastId: { DeliveryId: 1 } },
        craneData: { lastId: { CraneRequestId: 1 } },
        concreteData: { lastId: { ConcreteRequestId: 1 } },
      }),
    } as unknown as jest.Mocked<DeliveryService>;

    const authServiceMock = {
      getUser: jest.fn().mockReturnValue(of({ isAccount: true })),
      logout: jest.fn(),
    } as unknown as jest.Mocked<AuthService>;

    const projectSettingsServiceMock = {
      getProjectSharingSettings: jest.fn(),
    } as unknown as jest.Mocked<ProjectSettingsService>;

    await TestBed.configureTestingModule({
      declarations: [SidemenuComponent],
      providers: [
        { provide: Router, useValue: routerMock },
        { provide: BsModalService, useValue: modalServiceMock },
        { provide: Socket, useValue: socketMock },
        { provide: ToastrService, useValue: toastrMock },
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: ProfileService, useValue: profileServiceMock },
        { provide: DeliveryService, useValue: deliveryServiceMock },
        { provide: AuthService, useValue: authServiceMock },
        { provide: ProjectSettingsService, useValue: projectSettingsServiceMock },
        { provide: BsModalRef, useValue: {} },
      ],
    }).compileComponents();

    router = TestBed.inject(Router) as jest.Mocked<Router>;
    modalService = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    socket = TestBed.inject(Socket) as jest.Mocked<Socket>;
    toastr = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    profileService = TestBed.inject(ProfileService) as jest.Mocked<ProfileService>;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    authService = TestBed.inject(AuthService) as jest.Mocked<AuthService>;
    projectSettingsService = TestBed.inject(ProjectSettingsService) as jest.Mocked<ProjectSettingsService>;
  });

  beforeEach(() => {
    localStorage.clear();
    fixture = TestBed.createComponent(SidemenuComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.isCollapsed).toBe(true);
    expect(component.isOpened).toBe(true);
    expect(component.projectList).toEqual([]);
    expect(component.currentIndex).toBe(0);
  });

  it('should toggle sidebar', () => {
    component.isCollapsed = true;
    component.sidebarToggle();
    // expect(component.isCollapsed).toBe(false);
  });

  it('should handle project selection', () => {
    const mockProject = { id: 1, name: 'Test Project' };
    component.projectList = [mockProject];
    component.selectProject(0);
    expect(component.currentIndex).toBe(0);
  });

  it('should handle company selection', () => {
    const mockCompany = { id: 1, name: 'Test Company' };
    component.companyList = [mockCompany];
    component.selectCompany(0);
    expect(component.currentCompanyIndex).toBe(0);
  });

  // it('should handle navigation', () => {
  //   const path = '/test-path';
  //   component.redirect(path);
  //   expect(router.navigate).toHaveBeenCalledWith(['/test-path']);
  // });

  it('should handle checkbox click', () => {
    const type = 'testType';
    const isChecked = true;
    component.onCheckboxClicked(type, isChecked);
    expect(component.selectedTypesCount).toBeDefined();
  });

  it('should handle collapse states', () => {
    component.changeRequestCollapse();
    expect(component.allRequestIsOpened).toBe(false);

    component.changeAllCalendarCollapse();
    expect(component.allCalendarIsOpened).toBe(false);

    component.changeAllCarbonCollapse();
    expect(component.allCarbonIsOpened).toBe(false);
  });

  it('should handle project settings collapse', () => {
    component.changeSettingsCollapse();
    expect(component.isOpened).toBe(false);
  });

  it('should handle account portal navigation', () => {
    component.accountPortal();
    expect(router.navigate).toHaveBeenCalled();
  });

  it('should handle my account portal navigation', () => {
    component.myAccountPortal();
    expect(router.navigate).toHaveBeenCalled();
  });

  it('should handle project info retrieval', () => {
    component.ProjectId = 1;
    component.getProjectInfo();
    expect(projectService.getSingleProject).toHaveBeenCalledWith({ ProjectId: 1 });
  });

  it('should handle member info retrieval', () => {
    component.ProjectId = 1;
    component.currentCompanyId = 1;
    component.getMemberInfo();
    expect(profileService.getOverView).toHaveBeenCalledWith({
      ProjectId: 1,
      ParentCompanyId: 1,
    });
  });

  it('should handle unread notification count', () => {
    component.ProjectId = 1;
    component.currentCompanyId = 1;
    component.getUnReadNotification();
    expect(projectService.unReadCount).toHaveBeenCalledWith({
      ProjectId: 1,
      ParentCompanyId: 1,
    });
  });

  // ============ CONSTRUCTOR SUBSCRIPTION TESTS ============
  describe('Constructor Subscriptions', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should handle projectParent subscription with valid data', () => {
      const mockResponse = { ProjectId: 123 };
      jest.spyOn(component, 'getMemberInfo');
      jest.spyOn(component, 'getProjectInfo');
      jest.spyOn(component, 'getLoginUser');
      jest.spyOn(component, 'setProject');
      jest.spyOn(component, 'getUnReadNotification');
      jest.spyOn(component, 'checkURL');
      jest.spyOn(component, 'getProjectSharingSettings');

      // Trigger the subscription
      projectService.projectParent.next(mockResponse);

      expect(component.ProjectId).toBe(123);
      expect(component.isCollapsed).toBe(true);
      expect(component.getMemberInfo).toHaveBeenCalled();
      expect(component.getProjectInfo).toHaveBeenCalled();
      expect(component.getLoginUser).toHaveBeenCalled();
      expect(component.setProject).toHaveBeenCalled();
      expect(component.getUnReadNotification).toHaveBeenCalled();
      expect(component.checkURL).toHaveBeenCalled();
      expect(component.getProjectSharingSettings).toHaveBeenCalled();
    });

    it('should handle projectParent subscription with null/undefined data', () => {
      jest.spyOn(component, 'getMemberInfo');

      // Test with null
      projectService.projectParent.next(null);
      expect(component.getMemberInfo).not.toHaveBeenCalled();

      // Test with undefined
      projectService.projectParent.next(undefined);
      expect(component.getMemberInfo).not.toHaveBeenCalled();

      // Test with empty string
      projectService.projectParent.next('');
      expect(component.getMemberInfo).not.toHaveBeenCalled();
    });

    it('should handle accountProjectParent subscription', () => {
      const mockResponse = { ProjectId: 456 };
      jest.spyOn(component, 'getMemberInfo');

      projectService.accountProjectParent.next(mockResponse);

      expect(component.ProjectId).toBe(456);
      expect(component.getMemberInfo).toHaveBeenCalled();
    });

    it('should handle refreshProject subscription', () => {
      jest.spyOn(component, 'getParentCompany');

      projectService.refreshProject.next({ data: 'test' });

      expect(component.getParentCompany).toHaveBeenCalled();
    });

    it('should handle delivery service refresh subscriptions', () => {
      jest.spyOn(component, 'getUnReadNotification');

      // Test refresh subscription
      deliveryService.refresh.next({ data: 'test' });
      expect(component.getUnReadNotification).toHaveBeenCalled();

      jest.clearAllMocks();

      // Test refresh1 subscription
      deliveryService.refresh1.next({ data: 'test' });
      expect(component.getUnReadNotification).toHaveBeenCalled();
    });

    it('should handle fetchData subscriptions', () => {
      jest.spyOn(component, 'getUnReadNotification');

      // Test fetchData subscription
      deliveryService.fetchData.next({ data: 'test' });
      expect(component.getUnReadNotification).toHaveBeenCalled();

      jest.clearAllMocks();

      // Test fetchData1 subscription
      deliveryService.fetchData1.next({ data: 'test' });
      expect(component.getUnReadNotification).toHaveBeenCalled();
    });

    it('should handle inspection update subscriptions', () => {
      jest.spyOn(component, 'getUnReadNotification');

      // Test inspectionUpdated subscription
      deliveryService.inspectionUpdated.next({ data: 'test' });
      expect(component.getUnReadNotification).toHaveBeenCalled();

      jest.clearAllMocks();

      // Test inspectionUpdated1 subscription
      deliveryService.inspectionUpdated1.next({ data: 'test' });
      expect(component.getUnReadNotification).toHaveBeenCalled();
    });

    it('should handle concrete data subscriptions', () => {
      jest.spyOn(component, 'getUnReadNotification');

      // Test fetchConcreteData subscription
      deliveryService.fetchConcreteData.next({ data: 'test' });
      expect(component.getUnReadNotification).toHaveBeenCalled();

      jest.clearAllMocks();

      // Test fetchConcreteData1 subscription
      deliveryService.fetchConcreteData1.next({ data: 'test' });
      expect(component.getUnReadNotification).toHaveBeenCalled();
    });

    it('should handle refreshCount subscription - decrement unread count', () => {
      component.unReadCount = 5;

      deliveryService.refreshCount.next({ data: 'test' });

      expect(component.unReadCount).toBe(4);
    });

    it('should handle refreshCount subscription - not decrement when count is 0', () => {
      component.unReadCount = 0;

      deliveryService.refreshCount.next({ data: 'test' });

      expect(component.unReadCount).toBe(0);
    });

    it('should handle refreshnotifyCount subscription', () => {
      component.unReadCount = 5;

      deliveryService.refreshnotifyCount.next({ data: 'test' });

      expect(component.unReadCount).toBe(0);
    });

    it('should handle isAccountAdmin subscription', () => {
      projectService.isAccountAdmin.next({ data: 'test' });

      expect(component.myAccount).toBe(true);
      expect(component.isProject).toBe(false);
      expect(component.ProjectId).toBe(-1);
    });

    it('should handle isProject subscription', () => {
      jest.spyOn(component, 'getUnReadNotification');

      projectService.isProject.next({ data: 'test' });

      expect(component.myAccount).toBe(false);
      expect(component.isProject).toBe(true);
      expect(component.getUnReadNotification).toHaveBeenCalled();
    });

    it('should handle projectNameInSideMenu subscription', () => {
      localStorage.setItem('currentCompanyId', '123');
      jest.spyOn(component, 'getProjects');

      projectService.projectNameInSideMenu.next(456);

      expect(component.getProjects).toHaveBeenCalledWith(123);
    });

    it('should handle footerDropdown subscription - open all collapsed sections', () => {
      component.isOpened = false;
      component.allRequestIsOpened = false;
      component.allCalendarIsOpened = false;
      component.allCarbonIsOpened = false;

      deliveryService.footerDropdown.next(true);

      expect(component.isOpened).toBe(true);
      expect(component.allRequestIsOpened).toBe(true);
      expect(component.allCalendarIsOpened).toBe(true);
      expect(component.allCarbonIsOpened).toBe(true);
    });

    it('should handle footerDropdown subscription - no change when already open', () => {
      component.isOpened = true;
      component.allRequestIsOpened = true;
      component.allCalendarIsOpened = true;
      component.allCarbonIsOpened = true;

      deliveryService.footerDropdown.next(true);

      expect(component.isOpened).toBe(true);
      expect(component.allRequestIsOpened).toBe(true);
      expect(component.allCalendarIsOpened).toBe(true);
      expect(component.allCarbonIsOpened).toBe(true);
    });
  });

  // ============ METHOD TESTS ============
  describe('Component Methods', () => {
    beforeEach(() => {
      jest.clearAllMocks();
    });

    describe('getParentCompany', () => {
      it('should get parent company and set current company', () => {
        const mockCompanies = [
          { id: 1, name: 'Company 1' },
          { id: 2, name: 'Company 2' }
        ];
        projectService.getAccountCompany.mockReturnValue(of({ data: mockCompanies }));
        jest.spyOn(component, 'getProjects');

        component.getParentCompany();

        expect(projectService.getAccountCompany).toHaveBeenCalled();
        expect(component.companyList).toEqual(mockCompanies);
        expect(component.currentCompanyId).toBe(1);
        expect(localStorage.getItem('currentCompanyId')).toBe('1');
        expect(projectService.updateParentCompanyId).toHaveBeenCalledWith(1);
        expect(projectService.updateCompanyList).toHaveBeenCalledWith(mockCompanies);
        expect(component.getProjects).toHaveBeenCalledWith(1);
      });

      it('should use existing currentCompanyId from localStorage', () => {
        const mockCompanies = [
          { id: 1, name: 'Company 1' },
          { id: 2, name: 'Company 2' }
        ];
        localStorage.setItem('currentCompanyId', '2');
        projectService.getAccountCompany.mockReturnValue(of({ data: mockCompanies }));
        jest.spyOn(component, 'getProjects');

        component.getParentCompany();

        expect(component.currentCompanyId).toBe(2);
        expect(component.currentCompanyIndex).toBe(1);
      });

      it('should handle empty company list', () => {
        projectService.getAccountCompany.mockReturnValue(of({ data: [] }));
        jest.spyOn(component, 'getProjects');

        expect(() => component.getParentCompany()).not.toThrow();
      });
    });

    describe('getUnReadNotification', () => {
      it('should get unread notifications for project', () => {
        component.myAccount = false;
        component.ProjectId = 123;
        component.currentCompanyId = 456;
        projectService.unReadCount.mockReturnValue(of({ data: 5 }));

        component.getUnReadNotification();

        expect(projectService.unReadCount).toHaveBeenCalledWith({
          ProjectId: 123,
          ParentCompanyId: 456
        });
        expect(component.unReadCount).toBe(5);
      });

      it('should get unread notifications for account admin', () => {
        component.myAccount = true;
        projectService.unReadCount.mockReturnValue(of({ data: 3 }));

        component.getUnReadNotification();

        expect(projectService.unReadCount).toHaveBeenCalledWith({});
        expect(component.unReadCount).toBe(3);
      });
    });

    describe('checkCurrentDomain', () => {
      it('should handle account admin user', () => {
        authService.getUser.mockReturnValue(of({ isAccount: true }));
        component.ProjectId = -1;
        jest.spyOn(component, 'checkURL');
        jest.spyOn(component, 'getUnReadNotification');

        component.checkCurrentDomain();

        expect(component.accountAdmin).toBe(true);
        expect(component.loader).toBe(false);
        expect(component.myAccount).toBe(true);
        expect(projectService.updateMyAccount).toHaveBeenCalledWith(true);
        expect(projectService.updateAccountAdmin).toHaveBeenCalledWith(true);
        expect(component.checkURL).toHaveBeenCalled();
        expect(component.getUnReadNotification).toHaveBeenCalled();
      });

      it('should handle non-account user', () => {
        authService.getUser.mockReturnValue(of({ isAccount: false }));
        jest.spyOn(component, 'checkURL');

        component.checkCurrentDomain();

        expect(component.accountAdmin).toBe(false);
        expect(component.checkURL).not.toHaveBeenCalled();
      });
    });

    describe('checkURL', () => {
      it('should redirect to dashboard when invalid URL for account admin', () => {
        component.myAccount = true;
        Object.defineProperty(router, 'url', { value: '/invalid-url', configurable: true });

        const result = component.checkURL();

        expect(component.url).toBe('invalid-url');
        expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
        expect(result).toBe(true);
      });

      it('should not redirect when valid URL for account admin', () => {
        component.myAccount = true;
        Object.defineProperty(router, 'url', { value: '/billing', configurable: true });

        const result = component.checkURL();

        expect(component.url).toBe('billing');
        expect(router.navigate).not.toHaveBeenCalled();
        expect(result).toBe(false);
      });

      it('should not redirect when not account admin', () => {
        component.myAccount = false;
        Object.defineProperty(router, 'url', { value: '/invalid-url', configurable: true });

        const result = component.checkURL();

        expect(router.navigate).not.toHaveBeenCalled();
        expect(result).toBe(false);
      });
    });

    describe('setProject', () => {
      it('should set current project index when project exists', () => {
        component.projectList = [
          { id: 1, name: 'Project 1' },
          { id: 2, name: 'Project 2' }
        ];
        component.ProjectId = 2;

        component.setProject();

        expect(component.currentIndex).toBe(1);
        expect(projectService.setProject).toHaveBeenCalledWith(true);
      });

      it('should not set index when project does not exist', () => {
        component.projectList = [
          { id: 1, name: 'Project 1' },
          { id: 2, name: 'Project 2' }
        ];
        component.ProjectId = 999;

        component.setProject();

        expect(component.currentIndex).toBe(0); // Should remain unchanged
        expect(projectService.setProject).not.toHaveBeenCalled();
      });
    });

    describe('selectCompany', () => {
      it('should select company and update state', () => {
        component.companyList = [
          { id: 1, name: 'Company 1' },
          { id: 2, name: 'Company 2' }
        ];
        jest.spyOn(component, 'getProjects');

        component.selectCompany(1);

        expect(projectService.updateParentCompanyId).toHaveBeenCalledWith(2);
        expect(component.currentCompanyIndex).toBe(1);
        expect(component.projectList).toEqual([]);
        expect(component.currentCompanyId).toBe(2);
        expect(localStorage.getItem('currentCompanyId')).toBe('2');
        expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
        expect(component.getProjects).toHaveBeenCalledWith(2);
        expect(component.isCompanyCollapsed).toBe(true);
      });

      it('should handle invalid company index', () => {
        component.companyList = [{ id: 1, name: 'Company 1' }];

        expect(() => component.selectCompany(999)).not.toThrow();
      });
    });

    describe('openAddNDRModal', () => {
      it('should open inspection modal', () => {
        component.inspectionLastId = 123;
        const mockModalRef = { content: { lastId: undefined, closeBtnName: undefined } };
        modalService.show.mockReturnValue(mockModalRef as any);

        component.openAddNDRModal('inspection');

        expect(modalService.show).toHaveBeenCalled();
        expect(mockModalRef.content.lastId).toBe(123);
        expect(mockModalRef.content.closeBtnName).toBe('Close');
      });

      it('should open crane modal', () => {
        component.craneLastId = 456;
        const mockModalRef = { content: { lastId: undefined, closeBtnName: undefined } };
        modalService.show.mockReturnValue(mockModalRef as any);

        component.openAddNDRModal('crane');

        expect(modalService.show).toHaveBeenCalled();
        expect(mockModalRef.content.lastId).toBe(456);
        expect(mockModalRef.content.closeBtnName).toBe('Close');
      });

      it('should open delivery modal', () => {
        component.deliveryLastId = 789;
        const mockModalRef = { content: { lastId: undefined, closeBtnName: undefined } };
        modalService.show.mockReturnValue(mockModalRef as any);

        component.openAddNDRModal('delivery');

        expect(modalService.show).toHaveBeenCalled();
        expect(mockModalRef.content.lastId).toBe(789);
        expect(mockModalRef.content.closeBtnName).toBe('Close');
      });

      it('should open concrete modal', () => {
        const mockModalRef = { content: {} };
        modalService.show.mockReturnValue(mockModalRef as any);

        component.openAddNDRModal('concrete');

        expect(modalService.show).toHaveBeenCalled();
      });

      it('should handle unknown modal type', () => {
        expect(() => component.openAddNDRModal('unknown')).not.toThrow();
      });
    });

    describe('getLoginUser', () => {
      it('should get login user info', () => {
        component.ProjectId = 123;
        component.currentCompanyId = 456;
        const mockUser = { id: 1, name: 'Test User' };
        deliveryService.getMemberRole.mockReturnValue(of({ data: mockUser }));

        component.getLoginUser();

        expect(deliveryService.getMemberRole).toHaveBeenCalledWith({
          ProjectId: 123,
          ParentCompanyId: 456
        });
        expect(component.authUser).toEqual(mockUser);
        expect(deliveryService.updateLoginUser).toHaveBeenCalledWith(mockUser);
      });

      it('should handle API error', () => {
        component.ProjectId = 123;
        component.currentCompanyId = 456;
        deliveryService.getMemberRole.mockReturnValue(of({ data: null }));

        expect(() => component.getLoginUser()).not.toThrow();
      });
    });

    describe('getMemberInfo', () => {
      it('should get member info and update user data', () => {
        component.ProjectId = 123;
        component.currentCompanyId = 456;
        const mockUserData = { RoleId: 2, name: 'Test User' };
        profileService.getOverView.mockReturnValue(of({ data: mockUserData }));
        Object.defineProperty(router, 'url', { value: '/dashboard', configurable: true });

        component.getMemberInfo();

        expect(profileService.getOverView).toHaveBeenCalledWith({
          ProjectId: 123,
          ParentCompanyId: 456
        });
        expect(component.userData).toEqual(mockUserData);
        expect(component.roleId).toBe(2);
        expect(component.url).toBe('dashboard');
        expect(projectService.updatedUserData).toHaveBeenCalledWith(mockUserData);
      });

      it('should handle role-based URL validation for non-admin users', () => {
        component.ProjectId = 123;
        component.currentCompanyId = 456;
        const mockUserData = { RoleId: 3, name: 'Test User' };
        profileService.getOverView.mockReturnValue(of({ data: mockUserData }));
        Object.defineProperty(router, 'url', { value: '/invalid-url', configurable: true });

        component.getMemberInfo();

        expect(component.userData).toEqual(mockUserData);
        expect(component.roleId).toBe(3);
      });

      it('should handle admin users with menu access', () => {
        component.ProjectId = 123;
        component.currentCompanyId = 456;
        const mockUserData = { RoleId: 1, name: 'Admin User' };
        profileService.getOverView.mockReturnValue(of({ data: mockUserData }));
        Object.defineProperty(router, 'url', { value: '/members', configurable: true });

        component.getMemberInfo();

        expect(component.userData).toEqual(mockUserData);
        expect(component.roleId).toBe(1);
        expect(component.isOpened).toBe(false);
      });

      it('should handle null response', () => {
        component.ProjectId = 123;
        component.currentCompanyId = 456;
        profileService.getOverView.mockReturnValue(of(null));

        expect(() => component.getMemberInfo()).not.toThrow();
      });
    });

    describe('Collapse Methods', () => {
      it('should toggle settings collapse', () => {
        component.isOpened = true;
        component.changeSettingsCollapse();
        expect(component.isOpened).toBe(false);

        component.changeSettingsCollapse();
        expect(component.isOpened).toBe(true);
      });

      it('should close settings collapse', () => {
        component.isOpened = false;
        component.closeSettingsCollapse();
        expect(component.isOpened).toBe(true);
      });

      it('should toggle all booking settings collapse', () => {
        component.allRequestIsOpened = true;
        component.changeAllBookingSettingsCollapse();
        expect(component.allRequestIsOpened).toBe(false);

        component.changeAllBookingSettingsCollapse();
        expect(component.allRequestIsOpened).toBe(true);
      });

      it('should toggle carbon collapse', () => {
        component.allCarbonIsOpened = true;
        component.changeCarbonCollapse();
        expect(component.allCarbonIsOpened).toBe(false);

        component.changeCarbonCollapse();
        expect(component.allCarbonIsOpened).toBe(true);
      });

      it('should close all booking settings collapse', () => {
        component.allRequestIsOpened = false;
        component.closeAllbookingSettingsCollapse();
        expect(component.allRequestIsOpened).toBe(true);
      });
    });

    describe('onCheckboxClicked', () => {
      beforeEach(() => {
        const mockPermanentData = {
          inspectionData: { lastId: { InspectionId: 123 } },
          deliveryData: { lastId: { DeliveryId: 456 } },
          craneData: { lastId: { CraneRequestId: 789 } },
          concreteData: { lastId: { ConcreteRequestId: 101 } }
        };
        const mockTempData = { deliveryData: {}, craneData: {} };

        deliveryService.AllCalendarPermanentRespData$ = new BehaviorSubject(mockPermanentData);
        deliveryService.AllCalendarRespData$ = new BehaviorSubject(mockTempData);
        deliveryService.selectedBookingTypes = new BehaviorSubject(['deliveryData']);
        deliveryService.updateAllCalendarRespData = jest.fn();
        deliveryService.dataChanged = new BehaviorSubject(false);
      });

      it('should add type when checked', () => {
        const nextSpy = jest.spyOn(deliveryService.selectedBookingTypes, 'next');
        const updateSpy = jest.spyOn(deliveryService, 'updateAllCalendarRespData');
        const dataChangedSpy = jest.spyOn(deliveryService.dataChanged, 'next');

        component.onCheckboxClicked('inspectionData', true);

        expect(component.inspectionLastId).toBe(123);
        expect(component.deliveryLastId).toBe(456);
        expect(component.craneLastId).toBe(789);
        expect(nextSpy).toHaveBeenCalledWith(['deliveryData', 'inspectionData']);
        expect(updateSpy).toHaveBeenCalled();
        expect(dataChangedSpy).toHaveBeenCalledWith(true);
        expect(component.selectedTypesCount).toBe(2);
      });

      it('should remove type when unchecked', () => {
        deliveryService.selectedBookingTypes.next(['deliveryData', 'inspectionData']);
        const nextSpy = jest.spyOn(deliveryService.selectedBookingTypes, 'next');
        const updateSpy = jest.spyOn(deliveryService, 'updateAllCalendarRespData');

        component.onCheckboxClicked('inspectionData', false);

        expect(nextSpy).toHaveBeenCalledWith(['deliveryData']);
        expect(updateSpy).toHaveBeenCalled();
        expect(component.selectedTypesCount).toBe(1);
      });

      it('should handle adding existing type', () => {
        deliveryService.selectedBookingTypes.next(['deliveryData']);
        const nextSpy = jest.spyOn(deliveryService.selectedBookingTypes, 'next');

        component.onCheckboxClicked('deliveryData', true);

        expect(nextSpy).toHaveBeenCalledWith(['deliveryData']);
      });

      it('should handle removing non-existing type', () => {
        deliveryService.selectedBookingTypes.next(['deliveryData']);
        const nextSpy = jest.spyOn(deliveryService.selectedBookingTypes, 'next');

        component.onCheckboxClicked('inspectionData', false);

        expect(nextSpy).toHaveBeenCalledWith(['deliveryData']);
      });
    });

    describe('accountPortal', () => {
      it('should toggle account portal state', () => {
        component.myAccount = false;
        component.isCollapsed = false;
        component.currentCompanyId = 123;
        jest.spyOn(component, 'getProjects');
        jest.spyOn(component, 'checkURL');

        component.accountPortal();

        expect(component.myAccount).toBe(true);
        expect(component.isCollapsed).toBe(true);
        expect(component.getProjects).toHaveBeenCalledWith(123);
        expect(projectService.updateAccountAdmin).toHaveBeenCalledWith(true);
        expect(projectService.updateMyAccount).toHaveBeenCalledWith(true);
        expect(component.checkURL).toHaveBeenCalled();
      });

      it('should not update my account when toggling off', () => {
        component.myAccount = true;
        component.isCollapsed = true;
        jest.spyOn(component, 'getProjects');
        jest.spyOn(component, 'checkURL');

        component.accountPortal();

        expect(component.myAccount).toBe(false);
        expect(component.isCollapsed).toBe(false);
        expect(projectService.updateMyAccount).not.toHaveBeenCalled();
      });
    });

    describe('myAccountPortal', () => {
      it('should clear project and call account portal', () => {
        jest.spyOn(component, 'accountPortal');

        component.myAccountPortal();

        expect(projectService.updateClearProject).toHaveBeenCalledWith({ status: true });
        expect(component.accountPortal).toHaveBeenCalled();
      });
    });

    describe('getProjectInfo', () => {
      it('should get project info and handle overdue status', () => {
        component.ProjectId = 123;
        Object.defineProperty(router, 'url', { value: '/dashboard', configurable: true });
        const mockProject = {
          data: { status: 'overdue', name: 'Test Project' }
        };
        projectService.getSingleProject.mockReturnValue(of(mockProject));

        component.getProjectInfo();

        expect(projectService.getSingleProject).toHaveBeenCalledWith({ ProjectId: 123 });
        expect(component.url).toBe('dashboard');
        expect(router.navigate).toHaveBeenCalledWith(['/profile']);
      });

      it('should handle trial overdue status', () => {
        component.ProjectId = 123;
        Object.defineProperty(router, 'url', { value: '/dashboard', configurable: true });
        const mockProject = {
          data: { status: 'trialoverdue', name: 'Test Project' }
        };
        projectService.getSingleProject.mockReturnValue(of(mockProject));

        component.getProjectInfo();

        expect(router.navigate).toHaveBeenCalledWith(['/profile']);
      });

      it('should not redirect when on profile page', () => {
        component.ProjectId = 123;
        Object.defineProperty(router, 'url', { value: '/profile', configurable: true });
        const mockProject = {
          data: { status: 'overdue', name: 'Test Project' }
        };
        projectService.getSingleProject.mockReturnValue(of(mockProject));

        component.getProjectInfo();

        expect(router.navigate).not.toHaveBeenCalled();
      });

      it('should not redirect for active projects', () => {
        component.ProjectId = 123;
        Object.defineProperty(router, 'url', { value: '/dashboard', configurable: true });
        const mockProject = {
          data: { status: 'active', name: 'Test Project' }
        };
        projectService.getSingleProject.mockReturnValue(of(mockProject));

        component.getProjectInfo();

        expect(router.navigate).not.toHaveBeenCalled();
      });

      it('should handle no project ID', () => {
        component.ProjectId = null;

        component.getProjectInfo();

        expect(projectService.getSingleProject).not.toHaveBeenCalled();
      });

      it('should handle null project data', () => {
        component.ProjectId = 123;
        projectService.getSingleProject.mockReturnValue(of(null));

        expect(() => component.getProjectInfo()).not.toThrow();
      });
    });

    describe('getProjects', () => {
      it('should get projects and set first project as default', () => {
        const mockProjects = [
          { id: 1, name: 'Project 1', isAccount: true, memberDetails: [{ memberProjectStatus: 'active' }] },
          { id: 2, name: 'Project 2', isAccount: false, memberDetails: [{ memberProjectStatus: 'active' }] }
        ];
        projectService.getAccountProject.mockReturnValue(of({ data: mockProjects }));

        component.getProjects(123);

        expect(projectService.getAccountProject).toHaveBeenCalledWith(123);
        expect(component.projectList).toEqual(mockProjects);
        expect(component.projectAccount).toBe(true);
        expect(component.currentIndex).toBe(0);
        expect(projectService.updateProjectAccount).toHaveBeenCalledWith(true);
        expect(projectService.updateProjectParent).toHaveBeenCalledWith({ ProjectId: 1, ParentCompanyId: 123 });
        expect(projectService.updatedProjectId).toHaveBeenCalledWith(1);
        expect(localStorage.getItem('ProjectId')).toBe('1');
        expect(component.loader).toBe(false);
      });

      it('should use existing project from localStorage', () => {
        const mockProjects = [
          { id: 1, name: 'Project 1', isAccount: true, memberDetails: [{ memberProjectStatus: 'active' }] },
          { id: 2, name: 'Project 2', isAccount: false, memberDetails: [{ memberProjectStatus: 'active' }] }
        ];
        localStorage.setItem('ProjectId', '2');
        projectService.getAccountProject.mockReturnValue(of({ data: mockProjects }));

        component.getProjects(123);

        expect(component.projectAccount).toBe(false);
        expect(projectService.updatedProjectId).toHaveBeenCalledWith(2);
      });

      it('should logout when no active projects', () => {
        const mockProjects = [
          { id: 1, name: 'Project 1', memberDetails: [{ memberProjectStatus: 'inactive' }] }
        ];
        projectService.getAccountProject.mockReturnValue(of({ data: mockProjects }));

        component.getProjects(123);

        expect(component.projectList).toEqual([]);
        expect(authService.logout).toHaveBeenCalled();
      });

      it('should filter only active projects', () => {
        const mockProjects = [
          { id: 1, name: 'Project 1', memberDetails: [{ memberProjectStatus: 'active' }] },
          { id: 2, name: 'Project 2', memberDetails: [{ memberProjectStatus: 'inactive' }] },
          { id: 3, name: 'Project 3', memberDetails: [{ memberProjectStatus: 'active' }] }
        ];
        projectService.getAccountProject.mockReturnValue(of({ data: mockProjects }));

        component.getProjects(123);

        expect(component.projectList).toHaveLength(2);
        expect(component.projectList[0].id).toBe(1);
        expect(component.projectList[1].id).toBe(3);
      });

      it('should not update project parent when myAccount is true', () => {
        const mockProjects = [
          { id: 1, name: 'Project 1', isAccount: true, memberDetails: [{ memberProjectStatus: 'active' }] }
        ];
        component.myAccount = true;
        projectService.getAccountProject.mockReturnValue(of({ data: mockProjects }));

        component.getProjects(123);

        expect(projectService.updateProjectParent).not.toHaveBeenCalled();
      });
    });

    describe('selectProject', () => {
      it('should select project and check member access', () => {
        component.projectList = [
          { id: 1, name: 'Project 1' },
          { id: 2, name: 'Project 2' }
        ];
        jest.spyOn(component, 'checkMemberAccess');

        component.selectProject(1);

        expect(component.checkMemberAccess).toHaveBeenCalledWith(2, 1);
      });
    });

    describe('checkMemberAccess', () => {
      beforeEach(() => {
        component.projectList = [
          { id: 1, name: 'Project 1', isAccount: true },
          { id: 2, name: 'Project 2', isAccount: false }
        ];
        component.currentCompanyId = 123;
      });

      it('should allow access for active member', () => {
        const mockResponse = { data: { memberProjectStatus: 'active' } };
        deliveryService.getMemberRole.mockReturnValue(of(mockResponse));
        jest.spyOn(component, 'getProjectInfo');
        jest.spyOn(component, 'getLoginUser');

        component.checkMemberAccess(1, 0);

        expect(deliveryService.getMemberRole).toHaveBeenCalledWith({
          ProjectId: 1,
          ParentCompanyId: 123
        });
        expect(projectService.updatedProjectId).toHaveBeenCalledWith(1);
        expect(projectService.updateProjectAccount).toHaveBeenCalledWith(true);
        expect(projectService.updateProjectParent).toHaveBeenCalledWith({
          ProjectId: 1,
          ParentCompanyId: 123
        });
        expect(component.myAccount).toBe(false);
        expect(component.getProjectInfo).toHaveBeenCalled();
        expect(localStorage.getItem('ProjectId')).toBe('1');
        expect(component.getLoginUser).toHaveBeenCalled();
        expect(component.currentIndex).toBe(0);
        expect(component.isCollapsed).toBe(true);
        expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
      });

      it('should show error for inactive member', () => {
        const mockResponse = { data: { memberProjectStatus: 'inactive' } };
        deliveryService.getMemberRole.mockReturnValue(of(mockResponse));

        component.checkMemberAccess(1, 0);

        expect(toastr.error).toHaveBeenCalledWith('You were deactivated from this project.Please contact super admin');
        expect(projectService.updatedProjectId).not.toHaveBeenCalled();
      });
    });

    describe('Additional Methods', () => {
      it('should handle changeProjectCollapse', () => {
        component.isCollapsed = true;
        component.changeProjectCollapse();
        expect(component.isCollapsed).toBe(false);

        component.changeProjectCollapse();
        expect(component.isCollapsed).toBe(true);
      });

      it('should handle redirect', () => {
        component.redirect('test-path');
        expect(router.navigate).toHaveBeenCalledWith(['/test-path']);
      });

      it('should handle addProjects', () => {
        const mockModalRef = { content: { closeBtnName: undefined } };
        modalService.show.mockReturnValue(mockModalRef as any);

        component.addProjects();

        expect(component.isCollapsed).toBe(true);
        expect(modalService.show).toHaveBeenCalled();
        expect(mockModalRef.content.closeBtnName).toBe('Close');
      });

      it('should handle sidebarToggle', () => {
        component.shouldShow = false;
        const emitSpy = jest.fn();
        (component as any).sidemenuToggle = { emit: emitSpy };

        component.sidebarToggle();

        expect(component.shouldShow).toBe(true);
        expect(emitSpy).toHaveBeenCalledWith(true);
      });

      it('should handle ngOnInit', () => {
        jest.spyOn(deliveryService.selectedBookingTypes, 'next');

        component.ngOnInit();

        expect(deliveryService.selectedBookingTypes.next).toHaveBeenCalledWith(['deliveryData', 'craneData', 'concreteData', 'inspectionData']);
        expect(component.selectedTypesCount).toBe(4);
      });
    });

    describe('getProjectSharingSettings', () => {
      it('should get project settings and setup refresh', () => {
        component.ProjectId = 123;
        const mockSettings = {
          data: {
            projectSettings: {
              autoRefreshRateInMinutes: '5'
            }
          }
        };
        projectSettingsService.getProjectSettings.mockReturnValue(of(mockSettings));
        jest.spyOn(component, 'setupPageRefresh');

        component.getProjectSharingSettings();

        expect(projectSettingsService.getProjectSettings).toHaveBeenCalledWith({ ProjectId: 123 });
        expect(component.refreshRate).toBe(5);
        expect(component.setupPageRefresh).toHaveBeenCalled();
      });

      it('should not call API when no ProjectId', () => {
        component.ProjectId = null;

        component.getProjectSharingSettings();

        expect(projectSettingsService.getProjectSettings).not.toHaveBeenCalled();
      });

      it('should handle null response', () => {
        component.ProjectId = 123;
        projectSettingsService.getProjectSettings.mockReturnValue(of(null));

        expect(() => component.getProjectSharingSettings()).not.toThrow();
      });
    });

    describe('setupPageRefresh', () => {
      beforeEach(() => {
        jest.useFakeTimers();
        jest.spyOn(window, 'setInterval');
        Object.defineProperty(window, 'location', {
          value: { reload: jest.fn() },
          writable: true
        });
      });

      afterEach(() => {
        jest.useRealTimers();
      });

      it('should setup page refresh interval', () => {
        component.refreshRate = 2;

        component.setupPageRefresh();

        expect(setInterval).toHaveBeenCalledWith(expect.any(Function), 120000); // 2 * 60 * 1000
      });
    });

    describe('handleDownKeydown', () => {
      it('should handle Enter key for different actions', () => {
        const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
        jest.spyOn(component, 'selectCompany');
        jest.spyOn(component, 'changeProjectCollapse');
        jest.spyOn(component, 'myAccountPortal');
        jest.spyOn(component, 'selectProject');
        jest.spyOn(component, 'closeSettingsCollapse');
        jest.spyOn(component, 'openAddNDRModal');
        jest.spyOn(component, 'changeRequestCollapse');
        jest.spyOn(component, 'changeAllCarbonCollapse');
        jest.spyOn(component, 'changeSettingsCollapse');

        // Test all action types
        component.handleDownKeydown(mockEvent, 1, 'select');
        expect(component.selectCompany).toHaveBeenCalledWith(1);

        component.handleDownKeydown(mockEvent, null, 'changeproject');
        expect(component.changeProjectCollapse).toHaveBeenCalled();

        component.handleDownKeydown(mockEvent, null, 'account');
        expect(component.myAccountPortal).toHaveBeenCalled();

        component.handleDownKeydown(mockEvent, 2, 'selectpro');
        expect(component.selectProject).toHaveBeenCalledWith(2);

        component.handleDownKeydown(mockEvent, null, 'close');
        expect(component.closeSettingsCollapse).toHaveBeenCalled();

        component.handleDownKeydown(mockEvent, 'inspection', 'open');
        expect(component.openAddNDRModal).toHaveBeenCalledWith('inspection');

        component.handleDownKeydown(mockEvent, null, 'change');
        expect(component.changeRequestCollapse).toHaveBeenCalled();

        component.handleDownKeydown(mockEvent, null, 'changeall');
        expect(component.changeAllCarbonCollapse).toHaveBeenCalled();

        component.handleDownKeydown(mockEvent, null, 'changeset');
        expect(component.changeSettingsCollapse).toHaveBeenCalled();

        expect(mockEvent.preventDefault).toHaveBeenCalledTimes(9);
      });

      it('should handle Space key', () => {
        const mockEvent = { key: ' ', preventDefault: jest.fn() } as any;
        jest.spyOn(component, 'selectCompany');

        component.handleDownKeydown(mockEvent, 1, 'select');

        expect(component.selectCompany).toHaveBeenCalledWith(1);
        expect(mockEvent.preventDefault).toHaveBeenCalled();
      });

      it('should handle unknown action type', () => {
        const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;

        expect(() => component.handleDownKeydown(mockEvent, null, 'unknown')).not.toThrow();
        expect(mockEvent.preventDefault).toHaveBeenCalled();
      });

      it('should not handle other keys', () => {
        const mockEvent = { key: 'Tab', preventDefault: jest.fn() } as any;
        jest.spyOn(component, 'selectCompany');

        component.handleDownKeydown(mockEvent, 1, 'select');

        expect(component.selectCompany).not.toHaveBeenCalled();
        expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      });
    });

    describe('Error Handling and Edge Cases', () => {
      it('should handle API errors gracefully', () => {
        projectService.getAccountCompany.mockReturnValue(of({ data: null }));

        expect(() => component.getParentCompany()).not.toThrow();
      });

      it('should handle empty project list in setProject', () => {
        component.projectList = [];
        component.ProjectId = 1;

        expect(() => component.setProject()).not.toThrow();
        expect(projectService.setProject).not.toHaveBeenCalled();
      });

      it('should handle undefined values in subscriptions', () => {
        expect(() => {
          projectService.projectParent.next(undefined);
          projectService.accountProjectParent.next(undefined);
          deliveryService.refresh.next(undefined);
        }).not.toThrow();
      });
    });
  });
});
