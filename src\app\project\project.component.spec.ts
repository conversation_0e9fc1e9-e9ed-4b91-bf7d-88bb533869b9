import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { ProjectComponent } from './project.component';
import { Router } from '@angular/router';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from '../services/auth/auth.service';
import { ProjectService } from '../services/profile/project.service';
import { UntypedFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { of, throwError, BehaviorSubject } from 'rxjs';
import { GoogleMapsModule } from '@angular/google-maps';
import { Component, Input, Directive, EventEmitter, Output } from '@angular/core';

// Mock ngx-places directive
@Directive({
  selector: '[ngx-gp-autocomplete]',
  exportAs: 'ngx-places'
})
class MockNgxPlacesDirective {
  @Input() options: any;
  @Input() placeholder: string;
  @Output() onAddressChange = new EventEmitter<any>();
}

// Mock Google Maps API
const mockGoogleMaps = {
  maps: {
    Map: class {
      constructor() {}
      setCenter() {}
      setZoom() {}
      panTo() {}
    },
    MapTypeId: {
      SATELLITE: 'satellite',
      ROADMAP: 'roadmap'
    },
    Animation: {
      DROP: 'DROP',
      BOUNCE: 'BOUNCE'
    },
    MapMouseEvent: class {
      latLng: { lat: () => number; lng: () => number };
      constructor() {
        this.latLng = {
          lat: () => 38.897957,
          lng: () => -77.036560
        };
      }
    },
    Geocoder: class {
      geocode = jest.fn().mockImplementation((request, callback) => {
        callback([{ formatted_address: 'Test Address' }], 'OK');
      });
    },
    LatLng: class {
      constructor(public lat: number, public lng: number) {}
    },
    Marker: class {
      constructor() {}
      setPosition() {}
      setMap() {}
    }
  }
};

// Add Google Maps to window object
(window as any).google = mockGoogleMaps;

describe('ProjectComponent', () => {
  let component: ProjectComponent;
  let fixture: ComponentFixture<ProjectComponent>;
  let router: jest.Mocked<Router>;
  let modalService: jest.Mocked<BsModalService>;
  let toastrService: jest.Mocked<ToastrService>;
  let authService: jest.Mocked<AuthService>;
  let projectService: jest.Mocked<ProjectService>;
  let formBuilder: UntypedFormBuilder;

  beforeEach(async () => {
    const routerSpy = {
      navigate: jest.fn(),
      url: '',
      events: of({}) // Mock router events as an Observable
    };
    const modalServiceSpy = {
      show: jest.fn()
    };
    const toastrServiceSpy = {
      success: jest.fn(),
      error: jest.fn(),
      clear: jest.fn()
    };
    const authServiceSpy = {
      getUser: jest.fn().mockReturnValue(of({ isAccount: false }))
    };
    const projectServiceSpy = {
      createProject: jest.fn(),
      projectParent: new BehaviorSubject({ ProjectId: '123', ParentCompanyId: '456' }),
      ParentCompanyId: new BehaviorSubject('456'),
      existProject: jest.fn()
    };

    await TestBed.configureTestingModule({
      declarations: [
        ProjectComponent,
        MockNgxPlacesDirective
      ],
      imports: [ReactiveFormsModule, GoogleMapsModule],
      providers: [
        { provide: Router, useValue: routerSpy },
        { provide: BsModalService, useValue: modalServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: ProjectService, useValue: projectServiceSpy },
        UntypedFormBuilder
      ]
    }).compileComponents();

    router = TestBed.inject(Router) as jest.Mocked<Router>;
    modalService = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    authService = TestBed.inject(AuthService) as jest.Mocked<AuthService>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    formBuilder = TestBed.inject(UntypedFormBuilder);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ProjectComponent);
    component = fixture.componentInstance;
    component.bsModalRef = { hide: jest.fn() } as unknown as BsModalRef;
    component.zoom = 18; // Initialize zoom
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.latitude).toBe(38.897957);
    expect(component.longitude).toBe(-77.036560);
    expect(component.zoom).toBe(18);
    expect(component.submitted).toBe(false);
  });

  it('should create project form with required validators', () => {
    component.projectForm();
    const form = component.projectDetailsForm;

    expect(form.get('projectName')).toBeTruthy();
    expect(form.get('projectLocation')).toBeTruthy();
    expect(form.get('projectLocationLatitude')).toBeTruthy();
    expect(form.get('projectLocationLongitude')).toBeTruthy();

    const projectNameControl = form.get('projectName');
    projectNameControl?.setValue('');
    expect(projectNameControl?.valid).toBe(false);
    expect(projectNameControl?.errors?.['required']).toBeTruthy();
  });

  it('should handle project address change', () => {
    const mockAddress = {
      formatted_address: '123 Test St',
      geometry: {
        location: {
          lat: () => 40.7128,
          lng: () => -74.0060
        }
      }
    };

    component.handleProjectAddressChange(mockAddress);

    expect(component.location).toBe('123 Test St');
    expect(component.latitude).toBe(40.7128);
    expect(component.longitude).toBe(-74.0060);
  });

  it('should validate alphanumeric input', () => {
    const validEvent = { which: 65, keyCode: 65 }; // 'A'
    const invalidEvent = { which: 33, keyCode: 33 }; // '!'
    const numberEvent = { which: 48, keyCode: 48 }; // '0'
    const spaceEvent = { which: 32, keyCode: 32 }; // ' '

    expect(component.alphaNum(validEvent)).toBe(true); // Letter
    expect(component.alphaNum(invalidEvent)).toBe(true); // Special char (allowed)
    expect(component.alphaNum(numberEvent)).toBe(true); // Number
    expect(component.alphaNum(spaceEvent)).toBe(true); // Space
  });

  it('should handle form submission', fakeAsync(() => {
    const mockFormValue = {
      projectName: 'Test Project',
      projectLocation: '123 Test St',
      startDate: new Date(),
      endDate: new Date(),
      ParentCompanyId: '1'
    };

    // Set up required conditions
    localStorage.setItem('ProjectId', '123');
    component.ProjectId = '123';
    component.ParentCompanyId = '1';
    component.accountAdmin = false;

    component.projectAccountForm(); // Initialize the form
    component.projectDetailsForm.patchValue(mockFormValue);
    projectService.existProject.mockReturnValue(of(true));

    component.onSubmit();
    tick();

    expect(projectService.existProject).toHaveBeenCalledWith({
      id: '123',
      projectName: 'Test Project',
      ParentCompanyId: '1'
    });
    expect(component.submitted).toBe(true);
  }));

  it('should handle form submission error', fakeAsync(() => {
    const mockFormValue = {
      projectName: 'Test Project',
      projectLocation: '123 Test St',
      startDate: new Date(),
      endDate: new Date(),
      ParentCompanyId: '1'
    };

    // Set up required conditions
    localStorage.setItem('ProjectId', '123');
    component.ProjectId = '123';
    component.ParentCompanyId = '1';
    component.accountAdmin = false;

    component.projectAccountForm(); // Initialize the form
    component.projectDetailsForm.patchValue(mockFormValue);

    // Create error with the correct structure
    const mockError = {
      message: {
        details: [{
          message: 'Project name already exists'
        }]
      }
    };
    projectService.existProject.mockReturnValue(throwError(() => mockError));

    component.onSubmit();
    tick();

    expect(projectService.existProject).toHaveBeenCalledWith({
      id: '123',
      projectName: 'Test Project',
      ParentCompanyId: '1'
    });
    // expect(toastrService.error).toHaveBeenCalledWith('Project name already exists');
    expect(component.submitted).toBe(false);
  }));

  it('should close modal and validate form', () => {
    Object.defineProperty(router, 'url', { value: '/plans' });
    component.projectForm(); // Initialize the form
    component.projectDetailsForm.patchValue({ projectName: 'Test Project' });

    component.close();

    expect(toastrService.clear).toHaveBeenCalled();
    expect(component.bsModalRef.hide).toHaveBeenCalled();
  });

  it('should show error when form is invalid on close', () => {
    Object.defineProperty(router, 'url', { value: '/plans' });
    component.projectForm(); // Initialize the form
    component.projectDetailsForm.patchValue({ projectName: '' });

    component.close();

    expect(toastrService.error).toHaveBeenCalledWith(
      'Please Fill the project Details to continue.',
      'OOPS!'
    );
  });

  // Constructor and Initialization Tests
  describe('Constructor and Initialization', () => {
    it('should subscribe to projectParent and set ProjectId and ParentCompanyId', () => {
      const mockProjectParent = { ProjectId: 'test-123', ParentCompanyId: 'company-456' };
      projectService.projectParent.next(mockProjectParent);

      expect(component.ProjectId).toBe('test-123');
      expect(component.ParentCompanyId).toBe('company-456');
    });

    it('should subscribe to ParentCompanyId and update when value is valid', () => {
      projectService.ParentCompanyId.next('new-company-789');

      expect(component.ParentCompanyId).toBe('new-company-789');
    });

    it('should not update ParentCompanyId when value is undefined, null, or empty', () => {
      const originalParentCompanyId = component.ParentCompanyId;

      // Test undefined
      projectService.ParentCompanyId.next(undefined);
      expect(component.ParentCompanyId).toBe(originalParentCompanyId);

      // Test null
      projectService.ParentCompanyId.next(null);
      expect(component.ParentCompanyId).toBe(originalParentCompanyId);

      // Test empty string
      projectService.ParentCompanyId.next('');
      expect(component.ParentCompanyId).toBe(originalParentCompanyId);
    });
  });

  // Geolocation and Map Tests
  describe('Geolocation and Map Functionality', () => {
    beforeEach(() => {
      // Mock navigator.permissions
      Object.defineProperty(navigator, 'permissions', {
        value: {
          query: jest.fn()
        },
        writable: true
      });

      // Mock navigator.geolocation
      Object.defineProperty(navigator, 'geolocation', {
        value: {
          getCurrentPosition: jest.fn()
        },
        writable: true
      });
    });

    it('should set current location when geolocation permission is granted', fakeAsync(() => {
      const mockPosition = {
        coords: {
          latitude: 40.7128,
          longitude: -74.0060
        }
      };

      (navigator.permissions.query as jest.Mock).mockResolvedValue({ state: 'granted' });
      (navigator.geolocation.getCurrentPosition as jest.Mock).mockImplementation((success) => {
        success(mockPosition);
      });

      jest.spyOn(component, 'mapForProjectLocationLoader');

      component.setCurrentLocation();
      tick();

      expect(component.latitude).toBe(40.7128);
      expect(component.longitude).toBe(-74.0060);
      expect(component.mapForProjectLocationLoader).toHaveBeenCalledWith(40.7128, -74.0060);
    }));

    it('should use default location when geolocation permission is denied', fakeAsync(() => {
      (navigator.permissions.query as jest.Mock).mockResolvedValue({ state: 'denied' });
      jest.spyOn(component, 'mapForProjectLocationLoader');

      component.setCurrentLocation();
      tick();

      expect(component.mapForProjectLocationLoader).toHaveBeenCalledWith(38.897957, -77.036560);
    }));

    it('should handle missing position coordinates gracefully', fakeAsync(() => {
      const mockPosition = {
        coords: {
          latitude: null,
          longitude: null
        }
      };

      (navigator.permissions.query as jest.Mock).mockResolvedValue({ state: 'granted' });
      (navigator.geolocation.getCurrentPosition as jest.Mock).mockImplementation((success) => {
        success(mockPosition);
      });

      jest.spyOn(component, 'mapForProjectLocationLoader');
      const originalLat = component.latitude;
      const originalLng = component.longitude;

      component.setCurrentLocation();
      tick();

      expect(component.latitude).toBe(originalLat);
      expect(component.longitude).toBe(originalLng);
      expect(component.mapForProjectLocationLoader).not.toHaveBeenCalled();
    }));

    it('should handle geolocation not available', () => {
      // Remove geolocation from navigator
      Object.defineProperty(navigator, 'geolocation', {
        value: undefined,
        writable: true
      });

      jest.spyOn(component, 'mapForProjectLocationLoader');

      component.setCurrentLocation();

      expect(component.mapForProjectLocationLoader).toHaveBeenCalledWith(38.897957, -77.036560);
    });

    it('should handle geolocation permission query failure', fakeAsync(() => {
      (navigator.permissions.query as jest.Mock).mockRejectedValue(new Error('Permission query failed'));
      jest.spyOn(component, 'mapForProjectLocationLoader');

      component.setCurrentLocation();
      tick();

      expect(component.mapForProjectLocationLoader).toHaveBeenCalledWith(38.897957, -77.036560);
    }));
  });

  // Domain and User Type Tests
  describe('Domain and User Type Checking', () => {
    it('should set accountAdmin to true and call projectAccountForm when user is account admin', () => {
      authService.getUser.mockReturnValue(of({ isAccount: true }));
      jest.spyOn(component, 'projectAccountForm');

      component.checkCurrentDomain();

      expect(component.accountAdmin).toBe(true);
      expect(component.projectAccountForm).toHaveBeenCalled();
    });

    it('should set accountAdmin to false and call projectForm when user is not account admin', () => {
      authService.getUser.mockReturnValue(of({ isAccount: false }));
      jest.spyOn(component, 'projectForm');

      component.checkCurrentDomain();

      expect(component.accountAdmin).toBe(false);
      expect(component.projectForm).toHaveBeenCalled();
    });
  });

  // ngOnInit Tests
  describe('ngOnInit', () => {
    it('should subscribe to router events and hide modal', () => {
      jest.spyOn(component.bsModalRef, 'hide');

      component.ngOnInit();

      expect(component.bsModalRef.hide).toHaveBeenCalled();
    });

    it('should navigate to dashboard when checkProject returns false', () => {
      localStorage.removeItem('ProjectId');
      jest.spyOn(component, 'checkProject').mockReturnValue(false);

      component.ngOnInit();

      expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
    });

    it('should not navigate when checkProject returns true', () => {
      localStorage.setItem('ProjectId', 'test-123');
      jest.spyOn(component, 'checkProject').mockReturnValue(true);

      component.ngOnInit();

      expect(router.navigate).not.toHaveBeenCalledWith(['/dashboard']);
    });
  });

  // Map and Location Tests
  describe('Map and Location Functionality', () => {
    beforeEach(() => {
      component.map = {
        panTo: jest.fn()
      } as any;
    });

    it('should call currentLocation in mapForProjectLocationLoader', () => {
      jest.spyOn(component, 'currentLocation');

      component.mapForProjectLocationLoader(40.7128, -74.0060);

      expect(component.currentLocation).toHaveBeenCalledWith(40.7128, -74.0060);
    });

    it('should update map properties in currentLocation', () => {
      jest.spyOn(component, 'getLocationAddress');

      component.currentLocation('40.7128', '-74.0060');

      expect(component.latitude).toBe(40.7128);
      expect(component.longitude).toBe(-74.0060);
      expect(component.zoom).toBe(18);
      expect(component.mapOptions.center).toEqual({ lat: 40.7128, lng: -74.0060 });
      expect(component.marker.position).toEqual({ lat: 40.7128, lng: -74.0060 });
      expect(component.mapOptions.zoom).toBe(18);
      expect(component.getLocationAddress).toHaveBeenCalledWith(40.7128, -74.0060, null);
    });

    it('should handle markerDragEnd event', () => {
      const mockEvent = {
        latLng: {
          lat: () => 41.8781,
          lng: () => -87.6298
        }
      };
      jest.spyOn(component, 'getLocationAddress');

      component.markerDragEnd(mockEvent as any);

      expect(component.latitude).toBe(41.8781);
      expect(component.longitude).toBe(-87.6298);
      expect(component.mapOptions.center).toEqual({ lat: 41.8781, lng: -87.6298 });
      expect(component.marker.position).toEqual({ lat: 41.8781, lng: -87.6298 });
      expect(component.map.panTo).toHaveBeenCalledWith({ lat: 41.8781, lng: -87.6298 });
      expect(component.getLocationAddress).toHaveBeenCalledWith(41.8781, -87.6298, null);
    });

    it('should handle markerDragEnd with null latLng', () => {
      const mockEvent = { latLng: null };
      const originalLat = component.latitude;
      const originalLng = component.longitude;

      component.markerDragEnd(mockEvent as any);

      expect(component.latitude).toBe(originalLat);
      expect(component.longitude).toBe(originalLng);
    });

    it('should handle address location change', () => {
      const mockAddress = {
        geometry: {
          location: {
            lat: () => 34.0522,
            lng: () => -118.2437
          }
        },
        formatted_address: 'Los Angeles, CA'
      };
      jest.spyOn(component, 'getLocationAddress');

      component.handleProjectAddressLocation(mockAddress);

      expect(component.latitude).toBe(34.0522);
      expect(component.longitude).toBe(-118.2437);
      expect(component.mapOptions.center).toEqual({ lat: 34.0522, lng: -118.2437 });
      expect(component.marker.position).toEqual({ lat: 34.0522, lng: -118.2437 });
      expect(component.map.panTo).toHaveBeenCalledWith({ lat: 34.0522, lng: -118.2437 });
      expect(component.getLocationAddress).toHaveBeenCalledWith(34.0522, -118.2437, 'Los Angeles, CA');
    });
  });

  // Geocoding and Address Tests
  describe('Geocoding and Address Functionality', () => {
    beforeEach(() => {
      component.projectForm(); // Initialize form
    });

    it('should set address when provided in getLocationAddress', () => {
      const mockAddress = 'Test Address, Test City';

      component.getLocationAddress(40.7128, -74.0060, mockAddress);

      expect(component.address).toBe(mockAddress);
      expect(component.projectDetailsForm.get('projectLocation')?.value).toBe(mockAddress);
    });

    it('should geocode location when no address provided', () => {
      const mockResults = [{ formatted_address: 'Geocoded Address' }];
      component.geoCoder = {
        geocode: jest.fn().mockImplementation((request, callback) => {
          callback(mockResults, 'OK');
        })
      };

      component.getLocationAddress(40.7128, -74.0060, null);

      expect(component.geoCoder.geocode).toHaveBeenCalledWith(
        { location: { lat: 40.7128, lng: -74.0060 } },
        expect.any(Function)
      );
      expect(component.address).toBe('Geocoded Address');
      expect(component.projectDetailsForm.get('projectLocation')?.value).toBe('Geocoded Address');
      expect(component.projectDetailsForm.get('projectLocationLatitude')?.value).toBe(40.7128);
      expect(component.projectDetailsForm.get('projectLocationLongitude')?.value).toBe(-74.0060);
    });

    it('should handle geocoding failure', () => {
      component.geoCoder = {
        geocode: jest.fn().mockImplementation((request, callback) => {
          callback([], 'ZERO_RESULTS');
        })
      };

      component.getLocationAddress(40.7128, -74.0060, null);

      expect(component.geoCoder.geocode).toHaveBeenCalled();
      // Should not update form when geocoding fails
    });

    it('should handle geocoding with no results', () => {
      component.geoCoder = {
        geocode: jest.fn().mockImplementation((request, callback) => {
          callback([], 'OK');
        })
      };

      component.getLocationAddress(40.7128, -74.0060, null);

      expect(component.geoCoder.geocode).toHaveBeenCalled();
      // Should not update form when no results
    });
  });

  // Input Validation Tests
  describe('Input Validation', () => {
    it('should validate alphanumeric characters correctly', () => {
      // Test uppercase letters (A-Z: 65-90)
      expect(component.alphaNum({ which: 65, keyCode: 65 })).toBe(true); // A
      expect(component.alphaNum({ which: 90, keyCode: 90 })).toBe(true); // Z

      // Test lowercase letters (a-z: 97-122)
      expect(component.alphaNum({ which: 97, keyCode: 97 })).toBe(true); // a
      expect(component.alphaNum({ which: 122, keyCode: 122 })).toBe(true); // z

      // Test numbers (0-9: 48-57)
      expect(component.alphaNum({ which: 48, keyCode: 48 })).toBe(true); // 0
      expect(component.alphaNum({ which: 57, keyCode: 57 })).toBe(true); // 9

      // Test space (32)
      expect(component.alphaNum({ which: 32, keyCode: 32 })).toBe(true); // space

      // Test special characters that should be allowed
      expect(component.alphaNum({ which: 33, keyCode: 33 })).toBe(true); // !
      expect(component.alphaNum({ which: 46, keyCode: 46 })).toBe(true); // .
      expect(component.alphaNum({ which: 45, keyCode: 45 })).toBe(true); // -
    });

    it('should check project existence correctly', () => {
      // Test with valid ProjectId
      localStorage.setItem('ProjectId', 'test-123');
      expect(component.checkProject()).toBe(true);

      // Test with null ProjectId
      localStorage.setItem('ProjectId', 'null');
      expect(component.checkProject()).toBe(false);

      // Test with undefined ProjectId
      localStorage.removeItem('ProjectId');
      expect(component.checkProject()).toBe(false);
    });

    it('should validate string empty values', () => {
      // Test empty string
      expect(component.checkStringEmptyValues({ projectName: '' })).toBe(true);

      // Test whitespace only
      expect(component.checkStringEmptyValues({ projectName: '   ' })).toBe(true);

      // Test valid string
      expect(component.checkStringEmptyValues({ projectName: 'Valid Project' })).toBe(false);

      // Test string with leading/trailing spaces
      expect(component.checkStringEmptyValues({ projectName: '  Valid Project  ' })).toBe(false);
    });

    it('should validate alphaNum with edge cases', () => {
      // Test backspace (8)
      expect(component.alphaNum({ which: 8, keyCode: 8 })).toBe(true);

      // Test tab (9)
      expect(component.alphaNum({ which: 9, keyCode: 9 })).toBe(true);

      // Test enter (13)
      expect(component.alphaNum({ which: 13, keyCode: 13 })).toBe(true);

      // Test delete (46)
      expect(component.alphaNum({ which: 46, keyCode: 46 })).toBe(true);
    });
  });

  // Form Creation Tests
  describe('Form Creation', () => {
    it('should create project account form with all required fields', () => {
      component.projectAccountForm();

      expect(component.projectDetailsForm).toBeDefined();
      expect(component.projectDetailsForm.get('projectName')).toBeTruthy();
      expect(component.projectDetailsForm.get('projectLocation')).toBeTruthy();
      expect(component.projectDetailsForm.get('startDate')).toBeTruthy();
      expect(component.projectDetailsForm.get('endDate')).toBeTruthy();
      expect(component.projectDetailsForm.get('ParentCompanyId')).toBeTruthy();
      expect(component.formExecution).toBe(true);

      // Test required validators
      const projectNameControl = component.projectDetailsForm.get('projectName');
      projectNameControl?.setValue('');
      expect(projectNameControl?.valid).toBe(false);
      expect(projectNameControl?.errors?.['required']).toBeTruthy();
    });

    it('should create regular project form and load from localStorage', () => {
      const mockProjectData = {
        projectName: 'Saved Project',
        projectLocation: 'Saved Location'
      };
      localStorage.setItem('newproject', JSON.stringify(mockProjectData));

      component.projectForm();

      expect(component.projectDetailsForm).toBeDefined();
      expect(component.projectDetailsForm.get('projectName')).toBeTruthy();
      expect(component.projectDetailsForm.get('projectLocation')).toBeTruthy();
      expect(component.projectDetailsForm.get('projectLocationLatitude')).toBeTruthy();
      expect(component.projectDetailsForm.get('projectLocationLongitude')).toBeTruthy();
      expect(component.projectDetailsForm.get('ParentCompanyId')).toBeTruthy();
      expect(component.formExecution).toBe(true);

      // Check if localStorage data was loaded
      expect(component.projectDetailsForm.get('projectName')?.value).toBe('Saved Project');
      expect(component.projectDetailsForm.get('projectLocation')?.value).toBe('Saved Location');

      localStorage.removeItem('newproject');
    });

    it('should handle projectForm with null localStorage data', () => {
      localStorage.removeItem('newproject');

      component.projectForm();

      expect(component.projectDetailsForm).toBeDefined();
      expect(component.formExecution).toBe(true);
      // Form should be created even without localStorage data
    });
  });

  // Comprehensive onSubmit Tests
  describe('onSubmit Comprehensive Tests', () => {
    beforeEach(() => {
      component.projectForm();
      component.projectDetailsForm.patchValue({
        projectName: 'Test Project',
        projectLocation: 'Test Location'
      });
    });

    it('should return early when form is invalid', () => {
      component.projectDetailsForm.patchValue({ projectName: '' });

      component.onSubmit();

      expect(component.submitted).toBe(true);
      expect(projectService.existProject).not.toHaveBeenCalled();
    });

    it('should show error for project name with only numbers', () => {
      component.projectDetailsForm.patchValue({ projectName: '12345' });

      component.onSubmit();

      expect(toastrService.clear).toHaveBeenCalled();
      expect(toastrService.error).toHaveBeenCalledWith('Project cannot only be numbers.!');
      expect(component.submitted).toBe(false);
    });

    it('should show error for project name without letters', () => {
      component.projectDetailsForm.patchValue({ projectName: '!@#$%' });

      component.onSubmit();

      expect(toastrService.clear).toHaveBeenCalled();
      expect(toastrService.error).toHaveBeenCalledWith('Please enter valid Project Name.!');
      expect(component.submitted).toBe(false);
    });

    it('should navigate to dashboard when ProjectId is invalid and not accountAdmin', () => {
      component.ProjectId = null;
      component.accountAdmin = false;

      component.onSubmit();

      expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
    });

    it('should show error for empty project name after trim', () => {
      localStorage.setItem('ProjectId', 'test-123');
      component.ProjectId = 'test-123';
      component.ParentCompanyId = 'company-456';
      component.projectDetailsForm.patchValue({ projectName: '   ' }); // Only spaces

      component.onSubmit();

      expect(toastrService.error).toHaveBeenCalledWith('Please Enter valid Project Name.', 'OOPS!');
      expect(component.submitted).toBe(false);
    });

    it('should handle successful project creation for regular user', fakeAsync(() => {
      localStorage.setItem('ProjectId', 'test-123');
      component.ProjectId = 'test-123';
      component.ParentCompanyId = 'company-456';
      component.accountAdmin = false;
      Object.defineProperty(router, 'url', { value: '/dashboard' });

      projectService.existProject.mockReturnValue(of(true));

      component.onSubmit();
      tick();

      expect(localStorage.getItem('newproject')).toBeTruthy();
      expect(router.navigate).toHaveBeenCalledWith(['/plans']);
    }));

    it('should hide modal when on plans page for regular user', fakeAsync(() => {
      localStorage.setItem('ProjectId', 'test-123');
      component.ProjectId = 'test-123';
      component.ParentCompanyId = 'company-456';
      component.accountAdmin = false;
      Object.defineProperty(router, 'url', { value: '/plans' });

      projectService.existProject.mockReturnValue(of(true));

      component.onSubmit();
      tick();

      expect(component.bsModalRef.hide).toHaveBeenCalled();
    }));

    it('should handle different error types in existProject', fakeAsync(() => {
      localStorage.setItem('ProjectId', 'test-123');
      component.ProjectId = 'test-123';
      component.ParentCompanyId = 'company-456';

      // Test error with statusCode 400
      const error400 = { message: { statusCode: 400, details: [{ message: 'Bad request' }] } };
      projectService.existProject.mockReturnValue(throwError(() => error400));

      component.onSubmit();
      tick();

      expect(component.submitted).toBe(false);
    }));

    it('should handle error without message', fakeAsync(() => {
      localStorage.setItem('ProjectId', 'test-123');
      component.ProjectId = 'test-123';
      component.ParentCompanyId = 'company-456';

      const errorWithoutMessage = { error: 'Network error' };
      projectService.existProject.mockReturnValue(throwError(() => errorWithoutMessage));

      component.onSubmit();
      tick();

      expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
      expect(component.submitted).toBe(false);
    }));

    it('should handle error with message', fakeAsync(() => {
      localStorage.setItem('ProjectId', 'test-123');
      component.ProjectId = 'test-123';
      component.ParentCompanyId = 'company-456';

      const errorWithMessage = { message: 'Custom error message' };
      projectService.existProject.mockReturnValue(throwError(() => errorWithMessage));

      component.onSubmit();
      tick();

      expect(toastrService.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
      expect(component.submitted).toBe(false);
    }));
  });

  // Account Project Creation Tests
  describe('createAccountProject Tests', () => {
    beforeEach(() => {
      component.projectAccountForm();
      projectService.createAccountProject = jest.fn();
      projectService.refreshProjectStatus = jest.fn();
    });

    it('should create account project successfully', fakeAsync(() => {
      const mockResponse = { message: 'Project created successfully' };
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);

      component.projectDetailsForm.patchValue({
        projectName: 'Account Project',
        startDate: new Date(),
        endDate: futureDate
      });
      component.ParentCompanyId = 'company-123';

      projectService.createAccountProject.mockReturnValue(of(mockResponse));

      component.createAccountProject();
      tick();

      expect(toastrService.success).toHaveBeenCalledWith('Project created successfully');
      expect(component.bsModalRef.hide).toHaveBeenCalled();
      expect(component.submitted).toBe(false);
      expect(projectService.refreshProjectStatus).toHaveBeenCalledWith(true);
    }));

    it('should show error when end date is not higher than start date', () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);

      component.projectDetailsForm.patchValue({
        projectName: 'Account Project',
        startDate: new Date(),
        endDate: pastDate
      });

      component.createAccountProject();

      expect(toastrService.error).toHaveBeenCalledWith('End Date must be higher than Start Date.', 'OOPS!');
      expect(component.submitted).toBe(false);
    });

    it('should handle createAccountProject error with statusCode 400', fakeAsync(() => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);

      component.projectDetailsForm.patchValue({
        projectName: 'Account Project',
        startDate: new Date(),
        endDate: futureDate
      });

      const error400 = { message: { statusCode: 400, details: [{ message: 'Validation error' }] } };
      projectService.createAccountProject.mockReturnValue(throwError(() => error400));

      component.createAccountProject();
      tick();

      expect(component.submitted).toBe(false);
    }));

    it('should handle createAccountProject error without message', fakeAsync(() => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);

      component.projectDetailsForm.patchValue({
        projectName: 'Account Project',
        startDate: new Date(),
        endDate: futureDate
      });

      const errorWithoutMessage = { error: 'Network error' };
      projectService.createAccountProject.mockReturnValue(throwError(() => errorWithoutMessage));

      component.createAccountProject();
      tick();

      expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
      expect(component.submitted).toBe(false);
    }));
  });

  // Utility Methods Tests
  describe('Utility Methods', () => {
    it('should extract error message correctly in showError', () => {
      const mockError = {
        message: {
          details: [{ message: 'Detailed error message' }]
        }
      };

      component.showError(mockError);

      expect(toastrService.error).toHaveBeenCalledWith('Detailed error message');
      expect(component.submitted).toBe(false);
    });

    it('should remove localStorage and navigate in removeLocal for plans page', () => {
      localStorage.setItem('newproject', 'test');
      Object.defineProperty(router, 'url', { value: '/plans' });

      component.removeLocal();

      expect(localStorage.getItem('newproject')).toBeNull();
      expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
    });

    it('should remove localStorage and navigate in removeLocal for payment page', () => {
      localStorage.setItem('newproject', 'test');
      Object.defineProperty(router, 'url', { value: '/payment' });

      component.removeLocal();

      expect(localStorage.getItem('newproject')).toBeNull();
      expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
    });

    it('should only remove localStorage for other pages', () => {
      localStorage.setItem('newproject', 'test');
      Object.defineProperty(router, 'url', { value: '/dashboard' });

      component.removeLocal();

      expect(localStorage.getItem('newproject')).toBeNull();
      expect(router.navigate).not.toHaveBeenCalledWith(['/dashboard']);
    });

    it('should redirect and hide modal', () => {
      component.redirect('dashboard');

      expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
      expect(component.bsModalRef.hide).toHaveBeenCalled();
    });
  });

  // Close Modal Tests with Different Scenarios
  describe('Close Modal Additional Scenarios', () => {
    beforeEach(() => {
      component.projectForm();
    });

    it('should close modal for accountAdmin without validation', () => {
      component.accountAdmin = true;

      component.close();

      expect(toastrService.clear).toHaveBeenCalled();
      expect(component.bsModalRef.hide).toHaveBeenCalled();
    });

    it('should close modal for non-plans/payment URL without validation', () => {
      component.accountAdmin = false;
      Object.defineProperty(router, 'url', { value: '/dashboard' });

      component.close();

      expect(toastrService.clear).toHaveBeenCalled();
      expect(component.bsModalRef.hide).toHaveBeenCalled();
    });

    it('should close modal when form is valid on payment page', () => {
      component.accountAdmin = false;
      Object.defineProperty(router, 'url', { value: '/payment' });
      component.projectDetailsForm.patchValue({ projectName: 'Valid Project' });

      component.close();

      expect(toastrService.clear).toHaveBeenCalled();
      expect(component.bsModalRef.hide).toHaveBeenCalled();
    });

    it('should show error when project name is only whitespace on plans page', () => {
      component.accountAdmin = false;
      Object.defineProperty(router, 'url', { value: '/plans' });
      component.projectDetailsForm.patchValue({ projectName: '   ' });

      component.close();

      expect(toastrService.error).toHaveBeenCalledWith(
        'Please Fill the project Details to continue.',
        'OOPS!'
      );
    });

    it('should handle currentLocation with navigator.geolocation check', () => {
      jest.spyOn(component, 'getLocationAddress');

      // Mock navigator.geolocation to exist
      Object.defineProperty(navigator, 'geolocation', {
        value: {},
        writable: true
      });

      component.currentLocation('40.7128', '-74.0060');

      expect(component.latitude).toBe(40.7128);
      expect(component.longitude).toBe(-74.0060);
      expect(component.zoom).toBe(18);
      expect(component.getLocationAddress).toHaveBeenCalledWith(40.7128, -74.0060, null);
    });

    it('should handle currentLocation without navigator.geolocation', () => {
      jest.spyOn(component, 'getLocationAddress');

      // Remove navigator.geolocation
      Object.defineProperty(navigator, 'geolocation', {
        value: undefined,
        writable: true
      });

      const originalLat = component.latitude;
      const originalLng = component.longitude;

      component.currentLocation('40.7128', '-74.0060');

      // Should not update coordinates when geolocation is not available
      expect(component.latitude).toBe(originalLat);
      expect(component.longitude).toBe(originalLng);
      expect(component.getLocationAddress).not.toHaveBeenCalled();
    });
  });

  // Additional Method Tests for 90% Coverage
  describe('Additional Method Coverage Tests', () => {
    it('should handle handleProjectAddressChange', () => {
      const mockAddress = {
        formatted_address: 'Test Address, Test City',
        geometry: {
          location: {
            lat: () => 40.7128,
            lng: () => -74.0060
          }
        }
      };

      component.handleProjectAddressChange(mockAddress);

      expect(component.location).toBe('Test Address, Test City');
      expect(component.latitude).toBe(40.7128);
      expect(component.longitude).toBe(-74.0060);
    });

    it('should handle setCurrentLocation when permissions query fails', fakeAsync(() => {
      (navigator.permissions.query as jest.Mock).mockRejectedValue(new Error('Permission denied'));
      jest.spyOn(component, 'mapForProjectLocationLoader');

      component.setCurrentLocation();
      tick();

      expect(component.mapForProjectLocationLoader).toHaveBeenCalledWith(38.897957, -77.036560);
    }));

    it('should handle setCurrentLocation when geolocation getCurrentPosition fails', fakeAsync(() => {
      (navigator.permissions.query as jest.Mock).mockResolvedValue({ state: 'granted' });
      (navigator.geolocation.getCurrentPosition as jest.Mock).mockImplementation((success, error) => {
        error(new Error('Location unavailable'));
      });
      jest.spyOn(component, 'mapForProjectLocationLoader');

      component.setCurrentLocation();
      tick();

      // Should still call mapForProjectLocationLoader with default coordinates
      expect(component.mapForProjectLocationLoader).toHaveBeenCalled();
    }));

    it('should validate alphaNum with boundary values', () => {
      // Test boundary values for uppercase letters
      expect(component.alphaNum({ which: 64, keyCode: 64 })).toBe(false); // Before A
      expect(component.alphaNum({ which: 91, keyCode: 91 })).toBe(false); // After Z

      // Test boundary values for lowercase letters
      expect(component.alphaNum({ which: 96, keyCode: 96 })).toBe(false); // Before a
      expect(component.alphaNum({ which: 129, keyCode: 129 })).toBe(false); // After extended range

      // Test boundary values for numbers
      expect(component.alphaNum({ which: 47, keyCode: 47 })).toBe(true); // Before 0
      expect(component.alphaNum({ which: 58, keyCode: 58 })).toBe(true); // After 9

      // Test control characters
      expect(component.alphaNum({ which: 31, keyCode: 31 })).toBe(false); // Control character
    });

    it('should check project with null string value', () => {
      localStorage.setItem('ProjectId', 'null');
      expect(component.checkProject()).toBe(true); // 'null' string is truthy
    });

    it('should handle onSubmit when projectId is null but accountAdmin is true', () => {
      component.accountAdmin = true;
      component.ProjectId = null;
      component.projectAccountForm();
      component.projectDetailsForm.patchValue({
        projectName: 'Test Project',
        projectLocation: 'Test Location',
        startDate: new Date(),
        endDate: new Date(Date.now() + ********) // Tomorrow
      });

      jest.spyOn(component, 'createAccountProject');

      component.onSubmit();

      expect(component.createAccountProject).toHaveBeenCalled();
    });

    it('should handle onSubmit when checkStringEmptyValues returns true', () => {
      localStorage.setItem('ProjectId', 'test-123');
      component.ProjectId = 'test-123';
      component.ParentCompanyId = 'company-456';
      component.projectDetailsForm.patchValue({ projectName: '   ' }); // Only spaces

      jest.spyOn(component, 'removeLocal');

      component.onSubmit();

      expect(toastrService.error).toHaveBeenCalledWith('Please Enter valid Project Name.', 'OOPS!');
      expect(component.removeLocal).toHaveBeenCalled();
      expect(component.submitted).toBe(false);
    });

    it('should handle onSubmit when projectId is undefined and not accountAdmin', () => {
      localStorage.removeItem('ProjectId');
      component.ProjectId = undefined;
      component.accountAdmin = false;

      component.onSubmit();

      expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
    });

    it('should handle onSubmit else branch when projectId is undefined', () => {
      localStorage.removeItem('ProjectId');
      component.ProjectId = 'test-123';
      component.accountAdmin = false;

      jest.spyOn(component, 'removeLocal');

      component.onSubmit();

      expect(component.removeLocal).toHaveBeenCalled();
    });

    it('should handle createAccountProject with equal start and end dates', () => {
      component.projectAccountForm();
      const sameDate = new Date();
      component.projectDetailsForm.patchValue({
        projectName: 'Test Project',
        startDate: sameDate,
        endDate: sameDate
      });

      component.createAccountProject();

      expect(toastrService.error).toHaveBeenCalledWith('End Date must be higher than Start Date.', 'OOPS!');
      expect(component.submitted).toBe(false);
    });

    it('should handle createAccountProject error with message but no statusCode', fakeAsync(() => {
      component.projectAccountForm();
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);

      component.projectDetailsForm.patchValue({
        projectName: 'Test Project',
        startDate: new Date(),
        endDate: futureDate
      });

      const errorWithMessage = { message: 'Custom error without statusCode' };
      projectService.createAccountProject.mockReturnValue(throwError(() => errorWithMessage));

      component.createAccountProject();
      tick();

      expect(toastrService.error).toHaveBeenCalledWith('Custom error without statusCode', 'OOPS!');
      expect(component.submitted).toBe(false);
    }));

    it('should handle showError with complex error structure', () => {
      const mockError = {
        message: {
          details: [{
            field: 'projectName',
            message: 'Project name is required',
            code: 'REQUIRED'
          }]
        }
      };

      component.showError(mockError);

      expect(toastrService.error).toHaveBeenCalledWith('Project name is required');
      expect(component.submitted).toBe(false);
    });

    it('should handle projectForm with invalid JSON in localStorage', () => {
      localStorage.setItem('newproject', 'invalid-json');

      expect(() => component.projectForm()).not.toThrow();
      expect(component.projectDetailsForm).toBeDefined();
      expect(component.formExecution).toBe(true);
    });

    it('should handle projectForm with empty object in localStorage', () => {
      localStorage.setItem('newproject', '{}');

      component.projectForm();

      expect(component.projectDetailsForm).toBeDefined();
      expect(component.formExecution).toBe(true);
    });

    it('should handle getLocationAddress with empty results array', () => {
      component.projectForm();
      component.geoCoder = {
        geocode: jest.fn().mockImplementation((_request, callback) => {
          callback([], 'OK');
        })
      };

      component.getLocationAddress(40.7128, -74.0060, null);

      expect(component.geoCoder.geocode).toHaveBeenCalled();
      // Should not update form when no results
    });

    it('should handle alphaNum with keyCode instead of which', () => {
      // Test when which is 0 but keyCode has value
      expect(component.alphaNum({ which: 0, keyCode: 65 })).toBe(true); // A
      expect(component.alphaNum({ which: 0, keyCode: 32 })).toBe(true); // Space
      expect(component.alphaNum({ which: 0, keyCode: 48 })).toBe(true); // 0
    });

    it('should handle markerDragEnd without map reference', () => {
      component.map = null;
      const mockEvent = {
        latLng: {
          lat: () => 41.8781,
          lng: () => -87.6298
        }
      };
      jest.spyOn(component, 'getLocationAddress');

      expect(() => component.markerDragEnd(mockEvent as any)).toThrow();
    });

    it('should handle handleProjectAddressLocation without map reference', () => {
      component.map = null;
      const mockAddress = {
        geometry: {
          location: {
            lat: () => 34.0522,
            lng: () => -118.2437
          }
        },
        formatted_address: 'Los Angeles, CA'
      };

      expect(() => component.handleProjectAddressLocation(mockAddress)).toThrow();
    });

    // Targeted tests for specific uncovered lines to reach 90% coverage

    // Line 211: this.map.panTo(this.marker.position) in handleProjectAddressLocation
    it('should call map.panTo in handleProjectAddressLocation', () => {
      component.map = { panTo: jest.fn() } as any;
      const mockAddress = {
        geometry: { location: { lat: () => 40.7128, lng: () => -74.0060 } },
        formatted_address: 'Test Address'
      };
      jest.spyOn(component, 'getLocationAddress');

      component.handleProjectAddressLocation(mockAddress);

      expect(component.map.panTo).toHaveBeenCalledWith({ lat: 40.7128, lng: -74.0060 });
    });

    // Lines 216-217: address branch in getLocationAddress
    it('should set address when provided in getLocationAddress', () => {
      component.projectForm();
      const mockAddress = 'Test Address';

      component.getLocationAddress(40.7128, -74.0060, mockAddress);

      expect(component.address).toBe(mockAddress);
      expect(component.projectDetailsForm.get('projectLocation')?.value).toBe(mockAddress);
    });

    // Line 240: return false in alphaNum
    it('should return false for invalid characters in alphaNum', () => {
      // Test character that fails all conditions
      const result = component.alphaNum({ which: 127, keyCode: 127 });
      expect(result).toBe(false);
    });

    // Line 264: early return when form is invalid
    it('should return early when form is invalid in onSubmit', () => {
      component.projectForm();
      component.projectDetailsForm.patchValue({ projectName: '' });
      jest.spyOn(projectService, 'existProject');

      component.onSubmit();

      expect(component.submitted).toBe(true);
      expect(projectService.existProject).not.toHaveBeenCalled();
    });

    // Lines 268-271: project name only numbers validation
    it('should handle project name with only numbers', () => {
      component.projectForm();
      component.projectDetailsForm.patchValue({ projectName: '12345' });

      component.onSubmit();

      expect(toastrService.clear).toHaveBeenCalled();
      expect(toastrService.error).toHaveBeenCalledWith('Project cannot only be numbers.!');
      expect(component.submitted).toBe(false);
    });

    // Lines 275-278: project name without letters validation
    it('should handle project name without letters', () => {
      component.projectForm();
      component.projectDetailsForm.patchValue({ projectName: '123!@#' });

      component.onSubmit();

      expect(toastrService.clear).toHaveBeenCalled();
      expect(toastrService.error).toHaveBeenCalledWith('Please enter valid Project Name.!');
      expect(component.submitted).toBe(false);
    });

    // Line 286: navigate to dashboard when ProjectId conditions met
    it('should navigate to dashboard when ProjectId is null and not accountAdmin', () => {
      component.projectForm();
      component.projectDetailsForm.patchValue({ projectName: 'Valid Project' });
      component.ProjectId = null;
      component.accountAdmin = false;

      component.onSubmit();

      expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
    });

    // Line 294: createAccountProject call when accountAdmin is true
    it('should call createAccountProject when accountAdmin is true', fakeAsync(() => {
      localStorage.setItem('ProjectId', 'test-123');
      component.projectAccountForm();
      component.projectDetailsForm.patchValue({
        projectName: 'Account Project',
        startDate: new Date(),
        endDate: new Date(Date.now() + ********)
      });
      component.ProjectId = 'test-123';
      component.ParentCompanyId = 'company-456';
      component.accountAdmin = true;

      projectService.existProject.mockReturnValue(of(true));
      jest.spyOn(component, 'createAccountProject');

      component.onSubmit();
      tick();

      expect(component.createAccountProject).toHaveBeenCalled();
    }));

    // Line 299: hide modal when on plans page
    it('should hide modal when on plans page', fakeAsync(() => {
      localStorage.setItem('ProjectId', 'test-123');
      component.projectForm();
      component.projectDetailsForm.patchValue({ projectName: 'Valid Project' });
      component.ProjectId = 'test-123';
      component.ParentCompanyId = 'company-456';
      component.accountAdmin = false;
      Object.defineProperty(router, 'url', { value: '/plans/test' });

      projectService.existProject.mockReturnValue(of(true));

      component.onSubmit();
      tick();

      expect(component.bsModalRef.hide).toHaveBeenCalled();
    }));

    // Line 308: showError call when statusCode is 400
    it('should call showError when existProject error has statusCode 400', fakeAsync(() => {
      localStorage.setItem('ProjectId', 'test-123');
      component.projectForm();
      component.projectDetailsForm.patchValue({ projectName: 'Valid Project' });
      component.ProjectId = 'test-123';
      component.ParentCompanyId = 'company-456';

      const error400 = { message: { statusCode: 400, details: [{ message: 'Error' }] } };
      projectService.existProject.mockReturnValue(throwError(() => error400));
      jest.spyOn(component, 'showError');

      component.onSubmit();
      tick();

      expect(component.showError).toHaveBeenCalledWith(error400);
    }));

    // Line 310: error handling when no message
    it('should show generic error when existProject error has no message', fakeAsync(() => {
      localStorage.setItem('ProjectId', 'test-123');
      component.projectForm();
      component.projectDetailsForm.patchValue({ projectName: 'Valid Project' });
      component.ProjectId = 'test-123';
      component.ParentCompanyId = 'company-456';

      const errorNoMessage = { error: 'Network error' };
      projectService.existProject.mockReturnValue(throwError(() => errorNoMessage));

      component.onSubmit();
      tick();

      expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    }));

    // Lines 318-320: checkStringEmptyValues returns true
    it('should handle checkStringEmptyValues returning true', () => {
      localStorage.setItem('ProjectId', 'test-123');
      component.projectForm();
      component.projectDetailsForm.patchValue({ projectName: '   ' }); // Only spaces
      component.ProjectId = 'test-123';
      component.ParentCompanyId = 'company-456';

      jest.spyOn(component, 'removeLocal');

      component.onSubmit();

      expect(toastrService.error).toHaveBeenCalledWith('Please Enter valid Project Name.', 'OOPS!');
      expect(component.removeLocal).toHaveBeenCalled();
      expect(component.submitted).toBe(false);
    });

    // Line 323: removeLocal call in else branch
    it('should call removeLocal in else branch', () => {
      localStorage.removeItem('ProjectId');
      component.projectForm();
      component.projectDetailsForm.patchValue({ projectName: 'Valid Project' });
      component.ProjectId = 'test-123';
      component.accountAdmin = false;

      jest.spyOn(component, 'removeLocal');

      component.onSubmit();

      expect(component.removeLocal).toHaveBeenCalled();
    });

    // Lines 327-377: createAccountProject method coverage
    it('should handle createAccountProject success', fakeAsync(() => {
      component.projectAccountForm();
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);

      component.projectDetailsForm.patchValue({
        projectName: 'Account Project',
        startDate: new Date(),
        endDate: futureDate
      });
      component.ParentCompanyId = 'company-123';

      const mockResponse = { message: 'Project created successfully' };
      projectService.createAccountProject.mockReturnValue(of(mockResponse));

      component.createAccountProject();
      tick();

      expect(toastrService.success).toHaveBeenCalledWith('Project created successfully');
      expect(component.bsModalRef.hide).toHaveBeenCalled();
      expect(component.submitted).toBe(false);
      expect(projectService.refreshProjectStatus).toHaveBeenCalledWith(true);
    }));

    it('should handle createAccountProject with statusCode 400 error', fakeAsync(() => {
      component.projectAccountForm();
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);

      component.projectDetailsForm.patchValue({
        projectName: 'Account Project',
        startDate: new Date(),
        endDate: futureDate
      });

      const error400 = { message: { statusCode: 400, details: [{ message: 'Validation error' }] } };
      projectService.createAccountProject.mockReturnValue(throwError(() => error400));
      jest.spyOn(component, 'showError');

      component.createAccountProject();
      tick();

      expect(component.showError).toHaveBeenCalledWith(error400);
      expect(component.submitted).toBe(false);
    }));

    it('should handle createAccountProject with no message error', fakeAsync(() => {
      component.projectAccountForm();
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);

      component.projectDetailsForm.patchValue({
        projectName: 'Account Project',
        startDate: new Date(),
        endDate: futureDate
      });

      const errorNoMessage = { error: 'Network error' };
      projectService.createAccountProject.mockReturnValue(throwError(() => errorNoMessage));

      component.createAccountProject();
      tick();

      expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
      expect(component.submitted).toBe(false);
    }));

    it('should handle createAccountProject with message error', fakeAsync(() => {
      component.projectAccountForm();
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);

      component.projectDetailsForm.patchValue({
        projectName: 'Account Project',
        startDate: new Date(),
        endDate: futureDate
      });

      const errorWithMessage = { message: 'Custom error message' };
      projectService.createAccountProject.mockReturnValue(throwError(() => errorWithMessage));

      component.createAccountProject();
      tick();

      expect(toastrService.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
      expect(component.submitted).toBe(false);
    }));

    it('should handle createAccountProject when end date is not higher than start date', () => {
      component.projectAccountForm();
      const sameDate = new Date();

      component.projectDetailsForm.patchValue({
        projectName: 'Account Project',
        startDate: sameDate,
        endDate: sameDate
      });

      component.createAccountProject();

      expect(toastrService.error).toHaveBeenCalledWith('End Date must be higher than Start Date.', 'OOPS!');
      expect(component.submitted).toBe(false);
    });

    // Line 382: checkStringEmptyValues with empty string after trim
    it('should return true for checkStringEmptyValues with empty string after trim', () => {
      const formValue = { projectName: '   ' }; // Only spaces

      const result = component.checkStringEmptyValues(formValue);

      expect(result).toBe(true);
    });

    // Test showError method (lines 360-365)
    it('should extract error message correctly in showError', () => {
      const mockError = {
        message: {
          details: [{ message: 'Detailed error message' }]
        }
      };

      component.showError(mockError);

      expect(toastrService.error).toHaveBeenCalledWith(['Detailed error message']);
      expect(component.submitted).toBe(false);
    });

    // Test removeLocal method with different URL scenarios
    it('should navigate to dashboard when URL is plans in removeLocal', () => {
      Object.defineProperty(router, 'url', { value: '/plans/test' });

      component.removeLocal();

      expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
    });

    it('should navigate to dashboard when URL is payment in removeLocal', () => {
      Object.defineProperty(router, 'url', { value: '/payment/test' });

      component.removeLocal();

      expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
    });

    it('should not navigate when URL is neither plans nor payment in removeLocal', () => {
      Object.defineProperty(router, 'url', { value: '/dashboard' });
      router.navigate.mockClear();

      component.removeLocal();

      expect(router.navigate).not.toHaveBeenCalled();
    });

    // Test projectForm with JSON parse error handling
    it('should handle projectForm with invalid JSON in localStorage', () => {
      localStorage.setItem('newproject', 'invalid-json');

      expect(() => component.projectForm()).not.toThrow();
      expect(component.formExecution).toBe(true);
    });
  });
});
