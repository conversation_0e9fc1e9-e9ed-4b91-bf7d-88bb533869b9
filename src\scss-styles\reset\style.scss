/****** Common STyle *****************/
*:focus {
  outline: 0;
}

*:checked {
  outline: 0;
}

a {
  text-decoration: none;
  &:hover {
    text-decoration: none;
    color: $orange;
  }

  &:focus {
    outline: 0;
  }
}

body {
  font-family: 'Cairo', sans-serif !important;
  background: $grey10;
}

/****** scroll bar css *****************/
*::-webkit-scrollbar {
  width: 3px;
  height: 10px;
  background: $white;
}

*::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
}

*::-webkit-scrollbar-thumb {
  background-color: $orange;
  border-radius: 0;
}

/****** scroll bar css *****************/

.border-white {
  border-color: $white;
}

.border-gray {
  border-color: $gray;
}

.c-pointer {
  cursor: pointer;
}

.op-90 {
  opacity: 0.9;
}

.op-32 {
  opacity: 0.32;
}

.page-content {
  width: 100%;
  transition: 1s;
  @extend .pt59;

  &.admin-layout {
    min-height: 100vh;
  }
}

/****** Table Layout *****************/
.table-responsive {
  overflow-y: hidden;
}
.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #dee2e6;
  border-top: 1px solid #dee2e6;
}
.table td {
  border-top: 1px solid #dee2e6;
}

/* Global table-custom styling */
.table.table-custom {
  td {
    padding: 5px 0.75rem;
    height: auto;
  }

  tbody tr:nth-child(even) {
    background-color: transparent;
  }
}

.custom-primary-tab {
  .tab-container {
    .nav-tabs {
      background: $white;
      margin-bottom: 12px;
      border-bottom: 0;

      .nav-item {
        .nav-link {
          border: 0;
          background: transparent;

          @extend .fs12;

          color: $grey8;
          font-weight: 600;
          position: relative;

          &.active {
            color: $grey7;
            font-weight: bold;

            &::before {
              content: '';
              position: absolute;
              background: $orange;
              height: $value6;
              box-shadow: 3px 0 9px #f45e286c;
              bottom: 0;
              left: 0;
              right: 0;
              margin: 0 auto;
              width: calc(100% - 20px);
            }
          }
        }
      }
    }
  }
}

.custom-secondary-tab {
  .tab-container {
    .nav-tabs {
      background: $white;
      margin-bottom: 12px;
      border-bottom: 1.5px solid $orange;
      padding: 0px 25px;

      .nav-item {
        &:not(:last-child) {
          margin-right: 30px;
        }

        .nav-link {
          border: 0;
          background: transparent;
          @extend .fs12;
          color: $grey8;
          font-weight: 600;
          position: relative;
          padding: 0.5rem 0px;

          &.active {
            color: $grey7;
            font-weight: bold;
          }
        }
      }
    }
  }
}

/******************** Auth Section Styles **********************/

.card-container::after,
.auth-content {
  background: url('../../assets/images/auth-bg.svg') no-repeat bottom left;

  background-size: cover;
  background-attachment: fixed;
  position: relative;
}

.auth-content {
  min-height: 100vh;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
  }

  .auth-overlay {
    min-height: 100vh;
  }
}

.login-container,
.fotgot-container,
.reset-container {
  height: calc(100vh - 200px);
}

.terms-login-span {
  color: $orange;
}

.card-container {
  border-radius: 14px;
  overflow: hidden;
  position: relative;

  .card-header {
    padding: 10px 20px;
    border-bottom: 1px solid $white;
    background: $white;

    img {
      width: 150px;
    }
  }

  .card-body {
    position: relative;
  }

  &::after {
    content: '';
    background-size: cover;
    background-repeat: no-repeat;
    background-attachment: fixed;
    z-index: -2;
    position: absolute;
    width: 100%;
    top: 0;
    height: 100%;
    -webkit-filter: blur(30px);
    filter: blur(30px);
  }

  .form-control {
    &::placeholder {
      color: $grey43;
    }

    &:focus {
      box-shadow: none;
    }
  }
  .input-group .input-group-text {
    background-color: transparent;
    border-color: $grey41;
  }
}

/******************** Auth Section Styles **********************/

/********************  Add Employee Style Starts *******************/

.user-image {
  top: -18px;
  height: 87px;
  width: 87px;
  border: 2px solid $gray;

  img {
    height: 87px;
    width: 87px;
    object-fit: cover;

    &.default-img {
      object-fit: contain;
      height: 45px;
      width: 45px;
      margin-top: 18px;
    }
  }
}

.details-card {
  .icon {
    height: 45px;
    width: 45px;

    @extend .bg-blue3;

    top: 3px;

    @extend .p10;

    color: $navy-blue;
  }
}

/******************** / Add Employee Style Starts *****************/

/********************* Ngx pagination style change ***************/
.ngx-pagination {
  background: $grey19;
  display: inline-block;
  text-align: center;

  @extend .pl0;

  border-radius: 50px !important;

  a,
  li {
    line-height: 28px;
    height: 36px;
    width: 36px;

    @extend .fs13;

    border-radius: 50% !important;
  }

  li {
    &.current {
      background: $orange;
    }

    &:first-child {
      position: absolute;
      left: -50px;
      background: $grey19;

      &:hover {
        background: $orange !important;

        @extend .c-pointer;

        a,
        a::before,
        &.disabled::before {
          background: $orange !important;
          color: $white !important;
          border-color: $white;
        }
      }

      a::before,
      &.disabled::before {
        content: '';
        margin: 0;
        width: 7px;
        height: 7px;
        border-top: 1px solid #333;
        border-right: 1px solid #333;
        -moz-transform: rotate(-135deg);
        -webkit-transform: rotate(-135deg);
        transform: rotate(-135deg);
      }
    }

    &:last-child {
      position: absolute;
      right: -50px;
      background: $grey19;

      &:hover {
        background: $orange !important;

        @extend .c-pointer;

        a,
        a::after,
        &.disabled::after {
          background: $orange !important;
          color: $white;
          border-color: $white;
        }
      }

      a::after,
      &.disabled::after {
        content: '';
        margin: 0;
        width: 7px;
        height: 7px;
        border-top: 1px solid #333;
        border-right: 1px solid #333;
        -moz-transform: rotate(45deg);
        -webkit-transform: rotate(45deg);
        transform: rotate(45deg);
      }
    }
  }
}

/********************* Ngx pagination style change ***************/

// datepicker color change
.theme-green {
  .bs-datepicker-head {
    background-color: $orange;
  }

  .bs-datepicker-body {
    table {
      td {
        &.week {
          span {
            color: $orange;
          }
        }

        span {
          &.selected {
            background-color: $orange;
          }
        }
      }
    }
  }
}

// datepicker color change

// Login social

.icon-bar {
  position: fixed;
  top: 50%;
  right: 0;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);

  li {
    display: block;
    text-align: center;
    padding: 16px;
    transition: all 0.3s ease;
    color: $white;
    font-size: 20px;

    @extend .c-pointer;

    &.facebook {
      background: $blue9;
      color: $white;
    }

    &.twitter {
      background: $blue10;
      color: $white;
    }

    &.google {
      background: $red5;
      color: $white;
    }

    &.linkedin {
      background: $blue7;
      color: $white;
    }

    &:hover {
      background-color: $black;
    }
  }
}

/* Status Color Start */
.status {
  width: 81px;
  height: 24px;
  font-size: 11px;
  border-radius: 12px;
  text-align: center;
  line-height: 2;
  font-weight: 600;
}

.status-approved {
  background-color: $green7;
  width: 81px;
  height: 24px;
  color: $green3;
  font-size: 11px;
  border-radius: 12px;
  text-align: center;
  line-height: 2;
  font-weight: 600;
}

.status-expired {
  background-color: $grey2;
  width: 81px;
  height: 24px;
  color: $black;
  font-size: 11px;
  border-radius: 12px;
  text-align: center;
  line-height: 2;
  font-weight: 600;
}

.status-tentative {
  background-color: $yellow3;
  height: 24px;
  color: $yellow;
  font-size: 11px;
  border-radius: 12px;
  text-align: center;
  line-height: 2;
  font-weight: 600;
  padding: 2px 10px;
}

.status-declined {
  background-color: $red10;
  width: 81px;
  height: 24px;
  color: $red1;
  font-size: 11px;
  border-radius: 12px;
  text-align: center;
  line-height: 2;
  font-weight: 600;
}

.status-pending {
  background-color: $orange1;
  width: 81px;
  height: 24px;
  color: $orange-light;
  font-size: 11px;
  border-radius: 12px;
  text-align: center;
  line-height: 2;
  font-weight: 600;
}

.status-delivered {
  background-color: $blue6;
  width: 81px;
  height: 24px;
  color: $white;
  font-size: 11px;
  border-radius: 12px;
  text-align: center;
  line-height: 2;
  font-weight: 600;
}

/* Status Color End */

/* Page Content */
.page-section {
  @extend .p20;

  .page-card {
    .page-title {
      border-left: 3px solid $navy-blue;

      @extend .pl10;

      font-weight: 500;
    }
  }

  .page-inner-content {
    .top-header {
      .top-filter {
        .filter-icon {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background-color: $white;
          box-shadow: 0 3px 11px #bd9fbd1d;
          padding: 9px;

          @extend .c-pointer;

          .icon {
            margin: auto;
            display: block;
          }
        }

        .filter-count {
          top: -5px;
          right: -5px;
          width: 15px;
          height: 15px;
        }

        .search-icon {
          position: relative;
          width: 32px;
          height: 32px;
          background: $white;
          border-radius: 50%;
          box-shadow: 0px 2px 11px #bd9fbd1d;

          @extend .c-pointer;

          .input-search {
            width: 0;
            height: 32px;
            border: 0;
            border-radius: 50%;

            @extend .c-pointer;

            &::placeholder {
              padding-left: 30px;
              padding-right: 30px;
            }
          }

          .icon {
            position: absolute;
            top: 3px;
            right: 10px;
          }

          &:hover .input-search {
            width: 200px;
            position: absolute;
            border-radius: 20px;
            right: 0;
            top: 0;
            padding-right: 35px;

            &::placeholder {
              padding-left: 5px;
              padding-right: 5px;
            }
          }

          .input-hover-disable {
            width: 200px;
            position: absolute;
            border-radius: 20px;
            right: 0;
            top: 0;
            padding-right: 35px;

            &::placeholder {
              padding-left: 5px;
              padding-right: 5px;
            }
          }
        }
      }
    }

    .tab-grid {
      .member-img {
        width: $value35;
        height: $value35;
        border-radius: 50%;
        margin-right: 10px;
      }
    }
  }

  .delivery-request-form {
    .material-input {
      @extend .fs10;

      &::placeholder {
        @extend .fs10;
      }
    }
  }
}

/* Page Content */

/* Delivery Details */
.modal-body .delivery-details-content {
  overflow-y: auto;
}

.delivery-details-content {
  max-height: 500px;
  overflow-x: hidden;

  .tab-container {
    .nav-tabs {
      background-color: $grey13;
      margin-bottom: 12px;
      border-bottom: 0;

      .nav-item {
        .nav-link {
          border: 0;
          background: transparent;

          @extend .fs12;

          color: $grey8;
          font-weight: 600;
          position: relative;

          &.active {
            color: $grey7;
            font-weight: bold;

            &::before {
              content: '';
              position: absolute;
              background: $orange;
              height: $value7;
              box-shadow: 3px 0 9px #f45e286c;
              bottom: 0;
              left: 0;
              right: 0;
              margin: 0 auto;
            }
          }
        }
      }
    }

    .history-list {
      .list-item {
        border-width: 1px 0 0;
        border-radius: 0;
        border-color: $grey12;

        &:first-child {
          border-width: 0;
        }

        &:last-child {
          border-width: 1px 0 1px;
        }
      }
    }
  }
}

.tag-layout {
  border: 1px solid $grey28;
  font-size: 12px;
  font-family: 'Cairo', sans-serif !important;
  padding: 0px 3px;

  &::placeholder {
    font-family: 'Cairo', sans-serif !important;
  }
  delete-icon {
    &:hover {
      transform: none !important;
    }
  }
}

.fc .fc-daygrid-body-unbalanced .fc-daygrid-day-events {
  min-height: 3em !important;
}

/* Delivery Details */

/* ngx dropzone */
.ngx-file-drop__drop-zone {
  border: 2px dashed $grey16 !important;
  box-shadow: 0 3px 6px #00000029;
  border-radius: 20px !important;
  height: 160px !important;
}

.ngx-file-drop__content {
  display: block !important;
  height: 160px !important;

  input {
    display: block;
    opacity: 0;
  }

  .bulkupload-content {
    padding: 20px;
    cursor: pointer;
  }
}

.upload-table {
  .list-group-item {
    border-width: 0 0 1px 0;
  }

  .upload-img {
    max-width: 50px;
  }

  .dropdown-toggle::after {
    display: none;
  }

  .upload-option {
    right: 155px;
    top: 15px;
  }
}

.upload-table.upload-layout {
  border-bottom: 1px solid #ced4da;
}

/* ngx dropzone */
.responsible-list {
  height: $value50;
  border: 1px solid $grey11;
  padding: 5px;

  .custom-badge {
    background: $grey18;
  }
}

/* Switch */
.follo-switch {
  .switch {
    &.switch-medium {
      height: 17px !important;
      width: $value35 !important;
      min-width: $value30;
      &.checked {
        background-color: $green4 !important;

        small {
          left: 20px !important;
        }
      }
    }
  }
}

/* Switch */

/******************** / Home Style Starts *****************/
.home-content {
  .inner-content {
    padding: 100px 95px;

    .content-left {
      .content-heading {
        font-size: 40px;

        @extend .cairo-regular;
        @extend .text-black;

        font-weight: bold;
        margin-bottom: 20px;
      }

      .content-heading-sub {
        color: $orange;
        font-size: 40px;
        font-weight: bold;

        @extend .cairo-regular;
      }
    }
  }
}

.custom-select.no-arrow {
  height: 29px;
}

.custom-select.no-arrow.member-no-arrow {
  height: 28px;
}

/* Plan */

.plan-card {
  background: transparent linear-gradient(216deg, #fff 0%, #ffebe4 100%);
  opacity: 1;
  min-height: 100vh;

  .card {
    min-height: 370px;
  }

  span {
    &.text-white {
      position: relative;
      z-index: 0;

      &::before {
        content: '';
        background: $orange;
        width: $value55;
        height: $value55;
        border-radius: 28px !important;
        position: absolute;
        left: -27px;
        top: -23px;
        z-index: -1;
      }
    }
  }

  .enterprise-plan {
    span.text-white {
      &::before {
        left: -18px;
      }
    }
  }

  .price-shadow {
    &:hover {
      box-shadow: 0 15px 17px #00000026;
      opacity: 1;
    }
  }

  .contact-sale-btn {
    width: $value180;
    margin: 0 auto;
  }

  .plan-bill {
    opacity: 0.5;
  }

  .plan-info {
    line-height: $value30;
    text-align: left;
  }
}
.switch-toggle {

  .form-check-label {
    &::after {
      background-color: $white;
    }

    &::before {
      color: $white;
      border-color: $grey27;
      background-color: $grey27;
    }
  }

  .form-check-input {
    &:checked {
      ~ {
        .form-check-label {
          &::before {
            color: $white;
            border-color: $grey27;
            background-color: $grey27;
          }
        }
      }
    }
  }
}
/******************** / Home Style End *****************/

/******** Payment ******************/
.payment-content {
  background: url('../../assets/images/payment-bg.png') no-repeat bottom left #fff;
  background-size: contain;
  background-attachment: fixed;
  position: relative;
  height: 100vh;

  .custom-select-arrow {
    background: url('../../assets/images/down-arrow-grey.png') no-repeat right 1.7rem center/8px 6px;
    -webkit-appearance: none;
    appearance: none;
  }

  .form-control {
    border-color: $grey25;

    &::placeholder {
      color: $grey26;
    }
  }
}

.thankyou-content {
  background: transparent linear-gradient(216deg, #fff 0%, #ffebe4 100%) 0% 0% no-repeat padding-box;
  width: 100%;
  height: 100vh;
}

/******** Payment *****************/
.thanks-popup {
  .success-tick {
    width: $value75;
    height: $value75;
    border-radius: 100%;
    border: 5px solid $orange;
    margin: 0 auto 40px;

    .tick-icon {
      line-height: 2;
    }
  }
}

/* Upload */
.upload-profile {
  .member {
    position: relative;
    width: $value70;
    height: $value70;

    .default-profile {
      background-color: $grey9;
      border-radius: 50%;
      border: 1px solid $grey24;
      width: $value70;
      height: $value70;

      .profile-img {
        margin: 16px auto;
        display: block;
      }
    }

    .original-profile {
      max-height: $value70;
      max-width: $value70;
      overflow: hidden;
      border-radius: 10px;
      vertical-align: middle;
      display: table-cell;

      img {
        margin: 0 auto;
        display: block;
        max-width: 100%;
        max-height: $value70;
        object-fit: fill;
        border-radius: 10px;
      }
    }

    .image-upload {
      width: $value30;
      height: $value30;
      cursor: pointer;
      position: absolute;
      right: 0;
      bottom: -10px;
      background: $white;
      box-shadow: 0 3px 6px $black1;
      border-radius: 100%;

      input {
        display: none;
      }

      .upload-icon {
        margin: 10px;
      }
    }
  }
}

/* settings-content */
.settings-content {
  border: 1px solid $grey12;

  .settings-tab {
    .edit-profile {
      padding: 0 20px;
    }

    @extend .custom-primary-tab;

    .tab-container {
      .nav-tabs {
        .nav-link {
          &.active {
            color: $orange !important;
          }
        }
      }
    }

    .history-list {
      .list-item {
        border-width: 1px 0 0;
        border-radius: 0;
        border-color: $grey12;

        &:first-child {
          border-width: 0;
        }

        &:last-child {
          border-width: 1px 0 1px;
        }
      }
    }
  }
}

/* settings-content */

/* Companies-content */
.member.cmpny-profile {
  width: $value110;
  height: $value60;
}

.upload-cmpny-img {
  width: 100px;
  height: 70px;
  object-fit: cover;
}

.upload-profile .member .default-profile.rec-default-profile {
  border-radius: $value5;
  width: $value100;
  height: $value60;
}

.card-container.cmpny-card {
  background: $white;
  border-radius: $value5;
  min-height: $value155;
}

.card-container.card-container.cmpny-card:hover {
  box-shadow: 0 10px 10px $black2;
}

.card-container.cmpny-card::after {
  background: none;
}

.card-container.cmpny-card::before {
  left: 0;
  position: absolute;
  content: '';
  width: $value8;
  height: 100%;
}

.red-card.card-container.cmpny-card::before {
  background: $red2;
}

.blue-card.card-container.cmpny-card::before {
  background: $blue5;
}

.orange-card.card-container.cmpny-card::before {
  background: $lightorange;
}

.green-card.card-container.cmpny-card::before {
  background: $green5;
}

.card-column.card-container.cmpny-card h1 {
  color: $dark-blue;
}

.content-img {
  max-width: 100%;
  width: $value110;
  height: $value40;
  object-fit: contain;
}

.user-profile {
  width: $value30;
  height: $value30;
  object-fit: contain;
}

.plus-icon {
  color: $orange;
}

.modal {
  .modal-content {
    .company-select {
      .multiselect-dropdown {
        .dropdown-btn {
          border-top: 0;
          border-left: 0;
          border-right: 0;
          font-size: 12px;
          border-radius: 0;
          padding: 4px 0px;
          border-bottom: 1px solid $light-grey8;
        }
      }

      .dropdown-list {
        margin: 0;
        font-size: 12px;

        li.no-data {
          text-align: center;

          h5 {
            font-size: 12px;
          }
        }

        .multiselect-item-checkbox input[type='checkbox'] + div {
          &::before {
            color: $orange;
            border: 2px solid $orange;
          }
        }

        .multiselect-item-checkbox input[type='checkbox']:checked + div {
          &::before {
            background: $orange;
          }
        }

        .multiselect-dropdown .dropdown-btn .selected-item {
          border: 1px solid $orange;
          background: $orange;
        }
      }
    }

    .row {
      .company-select {
        .multiselect-dropdown {
          .dropdown-btn {
            .dropdown-down {
              border-top: 7px solid $grey46;
              border-left: 7px solid transparent;
              border-right: 7px solid transparent;
            }

            .dropdown-up {
              border-bottom: 7px solid $grey46;
              border-left: 7px solid transparent;
              border-right: 7px solid transparent;
            }

            .selected-item {
              border: 1px solid $orange !important;
              background: $orange !important;
              margin-bottom: 7px;
              word-wrap: break-word;
              width: 100% !important;
              max-width: 100% !important;
              font-size: 11px !important;
            }
          }
        }
      }
    }
  }
}

.side-menu-checkbox {
  width: 12px; /* Reduced size */
  height: 12px; /* Reduced size */
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  outline: none;
  background-color: white;
  border: 2px solid #f45e28;
  cursor: pointer;
  border-radius: 2px;
  display: inline-block;
  vertical-align: middle;
  position: relative;
  top: -1px;
  margin-right: 6px; /* Adjusted spacing for smaller checkbox */
}

.side-menu-checkbox:checked {
  background-color: #f45e28;
  border: 2px solid #f45e28;
}

.side-menu-checkbox:checked::after {
  content: '';
  display: block;
  width: 4px; /* Adjusted size for smaller checkbox */
  height: 8px; /* Adjusted size for smaller checkbox */
  border: solid white;
  border-width: 0 2px 2px 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
}

/* Companies-content */

/* change password */
.eye-cursor {
  cursor: pointer;
  padding-top: 2px;
}

/* change-password */

/* profile page */

.profile-content {
  border-bottom: 1px solid $grey27;
  margin: 30px;
  padding-top: 20px;

  .upload-profile {
    .member.profile {
      width: $value100;
      height: $value100;
    }

    .member {
      .profile-img {
        width: $value100;
        height: $value100;
        border-radius: 50px;
        object-fit: cover;
      }

      .image-upload.profile-upload {
        width: $value40;
        height: $value40;

        .camera-icon {
          position: absolute;
          right: 10px;
          top: 11px;
        }
      }
    }
  }
}

.profile-tab {
  .tab-container {
    .nav-tabs {
      .nav-item {
        .nav-link {
          @extend .fs16;

          &.active {
            color: $orange;

            &::before {
              height: $value5;
            }
          }
        }
      }
    }

    .history-list {
      .list-item {
        border-width: 1px 0 0;
        border-radius: 0;
        border-color: $grey12;

        &:first-child {
          border-width: 0;
        }

        &:last-child {
          border-width: 1px 0 1px;
        }
      }
    }

    td {
      &.status-success {
        color: $green;
      }

      &.status-danger {
        color: $red;
      }
    }
  }

  .bar {
    top: 364px;
    height: 40px;
  }
}

.plan-btn {
  height: 28px;
  line-height: normal;
  width: 81px;
  font-size: 11px;
  border-radius: 12px;
  text-align: center;

  &.manage-button {
    padding: 0px 3px;
    width: 85px;
  }
}

.over-view-form {
  .over-view {
    padding: 40px;
  }
}

/* profile page */

.ng2-dropdown-menu {
  z-index: 1051 !important;
  max-height: 150px !important;
  width: auto !important;
  overflow-y: auto;
}

.modal-header {
  .close {
    outline: none;
    background-color: transparent;
    border: 0px;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: 0.5;
  }
}

/* DFOW content */

.dfow-content {
  .dfow-input {
    border: 1px solid $white;
  }

  ul.filter-list {
    justify-content: flex-end !important;
  }

  .dfow-toggle {
    border-color: $grey29;
  }

  .table.table-custom tbody tr:nth-child(even) {
    background-color: transparent;
  }

  .table.table-custom td {
    height: 0;
    padding: 5px 0.75rem;
  }

  .table.table-custom tbody tr td:nth-child(1) {
    width: 1px;
  }

  .table.table-custom tbody tr td:nth-child(2) {
    width: $value75;
  }
   .table.table-custom tbody tr td:nth-child(5) {
    width: 200px;
  }
}

.bulkupload {
  .upload-close {
    position: absolute;
    right: $value25;
    top: $value5;
    opacity: 0.5;
    cursor: pointer;
    &:hover {
      outline: none;
    }
  }
}

/**  Calendar Component  **/

.custom-full-calendar {
  .fc .fc-popover {
    z-index: 999;
  }
  .fc-direction-ltr .fc-timegrid-col-events {
    margin: 0;
  }

  .fc-timegrid-event-harness {
    min-height: 10px;
  }

  .fc.fc-media-screen {
    .fc-header-toolbar {
      width: 75%;
      margin-bottom: 10px;

      .fc-button-group {
        .fc-button.fc-button-primary {
          background: transparent;
          color: $grey8;
          border: 1px solid $grey12;
          font-size: 14px;
          font-weight: 700;
          text-transform: capitalize;
          width: 102px;
          height: 45px;

          &:focus {
            box-shadow: 0 8px 16px $black3;
            border: none;
          }

          &:active {
            background: none;
            color: $dark-blue-1;
            box-shadow: 0 8px 16px $black3;
            border: none;
          }

          &:hover {
            background: none;
            color: $dark-blue-1;
            box-shadow: 0 8px 16px $black3;
            border: none;
          }
        }
      }

      .fc-button.fc-button-primary.fc-button-active {
        background: none;
        color: $dark-blue-1;
        box-shadow: 0 8px 16px $black3;
        border: none;
      }
    }

    .fc-daygrid.fc-dayGridMonth-view {
      .fc-scrollgrid {
        table {
          width: -webkit-fill-available !important;
          height: 100% !important;
        }

        th {
          width: 100px;
          border-width: 1px 0;
          vertical-align: middle;
          text-align: center;
        }

        .fc-col-header-cell-cushion {
          color: $dark-blue-1;
          text-transform: uppercase;
          font-size: 10px;
          font-weight: 600;
          text-align: center;
        }
      }
    }

    .fc-timegrid.fc-timeGridWeek-view {
      .fc-scrollgrid {
        th {
          border-width: 1px 0;
          vertical-align: middle;
          text-align: center;
        }

        .fc-col-header-cell-cushion {
          color: $dark-blue-1;
          text-transform: uppercase;
          font-size: 10px;
          font-weight: 600;
          text-align: center;
          margin-top: 10px;
        }
      }
    }

    .fc-scrollgrid-section {
      .fc-daygrid-day {
        .fc-daygrid-day-number {
          color: $grey27;
          font-size: 14px;
          margin-right: 20px;
          padding-bottom: 0px !important;
          padding-top: 0px !important;
        }
      }

      .fc-daygrid-day.fc-day.fc-day-today {
        .fc-daygrid-day-number {
          display: block;
          height: 30px;
          width: 30px;
          line-height: 30px;
          border-radius: 30px;
          background-color: $dark-blue-1;
          color: $white;
          text-align: center;
        }
      }

      .fc-scroller colgroup col {
        width: 65px !important;
      }

      .fc-timegrid-body {
        .fc-timegrid-slots colgroup col {
          width: 65px !important;
        }

        .fc-timegrid-cols colgroup col {
          width: 65px !important;
        }
      }

      a.fc-col-header-cell-cushion {
        color: $black;
      }
    }

    .fc-event-title-container {
      .fc-event-title.fc-sticky {
        padding-left: 10px;
      }
    }
  }

  .fc-direction-ltr .fc-timegrid-slot-label-frame {
    text-align: left;
    padding-left: 5px;
    position: absolute;
    background-color: $white;
    bottom: -11px;
  }

  .fc .fc-view-harness {
    height: 70vh !important;
  }

  .fc {
    .fc-scroller {
      overflow: hidden hidden !important;
      overflow-x: auto !important;
      overflow-y: auto !important;
    }

    .fc-header-toolbar {
      .fc-toolbar-chunk {
        div {
          display: flex;
          align-items: baseline;
        }
      }

      .fc-prev-button.fc-button.fc-button-primary,
      .fc-prevYear-button.fc-button.fc-button-primary,
      .fc-nextYear-button.fc-button.fc-button-primary,
      .fc-next-button.fc-button.fc-button-primary {
        background: none;
        border: 0;
        color: $light-grey3;
        padding: 0px;

        &:focus {
          box-shadow: 0 8px 16px $black3;
          border: none;
        }

        &:active {
          background: none;
          color: $dark-blue-1;
          box-shadow: 0 8px 16px $black3;
          border: none;
        }

        &:hover {
          background: none;
          color: $dark-blue-1;
          box-shadow: 0 8px 16px $black3;
          border: none;
        }
      }

      .fc-toolbar-title {
        font-size: 13px;
        font-weight: 700;
        width: 150px;
        text-align: center;
      }
    }

    .fc-daygrid-body {
      width: -webkit-fill-available !important;
      height: 100% !important;
      .fc-scrollgrid-sync-table {
        width: -webkit-fill-available;
        height: 100% !important;
        td {
          background: none;
          min-width: 50px;
        }

        tr {
          height: 50px;
        }
      }
    }

    .fc-h-event .fc-event-title {
      display: flex;
      align-items: center;
    }

    a.fc-daygrid-dot-event.fc-event.green_event,
    a.fc-daygrid-dot-event.fc-event.red_event,
    a.fc-daygrid-dot-event.fc-event.orange_event,
    a.fc-daygrid-dot-event.fc-event.grey_event {
      &:hover {
        background: transparent;
        cursor: pointer;
      }
    }

    .fc-event-title {
      font-size: 11px;
      font-weight: 700;
      text-align: left;
      padding-left: 8px;
      display: flex;
      align-items: center;

      &::before {
        left: 0;
        position: absolute;
        content: '';
        width: $value5;
        height: $value25;
      }
    }

    .fc-scrollgrid .fc-timegrid-col.fc-day-today {
      background: transparent;
    }

    .fc-scrollgrid .fc-timegrid-slot {
      height: 25px;
      position: relative;
    }

    .fc-daygrid-event {
      border-radius: 0 5px 5px 0;
      width: 100%;
      margin-left: 0 !important;
      border: 0;
    }

    .fc-direction-ltr .fc-daygrid-event.fc-event-start {
      margin-left: 0;
    }

    .fc-daygrid-event.full_blue_event {
      background: $blue6;
      width: 100%;
      color: $white;
      border-radius: 0;
      cursor: pointer;
    }

    .fc-daygrid-event-dot {
      display: none;
    }

    .fc-event-time {
      display: none;
    }

    .fc-daygrid-event.fc-event.red_event,
    .fc-timegrid-event.red_event {
      .fc-event-title,
      .fc-event-title-container {
        background-color: $light-grey4;
        color: $red1;

        &::before {
          background: $red1;
        }
      }
    }

    .fc-daygrid-event.fc-event.grey_event,
    .fc-timegrid-event.grey_event {
      .fc-event-title,
      .fc-event-title-container {
        background-color: $grey2;
        color: $black;

        &::before {
          background: $black;
        }
      }
    }

    .fc-daygrid-event.fc-event.green_event,
    .fc-timegrid-event.green_event {
      .fc-event-title,
      .fc-event-title-container {
        background-color: $light-grey5;
        color: $green3;

        &::before {
          background: $green3;
        }
      }
    }

    .fc-daygrid-event.fc-event.blue_event,
    .fc-timegrid-event.blue_event {
      .fc-event-title,
      .fc-event-title-container {
        background-color: $light-grey5;
        color: $blue8;

        &::before {
          background: $blue8;
        }
      }
    }

    .fc-daygrid-event.fc-event.full_blue_event_weekly,
    .fc-timegrid-event.full_blue_event_weekly {
      .fc-event-title,
      .fc-event-title-container {
        background-color: $light-grey5;
        color: $blue6;

        &::before {
          background: $blue6;
        }
      }
    }

    .fc-daygrid-event.fc-event.full_blue_event,
    .fc-timegrid-event.full_blue_event {
      .fc-event-title,
      .fc-event-title-container {
        background-color: $blue6;
        color: $white;

        &::before {
          background: $blue6;
        }
      }
    }

    .fc-daygrid-event.fc-event.orange_event,
    .fc-timegrid-event.orange_event {
      .fc-event-title,
      .fc-event-title-container {
        background-color: $light-grey6;
        color: $orange-light;

        &::before {
          background: $orange-light;
        }
      }
    }

    .fc-daygrid-event.fc-event.calendar_event,
    .fc-timegrid-event.calendar_event {
      .fc-event-title,
      .fc-event-title-container {
        background-color: $grayed-out;
        color: $grayed-out-1;
      }
    }

    .fc-daygrid-event.fc-event.yellow_event,
    .fc-timegrid-event.yellow_event {
      .fc-event-title,
      .fc-event-title-container {
        background-color: #fff5d5;
        color: $yellow;

        &::before {
          background: $yellow;
        }
      }
    }

    .fc-timegrid-event.fc-v-event.fc-event {
      border-width: 0;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      height: 100%;
      background-color: transparent;

      .fc-event-title {
        height: 100%;
        padding-top: 0px;
        font-size: 9px;

        &::before {
          height: 100%;
          top: 0;
        }
      }
    }

    .fc-timegrid-slot-label-cushion.fc-scrollgrid-shrink-cushion {
      color: $dark-blue-1;
      font-size: 12px;
      font-weight: 700;
    }
  }

  &.custom-full-calendarsetting {
    .calendar-fulldetails {
      .fc .fc-view-harness {
        height: 500px !important;
        top: 10px;
      }
    }

    .calendar-eventdetails {
      max-height: 500px;
      overflow-y: auto;
      border: 1px solid $grey12;
      margin-top: 65px !important;

      .follo-switch .switch.switch-medium {
        height: 15px !important;
        width: 30px !important;
      }

      .event-table {
        border-left: 4px solid $grayed-out-1;
        padding-bottom: 5px !important;
        width: 100% !important;

        .popover {
          width: 337px;
          max-width: 320px;
          box-shadow: 0 16px 16px $black3;
          border-radius: 5px;
        }

        .popover-header {
          background-color: $orange !important;
        }

        .popover-body {
          padding: 0px !important;
          box-shadow: 0 16px 16px #2c359319 !important;
          background-color: $orange !important;
        }

        td {
          padding: 0px !important;
          padding-left: 10px !important;
        }
      }
    }
  }

  .fc .fc-highlight {
    background: transparent;

    border: 1px solid $orange !important;
  }

  &.no-auth-calendar {
    height: 100%;
    margin-top: 0px;

    .fc.fc-media-screen .fc-header-toolbar {
      width: 100%;
    }

    .fc .fc-scroller-harness-liquid {
      height: 100%;
    }

    .fc .fc-scrollgrid-liquid {
      height: 80%;
    }

    .fc {
      .fc-scroller {
        overflow: hidden !important;
      }
    }

    .fc .fc-view-harness {
      height: 100vh !important;
    }

    .fc .fc-scroller-liquid-absolute {
      position: static;
    }

    .fc-direction-ltr {
      height: 90vh !important;
    }
  }

  &.weekly-calendar {
    .fc .fc-event-title {
      align-items: initial;
    }
  }
}

.delivery-calendar {
  z-index: 0;
}

.delivery-add {
  right: 0;
  top: 0;
  z-index: 1;
}

.delivery-description {
  max-width: 500px;
  box-shadow: 0 16px 16px $black3;
  padding: 15px;
  position: absolute;
  top: 100px;
  right: 0;
  z-index: 99;
  left: 220px;
  margin: 0 auto;
  border-radius: 5px;

  .bottom-line {
    border-bottom: 2px solid #efefef;
  }

  .delivery-details-content {
    line-height: 10px;
  }

  .header-list {
    top: 12px;
    left: 0;
  }

  .close {
    &:focus {
      outline: none;
    }
  }
}

.delivery-description.deliver-hide {
  display: none;
}

.delivery-description.deliver-show {
  display: block;
}

.calendarsetting-description {
  max-width: 300px;
  box-shadow: 0 16px 16px $black3;
  padding: 15px;
  position: absolute;
  top: 250px;
  right: 0;
  z-index: 99;
  left: 220px;
  margin: 0 auto;
  border-radius: 5px;

  .bottom-line {
    border-bottom: 2px solid #efefef;
  }

  .delivery-details-content {
    line-height: 10px;
  }

  .header-list {
    top: 12px;
    left: 0;
  }

  .close {
    &:focus {
      outline: none;
    }
  }
}

.calendarsetting-description.deliver-hide {
  display: none;
}

.calendarsetting-description.deliver-show {
  display: block;
}

.top-header.calendar-filter {
  right: 0;
  top: 5px;
}

.calendar-add {
  background-color: $orange !important;
  color: $white;
  border-radius: 50px;
  width: 40px;
  height: 40px;
  box-shadow: 0 5px 5px $red6;

  &:focus {
    outline: none;
  }
}

.custom-modal.event-popup {
  top: 150px;
}

@media (max-width: 768px) {
  .tab--responsivefix .card-title h5 {
    font-size: 17px;
  }

  .custom-full-calendar {
    .fc {
      .fc-toolbar {
        display: block !important;
        margin-top: 2rem;

        .fc-toolbar-chunk {
          div {
            justify-content: space-around;
          }
        }
      }
    }

    .fc.fc-media-screen {
      .fc-header-toolbar {
        width: 100%;
        margin-bottom: 0 !important;

        .fc-toolbar-chunk {
          margin-top: 2rem;
        }
      }
    }
  }

  .delivery-description {
    left: 0;
  }
}

/** END of Calendar content **/

// Notification card
.notification-card {
  z-index: 10000;
  right: 10px;
  position: fixed;
  bottom: 40px;
  width: 370px;

  .card-container.cmpny-card {
    min-height: 125px;
    box-shadow: 0 3px 6px $black1;
    border-radius: 10px;

    &::before {
      width: 10px;
    }
  }

  .orange-card.card-container.cmpny-card {
    &::before {
      opacity: 0.5;
    }
  }

  .close-img {
    top: -11px;
    right: -8px;
  }

  .notification-profile-img {
    width: 40px;
    height: 40px;
  }
}

.top-minus-3px {
  top: -3px;
}

.delivery-time {
  min-height: 60px;

  table {
    position: absolute;
    top: 0;
    left: 0;
  }

  .was-validated .form-control:invalid,
  .form-control.is-invalid {
    background-position: right calc(0.85em + 0.1875rem) center;
  }

  .bs-chevron {
    color: $orange;
    border-width: 2px 0 0 2px;
    width: 6px;
    height: 6px;
    cursor: pointer;
  }

  .bs-chevron.bs-chevron-up {
    top: 25px;
    right: -15px;
  }

  .bs-chevron.bs-chevron-down {
    right: -15px;
    top: -25px;
  }

  .bs-timepicker-field {
    text-align: left !important;
    font-size: 12px;
  }

  .btn.btn-default {
    font-size: 12px;
    padding: 5px;

    &:hover {
      background-color: $orange;
      color: $white;
    }
  }

  .btn.btn-link {
    cursor: default;
  }

  &.weeklycalendar-time {
    .bs-chevron.bs-chevron-up,
    .bs-chevron.bs-chevron-down {
      z-index: 9999999;
    }
  }
}

.notify-card {
  box-shadow: 0 0 20px $grey44;

  .notify-close {
    position: absolute;
    top: 5px;
    right: 20px;

    &:focus {
      outline: none;
    }
  }
}

//projectspage
.project-card {
  box-shadow: 0 0 20px $grey45;
  border-radius: 8px;

  .table td {
    border-top: 0;
  }

  .table.table-custom {
    border: 0;
  }
}

.projects-modal {
  .modal-header .close {
    padding: 0;
    margin: 0;
  }

  .modal-body {
    padding: 2rem;
  }
}

//dashboard-card

.dashboard-card {
  box-shadow: 0 0 20px $grey45;

  .highcharts-axis-labels {
    font-family: 'Cairo', sans-serif;
  }

  .highcharts-no-data {
    font-family: 'Cairo', sans-serif;
  }

  .card-header {
    background: transparent;
  }

  .card-body {
    padding: 13px;
  }

  .activity-img {
    width: 40px;
    height: 40px;
    border: 1px solid $grey25;
    border-radius: 18px;

    img {
      object-fit: cover;
      width: 100%;
      height: 100%;
    }
  }

  .dots {
    height: 10px;
    width: 10px;
    border-radius: 50%;
    display: inline-block;

    &.delivery-dots {
      background-color: $blue11;
    }

    &.crane-dots {
      background-color: #ffce2d;
    }

    &.concrete-dots {
      background-color: #838383;
    }

    &.inspection-dots {
      background-color: #a234fd
    }
  }

  .custom-select {
    box-shadow: 0 2px 3px $black4;
    border: 1px solid $light-grey7;
    border-radius: 4px;
    height: calc(1.5em + 0.75rem + 2px);
    padding: 0.375rem 1.75rem 0.375rem 0.75rem;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: none;
    padding-right: 20px;
    position: relative;
  }
  .custom-arrow {
    background: url('../../assets/images/small-down.svg') no-repeat right 0.75rem center/8px 12px;
  }

  .custom-select::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 10px;
    width: 10px;
    height: 10px;
    background: url('../../assets/images/small-down.svg') no-repeat right 0.75rem center/8px 12px;
    background-size: contain;
    pointer-events: none;
  }

  .custom-select::-ms-expand {
    display: none;
  }

  .table.table-custom {
    border: 0;

    thead {
      background-color: transparent;
    }

    td {
      border: 0;
    }

    thead th {
      font-weight: 600;
      vertical-align: bottom;
      border-bottom: none;
      border-top: none;
    }

    tbody tr:nth-child(even) {
      background-color: transparent;
    }
  }
  .site-plan-details {
    background-color: $gray-e;
    border-radius: 6px;
    color: $white;
  }
}

.highcharts-title {
  text-align: left !important;
  font-size: 12px !important;
}

.highcharts-legend-item {
  display: none;
}

//billing

.plan-detail {
  box-shadow: 0 0 20px $grey45;

  .table.table-custom td {
    height: 0;
    border-top: 0;
  }

  .table.table-custom tbody tr:nth-child(even) {
    background-color: transparent;
  }

  .btn {
    box-shadow: none;
  }
}

.billing-modal {
  max-width: 400px;
}

.project-head {
  position: relative;

  p {
    &::after {
      content: '';
      background: $grey8;
      width: 65%;
      height: 1px;
      bottom: 15px;
      right: 5px;
      position: absolute;
    }
  }
}

.agm-map {
  height: 200px;

  .gmnoprint {
    div {
      &:first-child {
        height: 61px !important;
      }
    }

    button {
      height: 30px !important;
      font-size: 12px !important;
      font-family: 'Cairo', sans-serif !important;
    }

    ul {
      li {
        font-size: 12px !important;

        label {
          vertical-align: text-top;
          margin-top: 2px;
          margin-left: 4px;
        }
      }
    }
  }

  .gm-style-iw {
    &.gm-style-iw-c {
      max-height: 50px !important;
      top: -6px;
      left: 38px;
      padding: 7px;
      border-radius: 0;
    }
  }
}

.pac-container {
  z-index: 10000 !important;
}

.rotateleft {
  transform: rotate(-270deg);
}

.get-arrow {
  width: 30px;
  display: inline-block;
  margin-right: 9px;
}

.text-wrap {
  white-space: normal !important;
}

.delivery-request-card {
  @extend .custom-primary-tab;

  border: 1px solid $grey12;
  border-radius: 3px;
  padding: 5px 0 30px;

  .pumpsize-gridoption {
    word-break: break-all;
  }

  .tab-container {
    .nav-tabs {
      margin-bottom: 0;
    }
  }

  .queued-list-active {
    background-color: rgba(255, 0, 0, 0.5);

    td {
      color: $white;

      .action-icon {
        filter: brightness(0) invert(1);
        cursor: pointer;
      }
    }
  }
}

.payment-card-layout {
  .payment-card {
    position: relative;
    height: 190px;

    .master-card {
      width: 21px;
      height: 14px;
    }

    .visa-card {
      width: 31px;
      height: 10px;
    }

    .small-card {
      width: 16px;
      height: 16px;
    }

    .delete-card {
      position: absolute;
      top: 8px;
      right: 4px;
      width: 25px;
      height: 25px;
    }

    .border-left-white {
      border-left: 0.5px solid $white;
      opacity: 0.3;
      height: 10px;
      margin-top: 3px;
    }

    .card-custom-dropdown {
      .btn {
        &.dropdown-toggle {
          background-color: $grey41;
          border-color: $grey41;
          padding: 4px 10px;
          border-radius: 20px;
          color: $orange;

          @extend .fs10;

          .arrow-bottom {
            font-size: 7px;
          }

          &:focus,
          &:hover {
            background-color: $grey41;
            border-color: $grey41;
            box-shadow: none;
          }

          &::after {
            display: none;
          }
        }
      }

      .dropdown-menu {
        background-color: $grey41;
        overflow: auto;
        resize: vertical;
        height: 100px;

        .dropdown-item {
          color: $white;

          @extend .fs10;

          background-color: $grey41;

          &:hover {
            color: $orange;
          }
        }
      }
    }
  }

  .add-new-card {
    background-color: rgba(244, 94, 40, 0.25);
    background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='5' ry='5' stroke='%23F45E28FF' stroke-width='4' stroke-dasharray='6%2c 14' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
    border-radius: 5px;
    height: 190px;

    .add-pay-icon {
      height: 80px;
    }
  }
}

.ngx-dropdown-container .ngx-dropdown-button {
  border-bottom: 1px solid $grey11 !important;
  border-radius: 0px !important;
  border-top: 0px !important;
  border-left: 0px !important;
  border-right: 0px !important;
  color: #495057 !important;
  background-color: #ffffff;
  padding: 5px 0px !important;
}

.ngx-dropdown-container .ngx-dropdown-list-container ul.selected-items li {
  background-color: $orange !important;
  color: #fff;
  margin-bottom: 2px;
}

.ngx-dropdown-container .ngx-dropdown-list-container ul {
  max-height: 100px !important;
  position: relative !important;
}

.ngx-dropdown-container .ngx-dropdown-list-container .search-container label {
  font-size: 13px !important;
}

.ngx-dropdown-container .ngx-dropdown-list-container ul.selected-items li .nsdicon-close {
  position: absolute !important;
  right: 8px !important;
  top: 0px !important;
}

.ngx-dropdown-container button .nsdicon-angle-down {
  right: 8px !important;
  position: relative;
  float: end;
}

.ngx-dropdown-container button .nsdicon-angle-down::before {
  border-style: solid;
  border-width: 0.2em 0.2em 0 0 !important;
  content: '';
  display: inline-block;
  height: 8px !important;
  position: relative;
  vertical-align: text-top;
  width: 8px !important;
  top: 2px !important;
  transform: rotate(135deg);
  color: $grey11 !important;
}

/*add-calendarevent modal*/
.add-calendarevent {
  width: 543px;
  margin: 10px auto;

  .addcalendar-listgroup span {
    display: inline-block;
  }

  .modal-header {
    .addnew-event {
      text-align: center !important;
    }
  }

  .follo-switch {
    .switch.switch-medium {
      height: 17px !important;
      width: 30px !important;
    }
  }

  .custom-radio {
    .custom-control-input:focus ~ .custom-control-label::before {
      box-shadow: none !important;
      border-color: $orange !important;
    }

    .custom-control-label::after {
      left: -3em !important;
    }

    .custom-control-input:checked ~ .custom-control-label {
      &::before {
        background-color: $orange;
        border-color: $orange !important;
      }
    }

    .custom-control-label {
      padding-top: 2px !important;

      &::before {
        background-color: transparent;
      }
    }
  }
}

.addcalendar-details {
  .addcalendar-displaydays {
    .displaylists {
      padding-left: 0px !important;
    }

    label {
      display: block;
      border: solid 1px $orange3;
      line-height: 40px;
      height: 40px;
      width: 40px;
      border-radius: 40px;
      -webkit-font-smoothing: antialiased;
      margin-top: 10px;
      font-family: 'Cairo', sans-serif;
      color: $grey15;
      text-align: center;
      font-size: 14px !important;
    }

    label:hover {
      cursor: pointer;
    }

    input[type='checkbox'] {
      display: none;
    }

    input:checked + label {
      border: solid 1px $yellow1;
      color: $white;
      background-color: $yellow1;
    }

    /* new stuff */
    .check {
      visibility: hidden;
    }

    input:checked + label .check {
      visibility: visible;
    }

    ul {
      list-style-type: none;
    }

    ul li {
      display: inline-block;
    }
  }
}

.addnewevent-popupscrollfix {
  overflow: hidden;
}

.fc .fc-daygrid-body-balanced .fc-daygrid-day-events {
  top: 15px !important;
}

#freshworks-container {
  #launcher-frame {
    bottom: -10px !important;
  }
}

.tooltip-inner {
  max-width: none;
}

.fc .fc-timegrid-axis-frame {
  overflow: hidden;
  display: flex;
  align-items: center;
  font-size: 14px;
  text-align: center;
}

.dfow-content ul.upload-btn li.list-group-item button {
  border-radius: 23px;
}

.pricing-image {
  height: 18px;
}

.popupcalendarsettings {
  position: fixed;
  bottom: 0px;
  right: 0px;
  min-height: min-content;
  top: -75px !important;

  .arrow {
    top: 55px !important;
  }

  .popover-body {
    padding: 0.2rem 0.75rem;
  }
}

//notification settings
.notification-page-card {
  border: 1px solid $grey12;
  border-radius: 5px;

  .panel-enabled {
    background-color: transparent;
    padding: 0px;
    border-radius: 0px;
    border-color: $grey12;
  }

  .notification-setting-lists {
    .panel-group:nth-child(even) {
      background: $grey9;
    }
  }

  .card {
    border: 1px solid $grey12;
    background-color: transparent;

    .card-body {
      padding: 0px;
    }
  }
}

.concrete-header {
  max-width: 20%;
}

.pump-header {
  max-width: 17%;
}

.line-1 {
  position: relative;
  height: 1px;
  background: $light-grey8;
  top: 15px;
  left: -35px;
  width: 568px;
  &.line-2 {
    left: -45px;
    width: 580px;
  }
}

.concrete-switch {
  left: 50%;
}

//table resizable
:host {
  &:last-child .bar {
    display: none;
  }
}

.wrapper {
  display: flex;
  justify-content: flex-end;
}

.content {
  flex: 1;
}

.bar {
  position: absolute;
  top: 104px;
  bottom: 0;
  width: 1px;
  height: 45px;
  margin: 0 -16px 0 16px;
  justify-self: flex-end;
  border-left: 0px solid transparent;
  border-right: 1px solid #cccccc;
  background-clip: content-box;
  cursor: ew-resize;
  opacity: 0;

  &:hover,
  &:active {
    opacity: 1;
  }
}

//table resizable customised

.table-custom {
  .bar {
    top: 95px;
  }

  &.request-resizable {
    .bar {
      top: 135px;
      height: 40px;
    }
  }

  &.concrete-request-status {
    .status-approved,
    .status-expired,
    .status-declined,
    .status-pending,
    .status-delivered {
      width: 100%;
      padding-top: 2px;
    }
  }
}



.gates-resizable {
  .bar {
    top: 75px;
  }
}

/* Switch */
.small-switch {
  .switch {
    &.switch-medium {
      height: 15px;
      width: 30px;
      min-width: 30px;

      &.checked {
        background-color: $green4 !important;

        small {
          width: 15px !important;
          height: 15px !important;
          left: 15px !important;
        }
      }

      small {
        left: 1px !important;
        width: 15px !important;
        height: 15px !important;
      }
    }
  }
}

.delivery-ddetails-content {
  word-break: break-word;
}

.cancelConfirmation-popup {
  .concrete-other-request {
    .form-control {
      border: 0px;
      border-bottom: 1px solid $grey11;
      border-radius: 0px;
      color: $grey11;
    }
  }
}

.today {
  background-color: $grey49;
  color: $black;

  &:hover {
    background-color: $grey49;
    color: $black;
  }
}

.sub-list-scroll li:hover {
  color: $black !important;
  font-weight: bold;
}

.members-table .list-inline-item:not(:last-child) {
  margin-right: 0rem !important;
}

.deactivate-modal {
  max-width: 700px;

  .custom-material-form .material-input {
    border-width: 1px;
    width: 130px;
  }

  .table td,
  .table th {
    border: 0px;
    background-color: transparent;
  }

  .table-custom.table thead th {
    border: 0px;
  }

  .table-custom.table thead {
    background-color: transparent;
  }

  .table-custom.table {
    border: 0px;
  }

  .custom-material-form select.material-input {
    background: url('../../assets/images/down-arrow-grey.png') no-repeat right 0.5rem center/10px
      10px;
    border-radius: 4px;
  }

  .content-deactivate {
    padding: 0px 110px;
  }

  .table-custom.table tbody tr:nth-child(even) {
    background-color: transparent;
  }
}

.options-modal {
  .modal-header {
    display: none;
  }
}

.invite-member-dot-option {
  .dropdown-menu.show {
    transform: translate(-50%, -50%) !important;
    left: -52px !important;
    top: 2px !important;
    padding: 4px;
    font-size: 12px;
    min-width: 112px;
    height: auto;
  }
}

.vertical-dots {
  transform: rotate(90deg);
}

.deactivate-member-table-scroll {
  overflow-y: auto !important;
  max-height: 50vh;
}

.border-grey-color {
  border: 1px solid $gray;
  border-radius: 5px;
}

.fixTableHead {
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 200px;

  thead th {
    position: sticky;
    top: 0;
  }

  tr {
    border: 2px solid $grey12;
  }

  table {
    border-collapse: collapse;
    width: 100%;
  }

  th {
    background: $grey9 !important;
  }
}

.wait-modal-body {
  height: 230px;

  img {
    height: 160px;
  }
}

.spinner-border {
  display: block;
}

.recurrence-year {
  .recurrance-column-day {
    padding-top: 30px;
  }
}

button.buttonstyle--new {
  width: 100px;
  height: 32px;
  border: 0;
  border-radius: 30px;
  margin-right: 10px !important;
}

section.report--section {
  padding: 18px 40px;

  .tabheader .nav li.active a {
    font-family: 'Cairo', sans-serif !important;
    font-size: 13px;
    width: 89px;
    float: left;
    background: $orange;
    margin-right: 20px;
    border-radius: 30px;
    text-align: center;
    height: 29px;
    color: $white;
    padding: 4px;
    font-weight: bold;
  }

  .tabheader .nav li a {
    font-family: 'Cairo', sans-serif !important;
    font-size: 13px;
    float: left;
    margin-right: 20px;
    text-align: center;
    color: $grey8;
    padding: 4px;
    cursor: pointer;
    width: 89px;
    height: 29px;
    font-weight: bold;
  }

  .tabheader .nav-tabs {
    float: left;
    padding-bottom: 10px;
  }

  li.li-position.active a::before {
    position: absolute;
    content: '';
    width: 19px;
    height: 3px;
    background: #f45e28;
    top: 37px;
    left: 33%;
  }

  li.li-position.active {
    position: relative;
  }

  .searchicon {
    color: $grey8;
    background: $white;
    box-shadow: 0px 3px 11px $boxshadow;
    width: 40px;
    height: 40px;
    text-align: center;
    padding-top: 6px;
    border-radius: 100%;
    margin-top: 12px;
    margin-right: 10px;
  }

  .heading--text {
    font-size: 14px;
    font-family: 'Cairo', sans-serif !important;
    font-weight: 500;
  }

  .content--div {
    float: left;
    width: 100%;
  }

  .card {
    box-shadow: 0px 0px 20px #d7dee365;
    border: 0;
  }
}

.modal-dialog.modal-downloads .modal-content {
  width: 666px;
  border-radius: 19px;
}

span.backicon {
  float: left;
  color: $orange;
  margin-right: 10px;
}

h2.title-text {
  float: left;
  color: $black;
  font-size: 16px;
  margin-top: 4px;
}

span.sorticon img {
  position: relative;
  top: -2px;
}

.reportsgrid {
  padding: 20px;

  .top-header.calendar-filter {
    right: 0;
    top: 5px;
    float: end;
  }

  .dropdown-menu.show {
    display: block;
    width: 227px;
    margin-top: 0;
    border-radius: 12px;
    box-shadow: 0px 0px 10px #0000001a;
    left: 25% !important;
  }

  ul.dropdown-menu.dropdown-custom.show {
    padding-top: 0;
  }

  ul.dropdown-menu.dropdown-custom.show li:first-child {
    border-bottom: 1px solid #dcdcdc;
    padding: 2px 10px;
  }

  ul.dropdown-menu.dropdown-custom li {
    padding: 0px 10px;
  }

  span.textlabel {
    font-size: 12px;
    font-family: 'Cairo', sans-serif !important;
    font-weight: bold;
  }

  span.textinput input {
    float: end;
    margin-top: 8px;
  }
}

button.columnsetting.dropdown-toggle {
  background: transparent;
  border: 0;
  border-bottom: 1px solid #dcdcdc;
  width: 235.71px;
  text-align: left;
  font-size: 13px;
  color: #3b4450;
  padding-bottom: 5px;
  padding-left: 1px;
}

span.mandantory {
  color: red;
}

button.columnsetting.dropdown-toggle::after {
  float: end;
  position: relative;
  top: 9px;
}

.filterdropdown {
  margin-right: 30px;
  margin-top: 6px;
}

.modal-dialog.modal-schedule {
  max-width: 750px;
}

.iconremove::after {
  display: none !important;
}

.dropdown-menu.runnow--dropdown.show {
  padding: 0;
  box-shadow: 0px 3px 6px #00000029;
  border: 0;
  transform: translate(50%, -131%) !important;
}

.dropdown-menu.runnow--dropdown.show li.sendto {
  background: $orange;

  a {
    color: #fff !important;
    padding: 5px 9px;
    font-family: 'Cairo', sans-serif !important;
    font-size: 12px;
  }

  svg {
    float: end;
    padding-top: 0px;
    width: 20px;
    height: 25px;
  }
}

.dropdown-menu.runnow--dropdown.show .dropdown-item:hover,
.dropdown-menu.runnow--dropdown.show .dropdown-item:focus {
  background-color: #e76e42;
  color: $white;
}

.showentries li:first-child {
  margin-bottom: 0 !important;
  float: left;
}

li.list-inline-item.dropdown--counts {
  margin-top: 1px;
  float: left;
}

li.list-inline-item.dropdown--counts select {
  border: 1px solid #5b5b5b;
  text-align: left !important;
  padding: 0px;
  max-width: 52px;
  width: 34px !important;
  background: url('../../assets/images/down-arrow-grey.png') no-repeat 88%;
  -webkit-appearance: none;
  background-size: 23%;
  font-size: 11px;
  -moz-appearance: none;
  padding-left: 3px;
  height: 30px;
}

ul.nestedul--align.list-style-none.display-inline {
  list-style: none;
  padding-left: 0;
  display: inline-flex;
}

li.firstleftarrow,
li.lastrightarrow {
  background: $grey19;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  padding: 4px;
}

ul.nestedul--align {
  background: $grey19;
  padding: 6px;
  border-radius: 20px;
  margin-left: 10px;
  margin-right: 10px;
}

ul.nestedul--align.list-style-none.display-inline li {
  width: 36px;
}

.status-pending span svg {
  width: 16px;
  height: 13px;
  position: relative;
  top: -1px;
}

a.float-end.topaction--styles {
  width: auto;
  margin-left: 20px;
  color: #3b4450;
  padding: 2px 11px;
  text-align: center;
}

a.float-end.topaction--styles svg {
  float: left;
  width: 14px;
  height: 27px;
  color: #3b4450;
  margin-right: 4px;
}

a.float-end.topaction--styles:hover,
.btn-group.open.show .topaction--styles {
  background: #f3f2f2;
  border-radius: 30px;
  color: $black !important;
  cursor: pointer;
}

span.textalign {
  font-size: 12px;
  font-family: 'Cairo', sans-serif !important;
  font-weight: 700;
}

a.backicon.btn.btn-orange {
  width: 65px;
  border-radius: 30px;
  font-size: 12px;
  font-weight: 700;
  padding: 4px 0;
  padding-right: 8px;

  svg {
    width: 10px;
    height: 14px;
    position: relative;
    top: -1px;
    left: 1px;
    margin-right: 3px;
  }
}

.topaction--styles.dropdown-toggle::after {
  display: none !important;
}

ul.dropdown-menu.dropdown-custom.filtermodal.show {
  width: 619px;
  left: -242px !important;
  box-shadow: 0px 10px 10px #0000005c;
  border: 0;
  padding-bottom: 0;
}

ul.dropdown-menu.dropdown-custom.filtermodal.report-filter.show {
  left: -430px !important;
}

.showentries li:first-child label {
  margin: 0;
  margin-top: 4px;
}

section.report--section .card:hover {
  border: 1px solid #ffb9a0;
}

ul.dropdown-menu.dropdown-custom.filtermodal.show li:first-child {
  border-bottom: 0;
}

.modal-dialog.modal-schedule .modal-content {
  border-radius: 20px;
  margin-top: 4rem !important;
}

.modal-dialog.modal-export .modal-content {
  border-radius: 20px;
  margin-top: 10rem !important;
}

.modal-dialog.modal-sendto .modal-content {
  border-radius: 20px;
  margin-top: 8rem;
}

.dropdown-menu.runnow--dropdown.show a.dropdown-item {
  font-size: 14px;
  padding-left: 8px;
  font-family: 'Montserrat SemiBold', sans-serif;
  padding-top: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid $grey49;
}

.note--styles {
  padding: 5px 16px;

  font-size: 13px;
}

.note--styles b {
  float: left;

  margin-right: 2px;
}

/*New,Edit Popup fixes on 24th Nov*/
.newupdate-alignments {
  .company-select .multiselect-dropdown .dropdown-btn .selected-item {
    margin-bottom: 2px !important;
  }

  .dropdown-multiselect--active .dropdown-multiselect__caret::before {
    top: 97% !important;
  }

  .newfixswitch {
    margin-top: 3px;
  }

  label {
    margin-bottom: 0;

    sup {
      color: red;
      font-size: 13px;
      position: relative;
      top: -1px;
    }
  }

  .escort--text {
    sup {
      color: red;
      font-size: 13px;
      position: relative;
      top: -1px;
    }
  }

  input.floating-input {
    border: 0;
    border-bottom: 1px solid;
    border-radius: 0;
    height: 25px !important;
  }

  .form-check-label {
    white-space: nowrap;
    position: relative;
    top: 2px;
    display: flex;

    p {
      margin-top: 0;
      margin-bottom: 0;
      padding-left: 5px;
    }
  }
  .edit-concrete-input {
    border-bottom: 1px solid #ced4da;
  }
}

.taginput--heightfix {
  .custom-material-form.add-concrete-material-form .customised-tag-input .tag-layout {
    height: 40px !important;
  }

  .form-check-label {
    white-space: nowrap;
    position: relative;
    top: 2px;
    display: flex;

    p {
      margin-top: 0;
      margin-bottom: 0;
      padding-left: 5px;
    }
  }
}

.newupdate-alignments .custom-material-form.add-concrete-material-form .multiselect-dropdown {
  margin-top: -1px;
}
.dropdown-btn .dropdown-multiselect--active .dropdown-multiselect__caret::before {
  top: 97% !important;
}

.modal-body.newupdate-alignments.taginput--heightfix.editconcrete {
  .multiselect-dropdown {
    margin-top: -1px;
  }

  .tag-wrapper {
    line-height: 30px;
    height: 25px;
  }

  .custom-material-form.add-concrete-material-form .tag-wrapper delete-icon svg {
    width: 10px;
    height: 25px;
  }

  .pump-sizefield {
    position: relative;
    top: 10px;
  }
}

.craneedit {
  .form-check-label {
    white-space: nowrap;
    position: relative;
    top: 2px;
    display: flex;

    p {
      margin-top: 0;
      margin-bottom: 0;
      padding-left: 5px;
    }
  }

  .messagetext {
    float: left;
    width: 100%;
    white-space: nowrap;
    margin-top: 10px;
  }
}

.modal-dialog.modal-schedule .addcalendar-details .addcalendar-displaydays label {
  line-height: 35px;
  height: 35px;
  width: 35px;
  font-size: 11px !important;
}

.reportsgrid .table-custom.request-resizable .bar {
  top: 78px;
  height: 40px;
}

.loader-icon {
  max-width: 200px;
}

::placeholder {
  color: $red11 !important;
}

.modal-dialog.heatmap-modal .modal-content {
  width: 745px;
  border-radius: 20px;
}

.reportexport-modal .modal-content,
.reportsave-modal .modal-content {
  border-radius: 20px;
}

.heatmap-modal .delivery-time .bs-chevron.bs-chevron-up {
  top: 23px;
}

.Weeklycalendar-picker {
  position: absolute;
  bottom: 75px;

  .form-control {
    border: 1px solid $light-grey8 !important;
    border-radius: 3px !important;
    background-color: transparent !important;
    padding-left: 3px !important;
  }
}

.report-select-popup {
  .custom-control-label {
    position: relative;
    padding-right: 0.5rem;
    width: 100%;
    font-size: 12px;
    font-weight: 700;

    &.project-setting-label,
    &.project-sharing-label {
      font-weight: 600;
    }
  }

  .custom-control-input:focus ~ .custom-control-label::before {
    box-shadow: none;
  }

  .custom-control-input:focus {
    box-shadow: none;
  }

  .custom-control-label::before,
  .custom-control-label::after {
    right: 0;
    left: auto;
    width: 0.8rem;
    height: 0.8rem;
  }

}

/*Project Settings Layout Start */
.project-setting-layout {
  padding: 25px 0px;

  .project-info {
    padding: 10px 25px;
  }

  .custom-secondary-tab .tab-container .nav-tabs {
    background-color: transparent;
    padding: 0px 40px;
  }

  .info-list {
    margin-bottom: 10px;

    .info-heading {
      font-weight: bold;
    }

    .info-heading,
    .info-content {
      font-size: 14px;
      color: $grey7;
      margin-bottom: 0;
      word-break: break-word;
    }
  }

  .project-sharing-settings {
    border-top: 1.5px solid $orange;
    padding: 10px 25px;

    .section-heading {
      color: $grey7;
      font-size: 14px;
      font-weight: bold;
    }
    .btn:disabled {
      background-color: $orange;
      color: $white;
      opacity: 0.65;
      transition: all 1s;
    }
  }

  .setting-list,
  .sub-list,
  .double-sub-list {
    li {
      border: 0;
      padding: 0;
    }
  }

  .sub-list {
    margin-left: 30px;
  }

  .double-sub-list,
  .dbl-sub-heading {
    margin-left: 60px;
  }

  .qr-card {
    &.card {
      border: 0;
      box-shadow: 0px 3px 6px $black1;
      border-radius: 20px;
    }

    .card-heading {
      color: $grey7;
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 0;
    }
  }

  .project-settings-tab {
    padding: 10px 20px;

    .section-heading,
    .info-content {
      color: $grey7;
      font-size: 14px;
      margin-bottom: 0;
    }

    .add-label,
    .reset-label {
      margin-left: 20px;
    }

    .tag-list {
      .selected-tag {
        background-color: $grey12;
        border-radius: 12px;
        display: inline-block;
        padding: 3px 12px;
        color: $grey7;
        font-size: 13px;
        font-weight: 500;
        margin-right: 20px;
        margin-bottom: 15px;

        .close-tag {
          cursor: pointer;
          margin-left: 10px;
        }
      }
    }

    .status-container {
      .status-list {
        margin-right: 25px;

        .s-box {
          width: 1rem;
          height: 1rem;
          margin-right: 12px;
          border-radius: 3px;
          cursor: pointer;
          border: 0px;

          &.s-approved {
            background-color: $green3;
          }

          &.s-review {
            background-color: $orange-light;
          }

          &.s-delivered {
            background-color: $blue6;
          }

          &.s-rejected {
            background-color: $red1;
          }

          &.s-expired {
            background-color: $black;
          }
        }

        .s-label {
          color: $grey7;
          font-size: 13px;
          font-weight: 500;
          margin: 0;
        }
      }
    }

    .preview-box {
      border: solid $gray;
      border-width: 1.5px 1.5px 1.5px 0px;
      position: relative;
      padding: 10px;
      max-width: 230px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        border-left: 5px solid $green3;
      }
    }

    .info-icon {
      bottom: -2px;
      left: 0.35rem;
      font-size: 13px;
      cursor: pointer;
      font-family: 'Cairo', sans-serif;
      position: absolute;
    }

    .set-default-outline {
      border-width: 2px;
    }
  }

  .custom-secondary-tab .tab-container .nav-tabs .nav-item .nav-link.active {
    border-bottom: 6px solid $orange;
    padding-bottom: 0px;
  }
}

.equipment-formgroup .multiselect-dropdown .dropdown-btn {
  line-height: 1.02857143 !important;
  padding: 10px 4px !important;
}

.equipment-formgroup .multiselect-dropdown {
  margin-top: 0px !important;
}

.modal
  .heatmap-modal.modal-dialog
  .modal-content
  .row
  .company-select
  .multiselect-dropdown
  .dropdown-btn {
  line-height: 1.8;

  .selected-item {
    margin-bottom: 5px;
    padding: 0px 5px;
  }

  .dropdown-multiselect__caret {
    line-height: 22px;
  }
}

.modal:has(.home-popup) {
  overflow: hidden;
}

.welcome_modal {
  .dropdown-multiselect__caret:before {
    display: none;
  }

  .country-code {
    width: 10%;
    border-width: 0 0 1px;
    border-color: $grey11;
    border-radius: 0;

    .multiselect-dropdown .dropdown-btn {
      padding: 5px !important;
      font-size: 12px;
      height: 30px;
      &::placeholder {
        font-size: 12px;
      }
    }
  }
}

.approve-popup {
  &.modal-dialog {
    max-width: 596px;

    .modal-title {
      font-size: 14px;
      color: $grey7;
    }

    .modal-content {
      box-shadow: 0px 3px 6px #00000029;
      border-radius: 20px;
    }
  }

  delete-icon {
    &:hover {
      transform: none !important;
    }
  }

  .ng-trigger {
    &:focus,
    &:hover {
      box-shadow: none !important;
    }

    &:focus,
    &:active {
      background: $orange !important;
      color: $white !important;
    }
  }

  .ng2-tag-input {
    border-bottom: 0 !important;
  }
}

.qrcode-popup {
  .modal-title {
    font-size: 16px;
    color: $grey7;
    font-weight: bold;
  }
}

@media print {
  @page {
    size: A4 landscape;
    max-height: 100%;
    page-break-after: always;
  }

  .full-calendarrender-section {
    page-break-after: always;

    .fc .fc-scrollgrid-liquid {
      width: max-content;
    }
  }
}

.project-settings {
  .tab-content {
    padding: 16px;
  }

  .material-input {
    border: 0px;
    border-radius: 0px;
    border-bottom: 1px solid $black7;
  }
}

.yearly-section {
  display: flex;
  align-items: center;
}

.yearly-section label {
  margin-right: 1rem !important;
}

.yearly-section label,
.yearly-section select,
.yearly-section p {
  margin-right: 8px;
  white-space: nowrap;
}

.yearly-section p {
  color: $orange;
  font-size: 14px;
  margin-bottom: 0px;
}

.schedule-map .ng2-tag-input {
  height: auto;
  max-height: 60px;
  overflow-y: auto;

  .ng2-tags-container {
    padding: 0rem 0.15rem;

    .tag__text {
      line-height: 30px;
    }
  }
}

.schedule-map.auto-approve-map .ng2-tag-input {
  max-height: 90px;
  .progress-bar {
    display: none;
  }
}

.custom-modal.filter-popup.modal-dialog.report-filter.report-filter-modal .modal-content {
  padding: 0px;
}

.no-data h5 {
  font-size: 0.75rem;
}

.new-delivery-popup {
  overflow: hidden;

  .details-control {
    right: 50px;
    top: 5px;

    .ctl-list {
      background: transparent;
    }
  }

  .modal-footer {
    justify-content: center;
    padding: 1.75rem;
  }

  .concrete-id-col {
    height: 80px;
  }

  .popover-body {
    padding: 0;
    background: $white 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 11px #00000029;
    border: 1px solid #efefef;
    border-radius: 5px;
  }

  .popover .arrow {
    position: fixed;
    margin-left: 30px;
    display: none;
  }

  .bs-popover-top {
    margin-bottom: 1.5rem;
  }
}

.delivery-show {
  .popover-body {
    padding: 0;
    background: $white 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 11px #00000029;
    border: 1px solid #efefef;
    border-radius: 5px;
  }

  .popover .arrow {
    position: fixed;
    margin-left: 30px;
    display: none;
  }

  .bs-popover-top {
    margin-bottom: 1.5rem;
  }
}


select option {
  height: 30px;
}

select {
  height: 50px;
}

.pro-settings {
  padding: 1rem;

  .row {
    width: 100%;
    margin: 0px;

    select {
      width: 100%;
    }

    .form-group {
      margin-right: 1rem;

      @media (max-width: 767px) {
        margin-right: 0rem;
      }
    }
  }
}

.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.project-setting-lists {
  .form-group-border {
    border: 1px solid $gray;
  }

  .form-group-greenborder {
    background-color: $green3;
    height: 44px;
    width: 3px;
  }

  .panel-heading {
    background-color: transparent;
  }

  .card,
  .card-header,
  .list-group-item {
    background-color: transparent;
    border: 0;
  }

  .list-group-item {
    padding-left: 0px;
    padding-top: 0px;
  }

  .card-header {
    padding-left: 0px;
    padding-right: 0px;
  }

  .card-body {
    padding: 0px;
  }

  .form-control {
    border: 0px;
    background-color: transparent;
    padding-left: 0px;
  }

  .card-form-group,
  .list-group-border {
    border-bottom: 1px solid #ccc;
    border-radius: 0px;
  }

  .panel-group {
    border-bottom: 1px solid #ccc;
  }
}

.select-status-input {
  width: 100px;
}

tag-input-dropdown {
  font-size: 10px !important;
  /* Change this value to adjust the font size */
}
.project-settings-table {
  width: 65%;
  th,
  td {
    border: none;
    width: 300px;
  }
  .table thead th {
    border: none;
    width: 300px;
    vertical-align: text-bottom;
  }
  .s-box {
    width: 1rem;
    height: 1rem;
    margin-right: 0px;
    border-radius: 3px;
    cursor: pointer;
    margin-left: 20px;
    border: 1px solid black;
    &.bg-box {
      margin-left: 45px;
    }
  }
  .preview-tab {
    background: #ccc 0% 0% no-repeat padding-box;
    border-radius: 0px 5px 5px 0px;
    opacity: 1;
    padding: 5px 15px;
    border-left: 4px solid #000;
    font-size: 12px;
  }
  &.settings-tab-table {
    .s-box {
      margin-left: 0px;
    }
  }
}

.collage-person {
  .circle-name {
    border: 1px solid $white;
    &:not(:first-child) {
      margin-left: -5px;
    }
  }
}

.add-location-modal {
  .close {
    right: 15px;
  }
  .custom-col {
    max-width: 92%;
  }
}

.location-layout {
  padding: 50px 20px;
  .location-table {
    overflow-x: hidden;
  }
  .notification-setting-lists .list-group.list-group-horizontal .list-group-item {
    padding: 0.75rem 1.25rem;
  }
  .btn:disabled {
    background-color: $orange;
    color: $white;
    opacity: 0.65;
    &:hover {
      background-color: $white;
      color: $orange;
    }
  }
}
.location-sent-popup {
  max-width: 450px;
}
.custom-class {
  overflow-x: hidden !important;
}

.location-setting-category::before {
  content: '';
  position: absolute;
  left: 0px;
  top: 0px;
  bottom: 0;
  background: #f45e28;
  width: 5px;
  height: 50px;
  margin: auto;
  box-shadow: 3px 0 9px #f45e286c;
}

.main-accordion {
  .accordion-group-locationsettings:nth-child(even) {
    background-color: $grey9;
  }
  &.location-accordion {
    .card-header,
    .card-body {
      padding: 0px;
    }
  }
}


.tab-grid.companies-tab {
  .custom-control-input:checked ~ .custom-control-label::after {
    filter: brightness(0) invert(1);
  }
  .custom-checkbox .custom-control-input:checked ~ .custom-control-label::before {
    background-color: $orange !important;
    border-color: $orange;
    transition: all 1s;
  }
}

.project-settings-tabfile {
  input[type='file'] {
    border: none;
    background: linear-gradient(to right, white 38%, #f45e28 22%);
    background-size: 100% 1px;
    background-position: 0 100%;
    background-repeat: no-repeat;
    padding-bottom: 2px;
  }

  input[type='file']::file-selector-button {
    margin-right: 20px;
    background-color: transparent;
    transition: all 1s;
    opacity: 1;
    color: transparent;
    padding: 2px 10px;
    border-radius: 30px;
    font-size: 12px;
    cursor: pointer;
  }
  .custom-label {
    height: calc(1em + 0.65rem + 2px);
    display: inline-block;
    width: fit-content;
    cursor: pointer;
    margin-right: 20px;
    padding: 2px 10px;
    border-radius: 30px;
    font-size: 12px;
    background-color: rgba(244, 96, 42, 0.2);
    color: #f45e28;
    border-color: transparent;
    z-index: 2;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
      box-shadow 0.15s ease-in-out;
    position: absolute;
    top: 0;
    right: 0;
    line-height: 1.5;
    overflow: hidden;
    font-weight: 400;
    left: 0;
    margin-bottom: 0.5rem;
    &::after {
      display: none;
    }
  }
  .spinner {
    position: absolute;
    left: 90px;
    top: 2px;
  }
}
.view-logistic-button {
  margin: auto;
}

.welcome_modal.enter-detail-modal .country-code .multiselect-dropdown .dropdown-btn {
  padding: 10px 0px !important;

  line-height: 0px !important;
}

.enter-detail-customform {
  overflow-x: hidden;
  .enter-detail-heading {
    margin-right: 250px;
  }
}

.additional-location-detail {
  input.floating-input {
    height: 37px !important;
  }
  .floating-input:focus ~ label,
  .floating-input:not(:placeholder-shown) ~ label {
    left: 0px;
  }
}
.primary-tooltip {
  .tooltip {
    position: relative;
    display: inline-block;
    opacity: 1;
    margin-left: 3px;
    margin-top: 3px;
    z-index: 999;
    &.custom-tooltip-margin {
      margin-left: 5px;
      margin-top: 5px;
    }
    &.project-settings-tooltip {
      top: 15px;
      left: 5px;
      .info-icon {
        bottom: 0px;
        left: 0.5rem;
      }
    }
  }
  .tooltip .tooltiptext {
    width: 375px;
    color: $black;
    text-align: center;
    background: #ffffff 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #00000029;
    border-radius: 8px;
    opacity: 1;
    font-size: 12px;
    position: absolute;
    z-index: 1;
    bottom: 30px;
    padding: 5px 2px;
    left: -75%;
    visibility: hidden;
    &.tooltiptext-info {
      width: 200px;
    }
    &.settings-tooltiptext-info {
      width: 160px;
      bottom: -50px;
    }
  }

  .tooltip:hover .tooltiptext {
    visibility: visible;
  }

  .tooltip .tooltiptext .arrow-down {
    visibility: hidden;
  }

  .tooltip:hover .arrow-down {
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 5px;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    left: -30px;
    margin-left: 35px;
    position: absolute;
    bottom: 25px;
    background-color: $white;
    box-shadow: 0px 3px 6px #00000029;
    visibility: visible;
  }
  .dot-border-info {
    height: 16px;
    width: 16px;
    background-color: #efefef;
    border-radius: 50%;
    display: inline-block;
    color: #374957;
    text-align: center;
    position: relative;
    cursor: pointer;
    &.location-border-info {
      height: 20px;
      width: 20px;
    }
  }
}
.dropdown-country-code {
  .timezone-formgroup .multiselect-dropdown .dropdown-btn {
    padding: 5px 0px !important;
    font-size: 12px;
    &::placeholder {
      font-size: 12px;
    }
  }
  &::after {
    .dropdown-multiselect__caret {
      .country-code {
        width: 30%;
        border-width: 0 0 1px;
        border-color: $grey11;
        border-radius: 0;
        padding: 5px 0px !important;
      }
    }
  }
}

.tooltip:hover .tooltiptext {
  visibility: visible;
}

.tooltip .tooltiptext .arrow-down {
  visibility: hidden;
}

.tooltip:hover .arrow-down {
  border-width: 0 2px 2px 0;
  display: inline-block;
  padding: 5px;
  transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  left: -30px;
  margin-left: 35px;
  position: absolute;
  bottom: 25px;
  background-color: $white;
  box-shadow: 0px 3px 6px #00000029;
  visibility: visible;
}
.dot-border-info {
  height: 16px;
  width: 16px;
  background-color: #efefef;
  border-radius: 50%;
  display: inline-block;
  color: #374957;
  text-align: center;
  position: relative;
  cursor: pointer;
  &.location-border-info {
    height: 20px;
    width: 20px;
  }
}

.dashboard-plan {
  .card-style-dashboard {
    box-shadow: 0px 0px 20px #d7dee365;
    border-radius: 8px;
  }
  .rotate-next-arrow {
    transform: rotate(180deg);
    filter: blur(100%);
  }
  .plan-content-bg {
    z-index: 998;
    &::-webkit-scrollbar {
      height: 4px;
    }
    .ng2-pdf-viewer-container {
      max-height: auto;
      height: 13rem;
      min-height: auto;
      overflow: auto;
      &::-webkit-scrollbar {
        height: 4px;
      }
    }
    &.expand-view-pdf {
      .ng2-pdf-viewer-container {
        height: calc(100vh - 105px);
      }
    }
    &.plan-no-content-bg {
      padding: 90px;
    }
  }
  .plans-icons-style {
    background-color: $white;
    z-index: 9;
  }
  .plan-content-bg-img {
    position: absolute;
    bottom: -165px;
  }
  .desktop-thumbnail-images {
    width: calc(100vw - 280px);
    height: calc(100vh - 0px);
    z-index: 999;
    border-radius: 10px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.25);
    .site-plan-search-icon:hover input[type='number'] {
      width: 200px;
    }
  }
  .desktop-thumbnail-innerimage {
    height: calc(100vh - 50px);
  }
  .site-plan-search-position {
    right: 15px;
    top: 5px;
  }
  .siteplan-pagenumbers {
    margin: auto 10px;
    display: inline-flex;
    .form-control {
      width: 28px;
      padding: 2px;
      height: 25px;
      text-align: center;
    }
  }
}
.image-container2 {
  overflow: auto;
  position: relative;
}

.image-container img {
  width: 100%;
  height: 100%;
  transform-origin: 0 0;
  transition: transform 0.2s ease-in-out;
}
.image-container2 img {
  width: 100%;
  transform-origin: 0 0;
  transition: transform 0.2s ease-in-out;
}

.arrow-expandedview {
  height: 20px !important;
  width: 20px !important;
}

.page-content.show_pagecontent:has(.dashboard-plan .desktop-thumbnail-images) {
  overflow: hidden;
  height: 100vh;
}

//guest user
body .modal-body .addcalendar-details .custom-material-form .row .guest-company-select {
  .multiselect-dropdown .dropdown-btn .selected-item {
    border: 1px solid $orange;
    background: $orange;
    font-size: 12px;
  }
  .multiselect-item-checkbox input[type='checkbox'] + div {
    &::before {
      color: $orange;
      border: 2px solid $orange;
    }
  }

  .multiselect-item-checkbox input[type='checkbox']:checked + div {
    &::before {
      background: $orange;
    }
  }
}

.guest-company-select {
  .dropdown-list li,
  .dropdown-btn {
    font-size: 12px;
  }
}

.select-dropdown option {
  font-size: 10px;
  padding: 5px;
}

.truncate {
  white-space: nowrap !important;
  overflow: hidden;
  text-overflow: ellipsis;
}

.projectsettings-listgroupitems {
  .list-group-item {
    border: 0px;
    background-color: transparent;
    padding-left: 0px;
    padding-bottom: 0px;
    padding-right: 0px;
  }
  .primary-tooltip .dot-border-info.location-border-info {
    height: 15px;
    width: 15px;
  }
  .primary-tooltip .tooltip.project-settings-tooltip .info-icon {
    bottom: -3px;
    left: 0.35rem;
  }
}

.custom-radio-option {
  overflow: hidden;
  .custom-control-input:focus ~ .custom-control-label::before,
  .custom-control-input:checked ~ .custom-control-label::before {
    box-shadow: none;
    background-color: $white;
    border-color: $orange;
  }
  .custom-radio .custom-control-input:checked ~ .custom-control-label::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='10' height='10' viewBox='-17 -17 34 34'%3e%3ccircle r='17' fill='%23f45e28'/%3e%3c/svg%3e");
  }
  .custom-control-label::after {
    top: 0.75rem;
    transform: translate(-50%, -50%);
    left: -0.97rem;
  }
}

.table.table-custom.mb-0.members-table.guest-members-table .bar {
  top: 139px;
}

.table.table-custom.mb-0.members-table.members-table-resize .bar {
  top: 150px;
}
.guestdelivery-equipment-select {
  .multiselect-dropdown .dropdown-btn {
    border: 0px !important;
    padding: 9px 4px !important;
  }
  .multiselect-dropdown {
    border-bottom: 1px solid #bebebe;
  }
}
.site-plan-search-icon {
  position: relative;
  width: 32px;
  height: 32px;
  background: $white;
  border-radius: 50%;
  @extend .c-pointer;

  input[type='number'] {
    width: 0;
    height: 32px;
    border: 0;
    border-radius: 50%;
    @extend .c-pointer;
    &::placeholder {
      padding-left: 30px;
      padding-right: 30px;
    }
  }

  .icon {
    position: absolute;
    top: 3px;
    right: 10px;
  }

  &:hover input[type='number'],
  input[type='number']:focus {
    width: 100px;
    position: absolute;
    border-radius: 20px;
    right: 0;
    top: 0;
    padding-right: 35px;
    border: 1px solid #bebebe;
    &::placeholder {
      padding-left: 5px;
      padding-right: 5px;
    }
    input[type='number']::-webkit-inner-spin-button,
    input[type='number']::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    input[type='number'] {
      -moz-appearance: textfield;
    }
  }

  .input-hover-disable {
    width: 100px;
    position: absolute;
    border-radius: 20px;
    right: 0;
    top: 0;
    padding-right: 35px;
    &::placeholder {
      padding-left: 5px;
      padding-right: 5px;
    }
  }
}

.custom-alert {
  color: #0f0f0f;
  background-color: #e4e2e1;
  border-color: #d6d6d6;
  padding: 0.75rem 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.timezone-formgroup {
  .multiselect-dropdown .dropdown-btn {
    border-radius: 0px !important;
    border: 0px !important;
    border-bottom: 1px solid #bebebe !important;
    font-size: 12px;
    padding: 10px 0px !important;
  }
  .dropdown-list .filter-textbox input {
    padding: 0px !important;
    font-size: 12px;
  }
  .multiselect-item-checkbox input[type='checkbox'] + div {
    font-size: 12px;

    padding-left: 0px !important;

    &::before {
      display: none;
    }

    &::after {
      display: none;
    }
  }
  .multiselect-dropdown .dropdown-btn .selected-item {
    background: transparent !important;
    border: 0px !important;
    color: #212529 !important;
    box-shadow: none !important;
    padding: 0px !important;
    display: contents;
    a {
      display: none;
    }
  }
  .dropdown-list ul {
    max-height: 100px !important;
  }
  &.homepage-formgroup {
    .dropdown-list li {
      padding: 6px 4px !important;
    }
  }
  &.delivery-formgroup {
    .dropdown-list ul .filter-textbox {
      padding: 0px 10px 6px;
    }
  }
}
.delivery-formgroup {
  margin-left: auto;
}

.form-group.timezone-formgroup.vehicleType-formgroup .multiselect-dropdown .dropdown-list {
  margin-top: 0px;
  ul.item2 {
    max-height: 45px !important;
  }
}

.form-switch .form-check-input {
  --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e") !important;
  width: 1.75rem;
}

.project-information-checkbox {
  .form-check-input:checked[type='checkbox'] {
    background-color: $white;
    border-color: $orange;
    background-size: 8px 8px;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='4' viewBox='0 0 8 8'%3e%3cpath fill='DarkOrange' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3e%3c/svg%3e");
  }
  .form-check-input[type='checkbox'] {
    border-color: $orange;
  }
  &.dropdown-options {
    .form-check-input[type='checkbox'] {
      border-color: #adb5bd;
      right: 0;
      left: auto;
      width: 0.8rem;
      height: 0.8rem;
    }
    .form-check-input:checked[type='checkbox'] {
      background-color: $white;
      border-color: $orange;
      background-size: 6px 6px;
    }
  }
}

.plans-google-map {
  .map-container {
    height: 200px !important;
    width: 375px !important;
  }
}
.follo-switch.list-group.list-group-horizontal {
  .switch.switch-medium small {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    position: absolute;
    top: 1px;
    left: 0;
    transition: 0.3s ease-out all;
    -webkit-transition: 0.3s ease-out all;
    border-radius: 100%;
    width: 15px;
    height: 15px;
  }
}

.notify-setting-chkbtn {
  .form-check-input:disabled {
    background-color: $gray-f;
    border: 1px solid #adb5bd;
  }
}

.notexpanded-contentbg {
  height: 200px;
  overflow: auto;
  border: 1px solid #ccc;
  img {
    display: block;
    max-width: 300%;
    max-height: 300%;
    width: auto;
    height: auto;
  }
}

.dashboard-plan .desktop-thumbnail-images *::-webkit-scrollbar {
  height: 3px;
}

.inspection-status {
  margin-right: 15px;
  display: flex;
  align-items: center;
  gap: 5px;
}
.sidemenu-count {
  margin-left: 22px;
}

.custom-select-wrapper {
  position: relative;
  display: inline-block;
  margin-left: -4px;
  width: 175px;
}

.custom-select-dropdown {
  appearance: none;
  background-color: #fff;
  border: 1px solid #ccc;
  padding: 10px;
  border-radius: 5px;
}

.custom-select-dropdown:focus {
  border-bottom-color: #007bff;
}

.custom-select-wrapper::after {
  content: '\25BC';
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  font-size: 12px;
  color: #333;
}

/* Style the options in the dropdown */
.custom-select-dropdown option {
  color:$black ;
  background-color: $white;
}

.filter-list-custom {
  list-style: none;
  padding: 0;
  margin: 0;
  font-size: 14px;
}

.filter-list-custom li {
  padding: 10px 10px 5px;
  cursor: pointer;
  border-bottom: 0px;
}

.filter-list-custom li:hover {
  background-color: transparent;
}

.filter-list-custom li:last-child {
  border-bottom: none;
}

.sub-filter-popup {
  background-color: $white;
  border: 1px solid #ddd;
  position: relative;
  left: 0;
  z-index: 10;
  display: block;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 16px;
  width: 250px;
  font-family: 'Arial', sans-serif;
}

.sub-filter-popup h3 {
  font-size: 16px;
  margin-bottom: 10px;
}

.sub-filter-popup ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sub-filter-popup li {
  padding: 5px 0;
}

.sub-filter-popup input[type='checkbox'] {
  margin-right: 10px;
}

.sub-filter-popup button {
  margin-top: 10px;
  background-color: #ff6600;
  color: $white;
  border: none;
  padding: 5px 10px;
  cursor: pointer;
}

.sub-filter-popup button:hover {
  background-color: #e65c00;
}

.text-style {
  font-size: 14px;
  font-weight: bold;
  font-family: 'Cairo', sans-serif;
  color: #3c3c3c;
  margin: 5px 0;
}

.styled-h1 {
  font-size: 14px;
  font-weight: bold;
  font-family: 'Cairo', sans-serif;
  color: #3c3c3c;
  margin: 0;
  display: flex;
  align-items: center;
}

.styled-select {
  font-size: 12px;
  font-weight: bold;
  font-family: 'Cairo', sans-serif;
  color: #3c3c3c;
  padding: 0px;
  border: 1px solid #fcf9f9;
  border-bottom: 1px solid #ccc;
  border-radius: 0px;
  background-color: $white;
  width: 200px;
  cursor: pointer;
  height: 40px;
}
.inspection-chevron-down {
  position: absolute;
  transform: translate(-50%, -50%);
  top: 35px;
  left: 220px !important;
}
.styled-option {
  font-size: 12px;
  font-weight: bold;
  font-family: 'Cairo', sans-serif;
  color: #3c3c3c;
  padding: 10px;
}

.form-header-align {
  margin-left: 300px;
}

.filter-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
}

.filter-options {
  list-style-type: none;
  padding: 0;
  margin: 0;
  max-height: calc(100vh - 385px);
  overflow-y: auto;
  overflow-x: hidden;
}

.filter-option {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  font-size: 14px;
}

.filter-option input {
  margin-right: 8px;
}

.selected-filters {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.filter-tag {
  background-color: #f1f1f1;
  border: 1px solid #ccc;
  padding: 5px 10px;
  font-size: 14px;
  font-family: 'Cairo', sans-serif;
  color: #333;
  display: inline-flex;
  align-items: center;
  border-radius: 40px;
}

.filter-tag .filter-count {
  background-color: $orange;
  color: $white;
  font-size: 12px;
    padding: 0px;
    border-radius: 50%;
    margin-left: 5px;
    height: 20px;
    width: 20px;
    display: flex;
    justify-content: center;
}



.close-icon-subfilter {
  margin-left: 8px;
  color: #999;
  cursor: pointer;
  font-size: 16px; /* Adjust size as needed */
  line-height: 1;
}

.filter-tag:hover {
  background-color: #e0e0e0;
  border-color: #bbb;
}

.filter-tag:hover::after {
  color: #333;
}

.filter-count {
  cursor: pointer;
  color: blue;
  margin-left: 5px;
}

.sub-filter-popup1 ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.sub-filter-popup1 li {
  padding: 5px 0;
  font-size: 14px;
}

.all-calendar-form-control {
  width: 150px;
  word-break: break-word;
}

/* Styling for the entire filter dropdown container */
.filter-container {
  display: flex;
  align-items: center;
  position: relative;
}

/* Styling for the Angular Material dropdown */
.filter-dropdown {
  width: 250px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Custom styling for the search input inside the dropdown */
.filter-search-input {
  width: 100%;
  flex: 1;
  border: none;
  outline: none;
  padding: 8px;
  padding-right: 30px; /* Add some padding to the right to make space for the icon */
  box-sizing: border-box;
  font-size: 14px;
}


/* Custom styling for the mat-select options */
.mat-option {
  font-size: 14px;
  padding: 10px 20px;
}

/* Styling for icons (search and filter) next to the dropdown */
.filter-icons {
  display: flex;
  gap: 10px;
  align-items: center;
  position: absolute;
  top: 15px;
  right: -40px;
}

.filter-icons mat-icon {
  cursor: pointer;
  color: #555;
}

/* Add custom hover styling if needed */
.mat-option:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.sub-filter-popup1 {
  position: absolute;
  top: 100%;
  left: 0;
  width: 200px;
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  background-color: $white;
  border: 1px solid #ccc;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}


/* Custom scrollbar */
.sub-filter-popup1::-webkit-scrollbar {
  width: 8px; /* Adjust the width */
}

.sub-filter-popup1::-webkit-scrollbar-track {
  background: transparent; /* No track background */
}

.sub-filter-popup1::-webkit-scrollbar-thumb {
  background-color: #f27020; /* Scrollbar thumb color */
  border-radius: 10px; /* Rounded edges */
  border: 2px solid transparent; /* Transparent space around scrollbar */
}

.filter-search-container {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}



.dropdown-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none; /* This makes sure the icon doesn't interfere with input field interaction */
  font-size: 14px; /* Adjust the size of the icon */
  color: #999; /* You can change the color based on your design */
  margin-left: 8px; /* Adjust as needed */
  cursor: pointer;
}

.custom-divider {
  height: 1px;
  width: auto;
  background-color: #ccc;
  margin: 7px 4px;
}

.filter-search-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
}

.search-icon {
  margin-right: 8px; /* Adjust as needed */
}

/* timeslot syle */

.timeslot_label{
  font-size: 15px;
  margin-right: 281px;
  font-weight: 600;
}

.date-time-picker {
  max-width: 400px;
  margin: 0 auto;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #fff;
}

.timezone {
  font-size: 13px;
  font-weight: 700;
}

.calendar {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
}

.day-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.day-tab {
  text-align: center;
  padding: 10px 16px;
  cursor: pointer;
  border: 1px solid transparent; /* Default, no border */
  transition: border-color 0.3s ease;
}

.day-date {
  font-size: 12px;
  font-weight: bold;
}

.day-name {
  font-size: 12px;
}

.day-tab:hover {
  border-color: #FF6D28; /* Orange border on hover */
}

.day-tab.selected {
  border: 2px solid #FF6D28; /* Orange border for selected tab */
  border-radius: 8px;
  color: #FF6D28;
}

.nav-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 0 10px;
}

.time-frame {
  display: flex;
  align-items: center;
  margin: 15px 0;
}

.am-pm-toggle {
  display: flex;
}

.am-pm-toggle button {
  font-size: 12px;
  background: none;
  border: 1px;
  border-radius: 5px;
  padding: 3px 6px;
  margin-right: 10px;
}

.am-pm-toggle .active {
  color: #FF6D28;
}

.duration {
  font-size: 14px;
}

.time-slots {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin-top: 10px;
  overflow-y: scroll;
  border: 1px solid #f7f3f3;
  padding: 12px;
  max-height: 105px;
  overflow-x: hidden;
}

.time-slots button {
  padding: 5px;
  border: none;
  background-color: #f2f2f2;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.time-slots button.selected {
  background-color: #FF6D28;
  color: white;
}

.time-slots button:hover {
  background-color: #ddd;
}

.location_multidropdown{
  width: 200%;
}

.duration_dropdown {
  width: 90px;
}

// equipment log

.status_slider {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
  border-radius: 12px
}

.status_slider input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 4px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #74c707;
}

input:checked + .slider:before {
  transform: translateX(20px);
}

.carbon_calendar {
  margin: bottom 3px;
}

.carbon-status-text {
  margin-left: 10px;
  font-size: 14px;
  color: #333;
}

.restore-icon {
  height: 33px;
}

// duration hours

.duration-dropdown {
  position: relative;
  display: inline-block;
  font-family: Arial, sans-serif;
}

.duration-dropdown-toggle {
  background: none;
  border: none;
  font-size: 12px;
  color: #000;
  cursor: pointer;
  font-family: 'Cairo', sans-serif;
}

.duration-dropdown-menu {
  position: absolute;
  background-color: #fff;
  border: 1px solid #ddd;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  padding: 8px;
  z-index: 1000;
  width: 120px;
}

.duration-container {
  display: flex;
  justify-content: space-between;
}

.duration-column {
  flex: 1;
  text-align: center;
  font-family: 'Cairo', sans-serif;
  font-size: 14px;
}

.duration-header {
  font-weight: bold;
  margin-bottom: 8px;
  font-family: 'Cairo', sans-serif;
  font-size: 14px;
}

.scrollable {
  max-height: 120px;
  overflow-y: auto;
}

.duration-item {
  padding: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.duration-item:hover {
  background-color: #f0f0f0;
}

.duration-item.selected {
  color: #f00;
}

.divider {
  width: 1px;
  background-color: #ccc;
  margin: 0 8px;
}

.arrow-icon {
  display: inline-block;
  margin-left: 5px;
  margin-bottom: 3px;
  width: 8px;
  height: 8px;
  border: solid #000;
  border-width: 2px 2px 0 0;
  transform: rotate(132deg); /* Arrow pointing down */
  transition: transform 0.3s ease;
}

.arrow-icon.open {
  transform: rotate(-45deg); /* Arrow pointing up */
}

.scheduler-form-input {
  .tag-layout {
    height: 100px;
    overflow-y: auto;
    margin-bottom: 20px;
  }
.ng2-dropdown-menu.ng2-dropdown-menu--inside-element {
  position: absolute !important;
  left: 20px !important;
  top: 50% !important;
  }
}



// filter scroll

.scrollable-filter-container {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 10px;
}

.filter-popup-custom {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  width: 265px;
}


.outlined-timeslot-box{
  border: 2px solid black;
  padding: 17px;
  margin: 14px;
  width: auto;
}

.timezone-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 6px;
}

.required {
  color: red;
}

.timezone-box {
  margin-top: 6px;
  background-color: #f5f5f5;
  padding: 5px 19px;
  border-radius: 6px;
  font-size: 12px;
  color: #333;
}

.custom-dropdown {
  position: relative;
  width: 250px;
  margin-bottom: 1rem;
}

.dropdown-header {
  padding: 0.3rem;
  border: 1px solid #ccc;
  background: #fff;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  border-top: hidden;
  border-left: hidden;
  border-right: hidden;
  font-size: 12px;
}

.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ccc;
  background: #fff;
  z-index: 10;
}

.dropdown-item {
  display: block;
  padding: 0.5rem;
  cursor: pointer;
}

/* Default checkbox styling */
.dropdown-item input[type="checkbox"] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 3px;
  outline: none;
  cursor: pointer;
  position: relative;
  background-color: white;
  border: 2px solid #f45e28;
}

/* Change background to orange when checked */
.dropdown-item input[type="checkbox"]:checked {
  background-color: #f45e28;
  border: 2px solid #f45e28;
}

.dropdown-item input[type="checkbox"]::after {
  content: '';
  display: block;
  width: 4px; /* Adjusted size for smaller checkbox */
  height: 8px; /* Adjusted size for smaller checkbox */
  border: solid white;
  border-width: 0 2px 2px 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
}

/* White checkmark inside when checked */
.dropdown-item input[type="checkbox"]::before {
  font-size: 12px;
  color: white;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: none;
}

.dropdown-item input[type="checkbox"]:checked::before {
  display: block;
}


.dropdown-item:hover {
  background-color: #f0f0f0;
}

.selected-filter {
  display: inline-flex;
  align-items: center;
  background-color: #f0f0f0; /* Light gray background */
  color: #333; /* Dark text */
  padding: 1px 7px;
  border-radius: 4px; /* Rounded corners */
  margin-right: 5px; /* Space between tags */
  font-size: 12px;
  font-weight: 500;
}

.selected-filter .close-icon {
  margin-left: 8px;
  cursor: pointer;
  font-weight: bold;
}


.dropdown-search {
  width: 100%;
  padding: 5px;
  margin-bottom: 5px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 12px;
}



//timeslot

.timeslot-container {
  width: 100%;
  height: 100%;
  max-width: 730px;
  margin: auto;
  padding: 0px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.timeslot-today-btn {
  background: #f5f5f5;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.timeslot-date-picker {
  position: relative;
}

.time-selection {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  width: 300px;
  border-radius: 30px;
}

.timeslot-to-text {
  font-weight: bold;
}

.calendar-icon:hover {
  opacity: 1;
}

/* Ensures the select box is properly styled */
.timeListDrop {
  height: 30px;
  width: 100px;
  font-size: 14px;
  appearance: none; /* Removes default dropdown styling */
  background-color: white;
  border: 1px solid #ccc;
  cursor: pointer;
}

/* Style the dropdown arrow */
.timeListDropWrapper {
  position: relative;
  display: inline-block;
}

/* Custom dropdown arrow */
.timeListDropWrapper::after {
  content: "▼";
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  pointer-events: none;
}

/* Force options to scroll properly */
.timeListDrop option {
  font-size: 14px;
  padding: 5px 10px;
}

/* Ensure dropdown expands properly */
.timeListDrop:focus {
  overflow: visible;
}

.dropdown-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

.timeslot-custom-dropdown {
  width: 100%;
  padding: 8px;
  font-size: 14px;
  background: white;
  border: 1px solid #ccc;
  cursor: pointer;
  appearance: none;
  max-height: 200px;
  overflow-y: auto;
}

.timeslot-custom-dropdown::-webkit-scrollbar {
  width: 8px;
}

.timeslot-custom-dropdown::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

/* Limit dropdown height */
.timeslot-custom-dropdown option {
  padding: 8px;
  font-size: 14px;
  color: #333;
}

/* On hover */
.timeslot-custom-dropdown option:hover {
  background-color: #f4f4f4;
  color: #000;
}

.timeslot-popup {
  display: flex !important;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 1050 !important;
  width: 80%;
  max-width: 600px;
  min-height: 600px; /* Set a minimum height */
  height: auto; /* Allow the height to adjust */
  overflow: visible;
}

.timeslot-modal-body {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%; /* Ensure it fills the modal */
  padding: 15px;
}

.full-calendar {
  flex-grow: 1; /* Allow calendar to take available space */
  min-height: 400px; /* Ensure calendar is visible */
}

.timeslot-modal-dialog-centered {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh; /* Ensures full height */
}

.timeslot-modal-content {
  width: 100%;
  height: auto;
  max-height: 90vh; /* Prevents overflow */
}

.timeslot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 15px;
  padding: 10px;
  background: #f9f9f9;
  border-radius: 8px;
  margin-bottom: 15px;
}

.custom-select {
  border-radius: 8px;
  padding: 5px;
  border: 1px solid #ccc;
  font-size: 14px;
}

.date-picker {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.hidden-input {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.actions {
  margin-top: 15px;
}

.cancel-btn {
  background: #ccc;
  border-radius: 5px;
  padding: 8px 15px;
}

.update-btn {
  background: #ff7043;
  color: #fff;
  border-radius: 5px;
  padding: 8px 15px;
}

////
.date-picker-container {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 5px 10px;
  border-radius: 8px;
  cursor: pointer;
  width: fit-content;
  margin-left: 175px;
  margin-bottom: 13px;
}

.selected-date {
  font-size: 14px;
  color: #FF6B6B;
  font-weight: bold;
  cursor: pointer;
}

.calendar-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
  opacity: 0.6;
  transition: opacity 0.2s ease-in-out;
}


.nav-arrow {
  width: 16px;
  height: 16px;
  cursor: pointer;
}



.today-btn {
  background-color: #eef1f3;
  color: #0c0c0c;
  border-color: #0c0c0c;
  padding: 5px 25px;
  font-size: 12px;
  border-radius: 21px;
  cursor: pointer;
  margin-right: 153px;
  width: 124px;
}
.today-btn:hover {
  background-color: #0056b3;
}


.child-header {
  background-color: #f8f9fa;
  font-size: 10px;
}

.utilities_child{
  float: left;
}

.utilities_parent{
  left:auto
}
.bill_link{
  margin-left: 10px;
}


//exception

.exception-section {
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
}

.section-title {
  font-weight: 600;
  margin-bottom: 16px;
  font-size: 14px;
  color: #333;
}

.exception-form-group {
  display: flex;
  flex-direction: column;
  min-width: 240px;

  .multiselect-dropdown {
    .dropdown-btn {
      border-radius: 0px !important;
      border: 0px !important;
      font-size: 12px;
      border-top: 0;
      border-left: 0;
      border-right: 0;
      padding: 4px 0px;
      border-bottom: 1px solid $light-grey8;
      .dropdown-down {
        border-top: 7px solid $grey46;
        border-left: 7px solid transparent;
        border-right: 7px solid transparent;
      }

      .dropdown-up {
        border-bottom: 7px solid $grey46;
        border-left: 7px solid transparent;
        border-right: 7px solid transparent;
      }
      .selected-item {
        border: 1px solid $orange !important;
        background: $orange !important;
        margin-bottom: 7px;
        word-wrap: break-word;
        width: 100% !important;
        max-width: 100% !important;
        font-size: 11px !important;
      }
    }
  }

  .dropdown-list {
    margin: 0;
    font-size: 12px;

    li.no-data {
      text-align: center;

      h5 {
        font-size: 12px;
      }
    }
  }
  .filter-textbox input {
    padding: 0px !important;
    font-size: 10px;
    .multiselect-item-checkbox input[type='checkbox'] + div {
      &::before {
        color: $orange;
        border: 2px solid $orange;
      }
    }

    .multiselect-item-checkbox input[type='checkbox']:checked + div {
      &::before {
        background: $orange;
      }
    }

    .multiselect-dropdown .dropdown-btn .selected-item {
      border: 1px solid $orange;
      background: $orange;
    }
  }

}

.exception-form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.exception-form-label {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.tooltip .tooltiptext {
  width: 220px;
  left: 0;
}

.note-text {
  font-size: 11px; /* or larger like 16px */
  font-weight: 650;
  display: block; /* to force new line if needed */
}
