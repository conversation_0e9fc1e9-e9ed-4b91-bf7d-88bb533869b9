import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, UntypedFormBuilder } from '@angular/forms';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { of, throwError } from 'rxjs';
import { NgxFileDropModule, NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';

import { GuestCraneDetailsComponent } from './guest-crane-details.component';
import { ProjectSharingService } from '../../../../services/projectSharingService/project-sharing.service';
import { DeliveryService } from '../../../../services/profile/delivery.service';
import { ProjectService } from '../../../../services/profile/project.service';
import { MixpanelService } from '../../../../services/mixpanel.service';

describe('GuestCraneDetailsComponent', () => {
  let component: GuestCraneDetailsComponent;
  let fixture: ComponentFixture<GuestCraneDetailsComponent>;
  let modalService: jest.Mocked<BsModalService>;
  let projectSharingService: jest.Mocked<ProjectSharingService>;
  let toastrService: jest.Mocked<ToastrService>;
  let modalRef: jest.Mocked<BsModalRef>;

  beforeEach(async () => {
    modalRef = {
      hide: jest.fn(),
      content: {},
      setClass: jest.fn()
    } as unknown as jest.Mocked<BsModalRef>;

    const modalServiceSpy = {
      show: jest.fn().mockReturnValue(modalRef)
    };
    const deliveryServiceSpy = {};
    const projectServiceSpy = {};
    const projectSharingServiceSpy = {
      guestGetEquipmentCraneRequest: jest.fn(),
      guestAddCraneRequestAttachment: jest.fn(),
      guestCreateCraneRequestComment: jest.fn()
    };
    const toastrServiceSpy = {
      success: jest.fn(),
      error: jest.fn()
    };
    const mixpanelServiceSpy = {
      addGuestUserMixpanelEvents: jest.fn()
    };

    await TestBed.configureTestingModule({
      declarations: [GuestCraneDetailsComponent],
      imports: [ReactiveFormsModule, NgxFileDropModule],
      providers: [
        { provide: BsModalService, useValue: modalServiceSpy },
        { provide: BsModalRef, useValue: {} },
        { provide: DeliveryService, useValue: deliveryServiceSpy },
        { provide: ProjectService, useValue: projectServiceSpy },
        { provide: ProjectSharingService, useValue: projectSharingServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: MixpanelService, useValue: mixpanelServiceSpy },
        { provide: ModalOptions, useValue: {} },
        UntypedFormBuilder
      ]
    }).compileComponents();

    modalService = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    projectSharingService = TestBed.inject(ProjectSharingService) as jest.Mocked<ProjectSharingService>;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
  });

  beforeEach(() => {
    // Mock localStorage with base64 encoded values
    jest.spyOn(Storage.prototype, 'getItem').mockImplementation((key) => {
      switch (key) {
        case 'guestProjectId':
          return btoa('123');
        case 'guestParentCompanyId':
          return btoa('456');
        case 'guestId':
          return btoa('789');
        default:
          return null;
      }
    });

    fixture = TestBed.createComponent(GuestCraneDetailsComponent);
    component = fixture.componentInstance;
    component.data = { id: 1 };
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize comment form', () => {
    expect(component.commentDetailsForm).toBeTruthy();
    expect(component.commentDetailsForm.get('comment')).toBeTruthy();
  });

  it('should get crane request details successfully', () => {
    const mockResponse = {
      data: {
        id: 1,
        attachments: [{ id: 1, fileName: 'test.pdf' }],
        comments: [{ id: 1, comment: 'Test comment' }],
        history: [
          { id: 1, type: 'comment', action: 'added' },
          { id: 2, type: 'status', action: 'changed' }
        ]
      }
    };

    component.ProjectId = 123;
    component.ParentCompanyId = 456;
    component.CraneRequestId = 1;

    projectSharingService.guestGetEquipmentCraneRequest.mockReturnValue(of(mockResponse));

    component.getNDR();

    expect(projectSharingService.guestGetEquipmentCraneRequest).toHaveBeenCalledWith({
      CraneRequestId: 1,
      ParentCompanyId: 456,
      ProjectId: 123
    });
    expect(component.NDRData).toEqual(mockResponse.data);
    expect(component.fileArray).toEqual(mockResponse.data.attachments);
    expect(component.commentList).toEqual(mockResponse.data.comments);
    expect(component.loader).toBe(false);
  });

  it('should handle error in getNDR', () => {
    projectSharingService.guestGetEquipmentCraneRequest.mockReturnValue(throwError(() => new Error('API Error')));
    component.getNDR();
    expect(component.loader).toBe(true);
  });

  it('should handle getResponsiblePeople correctly', () => {
    const person = { firstName: 'John', lastName: 'Doe' };
    expect(component.getResponsiblePeople(person)).toBe('JD');

    const personWithoutName = {};
    expect(component.getResponsiblePeople(personWithoutName)).toBe('UU');
  });

  it('should handle comment submission successfully', () => {
    const mockResponse = { message: 'Success' };
    projectSharingService.guestCreateCraneRequestComment.mockReturnValue(of(mockResponse));

    component.commentDetailsForm.patchValue({ comment: 'Test comment' });
    component.onCommentSubmit();

    expect(projectSharingService.guestCreateCraneRequestComment).toHaveBeenCalled();
    expect(toastrService.success).toHaveBeenCalled();
  });

  it('should handle empty comment submission', () => {
    component.commentDetailsForm.patchValue({ comment: '   ' });
    component.onCommentSubmit();

    expect(toastrService.error).toHaveBeenCalledWith('Please enter a valid comment.', 'OOPS!');
    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
  });

  it('should handle file upload successfully', () => {
    const mockResponse = { message: 'Success' };
    projectSharingService.guestAddCraneRequestAttachment.mockReturnValue(of(mockResponse));

    // Mock file data with proper FileSystemFileEntry implementation
    const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });
    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile),
      relativePath: 'test.pdf',
      isFile: true,
      isDirectory: false,
      name: 'test.pdf'
    };

    component.fileData = [[{ fileEntry: mockFileEntry, relativePath: 'test.pdf' }]];
    component.uploadData();

    expect(projectSharingService.guestAddCraneRequestAttachment).toHaveBeenCalled();
    expect(toastrService.success).toHaveBeenCalledWith('Success', 'SUCCESS!');
  });

  it('should handle file upload error', () => {
    projectSharingService.guestAddCraneRequestAttachment.mockReturnValue(
      throwError(() => new Error('Upload failed'))
    );

    // Mock file data
    const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });
    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile),
      relativePath: 'test.pdf',
      isFile: true,
      isDirectory: false,
      name: 'test.pdf'
    };

    component.fileData = [[{ fileEntry: mockFileEntry, relativePath: 'test.pdf' }]];
    component.uploadData();

    expect(projectSharingService.guestAddCraneRequestAttachment).toHaveBeenCalled();
    expect(toastrService.error).toHaveBeenCalled();
  });

  it('should handle comment submission error', () => {
    projectSharingService.guestCreateCraneRequestComment.mockReturnValue(
      throwError(() => new Error('Comment failed'))
    );

    component.commentDetailsForm.patchValue({ comment: 'Test comment' });
    component.onCommentSubmit();

    expect(projectSharingService.guestCreateCraneRequestComment).toHaveBeenCalled();
    expect(toastrService.error).toHaveBeenCalled();
  });

  it('should validate file extensions', () => {
    expect(component.isValidExtension('pdf')).toBe(true);
    expect(component.isValidExtension('doc')).toBe(true);
    expect(component.isValidExtension('exe')).toBe(false);
  });

  it('should open modal', () => {
    const template = {} as any;
    component.openConfirmationModalPopupForDetailsNDR(template);
    expect(modalService.show).toHaveBeenCalled();
  });

  it('should reset form when action is "yes"', () => {
    component.modalRef1 = modalRef;
    component.bsModalRef = modalRef;
    component.resetForm('yes');
    expect(modalRef.hide).toHaveBeenCalledTimes(2);
  });

  it('should reset form when action is "no"', () => {
    component.modalRef1 = modalRef;
    component.resetForm('no');
    expect(modalRef.hide).toHaveBeenCalledTimes(1);
  });

  it('should handle keyboard navigation for file actions', () => {
    const mockEvent = {
      preventDefault: jest.fn(),
      key: 'Enter'
    };

    // Create a mock formData object
    component.formData = new FormData();

    // Mock the methods to avoid actual implementation
    jest.spyOn(component, 'removeFile').mockImplementation(() => {});
    jest.spyOn(component, 'openEditModal').mockImplementation(() => {});

    // Test 'remove' type
    component.handleDownKeydown(mockEvent as any, 0, 0, 'remove');
    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(component.removeFile).toHaveBeenCalledWith(0, 0);

    // Test 'open' type
    component.handleDownKeydown(mockEvent as any, 0, 0, 'open');
    expect(component.openEditModal).toHaveBeenCalledWith(0, 0);
  });

  it('should handle file drop events', () => {
    const mockFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile),
      name: 'test.pdf',
      isFile: true,
      isDirectory: false
    } as FileSystemFileEntry;

    const mockDropEvent: NgxFileDropEntry[] = [{
      relativePath: 'test.pdf',
      fileEntry: mockFileEntry
    }];

    component.dropped(mockDropEvent);
    expect(component.files).toEqual(mockDropEvent);
  });

  it('should call openConfirmationModalPopupForDetailsNDR when statusChanged and currentStatus are true', () => {
    const template = {} as any;
    component.statusChanged = true;
    component.currentStatus = 'active';
    jest.spyOn(component, 'openConfirmationModalPopupForDetailsNDR');

    component.confirmationClose(template);

    expect(component.openConfirmationModalPopupForDetailsNDR).toHaveBeenCalledTimes(2);
    expect(component.openConfirmationModalPopupForDetailsNDR).toHaveBeenCalledWith(template);
  });

  it('should call openConfirmationModalPopupForDetailsNDR once when statusChanged is false', () => {
    const template = {} as any;
    component.statusChanged = false;
    component.currentStatus = '';
    jest.spyOn(component, 'openConfirmationModalPopupForDetailsNDR');

    component.confirmationClose(template);

    expect(component.openConfirmationModalPopupForDetailsNDR).toHaveBeenCalledTimes(1);
    expect(component.openConfirmationModalPopupForDetailsNDR).toHaveBeenCalledWith(template);
  });

  it('should extract error message and display it', () => {
    const mockError = {
      message: {
        details: [{ field: 'Test error message' }]
      }
    };

    component.showError(mockError);

    expect(toastrService.error).toHaveBeenCalledWith('Test error message');
  });

  it('should handle error with multiple details', () => {
    const mockError = {
      message: {
        details: [{ field1: 'Error 1', field2: 'Error 2' }]
      }
    };

    component.showError(mockError);

    expect(toastrService.error).toHaveBeenCalledWith('Error 1');
  });

  it('should extract comment error message and reset form state', () => {
    const mockCommentError = {
      message: {
        details: [{ comment: 'Comment error message' }]
      }
    };
    component.submitted = true;
    component.formSubmitted = true;

    component.showCommentError(mockCommentError);

    expect(toastrService.error).toHaveBeenCalledWith('Comment error message');
    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
  });

  it('should handle comment error with array values', () => {
    const mockCommentError = {
      message: {
        details: [{ errors: ['First error', 'Second error'] }]
      }
    };

    component.showCommentError(mockCommentError);

    expect(toastrService.error).toHaveBeenCalledWith('First error');
  });

  it('should return true for empty comment', () => {
    const result = component.checkStringEmptyValues({ comment: '' });
    expect(result).toBe(true);
  });

  it('should return true for whitespace-only comment', () => {
    const result = component.checkStringEmptyValues({ comment: '   ' });
    expect(result).toBe(true);
  });

  it('should return false for valid comment', () => {
    const result = component.checkStringEmptyValues({ comment: 'Valid comment' });
    expect(result).toBe(false);
  });

  it('should return false for comment with leading/trailing spaces but valid content', () => {
    const result = component.checkStringEmptyValues({ comment: '  Valid comment  ' });
    expect(result).toBe(false);
  });

  it('should extract file extension correctly', () => {
    expect(component.getFileExtension('document.pdf')).toBe('pdf');
    expect(component.getFileExtension('image.JPG')).toBe('jpg');
    expect(component.getFileExtension('file.name.with.dots.doc')).toBe('doc');
  });

  it('should handle files without extension', () => {
    expect(component.getFileExtension('filename')).toBe('filename');
  });

  it('should handle empty filename', () => {
    expect(component.getFileExtension('')).toBe('');
  });

  it('should validate all supported extensions', () => {
    expect(component.isValidExtension('jpg')).toBe(true);
    expect(component.isValidExtension('jpeg')).toBe(true);
    expect(component.isValidExtension('png')).toBe(true);
    expect(component.isValidExtension('pdf')).toBe(true);
    expect(component.isValidExtension('doc')).toBe(true);
  });

  it('should reject unsupported extensions', () => {
    expect(component.isValidExtension('txt')).toBe(false);
    expect(component.isValidExtension('exe')).toBe(false);
    expect(component.isValidExtension('zip')).toBe(false);
    expect(component.isValidExtension('')).toBe(false);
  });

  it('should be case sensitive for file extensions', () => {
    expect(component.isValidExtension('PDF')).toBe(false);
    expect(component.isValidExtension('JPG')).toBe(false);
  });

  it('should handle fileOver event', () => {
    const mockEvent = { target: 'test' };
    expect(() => component.fileOver(mockEvent)).not.toThrow();
  });

  it('should handle fileLeave event', () => {
    const mockEvent = { target: 'test' };
    expect(() => component.fileLeave(mockEvent)).not.toThrow();
  });

  it('should open edit modal with correct parameters', () => {
    const mockItem = {
      ProjectId: 123,
      recurrence: {
        id: 1,
        recurrence: 'Weekly',
        recurrenceEndDate: '2024-12-31'
      }
    };
    const action = 2;

    component.CraneRequestId = 456;
    component.ParentCompanyId = 789;
    component.bsModalRef = modalRef;

    component.openEditModal(mockItem, action);

    expect(modalService.show).toHaveBeenCalled();
    expect(modalRef.hide).toHaveBeenCalled();
  });

  it('should open edit modal without recurrence', () => {
    const mockItem = {
      ProjectId: 123
    };
    const action = 1;

    component.CraneRequestId = 456;
    component.ParentCompanyId = 789;
    component.bsModalRef = null;

    component.openEditModal(mockItem, action);

    expect(modalService.show).toHaveBeenCalled();
  });

  it('should process file with valid size', () => {
    const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });
    Object.defineProperty(mockFile, 'size', { value: 1000000 }); // 1MB

    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile)
    } as FileSystemFileEntry;

    // Initialize fileData with proper structure
    component.fileData = [[{}]];

    const mockFileReader = {
      onload: jest.fn(),
      readAsDataURL: jest.fn(),
      result: 'data:image/jpeg;base64,test'
    };
    (window as any).FileReader = jest.fn(() => mockFileReader);

    component.processFile(mockFileEntry, 0, 0, 'jpg');

    // Simulate FileReader onload
    mockFileReader.onload();

    expect(component.fileData[0][0].extension).toBe('jpg');
    expect(mockFileReader.readAsDataURL).toHaveBeenCalledWith(mockFile);
  });

  it('should reject file with size > 2MB', () => {
    const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });
    Object.defineProperty(mockFile, 'size', { value: 3000000 }); // 3MB

    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile)
    } as FileSystemFileEntry;

    component.fileData = [[{}]];

    component.processFile(mockFileEntry, 0, 0, 'jpg');

    expect(toastrService.error).toHaveBeenCalledWith('Please choose a attachment less than or equal to 2MB');
    expect(component.fileData).toEqual([]);
  });

  it('should handle processFile when fileData group is removed', () => {
    const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });
    Object.defineProperty(mockFile, 'size', { value: 1000000 }); // 1MB

    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile)
    } as FileSystemFileEntry;

    // Start with empty fileData
    component.fileData = [];

    component.processFile(mockFileEntry, 0, 0, 'jpg');

    // Should not throw error when accessing undefined fileData[0]
    expect(component).toBeTruthy();
  });

  it('should initialize series options correctly', () => {
    component.initializeSeriesOption();

    expect(component.seriesOptions).toEqual([
      {
        option: 1,
        text: 'This event',
        disabled: false,
      },
      {
        option: 2,
        text: 'This and all following events',
        disabled: false,
      },
    ]);
  });

  it('should toggle allRequestIsOpened and handle future dates', () => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 1);

    const mockData = {
      craneDeliveryStart: futureDate.toISOString()
    };

    component.allRequestIsOpened = false;
    jest.spyOn(component, 'initializeSeriesOption');

    component.changeRequestCollapse(mockData);

    expect(component.initializeSeriesOption).toHaveBeenCalled();
    expect(component.allRequestIsOpened).toBe(true);
  });

  it('should disable series options for past dates', () => {
    const pastDate = new Date();
    pastDate.setDate(pastDate.getDate() - 1);

    const mockData = {
      craneDeliveryStart: pastDate.toISOString()
    };

    component.allRequestIsOpened = false;
    jest.spyOn(component, 'initializeSeriesOption');

    component.changeRequestCollapse(mockData);

    expect(component.initializeSeriesOption).toHaveBeenCalled();
    expect(component.allRequestIsOpened).toBe(true);
    expect(component.seriesOptions[1].disabled).toBe(true);
  });

  it('should handle dropped files with invalid extension', () => {
    const mockFile = new File(['test content'], 'test.exe', { type: 'application/exe' });
    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile),
      name: 'test.exe',
      isFile: true,
      isDirectory: false
    } as FileSystemFileEntry;

    const mockDropEvent: NgxFileDropEntry[] = [{
      relativePath: 'test.exe',
      fileEntry: mockFileEntry
    }];

    component.dropped(mockDropEvent);

    expect(toastrService.error).toHaveBeenCalledWith(
      'Please select a valid file. Supported file format (.jpg,.jpeg,.png,.pdf,.doc)',
      'OOPS!'
    );
  });

  it('should handle dropped files with valid extension and process them', () => {
    const mockFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile),
      name: 'test.pdf',
      isFile: true,
      isDirectory: false
    } as FileSystemFileEntry;

    const mockDropEvent: NgxFileDropEntry[] = [{
      relativePath: 'test.pdf',
      fileEntry: mockFileEntry
    }];

    jest.spyOn(component, 'processFile');

    component.dropped(mockDropEvent);

    expect(component.files).toEqual(mockDropEvent);
    expect(component.processFile).toHaveBeenCalledWith(mockFileEntry, 0, 0, 'pdf');
  });

  it('should handle removeFile correctly', () => {
    component.formData = new FormData();
    component.formData.append('test.pdf', new File([''], 'test.pdf'));
    component.fileData = [
      [{ relativePath: 'test.pdf' }, { relativePath: 'test2.pdf' }]
    ];

    jest.spyOn(component.formData, 'delete');
    jest.spyOn(component.fileData[0], 'splice');
    jest.spyOn(component.fileData, 'splice');

    component.removeFile(0, 0);

    expect(component.formData.delete).toHaveBeenCalledWith('test.pdf');
    expect(component.fileData[0].splice).toHaveBeenCalledWith(0);
    expect(component.fileData.splice).toHaveBeenCalledWith(0);
  });

  it('should handle comment submission with 400 status code error', () => {
    const mockError = {
      message: {
        statusCode: 400,
        details: [{ comment: 'Comment validation error' }]
      }
    };

    projectSharingService.guestCreateCraneRequestComment.mockReturnValue(
      throwError(() => mockError)
    );
    jest.spyOn(component, 'showCommentError');

    component.commentDetailsForm.patchValue({ comment: 'Test comment' });
    component.onCommentSubmit();

    expect(component.showCommentError).toHaveBeenCalledWith(mockError);
  });

  it('should handle upload with 400 status code error', () => {
    const mockError = {
      message: {
        statusCode: 400,
        details: [{ attachment: 'File validation error' }]
      }
    };

    projectSharingService.guestAddCraneRequestAttachment.mockReturnValue(
      throwError(() => mockError)
    );
    jest.spyOn(component, 'showError');

    const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });
    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile),
      relativePath: 'test.pdf',
      isFile: true,
      isDirectory: false,
      name: 'test.pdf'
    };

    component.fileData = [[{ fileEntry: mockFileEntry, relativePath: 'test.pdf' }]];
    component.uploadData();

    expect(component.showError).toHaveBeenCalledWith(mockError);
  });

  it('should handle ngOnInit without ProjectId and ParentCompanyId', () => {
    component.ProjectId = null;
    component.ParentCompanyId = null;
    component.data = { id: 123 };
    jest.spyOn(component, 'getNDR');

    component.ngOnInit();

    expect(component.CraneRequestId).toBe(123);
    expect(component.getNDR).not.toHaveBeenCalled();
  });

  it('should handle ngOnInit with ProjectId and ParentCompanyId', () => {
    component.ProjectId = 456;
    component.ParentCompanyId = 789;
    component.data = { id: 123 };
    jest.spyOn(component, 'getNDR');

    component.ngOnInit();

    expect(component.CraneRequestId).toBe(123);
    expect(component.getNDR).toHaveBeenCalled();
  });

  it('should handle getNDR without ProjectId and ParentCompanyId', () => {
    component.ProjectId = null;
    component.ParentCompanyId = null;

    component.getNDR();

    expect(projectSharingService.guestGetEquipmentCraneRequest).not.toHaveBeenCalled();
  });

  it('should filter history list to exclude comments', () => {
    const mockResponse = {
      data: {
        id: 1,
        attachments: [],
        comments: [],
        history: [
          { id: 1, type: 'comment', action: 'added' },
          { id: 2, type: 'status', action: 'changed' },
          { id: 3, type: 'attachment', action: 'uploaded' },
          { id: 4, type: 'comment', action: 'deleted' }
        ]
      }
    };

    component.ProjectId = 123;
    component.ParentCompanyId = 456;
    component.CraneRequestId = 1;

    projectSharingService.guestGetEquipmentCraneRequest.mockReturnValue(of(mockResponse));

    component.getNDR();

    expect(component.historyList).toEqual([
      { id: 2, type: 'status', action: 'changed' },
      { id: 3, type: 'attachment', action: 'uploaded' }
    ]);
  });

  it('should handle getResponsiblePeople with only firstName', () => {
    const person = { firstName: 'John' };
    expect(component.getResponsiblePeople(person)).toBe('UU');
  });

  it('should handle getResponsiblePeople with only lastName', () => {
    const person = { lastName: 'Doe' };
    expect(component.getResponsiblePeople(person)).toBe('UU');
  });

  it('should handle getResponsiblePeople with null object', () => {
    expect(component.getResponsiblePeople(null)).toBe('UU');
  });

  it('should handle getResponsiblePeople with undefined object', () => {
    expect(component.getResponsiblePeople(undefined)).toBe('UU');
  });

  it('should handle keyboard navigation with Space key', () => {
    const mockEvent = {
      preventDefault: jest.fn(),
      key: ' '
    };

    component.formData = new FormData();
    jest.spyOn(component, 'removeFile').mockImplementation(() => {});

    component.handleDownKeydown(mockEvent as any, 0, 0, 'remove');
    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(component.removeFile).toHaveBeenCalledWith(0, 0);
  });

  it('should handle keyboard navigation with unsupported key', () => {
    const mockEvent = {
      preventDefault: jest.fn(),
      key: 'Tab'
    };

    jest.spyOn(component, 'removeFile').mockImplementation(() => {});

    component.handleDownKeydown(mockEvent as any, 0, 0, 'remove');
    expect(mockEvent.preventDefault).not.toHaveBeenCalled();
    expect(component.removeFile).not.toHaveBeenCalled();
  });

  it('should handle keyboard navigation with default case', () => {
    const mockEvent = {
      preventDefault: jest.fn(),
      key: 'Enter'
    };

    component.handleDownKeydown(mockEvent as any, 0, 0, 'unknown');
    expect(mockEvent.preventDefault).toHaveBeenCalled();
  });

  it('should handle uploadData with successful mixpanel tracking', () => {
    const mockResponse = { message: 'Success' };
    const mixpanelService = TestBed.inject(MixpanelService) as jest.Mocked<MixpanelService>;

    projectSharingService.guestAddCraneRequestAttachment.mockReturnValue(of(mockResponse));
    jest.spyOn(component, 'getNDR').mockImplementation(() => {});

    const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });
    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile),
      relativePath: 'test.pdf',
      isFile: true,
      isDirectory: false,
      name: 'test.pdf'
    };

    component.fileData = [[{ fileEntry: mockFileEntry, relativePath: 'test.pdf' }]];
    component.uploadData();

    expect(mixpanelService.addGuestUserMixpanelEvents).toHaveBeenCalledWith('Attachment added against a Crane Booking');
    expect(component.getNDR).toHaveBeenCalled();
    expect(component.fileData).toEqual([]);
    expect(component.uploadSubmitted).toBe(false);
  });

  it('should handle comment submission with successful mixpanel tracking', () => {
    const mockResponse = { message: 'Success' };
    const mixpanelService = TestBed.inject(MixpanelService) as jest.Mocked<MixpanelService>;

    projectSharingService.guestCreateCraneRequestComment.mockReturnValue(of(mockResponse));
    jest.spyOn(component, 'getNDR').mockImplementation(() => {});

    component.commentDetailsForm.patchValue({ comment: 'Test comment' });
    component.onCommentSubmit();

    expect(mixpanelService.addGuestUserMixpanelEvents).toHaveBeenCalledWith('Comment added against a Crane Booking');
    expect(component.getNDR).toHaveBeenCalled();
    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
  });

  it('should handle dropped files with directory entry', () => {
    const mockFileEntry = {
      name: 'test-folder',
      isFile: false,
      isDirectory: true,
      file: jest.fn()
    } as unknown as FileSystemFileEntry;

    const mockDropEvent: NgxFileDropEntry[] = [{
      relativePath: 'test-folder',
      fileEntry: mockFileEntry
    }];

    jest.spyOn(component, 'processFile');

    component.dropped(mockDropEvent);

    expect(component.files).toEqual(mockDropEvent);
    expect(component.processFile).not.toHaveBeenCalled();
  });

  it('should handle resetForm with modalRef1 being null', () => {
    component.modalRef1 = null;
    component.bsModalRef = modalRef;

    component.resetForm('yes');

    expect(modalRef.hide).toHaveBeenCalledTimes(1);
  });

  it('should handle constructor with missing localStorage values', () => {
    jest.spyOn(Storage.prototype, 'getItem').mockImplementation((_key: string) => {
      return null; // Simulate missing localStorage values
    });

    // Create a new component instance to test constructor
    const newFixture = TestBed.createComponent(GuestCraneDetailsComponent);
    const newComponent = newFixture.componentInstance;

    expect(newComponent.ProjectId).toBeNaN();
    expect(newComponent.ParentCompanyId).toBeNaN();
    expect(newComponent.guestUserId).toBeNaN();
  });
});
