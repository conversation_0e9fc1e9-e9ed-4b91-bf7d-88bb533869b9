/* eslint-disable no-param-reassign */
import { Component, TemplateRef, OnInit } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Title } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { LocationSettingsService } from '../services/location-settings/location-settings.service';
import { MixpanelService } from '../services/mixpanel.service';

@Component({
  selector: 'app-location',
  templateUrl: './location.component.html',
  })
export class LocationComponent implements OnInit {
  public modalRef: BsModalRef;

  public loader = true;

  public ProjectId;

  public ParentCompanyId;

  public currentPageNo = 1;

  public pageSize = 25;

  public search = '';

  public sort = 'DESC';

  public sortColumn = 'id';

  public locationList = [];

  public totalCount = 0;

  public showSearchbar = false;

  public chosenLocationNotification: any = [];

  public submitted = false;

  public isOpened = true;

  public defaultLocationPath: any = {};

  public constructor(private readonly modalService: BsModalService,
    private readonly locationService: LocationSettingsService,
    private readonly toastr: ToastrService,
    private readonly mixpanelService: MixpanelService,
    private readonly titleService: Title) {
    this.titleService.setTitle('Follo - Location');
    this.ProjectId = localStorage.getItem('ProjectId');
    this.ParentCompanyId = localStorage.getItem('currentCompanyId');
    if (this.ProjectId && this.ParentCompanyId) {
      this.getLocationList();
    }
  }

  public searchLocation(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.search = data;
    this.currentPageNo = 1;
    this.getLocationList();
  }

  public ngOnInit(): void { /* */ }

  public openModal1(template: TemplateRef<any>): void {
    this.modalRef = this.modalService.show(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-sm filter-popup custom-modal',
    });
  }

  public openModalPopup(template): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md location-sent-popup modal-dialog-centered custom-modal',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public changePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.getLocationList();
  }

  public changePageNo(pageNo: number): void {
    this.currentPageNo = pageNo;
    this.getLocationList();
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.currentPageNo = 1;
    this.getLocationList();
  }

  public getLocationList(): void {
    this.loader = true;
    if (this.ProjectId && this.ParentCompanyId && this.pageSize && this.currentPageNo) {
      const queryParams = {
        ProjectId: this.ProjectId,
        pageSize: this.pageSize,
        pageNo: this.currentPageNo,
        ParentCompanyId: this.ParentCompanyId,
        search: this.search,
        sort: this.sort,
        sortByField: this.sortColumn,
      };
      this.locationService.getLocationList(queryParams).subscribe((res): void => {
        const responseData = res.data;
        this.locationList = responseData.rows;
        this.defaultLocationPath = res.data ? res.data.defaultLocation : {};
        this.totalCount = responseData.count;
        this.loader = false;
      });
    }
  }

  public submitLocationNotification(): void {
    this.submitted = true;
    const payload = {
      chosenMemberPreference: this.chosenLocationNotification,
    };
    this.locationService.updateMemberLocationPreference(payload).subscribe({
      next: (response): void => {
        if (response) {
          this.chosenLocationNotification = [];
          this.getLocationList();
          this.toastr.success(response.message, 'Success');
          this.mixpanelService.addMixpanelEvents('Updated Member Location Preference');
          this.submitted = false;
        }
      },
      error: (locationNotificationErr): void => {
        this.submitted = false;
        if (locationNotificationErr.message?.statusCode === 400) {
          this.showError(locationNotificationErr);
        } else if (!locationNotificationErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(locationNotificationErr.message, 'OOPS!');
        }
      },
    });
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.chosenLocationNotification = [];
    this.toastr.error(errorMessage);
  }

  public sortByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getLocationList();
  }

  public checkInstantValue(data, event, type): any {
    if (type === 'default') {
      if (this.defaultLocationPath) {
        this.chosenLocationNotification.push({
          id: this.defaultLocationPath.LocationNotificationPreferences[0].id,
          MemberId: this.defaultLocationPath.LocationNotificationPreferences[0].MemberId,
          ProjectId: this.defaultLocationPath.LocationNotificationPreferences[0].ProjectId,
          follow: event.target.checked,
        });
      }
      if (this.locationList.length > 0) {
        this.locationList.forEach((element): void => {
          this.chosenLocationNotification.push({
            id: element.LocationNotificationPreferences[0].id,
            MemberId: element.LocationNotificationPreferences[0].MemberId,
            ProjectId: element.LocationNotificationPreferences[0].ProjectId,
            follow: event.target.checked,
          });
          element.LocationNotificationPreferences[0].follow = event.target.checked;
          if (element && element.paths.length > 0) {
            element.paths.forEach((subCategory): void => {
              this.chosenLocationNotification.push({
                id: subCategory.LocationNotificationPreferences[0].id,
                MemberId: subCategory.LocationNotificationPreferences[0].MemberId,
                ProjectId: subCategory.LocationNotificationPreferences[0].ProjectId,
                follow: event.target.checked,
              });
              subCategory.LocationNotificationPreferences[0].follow = event.target.checked;
              if (subCategory.tier && subCategory.tier.length > 0) {
                subCategory.tier.forEach((tierObject): void => {
                  this.chosenLocationNotification.push({
                    id: tierObject.LocationNotificationPreferences[0].id,
                    MemberId: tierObject.LocationNotificationPreferences[0].MemberId,
                    ProjectId: tierObject.LocationNotificationPreferences[0].ProjectId,
                    follow: event.target.checked,
                  });
                  tierObject.LocationNotificationPreferences[0].follow = event.target.checked;
                });
              }
            });
          }
          return element;
        });
      }
    }
    if (type === 'mainCategory') {
      this.handleMainCategoryFlow(data, event);
    }
    if (type === 'subCategory') {
      this.handleSubCategoryFlow(data, event);
    }
    if (type === 'tier') {
      this.handleTierFlow(data, event);
    }
  }

  public handleMainCategoryFlow(data, event): any {
    if (data) {
      this.chosenLocationNotification.push({
        id: data.LocationNotificationPreferences[0].id,
        MemberId: data.LocationNotificationPreferences[0].MemberId,
        ProjectId: data.LocationNotificationPreferences[0].ProjectId,
        follow: event.target.checked,
      });
      data.LocationNotificationPreferences[0].follow = event.target.checked;
      if (data?.paths.length > 0) {
        data.paths.forEach((subCategory): void => {
          this.chosenLocationNotification.push({
            id: subCategory.LocationNotificationPreferences[0].id,
            MemberId: subCategory.LocationNotificationPreferences[0].MemberId,
            ProjectId: subCategory.LocationNotificationPreferences[0].ProjectId,
            follow: event.target.checked,
          });
          subCategory.LocationNotificationPreferences[0].follow = event.target.checked;
          if (subCategory.tier && subCategory.tier.length > 0) {
            subCategory.tier.forEach((tierObject): void => {
              this.chosenLocationNotification.push({
                id: tierObject.LocationNotificationPreferences[0].id,
                MemberId: tierObject.LocationNotificationPreferences[0].MemberId,
                ProjectId: tierObject.LocationNotificationPreferences[0].ProjectId,
                follow: event.target.checked,
              });
              tierObject.LocationNotificationPreferences[0].follow = event.target.checked;
            });
          }
        });
      }
    }
    return data;
  }

  public handleSubCategoryFlow(data, event): any {
    if (data) {
      this.chosenLocationNotification.push({
        id: data.LocationNotificationPreferences[0].id,
        MemberId: data.LocationNotificationPreferences[0].MemberId,
        ProjectId: data.LocationNotificationPreferences[0].ProjectId,
        follow: event.target.checked,
      });
      if (data.tier && data.tier.length > 0) {
        data.tier.forEach((tierObject): void => {
          this.chosenLocationNotification.push({
            id: tierObject.LocationNotificationPreferences[0].id,
            MemberId: tierObject.LocationNotificationPreferences[0].MemberId,
            ProjectId: tierObject.LocationNotificationPreferences[0].ProjectId,
            follow: event.target.checked,
          });
          tierObject.LocationNotificationPreferences[0].follow = event.target.checked;
        });
      }
    }
    return data;
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public handleTierFlow(data, event): any {
    if (event.target.checked) {
      this.chosenLocationNotification.push({
        id: data.LocationNotificationPreferences[0].id,
        MemberId: data.LocationNotificationPreferences[0].MemberId,
        ProjectId: data.LocationNotificationPreferences[0].ProjectId,
        follow: true,
      });
    }
    if (!event.target.checked) {
      this.chosenLocationNotification.push({
        id: data.LocationNotificationPreferences[0].id,
        MemberId: data.LocationNotificationPreferences[0].MemberId,
        ProjectId: data.LocationNotificationPreferences[0].ProjectId,
        follow: false,
      });
    }
    return data;
  }
}
