import {
  Component, Input, OnInit, TemplateRef,
} from '@angular/core';
import { BsModalService, BsModalRef, ModalOptions } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { ToastrService } from 'ngx-toastr';
import moment from 'moment';
import { DeliveryService } from '../../../../services/profile/delivery.service';
import { ProjectService } from '../../../../services/profile/project.service';
import { MixpanelService } from '../../../../services/mixpanel.service';
import { EditCraneBookingComponent } from '../edit-crane-booking/edit-crane-booking.component';
import { ProjectSharingService } from '../../../../services/projectSharingService/project-sharing.service';

@Component({
  selector: 'app-guest-crane-details',
  templateUrl: './guest-crane-details.component.html',
  })
export class GuestCraneDetailsComponent implements OnInit {
  @Input() data: any;

  @Input() title: string;

  public currentTabId: number = 0;

  public loader = true;

  public statusChanged: boolean = false;

  public currentStatus = '';

  public modalRef1: BsModalRef;

  public CraneRequestId: any;

  public ParentCompanyId: number;

  public ProjectId: number;

  public guestUserId: any;

  public NDRData: any = [];

  public todayDate: Date;

  public fileData: any = [];

  public formData: FormData;

  public uploadSubmitted = false;

  public fileArray: any = [];

  public commentList: any = [];

  public commentDetailsForm: UntypedFormGroup;

  public submitted = false;

  public formSubmitted = false;

  public historyList: any = [];

  public modalRef: BsModalRef;

  public currentDeliverySaveItem: any = {};

  public seriesOptions = [];

  public allRequestIsOpened = false;

  public files: NgxFileDropEntry[] = [];


  public constructor(
    private readonly modalService: BsModalService,
    public bsModalRef: BsModalRef,
    private readonly deliveryService: DeliveryService,
    public projectService: ProjectService,
    public projectSharingService: ProjectSharingService,
    private readonly toastr: ToastrService,
    private readonly mixpanelService: MixpanelService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly option: ModalOptions,
  ) {
    this.todayDate = new Date();
    this.formData = new FormData();
    this.ProjectId = +window.atob(localStorage.getItem('guestProjectId'));
    this.ParentCompanyId = +window.atob(localStorage.getItem('guestParentCompanyId'));
    this.guestUserId = +window.atob(localStorage.getItem('guestId'));
    this.commentForm();
  }

  public ngOnInit(): void {
    const stateValue = this.data;
    this.CraneRequestId = stateValue.id;
    if (this.ProjectId && this.ParentCompanyId) {
      this.getNDR();
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'remove':
          this.removeFile(data, item);
          break;
        case 'open':
          this.openEditModal(data, item);
          break;
        default:
          break;
      }
    }
  }

  public getNDR(): void {
    const param = {
      CraneRequestId: this.CraneRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    if (this.ProjectId && this.ParentCompanyId) {
      this.projectSharingService.guestGetEquipmentCraneRequest(param).subscribe((res): void => {
        this.NDRData = res.data;
        this.currentDeliverySaveItem = res.data;
        this.commentList = this.NDRData.comments;
        this.fileArray = this.NDRData.attachments;
        this.historyList = this.NDRData.history;
        this.historyList = this.historyList.filter((data) => data.type !== 'comment');
        this.loader = false;
      });
    }
  }

  public getResponsiblePeople(object): any {
    if (object?.firstName && object?.lastName) {
      const string = `${object.firstName} ${object.lastName}`;
      const matches = string.match(/\b(\w)/g);
      const acronym = matches.join('').toUpperCase();
      return acronym;
    }
    return 'UU';
  }

  public confirmationClose(template: TemplateRef<any>): void {
    if (this.statusChanged && this.currentStatus) {
      this.openConfirmationModalPopupForDetailsNDR(template);
    }
    this.openConfirmationModalPopupForDetailsNDR(template);
  }

  public openConfirmationModalPopupForDetailsNDR(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.bsModalRef.hide();
      this.statusChanged = false;
    }
  }

  public removeFile(firstIndex: string | number, i: string | number): void {
    this.formData.delete(this.fileData[firstIndex][i].relativePath);
    this.fileData[firstIndex].splice(i);
    this.fileData.splice(firstIndex);
  }

  public uploadData(): void {
    this.uploadSubmitted = true;
    this.formData = new FormData();
    this.fileData.forEach((element: any[], i: any): void => {
      element.forEach(
        (data: { fileEntry: FileSystemFileEntry; relativePath: any }, index: any): void => {
          const { fileEntry } = data;
          fileEntry.file((_file: File): void => {
            this.formData.append('attachement', _file, data.relativePath);
          });
        },
      );
    });
    const params = {
      CraneRequestId: this.CraneRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
      userId: this.guestUserId,
    };
    this.projectSharingService.guestAddCraneRequestAttachment(params, this.formData).subscribe({
      next: (res): void => {
        this.fileData = [];
        this.uploadSubmitted = false;
        this.toastr.success(res.message, 'SUCCESS!');
        this.mixpanelService.addGuestUserMixpanelEvents('Attachment added against a Crane Booking');
        this.getNDR();
      },
      error: (attachmentErr): void => {
        this.uploadSubmitted = false;
        if (attachmentErr.message?.statusCode === 400) {
          this.showError(attachmentErr);
        } else {
          this.toastr.error(attachmentErr.message, 'OOPS!');
        }
      },
    });
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }

  public commentForm(): void {
    this.commentDetailsForm = this.formBuilder.group({
      comment: ['', Validators.compose([Validators.required])],
    });
  }

  public onCommentSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    if (this.commentDetailsForm.invalid) {
      this.formSubmitted = false;

      return;
    }
    const formValue = this.commentDetailsForm.value;
    if (!this.checkStringEmptyValues(formValue)) {
      const payload = {
        comment: formValue.comment.trim(),
        CraneRequestId: this.CraneRequestId,
        ParentCompanyId: this.ParentCompanyId,
        ProjectId: this.ProjectId,
        userId: this.guestUserId,
      };
      this.projectSharingService.guestCreateCraneRequestComment(payload).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addGuestUserMixpanelEvents('Comment added against a Crane Booking');
            this.submitted = false;
            this.formSubmitted = false;
            this.commentDetailsForm.reset();
            this.getNDR();
          }
        },
        error: (commentError): void => {
          this.commentDetailsForm.reset();
          if (commentError.message?.statusCode === 400) {
            this.showCommentError(commentError);
          } else {
            this.toastr.error(commentError.message, 'OOPS!');
            this.submitted = false;
          }
        },
      });
    } else {
      this.toastr.error('Please enter a valid comment.', 'OOPS!');
      this.submitted = false;
      this.formSubmitted = false;
    }
  }

  public checkStringEmptyValues(formValue: { comment: string }): boolean {
    if (formValue.comment.trim() === '') {
      return true;
    }
    return false;
  }

  public showCommentError(commentError: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let commentErrorMessage: any = '';
    commentErrorMessage = Object.values(commentError.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(commentErrorMessage);
  }

  public openEditModal(item, action): void {
    if (this.bsModalRef) {
      this.bsModalRef.hide();
    }
    const newPayload = {
      id: this.CraneRequestId,
      ProjectId: item.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      seriesOption: item?.recurrence && item?.recurrence?.recurrence !== 'Does Not Repeat' ? action : 1,
      recurrenceId: item?.recurrence ? item?.recurrence?.id : null,
      recurrenceEndDate: item?.recurrence
        ? item?.recurrence?.recurrenceEndDate
        : null,
    };
    const initialState = {
      data: newPayload,
      title: 'Modal with component',
    };
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(EditCraneBookingComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
      initialState,
    });
    this.modalRef.content.closeBtnName = 'Close';
    this.modalRef.content.seriesOption = item?.recurrence && item?.recurrence?.recurrence !== 'Does Not Repeat' ? action : 1;
    this.modalRef.content.recurrenceId = item?.recurrence ? item?.recurrence?.id : null;
    this.modalRef.content.recurrenceEndDate = item?.recurrence
      ? item?.recurrence?.recurrenceEndDate
      : null;
  }

  public dropped(files: NgxFileDropEntry[]): void {
    this.files = files;
    this.fileData.push(this.files);

    this.fileData.forEach((entryGroup: any[], i: number) => {
      entryGroup.forEach(
        (entry: { relativePath: string; fileEntry: FileSystemFileEntry }, index: number) => {
          const extension = this.getFileExtension(entry.relativePath);

          if (!this.isValidExtension(extension)) {
            this.toastr.error(
              'Please select a valid file. Supported file format (.jpg,.jpeg,.png,.pdf,.doc)',
              'OOPS!',
            );
            this.fileData.splice(i, 1);
            return;
          }

          if (entry.fileEntry.isFile) {
            this.processFile(entry.fileEntry, i, index, extension);
          }
        },
      );
    });
  }

  public getFileExtension(path: string): string {
    const parts = path.split('.');
    return parts[parts.length - 1].toLowerCase();
  }

  public isValidExtension(ext: string): boolean {
    return ['jpg', 'jpeg', 'png', 'pdf', 'doc'].includes(ext);
  }

  public processFile(
    fileEntry: FileSystemFileEntry,
    groupIndex: number,
    fileIndex: number,
    extension: string,
  ): void {
    fileEntry.file((file: File) => {
      const filesizeMB = +((file.size / 1_000_000).toFixed(4));

      if (filesizeMB > 2) {
        this.toastr.error('Please choose a attachment less than or equal to 2MB');
        this.fileData.splice(groupIndex, 1);
        return;
      }

      const reader = new FileReader();
      reader.onload = () => {
        if (this.fileData[groupIndex]) {
          this.fileData[groupIndex][fileIndex].image = reader.result;
        }
      };

      if (this.fileData[groupIndex]) {
        this.fileData[groupIndex][fileIndex].extension = extension;
        reader.readAsDataURL(file);
      }
    });
  }


  public fileOver(_event: any): void { /* */ }

  public fileLeave(_event: any): void { /* */ }

  public changeRequestCollapse(data): void {
    this.initializeSeriesOption();
    if (!moment(data.craneDeliveryStart).isAfter(moment())) {
      this.seriesOptions = this.seriesOptions.filter((object): any => {
        const seriesObject = object;
        if (seriesObject.option !== 1) {
          seriesObject.disabled = true;
        }
        return seriesObject;
      });
    }
    this.allRequestIsOpened = !this.allRequestIsOpened;
  }

  public initializeSeriesOption(): void {
    this.seriesOptions = [
      {
        option: 1,
        text: 'This event',
        disabled: false,
      },
      {
        option: 2,
        text: 'This and all following events',
        disabled: false,
      },
    ];
  }
}
