import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MembersSettingsComponent } from './members-settings.component';
import { ProjectService } from '../../services/profile/project.service';
import { BehaviorSubject } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('MembersSettingsComponent', () => {
  let component: MembersSettingsComponent;
  let fixture: ComponentFixture<MembersSettingsComponent>;
  let projectService: jest.Mocked<ProjectService>;

  const mockUserData = new BehaviorSubject<any>(null);

  beforeEach(async () => {
    const projectServiceSpy = {
      userData: mockUserData
    } as jest.Mocked<ProjectService>;

    await TestBed.configureTestingModule({
      declarations: [ MembersSettingsComponent ],
      providers: [
        { provide: ProjectService, useValue: projectServiceSpy }
      ],
      schemas: [NO_ERRORS_SCHEMA] // To ignore child components
    })
    .compileComponents();

    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(MembersSettingsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.userData).toEqual({});
    expect(component.isAdmin).toBe(false);
  });

  it('should set isAdmin to true when user has RoleId 1', () => {
    mockUserData.next({ RoleId: 1 });
    fixture.detectChanges();
    expect(component.isAdmin).toBe(true);
  });

  it('should set isAdmin to true when user has RoleId 2', () => {
    mockUserData.next({ RoleId: 2 });
    fixture.detectChanges();
    expect(component.isAdmin).toBe(true);
  });

  it('should set isAdmin to false when user has RoleId other than 1 or 2', () => {
    mockUserData.next({ RoleId: 3 });
    fixture.detectChanges();
    expect(component.isAdmin).toBe(false);
  });

  it('should handle undefined user data', () => {
    mockUserData.next(undefined);
    fixture.detectChanges();
    expect(component.isAdmin).toBe(false);
  });

  it('should handle null user data', () => {
    mockUserData.next(null);
    fixture.detectChanges();
    expect(component.isAdmin).toBe(false);
  });

  it('should handle empty string user data', () => {
    mockUserData.next('');
    fixture.detectChanges();
    expect(component.isAdmin).toBe(false);
  });

  it('should update userData when ProjectService emits new data', () => {
    const mockData = { RoleId: 1, name: 'Test User' };
    mockUserData.next(mockData);
    fixture.detectChanges();
    expect(component.userData).toEqual(mockData);
  });
});
