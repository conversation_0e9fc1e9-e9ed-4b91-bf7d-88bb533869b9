import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { ProjectService } from '../services/profile/project.service';
import { ProfileService } from '../services/profile/profile.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { AuthService } from '../services/auth/auth.service';
import { countryCodes } from '../services/countryCodes';
import { countryNames } from '../services/countryNames';

@Component({
  selector: 'app-overview',
  templateUrl: './overview.component.html',
  })
export class OverviewComponent implements OnInit {
  public overViewDetailsForm: UntypedFormGroup;

  public ProjectId: any;

  public submitted = false;

  public formSubmitted = false;

  public userData: any = {};

  public countryCode = [];

  public phoneMask = '00000-00000';

  public ParentCompanyId: any;

  public loader = false;

  public constructor(
    public projectService: ProjectService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly toastr: ToastrService,
    private readonly profileService: ProfileService,
    private readonly authService: AuthService,
    public deliveryService: DeliveryService,
  ) {
    this.getOverView();
    this.projectService.projectParent.subscribe((response13): void => {
      if (response13 !== undefined && response13 !== null && response13 !== '') {
        this.ProjectId = response13.ProjectId;
        this.ParentCompanyId = response13.ParentCompanyId;
        this.getOverView();
      }
    });
    this.profileService.refreshData.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        if (res === true) {
          this.getOverView();
        }
      }
    });
    this.overViewForm();
    this.getCountryCode();
  }

  public ngOnInit(): void { /* */ }


  public numberOnly(event: { which: any; keyCode: any }): boolean {
    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public changeMask(value: any): string {
    this.phoneMask = this.authService.checkDialCode(value);
    return this.phoneMask;
  }

  public alphaOnly(event: { keyCode: any }): boolean {
    const key = event.keyCode;
    return ((key >= 65 && key <= 90) || (key >= 97 && key <= 128) || key === 8);
  }

  public companyNameCheck(event: { keyCode: any }): boolean {
    const key = event.keyCode;
    return ((key >= 65 && key <= 90) || (key >= 97 && key <= 128) || key === 8 || key === 32
    || (key >= 48 && key <= 57) || (key > 31 && (key < 48 || key > 57)));
  }

  public alphaNum(event: { which: any; keyCode: any }): boolean {
    const key = (event.which) ? event.which : event.keyCode;
    const firstCondition = (key >= 65 && key <= 90);
    const secondCondition = (key >= 97 && key <= 128) || (key > 31 && (key < 48 || key > 57));
    if (key === 32) {
      return true;
    }
    if ((key > 31 && (key < 48 || key > 57)) && !firstCondition && !secondCondition) {
      return false;
    }

    return true;
  }

  public getOverView(): void {
    this.loader = true;
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    if (this.ProjectId && this.ParentCompanyId) {
      this.profileService.getOverView(param).subscribe((response: any): void => {
        if (response) {
          this.userData = response.data;
          this.loader = false;
          this.profileService.updatedOverView(this.userData);
          if (!this.userData.Company) {
            this.userData.Company = this.userData.ParentCompany?.Company[0];
          }
          this.overViewDetailsForm.get('CompanyId').setValue(this.userData.Company?.id);
          this.overViewDetailsForm.get('firstName').setValue(this.userData.User?.firstName);
          this.overViewDetailsForm.get('lastName').setValue(this.userData.User?.lastName);
          this.overViewDetailsForm.get('email').setValue(this.userData.User?.email);
          this.overViewDetailsForm.get('phoneNumber').setValue(this.userData.User?.phoneNumber);
          if (!this.userData.User?.phoneCode) {
            this.userData.User.phoneCode = '+1';
          }
          this.overViewDetailsForm.get('phoneCode').setValue(this.userData.User?.phoneCode);
          this.overViewDetailsForm.get('address').setValue(this.userData.Company?.address);
          this.overViewDetailsForm.get('secondAddress').setValue(this.userData.Company?.secondAddress);
          this.overViewDetailsForm.get('state').setValue(this.userData.Company?.state);
          this.overViewDetailsForm.get('city').setValue(this.userData.Company?.city);
          this.overViewDetailsForm.get('country').setValue(this.userData.Company?.country);
          this.overViewDetailsForm.get('zipCode').setValue(this.userData.Company?.zipCode);
          this.overViewDetailsForm.get('website').setValue(this.userData.Company?.website);
          this.overViewDetailsForm.get('companyName').setValue(this.userData.Company?.companyName);
        }
      });
    }
  }

  public overViewForm(): void {
    this.overViewDetailsForm = this.formBuilder.group(
      {
        CompanyId: [
          '', Validators.compose([Validators.required]),
        ],
        firstName: [
          '', Validators.compose([Validators.required]),
        ],
        lastName: ['', Validators.compose([Validators.required])],
        email: ['', Validators.compose([Validators.required]),
        ],
        phoneNumber: [
          '',
          Validators.compose([
            Validators.required,
          ]),
        ],
        phoneCode: [
          '',
          Validators.compose([
            Validators.required,
          ]),
        ],
        address: [
          '',
          // Validators.compose([
          // Validators.required,
          // ]),
        ],
        secondAddress: [
          '',
        ],
        state: [
          '',
          // Validators.compose([
          // Validators.required,
          // ]),
        ],
        city: [
          '',
          // Validators.compose([
          // Validators.required,
          // ]),
        ],
        zipCode: [
          '',
          // Validators.compose([
          // Validators.required,
          // ]),
        ],
        country: [
          '',
          // Validators.compose([
          // Validators.required,
          // ]),
        ],
        website: [
          '',
          // Validators.compose([
          // Validators.required,
          // Validators.pattern(reg),
          // ]),
        ],
        companyName: [
          '',
          Validators.compose([
            Validators.required,
          ]),
        ],
      },
    );
  }


  public getAssignPlace(
    overviewStreetNumber?: string,
    overviewPlace?: string,
    overviewSubLocalityLevel2?: string,
    overviewSubLocality?: string,
  ): string {
    const parts = [
      overviewStreetNumber,
      overviewPlace,
      overviewSubLocalityLevel2,
      overviewSubLocality,
    ].filter(Boolean);

    return parts.join(' ');
  }

  // eslint-disable-next-line max-lines-per-function
  public handleOverviewCompanyAddressChange(address: { address_components: any }): void {
    let overviewPlace: any; let overviewStreetNumber: any;
    let overviewLocalityValue: any; let overviewSubLocality: any;
    let overviewCity: any; let overviewState: any; let overviewCountry: any;
    let overviewZipCode: any; let overviewZipCodeSuffix: any;
    let overviewResultedAddress = []; let overviewSubLocalityLevel2: any;
    overviewResultedAddress = address?.address_components;
    if (overviewResultedAddress.length > 0) {
      overviewResultedAddress.forEach((value): void => {
        let types = [];
        types = value.types;
        types.forEach((typeValue, i): void => {
          if (typeValue.indexOf('sublocality_level_1') > -1) {
            overviewSubLocality = value?.long_name;
          }
          if (typeValue.indexOf('street_number') > -1) {
            overviewStreetNumber = value?.long_name;
          }
          if (typeValue.indexOf('sublocality_level_2') > -1) {
            overviewSubLocalityLevel2 = value?.long_name;
          }
          if (typeValue.indexOf('route') > -1) {
            overviewPlace = value?.long_name;
          }
          if (typeValue.indexOf('administrative_area_level_2') > -1) {
            overviewCity = value?.long_name;
          }
          if (typeValue.indexOf('locality') > -1) {
            overviewLocalityValue = value?.long_name;
          }
          if (typeValue.indexOf('administrative_area_level_1') > -1) {
            overviewState = value?.long_name;
          }
          if (typeValue.indexOf('country') > -1) {
            overviewCountry = value?.long_name;
          }
          if (typeValue === 'postal_code') {
            overviewZipCode = value?.long_name;
          }
          if (typeValue.indexOf('postal_code_suffix') > -1) {
            overviewZipCodeSuffix = value?.long_name;
          }
        });
      });
    }
    const assignPlace = this.getAssignPlace(
      overviewStreetNumber, overviewPlace, overviewSubLocalityLevel2, overviewSubLocality,
    );

    if (!assignPlace) {
      this.overViewDetailsForm.get('address').setValue(overviewSubLocality);
    } else {
      this.overViewDetailsForm.get('address').setValue(assignPlace);
    }
    this.overViewDetailsForm.get('country').setValue(overviewCountry);
    this.overViewDetailsForm.get('state').setValue(overviewState);
    if (overviewLocalityValue) {
      this.overViewDetailsForm.get('city').setValue(overviewLocalityValue);
    } else {
      this.overViewDetailsForm.get('city').setValue(overviewCity);
    }
    if (overviewZipCode) {
      this.overViewDetailsForm.get('zipCode').setValue(overviewZipCode);
    } else {
      this.overViewDetailsForm.get('zipCode').setValue(overviewZipCodeSuffix);
    }
  }

  public getCountryCode(): void {
    Object.keys(countryCodes).forEach((key): void => {
      const countryName = countryNames[key];
      this.countryCode.push({ countryDialCode: key, name: countryName });
    });
    this.countryCode.sort((a, b): number => (a.name > b.name ? 1 : -1));
  }

  public checkStringEmptyValues(formValue: { firstName: string; companyName: string; address: string; lastName: string }): boolean {
    if (formValue.firstName.trim() === '') {
      return true;
    }
    if (formValue.companyName.trim() === '') {
      return true;
    }
    if (formValue.firstName.trim() === '') {
      return true;
    }
    if (formValue.lastName) {
      if (formValue.lastName.trim() === '') {
        return true;
      }
    }
    return false;
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    if (this.overViewDetailsForm.invalid) {
      this.formSubmitted = false;
      return;
    }
    const formValue = this.overViewDetailsForm.value;
    if (!this.checkStringEmptyValues(formValue)) {
      let lastName: any;
      if (formValue.lastName) {
        lastName = formValue.lastName.trim();
      }
      const payload = {
        companyName: formValue.companyName.trim(),
        website: formValue?.website,
        firstName: formValue.firstName.trim(),
        lastName,
        phoneNumber: formValue.phoneNumber,
        phoneCode: formValue.phoneCode,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
        CompanyId: formValue.CompanyId,
      };
      this.profileService.updateProfile(payload).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.submitted = false;
            this.formSubmitted = false;
            this.getOverView();
            this.overViewDetailsForm.reset();
            this.overViewDetailsForm.reset();
          }
        },
        error: (updateProfileErr): void => {
          this.submitted = false;
          this.formSubmitted = false;
          if (updateProfileErr.message?.statusCode === 400) {
            this.showError(updateProfileErr);
          } else if (!updateProfileErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(updateProfileErr.message, 'OOPS!');
          }
        },
      });
    } else {
      this.toastr.error('Please enter valid comment.', 'OOPS!');
      this.submitted = false;
      this.formSubmitted = false;
    }
  }

  public showError(err: { message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] } }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }
}
