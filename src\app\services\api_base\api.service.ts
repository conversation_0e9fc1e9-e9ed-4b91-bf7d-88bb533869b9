import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import moment from 'moment';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
  })
export class ApiService {
  public header;

  public constructor(private readonly http: HttpClient) {
    // constructor
    this.header = new HttpHeaders({
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache',
      Pragma: 'no-cache',
      'Access-Control-Allow-Origin': '*',
      // eslint-disable-next-line quote-props
      timezoneoffset: `${moment().utcOffset()}`,
      timezoneoffsetInHours: `${moment(moment().utcOffset()).format('hh:mm')}`,
    });
  }

  public setauthorization(): void {
    const currentToken = localStorage.getItem('token');
    if (currentToken !== null || currentToken !== undefined) {
      this.header = this.header.set('Authorization', `JWT ${currentToken}`);
    } else {
      this.header = this.header.set('Authorization', '');
    }
  }

  public getByHeadersFile(path: string, body: any): Observable<any> {
    this.setauthorization();
    return this.http
      .post(`${environment.apiBaseUrl}${path}`, body, {
        headers: this.header,
        responseType: 'blob',
      })
      .pipe(catchError(this.formatErrors));
  }

  public get(path: string, params: HttpParams = new HttpParams()): Observable<any> {
    this.setauthorization();
    return this.http
      .get(`${environment.apiBaseUrl}${path}`, { params, headers: this.header })
      .pipe(catchError(this.formatErrors));
  }

  public postMethod(
    path: string,
    body: any,
    params: HttpParams = new HttpParams(),
  ): Observable<any> {
    this.setauthorization();
    return this.http
      .post(`${environment.apiBaseUrl}${path}`, body, { params, headers: this.header })
      .pipe(catchError(this.formatErrors));
  }

  public putMethod(
    path: string,
    body: any,
    params: HttpParams = new HttpParams(),
  ): Observable<any> {
    this.setauthorization();
    return this.http
      .put(`${environment.apiBaseUrl}${path}`, body, { params, headers: this.header })
      .pipe(catchError(this.formatErrors));
  }

  public post(path: string, body: any): Observable<any> {
    this.setauthorization();
    return this.http
      .post(`${environment.apiBaseUrl}${path}`, body, { headers: this.header })
      .pipe(catchError(this.formatErrors));
  }

  public requestImage(path, data): Observable<any> {
    this.setauthorization();
    const currentToken = localStorage.getItem('token');
    const header = {
      headers: new HttpHeaders()
        .set('Authorization', `JWT ${currentToken}`)
        .set('timezoneoffset', `${moment().utcOffset()}`),
    };
    return this.http
      .post(`${environment.apiBaseUrl}${path}`, data, header)
      .pipe(catchError(this.formatErrors));
  }

  public getExcel(path): Observable<any> {
    this.setauthorization();
    return this.http
      .get(`${environment.apiBaseUrl}${path}`, { headers: this.header, responseType: 'blob' })
      .pipe(catchError(this.formatErrors));
  }

  public getExcel1(path: string, body: any): Observable<any> {
    this.setauthorization();
    return body.exportType === 'EXCEL'
      ? this.http
        .post(`${environment.apiBaseUrl}${path}`, body, {
          headers: this.header,
          responseType: 'blob',
        })
        .pipe(catchError(this.formatErrors))
      : this.http
        .post(`${environment.apiBaseUrl}${path}`, body, {
          headers: this.header,
        })
        .pipe(catchError(this.formatErrors));
  }

  public put(path: string, body: any): Observable<any> {
    this.setauthorization();
    return this.http
      .put(`${environment.apiBaseUrl}${path}`, body, { headers: this.header })
      .pipe(catchError(this.formatErrors));
  }

  public delete(path, body: any): Observable<any> {
    return this.http
      .delete(`${environment.apiBaseUrl}${path}`, body)
      .pipe(catchError(this.formatErrors));
  }

  public deleteMethod(path: string, params: HttpParams = new HttpParams()): Observable<any> {
    this.setauthorization();
    return this.http
      .delete(`${environment.apiBaseUrl}${path}`, { params, headers: this.header })
      .pipe(catchError(this.formatErrors));
  }

  public bulkUploadLocation(path: string,
    data: any, params: HttpParams = new HttpParams()): Observable<any> {
    this.setauthorization();
    const currentToken = localStorage.getItem('token');
    const headers = new HttpHeaders()
      .set('Authorization', `JWT ${currentToken}`)
      .set('timezoneoffset', `${moment().utcOffset()}`);
    return this.http
      .post(`${environment.apiBaseUrl}${path}`, data, { params, headers })
      .pipe(catchError(this.formatErrors));
  }

  private formatErrors(error: any): any {
    return throwError(() => error.error);
  }
}
