.form-control:focus,
*:focus {
  outline: 0;
  box-shadow: none;
}
.form-select:focus {
  box-shadow: none;
}
.auth-content {
  .form-control {
    @extend .color-grey4;
    @extend .pl0;
    @extend .fw500;

    &::placeholder {
      @extend .color-grey4;
      @extend .fs13;

      font-weight: 600;
    }
  }

  .form-control,
  .input-group-text {
    background-color: transparent;
    border-width: 0 0 1px;
    border-radius: 0;
    border-color: $grey41;

    @extend .color-grey4;
  }

  .form-control.is-invalid {
    background: none;
  }

  .input-group-text {
    border-left: none !important;
  }

  .form-icon {
    margin-left: -3px;
  }

  .forgot-link {
    position: relative;
    text-decoration: none;
    @extend .color-dark-black;

    &::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 1px;
      opacity: 0.15;
      background-color: $orange;
      left: 0;
      bottom: 0;
    }
  }

  .form-check {
    .form-check-input:checked ~ .form-check-label {
      &::before {
        background-color: $orange;
        border-color: $orange;
      }
    }

    .form-check-label {
      &::before {
        border: 1px solid rgba(112, 112, 112, 1);
        background-color: transparent;
      }

      &::after,
      &::before {
        border-radius: 3px;
        top: 1.5px;
        left: -21px;
      }
    }
  }
}

.input-group {
  .input-group-text {
    background-color: $white;
    border-width: 0 0 1px;
    border-radius: 0px;
    border-color: $grey11;
  }
}

.custom-input-form {
  .custom-select,
  input.form-control {
    height: 44px;
  }
}

.custom-select {
  background: url('../../assets/images/down-arrow-grey.png') no-repeat right 0.75rem center/10px 9px;
  &.no-arrow {
    background: none !important;
  }
}

.underline {
  background-color: $orange;
  display: inline-block;
  height: 0.5px;
  left: 0;
  margin-top: -5px;
  position: absolute;
  top: 40px;
  -webkit-transform: scale(0, 1);
  transform: scale(0, 1);
  -webkit-transition: all 0.5s linear;
  transition: all 0.5s linear;
  width: 100%;
  z-index: 99999;
}

.form-control:focus + .underline {
  transform: scale(1);
}


/* Material Input */
.custom-material-form {
  .tag-wrapper {
    font-family: 'Cairo', sans-serif;
  }
  label {
    font-size: 12px !important;
  }
  .form-group {
    margin-bottom: 1rem;
  }
  .material-input {
    border-width: 0 0 1px;
    border-color: $grey11;
    border-radius: 0;
    padding: 5px !important;
    font-size: 12px !important;

    &.form-control {
      height: $value30;
      font-size: 12px !important;
      &::placeholder {
        color: $grey11;
      }
    }
    &.project-detail-label {
      font-size: 12px !important;
    }
    &[type='text']:disabled {
      border-width: 0;
      height: 25px;
    }
  }

  textarea {
    resize: none;
    border-radius: 0;
    font-size: 12px !important;
  }
  textarea.form-control {
    min-height: 65px;
  }
  select {
    &.material-input {
      -moz-appearance: none;
      -webkit-appearance: none;
      appearance: none;
      background: url('../../assets/images/down-chevron.svg') no-repeat right 0.5rem center/13px
        12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      padding-right: 30px;
      &.pb4 {
        padding-bottom: 4px !important;
      }
    }
  }

  .input-pl-10-overview {
    .form-control {
      height: 28px !important;
    }
  }

  .tag-layout {
    .ng2-tag-input {
      .ng2-tags-container {
        tag-input-form {
          width: 100% !important;
          height: 8vh;
          overflow-y: hidden;
          .ng2-tag-input__text-input {
            width: 100% !important;
            font-family: 'Cairo', sans-serif !important;
            font-size: 12px !important;
            div {
              width: 100%;
            }
            &::placeholder {
              font-family: 'Cairo', sans-serif !important;
            }
          }
        }
      }
    }
  }

  &.add-concrete-material-form {
    .form-control {
      border-color: $light-grey8;
    }
    .tag-wrapper {
      .tag__text {
        width: auto;
      }
      delete-icon {
        text-align: center;
        svg {
          width: 10px;
          height: 32px;
          &:hover {
            width: 7px;
          }
        }
      }
    }
    .taginput-height {
      .tag-layout {
        height: 10vh;
        overflow-y: auto;
        border-color: $light-grey8;
        &.newdelivery-taglayout {
          height: 13vh;
        }
      }
      .ng2-tag-input {
        border-bottom: 0px;
      }
      &.location-tag-height {
        delete-icon {
          width: 10px;
        }
        .tag-wrapper {
          line-height: 30px;
        }
      }
    }
    .customised-tag-input {
      .tag-layout {
        border: 0px;
        border-bottom: 1px solid $light-grey8;
        height: 40px;
        overflow: hidden;
        .tag-wrapper {
          .tag__text {
            width: 200px;
          }
        }
        .ng2-tag-input {
          .ng2-tags-container {
            flex-wrap: nowrap;
          }
        }
        .ng2-tag-input__text-input {
          padding: 0px;
        }
      }
    }
    .multiselect-dropdown {
      margin-top: 6px;
      .dropdown-btn {
        .dropdown-multiselect__caret {
          &::before {
            border-width: 6px 6px 0 !important;
            top: 55% !important;
          }
        }
      }
    }

  }
  &.home-page-modal {
    .dropdown-multiselect__caret:before {
      display: none;
    }
    .multiselect-dropdown .dropdown-btn {
      padding: 2px 12px;
      line-height: 1.6;
    }
    .multiselect-item-checkbox input[type='checkbox'] + div {
      padding-left: 0em;
    }
    .selected-item {
      background: transparent;
    }
  }
}

.custom-material-form.invite-member-form {
  select {
    &.material-input {
      border: 1px solid $gray;
      border-radius: 5px;
      padding: 0 20px;
    }

    &.select-custom-arrow {
      appearance: none;
      -webkit-appearance: none;
      -moz-appearance: none;
      background: transparent;
      background-image: url("data:image/svg+xml;utf8,<svg fill='black' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>");
      background-repeat: no-repeat;
      background-position-x: 98%;
      background-position-y: 5px;
      background-size: 18px;
    }
  }

  ::-webkit-scrollbar {
    width: 6px;
    height: 3px;
    background: $white;
  }

  .member-scroll {
    max-height: 275px;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .custom-select-li {
    .dropdown-toggle {
      background-color: $white;
      border: 1px solid $gray;
      border-radius: 5px;
      text-align: left;
      color: $black;
      font-size: 11px;

      &:focus,
      &:hover {
        box-shadow: none !important;
        background-color: $white;
        border: 1px solid $gray;
        border-radius: 5px;
        color: $black;
      }

      &::after {
        position: absolute;
        right: 10px;
        top: 12px;
        color: rgba(0, 0, 0, 0.5);
        font-size: 15px;
      }
    }

    .dropdown-item {
      font-size: 12px;
    }
    .dropdown-menu {
      overflow: auto;
      resize: vertical;
      height: 100px;
      &.company-scroll {
        height: 120px;
      }
    }
  }
}
@media (max-width: 360px) {
  .custom-material-form {
    .input-pl-10 {
      .form-control {
        padding-left: 10px !important;
      }
    }
  }
}

@media (max-width: 360px) {
  .custom-material-form {
    .input-pl-10-overview {
      .form-control {
        padding-left: 10px !important;
      }
    }
  }
}

@media (min-width: 768px) and (max-width: 992px) {
  .custom-material-form {
    .input-pl-10-overview {
      .form-control {
        padding-left: 10px !important;
      }
    }
  }
}

@media (min-width: 1024px) and (max-width: 1080px) {
  .custom-material-form {
    .input-pl-10-overview {
      .form-control {
        padding-left: 10px !important;
      }
    }
  }
}

/* Material Input */

/* Autofill for chrome */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px $white inset !important;
}
.tag-input-sample {
  width: 320px;
  &.scheduler-form-taginput {
    width: 330px;
    margin-left: 30px;
  }
}

//Floating Label CSS starts
.floating-concrete {
  .floating-label {
    position: relative;
    margin-bottom: 20px;
  }
  .floating-input {
    font-size: 12px;
    padding: 4px 4px;
    display: block;
    width: 100%;
    height: 30px;
    background-color: transparent;
    border: none;
    border-bottom: 1px solid $light-grey8;
    border-radius: 0px;
  }
  .floating-input:focus {
    outline: none;
  }
  label {
    color: $grey51;
    font-size: 12px;
    font-weight: normal;
    position: absolute;
    pointer-events: none;
    left: 5px;
    top: 5px;
    transition: 0.2s ease all;
    -moz-transition: 0.2s ease all;
    -webkit-transition: 0.2s ease all;
    &.schedule-form-label {
      left: 0px;
    }
  }
  .colorgrey11 {
    color: $grey11 !important;
  }
  .floating-input:focus ~ label,
  .floating-input:not(:placeholder-shown) ~ label {
    top: -18px;
    font-size: 12px;
    color: #757575;
  }
  /* active state */
  .floating-input:focus ~ .bar:before,
  .floating-input:focus ~ .bar:after ~ .bar:after {
    width: 50%;
  }

  *,
  *:before,
  *:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
  }
}
//Floating Label CSS ends

.custom-tag-input {
  input {
    font-size: 12px;
    padding: 5px 0px;
    &::placeholder {
      font-size: 12px !important;
      padding: 1em 0px !important;
    }
  }
  .tag-wrapper {
    .tag__text {
      font-size: 12px;
      line-height: 30px;
    }
    delete-icon {
      margin-top: -3px;
    }
  }
}
.container input:checked ~ .checkmark {
  background-color: $orange;
}

.filter-count-report {
  width: 16px;
  height: 16px;
  font-size: 10px;
  bottom: 15px;
}
.add-concrete-dropdown {
  .multiselect-dropdown {
    margin-top: 10px !important;
  }
}
.homepage-complete-details {
  height: calc(100vh - 150px);
  overflow-y: auto;
  overflow-x: hidden;
  padding: 10px;
}
.project-detail-form {
  border: 1px solid $gray-a;
  margin: 0px;
  background-color: $white;
  padding: 0px 0px 10px;
  height: calc(100vh - 50px);
  overflow-y: auto;
  overflow-x: hidden;
  .material-input.form-control {
    padding-left: 0px !important;
  }
  .project-detail-card {
    box-shadow: 0px 0px 20px #d7dee365;
    border-radius: 8px;
    opacity: 1;
  }
  &.invite-link-form {
    padding: 160px 20px;

    .invite-link-img {
      height: 150px;
    }
  }
  .guest-page-footer {
    position: fixed;
    bottom: 0px;
    width: 100%;
    background-color: $white;
  }
}
.basic-detail-form-error {
  max-height: calc(100vh - 170px);
  overflow-y: auto;
  padding: 10px;
}
.ng2-tag-input__text-input {
  font-size: 10px !important;
}
.custom-material-form.add-concrete-material-form.project-setting-form .tag-wrapper delete-icon svg {
  height: 34px;
}

.guestuser-formcontrol {
  height: 2.2rem;
}
.equipment-select {
  .multiselect-dropdown {
    margin-top: 12px;
  }
}

.modal
  .modal-content
  .company-select.editdelivery-equipment-select
  .multiselect-dropdown
  .dropdown-btn {
  padding: 5px 0px;
}

.crane-tag-layout {
  .ng2-tag-input {
    height: 150px;
  }
}

.modal
  .modal-content
  .company-select.multiple-equipment-request
  .multiselect-dropdown
  .dropdown-btn {
  padding-top: 7px;
}

.project-details-map .agm-map.project-map .map-container {
  height: 200px !important;
  width: auto !important;
}

.form-check-input:checked[type='radio'] {
  --bs-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-2 -2 4 4'%3e%3ccircle r='2' fill='%230d6efd'/%3e%3c/svg%3e");
  background-color: $white;
  border-color: #0d6efd;
  background-size: 10px 8px;
  background-repeat: no-repeat;
  width: 13px;
  height: 13px;
}

.form-check-input:focus {
  border-color: none;
  outline: 0;
}

.custom-control-input:checked {
  background-color: $orange;
  border-color: $orange;
}
.form-group.company-select
  .ng-untouched.ng-pristine.ng-valid
  .multiselect-dropdown
  .dropdown-btn
  .selected-item-container {
  float: none;
}

.fc .fc-daygrid-more-link:hover {
  background-color: transparent;
}
.project-settings-information .agm-map.project-map .map-container {
  height: 200px !important;
  width: 300px !important;
}

.form-check-input:checked[type='checkbox'] {
  background-color: $orange;
}

.form-check-input[type='checkbox'] {
  border-color: #adb5bd;
}

.custom-checkbox .form-check-input[type='checkbox'] {
  border: 1px solid $grey7;
  background-color: transparent;
  border-radius: 3px;
  width: 18px;
  height: 18px;
}

.custom-checkbox .form-check-input:checked[type='checkbox'] {
  background-image: url('../../assets/images/checkbox-tick.svg');
  border-radius: 3px;
  width: 18px;
  height: 18px;
  background-size: 9px 9px;
}

.modal-backdrop.show {
  opacity: 0.6;
  background-color: $black;
}

.form-check.form-check-inline {
  .form-check-input:checked[type='radio'] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-2 -2 4 4'%3e%3ccircle r='2' fill='%23f45e28'/%3e%3c/svg%3e");
    background-color: $white;
    border-color: $orange;
    background-size: 8px 7px;
    background-repeat: no-repeat;
    width: 13px;
    height: 13px;
  }
}
.location-settings-lists {
  .list-group.list-group-horizontal .list-group-item {
    padding: 0.75rem 1.25rem;
  }
}
.table-responsive {
  width: 100%;
}
.custom-material-form .location-settings-textarea textarea.form-control {
  min-height: auto;
}
.form-group .form-control.bs-timepicker-field {
  width: 50px;
}

.notification-intermediate-btn {
  .form-check-input:checked[type='checkbox'] {
    background-color: transparent;
    border-color: $orange;
  }
}
.notify-checkbox {
  .form-check-input[type='checkbox']:checked {
    border-color: #007bff;
    background-color: #007bff;
  }
  .form-check-input[type='checkbox'] {
    background-color: $white;
  }
}
.settings-comapanies-table {
  .custom-checkbox .form-check-input:checked[type='checkbox'] {
    background-color: $orange;
    border-color: $orange;
    background-image: url('../../assets/images/checkbox-tick-white.svg');
  }
  .form-check-input:disabled {
    width: 18px;
    height: 18px;
    border: 1px solid #292529;
  }
}
.custom-checkbox.deliver-check .form-check-input:checked[type='checkbox'] {
  background-image: url('../../assets/images/checkbox-tick-white.svg');
  border-radius: 3px;
  width: 18px;
  height: 18px;
  background-color: $orange;
  border-color: $orange;
}

.custom-material-form .material-input.pb4 {
  padding-bottom: 4px !important;
}

.guestuser-projectdetail {
  .agm-map.project-map .map-container {
    height: 200px !important;
    width: auto !important;
  }
}

.custom-material-form .material-input.form-control.edit-phone-number {
  height: 29px;
}

.form-group.company-select.editinspection-equipment-select {
.multiselect-dropdown .dropdown-btn, .material-input.form-control{
    padding: 5px 0px !important;
  }
}


.modal .modal-content .company-select.equipment-select.inspection-select .multiselect-dropdown .dropdown-btn {
  padding: 10px 0px;
}

.equipment-label {
  margin-bottom: 7px !important;
}
.inline-inspection-form-group {
  margin-left: -97px;
  margin-top: -10px;
}
.pr-0-important {
  padding-left: 0rem !important;
}
