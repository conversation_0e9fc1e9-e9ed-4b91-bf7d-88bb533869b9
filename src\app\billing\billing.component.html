<section class="page-section billing">
  <div class="page-inner-content">
    <div class="top-header my-3">
      <div class="px-md-4">
        <h1 class="fs26 fw-bold cairo-regular">Billing</h1>
      </div>
    </div>
    <div class="row" *ngIf="loader == true">
      <div class="text-center">
        <div class="fs18 fw-bold cairo-regular my-5 text-black">Loading...</div>
      </div>
    </div>
    <div class="card plan-detail white-border m-md-3 br-10 mb-3 mb-md-0" *ngIf="loader == false">
      <div class="card-body">
        <h2 class="fs16 fw600 color-grey7 plan-heading">My Plan Detail</h2>
        <div class="row mt-3 my-plan-row">
          <div class="col-md-2 col-sm-2">
            <h3 class="fs12 fw600 color-grey8 mb-2">Plan Name</h3>
            <p class="fs12 fw600 color-grey7">Enterprise Plan</p>
          </div>
          <div class="col-md-2 col-sm-2 text-nowrap">
            <h3 class="fs12 fw600 color-grey8 mb-2">Amount</h3>
            <p class="fs12 fw600 color-grey7">${{ nextPayDet.amount }}</p>
          </div>
          <div class="col-md-3 col-sm-3 text-nowrap">
            <h3 class="fs12 fw600 color-grey8 mb-2">Last Payment</h3>
            <p class="fs12 fw600 color-grey7">{{ nextPayDet.lastPayment | date : 'short' }}</p>
          </div>
          <div class="col-md-3 col-sm-3 text-nowrap">
            <h3 class="fs12 fw600 color-grey8 mb-2">Next Payment</h3>
            <p class="fs12 fw600 color-green">{{ nextPayDet.nextPayment | date : 'short' }}</p>
          </div>
          <div class="col-md-2 col-sm-2">
            <button
              (click)="openModal(paymentModel)"
              class="btn btn-orange billing-button color-orange radius20 fs13 w-100 mx-auto fw-bold cairo-regular"
            >
              Pay
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="card plan-detail white-border m-md-3 br-10 mb-3 mb-md-0" *ngIf="loader == false">
      <h2 class="fs16 fw600 color-grey7 p-3">Payment History</h2>

      <div class="card-body px-0 pt-0">
        <div class="table-responsive rounded tab-grid">
          <table class="table table-custom mb-0 border-0" aria-describedby="Memtable">
            <thead>
              <th scope="col" resizable>Amount</th>
              <th scope="col" resizable>Status</th>
              <th scope="col" resizable>Recipient</th>
              <th scope="col" resizable>Date Paid</th>
              <th scope="col" resizable>Payment Method</th>
              <th scope="col" resizable>Invoice</th>
            </thead>
            <tbody>
              <tr
                *ngFor="
                  let item of billingData
                    | paginate
                      : {
                          itemsPerPage: pageSize,
                          currentPage: currentPageNo,
                          totalItems: totalCount
                        };
                  let i = index
                "
              >
                <td>${{ item.amount }}</td>
                <td>
                  <span class="color-green">{{ item.status }}</span>
                </td>
                <td>{{ item?.User?.firstName }} {{ item?.User?.lastName }}</td>
                <td>{{ item.lastPayment | date : 'short' }}</td>
                <td>{{ item.paymentMethod }}</td>
                <td>
                  <button
                    class="btn btn-grey-outline fs10 radius20 fs500"
                    (click)="downloadReceipt(item.receiptUrl)"
                  >
                    Download
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div
          class="tab-pagination px-2"
          id="tab-pagination1"
          *ngIf="loader == false && totalCount > 25"
        >
          <div class="row mt-3">
            <div class="col-md-3 col-12 text-lg-left text-center align-items-center">
              <ul class="list-inline my-3">
                <li class="list-inline-item notify-pagination">
                  <label for="pageSizeSelect" class="fs12 color-grey4">Show entries</label>
                </li>
                <li class="list-inline-item">
                  <select
                    class="w-auto form-select fs12 color-grey4"
                    (change)="changePageSize($event.target.value)"
                    ngModel="{{ pageSize }}"
                  >
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                    <option value="150">150</option>
                  </select>
                </li>
              </ul>
            </div>
            <div class="col-md-7 col-12 text-center">
              <div class="my-3 position-relative d-inline-block">
                <pagination-controls
                  (pageChange)="changePageNo($event)"
                  previousLabel=""
                  nextLabel=""
                >
                </pagination-controls>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<ng-template #paymentModel>
  <div class="modal-body">
    <div class="text-center my-4 mx-auto">
      <p class="fs14 color-grey7 fw600">Choose payment mode</p>
      <div class="d-flex justify-content-center mt-4">
        <button
          class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular eye-cursor me-3 px-2rem"
          type="button"
          [disabled]="submitted"
          (click)="payOffline()"
        >
          <em class="fa fa-spinner" aria-hidden="true" *ngIf="submitted && payOfflineChosen"></em>
          Offline
        </button>
        <button
          class="btn btn-orange radius20 fs12 color-orange fw-bold cairo-regular eye-cursor px-2rem"
          (click)="openCardModal(cardModel)"
          [disabled]="submitted"
          (click)="payOnline()"
        >
          <em class="fa fa-spinner" aria-hidden="true" *ngIf="submitted && payOnlineChosen"></em>
          Online
        </button>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #cardModel>
  <div class="modal-body">
    <div class="text-center my-12 mx-auto">
      <p class="fs14 color-grey7 fw600">you have chosen online payment</p>
      <img src="./assets/images/cross-details.svg" alt="close" (click)="reset()" (keydown)="handleToggleKeydown($event)"/>
      <div class="d-flex justify-content-center mt-4">
        <form
          name="form"
          [formGroup]="billingCardDetailsForm"
          id="payment-form2"
          (ngSubmit)="onSubmit()"
          novalidate
        >
          <div class="row">
            <div class="col-md-12">
              <div class="form-group">
                <label for="cardName" class="fs14 color-grey23 mb-0">Name on card</label>
                <input id="cardName"
                  class="form-control"
                  placeholder=""
                  formControlName="name"
                  (keypress)="alphaOnly($event)"
                />
                <div
                  class="color-red"
                  *ngIf="submitted && billingCardDetailsForm.get('name').errors"
                >
                  <small *ngIf="billingCardDetailsForm.get('name').errors.required"
                    >*Enter Name</small
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6 pe-md-1">
              <div class="form-group">
                <label for="cardNum" class="fs14 color-grey23 mb-0">Card number</label>
                <input id="cardNum"
                  class="form-control"
                  placeholder=""
                  formControlName="number"
                  (keypress)="numberOnly($event)"
                  mask="{{ cardMask }}"
                />
                <div
                  class="color-red"
                  *ngIf="submitted && billingCardDetailsForm.get('number').errors"
                >
                  <small *ngIf="billingCardDetailsForm.get('number').errors.required"
                    >*Enter Number</small
                  >
                  <small
                    *ngIf="
                      !billingCardDetailsForm.get('number').errors.required &&
                      !billingCardDetailsForm.get('number').valid
                    "
                    >*Enter Valid Card Number</small
                  >
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="row">
                <div class="col-md-8 col-8">
                  <label class="fs14 color-grey23 mb-0" for="expMonth">Expiry date (MM YYYY)</label>
                  <div class="row">
                    <div class="col-md-6 col-6 pe-1">
                      <div class="form-group">
                        <select  id="expMonth"
                          class="custom-select month pe-0"
                          formControlName="exp_month"
                          [ngClass]="{
                            'is-invalid':
                              submitted && billingCardDetailsForm.get('exp_month').invalid
                          }"
                          required
                        >
                          <option selected hidden value="">MM</option>
                          <ng-container *ngFor="let month of billingCardMonths">
                            <option value="{{ month }}">{{ month }}</option>
                          </ng-container>
                        </select>
                        <div
                          class="color-red"
                          *ngIf="submitted && billingCardDetailsForm.get('exp_month').errors"
                        >
                          <small
                            class="d-block pt-1 error-text"
                            *ngIf="billingCardDetailsForm.get('exp_month').errors.required"
                            >*Choose Expiry Month</small
                          >
                        </div>
                      </div>
                    </div>
                    <div class="col-md-6 col-6 pe-1">
                      <div class="form-group">
                        <select
                          class="custom-select year pe-0"
                          formControlName="exp_year"
                          [ngClass]="{
                            'is-invalid':
                              submitted && billingCardDetailsForm.get('exp_year').invalid
                          }"
                          required
                        >
                          <option selected hidden value="">YYYY</option>
                          <ng-container *ngFor="let year of billingCardYears">
                            <option value="{{ year }}">{{ year }}</option>
                          </ng-container>
                        </select>
                        <div
                          class="color-red"
                          *ngIf="submitted && billingCardDetailsForm.get('exp_year').errors"
                        >
                          <small
                            class="d-block pt-1 error-text"
                            *ngIf="billingCardDetailsForm.get('exp_year').errors.required"
                            >*Choose Expiry Year</small
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4 col-4">
                  <label for="cvv" class="fs14 color-grey23 mb-0">(CVC/CVV)</label>
                  <div class="form-group">
                    <input id="cvv"
                      class="form-control"
                      placeholder=""
                      formControlName="cvc"
                      maxlength="3"
                    />
                    <div
                      class="color-red"
                      *ngIf="submitted && billingCardDetailsForm.get('cvc').errors"
                      (keypress)="numberOnly($event)"
                    >
                      <small *ngIf="billingCardDetailsForm.get('cvc').errors.required"
                        >*Enter cvc</small
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6 pe-md-1">
              <div class="form-group">
                <label for="country" class="fs14 color-grey23 mb-0">Country</label>
                <select id="country"
                  class="custom-select form-control material-input fs13 color-grey23"
                  formControlName="country"
                >
                  <option value="" disabled selected hidden>Country</option>
                  <option *ngFor="let item of countryList" value="{{ item.countryName }}">
                    {{ item.countryName }}
                  </option>
                </select>
                <div
                  class="color-red"
                  *ngIf="submitted && billingCardDetailsForm.get('country').errors"
                >
                  <small *ngIf="billingCardDetailsForm.get('country').errors.required"
                    >*Enter country</small
                  >
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="zipCode" class="fs14 color-grey23 mb-0">Zip code</label>
                <input id="zipCode" class="form-control" placeholder="" formControlName="zipCode" />
                <div
                  class="color-red"
                  *ngIf="submitted && billingCardDetailsForm.get('zipCode').errors"
                  (keypress)="alphaNum($event)"
                >
                  <small *ngIf="billingCardDetailsForm.get('zipCode').errors.required"
                    >*Enter zip code</small
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="my-3 text-center">
            <a
              class="btn btn-grey-light color-dark-grey radius20 fs12 me-4 mt-2 fw-bold cairo-regular px-2rem"
              (click)="reset()"
              >Cancel</a
            >
            <button
              class="btn btn-orange-dark radius20 fs12 mt-2 fw-bold cairo-regular px-5"
              type="submit"
              [disabled]="formSubmitted && billingCardDetailsForm.valid"
            >
              <em
                class="fa fa-spinner"
                aria-hidden="true"
                *ngIf="formSubmitted && billingCardDetailsForm.valid"
              ></em>
              Pay
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</ng-template>
