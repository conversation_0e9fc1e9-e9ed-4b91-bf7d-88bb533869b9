.btn-blue {
  background-color: $blue;
  color: $white;

  &:hover {
    background-color: $blue2;
    color: $white;
  }
}

.btn-blue2 {
  background-color: $blue2;
  color: $white;

  &:hover {
    background-color: $blue;
    color: $white;
  }
}

.btn-navy-blue {
  background-color: $navy-blue;
  color: $white;

  &:hover {
    background-color: $navy-blue;
    color: $white;
  }
}

.btn-grey {
  background-color: $grey2;
  color: $dark-grey;
  &.btn-radius {
    font-size: 10px;
    border-radius: 20px;
    padding-top: 6px;
    padding-bottom: 6px;
  }
  &:hover {
    background-color: $grey3;
    color: $white;
  }
  &:focus {
    box-shadow: none;
  }
}

.btn-grey-light {
  background-color: $dark-grey-1;
  color: $dark-grey;
  transition: all 1s;

  &:hover {
    background-color: $grey3;
    color: $white;
  }
}

.btn-green {
  background-color: $green7;
  color: $green;
  transition: all 1s;
  opacity: 1;
  &:hover {
    background-color: $green2;
    color: $white;
  }
}
.btn-green2 {
  background-color: $green8;
  color: $white;
  transition: all 1s;
  opacity: 1;
  padding: 4px 10px !important;
  &:hover {
    background-color: $white;
    color: $green8;
    border: 1px solid $green8;
  }
}
.btn-transparent {
  background-color: transparent;
  color: #0086ee;
  &:hover {
    background-color: transparent;
    color: $white;
  }
}
.btn-transparent-red {
  background-color: transparent;
  color: #f45e28;
  &:hover {
    background-color: transparent;
    color: #f45e28;
  }
}
.btn-green1 {
  background-color: rgba(0, 214, 35, 0.2);
  color: $green3;
  transition: all 1s;
  opacity: 1;
  &.btn-radius {
    font-size: 10px;
    border-radius: 20px;
    padding-top: 6px;
    padding-bottom: 6px;
  }
  &:hover {
    background-color: $green2;
    color: $white;
  }
}

.btn-white {
  background: $white1;
  line-height: 28px;
  transition: 1s all;
&.btn-template-white {
  line-height: 32px;
}
  &:focus,
  &:hover {
    background-color: $white1;
    border-color: $orange;
    color: $orange;
    box-shadow: 0 0 5px 0 $grey47;
    transform: translateX(10px);
  }
}

.btn-white-outline {
  background: $white;
  border-color: $navy-blue;
  color: $navy-blue;

  &:focus,
  &:hover {
    background-color: $navy-blue;
    color: $white;
  }
}

.btn-orange {
  background-color: $red8;
  transition: all 1s;
  opacity: 1;
  color: $orange;

  &:hover {
    background-color: $orange;
    color: $white;
  }
  &:focus {
    box-shadow: none;
  }
}

.btn-light-orange {
  background-color: $orange1;
  transition: all 1s;
  opacity: 1;
  color: $orange-light;
  font-size: 10px;
  border-radius: 20px;
  padding-top: 6px;
  padding-bottom: 6px;
  &:hover {
    background-color: $orange-light;
    color: $white;
  }
  &:focus {
    box-shadow: none;
  }
}
.btn-light-blue {
  background-color: $btn-light-blue;
  transition: all 1s;
  color: $blue20;
  font-size: 10px;
  border-radius: 20px;
  padding-top: 6px;
  padding-bottom: 6px;
  &:hover {
    background-color: $blue20;
    color: $white;
  }
}

.btn-red {
  background-color: $red9;
  transition: all 1s;
  opacity: 1;
  color: $red1;

  &:hover {
    background-color: $red1;
    color: $white;
  }
}

.btn-orange-dark {
  background-color: $orange;
  color: $white;
  transition: all 1s;
  opacity: 1;

  &:hover {
    background-color: $orange;
    color: $white;
  }
  &:focus {
    box-shadow: none;
  }
  &.btn-okdark {
    padding: 4px 65px;
  }
  &:disabled {
    background-color: $orange;
    color: $white;
    transition: all 1s;
    opacity: 0.65;
  }
}

.btn-orange-outline {
  background: transparent;
  border-color: $orange;
  color: $orange;
  transition: all 1s;

  &:focus,
  &:hover {
    background-color: transparent;
    color: $orange;
  }
  &.btn-hover-disabled {
    &:focus,
    &:hover {
      border-color: $orange;
      color: $orange;
    }
  }
}

.btn-grey-outline {
  background: transparent;
  border-color: $grey40;
  color: $grey7;
  transition: all 1s;

  &:focus,
  &:hover {
    background-color: transparent;
    color: $orange;
    border-color: $orange;
  }
}

.btn-orange-dark1 {
  background-color: $orange;
  color: $white;
  transition: all 1s;
  opacity: 1;
  font-size: 12px;
  padding: 0;
  width: 50px;
  height: 25px;
  border-radius: 20px;

  &:hover {
    background-color: transparent;
    color: $orange;
    border-color: $orange;
  }
}

.btn-orange-dark2 {
  background-color: $orange;
  color: $white;
  transition: all 1s;
  opacity: 1;
  font-size: 10px;
  border-radius: 20px;
  padding-top: 6px;
  padding-bottom: 6px;

  &:hover {
    background-color: transparent;
    color: $orange;
    border-color: $orange;
  }
}

.btn-primary-grey-outline {
  border: 1px solid $grey40;
}

.btn-dark-grey {
  background-color: $grey41;
  border-color: $grey41;

  &:focus,
  &:hover {
    background-color: $grey41;
    border-color: $grey41;
  }
}

.run-btn {
  border-radius: 20px;
  padding: 0px 12px;
}

.btn-transparent-gray {
  background-color: transparent;
  color: $grey50;

  &:hover {
    background-color: $grey9;
    color: $grey50;
  }
  &:focus {
    box-shadow: none;
  }
}
.btm-border-orange{
  border-bottom: 1.5px solid $orange;
}
.btn.focus {
  outline: 0;
  box-shadow: none;
}

/*Custom toggle Switch with text */
.switches-container {
  width: 16rem;
  position: relative;
  display: flex;
  padding: 0;
  background: $white;
  border: 1.5px solid $orange;
  line-height: 1.5rem;
  border-radius: 0;
  input {
    visibility: hidden;
    position: absolute;
    top: 0;
    &:nth-of-type(1):checked {
      & ~ .switch-wrapper {
        transform: translateX(0%);
        .switch {
          div {
            &:nth-of-type(1) {
              opacity: 1;
            }
          }
        }
      }
    }
    &:nth-of-type(2):checked {
      & ~ .switch-wrapper {
        transform: translateX(100%);
        .switch {
          div {
            &:nth-of-type(2) {
              opacity: 1;
            }
          }
        }
      }
    }
  }
  label {
    width: 50%;
    padding: 0;
    margin: 0;
    text-align: center;
    cursor: pointer;
    color: $orange;
    font-size: 14px;
  }
  &.settingstab-switches {
    width: 14rem;
    line-height: 1.2rem;
  }
}
.switch-wrapper {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 50%;
  z-index: 3;
  transition: transform 0.5s cubic-bezier(0.77, 0, 0.175, 1);
}
.switch {
  background: $orange;
  height: 100%;
  div {
    width: 100%;
    text-align: center;
    opacity: 0;
    display: block;
    color: $white;
    transition: opacity 0.2s cubic-bezier(0.77, 0, 0.175, 1) 0.125s;
    will-change: opacity;
    position: absolute;
    top: 0;
    left: 0;
    font-size: 14px;
  }
}
.btn-transparent-lgtgray {
  background-color: transparent;
  color: $grey50;

  &:hover {
    background-color: $grey9;
    color: $grey50;
  }
  &:active {
    background: rgba(244, 96, 42, 0.2);
    transition: all 1s;
    opacity: 1;
    color: $black;
  }
  &:focus {
    box-shadow: none;
  }
}

.btn:disabled {
  cursor: pointer;
  pointer-events: auto;
  background-color: $white;
  border-color: $white;
  color: $orange;
  opacity: unset;
  &:hover {
    border-color: $orange;
  }
}

.markread-btn,.editdelivery-booking-btn,.template-submit {
  .btn:disabled {
    color: $orange;
    background-color: rgb(255, 222, 210);
    &:hover {
      color: $white;
      background-color: $orange;
    }
  }
}

.btn-close:focus {
  box-shadow: none;
}

.spin-btn:disabled {
  border-color: $orange;
}