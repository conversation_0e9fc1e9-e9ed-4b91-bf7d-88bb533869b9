import {
  Component, TemplateRef, ViewChild, AfterViewInit,
} from '@angular/core';
import { FullCalendarComponent } from '@fullcalendar/angular';
import { Calendar, CalendarOptions } from '@fullcalendar/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import moment from 'moment';
import interactionPlugin from '@fullcalendar/interaction';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import { Title } from '@angular/platform-browser';
import { CalendarService } from '../../../services/profile/calendar.service';
import { ProjectService } from '../../../services/profile/project.service';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { EditDeliveryFormComponent } from '../../../delivery-requests/delivery-details/edit-delivery-form/edit-delivery-form.component';
import { MixpanelService } from '../../../services/mixpanel.service';
import { ProjectSharingService } from '../../../services';
import { GuestDeliveryDetailsComponent } from '../delivery/guest-delivery-details/guest-delivery-details.component';

@Component({
  selector: 'app-delivery-calendar',
  templateUrl: './delivery-calendar.component.html',
  })
export class DeliveryCalendarComponent implements AfterViewInit {
  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public descriptionPopup = false;

  public events: any = [];

  public calendar: Calendar;

  public calendarApi: any;

  public ProjectId: any;

  public Range: any = {};

  public loader = true;

  public deliveryList: any = [];

  public search = '';

  public lastId: any = 0;

  public dropdownSettings: IDropdownSettings;

  public eventData: any = {};

  public calendarCurrentDeliveryIndex = -1;

  public showStatus = false;

  public authUser: any = {};

  public filterForm: UntypedFormGroup;

  public filterCount = 0;

  public definableDropdownSettings: IDropdownSettings;

  public statusValue: any = [];

  public currentStatus = '';

  public currentEditItem: any = {};

  public modalLoader = false;

  public companyList: any = [];

  public defineList: any = [];

  public voidSubmitted = false;

  public gateList: any = [];

  public equipmentList: any = [];

  public submitted = false;

  public formSubmitted = false;

  public memberList: any = [];

  public statusSubmitted = false;

  public deliveryId;

  public currentDeliverySaveItem: any = {};

  public ParentCompanyId: number;

  public showSearchbar = false;

  public calendarDescriptionPopup = false;

  public viewEventData: any;

  public message = '';

  public toolTipContent = '';

  public wholeStatus = ['Approved', 'Declined', 'Delivered', 'Pending'];

  public currentViewMonth: moment.MomentInput;

  public currentView = 'Day';

  public monthlyEventloader = true;

  public monthEvents = [];

  public allRequestIsOpened = false;

  public seriesOptions = [];

  public approved: string;

  public pending: string;

  public rejected: string;

  public delivered: string;

  public expired: string;

  public locationList: any = [];

  public isGuestUser: boolean = false;

  public isGuestReq: boolean = false;

  public id: number;

  public email: string;

  public guestUserId: any;

  public guestMemberId: any;

  @ViewChild('fullcalendar', { static: true }) public calendarComponent1: FullCalendarComponent;

  public calendarOptions: CalendarOptions = {
    selectable: true,
    initialView: 'timeGridDay',
    plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin],
    aspectRatio: 2,
    slotEventOverlap: false,
    contentHeight: 'liquid',
    fixedWeekCount: false,
    expandRows: true,
    nowIndicator: true,
    moreLinkClick: 'popover',
    events: this.events,
    customButtons: {
      prev: {
        text: 'PREV',
        click: (): void => this.goPrev(),
      },
      next: {
        text: 'Next',
        click: (): void => this.goNext(),
      },
      prevYear: {
        text: 'PREV',
        click: (): void => this.goPrevYear(),
      },
      nextYear: {
        text: 'Next',
        click: (): void => this.goNextYear(),
      },
      timeGridWeek: {
        text: 'Week',
        click: (): void => this.goTimeGridWeekOrDay('timeGridWeek'),
      },
      timeGridDay: {
        text: 'Day',
        click: (): void => this.goTimeGridWeekOrDay('timeGridDay'),
      },
      dayGridMonth: {
        text: 'Month',
        click: (): void => this.goDayGridMonth(),
      },
    },
    showNonCurrentDates: false,
    headerToolbar: {
      right: '',
      center: 'prevYear,prev,title,next,nextYear',
      left: 'dayGridMonth,timeGridWeek,timeGridDay',
    },
    firstDay: 0,
    eventClick: (arg): void => {
      this.eventData = [];
      this.deliveryDescription(arg);
    },
    datesSet: (info): void => {
      this.currentViewMonth = info.view.title;
    },
    eventTimeFormat: {
      hour: 'numeric',
      minute: '2-digit',
      meridiem: 'short',
    },
    eventDidMount(info): void {
      const argument = info;
      const isGuest = argument.event._def.extendedProps.isGuestUser;
      const { isGuestRequest } = argument.event.extendedProps;
      const requestCreatorId = argument.event._def.extendedProps.creatorId;
      const calendarType = argument.event._def.extendedProps.requestType;
      const loginMemberId = argument.event._def.extendedProps.guestMemberId;
      const line1 = argument.event._def.extendedProps.description;
      const start = argument.event._def.extendedProps.deliveryStartTime;
      const end = argument.event._def.extendedProps.deliveryEndTime;
      const { line2 } = argument.event._def.extendedProps;
      const line3 = 'Booking';
      const eventTitleElement: HTMLElement = argument.el.querySelector('.fc-event-title');
      eventTitleElement.style.backgroundColor = info.backgroundColor;
      eventTitleElement.style.color = info.textColor;
      eventTitleElement.style.borderLeft = `4px solid ${info.borderColor}`;
      if (argument.event._def.allDay) {
        if ((isGuest && isGuestRequest && (requestCreatorId === loginMemberId)) || calendarType === 'calendarEvent') {
          eventTitleElement.innerHTML = `
            <div class="d-flex flex-column">
              <p class="m-0"><img class="w10 h10" src="./assets/images/noun-event-alert.svg" class="form-icon" alt="allday"> ${line1}</p>
              <small>${line2}</small>
            </div>`;
        } else {
          eventTitleElement.innerHTML = `
            <div class="d-flex flex-column">
              <p class="m-0"><img class="w10 h10" src="./assets/images/noun-event-alert.svg" class="form-icon" alt="allday"> ${line3}</p>
              <small class="text-wrap">${start} - ${end}</small>
            </div>`;
        }
      } else if ((isGuest && isGuestRequest && (requestCreatorId === loginMemberId)) || calendarType === 'calendarEvent') {
        eventTitleElement.innerHTML = `
          <div class="d-flex flex-column">
            <p class="m-0">${line1}</p>
            <small class="text-wrap truncate">${line2}</small>
          </div>`;
      } else {
        eventTitleElement.innerHTML = `
          <div class="d-flex flex-column">
            <p class="m-0">${line3}</p>
            <small class="text-wrap">${start} - ${end}</small>
          </div>`;
      }
    },
    dayMaxEventRows: true, // for all non-TimeGrid views
    views: {
      timeGrid: {
        dayMaxEventRows: 2, // adjust to 6 only for timeGridWeek/timeGridDay
      },
      dayGridMonth: {
        allDaySlot: true,
      },
      timeGridWeek: {
        allDaySlot: true,
      },
      timeGridDay: {
        allDaySlot: true,
      },
    },
    dateClick: (info): void => {
    },
  };

  public constructor(
    public bsModalRef: BsModalRef,
    private readonly modalService: BsModalService,
    public calendarService: CalendarService,
    public projectService: ProjectService,
    private readonly formBuilder: UntypedFormBuilder,
    public deliveryService: DeliveryService,
    private readonly ProjectSharingServices: ProjectSharingService,
    private readonly toastr: ToastrService,
    public router: Router,
    private readonly mixpanelService: MixpanelService,
    private readonly titleService: Title,
  ) {
    this.titleService.setTitle('Follo - Guest Delivery Calendar');
    this.filterDetailsForm();
    this.id = +window.atob(localStorage.getItem('guestId'));
    this.email = window.atob(localStorage.getItem('guestEmail'));
    this.ProjectId = +window.atob(localStorage.getItem('guestProjectId'));
    this.ParentCompanyId = +window.atob(localStorage.getItem('guestParentCompanyId'));
    this.guestUserId = +window.atob(localStorage.getItem('guestId'));
    this.checkGuest();
    this.getEventNDR();
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'clear':
          this.clear();
          break;
        case 'openFilter':
          this.openFilterModal(data);
          break;
        case 'open':
          this.openIdModal(data);
          break;
        case 'close':
          this.closeCalendarDescription();
          break;
        case 'edit':
          this.openEditModal(data, item);
          break;
        default:
          break;
      }
    }
  }

  public checkGuest() {
    const payload = {
      email: this.email,
      ProjectId: this.ProjectId,
    };

    this.ProjectSharingServices.checkIsGuest(payload).subscribe({
      next: (response: any): void => {
        if (response?.data) {
          this.guestMemberId = response?.data.id;
          this.isGuestUser = response.data.isGuestUser;
        } else {
          this.isGuestUser = false;
        }
      },
      error: (error: any) => {
        console.error('Error occurred:', error);
        this.toastr.error('Try again later.!', 'Something went wrong.');
      },
    });
  }

  public initializeSeriesOption(): void {
    this.seriesOptions = [
      {
        option: 1,
        text: 'This event',
        disabled: false,
      },
      {
        option: 2,
        text: 'This and all following events',
        disabled: false,
      },
    ];
  }

  public changeRequestCollapse(data): void {
    this.initializeSeriesOption();
    if (!moment(data.deliveryStart).isAfter(moment())) {
      this.seriesOptions = this.seriesOptions.filter((object): any => {
        const seriesObject = object;
        if (seriesObject.option !== 1) {
          seriesObject.disabled = true;
        }
        return seriesObject;
      });
    }
    this.allRequestIsOpened = !this.allRequestIsOpened;
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.getEventNDR();
  }

  public getSearchNDR(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.search = data;
    this.getEventNDR();
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.search = '';
    this.filterDetailsForm();
    this.getEventNDR();
    this.modalRef.hide();
  }

  public calendarGetCompany(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices.getCompanyList(params).subscribe((companyResponse: any): void => {
      if (companyResponse) {
        this.companyList = companyResponse.companyList;
        this.calendarGetDefinable();
        this.dropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'companyName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
      }
    });
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      companyFilter: [''],
      descriptionFilter: [''],
      statusFilter: [''],
      memberFilter: [''],
      gateFilter: [''],
      equipmentFilter: [''],
      locationFilter: [''],
      dateFilter: [''],
      pickFrom: [''],
      pickTo: [''],
    });
  }

  public ngAfterViewInit(): void {
    this.setCalendar();
    this.getMembers();
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      id: this.guestUserId,
    };
    this.ProjectSharingServices.guestGetMemberRole(params).subscribe((res): void => {
    });
  }

  public setCalendar(): void {
    this.calendarApi = this.calendarComponent1.getApi();
    this.Range = this.calendarApi?.currentData?.dateProfile.activeRange;
    this.getEventNDR();
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('descriptionFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('dateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('companyFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('memberFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('gateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('equipmentFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('locationFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('statusFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('pickFrom').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('pickTo').value !== '') {
      this.filterCount += 1;
    }
    this.getEventNDR();
    this.modalRef.hide();
  }

  public buildGetEventNdrPayload() {
    if (!this.filterForm) return {};
    const getEventNdrDateInFilterForm = this.filterForm.value.dateFilter
      ? moment(this.filterForm.value.dateFilter).format('YYYY-MM-DD')
      : this.filterForm.value.dateFilter;
    const getEventNdrPayloadData = {
      companyFilter: +this.filterForm.value.companyFilter,
      descriptionFilter: this.filterForm.value.descriptionFilter,
      dateFilter: getEventNdrDateInFilterForm,
      statusFilter: this.filterForm.value.statusFilter,
      memberFilter: +this.filterForm.value.memberFilter,
      gateFilter: +this.filterForm.value.gateFilter,
      equipmentFilter: this.filterForm.value.equipmentFilter === null || this.filterForm.value.equipmentFilter === '' ? null : +this.filterForm.value.equipmentFilter,
      locationFilter: this.filterForm.value.locationFilter,
      pickFrom: this.filterForm.value.pickFrom,
      pickTo: this.filterForm.value.pickTo,
      ParentCompanyId: this.ParentCompanyId,
      search: this.search,
      id: this.id,
      start: moment(this.Range?.start).format('YYYY-MM-DD'),
      end: moment(this.Range?.end).format('YYYY-MM-DD'),
      filterCount: this.filterCount,
      calendarView: this.currentView,
    };
    return getEventNdrPayloadData;
  }


  public getDeliveryListDetails(approved, pending, rejected, expired, delivered, cardData): void {
    const previewSelected = cardData.filter((item) => item.selected);
    this.deliveryList.forEach((element): void => {
      const assignData = this.getAssignedPayload(element);
      if (element.requestType === 'calendarEvent') {
        assignData.description = element.description;
        assignData.title = element.description;
      }

      if (element.requestType === 'calendarEvent' && element.isAllDay === true) {
        delete assignData.allDay;
        delete assignData.allDaySlot;
        assignData.allDay = true;
        assignData.allDaySlot = true;
      }

      if (
        element.requestType === 'deliveryRequest'
        || element.requestType === 'deliveryRequestWithCrane'
      ) {
        previewSelected.forEach((item) => {
          const value = this.getValueByLabel(item.label, element);

          if (item.line === 1) {
            assignData.description = value;
          } else if (item.line === 2) {
            assignData.line2 = value;
          }
        });
      }
      const statusStyles = {
        Pending: pending,
        Approved: approved,
        Declined: rejected,
        Expired: expired,
        Delivered: delivered,
      };
      if (element.status && statusStyles[element.status]) {
        const style = statusStyles[element.status];
        assignData.color = style.backgroundColor;
        assignData.textColor = style.fontColor;
        assignData.borderColor = element.status === 'Delivered'
          ? style.backgroundColor
          : style.fontColor;
        this.events.push(assignData);
      } else if (!element.status) {
        assignData.className = 'calendar_event';
        this.events.push(assignData);
      }
    });
  }

  public getValueByLabel(label: string, element: any): string {
    switch (label) {
      case 'Description':
        return element.description || '';

      case 'Responsible Company':
        return element?.companyDetails?.[0]?.Company?.companyName || '';

      case 'Responsible Person': {
        const user = element?.memberDetails?.[0]?.Member?.User;
        return `${user?.firstName || ''} ${user?.lastName || ''}`.trim();
      }

      case 'Gate':
        return element?.gateDetails?.[0]?.Gate?.gateName || '';

      case 'Delivery ID':
        return element.DeliveryId || '';

      case 'Definable Feature Of Work':
        return element?.defineWorkDetails?.[0]?.DeliverDefineWork?.DFOW || '';

      case 'Equipment':
        return element?.equipmentDetails?.[0]?.Equipment?.equipmentName || '';

      default:
        return '';
    }
  }


  public getAssignedPayload(element) {
    const payload = {
      description: '',
      title: '',
      start: '',
      end: '',
      id: '',
      uniqueNumber: '',
      companyName: '',
      allDay: false,
      allDaySlot: false,
      line2: '',
      isGuestRequest: false,
      deliveryStartTime: '',
      deliveryEndTime: '',
      creatorId: null,
      isGuestUser: this.isGuestUser,
      guestMemberId: this.guestMemberId,
      requestType: element.requestType,
      color: '',
      textColor: '',
      borderColor: '',
      className: '',
    };

    payload.isGuestRequest = element.isCreatedByGuestUser;
    if (element?.createdUserDetails?.id) {
      payload.creatorId = element.createdUserDetails.id;
    } else {
      payload.creatorId = null;
    }

    payload.deliveryStartTime = moment(element.deliveryStart).format('hh:mm A');
    payload.deliveryEndTime = moment(element.deliveryEnd).format('hh:mm A');
    payload.start = element.requestType === 'deliveryRequest'
      || element.requestType === 'deliveryRequestWithCrane'
      ? element.deliveryStart
      : element.fromDate;
    payload.end = element.requestType === 'deliveryRequest'
      || element.requestType === 'deliveryRequestWithCrane'
      ? element.deliveryEnd
      : element.toDate;
    payload.id = element.id;
    payload.uniqueNumber = element.uniqueNumber;

    return payload;
  }

  public getEventNDR(): void {
    this.loader = true;
    this.deliveryList = [];
    if (this.ProjectId && this.ParentCompanyId) {
      const filterParams = {
        ProjectId: this.ProjectId,
        void: 0,
      };
      const getEventNdrPayload = this.buildGetEventNdrPayload();
      this.ProjectSharingServices
        .getEventNDR(filterParams, getEventNdrPayload)
        .subscribe((NdrResponse: any): void => {
          if (NdrResponse) {
            this.loader = false;
            const responseData = NdrResponse.data;
            const statusData = JSON.parse(NdrResponse.statusData.statusColorCode);
            const cardData = JSON.parse(NdrResponse.cardData.deliveryCard);
            const UseTextColorAsLegendColor = JSON.parse(
              NdrResponse.statusData.useTextColorAsLegend,
            );
            const isDefaultColor = JSON.parse(NdrResponse.statusData.isDefaultColor);
            const approved = statusData.find((item) => item.status === 'approved');
            const pending = statusData.find((item) => item.status === 'pending');
            const delivered = statusData.find((item) => item.status === 'delivered');
            const rejected = statusData.find((item) => item.status === 'rejected');
            const expired = statusData.find((item) => item.status === 'expired');

            if (UseTextColorAsLegendColor) {
              this.approved = approved.fontColor;
              this.pending = pending.fontColor;
              this.expired = expired.fontColor;
              this.rejected = rejected.fontColor;
              this.delivered = delivered.fontColor;
            } else {
              this.approved = approved.backgroundColor;
              this.pending = pending.backgroundColor;
              this.expired = expired.backgroundColor;
              this.rejected = rejected.backgroundColor;
              this.delivered = delivered.backgroundColor;
            }
            if (isDefaultColor) {
              this.delivered = delivered.backgroundColor;
            }
            this.lastId = NdrResponse.lastId;
            this.deliveryList = responseData;
            this.events = [];
            this.getDeliveryListDetails(approved, pending, rejected, expired, delivered, cardData);
            this.calendarApi.removeAllEventSources();
            this.calendarApi.addEventSource(this.events);
          }
        });
    }
  }

  public goNext(): void {
    this.calendarApi.next();
    this.setCalendar();
  }

  public goTimeGridWeekOrDay(view): void {
    if (view === 'timeGridWeek') {
      this.currentView = 'Week';
    } else {
      this.currentView = 'Day';
    }
    this.closeDescription();
    this.calendarApi.changeView(view);
    this.setCalendar();
  }

  public goDayGridMonth(): void {
    this.currentView = 'Month';
    this.closeDescription();
    this.calendarApi.changeView('dayGridMonth');
    this.setCalendar();
  }

  public goPrev(): void {
    this.closeDescription();
    this.calendarApi.prev(); // call a method on the Calendar object
    this.setCalendar();
  }

  public goPrevYear(): void {
    this.closeDescription();
    this.calendarApi.prevYear(); // call a method on the Calendar object
    this.setCalendar();
  }

  public goNextYear(): void {
    this.closeDescription();
    this.calendarApi.nextYear(); // call a method on the Calendar object
    this.setCalendar();
  }

  public deliveryDescription(arg): void {
    this.isGuestReq = arg.event._def.extendedProps.isGuestRequest;
    const requestCreatorId = arg.event._def.extendedProps.creatorId;
    const loginMemberId = arg.event._def.extendedProps.guestMemberId;
    const calendarType = arg.event._def.extendedProps.requestType;
    if (calendarType !== 'calendarEvent') {
      if (this.isGuestUser && !this.isGuestReq) {
        return;
      }
      if (this.isGuestUser && this.isGuestReq && (requestCreatorId !== loginMemberId)) {
        return;
      }
    }
    this.eventData = [];
    this.allRequestIsOpened = false;
    this.calendarDescriptionPopup = false;
    this.descriptionPopup = false;
    if (Object.keys(arg).length !== 0) {
      let index;
      if (arg.event.extendedProps.uniqueNumber) {
        index = this.deliveryList.findIndex(
          (item: { id: any; uniqueNumber: any }): any => item.id === +arg.event.id && item.uniqueNumber === arg.event.extendedProps.uniqueNumber,
        );
      } else {
        index = this.deliveryList.findIndex((item): any => item.id === +arg.event.id);
      }
      this.eventData = this.deliveryList[index];
      this.calendarCurrentDeliveryIndex = index;
      if (
        this.eventData.requestType === 'deliveryRequest'
        || this.eventData.requestType === 'deliveryRequestWithCrane'
      ) {
        this.getDateDetails(arg);
      } else if (this.eventData.requestType === 'calendarEvent') {
        this.calendarDescription(arg);
      }
    }
  }

  public getDateDetails(arg) {
    this.eventData.startDate = moment(
      this.deliveryList[this.calendarCurrentDeliveryIndex].deliveryStart,
    ).format('MM/DD/YYYY');
    if (this.eventData.approved_at !== null) {
      this.eventData.approvedAt = moment(this.eventData.approved_at).format('lll');
    }
    this.eventData.startTime = moment(
      this.deliveryList[this.calendarCurrentDeliveryIndex].deliveryStart,
    ).format('hh:mm A');
    this.eventData.endTime = moment(this.eventData.deliveryEnd).format('hh:mm A');
    this.getNDR(arg);
    this.calendarDescriptionPopup = false;
    this.openIdModal(this.eventData);
  }

  public closeCalendarDescription(): void {
    this.calendarDescriptionPopup = false;
    this.descriptionPopup = false;
    this.allRequestIsOpened = false;
    this.viewEventData = '';
  }

  public calendarDescription(arg): void {
    this.calendarDescriptionPopup = false;
    this.descriptionPopup = false;
    this.viewEventData = '';
    if (Object.keys(arg).length !== 0) {
      const index = this.events.findIndex(
        (item: any): any => item.description === arg.event.title
          && item.uniqueNumber === arg.event.extendedProps.uniqueNumber,
      );
      this.viewEventData = this.deliveryList[index];
      this.occurMessage(this.viewEventData);
      this.calendarDescriptionPopup = true;
    }
  }

  public occurMessage(data): void {
    this.message = 'Occurs every day';
    if (data.repeatEveryType === 'Day') {
      this.message = '';
      this.message = 'Occurs every day';
    }
    if (data.repeatEveryType === 'Days') {
      this.message = '';
      if (+data.repeatEveryCount === 2) {
        this.message = 'Occurs every other day';
      } else {
        this.message = `Occurs every ${data.repeatEveryCount} days`;
      }
    }
    if (data.repeatEveryType === 'Week') {
      this.message = '';
      let weekDays = '';
      data.days.forEach((day1: any): any => {
        weekDays = `${weekDays + day1},`;
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message += `Occurs every ${weekDays}`;
    }
    if (data.repeatEveryType === 'Weeks') {
      let weekDays = '';
      data.days.forEach((day1: any): any => {
        weekDays = `${weekDays + day1},`;
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message = '';
      if (+data.repeatEveryCount === 2) {
        this.message = `Occurs every other  ${weekDays}`;
      } else {
        this.message = `Occurs every ${data.repeatEveryCount} weeks on ${weekDays}`;
      }
    }
    if (
      data.repeatEveryType === 'Month'
      || data.repeatEveryType === 'Months'
      || data.repeatEveryType === 'Year'
      || data.repeatEveryType === 'Years'
    ) {
      if (data.chosenDateOfMonth) {
        this.message = `Occurs on day ${data.dateOfMonth}`;
      } else {
        this.message = `Occurs on the ${data.monthlyRepeatType}`;
      }
    }
    this.message += ` until ${moment(data.endTime).format('MM-DD-YYYY')}`;
  }

  public closeDescription(): void {
    this.descriptionPopup = false;
  }

  public changeFormat(fromDate: any): any {
    if (fromDate) {
      const dayFormat = moment(new Date(fromDate)).format('ddd MM/DD/YYYY');
      return dayFormat;
    }
  }

  public openIdModal(item): void {
    const data = item;
    data.isGuestUser = this.isGuestUser;
    data.ParentCompanyId = this.ParentCompanyId;
    const initialState = {
      data,
      title: 'Modal with component',
    };
    this.modalRef = this.modalService.show(GuestDeliveryDetailsComponent, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-delivery-popup custom-modal',
      initialState,
    });

    this.modalRef.content.closeBtnName = 'Close';
  }


  public calendarGetOverAllGate(): void {
    this.modalLoader = true;
    const calendarGetGateParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices
      .guestGateList(calendarGetGateParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((calendarGetGateListResponse): void => {
        this.gateList = calendarGetGateListResponse.data;
        this.calendarGetOverAllEquipment();
      });
  }

  public calendarGetOverAllEquipment(): void {
    const calendarGetEquipmentParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices
      .guestListEquipment(calendarGetEquipmentParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((calendarGetEquipmentListParams): void => {
        this.equipmentList = calendarGetEquipmentListParams.data;
        this.calendarGetCompany();
      });
  }

  public calendarGetDefinable(): void {
    const calendarGetDefinableParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices
      .guestGetDefinableWork(calendarGetDefinableParams)
      .subscribe((calendarGetDefinableListResponse: any): void => {
        if (calendarGetDefinableListResponse) {
          const { data } = calendarGetDefinableListResponse;
          this.defineList = data;
          this.definableDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'DFOW',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
          this.getLocations();
        }
      });
  }

  public getLocations(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices.getLocations(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.locationList = data;
        this.openContentModal();
      }
    });
  }

  public close(): void {
    this.submitted = false;
    this.formSubmitted = false;
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }


  public getResponsiblePeople(object): any {
    if (object?.firstName && object?.lastName) {
      const string = `${object.firstName} ${object.lastName}`;
      const matches = string.match(/\b(\w)/g);
      const acronym = matches.join('').toUpperCase();
      return acronym;
    }
    return 'UU';
  }

  public getNDR(data): void {
    this.toolTipContent = '';
    const param = {
      DeliveryRequestId: +data.event.id,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices.guestGetNDRData(param).subscribe((res): void => {
      this.eventData.edit = true;
      this.eventData.companyDetails = res.data.companyDetails;
      this.eventData.gateDetails = res.data.gateDetails;
      this.eventData.memberDetails = res.data.memberDetails;
      this.eventData.equipmentDetails = res.data.equipmentDetails;
      if (this.eventData.memberDetails.length > 3) {
        const slicedArray = this.eventData?.memberDetails?.slice(3) || [];
        slicedArray.map((a): any => {
          if (a.Member.User.firstName) {
            this.toolTipContent += `${a.Member.User.firstName} ${a.Member.User.lastName}, `;
          } else {
            this.toolTipContent += `${a.Member.User.email}, `;
          }
        });
      }
    });
  }

  public openEditModal(item, action): void {
    this.closeDescription();
    if (this.modalRef) {
      this.close();
    }
    this.deliveryService.updatedDeliveryId(item.id);
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(EditDeliveryFormComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
    });
    this.modalRef.content.closeBtnName = 'Close';
    this.modalRef.content.seriesOption = item?.recurrence && item?.recurrence?.recurrence !== 'Does Not Repeat' ? action : 1;
    this.modalRef.content.recurrenceId = item?.recurrence ? item?.recurrence?.id : null;
    this.modalRef.content.recurrenceEndDate = item?.recurrence
      ? item?.recurrence?.recurrenceEndDate
      : null;
  }

  public openContentModal(): void {
    this.modalLoader = false;
  }

  public openFilterModal(template: TemplateRef<any>): void {
    this.calendarGetOverAllGate();
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-sm filter-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }
}
