<section class="page-section plan-profile-filter ">
    <div class="settings-content edit-profile radius5 py-4">
<div class="settings-tab bg-white">
    <div class="row align-items-center profile-content mx-4">
            <div class="upload-profile mb-4">
                <div class="member profile">
                  <img src="./assets/images/default-user.svg"  class="profile-img rounded-circle" alt="Profile" *ngIf="userData.User?.profilePic=='' || userData.User?.profilePic==null">
                        <a href={{userData.User?.profilePic}}><img src={{userData.User?.profilePic}} class="profile-img" alt="Profile"  *ngIf="userData.User?.profilePic!='' && userData.User?.profilePic!=null"></a>
                    <div class="image-upload profile-upload">
                        <label for="file-input">
                            <img src="./assets/images/black_camera_icon.svg" class="camera-icon"  alt="Profile">
                        </label>
                        <input id="file-input" type="file" (change)="fileChangeEvent($event.target.files)"/>
                    </div>
                </div>
             </div>
        <div class="ms-3 profile-name">
            <h1 class="fs18 font-weight-medium">{{userData?.User?.firstName}} {{userData?.User?.lastName}}</h1>
            <p class="font-weight-medium fs18 color-grey27">{{userData?.Role?.roleName}}</p>
        </div>
    </div>
    <div class="profile-tab mx-4 mt-2">
        <tabset *ngIf="subscriptionStatus">
            <tab heading="Profile Management" [active]="currentTabId === 0">
                <app-overview></app-overview>
            </tab>
            <tab heading="Plans and Projects" *ngIf="userData.RoleId === 2" active=true (selectTab)="change()">
            <app-plans-and-project></app-plans-and-project>
            </tab>
            <tab heading="Change Password" (selectTab)="change()" [active]="currentTabId === 2">
              <app-change-password></app-change-password>
            </tab>
        </tabset>
        <tabset *ngIf="!subscriptionStatus">
          <tab heading="Profile Management" [active]="currentTabId === 0">
              <app-overview></app-overview>
          </tab>
          <tab heading="Plans and Projects" *ngIf="userData.RoleId == 2 && !isAccount" [active]="currentTabId === 1">
          <app-plans-and-project></app-plans-and-project>
          </tab>
          <tab heading="Change Password" (selectTab)="change()" [active]="currentTabId === 2">
            <app-change-password></app-change-password>
          </tab>                
      </tabset>
    </div>

    </div>
    </div>
</section>

<div id="filter-temp7">
<!--Filter Modal-->
<ng-template #filter>
    <div class="modal-header border-0 pb-0">
      <h4 class="fs14 fw-bold cairo-regular color-text7 my-0">Filter</h4>
      <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close"></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="filter-content">
        <form class="custom-material-form">
          <div class="row">
            <div class="col-md-12">
              <div class="input-group mb-3">
                <input type="text" class="form-control fs12   material-input" placeholder="Project Name">
                 <span class="input-group-text">
                    <img src="./assets/images/search-icon.svg" alt="Search">
                  </span>
              </div>
              <div class="form-group">
                <select class="form-control fs12   material-input">
                  <option value=""> Subscription Plan</option>
                </select>
              </div>
              <div class="input-group mb-3">
                <input type="text" class="form-control fs12   material-input" placeholder="Subscribed On">
                  <span class="input-group-text">
                    <img src="./assets/images/search-icon.svg" alt="Search">
                  </span>
              </div>
              <div class="input-group mb-3">
                <input type="text" class="form-control fs12   material-input" placeholder="Auto Renewal On">
                  <span class="input-group-text">
                    <img src="./assets/images/search-icon.svg" alt="Search">
                  </span>
              </div>
              <button
                class="btn btn-orange color-orange radius20 col-md-8 mx-auto mt-2 fs12 fw-bold cairo-regular w-100 px-2rem"
                type="submit">Apply</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
  <!--Filter Modal-->
 </div> 

