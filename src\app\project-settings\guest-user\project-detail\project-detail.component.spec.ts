import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ProjectDetailComponent } from './project-detail.component';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { BsModalService } from 'ngx-bootstrap/modal';
import { ProjectService } from '../../../services/profile/project.service';
import { ProjectSettingsService, ProjectSharingService } from '../../../services';
import { of, throwError } from 'rxjs';

// Mock Google Maps API
const mockGoogleMaps = {
  maps: {
    MapTypeId: {
      SATELLITE: 'satellite',
      ROADMAP: 'roadmap'
    },
    Animation: {
      DROP: 'DROP',
      BOUNCE: 'BOUNCE'
    },
    Geocoder: class {
      geocode = jest.fn().mockImplementation((request, callback) => {
        callback([{ formatted_address: 'Test Address' }], 'OK');
      });
    }
  }
};

// Add Google Maps to window object
(window as any).google = mockGoogleMaps;

describe('ProjectDetailComponent', () => {
  let component: ProjectDetailComponent;
  let fixture: ComponentFixture<ProjectDetailComponent>;
  let router: jest.Mocked<Router>;
  let toastr: jest.Mocked<ToastrService>;
  let modalService: jest.Mocked<BsModalService>;
  let projectService: jest.Mocked<ProjectService>;
  let projectSettingsService: jest.Mocked<ProjectSettingsService>;
  let projectSharingService: jest.Mocked<ProjectSharingService>;

  const mockProjectData = {
    data: {
      projectList: {
        id: 1,
        ParentCompanyId: 2,
        projectLocationLatitude: '38.897957',
        projectLocationLongitude: '-77.036560',
        ProjectSettings: {
          allowGuestToAddDeliveryBooking: true,
          allowGuestToAddCraneBooking: true,
          allowGuestToAddConcreteBooking: true,
          allowGuestToViewDeliveryCalendar: true,
          allowGuestToViewCraneCalendar: true,
          allowGuestToViewConcreteCalendar: true,
          autoRefreshRateInMinutes: '5',
          projectLogisticPlanUrl: 'http://example.com/plan.pdf'
        }
      }
    }
  };

  beforeEach(async () => {
    const routerMock = {
      navigate: jest.fn(),
      url: '/project-settings/guest-user/abc123'
    };
    const toastrMock = {
      error: jest.fn()
    };
    const modalServiceMock = {
      show: jest.fn()
    };
    const projectServiceMock = {
      decodeProjectDetailUrl: jest.fn().mockReturnValue(of({}))
    };
    const projectSettingsServiceMock = {};
    const projectSharingServiceMock = {};

    await TestBed.configureTestingModule({
      declarations: [ ProjectDetailComponent ],
      providers: [
        { provide: Router, useValue: routerMock },
        { provide: ToastrService, useValue: toastrMock },
        { provide: BsModalService, useValue: modalServiceMock },
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: ProjectSettingsService, useValue: projectSettingsServiceMock },
        { provide: ProjectSharingService, useValue: projectSharingServiceMock }
      ]
    })
    .compileComponents();

    router = TestBed.inject(Router) as jest.Mocked<Router>;
    toastr = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    modalService = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    projectSettingsService = TestBed.inject(ProjectSettingsService) as jest.Mocked<ProjectSettingsService>;
    projectSharingService = TestBed.inject(ProjectSharingService) as jest.Mocked<ProjectSharingService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ProjectDetailComponent);
    component = fixture.componentInstance;
    component.encryptedUrl = 'abc123';
    localStorage.setItem('guestId', btoa('1'));
  });

  afterEach(() => {
    localStorage.clear();
    jest.clearAllMocks();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.mainLoader).toBeFalsy();
    expect(component.projectSharingSettings).toEqual({ isPublicWebsiteEnabled: false });
  });

  it('should get project details on initialization', () => {
    projectService.decodeProjectDetailUrl.mockReturnValue(of(mockProjectData));
    component.gettingProjectDetailsResponse();

    expect(projectService.decodeProjectDetailUrl).toHaveBeenCalled();
    expect(component.projectList).toEqual(mockProjectData.data.projectList);
    expect(component.latitude).toBe(38.897957);
    expect(component.longitude).toBe(-77.036560);
    expect(component.projectID).toBe(1);
    expect(component.ParentCompanyId).toBe(2);
  });

  it('should handle error when getting project details', () => {
    projectService.decodeProjectDetailUrl.mockReturnValue(throwError(() => new Error('API Error')));
    component.gettingProjectDetailsResponse();

    expect(toastr.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should handle keyboard navigation for calendar', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const navigateCalendarSpy = jest.spyOn(component, 'navigateCalendar');

    component.handleDownKeydown(event, 'calendar');

    expect(navigateCalendarSpy).toHaveBeenCalled();
  });

  it('should handle keyboard navigation for delivery', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const navigateDeliveryBookingSpy = jest.spyOn(component, 'navigateDeliveryBooking');

    component.handleDownKeydown(event, 'delivery');

    expect(navigateDeliveryBookingSpy).toHaveBeenCalled();
  });

  it('should navigate to delivery calendar when viewDeliveryCalendar is true', () => {
    component.viewDeliveryCalendar = true;
    component.navigateCalendar();

    expect(router.navigate).toHaveBeenCalledWith(['/guest-delivery-calendar']);
  });

  it('should navigate to enter-details when guestId is not set', () => {
    localStorage.removeItem('guestId');
    component.viewDeliveryCalendar = true;
    component.navigateCalendar();

    expect(router.navigate).toHaveBeenCalledWith(['/enter-details'], {
      queryParams: { type: 'deliveryCalendar' }
    });
  });

  it('should update map location when projectLocation is called', () => {
    // Mock navigator.geolocation
    Object.defineProperty(navigator, 'geolocation', {
      value: {
        getCurrentPosition: jest.fn()
      },
      configurable: true
    });

    const latitude = '38.897957';
    const longitude = '-77.036560';

    component.projectLocation(latitude, longitude);

    expect(component.latitude).toBe(38.897957);
    expect(component.longitude).toBe(-77.036560);
    expect(component.zoom).toBe(18);
    expect(component.mapOptions.center).toEqual({
      lat: 38.897957,
      lng: -77.036560
    });
  });

  it('should open project logistic plan in new window', () => {
    component.projectSharingSettings = {
      projectLogisticPlanUrl: 'http://example.com/plan.pdf'
    };
    const windowOpenSpy = jest.spyOn(window, 'open').mockImplementation(() => null);

    component.downloadFile();

    expect(windowOpenSpy).toHaveBeenCalledWith('http://example.com/plan.pdf', '_blank');
  });

  // ==================== ENHANCED TEST CASES ====================

  describe('Enhanced Keyboard Navigation', () => {
    it('should handle keyboard navigation for crane booking', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      const preventDefaultSpy = jest.spyOn(event, 'preventDefault');
      const navigateCraneBookingSpy = jest.spyOn(component, 'navigateCraneBooking');

      component.handleDownKeydown(event, 'crane');

      expect(navigateCraneBookingSpy).toHaveBeenCalled();
      expect(preventDefaultSpy).toHaveBeenCalled();
    });

    it('should handle keyboard navigation for concrete booking', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      const preventDefaultSpy = jest.spyOn(event, 'preventDefault');
      const navigateConcreteBookingSpy = jest.spyOn(component, 'navigateConcreteBooking');

      component.handleDownKeydown(event, 'concrete');

      expect(navigateConcreteBookingSpy).toHaveBeenCalled();
      expect(preventDefaultSpy).toHaveBeenCalled();
    });

    it('should handle space key navigation', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      const preventDefaultSpy = jest.spyOn(event, 'preventDefault');
      const navigateCalendarSpy = jest.spyOn(component, 'navigateCalendar');

      component.handleDownKeydown(event, 'calendar');

      expect(navigateCalendarSpy).toHaveBeenCalled();
      expect(preventDefaultSpy).toHaveBeenCalled();
    });

    it('should not handle invalid keyboard events', () => {
      const event = new KeyboardEvent('keydown', { key: 'Tab' });
      const preventDefaultSpy = jest.spyOn(event, 'preventDefault');
      const navigateCalendarSpy = jest.spyOn(component, 'navigateCalendar');

      component.handleDownKeydown(event, 'calendar');

      expect(navigateCalendarSpy).not.toHaveBeenCalled();
      expect(preventDefaultSpy).not.toHaveBeenCalled();
    });

    it('should handle unknown navigation type', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      const navigateCalendarSpy = jest.spyOn(component, 'navigateCalendar');

      component.handleDownKeydown(event, 'unknown');

      expect(navigateCalendarSpy).not.toHaveBeenCalled();
    });
  });

  describe('Navigation Methods - Positive Cases', () => {
    beforeEach(() => {
      localStorage.setItem('guestId', btoa('1'));
    });

    it('should navigate to crane calendar when viewCraneCalendar is true and guestId exists', () => {
      component.viewCraneCalendar = true;
      component.navigateCalendar();

      expect(router.navigate).toHaveBeenCalledWith(['/guest-crane-calendar']);
    });

    it('should navigate to concrete calendar when viewConcreteCalendar is true and guestId exists', () => {
      component.viewConcreteCalendar = true;
      component.navigateCalendar();

      expect(router.navigate).toHaveBeenCalledWith(['/guest-concrete-calendar']);
    });

    it('should navigate to guest delivery booking when guestId exists', () => {
      component.navigateDeliveryBooking();

      expect(router.navigate).toHaveBeenCalledWith(['/guest-delivery-booking']);
    });

    it('should navigate to guest crane booking when guestId exists', () => {
      component.navigateCraneBooking();

      expect(router.navigate).toHaveBeenCalledWith(['/guest-crane-booking']);
    });

    it('should navigate to guest concrete booking when guestId exists', () => {
      component.navigateConcreteBooking();

      expect(router.navigate).toHaveBeenCalledWith(['/guest-concrete-booking']);
    });
  });

  describe('Navigation Methods - Negative Cases', () => {
    beforeEach(() => {
      localStorage.removeItem('guestId');
    });

    it('should navigate to enter-details for crane calendar when guestId does not exist', () => {
      component.viewCraneCalendar = true;
      component.navigateCalendar();

      expect(router.navigate).toHaveBeenCalledWith(['/enter-details'], {
        queryParams: { type: 'craneCalendar' }
      });
    });

    it('should navigate to enter-details for concrete calendar when guestId does not exist', () => {
      component.viewConcreteCalendar = true;
      component.navigateCalendar();

      expect(router.navigate).toHaveBeenCalledWith(['/enter-details'], {
        queryParams: { type: 'concreteCalendar' }
      });
    });

    it('should navigate to enter-details for delivery booking when guestId does not exist', () => {
      component.navigateDeliveryBooking();

      expect(router.navigate).toHaveBeenCalledWith(['/enter-details'], {
        queryParams: { type: 'deliveryBooking' }
      });
    });

    it('should navigate to enter-details for crane booking when guestId does not exist', () => {
      component.navigateCraneBooking();

      expect(router.navigate).toHaveBeenCalledWith(['/enter-details'], {
        queryParams: { type: 'craneBooking' }
      });
    });

    it('should navigate to enter-details for concrete booking when guestId does not exist', () => {
      component.navigateConcreteBooking();

      expect(router.navigate).toHaveBeenCalledWith(['/enter-details'], {
        queryParams: { type: 'concreteBooking' }
      });
    });

    it('should not navigate when no calendar permissions are enabled', () => {
      component.viewDeliveryCalendar = false;
      component.viewCraneCalendar = false;
      component.viewConcreteCalendar = false;

      component.navigateCalendar();

      expect(router.navigate).not.toHaveBeenCalled();
    });
  });

  describe('Project Details Response - Enhanced Cases', () => {

    it('should handle project details with null data', () => {
      const mockNullData = { data: null };

      projectService.decodeProjectDetailUrl.mockReturnValue(of(mockNullData));
      component.gettingProjectDetailsResponse();

      expect(component.mainLoader).toBe(false);
      expect(component.projectList).toBeUndefined();
    });

    it('should handle project details with empty response', () => {
      projectService.decodeProjectDetailUrl.mockReturnValue(of({}));
      component.gettingProjectDetailsResponse();

      expect(component.mainLoader).toBe(false);
      expect(component.projectList).toBeUndefined();
    });

    it('should set permission flags correctly from project settings', () => {
      const mockDataWithPermissions = {
        data: {
          projectList: {
            id: 1,
            ParentCompanyId: 2,
            projectLocationLatitude: '38.897957',
            projectLocationLongitude: '-77.036560',
            ProjectSettings: {
              allowGuestToAddDeliveryBooking: false,
              allowGuestToAddCraneBooking: true,
              allowGuestToAddConcreteBooking: false,
              allowGuestToViewDeliveryCalendar: true,
              allowGuestToViewCraneCalendar: false,
              allowGuestToViewConcreteCalendar: true,
              autoRefreshRateInMinutes: '10'
            }
          }
        }
      };

      projectService.decodeProjectDetailUrl.mockReturnValue(of(mockDataWithPermissions));
      component.gettingProjectDetailsResponse();

      expect(component.addDeliveryBooking).toBe(false);
      expect(component.addCraneBooking).toBe(true);
      expect(component.addConcreteBooking).toBe(false);
      expect(component.viewDeliveryCalendar).toBe(true);
      expect(component.viewCraneCalendar).toBe(false);
      expect(component.viewConcreteCalendar).toBe(true);
      expect(component.refreshRate).toBe(10);
    });

    it('should set localStorage values correctly', () => {
      projectService.decodeProjectDetailUrl.mockReturnValue(of(mockProjectData));
      component.gettingProjectDetailsResponse();

      expect(localStorage.getItem('guestProjectId')).toBe(btoa('1'));
      expect(localStorage.getItem('guestParentCompanyId')).toBe(btoa('2'));
    });

    it('should handle invalid coordinates gracefully', () => {
      const mockDataWithInvalidCoords = {
        data: {
          projectList: {
            id: 1,
            ParentCompanyId: 2,
            projectLocationLatitude: 'invalid',
            projectLocationLongitude: 'invalid',
            ProjectSettings: {
              allowGuestToAddDeliveryBooking: true,
              autoRefreshRateInMinutes: '5'
            }
          }
        }
      };

      projectService.decodeProjectDetailUrl.mockReturnValue(of(mockDataWithInvalidCoords));
      component.gettingProjectDetailsResponse();

      expect(component.latitude).toBeNaN();
      expect(component.longitude).toBeNaN();
      expect(component.zoom).toBe(18);
    });

    it('should handle network timeout error', () => {
      const timeoutError = new Error('Timeout');
      timeoutError.name = 'TimeoutError';

      projectService.decodeProjectDetailUrl.mockReturnValue(throwError(() => timeoutError));
      component.gettingProjectDetailsResponse();

      expect(toastr.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
      expect(component.mainLoader).toBe(false);
    });

    it('should handle 404 error', () => {
      const notFoundError = { status: 404, message: 'Not Found' };

      projectService.decodeProjectDetailUrl.mockReturnValue(throwError(() => notFoundError));
      component.gettingProjectDetailsResponse();

      expect(toastr.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });
  });

  describe('Permission Logic Tests', () => {
    beforeEach(() => {
      component.mainLoader = false;
    });

    it('should set craneBookTop to false when delivery booking and calendar views are enabled', () => {
      component.addDeliveryBooking = true;
      component.viewDeliveryCalendar = true;
      component.viewCraneCalendar = false;
      component.viewConcreteCalendar = false;

      projectService.decodeProjectDetailUrl.mockReturnValue(of(mockProjectData));
      component.gettingProjectDetailsResponse();

      expect(component.craneBookTop).toBe(false);
    });

    it('should set craneBookTop to true when no delivery booking or calendar views', () => {
      const mockDataNoPermissions = {
        data: {
          projectList: {
            id: 1,
            ParentCompanyId: 2,
            projectLocationLatitude: '38.897957',
            projectLocationLongitude: '-77.036560',
            ProjectSettings: {
              allowGuestToAddDeliveryBooking: false,
              allowGuestToViewDeliveryCalendar: false,
              allowGuestToViewCraneCalendar: false,
              allowGuestToViewConcreteCalendar: false,
              autoRefreshRateInMinutes: '5'
            }
          }
        }
      };

      projectService.decodeProjectDetailUrl.mockReturnValue(of(mockDataNoPermissions));
      component.gettingProjectDetailsResponse();

      expect(component.craneBookTop).toBe(true);
    });

    it('should set concreteBookTop to false when delivery and crane booking are enabled', () => {
      const mockDataBothBookings = {
        data: {
          projectList: {
            id: 1,
            ParentCompanyId: 2,
            projectLocationLatitude: '38.897957',
            projectLocationLongitude: '-77.036560',
            ProjectSettings: {
              allowGuestToAddDeliveryBooking: true,
              allowGuestToAddCraneBooking: true,
              allowGuestToViewDeliveryCalendar: true,
              autoRefreshRateInMinutes: '5'
            }
          }
        }
      };

      projectService.decodeProjectDetailUrl.mockReturnValue(of(mockDataBothBookings));
      component.gettingProjectDetailsResponse();

      expect(component.concreteBookTop).toBe(false);
    });

    it('should set concreteBookTop to true when no bookings are enabled', () => {
      const mockDataNoBookings = {
        data: {
          projectList: {
            id: 1,
            ParentCompanyId: 2,
            projectLocationLatitude: '38.897957',
            projectLocationLongitude: '-77.036560',
            ProjectSettings: {
              allowGuestToAddDeliveryBooking: false,
              allowGuestToAddCraneBooking: false,
              allowGuestToViewDeliveryCalendar: false,
              allowGuestToViewCraneCalendar: false,
              allowGuestToViewConcreteCalendar: false,
              autoRefreshRateInMinutes: '5'
            }
          }
        }
      };

      projectService.decodeProjectDetailUrl.mockReturnValue(of(mockDataNoBookings));
      component.gettingProjectDetailsResponse();

      expect(component.concreteBookTop).toBe(true);
    });
  });

  describe('Map Functionality Tests', () => {
    it('should initialize geocoder and call projectLocation', () => {
      const projectLocationSpy = jest.spyOn(component, 'projectLocation');
      const latitude = 40.7128;
      const longitude = -74.0060;

      component.mapForProjectLocationLoader(latitude, longitude);

      expect(component.geoCoder).toBeDefined();
      expect(projectLocationSpy).toHaveBeenCalledWith(latitude, longitude);
    });

    it('should handle projectLocation with string coordinates', () => {
      Object.defineProperty(navigator, 'geolocation', {
        value: { getCurrentPosition: jest.fn() },
        configurable: true
      });

      const latitude = '40.7128';
      const longitude = '-74.0060';

      component.projectLocation(latitude, longitude);

      expect(component.latitude).toBe(40.7128);
      expect(component.longitude).toBe(-74.0060);
      expect(component.zoom).toBe(18);
      expect(component.mapOptions.center).toEqual({
        lat: 40.7128,
        lng: -74.0060
      });
      expect(component.marker.position).toEqual({
        lat: 40.7128,
        lng: -74.0060
      });
      expect(component.mapOptions.zoom).toBe(18);
    });

    it('should handle projectLocation when geolocation is not available', () => {
      Object.defineProperty(navigator, 'geolocation', {
        value: undefined,
        configurable: true
      });

      const latitude = '40.7128';
      const longitude = '-74.0060';

      component.projectLocation(latitude, longitude);

      // Should still set values even without geolocation
      expect(component.latitude).toBe(40.7128);
      expect(component.longitude).toBe(-74.0060);
    });

    it('should handle invalid coordinate strings', () => {
      Object.defineProperty(navigator, 'geolocation', {
        value: { getCurrentPosition: jest.fn() },
        configurable: true
      });

      component.projectLocation('invalid', 'invalid');

      expect(component.latitude).toBeNaN();
      expect(component.longitude).toBeNaN();
    });
  });

  describe('Page Refresh Functionality', () => {
    beforeEach(() => {
      jest.useFakeTimers();
      jest.spyOn(window, 'setInterval');
      Object.defineProperty(window, 'location', {
        value: { reload: jest.fn() },
        writable: true
      });
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('should setup page refresh with correct interval', () => {
      component.refreshRate = 5;
      component.setupPageRefresh();

      expect(setInterval).toHaveBeenCalledWith(expect.any(Function), 300000); // 5 * 60 * 1000
    });

    it('should reload page when interval triggers', () => {
      component.refreshRate = 1;
      component.setupPageRefresh();

      jest.advanceTimersByTime(60000); // 1 minute

      expect(window.location.reload).toHaveBeenCalled();
    });

    it('should handle zero refresh rate', () => {
      component.refreshRate = 0;
      component.setupPageRefresh();

      expect(setInterval).toHaveBeenCalledWith(expect.any(Function), 0);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle downloadFile when projectSharingSettings is null', () => {
      component.projectSharingSettings = null;
      const windowOpenSpy = jest.spyOn(window, 'open').mockImplementation(() => null);

      expect(() => component.downloadFile()).toThrow();
      expect(windowOpenSpy).not.toHaveBeenCalled();
    });

    it('should handle downloadFile when projectLogisticPlanUrl is undefined', () => {
      component.projectSharingSettings = {};
      const windowOpenSpy = jest.spyOn(window, 'open').mockImplementation(() => null);

      component.downloadFile();

      expect(windowOpenSpy).toHaveBeenCalledWith(undefined, '_blank');
    });

    it('should handle guestId with invalid base64', () => {
      localStorage.setItem('guestId', 'invalid-base64');

      expect(() => {
        component.navigateDeliveryBooking();
      }).not.toThrow();

      expect(router.navigate).toHaveBeenCalledWith(['/enter-details'], {
        queryParams: { type: 'deliveryBooking' }
      });
    });

    it('should handle missing localStorage guestId gracefully', () => {
      localStorage.removeItem('guestId');

      component.navigateDeliveryBooking();

      expect(router.navigate).toHaveBeenCalledWith(['/enter-details'], {
        queryParams: { type: 'deliveryBooking' }
      });
    });
  });

  describe('Integration Tests', () => {
    it('should complete full initialization flow successfully', () => {
      const setupPageRefreshSpy = jest.spyOn(component, 'setupPageRefresh');
      const mapForProjectLocationLoaderSpy = jest.spyOn(component, 'mapForProjectLocationLoader');

      projectService.decodeProjectDetailUrl.mockReturnValue(of(mockProjectData));
      component.gettingProjectDetailsResponse();

      expect(projectService.decodeProjectDetailUrl).toHaveBeenCalledWith({
        encodeUrl: 'abc123'
      });
      expect(component.mainLoader).toBe(false);
      expect(mapForProjectLocationLoaderSpy).toHaveBeenCalledWith(38.897957, -77.036560);
      expect(setupPageRefreshSpy).toHaveBeenCalled();
      expect(localStorage.getItem('guestProjectId')).toBe(btoa('1'));
      expect(localStorage.getItem('guestParentCompanyId')).toBe(btoa('2'));
    });

    it('should handle complete error flow', () => {
      const error = new Error('Network Error');
      projectService.decodeProjectDetailUrl.mockReturnValue(throwError(() => error));

      component.gettingProjectDetailsResponse();

      expect(component.mainLoader).toBe(false);
      expect(toastr.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
      expect(component.projectList).toBeUndefined();
    });
  });

  describe('Accessibility and User Experience', () => {
    it('should prevent default behavior for Enter key navigation', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      const preventDefaultSpy = jest.spyOn(event, 'preventDefault');

      component.handleDownKeydown(event, 'calendar');

      expect(preventDefaultSpy).toHaveBeenCalled();
    });

    it('should prevent default behavior for Space key navigation', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      const preventDefaultSpy = jest.spyOn(event, 'preventDefault');

      component.handleDownKeydown(event, 'delivery');

      expect(preventDefaultSpy).toHaveBeenCalled();
    });

    it('should not prevent default for non-navigation keys', () => {
      const event = new KeyboardEvent('keydown', { key: 'Escape' });
      const preventDefaultSpy = jest.spyOn(event, 'preventDefault');

      component.handleDownKeydown(event, 'calendar');

      expect(preventDefaultSpy).not.toHaveBeenCalled();
    });
  });
});
