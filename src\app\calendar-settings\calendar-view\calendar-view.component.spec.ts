import { ComponentFixture } from '@angular/core/testing';
import { CalendarViewComponent } from './calendar-view.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Title } from '@angular/platform-browser';
import { CalendarService } from '../../services/profile/calendar.service';
import { ProjectService } from '../../services/profile/project.service';
import { FullCalendarComponent } from '@fullcalendar/angular';
import { EventMountArg } from '@fullcalendar/core';
import * as moment from 'moment';
import { of, BehaviorSubject } from 'rxjs';

jest.mock('@fullcalendar/angular');
jest.mock('ngx-bootstrap/modal');
jest.mock('ngx-toastr');
jest.mock('@angular/platform-browser');
jest.mock('../../services/profile/calendar.service');
jest.mock('../../services/profile/project.service');
jest.mock('moment', () => {
  const originalMoment = jest.requireActual('moment');
  return (date) => {
    return originalMoment(date);
  };
});

describe('CalendarViewComponent', () => {
  let component: CalendarViewComponent;
  let fixture: ComponentFixture<CalendarViewComponent>;
  let modalService: jest.Mocked<BsModalService>;
  let calendarService: jest.Mocked<CalendarService>;
  let projectService: jest.Mocked<ProjectService>;
  let toastr: jest.Mocked<ToastrService>;
  let titleService: jest.Mocked<Title>;
  let mockModalRef: Partial<BsModalRef>;
  let mockCalendarApi: any;

  beforeEach(() => {
    mockModalRef = {
      content: {},
      hide: jest.fn()
    };

    modalService = {
      show: jest.fn().mockReturnValue(mockModalRef),
      hide: jest.fn(),
    } as unknown as jest.Mocked<BsModalService>;

    calendarService = {
      getCalendarEventData: new BehaviorSubject(null),
      getCalendarEvents: jest.fn().mockReturnValue(of({})),
      getCalendarMonthEvents: jest.fn().mockReturnValue(of({})),
    } as unknown as jest.Mocked<CalendarService>;

    projectService = {
      projectParent: new BehaviorSubject(null),
      deleteCalendarEvent: jest.fn().mockReturnValue(of({})),
    } as unknown as jest.Mocked<ProjectService>;

    toastr = {
      error: jest.fn(),
      success: jest.fn(),
      warning: jest.fn(),
      info: jest.fn(),
    } as unknown as jest.Mocked<ToastrService>;

    titleService = {
      setTitle: jest.fn(),
    } as unknown as jest.Mocked<Title>;

    // Create fresh mock functions for each test
    mockCalendarApi = {
      prevYear: jest.fn(),
      nextYear: jest.fn(),
      prev: jest.fn(),
      next: jest.fn(),
      changeView: jest.fn(),
      removeAllEventSources: jest.fn(),
      addEventSource: jest.fn(),
      currentData: {
        dateProfile: {
          activeRange: {
            start: new Date('2023-01-01'),
            end: new Date('2023-01-31')
          }
        }
      }
    };

    component = new CalendarViewComponent(
      modalService,
      calendarService,
      projectService,
      toastr,
      titleService
    );

    // Mock calendarComponent1 and its getApi method
    component.calendarComponent1 = {
      getApi: jest.fn().mockReturnValue(mockCalendarApi)
    } as unknown as FullCalendarComponent;

    // Initialize calendarApi
    component.setCalendar();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Calendar Navigation', () => {
    it('should navigate to previous year', () => {
      component.goPrevYear();
      expect(component.calendarApi.prevYear).toHaveBeenCalled();
    });

    it('should navigate to next year', () => {
      component.goNextYear();
      expect(component.calendarApi.nextYear).toHaveBeenCalled();
    });

    it('should navigate to previous month', () => {
      component.goPrev();
      expect(component.calendarApi.prev).toHaveBeenCalled();
    });

    it('should navigate to next month', () => {
      component.goNext();
      expect(component.calendarApi.next).toHaveBeenCalled();
    });

    it('should change to week view', async () => {
      jest.useFakeTimers();
      component.goTimeGridWeekOrDay('timeGridWeek');
      jest.advanceTimersByTime(1000);
      expect(component.calendarApi.changeView).toHaveBeenCalledWith('timeGridWeek');
      jest.useRealTimers();
    });

    it('should change to day view', async () => {
      jest.useFakeTimers();
      component.goTimeGridWeekOrDay('timeGridDay');
      jest.advanceTimersByTime(1000);
      expect(component.calendarApi.changeView).toHaveBeenCalledWith('timeGridDay');
      jest.useRealTimers();
    });

    it('should change to month view', () => {
      component.goDayGridMonth();
      expect(component.calendarApi.changeView).toHaveBeenCalledWith('dayGridMonth');
    });

    it('should handle multiple view changes in sequence', () => {
      // Create a separate mock for this test to track multiple calls
      const sequenceMock = jest.fn();
      component.calendarApi.changeView = sequenceMock;

      component.goDayGridMonth();
      expect(sequenceMock).toHaveBeenCalledWith('dayGridMonth');
      expect(component.currentView).toBe('Month');

      // Reset the mock between calls to avoid interference
      sequenceMock.mockClear();

      jest.useFakeTimers();
      component.goTimeGridWeekOrDay('timeGridWeek');
      jest.advanceTimersByTime(1000);
      expect(sequenceMock).toHaveBeenCalledWith('timeGridWeek');
      expect(component.currentView).toBe('Week');
      jest.useRealTimers();
    });
  });

  describe('Event Handling', () => {
    it('should open add event modal', () => {
      const mockDateArg = { dateStr: '2024-03-20' };
      const mockModalRef = { content: { closeBtnName: '' } };

      modalService.show = jest.fn().mockReturnValue(mockModalRef);

      component.openAddEventModal(mockDateArg);

      expect(modalService.show).toHaveBeenCalled();
      expect(mockModalRef.content.closeBtnName).toBe('Close');
    });

    it('should handle keyboard events', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() };
      const mockData = { id: '123' };
      const mockItem = {};

      component.closeDescription = jest.fn();
      component.openDeleteModal = jest.fn();
      component.openEditModal = jest.fn();
      component.showPopover = jest.fn();
      component.clear = jest.fn();

      component.handleDownKeydown(mockEvent as any, mockData, mockItem, 'close');
      expect(component.closeDescription).toHaveBeenCalled();

      component.handleDownKeydown(mockEvent as any, mockData, mockItem, 'delete');
      expect(component.openDeleteModal).toHaveBeenCalledWith(mockData, mockItem);

      component.handleDownKeydown(mockEvent as any, mockData, mockItem, 'edit');
      expect(component.openEditModal).toHaveBeenCalledWith(mockData);
    });

    it('should close description popup', () => {
      component.descriptionPopup = true;
      component.viewEventData = 'test data';

      component.closeDescription();

      expect(component.descriptionPopup).toBeFalsy();
      expect(component.viewEventData).toBe('');
    });

    it('should close modal if it exists', () => {
      component.modalRef = mockModalRef as BsModalRef;

      component.close();

      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should clear viewEventData on onShown', () => {
      component.viewEventData = 'test data';

      component.onShown();

      expect(component.viewEventData).toBe('');
    });
  });

  describe('Date Formatting', () => {
    it('should format date correctly', () => {
      const testDate = new Date('2023-05-15');
      const formattedDate = component.changeFormat(testDate);

      // Format should match 'ddd MM/DD/YYYY'
      expect(formattedDate).toMatch(/^[A-Za-z]{3} \d{2}\/\d{2}\/\d{4}$/);
    });

    it('should handle null date in changeFormat', () => {
      const result = component.changeFormat(null);
      expect(result).toBeUndefined();
    });
  });

  describe('Event Description', () => {
    it('should show delivery description for valid event', () => {
      const mockEvent = {
        event: {
          title: 'Test Event',
          extendedProps: {
            uniqueNumber: '123'
          }
        }
      };

      component.calendarEvents = [{
        description: 'Test Event',
        uniqueNumber: '123'
      }];

      component.occurMessage = jest.fn();
      component.deliveryDescription(mockEvent);

      expect(component.descriptionPopup).toBeTruthy();
      expect(component.viewEventData).toEqual(component.calendarEvents[0]);
      expect(component.occurMessage).toHaveBeenCalledWith(component.viewEventData);
    });

    it('should not show description for invalid event', () => {
      const mockEvent = {
        event: {
          title: 'Unknown Event',
          extendedProps: {
            uniqueNumber: '999'
          }
        }
      };

      component.calendarEvents = [{
        description: 'Test Event',
        uniqueNumber: '123'
      }];

      component.occurMessage = jest.fn();
      component.deliveryDescription(mockEvent);

      expect(component.descriptionPopup).toBeTruthy();
      expect(component.viewEventData).toBeUndefined();
    });
  });

  describe('Event Mounting', () => {
    it('should format all-day events correctly', () => {
      const titleElement = { innerHTML: '' };
      const mockInfo = {
        event: {
          _def: {
            title: 'All Day Event',
            allDay: true
          }
        },
        el: {
          querySelector: jest.fn().mockReturnValue(titleElement)
        },
        timeText: '',
        backgroundColor: '#fff',
        borderColor: '#000',
        textColor: '#000',
        view: {},
        isMirror: false,
        isStart: true,
        isEnd: true,
        isPast: false,
        isFuture: true,
        isToday: false
      } as unknown as EventMountArg;

      const eventDidMount = component.calendarOptions.eventDidMount;
      eventDidMount(mockInfo);

      expect(mockInfo.el.querySelector).toHaveBeenCalledWith('.fc-event-title');
      // Check that the HTML contains the all-day icon
      expect(titleElement.innerHTML).toContain('noun-event-alert.svg');
      expect(titleElement.innerHTML).toContain('All Day Event');
    });

    it('should format regular events correctly', () => {
      const titleElement = { innerHTML: '' };
      const mockInfo = {
        event: {
          _def: {
            title: 'Regular Event',
            allDay: false
          }
        },
        el: {
          querySelector: jest.fn().mockReturnValue(titleElement)
        },
        timeText: '',
        backgroundColor: '#fff',
        borderColor: '#000',
        textColor: '#000',
        view: {},
        isMirror: false,
        isStart: true,
        isEnd: true,
        isPast: false,
        isFuture: true,
        isToday: false
      } as unknown as EventMountArg;

      const eventDidMount = component.calendarOptions.eventDidMount;
      eventDidMount(mockInfo);

      expect(mockInfo.el.querySelector).toHaveBeenCalledWith('.fc-event-title');
      // Check that the HTML does NOT contain the all-day icon
      expect(titleElement.innerHTML).not.toContain('noun-event-alert.svg');
      expect(titleElement.innerHTML).toContain('Regular Event');
    });
  });

  describe('Calendar API and Events', () => {
    it('should refresh events when calendar is set', () => {
      component.getCalendarEvents = jest.fn();
      component.getCalendarMonthEvents = jest.fn();

      component.setCalendar();

      expect(component.getCalendarEvents).toHaveBeenCalled();
      expect(component.getCalendarMonthEvents).toHaveBeenCalled();
    });

    it('should handle calendar date click', () => {
      const mockDateInfo = { dateStr: '2023-05-15' };
      component.openAddEventModal(mockDateInfo);

      expect(modalService.show).toHaveBeenCalled();
      expect(mockModalRef.content).toHaveProperty('closeBtnName', 'Close');
    });
  });

  describe('Additional Coverage Tests', () => {
    beforeEach(() => {
      component.ProjectId = 1;
      component.ParentCompanyId = 2;
      component.Range = {
        start: new Date('2023-01-01'),
        end: new Date('2023-01-31')
      };
    });

    it('should handle getCalendarEvents successfully', () => {
      const mockResponse = {
        events: [
          {
            id: '1',
            description: 'Test Event',
            uniqueNumber: '123',
            fromDate: '2023-01-15T10:00:00Z',
            toDate: '2023-01-15T11:00:00Z',
            isAllDay: false
          },
          {
            id: '2',
            description: 'All Day Event',
            uniqueNumber: '456',
            fromDate: '2023-01-16T00:00:00Z',
            toDate: '2023-01-16T23:59:59Z',
            isAllDay: true
          }
        ]
      };

      calendarService.getCalendarEvents.mockReturnValue(of(mockResponse));

      component.getCalendarEvents();

      expect(calendarService.getCalendarEvents).toHaveBeenCalledWith({
        start: '2023-01-01',
        end: '2023-01-31',
        ProjectId: 1,
        ParentCompanyId: 2,
        search: '',
        calendarView: 'Month',
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        isDST: expect.any(Boolean)
      });

      expect(component.calendarEvents).toEqual(mockResponse.events);
      expect(component.events).toHaveLength(2);
      expect(component.events[0]).toEqual({
        title: 'Test Event',
        uniqueNumber: '123',
        start: '2023-01-15T10:00:00Z',
        end: '2023-01-15T11:00:00Z',
        id: '1',
        allDaySlot: false,
        allDay: false,
        className: ['calendar_event']
      });
      expect(component.events[1]).toEqual({
        title: 'All Day Event',
        uniqueNumber: '456',
        start: '2023-01-16T00:00:00Z',
        end: '2023-01-16T23:59:59Z',
        id: '2',
        allDaySlot: true,
        allDay: true,
        className: ['calendar_event']
      });
      expect(component.loader).toBe(false);
    });

    it('should handle getCalendarEvents when ProjectId or ParentCompanyId is missing', () => {
      component.ProjectId = null;
      component.ParentCompanyId = null;

      component.getCalendarEvents();

      expect(calendarService.getCalendarEvents).not.toHaveBeenCalled();
    });

    it('should handle getCalendarMonthEvents successfully', () => {
      component.currentView = 'Month';
      component.currentViewMonth = 'January 2023';

      const mockResponse = {
        events: [
          {
            fromDate: '2023-01-15T10:00:00Z',
            description: 'Event 1'
          },
          {
            fromDate: '2023-01-15T14:00:00Z',
            description: 'Event 2'
          },
          {
            fromDate: '2023-01-20T09:00:00Z',
            description: 'Event 3'
          }
        ]
      };

      calendarService.getCalendarMonthEvents.mockReturnValue(of(mockResponse));

      component.getCalendarMonthEvents();

      expect(calendarService.getCalendarMonthEvents).toHaveBeenCalled();
      expect(component.monthlyEventloader).toBe(false);
    });

    it('should handle getCalendarMonthEvents when not in Month view', () => {
      component.currentView = 'Week';

      component.getCalendarMonthEvents();

      expect(calendarService.getCalendarMonthEvents).not.toHaveBeenCalled();
    });

    it('should handle openDeleteModal', () => {
      const eventData = { id: 'event123' };
      const template = {} as any;

      component.openDeleteModal(eventData, template);

      expect(modalService.show).toHaveBeenCalledWith(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-md new-gate-popup custom-modal'
      });
      expect(component.deleteEventId).toBe('event123');
    });

    it('should handle resetAndClose', () => {
      component.deleteEventId = 'test123';
      component.modalRef = mockModalRef as BsModalRef;

      component.resetAndClose();

      expect(component.deleteEventId).toBe('');
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should handle deleteCalendarEvent successfully', () => {
      component.deleteEventId = 'event123';
      component.ProjectId = 1;
      component.ParentCompanyId = 2;
      component.modalRef = mockModalRef as BsModalRef;

      const mockResponse = { message: 'Event deleted successfully' };
      projectService.deleteCalendarEvent = jest.fn().mockReturnValue(of(mockResponse));
      component.getCalendarEvents = jest.fn();
      component.getCalendarMonthEvents = jest.fn();
      component.triggerFalseClick = jest.fn();

      component.deleteCalendarEvent();

      expect(component.deleteSubmitted).toBe(false); // Should be false after successful completion
      expect(component.descriptionPopup).toBe(false);
      expect(component.triggerFalseClick).toHaveBeenCalled();
      expect(projectService.deleteCalendarEvent).toHaveBeenCalledWith(
        'event123',
        { ProjectId: 1, ParentCompanyId: 2 },
        ''
      );
      expect(toastr.success).toHaveBeenCalledWith('Event deleted successfully', 'Success');
      expect(component.getCalendarEvents).toHaveBeenCalled();
      expect(component.getCalendarMonthEvents).toHaveBeenCalled();
      expect(component.deleteEventId).toBe('');
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should handle deleteCalendarEvent with 400 error', () => {
      component.deleteEventId = 'event123';
      const error = {
        message: {
          statusCode: 400,
          details: [{ error: 'Validation error' }]
        }
      };

      projectService.deleteCalendarEvent = jest.fn().mockImplementation(() => {
        throw error;
      });
      component.showError = jest.fn();

      try {
        component.deleteCalendarEvent();
      } catch (e) {
        // Expected error
        console.warn('Handled expected error:', e.message);
      }

      expect(component.deleteSubmitted).toBe(true);
    });

    it('should handle deleteCalendarEvent with network error', () => {
      component.deleteEventId = 'event123';
      const error = { message: null };

      projectService.deleteCalendarEvent = jest.fn().mockImplementation(() => {
        throw error;
      });

      try {
        component.deleteCalendarEvent();
      } catch (e) {
        // Expected error
        console.warn('Handled expected error:', e.message);
      }

      expect(component.deleteSubmitted).toBe(true);
    });

    it('should handle deleteCalendarEvent with general error', () => {
      component.deleteEventId = 'event123';
      const error = { message: 'Server error' };

      projectService.deleteCalendarEvent = jest.fn().mockImplementation(() => {
        throw error;
      });

      try {
        component.deleteCalendarEvent();
      } catch (e) {
        // Expected error
        console.warn('Handled expected error:', e.message);
      }

      expect(component.deleteSubmitted).toBe(true);
    });

    it('should handle showError', () => {
      const error = {
        message: {
          details: [{ error: 'Test error message' }]
        }
      };

      component.showError(error);

      expect(component.deleteSubmitted).toBe(false);
      expect(toastr.error).toHaveBeenCalledWith(['Test error message']);
    });

    it('should handle getSearch with data', () => {
      component.getCalendarEvents = jest.fn();
      component.getCalendarMonthEvents = jest.fn();

      component.getSearch('test search');

      expect(component.showSearchbar).toBe(true);
      expect(component.search).toBe('test search');
      expect(component.getCalendarEvents).toHaveBeenCalled();
      expect(component.getCalendarMonthEvents).toHaveBeenCalled();
    });

    it('should handle getSearch with empty data', () => {
      component.getCalendarEvents = jest.fn();
      component.getCalendarMonthEvents = jest.fn();

      component.getSearch('');

      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
      expect(component.getCalendarEvents).toHaveBeenCalled();
      expect(component.getCalendarMonthEvents).toHaveBeenCalled();
    });

    it('should handle triggerFalseClick', () => {
      const mockElement = { click: jest.fn() };
      jest.spyOn(document, 'getElementById').mockReturnValue(mockElement as any);

      component.triggerFalseClick();

      expect(document.getElementById).toHaveBeenCalledWith('myCheck');
      expect(mockElement.click).toHaveBeenCalled();
    });

    it('should handle openAddCalendarEventModal', () => {
      component.triggerFalseClick = jest.fn();

      component.openAddCalendarEventModal();

      expect(component.descriptionPopup).toBe(false);
      expect(component.triggerFalseClick).toHaveBeenCalled();
      expect(modalService.show).toHaveBeenCalled();
      expect(mockModalRef.content.closeBtnName).toBe('Close');
    });

    it('should handle openModal', () => {
      const template = {} as any;

      component.openModal(template);

      expect(modalService.show).toHaveBeenCalledWith(template, {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
      });
    });

    it('should handle openEditModal', () => {
      const data = { id: 'event123' };
      component.triggerFalseClick = jest.fn();

      component.openEditModal(data);

      expect(component.descriptionPopup).toBe(false);
      expect(component.triggerFalseClick).toHaveBeenCalled();
      expect(modalService.show).toHaveBeenCalled();
      expect(mockModalRef.content.closeBtnName).toBe('Close');
    });

    it('should handle showPopover', () => {
      const eventData = { id: 'event123', description: 'Test Event' };
      component.onShown = jest.fn();
      component.occurMessage = jest.fn();

      component.showPopover(eventData);

      expect(component.descriptionPopup).toBe(false);
      expect(component.onShown).toHaveBeenCalled();
      expect(component.viewEventData).toBe(eventData);
      expect(component.occurMessage).toHaveBeenCalledWith(eventData);
    });

    it('should handle ngAfterViewInit with valid project data', () => {
      const mockProjectData = {
        ParentCompanyId: 123,
        ProjectId: 456
      };

      projectService.projectParent.next(mockProjectData);
      component.setCalendar = jest.fn();

      component.ngAfterViewInit();

      expect(component.ParentCompanyId).toBe(123);
      expect(component.ProjectId).toBe(456);
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should handle ngAfterViewInit with undefined project data', () => {
      projectService.projectParent.next(undefined);
      component.setCalendar = jest.fn();

      component.ngAfterViewInit();

      expect(component.setCalendar).not.toHaveBeenCalled();
    });

    it('should handle ngAfterViewInit with null project data', () => {
      projectService.projectParent.next(null);
      component.setCalendar = jest.fn();

      component.ngAfterViewInit();

      expect(component.setCalendar).not.toHaveBeenCalled();
    });

    it('should handle ngAfterViewInit with empty string project data', () => {
      projectService.projectParent.next('');
      component.setCalendar = jest.fn();

      component.ngAfterViewInit();

      expect(component.setCalendar).not.toHaveBeenCalled();
    });

    it('should handle clear function', () => {
      component.showSearchbar = true;
      component.search = 'test search';
      component.getCalendarEvents = jest.fn();
      component.getCalendarMonthEvents = jest.fn();

      component.clear();

      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
      expect(component.getCalendarEvents).toHaveBeenCalled();
      expect(component.getCalendarMonthEvents).toHaveBeenCalled();
    });

    it('should handle close when modalRef is null', () => {
      component.modalRef = null;

      component.close();

      // Should not throw error
      expect(component.modalRef).toBeNull();
    });

    it('should handle keyboard events with space key', () => {
      const mockEvent = { key: ' ', preventDefault: jest.fn() };
      const mockData = { id: '123' };
      const mockItem = {};

      component.clear = jest.fn();

      component.handleDownKeydown(mockEvent as any, mockData, mockItem, 'clear');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.clear).toHaveBeenCalled();
    });

    it('should handle keyboard events with show type', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() };
      const mockData = { id: '123' };
      const mockItem = {};

      component.showPopover = jest.fn();

      component.handleDownKeydown(mockEvent as any, mockData, mockItem, 'show');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.showPopover).toHaveBeenCalledWith(mockData);
    });

    it('should handle keyboard events with non-Enter/Space key', () => {
      const mockEvent = { key: 'Tab', preventDefault: jest.fn() };
      const mockData = { id: '123' };
      const mockItem = {};

      component.clear = jest.fn();

      component.handleDownKeydown(mockEvent as any, mockData, mockItem, 'clear');

      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      expect(component.clear).not.toHaveBeenCalled();
    });

    it('should handle subscription cleanup in getCalendarEvents', () => {
      component.ProjectId = 1;
      component.ParentCompanyId = 2;

      const mockUnsubscribe = jest.fn();
      component.subscription = { unsubscribe: mockUnsubscribe };

      calendarService.getCalendarEvents.mockReturnValue(of({}));

      component.getCalendarEvents();

      expect(mockUnsubscribe).toHaveBeenCalled();
    });

    it('should handle subscription cleanup in getCalendarMonthEvents', () => {
      component.ProjectId = 1;
      component.ParentCompanyId = 2;
      component.currentView = 'Month';

      const mockUnsubscribe = jest.fn();
      component.subscription1 = { unsubscribe: mockUnsubscribe };

      calendarService.getCalendarMonthEvents.mockReturnValue(of({}));

      component.getCalendarMonthEvents();

      expect(mockUnsubscribe).toHaveBeenCalled();
    });
  });

  describe('Occur Message Tests', () => {
    it('should handle occurMessage for Day repeat type', () => {
      const data = {
        repeatEveryType: 'Day',
        endTime: '2023-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs every day until 12-31-2023');
    });

    it('should handle occurMessage for Days repeat type with count 2', () => {
      const data = {
        repeatEveryType: 'Days',
        repeatEveryCount: 2,
        endTime: '2023-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs every other day until 12-31-2023');
    });

    it('should handle occurMessage for Days repeat type with count > 2', () => {
      const data = {
        repeatEveryType: 'Days',
        repeatEveryCount: 5,
        endTime: '2023-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs every 5 days until 12-31-2023');
    });

    it('should handle occurMessage for Week repeat type', () => {
      const data = {
        repeatEveryType: 'Week',
        days: ['Monday', 'Wednesday', 'Friday'],
        endTime: '2023-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs every Monday,Wednesday,Friday until 12-31-2023');
    });

    it('should handle occurMessage for Weeks repeat type with count 2', () => {
      const data = {
        repeatEveryType: 'Weeks',
        repeatEveryCount: 2,
        days: ['Monday', 'Friday'],
        endTime: '2023-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs every other  Monday,Friday until 12-31-2023');
    });

    it('should handle occurMessage for Weeks repeat type with count > 2', () => {
      const data = {
        repeatEveryType: 'Weeks',
        repeatEveryCount: 3,
        days: ['Tuesday', 'Thursday'],
        endTime: '2023-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs every 3 weeks on Tuesday,Thursday until 12-31-2023');
    });

    it('should handle occurMessage for Month repeat type with chosen date', () => {
      const data = {
        repeatEveryType: 'Month',
        chosenDateOfMonth: true,
        dateOfMonth: 15,
        endTime: '2023-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs on day 15 until 12-31-2023');
    });

    it('should handle occurMessage for Month repeat type without chosen date', () => {
      const data = {
        repeatEveryType: 'Month',
        chosenDateOfMonth: false,
        monthlyRepeatType: 'first Monday',
        endTime: '2023-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs on the first Monday until 12-31-2023');
    });

    it('should handle occurMessage for Months repeat type', () => {
      const data = {
        repeatEveryType: 'Months',
        chosenDateOfMonth: true,
        dateOfMonth: 20,
        endTime: '2023-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs on day 20 until 12-31-2023');
    });

    it('should handle occurMessage for Year repeat type', () => {
      const data = {
        repeatEveryType: 'Year',
        chosenDateOfMonth: false,
        monthlyRepeatType: 'second Tuesday',
        endTime: '2023-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs on the second Tuesday until 12-31-2023');
    });

    it('should handle occurMessage for Years repeat type', () => {
      const data = {
        repeatEveryType: 'Years',
        chosenDateOfMonth: true,
        dateOfMonth: 25,
        endTime: '2023-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs on day 25 until 12-31-2023');
    });

    it('should handle occurMessage with default case', () => {
      const data = {
        repeatEveryType: 'Unknown',
        endTime: '2023-12-31'
      };

      component.occurMessage(data);

      expect(component.message).toBe('Occurs every day until 12-31-2023');
    });
  });
});
