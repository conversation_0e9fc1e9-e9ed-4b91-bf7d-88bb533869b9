import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ProjectAccountAdminComponent } from './project-account-admin.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Router } from '@angular/router';
import { UntypedFormBuilder } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { ProfileService } from '../services/profile/profile.service';
import { ProjectService } from '../services/profile/project.service';
import { AuthService } from '../services/auth/auth.service';
import { of, throwError, BehaviorSubject } from 'rxjs';

// Mock the ProjectComponent module
jest.mock('../project/project.component', () => ({
  ProjectComponent: class MockProjectComponent {
    closeBtnName = '';
  }
}));

describe('ProjectAccountAdminComponent', () => {
  let component: ProjectAccountAdminComponent;
  let fixture: ComponentFixture<ProjectAccountAdminComponent>;
  let modalServiceMock: jest.Mocked<BsModalService>;
  let profileServiceMock: jest.Mocked<ProfileService>;
  let projectServiceMock: jest.Mocked<ProjectService>;
  let authServiceMock: jest.Mocked<AuthService>;
  let toastrServiceMock: jest.Mocked<ToastrService>;
  let routerMock: jest.Mocked<Router>;
  let modalRefMock: Partial<BsModalRef>;
  let refreshProjectSubject: BehaviorSubject<any>;
  let projectParentSubject: BehaviorSubject<any>;
  let parentCompanyIdSubject: BehaviorSubject<any>;

  beforeEach(async () => {
    // Initialize BehaviorSubjects for observables
    refreshProjectSubject = new BehaviorSubject<any>('');
    projectParentSubject = new BehaviorSubject<any>({ ParentCompanyId: 1 });
    parentCompanyIdSubject = new BehaviorSubject<any>(1);

    const modalMock = {
      show: jest.fn().mockReturnValue({
        content: { closeBtnName: '' },
        hide: jest.fn(),
        setClass: jest.fn()
      })
    };
    modalRefMock = {
      hide: jest.fn(),
      setClass: jest.fn()
    };
    const profileMock = {
      getProjectList: jest.fn().mockReturnValue(of({
        data: {
          rows: [],
          count: 0
        }
      }))
    };
    const projectMock = {
      checkCurrentDomain: jest.fn().mockReturnValue(false),
      updateProjectParent: jest.fn(),
      setProject: jest.fn(),
      refreshProject: refreshProjectSubject.asObservable(),
      projectParent: projectParentSubject.asObservable(),
      ParentCompanyId: parentCompanyIdSubject.asObservable()
    };
    const authMock = {
      getUser: jest.fn().mockReturnValue(of({ isAccount: false }))
    };
    const toastrMock = {
      success: jest.fn(),
      error: jest.fn()
    };
    const routerMockObj = {
      navigate: jest.fn().mockResolvedValue(true)
    };

    await TestBed.configureTestingModule({
      declarations: [ ProjectAccountAdminComponent ],
      providers: [
        { provide: BsModalService, useValue: modalMock },
        { provide: BsModalRef, useValue: { hide: jest.fn(), setClass: jest.fn() } },
        { provide: UntypedFormBuilder, useValue: new UntypedFormBuilder() },
        { provide: ProfileService, useValue: profileMock },
        { provide: ProjectService, useValue: projectMock },
        { provide: AuthService, useValue: authMock },
        { provide: ToastrService, useValue: toastrMock },
        { provide: Router, useValue: routerMockObj }
      ]
    })
    .compileComponents();

    modalServiceMock = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    profileServiceMock = TestBed.inject(ProfileService) as jest.Mocked<ProfileService>;
    projectServiceMock = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    authServiceMock = TestBed.inject(AuthService) as jest.Mocked<AuthService>;
    toastrServiceMock = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    routerMock = TestBed.inject(Router) as jest.Mocked<Router>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ProjectAccountAdminComponent);
    component = fixture.componentInstance;
    // Set up modalRef mock properly
    component.modalRef = modalRefMock as BsModalRef;
    component.loader = true;
    component.ngOnInit = () => {
      component.getProjects();
    };
    fixture.detectChanges();
  });

  // ===== POSITIVE TEST SCENARIOS =====

  describe('Component Initialization', () => {
    it('should create component successfully', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with correct default values', () => {
      expect(component.pageNo).toBe(1);
      expect(component.pageSize).toBe(25);
      expect(component.serialNo).toBe(0);
      expect(component.totalCount).toBe(0);
      expect(component.currentPageNo).toBe(1);
      expect(component.loader).toBe(true);
      expect(component.myAccount).toBe(false);
      expect(component.accountAdmin).toBe(false);
      expect(component.filterCount).toBe(0);
      expect(component.search).toBe('');
      expect(component.showSearchbar).toBe(false);
      expect(component.projectList).toEqual([]);
      expect(component.userData).toEqual([]);
    });

    it('should initialize filter form with correct structure', () => {
      expect(component.filterForm).toBeDefined();
      expect(component.filterForm.get('idFilter')).toBeTruthy();
      expect(component.filterForm.get('nameFilter')).toBeTruthy();
      expect(component.filterForm.get('idFilter')?.value).toBe('');
      expect(component.filterForm.get('nameFilter')?.value).toBe('');
    });

    it('should subscribe to project service observables on initialization', () => {
      // Trigger observable emissions
      refreshProjectSubject.next('refresh');
      projectParentSubject.next({ ParentCompanyId: 123 });
      parentCompanyIdSubject.next(456);

      expect(component.ParentCompanyId).toBe(456);
    });
  });

  describe('Project Management - Positive Scenarios', () => {
    it('should successfully fetch projects with valid data', () => {
      const mockResponse = {
        data: {
          rows: [
            {
              id: 1,
              name: 'Test Project',
              projectAdminDetails: [
                { User: { firstName: 'John' } },
                { User: { firstName: 'Jane' } }
              ]
            }
          ],
          count: 1
        }
      };

      profileServiceMock.getProjectList.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'converToString');

      component.getProjects();

      expect(profileServiceMock.getProjectList).toHaveBeenCalled();
      expect(component.projectList).toEqual(mockResponse.data.rows);
      expect(component.totalCount).toBe(1);
      expect(component.loader).toBe(false);
      expect(component.converToString).toHaveBeenCalled();
    });

    it('should successfully redirect to project with valid data', () => {
      const projectData = { id: 123, ParentCompanyId: 456 };

      component.redirectProject(projectData);

      expect(projectServiceMock.updateProjectParent).toHaveBeenCalledWith({
        ProjectId: 123,
        ParentCompanyId: 456
      });
      expect(projectServiceMock.setProject).toHaveBeenCalledWith(true);
      expect(routerMock.navigate).toHaveBeenCalledWith(['/calendar']);
    });

    it('should successfully convert project admin details to string', () => {
      const mockProjectList = [
        {
          projectAdminDetails: [
            { User: { firstName: 'John' } },
            { User: { firstName: 'Jane' } },
            { User: { firstName: 'Bob' } }
          ]
        },
        {
          projectAdminDetails: [
            { User: { firstName: 'Alice' } }
          ]
        }
      ];

      component.projectList = mockProjectList;
      component.converToString();

      expect(component.projectList[0].adminData).toBe('John, Jane, Bob');
      expect(component.projectList[1].adminData).toBe('Alice');
    });
  });

  describe('Search and Filter - Positive Scenarios', () => {
    it('should handle search with valid input', () => {
      jest.spyOn(component, 'getProjects');

      component.getSearchProject('test project');

      expect(component.showSearchbar).toBe(true);
      expect(component.search).toBe('test project');
      expect(component.pageNo).toBe(1);
      expect(component.getProjects).toHaveBeenCalled();
    });

    it('should clear search successfully', () => {
      component.showSearchbar = true;
      component.search = 'test';
      component.pageNo = 3;
      jest.spyOn(component, 'getProjects');

      component.clear();

      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
      expect(component.pageNo).toBe(1);
      expect(component.getProjects).toHaveBeenCalled();
    });

    it('should submit filter with valid data', () => {
      component.filterForm.patchValue({
        idFilter: '123',
        nameFilter: 'Test Project'
      });
      jest.spyOn(component, 'getProjects');

      component.filterSubmit();

      expect(component.filterCount).toBe(2);
      expect(component.pageNo).toBe(1);
      expect(component.getProjects).toHaveBeenCalled();
      expect(modalRefMock.hide).toHaveBeenCalled();
    });

    it('should reset filter successfully', () => {
      component.filterForm.patchValue({
        idFilter: '123',
        nameFilter: 'Test Project'
      });
      component.filterCount = 2;
      component.search = 'test';
      component.pageNo = 3;
      jest.spyOn(component, 'getProjects');

      component.resetFilter();

      expect(component.filterCount).toBe(0);
      expect(component.search).toBe('');
      expect(component.pageNo).toBe(1);
      expect(component.filterForm.get('idFilter')?.value).toBe('');
      expect(component.filterForm.get('nameFilter')?.value).toBe('');
      expect(component.getProjects).toHaveBeenCalled();
      expect(modalRefMock.hide).toHaveBeenCalled();
    });
  });

  describe('Pagination - Positive Scenarios', () => {
    it('should change page size successfully', () => {
      jest.spyOn(component, 'getProjects');

      component.changePageSize(50);

      expect(component.pageSize).toBe(50);
      expect(component.pageNo).toBe(1);
      expect(component.getProjects).toHaveBeenCalled();
    });

    it('should change page number successfully', () => {
      jest.spyOn(component, 'getProjects');

      component.changePageNo(3);

      expect(component.pageNo).toBe(3);
      expect(component.getProjects).toHaveBeenCalled();
    });
  });

  describe('Modal Operations - Positive Scenarios', () => {
    it('should open add projects modal successfully', () => {
      component.addProjects();

      expect(modalServiceMock.show).toHaveBeenCalled();
      expect(component.bsModalRef.content.closeBtnName).toBe('Close');
    });

    it('should open filter modal successfully', () => {
      const mockTemplate = {} as any;

      component.openModal1(mockTemplate);

      expect(modalServiceMock.show).toHaveBeenCalledWith(
        mockTemplate,
        { backdrop: 'static', keyboard: false, class: 'modal-sm filter-popup custom-modal' }
      );
    });

    it('should open general modal successfully', () => {
      const mockTemplate = {} as any;

      component.openModal(mockTemplate);

      expect(modalServiceMock.show).toHaveBeenCalledWith(
        mockTemplate,
        { backdrop: 'static', keyboard: false, class: 'modal-md  projects-modal custom-modal' }
      );
    });
  });

  describe('Authentication and Domain Check - Positive Scenarios', () => {
    it('should set account admin status when user is account admin', () => {
      projectServiceMock.checkCurrentDomain.mockReturnValue(true);
      authServiceMock.getUser.mockReturnValue(of({ isAccount: true }));

      component.checkCurrentDomain();

      expect(component.myAccount).toBe(true);
      expect(component.accountAdmin).toBe(true);
    });

    it('should set myAccount but not accountAdmin when user is not account admin', () => {
      projectServiceMock.checkCurrentDomain.mockReturnValue(true);
      authServiceMock.getUser.mockReturnValue(of({ isAccount: false }));

      component.checkCurrentDomain();

      expect(component.myAccount).toBe(true);
      expect(component.accountAdmin).toBe(false);
    });
  });

  describe('Keyboard Event Handling - Positive Scenarios', () => {
    it('should handle Enter key for filter action', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      const mockData = {} as any;
      jest.spyOn(component, 'openModal1');

      component.handleDownKeydown(mockEvent, mockData, 'filter');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.openModal1).toHaveBeenCalledWith(mockData);
    });

    it('should handle Space key for search action', () => {
      const mockEvent = { key: ' ', preventDefault: jest.fn() } as any;
      const mockData = {} as any;
      jest.spyOn(component, 'clear');

      component.handleDownKeydown(mockEvent, mockData, 'search');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.clear).toHaveBeenCalled();
    });

    it('should handle Enter key for project action', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      const mockData = { id: 1, ParentCompanyId: 1 } as any;
      jest.spyOn(component, 'redirectProject');

      component.handleDownKeydown(mockEvent, mockData, 'project');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.redirectProject).toHaveBeenCalledWith(mockData);
    });
  });

  // ===== NEGATIVE TEST SCENARIOS =====

  describe('Project Management - Negative Scenarios', () => {
    it('should handle API error when fetching projects', () => {
      const errorResponse = new Error('API Error');
      profileServiceMock.getProjectList.mockReturnValue(throwError(errorResponse));

      component.getProjects();

      expect(profileServiceMock.getProjectList).toHaveBeenCalled();
      expect(component.loader).toBe(true); // Should remain true on error
    });

    it('should handle null response when fetching projects', () => {
      profileServiceMock.getProjectList.mockReturnValue(of(null));

      component.getProjects();

      expect(profileServiceMock.getProjectList).toHaveBeenCalled();
      // Component should handle null response gracefully
    });

    it('should handle response without data property', () => {
      profileServiceMock.getProjectList.mockReturnValue(of({}));

      component.getProjects();

      expect(profileServiceMock.getProjectList).toHaveBeenCalled();
      // Should not crash when response.data is undefined
    });

    it('should handle project redirection with missing properties', () => {
      const incompleteProjectData = { id: 123 }; // Missing ParentCompanyId

      component.redirectProject(incompleteProjectData);

      expect(projectServiceMock.updateProjectParent).toHaveBeenCalledWith({
        ProjectId: 123,
        ParentCompanyId: undefined
      });
      expect(projectServiceMock.setProject).toHaveBeenCalledWith(true);
      expect(routerMock.navigate).toHaveBeenCalledWith(['/calendar']);
    });

    it('should handle converToString with missing User data', () => {
      const mockProjectListWithMissingData = [
        {
          projectAdminDetails: [
            { User: { firstName: 'John' } },
            { User: null }, // Missing User
            { User: { firstName: 'Jane' } },
            {} // Missing User property entirely
          ]
        }
      ];

      component.projectList = mockProjectListWithMissingData;

      expect(() => component.converToString()).not.toThrow();
      // Should handle missing data gracefully
    });

    it('should handle converToString with empty projectAdminDetails', () => {
      const mockProjectListEmpty = [
        { projectAdminDetails: [] },
        { projectAdminDetails: null },
        {} // Missing projectAdminDetails
      ];

      component.projectList = mockProjectListEmpty;

      expect(() => component.converToString()).not.toThrow();
    });
  });

  describe('Search and Filter - Negative Scenarios', () => {
    it('should handle empty search input', () => {
      jest.spyOn(component, 'getProjects');

      component.getSearchProject('');

      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
      expect(component.pageNo).toBe(1);
      expect(component.getProjects).toHaveBeenCalled();
    });

    it('should handle filter submission with empty values', () => {
      component.filterForm.patchValue({
        idFilter: '',
        nameFilter: ''
      });
      jest.spyOn(component, 'getProjects');

      component.filterSubmit();

      expect(component.filterCount).toBe(0);
      expect(component.getProjects).toHaveBeenCalled();
    });

    it('should handle filter submission with only one field filled', () => {
      component.filterForm.patchValue({
        idFilter: '123',
        nameFilter: ''
      });
      jest.spyOn(component, 'getProjects');

      component.filterSubmit();

      expect(component.filterCount).toBe(1);
      expect(component.getProjects).toHaveBeenCalled();
    });
  });

  describe('Authentication and Domain Check - Negative Scenarios', () => {
    it('should not set myAccount when domain check fails', () => {
      projectServiceMock.checkCurrentDomain.mockReturnValue(false);

      component.checkCurrentDomain();

      expect(component.myAccount).toBe(false);
      expect(component.accountAdmin).toBe(false);
    });

    it('should handle authentication service error', () => {
      projectServiceMock.checkCurrentDomain.mockReturnValue(true);
      authServiceMock.getUser.mockReturnValue(throwError(new Error('Auth Error')));

      component.checkCurrentDomain();

      expect(component.myAccount).toBe(true);
      // Should handle auth error gracefully
    });

    it('should handle null user response', () => {
      projectServiceMock.checkCurrentDomain.mockReturnValue(true);
      authServiceMock.getUser.mockReturnValue(of(null));

      component.checkCurrentDomain();

      expect(component.myAccount).toBe(true);
      // Should handle null user gracefully
    });
  });

  describe('Keyboard Event Handling - Negative Scenarios', () => {
    it('should not handle non-Enter/Space keys', () => {
      const mockEvent = { key: 'Tab', preventDefault: jest.fn() } as any;
      const mockData = {} as any;
      jest.spyOn(component, 'openModal1');
      jest.spyOn(component, 'clear');
      jest.spyOn(component, 'redirectProject');

      component.handleDownKeydown(mockEvent, mockData, 'filter');

      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      expect(component.openModal1).not.toHaveBeenCalled();
      expect(component.clear).not.toHaveBeenCalled();
      expect(component.redirectProject).not.toHaveBeenCalled();
    });

    it('should handle unknown action type', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      const mockData = {} as any;
      jest.spyOn(component, 'openModal1');
      jest.spyOn(component, 'clear');
      jest.spyOn(component, 'redirectProject');

      component.handleDownKeydown(mockEvent, mockData, 'unknown');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.openModal1).not.toHaveBeenCalled();
      expect(component.clear).not.toHaveBeenCalled();
      expect(component.redirectProject).not.toHaveBeenCalled();
    });
  });

  describe('Pagination - Negative Scenarios', () => {
    it('should handle invalid page size', () => {
      jest.spyOn(component, 'getProjects');

      component.changePageSize(0);

      expect(component.pageSize).toBe(0);
      expect(component.pageNo).toBe(1);
      expect(component.getProjects).toHaveBeenCalled();
    });

    it('should handle negative page number', () => {
      jest.spyOn(component, 'getProjects');

      component.changePageNo(-1);

      expect(component.pageNo).toBe(-1);
      expect(component.getProjects).toHaveBeenCalled();
    });
  });

  describe('Observable Subscriptions - Edge Cases', () => {
    it('should handle undefined/null values in refreshProject subscription', () => {
      jest.spyOn(component, 'getProjects');

      refreshProjectSubject.next(undefined);
      refreshProjectSubject.next(null);
      refreshProjectSubject.next('');

      expect(component.getProjects).not.toHaveBeenCalled();
    });

    it('should handle undefined/null values in projectParent subscription', () => {
      projectParentSubject.next(undefined);
      projectParentSubject.next(null);
      projectParentSubject.next('');

      // Should not crash
      expect(component).toBeTruthy();
    });

    it('should handle undefined/null values in ParentCompanyId subscription', () => {
      jest.spyOn(component, 'getProjects');

      parentCompanyIdSubject.next(undefined);
      parentCompanyIdSubject.next(null);
      parentCompanyIdSubject.next('');

      expect(component.getProjects).not.toHaveBeenCalled();
    });
  });

  describe('Form Validation - Edge Cases', () => {
    it('should handle undefined filterForm in getProjects', () => {
      component.filterForm = undefined as any;

      expect(() => component.getProjects()).not.toThrow();
    });

    it('should handle form reset when modalRef is undefined', () => {
      component.modalRef = undefined as any;

      expect(() => component.resetFilter()).toThrow();
    });
  });
});
