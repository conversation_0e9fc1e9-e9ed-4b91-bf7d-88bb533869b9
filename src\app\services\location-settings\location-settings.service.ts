import { Observable } from 'rxjs';
import { Injectable } from '@angular/core';
import { ApiService } from '../api_base/api.service';

@Injectable({
  providedIn: 'root',
  })

export class LocationSettingsService {
  public constructor(private readonly api: ApiService) { }

  public addLocation(payload: any, params): Observable<any> {
    return this.api.postMethod('location', payload, params);
  }

  public editLocation(payload, queryParams): Observable<any> {
    return this.api.putMethod('location', payload, queryParams);
  }

  public getLocations(queryParams): Observable<any> {
    return this.api.get('location', queryParams);
  }

  public getLocationList(queryParams): Observable<any> {
    return this.api.get('location/locations', queryParams);
  }

  public updateMemberLocationPreference(payload): Observable<any> {
    return this.api.put('location/member_location_preference', payload);
  }

  public getLocation(queryParams): Observable<any> {
    return this.api.get('location/get_location', queryParams);
  }

  public deleteLocation(queryParams): Observable<any> {
    return this.api.deleteMethod('location', queryParams);
  }

  public downloadSampleExcelFile(payload: any): Observable<any> {
    return this.api.getByHeadersFile('location/sample_location_excel_download', payload);
  }

  public importLocationFile(params: any, payload: any): Observable<any> {
    return this.api.bulkUploadLocation('location/import_location', params, payload);
  }
}
