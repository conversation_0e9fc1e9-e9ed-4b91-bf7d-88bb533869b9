<form
  name="form"
  [formGroup]="guestUserFormData"
  (ngSubmit)="submit()"
  class="custom-material-form enter-detail-customform"
  novalidate
>
  <div class="row m-0 py-5">
    <div class="col-md-2 col-1">
      <img
        class="c-pointer"
        src="../../../../assets/images/back-arrow-guest.svg"
        alt="back" (keydown)="handleToggleKeydown($event)"
        (click)="previousPage()"
      />
    </div>
    <div class="col-md-10 col-10 m-auto">
      <h1 class="fs16 fw-bold ps-0 pb-0 mb-0 enter-detail-heading text-center">
        Enter your Basic Details
      </h1>
    </div>
  </div>
  <div class="row m-0">
    <div class="col-md-12 mb-2">
      <div class="form-group">
        <label class="fs14 fw600 color-orange" for="email">Enter your work email<sup>*</sup></label>
        <input id="email"
          type="text"
          class="form-control fs12 material-input bg-transparent"
          placeholder="Email ID"
          formControlName="email"
          (keyup)="ifValidEmail()"
        />
        <div class="color-red" *ngIf="detailsSubmitted && guestUserFormData.get('email').errors">
          <small *ngIf="guestUserFormData.get('email').errors.required"
            >*Email address is Required</small>
          <small *ngIf="guestUserFormData.get('email').errors.pattern"
            >Enter valid email address</small>
        </div>
      </div>
    </div>
    <div class="col-md-12 mb-2">
      <div class="form-group">
        <div class="dropdown-country-code">
          <label class="color-orange fs13" for="phnNumber">Enter Your Mobile Number<sup>*</sup></label>
          <div class="form-group mb-3 timezone-formgroup homepage-formgroup row">
            <div class="col-md-2 col-3">
            <ng-multiselect-dropdown id="phnNumber"
              [settings]="codeDropdownSettings"
              [data]="countryCode"
              (onSelect)="selectCode($event)"
              formControlName="phoneCode"
              [(ngModel)]="this.defaultCode"
              class="country-code"
            >
            </ng-multiselect-dropdown>
          </div>
            <div class="col-md-10 col-8">
            <input
              type="text"
              class="form-control material-input fs13"
              name="mobile"
              formControlName="phoneNumber"
              (keypress)="numberOnly($event)"
              mask="{{ phoneMask }}"
              placeholder="Mobile Number"
              autocomplete="tel"
            />
          </div>
          </div>
        </div>

        <div
          class="color-red"
          *ngIf="detailsSubmitted && guestUserFormData.get('phoneCode').errors"
        >
          <small *ngIf="guestUserFormData.get('phoneCode').errors.required"
            >*Enter Phone code<sup>*</sup></small
          >
        </div>

        <div
          class="color-red"
          *ngIf="detailsSubmitted && guestUserFormData.get('phoneNumber').errors"
        >
          <small *ngIf="guestUserFormData.get('phoneNumber').errors.required"
            >* Phone Number is Required</small
          >
          <small
            *ngIf="
              !guestUserFormData.get('phoneNumber').errors.required &&
              guestUserFormData.get('phoneNumber').errors
            "
            >*Enter Valid Phone Number</small
          >
        </div>
      </div>
    </div>
    <div class="col-md-12 mb-2">
      <div class="form-group">
        <label class="fs14 fw600 color-orange" for="firstName">Enter your First Name<sup>*</sup></label>
        <input  id="firstName"
          type="text"
          class="form-control fs12 material-input bg-transparent"
          placeholder="First Name"
          (keypress)="alphaOnly($event)"
          formControlName="firstName"
          autocomplete="off"
        />
        <div
          class="color-red"
          *ngIf="detailsSubmitted && guestUserFormData.get('firstName').errors"
        >
          <small *ngIf="guestUserFormData.get('firstName').errors.required"
            >*First Name is required.</small
          >
          <small *ngIf="guestUserFormData.get('firstName').errors.minlength"
            >* First Name should contains minimum 3 characters.</small
          >
        </div>
      </div>
    </div>
    <div class="col-md-12 mb-2">
      <div class="form-group">
        <label class="fs14 fw600 color-orange"  for="lastName">Enter your Last Name<sup>*</sup></label>
        <input  id="lastName"
          type="text"
          class="form-control fs12 material-input bg-transparent"
          placeholder="Last Name"
          formControlName="lastName"
          (keypress)="alphaOnly($event)"
          autocomplete="off"
        />
        <div class="color-red" *ngIf="detailsSubmitted && guestUserFormData.get('lastName').errors">
          <small *ngIf="guestUserFormData.get('lastName').errors.required"
            >*Last Name is required.</small
          >
          <small *ngIf="guestUserFormData.get('lastName').errors.minlength"
            >* Last Name should contains minimum 3 characters.</small
          >
        </div>
      </div>
    </div>
    <div class="col-md-12 mb-2">
      <div class="form-group">
        <label class="fs14 fw600 color-orange" for="companyName">Company Name<sup>*</sup></label>
        <div class="form-group mb-3 timezone-formgroup homepage-formgroup d-flex">
          <ng-multiselect-dropdown id="companyName"
            [placeholder]="'Company Name'"
            [settings]="companyDropdownSettings"
            [data]="companyLists"
            (onSelect)="selectCompany($event)"
            formControlName="companyName"
            [(ngModel)]="this.defaultCompany"
            class="country-code w-100"
          >
          </ng-multiselect-dropdown>
        </div>
        <div
          class="color-red"
          *ngIf="detailsSubmitted && guestUserFormData.get('companyName').errors"
        >
          <small *ngIf="guestUserFormData.get('companyName').errors.required"
            >*Company Name is required.</small
          >
        </div>
      </div>
    </div>
  </div>
  <div class="row mt-5 text-center justify-content-center">
    <div class="col-10 col-md-12">
      <button
        class="btn w-100 btn-orange-dark px-5 radius20 fs13 fw-bold"
        type="submit"
      >
        Submit
      </button>
    </div>
  </div>
</form>
