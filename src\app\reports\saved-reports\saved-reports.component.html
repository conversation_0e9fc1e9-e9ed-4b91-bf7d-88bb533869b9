<div class="table-responsive mt-3" id="content">
  <table
    id="exceltable"
    class="table table-custom request-resizable mb-0"
    aria-describedby="deliveries"
  >
    <thead>
      <tr>
        <th scope="col" resizable>
          Author
          <span>
            <img
              src="./assets/images/down-chevron.svg"
              alt="down-arrow"
              class="h-10px ms-2"
              (click)="sortByField('author', 'ASC')"
              (keydown)="handleToggleKeydown($event, 'author', 'ASC')"
              *ngIf="sortColumn !== 'author'"
            />
            <img
              src="./assets/images/down-chevron.svg"
              alt="down-arrow"
              class="h-10px ms-2"
              (click)="sortByField('author', 'ASC')"
              (keydown)="handleToggleKeydown($event, 'author', 'ASC')"
              *ngIf="sort === 'DESC' && sortColumn === 'author'"
            />
            <img
              src="./assets/images/up-chevron.svg"
              alt="up-arrow"
              class="h-10px ms-2"
              (click)="sortByField('author', 'DESC')"
              (keydown)="handleToggleKeydown($event, 'author', 'DESC')"
              *ngIf="sort === 'ASC' && sortColumn === 'author'"
            />
          </span>
        </th>
        <th scope="col" resizable>
          Report Name
          <span>
            <img
              src="./assets/images/down-chevron.svg"
              alt="down-arrow"
              class="h-10px ms-2"
              (click)="sortByField('reportName', 'ASC')"
              (keydown)="handleToggleKeydown($event, 'reportName', 'ASC')"
              *ngIf="sortColumn !== 'reportName'"
            />
            <img
              src="./assets/images/down-chevron.svg"
              alt="down-arrow"
              class="h-10px ms-2"
              (click)="sortByField('reportName', 'ASC')"
              (keydown)="handleToggleKeydown($event, 'reportName', 'ASC')"
              *ngIf="sort === 'DESC' && sortColumn === 'reportName'"
            />
            <img
              src="./assets/images/up-chevron.svg"
              alt="up-arrow"
              class="h-10px ms-2"
              (click)="sortByField('reportName', 'DESC')"
              (keydown)="handleToggleKeydown($event, 'reportName', 'DESC')"
              *ngIf="sort === 'ASC' && sortColumn === 'reportName'"
            />
          </span>
        </th>
        <th scope="col" resizable>
          Template Type
          <span>
            <img
              src="./assets/images/down-chevron.svg"
              alt="down-arrow"
              class="h-10px ms-2"
              (click)="sortByField('reportType', 'ASC')"
              (keydown)="handleToggleKeydown($event, 'reportType', 'ASC')"
              *ngIf="sortColumn !== 'reportType'"
            />
            <img
              src="./assets/images/down-chevron.svg"
              alt="down-arrow"
              class="h-10px ms-2"
              (click)="sortByField('reportType', 'ASC')"
              (keydown)="handleToggleKeydown($event, 'reportType', 'ASC')"
              *ngIf="sort === 'DESC' && sortColumn === 'reportType'"
            />
            <img
              src="./assets/images/up-chevron.svg"
              alt="up-arrow"
              class="h-10px ms-2"
              (click)="sortByField('reportType', 'DESC')"
              (keydown)="handleToggleKeydown($event, 'reportType', 'DESC')"
              *ngIf="sort === 'ASC' && sortColumn === 'reportType'"
            />
          </span>
        </th>
        <th scope="col" resizable>
          File Format
          <span>
            <img
              src="./assets/images/down-chevron.svg"
              alt="down-arrow"
              class="h-10px ms-2"
              (click)="sortByField('outputFormat', 'ASC')"
              (keydown)="handleToggleKeydown($event, 'outputFormat', 'ASC')"
              *ngIf="sortColumn !== 'outputFormat'"
            />
            <img
              src="./assets/images/down-chevron.svg"
              alt="down-arrow"
              class="h-10px ms-2"
              (click)="sortByField('outputFormat', 'ASC')"
              (keydown)="handleToggleKeydown($event, 'outputFormat', 'ASC')"
              *ngIf="sort === 'DESC' && sortColumn === 'outputFormat'"
            />
            <img
              src="./assets/images/up-chevron.svg"
              alt="up-arrow"
              class="h-10px ms-2"
              (click)="sortByField('outputFormat', 'DESC')"
              (keydown)="handleToggleKeydown($event, 'outputFormat', 'DESC')"
              *ngIf="sort === 'ASC' && sortColumn === 'outputFormat'"
            />
          </span>
        </th>
        <th scope="col" resizable>
          Last Scheduled Run
          <span>
            <img
              src="./assets/images/down-chevron.svg"
              alt="down-arrow"
              class="h-10px ms-2"
              (click)="sortByField('lastRun', 'ASC')"
              (keydown)="handleToggleKeydown($event, 'lastRun', 'ASC')"
              *ngIf="sortColumn !== 'lastRun'"
            />
            <img
              src="./assets/images/down-chevron.svg"
              alt="down-arrow"
              class="h-10px ms-2"
              (click)="sortByField('lastRun', 'ASC')"
              (keydown)="handleToggleKeydown($event, 'lastRun', 'ASC')"
              *ngIf="sort === 'DESC' && sortColumn === 'lastRun'"
            />
            <img
              src="./assets/images/up-chevron.svg"
              alt="up-arrow"
              class="h-10px ms-2"
              (click)="sortByField('lastRun', 'DESC')"
              (keydown)="handleToggleKeydown($event, 'lastRun', 'DESC')"
              *ngIf="sort === 'ASC' && sortColumn === 'lastRun'"
            />
          </span>
        </th>
        <th class="text-center" scope="col" resizable>Action</th>
      </tr>
    </thead>
    <tbody *ngIf="!loader && savedReports.length > 0">
      <tr
        *ngFor="
          let data of savedReports
            | paginate
              : {
                  id: 'pagination1',
                  itemsPerPage: pageSize,
                  currentPage: pageNo,
                  totalItems: totalSavedCount
                };
          let i = index
        "
      >
        <td *ngIf="data?.createdUser">
          {{ data?.createdUser?.firstName }} {{ data?.createdUser?.lastName }}
        </td>
        <td>{{ data.reportName }}</td>
        <td>{{ data.reportType }}</td>
        <td *ngIf="data?.outputFormat">{{ data?.outputFormat }}</td>
        <td *ngIf="data?.lastRun">{{ data.lastRun | date : 'MM/dd/yyyy hh:mm a' }}</td>
        <td *ngIf="!data?.lastRun">-</td>
        <td class="text-center">
          <ul class="list-inline mb-0">
            <li class="list-inline-item" (click)="autoDownloadReportWithRunOption(data)"
            (keydown)="handleDownKeydown($event, data,'','auto')">
              <a class="btn btn-orange-dark fs12 run-btn">
                <em class="fa fa-spinner pe-1" aria-hidden="true" *ngIf="data.runNowLoader"></em>
                Run</a
              >
            </li>
            <li
              class="list-inline-item"
              tooltip="Download"
              placement="top"
              (click)="downloadSavedReport(data, 'lastRun')"
              (keydown)="handleDownKeydown($event, data, 'lastRun', 'download')"
            >
              <a href="javascript:void(0)"
                ><img
                  src="./assets/images/down-icon.svg"
                  alt="edit"
                  class="h-15px action-icon"
                  tooltip="Download"
                  placement="top"
              /></a>
            </li>
            <li
              class="list-inline-item mx-2"
              tooltip="Delete"
              placement="top"
              (click)="openDeleteModal(data, deleteList)"
              (keydown)="handleDownKeydown($event, data, deleteList, 'delete')"
              *ngIf="authUser?.UserId === data?.createdBy || authUser?.RoleId === 2"
            >
              <a href="javascript:void(0)"
                ><img
                  src="./assets/images/delete.svg"
                  alt="delete"
                  class="h-15px"
                  tooltip="Delete"
                  placement="top"
              /></a>
            </li>
          </ul>
        </td>
      </tr>
    </tbody>
    <tr *ngIf="loader == true">
      <td colspan="7" class="text-center">
        <div class="fs18 fw-bold cairo-regular my-5 text-black">
          <img src="../../assets/images/loader-member.gif" alt="loader-icon" class="loader-icon" />
        </div>
      </td>
    </tr>
    <tr *ngIf="loader == false && totalSavedCount == 0">
      <td colspan="7" class="text-center">
        <div class="fs18 fw-bold cairo-regular my-5 text-black">No Records Found</div>
      </td>
    </tr>
  </table>
</div>
<div class="tab-pagination px-2" *ngIf="loader == false && totalSavedCount > 25">
  <div class="row">
    <div class="col-md-3 align-items-center">
      <ul class="list-inline my-3 showentries">
        <li class="list-inline-item notify-pagination">
          <label for="showEnt" class="fs12 color-grey4 float-start">Show entries</label>
        </li>
        <li class="list-inline-item dropdown--counts">
          <select id="showEnt"
            class="w-auto form-select fs12 color-grey4"
            (change)="changePageSize($event.target.value)"
            ngModel="{{ pageSize }}"
          >
            <option value="25">25</option>
            <option value="10">10</option>
            <option value="100">100</option>
            <option value="150">150</option>
          </select>
        </li>
      </ul>
    </div>
    <div class="col-md-8 text-center">
      <div class="my-3 position-relative d-inline-block">
        <pagination-controls
          id="pagination1"
          (pageChange)="changePageNo($event)"
          previousLabel=""
          nextLabel=""
        >
        </pagination-controls>
      </div>
    </div>
  </div>
</div>

<!-- Delete Modal -->
<ng-template #deleteList>
  <div class="modal-body">
    <div class="text-center my-4">
      <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
        Are you sure you want to delete ?
      </p>
      <button
        class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
        (click)="resetAndClose()"
      >
        No
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
        type="submit"
        (click)="deleteSavedReport()"
        [disabled]="deleteSavedReportSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="deleteSavedReportSubmitted"></em>Yes
      </button>
    </div>
  </div>
</ng-template>
<!-- Delete Modal -->
