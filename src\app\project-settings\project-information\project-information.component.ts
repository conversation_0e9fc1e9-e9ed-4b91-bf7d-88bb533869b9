import {
  Component, ElementRef, OnInit, TemplateRef, ViewChild,
} from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Title } from '@angular/platform-browser';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ProjectService } from '../../services/profile/project.service';
import { ProjectSettingsService } from '../../services/project_settings/project-settings.service';

declare let google: any;

@Component({
  selector: 'app-project-information',
  templateUrl: './project-information.component.html',
  })
export class ProjectInformationComponent implements OnInit {
  public modalRef?: BsModalRef;

  public latitude: number;

  public longitude: number;

  public zoom: number;

  public ProjectId: string | number;

  public projectDetail;

  public loader = true;

  public isSaving = true;

  public mainLoader = true;


  public geoCoder: {
    geocode: (
      arg0: { location: { lat: any; lng: any } },
      arg1: (results: any, status: any) => void,
    ) => void;
  };

  public projectSharingSettingsFormData: UntypedFormGroup;

  public updateLoader: boolean;

  public saveButtonDisabled = true;

  // public selectedFile: any;

  public formData: any = {};

  public fileUpload: boolean;

  public websiteEnabled: any;

  public shareProjectInfo: any;

  public addDeliveryBooking: any;

  public addCraneBooking: any;

  public addConcreteBooking: any;

  public viewDeliveryCalendar: any;

  public viewCraneCalendar: any;

  public viewConcreteCalendar: any;

  public refreshRate: any;

  public shareLogisticPlan: any;

  public pdfName: any;

  public pdfUploaded: any;

  public count = 0;

  public planFile: any;

  public logisticPlanUploaded = false;

  public logisticPlanName: any;

  public logisticPlanLink: any;

  public planUploading = false;

  public planDeleting = false;

  public buttonhide = false;

  @ViewChild('fileInput', { static: false }) public fileInput!:
  ElementRef<HTMLInputElement>;

  @ViewChild('logisticPlan', { static: false }) public logisticPlan!:
  ElementRef<HTMLInputElement>;

  public mapOptions: google.maps.MapOptions = {
    center: { lat: 38.897957, lng: -77.036560 },
    zoomControl: true,
    mapTypeControl: true,
    streetViewControl: false,
    fullscreenControl: true,
    draggable: true,
    zoom: 18,
    mapTypeId: google.maps.MapTypeId.SATELLITE,
  };

  public marker = {
    position: { lat: 38.897957, lng: -77.036560 },
    options: { animation: google.maps.Animation.DROP },
  };

  public constructor(
    private readonly modalService: BsModalService,
    private readonly projectService: ProjectService,
    private readonly toastr: ToastrService,
    private readonly titleService: Title,
    public projectSettingsService: ProjectSettingsService,
    private readonly formBuilder: UntypedFormBuilder,
  ) {
    this.titleService.setTitle('Follo - Project Information');
    this.projectService.projectParent.subscribe((response19): void => {
      if (response19 !== undefined && response19 !== null && response19 !== '') {
        this.ProjectId = response19.ProjectId;
        this.getProjectDetail(this.ProjectId);
        this.getProjectSharingSettings();
        this.getProjectLogisticPlan();
        this.projectSharingSettingsForm();
      }
    });
  }

  public ngOnInit(): void {
    this.projectSharingSettingsForm();
  }

  // Get Current Location Coordinates
  public setCurrentLocation(): void {
    if ('geolocation' in navigator) {
      navigator.geolocation.getCurrentPosition((position: any): void => { // NOSONAR
        if (!this.projectDetail) {
          this.latitude = position.coords.latitude;
          this.longitude = position.coords.longitude;
          this.zoom = 15;
        } else {
          this.latitude = this.projectDetail.projectLocationLatitude;
          this.longitude = this.projectDetail.projectLocationLongitude;
          this.zoom = 15;
        }
      });
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, type: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'down':
          this.downloadPlan();
          break;
        case 'delete':
          this.openDeleteModal(data);
          break;
        default:
          break;
      }
    }
  }

  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = {
      backdrop: 'true',
      keyboard: false,
      class: 'modal-sm qrcode-popup modal-dialog-centered',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public getProjectDetail(id): void {
    this.mainLoader = true;
    const params = {
      ProjectId: id,
    };

    this.projectService.getProjectSubscription(params).subscribe({
      next: (projectList: any): void => {
        if (projectList.data) {
          this.mainLoader = false;
          this.projectDetail = projectList.data;
          this.latitude = Number(this.projectDetail.projectLocationLatitude);
          this.longitude = Number(this.projectDetail.projectLocationLongitude);
          this.zoom = 15;
          this.mapForProjectLocationLoader(this.latitude, this.longitude);
        }
      },
      error: (error): void => {
        this.mainLoader = false;
        this.toastr.error('Try again later.!', 'Something went wrong.');
      },
    });
  }

  public mapForProjectLocationLoader(latitude: any, longitude: any): void {
    this.geoCoder = new google.maps.Geocoder();
    this.projectLocation(latitude, longitude);
  }

  public projectLocation(latitude: string, longitude: string): void {
    const lat = parseFloat(latitude);
    const long = parseFloat(longitude);
    if (navigator.geolocation) {
      this.latitude = lat;
      this.longitude = long;
      this.zoom = 18;
      this.mapOptions.center = {
        lat: this.latitude,
        lng: this.longitude,
      };
      this.marker.position = { lat: this.latitude, lng: this.longitude };
      this.mapOptions.zoom = 18;
    }
  }

  public projectSharingSettingsForm(): void {
    this.projectSharingSettingsFormData = this.formBuilder.group({
      isPublicWebsiteEnabled: ['', Validators.required],
      shareProjectInformation: [''],
      shareprojectLogisticPlan: ['', Validators.required],
      // projectLogisticPlanUrl: [''],
      allowGuestToAddDeliveryBooking: ['', Validators.required],
      allowGuestToAddCraneBooking: ['', Validators.required],
      allowGuestToAddConcreteBooking: ['', Validators.required],
      allowGuestToViewDeliveryCalendar: ['', Validators.required],
      allowGuestToViewCraneCalendar: ['', Validators.required],
      allowGuestToViewConcreteCalendar: ['', Validators.required],
      autoRefreshRateInMinutes: ['', Validators.required],
      publicWebsiteUrl: [''],
      // pdfOriginalName: [''],
      // isPdfUploaded: ['', Validators.required],
    });
  }

  public submit(): void {
    this.updateLoader = true;
    if (this.ProjectId) {
      const payload: any = {
        ProjectId: this.ProjectId,
        isPublicWebsiteEnabled: this.projectSharingSettingsFormData.value.isPublicWebsiteEnabled,
        shareprojectLogisticPlan:
            this.projectSharingSettingsFormData.value.shareprojectLogisticPlan,
        shareProjectInformation: this.projectSharingSettingsFormData.value.shareProjectInformation,
        allowGuestToAddDeliveryBooking:
            this.projectSharingSettingsFormData.value.allowGuestToAddDeliveryBooking,
        allowGuestToAddCraneBooking:
            this.projectSharingSettingsFormData.value.allowGuestToAddCraneBooking,
        allowGuestToAddConcreteBooking:
            this.projectSharingSettingsFormData.value.allowGuestToAddConcreteBooking,
        allowGuestToViewDeliveryCalendar:
            this.projectSharingSettingsFormData.value.allowGuestToViewDeliveryCalendar,
        allowGuestToViewCraneCalendar:
            this.projectSharingSettingsFormData.value.allowGuestToViewCraneCalendar,
        allowGuestToViewConcreteCalendar:
            this.projectSharingSettingsFormData.value.allowGuestToViewConcreteCalendar,
        autoRefreshRateInMinutes:
            this.projectSharingSettingsFormData.value.autoRefreshRateInMinutes,
        publicWebsiteUrl: this.projectSharingSettingsFormData.value.publicWebsiteUrl,
        formData: this.formData,
      };
      this.projectService.updateProjectSharingSettings(payload).subscribe({
        next: (res): void => {
          if (res) {
            this.updateLoader = false;
            this.isSaving = false;
            this.toastr.success(res.message, 'Success');
            this.getProjectSharingSettings();
            this.saveButtonDisabled = true;
          }
        },
        error: (error): void => {
          this.updateLoader = false;
          this.isSaving = false;
          this.toastr.error('Try again later.!', 'Something went wrong.');
        },
      });
    }
  }

  public openDeleteModal(template: TemplateRef<any>): void {
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-md new-gate-popup custom-modal',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public getProjectSharingSettings(): void {
    this.loader = true;
    this.isSaving = true;
    this.saveButtonDisabled = true;

    if (this.ProjectId) {
      const params = {
        ProjectId: this.ProjectId,
      };
      this.projectSettingsService.getProjectSettings(params).subscribe((res): void => {
        const responseData = res?.data?.projectSettings;
        if (responseData) {
          this.getCardData(responseData);
          this.saveButtonDisabled = true;
        }
        this.loader = false;
        this.isSaving = false;
      });
    }
  }

  public getCardData(responseData): void {
    this.projectSharingSettingsFormData
      .get('isPublicWebsiteEnabled')
      .setValue(responseData.isPublicWebsiteEnabled);
    this.projectSharingSettingsFormData
      .get('shareProjectInformation')
      .setValue(responseData.shareProjectInformation);
    this.projectSharingSettingsFormData
      .get('shareprojectLogisticPlan')
      .setValue(responseData.shareprojectLogisticPlan);
    this.projectSharingSettingsFormData
      .get('allowGuestToAddDeliveryBooking')
      .setValue(responseData.allowGuestToAddDeliveryBooking);
    this.projectSharingSettingsFormData
      .get('allowGuestToAddCraneBooking')
      .setValue(responseData.allowGuestToAddCraneBooking);
    this.projectSharingSettingsFormData
      .get('allowGuestToAddConcreteBooking')
      .setValue(responseData.allowGuestToAddConcreteBooking);
    this.projectSharingSettingsFormData
      .get('allowGuestToViewDeliveryCalendar')
      .setValue(responseData.allowGuestToViewDeliveryCalendar);
    this.projectSharingSettingsFormData
      .get('allowGuestToViewCraneCalendar')
      .setValue(responseData.allowGuestToViewCraneCalendar);
    this.projectSharingSettingsFormData
      .get('allowGuestToViewConcreteCalendar')
      .setValue(responseData.allowGuestToViewConcreteCalendar);
    this.projectSharingSettingsFormData
      .get('autoRefreshRateInMinutes')
      .setValue(responseData.autoRefreshRateInMinutes);
    this.projectSharingSettingsFormData
      .get('publicWebsiteUrl')
      .setValue(responseData.publicWebsiteUrl);
    if (this.projectSharingSettingsFormData.value.isPublicWebsiteEnabled) {
      this.projectSharingSettingsFormData.value.shareProjectInformation = true;
      this.projectSharingSettingsFormData
        .get('shareProjectInformation')
        .setValue(true);
    }
    this.storingResponseValue();
  }

  public storingResponseValue(): void {
    this.websiteEnabled = this.projectSharingSettingsFormData.value.isPublicWebsiteEnabled;
    this.shareProjectInfo = this.projectSharingSettingsFormData.value.shareProjectInformation;
    this.shareLogisticPlan = this.projectSharingSettingsFormData.value.shareprojectLogisticPlan;
    this.addDeliveryBooking = this.projectSharingSettingsFormData.value.allowGuestToAddDeliveryBooking;
    this.addCraneBooking = this.projectSharingSettingsFormData.value.allowGuestToAddCraneBooking;
    this.addConcreteBooking = this.projectSharingSettingsFormData.value.allowGuestToAddConcreteBooking;
    this.viewDeliveryCalendar = this.projectSharingSettingsFormData.value.allowGuestToViewDeliveryCalendar;
    this.viewCraneCalendar = this.projectSharingSettingsFormData.value.allowGuestToViewCraneCalendar;
    this.viewConcreteCalendar = this.projectSharingSettingsFormData.value.allowGuestToViewConcreteCalendar;
    this.refreshRate = this.projectSharingSettingsFormData.value.autoRefreshRateInMinutes;
  }

  public projectSharingSetting(type: any): void {
    if (type === 'publicWebsiteEnabled') {
      this.projectSharingSettingsFormData
        .get('isPublicWebsiteEnabled')
        .setValue(!this.projectSharingSettingsFormData.value.isPublicWebsiteEnabled);
      this.checkWithResponseValue(
        this.projectSharingSettingsFormData.value.isPublicWebsiteEnabled,
        this.websiteEnabled,
      );
      this.projectSharingSettingsFormData
        .get('shareProjectInformation')
        .setValue(!this.projectSharingSettingsFormData.value.shareProjectInformation);
      this.checkWithResponseValue(
        this.projectSharingSettingsFormData.value.shareProjectInformation,
        this.shareProjectInfo,
      );
    }

    if (type === 'shareprojectLogisticPlan') {
      this.projectSharingSettingsFormData
        .get('shareprojectLogisticPlan')
        .setValue(!this.projectSharingSettingsFormData.value.shareprojectLogisticPlan);
      this.checkWithResponseValue(
        this.projectSharingSettingsFormData.value.shareprojectLogisticPlan,
        this.shareLogisticPlan,
      );
    }
    if (type === 'addDeliveryBooking') {
      this.projectSharingSettingsFormData
        .get('allowGuestToAddDeliveryBooking')
        .setValue(!this.projectSharingSettingsFormData.value.allowGuestToAddDeliveryBooking);
      this.checkWithResponseValue(
        this.projectSharingSettingsFormData.value.allowGuestToAddDeliveryBooking,
        this.addDeliveryBooking,
      );
    }
    if (type === 'addCraneBooking') {
      this.projectSharingSettingsFormData
        .get('allowGuestToAddCraneBooking')
        .setValue(!this.projectSharingSettingsFormData.value.allowGuestToAddCraneBooking);
      this.checkWithResponseValue(
        this.projectSharingSettingsFormData.value.allowGuestToAddCraneBooking,
        this.addCraneBooking,
      );
    }
    if (type === 'viewDeliveryCalendar') {
      this.projectSharingSettingsFormData
        .get('allowGuestToViewDeliveryCalendar')
        .setValue(!this.projectSharingSettingsFormData.value.allowGuestToViewDeliveryCalendar);
      this.checkWithResponseValue(
        this.projectSharingSettingsFormData.value.allowGuestToViewDeliveryCalendar,
        this.viewDeliveryCalendar,
      );
    }
    if (type === 'viewCraneCalendar') {
      this.projectSharingSettingsFormData
        .get('allowGuestToViewCraneCalendar')
        .setValue(!this.projectSharingSettingsFormData.value.allowGuestToViewCraneCalendar);
      this.checkWithResponseValue(
        this.projectSharingSettingsFormData.value.allowGuestToViewCraneCalendar,
        this.viewCraneCalendar,
      );
    }
  }

  public concreteProjectSharingSettings(type: any): void {
    if (type === 'addConcreteBooking') {
      this.projectSharingSettingsFormData
        .get('allowGuestToAddConcreteBooking')
        .setValue(!this.projectSharingSettingsFormData.value.allowGuestToAddConcreteBooking);
      this.checkWithResponseValue(
        this.projectSharingSettingsFormData.value.allowGuestToAddConcreteBooking,
        this.addConcreteBooking,
      );
    }
    if (type === 'viewConcreteCalendar') {
      this.projectSharingSettingsFormData
        .get('allowGuestToViewConcreteCalendar')
        .setValue(!this.projectSharingSettingsFormData.value.allowGuestToViewConcreteCalendar);
      this.checkWithResponseValue(
        this.projectSharingSettingsFormData.value.allowGuestToViewConcreteCalendar,
        this.viewConcreteCalendar,
      );
    }
  }

  public checkWithResponseValue(newValue, originalvalue): void {
    if (newValue !== originalvalue) {
      this.count += 1;
    } else {
      this.count -= 1;
    }
    if (this.count >= 1) {
      this.saveButtonDisabled = false;
    } else {
      this.saveButtonDisabled = true;
    }
  }

  public checkRefreshRate(newValue, originalvalue): void {
    if (newValue !== originalvalue) {
      this.saveButtonDisabled = false;
    } else {
      this.saveButtonDisabled = true;
    }
  }

  public openPublicWebsite(): void {
    (window as any).open(this.projectSharingSettingsFormData.value.publicWebsiteUrl, '_blank');
  }

  public copyLink(publicUrl: string): void {
    navigator.clipboard.writeText(publicUrl)
      .then(() => {
        this.toastr.success('Public website link copied successfully');
      })
      .catch((err) => {
        console.error('Failed to copy: ', err);
        this.toastr.error('Failed to copy the link');
      });
  }

  // public onFileSelected(files: { name: any }[]): void {
  //   this.selectedFile = files[0];
  //   const extension = files[0].name.split('.')[files[0].name.split('.').length - 1];
  //   const realExtension = extension.toLowerCase();
  //   if (realExtension === 'pdf' || realExtension === 'png' || realExtension === 'jpg' || realExtension === 'jpeg') {
  //     this.selectedFile = files[0];
  //     this.formData = new FormData();
  //     this.formData.append('projectPlan', files[0], files[0].name);
  //     this.projectSharingSettingsFormData
  //       .get('pdfOriginalName')
  //       .setValue((this.projectSharingSettingsFormData.value.pdfOriginalName = files[0].name));
  //   } else {
  //     this.selectedFile = null;
  //     this.toastr.error('Only *pdf,*jpg,*png,*jpeg files are allowed');
  //     this.resetFileInput();
  //     return;
  //   }
  //   if (this.selectedFile) {
  //     this.saveButtonDisabled = false;
  //   }
  // }

  // public openDeleteModal(template: TemplateRef<any>): void {
  //   let data = {};
  //   data = {
  //     backdrop: 'static',
  //     keyboard: false,
  //     class: 'modal-md new-gate-popup custom-modal',
  //   };
  //   this.modalRef = this.modalService.show(template, data);
  // }

  // public downloadFile(): void {
  //   (window as any).open(
  //     this.projectSharingSettingsFormData.value.projectLogisticPlanUrl,
  //     '_blank',
  //   );
  // }


  public downloadQRCodeImage(): void {
    const qrCodeElement = document.querySelector('qrcode');

    if (qrCodeElement) {
      const canvas = qrCodeElement.querySelector('canvas');
      if (canvas) {
        const link = document.createElement('a');
        link.href = canvas.toDataURL('image/png');
        link.download = `${this.projectDetail.projectName}.png`;
        link.click();
        this.toastr.success('QR-code Downloaded successfully');
      }
    }
  }

  public onPlanSelected(files: { name: any }[]): void {
    this.planFile = files[0];
    const extension = files[0].name.split('.')[files[0].name.split('.').length - 1];
    const realExtension = extension.toLowerCase();
    if (realExtension === 'pdf' || realExtension === 'png' || realExtension === 'jpg' || realExtension === 'jpeg') {
      this.planUploading = true;
      this.planFile = files[0];
      this.formData = new FormData();
      this.formData.append('projectPlan', files[0], files[0].name);
      const data = {
        ProjectId: this.ProjectId,
        projectName: this.projectDetail.projectName,
      };
      this.projectService.uploadProjectLogisticPlanUrl(data, this.formData).subscribe({
        next: (res): void => {
          if (res?.data?.fileUrl) {
            this.planUploading = false;
            this.getProjectLogisticPlan();
            this.toastr.success(res.message);
            this.logisticPlan.nativeElement.value = null;
          } else {
            this.planUploading = false;
            this.logisticPlan.nativeElement.value = null;
          }
        },
        error: (error): void => {
          this.planUploading = false;
          this.toastr.error('Try again later.!', 'Something went wrong.');
        },
      });
    } else {
      this.planFile = null;
      this.toastr.error('Only *pdf,*jpg,*png,*jpeg files are allowed');
    }
  }

  public getProjectLogisticPlan(): void {
    if (this.ProjectId) {
      const params = {
        ProjectId: this.ProjectId,
      };
      this.projectSettingsService.getProjectSettings(params).subscribe((res): void => {
        const responseData = res?.data?.projectSettings;
        if (responseData) {
          this.logisticPlanUploaded = responseData.isPdfUploaded;
          this.logisticPlanName = responseData.pdfOriginalName;
          this.logisticPlanLink = responseData.projectLogisticPlanUrl;
        }
      });
    }
  }

  public downloadPlan(): void {
    (window as any).open(
      this.logisticPlanLink,
      '_blank',
    );
  }

  public deletePlan(action: string): void {
    if (action === 'no') {
      this.modalRef.hide();
    } else {
      this.planFile = null;
      const payload: any = {
        isPdfUploaded: false,
        ProjectId: this.ProjectId,
        convertedImageLinks: null,
      };
      this.planDeleting = true;
      this.projectService.updateLogisticPlanSettings(payload).subscribe({
        next: (res): void => {
          if (res) {
            this.planDeleting = false;
            this.modalRef.hide();
            this.toastr.success('Site Plan deleted successfully');
            this.getProjectLogisticPlan();
          } else {
            this.planDeleting = false;
            this.modalRef.hide();
            this.toastr.error('Unable to delete Site Plan');
          }
        },
        error: (error): void => {
          this.planDeleting = false;
          this.modalRef.hide();
          this.toastr.error('Try again later.!', 'Something went wrong.');
        },
      });
    }
  }
}
