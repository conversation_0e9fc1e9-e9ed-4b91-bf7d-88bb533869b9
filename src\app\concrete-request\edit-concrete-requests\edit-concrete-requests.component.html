<div class="modal-header">
  <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">
    <img src="./assets/images/add-concrete.svg" alt="Delivery" class="me-2" />Edit Concrete Booking
  </h1>
  <button
    type="button"
    class="close ms-auto"
    aria-label="Close"
    (click)="close(cancelConfirmation)"
  >
    <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close" /></span>
  </button>
</div>
<div class="modal-body newupdate-alignments taginput--heightfix editconcrete" *ngIf="!modalLoader">
  <form
    name="form"
    class="custom-material-form add-concrete-material-form"
    [formGroup]="concreteRequest"
    novalidate
  >
    <div class="row">
      <div class="col-md-6">
        <div class="form-group mb-0">
          <label class="fs12 fw600" for="description">Description<sup>*</sup></label>
          <textarea  id="description"
            class="form-control fs11 radius0"
            rows="2"
            maxlength="150"
            formControlName="description"
            (ngModelChange)="onEditSubmitForm();"
          ></textarea>
          <div class="color-red" *ngIf="submitted && concreteRequest.get('description').errors">
            <small *ngIf="concreteRequest.get('description').errors.required"
              >*Description is Required.</small
            >
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group pt-0 mt-0">
          <label class="fs12 fw600 mb-0"  for="concid">Concrete Booking ID</label>
          <input  id="concid"
            type="text"
            class="form-control fs11 material-input p-0 h-25 mt-2"
            disabled="disabled"
            value="{{ concreteRequest.get('ConcreteRequestId').value }}"
          />
        </div>
      </div>
    </div>
    <div class="row mt-2">
      <div class="col-md-6 primary-tooltip">
        <div class="form-group mb-0 mt-2 timezone-formgroup timezone-buttonpadding">
          <label class="fs12 fw600"  for="location">Location<span class="color-red"><sup>*</sup></span>
            <div class="dot-border-info location-border-info tooltip location-tooltip">
              <span class="fw700 info-icon fs12">i</span>
              <span class="tooltiptext tooltiptext-info"
                >Where will the materials/equipment be installed</span
              >
              <div class="arrow-down"></div></div
          ></label>
          <ng-multiselect-dropdown  id="location"
            [placeholder]="'Choose Location'"
            [settings]="locationDropdownSettings"
            [data]="locationDropdown"
            (onSelect)="locationSelected($event)"
            formControlName="LocationId"
            (ngModelChange)="onEditSubmitForm();"
          >
          </ng-multiselect-dropdown>
          <div class="color-red" *ngIf="submitted && concreteRequest.get('LocationId').errors">
            <small *ngIf="concreteRequest.get('LocationId').errors.required"
              >*Location is Required.</small
            >
          </div>
        </div>
      </div>
      <div class="col-md-6 mt-4 additional-location-detail mt-29rem">
        <div class="floating-concrete mb-3">
          <div class="form-group floating-label">
            <input
              class="floating-input form-control fs12 px-0"
              type="text"
              placeholder=" "
              formControlName="location"   id="addloc"
              (ngModelChange)="onEditSubmitForm();"
            />
            <label class="fs12 fw600 m-0 color-grey11"  for="addloc"
              >Additional Location Details
            </label>

          </div>
        </div>
      </div>

    </div>

    <div class="row mt-3">

      <div class="col-md-6">
        <div class="form-group">
          <label class="fs12 fw600 mt-2"   for="gate">Gate<sup>*</sup></label>
          <select class="form-control fs12 material-input px-0 mt-1" formControlName="GateId"   id="gate"
          (change)="getSlots()"
          >
            <option value="" disabled selected hidden>Gate<sup>*</sup></option>
            <option *ngFor="let item of gateList" value="{{ item?.id }}">
              {{ item.gateName }}
            </option>
          </select>
          <div class="color-red" *ngIf="submitted && concreteRequest.get('GateId').errors">
            <small *ngIf="concreteRequest.get('GateId').errors.required">*Gate is Required.</small>
          </div>
        </div>
      </div>

      <div class="col-md-6 mt-2">
        <div class="form-group mb-0 company-select equipment-select">
          <label class="fs12 fw600 mb-3" for="equipment">Equipment<sup>*</sup></label>
          <ng-multiselect-dropdown id="equipment"
          [placeholder]="'Equipment'"
          [settings]="equipmentDropdownSettings"
          [data]="equipmentList"
          formControlName="EquipmentId"
          (ngModelChange)="equipmentSelected($event)"
          >
        </ng-multiselect-dropdown>
          <div class="color-red" *ngIf="submitted && concreteRequest.get('EquipmentId').errors">
            <small *ngIf="concreteRequest.get('EquipmentId').errors.required"
              >*Equipment is Required.</small
            >
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-md-12">
        <div class="form-group taginput-height">
          <label class="fs12 fw600" for="resperson">Responsible Person<sup>*</sup></label>
          <tag-input  id="resperson"
            [onlyFromAutocomplete]="true"
            [placeholder]="' '"
            formControlName="responsiblePersons"
            [onTextChangeDebounce]="500"
            class="tag-layout"
            [identifyBy]="'id'"
            [displayBy]="'email'"
            (ngModelChange)="onEditSubmitForm();"
            (ngModelChange)="gatecheck($event, 'Person')"
          >
            <tag-input-dropdown
              [showDropdownIfEmpty]="false"
              [displayBy]="'email'"
              [identifyBy]="'id'"
              [appendToBody]="false"
              [autocompleteObservable]="requestAutoEditcompleteItems"
            >
              <ng-template let-item="item" let-index="index">
                {{ item.email }}
              </ng-template>
            </tag-input-dropdown>
          </tag-input>
          <small class="color-red" *ngIf="errmemberenable"
            >Please select an active member,the existing member is deactivated.</small
          >
          <div
            class="color-red"
            *ngIf="submitted && concreteRequest.get('responsiblePersons').errors"
          >
            <small>*Please choose Responsible person.</small>
          </div>
        </div>
      </div>
    </div>

    <div class="row mt-3">
      <div class="col-md-12">
        <app-time-slot
          #timeSlotRef
          (selectTime)="selectTime($event.start, $event.end)"
          [selectedBookingDate]="concreteRequest.get('concretePlacementDate').value"
          [timeZone]="timeZone"
          [isEditMode]="true"
          [selectedId]="concreteRequest.get('id').value"
          [selectedBookingType]="'concreteRequest'"
          [equipmentId] = "concreteRequest.get('EquipmentId').value"
          [LocationId] = "concreteRequest.get('LocationId').value"
          [gateId] = "concreteRequest.get('GateId').value"
        >
        </app-time-slot>
      </div>
    </div>

    <div class="row mt-3 addcalendar-details" *ngIf="seriesOption !== 1">
      <div class="col-md-6">
        <!-- recurrance code start -->

        <div class="col-md-12 p-0">
          <label class="fs12 fw600 mb-0"  for="recurrance">Recurrence<sup>*</sup></label>
          <div class="form-group">
            <select id="recurrance"
              class="form-control fs12 material-input px-0"
              formControlName="recurrence"
              (change)="onRecurrenceSelect($event.target.value)"
            >
              <option *ngFor="let type of recurrence" value="{{ type.value }}">
                {{ type.value }}
              </option>
            </select>
          </div>
          <div
            class="row mt-2"
            *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
          >
            <div
              class="col-md-12 mt-md-0"
              *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
            >
              <label class="fs12 fw600 mb-0" for="repevery">Repeat Every</label>
            </div>
            <div
              class="col-md-6 mt-md-0"
              *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
            >
              <div class="form-group">
                <input id="repevery"
                  type="text"
                  formControlName="repeatEveryCount"
                  class="form-control fs12 material-input p-0"
                  (input)="changeRecurrenceCount($event.target.value)"
                  min="1"
                />
              </div>
            </div>

            <div class="col-md-6 mt-md-0" *ngIf="isRepeatWithSingleRecurrence">
              <div class="form-group">
                <select
                  class="form-control fs12 material-input px-2"
                  formControlName="repeatEveryType"
                  (change)="chooseRepeatEveryType($event.target.value, null)"
                >
                  <option value="" disabled selected hidden>Select Recurrence</option>
                  <option
                    *ngFor="let type of repeatWithSingleRecurrence"
                    value="{{ type.value }}"
                  >
                    {{ type.value }}
                  </option>
                </select>
              </div>
            </div>
            <div
              class="col-md-4 mt-md-0"
              *ngIf="isRepeatWithMultipleRecurrence || showRecurrenceTypeDropdown"
            >
              <div class="form-group">
                <select
                  class="form-control fs12 material-input px-2"
                  formControlName="repeatEveryType"
                  (change)="chooseRepeatEveryType($event.target.value, null)"
                >
                  <option value="" disabled selected hidden>Select Recurrence</option>
                  <option
                    *ngFor="let type of repeatWithMultipleRecurrence"
                    value="{{ type.value }}"
                  >
                    {{ type.value }}
                  </option>
                </select>
              </div>
            </div>
          </div>
          <div class="row addcalendar-displaydays">
            <div
              class="col-md-12 pt-0"
              *ngIf="
                (selectedRecurrence === 'Weekly' ||
                  isRepeatWithMultipleRecurrence ||
                  isRepeatWithSingleRecurrence) &&
                selectedRecurrence !== 'Monthly' &&
                selectedRecurrence !== 'Yearly'
              "
            >
              <ul class="displaylists ps-0 mb-0">
                <li *ngFor="let item of weekDays; let i = index" class="fs12 list-inline-item">
                  <input
                    type="checkbox"
                    [disabled]="item.isDisabled"
                    [value]="item.value"
                    class="d-none"
                    id="days-{{ i }}"
                    (change)="onChange($event)"
                    [checked]="item.checked"
                  />
                  <label for="days-{{ i }}">{{ item.display }}</label>
                </li>
                <div
                  class="color-red"
                  *ngIf="submitted && concreteRequest.controls['days'].errors"
                >
                  <small *ngIf="concreteRequest.controls['days'].errors.required"
                    >*Required
                  </small>
                </div>
              </ul>
            </div>
          </div>
          <div
            class="row"
            *ngIf="selectedRecurrence === 'Monthly' || selectedRecurrence === 'Yearly'"
            [ngClass]="selectedRecurrence === 'Yearly' ? 'recurrence-year' : ''"
          >
            <div class="col-md-12 mt-md-0 ps-2 pt-0 recurrance-column-day">
              <div class="w-100 float-start">
                <div class="form-check">
                  <input
                    class="form-check-input c-pointer"
                    type="radio"
                    formControlName="chosenDateOfMonth"
                    id="flexRadioDefault1"
                    [value]="1"
                    (change)="changeMonthlyRecurrence()"
                  />
                  <label class="form-check-label fs12 color-orange" for="flexRadioDefault1">
                    On day {{ monthlyDate }}
                  </label>
                </div>
                <div class="form-check">
                  <input
                    class="form-check-input c-pointer"
                    type="radio"
                    formControlName="chosenDateOfMonth"
                    id="flexRadioDefault2"
                    [value]="2"
                    (change)="changeMonthlyRecurrence()"
                  />
                  <label class="form-check-label fs12 color-orange" for="flexRadioDefault2">
                    On the
                    {{ monthlyDayOfWeek }}
                    <p *ngIf="selectedRecurrence === 'Yearly'">
                      of
                      {{ concreteRequest.get('concretePlacementDate').value | date : 'LLLL' }}
                    </p>
                  </label>
                </div>
                <div class="form-check" *ngIf="enableOption">
                  <input
                    class="form-check-input c-pointer"
                    type="radio"
                    formControlName="chosenDateOfMonth"
                    id="flexRadioDefault3"
                    [value]="3"
                    (change)="changeMonthlyRecurrence()"
                  />
                  <label class="form-check-label fs12 color-orange" for="flexRadioDefault3">
                    On the
                    {{ monthlyLastDayOfWeek }}
                    <p *ngIf="selectedRecurrence === 'Yearly'">
                      of
                      {{ concreteRequest.get('concretePlacementDate').value | date : 'LLLL' }}
                    </p>
                  </label>
                </div>
              </div>
              <div>
                <div
                  class="color-red"
                  *ngIf="
                    submitted &&
                    (concreteRequest.get('monthlyRepeatType')?.errors ||
                    concreteRequest.get('dateOfMonth')?.errors)
                  "
                >
                  <small *ngIf="concreteRequest.get('monthlyRepeatType')?.errors?.required"
                    >*required</small
                  >
                  <small *ngIf="concreteRequest.get('dateOfMonth')?.errors?.required"
                    >*required</small
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="row addcalendar-displaydays">
            <div class="col-md-12 mt-md-0 pb-0" *ngIf="message">
              <p class="fs12 color-grey11 mb-0">
                <span class="color-red fw-bold">*</span>
                {{ message }}
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="col-md-12 float-start">
          <div
            class="row mt-0"
            *ngIf="
              selectedRecurrence === 'Daily' ||
              selectedRecurrence === 'Monthly' ||
              selectedRecurrence === 'Yearly' ||
              selectedRecurrence === 'Weekly'
            "
          >
            <div class="col-md-12 p-0">
              <div class="form-group ms-3 w-100">
                <label class="fs12 fw600" for="recenddate">Recurrence End Date<sup>*</sup></label>
                <div class="input-group mb-3">
                  <input id="recenddate"
                    class="form-control fs10 material-input"
                    #dp="bsDatepicker"
                    bsDatepicker
                    formControlName="recurrenceEndDate"
                    placeholder="Recurrence End Date *"
                    [bsConfig]="{ isAnimated: true, showWeekNumbers: false, customTodayClass: 'today' }"
                    (ngModelChange)="showMonthlyRecurrence()"
                    [minDate]="recurrenceMinDate"
                  /><br />

                  <div
                    class="color-red"
                    *ngIf="
                    submitted && concreteRequest.get('recurrenceEndDate').errors
                    "
                  >
                    <small *ngIf="concreteRequest.get('recurrenceEndDate').errors.required"
                      >*Recurrence End Date is Required.</small
                    >
                  </div>

                    <span class="input-group-text">
                      <img
                        src="./assets/images/date.svg"
                        class="h-12px"
                        alt="Date"
                        (click)="dp.toggle()"
                        (keydown)="dp.toggle()"
                        [attr.aria-expanded]="dp.isOpen"
                      />
                    </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row py-3">
      <div class="col-md-3">
        <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Concrete Details</h1>
      </div>
      <div class="col-md-9 line-1">
      </div>
    </div>
    <div class="row">
      <div class="col-md-6 pt-0">
        <div class="form-group company-select mb-0" id="company-select8">
          <label class="fs12 fw600" for="consupplier">Concrete Supplier<sup>*</sup></label>
          <ng-multiselect-dropdown  id="consupplier"
            [placeholder]="' '"
            [settings]="concreteSupplierDropdownSettings"
            [data]="concreteSupplierDropdown"
            formControlName="concreteSupplier"
            (ngModelChange)="onEditSubmitForm();"
            class="mt-2"
          >
          </ng-multiselect-dropdown>
          <div
            class="color-red"
            *ngIf="submitted && concreteRequest.get('concreteSupplier').errors"
          >
            <small *ngIf="concreteRequest.get('concreteSupplier').errors.required"
              >*Concrete Supplier is Required.</small
            >
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="form-group customised-tag-input mb-0">
          <label class="fs12 fw600 mb-0"  for="mixdesign">Mix Design</label>
          <tag-input  id="mixdesign"
            [onlyFromAutocomplete]="false"
            [placeholder]="' '"
            formControlName="mixDesign"
            [(ngModel)]="mixDesignList"
            (ngModelChange)="checkMixDesignDuplication($event)"
            secondaryPlaceholder=" "
            [onTextChangeDebounce]="500"
            class="tag-layout"
            [identifyBy]="'id'"
            [displayBy]="'mixDesign'"
            (ngModelChange)="onEditSubmitForm();"
          >
            <tag-input-dropdown
              [showDropdownIfEmpty]="false"
              [displayBy]="'mixDesign'"
              [identifyBy]="'id'"
              [appendToBody]="false"
              [autocompleteItems]="mixDesignDropdown"
            >
              <ng-template let-item="item" let-index="index">
                <div class="tag-input-sample fs12">
                  {{ item.mixDesign }}
                </div>
              </ng-template>
            </tag-input-dropdown>
          </tag-input>
          <p class="color-grey11 fs12 mb-0">*Type and press enter to create the mix design tag</p>

        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-6">
        <div class="floating-concrete mt-3">
          <div class="form-group floating-label">
            <input   id="ordernumber"
              class="floating-input form-control fs12 px-0"
              type="text"
              placeholder=" "
              formControlName="concreteOrderNumber"
              (ngModelChange)="onEditSubmitForm();"
            />
            <label class="fs12 fw600 m-0 color-grey11"  for="ordernumber"
              >Order Number

            </label>

          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="floating-concrete mt-3">
          <div class="form-group floating-label">
            <input  id="slump"
              class="floating-input form-control fs12 px-0"
              type="text"
              placeholder=" "
              formControlName="slump"
              (ngModelChange)="onEditSubmitForm();"
            />
            <label class="fs12 fw600 m-0 color-grey11"  for="slump"
              >Slump
            </label>

          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-6">
        <div class="floating-concrete mt-1">
          <div class="form-group floating-label">
            <input
              class="floating-input form-control fs12 px-0"
              type="text"
              placeholder=" "  id="truck"
              formControlName="truckSpacingHours"
              (ngModelChange)="onEditSubmitForm();"
            />

            <label class="fs12 fw600 m-0 color-grey11"  for="truck"
              >Truck Spacing
            </label>

          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="floating-concrete mt-1">
          <div class="form-group floating-label">
            <input  id="quantity"
              class="floating-input form-control fs12 px-0"
              type="text"
              placeholder=" "
              formControlName="concreteQuantityOrdered"
              (ngModelChange)="onEditSubmitForm();"
            />

            <label class="fs12 fw600 m-0 color-grey11"  for="quantity"
              >Quantity Ordered (CY)
            </label>

          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-6">
        <div class="floating-concrete mt-1">
          <div class="form-group floating-label">
            <input
              class="floating-input form-control fs12 px-0"
              type="text"  id="orderpump"
              placeholder=" "
              formControlName="primerForPump"
              (ngModelChange)="onEditSubmitForm();"
            />
            <label class="fs12 fw600 m-0 color-grey11"  for="orderpump"
              >Primer ordered for the Pump
            </label>

          </div>
        </div>
      </div>
      <div class="col-md-6">
        <ul
          class="small-switch list-group list-group-horizontal justify-content-start mnb28 mb-1"
          id="switch-control4"
        >
          <span class="fs12 my-auto">Concrete Confirmed</span>
          <li class="fs12 list-group-item border-0 mt-1 p-0 color-grey11">
            <ui-switch
              switchColor="#fff"
              defaultBoColor="#CECECE"
              defaultBgColor="#CECECE"
              formControlName="isConcreteConfirmed"
              (change)="changeConcreteConfirmed($event)"
              class="ms-2"
              (ngModelChange)="onEditSubmitForm();"
            >
            </ui-switch>
          </li>
        </ul>
        <div
          class="fs12 fw700 text-black mt-1"
          *ngIf="concreteRequest.get('isConcreteConfirmed').value === true"
        >
          Confirmed on
          {{ concreteRequest.get('concreteConfirmedOn').value | date : 'medium' }}
        </div>
        <div
          class="color-red"
          *ngIf="submitted && concreteRequest.get('isConcreteConfirmed').errors"
        >
          <small *ngIf="concreteRequest.get('isConcreteConfirmed').errors.required"
            >*Concrete Confirmed is Required.</small
          >
        </div>
      </div>
    </div>
    <label class="fs12 fw600"  for="carbtrack">Carbon Tracking</label>
    <div class="row mb-0 mx-0 pb-3 border-light-gray border-left-green">
      <div class="col-md-6">
        <div class="form-group my-0 company-select equipment-select">
          <label class="fs12 fw600 mt10"  for="orgadd">Origination Address</label>
          <div class="border-bottom-grey">
            <input  id="orgadd"
              type="text"
              class="form-control fs12 material-input border-0 px-0 mt6"
              formControlName="originationAddress"
              placeholder="Origination Address"
              ngx-gp-autocomplete
              (onAddressChange)="handleAddressChange($event)"
              #placesRef="ngx-places"
            />
          </div>

        </div>
      </div>
      <div class="col-md-6 primary-tooltip">
        <div class="form-group mb-0 timezone-formgroup vehicleType-formgroup mt5">
          <label class="fs12 fw600"  for="vehtype">Vehicle Type</label>
          <ng-multiselect-dropdown  id="vehtype"
            [placeholder]="'Choose Vehicle Type'"
            [settings]="vehicleTypeDropdownSettings"
            [data]="vehicleTypes"
            (onSelect)="vehicleTypeSelected($event)"
            formControlName="vehicleType"
          >
          </ng-multiselect-dropdown>

        </div>
      </div>
    </div>
    <div class="row py-3">
      <div class="col-md-3">
        <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Pump Details</h1>
      </div>
      <div class="col-md-9">
        <div class="line-1"></div>
      </div>
    </div>
    <div class="row pb-2">
      <div class="col-md-6">
        <ul
          class="small-switch list-group list-group-horizontal justify-content-start mnb28"
          id="switch-control4"
        >
          <span class="fs12 my-auto">Pump Required</span>
          <li class="fs12 list-group-item border-0 p-0 mt-1 color-grey11">
            <ui-switch
              switchColor="#fff"
              defaultBoColor="#CECECE"
              defaultBgColor="#CECECE"
              class="ms-2"
              formControlName="isPumpRequired"
              (ngModelChange)="onEditSubmitForm();"
            >
            </ui-switch>
          </li>
        </ul>
      </div>
    </div>
    <div
      class="row align-items-end"
      *ngIf="concreteRequest.get('isPumpRequired').value === false ? false : true"
    >
      <div class="col-md-6 mt-2 pump-sizefield">
        <div class="form-group customised-tag-input mb-0">
          <label class="fs12 fw600" for="pumpsize">Pump Size<sup>*</sup></label>
          <tag-input id="pumpsize"
            [onlyFromAutocomplete]="false"
            [placeholder]="' '"
            formControlName="pumpSize"
            [(ngModel)]="pumpSizeList"
            (ngModelChange)="checkPumpSizeDuplication($event)"
            secondaryPlaceholder=" "
            [onTextChangeDebounce]="500"
            class="tag-layout"
            [identifyBy]="'id'"
            [displayBy]="'pumpSize'"
            (ngModelChange)="onEditSubmitForm();"
          >
            <tag-input-dropdown
              [showDropdownIfEmpty]="false"
              [displayBy]="'pumpSize'"
              [identifyBy]="'id'"
              [appendToBody]="false"
              [autocompleteItems]="pumpSizeDropdown"
            >
              <ng-template let-item="item" let-index="index">
                <div class="tag-input-sample fs12">
                  {{ item.pumpSize }}
                </div>
              </ng-template>
            </tag-input-dropdown>
          </tag-input>
          <p class="color-grey11 fs12 mb-0">*Type and press enter to create the pump size tag</p>
          <div class="color-red" *ngIf="submitted && concreteRequest.get('pumpSize').errors">
            <small>*Pump Size is Required</small>
          </div>
        </div>
      </div>
      <div class="col-md-6 mt-2">
        <label class="fs12 fw600 m-0" for="pumporder">Pump Ordered</label>
        <div class="input-group mb-2 edit-concrete-input">
          <input id="pumporder"
            class="form-control fs12 ps-2 fw500 material-input border-0"
            #dp="bsDatepicker"
            bsDatepicker
            placeholder="Pump Ordered"
            [bsConfig]="{ isAnimated: true, showWeekNumbers: false, customTodayClass: 'today' }"
            formControlName="pumpOrderedDate"
            (ngModelChange)="onEditSubmitForm();"
          />
            <span class="input-group-text border-0">
              <img
                src="./assets/images/date.svg"
                class="h-12px"
                alt="Date"
                (click)="dp.toggle()" (keydown)="dp.toggle()"
                [attr.aria-expanded]="dp.isOpen"
              />
            </span>
        </div>
        <div class="color-red" *ngIf="submitted && concreteRequest.get('pumpOrderedDate').errors">
          <small *ngIf="concreteRequest.get('pumpOrderedDate').errors.required"
            >*Pump Ordered is Required.</small
          >
        </div>
      </div>
    </div>

    <div class="row" *ngIf="concreteRequest.get('isPumpRequired').value === false ? false : true">
      <div class="col-md-6 mt-0">
        <div class="floating-concreted mt-3">
          <div class="form-group mb-0">
            <label class="fs12 fw600 m-0" for="pumplocation">Pump Location</label>
            <input id="pumplocation"
              class="floating-input form-control fs12 px-0"
              type="text"
              placeholder=" "
              formControlName="pumpLocation"
              (ngModelChange)="onEditSubmitForm();"
            />
          </div>
        </div>
        <div class="color-red" *ngIf="submitted && concreteRequest.get('pumpLocation').errors">
          <small *ngIf="concreteRequest.get('pumpLocation').errors.required"
            >*Pump Location is Required.</small
          >
        </div>
      </div>
      <div class="col-md-6 mt-1">
        <div class="form-group">
          <div class="d-flex mobile-display-block pt-0">
            <div class="input-group mb-0 delivery-time">
              <label class="fs12 fw600" for="pumpshowtime">Pump Show up Time:</label>
              <timepicker id="pumpshowtime"
                [formControlName]="'pumpWorkStart'"
                (ngModelChange)="changeDate1($event)"
                (keypress)="numberOnly($event)"
                class="mt-2"
              >
              </timepicker>
            </div>
            <div class="color-red" *ngIf="submitted && concreteRequest.get('pumpWorkStart').errors">
              <small *ngIf="concreteRequest.get('pumpWorkStart').errors.required"
                >*Pump Show up Time is Required</small
              >
            </div>
            <div class="input-group mb-0 delivery-time">
              <label class="fs12 fw600" for="pumpcomptime">Pump Completion Time:</label>
              <timepicker  id="pumpcomptime"
                [formControlName]="'pumpWorkEnd'"
                class="mt-2"
                (ngModelChange)="deliveryEndTimeChangeDetection()"
                (keypress)="numberOnly($event)"
              >
              </timepicker>
            </div>
            <div class="color-red" *ngIf="submitted && concreteRequest.get('pumpWorkEnd').errors">
              <small *ngIf="concreteRequest.get('pumpWorkEnd').errors.required"
                >*Pump Completion Time is Required</small
              >
            </div>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="concreteRequest.get('isPumpRequired').value === false ? false : true">
    <label class="fs12 fw600"  for="carbtracking">Carbon Tracking</label>
    <div class="row mb-0 mx-0 pb-3 border-light-gray border-left-green">
      <div class="col-md-6">
        <div class="form-group my-0 company-select equipment-select">
          <label class="fs12 fw600 mt10"  for="orgiadd">Origination Address</label>
          <div class="border-bottom-grey">
            <input  id="orgiadd"
              type="text"
              class="form-control fs12 material-input border-0 px-0 mt6"
              formControlName="originationAddressPump"
              placeholder="Origination Address"
              ngx-gp-autocomplete
              (onAddressChange)="handlePumpAddressChange($event)"
              #placesRef="ngx-places"
            />
          </div>

        </div>
      </div>
      <div class="col-md-6 primary-tooltip">
        <div class="form-group mb-0 timezone-formgroup vehicleType-formgroup mt5">
          <label class="fs12 fw600"  for="vehicletype">Vehicle Type</label>
          <ng-multiselect-dropdown  id="vehicletype"
            [placeholder]="'Choose Vehicle Type'"
            [settings]="vehicleTypeDropdownSettings"
            [data]="vehicleTypes"
            (onSelect)="pumpVehicleTypeSelected($event)"
            formControlName="vehicleTypePump"
          >
          </ng-multiselect-dropdown>

        </div>
      </div>
    </div>
    </div>
    <div
      class="row pb-2"
      *ngIf="concreteRequest.get('isPumpRequired').value === false ? false : true"
    >
      <div class="col-md-6">
        <div class="form-group mb-0">
          <ul
            class="small-switch list-group list-group-horizontal justify-content-start mnb28 mb-2"
            id="switch-control4"
          >
            <span class="fs12 my-auto mt5">Pump Confirmed</span>
            <li class="fs12 list-group-item border-0 p-0 mt-1 color-grey11">
              <ui-switch
                switchColor="#fff"
                defaultBoColor="#CECECE"
                defaultBgColor="#CECECE"
                formControlName="isPumpConfirmed"
                (change)="changePumpConfirmed($event)"
                class="ms-2"
                (ngModelChange)="onEditSubmitForm();"
              >
              </ui-switch>
            </li>
          </ul>
          <div
            class="fs12 fw700 text-black ng-star-inserted mb-0"
            *ngIf="concreteRequest.get('pumpConfirmedOn').value"
          >
            Confirmed on
            {{ concreteRequest.get('pumpConfirmedOn').value | date : 'medium' }}
          </div>
        </div>
      </div>
    </div>

    <div class="row py-2 mt-2" *ngIf="currentEditItem.status === 'Completed'">
      <div class="col-md-3 pump-header">
        <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Other Details</h1>
      </div>
      <div class="col-md-9">
        <div class="line-1 line-2"></div>
      </div>
    </div>
    <div class="row mt-2" *ngIf="currentEditItem.status === 'Completed'">
      <div class="w-50 float-start">
        <div class="col-md-12">
          <label class="fs12 fw600 m-0"  for="completiontime">Completion time</label>
        </div>
        <div class="col-md-6 float-start">
          <div class="form-group d-flex">
            <select id="hrs"
              class="form-control fs12 material-input px-2"
              formControlName="hoursToCompletePlacement"
              (ngModelChange)="onEditSubmitForm();"
            >
              <option value="" disabled selected hidden>Select</option>
              <option *ngFor="let hrs of hoursDropdown" value="{{ hrs }}">
                {{ hrs }}
              </option>
            </select>
            <label class="fs10 my-auto" for="hrs">Hrs</label>
          </div>
        </div>
        <div class="col-md-6 float-start">
          <div class="form-group d-flex">
            <select id="mins"
              class="form-control fs12 material-input px-2"
              formControlName="minutesToCompletePlacement"
              (ngModelChange)="onEditSubmitForm();"
            >
              <option value="" disabled selected hidden>Select</option>
              <option *ngFor="let mins of minutesDropdown" value="{{ mins }}">
                {{ mins }}
              </option>
            </select>
            <label class="fs10 my-auto" for="mins">Mins</label>
          </div>
        </div>
      </div>
      <div class="col-md-6 float-start">
        <div class="floating-concreted mt-1">
          <div class="form-group">
            <label class="fs12 fw600 m-0" for="cubic">Total Cubic Yards Placed</label>
            <input  id="cubic"
              class="floating-input form-control fs12 px-0"
              type="text"
              placeholder=" "
              formControlName="cubicYardsTotal"
              (ngModelChange)="onEditSubmitForm();"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="row pb-2 ms-0 mb-0 mt-2">
      <div class="col-md-12 pl0 ps-0">
        <label class="fs12 fw600"  for="notes">Notes</label>
        <textarea  id="notes"
          class="form-control fs11 radius0 mt-1"
          rows="2"
          formControlName="notes"
          (ngModelChange)="onEditSubmitForm();"
        ></textarea>
      </div>
    </div>
    <div class="modal-footer border-0 justify-content-center add-calendar-footer">
      <div class="mt-0 mb15 text-center">
        <button
          class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3 px-2rem"
          type="button"
          (click)="close(cancelConfirmation)"
        >
          Cancel
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem"
          [disabled]="(formSubmitted && concreteRequest.valid) || formEdited"
          (click)="onSubmit()"
        >
          <em
            class="fa fa-spinner"
            aria-hidden="true"
            *ngIf="formSubmitted && concreteRequest.valid"
          ></em>
          Submit
        </button>
      </div>
    </div>
  </form>
</div>
<div class="modal-body text-center" *ngIf="modalLoader">Loading...</div>

<!--Confirmation Popup-->
<div id="confirm-popup6">
  <ng-template #cancelConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">Are you sure you want to cancel?</p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetForm('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="resetForm('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>

<!--Recurrence Edit Confirmation Popup-->
<div id="confirm-popup7">
  <ng-template #cancelRecurrence>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">If recurrence was edited may result in the loss of existing booking data</p>
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">Are you sure you want to continue?</p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="recurrenceSubmit('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="recurrenceSubmit('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
