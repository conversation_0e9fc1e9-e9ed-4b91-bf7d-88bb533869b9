/**
 * Google Maps API mock for testing
 * This file provides a comprehensive mock of the Google Maps API to avoid type conflicts in tests
 */

export function setupGoogleMapsMock(): void {
  // Mock LatLng class
  const mockLatLng = class {
    private readonly _lat: number;
    private readonly _lng: number;

    constructor(lat: number, lng: number) {
      this._lat = lat;
      this._lng = lng;
    }

    lat() { return this._lat; }
    lng() { return this._lng; }
    equals() { return true; }
    toJSON() { return { lat: this._lat, lng: this._lng }; }
    toUrlValue() { return `${this._lat},${this._lng}`; }
  };

  // Mock Map class
  const mockMap = class {
    static readonly DEMO_MAP_ID = 'DEMO_MAP_ID';
    panTo() {  /* */}
  };

  // Mock Marker class
  const mockMarker = class {
    static readonly MAX_ZINDEX = 1000000;
    setMap() {  /* */}
    setPosition() {  /* */}
  };

  // Mock InfoWindow class
  const mockInfoWindow = class {
    open() {  /* */}
    close() {  /* */}
    focus() {  /* */}
    getContent() { return null; }
    getHeaderContent() { return null; }
    getHeaderDisabled() { return false; }
    getPixelOffset() { return null; }
    getPosition() { return null; }
    getZIndex() { return 0; }
    setContent() {  /* */}
    setHeaderContent() {  /* */}
    setHeaderDisabled() {  /* */}
    setOptions() {  /* */}
    setPixelOffset() {  /* */}
    setPosition() {  /* */}
    setZIndex() {  /* */}
    addListener() { return null; }
    bindTo() {  /* */}
    get() { return null; }
    notify() {  /* */}
    set() {  /* */}
    setValues() {  /* */}
    unbind() {  /* */}
    unbindAll() {  /* */}
  };

  // Mock Geocoder class
  const mockGeocoder = class {
    geocode() {
      /* */
    }
  };

  // Use Object.defineProperty to avoid type conflicts
  Object.defineProperty(window, 'google', {
    value: {
      maps: {
        Map: mockMap,
        MapTypeId: {
          SATELLITE: 'satellite',
          ROADMAP: 'roadmap',
          HYBRID: 'hybrid',
          TERRAIN: 'terrain'
        },
        Animation: {
          DROP: 1.0,
          BOUNCE: 0.0
        },
        LatLng: mockLatLng,
        Marker: mockMarker,
        InfoWindow: mockInfoWindow,
        Geocoder: mockGeocoder
      }
    },
    writable: true,
    configurable: true
  });
}

export function cleanupGoogleMapsMock(): void {
  delete (window as any).google;
}
