import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReportsComponent } from './reports.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { ProjectService } from '../services/profile/project.service';
import { of } from 'rxjs';
import { TemplateRef } from '@angular/core';

describe('ReportsComponent', () => {
  let component: ReportsComponent;
  let fixture: ComponentFixture<ReportsComponent>;
  let modalService: BsModalService;
  let projectService: ProjectService;
  let formBuilder: UntypedFormBuilder;

  const mockProjectService = {
    projectParent: of({
      ProjectId: '123',
      ParentCompanyId: '456'
    }),
    listAllMember: jest.fn()
  };

  const mockBsModalRef = {
    hide: jest.fn()
  };

  const mockBsModalService = {
    show: jest.fn().mockReturnValue(mockBsModalRef)
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ReportsComponent],
      imports: [ReactiveFormsModule],
      providers: [
        { provide: BsModalService, useValue: mockBsModalService },
        { provide: ProjectService, useValue: mockProjectService },
        UntypedFormBuilder
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ReportsComponent);
    component = fixture.componentInstance;
    modalService = TestBed.inject(BsModalService);
    projectService = TestBed.inject(ProjectService);
    formBuilder = TestBed.inject(UntypedFormBuilder);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.currentPageNo).toBe(1);
    expect(component.pageSize).toBe(25);
    expect(component.filterCount).toBe(0);
    expect(component.isTemplate).toBe(true);
    expect(component.isScheduled).toBe(false);
    expect(component.isSaved).toBe(false);
    expect(component.isRecent).toBe(false);
    expect(component.showSearchbar).toBe(false);
    expect(component.search).toBe('');
  });

  it('should initialize filter form', () => {
    component.filterDetailsForm();
    expect(component.filterForm.get('createdUserId')).toBeTruthy();
    expect(component.filterForm.get('reportName')).toBeTruthy();
    expect(component.filterForm.get('templateType')).toBeTruthy();
    expect(component.filterForm.get('lastRun')).toBeTruthy();
    expect(component.filterForm.get('nextRun')).toBeTruthy();
  });

  it('should get members when ProjectId and ParentCompanyId are available', () => {
    const mockResponse = {
      data: [
        {
          RoleId: 2,
          UserId: '1',
          User: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>'
          }
        },
        {
          RoleId: 1,
          UserId: '2',
          User: {
            firstName: 'Jane',
            lastName: 'Smith',
            email: '<EMAIL>'
          }
        }
      ]
    };

    mockProjectService.listAllMember.mockReturnValue(of(mockResponse));
    component.getMembers();

    expect(projectService.listAllMember).toHaveBeenCalledWith({
      ProjectId: '123',
      ParentCompanyId: '456'
    });
    expect(component.memberList.length).toBe(1);
    expect(component.memberList[0].author).toBe('John Doe');
  });

  it('should handle filter submission correctly', () => {
    component.filterDetailsForm();
    component.filterForm.patchValue({
      createdUserId: '1',
      reportName: 'Test Report',
      templateType: 'Delivery',
      lastRun: '2024-03-20',
      nextRun: '2024-03-21'
    });

    component.modalRef = mockBsModalRef as any;
    component.filterSubmit();

    expect(component.filterCount).toBe(4);
    expect(component.filterFormValues).toEqual({
      createdUserId: '1',
      reportName: 'Test Report',
      templateType: 'Delivery',
      lastRun: '2024-03-20',
      nextRun: '2024-03-21'
    });
    expect(mockBsModalRef.hide).toHaveBeenCalled();
  });

  it('should reset filter correctly', () => {
    component.filterDetailsForm();
    component.filterForm.patchValue({
      createdUserId: '1',
      reportName: 'Test Report'
    });
    component.search = 'test';
    component.filterCount = 2;
    component.modalRef = mockBsModalRef as any;

    component.resetFilter();

    expect(component.filterCount).toBe(0);
    expect(component.search).toBe('');
    expect(component.filterFormValues).toBeNull();
    expect(mockBsModalRef.hide).toHaveBeenCalled();
  });

  it('should handle search correctly', () => {
    component.getSearch('test');
    expect(component.showSearchbar).toBe(true);
    expect(component.search).toBe('test');

    component.getSearch('');
    expect(component.showSearchbar).toBe(false);
    expect(component.search).toBe('');
  });

  it('should handle tab clicks correctly', () => {
    component.inTemplateClick();
    expect(component.isTemplate).toBe(true);
    expect(component.isScheduled).toBe(false);
    expect(component.isSaved).toBe(false);
    expect(component.isRecent).toBe(false);

    component.inScheduledClick();
    expect(component.isTemplate).toBe(false);
    expect(component.isScheduled).toBe(true);
    expect(component.isSaved).toBe(false);
    expect(component.isRecent).toBe(false);

    component.inSavedClick();
    expect(component.isTemplate).toBe(false);
    expect(component.isScheduled).toBe(false);
    expect(component.isSaved).toBe(true);
    expect(component.isRecent).toBe(false);

    component.inRecentClick();
    expect(component.isTemplate).toBe(false);
    expect(component.isScheduled).toBe(false);
    expect(component.isSaved).toBe(false);
    expect(component.isRecent).toBe(true);
  });

  it('should change page number', () => {
    component.changePageNo(2);
    expect(component.currentPageNo).toBe(2);
  });

  it('should open delete modal', () => {
    const template = {} as TemplateRef<any>;
    component.openDeleteModal(template);
    expect(modalService.show).toHaveBeenCalledWith(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'delete-modal custom-modal'
    });
  });

  it('should open filter modal', () => {
    const template = {} as TemplateRef<any>;
    component.openModal(template);
    expect(modalService.show).toHaveBeenCalledWith(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-sm filter-popup custom-modal'
    });
  });

  it('should handle keyboard events correctly', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const spy = jest.spyOn(component, 'clear');
    
    component.handleDownKeydown(event, null, 'clear');
    expect(spy).toHaveBeenCalled();

    const modalSpy = jest.spyOn(component, 'openModal');
    component.handleDownKeydown(event, 'template', 'modal');
    expect(modalSpy).toHaveBeenCalledWith('template');
  });
});
