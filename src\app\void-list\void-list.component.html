<section class="page-section">
  <div class="page-inner-content">
    <div class="top-header my-3">
      <div class="row pt-md-40px">
        <div class="col-md-8">
          <div class="top-btn">
            <ul class="list-group list-group-horizontal">

            </ul>
          </div>
        </div>
        <div class="col-md-4">
          <div class="top-filter">
            <ul class="list-group list-group-horizontal justify-content-end">
              <li class="list-group-item p0 border-0 bg-transparent me-2">
                <div class="search-icon">
                  <input
                    class="form-control fs12 color-grey8"
                    [ngClass]="showSearchbar ? 'input-hover-disable' : 'input-search'"
                    placeholder="What are you looking for?"
                    (input)="getSearchNDR($event.target.value)"
                    [(ngModel)]="search"
                  />
                  <div class="icon">
                    <img
                      src="./assets/images/cross-close.svg"
                      *ngIf="showSearchbar"
                      (click)="clear()"
                      (keydown)="handleModalKeydown($event)"
                      alt="close-cross"
                    />
                    <em class="fa fa-search fs12 color-grey8" *ngIf="!showSearchbar"></em>
                  </div>
                </div>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent me-2 position-relative">
                <div class="filter-icon" (click)="openModal1(filter)" (keydown)="handleModalKeydown($event, filter)">
                  <img src="./assets/images/filter.svg" class="h-12px icon" alt="Filter" />
                </div>
                <div
                  class="bg-orange rounded-circle position-absolute text-white filter-count"
                  *ngIf="filterCount > 0"
                >
                  <p class="m-0 text-center fs10">{{ filterCount }}</p>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="row mt-md-4 mt-2">
      <div class="col-12">
        <div class="page-card bg-white rounded">
          <div class="table-responsive rounded tab-grid">
            <table class="table table-custom mb-0" aria-describedby="Emptable">
              <thead>

                <th scope="col" resizable>
                  ID
                  <span>
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('id', 'ASC')"
                      (keydown)="handleSortKeydown($event,'id', 'ASC')"
                      *ngIf="sortColumn !== 'id'"
                    />
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('id', 'ASC')"
                      (keydown)="handleSortKeydown($event,'id', 'ASC')"
                      *ngIf="sort === 'DESC' && sortColumn === 'id'"
                    />
                    <img
                      src="./assets/images/up-chevron.svg"
                      alt="up-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('id', 'DESC')"
                      (keydown)="handleSortKeydown($event,'id', 'DESC')"
                      *ngIf="sort === 'ASC' && sortColumn === 'id'"
                    />
                  </span>
                </th>
                <th scope="col" resizable>
                  Description
                  <span>
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('description', 'ASC')"
                      (keydown)="handleSortKeydown($event,'description', 'ASC')"
                      *ngIf="sortColumn !== 'description'"
                    />
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('description', 'ASC')"
                      (keydown)="handleSortKeydown($event,'description', 'ASC')"
                      *ngIf="sort === 'DESC' && sortColumn === 'description'"
                    />
                    <img
                      src="./assets/images/up-chevron.svg"
                      alt="up-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('description', 'DESC')"
                      (keydown)="handleSortKeydown($event,'description', 'DESC')"
                      *ngIf="sort === 'ASC' && sortColumn === 'description'"
                    />
                  </span>
                </th>
                <th scope="col" resizable>Date and Time</th>
                <th scope="col" resizable>
                  Approved By
                  <span>
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('approvedUser', 'ASC')"
                      (keydown)="handleSortKeydown($event,'approvedUser', 'ASC')"
                      *ngIf="sortColumn !== 'approvedUser'"
                    />
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('approvedUser', 'ASC')"
                      (keydown)="handleSortKeydown($event,'approvedUser', 'ASC')"
                      *ngIf="sort === 'DESC' && sortColumn === 'approvedUser'"
                    />
                    <img
                      src="./assets/images/up-chevron.svg"
                      alt="up-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('approvedUser', 'DESC')"
                      (keydown)="handleSortKeydown($event,'approvedUser', 'DESC')"
                      *ngIf="sort === 'ASC' && sortColumn === 'approvedUser'"
                    />
                  </span>
                </th>
                <th scope="col" resizable>Equipment</th>
                <th scope="col" resizable>
                  Booking Type
                  <span>
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('requestType', 'ASC')"
                      (keydown)="handleSortKeydown($event,'requestType', 'ASC')"
                      *ngIf="sortColumn !== 'requestType'"
                    />
                    <img
                      src="./assets/images/down-chevron.svg"
                      alt="down-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('requestType', 'ASC')"
                      (keydown)="handleSortKeydown($event,'requestType', 'ASC')"
                      *ngIf="sort === 'DESC' && sortColumn === 'requestType'"
                    />
                    <img
                      src="./assets/images/up-chevron.svg"
                      alt="up-arrow"
                      class="h-10px ms-2"
                      (click)="sortByField('requestType', 'DESC')"
                      (keydown)="handleSortKeydown($event,'requestType', 'DESC')"
                      *ngIf="sort === 'ASC' && sortColumn === 'requestType'"
                    />
                  </span>
                </th>
                <th scope="col" resizable>
                  Restore
                 </th>
              </thead>
              <tbody>
                <tr
                  *ngFor="
                    let item of voidPageDeliveryList
                      | paginate
                        : { itemsPerPage: pageSize, currentPage: pageNo, totalItems: totalCount };
                    let i = index
                  "
                >

                  <td>
                    <span
                      *ngIf="item.requestType === 'deliveryRequest'"
                      (click)="
                        openIdModal(
                          deliveryDetails,
                          craneRequestDetail,
                          concreteRequestDetail,
                          inspectionRequestDetail,
                          item
                        )
                      "
                      (keydown)="handleKeyDown($event, deliveryDetails, craneRequestDetail, concreteRequestDetail, inspectionRequestDetail, item)"

                      class="c-pointer"
                      ><u>{{ item.DeliveryId }}</u></span
                    >
                    <span
                      *ngIf="
                        item.requestType === 'deliveryRequestWithCrane' ||
                        item.requestType === 'craneRequest'
                      "
                      (click)="
                        openIdModal(
                          deliveryDetails,
                          craneRequestDetail,
                          concreteRequestDetail,
                          inspectionRequestDetail,
                          item
                        )
                      "
                      (keydown)="handleKeyDown($event, deliveryDetails, craneRequestDetail, concreteRequestDetail, inspectionRequestDetail, item)"

                      class="c-pointer"
                      ><u>{{ item.CraneRequestId }}</u></span
                    >
                    <span
                      *ngIf="item.requestType === 'concreteRequest'"
                      (click)="
                        openIdModal(
                          deliveryDetails,
                          craneRequestDetail,
                          concreteRequestDetail,
                          inspectionRequestDetail,
                          item
                        )
                      "
                      (keydown)="handleKeyDown($event, deliveryDetails, craneRequestDetail, concreteRequestDetail, inspectionRequestDetail, item)"

                      class="c-pointer"
                      ><u>{{ item.ConcreteRequestId }}</u></span
                    >
                    <span
                      *ngIf="item.requestType === 'inspectionRequestWithCrane' || item.requestType === 'inspectionRequest'"
                      (click)="
                        openIdModal(
                          deliveryDetails,
                          craneRequestDetail,
                          concreteRequestDetail,
                          inspectionRequestDetail,
                          item
                        )
                      "
                      (keydown)="handleKeyDown($event, deliveryDetails, craneRequestDetail, concreteRequestDetail, inspectionRequestDetail, item)"

                      class="c-pointer"
                      ><u>{{ item.InspectionId }}</u></span
                    >
                  </td>
                  <td class="w-300px">
                    <span
                      (click)="
                        openIdModal(
                          deliveryDetails,
                          craneRequestDetail,
                          concreteRequestDetail,
                          inspectionRequestDetail,
                          item
                        )
                      "
                      (keydown)="handleKeyDown($event, deliveryDetails, craneRequestDetail, concreteRequestDetail, inspectionRequestDetail, item)"

                      class="c-pointer"
                      ><u>{{ item.description }}</u></span
                    >
                  </td>
                  <td>
                    <span
                      *ngIf="
                        item.requestType === 'deliveryRequest' ||
                        item.requestType === 'deliveryRequestWithCrane'
                      "
                    >
                      {{ item.deliveryStart | date : 'medium' }}
                    </span>
                    <span *ngIf="item.requestType === 'craneRequest'">
                      {{ item.craneDeliveryStart | date : 'medium' }}
                    </span>
                    <span *ngIf="item.requestType === 'concreteRequest'">
                      {{ item.concretePlacementStart | date : 'medium' }}
                    </span>
                    <span *ngIf="item.requestType === 'inspectionRequest' || item.requestType === 'inspectionRequestWithCrane'
                    ">
                      {{ item.inspectionStart | date : 'medium' }}
                    </span>
                  </td>
                  <td
                    (click)="
                      openIdModal(deliveryDetails, craneRequestDetail, concreteRequestDetail,inspectionRequestDetail, item)
                    "
                    (keydown)="handleKeyDown($event, deliveryDetails, craneRequestDetail, concreteRequestDetail, inspectionRequestDetail, item)"

                    class="c-pointer"
                    *ngIf="item.requestType !== 'concreteRequest'"
                  >
                    {{ item?.approverDetails?.User?.firstName }}
                    {{ item?.approverDetails?.User?.lastName }}
                  </td>
                  <td
                    (click)="
                      openIdModal(deliveryDetails, craneRequestDetail, concreteRequestDetail,inspectionRequestDetail, item)
                    "
                    (keydown)="handleKeyDown($event, deliveryDetails, craneRequestDetail, concreteRequestDetail, inspectionRequestDetail, item)"

                    class="c-pointer"
                    *ngIf="item.requestType === 'concreteRequest'"
                  >
                    -
                  </td>
                  <td
                    (click)="
                      openIdModal(deliveryDetails, craneRequestDetail, concreteRequestDetail,inspectionRequestDetail, item)
                    "
                    (keydown)="handleKeyDown($event, deliveryDetails, craneRequestDetail, concreteRequestDetail, inspectionRequestDetail, item)"

                    class="c-pointer"
                    *ngIf="item.requestType !== 'concreteRequest'"
                  >
                    {{ item.equipmentDetails[0]?.Equipment?.equipmentName ? item.equipmentDetails[0]?.Equipment?.equipmentName : '-' }}

                  </td>
                  <td
                    (click)="
                      openIdModal(deliveryDetails, craneRequestDetail, concreteRequestDetail,inspectionRequestDetail, item)
                    "
                    (keydown)="handleKeyDown($event, deliveryDetails, craneRequestDetail, concreteRequestDetail, inspectionRequestDetail, item)"

                    class="c-pointer"
                    *ngIf="item.requestType === 'concreteRequest'"
                  >
                    -
                  </td>
                  <td
                    class="c-pointer"
                    *ngIf="item.requestType === 'deliveryRequest'"
                    (click)="
                      openIdModal(deliveryDetails, craneRequestDetail, concreteRequestDetail,inspectionRequestDetail, item)
                    "
                    (keydown)="handleKeyDown($event, deliveryDetails, craneRequestDetail, concreteRequestDetail, inspectionRequestDetail, item)"

                  >
                    Delivery Booking
                  </td>
                  <td
                    class="c-pointer"
                    *ngIf="item.requestType === 'craneRequest'"
                    (click)="
                      openIdModal(deliveryDetails, craneRequestDetail, concreteRequestDetail,inspectionRequestDetail, item)
                    "
                    (keydown)="handleKeyDown($event, deliveryDetails, craneRequestDetail, concreteRequestDetail, inspectionRequestDetail, item)"

                  >
                    Crane Booking
                  </td>
                  <td
                    class="c-pointer"
                    *ngIf="item.requestType === 'deliveryRequestWithCrane'"
                    (click)="
                      openIdModal(deliveryDetails, craneRequestDetail, concreteRequestDetail,inspectionRequestDetail, item)
                    "
                    (keydown)="handleKeyDown($event, deliveryDetails, craneRequestDetail, concreteRequestDetail, inspectionRequestDetail, item)"

                  >
                    Delivery and Crane Booking
                  </td>
                  <td
                    class="c-pointer"
                    *ngIf="item.requestType === 'concreteRequest'"
                    (click)="
                      openIdModal(deliveryDetails, craneRequestDetail, concreteRequestDetail,inspectionRequestDetail, item)
                    "
                    (keydown)="handleKeyDown($event, deliveryDetails, craneRequestDetail, concreteRequestDetail, inspectionRequestDetail, item)"
                  >
                    Concrete Booking
                  </td>
                  <td
                  class="c-pointer"
                  *ngIf="item.requestType === 'inspectionRequest'"
                  (click)="
                    openIdModal(deliveryDetails, craneRequestDetail, concreteRequestDetail,inspectionRequestDetail, item)
                  "
                  (keydown)="handleKeyDown($event, deliveryDetails, craneRequestDetail, concreteRequestDetail, inspectionRequestDetail, item)"

                >
                  Inspection Booking
                </td>
                <td
                  class="c-pointer"
                  *ngIf="item.requestType === 'inspectionRequestWithCrane'"
                  (click)="
                    openIdModal(deliveryDetails, craneRequestDetail, concreteRequestDetail,inspectionRequestDetail, item)
                  "
                  (keydown)="handleKeyDown($event, deliveryDetails, craneRequestDetail, concreteRequestDetail, inspectionRequestDetail, item)"

                >
                  Inspection and Crane Booking
                </td>
                <td>
                  <ul class="list-inline">
                    <li
                    class="list-inline-item"
                    (click)="openDeleteModal(-1, deleteList)"
                    [disabled]="checkSelectedRow()"  (keydown)="handleDeleteKeydown($event,-1, deleteList)"
                    title="Restore"
                    >
                      <a href="javascript:void(0)"><img
                        src="./assets/images/sidemenu-icons/restore4.png"
                        alt="edit"
                        class="restore-icon action-icon"
                        (click)="setSelectedItem(i)"  (keydown)="handleToggleKeydown($event, i)"
                    /></a>
                    </li>
                  </ul>
                </td>
                </tr>
              </tbody>
              <tr *ngIf="loader == true">
                <td colspan="7" class="text-center">
                  <div class="fs18 fw-bold cairo-regular my-5 text-black">Loading...</div>
                </td>
              </tr>
              <tr *ngIf="loader == false && voidPageDeliveryList.length == 0">
                <td colspan="7" class="text-center">
                  <div class="fs18 fw-bold cairo-regular my-5 text-black">
                    No Records Found
                  </div>
                </td>
              </tr>
            </table>
          </div>
          <div
            class="tab-pagination px-2"
            id="tab-pagination12"
            *ngIf="loader == false && totalCount > 25"
          >
            <div class="row">
              <div class="col-md-4 align-items-center">
                <ul class="list-inline my-3">
                  <li class="list-inline-item notify-pagination">
                    <label for="showEnt" class="fs12 color-grey4">Show entries</label>
                  </li>
                  <li class="list-inline-item">
                    <select id="showEnt"
                      class="w-auto form-select fs12 color-grey4 h30"
                      (change)="changePageSize($event.target.value)"
                      ngModel="{{ pageSize }}"
                    >
                      <option value="25">25</option>
                      <option value="50">50</option>
                      <option value="100">100</option>
                      <option value="150">150</option>
                    </select>
                  </li>
                </ul>
              </div>
              <div class="col-md-8 text-start">
                <div class="my-3 position-relative d-inline-block">
                  <pagination-controls
                    (pageChange)="changePageNo($event)"
                    previousLabel=""
                    nextLabel=""
                  >
                  </pagination-controls>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Delivery Details View Popup-->
<ng-template #deliveryDetails>
  <div class="modal-header">
    <h2 class="fs14 fw-bold cairo-regular color-text7 my-1">
      <img src="./assets/images/delivery-pop.svg" alt="Delivery" class="me-2" />Delivery Details
    </h2>
    <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body pt-0 px-0 pb-4">
    <div class="delivery-details-content" id="delivery-content5">
      <tabset>
        <tab heading="Details" id="tab1">
          <app-details></app-details>
        </tab>
        <tab heading="Attachments">
          <app-attachments></app-attachments>
        </tab>
        <tab heading="Comments">
          <app-comments></app-comments>
        </tab>
        <tab heading="History">
          <app-history></app-history>
        </tab>
      </tabset>
    </div>
  </div>
</ng-template>

<!-- Delivery Details View Popup-->
<ng-template #craneRequestDetail>
  <div class="modal-header">
    <h2 class="fs14 fw-bold cairo-regular color-text7 my-1">
      <img src="./assets/images/delivery-pop.svg" alt="Delivery" class="me-2" />Crane Booking
      Details
    </h2>
    <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body pt-0 px-0 pb-4">
    <div class="delivery-details-content" id="delivery-content5">
      <tabset>
        <tab heading="Details" id="tab1">
          <app-crane-request-detail-view-content></app-crane-request-detail-view-content>
        </tab>
        <tab heading="Attachments">
          <app-crane-request-attachment></app-crane-request-attachment>
        </tab>
        <tab heading="Comments">
          <app-crane-request-comment></app-crane-request-comment>
        </tab>
        <tab heading="History">
          <app-crane-request-history></app-crane-request-history>
        </tab>
      </tabset>
    </div>
  </div>
</ng-template>

<!-- modal -->
<div id="filter-temp9">
  <!--Filter Modal-->
  <ng-template #filter>
    <div class="modal-header border-0 pb-0">
      <h3 class="fs14 fw-bold cairo-regular color-text7 my-0">Filter</h3>
      <button type="button" class="close ms-auto" aria-label="Close" (click)="close()">
        <span aria-hidden="true"
          ><img src="./assets/images/modal-close.svg" alt="Modal Close"
        /></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="filter-content">
        <form
          class="custom-material-form"
          id="filter-form4"
          [formGroup]="filterForm"
          (ngSubmit)="filterSubmit()"
          novalidate
        >
          <div class="row">
            <div class="col-md-12">
              <div class="input-group mb-3">
                <input
                  type="text"
                  class="form-control fs12 material-input"
                  placeholder="Desciription"
                  formControlName="descriptionFilter"
                />
                  <span class="input-group-text">
                    <img src="./assets/images/search-icon.svg" alt="Search" />
                  </span>
              </div>
              <div class="input-group mb-3">
                <input
                  class="form-control fs12 material-input"
                  #dp="bsDatepicker"
                  bsDatepicker
                  formControlName="dateFilter"
                  placeholder="Delivery Date"
                  [bsConfig]="{
                    isAnimated: true,
                    showWeekNumbers: false,
                    customTodayClass: 'today'
                  }"
                />
              </div>
              <div class="form-group">
                <select
                  class="form-control fs12 material-input"
                  id="companyFilter1"
                  formControlName="companyFilter"
                >
                  <option value="" disabled selected hidden>Company</option>
                  <option *ngFor="let item of companyList" value="{{ item?.companyName }}">
                    {{ item.companyName }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="memberFilter">
                  <option value="" disabled selected hidden>Responsible Persons</option>
                  <ng-container *ngFor="let item of memberList">
                    <option *ngIf="item.status === 'pending' && !item.isGuestUser" value="{{ item.id }}">
                      {{ item.User?.email }}
                    </option>
                    <option *ngIf="item.status === 'pending' && item.isGuestUser" value="{{ item.id }}">
                      {{ item.User?.email }}(Guest)
                    </option>
                    <option *ngIf="item.status === 'completed'" value="{{ item.id }}">
                      {{ item.User?.firstName }}{{ item.User?.lastName }}
                    </option>
                  </ng-container>
                </select>
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="locationFilter">
                  <option value="" disabled selected hidden>Select Location</option>
                  <option *ngFor="let item of locationDropdown" value="{{ item.location }}">
                    {{ item.location }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="mixDesignFilter">
                  <option value="" disabled selected hidden>Select Mix Design</option>
                  <option *ngFor="let item of mixDesignDropdown" value="{{ item.mixDesign }}">
                    {{ item.mixDesign }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="gateFilter">
                  <option value="" disabled selected hidden>Gate</option>
                  <option *ngFor="let item of gateList" value="{{ item.id }}">
                    {{ item.gateName }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="equipmentFilter">
                  <option value="" disabled selected hidden>Equipment</option>
                  <option *ngFor="let item of equipmentList" value="{{ item.equipmentName }}">
                    {{ item.equipmentName }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <select
                  class="form-control fs12 material-input"
                  id="statusFilter2"
                  formControlName="statusFilter"
                >
                  <option value="" disabled selected hidden>Status</option>
                  <option *ngFor="let item of wholeStatus" value="{{ item }}">{{ item }}</option>
                </select>
              </div>
              <div class="row justify-content-end">
                <button
                  class="btn btn-orange radius20 col-4 mt-2 fs12 fw-bold cairo-regular mx-1"
                  type="submit"
                >
                  Apply
                </button>
                <button
                  class="btn btn-orange radius20 fs12 col-4 mt-2 fw-bold cairo-regular mx-1"
                  type="button"
                  (click)="resetFilter()"
                >
                  Reset
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
</div>

<ng-template #deleteList>
  <div class="modal-body">
    <div class="text-center my-4" *ngIf="!remove">
      <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
        Are you sure you you want to Restore '{{
          voidPageDeliveryList[currentRestoreId]?.description | titlecase
        }}'?
      </p>
      <button
        class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
        (click)="modalRef.hide()"
      >
        No
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
        type="submit"
        (click)="restoreRequest()"
        [disabled]="restoreSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="restoreSubmitted"></em>Yes
      </button>
    </div>
    <div class="text-center my-4" id="remove-popup6" *ngIf="remove">
      <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
        Are you sure you want to Restore?
      </p>
      <button
        class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
        (click)="modalRef.hide()"
      >
        No
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
        type="submit"
        (click)="restoreItemRequest()"
        [disabled]="restoreSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="restoreSubmitted"></em>Yes
      </button>
    </div>
  </div>
</ng-template>
<!--Filter Modal-->

<!-- Delivery Details View Popup-->
<ng-template #concreteRequestDetail>
  <div class="modal-header">
    <h2 class="fs14 fw-bold cairo-regular color-text7 my-1">
      <img src="./assets/images/delivery-pop.svg" alt="Delivery" class="me-2" />Concrete Booking
      Details
    </h2>
    <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body pt-0 px-0 pb-4">
    <div class="delivery-details-content" id="delivery-content5">
      <tabset>
        <tab heading="Details" id="tab1" active="active">
          <app-concrete-detail-content> </app-concrete-detail-content>
        </tab>
        <tab heading="Attachments">
          <app-concrete-attachments> </app-concrete-attachments>
        </tab>
        <tab heading="Comments">
          <app-concrete-comment> </app-concrete-comment>
        </tab>
        <tab heading="History">
          <app-concrete-history> </app-concrete-history>
        </tab>
      </tabset>
    </div>
  </div>
</ng-template>

<ng-template #inspectionRequestDetail>
  <div class="modal-header">
    <h2 class="fs14 fw-bold cairo-regular color-text7 my-1">
      <img src="./assets/images/delivery-pop.svg" alt="Delivery" class="me-2" />Inspection Booking
      Details
    </h2>
    <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body pt-0 px-0 pb-4">
    <div class="delivery-details-content" id="delivery-content5">
      <tabset>
        <tab heading="Details" id="tab1" active="active">
          <app-inspection-details></app-inspection-details>
        </tab>
        <tab heading="Attachments">
          <app-inspection-attachments> </app-inspection-attachments>
        </tab>
        <tab heading="Comments">
          <app-inspection-comments></app-inspection-comments>
        </tab>
        <tab heading="History">
          <app-inspection-history> </app-inspection-history>
        </tab>
      </tabset>
    </div>
  </div>
</ng-template>
