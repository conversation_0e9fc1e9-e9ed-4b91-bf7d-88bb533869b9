import * as Highcharts from 'highcharts';
import { Component } from '@angular/core';
@Component({
  selector: 'app-chart-page',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class CarbonDashboardComponent {
  Highcharts = Highcharts; // Required for the chart
  chartOptions: any;

  ngOnInit() {
    setTimeout(() => {
      this.chartOptions = {
        chart: { type: 'column' },
        title: { text: 'Test Chart' },
        xAxis: { categories: ['A', 'B', 'C'] },
        yAxis: { title: { text: 'Values' } },
        series: [{ name: 'Test', type: 'column', data: [10, 20, 30] }]
      };
      console.log('Chart Options Set:', this.chartOptions);
    }, 100);
  }

}
