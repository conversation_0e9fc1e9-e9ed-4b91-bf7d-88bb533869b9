import { EditCalendarEventComponent } from './edit-calendar-event.component';
import { UntypedFormBuilder, UntypedFormArray, UntypedFormControl } from '@angular/forms';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { ProjectService } from '../../services/profile/project.service';
import { CalendarService } from '../../services/profile/calendar.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import moment from 'moment';
import { of, throwError, Subject } from 'rxjs';

describe('EditCalendarEventComponent', () => {
  let component: EditCalendarEventComponent;
  let fb: UntypedFormBuilder;
  let modalRef: any;
  let modalRef1: any;
  let modalService: any;
  let projectService: any;
  let toastr: any;
  let calendarService: any;
  let deliveryService: any;

  beforeEach(() => {
    fb = new UntypedFormBuilder();
    modalRef = { hide: jest.fn() };
    modalRef1 = { hide: jest.fn() };
    modalService = { show: jest.fn() };
    toastr = { error: jest.fn(), success: jest.fn() };
    projectService = {
      projectParent: new Subject(),
      getTimeZoneList: jest.fn(() => of({ data: [{ id: 1, location: 'UTC' }] })),
      getSingleProject: jest.fn(() => of({})),
      getLocations: jest.fn(() => of({ data: [{ id: 1, locationPath: 'A', gateDetails: [{ id: 1, gateName: 'Gate1' }], EquipmentId: [{ id: 1, equipmentName: 'Equip1' }], TimeZoneId: [{ id: 1, location: 'UTC' }] }] }))
    };
    calendarService = {
      getEventData: jest.fn(() => of({ event: { id: 1, description: 'desc', fromDate: new Date(), toDate: new Date(), endDate: new Date(), startTime: new Date(), endTime: new Date(), isAllDay: false, GateId: '[1]', LocationId: '[1]', EquipmentId: '[1]', isApplicableToDelivery: true, isApplicableToCrane: true, isApplicableToConcrete: true, isApplicableToInspection: true, recurrence: 'Daily', repeatEveryCount: 1, repeatEveryType: 'Day', dateOfMonth: 1, monthlyRepeatType: null, chosenDateOfMonth: true, TimeZone: { id: 1, location: 'UTC' } } })),
      editCalendarEvent: jest.fn(() => of({ message: 'Success' })),
      updateCalendarEvents: jest.fn()
    };
    deliveryService = { loginUser: new Subject() };
    component = new EditCalendarEventComponent(
      fb,
      modalRef,
      modalRef1,
      modalService,
      projectService,
      toastr,
      calendarService,
      deliveryService
    );
    component.data = { id: 1 };
    component.title = 'Edit Event';
    component.ProjectId = 1;
    component.ParentCompanyId = 1;
    component.locationList = [{ id: 1, locationPath: 'A', gateDetails: [{ id: 1, gateName: 'Gate1' }], EquipmentId: [{ id: 1, equipmentName: 'Equip1' }], TimeZoneId: [{ id: 1, location: 'UTC' }] }];
    component.gateList = [{ id: 1, gateName: 'Gate1' }];
    component.equipmentList = [{ id: 1, equipmentName: 'Equip1' }];
    component.timezoneList = [{ id: 1, location: 'UTC' }]; // type: any[] // type: any[]
    component.weekDays = [
      { value: 'Monday', checked: false, isDisabled: false },
      { value: 'Tuesday', checked: false, isDisabled: false },
      { value: 'Wednesday', checked: false, isDisabled: false },
      { value: 'Thursday', checked: false, isDisabled: false },
      { value: 'Friday', checked: false, isDisabled: false },
      { value: 'Saturday', checked: false, isDisabled: false },
      { value: 'Sunday', checked: false, isDisabled: false }
    ];
    component.ediCalendarForm();
    component.authUser = { RoleId: 1 };
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call ngOnInit and getEventDetail', () => {
    const spy = jest.spyOn(component, 'getEventDetail');
    component.ngOnInit();
    expect(spy).toHaveBeenCalled();
  });

  it('should getTimeZoneList success', () => {
    component.getTimeZoneList();
    expect(component.loader).toBe(false);
    expect(component.dropdownSettings).toBeDefined();
  });

  it('should handle getTimeZoneList error 400', () => {
    projectService.getTimeZoneList = jest.fn(() => ({ subscribe: ({ error }) => error({ message: { statusCode: 400, details: [{ message: 'err' }] } }) }));
    const spy = jest.spyOn(component, 'showError');
    component.getTimeZoneList();
    expect(spy).toHaveBeenCalled();
  });

  it('should handle getTimeZoneList error with no message', () => {
    projectService.getTimeZoneList = jest.fn(() => ({ subscribe: ({ error }) => error({}) }));
    component.getTimeZoneList();
    expect(toastr.error).toHaveBeenCalled();
  });

  it('should handle getTimeZoneList error with message', () => {
    projectService.getTimeZoneList = jest.fn(() => ({ subscribe: ({ error }) => error({ message: 'err' }) }));
    component.getTimeZoneList();
    expect(toastr.error).toHaveBeenCalled();
  });

  it('should getLocations', () => {
    component.getLocations();
    expect(component.locationList).toBeDefined();
    expect(component.gateList).toBeDefined();
    expect(component.equipmentList).toBeDefined();
  });

  it('should timeZoneSelected', () => {
    component.timezoneList = [{ id: 1, location: 'UTC' }];
    component.timeZoneSelected(1);
    expect(component.selectedValue).toBeDefined();
  });

  it('should toggleAllDay true', () => {
    const spy = jest.spyOn(component, 'resetTimer');
    component.toggleAllDay(true);
    expect(component.isAllDayChosen).toBe(true);
    expect(spy).toHaveBeenCalled();
  });

  it('should toggleAllDay false', () => {
    const spy = jest.spyOn(component, 'setCurrentTiming');
    component.toggleAllDay(false);
    expect(component.isAllDayChosen).toBe(false);
    expect(spy).toHaveBeenCalled();
  });

  it('should getEventDetail', () => {
    component.getEventDetail();
    expect(component.eventDetail).toBeDefined();
  });

  it('should setTimezone', () => {
    component.eventDetail = { TimeZone: { id: 1, location: 'UTC' } };
    component.editCalendarEvent = fb.group({ TimeZoneId: [''] });
    component.setTimezone();
    expect(component.timezonevalues).toBeDefined();
  });

  it('should setlocation', () => {
    component.eventDetail = { LocationId: 1 };
    component.locationList = [{ id: 1, locationPath: 'A' }];
    component.editCalendarEvent = fb.group({ LocationId: [''] });
    component.setlocation();
    expect(component.getChosenLocation).toBeDefined();
  });

  it('should setEquipment', () => {
    component.eventDetail = { EquipmentId: '[1]' };
    component.equipmentList = [{ id: 1, equipmentName: 'Equip1' }];
    component.editCalendarEvent = fb.group({ EquipmentId: [''] });
    component.setEquipment();
    expect(component.editCalendarEvent.get('EquipmentId').value).toBeDefined();
  });

  it('should setGate', () => {
    component.eventDetail = { GateId: '[1]' };
    component.gateList = [{ id: 1, gateName: 'Gate1' }];
    component.editCalendarEvent = fb.group({ GateId: [''] });
    component.setGate();
    expect(component.editCalendarEvent.get('GateId').value).toBeDefined();
  });

  it('should setLocation', () => {
    component.eventDetail = { LocationId: '[1]' };
    component.locationList = [{ id: 1, locationPath: 'A' }];
    component.editCalendarEvent = fb.group({ LocationId: [''] });
    component.setLocation();
    expect(component.editCalendarEvent.get('LocationId').value).toBeDefined();
  });

  it('should chooseRepeatEveryType for all branches', () => {
    component.editCalendarEvent = fb.group({
      recurrence: [''],
      repeatEveryCount: [2],
      days: new UntypedFormArray([]),
      monthlyRepeatType: [''],
      chosenDateOfMonth: [1],
    });
    component.weekDays = [
      { value: 'Monday', checked: false, isDisabled: false },
      { value: 'Tuesday', checked: false, isDisabled: false }
    ];
    component.chooseRepeatEveryType('Day', {});
    component.chooseRepeatEveryType('Week', { days: ['Monday'] });
    component.chooseRepeatEveryType('Month', {});
    component.chooseRepeatEveryType('Year', {});
    component.chooseRepeatEveryType('Days', {});
    component.chooseRepeatEveryType('Weeks', {});
    component.chooseRepeatEveryType('Months', {});
    component.chooseRepeatEveryType('Years', {});
    component.chooseRepeatEveryType('Day', {});
    expect(component.selectedRecurrence).toBeDefined();
  });

  it('should setFormValue for all branches', () => {
    component.editCalendarEvent = fb.group({
      repeatEveryType: [''],
      repeatEveryCount: [1],
    });
    component.setFormValue('Does Not Repeat');
    component.setFormValue('Daily');
    component.setFormValue('Weekly');
    component.setFormValue('Monthly');
    component.setFormValue('Yearly');
    expect(component.editCalendarEvent.get('repeatEveryType').value).toBeDefined();
  });

  it('should onRecurrenceSelect for all branches', () => {
    component.editCalendarEvent = fb.group({
      repeatEveryCount: [2],
      days: new UntypedFormArray([]),
      recurrence: [''],
      chosenDateOfMonth: [1],
      toDate: [new Date()],
      endDate: [new Date()],
      repeatEveryType: [''],
      dateOfMonth: [''],
      monthlyRepeatType: [''],
      LocationId: [''],
      EquipmentId: [''],
      GateId: [''],
      TimeZoneId: [''],
      isAllDay: [false],
      isApplicableToDelivery: [true],
      isApplicableToCrane: [true],
      isApplicableToConcrete: [true],
      isApplicableToInspection: [true],
      description: [''],
      fromDate: [new Date()],
      startTime: [new Date()],
      endTime: [new Date()],
    });
    component.weekDays = [
      { value: 'Monday', checked: false, isDisabled: false },
      { value: 'Tuesday', checked: false, isDisabled: false }
    ];
    component.onRecurrenceSelect('Daily');
    component.editCalendarEvent.get('repeatEveryCount').setValue(1);
    component.onRecurrenceSelect('Weekly');
    component.onRecurrenceSelect('Monthly');
    component.onRecurrenceSelect('Yearly');
    expect(component.selectedRecurrence).toBeDefined();
  });

  it('should numberOnly', () => {
    expect(component.numberOnly({ which: 48 })).toBe(true);
    expect(component.numberOnly({ which: 65 })).toBe(false);
  });

  it('should close with dirty/touched', () => {
    component.editCalendarEvent.markAsDirty();
    component.editCalendarEvent.markAsTouched();
    const spy = jest.spyOn(component, 'openConfirmationModalPopup');
    const mockTemplateRef = { elementRef: {}, createEmbeddedView: jest.fn() } as any;
    component.close(mockTemplateRef);
    expect(spy).toHaveBeenCalled();
  });

  it('should close without dirty/touched', () => {
    const spy = jest.spyOn(component, 'resetForm');
    const mockTemplateRef = { elementRef: {}, createEmbeddedView: jest.fn() } as any;
    component.close(mockTemplateRef);
    expect(spy).toHaveBeenCalled();
  });

  it('should openConfirmationModalPopup', () => {
    component.openConfirmationModalPopup({});
    expect(modalService.show).toHaveBeenCalled();
  });

  it('should resetForm action no', () => {
    (component as any).modalRef1 = modalRef1;
    component.resetForm('no');
    expect(modalRef1.hide).toHaveBeenCalled();
  });

  it('should resetForm action yes', () => {
    (component as any).modalRef1 = modalRef1;
    component.resetForm('yes');
    expect(modalRef1.hide).toHaveBeenCalled();
    expect(modalRef.hide).toHaveBeenCalled();
  });

  it('should sortWeekDays', () => {
    const sorted = component.sortWeekDays(['Monday', 'Sunday']);
    expect(sorted[0]).toBe('Sunday');
  });

  it('should changeMonthlyRecurrence', () => {
    component.editCalendarEvent = fb.group({
      chosenDateOfMonth: [1],
      fromDate: [new Date()],
      dateOfMonth: [''],
      monthlyRepeatType: ['']
    });
    component.changeMonthlyRecurrence();
    component.editCalendarEvent.get('chosenDateOfMonth').setValue(2);
    component.changeMonthlyRecurrence();
    component.editCalendarEvent.get('chosenDateOfMonth').setValue(3);
    component.changeMonthlyRecurrence();
    expect(component.editCalendarEvent.get('dateOfMonth')).toBeDefined();
  });

  it('should showMonthlyRecurrence', () => {
    component.editCalendarEvent = fb.group({ fromDate: [new Date()] });
    component.showMonthlyRecurrence();
    expect(component.monthlyDate).toBeDefined();
  });

  it('should submitPayload', () => {
    component.editCalendarEvent = fb.group({
      description: ['desc'],
      fromDate: [new Date()],
      toDate: [new Date()],
      startTime: [new Date()],
      endTime: [new Date()],
      TimeZoneId: [[{ id: 1, location: 'UTC' }]],
      isAllDay: [false],
      GateId: [[{ id: 1 }]],
      LocationId: [[{ id: 1 }]],
      EquipmentId: [[{ id: 1 }]],
      isApplicableToDelivery: [true],
      isApplicableToCrane: [true],
      isApplicableToConcrete: [true],
      isApplicableToInspection: [true],
      recurrence: ['Monthly'],
      days: [['Monday']],
      repeatEveryType: ['Month'],
      repeatEveryCount: [1],
      chosenDateOfMonth: [1],
      dateOfMonth: [1],
      monthlyRepeatType: [''],
      endDate: [new Date()]
    });
    component.timezonevalues = [{ id: 1, location: 'UTC' }];
    component.monthlyDate = '1';
    const payload = component.submitPayload();
    expect(payload).toBeDefined();
  });

  it('should onSubmit valid', () => {
    component.editCalendarEvent = fb.group({
      description: ['desc'],
      fromDate: [moment().add(2, 'days').toDate()],
      toDate: [moment().add(3, 'days').toDate()],
      endDate: [moment().add(4, 'days').toDate()],
      startTime: [new Date()],
      endTime: [new Date(Date.now() + 3600000)],
      TimeZoneId: [[{ id: 1, location: 'UTC' }]],
      isAllDay: [false],
      GateId: [[{ id: 1 }]],
      LocationId: [[{ id: 1 }]],
      EquipmentId: [[{ id: 1 }]],
      isApplicableToDelivery: [true],
      isApplicableToCrane: [true],
      isApplicableToConcrete: [true],
      isApplicableToInspection: [true],
      recurrence: ['Monthly'],
      days: [['Monday']],
      repeatEveryType: ['Month'],
      repeatEveryCount: [1],
      chosenDateOfMonth: [1],
      dateOfMonth: [1],
      monthlyRepeatType: [''],
    });
    component.timezonevalues = [{ id: 1, location: 'UTC' }];
    component.editEventId = 1;
    component.ProjectId = 1;
    component.ParentCompanyId = 1;
    component.authUser = { RoleId: 1 };
    component.validateForm = jest.fn(() => true);
    component.submitPayload = jest.fn(() => ({
  description: '', fromDate: '', toDate: '', startTime: '', endTime: '',
  TimeZoneId: '', isAllDay: '', GateId: '', LocationId: '', EquipmentId: '',
  isApplicableToDelivery: '', isApplicableToCrane: '', isApplicableToConcrete: '',
  isApplicableToInspection: '', recurrence: '', days: [], repeatEveryType: '',
  repeatEveryCount: '', chosenDateOfMonth: false, dateOfMonth: '', monthlyRepeatType: '',
  createdBy: '', endDate: ''
}));
    calendarService.editCalendarEvent = jest.fn(() => of({ message: 'Success' }));
    component.onSubmit();
    expect(modalRef.hide).toHaveBeenCalled();
    expect(toastr.success).toHaveBeenCalled();
  });

  it('should onSubmit invalid', () => {
    component.validateForm = jest.fn(() => false);
    component.onSubmit();
    expect(component.formSubmitted).toBe(false);
  });

  it('should onSubmit error 400', () => {
    component.editCalendarEvent = fb.group({
      description: ['desc'],
      fromDate: [moment().add(2, 'days').toDate()],
      toDate: [moment().add(3, 'days').toDate()],
      endDate: [moment().add(4, 'days').toDate()],
      startTime: [new Date()],
      endTime: [new Date(Date.now() + 3600000)],
      TimeZoneId: [[{ id: 1, location: 'UTC' }]],
      isAllDay: [false],
      GateId: [[{ id: 1 }]],
      LocationId: [[{ id: 1 }]],
      EquipmentId: [[{ id: 1 }]],
      isApplicableToDelivery: [true],
      isApplicableToCrane: [true],
      isApplicableToConcrete: [true],
      isApplicableToInspection: [true],
      recurrence: ['Monthly'],
      days: [['Monday']],
      repeatEveryType: ['Month'],
      repeatEveryCount: [1],
      chosenDateOfMonth: [1],
      dateOfMonth: [1],
      monthlyRepeatType: [''],
    });
    component.timezonevalues = [{ id: 1, location: 'UTC' }];
    component.editEventId = 1;
    component.ProjectId = 1;
    component.ParentCompanyId = 1;
    component.authUser = { RoleId: 1 };
    component.validateForm = jest.fn(() => true);
    component.submitPayload = jest.fn(() => ({
  description: '', fromDate: '', toDate: '', startTime: '', endTime: '',
  TimeZoneId: '', isAllDay: '', GateId: '', LocationId: '', EquipmentId: '',
  isApplicableToDelivery: '', isApplicableToCrane: '', isApplicableToConcrete: '',
  isApplicableToInspection: '', recurrence: '', days: [], repeatEveryType: '',
  repeatEveryCount: '', chosenDateOfMonth: false, dateOfMonth: '', monthlyRepeatType: '',
  createdBy: '', endDate: ''
}));
    calendarService.editCalendarEvent = jest.fn(() => ({ subscribe: ({ error }) => error({ message: { statusCode: 400, details: [{ message: 'err' }] } }) }));
    const spy = jest.spyOn(component, 'showError');
    component.onSubmit();
    expect(spy).toHaveBeenCalled();
  });

  it('should onSubmit error with no message', () => {
    component.editCalendarEvent = fb.group({
      description: ['desc'],
      fromDate: [moment().add(2, 'days').toDate()],
      toDate: [moment().add(3, 'days').toDate()],
      endDate: [moment().add(4, 'days').toDate()],
      startTime: [new Date()],
      endTime: [new Date(Date.now() + 3600000)],
      TimeZoneId: [[{ id: 1, location: 'UTC' }]],
      isAllDay: [false],
      GateId: [[{ id: 1 }]],
      LocationId: [[{ id: 1 }]],
      EquipmentId: [[{ id: 1 }]],
      isApplicableToDelivery: [true],
      isApplicableToCrane: [true],
      isApplicableToConcrete: [true],
      isApplicableToInspection: [true],
      recurrence: ['Monthly'],
      days: [['Monday']],
      repeatEveryType: ['Month'],
      repeatEveryCount: [1],
      chosenDateOfMonth: [1],
      dateOfMonth: [1],
      monthlyRepeatType: [''],
    });
    component.timezonevalues = [{ id: 1, location: 'UTC' }];
    component.editEventId = 1;
    component.ProjectId = 1;
    component.ParentCompanyId = 1;
    component.authUser = { RoleId: 1 };
    component.validateForm = jest.fn(() => true);
    component.submitPayload = jest.fn(() => ({
  description: '', fromDate: '', toDate: '', startTime: '', endTime: '',
  TimeZoneId: '', isAllDay: '', GateId: '', LocationId: '', EquipmentId: '',
  isApplicableToDelivery: '', isApplicableToCrane: '', isApplicableToConcrete: '',
  isApplicableToInspection: '', recurrence: '', days: [], repeatEveryType: '',
  repeatEveryCount: '', chosenDateOfMonth: false, dateOfMonth: '', monthlyRepeatType: '',
  createdBy: '', endDate: ''
}));
    calendarService.editCalendarEvent = jest.fn(() => ({ subscribe: ({ error }) => error({}) }));
    component.onSubmit();
    expect(toastr.error).toHaveBeenCalled();
  });

  it('should onSubmit error with message', () => {
    component.editCalendarEvent = fb.group({
      description: ['desc'],
      fromDate: [moment().add(2, 'days').toDate()],
      toDate: [moment().add(3, 'days').toDate()],
      endDate: [moment().add(4, 'days').toDate()],
      startTime: [new Date()],
      endTime: [new Date(Date.now() + 3600000)],
      TimeZoneId: [[{ id: 1, location: 'UTC' }]],
      isAllDay: [false],
      GateId: [[{ id: 1 }]],
      LocationId: [[{ id: 1 }]],
      EquipmentId: [[{ id: 1 }]],
      isApplicableToDelivery: [true],
      isApplicableToCrane: [true],
      isApplicableToConcrete: [true],
      isApplicableToInspection: [true],
      recurrence: ['Monthly'],
      days: [['Monday']],
      repeatEveryType: ['Month'],
      repeatEveryCount: [1],
      chosenDateOfMonth: [1],
      dateOfMonth: [1],
      monthlyRepeatType: [''],
    });
    component.timezonevalues = [{ id: 1, location: 'UTC' }];
    component.editEventId = 1;
    component.ProjectId = 1;
    component.ParentCompanyId = 1;
    component.authUser = { RoleId: 1 };
    component.validateForm = jest.fn(() => true);
    component.submitPayload = jest.fn(() => ({
  description: '', fromDate: '', toDate: '', startTime: '', endTime: '',
  TimeZoneId: '', isAllDay: '', GateId: '', LocationId: '', EquipmentId: '',
  isApplicableToDelivery: '', isApplicableToCrane: '', isApplicableToConcrete: '',
  isApplicableToInspection: '', recurrence: '', days: [], repeatEveryType: '',
  repeatEveryCount: '', chosenDateOfMonth: false, dateOfMonth: '', monthlyRepeatType: '',
  createdBy: '', endDate: ''
}));
    calendarService.editCalendarEvent = jest.fn(() => ({ subscribe: ({ error }) => error({ message: 'err' }) }));
    component.onSubmit();
    expect(toastr.error).toHaveBeenCalled();
  });

  it('should validateForm', () => {
    component.editCalendarEvent = fb.group({});
    component.validateBasicFields = jest.fn(() => true);
    component.validateDateFields = jest.fn(() => true);
    expect(component.validateForm()).toBe(true);
    component.validateBasicFields = jest.fn(() => false);
    expect(component.validateForm()).toBe(false);
    component.validateBasicFields = jest.fn(() => true);
    component.validateDateFields = jest.fn(() => false);
    expect(component.validateForm()).toBe(false);
  });

  it('should validateBasicFields', () => {
    component.editCalendarEvent = fb.group({
      isApplicableToDelivery: [false],
      isApplicableToCrane: [false],
      isApplicableToConcrete: [false],
      isApplicableToInspection: [false],
      isAllDay: [false],
      startTime: [new Date()],
      endTime: [new Date()],
    });
    expect(component.validateBasicFields()).toBe(false);
    component.editCalendarEvent = fb.group({
      isApplicableToDelivery: [true],
      isApplicableToCrane: [false],
      isApplicableToConcrete: [false],
      isApplicableToInspection: [false],
      isAllDay: [false],
      startTime: [new Date()],
      endTime: [new Date(Date.now() + 3600000)],
    });
    expect(component.validateBasicFields()).toBe(true);
    component.editCalendarEvent = fb.group({
      isApplicableToDelivery: [true],
      isApplicableToCrane: [false],
      isApplicableToConcrete: [false],
      isApplicableToInspection: [false],
      isAllDay: [false],
      startTime: [new Date()],
      endTime: [new Date()],
    });
    expect(component.validateBasicFields()).toBe(false);
    component.editCalendarEvent = fb.group({
      isApplicableToDelivery: [true],
      isApplicableToCrane: [false],
      isApplicableToConcrete: [false],
      isApplicableToInspection: [false],
      isAllDay: [false],
      startTime: [new Date(Date.now() + 3600000)],
      endTime: [new Date()],
    });
    expect(component.validateBasicFields()).toBe(false);
    component.editCalendarEvent = fb.group({
      isApplicableToDelivery: [true],
      isApplicableToCrane: [false],
      isApplicableToConcrete: [false],
      isApplicableToInspection: [false],
      isAllDay: [true],
      startTime: [new Date()],
      endTime: [new Date()],
    });
    expect(component.validateBasicFields()).toBe(true);
  });

  it('should validateDateFields', () => {
    component.editCalendarEvent = fb.group({
      recurrence: ['Weekly'],
      fromDate: [moment().add(2, 'days').toDate()],
      toDate: [moment().add(3, 'days').toDate()],
      endDate: [moment().add(4, 'days').toDate()],
    });
    expect(component.validateDateFields()).toBe(true);
    component.editCalendarEvent = fb.group({
      recurrence: ['Weekly'],
      fromDate: [moment().subtract(2, 'days').toDate()],
      toDate: [moment().add(3, 'days').toDate()],
      endDate: [moment().add(4, 'days').toDate()],
    });
    expect(component.validateDateFields()).toBe(false);
    component.editCalendarEvent = fb.group({
      recurrence: ['Weekly'],
      fromDate: [moment().add(2, 'days').toDate()],
      toDate: [moment().subtract(3, 'days').toDate()],
      endDate: [moment().add(4, 'days').toDate()],
    });
    expect(component.validateDateFields()).toBe(false);
    component.editCalendarEvent = fb.group({
      recurrence: ['Weekly'],
      fromDate: [moment().add(2, 'days').toDate()],
      toDate: [moment().add(3, 'days').toDate()],
      endDate: [moment().subtract(4, 'days').toDate()],
    });
    expect(component.validateDateFields()).toBe(false);
    component.editCalendarEvent = fb.group({
      recurrence: ['Daily'],
      fromDate: [moment().add(2, 'days').toDate()],
      toDate: [moment().add(3, 'days').toDate()],
      endDate: [moment().add(4, 'days').toDate()],
    });
    expect(component.validateDateFields()).toBe(true);
  });

  it('should showError', () => {
    component.editCalendarEvent = fb.group({});
    component.showError({ message: { details: [{ message: 'err' }] } });
    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
    expect(toastr.error).toHaveBeenCalled();
  });

  it('should resetTimer', () => {
    component.editCalendarEvent = fb.group({ startTime: [''], endTime: [''] });
    component.resetTimer();
    expect(component.editCalendarEvent.get('startTime').value).toBeDefined();
    expect(component.editCalendarEvent.get('endTime').value).toBeDefined();
  });

  it('should ediCalendarForm', () => {
    component.ediCalendarForm();
    expect(component.editCalendarEvent).toBeDefined();
  });

  it('should setCurrentTiming', () => {
    component.editCalendarEvent = fb.group({ startTime: [''], endTime: [''], fromDate: [''], toDate: [''], endDate: [''] });
    component.setCurrentTiming();
    expect(component.editCalendarEvent.get('startTime').value).toBeDefined();
    expect(component.editCalendarEvent.get('endTime').value).toBeDefined();
  });

  it('should updateFormValidation', () => {
    component.editCalendarEvent = fb.group({ chosenDateOfMonth: [1], dateOfMonth: [''], monthlyRepeatType: [''] });
    component.updateFormValidation();
    component.editCalendarEvent.get('chosenDateOfMonth').setValue(2);
    component.updateFormValidation();
    expect(component.editCalendarEvent.get('chosenDateOfMonth')).toBeDefined();
  });

  it('should formControlValueChanged', () => {
    component.editCalendarEvent = fb.group({
      startTime: [''],
      endTime: [''],
      isAllDay: [false],
      repeatEveryType: [''],
      days: new UntypedFormArray([]),
      chosenDateOfMonth: [1],
      dateOfMonth: [''],
      monthlyRepeatType: ['']
    });
    component.formControlValueChanged();
    expect(component.editCalendarEvent.get('startTime')).toBeDefined();
  });

  it('should locationSelected', () => {
    component.locationList = [{ id: 1, gateDetails: [{ id: 1 }], EquipmentId: [{ id: 1 }], TimeZoneId: [{ id: 1, location: 'UTC' }] }];
    component.editCalendarEvent = fb.group({ GateId: [''], EquipmentId: [''] });
    component.locationSelected({ id: 1 });
    expect(component.selectedLocationId).toBe(1);
  });

  it('should onChange add and remove', () => {
    component.editCalendarEvent = fb.group({ days: new UntypedFormArray([]), recurrence: ['Weekly'], repeatEveryCount: [1], repeatEveryType: [''], toDate: [new Date()], endDate: [new Date()] });
    component.selectedRecurrence = 'Weekly';
    component.weekDays = [
      { value: 'Monday', checked: false, isDisabled: false },
      { value: 'Tuesday', checked: false, isDisabled: false }
    ];
    component.onChange({ target: { value: 'Monday', checked: true } });
    component.onChange({ target: { value: 'Monday', checked: false } });
    component.selectedRecurrence = 'Daily';
    component.onChange({ target: { value: 'Tuesday', checked: true } });
    component.onChange({ target: { value: 'Tuesday', checked: false } });
    expect(component.editCalendarEvent.get('recurrence').value).toBeDefined();
  });

  it('should changeRecurrenceCount', () => {
    component.editCalendarEvent = fb.group({ recurrence: ['Daily'], repeatEveryType: [''], repeatEveryCount: [2] });
    component.changeRecurrenceCount(2);
    component.editCalendarEvent.get('recurrence').setValue('Weekly');
    component.changeRecurrenceCount(1);
    component.editCalendarEvent.get('recurrence').setValue('Monthly');
    component.changeRecurrenceCount(1);
    component.editCalendarEvent.get('recurrence').setValue('Yearly');
    component.changeRecurrenceCount(1);
    expect(component.editCalendarEvent.get('repeatEveryType').value).toBeDefined();
  });

  it('should occurMessage', () => {
    component.editCalendarEvent = fb.group({
      repeatEveryType: ['Day'],
      repeatEveryCount: [2],
      recurrence: ['Daily'],
      toDate: [new Date()],
      endDate: [new Date()]
    });
    component.weekDays = [
      { value: 'Monday', checked: true, isDisabled: false },
      { value: 'Tuesday', checked: false, isDisabled: false }
    ];
    component.occurMessage();
    component.editCalendarEvent.get('repeatEveryType').setValue('Days');
    component.occurMessage();
    component.editCalendarEvent.get('repeatEveryType').setValue('Week');
    component.occurMessage();
    component.editCalendarEvent.get('repeatEveryType').setValue('Weeks');
    component.occurMessage();
    component.editCalendarEvent.get('recurrence').setValue('Monthly');
    component.occurMessage();
    expect(component.message).toBeDefined();
  });

  it('should repeatTypeMessage', () => {
    component.editCalendarEvent = fb.group({ repeatEveryType: ['Month'], chosenDateOfMonth: [1] });
    component.monthlyDate = '1';
    component.repeatTypeMessage();
    component.editCalendarEvent.get('repeatEveryType').setValue('Months');
    component.repeatTypeMessage();
    component.editCalendarEvent.get('repeatEveryType').setValue('Year');
    component.repeatTypeMessage();
    component.editCalendarEvent.get('repeatEveryType').setValue('Years');
    component.editCalendarEvent.get('chosenDateOfMonth').setValue(2);
    component.monthlyDayOfWeek = 'First Monday';
    component.repeatTypeMessage();
    component.editCalendarEvent.get('chosenDateOfMonth').setValue(3);
    component.monthlyLastDayOfWeek = 'Last Monday';
    component.repeatTypeMessage();
    expect(component.message).toBeDefined();
  });
});
