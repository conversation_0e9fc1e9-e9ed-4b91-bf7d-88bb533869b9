import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AppComponent } from './app.component';
import { BnNgIdleService } from 'bn-ng-idle';
import { AuthService } from './services';
import { NgZone } from '@angular/core';
import { of, Subject } from 'rxjs';

// Mock BroadcastChannel
class MockBroadcastChannel {
  public postMessage = jest.fn();
  public onmessage: ((event: { data: any }) => void) | null = null;
  public close = jest.fn();
  public onmessageerror: ((event: MessageEvent) => void) | null = null;
  public addEventListener = jest.fn();
  public removeEventListener = jest.fn();
  public dispatchEvent = jest.fn();

  constructor(public name: string) {}
}

declare global {
  interface Window {
    BroadcastChannel: {
      new (name: string): MockBroadcastChannel;
      prototype: MockBroadcastChannel;
    };
  }
}

describe('AppComponent', () => {
  let component: AppComponent;
  let fixture: ComponentFixture<AppComponent>;
  let bnIdleServiceMock: Partial<BnNgIdleService>;
  let authServiceMock: Partial<AuthService>;
  let ngZone: NgZone;
  let mockChannel: MockBroadcastChannel;

  beforeEach(async () => {
    // Setup BroadcastChannel mock
    mockChannel = new MockBroadcastChannel('app-activity');
    window.BroadcastChannel = jest.fn().mockImplementation(() => mockChannel);

    bnIdleServiceMock = {
      startWatching: jest.fn().mockReturnValue(of(false)),
      stopTimer: jest.fn(),
      resetTimer: jest.fn(),
      expired$: new Subject<boolean>()
    };

    authServiceMock = {
      logout: jest.fn()
    };

    ngZone = new NgZone({ enableLongStackTrace: false });

    await TestBed.configureTestingModule({
      declarations: [AppComponent],
      providers: [
        { provide: BnNgIdleService, useValue: bnIdleServiceMock },
        { provide: AuthService, useValue: authServiceMock },
        { provide: NgZone, useValue: ngZone },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AppComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct title', () => {
    expect(component.title).toBe('Admin Dashboard');
  });

  it('should start watching for idle timeout on init', () => {
    component.ngOnInit();
    expect(bnIdleServiceMock.startWatching).toHaveBeenCalledWith(14400);
  });

  it('should handle idle timeout', () => {
    component.ngOnInit();
    (bnIdleServiceMock.startWatching as jest.Mock).mockReturnValue(of(true));

    // Wait for the next tick to allow the subscription to process
    setTimeout(() => {
      const call = mockChannel.postMessage.mock.calls[0][0];
      expect(call.type).toBe('idle');
      expect(typeof call.tabId).toBe('string');
    }, 0);
  });

  it('should handle user activity', () => {
    component.ngOnInit();

    // Simulate user activity
    const activityEvent = new MouseEvent('mousemove');
    window.dispatchEvent(activityEvent);

    // Wait for the next tick to allow the event to process
    setTimeout(() => {
      const call = mockChannel.postMessage.mock.calls[0][0];
      expect(call.type).toBe('activity');
      expect(typeof call.tabId).toBe('string');
    }, 0);
  });

  it('should handle logout message from broadcast channel', () => {
    component.ngOnInit();

    // Simulate logout message
    mockChannel.onmessage?.({ data: { type: 'logout' } });

    expect(authServiceMock.logout).toHaveBeenCalled();
    expect(bnIdleServiceMock.stopTimer).toHaveBeenCalled();
  });

  it('should cleanup resources on destroy', () => {
    component.ngOnInit();
    component.ngOnDestroy();

    expect(mockChannel.close).toHaveBeenCalled();
  });

  it('should handle all tabs being idle', () => {
    component.ngOnInit();

    // Simulate all tabs being idle
    mockChannel.onmessage?.({ data: { type: 'idle', tabId: 'tab1' } });
    mockChannel.onmessage?.({ data: { type: 'idle', tabId: 'tab2' } });

    // Wait for the next tick to allow the messages to process
    setTimeout(() => {
      const call = mockChannel.postMessage.mock.calls[0][0];
      expect(call.type).toBe('logout');
      expect(authServiceMock.logout).toHaveBeenCalled();
      expect(bnIdleServiceMock.stopTimer).toHaveBeenCalled();
    }, 0);
  });
});
