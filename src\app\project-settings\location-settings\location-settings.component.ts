import { Component, TemplateRef, OnInit } from '@angular/core';
import {
  UntypedFormBuilder, UntypedFormGroup, Validators, UntypedFormArray,
} from '@angular/forms';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { Title } from '@angular/platform-browser';
import { LocationSettingsService } from '../../services/location-settings/location-settings.service';
import { ProjectService } from '../../services/profile/project.service';
import { MixpanelService } from '../../services/mixpanel.service';

@Component({
  selector: 'app-location-settings',
  templateUrl: './location-settings.component.html',
  })
export class LocationSettingsComponent implements OnInit {
  public currentPageNo = 1;

  public pageSize = 5;

  public sort = 'DESC';

  public sortColumn = 'id';

  public modalRef: BsModalRef;

  public items = ['<PERSON>', '<PERSON> joe'];

  public isOpened = true;

  public ProjectId;

  public ParentCompanyId;

  public loader = false;

  public addLoader = false;

  public locations = [];

  public locationForm: UntypedFormGroup;

  public deleteSubmitted = false;

  public currentItem;

  public actionType = 'Add';

  public showSearchbar = false;

  public search = '';

  public totalCount = 0;

  public locationFiles: NgxFileDropEntry[] = [];

  public importSubmitted = false;

  public formData: any = [];

  public chosenLocationId: number;

  public todayDate = new Date();

  public defaultLocationPath: any = {};

  public lastRemovedSubCategoryIndex: number = -1;

  public subCatCount: number = 1;

  public tierCount: number = 1;

  public showAddTierButton: boolean[] = [];

  public gateList: any = [];

  public equipmentList: any;

  public equipmentDropdownSettings: { singleSelection: boolean; idField: string; textField: string; selectAllText: string; unSelectAllText: string; itemsShowLimit: number; allowSearchFilter: boolean; };

  public gateDropdownSettings: { singleSelection: boolean; idField: string; textField: string; selectAllText: string; unSelectAllText: string; itemsShowLimit: number; allowSearchFilter: boolean; };
  public timezoneList: any;
  public dropdownSettings: { singleSelection: boolean; idField: string; textField: string; allowSearchFilter: boolean; closeDropDownOnSelection: boolean; };
  public getSelectedTimeZone: any;
  public defaultTimeZoneId: any;

  public constructor(
    private readonly modalService: BsModalService,
    private readonly locationService: LocationSettingsService,
    private readonly projectService: ProjectService,
    private readonly fb: UntypedFormBuilder,
    private readonly toastr: ToastrService,
    private readonly titleService: Title,
    private modalRef1: BsModalRef,
    private readonly mixpanelService: MixpanelService,
  ) {
    this.titleService.setTitle('Follo - Project Settings');
    this.ProjectId = localStorage.getItem('ProjectId');
    this.ParentCompanyId = localStorage.getItem('currentCompanyId');
    if (this.ProjectId && this.ParentCompanyId) {
      this.getLocations();
      this.getOverAllGate();
      this.getOverAllEquipment();
      this.getTimeZoneList();
    }
    this.showAddTierButton = [];
  }

  public ngOnInit(): void { /* */ }

  public changeSettingsCollapse(): void {
    this.isOpened = !this.isOpened;
  }

  public changePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.getLocations();
  }

  public changePageNo(pageNo: number): void {
    this.currentPageNo = pageNo;
    this.getLocations();
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.currentPageNo = 1;
    this.getLocations();
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, value: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'open':
          this.openModal(data, item, value);
          break;
        case 'open1':
          this.openModal(data, item, value);
          break;
        case 'delete':
          this.openDeleteModal(data, item);
          break;
        case 'clear':
          this.clear();
          break;
        case 'remove':
          this.removeFile(data);
          break;
        case 'download':
          this.download();
          break;
        case 'addSub':
          this.addSubCategory();
          break;
        case 'removeSub':
          this.removeSubCategory(data);
          break;
        case 'addTir':
          this.addTier(data);
          break;
        case 'removeTi':
          this.removeTier(data, item);
          break;
        default:
          break;
      }
    }
  }

  public getOverAllGate(): void {
    const params = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .gateList(params, { isFilter: true, showActivatedAlone: true })
      .subscribe((res): void => {
        this.gateList = res.data;
        this.gateDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'gateName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
      });
  }

  public getOverAllEquipment(): void {
    const newNdrGetEquipmentsParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listEquipment(newNdrGetEquipmentsParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((equipmentListResponseForNewNdr): void => {
        this.equipmentList = equipmentListResponseForNewNdr.data;
        this.equipmentDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'equipmentName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
      });
  }

  public getTimeZoneList(): void {
    this.projectService.getTimeZoneList().subscribe({
      next: (response: any): void => {
        this.loader = true;
        if (response) {
          const params = {
            ProjectId: this.ProjectId,
          };
          if (params.ProjectId) {
            this.projectService.getSingleProject(params).subscribe((projectList: any): void => {
              if (projectList) {
                this.timezoneList = response.data;
                this.dropdownSettings = {
                  singleSelection: true,
                  idField: 'id',
                  textField: 'location',
                  allowSearchFilter: true,
                  closeDropDownOnSelection: true,
                };
                this.defaultTimeZoneId = projectList.data.TimeZoneId;
                this.getSelectedTimeZone = this.timezoneList.filter(
                  (obj: any): any => +obj.id === +projectList.data.TimeZoneId,
                );
                this.loader = false;
              }
            });
          }
        }
      },
      error: (getTimeZoneListErr): void => {
        if (getTimeZoneListErr.message?.statusCode === 400) {
          this.showError(getTimeZoneListErr);
        } else if (!getTimeZoneListErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(getTimeZoneListErr.message, 'OOPS!');
        }
      },
    });
  }

  public searchLocation(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.search = data;
    this.currentPageNo = 1;
    this.getLocations();
  }

  public sortByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getLocations();
  }

  public openModal(item, template: TemplateRef<any>, type): void {
    this.createLocationForm();
    this.getSelectedTimeZone = this.timezoneList.filter(
      (obj: any): any => +obj.id === this.defaultTimeZoneId
    );
    this.locationForm.get('TimeZoneId').patchValue(this.getSelectedTimeZone);
    this.modalRef = this.modalService.show(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-md add-location-modal custom-modal',
    });
    if (type === 'add') {
      this.actionType = 'Add';
    } else if (type === 'edit') {
      this.actionType = 'Edit';
      if (this.search) {
        this.chosenLocationId = item.id;
        this.getLocation();
      } else {
        this.bindFormArrayData(item);
      }
    }
  }

  public openImportModal(template: TemplateRef<any>): void {
    this.modalRef = this.modalService.show(template, {
      backdrop: 'static',
      class: 'modal-lg custom-modal',
    });
  }

  public getLocations(): void {
    this.loader = true;
    const queryParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      pageSize: this.pageSize,
      pageNo: this.currentPageNo,
      search: this.search,
      sort: this.sort,
      sortByField: this.sortColumn,
    };
    this.locationService.getLocations(queryParams).subscribe((response): void => {
      if (response) {
        this.locations = response.data ? response.data.rows : [];
        this.defaultLocationPath = response.data ? response.data.defaultLocation : {};
        this.totalCount = response.data.count;
        this.loader = false;
      }
    });
  }

  public getLocation(): void {
    this.addLoader = true;
    const queryParams = {
      id: this.chosenLocationId,
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.locationService.getLocation(queryParams).subscribe((response): void => {
      if (response) {
        const location = response.data ? response.data : {};
        this.bindFormArrayData(location);
        this.addLoader = false;
      }
    });
  }

  public createLocationForm(): void {
    this.locationForm = this.fb.group({
      id: null,
      mainCategory: [null, Validators.required],
      paths: new UntypedFormArray([this.initSubCategory()]),
      notes: [null],
      GateId: [null,Validators.required],
      EquipmentId: [null,Validators.required],
      TimeZoneId: [null,Validators.required],
    });
  }

  public submitLocation(actionType): void {
    this.addLoader = true;
    const queryParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      platform: 'web',
    };

    this.locationForm.value.EquipmentId.forEach((data : any) => {
      const equipment = this.equipmentList.find((equipment : any) => equipment.id === data.id); // Corrected
      if (equipment) {
        data.PresetEquipmentType = equipment.PresetEquipmentType;
      }
    });


    const payload = {
      id: this.locationForm.value.id,
      mainCategory: this.locationForm.value.mainCategory,
      paths: this.locationForm.value.paths,
      notes: this.locationForm.value.notes,
      GateId: this.locationForm.value.GateId,
      EquipmentId: this.locationForm.value.EquipmentId,
      TimeZoneId: this.locationForm.value.TimeZoneId,
    };
    if (actionType === 'Add') {
      this.locationService.addLocation(payload, queryParams).subscribe((response): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.mixpanelService.addMixpanelEvents('Added Location');
          this.getLocations();
          this.resetAddLocation();
        }
      });
    } else if (actionType === 'Edit') {
      if(!payload.EquipmentId || !payload.GateId || !payload.TimeZoneId){
        this.toastr.error('Please Fill All The Required Fields');
      }else{
        this.locationService.editLocation(payload, queryParams).subscribe((response): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Edited Location');
            this.getLocations();
            this.resetAddLocation();
          }
        });
      }
    }
  }

  public getEquipmentNames(item: any): string {
    if (item.EquipmentId && Array.isArray(item.EquipmentId)) {
      return item.EquipmentId.map((equipment: any) => equipment.equipmentName).join(', ');
    }
    return '';
  }

  public getGateNames(item: any): string {
    if (item.gateDetails && Array.isArray(item.gateDetails)) {
      return item.gateDetails.map((gate: any) => gate.gateName).join(', ');
    }
    return '';
  }

  public dfowImportFiledropped(files: NgxFileDropEntry[]): void {
    if (files.length === 1) {
      this.locationFiles = files;
      this.locationFiles.forEach((dfowElement, i): void => {
        const relativePath = dfowElement.relativePath.split('.');
        const extension = relativePath[relativePath.length - 1];
        if (extension === 'xlsx') {
          if (dfowElement.fileEntry.isFile) {
            const fileEntry = dfowElement.fileEntry as FileSystemFileEntry;
            fileEntry.file((_file: File): void => {
              this.formData = new FormData();
              this.formData.append('location', _file, dfowElement.relativePath);
            });
          }
        } else {
          this.locationFiles.splice(i);
          this.toastr.error('Please select a valid file. Supported file format (.xlsx)', 'OOPS!');
        }
      });
    } else {
      this.toastr.error('Please import single file', 'OOPS!');
    }
  }

  public importData(): void {
    this.importSubmitted = true;
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      platform: 'web',
    };
    this.locationService.importLocationFile(params, this.formData).subscribe({
      next: (res): void => {
        this.toastr.success(res.message, 'SUCCESS!');
        this.mixpanelService.addMixpanelEvents('Added Location');
        this.locationFiles = [];
        this.getLocations();
        this.importSubmitted = false;
        this.modalRef.hide();
      },
      error: (importDataError): void => {
        this.importSubmitted = false;
        if (importDataError.message?.statusCode === 400) {
          this.showError(importDataError);
        } else if (!importDataError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(importDataError.message, 'OOPS!');
        }
      },
    });
  }

  public download(): any {
    const payload = {
      ProjectId: +this.ProjectId,
      ParentCompanyId: +this.ParentCompanyId,
    };
    this.locationService.downloadSampleExcelFile(payload).subscribe((res: any): void => {
      this.projectService.getProject().subscribe({
        next: (response): any => {
          const project = response.data.filter((data): {} => +data.id === +this.ProjectId);
          const fileName = `${project[0].projectName}_${project[0].id}_${new Date().getTime()}`;
          const downloadURL = window.URL.createObjectURL(res);
          const link = document.createElement('a');
          link.href = downloadURL;
          link.download = `${fileName}.xlsx`;
          link.click();
        },
        error: (downloadLocationError): void => {
          if (downloadLocationError.message?.statusCode === 400) {
            this.showError(downloadLocationError);
          } else if (!downloadLocationError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(downloadLocationError.message, 'OOPS!');
          }
        },
      });
    });
  }

  public resetAddLocation(): void {
    this.modalRef.hide();
    this.locationForm.reset();
    this.addLoader = false;
  }

  public removeFile(i: number): void {
    this.locationFiles.splice(i);
  }

  // Binding Form array data
  public bindFormArrayData(data): void {
    this.locationForm.get('id').setValue(data.id);
    this.locationForm.get('mainCategory').setValue(data.locationName);
    this.locationForm.get('notes').setValue(data.notes);
    this.locationForm.get('GateId').setValue(data.gateDetails);
    this.locationForm.get('EquipmentId').setValue(data.EquipmentId);
    this.locationForm.get('TimeZoneId').setValue(data.TimeZoneId);

    let control: any = [];
    control = this.locationForm.get('paths') as UntypedFormArray;
    data.paths.forEach((element, subCategoryIndex): void => {
      if (control.controls[subCategoryIndex]) {
        control.controls[subCategoryIndex].get('id').setValue(element.id);
        control.controls[subCategoryIndex].get('subCategory').setValue(element.locationName);
        control.controls[subCategoryIndex].get('isDeleted').setValue(0);
        const tierControl = control.controls[subCategoryIndex].get('tier') as UntypedFormArray;
        element.tier.forEach((tierEle, tierIndex): void => {
          if (tierControl.controls[tierIndex]) {
            tierControl.controls[tierIndex].get('id').setValue(tierEle.id);
            tierControl.controls[tierIndex].get('tier').setValue(tierEle.locationName);
            tierControl.controls[tierIndex].get('isDeleted').setValue(0);
          } else {
            tierControl.push(
              this.fb.group({
                id: tierEle.id,
                tier: tierEle.locationName,
                isDeleted: 0,
              }),
            );
          }
        });
      } else {
        control.push(
          this.fb.group({
            id: element.id,
            subCategory: element.locationName,
            isDeleted: 0,
            tier: new UntypedFormArray([]), // Initialize an empty FormArray for tier
          }),
        );
        const newSubCategoryIndex = control.length - 1; // Get the index of the newly added subCategory
        element.tier.forEach((tierEle): void => {
          const tierControl = control.controls[newSubCategoryIndex].get('tier') as UntypedFormArray;
          tierControl.push(
            this.fb.group({
              id: tierEle.id,
              tier: tierEle.locationName,
              isDeleted: 0,
            }),
          );
        });
      }
    });
  }

  //  Init sub category
  public initSubCategory(): any {
    return this.fb.group({
      id: null,
      subCategory: [null],
      isDeleted: 0,
      tier: new UntypedFormArray([this.initLocationPaths()]),
    });
  }

  //  Init tier levels for sub category
  public initLocationPaths(): any {
    return this.fb.group({
      id: null,
      tier: [null],
      isDeleted: 0,
    });
  }

  public getSubLocationPaths(form): any {
    return form.controls.paths.controls;
  }

  public getTierPaths(form): any {
    return form.controls.tier.controls;
  }

  public addSubCategory(): void {
    this.subCatCount = 1;
    const temps = this.locationForm.get('paths') as UntypedFormArray;
    temps.push(this.initSubCategory());
  }

  public removeSubCategory(i): void {
    const temps = this.locationForm.get('paths') as UntypedFormArray;
    temps.controls[i].get('isDeleted').setValue(1);
    const temps2 = temps.controls[i].get('tier') as UntypedFormArray;
    this.lastRemovedSubCategoryIndex = i;
    const tierLength = temps2.length;
    const subCatLength = temps.length;
    for (let k = 0; k < tierLength; k++) {
      temps2.controls[k].get('isDeleted').setValue(1);
    }
    this.subCatCount = 0;
    for (let n = 0; n < subCatLength; n++) {
      if (temps.value[n].isDeleted === 0) {
        this.subCatCount++;
      }
    }
    if (this.subCatCount >= 1) {
      this.subCatCount = 1;
    } else {
      this.subCatCount = 0;
    }
    this.showAddTierButton[i] = false;
  }

  public addTier(i): void {
    this.tierCount = 1;
    const temps = this.locationForm.get('paths') as UntypedFormArray;
    const temps2 = temps.controls[i].get('tier') as UntypedFormArray;
    temps2.push(this.initLocationPaths());
    this.showAddTierButton[i] = false;
  }

  public removeTier(i, j): void {
    const temps = this.locationForm.get('paths') as UntypedFormArray;
    const temps2 = temps.controls[i].get('tier') as UntypedFormArray;
    temps2.controls[j].get('isDeleted').setValue(1);
    const tierLength = temps2.length;
    this.tierCount = 0;
    for (let n = 0; n < tierLength; n++) {
      if (temps2.value[n].isDeleted === 0) {
        this.tierCount++;
      }
    }
    if (this.tierCount >= 1) {
      this.tierCount = 1;
      this.showAddTierButton[i] = false;
    } else {
      this.tierCount = 0;
      this.showAddTierButton[i] = true;
    }
  }

  public deleteLocation(): void {
    this.deleteSubmitted = true;
    this.locationService
      .deleteLocation({
        id: this.currentItem.id,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      })
      .subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Deleted Location');
            this.getLocations();
            this.deleteSubmitted = false;
            this.modalRef.hide();
            this.currentItem = '';
          }
        },
        error: (deleteLocationError): void => {
          this.deleteSubmitted = false;
          this.getLocations();
          this.currentItem = '';
          if (deleteLocationError.message?.statusCode === 400) {
            this.showError(deleteLocationError);
            this.modalRef.hide();
          } else if (!deleteLocationError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deleteLocationError.message, 'OOPS!');
            this.modalRef.hide();
          }
        },
      });
  }

  public openDeleteModal(item: number, template: TemplateRef<any>): void {
    this.currentItem = item;
    this.modalRef = this.modalService.show(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-md add-location-modal custom-modal',
    });
  }

  public resetAndClose(): void {
    this.modalRef.hide();
    this.deleteSubmitted = false;
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }

  public openConfirmationModalPopup(template): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public close(template: TemplateRef<any>): void {
    if (this.locationForm.touched && this.locationForm.dirty) {
      this.openConfirmationModalPopup(template);
    } else {
      this.resetForm('yes');
    }
  }

  public resetForm(action): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.resetAddLocation();
    }
  }

  isLastSubCategory(index: number): boolean {
    const paths = this.locationForm.get('paths') as UntypedFormArray;
    return index === paths.controls.length - 1;
  }
}
