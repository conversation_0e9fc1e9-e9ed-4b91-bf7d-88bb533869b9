/* eslint-disable max-lines-per-function */
import {
  Component,
  TemplateRef,
  OnInit,
  Input,
} from '@angular/core';
import { BsModalRef, BsModalService, ModalOptions } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Observable } from 'rxjs';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import moment from 'moment';
import { Router } from '@angular/router';
import { ProjectService } from '../../../../services/profile/project.service';
import { DeliveryService } from '../../../../services/profile/delivery.service';
import { hoursDropdown } from '../../../../services/hoursDropdown';
import { minutesDropdown } from '../../../../services/minutesDropdown';
import { MixpanelService } from '../../../../services/mixpanel.service';
import { ProjectSettingsService } from '../../../../services/project_settings/project-settings.service';
import { ProjectSharingService } from '../../../../services/projectSharingService/project-sharing.service';


@Component({
  selector: 'app-edit-concrete-booking',
  templateUrl: './edit-concrete-booking.component.html',
})
export class EditConcreteBookingComponent implements OnInit {
  @Input() data: any;

  @Input() title: string;

  public concreteRequest: UntypedFormGroup;

  public submitted = false;

  public dropdownSettings: any = {};

  public modalLoader = false;

  public placementStart: Date;

  public placementEnd: Date;

  public concreteRequestId: any;

  public array3Value = [];

  public editBeforeCompany = [];

  public editBeforeLocation = [];

  public editBeforePumpsize = [];

  public editBeforeMixDesigns = [];

  public pumpWorkEnd: Date;

  public pumpWorkStart: Date;

  public hoursDropdown = hoursDropdown;

  public minutesDropdown = minutesDropdown;

  public concreteSupplierDropdownSettings: IDropdownSettings = {
    singleSelection: false,
    idField: 'id',
    textField: 'companyName',
    selectAllText: 'Select All',
    unSelectAllText: 'UnSelect All',
    itemsShowLimit: 6,
    allowSearchFilter: true,
  };

  public isDisabled = true;

  public authUser: any = {};

  public bindData: any;

  public ProjectId: number;

  public ParentCompanyId: number;

  public locationDropdown: any = [];

  public concreteSupplierDropdown: [];

  public mixDesignDropdown: [];

  public pumpSizeDropdown: [];

  public formSubmitted = false;

  public loader = false;

  public currentEditItem: any = {};

  public NDRTimingChanged = false;

  public formEdited = true;

  public locationList = [];

  public mixDesignList = [];

  public pumpSizeList = [];

  public primerOrderedDropdown = [
    { id: 1, data: 'Yes' },
    { id: 2, data: 'No' },
  ];

  public errmemberenable = false;

  public memberList: any = [];

  public deliveryWindowTime;

  public deliveryWindowTimeUnit;

  public seriesOption: number;

  public recurrenceId: number;

  public recurrenceEndDate;

  public isDisabledDate = false;

  public minDateOfrecurrenceEndDate;

  public selectedLocationId: any;

  public locationDropdownSettings: IDropdownSettings = {
    singleSelection: true,
    idField: 'id',
    textField: 'locationPath',
    allowSearchFilter: true,
    closeDropDownOnSelection: true,
  };

  public getChosenLocation: any;

  public locationDetailsDropdown: any[];

  public guestUserId: any;

  public constructor(
    private readonly modalService: BsModalService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly deliveryService: DeliveryService,
    private readonly projectService: ProjectService,
    private readonly modalRef: BsModalRef,
    private readonly toastr: ToastrService,
    public socket: Socket,
    private readonly mixpanelService: MixpanelService,
    public router: Router,
    private modalRef1: BsModalRef,
    public projectSettingsService: ProjectSettingsService,
    public projectSharingService: ProjectSharingService,
    private readonly option: ModalOptions,
  ) {
    this.ProjectId = +window.atob(localStorage.getItem('guestProjectId'));
    this.ParentCompanyId = +window.atob(localStorage.getItem('guestParentCompanyId'));
    this.guestUserId = +window.atob(localStorage.getItem('guestId'));
    this.concreteRequestCreationForm();
    this.getDropdownValues();
    this.getMembers();
    this.getProjectSettings();
  }

  public getDropdownValues(): any {
    this.modalLoader = true;
    if (this.ProjectId) {
      const payload = {
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.projectSharingService.guestGetConcreteRequestDropdownData(payload).subscribe((response): void => {
        if (response.data) {
          this.locationDropdown = response.data.locationDropdown;
          this.locationDetailsDropdown = response.data.locationDetailsDropdown;
          this.concreteSupplierDropdown = response.data.concreteSupplierDropdown;
          this.mixDesignDropdown = response.data.mixDesignDropdown;
          this.pumpSizeDropdown = response.data.pumpSizeDropdown;
        }
      });
    }
  }

  public ngOnInit(): void {
    const stateValue = this.data;
    this.concreteRequestId = stateValue.id;
    this.seriesOption = stateValue.seriesOption;
    this.getConcreteRequestData();
    const setStartTime = 7;
    this.placementStart = new Date();
    this.placementStart.setHours(setStartTime);
    this.placementStart.setMinutes(0);
    this.placementEnd = new Date();
    this.placementEnd.setHours(setStartTime + 1);
    this.placementEnd.setMinutes(0);
    this.pumpWorkStart = this.placementStart;
    this.pumpWorkEnd = this.placementEnd;
    this.concreteRequest.get('concretePlacementStart').setValue(this.placementStart);
    this.concreteRequest.get('concretePlacementEnd').setValue(this.placementEnd);
    this.concreteRequest.get('pumpWorkStart').setValue(this.pumpWorkStart);
    this.concreteRequest.get('pumpWorkEnd').setValue(this.pumpWorkEnd);
  }

  public locationSelected(data): void {
    this.getChosenLocation = this.locationDropdown.filter((obj: any): any => +obj.id === +data.id);
    this.selectedLocationId = this.getChosenLocation[0]?.id;
  }

  public getProjectSettings(): void {
    if (this.ProjectId) {
      const params = {
        ProjectId: this.ProjectId,
      };
      this.projectSettingsService.getGuestProjectSettings(params).subscribe((res): void => {
        const responseData = res.data;
        this.deliveryWindowTime = responseData.deliveryWindowTime;
        this.deliveryWindowTimeUnit = responseData.deliveryWindowTimeUnit;
      });
    }
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }

  public areDifferentByProperty(a, b, prop): boolean {
    const array1 = a.map((x): any => x[prop]);
    const array2 = b.map((x): any => x[prop]);
    this.array3Value = array1.concat(array2);
    this.array3Value = [...new Set([...array1, ...array2])];
    return this.array3Value.length !== array1.length;
  }

  public close(template: TemplateRef<any>): void {
    if (
      this.concreteRequest.touched
        || this.NDRTimingChanged
        || (this.concreteRequest.get('concreteSupplier').dirty
          && this.concreteRequest.get('concreteSupplier').value
          && this.concreteRequest.get('concreteSupplier').value.length > 0
          && this.areDifferentByProperty(
            this.editBeforeCompany,
            this.concreteRequest.get('concreteSupplier').value,
            'concreteSupplier',
          ))
        || (this.concreteRequest.get('pumpSize').dirty
          && this.concreteRequest.get('pumpSize').value
          && this.concreteRequest.get('pumpSize').value.length > 0
          && this.areDifferentByProperty(
            this.editBeforePumpsize,
            this.concreteRequest.get('pumpSize').value,
            'pumpSize',
          ))
        || (this.concreteRequest.get('mixDesign').dirty
          && this.concreteRequest.get('mixDesign').value
          && this.concreteRequest.get('mixDesign').value.length > 0
          && this.areDifferentByProperty(
            this.editBeforeMixDesigns,
            this.concreteRequest.get('mixDesign').value,
            'mixDesign',
          ))
    ) {
      this.openConfirmationModalPopupForEditConcreteRequest(template);
    } else {
      this.resetForm('yes');
    }
  }

  public openConfirmationModalPopupForEditConcreteRequest(template): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public QuantitynumberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode === 32 || charCode === 46) {
      return true;
    }
    if (charCode > 31 && (charCode < 48 || charCode > 57 || charCode !== 32 || charCode !== 46)) {
      return false;
    }
    return true;
  }

  public checkLocationDuplication(data): void {
    if (data && data.length >= 1) {
      const valueArr = data.map((item): any => item.location);
      const isDuplicate = valueArr.some((item, idx): any => valueArr.indexOf(item) !== idx);
      if (isDuplicate) {
        this.locationList.pop();
      }
    }
  }

  public checkMixDesignDuplication(data): void {
    if (data && data.length >= 1) {
      const valueArr = data.map((item): any => item.mixDesign);
      const isDuplicate = valueArr.some((item, idx): any => valueArr.indexOf(item) !== idx);
      if (isDuplicate) {
        this.mixDesignList.pop();
      }
    }
  }

  public checkPumpSizeDuplication(data): void {
    if (data && data.length >= 1) {
      const valueArr = data.map((item): any => item.pumpSize);
      const isDuplicate = valueArr.some((item, idx): any => valueArr.indexOf(item) !== idx);
      if (isDuplicate) {
        this.pumpSizeList.pop();
      }
    }
  }

  public changeDate(event: any): void {
    if (!this.modalLoader) {
      const startTime = new Date(event).getHours();
      const minutes = new Date(event).getMinutes();
      this.placementEnd = new Date();
      this.placementEnd.setHours(startTime + 1);
      this.placementEnd.setMinutes(minutes);
      this.concreteRequest.get('concretePlacementEnd').setValue(this.placementEnd);
      this.NDRTimingChanged = true;
    }
    this.onEditSubmitForm();
  }

  public changeDate1(event: any): void {
    if (!this.modalLoader) {
      const startTime = new Date(event).getHours();
      const minutes = new Date(event).getMinutes();
      this.pumpWorkEnd = new Date();
      this.pumpWorkEnd.setHours(startTime + 1);
      this.pumpWorkEnd.setMinutes(minutes);
      this.concreteRequest.get('pumpWorkEnd').setValue(this.pumpWorkEnd);
      this.NDRTimingChanged = true;
    }
    this.onEditSubmitForm();
  }

  public createConcreteRequest(payload): void {
    this.projectSharingService.guestEditConcreteRequest(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.mixpanelService.addGuestUserMixpanelEvents('Edited Concrete Booking');
          this.socket.emit('ConcreteEditHistory', response);
          this.formReset();
          this.concreteRequest.reset();
          this.closeForm();
          this.NDRTimingChanged = false;
          this.formEdited = false;
        }
      },
      error: (NDRCreateHistoryError): void => {
        this.formReset();
        this.NDRTimingChanged = false;
        this.formEdited = false;
        if (NDRCreateHistoryError.message?.statusCode === 400) {
          this.showError(NDRCreateHistoryError);
        } else if (!NDRCreateHistoryError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(NDRCreateHistoryError.message, 'OOPS!');
        }
      },
    });
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public onEditSubmitForm(): void {
    if (this.concreteRequest.dirty || this.NDRTimingChanged) {
      this.formEdited = false;
    }
  }

  public gatecheck(value, menuname): void {
    if (menuname === 'Person') {
      const result = this.memberList.filter((o): any => value?.some(({ id }): any => o.id === id));
      if (value && value.length !== result.length) {
        this.errmemberenable = true;
      } else {
        this.errmemberenable = false;
      }
    }
  }

  public getIndex(formValue) {
    const arr1 = this.memberList;
    const arr2 = formValue.responsiblePersons;
    let index2: number;
    const result = arr1.filter((o) => arr2.some(({ id }) => o.id === id));
    if (formValue.responsiblePersons.length !== result.length) {
      index2 = -1;
    } else {
      index2 = 0;
    }
    return index2;
  }

  public onSubmit(): void {
    if (this.concreteRequest.dirty || this.NDRTimingChanged) {
      this.formEdited = false;
    }
    this.submitted = true;
    this.formSubmitted = true;
    const persons = [];
    if (this.concreteRequest.invalid) {
      this.formSubmitted = false;
      return;
    }
    const formValue = this.concreteRequest.value;
    const placementDate = new Date(formValue.concretePlacementDate);
    const startHours = new Date(formValue.concretePlacementStart).getHours();
    const startMinutes = new Date(formValue.concretePlacementStart).getMinutes();
    const placementStart = this.convertStart(placementDate, startHours, startMinutes);
    const endHours = new Date(formValue.concretePlacementEnd).getHours();
    const endMinutes = new Date(formValue.concretePlacementEnd).getMinutes();
    const placementEnd = this.convertStart(placementDate, endHours, endMinutes);
    const placementDate1 = new Date(formValue.pumpOrderedDate);
    const startHours1 = new Date(formValue.pumpWorkStart).getHours();
    const startMinutes1 = new Date(formValue.pumpWorkStart).getMinutes();
    const placementStart1 = this.convertStart(placementDate1, startHours1, startMinutes1);
    const endHours1 = new Date(formValue.pumpWorkEnd).getHours();
    const endMinutes1 = new Date(formValue.pumpWorkEnd).getMinutes();
    const placementEnd1 = this.convertStart(placementDate1, endHours1, endMinutes1);
    if (this.checkConcreteRequestCreateFutureDate(placementStart, placementEnd)) {
      const index2 = this.getIndex(formValue);
      if (index2 === -1) {
        if (index2 === -1) {
          this.errmemberenable = true;
        }
        this.formSubmitted = false;
      } else {
        if (index2 === -1) {
          this.errmemberenable = false;
        }
        this.checkRequestStatusAndUserRole(
          formValue,
          placementStart,
          placementEnd,
          persons,
          placementStart1,
          placementEnd1,
        );
      }
    } else {
      this.checkRequestStatusAndUserRole(
        formValue,
        placementStart,
        placementEnd,
        persons,
        placementStart1,
        placementEnd1,
      );
    }
  }

  public checkRequestStatusAndUserRole(
    formValue,
    placementStart,
    placementEnd,
    persons,
    placementStart1,
    placementEnd1,
  ): void {
    if (
      !this.validateRequest(
        formValue,
        placementStart,
        placementEnd,
        placementStart1,
        placementEnd1,
      )
    ) {
      return;
    }

    this.processRequest(
      formValue,
      placementStart,
      placementEnd,
      persons,
      placementStart1,
      placementEnd1,
    );
  }

  public validateRequest(
    formValue,
    placementStart,
    placementEnd,
    placementStart1,
    placementEnd1,
  ): boolean {
    if (this.checkNewPlacementStartEndSame(placementStart, placementEnd)) {
      this.toastr.error('Placement start time and End time should not be the same');
      this.formSubmitted = false;
      return false;
    }

    if (
      formValue.isPumpRequired
      && this.checkNewPlacementStartEndSame(placementStart1, placementEnd1)
    ) {
      this.toastr.error('Pump Start time and End time should not be the same');
      this.formSubmitted = false;
      return false;
    }

    const roleId = this.authUser.RoleId;
    const { status } = this.currentEditItem;

    if (status === 'Completed' && roleId !== 2) {
      if (
        moment(this.currentEditItem.concretePlacementStart).format('MM/DD/YYYY')
          !== moment(this.concreteRequest.get('concretePlacementDate').value).format('MM/DD/YYYY')
        || new Date(this.currentEditItem.concretePlacementStart).getTime()
          !== new Date(this.concreteRequest.get('concretePlacementStart').value).getTime()
        || new Date(this.currentEditItem.concretePlacementEnd).getTime()
          !== new Date(this.concreteRequest.get('concretePlacementEnd').value).getTime()
      ) {
        this.toastr.error('You are not allowed to change the date/time');
        this.formSubmitted = false;
        return false;
      }
    }

    if (
      status === 'Approved'
      && roleId !== 2
      && roleId !== 3
    ) {
      if (
        moment(this.currentEditItem.concretePlacementDate).format('MM/DD/YYYY')
          !== moment(this.concreteRequest.get('concretePlacementDate').value).format('MM/DD/YYYY')
        || new Date(this.currentEditItem.concretePlacementStart).getTime()
          !== new Date(this.concreteRequest.get('concretePlacementStart').value).getTime()
        || new Date(this.currentEditItem.concretePlacementEnd).getTime()
          !== new Date(this.concreteRequest.get('concretePlacementEnd').value).getTime()
      ) {
        if (!this.checkConcreteRequestCreateFutureDate(placementStart, placementEnd)) {
          this.toastr.error(
            'Booking not allowed to edit. Please contact the project administrator to edit this booking',
          );
          this.formSubmitted = false;
          this.submitted = false;
          return false;
        }
      }
    }

    return true;
  }

  public processRequest(
    formValue,
    placementStart,
    placementEnd,
    persons,
    placementStart1,
    placementEnd1,
  ): void {
    const roleId = this.authUser.RoleId;

    if (roleId === 1 || roleId === 2 || roleId === 3) {
      this.createPlacement(
        formValue,
        placementStart,
        placementEnd,
        persons,
        placementStart1,
        placementEnd1,
      );
      return;
    }

    if (formValue.isPumpRequired) {
      if (!this.checkConcreteRequestCreateFutureDate(placementStart1, placementEnd1)) {
        this.toastr.error('Please Enter Valid Pump Ordered Date.');
        this.submitted = false;
        this.formSubmitted = false;
        return;
      }
    }

    if (this.checkConcreteRequestCreateFutureDate(placementStart, placementEnd)) {
      this.createPlacement(
        formValue,
        placementStart,
        placementEnd,
        persons,
        placementStart1,
        placementEnd1,
      );
    } else {
      this.toastr.error(
        'Booking not allowed to edit. Please contact the project administrator to edit this booking',
      );
      this.submitted = false;
      this.formSubmitted = false;
    }
  }


  public checkNewPlacementStartEnd(newDeliveryStart, newDeliveryEnd): boolean {
    const newStartDate = new Date(newDeliveryStart).getTime();
    const newEndDate = new Date(newDeliveryEnd).getTime();
    if (newStartDate < newEndDate) {
      return true;
    }
    return false;
  }

  public createPlacement(
    formValue,
    placementStart,
    placementEnd,
    responsbilePersonsData,
    placementStart1,
    placementEnd1,
  ): void {
    if (
      !this.validatePlacementTimes(
        formValue,
        placementStart,
        placementEnd,
        placementStart1,
        placementEnd1,
      )
    ) {
      return;
    }

    const preparedData = this.preparePlacementData(formValue, responsbilePersonsData);

    if (!preparedData) {
      return;
    }

    this.submitPlacement(
      formValue,
      placementStart,
      placementEnd,
      placementStart1,
      placementEnd1,
      preparedData,
    );
  }

  public validatePlacementTimes(
    formValue, placementStart, placementEnd, placementStart1, placementEnd1,
  ): boolean {
    if (!this.checkNewPlacementStartEnd(placementStart, placementEnd)) {
      this.submitted = false;
      this.formSubmitted = false;
      this.toastr.error('Please enter placement start time lesser than end time');
      return false;
    }

    if (
      formValue.isPumpRequired
      && !this.checkNewPlacementStartEnd(placementStart1, placementEnd1)
    ) {
      this.submitted = false;
      this.formSubmitted = false;
      this.toastr.error('Please enter pump start time lesser than end time');
      return false;
    }

    return true;
  }

  public preparePlacementData(formValue, responsbilePersonsData): any {
    const concreteSupplier = [];
    const mixDesign = [];
    const pumpSize = [];

    const newNdrPersonDetails = formValue.responsiblePersons;

    if (!newNdrPersonDetails || newNdrPersonDetails.length === 0) {
      this.formReset();
      this.toastr.error('Responsible Person is required');
      return null;
    }

    newNdrPersonDetails.forEach((element) => {
      responsbilePersonsData.push(element.id);
    });

    formValue.concreteSupplier?.forEach((element) => {
      concreteSupplier.push(element.id);
    });

    formValue.mixDesign?.forEach((element) => {
      if (Number(element.id) && element.id !== element.mixDesign) {
        mixDesign.push({ id: element.id, mixDesign: element.mixDesign, chosenFromDropdown: true });
      } else {
        mixDesign.push({ id: null, mixDesign: element.mixDesign, chosenFromDropdown: false });
      }
    });

    formValue.pumpSize?.forEach((element) => {
      if (Number(element.id) && element.id !== element.pumpSize) {
        pumpSize.push({ id: element.id, pumpSize: element.pumpSize, chosenFromDropdown: true });
      } else {
        pumpSize.push({ id: null, pumpSize: element.pumpSize, chosenFromDropdown: false });
      }
    });

    if (this.checkStringEmptyValues(formValue)) {
      this.formReset();
      return null;
    }

    return {
      concreteSupplier, mixDesign, pumpSize, responsbilePersonsData,
    };
  }

  public submitPlacement(
    formValue,
    placementStart,
    placementEnd,
    placementStart1,
    placementEnd1,
    data,
  ): void {
    const isPumpConfirmed = formValue.isPumpConfirmed ?? false;
    const isPumpRequired = formValue.isPumpRequired ?? false;
    const isConcreteConfirmed = formValue.isConcreteConfirmed ?? false;
    const pumpRequired = this.concreteRequest.get('isPumpRequired').value === true;

    const payload = {
      id: formValue.id,
      location: formValue.location,
      LocationDetailId: formValue.LocationDetailId,
      description: formValue.description,
      LocationId: this.selectedLocationId,
      ProjectId: this.ProjectId,
      userId: this.guestUserId,
      concreteSupplier: data.concreteSupplier,
      notes: formValue.notes,
      concretePlacementStart: placementStart,
      concretePlacementEnd: placementEnd,
      isPumpConfirmed: pumpRequired ? isPumpConfirmed : false,
      isPumpRequired,
      isConcreteConfirmed,
      ParentCompanyId: this.ParentCompanyId,
      responsiblePersons: data.responsbilePersonsData,
      concreteOrderNumber: formValue.concreteOrderNumber,
      truckSpacingHours: formValue.truckSpacingHours,
      mixDesign: data.mixDesign,
      slump: formValue.slump,
      concreteQuantityOrdered: formValue.concreteQuantityOrdered,
      concreteConfirmedOn: formValue.concreteConfirmedOn,
      pumpSize: pumpRequired ? data.pumpSize : [],
      pumpLocation: pumpRequired ? formValue.pumpLocation : null,
      pumpOrderedDate: pumpRequired ? formValue.pumpOrderedDate : null,
      pumpWorkStart: pumpRequired ? placementStart1 : null,
      pumpWorkEnd: pumpRequired ? placementEnd1 : null,
      pumpConfirmedOn: formValue.pumpConfirmedOn,
      cubicYardsTotal: formValue.cubicYardsTotal,
      hoursToCompletePlacement: formValue.hoursToCompletePlacement,
      minutesToCompletePlacement: formValue.minutesToCompletePlacement,
      requestType: 'concreteRequest',
      primerForPump: formValue.primerForPump,
      ConcreteRequestId: formValue.ConcreteRequestId,
      seriesOption: this.seriesOption,
      recurrenceId: formValue.recurrenceId,
      recurrenceEndDate: formValue.recurrenceEndDate
        ? moment(formValue.recurrenceEndDate).format('YYYY-MM-DD')
        : null,
      recurrenceSeriesStartDate: moment(formValue.concretePlacementStart).format('YYYY-MM-DD'),
      recurrenceSeriesEndDate: moment(formValue.concretePlacementEnd).format('YYYY-MM-DD'),
      previousSeriesRecurrenceEndDate: moment(formValue.concretePlacementStart)
        .add(-1, 'days')
        .format('YYYY-MM-DD'),
      nextSeriesRecurrenceStartDate: moment(formValue.concretePlacementStart)
        .add(1, 'days')
        .format('YYYY-MM-DD'),
      deliveryStartTime: moment(formValue.concretePlacementStart).format('HH:mm'),
      deliveryEndTime: moment(formValue.concretePlacementEnd).format('HH:mm'),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    };

    this.createConcreteRequest(payload);
  }


  public checkStringEmptyValues(formValue): boolean {
    if (formValue.description.trim() === '') {
      this.toastr.error('Please enter valid description', 'OOPS!');
      return true;
    }
    if (formValue.isPumpRequired && formValue.pumpLocation.trim() === '') {
      this.toastr.error('Please enter valid pump location', 'OOPS!');
      return true;
    }
    if (formValue.cubicYardsTotal && formValue.cubicYardsTotal.trim() === '') {
      this.toastr.error('Please enter valid number of cubic yards', 'OOPS!');
      return true;
    }
    return false;
  }

  public throwEndError(): void {
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error('Please Enter Start time Lesser than End time');
  }

  public formReset(): void {
    this.formSubmitted = false;
    this.submitted = false;
  }

  public checkConcreteRequestCreateFutureDate(start, end): boolean {
    const newDeliveryStartDate = moment(new Date(start));
    const newDeliveryCurrentDate = moment()
      .clone()
      .add(this.deliveryWindowTime, this.deliveryWindowTimeUnit);
    const newDeliveryEndDate = moment(new Date(end));
    if (
      newDeliveryStartDate.isAfter(newDeliveryCurrentDate)
        && newDeliveryEndDate.isAfter(newDeliveryCurrentDate)
    ) {
      return true;
    }
    return false;
  }

  public convertStart(placementDate, startHours, startMinutes): string {
    const fullYear = placementDate.getFullYear();
    const fullMonth = placementDate.getMonth();
    const date = placementDate.getDate();
    const placementNewStart = new Date(fullYear, fullMonth, date, startHours, startMinutes);
    const placementStart = placementNewStart.toUTCString();
    return placementStart;
  }

  public checkNewPlacementStartEndSame(newPlacementStartTime, newPlacementEndTime): boolean {
    const startDate = new Date(newPlacementStartTime).getTime();
    const endDate = new Date(newPlacementEndTime).getTime();
    if (startDate === endDate) {
      return true;
    }
    return false;
  }

  public resetForm(action): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      if (this.modalRef) {
        this.modalRef.hide();
      }
      this.concreteRequest.reset();
      this.NDRTimingChanged = false;
      this.formSubmitted = false;
      this.submitted = false;
      this.formEdited = true;
      this.getDropdownValues();
    }
  }

  public concreteRequestCreationForm(): any {
    this.concreteRequest = this.formBuilder.group({
      id: [''],
      ConcreteRequestId: [''],
      location: [''],
      LocationId: ['', Validators.compose([Validators.required])],
      LocationDetailId: ['', Validators.compose([Validators.required])],
      responsiblePersons: [''],
      description: ['', Validators.compose([Validators.required])],
      concreteSupplier: ['', Validators.compose([Validators.required])],
      concreteOrderNumber: [''],
      truckSpacingHours: [''],
      notes: [''],
      primerForPump: [''],
      mixDesign: [''],
      slump: [''],
      concreteQuantityOrdered: [''],
      concreteConfirmedOn: [''],
      isConcreteConfirmed: ['', Validators.compose([Validators.required])],
      pumpSize: [''],
      pumpLocation: [''],
      pumpOrderedDate: [''],
      pumpWorkStart: [''],
      pumpWorkEnd: [''],
      pumpConfirmedOn: [''],
      isPumpConfirmed: [''],
      isPumpRequired: ['', Validators.compose([Validators.required])],
      cubicYardsTotal: [''],
      hoursToCompletePlacement: [''],
      minutesToCompletePlacement: [''],
      concretePlacementDate: ['', Validators.compose([Validators.required])],
      concretePlacementStart: ['', Validators.compose([Validators.required])],
      concretePlacementEnd: ['', Validators.compose([Validators.required])],
      recurrenceId: [''],
      recurrence: [''],
      recurrenceEndDate: [''],
    });
    this.formControlValueChanged();
  }

  public requestAutoEditcompleteItems = (text: string): Observable<any> => {
    const param = {
      ProjectId: this.ProjectId,
      search: text,
      ParentCompanyId: this.ParentCompanyId,
    };
    return this.projectSharingService.guestSearchNewMember(param);
  };

  public formControlValueChanged(): any {
    const pumpSize = this.concreteRequest.get('pumpSize');
    const pumpLocation = this.concreteRequest.get('pumpLocation');
    const pumpOrderedDate = this.concreteRequest.get('pumpOrderedDate');
    const pumpWorkStart = this.concreteRequest.get('pumpWorkStart');
    const pumpWorkEnd = this.concreteRequest.get('pumpWorkEnd');
    this.concreteRequest.get('isPumpRequired').valueChanges.subscribe((value: boolean): any => {
      if (value === true) {
        pumpSize.setValidators([Validators.required]);
        pumpLocation.setValidators([Validators.required]);
        pumpOrderedDate.setValidators([Validators.required]);
        pumpWorkStart.setValidators([Validators.required]);
        pumpWorkEnd.setValidators([Validators.required]);
        this.concreteRequest.get('pumpWorkStart').setValue(new Date(this.pumpWorkStart));
        this.concreteRequest.get('pumpWorkEnd').setValue(new Date(this.pumpWorkEnd));
        this.concreteRequest
          .get('pumpOrderedDate')
          .setValue(
            moment(this.concreteRequest.get('concretePlacementDate').value).format('MM/DD/YYYY'),
          );
      } else if (value === false || value === null) {
        pumpSize.clearValidators();
        pumpLocation.clearValidators();
        pumpOrderedDate.clearValidators();
        pumpWorkStart.clearValidators();
        pumpWorkEnd.clearValidators();
      }
      pumpSize.updateValueAndValidity();
      pumpLocation.updateValueAndValidity();
      pumpOrderedDate.updateValueAndValidity();
      pumpWorkStart.updateValueAndValidity();
      pumpWorkEnd.updateValueAndValidity();
    });
    this.concreteRequest.get('concretePlacementDate').valueChanges.subscribe((value: any): void => {
      this.concreteRequest
        .get('pumpOrderedDate')
        .setValue(
          moment(this.concreteRequest.get('concretePlacementDate').value).format('MM/DD/YYYY'),
        );
    });
  }


  public getConcreteRequestData(): void {
    this.loader = true;
    this.currentEditItem = {};
    const param = {
      ConcreteRequestId: this.concreteRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    this.projectSharingService.guestGetConcreteRequestDetail(param).subscribe((res): void => {
      this.currentEditItem = res.data;
      this.concreteRequest.get('id').setValue(this.currentEditItem.id);
      this.concreteRequest
        .get('ConcreteRequestId')
        .setValue(this.currentEditItem.ConcreteRequestId);
      this.concreteRequest.get('description').setValue(this.currentEditItem.description);
      this.concreteRequest.get('LocationDetailId').setValue(this.currentEditItem.locationDetails[0].ConcreteLocation?.id);
      this.concreteRequest.get('location').setValue(this.currentEditItem.locationDetails[0].ConcreteLocation?.location);
      this.concreteRequest.get('notes').setValue(this.currentEditItem.notes);
      this.concreteRequest
        .get('isPumpConfirmed')
        .setValue(
          this.currentEditItem.isPumpConfirmed ? this.currentEditItem.isPumpConfirmed : null,
        );
      this.concreteRequest
        .get('concretePlacementDate')
        .setValue(moment(this.currentEditItem.concretePlacementStart).format('MM/DD/YYYY'));
      this.concreteRequest
        .get('concretePlacementStart')
        .setValue(new Date(this.currentEditItem.concretePlacementStart));
      this.concreteRequest
        .get('concretePlacementEnd')
        .setValue(new Date(this.currentEditItem.concretePlacementEnd));
      this.concreteRequest.get('isPumpRequired').setValue(this.currentEditItem.isPumpRequired);
      this.concreteRequest
        .get('isConcreteConfirmed')
        .setValue(this.currentEditItem.isConcreteConfirmed);
      this.concreteRequest
        .get('concreteOrderNumber')
        .setValue(this.currentEditItem.concreteOrderNumber);
      this.concreteRequest
        .get('truckSpacingHours')
        .setValue(this.currentEditItem.truckSpacingHours);
      this.concreteRequest.get('mixDesign').setValue(this.currentEditItem.mixDesign);
      this.concreteRequest.get('slump').setValue(this.currentEditItem.slump);
      this.concreteRequest
        .get('concreteQuantityOrdered')
        .setValue(this.currentEditItem.concreteQuantityOrdered);
      this.concreteRequest.get('primerForPump').setValue(this.currentEditItem.primerForPump);
      this.concreteRequest
        .get('concreteConfirmedOn')
        .setValue(this.currentEditItem.concreteConfirmedOn);
      this.concreteRequest.get('pumpSize').setValue(this.currentEditItem.pumpSize);
      this.concreteRequest.get('pumpLocation').setValue(this.currentEditItem.pumpLocation);
      this.concreteRequest
        .get('pumpOrderedDate')
        .setValue(moment(this.currentEditItem.pumpOrderedDate).format('MM/DD/YYYY'));
      this.concreteRequest
        .get('pumpWorkStart')
        .setValue(new Date(this.currentEditItem.pumpWorkStart));
      this.concreteRequest.get('pumpWorkEnd').setValue(new Date(this.currentEditItem.pumpWorkEnd));
      this.concreteRequest.get('pumpConfirmedOn').setValue(this.currentEditItem.pumpConfirmedOn);
      this.concreteRequest.get('cubicYardsTotal').setValue(this.currentEditItem.cubicYardsTotal);
      this.concreteRequest
        .get('hoursToCompletePlacement')
        .setValue(this.currentEditItem.hoursToCompletePlacement);
      this.concreteRequest
        .get('minutesToCompletePlacement')
        .setValue(this.currentEditItem.minutesToCompletePlacement);
      this.concreteRequest.get('recurrenceId').setValue(this.currentEditItem?.recurrence?.id);
      this.concreteRequest.get('recurrence').setValue(this.currentEditItem?.recurrence?.recurrence);
      if (this.currentEditItem?.recurrence?.recurrenceEndDate) {
        this.concreteRequest
          .get('recurrenceEndDate')
          .setValue(
            moment(this.currentEditItem?.recurrence?.recurrenceEndDate).format('MM/DD/YYYY'),
          );
      }
      this.minDateOfrecurrenceEndDate = new Date(
        this.currentEditItem?.recurrence?.recurrenceEndDate,
      );
      this.setDropdownValues();
    });
  }

  public openContentModal(): void {
    this.modalLoader = false;
  }

  public setDropdownValues(): void {
    const {
      memberDetails,
      location,
      concreteSupplierDetails,
      pumpSizeDetails,
      mixDesignDetails,
    } = this.currentEditItem;
    const newMemberList = [];
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      id: this.guestUserId,
    };
    this.projectSharingService.guestGetMemberRole(params).subscribe((res): void => {
      this.authUser = res.data;
      if (memberDetails !== undefined) {
        memberDetails.forEach(
          (element: {
            Member: { User: { firstName: any; lastName: any; email: any }; id: any };
          }): void => {
            let email: string;
            if (element.Member?.User?.firstName != null) {
              email = `${element.Member?.User?.firstName} ${element.Member?.User?.lastName} (${element.Member?.User?.email})`;
            } else {
              email = `(${element.Member?.User?.email})`;
            }
            const data: any = {
              email,
              id: element.Member?.id,
            };
            if (element.Member?.User?.email === this.authUser?.User?.email) {
              data.readonly = true;
            }
            if (element.Member?.User?.email) {
              newMemberList.push(data);
            }
          },
        );
      }
    });
    this.concreteRequest.get('responsiblePersons').patchValue(newMemberList);

    if (location) {
      this.getChosenLocation = [];
      this.getChosenLocation.push({
        id: location.id,
        locationPath: location.locationPath,
      });
      this.concreteRequest.get('LocationId').setValue(this.getChosenLocation);
      this.selectedLocationId = location.id;
    }
    const mixDesignTags = [];
    mixDesignDetails.forEach((element): void => {
      if (element?.ConcreteMixDesign?.mixDesign) {
        mixDesignTags.push({
          id: element.ConcreteMixDesign.id,
          mixDesign: element.ConcreteMixDesign.mixDesign,
        });
      }
    });
    this.editBeforeMixDesigns = mixDesignTags;
    this.concreteRequest.get('mixDesign').patchValue(mixDesignTags);
    this.mixDesignList = mixDesignTags;
    const pumpSizeTags = [];
    pumpSizeDetails.forEach((element): void => {
      if (element?.ConcretePumpSize?.pumpSize) {
        pumpSizeTags.push({
          id: element.ConcretePumpSize.id,
          pumpSize: element.ConcretePumpSize.pumpSize,
        });
      }
    });
    this.editBeforePumpsize = pumpSizeTags;
    this.concreteRequest.get('pumpSize').patchValue(pumpSizeTags);
    this.pumpSizeList = pumpSizeTags;
    const newCompanyList = [];
    concreteSupplierDetails.forEach((element): void => {
      const data = {
        id: element.Company.id,
        companyName: element.Company.companyName,
      };
      newCompanyList.push(data);
    });
    this.editBeforeCompany = newCompanyList;
    this.concreteRequest.get('concreteSupplier').patchValue(newCompanyList);
    this.openContentModal();
    this.seriesOption = this.modalRef?.content?.seriesOption;
    this.isDisabledDate = this.seriesOption !== 1;
  }

  public deliveryEndTimeChangeDetection(): void {
    this.NDRTimingChanged = true;
    this.onEditSubmitForm();
  }

  public changeConcreteConfirmed(data): void {
    if (data) {
      this.concreteRequest.get('isConcreteConfirmed').setValue(true);
      this.concreteRequest.get('concreteConfirmedOn').setValue(new Date());
    } else {
      this.concreteRequest.get('isConcreteConfirmed').setValue(null);
      this.concreteRequest.get('concreteConfirmedOn').setValue(null);
    }
  }

  public changePumpConfirmed(data): void {
    if (data) {
      this.concreteRequest.get('isPumpConfirmed').setValue(true);
      this.concreteRequest.get('pumpConfirmedOn').setValue(new Date());
    } else {
      this.concreteRequest.get('isPumpConfirmed').setValue(null);
      this.concreteRequest.get('pumpConfirmedOn').setValue(null);
    }
  }

  public closeForm(): void {
    if (this.modalRef1) {
      this.modalRef1.hide();
    }
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.concreteRequest.reset();
    this.NDRTimingChanged = false;
    this.formSubmitted = false;
    this.submitted = false;
    this.formEdited = true;
    this.getDropdownValues();
    this.router.navigate(['/guest-concrete-calendar']);
  }
}
