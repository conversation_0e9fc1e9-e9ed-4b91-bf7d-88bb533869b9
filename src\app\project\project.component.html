<div class="modal-header px-0 py-2 border-0">
        <button type="button" class="close ms-auto" aria-label="Close" (click)="close()">
      <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close"></span>
    </button>

  </div>

  <div class="mb-4 d-flex text-center">
    <button (click)="redirect('dashboard')"  class="btn btn-orange fs14 fw-bold cairo-regular shadow-none ms-3 me-4 me-md-5" *ngIf="!accountAdmin && formExecution">Back</button>
    <h1 class="color-grey7 fs18 fw-bold cairo-regular my-2 ms-md-5">Add New Project</h1>
  </div>
  <div class="modal-body pt-0 px-md-5 pb-5">
    <p *ngIf="!formExecution" class="text-center">Loading...</p>
    <form class="custom-material-form mt-5" name="form" [formGroup]="projectDetailsForm" (ngSubmit)="onSubmit()"  novalidate *ngIf="formExecution">
      <div class="row">
        <div class="col-md-12">
          <div class="form-group mb-4">
            <label for="proName" class="color-orange fs13">Enter Your Project Name</label>
              <input id="proName"  type="text" class="form-control material-input fs13 " placeholder="Project Name" formControlName="projectName" (keypress)="alphaNum($event)" >
              <div class="color-red" *ngIf="submitted && projectDetailsForm.get('projectName').errors">
                <small *ngIf="projectDetailsForm.get('projectName').errors.required">*Enter Project Name</small>
             </div>
          </div>
          <div class="mb-4 project-details-map" id="agm-location">
            <label for="loc" class="color-orange fs13">Select the Location</label>
            <google-map class="agm-map project-map" [options]="mapOptions" (mapClick)="markerDragEnd($event)" id="loc">
              <map-marker [position]="marker.position" [options]="marker.options" (mapDragend)="markerDragEnd($event)"></map-marker>
            </google-map>
          </div>

          <div class="form-group mb-4">
            <label for="proAdd" class="color-orange fs13">Project Address</label>
              <input id="proAdd" type="text" class="form-control material-input fs14" formControlName="projectLocation" ngx-gp-autocomplete (onAddressChange)="handleProjectAddressLocation($event)" #placesRef="ngx-places">
            <div class="color-red" *ngIf="submitted && projectDetailsForm.get('projectLocation').errors">
              <small *ngIf="projectDetailsForm.get('projectLocation').errors.required">*Project Location is required.</small>
           </div>
          </div>
          <div class="row" *ngIf="accountAdmin">
            <div class="col-md-6 col-sm-12 ">
               <div class="form-group">
                <label for="proStart" class="fs12 fw600 color-orange">Project Start Date</label>
                <div class="input-group mb-3">
                    <input id="proStart" class="form-control fs10  material-input" #dp="bsDatepicker" bsDatepicker
                      placeholder="Start Date" [bsConfig]="{ isAnimated: true, adaptivePosition: true, showWeekNumbers:false,'customTodayClass': 'today' }" formControlName="startDate">
                      <span class="input-group-text">
                        <img src="./assets/images/date.svg" class="h-12px" alt="Date"  (click)="dp.toggle()" [attr.aria-expanded]="dp.isOpen" (keydown) = "dp.toggle()" >
                      </span>
                  </div>
               </div>
               <div class="color-red" *ngIf="submitted && projectDetailsForm.get('startDate').errors">
                <small *ngIf="projectDetailsForm.get('startDate').errors.required">* Start Date is Requried</small>
             </div>

            </div>
            <div class="col-md-6 col-sm-12 ">
                <div class="form-group">
                 <label for="proEnd" class="fs12 fw600 color-orange">Project End Date</label>
                 <div class="input-group mb-3">
                     <input class="form-control fs10  material-input" #enddp="bsDatepicker" bsDatepicker  id="proEnd" formControlName="endDate"
                       placeholder="End Date" [bsConfig]="{ isAnimated: true, adaptivePosition: true,showWeekNumbers:false,'customTodayClass': 'today' }" >
                       <span class="input-group-text">
                         <img src="./assets/images/date.svg" class="h-12px" alt="Date"  (click)="enddp.toggle()" [attr.aria-expanded]="enddp.isOpen" (keydown) = "enddp.toggle()">
                       </span>
                   </div>
                </div>
                <div class="color-red" *ngIf="submitted && projectDetailsForm.get('endDate').errors">
                  <small *ngIf="projectDetailsForm.get('endDate').errors.required">* End Date is Requried</small>
               </div>

             </div>
          </div>
          <div class="text-center mt-5">
            <button class="btn btn-grey-light color-dark-grey radius20 fs12 mb-3 me-1 me-sm-3 me-md-3 fw-bold cairo-regular   px-5" (click)="close()" type="button">Cancel</button>
            <button class="btn btn-orange radius20 fs12 fw-bold cairo-regular mb-3 px-5" type="submit"><em class="fa fa-spinner" aria-hidden="true" *ngIf="submitted && projectDetailsForm.valid"></em> Add</button>
          </div>
        </div>
      </div>
    </form>
</div>

