import { of } from 'rxjs';
import { TestBed } from '@angular/core/testing';
import { Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { UserIdleService } from 'angular-user-idle';
import { AuthGuard } from './auth.guard';
import { AuthService } from './services/auth/auth.service';
import { ProjectService } from './services/profile/project.service';

describe('AuthGuard', () => {
  let guard: AuthGuard;
  let authService: Partial<AuthService>;
  let router: Partial<Router>;
  let projectService: Partial<ProjectService>;
  let userIdle: Partial<UserIdleService>;

  const mockRoute = {} as ActivatedRouteSnapshot;
  const mockRouterState = { url: '/test' } as RouterStateSnapshot;

  beforeEach(() => {
    const authServiceMock = {
      loggeduserIn: jest.fn(),
      logout: jest.fn(),
    };

    const routerMock = {
      navigate: jest.fn(),
    };

    const projectServiceMock = {
      getSingleProject: jest.fn(),
      updatedSubStatus: jest.fn(),
      projectId: of('123'),
    };

    const userIdleMock = {
      resetTimer: jest.fn(),
      stopTimer: jest.fn(),
      stopWatching: jest.fn(),
      startWatching: jest.fn(),
      onTimerStart: jest.fn().mockReturnValue(of(0)),
      onTimeout: jest.fn().mockReturnValue(of(null)),
    };

    TestBed.configureTestingModule({
      providers: [
        AuthGuard,
        { provide: AuthService, useValue: authServiceMock },
        { provide: Router, useValue: routerMock },
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: UserIdleService, useValue: userIdleMock }
      ]
    });

    guard = TestBed.inject(AuthGuard);
    authService = TestBed.inject(AuthService);
    router = TestBed.inject(Router);
    projectService = TestBed.inject(ProjectService);
    userIdle = TestBed.inject(UserIdleService);
  });

  it('should be created', () => {
    expect(guard).toBeTruthy();
  });

  describe('canActivate', () => {
    it('should redirect to login if user is not logged in', () => {
      (authService.loggeduserIn as jest.Mock).mockReturnValue(false);

      const result = guard.canActivate(mockRoute, mockRouterState);

      expect(result).toBe(false);
      expect(userIdle.resetTimer).toHaveBeenCalled();
      expect(userIdle.stopTimer).toHaveBeenCalled();
      expect(userIdle.stopWatching).toHaveBeenCalled();
      expect(router.navigate).toHaveBeenCalledWith(['/login']);
    });

    it('should allow access if user is logged in and no project is selected', () => {
      (authService.loggeduserIn as jest.Mock).mockReturnValue(true);
      guard.ProjectId = -1;

      const result = guard.canActivate(mockRoute, mockRouterState);

      expect(result).toBe(true);
      expect(userIdle.startWatching).toHaveBeenCalled();
    });

    it('should redirect to profile if project is overdue', () => {
      (authService.loggeduserIn as jest.Mock).mockReturnValue(true);
      guard.ProjectId = 1;
      (projectService.getSingleProject as jest.Mock).mockReturnValue(of({
        data: { status: 'overdue' }
      }));

      const result = guard.canActivate(mockRoute, mockRouterState);

      expect(projectService.updatedSubStatus).toHaveBeenCalledWith({ status: true });
      expect(router.navigate).toHaveBeenCalledWith(['/profile']);
    });

    it('should allow access if project is not overdue', () => {
      (authService.loggeduserIn as jest.Mock).mockReturnValue(true);
      guard.ProjectId = 1;
      (projectService.getSingleProject as jest.Mock).mockReturnValue(of({
        data: { status: 'active' }
      }));

      const result = guard.canActivate(mockRoute, mockRouterState);

      expect(projectService.updatedSubStatus).toHaveBeenCalledWith({ status: false });
      expect(userIdle.startWatching).toHaveBeenCalled();
    });
  });

  describe('getProjectInfo', () => {
    it('should return true if project is overdue', () => {
      guard.ProjectId = 1;
      (projectService.getSingleProject as jest.Mock).mockReturnValue(of({
        data: { status: 'overdue' }
      }));

      const result = guard.getProjectInfo();

      expect(projectService.getSingleProject).toHaveBeenCalledWith({ ProjectId: 1 });
    });

    it('should return false if project is not overdue', () => {
      guard.ProjectId = 1;
      (projectService.getSingleProject as jest.Mock).mockReturnValue(of({
        data: { status: 'active' }
      }));

      const result = guard.getProjectInfo();

      expect(projectService.getSingleProject).toHaveBeenCalledWith({ ProjectId: 1 });
    });
  });
});
