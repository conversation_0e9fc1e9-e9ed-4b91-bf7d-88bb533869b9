import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-invite-link',
  templateUrl: './invite-link.component.html',
  })
export class InviteLinkComponent implements OnInit {
  public constructor(
    public router: Router,
  ) {}

  public ngOnInit(): void { /* */ }

  public submit() {
    const encodedUrl = localStorage.getItem('url');
    if (!encodedUrl) {
      this.router.navigate([null]);
      return;
    }
    this.router.navigate([window.atob(encodedUrl)]);
  }
}
