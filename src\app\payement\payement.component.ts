import {
  Component, OnInit, ViewChild, TemplateRef,
} from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import { AuthService } from '../services/auth/auth.service';
import { ProjectService } from '../services/profile/project.service';
import { MixpanelService } from '../services/mixpanel.service';

@Component({
  selector: 'app-payement',
  templateUrl: './payement.component.html',
  styleUrls: ['./payement.component.scss'],
  })
export class PayementComponent implements OnInit {
  public planList = [];

  public modalRef: BsModalRef;

  public currentPlan: any = {};

  public interval = '';

  public paymentCardDetailsForm: UntypedFormGroup;

  public submitted = false;

  public formSubmitted = false;


  public dt = new Date();

  public paymentCardyears = [];

  public paymentCardMonths: any[] = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'];

  public startyear = this.dt.getFullYear();

  public month = 1 + this.dt.getMonth();

  public loggedIn = false;

  public currentUser: any = {};

  public countryList: any = [];

  public cardMask = '0000-0000-0000-0000';

  public urlData = '';

  public upgradePlans = false;

  public upgradeProjectId: number;

  public newProjectCretedWithProjectPlan: any;

  @ViewChild('thanks') public successPopup: TemplateRef<any>;

  @ViewChild('projectsuccess') public projectSuccessPopup: TemplateRef<any>;

  @ViewChild('refresh') public refreshModal: TemplateRef<any>;


  public constructor(private readonly router: Router,
    public socket: Socket,
    private readonly modalService: BsModalService,
    private readonly toastr: ToastrService, private readonly authService: AuthService,
    private readonly projectService: ProjectService,
    private readonly mixpanelService: MixpanelService,
    private readonly formBuilder: UntypedFormBuilder) {
    this.loggedIn = this.authService.loggeduserIn();
  }


  public getAuthUser(): void {
    this.authService.getUser().subscribe((response: any): void => {
      this.currentUser = response;
    });
  }



  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-md thanks-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }


  public getPlans(interval): void {
    this.authService.getPlans(interval).subscribe({
      next: (planlist: any): void => {
        if (planlist) {
          this.planList = planlist.response;
          if (!this.upgradePlans) {
            const id = localStorage.getItem('planid');
            const index = this.planList.findIndex((item): boolean => item.id === +id);
            this.currentPlan = this.planList[index];
            this.interval = localStorage.getItem('interval');
            if (index === -1) {
              this.router.navigate(['/plans']);
            }
          } else {
            const id = localStorage.getItem('upgradePlanId');
            const index = this.planList.findIndex((item): boolean => item.id === +id);
            this.currentPlan = this.planList[index];
            this.interval = localStorage.getItem('upgradeInterval');
            if (index === -1) {
              this.router.navigate([`/upgradeplans/${this.upgradeProjectId}`]);
            }
          }
        }
      },
      error: (err): void => {
        this.toastr.error(err.message, 'OOPS!');
        this.router.navigate(['/home']);
      },
    });
  }

  public cardForm(): void {
    this.paymentCardDetailsForm = this.formBuilder.group(
      {
        number: [
          '',
          Validators.compose([
            // Validators.required,
          ]),
        ],
        name: [
          '',
          Validators.compose([
            // Validators.required,
          ]),
        ],
        zipCode: [
          '',
        ],
        country: [
          '',
        ],
        interval: [
          '',
        ],
        /* eslint-disable @typescript-eslint/camelcase */
        exp_month: [
          '',
          Validators.compose([
            // Validators.required,
          ]),
        ],
        exp_year: [
          '',
          Validators.compose([
            // Validators.required,
          ]),
        ],
        cvc: [
          '',
          Validators.compose([
            // Validators.required,
          ]),
        ],
      },
    );
    const urlValue = this.router.url.split('/');
    const data = urlValue[1];
    this.urlData = data;
    if (data === 'upgradepayment') {
      this.paymentCardDetailsForm.get('interval').setValue(localStorage.getItem('upgradeInterval'));
    } else {
      this.paymentCardDetailsForm.get('interval').setValue(localStorage.getItem('interval'));
    }
  }

  public selectPlan(interval): void {
    let newValue;
    if (interval === 'month') {
      newValue = 'monthly';
    } else {
      newValue = 'yearly';
    }
    const index = this.planList.findIndex((item): boolean => (
      item.Plan.planType.toLowerCase() === this.currentPlan.Plan.planType.toLowerCase()
            && item.stripePlanName === newValue));
    if (index !== -1) {
      this.currentPlan = this.planList[index];
    }
    if (!this.upgradePlans) {
      localStorage.setItem('planid', this.currentPlan.id);
      localStorage.setItem('interval', interval);
    } else {
      localStorage.setItem('upgradePlanId', this.currentPlan.id);
      localStorage.setItem('upgradeInterval', interval);
    }
  }

  public allowAlphaNumeric(event): boolean {
    const keyCode = (event.which) ? event.which : event.keyCode;
    const capsAlphabet = (keyCode >= 65 && keyCode <= 90);
    const smallAlphabet = (keyCode >= 97 && keyCode <= 128);
    if ((keyCode > 31 && (keyCode < 48 || keyCode > 57)) && !capsAlphabet && !smallAlphabet) {
      return false;
    }

    return true;
  }

  public reset(): void {
    this.paymentCardDetailsForm.reset();
    this.paymentCardDetailsForm.get('exp_year').setValue('');
    this.paymentCardDetailsForm.get('exp_month').setValue('');
    this.paymentCardDetailsForm.get('country').setValue('');
    this.paymentCardDetailsForm.get('interval').setValue(localStorage.getItem('interval'));
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    const month = this.paymentCardDetailsForm.value.exp_month;
    const year = this.paymentCardDetailsForm.value.exp_year;
    if (this.paymentCardDetailsForm.invalid) {
      this.formSubmitted = false;
      return;
    }
    if (!this.paymentCardDetailsForm.value.name.trim()) {
      this.toastr.error('Name should not be empty.');
      return;
    }
    if (+(month) < this.month && this.startyear === +(year)) {
      this.toastr.error('Card Expired');
      this.formSubmitted = false;
      this.submitted = false;
    } else if (!this.loggedIn) {
      this.openModal(this.refreshModal);
      const data = {
        basicDetails: JSON.parse(localStorage.getItem('basic')),
        companyDetails: JSON.parse(localStorage.getItem('company')),
        projectDetails: JSON.parse(localStorage.getItem('project')),
        planData: { id: this.currentPlan.id },
        cardDetails: this.paymentCardDetailsForm.value,
      };
      this.authService.register(data).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.createUserProfile(data);
            this.modalRef.hide();
            this.submitted = false;
            this.formSubmitted = false;
            localStorage.removeItem('basic');
            localStorage.removeItem('company');
            localStorage.removeItem('project');
            localStorage.removeItem('interval');
            localStorage.removeItem('planid');
            this.openModal(this.successPopup);
            this.router.navigate(['/login']);
          }
        },
        error: (err): void => {
          this.throwError(err);
        },
      });
    } else if (!this.upgradePlans) {
      this.createProject();
    } else {
      this.upgradeNewPlans();
    }
  }

  public upgradeNewPlans(): void {
    this.openModal(this.refreshModal);
    const data = {
      PlanId: this.currentPlan.id,
      existcard: false,
      cardDetails: this.paymentCardDetailsForm.value,
      ProjectId: this.upgradeProjectId,
    };
    this.projectService.upgradeProjectPlans(data).subscribe({
      next: (response: any): void => {
        if (response) {
          this.modalRef.hide();
          this.toastr.success(response.message, 'Success');
          this.submitted = false;
          this.formSubmitted = false;
          localStorage.removeItem('upgradePlanId');
          localStorage.removeItem('upgradeInterval');
          this.openModal(this.projectSuccessPopup);
          this.router.navigate(['/dashboard']);
        }
      },
      error: (upgradeNewPlansErr): void => {
        this.modalRef.hide();
        this.submitted = false;
        this.formSubmitted = false;
        if (upgradeNewPlansErr.message?.statusCode === 400) {
          this.showError(upgradeNewPlansErr);
          this.formSubmitted = false;
        } else if (!upgradeNewPlansErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(upgradeNewPlansErr.message, 'OOPS!');
        }
      },
    });
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.toastr.error(errorMessage);
  }

  public numberOnly(event): boolean {
    const charCode = (event.which) ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public alphaOnly(event): boolean {
    const key = event.keyCode;
    return ((key >= 65 && key <= 90) || (key >= 97 && key <= 128) || key === 8 || key === 32);
  }

  public createProject(): void {
    this.openModal(this.refreshModal);
    const projectData = JSON.parse(localStorage.getItem('newproject'));
    const payload = {
      firstName: this.currentUser.firstName,
      phoneNumber: this.currentUser.phoneNumber,
      email: this.currentUser.email,
      projectLocation: projectData.projectLocation,
      projectLocationLatitude: projectData.projectLocationLatitude,
      projectLocationLongitude: projectData.projectLocationLongitude,
      projectName: projectData.projectName,
      PlanId: this.currentPlan.id,
      stripeSubscription: this.newProjectCretedWithProjectPlan,
      // existcard: false,
      // cardDetails: this.paymentCardDetailsForm.value,
      ParentCompanyId: projectData.ParentCompanyId,
    };
    this.projectService.createProject(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.modalRef.hide();
          this.toastr.success(response.message, 'Success');
          this.submitted = false;
          this.formSubmitted = false;
          localStorage.removeItem('newproject');
          localStorage.removeItem('interval');
          localStorage.removeItem('planid');
          this.openModal(this.projectSuccessPopup);
          this.router.navigate(['/dashboard']);
        }
      },
      error: (err): void => {
        this.throwError(err);
      },
    });
  }

  public throwError(paymentError): void {
    this.modalRef.hide();
    if (paymentError.message?.statusCode === 400) {
      this.showError(paymentError);
      this.formSubmitted = false;
    } else if (!paymentError.message) {
      this.toastr.error('Try again later.!', 'Something went wrong.');
    } else {
      this.toastr.error(paymentError.message, 'OOPS!');
      this.submitted = false;
      this.formSubmitted = false;
    }
  }

  public getCountry(): void {
    this.authService.getCountry().subscribe((response: any): void => {
      if (response) {
        this.countryList = response.countryList;
      }
    });
  }

  public ngOnInit(): void {
    this.initializeComponent();
    for (let i = 0; i <= 20; i += 1) {
      this.paymentCardyears.push(this.startyear + i);
    }
    this.projectService.newProjectCreatedWithProjectPlan.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.newProjectCretedWithProjectPlan = res;
      }
    });
  }

  public initializeComponent(): void {
    this.loggedIn = this.authService.loggeduserIn();
    this.getCountry();

    if (!this.loggedIn) {
      this.getPlans('all');

      const companyDetails = localStorage.getItem('company');
      const basicDetails = localStorage.getItem('basic');
      const projectDetails = localStorage.getItem('project');

      const companyCondition = (companyDetails === undefined || companyDetails === null);
      const basicCondition = (basicDetails === undefined || basicDetails === null);
      const projectCondition = (projectDetails === undefined || projectDetails === null);

      if (basicCondition || companyCondition || projectCondition) {
        this.router.navigate(['/home']);
      }
    } else {
      this.getAuthUser();
      this.getPlans('all');

      const urlValue = this.router.url.split('/');
      const data = urlValue[1];
      this.urlData = data;

      if (data !== 'upgradepayment') {
        const projectDetails = localStorage.getItem('newproject');
        const projectCondition = (projectDetails === undefined || projectDetails === null);
        if (projectCondition) {
          this.router.navigate(['/plans']);
        }
      } else {
        this.upgradePlans = true;
        const projectId = urlValue[2];
        this.upgradeProjectId = +projectId;
        this.getPlans('all');
      }
    }

    this.cardForm();
  }

  public redirect(): void {
    if (this.upgradePlans) {
      this.router.navigate([`/upgradeplans/${this.upgradeProjectId}`]);
    } else {
      this.router.navigate(['/plans']);
    }
  }
}
