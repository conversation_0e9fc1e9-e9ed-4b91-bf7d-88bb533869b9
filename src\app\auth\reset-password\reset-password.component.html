<section class="auth-content">
  <div class="auth-overlay d-flex align-items-start w-100">
    <div class="container-fluid">
      <div class="top-logo">
        <img
          src="./assets/images/logo.svg"
          routerLink="/login"
          width="100"
          class="c-pointer auth-logo my-5 mx-5"
          alt="Follo"
        />
      </div>
      <div class="row align-items-center reset-container">
        <div class="col-md-7 col-lg-4 offset-md-5 offset-lg-6 px-lg-0">
          <div class="card-container">
            <div class="card-body p20">
              <div class="row">
                <div class="col-md-12 col-lg-11 m-auto">
                  <div class="mb-4">
                    <h1 class="auth-title color-grey7 fs22 fw-bold cairo-regular my-2">
                      Reset Password
                    </h1>
                    <p class="color-grey4 fs13 cairo-regular fw500">
                      Enter the email address associated with your account.
                    </p>
                  </div>
                  <form
                    name="form"
                    [formGroup]="resetpasswordForm"
                    (ngSubmit)="onSubmit()"
                    novalidate
                  >
                    <div class="mb-4">
                      <div class="input-group">
                        <span class="input-group-text"
                          ><img
                            src="./assets/images/password.svg"
                            width="12"
                            class="lock-icon"
                            alt="Password"
                        /></span>
                        <input
                          id="password"
                          [type]="PasswordOneType"
                          class="form-control"
                          placeholder="Password"
                          (keyup)="passwordValid($event)"
                          autocomplete="off"
                          formControlName="password"
                          [ngClass]="{ 'is-invalid': submitted &amp;&amp; resetpasswordForm.controls.password.errors }"
                        />
                        <span class="input-group-text w-41px c-pointer" (click)="togglePass(1)" (keydown)="handleToggleKeydown($event, 1)">
                          <em
                            class="fas color-grey4 fs12"
                            [ngClass]="{
                              'fa-eye': togglePasswordOne,
                              'fa-eye-slash': !togglePasswordOne
                            }"
                          ></em>
                        </span>
                      </div>
                      <div
                        class="color-red"
                        *ngIf="submitted && resetpasswordForm.get('password')?.errors"
                      >
                        <small *ngIf="resetpasswordForm.get('password')?.errors.required"
                          >*Password is Required</small
                        >
                      </div>
                      <div *ngIf="resetpasswordForm.get('password')?.value?.length > 0">
                        <div>
                          <label
                            class="col ps-0"
                            [ngClass]="{
                              'text-danger': resetpasswordForm.controls['password'].hasError('required') || resetpasswordForm.controls['password'].hasError('hasNumber'),
                              'text-success': !resetpasswordForm.controls['password'].hasError('required') && !resetpasswordForm.controls['password'].hasError('hasNumber')
                            }"
                            for="password"
                          >
                            <p class="d-flex mb-0">
                              <span>
                                <img
                                  alt="tick"
                                  [src]="
                                    resetpasswordForm.controls['password'].hasError('required') || resetpasswordForm.controls['password'].hasError('hasNumber')
                                      ? './assets/images/red-tick.svg'
                                      : './assets/images/green-tick.svg'
                                  "
                                />
                              </span>
                              <small class="d-block my-1 ms-3 responsive-font-validation">
                                At least 1 number
                              </small>
                            </p>
                          </label>
                        </div>

                        <div>
                          <label
                            for="password"
                            class="col ps-0"
                            [ngClass]="
                              resetpasswordForm.controls['password'].hasError('required') ||
                              resetpasswordForm.controls['password'].hasError('minlength')
                                ? 'text-danger'
                                : 'text-success'
                            "
                          >
                            <p class="d-flex mb-0">
                              <span
                                ><img
                                  alt="tick"
                                  [src]="
                                    resetpasswordForm.controls['password'].hasError('required') ||
                                    resetpasswordForm.controls['password'].hasError('minlength')
                                      ? './assets/images/red-tick.svg'
                                      : './assets/images/green-tick.svg'
                                  "
                              /></span>
                              <small class="d-block my-1 ms-3 responsive-font-validation"
                                >Password must contain at least 8 characters</small
                              >
                            </p>
                          </label>
                        </div>
                        <div>
                          <label
                            for="password"
                            class="col ps-0"
                            [ngClass]="
                              resetpasswordForm.controls['password'].hasError('required') ||
                              resetpasswordForm.controls['password'].hasError('hasCapitalCase')
                                ? 'text-danger'
                                : 'text-success'
                            "
                          >
                            <p class="d-flex mb-0">
                              <span
                                ><img
                                  alt="tick"
                                  [src]="
                                    resetpasswordForm.controls['password'].hasError('required') ||
                                    resetpasswordForm.controls['password'].hasError(
                                      'hasCapitalCase'
                                    )
                                      ? './assets/images/red-tick.svg'
                                      : './assets/images/green-tick.svg'
                                  "
                              /></span>
                              <small class="d-block my-1 ms-3 responsive-font-validation"
                                >At least 1 uppercase letter</small
                              >
                            </p>
                          </label>
                        </div>
                        <div>
                          <label
                            for="password"
                            class="col ps-0"
                            [ngClass]="
                              resetpasswordForm.controls['password'].hasError('required') ||
                              resetpasswordForm.controls['password'].hasError('hasSmallCase')
                                ? 'text-danger'
                                : 'text-success'
                            "
                          >
                            <p class="d-flex mb-0">
                              <span
                                ><img
                                  alt="tick"
                                  [src]="
                                    resetpasswordForm.controls['password'].hasError('required') ||
                                    resetpasswordForm.controls['password'].hasError('hasSmallCase')
                                      ? './assets/images/red-tick.svg'
                                      : './assets/images/green-tick.svg'
                                  "
                              /></span>
                              <small class="d-block my-1 ms-3 responsive-font-validation"
                                >At least 1 lowercase letter</small
                              >
                            </p>
                          </label>
                        </div>
                        <div>
                          <label
                            for="password"
                            class="col ps-0"
                            [ngClass]="
                              resetpasswordForm.controls['password'].hasError('required') ||
                              resetpasswordForm.controls['password'].hasError(
                                'hasSpecialCharacters'
                              )
                                ? 'text-danger'
                                : 'text-success'
                            "
                          >
                            <p class="d-flex mb-0">
                              <span
                                ><img
                                  alt="tick"
                                  [src]="
                                    resetpasswordForm.controls['password'].hasError('required') ||
                                    resetpasswordForm.controls['password'].hasError(
                                      'hasSpecialCharacters'
                                    )
                                      ? './assets/images/red-tick.svg'
                                      : './assets/images/green-tick.svg'
                                  "
                              /></span>
                              <small class="d-block my-1 ms-3 responsive-font-validation"
                                >At least 1 special character(!#_$%*)</small
                              >
                            </p>
                          </label>
                        </div>
                      </div>
                    </div>
                    <div class="mb-4">
                      <div class="input-group">
                        <span class="input-group-text"
                          ><img src="./assets/images/password.svg" alt="Password"
                        /></span>
                        <input
                          [type]="PasswordTwoType"
                          class="form-control"
                          placeholder="Confirm Password"
                          autocomplete="off"
                          formControlName="confirmPassword"
                          [ngClass]="{ 'is-invalid': submitted &amp;&amp; resetpasswordForm.controls.confirmPassword.errors }"
                        />
                        <span class="input-group-text w-41px c-pointer" (click)="togglePass(2)" (keydown)="handleToggleKeydown($event, 2)">
                          <em
                            class="fas color-grey4 fs12"
                            [ngClass]="{
                              'fa-eye': togglePasswordTwo,
                              'fa-eye-slash': !togglePasswordTwo
                            }"
                          ></em>
                        </span>
                      </div>
                      <div
                        *ngIf="submitted &amp;&amp; resetpasswordForm.controls.confirmPassword.errors"
                        class="text-danger fs12"
                      >
                        <div *ngIf="resetpasswordForm.controls.confirmPassword.errors.required">
                          Confirm Password is required
                        </div>
                        <div *ngIf="resetpasswordForm.controls.confirmPassword.errors.mustMatch">
                          Passwords must match
                        </div>
                      </div>
                    </div>
                    <button
                      type="submit"
                      class="btn btn-orange color-orange col-6 col-md-4 radius20 fs13 fw-bold cairo-regular w-100"
                      [disabled]="formSubmitted && resetpasswordForm.valid"
                    >
                      <em
                        class="fa fa-spinner"
                        aria-hidden="true"
                        *ngIf="formSubmitted && resetpasswordForm.valid"
                      ></em
                      >Submit
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
