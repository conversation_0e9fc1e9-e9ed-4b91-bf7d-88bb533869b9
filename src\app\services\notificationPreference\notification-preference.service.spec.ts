import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NotificationPreferenceService } from './notification-preference.service';
import { ApiService } from '../api_base/api.service';
import { of, throwError } from 'rxjs';

describe('NotificationPreferenceService', (): void => {
  let service: NotificationPreferenceService;
  let apiServiceMock: jest.Mocked<ApiService>;

  beforeEach((): void => {
    // Create mock for ApiService
    apiServiceMock = {
      get: jest.fn(),
      postMethod: jest.fn()
    } as unknown as jest.Mocked<ApiService>;

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        NotificationPreferenceService,
        { provide: ApiService, useValue: apiServiceMock }
      ]
    });

    service = TestBed.inject(NotificationPreferenceService);
  });

  it('should be created', (): void => {
    expect(service).toBeTruthy();
  });

  describe('getNotificationList', (): void => {
    it('should call get with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        MemberId: '123',
        ProjectId: '456',
        ParentCompanyId: '789'
      };

      const mockResponse = {
        data: [{
          inAppNotification: [
            {
              instant: true,
              dailyDigest: false,
              NotificationPreferenceItem: {
                id: 1,
                description: 'Test Notification',
              },
            },
          ],
          emailNotification: [
            {
              instant: true,
              dailyDigest: false,
              NotificationPreferenceItem: {
                id: 2,
                description: 'Test Email',
              },
            },
          ],
          digestTiming: {
            time: '05:00',
            timeFormat: 'AM',
            TimeZoneId: '1',
          },
        }]
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getNotificationList(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        'notification_preference/get_notification_preference_list',
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty payload', (): void => {
      // Arrange
      const mockPayload = {};
      const mockResponse = { data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getNotificationList(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        'notification_preference/get_notification_preference_list',
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('setNotificationPreference', (): void => {
    it('should call postMethod with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        MemberId: '123',
        ProjectId: '456'
      };

      const mockPayload = {
        inAppNotification: [
          {
            id: 1,
            instant: true,
            dailyDigest: false
          }
        ],
        emailNotification: [
          {
            id: 2,
            instant: false,
            dailyDigest: true
          }
        ],
        digestTiming: {
          time: '05:00',
          timeFormat: 'AM',
          TimeZoneId: '1'
        }
      };

      const mockResponse = {
        success: true,
        message: 'Notification preferences updated successfully'
      };

      apiServiceMock.postMethod.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.setNotificationPreference(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.postMethod).toHaveBeenCalledWith(
        'notification_preference/set_notification_preference',
        mockPayload,
        mockParams
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty params and payload', (): void => {
      // Arrange
      const mockParams = {};
      const mockPayload = {};
      const mockResponse = { success: false, message: 'Invalid data' };

      apiServiceMock.postMethod.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.setNotificationPreference(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.postMethod).toHaveBeenCalledWith(
        'notification_preference/set_notification_preference',
        mockPayload,
        mockParams
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Error handling', (): void => {
    it('should propagate errors from the API service in getNotificationList', (done): void => {
      // Arrange
      const mockPayload = { MemberId: '123' };
      const mockError = 'API Error';

      apiServiceMock.get.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.getNotificationList(mockPayload).subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(mockError);
          done();
        }
      });
    });

    it('should propagate errors from the API service in setNotificationPreference', (done): void => {
      // Arrange
      const mockParams = { MemberId: '123' };
      const mockPayload = { inAppNotification: [] };
      const mockError = 'API Error';

      apiServiceMock.postMethod.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.setNotificationPreference(mockParams, mockPayload).subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(mockError);
          done();
        }
      });
    });
  });

  describe('Service dependencies', (): void => {
    it('should have ApiService injected', (): void => {
      // This test verifies that the ApiService is properly injected
      expect(TestBed.inject(ApiService)).toBeDefined();
    });
  });

  describe('Integration with NotificationSettingsComponent', (): void => {
    it('should provide data in the format expected by NotificationSettingsComponent', (): void => {
      // Arrange
      const mockPayload = {
        MemberId: '789',
        ProjectId: '123',
        ParentCompanyId: '456'
      };

      const mockResponse = {
        data: [{
          inAppNotification: [
            {
              instant: true,
              dailyDigest: false,
              NotificationPreferenceItem: {
                id: 1,
                description: 'Test Notification',
              },
            },
          ],
          emailNotification: [
            {
              instant: true,
              dailyDigest: false,
              NotificationPreferenceItem: {
                id: 2,
                description: 'Test Email',
              },
            },
          ],
          digestTiming: {
            time: '05:00',
            timeFormat: 'AM',
            TimeZoneId: '1',
          },
        }]
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getNotificationList(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert - verify the structure matches what's expected in the component
      expect(result.data[0]).toHaveProperty('inAppNotification');
      expect(result.data[0]).toHaveProperty('emailNotification');
      expect(result.data[0]).toHaveProperty('digestTiming');
      expect(result.data[0].inAppNotification[0]).toHaveProperty('NotificationPreferenceItem');
      expect(result.data[0].emailNotification[0]).toHaveProperty('NotificationPreferenceItem');
      expect(result.data[0].digestTiming).toHaveProperty('time');
      expect(result.data[0].digestTiming).toHaveProperty('timeFormat');
      expect(result.data[0].digestTiming).toHaveProperty('TimeZoneId');
    });
  });
});
