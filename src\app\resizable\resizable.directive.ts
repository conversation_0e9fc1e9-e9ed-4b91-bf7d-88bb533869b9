import { DOCUMENT } from '@angular/common';
import {
  Directive, ElementRef, Inject, Output,
} from '@angular/core';
import {
  distinctUntilChanged,
  map,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs/operators';
import { fromEvent } from 'rxjs';

@Directive({
  selector: '[resizable]',
  })
export class ResizableDirective {
  @Output()
  public readonly resizable = fromEvent<MouseEvent>(
    this.elementRef.nativeElement,
    'mousedown',
  ).pipe(
    tap((e): any => e.preventDefault()),
    switchMap((): any => {
      const { width, right } = this.elementRef.nativeElement
        .closest('th')
        .getBoundingClientRect();

      return fromEvent<MouseEvent>(this.documentRef, 'mousemove').pipe(
        map(({ clientX }): any => width + clientX - right),
        distinctUntilChanged(),
        takeUntil(fromEvent(this.documentRef, 'mouseup')),
      );
    }),
  );

  public constructor(
    @Inject(DOCUMENT) private readonly documentRef: Document,
    @Inject(ElementRef)
    private readonly elementRef: ElementRef<HTMLElement>,
  // eslint-disable-next-line no-empty-function
  ) {}
}
