import { ToastrService } from 'ngx-toastr';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';

import { Component, OnInit, TemplateRef } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';

import { ProjectService } from '../services/profile/project.service';
import { BillingService } from '../services/dashboard/billing.service';
import { AuthService } from '../services/auth/auth.service';

@Component({
  selector: 'app-billing',
  templateUrl: './billing.component.html',
  })
export class BillingComponent implements OnInit {
  public modalRef: BsModalRef;

  public currentPageNo = 1;

  public userData: any = [];

  public ParentCompanyId;

  public loader = true;

  public pageSize = 25;

  public billingData: any = {};

  public totalCount = 0;

  public nextPayDet: any = {};

  public submitted = false;

  public payOfflineChosen = false;

  public payOnlineChosen = false;

  public countryList = [];

  public billingCardDetailsForm: UntypedFormGroup;

  public formSubmitted = false;

  public dt = new Date();

  public billingCardMonths: any[] = [
    '01',
    '02',
    '03',
    '04',
    '05',
    '06',
    '07',
    '08',
    '09',
    '10',
    '11',
    '12',
  ];

  public startyear = this.dt.getFullYear();

  public month = 1 + this.dt.getMonth();

  public billingCardYears = [];

  public cardMask = '0000-0000-0000-0000';

  public constructor(
    private readonly modalService: BsModalService,
    private readonly projectService: ProjectService,
    public billingService: BillingService,
    public toastr: ToastrService,
    private readonly authService: AuthService,
    private readonly router: Router,
    private readonly formBuilder: UntypedFormBuilder,
  ) {
    this.projectService.ParentCompanyId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ParentCompanyId = res;
        this.getBilling();
      }
    });
    this.getCountry();
    this.cardForm();
  }

  public ngOnInit(): void {
    for (let i = 0; i <= 20; i += 1) {
      this.billingCardYears.push(this.startyear + i);
    }
  }

  public handleToggleKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.reset();
    }
  }

  public getCountry(): void {
    this.authService.getCountry().subscribe((response: any): void => {
      if (response) {
        this.countryList = response.countryList;
      }
    });
  }

  public alphaNum(event): boolean {
    const key = event.which ? event.which : event.keyCode;
    const firstCondition = key >= 65 && key <= 90;
    const secondCondition = key >= 97 && key <= 128;
    if (key > 31 && (key < 48 || key > 57) && !firstCondition && !secondCondition) {
      return false;
    }

    return true;
  }

  public getBilling(): void {
    const param = {
      pageSize: this.pageSize,
      pageNo: this.currentPageNo,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.billingService.getBilling(param).subscribe((response: any): void => {
      if (response) {
        this.billingData = response.data.rows;
        this.totalCount = response.data.count;
        this.nextPayDet = response.data.nextPayDet;
      }
    });
  }

  public payOffline(): void {
    this.submitted = true;
    this.payOfflineChosen = true;
    const params = {
      ParentCompanyId: this.ParentCompanyId,
    };
    const payload = { amount: this.nextPayDet.amount };
    this.billingService.payOffline(params, payload).subscribe({
      next: (response: any): void => {
        if (this.modalRef) {
          this.modalRef.hide();
        }
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.getBilling();
          this.submitted = false;
          this.payOfflineChosen = false;
        }
      },
      error: (payOfflineError): void => {
        this.submitted = false;
        this.payOfflineChosen = false;
        if (payOfflineError.message?.statusCode === 400) {
          this.showError(payOfflineError);
        } else if (!payOfflineError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else if (payOfflineError.data !== undefined) {
          this.toastr.error(payOfflineError.message, 'OOPS!');
        } else {
          this.toastr.error(payOfflineError.message, 'OOPS!');
        }
      },
    });
  }

  public payOnline(): void {
    this.submitted = true;
    this.payOnlineChosen = true;
  }

  public alphaOnly(event): boolean {
    const key = event.keyCode;
    return (key >= 65 && key <= 90) || (key >= 97 && key <= 128) || key === 8;
  }

  public reset(): void {
    this.modalRef.hide();
    this.billingCardDetailsForm.reset();
    this.billingCardDetailsForm.get('exp_year').setValue('');
    this.billingCardDetailsForm.get('exp_month').setValue('');
    this.billingCardDetailsForm.get('country').setValue('');
  }

  public numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public downloadReceipt(url): void { /* */ }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    const month = this.billingCardDetailsForm.value.exp_month;
    const year = this.billingCardDetailsForm.value.exp_year;
    if (this.billingCardDetailsForm.invalid) {
      this.formSubmitted = false;
      return;
    }
    if (+month < this.month && this.startyear === +year) {
      this.toastr.error('Card Expired');
      this.formSubmitted = false;
      this.submitted = false;
    }
    const params = {
      ParentCompanyId: this.ParentCompanyId,
    };
    const payload = {
      amount: this.nextPayDet.amount,
      cardDetails: this.billingCardDetailsForm.value,
    };
    this.billingService.payOnline(params, payload).subscribe({
      next: (response: any): void => {
        if (this.modalRef) {
          this.modalRef.hide();
        }
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.getBilling();
          this.submitted = false;
          this.payOnlineChosen = false;
        }
      },
      error: (payOnlineError): void => {
        this.submitted = false;
        this.payOnlineChosen = false;
        if (payOnlineError.message?.statusCode === 400) {
          this.showError(payOnlineError);
        } else if (!payOnlineError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else if (payOnlineError.data !== undefined) {
          this.toastr.error(payOnlineError.message, 'OOPS!');
        } else {
          this.toastr.error(payOnlineError.message, 'OOPS!');
        }
      },
    });
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.toastr.error(errorMessage);
  }

  public changePageSize(pageSize): void {
    this.pageSize = pageSize;
    this.getBilling();
  }

  public changePageNo(pageNo): void {
    this.currentPageNo = pageNo;
    this.getBilling();
  }

  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-sm billing-modal  custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }

  public openCardModal(template: TemplateRef<any>): void {
    this.modalRef.hide();
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-lg' };
    this.modalRef = this.modalService.show(template, data);
  }

  public cardForm(): void {
    this.billingCardDetailsForm = this.formBuilder.group({
      zipCode: [''],
      interval: [''],
      country: [''],
      name: ['', Validators.compose([Validators.required])],
      number: ['', Validators.compose([Validators.required])],
      /* eslint-disable @typescript-eslint/camelcase */
      exp_month: ['', Validators.compose([Validators.required])],
      cvc: ['', Validators.compose([Validators.required])],
      exp_year: ['', Validators.compose([Validators.required])],
    });
  }
}
