import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { of, throwError } from 'rxjs';
import { AccordionModule } from 'ngx-bootstrap/accordion';
import { ProjectService } from '../services/profile/project.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { NotificationPreferenceService } from '../services/notificationPreference/notification-preference.service';
import { NotificationSettingsComponent } from './notification-settings.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('NotificationSettingsComponent', () => {
  let component: NotificationSettingsComponent;
  let fixture: ComponentFixture<NotificationSettingsComponent>;
  let notificationService: jest.Mocked<NotificationPreferenceService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let toastrService: jest.Mocked<ToastrService>;
  let projectService: jest.Mocked<ProjectService>;

  const mockNotificationList = {
    data: [{
      inAppNotification: [
        {
          instant: true,
          dailyDigest: false,
          NotificationPreferenceItem: {
            id: 1,
            description: 'Test Notification',
          },
        },
      ],
      emailNotification: [
        {
          instant: true,
          dailyDigest: false,
          NotificationPreferenceItem: {
            id: 2,
            description: 'Test Email',
          },
        },
      ],
      digestTiming: {
        time: '05:00',
        timeFormat: 'AM',
        TimeZoneId: '1',
      },
    }],
  };

  const mockTimeZoneList = {
    data: [
      { id: '1', location: 'UTC', timeZoneOffsetInMinutes: 0 },
    ]
  };

  beforeEach(async () => {
    const notificationServiceMock = {
      getNotificationList: jest.fn().mockReturnValue(of(mockNotificationList)),
      setNotificationPreference: jest.fn().mockReturnValue(of({ message: 'Success' }))
    };
    const deliveryServiceMock = {
      getMemberRole: jest.fn().mockReturnValue(of({ data: { id: '789' } }))
    };
    const toastrServiceMock = {
      success: jest.fn(),
      error: jest.fn()
    };
    const projectServiceMock = {
      projectParent: of({ ProjectId: '123', ParentCompanyId: '456' }),
      getTimeZoneList: jest.fn().mockReturnValue(of(mockTimeZoneList))
    };

    await TestBed.configureTestingModule({
      declarations: [NotificationSettingsComponent],
      imports: [
        ReactiveFormsModule,
        AccordionModule.forRoot(),
        NoopAnimationsModule
      ],
      providers: [
        FormBuilder,
        Title,
        { provide: NotificationPreferenceService, useValue: notificationServiceMock },
        { provide: DeliveryService, useValue: deliveryServiceMock },
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: ProjectService, useValue: projectServiceMock },
      ],
    }).compileComponents();

    notificationService = TestBed.inject(NotificationPreferenceService) as jest.Mocked<NotificationPreferenceService>;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
  });

  beforeEach(fakeAsync(() => {
    fixture = TestBed.createComponent(NotificationSettingsComponent);
    component = fixture.componentInstance;
    
    // Initialize the component
    fixture.detectChanges();
    
    // Wait for all async operations to complete
    tick();
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', fakeAsync(() => {
    // Reset component state
    component.loader = true;
    component.submitted = false;
    component.oneAtATime = true;
    component.isFirstOpen = true;
    
    fixture.detectChanges();
    tick();
    
    expect(component.loader).toBe(true);
    expect(component.submitted).toBe(false);
    expect(component.oneAtATime).toBe(true);
    expect(component.isFirstOpen).toBe(true);
  }));

  it('should get notification list on initialization', fakeAsync(() => {
    expect(deliveryService.getMemberRole).toHaveBeenCalledWith({
      ProjectId: '123',
      ParentCompanyId: '456',
    });
    expect(projectService.getTimeZoneList).toHaveBeenCalled();
    expect(notificationService.getNotificationList).toHaveBeenCalledWith({
      MemberId: '789',
      ProjectId: '123',
      ParentCompanyId: '456',
    });
    expect(component.notificationList).toBeDefined();
    expect(component.loader).toBe(false);
    tick();
  }));

  it('should handle notification list error', fakeAsync(() => {
    const error = { message: { statusCode: 400, details: [{ error: 'Test Error' }] } };
    notificationService.getNotificationList.mockReturnValue(throwError(() => error));

    // Reset component state
    component.notificationList = undefined;
    component.loader = true;

    // Trigger the error
    component.getNotificationList();
    fixture.detectChanges();
    tick();

    expect(toastrService.error).toHaveBeenCalled();
  }));

  it('should update instant notification value', fakeAsync(() => {
    // Ensure notificationList is initialized
    component.notificationList = mockNotificationList.data[0];
    
    const mockEvent = { target: { checked: true } };
    const mockData = { instant: false, dailyDigest: false };

    component.checkInstantValue(mockData, mockEvent, 'inApp');
    expect(mockData.instant).toBe(true);
    expect(mockData.dailyDigest).toBe(false);
    tick();
  }));

  it('should update daily digest value', fakeAsync(() => {
    // Ensure notificationList is initialized
    component.notificationList = mockNotificationList.data[0];
    
    const mockEvent = { target: { checked: true } };
    const mockData = { instant: true, dailyDigest: false };

    component.checkDailyDigestValue(mockData, mockEvent, 'inApp');
    expect(mockData.dailyDigest).toBe(true);
    expect(mockData.instant).toBe(false);
    tick();
  }));

  it('should submit notification preferences successfully', fakeAsync(() => {
    // Ensure notificationList is initialized
    component.notificationList = mockNotificationList.data[0];
    component.MemberId = '789';
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.notificationForm = new FormBuilder().group({
      time: ['05:00'],
      timeFormat: ['AM'],
      TimeZoneId: ['1']
    });

    const mockResponse = { message: 'Success' };
    notificationService.setNotificationPreference.mockReturnValue(of(mockResponse));

    component.onSubmit();
    fixture.detectChanges();
    tick();

    expect(component.submitted).toBe(false); // Should be reset to false after success
    expect(notificationService.setNotificationPreference).toHaveBeenCalledWith(
      {
        MemberId: '789',
        ProjectId: '123',
        ParentCompanyId: '456'
      },
      {
        inAppNotification: mockNotificationList.data[0].inAppNotification,
        emailNotification: mockNotificationList.data[0].emailNotification,
        dailyDigestTiming: component.notificationForm.value
      }
    );
    expect(toastrService.success).toHaveBeenCalledWith('Success');
    expect(notificationService.getNotificationList).toHaveBeenCalled(); // Should refresh list
  }));

  it('should handle submit error', fakeAsync(() => {
    // Ensure notificationList is initialized
    component.notificationList = mockNotificationList.data[0];
    component.notificationForm = new FormBuilder().group({
      time: ['05:00'],
      timeFormat: ['AM'],
      TimeZoneId: ['1']
    });
    
    const error = { message: { statusCode: 400, details: [{ error: 'Test Error' }] } };
    notificationService.setNotificationPreference.mockReturnValue(throwError(() => error));

    component.onSubmit();
    fixture.detectChanges();
    tick();

    expect(component.submitted).toBe(false);
    expect(toastrService.error).toHaveBeenCalled();
  }));

  it('should toggle notification sections', () => {
    component.inAppNotification(true);
    expect(component.isAppNotificationExpanded).toBe(true);

    component.emailNotification(true);
    expect(component.isEmailNotificationExpanded).toBe(true);
  });

  it('should initialize notification form with validators', () => {
    expect(component.notificationForm.get('time')?.validator).toBeTruthy();
    expect(component.notificationForm.get('timeFormat')?.validator).toBeTruthy();
    expect(component.notificationForm.get('TimeZoneId')?.validator).toBeTruthy();
  });

  // Additional test cases for better coverage

  it('should handle getTimeZoneList success', fakeAsync(() => {
    const mockTimeZoneResponse = { data: mockTimeZoneList.data };
    projectService.getTimeZoneList.mockReturnValue(of(mockTimeZoneResponse));

    component.getTimeZoneList();
    tick();

    expect(projectService.getTimeZoneList).toHaveBeenCalled();
    expect(component.timezoneList).toEqual(mockTimeZoneList.data);
  }));

  it('should handle getTimeZoneList error with status 400', fakeAsync(() => {
    const error = { message: { statusCode: 400, details: [{ error: 'Timezone Error' }] } };
    projectService.getTimeZoneList.mockReturnValue(throwError(() => error));

    const showErrorSpy = jest.spyOn(component, 'showError');
    component.getTimeZoneList();
    tick();

    expect(showErrorSpy).toHaveBeenCalledWith(error);
  }));

  it('should handle getTimeZoneList error without message', fakeAsync(() => {
    const error = {};
    projectService.getTimeZoneList.mockReturnValue(throwError(() => error));

    component.getTimeZoneList();
    tick();

    expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  }));

  it('should handle getTimeZoneList generic error', fakeAsync(() => {
    const error = { message: 'Generic error' };
    projectService.getTimeZoneList.mockReturnValue(throwError(() => error));

    component.getTimeZoneList();
    tick();

    expect(toastrService.error).toHaveBeenCalledWith('Generic error', 'OOPS!');
  }));

  it('should handle getNotificationListError', () => {
    const error = { message: { details: [{ error: 'Test Error' }] } };

    component.getNotificationListError(error);

    expect(toastrService.error).toHaveBeenCalledWith('Test Error');
  });

  it('should handle showError method', () => {
    const error = { message: { details: [{ error: 'Test Error' }] } };
    component.submitted = true;

    component.showError(error);

    expect(component.submitted).toBe(false);
    expect(toastrService.error).toHaveBeenCalledWith('Test Error');
  });

  it('should handle submit error without message', fakeAsync(() => {
    component.notificationList = mockNotificationList.data[0];
    component.MemberId = '789';
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.notificationForm = new FormBuilder().group({
      time: ['05:00'],
      timeFormat: ['AM'],
      TimeZoneId: ['1']
    });

    const error = {};
    notificationService.setNotificationPreference.mockReturnValue(throwError(() => error));

    component.onSubmit();
    tick();

    expect(component.submitted).toBe(false);
    expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  }));

  it('should handle submit generic error', fakeAsync(() => {
    component.notificationList = mockNotificationList.data[0];
    component.MemberId = '789';
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.notificationForm = new FormBuilder().group({
      time: ['05:00'],
      timeFormat: ['AM'],
      TimeZoneId: ['1']
    });

    const error = { message: 'Generic submit error' };
    notificationService.setNotificationPreference.mockReturnValue(throwError(() => error));

    component.onSubmit();
    tick();

    expect(component.submitted).toBe(false);
    expect(toastrService.error).toHaveBeenCalledWith('Generic submit error', 'OOPS!');
  }));

  // Test notification state management methods

  it('should calculate email notification values - all instant selected', () => {
    const mockEmailNotifications = [
      { instant: true, dailyDigest: false },
      { instant: true, dailyDigest: false }
    ];
    component.notificationList = { emailNotification: mockEmailNotifications };

    component.getEmailNotificationValue();

    expect(component.isAllEmailInstantSelected).toBe(2);
    expect(component.isAllEmailDailyDigestSelected).toBe(0);
  });

  it('should calculate email notification values - all daily digest selected', () => {
    const mockEmailNotifications = [
      { instant: false, dailyDigest: true },
      { instant: false, dailyDigest: true }
    ];
    component.notificationList = { emailNotification: mockEmailNotifications };

    component.getEmailNotificationValue();

    expect(component.isAllEmailDailyDigestSelected).toBe(2);
    expect(component.isAllEmailInstantSelected).toBe(0);
  });

  it('should calculate email notification values - partial instant selected', () => {
    const mockEmailNotifications = [
      { instant: true, dailyDigest: false },
      { instant: false, dailyDigest: false }
    ];
    component.notificationList = { emailNotification: mockEmailNotifications };

    component.getEmailNotificationValue();

    expect(component.isAllEmailInstantSelected).toBe(1);
    expect(component.isAllEmailDailyDigestSelected).toBe(0);
  });

  it('should calculate email notification values - partial daily digest selected', () => {
    const mockEmailNotifications = [
      { instant: false, dailyDigest: true },
      { instant: false, dailyDigest: false }
    ];
    component.notificationList = { emailNotification: mockEmailNotifications };

    component.getEmailNotificationValue();

    expect(component.isAllEmailDailyDigestSelected).toBe(1);
    expect(component.isAllEmailInstantSelected).toBe(0);
  });

  it('should calculate email notification values - none selected', () => {
    const mockEmailNotifications = [
      { instant: false, dailyDigest: false },
      { instant: false, dailyDigest: false }
    ];
    component.notificationList = { emailNotification: mockEmailNotifications };

    component.getEmailNotificationValue();

    expect(component.isAllEmailInstantSelected).toBe(0);
    expect(component.isAllEmailDailyDigestSelected).toBe(0);
  });

  it('should calculate in-app notification values - all instant selected', () => {
    const mockInAppNotifications = [
      { instant: true, dailyDigest: false },
      { instant: true, dailyDigest: false }
    ];
    component.notificationList = { inAppNotification: mockInAppNotifications };

    component.getAppNotificationValue();

    expect(component.isAllInAppInstantSelected).toBe(2);
    expect(component.isAllInAppDailyDigestSelected).toBe(0);
  });

  it('should calculate in-app notification values - all daily digest selected', () => {
    const mockInAppNotifications = [
      { instant: false, dailyDigest: true },
      { instant: false, dailyDigest: true }
    ];
    component.notificationList = { inAppNotification: mockInAppNotifications };

    component.getAppNotificationValue();

    expect(component.isAllInAppDailyDigestSelected).toBe(2);
    expect(component.isAllInAppInstantSelected).toBe(0);
  });

  it('should calculate in-app notification values - partial instant selected', () => {
    const mockInAppNotifications = [
      { instant: true, dailyDigest: false },
      { instant: false, dailyDigest: false }
    ];
    component.notificationList = { inAppNotification: mockInAppNotifications };

    component.getAppNotificationValue();

    expect(component.isAllInAppInstantSelected).toBe(1);
    expect(component.isAllInAppDailyDigestSelected).toBe(0);
  });

  it('should calculate in-app notification values - partial daily digest selected', () => {
    const mockInAppNotifications = [
      { instant: false, dailyDigest: true },
      { instant: false, dailyDigest: false }
    ];
    component.notificationList = { inAppNotification: mockInAppNotifications };

    component.getAppNotificationValue();

    expect(component.isAllInAppDailyDigestSelected).toBe(1);
    expect(component.isAllInAppInstantSelected).toBe(0);
  });

  it('should calculate in-app notification values - none selected', () => {
    const mockInAppNotifications = [
      { instant: false, dailyDigest: false },
      { instant: false, dailyDigest: false }
    ];
    component.notificationList = { inAppNotification: mockInAppNotifications };

    component.getAppNotificationValue();

    expect(component.isAllInAppInstantSelected).toBe(0);
    expect(component.isAllInAppDailyDigestSelected).toBe(0);
  });

  it('should check whole state for inApp type', () => {
    const mockInAppNotifications = [{ instant: true, dailyDigest: false }];
    component.notificationList = { inAppNotification: mockInAppNotifications };
    const getAppNotificationValueSpy = jest.spyOn(component, 'getAppNotificationValue');

    component.checkWholeState('inApp');

    expect(getAppNotificationValueSpy).toHaveBeenCalled();
  });

  it('should check whole state for email type', () => {
    const mockEmailNotifications = [{ instant: true, dailyDigest: false }];
    component.notificationList = { emailNotification: mockEmailNotifications };
    const getEmailNotificationValueSpy = jest.spyOn(component, 'getEmailNotificationValue');

    component.checkWholeState('email');

    expect(getEmailNotificationValueSpy).toHaveBeenCalled();
  });

  it('should check whole state for true (both types)', () => {
    const mockNotifications = {
      inAppNotification: [{ instant: true, dailyDigest: false }],
      emailNotification: [{ instant: true, dailyDigest: false }]
    };
    component.notificationList = mockNotifications;
    const getAppNotificationValueSpy = jest.spyOn(component, 'getAppNotificationValue');
    const getEmailNotificationValueSpy = jest.spyOn(component, 'getEmailNotificationValue');

    component.checkWholeState(true);

    expect(getAppNotificationValueSpy).toHaveBeenCalled();
    expect(getEmailNotificationValueSpy).toHaveBeenCalled();
  });

  // Test checkbox interaction edge cases

  it('should handle unchecking instant notification', fakeAsync(() => {
    component.notificationList = mockNotificationList.data[0];
    const mockEvent = { target: { checked: false } };
    const mockData = { instant: true, dailyDigest: false };

    component.checkInstantValue(mockData, mockEvent, 'inApp');

    expect(mockData.instant).toBe(false);
    expect(mockData.dailyDigest).toBe(false);
    tick();
  }));

  it('should handle unchecking daily digest notification', fakeAsync(() => {
    component.notificationList = mockNotificationList.data[0];
    const mockEvent = { target: { checked: false } };
    const mockData = { instant: false, dailyDigest: true };

    component.checkDailyDigestValue(mockData, mockEvent, 'email');

    expect(mockData.instant).toBe(false);
    expect(mockData.dailyDigest).toBe(false);
    tick();
  }));

  // Test notification list initialization edge cases

  it('should handle notification list without digest timing', fakeAsync(() => {
    const mockNotificationWithoutTiming = {
      data: [{
        inAppNotification: [],
        emailNotification: [],
        digestTiming: null
      }]
    };

    component.timezoneList = mockTimeZoneList.data;
    notificationService.getNotificationList.mockReturnValue(of(mockNotificationWithoutTiming));

    component.getNotificationList();
    tick();

    expect(component.notificationForm.get('time')?.value).toBe('05:00');
    expect(component.notificationForm.get('timeFormat')?.value).toBe('AM');
    expect(component.notificationForm.get('TimeZoneId')?.value).toBe('1');
  }));

  it('should handle notification list with empty digest timing', fakeAsync(() => {
    const mockNotificationWithEmptyTiming = {
      data: [{
        inAppNotification: [],
        emailNotification: [],
        digestTiming: { time: '', timeFormat: '', TimeZoneId: '' }
      }]
    };

    component.timezoneList = mockTimeZoneList.data;
    notificationService.getNotificationList.mockReturnValue(of(mockNotificationWithEmptyTiming));

    component.getNotificationList();
    tick();

    expect(component.notificationForm.get('time')?.value).toBe('05:00');
    expect(component.notificationForm.get('timeFormat')?.value).toBe('AM');
    expect(component.notificationForm.get('TimeZoneId')?.value).toBe('1');
  }));

  // Test toggle methods with false values

  it('should toggle notification sections to false', () => {
    component.inAppNotification(false);
    expect(component.isAppNotificationExpanded).toBe(false);

    component.emailNotification(false);
    expect(component.isEmailNotificationExpanded).toBe(false);
  });

  // Test form validation edge cases

  it('should have required validators on all form fields', () => {
    const timeControl = component.notificationForm.get('time');
    const timeFormatControl = component.notificationForm.get('timeFormat');
    const timeZoneControl = component.notificationForm.get('TimeZoneId');

    // Test required validation
    timeControl?.setValue('');
    timeFormatControl?.setValue('');
    timeZoneControl?.setValue('');

    expect(timeControl?.hasError('required')).toBe(true);
    expect(timeFormatControl?.hasError('required')).toBe(true);
    expect(timeZoneControl?.hasError('required')).toBe(true);
  });

  it('should validate form fields with valid values', () => {
    const timeControl = component.notificationForm.get('time');
    const timeFormatControl = component.notificationForm.get('timeFormat');
    const timeZoneControl = component.notificationForm.get('TimeZoneId');

    timeControl?.setValue('09:30');
    timeFormatControl?.setValue('PM');
    timeZoneControl?.setValue('2');

    expect(timeControl?.valid).toBe(true);
    expect(timeFormatControl?.valid).toBe(true);
    expect(timeZoneControl?.valid).toBe(true);
  });

  // Test constructor subscription edge cases

  it('should handle project service subscription with undefined response', fakeAsync(() => {
    // Reset the component to test constructor behavior
    const newProjectService = {
      projectParent: of(undefined),
      getTimeZoneList: jest.fn().mockReturnValue(of(mockTimeZoneList))
    };

    TestBed.overrideProvider(ProjectService, { useValue: newProjectService });
    const newFixture = TestBed.createComponent(NotificationSettingsComponent);
    const newComponent = newFixture.componentInstance;

    newFixture.detectChanges();
    tick();

    // Should not call getMemberRole when projectParent is undefined
    expect(deliveryService.getMemberRole).not.toHaveBeenCalled();
  }));

  it('should handle project service subscription with null response', fakeAsync(() => {
    const newProjectService = {
      projectParent: of(null),
      getTimeZoneList: jest.fn().mockReturnValue(of(mockTimeZoneList))
    };

    TestBed.overrideProvider(ProjectService, { useValue: newProjectService });
    const newFixture = TestBed.createComponent(NotificationSettingsComponent);
    const newComponent = newFixture.componentInstance;

    newFixture.detectChanges();
    tick();

    expect(deliveryService.getMemberRole).not.toHaveBeenCalled();
  }));

  it('should handle project service subscription with empty string response', fakeAsync(() => {
    const newProjectService = {
      projectParent: of(''),
      getTimeZoneList: jest.fn().mockReturnValue(of(mockTimeZoneList))
    };

    TestBed.overrideProvider(ProjectService, { useValue: newProjectService });
    const newFixture = TestBed.createComponent(NotificationSettingsComponent);
    const newComponent = newFixture.componentInstance;

    newFixture.detectChanges();
    tick();

    expect(deliveryService.getMemberRole).not.toHaveBeenCalled();
  }));
});

