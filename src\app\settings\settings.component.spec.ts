import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SettingsComponent } from './settings.component';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { NO_ERRORS_SCHEMA, Component } from '@angular/core';

// Mock component for app-override-request
@Component({
  selector: 'app-override-request',
  template: '<div>Mock Override Request</div>'
})
class MockOverrideRequestComponent {}

describe('SettingsComponent', () => {
  let component: SettingsComponent;
  let fixture: ComponentFixture<SettingsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ 
        SettingsComponent,
        MockOverrideRequestComponent
      ],
      imports: [TabsModule],
      schemas: [NO_ERRORS_SCHEMA]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SettingsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should render the settings title', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    const titleElement = compiled.querySelector('.title');
    expect(titleElement).toBeTruthy();
    expect(titleElement.textContent).toContain('Settings');
  });
});
