import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CraneComponent } from './crane.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { ReportsService } from '../../services/reports/reports.service';
import { ProjectService } from '../../services/profile/project.service';
import { ToastrService } from 'ngx-toastr';
import { DeliveryService } from '../../services/profile/delivery.service';
import { of, BehaviorSubject } from 'rxjs';
import { ElementRef, Pipe, PipeTransform, NO_ERRORS_SCHEMA } from '@angular/core';
import { SchedulerFormComponent } from '../scheduler-form/scheduler-form.component';
import { SaveReportFormComponent } from '../save-report-form/save-report-form.component';

@Pipe({
  name: 'paginate'
})
class MockPaginatePipe implements PipeTransform {
  transform(value: any[], config: any): any[] {
    return value;
  }
}

describe('CraneComponent', () => {
  let component: CraneComponent;
  let fixture: ComponentFixture<CraneComponent>;
  let modalServiceSpy: jest.Mocked<BsModalService>;
  let reportsServiceSpy: jest.Mocked<ReportsService>;
  let projectServiceSpy: jest.Mocked<ProjectService>;
  let toastrServiceSpy: jest.Mocked<ToastrService>;
  let deliveryServiceSpy: jest.Mocked<DeliveryService>;
  let formBuilder: UntypedFormBuilder;

  beforeEach(async () => {
    modalServiceSpy = {
      show: jest.fn().mockReturnValue({
        hide: jest.fn(),
        content: {}
      })
    } as any;

    reportsServiceSpy = {
      craneReports: jest.fn().mockReturnValue(of({
        data: { rows: [], count: 0 }
      })),
      exportCraneRequest: jest.fn().mockReturnValue(of({
        data: 'http://example.com/report.pdf'
      })),
      exportCraneRequestInExcelFormat: jest.fn().mockReturnValue(of({}))
    } as any;

    projectServiceSpy = {
      projectParent: new BehaviorSubject({
        ProjectId: '123',
        ParentCompanyId: '456'
      }),
      gateList: jest.fn().mockReturnValue(of({
        data: [{ id: 1, gateName: 'Gate 1' }]
      })),
      listEquipment: jest.fn().mockReturnValue(of({
        data: [{ id: 1, equipmentName: 'Equipment 1' }]
      })),
      getDefinableWork: jest.fn().mockReturnValue(of({
        data: [{ id: 1, DFOW: 'DFOW 1' }]
      })),
      getCompanies: jest.fn().mockReturnValue(of({
        data: [{ id: 1, companyName: 'Company 1' }]
      })),
      getLocations: jest.fn().mockReturnValue(of({
        data: [{ id: 1, locationPath: 'Location 1' }]
      })),
      listAllMember: jest.fn().mockReturnValue(of({
        data: [{ id: 1, User: { email: '<EMAIL>' } }]
      }))
    } as any;

    toastrServiceSpy = {
      success: jest.fn(),
      error: jest.fn()
    } as any;

    deliveryServiceSpy = {
      saveAsExcelFile: jest.fn()
    } as any;

    formBuilder = new UntypedFormBuilder();

    await TestBed.configureTestingModule({
      declarations: [CraneComponent, MockPaginatePipe],
      imports: [ReactiveFormsModule],
      providers: [
        { provide: BsModalService, useValue: modalServiceSpy },
        { provide: ReportsService, useValue: reportsServiceSpy },
        { provide: ProjectService, useValue: projectServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: DeliveryService, useValue: deliveryServiceSpy },
        { provide: UntypedFormBuilder, useValue: formBuilder },
        { provide: ElementRef, useValue: { nativeElement: document.createElement('div') } }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CraneComponent);
    component = fixture.componentInstance;

    // Initialize component properties
    component.craneLoader = true;
    component.pageSize = 25;
    component.pageNo = 1;
    component.filterCount = 0;
    component.showSearchbar = false;
    component.currentPageNo = 1;
    component.perPage = 1;

    // Initialize forms
    component.exportForm = formBuilder.group({
      reportType: ['PDF'], // Set default value to 'PDF' here
      reportName: ['Crane Report'],
      schedule: [{}]
    });

    component.filterForm = formBuilder.group({
      equipment: [''],
      status: [''],
      gate: [''],
      company: [''],
      location: [''],
      responsible: [''],
      define: [''],
      descriptionFilter: [''],
      dateFilter: [''],
      statusFilter: [''],
      equipmentFilter: [''],
      gateFilter: [''],
      companyFilter: [''],
      locationFilter: [''],
      responsibleFilter: [''],
      defineFilter: [''],
      memberFilter: [''],
      pickFrom: [''],
      pickTo: [''],
      idFilter: [''] // Make sure idFilter is included
    });

    // Mock document click handler
    document.addEventListener = jest.fn();

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Initialization', () => {
    it('should initialize with default values', () => {
      expect(component.currentPageNo).toBe(1);
      expect(component.perPage).toBe(1);
      expect(component.filterCount).toBe(0);
      expect(component.showSearchbar).toBe(false);
      expect(component.pageSize).toBe(25);
      expect(component.pageNo).toBe(1);
      expect(component.craneLoader).toBe(true);
      expect(component.selectedRecurrence).toBe('Does Not Repeat');
      expect(component.reportType).toBe('PDF');
      expect(component.reportName).toBe('Crane Report');
      expect(component.sortColumn).toBe('craneDeliveryStart');
      expect(component.sort).toBe('ASC');
      expect(component.search).toBe('');
    });

    it('should initialize dropdown states', () => {
      expect(component.isEquipmentDropdownOpen).toBe(false);
      expect(component.isStatusDropdownOpen).toBe(false);
      expect(component.isGateDropdownOpen).toBe(false);
      expect(component.isCompanyDropdownOpen).toBe(false);
      expect(component.isLocationDropdownOpen).toBe(false);
      expect(component.isResponsibleDropdownOpen).toBe(false);
      expect(component.isDefineDropdownOpen).toBe(false);
    });

    it('should initialize selected arrays', () => {
      expect(component.selectedEquipment).toEqual([]);
      expect(component.selectedGate).toEqual([]);
      expect(component.selectedCompany).toEqual([]);
      expect(component.selectedLocation).toEqual([]);
      expect(component.selectedResponsible).toEqual([]);
      expect(component.selectedDefine).toEqual([]);
    });

    it('should initialize placeholders', () => {
      expect(component.equipmentPlaceholder).toBe('Select Equipment');
      expect(component.gatePlaceholder).toBe('Select Gate');
      expect(component.companyPlaceholder).toBe('Select Company');
      expect(component.locationPlaceholder).toBe('Select Location');
      expect(component.statusPlaceholder).toBe('Select Status');
      expect(component.responsiblePlaceholder).toBe('Select Responsible Person');
      expect(component.definePlaceholder).toBe('Select DFOW');
    });

    it('should initialize status list', () => {
      expect(component.wholeStatus).toEqual(['Approved', 'Completed', 'Declined', 'Delivered', 'Pending']);
      expect(component.statusList).toEqual([
        { name: 'Approved' },
        { name: 'Declined' },
        { name: 'Delivered' },
        { name: 'Pending' }
      ]);
    });

    it('should initialize table headers', () => {
      expect(component.tableHeaders.id.isActive).toBe(true);
      expect(component.tableHeaders.description.isActive).toBe(true);
      expect(component.tableHeaders.date.isActive).toBe(true);
      expect(component.tableHeaders.status.isActive).toBe(true);
      expect(component.tableHeaders.approvedby.isActive).toBe(true);
      expect(component.tableHeaders.equipment.isActive).toBe(false);
    });

    it('should initialize export type array', () => {
      expect(component.exportType).toEqual(['EXCEL', 'CSV', 'PDF']);
    });

    it('should initialize filter form', () => {
      expect(component.filterForm).toBeDefined();
      expect(component.filterForm.get('companyFilter')).toBeDefined();
      expect(component.filterForm.get('descriptionFilter')).toBeDefined();
      expect(component.filterForm.get('statusFilter')).toBeDefined();
      expect(component.filterForm.get('dateFilter')).toBeDefined();
      expect(component.filterForm.get('memberFilter')).toBeDefined();
      expect(component.filterForm.get('equipmentFilter')).toBeDefined();
      expect(component.filterForm.get('gateFilter')).toBeDefined();
      expect(component.filterForm.get('defineFilter')).toBeDefined();
      expect(component.filterForm.get('idFilter')).toBeDefined();
      expect(component.filterForm.get('pickFrom')).toBeDefined();
      expect(component.filterForm.get('pickTo')).toBeDefined();
      expect(component.filterForm.get('locationFilter')).toBeDefined();
    });

    it('should initialize export form', () => {
      expect(component.exportForm).toBeDefined();
      expect(component.exportForm.get('reportName')).toBeDefined();
      expect(component.exportForm.get('reportType')).toBeDefined();
      // expect(component.exportForm.get('reportName').value).toBe('Crane Report');
      expect(component.exportForm.get('reportType').value).toBe('PDF');
    });

    it('should initialize active headers from table headers', () => {
      const activeHeaders = Object.values(component.tableHeaders).filter(
        (element: any) => element.isActive === true
      );
      expect(component.activeHeaders.length).toBe(activeHeaders.length);
    });
  });

  describe('Modal Operations', () => {
    it('should open export modal', () => {
      const template = {} as any;
      component.openModal(template);
      expect(modalServiceSpy.show).toHaveBeenCalledWith(template, {
        class: 'modal-export'
      });
    });

    it('should open downloads modal', () => {
      const template = {} as any;
      component.openModal1(template);
      expect(modalServiceSpy.show).toHaveBeenCalledWith(template, {
        class: 'modal-downloads'
      });
    });

    it('should open send to modal', () => {
      const template = {} as any;
      component.openModal3(template);
      expect(modalServiceSpy.show).toHaveBeenCalledWith(template, {
        class: 'modal-sendto'
      });
    });

    it('should open filter modal', () => {
      const template = {} as any;
      component.openModal4(template);
      expect(modalServiceSpy.show).toHaveBeenCalledWith(template, {
        class: ' report-filter-modal filter-popup report-filter custom-modal'
      });
    });

    it('should open schedule popup', () => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.updatedMemberList = [{ id: 1, UserEmail: '<EMAIL>' }];
      component.filterPayload = { test: 'data' };

      component.openSchedulePopup();

      expect(modalServiceSpy.show).toHaveBeenCalled();
    });

    it('should open save modal', () => {
      component.ProjectId = '123';
      component.filterPayload = { test: 'data' };
      component.updatedMemberList = [{ id: 1, UserEmail: '<EMAIL>' }];
      component.pageSize = 25;
      component.pageNo = 1;

      component.openModalSave();

      expect(modalServiceSpy.show).toHaveBeenCalled();
    });

    it('should close filter popup', () => {
      component.modalRef4 = { hide: jest.fn() } as any;
      component.closeFilterPopup();

      expect(component.modalRef4.hide).toHaveBeenCalled();
      expect(component.isEquipmentDropdownOpen).toBe(false);
      expect(component.isStatusDropdownOpen).toBe(false);
    });
  });

  describe('Dropdown Functionality', () => {
    it('should toggle dropdowns correctly', () => {
      component.toggleDropdown('equipment');
      expect(component.isEquipmentDropdownOpen).toBe(true);

      component.toggleDropdown('equipment');
      expect(component.isEquipmentDropdownOpen).toBe(false);
    });

    it('should close all dropdowns when toggling a different one', () => {
      component.isEquipmentDropdownOpen = true;
      component.isStatusDropdownOpen = true;

      component.toggleDropdown('gate');

      expect(component.isEquipmentDropdownOpen).toBe(false);
      expect(component.isStatusDropdownOpen).toBe(false);
      expect(component.isGateDropdownOpen).toBe(true);
    });

    it('should handle document click outside dropdowns', () => {
      component.isEquipmentDropdownOpen = true;
      component.isStatusDropdownOpen = true;
      component.isGateDropdownOpen = true;

      const event = new MouseEvent('click');
      Object.defineProperty(event, 'target', {
        value: document.createElement('div')
      });

      component.onDocumentClick(event);

      expect(component.isEquipmentDropdownOpen).toBe(false);
      expect(component.isStatusDropdownOpen).toBe(false);
      expect(component.isGateDropdownOpen).toBe(false);
    });


  });

  describe('Pagination and Sorting', () => {
    it('should handle page size change', () => {
      const newPageSize = 50;
      jest.spyOn(component, 'getCraneReports');

      component.changeCranePageSize(newPageSize);

      expect(component.pageSize).toBe(newPageSize);
      expect(component.getCraneReports).toHaveBeenCalled();
    });

    it('should handle page number change', () => {
      const newPageNo = 2;
      jest.spyOn(component, 'getCraneReports');

      component.changePageNo(newPageNo);

      expect(component.pageNo).toBe(newPageNo);
      expect(component.getCraneReports).toHaveBeenCalled();
    });

    it('should handle sort by field', () => {
      const fieldName = 'description';
      const sortType = 'DESC';
      jest.spyOn(component, 'getCraneReports');

      component.sortByField(fieldName, sortType);

      expect(component.sortColumn).toBe(fieldName);
      expect(component.sort).toBe(sortType);
      expect(component.getCraneReports).toHaveBeenCalled();
    });
  });

  describe('Filter Operations', () => {
    it('should handle filter submission with all fields', () => {
      jest.spyOn(component, 'getCraneReports');
      component.filterForm.patchValue({
        descriptionFilter: 'test description',
        dateFilter: ['2024-01-01', '2024-01-31'],
        companyFilter: [1, 2],
        memberFilter: [1],
        gateFilter: [1],
        equipmentFilter: [1],
        statusFilter: ['Approved'],
        defineFilter: [1],
        idFilter: 'CR001',
        pickFrom: 'Location A',
        pickTo: 'Location B',
        locationFilter: [1],
        responsibleFilter: [1] // Add responsibleFilter to make count 11
      });

      component.filterSubmit();

      expect(component.filterCount).toBe(10);
      expect(component.pageNo).toBe(1);
      expect(component.getCraneReports).toHaveBeenCalled();
    });

    it('should handle filter submission with empty fields', () => {
      jest.spyOn(component, 'getCraneReports');
      component.filterForm.patchValue({
        descriptionFilter: '',
        dateFilter: '',
        companyFilter: '',
        memberFilter: '',
        gateFilter: '',
        equipmentFilter: '',
        statusFilter: '',
        defineFilter: '',
        idFilter: '',
        pickFrom: '',
        pickTo: '',
        locationFilter: ''
      });

      component.filterSubmit();

      expect(component.filterCount).toBe(0);
      expect(component.getCraneReports).toHaveBeenCalled();
    });

    it('should close filter popup after submission', () => {
      component.modalRef4 = { hide: jest.fn() } as any;
      jest.spyOn(component, 'closeFilterPopup');
      jest.spyOn(component, 'getCraneReports');

      component.filterSubmit();

      expect(component.closeFilterPopup).toHaveBeenCalled();
    });

    it('should reset filters correctly', () => {
      jest.spyOn(component, 'getCraneReports');
      component.selectedEquipment = [{ id: 1, equipmentName: 'Equipment 1' }];
      component.selectedStatus = [{ name: 'Approved' }];
      component.filterCount = 5;
      component.filterForm.get('descriptionFilter').setValue('test description');

      component.resetFilter();

      expect(component.selectedEquipment).toEqual([]);
      expect(component.selectedStatus).toEqual([]);
      expect(component.selectedGate).toEqual([]);
      expect(component.selectedCompany).toEqual([]);
      expect(component.selectedLocation).toEqual([]);
      expect(component.selectedResponsible).toEqual([]);
      expect(component.selectedDefine).toEqual([]);
      expect(component.filterCount).toBe(0);
      expect(component.pageNo).toBe(1);
      expect(component.gatePlaceholder).toBe('Select Gate');
      expect(component.companyPlaceholder).toBe('Select Company');
      expect(component.equipmentPlaceholder).toBe('Select Equipment');
      expect(component.getCraneReports).toHaveBeenCalled();
    });
  });

  describe('Utility Methods', () => {
    it('should map filter array correctly', () => {
      const testArray = [1, 2, 3];
      const result = component.mapFilterArray(testArray);
      expect(result).toEqual([1, 2, 3]);
    });

    it('should return empty array for null input', () => {
      const result = component.mapFilterArray(null);
      expect(result).toEqual([]);
    });

    it('should return empty array for empty input', () => {
      const result = component.mapFilterArray([]);
      expect(result).toEqual([]);
    });

    it('should handle recurrence selection', () => {
      const value = 'Weekly';
      component.onRecurrenceSelect(value);
      expect(component.selectedRecurrence).toBe(value);
    });
  });

  describe('Data Loading Methods', () => {
    beforeEach(() => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
    });

    it('should call getCraneReports on initialization', () => {
      jest.spyOn(component, 'getCraneReports');
      component.ngOnInit();
      expect(component.getCraneReports).toHaveBeenCalled();
    });

    it('should fetch crane reports successfully', () => {
      const mockResponse = {
        data: {
          rows: [{ id: 1, description: 'Test crane' }],
          count: 1
        }
      };
      reportsServiceSpy.craneReports.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getOverAllGateForNdrGrid');

      component.getCraneReports();

      expect(component.craneLoader).toBe(true);
      expect(reportsServiceSpy.craneReports).toHaveBeenCalled();
      expect(component.getOverAllGateForNdrGrid).toHaveBeenCalled();
    });

    it('should handle crane reports response', () => {
      const mockResponse = {
        data: {
          rows: [{ id: 1, description: 'Test crane' }],
          count: 1
        }
      };
      reportsServiceSpy.craneReports.mockReturnValue(of(mockResponse));

      component.getCraneReports();

      expect(component.craneRequestList).toEqual(mockResponse.data.rows);
      expect(component.craneRequestTotalCount).toBe(mockResponse.data.count);
      expect(component.craneLoader).toBe(false);
    });

    it('should get gate list successfully', () => {
      const mockGateResponse = {
        data: [{ id: 1, gateName: 'Gate 1' }]
      };
      projectServiceSpy.gateList.mockReturnValue(of(mockGateResponse));
      jest.spyOn(component, 'getOverAllEquipmentForNdrGrid');

      component.getOverAllGateForNdrGrid();

      expect(projectServiceSpy.gateList).toHaveBeenCalledWith(
        {
          ProjectId: component.ProjectId,
          pageSize: 0,
          pageNo: 0,
          ParentCompanyId: component.ParentCompanyId
        },
        { isFilter: true, showActivatedAlone: true }
      );
      expect(component.gateList).toEqual(mockGateResponse.data);
      expect(component.getOverAllEquipmentForNdrGrid).toHaveBeenCalled();
    });

    it('should get equipment list successfully', () => {
      const mockEquipmentResponse = {
        data: [{ id: 1, equipmentName: 'Equipment 1' }]
      };
      projectServiceSpy.listEquipment.mockReturnValue(of(mockEquipmentResponse));
      jest.spyOn(component, 'getDefinableForNdrGrid');

      component.getOverAllEquipmentForNdrGrid();

      expect(projectServiceSpy.listEquipment).toHaveBeenCalledWith(
        {
          ProjectId: component.ProjectId,
          pageSize: 0,
          pageNo: 0,
          ParentCompanyId: component.ParentCompanyId
        },
        { isFilter: true, showActivatedAlone: true }
      );
      expect(component.equipmentList).toEqual(mockEquipmentResponse.data);
      expect(component.getDefinableForNdrGrid).toHaveBeenCalled();
    });

    it('should get definable work list successfully', () => {
      const mockDefineResponse = {
        data: [{ id: 1, DFOW: 'DFOW 1' }]
      };
      projectServiceSpy.getDefinableWork.mockReturnValue(of(mockDefineResponse));
      jest.spyOn(component, 'getCompaniesForNdrGrid');

      component.getDefinableForNdrGrid();

      expect(projectServiceSpy.getDefinableWork).toHaveBeenCalledWith({
        ProjectId: component.ProjectId,
        ParentCompanyId: component.ParentCompanyId
      });
      expect(component.defineList).toEqual(mockDefineResponse.data);
      expect(component.getCompaniesForNdrGrid).toHaveBeenCalled();
    });

    it('should get companies list successfully', () => {
      const mockCompanyResponse = {
        data: [{ id: 1, companyName: 'Company 1' }]
      };
      projectServiceSpy.getCompanies.mockReturnValue(of(mockCompanyResponse));
      jest.spyOn(component, 'getLocationsForNdrGrid');

      component.getCompaniesForNdrGrid();

      expect(projectServiceSpy.getCompanies).toHaveBeenCalledWith({
        ProjectId: component.ProjectId,
        ParentCompanyId: component.ParentCompanyId
      });
      expect(component.companyList).toEqual(mockCompanyResponse.data);
      expect(component.getLocationsForNdrGrid).toHaveBeenCalled();
    });

    it('should get locations list successfully', () => {
      const mockLocationResponse = {
        data: [{ id: 1, locationPath: 'Location 1' }]
      };
      projectServiceSpy.getLocations.mockReturnValue(of(mockLocationResponse));

      component.getLocationsForNdrGrid();

      expect(projectServiceSpy.getLocations).toHaveBeenCalledWith({
        ProjectId: component.ProjectId,
        ParentCompanyId: component.ParentCompanyId
      });
      expect(component.locationList).toEqual(mockLocationResponse.data);
      expect(component.filteredLocationList).toEqual(mockLocationResponse.data);
    });

    it('should get members list successfully', () => {
      const mockMemberResponse = {
        data: [{ id: 1, User: { email: '<EMAIL>' } }]
      };
      projectServiceSpy.listAllMember.mockReturnValue(of(mockMemberResponse));

      component.getMembers();

      expect(projectServiceSpy.listAllMember).toHaveBeenCalledWith({
        ProjectId: component.ProjectId,
        ParentCompanyId: component.ParentCompanyId
      });
      expect(component.memberList).toEqual(mockMemberResponse.data);
      expect(component.updatedMemberList).toEqual([
        { id: 1, User: { email: '<EMAIL>' }, UserEmail: '<EMAIL>' }
      ]);
    });
  });

  describe('Payload Preparation', () => {
    beforeEach(() => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.search = 'test search';
      component.sort = 'DESC';
      component.sortColumn = 'description';
    });

    it('should prepare crane request payload with all filters', () => {
      component.filterForm.patchValue({
        companyFilter: 1,
        descriptionFilter: 'test description',
        dateFilter: ['2024-01-01', '2024-01-31'],
        statusFilter: ['Approved'],
        memberFilter: [1],
        gateFilter: [1],
        equipmentFilter: [1],
        locationFilter: [1],
        defineFilter: [1],
        idFilter: 'CR001',
        pickFrom: 'Location A',
        pickTo: 'Location B'
      });

      const payload = component.prepareCraneRequestPayload();

      expect(payload.companyFilter).toBe(1);
      expect(payload.descriptionFilter).toBe('test description');
      expect(payload.startdate).toBe('2024-01-01');
      expect(payload.enddate).toBe('2024-01-31');
      expect(payload.statusFilter).toEqual(['Approved']);
      expect(payload.memberFilter).toEqual([1]);
      expect(payload.gateFilter).toEqual([1]);
      expect(payload.equipmentFilter).toEqual([1]);
      expect(payload.locationFilter).toEqual([1]);
      expect(payload.defineFilter).toEqual([1]);
      expect(payload.idFilter).toBe('CR001');
      expect(payload.pickFrom).toBe('Location A');
      expect(payload.pickTo).toBe('Location B');
      expect(payload.search).toBe('test search');
      expect(payload.sort).toBe('DESC');
      expect(payload.sortByField).toBe('description');
      expect(payload.ParentCompanyId).toBe('456');
    });

    it('should prepare crane request payload with empty filters', () => {
      component.filterForm.patchValue({
        companyFilter: '',
        descriptionFilter: '',
        dateFilter: '',
        statusFilter: '',
        memberFilter: '',
        gateFilter: '',
        equipmentFilter: '',
        locationFilter: '',
        defineFilter: '',
        idFilter: '',
        pickFrom: '',
        pickTo: ''
      });

      const payload = component.prepareCraneRequestPayload();

      expect(payload.companyFilter).toBe(0);
      expect(payload.descriptionFilter).toBe('');
      expect(payload.startdate).toBe('');
      expect(payload.enddate).toBe('');
      expect(payload.statusFilter).toEqual([]);
      expect(payload.idFilter).toBe(0);
    });

    it('should handle undefined filter form', () => {
      component.filterForm = undefined;

      const payload = component.prepareCraneRequestPayload();

      expect(payload.search).toBe('test search');
      expect(payload.sort).toBe('DESC');
      expect(payload.sortByField).toBe('description');
      expect(payload.ParentCompanyId).toBe('456');
    });
  });

  describe('Selection and Toggle Methods', () => {
    beforeEach(() => {
      component.selectedEquipment = [];
      component.selectedGate = [];
      component.selectedCompany = [];
      component.selectedLocation = [];
      component.selectedResponsible = [];
      component.selectedDefine = [];
      component.selectedStatus = [];
      component.locationList = [
        { id: 1, locationPath: 'Location 1', LocationId: null },
        { id: 2, locationPath: 'Location 2', LocationId: 1 },
        { id: 3, locationPath: 'Location 3', LocationId: 2 }
      ];
    });

    it('should toggle equipment selection', () => {
      const equipment = { id: 1, equipmentName: 'Equipment 1' };
      const event = new Event('click');
      jest.spyOn(event, 'stopPropagation');

      component.toggleSelection(equipment, 'equipment', event);

      expect(component.selectedEquipment).toContain(equipment);
      expect(component.equipmentPlaceholder).toBe('Equipment 1');
    });

    it('should toggle gate selection', () => {
      const gate = { id: 1, gateName: 'Gate 1' };
      const event = new Event('click');

      component.toggleSelection(gate, 'gate', event);

      expect(component.selectedGate).toContain(gate);
      expect(component.gatePlaceholder).toBe('Gate 1');
    });

    it('should toggle company selection', () => {
      const company = { id: 1, companyName: 'Company 1' };
      const event = new Event('click');

      component.toggleSelection(company, 'company', event);

      expect(component.selectedCompany).toContain(company);
      expect(component.companyPlaceholder).toBe('Company 1');
    });

    it('should toggle status selection', () => {
      const status = { name: 'Approved' };
      const event = new Event('click');

      component.toggleSelection(status, 'status', event);

      expect(component.selectedStatus).toContain(status);
      expect(component.statusPlaceholder).toBe('Approved');
    });

    it('should toggle responsible selection', () => {
      const responsible = { id: 1, User: { email: '<EMAIL>' } };
      const event = new Event('click');

      component.toggleSelection(responsible, 'responsible', event);

      expect(component.selectedResponsible).toContain(responsible);
      expect(component.responsiblePlaceholder).toBe('<EMAIL>');
    });

    it('should toggle define selection', () => {
      const define = { id: 1, DFOW: 'DFOW 1' };
      const event = new Event('click');

      component.toggleSelection(define, 'define', event);

      expect(component.selectedDefine).toContain(define);
      expect(component.definePlaceholder).toBe('DFOW 1');
    });

    it('should handle multiple selections with placeholder update', () => {
      const equipment1 = { id: 1, equipmentName: 'Equipment 1' };
      const equipment2 = { id: 2, equipmentName: 'Equipment 2' };
      const event = new Event('click');

      component.toggleSelection(equipment1, 'equipment', event);
      component.toggleSelection(equipment2, 'equipment', event);

      expect(component.selectedEquipment).toHaveLength(2);
      expect(component.equipmentPlaceholder).toBe('Equipment 1 + 1 more');
    });

    it('should unselect item when already selected', () => {
      const equipment = { id: 1, equipmentName: 'Equipment 1' };
      const event = new Event('click');

      // First selection
      component.toggleSelection(equipment, 'equipment', event);
      expect(component.selectedEquipment).toContain(equipment);

      // Second selection (unselect)
      component.toggleSelection(equipment, 'equipment', event);
      expect(component.selectedEquipment).not.toContain(equipment);
      expect(component.equipmentPlaceholder).toBe('Select Equipment');
    });

    it('should handle location selection with children', () => {
      const location = { id: 1, locationPath: 'Location 1', LocationId: null };
      const event = new Event('click');

      component.toggleSelection(location, 'location', event);

      expect(component.selectedLocation).toContain(location);
      expect(component.locationPlaceholder).toBe('Location 1');
    });

    it('should handle location selection with isDefault true', () => {
      const location = { id: 1, locationPath: 'All Locations', isDefault: true };
      const event = new Event('click');

      component.toggleSelection(location, 'location', event);

      expect(component.selectedLocation).toEqual(component.locationList);
    });

    it('should check if item is selected correctly', () => {
      const equipment = { id: 1, equipmentName: 'Equipment 1' };
      component.selectedEquipment = [equipment];

      expect(component.isSelected(equipment, 'equipment')).toBe(true);
      expect(component.isSelected({ id: 2, equipmentName: 'Equipment 2' }, 'equipment')).toBe(false);
    });

    it('should check if status is selected correctly', () => {
      const status = { name: 'Approved' };
      component.selectedStatus = [status];

      expect(component.isSelected(status, 'status')).toBe(true);
      expect(component.isSelected({ name: 'Declined' }, 'status')).toBe(false);
    });

    it('should return false for unknown selection type', () => {
      const item = { id: 1, name: 'Test' };
      expect(component.isSelected(item, 'unknown')).toBe(false);
    });
  });

  describe('Form Control Methods', () => {
    beforeEach(() => {
      component.filterForm = formBuilder.group({
        equipmentFilter: [[]],
        gateFilter: [[]],
        companyFilter: [[]],
        locationFilter: [[]],
        memberFilter: [[]],
        statusFilter: [[]],
        defineFilter: [[]]
      });
    });

    it('should select equipment option', () => {
      const equipment = { id: 1, equipmentName: 'Equipment 1' };
      const event = new Event('click');
      jest.spyOn(event, 'stopPropagation');

      component.selectOption(equipment, 'equipment', event);

      expect(event.stopPropagation).toHaveBeenCalled();
      expect(component.filterForm.get('equipmentFilter').value).toContain(1);
    });

    it('should select gate option', () => {
      const gate = { id: 1, gateName: 'Gate 1' };
      const event = new Event('click');

      component.selectOption(gate, 'gate', event);

      expect(component.filterForm.get('gateFilter').value).toContain(1);
    });

    it('should select responsible option using memberFilter', () => {
      const responsible = { id: 1, User: { email: '<EMAIL>' } };
      const event = new Event('click');

      component.selectOption(responsible, 'responsible', event);

      expect(component.filterForm.get('memberFilter').value).toContain(1);
    });

    it('should handle status selection', () => {
      const status = { name: 'Approved' };
      const event = new Event('click');

      component.selectOption(status, 'status', event);

      expect(component.filterForm.get('statusFilter').value).toContain('Approved');
    });

    it('should handle location selection with children', () => {
      component.locationList = [
        { id: 1, locationPath: 'Location 1', LocationId: null },
        { id: 2, locationPath: 'Location 2', LocationId: 1 }
      ];
      const location = { id: 1, locationPath: 'Location 1' };
      const event = new Event('click');

      component.selectOption(location, 'location', event);

      expect(component.filterForm.get('locationFilter').value).toContain(1);
    });

    it('should handle location selection with isDefault', () => {
      component.locationList = [
        { id: 1, locationPath: 'Location 1' },
        { id: 2, locationPath: 'Location 2' }
      ];
      const location = { id: 1, locationPath: 'All Locations', isDefault: true };
      const event = new Event('click');

      component.selectOption(location, 'location', event);

      expect(component.filterForm.get('locationFilter').value).toEqual([1, 2]);
    });

    it('should unselect option when already selected', () => {
      const equipment = { id: 1, equipmentName: 'Equipment 1' };
      const event = new Event('click');

      // First selection
      component.selectOption(equipment, 'equipment', event);
      expect(component.filterForm.get('equipmentFilter').value).toContain(1);

      // Second selection (unselect)
      component.selectOption(equipment, 'equipment', event);
      expect(component.filterForm.get('equipmentFilter').value).not.toContain(1);
    });

    it('should handle non-array form control values', () => {
      component.filterForm.get('equipmentFilter').setValue(null);
      const equipment = { id: 1, equipmentName: 'Equipment 1' };
      const event = new Event('click');

      component.selectOption(equipment, 'equipment', event);

      expect(component.filterForm.get('equipmentFilter').value).toContain(1);
    });

    it('should return early for non-existent form control', () => {
      const item = { id: 1, name: 'Test' };
      const event = new Event('click');

      component.selectOption(item, 'nonexistent', event);

      // Should not throw error and return early
      expect(true).toBe(true);
    });
  });

  describe('Export Functionality', () => {
    beforeEach(() => {
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.pageSize = 25;
      component.pageNo = 1;
      component.exportForm = formBuilder.group({
        reportName: ['Crane Report'],
        reportType: ['PDF']
      });
    });

    it('should handle export form initialization', () => {
      component.exportDetailsForm();

      expect(component.exportForm.get('reportName').value).toBe('Crane Report');
      expect(component.exportForm.get('reportType').value).toBe('PDF');
    });

    it('should handle export with invalid form', () => {
      component.exportForm.get('reportName').setValue('');
      component.exportForm.get('reportType').setValue('');

      component.onExport();

      expect(component.exportSubmitted).toBe(false);
      expect(component.formSubmitted).toBe(true);
    });

    it('should handle PDF export successfully', () => {
      const mockResponse = { data: 'http://example.com/report.pdf' };
      reportsServiceSpy.exportCraneRequest.mockReturnValue(of(mockResponse));
      component.reportType = 'PDF';

      // Mock document methods
      const mockLink = {
        setAttribute: jest.fn(),
        click: jest.fn(),
        remove: jest.fn()
      };
      jest.spyOn(document, 'createElement').mockReturnValue(mockLink as any);
      jest.spyOn(document.body, 'appendChild').mockImplementation();
      jest.spyOn(component, 'cancelexport');

      component.onExport();

      expect(reportsServiceSpy.exportCraneRequest).toHaveBeenCalled();
      expect(mockLink.setAttribute).toHaveBeenCalledWith('target', '_self');
      expect(mockLink.setAttribute).toHaveBeenCalledWith('href', mockResponse.data);
      expect(mockLink.click).toHaveBeenCalled();
      expect(mockLink.remove).toHaveBeenCalled();
      expect(toastrServiceSpy.success).toHaveBeenCalledWith('Crane Booking exported successfully');
      expect(component.cancelexport).toHaveBeenCalled();
    });

    it('should handle CSV export successfully', () => {
      const mockResponse = { data: 'http://example.com/report.csv' };
      reportsServiceSpy.exportCraneRequest.mockReturnValue(of(mockResponse));
      component.reportType = 'CSV';

      const mockLink = {
        setAttribute: jest.fn(),
        click: jest.fn(),
        remove: jest.fn()
      };
      jest.spyOn(document, 'createElement').mockReturnValue(mockLink as any);
      jest.spyOn(document.body, 'appendChild').mockImplementation();
      jest.spyOn(component, 'cancelexport');

      component.onExport();

      expect(reportsServiceSpy.exportCraneRequest).toHaveBeenCalled();
      expect(toastrServiceSpy.success).toHaveBeenCalledWith('Crane Booking exported successfully');
      expect(component.cancelexport).toHaveBeenCalled();
    });

    it('should handle EXCEL export successfully', () => {
      const mockResponse = { data: 'excel data' };
      reportsServiceSpy.exportCraneRequestInExcelFormat.mockReturnValue(of(mockResponse));
      component.reportType = 'EXCEL';
      jest.spyOn(component, 'cancelexport');

      component.onExport();

      expect(reportsServiceSpy.exportCraneRequestInExcelFormat).toHaveBeenCalled();
      expect(deliveryServiceSpy.saveAsExcelFile).toHaveBeenCalledWith(mockResponse, 'Crane Report');
      expect(toastrServiceSpy.success).toHaveBeenCalledWith('Crane Booking exported successfully');
      expect(component.cancelexport).toHaveBeenCalled();
    });

    it('should handle special sort condition in export', () => {
      component.sort = 'DESC';
      component.sortColumn = ' CraneRequestId';
      reportsServiceSpy.exportCraneRequest.mockReturnValue(of({ data: 'test' }));

      component.onExport();

      expect(component.sort).toBe('ASC');
      expect(component.sortColumn).toBe('datetime');
    });

    it('should cancel export correctly', () => {
      component.modalRef = { hide: jest.fn() } as any;
      component.exportSubmitted = true;

      component.cancelexport();

      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(component.exportSubmitted).toBe(false);
      expect(component.exportForm.get('reportName').value).toBe('Crane Report');
      expect(component.exportForm.get('reportType').value).toBe('PDF');
    });
  });

  describe('Keyboard Event Handlers', () => {
    it('should handle toggle keydown with Enter key', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'sortByField');

      component.handleToggleKeydown(event, 'description', 'ASC');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.sortByField).toHaveBeenCalledWith('description', 'ASC');
    });

    it('should handle toggle keydown with Space key', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'sortByField');

      component.handleToggleKeydown(event, 'date', 'DESC');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.sortByField).toHaveBeenCalledWith('date', 'DESC');
    });

    it('should not handle toggle keydown with other keys', () => {
      const event = new KeyboardEvent('keydown', { key: 'Tab' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'sortByField');

      component.handleToggleKeydown(event, 'description', 'ASC');

      expect(event.preventDefault).not.toHaveBeenCalled();
      expect(component.sortByField).not.toHaveBeenCalled();
    });

    it('should handle dropdown keydown with Enter key', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'toggleDropdown');

      component.handleDownKeydown(event, 'equipment');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.toggleDropdown).toHaveBeenCalledWith('equipment');
    });

    it('should handle dropdown keydown with Space key', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'toggleDropdown');

      component.handleDownKeydown(event, 'status');

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.toggleDropdown).toHaveBeenCalledWith('status');
    });
  });

  describe('Table Header Management', () => {
    beforeEach(() => {
      // Mock DOM elements
      const mockCheckbox = {
        checked: false,
        id: 'equipment'
      };
      jest.spyOn(document, 'getElementById').mockReturnValue(mockCheckbox as any);
    });

    it('should handle input change for activating header', () => {
      const mockCheckbox = { checked: true };
      jest.spyOn(document, 'getElementById').mockReturnValue(mockCheckbox as any);

      component.handleInputChange('equipment');

      expect(component.tableHeaders.equipment.isActive).toBe(true);
      expect(component.activeHeaders.length).toBeGreaterThan(0);
    });

    it('should handle input change for deactivating header when more than one active', () => {
      const mockCheckbox = { checked: false };
      jest.spyOn(document, 'getElementById').mockReturnValue(mockCheckbox as any);
      component.activeHeaders = [
        { isActive: true },
        { isActive: true }
      ];

      component.handleInputChange('equipment');

      expect(component.tableHeaders.equipment.isActive).toBe(false);
    });

    it('should not deactivate header when only one active', () => {
      const mockCheckbox = { checked: false };
      jest.spyOn(document, 'getElementById').mockReturnValue(mockCheckbox as any);
      component.activeHeaders = [{ isActive: true }];

      component.handleInputChange('equipment');

      // Should not change when only one header is active
      expect(component.activeHeaders.length).toBe(1);
    });

    it('should select all headers when selectall is checked', () => {
      const mockCheckbox = { checked: true };
      jest.spyOn(document, 'getElementById').mockReturnValue(mockCheckbox as any);

      component.selectall('selectAll');

      Object.keys(component.tableHeaders).forEach(key => {
        expect(component.tableHeaders[key].isActive).toBe(true);
      });
    });

    it('should deselect all headers except required ones when selectall is unchecked', () => {
      const mockCheckbox = { checked: false };
      jest.spyOn(document, 'getElementById').mockReturnValue(mockCheckbox as any);

      component.selectall('selectAll');

      expect(component.tableHeaders.id.isActive).toBe(true);
      expect(component.tableHeaders.description.isActive).toBe(true);
      expect(component.tableHeaders.date.isActive).toBe(true);
      expect(component.tableHeaders.equipment.isActive).toBe(false);
    });
  });

  describe('Location Filtering', () => {
    beforeEach(() => {
      component.locationList = [
        { id: 1, locationPath: 'Location A' },
        { id: 2, locationPath: 'Location B' },
        { id: 3, locationPath: 'Another Place' }
      ];
      component.filteredLocationList = [...component.locationList];
    });

    it('should filter location list based on search input', () => {
      const event = {
        target: { value: 'Location' }
      };

      component.changeLocationFilterOptionList(event);

      expect(component.filteredLocationList).toHaveLength(2);
      expect(component.filteredLocationList[0].locationPath).toBe('Location A');
      expect(component.filteredLocationList[1].locationPath).toBe('Location B');
    });

    it('should show all locations when search is empty', () => {
      const event = {
        target: { value: '' }
      };

      component.changeLocationFilterOptionList(event);

      expect(component.filteredLocationList).toEqual(component.locationList);
    });

    it('should handle case insensitive search', () => {
      const event = {
        target: { value: 'location' }
      };

      component.changeLocationFilterOptionList(event);

      expect(component.filteredLocationList).toHaveLength(2);
    });

    it('should return empty list when no matches found', () => {
      const event = {
        target: { value: 'NonExistent' }
      };

      component.changeLocationFilterOptionList(event);

      expect(component.filteredLocationList).toHaveLength(0);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle null project service response', () => {
      projectServiceSpy.projectParent.next(null);

      // Should not throw error
      expect(component.ProjectId).toBeUndefined();
    });

    it('should handle empty project service response', () => {
      projectServiceSpy.projectParent.next('');

      // Should not throw error
      expect(component.ProjectId).toBeUndefined();
    });

    it('should handle undefined project service response', () => {
      projectServiceSpy.projectParent.next(undefined);

      // Should not throw error
      expect(component.ProjectId).toBeUndefined();
    });

    it('should handle ngOnInit DOM manipulation', () => {
      const mockElement = { classList: { add: jest.fn() } };
      const mockCustomTable = document.createElement('div');
      const mockAddEventListener = jest.fn();

      jest.spyOn(document, 'addEventListener').mockImplementation(mockAddEventListener);
      jest.spyOn(document, 'querySelector')
        .mockReturnValueOnce(mockCustomTable)
        .mockReturnValueOnce(mockElement as any);

      component.ngOnInit();

      expect(mockAddEventListener).toHaveBeenCalledWith('DOMContentLoaded', expect.any(Function));

      // Simulate the DOMContentLoaded event
      const eventHandler = mockAddEventListener.mock.calls[0][1];
      eventHandler();

      expect(mockElement.classList.add).toHaveBeenCalledWith('custom-class');
    });

    it('should handle ngOnInit when elements not found', () => {
      const mockAddEventListener = jest.fn();
      jest.spyOn(document, 'addEventListener').mockImplementation(mockAddEventListener);
      jest.spyOn(document, 'querySelector').mockReturnValue(null);

      // Should not throw error
      expect(() => component.ngOnInit()).not.toThrow();
      expect(mockAddEventListener).toHaveBeenCalledWith('DOMContentLoaded', expect.any(Function));

      // Simulate the DOMContentLoaded event with null elements
      const eventHandler = mockAddEventListener.mock.calls[0][1];
      expect(() => eventHandler()).not.toThrow();
    });

    it('should handle document click with null target', () => {
      const event = new MouseEvent('click');
      Object.defineProperty(event, 'target', { value: null });

      // Should not throw error
      expect(() => component.onDocumentClick(event)).not.toThrow();
    });

    it('should handle document click inside modal', () => {
      const mockModal = { contains: jest.fn().mockReturnValue(true) };
      jest.spyOn(document, 'getElementById').mockReturnValue(mockModal as any);

      const event = new MouseEvent('click');
      Object.defineProperty(event, 'target', { value: document.createElement('div') });

      component.onDocumentClick(event);

      // Dropdowns should remain open when clicking inside modal
      expect(mockModal.contains).toHaveBeenCalled();
    });

    it('should handle fetchCraneReports with null response', () => {
      reportsServiceSpy.craneReports.mockReturnValue(of(null));
      jest.spyOn(component, 'getOverAllGateForNdrGrid');

      component.fetchCraneReports({}, {});

      expect(component.getOverAllGateForNdrGrid).toHaveBeenCalled();
    });

    it('should handle service errors gracefully', () => {
      const error = new Error('Service error');
      reportsServiceSpy.craneReports.mockReturnValue(of(error));

      // Should not throw error
      expect(() => component.getCraneReports()).not.toThrow();
    });
  });

  describe('Constructor and Initialization', () => {
    it('should initialize component with correct default values', () => {
      expect(component.seletedValue).toBe('PDF');
      expect(component.seletedValue1).toBe('Crane Report');
      expect(component.activeHeaders.length).toBeGreaterThan(0);
      expect(component.selectedRecurrence).toBe('Does Not Repeat');
      expect(component.reportType).toBe('PDF');
      expect(component.reportName).toBe('Crane Report');
    });

    it('should handle project service subscription', () => {
      const mockProject = {
        ProjectId: 'test-project-123',
        ParentCompanyId: 'test-company-456'
      };

      // Spy on methods before triggering the subscription
      jest.spyOn(component, 'filterDetailsForm');
      jest.spyOn(component, 'getCraneReports');
      jest.spyOn(component, 'exportDetailsForm');
      jest.spyOn(component, 'getMembers');

      // Trigger the subscription
      projectServiceSpy.projectParent.next(mockProject);

      // Verify the component properties are updated
      expect(component.ProjectId).toBe('test-project-123');
      expect(component.ParentCompanyId).toBe('test-company-456');

      // Verify the methods are called
      expect(component.filterDetailsForm).toHaveBeenCalled();
      expect(component.getCraneReports).toHaveBeenCalled();
      expect(component.exportDetailsForm).toHaveBeenCalled();
      expect(component.getMembers).toHaveBeenCalled();
    });

    it('should initialize active headers from table headers', () => {
      const activeHeadersCount = Object.values(component.tableHeaders).filter(
        (header: any) => header.isActive === true
      ).length;

      expect(component.activeHeaders.length).toBe(activeHeadersCount);
      expect(component.activeHeaders.length).toBeGreaterThan(0);
    });
  });
});


