import { TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { BillingService } from './billing.service';
import { ApiService } from '../api_base/api.service';
import { of, throwError } from 'rxjs';

describe('BillingService', (): void => {
  let service: BillingService;
  let apiServiceMock: jest.Mocked<ApiService>;

  beforeEach((): void => {
    // Create mock for ApiService
    apiServiceMock = {
      get: jest.fn(),
      post: jest.fn()
    } as unknown as jest.Mocked<ApiService>;

    TestBed.configureTestingModule({
      imports: [
        RouterTestingModule.withRoutes([]),
        HttpClientTestingModule,
      ],
      providers: [
        BillingService,
        { provide: ApiService, useValue: apiServiceMock }
      ]
    });

    service = TestBed.inject(BillingService);
  });

  it('should be created', (): void => {
    expect(service).toBeTruthy();
  });

  describe('getBilling', (): void => {
    it('should call get with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ParentCompanyId: 456,
        pageSize: 10,
        pageNo: 1
      };

      const mockResponse = {
        data: {
          rows: [
            { id: 1, amount: 100, status: 'Pending', dueDate: '2023-05-01' },
            { id: 2, amount: 200, status: 'Paid', dueDate: '2023-04-01' }
          ],
          count: 2,
          nextPayDet: {
            amount: 100,
            dueDate: '2023-05-01'
          }
        }
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getBilling(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`billing/listBilling?ParentCompanyId=${mockParams.ParentCompanyId}`);
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty params', (): void => {
      // Arrange
      const mockParams = {};
      const mockResponse = { data: { rows: [], count: 0 } };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getBilling(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith('billing/listBilling?ParentCompanyId=undefined');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('payOffline', (): void => {
    it('should call post with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ParentCompanyId: 456
      };

      const mockPayload = {
        BillingId: 1,
        paymentMethod: 'Check',
        checkNumber: '12345',
        amount: 100,
        notes: 'Payment for services'
      };

      const mockResponse = {
        success: true,
        message: 'Payment recorded successfully'
      };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.payOffline(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(`billing/payOffline?ParentCompanyId=${mockParams.ParentCompanyId}`, mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty params', (): void => {
      // Arrange
      const mockParams = {};
      const mockPayload = { BillingId: 1 };
      const mockResponse = { success: false, message: 'Missing ParentCompanyId' };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.payOffline(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('billing/payOffline?ParentCompanyId=undefined', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('payOnline', (): void => {
    it('should call post with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ParentCompanyId: 456
      };

      const mockPayload = {
        BillingId: 1,
        paymentMethod: 'CreditCard',
        cardNumber: '**** **** **** 1234',
        amount: 100,
        cardholderName: 'John Doe'
      };

      const mockResponse = {
        success: true,
        message: 'Payment processed successfully',
        data: {
          transactionId: 'txn_123456',
          status: 'Completed'
        }
      };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.payOnline(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(`billing/payOnline?ParentCompanyId=${mockParams.ParentCompanyId}`, mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should handle empty params', (): void => {
      // Arrange
      const mockParams = {};
      const mockPayload = { BillingId: 1 };
      const mockResponse = { success: false, message: 'Missing ParentCompanyId' };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.payOnline(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('billing/payOnline?ParentCompanyId=undefined', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Error handling', (): void => {
    it('should propagate errors from the API service in getBilling', (done): void => {
      // Arrange
      const mockParams = { ParentCompanyId: 456 };
      const mockError = 'API Error';

      apiServiceMock.get.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.getBilling(mockParams).subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(mockError);
          done();
        }
      });
    });

    it('should propagate errors from the API service in payOffline', (done): void => {
      // Arrange
      const mockParams = { ParentCompanyId: 456 };
      const mockPayload = { amount: 100 };
      const mockError = 'API Error';

      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.payOffline(mockParams, mockPayload).subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(mockError);
          done();
        }
      });
    });

    it('should propagate errors from the API service in payOnline', (done): void => {
      // Arrange
      const mockParams = { ParentCompanyId: 456 };
      const mockPayload = { amount: 100 };
      const mockError = 'API Error';

      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.payOnline(mockParams, mockPayload).subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(mockError);
          done();
        }
      });
    });
  });

  describe('Service dependencies', (): void => {
    it('should have ApiService injected', (): void => {
      // This test verifies that the ApiService is properly injected
      expect(TestBed.inject(ApiService)).toBeDefined();
    });
  });

  describe('Integration with BillingComponent', (): void => {
    it('should provide data in the format expected by BillingComponent', (): void => {
      // Arrange
      const mockParams = {
        ParentCompanyId: 456,
        pageSize: 25,
        pageNo: 1
      };

      const mockResponse = {
        data: {
          rows: [
            {
              id: 1,
              amount: 100,
              status: 'Pending',
              dueDate: '2023-05-01',
              description: 'Monthly subscription',
              invoiceNumber: 'INV-2023-001'
            }
          ],
          count: 1,
          nextPayDet: {
            amount: 100,
            dueDate: '2023-05-01',
            id: 1
          }
        }
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getBilling(mockParams).subscribe(response => {
        result = response;
      });

      // Assert - verify the structure matches what's expected in the component
      expect(result.data).toHaveProperty('rows');
      expect(result.data).toHaveProperty('count');
      expect(result.data).toHaveProperty('nextPayDet');
      expect(result.data.rows[0]).toHaveProperty('id');
      expect(result.data.rows[0]).toHaveProperty('amount');
      expect(result.data.rows[0]).toHaveProperty('status');
      expect(result.data.rows[0]).toHaveProperty('dueDate');
      expect(result.data.nextPayDet).toHaveProperty('amount');
      expect(result.data.nextPayDet).toHaveProperty('dueDate');
      expect(result.data.nextPayDet).toHaveProperty('id');
    });
  });
});
