import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from '../api_base/api.service';

@Injectable({
  providedIn: 'root',
  })
export class DashboardService {
  public constructor(private readonly api: ApiService) {
    // constructor
  }

  public getDashboardData(payload): Observable<any> {
    return this.api.post('dashboard/get_dashboard_detail', payload);
  }

  public getUpcomingDelivery(params): Observable<any> {
    return this.api.get(`dashboard/get_upcoming_delivery/${params.pageSize}/${params.pageNo}/${params.ParentCompanyId}`);
  }

  public getGraphDelivery(params, payload): Observable<any> {
    return this.api.post(`dashboard/get_graph_detail/${params.ParentCompanyId}`, payload);
  }

  public getCraneRequestGraphData(params, payload): Observable<any> {
    return this.api.post(`dashboard/get_crane_graph_data/${params.ParentCompanyId}`, payload);
  }

  public getConcreteRequestGraphData(params, payload): Observable<any> {
    return this.api.post(`dashboard/get_concrete_graph_data/${params.ParentCompanyId}`, payload);
  }

  public getInspectionRequestGraphData(params, payload): Observable<any> {
    return this.api.post(`dashboard/get_inspection_graph_data/${params.ParentCompanyId}`, payload);
  }
}
