import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from '../services/auth/auth.service';
import { ProjectService } from '../services/profile/project.service';
import { CustomValidators } from '../custom-validators';

@Component({
  selector: 'app-change-password',
  templateUrl: './change-password.component.html',
  })
export class ChangePasswordComponent implements OnInit {
  public changePasswordForm: UntypedFormGroup;

  public changeFormSubmitted = false;

  public togglePassword = false;

  public newTogglePassword = false;

  public formSubmitted = false;

  public confirmTogglePassword = false;

  public ParentCompanyId;

  public passwordError: boolean = true;

  public lowerpasswordError: boolean = false;

  public numericpasswordError: boolean = false;

  public minlengthpasswordError: boolean = false;

  public constructor(
    private readonly fb: UntypedFormBuilder,
    private readonly api: AuthService,
    public toastr: ToastrService,
    private readonly projectService: ProjectService,
  ) {
    // constructor
    this.projectService.ParentCompanyId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ParentCompanyId = res;
      }
    });
  }

  public ngOnInit(): void {
    this.createChangePasswordForm();
  }

  public passwordValid(value): any {
    const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[#?!@$%^&*-])[A-Za-z\d#?!@$%^&*-]{8,30}$/gm;
    if (regex.exec(value.target.value) === null) {
      this.passwordError = true;
    } else {
      this.passwordError = false;
    }
  }

  public changePassword(): void {
    this.changeFormSubmitted = true;
    this.formSubmitted = true;
    if (this.changePasswordForm.invalid) {
      this.formSubmitted = false;
      return;
    }
    const formValue = this.changePasswordForm.value;
    const payload: any = {
      oldPassword: formValue.currentPassword,
      newPassword: formValue.newPassword,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.api.changePassword(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.changeFormSubmitted = false;
          this.formSubmitted = false;
          this.changePasswordForm.reset();
          this.api.logout();
        }
      },
      error: (changePasswordError): void => {
        this.changeFormSubmitted = false;
        this.formSubmitted = false;
        if (changePasswordError.message?.statusCode === 400) {
          this.showError(changePasswordError);
          this.formSubmitted = false;
        } else if (!changePasswordError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(changePasswordError.message, 'OOPS!');
        }
      },
    });
  }

  public passwordToggle(): void {
    this.togglePassword = !this.togglePassword;
  }

  public newPasswordToggle(): void {
    this.newTogglePassword = !this.newTogglePassword;
  }

  public confirmPasswordToggle(): void {
    this.confirmTogglePassword = !this.confirmTogglePassword;
  }

  public handleDownKeydown(event: KeyboardEvent, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'cpassword':
          this.confirmPasswordToggle();
          break;
        case 'newpassword':
          this.newPasswordToggle();
          break;
        case 'password':
          this.passwordToggle();
          break;
        default:
          break;
      }
    }
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.changeFormSubmitted = false;
    this.toastr.error(errorMessage);
  }

  private createChangePasswordForm(): void {
    const nonWhitespaceRegExp = /\S/;
    this.changePasswordForm = this.fb.group( // NOSONAR
      {
        currentPassword: ['', Validators.required],
        newPassword: [
          null,
          Validators.compose([
            Validators.required,
            Validators.pattern(nonWhitespaceRegExp),
            CustomValidators.patternValidator(/\d/, {
              hasNumber: true,
            }),
            CustomValidators.patternValidator(/[A-Z]/, {
              hasCapitalCase: true,
            }),
            CustomValidators.patternValidator(/[a-z]/, {
              hasSmallCase: true,
            }),
            CustomValidators.patternValidator(/[ !@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/, {
              hasSpecialCharacters: true,
            }),
            Validators.minLength(8),
          ]),
        ],
        confirmPassword: [
          '',
          Validators.compose([Validators.required, Validators.pattern(nonWhitespaceRegExp)]),
        ],
      },
      {
        validator: this.MustMatch('newPassword', 'confirmPassword'),
      },
    );
  }

  private MustMatch(controlName: string, matchingControlName: string): any {
    return (formGroup: UntypedFormGroup): any => {
      const control = formGroup.controls[controlName];
      const matchingControl = formGroup.controls[matchingControlName];
      if (matchingControl.errors && !matchingControl.errors.mustMatch) {
        return;
      }
      if (control.value !== matchingControl.value) {
        matchingControl.setErrors({ mustMatch: true });
      } else {
        matchingControl.setErrors(null);
      }
    };
  }
}
