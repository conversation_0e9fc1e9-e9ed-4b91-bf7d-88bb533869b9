import { Component, Input, TemplateRef } from '@angular/core';
import moment from 'moment';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { ProjectService } from '../../services/profile/project.service';
import { ReportsService } from '../../services/reports/reports.service';
import { DeliveryService } from '../../services/profile/delivery.service';

@Component({
  selector: 'app-scheduled-reports',
  templateUrl: './scheduled-reports.component.html',
  })
export class ScheduledReportsComponent {
  @Input() public filterValues: any;

  @Input() public getSearchText: any;

  public ProjectId: string | number;

  public ParentCompanyId: string | number;

  public loader = true;

  public scheduleReports: any = [];

  public pageSize = 25;

  public pageNo = 1;

  public sortColumn = 'id';

  public sort = 'DESC';

  public modalRef: BsModalRef;

  public filterCount = 0;

  public deleteSchedulerReportData: any = {};

  public deleteSchedulerReportSubmitted = false;

  public count = 0;

  public authUser: any = {};

  public constructor(
    public projectService: ProjectService,
    private readonly reportsService: ReportsService,
    private readonly modalService: BsModalService,
    private readonly toastr: ToastrService,
    private readonly deliveryService: DeliveryService,
  ) {
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
      }
    });
    this.projectService.projectParent.subscribe((response19): void => {
      if (response19 !== undefined && response19 !== null && response19 !== '') {
        this.ProjectId = response19.ProjectId;
        this.getScheduleReports();
      }
    });
  }

  public ngOnChanges(): void {
    this.getScheduleReports();
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'download':
          this.downloadScheduledReport(data, item);
          break;
        case 'delete':
          this.openDeleteModal(data, item);
          break;
        case 'auto':
          this.autoDownloadReportWithRunOption(data);
          break;
        default:
          break;
      }
    }
  }

  public sortByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getScheduleReports();
  }

  public changePageNo(pageNo: number): void {
    this.pageNo = pageNo;
    this.getScheduleReports();
  }

  public changePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.getScheduleReports();
  }

  public openModal(template: TemplateRef<any>): void {
    this.modalRef = this.modalService.show(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-sm filter-popup custom-modal',
    });
  }

  public openDeleteModal(deleteData: any, template: TemplateRef<any>): void {
    this.deleteSchedulerReportData = {};
    this.deleteSchedulerReportData = deleteData;
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-delivery-popup custom-modal',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public downloadScheduledReport(data, action): void {
    if (data && action === 'lastRun' && !data.lastRun && !data.s3_url) {
      return;
    }
    if ((data && action === 'reRun') || (data?.s3_url && action === 'lastRun')) {
      const link = document.createElement('a');
      link.setAttribute('target', '_self');
      if (action === 'reRun') {
        link.setAttribute('href', data);
      } else {
        link.setAttribute('href', data.s3_url);
      }
      document.body.appendChild(link);
      link.click();
      link.remove();
      this.toastr.success('Report downloaded successfully');
    } else {
      this.toastr.info('There are no events available within the scheduled date range');
    }
  }

  public getScheduleReports(): void {
    this.loader = true;
    this.scheduleReports = [];

    if (!this.ProjectId) return;

    const payload = this.buildPayload();

    this.reportsService.getScheduledReports(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.scheduleReports = response.data.scheduledReports;
          this.count = response.data.count;
        }
        this.loader = false;
      },
      error: (error): void => {
        this.loader = false;
        if (error.message?.statusCode === 400) {
          this.showError(error);
        } else if (!error.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(error.message, 'OOPS!');
        }
      }
    });
  }

  public buildPayload(): any {
    const payload: any = {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.pageNo,
      sort: this.sort,
      sortByField: this.sortColumn,
      search: this.getSearchText || '',
    };

    if (!this.filterValues) return payload;

    const {
      createdUserId, reportName, templateType, lastRun,
    } = this.filterValues;

    if (createdUserId) payload.createdUserId = createdUserId;
    if (reportName) payload.reportName = reportName;
    if (templateType) payload.templateType = templateType;
    if (lastRun) {
      payload.lastRun = moment(lastRun).format('YYYY-MM-DD');
      payload.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    }

    return payload;
  }


  public autoDownloadReportWithRunOption(data): void {
    const selectedRow = data;
    selectedRow.runNowLoader = true;

    this.reportsService.runNowScheduledOrSavedReport({
      id: data.id,
      ProjectId: this.ProjectId,
    }).subscribe({
      next: (response: any): void => {
        if (response) {
          const regex = /^https:\/\/\S+\.(pdf|xlsx|csv)$/;
          if (response.data && response.data === 'No data found') {
            this.toastr.info('There are no events available within the scheduled date range');
            selectedRow.runNowLoader = false;
            return;
          }
          if (!regex.test(response.data.trim())) {
            this.toastr.error('Something went wrong.');
            selectedRow.runNowLoader = false;
            return;
          }
          this.downloadScheduledReport(response.data, 'reRun');
          selectedRow.runNowLoader = false;
        }
      },
      error: (runNowScheduledReportError): void => {
        selectedRow.runNowLoader = false;
        if (runNowScheduledReportError.message?.statusCode === 400) {
          this.showError(runNowScheduledReportError);
        } else if (!runNowScheduledReportError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(runNowScheduledReportError.message, 'OOPS!');
        }
      },
    });
  }

  public deleteScheduleReport(): void {
    this.deleteSchedulerReportSubmitted = true;
    this.reportsService
      .deleteScheduledReports({
        id: this.deleteSchedulerReportData.id,
        ProjectId: this.ProjectId,
      })
      .subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.getScheduleReports();
            this.deleteSchedulerReportData = {};
            this.deleteSchedulerReportSubmitted = false;
            this.modalRef.hide();
          }
        },
        error: (deleteConcreteRequestError): void => {
          this.deleteSchedulerReportSubmitted = false;
          if (deleteConcreteRequestError.message?.statusCode === 400) {
            this.showError(deleteConcreteRequestError);
          } else if (!deleteConcreteRequestError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deleteConcreteRequestError.message, 'OOPS!');
          }
        },
      });
  }

  public resetAndClose(): void {
    this.modalRef.hide();
    this.deleteSchedulerReportData = {};
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }
}
