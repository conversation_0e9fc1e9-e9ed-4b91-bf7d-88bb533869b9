import { Component, TemplateRef, OnInit } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Title } from '@angular/platform-browser';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { ProjectSharingService } from '../../services/projectSharingService/project-sharing.service';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { countryCodes } from '../../services/countryCodes';
import { countryNames } from '../../services/countryNames';

@Component({
  selector: 'app-guestmembers',
  templateUrl: './guestmembers.component.html'
  })
export class GuestmembersComponent implements OnInit {
  public showSearchbar = false;

  public filterCount = 0;

  public filterModalLoader = false;

  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public memberList: any = [];

  public loader = true;

  public pageSize = 25;

  public pageNo = 1;

  public totalCount = 0;

  public currentPageNo = 1;

  public ProjectId: number;

  public ParentCompanyId: number;

  public userData: any = {};

  public isAdmin = false;

  public filterForm: UntypedFormGroup;

  public search = '';

  public sort = 'DESC';

  public sortColumn = 'id';

  public modalLoader = false;

  public createNewModalLoader = false;

  public companyList: any = [];

  public companyListForFilter: any = [];

  public countryCode = [];

  public roleList: any = [];


  public responseData: any;

  public addGuestLoader: boolean=false;

  public config = {
    displayKey: 'companyName',
    search: true,
    height: 'auto',
    placeholder: 'Company',
    customComparator: (): void => {},
    limitTo: 0,
    moreText: 'more',
    noResultsFound: 'No results found!',
    searchPlaceholder: 'Search',
    searchOnKey: 'companyName',
    clearOnSelection: false,
    inputDirection: 'ltr',
  };

  public wholeStatus = ['Declined', 'Pending'];

  public constructor(
    private readonly modalService: BsModalService,
    private readonly titleService: Title,
    public projectService: ProjectService,
    public deliveryService: DeliveryService,
    public projectSharingService: ProjectSharingService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly toastr: ToastrService,
  ) {
    this.titleService.setTitle('Follo - Members');
    this.projectService.projectParent.subscribe((response11): void => {
      this.ProjectId = response11.ProjectId;
      this.ParentCompanyId = response11.ParentCompanyId;
      if (
        this.ProjectId !== undefined
        && this.ProjectId !== null
        && this.ProjectId > 0
        && this.ParentCompanyId !== undefined
        && this.ParentCompanyId !== null
        && this.ParentCompanyId > 0
      ) {
        this.loader = true;
        this.getMembers();
        this.getCompanies();
        this.getMembers();
      }
    });
    this.projectService.userData.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.userData = res;
        if (
          this.userData.RoleId === 2
          || this.userData.RoleId === 1
          || this.userData.RoleId === 3
        ) {
          this.isAdmin = true;
        }
      }
    });
    this.getCountryCode();
    this.getRoles();
    this.filterDetailsForm();
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'approved':
          this.addGuestAsMember(data);
          break;
        case 'declined':
          this.confirmationDecline(data, item);
          break;
        case 'auto':
          this.openModal1(data);
          break;
        default:
          break;
      }
    }
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      companyFilter: [''],
      nameFilter: [''],
      statusFilter: [''],
    });
  }

  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg members-popup custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public openModal1(template: TemplateRef<any>): void {
    this.filterModalLoader = true;
    setTimeout((): void => {
      this.modalRef = this.modalService.show(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-sm filter-popup custom-modal',
      });
    }, 100);
  }

  public setCompany(template: TemplateRef<any>): void {
    this.openModal(template);
  }

  ngOnInit(): void { /* */ }

  public changePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.getMembers();
  }

  public getMembers(): void {
    this.loader = true;
    const param = {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.currentPageNo,
      ParentCompanyId: this.ParentCompanyId,
    };
    let payload: any = {};
    if (this.filterForm !== undefined) {
      payload = {
        companyFilter: this.filterForm.value.companyFilter,
        nameFilter: this.filterForm.value.nameFilter,
        statusFilter: this.filterForm.value.statusFilter ? this.filterForm.value.statusFilter.toLowerCase() : this.filterForm.value.statusFilter,
        search: this.search,
      };
    }
    payload.search = this.search;
    payload.sort = this.sort;
    payload.sortByField = this.sortColumn;
    this.projectService.listGuestMembers(param, payload).subscribe((response: any): void => {
      if (response) {
        const responseData = response.data;
        this.loader = false;
        this.memberList = responseData.rows;
        this.totalCount = responseData.count;
      }
    });
  }

  public getCompanies(): void {
    if (this.modalLoader === false
      && this.createNewModalLoader === false
      && this.filterModalLoader === false
    ) {
      this.loader = true;
    }
    const param = {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.currentPageNo,
      ParentCompanyId: this.ParentCompanyId,
    };
    const payload = { inviteMember: true };
    this.projectService.listCompany(param, payload).subscribe((response: any): void => {
      if (response) {
        this.companyList = [];
        this.companyListForFilter = [];
        const responseData = response.data;
        this.loader = false;
        responseData.rows.map((x: { companyName: any; id: any }): any => {
          if (x.companyName) {
            this.companyList.push({ id: x.id, companyName: x.companyName });
          }
          return null;
        });
        this.companyList.push({
          id: response?.parentCompany[0]?.id,
          companyName: response?.parentCompany[0]?.companyName,
        });
        const newCompanyArray = this.companyList.reduce(
          (arr: any[], item: { companyName: string }): any => {
            const exists = !!arr.find(
              (x: { companyName: string }): any => x.companyName.toLowerCase() === item.companyName.toLowerCase(),
            );
            if (!exists) {
              arr.push(item);
            }
            return arr;
          },
          [],
        );
        this.companyList = newCompanyArray;
        this.companyList.sort(
          (
            a: { companyName: { toLowerCase: () => number } },
            b: { companyName: { toLowerCase: () => number } },
          ): any => (a.companyName.toLowerCase() > b.companyName.toLowerCase() ? 1 : -1),
        );
        responseData.rows.map((x: { companyName: any }): any => {
          if (x.companyName) {
            this.companyListForFilter.push(x.companyName);
          }
          return null;
        });
        this.companyListForFilter.push(response?.parentCompany[0]?.companyName);
        const newCompanyFilterArray = this.companyListForFilter.reduce(
          (arr: any[], item: string): any => {
            const exists = !!arr.find((x: string): any => x.toLowerCase() === item.toLowerCase());
            if (!exists) {
              arr.push(item);
            }
            return arr;
          },
          [],
        );
        this.companyListForFilter = newCompanyFilterArray;
        this.companyListForFilter.sort(
          (a: { toLowerCase: () => number }, b: { toLowerCase: () => number }): any => (a.toLowerCase() > b.toLowerCase() ? 1 : -1),
        );
        this.filterModalLoader = false;
      }
    });
  }

  public getCountryCode(): void {
    Object.keys(countryCodes).forEach((key): void => {
      const countryName = countryNames[key];
      this.countryCode.push({ countryDialCode: key, name: countryName });
    });
    this.countryCode.sort((a, b): number => (a.name > b.name ? 1 : -1));
  }

  public changePageNo(pageNo: number): void {
    this.currentPageNo = pageNo;
    this.getMembers();
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('companyFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('nameFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('statusFilter').value !== '') {
      this.filterCount += 1;
    }
    this.pageNo = 1;
    this.getMembers();
    this.modalRef.hide();
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.search = '';
    this.pageNo = 1;
    this.filterDetailsForm();
    this.getMembers();
    this.modalRef.hide();
  }

  public getRoles(): void {
    this.projectService.getRoles().subscribe((response: any): void => {
      if (response) {
        this.roleList = response.data;
      }
    });
  }

  public onRoleChange(item: any, selectedRole: any): void {
    item.selectedRoleId = selectedRole;
  }

  public sortByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getMembers();
  }

  public getSearchMember(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.pageNo = 1;
    this.search = data;
    this.getMembers();
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.pageNo = 1;
    this.getMembers();
  }

  public addGuestAsMember(data: any): void {
    if (!data.selectedRoleId) {
      this.toastr.error('Please select role');
    } else {
      const payload = {
        ProjectId: +data.ProjectId,
        ParentCompanyId: +data.ParentCompanyId,
        guestUserId: +data.UserId,
        guestId: +data.id,
        guestEmail: data.User.email,
        selectedRoleId: +data.selectedRoleId,
        guestFirstName: data.User.firstName,
        guestLastName: data.User.lastName,
        memberId: +this.userData.User.id,
        memberFirstName: this.userData.User.firstName,
        memberLastName: this.userData.User.lastName,
      };
      this.addGuestLoader = true;
      this.projectSharingService.addGuestAsMember(payload).subscribe({
        next: (res): void => {
          if (res) {
            this.toastr.success('Guest User approved successfully.You can see them in Members list');
            this.getMembers();
          }
        },
        error: (error): void => {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        },
      });
      this.addGuestLoader = false;
    }
  }

  public confirmationDecline(template: TemplateRef<any>, item: any): void {
    this.responseData = item;
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else if (this.modalRef1) {
      this.modalRef1.hide();
      const payload = {
        ProjectId: +this.responseData.ProjectId,
        ParentCompanyId: +this.responseData.ParentCompanyId,
        guestUserId: +this.responseData.UserId,
        guestId: +this.responseData.id,
        guestEmail: this.responseData.User.email,
        selectedRoleId: +this.responseData.selectedRoleId,
        guestFirstName: this.responseData.User.firstName,
        guestLastName: this.responseData.User.lastName,
        memberId: +this.userData.User.id,
        memberFirstName: this.userData.User.firstName,
        memberLastName: this.userData.User.lastName,
      };
      this.projectSharingService.rejectGuestRequest(payload).subscribe({
        next: (res): void => {
          this.toastr.success('Guest Declined Successfully');
        },
        error: (error): void => {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        },
      });
      this.getMembers();
    }
  }
}
