<div class="plan-card pt40">
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-1">
        <button
          class="btn btn-orange fs14 fw-bold cairo-regular shadow-none mb-3 mb-md-0"
          (click)="redirect('register-project')"
          *ngIf="!upgradePlans"
        >
          Back
        </button>
        <button
          class="btn btn-orange fs14 fw-bold cairo-regular shadow-none mb-3 mb-md-0"
          (click)="redirect('profile')"
          *ngIf="upgradePlans"
        >
          Back
        </button>
      </div>
      <div class="col-md-11 text-center">
        <h1 class="fw-bold cairo-regular fs25" *ngIf="!upgradePlans">
          Choose the appropriate plan for you
        </h1>
        <h1 class="fw-bold cairo-regular fs25" *ngIf="upgradePlans">Upgrade the plan</h1>
      </div>
    </div>
  </div>
  <div class="text-center pt-5">
    <div class="d-inline-flex align-items-baseline">
      <p class="fw-bold cairo-regular fs12 mr5">Billed Monthly</p>
      <div class="form-check form-switch switch-toggle">
        <input class="form-check-input" type="checkbox" role="switch"
        id="flexSwitchCheckChecked"
        (change)="checkChange()"
        [(ngModel)]="billed"
        [ngModelOptions]="{ standalone: true }"
        [checked]="billed"
        aria-checked="{{ billed ? 'true' : 'false' }}">
        <label class="form-check-label fw-bold cairo-regular fs12 plan-bill" for="flexSwitchCheckChecked">Billed Annual</label>
      </div>
    </div>
  </div>
  <div class="container-fluid">
    <div class="row">
      <div class="col-xl-9 col-md-10 mx-auto">
        <div class="row" *ngIf="!loader">
          <div
            class="col-sm-12 col-md-6 col-lg-4 mb-3"
            *ngFor="let item of planList; let i = index"
          >
            <div
              class="card h-100 radius20 price-shadow pb-3"
              [ngClass]="{ 'enterprise-plan': i === 1 || i === 2 ? true : false }"
              [ngStyle]="{ 'background-color': item.id === selectedPlanId ? 'lightblue' : '' }"
            >
              <div class="card-body text-center pt55 radius20" (click)="highLightPlan(item)" (keydown)="handleDownKeydown($event, item)">
                <h2 class="fw-bold cairo-regular fs20">
                  <span>{{ item.Plan.planType?.slice(0, 3) }}</span
                  >{{ item.Plan.planType?.slice(3) }}
                </h2>
                <div class="mt50 px-0">
                  <ul class="list-group fs13 plan-info" *ngIf="item.stripePlanName == 'free'">
                    <li class="list-group-item plan-list-items border-0 p-0 bg-transparent d-flex">
                      <img
                        src="../../assets/images/tick-orange.svg"
                        class="img-fluid h18"
                        alt="tick-orange"
                      />
                      <span class="ps-2">All Project Features</span>
                    </li>
                    <li class="list-group-item plan-list-items border-0 p-0 bg-transparent d-flex">
                      <img
                        src="../../assets/images/tick-orange.svg"
                        class="img-fluid h18"
                        alt="tick-orange"
                      />
                      <span class="ps-2">Free for 14 days</span>
                    </li>
                  </ul>
                  <ul
                    class="list-group fs13 plan-info"
                    *ngIf="item.Plan.planType.toLowerCase() == 'project plan'"
                  >
                    <li class="list-group-item plan-list-items border-0 p-0 bg-transparent d-flex">
                      <img
                        src="../../assets/images/tick-orange.svg"
                        class="img-fluid h18"
                        alt="tick-orange"
                      />
                      <span class="ps-2">Unlimited Delivery Bookings</span>
                    </li>
                    <li class="list-group-item plan-list-items border-0 p-0 bg-transparent d-flex">
                      <img
                        src="../../assets/images/tick-orange.svg"
                        class="img-fluid h18"
                        alt="tick-orange"
                      />
                      <span class="ps-2">Unlimited Users</span>
                    </li>
                    <li
                      class="list-group-item plan-list-items border-0 p-0 bg-transparent d-flex pt-2"
                    >
                      <img
                        src="../../assets/images/tick-orange.svg"
                        class="img-fluid pricing-image"
                        alt="tick-orange"
                      />
                      <span class="ps-2 lh13">
                        State of the art analytic tools for the project</span
                      >
                    </li>
                  </ul>
                  <ul
                    class="list-group fs13 plan-info"
                    *ngIf="item.Plan.planType.toLowerCase() == 'enterprise plan'"
                  >
                    <li class="list-group-item plan-list-items border-0 p-0 bg-transparent d-flex">
                      <img
                        src="../../assets/images/tick-orange.svg"
                        class="img-fluid h18"
                        alt="tick-orange"
                      />
                      <span class="ps-2">Unlimited Projects</span>
                    </li>
                    <li class="list-group-item plan-list-items border-0 p-0 bg-transparent d-flex">
                      <img
                        src="../../assets/images/tick-orange.svg"
                        class="img-fluid h18"
                        alt="tick-orange"
                      />
                      <span class="ps-2">Unlimited Users</span>
                    </li>
                    <li class="list-group-item plan-list-items border-0 p-0 bg-transparent d-flex">
                      <img
                        src="../../assets/images/tick-orange.svg"
                        class="img-fluid h18"
                        alt="tick-orange"
                      />
                      <span class="ps-2">Enterprise Level Analytics</span>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="card-footer bg-transparent border-0 text-center py-3">
                <p
                  class="fw-bold cairo-regular"
                  *ngIf="item.Plan.planType.toLowerCase() == 'project plan'"
                >
                  $ {{ item.stripeAmount / 100 }}
                </p>
                <p
                  class="fw-bold cairo-regular text-capitalize"
                  *ngIf="item.stripePlanName === 'yearly'"
                >
                  Annual
                </p>
                <p
                  class="fw-bold cairo-regular text-capitalize"
                  *ngIf="item.stripePlanName !== 'yearly'"
                >
                  {{ item.stripePlanName }}
                </p>
                <button
                  class="btn btn-orange radius20 fs14 fw-bold cairo-regular contact-sale-btn"
                  *ngIf="!loggedIn"
                  (click)="SelectedPlan(i)"
                  [disabled]="formSubmitted"
                >
                  <em
                    class="fa fa-spinner"
                    aria-hidden="true"
                    *ngIf="formSubmitted && i == selectedIndex"
                  ></em
                  >Get Started
                </button>
                <button
                  class="btn btn-orange radius20 fs14 fw-bold cairo-regular mt-4 contact-sale-btn"
                  *ngIf="loggedIn && !upgradePlans"
                  (click)="SelectedPlan(i)"
                  [disabled]="formSubmitted"
                >
                  <em
                    class="fa fa-spinner"
                    aria-hidden="true"
                    *ngIf="formSubmitted && i == selectedIndex"
                  ></em
                  >Create Project
                </button>
                <button
                  class="btn btn-orange radius20 fs14 fw-bold cairo-regular mt-4 contact-sale-btn"
                  *ngIf="loggedIn && upgradePlans"
                  (click)="SelectedPlan(i)"
                  [disabled]="formSubmitted"
                >
                  <em
                    class="fa fa-spinner"
                    aria-hidden="true"
                    *ngIf="formSubmitted && i == selectedIndex"
                  ></em
                  >Upgrade
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="row text-center" *ngIf="loader">Loading....</div>
      </div>
    </div>
  </div>
</div>
<ng-template #thanks>
  <div class="modal-body">
    <div class="text-center my-4 col-md-11 mx-auto">
      <div class="success-tick">
        <em class="fa fa-check fa-2x text-black tick-icon"></em>
      </div>
      <p class="color-grey4 fs12 mb-5">
        Thanks for getting started with Follo! We have sent you an email with your login
        information.
      </p>
      <p class="color-grey4 fs12 mb-5">
        Please visit your inbox to continue with your Follo account setup. Thank you again!
      </p>
      <button
        class="btn btn-orange radius20 fs12 color-orange fw-bold cairo-regular px-5"
        (click)="modalRef.hide()"
      >
        Close
      </button>
    </div>
  </div>
</ng-template>
<ng-template #projectsuccess>
  <div class="modal-body">
    <div class="text-center my-4 col-md-11 mx-auto">
      <div class="success-tick">
        <em class="fa fa-check fa-2x text-black tick-icon"></em>
      </div>
      <p class="color-grey4 fs12 mb-5">Thank you for subscribing to Follo!</p>
      <p class="color-grey4 fs12 mb-5">We appreciate your business.</p>
      <p class="color-grey4 fs12 mb-5">Please click on close to access your project.</p>
      <button
        class="btn btn-orange radius20 fs12 color-orange fw-bold cairo-regular px-5"
        (click)="modalRef.hide()"
      >
        Close
      </button>
    </div>
  </div>
</ng-template>
<ng-template #refresh>
  <div class="modal-body">
    <div class="text-center my-4 col-md-11 mx-auto">
      <div class="spinner-border my-3 mx-auto"></div>
      *Processing your payment. Please do not refresh or reload the browser.
    </div>
  </div>
</ng-template>
