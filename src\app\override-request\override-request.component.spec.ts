import { ComponentFixture, TestBed } from '@angular/core/testing';
import { OverrideRequestComponent } from './override-request.component';
import { UntypedFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { ProjectService } from '../services/profile/project.service';
import { BehaviorSubject, of, throwError } from 'rxjs';

describe('OverrideRequestComponent', () => {
  let component: OverrideRequestComponent;
  let fixture: ComponentFixture<OverrideRequestComponent>;
  let projectServiceMock: any;
  let toastrServiceMock: any;
  let projectIdSubject: BehaviorSubject<any>;

  beforeEach(async () => {
    projectIdSubject = new BehaviorSubject<any>(null);
    projectServiceMock = {
      projectId: projectIdSubject.asObservable(),
      applyOverRide: jest.fn()
    };

    toastrServiceMock = {
      success: jest.fn(),
      error: jest.fn()
    };

    await TestBed.configureTestingModule({
      declarations: [ OverrideRequestComponent ],
      imports: [ ReactiveFormsModule ],
      providers: [
        UntypedFormBuilder,
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: ToastrService, useValue: toastrServiceMock }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(OverrideRequestComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the form with empty comment', () => {
    expect(component.overRideDetailsForm.get('comment').value).toBe('');
  });

  it('should update ProjectId when projectService emits new value', () => {
    const testProjectId = '123';
    projectIdSubject.next(testProjectId);
    expect(component.ProjectId).toBe(testProjectId);
  });

  it('should validate required comment field', () => {
    const commentControl = component.overRideDetailsForm.get('comment');
    expect(commentControl.valid).toBeFalsy();
    expect(commentControl.errors.required).toBeTruthy();
  });

  it('should check empty string values correctly', () => {
    const emptyFormValue = { comment: '   ' };
    const nonEmptyFormValue = { comment: 'test comment' };
    
    expect(component.checkStringEmptyValues(emptyFormValue)).toBeTruthy();
    expect(component.checkStringEmptyValues(nonEmptyFormValue)).toBeFalsy();
  });

  it('should not submit form when invalid', () => {
    component.onSubmit();
    expect(component.submitted).toBeTruthy();
    expect(component.formSubmitted).toBeFalsy();
    expect(projectServiceMock.applyOverRide).not.toHaveBeenCalled();
  });

  it('should handle successful form submission', () => {
    const testComment = 'test comment';
    const testProjectId = '123';
    const mockResponse = { message: 'Success message' };

    projectIdSubject.next(testProjectId);
    component.overRideDetailsForm.patchValue({ comment: testComment });
    projectServiceMock.applyOverRide.mockReturnValue(of(mockResponse));

    component.onSubmit();

    expect(projectServiceMock.applyOverRide).toHaveBeenCalledWith({
      comment: testComment,
      ProjectId: testProjectId
    });
    expect(toastrServiceMock.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
    expect(component.submitted).toBeFalsy();
    expect(component.formSubmitted).toBeFalsy();
    expect(component.overRideDetailsForm.get('comment').value).toBeNull();
  });

  it('should handle 400 error response', () => {
    const testComment = 'test comment';
    const errorResponse = {
      message: {
        statusCode: 400,
        details: [['Error message']]
      }
    };

    component.overRideDetailsForm.patchValue({ comment: testComment });
    projectServiceMock.applyOverRide.mockReturnValue(throwError(() => errorResponse));

    component.onSubmit();

    expect(toastrServiceMock.error).toHaveBeenCalledWith(['Error message']);
    expect(component.submitted).toBeFalsy();
    expect(component.formSubmitted).toBeFalsy();
  });

  it('should handle general error response', () => {
    const testComment = 'test comment';
    const errorResponse = {
      message: 'General error'
    };

    component.overRideDetailsForm.patchValue({ comment: testComment });
    projectServiceMock.applyOverRide.mockReturnValue(throwError(() => errorResponse));

    component.onSubmit();

    expect(toastrServiceMock.error).toHaveBeenCalledWith('General error', 'OOPS!');
    expect(component.submitted).toBeFalsy();
  });

  it('should show error for empty comment submission', () => {
    component.overRideDetailsForm.patchValue({ comment: '   ' });
    component.onSubmit();

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Please enter valid comment!', 'OOPS!');
    expect(component.submitted).toBeFalsy();
    expect(component.formSubmitted).toBeFalsy();
  });
});
