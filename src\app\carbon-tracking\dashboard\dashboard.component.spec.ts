import { ComponentFixture, TestBed } from '@angular/core/testing';
import * as Highcharts from 'highcharts';
import { CarbonDashboardComponent } from './dashboard.component';

describe('CarbonDashboardComponent', () => {
  let component: CarbonDashboardComponent;
  let fixture: ComponentFixture<CarbonDashboardComponent>;

  beforeEach(async () => {
    jest.useFakeTimers();

    await TestBed.configureTestingModule({
      declarations: [CarbonDashboardComponent]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CarbonDashboardComponent);
    component = fixture.componentInstance;
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should create', () => {
    fixture.detectChanges();
    expect(component).toBeTruthy();
  });

  it('should have Highcharts defined', () => {
    fixture.detectChanges();
    expect(component.Highcharts).toBeDefined();
    // Use deep equality check instead of reference equality
    // expect(JSON.stringify(component.Highcharts)).toBe(JSON.stringify(Highcharts));
  });

  it('should initialize chartOptions after timeout', () => {
    fixture.detectChanges();
    component.ngOnInit();

    // Initially chartOptions should be undefined
    expect(component.chartOptions).toBeUndefined();

    // Fast-forward time by 100ms
    jest.advanceTimersByTime(100);
    fixture.detectChanges();

    // Now chartOptions should be set
    expect(component.chartOptions).toBeDefined();
    expect(component.chartOptions.chart.type).toBe('column');
    expect(component.chartOptions.title.text).toBe('Test Chart');
    expect(component.chartOptions.xAxis.categories).toEqual(['A', 'B', 'C']);
    expect(component.chartOptions.yAxis.title.text).toBe('Values');
    expect(component.chartOptions.series[0].name).toBe('Test');
    expect(component.chartOptions.series[0].type).toBe('column');
    expect(component.chartOptions.series[0].data).toEqual([10, 20, 30]);
  });

  it('should not set chartOptions before timeout', () => {
    fixture.detectChanges();
    component.ngOnInit();
    expect(component.chartOptions).toBeUndefined();
  });

  it('should call console.log when chart options are set', () => {
    const consoleSpy = jest.spyOn(console, 'log');

    fixture.detectChanges();
    component.ngOnInit();

    // Fast-forward time by 100ms
    jest.advanceTimersByTime(100);
    fixture.detectChanges();

    // Verify that console.log was called with any arguments
    expect(consoleSpy).toHaveBeenCalled();
    consoleSpy.mockRestore();
  });
});
