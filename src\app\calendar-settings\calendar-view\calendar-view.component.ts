/* eslint-disable no-underscore-dangle */
import {
  Component, OnInit, TemplateRef, ViewChild,
} from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { FullCalendarComponent } from '@fullcalendar/angular';
import interactionPlugin from '@fullcalendar/interaction';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import moment from 'moment';
import { Calendar, CalendarOptions } from '@fullcalendar/core';
import { ToastrService } from 'ngx-toastr';
import { Title } from '@angular/platform-browser';
import { CalendarService } from '../../services/profile/calendar.service';
import { ProjectService } from '../../services/profile/project.service';
import { AddCalendarEventComponent } from '../add-calendar-event/add-calendar-event.component';
import { EditCalendarEventComponent } from '../edit-calendar-event/edit-calendar-event.component';
import { weekDays } from '../../services/common';

@Component({
  selector: 'app-calendar-view',
  templateUrl: './calendar-view.component.html',
})
export class CalendarViewComponent implements OnInit {
  public html = '<span class="btn btn-danger">Never trust not sanitized HTML!!!</span>';

  public modalRef: BsModalRef;

  public descriptionPopup = false;

  public calendar: Calendar;

  public calendarApi;

  public Range: any = {};

  public loader = true;

  public calendarEvents: any = [];

  public events: any = [];

  public ParentCompanyId;

  public ProjectId;

  public showSearchbar = false;

  public search = '';

  public monthEvents = [];

  public currentView = 'Month';

  public viewEventData: any;

  public message = '';

  public deleteSubmitted = false;

  public monthlyEventloader = true;

  public deleteEventId: string;

  public currentViewMonth: moment.MomentInput;

  public subscription: any;

  public subscription1: any;

  public weekDays: any = weekDays;

  @ViewChild('fullcalendar', { static: true }) public calendarComponent1: FullCalendarComponent;

  public calendarOptions: CalendarOptions = {
    selectable: true,
    initialView: 'dayGridMonth',
    plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin],
    aspectRatio: 2,
    dayMaxEvents: true,
    slotEventOverlap: false,
    eventOverlap: false,
    expandRows: true,
    nowIndicator: true,
    dayMaxEventRows: 1,
    events: this.events,
    contentHeight: 'liquid',
    fixedWeekCount: false,
    lazyFetching: true,
    customButtons: {
      prev: {
        text: 'PREV',
        click: (): void => this.goPrev(),
      },
      next: {
        text: 'Next',
        click: (): void => this.goNext(),
      },
      prevYear: {
        text: 'PREV',
        click: (): void => this.goPrevYear(),
      },
      nextYear: {
        text: 'Next',
        click: (): void => this.goNextYear(),
      },
      timeGridWeek: {
        text: 'Week',
        click: (): void => this.goTimeGridWeekOrDay('timeGridWeek'),
      },
      timeGridDay: {
        text: 'Day',
        click: (): void => this.goTimeGridWeekOrDay('timeGridDay'),
      },
      dayGridMonth: {
        text: 'Month',
        click: (): void => this.goDayGridMonth(),
      },
    },
    showNonCurrentDates: false,
    headerToolbar: {
      right: '',
      center: 'prevYear,prev,title,next,nextYear',
      left: 'dayGridMonth,timeGridWeek,timeGridDay',
    },
    firstDay: 0,
    eventClick: (eventArg): void => {
      this.deliveryDescription(eventArg);
    },
    eventDidMount(info): void {
      const argument = info;
      const calendarDescription = argument.event._def.title;
      if (argument.event._def.allDay) {
        argument.el.querySelector('.fc-event-title').innerHTML = `
        <div class="d-flex flex-row">
        <p class="m-0"> <img  class="w10 h10" src="./assets/images/noun-event-alert.svg" class="form-icon" alt="allday" >  ${calendarDescription} </p>
        </div>`;
      } else {
        // eslint-disable-next-line no-param-reassign
        argument.el.querySelector('.fc-event-title').innerHTML = `
        <div class="d-flex flex-row">
        <p class="m-0"> ${calendarDescription} </p>
        </div>`;
      }
    },
    eventTimeFormat: {
      hour: 'numeric',
      minute: '2-digit',
      meridiem: 'short',
    },
    dateClick: (info): void => {
      this.openAddEventModal(info);
    },
    datesSet: (info): void => {
      this.currentViewMonth = info.view.title;
    },
  };

  public constructor(
    private readonly modalService: BsModalService,
    public calendarService: CalendarService,
    public projectService: ProjectService,
    private readonly toastr: ToastrService,
    private readonly titleService: Title,
  ) {
    this.calendarService.getCalendarEventData.subscribe((res): void => {
      if (res) {
        this.getCalendarEvents();
        this.getCalendarMonthEvents();
      }
    });
    this.titleService.setTitle('Follo - Calendar Settings');
  }

  public ngOnInit(): void { /* */ }

  public setCalendar(): void {
    this.calendarApi = this.calendarComponent1.getApi();
    this.Range = this.calendarApi?.currentData?.dateProfile.activeRange;
    this.getCalendarEvents();
    this.getCalendarMonthEvents();
  }

  public openAddEventModal(dateArg): void {
    const passData = {
      date: dateArg.dateStr,
      currentView: this.currentView,
    };
    const initialState = {
      data: passData,
      title: 'Modal with component',
    };
    const className = 'modal-lg new-delivery-popup custom-modal add-calendarevent';
    this.modalRef = this.modalService.show(AddCalendarEventComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
      initialState,
    });
    this.modalRef.content.closeBtnName = 'Close';
  }

  public triggerFalseClick(): void {
    document.getElementById('myCheck').click();
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'close':
          this.closeDescription();
          break;
        case 'delete':
          this.openDeleteModal(data, item);
          break;
        case 'edit':
          this.openEditModal(data);
          break;
        case 'show':
          this.showPopover(data);
          break;
        case 'clear':
          this.clear();
          break;
        default:
          break;
      }
    }
  }

  public goPrevYear(): void {
    this.closeDescription();
    this.calendarApi.prevYear(); // call a method on the Calendar object
    this.setCalendar();
  }

  public goNextYear(): void {
    this.closeDescription();
    this.calendarApi.nextYear(); // call a method on the Calendar object
    this.setCalendar();
  }

  public ngAfterViewInit(): void {
    this.projectService.projectParent.subscribe((response): void => {
      if (response !== undefined && response !== null && response !== '') {
        this.ParentCompanyId = response.ParentCompanyId;
        this.ProjectId = response.ProjectId;
        this.setCalendar();
      }
    });
  }

  public openAddCalendarEventModal(): void {
    this.descriptionPopup = false;
    this.triggerFalseClick();
    const className = 'modal-lg new-delivery-popup custom-modal add-calendarevent';
    this.modalRef = this.modalService.show(AddCalendarEventComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
    });
    this.modalRef.content.closeBtnName = 'Close';
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.getCalendarEvents();
    this.getCalendarMonthEvents();
  }

  public getCalendarMonthEvents(): void {
    if (this.ProjectId && this.ParentCompanyId) {
      this.monthlyEventloader = true;
      const getEventNdrPayload: any = {};
      this.monthEvents = [];
      getEventNdrPayload.start = moment(this.Range?.start).format('YYYY-MM-DD');
      getEventNdrPayload.end = moment(this.Range?.end).format('YYYY-MM-DD');
      getEventNdrPayload.ProjectId = this.ProjectId;
      getEventNdrPayload.ParentCompanyId = this.ParentCompanyId;
      getEventNdrPayload.search = this.search;
      getEventNdrPayload.currentViewMonth = this.currentViewMonth;
      getEventNdrPayload.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      getEventNdrPayload.isDST = moment(new Date()).isDST();
      if (this.currentView === 'Month') {
        if (this.subscription1) {
          this.subscription1.unsubscribe();
        }
        this.subscription1 = this.calendarService
          .getCalendarMonthEvents(getEventNdrPayload)
          .subscribe((NdrResponse: any): void => {
            if (NdrResponse) {
              const monthEvents = [];
              for (
                let index = 1;
                index <= +moment(getEventNdrPayload.currentViewMonth, 'MMM YYYY').daysInMonth();
                index += 1
              ) {
                const oneDayEvents = NdrResponse.events.filter(
                  (object): any => +moment(new Date(object.fromDate)).format('DD') === index
                    && moment(new Date(object.fromDate)).format('MMMM YYYY')
                      === getEventNdrPayload.currentViewMonth,
                );
                if (oneDayEvents.length > 0) {
                  monthEvents.push({
                    day: moment(new Date(oneDayEvents[0].fromDate)).format('ddd, MMMM DD YYYY'),
                    events: oneDayEvents,
                  });
                }
              }
              this.monthEvents = monthEvents;
              this.monthlyEventloader = false;
            }
          });
      }
    }
  }

  public getCalendarEvents(): void {
    if (this.ProjectId && this.ParentCompanyId) {
      this.loader = true;
      this.events = [];
      this.calendarEvents = [];
      const getEventNdrPayload: any = {};
      getEventNdrPayload.start = moment(this.Range?.start).format('YYYY-MM-DD');
      getEventNdrPayload.end = moment(this.Range?.end).format('YYYY-MM-DD');
      getEventNdrPayload.ProjectId = this.ProjectId;
      getEventNdrPayload.ParentCompanyId = this.ParentCompanyId;
      getEventNdrPayload.search = this.search;
      getEventNdrPayload.calendarView = this.currentView;
      getEventNdrPayload.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      getEventNdrPayload.isDST = moment(new Date()).isDST();
      if (this.subscription) {
        this.subscription.unsubscribe();
      }
      this.subscription = this.calendarService
        .getCalendarEvents(getEventNdrPayload)
        .subscribe((NdrResponse: any): void => {
          this.calendarApi.removeAllEventSources();
          this.calendarApi.addEventSource(this.events);
          if (NdrResponse) {
            this.calendarEvents = NdrResponse.events;
            if (this.calendarEvents?.length > 0) {
              this.events = [];
              this.calendarEvents.forEach((element): void => {
                const data = {
                  title: element.description,
                  uniqueNumber: element.uniqueNumber,
                  start: element.fromDate,
                  end: element.toDate,
                  id: element.id,
                  allDaySlot: false,
                  allDay: false,
                  className: ['calendar_event'],
                };
                if (element.isAllDay) {
                  delete data.allDaySlot;
                  delete data.allDay;
                  data.allDaySlot = true;
                  data.allDay = true;
                }
                this.events.push(data);
              });
              this.calendarApi.removeAllEventSources();
              this.calendarApi.addEventSource(this.events);
            }
            this.loader = false;
          }
        });
    }
  }

  public openDeleteModal(eventData: { id: any }, template: TemplateRef<any>): void {
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-md new-gate-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
    this.deleteEventId = eventData.id;
  }

  public resetAndClose(): void {
    this.deleteEventId = '';
    this.modalRef.hide();
  }

  public deleteCalendarEvent(): void {
    this.deleteSubmitted = true;
    this.descriptionPopup = false;
    this.triggerFalseClick();
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.deleteCalendarEvent(this.deleteEventId, params, '').subscribe({
      next: (response: any): void => {
        if (response) {
          this.deleteSubmitted = false;
          this.toastr.success(response.message, 'Success');
          this.getCalendarEvents();
          this.getCalendarMonthEvents();
          this.deleteEventId = '';
          this.modalRef.hide();
        }
      },
      error: (deleteCalendarEventError): void => {
        this.deleteSubmitted = false;
        if (deleteCalendarEventError.message?.statusCode === 400) {
          this.showError(deleteCalendarEventError);
        } else if (!deleteCalendarEventError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(deleteCalendarEventError.message, 'OOPS!');
        }
      },
    });
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.deleteSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public getSearch(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.search = data;
    this.getCalendarEvents();
    this.getCalendarMonthEvents();
  }

  public goNext(): void {
    this.events = [];
    this.closeDescription();
    this.calendarApi.next();
    this.setCalendar();
  }

  public goTimeGridWeekOrDay(view: string): void {
    this.loader = true;
    if (view === 'timeGridWeek') {
      this.currentView = 'Week';
    } else {
      this.currentView = 'Day';
    }
    setTimeout((): void => {
      this.closeDescription();
      this.calendarApi.changeView(view);
      this.setCalendar();
    }, 1000);
  }

  public goDayGridMonth(): void {
    this.currentView = 'Month';
    this.closeDescription();
    this.calendarApi.changeView('dayGridMonth');
    this.setCalendar();
  }

  public goPrev(): void {
    this.events = [];
    this.closeDescription();
    this.calendarApi.prev(); // call a method on the Calendar object
    this.setCalendar();
  }

  public closeDescription(): void {
    this.descriptionPopup = false;
    this.viewEventData = '';
  }

  public close(): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public openEditModal(data: { id: any }): void {
    this.descriptionPopup = false;
    this.triggerFalseClick();
    const passData = {
      id: data.id,
    };
    const initialState = {
      data: passData,
      title: 'Modal with component',
    };
    const className = 'modal-lg new-delivery-popup custom-modal add-calendarevent';
    this.modalRef = this.modalService.show(EditCalendarEventComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
      initialState,
    });
    this.modalRef.content.closeBtnName = 'Close';
  }

  public onShown(): void {
    this.viewEventData = '';
  }

  public changeFormat(fromDate: any): any {
    if (fromDate) {
      const dayFormat = moment(new Date(fromDate)).format('ddd MM/DD/YYYY');
      return dayFormat;
    }
  }

  public deliveryDescription(eventData): void {
    this.descriptionPopup = false;
    if (Object.keys(eventData).length !== 0) {
      const index = this.calendarEvents.findIndex(
        (item: { description: any; uniqueNumber: any }): any => item.description === eventData.event.title
          && item.uniqueNumber === eventData.event.extendedProps.uniqueNumber,
      );
      this.viewEventData = this.calendarEvents[index];
      this.occurMessage(this.viewEventData);
      this.descriptionPopup = true;
    }
  }

  public showPopover(eventData: any): void {
    this.descriptionPopup = false;
    this.onShown();
    this.viewEventData = eventData;
    this.occurMessage(this.viewEventData);
  }

  public occurMessage(data): void {
    this.message = 'Occurs every day';
    if (data.repeatEveryType === 'Day') {
      this.message = '';
      this.message = 'Occurs every day';
    }
    if (data.repeatEveryType === 'Days') {
      this.message = '';
      if (+data.repeatEveryCount === 2) {
        this.message = 'Occurs every other day';
      } else {
        this.message = `Occurs every ${data.repeatEveryCount} days`;
      }
    }
    if (data.repeatEveryType === 'Week') {
      this.message = '';
      let weekDays = '';
      data.days.forEach((day1: any): any => {
        weekDays = `${weekDays + day1},`;
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message += `Occurs every ${weekDays}`;
    }
    if (data.repeatEveryType === 'Weeks') {
      let weekDays = '';
      data.days.forEach((day1: any): any => {
        weekDays = `${weekDays + day1},`;
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message = '';
      if (+data.repeatEveryCount === 2) {
        this.message = `Occurs every other  ${weekDays}`;
      } else {
        this.message = `Occurs every ${data.repeatEveryCount} weeks on ${weekDays}`;
      }
    }
    if (
      data.repeatEveryType === 'Month'
      || data.repeatEveryType === 'Months'
      || data.repeatEveryType === 'Year'
      || data.repeatEveryType === 'Years'
    ) {
      if (data.chosenDateOfMonth) {
        this.message = `Occurs on day ${data.dateOfMonth}`;
      } else {
        this.message = `Occurs on the ${data.monthlyRepeatType}`;
      }
    }
    this.message += ` until ${moment(data.endTime).format('MM-DD-YYYY')}`;
  }
}
