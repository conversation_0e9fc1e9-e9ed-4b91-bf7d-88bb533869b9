<div class="comments-form my-3 mx-3">
    <form name="form" class="custom-material-form" [formGroup]="overRideDetailsForm" (ngSubmit)="onSubmit()" novalidate>
        <div class="row">
            <div class="col-md-7">
                <div class="form-group mt-5">
                    <label class="fs12 fw600" for="comments">Enter your comments here</label>
                    <textarea class="form-control fs12" formControlName="comment" id="comments" placeholder="" rows="3"></textarea >
                        <div class="color-red" *ngIf="submitted && overRideDetailsForm.get('comment').errors">
                            <small *ngIf="overRideDetailsForm.get('comment').errors.required">*Comment is required.</small>
                          </div>
                  </div>
                 <div class="d-flex justify-content-end">
                    <button class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem" type="submit"  [disabled]="formSubmitted && overRideDetailsForm.valid"><em class="fa fa-spinner" aria-hidden="true" *ngIf="formSubmitted && overRideDetailsForm.valid"></em>Submit</button>
                 </div>
            </div>
        </div>
    </form>
</div>
