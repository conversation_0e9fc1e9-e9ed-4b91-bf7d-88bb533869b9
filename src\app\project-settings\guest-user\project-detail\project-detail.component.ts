import { Component } from '@angular/core';
import { NavigationExtras, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ProjectService } from '../../../services/profile/project.service';
import { ProjectSettingsService, ProjectSharingService } from '../../../services';

declare let google: any;
@Component({
  selector: 'app-project-detail',
  templateUrl: './project-detail.component.html',
  })
export class ProjectDetailComponent {
  public latitude: number;

  public longitude: number;

  public zoom: number;

  public encryptedUrl: string;

  public projectID: number;

  public ParentCompanyId: number;

  public geoCoder: {
    geocode: (
      arg0: { location: { lat: any; lng: any } },
      arg1: (results: any, status: any) => void,
    ) => void;
  };

  public mainLoader: boolean;

  public addDeliveryBooking: boolean;

  public addCraneBooking: boolean;

  public addConcreteBooking: boolean;

  public viewDeliveryCalendar: boolean;

  public viewCraneCalendar: boolean;

  public viewConcreteCalendar: boolean;

  public craneBookTop: boolean = false;

  public concreteBookTop: boolean = false;

  public modalRef: BsModalRef;

  public projectList: any;

  public guestData: any;

  public projectSharingSettings: any = {
    isPublicWebsiteEnabled: false,
  };

  public refreshRate: any;

  public mapOptions: google.maps.MapOptions = {
    center: { lat: 38.897957, lng: -77.036560 },
    zoomControl: true,
    mapTypeControl: true,
    streetViewControl: false,
    fullscreenControl: true,
    draggable: true,
    zoom: 18,
    mapTypeId: google.maps.MapTypeId.SATELLITE,
  };

  public marker = {
    position: { lat: 38.897957, lng: -77.036560 },
    options: { animation: google.maps.Animation.DROP },
  };

  public constructor(
    private readonly modalService: BsModalService,
    public router: Router,
    private readonly toastr: ToastrService,
    private readonly projectService: ProjectService,
    public projectSettingsService: ProjectSettingsService,
    private readonly ProjectSharingServices: ProjectSharingService,
  ) {
    localStorage.setItem('url', window.btoa(this.router.url));
    const url = this.router.url.split('/');
    this.encryptedUrl = url[2];
    if (this.encryptedUrl) {
      this.gettingProjectDetailsResponse();
    }
  }

  public handleDownKeydown(event: KeyboardEvent, type: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'calendar':
          this.navigateCalendar();
          break;
        case 'delivery':
          this.navigateDeliveryBooking();
          break;
        case 'crane':
          this.navigateCraneBooking();
          break;
        case 'concrete':
          this.navigateConcreteBooking();
          break;
        default:
          break;
      }
    }
  }

  public gettingProjectDetailsResponse(): void {
    this.mainLoader = true;
    const payload: any = {
      encodeUrl: this.encryptedUrl,
      // ProjectId: this.guestData.ProjectId,
    };
    this.projectService.decodeProjectDetailUrl(payload).subscribe({
      next: (response: any): void => {
        if (response.data) {
          this.projectList = response.data.projectList;
          this.latitude = Number(this.projectList.projectLocationLatitude);
          this.longitude = Number(this.projectList.projectLocationLongitude);
          this.zoom = 15;
          this.projectID = this.projectList.id;
          this.ParentCompanyId = this.projectList.ParentCompanyId;
          this.mapForProjectLocationLoader(this.latitude, this.longitude);
          this.projectSharingSettings = response?.data?.projectList?.ProjectSettings;
          this.addDeliveryBooking = this.projectSharingSettings.allowGuestToAddDeliveryBooking;
          this.addCraneBooking = this.projectSharingSettings.allowGuestToAddCraneBooking;
          this.addConcreteBooking = this.projectSharingSettings.allowGuestToAddConcreteBooking;
          this.viewDeliveryCalendar = this.projectSharingSettings.allowGuestToViewDeliveryCalendar;
          this.viewCraneCalendar = this.projectSharingSettings.allowGuestToViewCraneCalendar;
          this.viewConcreteCalendar = this.projectSharingSettings.allowGuestToViewConcreteCalendar;
          this.refreshRate = +this.projectSharingSettings.autoRefreshRateInMinutes;
        }
        this.mainLoader = false;
        if (
          this.addDeliveryBooking
          && (this.viewCraneCalendar || this.viewDeliveryCalendar || this.viewConcreteCalendar)
        ) {
          this.craneBookTop = false;
        } else {
          this.craneBookTop = true;
        }
        if (
          this.addDeliveryBooking
          && this.addCraneBooking
          && (this.viewCraneCalendar || this.viewDeliveryCalendar || this.viewConcreteCalendar)
        ) {
          this.concreteBookTop = false;
        } else if (
          this.addDeliveryBooking
          && (this.viewCraneCalendar || this.viewDeliveryCalendar || this.viewConcreteCalendar)
        ) {
          this.concreteBookTop = false;
        } else if (
          this.addCraneBooking
          && (this.viewCraneCalendar || this.viewDeliveryCalendar || this.viewConcreteCalendar)
        ) {
          this.concreteBookTop = false;
        } else if (this.addDeliveryBooking && this.addCraneBooking) {
          this.concreteBookTop = false;
        } else {
          this.concreteBookTop = true;
        }
        localStorage.setItem('guestProjectId', window.btoa(this.projectID.toString()));
        localStorage.setItem('guestParentCompanyId', window.btoa(this.ParentCompanyId.toString()));
        this.setupPageRefresh();
      },
      error: (error): void => {
        this.mainLoader = false;
        this.toastr.error('Try again later.!', 'Something went wrong.');
      },
    });
  }

  public mapForProjectLocationLoader(latitude: any, longitude: any): void {
    this.geoCoder = new google.maps.Geocoder();
    this.projectLocation(latitude, longitude);
  }

  public projectLocation(latitude: string, longitude: string): void {
    const lat = parseFloat(latitude);
    const long = parseFloat(longitude);

    // Always set coordinates regardless of geolocation availability
    this.latitude = lat;
    this.longitude = long;
    this.zoom = 18;

    if (navigator.geolocation) {
      this.mapOptions.center = {
        lat: this.latitude,
        lng: this.longitude,
      };
      this.marker.position = { lat: this.latitude, lng: this.longitude };
      this.mapOptions.zoom = 18;
    }
  }

  public downloadFile(): void {
    window.open(this.projectSharingSettings.projectLogisticPlanUrl, '_blank');
  }

  public navigateCalendar(): void {
    if (this.viewDeliveryCalendar) {
      const projectDetails: NavigationExtras = {
        queryParams: {
          type: 'deliveryCalendar',
        },
      };
      if (this.hasValidGuestId()) {
        this.router.navigate(['/guest-delivery-calendar']);
      } else {
        this.router.navigate(['/enter-details'], projectDetails);
      }
    } else if (this.viewCraneCalendar) {
      const projectDetails: NavigationExtras = {
        queryParams: {
          type: 'craneCalendar',
        },
      };
      if (this.hasValidGuestId()) {
        this.router.navigate(['/guest-crane-calendar']);
      } else {
        this.router.navigate(['/enter-details'], projectDetails);
      }
    } else if (this.viewConcreteCalendar) {
      const projectDetails: NavigationExtras = {
        queryParams: {
          type: 'concreteCalendar',
        },
      };
      if (this.hasValidGuestId()) {
        this.router.navigate(['/guest-concrete-calendar']);
      } else {
        this.router.navigate(['/enter-details'], projectDetails);
      }
    }
  }

  public navigateDeliveryBooking(): void {
    const projectDetails: NavigationExtras = {
      queryParams: {
        type: 'deliveryBooking',
      },
    };
    if (this.hasValidGuestId()) {
      this.router.navigate(['/guest-delivery-booking']);
    } else {
      this.router.navigate(['/enter-details'], projectDetails);
    }
  }

  public navigateCraneBooking(): void {
    const projectDetails: NavigationExtras = {
      queryParams: {
        type: 'craneBooking',
      },
    };
    if (this.hasValidGuestId()) {
      this.router.navigate(['/guest-crane-booking']);
    } else {
      this.router.navigate(['/enter-details'], projectDetails);
    }
  }

  public navigateConcreteBooking(): void {
    const projectDetails: NavigationExtras = {
      queryParams: {
        type: 'concreteBooking',
      },
    };
    if (this.hasValidGuestId()) {
      this.router.navigate(['/guest-concrete-booking']);
    } else {
      this.router.navigate(['/enter-details'], projectDetails);
    }
  }

  private hasValidGuestId(): boolean {
    try {
      const guestId = localStorage.getItem('guestId');
      if (!guestId) {
        return false;
      }
      return +window.atob(guestId) > 0;
    } catch (error) {
      return false;
    }
  }

  public setupPageRefresh(): void {
    setInterval(() => {
      location.reload();
    }, this.refreshRate * 60 * 1000);
  }
}
