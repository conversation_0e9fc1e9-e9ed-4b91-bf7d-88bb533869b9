{"name": "follo-web", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "jest", "test:coverage": "jest --coverage", "e2e": "ng e2e", "lint:css": "stylelint --color --fix \"**/*.scss\"", "lint:ts": "eslint --color --fix --ext .ts src", "lint:watch": "nodemon -e scss,ts -x npm run start-lint", "serve": "node src/api/index.js", "start-lint": "npm run lint:css && npm run lint:ts", "start-dev": "npm-run-all --parallel lint:watch start", "sonar": "sonar-scanner", "bundle:report": "source-map-explorer dist/follo-web/**/*.js"}, "private": true, "dependencies": {"@angular-magic/ngx-gp-autocomplete": "^2.0.2", "@angular/animations": "^16.2.12", "@angular/cdk": "^19.2.16", "@angular/common": "^16.2.12", "@angular/compiler": "^16.2.12", "@angular/core": "^16.2.12", "@angular/forms": "^16.2.12", "@angular/google-maps": "^15.2.9", "@angular/platform-browser": "^16.2.12", "@angular/platform-browser-dynamic": "^16.2.12", "@angular/router": "^16.2.12", "@fullcalendar/angular": "6.1.10", "@fullcalendar/core": "6.1.11", "@fullcalendar/daygrid": "6.1.11", "@fullcalendar/interaction": "6.1.11", "@fullcalendar/timegrid": "6.1.11", "@googlemaps/js-api-loader": "^1.16.6", "@lottiefiles/lottie-player": "^2.0.4", "@sentry/angular": "^8.7.0", "@sentry/tracing": "^7.114.0", "@swimlane/ngx-charts": "^21.1.3", "@types/google.maps": "^3.55.9", "angular-user-idle": "^3.0.1", "angularx-qrcode": "^15.0.1", "bn-ng-idle": "^2.0.5", "bootstrap": "^5.3.3", "bootstrap-float-label": "^3.0.1", "exceljs": "^4.4.0", "file-saver": "^2.0.2", "highcharts": "^11.4.3", "intercom": "^0.5.1", "mixpanel-browser": "^2.49.0", "moment": "^2.30.1", "ng-multiselect-dropdown": "^1.0.0", "ng2-material-dropdown": "^1.0.0", "ng2-pdf-viewer": "^9.1.5", "ngx-bootstrap": "^11.0.2", "ngx-chips": "^3.0.0", "ngx-color-picker": "^15.0.0", "ngx-file-drop": "^16.0.0", "ngx-loading": "^17.0.0", "ngx-mask": "^14.3.3", "ngx-pagination": "^6.0.3", "ngx-select-dropdown": "^3.3.2", "ngx-socket-io": "^4.5.0", "ngx-toastr": "^17.0.2", "ngx-ui-switch": "^15.0.0", "resizable-columns": "^1.0.4", "rxjs": "^7.8.1", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.13.3"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.12", "@angular/cli": "^16.2.12", "@angular/compiler-cli": "^16.2.12", "@angular/language-service": "^16.2.12", "@types/google.maps": "^3.55.9", "@types/jasmine": "^4.3.0", "@types/jasminewd2": "^2.0.13", "@types/jest": "^28.1.1", "@types/jquery": "^3.5.30", "@types/node": "^20.14.2", "@typescript-eslint/eslint-plugin": "^7.13.0", "codelyzer": "^6.0.2", "eslint": "^8.57.0", "eslint-config-airbnb-angular": "^1.0.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-angular": "^4.1.0", "eslint-plugin-html": "^8.1.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-promise": "^6.2.0", "jasmine-spec-reporter": "^7.0.0", "jest": "^29.7.0", "jest-preset-angular": "^12.2.6", "json-server-auth": "^2.1.0", "lighthouse": "^12.0.0", "nodemon": "^3.1.3", "npm-run-all": "^4.1.5", "protractor": "~7.0.0", "sonar-scanner": "^3.1.0", "source-map-explorer": "^2.5.3", "style-loader": "^4.0.0", "stylelint": "^15.11.0", "stylelint-config-standard": "^36.0.0", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "tslint": "^6.1.3", "typescript": "^5.0.2", "webpack": "^5.99.9"}}