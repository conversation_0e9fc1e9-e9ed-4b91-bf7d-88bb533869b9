import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EditCraneBookingComponent } from './edit-crane-booking.component';
import { UntypedFormBuilder, ReactiveFormsModule, FormsModule, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { BsModalService, BsModalRef, ModalOptions } from 'ngx-bootstrap/modal';
import { Router } from '@angular/router';
import { Socket } from 'ngx-socket-io';
import { DeliveryService } from '../../../../services/profile/delivery.service';
import { MixpanelService } from '../../../../services/mixpanel.service';
import { ProjectSettingsService } from '../../../../services/project_settings/project-settings.service';
import { ProjectSharingService } from '../../../../services/projectSharingService/project-sharing.service';
import { of, throwError } from 'rxjs';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { NO_ERRORS_SCHEMA, Component } from '@angular/core';

// Create a host component with no template to avoid template-related errors
@Component({
  selector: 'app-test-host',
  template: ''
})
class TestHostComponent extends EditCraneBookingComponent {
  constructor(
    formBuilder: UntypedFormBuilder,
    socket: Socket,
    toastr: ToastrService,
    router: Router,
    modalRef: BsModalRef,
    modalService: BsModalService,
    modalRef1: BsModalRef,
    mixpanelService: MixpanelService,
    deliveryService: DeliveryService,
    projectSettingsService: ProjectSettingsService,
    projectSharingService: ProjectSharingService,
    option: ModalOptions
  ) {
    super(
      formBuilder,
      socket,
      toastr,
      router,
      modalRef,
      modalService,
      modalRef1,
      mixpanelService,
      deliveryService,
      projectSettingsService,
      projectSharingService,
      option
    );
  }
}

describe('EditCraneBookingComponent', () => {
  let component: TestHostComponent;
  let fixture: ComponentFixture<TestHostComponent>;
  let mockToastr: jest.Mocked<ToastrService>;
  let mockModalService: jest.Mocked<BsModalService>;
  let mockRouter: jest.Mocked<Router>;
  let mockSocket: jest.Mocked<Socket>;
  let mockDeliveryService: jest.Mocked<DeliveryService>;
  let mockMixpanelService: jest.Mocked<MixpanelService>;
  let mockProjectSettingsService: jest.Mocked<ProjectSettingsService>;
  let mockProjectSharingService: jest.Mocked<ProjectSharingService>;
  let mockModalRef: jest.Mocked<BsModalRef>;
  let mockModalRef1: jest.Mocked<BsModalRef>;

  beforeEach(async () => {
    mockToastr = {
      success: jest.fn(),
      error: jest.fn()
    } as any;

    mockModalService = {
      show: jest.fn().mockReturnValue({ content: { seriesOption: 1 } })
    } as any;

    mockRouter = {
      navigate: jest.fn()
    } as any;

    mockSocket = {
      emit: jest.fn(),
      on: jest.fn()
    } as any;

    mockDeliveryService = {} as any;

    mockMixpanelService = {
      track: jest.fn(),
      addGuestUserMixpanelEvents: jest.fn()
    } as any;

    mockModalRef = {
      hide: jest.fn(),
      content: { seriesOption: 1 }
    } as any;

    mockModalRef1 = {
      hide: jest.fn()
    } as any;

    mockProjectSettingsService = {
      getGuestProjectSettings: jest.fn().mockReturnValue(of({
        data: {
          deliveryWindowTime: 30,
          deliveryWindowTimeUnit: 'minutes'
        }
      }))
    } as any;

    mockProjectSharingService = {
      guestGateList: jest.fn().mockReturnValue(of({ data: [] })),
      guestListCraneEquipment: jest.fn().mockReturnValue(of({ data: { rows: [] } })),
      guestGetCompanies: jest.fn().mockReturnValue(of({ data: [] })),
      guestGetDefinableWork: jest.fn().mockReturnValue(of({ data: [] })),
      guestGetLocations: jest.fn().mockReturnValue(of({ data: [] })),
      guestEditCraneRequest: jest.fn().mockReturnValue(of({ message: 'Success' })),
      guestGetEquipmentCraneRequest: jest.fn().mockReturnValue(of({ data: {} })),
      guestGetMemberRole: jest.fn().mockReturnValue(of({ data: {} })),
      listAllMember: jest.fn().mockReturnValue(of({ data: [] })),
      guestSearchNewMember: jest.fn().mockReturnValue(of([]))
    } as any;

    await TestBed.configureTestingModule({
      declarations: [TestHostComponent],
      imports: [
        ReactiveFormsModule,
        FormsModule,
        BsDatepickerModule.forRoot()
      ],
      providers: [
        UntypedFormBuilder,
        { provide: ToastrService, useValue: mockToastr },
        { provide: BsModalService, useValue: mockModalService },
        { provide: Router, useValue: mockRouter },
        { provide: Socket, useValue: mockSocket },
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: MixpanelService, useValue: mockMixpanelService },
        { provide: ProjectSettingsService, useValue: mockProjectSettingsService },
        { provide: ProjectSharingService, useValue: mockProjectSharingService },
        { provide: BsModalRef, useValue: mockModalRef },
        { provide: ModalOptions, useValue: {} }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    // Mock localStorage
    jest.spyOn(Storage.prototype, 'getItem').mockImplementation((key: string) => {
      if (key === 'guestProjectId') return btoa('1');
      if (key === 'guestParentCompanyId') return btoa('2');
      if (key === 'guestId') return btoa('3');
      return null;
    });

    fixture = TestBed.createComponent(TestHostComponent);
    component = fixture.componentInstance;

    // Set up component properties manually
    component.data = { id: 1, seriesOption: 1 };
    component.ProjectId = 1;
    component.ParentCompanyId = 2;
    component.guestUserId = 3;
    component.modalRef = mockModalRef;
    component.modalRef1 = mockModalRef1;

    // Create a real form using the actual FormBuilder
    const formBuilder = TestBed.inject(UntypedFormBuilder);
    component.craneEditRequest = formBuilder.group({
      id: [''],
      EquipmentId: [[]],
      LocationId: ['', [Validators.required]],
      additionalNotes: [''],
      CraneRequestId: [''],
      responsiblePersons: [''],
      description: ['', [Validators.required]],
      deliveryDate: ['', [Validators.required]],
      craneDeliveryStart: ['', [Validators.required]],
      craneDeliveryEnd: ['', [Validators.required]],
      isEscortNeeded: [false],
      companies: [[]],
      definableFeatureOfWorks: [[]],
      pickUpLocation: [''],
      dropOffLocation: [''],
      isAssociatedWithDeliveryRequest: [false, [Validators.required]],
      recurrenceId: [''],
      recurrence: [''],
      recurrenceEndDate: ['']
    });

    component.gateList = [];
    component.equipmentList = [];
    component.definableFeatureOfWorkList = [];
    component.companyList = [];
    component.memberList = [{ id: 1, email: '<EMAIL>' }];
    component.locationList = [];
    component.deliveryWindowTime = 30;
    component.deliveryWindowTimeUnit = 'minutes';
    component.currentEditItem = {
      id: 1,
      status: 'Pending',
      craneDeliveryStart: new Date(),
      craneDeliveryEnd: new Date(),
      deliveryDate: new Date()
    };

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Initialization', () => {
    it('should initialize with correct project and company IDs', () => {
      expect(component.ProjectId).toBe(1);
      expect(component.ParentCompanyId).toBe(2);
      expect(component.guestUserId).toBe(3);
    });

    it('should call ngOnInit and set crane request ID', () => {
      component.ngOnInit();
      expect(component.craneRequestId).toBe(1);
      expect(component.seriesOption).toBe(1);
    });

    it('should call ngAfterViewInit and set disabled date', () => {
      component.ngAfterViewInit();
      expect(component.isDisabledDate).toBe(false); // seriesOption is 1
    });
  });

  describe('Project Settings', () => {
    it('should get project settings successfully', () => {
      component.getProjectSettings();

      expect(mockProjectSettingsService.getGuestProjectSettings).toHaveBeenCalledWith({
        ProjectId: 1
      });
      expect(component.deliveryWindowTime).toBe(30);
      expect(component.deliveryWindowTimeUnit).toBe('minutes');
    });

    it('should not call getProjectSettings when ProjectId is not set', () => {
      component.ProjectId = null;
      component.getProjectSettings();

      expect(mockProjectSettingsService.getGuestProjectSettings).not.toHaveBeenCalled();
    });
  });

  describe('Data Loading Methods', () => {
    it('should get gate list successfully', () => {
      const mockResponse = { data: [{ id: 1, name: 'Gate 1' }] };
      mockProjectSharingService.guestGateList.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getOverAllEquipmentforEditNdr').mockImplementation();

      component.getOverAllGate();

      expect(component.modalLoader).toBe(true);
      expect(mockProjectSharingService.guestGateList).toHaveBeenCalledWith(
        {
          ProjectId: 1,
          pageSize: 0,
          pageNo: 0,
          ParentCompanyId: 2
        },
        { isFilter: true, showActivatedAlone: true }
      );
      expect(component.gateList).toEqual(mockResponse.data);
      expect(component.getOverAllEquipmentforEditNdr).toHaveBeenCalled();
    });

    it('should get equipment list successfully', () => {
      const mockResponse = { data: { rows: [{ id: 1, equipmentName: 'Equipment 1' }] } };
      mockProjectSharingService.guestListCraneEquipment.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getCompaniesForEditNdr').mockImplementation();

      component.getOverAllEquipmentforEditNdr();

      expect(mockProjectSharingService.guestListCraneEquipment).toHaveBeenCalledWith(
        {
          ProjectId: 1,
          pageSize: 0,
          pageNo: 0,
          ParentCompanyId: 2
        },
        { showActivatedAlone: true }
      );
      expect(component.equipmentList).toEqual(mockResponse.data.rows);
      expect(component.equipmentDropdownSettings).toBeDefined();
      expect(component.getCompaniesForEditNdr).toHaveBeenCalled();
    });

    it('should get companies list successfully', () => {
      const mockResponse = { data: [{ id: 1, companyName: 'Company 1' }] };
      mockProjectSharingService.guestGetCompanies.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getDefinableForEditNdr').mockImplementation();

      component.getCompaniesForEditNdr();

      expect(mockProjectSharingService.guestGetCompanies).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 2
      });
      expect(component.companyList).toEqual(mockResponse.data);
      expect(component.editNdrCompanyDropdownSettings).toBeDefined();
      expect(component.getDefinableForEditNdr).toHaveBeenCalled();
    });

    it('should handle null response in getCompaniesForEditNdr', () => {
      mockProjectSharingService.guestGetCompanies.mockReturnValue(of(null));
      jest.spyOn(component, 'getDefinableForEditNdr').mockImplementation();

      component.getCompaniesForEditNdr();

      expect(component.getDefinableForEditNdr).not.toHaveBeenCalled();
    });

    it('should get definable work list successfully', () => {
      const mockResponse = { data: [{ id: 1, DFOW: 'Work 1' }] };
      mockProjectSharingService.guestGetDefinableWork.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getLocationForEditNdr').mockImplementation();

      component.getDefinableForEditNdr();

      expect(mockProjectSharingService.guestGetDefinableWork).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 2
      });
      expect(component.definableFeatureOfWorkList).toEqual(mockResponse.data);
      expect(component.editNdrDefinableDropdownSettings).toBeDefined();
      expect(component.getLocationForEditNdr).toHaveBeenCalled();
    });

    it('should handle null response in getDefinableForEditNdr', () => {
      mockProjectSharingService.guestGetDefinableWork.mockReturnValue(of(null));
      jest.spyOn(component, 'getLocationForEditNdr').mockImplementation();

      component.getDefinableForEditNdr();

      expect(component.getLocationForEditNdr).not.toHaveBeenCalled();
    });

    it('should get locations list successfully', () => {
      const mockResponse = { data: [{ id: 1, locationPath: 'Location 1' }] };
      mockProjectSharingService.guestGetLocations.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getEquipmentCraneRequest').mockImplementation();

      component.getLocationForEditNdr();

      expect(mockProjectSharingService.guestGetLocations).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 2
      });
      expect(component.locationList).toEqual(mockResponse.data);
      expect(component.editNdrLocationDropdownSettings).toBeDefined();
      expect(component.getEquipmentCraneRequest).toHaveBeenCalled();
    });

    it('should handle null response in getLocationForEditNdr', () => {
      mockProjectSharingService.guestGetLocations.mockReturnValue(of(null));
      jest.spyOn(component, 'getEquipmentCraneRequest').mockImplementation();

      component.getLocationForEditNdr();

      expect(component.getEquipmentCraneRequest).not.toHaveBeenCalled();
    });

    it('should get members list successfully', () => {
      const mockResponse = { data: [{ id: 1, name: 'Member 1' }] };
      mockProjectSharingService.listAllMember.mockReturnValue(of(mockResponse));

      component.getMembers();

      expect(mockProjectSharingService.listAllMember).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 2
      });
      expect(component.memberList).toEqual(mockResponse.data);
    });

    it('should handle null response in getMembers', () => {
      mockProjectSharingService.listAllMember.mockReturnValue(of(null));

      component.getMembers();

      expect(component.memberList).toEqual([{ id: 1, email: '<EMAIL>' }]); // Should remain unchanged
    });
  });

  describe('Form Validation', () => {
    it('should create form with correct validators', () => {
      component.editCraneRequestForm();

      expect(component.craneEditRequest.get('description').hasError('required')).toBe(true);
      expect(component.craneEditRequest.get('LocationId').hasError('required')).toBe(true);
      expect(component.craneEditRequest.get('deliveryDate').hasError('required')).toBe(true);
      expect(component.craneEditRequest.get('craneDeliveryStart').hasError('required')).toBe(true);
      expect(component.craneEditRequest.get('craneDeliveryEnd').hasError('required')).toBe(true);
    });

    it('should validate delivery dates correctly', () => {
      component.deliveryWindowTime = 30;
      component.deliveryWindowTimeUnit = 'minutes';

      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);

      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);

      expect(component.checkEditDeliveryFutureDate(futureDate, futureDate)).toBe(true);
      expect(component.checkEditDeliveryFutureDate(pastDate, pastDate)).toBe(false);
    });

    it('should validate start and end times correctly', () => {
      const startDate = new Date();
      const endDate = new Date(startDate);
      endDate.setHours(endDate.getHours() + 1);

      expect(component.checkStartEnd(startDate, endDate)).toBe(true);
      expect(component.checkStartEnd(endDate, startDate)).toBe(false);
    });

    it('should validate string empty values', () => {
      expect(component.checkEditDeliveryStringEmptyValues({ description: '', additionalNotes: '' })).toBe(true);
      expect(component.checkEditDeliveryStringEmptyValues({ description: '   ', additionalNotes: '' })).toBe(true);
      expect(component.checkEditDeliveryStringEmptyValues({ description: 'Valid', additionalNotes: '   ' })).toBe(true);
      expect(component.checkEditDeliveryStringEmptyValues({ description: 'Valid', additionalNotes: 'Valid' })).toBe(false);
      expect(component.checkEditDeliveryStringEmptyValues({ description: 'Valid', additionalNotes: null })).toBe(false);
    });
  });

  describe('Utility Methods', () => {
    it('should convert start time correctly', () => {
      const date = new Date(2023, 5, 15); // June 15, 2023
      const result = component.convertStart(date, 10, 30);

      expect(result.getFullYear()).toBe(2023);
      expect(result.getMonth()).toBe(5);
      expect(result.getDate()).toBe(15);
      expect(result.getHours()).toBe(10);
      expect(result.getMinutes()).toBe(30);
    });

    it('should validate number only input', () => {
      expect(component.numberOnly({ which: 48, keyCode: 48 })).toBe(true); // '0'
      expect(component.numberOnly({ which: 57, keyCode: 57 })).toBe(true); // '9'
      expect(component.numberOnly({ which: 65, keyCode: 65 })).toBe(false); // 'A'
      expect(component.numberOnly({ which: 32, keyCode: 32 })).toBe(false); // Space (not allowed)
    });

    it('should check array differences by property', () => {
      const array1 = [{ name: 'A' }, { name: 'B' }];
      const array2 = [{ name: 'A' }, { name: 'C' }];
      const array3 = [{ name: 'A' }, { name: 'B' }];

      expect(component.areDifferentByProperty(array1, array2, 'name')).toBe(true);
      expect(component.areDifferentByProperty(array1, array3, 'name')).toBe(false);
    });

    it('should open content modal', () => {
      component.openContentModal();
      expect(component.modalLoader).toBe(false);
    });

    it('should throw error with correct message', () => {
      component.throwError('time error');
      expect(mockToastr.error).toHaveBeenCalledWith('Please Enter Start time Lesser than End time');

      component.throwError('other error');
      expect(mockToastr.error).toHaveBeenCalledWith(
        'Booking not allowed to edit. Please contact the project administrator to edit this booking'
      );
    });

    it('should reset form correctly', () => {
      component.formReset();
      expect(component.formEditSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
    });
  });

  describe('Modal Operations', () => {
    it('should reset form with no action', () => {
      component.resetForm('no');
      expect(mockModalRef1.hide).toHaveBeenCalled();
    });

    it('should reset form with yes action', () => {
      component.resetForm('yes');
      expect(mockModalRef.hide).toHaveBeenCalled();
      expect(component.formEditSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(component.NDRTimingChanged).toBe(false);
    });

    it('should open confirmation modal', () => {
      const mockTemplate = {} as any;
      component.openConfirmationModalPopupForEditNDR(mockTemplate);

      expect(mockModalService.show).toHaveBeenCalledWith(mockTemplate, {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
      });
    });

    it('should close form and navigate', () => {
      component.closeForm();
      expect(mockModalRef.hide).toHaveBeenCalled();
      expect(component.formEditSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(component.NDRTimingChanged).toBe(false);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/guest-crane-calendar']);
    });
  });

  describe('Location Handling', () => {
    it('should select location correctly', () => {
      component.locationList = [{ id: 1, locationPath: 'Location 1' }];
      component.locationSelected({ id: 1 });

      expect(component.getChosenLocation).toEqual([{ id: 1, locationPath: 'Location 1' }]);
      expect(component.selectedLocationId).toBe(1);
    });

    it('should set location from current edit item', () => {
      component.currentEditItem = {
        location: { id: 1, locationPath: 'Test Location' }
      };

      component.setlocation();

      expect(component.getChosenLocation).toEqual([{ id: 1, locationPath: 'Test Location' }]);
      expect(component.selectedLocationId).toBe(1);
    });

    it('should handle missing location in current edit item', () => {
      component.currentEditItem = {};
      component.setlocation();

      expect(component.getChosenLocation).toBeUndefined();
    });
  });

  describe('Date and Time Handling', () => {
    it('should change date and update end time', () => {
      const mockEvent = new Date(2023, 5, 15, 10, 30);
      jest.spyOn(component, 'onEditSubmitForm').mockImplementation();

      component.changeDate(mockEvent);

      expect(component.deliveryEnd.getHours()).toBe(11); // Start + 1 hour
      expect(component.deliveryEnd.getMinutes()).toBe(30);
      expect(component.NDRTimingChanged).toBe(true);
      expect(component.onEditSubmitForm).toHaveBeenCalled();
    });

    it('should handle delivery end time change detection', () => {
      jest.spyOn(component, 'onEditSubmitForm').mockImplementation();

      component.deliveryEndTimeChangeDetection();

      expect(component.NDRTimingChanged).toBe(true);
      expect(component.onEditSubmitForm).toHaveBeenCalled();
    });

    it('should handle onEditSubmitForm', () => {
      component.craneEditRequest.markAsDirty();
      component.onEditSubmitForm();

      expect(component.formEdited).toBe(false);
    });
  });

  describe('Form Submission', () => {
    beforeEach(() => {
      // Set up valid form data
      component.craneEditRequest.patchValue({
        EquipmentId: [{ id: 1 }],
        description: 'Test Description',
        deliveryDate: new Date(),
        craneDeliveryStart: new Date(),
        craneDeliveryEnd: new Date(Date.now() + 3600000), // 1 hour later
        LocationId: 1,
        companies: [{ id: 1 }],
        responsiblePersons: [{ id: 1 }],
        additionalNotes: 'Test Notes'
      });
      component.selectedLocationId = 1;
    });

    it('should handle successful form submission', () => {
      const mockResponse = { message: 'Success' };
      mockProjectSharingService.guestEditCraneRequest.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'editCraneRequestSuccess').mockImplementation();

      component.editCraneRequest({});

      expect(component.formEditSubmitted).toBe(true);
      expect(mockProjectSharingService.guestEditCraneRequest).toHaveBeenCalled();
    });

    it('should handle form submission error with status code 400', () => {
      const mockError = {
        message: {
          statusCode: 400,
          details: [{ field: 'Error message' }]
        }
      };
      mockProjectSharingService.guestEditCraneRequest.mockReturnValue(throwError(() => mockError));
      jest.spyOn(component, 'showError').mockImplementation();

      component.editCraneRequest({});

      expect(component.showError).toHaveBeenCalledWith(mockError);
    });

    it('should handle form submission error without message', () => {
      const mockError = {};
      mockProjectSharingService.guestEditCraneRequest.mockReturnValue(throwError(() => mockError));

      component.editCraneRequest({});

      expect(mockToastr.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should handle form submission error with message', () => {
      const mockError = { message: 'Custom error message' };
      mockProjectSharingService.guestEditCraneRequest.mockReturnValue(throwError(() => mockError));

      component.editCraneRequest({});

      expect(mockToastr.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
    });

    it('should handle successful edit crane request', () => {
      jest.spyOn(component, 'formReset').mockImplementation();
      jest.spyOn(component, 'closeForm').mockImplementation();

      component.editCraneRequestSuccess({ message: 'Success' });

      expect(mockToastr.success).toHaveBeenCalledWith('Success', 'Success');
      expect(component.formReset).toHaveBeenCalled();
      expect(component.closeForm).toHaveBeenCalled();
      expect(component.NDRTimingChanged).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
    });

    it('should show error correctly', () => {
      const mockError = {
        message: {
          details: [{ field: 'Error message' }]
        }
      };

      component.showError(mockError);

      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(mockToastr.error).toHaveBeenCalledWith(['Error message']);
    });
  });

  describe('Gate Check and Validation', () => {
    it('should validate person selection', () => {
      component.memberList = [{ id: 1 }, { id: 2 }];

      component.gatecheck([{ id: 1 }, { id: 3 }], 'Person');
      expect(component.errmemberenable).toBe(true);

      component.gatecheck([{ id: 1 }, { id: 2 }], 'Person');
      expect(component.errmemberenable).toBe(false);
    });

    it('should get index value correctly', () => {
      component.memberList = [{ id: 1 }, { id: 2 }];

      const formValue1 = { responsiblePersons: [{ id: 1 }, { id: 3 }] };
      expect(component.getIndexValue(formValue1)).toBe(-1);

      const formValue2 = { responsiblePersons: [{ id: 1 }, { id: 2 }] };
      expect(component.getIndexValue(formValue2)).toBe(0);
    });
  });

  describe('Autocomplete', () => {
    it('should return autocomplete items', () => {
      const mockResponse = [{ id: 1, name: 'Member 1' }];
      mockProjectSharingService.guestSearchNewMember.mockReturnValue(of(mockResponse));

      const result = component.requestAutoEditcompleteItems('test');

      expect(mockProjectSharingService.guestSearchNewMember).toHaveBeenCalledWith({
        ProjectId: 1,
        search: 'test',
        ParentCompanyId: 2
      });

      result.subscribe(data => {
        expect(data).toEqual(mockResponse);
      });
    });

    it('should handle empty search text', () => {
      const mockResponse = [];
      mockProjectSharingService.guestSearchNewMember.mockReturnValue(of(mockResponse));

      const result = component.requestAutoEditcompleteItems('');

      expect(mockProjectSharingService.guestSearchNewMember).toHaveBeenCalledWith({
        ProjectId: 1,
        search: '',
        ParentCompanyId: 2
      });

      result.subscribe(data => {
        expect(data).toEqual(mockResponse);
      });
    });
  });

  describe('Equipment Handling', () => {
    it('should set equipment from current edit item', () => {
      component.currentEditItem = {
        equipmentDetails: [
          { Equipment: { id: 1, equipmentName: 'Crane 1' } },
          { Equipment: { id: 2, equipmentName: 'Crane 2' } }
        ]
      };

      component.setEquipment();

      expect(component.editBeforeEquipment).toEqual([
        { id: 1, equipmentName: 'Crane 1' },
        { id: 2, equipmentName: 'Crane 2' }
      ]);
      expect(component.craneEditRequest.get('EquipmentId').value).toEqual([
        { id: 1, equipmentName: 'Crane 1' },
        { id: 2, equipmentName: 'Crane 2' }
      ]);
    });

    it('should handle undefined equipment details', () => {
      component.currentEditItem = { equipmentDetails: undefined };

      component.setEquipment();

      expect(component.editBeforeEquipment).toEqual([]);
      expect(component.craneEditRequest.get('EquipmentId').value).toEqual([]);
    });

    it('should handle empty equipment details array', () => {
      component.currentEditItem = { equipmentDetails: [] };

      component.setEquipment();

      expect(component.editBeforeEquipment).toEqual([]);
      expect(component.craneEditRequest.get('EquipmentId').value).toEqual([]);
    });
  });

  describe('Member Handling - setMember', () => {
    beforeEach(() => {
      mockProjectSharingService.guestGetMemberRole.mockReturnValue(of({
        data: { User: { email: '<EMAIL>' }, RoleId: 1 }
      }));
    });

    it('should set members with full names', () => {
      component.currentEditItem = {
        memberDetails: [
          {
            Member: {
              id: 1,
              User: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' }
            }
          },
          {
            Member: {
              id: 2,
              User: { firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>' }
            }
          }
        ]
      };

      component.setMember();

      expect(mockProjectSharingService.guestGetMemberRole).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 2,
        id: 3
      });
    });

    it('should set members without first names', () => {
      component.currentEditItem = {
        memberDetails: [
          {
            Member: {
              id: 1,
              User: { firstName: null, lastName: null, email: '<EMAIL>' }
            }
          }
        ]
      };

      component.setMember();

      expect(mockProjectSharingService.guestGetMemberRole).toHaveBeenCalled();
    });

    it('should mark current user as readonly', () => {
      component.currentEditItem = {
        memberDetails: [
          {
            Member: {
              id: 1,
              User: { firstName: 'Admin', lastName: 'User', email: '<EMAIL>' }
            }
          }
        ]
      };

      component.setMember();

      expect(mockProjectSharingService.guestGetMemberRole).toHaveBeenCalled();
    });

    it('should handle undefined member details', () => {
      component.currentEditItem = { memberDetails: undefined };

      component.setMember();

      expect(mockProjectSharingService.guestGetMemberRole).toHaveBeenCalled();
    });

    it('should skip members without email', () => {
      component.currentEditItem = {
        memberDetails: [
          {
            Member: {
              id: 1,
              User: { firstName: 'John', lastName: 'Doe', email: null }
            }
          }
        ]
      };

      component.setMember();

      expect(mockProjectSharingService.guestGetMemberRole).toHaveBeenCalled();
    });
  });

  describe('Company Handling - setCompany', () => {
    it('should set companies from current edit item', () => {
      component.currentEditItem = {
        companyDetails: [
          { Company: { id: 1, companyName: 'Company A' } },
          { Company: { id: 2, companyName: 'Company B' } }
        ]
      };

      component.setCompany();

      expect(component.editBeforeCompany).toEqual([
        { id: 1, companyName: 'Company A' },
        { id: 2, companyName: 'Company B' }
      ]);
      expect(component.craneEditRequest.get('companies').value).toEqual([
        { id: 1, companyName: 'Company A' },
        { id: 2, companyName: 'Company B' }
      ]);
    });

    it('should handle undefined company details', () => {
      component.currentEditItem = { companyDetails: undefined };

      component.setCompany();

      expect(component.editBeforeCompany).toEqual([]);
      expect(component.craneEditRequest.get('companies').value).toEqual([]);
    });
  });

  describe('Definable Work Handling - setDefine', () => {
    it('should set definable work from current edit item', () => {
      component.currentEditItem = {
        defineWorkDetails: [
          { DeliverDefineWork: { id: 1, DFOW: 'Work A' } },
          { DeliverDefineWork: { id: 2, DFOW: 'Work B' } }
        ]
      };

      component.setDefine();

      expect(component.editBeforeDFOW).toEqual([
        { id: 1, DFOW: 'Work A' },
        { id: 2, DFOW: 'Work B' }
      ]);
      expect(component.craneEditRequest.get('definableFeatureOfWorks').value).toEqual([
        { id: 1, DFOW: 'Work A' },
        { id: 2, DFOW: 'Work B' }
      ]);
    });

    it('should handle undefined define work details', () => {
      component.currentEditItem = { defineWorkDetails: undefined };

      component.setDefine();

      expect(component.editBeforeDFOW).toEqual([]);
      expect(component.craneEditRequest.get('definableFeatureOfWorks').value).toEqual([]);
    });
  });

  describe('Equipment Crane Request Loading', () => {
    beforeEach(() => {
      mockProjectSharingService.guestGetEquipmentCraneRequest.mockReturnValue(of({
        data: {
          id: 1,
          CraneRequestId: 'CR001',
          description: 'Test Description',
          craneDeliveryStart: new Date('2023-06-15T10:00:00'),
          craneDeliveryEnd: new Date('2023-06-15T11:00:00'),
          escort: true,
          additionalNotes: 'Test Notes',
          pickUpLocation: 'Pickup Point',
          dropOffLocation: 'Drop Point',
          isAssociatedWithDeliveryRequest: false,
          location: { id: 1, locationPath: 'Test Location' },
          recurrence: {
            id: 1,
            recurrence: 'daily',
            recurrenceEndDate: new Date('2023-12-31')
          }
        }
      }));
      jest.spyOn(component, 'setlocation').mockImplementation();
      jest.spyOn(component, 'setCompany').mockImplementation();
      jest.spyOn(component, 'setDefine').mockImplementation();
      jest.spyOn(component, 'setMember').mockImplementation();
      jest.spyOn(component, 'openContentModal').mockImplementation();
    });

    it('should get equipment crane request and populate form', () => {
      component.getEquipmentCraneRequest();

      expect(mockProjectSharingService.guestGetEquipmentCraneRequest).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 2,
        id: 1
      });
    });

    it('should handle equipment crane request with recurrence', () => {
      component.getEquipmentCraneRequest();

      expect(component.setlocation).toHaveBeenCalled();
      expect(component.setCompany).toHaveBeenCalled();
      expect(component.setDefine).toHaveBeenCalled();
      expect(component.setMember).toHaveBeenCalled();
      expect(component.openContentModal).toHaveBeenCalled();
    });

    it('should handle equipment crane request without recurrence', () => {
      mockProjectSharingService.guestGetEquipmentCraneRequest.mockReturnValue(of({
        data: {
          id: 1,
          description: 'Test Description',
          craneDeliveryStart: new Date('2023-06-15T10:00:00'),
          craneDeliveryEnd: new Date('2023-06-15T11:00:00'),
          recurrence: null
        }
      }));

      component.getEquipmentCraneRequest();

      expect(component.setlocation).toHaveBeenCalled();
    });

    it('should handle equipment crane request without recurrence end date', () => {
      mockProjectSharingService.guestGetEquipmentCraneRequest.mockReturnValue(of({
        data: {
          id: 1,
          description: 'Test Description',
          craneDeliveryStart: new Date('2023-06-15T10:00:00'),
          craneDeliveryEnd: new Date('2023-06-15T11:00:00'),
          recurrence: {
            id: 1,
            recurrence: 'daily',
            recurrenceEndDate: null
          }
        }
      }));

      component.getEquipmentCraneRequest();

      expect(component.setlocation).toHaveBeenCalled();
    });
  });

  describe('Form Close Handling', () => {
    beforeEach(() => {
      jest.spyOn(component, 'openConfirmationModalPopupForEditNDR').mockImplementation();
      jest.spyOn(component, 'resetForm').mockImplementation();
      jest.spyOn(component, 'areDifferentByProperty').mockReturnValue(false);
    });

    it('should open confirmation modal when form is touched', () => {
      const mockTemplate = {} as any;
      component.craneEditRequest.markAsTouched();

      component.close(mockTemplate);

      expect(component.openConfirmationModalPopupForEditNDR).toHaveBeenCalledWith(mockTemplate);
    });

    it('should open confirmation modal when NDR timing changed', () => {
      const mockTemplate = {} as any;
      component.NDRTimingChanged = true;

      component.close(mockTemplate);

      expect(component.openConfirmationModalPopupForEditNDR).toHaveBeenCalledWith(mockTemplate);
    });

    it('should open confirmation modal when definable work changed', () => {
      const mockTemplate = {} as any;
      component.craneEditRequest.get('definableFeatureOfWorks').markAsDirty();
      component.craneEditRequest.get('definableFeatureOfWorks').setValue([{ id: 1, DFOW: 'Work' }]);
      jest.spyOn(component, 'areDifferentByProperty').mockReturnValue(true);

      component.close(mockTemplate);

      expect(component.openConfirmationModalPopupForEditNDR).toHaveBeenCalledWith(mockTemplate);
    });

    it('should open confirmation modal when companies changed', () => {
      const mockTemplate = {} as any;
      component.craneEditRequest.get('companies').markAsDirty();
      component.craneEditRequest.get('companies').setValue([{ id: 1, companyName: 'Company' }]);
      jest.spyOn(component, 'areDifferentByProperty').mockReturnValue(true);

      component.close(mockTemplate);

      expect(component.openConfirmationModalPopupForEditNDR).toHaveBeenCalledWith(mockTemplate);
    });

    it('should open confirmation modal when equipment changed', () => {
      const mockTemplate = {} as any;
      component.craneEditRequest.get('EquipmentId').markAsDirty();
      component.craneEditRequest.get('EquipmentId').setValue([{ id: 1, equipmentName: 'Equipment' }]);
      jest.spyOn(component, 'areDifferentByProperty').mockReturnValue(true);

      component.close(mockTemplate);

      expect(component.openConfirmationModalPopupForEditNDR).toHaveBeenCalledWith(mockTemplate);
    });

    it('should reset form directly when no changes detected', () => {
      const mockTemplate = {} as any;

      component.close(mockTemplate);

      expect(component.resetForm).toHaveBeenCalledWith('yes');
    });
  });

  describe('Payload Building', () => {
    it('should build edit crane request payload correctly', () => {
      const formValue = {
        id: 1,
        description: 'Test Description',
        isEscortNeeded: true,
        additionalNotes: 'Test Notes',
        pickUpLocation: 'Pickup',
        dropOffLocation: 'Drop',
        recurrenceId: 1,
        recurrenceEndDate: '12/31/2023',
        craneDeliveryStart: new Date('2023-06-15T10:00:00'),
        craneDeliveryEnd: new Date('2023-06-15T11:00:00')
      };
      const deliveryStart = new Date('2023-06-15T10:00:00');
      const deliveryEnd = new Date('2023-06-15T11:00:00');
      const companies = [1, 2];
      const responsiblePersons = [1, 2];
      const definableFeatureOfWorks = [1];
      const equipments = [1, 2];

      component.selectedLocationId = 1;
      component.seriesOption = 1;

      const payload = component.buildEditCraneRequestPayload(
        formValue,
        deliveryStart,
        deliveryEnd,
        companies,
        responsiblePersons,
        definableFeatureOfWorks,
        equipments
      );

      expect(payload).toEqual({
        id: 1,
        description: 'Test Description',
        companies,
        isEscortNeeded: true,
        ProjectId: 1,
        userId: 3,
        additionalNotes: 'Test Notes',
        EquipmentId: equipments,
        LocationId: 1,
        craneDeliveryStart: deliveryStart,
        craneDeliveryEnd: deliveryEnd,
        ParentCompanyId: 2,
        responsiblePersons,
        definableFeatureOfWorks,
        isAssociatedWithDeliveryRequest: false,
        pickUpLocation: 'Pickup',
        dropOffLocation: 'Drop',
        seriesOption: 1,
        recurrenceId: 1,
        recurrenceEndDate: '2023-12-31',
        recurrenceSeriesStartDate: '2023-06-15',
        recurrenceSeriesEndDate: '2023-06-15',
        previousSeriesRecurrenceEndDate: '2023-06-14',
        nextSeriesRecurrenceStartDate: '2023-06-16',
        deliveryStartTime: '10:00',
        deliveryEndTime: '11:00',
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      });
    });

    it('should handle null escort condition', () => {
      const formValue = {
        id: 1,
        description: 'Test',
        isEscortNeeded: null,
        craneDeliveryStart: new Date(),
        craneDeliveryEnd: new Date()
      };

      const payload = component.buildEditCraneRequestPayload(
        formValue,
        new Date(),
        new Date(),
        [],
        [],
        [],
        []
      );

      expect(payload.isEscortNeeded).toBe(false);
    });

    it('should handle undefined escort condition', () => {
      const formValue = {
        id: 1,
        description: 'Test',
        isEscortNeeded: undefined,
        craneDeliveryStart: new Date(),
        craneDeliveryEnd: new Date()
      };

      const payload = component.buildEditCraneRequestPayload(
        formValue,
        new Date(),
        new Date(),
        [],
        [],
        [],
        []
      );

      expect(payload.isEscortNeeded).toBe(false);
    });

    it('should handle null recurrence end date', () => {
      const formValue = {
        id: 1,
        description: 'Test',
        recurrenceEndDate: null,
        craneDeliveryStart: new Date(),
        craneDeliveryEnd: new Date()
      };

      const payload = component.buildEditCraneRequestPayload(
        formValue,
        new Date(),
        new Date(),
        [],
        [],
        [],
        []
      );

      expect(payload.recurrenceEndDate).toBe(null);
    });
  });

  describe('Update Crane Delivery', () => {
    beforeEach(() => {
      jest.spyOn(component, 'checkStartEnd').mockReturnValue(true);
      jest.spyOn(component, 'checkEditDeliveryStringEmptyValues').mockReturnValue(false);
      jest.spyOn(component, 'buildEditCraneRequestPayload').mockReturnValue({});
      jest.spyOn(component, 'editCraneRequest').mockImplementation();
      jest.spyOn(component, 'formReset').mockImplementation();
      jest.spyOn(component, 'throwError').mockImplementation();
    });

    it('should handle invalid start/end time', () => {
      jest.spyOn(component, 'checkStartEnd').mockReturnValue(false);

      const formValue = { companies: [{ id: 1 }], responsiblePersons: [{ id: 1 }] };
      component.updateCraneDelivery(formValue, new Date(), new Date(), [], [], [], []);

      expect(component.throwError).toHaveBeenCalledWith('time error');
    });

    it('should handle missing companies', () => {
      const formValue = { companies: [], responsiblePersons: [{ id: 1 }] };
      component.updateCraneDelivery(formValue, new Date(), new Date(), [], [], [], []);

      expect(component.formReset).toHaveBeenCalled();
      expect(mockToastr.error).toHaveBeenCalledWith('Responsible Company is required');
    });

    it('should handle null companies', () => {
      const formValue = { companies: null, responsiblePersons: [{ id: 1 }] };
      component.updateCraneDelivery(formValue, new Date(), new Date(), [], [], [], []);

      expect(component.formReset).toHaveBeenCalled();
      expect(mockToastr.error).toHaveBeenCalledWith('Responsible Company is required');
    });

    it('should handle missing responsible persons', () => {
      const formValue = { companies: [{ id: 1 }], responsiblePersons: [] };
      component.updateCraneDelivery(formValue, new Date(), new Date(), [], [], [], []);

      expect(component.formReset).toHaveBeenCalled();
      expect(mockToastr.error).toHaveBeenCalledWith('Responsible Person is required');
    });

    it('should handle null responsible persons', () => {
      const formValue = { companies: [{ id: 1 }], responsiblePersons: null };
      component.updateCraneDelivery(formValue, new Date(), new Date(), [], [], [], []);

      expect(component.formReset).toHaveBeenCalled();
      expect(mockToastr.error).toHaveBeenCalledWith('Responsible Person is required');
    });

    it('should handle string validation errors', () => {
      jest.spyOn(component, 'checkEditDeliveryStringEmptyValues').mockReturnValue(true);

      const formValue = { companies: [{ id: 1 }], responsiblePersons: [{ id: 1 }] };
      component.updateCraneDelivery(formValue, new Date(), new Date(), [], [], [], []);

      expect(component.formReset).toHaveBeenCalled();
    });

    it('should process valid form data successfully', () => {
      const formValue = {
        companies: [{ id: 1 }],
        responsiblePersons: [{ id: 1 }],
        definableFeatureOfWorks: [{ id: 1 }],
        EquipmentId: [{ id: 1 }]
      };

      component.updateCraneDelivery(formValue, new Date(), new Date(), [], [], [], []);

      expect(component.buildEditCraneRequestPayload).toHaveBeenCalled();
      expect(component.editCraneRequest).toHaveBeenCalled();
    });

    it('should handle empty definable feature of works', () => {
      const formValue = {
        companies: [{ id: 1 }],
        responsiblePersons: [{ id: 1 }],
        definableFeatureOfWorks: null,
        EquipmentId: [{ id: 1 }]
      };

      component.updateCraneDelivery(formValue, new Date(), new Date(), [], [], [], []);

      expect(component.editCraneRequest).toHaveBeenCalled();
    });

    it('should handle empty equipment list', () => {
      const formValue = {
        companies: [{ id: 1 }],
        responsiblePersons: [{ id: 1 }],
        EquipmentId: null
      };

      component.updateCraneDelivery(formValue, new Date(), new Date(), [], [], [], []);

      expect(component.editCraneRequest).toHaveBeenCalled();
    });
  });

  describe('Check Request Status and User Role', () => {
    beforeEach(() => {
      jest.spyOn(component, 'updateCraneDelivery').mockImplementation();
      jest.spyOn(component, 'throwError').mockImplementation();
      jest.spyOn(component, 'checkEditDeliveryFutureDate').mockReturnValue(true);

      component.currentEditItem = {
        status: 'Pending',
        craneDeliveryStart: new Date('2023-06-15T10:00:00'),
        craneDeliveryEnd: new Date('2023-06-15T11:00:00'),
        deliveryDate: new Date('2023-06-15')
      };
      component.authUser = { RoleId: 1 };
    });

    it('should allow editing for pending status', () => {
      const formValue = {};
      const deliveryStart = new Date();
      const deliveryEnd = new Date();

      component.checkRequestStatusAndUserRole(formValue, deliveryStart, deliveryEnd, [], [], [], []);

      expect(component.updateCraneDelivery).toHaveBeenCalledWith(
        formValue, deliveryStart, deliveryEnd, [], [], [], []
      );
    });

    it('should prevent date/time changes for completed status (non-admin)', () => {
      component.currentEditItem.status = 'Completed';
      component.authUser.RoleId = 1; // Non-admin

      // Set different date
      component.craneEditRequest.get('deliveryDate').setValue(new Date('2023-06-16'));

      const formValue = {};
      component.checkRequestStatusAndUserRole(formValue, new Date(), new Date(), [], [], [], []);

      expect(mockToastr.error).toHaveBeenCalledWith('You are not allowed to change the date/time ');
      expect(component.formEditSubmitted).toBe(false);
    });

    it('should allow date/time changes for completed status (admin)', () => {
      component.currentEditItem.status = 'Completed';
      component.authUser.RoleId = 2; // Admin

      const formValue = {};
      component.checkRequestStatusAndUserRole(formValue, new Date(), new Date(), [], [], [], []);

      expect(component.updateCraneDelivery).toHaveBeenCalled();
    });

    it('should handle approved status with future date validation', () => {
      component.currentEditItem.status = 'Approved';
      component.authUser.RoleId = 1; // Non-admin
      jest.spyOn(component, 'checkEditDeliveryFutureDate').mockReturnValue(false);

      // Set different date
      component.craneEditRequest.get('deliveryDate').setValue(new Date('2023-06-16'));

      const formValue = {};
      component.checkRequestStatusAndUserRole(formValue, new Date(), new Date(), [], [], [], []);

      expect(component.throwError).toHaveBeenCalledWith('error');
      expect(component.formEditSubmitted).toBe(false);
    });

    it('should allow approved status changes when future date validation passes', () => {
      component.currentEditItem.status = 'Approved';
      component.authUser.RoleId = 1; // Non-admin
      jest.spyOn(component, 'checkEditDeliveryFutureDate').mockReturnValue(true);

      // Set different date
      component.craneEditRequest.get('deliveryDate').setValue(new Date('2023-06-16'));

      const formValue = {};
      component.checkRequestStatusAndUserRole(formValue, new Date(), new Date(), [], [], [], []);

      expect(component.updateCraneDelivery).toHaveBeenCalled();
    });
  });

  describe('Form Submission - onEditSubmit', () => {
    beforeEach(() => {
      jest.spyOn(component, 'convertStart').mockReturnValue(new Date());
      jest.spyOn(component, 'checkEditDeliveryFutureDate').mockReturnValue(true);
      jest.spyOn(component, 'getIndexValue').mockReturnValue(0);
      jest.spyOn(component, 'checkRequestStatusAndUserRole').mockImplementation();

      // Set up valid form
      component.craneEditRequest.patchValue({
        EquipmentId: [{ id: 1 }],
        deliveryDate: new Date(),
        craneDeliveryStart: new Date(),
        craneDeliveryEnd: new Date()
      });
    });

    it('should handle missing equipment', () => {
      component.craneEditRequest.patchValue({ EquipmentId: [] });

      component.onEditSubmit();

      expect(mockToastr.error).toHaveBeenCalledWith('Equipment is required');
      expect(component.formEditSubmitted).toBe(false);
    });

    it('should handle invalid form', () => {
      component.craneEditRequest.get('description').setValue(''); // Make form invalid

      component.onEditSubmit();

      expect(component.formEditSubmitted).toBe(false);
    });

    it('should handle invalid member selection', () => {
      jest.spyOn(component, 'getIndexValue').mockReturnValue(-1);

      component.onEditSubmit();

      expect(component.errmemberenable).toBe(true);
      expect(component.formEditSubmitted).toBe(false);
    });

    it('should proceed with valid form and future date', () => {
      component.onEditSubmit();

      expect(component.checkRequestStatusAndUserRole).toHaveBeenCalled();
    });

    it('should proceed with valid form and past date', () => {
      jest.spyOn(component, 'checkEditDeliveryFutureDate').mockReturnValue(false);

      component.onEditSubmit();

      expect(component.checkRequestStatusAndUserRole).toHaveBeenCalled();
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle service errors in data loading methods', () => {
      mockProjectSharingService.guestGateList.mockReturnValue(throwError(() => new Error('Service error')));

      component.getOverAllGate();

      // Should handle error gracefully without crashing
      expect(mockProjectSharingService.guestGateList).toHaveBeenCalled();
    });

    it('should handle null responses in data loading', () => {
      mockProjectSharingService.guestListCraneEquipment.mockReturnValue(of(null));
      jest.spyOn(component, 'getCompaniesForEditNdr').mockImplementation();

      component.getOverAllEquipmentforEditNdr();

      expect(component.getCompaniesForEditNdr).not.toHaveBeenCalled();
    });

    it('should handle empty arrays in areDifferentByProperty', () => {
      const result1 = component.areDifferentByProperty([], [], 'name');
      expect(result1).toBe(false);

      const result2 = component.areDifferentByProperty([{ name: 'A' }], [], 'name');
      expect(result2).toBe(true);

      const result3 = component.areDifferentByProperty([], [{ name: 'A' }], 'name');
      expect(result3).toBe(true);
    });

    it('should handle special characters in numberOnly validation', () => {
      expect(component.numberOnly({ which: 46, keyCode: 46 })).toBe(false); // Period
      expect(component.numberOnly({ which: 45, keyCode: 45 })).toBe(false); // Minus
      expect(component.numberOnly({ which: 43, keyCode: 43 })).toBe(false); // Plus
      expect(component.numberOnly({ which: 8, keyCode: 8 })).toBe(true); // Backspace
      expect(component.numberOnly({ which: 9, keyCode: 9 })).toBe(true); // Tab
    });

    it('should handle edge cases in checkEditDeliveryFutureDate', () => {
      component.deliveryWindowTime = 0;
      component.deliveryWindowTimeUnit = 'minutes';

      const currentTime = new Date();
      const result = component.checkEditDeliveryFutureDate(currentTime, currentTime);

      expect(result).toBe(false); // Should be false for current time with 0 window
    });

    it('should handle different time units in checkEditDeliveryFutureDate', () => {
      component.deliveryWindowTime = 1;
      component.deliveryWindowTimeUnit = 'hours';

      const futureTime = new Date();
      futureTime.setHours(futureTime.getHours() + 2);

      const result = component.checkEditDeliveryFutureDate(futureTime, futureTime);

      expect(result).toBe(true);
    });

    it('should handle modal operations with null modalRef1', () => {
      component.modalRef1 = null;

      component.resetForm('yes');

      expect(component.formEditSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
    });

    it('should handle changeDate when modalLoader is true', () => {
      component.modalLoader = true;
      jest.spyOn(component, 'onEditSubmitForm').mockImplementation();

      const testDate = new Date('2023-06-15T10:30:00');
      component.changeDate(testDate);

      expect(component.onEditSubmitForm).toHaveBeenCalled();
      // Should not modify deliveryEnd when modalLoader is true
    });

    it('should handle locationSelected with invalid data', () => {
      component.locationList = [{ id: 1, locationPath: 'Location 1' }];

      component.locationSelected({ id: 999 }); // Non-existent ID

      expect(component.getChosenLocation).toEqual([]);
      expect(component.selectedLocationId).toBeUndefined();
    });

    it('should handle setlocation with missing location data', () => {
      component.currentEditItem = { location: null };

      component.setlocation();

      expect(component.getChosenLocation).toBeUndefined();
    });
  });

  describe('Complex Integration Scenarios', () => {
    it('should handle complete form submission workflow with all validations', () => {
      // Setup complete valid form
      component.craneEditRequest.patchValue({
        EquipmentId: [{ id: 1, equipmentName: 'Crane 1' }],
        description: 'Valid Description',
        deliveryDate: new Date('2023-06-15'),
        craneDeliveryStart: new Date('2023-06-15T10:00:00'),
        craneDeliveryEnd: new Date('2023-06-15T11:00:00'),
        LocationId: 1,
        companies: [{ id: 1, companyName: 'Company A' }],
        responsiblePersons: [{ id: 1, email: '<EMAIL>' }],
        additionalNotes: 'Valid Notes',
        isEscortNeeded: true
      });

      component.selectedLocationId = 1;
      component.currentEditItem = { status: 'Pending' };
      component.authUser = { RoleId: 1 };

      jest.spyOn(component, 'checkEditDeliveryFutureDate').mockReturnValue(true);
      jest.spyOn(component, 'getIndexValue').mockReturnValue(0);
      jest.spyOn(component, 'checkStartEnd').mockReturnValue(true);
      jest.spyOn(component, 'checkEditDeliveryStringEmptyValues').mockReturnValue(false);
      jest.spyOn(component, 'editCraneRequest').mockImplementation();

      component.onEditSubmit();

      expect(component.editSubmitted).toBe(true);
      expect(component.editCraneRequest).toHaveBeenCalled();
    });

    it('should handle form submission with recurrence data', () => {
      component.craneEditRequest.patchValue({
        EquipmentId: [{ id: 1 }],
        description: 'Test',
        deliveryDate: new Date(),
        craneDeliveryStart: new Date(),
        craneDeliveryEnd: new Date(),
        recurrenceId: 1,
        recurrenceEndDate: '12/31/2023'
      });

      const payload = component.buildEditCraneRequestPayload(
        component.craneEditRequest.value,
        new Date(),
        new Date(),
        [1],
        [1],
        [],
        [1]
      );

      expect(payload.recurrenceId).toBe(1);
      expect(payload.recurrenceEndDate).toBe('2023-12-31');
    });

    it('should handle multiple error conditions in sequence', () => {
      // Test equipment error first
      component.craneEditRequest.patchValue({ EquipmentId: [] });
      component.onEditSubmit();
      expect(mockToastr.error).toHaveBeenCalledWith('Equipment is required');

      // Reset and test form validation error
      component.craneEditRequest.patchValue({
        EquipmentId: [{ id: 1 }],
        description: '' // Invalid
      });
      component.onEditSubmit();
      expect(component.formEditSubmitted).toBe(false);

      // Reset and test member validation error
      component.craneEditRequest.patchValue({
        EquipmentId: [{ id: 1 }],
        description: 'Valid'
      });
      jest.spyOn(component, 'getIndexValue').mockReturnValue(-1);
      component.onEditSubmit();
      expect(component.errmemberenable).toBe(true);
    });
  });

  describe('Negative Test Cases', () => {
    it('should fail gracefully when localStorage is empty', () => {
      jest.spyOn(Storage.prototype, 'getItem').mockReturnValue(null);

      // This would normally cause errors, but component should handle gracefully
      expect(() => {
        const newComponent = new TestHostComponent(
          TestBed.inject(UntypedFormBuilder),
          mockSocket,
          mockToastr,
          mockRouter,
          mockModalRef,
          mockModalService,
          mockModalRef1,
          mockMixpanelService,
          mockDeliveryService,
          mockProjectSettingsService,
          mockProjectSharingService,
          {}
        );
      }).not.toThrow();
    });

    it('should handle invalid date formats in convertStart', () => {
      const invalidDate = new Date('invalid');
      const result = component.convertStart(invalidDate, 10, 30);

      expect(result).toBeInstanceOf(Date);
      expect(isNaN(result.getTime())).toBe(true);
    });

    it('should handle null/undefined values in form validation', () => {
      const result1 = component.checkEditDeliveryStringEmptyValues({
        description: null as any,
        additionalNotes: null as any
      });
      expect(result1).toBe(true);

      const result2 = component.checkEditDeliveryStringEmptyValues({
        description: undefined as any,
        additionalNotes: undefined as any
      });
      expect(result2).toBe(true);
    });

    it('should handle extreme date values', () => {
      const extremeFutureDate = new Date('2099-12-31');
      const extremePastDate = new Date('1900-01-01');

      expect(component.checkStartEnd(extremePastDate, extremeFutureDate)).toBe(true);
      expect(component.checkStartEnd(extremeFutureDate, extremePastDate)).toBe(false);
    });

    it('should handle concurrent form submissions', () => {
      component.formEditSubmitted = true;

      // Attempt another submission while one is in progress
      component.onEditSubmit();

      // Should not proceed with submission
      expect(component.editSubmitted).toBe(true);
    });

    it('should handle malformed API responses', () => {
      mockProjectSharingService.guestGetEquipmentCraneRequest.mockReturnValue(of({
        data: {
          // Missing required fields
          id: null,
          description: null
        }
      }));

      expect(() => component.getEquipmentCraneRequest()).not.toThrow();
    });
  });
});
