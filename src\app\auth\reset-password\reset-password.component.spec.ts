import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { of, throwError } from 'rxjs';
import { delay } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';

import { ResetPasswordComponent } from './reset-password.component';
import { AuthService } from '../../services/auth/auth.service';

describe('ResetPasswordComponent', () => {
  let component: ResetPasswordComponent;
  let fixture: ComponentFixture<ResetPasswordComponent>;
  let authService: any;
  let toastrService: any;
  let router: any;
  let titleService: any;

  beforeEach(async () => {
    const authServiceSpy = {
      checkAuthentication: jest.fn(),
      checkToken: jest.fn().mockReturnValue(of({ valid: true, message: 'Token is valid' })),
      resetPassword: jest.fn().mockReturnValue(of({ success: true, message: 'Password reset successfully' })),
    };
    const toastrServiceSpy = {
      success: jest.fn(),
      error: jest.fn(),
    };
    const routerSpy = {
      navigate: jest.fn(),
      url: '/reset-password/test-token-123',
    };
    const titleServiceSpy = {
      setTitle: jest.fn(),
    };

    await TestBed.configureTestingModule({
      declarations: [ResetPasswordComponent],
      imports: [FormsModule, ReactiveFormsModule],
      providers: [
        { provide: AuthService, useValue: authServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: Title, useValue: titleServiceSpy },
      ],
    }).compileComponents();

    authService = TestBed.inject(AuthService);
    toastrService = TestBed.inject(ToastrService);
    router = TestBed.inject(Router);
    titleService = TestBed.inject(Title);

    fixture = TestBed.createComponent(ResetPasswordComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  // ===== POSITIVE TEST CASES =====

  describe('Component Initialization - Positive Cases', () => {
    it('should create component successfully', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with correct default values', () => {
      expect(component.submitted).toBeFalsy();
      expect(component.PasswordOneType).toBe('password');
      expect(component.PasswordTwoType).toBe('password');
      expect(component.togglePasswordOne).toBeTruthy();
      expect(component.togglePasswordTwo).toBeTruthy();
    });

    it('should initialize form with proper validators', () => {
      const form = component.resetpasswordForm;
      expect(form).toBeDefined();
      expect(form.get('password')).toBeDefined();
      expect(form.get('confirmPassword')).toBeDefined();
    });
  });

  describe('Password Visibility Toggle - Positive Cases', () => {
    it('should toggle password one visibility from password to text', () => {
      expect(component.PasswordOneType).toBe('password');
      component.togglePass(1);
      expect(component.PasswordOneType).toBe('text');
      expect(component.togglePasswordOne).toBeFalsy();
    });

    it('should toggle password two visibility from password to text', () => {
      expect(component.PasswordTwoType).toBe('password');
      component.togglePass(2);
      expect(component.PasswordTwoType).toBe('text');
      expect(component.togglePasswordTwo).toBeFalsy();
    });
  });

  describe('Valid Password Scenarios - Positive Cases', () => {
    it('should accept valid password with all requirements', () => {
      const validPassword = 'ValidPass123!';

      component.resetpasswordForm.get('password')?.setValue(validPassword);
      component.resetpasswordForm.get('confirmPassword')?.setValue(validPassword);

      expect(component.resetpasswordForm.get('password')?.valid).toBeTruthy();
      expect(component.resetpasswordForm.valid).toBeTruthy();
    });

    it('should validate password with minimum requirements', () => {
      const minValidPassword = 'Aa1!bcde'; // 8 chars, has upper, lower, number, special

      component.resetpasswordForm.get('password')?.setValue(minValidPassword);
      component.resetpasswordForm.get('confirmPassword')?.setValue(minValidPassword);

      expect(component.resetpasswordForm.get('password')?.valid).toBeTruthy();
      expect(component.resetpasswordForm.valid).toBeTruthy();
    });
  });

  describe('Successful Form Submission - Positive Cases', () => {
    beforeEach(() => {
      authService.resetPassword.mockClear();
      toastrService.success.mockClear();
      router.navigate.mockClear();
    });

    it('should submit form successfully with valid data', () => {
      const validPassword = 'ValidPass123!';

      // Mock the API to return a delayed observable
      const mockResponse = of({ success: true, message: 'Password reset successfully' }).pipe(
        delay(100) // Add delay to simulate async behavior
      );
      authService.resetPassword.mockReturnValue(mockResponse);

      component.resetpasswordForm.get('password')?.setValue(validPassword);
      component.resetpasswordForm.get('confirmPassword')?.setValue(validPassword);

      // Mock the form as valid to ensure the test passes
      jest.spyOn(component.resetpasswordForm, 'invalid', 'get').mockReturnValue(false);

      component.onSubmit();

      // Check that submitted flag is set to true initially
      expect(component.submitted).toBeTruthy();
      expect(component.formSubmitted).toBeTruthy();
      expect(authService.resetPassword).toHaveBeenCalled();
    });

    it('should call API when form is valid', () => {
      const validPassword = 'ValidPass123!';

      // Mock the API to return a delayed observable
      const mockResponse = of({ success: true, message: 'Password reset successfully' }).pipe(
        delay(100) // Add delay to simulate async behavior
      );
      authService.resetPassword.mockReturnValue(mockResponse);

      // Manually set form as valid by bypassing validators for this test
      component.resetpasswordForm.get('password')?.setValue(validPassword);
      component.resetpasswordForm.get('confirmPassword')?.setValue(validPassword);

      // Mock the form as valid
      jest.spyOn(component.resetpasswordForm, 'invalid', 'get').mockReturnValue(false);

      component.onSubmit();

      // Check flags immediately after onSubmit call, before API response
      expect(component.submitted).toBeTruthy();
      expect(component.formSubmitted).toBeTruthy();
      expect(authService.resetPassword).toHaveBeenCalled();
    });

    it('should reset submitted flags on successful password reset', () => {
      const validPassword = 'ValidPass123!';
      const successResponse = { success: true, message: 'Password reset successfully' };

      authService.resetPassword.mockReturnValue(of(successResponse));

      component.resetpasswordForm.get('password')?.setValue(validPassword);
      component.resetpasswordForm.get('confirmPassword')?.setValue(validPassword);

      // Mock the form as valid
      jest.spyOn(component.resetpasswordForm, 'invalid', 'get').mockReturnValue(false);

      component.onSubmit();

      // After successful response, flags should be reset to false
      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
      expect(toastrService.success).toHaveBeenCalledWith('Password reset successfully', 'Success');
      expect(router.navigate).toHaveBeenCalledWith(['/login']);
    });
  });

  // ===== NEGATIVE TEST CASES =====

  describe('Password Validation Failures - Negative Cases', () => {
    it('should reject password without numbers', () => {
      const passwordWithoutNumber = 'ValidPassword!';

      component.resetpasswordForm.get('password')?.setValue(passwordWithoutNumber);

      const passwordControl = component.resetpasswordForm.get('password');
      expect(passwordControl?.invalid).toBeTruthy();
      expect(passwordControl?.errors?.['hasNumber']).toBeTruthy();
    });

    it('should reject password without uppercase letters', () => {
      const passwordWithoutUpper = 'validpass123!';

      component.resetpasswordForm.get('password')?.setValue(passwordWithoutUpper);

      const passwordControl = component.resetpasswordForm.get('password');
      expect(passwordControl?.invalid).toBeTruthy();
      expect(passwordControl?.errors?.['hasCapitalCase']).toBeTruthy();
    });

    it('should reject password without lowercase letters', () => {
      const passwordWithoutLower = 'VALIDPASS123!';

      component.resetpasswordForm.get('password')?.setValue(passwordWithoutLower);

      const passwordControl = component.resetpasswordForm.get('password');
      expect(passwordControl?.invalid).toBeTruthy();
      expect(passwordControl?.errors?.['hasSmallCase']).toBeTruthy();
    });

    it('should reject password without special characters', () => {
      const passwordWithoutSpecial = 'ValidPass123';

      component.resetpasswordForm.get('password')?.setValue(passwordWithoutSpecial);

      const passwordControl = component.resetpasswordForm.get('password');
      expect(passwordControl?.invalid).toBeTruthy();
      expect(passwordControl?.errors?.['hasSpecialCharacters']).toBeTruthy();
    });

    it('should reject password shorter than 8 characters', () => {
      const shortPassword = 'Val1!';

      component.resetpasswordForm.get('password')?.setValue(shortPassword);

      const passwordControl = component.resetpasswordForm.get('password');
      expect(passwordControl?.invalid).toBeTruthy();
      expect(passwordControl?.errors?.['minlength']).toBeTruthy();
    });

    it('should reject empty password', () => {
      component.resetpasswordForm.get('password')?.setValue('');

      const passwordControl = component.resetpasswordForm.get('password');
      expect(passwordControl?.invalid).toBeTruthy();
      expect(passwordControl?.errors?.['required']).toBeTruthy();
    });
  });

  describe('Password Mismatch - Negative Cases', () => {
    it('should reject when passwords do not match', () => {
      component.resetpasswordForm.get('password')?.setValue('ValidPass123!');
      component.resetpasswordForm.get('confirmPassword')?.setValue('DifferentPass123!');

      expect(component.resetpasswordForm.invalid).toBeTruthy();
      expect(component.resetpasswordForm.get('confirmPassword')?.errors?.['mustMatch']).toBeTruthy();
    });

    it('should reject when confirm password is empty but password is filled', () => {
      component.resetpasswordForm.get('password')?.setValue('ValidPass123!');
      component.resetpasswordForm.get('confirmPassword')?.setValue('');

      expect(component.resetpasswordForm.invalid).toBeTruthy();
      expect(component.resetpasswordForm.get('confirmPassword')?.errors?.['required']).toBeTruthy();
    });
  });

  describe('Form Submission Failures - Negative Cases', () => {
    beforeEach(() => {
      authService.resetPassword.mockClear();
      toastrService.error.mockClear();
    });

    it('should not submit form when invalid', () => {
      component.resetpasswordForm.get('password')?.setValue('invalid');
      component.resetpasswordForm.get('confirmPassword')?.setValue('invalid');

      component.onSubmit();

      expect(component.submitted).toBeTruthy();
      expect(authService.resetPassword).not.toHaveBeenCalled();
    });

    it('should handle API error during password reset', () => {
      const validPassword = 'ValidPass123!';
      const errorResponse = { message: 'Server error occurred' };

      authService.resetPassword.mockReturnValue(throwError(() => errorResponse));

      component.resetpasswordForm.get('password')?.setValue(validPassword);
      component.resetpasswordForm.get('confirmPassword')?.setValue(validPassword);

      component.onSubmit();

      expect(toastrService.error).toHaveBeenCalled();
    });
  });

  describe('Edge Cases - Negative Cases', () => {
    it('should toggle password two for any id that is not 1', () => {
      const initialPasswordTwoType = component.PasswordTwoType;

      component.togglePass(999); // Any id that's not 1 triggers password two toggle

      expect(component.PasswordTwoType).not.toBe(initialPasswordTwoType);
      expect(component.PasswordTwoType).toBe('text'); // Should be toggled to text
    });

    it('should only toggle password one when id is exactly 1', () => {
      const initialPasswordOneType = component.PasswordOneType;
      const initialPasswordTwoType = component.PasswordTwoType;

      component.togglePass(1); // Only id 1 should toggle password one

      expect(component.PasswordOneType).not.toBe(initialPasswordOneType);
      expect(component.PasswordOneType).toBe('text');
      expect(component.PasswordTwoType).toBe(initialPasswordTwoType); // Should remain unchanged
    });

    it('should handle multiple password validation errors simultaneously', () => {
      const invalidPassword = 'abc'; // Too short, no uppercase, no number, no special char

      component.resetpasswordForm.get('password')?.setValue(invalidPassword);

      const passwordControl = component.resetpasswordForm.get('password');
      expect(passwordControl?.invalid).toBeTruthy();
      expect(passwordControl?.errors?.['minlength']).toBeTruthy();
      expect(passwordControl?.errors?.['hasCapitalCase']).toBeTruthy();
      expect(passwordControl?.errors?.['hasNumber']).toBeTruthy();
      expect(passwordControl?.errors?.['hasSpecialCharacters']).toBeTruthy();
    });
  });

  // ===== CONSTRUCTOR AND LIFECYCLE TESTS =====

  describe('Constructor and Lifecycle - Comprehensive Coverage', () => {
    it('should set page title on construction', () => {
      expect(titleService.setTitle).toHaveBeenCalledWith('Follo - Reset Password');
    });

    it('should extract token from router URL on construction', () => {
      expect(component.resetToken).toBe('test-token-123');
    });

    it('should call checkAuthentication on construction', () => {
      expect(authService.checkAuthentication).toHaveBeenCalled();
    });

    it('should call checkToken on construction', () => {
      expect(authService.checkToken).toHaveBeenCalledWith('test-token-123');
    });

    it('should initialize form with correct structure in ngOnInit', () => {
      component.ngOnInit();

      const form = component.resetpasswordForm;
      expect(form.get('password')).toBeDefined();
      expect(form.get('confirmPassword')).toBeDefined();
      expect(form.get('password')?.hasError('required')).toBeTruthy();
      expect(form.get('confirmPassword')?.hasError('required')).toBeTruthy();
    });
  });

  // ===== TOKEN VALIDATION TESTS =====

  describe('Token Validation - Positive and Negative Cases', () => {
    beforeEach(() => {
      authService.checkToken.mockClear();
      toastrService.error.mockClear();
      router.navigate.mockClear();
    });

    it('should handle successful token validation', () => {
      const mockResponse = { valid: true, message: 'Token is valid' };
      authService.checkToken.mockReturnValue(of(mockResponse));

      component.checkToken();

      expect(authService.checkToken).toHaveBeenCalledWith('test-token-123');
      expect(component.response).toEqual(mockResponse);
    });

    it('should handle token validation error and redirect to login', () => {
      const errorResponse = { message: 'Invalid token' };
      authService.checkToken.mockReturnValue(throwError(() => errorResponse));

      component.checkToken();

      expect(toastrService.error).toHaveBeenCalledWith('Invalid token', 'OOPS!');
      expect(router.navigate).toHaveBeenCalledWith(['/login']);
    });

    it('should handle token validation with null response', () => {
      authService.checkToken.mockReturnValue(of(null));

      component.checkToken();

      expect(component.response).toBeUndefined();
    });

    it('should handle token validation with undefined response', () => {
      authService.checkToken.mockReturnValue(of(undefined));

      component.checkToken();

      expect(component.response).toBeUndefined();
    });
  });

  // ===== PASSWORD VALIDATION METHOD TESTS =====

  describe('passwordValid Method - Comprehensive Coverage', () => {
    it('should set passwordError to false for valid password', () => {
      const mockEvent = {
        target: { value: 'ValidPass123!' }
      };

      component.passwordValid(mockEvent);

      expect(component.passwordError).toBeFalsy();
    });

    it('should set passwordError to true for invalid password', () => {
      const mockEvent = {
        target: { value: 'invalid' }
      };

      component.passwordValid(mockEvent);

      expect(component.passwordError).toBeTruthy();
    });

    it('should handle empty password in passwordValid', () => {
      const mockEvent = {
        target: { value: '' }
      };

      component.passwordValid(mockEvent);

      expect(component.passwordError).toBeTruthy();
    });

    it('should handle password with only numbers', () => {
      const mockEvent = {
        target: { value: '12345678' }
      };

      component.passwordValid(mockEvent);

      expect(component.passwordError).toBeTruthy();
    });

    it('should handle password with only letters', () => {
      const mockEvent = {
        target: { value: 'abcdefgh' }
      };

      component.passwordValid(mockEvent);

      expect(component.passwordError).toBeTruthy();
    });
  });

  // ===== KEYBOARD EVENT HANDLING TESTS =====

  describe('handleToggleKeydown Method - Comprehensive Coverage', () => {
    it('should call togglePass when Enter key is pressed', () => {
      const mockEvent = {
        key: 'Enter',
        preventDefault: jest.fn()
      } as any;
      const spy = jest.spyOn(component, 'togglePass');

      component.handleToggleKeydown(mockEvent, 1);

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(spy).toHaveBeenCalledWith(1);
    });

    it('should call togglePass when Space key is pressed', () => {
      const mockEvent = {
        key: ' ',
        preventDefault: jest.fn()
      } as any;
      const spy = jest.spyOn(component, 'togglePass');

      component.handleToggleKeydown(mockEvent, 2);

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(spy).toHaveBeenCalledWith(2);
    });

    it('should not call togglePass for other keys', () => {
      const mockEvent = {
        key: 'Tab',
        preventDefault: jest.fn()
      } as any;
      const spy = jest.spyOn(component, 'togglePass');

      component.handleToggleKeydown(mockEvent, 1);

      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      expect(spy).not.toHaveBeenCalled();
    });

    it('should handle Escape key without action', () => {
      const mockEvent = {
        key: 'Escape',
        preventDefault: jest.fn()
      } as any;
      const spy = jest.spyOn(component, 'togglePass');

      component.handleToggleKeydown(mockEvent, 1);

      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      expect(spy).not.toHaveBeenCalled();
    });
  });

  // ===== ADVANCED ERROR HANDLING TESTS =====

  describe('Advanced Error Handling - Comprehensive Coverage', () => {
    beforeEach(() => {
      authService.resetPassword.mockClear();
      toastrService.error.mockClear();
      toastrService.success.mockClear();
      router.navigate.mockClear();
    });

    it('should handle 400 status code error with showError method', () => {
      const validPassword = 'ValidPass123!';
      const errorResponse = {
        message: {
          statusCode: 400,
          details: [{ field: 'password', message: 'Password too weak' }]
        }
      };

      authService.resetPassword.mockReturnValue(throwError(() => errorResponse));
      const showErrorSpy = jest.spyOn(component, 'showError');

      component.resetpasswordForm.get('password')?.setValue(validPassword);
      component.resetpasswordForm.get('confirmPassword')?.setValue(validPassword);
      jest.spyOn(component.resetpasswordForm, 'invalid', 'get').mockReturnValue(false);

      component.onSubmit();

      expect(showErrorSpy).toHaveBeenCalledWith(errorResponse);
      expect(component.formSubmitted).toBeFalsy();
      expect(component.submitted).toBeFalsy();
    });

    it('should handle error without message property', () => {
      const validPassword = 'ValidPass123!';
      const errorResponse = {}; // No message property

      authService.resetPassword.mockReturnValue(throwError(() => errorResponse));

      component.resetpasswordForm.get('password')?.setValue(validPassword);
      component.resetpasswordForm.get('confirmPassword')?.setValue(validPassword);
      jest.spyOn(component.resetpasswordForm, 'invalid', 'get').mockReturnValue(false);

      component.onSubmit();

      expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
    });

    it('should handle generic error with message', () => {
      const validPassword = 'ValidPass123!';
      const errorResponse = { message: 'Network error occurred' };

      authService.resetPassword.mockReturnValue(throwError(() => errorResponse));

      component.resetpasswordForm.get('password')?.setValue(validPassword);
      component.resetpasswordForm.get('confirmPassword')?.setValue(validPassword);
      jest.spyOn(component.resetpasswordForm, 'invalid', 'get').mockReturnValue(false);

      component.onSubmit();

      expect(toastrService.error).toHaveBeenCalledWith('Network error occurred', 'OOPS!');
      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
    });

    it('should test showError method with proper error structure', () => {
      const errorObj = {
        message: {
          details: [{ field: 'password', message: 'Invalid password format' }]
        }
      };

      component.showError(errorObj);

      expect(toastrService.error).toHaveBeenCalledWith('Invalid password format');
    });

    it('should handle showError with multiple error details', () => {
      const errorObj = {
        message: {
          details: [
            { field: 'password', message: 'Password too short' },
            { field: 'confirm', message: 'Passwords do not match' }
          ]
        }
      };

      component.showError(errorObj);

      // Should show the first error message
      expect(toastrService.error).toHaveBeenCalledWith('Password too short');
    });
  });

  // ===== MUSTMATCH VALIDATOR COMPREHENSIVE TESTS =====

  describe('MustMatch Validator - Comprehensive Coverage', () => {
    it('should return undefined when passwords match', () => {
      component.resetpasswordForm.get('password')?.setValue('ValidPass123!');
      component.resetpasswordForm.get('confirmPassword')?.setValue('ValidPass123!');

      const validator = component.MustMatch('password', 'confirmPassword');
      const result = validator(component.resetpasswordForm);

      expect(result).toBeUndefined();
      expect(component.resetpasswordForm.get('confirmPassword')?.errors).toBeNull();
    });

    it('should set mustMatch error when passwords do not match', () => {
      component.resetpasswordForm.get('password')?.setValue('ValidPass123!');
      component.resetpasswordForm.get('confirmPassword')?.setValue('DifferentPass123!');

      const validator = component.MustMatch('password', 'confirmPassword');
      validator(component.resetpasswordForm);

      expect(component.resetpasswordForm.get('confirmPassword')?.errors?.['mustMatch']).toBeTruthy();
    });

    it('should not override existing errors when passwords do not match', () => {
      // Set an existing error first
      component.resetpasswordForm.get('confirmPassword')?.setErrors({ required: true });
      component.resetpasswordForm.get('password')?.setValue('ValidPass123!');
      component.resetpasswordForm.get('confirmPassword')?.setValue('DifferentPass123!');

      const validator = component.MustMatch('password', 'confirmPassword');
      validator(component.resetpasswordForm);

      const errors = component.resetpasswordForm.get('confirmPassword')?.errors;
      expect(errors?.['mustMatch']).toBeTruthy();
    });

    it('should handle empty password values in MustMatch', () => {
      component.resetpasswordForm.get('password')?.setValue('');
      component.resetpasswordForm.get('confirmPassword')?.setValue('');

      const validator = component.MustMatch('password', 'confirmPassword');
      const result = validator(component.resetpasswordForm);

      expect(result).toBeUndefined();
      expect(component.resetpasswordForm.get('confirmPassword')?.errors).toBeNull();
    });

    it('should handle null password values in MustMatch', () => {
      component.resetpasswordForm.get('password')?.setValue(null);
      component.resetpasswordForm.get('confirmPassword')?.setValue(null);

      const validator = component.MustMatch('password', 'confirmPassword');
      const result = validator(component.resetpasswordForm);

      expect(result).toBeUndefined();
      expect(component.resetpasswordForm.get('confirmPassword')?.errors).toBeNull();
    });
  });

  // ===== PASSWORD TOGGLE COMPREHENSIVE TESTS =====

  describe('Password Toggle - Comprehensive Coverage', () => {
    it('should toggle password one from text back to password', () => {
      // First toggle to text
      component.togglePass(1);
      expect(component.PasswordOneType).toBe('text');
      expect(component.togglePasswordOne).toBeFalsy();

      // Toggle back to password
      component.togglePass(1);
      expect(component.PasswordOneType).toBe('password');
      expect(component.togglePasswordOne).toBeTruthy();
    });

    it('should toggle password two from text back to password', () => {
      // First toggle to text
      component.togglePass(2);
      expect(component.PasswordTwoType).toBe('text');
      expect(component.togglePasswordTwo).toBeFalsy();

      // Toggle back to password
      component.togglePass(2);
      expect(component.PasswordTwoType).toBe('password');
      expect(component.togglePasswordTwo).toBeTruthy();
    });

    it('should handle multiple toggles correctly', () => {
      // Multiple toggles for password one
      for (let i = 0; i < 5; i++) {
        component.togglePass(1);
      }
      expect(component.PasswordOneType).toBe('text');
      expect(component.togglePasswordOne).toBeFalsy();

      // Multiple toggles for password two
      for (let i = 0; i < 4; i++) {
        component.togglePass(2);
      }
      expect(component.PasswordTwoType).toBe('password');
      expect(component.togglePasswordTwo).toBeTruthy();
    });

    it('should handle zero as toggle id (should toggle password two)', () => {
      const initialType = component.PasswordTwoType;
      const initialToggle = component.togglePasswordTwo;

      component.togglePass(0);

      expect(component.PasswordTwoType).not.toBe(initialType);
      expect(component.togglePasswordTwo).not.toBe(initialToggle);
    });

    it('should handle negative numbers as toggle id (should toggle password two)', () => {
      const initialType = component.PasswordTwoType;
      const initialToggle = component.togglePasswordTwo;

      component.togglePass(-5);

      expect(component.PasswordTwoType).not.toBe(initialType);
      expect(component.togglePasswordTwo).not.toBe(initialToggle);
    });
  });

  // ===== FORM VALIDATION EDGE CASES =====

  describe('Form Validation Edge Cases - Comprehensive Coverage', () => {
    it('should handle whitespace-only password', () => {
      const whitespacePassword = '        '; // 8 spaces

      component.resetpasswordForm.get('password')?.setValue(whitespacePassword);

      const passwordControl = component.resetpasswordForm.get('password');
      expect(passwordControl?.invalid).toBeTruthy();
      // Should fail the non-whitespace pattern validator
    });

    it('should handle password with maximum length', () => {
      const maxLengthPassword = 'A'.repeat(30) + '1!'; // 32 chars total

      component.resetpasswordForm.get('password')?.setValue(maxLengthPassword);
      component.resetpasswordForm.get('confirmPassword')?.setValue(maxLengthPassword);

      const passwordControl = component.resetpasswordForm.get('password');
      // Should be valid as it meets all requirements
      expect(passwordControl?.hasError('minlength')).toBeFalsy();
    });

    it('should handle special characters in password validation', () => {
      const specialChars = ['!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '_', '+', '-', '=', '[', ']', '{', '}', ';', "'", ':', '"', '\\', '|', ',', '.', '<', '>', '/', '?'];

      specialChars.forEach(char => {
        const passwordWithSpecialChar = `ValidPass123${char}`;
        component.resetpasswordForm.get('password')?.setValue(passwordWithSpecialChar);

        const passwordControl = component.resetpasswordForm.get('password');
        expect(passwordControl?.hasError('hasSpecialCharacters')).toBeFalsy();
      });
    });

    it('should validate confirm password independently', () => {
      component.resetpasswordForm.get('confirmPassword')?.setValue('   '); // Whitespace

      const confirmPasswordControl = component.resetpasswordForm.get('confirmPassword');
      expect(confirmPasswordControl?.invalid).toBeTruthy();
    });

    it('should handle form reset after validation errors', () => {
      // Set invalid values
      component.resetpasswordForm.get('password')?.setValue('invalid');
      component.resetpasswordForm.get('confirmPassword')?.setValue('different');

      // Trigger validation
      component.onSubmit();
      expect(component.submitted).toBeTruthy();

      // Reset form
      component.resetpasswordForm.reset();
      component.submitted = false;

      expect(component.resetpasswordForm.get('password')?.value).toBeNull();
      expect(component.resetpasswordForm.get('confirmPassword')?.value).toBeNull();
    });
  });

  // ===== API RESPONSE EDGE CASES =====

  describe('API Response Edge Cases - Comprehensive Coverage', () => {
    beforeEach(() => {
      authService.resetPassword.mockClear();
      authService.checkToken.mockClear();
      toastrService.success.mockClear();
      toastrService.error.mockClear();
      router.navigate.mockClear();
    });

    it('should handle successful response without message', () => {
      const validPassword = 'ValidPass123!';
      const successResponse = { success: true }; // No message property

      authService.resetPassword.mockReturnValue(of(successResponse));

      component.resetpasswordForm.get('password')?.setValue(validPassword);
      component.resetpasswordForm.get('confirmPassword')?.setValue(validPassword);
      jest.spyOn(component.resetpasswordForm, 'invalid', 'get').mockReturnValue(false);

      component.onSubmit();

      expect(toastrService.success).toHaveBeenCalledWith(undefined, 'Success');
      expect(router.navigate).toHaveBeenCalledWith(['/login']);
    });

    it('should handle null response from resetPassword API', () => {
      const validPassword = 'ValidPass123!';

      authService.resetPassword.mockReturnValue(of(null));

      component.resetpasswordForm.get('password')?.setValue(validPassword);
      component.resetpasswordForm.get('confirmPassword')?.setValue(validPassword);
      jest.spyOn(component.resetpasswordForm, 'invalid', 'get').mockReturnValue(false);

      component.onSubmit();

      // Should not call success toast or navigate if response is null
      expect(toastrService.success).not.toHaveBeenCalled();
      expect(router.navigate).not.toHaveBeenCalled();
      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
    });

    it('should handle undefined response from resetPassword API', () => {
      const validPassword = 'ValidPass123!';

      authService.resetPassword.mockReturnValue(of(undefined));

      component.resetpasswordForm.get('password')?.setValue(validPassword);
      component.resetpasswordForm.get('confirmPassword')?.setValue(validPassword);
      jest.spyOn(component.resetpasswordForm, 'invalid', 'get').mockReturnValue(false);

      component.onSubmit();

      expect(toastrService.success).not.toHaveBeenCalled();
      expect(router.navigate).not.toHaveBeenCalled();
      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
    });

    it('should handle checkToken with falsy response', () => {
      authService.checkToken.mockReturnValue(of(false));

      component.checkToken();

      expect(component.response).toBeUndefined();
    });

    it('should handle checkToken with empty object response', () => {
      const emptyResponse = {};
      authService.checkToken.mockReturnValue(of(emptyResponse));

      component.checkToken();

      expect(component.response).toEqual(emptyResponse);
    });
  });

  // ===== CONSTRUCTOR URL PARSING EDGE CASES =====

  describe('Constructor URL Parsing - Edge Cases', () => {
    it('should handle different router URL formats', () => {
      // Test with different URL structures
      const testCases = [
        { url: '/reset-password/abc123', expectedToken: 'abc123' },
        { url: '/reset-password/token-with-dashes', expectedToken: 'token-with-dashes' },
        { url: '/reset-password/123456789', expectedToken: '123456789' },
        { url: '/reset-password/', expectedToken: '' }
      ];

      testCases.forEach(testCase => {
        router.url = testCase.url;

        // Create new component instance to test constructor
        const newFixture = TestBed.createComponent(ResetPasswordComponent);
        const newComponent = newFixture.componentInstance;

        expect(newComponent.resetToken).toBe(testCase.expectedToken);
      });
    });
  });

  // ===== INTEGRATION TESTS =====

  describe('Integration Tests - Full Workflow', () => {
    it('should complete full password reset workflow successfully', () => {
      const validPassword = 'ValidPass123!';

      // Mock successful token validation
      const tokenResponse = { valid: true, message: 'Token is valid' };
      authService.checkToken.mockReturnValue(of(tokenResponse));

      // Mock successful password reset
      const resetResponse = { success: true, message: 'Password reset successfully' };
      authService.resetPassword.mockReturnValue(of(resetResponse));

      // Initialize component (simulates constructor and ngOnInit)
      component.ngOnInit();
      component.checkToken();

      // Fill form with valid data
      component.resetpasswordForm.get('password')?.setValue(validPassword);
      component.resetpasswordForm.get('confirmPassword')?.setValue(validPassword);

      // Submit form
      component.onSubmit();

      // Verify complete workflow
      expect(authService.checkToken).toHaveBeenCalledWith('test-token-123');
      expect(component.response).toEqual(tokenResponse);
      expect(authService.resetPassword).toHaveBeenCalled();
      expect(toastrService.success).toHaveBeenCalledWith('Password reset successfully', 'Success');
      expect(router.navigate).toHaveBeenCalledWith(['/login']);
      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
    });

    it('should handle complete workflow with token validation failure', () => {
      const tokenError = { message: 'Token expired' };
      authService.checkToken.mockReturnValue(throwError(() => tokenError));

      component.checkToken();

      expect(toastrService.error).toHaveBeenCalledWith('Token expired', 'OOPS!');
      expect(router.navigate).toHaveBeenCalledWith(['/login']);
    });
  });
});
