import { ComponentFixture, TestBed } from '@angular/core/testing';
import { LogoutAlertComponent } from './logout-alert.component';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { AuthService } from '../services';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('LogoutAlertComponent', () => {
  let component: LogoutAlertComponent;
  let fixture: ComponentFixture<LogoutAlertComponent>;
  let authServiceMock: jest.Mocked<AuthService>;
  let modalRefMock: jest.Mocked<BsModalRef>;

  beforeEach(async () => {
    // Create mocks
    authServiceMock = {
      logout: jest.fn()
    } as any;

    modalRefMock = {
      hide: jest.fn()
    } as any;

    await TestBed.configureTestingModule({
      declarations: [LogoutAlertComponent],
      providers: [
        { provide: AuthService, useValue: authServiceMock },
        { provide: BsModalRef, useValue: modalRefMock }
      ],
      schemas: [NO_ERRORS_SCHEMA] // To ignore child components
    }).compileComponents();

    fixture = TestBed.createComponent(LogoutAlertComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call authService.logout when logout is called', () => {
    // Act
    component.logout();

    // Assert
    expect(authServiceMock.logout).toHaveBeenCalled();
  });

  it('should have access to modal reference', () => {
    expect(component.bsModalRef).toBeTruthy();
    expect(component.bsModalRef).toBe(modalRefMock);
  });
});
