import { Component, TemplateRef, OnInit } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import moment from 'moment';
import { ProjectService } from '../services/profile/project.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { EditDeliveryFormComponent } from '../delivery-requests/delivery-details/edit-delivery-form/edit-delivery-form.component';

@Component({
  selector: 'app-void-list',
  templateUrl: './void-list.component.html',
})
export class VoidListComponent implements OnInit {
  public currentPageNo = 1;

  public userData = [];

  public modalRef: BsModalRef;

  public ProjectId: any;

  public loader = true;

  public pageSize = 25;

  public pageNo = 1;

  public totalCount = 0;

  public lastId = 0;

  public voidPageDeliveryList: any = [];

  public selectAll = false;

  public companyList: any = [];

  public defineList: any = [];

  public gateList: any = [];

  public equipmentList: any = [];

  public deliverDetailsForm: UntypedFormGroup;

  public autocompleteItemsAsObjects: any = [];

  public deliverEditForm: UntypedFormGroup;

  public submitted = false;

  public escort = false;

  public formSubmitted = false;

  public editSubmitted = false;

  public formEditSubmitted = false;

  public authUser: any = {};

  public statusValue: any = [];

  public currentDeliveryItem: any = {};

  public currentEditItem: any = {};

  public currentStatus = '';

  public showStatus = false;

  public search = '';

  public filterForm: UntypedFormGroup;

  public memberList: any = [];

  public restoreRecord: any = {};

  public restoreSubmitted = false;

  public currentRestoreId: any;

  public modalRef3: BsModalRef;

  public remove = false;

  public filterCount = 0;

  public restoreIndex: any = [];

  public wholeStatus = ['Approved', 'Completed', 'Declined', 'Delivered', 'Pending', 'Tentative'];

  public loginMemberData: any = {};

  public ParentCompanyId: any;

  public sort = 'DESC';

  public selectedVoidIndex : any;

  public sortColumn = 'id';

  public showSearchbar = false;

  public locationDropdown: any[] = [];

  public mixDesignDropdown: any[] = [];

  // Template references
  public deliveryDetails: TemplateRef<any>;
  public craneRequestDetail: TemplateRef<any>;
  public concreteRequestDetail: TemplateRef<any>;
  public inspectionRequestDetail: TemplateRef<any>;

  public constructor(private readonly modalService: BsModalService,
    public projectService: ProjectService,
    private readonly formBuilder: UntypedFormBuilder,
    public router: Router,
    private readonly toastr: ToastrService,
    private readonly titleService: Title,
    private readonly deliveryService: DeliveryService) {
  }

  public ngOnInit(): void {
    this.titleService.setTitle('Follo - Void List');
    this.projectService.projectParent.subscribe((response17): void => {
      this.ProjectId = response17.ProjectId;
      this.ParentCompanyId = response17.ParentCompanyId;
      if (response17 !== undefined && response17 !== null && response17 !== '') {
        this.loader = true;
        this.pageNo = 1;
        this.getLoginMember();
        this.getDeliveryRequest();
        this.voidPageGetCompany();
        this.voidPageGetDefinable();
        this.voidPageGetOverAllGate();
        this.voidPageGetOverAllEquipment();
        this.voidPageGetMembers();
        this.getFilterDropdown();
      }
    });
    this.filterDetailsForm();
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.pageNo = 1;
    this.filterDetailsForm();
    this.getDeliveryRequest();
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.setSelectedItem(data);
    }
  }

  public handleDeleteKeydown(event: KeyboardEvent, data: any, deleteList: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.openDeleteModal(data, deleteList);
    }
  }

  public handleSortKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public handleModalKeydown(event: KeyboardEvent, data?: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (data) {
        this.openModal1(data);
      } else {
        this.clear();
      }
    }
  }

  public handleKeyDown(
    event: KeyboardEvent,
    deliveryDetails: any,
    craneRequestDetail: any,
    concreteRequestDetail: any,
    inspectionRequestDetail: any,
    item: any,
  ) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.openIdModal(deliveryDetails, craneRequestDetail, concreteRequestDetail, inspectionRequestDetail, item);
    }
  }

  public getLoginMember(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.getMemberRole(param).subscribe((response: any): void => {
      if (response) {
        this.loginMemberData = response.data;
      }
    });
  }

  public getFilterDropdown(): any {
    if (this.ProjectId) {
      const payload = {
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.deliveryService.getConcreteRequestDropdownData(payload).subscribe(
        (response): void => {
          if (response.data) {
            this.locationDropdown = response.data.locationDropdown;
            this.mixDesignDropdown = response.data.mixDesignDropdown;
          }
        },
      );
    }
  }


  public voidPageGetMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.pageNo,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }

  public close(): void{
    this.modalRef.hide();
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('descriptionFilter').value !== '') { this.filterCount += 1; }
    if (this.filterForm.get('companyFilter').value !== '') { this.filterCount += 1; }
    if (this.filterForm.get('memberFilter').value !== '') { this.filterCount += 1; }
    if (this.filterForm.get('gateFilter').value !== '') { this.filterCount += 1; }
    if (this.filterForm.get('equipmentFilter').value !== '') { this.filterCount += 1; }
    if (this.filterForm.get('statusFilter').value !== '') { this.filterCount += 1; }
    if (this.filterForm.get('dateFilter').value !== '') { this.filterCount += 1; }
    if (this.filterForm.get('locationFilter').value !== '') { this.filterCount += 1; }
    if (this.filterForm.get('mixDesignFilter').value !== '') { this.filterCount += 1; }
    this.pageNo = 1;
    this.getDeliveryRequest();
  }

  public voidPageGetCompany(): void {
    const voidPageGetCompanyParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getCompanies(voidPageGetCompanyParams)
      .subscribe((voidPageGetCompanyResponse: any): void => {
        if (voidPageGetCompanyResponse) {
          this.companyList = voidPageGetCompanyResponse.data;
        }
      });
  }

  public voidPageGetDefinable(): void {
    const voidPageGetDefinableParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getDefinableWork(voidPageGetDefinableParams)
      .subscribe((voidPageGetDefinableResponse: any): void => {
        if (voidPageGetDefinableResponse) {
          const { data } = voidPageGetDefinableResponse;
          this.defineList = data;
        }
      });
  }

  public voidPageGetOverAllGate(): void {
    const voidPageGetGateParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.gateList(voidPageGetGateParams, { isFilter: true, showActivatedAlone: true })
      .subscribe((voidPageGetGateResponse): void => {
        this.gateList = voidPageGetGateResponse.data;
      });
  }

  public voidPageGetOverAllEquipment(): void {
    const voidPageGetEquipmentParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listEquipment(voidPageGetEquipmentParams, { isFilter: true, showActivatedAlone: true })
      .subscribe((voidPageGetEquipmentResponse): void => {
        this.equipmentList = voidPageGetEquipmentResponse.data;
      });
  }

  public sortByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getDeliveryRequest();
  }

  public getDeliveryRequest(): void {
    this.loader = true;
    this.voidPageDeliveryList = [];
    const param = {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.pageNo,
      void: 1,
    };
    let payload: any = {};
    if (this.filterForm !== undefined) {
      const getDeliveryRequestDateInFilterForm = this.filterForm.value
        .dateFilter
        ? moment(this.filterForm.value.dateFilter).format('YYYY-MM-DD')
        : this.filterForm.value.dateFilter;
      payload = {
        companyFilter: this.filterForm.value.companyFilter,
        descriptionFilter: this.filterForm.value.descriptionFilter,
        statusFilter: this.filterForm.value.statusFilter,
        memberFilter: +this.filterForm.value.memberFilter,
        gateFilter: +this.filterForm.value.gateFilter,
        equipmentFilter: this.filterForm.value.equipmentFilter === null || this.filterForm.value.equipmentFilter === '' ? null : +this.filterForm.value.equipmentFilter,
        locationFilter: this.filterForm.value.locationFilter,
        mixDesignFilter: this.filterForm.value.mixDesignFilter,
        dateFilter: getDeliveryRequestDateInFilterForm,
        search: this.search,
      };
    }
    payload.search = this.search;
    payload.ParentCompanyId = this.ParentCompanyId;
    payload.queuedNdr = false;
    payload.sort = this.sort;
    payload.sortByField = this.sortColumn;
    payload.filterCount = this.filterCount;
    this.deliveryService.getVoidList(param, payload)
      .subscribe((listNdrResponse: any): void => {
        if (listNdrResponse) {
          const listNdrResponseData = listNdrResponse.data;
          this.lastId = listNdrResponse.lastId;
          this.loader = false;
          this.voidPageDeliveryList = listNdrResponseData.rows;
          if (this.selectAll && this.voidPageDeliveryList.length > 0) {
            this.voidPageDeliveryList.map((obj: any, index: string | number): void => {
              this.voidPageDeliveryList[index].isChecked = true;
              return null;
            });
          } else {
            this.selectAll = false;
            if (this.voidPageDeliveryList.length > 0) {
              this.voidPageDeliveryList.map((obj: any, index: string | number): void => {
                this.voidPageDeliveryList[index].isChecked = false;
                return null;
              });
            }
          }
          this.totalCount = listNdrResponseData.count;
          if (this.modalRef) {
            this.modalRef.hide();
          }
        }
      });
  }

  public checkSelectedRow(): boolean {
    if (this.selectAll) {
      return false;
    }
    const index = this.voidPageDeliveryList.findIndex((item: { isChecked: boolean }): boolean => (item.isChecked === true));
    if (index !== -1) {
      return false;
    }
    return true;
  }

  public setSelectedItem(index: string | number): void {
    this.selectedVoidIndex = index
    this.voidPageDeliveryList[index].isChecked = !this.voidPageDeliveryList[index].isChecked;
  }

  public openDeleteModal(index: number, template: TemplateRef<any>): void {
    if (index !== -1) {
      const { voidList } = this.voidPageDeliveryList[index];
      const MemberId = this.loginMemberData.id;
      const voidIndex = voidList.findIndex((item: { MemberId: any }): boolean => (item.MemberId === MemberId));
      this.restoreIndex[0] = voidList[voidIndex].id;
      this.currentRestoreId = index;
      this.remove = false;
    } else if (index === -1) {
      this.remove = true;
    }
    this.openModal(template);
  }

  public openModal1(template: TemplateRef<any>): void {
    this.modalRef = this.modalService.show(template, { backdrop: 'static', keyboard: false, class: 'modal-sm filter-popup custom-modal' });
  }

  public checkoverlapping(payload){
    return this.deliveryService.checkOverlapping(payload).subscribe((response: any) =>{
      return response.isAllowed
    })
  }

  public restoreRequest(): void {
    this.restoreSubmitted = true;
    this.deliveryService.removeVoid({
      id: this.restoreIndex,
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      isSelectAll: this.selectAll,
    }).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.pageNo = 1;
          this.getDeliveryRequest();
          this.restoreSubmitted = false;
          this.modalRef.hide();
        }
      },
      error: (restoreRequestErr): void => {
        this.restoreSubmitted = false;
        if (restoreRequestErr.message?.statusCode === 400) {
          this.showError(restoreRequestErr);
        } else if (!restoreRequestErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(restoreRequestErr.message, 'OOPS!');
        }
      },
    });
  }


  public restoreItemRequest(): void {
    const element = this.voidPageDeliveryList[this.selectedVoidIndex];
    const isNotAllowed = this.checkoverlapping(element);
    if (!isNotAllowed) {
      this.restoreRequest();
    } else {
      this.toastr.info("There is another booking overlapping this Date and Time.Please update the Date or Time to Retore this booking...!")
      this.modalRef.hide();
      this.restoreSubmitted = false;
      this.deliveryService.updatedDeliveryId(element.id);
      const className = 'modal-lg new-delivery-popup custom-modal';
      this.modalRef3 = this.modalService.show(EditDeliveryFormComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
      });
      this.modalRef3.content.closeBtnName = 'Close';
      this.modalRef3.content.seriesOption = element?.recurrence && element?.recurrence?.recurrence !== 'Does Not Repeat';
      this.modalRef3.content.recurrenceId = element?.recurrence ? element?.recurrence?.id : null;
      this.modalRef3.content.recurrenceEndDate = element?.recurrence
        ? element?.recurrence?.recurrenceEndDate
        : null;
    }
  }

  public removeItem(): void {
    this.restoreSubmitted = true;

    if (this.selectAll) {
      this.restoreRequest();
      return;
    }

    this.voidPageDeliveryList.forEach((element: any): void => {
      if (!element.isChecked) return;

      const { voidList, requestType, id } = element;
      const itemId = this.getItemId(voidList, requestType, id);
      if (itemId !== null) {
        this.restoreIndex.push(itemId);
      }

      if (requestType === 'deliveryRequest' || requestType === 'deliveryRequestWithCrane') {
        this.handleDeliveryRequest(element);
      }
    });
  }

  public getItemId(voidList: any[], requestType: string, id: any): any {
    const mapping: { [key: string]: string } = {
      concreteRequest: 'ConcreteRequestId',
      craneRequest: 'CraneRequestId',
      inspectionRequest: 'InspectionRequestId',
      deliveryRequest: 'DeliveryRequestId',
      deliveryRequestWithCrane: 'DeliveryRequestId',
    };

    const key = mapping[requestType];
    if (!key) return null;

    const voidIndex = voidList.findIndex((item: any) => +item[key] === +id);
    return voidIndex !== -1 ? voidList[voidIndex].id : null;
  }

  public handleDeliveryRequest(element: any): void {
    const isNotAllowed = this.checkoverlapping(element);

    if (!isNotAllowed) {
      this.restoreRequest();
      return;
    }

    this.voidPageDeliveryList.forEach((el: any): void => {
      if (!el.isChecked) return;

      const { voidList, requestType, id } = el;
      let voidIndex = -1;

      switch (requestType) {
        case 'concreteRequest':
          voidIndex = voidList.findIndex((i: { ConcreteRequestId: string | number }) => +i.ConcreteRequestId === +id);
          break;
        case 'craneRequest':
          voidIndex = voidList.findIndex((i: { CraneRequestId: any }) => i.CraneRequestId === id);
          break;
        case 'inspectionRequest':
          voidIndex = voidList.findIndex((i: { InspectionId: any }) => i.InspectionId === id);
          break;
        case 'deliveryRequest':
        case 'deliveryRequestWithCrane':
          voidIndex = voidList.findIndex((i: { DeliveryRequestId: any }) => i.DeliveryRequestId === id);
          break;
      }

      if (voidIndex !== -1) {
        this.restoreIndex.push(voidList[voidIndex].id);
      }

      if (requestType === 'deliveryRequest' || requestType === 'deliveryRequestWithCrane') {
        const overlap = this.checkoverlapping(el);
        if (!overlap) {
          this.restoreRequest();
        } else {
          this.showOverlapModal(el);
        }
      }
    });

    if (element.requestType !== 'deliveryRequest' && element.requestType !== 'deliveryRequestWithCrane') {
      this.showOverlapModal(element);
    }
  }

  private showOverlapModal(element: any): void {
    this.toastr.info("There is another booking overlapping this Date and Time. Please update the Date or Time to Restore this booking...!");
    this.modalRef.hide();
    this.restoreSubmitted = false;
    this.deliveryService.updatedDeliveryId(element.id);
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef3 = this.modalService.show(EditDeliveryFormComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
    });
    this.modalRef3.content.closeBtnName = 'Close';
    this.modalRef3.content.seriesOption = element?.recurrence && element?.recurrence?.recurrence !== 'Does Not Repeat';
    this.modalRef3.content.recurrenceId = element?.recurrence ? element?.recurrence?.id : null;
    this.modalRef3.content.recurrenceEndDate = element?.recurrence ? element?.recurrence?.recurrenceEndDate : null;
  }


  public showError(err: { message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] } }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.editSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public selectAllVoidListData(): void {
    this.selectAll = !this.selectAll;
    if (this.selectAll) {
      this.voidPageDeliveryList.map((obj: any, index: string | number): void => {
        this.voidPageDeliveryList[index].isChecked = true;
        return null;
      });
    } else {
      this.voidPageDeliveryList.map((obj: any, index: string | number): void => {
        this.voidPageDeliveryList[index].isChecked = false;
        return null;
      });
    }
  }


  public changePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.getDeliveryRequest();
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group(
      {
        companyFilter: [''],
        descriptionFilter: [''],
        statusFilter: [''],
        memberFilter: [''],
        gateFilter: [''],
        equipmentFilter: [''],
        dateFilter: [''],
        locationFilter: [''],
        mixDesignFilter: [''],
      },
    );
  }

  public openIdModal(template: TemplateRef<any>, template1: TemplateRef<any>, template2: TemplateRef<any>,template3: TemplateRef<any>, item: { requestType: string; CraneRequestId: string | number; id: any; ConcreteRequestId: string | number; InspectionId: any; status: string ;
}): void {
    if (item.requestType === 'craneRequest') {
      this.deliveryService.updatedEditCraneRequestId(+item.CraneRequestId);
      this.openModal(template1);
    } else if (item.requestType === 'deliveryRequest' || item.requestType === 'deliveryRequestWithCrane') {
      this.deliveryService.updatedDeliveryId(item.id);
      this.openModal(template);
    } else if (item.requestType === 'concreteRequest') {
      this.deliveryService.updatedEditConcreteRequestId(+item.ConcreteRequestId);
      this.openModal(template2);
    } else if (item.requestType === 'inspectionRequest' || item.requestType === 'inspectionRequestWithCrane') {
      this.deliveryService.updatedInspectionId(+item.id);
      this.openModal(template3);
    }
    this.currentDeliveryItem = item;
    if (item.status === 'Pending') {
      this.showStatus = true;
    }
  }

  public clear(): void{
    this.showSearchbar = false;
    this.search = '';
    this.pageNo = 1;
    this.getDeliveryRequest();
  }

  public getSearchNDR(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.pageNo = 1;
    this.search = data;
    this.getDeliveryRequest();
  }

  public changePageNo(pageNo: number): void {
    this.pageNo = pageNo;
    this.getDeliveryRequest();
  }

  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-lg modal-dialog-centered new-delivery-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }
}
