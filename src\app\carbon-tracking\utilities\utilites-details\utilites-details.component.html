<div class="modal-header">
    <h2 class="fs14 fw-bold cairo-regular color-text7 my-1">
        Utility Details
    </h2>
    <button type="button" class="close ms-auto" aria-label="Close" (click)="closeModal()">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<div class="modal-body pt-0 px-0 pb-4">
    <div class="delivery-ddetails-content my-3 mx-2">
        <div class="row px-2 my-4">
            <div class="col-md-5 col-lg-4">
                <h4 class="color-grey14 fs12 font-weight-normal">Document Name</h4>
                <p class="color-grey15 fs12 fw500">{{ data.bill_name }}</p>
            </div>
            <div class="col-md-4 col-lg-4">
                <h4 class="color-grey14 fs12 mb5 font-weight-normal">Utility Type</h4>
                <p class="color-grey15 fs12 mb-2 fw500">{{ data.utilityType?data.utilityType:'-' }}</p>
            </div>
            <div class="col-md-5 col-lg-4">
                <h4 class="color-grey14 fs12 mb5 font-weight-normal">Utility Units</h4>
                <div *ngIf="data.utilityUnits && data.utilityUnits?.length > 0">
                    <p class="color-grey15 fs12 mb-2 fw500">
                        {{ data.utilityUnits?data.utilityUnits:'-' }}
                    </p>
                </div>
                <p class="color-grey15 fs12 mb-2 fw500" *ngIf="!data.utilityUnits || data.utilityUnits?.length === 0">
                    -
                </p>
            </div>
            <div class="col-md-4 col-lg-4">
                <h4 class="color-grey14 fs12 mb5 font-weight-normal">Total Bill Amount</h4>
                <p class="color-grey15 fs12 mb-2 fw500">{{ data.bill_amount }}</p>
            </div>
        </div>
        <div class="row px-2 my-4" *ngIf="data.child && data.child?.length > 0">

            <div class="col-md-4 col-lg-4">
                <h4 class="color-grey14 fs12 mb5 font-weight-normal">Bill Amount</h4>
                <p class="color-grey15 fs12 mb-2 fw500">{{ data.child[0].cost?data.child[0].cost:'data.child[0].Cost' }}</p>
            </div>
            <div class="col-md-4 col-lg-4">
                <h4 class="color-grey14 fs12 mb5 font-weight-normal">Utility Item 1</h4>
                <p class="color-grey15 fs12 mb-2 fw500">{{ data.child[0].utilityType }}</p>
            </div>
            <div class="col-md-4 col-lg-4">
                <h4 class="color-grey14 fs12 mb5 font-weight-normal">Utility Quantiy 1</h4>
                <p class="color-grey15 fs12 mb-2 fw500">{{ data.child[0].quantity?data.child[0].quantity:data.child[0].Quantity }}</p>
            </div>
        </div>
        <div class="row px-2 my-4" *ngIf="data.child && data.child?.length > 1">
            <div class="col-md-4 col-lg-4">
                <h4 class="color-grey14 fs12 mb5 font-weight-normal">Bill Amount</h4>
                <p class="color-grey15 fs12 mb-2 fw500">{{ data.child[1].cost?data.child[1].cost:'data.child[1].Cost' }}</p>
            </div>
            <div class="col-md-4 col-lg-4">
                <h4 class="color-grey14 fs12 mb5 font-weight-normal">Utility Item 2</h4>
                <p class="color-grey15 fs12 mb-2 fw500">{{ data.child[1].utilityType }}</p>
            </div>
            <div class="col-md-4 col-lg-4">
                <h4 class="color-grey14 fs12 mb5 font-weight-normal">Utility Quantiy 2</h4>
                <p class="color-grey15 fs12 mb-2 fw500">{{ data.child[1].quantity?data.child[1].quantity:data.child[1].Quantity }}</p>
            </div>
        </div>
    </div>
</div>