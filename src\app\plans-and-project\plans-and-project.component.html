<ngx-loading [show]="redirectToStripeLoader" [config]="{ backdropBorderRadius: '3px' }">
</ngx-loading>
<section class="page-section">
  <div class="page-inner-content">
    <div class="top-header row mb-3">
      <div class="col-md-8"></div>
      <div class="col-md-4">
        <div class="top-filter">
          <ul class="list-group list-group-horizontal justify-content-end">
            <li class="list-group-item p0 border-0 bg-transparent">
              <div class="search-icon">
                <input
                  class="form-control fs12 color-grey8"
                  [ngClass]="showSearchbar ? 'input-hover-disable' : 'input-search'"
                  placeholder="What are you looking for?"
                  (input)="searchProject($event.target.value)"
                  [(ngModel)]="search"
                />
                <div class="icon">
                  <img
                    src="./assets/images/cross-close.svg"
                    *ngIf="showSearchbar"
                    (click)="clear()"
                    (keydown)="clear()"
                    alt="close-cross"
                  />
                  <em class="fa fa-search fs12 color-grey8" *ngIf="!showSearchbar"></em>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="page-card bg-white rounded">
      <div class="table-responsive rounded tab-grid">
        <table class="table table-custom mb-0" aria-describedby="Emptable">
          <thead>
            <th scope="col" resizable>ID</th>
            <th scope="col" resizable>
              Project Name
              <span>
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('projectName', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'projectName', 'ASC')"
                  *ngIf="sortColumn !== 'projectName'"
                />
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('projectName', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'projectName', 'ASC')"
                  *ngIf="sort === 'DESC' && sortColumn === 'projectName'"
                />
                <img
                  src="./assets/images/up-chevron.svg"
                  alt="up-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('projectName', 'DESC')"
                  (keydown)="handleToggleKeydown($event, 'projectName', 'DESC')"
                  *ngIf="sort === 'ASC' && sortColumn === 'projectName'"
                />
              </span>
            </th>
            <th scope="col" resizable>Subscription Plan</th>
            <th scope="col" resizable>
              Subscribed On
              <span>
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('startDate', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'startDate', 'ASC')"
                  *ngIf="sortColumn !== 'startDate'"
                />
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('startDate', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'startDate', 'ASC')"
                  *ngIf="sort === 'DESC' && sortColumn === 'startDate'"
                />
                <img
                  src="./assets/images/up-chevron.svg"
                  alt="up-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('startDate', 'DESC')"
                  (keydown)="handleToggleKeydown($event, 'startDate', 'DESC')"
                  *ngIf="sort === 'ASC' && sortColumn === 'startDate'"
                />
              </span>
            </th>
            <th scope="col" resizable>
              Auto Renewal On
              <span>
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('endDate', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'endDate', 'ASC')"
                  *ngIf="sortColumn !== 'endDate'"
                />
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('endDate', 'ASC')"
                  (keydown)="handleToggleKeydown($event, 'endDate', 'ASC')"
                  *ngIf="sort === 'DESC' && sortColumn === 'endDate'"
                />
                <img
                  src="./assets/images/up-chevron.svg"
                  alt="up-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('endDate', 'DESC')"
                  (keydown)="handleToggleKeydown($event, 'endDate', 'DESC')"
                  *ngIf="sort === 'ASC' && sortColumn === 'endDate'"
                />
              </span>
            </th>
            <th scope="col" resizable>Status</th>
            <th scope="col" class="text-left pe-5" resizable>Action</th>
          </thead>
          <tbody *ngIf="loader == false && projectList?.length > 0">
            <tr
              *ngFor="
                let item of projectList
                  | paginate
                    : { itemsPerPage: pageSize, currentPage: pageNo, totalItems: totalCount };
                let i = index
              "
            >
              <td>{{ item.id }}</td>
              <td>{{ item.projectName }}</td>
              <td>{{ item.stripePlan?.Plan?.planType }}</td>
              <td *ngIf="item.subDetail">
                {{ item.subDetail?.subscribedOn | date : 'mediumDate' }}
              </td>
              <td *ngIf="item.subDetail && !item?.cancel_at_period_end">
                {{ item.subDetail?.autoRenewal | date : 'mediumDate' }}
              </td>
              <td *ngIf="item?.cancel_at_period_end">{{ item.subDetail?.autoRenewal }}</td>
              <td *ngIf="!item.subDetail">------</td>
              <td *ngIf="!item.subDetail">------</td>
              <td
                *ngIf="
                  item.status != '' &&
                  item.status != null &&
                  item.status != undefined &&
                  !item?.cancel_at_period_end
                "
              >
                Expired
              </td>
              <td
              *ngIf="
                item.status != '' &&
                item.status != null &&
                item.status != undefined &&
                !item?.cancel_at_period_end &&
                item?.subDetail?.status === 'Not applicable'
              "
            >
            Not applicable
            </td>
              <td
                *ngIf="
                  (item.status == '' || item.status == null || item.status == undefined) &&
                  !item?.cancel_at_period_end
                "
              >
                Active
              </td>
              <td *ngIf="item?.cancel_at_period_end && item?.subDetail?.status === 'To be cancel'">
                Active till {{ item?.cancel_at | date : 'mediumDate' }}
              </td>
              <td *ngIf="item?.cancel_at_period_end && item?.subDetail?.status !== 'To be cancel'">
                {{ item?.subDetail?.status }}
              </td>
              <td>
                <ul class="list-inline mb-0">
                  <li class="list-inline-item mx-2 mb-2" tooltip="Edit" placement="top">
                    <button
                      type="button"
                      class="fw-bold cairo-regular btn btn-blue plan-btn p-0"
                      (click)="openEditProjectModal(editProject, item)"
                    >
                      Edit
                    </button>
                  </li>
                  <li
                    class="list-inline-item mx-2 mb-2"
                    tooltip="Upgrade"
                    placement="top"
                    *ngIf="item.stripePlan?.Plan?.planType === 'Trial Plan' &&
                    item?.subDetail?.status !== 'Not applicable'"
                  >
                    <button
                      type="button"
                      class="fw-bold cairo-regular btn btn-green plan-btn p-0"
                      (click)="redirect('upgradeplans', item)"
                    >
                      Upgrade
                    </button>
                  </li>
                  <li
                    class="list-inline-item mx-2 mb-2"
                    tooltip="Manage Billing"
                    placement="top"
                    *ngIf="item.enableBilling && item.stripePlan?.Plan?.planType !== 'Trial Plan' &&
                    item?.subDetail?.status !== 'Not applicable'"
                  >
                    <button
                      type="button"
                      class="fw-bold cairo-regular btn btn-green plan-btn manage-button"
                      (click)="redirect1()"
                    >
                      Manage Billing
                    </button>
                  </li>
                </ul>
              </td>
            </tr>
          </tbody>
          <tr *ngIf="loader == true">
            <td colspan="8" class="text-center">
              <div class="fs18 fw-bold cairo-regular my-5 text-black">Loading...</div>
            </td>
          </tr>
          <tr *ngIf="loader == false && projectList?.length == 0">
            <td colspan="8" class="text-center">
              <div class="fs18 fw-bold cairo-regular my-5 text-black">
                No Records Found
              </div>
            </td>
          </tr>
        </table>
      </div>
      <div
        class="tab-pagination px-2"
        id="tab-pagination9"
        *ngIf="loader == false && totalCount > 25"
      >
        <div class="row">
          <div class="col-md-2 align-items-center">
            <ul class="list-inline my-3">
              <li class="list-inline-item notify-pagination">
                <label class="fs12 color-grey4"  for="showEnt">Show entries</label>
              </li>
              <li class="list-inline-item">
                <select  id="showEnt"
                class="w-auto form-select fs12 color-grey4"
                  (change)="changePageSize($event.target.value)"
                  [ngModel]="pageSize"
                >
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                  <option value="150">150</option>
                </select>
              </li>
            </ul>
          </div>
          <div class="col-md-8 text-center">
            <div class="my-3 position-relative d-inline-block">
              <pagination-controls
                (pageChange)="changePageNo($event)"
                previousLabel=""
                nextLabel=""
              >
              </pagination-controls>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<ng-template #deleteList>
  <div class="modal-body">
    <div class="text-center my-4">
      <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
        Are you sure you want to Cancel Subscription '{{ projectList[cancelIndex]?.projectName }}'?
      </p>
      <button
        class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
        (click)="close()"
      >
        No
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
        type="submit"
        (click)="cancelSubscription()"
        [disabled]="deleteSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="deleteSubmitted"></em>Yes
      </button>
    </div>
  </div>
</ng-template>
<ng-template #editProject>
  <div class="modal-header px-0 py-2 border-0">
    <button
      type="button"
      class="close plans-edit-closeicon ms-auto"
      aria-label="Close"
      (click)="closeEditProject(cancelConfirmation)"
    >
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="mb-4 d-flex text-center ms-3">
    <h1 class="color-grey7 fs18 fw-bold cairo-regular my-2 ms-md-5">Edit Project</h1>
  </div>
  <div class="modal-body pt-0 px-md-5 pb-5">
    <p *ngIf="editProjectLoader" class="text-center">Loading...</p>
    <form
      class="custom-material-form"
      name="form"
      [formGroup]="projectDetailsForm"
      (ngSubmit)="onSubmit()"
      novalidate
      *ngIf="!editProjectLoader"
    >
      <div class="row">
        <div class="col-md-12">
          <div class="form-group mb-4">
            <label class="color-orange fs13"  for="projName">Enter Your Project Name</label>
            <input  id="projName"
              type="text"
              class="form-control material-input fs13"
              placeholder="Project Name"
              formControlName="projectName"
              (keypress)="alphaNum($event)"
            />
            <div
              class="color-red"
              *ngIf="submitted && projectDetailsForm.get('projectName').errors"
            >
              <small *ngIf="projectDetailsForm.get('projectName').errors.required"
                >*Enter Project Name</small
              >
            </div>
          </div>
          <div class="form-group mb-4">
            <select class="form-control fs12 material-input px-2" formControlName="TimeZoneId">
              <option value="" disabled selected hidden>Choose TimeZone</option>
              <option *ngFor="let type of timezoneList" value="{{ type.id }}">
                {{ type.location }}
              </option>
            </select>
          </div>
          <div class="mb-4" id="agm-location" class="plans-google-map mb-4">
            <label class="color-orange fs13"  for="location">Select the Location</label>
            <google-map class="agm-map project-map" [options]="mapOptions" (mapClick)="markerDragEnd($event)" id="location">
              <map-marker [position]="marker.position" [options]="marker.options" (mapDragend)="markerDragEnd($event)"></map-marker>
            </google-map>
          </div>

          <div class="form-group mb-4">
            <label class="color-orange fs13 mb-2"  for="projAdd">Project Address</label>
            <input  id="projAdd"
              type="text"
              class="form-control material-input fs14"
              formControlName="projectLocation"
              ngx-gp-autocomplete
              (onAddressChange)="handleProjectAddressLocation($event)"
              #placesRef="ngx-places"
            />
            <div
              class="color-red"
              *ngIf="submitted && projectDetailsForm.get('projectLocation').errors"
            >
              <small *ngIf="projectDetailsForm.get('projectLocation').errors.required"
                >*Project Location is required.</small
              >
            </div>
          </div>

          <div class="text-center mt-5">
            <button
              class="btn btn-grey-light color-dark-grey radius20 fs12 mb-3 me-1 me-sm-3 me-md-3 fw-bold cairo-regular px-5"
              (click)="closeEditProject(cancelConfirmation)"
              type="button"
            >
              Cancel
            </button>
            <button
              class="btn btn-orange radius20 fs12 fw-bold cairo-regular mb-3 px-5"
              type="submit"
            >
              <em
                class="fa fa-spinner"
                aria-hidden="true"
                *ngIf="submitted && projectDetailsForm.valid"
              ></em>
              Update
            </button>
          </div>
        </div>
      </div>
    </form>
  </div>
</ng-template>
<!--Confirmation Popup-->
<div id="confirm-popup11">
  <ng-template #cancelConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure you want to cancel?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetForm('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="resetForm('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
<!--Confirmation Popup-->
