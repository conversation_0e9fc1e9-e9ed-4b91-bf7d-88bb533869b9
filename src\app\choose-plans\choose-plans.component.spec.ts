import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ChoosePlansComponent } from './choose-plans.component';
import { Router, ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import { AuthService } from '../services/auth/auth.service';
import { ProjectService } from '../services/profile/project.service';
import { MixpanelService } from '../services/mixpanel.service';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { of, throwError, Subject } from 'rxjs';
import { FormsModule } from '@angular/forms';
import { NO_ERRORS_SCHEMA, TemplateRef } from '@angular/core';
import { ProjectComponent } from '../project/project.component';

describe('ChoosePlansComponent', () => {
  let component: ChoosePlansComponent;
  let fixture: ComponentFixture<ChoosePlansComponent>;
  let router: Router;
  let authService: AuthService;
  let projectService: ProjectService;
  let toastrService: ToastrService;
  let modalService: BsModalService;
  let mixpanelService: MixpanelService;
  let bsModalRef: BsModalRef;

  const mockRouter = {
    navigate: jest.fn(),
    url: '/choose-plans'
  };

  const mockActivatedRoute = {
    queryParams: of({ customerId: '123' })
  };

  const mockAuthService = {
    loggeduserIn: jest.fn(),
    getUser: jest.fn(),
    register: jest.fn(),
    getPlans: jest.fn().mockReturnValue(of({ response: [] })),
    getUpgradePlans: jest.fn().mockReturnValue(of({ response: [] }))
  };

  const mockProjectService = {
    createProject: jest.fn(),
    getSingleProject: jest.fn(),
    upgradeProjectPlans: jest.fn(),
    checkout: jest.fn(),
    checkoutSession: jest.fn().mockReturnValue(of({ data: { url: 'https://test-url.com' } })),
    newProjectCreatedWithProjectPlan: new Subject()
  };

  const mockToastrService = {
    success: jest.fn(),
    error: jest.fn(),
    clear: jest.fn()
  };

  const mockBsModalRef = {
    content: {
      closeBtnName: 'Close'
    },
    hide: jest.fn()
  };

  const mockModalService = {
    show: jest.fn().mockReturnValue(mockBsModalRef)
  };

  const mockMixpanelService = {
    addMixpanelEvents: jest.fn(),
    createUserProfile: jest.fn()
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ChoosePlansComponent],
      imports: [FormsModule],
      providers: [
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: AuthService, useValue: mockAuthService },
        { provide: ProjectService, useValue: mockProjectService },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: BsModalService, useValue: mockModalService },
        { provide: MixpanelService, useValue: mockMixpanelService },
        { provide: Socket, useValue: {} }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ChoosePlansComponent);
    component = fixture.componentInstance;
    router = TestBed.inject(Router);
    authService = TestBed.inject(AuthService);
    projectService = TestBed.inject(ProjectService);
    toastrService = TestBed.inject(ToastrService);
    modalService = TestBed.inject(BsModalService);
    mixpanelService = TestBed.inject(MixpanelService);
    bsModalRef = mockBsModalRef as any;

    // Mock localStorage
    const mockBasicDetails = {
      email: '<EMAIL>',
      phoneCode: '+1',
      phoneNumber: '1234567890'
    };
    const mockCompanyDetails = {
      fullName: 'John',
      lastName: 'Doe'
    };
    localStorage.setItem('basic', JSON.stringify(mockBasicDetails));
    localStorage.setItem('company', JSON.stringify(mockCompanyDetails));

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  afterEach(() => {
    localStorage.clear();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('getloginData', () => {
    it('should set loggedIn to true when user is logged in', () => {
      mockAuthService.loggeduserIn.mockReturnValue(true);
      component.getloginData();
      expect(component.loggedIn).toBe(true);
    });

    it('should handle not logged in user with missing basic details', () => {
      mockAuthService.loggeduserIn.mockReturnValue(false);
      localStorage.removeItem('basic');
      component.getloginData();
      expect(toastrService.error).toHaveBeenCalledWith('Please Fill the Basic Details', 'OOPS');
      expect(router.navigate).toHaveBeenCalledWith(['/home']);
    });
  });

  describe('getPlans', () => {
    it('should fetch monthly plans successfully', () => {
      const mockPlans = { response: [{ id: 1, name: 'Basic Plan' }] };
      mockAuthService.getPlans.mockReturnValue(of(mockPlans));

      component.getPlans('monthly');

      expect(component.loader).toBe(false);
      expect(component.planList).toEqual(mockPlans.response);
    });

    it('should handle error when fetching plans', () => {
      mockAuthService.getPlans.mockReturnValue(throwError(() => ({ message: 'Error' })));

      component.getPlans('monthly');

      expect(toastrService.error).toHaveBeenCalledWith('Error', 'OOPS!');
      expect(router.navigate).toHaveBeenCalledWith(['/home']);
    });
  });

  describe('SelectedPlan', () => {
    beforeEach(() => {
      component.planList = [{
        id: 1,
        Plan: { planType: 'Project Plan' }
      }];
      component.submitted = false;
      component.formSubmitted = false;
      component.selectedIndex = -1;
    });

    // it('should handle project plan selection', () => {
    //   const setProjectPlanSpy = jest.spyOn(component, 'setProjectPlan');
    //   component.SelectedPlan(0);

    //   expect(component.selectedIndex).toBe(0);
    //   expect(component.submitted).toBe(true);
    //   expect(component.formSubmitted).toBe(true);
    //   expect(setProjectPlanSpy).toHaveBeenCalled();
    // });

    it('should handle enterprise plan selection', () => {
      component.planList[0].Plan.planType = 'Enterprise Plan';
      component.SelectedPlan(0);

      expect(toastrService.success).toHaveBeenCalledWith('Please Contact Sales team.', 'Success');
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
    });
  });

  describe('createProject', () => {
    it('should create project successfully', () => {
      const mockResponse = { message: 'Project created successfully' };
      mockProjectService.createProject.mockReturnValue(of(mockResponse));

      component.createProject({ projectName: 'Test Project' });

      expect(toastrService.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
    });

    it('should handle project creation error', () => {
      mockProjectService.createProject.mockReturnValue(throwError(() => ({ message: 'Error' })));

      component.createProject({ projectName: 'Test Project' });

      expect(toastrService.error).toHaveBeenCalledWith('Error', 'OOPS!');
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
    });
  });

  describe('checkChange', () => {
    it('should get monthly plans when not upgrading', () => {
      component.urlData = 'choose-plans';
      component.billed = false;
      const getPlansSpy = jest.spyOn(component, 'getPlans');

      component.checkChange();

      expect(getPlansSpy).toHaveBeenCalledWith('monthly');
    });

    it('should get yearly plans when not upgrading', () => {
      component.urlData = 'choose-plans';
      component.billed = true;
      const getPlansSpy = jest.spyOn(component, 'getPlans');

      component.checkChange();

      expect(getPlansSpy).toHaveBeenCalledWith('yearly');
    });

    it('should get upgrade plans when upgrading', () => {
      component.urlData = 'upgradeplans';
      component.billed = false;
      const getUpgradePlansSpy = jest.spyOn(component, 'getUpgradePlans');

      component.checkChange();

      expect(getUpgradePlansSpy).toHaveBeenCalledWith('monthly');
    });
  });

  describe('redirect', () => {
    it('should open modal when logged in and not upgrading', () => {
      component.loggedIn = true;
      component.upgradePlans = false;
      const openModalSpy = jest.spyOn(component, 'openModal');

      component.redirect('register-project');

      expect(openModalSpy).toHaveBeenCalled();
    });

    it('should navigate when not logged in', () => {
      component.loggedIn = false;

      component.redirect('register-project');

      expect(router.navigate).toHaveBeenCalledWith(['/register-project']);
    });

    it('should navigate when upgrading plans', () => {
      component.loggedIn = true;
      component.upgradePlans = true;

      component.redirect('profile');

      expect(router.navigate).toHaveBeenCalledWith(['/profile']);
    });
  });

  describe('ngOnInit', () => {
    it('should clear toastr and subscribe to newProjectCreatedWithProjectPlan', () => {
      const createProject1Spy = jest.spyOn(component, 'createProject1').mockImplementation();

      component.ngOnInit();

      expect(toastrService.clear).toHaveBeenCalled();

      // Emit value to test subscription
      mockProjectService.newProjectCreatedWithProjectPlan.next('test-project');
      expect(createProject1Spy).toHaveBeenCalled();
    });

    it('should not call createProject1 when subscription value is undefined', () => {
      const createProject1Spy = jest.spyOn(component, 'createProject1').mockImplementation();

      component.ngOnInit();

      mockProjectService.newProjectCreatedWithProjectPlan.next(undefined);
      expect(createProject1Spy).not.toHaveBeenCalled();
    });

    it('should not call createProject1 when subscription value is null', () => {
      const createProject1Spy = jest.spyOn(component, 'createProject1').mockImplementation();

      component.ngOnInit();

      mockProjectService.newProjectCreatedWithProjectPlan.next(null);
      expect(createProject1Spy).not.toHaveBeenCalled();
    });

    it('should not call createProject1 when subscription value is empty string', () => {
      const createProject1Spy = jest.spyOn(component, 'createProject1').mockImplementation();

      component.ngOnInit();

      mockProjectService.newProjectCreatedWithProjectPlan.next('');
      expect(createProject1Spy).not.toHaveBeenCalled();
    });
  });

  describe('setPlans', () => {
    it('should get yearly plans when planid and interval year exist in localStorage', () => {
      localStorage.setItem('planid', '123');
      localStorage.setItem('interval', 'year');
      const getPlansSpy = jest.spyOn(component, 'getPlans').mockImplementation();

      component.setPlans();

      expect(getPlansSpy).toHaveBeenCalledWith('yearly');
      expect(component.billed).toBe(true);
      expect(component.selectedPlanId).toBe(123);
    });

    it('should get monthly plans when planid exists but interval is not year', () => {
      localStorage.setItem('planid', '456');
      localStorage.setItem('interval', 'month');
      const getPlansSpy = jest.spyOn(component, 'getPlans').mockImplementation();

      component.setPlans();

      expect(getPlansSpy).toHaveBeenCalledWith('monthly');
      expect(component.selectedPlanId).toBe(456);
    });

    it('should get monthly plans when planid does not exist', () => {
      localStorage.removeItem('planid');
      const getPlansSpy = jest.spyOn(component, 'getPlans').mockImplementation();

      component.setPlans();

      expect(getPlansSpy).toHaveBeenCalledWith('monthly');
    });

    it('should get monthly plans when planid is null', () => {
      localStorage.setItem('planid', null);
      const getPlansSpy = jest.spyOn(component, 'getPlans').mockImplementation();

      component.setPlans();

      expect(getPlansSpy).toHaveBeenCalledWith('monthly');
    });
  });

  describe('handleDownKeydown', () => {
    it('should call highLightPlan when Enter key is pressed', () => {
      const mockItem = { id: 1, name: 'Test Plan' };
      const mockEvent = {
        key: 'Enter',
        preventDefault: jest.fn()
      } as any;
      const highLightPlanSpy = jest.spyOn(component, 'highLightPlan').mockImplementation();

      component.handleDownKeydown(mockEvent, mockItem);

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(highLightPlanSpy).toHaveBeenCalledWith(mockItem);
    });

    it('should call highLightPlan when Space key is pressed', () => {
      const mockItem = { id: 2, name: 'Test Plan 2' };
      const mockEvent = {
        key: ' ',
        preventDefault: jest.fn()
      } as any;
      const highLightPlanSpy = jest.spyOn(component, 'highLightPlan').mockImplementation();

      component.handleDownKeydown(mockEvent, mockItem);

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(highLightPlanSpy).toHaveBeenCalledWith(mockItem);
    });

    it('should not call highLightPlan for other keys', () => {
      const mockItem = { id: 3, name: 'Test Plan 3' };
      const mockEvent = {
        key: 'Tab',
        preventDefault: jest.fn()
      } as any;
      const highLightPlanSpy = jest.spyOn(component, 'highLightPlan').mockImplementation();

      component.handleDownKeydown(mockEvent, mockItem);

      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      expect(highLightPlanSpy).not.toHaveBeenCalled();
    });
  });

  describe('highLightPlan', () => {
    it('should set selectedPlanId to the plan id', () => {
      const mockPlan = { id: 789 };

      component.highLightPlan(mockPlan);

      expect(component.selectedPlanId).toBe(789);
    });
  });

  describe('getloginData - additional scenarios', () => {
    it('should handle not logged in user with missing company details', () => {
      mockAuthService.loggeduserIn.mockReturnValue(false);
      localStorage.removeItem('company');
      component.getloginData();
      expect(toastrService.error).toHaveBeenCalledWith('Please Fill the Company Details', 'OOPS');
      expect(router.navigate).toHaveBeenCalledWith(['/home']);
    });

    it('should handle not logged in user with missing project details', () => {
      mockAuthService.loggeduserIn.mockReturnValue(false);
      localStorage.removeItem('project');
      component.getloginData();
      expect(toastrService.error).toHaveBeenCalledWith('Please Fill the Project Details', 'OOPS');
      expect(router.navigate).toHaveBeenCalledWith(['/home']);
    });

    it('should call setPlans when user is not logged in and all details are present', () => {
      mockAuthService.loggeduserIn.mockReturnValue(false);
      localStorage.setItem('project', JSON.stringify({ name: 'Test Project' }));
      const setPlansSpy = jest.spyOn(component, 'setPlans').mockImplementation();

      component.getloginData();

      expect(setPlansSpy).toHaveBeenCalled();
    });
  });

  describe('getUpgradePlans', () => {
    it('should fetch upgrade plans successfully', () => {
      const mockUpgradePlans = { response: [{ id: 1, name: 'Upgrade Plan' }] };
      mockAuthService.getUpgradePlans.mockReturnValue(of(mockUpgradePlans));

      component.getUpgradePlans('monthly');

      expect(component.loader).toBe(false);
      expect(component.planList).toEqual(mockUpgradePlans.response);
    });

    it('should handle error when fetching upgrade plans', () => {
      mockAuthService.getUpgradePlans.mockReturnValue(throwError(() => ({ message: 'Upgrade Error' })));

      component.getUpgradePlans('yearly');

      expect(toastrService.error).toHaveBeenCalledWith('Upgrade Error', 'OOPS!');
      expect(router.navigate).toHaveBeenCalledWith(['/home']);
    });

    it('should set loader to true initially', () => {
      mockAuthService.getUpgradePlans.mockReturnValue(of({ response: [] }));
      component.loader = false;

      component.getUpgradePlans('monthly');

      expect(component.loader).toBe(false);
    });
  });

  describe('checkProject', () => {
    it('should return false when ProjectId is undefined', () => {
      localStorage.removeItem('ProjectId');

      const result = component.checkProject();

      expect(result).toBe(false);
    });

    it('should return false when ProjectId is null', () => {
      localStorage.setItem('ProjectId', 'null');

      const result = component.checkProject();

      expect(result).toBe(true);
    });

    it('should return true when ProjectId exists', () => {
      localStorage.setItem('ProjectId', '123');

      const result = component.checkProject();

      expect(result).toBe(true);
    });
  });

  describe('getAuthUser', () => {
    it('should get user data successfully', () => {
      const mockUser = { id: 1, name: 'Test User' };
      mockAuthService.getUser.mockReturnValue(of(mockUser));

      component.getAuthUser();

      expect(component.currentUser).toEqual(mockUser);
    });

    it('should handle error when getting user data', () => {
      mockAuthService.getUser.mockReturnValue(throwError(() => new Error('User fetch error')));

      expect(() => component.getAuthUser()).not.toThrow();
    });
  });

  describe('openModal', () => {
    it('should open project modal with correct configuration', () => {
      component.openModal();

      expect(modalService.show).toHaveBeenCalledWith(ProjectComponent, {
        backdrop: 'static',
        keyboard: false,
        class: 'custom-modal',
      });
      expect(component.bsModalRef.content.closeBtnName).toBe('Close');
    });
  });

  describe('openModal1', () => {
    it('should open modal with template and correct configuration', () => {
      const mockTemplate = {} as TemplateRef<any>;

      component.openModal1(mockTemplate);

      expect(modalService.show).toHaveBeenCalledWith(mockTemplate, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-md thanks-popup custom-modal'
      });
    });
  });

  describe('checkout', () => {
    beforeEach(() => {
      // Mock window.open
      Object.defineProperty(window, 'open', {
        writable: true,
        value: jest.fn()
      });
    });

    it('should open checkout URL successfully', () => {
      const mockPlanData = { id: 1, name: 'Test Plan' };
      const mockResponse = { data: { url: 'https://checkout.stripe.com/test' } };
      mockProjectService.checkout.mockReturnValue(of(mockResponse));

      component.checkout(mockPlanData);

      expect(mockProjectService.checkout).toHaveBeenCalledWith({
        planData: mockPlanData,
        url: mockRouter.url
      });
      expect(window.open).toHaveBeenCalledWith(mockResponse.data.url, '_blank');
    });

    it('should handle checkout error with status code 400', () => {
      const mockPlanData = { id: 1, name: 'Test Plan' };
      const mockError = { message: { statusCode: 400 } };
      mockProjectService.checkout.mockReturnValue(throwError(() => mockError));
      const showErrorSpy = jest.spyOn(component, 'showError').mockImplementation();

      component.checkout(mockPlanData);

      expect(showErrorSpy).toHaveBeenCalledWith(mockError);
    });

    it('should handle checkout error without message', () => {
      const mockPlanData = { id: 1, name: 'Test Plan' };
      const mockError = {};
      mockProjectService.checkout.mockReturnValue(throwError(() => mockError));

      component.checkout(mockPlanData);

      expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should handle checkout error with message', () => {
      const mockPlanData = { id: 1, name: 'Test Plan' };
      const mockError = { message: 'Checkout failed' };
      mockProjectService.checkout.mockReturnValue(throwError(() => mockError));

      component.checkout(mockPlanData);

      expect(toastrService.error).toHaveBeenCalledWith('Checkout failed', 'OOPS!');
    });
  });

  describe('checkoutSession', () => {
    beforeEach(() => {
      Object.defineProperty(window, 'open', {
        writable: true,
        value: jest.fn()
      });
    });

    it('should open checkout session URL successfully', () => {
      const mockPlanData = { id: 1, name: 'Test Plan' };
      const mockResponse = { data: { url: 'https://checkout.stripe.com/session' } };
      mockProjectService.checkoutSession.mockReturnValue(of(mockResponse));

      component.checkoutSession(mockPlanData);

      const expectedPayload = {
        planData: mockPlanData,
        url: mockRouter.url,
        email: '<EMAIL>',
        name: 'John Doe',
        phone: '*************'
      };
      expect(mockProjectService.checkoutSession).toHaveBeenCalledWith(expectedPayload);
      expect(window.open).toHaveBeenCalledWith(mockResponse.data.url, '_self');
    });

    it('should handle checkout session error with status code 400', () => {
      const mockPlanData = { id: 1, name: 'Test Plan' };
      const mockError = { message: { statusCode: 400 } };
      mockProjectService.checkoutSession.mockReturnValue(throwError(() => mockError));
      const showErrorSpy = jest.spyOn(component, 'showError').mockImplementation();

      component.checkoutSession(mockPlanData);

      expect(showErrorSpy).toHaveBeenCalledWith(mockError);
    });

    it('should handle checkout session error without message', () => {
      const mockPlanData = { id: 1, name: 'Test Plan' };
      const mockError = {};
      mockProjectService.checkoutSession.mockReturnValue(throwError(() => mockError));

      component.checkoutSession(mockPlanData);

      expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should handle checkout session error with message', () => {
      const mockPlanData = { id: 1, name: 'Test Plan' };
      const mockError = { message: 'Session failed' };
      mockProjectService.checkoutSession.mockReturnValue(throwError(() => mockError));

      component.checkoutSession(mockPlanData);

      expect(toastrService.error).toHaveBeenCalledWith('Session failed', 'OOPS!');
    });
  });

  describe('setProjectPlan', () => {
    const mockPlanData = { id: 123, name: 'Project Plan' };

    beforeEach(() => {
      component.submitted = true;
      component.formSubmitted = true;
    });

    it('should handle project plan for logged in user not upgrading', () => {
      component.upgradePlans = false;
      component.loggedIn = true;
      component.billed = false;
      const checkoutSpy = jest.spyOn(component, 'checkout').mockImplementation();

      component.setProjectPlan(mockPlanData);

      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(localStorage.getItem('planid')).toBe(123);
      expect(localStorage.getItem('interval')).toBe('month');
      expect(checkoutSpy).toHaveBeenCalledWith(mockPlanData);
    });

    it('should handle project plan for logged in user not upgrading with yearly billing', () => {
      component.upgradePlans = false;
      component.loggedIn = true;
      component.billed = true;
      const checkoutSpy = jest.spyOn(component, 'checkout').mockImplementation();

      component.setProjectPlan(mockPlanData);

      expect(localStorage.getItem('interval')).toBe('year');
      expect(checkoutSpy).toHaveBeenCalledWith(mockPlanData);
    });

    it('should handle project plan for not logged in user', () => {
      component.upgradePlans = false;
      component.loggedIn = false;
      const checkoutSessionSpy = jest.spyOn(component, 'checkoutSession').mockImplementation();

      component.setProjectPlan(mockPlanData);

      expect(checkoutSessionSpy).toHaveBeenCalledWith(mockPlanData);
    });

    it('should handle project plan for upgrading user', () => {
      component.upgradePlans = true;
      component.loggedIn = true;
      component.billed = false;
      const checkoutSpy = jest.spyOn(component, 'checkout').mockImplementation();

      component.setProjectPlan(mockPlanData);

      expect(localStorage.getItem('upgradePlanId')).toBe(123);
      expect(localStorage.getItem('upgradeInterval')).toBe('month');
      expect(checkoutSpy).toHaveBeenCalledWith(mockPlanData);
    });

    it('should handle project plan for upgrading user with yearly billing', () => {
      component.upgradePlans = true;
      component.loggedIn = false;
      component.billed = true;
      const checkoutSessionSpy = jest.spyOn(component, 'checkoutSession').mockImplementation();

      component.setProjectPlan(mockPlanData);

      expect(localStorage.getItem('upgradeInterval')).toBe('year');
      expect(checkoutSessionSpy).toHaveBeenCalledWith(mockPlanData);
    });
  });

  describe('throwError', () => {
    beforeEach(() => {
      component.modalRef = mockBsModalRef as any;
    });

    it('should handle error with status code 400', () => {
      const mockError = { message: { statusCode: 400 } };
      const showErrorSpy = jest.spyOn(component, 'showError').mockImplementation();

      component.throwError(mockError);

      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(showErrorSpy).toHaveBeenCalledWith(mockError);
      expect(component.formSubmitted).toBe(false);
    });

    it('should handle error without message', () => {
      const mockError = {};

      component.throwError(mockError);

      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should handle error with message', () => {
      const mockError = { message: 'Custom error message' };

      component.throwError(mockError);

      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(toastrService.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
    });
  });

  describe('upgradeToNewPlans', () => {
    beforeEach(() => {
      component.upgradeProjectId = 123;
      component.modalRef = mockBsModalRef as any;
      localStorage.setItem('upgradePlanId', '456');
      localStorage.setItem('upgradeInterval', 'year');
    });

    it('should upgrade plans successfully', () => {
      const mockPlanData = { id: 789 };
      const mockResponse = { message: 'Plan upgraded successfully' };
      mockProjectService.upgradeProjectPlans.mockReturnValue(of(mockResponse));

      component.upgradeToNewPlans(mockPlanData);

      const expectedData = {
        PlanId: 789,
        existcard: false,
        ProjectId: 123
      };
      expect(mockProjectService.upgradeProjectPlans).toHaveBeenCalledWith(expectedData);
      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(toastrService.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(localStorage.getItem('upgradePlanId')).toBeNull();
      expect(localStorage.getItem('upgradeInterval')).toBeNull();
      expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
    });

    it('should handle upgrade error with status code 400', () => {
      const mockPlanData = { id: 789 };
      const mockError = { message: { statusCode: 400 } };
      mockProjectService.upgradeProjectPlans.mockReturnValue(throwError(() => mockError));
      const showErrorSpy = jest.spyOn(component, 'showError').mockImplementation();

      component.upgradeToNewPlans(mockPlanData);

      expect(showErrorSpy).toHaveBeenCalledWith(mockError);
    });

    it('should handle upgrade error without message', () => {
      const mockPlanData = { id: 789 };
      const mockError = {};
      mockProjectService.upgradeProjectPlans.mockReturnValue(throwError(() => mockError));

      component.upgradeToNewPlans(mockPlanData);

      expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should handle upgrade error with message', () => {
      const mockPlanData = { id: 789 };
      const mockError = { message: 'Upgrade failed' };
      mockProjectService.upgradeProjectPlans.mockReturnValue(throwError(() => mockError));

      component.upgradeToNewPlans(mockPlanData);

      expect(toastrService.error).toHaveBeenCalledWith('Upgrade failed', 'OOPS!');
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
    });
  });

  describe('upgradeNewPlans', () => {
    beforeEach(() => {
      mockRouter.url = '/upgradeplans/123?customerId=456';
      localStorage.setItem('upgradePlanId', '789');
    });

    it('should set upgrade plans and open modal', () => {
      const openModal1Spy = jest.spyOn(component, 'openModal1').mockImplementation();
      const mockResponse = { message: 'Upgrade successful' };
      mockProjectService.upgradeProjectPlans.mockReturnValue(of(mockResponse));

      component.upgradeNewPlans();

      expect(component.upgradePlans).toBe(true);
      expect(component.upgradeProjectId).toBe(123);
      expect(openModal1Spy).toHaveBeenCalled();
      expect(mockProjectService.upgradeProjectPlans).toHaveBeenCalledWith({
        PlanId: 789,
        ProjectId: 123,
        stripeCustomerId: component.customerId
      });
    });
  });

  describe('checkUpgradePlan', () => {
    beforeEach(() => {
      component.loggedIn = true;
    });

    it('should handle logged in user with missing new project details', () => {
      localStorage.removeItem('newproject');
      const getAuthUserSpy = jest.spyOn(component, 'getAuthUser').mockImplementation();
      const checkProjectSpy = jest.spyOn(component, 'checkProject').mockReturnValue(true);
      const openModalSpy = jest.spyOn(component, 'openModal').mockImplementation();
      const setPlansSpy = jest.spyOn(component, 'setPlans').mockImplementation();

      component.checkUpgradePlan();

      expect(getAuthUserSpy).toHaveBeenCalled();
      expect(checkProjectSpy).toHaveBeenCalled();
      expect(toastrService.error).toHaveBeenCalledWith('Please Fill the project Details.', 'OOPS!');
      expect(openModalSpy).toHaveBeenCalled();
      expect(setPlansSpy).toHaveBeenCalled();
    });

    it('should navigate to dashboard when project check fails', () => {
      localStorage.removeItem('newproject');
      const getAuthUserSpy = jest.spyOn(component, 'getAuthUser').mockImplementation();
      const checkProjectSpy = jest.spyOn(component, 'checkProject').mockReturnValue(false);
      const setPlansSpy = jest.spyOn(component, 'setPlans').mockImplementation();

      component.checkUpgradePlan();

      expect(getAuthUserSpy).toHaveBeenCalled();
      expect(checkProjectSpy).toHaveBeenCalled();
      expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
      expect(setPlansSpy).toHaveBeenCalled();
    });

    it('should hide modal when new project details exist', () => {
      localStorage.setItem('newproject', JSON.stringify({ name: 'Test Project' }));
      const getAuthUserSpy = jest.spyOn(component, 'getAuthUser').mockImplementation();
      const setPlansSpy = jest.spyOn(component, 'setPlans').mockImplementation();
      component.bsModalRef = mockBsModalRef as any;

      component.checkUpgradePlan();

      expect(getAuthUserSpy).toHaveBeenCalled();
      expect(component.bsModalRef.hide).toHaveBeenCalled();
      expect(setPlansSpy).toHaveBeenCalled();
    });
  });

  describe('ngAfterViewInit', () => {
    beforeEach(() => {
      component.loggedIn = true;
    });

    it('should handle upgradeplans URL with query params for logged in user', () => {
      mockRouter.url = '/upgradeplans?customerId=123';
      const setPlansSpy = jest.spyOn(component, 'setPlans').mockImplementation();
      const upgradeNewPlansSpy = jest.spyOn(component, 'upgradeNewPlans').mockImplementation();

      component.ngAfterViewInit();

      expect(component.urlData).toBe('upgradeplans');
      expect(setPlansSpy).toHaveBeenCalled();
      expect(component.customerId).toBe('123');
      expect(upgradeNewPlansSpy).toHaveBeenCalled();
    });

    it('should handle choose-plans URL with query params for logged in user', () => {
      mockRouter.url = '/choose-plans?customerId=456';
      const setPlansSpy = jest.spyOn(component, 'setPlans').mockImplementation();
      const createProject1Spy = jest.spyOn(component, 'createProject1').mockImplementation();

      component.ngAfterViewInit();

      expect(component.urlData).toBe('choose-plans');
      expect(setPlansSpy).toHaveBeenCalled();
      expect(createProject1Spy).toHaveBeenCalled();
    });

    it('should handle URL with query params for not logged in user', () => {
      component.loggedIn = false;
      mockRouter.url = '/choose-plans?customerId=789';
      const registerUser1Spy = jest.spyOn(component, 'registerUser1').mockImplementation();

      component.ngAfterViewInit();

      expect(registerUser1Spy).toHaveBeenCalled();
    });

    it('should handle URL without query params and not upgradeplans', () => {
      mockRouter.url = '/choose-plans';
      const checkUpgradePlanSpy = jest.spyOn(component, 'checkUpgradePlan').mockImplementation();

      component.ngAfterViewInit();

      expect(component.urlData).toBe('choose-plans');
      expect(checkUpgradePlanSpy).toHaveBeenCalled();
    });

    it('should handle upgradeplans URL for logged in user without query params', () => {
      mockRouter.url = '/upgradeplans';
      const upgradePlanDataSpy = jest.spyOn(component, 'upgradePlanData').mockImplementation();

      component.ngAfterViewInit();

      expect(component.urlData).toBe('upgradeplans');
      expect(upgradePlanDataSpy).toHaveBeenCalledWith(['', 'upgradeplans']);
    });
  });

  describe('upgradePlanData', () => {
    it('should get single project and set upgrade plans for yearly billing', () => {
      const urlValue = ['', 'upgradeplans', '123'];
      const mockProjectData = {
        data: {
          PlanId: 456,
          stripePlan: { stripePlanName: 'yearly' }
        }
      };
      mockProjectService.getSingleProject.mockReturnValue(of(mockProjectData));
      const getUpgradePlansSpy = jest.spyOn(component, 'getUpgradePlans').mockImplementation();

      component.upgradePlanData(urlValue);

      expect(mockProjectService.getSingleProject).toHaveBeenCalledWith({ ProjectId: '123' });
      expect(component.projectList).toEqual(mockProjectData.data);
      expect(component.selectedPlanId).toBe(456);
      expect(component.billed).toBe(true);
      expect(getUpgradePlansSpy).toHaveBeenCalledWith('yearly');
    });

    it('should get single project and set upgrade plans for monthly billing', () => {
      const urlValue = ['', 'upgradeplans', '789'];
      const mockProjectData = {
        data: {
          PlanId: 101,
          stripePlan: { stripePlanName: 'monthly' }
        }
      };
      mockProjectService.getSingleProject.mockReturnValue(of(mockProjectData));
      const getUpgradePlansSpy = jest.spyOn(component, 'getUpgradePlans').mockImplementation();

      component.upgradePlanData(urlValue);

      expect(component.selectedPlanId).toBe(101);
      expect(component.billed).toBe(false);
      expect(getUpgradePlansSpy).toHaveBeenCalledWith('monthly');
    });

    it('should handle invalid project access', () => {
      const urlValue = ['', 'upgradeplans', '999'];
      const mockProjectData = { data: null };
      mockProjectService.getSingleProject.mockReturnValue(of(mockProjectData));

      component.upgradePlanData(urlValue);

      expect(toastrService.error).toHaveBeenCalledWith('Invalid access', 'OOPS!');
      expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
    });

    it('should handle project fetch error', () => {
      const urlValue = ['', 'upgradeplans', '888'];
      const mockError = { message: 'Project not found' };
      mockProjectService.getSingleProject.mockReturnValue(throwError(() => mockError));

      component.upgradePlanData(urlValue);

      expect(toastrService.error).toHaveBeenCalledWith('Project not found', 'OOPS!');
      expect(router.navigate).toHaveBeenCalledWith(['/home']);
    });
  });

  describe('SelectedPlan - additional scenarios', () => {
    beforeEach(() => {
      component.planList = [{
        id: 1,
        Plan: { planType: 'Standard Plan' }
      }];
    });

    it('should handle standard plan selection for logged in user', () => {
      component.loggedIn = true;
      component.currentUser = {
        firstName: 'John',
        phoneNumber: '1234567890',
        email: '<EMAIL>'
      };
      localStorage.setItem('newproject', JSON.stringify({
        projectLocation: 'Test Location',
        projectLocationLatitude: 40.7128,
        projectLocationLongitude: -74.0060,
        projectName: 'Test Project',
        ParentCompanyId: 1
      }));
      const createProjectSpy = jest.spyOn(component, 'createProject').mockImplementation();

      component.SelectedPlan(0);

      expect(component.selectedIndex).toBe(0);
      expect(component.submitted).toBe(true);
      expect(component.formSubmitted).toBe(true);
      expect(component.selectedPlanId).toBe(1);
      expect(toastrService.clear).toHaveBeenCalled();
      expect(createProjectSpy).toHaveBeenCalled();
    });

    it('should handle standard plan selection for not logged in user', () => {
      component.loggedIn = false;
      localStorage.setItem('basic', JSON.stringify({ email: '<EMAIL>' }));
      localStorage.setItem('company', JSON.stringify({ name: 'Test Company' }));
      localStorage.setItem('project', JSON.stringify({ name: 'Test Project' }));
      const registerUserSpy = jest.spyOn(component, 'registerUser').mockImplementation();

      component.SelectedPlan(0);

      expect(registerUserSpy).toHaveBeenCalled();
    });

    it('should handle project plan selection', () => {
      component.planList[0].Plan.planType = 'Project Plan';
      const setProjectPlanSpy = jest.spyOn(component, 'setProjectPlan').mockImplementation();

      component.SelectedPlan(0);

      expect(setProjectPlanSpy).toHaveBeenCalledWith(component.planList[0]);
    });
  });

  describe('registerUser1', () => {
    it('should register user successfully', () => {
      const mockResponse = { message: 'User registered successfully' };
      mockAuthService.register.mockReturnValue(of(mockResponse));
      localStorage.setItem('basic', JSON.stringify({ email: '<EMAIL>' }));
      localStorage.setItem('company', JSON.stringify({ name: 'Test Company' }));
      localStorage.setItem('project', JSON.stringify({ name: 'Test Project' }));
      localStorage.setItem('planid', '123');

      component.registerUser1();

      expect(toastrService.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(router.navigate).toHaveBeenCalledWith(['/login']);
    });

    it('should handle registration error', () => {
      const mockError = { message: 'Registration failed' };
      mockAuthService.register.mockReturnValue(throwError(() => mockError));
      const throwErrorSpy = jest.spyOn(component, 'throwError').mockImplementation();

      component.registerUser1();

      expect(throwErrorSpy).toHaveBeenCalledWith(mockError);
    });
  });

  describe('createProject1', () => {
    it('should create project successfully', () => {
      const mockResponse = { message: 'Project created successfully' };
      const mockUser = { firstName: 'John', phoneNumber: '1234567890', email: '<EMAIL>' };
      mockAuthService.getUser.mockReturnValue(of(mockUser));
      mockProjectService.createProject.mockReturnValue(of(mockResponse));
      localStorage.setItem('newproject', JSON.stringify({
        projectLocation: 'Test Location',
        projectLocationLatitude: 40.7128,
        projectLocationLongitude: -74.0060,
        projectName: 'Test Project',
        ParentCompanyId: 1
      }));
      localStorage.setItem('planid', '123');

      component.createProject1();

      expect(toastrService.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(router.navigate).toHaveBeenCalledWith(['/dashboard']);
    });

  });

  describe('showError', () => {
    it('should display validation errors', () => {
      const mockError = {
        message: {
          error: {
            email: ['Email is required'],
            password: ['Password must be at least 8 characters']
          }
        }
      };

      component.showError(mockError);

      expect(toastrService.error).toHaveBeenCalledWith('Email is required', 'OOPS!');
      expect(toastrService.error).toHaveBeenCalledWith('Password must be at least 8 characters', 'OOPS!');
    });

    it('should handle error without validation details', () => {
      const mockError = { message: { error: {} } };

      component.showError(mockError);

      // Should not call toastr.error if no validation errors
      expect(toastrService.error).not.toHaveBeenCalled();
    });
  });

  describe('registerUser', () => {
    const mockData = {
      basicDetails: { email: '<EMAIL>' },
      companyDetails: { name: 'Test Company' },
      projectDetails: { name: 'Test Project' },
      planData: { id: 1 }
    };

    it('should register user successfully', () => {
      const mockResponse = { message: 'Registration successful' };
      mockAuthService.register.mockReturnValue(of(mockResponse));
      const createProjectSpy = jest.spyOn(component, 'createProject').mockImplementation();

      component.registerUser(mockData);

      expect(toastrService.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
    });

    it('should handle registration error with status code 400', () => {
      const mockError = { message: { statusCode: 400 } };
      mockAuthService.register.mockReturnValue(throwError(() => mockError));
      const showErrorSpy = jest.spyOn(component, 'showError').mockImplementation();

      component.registerUser(mockData);

      expect(showErrorSpy).toHaveBeenCalledWith(mockError);
      expect(component.formSubmitted).toBe(false);
    });

    it('should handle registration error without message', () => {
      const mockError = {};
      mockAuthService.register.mockReturnValue(throwError(() => mockError));

      component.registerUser(mockData);

      expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should handle registration error with message', () => {
      const mockError = { message: 'Registration failed' };
      mockAuthService.register.mockReturnValue(throwError(() => mockError));

      component.registerUser(mockData);

      expect(toastrService.error).toHaveBeenCalledWith('Registration failed', 'OOPS!');
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
    });
  });

  describe('checkChange - additional scenarios', () => {
    it('should get yearly upgrade plans when upgrading and billed is true', () => {
      component.urlData = 'upgradeplans';
      component.billed = true;
      const getUpgradePlansSpy = jest.spyOn(component, 'getUpgradePlans').mockImplementation();

      component.checkChange();

      expect(getUpgradePlansSpy).toHaveBeenCalledWith('yearly');
    });
  });

  describe('Component initialization and edge cases', () => {
    it('should handle component creation with all dependencies', () => {
      expect(component).toBeTruthy();
      expect(component.planList).toEqual([]);
      expect(component.billed).toBe(false);
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.loggedIn).toBe(false);
      expect(component.upgradePlans).toBe(false);
      expect(component.loader).toBe(true);
    });

    it('should handle empty plan list scenarios', () => {
      component.planList = [];

      expect(() => component.SelectedPlan(0)).not.toThrow();
    });

    it('should handle invalid plan index', () => {
      component.planList = [{ id: 1, Plan: { planType: 'Test Plan' } }];

      expect(() => component.SelectedPlan(-1)).not.toThrow();
      expect(() => component.SelectedPlan(999)).not.toThrow();
    });

    it('should handle missing localStorage data gracefully', () => {
      localStorage.clear();

      expect(() => component.setPlans()).not.toThrow();
      expect(() => component.getloginData()).not.toThrow();
    });

    it('should handle URL parsing edge cases', () => {
      mockRouter.url = '';

      expect(() => component.ngAfterViewInit()).not.toThrow();
    });

    it('should handle malformed URL scenarios', () => {
      mockRouter.url = '/invalid/url/structure';

      expect(() => component.ngAfterViewInit()).not.toThrow();
    });
  });

  describe('Mixpanel integration', () => {
    it('should call mixpanel service methods when available', () => {
      // Test that mixpanel methods are called appropriately
      expect(mixpanelService.addMixpanelEvents).toBeDefined();
      expect(mixpanelService.createUserProfile).toBeDefined();
    });
  });

  describe('Modal management', () => {
    it('should handle modal reference properly', () => {
      component.modalRef = mockBsModalRef as any;

      expect(component.modalRef).toBeDefined();
      expect(component.modalRef.hide).toBeDefined();
    });

    it('should handle bsModalRef properly', () => {
      component.bsModalRef = mockBsModalRef as any;

      expect(component.bsModalRef).toBeDefined();
      expect(component.bsModalRef.content).toBeDefined();
    });
  });
});
