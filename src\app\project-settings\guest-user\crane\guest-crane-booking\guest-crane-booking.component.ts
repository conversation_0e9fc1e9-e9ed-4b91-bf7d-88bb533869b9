import {
  Component, OnInit, TemplateRef, AfterViewInit, Input,
} from '@angular/core';
import {
  UntypedFormBuilder, UntypedFormGroup, Validators, UntypedFormArray, UntypedFormControl,
} from '@angular/forms';
import { Observable } from 'rxjs';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import moment from 'moment';
import { Router } from '@angular/router';
import { ProjectSharingService } from '../../../../services/projectSharingService/project-sharing.service';
import { DeliveryService } from '../../../../services/profile/delivery.service';
import { ProjectService } from '../../../../services/profile/project.service';
import { MixpanelService } from '../../../../services/mixpanel.service';
import {
  weekDays, recurrence, repeatWithSingleRecurrence, repeatWithMultipleRecurrence,
} from '../../../../services/common';

@Component({
  selector: 'app-guest-crane-booking',
  templateUrl: './guest-crane-booking.component.html',
})
export class GuestCraneBookingComponent implements OnInit, AfterViewInit {
  @Input() data: any;

  @Input() title: string;

  public craneRequest: UntypedFormGroup | null;

  public submitted = false;

  public formSubmitted = false;

  public modalLoader = false;

  public ProjectId: any;

  public ParentCompanyId: any;

  public newNdrCompanyDropdownSettings: IDropdownSettings;

  public companyList: any = [];

  public locationList: any = [];

  public selectedLocationId: any;

  public locationDropdownSettings: IDropdownSettings;

  public lastId: any;

  public newNdrDefinableDropdownSettings: IDropdownSettings;

  public NDRTimingChanged = false;

  public equipmentList: any = [];

  public definableFeatureOfWorkList: any = [];

  public deliveryEnd: Date;

  public deliveryStart: Date;

  public loader = false;

  public todayDate = new Date();

  public authUser: any = {};

  public editSubmitted = false;

  public responsibleCompanySelectedItems = [];

  public selectedRecurrence = 'Does Not Repeat';

  public recurrence = recurrence;

  public repeatWithSingleRecurrence = repeatWithSingleRecurrence;

  public repeatWithMultipleRecurrence = repeatWithMultipleRecurrence;

  public weekDays: any = weekDays;

  public isRepeatWithMultipleRecurrence = false;

  public isRepeatWithSingleRecurrence = false;

  public showRecurrenceTypeDropdown = false;

  public checkform: any = new UntypedFormArray([]);

  public message = '';

  public monthlyDate = '';

  public monthlyDayOfWeek = '';

  public monthlyLastDayOfWeek = '';

  public enableOption = false;

  public valueExists = [];

  public endTime: Date;

  public startTime: Date;

  public selectedValue;

  public timezoneList: [];

  public selectedTimeZoneValue: any;

  public dropdownSettings: IDropdownSettings;

  public timeZoneValues: any;

  public getSelectedTimeZone;

  public defaultValue;

  public defaultLocationValue;

  public recurrenceMinDate = new Date();

  public guestUserId: any;

  public equipmentDropdownSettings: IDropdownSettings;
  
  public noEquipmentOption = { id: 0, equipmentName: 'No Equipment Needed' };


  public constructor(
    private readonly formBuilder: UntypedFormBuilder,
    private modalRef1: BsModalRef,
    private readonly deliveryService: DeliveryService,
    private readonly modalService: BsModalService,
    public projectService: ProjectService,
    public projectSharingService: ProjectSharingService,
    private readonly toastr: ToastrService,
    public socket: Socket,
    public router: Router,
    private readonly mixpanelService: MixpanelService,
  ) {
    this.selectedValue = this.recurrence[0].value;
    this.ProjectId = +window.atob(localStorage.getItem('guestProjectId'));
    this.ParentCompanyId = +window.atob(localStorage.getItem('guestParentCompanyId'));
    this.guestUserId = +window.atob(localStorage.getItem('guestId'));
    if (this.ProjectId && this.ParentCompanyId) {
      this.getOverAllEquipmentInNewDelivery();
    }
    this.craneRequestCreationForm();
  }

  public chooseRepeatEveryType(value): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    if (value === 'Day' || value === 'Days') {
      this.craneRequest.get('recurrence').setValue('Daily');
    }
    if (value === 'Week' || value === 'Weeks') {
      this.craneRequest.get('recurrence').setValue('Weekly');
    }
    if (value === 'Month' || value === 'Months') {
      this.craneRequest.get('recurrence').setValue('Monthly');
    }
    if (value === 'Year' || value === 'Years') {
      this.craneRequest.get('recurrence').setValue('Yearly');
    }
    if (value === 'Day' || value === 'Days') {
      this.checkform = this.craneRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day: any): any => {
        const dayObj = day;
        dayObj.checked = true;
        dayObj.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj.value));
        return dayObj;
      });
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.showRecurrenceTypeDropdown = true;
    }
    if (value === 'Week' || value === 'Weeks') {
      this.weekDays = this.weekDays.map((day15: any): void => {
        const dayObj15 = day15;
        if (dayObj15.value === 'Monday') {
          dayObj15.checked = true;
        } else {
          dayObj15.checked = false;
        }
        dayObj15.isDisabled = false;
        return dayObj15;
      });
      this.checkform = this.craneRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (value === 'Day' || value === 'Week' || value === 'Month' || value === 'Year') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showRecurrenceTypeDropdown = false;
    }
    if (value === 'Days' || value === 'Weeks' || value === 'Months' || value === 'Years') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.showRecurrenceTypeDropdown = false;
    }
    if (this.craneRequest.get('repeatEveryCount').value > 1) {
      this.showRecurrenceTypeDropdown = true;
      this.isRepeatWithMultipleRecurrence = false;
    }
    this.selectedRecurrence = this.craneRequest.get('recurrence').value;
    this.occurMessage();
  }

  public occurMessage(): void {
    this.message = '';
    if (this.craneRequest.get('repeatEveryType').value === 'Day') {
      this.message = 'Occurs every day';
    }
    if (this.craneRequest.get('repeatEveryType').value === 'Days') {
      if (+this.craneRequest.get('repeatEveryCount').value === 2) {
        this.message = 'Occurs every other day';
      } else {
        this.message = `Occurs every ${this.craneRequest.get('repeatEveryCount').value} days`;
      }
    }
    if (this.craneRequest.get('repeatEveryType').value === 'Week') {
      let weekDays = '';
      this.weekDays.map((dayObj1: any): any => {
        if (dayObj1.checked) {
          weekDays = `${weekDays + dayObj1.value},`;
        }
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message += `Occurs every ${weekDays}`;
    }
    if (this.craneRequest.get('repeatEveryType').value === 'Weeks') {
      let weekDays = '';
      this.weekDays.map((dayObj2: any): any => {
        if (dayObj2.checked) {
          weekDays = `${weekDays + dayObj2.value},`;
        }
        return false;
      });
      if (+this.craneRequest.get('repeatEveryCount').value === 2) {
        this.message = `Occurs every other  ${weekDays}`;
      } else {
        this.message = `Occurs every ${
          this.craneRequest.get('repeatEveryCount').value
        } weeks on ${weekDays}`;
      }
      weekDays = weekDays.replace(/,\s*$/, '');
    }
    if (
      this.craneRequest.get('repeatEveryType').value === 'Month'
        || this.craneRequest.get('repeatEveryType').value === 'Months'
        || this.craneRequest.get('repeatEveryType').value === 'Year'
        || this.craneRequest.get('repeatEveryType').value === 'Years'
    ) {
      this.occurCraneMessage();
    }
    if (this.message) {
      this.message += ` until ${moment(this.craneRequest.get('endDate').value).format(
        'MMMM DD, YYYY',
      )}`;
    }
  }

  public occurCraneMessage() {
    if (this.craneRequest.get('chosenDateOfMonth').value === 1) {
      this.message = `Occurs on day ${this.monthlyDate}`;
    } else if (this.craneRequest.get('chosenDateOfMonth').value === 2) {
      this.message = `Occurs on the ${this.monthlyDayOfWeek}`;
    } else {
      this.message = `Occurs on the ${this.monthlyLastDayOfWeek}`;
    }
  }

  public changeRecurrenceCount(value): void {
    const count = +value;
    const recurrencedata = this.craneRequest.get('recurrence').value;

    if (count < 1) {
      this.craneRequest.get('repeatEveryCount').setValue(1);
      return;
    }

    const isSingle = count === 1;
    const isMultiple = count > 1;

    // Default flags
    this.isRepeatWithSingleRecurrence = false;
    this.isRepeatWithMultipleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;

    switch (recurrencedata) {
      case 'Daily':
        this.isRepeatWithSingleRecurrence = isSingle;
        this.showRecurrenceTypeDropdown = isMultiple;
        this.craneRequest.get('repeatEveryType').setValue(isSingle ? 'Day' : 'Days');
        break;

      case 'Weekly':
        this.isRepeatWithSingleRecurrence = isSingle;
        this.isRepeatWithMultipleRecurrence = isMultiple;
        this.craneRequest.get('repeatEveryType').setValue(isSingle ? 'Week' : 'Weeks');
        break;

      case 'Monthly':
        this.isRepeatWithMultipleRecurrence = isMultiple;
        this.isRepeatWithSingleRecurrence = isSingle;
        this.craneRequest.get('repeatEveryType').setValue(isSingle ? 'Month' : 'Months');
        this.changeMonthlyRecurrence();
        this.showMonthlyRecurrence();
        break;

      case 'Yearly':
        this.isRepeatWithMultipleRecurrence = isMultiple;
        this.isRepeatWithSingleRecurrence = isSingle;
        this.craneRequest.get('repeatEveryType').setValue(isSingle ? 'Year' : 'Years');
        break;

      default:
        break;
    }

    this.selectedRecurrence = recurrencedata;
    this.occurMessage();
  }


  public changeMonthlyRecurrence(): void {
    this.setMonthlyOrYearlyRecurrenceOption();
    this.updateFormValidation();
    this.showMonthlyRecurrence();
    this.occurMessage();
  }

  public setMonthlyOrYearlyRecurrenceOption(): void {
    if (this.craneRequest.get('chosenDateOfMonth').value === 1) {
      this.craneRequest
        .get('dateOfMonth')
        .setValue(moment(this.craneRequest.get('deliveryDate').value).format('DD'));
      this.craneRequest.get('monthlyRepeatType').setValue(null);
    } else if (this.craneRequest.get('chosenDateOfMonth').value === 2) {
      this.craneRequest.get('dateOfMonth').setValue(null);
      this.craneRequest.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
    } else if (this.craneRequest.get('chosenDateOfMonth').value === 3) {
      this.craneRequest.get('dateOfMonth').setValue(null);
      this.craneRequest.get('monthlyRepeatType').setValue(this.monthlyLastDayOfWeek);
    }
  }

  public showMonthlyRecurrence(): void {
    if (this.craneRequest.get('deliveryDate').value) {
      const startDate = moment(this.craneRequest.get('deliveryDate').value).format('YYYY-MM');
      const chosenDay = moment(this.craneRequest.get('deliveryDate').value).format('dddd');
      this.monthlyDate = moment(this.craneRequest.get('deliveryDate').value).format('DD');
      const day = moment(startDate, 'YYYY-MM').startOf('month').day(chosenDay);
      const getAllDays = [];
      if (day.date() > 7) day.add(7, 'd');
      const month = day.month();
      while (month === day.month()) {
        getAllDays.push(day.format('YYYY-MM-DD'));
        day.add(7, 'd');
      }
      let week;
      let extraOption;
      this.enableOption = false;
      getAllDays.forEach((element, i): void => {
        if (
          moment(this.craneRequest.get('deliveryDate').value).format('YYYY-MM-DD')
            === moment(element).format('YYYY-MM-DD')
        ) {
          const number = i + 1;
          if (number === 1) {
            week = 'First';
          }
          if (number === 2) {
            week = 'Second';
          }
          if (number === 3) {
            week = 'Third';
          }
          if (number === 4) {
            this.enableOption = true;
            extraOption = 'Last';
            week = 'Fourth';
          }
          if (number === 5) {
            week = 'Last';
          }
          if (number === 6) {
            week = 'Last';
          }
        }
      });
      this.monthlyDayOfWeek = `${week} ${chosenDay}`;
      this.monthlyLastDayOfWeek = `${extraOption} ${chosenDay}`;
      if (!this.enableOption && this.craneRequest.get('chosenDateOfMonth').value === 3) {
        this.craneRequest.get('chosenDateOfMonth').setValue(2);
        this.craneRequest.get('dateOfMonth').setValue(null);
        this.craneRequest.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
      }
      this.setMonthlyOrYearlyRecurrenceOption();
      this.occurMessage();
    }
  }

  public updateFormValidation(): void {
    const chosenDateOfMonth = this.craneRequest.get('chosenDateOfMonth');
    const dateOfMonth = this.craneRequest.get('dateOfMonth');
    const monthlyRepeatType = this.craneRequest.get('monthlyRepeatType');
    if (this.craneRequest.get('chosenDateOfMonth').value === 1) {
      dateOfMonth.setValidators([Validators.required]);
      monthlyRepeatType.clearValidators();
    } else {
      monthlyRepeatType.setValidators([Validators.required]);
      dateOfMonth.clearValidators();
    }
    chosenDateOfMonth.updateValueAndValidity();
    dateOfMonth.updateValueAndValidity();
    monthlyRepeatType.updateValueAndValidity();
  }

  public onChange(event): void {
    this.checkform = this.craneRequest.get('days') as UntypedFormArray;
    this.valueExists = this.checkform.controls.filter(
      (object): any => object.value === event.target.value,
    );
    if (event.target.checked) {
      this.checkform.push(new UntypedFormControl(event.target.value));
      this.weekDays = this.weekDays.map((day16: any): void => {
        const dayObj16 = day16;
        if (day16.value === event.target.value) {
          dayObj16.checked = true;
        }
        return dayObj16;
      });
      if (this.checkform.controls.length === 2) {
        this.weekDays = this.weekDays.map((day17: any): void => {
          const dayObj17 = day17;
          dayObj17.isDisabled = false;
          return dayObj17;
        });
      }
    } else if (this.selectedRecurrence === 'Weekly') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day18: any): void => {
            const dayObj18 = day18;
            if (dayObj18.value === event.target.value) {
              dayObj18.checked = false;
            }
            return dayObj18;
          });
        }
        if (this.checkform.controls.length === 1) {
          this.weekDays = this.weekDays.map((day19: any): void => {
            const dayObj19 = day19;
            if (dayObj19.value === this.checkform.controls[0].value) {
              dayObj19.isDisabled = true;
              dayObj19.checked = true;
            }
            return dayObj19;
          });
          return;
        }
        i += 1;
      });
    } else if (this.selectedRecurrence === 'Daily') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day): void => {
            const dayObj = day;
            if (dayObj.value === event.target.value) {
              dayObj.checked = false;
              dayObj.isDisabled = false;
            }
            return dayObj;
          });
          return;
        }
        i += 1;
      });
    }
    if (this.checkform.controls.length !== 7) {
      this.craneRequest.get('recurrence').setValue('Weekly');
      if (+this.craneRequest.get('repeatEveryCount').value === 1) {
        this.craneRequest.get('repeatEveryType').setValue('Week');
      } else {
        this.craneRequest.get('repeatEveryType').setValue('Weeks');
      }
      this.selectedRecurrence = this.craneRequest.get('recurrence').value;
    }
    if (this.checkform.controls.length === 7) {
      this.craneRequest.get('recurrence').setValue('Daily');
      if (+this.craneRequest.get('repeatEveryCount').value === 1) {
        this.craneRequest.get('repeatEveryType').setValue('Day');
      } else {
        this.craneRequest.get('repeatEveryType').setValue('Days');
      }
      this.selectedRecurrence = this.craneRequest.get('recurrence').value;
    }
    this.occurMessage();
  }


  public getReapeatEveryType(value) {
    if (value === 'Does Not Repeat') {
      this.craneRequest.get('repeatEveryType').setValue('');
    } else {
      this.craneRequest.get('repeatEveryCount').setValue(1);
    }
    if (value === 'Daily') {
      this.craneRequest.get('repeatEveryType').setValue('Day');
    }
    if (value === 'Weekly') {
      this.craneRequest.get('repeatEveryType').setValue('Week');
    }
    if (value === 'Monthly') {
      this.craneRequest.get('repeatEveryType').setValue('Month');
    }
    if (value === 'Yearly') {
      this.craneRequest.get('repeatEveryType').setValue('Year');
    }
  }

  public getRepeatEveryCount(value) {
    if (
      this.craneRequest.get('repeatEveryCount').value === 1
        && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.craneRequest.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showMonthlyRecurrence();
    }
    if (
      this.craneRequest.get('repeatEveryCount').value > 1
        && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.craneRequest.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.showMonthlyRecurrence();
    }
  }

  public onRecurrenceSelect(value): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    this.selectedRecurrence = value;
    this.getReapeatEveryType(value);
    if (this.craneRequest.get('repeatEveryCount').value > 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.checkform = this.craneRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day11: any): void => {
        const dayObj11 = day11;
        dayObj11.checked = true;
        dayObj11.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj11.value));
        return dayObj11;
      });
    }
    if (this.craneRequest.get('repeatEveryCount').value > 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.weekDays = this.weekDays.map((day12: any): void => {
        const dayObj12 = day12;
        if (dayObj12.value === 'Monday') {
          dayObj12.checked = true;
        } else {
          dayObj12.checked = false;
        }
        dayObj12.isDisabled = false;
        return dayObj12;
      });
      this.checkform = this.craneRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.craneRequest.get('repeatEveryCount').value === 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.weekDays = this.weekDays.map((day13: any): void => {
        const dayObj13 = day13;
        if (dayObj13.value === 'Monday') {
          dayObj13.checked = true;
        } else {
          dayObj13.checked = false;
        }
        dayObj13.isDisabled = false;
        return dayObj13;
      });
      this.checkform = this.craneRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.craneRequest.get('repeatEveryCount').value === 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.checkform = this.craneRequest.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day14: any): void => {
        const dayObj14 = day14;
        dayObj14.checked = true;
        dayObj14.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj14.value));
        return dayObj14;
      });
    }
    this.getRepeatEveryCount(value);
    this.occurMessage();
  }

  public setDefaultPerson(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      id: this.guestUserId,
    };
    this.projectSharingService.guestGetMemberRole(params).subscribe({
      next: (res): void => {
        if (res !== undefined && res !== null && res !== '') {
          this.authUser = res.data;
          let email: string;
          if (this.authUser.User.lastName != null) {
            email = `${this.authUser.User.firstName} ${this.authUser?.User?.lastName} (${this.authUser.User.email})`;
          } else {
            email = `${this.authUser.User.firstName} (${this.authUser.User.email})`;
          }
          const newMemberList = [
            {
              email,
              id: this.authUser.id,
              readonly: true,
            },
          ];
          this.craneRequest.get('responsiblePersons').patchValue(newMemberList);
        }
      },
      error: (error): void => {
        console.error('Error occurred:', error);
      }
    });
  }

  public ngAfterViewInit(): void {
    this.getLastCraneRequestId();
    this.setDefaultPerson();
    this.getTimeZoneList();
  }

  public getSelectedDate(): void {
    const getData = this.data;
    if (getData) {
      if (getData.date && getData.currentView === 'Month') {
        this.craneRequest
          .get('deliveryDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00'));
        this.setDefaultDateAndTime(this.craneRequest.get('deliveryDate').value);
      }
      if (
        (getData.date && getData.currentView === 'Week')
          || (getData.date && getData.currentView === 'Day')
      ) {
        this.craneRequest
          .get('deliveryDate')
          .setValue(moment(getData.date, 'YYYY-MM-DD').format('MM-DD-YYYY 00:00:00'));
        this.craneRequest
          .get('craneDeliveryStart')
          .setValue(moment(getData.date, 'YYYY-MM-DD hh:mm:ss').format());
        this.craneRequest
          .get('craneDeliveryEnd')
          .setValue(moment(getData.date, 'YYYY-MM-DD hh:mm:ss').add(30, 'minutes').format());
      }
    } else {
      this.setDefaultDateAndTime(null);
    }
  }

  public getLastCraneRequestId(): void {
    this.modalLoader = true;
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService.guestGetLastCraneRequestId(params).subscribe({
      next: (response): void => {
        this.lastId = response.lastId?.CraneRequestId;
        this.modalLoader = false;
      },
      error: (error): void => {
        console.error('Error occurred:', error);
        this.modalLoader = false;
      }
    });
  }

  public getOverAllEquipmentInNewDelivery(): void {
    const newNdrGetEquipmentsParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService.guestListCraneEquipment(newNdrGetEquipmentsParams, {
      showActivatedAlone: true,
    })
      .subscribe({
        next: (equipmentListResponseForNewNdr): void => {
          this.equipmentList = equipmentListResponseForNewNdr.data.rows;
          this.equipmentDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'equipmentName',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
          this.newNdrgetCompanies();
        },
        error: (error): void => {
          console.error('Error occurred:', error);
        }
      });
  }

  public newNdrgetCompanies(): void {
    const newNdrGetCompaniesParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService
      .guestGetCompanies(newNdrGetCompaniesParams)
      .subscribe({
        next: (companiesResponseForNewNdr: any): void => {
          if (companiesResponseForNewNdr) {
            this.companyList = companiesResponseForNewNdr.data;
            const loggedInUser = this.companyList.filter(
              (a: { id: any }): any => a.id === this.authUser.CompanyId,
            );
            this.responsibleCompanySelectedItems = loggedInUser;
            this.getDefinable();
            this.newNdrCompanyDropdownSettings = {
              singleSelection: false,
              idField: 'id',
              textField: 'companyName',
              selectAllText: 'Select All',
              unSelectAllText: 'UnSelect All',
              itemsShowLimit: 6,
              allowSearchFilter: true,
            };
          }
        },
        error: (error): void => {
          console.error('Error occurred:', error);
        }
      });
  }

  public onItemSelect(): any {}

  public getDefinable(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService.guestGetDefinableWork(param).subscribe({
      next: (response: any): void => {
        if (response) {
          const { data } = response;
          this.definableFeatureOfWorkList = data;
          this.newNdrDefinableDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'DFOW',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
          this.getLocations();
        }
      },
      error: (error): void => {
        console.error('Error occurred:', error);
      }
    });
  }

  public getLocations(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService.guestGetLocations(param).subscribe({
      next: (response: any): void => {
        if (response) {
          const { data } = response;
          this.locationList = data;
          this.locationDropdownSettings = {
            singleSelection: true,
            idField: 'id',
            textField: 'locationPath',
            allowSearchFilter: true,
            closeDropDownOnSelection: true,
          };
          this.setDefaultLocationPath();
        }
      },
      error: (error): void => {
        console.error('Error occurred:', error);
      }
    });
  }

  public setDefaultLocationPath(): void {
    if (this.locationList.length > 0) {
      const getChosenLocation: any = this.locationList.filter((obj: any): any => obj.isDefault === true);
      if (getChosenLocation) {
        this.craneRequest.get('LocationId').patchValue({
          id: getChosenLocation[0]?.id,
          locationPath: getChosenLocation[0]?.locationPath,
          isDefault: getChosenLocation[0]?.isDefault,
        });
        this.selectedLocationId = getChosenLocation[0]?.id;
        this.defaultLocationValue = getChosenLocation;
      }
    }
    this.closeModalPopup();
  }

  public closeModalPopup(): void {
    this.modalLoader = false;
  }

  public setDefaultDateAndTime(date): void {
    const setStartTime = 7;
    this.deliveryStart = new Date();
    this.deliveryEnd = new Date();
    if (date) {
      this.deliveryStart = new Date(date);
      this.deliveryEnd = new Date(date);
    }
    this.deliveryStart.setHours(setStartTime);
    this.deliveryStart.setMinutes(0);
    this.deliveryEnd.setHours(setStartTime + 1);
    this.deliveryEnd.setMinutes(0);
    this.craneRequest.get('craneDeliveryStart').setValue(this.deliveryStart);
    this.craneRequest.get('craneDeliveryEnd').setValue(this.deliveryEnd);
  }

  public ngOnInit(): void {
    this.getSelectedDate();
  }

  public requestAutocompleteItems = (text: string): Observable<any> => {
    const param = {
      ProjectId: this.ProjectId,
      search: text,
      ParentCompanyId: this.ParentCompanyId,
    };
    return this.projectSharingService.guestSearchNewMember(param);
  };

  public numberOnly(event: { which: any; keyCode: any; ctrlKey?: boolean }): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    // Allow: backspace, delete, tab, escape, enter, decimal point
    if ([8, 9, 27, 13, 46].indexOf(charCode) !== -1 ||
        // Allow: Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X
        (charCode === 65 && event.ctrlKey === true) ||
        (charCode === 67 && event.ctrlKey === true) ||
        (charCode === 86 && event.ctrlKey === true) ||
        (charCode === 88 && event.ctrlKey === true)) {
      return true;
    }
    // Ensure that it is a number and stop the keypress
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public deliveryEndTimeChangeDetection(): void {
    this.NDRTimingChanged = true;
  }

  public changeDate(event: any): void {
    if (!this.modalLoader) {
      const startTime = new Date(event).getHours();
      const minutes = new Date(event).getMinutes();
      this.deliveryEnd = new Date();
      this.deliveryEnd.setHours(startTime + 1);
      this.deliveryEnd.setMinutes(minutes);
      this.craneRequest.get('craneDeliveryEnd').setValue(this.deliveryEnd);
      this.NDRTimingChanged = true;
    }
  }

    public checkEquipmentType(value: any[]): void {
      if(value) {
        if(value.length == this.equipmentList.length -1 && this.equipmentList[0].id == 0) {
          this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        }
        if(value.length != this.equipmentList.length && this.equipmentList[0].id != 0) {
          this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
          this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
        }
        if(value.length == 0) {
          this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
          this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
        }
        let hasNoEquipmentOption = false;
        let hasOtherEquipment = false;
        // Check if "No Equipment Needed" (id = 0) is selected
        hasNoEquipmentOption = value.some((item: any) => item.id === 0);

        // Check if other equipment is selected
        hasOtherEquipment = value.some((item: any) => item.id !== 0);

        const previousSelection = this.craneRequest.get('EquipmentId').value || [];
        const previousHasOther = previousSelection.some((item: any) => item.id !== 0);

        // Rule 1: If "No Equipment Needed" is selected and other items are selected, keep only "No Equipment Needed"
        if (hasNoEquipmentOption && hasOtherEquipment && !previousHasOther) {
          this.toastr.warning('When "No Equipment Needed" is selected, other equipment options cannot be selected.', 'Warning');
          const noEquipmentOnly = value.filter((item: any) => item.id === 0);
          this.craneRequest.get('EquipmentId').setValue(noEquipmentOnly);
          value = noEquipmentOnly;
          hasOtherEquipment = false;
        }

        // Rule 2: If other equipment is already selected and "No Equipment Needed" is now selected, remove it
        if (previousHasOther && hasNoEquipmentOption) {
          this.toastr.warning('When other equipment is selected, "No Equipment Needed" cannot be selected.', 'Warning');
          const filteredSelection = value.filter((item: any) => item.id !== 0);
          this.craneRequest.get('EquipmentId').setValue(filteredSelection);
          value = filteredSelection;
          hasNoEquipmentOption = false;
        }
      }

    }
  

  public close(template: TemplateRef<any>): void {
    if (
      (this.craneRequest.touched && this.craneRequest.dirty)
        || (this.craneRequest.get('definableFeatureOfWorks').dirty
          && this.craneRequest.get('definableFeatureOfWorks').value
          && this.craneRequest.get('definableFeatureOfWorks').value.length > 0)
        || (this.craneRequest.get('companies').dirty
          && this.craneRequest.get('companies').value
          && this.craneRequest.get('companies').value.length > 0)
        || (this.craneRequest.get('EquipmentId').dirty
          && this.craneRequest.get('EquipmentId').value
          && this.craneRequest.get('EquipmentId').value.length > 0)
        || this.NDRTimingChanged
    ) {
      this.openConfirmationModalPopup(template);
    } else {
      this.resetForm('yes');
    }
  }

  public openConfirmationModalPopup(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.craneRequest.reset();
      this.setDefaultPerson();
      this.submitted = false;
      this.formSubmitted = false;
      this.editSubmitted = false;
      this.NDRTimingChanged = false;
      this.router.navigate([window.atob(localStorage.getItem('url'))]);
    }
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    const companies = [];
    const persons = [];
    const define = [];
    const equipments = [];
    if (this.craneRequest.invalid) {
      this.formSubmitted = false;
      return;
    }
    const formValue = this.craneRequest.value;
    if (formValue.EquipmentId.length <= 0) {
      this.toastr.error('Equipment is required');
      this.formSubmitted = false;
      return;
    }
    const newTimeZoneDetails = formValue.TimeZoneId;
    const deliveryDate = new Date(formValue.deliveryDate);
    const EndDate = new Date(formValue.endDate);
    const startHours = new Date(formValue.craneDeliveryStart).getHours();
    const startMinutes = new Date(formValue.craneDeliveryStart).getMinutes();
    const deliveryStart = this.convertStart(deliveryDate, startHours, startMinutes);
    const endHours = new Date(formValue.craneDeliveryEnd).getHours();
    const endMinutes = new Date(formValue.craneDeliveryEnd).getMinutes();
    const startPicker = moment(formValue.craneDeliveryStart).format('HH:mm');
    const endPicker = moment(formValue.craneDeliveryEnd).format('HH:mm');
    const weekStartDate = moment(formValue.deliveryDate).format('YYYY MM DD 00:00:00');
    const weekEndDate = formValue.recurrence !== 'Does Not Repeat'
      ? moment(formValue.endDate).format('YYYY MM DD 00:00:00')
      : moment(formValue.deliveryDate).format('YYYY MM DD 00:00:00');
    const deliveryEnd = formValue.recurrence !== 'Does Not Repeat'
      ? this.convertStart(EndDate, endHours, endMinutes)
      : this.convertStart(deliveryDate, endHours, endMinutes);

    this.createDelivery({
      newNdrFormValue: formValue,
      deliveryStart,
      deliveryEnd,
      companies,
      responsbilePersonsData: persons,
      definableFeatureOfWorkData: define,
      newTimeZoneDetails,
      startPicker,
      endPicker,
      weekStartDate,
      weekEndDate,
      equipments,
    }

    );
  }

  public convertStart(deliveryDate: Date, startHours: number, startMinutes: number): string {
    const fullYear = deliveryDate.getFullYear();
    const fullMonth = deliveryDate.getMonth();
    const date = deliveryDate.getDate();
    const deliveryNewStart = new Date(fullYear, fullMonth, date, startHours, startMinutes);
    const deliveryStart = deliveryNewStart.toUTCString();
    return deliveryStart;
  }

  public checkNewDeliveryStartEnd(
    newDeliveryStart: string | number | Date,
    newDeliveryEnd: string | number | Date,
  ): boolean {
    const newStartDate = new Date(newDeliveryStart).getTime();
    const newEndDate = new Date(newDeliveryEnd).getTime();
    if (newStartDate < newEndDate) {
      return true;
    }
    return false;
  }

  public constructPayload({
    newNdrFormValue,
    companies,
    equipments,
    deliveryStart,
    deliveryEnd,
    responsbilePersonsData,
    definableFeatureOfWorkData,
    startPicker,
    endPicker,
  }) {
    const escortCondition = newNdrFormValue.isEscortNeeded === null
                  || newNdrFormValue.isEscortNeeded === undefined;
    const payload = {
      description: newNdrFormValue.description,
      companies,
      isEscortNeeded: escortCondition ? false : newNdrFormValue.isEscortNeeded,
      ProjectId: this.ProjectId,
      userId: this.guestUserId,
      additionalNotes: newNdrFormValue.additionalNotes,
      EquipmentId: equipments,
      LocationId: this.selectedLocationId,
      craneDeliveryStart: deliveryStart,
      craneDeliveryEnd: deliveryEnd,
      ParentCompanyId: this.ParentCompanyId,
      responsiblePersons: responsbilePersonsData,
      definableFeatureOfWorks: definableFeatureOfWorkData,
      isAssociatedWithDeliveryRequest: false,
      pickUpLocation: newNdrFormValue.pickUpLocation,
      dropOffLocation: newNdrFormValue.dropOffLocation,
      recurrence: newNdrFormValue.recurrence,
      chosenDateOfMonth: newNdrFormValue.chosenDateOfMonth === 1,
      dateOfMonth: newNdrFormValue.dateOfMonth,
      monthlyRepeatType: newNdrFormValue.monthlyRepeatType,
      days: newNdrFormValue.days ? this.sortWeekDays(newNdrFormValue.days) : [],
      repeatEveryType: newNdrFormValue.repeatEveryType
        ? newNdrFormValue.repeatEveryType
        : null,
      repeatEveryCount: newNdrFormValue.repeatEveryCount
        ? newNdrFormValue.repeatEveryCount.toString()
        : null,
      TimeZoneId: this.timeZoneValues,
      startPicker,
      endPicker,
    };
    return payload;
  }

  public createDelivery(params: {
    newNdrFormValue: any;
    deliveryStart: string;
    deliveryEnd: string;
    companies: any[];
    responsbilePersonsData: any[];
    definableFeatureOfWorkData: any[];
    newTimeZoneDetails: any;
    startPicker: any;
    endPicker: any;
    weekStartDate: any;
    weekEndDate: any;
    equipments: any[];
  }): void {
    const success = this.prepareDeliveryData(params);
    if (!success) return;
    const {
      newNdrFormValue,
      companies,
      responsbilePersonsData,
      definableFeatureOfWorkData,
      deliveryStart,
      deliveryEnd,
      startPicker,
      endPicker,
      weekStartDate,
      weekEndDate,
      equipments,
    } = params;
    if (!this.checkStringEmptyValues(newNdrFormValue)) {
      const payload = this.constructPayload({
        newNdrFormValue,
        companies,
        equipments,
        deliveryStart,
        deliveryEnd,
        responsbilePersonsData,
        definableFeatureOfWorkData,
        startPicker,
        endPicker,
      });

      if (payload.recurrence === 'Monthly' || payload.recurrence === 'Yearly') {
        payload.dateOfMonth = this.monthlyDate;
      }

      if (payload.recurrence) {
        payload.startPicker = startPicker;
        payload.endPicker = endPicker;
        payload.craneDeliveryStart = weekStartDate;
        payload.craneDeliveryEnd = weekEndDate;

        if (!this.validateRecurrencePayload(payload)) {
          return;
        }
      }
      this.createNDR(payload);
    } else {
      this.formReset();
    }
  }

  public validateRecurrencePayload(payload) {
    if (payload.startPicker === payload.endPicker) {
      this.showErrorMessage('Delivery Start time and End time should not be the same');
      return false;
    }

    if (payload.startPicker > payload.endPicker) {
      this.showErrorMessage('Please enter From Time lesser than To Time');
      return false;
    }

    if (
      payload.recurrence !== 'Does Not Repeat'
      && new Date(payload.craneDeliveryEnd) <= new Date(payload.craneDeliveryStart)
    ) {
      this.showErrorMessage('Please enter End Date greater than Start Date');
      return false;
    }

    return true;
  }

  public showErrorMessage(message: string) {
    this.toastr.error(message);
    this.formSubmitted = false;
    this.submitted = false;
  }

  public prepareDeliveryData(params: {
    newNdrFormValue: any;
    companies: any[];
    responsbilePersonsData: any[];
    definableFeatureOfWorkData: any[];
    newTimeZoneDetails: any;
    equipments: any[];
  }): boolean {
    const {
      newNdrFormValue,
      companies,
      responsbilePersonsData,
      definableFeatureOfWorkData,
      newTimeZoneDetails,
      equipments,
    } = params;

    const newNdrCompanyDetails = newNdrFormValue.companies;
    const newNdrPersonDetails = newNdrFormValue.responsiblePersons;
    const newNdrDefinableDetails = newNdrFormValue.definableFeatureOfWorks;

    if (!newNdrCompanyDetails || newNdrCompanyDetails.length === 0) {
      this.formReset();
      this.toastr.error('Responsible Company is required');
      return false;
    }

    if (!newNdrPersonDetails || newNdrPersonDetails.length === 0) {
      this.formReset();
      this.toastr.error('Responsible Person is required');
      return false;
    }

    newNdrCompanyDetails.forEach((element: { id: any }) => companies.push(element.id));
    newNdrPersonDetails.forEach((element: { id: any }) => responsbilePersonsData.push(element.id));

    if (newNdrDefinableDetails?.length > 0) {
      newNdrDefinableDetails.forEach(
        (element: { id: any }) => definableFeatureOfWorkData.push(element.id),
      );
    }

    if (newTimeZoneDetails?.length > 0 && newNdrFormValue.TimeZoneId?.length > 0) {
      newNdrFormValue.TimeZoneId.forEach((element: { id: any }) => {
        this.timeZoneValues = element.id;
      });
    }

    if (newNdrFormValue.EquipmentId?.length > 0) {
      newNdrFormValue.EquipmentId.forEach((element: { id: any }) => {
        equipments.push(element.id);
      });
    }

    return true;
  }


  public checkStringEmptyValues(formValue: { description: string; notes: string }): boolean {
    if (formValue.description.trim() === '') {
      this.toastr.error('Please Enter valid Company Name.', 'OOPS!');
      return true;
    }
    if (formValue.notes) {
      if (formValue.notes.trim() === '') {
        this.toastr.error('Please Enter valid address.', 'OOPS!');
        return true;
      }
    }
    return false;
  }

  public sortWeekDays(data: any[]): any {
    const order = {
      Sunday: 1,
      Monday: 2,
      Tuesday: 3,
      Wednesday: 4,
      Thursday: 5,
      Friday: 6,
      Saturday: 7,
    };

    if (data.length > 0) {
      return data.sort((a, b): any => order[a] - order[b]);
    }
  }

  public createNDR(payload: {
    description: any;
    companies: any;
    isEscortNeeded: any;
    ProjectId: any;
    userId: any;
    additionalNotes: any;
    EquipmentId: any;
    LocationId: any;
    craneDeliveryStart: any;
    craneDeliveryEnd: any;
    ParentCompanyId: any;
    responsiblePersons: any;
    definableFeatureOfWorks: any;
    isAssociatedWithDeliveryRequest: boolean;
    pickUpLocation: any;
    dropOffLocation: any;
    recurrence: any;
    chosenDateOfMonth: any;
    dateOfMonth: any;
    monthlyRepeatType: any;
    days: any;
    repeatEveryType: any;
    repeatEveryCount: any;
  }): void {
    this.projectSharingService.guestCreateCraneRequest(payload).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.mixpanelService.addGuestUserMixpanelEvents('Guest Created New Crane Booking');
          this.formReset();
          this.craneRequest.reset();
          this.resetForm('yes');
          this.NDRTimingChanged = false;
          this.isRequestToMember();
        }
      },
      error: (NDRCreateHistoryError): void => {
        this.formReset();
        this.NDRTimingChanged = false;
        if (NDRCreateHistoryError.message?.statusCode === 400) {
          this.showError(NDRCreateHistoryError);
        } else if (!NDRCreateHistoryError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else if (NDRCreateHistoryError.message.includes('overlaps')) {
          this.toastr.error(NDRCreateHistoryError.message, 'Alert!');
        } else {
          this.toastr.error(NDRCreateHistoryError.message, 'OOPS!');
        }
      },
    });
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public formReset(): void {
    this.formSubmitted = false;
    this.submitted = false;
  }

  public craneRequestCreationForm(): void {
    this.craneRequest = this.formBuilder.group({
      EquipmentId: [this.formBuilder.array([])],
      LocationId: ['', Validators.compose([Validators.required])],
      additionalNotes: [''],
      responsiblePersons: ['', Validators.compose([Validators.required])],
      description: ['', Validators.compose([Validators.required])],
      deliveryDate: ['', Validators.compose([Validators.required])],
      craneDeliveryStart: ['', Validators.compose([Validators.required])],
      craneDeliveryEnd: ['', Validators.compose([Validators.required])],
      isEscortNeeded: [false],
      companies: [this.formBuilder.array([])],
      definableFeatureOfWorks: [this.formBuilder.array([])],
      pickUpLocation: ['', Validators.compose([Validators.required])],
      dropOffLocation: ['', Validators.compose([Validators.required])],
      isAssociatedWithDeliveryRequest: [false, Validators.compose([Validators.required])],
      recurrence: [''],
      repeatEveryCount: [''],
      repeatEveryType: [''],
      days: new UntypedFormArray([]),
      chosenDateOfMonth: [false, ''],
      dateOfMonth: [''],
      monthlyRepeatType: [''],
      endDate: [''],
      TimeZoneId: [this.formBuilder.array([]), Validators.compose([Validators.required])],
    });

    this.craneRequest.get('isEscortNeeded').setValue(false);
    const newDate = moment().format('MM/DD/YYYY');
    this.craneRequest.get('deliveryDate').setValue(newDate);
    this.craneRequest.get('craneDeliveryStart').setValue(this.deliveryStart);
    this.craneRequest.get('craneDeliveryEnd').setValue(this.deliveryEnd);
    this.craneRequest.get('recurrence').setValue(this.selectedValue);
    this.formControlValueChanged();
    this.setCurrentTiming();
    if (this.selectedValue === 'Does Not Repeat') {
      this.craneRequest.get('repeatEveryType').setValue('');
    } else {
      this.craneRequest.get('repeatEveryCount').setValue(1);
    }
  }

  public formControlValueChanged(): void {
    this.craneRequest.get('repeatEveryType').valueChanges.subscribe((value: string): void => {
      const days = this.craneRequest.get('days');
      const chosenDateOfMonth = this.craneRequest.get('chosenDateOfMonth');
      const dateOfMonth = this.craneRequest.get('dateOfMonth');
      const monthlyRepeatType = this.craneRequest.get('monthlyRepeatType');
      if (value === 'Week' || value === 'Day' || value === 'Weeks') {
        days.setValidators([Validators.required]);
      } else {
        days.clearValidators();
      }
      if (value === 'Month' || value === 'Months' || value === 'Year' || value === 'Years') {
        if (this.craneRequest.get('chosenDateOfMonth').value === 1) {
          dateOfMonth.setValidators([Validators.required]);
          monthlyRepeatType.clearValidators();
        } else {
          monthlyRepeatType.setValidators([Validators.required]);
          dateOfMonth.clearValidators();
        }
      } else {
        chosenDateOfMonth.clearValidators();
        dateOfMonth.clearValidators();
        monthlyRepeatType.clearValidators();
      }
      chosenDateOfMonth.updateValueAndValidity();
      dateOfMonth.updateValueAndValidity();
      monthlyRepeatType.updateValueAndValidity();
      days.updateValueAndValidity();
    });
  }

  public setCurrentTiming(): void {
    const newDate = moment().format('MM-DD-YYYY');
    const hours = moment(new Date()).format('HH');
    this.startTime = new Date();
    this.startTime.setHours(+hours);
    this.startTime.setMinutes(0);
    this.endTime = new Date();
    this.endTime.setHours(+hours);
    this.endTime.setMinutes(30);
    if (!this.craneRequest.get('endDate')?.value) {
      this.craneRequest.get('endDate').setValue(newDate);
    }
    this.changeMonthlyRecurrence();
  }

  public timeZoneSelected(id): void {
    this.selectedTimeZoneValue = this.timezoneList.find((obj: any): any => +obj.id === +id);
  }

  public getTimeZoneList(): void {
    this.projectSharingService.guestGetTimeZoneList().subscribe({
      next: (response: any): void => {
        this.loader = true;
        if (response) {
          const params = {
            ProjectId: this.ProjectId,
          };
          if (params.ProjectId) {
            this.projectSharingService.guestGetSingleProject(params).subscribe((projectList: any): void => {
              if (projectList) {
                this.timezoneList = response.data;
                this.dropdownSettings = {
                  singleSelection: true,
                  idField: 'id',
                  textField: 'location',
                  allowSearchFilter: true,
                  closeDropDownOnSelection: true,
                };
                this.getSelectedTimeZone = this.timezoneList.filter(
                  (obj: any): any => +obj.id === +projectList.data.TimeZoneId,
                );
                this.defaultValue = this.getSelectedTimeZone;
                this.loader = false;
              }
            });
          }
        }
      },
      error: (getTimeZoneListErr): void => {
        if (getTimeZoneListErr.message?.statusCode === 400) {
          this.showError(getTimeZoneListErr);
        } else if (!getTimeZoneListErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(getTimeZoneListErr.message, 'OOPS!');
        }
      },
    });
  }

  public locationSelected(data): void {
    const getChosenLocation = this.locationList.find((obj: any): any => +obj.id === +data.id);
    this.selectedLocationId = getChosenLocation?.id;
  }

  public isRequestToMember() {
    const payload = {
      userId: this.guestUserId,
      ProjectId: this.ProjectId,
    };
    this.projectSharingService.isRequestToMember(payload).subscribe({
      next: (response: any): void => {
        if (!response?.data.isRequestedToBeAMember && response?.data.status !== 'declined') {
          this.router.navigate(['/submit-book']);
        } else {
          this.router.navigate([window.atob(localStorage.getItem('url'))]);
        }
      },
      error: (error: any) => {
        console.error('Error occurred:', error);
        this.toastr.error('Try again later.!', 'Something went wrong.');
      },
    });
  }
}
