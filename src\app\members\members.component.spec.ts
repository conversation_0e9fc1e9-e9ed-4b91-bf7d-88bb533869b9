import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { ModalModule, BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { TagInputModule } from 'ngx-chips';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { NgxMaskModule } from 'ngx-mask';
import { NgxPaginationModule } from 'ngx-pagination';
import { Router } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { AnimationBuilder } from '@angular/animations';
import { of, throwError, BehaviorSubject } from 'rxjs';
import { MembersComponent } from './members.component';
import { ProjectService } from '../services/profile/project.service';
import { ProfileService } from '../services/profile/profile.service';
import { UserService } from '../services/user.service';
import { AuthService } from '../services/auth/auth.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { MixpanelService } from '../services/mixpanel.service';

describe('MembersComponent', () => {
  let component: MembersComponent;
  let fixture: ComponentFixture<MembersComponent>;
  let projectService: jest.Mocked<ProjectService>;
  let profileService: jest.Mocked<ProfileService>;
  let userService: jest.Mocked<UserService>;
  let authService: jest.Mocked<AuthService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let mixpanelService: jest.Mocked<MixpanelService>;
  let toastrService: jest.Mocked<ToastrService>;
  let modalService: jest.Mocked<BsModalService>;
  let router: jest.Mocked<Router>;
  let titleService: jest.Mocked<Title>;

  const mockProjectParent = new BehaviorSubject({ ProjectId: 123, ParentCompanyId: 456 });
  const mockUserData = new BehaviorSubject({ RoleId: 1, firstName: 'Test', lastName: 'User' });
  const mockLoginUser = new BehaviorSubject({ id: 1, email: '<EMAIL>' });

  beforeEach(async () => {
    const projectServiceMock = {
      projectParent: mockProjectParent,
      userData: mockUserData,
      listCompany: jest.fn().mockReturnValue(of({
        data: {
          rows: [
            { id: 1, companyName: 'Test Company 1' },
            { id: 2, companyName: 'Test Company 2' }
          ]
        },
        parentCompany: [{ id: 3, companyName: 'Parent Company' }]
      })),
      listMember: jest.fn().mockReturnValue(of({
        data: {
          rows: [
            {
              id: 1,
              User: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
              RoleId: 2,
              CompanyId: 1,
              phoneCode: '+1',
              phoneNumber: '1234567890',
              isChecked: false
            }
          ],
          count: 1
        },
        lastId: 1
      })),
      getRoles: jest.fn().mockReturnValue(of({
        data: [
          { id: 1, roleName: 'Admin' },
          { id: 2, roleName: 'User' }
        ]
      })),
      inviteMembers: jest.fn().mockReturnValue(of({ message: 'Members invited successfully' })),
      editMember: jest.fn().mockReturnValue(of({ message: 'Member updated successfully' })),
      deleteMember: jest.fn().mockReturnValue(of({ message: 'Member deleted successfully' })),
      activateMember: jest.fn().mockReturnValue(of({ message: 'Member activated successfully' })),
      deactivateMember: jest.fn().mockReturnValue(of({ message: 'Member deactivated successfully' })),
      getMemberMappedRequests: jest.fn().mockReturnValue(of({
        data: {
          mappedRequest: [],
          members: []
        }
      })),
      resendInviteLink: jest.fn().mockReturnValue(of({ message: 'Invite link sent successfully' })),
      getOnboardingInviteLink: jest.fn().mockReturnValue(of({
        data: { link: 'https://example.com/invite/123' }
      })),
      searchAllMember: jest.fn().mockReturnValue(of([]))
    };

    const profileServiceMock = {
      getOverView: jest.fn().mockReturnValue(of({
        data: { id: 1, name: 'Test Profile' }
      })),
      updatedOverView: jest.fn()
    };

    const userServiceMock = {
      checkUser: jest.fn().mockReturnValue(of({
        existUser: { profilePic: 'test-pic.jpg' },
        isMemberExists: false,
        isRestrictedEmail: false
      }))
    };

    const authServiceMock = {
      checkDialCode: jest.fn().mockReturnValue('00000-00000')
    };

    const deliveryServiceMock = {
      loginUser: mockLoginUser
    };

    const mixpanelServiceMock = {
      addMixpanelEvents: jest.fn()
    };

    const toastrServiceMock = {
      success: jest.fn(),
      error: jest.fn(),
      clear: jest.fn()
    };

    const modalServiceMock = {
      show: jest.fn().mockReturnValue({
        hide: jest.fn()
      })
    };

    const routerMock = {
      navigate: jest.fn()
    };

    const titleServiceMock = {
      setTitle: jest.fn()
    };

    const animationBuilderMock = {
      build: jest.fn().mockReturnValue({
        create: jest.fn().mockReturnValue({
          play: jest.fn(),
          pause: jest.fn(),
          finish: jest.fn(),
          destroy: jest.fn(),
          reset: jest.fn()
        })
      })
    };

    await TestBed.configureTestingModule({
      declarations: [MembersComponent],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        RouterTestingModule.withRoutes([]),
        HttpClientTestingModule,
        ModalModule.forRoot(),
        TagInputModule,
        ToastrModule.forRoot(),
        BsDropdownModule.forRoot(),
        NgxMaskModule.forRoot(),
        NgxPaginationModule,
      ],
      providers: [
        BsModalRef,
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: ProfileService, useValue: profileServiceMock },
        { provide: UserService, useValue: userServiceMock },
        { provide: AuthService, useValue: authServiceMock },
        { provide: DeliveryService, useValue: deliveryServiceMock },
        { provide: MixpanelService, useValue: mixpanelServiceMock },
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: BsModalService, useValue: modalServiceMock },
        { provide: Router, useValue: routerMock },
        { provide: Title, useValue: titleServiceMock },
        { provide: AnimationBuilder, useValue: animationBuilderMock }
      ],
    }).compileComponents();

    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    profileService = TestBed.inject(ProfileService) as jest.Mocked<ProfileService>;
    userService = TestBed.inject(UserService) as jest.Mocked<UserService>;
    authService = TestBed.inject(AuthService) as jest.Mocked<AuthService>;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    mixpanelService = TestBed.inject(MixpanelService) as jest.Mocked<MixpanelService>;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    modalService = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    router = TestBed.inject(Router) as jest.Mocked<Router>;
    titleService = TestBed.inject(Title) as jest.Mocked<Title>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(MembersComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.currentPageNo).toBe(1);
      expect(component.pageSize).toBe(25);
      expect(component.pageNo).toBe(1);
      expect(component.loader).toBe(false); // Set to false after service calls
      expect(component.selectAll).toBe(false);
      expect(component.memberList.length).toBe(1); // From mock data
      expect(component.roleList).toBeDefined();
      expect(component.companyList).toBeDefined();
      expect(component.inviteMemberList).toEqual([]);
      expect(component.deleteIndex).toEqual([]);
      expect(component.phoneMask).toBe('00000-00000');
      expect(component.search).toBe('');
      expect(component.filterCount).toBe(0);
      expect(component.isAdmin).toBe(true); // Set to true from mock userData with RoleId 1
    });

    it('should set page title on initialization', () => {
      expect(titleService.setTitle).toHaveBeenCalledWith('Follo - Members');
    });

    it('should initialize forms on construction', () => {
      expect(component.inviteDetailsForm).toBeDefined();
      expect(component.memberEditForm).toBeDefined();
      expect(component.filterForm).toBeDefined();
    });

    it('should call required services on initialization', () => {
      expect(projectService.getRoles).toHaveBeenCalled();
    });

    it('should handle projectParent subscription', () => {
      const mockData = { ProjectId: 789, ParentCompanyId: 101 };
      mockProjectParent.next(mockData);

      expect(component.ProjectId).toBe(789);
      expect(component.ParentCompanyId).toBe(101);
    });

    it('should handle userData subscription and set admin status', () => {
      const adminUser = { RoleId: 1, firstName: 'Admin', lastName: 'User' };
      mockUserData.next(adminUser);

      expect(component.userData).toEqual(adminUser);
      expect(component.isAdmin).toBe(true);
    });

    it('should handle userData subscription for non-admin user', () => {
      const regularUser = { RoleId: 4, firstName: 'Regular', lastName: 'User' };

      // Reset isAdmin before testing
      component.isAdmin = false;
      mockUserData.next(regularUser);

      expect(component.userData).toEqual(regularUser);
      expect(component.isAdmin).toBe(false);
    });

    it('should handle loginUser subscription', () => {
      const loginUser = { id: 2, email: '<EMAIL>' };
      mockLoginUser.next(loginUser);

      expect(component.authUser).toEqual(loginUser);
    });

    it('should initialize country codes', () => {
      component.getCountryCode();
      expect(component.countryCode).toBeDefined();
      expect(component.countryCode.length).toBeGreaterThan(0);
    });
  });

  describe('Member Management', () => {
    beforeEach(() => {
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
    });

    it('should get members list', () => {
      component.getMembers();

      expect(projectService.listMember).toHaveBeenCalledWith(
        {
          ProjectId: 123,
          pageSize: 25,
          pageNo: 1,
          ParentCompanyId: 456
        },
        expect.any(Object)
      );
      expect(component.loader).toBe(false);
      expect(component.memberList.length).toBe(1);
      expect(component.totalCount).toBe(1);
    });

    it('should get companies list', () => {
      component.getCompanies();

      expect(projectService.listCompany).toHaveBeenCalledWith(
        {
          ProjectId: 123,
          pageSize: 25,
          pageNo: 1,
          ParentCompanyId: 456
        },
        { inviteMember: true }
      );
      expect(component.companyList.length).toBeGreaterThan(0);
    });

    it('should get roles list', () => {
      component.getRoles();

      expect(projectService.getRoles).toHaveBeenCalled();
      expect(component.roleList).toBeDefined();
    });

    it('should change page size and refresh members', () => {
      const newPageSize = 50;
      component.changePageSize(newPageSize);

      expect(component.pageSize).toBe(newPageSize);
      expect(projectService.listMember).toHaveBeenCalled();
    });

    it('should change page number and refresh members', () => {
      const newPageNo = 2;
      component.changePageNo(newPageNo);

      expect(component.currentPageNo).toBe(newPageNo);
      expect(projectService.listMember).toHaveBeenCalled();
    });

    it('should handle member search', () => {
      const searchTerm = 'john';
      component.getSearchMember(searchTerm);

      expect(component.search).toBe(searchTerm);
      expect(component.showSearchbar).toBe(true);
      expect(component.pageNo).toBe(1);
      expect(projectService.listMember).toHaveBeenCalled();
    });

    it('should clear search', () => {
      component.search = 'test';
      component.showSearchbar = true;
      component.clear();

      expect(component.search).toBe('');
      expect(component.showSearchbar).toBe(false);
      expect(component.pageNo).toBe(1);
      expect(projectService.listMember).toHaveBeenCalled();
    });

    it('should sort by field', () => {
      const fieldName = 'firstName';
      const sortType = 'ASC';
      component.sortByField(fieldName, sortType);

      expect(component.sortColumn).toBe(fieldName);
      expect(component.sort).toBe(sortType);
      expect(projectService.listMember).toHaveBeenCalled();
    });
  });

  describe('Form Operations', () => {
    it('should initialize invite form with required validators', () => {
      expect(component.inviteDetailsForm.get('RoleId').hasError('required')).toBe(true);
      expect(component.inviteDetailsForm.get('CompanyId').hasError('required')).toBe(true);
      expect(component.inviteDetailsForm.get('email').hasError('required')).toBe(true);
    });

    it('should initialize member edit form with required validators', () => {
      expect(component.memberEditForm.get('RoleId').hasError('required')).toBe(true);
      expect(component.memberEditForm.get('CompanyId').hasError('required')).toBe(true);
      expect(component.memberEditForm.get('email').hasError('required')).toBe(true);
      expect(component.memberEditForm.get('firstName').hasError('required')).toBe(true);
      expect(component.memberEditForm.get('lastName').hasError('required')).toBe(true);
      expect(component.memberEditForm.get('phoneNumber').hasError('required')).toBe(true);
      expect(component.memberEditForm.get('phoneCode').hasError('required')).toBe(true);
    });

    it('should validate email format in edit form', () => {
      const emailControl = component.memberEditForm.get('email');
      emailControl.setValue('invalid-email');
      expect(emailControl.hasError('pattern')).toBe(true);

      emailControl.setValue('<EMAIL>');
      expect(emailControl.hasError('pattern')).toBe(false);
    });

    it('should initialize filter form', () => {
      expect(component.filterForm.get('companyFilter')).toBeDefined();
      expect(component.filterForm.get('nameFilter')).toBeDefined();
      expect(component.filterForm.get('roleFilter')).toBeDefined();
      expect(component.filterForm.get('statusFilter')).toBeDefined();
    });

    it('should change phone mask based on country code', () => {
      const phoneCode = '+44';
      const result = component.changeMask(phoneCode);

      expect(authService.checkDialCode).toHaveBeenCalledWith(phoneCode);
      expect(result).toBe('00000-00000');
    });
  });

  describe('Invite Operations', () => {
    beforeEach(() => {
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
    });

    it('should validate invite form submission with invalid members', () => {
      component.inviteMemberList = [
        { id: '<EMAIL>', email: '<EMAIL>' }
      ];

      component.onInviteSubmit();

      expect(component.submitted).toBe(true);
      expect(component.invalidMemberForm).toBe(true);
      expect(component.invalidMemberObject).toBe(1);
    });

    it('should submit valid invite form', () => {
      component.inviteMemberList = [
        {
          id: '<EMAIL>',
          email: '<EMAIL>',
          RoleId: 1,
          CompanyId: 1
        }
      ];

      component.onInviteSubmit();

      expect(projectService.inviteMembers).toHaveBeenCalledWith({
        membersList: component.inviteMemberList,
        ProjectId: 123,
        ParentCompanyId: 456,
        requestType: 0
      });
      expect(toastrService.success).toHaveBeenCalledWith('Members invited successfully', 'Success');
      expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Member Onboard Invite Sent');
    });

    it('should handle invite submission error', () => {
      component.inviteMemberList = [
        {
          id: '<EMAIL>',
          email: '<EMAIL>',
          RoleId: 1,
          CompanyId: 1
        }
      ];

      projectService.inviteMembers.mockReturnValue(throwError({ message: 'Invite failed' }));

      component.onInviteSubmit();

      expect(component.submitted).toBe(false);
      expect(toastrService.error).toHaveBeenCalledWith('Invite failed', 'OOPS!');
    });

    it('should append profile picture to invite member', () => {
      const userList = [
        { id: '<EMAIL>', email: '<EMAIL>' }
      ];
      component.inviteMemberList = userList;

      component.appendProfilePicture(userList);

      expect(userService.checkUser).toHaveBeenCalledWith({
        email: '<EMAIL>',
        ProjectId: 123,
        ParentCompanyId: 456
      });
    });

    it('should handle invalid email in appendProfilePicture', () => {
      const userList = [
        { id: 'invalid-email', email: 'invalid-email' }
      ];
      component.inviteMemberList = userList;

      component.appendProfilePicture(userList);

      expect(toastrService.error).toHaveBeenCalledWith('Please enter valid email');
      expect(component.inviteMemberList.length).toBe(0);
    });

    it('should handle existing member in appendProfilePicture', () => {
      const userList = [
        { id: '<EMAIL>', email: '<EMAIL>' }
      ];
      component.inviteMemberList = userList;

      userService.checkUser.mockReturnValue(of({
        existUser: { profilePic: 'test-pic.jpg' },
        isMemberExists: true,
        isRestrictedEmail: false
      }));

      component.appendProfilePicture(userList);

      expect(toastrService.error).toHaveBeenCalledWith('Member <EMAIL> already exists in this project');
    });

    it('should append role to member', () => {
      const member = { id: '<EMAIL>' };
      const roleId = 1;
      const roleName = 'Admin';

      component.inviteMemberList = [member];
      component.appendRole(roleId, member, roleName);

      expect(component.isRoleCollapsed).toBe(true);
      expect(component.inviteMemberList[0].RoleId).toBe(roleId);
      expect(component.inviteMemberList[0].roleName).toBe(roleName);
    });

    it('should append company to member', () => {
      const member = { id: '<EMAIL>' };
      const companyId = 1;
      const companyName = 'Test Company';

      component.inviteMemberList = [member];
      component.appendCompany(companyId, member, companyName);

      expect(component.isCompanyCollapsed).toBe(true);
      expect(component.inviteMemberList[0].CompanyId).toBe(companyId);
      expect(component.inviteMemberList[0].companyName).toBe(companyName);
    });

    it('should handle restricted email in onItemAdded', () => {
      const member = { id: '<EMAIL>' };
      component.inviteMemberList = [
        {
          id: '<EMAIL>',
          email: '<EMAIL>',
          RoleId: 1,
          ProjectId: 123,
          ParentCompanyId: 456
        }
      ];

      userService.checkUser.mockReturnValue(of({
        existUser: null,
        isMemberExists: false,
        isRestrictedEmail: true
      }));

      component.onItemAdded(member, 1, 'Admin');

      expect(toastrService.error).toHaveBeenCalledWith('Please use work email address to invite Project Admin');
    });

    it('should resend email link', () => {
      const memberData = {
        id: 1,
        ParentCompanyId: 456,
        User: { email: '<EMAIL>' },
        Role: { roleName: 'Admin' }
      };

      component.resendEmailLink(memberData);

      expect(projectService.resendInviteLink).toHaveBeenCalledWith({
        memberId: 1,
        ParentCompanyId: 456,
        email: '<EMAIL>',
        type: 'Admin',
        requestType: 0
      });
      expect(toastrService.success).toHaveBeenCalledWith('Invite link sent successfully');
      expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Member Onboard Invite Resent');
    });
  });

  describe('Edit Operations', () => {
    beforeEach(() => {
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
      component.memberList = [
        {
          id: 1,
          User: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
          RoleId: 2,
          CompanyId: 1,
          phoneCode: '+1',
          phoneNumber: '1234567890'
        }
      ];
    });

    it('should open edit modal and populate form', () => {
      const template = {} as any;
      const index = 0;

      component.openEditModal(index, template);

      expect(component.modalLoader).toBe(true);
      expect(component.editIndex).toBe(index);
      expect(component.memberEditForm.get('firstName').value).toBe('John');
      expect(component.memberEditForm.get('lastName').value).toBe('Doe');
      expect(component.memberEditForm.get('email').value).toBe('<EMAIL>');
      expect(component.memberEditForm.get('RoleId').value).toBe(2);
      expect(component.memberEditForm.get('phoneCode').value).toBe('+1');
      expect(component.memberEditForm.get('phoneNumber').value).toBe('1234567890');
    });

    it('should submit valid edit form', () => {
      component.editIndex = 0;
      component.memberEditForm.patchValue({
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        RoleId: 1,
        CompanyId: 2,
        phoneCode: '+1',
        phoneNumber: '9876543210'
      });

      component.onEditSubmit();

      expect(projectService.editMember).toHaveBeenCalledWith({
        id: 1,
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        phoneNumber: '9876543210',
        phoneCode: '+1',
        ProjectId: 123,
        RoleId: 1,
        CompanyId: 2,
        ParentCompanyId: 456
      });
      expect(toastrService.success).toHaveBeenCalledWith('Member updated successfully', 'Success');
      expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Edited Member');
    });

    it('should handle invalid edit form submission', () => {
      component.memberEditForm.patchValue({
        firstName: '',
        lastName: '',
        email: 'invalid-email'
      });

      const result = component.onEditSubmit();

      expect(component.editSubmitted).toBe(true);
      expect(component.formEditSubmitted).toBe(false);
      expect(result).toBeUndefined();
    });

    it('should handle edit form submission with empty names', () => {
      component.editIndex = 0;
      component.memberEditForm.patchValue({
        firstName: '   ',
        lastName: '   ',
        email: '<EMAIL>',
        RoleId: 1,
        CompanyId: 1,
        phoneCode: '+1',
        phoneNumber: '1234567890'
      });

      component.onEditSubmit();

      expect(toastrService.error).toHaveBeenCalledWith('Please enter valid Name.', 'OOPS!');
      expect(component.editSubmitted).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
    });

    it('should handle edit submission error', () => {
      component.editIndex = 0;
      component.memberEditForm.patchValue({
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        RoleId: 1,
        CompanyId: 2,
        phoneCode: '+1',
        phoneNumber: '9876543210'
      });

      projectService.editMember.mockReturnValue(throwError({ message: 'Edit failed' }));

      component.onEditSubmit();

      expect(component.editSubmitted).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
      expect(toastrService.error).toHaveBeenCalledWith('Edit failed', 'OOPS!');
    });

    it('should get overview after successful edit', () => {
      component.editIndex = 0;
      component.memberEditForm.patchValue({
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        RoleId: 1,
        CompanyId: 2,
        phoneCode: '+1',
        phoneNumber: '9876543210'
      });

      component.onEditSubmit();

      expect(profileService.getOverView).toHaveBeenCalledWith({
        ProjectId: 123,
        ParentCompanyId: 456
      });
    });
  });

  describe('Delete Operations', () => {
    beforeEach(() => {
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
      component.memberList = [
        { id: 1, isChecked: false },
        { id: 2, isChecked: true },
        { id: 3, isChecked: false }
      ];
    });

    it('should open delete modal for single member', () => {
      const template = {} as any;
      const index = 0;

      component.openDeleteModal(index, template);

      expect(component.deleteIndex[0]).toBe(1);
      expect(component.currentDeleteId).toBe(index);
      expect(component.remove).toBe(false);
    });

    it('should open delete modal for bulk delete', () => {
      const template = {} as any;
      const index = -1;

      component.openDeleteModal(index, template);

      expect(component.remove).toBe(true);
    });

    it('should delete single member', () => {
      component.deleteIndex = [1];
      component.selectAll = false;

      component.deleteMember();

      expect(projectService.deleteMember).toHaveBeenCalledWith({
        id: [1],
        ProjectId: 123,
        ParentCompanyId: 456,
        isSelectAll: false
      });
      expect(toastrService.success).toHaveBeenCalledWith('Member deleted successfully', 'Success');
      expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Deleted Member');
    });

    it('should handle delete error', () => {
      component.deleteIndex = [1];
      projectService.deleteMember.mockReturnValue(throwError({ message: 'Delete failed' }));

      component.deleteMember();

      expect(component.deleteSubmitted).toBe(false);
      expect(toastrService.error).toHaveBeenCalledWith('Delete failed', 'OOPS!');
    });

    it('should remove selected items', () => {
      component.selectAll = false;

      component.removeItem();

      expect(component.deleteSubmitted).toBe(true);
      expect(component.deleteIndex).toContain(2);
    });

    it('should remove all items when selectAll is true', () => {
      component.selectAll = true;

      component.removeItem();

      expect(component.deleteSubmitted).toBe(true);
      expect(projectService.deleteMember).toHaveBeenCalled();
    });
  });

  describe('Filter Operations', () => {
    beforeEach(() => {
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
    });

    it('should submit filter with multiple criteria', () => {
      component.filterForm.patchValue({
        companyFilter: 'Test Company',
        nameFilter: 'John',
        roleFilter: '1',
        statusFilter: 'Active'
      });

      component.filterSubmit();

      expect(component.filterCount).toBe(4);
      expect(component.pageNo).toBe(1);
      expect(projectService.listMember).toHaveBeenCalled();
    });

    it('should reset filter', () => {
      component.filterCount = 3;
      component.search = 'test';
      component.pageNo = 2;

      component.resetFilter();

      expect(component.filterCount).toBe(0);
      expect(component.search).toBe('');
      expect(component.pageNo).toBe(1);
      expect(projectService.listMember).toHaveBeenCalled();
    });

    it('should count filter criteria correctly', () => {
      component.filterForm.patchValue({
        companyFilter: 'Test Company',
        nameFilter: '',
        roleFilter: '1',
        statusFilter: ''
      });

      component.filterSubmit();

      expect(component.filterCount).toBe(2);
    });
  });

  describe('Modal Operations', () => {
    it('should open modal with correct configuration', () => {
      const template = {} as any;
      const action = 'create';
      const chosenValue = { CompanyId: 1 };

      component.openModal(template, action, chosenValue);

      expect(modalService.show).toHaveBeenCalledWith(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg members-popup custom-modal'
      });
    });

    it('should open filter modal', () => {
      const template = {} as any;

      component.openModal1(template);

      expect(component.filterModalLoader).toBe(true);
      expect(modalService.show).toHaveBeenCalledWith(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-sm filter-popup custom-modal'
      });
    });

    it('should open option modal', () => {
      const template = {} as any;

      component.openOptionModal(template);

      expect(modalService.show).toHaveBeenCalledWith(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-md new-gate-popup custom-modal options-modal'
      });
    });

    it('should close modal and reset forms', () => {
      const template = {} as any;
      const form = 'addMember';
      component.inviteDetailsForm.get('email').setValue('<EMAIL>');
      component.inviteDetailsForm.markAsDirty();

      component.close(template, form);

      expect(modalService.show).toHaveBeenCalled();
    });

    it('should cancel form with yes action', () => {
      component.inviteDetailsForm.get('email').setValue('<EMAIL>');
      component.inviteMemberList = [{ id: 1 }];

      component.cancelForm('yes');

      expect(component.inviteMemberList).toEqual([]);
    });

    it('should cancel form with no action', () => {
      const mockModalRef = { hide: jest.fn() };
      component.modalRef1 = mockModalRef as any;

      component.cancelForm('no');

      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should set company and open modal', () => {
      const template = {} as any;

      component.setCompany(template);

      expect(component.createNewModalLoader).toBe(true);
      expect(component.memberEditForm.get('CompanyId').value).toBe('');
    });
  });

  describe('Member Selection Operations', () => {
    beforeEach(() => {
      component.memberList = [
        { id: 1, isChecked: false },
        { id: 2, isChecked: true },
        { id: 3, isChecked: false }
      ];
    });

    it('should select all members', () => {
      component.selectAll = false;

      component.selectAllMemberData();

      expect(component.selectAll).toBe(true);
      expect(component.memberList[0].isChecked).toBe(true);
      expect(component.memberList[1].isChecked).toBe(true);
      expect(component.memberList[2].isChecked).toBe(true);
    });

    it('should deselect all members', () => {
      component.selectAll = true;

      component.selectAllMemberData();

      expect(component.selectAll).toBe(false);
      expect(component.memberList[0].isChecked).toBe(false);
      expect(component.memberList[1].isChecked).toBe(false);
      expect(component.memberList[2].isChecked).toBe(false);
    });

    it('should toggle individual member selection', () => {
      const index = 0;
      const initialState = component.memberList[index].isChecked;

      component.setSelectedItem(index);

      expect(component.memberList[index].isChecked).toBe(!initialState);
    });

    it('should check if any row is selected', () => {
      component.selectAll = false;

      const result = component.checkSelectedRow();

      expect(result).toBe(false); // Because memberList[1].isChecked is true
    });

    it('should return true when no rows are selected', () => {
      component.selectAll = false;
      component.memberList.forEach(member => member.isChecked = false);

      const result = component.checkSelectedRow();

      expect(result).toBe(true);
    });
  });

  describe('Utility Functions', () => {
    it('should validate alphabetic characters only', () => {
      const alphaEvent = { keyCode: 65 }; // 'A'
      const numericEvent = { keyCode: 49 }; // '1'
      const spaceEvent = { keyCode: 32 }; // Space
      const backspaceEvent = { keyCode: 8 }; // Backspace

      expect(component.alphaOnly(alphaEvent)).toBe(true);
      expect(component.alphaOnly(numericEvent)).toBe(false);
      expect(component.alphaOnly(spaceEvent)).toBe(true);
      expect(component.alphaOnly(backspaceEvent)).toBe(true);
    });

    it('should validate numeric characters only', () => {
      const numericEvent = { which: 49, keyCode: 49 }; // '1'
      const alphaEvent = { which: 65, keyCode: 65 }; // 'A'
      const backspaceEvent = { which: 8, keyCode: 8 }; // Backspace

      expect(component.numberOnly(numericEvent)).toBe(true);
      expect(component.numberOnly(alphaEvent)).toBe(false);
      expect(component.numberOnly(backspaceEvent)).toBe(true);
    });

    it('should check for empty string values', () => {
      const validForm = { firstName: 'John', lastName: 'Doe' };
      const invalidForm1 = { firstName: '   ', lastName: 'Doe' };
      const invalidForm2 = { firstName: 'John', lastName: '   ' };

      expect(component.checkStringEmptyValues(validForm)).toBe(false);
      expect(component.checkStringEmptyValues(invalidForm1)).toBe(true);
      expect(component.checkStringEmptyValues(invalidForm2)).toBe(true);
    });

    it('should navigate to company page', () => {
      const mockModalRef = { hide: jest.fn() };
      component.modalRef = mockModalRef as any;

      component.goToCompany();

      expect(mockModalRef.hide).toHaveBeenCalled();
      expect(router.navigate).toHaveBeenCalledWith(['/companies']);
    });

    it('should reset and close forms', () => {
      component.inviteDetailsForm.get('email').setValue('<EMAIL>');
      component.inviteMemberList = [{ id: 1 }];
      const mockModalRef = { hide: jest.fn() };
      component.modalRef = mockModalRef as any;

      component.resetAndClose();

      expect(component.inviteMemberList).toEqual([]);
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should copy input message to clipboard', async () => {
      const mockInputElement = {
        value: 'https://example.com/invite/123'
      } as HTMLInputElement;

      // Mock navigator.clipboard
      Object.assign(navigator, {
        clipboard: {
          writeText: jest.fn().mockResolvedValue(undefined)
        }
      });

      await component.copyInputMessage(mockInputElement);

      expect(navigator.clipboard.writeText).toHaveBeenCalledWith('https://example.com/invite/123');
      expect(toastrService.success).toHaveBeenCalledWith('Invite link copied successfully');
    });

    it('should handle clipboard copy error', async () => {
      const mockInputElement = {
        value: 'https://example.com/invite/123'
      } as HTMLInputElement;

      // Mock navigator.clipboard with error
      Object.assign(navigator, {
        clipboard: {
          writeText: jest.fn().mockRejectedValue(new Error('Copy failed'))
        }
      });

      await component.copyInputMessage(mockInputElement);

      expect(toastrService.error).toHaveBeenCalledWith('Failed to copy invite link');
    });

    it('should close copy link popup', () => {
      const mockModalRef = { hide: jest.fn() };
      component.copyInviteLinkPopup = mockModalRef as any;
      component.onboardingInviteLink = 'test-link';

      component.closeCopyLinkPopup();

      expect(mockModalRef.hide).toHaveBeenCalled();
      expect(component.onboardingInviteLink).toBe('');
    });
  });

  describe('Keyboard Event Handling', () => {
    it('should handle toggle keydown events', () => {
      const enterEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      const spaceEvent = { key: ' ', preventDefault: jest.fn() } as any;
      const otherEvent = { key: 'Tab', preventDefault: jest.fn() } as any;

      const sortSpy = jest.spyOn(component, 'sortByField');

      component.handleToggleKeydown(enterEvent, 'name', 'ASC');
      expect(enterEvent.preventDefault).toHaveBeenCalled();
      expect(sortSpy).toHaveBeenCalledWith('name', 'ASC');

      component.handleToggleKeydown(spaceEvent, 'email', 'DESC');
      expect(spaceEvent.preventDefault).toHaveBeenCalled();
      expect(sortSpy).toHaveBeenCalledWith('email', 'DESC');

      component.handleToggleKeydown(otherEvent, 'id', 'ASC');
      expect(otherEvent.preventDefault).not.toHaveBeenCalled();
    });

    it('should handle down keydown events for different types', () => {
      const enterEvent = { key: 'Enter', preventDefault: jest.fn() } as any;

      const appendCompanySpy = jest.spyOn(component, 'appendCompany');
      const appendRoleSpy = jest.spyOn(component, 'appendRole');
      const goToCompanySpy = jest.spyOn(component, 'goToCompany');
      const openModal1Spy = jest.spyOn(component, 'openModal1');

      component.handleDownKeydown(enterEvent, 'data', 'item', 'value', 'company');
      expect(appendCompanySpy).toHaveBeenCalledWith('data', 'item', 'value');

      component.handleDownKeydown(enterEvent, 'data', 'item', 'value', 'role');
      expect(appendRoleSpy).toHaveBeenCalledWith('data', 'item', 'value');

      component.handleDownKeydown(enterEvent, 'data', 'item', 'value', 'goto');
      expect(goToCompanySpy).toHaveBeenCalled();

      component.handleDownKeydown(enterEvent, 'data', 'item', 'value', 'open');
      expect(openModal1Spy).toHaveBeenCalledWith('data');
    });
  });

  describe('Activation/Deactivation Operations', () => {
    beforeEach(() => {
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
    });

    it('should open deactivate modal and get mapped requests', () => {
      const template = {} as any;
      const memberData = { id: 1, name: 'Test Member' };
      const event = false;

      component.openDeactivateModal(template, memberData, event);

      expect(component.memberData).toEqual(memberData);
      expect(modalService.show).toHaveBeenCalledWith(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-md new-gate-popup custom-modal deactivate-modal'
      });
      expect(projectService.getMemberMappedRequests).toHaveBeenCalledWith({
        ProjectId: 123,
        id: 1
      });
    });

    it('should activate member directly when event is true', () => {
      const template = {} as any;
      const memberData = { id: 1, name: 'Test Member' };
      const event = true;

      const activateSpy = jest.spyOn(component, 'activateMember');

      component.openDeactivateModal(template, memberData, event);

      expect(activateSpy).toHaveBeenCalledWith(memberData);
    });

    it('should activate member successfully', () => {
      const memberData = { id: 1, name: 'Test Member' };

      component.activateMember(memberData);

      expect(projectService.activateMember).toHaveBeenCalledWith({
        id: 1,
        ProjectId: 123,
        ParentCompanyId: 456,
        isActive: true
      });
      expect(toastrService.success).toHaveBeenCalledWith('Member activated successfully', 'Success');
      expect(projectService.listMember).toHaveBeenCalled();
    });

    it('should handle activation error', () => {
      const memberData = { id: 1, name: 'Test Member' };
      projectService.activateMember.mockReturnValue(throwError({ message: 'Activation failed' }));

      component.activateMember(memberData);

      expect(toastrService.error).toHaveBeenCalledWith('Activation failed', 'OOPS!');
    });

    it('should switch member for request with requestType', () => {
      const requestData: any = {
        requestType: true,
        memberDetails: [{ Member: { id: 1 } }]
      };
      const newMemberId = 2;

      component.switchMember(requestData, newMemberId);

      expect(requestData.changedMemberId).toBe(2);
    });

    it('should switch member for request without requestType', () => {
      const requestData: any = {
        requestType: false,
        controllUserDetails: { id: 1 }
      };
      const newMemberId = 2;

      component.switchMember(requestData, newMemberId);

      expect(requestData.changedMemberId).toBe(2);
    });

    it('should not set changedMemberId when same member is selected', () => {
      const requestData: any = {
        requestType: true,
        memberDetails: [{ Member: { id: 1 } }]
      };
      const sameMemberId = 1;

      component.switchMember(requestData, sameMemberId);

      expect(requestData.changedMemberId).toBeNull();
    });

    it('should deactivate member with mapped requests', () => {
      component.memberData = { id: 1 };
      component.mappedRequestList = [
        { id: 1, changedMemberId: 2 },
        { id: 2, changedMemberId: 3 }
      ];

      component.deactivateMemberAction();

      expect(projectService.deactivateMember).toHaveBeenCalledWith({
        id: 1,
        memberSwitchedRequests: component.mappedRequestList,
        ProjectId: 123,
        ParentCompanyId: 456
      });
      expect(toastrService.success).toHaveBeenCalledWith('Member deactivated successfully', 'Success');
    });

    it('should deactivate member without mapped requests', () => {
      component.memberData = { id: 1 };
      component.mappedRequestList = [];

      component.deactivateMemberAction();

      expect(projectService.deactivateMember).toHaveBeenCalledWith({
        id: 1,
        ProjectId: 123,
        ParentCompanyId: 456
      });
      expect(toastrService.success).toHaveBeenCalledWith('Member deactivated successfully', 'Success');
    });

    it('should handle deactivation error', () => {
      component.memberData = { id: 1 };
      component.mappedRequestList = [];
      projectService.deactivateMember.mockReturnValue(throwError({ message: 'Deactivation failed' }));

      component.deactivateMemberAction();

      expect(toastrService.error).toHaveBeenCalledWith('Deactivation failed', 'OOPS!');
    });

    it('should close deactivate member modal', () => {
      const mockModalRef = { hide: jest.fn() };
      component.modalRef = mockModalRef as any;

      component.closeDeactivateMember();

      expect(mockModalRef.hide).toHaveBeenCalled();
      expect(projectService.listMember).toHaveBeenCalled();
    });
  });

  describe('Invite Link Operations', () => {
    beforeEach(() => {
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
    });

    it('should open wait modal and get onboarding invite link', () => {
      const template = {} as any;
      const template1 = {} as any;
      const memberData = {
        ProjectId: 123,
        ParentCompanyId: 456,
        id: 1,
        UserId: 2,
        User: { email: '<EMAIL>' }
      };

      component.openWaitModal(template, template1, memberData);

      expect(modalService.show).toHaveBeenCalledWith(template, {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
      });
      expect(projectService.getOnboardingInviteLink).toHaveBeenCalledWith({
        ProjectId: 123,
        ParentCompanyId: 456,
        id: 1,
        UserId: 2,
        email: '<EMAIL>'
      });
    });

    it('should handle onboarding invite link error', () => {
      const template = {} as any;
      const template1 = {} as any;
      const memberData = {
        ProjectId: 123,
        ParentCompanyId: 456,
        id: 1,
        UserId: 2,
        User: { email: '<EMAIL>' }
      };

      projectService.getOnboardingInviteLink.mockReturnValue(throwError({ message: 'Link generation failed' }));

      component.openWaitModal(template, template1, memberData);

      expect(component.deleteSubmitted).toBe(false);
      expect(toastrService.error).toHaveBeenCalledWith('Link generation failed', 'OOPS!');
    });
  });

  describe('Error Handling', () => {
    it('should show error with details', () => {
      const error = {
        message: {
          details: [{ field: 'Email is required' }]
        }
      };

      component.showError(error);

      expect(component.submitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
      expect(toastrService.error).toHaveBeenCalledWith('Email is required');
    });

    it('should handle error with statusCode 400', () => {
      const error = {
        message: { statusCode: 400, details: [{ field: 'Validation error' }] }
      };

      const showErrorSpy = jest.spyOn(component, 'showError');

      // Simulate error handling in invite submission
      projectService.inviteMembers.mockReturnValue(throwError(error));
      component.inviteMemberList = [{ id: '<EMAIL>', RoleId: 1, CompanyId: 1 }];

      component.onInviteSubmit();

      expect(showErrorSpy).toHaveBeenCalledWith(error);
    });

    it('should handle error without message', () => {
      const error = {};

      projectService.inviteMembers.mockReturnValue(throwError(error));
      component.inviteMemberList = [{ id: '<EMAIL>', RoleId: 1, CompanyId: 1 }];

      component.onInviteSubmit();

      expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });
  });

  describe('Lifecycle Methods', () => {
    it('should handle onHidden', () => {
      expect(() => component.onHidden()).not.toThrow();
    });

    it('should handle onShown', () => {
      expect(() => component.onShown()).not.toThrow();
    });

    it('should handle isOpenChange', () => {
      expect(() => component.isOpenChange()).not.toThrow();
    });

    it('should handle ngOnInit', () => {
      expect(() => component.ngOnInit()).not.toThrow();
    });

    it('should call getMembers in ngAfterViewInit', () => {
      const spy = jest.spyOn(component, 'getMembers');
      component.ngAfterViewInit();
      expect(spy).toHaveBeenCalled();
    });
  });

  describe('Additional Branch Coverage', () => {
    it('should call openModalPopup and set modalRef1', () => {
      const template = {} as any;
      component.openModalPopup(template);
      expect(component.modalRef1).toBeDefined();
    });

    it('should call cancelForm with no action', () => {
      const mockModalRef1 = { hide: jest.fn() };
      component.modalRef1 = mockModalRef1 as any;
      component.cancelForm('no');
      expect(mockModalRef1.hide).toHaveBeenCalled();
    });

    it('should call cancelForm with yes action and reset forms', () => {
      const mockModalRef1 = { hide: jest.fn() };
      const mockModalRef = { hide: jest.fn() };
      component.modalRef1 = mockModalRef1 as any;
      component.modalRef = mockModalRef as any;
      component.inviteDetailsForm.get('RoleId').setValue('test');
      component.inviteMemberList = [{ id: 1 }];
      component.memberEditForm.get('email').setValue('<EMAIL>');
      component.cancelForm('yes');
      expect(component.inviteDetailsForm.get('RoleId').value).toBe('');
      expect(component.inviteMemberList).toEqual([]);
      expect(component.memberEditForm.get('email').value).toBeNull();
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should call removeItem with selectAll true', () => {
      component.selectAll = true;
      const spy = jest.spyOn(component, 'deleteMember');
      component.removeItem();
      expect(spy).toHaveBeenCalled();
    });

    it('should call removeItem with selectAll false and checked members', () => {
      component.selectAll = false;
      component.memberList = [
        { id: 1, isChecked: false },
        { id: 2, isChecked: true },
        { id: 3, isChecked: false }
      ];
      const spy = jest.spyOn(component, 'deleteMember');
      component.removeItem();
      expect(component.deleteIndex).toContain(2);
      expect(spy).toHaveBeenCalled();
    });
  });

  describe('Autocomplete Operations', () => {
    it('should request autocomplete items', () => {
      const searchText = 'john';

      const result = component.requestAutocompleteItems(searchText);

      expect(projectService.searchAllMember).toHaveBeenCalledWith({
        ParentCompanyId: 456,
        search: searchText,
        ProjectId: 123
      });
      expect(result).toBeDefined();
    });
  });
});
