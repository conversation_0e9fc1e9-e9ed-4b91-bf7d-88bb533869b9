<div class="fs18 fw-bold cairo-regular my-5 text-black text-center" *ngIf="mainLoader">
  Loading...
</div>

<div *ngIf="!mainLoader" class="">
  <div class="row project-info mx-0 max-container">
    <div class="col-md-4 col-sm-6">
      <div class="row info-list">
        <div class="col-md-6"><h3 class="info-heading">Project Name:</h3></div>
        <div class="col-md-6">
          <p class="info-content">{{ projectDetail ? projectDetail.projectName : '-' }}</p>
        </div>
      </div>
      <div class="row info-list">
        <div class="col-md-6"><h3 class="info-heading">Subscription Plan:</h3></div>
        <div class="col-md-6">
          <p class="info-content">
            {{ projectDetail ? projectDetail.stripePlan.stripeProductName : '-' }}
          </p>
        </div>
      </div>
      <div class="row info-list">
        <div class="col-md-6"><h3 class="info-heading">Subscription On:</h3></div>
        <div class="col-md-6">
          <p class="info-content">
            {{
              projectDetail?.subDetail?.subscribedOn
                ? (projectDetail?.subDetail?.subscribedOn | date : 'MMM dd, yyyy')
                : '------'
            }}
          </p>
        </div>
      </div>
      <div class="row info-list">
        <div class="col-md-6"><h3 class="info-heading">Status:</h3></div>
        <div class="col-md-6" *ngIf="projectDetail">
          <p
            class="info-content"
            *ngIf="
              projectDetail.status != '' &&
              projectDetail.status != null &&
              projectDetail.status != undefined &&
              !projectDetail?.cancel_at_period_end
            "
          >
            Expired
          </p>
          <p
            class="info-content"
            *ngIf="
              (projectDetail.status == '' ||
                projectDetail.status == null ||
                projectDetail.status == undefined) &&
              !projectDetail?.cancel_at_period_end
            "
          >
            Active
          </p>
          <p
            class="info-content"
            *ngIf="
              projectDetail?.cancel_at_period_end &&
              projectDetail?.subDetail?.status === 'To be cancel'
            "
          >
            Active till {{ projectDetail?.cancel_at | date : 'mediumDate' }}
          </p>
          <p
            class="info-content"
            *ngIf="
              projectDetail?.cancel_at_period_end &&
              projectDetail?.subDetail?.status !== 'To be cancel'
            "
          >
            {{ projectDetail?.subDetail?.status }}
          </p>
        </div>
      </div>
    </div>
    <div class="col-md-4 col-sm-6">
      <div class="row info-list">
        <div class="col-md-6"><h3 class="info-heading">Project Address:</h3></div>
        <div class="col-md-6">
          <p class="info-content">
            {{ projectDetail ? projectDetail.projectLocation : '-' }}
          </p>
        </div>
      </div>
      <div class="primary-tooltip">
        <h3 class="info-heading fs14 fw700">
          Site Plan:
          <div class="dot-border-info location-border-info tooltip location-tooltip">
            <span class="fw700 info-icon fs12">i</span>
            <span class="tooltiptext tooltiptext-info"
              >Only *pdf, *png, *jpg, *jpeg files are allowed</span
            >
          </div>
        </h3>
      </div>
      <div class="input-group project-settings-tabfile">
          <div style="display: inline-flex" *ngIf="!logisticPlanUploaded">
            <label for="infoformFile" class="custom-label">Upload File</label>
            <input
              class="px-0 border-0 bg-transparent fs12"
              type="file"
              (change)="onPlanSelected($event.target.files)"
              id="infoformFile"
              #logisticPlan
            />

            <span class="float-end fs12 me-3 p2 color-orange fw-bold my-auto" *ngIf="planUploading">
              <em class="fas fa-sync fa-spin" width="10px" alt="uploading"></em>
            </span>
          </div>
          <div *ngIf="logisticPlanUploaded">
            <span class="fs12 btm-border-orange ms-0">
              {{ logisticPlanName }}
            </span>
            <img
              src="../../../assets/images/download_report.svg"
              alt="export"
              class="h-15px mx-2 mt-2 c-pointer"
              (click)="downloadPlan()"
              (keydown)="handleDownKeydown($event, '', 'down')"
            />
            <img
              src="./assets/images/delete.svg"
              alt="delete"
              class="h-15px c-pointer mt-2"
              (click)="openDeleteModal(deleteLogisticPlan)"
              (keydown)="handleDownKeydown($event, deleteLogisticPlan, 'delete')"
            />
          </div>
      </div>
    </div>
    <div class="col-md-4 project-settings-information" *ngIf="projectDetail">
      <google-map class="agm-map project-map" [options]="mapOptions">
        <map-marker [position]="marker.position" [options]="marker.options"></map-marker>
      </google-map>
    </div>
  </div>

  <!-- Project Sharing Settings -->
  <div class="project-sharing-settings">
    <div class="max-container">
      <div class="d-flex justify-content-between mb-3">
        <div class="primary-tooltip">
          <h3 class="info-heading fs14 fw700">
            Project Sharing Settings
            <div class="dot-border-info location-border-info tooltip location-tooltip">
              <span class="fw700 info-icon fs12">i</span>
              <span class="tooltiptext tooltiptext-info">Click Save button after changes</span>
            </div>
          </h3>
        </div>
        <button
          class="btn btn-orange-dark rounded-pill px-4 fs14 c-pointer"
          (click)="submit()"
          [disabled]="saveButtonDisabled"
        >
          <em
            class="fa fa-spinner"
            aria-hidden="true"
            *ngIf="updateLoader && projectSharingSettingsFormData.valid"
          ></em
          >Save
        </button>
      </div>
      <div
        class="custom-alert w-fit-content mb-2 fs12 cairo-regular fw-bold"
        *ngIf="!saveButtonDisabled"
      >
        Please remember to click the Save button once you've made your changes
      </div>
      <div
        class="col-md-12 fs18 fw-bold cairo-regular my-5 text-black text-center"
        *ngIf="loader && isSaving"
      >
        Loading...
      </div>
      <div *ngIf="!loader" class="row align-items-center project-information-checkbox">
        <div class="col-md-6 mb-4 mb-md-0 sharing-col">
          <ul class="list-group setting-list mb-2">
            <li class="list-group-item bg-transparent">
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="checkbox"
                  [checked]="projectSharingSettingsFormData.value.isPublicWebsiteEnabled"
                  (change)="projectSharingSetting('publicWebsiteEnabled')"
                  id="gridCheck"
                  checked
                />
                <label class="form-check-label c-pointer fs12 px-3" for="gridCheck">
                  Enable Public Website (QR Code Compatible)
                </label>
              </div>
            </li>
          </ul>
          <ul
            class="list-group sub-list mb-2"
            *ngIf="projectSharingSettingsFormData.value.isPublicWebsiteEnabled"
          >
            <li class="list-group-item bg-transparent project_disable_button">
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="checkbox"
                  [disabled]="projectSharingSettingsFormData.value.isPublicWebsiteEnabled"
                  [checked]="projectSharingSettingsFormData.value.shareProjectInformation"
                  (change)="projectSharingSetting('shareProjectInformation')"
                  id="gridCheck1"
                />
                <label class="form-check-label c-pointer fs12 px-3" for="gridCheck1">
                  Share Project Information (Name, Address, Location)
                </label>
              </div>
            </li>
            <li class="list-group-item bg-transparent">
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="checkbox"
                  [checked]="projectSharingSettingsFormData.value.shareprojectLogisticPlan"
                  (change)="projectSharingSetting('shareprojectLogisticPlan')"
                  id="gridCheck2"
                />
                <label class="form-check-label c-pointer fs12 px-3" for="gridCheck2">
                  Share Project Logistics Plan (attachment)
                </label>
              </div>

            </li>
          </ul>
          <label for="delCal"
            class="dbl-sub-heading fs12 fw-bold"
            *ngIf="projectSharingSettingsFormData.value.isPublicWebsiteEnabled"
            >Delivery Calendar</label
          >
          <ul
            class="list-group double-sub-list mb-2"
            *ngIf="projectSharingSettingsFormData.value.isPublicWebsiteEnabled"
          >
            <li class="list-group-item bg-transparent">
              <div class="form-check">
                <input id="delCal"
                  class="form-check-input"
                  type="checkbox"
                  [checked]="projectSharingSettingsFormData.value.allowGuestToAddDeliveryBooking"
                  (change)="projectSharingSetting('addDeliveryBooking')"
                  id="gridCheck3"
                />
                <label class="form-check-label c-pointer fs12 px-3" for="gridCheck3">
                  Allow guests to add delivery bookings
                </label>
              </div>
            </li>
            <li class="list-group-item bg-transparent">
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="checkbox"
                  [checked]="projectSharingSettingsFormData.value.allowGuestToViewDeliveryCalendar"
                  (change)="projectSharingSetting('viewDeliveryCalendar')"
                  id="gridCheck4"
                />
                <label class="form-check-label c-pointer fs12 px-3" for="gridCheck4">
                  Allow guests to view the full calendar
                </label>
              </div>
            </li>
          </ul>
          <label for="craCal"
            class="dbl-sub-heading fs12 fw-bold"
            *ngIf="projectSharingSettingsFormData.value.isPublicWebsiteEnabled"
            >Crane Calendar</label
          >
          <ul
            class="list-group double-sub-list"
            *ngIf="projectSharingSettingsFormData.value.isPublicWebsiteEnabled"
          >
            <li class="list-group-item bg-transparent">
              <div class="form-check">
                <input id="craCal"
                  class="form-check-input"
                  type="checkbox"
                  [checked]="projectSharingSettingsFormData.value.allowGuestToAddCraneBooking"
                  (change)="projectSharingSetting('addCraneBooking')"
                  id="gridCheck5"
                />
                <label class="form-check-label c-pointer fs12 px-0" for="gridCheck5">
                  Allow guests to add crane bookings
                </label>
              </div>
            </li>
            <li class="list-group-item bg-transparent">
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="checkbox"
                  [checked]="projectSharingSettingsFormData.value.allowGuestToViewCraneCalendar"
                  (change)="projectSharingSetting('viewCraneCalendar')"
                  id="gridCheck6"
                />
                <label class="form-check-label c-pointer fs12 px-0" for="gridCheck6">
                  Allow guests to view the full calendar
                </label>
              </div>
            </li>
          </ul>
          <label  for="conCal"
            class="dbl-sub-heading fs12 fw-bold"
            *ngIf="projectSharingSettingsFormData.value.isPublicWebsiteEnabled"
            >Concrete Calendar</label
          >
          <ul
            class="list-group double-sub-list"
            *ngIf="projectSharingSettingsFormData.value.isPublicWebsiteEnabled"
          >
            <li class="list-group-item bg-transparent">
              <div class="form-check">
                <input  id="conCal"
                  class="form-check-input"
                  type="checkbox"
                  [checked]="projectSharingSettingsFormData.value.allowGuestToAddConcreteBooking"
                  (change)="concreteProjectSharingSettings('addConcreteBooking')"
                  id="gridCheck7"
                />
                <label class="form-check-label c-pointer fs12 px-0" for="gridCheck7">
                  Allow guests to add concrete bookings
                </label>
              </div>
            </li>
            <li class="list-group-item bg-transparent">
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="checkbox"
                  [checked]="projectSharingSettingsFormData.value.allowGuestToViewConcreteCalendar"
                  (change)="concreteProjectSharingSettings('viewConcreteCalendar')"
                  id="gridCheck8"
                />
                <label class="form-check-label c-pointer fs12 px-0" for="gridCheck8">
                  Allow guests to view the full calendar
                </label>
              </div>

            </li>
          </ul>
          <ul class="list-group double-sub-list mt-2">
            <li class="list-group-item bg-transparent">
              <label  for="atweb"
                class="fs12 color-grey7 fw600"
                *ngIf="projectSharingSettingsFormData.value.isPublicWebsiteEnabled"
                >Enter Website auto-refresh rate</label
              >
              <div
                class="input-group mb-3 custom-material-form w-20"
                *ngIf="projectSharingSettingsFormData.value.isPublicWebsiteEnabled"
              >
                <input  id="atweb"
                  type="text"
                  class="form-control material-input fs14 color-grey7 h-auto bg-transparent"
                  [(ngModel)]="this.projectSharingSettingsFormData.value.autoRefreshRateInMinutes"
                  (ngModelChange)="
                    checkRefreshRate(
                      this.projectSharingSettingsFormData.value.autoRefreshRateInMinutes,
                      this.refreshRate
                    )
                  "
                  placeholder="0"
                />
                <span class="input-group-text bg-transparent fs12" id="mins">Mins</span>
              </div>
            </li>
          </ul>
        </div>
        <div
          class="col-md-6 qr-col"
          *ngIf="projectSharingSettingsFormData.value.isPublicWebsiteEnabled"
        >
          <div class="card qr-card">
            <div class="card-body">
              <label for="pweb" class="card-heading">Public website QR Code</label>
              <div class="row">
                <div class="col-md-6 col-xl-5 px-0" #QRcodecanvas>
                  <qrcode  id="pweb"
                    [qrdata]="this.projectSharingSettingsFormData.value.publicWebsiteUrl"
                    [width]="170"
                    [errorCorrectionLevel]="'M'"
                  ></qrcode>
                </div>
                <div class="col-md-6 col-xl-7">
                  <button
                    class="btn btn-orange px-3 fs12 rounded-pill fw-bold mb-4 me-2"
                    (click)="openPublicWebsite()"
                  >
                    Visit Public URL
                  </button>
                  <button
                    class="btn btn-orange px-3 fs12 rounded-pill fw-bold mb-4 me-2"
                    tooltip="Copied!"
                    triggers="click"
                    triggers="mousedown:focusout"
                    [delay]="500"
                    triggers="mousedown:blur"
                    [delay]="500"
                    (click)="copyLink(this.projectSharingSettingsFormData.value.publicWebsiteUrl)"
                  >
                    Copy URL
                  </button>
                  <button
                    class="btn btn-orange px-3 fs12 rounded-pill fw-bold mb-4 me-2"
                    (click)="downloadQRCodeImage()"
                  >
                    Download QR Code Image
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Project Sharing Settings -->
</div>

<!-- Delete Logistic Plan -->
<ng-template #deleteLogisticPlan>
  <div class="modal-body">
    <div class="text-center my-4">
      <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">Are you sure you want to delete ?</p>

      <button
        class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
        (click)="deletePlan('no')"
      >
        No
      </button>

      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5 mt-0"
        (click)="deletePlan('yes')"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="planDeleting"></em>Yes
      </button>
    </div>
  </div>
</ng-template>
<!-- Delete Logistic Plan -->
