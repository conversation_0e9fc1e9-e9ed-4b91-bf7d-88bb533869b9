/* eslint-disable max-lines-per-function */
import {
  Component, Input, OnInit, TemplateRef,
} from '@angular/core';
import {
  UntypedFormArray, UntypedFormBuilder, UntypedFormGroup, Validators, UntypedFormControl,
} from '@angular/forms';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import moment from 'moment';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { ProjectService } from '../../services/profile/project.service';
import { CalendarService } from '../../services/profile/calendar.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import {
  weekDays, recurrence, repeatWithSingleRecurrence, repeatWithMultipleRecurrence,
} from '../../services/common';

@Component({
  selector: 'app-edit-calendar-event',
  templateUrl: './edit-calendar-event.component.html',
})
export class EditCalendarEventComponent implements OnInit {
  @Input() data: any;

  @Input() title: string;

  public editCalendarEvent: UntypedFormGroup;

  public submitted = false;

  public formSubmitted = false;

  public ProjectId: any;

  public ParentCompanyId: any;

  public loader = false;

  public modalLoader = false;

  public endTime: Date;

  public startTime: Date;

  public selectedRecurrence = 'Does Not Repeat';

  public locationDropdownSettings: IDropdownSettings;

  public equipmentDropdownSettings: IDropdownSettings;

  public gateDropdownSettings: IDropdownSettings;

  public isAllDayChosen = false;

  public todayDate = new Date();

  public dateArg: any;

  public editEventId: any;

  public checkform: any = new UntypedFormArray([]);

  public recurrence = recurrence;

  public repeatWithSingleRecurrence = repeatWithSingleRecurrence;

  public repeatWithMultipleRecurrence = repeatWithMultipleRecurrence;

  public weekDays: any = weekDays;

  public isRepeatWithMultipleRecurrence = false;

  public isRepeatWithSingleRecurrence = false;

  public locationList: any;
  public gateList: any;
  public equipmentList: any;
  public timeZone: any;
  public selectedLocationId: any;


  public valueExists = [];

  public showRecurrenceTypeDropdown = false;

  public message = '';

  public monthlyDayOfWeek = '';

  public monthlyDate = '';

  public monthlyLastDayOfWeek = '';

  public enableOption = false;

  public timezoneList: any[] = [];

  public selectedValue: any;

  public authUser: any = {};

  public dropdownSettings: IDropdownSettings;

  public timezonevalues: any;

  public eventDetail: any = {};
  getChosenLocation: any[];

  public noEquipmentOption = { id: 0, equipmentName: 'No Equipment Needed' };

  public constructor(
    private readonly formBuilder: UntypedFormBuilder,
    private readonly modalRef: BsModalRef,
    private modalRef1: BsModalRef,
    private readonly modalService: BsModalService,
    public projectService: ProjectService,
    private readonly toastr: ToastrService,
    public calendarService: CalendarService,
    private readonly deliveryService: DeliveryService,
  ) {
    this.projectService.projectParent.subscribe((response7): void => {
      if (response7 !== undefined && response7 !== null && response7 !== '') {
        this.loader = true;
        this.ProjectId = response7.ProjectId;
        this.ParentCompanyId = response7.ParentCompanyId;
        this.loader = true;
      }
    });
    this.getTimeZoneList();
    this.ediCalendarForm();
    this.getLocations();

    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
      }
    });
  }

  public ngOnInit(): void {
    this.getEventDetail();
  }

  public getTimeZoneList(): void {
    this.projectService.getTimeZoneList().subscribe({
      next:(response: any): void => {
        this.loader = true;
        if (response) {
          const params = {
            ProjectId: this.ProjectId,
          };
          if (params.ProjectId) {
            this.projectService.getSingleProject(params).subscribe((projectList: any): void => {
              if (projectList) {
                this.timezoneList = response.data;
                this.dropdownSettings = {
                  singleSelection: true,
                  idField: 'id',
                  textField: 'location',
                  allowSearchFilter: true,
                  closeDropDownOnSelection: true,
                };
                this.loader = false;
              }
            });
          }
        }
      },
      error: (getTimeZoneListErr): void => {
        if (getTimeZoneListErr.message?.statusCode === 400) {
          this.showError(getTimeZoneListErr);
        } else if (!getTimeZoneListErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(getTimeZoneListErr.message, 'OOPS!');
        }
      },
    });
  }


  public getLocations(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getLocations(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.locationList = data;
        if(data[0].gateDetails){
          this.gateList = data[0].gateDetails
        }else{
          this.gateList = []
        }
        this.equipmentList = [this.noEquipmentOption, ...data[0].EquipmentId?data[0].EquipmentId:[]]
        this.equipmentDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'equipmentName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
        this.gateDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'gateName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
        this.locationDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'locationPath',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          allowSearchFilter: true,
        };
      }
    });
  }

  public timeZoneSelected(id): void {
    this.selectedValue = this.timezoneList.find((obj: any): any => +obj.id === +id);
  }

  public toggleAllDay(data): void {
    if (data) {
      this.isAllDayChosen = true;
      this.resetTimer();
    } else {
      this.isAllDayChosen = false;
      this.setCurrentTiming();
    }
  }

  public getEventDetail(): void {
    this.loader = true;
    const getData = this.data;
    this.editEventId = getData.id;
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.calendarService.getEventData(getData.id, params).subscribe((res): void => {
      this.eventDetail = res.event;
      this.editCalendarEvent.get('id').setValue(this.eventDetail.id);
      this.editCalendarEvent.get('description').setValue(this.eventDetail.description);
      this.editCalendarEvent
        .get('fromDate')
        .setValue(new Date(moment(this.eventDetail.fromDate).format('MM/DD/YYYY')));
      this.editCalendarEvent
        .get('toDate')
        .setValue(new Date(moment(this.eventDetail.toDate).format('MM/DD/YYYY')));
      this.editCalendarEvent
        .get('endDate')
        .setValue(new Date(moment(this.eventDetail.endDate).format('MM/DD/YYYY')));

      this.editCalendarEvent.get('startTime').setValue(new Date(this.eventDetail.startTime));
      this.editCalendarEvent.get('endTime').setValue(new Date(this.eventDetail.endTime));
      this.editCalendarEvent.get('isAllDay').setValue(this.eventDetail.isAllDay);
      this.editCalendarEvent.get('GateId').setValue(this.eventDetail.GateId);
      this.editCalendarEvent.get('LocationId').setValue(this.eventDetail.LocationId);
      this.editCalendarEvent.get('EquipmentId').setValue(JSON.parse(this.eventDetail.EquipmentId)); //this.eventDetail.EquipmentId);
      this.equipmentDropdownSettings = {
        singleSelection: false,
        idField: 'id',
        textField: 'equipmentName',
        selectAllText: 'Select All',
        unSelectAllText: 'UnSelect All',
        itemsShowLimit: 6,
        allowSearchFilter: true,
      };
      this.locationDropdownSettings = {
        singleSelection: false,
        idField: 'id',
        textField: 'locationPath',
        selectAllText: 'Select All',
        unSelectAllText: 'UnSelect All',
        allowSearchFilter: true,
      };
      this.gateDropdownSettings = {
        singleSelection: false,
        idField: 'id',
        textField: 'gateName',
        selectAllText: 'Select All',
        unSelectAllText: 'UnSelect All',
        itemsShowLimit: 6,
        allowSearchFilter: true,
      };
      this.editCalendarEvent
        .get('isApplicableToDelivery')
        .setValue(this.eventDetail.isApplicableToDelivery);
      this.editCalendarEvent
        .get('isApplicableToCrane')
        .setValue(this.eventDetail.isApplicableToCrane);
      this.editCalendarEvent
        .get('isApplicableToConcrete')
        .setValue(this.eventDetail.isApplicableToConcrete);
      this.editCalendarEvent
        .get('isApplicableToInspection')
        .setValue(this.eventDetail.isApplicableToInspection);
      this.editCalendarEvent.get('recurrence').setValue(this.eventDetail.recurrence);
      this.editCalendarEvent.get('repeatEveryCount').setValue(this.eventDetail.repeatEveryCount);
      this.editCalendarEvent.get('repeatEveryType').setValue(this.eventDetail.repeatEveryType);
      this.editCalendarEvent.get('dateOfMonth').setValue(this.eventDetail.dateOfMonth);
      this.editCalendarEvent.get('monthlyRepeatType').setValue(this.eventDetail.monthlyRepeatType);
      if (this.eventDetail.chosenDateOfMonth) {
        this.editCalendarEvent.get('chosenDateOfMonth').setValue(1);
      } else {
        this.showMonthlyRecurrence();
        if (this.editCalendarEvent.get('monthlyRepeatType').value === this.monthlyDayOfWeek) {
          this.editCalendarEvent.get('chosenDateOfMonth').setValue(2);
        }
        if (this.editCalendarEvent.get('monthlyRepeatType').value === this.monthlyLastDayOfWeek) {
          this.editCalendarEvent.get('chosenDateOfMonth').setValue(3);
        }
      }
      this.changeRecurrenceCount(+this.eventDetail.repeatEveryCount);
      this.chooseRepeatEveryType(this.eventDetail.repeatEveryType, this.eventDetail);
      setTimeout(() => {
        this.setEquipment();
        this.setGate();
        this.setLocation()
        this.setTimezone();
      }, 1000);

      if (this.editCalendarEvent.get('isAllDay').value) {
        this.isAllDayChosen = true;
      }
    });
  }


  public setTimezone(): void {
    if (this.eventDetail.TimeZone) {
      this.timezonevalues = [];
      const data = {
        id: this.eventDetail.TimeZone.id,
        location: this.eventDetail.TimeZone.location,
      };
      this.timezonevalues.push(data);
      this.editCalendarEvent.get('TimeZoneId').patchValue(this.timezonevalues);
    }
  }


  public setlocation(): void {
    const locationId = this.eventDetail?.LocationId;

    const selectedLocation = this.locationList.find(loc => loc.id === locationId);

    if (selectedLocation) {
      this.getChosenLocation = [{
        id: selectedLocation.id,
        locationPath: selectedLocation.locationPath
      }];

      this.editCalendarEvent.get('LocationId')?.patchValue(this.getChosenLocation);
      this.selectedLocationId = selectedLocation.id;
    }

  }



  public setEquipment(): void {
    const EquipmentId  = JSON.parse(this.eventDetail.EquipmentId);
    const newEquipmentList = [];
    if (Array.isArray(EquipmentId)) {
      EquipmentId.forEach((id: any) => {
        const match = this.equipmentList.find(e => e.id === id);
        if (match) {
          newEquipmentList.push(match);
        }
      });
    }
    this.editCalendarEvent.get('EquipmentId').patchValue(newEquipmentList);
  }
  public setGate(): void {
    const GateId  = JSON.parse(this.eventDetail.GateId);
    const newGateList = [];

    if (Array.isArray(GateId)) {
      GateId.forEach((id: any) => {
        const match = this.gateList.find(e => e.id === id);
        if (match) {
          newGateList.push(match);
        }
      });
    }
    this.editCalendarEvent.get('GateId').patchValue(newGateList);
  }
  public setLocation(): void {
    const LocationId  = JSON.parse(this.eventDetail.LocationId);
    const newLocationList = [];

    if (Array.isArray(LocationId)) {
      LocationId.forEach((id: any) => {
        const match = this.locationList.find(e => e.id === id);
        if (match) {
          newLocationList.push(match);
        }
      });
    }
    this.editCalendarEvent.get('LocationId').patchValue(newLocationList);
  }

  public chooseRepeatEveryType(value, eventDetail): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    if (value === 'Day' || value === 'Days') {
      this.editCalendarEvent.get('recurrence').setValue('Daily');
    }
    if (value === 'Week' || value === 'Weeks') {
      this.editCalendarEvent.get('recurrence').setValue('Weekly');
    }
    if (value === 'Month' || value === 'Months') {
      this.editCalendarEvent.get('recurrence').setValue('Monthly');
    }
    if (value === 'Year' || value === 'Years') {
      this.editCalendarEvent.get('recurrence').setValue('Yearly');
    }
    if (value === 'Day' || value === 'Days') {
      this.checkform = this.editCalendarEvent.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day: any): any => {
        const dayObj = day;
        dayObj.checked = true;
        dayObj.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj.value));
        return dayObj;
      });
    }
    if (value === 'Week' || value === 'Weeks') {
      if (eventDetail?.days?.length > 0) {
        this.checkform = this.editCalendarEvent.get('days') as UntypedFormArray;
        this.checkform.controls = [];
        this.weekDays = this.weekDays.map((day1: any): void => {
          const dayObj1 = day1;
          if (eventDetail.days.includes(dayObj1.value)) {
            dayObj1.checked = true;
            this.checkform.push(new UntypedFormControl(dayObj1.value));
          } else {
            dayObj1.checked = false;
          }
          dayObj1.isDisabled = false;
          return dayObj1;
        });
      } else {
        this.weekDays = this.weekDays.map((day2: any): void => {
          const dayObj2 = day2;
          if (dayObj2.value === 'Monday') {
            dayObj2.checked = true;
          } else {
            dayObj2.checked = false;
          }
          dayObj2.isDisabled = false;
          return dayObj2;
        });
        this.checkform = this.editCalendarEvent.get('days') as UntypedFormArray;
        this.checkform.controls = [];
        this.checkform.push(new UntypedFormControl('Monday'));
      }
    }
    if (value === 'Day' || value === 'Week' || value === 'Month' || value === 'Year') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showRecurrenceTypeDropdown = false;
    }
    if (value === 'Days' || value === 'Weeks' || value === 'Months' || value === 'Years') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.showRecurrenceTypeDropdown = false;
    }
    if (this.editCalendarEvent.get('repeatEveryCount').value > 1) {
      this.showRecurrenceTypeDropdown = true;
      this.isRepeatWithMultipleRecurrence = false;
    }
    this.selectedRecurrence = this.editCalendarEvent.get('recurrence').value;
    this.occurMessage();
    this.showMonthlyRecurrence();
  }

  public setFormValue(value) {
    if (value === 'Does Not Repeat') {
      this.editCalendarEvent.get('repeatEveryType').setValue('');
    } else {
      this.editCalendarEvent.get('repeatEveryCount').setValue(1);
    }
    if (value === 'Daily') {
      this.editCalendarEvent.get('repeatEveryType').setValue('Day');
    }
    if (value === 'Weekly') {
      this.editCalendarEvent.get('repeatEveryType').setValue('Week');
    }
    if (value === 'Monthly') {
      this.editCalendarEvent.get('repeatEveryType').setValue('Month');
    }
    if (value === 'Yearly') {
      this.editCalendarEvent.get('repeatEveryType').setValue('Year');
    }
  }

  public onRecurrenceSelect(value): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    this.selectedRecurrence = value;
    this.setFormValue(value);
    if (this.editCalendarEvent.get('repeatEveryCount').value > 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.checkform = this.editCalendarEvent.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day3: any): void => {
        const dayObj3 = day3;
        dayObj3.checked = true;
        dayObj3.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj3.value));
        return dayObj3;
      });
    }
    if (this.editCalendarEvent.get('repeatEveryCount').value > 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.weekDays = this.weekDays.map((day4: any): void => {
        const dayObj4 = day4;
        if (dayObj4.value === 'Monday') {
          dayObj4.checked = true;
        } else {
          dayObj4.checked = false;
        }
        dayObj4.isDisabled = false;
        return dayObj4;
      });
      this.checkform = this.editCalendarEvent.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.editCalendarEvent.get('repeatEveryCount').value === 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.weekDays = this.weekDays.map((day5: any): void => {
        const dayObj5 = day5;
        if (dayObj5.value === 'Monday') {
          dayObj5.checked = true;
        } else {
          dayObj5.checked = false;
        }
        dayObj5.isDisabled = false;
        return dayObj5;
      });
      this.checkform = this.editCalendarEvent.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.editCalendarEvent.get('repeatEveryCount').value === 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.checkform = this.editCalendarEvent.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day6: any): void => {
        const dayObj6 = day6;
        dayObj6.checked = true;
        dayObj6.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj6.value));
        return dayObj6;
      });
    }
    if (
      this.editCalendarEvent.get('repeatEveryCount').value === 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.editCalendarEvent.get('chosenDateOfMonth').setValue(1);
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showMonthlyRecurrence();
    }
    if (
      this.editCalendarEvent.get('repeatEveryCount').value > 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.editCalendarEvent.get('chosenDateOfMonth').setValue(1);
      this.showMonthlyRecurrence();
    }
    this.occurMessage();
    this.showMonthlyRecurrence();
  }

  public numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public close(template: TemplateRef<any>): void {
    if (this.editCalendarEvent.touched && this.editCalendarEvent.dirty) {
      this.openConfirmationModalPopup(template);
    } else {
      this.resetForm('yes');
    }
  }

  public openConfirmationModalPopup(template): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public resetForm(action): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.modalRef.hide();
      this.submitted = false;
      this.formSubmitted = false;
      this.selectedRecurrence = 'Does Not Repeat';
      this.editCalendarEvent.reset();
    }
  }

  public sortWeekDays(data: any[]): any {
    const order = {
      Sunday: 1,
      Monday: 2,
      Tuesday: 3,
      Wednesday: 4,
      Thursday: 5,
      Friday: 6,
      Saturday: 7,
    };

    if (data.length > 0) {
      return data.sort((a, b): any => order[a] - order[b]);
    }
  }

  public changeMonthlyRecurrence(): void {
    if (this.editCalendarEvent.get('chosenDateOfMonth').value === 1) {
      this.editCalendarEvent
        .get('dateOfMonth')
        .setValue(moment(this.editCalendarEvent.get('fromDate').value).format('DD'));
      this.editCalendarEvent.get('monthlyRepeatType').setValue(null);
    } else if (this.editCalendarEvent.get('chosenDateOfMonth').value === 2) {
      this.editCalendarEvent.get('dateOfMonth').setValue(null);
      this.editCalendarEvent.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
    } else if (this.editCalendarEvent.get('chosenDateOfMonth').value === 3) {
      this.editCalendarEvent.get('dateOfMonth').setValue(null);
      this.editCalendarEvent.get('monthlyRepeatType').setValue(this.monthlyLastDayOfWeek);
    }
    this.updateFormValidation();
    this.showMonthlyRecurrence();
    this.occurMessage();
  }

  public showMonthlyRecurrence(): void {
    if (this.editCalendarEvent.get('fromDate').value) {
      const startDate = moment(this.editCalendarEvent.get('fromDate').value).format('YYYY-MM');
      const chosenDay = moment(this.editCalendarEvent.get('fromDate').value).format('dddd');
      this.monthlyDate = moment(this.editCalendarEvent.get('fromDate').value).format('DD');
      const day = moment(startDate, 'YYYY-MM').startOf('month').day(chosenDay);
      const getAllDays = [];
      if (day.date() > 7) day.add(7, 'd');
      const month = day.month();
      while (month === day.month()) {
        getAllDays.push(day.format('YYYY-MM-DD'));
        day.add(7, 'd');
      }
      let week;
      let extraOption;
      this.enableOption = false;
      getAllDays.forEach((element, i): void => {
        if (
          moment(this.editCalendarEvent.get('fromDate').value).format('YYYY-MM-DD')
          === moment(element).format('YYYY-MM-DD')
        ) {
          const number = i + 1;
          if (number === 1) {
            week = 'First';
          }
          if (number === 2) {
            week = 'Second';
          }
          if (number === 3) {
            week = 'Third';
          }
          if (number === 4) {
            this.enableOption = true;
            extraOption = 'Last';
            week = 'Fourth';
          }
          if (number === 5) {
            week = 'Last';
          }
          if (number === 6) {
            week = 'Last';
          }
        }
      });
      this.monthlyDayOfWeek = `${week} ${chosenDay}`;
      this.monthlyLastDayOfWeek = `${extraOption} ${chosenDay}`;
      this.occurMessage();
    }
  }

  public submitPayload() {
          const equimentIds = this.editCalendarEvent.value.EquipmentId.map((item: { id: any }) => item.id);
      const gateIds = this.editCalendarEvent.value.GateId.map((item: { id: any }) => item.id);
      const locationIds = this.editCalendarEvent.value.LocationId.map((item: { id: any }) => item.id);


    const payload = {
      description: this.editCalendarEvent.value.description,
      fromDate: moment(this.editCalendarEvent.value.fromDate).format('YYYY MM DD 00:00:00'),
      toDate: moment(this.editCalendarEvent.value.toDate).format('YYYY MM DD 00:00:00'),
      startTime: moment(this.editCalendarEvent.value.startTime).format('HH:mm'),
      endTime: moment(this.editCalendarEvent.value.endTime).format('HH:mm'),
      TimeZoneId: this.timezonevalues,
      isAllDay: this.editCalendarEvent.value.isAllDay,
      GateId: gateIds,
      LocationId: locationIds,
      EquipmentId: equimentIds,
      isApplicableToDelivery: this.editCalendarEvent.value.isApplicableToDelivery,
      isApplicableToCrane: this.editCalendarEvent.value.isApplicableToCrane,
      isApplicableToConcrete: this.editCalendarEvent.value.isApplicableToConcrete,
      isApplicableToInspection: this.editCalendarEvent.value.isApplicableToInspection,
      recurrence: this.editCalendarEvent.value.recurrence,
      days: this.editCalendarEvent.value.days
        ? this.sortWeekDays(this.editCalendarEvent.value.days)
        : [],
      repeatEveryType: this.editCalendarEvent.value.repeatEveryType
        ? this.editCalendarEvent.value.repeatEveryType
        : null,
      repeatEveryCount: this.editCalendarEvent.value.repeatEveryCount
        ? this.editCalendarEvent.value.repeatEveryCount.toString()
        : null,
      chosenDateOfMonth: this.editCalendarEvent.value.chosenDateOfMonth === 1,
      dateOfMonth: this.editCalendarEvent.value.dateOfMonth,
      monthlyRepeatType: this.editCalendarEvent.value.monthlyRepeatType,
      createdBy: this.authUser.RoleId,
      endDate: moment(this.editCalendarEvent.value.toDate).format('YYYY MM DD 00:00:00'),
    };

    if (payload.isAllDay) {
      payload.startTime = '00:00';
      payload.endTime = '00:00';
    }

    if (payload.recurrence === 'Monthly' || payload.recurrence === 'Yearly') {
      payload.dateOfMonth = this.monthlyDate;
    }
    if (
      payload.recurrence === 'Monthly'
      || payload.recurrence === 'Yearly'
      || payload.recurrence === 'Weekly'
    ) {
      payload.endDate = moment(this.editCalendarEvent.value.endDate).format(
        'YYYY MM DD 00:00:00',
      );
    }

    return payload;
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;

    if (!this.validateForm()) {
      this.formSubmitted = false;
      return;
    }

    const formValue = this.editCalendarEvent.value;
    const newtimezoneDetails = formValue.TimeZoneId;

    if (
      newtimezoneDetails !== null &&
      newtimezoneDetails.length > 0 &&
      formValue.TimeZoneId.length > 0
    ) {
      formValue.TimeZoneId.forEach((element: { id: any }): void => {
        this.timezonevalues = element.id;
      });
    }

    const payload = this.submitPayload();
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };

    this.calendarService.editCalendarEvent(this.editEventId, params, payload).subscribe({
      next: (response): void => {
        if (response) {
          this.modalRef.hide();
          this.toastr.success(response.message, 'Success');
          this.selectedRecurrence = 'Does Not Repeat';
          this.calendarService.updateCalendarEvents(true);
          this.submitted = false;
          this.formSubmitted = false;
          this.editCalendarEvent.reset();
          this.editEventId = '';
        }
      },
      error: (editCalendarEventError): void => {
        if (editCalendarEventError.message?.statusCode === 400) {
          this.showError(editCalendarEventError);
        } else if (!editCalendarEventError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(editCalendarEventError.message, 'OOPS!');
        }
        this.submitted = false;
        this.formSubmitted = false;
      },
    });
  }

  public validateForm(): boolean {
    if (!this.validateBasicFields()) return false;
    if (!this.validateDateFields()) return false;
    return true;
  }

  public validateBasicFields(): boolean {
    if (this.editCalendarEvent.invalid) return false;

    const formValue = this.editCalendarEvent.value;

    if (
      !formValue.isApplicableToDelivery
      && !formValue.isApplicableToCrane
      && !formValue.isApplicableToConcrete
      && !formValue.isApplicableToInspection
    ) return false;

    if (!formValue.isAllDay) {
      const start = new Date(formValue.startTime).getTime();
      const end = new Date(formValue.endTime).getTime();

      if (start === end) {
        this.toastr.error('Start time and End time cannot be the same');
        return false;
      }
      if (end < start) {
        this.toastr.error('End time must be greater than Start time');
        return false;
      }
    }

    return true;
  }

  public validateDateFields(): boolean {
    const formValue = this.editCalendarEvent.value;
    const today = moment().startOf('day');
    const fromDate = formValue.fromDate ? moment(formValue.fromDate).startOf('day') : null;
    const toDate = formValue.toDate ? moment(formValue.toDate).startOf('day') : null;
    const endDate = formValue.endDate ? moment(formValue.endDate).startOf('day') : null;

    if (fromDate && toDate) {
      if (fromDate.isAfter(toDate)) {
        this.toastr.error('End Date must be greater than Start Date.!');
        return false;
      }
      if (!fromDate.isAfter(today) || !toDate.isAfter(today)) {
        this.toastr.error('Please select Future Date.!');
        return false;
      }
    }

    if (
      formValue.recurrence !== 'Daily'
      && formValue.recurrence !== 'Does Not Repeat'
      && fromDate
      && endDate
    ) {
      if (fromDate.isAfter(endDate)) {
        this.toastr.error('End Date must be greater than Start Date.!');
        return false;
      }
      if (!fromDate.isAfter(today) || !endDate.isAfter(today)) {
        this.toastr.error('Please select Future Date.!');
        return false;
      }
    }

    return true;
  }



  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public resetTimer(): void {
    this.startTime = new Date();
    this.startTime.setHours(0);
    this.startTime.setMinutes(0);
    this.endTime = new Date();
    this.endTime.setHours(0);
    this.endTime.setMinutes(0);
    this.editCalendarEvent.get('startTime').setValue(this.startTime);
    this.editCalendarEvent.get('endTime').setValue(this.endTime);
  }

  public ediCalendarForm(): void {
    this.editCalendarEvent = this.formBuilder.group({
      id: ['', Validators.compose([Validators.required])],
      description: ['', Validators.compose([Validators.required])],
      fromDate: ['', Validators.compose([Validators.required])],
      toDate: ['', Validators.compose([Validators.required])],
      endDate: [''],
      isAllDay: [false, Validators.compose([Validators.required])],
      isApplicableToDelivery: [false, Validators.compose([Validators.required])],
      isApplicableToCrane: [false, Validators.compose([Validators.required])],
      isApplicableToConcrete: [false, Validators.compose([Validators.required])],
      isApplicableToInspection: [false, Validators.compose([Validators.required])],
      recurrence: ['', Validators.compose([Validators.required])],
      repeatEveryCount: [''],
      repeatEveryType: [''],
      days: new UntypedFormArray([]),
      startTime: [''],
      endTime: [''],
      chosenDateOfMonth: [false, ''],
      dateOfMonth: [''],
      monthlyRepeatType: [''],
      TimeZoneId: ['', Validators.compose([Validators.required])],
      GateId: [''],
      LocationId: [''],
      EquipmentId: [''],
    });
    this.formControlValueChanged();
    this.setCurrentTiming();
    const startTime = this.editCalendarEvent.get('startTime');
    const endTime = this.editCalendarEvent.get('endTime');
    if (this.editCalendarEvent.get('isAllDay').value === false) {
      startTime.setValidators([Validators.required]);
      endTime.setValidators([Validators.required]);
    } else if (this.editCalendarEvent.get('isAllDay').value === true) {
      startTime.clearValidators();
      endTime.clearValidators();
    }
    startTime.updateValueAndValidity();
    endTime.updateValueAndValidity();
  }

  public setCurrentTiming(): void {
    const newDate = moment().format('MM/DD/YYYY');
    const hours = moment(new Date()).format('HH');
    this.startTime = new Date();
    this.startTime.setHours(+hours);
    this.startTime.setMinutes(0);
    this.endTime = new Date();
    this.endTime.setHours(+hours);
    this.endTime.setMinutes(30);
    this.editCalendarEvent.get('startTime').setValue(this.startTime);
    this.editCalendarEvent.get('endTime').setValue(this.endTime);
    if (!this.editCalendarEvent.get('fromDate')?.value) {
      this.editCalendarEvent.get('fromDate').setValue(newDate);
    }
    if (!this.editCalendarEvent.get('toDate')?.value) {
      this.editCalendarEvent.get('toDate').setValue(newDate);
    }
    if (!this.editCalendarEvent.get('endDate')?.value) {
      this.editCalendarEvent.get('endDate').setValue(newDate);
    }
  }

  public updateFormValidation(): void {
    const chosenDateOfMonth = this.editCalendarEvent.get('chosenDateOfMonth');
    const dateOfMonth = this.editCalendarEvent.get('dateOfMonth');
    const monthlyRepeatType = this.editCalendarEvent.get('monthlyRepeatType');
    if (this.editCalendarEvent.get('chosenDateOfMonth').value === 1) {
      dateOfMonth.setValidators([Validators.required]);
      monthlyRepeatType.clearValidators();
    } else {
      monthlyRepeatType.setValidators([Validators.required]);
      dateOfMonth.clearValidators();
    }
    chosenDateOfMonth.updateValueAndValidity();
    dateOfMonth.updateValueAndValidity();
    monthlyRepeatType.updateValueAndValidity();
  }

  public formControlValueChanged(): void {
    const startTime = this.editCalendarEvent.get('startTime');
    const endTime = this.editCalendarEvent.get('endTime');
    this.editCalendarEvent.get('isAllDay').valueChanges.subscribe((value: boolean): void => {
      if (value === false) {
        startTime.setValidators([Validators.required]);
        endTime.setValidators([Validators.required]);
      } else if (value === true) {
        startTime.clearValidators();
        endTime.clearValidators();
      }
      startTime.updateValueAndValidity();
      endTime.updateValueAndValidity();
    });
    this.editCalendarEvent.get('repeatEveryType').valueChanges.subscribe((value: string): void => {
      const days = this.editCalendarEvent.get('days');
      const chosenDateOfMonth = this.editCalendarEvent.get('chosenDateOfMonth');
      const dateOfMonth = this.editCalendarEvent.get('dateOfMonth');
      const monthlyRepeatType = this.editCalendarEvent.get('monthlyRepeatType');
      if (value === 'Week' || value === 'Day' || value === 'Weeks') {
        days.setValidators([Validators.required]);
      } else {
        days.clearValidators();
      }
      if (value === 'Month' || value === 'Months' || value === 'Year' || value === 'Years') {
        if (this.editCalendarEvent.get('chosenDateOfMonth').value === 1) {
          dateOfMonth.setValidators([Validators.required]);
          monthlyRepeatType.clearValidators();
        } else {
          monthlyRepeatType.setValidators([Validators.required]);
          dateOfMonth.clearValidators();
        }
      } else {
        chosenDateOfMonth.clearValidators();
        dateOfMonth.clearValidators();
        monthlyRepeatType.clearValidators();
      }
      chosenDateOfMonth.updateValueAndValidity();
      dateOfMonth.updateValueAndValidity();
      monthlyRepeatType.updateValueAndValidity();
      days.updateValueAndValidity();
    });
  }


  public locationSelected(data: { id: string | number }): void {
    const getChosenLocation = this.locationList.find((obj: any): any => +obj.id === +data.id);
    if (getChosenLocation) {
      this.editCalendarEvent.get('GateId').setValue('');
      this.editCalendarEvent
        .get('EquipmentId')
        .setValue('');
      this.gateList = []
      this.equipmentList = []
      if(getChosenLocation.gateDetails){
        this.gateList = getChosenLocation.gateDetails;
        this.equipmentList = [this.noEquipmentOption, ...getChosenLocation.EquipmentId];
        this.timeZone = getChosenLocation.TimeZoneId[0].location
      }
    }
    this.selectedLocationId = getChosenLocation?.id;
  }

  public onChange(event): void {
    this.checkform = this.editCalendarEvent.get('days') as UntypedFormArray;
    this.valueExists = this.checkform.controls.filter(
      (object): any => object.value === event.target.value,
    );
    if (event.target.checked) {
      this.checkform.push(new UntypedFormControl(event.target.value));
      this.weekDays = this.weekDays.map((day7: any): void => {
        const dayObj7 = day7;
        if (day7.value === event.target.value) {
          dayObj7.checked = true;
        }
        return dayObj7;
      });
      if (this.checkform.controls.length === 2) {
        this.weekDays = this.weekDays.map((day8: any): void => {
          const dayObj8 = day8;
          dayObj8.isDisabled = false;
          return dayObj8;
        });
      }
    } else if (this.selectedRecurrence === 'Weekly') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day9: any): void => {
            const dayObj9 = day9;
            if (dayObj9.value === event.target.value) {
              dayObj9.checked = false;
            }
            return dayObj9;
          });
        }
        if (this.checkform.controls.length === 1) {
          this.weekDays = this.weekDays.map((day10: any): void => {
            const dayObj10 = day10;
            if (dayObj10.value === this.checkform.controls[0].value) {
              dayObj10.isDisabled = true;
              dayObj10.checked = true;
            }
            return dayObj10;
          });
          return;
        }
        i += 1;
      });
    } else if (this.selectedRecurrence === 'Daily') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day): void => {
            const dayObj = day;
            if (dayObj.value === event.target.value) {
              dayObj.checked = false;
              dayObj.isDisabled = false;
            }
            return dayObj;
          });
          return;
        }
        i += 1;
      });
    }
    if (this.checkform.controls.length !== 7) {
      this.editCalendarEvent.get('recurrence').setValue('Weekly');
      if (+this.editCalendarEvent.get('repeatEveryCount').value === 1) {
        this.editCalendarEvent.get('repeatEveryType').setValue('Week');
      } else {
        this.editCalendarEvent.get('repeatEveryType').setValue('Weeks');
      }
      this.selectedRecurrence = this.editCalendarEvent.get('recurrence').value;
    }
    if (this.checkform.controls.length === 7) {
      this.editCalendarEvent.get('recurrence').setValue('Daily');
      if (+this.editCalendarEvent.get('repeatEveryCount').value === 1) {
        this.editCalendarEvent.get('repeatEveryType').setValue('Day');
      } else {
        this.editCalendarEvent.get('repeatEveryType').setValue('Days');
      }
      this.selectedRecurrence = this.editCalendarEvent.get('recurrence').value;
    }
    this.occurMessage();
    this.showMonthlyRecurrence();
  }

  public changeRecurrenceCount(value): void {
    const recurrencedata = this.editCalendarEvent.get('recurrence').value;
    const count = +value;

    if (count <= 0) {
      this.editCalendarEvent.get('repeatEveryCount').setValue(1);
      return;
    }

    this.selectedRecurrence = recurrencedata;

    // Set recurrence flags
    this.isRepeatWithSingleRecurrence = count === 1;
    this.isRepeatWithMultipleRecurrence = count > 1;
    this.showRecurrenceTypeDropdown = recurrencedata === 'Daily' && count > 1;

    let repeatEveryType = '';

    switch (recurrencedata) {
      case 'Daily':
        repeatEveryType = count === 1 ? 'Day' : 'Days';
        break;
      case 'Weekly':
        repeatEveryType = count === 1 ? 'Week' : 'Weeks';
        break;
      case 'Monthly':
        repeatEveryType = count === 1 ? 'Month' : 'Months';
        this.changeMonthlyRecurrence();
        this.showMonthlyRecurrence();
        break;
      case 'Yearly':
        repeatEveryType = count === 1 ? 'Year' : 'Years';
        break;
      default:
        break;
    }

    this.editCalendarEvent.get('repeatEveryType').setValue(repeatEveryType);
    this.occurMessage();
  }


  public occurMessage(): void {
    this.message = '';
    if (this.editCalendarEvent.get('repeatEveryType').value === 'Day') {
      this.message = 'Occurs every day';
    }
    if (this.editCalendarEvent.get('repeatEveryType').value === 'Days') {
      if (+this.editCalendarEvent.get('repeatEveryCount').value === 2) {
        this.message = 'Occurs every other day';
      } else {
        this.message = `Occurs every ${this.editCalendarEvent.get('repeatEveryCount').value} days`;
      }
    }
    if (this.editCalendarEvent.get('repeatEveryType').value === 'Week') {
      let weekDays = '';
      this.weekDays.map((dayObj3: any): any => {
        if (dayObj3.checked) {
          weekDays = `${weekDays + dayObj3.value},`;
        }
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message += `Occurs every ${weekDays}`;
    }
    if (this.editCalendarEvent.get('repeatEveryType').value === 'Weeks') {
      let weekDays = '';
      this.weekDays.map((dayObj4: any): any => {
        if (dayObj4.checked) {
          weekDays = `${weekDays + dayObj4.value},`;
        }
        return false;
      });
      if (+this.editCalendarEvent.get('repeatEveryCount').value === 2) {
        this.message = `Occurs every other  ${weekDays}`;
      } else {
        this.message = `Occurs every ${
          this.editCalendarEvent.get('repeatEveryCount').value
        } weeks on ${weekDays}`;
      }
      weekDays = weekDays.replace(/,\s*$/, '');
    }

    this.repeatTypeMessage();

    if (this.message && this.editCalendarEvent.get('recurrence').value === 'Daily') {
      this.message += ` until ${moment(this.editCalendarEvent.get('toDate').value).format(
        'MMMM DD, YYYY',
      )}`;
    } else if (
      this.message
      && (this.editCalendarEvent.get('recurrence').value === 'Monthly'
        || this.editCalendarEvent.get('recurrence').value === 'Weekly'
        || this.editCalendarEvent.get('recurrence').value === 'Yearly')
    ) {
      this.message += ` until ${moment(this.editCalendarEvent.get('endDate').value).format(
        'MMMM DD, YYYY',
      )}`;
    }
  }

  public repeatTypeMessage() {
    if (
      this.editCalendarEvent.get('repeatEveryType').value === 'Month'
      || this.editCalendarEvent.get('repeatEveryType').value === 'Months'
      || this.editCalendarEvent.get('repeatEveryType').value === 'Year'
      || this.editCalendarEvent.get('repeatEveryType').value === 'Years'
    ) {
      if (this.editCalendarEvent.get('chosenDateOfMonth').value === 1) {
        this.message = `Occurs on day ${this.monthlyDate}`;
      } else if (this.editCalendarEvent.get('chosenDateOfMonth').value === 2) {
        this.message = `Occurs on the ${this.monthlyDayOfWeek}`;
      } else {
        this.message = `Occurs on the ${this.monthlyLastDayOfWeek}`;
      }
    }
  }
   public checkEquipmentType(value: any[]): void {
    let hasNoEquipmentOption = false;
    let hasOtherEquipment = false;
    if (value) {
      if(value.length == this.equipmentList.length -1 && this.equipmentList[0].id == 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
      }
      if(value.length != this.equipmentList.length && this.equipmentList[0].id != 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
      }
      if(value.length == 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
      }
      // Check if "No Equipment Needed" (id = 0) is selected
      hasNoEquipmentOption = value.some((item: any) => item.id === 0);

      // Check if other equipment is selected
      hasOtherEquipment = value.some((item: any) => item.id !== 0);

      const previousSelection = this.editCalendarEvent.get('EquipmentId').value || [];
      const previousHasOther = previousSelection.some((item: any) => item.id !== 0);

      // Rule 1: If "No Equipment Needed" is selected and other items are selected, keep only "No Equipment Needed"
      if (hasNoEquipmentOption && hasOtherEquipment && !previousHasOther) {
        this.toastr.warning('When "No Equipment Needed" is selected, other equipment options cannot be selected.', 'Warning');
        const noEquipmentOnly = value.filter((item: any) => item.id === 0);
        this.editCalendarEvent.get('EquipmentId').setValue(noEquipmentOnly);
        value = noEquipmentOnly;
        hasOtherEquipment = false;
      }

      // Rule 2: If other equipment is already selected and "No Equipment Needed" is now selected, remove it
      if (previousHasOther && hasNoEquipmentOption) {
        this.toastr.warning('When other equipment is selected, "No Equipment Needed" cannot be selected.', 'Warning');
        const filteredSelection = value.filter((item: any) => item.id !== 0);
        this.editCalendarEvent.get('EquipmentId').setValue(filteredSelection);
        value = filteredSelection;
        hasNoEquipmentOption = false;
      }
    }

  }
}
