<div>
<form name="form" class="custom-material-form project-detail-form" novalidate *ngIf="(projectSharingSettings && projectSharingSettings?.isPublicWebsiteEnabled) && !mainLoader && (projectList?.status=='' || this.projectList?.status == null)">
  <div class="row m-0">
    <div class="row m-0 bg-dark-orange w-100">
      <div class="col-md-12">
        <div class="top-logo">
          <a href="https://www.folloit.com/" target="_blank" rel="noopener">
            <img
              src="./assets/images/logo.svg"
              width="100"
              class="c-pointer auth-logo project-detail-logo py-4"
              alt="Follo"
          /></a>
        </div>
      </div>
    </div>
    <div class="row mt-4 mx-1">
      <div class="col-md-2 col-5 mb-2">
        <div class="form-group">
          <label class="fw700 color-light-grey9 mb-0"  for="proName">Project Name</label>
          <p>
            {{ projectList?.projectName }}
          </p>
        </div>
      </div>
      <div class="col-md-2 col-7 mb-2">
        <div class="form-group">
          <label class="fs14 fw700 color-light-grey9 mb-0" for="proAdd">Project Address</label>
          <p>
            {{ projectList?.projectLocation }}
          </p>
        </div>
      </div>
    </div>
    <div class="view-logistic-button" *ngIf="projectSharingSettings?.shareprojectLogisticPlan && projectSharingSettings?.isPdfUploaded">
      <button
        type="submit"
        class="btn btn-orange color-orange radius20 fs13 fw-bold cairo-regular btn-block"
        (click)="downloadFile()"
      >
        View Site Plan
      </button>
    </div>
    <div class="col-md-12 mb-2 guestuser-projectdetail">
      <div class="form-group">
        <label class="fw700 color-light-grey9" for="location">Location</label>
        <google-map class="agm-map project-map" [options]="mapOptions" id="location">
          <map-marker [position]="marker.position" [options]="marker.options"></map-marker>
        </google-map>
      </div>
    </div>
  </div>

  <!-- Booking card view -->
  <div class="row m-0">
    <div
      class="col-md-6 col-6 "
      *ngIf="viewDeliveryCalendar || viewConcreteCalendar || viewCraneCalendar">
      <div class="card eye-cursor"
      >
        <div class="card-body text-center project-detail-card" (click)="navigateCalendar()"  (keydown)="handleDownKeydown($event, 'calendar')">
          <img
            class=""
            src="../../../../assets/images/view-calendar.svg"
            alt="viewcalendar"
            height="{20}"
            width="{20}"
          />
          <p class="mt-2 fw700 fs14">View Calendar</p>
        </div>
      </div>
    </div>
    <div class="col-md-6 col-6" *ngIf="addDeliveryBooking" (click)="navigateDeliveryBooking()"  (keydown)="handleDownKeydown($event, 'delivery')">
      <div class="card eye-cursor">
        <div class="card-body text-center project-detail-card">
          <img
            class=""
            src="../../../../assets/images/book-delivery.svg"
            alt="bookdelivery"
            height="{20}"
            width="{20}"
          />
          <p class="mt-2 fw700 fs14">Book Delivery</p>
        </div>
      </div>
    </div>
    <div *ngIf="addCraneBooking" class="col-md-6 col-6"
    [ngClass]="{
        'mt-0': craneBookTop ,
        'mt-4': !craneBookTop
      }"
      (click)="navigateCraneBooking()"  (keydown)="handleDownKeydown($event, 'crane')">
        <div class="card eye-cursor">
          <div class="card-body text-center project-detail-card">
            <img
              class=""
              src="../../../../assets/images/book-crane.svg"
              alt="viewreport"
              height="{20}"
              width="{20}"
            />
            <p class="mt-2 fw700 fs14">Book Crane</p>
          </div>
        </div>
      </div>
      <div *ngIf="addConcreteBooking" class="col-md-6  col-6"
      [ngClass]="{
        'mt-0': concreteBookTop ,
        'mt-4': !concreteBookTop
      }"
      (click)="navigateConcreteBooking()" (keydown)="handleDownKeydown($event, 'concrete')">
        <div class="card eye-cursor">
          <div class="card-body text-center project-detail-card">
            <img
              class=""
              src="../../../../assets/images/book-concrete.svg"
              alt="bookconcrete"
              height="{20}"
              width="{20}"
            />
            <p class="mt-2 fw700 fs14">Book Concrete</p>
          </div>
        </div>
      </div>
  </div>

  <!-- Footer -->
  <div class="guest-page-footer">
    <p class="fs16 fw500 text-center color-orange">Powered by follo.co</p>
  </div>
</form>

<!-- Below Block if project public website is not enabled -->
<div>
<div class="container fs18 cairo-regular fw500 text-black" *ngIf="(!projectSharingSettings?.isPublicWebsiteEnabled) && (this.projectList?.status=='' || this.projectList?.status == null )&& !mainLoader">
  <div class="row justify-content-center align-items-center min-vh-100">
      <div class="col-md-6">
          <div class="text-center">
            <img src="../../../../assets/images/noun_Alert.svg" alt="Alert"/>
            <p class="mt-2">This website is temporarily unavailable. We are actively working on bringing you updates. We will be back in operation as soon as possible!</p>
          </div>
      </div>
  </div>
</div>

<!-- Below Block if project is expired -->
<div>
<div class="container fs18 cairo-regular fw500 text-black" *ngIf="(this.projectList?.status !== '' && this.projectList?.status !== null) && !mainLoader">
  <div class="row justify-content-center align-items-center min-vh-100">
      <div class="col-md-6">
          <div class="text-center">
            <img src="../../../../assets/images/noun_Alert.svg" alt="Alert"/>
            <p class="mt-2">This Project was Expired please contact Project Administrator!</p>
          </div>
      </div>
  </div>
</div>
</div>
