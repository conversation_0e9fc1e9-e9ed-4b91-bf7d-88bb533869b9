import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { HttpClientModule } from '@angular/common/http';
import { RouterTestingModule } from '@angular/router/testing';
import { AuthService } from '../../services/auth/auth.service';
import { MainLayoutComponent } from './main-layout.component';
import { HeaderComponent } from '../header/header.component';

describe('MainLayoutComponent', () => {
  let component: MainLayoutComponent;
  let fixture: ComponentFixture<MainLayoutComponent>;

  const mockAuthService = {
    contentLogout: jest.fn(),
    loggeduserIn: jest.fn(),
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [MainLayoutComponent, HeaderComponent],
      imports: [RouterTestingModule, HttpClientModule],
      providers: [
        { provide: AuthService, useValue: mockAuthService },
      ],
      // This allows us to ignore child components
      // we don't want to test
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(MainLayoutComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should render the main layout structure', () => {
    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('.main-layout')).toBeTruthy();
    expect(compiled.querySelector('.main-content')).toBeTruthy();
  });

  it('should contain router-outlet', () => {
    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('router-outlet')).toBeTruthy();
  });

  it('should contain app-header component', () => {
    const compiled = fixture.nativeElement;
    expect(compiled.querySelector('app-header')).toBeTruthy();
  });
});
