import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HeatmapComponent } from './heat-map.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, ReactiveFormsModule, FormsModule, NgControl, FormControl } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { ReportsService } from '../../services/reports/reports.service';
import { ProjectService } from '../../services/profile/project.service';
import { of, throwError } from 'rxjs';
import { Pipe, PipeTransform } from '@angular/core';
import * as moment from 'moment';
import * as _ from 'lodash';
import { SchedulerFormComponent } from '../scheduler-form/scheduler-form.component';
import { SaveReportFormComponent } from '../save-report-form/save-report-form.component';

@Pipe({
  name: 'paginate'
})
class MockPaginatePipe implements PipeTransform {
  transform(value: any[], config: any): any[] {
    return value;
  }
}

// Mock NgControl
class MockNgControl extends NgControl {
  control = new FormControl();
  viewToModelUpdate() {}
}

describe('HeatmapComponent', () => {
  let component: HeatmapComponent;
  let fixture: ComponentFixture<HeatmapComponent>;
  let modalService: BsModalService;
  let reportsService: ReportsService;
  let projectService: ProjectService;
  let toastrService: ToastrService;
  let mockModalRef: Partial<BsModalRef>;

  const mockProjectData = {
    ProjectId: 1,
    ParentCompanyId: 1
  };

  const mockHeatMapData = {
    data: {
      result: {
        '2024-01-01': {
          timeslots: {
            '01:00': 1,
            '02:00': 2
          },
          totalCount: 3
        }
      },
      count: 1
    }
  };

  const mockMemberList = {
    data: [
      {
        id: 1,
        User: {
          email: '<EMAIL>'
        }
      }
    ]
  };

  beforeEach(async () => {
    // Mock window.getComputedStyle
    Object.defineProperty(window, 'getComputedStyle', {
      value: () => ({
        getPropertyValue: () => ''
      })
    });

    mockModalRef = {
      hide: jest.fn()
    };

    await TestBed.configureTestingModule({
      declarations: [HeatmapComponent, MockPaginatePipe],
      imports: [ReactiveFormsModule, FormsModule],
      providers: [
        BsModalService,
        UntypedFormBuilder,
        {
          provide: ReportsService,
          useValue: {
            heatMapReports: jest.fn().mockReturnValue(of(mockHeatMapData)),
            exportHeatMap: jest.fn().mockReturnValue(of({})),
            saveAsExcelFile: jest.fn(),
            exportHeatMapReports: jest.fn()
          }
        },
        {
          provide: ProjectService,
          useValue: {
            projectParent: of(mockProjectData),
            listAllMember: jest.fn().mockReturnValue(of(mockMemberList)),
            listEquipment: jest.fn(),
            getDefinableWork: jest.fn(),
            getCompanies: jest.fn(),
            getMembers: jest.fn(),
            getGates: jest.fn(),
            getLocations: jest.fn()
          }
        },
        {
          provide: ToastrService,
          useValue: {
            success: jest.fn(),
            error: jest.fn()
          }
        },
        {
          provide: NgControl,
          useClass: MockNgControl
        }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(HeatmapComponent);
    component = fixture.componentInstance;
    modalService = TestBed.inject(BsModalService);
    reportsService = TestBed.inject(ReportsService);
    projectService = TestBed.inject(ProjectService);
    toastrService = TestBed.inject(ToastrService);

    // Set up modalRef
    component.modalRef = mockModalRef as BsModalRef;

    // Initialize form
    component.filterDetailsForm();
    component.filterForm.get('templateType').setValue([{ id: 0, name: 'Delivery' }]);

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.pageSize).toBe(10);
    expect(component.currentPageNo).toBe(1);
    expect(component.pageNo).toBe(1);
    expect(component.sortOrder).toBe('asc');
    expect(component.loader).toBe(false);
  });

  it('should handle project data subscription', () => {
    expect(component.ProjectId).toBe(mockProjectData.ProjectId);
    expect(component.ParentCompanyId).toBe(mockProjectData.ParentCompanyId);
  });

  describe('Pagination', () => {
    it('should change page size', () => {
      const newPageSize = 20;
      jest.spyOn(component, 'heatMapReports');
      component.changePageSize(newPageSize);
      expect(component.pageSize).toBe(newPageSize);
      expect(component.pageNo).toBe(1);
      expect(component.heatMapReports).toHaveBeenCalled();
    });

    it('should change page number', () => {
      const newPageNo = 2;
      jest.spyOn(component, 'heatMapReports');
      component.changePageNo(newPageNo);
      expect(component.pageNo).toBe(newPageNo);
      expect(component.heatMapReports).toHaveBeenCalled();
    });
  });

  describe('Filter Form', () => {
    it('should reset filter form', () => {
      // Set up initial filter count and form values
      component.filterCount = 3;
      component.filterForm.patchValue({
        startTime: '10:00',
        templateType: [{ id: 0, name: 'Delivery' }],
        dateFilter: ['2024-01-01', '2024-01-02']
      });

      // Mock defaultFilterSettings to not increment filterCount
      jest.spyOn(component, 'defaultFilterSettings').mockImplementation(() => {
        component.filterForm.get('templateType').setValue(component.defaultRequestType);
      });

      jest.spyOn(component, 'heatMapReports');
      component.resetFilter();

      expect(mockModalRef.hide).toHaveBeenCalled();
      expect(component.filterCount).toBe(0);
      expect(component.heatMapReports).toHaveBeenCalled();
    });

    it('should validate number only input', () => {
      const validEvent = { which: 48 }; // '0'
      const invalidEvent = { which: 65 }; // 'A'
      expect(component.numberOnly(validEvent)).toBe(true);
      expect(component.numberOnly(invalidEvent)).toBe(false);
    });
  });

  describe('Data Fetching', () => {
    it('should fetch heat map reports', () => {
      component.heatMapReports();
      expect(reportsService.heatMapReports).toHaveBeenCalled();

      const expectedData = [{
        date: '2024-01-01',
        timeslots: [1, 2],
        totalCount: 3
      }];

      expect(component.heatMapReportList).toEqual(expectedData);
      expect(component.totalListCount).toBe(1);
    });

    it('should handle error when fetching heat map reports', () => {
      const error = new Error('API Error');
      jest.spyOn(reportsService, 'heatMapReports').mockReturnValue(throwError(() => error));
      jest.spyOn(toastrService, 'error');

      // Mock hasSelectedTemplateType to return true
      jest.spyOn(component, 'hasSelectedTemplateType').mockReturnValue(true);

      // Mock buildParams and buildPayload to return valid values
      jest.spyOn(component, 'buildParams').mockReturnValue({
        ProjectId: 1,
        pageSize: 10,
        pageNo: 1,
        void: 0,
        sortOrder: 'asc'
      });
      jest.spyOn(component, 'buildPayload').mockReturnValue({
        templateType: [{ id: 0, name: 'Delivery' }]
      });

      // Mock isSingleDayWithInvalidTime to return false
      jest.spyOn(component, 'isSingleDayWithInvalidTime').mockReturnValue(false);

      component.heatMapReports();

      expect(component.loader).toBe(false);
      expect(toastrService.error).toHaveBeenCalled();
    });

    it('should fetch member list', () => {
      component.getMembers();

      expect(projectService.listAllMember).toHaveBeenCalledWith({
        ProjectId: mockProjectData.ProjectId,
        ParentCompanyId: mockProjectData.ParentCompanyId
      });

      expect(component.updatedMemberList).toEqual([{
        id: 1,
        User: { email: '<EMAIL>' },
        UserEmail: '<EMAIL>'
      }]);
    });
  });

  describe('Modal Operations', () => {
    it('should open modal', () => {
      const template = {} as any;
      jest.spyOn(modalService, 'show').mockReturnValue(mockModalRef as BsModalRef);
      component.openModal(template);
      expect(modalService.show).toHaveBeenCalledWith(template, {
        class: 'heatmap-modal'
      });
    });

    it('should open export modal', () => {
      const template = {} as any;
      jest.spyOn(modalService, 'show').mockReturnValue(mockModalRef as BsModalRef);
      component.openModalExport(template);
      expect(modalService.show).toHaveBeenCalledWith(template, {
        class: 'reportexport-modal radius20'
      });
    });
  });

  describe('Utility Methods', () => {
    it('should format date correctly', () => {
      const date = new Date('2024-01-01');
      expect(component.formatDate(date)).toBe('2024-01-01');
    });

    it('should format time correctly', () => {
      const time = new Date();
      time.setHours(14);
      time.setMinutes(30);
      time.setSeconds(0);

      const result = component.formatTime(time);
      expect(result).toBe('14:30:00');
    });

    it('should get opacity based on value', () => {
      // Set up test data
      component.heatMapReportList = [{
        date: '2024-01-01',
        timeslots: [1, 2],
        totalCount: 3
      }];

      expect(component.getOpacity(1)).toBe('rgba(244, 94, 40, 0.5)');
      expect(component.getOpacity(2)).toBe('rgba(244, 94, 40, 1)');
      expect(component.getOpacity(0)).toBe('#e3e2e3');
    });
  });

  describe('Export Functionality', () => {
    it('should export heat map as Excel', () => {
      // Setup export form
      component.exportForm = new UntypedFormBuilder().group({
        reportName: ['Test Report'],
        reportType: ['EXCEL']
      });

      // Mock methods
      jest.spyOn(component, 'buildParams').mockReturnValue({ ProjectId: 1 });
      jest.spyOn(component, 'createHeatMapPayload').mockReturnValue({
        templateType: [{ id: 0, name: 'Delivery' }],
        startDate: '2024-01-01',
        endDate: '2024-01-02'
      });

      // Call the method
      component.payloadExportService(
        { ProjectId: 1 },
        {
          exportType: 'EXCEL',
          reportName: 'Test Report',
          templateType: [{ id: 0, name: 'Delivery' }],
          startDate: '2024-01-01',
          endDate: '2024-01-02'
        }
      );

      // Verify the service was called
      expect(reportsService.exportHeatMap).toHaveBeenCalled();
      expect(reportsService.saveAsExcelFile).toHaveBeenCalled();
      expect(toastrService.success).toHaveBeenCalledWith('Heat Map exported successfully');
    });
  });

  describe('Sorting', () => {
    it('should toggle sort order and refresh data', () => {
      // Initial sort order is 'asc'
      expect(component.sortOrder).toBe('asc');

      // Mock heatMapReports
      jest.spyOn(component, 'heatMapReports');

      // Call sort
      component.sortData();

      // Should toggle to 'desc'
      expect(component.sortOrder).toBe('desc');
      expect(component.heatMapReports).toHaveBeenCalled();

      // Call sort again
      component.sortData();

      // Should toggle back to 'asc'
      expect(component.sortOrder).toBe('asc');
      expect(component.heatMapReports).toHaveBeenCalledTimes(2);
    });
  });

  describe('Keyboard Event Handling', () => {
    it('should handle toggle keydown with Enter key', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'sortData');

      component.handleToggleKeydown(event);

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.sortData).toHaveBeenCalled();
    });

    it('should handle toggle keydown with Space key', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'sortData');

      component.handleToggleKeydown(event);

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.sortData).toHaveBeenCalled();
    });

    it('should not handle toggle keydown with other keys', () => {
      const event = new KeyboardEvent('keydown', { key: 'Tab' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'sortData');

      component.handleToggleKeydown(event);

      expect(event.preventDefault).not.toHaveBeenCalled();
      expect(component.sortData).not.toHaveBeenCalled();
    });
  });

  describe('ngAfterViewInit', () => {
    it('should subscribe to project data and initialize data', () => {
      jest.spyOn(component, 'getMembers');
      jest.spyOn(component, 'getCompaniesForNdrGrid');
      jest.spyOn(component, 'getOverAllGateForNdrGrid');

      component.ngAfterViewInit();

      expect(component.getMembers).toHaveBeenCalled();
      expect(component.getCompaniesForNdrGrid).toHaveBeenCalled();
      expect(component.getOverAllGateForNdrGrid).toHaveBeenCalled();
    });

    it('should handle undefined project data in ngAfterViewInit', () => {
      // Mock projectService to return undefined
      const originalProjectParent = projectService.projectParent;
      Object.defineProperty(projectService, 'projectParent', {
        get: () => of(undefined),
        configurable: true
      });
      jest.spyOn(component, 'getMembers');

      component.ngAfterViewInit();

      expect(component.getMembers).not.toHaveBeenCalled();

      // Restore original
      Object.defineProperty(projectService, 'projectParent', {
        get: () => originalProjectParent,
        configurable: true
      });
    });
  });

  describe('Modal Operations - Extended', () => {
    it('should open save modal with correct configuration', () => {
      const mockSaveModalRef = {
        content: {
          ProjectId: undefined,
          reportType: undefined,
          updatedMemberList: undefined,
          exportType: undefined,
          filterPayload: undefined,
          pageSize: undefined,
          pageNo: undefined,
          sortOrder: undefined,
          void: undefined
        }
      };
      jest.spyOn(modalService, 'show').mockReturnValue(mockSaveModalRef as any);

      component.openModalSave();

      expect(modalService.show).toHaveBeenCalledWith(SaveReportFormComponent, { class: 'modal-save' });
      expect(mockSaveModalRef.content.ProjectId).toBe(component.ProjectId);
      expect(mockSaveModalRef.content.reportType).toBe('Heat Map');
    });

    it('should open schedule popup with correct configuration', () => {
      const mockScheduleModalRef = {
        content: {
          ProjectId: undefined,
          ParentCompanyId: undefined,
          reportType: undefined,
          updatedMemberList: undefined,
          exportType: undefined,
          filterPayload: undefined,
          sort: undefined
        }
      };
      jest.spyOn(modalService, 'show').mockReturnValue(mockScheduleModalRef as any);

      component.openSchedulePopup();

      expect(modalService.show).toHaveBeenCalledWith(SchedulerFormComponent);
      expect(mockScheduleModalRef.content.ProjectId).toBe(component.ProjectId);
      expect(mockScheduleModalRef.content.reportType).toBe('Heat Map');
    });
  });

  describe('Filter Submit', () => {
    it('should count filters correctly and submit', () => {
      component.filterForm.patchValue({
        dateFilter: ['2024-01-01', '2024-01-02'],
        companyFilter: '1',
        memberFilter: '2',
        gateFilter: '3',
        equipmentFilter: '4',
        statusFilter: 'Approved',
        defineFilter: '5',
        startTime: '08:00',
        templateType: [{ id: 0, name: 'Delivery' }],
        locationFilter: '6'
      });

      jest.spyOn(component, 'heatMapReports');
      component.filterSubmit();

      expect(component.filterCount).toBe(10); // All filters are set
      expect(component.heatMapReports).toHaveBeenCalled();
      expect(mockModalRef.hide).toHaveBeenCalled();
      expect(component.formSubmitted).toBe(false);
    });

    it('should handle empty filters in filterSubmit', () => {
      component.filterForm.patchValue({
        dateFilter: '',
        companyFilter: '',
        memberFilter: '',
        gateFilter: '',
        equipmentFilter: '',
        statusFilter: '',
        defineFilter: '',
        startTime: '',
        templateType: '',
        locationFilter: ''
      });

      jest.spyOn(component, 'heatMapReports');
      component.filterSubmit();

      expect(component.filterCount).toBe(0);
      expect(component.heatMapReports).toHaveBeenCalled();
    });
  });

  describe('Data Fetching - Extended', () => {
    it('should handle error when fetching members', () => {
      const error = new Error('Member API Error');
      jest.spyOn(projectService, 'listAllMember').mockReturnValue(throwError(() => error));

      component.getMembers();

      expect(projectService.listAllMember).toHaveBeenCalled();
    });

    it('should handle empty member response', () => {
      jest.spyOn(projectService, 'listAllMember').mockReturnValue(of({ data: [] }));

      component.getMembers();

      expect(component.updatedMemberList).toEqual([]);
    });

    it('should fetch equipment list successfully', () => {
      const mockEquipmentResponse = {
        data: [{ id: 1, name: 'Equipment 1' }]
      };
      jest.spyOn(projectService, 'listEquipment').mockReturnValue(of(mockEquipmentResponse));
      jest.spyOn(component, 'getDefinableForNdrGrid');

      component.getOverAllEquipmentForNdrGrid();

      expect(projectService.listEquipment).toHaveBeenCalledWith(
        {
          ProjectId: component.ProjectId,
          pageSize: 0,
          pageNo: 0,
          ParentCompanyId: component.ParentCompanyId,
        },
        {
          isFilter: true,
          showActivatedAlone: true,
        }
      );
      expect(component.equipmentList).toEqual(mockEquipmentResponse.data);
      expect(component.getDefinableForNdrGrid).toHaveBeenCalled();
    });

    it('should fetch definable work successfully', () => {
      const mockDefinableResponse = {
        data: [{ id: 1, name: 'Work 1' }]
      };
      jest.spyOn(projectService, 'getDefinableWork').mockReturnValue(of(mockDefinableResponse));
      jest.spyOn(component, 'getCompaniesForNdrGrid');

      component.getDefinableForNdrGrid();

      expect(projectService.getDefinableWork).toHaveBeenCalledWith({
        ProjectId: component.ProjectId,
        ParentCompanyId: component.ParentCompanyId,
      });
      expect(component.defineList).toEqual(mockDefinableResponse.data);
      expect(component.getCompaniesForNdrGrid).toHaveBeenCalled();
    });

    it('should handle null response in getDefinableForNdrGrid', () => {
      jest.spyOn(projectService, 'getDefinableWork').mockReturnValue(of(null));
      jest.spyOn(component, 'getCompaniesForNdrGrid');

      component.getDefinableForNdrGrid();

      expect(component.getCompaniesForNdrGrid).not.toHaveBeenCalled();
    });

    it('should fetch companies successfully', () => {
      const mockCompaniesResponse = {
        data: [{ id: 1, name: 'Company 1' }]
      };
      jest.spyOn(projectService, 'getCompanies').mockReturnValue(of(mockCompaniesResponse));
      jest.spyOn(component, 'getLocationsForNdrGrid');

      component.getCompaniesForNdrGrid();

      expect(projectService.getCompanies).toHaveBeenCalledWith({
        ProjectId: component.ProjectId,
        ParentCompanyId: component.ParentCompanyId,
      });
      expect(component.companyList).toEqual(mockCompaniesResponse.data);
      expect(component.getLocationsForNdrGrid).toHaveBeenCalled();
    });

    it('should handle null response in getCompaniesForNdrGrid', () => {
      jest.spyOn(projectService, 'getCompanies').mockReturnValue(of(null));
      jest.spyOn(component, 'getLocationsForNdrGrid');

      component.getCompaniesForNdrGrid();

      expect(component.getLocationsForNdrGrid).not.toHaveBeenCalled();
    });

    it('should fetch gates successfully', () => {
      const mockGatesResponse = {
        data: [{ id: 1, name: 'Gate 1' }]
      };
      jest.spyOn(projectService, 'gateList').mockReturnValue(of(mockGatesResponse));
      jest.spyOn(component, 'getOverAllEquipmentForNdrGrid');

      component.getOverAllGateForNdrGrid();

      expect(projectService.gateList).toHaveBeenCalledWith(
        {
          ProjectId: component.ProjectId,
          pageSize: 0,
          pageNo: 0,
          ParentCompanyId: component.ParentCompanyId,
        },
        { isFilter: true, showActivatedAlone: true }
      );
      expect(component.gateList).toEqual(mockGatesResponse.data);
      expect(component.getOverAllEquipmentForNdrGrid).toHaveBeenCalled();
    });

    it('should fetch locations successfully', () => {
      const mockLocationsResponse = {
        data: [{ id: 1, name: 'Location 1' }]
      };
      jest.spyOn(projectService, 'getLocations').mockReturnValue(of(mockLocationsResponse));

      component.getLocationsForNdrGrid();

      expect(projectService.getLocations).toHaveBeenCalledWith({
        ProjectId: component.ProjectId,
        ParentCompanyId: component.ParentCompanyId,
      });
      expect(component.locationList).toEqual(mockLocationsResponse.data);
    });

    it('should handle null response in getLocationsForNdrGrid', () => {
      jest.spyOn(projectService, 'getLocations').mockReturnValue(of(null));

      component.getLocationsForNdrGrid();

      expect(component.locationList).toEqual([]);
    });
  });

  describe('Heat Map Reports - Extended', () => {
    it('should handle template type validation error', () => {
      jest.spyOn(component, 'hasSelectedTemplateType').mockReturnValue(false);
      jest.spyOn(component, 'showTemplateTypeError');

      component.heatMapReports();

      expect(component.showTemplateTypeError).toHaveBeenCalled();
    });

    it('should handle single day with invalid time validation', () => {
      jest.spyOn(component, 'hasSelectedTemplateType').mockReturnValue(true);
      jest.spyOn(component, 'buildParams').mockReturnValue({ ProjectId: 1 });
      jest.spyOn(component, 'buildPayload').mockReturnValue({
        startDate: '2024-01-01',
        endDate: '2024-01-01',
        eventStartTime: '10:00',
        eventEndTime: '10:00'
      });
      jest.spyOn(component, 'isSingleDayWithInvalidTime').mockReturnValue(true);

      component.heatMapReports();

      expect(component.loader).toBe(false);
      expect(component.formSubmitted).toBe(false);
    });

    it('should build params correctly', () => {
      component.ProjectId = 123;
      component.pageSize = 25;
      component.pageNo = 2;
      component.sortOrder = 'desc';

      const params = component.buildParams();

      expect(params).toEqual({
        ProjectId: 123,
        pageSize: 25,
        pageNo: 2,
        sortOrder: 'desc',
        void: 0
      });
    });

    it('should build payload correctly with all fields', () => {
      component.filterForm.patchValue({
        companyFilter: '5',
        dateFilter: [new Date('2024-01-01'), new Date('2024-01-02')],
        statusFilter: 'Approved',
        memberFilter: '10',
        gateFilter: '15',
        equipmentFilter: '20',
        templateType: [{ id: 0, name: 'Delivery' }],
        defineFilter: '25',
        startTime: new Date('2024-01-01T08:00:00'),
        endTime: new Date('2024-01-01T18:00:00'),
        locationFilter: '30'
      });
      component.ParentCompanyId = 100;

      const payload = component.buildPayload();

      expect(payload.companyFilter).toBe(5);
      expect(payload.statusFilter).toBe('Approved');
      expect(payload.memberFilter).toBe(10);
      expect(payload.gateFilter).toBe(15);
      expect(payload.equipmentFilter).toBe(20);
      expect(payload.defineFilter).toBe(25);
      expect(payload.locationFilter).toBe(30);
      expect(payload.ParentCompanyId).toBe(100);
      expect(payload.templateType).toEqual([{ id: 0, name: 'Delivery' }]);
    });

    it('should build payload with default values for empty fields', () => {
      component.filterForm.patchValue({
        companyFilter: '',
        statusFilter: '',
        memberFilter: '',
        gateFilter: '',
        equipmentFilter: '',
        templateType: [{ id: 0, name: 'Delivery' }],
        defineFilter: '',
        startTime: null,
        endTime: null,
        locationFilter: ''
      });

      const payload = component.buildPayload();

      expect(payload.companyFilter).toBe(0);
      expect(payload.statusFilter).toBe('');
      expect(payload.memberFilter).toBe(0);
      expect(payload.gateFilter).toBe(0);
      expect(payload.equipmentFilter).toBe(0);
      expect(payload.defineFilter).toBe(0);
      expect(payload.locationFilter).toBe(0);
      expect(payload.startTime).toBe('07:00');
      expect(payload.endTime).toBe('17:00');
    });

    it('should validate single day with same start and end time', () => {
      const payload = {
        startDate: '2024-01-01',
        endDate: '2024-01-01',
        eventStartTime: new Date('2024-01-01T10:00:00'),
        eventEndTime: new Date('2024-01-01T10:00:00')
      };
      jest.spyOn(toastrService, 'error');

      const result = component.isSingleDayWithInvalidTime(payload);

      expect(result).toBe(true);
      expect(toastrService.error).toHaveBeenCalledWith('Time Range Start time and End time should not be the same');
    });

    it('should validate single day with start time greater than end time', () => {
      const payload = {
        startDate: '2024-01-01',
        endDate: '2024-01-01',
        eventStartTime: new Date('2024-01-01T18:00:00'),
        eventEndTime: new Date('2024-01-01T10:00:00')
      };
      jest.spyOn(toastrService, 'error');

      const result = component.isSingleDayWithInvalidTime(payload);

      expect(result).toBe(true);
      expect(toastrService.error).toHaveBeenCalledWith('Please enter Time Range From Time lesser than To Time');
    });

    it('should return false for valid single day time range', () => {
      const payload = {
        startDate: '2024-01-01',
        endDate: '2024-01-01',
        eventStartTime: new Date('2024-01-01T08:00:00'),
        eventEndTime: new Date('2024-01-01T18:00:00')
      };

      const result = component.isSingleDayWithInvalidTime(payload);

      expect(result).toBe(false);
    });

    it('should return false for different dates', () => {
      const payload = {
        startDate: '2024-01-01',
        endDate: '2024-01-02',
        eventStartTime: new Date('2024-01-01T18:00:00'),
        eventEndTime: new Date('2024-01-01T10:00:00')
      };

      const result = component.isSingleDayWithInvalidTime(payload);

      expect(result).toBe(false);
    });
  });

  describe('Close Icon and Form Management', () => {
    it('should handle closeIcon with valid selectedArray', () => {
      component.selectedArray = [{
        templateType: [{ id: 0, name: 'Delivery' }],
        companyFilter: 5,
        defineFilter: 10,
        equipmentFilter: 15,
        gateFilter: 20,
        memberFilter: 25,
        statusFilter: 'Approved',
        eventStartTime: new Date('2024-01-01T08:00:00'),
        eventEndTime: new Date('2024-01-01T18:00:00')
      }];

      jest.spyOn(component, 'setFormValue');
      jest.spyOn(component, 'setConditionalValue');

      component.closeIcon();

      expect(mockModalRef.hide).toHaveBeenCalled();
      expect(component.setFormValue).toHaveBeenCalledWith('templateType', [{ id: 0, name: 'Delivery' }]);
      expect(component.setConditionalValue).toHaveBeenCalledWith('companyFilter', 5);
    });

    it('should handle closeIcon with empty selectedArray', () => {
      component.selectedArray = null;

      component.closeIcon();

      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should set form value correctly', () => {
      const controlName = 'testControl';
      const value = 'testValue';
      jest.spyOn(component.filterForm.get(controlName), 'setValue');

      component.setFormValue(controlName, value);

      expect(component.filterForm.get(controlName).setValue).toHaveBeenCalledWith(value, undefined);
    });

    it('should set form value silently', () => {
      const controlName = 'testControl';
      const value = 'testValue';
      jest.spyOn(component.filterForm.get(controlName), 'setValue');

      component.setFormValue(controlName, value, true);

      expect(component.filterForm.get(controlName).setValue).toHaveBeenCalledWith(value, { emitEvent: false });
    });

    it('should set conditional value for positive numbers', () => {
      const controlName = 'testControl';
      jest.spyOn(component, 'setFormValue');

      component.setConditionalValue(controlName, 5);

      expect(component.setFormValue).toHaveBeenCalledWith(controlName, 5, false);
    });

    it('should set conditional value for zero or negative numbers', () => {
      const controlName = 'testControl';
      jest.spyOn(component, 'setFormValue');

      component.setConditionalValue(controlName, 0);

      expect(component.setFormValue).toHaveBeenCalledWith(controlName, '', true);
    });
  });

  describe('Export Functionality - Extended', () => {
    beforeEach(() => {
      component.exportDetailsForm();
    });

    it('should handle export with invalid form', () => {
      component.exportForm.patchValue({
        reportName: '',
        reportType: ''
      });

      component.export();

      expect(component.exportSubmitted).toBe(false);
    });

    it('should handle export with no template type selected', () => {
      component.exportForm.patchValue({
        reportName: 'Test Report',
        reportType: 'PDF'
      });
      component.filterForm.patchValue({
        templateType: []
      });
      jest.spyOn(toastrService, 'error');

      component.export();

      expect(component.loader).toBe(false);
      expect(component.exportSubmitted).toBe(false);
      expect(toastrService.error).toHaveBeenCalledWith('Please choose any one of the booking type');
    });

    it('should export PDF successfully', () => {
      component.exportForm.patchValue({
        reportName: 'Test PDF Report',
        reportType: 'PDF'
      });
      component.filterForm.patchValue({
        templateType: [{ id: 0, name: 'Delivery' }],
        dateFilter: [new Date('2024-01-01'), new Date('2024-01-02')]
      });

      const mockResponse = {
        data: 'http://example.com/report.pdf'
      };
      jest.spyOn(reportsService, 'exportHeatMap').mockReturnValue(of(mockResponse));
      jest.spyOn(toastrService, 'success');

      // Mock document.createElement and related methods
      const mockLink = {
        setAttribute: jest.fn(),
        click: jest.fn(),
        remove: jest.fn()
      };
      jest.spyOn(document, 'createElement').mockReturnValue(mockLink as any);
      jest.spyOn(document.body, 'appendChild').mockImplementation();

      component.export();

      expect(reportsService.exportHeatMap).toHaveBeenCalled();
      expect(mockLink.setAttribute).toHaveBeenCalledWith('target', '_self');
      expect(mockLink.setAttribute).toHaveBeenCalledWith('href', 'http://example.com/report.pdf');
      expect(mockLink.click).toHaveBeenCalled();
      expect(toastrService.success).toHaveBeenCalledWith('Heat Map exported successfully');
    });

    it('should handle PDF export with no data found', () => {
      component.exportForm.patchValue({
        reportName: 'Test PDF Report',
        reportType: 'PDF'
      });
      component.filterForm.patchValue({
        templateType: [{ id: 0, name: 'Delivery' }]
      });

      const mockResponse = {
        message: 'No data found.'
      };
      jest.spyOn(reportsService, 'exportHeatMap').mockReturnValue(of(mockResponse));
      jest.spyOn(toastrService, 'error');

      component.export();

      expect(toastrService.error).toHaveBeenCalledWith('No data found');
    });

    it('should create heat map payload correctly', () => {
      component.filterForm.patchValue({
        templateType: [{ id: 0, name: 'Delivery' }],
        companyFilter: '5',
        dateFilter: [new Date('2024-01-01'), new Date('2024-01-02')],
        statusFilter: 'Approved',
        memberFilter: '10',
        gateFilter: '15',
        equipmentFilter: '20',
        defineFilter: '25',
        startTime: new Date('2024-01-01T08:00:00'),
        endTime: new Date('2024-01-01T18:00:00')
      });
      component.ParentCompanyId = 100;

      const payload = component.createHeatMapPayload();

      expect(payload).toBeDefined();
      expect(payload.templateType).toEqual([{ id: 0, name: 'Delivery' }]);
      expect(payload.companyFilter).toBe(5);
      expect(payload.ParentCompanyId).toBe(100);
    });

    it('should return null for createHeatMapPayload with no template type', () => {
      component.filterForm.patchValue({
        templateType: []
      });

      const payload = component.createHeatMapPayload();

      expect(payload).toBeNull();
    });

    it('should cancel export correctly', () => {
      component.exportSubmitted = true;
      component.exportForm.patchValue({
        reportName: 'Custom Report',
        reportType: 'EXCEL'
      });

      component.cancelExport();

      expect(mockModalRef.hide).toHaveBeenCalled();
      expect(component.exportSubmitted).toBe(false);
      expect(component.exportForm.get('reportName').value).toBe('Heat Map Report');
      expect(component.exportForm.get('reportType').value).toBe('PDF');
    });
  });

  describe('Default Settings and Initialization', () => {
    it('should set default date and time correctly', () => {
      component.setDefaultDateAndTime();

      expect(component.deliveryStart.getHours()).toBe(7);
      expect(component.deliveryStart.getMinutes()).toBe(0);
      expect(component.deliveryEnd.getHours()).toBe(17);
      expect(component.deliveryEnd.getMinutes()).toBe(0);
    });

    it('should set default date range picker correctly', () => {
      component.setDefaultDateRangePicker();

      const dateFilter = component.filterForm.get('dateFilter').value;
      expect(dateFilter).toHaveLength(2);
      expect(dateFilter[0]).toBeInstanceOf(Date);
      expect(dateFilter[1]).toBeInstanceOf(Date);
    });

    it('should initialize default filter settings', () => {
      component.filterCount = 0;
      component.filterForm.patchValue({
        startTime: '08:00',
        templateType: [{ id: 0, name: 'Delivery' }],
        dateFilter: ['2024-01-01', '2024-01-02']
      });

      component.defaultFilterSettings();

      expect(component.filterForm.get('templateType').value).toEqual(component.defaultRequestType);
      expect(component.filterCount).toBeGreaterThan(0);
    });

    it('should handle hasSelectedTemplateType correctly', () => {
      component.filterForm.patchValue({
        templateType: [{ id: 0, name: 'Delivery' }]
      });

      expect(component.hasSelectedTemplateType()).toBe(true);

      component.filterForm.patchValue({
        templateType: []
      });

      expect(component.hasSelectedTemplateType()).toBe(false);
    });

    it('should show template type error correctly', () => {
      jest.spyOn(toastrService, 'error');

      component.showTemplateTypeError();

      expect(component.formSubmitted).toBe(false);
      expect(component.loader).toBe(false);
      expect(toastrService.error).toHaveBeenCalledWith('Please choose any one of the booking type');
    });
  });

  describe('Utility Methods - Extended', () => {
    it('should format date with null input', () => {
      expect(component.formatDate(null)).toBe('');
    });

    it('should format time with null input', () => {
      expect(component.formatTime(null)).toBe('');
    });

    it('should handle getOpacity with empty heat map list', () => {
      component.heatMapReportList = [];

      const result = component.getOpacity(1);

      expect(result).toBe('#e3e2e3');
    });

    it('should handle number validation with keyCode', () => {
      const eventWithKeyCode = { keyCode: 48, which: undefined }; // '0'
      const invalidEventWithKeyCode = { keyCode: 65, which: undefined }; // 'A'

      expect(component.numberOnly(eventWithKeyCode)).toBe(true);
      expect(component.numberOnly(invalidEventWithKeyCode)).toBe(false);
    });

    it('should handle number validation for special keys', () => {
      const backspaceEvent = { which: 8 }; // Backspace
      const deleteEvent = { which: 46 }; // Delete

      expect(component.numberOnly(backspaceEvent)).toBe(true);
      expect(component.numberOnly(deleteEvent)).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle export error', () => {
      component.exportForm.patchValue({
        reportName: 'Test Report',
        reportType: 'PDF'
      });
      component.filterForm.patchValue({
        templateType: [{ id: 0, name: 'Delivery' }]
      });

      const error = new Error('Export failed');
      jest.spyOn(reportsService, 'exportHeatMap').mockReturnValue(throwError(() => error));

      component.export();

      expect(component.loader).toBe(true); // Loader should be set before the error
    });

    it('should handle getHeatMapReports with null response', () => {
      const params = { ProjectId: 1 };
      const payload = { templateType: [{ id: 0, name: 'Delivery' }] };

      jest.spyOn(reportsService, 'heatMapReports').mockReturnValue(of(null));

      component.getHeatMapReports(params, payload);

      expect(component.loader).toBe(false);
    });

    it('should handle getHeatMapReports with response but no data', () => {
      const params = { ProjectId: 1 };
      const payload = { templateType: [{ id: 0, name: 'Delivery' }] };

      jest.spyOn(reportsService, 'heatMapReports').mockReturnValue(of({ data: null }));

      component.getHeatMapReports(params, payload);

      expect(component.loader).toBe(false);
    });
  });
});
