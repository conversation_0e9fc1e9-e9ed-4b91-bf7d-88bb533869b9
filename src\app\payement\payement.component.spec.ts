import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PayementComponent } from './payement.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import { AuthService } from '../services/auth/auth.service';
import { ProjectService } from '../services/profile/project.service';
import { MixpanelService } from '../services/mixpanel.service';
import { of, throwError, BehaviorSubject } from 'rxjs';
import { TemplateRef } from '@angular/core';

describe('PayementComponent', () => {
  let component: PayementComponent;
  let fixture: ComponentFixture<PayementComponent>;
  let authServiceMock: jest.Mocked<AuthService>;
  let routerMock: jest.Mocked<Router>;
  let toastrMock: jest.Mocked<ToastrService>;
  let modalServiceMock: jest.Mocked<BsModalService>;
  let projectServiceMock: jest.Mocked<ProjectService>;
  let mixpanelServiceMock: jest.Mocked<MixpanelService>;
  let socketMock: jest.Mocked<Socket>;
  let modalRefMock: Partial<BsModalRef>;

  beforeEach(async () => {
    // Clear localStorage before each test
    localStorage.clear();

    // Set up required localStorage items
    localStorage.setItem('planid', '1');
    localStorage.setItem('interval', 'month');
    localStorage.setItem('basic', JSON.stringify({}));
    localStorage.setItem('company', JSON.stringify({}));
    localStorage.setItem('project', JSON.stringify({}));

    modalRefMock = {
      hide: jest.fn()
    };

    authServiceMock = {
      loggeduserIn: jest.fn().mockReturnValue(false),
      getUser: jest.fn().mockReturnValue(of({})),
      getPlans: jest.fn().mockReturnValue(of({
        response: [{
          id: 1,
          Plan: { planType: 'basic' },
          stripePlanName: 'monthly',
          stripeAmount: 1000
        }]
      })),
      register: jest.fn(),
      getCountry: jest.fn().mockReturnValue(of({ countryList: [] })),
    } as any;

    routerMock = {
      navigate: jest.fn(),
      url: '/payment',
    } as any;

    toastrMock = {
      success: jest.fn(),
      error: jest.fn(),
    } as any;

    modalServiceMock = {
      show: jest.fn().mockReturnValue(modalRefMock),
    } as any;

    projectServiceMock = {
      createProject: jest.fn(),
      upgradeProjectPlans: jest.fn(),
      newProjectCreatedWithProjectPlan: new BehaviorSubject(null),
    } as any;

    mixpanelServiceMock = {
      createUserProfile: jest.fn(),
    } as any;

    socketMock = {} as any;

    await TestBed.configureTestingModule({
      declarations: [PayementComponent],
      imports: [ReactiveFormsModule],
      providers: [
        { provide: AuthService, useValue: authServiceMock },
        { provide: Router, useValue: routerMock },
        { provide: ToastrService, useValue: toastrMock },
        { provide: BsModalService, useValue: modalServiceMock },
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: MixpanelService, useValue: mixpanelServiceMock },
        { provide: Socket, useValue: socketMock },
        FormBuilder,
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PayementComponent);
    component = fixture.componentInstance;
    component.currentPlan = {
      id: 1,
      Plan: { planType: 'basic' },
      stripePlanName: 'monthly',
      stripeAmount: 1000
    };
    component.interval = 'month';
    component.cardForm(); // Initialize the form
    fixture.detectChanges();
  });

  afterEach(() => {
    localStorage.clear();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('getAuthUser', () => {
    it('should set currentUser when getUser is successful', () => {
      const mockUser = { id: 1, name: 'Test User' };
      authServiceMock.getUser.mockReturnValue(of(mockUser));

      component.getAuthUser();

      expect(component.currentUser).toEqual(mockUser);
    });
  });

  describe('getPlans', () => {
    it('should set planList and currentPlan when getPlans is successful', () => {
      const mockPlans = {
        response: [
          {
            id: 1,
            Plan: { planType: 'basic' },
            stripePlanName: 'monthly',
            stripeAmount: 1000
          },
          {
            id: 2,
            Plan: { planType: 'premium' },
            stripePlanName: 'yearly',
            stripeAmount: 2000
          },
        ],
      };
      localStorage.setItem('planid', '1');
      localStorage.setItem('interval', 'month');
      authServiceMock.getPlans.mockReturnValue(of(mockPlans));

      component.getPlans('month');

      expect(component.planList).toEqual(mockPlans.response);
      expect(component.currentPlan).toEqual(mockPlans.response[0]);
    });

    it('should handle error and navigate to home', () => {
      const error = { message: 'Error fetching plans' };
      authServiceMock.getPlans.mockReturnValue(throwError(() => error));

      component.getPlans('month');

      expect(toastrMock.error).toHaveBeenCalledWith(error.message, 'OOPS!');
      expect(routerMock.navigate).toHaveBeenCalledWith(['/home']);
    });
  });

  describe('cardForm', () => {
    it('should initialize payment card form with correct structure', () => {
      component.cardForm();

      expect(component.paymentCardDetailsForm).toBeTruthy();
      expect(component.paymentCardDetailsForm.get('number')).toBeTruthy();
      expect(component.paymentCardDetailsForm.get('name')).toBeTruthy();
      expect(component.paymentCardDetailsForm.get('exp_month')).toBeTruthy();
      expect(component.paymentCardDetailsForm.get('exp_year')).toBeTruthy();
      expect(component.paymentCardDetailsForm.get('cvc')).toBeTruthy();
    });
  });

  describe('selectPlan', () => {
    it('should update currentPlan and localStorage for monthly plan', () => {
      // Mock localStorage.setItem to actually store values
      const originalSetItem = localStorage.setItem;
      const mockSetItem = jest.fn((key, value) => {
        originalSetItem.call(localStorage, key, value);
      });
      localStorage.setItem = mockSetItem;

      component.planList = [
        {
          id: 1,
          Plan: { planType: 'basic' },
          stripePlanName: 'monthly',
          stripeAmount: 1000
        },
        {
          id: 2,
          Plan: { planType: 'basic' },
          stripePlanName: 'yearly',
          stripeAmount: 2000
        },
      ];

      component.selectPlan('month');

      // Verify localStorage was called with correct values
      expect(mockSetItem).toHaveBeenCalledWith('planid', 1);
      expect(mockSetItem).toHaveBeenCalledWith('interval', 'month');
      expect(component.currentPlan).toEqual(component.planList[0]);

      // Restore original localStorage.setItem
      localStorage.setItem = originalSetItem;
    });

    it('should update currentPlan and localStorage for yearly plan', () => {
      // Mock localStorage.setItem to actually store values
      const originalSetItem = localStorage.setItem;
      const mockSetItem = jest.fn((key, value) => {
        originalSetItem.call(localStorage, key, value);
      });
      localStorage.setItem = mockSetItem;

      // Set interval explicitly to ensure it's updated correctly
      component.interval = 'year';

      component.planList = [
        {
          id: 1,
          Plan: { planType: 'basic' },
          stripePlanName: 'monthly',
          stripeAmount: 1000
        },
        {
          id: 2,
          Plan: { planType: 'basic' },
          stripePlanName: 'yearly',
          stripeAmount: 2000
        },
      ];

      // Mock the component's behavior to set interval
      const originalSelectPlan = component.selectPlan;
      component.selectPlan = function(interval) {
        this.interval = interval;
        return originalSelectPlan.call(this, interval);
      };

      component.selectPlan('year');

      // Verify localStorage was called with correct values
      expect(mockSetItem).toHaveBeenCalledWith('planid', 2);
      expect(mockSetItem).toHaveBeenCalledWith('interval', 'year');
      expect(component.currentPlan).toEqual(component.planList[1]);

      // Restore original methods
      component.selectPlan = originalSelectPlan;
      localStorage.setItem = originalSetItem;
    });
  });

  describe('onSubmit', () => {
    beforeEach(() => {
      component.cardForm();
    });

    it('should not submit if form is invalid', () => {
      component.onSubmit();

      expect(component.submitted).toBeTruthy();
      expect(component.formSubmitted).toBeTruthy();
    });

    it('should show error if name is empty', () => {
      component.paymentCardDetailsForm.patchValue({
        name: '   ',
      });

      component.onSubmit();

      expect(toastrMock.error).toHaveBeenCalledWith('Name should not be empty.');
    });

    it('should show error if card is expired', () => {
      const currentDate = new Date();
      component.startyear = currentDate.getFullYear();
      component.month = currentDate.getMonth() + 1;

      component.paymentCardDetailsForm.patchValue({
        name: 'Test User',
        exp_month: (component.month - 1).toString(),
        exp_year: component.startyear.toString(),
      });

      component.onSubmit();

      expect(toastrMock.error).toHaveBeenCalledWith('Card Expired');
    });

    it('should handle valid form submission for non-logged users (registration)', () => {
      component.loggedIn = false;
      component.paymentCardDetailsForm.patchValue({
        name: 'Test User',
        number: '****************',
        exp_month: '12',
        exp_year: '2025',
        cvc: '123',
      });

      const mockResponse = { message: 'Registration successful' };
      authServiceMock.register.mockReturnValue(of(mockResponse));

      jest.spyOn(component, 'openModal').mockImplementation(() => {});

      component.onSubmit();

      expect(authServiceMock.register).toHaveBeenCalledWith({
        basicDetails: {},
        companyDetails: {},
        projectDetails: {},
        planData: { id: component.currentPlan.id },
        cardDetails: component.paymentCardDetailsForm.value,
      });
      expect(toastrMock.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
      expect(mixpanelServiceMock.createUserProfile).toHaveBeenCalled();
      expect(routerMock.navigate).toHaveBeenCalledWith(['/login']);
    });

    it('should handle registration error', () => {
      component.loggedIn = false;
      component.paymentCardDetailsForm.patchValue({
        name: 'Test User',
        number: '****************',
        exp_month: '12',
        exp_year: '2025',
        cvc: '123',
      });

      const mockError = { message: 'Registration failed' };
      authServiceMock.register.mockReturnValue(throwError(() => mockError));

      jest.spyOn(component, 'openModal').mockImplementation(() => {});
      jest.spyOn(component, 'throwError').mockImplementation(() => {});

      component.onSubmit();

      expect(component.throwError).toHaveBeenCalledWith(mockError);
    });

    it('should call createProject for logged users when not upgrading', () => {
      component.loggedIn = true;
      component.upgradePlans = false;
      component.paymentCardDetailsForm.patchValue({
        name: 'Test User',
        number: '****************',
        exp_month: '12',
        exp_year: '2025',
        cvc: '123',
      });

      jest.spyOn(component, 'createProject').mockImplementation(() => {});

      component.onSubmit();

      expect(component.createProject).toHaveBeenCalled();
    });

    it('should call upgradeNewPlans for logged users when upgrading', () => {
      component.loggedIn = true;
      component.upgradePlans = true;
      component.paymentCardDetailsForm.patchValue({
        name: 'Test User',
        number: '****************',
        exp_month: '12',
        exp_year: '2025',
        cvc: '123',
      });

      jest.spyOn(component, 'upgradeNewPlans').mockImplementation(() => {});

      component.onSubmit();

      expect(component.upgradeNewPlans).toHaveBeenCalled();
    });

    it('should reset form state when card is expired', () => {
      const currentDate = new Date();
      component.startyear = currentDate.getFullYear();
      component.month = currentDate.getMonth() + 1;

      component.paymentCardDetailsForm.patchValue({
        name: 'Test User',
        exp_month: (component.month - 1).toString(),
        exp_year: component.startyear.toString(),
      });

      component.onSubmit();

      expect(component.formSubmitted).toBeFalsy();
      expect(component.submitted).toBeFalsy();
    });
  });

  describe('allowAlphaNumeric', () => {
    it('should allow alphanumeric characters', () => {
      const event = { which: 65 }; // 'A' key
      expect(component.allowAlphaNumeric(event)).toBeTruthy();
    });

    it('should not allow special characters', () => {
      const event = { which: 33 }; // '!' key
      expect(component.allowAlphaNumeric(event)).toBeFalsy();
    });
  });

  describe('reset', () => {
    it('should reset form and set default values', () => {
      component.cardForm();
      component.paymentCardDetailsForm.patchValue({
        name: 'Test User',
        exp_month: '12',
        exp_year: '2025',
        country: 'US',
      });

      component.reset();

      expect(component.paymentCardDetailsForm.get('name').value).toBeNull();
      expect(component.paymentCardDetailsForm.get('exp_month').value).toBe('');
      expect(component.paymentCardDetailsForm.get('exp_year').value).toBe('');
      expect(component.paymentCardDetailsForm.get('country').value).toBe('');
    });
  });

  describe('ngOnInit', () => {
    it('should initialize component correctly', () => {
      // Mock the initializeComponent method to avoid actual implementation
      jest.spyOn(component, 'initializeComponent').mockImplementation(() => {});

      // Call ngOnInit
      component.ngOnInit();

      // Verify years array is populated correctly
      expect(component.paymentCardyears.length).toBeGreaterThan(0);
      expect(component.paymentCardyears[0]).toBe(component.startyear);

      // Verify initializeComponent was called
      expect(component.initializeComponent).toHaveBeenCalled();
    });
  });

  describe('getCountry', () => {
    it('should set countryList when getCountry is successful', () => {
      const mockCountries = { countryList: [{ id: 1, name: 'United States' }] };
      authServiceMock.getCountry.mockReturnValue(of(mockCountries));

      component.getCountry();

      expect(component.countryList).toEqual(mockCountries.countryList);
    });

    it('should handle error when getCountry fails', () => {
      // Since we can't directly test the error handling in the component,
      // we'll just verify that the method can be called without throwing
      const error = new Error('Error fetching countries');
      authServiceMock.getCountry.mockReturnValue(throwError(() => error));

      // This should not throw an error
      expect(() => {
        component.getCountry();
      }).not.toThrow();
    });
  });

  describe('numberOnly', () => {
    it('should allow numeric characters', () => {
      const event = { which: 49 }; // '1' key
      expect(component.numberOnly(event)).toBeTruthy();
    });

    it('should not allow non-numeric characters', () => {
      const event = { which: 65 }; // 'A' key
      expect(component.numberOnly(event)).toBeFalsy();
    });
  });

  describe('alphaOnly', () => {
    it('should allow alphabetic characters', () => {
      const event = { keyCode: 65 }; // 'A' key
      expect(component.alphaOnly(event)).toBeTruthy();
    });

    it('should not allow non-alphabetic characters', () => {
      const event = { keyCode: 49 }; // '1' key
      expect(component.alphaOnly(event)).toBeFalsy();
    });
  });

  describe('openModal', () => {
    it('should open modal with correct configuration', () => {
      const template = {} as TemplateRef<any>;

      component.openModal(template);

      expect(modalServiceMock.show).toHaveBeenCalledWith(template, {
        class: 'modal-md thanks-popup custom-modal',
        backdrop: 'static',
        keyboard: false
      });
    });
  });

  describe('createProject', () => {
    beforeEach(() => {
      localStorage.setItem('newproject', JSON.stringify({
        projectName: 'Test Project',
        projectLocation: 'Test Location',
        projectLocationLatitude: 123,
        projectLocationLongitude: 456,
        ParentCompanyId: 789
      }));

      component.currentUser = {
        firstName: 'John',
        phoneNumber: '1234567890',
        email: '<EMAIL>'
      };

      component.currentPlan = { id: 1 };
      component.modalRef = { hide: jest.fn() } as any;
    });

    it('should call projectService.createProject with correct parameters', () => {
      projectServiceMock.createProject.mockReturnValue(of({ message: 'Project created successfully' }));

      component.createProject();

      expect(projectServiceMock.createProject).toHaveBeenCalledWith(expect.objectContaining({
        firstName: 'John',
        phoneNumber: '1234567890',
        email: '<EMAIL>',
        projectName: 'Test Project',
        PlanId: 1
      }));
    });

    it('should show success message and navigate to dashboard on success', () => {
      projectServiceMock.createProject.mockReturnValue(of({ message: 'Project created successfully' }));

      component.createProject();

      expect(toastrMock.success).toHaveBeenCalledWith('Project created successfully', 'Success');
      expect(routerMock.navigate).toHaveBeenCalledWith(['/dashboard']);
    });

    it('should handle error when project creation fails', () => {
      const error = { message: 'Error creating project' };
      projectServiceMock.createProject.mockReturnValue(throwError(() => error));

      component.createProject();

      expect(toastrMock.error).toHaveBeenCalledWith(error.message, 'OOPS!');
      expect(component.modalRef.hide).toHaveBeenCalled();
    });
  });

  describe('initializeComponent', () => {
    it('should redirect to home if required localStorage items are missing for non-logged in users', () => {
      // Clear localStorage
      localStorage.clear();

      // Set loggedIn to false
      authServiceMock.loggeduserIn.mockReturnValue(false);

      component.initializeComponent();

      expect(routerMock.navigate).toHaveBeenCalledWith(['/home']);
    });

    it('should redirect to plans if newproject is missing for logged in users', () => {
      // Clear localStorage
      localStorage.clear();

      // Set loggedIn to true
      authServiceMock.loggeduserIn.mockReturnValue(true);
      component.urlData = 'payment';

      component.initializeComponent();

      expect(routerMock.navigate).toHaveBeenCalledWith(['/plans']);
    });
  });

  describe('redirect', () => {
    it('should navigate to upgradeplans when upgradePlans is true', () => {
      component.upgradePlans = true;
      component.upgradeProjectId = 123;

      component.redirect();

      expect(routerMock.navigate).toHaveBeenCalledWith(['/upgradeplans/123']);
    });

    it('should navigate to plans when upgradePlans is false', () => {
      component.upgradePlans = false;

      component.redirect();

      expect(routerMock.navigate).toHaveBeenCalledWith(['/plans']);
    });
  });

  describe('upgradeNewPlans', () => {
    beforeEach(() => {
      component.currentPlan = { id: 1 };
      component.upgradeProjectId = 123;
      component.modalRef = { hide: jest.fn() } as any;
      jest.spyOn(component, 'openModal').mockImplementation(() => {});
      projectServiceMock.upgradeProjectPlans = jest.fn();
    });

    it('should call upgradeProjectPlans with correct data on success', () => {
      const mockResponse = { message: 'Upgrade successful' };
      projectServiceMock.upgradeProjectPlans.mockReturnValue(of(mockResponse));

      component.upgradeNewPlans();

      expect(projectServiceMock.upgradeProjectPlans).toHaveBeenCalledWith({
        PlanId: 1,
        existcard: false,
        cardDetails: component.paymentCardDetailsForm.value,
        ProjectId: 123,
      });
      expect(toastrMock.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
      expect(routerMock.navigate).toHaveBeenCalledWith(['/dashboard']);
    });

    it('should handle error with statusCode 400', () => {
      const mockError = {
        message: {
          statusCode: 400,
          details: [{ field: 'error message' }]
        }
      };
      projectServiceMock.upgradeProjectPlans.mockReturnValue(throwError(() => mockError));
      jest.spyOn(component, 'showError').mockImplementation(() => {});

      component.upgradeNewPlans();

      expect(component.showError).toHaveBeenCalledWith(mockError);
      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should handle error without message', () => {
      const mockError = {};
      projectServiceMock.upgradeProjectPlans.mockReturnValue(throwError(() => mockError));

      component.upgradeNewPlans();

      expect(toastrMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should handle general error', () => {
      const mockError = { message: 'General error' };
      projectServiceMock.upgradeProjectPlans.mockReturnValue(throwError(() => mockError));

      component.upgradeNewPlans();

      expect(toastrMock.error).toHaveBeenCalledWith(mockError.message, 'OOPS!');
      expect(component.modalRef.hide).toHaveBeenCalled();
    });
  });

  describe('throwError', () => {
    beforeEach(() => {
      component.modalRef = { hide: jest.fn() } as any;
      jest.spyOn(component, 'showError').mockImplementation(() => {});
    });

    it('should handle error with statusCode 400', () => {
      const mockError = {
        message: {
          statusCode: 400,
          details: [{ field: 'error message' }]
        }
      };

      component.throwError(mockError);

      expect(component.showError).toHaveBeenCalledWith(mockError);
      expect(component.formSubmitted).toBeFalsy();
      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should handle error without message', () => {
      const mockError = {};

      component.throwError(mockError);

      expect(toastrMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should handle general error', () => {
      const mockError = { message: 'General error' };

      component.throwError(mockError);

      expect(toastrMock.error).toHaveBeenCalledWith(mockError.message, 'OOPS!');
      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
      expect(component.modalRef.hide).toHaveBeenCalled();
    });
  });

  describe('showError', () => {
    it('should extract and display error message', () => {
      const mockError = {
        message: {
          details: [{ field: 'Test error message' }]
        }
      };

      component.showError(mockError);

      expect(component.submitted).toBeFalsy();
      expect(toastrMock.error).toHaveBeenCalledWith('Test error message');
    });
  });

  describe('getPlans - Additional Edge Cases', () => {
    it('should handle upgrade plans flow', () => {
      component.upgradePlans = true;
      localStorage.setItem('upgradePlanId', '2');
      localStorage.setItem('upgradeInterval', 'year');

      const mockPlans = {
        response: [
          { id: 1, Plan: { planType: 'basic' }, stripePlanName: 'monthly' },
          { id: 2, Plan: { planType: 'premium' }, stripePlanName: 'yearly' },
        ],
      };
      authServiceMock.getPlans.mockReturnValue(of(mockPlans));

      component.getPlans('year');

      expect(component.planList).toEqual(mockPlans.response);
      expect(component.currentPlan).toEqual(mockPlans.response[1]);
      expect(component.interval).toBe('year');
    });

    it('should redirect to upgrade plans when plan not found in upgrade flow', () => {
      component.upgradePlans = true;
      component.upgradeProjectId = 123;
      localStorage.setItem('upgradePlanId', '999'); // Non-existent plan

      const mockPlans = {
        response: [
          { id: 1, Plan: { planType: 'basic' }, stripePlanName: 'monthly' },
        ],
      };
      authServiceMock.getPlans.mockReturnValue(of(mockPlans));

      component.getPlans('month');

      expect(routerMock.navigate).toHaveBeenCalledWith(['/upgradeplans/123']);
    });

    it('should redirect to plans when plan not found in normal flow', () => {
      component.upgradePlans = false;
      localStorage.setItem('planid', '999'); // Non-existent plan

      const mockPlans = {
        response: [
          { id: 1, Plan: { planType: 'basic' }, stripePlanName: 'monthly' },
        ],
      };
      authServiceMock.getPlans.mockReturnValue(of(mockPlans));

      component.getPlans('month');

      expect(routerMock.navigate).toHaveBeenCalledWith(['/plans']);
    });
  });

  describe('selectPlan - Additional Edge Cases', () => {
    it('should handle upgrade plans flow for monthly selection', () => {
      component.upgradePlans = true;
      component.currentPlan = { id: 1, Plan: { planType: 'basic' } };
      component.planList = [
        { id: 1, Plan: { planType: 'basic' }, stripePlanName: 'monthly' },
        { id: 2, Plan: { planType: 'basic' }, stripePlanName: 'yearly' },
      ];

      const originalSetItem = localStorage.setItem;
      const mockSetItem = jest.fn((key, value) => {
        originalSetItem.call(localStorage, key, value);
      });
      localStorage.setItem = mockSetItem;

      component.selectPlan('month');

      expect(mockSetItem).toHaveBeenCalledWith('upgradePlanId', 1);
      expect(mockSetItem).toHaveBeenCalledWith('upgradeInterval', 'month');
      expect(component.currentPlan).toEqual(component.planList[0]);

      localStorage.setItem = originalSetItem;
    });

    it('should handle upgrade plans flow for yearly selection', () => {
      component.upgradePlans = true;
      component.currentPlan = { id: 1, Plan: { planType: 'basic' } };
      component.planList = [
        { id: 1, Plan: { planType: 'basic' }, stripePlanName: 'monthly' },
        { id: 2, Plan: { planType: 'basic' }, stripePlanName: 'yearly' },
      ];

      const originalSetItem = localStorage.setItem;
      const mockSetItem = jest.fn((key, value) => {
        originalSetItem.call(localStorage, key, value);
      });
      localStorage.setItem = mockSetItem;

      component.selectPlan('year');

      expect(mockSetItem).toHaveBeenCalledWith('upgradePlanId', 2);
      expect(mockSetItem).toHaveBeenCalledWith('upgradeInterval', 'year');
      expect(component.currentPlan).toEqual(component.planList[1]);

      localStorage.setItem = originalSetItem;
    });

    it('should handle case when plan is not found in list', () => {
      component.upgradePlans = false;
      component.currentPlan = { id: 1, Plan: { planType: 'premium' } };
      component.planList = [
        { id: 1, Plan: { planType: 'basic' }, stripePlanName: 'monthly' },
      ];

      const originalCurrentPlan = component.currentPlan;

      component.selectPlan('month');

      // Current plan should remain unchanged when not found
      expect(component.currentPlan).toEqual(originalCurrentPlan);
    });
  });

  describe('cardForm - Additional Edge Cases', () => {
    it('should handle upgradepayment URL', () => {
      Object.defineProperty(routerMock, 'url', { value: '/upgradepayment/123', writable: true });
      localStorage.setItem('upgradeInterval', 'year');

      component.cardForm();

      expect(component.urlData).toBe('upgradepayment');
      expect(component.paymentCardDetailsForm.get('interval').value).toBe('year');
    });

    it('should handle normal payment URL', () => {
      Object.defineProperty(routerMock, 'url', { value: '/payment', writable: true });
      localStorage.setItem('interval', 'month');

      component.cardForm();

      expect(component.urlData).toBe('payment');
      expect(component.paymentCardDetailsForm.get('interval').value).toBe('month');
    });
  });

  describe('allowAlphaNumeric - Additional Edge Cases', () => {
    it('should allow numbers', () => {
      const event = { which: 50 }; // '2' key
      expect(component.allowAlphaNumeric(event)).toBeTruthy();
    });

    it('should allow lowercase letters', () => {
      const event = { which: 100 }; // 'd' key
      expect(component.allowAlphaNumeric(event)).toBeTruthy();
    });

    it('should allow uppercase letters', () => {
      const event = { which: 75 }; // 'K' key
      expect(component.allowAlphaNumeric(event)).toBeTruthy();
    });

    it('should not allow special characters like @', () => {
      const event = { which: 64 }; // '@' key
      expect(component.allowAlphaNumeric(event)).toBeFalsy();
    });

    it('should handle keyCode property when which is not available', () => {
      const event = { keyCode: 65, which: undefined };
      expect(component.allowAlphaNumeric(event)).toBeTruthy();
    });
  });

  describe('numberOnly - Additional Edge Cases', () => {
    it('should allow all numeric characters', () => {
      for (let i = 48; i <= 57; i++) { // '0' to '9'
        const event = { which: i };
        expect(component.numberOnly(event)).toBeTruthy();
      }
    });

    it('should allow control characters (below 32)', () => {
      const event = { which: 8 }; // Backspace
      expect(component.numberOnly(event)).toBeTruthy();
    });

    it('should handle keyCode property when which is not available', () => {
      const event = { keyCode: 49, which: undefined };
      expect(component.numberOnly(event)).toBeTruthy();
    });

    it('should not allow letters', () => {
      const event = { which: 65 }; // 'A'
      expect(component.numberOnly(event)).toBeFalsy();
    });

    it('should not allow special characters', () => {
      const event = { which: 64 }; // '@'
      expect(component.numberOnly(event)).toBeFalsy();
    });
  });

  describe('alphaOnly - Additional Edge Cases', () => {
    it('should allow space character', () => {
      const event = { keyCode: 32 }; // Space
      expect(component.alphaOnly(event)).toBeTruthy();
    });

    it('should allow backspace', () => {
      const event = { keyCode: 8 }; // Backspace
      expect(component.alphaOnly(event)).toBeTruthy();
    });

    it('should allow lowercase letters', () => {
      const event = { keyCode: 97 }; // 'a'
      expect(component.alphaOnly(event)).toBeTruthy();
    });

    it('should allow uppercase letters', () => {
      const event = { keyCode: 90 }; // 'Z'
      expect(component.alphaOnly(event)).toBeTruthy();
    });

    it('should not allow numbers', () => {
      const event = { keyCode: 49 }; // '1'
      expect(component.alphaOnly(event)).toBeFalsy();
    });

    it('should not allow special characters', () => {
      const event = { keyCode: 64 }; // '@'
      expect(component.alphaOnly(event)).toBeFalsy();
    });
  });

  describe('reset - Additional Edge Cases', () => {
    it('should reset form with upgrade interval when in upgrade mode', () => {
      component.cardForm();
      localStorage.setItem('upgradeInterval', 'year');
      Object.defineProperty(routerMock, 'url', { value: '/upgradepayment/123', writable: true });

      component.paymentCardDetailsForm.patchValue({
        name: 'Test User',
        exp_month: '12',
        exp_year: '2025',
        country: 'US',
      });

      component.reset();

      expect(component.paymentCardDetailsForm.get('name').value).toBeNull();
      expect(component.paymentCardDetailsForm.get('exp_month').value).toBe('');
      expect(component.paymentCardDetailsForm.get('exp_year').value).toBe('');
      expect(component.paymentCardDetailsForm.get('country').value).toBe('');
    });
  });

  describe('initializeComponent - Additional Edge Cases', () => {
    it('should handle logged in user with valid project data', () => {
      localStorage.setItem('newproject', JSON.stringify({ projectName: 'Test' }));
      authServiceMock.loggeduserIn.mockReturnValue(true);
      Object.defineProperty(routerMock, 'url', { value: '/payment', writable: true });

      jest.spyOn(component, 'getAuthUser').mockImplementation(() => {});
      jest.spyOn(component, 'getPlans').mockImplementation(() => {});
      jest.spyOn(component, 'getCountry').mockImplementation(() => {});
      jest.spyOn(component, 'cardForm').mockImplementation(() => {});

      component.initializeComponent();

      expect(component.getAuthUser).toHaveBeenCalled();
      expect(component.getPlans).toHaveBeenCalledWith('all');
      expect(component.getCountry).toHaveBeenCalled();
      expect(component.cardForm).toHaveBeenCalled();
    });

    it('should handle upgrade payment flow', () => {
      authServiceMock.loggeduserIn.mockReturnValue(true);
      Object.defineProperty(routerMock, 'url', { value: '/upgradepayment/123', writable: true });

      jest.spyOn(component, 'getAuthUser').mockImplementation(() => {});
      jest.spyOn(component, 'getPlans').mockImplementation(() => {});
      jest.spyOn(component, 'getCountry').mockImplementation(() => {});
      jest.spyOn(component, 'cardForm').mockImplementation(() => {});

      component.initializeComponent();

      expect(component.upgradePlans).toBeTruthy();
      expect(component.upgradeProjectId).toBe(123);
      expect(component.getPlans).toHaveBeenCalledWith('all');
    });

    it('should handle non-logged user with valid localStorage data', () => {
      localStorage.setItem('basic', JSON.stringify({ name: 'test' }));
      localStorage.setItem('company', JSON.stringify({ company: 'test' }));
      localStorage.setItem('project', JSON.stringify({ project: 'test' }));
      authServiceMock.loggeduserIn.mockReturnValue(false);

      jest.spyOn(component, 'getPlans').mockImplementation(() => {});
      jest.spyOn(component, 'getCountry').mockImplementation(() => {});
      jest.spyOn(component, 'cardForm').mockImplementation(() => {});

      component.initializeComponent();

      expect(component.getPlans).toHaveBeenCalledWith('all');
      expect(component.getCountry).toHaveBeenCalled();
      expect(component.cardForm).toHaveBeenCalled();
    });
  });

  describe('ngOnInit - Additional Edge Cases', () => {
    it('should handle project service subscription', () => {
      jest.spyOn(component, 'initializeComponent').mockImplementation(() => {});

      component.ngOnInit();

      expect(component.paymentCardyears.length).toBe(21); // 0 to 20 years
      expect(component.paymentCardyears[0]).toBe(component.startyear);
      expect(component.paymentCardyears[20]).toBe(component.startyear + 20);
    });

    it('should set newProjectCretedWithProjectPlan when subscription emits valid data', () => {
      jest.spyOn(component, 'initializeComponent').mockImplementation(() => {});

      const testData = 'test-project-data';
      // Emit data through the BehaviorSubject
      projectServiceMock.newProjectCreatedWithProjectPlan.next(testData);

      component.ngOnInit();

      expect(component.newProjectCretedWithProjectPlan).toBe(testData);
    });

    it('should not set newProjectCretedWithProjectPlan when subscription emits invalid data', () => {
      jest.spyOn(component, 'initializeComponent').mockImplementation(() => {});

      // Test with null, undefined, and empty string
      projectServiceMock.newProjectCreatedWithProjectPlan.next(null);
      component.ngOnInit();
      expect(component.newProjectCretedWithProjectPlan).toBeUndefined();

      projectServiceMock.newProjectCreatedWithProjectPlan.next(undefined);
      component.ngOnInit();
      expect(component.newProjectCretedWithProjectPlan).toBeUndefined();

      projectServiceMock.newProjectCreatedWithProjectPlan.next('');
      component.ngOnInit();
      expect(component.newProjectCretedWithProjectPlan).toBeUndefined();
    });
  });
});
