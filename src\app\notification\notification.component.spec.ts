import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NotificationComponent } from './notification.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, ReactiveFormsModule, FormsModule, NgControl } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { Socket } from 'ngx-socket-io';
import { Title } from '@angular/platform-browser';
import { DeliveryService } from '../services/profile/delivery.service';
import { ProjectService } from '../services/profile/project.service';
import { of, throwError } from 'rxjs';
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'paginate'
})
class MockPaginatePipe implements PipeTransform {
  transform(value: any[], config: any): any[] {
    return value;
  }
}

describe('NotificationComponent', () => {
  let component: NotificationComponent;
  let fixture: ComponentFixture<NotificationComponent>;
  let modalService: jest.Mocked<BsModalService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let socket: jest.Mocked<Socket>;
  let formBuilder: UntypedFormBuilder;
  let router: jest.Mocked<Router>;
  let titleService: jest.Mocked<Title>;
  let toastr: jest.Mocked<ToastrService>;
  let projectService: jest.Mocked<ProjectService>;

  beforeEach(async () => {
    const modalServiceSpy = {
      show: jest.fn().mockReturnValue({
        content: {
          closeBtnName: 'Close'
        },
        hide: jest.fn(),
        setClass: jest.fn()
      })
    };
    const deliveryServiceSpy = {
      listNotification: jest.fn().mockReturnValue(of({
        data: {
          rows: [{ id: 1, message: 'Test notification' }],
          count: 1
        }
      })),
      setReadNotification: jest.fn().mockReturnValue(of({ count: 5 })),
      deleteNotification: jest.fn().mockReturnValue(of({ message: 'Deleted successfully' })),
      ReadallNotification: jest.fn().mockReturnValue(of({ success: true })),
      notificationRefreshCount: jest.fn(),
      updatedRefreshCount: jest.fn(),
      updatedDeliveryId: jest.fn(),
      createVoid: jest.fn().mockReturnValue(of({ message: 'Void created' })),
      getNDRData: jest.fn().mockReturnValue(of({ data: {} })),
      getNDR: jest.fn(),
      getProject: jest.fn(),
      refresh: { next: jest.fn(), subscribe: jest.fn() },
      refresh1: { next: jest.fn(), subscribe: jest.fn() },
      inspectionUpdated: of(null),
      inspectionUpdated1: of(null),
      fetchData: of(null),
      fetchData1: of(null),
      fetchConcreteData: of(null),
      fetchConcreteData1: of(null),
      loginUser: of({ RoleId: 1 }),
      getCurrentStatus: of('')
    };
    const socketSpy = {
      on: jest.fn()
    };
    const routerSpy = {
      navigate: jest.fn()
    };
    const titleServiceSpy = {
      setTitle: jest.fn()
    };
    const toastrSpy = {
      error: jest.fn(),
      success: jest.fn()
    };
    const projectServiceSpy = {
      getProject: jest.fn().mockReturnValue(of({ data: [] })),
      projectParent: of({ ProjectId: 1, ParentCompanyId: 1 }),
      isAccountAdmin: of(true),
      ParentCompanyId: of(1),
      getProjects: jest.fn().mockReturnValue(of([])),
      clearProject: of({ status: false }),
      isMyAccount: of(''),
      isProject: of(''),
      unReadCount: jest.fn().mockReturnValue(of({ data: 0 })),
      updatedProjectId: jest.fn()
    };

    await TestBed.configureTestingModule({
      declarations: [
        NotificationComponent,
        MockPaginatePipe
      ],
      imports: [
        ReactiveFormsModule,
        FormsModule
      ],
      providers: [
        { provide: BsModalService, useValue: modalServiceSpy },
        { provide: DeliveryService, useValue: deliveryServiceSpy },
        { provide: Socket, useValue: socketSpy },
        { provide: UntypedFormBuilder, useValue: new UntypedFormBuilder() },
        { provide: Router, useValue: routerSpy },
        { provide: Title, useValue: titleServiceSpy },
        { provide: ToastrService, useValue: toastrSpy },
        { provide: ProjectService, useValue: projectServiceSpy },
        { provide: NgControl, useValue: {} }
      ]
    }).compileComponents();

    modalService = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    socket = TestBed.inject(Socket) as jest.Mocked<Socket>;
    formBuilder = TestBed.inject(UntypedFormBuilder);
    router = TestBed.inject(Router) as jest.Mocked<Router>;
    titleService = TestBed.inject(Title) as jest.Mocked<Title>;
    toastr = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(NotificationComponent);
    component = fixture.componentInstance;
    
    // Initialize required properties
    component.notificationList = [];
    component.projectList = [];
    component.loader = false;
    component.currentPageNo = 1;
    component.pageSize = 25;
    component.pageNo = 1;
    component.totalCount = 0;
    component.unReadCount = 0;
    component.ProjectId = 1;
    component.ParentCompanyId = 1;
    component.myAccount = false;
    component.accountAdmin = false;
    component.filterForm = formBuilder.group({
      dateFilter: [''],
      projectNameFilter: [''],
      descriptionFilter: [''],
      statusFilter: ['']
    });

    // Set up modalRef
    component.modalRef = {
      content: {
        closeBtnName: 'Close'
      },
      hide: jest.fn(),
      setClass: jest.fn()
    };

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.currentPageNo).toBe(1);
    expect(component.notificationList).toEqual([]);
    expect(component.loader).toBe(false);
    expect(component.pageSize).toBe(25);
    expect(component.pageNo).toBe(1);
    expect(component.totalCount).toBe(0);
  });

  it('should set title on initialization', () => {
    expect(titleService.setTitle).toHaveBeenCalledWith('Follo - Notifications');
  });

  it('should initialize filter form', () => {
    expect(component.filterForm).toBeTruthy();
    expect(component.filterForm.get('dateFilter')).toBeTruthy();
    expect(component.filterForm.get('projectNameFilter')).toBeTruthy();
    expect(component.filterForm.get('descriptionFilter')).toBeTruthy();
    expect(component.filterForm.get('statusFilter')).toBeTruthy();
  });

  // it('should get notifications successfully', () => {
  //   const mockNotifications = {
  //     data: [{ id: 1, message: 'Test notification' }],
  //     totalCount: 1
  //   };
  //   deliveryService.listNotification.mockReturnValue(of(mockNotifications));

  //   component.getNotification();

  //   expect(deliveryService.listNotification).toHaveBeenCalledWith(
  //     { pageNo: 1, pageSize: 25 },
  //     {}
  //   );
  //   expect(component.notificationList).toEqual(mockNotifications.data);
  //   expect(component.totalCount).toBe(mockNotifications.totalCount);
  //   expect(component.loader).toBe(false);
  // });

  // it('should handle error when getting notifications', () => {
  //   const error = new Error('API Error');
  //   deliveryService.listNotification.mockReturnValue(throwError(() => error));

  //   component.getNotification();

  //   expect(toastr.error).toHaveBeenCalledWith(error.message);
  // });

  // it('should get unread notifications', () => {
  //   const mockUnreadCount = 5;
  //   deliveryService.setReadNotification.mockReturnValue(of({ count: mockUnreadCount }));

  //   component.getUnReadNotification();

  //   expect(deliveryService.setReadNotification).toHaveBeenCalledWith({
  //     ParentCompanyId: component.ParentCompanyId
  //   });
  //   expect(component.unReadCount).toBe(mockUnreadCount);
  // });

  it('should handle filter submission', () => {
    const mockFilterData = {
      dateFilter: '2024-03-20',
      projectNameFilter: 'Test Project',
      descriptionFilter: 'Test Description',
      statusFilter: 'Approved'
    };

    component.filterForm.patchValue(mockFilterData);
    component.filterSubmit();

    expect(component.pageNo).toBe(1);
    expect(deliveryService.listNotification).toHaveBeenCalled();
    expect(component.modalRef.hide).toHaveBeenCalled();
  });

  it('should reset filter form', () => {
    component.filterForm.patchValue({
      dateFilter: '2024-03-20',
      projectNameFilter: 'Test Project',
      descriptionFilter: 'Test Description',
      statusFilter: 'Approved'
    });

    component.resetFilter();

    expect(component.filterForm.get('dateFilter').value).toBe('');
    expect(component.filterForm.get('projectNameFilter').value).toBe('');
    expect(component.filterForm.get('descriptionFilter').value).toBe('');
    expect(component.filterForm.get('statusFilter').value).toBe('');
    expect(component.pageNo).toBe(1);
    expect(deliveryService.listNotification).toHaveBeenCalled();
    expect(component.modalRef.hide).toHaveBeenCalled();
  });

  it('should handle page size change', () => {
    const newPageSize = 50;
    component.changePageSize(newPageSize);

    expect(component.pageSize).toBe(newPageSize);
    expect(deliveryService.listNotification).toHaveBeenCalled();
  });

  it('should handle page number change', () => {
    const newPageNo = 2;
    component.changePageNo(newPageNo);

    expect(component.pageNo).toBe(newPageNo);
    expect(deliveryService.listNotification).toHaveBeenCalled();
  });

  it('should handle keyboard events for toggle', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const data = { DeliveryNotification: [{ seen: false, id: 1 }] };
    jest.spyOn(component, 'openIdModal');

    component.handleToggleKeydown(event, data);

    expect(component.openIdModal).toHaveBeenCalledWith(data);
  });

  it('should handle keyboard events for actions', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const data = { DeliveryNotification: [{ seen: false, id: 1 }] };
    const item = { id: 1 };
    jest.spyOn(component, 'setRead');

    component.handleDownKeydown(event, data, item, 'setRead');

    expect(component.setRead).toHaveBeenCalledWith(data, item);
  });

  // ============ POSITIVE TEST CASES ============

  describe('getNotification', () => {
    it('should get notifications successfully', () => {
      const mockResponse = {
        data: {
          rows: [{ id: 1, message: 'Test notification' }],
          count: 1
        }
      };
      deliveryService.listNotification.mockReturnValue(of(mockResponse));
      component.ProjectId = 1;
      component.ParentCompanyId = 1;

      component.getNotification();

      expect(component.loader).toBe(true);
      expect(deliveryService.listNotification).toHaveBeenCalledWith(
        { pageSize: 25, pageNo: 1 },
        expect.objectContaining({
          search: '',
          ProjectId: 1,
          ParentCompanyId: 1
        })
      );
      expect(component.notificationList).toEqual(mockResponse.data.rows);
      expect(component.totalCount).toBe(mockResponse.data.count);
      expect(component.loader).toBe(false);
    });

    it('should get notifications with filters applied', () => {
      const mockResponse = {
        data: {
          rows: [{ id: 1, message: 'Filtered notification' }],
          count: 1
        }
      };
      deliveryService.listNotification.mockReturnValue(of(mockResponse));
      component.ProjectId = 1;
      component.ParentCompanyId = 1;
      component.filterForm.patchValue({
        dateFilter: '2024-03-20',
        projectNameFilter: '1',
        descriptionFilter: 'test',
        statusFilter: 'Approved'
      });

      component.getNotification();

      expect(deliveryService.listNotification).toHaveBeenCalledWith(
        { pageSize: 25, pageNo: 1 },
        expect.objectContaining({
          descriptionFilter: 'test',
          projectNameFilter: 1,
          dateFilter: new Date('2024-03-20'),
          statusFilter: 'approved',
          search: '',
          ProjectId: 1,
          ParentCompanyId: 1
        })
      );
    });

    it('should get notifications for account admin', () => {
      const mockResponse = {
        data: {
          rows: [{ id: 1, message: 'Admin notification' }],
          count: 1
        }
      };
      deliveryService.listNotification.mockReturnValue(of(mockResponse));
      component.ProjectId = 1;
      component.ParentCompanyId = 1;
      component.accountAdmin = true;

      component.getNotification();

      expect(deliveryService.listNotification).toHaveBeenCalledWith(
        { pageSize: 25, pageNo: 1 },
        expect.objectContaining({
          search: '',
          ParentCompanyId: 1
        })
      );
      expect(component.notificationList).toEqual(mockResponse.data.rows);
    });

    it('should not call API when ProjectId or ParentCompanyId is missing', () => {
      component.ProjectId = null;
      component.ParentCompanyId = 1;

      component.getNotification();

      expect(deliveryService.listNotification).not.toHaveBeenCalled();
    });
  });

  describe('getUnReadNotification', () => {
    it('should get unread notifications for non-myAccount', () => {
      const mockResponse = { data: 5 };
      projectService.unReadCount.mockReturnValue(of(mockResponse));
      component.myAccount = false;
      component.ProjectId = 1;
      component.ParentCompanyId = 1;

      component.getUnReadNotification();

      expect(projectService.unReadCount).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 1
      });
      expect(component.unReadCount).toBe(5);
    });

    it('should get unread notifications for myAccount', () => {
      const mockResponse = { data: 3 };
      projectService.unReadCount.mockReturnValue(of(mockResponse));
      component.myAccount = true;

      component.getUnReadNotification();

      expect(projectService.unReadCount).toHaveBeenCalledWith({});
      expect(component.unReadCount).toBe(3);
    });
  });

  describe('setRead', () => {
    it('should mark notification as read when not seen', () => {
      const mockData = {
        DeliveryNotification: [{ seen: false, id: 1 }]
      };
      const mockResponse = { success: true };
      deliveryService.setReadNotification.mockReturnValue(of(mockResponse));
      component.notificationList = [mockData];
      component.ParentCompanyId = 1;

      component.setRead(mockData, 0);

      expect(deliveryService.setReadNotification).toHaveBeenCalledWith({
        id: 1,
        ParentCompanyId: 1
      });
      expect(component.notificationList[0].DeliveryNotification[0].seen).toBe(true);
      expect(deliveryService.updatedRefreshCount).toHaveBeenCalledWith(true);
    });

    it('should not call API when notification is already seen', () => {
      const mockData = {
        DeliveryNotification: [{ seen: true, id: 1 }]
      };

      component.setRead(mockData, 0);

      expect(deliveryService.setReadNotification).not.toHaveBeenCalled();
    });

    it('should handle missing DeliveryNotification array', () => {
      const mockData = {};

      component.setRead(mockData, 0);

      expect(deliveryService.setReadNotification).not.toHaveBeenCalled();
    });
  });

  describe('delete', () => {
    it('should delete notification successfully', () => {
      const mockResponse = { message: 'Notification deleted successfully' };
      deliveryService.deleteNotification.mockReturnValue(of(mockResponse));
      component.deleteNotification = { id: 1 };
      component.ParentCompanyId = 1;
      component.modalRef = { hide: jest.fn(), setClass: jest.fn() } as any;

      component.delete();

      expect(component.deleteSubmitted).toBe(true);
      expect(deliveryService.deleteNotification).toHaveBeenCalledWith({
        id: 1,
        ParentCompanyId: 1
      });
      expect(component.deleteSubmitted).toBe(false);
      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(toastr.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
      expect(deliveryService.listNotification).toHaveBeenCalled();
    });

    it('should handle delete error with status code 400', () => {
      const error = {
        message: {
          statusCode: 400,
          details: [{ error: 'Validation error' }]
        }
      };
      deliveryService.deleteNotification.mockReturnValue(throwError(() => error));
      component.deleteNotification = { id: 1 };
      component.ParentCompanyId = 1;
      jest.spyOn(component, 'showError');

      component.delete();

      expect(component.showError).toHaveBeenCalledWith(error);
    });

    it('should handle delete error without message', () => {
      const error = {};
      deliveryService.deleteNotification.mockReturnValue(throwError(() => error));
      component.deleteNotification = { id: 1 };
      component.ParentCompanyId = 1;

      component.delete();

      expect(toastr.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should handle delete error with message', () => {
      const error = { message: 'Delete failed' };
      deliveryService.deleteNotification.mockReturnValue(throwError(() => error));
      component.deleteNotification = { id: 1 };
      component.ParentCompanyId = 1;

      component.delete();

      expect(toastr.error).toHaveBeenCalledWith('Delete failed', 'OOPS!');
    });
  });

  describe('markall', () => {
    it('should mark all notifications as read', () => {
      const mockResponse = { success: true };
      deliveryService.ReadallNotification.mockReturnValue(of(mockResponse));
      component.ProjectId = 1;

      component.markall();

      expect(deliveryService.ReadallNotification).toHaveBeenCalledWith({
        ProjectId: 1
      });
      expect(deliveryService.notificationRefreshCount).toHaveBeenCalledWith(true);
      expect(deliveryService.listNotification).toHaveBeenCalled();
      expect(projectService.unReadCount).toHaveBeenCalled();
    });
  });

  describe('openIdModal', () => {
    beforeEach(() => {
      component.ProjectId = 1;
      component.ParentCompanyId = 1;
    });

    it('should open crane request modal', () => {
      const mockItem = {
        requestType: 'craneRequest',
        CraneRequest: { CraneRequestId: 123 }
      };

      component.openIdModal(mockItem);

      expect(modalService.show).toHaveBeenCalledWith(
        expect.any(Function),
        expect.objectContaining({
          backdrop: 'static',
          keyboard: false,
          class: 'modal-lg new-delivery-popup custom-modal',
          initialState: {
            data: {
              id: 123,
              ProjectId: 1,
              ParentCompanyId: 1
            },
            title: 'Modal with component'
          }
        })
      );
    });

    it('should open delivery request modal', () => {
      const mockItem = {
        requestType: 'deliveryRequest',
        DeliveryRequestId: 456
      };

      component.openIdModal(mockItem);

      expect(modalService.show).toHaveBeenCalledWith(
        expect.any(Function),
        expect.objectContaining({
          backdrop: 'static',
          keyboard: false,
          class: 'modal-lg new-delivery-popup custom-modal',
          initialState: {
            data: {
              id: 456,
              ProjectId: 1,
              ParentCompanyId: 1
            },
            title: 'Modal with component'
          }
        })
      );
    });

    it('should open concrete request modal', () => {
      const mockItem = {
        requestType: 'concreteRequest',
        ConcreteRequest: { ConcreteRequestId: 789 }
      };

      component.openIdModal(mockItem);

      expect(modalService.show).toHaveBeenCalledWith(
        expect.any(Function),
        expect.objectContaining({
          backdrop: 'static',
          keyboard: false,
          class: 'modal-lg new-delivery-popup custom-modal',
          initialState: {
            data: {
              id: 789,
              ProjectId: 1,
              ParentCompanyId: 1
            },
            title: 'Modal with component'
          }
        })
      );
    });
  });

  describe('Search and Navigation', () => {
    it('should handle search notification', () => {
      const searchTerm = 'test search';
      component.getSearchNotification(searchTerm);

      expect(component.showSearchbar).toBe(true);
      expect(component.search).toBe(searchTerm);
      expect(component.pageNo).toBe(1);
      expect(deliveryService.listNotification).toHaveBeenCalled();
    });

    it('should hide search bar when search is empty', () => {
      component.getSearchNotification('');

      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
      expect(component.pageNo).toBe(1);
    });

    it('should clear search', () => {
      component.showSearchbar = true;
      component.search = 'test';
      component.pageNo = 2;

      component.clear();

      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
      expect(component.pageNo).toBe(1);
      expect(deliveryService.listNotification).toHaveBeenCalled();
    });

    it('should redirect to delivery request', () => {
      const mockData = {
        Project: { id: 123 }
      };

      component.redirect(mockData);

      expect(projectService.updatedProjectId).toHaveBeenCalledWith(123);
      expect(router.navigate).toHaveBeenCalledWith(['/delivery-request']);
    });
  });

  describe('Modal Operations', () => {
    it('should open modal with template', () => {
      const mockTemplate = {} as any;
      component.openModal(mockTemplate);

      expect(modalService.show).toHaveBeenCalledWith(mockTemplate, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal'
      });
    });

    it('should open filter modal', () => {
      const mockTemplate = {} as any;
      component.openModal1(mockTemplate);

      expect(modalService.show).toHaveBeenCalledWith(mockTemplate, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-sm filter-popup custom-modal'
      });
    });

    it('should close modal', () => {
      component.modalRef = { hide: jest.fn(), setClass: jest.fn() } as any;
      component.close();

      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should set delete content', () => {
      const mockTemplate = {} as any;
      const mockData = { id: 1 };
      jest.spyOn(component, 'openModal');

      component.setDeleteContent(mockTemplate, mockData);

      expect(component.deleteNotification).toEqual(mockData);
      expect(component.openModal).toHaveBeenCalledWith(mockTemplate);
    });
  });

  describe('Utility Methods', () => {
    it('should select status', () => {
      const status = 'Approved';
      component.selectStatus(status);

      expect(component.currentStatus).toBe(status);
    });

    it('should show error message', () => {
      const mockError = {
        message: {
          details: [{ error: 'Test error message' }]
        }
      };

      component.showError(mockError);

      expect(toastr.error).toHaveBeenCalledWith('Test error message');
    });

    it('should get projects', () => {
      const mockResponse = { data: [{ id: 1, name: 'Project 1' }] };
      projectService.getProject.mockReturnValue(of(mockResponse));

      component.getProjects();

      expect(projectService.getProject).toHaveBeenCalled();
      expect(component.projectList).toEqual(mockResponse.data);
    });
  });

  describe('Keyboard Event Handling', () => {
    it('should handle space key for toggle', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      const data = { DeliveryNotification: [{ seen: false, id: 1 }] };
      jest.spyOn(component, 'openIdModal');
      jest.spyOn(event, 'preventDefault');

      component.handleToggleKeydown(event, data);

      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.openIdModal).toHaveBeenCalledWith(data);
    });

    it('should handle different action types in handleDownKeydown', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      const data = { template: 'test' };
      const item = { id: 1 };
      jest.spyOn(component, 'clear');
      jest.spyOn(component, 'openModal1');

      // Test clear action
      component.handleDownKeydown(event, data, item, 'clear');
      expect(component.clear).toHaveBeenCalled();

      // Test open action
      component.handleDownKeydown(event, data, item, 'open');
      expect(component.openModal1).toHaveBeenCalledWith(data);

      // Test default case
      component.handleDownKeydown(event, data, item, 'unknown');
      // Should not throw error
    });

    it('should ignore non-Enter/Space keys', () => {
      const event = new KeyboardEvent('keydown', { key: 'Tab' });
      const data = { DeliveryNotification: [{ seen: false, id: 1 }] };
      jest.spyOn(component, 'openIdModal');

      component.handleToggleKeydown(event, data);

      expect(component.openIdModal).not.toHaveBeenCalled();
    });
  });

  // ============ NEGATIVE TEST CASES ============

  describe('Error Handling', () => {
    it('should handle getNotification API error', () => {
      const error = new Error('API Error');
      deliveryService.listNotification.mockReturnValue(throwError(() => error));
      component.ProjectId = 1;
      component.ParentCompanyId = 1;

      component.getNotification();

      expect(component.loader).toBe(true);
      // Note: The component doesn't have explicit error handling for getNotification
      // This test ensures the error doesn't break the component
    });

    it('should handle getUnReadNotification API error', () => {
      const error = new Error('API Error');
      projectService.unReadCount.mockReturnValue(throwError(() => error));

      component.getUnReadNotification();

      // Should not throw error
      expect(projectService.unReadCount).toHaveBeenCalled();
    });

    it('should handle setRead API error', () => {
      const mockData = {
        DeliveryNotification: [{ seen: false, id: 1 }]
      };
      const error = new Error('API Error');
      deliveryService.setReadNotification.mockReturnValue(throwError(() => error));
      component.notificationList = [mockData];
      component.ParentCompanyId = 1;

      component.setRead(mockData, 0);

      expect(deliveryService.setReadNotification).toHaveBeenCalled();
      // Should not update the notification as seen due to error
      expect(component.notificationList[0].DeliveryNotification[0].seen).toBe(false);
    });

    it('should handle markall API error', () => {
      const error = new Error('API Error');
      deliveryService.ReadallNotification.mockReturnValue(throwError(() => error));
      component.ProjectId = 1;

      component.markall();

      expect(deliveryService.ReadallNotification).toHaveBeenCalled();
      // Should not throw error
    });
  });

  describe('Edge Cases', () => {
    it('should handle undefined filterForm in getNotification', () => {
      component.filterForm = undefined;
      component.ProjectId = 1;
      component.ParentCompanyId = 1;
      const mockResponse = {
        data: {
          rows: [{ id: 1, message: 'Test notification' }],
          count: 1
        }
      };
      deliveryService.listNotification.mockReturnValue(of(mockResponse));

      component.getNotification();

      expect(deliveryService.listNotification).toHaveBeenCalledWith(
        { pageSize: 25, pageNo: 1 },
        expect.objectContaining({
          search: '',
          ProjectId: 1,
          ParentCompanyId: 1
        })
      );
    });

    it('should handle null response in getNotification', () => {
      deliveryService.listNotification.mockReturnValue(of(null));
      component.ProjectId = 1;
      component.ParentCompanyId = 1;

      component.getNotification();

      expect(component.loader).toBe(true);
      // Should not throw error when response is null
    });

    it('should handle missing modalRef in delete', () => {
      const mockResponse = { message: 'Notification deleted successfully' };
      deliveryService.deleteNotification.mockReturnValue(of(mockResponse));
      component.deleteNotification = { id: 1 };
      component.ParentCompanyId = 1;
      component.modalRef = null;

      component.delete();

      expect(deliveryService.deleteNotification).toHaveBeenCalled();
      expect(toastr.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
      // Should not throw error when modalRef is null
    });

    it('should handle empty search string', () => {
      component.getSearchNotification('   ');

      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('   ');
      expect(component.pageNo).toBe(1);
    });

    it('should handle missing Project in redirect', () => {
      const mockData = {};

      expect(() => component.redirect(mockData)).toThrow();
    });
  });

  describe('Form Validation', () => {
    it('should handle filter form with invalid date', () => {
      component.filterForm.patchValue({
        dateFilter: 'invalid-date',
        projectNameFilter: 'invalid-number',
        descriptionFilter: 'test',
        statusFilter: 'Approved'
      });
      component.ProjectId = 1;
      component.ParentCompanyId = 1;

      component.getNotification();

      expect(deliveryService.listNotification).toHaveBeenCalledWith(
        { pageSize: 25, pageNo: 1 },
        expect.objectContaining({
          descriptionFilter: 'test',
          projectNameFilter: NaN,
          dateFilter: new Date('invalid-date'),
          statusFilter: 'approved'
        })
      );
    });

    it('should count filters correctly in filterSubmit', () => {
      component.filterForm.patchValue({
        dateFilter: '2024-03-20',
        projectNameFilter: '1',
        descriptionFilter: 'test',
        statusFilter: 'Approved'
      });

      component.filterSubmit();

      expect(component.filterCount).toBe(4);
    });

    it('should count partial filters in filterSubmit', () => {
      component.filterForm.patchValue({
        dateFilter: '',
        projectNameFilter: '1',
        descriptionFilter: 'test',
        statusFilter: ''
      });

      component.filterSubmit();

      expect(component.filterCount).toBe(2);
    });
  });

  describe('Constructor Subscriptions', () => {
    it('should handle refresh subscription with valid data', () => {
      jest.spyOn(component, 'getNotification');

      // Simulate refresh subscription trigger
      deliveryService.refresh.next('valid-data');

      expect(component.getNotification).toHaveBeenCalled();
    });

    it('should ignore refresh subscription with invalid data', () => {
      jest.spyOn(component, 'getNotification');

      // Simulate refresh subscription with invalid data
      deliveryService.refresh.next(null);
      deliveryService.refresh.next(undefined);
      deliveryService.refresh.next('');

      expect(component.getNotification).not.toHaveBeenCalled();
    });

    it('should handle refresh1 subscription', () => {
      jest.spyOn(component, 'getNotification');

      deliveryService.refresh1.next('valid-data');

      expect(component.getNotification).toHaveBeenCalled();
    });

    it('should handle inspectionUpdated subscription', () => {
      jest.spyOn(component, 'getNotification');

      deliveryService.inspectionUpdated.next('valid-data');

      expect(component.getNotification).toHaveBeenCalled();
    });

    it('should handle inspectionUpdated1 subscription', () => {
      jest.spyOn(component, 'getNotification');

      deliveryService.inspectionUpdated1.next('valid-data');

      expect(component.getNotification).toHaveBeenCalled();
    });

    it('should handle fetchData subscription', () => {
      jest.spyOn(component, 'getNotification');

      deliveryService.fetchData.next('valid-data');

      expect(component.getNotification).toHaveBeenCalled();
    });

    it('should handle fetchData1 subscription', () => {
      jest.spyOn(component, 'getNotification');

      deliveryService.fetchData1.next('valid-data');

      expect(component.getNotification).toHaveBeenCalled();
    });

    it('should handle fetchConcreteData subscription', () => {
      jest.spyOn(component, 'getNotification');

      deliveryService.fetchConcreteData.next('valid-data');

      expect(component.getNotification).toHaveBeenCalled();
    });

    it('should handle fetchConcreteData1 subscription', () => {
      jest.spyOn(component, 'getNotification');

      deliveryService.fetchConcreteData1.next('valid-data');

      expect(component.getNotification).toHaveBeenCalled();
    });

    it('should handle projectParent subscription', () => {
      jest.spyOn(component, 'getNotification');
      const mockResponse = { ProjectId: 123, ParentCompanyId: 456 };

      projectService.projectParent.next(mockResponse);

      expect(component.ProjectId).toBe(123);
      expect(component.ParentCompanyId).toBe(456);
      expect(component.accountAdmin).toBe(false);
      expect(component.getNotification).toHaveBeenCalled();
    });

    it('should handle isAccountAdmin subscription when ProjectId is not set', () => {
      jest.spyOn(component, 'getNotification');
      component.ProjectId = null;

      projectService.isAccountAdmin.next(true);

      expect(component.accountAdmin).toBe(true);
      expect(component.getNotification).toHaveBeenCalled();
    });

    it('should not update accountAdmin when ProjectId is set', () => {
      jest.spyOn(component, 'getNotification');
      component.ProjectId = 123;

      projectService.isAccountAdmin.next(true);

      expect(component.accountAdmin).toBe(false);
      expect(component.getNotification).not.toHaveBeenCalled();
    });

    it('should handle ParentCompanyId subscription', () => {
      jest.spyOn(component, 'getProjects');

      projectService.ParentCompanyId.next(789);

      expect(component.ParentCompanyId).toBe(789);
      expect(component.getProjects).toHaveBeenCalled();
    });

    it('should handle clearProject subscription with status true', () => {
      const mockResponse = { status: true };

      projectService.clearProject.next(mockResponse);

      expect(component.ProjectId).toBe(-1);
    });

    it('should not update ProjectId when clearProject status is false', () => {
      const mockResponse = { status: false };
      component.ProjectId = 123;

      projectService.clearProject.next(mockResponse);

      expect(component.ProjectId).toBe(123);
    });

    it('should handle loginUser subscription with RoleId 2', () => {
      const mockUser = { RoleId: 2, id: 1 };

      deliveryService.loginUser.next(mockUser);

      expect(component.authUser).toEqual(mockUser);
      expect(component.statusValue).toEqual(['Approved', 'Declined']);
    });

    it('should handle loginUser subscription with RoleId 3', () => {
      const mockUser = { RoleId: 3, id: 1 };

      deliveryService.loginUser.next(mockUser);

      expect(component.authUser).toEqual(mockUser);
      expect(component.statusValue).toEqual(['Delivered']);
    });

    it('should handle getCurrentStatus subscription', () => {
      jest.spyOn(component, 'getNDR');
      const deliveryRequestId = 'test-id';

      deliveryService.getCurrentStatus.next(deliveryRequestId);

      expect(component.DeliveryRequestId).toBe(deliveryRequestId);
      expect(component.getNDR).toHaveBeenCalled();
    });

    it('should handle isMyAccount subscription', () => {
      projectService.isMyAccount.next('valid-data');

      expect(component.myAccount).toBe(true);
    });

    it('should handle isProject subscription', () => {
      component.myAccount = true;

      projectService.isProject.next('valid-data');

      expect(component.myAccount).toBe(false);
    });
  });

  describe('Missing Method Coverage', () => {
    describe('setStatus', () => {
      it('should set delivery request ID and project ID', () => {
        const mockItem = {
          DeliveryRequest: { id: 123 },
          Project: { id: 456 }
        };
        jest.spyOn(component, 'getNDR');

        component.setStatus(mockItem);

        expect(component.DeliveryRequestId).toBe(123);
        expect(component.ProjectId).toBe(456);
        expect(deliveryService.updatedDeliveryId).toHaveBeenCalledWith(123);
        expect(component.getNDR).toHaveBeenCalled();
      });
    });

    describe('getNDR', () => {
      beforeEach(() => {
        component.DeliveryRequestId = 123;
        component.authUser = { id: 1, RoleId: 2 };
      });

      it('should get NDR data successfully and set void to true when user is in void list', () => {
        const mockResponse = {
          data: {
            status: 'Pending',
            voidList: [{ MemberId: 1 }, { MemberId: 2 }]
          }
        };
        deliveryService.getNDRData.mockReturnValue(of(mockResponse));

        component.getNDR();

        expect(component.void).toBe(false);
        expect(component.show).toBe(false);
        expect(deliveryService.getNDRData).toHaveBeenCalledWith({
          DeliveryRequestId: 123
        });
        expect(component.currentDeliverySaveItem).toEqual(mockResponse.data);
        expect(component.void).toBe(true);
        expect(component.show).toBe(true);
        expect(component.showStatus).toBe(true);
      });

      it('should set void to false when user is not in void list', () => {
        const mockResponse = {
          data: {
            status: 'Pending',
            voidList: [{ MemberId: 2 }, { MemberId: 3 }]
          }
        };
        deliveryService.getNDRData.mockReturnValue(of(mockResponse));

        component.getNDR();

        expect(component.void).toBe(false);
        expect(component.show).toBe(true);
      });

      it('should show status for RoleId 3 with Approved status', () => {
        const mockResponse = {
          data: {
            status: 'Approved',
            voidList: []
          }
        };
        component.authUser.RoleId = 3;
        deliveryService.getNDRData.mockReturnValue(of(mockResponse));

        component.getNDR();

        expect(component.showStatus).toBe(true);
      });

      it('should not show status for invalid conditions', () => {
        const mockResponse = {
          data: {
            status: 'Delivered',
            voidList: []
          }
        };
        deliveryService.getNDRData.mockReturnValue(of(mockResponse));

        component.getNDR();

        expect(component.showStatus).toBe(false);
      });
    });

    describe('openEditModal', () => {
      it('should hide existing modal and open edit modal', () => {
        const mockItem = { id: 123 };
        component.modalRef = { hide: jest.fn(), setClass: jest.fn() } as any;

        component.openEditModal(mockItem);

        expect(component.modalRef.hide).toHaveBeenCalled();
        expect(deliveryService.updatedDeliveryId).toHaveBeenCalledWith(123);
        expect(modalService.show).toHaveBeenCalledWith(
          expect.any(Function),
          {
            backdrop: 'static',
            keyboard: false,
            class: 'modal-lg new-delivery-popup custom-modal'
          }
        );
      });

      it('should open edit modal without hiding when no existing modal', () => {
        const mockItem = { id: 123 };
        component.modalRef = null;

        component.openEditModal(mockItem);

        expect(deliveryService.updatedDeliveryId).toHaveBeenCalledWith(123);
        expect(modalService.show).toHaveBeenCalled();
      });
    });

    describe('addToVoid', () => {
      beforeEach(() => {
        component.currentDeliverySaveItem = { id: 123 };
        component.ProjectId = 456;
        component.voidSubmitted = false;
        component.modalRef = { hide: jest.fn(), setClass: jest.fn() } as any;
      });

      it('should add to void successfully', () => {
        const mockResponse = { message: 'Added to void successfully' };
        deliveryService.createVoid.mockReturnValue(of(mockResponse));

        component.addToVoid();

        expect(component.voidSubmitted).toBe(true);
        expect(deliveryService.createVoid).toHaveBeenCalledWith({
          DeliveryRequestId: 123,
          ProjectId: 456
        });
        expect(toastr.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
        expect(component.voidSubmitted).toBe(false);
        expect(router.navigate).toHaveBeenCalledWith(['/void-list']);
        expect(component.modalRef.hide).toHaveBeenCalled();
      });

      it('should handle void creation error with status code 400', () => {
        const error = {
          message: {
            statusCode: 400,
            details: [{ error: 'Validation error' }]
          }
        };
        deliveryService.createVoid.mockReturnValue(throwError(() => error));
        jest.spyOn(component, 'showError');

        component.addToVoid();

        expect(component.showError).toHaveBeenCalledWith(error);
        expect(component.voidSubmitted).toBe(false);
      });

      it('should handle void creation error without message', () => {
        const error = {};
        deliveryService.createVoid.mockReturnValue(throwError(() => error));

        component.addToVoid();

        expect(toastr.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
        expect(component.voidSubmitted).toBe(false);
      });

      it('should handle void creation error with message', () => {
        const error = { message: 'Void creation failed' };
        deliveryService.createVoid.mockReturnValue(throwError(() => error));

        component.addToVoid();

        expect(toastr.error).toHaveBeenCalledWith('Void creation failed', 'OOPS!');
        expect(component.voidSubmitted).toBe(false);
      });

      it('should not create void when already submitted', () => {
        component.voidSubmitted = true;

        component.addToVoid();

        expect(deliveryService.createVoid).not.toHaveBeenCalled();
      });

      it('should handle void creation without modal', () => {
        const mockResponse = { message: 'Added to void successfully' };
        deliveryService.createVoid.mockReturnValue(of(mockResponse));
        component.modalRef = null;

        component.addToVoid();

        expect(toastr.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
        expect(router.navigate).toHaveBeenCalledWith(['/void-list']);
        // Should not throw error when modalRef is null
      });
    });

    describe('filterDetailsForm', () => {
      it('should create filter form with correct structure', () => {
        component.filterDetailsForm();

        expect(component.filterForm).toBeTruthy();
        expect(component.filterForm.get('dateFilter')).toBeTruthy();
        expect(component.filterForm.get('projectNameFilter')).toBeTruthy();
        expect(component.filterForm.get('descriptionFilter')).toBeTruthy();
        expect(component.filterForm.get('statusFilter')).toBeTruthy();

        // Check initial values
        expect(component.filterForm.get('dateFilter').value).toBe('');
        expect(component.filterForm.get('projectNameFilter').value).toBe('');
        expect(component.filterForm.get('descriptionFilter').value).toBe('');
        expect(component.filterForm.get('statusFilter').value).toBe('');
      });
    });
  });

  describe('Advanced Edge Cases and Error Scenarios', () => {
    describe('Pagination Edge Cases', () => {
      it('should handle page size change to 0', () => {
        component.changePageSize(0);

        expect(component.pageSize).toBe(0);
        expect(deliveryService.listNotification).toHaveBeenCalled();
      });

      it('should handle negative page number', () => {
        component.changePageNo(-1);

        expect(component.pageNo).toBe(-1);
        expect(deliveryService.listNotification).toHaveBeenCalled();
      });

      it('should handle very large page size', () => {
        component.changePageSize(999999);

        expect(component.pageSize).toBe(999999);
        expect(deliveryService.listNotification).toHaveBeenCalled();
      });

      it('should reset page number to 1 when changing page size', () => {
        component.pageNo = 5;
        component.changePageSize(50);

        expect(component.pageNo).toBe(5); // changePageSize doesn't reset pageNo
        expect(component.pageSize).toBe(50);
      });
    });

    describe('Search Edge Cases', () => {
      it('should handle search with special characters', () => {
        const specialSearch = '!@#$%^&*()_+{}|:"<>?[]\\;\',./"';

        component.getSearchNotification(specialSearch);

        expect(component.search).toBe(specialSearch);
        expect(component.showSearchbar).toBe(true);
        expect(deliveryService.listNotification).toHaveBeenCalled();
      });

      it('should handle very long search string', () => {
        const longSearch = 'a'.repeat(1000);

        component.getSearchNotification(longSearch);

        expect(component.search).toBe(longSearch);
        expect(component.showSearchbar).toBe(true);
      });

      it('should handle search with only whitespace', () => {
        component.getSearchNotification('   \t\n   ');

        expect(component.showSearchbar).toBe(false);
        expect(component.search).toBe('   \t\n   ');
      });
    });

    describe('Modal Edge Cases', () => {
      it('should handle close when modalRef is undefined', () => {
        component.modalRef = undefined;

        expect(() => component.close()).toThrow();
      });

      it('should handle openIdModal with missing request type', () => {
        const mockItem = {
          // No requestType property
          CraneRequest: { CraneRequestId: 123 }
        };

        component.openIdModal(mockItem);

        // Should not open any modal when requestType is missing
        expect(modalService.show).not.toHaveBeenCalled();
      });

      it('should handle openIdModal with unknown request type', () => {
        const mockItem = {
          requestType: 'unknownType',
          SomeRequest: { id: 123 }
        };

        component.openIdModal(mockItem);

        // Should not open any modal for unknown request type
        expect(modalService.show).not.toHaveBeenCalled();
      });
    });

    describe('Data Validation Edge Cases', () => {
      it('should handle setRead with null data', () => {
        component.setRead(null, 0);

        expect(deliveryService.setReadNotification).not.toHaveBeenCalled();
      });

      it('should handle setRead with undefined DeliveryNotification', () => {
        const mockData = {
          DeliveryNotification: undefined
        };

        component.setRead(mockData, 0);

        expect(deliveryService.setReadNotification).not.toHaveBeenCalled();
      });

      it('should handle setRead with empty DeliveryNotification array', () => {
        const mockData = {
          DeliveryNotification: []
        };

        component.setRead(mockData, 0);

        expect(deliveryService.setReadNotification).not.toHaveBeenCalled();
      });

      it('should handle redirect with null Project', () => {
        const mockData = {
          Project: null
        };

        expect(() => component.redirect(mockData)).toThrow();
      });

      it('should handle redirect with undefined Project.id', () => {
        const mockData = {
          Project: { id: undefined }
        };

        component.redirect(mockData);

        expect(projectService.updatedProjectId).toHaveBeenCalledWith(undefined);
        expect(router.navigate).toHaveBeenCalledWith(['/delivery-request']);
      });
    });
  });

  // ============ ADDITIONAL COMPREHENSIVE TEST CASES ============

  describe('Additional Positive Test Cases', () => {
    describe('ngOnInit', () => {
      it('should call ngOnInit without errors', () => {
        expect(() => component.ngOnInit()).not.toThrow();
      });
    });

    describe('localStorage Integration', () => {
      it('should set localStorage when redirecting', () => {
        const mockData = {
          Project: { id: 123 }
        };
        const setItemSpy = jest.spyOn(Storage.prototype, 'setItem');

        component.redirect(mockData);

        expect(setItemSpy).toHaveBeenCalledWith('ProjectId', '123');
        setItemSpy.mockRestore();
      });
    });

    describe('resetFilter', () => {
      it('should reset all filter properties and call necessary methods', () => {
        component.filterCount = 5;
        component.search = 'test search';
        component.pageNo = 3;
        component.filterForm.patchValue({
          dateFilter: '2024-03-20',
          projectNameFilter: '1',
          descriptionFilter: 'test',
          statusFilter: 'Approved'
        });
        jest.spyOn(component, 'filterDetailsForm');

        component.resetFilter();

        expect(component.filterCount).toBe(0);
        expect(component.search).toBe('');
        expect(component.pageNo).toBe(1);
        expect(component.filterDetailsForm).toHaveBeenCalled();
        expect(deliveryService.listNotification).toHaveBeenCalled();
        expect(component.modalRef.hide).toHaveBeenCalled();
      });
    });

    describe('showError', () => {
      it('should display error message from error object', () => {
        const mockError = {
          message: {
            details: [{ error: 'Test error message' }]
          }
        };

        component.showError(mockError);

        expect(toastr.error).toHaveBeenCalledWith('Test error message');
      });

      it('should handle error with multiple details', () => {
        const mockError = {
          message: {
            details: [
              { error: 'First error', field: 'field1' },
              { warning: 'Warning message', field: 'field2' }
            ]
          }
        };

        component.showError(mockError);

        expect(toastr.error).toHaveBeenCalledWith('First error,field1');
      });
    });

    describe('getNotification with different scenarios', () => {
      it('should handle empty notification list response', () => {
        const mockResponse = {
          data: {
            rows: [],
            count: 0
          }
        };
        deliveryService.listNotification.mockReturnValue(of(mockResponse));
        component.ProjectId = 1;
        component.ParentCompanyId = 1;

        component.getNotification();

        expect(component.notificationList).toEqual([]);
        expect(component.totalCount).toBe(0);
        expect(component.loader).toBe(false);
      });

      it('should handle large notification list', () => {
        const mockNotifications = Array.from({ length: 100 }, (_, i) => ({
          id: i + 1,
          message: `Notification ${i + 1}`
        }));
        const mockResponse = {
          data: {
            rows: mockNotifications,
            count: 100
          }
        };
        deliveryService.listNotification.mockReturnValue(of(mockResponse));
        component.ProjectId = 1;
        component.ParentCompanyId = 1;

        component.getNotification();

        expect(component.notificationList).toEqual(mockNotifications);
        expect(component.totalCount).toBe(100);
      });
    });
  });

  describe('Additional Negative Test Cases', () => {
    describe('Error Handling for API Calls', () => {
      it('should handle getProjects API error gracefully', () => {
        const error = new Error('Projects API Error');
        projectService.getProject.mockReturnValue(throwError(() => error));

        expect(() => component.getProjects()).not.toThrow();
      });

      it('should handle getNDR API error gracefully', () => {
        const error = new Error('NDR API Error');
        deliveryService.getNDRData.mockReturnValue(throwError(() => error));
        component.DeliveryRequestId = 123;

        expect(() => component.getNDR()).not.toThrow();
      });
    });

    describe('Edge Cases for Modal Operations', () => {
      it('should handle openIdModal when modalRef.content is undefined', () => {
        const mockItem = {
          requestType: 'craneRequest',
          CraneRequest: { CraneRequestId: 123 }
        };
        modalService.show.mockReturnValue({
          content: undefined,
          hide: jest.fn(),
          setClass: jest.fn()
        } as any);

        expect(() => component.openIdModal(mockItem)).toThrow();
      });

      it('should handle setDeleteContent with null template', () => {
        const mockData = { id: 1 };
        jest.spyOn(component, 'openModal');

        component.setDeleteContent(null, mockData);

        expect(component.deleteNotification).toEqual(mockData);
        expect(component.openModal).toHaveBeenCalledWith(null);
      });
    });

    describe('Boundary Value Testing', () => {
      it('should handle zero page size', () => {
        component.changePageSize(0);
        expect(component.pageSize).toBe(0);
      });

      it('should handle negative page number', () => {
        component.changePageNo(-5);
        expect(component.pageNo).toBe(-5);
      });

      it('should handle extremely large page size', () => {
        const largePageSize = Number.MAX_SAFE_INTEGER;
        component.changePageSize(largePageSize);
        expect(component.pageSize).toBe(largePageSize);
      });
    });

    describe('Form Validation Edge Cases', () => {
      it('should handle filterSubmit with all empty values', () => {
        component.filterForm.patchValue({
          dateFilter: '',
          projectNameFilter: '',
          descriptionFilter: '',
          statusFilter: ''
        });

        component.filterSubmit();

        expect(component.filterCount).toBe(0);
        expect(component.pageNo).toBe(1);
      });

      it('should handle filterSubmit with mixed valid and invalid values', () => {
        component.filterForm.patchValue({
          dateFilter: 'invalid-date',
          projectNameFilter: 'not-a-number',
          descriptionFilter: 'valid description',
          statusFilter: 'VALID_STATUS'
        });

        component.filterSubmit();

        expect(component.filterCount).toBe(4); // All non-empty values count
      });
    });
  });

  describe('Integration Test Cases', () => {
    describe('Complete User Workflows', () => {
      it('should handle complete search workflow', () => {
        // Start search
        component.getSearchNotification('test query');
        expect(component.showSearchbar).toBe(true);
        expect(component.search).toBe('test query');

        // Clear search
        component.clear();
        expect(component.showSearchbar).toBe(false);
        expect(component.search).toBe('');
        expect(component.pageNo).toBe(1);
      });

      it('should handle complete filter workflow', () => {
        // Set filters
        component.filterForm.patchValue({
          dateFilter: '2024-03-20',
          projectNameFilter: '1',
          descriptionFilter: 'test',
          statusFilter: 'Approved'
        });

        // Submit filters
        component.filterSubmit();
        expect(component.filterCount).toBe(4);
        expect(component.pageNo).toBe(1);

        // Reset filters
        component.resetFilter();
        expect(component.filterCount).toBe(0);
        expect(component.search).toBe('');
      });

      it('should handle complete notification read workflow', () => {
        const mockData = {
          DeliveryNotification: [{ seen: false, id: 1 }]
        };
        const mockResponse = { success: true };
        deliveryService.setReadNotification.mockReturnValue(of(mockResponse));
        component.notificationList = [mockData];
        component.ParentCompanyId = 1;

        // Mark as read
        component.setRead(mockData, 0);

        expect(deliveryService.setReadNotification).toHaveBeenCalledWith({
          id: 1,
          ParentCompanyId: 1
        });
        expect(component.notificationList[0].DeliveryNotification[0].seen).toBe(true);
        expect(deliveryService.updatedRefreshCount).toHaveBeenCalledWith(true);
      });
    });
  });
});
