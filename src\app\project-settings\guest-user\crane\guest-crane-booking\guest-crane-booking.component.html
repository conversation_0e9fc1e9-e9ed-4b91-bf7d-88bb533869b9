<div class="modal-header p-3 border-bottom-grayb">
  <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">
    <img src="./assets/images/equipments.svg" alt="Delivery" class="me-2" />New Crane Pick Booking
  </h1>
</div>
<div class="modal-body newupdate-alignments craneedit p-3" *ngIf="!modalLoader">
  <div class="addcalendar-details">
    <form
      name="form"
      class="custom-material-form"
      id="deliverydetails-form3"
      [formGroup]="craneRequest"
      (ngSubmit)="onSubmit()"
      novalidate
    >
      <div class="row">
        <div class="col-md-6">
          <div class="form-group mb-0">
            <label class="fs12 fw600" for="description">Description<sup>*</sup></label>
            <textarea  id="description"
              class="form-control fs11 radius0"
              placeholder="Enter Description"
              rows="2"
              formControlName="description"
              maxlength="150"
            ></textarea>
            <div class="color-red" *ngIf="submitted && craneRequest.get('description').errors">
              <small *ngIf="craneRequest.get('description').errors.required"
                >*Description is Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6" *ngIf="lastId">
          <div class="form-group mb-0">
            <label class="fs12 fw600"  for="cranId">Crane Pick ID</label>
            <input  id="cranId"
              type="text"
              class="form-control fs10 color-orange fw500 material-input ps-3"
              placeholder=""
              value="{{ lastId }}"
              disabled="disabled"
            />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 pt-1">
          <div class="form-group mb-0 guest-company-select" id="company-select4">
            <label class="fs12 fw600"  for="resCompany">Responsible Company<sup>*</sup></label>
            <ng-multiselect-dropdown  id="resCompany"
              [placeholder]="'Responsible Company*'"
              [settings]="newNdrCompanyDropdownSettings"
              [data]="companyList"
              formControlName="companies"
              [(ngModel)]="responsibleCompanySelectedItems"
            >
            </ng-multiselect-dropdown>
            <div class="color-red" *ngIf="submitted && craneRequest.get('companies').errors">
              <small *ngIf="craneRequest.get('companies').errors.required"
                >*Company is Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6 mt-1">
          <div class="form-group mb-0 guest-company-select" id="company-select8">
            <label class="fs12 fw600" for="defWork">Definable Feature Of Work</label>
            <ng-multiselect-dropdown id="defWork"
              [placeholder]="'Definable Feature Of Work (Scope)'"
              [settings]="newNdrDefinableDropdownSettings"
              [data]="definableFeatureOfWorkList"
              formControlName="definableFeatureOfWorks"
            >
            </ng-multiselect-dropdown>
            <div
              class="color-red"
              *ngIf="submitted && craneRequest.get('definableFeatureOfWorks').errors"
            >
              <small *ngIf="craneRequest.get('definableFeatureOfWorks').errors.required"
                >*Definable Feature Of Work is Required.
              </small>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="form-group mt-2 mb-0">
            <label class="fs12 fw600" for="resPerson">Responsible Person<sup>*</sup></label>
            <ul
              class="follo-switch list-group list-group-horizontal justify-content-end float-end newfixswitch"
              id="switch-control4"
            >
              <li class="fs12 list-group-item border-0 pe-3 escort--text py-0">Escort Needed?</li>
              <li class="list-group-item border-0 px-0 py-0">
                <ui-switch
                  switchColor="#fff"
                  defaultBoColor="#CECECE"
                  defaultBgColor="#CECECE"
                  formControlName="isEscortNeeded"
                >
                </ui-switch>
              </li>
            </ul>

            <div class="w-100 float-start">
              <tag-input
                formControlName="responsiblePersons"
                [onlyFromAutocomplete]="true"
                [placeholder]="'Responsible Person *'"
                [onTextChangeDebounce]="500"
                class="tag-layout"
                [identifyBy]="'id'"
                [displayBy]="'email'"
              >
                <tag-input-dropdown
                  [showDropdownIfEmpty]="false"
                  [displayBy]="'email'"
                  [identifyBy]="'id'"
                  [autocompleteObservable]="requestAutocompleteItems"
                >
                  <ng-template let-item="item" let-index="index">
                    <span class="fs10">{{ item.email }}</span>
                  </ng-template>
                </tag-input-dropdown>
              </tag-input>
            </div>
            <div
              class="color-red"
              *ngIf="submitted && craneRequest.get('responsiblePersons').errors"
            >
              <small *ngIf="craneRequest.get('responsiblePersons').errors.required"
                >*Please choose Responsible person.</small
              >
            </div>
          </div>
        </div>

        <div class="col-md-6 mt-0">
          <div class="row">
            <div class="col-md-12 mt-4">
              <label class="fs12 fw600" for="delDate">Delivery Date<sup>*</sup></label>
              <div class="input-group">
                <input  id="delDate"
                  class="form-control fs12 ps-0 fw500 material-input"
                  #dp="bsDatepicker"
                  bsDatepicker
                  formControlName="deliveryDate"
                  placeholder="Delivery Date *"
                  [bsConfig]="{
                    isAnimated: true,
                    showWeekNumbers: false,
                    customTodayClass: 'today'
                  }"
                  (ngModelChange)="showMonthlyRecurrence()"
                />
                  <span class="input-group-text">
                    <img
                      src="./assets/images/date.svg"
                      class="h-12px"
                      alt="Date"
                      (click)="dp.toggle()"
                      (keydown)="dp.toggle()"
                      [attr.aria-expanded]="dp.isOpen"
                    />
                  </span>
              </div>
              <div class="w-100 float-start">
                <div
                  class="input-group mb-3 color-red"
                  *ngIf="submitted && craneRequest.get('deliveryDate').errors"
                >
                  <small *ngIf="craneRequest.get('deliveryDate').errors.required"
                    >*Date is Required.</small
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="form-group mt-2 mb-2 timezone-formgroup">
            <label class="fs12 fw600" for="timeZone">Time Zone</label>

            <ng-multiselect-dropdown id="timeZone"
              [placeholder]="'Choose TimeZone'"
              [settings]="dropdownSettings"
              [data]="timezoneList"
              (change)="timeZoneSelected($event.target.value)"
              formControlName="TimeZoneId"
              [(ngModel)]="defaultValue"
              class="ms-0 ps-0"
            >
            </ng-multiselect-dropdown>
            <div class="color-red" *ngIf="submitted && craneRequest?.get('TimeZoneId')?.errors">
              <small *ngIf="craneRequest.get('TimeZoneId').errors.required"
                >*TimeZone is Required.</small
              >
            </div>
          </div>
          <div class="row mt-3">
            <div class="col-md-6 pe-md-0">
              <div class="input-group delivery-time">
                <label class="fs12 fw600" for="frmTime">From Time:</label>
                <timepicker id="frmTime"
                  [formControlName]="'craneDeliveryStart'"
                  (ngModelChange)="changeDate($event)"
                  (keypress)="numberOnly($event)"
                ></timepicker>
                <div
                  class="color-red mt35"
                  *ngIf="submitted && craneRequest.get('craneDeliveryStart').errors"
                >
                  <small *ngIf="craneRequest.get('craneDeliveryStart').errors.required"
                    >*Start time is Required.</small
                  >
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="input-group delivery-time">
                <label class="fs12 fw600"  for="toTime">To Time:</label>
                <timepicker id="toTime"
                  [formControlName]="'craneDeliveryEnd'"
                  (keypress)="numberOnly($event)"
                  (ngModelChange)="deliveryEndTimeChangeDetection()"
                ></timepicker>
                <div
                  class="color-red mt35"
                  *ngIf="submitted && craneRequest.get('craneDeliveryEnd').errors"
                >
                  <small *ngIf="craneRequest.get('craneDeliveryEnd').errors.required"
                    >*End time is Required.</small
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <div class="form-group pt12 guest-company-select guestdelivery-equipment-select">
            <label class="fs12 fw600"  for="equipment">Equipment<sup>*</sup></label>
            <ng-multiselect-dropdown  id="equipment"
              [placeholder]="'Equipment'"
              [settings]="equipmentDropdownSettings"
              [data]="equipmentList"
              formControlName="EquipmentId" (ngModelChange)="checkEquipmentType($event)"
            >
            </ng-multiselect-dropdown>
            <div class="color-red" *ngIf="submitted && craneRequest.get('EquipmentId').errors">
              <small *ngIf="craneRequest.get('EquipmentId').errors.required"
                >*Equipment is Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6 col-12 mt-0 mt-md-2 primary-tooltip">
          <div class="form-group mb-0 timezone-formgroup">
            <label class="fs12 fw600"  for="location"
              >Location
              <span class="color-red"><sup>*</sup></span>
              <div class="dot-border-info location-border-info tooltip location-tooltip">
                <span class="fw700 info-icon fs12">i</span>
                <span class="tooltiptext tooltiptext-info"
                  >Where will the materials/equipment be installed</span
                >
                <div class="arrow-down"></div>
              </div>
            </label>
            <ng-multiselect-dropdown  id="location"
              [placeholder]="'Choose Location'"
              [settings]="locationDropdownSettings"
              [data]="locationList"
              (onSelect)="locationSelected($event)"
              formControlName="LocationId"
              [(ngModel)]="defaultLocationValue"
            >
            </ng-multiselect-dropdown>
            <div class="color-red" *ngIf="submitted && craneRequest.get('LocationId').errors">
              <small *ngIf="craneRequest.get('LocationId').errors.required"
                >*Location is Required.</small
              >
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6 mt-2">
          <div class="form-group mb-0">
            <label class="fs12 fw600"  for="pickFrom"
              >Picking From<span class="color-red"><sup>*</sup></span></label
            >
            <textarea  id="pickFrom"
              class="form-control fs11 radius0"
              placeholder="Picking From"
              rows="2"
              formControlName="pickUpLocation"
              maxlength="150"
            ></textarea>
            <div class="color-red" *ngIf="submitted && craneRequest.get('pickUpLocation').errors">
              <small *ngIf="craneRequest.get('pickUpLocation').errors.required"
                >*Picking From is Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6 mt-2">
          <div class="form-group mb-0">
            <label class="fs12 fw600"  for="pickTo"
              >Picking To<span class="color-red"><sup>*</sup></span></label
            >
            <textarea  id="pickTo"
              class="form-control fs11 radius0"
              placeholder="Picking To"
              rows="2"
              formControlName="dropOffLocation"
              maxlength="150"
            ></textarea>
            <div class="color-red" *ngIf="submitted && craneRequest.get('dropOffLocation').errors">
              <small *ngIf="craneRequest.get('dropOffLocation').errors.required"
                >*Picking To is Required.</small
              >
            </div>
          </div>
        </div>
      </div>
      <div class="row px-3">
        <!-- Recurrance code start -->

        <div class="float-start w-100 mt-0 mt-md-2 guest-company-select px-0">
          <label class="fs12 fw600"  for="recurrence">Recurrence<sup>*</sup></label>
          <select  id="recurrence"
            class="form-control select-dropdown fs12 material-input px-0"
            formControlName="recurrence"
            (change)="onRecurrenceSelect($event.target.value)"
          >
            <option value="" disabled selected hidden>Select Recurrence</option>
            <option *ngFor="let type of recurrence" value="{{ type.value }}">
              {{ type.value }}
            </option>
          </select>
        </div>
        <div
          class="col-md-12 p-0 float-start mt-2"
          *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
        >
          <div
            class="col-md-12 p-0 mt-md-0"
            *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
          >
            <label class="fs12 fw600"  for="rptEvery">Repeat Every</label>
          </div>
          <div
            class="col-md-6 ps-0 float-start mt-md-0"
            *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
          >
            <div class="form-group mb-0">
              <input  id="rptEvery"
                type="text"
                formControlName="repeatEveryCount"
                class="form-control fs12 material-input p-0"
                (input)="changeRecurrenceCount($event.target.value)"
                min="1"
              />
            </div>
          </div>
          <div class="col-md-6 p-0 float-start mt-md-0" *ngIf="isRepeatWithSingleRecurrence">
            <div class="form-group mb-0 guest-company-select">
              <select
                class="form-control select-dropdown fs12 material-input px-2"
                formControlName="repeatEveryType"
                (change)="chooseRepeatEveryType($event.target.value)"
              >
                <option value="" disabled selected hidden>Select Recurrence</option>
                <option *ngFor="let type of repeatWithSingleRecurrence" value="{{ type.value }}">
                  {{ type.value }}
                </option>
              </select>
            </div>
          </div>
          <div
            class="col-md-6 p-0 float-start mt-md-0"
            *ngIf="isRepeatWithMultipleRecurrence || showRecurrenceTypeDropdown"
          >
            <div class="form-group guest-company-select">
              <select
                class="form-control select-dropdown fs12 material-input px-2"
                formControlName="repeatEveryType"
                (change)="chooseRepeatEveryType($event.target.value)"
              >
                <option value="" disabled selected hidden>Select Recurrence</option>
                <option *ngFor="let type of repeatWithMultipleRecurrence" value="{{ type.value }}">
                  {{ type.value }}
                </option>
              </select>
            </div>
          </div>
        </div>
        <div class="row addcalendar-displaydays">
          <div
            class="col-md-12 pt-0"
            *ngIf="
              (selectedRecurrence === 'Weekly' ||
                isRepeatWithMultipleRecurrence ||
                isRepeatWithSingleRecurrence) &&
              selectedRecurrence !== 'Monthly' &&
              selectedRecurrence !== 'Yearly'
            "
          >
            <ul class="displaylists ps-0">
              <li *ngFor="let item of weekDays; let i = index" class="fs12 list-inline-item">
                <input
                  type="checkbox"
                  [disabled]="item.isDisabled"
                  [value]="item.value"
                  class="d-none"
                  id="days-{{ i }}"
                  (change)="onChange($event)"
                  [checked]="item.checked"
                />
                <label for="days-{{ i }}">{{ item.display }}</label>
              </li>
              <div class="color-red" *ngIf="submitted && craneRequest.controls['days'].errors">
                <small *ngIf="craneRequest.controls['days'].errors.required">*Required </small>
              </div>
            </ul>
          </div>
        </div>
        <div
          class="row"
          *ngIf="selectedRecurrence === 'Monthly' || selectedRecurrence === 'Yearly'"
        >
          <div class="col-md-12 float-start mt-md-0 ps-0">
            <div class="w-100 float-start mt-1">
              <div class="form-check">
                <input
                  class="form-check-input c-pointer"
                  type="radio"
                  formControlName="chosenDateOfMonth"
                  id="flexRadioDefault1"
                  [value]="1"
                  (change)="changeMonthlyRecurrence()"
                />
                <label class="form-check-label fs12 color-orange" for="flexRadioDefault1">
                  On day {{ monthlyDate }}
                </label>
              </div>
              <div class="form-check">
                <input
                  class="form-check-input c-pointer"
                  type="radio"
                  formControlName="chosenDateOfMonth"
                  id="flexRadioDefault2"
                  [value]="2"
                  (change)="changeMonthlyRecurrence()"
                />
                <label class="form-check-label fs12 color-orange" for="flexRadioDefault2">
                  On the {{ monthlyDayOfWeek }}
                  <p *ngIf="selectedRecurrence === 'Yearly'">
                    of
                    {{ craneRequest.get('deliveryDate').value | date : 'LLLL' }}
                  </p>
                </label>
              </div>
              <div class="form-check" *ngIf="enableOption">
                <input
                  class="form-check-input c-pointer"
                  type="radio"
                  formControlName="chosenDateOfMonth"
                  id="flexRadioDefault3"
                  [value]="3"
                  (change)="changeMonthlyRecurrence()"
                />
                <label class="form-check-label fs12 color-orange" for="flexRadioDefault3">
                  On the
                  {{ monthlyLastDayOfWeek }}
                  <p *ngIf="selectedRecurrence === 'Yearly'">
                    of
                    {{ craneRequest.get('deliveryDate').value | date : 'LLLL' }}
                  </p>
                </label>
              </div>
            </div>

            <div>
              <div
                class="color-red"
                *ngIf="
                  submitted &&
                  (craneRequest.get('monthlyRepeatType')?.errors ||
                    craneRequest.get('dateOfMonth')?.errors)
                "
              >
                <small *ngIf="craneRequest.get('monthlyRepeatType')?.errors?.required"
                  >*required</small
                >
                <small *ngIf="craneRequest.get('dateOfMonth')?.errors?.required">*required</small>
              </div>
            </div>
          </div>
        </div>
        <div class="row addcalendar-displaydays messagetext">
          <div class="col-md-6 mt-md-0 pb-0" *ngIf="message">
            <p class="fs12 color-grey11">
              <span class="color-red fw-bold">*</span>
              {{ message }}
            </p>
          </div>
        </div>
        <!-- Recurrance code End -->
        <div
          class="row mt15 w-100"
          *ngIf="
            selectedRecurrence === 'Daily' ||
            selectedRecurrence === 'Monthly' ||
            selectedRecurrence === 'Yearly' ||
            selectedRecurrence === 'Weekly'
          "
        >
          <div class="col-md-12">
            <label class="fs12 fw600"  for="enddate">End Date<sup>*</sup></label>
            <div class="input-group w-100">
              <input  id="enddate"
                class="form-control fs12 fw500 material-input"
                #dp="bsDatepicker"
                bsDatepicker
                formControlName="endDate"
                [minDate]="recurrenceMinDate"
                placement="top"
                placeholder="End Date *"
                [bsConfig]="{
                  isAnimated: true,
                  showWeekNumbers: false,
                  customTodayClass: 'today'
                }"
                (ngModelChange)="showMonthlyRecurrence()"
              />
                <span class="input-group-text">
                  <img
                    src="./assets/images/date.svg"
                    class="h-12px"
                    alt="Date"
                    (click)="dp.toggle()"
                    (keydown) = "dp.toggle()"
                    [attr.aria-expanded]="dp.isOpen"
                  />
                </span>
            </div>
          </div>
        </div>
        <div class="col-md-12 mt-2 px-0">
          <div class="form-group mb-0">
            <label class="fs12 fw600" for="addNotes">Additional Notes</label>
            <textarea id="addNotes"
              class="form-control fs12 min-h-65px"
              placeholder=""
              rows="2"
              formControlName="additionalNotes"
            ></textarea>
            <div class="color-red" *ngIf="submitted && craneRequest.get('additionalNotes').errors">
              <small *ngIf="craneRequest.get('additionalNotes').errors.required"
                >*Notes is Required.</small
              >
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12 mt-2">
          <p class="fs11 color-grey17 fw500">
            The crane delivery needs to be approved by Project Administrator to be added to the
            calendar. Please click on submit to send the booking for approval.
          </p>
        </div>
      </div>
      <div class="my-1 text-center">
        <button
          class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3 px-2rem"
          type="button"
          (click)="close(cancelConfirmation)"
        >
          Cancel
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem"
          type="submit"
          [disabled]="formSubmitted && craneRequest.valid"
        >
          <em
            class="fa fa-spinner"
            aria-hidden="true"
            *ngIf="formSubmitted && craneRequest.valid"
          ></em
          >Submit
        </button>
      </div>
    </form>
  </div>
</div>
<div class="modal-body text-center" *ngIf="modalLoader">Loading...</div>

<!--Confirmation Popup-->
<div id="confirm-popup7">
  <ng-template #cancelConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure you want to cancel?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetForm('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="resetForm('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
