import {
  Component, Input, OnInit, TemplateRef,
} from '@angular/core';
import { BsModalService, BsModalRef, ModalOptions } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { Router } from '@angular/router';
import { Socket } from 'ngx-socket-io';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import moment from 'moment';
import { DeliveryService } from '../../../../services/profile/delivery.service';
import { ProjectService } from '../../../../services/profile/project.service';
import { MixpanelService } from '../../../../services/mixpanel.service';
import { EditConcreteBookingComponent } from '../edit-concrete-booking/edit-concrete-booking.component';
import { ProjectSharingService } from '../../../../services/projectSharingService/project-sharing.service';

@Component({
  selector: 'app-guest-concrete-details',
  templateUrl: './guest-concrete-details.component.html',
})
export class GuestConcreteDetailsComponent implements OnInit {
  @Input() data: any;

  @Input() title: string;

  public loader = true;

  public currentTabId = 0;

  public ProjectId: any;

  public ParentCompanyId: any;

  public ConcreteRequestId: any;

  public modalRef1: BsModalRef;

  public currentDeliverySaveItem: any = {};

  public concreteRequest: any;

  public fileData: any = [];

  public formData: FormData;

  public uploadSubmitted = false;

  public fileArray: any = [];

  public commentList: any = [];

  public commentDetailsForm: UntypedFormGroup;

  public submitted = false;

  public formSubmitted = false;

  public historyList: any = [];

  public seriesOptions = [];

  public allRequestIsOpened = false;

  public guestUserId: any;

  public files: NgxFileDropEntry[] = [];



  public constructor(
    private readonly modalService: BsModalService,
    public modalRef: BsModalRef,
    public deliveryService: DeliveryService,
    public socket: Socket,
    public bsModalRef: BsModalRef,
    private readonly mixpanelService: MixpanelService,
    private readonly formBuilder: UntypedFormBuilder,
    public router: Router,
    public toastr: ToastrService,
    public projectService: ProjectService,
    public projectSharingService: ProjectSharingService,
    private readonly option: ModalOptions,
  ) {
    this.ProjectId = +window.atob(localStorage.getItem('guestProjectId'));
    this.ParentCompanyId = +window.atob(localStorage.getItem('guestParentCompanyId'));
    this.guestUserId = +window.atob(localStorage.getItem('guestId'));
    this.formData = new FormData();
    this.commentForm();
  }

  public ngOnInit(): void {
    const stateValue = this.data;
    this.ConcreteRequestId = stateValue.id;
    this.getConcreteRequest();
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'remove':
          this.removeFile(data, item);
          break;
        case 'open':
          this.openEditModal(data, item);
          break;
        default:
          break;
      }
    }
  }


  public getConcreteRequest(): void {
    this.loader = true;
    const param = {
      ConcreteRequestId: this.ConcreteRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    if (this.ProjectId && this.ParentCompanyId) {
      this.projectSharingService.guestGetConcreteRequestDetail(param).subscribe((res): void => {
        this.concreteRequest = res.data;
        this.currentDeliverySaveItem = res.data;
        this.fileArray = this.concreteRequest.attachments;
        this.commentList = this.concreteRequest.comments;
        this.historyList = this.concreteRequest.history;
        this.historyList = this.historyList.filter(
          (data: { type: string }) => data.type !== 'comment',
        );
        this.loader = false;
      });
    }
  }

  public getResponsiblePeople(object): any {
    if (object?.firstName && object?.lastName) {
      const string = `${object.firstName} ${object.lastName}`;
      const matches = string.match(/\b(\w)/g);
      const acronym = matches.join('').toUpperCase();
      return acronym;
    }
    return 'UU';
  }

  public removeFile(firstIndex: string | number, i: string | number): void {
    this.formData.delete(this.fileData[firstIndex][i].relativePath);
    this.fileData[firstIndex].splice(i);
    this.fileData.splice(firstIndex);
  }

  public uploadData(): void {
    this.uploadSubmitted = true;
    this.formData = new FormData();
    this.fileData.forEach((element: any[], i: any): void => {
      element.forEach(
        (data: { fileEntry: FileSystemFileEntry; relativePath: any }, index: any): void => {
          const { fileEntry } = data;
          fileEntry.file((_file: File): void => {
            this.formData.append('attachment', _file, data.relativePath);
          });
        },
      );
    });
    const params = {
      ConcreteRequestId: this.ConcreteRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
      userId: this.guestUserId,
    };
    this.projectSharingService.guestAddConcreteRequestAttachment(params, this.formData).subscribe({
      next: (res): void => {
        this.fileData = [];
        this.uploadSubmitted = false;
        this.toastr.success(res.message, 'SUCCESS!');
        this.mixpanelService.addGuestUserMixpanelEvents('Attachment added against a Concrete Booking');
        this.socket.emit('ConcreteApproveHistory', res);
        this.getConcreteRequest();
      },
      error: (attachmentErr): void => {
        this.uploadSubmitted = false;
        if (attachmentErr.message?.statusCode === 400) {
          this.showError(attachmentErr);
        } else {
          this.toastr.error(attachmentErr.message, 'OOPS!');
        }
      },
    });
  }

  public dropped(files: NgxFileDropEntry[]): void {
    this.files = files;
    this.fileData.push(this.files);

    this.fileData.forEach((entryGroup: any[], i: number) => {
      entryGroup.forEach(
        (entry: { relativePath: string; fileEntry: FileSystemFileEntry }, index: number) => {
          const extension = this.getFileExtension(entry.relativePath);

          if (!this.isValidExtension(extension)) {
            this.toastr.error(
              'Please select a valid file. Supported file format (.jpg,.jpeg,.png,.pdf,.doc)',
              'OOPS!',
            );
            this.fileData.splice(i, 1);
            return;
          }

          if (entry.fileEntry.isFile) {
            this.processFile(entry.fileEntry, i, index, extension);
          }
        },
      );
    });
  }

  public getFileExtension(path: string): string {
    const parts = path.split('.');
    return parts[parts.length - 1].toLowerCase();
  }

  public isValidExtension(ext: string): boolean {
    return ['jpg', 'jpeg', 'png', 'pdf', 'doc'].includes(ext);
  }

  public processFile(
    fileEntry: FileSystemFileEntry,
    groupIndex: number,
    fileIndex: number,
    extension: string,
  ): void {
    fileEntry.file((file: File) => {
      const filesizeMB = +((file.size / 1_000_000).toFixed(4));

      if (filesizeMB > 2) {
        this.toastr.error('Please choose a attachment less than or equal to 2MB');
        this.fileData.splice(groupIndex, 1);
        return;
      }

      const reader = new FileReader();
      reader.onload = () => {
        if (this.fileData[groupIndex]) {
          this.fileData[groupIndex][fileIndex].image = reader.result;
        }
      };

      if (this.fileData[groupIndex]) {
        this.fileData[groupIndex][fileIndex].extension = extension;
        reader.readAsDataURL(file);
      }
    });
  }

  public fileOver(_event: any): void { /* */ }

  public fileLeave(_event: any): void { /* */ }

  public closeModal(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide(); // Hides the confirmation modal if the action is 'no'
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide(); // Hides the confirmation modal if it exists
      }
      this.bsModalRef.hide(); // Hides the main modal
    }
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }


  public commentForm(): void {
    this.commentDetailsForm = this.formBuilder.group({
      comment: ['', Validators.compose([Validators.required])],
    });
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    if (this.commentDetailsForm.invalid) {
      this.formSubmitted = false;

      return;
    }
    const formValue = this.commentDetailsForm.value;
    if (!this.checkStringEmptyValues(formValue)) {
      const payload = {
        comment: formValue.comment.trim(),
        ConcreteRequestId: this.ConcreteRequestId,
        ParentCompanyId: this.ParentCompanyId,
        ProjectId: this.ProjectId,
        userId: this.guestUserId,
      };
      this.projectSharingService.guestCreateConcreteRequestComment(payload).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addGuestUserMixpanelEvents('Comment added against a Concrete Booking');
            this.submitted = false;
            this.formSubmitted = false;
            this.commentDetailsForm.reset();
            this.getConcreteRequest();
          }
        },
        error: (commentError): void => {
          this.commentDetailsForm.reset();
          if (commentError.message?.statusCode === 400) {
            this.showCommentError(commentError);
          } else {
            this.toastr.error(commentError.message, 'OOPS!');
            this.submitted = false;
          }
        },
      });
    } else {
      this.toastr.error('Please enter a valid comment.', 'OOPS!');
      this.submitted = false;
      this.formSubmitted = false;
    }
  }

  public showCommentError(commentError: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let commentErrorMessage: any = '';
    commentErrorMessage = Object.values(commentError.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(commentErrorMessage);
  }

  public checkStringEmptyValues(formValue: { comment: string }): boolean {
    if (formValue.comment.trim() === '') {
      return true;
    }
    return false;
  }

  public openEditModal(item, action): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    const newPayload = {
      id: this.ConcreteRequestId,
      ProjectId: item.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      seriesOption: item?.recurrence && item?.recurrence?.recurrence !== 'Does Not Repeat' ? action : 1,
    };
    const initialState = {
      data: newPayload,
      title: 'Modal with component',
    };
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(EditConcreteBookingComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
      initialState,
    });
    this.modalRef.content.closeBtnName = 'Close';
    this.modalRef.content.seriesOption = item?.recurrence && item?.recurrence?.recurrence !== 'Does Not Repeat' ? action : 1;
    this.modalRef.content.recurrenceId = item?.recurrence ? item?.recurrence?.id : null;
    this.modalRef.content.recurrenceEndDate = item?.recurrence
      ? item?.recurrence?.recurrenceEndDate
      : null;
  }

  public changeRequestCollapse(data): void {
    this.initializeSeriesOption();
    if (!moment(data.concretePlacementStart).isAfter(moment())) {
      this.seriesOptions = this.seriesOptions.filter((object): any => {
        const seriesObject = object;
        if (seriesObject.option !== 1) {
          seriesObject.disabled = true;
        }
        return seriesObject;
      });
    }
    this.allRequestIsOpened = !this.allRequestIsOpened;
  }

  public initializeSeriesOption(): void {
    this.seriesOptions = [
      {
        option: 1,
        text: 'This event',
        disabled: false,
      },
      {
        option: 2,
        text: 'This and all following events',
        disabled: false,
      },
    ];
  }
}
