import { Injectable } from '@angular/core';
import { Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { UserIdleService } from 'angular-user-idle';
import { AuthService } from './services/auth/auth.service';
import { ProjectService } from './services/profile/project.service';


@Injectable({
  providedIn: 'root',
  })

export class AuthGuard {
  public ProjectId: number;

  public url = '';

  public constructor(
    private readonly router: Router, private readonly authService: AuthService,
    private readonly projectService: ProjectService,
    private readonly userIdle: UserIdleService,
  ) {
    this.projectService.projectId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ProjectId = res;
      }
    });
  }

  public getProjectInfo(): boolean {
    const params = {
      ProjectId: this.ProjectId,
    };
    let data: boolean;
    if (params.ProjectId) {
      this.projectService.getSingleProject(params).subscribe((projectList: any): boolean => {
        if (projectList) {
          if ((projectList.data.status === 'overdue' || projectList.data.status === 'trialoverdue')) {
            data = true;
          }
        } else {
          data = false;
        }
        return data;
      });
      return data;
    }
  }

  public canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): boolean {
    if (!this.authService.loggeduserIn()) {
      this.userIdle.resetTimer();
      this.userIdle.stopTimer();
      this.userIdle.stopWatching();
      this.router.navigate(['/login']);
      return false;
    }
    if (this.ProjectId !== -1) {
      const params = {
        ProjectId: this.ProjectId,
      };
      if (params.ProjectId) {
        this.projectService.getSingleProject(params).subscribe((projectList: any): boolean => {
          if (projectList.data !== null) {
            if ((projectList.data.status === 'overdue' || projectList.data.status === 'trialoverdue')) {
              if (state.url !== '/profile') {
                this.router.navigate(['/profile']);
              }
              this.projectService.updatedSubStatus({ status: true });
              return false;
            }
            this.projectService.updatedSubStatus({ status: false });
          } else {
            return false;
          }
          return null;
        });
      }
    }

    this.userIdle.startWatching();
    this.userIdle.onTimerStart().subscribe((count): void => {
      const eventList = ['click', 'mouseover', 'keydown', 'DOMMouseScroll', 'mousewheel',
        'mousedown', 'touchstart', 'touchmove', 'scroll', 'keyup'];
      eventList.forEach((event): void => {
        document.body.addEventListener(event, (): void => this.userIdle.resetTimer());
      });
    });
    this.userIdle.onTimeout().subscribe((): void => this.authService.logout());
    return true;
  }
}
