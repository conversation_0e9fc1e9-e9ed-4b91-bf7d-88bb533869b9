import { ComponentFixture, TestBed } from '@angular/core/testing';
import { OverviewComponent } from './overview.component';
import { UntypedFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { ProjectService } from '../services/profile/project.service';
import { ProfileService } from '../services/profile/profile.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { AuthService } from '../services/auth/auth.service';
import { of, throwError, BehaviorSubject } from 'rxjs';
import { countryCodes } from '../services/countryCodes';

describe('OverviewComponent', () => {
  let component: OverviewComponent;
  let fixture: ComponentFixture<OverviewComponent>;
  let projectServiceMock: jest.Mocked<ProjectService>;
  let profileServiceMock: jest.Mocked<ProfileService>;
  let toastrServiceMock: jest.Mocked<ToastrService>;
  let authServiceMock: jest.Mocked<AuthService>;
  let deliveryServiceMock: jest.Mocked<DeliveryService>;

  beforeEach(async () => {
    projectServiceMock = {
      projectParent: new BehaviorSubject<any>(null)
    } as any;

    profileServiceMock = {
      getOverView: jest.fn(),
      refreshData: new BehaviorSubject<any>(null),
      updatedOverView: jest.fn(),
      updateProfile: jest.fn().mockReturnValue(of({ message: 'Success' }))
    } as any;

    toastrServiceMock = {
      success: jest.fn(),
      error: jest.fn()
    } as any;

    authServiceMock = {
      checkDialCode: jest.fn()
    } as any;

    deliveryServiceMock = {} as any;

    await TestBed.configureTestingModule({
      declarations: [OverviewComponent],
      imports: [ReactiveFormsModule],
      providers: [
        UntypedFormBuilder,
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: ProfileService, useValue: profileServiceMock },
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: AuthService, useValue: authServiceMock },
        { provide: DeliveryService, useValue: deliveryServiceMock }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(OverviewComponent);
    component = fixture.componentInstance;
    profileServiceMock.getOverView.mockClear();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Form Initialization', () => {
    it('should initialize the form with required controls', () => {
      expect(component.overViewDetailsForm.get('CompanyId')).toBeTruthy();
      expect(component.overViewDetailsForm.get('firstName')).toBeTruthy();
      expect(component.overViewDetailsForm.get('lastName')).toBeTruthy();
      expect(component.overViewDetailsForm.get('email')).toBeTruthy();
      expect(component.overViewDetailsForm.get('phoneNumber')).toBeTruthy();
      expect(component.overViewDetailsForm.get('phoneCode')).toBeTruthy();
      expect(component.overViewDetailsForm.get('companyName')).toBeTruthy();
    });

    it('should have required validators on mandatory fields', () => {
      const form = component.overViewDetailsForm;
      expect(form.get('CompanyId').valid).toBeFalsy();
      expect(form.get('firstName').valid).toBeFalsy();
      expect(form.get('lastName').valid).toBeFalsy();
      expect(form.get('email').valid).toBeFalsy();
      expect(form.get('phoneNumber').valid).toBeFalsy();
      expect(form.get('phoneCode').valid).toBeFalsy();
      expect(form.get('companyName').valid).toBeFalsy();
    });
  });

  describe('getOverView', () => {
    it('should fetch overview data when ProjectId and ParentCompanyId are available', () => {
      const mockResponse = {
        data: {
          User: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phoneNumber: '1234567890',
            phoneCode: '+1'
          },
          Company: {
            id: '1',
            companyName: 'Test Company',
            address: '123 Test St',
            website: 'www.test.com'
          }
        }
      };

      profileServiceMock.getOverView.mockReturnValue(of(mockResponse));

      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      component.getOverView();

      expect(profileServiceMock.getOverView).toHaveBeenCalledWith({
        ProjectId: '123',
        ParentCompanyId: '456'
      });
      expect(component.userData).toEqual(mockResponse.data);
      expect(component.loader).toBeFalsy();
    });

    it('should not fetch data when ProjectId or ParentCompanyId is missing', () => {
      profileServiceMock.getOverView.mockClear();
      
      component.ProjectId = null;
      component.ParentCompanyId = '456';
      component.getOverView();

      expect(profileServiceMock.getOverView).not.toHaveBeenCalled();
    });
  });

  describe('Input Validation Methods', () => {
    describe('numberOnly', () => {
      it('should allow numeric characters (0-9)', () => {
        for (let i = 48; i <= 57; i++) {
          const event = { which: i, keyCode: i };
          expect(component.numberOnly(event)).toBeTruthy();
        }
      });

      it('should allow control characters (backspace, delete, etc.)', () => {
        const controlChars = [8, 9, 27, 13, 46]; // backspace, tab, escape, enter, delete
        controlChars.forEach(charCode => {
          const event = { which: charCode, keyCode: charCode };
          expect(component.numberOnly(event)).toBeTruthy();
        });
      });

      it('should reject alphabetic characters', () => {
        const alphaEvent1 = { which: 65, keyCode: 65 }; // 'A'
        const alphaEvent2 = { which: 97, keyCode: 97 }; // 'a'
        expect(component.numberOnly(alphaEvent1)).toBeFalsy();
        expect(component.numberOnly(alphaEvent2)).toBeFalsy();
      });

      it('should reject special characters', () => {
        const specialChars = [33, 64, 35, 36, 37]; // !, @, #, $, %
        specialChars.forEach(charCode => {
          const event = { which: charCode, keyCode: charCode };
          expect(component.numberOnly(event)).toBeFalsy();
        });
      });

      it('should use keyCode when which is not available', () => {
        const event = { which: null, keyCode: 48 };
        expect(component.numberOnly(event)).toBeTruthy();
      });
    });

    describe('alphaOnly', () => {
      it('should allow uppercase letters (A-Z)', () => {
        for (let i = 65; i <= 90; i++) {
          const event = { keyCode: i };
          expect(component.alphaOnly(event)).toBeTruthy();
        }
      });

      it('should allow lowercase letters (a-z)', () => {
        for (let i = 97; i <= 122; i++) {
          const event = { keyCode: i };
          expect(component.alphaOnly(event)).toBeTruthy();
        }
      });

      it('should allow backspace', () => {
        const event = { keyCode: 8 };
        expect(component.alphaOnly(event)).toBeTruthy();
      });

      it('should reject numeric characters', () => {
        const numericEvent = { keyCode: 48 }; // '0'
        expect(component.alphaOnly(numericEvent)).toBeFalsy();
      });

      it('should reject special characters', () => {
        const specialEvent = { keyCode: 33 }; // '!'
        expect(component.alphaOnly(specialEvent)).toBeFalsy();
      });

      it('should allow extended ASCII characters', () => {
        const extendedEvent = { keyCode: 128 };
        expect(component.alphaOnly(extendedEvent)).toBeTruthy();
      });
    });

    describe('companyNameCheck', () => {
      it('should allow uppercase letters', () => {
        const event = { keyCode: 65 }; // 'A'
        expect(component.companyNameCheck(event)).toBeTruthy();
      });

      it('should allow lowercase letters', () => {
        const event = { keyCode: 97 }; // 'a'
        expect(component.companyNameCheck(event)).toBeTruthy();
      });

      it('should allow space character', () => {
        const event = { keyCode: 32 }; // Space
        expect(component.companyNameCheck(event)).toBeTruthy();
      });

      it('should allow numeric characters', () => {
        const event = { keyCode: 48 }; // '0'
        expect(component.companyNameCheck(event)).toBeTruthy();
      });

      it('should allow backspace', () => {
        const event = { keyCode: 8 };
        expect(component.companyNameCheck(event)).toBeTruthy();
      });

      it('should allow special characters within range', () => {
        const event = { keyCode: 33 }; // '!'
        expect(component.companyNameCheck(event)).toBeTruthy();
      });

      it('should reject control characters below 32 (except backspace)', () => {
        const event = { keyCode: 31 };
        expect(component.companyNameCheck(event)).toBeFalsy();
      });
    });

    describe('alphaNum', () => {
      it('should allow uppercase letters', () => {
        const event = { which: 65, keyCode: 65 }; // 'A'
        expect(component.alphaNum(event)).toBeTruthy();
      });

      it('should allow lowercase letters', () => {
        const event = { which: 97, keyCode: 97 }; // 'a'
        expect(component.alphaNum(event)).toBeTruthy();
      });

      it('should allow space character', () => {
        const event = { which: 32, keyCode: 32 }; // Space
        expect(component.alphaNum(event)).toBeTruthy();
      });

      it('should allow numeric characters', () => {
        const event = { which: 48, keyCode: 48 }; // '0'
        expect(component.alphaNum(event)).toBeTruthy();
      });

      it('should reject invalid special characters', () => {
        const event = { which: 33, keyCode: 33 }; // '!'
        expect(component.alphaNum(event)).toBeFalsy();
      });

      it('should use keyCode when which is not available', () => {
        const event = { which: null, keyCode: 65 };
        expect(component.alphaNum(event)).toBeTruthy();
      });

      it('should allow extended ASCII characters', () => {
        const event = { which: 128, keyCode: 128 };
        expect(component.alphaNum(event)).toBeTruthy();
      });
    });
  });

  describe('Phone Mask Handling', () => {
    it('should update phone mask based on country code', () => {
      const mockMask = '************';
      authServiceMock.checkDialCode.mockReturnValue(mockMask);

      const result = component.changeMask('+1');
      
      expect(authServiceMock.checkDialCode).toHaveBeenCalledWith('+1');
      expect(component.phoneMask).toBe(mockMask);
      expect(result).toBe(mockMask);
    });
  });

  describe('Address Handling', () => {
    describe('getAssignPlace', () => {
      it('should correctly format address components', () => {
        const result = component.getAssignPlace(
          '123',
          'Main St',
          'Apt 4B',
          'Downtown'
        );

        expect(result).toBe('123 Main St Apt 4B Downtown');
      });

      it('should handle empty address components', () => {
        const result = component.getAssignPlace(
          '123',
          'Main St',
          '',
          ''
        );

        expect(result).toBe('123 Main St');
      });

      it('should handle all empty components', () => {
        const result = component.getAssignPlace('', '', '', '');
        expect(result).toBe('');
      });

      it('should handle undefined components', () => {
        const result = component.getAssignPlace(undefined, undefined, undefined, undefined);
        expect(result).toBe('');
      });

      it('should handle mixed empty and valid components', () => {
        const result = component.getAssignPlace('123', '', 'Apt 4B', '');
        expect(result).toBe('123 Apt 4B');
      });
    });

    describe('handleOverviewCompanyAddressChange', () => {
      it('should handle complete address data', () => {
        const mockAddress = {
          address_components: [
            { types: ['street_number'], long_name: '123' },
            { types: ['route'], long_name: 'Main St' },
            { types: ['sublocality_level_1'], long_name: 'Downtown' },
            { types: ['sublocality_level_2'], long_name: 'District 1' },
            { types: ['locality'], long_name: 'City Center' },
            { types: ['administrative_area_level_2'], long_name: 'Metro City' },
            { types: ['administrative_area_level_1'], long_name: 'State' },
            { types: ['country'], long_name: 'Country' },
            { types: ['postal_code'], long_name: '12345' },
            { types: ['postal_code_suffix'], long_name: '6789' }
          ]
        };

        component.handleOverviewCompanyAddressChange(mockAddress);

        expect(component.overViewDetailsForm.get('address').value).toBe('123 Main St District 1 Downtown');
        expect(component.overViewDetailsForm.get('city').value).toBe('City Center');
        expect(component.overViewDetailsForm.get('state').value).toBe('State');
        expect(component.overViewDetailsForm.get('country').value).toBe('Country');
        expect(component.overViewDetailsForm.get('zipCode').value).toBe('12345');
      });

      it('should handle address without locality (use administrative_area_level_2)', () => {
        const mockAddress = {
          address_components: [
            { types: ['street_number'], long_name: '123' },
            { types: ['route'], long_name: 'Main St' },
            { types: ['administrative_area_level_2'], long_name: 'Metro City' },
            { types: ['administrative_area_level_1'], long_name: 'State' },
            { types: ['country'], long_name: 'Country' }
          ]
        };

        component.handleOverviewCompanyAddressChange(mockAddress);

        expect(component.overViewDetailsForm.get('city').value).toBe('Metro City');
      });

      it('should handle address without postal_code (use postal_code_suffix)', () => {
        const mockAddress = {
          address_components: [
            { types: ['street_number'], long_name: '123' },
            { types: ['route'], long_name: 'Main St' },
            { types: ['postal_code_suffix'], long_name: '6789' }
          ]
        };

        component.handleOverviewCompanyAddressChange(mockAddress);

        expect(component.overViewDetailsForm.get('zipCode').value).toBe('6789');
      });

      it('should handle address without assignPlace (use sublocality)', () => {
        const mockAddress = {
          address_components: [
            { types: ['sublocality_level_1'], long_name: 'Downtown' }
          ]
        };

        jest.spyOn(component, 'getAssignPlace').mockReturnValue('');

        component.handleOverviewCompanyAddressChange(mockAddress);

        expect(component.overViewDetailsForm.get('address').value).toBe('Downtown');
      });

      it('should handle empty address components', () => {
        const mockAddress = {
          address_components: []
        };

        component.handleOverviewCompanyAddressChange(mockAddress);

        expect(component.overViewDetailsForm.get('address').value).toBeUndefined();
      });

      it('should handle address with multiple type values', () => {
        const mockAddress = {
          address_components: [
            {
              types: ['street_number', 'premise'],
              long_name: '123'
            },
            {
              types: ['route', 'political'],
              long_name: 'Main St'
            }
          ]
        };

        component.handleOverviewCompanyAddressChange(mockAddress);

        expect(component.overViewDetailsForm.get('address').value).toBe('123 Main St');
      });

      it('should handle partial address data', () => {
        const mockAddress = {
          address_components: [
            { types: ['administrative_area_level_1'], long_name: 'State' },
            { types: ['country'], long_name: 'Country' }
          ]
        };

        component.handleOverviewCompanyAddressChange(mockAddress);

        expect(component.overViewDetailsForm.get('state').value).toBe('State');
        expect(component.overViewDetailsForm.get('country').value).toBe('Country');
      });
    });
  });

  describe('Form Submission', () => {
    it('should handle form submission with valid data', () => {
      const mockFormData = {
        CompanyId: '1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        phoneCode: '+1',
        companyName: 'Test Company'
      };

      component.overViewDetailsForm.patchValue(mockFormData);
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      profileServiceMock.updateProfile.mockReturnValue(of({ message: 'Success' }));

      component.onSubmit();

      expect(profileServiceMock.updateProfile).toHaveBeenCalledWith({
        companyName: 'Test Company',
        website: undefined,
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: '1234567890',
        phoneCode: '+1',
        ProjectId: '123',
        ParentCompanyId: '456',
        CompanyId: '1'
      });
      expect(toastrServiceMock.success).toHaveBeenCalledWith('Success', 'Success');
      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
    });

    it('should show error message on invalid form submission', () => {
      component.onSubmit();
      expect(component.submitted).toBeTruthy();
      expect(component.formSubmitted).toBeFalsy();
    });

    it('should handle form submission with empty string values', () => {
      const mockFormData = {
        CompanyId: '1',
        firstName: '   ',
        lastName: 'Doe',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        phoneCode: '+1',
        companyName: 'Test Company'
      };

      component.overViewDetailsForm.patchValue(mockFormData);
      component.onSubmit();

      expect(toastrServiceMock.error).toHaveBeenCalledWith('Please enter valid comment.', 'OOPS!');
      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
    });

    it('should handle form submission with empty company name', () => {
      const mockFormData = {
        CompanyId: '1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        phoneCode: '+1',
        companyName: '   '
      };

      component.overViewDetailsForm.patchValue(mockFormData);
      component.onSubmit();

      expect(toastrServiceMock.error).toHaveBeenCalledWith('Please enter valid comment.', 'OOPS!');
      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
    });

    it('should handle form submission with empty last name', () => {
      const mockFormData = {
        CompanyId: '1',
        firstName: 'John',
        lastName: '   ',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        phoneCode: '+1',
        companyName: 'Test Company'
      };

      component.overViewDetailsForm.patchValue(mockFormData);
      component.onSubmit();

      expect(toastrServiceMock.error).toHaveBeenCalledWith('Please enter valid comment.', 'OOPS!');
      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
    });

    it('should handle form submission without last name', () => {
      const mockFormData = {
        CompanyId: '1',
        firstName: 'John',
        lastName: null,
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        phoneCode: '+1',
        companyName: 'Test Company'
      };

      component.overViewDetailsForm.patchValue(mockFormData);
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      profileServiceMock.updateProfile.mockReturnValue(of({ message: 'Success' }));

      component.onSubmit();

      expect(profileServiceMock.updateProfile).toHaveBeenCalledWith({
        companyName: 'Test Company',
        website: undefined,
        firstName: 'John',
        lastName: undefined,
        phoneNumber: '1234567890',
        phoneCode: '+1',
        ProjectId: '123',
        ParentCompanyId: '456',
        CompanyId: '1'
      });
    });

    it('should handle update profile error with status code 400', () => {
      const mockFormData = {
        CompanyId: '1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        phoneCode: '+1',
        companyName: 'Test Company'
      };

      const errorResponse = {
        message: {
          statusCode: 400,
          details: [{ field: 'Email already exists' }]
        }
      };

      component.overViewDetailsForm.patchValue(mockFormData);
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      profileServiceMock.updateProfile.mockReturnValue(throwError(errorResponse));
      jest.spyOn(component, 'showError');

      component.onSubmit();

      expect(component.showError).toHaveBeenCalledWith(errorResponse);
      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
    });

    it('should handle update profile error without message', () => {
      const mockFormData = {
        CompanyId: '1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        phoneCode: '+1',
        companyName: 'Test Company'
      };

      const errorResponse = {};

      component.overViewDetailsForm.patchValue(mockFormData);
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      profileServiceMock.updateProfile.mockReturnValue(throwError(errorResponse));

      component.onSubmit();

      expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
    });

    it('should handle update profile error with generic message', () => {
      const mockFormData = {
        CompanyId: '1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        phoneCode: '+1',
        companyName: 'Test Company'
      };

      const errorResponse = {
        message: 'Generic error message'
      };

      component.overViewDetailsForm.patchValue(mockFormData);
      component.ProjectId = '123';
      component.ParentCompanyId = '456';
      profileServiceMock.updateProfile.mockReturnValue(throwError(errorResponse));

      component.onSubmit();

      expect(toastrServiceMock.error).toHaveBeenCalledWith('Generic error message', 'OOPS!');
      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
    });
  });

  describe('Constructor and Initialization', () => {
    it('should initialize component with default values', () => {
      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
      expect(component.userData).toEqual({});
      expect(component.countryCode).toEqual([]);
      expect(component.phoneMask).toBe('00000-00000');
      expect(component.loader).toBeFalsy();
    });

    it('should call getOverView on constructor', () => {
      jest.spyOn(component, 'getOverView');
      // Create new component instance to test constructor
      const newComponent = new OverviewComponent(
        projectServiceMock,
        TestBed.inject(UntypedFormBuilder),
        toastrServiceMock,
        profileServiceMock,
        authServiceMock,
        deliveryServiceMock
      );
      expect(newComponent.getOverView).toHaveBeenCalled();
    });

    it('should subscribe to projectService.projectParent and call getOverView when data is available', () => {
      const mockProjectData = {
        ProjectId: '123',
        ParentCompanyId: '456'
      };

      jest.spyOn(component, 'getOverView');
      projectServiceMock.projectParent.next(mockProjectData);

      expect(component.ProjectId).toBe('123');
      expect(component.ParentCompanyId).toBe('456');
      expect(component.getOverView).toHaveBeenCalled();
    });

    it('should not call getOverView when projectParent data is null', () => {
      jest.spyOn(component, 'getOverView');
      projectServiceMock.projectParent.next(null);
      expect(component.getOverView).not.toHaveBeenCalled();
    });

    it('should subscribe to profileService.refreshData and call getOverView when true', () => {
      jest.spyOn(component, 'getOverView');
      profileServiceMock.refreshData.next(true);
      expect(component.getOverView).toHaveBeenCalled();
    });

    it('should not call getOverView when refreshData is false', () => {
      jest.spyOn(component, 'getOverView');
      profileServiceMock.refreshData.next(false);
      expect(component.getOverView).not.toHaveBeenCalled();
    });
  });

  describe('getOverView - Extended Tests', () => {
    it('should handle response with ParentCompany data when Company is missing', () => {
      const mockResponse = {
        data: {
          User: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phoneNumber: '1234567890'
          },
          ParentCompany: {
            Company: [{
              id: '1',
              companyName: 'Parent Company',
              address: '123 Parent St'
            }]
          }
        }
      };

      profileServiceMock.getOverView.mockReturnValue(of(mockResponse));
      component.ProjectId = '123';
      component.ParentCompanyId = '456';

      component.getOverView();

      expect(component.userData.Company).toEqual(mockResponse.data.ParentCompany.Company[0]);
    });

    it('should set default phone code when user phone code is missing', () => {
      const mockResponse = {
        data: {
          User: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phoneNumber: '1234567890'
          },
          Company: {
            id: '1',
            companyName: 'Test Company'
          }
        }
      };

      profileServiceMock.getOverView.mockReturnValue(of(mockResponse));
      component.ProjectId = '123';
      component.ParentCompanyId = '456';

      component.getOverView();

      expect(component.userData.User.phoneCode).toBe('+1');
      expect(component.overViewDetailsForm.get('phoneCode').value).toBe('+1');
    });

    it('should call profileService.updatedOverView with userData', () => {
      const mockResponse = {
        data: {
          User: { firstName: 'John' },
          Company: { id: '1' }
        }
      };

      profileServiceMock.getOverView.mockReturnValue(of(mockResponse));
      component.ProjectId = '123';
      component.ParentCompanyId = '456';

      component.getOverView();

      expect(profileServiceMock.updatedOverView).toHaveBeenCalledWith(mockResponse.data);
    });

    it('should handle getOverView error', () => {
      profileServiceMock.getOverView.mockReturnValue(throwError('API Error'));
      component.ProjectId = '123';
      component.ParentCompanyId = '456';

      component.getOverView();

      expect(component.loader).toBeTruthy(); // Should remain true on error
    });
  });

  describe('getCountryCode', () => {
    it('should populate countryCode array with country data', () => {
      component.getCountryCode();

      expect(component.countryCode.length).toBeGreaterThan(0);
      expect(component.countryCode[0]).toHaveProperty('countryDialCode');
      expect(component.countryCode[0]).toHaveProperty('name');
    });

    it('should sort countries by name', () => {
      component.getCountryCode();

      const names = component.countryCode.map(country => country.name);
      const sortedNames = [...names].sort();
      expect(names).toEqual(sortedNames);
    });
  });

  describe('checkStringEmptyValues', () => {
    it('should return true for empty firstName', () => {
      const formValue = {
        firstName: '   ',
        lastName: 'Doe',
        companyName: 'Test Company',
        address: '123 Main St'
      };

      expect(component.checkStringEmptyValues(formValue)).toBeTruthy();
    });

    it('should return true for empty companyName', () => {
      const formValue = {
        firstName: 'John',
        lastName: 'Doe',
        companyName: '   ',
        address: '123 Main St'
      };

      expect(component.checkStringEmptyValues(formValue)).toBeTruthy();
    });

    it('should return true for empty lastName when lastName exists', () => {
      const formValue = {
        firstName: 'John',
        lastName: '   ',
        companyName: 'Test Company',
        address: '123 Main St'
      };

      expect(component.checkStringEmptyValues(formValue)).toBeTruthy();
    });

    it('should return false for valid form values', () => {
      const formValue = {
        firstName: 'John',
        lastName: 'Doe',
        companyName: 'Test Company',
        address: '123 Main St'
      };

      expect(component.checkStringEmptyValues(formValue)).toBeFalsy();
    });

    it('should return false when lastName is null or undefined', () => {
      const formValue = {
        firstName: 'John',
        lastName: null,
        companyName: 'Test Company',
        address: '123 Main St'
      };

      expect(component.checkStringEmptyValues(formValue)).toBeFalsy();
    });
  });

  describe('showError', () => {
    it('should display error message from error details', () => {
      const errorObj = {
        message: {
          details: [{ field: 'Email already exists' }]
        }
      };

      component.showError(errorObj);

      expect(toastrServiceMock.error).toHaveBeenCalledWith('Email already exists');
      expect(component.submitted).toBeFalsy();
      expect(component.formSubmitted).toBeFalsy();
    });
  });
});
