import { TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { DeliveryService } from './delivery.service';
import { ApiService } from '../api_base/api.service';
import { Router } from '@angular/router';
import { of, throwError, Subject } from 'rxjs';
import * as FileSaver from 'file-saver';
import * as XLSX from 'xlsx';

// Mock FileSaver and XLSX
jest.mock('file-saver', () => ({
  saveAs: jest.fn()
}));

jest.mock('xlsx', () => ({
  utils: {
    json_to_sheet: jest.fn().mockReturnValue({})
  },
  write: jest.fn().mockReturnValue(new ArrayBuffer(8)),
  WorkBook: {}
}));

describe('DeliveryService', (): void => {
  let service: DeliveryService;
  let apiServiceMock: jest.Mocked<ApiService>;
  let routerMock: jest.Mocked<Router>;

  beforeEach((): void => {
    // Create mocks
    apiServiceMock = {
      post: jest.fn(),
      get: jest.fn(),
      put: jest.fn(),
      requestImage: jest.fn(),
      getExcel: jest.fn(),
      getExcel1: jest.fn(),
      getByHeadersFile: jest.fn()
    } as unknown as jest.Mocked<ApiService>;

    routerMock = {
      navigate: jest.fn()
    } as unknown as jest.Mocked<Router>;

    TestBed.configureTestingModule({
      imports: [
        RouterTestingModule.withRoutes([]),
        HttpClientTestingModule,
      ],
      providers: [
        DeliveryService,
        { provide: ApiService, useValue: apiServiceMock },
        { provide: Router, useValue: routerMock }
      ]
    });

    service = TestBed.inject(DeliveryService);
  });

  it('should be created', (): void => {
    expect(service).toBeTruthy();
  });

  describe('BehaviorSubject methods', (): void => {
    it('should update login user data', (): void => {
      // Arrange
      const testData = { id: 1, name: 'Test User' };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.loginUser.subscribe(data => {
        result = data;
      });

      // Act
      service.updateLoginUser(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update delivery request ID', (): void => {
      // Arrange
      const testData = 123;
      let result: any;

      // Subscribe to the BehaviorSubject
      service.DeliveryRequestId.subscribe(data => {
        result = data;
      });

      // Act
      service.updatedDeliveryId(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update inspection request ID', (): void => {
      // Arrange
      const testData = 456;
      let result: any;

      // Subscribe to the BehaviorSubject
      service.InspectionRequestId.subscribe(data => {
        result = data;
      });

      // Act
      service.updatedInspectionId(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update all calendar response data', (): void => {
      // Arrange
      const testData = { events: [{ id: 1, title: 'Test Event' }] };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.AllCalendarRespData.subscribe(data => {
        result = data;
      });

      // Act
      service.updateAllCalendarRespData(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update history with action', (): void => {
      // Arrange
      const testData = { id: 1, name: 'Test Data' };
      const testAction = 'create';
      let result: any;

      // Subscribe to the BehaviorSubject
      service.refresh.subscribe(data => {
        result = data;
      });

      // Act
      service.updatedHistory(testData, testAction);

      // Assert
      expect(result).toEqual({ ...testData, action: testAction });
    });
  });

  describe('API methods for delivery requests', (): void => {
    it('should call createNDR with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        deliveryDate: '2023-01-01',
        description: 'Test Delivery'
      };
      const mockResponse = { success: true, data: { id: 123 } };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.createNDR(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('delivery/new_request/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call editNDR with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        id: 123,
        deliveryDate: '2023-01-01',
        description: 'Updated Delivery'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.editNDR(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('delivery/edit_request/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call updateRequest with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        ids: [123, 456],
        status: 'approved'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateRequest(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('delivery/edit_multiple_request/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call updateStatus with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        id: 123,
        status: 'approved'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateStatus(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('delivery/update_status', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('API methods for inspection requests', (): void => {
    it('should call createInspectionNDR with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        inspectionDate: '2023-01-01',
        description: 'Test Inspection'
      };
      const mockResponse = { success: true, data: { id: 123 } };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.createInspectionNDR(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('inspection/new_request/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call editInspectionNDR with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        id: 123,
        inspectionDate: '2023-01-01',
        description: 'Updated Inspection'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.editInspectionNDR(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('inspection/edit_request/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call updateInspectionStatus with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        id: 123,
        status: 'approved'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateInspectionStatus(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('inspection/update_status', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('API methods for crane requests', (): void => {
    it('should call createCraneRequest with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        craneDate: '2023-01-01',
        description: 'Test Crane Request'
      };
      const mockResponse = { success: true, data: { id: 123 } };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.createCraneRequest(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('crane_request/create_crane_request/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call editCraneRequest with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        id: 123,
        craneDate: '2023-01-01',
        description: 'Updated Crane Request'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.editCraneRequest(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('crane_request/edit_crane_request/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call updateCraneRequestStatus with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        id: 123,
        status: 'approved'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateCraneRequestStatus(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('crane_request/update_crane_request_status', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('API methods for concrete requests', (): void => {
    it('should call createConcreteRequest with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        concreteDate: '2023-01-01',
        description: 'Test Concrete Request'
      };
      const mockResponse = { success: true, data: { id: 123 } };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.createConcreteRequest(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('concrete_request/create_concrete_request/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call editConcreteRequest with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        id: 123,
        concreteDate: '2023-01-01',
        description: 'Updated Concrete Request'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.editConcreteRequest(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('concrete_request/edit_concrete_request/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call updateConcreteRequestStatus with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        id: 123,
        status: 'approved'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateConcreteRequestStatus(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('concrete_request/update_concrete_request_status', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('API methods for attachments', (): void => {
    it('should call attachement with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        DeliveryRequestId: 123,
        ParentCompanyId: 456
      };
      const mockPayload = new FormData();
      const mockResponse = { success: true };

      apiServiceMock.requestImage.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.attachement(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.requestImage).toHaveBeenCalledWith(
        `attachement/add_attachement/${mockParams.DeliveryRequestId}/${mockParams.ParentCompanyId}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call inspectionAttachement with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        InspectionRequestId: 123,
        ParentCompanyId: 456
      };
      const mockPayload = new FormData();
      const mockResponse = { success: true };

      apiServiceMock.requestImage.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.inspectionAttachement(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.requestImage).toHaveBeenCalledWith(
        `attachement/add_inspection_attachement/${mockParams.InspectionRequestId}/${mockParams.ParentCompanyId}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call removeAttachement with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        id: 123,
        ParentCompanyId: 456
      };
      const mockResponse = { success: true };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.removeAttachement(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`attachement/remove_attachement/${mockParams.id}/${mockParams.ParentCompanyId}`);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('API methods for comments', (): void => {
    it('should call createComment with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        requestId: 123,
        comment: 'Test comment'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.createComment(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('comment/create_comment/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call createInspectionComment with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        requestId: 123,
        comment: 'Test inspection comment'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.createInspectionComment(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('comment/create_inspection_comment/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call createCraneRequestComment with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        requestId: 123,
        comment: 'Test crane comment'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.createCraneRequestComment(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('crane_request_comment/create_crane_request_comment/', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Export methods', (): void => {
    it('should call exportAsExcelFile correctly', (): void => {
      // Arrange
      const mockJson = [{ name: 'Test', value: 123 }];
      const mockFileName = 'test-file';

      // Act
      service.exportAsExcelFile(mockJson, mockFileName);

      // Assert
      expect(XLSX.utils.json_to_sheet).toHaveBeenCalledWith(mockJson);
      expect(XLSX.write).toHaveBeenCalled();
      expect(FileSaver.saveAs).toHaveBeenCalled();
    });

    it('should call saveAsExcelFile correctly', (): void => {
      // Arrange
      const mockBuffer = new ArrayBuffer(8);
      const mockFileName = 'test-file';
      const dateNow = Date.now();
      jest.spyOn(Date.prototype, 'getTime').mockReturnValue(dateNow);

      // Act
      service.saveAsExcelFile(mockBuffer, mockFileName);

      // Assert
      expect(FileSaver.saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        `${mockFileName}_${dateNow}.xlsx`
      );
    });

    it('should call saveAsPdfFile correctly', (): void => {
      // Arrange
      const mockBuffer = new ArrayBuffer(8);
      const mockFileName = 'test-file';
      const dateNow = Date.now();
      jest.spyOn(Date.prototype, 'getTime').mockReturnValue(dateNow);

      // Act
      service.saveAsPdfFile(mockBuffer, mockFileName);

      // Assert
      expect(FileSaver.saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        `${mockFileName}_${dateNow}.pdf`
      );
    });
  });

  describe('Error handling for API methods', (): void => {
    it('should handle error in createNDR', (): void => {
      // Arrange
      const mockPayload = {
        deliveryDate: '2023-01-01',
        description: 'Test Delivery'
      };
      const mockError = new Error('API Error');
      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.createNDR(mockPayload).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        }
      });
    });

    it('should handle error in editNDR', (): void => {
      // Arrange
      const mockPayload = {
        id: 123,
        deliveryDate: '2023-01-01',
        description: 'Updated Delivery'
      };
      const mockError = new Error('API Error');
      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.editNDR(mockPayload).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        }
      });
    });

    it('should handle error in updateRequest', (): void => {
      // Arrange
      const mockPayload = {
        ids: [123, 456],
        status: 'approved'
      };
      const mockError = new Error('API Error');
      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.updateRequest(mockPayload).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        }
      });
    });
  });

  describe('Edge cases for BehaviorSubject methods', (): void => {
    it('should handle null data in updateLoginUser', (): void => {
      // Arrange
      let result: any;
      service.loginUser.subscribe(data => {
        result = data;
      });

      // Act
      service.updateLoginUser(null);

      // Assert
      expect(result).toBeNull();
    });

    it('should handle undefined data in updatedDeliveryId', (): void => {
      // Arrange
      let result: any;
      service.DeliveryRequestId.subscribe(data => {
        result = data;
      });

      // Act
      service.updatedDeliveryId(undefined);

      // Assert
      expect(result).toBeUndefined();
    });

    it('should handle empty object in updateAllCalendarRespData', (): void => {
      // Arrange
      let result: any;
      service.AllCalendarRespData.subscribe(data => {
        result = data;
      });

      // Act
      service.updateAllCalendarRespData({});

      // Assert
      expect(result).toEqual({});
    });
  });

  describe('Additional API method scenarios', (): void => {
    it('should handle empty payload in createNDR', (): void => {
      // Arrange
      const mockPayload = {};
      const mockResponse = { success: true, data: { id: 123 } };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.createNDR(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('delivery/new_request/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should handle multiple status updates in updateRequest', (): void => {
      // Arrange
      const mockPayload = {
        ids: [123, 456, 789],
        status: 'rejected'
      };
      const mockResponse = { success: true };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateRequest(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('delivery/edit_multiple_request/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should handle attachment with empty FormData', (): void => {
      // Arrange
      const mockParams = {
        DeliveryRequestId: 123,
        ParentCompanyId: 456
      };
      const mockPayload = new FormData();
      const mockResponse = { success: true };
      apiServiceMock.requestImage.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.attachement(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.requestImage).toHaveBeenCalledWith(
        `attachement/add_attachement/${mockParams.DeliveryRequestId}/${mockParams.ParentCompanyId}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Export method edge cases', (): void => {
    it('should handle empty array in exportAsExcelFile', (): void => {
      // Arrange
      const mockJson: any[] = [];
      const mockFileName = 'test-file';

      // Act
      service.exportAsExcelFile(mockJson, mockFileName);

      // Assert
      expect(XLSX.utils.json_to_sheet).toHaveBeenCalledWith(mockJson);
      expect(XLSX.write).toHaveBeenCalled();
      expect(FileSaver.saveAs).toHaveBeenCalled();
    });

    it('should handle null buffer in saveAsExcelFile', (): void => {
      // Arrange
      const mockBuffer = null;
      const mockFileName = 'test-file';
      const dateNow = Date.now();
      jest.spyOn(Date.prototype, 'getTime').mockReturnValue(dateNow);

      // Act & Assert
      expect(() => service.saveAsExcelFile(mockBuffer as any, mockFileName)).toThrow();
    });

    it('should handle empty filename in saveAsPdfFile', (): void => {
      // Arrange
      const mockBuffer = new ArrayBuffer(8);
      const mockFileName = '';
      const dateNow = Date.now();
      jest.spyOn(Date.prototype, 'getTime').mockReturnValue(dateNow);

      // Act
      service.saveAsPdfFile(mockBuffer, mockFileName);

      // Assert
      expect(FileSaver.saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        `_${dateNow}.pdf`
      );
    });
  });

  describe('Comment method edge cases', (): void => {
    it('should handle empty comment in createComment', (): void => {
      // Arrange
      const mockPayload = {
        requestId: 123,
        comment: ''
      };
      const mockResponse = { success: true };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.createComment(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('comment/create_comment/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should handle null requestId in createInspectionComment', (): void => {
      // Arrange
      const mockPayload = {
        requestId: null,
        comment: 'Test comment'
      };
      const mockResponse = { success: true };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.createInspectionComment(mockPayload as any).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('comment/create_inspection_comment/', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Concurrent operations', (): void => {
    it('should handle multiple concurrent subscriptions to loginUser', (): void => {
      // Arrange
      const testData = { id: 1, name: 'Test User' };
      const results: any[] = [];
      const subscriptions = 3;

      // Act
      for (let i = 0; i < subscriptions; i++) {
        service.loginUser.subscribe(data => {
          if (data && Object.keys(data).length > 0) {
            results.push(data);
          }
        });
      }
      service.updateLoginUser(testData);

      // Assert
      expect(results.length).toBe(subscriptions);
      results.forEach(result => {
        expect(result).toEqual(testData);
      });
    });

    it('should handle multiple concurrent API calls', (): void => {
      // Arrange
      const mockPayloads = [
        { id: 1, status: 'approved' },
        { id: 2, status: 'rejected' },
        { id: 3, status: 'pending' }
      ];
      const mockResponses = mockPayloads.map(payload => ({ success: true, data: payload }));
      mockResponses.forEach((response, index) => {
        apiServiceMock.post.mockReturnValueOnce(of(response));
      });

      // Act
      const results: any[] = [];
      mockPayloads.forEach(payload => {
        service.updateStatus(payload).subscribe(response => {
          results.push(response);
        });
      });

      // Assert
      expect(results.length).toBe(mockPayloads.length);
      results.forEach((result, index) => {
        expect(result).toEqual(mockResponses[index]);
      });
    });
  });

  describe('Status validation', (): void => {
    it('should validate status values in updateRequest', (): void => {
      // Arrange
      const validStatuses = ['approved', 'rejected', 'pending'];
      const invalidStatus = 'invalid_status';
      const mockPayload = {
        ids: [123],
        status: invalidStatus
      };
      const mockResponse = { success: false, error: 'Invalid status' };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateRequest(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(result.success).toBe(false);
      expect(validStatuses).not.toContain(invalidStatus);
    });

    it('should handle different status combinations in updateRequest', (): void => {
      // Arrange
      const statuses = ['approved', 'rejected', 'pending'];
      const results: any[] = [];
      const mockResponses = statuses.map(status => ({ success: true, status }));

      statuses.forEach((status, index) => {
        apiServiceMock.post.mockReturnValueOnce(of(mockResponses[index]));
        const payload = { ids: [123], status };
        service.updateRequest(payload).subscribe(response => {
          results.push(response);
        });
      });

      // Assert
      expect(results.length).toBe(statuses.length);
      results.forEach((result, index) => {
        expect(result.status).toBe(statuses[index]);
      });
    });
  });

  describe('File handling', (): void => {
    it('should validate file types in attachment', (): void => {
      // Arrange
      const mockParams = {
        DeliveryRequestId: 123,
        ParentCompanyId: 456
      };
      const mockPayload = new FormData();
      const file = new File([''], 'test.txt', { type: 'text/plain' });
      mockPayload.append('file', file);
      const mockResponse = { success: false, error: 'Invalid file type' };
      apiServiceMock.requestImage.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.attachement(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(result.success).toBe(false);
    });

    it('should handle large file uploads', (): void => {
      // Arrange
      const mockParams = {
        DeliveryRequestId: 123,
        ParentCompanyId: 456
      };
      const mockPayload = new FormData();
      const largeFile = new File(['x'.repeat(10 * 1024 * 1024)], 'large.txt', { type: 'text/plain' });
      mockPayload.append('file', largeFile);
      const mockResponse = { success: false, error: 'File too large' };
      apiServiceMock.requestImage.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.attachement(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(result.success).toBe(false);
    });
  });

  describe('Subscription cleanup', (): void => {
    it('should cleanup subscriptions when service is destroyed', (): void => {
      // Arrange
      const testData = { id: 1, name: 'Test User' };
      const results: any[] = [];
      const subscription = service.loginUser.subscribe(data => {
        if (data && Object.keys(data).length > 0) {
          results.push(data);
        }
      });

      // Act
      service.updateLoginUser(testData);
      subscription.unsubscribe();
      service.updateLoginUser({ id: 2, name: 'New User' });

      // Assert
      expect(results.length).toBe(1);
      expect(results[0]).toEqual(testData);
    });

    it('should handle multiple subscription cleanup', (): void => {
      // Arrange
      const subscriptions: any[] = [];
      const results: any[] = [];
      const testData = { id: 1, name: 'Test User' };

      // Act
      for (let i = 0; i < 3; i++) {
        const sub = service.loginUser.subscribe(data => {
          if (data && Object.keys(data).length > 0) {
            results.push(data);
          }
        });
        subscriptions.push(sub);
      }
      service.updateLoginUser(testData);
      subscriptions.forEach(sub => sub.unsubscribe());
      service.updateLoginUser({ id: 2, name: 'New User' });

      // Assert
      expect(results.length).toBe(3);
      results.forEach(result => {
        expect(result).toEqual(testData);
      });
    });
  });

  describe('Additional BehaviorSubject methods', (): void => {
    it('should update all calendar permanent response data', (): void => {
      // Arrange
      const testData = { permanentEvents: [{ id: 1, title: 'Permanent Event' }] };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.AllCalendarPermanentRespData.subscribe(data => {
        result = data;
      });

      // Act
      service.updateAllCalendarPermanentRespData(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update status card values', (): void => {
      // Arrange
      const testData = { approved: 5, pending: 3, rejected: 1 };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.AllCalendarStatusCard.subscribe(data => {
        result = data;
      });

      // Act
      service.updateStatusCardValues(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update edit crane request ID', (): void => {
      // Arrange
      const testData = 789;
      let result: any;

      // Subscribe to the BehaviorSubject
      service.EditCraneRequestId.subscribe(data => {
        result = data;
      });

      // Act
      service.updatedEditCraneRequestId(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update edit concrete request ID', (): void => {
      // Arrange
      const testData = 101112;
      let result: any;

      // Subscribe to the BehaviorSubject
      service.EditConcreteRequestId.subscribe(data => {
        result = data;
      });

      // Act
      service.updatedEditConcreteRequestId(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update complete concrete request status', (): void => {
      // Arrange
      const testData = { status: 'completed', id: 123 };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.completeConcreteRequest.subscribe(data => {
        result = data;
      });

      // Act
      service.completeConcreteRequestStatus(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update footer dropdown status', (): void => {
      // Arrange
      const testData = { isOpen: true, selectedOption: 'option1' };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.footerDropdown.subscribe(data => {
        result = data;
      });

      // Act
      service.footerDropdownStatus(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update show footer buttons visibility', (): void => {
      // Arrange
      const testData = { visible: true, buttons: ['save', 'cancel'] };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.showFooterButtons.subscribe(data => {
        result = data;
      });

      // Act
      service.showFooterButtonsVisible(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update refresh count', (): void => {
      // Arrange
      const testData = 5;
      let result: any;

      // Subscribe to the BehaviorSubject
      service.refreshCount.subscribe(data => {
        result = data;
      });

      // Act
      service.updatedRefreshCount(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update notification refresh count', (): void => {
      // Arrange
      const testData = 10;
      let result: any;

      // Subscribe to the BehaviorSubject
      service.refreshnotifyCount.subscribe(data => {
        result = data;
      });

      // Act
      service.notificationRefreshCount(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update state of NDR', (): void => {
      // Arrange
      const testData = { isQueued: true, requestId: 123 };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.isQueuedNDR.subscribe(data => {
        result = data;
      });

      // Act
      service.updateStateOfNDR(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should update state of inspection NDR', (): void => {
      // Arrange
      const testData = { isQueued: false, requestId: 456 };
      let result: any;

      // Subscribe to the BehaviorSubject
      service.isQueuedInspectionNDR.subscribe(data => {
        result = data;
      });

      // Act
      service.updateStateOfInspectionNDR(testData);

      // Assert
      expect(result).toEqual(testData);
    });
  });

  describe('List API methods', (): void => {
    it('should call listNDR with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: 123,
        pageSize: 10,
        pageNo: 1,
        void: false
      };
      const mockPayload = { filter: 'test' };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.listNDR(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `delivery/list_NDR/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.void}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call listInspectionNDR with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: 456,
        pageSize: 20,
        pageNo: 2,
        void: true
      };
      const mockPayload = { filter: 'inspection' };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.listInspectionNDR(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `inspection/list_NDR/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.void}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getCraneRequestList with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: 789,
        pageSize: 15,
        pageNo: 1,
        void: false
      };
      const mockPayload = { filter: 'crane' };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getCraneRequestList(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `crane_request/get_crane_request_list/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.void}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getVoidList with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: 101,
        pageSize: 25,
        pageNo: 3,
        void: true
      };
      const mockPayload = { filter: 'void' };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getVoidList(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `void/get_void_list/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.void}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getConcreteRequestList with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: 202,
        pageSize: 30,
        pageNo: 1,
        void: false
      };
      const mockPayload = { filter: 'concrete' };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getConcreteRequestList(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `concrete_request/get_concrete_request_list/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.void}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call gateList with correct parameters', (): void => {
      // Arrange
      const mockParams = { ProjectId: 303 };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.gateList(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`gates/gate_all_list/${mockParams.ProjectId}`);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Notification API methods', (): void => {
    it('should call listNotification with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        pageSize: 10,
        pageNo: 1
      };
      const mockData = { filter: 'unread' };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.listNotification(mockParams, mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `notification/list_notification/${mockParams.pageSize}/${mockParams.pageNo}`,
        mockData
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call ReadallNotification with correct parameters', (): void => {
      // Arrange
      const mockParams = { ProjectId: 123 };
      const mockResponse = { success: true };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.ReadallNotification(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`delivery/readall_notification/?ProjectId=${mockParams.ProjectId}`);
      expect(result).toEqual(mockResponse);
    });

    it('should call readallNotifications with correct parameters', (): void => {
      // Arrange
      const mockParams = { ProjectId: 456 };
      const mockResponse = { success: true };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.readallNotifications(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`delivery/markall_notification/${mockParams.ProjectId}`);
      expect(result).toEqual(mockResponse);
    });

    it('should call deleteNotification with correct parameters', (): void => {
      // Arrange
      const mockData = {
        id: 123,
        ParentCompanyId: 456
      };
      const mockResponse = { success: true };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.deleteNotification(mockData).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `notification/delete_notification/?id=${mockData.id}&ParentCompanyId=${mockData.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call setReadNotification with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        id: 789,
        ParentCompanyId: 101
      };
      const mockResponse = { success: true };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.setReadNotification(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `notification/read_notification/?id=${mockParams.id}&ParentCompanyId=${mockParams.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Member search API methods', (): void => {
    it('should call searchNewMember with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: 123,
        search: 'john',
        ParentCompanyId: 456
      };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.searchNewMember(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `member/search_member/${mockParams.ProjectId}/${mockParams.search}/${mockParams.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call searchNewMemberForAutoApprove with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: 789,
        search: 'jane',
        ParentCompanyId: 101
      };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.searchNewMemberForAutoApprove(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `member/search_member_auto_approve/${mockParams.ProjectId}/${mockParams.search}/${mockParams.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Update request methods', (): void => {
    it('should call updateInspectionRequest with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        ids: [123, 456],
        status: 'approved'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateInspectionRequest(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('inspection/edit_multiple_request/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call updateCraneRequest with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        ids: [789, 101],
        status: 'rejected'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateCraneRequest(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('crane_request/edit_multiple_request/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call updateConcreteRequest with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        ids: [111, 222],
        status: 'pending'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateConcreteRequest(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('concrete_request/edit_multiple_request/', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Queued request methods', (): void => {
    it('should call editQueuedNDRForm with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        id: 123,
        deliveryDate: '2023-01-01',
        description: 'Updated Queued Delivery'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.editQueuedNDRForm(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('delivery/edit_queued_request/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call editQueuedInspectionNDRForm with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        id: 456,
        inspectionDate: '2023-01-01',
        description: 'Updated Queued Inspection'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.editQueuedInspectionNDRForm(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('inspection/edit_queued_request/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call submitQueuedNDR with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        id: 789,
        status: 'submitted'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.submitQueuedNDR(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('delivery/update_to_current_NDR/', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Error handling for additional API methods', (): void => {
    it('should handle error in listNDR', (): void => {
      // Arrange
      const mockParams = { ProjectId: 123, pageSize: 10, pageNo: 1, void: false };
      const mockPayload = { filter: 'test' };
      const mockError = new Error('Network Error');
      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.listNDR(mockParams, mockPayload).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        }
      });
    });

    it('should handle error in searchNewMember', (): void => {
      // Arrange
      const mockParams = { ProjectId: 123, search: 'john', ParentCompanyId: 456 };
      const mockError = new Error('Search Error');
      apiServiceMock.get.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.searchNewMember(mockParams).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        }
      });
    });

    it('should handle error in updateInspectionRequest', (): void => {
      // Arrange
      const mockPayload = { ids: [123], status: 'approved' };
      const mockError = new Error('Update Error');
      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.updateInspectionRequest(mockPayload).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        }
      });
    });

    it('should handle error in deleteNotification', (): void => {
      // Arrange
      const mockData = { id: 123, ParentCompanyId: 456 };
      const mockError = new Error('Delete Error');
      apiServiceMock.get.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.deleteNotification(mockData).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        }
      });
    });
  });

  describe('Edge cases for API methods', (): void => {
    it('should handle empty search string in searchNewMember', (): void => {
      // Arrange
      const mockParams = { ProjectId: 123, search: '', ParentCompanyId: 456 };
      const mockResponse = { success: true, data: [] };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.searchNewMember(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `member/search_member/${mockParams.ProjectId}/${mockParams.search}/${mockParams.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle zero ProjectId in gateList', (): void => {
      // Arrange
      const mockParams = { ProjectId: 0 };
      const mockResponse = { success: false, error: 'Invalid ProjectId' };
      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.gateList(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(result.success).toBe(false);
    });

    it('should handle large page numbers in listNDR', (): void => {
      // Arrange
      const mockParams = { ProjectId: 123, pageSize: 1000, pageNo: 999, void: false };
      const mockPayload = { filter: 'test' };
      const mockResponse = { success: true, data: [], totalPages: 999 };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.listNDR(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(result.totalPages).toBe(999);
    });

    it('should handle empty ids array in updateRequest', (): void => {
      // Arrange
      const mockPayload = { ids: [], status: 'approved' };
      const mockResponse = { success: false, error: 'No IDs provided' };
      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateRequest(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(result.success).toBe(false);
    });
  });

  describe('BehaviorSubject observables', (): void => {
    it('should provide observable for AllCalendarRespData$', (): void => {
      // Arrange
      const testData = { events: [{ id: 1, title: 'Test Event' }] };
      let result: any;

      // Act
      service.AllCalendarRespData$.subscribe(data => {
        result = data;
      });
      service.updateAllCalendarRespData(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should provide observable for AllCalendarPermanentRespData$', (): void => {
      // Arrange
      const testData = { permanentEvents: [{ id: 1, title: 'Permanent Event' }] };
      let result: any;

      // Act
      service.AllCalendarPermanentRespData$.subscribe(data => {
        result = data;
      });
      service.updateAllCalendarPermanentRespData(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should provide observable for AllCalendarStatusCard$', (): void => {
      // Arrange
      const testData = { approved: 5, pending: 3, rejected: 1 };
      let result: any;

      // Act
      service.AllCalendarStatusCard$.subscribe(data => {
        result = data;
      });
      service.updateStatusCardValues(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should provide observable for dataChanged$', (): void => {
      // Arrange
      const testData = true;
      let result: any;

      // Act
      service.dataChanged$.subscribe(data => {
        result = data;
      });
      service.dataChanged.next(testData);

      // Assert
      expect(result).toEqual(testData);
    });

    it('should provide observable for selectedBookingTypes$', (): void => {
      // Arrange
      const testData = ['delivery', 'inspection', 'crane'];
      let result: any;

      // Act
      service.selectedBookingTypes$.subscribe(data => {
        result = data;
      });
      service.selectedBookingTypes.next(testData);

      // Assert
      expect(result).toEqual(testData);
    });
  });

  describe('Additional missing API methods', (): void => {
    it('should call getMemberRole with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: 123,
        ParentCompanyId: 456
      };
      const mockResponse = { success: true, data: { role: 'admin' } };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getMemberRole(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(`delivery/get_user_role/${mockParams.ProjectId}/${mockParams.ParentCompanyId}`);
      expect(result).toEqual(mockResponse);
    });

    it('should call getAvailableTimeSlots with correct parameters', (): void => {
      // Arrange
      const projectId = 123;
      const mockPayload = { date: '2023-01-01', location: 'test' };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getAvailableTimeSlots(projectId, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(`location/available_timeslots?ProjectId=${projectId}`, mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call updateConcreteRequestStatus with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        id: 123,
        status: 'completed'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateConcreteRequestStatus(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('concrete_request/update_concrete_request_status', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call removeVoid with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        id: 123,
        reason: 'test removal'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.removeVoid(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('void/remove_void', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call checkOverlapping with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        startTime: '09:00',
        endTime: '10:00',
        date: '2023-01-01'
      };
      const mockResponse = { success: true, overlapping: false };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.checkOverlapping(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('calendar/checkOverlapping', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call importBulkNDRTemplate with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        ProjectId: 123,
        ParentCompanyId: 456
      };
      const mockResponse = new ArrayBuffer(8);

      apiServiceMock.getByHeadersFile.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.importBulkNDRTemplate(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.getByHeadersFile).toHaveBeenCalledWith(
        `delivery/sample_delivery_request_template/${mockPayload.ProjectId}/${mockPayload.ParentCompanyId}`,
        ''
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call importCompanyTemplate with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        ProjectId: 789,
        ParentCompanyId: 101
      };
      const mockResponse = new ArrayBuffer(8);

      apiServiceMock.getByHeadersFile.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.importCompanyTemplate(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.getByHeadersFile).toHaveBeenCalledWith(
        `company/sample_company_template/${mockPayload.ProjectId}/${mockPayload.ParentCompanyId}`,
        ''
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call importBulkNDR with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: 123,
        ParentCompanyId: 456
      };
      const mockPayload = new FormData();
      const mockResponse = { success: true };

      apiServiceMock.requestImage.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.importBulkNDR(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.requestImage).toHaveBeenCalledWith(
        `delivery/bulk_upload_delivery_request/${mockParams.ProjectId}/${mockParams.ParentCompanyId}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call deleteQueuedNdr with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        id: 123,
        reason: 'test deletion'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.deleteQueuedNdr(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('delivery/delete_queued_Ndr', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call deleteConcreteRequest with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        id: 456,
        reason: 'test deletion'
      };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.deleteConcreteRequest(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('concrete_request/delete_concrete_request', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Additional BehaviorSubject update methods', (): void => {
    it('should update various BehaviorSubject properties', (): void => {
      // Test dataChanged
      let dataChangedResult: any;
      service.dataChanged$.subscribe(data => {
        dataChangedResult = data;
      });
      service.dataChanged.next(true);
      expect(dataChangedResult).toBe(true);

      // Test selectedBookingTypes
      let bookingTypesResult: any;
      service.selectedBookingTypes$.subscribe(data => {
        bookingTypesResult = data;
      });
      service.selectedBookingTypes.next(['delivery', 'inspection']);
      expect(bookingTypesResult).toEqual(['delivery', 'inspection']);

      // Test getCurrentStatus
      let statusResult: any;
      service.getCurrentStatus.subscribe(data => {
        statusResult = data;
      });
      service.getCurrentStatus.next('active');
      expect(statusResult).toBe('active');

      // Test getCurrentCraneRequestStatus
      let craneStatusResult: any;
      service.getCurrentCraneRequestStatus.subscribe(data => {
        craneStatusResult = data;
      });
      service.getCurrentCraneRequestStatus.next('pending');
      expect(craneStatusResult).toBe('pending');

      // Test getCurrentConcreteRequestStatus
      let concreteStatusResult: any;
      service.getCurrentConcreteRequestStatus.subscribe(data => {
        concreteStatusResult = data;
      });
      service.getCurrentConcreteRequestStatus.next('approved');
      expect(concreteStatusResult).toBe('approved');
    });
  });

  describe('Comprehensive API method coverage', (): void => {
    it('should call decryption with correct parameters', (): void => {
      // Arrange
      const mockPayload = { encryptedData: 'test123' };
      const mockResponse = { success: true, data: 'decrypted' };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.decryption(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('delivery/decrypt', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call weeklyDeliveryList with correct parameters', (): void => {
      // Arrange
      const mockParams = { ProjectId: 123, void: false };
      const mockPayload = { startDate: '2023-01-01', endDate: '2023-01-07' };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.weeklyDeliveryList(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `reports/weekly_calendar_request/${mockParams.ProjectId}/${mockParams.void}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call noAuthWeeklyDeliveryList with correct parameters', (): void => {
      // Arrange
      const mockParams = { ProjectId: 456, void: true };
      const mockPayload = { startDate: '2023-01-01', endDate: '2023-01-07' };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.noAuthWeeklyDeliveryList(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `reports/weekly_calendar_request/no_auth/${mockParams.ProjectId}/${mockParams.void}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call exportWeeklyCalendarRequest with correct parameters', (): void => {
      // Arrange
      const mockParams = { ProjectId: 789, void: false };
      const mockPayload = { format: 'excel' };
      const mockResponse = { success: true, data: 'exported' };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.exportWeeklyCalendarRequest(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `reports/export_weekly_calendar/${mockParams.ProjectId}/${mockParams.void}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getDefinable with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: 123,
        pageSize: 10,
        pageNo: 1,
        sort: 'asc',
        export: false,
        sortByField: 'name'
      };
      const mockPayload = { filter: 'test' };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getDefinable(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `definable/get_definable/${mockParams.ProjectId}/${mockParams.pageSize}/${mockParams.pageNo}/${mockParams.sort}/${mockParams.export}/${mockParams.sortByField}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call exportDefinable with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: 456,
        sort: 'desc',
        ParentCompanyId: 789
      };
      const mockResponse = new ArrayBuffer(8);

      apiServiceMock.getExcel.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.exportDefinable(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.getExcel).toHaveBeenCalledWith(
        `definable/export_definable/${mockParams.ProjectId}/${mockParams.sort}/${mockParams.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call importDefinable with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: 123,
        ParentCompanyId: 456
      };
      const mockPayload = new FormData();
      const mockResponse = { success: true };

      apiServiceMock.requestImage.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.importDefinable(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.requestImage).toHaveBeenCalledWith(
        `definable/create_definable/${mockParams.ProjectId}/${mockParams.ParentCompanyId}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call importCompany with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: 789,
        ParentCompanyId: 101
      };
      const mockPayload = new FormData();
      const mockResponse = { success: true };

      apiServiceMock.requestImage.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.importCompany(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.requestImage).toHaveBeenCalledWith(
        `company/create_company/${mockParams.ProjectId}/${mockParams.ParentCompanyId}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Additional BehaviorSubject update methods coverage', (): void => {
    it('should update all remaining BehaviorSubject properties', (): void => {
      // Test inspectionUpdated
      let inspectionResult: any;
      service.inspectionUpdated.subscribe(data => {
        inspectionResult = data;
      });
      service.inspectionUpdated.next({ id: 1, updated: true });
      expect(inspectionResult).toEqual({ id: 1, updated: true });

      // Test inspectionUpdated1
      let inspection1Result: any;
      service.inspectionUpdated1.subscribe(data => {
        inspection1Result = data;
      });
      service.inspectionUpdated1.next({ id: 2, updated: true });
      expect(inspection1Result).toEqual({ id: 2, updated: true });

      // Test getInspectionCurrentStatus
      let inspectionStatusResult: any;
      service.getInspectionCurrentStatus.subscribe(data => {
        inspectionStatusResult = data;
      });
      service.getInspectionCurrentStatus.next('pending');
      expect(inspectionStatusResult).toBe('pending');

      // Test fetchData
      let fetchDataResult: any;
      service.fetchData.subscribe(data => {
        fetchDataResult = data;
      });
      service.fetchData.next({ fetch: true });
      expect(fetchDataResult).toEqual({ fetch: true });

      // Test fetchData1
      let fetchData1Result: any;
      service.fetchData1.subscribe(data => {
        fetchData1Result = data;
      });
      service.fetchData1.next({ fetch1: true });
      expect(fetchData1Result).toEqual({ fetch1: true });

      // Test fetchConcreteData
      let fetchConcreteResult: any;
      service.fetchConcreteData.subscribe(data => {
        fetchConcreteResult = data;
      });
      service.fetchConcreteData.next({ concrete: true });
      expect(fetchConcreteResult).toEqual({ concrete: true });

      // Test fetchConcreteData1
      let fetchConcrete1Result: any;
      service.fetchConcreteData1.subscribe(data => {
        fetchConcrete1Result = data;
      });
      service.fetchConcreteData1.next({ concrete1: true });
      expect(fetchConcrete1Result).toEqual({ concrete1: true });

      // Test refresh1
      let refresh1Result: any;
      service.refresh1.subscribe(data => {
        refresh1Result = data;
      });
      service.refresh1.next({ refresh: true });
      expect(refresh1Result).toEqual({ refresh: true });
    });
  });

  describe('Final missing API methods for 90% coverage', (): void => {
    it('should call getUpcomingList with correct parameters', (): void => {
      // Arrange
      const mockParams = { ProjectId: 123, limit: 10 };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getUpcomingList(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith('concrete_request/upcoming_request_list', mockParams);
      expect(result).toEqual(mockResponse);
    });

    it('should call createVoid with correct parameters', (): void => {
      // Arrange
      const mockPayload = { reason: 'test void', date: '2023-01-01' };
      const mockResponse = { success: true, data: { id: 123 } };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.createVoid(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('void/create_void', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call createInspectionVoid with correct parameters', (): void => {
      // Arrange
      const mockPayload = { reason: 'inspection void', date: '2023-01-01' };
      const mockResponse = { success: true, data: { id: 456 } };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.createInspectionVoid(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('void/create_inspection_void', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call addCraneRequestToVoid with correct parameters', (): void => {
      // Arrange
      const mockPayload = { craneRequestId: 123, voidId: 456 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.addCraneRequestToVoid(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('void/add_crane_request_to_void', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call addConcreteRequestToVoid with correct parameters', (): void => {
      // Arrange
      const mockPayload = { concreteRequestId: 789, voidId: 101 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.addConcreteRequestToVoid(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('void/add_concrete_request_to_void', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call saveReportWeeklyCalendarRequest with correct parameters', (): void => {
      // Arrange
      const mockParams = { ProjectId: 123, void: false };
      const mockPayload = { reportName: 'Weekly Report', format: 'pdf' };
      const mockResponse = { success: true, reportId: 456 };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.saveReportWeeklyCalendarRequest(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(
        `reports/saved/export_weekly_calendar/${mockParams.ProjectId}/${mockParams.void}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call exportWeeklyCalendarRequestInExcelFormat with correct parameters', (): void => {
      // Arrange
      const mockParams = { ProjectId: 789, void: true };
      const mockPayload = { startDate: '2023-01-01', endDate: '2023-01-07' };
      const mockResponse = new ArrayBuffer(8);

      // Mock getExcel1 method
      apiServiceMock.getExcel1 = jest.fn().mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.exportWeeklyCalendarRequestInExcelFormat(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.getExcel1).toHaveBeenCalledWith(
        `reports/export_weekly_calendar/${mockParams.ProjectId}/${mockParams.void}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Error handling for final missing methods', (): void => {
    it('should handle error in getUpcomingList', (): void => {
      // Arrange
      const mockParams = { ProjectId: 123 };
      const mockError = new Error('API Error');
      apiServiceMock.get.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.getUpcomingList(mockParams).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        }
      });
    });

    it('should handle error in createVoid', (): void => {
      // Arrange
      const mockPayload = { reason: 'test void' };
      const mockError = new Error('Create Error');
      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.createVoid(mockPayload).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        }
      });
    });

    it('should handle error in createInspectionVoid', (): void => {
      // Arrange
      const mockPayload = { reason: 'inspection void' };
      const mockError = new Error('Create Error');
      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.createInspectionVoid(mockPayload).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        }
      });
    });

    it('should handle error in addCraneRequestToVoid', (): void => {
      // Arrange
      const mockPayload = { craneRequestId: 123, voidId: 456 };
      const mockError = new Error('Add Error');
      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.addCraneRequestToVoid(mockPayload).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        }
      });
    });

    it('should handle error in addConcreteRequestToVoid', (): void => {
      // Arrange
      const mockPayload = { concreteRequestId: 789, voidId: 101 };
      const mockError = new Error('Add Error');
      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.addConcreteRequestToVoid(mockPayload).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        }
      });
    });

    it('should handle error in saveReportWeeklyCalendarRequest', (): void => {
      // Arrange
      const mockParams = { ProjectId: 123, void: false };
      const mockPayload = { reportName: 'Test Report' };
      const mockError = new Error('Save Error');
      apiServiceMock.post.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.saveReportWeeklyCalendarRequest(mockParams, mockPayload).subscribe({
        error: (error) => {
          expect(error).toBe(mockError);
        }
      });
    });
  });

  describe('Comprehensive coverage for remaining methods', (): void => {
    it('should call getCraneRequestAttachements with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        CraneRequestId: 123,
        ParentCompanyId: 456,
        ProjectId: 789
      };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getCraneRequestAttachements(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `crane_request_attachment/get_crane_request_attachements/${mockParams.CraneRequestId}/${mockParams.ParentCompanyId}/${mockParams.ProjectId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getConcreteRequestAttachments with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ConcreteRequestId: 123,
        ParentCompanyId: 456,
        ProjectId: 789
      };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getConcreteRequestAttachments(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `concrete_request_attachment/get_concrete_request_attachments/${mockParams.ConcreteRequestId}/${mockParams.ParentCompanyId}/${mockParams.ProjectId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getHistory with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        DeliveryRequestId: 123,
        ParentCompanyId: 456
      };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getHistory(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `history/get_history/${mockPayload.DeliveryRequestId}/${mockPayload.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getInspectionHistory with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        inspectionRequestId: 789,
        ParentCompanyId: 101
      };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getInspectionHistory(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `history/get_inspection_history/${mockPayload.inspectionRequestId}/${mockPayload.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getDeliveryRequestComment with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        DeliveryRequestId: 123,
        ParentCompanyId: 456,
        ProjectId: 789
      };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getDeliveryRequestComment(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `comment/get_comment/${mockPayload.DeliveryRequestId}/${mockPayload.ParentCompanyId}/${mockPayload.ProjectId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getInspectionRequestComment with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        InspectionRequestId: 123,
        ParentCompanyId: 456,
        ProjectId: 789
      };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getInspectionRequestComment(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `comment/get_inspection_comment/${mockPayload.InspectionRequestId}/${mockPayload.ParentCompanyId}/${mockPayload.ProjectId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getConcreteRequestDropdownData with correct parameters', (): void => {
      // Arrange
      const mockPayload = { filter: 'dropdown' };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getConcreteRequestDropdownData(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith('concrete_request/concrete_dropdown_detail', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call getConcreteRequestDetail with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        ConcreteRequestId: 123,
        ProjectId: 456,
        ParentCompanyId: 789
      };
      const mockResponse = { success: true, data: {} };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getConcreteRequestDetail(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `concrete_request/get_single_Concrete_request/${mockPayload.ConcreteRequestId}/${mockPayload.ProjectId}/${mockPayload.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getCraneRequestComment with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        CraneRequestId: 123,
        ParentCompanyId: 456,
        ProjectId: 789
      };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getCraneRequestComment(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `crane_request_comment/get_crane_request_comments/${mockPayload.CraneRequestId}/${mockPayload.ParentCompanyId}/${mockPayload.ProjectId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getConcreteRequestComment with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        ConcreteRequestId: 123,
        ParentCompanyId: 456,
        ProjectId: 789
      };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getConcreteRequestComment(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `concrete_request_comment/get_concrete_request_comments/${mockPayload.ConcreteRequestId}/${mockPayload.ParentCompanyId}/${mockPayload.ProjectId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getCraneRequestHistory with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        CraneRequestId: 123,
        ParentCompanyId: 456,
        ProjectId: 789
      };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getCraneRequestHistory(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `crane_request_history/get_crane_request_histories/${mockPayload.CraneRequestId}/${mockPayload.ParentCompanyId}/${mockPayload.ProjectId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getConcreteRequestHistory with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        ConcreteRequestId: 123,
        ParentCompanyId: 456,
        ProjectId: 789
      };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getConcreteRequestHistory(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `concrete_request_history/get_concrete_request_histories/${mockPayload.ConcreteRequestId}/${mockPayload.ParentCompanyId}/${mockPayload.ProjectId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call updateDefinable with correct parameters', (): void => {
      // Arrange
      const mockParams = { ProjectId: 123 };
      const mockPayload = { name: 'Updated Definable' };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateDefinable(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith(`definable/update_definable/${mockParams.ProjectId}`, mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call deleteDefinable with correct parameters', (): void => {
      // Arrange
      const mockPayload = { id: 123 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.deleteDefinable(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('definable/delete_definable', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call createComment with correct parameters', (): void => {
      // Arrange
      const mockPayload = { comment: 'Test comment', requestId: 123 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.createComment(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('comment/create_comment/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call createInspectionComment with correct parameters', (): void => {
      // Arrange
      const mockPayload = { comment: 'Inspection comment', requestId: 456 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.createInspectionComment(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('comment/create_inspection_comment/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call createCraneRequestComment with correct parameters', (): void => {
      // Arrange
      const mockPayload = { comment: 'Crane comment', requestId: 789 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.createCraneRequestComment(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('crane_request_comment/create_crane_request_comment/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call createConcreteRequestComment with correct parameters', (): void => {
      // Arrange
      const mockPayload = { comment: 'Concrete comment', requestId: 101 };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.createConcreteRequestComment(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('concrete_request_comment/create_concrete_request_comment/', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call getNDRData with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        DeliveryRequestId: 123,
        ParentCompanyId: 456
      };
      const mockResponse = { success: true, data: {} };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getNDRData(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `delivery/get_single_NDR/${mockParams.DeliveryRequestId}/${mockParams.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getInspectionNDRData with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        inspectionRequestId: 789,
        ParentCompanyId: 101
      };
      const mockResponse = { success: true, data: {} };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getInspectionNDRData(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `inspection/get_single_NDR/${mockParams.inspectionRequestId}/${mockParams.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getEquipmentCraneRequest with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        CraneRequestId: 123,
        ProjectId: 456,
        ParentCompanyId: 789
      };
      const mockResponse = { success: true, data: {} };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getEquipmentCraneRequest(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `crane_request/get_single_crane_request/${mockParams.CraneRequestId}/${mockParams.ProjectId}/${mockParams.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Status update methods', (): void => {
    it('should call updateStatus with correct parameters', (): void => {
      // Arrange
      const mockPayload = { id: 123, status: 'approved' };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateStatus(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('delivery/update_status', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call updateInspectionStatus with correct parameters', (): void => {
      // Arrange
      const mockPayload = { id: 456, status: 'completed' };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateInspectionStatus(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('inspection/update_status', mockPayload);
      expect(result).toEqual(mockResponse);
    });

    it('should call updateCraneRequestStatus with correct parameters', (): void => {
      // Arrange
      const mockPayload = { id: 789, status: 'pending' };
      const mockResponse = { success: true };

      apiServiceMock.post.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateCraneRequestStatus(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.post).toHaveBeenCalledWith('crane_request/update_crane_request_status', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('File export methods', (): void => {
    it('should call exportAsExcelFile and trigger saveAsExcelFile', (): void => {
      // Arrange
      const mockData = [{ id: 1, name: 'Test' }];
      const mockFileName = 'test-export';

      // Spy on saveAsExcelFile method
      const saveAsExcelFileSpy = jest.spyOn(service, 'saveAsExcelFile').mockImplementation(() => {});

      // Act
      service.exportAsExcelFile(mockData, mockFileName);

      // Assert
      expect(saveAsExcelFileSpy).toHaveBeenCalled();
    });

    it('should handle saveAsExcelFile method call', (): void => {
      // Arrange
      const mockBuffer = new ArrayBuffer(8);
      const mockFileName = 'test-file';

      // Act & Assert - Just verify the method exists and can be called
      expect(() => service.saveAsExcelFile(mockBuffer, mockFileName)).not.toThrow();
    });

    it('should handle saveAsPdfFile method call', (): void => {
      // Arrange
      const mockBuffer = new ArrayBuffer(8);
      const mockFileName = 'test-pdf';

      // Act & Assert - Just verify the method exists and can be called
      expect(() => service.saveAsPdfFile(mockBuffer, mockFileName)).not.toThrow();
    });
  });

  describe('Final attachment methods for 90% coverage', (): void => {
    it('should call addCraneRequestAttachment with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        CraneRequestId: 123,
        ParentCompanyId: 456,
        ProjectId: 789
      };
      const mockPayload = new FormData();
      const mockResponse = { success: true };

      apiServiceMock.requestImage.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.addCraneRequestAttachment(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.requestImage).toHaveBeenCalledWith(
        `crane_request_attachment/add_crane_request_attachement/${mockParams.CraneRequestId}/${mockParams.ParentCompanyId}/${mockParams.ProjectId}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call addConcreteRequestAttachment with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ConcreteRequestId: 123,
        ParentCompanyId: 456,
        ProjectId: 789
      };
      const mockPayload = new FormData();
      const mockResponse = { success: true };

      apiServiceMock.requestImage.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.addConcreteRequestAttachment(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.requestImage).toHaveBeenCalledWith(
        `concrete_request_attachment/add_concrete_request_attachment/${mockParams.ConcreteRequestId}/${mockParams.ParentCompanyId}/${mockParams.ProjectId}`,
        mockPayload
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call removeInspectionAttachement with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        id: 123,
        ParentCompanyId: 456
      };
      const mockResponse = { success: true };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.removeInspectionAttachement(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `attachement/remove_inspection_attachement/${mockParams.id}/${mockParams.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call removeCraneRequestAttachement with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        id: 123,
        ParentCompanyId: 456,
        ProjectId: 789
      };
      const mockResponse = { success: true };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.removeCraneRequestAttachement(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `crane_request_attachment/remove_crane_request_attachement/${mockParams.id}/${mockParams.ParentCompanyId}/${mockParams.ProjectId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call removeConcreteRequestAttachment with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        id: 123,
        ParentCompanyId: 456,
        ProjectId: 789
      };
      const mockResponse = { success: true };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.removeConcreteRequestAttachment(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `concrete_request_attachment/remove_concrete_request_attachment/${mockParams.id}/${mockParams.ParentCompanyId}/${mockParams.ProjectId}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call getInspectionAttachement with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        InspectionRequestId: 123,
        ParentCompanyId: 456
      };
      const mockResponse = { success: true, data: [] };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getInspectionAttachement(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith(
        `attachement/get_inspection_attachement/${mockParams.InspectionRequestId}/${mockParams.ParentCompanyId}`
      );
      expect(result).toEqual(mockResponse);
    });
  });
});
