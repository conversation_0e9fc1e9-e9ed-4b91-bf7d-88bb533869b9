import { Component, TemplateRef } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { ProjectService } from '../../services/profile/project.service';
import { AuthService } from '../../services/auth/auth.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { MixpanelService } from '../../services/mixpanel.service';

@Component({
  selector: 'app-waste-log',
  templateUrl: './waste-log.component.html',
  })
export class WasteLogComponent {
  public currentPageNo = 1;

  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public ProjectId: any;

  public equipmentlogList: any = [];

  public memberList: any = [];

  public submitted = false;

  public formSubmitted = false;

  public formEditSubmitted = false;

  public editSubmitted = false;

  public editIndex: any;

  public currentDeleteId: any;

  public deleteSubmitted = false;

  public loader = true;

  public pageSize = 25;

  public pageNo = 1;

  public totalCount = 0;

  public wasteLogDetailsForm: UntypedFormGroup;

  public equipmentEditForm: UntypedFormGroup;

  public selectAll = false;

  public deleteIndex: any = [];

  public remove = false;

  public lastId: any = 0;

  public filterCount = 0;

  public equipmentList = [];

  public wasteTypeList = ['Textile Waste'];

  public companiesList = [];

  public filterForm: UntypedFormGroup;

  public search = '';

  public equipmentTypeList: any = [];

  public haulVehicleList = ['Medium Duty Truck', 'Heavy Duty Truck' , 'Light Duty Truck' , 'Passenger Car'];

  public modalLoader = false;

  public ParentCompanyId: any;

  public sort = 'DESC';

  public sortColumn = 'id';

  public presetEquipmentTypeList = [];

  public showSearchbar = false;

  public authUser: any = {};

  public getMappedRequestLoader: boolean = false;

  public mappedRequestList: any[];

  public craneEquipmentDropdownList: any;

  public nonCraneEquipmentDropdownList: any;

  public deactivateEquipmentLoader: boolean;

  public equipmentData: any;

  public constructor(
    private readonly modalService: BsModalService,
    public projectService: ProjectService,
    private readonly titleService: Title,
    private readonly mixpanelService: MixpanelService,
    private readonly deliveryService: DeliveryService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly authService: AuthService,
    private readonly toastr: ToastrService,
  ) {
    this.titleService.setTitle('Follo - Equipment');
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
      }
    });
    this.projectService.projectId.subscribe((res): void => {
      this.ProjectId = res;
      if (res !== undefined && res !== null && res !== '') {
        this.loader = true;
        this.getEquipments();
      }
    });
    this.projectService.ParentCompanyId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ParentCompanyId = res;
        this.getEquipments();
      }
    });
    this.equipmentForm();
    this.editEquipmentForm();
    this.filterDetailsForm();
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'clear':
          this.clear();
          break;
        case 'delete':
          this.openDeleteModal(data, item);
          break;
        case 'filter':
          this.openFilterModal(data);
          break;
        default:
          break;
      }
    }
  }

  public sortByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getEquipments();
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.pageNo = 1;
    this.getEquipments();
  }

  public getSearchEquipment(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.pageNo = 1;
    this.search = data;
    this.getEquipments();
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.search = '';
    this.pageNo = 1;
    this.filterDetailsForm();
    this.getEquipments();
    this.modalRef.hide();
    this.wasteLogDetailsForm.reset();
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.wasteLogDetailsForm.reset();
      this.submitted = false;
      this.formSubmitted = false;
      this.editSubmitted = false;
      this.formEditSubmitted = false;
      this.modalRef.hide();
    }
  }

  public close(template: TemplateRef<any>, form: string): void {
    if (form === 'deactivateEquipment') {
      this.modalRef.hide();
      this.getEquipments();
    }
    if (form === 'newequipment') {
      if (this.wasteLogDetailsForm.dirty) {
        this.closeModalPopup(template);
      } else {
        this.resetForm('yes');
      }
    } else if (this.equipmentEditForm.dirty) {
      this.closeModalPopup(template);
    } else {
      this.resetForm('yes');
    }
  }

  public closeModalPopup(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public getEquipments(): void {
    this.loader = true;
    if (this.ProjectId && this.ParentCompanyId) {
      const param = {
        ProjectId: this.ProjectId,
        pageSize: this.pageSize,
        pageNo: this.pageNo,
        ParentCompanyId: this.ParentCompanyId,
      };
      let payload: any = {};
      if (this.filterForm !== undefined) {
        payload = {
          idFilter: +this.filterForm.value.idFilter,
          nameFilter: this.filterForm.value.nameFilter,
          companyNameFilter: this.filterForm.value.companyNameFilter,
          typeFilter: this.filterForm.value.typeFilter,
          memberFilter: +this.filterForm.value.memberFilter,
          search: this.search,
        };
      }
      payload.search = this.search;
      payload.ParentCompanyId = this.ParentCompanyId;
      payload.sort = this.sort;
      payload.sortByField = this.sortColumn;
      payload.isFilter = false;
      payload.showActivatedAlone = false;
      payload.projectId = this.ProjectId;
      this.projectService.listWasteLog(param, payload).subscribe((response: any): void => {
        if (response) {
          const responseData = response.data;
          this.loader = false;
          this.equipmentlogList = responseData.rows;
          this.totalCount = responseData.count;
        }
      });
    }
  }

  public switchEquipment(data, equipmentId): void {
    const equipmentObject = data;
    if (+data.equipmentDetails[0].Equipment.id !== +equipmentId) {
      equipmentObject.changedEquipmentId = +equipmentId;
    } else {
      equipmentObject.changedEquipmentId = null;
    }
    return equipmentObject;
  }

  public onStatusChange(event: Event) {
    const isChecked = (event.target as HTMLInputElement).checked;
    console.log('Switch status:', isChecked);  // Use this to debug or perform any action
  }


  public deactiveEquipment(): void {
    this.deactivateEquipmentLoader = true;
    let filterChangedEquipmentData = [];
    filterChangedEquipmentData = this.mappedRequestList.filter(
      (obj): any => obj.changedEquipmentId && obj.changedEquipmentId !== null,
    );
    if (
      filterChangedEquipmentData
      && filterChangedEquipmentData.length > 0
      && this.mappedRequestList.length === filterChangedEquipmentData.length
    ) {
      const payload = {
        id: this.equipmentData.id,
        equipmentSwitchedRequests: filterChangedEquipmentData,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.projectService.deactiveEquipment(payload).subscribe({
        next: (response): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.getEquipments();
            filterChangedEquipmentData = [];
            this.modalRef.hide();
            this.equipmentData = '';
            this.deactivateEquipmentLoader = false;
          }
        },
        error: (deactivateGateErr): void => {
          if (deactivateGateErr.message?.statusCode === 400) {
            this.showError(deactivateGateErr);
          } else if (!deactivateGateErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deactivateGateErr.message, 'OOPS!');
          }
        },
      });
    } else if (this.mappedRequestList.length === 0) {
      const payload = {
        id: this.equipmentData.id,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.projectService.deactiveEquipment(payload).subscribe({
        next: (response): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.getEquipments();
            this.modalRef.hide();
            this.equipmentData = '';
            this.deactivateEquipmentLoader = false;
          }
        },
        error: (deactivateGateErr): void => {
          if (deactivateGateErr.message?.statusCode === 400) {
            this.showError(deactivateGateErr);
          } else if (!deactivateGateErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deactivateGateErr.message, 'OOPS!');
          }
        },
      });
    } else {
      this.deactivateEquipmentLoader = false;
    }
  }

  public changePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.getEquipments();
  }

  public changePageNo(pageNo: number): void {
    this.currentPageNo = pageNo;
    this.getEquipments();
  }

  public equipmentForm(): void {
    this.wasteLogDetailsForm = this.formBuilder.group({
      hauler: ['', Validators.compose([Validators.required])],
      wasteType: ['', Validators.compose([Validators.required])],
      quantity: ['', Validators.compose([Validators.required])],
      landfillDestination: ['', Validators.compose([Validators.required])],
      haulVehicle: ['', Validators.compose([Validators.required])],
      wasteTickets: [''],
      projectId: [''],
    });
  }




  public openDeactivateModal(template, data, event): void {
    this.equipmentData = data;
    if (!event) {
      this.modalRef = this.modalService.show(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-md new-gate-popup custom-modal deactivate-modal',
      });
      this.mappedRequestList = [];
      this.getMappedRequestLoader = true;
      const payload = {
        ProjectId: this.ProjectId,
        id: data.equipmentAutoId,
        equipmentName: data.equipmentName,
      };
      this.projectService.getEquipmentMappedRequests(payload).subscribe((response: any): void => {
        if (response) {
          this.mappedRequestList = response.data.mappedRequest;
          this.craneEquipmentDropdownList = response.data.equipments.craneEquipments;
          this.nonCraneEquipmentDropdownList = response.data.equipments.nonCraneEquipments;
          this.getMappedRequestLoader = false;
        }
      });
    }
    if (event) {
      this.activateEquipment(data);
    }
  }

  public activateEquipment(data): void {
    const payload = {
      id: data.id,
      equipmentName: data.equipmentName.trim(),
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      isActive: true,
    };
    this.projectService.editEquipment(payload).subscribe({
      next: (response): void => {
        if (response) {
          this.toastr.success(response.message, 'Success');
          this.getEquipments();
        }
      },
      error: (activateEquipmentErr): void => {
        if (activateEquipmentErr.message?.statusCode === 400) {
          this.showError(activateEquipmentErr);
        } else if (!activateEquipmentErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(activateEquipmentErr.message, 'OOPS!');
        }
      },
    });
  }

  public editEquipmentForm(): void {
    this.equipmentEditForm = this.formBuilder.group({
      equipmentName: ['', Validators.compose([Validators.required])],
      EquipmentType: ['', Validators.compose([Validators.required])],
      id: [''],
      equipmentAutoId: [''],
      controlledBy: ['', Validators.compose([Validators.required])],
    });
  }

  public checkStringEmptyValues(formValue: { EquipmentName: string }): boolean {
    return false;
  }

  public onSubmit(): void {
    this.submitted = true;
    this.formSubmitted = true;
    if (this.wasteLogDetailsForm.invalid) {
      this.formSubmitted = false;
      return;
    }
    const formValue = this.wasteLogDetailsForm.value;
    if (!this.checkStringEmptyValues(formValue)) {
      const payload = {
        hauler: formValue.hauler,
        wasteType: formValue.wasteType,
        quantity: formValue.quantity,
        landfillDestination: formValue.landfillDestination,
        haulVehicle: formValue.haulVehicle,
        wasteTickets: formValue.wasteTickets,
        projectId: this.ProjectId
      };
      this.projectService.addWasteLog(payload).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Added Equipment');
            this.wasteLogDetailsForm.reset();
            this.submitted = false;
            this.formSubmitted = false;
            this.resetForm('yes');
            this.getEquipments();
            this.modalRef.hide();
          }
        },
        error: (addEquipmentError): void => {
          this.submitted = false;
          this.formSubmitted = false;
          if (addEquipmentError.message?.statusCode === 400) {
            this.showError(addEquipmentError);
            this.formSubmitted = false;
          } else if (!addEquipmentError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(addEquipmentError.message, 'OOPS!');
          }
        },
      });
    } else {
      this.toastr.error('Please Enter valid Equipment Name/Type.', 'OOPS!');
      this.submitted = false;
      this.formSubmitted = false;
    }
  }

  public onEditSubmit(): void {
    this.editSubmitted = true;
    this.formEditSubmitted = true;
    if (this.equipmentEditForm.invalid) {
      this.formEditSubmitted = false;
      return;
    }
    const formValue = this.equipmentEditForm.value;
    if (!this.checkStringEmptyValues(formValue)) {
      const payload = {
        equipmentName: formValue.equipmentName.trim(),
        PresetEquipmentTypeId: +formValue.EquipmentType,
        controlledBy: formValue.controlledBy,
        id: formValue.id,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.projectService.editEquipment(payload).subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Edited  Equipment');
            this.editSubmitted = false;
            this.formEditSubmitted = false;
            this.equipmentEditForm.reset();
            this.getEquipments();
            this.modalRef.hide();
          }
        },
        error: (editEquipmentError): void => {
          this.submitted = false;
          this.formEditSubmitted = false;
          if (editEquipmentError.message?.statusCode === 400) {
            this.showError(editEquipmentError);
            this.formEditSubmitted = false;
          } else if (!editEquipmentError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(editEquipmentError.message, 'OOPS!');
          }
        },
      });
    } else {
      this.toastr.error('Please Enter valid Equipment Name/Type.', 'OOPS!');
      this.submitted = false;
      this.formEditSubmitted = false;
    }
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.editSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-delivery-popup custom-modal',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public resetAndClose(): void {
    this.wasteLogDetailsForm.reset();
    this.submitted = false;
    this.formSubmitted = false;
    this.editSubmitted = false;
    this.formEditSubmitted = false;
    this.modalRef.hide();
  }

  public openDeleteModal(index: number, template: TemplateRef<any>): void {
    if (index !== -1) {
      this.deleteIndex[0] = this.equipmentlogList[index].id;
      this.currentDeleteId = index;
      this.remove = false;
    } else if (index === -1) {
      this.remove = true;
    }
    this.openModal(template);
  }

  public checkSelectedRow(): boolean {
    if (this.selectAll) {
      return false;
    }
    const index = this.equipmentlogList.findIndex(
      (item: { isChecked: boolean }): boolean => item.isChecked === true,
    );
    if (index !== -1) {
      return false;
    }
    return true;
  }

  public setSelectedItem(index: string | number): void {
    this.equipmentlogList[index].isChecked = !this.equipmentlogList[index].isChecked;
  }

  public openFilterModal(template: TemplateRef<any>): void {
    this.modalRef = this.modalService.show(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-sm filter-popup custom-modal',
    });
  }

  public deleteEquipment(): void {
    this.deleteSubmitted = true;
    this.projectService
      .deleteEquipment({
        id: this.deleteIndex,
        ProjectId: this.ProjectId,
        isSelectAll: this.selectAll,
        ParentCompanyId: this.ParentCompanyId,
      })
      .subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Deleted  Equipment');
            this.getEquipments();
            this.deleteSubmitted = false;
            this.modalRef.hide();
          }
        },
        error: (deleteEquipmentError): void => {
          this.deleteSubmitted = false;
          this.getEquipments();
          if (deleteEquipmentError.message?.statusCode === 400) {
            this.showError(deleteEquipmentError);
          } else if (!deleteEquipmentError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deleteEquipmentError.message, 'OOPS!');
          }
        },
      });
  }

  public selectAllEquipmentsData(): void {
    this.selectAll = !this.selectAll;
    if (this.selectAll) {
      this.equipmentlogList.map((obj: any, index: string | number): void => {
        this.equipmentlogList[index].isChecked = true;
        return null;
      });
    } else {
      this.equipmentlogList.map((obj: any, index: string | number): void => {
        this.equipmentlogList[index].isChecked = false;
        return null;
      });
    }
  }

  public alphaNumericForEquipments(event: { which: any; keyCode: any }): boolean {
    const key = event.which ? event.which : event.keyCode;
    const condition1 = key >= 65 && key <= 90;
    const condition2 = key >= 97 && key <= 128;
    const condition3 = key === 8 || key === 32;
    if (condition3) {
      return true;
    }
    if (key > 31 && (key < 48 || key > 57) && !condition1 && !condition2) {
      return false;
    }
    return true;
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('idFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('nameFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('companyNameFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('typeFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('memberFilter').value !== '') {
      this.filterCount += 1;
    }
    this.pageNo = 1;
    this.getEquipments();
    this.modalRef.hide();
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      idFilter: [''],
      nameFilter: [''],
      companyNameFilter: [''],
      typeFilter: [''],
      memberFilter: [''],
    });
  }

  public removeItem(): void {
    this.deleteSubmitted = true;
    if (this.selectAll) {
      this.deleteEquipment();
    } else {
      this.equipmentlogList.forEach((element: { isChecked: any; id: any }): void => {
        if (element.isChecked) {
          this.deleteIndex.push(element.id);
        }
      });
      this.deleteEquipment();
    }
  }

  public openEditModal(index: string | number, template: TemplateRef<any>): void {
    this.editIndex = index;
    this.equipmentEditForm.get('id').setValue(this.equipmentlogList[index].id);
    this.equipmentEditForm
      .get('equipmentAutoId')
      .setValue(this.equipmentlogList[index].equipmentAutoId);
    this.equipmentEditForm.get('equipmentName').setValue(this.equipmentlogList[index].equipmentName);
    this.equipmentEditForm
      .get('EquipmentType')
      .setValue(this.equipmentlogList[index].PresetEquipmentType?.id);
    this.equipmentEditForm.get('controlledBy').setValue(this.equipmentlogList[index].controlledBy);
    this.openModal(template);
  }
}
function elseif(arg0: boolean) {
  throw new Error('Function not implemented.');
}
