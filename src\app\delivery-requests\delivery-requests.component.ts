/* eslint-disable max-lines-per-function */
import { Component, TemplateRef, OnInit } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import moment from 'moment';
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { Observable } from 'rxjs';
import { Title } from '@angular/platform-browser';
import { ProjectService } from '../services/profile/project.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { NewDeliveryFormComponent } from './delivery-details/new-delivery-form/new-delivery-form.component';
import { EditDeliveryFormComponent } from './delivery-details/edit-delivery-form/edit-delivery-form.component';
import { EditCraneRequestComponent } from '../crane-requests/edit-crane-request/edit-crane-request.component';
import { DeliveryDetailsNewComponent } from './delivery-details/delivery-details-new/delivery-details-new.component';

type DateInput = string | number | Date;

@Component({
  selector: 'app-delivery-requests',
  templateUrl: './delivery-requests.component.html',
})
export class DeliveryRequestsComponent implements OnInit {
  public currentPageNo = 1;

  public userData = [];

  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public ProjectId: string | number;

  public loader = true;

  public pageSize = 25;

  public pageNo = 1;

  public totalCount = 0;

  public lastId: any = 0;

  public deliveryList: any = [];

  public companyList: any = [];

  public defineList: any = [];

  public defineListData: any = [];

  public gateList: any = [];

  public equipmentList: any = [];

  public currentTemplate: TemplateRef<any>;

  public autocompleteItemsAsObjects: any = [];

  public submitted = false;

  public escort = false;

  public formSubmitted = false;

  public editSubmitted = false;

  public editMultipleSubmitted = false;

  public modalLoader = false;

  public filterCount = 0;

  public formEditSubmitted = false;

  public authUser: any = {};

  public statusValue: any = [];

  public currentDeliveryIndex: any = {};

  public currentEditItem: any = {};

  public currentStatus = '';

  public statusSubmitted = false;

  public showStatus = false;

  public search = '';

  public filterForm: UntypedFormGroup;

  public memberList: any = [];

  public wholeStatus = ['Approved', 'Declined', 'Delivered', 'Pending'];

  public voidSubmitted = false;

  public currentDeliverySaveItem: any = {};

  public deliveryId: number;

  public ParentCompanyId: any;

  public importSubmitted = false;

  public files: NgxFileDropEntry[] = [];

  public formData: any = [];

  public todayDate = new Date();

  public data1: { name: any; values: any }[];

  public workbookData: any;

  public deleteDeliveryRequestSubmitted = false;

  public bulkNdrUploadInProgress = false;

  public currentlySelectedTab = 'tab0';

  public currentDeliveryRequestSelectAll = false;

  public editModalLoader = false;

  public deliverEditMultipleForm: UntypedFormGroup;

  public newNdrDefinableDropdownSettings: IDropdownSettings;

  public newNdrCompanyDropdownSettings: IDropdownSettings;

  public selectedDeliveryRequestId = '';

  public selectedDeliveryRequestIdForMultipleEdit = [];

  public NDRTimingChanged = false;

  public formEdited = false;

  public deliveryStart: string | Date;

  public deliveryEnd: string | Date;

  public modalRef2: BsModalRef;

  public companyEdited = false;

  public dfowEdited = false;

  public escortEdited = false;

  public responsiblePersonEdited = false;

  public gateEdited = false;

  public equipmentEdited = false;

  public voidEdited = false;

  public statusEdited = false;

  public deliveryDateEdited = false;

  public editedFields = '';

  public showSearchbar = false;

  public param1: any;

  public param2: any;

  public url: any;

  public sortColumn = 'id';

  public sort = 'DESC';

  public selectedDeliveryListStatus = '';

  public selectedstatuslength: number;

  public statustype: any;

  public voidvalue = false;

  public approvedBackgroundColor: string;

  public approvedFontColor: string;

  public rejectedBackgroundColor: string;

  public rejectedFontColor: string;

  public deliveredBackgroundColor: string;

  public deliveredFontColor: string;

  public pendingBackgroundColor: string;

  public pendingFontColor: string;

  public expiredBackgroundColor: string;

  public expiredFontColor: string;

  public locationList: any = [];

  public equipmentDropdownSettings: IDropdownSettings;

  public craneEquipmentTypeChosen = false;

  public isAssociatedWithCraneRequest = false;

  public noEquipmentOption = { id: 0, equipmentName: 'No Equipment Needed' };

  public constructor(
    private readonly modalService: BsModalService,
    public projectService: ProjectService,
    private readonly formBuilder: UntypedFormBuilder,
    public router: Router,
    public socket: Socket,
    private readonly toastr: ToastrService,
    private readonly deliveryService: DeliveryService,
    private readonly titleService: Title,
    private readonly route: ActivatedRoute,
  ) {
    this.titleService.setTitle('Follo - Delivery Bookings');
    /**/
    this.projectService.projectParent.subscribe((response19): void => {
      if (response19 !== undefined && response19 !== null && response19 !== '') {
        this.loader = true;
        this.deliveryList = [];
        this.ProjectId = response19.ProjectId;
        this.ParentCompanyId = response19.ParentCompanyId;
        this.getDeliveryRequest();
        this.getOverAllGateInNewDelivery();
        this.getMembers();
      }
    });
    this.projectService.ParentCompanyId.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.ParentCompanyId = res;
      }
    });
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
        if (this.authUser.RoleId === 2 || this.authUser.RoleId === 1) {
          this.statusValue = ['Approved', 'Declined'];
        } else if (this.authUser.RoleId === 3) {
          this.statusValue = ['Delivered', 'Approved'];
        }
      }
    });
    this.deliveryService.getCurrentStatus.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.deliveryId = res;
        this.getDeliveryRequest();
      }
    });
    this.projectService.fileUpload.subscribe((res): void => {
      if (res && res.status === 'uploading') {
        this.bulkNdrUploadInProgress = true;
      }
      if (res && res.status === 'uploadDone') {
        this.projectService.uploadBulkNdrFile({ status: '' });
        this.bulkNdrUploadInProgress = false;
        this.getDeliveryRequest();
      }
    });
    this.filterDetailsForm();
  }

  public ngOnInit(): void {
    this.router.events.subscribe((e): void => {
      if (this.modalRef) {
        this.modalRef.hide();
      }
    });
    this.deliveryService.refresh.subscribe((getNdrResponse): void => {
      if (getNdrResponse !== undefined && getNdrResponse !== null && getNdrResponse !== '') {
        this.getDeliveryRequest();
      }
    });
  }

  public handleInputChange(event): void {
    this.voidvalue = false;
    const checkbox = document.getElementById('subscribe') as HTMLInputElement | null;
    if (checkbox.checked === true) {
      this.voidvalue = true;
    } else {
      this.voidvalue = false;
    }
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'clear':
          this.clear();
          break;
        case 'filter':
          this.openModal1(data);
          break;
        case 'open':
          this.openIdModal(data, item);
          break;
        case 'edit':
          this.openEditModal(data, item);
          break;
        case 'file':
          this.removeFile(data);
          break;
        case 'download':
          this.download();
          break;
        default:
          break;
      }
    }
  }

  public sortByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getDeliveryRequest();
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.search = '';
    this.pageNo = 1;
    this.filterDetailsForm();
    this.getDeliveryRequest();
    this.modalRef.hide();
  }

  public selectStatus(status: string): void {
    this.currentStatus = status;
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('descriptionFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('dateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('companyFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('memberFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('gateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('equipmentFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('locationFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('statusFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('pickFrom').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('pickTo').value !== '') {
      this.filterCount += 1;
    }
    this.pageNo = 1;
    this.getDeliveryRequest();
    this.modalRef.hide();
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.pageNo = 1;
    this.getDeliveryRequest();
  }

  public getSearchNDR(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.pageNo = 1;
    this.search = data;
    this.getDeliveryRequest();
  }

  public redirect(path: any): void {
    this.router.navigate([`/${path}`]);
  }

  public getOverAllGateForNdrGrid(): void {
    this.modalLoader = true;
    const getOverAllGateForNdrGridParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .gateList(getOverAllGateForNdrGridParams, { isFilter: true, showActivatedAlone: true })
      .subscribe((getOverAllGateForNdrGridResponse): void => {
        this.gateList = getOverAllGateForNdrGridResponse.data;
        this.getOverAllEquipmentForNdrGrid();
      });
  }

  public getOverAllEquipmentForNdrGrid(): void {
    const getOverAllEquipmentForNdrGridParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listEquipment(getOverAllEquipmentForNdrGridParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((getOverAllEquipmentForNdrGridResponse): void => {
        this.equipmentList = [this.noEquipmentOption, ...getOverAllEquipmentForNdrGridResponse.data];
        this.getCompaniesForNdrGrid();
      });
  }

  public getDefinableForNdrGrid(): void {
    const getDefinableForNdrGridParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getDefinableWork(getDefinableForNdrGridParams)
      .subscribe((getDefinableForNdrGridResponse: any): void => {
        if (getDefinableForNdrGridResponse) {
          const { data } = getDefinableForNdrGridResponse;
          this.defineList = data;
          this.getLocations();
        }
      });
  }

  public getLocations(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getLocations(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.locationList = data;
        this.closePopupContentModal();
      }
    });
  }

  public close(): void {
    this.submitted = false;
    this.formSubmitted = false;
    this.deleteDeliveryRequestSubmitted = false;
    this.editSubmitted = false;
    this.formEditSubmitted = false;
    this.modalRef.hide();
  }

  public closeEditMultiplePopup(): void {
    this.formEditSubmitted = false;
    this.modalRef.hide();
    this.editModalLoader = false;
    this.selectedDeliveryRequestId = '';
    this.selectedDeliveryListStatus = '';
    this.deliveryList.map((obj: any, index: string | number): void => {
      this.deliveryList[index].isChecked = false;
      return null;
    });
    this.currentDeliveryRequestSelectAll = false;
    this.selectedDeliveryRequestIdForMultipleEdit = [];
    this.editMultipleSubmitted = false;
    this.companyEdited = false;
    this.dfowEdited = false;
    this.escortEdited = false;
    this.responsiblePersonEdited = false;
    this.gateEdited = false;
    this.equipmentEdited = false;
    this.voidEdited = false;
    this.statusEdited = false;
    this.deliveryDateEdited = false;
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public getCompaniesForNdrGrid(): void {
    const getCompaniesForNdrGridParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getCompanies(getCompaniesForNdrGridParams)
      .subscribe((getCompaniesForNdrGridResponse: any): void => {
        if (getCompaniesForNdrGridResponse) {
          this.companyList = getCompaniesForNdrGridResponse.data;
          this.getDefinableForNdrGrid();
        }
      });
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      companyFilter: [''],
      descriptionFilter: [''],
      statusFilter: [''],
      dateFilter: [''],
      memberFilter: [''],
      gateFilter: [''],
      equipmentFilter: [''],
      locationFilter: [''],
      pickFrom: [''],
      pickTo: [''],
    });
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }

  public getDeliveryRequest(): void {
    this.loader = true;
    this.deliveryList = [];
    const getDeliveryRequestParam = {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.pageNo,
      void: 0,
    };
    let getDeliveryRequestPayload: any = {};
    if (this.filterForm !== undefined) {
      const getDeliveryRequestDateInFilterForm = this.filterForm.value.dateFilter
        ? moment(this.filterForm.value.dateFilter).format('YYYY-MM-DD')
        : this.filterForm.value.dateFilter;
      getDeliveryRequestPayload = {
        companyFilter: +this.filterForm.value.companyFilter,
        descriptionFilter: this.filterForm.value.descriptionFilter,
        dateFilter: getDeliveryRequestDateInFilterForm,
        statusFilter: this.filterForm.value.statusFilter,
        memberFilter: +this.filterForm.value.memberFilter,
        gateFilter: +this.filterForm.value.gateFilter,
       equipmentFilter: this.filterForm.value.equipmentFilter === null || this.filterForm.value.equipmentFilter === '' ? null : +this.filterForm.value.equipmentFilter,
        locationFilter: this.filterForm.value.locationFilter,
        pickFrom: this.filterForm.value.pickFrom,
        pickTo: this.filterForm.value.pickTo,
        search: this.search,
      };
    }
    getDeliveryRequestPayload.search = this.search;
    getDeliveryRequestPayload.sort = this.sort;
    getDeliveryRequestPayload.sortByField = this.sortColumn;
    getDeliveryRequestPayload.ParentCompanyId = this.ParentCompanyId;
    getDeliveryRequestPayload.queuedNdr = false;
    this.deliveryService
      .listNDR(getDeliveryRequestParam, getDeliveryRequestPayload)
      .subscribe((response: any): void => {
        if (response) {
          const responseData = response.data;
          this.lastId = response.lastId;
          const statusCode = JSON.parse(response?.statusData.statusColorCode);

          const approved = statusCode.find((item) => item.status === 'approved');
          const pending = statusCode.find((item) => item.status === 'pending');
          const delivered = statusCode.find((item) => item.status === 'delivered');
          const rejected = statusCode.find((item) => item.status === 'rejected');
          const expired = statusCode.find((item) => item.status === 'expired');

          this.approvedBackgroundColor = approved.backgroundColor;
          this.approvedFontColor = approved.fontColor;
          this.rejectedBackgroundColor = rejected.backgroundColor;
          this.rejectedFontColor = rejected.fontColor;
          this.expiredBackgroundColor = expired.backgroundColor;
          this.expiredFontColor = expired.fontColor;
          this.deliveredBackgroundColor = delivered.backgroundColor;
          this.deliveredFontColor = delivered.fontColor;
          this.pendingBackgroundColor = pending.backgroundColor;
          this.pendingFontColor = pending.fontColor;


          this.loader = false;
          this.deliveryList = responseData.rows;
          this.deliveryList.map(
            (item: { memberDetails: any }, indexValue: string | number): void => {
              if (this.authUser.RoleId === 4 || this.authUser.RoleId === 3) {
                const responsibleMemberArray = item.memberDetails;
                const index = responsibleMemberArray.findIndex(
                  (i: { Member: { id: any } }): boolean => i.Member.id === this.authUser.id,
                );
                if (index !== -1) {
                  this.deliveryList[indexValue].isAllowedToEdit = true;
                } else {
                  this.deliveryList[indexValue].isAllowedToEdit = false;
                }
              } else {
                this.deliveryList[indexValue].isAllowedToEdit = true;
              }
              return null;
            },
          );
          this.totalCount = responseData.count;
          const isTokenExists = localStorage.getItem('token');
          if (isTokenExists) {
            this.route.queryParams.subscribe((params): any => {
              this.param1 = params.requestId;
              this.param2 = params.memberId;
              this.url = this.router.url.split('?');
              this.router.navigateByUrl(this.url[0]);
              if (this.param1 && this.param2) {
                const object = {
                  encryptedRequestId: this.param1,
                  encryptedMemberId: this.param2,
                  ProjectId: +localStorage.getItem('ProjectId'),
                  ParentCompanyId: +localStorage.getItem('currentCompanyId'),
                };
                this.deliveryService.decryption(object).subscribe((res): void => {
                  if (res?.data) {
                    this.param1 = '';
                    this.param2 = '';
                    this.url = '';
                    const requestId = res.data.decryptedRequestId;
                    this.deliveryService.updateStateOfNDR('current');
                    const newPayload = {
                      id: +requestId,
                      ProjectId: +localStorage.getItem('ProjectId'),
                      ParentCompanyId: +localStorage.getItem('currentCompanyId'),
                    };
                    if (newPayload.ProjectId > 0 && newPayload.ParentCompanyId > 0) {
                      const initialState = {
                        data: newPayload,
                        title: 'Modal with component',
                      };
                      this.modalRef = this.modalService.show(DeliveryDetailsNewComponent, {
                        backdrop: 'static',
                        keyboard: false,
                        class: 'modal-lg new-delivery-popup custom-modal',
                        initialState,
                      });
                      this.modalRef.content.closeBtnName = 'Close';
                    }
                  }
                });
              }
            });
          } else {
            this.router.navigate(['/login']);
          }
        }
      });
  }

  public selectAllCurrentDeliveryRequestForEdit(): void {
    this.selectedDeliveryRequestId = '';
    this.selectedDeliveryListStatus = '';
    this.selectedDeliveryRequestIdForMultipleEdit = [];
    this.currentDeliveryRequestSelectAll = !this.currentDeliveryRequestSelectAll;
    if (this.currentDeliveryRequestSelectAll) {
      this.deliveryList.map((obj: any, index: string | number): void => {
        if (obj.isAllowedToEdit) {
          this.deliveryList[index].isChecked = true;
          this.selectedDeliveryRequestId += `${this.deliveryList[index].DeliveryId},`;
          this.selectedDeliveryListStatus += `${this.deliveryList[index].status},`;
          this.selectedDeliveryRequestIdForMultipleEdit.push(this.deliveryList[index].id);
        } else {
          this.deliveryList[index].isChecked = false;
        }
        return null;
      });
      this.selectedDeliveryRequestId = this.selectedDeliveryRequestId.replace(/,\s*$/, '');
      const output = this.selectedDeliveryListStatus.split(',');
      const arr = output.filter((item) => item);
      const unique = arr.filter((item, i, ar) => ar.indexOf(item) === i);
      this.statustype = unique;
      this.selectedstatuslength = unique.length;
    } else {
      this.deliveryList.map((obj: any, index: string | number): void => {
        this.deliveryList[index].isChecked = false;
        return null;
      });
    }
  }

  public checkIfCurrentDeliveryRequestRowSelected(): boolean {
    if (this.currentDeliveryRequestSelectAll) {
      return false;
    }
    const indexFind = this.deliveryList.findIndex(
      (item: { isChecked: boolean }): boolean => item.isChecked === true,
    );

    if (indexFind !== -1) {
      return false;
    }
    return true;
  }

  public setSelectedCurrentDeliveryRequestItem(index: string | number): void {
    this.deliveryList[index].isChecked = !this.deliveryList[index].isChecked;
    if (this.deliveryList[index].isChecked) {
      this.selectedDeliveryRequestId += `${this.deliveryList[index].DeliveryId},`;
      this.selectedDeliveryRequestIdForMultipleEdit.push(this.deliveryList[index].id);
      this.selectedDeliveryListStatus += `${this.deliveryList[index].status},`;
      const output = this.selectedDeliveryListStatus.split(',');
      const arr = output.filter((item) => item);
      const unique = arr.filter((item, i, ar) => ar.indexOf(item) === i);
      this.statustype = unique;
      this.selectedstatuslength = unique.length;
    } else {
      const selectedIds = this.selectedDeliveryRequestId.replace(
        `${this.deliveryList[index].DeliveryId},`,
        '',
      );
      this.selectedDeliveryRequestId = '';
      this.selectedDeliveryRequestId = selectedIds;
      const ifDeliveryIdExists = this.selectedDeliveryRequestIdForMultipleEdit.indexOf(
        this.deliveryList[index].id,
      );
      if (ifDeliveryIdExists > -1) {
        this.selectedDeliveryRequestIdForMultipleEdit.splice(ifDeliveryIdExists, 1);
      }
    }
  }

  public setGateEquipment(): void {
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(NewDeliveryFormComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
    });
    this.modalRef.content.lastId = this.lastId;
    this.modalRef.content.closeBtnName = 'Close';
  }

  public openModal(template: TemplateRef<any>): void {
    this.currentTemplate = template;
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-delivery-popup custom-modal',
    };
    this.modalRef = this.modalService.show(this.currentTemplate, data);
  }

  public closePopupContentModal(): void {
    this.modalLoader = false;
  }

  public setStatus(item: { id: any; status: string }): void {
    this.deliveryId = -1;
    this.deliveryService.updatedDeliveryId(item.id);
    this.currentDeliveryIndex = this.deliveryList.findIndex(
      (i: { id: any }): boolean => i.id === item.id,
    );
    this.currentDeliverySaveItem = this.deliveryList[this.currentDeliveryIndex];
    const condition = item.status !== 'Expired' && item.status !== 'Delivered';
    if (
      (this.authUser.RoleId === 2 && condition)
      || (this.authUser.RoleId === 3 && item.status === 'Approved')
    ) {
      this.showStatus = true;
    } else {
      this.showStatus = false;
    }
  }

  public openIdModal(item: { id: any; ProjectId: any }, ndrStatus: any): void {
    this.deliveryService.updateStateOfNDR(ndrStatus);
    const newPayload = {
      id: item.id,
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    const initialState = {
      data: newPayload,
      title: 'Modal with component',
    };
    this.modalRef = this.modalService.show(DeliveryDetailsNewComponent, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-delivery-popup custom-modal',
      initialState,
    });
    this.modalRef.content.closeBtnName = 'Close';
  }

  public openEditModal(item, ndrState: string): void {
    if (this.modalRef) {
      this.close();
    }
    this.deliveryService.updateStateOfNDR(ndrState);
    const className = 'modal-lg new-delivery-popup custom-modal';
    if (
      ndrState === 'crane'
      && !item.isAssociatedWithDeliveryRequest
      && !item.isAssociatedWithCraneRequest
    ) {
      this.deliveryService.updatedEditCraneRequestId(+item.CraneRequestId);
      this.modalRef = this.modalService.show(EditCraneRequestComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
      });
    } else {
      this.deliveryService.updatedDeliveryId(item.id);
      this.modalRef = this.modalService.show(EditDeliveryFormComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
      });
    }

    this.modalRef.content.closeBtnName = 'Close';
    this.modalRef.content.seriesOption = 1;
    this.modalRef.content.recurrenceId = item?.recurrence ? item?.recurrence?.id : null;
    this.modalRef.content.recurrenceEndDate = item?.recurrence
      ? item?.recurrence?.recurrenceEndDate
      : null;
  }

  public changePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.getDeliveryRequest();
  }

  public openModal1(template: TemplateRef<any>): void {
    this.getOverAllGateForNdrGrid();
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-sm filter-popup custom-modal',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public changePageNo(pageNo: number): void {
    this.pageNo = pageNo;
    this.getDeliveryRequest();
  }

  public dropped(files: NgxFileDropEntry[]): void {
    if (files.length === 1) {
      this.files = files;
      this.files.forEach((element, i): void => {
        const relativePath = element.relativePath.split('.');
        const extension = relativePath[relativePath.length - 1];
        if (extension === 'xlsx') {
          if (element.fileEntry.isFile) {
            const fileEntry = element.fileEntry as FileSystemFileEntry;
            fileEntry.file((_file: File): void => {
              this.formData = new FormData();
              this.formData.append('delivery_request', _file, element.relativePath);
            });
          }
        } else {
          this.files.splice(i);
          this.toastr.error('Please select a valid file. Supported file format (.xlsx)', 'OOPS!');
        }
      });
    } else {
      this.toastr.error('Please import single file', 'OOPS!');
    }
  }

  public importDeliveryRequest(): void {
    this.importSubmitted = true;
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.importBulkNDR(params, this.formData).subscribe({
      next: (res): void => {
        this.projectService.uploadBulkNdrFile({ status: 'uploading' });
        this.files = [];
        this.importSubmitted = false;
        this.modalRef.hide();
      },
      error: (importDeliveryRequestError): void => {
        this.importSubmitted = false;
        if (importDeliveryRequestError.message?.statusCode === 400) {
          this.showError(importDeliveryRequestError);
        } else if (!importDeliveryRequestError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(importDeliveryRequestError.message, 'OOPS!');
        }
      },
    });
  }

  public download(): any {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };

    this.deliveryService.importBulkNDRTemplate(params).subscribe((res: any): void => {
      this.projectService.getProject().subscribe({
        next: (response): any => {
          const project = response.data.filter(
            (data: { id: string | number }): {} => +data.id === +this.ProjectId,
          );
          const fileName = `${project[0].projectName}_${project[0].id}_${new Date().getTime()}`;
          const downloadURL = window.URL.createObjectURL(res);
          const link = document.createElement('a');
          link.href = downloadURL;
          link.download = `${fileName}.xlsx`;
          link.click();
        },
        error: (downloadDeliveryRequestError): void => {
          if (downloadDeliveryRequestError.message?.statusCode === 400) {
            this.showError(downloadDeliveryRequestError);
          } else if (!downloadDeliveryRequestError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(downloadDeliveryRequestError.message, 'OOPS!');
          }
        },
      });
    });
  }

  public removeFile(i: number): void {
    this.files.splice(i, 1);
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.modalRef.hide();
      this.files = [];
    }
  }

  public closeModal(template: TemplateRef<any>): void {
    if (this.files.length > 0) {
      let data = {};
      data = {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      };
      this.modalRef1 = this.modalService.show(template, data);
    } else {
      this.resetForm('yes');
    }
  }

  public openEditMultipleModal(template: TemplateRef<any>): void {
    this.deliverForm();
    this.setDefaultPerson();
    this.currentTemplate = template;
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-delivery-popup custom-modal edit-multiple-modal',
    };
    this.modalRef = this.modalService.show(this.currentTemplate, data);
    this.selectedDeliveryRequestId = this.selectedDeliveryRequestId.replace(/,\s*$/, '');
  }

  public deliverForm(): void {
    this.deliverEditMultipleForm = this.formBuilder.group({
      EquipmentId: [this.formBuilder.array([])],
      GateId: [''],
      person: [''],
      deliveryDate: [''],
      deliveryStart: [''],
      deliveryEnd: [''],
      escort: [false],
      companyItems: [this.formBuilder.array([])],
      defineItems: [this.formBuilder.array([])],
      status: [''],
      void: [''],
      cranePickUpLocation: [''],
      craneDropOffLocation: [''],
    });
  }

  public getOverAllGateInNewDelivery(): void {
    this.modalLoader = true;
    const params = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .gateList(params, { isFilter: true, showActivatedAlone: true })
      .subscribe((res): void => {
        this.gateList = res.data;
        this.getOverAllEquipmentInNewDelivery();
      });
  }

  public getOverAllEquipmentInNewDelivery(): void {
    const newNdrGetEquipmentsParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listEquipment(newNdrGetEquipmentsParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((equipmentListResponseForNewNdr): void => {
        this.equipmentList = [this.noEquipmentOption, ...equipmentListResponseForNewNdr.data];
        this.equipmentDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'equipmentName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
        this.newNdrgetCompanies();
      });
  }

  public newNdrgetCompanies(): void {
    const newNdrGetCompaniesParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getCompanies(newNdrGetCompaniesParams)
      .subscribe((companiesResponseForNewNdr: any): void => {
        if (companiesResponseForNewNdr) {
          this.companyList = companiesResponseForNewNdr.data;
          this.getDefinable();
          this.newNdrCompanyDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'companyName',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
        }
      });
  }

  public onEditSubmitForm(fieldName: string): void {
    if (fieldName === 'companies') {
      this.companyEdited = true;
    }
    if (fieldName === 'dfow') {
      this.dfowEdited = true;
    }
    if (fieldName === 'escort') {
      this.escortEdited = true;
    }
    if (fieldName === 'persons') {
      this.responsiblePersonEdited = true;
    }
    if (fieldName === 'gate') {
      this.gateEdited = true;
    }
    if (fieldName === 'deliveryDate') {
      if (this.deliverEditMultipleForm.get('deliveryDate').value) {
        this.setDefaultDeliveryTime();
        this.deliveryDateEdited = true;
      } else {
        this.deliveryDateEdited = false;
        this.deliverEditMultipleForm.get('deliveryStart').setValue('');
        this.deliverEditMultipleForm.get('deliveryEnd').setValue('');
      }
    }
    if (fieldName === 'void') {
      this.voidEdited = true;
    }
    if (fieldName === 'status') {
      this.statusEdited = true;
    }
  }

  public setDefaultDeliveryTime(): void {
    const setStartTime = 7;
    this.deliveryStart = new Date();
    this.deliveryStart.setHours(setStartTime);
    this.deliveryStart.setMinutes(0);
    this.deliveryEnd = new Date();
    this.deliveryEnd.setHours(setStartTime + 1);
    this.deliveryEnd.setMinutes(0);
    this.deliverEditMultipleForm.get('deliveryStart').setValue(this.deliveryStart);
    this.deliverEditMultipleForm.get('deliveryEnd').setValue(this.deliveryEnd);
  }

  public convertStart(deliveryDate: Date, startHours: number, startMinutes: number): string {
    const fullYear = deliveryDate.getFullYear();
    const fullMonth = deliveryDate.getMonth();
    const date = deliveryDate.getDate();
    const deliveryNewStart = new Date(fullYear, fullMonth, date, startHours, startMinutes);
    const deliveryStart = deliveryNewStart.toUTCString();
    return deliveryStart;
  }

  public checkNewDeliveryStartEndSame(
    newDeliveryStartTime: DateInput,
    newDeliveryEndTime: DateInput,
  ): boolean {
    const startDate = new Date(newDeliveryStartTime).getTime();
    const endDate = new Date(newDeliveryEndTime).getTime();
    if (startDate === endDate) {
      return true;
    }
    return false;
  }

  public setDefaultPerson(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.getMemberRole(params).subscribe((res): void => {
      this.authUser = res.data;
      this.deliveryService.updateLoginUser(this.authUser);
      let email: string;
      if (this.authUser.User.lastName != null) {
        email = `${this.authUser.User.firstName} ${this.authUser?.User?.lastName} (${this.authUser.User.email})`;
      } else {
        email = `${this.authUser.User.firstName} (${this.authUser.User.email})`;
      }
      const newMemberList = [
        {
          email,
          id: this.authUser.id,
          readonly: true,
        },
      ];
      this.deliverEditMultipleForm.get('person').patchValue(newMemberList);
    });
  }

  public getDefinable(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getDefinableWork(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.defineListData = data;
        this.newNdrDefinableDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'DFOW',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
      }
    });
  }

  public requestAutocompleteItems = (text: string): Observable<any> => {
    const param = {
      ProjectId: this.ProjectId,
      search: text,
      ParentCompanyId: this.ParentCompanyId,
    };
    return this.deliveryService.searchNewMember(param);
  };

  public numberOnly(event: { which: any; keyCode: any }): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public changeDate(event: any): void {
    if (!this.editModalLoader) {
      const startTime = new Date(event).getHours();
      const minutes = new Date(event).getMinutes();
      this.deliveryEnd = new Date();
      this.deliveryEnd.setHours(startTime + 1);
      this.deliveryEnd.setMinutes(minutes);
      this.deliverEditMultipleForm.get('deliveryEnd').setValue(this.deliveryEnd);
      this.NDRTimingChanged = true;
    }
  }

  public deliveryEndTimeChangeDetection(): void {
    this.NDRTimingChanged = true;
  }

  public openConfirmationPopup(template: TemplateRef<any>): void {
    this.editedFields = '';

    const formValues = this.deliverEditMultipleForm.value;

    const conditions: { [key: string]: { edited: boolean; valid: boolean; label: string } } = {
      company: {
        edited: this.companyEdited,
        valid: !!formValues.companyItems?.length,
        label: 'Responsible Company',
      },
      dfow: {
        edited: this.dfowEdited,
        valid: !!formValues.defineItems?.length,
        label: 'Definable Feature Of Work',
      },
      escort: {
        edited: this.escortEdited,
        valid: formValues.escort === true,
        label: 'Escort',
      },
      person: {
        edited: this.responsiblePersonEdited,
        valid: Array.isArray(formValues.person) && formValues.person.length > 1,
        label: 'Responsible Person',
      },
      gate: {
        edited: this.gateEdited,
        valid: !!formValues.GateId,
        label: 'Gate',
      },
      equipment: {
        edited: this.equipmentEdited,
        valid: !!formValues.EquipmentId?.length,
        label: 'Equipment',
      },
      deliveryDate: {
        edited: this.deliveryDateEdited,
        valid: !!formValues.deliveryDate,
        label: 'Delivery Date',
      },
      void: {
        edited: this.voidEdited,
        valid: !!formValues.void,
        label: 'Void',
      },
      status: {
        edited: this.statusEdited,
        valid: !!formValues.status,
        label: 'Status',
      },
    };

    const editedLabels = Object.values(conditions)
      .filter((cond) => cond.edited && cond.valid)
      .map((cond) => cond.label);

    this.editedFields = editedLabels.join(', ');

    if (this.editedFields) {
      const data = {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
      };
      this.modalRef2 = this.modalService.show(template, data);
    }
  }


  public editMultipleConfirmation(action: string): void {
    if (action === 'no') {
      this.modalRef2.hide();
    } else {
      this.modalRef2.hide();
      this.onEditMultipleSubmit();
    }
  }

  public checkStartEnd(
    deliveryStart: DateInput,
    deliveryEnd: DateInput,
  ): boolean {
    const startDate = new Date(deliveryStart).getTime();
    const endDate = new Date(deliveryEnd).getTime();
    if (startDate < endDate) {
      return true;
    }
    return false;
  }

  public checkEditDeliveryFutureDate(
    editDeliveryStart: DateInput,
    editDeliveryEnd: DateInput,
  ): boolean {
    const editStartDate = new Date(editDeliveryStart).getTime();
    const editCurrentDate = new Date().getTime();
    const editEndDate = new Date(editDeliveryEnd).getTime();
    if (editStartDate > editCurrentDate && editEndDate > editCurrentDate) {
      return true;
    }
    return false;
  }

  public onEditMultipleSubmit(): void {
    const payload = this.preparePayload();
    if (!payload) {
      return;
    }
    this.submitEditMultiple(payload);
  }

  public preparePayload() {
    if (this.deliveryDateEdited) {
      const deliveryDate = new Date(this.deliverEditMultipleForm.get('deliveryDate').value);
      const start = new Date(this.deliverEditMultipleForm.get('deliveryStart').value);
      const end = new Date(this.deliverEditMultipleForm.get('deliveryEnd').value);

      this.deliveryStart = this.convertStart(deliveryDate, start.getHours(), start.getMinutes());
      this.deliveryEnd = this.convertStart(deliveryDate, end.getHours(), end.getMinutes());

      if (this.checkNewDeliveryStartEndSame(this.deliveryStart, this.deliveryEnd)) {
        this.toastr.error('Delivery Start time and End time should not be the same');
        return null;
      }
      if (!this.checkStartEnd(this.deliveryStart, this.deliveryEnd)) {
        this.toastr.error('Please Enter Start time Lesser than End time');
        return null;
      }
      if (!this.checkEditDeliveryFutureDate(this.deliveryStart, this.deliveryEnd)) {
        this.toastr.error('Please Enter Future Date.');
        return null;
      }
    } else {
      this.deliveryStart = null;
      this.deliveryEnd = null;
    }

    if (this.isAssociatedWithCraneRequest) {
      if (
        !this.deliverEditMultipleForm.get('cranePickUpLocation').value
        || !this.deliverEditMultipleForm.get('craneDropOffLocation').value
      ) {
        this.toastr.error('Please enter Picking From and Picking To');
        return null;
      }
    }

    const payload: any = {
      deliveryRequestIds: this.selectedDeliveryRequestIdForMultipleEdit,
      escort: this.deliverEditMultipleForm.get('escort').value,
      GateId: this.deliverEditMultipleForm.get('GateId').value,
      deliveryStart: this.deliveryStart,
      deliveryEnd: this.deliveryEnd,
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      editedFields: this.editedFields,
      status: this.deliverEditMultipleForm.get('status').value,
      void: this.voidvalue,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      companies: this.extractIds('companyItems'),
      persons: this.extractIds('person'),
      define: this.extractIds('defineItems'),
      EquipmentId: this.extractIds('EquipmentId'),
      cranePickUpLocation: this.isAssociatedWithCraneRequest
        ? this.deliverEditMultipleForm.get('cranePickUpLocation').value.trim()
        : null,
      craneDropOffLocation: this.isAssociatedWithCraneRequest
        ? this.deliverEditMultipleForm.get('craneDropOffLocation').value.trim()
        : null,
      isAssociatedWithCraneRequest: this.isAssociatedWithCraneRequest,
    };

    return payload;
  }

  public extractIds(formControlName: string): any[] | null {
    const { value } = this.deliverEditMultipleForm.get(formControlName);
    if (value && value.length > 0) {
      return value.map((item: { id: any }) => item.id);
    }
    return null;
  }

  public submitEditMultiple(payload: any): void {
    this.editMultipleSubmitted = true;
    this.deliveryService.updateRequest(payload).subscribe({
      next: (editNdrResponse: any): void => {
        if (editNdrResponse) {
          this.editNDRSuccess(editNdrResponse);
        }
      },
      error: (editNDRHistoryError): void => {
        this.showErrorMessage(editNDRHistoryError);
      },
    });
  }

  public showErrorMessage(editNDRHistoryError): void {
    this.editMultipleSubmitted = false;
    if (editNDRHistoryError.message?.statusCode === 400) {
      this.showError(editNDRHistoryError);
    } else if (!editNDRHistoryError.message) {
      this.toastr.error('Try again later.!', 'Something went wrong.');
    } else {
      this.toastr.error(editNDRHistoryError.message, 'OOPS!');
    }
  }

  public editNDRSuccess(response: { message: string }): void {
    this.toastr.success(response.message, 'Success');
    this.socket.emit('NDREditHistory', response);
    this.deliverForm();
    this.deliveryService.updatedHistory({ status: true }, 'NDREditHistory');
    this.NDRTimingChanged = false;
    this.companyEdited = false;
    this.dfowEdited = false;
    this.escortEdited = false;
    this.responsiblePersonEdited = false;
    this.gateEdited = false;
    this.equipmentEdited = false;
    this.voidEdited = false;
    this.statusEdited = false;
    this.deliveryDateEdited = false;
    this.modalRef.hide();
    this.currentDeliveryRequestSelectAll = false;
    this.editMultipleSubmitted = false;
    this.voidvalue = false;
    this.selectedDeliveryRequestId = '';
    this.craneEquipmentTypeChosen = false;
    this.isAssociatedWithCraneRequest = false;
    this.deliverEditMultipleForm.reset();
    this.selectedDeliveryRequestIdForMultipleEdit = [];
  }

  public checkEquipmentType(event: any): void {
    let findEquipmentType: any;

    if (event && event.length > 0) {
      this.equipmentEdited = true;
      event.some((ev) => {
        findEquipmentType = this.equipmentList.find((item) => item.id === ev.id);
        if (findEquipmentType) {
          this.craneEquipmentTypeChosen = findEquipmentType.PresetEquipmentType.isCraneType;
          if (this.craneEquipmentTypeChosen) {
            this.isAssociatedWithCraneRequest = true;
            return true; // exit .some()
          }
          this.isAssociatedWithCraneRequest = false;
        }
        return false;
      });
    } else {
      this.craneEquipmentTypeChosen = false;
    }
  }
}
