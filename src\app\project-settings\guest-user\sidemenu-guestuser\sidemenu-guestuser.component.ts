import {
  Component, EventEmitter, OnInit, Output,
} from '@angular/core';
import { Router } from '@angular/router';
import { ProjectSettingsService, ProjectSharingService } from '../../../services';

@Component({
  selector: 'app-sidemenu-guestuser',
  templateUrl: './sidemenu-guestuser.component.html',
  })
export class SidemenuGuestuserComponent implements OnInit {
  @Output() toggleEmitEvent = new EventEmitter<any>();

  public isCompanyCollapsed = true;

  public isOpened = true;

  public isPublicWebsiteEnabled: boolean;

  public showDeliveryCalendar: boolean;

  public showCraneCalendar: boolean;

  public showConcreteCalendar: boolean;

  public ProjectId: number;

  public ParentCompanyId: number;

  public constructor(
    public projectSettingsService: ProjectSettingsService,
    private readonly ProjectSharingServices: ProjectSharingService,
    public router: Router,
  ) {
    this.ProjectId = +window.atob(localStorage.getItem('guestProjectId'));
    this.ParentCompanyId = +window.atob(localStorage.getItem('guestParentCompanyId'));

    this.getProjectSettings();
  }

  public closeSettingsCollapse(): void {
    this.toggleEmitEvent.emit();
  }

  public ngOnInit(): void { /* */ }

  public getProjectSettings(): void {
    const params = {
      ProjectId: this.ProjectId,
    };
    this.projectSettingsService.getGuestProjectSettings(params).subscribe((res): void => {
      const responseData = res?.data?.projectSettings;
      this.showDeliveryCalendar = responseData.allowGuestToViewDeliveryCalendar;
      this.showCraneCalendar = responseData.allowGuestToViewCraneCalendar;
      this.showConcreteCalendar = responseData.allowGuestToViewConcreteCalendar;
      this.isPublicWebsiteEnabled = responseData.isPublicWebsiteEnabled;
    });
  }

  public handleToggleKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.closeSettingsCollapse();
    }
  }

  public homePage(): void {
    this.router.navigate([window.atob(localStorage.getItem('url'))]);
  }
}
