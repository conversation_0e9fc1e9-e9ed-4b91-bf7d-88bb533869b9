<div class="footer-side">
  <ul class="list-group foot-list-group">
    <!-- Dropdown Menu -->
    <li
      class="list-group-item dropdown-parent-list project-list mb-3 py-2 border-0"
      routerLinkActive="active"
    >
      <a
        [attr.aria-expanded]="!isCollapsed"
        aria-controls="collapseBasic"
        class="position-relative footer-menu"
      >
        <img
          src="./assets/images/default-user.svg"
          class="profile-img rounded-circle eye-cursor"
          alt="Profile"
          *ngIf="currentUser.profilePic == '' || currentUser.profilePic == null"
          (click)="changeSettingsCollapse()"  (keydown)="handleDownKeydown($event, 'change')"
        />
        <a href="{{ currentUser?.profilePic }}"
          ><img
            src="{{ currentUser?.profilePic }}"
            class="profile-img rounded-circle eye-cursor"
            alt="Profile"
            *ngIf="currentUser.profilePic != '' && currentUser?.profilePic != null"
            (click)="changeSettingsCollapse()"  (keydown)="handleDownKeydown($event, 'change')"
        /></a>
        <span class="menu-txt side-menu-li color-grey20 fs12">
          <span
            class="eye-cursor profile-name"
            (click)="changeSettingsCollapse()"  (keydown)="handleDownKeydown($event, 'change')"
            *ngIf="currentUser?.firstName && currentUser?.firstName?.length <= 20"
          >
            {{ currentUser?.firstName | titlecase }}
          </span>
          <span
            class="eye-cursor profile-name eye-cursor"
            *ngIf="
              currentUser?.firstName &&
              currentUser?.firstName?.length > 20 &&
              currentUser?.firstName !== 'Undefined Undefined'
            "
            (click)="changeSettingsCollapse()"  (keydown)="handleDownKeydown($event, 'change')"
            tooltip="{{ currentUser?.firstName }}"
            placement="top"
          >
            {{ (currentUser?.firstName | slice : 0 : 13) + '..' | titlecase }}
          </span>
          <img
            [ngClass]="{ rotateup: !isCollapsed }"
            (click)="changeSettingsCollapse()"
            src="./assets/images/down-arrow.svg"
            alt="Down Arrow"
            class="arrow float-end footer-arrow eye-cursor"  (keydown)="handleDownKeydown($event, 'change')"
          />
        </span>
      </a>
    </li>
    <li class="sub-menu menu-txt footer-point" [collapse]="isCollapsed" [isAnimated]="true">
      <ul class="sub-list list-group sub-list-scroll">
        <li class="list-group-item color-grey7 fs11 fw600 text-end" (click)="logout()"  (keydown)="handleDownKeydown($event, 'logout')">Logout</li>
        <li
          class="list-group-item color-grey7 fs11 fw600 text-end"
          routerLink="/profile"
          (click)="isCollapsed = !isCollapsed"  (keydown.enter)="isCollapsed = !isCollapsed"
        >
          Profile
        </li>
      </ul>
    </li>
    <!-- Dropdown Menu -->
  </ul>
</div>
