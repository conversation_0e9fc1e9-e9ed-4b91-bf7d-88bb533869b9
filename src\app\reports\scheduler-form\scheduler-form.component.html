<div class="modal-header">
  <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Schedule Report</h1>
  <button type="button" class="close ms-auto" aria-label="Close" (click)="scheduleClose()">
    <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close" /></span>
  </button>
</div>
<div class="modal-body py-0">
  <div class="addcalendar-details m-1">
    <form
      name="exportform"
      class="custom-material-form heatmap-schedule-form"
      [formGroup]="scheduleForm"
    >
      <div class="row">
        <div class="col-md-6">
          <div class="form-group mt-4">
            <div class="floating-concrete">
              <div class="form-group floating-label">
                <input
                  class="floating-input form-control fs12 px-0"
                  type="text"
                  placeholder=" "
                  formControlName="reportName"
                  id="reportName"
                />
                <div
                  class="color-red fs12"
                  *ngIf="submitted && scheduleForm.get('reportName').errors"
                >
                  <small *ngIf="scheduleForm.get('reportName').errors.required"
                    >*Report Name is Required.</small
                  >
                </div>
                <label for="reportName" class="fs12 fw600 m-0 colorgrey11 schedule-form-label"
                  >Report Name
                  <span class="color-red">
                    <sup>*</sup>
                  </span></label
                >
              </div>
            </div>
          </div>
          <div class="row form-group mobile-display-block">
            <div class="col-md-6">
              <label class="fs12 fw600 m-0 color-grey11" for="runReport"
                >Run Report At
                <span class="color-red">
                  <sup>*</sup>
                </span>
              </label>
              <div class="input-group mx-0 pt-2">
                <input id="runReport"
                  formControlName="startDate"
                  placeholder="Run Report At"
                  class="form-control fs12 fw500 material-input"
                  placement="bottom"
                  #dp="bsDatepicker"
                  bsDatepicker
                  [bsConfig]="{
                    isAnimated: true,
                    showWeekNumbers: false,
                    displayMonths: 1,
                    customTodayClass: 'today'
                  }"
                  (ngModelChange)="showMonthlyRecurrence()"
                />

                  <span class="input-group-text">
                    <img
                      src="./assets/images/date.svg"
                      class="h-12px"
                      alt="Date"
                      (click)="dp.toggle()"
                      [attr.aria-expanded]="dp.isOpen"
                      (keydown)="dp.toggle()"
                    />
                  </span>
              </div>
              <div
                class="color-red fs12"
                *ngIf="
                  submitted &&
                  (scheduleForm.get('startDate').errors || scheduleForm.get('startTime').errors)
                "
              >
                <small
                  *ngIf="
                    scheduleForm.get('startDate').errors.required ||
                    scheduleForm.get('startTime').errors.required
                  "
                  >*Run Report At is Required.</small
                >
              </div>
            </div>
            <div class="col-md-6">
              <div class="row form-group mx-0">
                <div class="col-md-6 float-start ps-0">
                  <div class="input-group mb-0 delivery-time">
                    <timepicker [formControlName]="'startTime'" class="mt-2"> </timepicker>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="scheduler-form-input">
            <tag-input
            formControlName="sendTo"
            [onlyFromAutocomplete]="true"
            [placeholder]="'Send Report to'"
            [secondaryPlaceholder]="'Send Report to'"
            [onTextChangeDebounce]="500"
            class="tag-layout newdelivery-taglayout w-100"
            [identifyBy]="'id'"
            [displayBy]="'UserEmail'"
          >
            <tag-input-dropdown
              [showDropdownIfEmpty]="true"
              [keepOpen]="true"
              [displayBy]="'UserEmail'"
              [identifyBy]="'id'"
              [autocompleteObservable]="requestAutocompleteItems"
              [appendToBody]="false"
            >
              <ng-template let-item="item" let-index="index">
                <span class="fs10">{{ item.UserEmail }}</span>
              </ng-template>
            </tag-input-dropdown>
          </tag-input>
            <div class="color-red fs12" *ngIf="submitted && scheduleForm.get('sendTo').errors">
              <small *ngIf="scheduleForm.get('sendTo').errors.required"
                >*Send Report To is Required.</small
              >
            </div>
          </div>

          <div class="form-group">
            <div class="floating-concrete">
              <div class="form-group floating-label">
                <input id="emailSubject"
                  class="floating-input form-control fs12 px-0"
                  type="text"
                  placeholder=" "
                  formControlName="subject"
                />
                <div class="color-red fs12" *ngIf="submitted && scheduleForm.get('subject').errors">
                  <small *ngIf="scheduleForm.get('subject').errors.required"
                    >*Email Subject is Required.</small
                  >
                </div>
                <label for="emailMessage" class="fs12 fw600 m-0 colorgrey11 schedule-form-label"
                  >Email Subject
                  <span class="color-red">
                    <sup>*</sup>
                  </span>
                </label>
              </div>
            </div>
          </div>
          <div class="form-group">
            <label class="fs12 fw600 color-grey11" for="emailMessage"
              >Email Message
              <span class="color-red">
                <sup>*</sup>
              </span></label
            >
            <textarea id="emailMessage"
              class="form-control fs11 radius0 mt-1"
              rows="2"
              formControlName="message"
            ></textarea>
            <div class="color-red fs12" *ngIf="submitted && scheduleForm.get('message').errors">
              <small *ngIf="scheduleForm.get('message').errors.required"
                >*Email Message is Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <label class="fs12 fw600 m-0 color-grey11" for="fileFormat"
            >File Format
            <span class="color-red">
              <sup>*</sup>
            </span>
          </label>
          <div class="form-group">
            <select class="form-control fs12 material-input px-2" formControlName="outputFormat" id="fileFormat">
              <option value="" disabled selected hidden>
                File Format
                <span class="color-red">
                  <sup>*</sup>
                </span>
              </option>
              <option *ngFor="let type of exportType" value="{{ type }}">
                {{ type }}
              </option>
            </select>
            <div
              class="color-red fs12"
              *ngIf="submitted && scheduleForm.get('outputFormat').errors"
            >
              <small *ngIf="scheduleForm.get('outputFormat').errors.required"
                >*File Format is Required.</small
              >
            </div>
          </div>
          <div class="row">
            <div class="col-md-12 pt-3">
              <label class="fs12 fw600 m-0 color-grey11" for="recurrence"
                >Recurrence
                <span class="color-red">
                  <sup>*</sup>
                </span></label
              >
              <div class="form-group">
                <select id="recurrence"
                  class="form-control fs12 material-input px-2"
                  formControlName="recurrence"
                  (change)="onRecurrenceSelect($event.target.value)"
                >
                  <option *ngFor="let type of recurrence" value="{{ type.value }}">
                    {{ type.value }}
                  </option>
                </select>
              </div>
            </div>
          </div>
          <div
            class="row"
            *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
          >
            <div
              class="col-md-12 mt-md-0"
              *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
            >
              <label class="fs12 fw600 m-0 color-grey11" for="repeatEvery"
                >Repeat Every
                <span class="color-red">
                  <sup>*</sup>
                </span></label
              >
            </div>
            <div
              class="col-md-4 mt-md-0"
              *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
            >
              <div class="form-group">
                <input id="repeatEvery"
                  type="text"
                  formControlName="repeatEveryCount"
                  class="form-control fs12 material-input p-0"
                  (input)="changeRecurrenceCount($event.target.value)"
                  min="1"
                />
                <div
                  class="color-red fs12"
                  *ngIf="submitted && scheduleForm.get('repeatEveryCount').errors"
                >
                  <small *ngIf="scheduleForm.get('repeatEveryCount').errors.required"
                    >*Required.</small
                  >
                </div>
              </div>
            </div>
            <div class="col-md-4 mt-md-0" *ngIf="isRepeatWithSingleRecurrence">
              <div class="form-group">
                <select
                  class="form-control fs12 material-input px-2"
                  formControlName="repeatEveryType"
                  (change)="chooseRepeatEveryType($event.target.value)"
                >
                  <option value="" disabled selected hidden>Select Recurrence</option>
                  <option *ngFor="let type of repeatWithSingleRecurrence" value="{{ type.value }}">
                    {{ type.value }}
                  </option>
                </select>
              </div>
            </div>
            <div
              class="col-md-4 mt-md-0"
              *ngIf="isRepeatWithMultipleRecurrence || showRecurrenceTypeDropdown"
            >
              <div class="form-group">
                <select
                  class="form-control fs12 material-input px-2"
                  formControlName="repeatEveryType"
                  (change)="chooseRepeatEveryType($event.target.value)"
                >
                  <option value="" disabled selected hidden>Select Recurrence</option>
                  <option
                    *ngFor="let type of repeatWithMultipleRecurrence"
                    value="{{ type.value }}"
                  >
                    {{ type.value }}
                  </option>
                </select>
              </div>
            </div>
          </div>
          <div class="row addcalendar-displaydays">
            <div
              class="col-md-12 pt-0"
              *ngIf="
                (selectedRecurrence === 'Weekly' ||
                  isRepeatWithMultipleRecurrence ||
                  isRepeatWithSingleRecurrence) &&
                selectedRecurrence !== 'Monthly' &&
                selectedRecurrence !== 'Yearly'
              "
            >
              <ul class="displaylists ps-0">
                <li *ngFor="let item of weekDays; let i = index" class="fs12 list-inline-item">
                  <input
                    type="checkbox"
                    [disabled]="item.isDisabled"
                    [value]="item.value"
                    class="d-none"
                    id="days-{{ i }}"
                    (change)="onChange($event)"
                    [checked]="item.checked"
                  />
                  <label for="days-{{ i }}">{{ item.display }}</label>
                </li>
              </ul>
            </div>
          </div>
          <div
            class="row"
            *ngIf="selectedRecurrence === 'Monthly' || selectedRecurrence === 'Yearly'"
          >
            <div class="col-md-11 mt-md-0 ps-2">
              <div class="form-check">
                <input
                  class="form-check-input c-pointer"
                  type="radio"
                  formControlName="chosenDateOfMonth"
                  id="flexRadioDefault1"
                  [value]="1"
                  (change)="changeMonthlyRecurrence()"
                />
                <label class="form-check-label fs12 color-orange" for="flexRadioDefault1">
                  <span *ngIf="selectedRecurrence === 'Monthly'"> On day {{ monthlyDate }} </span>
                  <span *ngIf="selectedRecurrence === 'Yearly'">
                    On {{ scheduleForm.get('startDate').value | date : 'MMMM' }} {{ monthlyDate }}
                  </span>
                </label>
              </div>
              <div class="form-check">
                <input
                  class="form-check-input c-pointer"
                  type="radio"
                  formControlName="chosenDateOfMonth"
                  id="flexRadioDefault2"
                  [value]="2"
                  (change)="changeMonthlyRecurrence()"
                />
                <label class="form-check-label fs12 color-orange" for="flexRadioDefault2">
                  On the {{ monthlyDayOfWeek }}
                  <span *ngIf="selectedRecurrence === 'Yearly'">
                    of {{ scheduleForm.get('startDate').value | date : 'LLLL' }}
                  </span>
                </label>
              </div>
              <div class="form-check" *ngIf="enableOption">
                <input
                  class="form-check-input c-pointer"
                  type="radio"
                  formControlName="chosenDateOfMonth"
                  id="flexRadioDefault3"
                  [value]="3"
                  (change)="changeMonthlyRecurrence()"
                />
                <label class="form-check-label fs12 color-orange" for="flexRadioDefault3">
                  On the {{ monthlyLastDayOfWeek }}
                  <span *ngIf="selectedRecurrence === 'Yearly'">
                    of {{ scheduleForm.get('startDate').value | date : 'LLLL' }}
                  </span>
                </label>
              </div>
            </div>
          </div>
          <div class="row form-group">
            <div class="col-md-12">
              <label class="fs12 fw600 color-grey11 mb-0" for="dateRange"
                >Date Range
                <span class="color-red">
                  <sup>*</sup>
                </span></label
              >
              <div class="input-group mx-0 pt-2" (click)="openModal(daterangeModal)" (keydown)="handleToggleKeydown($event,daterangeModal)">
                <input id="dateRange" placeholder="Date Range" class="form-control fs12 fw500 material-input" value = "{{bsInlineRangeValue[0] | date:'EEE, dd MMM yyyy'}} - {{bsInlineRangeValue[1] | date:'EEE, dd MMM yyyy'}}"/>

                  <span class="input-group-text">
                    <img src="./assets/images/date.svg" class="h-12px" alt="Date" />
                  </span>
              </div>
            </div>
          </div>
          <div class="row form-group" *ngIf="selectedRecurrence != 'Does Not Repeat'">
            <div class="col-md-12">
              <label class="fs12 fw600 color-grey11 mb-0" for="recEndDate"
                >Recurrence End Date
                <span class="color-red">
                  <sup>*</sup>
                </span></label
              >
              <div class="input-group mx-0 pt-2">
                <input id="recEndDate"
                  formControlName="endDate"
                  placeholder="Recurrence End Date"
                  class="form-control fs12 fw500 material-input"
                  #dp="bsDatepicker"
                  placement="top"
                  bsDatepicker
                  [bsConfig]="{
                    isAnimated: true,
                    showWeekNumbers: false,
                    displayMonths: 1,
                    customTodayClass: 'today'
                  }"
                  (ngModelChange)="showMonthlyRecurrence()"
                />
                  <span class="input-group-text">
                    <img
                      src="./assets/images/date.svg"
                      class="h-12px"
                      alt="Date"
                      (click)="dp.toggle()"
                      [attr.aria-expanded]="dp.isOpen"
                      (keydown)="dp.toggle()"
                    />
                  </span>
              </div>
            </div>
          </div>
          <div class="color-red fs12" *ngIf="submitted && scheduleForm.get('endDate').errors">
            <small *ngIf="scheduleForm.get('endDate').errors.required"
              >*Recurrence End Date is Required.</small
            >
          </div>
          <div class="row addcalendar-displaydays">
            <div class="col-md-12 mt-md-0 pb-0" *ngIf="message">
              <p class="fs12 color-grey11">
                <span class="color-red fw-bold">*</span>
                {{ message }}
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer border-0 justify-content-center add-calendar-footer mt-0 py-0">
        <div class="mt-0 mb15 text-center">
          <button
            class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3 px-2rem"
            type="button"
            (click)="scheduleClose()"
          >
            Cancel
          </button>
          <button
            class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem"
            [disabled]="formSubmitted && scheduleForm.valid"
            (click)="scheduleSubmit()"
          >
            <em class="fa fa-spinner" aria-hidden="true" *ngIf="formSubmitted"></em>
            Submit
          </button>
        </div>
      </div>
    </form>
  </div>
</div>

<!--Date Range Picker Modal Start-->
<ng-template #daterangeModal>
  <div class="modal-header date-range-header">
    <h4 class="modal-title">Date Range</h4>
    <button
      type="button"
      class="btn-close close ms-auto h1 w1"
      aria-label="Close"
      (click)="modalRef?.hide()"
    >
      <span aria-hidden="true" class="visually-hidden">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <tabset [vertical]="true" type="pills" class="custom-vertical-tab">
      <tab *ngFor="let time of timeSlot" [heading]="time.timelineName" (selectTab) = "changeTimeRange(time)" [active]="time.order === order">
        <bs-daterangepicker-inline *ngIf = "time.timelineName == 'Custom'" [bsValue]="bsInlineRangeValue" (bsValueChange) = "changeDateRange($event)"></bs-daterangepicker-inline>
        <bs-daterangepicker-inline *ngIf = "time.timelineName != 'Custom'" [bsValue]="bsInlineRangeValue" isDisabled = true (bsValueChange) = "changeDateRange($event)"></bs-daterangepicker-inline>
      </tab>
    </tabset>
    <div class="d-flex justify-content-between align-items-center date-range-footer">
      <div>
        <span class="date-range-info">{{bsInlineRangeValue[0] | date:'EEE, dd MMM yyyy'}} - {{bsInlineRangeValue[1] | date:'EEE, dd MMM yyyy'}}</span>
      </div>
      <div>
        <button
          class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3 px-2rem"
          type="button"  (click)="modalRef?.hide()"
        >
          Cancel
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem" (click)="modalRef?.hide()"
        >
          Apply
        </button>
      </div>
    </div>
  </div>
</ng-template>
<!--Date Range Picker Modal End-->
