import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EditCraneRequestComponent } from './edit-crane-request.component';
import { FormBuilder, ReactiveFormsModule, FormsModule, UntypedFormGroup, UntypedFormControl } from '@angular/forms';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { Socket } from 'ngx-socket-io';
import { MixpanelService } from '../../services/mixpanel.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';
import { ProjectSettingsService } from '../../services/project_settings/project-settings.service';
import { of, throwError, BehaviorSubject } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import moment from 'moment';

// Mock jest functions for testing
declare const jest: any;
declare const describe: any;
declare const it: any;
declare const expect: any;
declare const beforeEach: any;

describe('EditCraneRequestComponent', () => {
  let component: EditCraneRequestComponent;
  let fixture: ComponentFixture<EditCraneRequestComponent>;
  let mockFormBuilder: FormBuilder;
  let mockBsModalRef: Partial<BsModalRef>;
  let mockBsModalService: Partial<BsModalService>;
  let mockToastrService: Partial<ToastrService>;
  let mockRouter: Partial<Router>;
  let mockSocket: Partial<Socket>;
  let mockMixpanelService: Partial<MixpanelService>;
  let mockDeliveryService: Partial<DeliveryService>;
  let mockProjectService: Partial<ProjectService>;
  let mockProjectSettingsService: Partial<ProjectSettingsService>;

  beforeEach(async () => {
    mockFormBuilder = new FormBuilder();
    mockBsModalRef = {
      hide: jest.fn()
    };
    mockBsModalService = {
      show: jest.fn()
    };
    mockToastrService = {
      success: jest.fn(),
      error: jest.fn()
    };
    mockRouter = {
      navigate: jest.fn()
    };
    mockSocket = {
      emit: jest.fn()
    };
    mockMixpanelService = {
      addMixpanelEvents: jest.fn()
    };
    mockDeliveryService = {
      getEquipmentCraneRequest: jest.fn().mockReturnValue(of({ data: {} })),
      editCraneRequest: jest.fn().mockReturnValue(of({ message: 'Success' })),
      updateCraneRequestHistory: jest.fn(),
      searchNewMember: jest.fn().mockReturnValue(of({ data: [] })),
      getAvailableTimeSlots: jest.fn().mockReturnValue(of({ slots: { AM: [], PM: [] } })),
      getMemberRole: jest.fn().mockReturnValue(of({ data: {} })),
      EditCraneRequestId: new BehaviorSubject('test-id'),
      loginUser: new BehaviorSubject({ id: 1, name: 'Test User' })
    };
    mockProjectService = {
      projectParent: new BehaviorSubject({}),
      accountProjectParent: new BehaviorSubject({}),
      getLocations: jest.fn().mockReturnValue(of({ data: [] })),
      listAllMember: jest.fn().mockReturnValue(of({ data: [] }))
    };
    mockProjectSettingsService = {
      getProjectSettings: jest.fn().mockReturnValue(of({}))
    };

    await TestBed.configureTestingModule({
      declarations: [EditCraneRequestComponent],
      imports: [ReactiveFormsModule, FormsModule],
      providers: [
        { provide: FormBuilder, useValue: mockFormBuilder },
        { provide: BsModalRef, useValue: mockBsModalRef },
        { provide: BsModalService, useValue: mockBsModalService },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: Router, useValue: mockRouter },
        { provide: Socket, useValue: mockSocket },
        { provide: MixpanelService, useValue: mockMixpanelService },
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: ProjectService, useValue: mockProjectService },
        { provide: ProjectSettingsService, useValue: mockProjectSettingsService }
      ],
      schemas: [NO_ERRORS_SCHEMA] // Add this line to ignore unknown elements
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(EditCraneRequestComponent);
    component = fixture.componentInstance;

    // Mock the form controls that need value accessors
    component.craneEditRequest = mockFormBuilder.group({
      id: [''],
      EquipmentId: [[]],
      LocationId: [''],
      GateId: [''],
      additionalNotes: [''],
      CraneRequestId: [''],
      responsiblePersons: [[]],
      description: [''],
      deliveryDate: [''],
      craneDeliveryStart: [''],
      craneDeliveryEnd: [''],
      isEscortNeeded: [false],
      companies: [[]],
      definableFeatureOfWorks: [[]],
      pickUpLocation: [''],
      dropOffLocation: [''],
      isAssociatedWithDeliveryRequest: [false],
      recurrenceId: [''],
      recurrence: [''],
      recurrenceEndDate: [''],
      repeatEveryCount: [''],
      repeatEveryType: [''],
      days: mockFormBuilder.array([]),
      chosenDateOfMonth: [false],
      dateOfMonth: [''],
      monthlyRepeatType: [''],
      endDate: ['']
    });

    // Initialize checkform as FormArray
    component.checkform = mockFormBuilder.array([]);

    // Mock additional component methods
    component.throwError = jest.fn();
    component.showError = jest.fn();
    component.resetForm = jest.fn();
    component.onEditSubmitForm = jest.fn();

    // Initialize component properties
    component.ProjectId = 'test-project-id';
    component.ParentCompanyId = 'test-company-id';
    component.NDRTimingChanged = false;
    component.formEdited = false;
    component.formEditSubmitted = false;
    component.editSubmitted = false;
    component.gateList = [];
    component.equipmentList = [];
    component.definableFeatureOfWorkList = [];
    component.weekDates = [];
    component.weekDays = [
      { value: 'Monday', checked: false },
      { value: 'Tuesday', checked: false },
      { value: 'Wednesday', checked: false },
      { value: 'Thursday', checked: false },
      { value: 'Friday', checked: false },
      { value: 'Saturday', checked: false },
      { value: 'Sunday', checked: false }
    ];

    // Skip detectChanges to avoid template rendering issues
    // fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form on construction', () => {
    expect(component.craneEditRequest).toBeInstanceOf(UntypedFormGroup);
    expect(component.craneEditRequest.get('description')).toBeTruthy();
    expect(component.craneEditRequest.get('CraneRequestId')).toBeTruthy();
  });

  it('should handle form submission with valid data', () => {
    const mockFormValue = {
      description: 'Test Description',
      CraneRequestId: '123',
      companies: [],
      EquipmentId: ['1'],
      LocationId: '1',
      GateId: '1',
      deliveryDate: '2024-03-20',
      craneDeliveryStart: new Date(),
      craneDeliveryEnd: new Date(),
      id: '1',
      additionalNotes: 'Test notes',
      responsiblePersons: [],
      isEscortNeeded: false,
      definableFeatureOfWorks: [],
      pickUpLocation: 'Location A',
      dropOffLocation: 'Location B'
    };

    // Set up component properties needed for form submission
    component.currentEditItem = {
      status: 'Draft',
      deliveryDate: '2024-03-20',
      craneDeliveryStart: new Date(),
      craneDeliveryEnd: new Date()
    };
    component.authUser = { RoleId: 1 };
    component.formEdited = true;

    // Mock the checkStartEnd method to return true
    component.checkStartEnd = jest.fn().mockReturnValue(true);

    // Mock the updateCraneDelivery method
    component.updateCraneDelivery = jest.fn();

    // Set up the form with valid values
    component.craneEditRequest.patchValue(mockFormValue);

    // Mark the form as valid
    Object.defineProperty(component.craneEditRequest, 'valid', { get: () => true });

    component.onEditSubmit();

    // Check if updateCraneDelivery was called
    expect(component.updateCraneDelivery).toHaveBeenCalled();
  });

  it('should show error toast when form is invalid', () => {
    component.onEditSubmit();
    expect(mockToastrService.error).toHaveBeenCalled();
  });

  it('should handle successful edit request', () => {
    const mockResponse = { message: 'Success' };
    component.editCraneRequestSuccess(mockResponse);

    expect(mockToastrService.success).toHaveBeenCalledWith('Success', 'Success');
    expect(mockSocket.emit).toHaveBeenCalledWith('CraneEditHistory', mockResponse);
    expect(mockDeliveryService.updateCraneRequestHistory).toHaveBeenCalled();
  });

  it('should detect delivery end time changes', () => {
    component.deliveryEndTimeChangeDetection();
    expect(component.NDRTimingChanged).toBeTruthy();
    expect(component.formEdited).toBeFalsy();
  });

  it('should build edit crane request payload correctly', () => {
    const mockFormValue = {
      id: '1',
      description: 'Test',
      isEscortNeeded: true,
      additionalNotes: 'Notes',
      EquipmentId: ['1'],
      LocationId: '1',
      craneDeliveryStart: new Date(),
      craneDeliveryEnd: new Date(),
      pickUpLocation: 'Location 1',
      dropOffLocation: 'Location 2',
      GateId: '1'
    };

    const payload = component.buildEditCraneRequestPayload(
      mockFormValue,
      new Date(),
      new Date(),
      [],
      [],
      [],
      ['1']
    );

    expect(payload.id).toBe('1');
    expect(payload.description).toBe('Test');
    expect(payload.isEscortNeeded).toBe(true);
  });

  // Date/Time validation tests
  describe('checkStartEnd', () => {
    it('should return true when start date is before end date', () => {
      const startDate = new Date('2024-03-20T10:00:00');
      const endDate = new Date('2024-03-20T12:00:00');

      const result = component.checkStartEnd(startDate, endDate);

      expect(result).toBe(true);
    });

    it('should return false when start date is after end date', () => {
      const startDate = new Date('2024-03-20T12:00:00');
      const endDate = new Date('2024-03-20T10:00:00');

      const result = component.checkStartEnd(startDate, endDate);

      expect(result).toBe(false);
    });

    it('should return false when start date equals end date', () => {
      const startDate = new Date('2024-03-20T10:00:00');
      const endDate = new Date('2024-03-20T10:00:00');

      const result = component.checkStartEnd(startDate, endDate);

      expect(result).toBe(false);
    });

    it('should handle string date inputs', () => {
      const result = component.checkStartEnd('2024-03-20T10:00:00', '2024-03-20T12:00:00');
      expect(result).toBe(true);
    });

    it('should handle number date inputs', () => {
      const startTime = new Date('2024-03-20T10:00:00').getTime();
      const endTime = new Date('2024-03-20T12:00:00').getTime();

      const result = component.checkStartEnd(startTime, endTime);
      expect(result).toBe(true);
    });
  });

  // Form validation tests
  describe('checkEditDeliveryStringEmptyValues', () => {
    it('should return true and show error for empty description', () => {
      const formValue = {
        description: '   ',
        additionalNotes: 'Valid notes'
      };

      const result = component.checkEditDeliveryStringEmptyValues(formValue);

      expect(result).toBe(true);
      expect(mockToastrService.error).toHaveBeenCalledWith('Please Enter valid description.', 'OOPS!');
    });

    it('should return true and show error for empty additional notes', () => {
      const formValue = {
        description: 'Valid description',
        additionalNotes: '   '
      };

      const result = component.checkEditDeliveryStringEmptyValues(formValue);

      expect(result).toBe(true);
      expect(mockToastrService.error).toHaveBeenCalledWith('Please Enter valid notes.', 'OOPS!');
    });

    it('should return false for valid inputs', () => {
      const formValue = {
        description: 'Valid description',
        additionalNotes: 'Valid notes'
      };

      const result = component.checkEditDeliveryStringEmptyValues(formValue);

      expect(result).toBe(false);
    });

    it('should return false when additionalNotes is undefined', () => {
      const formValue = {
        description: 'Valid description',
        additionalNotes: undefined
      };

      const result = component.checkEditDeliveryStringEmptyValues(formValue);

      expect(result).toBe(false);
    });
  });

  describe('validateEditCraneRequestInputs', () => {
    it('should return false and show error when companies are missing', () => {
      const formValue = {
        companies: [],
        responsiblePersons: [{ id: 1 }],
        description: 'Valid description'
      };

      const result = component.validateEditCraneRequestInputs(formValue);

      expect(result).toBe(false);
      expect(mockToastrService.error).toHaveBeenCalledWith('Responsible Company is required');
    });

    it('should return false and show error when companies are null', () => {
      const formValue = {
        companies: null,
        responsiblePersons: [{ id: 1 }],
        description: 'Valid description'
      };

      const result = component.validateEditCraneRequestInputs(formValue);

      expect(result).toBe(false);
      expect(mockToastrService.error).toHaveBeenCalledWith('Responsible Company is required');
    });

    it('should return false and show error when responsible persons are missing', () => {
      const formValue = {
        companies: [{ id: 1 }],
        responsiblePersons: [],
        description: 'Valid description'
      };

      const result = component.validateEditCraneRequestInputs(formValue);

      expect(result).toBe(false);
      expect(mockToastrService.error).toHaveBeenCalledWith('Responsible Person is required');
    });

    it('should return false and show error when responsible persons are null', () => {
      const formValue = {
        companies: [{ id: 1 }],
        responsiblePersons: null,
        description: 'Valid description'
      };

      const result = component.validateEditCraneRequestInputs(formValue);

      expect(result).toBe(false);
      expect(mockToastrService.error).toHaveBeenCalledWith('Responsible Person is required');
    });

    it('should return false when string validation fails', () => {
      const formValue = {
        companies: [{ id: 1 }],
        responsiblePersons: [{ id: 1 }],
        description: '   ',
        additionalNotes: 'Valid notes'
      };

      jest.spyOn(component, 'checkEditDeliveryStringEmptyValues').mockReturnValue(true);

      const result = component.validateEditCraneRequestInputs(formValue);

      expect(result).toBe(false);
      expect(component.checkEditDeliveryStringEmptyValues).toHaveBeenCalledWith(formValue);
    });

    it('should return true for valid inputs', () => {
      const formValue = {
        companies: [{ id: 1 }],
        responsiblePersons: [{ id: 1 }],
        description: 'Valid description',
        additionalNotes: 'Valid notes'
      };

      jest.spyOn(component, 'checkEditDeliveryStringEmptyValues').mockReturnValue(false);

      const result = component.validateEditCraneRequestInputs(formValue);

      expect(result).toBe(true);
    });
  });

  // Error handling tests
  describe('editCraneRequest', () => {
    beforeEach(() => {
      component.formEditSubmitted = false;
      component.NDRTimingChanged = false;
    });

    it('should handle successful edit request', () => {
      const mockPayload = { id: 1, description: 'Test' };
      const mockResponse = { message: 'Success' };

      mockDeliveryService.editCraneRequest = jest.fn().mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'editCraneRequestSuccess').mockImplementation(() => {});

      component.editCraneRequest(mockPayload);

      expect(mockDeliveryService.editCraneRequest).toHaveBeenCalledWith(mockPayload);
      expect(component.editCraneRequestSuccess).toHaveBeenCalledWith(mockResponse);
      expect(mockMixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Edited Crane Booking');
    });

    it('should handle error with status code 400', () => {
      const mockPayload = { id: 1, description: 'Test' };
      const mockError = { message: { statusCode: 400 } };

      mockDeliveryService.editCraneRequest = jest.fn().mockReturnValue(throwError(mockError));

      component.editCraneRequest(mockPayload);

      expect(component.showError).toHaveBeenCalledWith(mockError);
      expect(component.NDRTimingChanged).toBe(false);
    });

    it('should handle error without message', () => {
      const mockPayload = { id: 1, description: 'Test' };
      const mockError = {};

      mockDeliveryService.editCraneRequest = jest.fn().mockReturnValue(throwError(mockError));

      component.editCraneRequest(mockPayload);

      expect(mockToastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
      expect(component.NDRTimingChanged).toBe(false);
    });

    it('should handle general error with message', () => {
      const mockPayload = { id: 1, description: 'Test' };
      const mockError = { message: 'General error' };

      mockDeliveryService.editCraneRequest = jest.fn().mockReturnValue(throwError(mockError));

      component.editCraneRequest(mockPayload);

      expect(mockToastrService.error).toHaveBeenCalledWith('General error', 'OOPS!');
      expect(component.NDRTimingChanged).toBe(false);
    });
  });

  describe('formReset', () => {
    it('should reset form submission flags', () => {
      component.formEditSubmitted = true;
      component.editSubmitted = true;

      component.formReset();

      expect(component.formEditSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
    });
  });

  // Utility method tests
  describe('areDifferentByProperty', () => {
    beforeEach(() => {
      component.array3Value = [];
    });

    it('should return true when arrays have different elements', () => {
      const array1 = [{ id: 1, name: 'A' }, { id: 2, name: 'B' }];
      const array2 = [{ id: 3, name: 'C' }];

      const result = component.areDifferentByProperty(array1, array2, 'id');

      expect(result).toBe(true);
      expect(component.array3Value).toEqual([1, 2, 3]);
    });

    it('should return false when arrays have same elements', () => {
      const array1 = [{ id: 1, name: 'A' }];
      const array2 = [{ id: 1, name: 'A' }];

      const result = component.areDifferentByProperty(array1, array2, 'id');

      expect(result).toBe(false);
      expect(component.array3Value).toEqual([1]);
    });

    it('should handle empty arrays', () => {
      const array1 = [];
      const array2 = [];

      const result = component.areDifferentByProperty(array1, array2, 'id');

      expect(result).toBe(false);
      expect(component.array3Value).toEqual([]);
    });

    it('should handle arrays with different properties', () => {
      const array1 = [{ name: 'John', age: 25 }];
      const array2 = [{ name: 'Jane', age: 30 }];

      const result = component.areDifferentByProperty(array1, array2, 'name');

      expect(result).toBe(true);
      expect(component.array3Value).toEqual(['John', 'Jane']);
    });
  });

  describe('numberOnly', () => {
    it('should return true for numeric characters', () => {
      const event = { which: 48, keyCode: 48 }; // '0'
      const result = component.numberOnly(event);
      expect(result).toBe(true);
    });

    it('should return true for numeric characters 0-9', () => {
      for (let i = 48; i <= 57; i++) {
        const event = { which: i, keyCode: i };
        const result = component.numberOnly(event);
        expect(result).toBe(true);
      }
    });

    it('should return false for non-numeric characters', () => {
      const event = { which: 65, keyCode: 65 }; // 'A'
      const result = component.numberOnly(event);
      expect(result).toBe(false);
    });

    it('should return true for special keys (backspace, delete, etc.)', () => {
      const event = { which: 8, keyCode: 8 }; // Backspace
      const result = component.numberOnly(event);
      expect(result).toBe(true);
    });

    it('should use keyCode when which is not available', () => {
      const event = { which: null, keyCode: 49 }; // '1'
      const result = component.numberOnly(event);
      expect(result).toBe(true);
    });
  });

  // updateCraneDelivery tests
  describe('updateCraneDelivery', () => {
    let mockParams;

    beforeEach(() => {
      mockParams = {
        editCraneRequestFormValue: {
          companies: [{ id: 1 }],
          responsiblePersons: [{ id: 1 }],
          definableFeatureOfWorks: [{ id: 1 }],
          EquipmentId: [{ id: 1 }],
          description: 'Test description'
        },
        deliveryStart: new Date('2024-03-20T10:00:00'),
        deliveryEnd: new Date('2024-03-20T12:00:00'),
        companies: [],
        responsbilePersonsData: [],
        definableFeatureOfWorkData: [],
        equipments: [],
        gates: []
      };
    });

    it('should return early if checkStartEnd fails', () => {
      jest.spyOn(component, 'checkStartEnd').mockReturnValue(false);
      jest.spyOn(component, 'throwError');
      jest.spyOn(component, 'validateEditCraneRequestInputs');

      component.updateCraneDelivery(mockParams);

      expect(component.throwError).toHaveBeenCalledWith('time error');
      expect(component.validateEditCraneRequestInputs).not.toHaveBeenCalled();
    });

    it('should return early if validation fails', () => {
      jest.spyOn(component, 'checkStartEnd').mockReturnValue(true);
      jest.spyOn(component, 'validateEditCraneRequestInputs').mockReturnValue(false);
      jest.spyOn(component, 'buildEditCraneRequestPayload');

      component.updateCraneDelivery(mockParams);

      expect(component.validateEditCraneRequestInputs).toHaveBeenCalledWith(mockParams.editCraneRequestFormValue);
      expect(component.buildEditCraneRequestPayload).not.toHaveBeenCalled();
    });

    it('should process arrays and call editCraneRequest when validation passes', () => {
      jest.spyOn(component, 'checkStartEnd').mockReturnValue(true);
      jest.spyOn(component, 'validateEditCraneRequestInputs').mockReturnValue(true);
      jest.spyOn(component, 'buildEditCraneRequestPayload').mockReturnValue({ id: 1 });
      jest.spyOn(component, 'editCraneRequest');

      component.updateCraneDelivery(mockParams);

      expect(mockParams.companies).toEqual([1]);
      expect(mockParams.responsbilePersonsData).toEqual([1]);
      expect(mockParams.definableFeatureOfWorkData).toEqual([1]);
      expect(mockParams.equipments).toEqual([1]);
      expect(component.editCraneRequest).toHaveBeenCalledWith({ id: 1 });
    });

    it('should handle undefined arrays gracefully', () => {
      const paramsWithUndefined = {
        ...mockParams,
        editCraneRequestFormValue: {
          ...mockParams.editCraneRequestFormValue,
          companies: undefined,
          responsiblePersons: undefined,
          definableFeatureOfWorks: undefined,
          EquipmentId: undefined
        }
      };

      jest.spyOn(component, 'checkStartEnd').mockReturnValue(true);
      jest.spyOn(component, 'validateEditCraneRequestInputs').mockReturnValue(true);
      jest.spyOn(component, 'buildEditCraneRequestPayload').mockReturnValue({ id: 1 });
      jest.spyOn(component, 'editCraneRequest');

      component.updateCraneDelivery(paramsWithUndefined);

      expect(paramsWithUndefined.companies).toEqual([]);
      expect(paramsWithUndefined.responsbilePersonsData).toEqual([]);
      expect(paramsWithUndefined.definableFeatureOfWorkData).toEqual([]);
      expect(paramsWithUndefined.equipments).toEqual([]);
    });
  });

  // Additional method tests
  describe('requestAutoEditcompleteItems', () => {
    it('should call searchNewMember with correct parameters', () => {
      const searchText = 'test search';
      const expectedParams = {
        ProjectId: component.ProjectId,
        search: searchText,
        ParentCompanyId: component.ParentCompanyId
      };

      component.requestAutoEditcompleteItems(searchText);

      expect(mockDeliveryService.searchNewMember).toHaveBeenCalledWith(expectedParams);
    });
  });

  describe('deliveryEndTimeChangeDetection', () => {
    it('should set NDRTimingChanged to true and call onEditSubmitForm', () => {
      component.NDRTimingChanged = false;

      component.deliveryEndTimeChangeDetection();

      expect(component.NDRTimingChanged).toBe(true);
      expect(component.onEditSubmitForm).toHaveBeenCalled();
    });
  });

  // Complex Form Submission Tests
  describe('onEditSubmit comprehensive tests', () => {
    beforeEach(() => {
      component.currentEditItem = {
        status: 'Draft',
        deliveryDate: '2024-03-20',
        craneDeliveryStart: new Date('2024-03-20T10:00:00'),
        craneDeliveryEnd: new Date('2024-03-20T12:00:00')
      };
      component.authUser = { RoleId: 1 };
      component.memberList = [{ id: 1 }, { id: 2 }];
      jest.spyOn(component, 'convertStart').mockReturnValue(new Date('2024-03-20T10:00:00'));
      jest.spyOn(component, 'checkEditDeliveryFutureDate').mockReturnValue(true);
      jest.spyOn(component, 'getIndexValue').mockReturnValue(0);
      jest.spyOn(component, 'checkRequestStatusAndUserRole').mockImplementation(() => {});
    });

    it('should show error when equipment is not selected', () => {
      component.craneEditRequest.patchValue({
        EquipmentId: []
      });

      component.onEditSubmit();

      expect(mockToastrService.error).toHaveBeenCalledWith('Equipment is required');
      expect(component.formEditSubmitted).toBe(false);
    });

    it('should return early when form is invalid', () => {
      component.craneEditRequest.patchValue({
        EquipmentId: [{ id: 1 }],
        description: '' // Invalid - required field
      });
      Object.defineProperty(component.craneEditRequest, 'invalid', { get: () => true });

      component.onEditSubmit();

      expect(component.formEditSubmitted).toBe(false);
      expect(component.checkRequestStatusAndUserRole).not.toHaveBeenCalled();
    });

    it('should handle future date validation failure with member validation error', () => {
      component.craneEditRequest.patchValue({
        EquipmentId: [{ id: 1 }],
        description: 'Valid description',
        deliveryDate: '2024-03-20',
        craneDeliveryStart: new Date('2024-03-20T10:00:00'),
        craneDeliveryEnd: new Date('2024-03-20T12:00:00')
      });
      Object.defineProperty(component.craneEditRequest, 'invalid', { get: () => false });
      jest.spyOn(component, 'getIndexValue').mockReturnValue(-1);

      component.onEditSubmit();

      expect(component.errmemberenable).toBe(true);
      expect(component.formEditSubmitted).toBe(false);
    });

    it('should proceed with submission when all validations pass', () => {
      component.craneEditRequest.patchValue({
        EquipmentId: [{ id: 1 }],
        description: 'Valid description',
        deliveryDate: '2024-03-20',
        craneDeliveryStart: new Date('2024-03-20T10:00:00'),
        craneDeliveryEnd: new Date('2024-03-20T12:00:00')
      });
      Object.defineProperty(component.craneEditRequest, 'invalid', { get: () => false });

      component.onEditSubmit();

      expect(component.checkRequestStatusAndUserRole).toHaveBeenCalled();
    });

    it('should handle past date scenario', () => {
      component.craneEditRequest.patchValue({
        EquipmentId: [{ id: 1 }],
        description: 'Valid description',
        deliveryDate: '2024-03-20',
        craneDeliveryStart: new Date('2024-03-20T10:00:00'),
        craneDeliveryEnd: new Date('2024-03-20T12:00:00')
      });
      Object.defineProperty(component.craneEditRequest, 'invalid', { get: () => false });
      jest.spyOn(component, 'checkEditDeliveryFutureDate').mockReturnValue(false);

      component.onEditSubmit();

      expect(component.checkRequestStatusAndUserRole).toHaveBeenCalled();
    });
  });

  // Status and User Role Checking Tests
  describe('checkRequestStatusAndUserRole', () => {
    let mockParams;

    beforeEach(() => {
      mockParams = {
        formValue: {
          deliveryDate: '2024-03-20',
          craneDeliveryStart: new Date('2024-03-20T10:00:00'),
          craneDeliveryEnd: new Date('2024-03-20T12:00:00')
        },
        deliveryStart: new Date('2024-03-20T10:00:00'),
        deliveryEnd: new Date('2024-03-20T12:00:00'),
        companies: [],
        persons: [],
        define: [],
        equipments: [],
        gates: []
      };
      jest.spyOn(component, 'updateCraneDelivery').mockImplementation(() => {});
      jest.spyOn(component, 'checkEditDeliveryFutureDate').mockReturnValue(true);
    });

    it('should prevent date/time changes for completed requests by non-admin users', () => {
      component.currentEditItem = {
        status: 'Completed',
        craneDeliveryStart: new Date('2024-03-19T10:00:00'), // Different date
        craneDeliveryEnd: new Date('2024-03-19T12:00:00')
      };
      component.authUser = { RoleId: 1 }; // Non-admin
      component.craneEditRequest.patchValue({
        deliveryDate: '2024-03-20', // Different from current
        craneDeliveryStart: new Date('2024-03-20T10:00:00'),
        craneDeliveryEnd: new Date('2024-03-20T12:00:00')
      });

      component.checkRequestStatusAndUserRole(mockParams);

      expect(mockToastrService.error).toHaveBeenCalledWith('You are not allowed to change the date/time ');
      expect(component.formEditSubmitted).toBe(false);
    });

    it('should allow changes for completed requests by admin users', () => {
      component.currentEditItem = {
        status: 'Completed',
        craneDeliveryStart: new Date('2024-03-19T10:00:00'),
        craneDeliveryEnd: new Date('2024-03-19T12:00:00')
      };
      component.authUser = { RoleId: 2 }; // Admin
      component.craneEditRequest.patchValue({
        deliveryDate: '2024-03-20',
        craneDeliveryStart: new Date('2024-03-20T10:00:00'),
        craneDeliveryEnd: new Date('2024-03-20T12:00:00')
      });

      component.checkRequestStatusAndUserRole(mockParams);

      expect(component.updateCraneDelivery).toHaveBeenCalled();
    });

    it('should handle approved requests with past date validation', () => {
      component.currentEditItem = {
        status: 'Approved',
        deliveryDate: '2024-03-19',
        craneDeliveryStart: new Date('2024-03-19T10:00:00'),
        craneDeliveryEnd: new Date('2024-03-19T12:00:00')
      };
      component.authUser = { RoleId: 1 }; // Non-admin
      component.craneEditRequest.patchValue({
        deliveryDate: '2024-03-20', // Different date
        craneDeliveryStart: new Date('2024-03-20T10:00:00'),
        craneDeliveryEnd: new Date('2024-03-20T12:00:00')
      });
      jest.spyOn(component, 'checkEditDeliveryFutureDate').mockReturnValue(false);
      jest.spyOn(component, 'throwError').mockImplementation(() => {});

      component.checkRequestStatusAndUserRole(mockParams);

      expect(component.throwError).toHaveBeenCalledWith('error');
      expect(component.formEditSubmitted).toBe(false);
    });

    it('should proceed with approved requests when future date validation passes', () => {
      component.currentEditItem = {
        status: 'Approved',
        deliveryDate: '2024-03-19',
        craneDeliveryStart: new Date('2024-03-19T10:00:00'),
        craneDeliveryEnd: new Date('2024-03-19T12:00:00')
      };
      component.authUser = { RoleId: 1 };
      component.craneEditRequest.patchValue({
        deliveryDate: '2024-03-20',
        craneDeliveryStart: new Date('2024-03-20T10:00:00'),
        craneDeliveryEnd: new Date('2024-03-20T12:00:00')
      });

      component.checkRequestStatusAndUserRole(mockParams);

      expect(component.updateCraneDelivery).toHaveBeenCalled();
    });
  });

  // Additional validation tests
  describe('buildEditCraneRequestPayload edge cases', () => {
    it('should handle null escort values correctly', () => {
      const mockFormValue = {
        id: '1',
        description: 'Test',
        isEscortNeeded: null,
        additionalNotes: 'Notes',
        EquipmentId: ['1'],
        LocationId: '1',
        craneDeliveryStart: new Date(),
        craneDeliveryEnd: new Date(),
        pickUpLocation: 'Location 1',
        dropOffLocation: 'Location 2',
        GateId: '1'
      };

      const payload = component.buildEditCraneRequestPayload(
        mockFormValue,
        new Date(),
        new Date(),
        [],
        [],
        [],
        ['1']
      );

      expect(payload.id).toBe('1');
      expect(payload.description).toBe('Test');
    });

    it('should handle undefined escort values correctly', () => {
      const mockFormValue = {
        id: '1',
        description: 'Test',
        isEscortNeeded: undefined,
        additionalNotes: 'Notes',
        EquipmentId: ['1'],
        LocationId: '1',
        craneDeliveryStart: new Date(),
        craneDeliveryEnd: new Date(),
        pickUpLocation: 'Location 1',
        dropOffLocation: 'Location 2',
        GateId: '1'
      };

      const payload = component.buildEditCraneRequestPayload(
        mockFormValue,
        new Date(),
        new Date(),
        [],
        [],
        [],
        ['1']
      );

      expect(payload.id).toBe('1');
      expect(payload.description).toBe('Test');
    });
  });

  // Component state tests
  describe('Component state management', () => {
    it('should initialize with correct default values', () => {
      // Initialize component properties that are set in beforeEach
      expect(component.formEditSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(component.NDRTimingChanged).toBe(false);
      expect(component.formEdited).toBe(false);
    });

    it('should handle array properties correctly', () => {
      expect(Array.isArray(component.gateList)).toBe(true);
      expect(Array.isArray(component.equipmentList)).toBe(true);
      expect(Array.isArray(component.definableFeatureOfWorkList)).toBe(true);
    });
  });

  // Error scenarios
  describe('Error handling scenarios', () => {
    it('should handle editCraneRequestSuccess with proper cleanup', () => {
      const mockResponse = { message: 'Success message' };

      component.NDRTimingChanged = true;
      component.formEditSubmitted = true;

      component.editCraneRequestSuccess(mockResponse);

      expect(mockToastrService.success).toHaveBeenCalledWith('Success message', 'Success');
      expect(mockSocket.emit).toHaveBeenCalledWith('CraneEditHistory', mockResponse);
      expect(mockDeliveryService.updateCraneRequestHistory).toHaveBeenCalledWith({ status: true }, 'CraneEditHistory');
      expect(component.resetForm).toHaveBeenCalledWith('yes');
      expect(component.NDRTimingChanged).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
    });
  });

  // Array manipulation tests
  describe('Array manipulation in updateCraneDelivery', () => {
    it('should handle empty arrays in form values', () => {
      const mockParams = {
        editCraneRequestFormValue: {
          companies: [],
          responsiblePersons: [],
          definableFeatureOfWorks: [],
          EquipmentId: [],
          description: 'Test description'
        },
        deliveryStart: new Date('2024-03-20T10:00:00'),
        deliveryEnd: new Date('2024-03-20T12:00:00'),
        companies: [],
        responsbilePersonsData: [],
        definableFeatureOfWorkData: [],
        equipments: [],
        gates: []
      };

      jest.spyOn(component, 'checkStartEnd').mockReturnValue(true);
      jest.spyOn(component, 'validateEditCraneRequestInputs').mockReturnValue(true);
      jest.spyOn(component, 'buildEditCraneRequestPayload').mockReturnValue({ id: 1 });
      jest.spyOn(component, 'editCraneRequest');

      component.updateCraneDelivery(mockParams);

      expect(mockParams.companies).toEqual([]);
      expect(mockParams.responsbilePersonsData).toEqual([]);
      expect(mockParams.definableFeatureOfWorkData).toEqual([]);
      expect(mockParams.equipments).toEqual([]);
    });
  });

  // Error Handling Tests
  describe('Error Handling', () => {
    describe('showError', () => {
      it('should display error message from error details', () => {
        const mockError = {
          message: {
            details: [{ errorField: 'Test error message' }]
          }
        };

        component.showError(mockError);

        expect(component.submitted).toBe(false);
        expect(component.formSubmitted).toBe(false);
        expect(mockToastrService.error).toHaveBeenCalledWith('Test error message');
      });

      it('should handle error with multiple detail values', () => {
        const mockError = {
          message: {
            details: [{ field1: 'Error 1', field2: 'Error 2' }]
          }
        };

        component.showError(mockError);

        expect(mockToastrService.error).toHaveBeenCalled();
      });
    });

    describe('throwError', () => {
      beforeEach(() => {
        component.editSubmitted = true;
        component.formEditSubmitted = true;
      });

      it('should handle time error', () => {
        component.throwError('time error');

        expect(component.editSubmitted).toBe(false);
        expect(component.formEditSubmitted).toBe(false);
        expect(mockToastrService.error).toHaveBeenCalledWith('Please Enter Start time Lesser than End time');
      });

      it('should handle general error', () => {
        component.throwError('general error');

        expect(component.editSubmitted).toBe(false);
        expect(component.formEditSubmitted).toBe(false);
        expect(mockToastrService.error).toHaveBeenCalledWith(
          'Booking not allowed to edit. Please contact the project administrator to edit this booking'
        );
      });
    });

    describe('checkEditDeliveryFutureDate', () => {
      beforeEach(() => {
        component.deliveryWindowTime = 2;
        component.deliveryWindowTimeUnit = 'hours';
      });

      it('should return true for future dates', () => {
        const futureStart = new Date();
        futureStart.setHours(futureStart.getHours() + 5);
        const futureEnd = new Date();
        futureEnd.setHours(futureEnd.getHours() + 6);

        const result = component.checkEditDeliveryFutureDate(futureStart, futureEnd);

        expect(result).toBe(true);
      });

      it('should return false for past dates', () => {
        const pastStart = new Date();
        pastStart.setHours(pastStart.getHours() - 5);
        const pastEnd = new Date();
        pastEnd.setHours(pastEnd.getHours() - 4);

        const result = component.checkEditDeliveryFutureDate(pastStart, pastEnd);

        expect(result).toBe(false);
      });

      it('should handle string date inputs', () => {
        const futureStart = new Date();
        futureStart.setHours(futureStart.getHours() + 5);
        const futureEnd = new Date();
        futureEnd.setHours(futureEnd.getHours() + 6);

        const result = component.checkEditDeliveryFutureDate(
          futureStart.toISOString(),
          futureEnd.toISOString()
        );

        expect(result).toBe(true);
      });
    });
  });

  // Data Setting Methods Tests
  describe('Data Setting Methods', () => {
    describe('setEquipment', () => {
      it('should set equipment from current edit item', () => {
        component.currentEditItem = {
          equipmentDetails: [
            { Equipment: { id: 1, equipmentName: 'Crane 1' } },
            { Equipment: { id: 2, equipmentName: 'Crane 2' } }
          ]
        };

        component.setEquipment();

        expect(component.editBeforeEquipment).toHaveLength(2);
        expect(component.editBeforeEquipment[0]).toEqual({
          id: 1,
          equipmentName: 'Crane 1'
        });
        expect(component.craneEditRequest.get('EquipmentId').value).toHaveLength(2);
      });

      it('should handle undefined equipment details', () => {
        component.currentEditItem = {};

        component.setEquipment();

        expect(component.editBeforeEquipment).toEqual([]);
        expect(component.craneEditRequest.get('EquipmentId').value).toEqual([]);
      });
    });

    describe('setlocation', () => {
      it('should set location from current edit item', () => {
        component.currentEditItem = {
          location: {
            id: 1,
            locationPath: 'Test Location',
            TimeZoneId: [{ location: 'America/New_York' }]
          }
        };

        component.setlocation();

        expect(component.getChosenLocation).toHaveLength(1);
        expect(component.getChosenLocation[0].id).toBe(1);
        expect(component.selectedLocationId).toBe(1);
        expect(component.timeZone).toBe('America/New_York');
        expect(component.craneEditRequest.get('LocationId').value).toEqual(component.getChosenLocation);
      });

      it('should handle missing location', () => {
        component.currentEditItem = {};

        expect(() => component.setlocation()).not.toThrow();
      });
    });

    describe('setCompany', () => {
      it('should set companies from current edit item', () => {
        component.currentEditItem = {
          companyDetails: [
            { Company: { id: 1, companyName: 'Company 1' } },
            { Company: { id: 2, companyName: 'Company 2' } }
          ]
        };

        component.setCompany();

        expect(component.editBeforeCompany).toHaveLength(2);
        expect(component.editBeforeCompany[0]).toEqual({
          id: 1,
          companyName: 'Company 1'
        });
        expect(component.craneEditRequest.get('companies').value).toHaveLength(2);
      });

      it('should handle undefined company details', () => {
        component.currentEditItem = {};

        component.setCompany();

        expect(component.editBeforeCompany).toEqual([]);
        expect(component.craneEditRequest.get('companies').value).toEqual([]);
      });
    });

    describe('setDefine', () => {
      it('should set definable features from current edit item', () => {
        component.currentEditItem = {
          defineWorkDetails: [
            { DeliverDefineWork: { id: 1, DFOW: 'Feature 1' } },
            { DeliverDefineWork: { id: 2, DFOW: 'Feature 2' } }
          ]
        };

        component.setDefine();

        expect(component.editBeforeDFOW).toHaveLength(2);
        expect(component.editBeforeDFOW[0]).toEqual({
          id: 1,
          DFOW: 'Feature 1'
        });
        expect(component.craneEditRequest.get('definableFeatureOfWorks').value).toHaveLength(2);
      });

      it('should handle undefined define work details', () => {
        component.currentEditItem = {};

        component.setDefine();

        expect(component.editBeforeDFOW).toEqual([]);
        expect(component.craneEditRequest.get('definableFeatureOfWorks').value).toEqual([]);
      });
    });
  });

  // Additional lifecycle and initialization tests
  describe('Component initialization and lifecycle', () => {
    it('should initialize dropdown settings', () => {
      // Test that dropdown settings are properly initialized
      expect(component.editNdrCompanyDropdownSettings).toBeDefined();
    });

    it('should handle ngOnInit lifecycle', () => {
      // Mock the necessary methods that are called in ngOnInit
      jest.spyOn(component, 'editCraneRequestForm').mockImplementation(() => {});

      component.ngOnInit();

      // Verify that initialization methods are called
      expect(component.editCraneRequestForm).toHaveBeenCalled();
    });

    it('should handle ngAfterViewInit lifecycle', () => {
      // Mock the necessary methods
      jest.spyOn(component, 'editCraneRequestForm').mockImplementation(() => {});

      component.ngAfterViewInit();

      // Verify that view initialization is handled
      expect(component.editCraneRequestForm).toHaveBeenCalled();
    });
  });

  // Modal and UI interaction tests
  describe('Modal and UI interactions', () => {
    describe('close', () => {
      beforeEach(() => {
        jest.spyOn(component, 'areDifferentByProperty').mockReturnValue(false);
        jest.spyOn(component, 'openConfirmationModalPopupForEditNDR').mockImplementation(() => {});
        jest.spyOn(component, 'resetForm').mockImplementation(() => {});
      });

      it('should call resetForm when no changes detected', () => {
        component.craneEditRequest.markAsUntouched();
        component.NDRTimingChanged = false;

        component.close(null);

        expect(component.resetForm).toHaveBeenCalledWith('yes');
      });

      it('should open confirmation modal when form is touched', () => {
        component.craneEditRequest.markAsTouched();
        const mockTemplate = {} as any;

        component.close(mockTemplate);

        expect(component.openConfirmationModalPopupForEditNDR).toHaveBeenCalledWith(mockTemplate);
      });

      it('should open confirmation modal when NDRTimingChanged is true', () => {
        component.craneEditRequest.markAsUntouched();
        component.NDRTimingChanged = true;
        const mockTemplate = {} as any;

        component.close(mockTemplate);

        expect(component.openConfirmationModalPopupForEditNDR).toHaveBeenCalledWith(mockTemplate);
      });

      it('should open confirmation modal when definable features changed', () => {
        component.craneEditRequest.markAsUntouched();
        component.NDRTimingChanged = false;
        component.craneEditRequest.get('definableFeatureOfWorks').markAsDirty();
        component.craneEditRequest.get('definableFeatureOfWorks').setValue([{ DFOW: 'Test' }]);
        jest.spyOn(component, 'areDifferentByProperty').mockReturnValue(true);
        const mockTemplate = {} as any;

        component.close(mockTemplate);

        expect(component.openConfirmationModalPopupForEditNDR).toHaveBeenCalledWith(mockTemplate);
      });

      it('should open confirmation modal when companies changed', () => {
        component.craneEditRequest.markAsUntouched();
        component.NDRTimingChanged = false;
        component.craneEditRequest.get('companies').markAsDirty();
        component.craneEditRequest.get('companies').setValue([{ companyName: 'Test Company' }]);
        jest.spyOn(component, 'areDifferentByProperty').mockReturnValue(true);
        const mockTemplate = {} as any;

        component.close(mockTemplate);

        expect(component.openConfirmationModalPopupForEditNDR).toHaveBeenCalledWith(mockTemplate);
      });

      it('should open confirmation modal when equipment changed', () => {
        component.craneEditRequest.markAsUntouched();
        component.NDRTimingChanged = false;
        component.craneEditRequest.get('EquipmentId').markAsDirty();
        component.craneEditRequest.get('EquipmentId').setValue([{ equipmentName: 'Test Equipment' }]);
        jest.spyOn(component, 'areDifferentByProperty').mockReturnValue(true);
        const mockTemplate = {} as any;

        component.close(mockTemplate);

        expect(component.openConfirmationModalPopupForEditNDR).toHaveBeenCalledWith(mockTemplate);
      });
    });

    describe('openConfirmationModalPopupForEditNDR', () => {
      it('should open modal with correct configuration', () => {
        const mockTemplate = {} as any;
        const expectedConfig = {
          keyboard: false,
          class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
        };

        component.openConfirmationModalPopupForEditNDR(mockTemplate);

        expect(mockBsModalService.show).toHaveBeenCalledWith(mockTemplate, expectedConfig);
      });
    });

    it('should handle modal loader state', () => {
      component.modalLoader = true;
      expect(component.modalLoader).toBe(true);

      component.modalLoader = false;
      expect(component.modalLoader).toBe(false);
    });
  });

  // Form Control Value Changes Tests
  describe('formControlValueChanged', () => {
    it('should subscribe to form value changes', () => {
      jest.spyOn(component.craneEditRequest.valueChanges, 'subscribe');

      component.formControlValueChanged();

      expect(component.craneEditRequest.valueChanges.subscribe).toHaveBeenCalled();
    });

    it('should call onEditSubmitForm when form values change', () => {
      jest.spyOn(component, 'onEditSubmitForm').mockImplementation(() => {});

      component.formControlValueChanged();

      // Trigger form value change
      component.craneEditRequest.get('description').setValue('Test Description');

      expect(component.onEditSubmitForm).toHaveBeenCalled();
    });
  });

  // Form building and validation tests
  describe('Form building and validation', () => {
    it('should build form with proper validators', () => {
      component.editCraneRequestForm();

      expect(component.craneEditRequest).toBeDefined();
      expect(component.craneEditRequest.get('description')).toBeTruthy();
      expect(component.craneEditRequest.get('LocationId')).toBeTruthy();
      expect(component.craneEditRequest.get('GateId')).toBeTruthy();
    });

    it('should handle form value changes', () => {
      const testValue = 'Test Description';
      component.craneEditRequest.get('description').setValue(testValue);

      expect(component.craneEditRequest.get('description').value).toBe(testValue);
    });

    it('should validate required fields', () => {
      component.craneEditRequest.get('description').setValue('');
      component.craneEditRequest.get('LocationId').setValue('');
      component.craneEditRequest.get('GateId').setValue('');

      expect(component.craneEditRequest.get('description').invalid).toBe(true);
      expect(component.craneEditRequest.get('LocationId').invalid).toBe(true);
      expect(component.craneEditRequest.get('GateId').invalid).toBe(true);
    });
  });

  // Data processing and transformation tests
  describe('Data processing and transformation', () => {
    it('should handle equipment data processing', () => {
      const mockEquipmentData = [
        { id: 1, name: 'Crane 1' },
        { id: 2, name: 'Crane 2' }
      ];

      component.equipmentList = mockEquipmentData;

      expect(component.equipmentList.length).toBe(2);
      expect(component.equipmentList[0].id).toBe(1);
    });

    it('should handle gate data processing', () => {
      const mockGateData = [
        { id: 1, name: 'Gate A' },
        { id: 2, name: 'Gate B' }
      ];

      component.gateList = mockGateData;

      expect(component.gateList.length).toBe(2);
      expect(component.gateList[0].name).toBe('Gate A');
    });

    it('should handle definable feature of work data', () => {
      const mockDfowData = [
        { id: 1, name: 'Feature 1' },
        { id: 2, name: 'Feature 2' }
      ];

      component.definableFeatureOfWorkList = mockDfowData;

      expect(component.definableFeatureOfWorkList.length).toBe(2);
      expect(component.definableFeatureOfWorkList[1].name).toBe('Feature 2');
    });
  });

  // Error handling and edge cases
  describe('Additional error handling and edge cases', () => {
    it('should handle null currentEditItem', () => {
      component.currentEditItem = null;

      // Should not throw error when currentEditItem is null
      expect(() => {
        component.onEditSubmit();
      }).not.toThrow();
    });

    it('should handle undefined authUser', () => {
      component.authUser = undefined;

      // Should handle undefined authUser gracefully
      expect(component.authUser).toBeUndefined();
    });

    it('should handle empty form arrays', () => {
      component.craneEditRequest.patchValue({
        companies: [],
        responsiblePersons: [],
        definableFeatureOfWorks: [],
        EquipmentId: []
      });

      const formValue = component.craneEditRequest.value;
      expect(Array.isArray(formValue.companies)).toBe(true);
      expect(formValue.companies.length).toBe(0);
    });

    it('should handle form reset scenarios', () => {
      component.formEditSubmitted = true;
      component.editSubmitted = true;
      component.NDRTimingChanged = true;

      component.formReset();

      expect(component.formEditSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      // NDRTimingChanged should not be reset by formReset
      expect(component.NDRTimingChanged).toBe(true);
    });
  });

  // Validation Methods Tests
  describe('Validation Methods', () => {
    describe('gatecheck', () => {
      beforeEach(() => {
        component.memberList = [
          { id: 1, name: 'Member 1' },
          { id: 2, name: 'Member 2' },
          { id: 3, name: 'Member 3' }
        ];
      });

      it('should set errmemberenable to true when Person validation fails', () => {
        const value = [{ id: 1 }, { id: 4 }]; // id 4 doesn't exist in memberList

        component.gatecheck(value, 'Person');

        expect(component.errmemberenable).toBe(true);
      });

      it('should set errmemberenable to false when Person validation passes', () => {
        const value = [{ id: 1 }, { id: 2 }]; // Both exist in memberList

        component.gatecheck(value, 'Person');

        expect(component.errmemberenable).toBe(false);
      });

      it('should not affect errmemberenable for non-Person menu', () => {
        component.errmemberenable = false;
        const value = [{ id: 1 }, { id: 4 }];

        component.gatecheck(value, 'Equipment');

        expect(component.errmemberenable).toBe(false);
      });
    });

    describe('getIndexValue', () => {
      beforeEach(() => {
        component.memberList = [
          { id: 1, name: 'Member 1' },
          { id: 2, name: 'Member 2' },
          { id: 3, name: 'Member 3' }
        ];
      });

      it('should return 0 when all responsible persons exist in member list', () => {
        const formValue = {
          responsiblePersons: [{ id: 1 }, { id: 2 }]
        };

        const result = component.getIndexValue(formValue);

        expect(result).toBe(0);
      });

      it('should return -1 when some responsible persons do not exist in member list', () => {
        const formValue = {
          responsiblePersons: [{ id: 1 }, { id: 4 }] // id 4 doesn't exist
        };

        const result = component.getIndexValue(formValue);

        expect(result).toBe(-1);
      });

      it('should return 0 for empty responsible persons array', () => {
        const formValue = {
          responsiblePersons: []
        };

        const result = component.getIndexValue(formValue);

        expect(result).toBe(0);
      });
    });

    describe('convertStart', () => {
      it('should convert delivery date with hours and minutes', () => {
        const deliveryDate = new Date('2024-03-20T00:00:00');
        const startHours = 10;
        const startMinutes = 30;

        const result = component.convertStart(deliveryDate, startHours, startMinutes);

        expect(result.getFullYear()).toBe(2024);
        expect(result.getMonth()).toBe(2); // March is month 2 (0-indexed)
        expect(result.getDate()).toBe(20);
        expect(result.getHours()).toBe(10);
        expect(result.getMinutes()).toBe(30);
      });

      it('should handle different time zones correctly', () => {
        const deliveryDate = new Date('2024-12-25T00:00:00');
        const startHours = 23;
        const startMinutes = 59;

        const result = component.convertStart(deliveryDate, startHours, startMinutes);

        expect(result.getHours()).toBe(23);
        expect(result.getMinutes()).toBe(59);
      });
    });
  });

  // Member Setting Tests
  describe('setMember', () => {
    beforeEach(() => {
      component.ProjectId = 'test-project';
      component.ParentCompanyId = 'test-company';
      mockDeliveryService.getMemberRole = jest.fn().mockReturnValue(of({
        data: { User: { email: '<EMAIL>' } }
      }));
    });

    it('should set members from current edit item', () => {
      component.currentEditItem = {
        memberDetails: [
          {
            Member: {
              id: 1,
              User: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
              isGuestUser: false
            }
          },
          {
            Member: {
              id: 2,
              User: { firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>' },
              isGuestUser: true
            }
          }
        ]
      };

      component.setMember();

      expect(mockDeliveryService.getMemberRole).toHaveBeenCalledWith({
        ProjectId: 'test-project',
        ParentCompanyId: 'test-company'
      });
    });

    it('should handle member with only email', () => {
      component.currentEditItem = {
        memberDetails: [
          {
            Member: {
              id: 1,
              User: { email: '<EMAIL>' },
              isGuestUser: false
            }
          }
        ]
      };

      component.setMember();

      expect(mockDeliveryService.getMemberRole).toHaveBeenCalled();
    });

    it('should handle undefined member details', () => {
      component.currentEditItem = {};

      component.setMember();

      expect(mockDeliveryService.getMemberRole).toHaveBeenCalled();
    });

    it('should mark current user as readonly', () => {
      component.authUser = { User: { email: '<EMAIL>' } };
      component.currentEditItem = {
        memberDetails: [
          {
            Member: {
              id: 1,
              User: { firstName: 'Current', lastName: 'User', email: '<EMAIL>' },
              isGuestUser: false
            }
          }
        ]
      };

      component.setMember();

      expect(mockDeliveryService.getMemberRole).toHaveBeenCalled();
    });
  });

  // Service integration tests
  describe('Service integration', () => {
    it('should handle delivery service responses', () => {
      const mockResponse = { data: { equipment: [] } };
      mockDeliveryService.getEquipmentCraneRequest = jest.fn().mockReturnValue(of(mockResponse));

      // Test service integration
      expect(mockDeliveryService.getEquipmentCraneRequest).toBeDefined();
    });

    it('should handle project service responses', () => {
      const mockLocationData = { data: [{ id: 1, name: 'Location 1' }] };
      mockProjectService.getLocations = jest.fn().mockReturnValue(of(mockLocationData));

      expect(mockProjectService.getLocations).toBeDefined();
    });

    it('should handle project settings service', () => {
      const mockSettings = { setting1: 'value1' };
      mockProjectSettingsService.getProjectSettings = jest.fn().mockReturnValue(of(mockSettings));

      expect(mockProjectSettingsService.getProjectSettings).toBeDefined();
    });
  });

  // Input validation edge cases
  describe('Input validation edge cases', () => {
    it('should handle special characters in description', () => {
      const specialCharsDescription = 'Test @#$%^&*()_+ description';
      const formValue = {
        description: specialCharsDescription,
        additionalNotes: 'Valid notes'
      };

      const result = component.checkEditDeliveryStringEmptyValues(formValue);

      expect(result).toBe(false);
    });

    it('should handle very long description', () => {
      const longDescription = 'A'.repeat(1000);
      const formValue = {
        description: longDescription,
        additionalNotes: 'Valid notes'
      };

      const result = component.checkEditDeliveryStringEmptyValues(formValue);

      expect(result).toBe(false);
    });

    it('should handle null values in form validation', () => {
      const formValue = {
        description: null,
        additionalNotes: null
      };

      // Should handle null values gracefully
      expect(() => {
        component.checkEditDeliveryStringEmptyValues(formValue);
      }).not.toThrow();
    });
  });

  // Additional comprehensive test coverage for missing methods
  describe('Constructor and Initialization', () => {
    it('should initialize with project service subscriptions', () => {
      expect(component.ProjectId).toBe('test-project-id');
      expect(component.ParentCompanyId).toBe('test-company-id');
    });

    it('should call getMembers and getProjectSettings in constructor', () => {
      jest.spyOn(component, 'getMembers');
      jest.spyOn(component, 'getProjectSettings');

      // Create new component instance to test constructor
      const newComponent = new EditCraneRequestComponent(
        mockFormBuilder,
        mockSocket as any,
        mockToastrService as any,
        mockRouter as any,
        mockBsModalRef as any,
        mockBsModalService as any,
        mockBsModalRef as any,
        mockBsModalRef as any,
        mockMixpanelService as any,
        mockDeliveryService as any,
        mockProjectService as any,
        mockProjectSettingsService as any
      );

      expect(newComponent).toBeDefined();
    });
  });

  // Form Control Value Changes Tests
  describe('formControlValueChanged', () => {
    beforeEach(() => {
      jest.spyOn(component, 'onEditSubmitForm').mockImplementation(() => {});
    });

    it('should call onEditSubmitForm when form values change', () => {
      component.formControlValueChanged();

      // Simulate form value changes
      component.craneEditRequest.get('description').setValue('New Description');

      expect(component.onEditSubmitForm).toHaveBeenCalled();
    });

    it('should handle form control subscription', () => {
      const mockSubscription = {
        unsubscribe: jest.fn()
      };

      component.formControlValueChanged();

      // Verify subscription is set up
      expect(component.craneEditRequest.valueChanges).toBeDefined();
    });
  });

  // Location Selection Tests
  describe('locationSelected', () => {
    beforeEach(() => {
      component.locationList = [
        {
          id: 1,
          locationPath: 'Location 1',
          gateDetails: [{ id: 1, name: 'Gate 1' }],
          EquipmentId: [{ id: 1, equipmentName: 'Equipment 1' }],
          TimeZoneId: [{ location: 'America/New_York' }]
        },
        {
          id: 2,
          locationPath: 'Location 2',
          gateDetails: [{ id: 2, name: 'Gate 2' }],
          EquipmentId: [],
          TimeZoneId: [{ location: 'Europe/London' }]
        }
      ];
    });

    it('should update gate and equipment lists when location is selected', () => {
      const selectedLocation = { id: 1 };

      component.locationSelected(selectedLocation);

      expect(component.getChosenLocation).toHaveLength(1);
      expect(component.getChosenLocation[0].id).toBe(1);
      expect(component.gateList).toEqual([{ id: 1, name: 'Gate 1' }]);
      expect(component.equipmentList).toHaveLength(1);
      expect(component.timeZone).toBe('America/New_York');
      expect(component.selectedLocationId).toBe(1);
    });

    it('should handle location with no equipment', () => {
      const selectedLocation = { id: 2 };

      component.locationSelected(selectedLocation);

      expect(component.equipmentList).toEqual([]);
      expect(component.craneEditRequest.get('EquipmentId').value).toEqual([]);
    });

    it('should handle location with equipment', () => {
      const selectedLocation = { id: 1 };

      component.locationSelected(selectedLocation);

      expect(component.editBeforeEquipment).toHaveLength(1);
      expect(component.editBeforeEquipment[0]).toEqual({
        id: 1,
        equipmentName: 'Equipment 1'
      });
    });
  });

  // Equipment Selection Tests
  describe('equipmentSelected', () => {
    it('should handle equipment selection', () => {
      const mockValue = { id: 1, equipmentName: 'Test Equipment' };

      // This method is currently empty but should not throw
      expect(() => component.equipmentSelected(mockValue)).not.toThrow();
    });
  });

  // Date and Time Management Tests
  describe('changeDate', () => {
    beforeEach(() => {
      component.modalLoader = false;
      jest.spyOn(component, 'onEditSubmitForm').mockImplementation(() => {});
    });

    it('should update delivery end time when date changes', () => {
      const testDate = new Date('2024-03-20T10:30:00');

      component.changeDate(testDate);

      const expectedEndTime = new Date();
      expectedEndTime.setHours(11); // startTime + 1 hour
      expectedEndTime.setMinutes(30);

      expect(component.deliveryEnd.getHours()).toBe(11);
      expect(component.deliveryEnd.getMinutes()).toBe(30);
      expect(component.NDRTimingChanged).toBe(true);
      expect(component.onEditSubmitForm).toHaveBeenCalled();
    });

    it('should not update when modal is loading', () => {
      component.modalLoader = true;
      component.NDRTimingChanged = false;

      const testDate = new Date('2024-03-20T10:30:00');
      component.changeDate(testDate);

      // Should still call onEditSubmitForm but not change NDRTimingChanged
      expect(component.onEditSubmitForm).toHaveBeenCalled();
    });
  });

  // Duration Selection Tests
  describe('Duration Selection Methods', () => {
    beforeEach(() => {
      component.craneEditRequest.patchValue({
        craneDeliveryStart: new Date('2024-03-20T10:00:00')
      });
      component.selectedHour = 1;
      component.selectedMinute = 30;
      jest.spyOn(component, 'durationCloseDropdown').mockImplementation(() => {});
      jest.spyOn(component, 'updateDropdownState').mockImplementation(() => {});
      jest.spyOn(component, 'selectDuration').mockImplementation(() => {});
    });

    describe('selectHour', () => {
      it('should update delivery end time based on selected hour', () => {
        component.selectHour(2);

        expect(component.selectedHour).toBe(2);
        expect(component.deliveryEnd).toBeDefined();
        expect(component.durationCloseDropdown).toHaveBeenCalled();
        expect(component.updateDropdownState).toHaveBeenCalled();
        expect(component.selectDuration).toHaveBeenCalledWith(2);
      });

      it('should calculate total minutes correctly', () => {
        component.selectedMinute = 45;
        component.selectHour(3);

        const totalMinutes = (3 * 60) + 45; // 225 minutes
        expect(component.selectedHour).toBe(3);
      });
    });

    describe('selectMinute', () => {
      it('should update delivery end time based on selected minute', () => {
        component.selectMinute(45);

        expect(component.selectedMinute).toBe(45);
        expect(component.deliveryEnd).toBeDefined();
        expect(component.durationCloseDropdown).toHaveBeenCalled();
        expect(component.updateDropdownState).toHaveBeenCalled();
        expect(component.selectDuration).toHaveBeenCalledWith(45);
      });
    });

    describe('selectDuration', () => {
      it('should calculate selected minutes from hour and minute', () => {
        component.selectedHour = 2;
        component.selectedMinute = 30;

        component.selectDuration(null);

        expect(component.selectedMinutes).toBe(150); // (2 * 60) + 30
      });
    });

    describe('durationToggleDropdown', () => {
      it('should toggle dropdown state', () => {
        component.durationisOpen = false;
        component.durationToggleDropdown();
        expect(component.durationisOpen).toBe(true);

        component.durationToggleDropdown();
        expect(component.durationisOpen).toBe(false);
      });
    });

    describe('updateDropdownState', () => {
      it('should close dropdown when both hour and minute are selected', () => {
        component.selectedHour = 1;
        component.selectedMinutes = 30;
        component.durationisOpen = true;

        component.updateDropdownState();

        expect(component.durationisOpen).toBe(false);
      });

      it('should keep dropdown open when selections are incomplete', () => {
        component.selectedHour = null;
        component.selectedMinutes = 30;
        component.durationisOpen = true;

        component.updateDropdownState();

        expect(component.durationisOpen).toBe(true);
      });
    });
  });

  describe('getProjectIdAndParentCompanyId', () => {
    it('should set ProjectId and ParentCompanyId when response is valid', () => {
      const response = {
        ProjectId: 'new-project-id',
        ParentCompanyId: 'new-company-id'
      };

      component.getProjectIdAndParentCompanyId(response);

      expect(component.ProjectId).toBe('new-project-id');
      expect(component.ParentCompanyId).toBe('new-company-id');
      expect(component.loader).toBe(true);
    });

    it('should not set values when response is undefined', () => {
      const originalProjectId = component.ProjectId;
      const originalCompanyId = component.ParentCompanyId;

      component.getProjectIdAndParentCompanyId(undefined);

      expect(component.ProjectId).toBe(originalProjectId);
      expect(component.ParentCompanyId).toBe(originalCompanyId);
    });

    it('should not set values when response is null', () => {
      const originalProjectId = component.ProjectId;
      const originalCompanyId = component.ParentCompanyId;

      component.getProjectIdAndParentCompanyId(null);

      expect(component.ProjectId).toBe(originalProjectId);
      expect(component.ParentCompanyId).toBe(originalCompanyId);
    });

    it('should not set values when response is empty string', () => {
      const originalProjectId = component.ProjectId;
      const originalCompanyId = component.ParentCompanyId;

      component.getProjectIdAndParentCompanyId('');

      expect(component.ProjectId).toBe(originalProjectId);
      expect(component.ParentCompanyId).toBe(originalCompanyId);
    });
  });

  describe('getProjectSettings', () => {
    it('should call projectSettingsService when ProjectId exists', () => {
      component.ProjectId = 'test-project-id';
      const mockResponse = {
        data: {
          deliveryWindowTime: 2,
          deliveryWindowTimeUnit: 'hours'
        }
      };
      mockProjectSettingsService.getProjectSettings = jest.fn().mockReturnValue(of(mockResponse));

      component.getProjectSettings();

      expect(mockProjectSettingsService.getProjectSettings).toHaveBeenCalledWith({
        ProjectId: 'test-project-id'
      });
      expect(component.deliveryWindowTime).toBe(2);
      expect(component.deliveryWindowTimeUnit).toBe('hours');
    });

    it('should not call service when ProjectId is not set', () => {
      component.ProjectId = null;
      mockProjectSettingsService.getProjectSettings = jest.fn();

      component.getProjectSettings();

      expect(mockProjectSettingsService.getProjectSettings).not.toHaveBeenCalled();
    });
  });

  describe('getBookingData', () => {
    beforeEach(() => {
      component.craneEditRequest.patchValue({
        EquipmentId: [{ id: 1 }],
        LocationId: [{ id: 2 }],
        GateId: 3
      });
      component.timeZone = 'America/New_York';
    });

    it('should call timeSlotComponent.getEventNDR when timeSlotComponent exists', () => {
      const mockTimeSlotComponent = {
        getEventNDR: jest.fn()
      };
      component.timeSlotComponent = mockTimeSlotComponent as any;

      component.getBookingData();

      expect(mockTimeSlotComponent.getEventNDR).toHaveBeenCalledWith(
        [{ id: 1 }],
        2,
        3,
        'America/New_York',
        ''
      );
    });

    it('should not call getEventNDR when timeSlotComponent is null', () => {
      component.timeSlotComponent = null;

      expect(() => component.getBookingData()).not.toThrow();
    });
  });

  describe('getOverAllGate', () => {
    it('should set modalLoader and call projectService.gateList', () => {
      const mockGateResponse = { data: [{ id: 1, name: 'Gate 1' }] };
      mockProjectService.gateList = jest.fn().mockReturnValue(of(mockGateResponse));
      jest.spyOn(component, 'getOverAllEquipmentforEditNdr').mockImplementation(() => {});

      component.getOverAllGate();

      expect(component.modalLoader).toBe(true);
      expect(mockProjectService.gateList).toHaveBeenCalledWith(
        {
          ProjectId: component.ProjectId,
          pageSize: 0,
          pageNo: 0,
          ParentCompanyId: component.ParentCompanyId
        },
        { isFilter: true, showActivatedAlone: true }
      );
      expect(component.gateList).toEqual(mockGateResponse.data);
      expect(component.getOverAllEquipmentforEditNdr).toHaveBeenCalled();
    });
  });

  describe('getOverAllEquipmentforEditNdr', () => {
    it('should call projectService.listCraneEquipment and set equipment settings', () => {
      const mockEquipmentResponse = {
        data: {
          rows: [
            { id: 1, equipmentName: 'Crane 1' },
            { id: 2, equipmentName: 'Crane 2' }
          ]
        }
      };
      mockProjectService.listCraneEquipment = jest.fn().mockReturnValue(of(mockEquipmentResponse));
      jest.spyOn(component, 'getCompaniesForEditNdr').mockImplementation(() => {});

      component.getOverAllEquipmentforEditNdr();

      expect(mockProjectService.listCraneEquipment).toHaveBeenCalledWith(
        {
          ProjectId: component.ProjectId,
          pageSize: 0,
          pageNo: 0,
          ParentCompanyId: component.ParentCompanyId
        },
        { showActivatedAlone: true }
      );
      expect(component.equipmentList).toEqual(mockEquipmentResponse.data.rows);
      expect(component.equipmentDropdownSettings).toBeDefined();
      expect(component.equipmentDropdownSettings.singleSelection).toBe(false);
      expect(component.equipmentDropdownSettings.idField).toBe('id');
      expect(component.equipmentDropdownSettings.textField).toBe('equipmentName');
      expect(component.getCompaniesForEditNdr).toHaveBeenCalled();
    });
  });

  describe('getCompaniesForEditNdr', () => {
    it('should call projectService.getCompanies and set company settings', () => {
      const mockCompanyResponse = {
        data: [
          { id: 1, companyName: 'Company 1' },
          { id: 2, companyName: 'Company 2' }
        ]
      };
      mockProjectService.getCompanies = jest.fn().mockReturnValue(of(mockCompanyResponse));
      jest.spyOn(component, 'getDefinableForEditNdr').mockImplementation(() => {});

      component.getCompaniesForEditNdr();

      expect(mockProjectService.getCompanies).toHaveBeenCalledWith({
        ProjectId: component.ProjectId,
        ParentCompanyId: component.ParentCompanyId
      });
      expect(component.companyList).toEqual(mockCompanyResponse.data);
      expect(component.editNdrCompanyDropdownSettings).toBeDefined();
      expect(component.editNdrCompanyDropdownSettings.singleSelection).toBe(false);
      expect(component.editNdrCompanyDropdownSettings.idField).toBe('id');
      expect(component.editNdrCompanyDropdownSettings.textField).toBe('companyName');
      expect(component.getDefinableForEditNdr).toHaveBeenCalled();
    });

    it('should handle empty company response', () => {
      const mockCompanyResponse = { data: null };
      mockProjectService.getCompanies = jest.fn().mockReturnValue(of(mockCompanyResponse));
      jest.spyOn(component, 'getDefinableForEditNdr').mockImplementation(() => {});

      component.getCompaniesForEditNdr();

      expect(component.getDefinableForEditNdr).not.toHaveBeenCalled();
    });
  });

  describe('getDefinableForEditNdr', () => {
    it('should call projectService.getDefinableWork and set definable settings', () => {
      const mockDefinableResponse = {
        data: [
          { id: 1, DFOW: 'Feature 1' },
          { id: 2, DFOW: 'Feature 2' }
        ]
      };
      mockProjectService.getDefinableWork = jest.fn().mockReturnValue(of(mockDefinableResponse));
      jest.spyOn(component, 'getLocationForEditNdr').mockImplementation(() => {});

      component.getDefinableForEditNdr();

      expect(mockProjectService.getDefinableWork).toHaveBeenCalledWith({
        ProjectId: component.ProjectId,
        ParentCompanyId: component.ParentCompanyId
      });
      expect(component.definableFeatureOfWorkList).toEqual(mockDefinableResponse.data);
      expect(component.editNdrDefinableDropdownSettings).toBeDefined();
      expect(component.editNdrDefinableDropdownSettings.singleSelection).toBe(false);
      expect(component.editNdrDefinableDropdownSettings.idField).toBe('id');
      expect(component.editNdrDefinableDropdownSettings.textField).toBe('DFOW');
      expect(component.getLocationForEditNdr).toHaveBeenCalled();
    });

    it('should handle empty definable response', () => {
      const mockDefinableResponse = { data: null };
      mockProjectService.getDefinableWork = jest.fn().mockReturnValue(of(mockDefinableResponse));
      jest.spyOn(component, 'getLocationForEditNdr').mockImplementation(() => {});

      component.getDefinableForEditNdr();

      expect(component.getLocationForEditNdr).not.toHaveBeenCalled();
    });
  });

  describe('getLocationForEditNdr', () => {
    it('should call projectService.getLocations and set location settings', () => {
      const mockLocationResponse = {
        data: [
          {
            id: 1,
            locationPath: 'Location 1',
            gateDetails: [{ id: 1, name: 'Gate 1' }],
            EquipmentId: [{ id: 1, equipmentName: 'Equipment 1', PresetEquipmentType: { isCraneType: true } }]
          }
        ]
      };
      mockProjectService.getLocations = jest.fn().mockReturnValue(of(mockLocationResponse));
      jest.spyOn(component, 'getEquipmentCraneRequest').mockImplementation(() => {});

      component.getLocationForEditNdr();

      expect(mockProjectService.getLocations).toHaveBeenCalledWith({
        ProjectId: component.ProjectId,
        ParentCompanyId: component.ParentCompanyId
      });
      expect(component.locationList).toEqual(mockLocationResponse.data);
      expect(component.gateList).toEqual(mockLocationResponse.data[0].gateDetails);
      expect(component.equipmentList).toHaveLength(1);
      expect(component.editNdrLocationDropdownSettings).toBeDefined();
      expect(component.editNdrLocationDropdownSettings.singleSelection).toBe(true);
      expect(component.editNdrLocationDropdownSettings.idField).toBe('id');
      expect(component.editNdrLocationDropdownSettings.textField).toBe('locationPath');
      expect(component.getEquipmentCraneRequest).toHaveBeenCalled();
    });

    it('should filter equipment by crane type', () => {
      const mockLocationResponse = {
        data: [
          {
            id: 1,
            locationPath: 'Location 1',
            gateDetails: [],
            EquipmentId: [
              { id: 1, equipmentName: 'Crane', PresetEquipmentType: { isCraneType: true } },
              { id: 2, equipmentName: 'Truck', PresetEquipmentType: { isCraneType: false } },
              { id: 3, equipmentName: 'Another Crane', PresetEquipmentType: { isCraneType: true } }
            ]
          }
        ]
      };
      mockProjectService.getLocations = jest.fn().mockReturnValue(of(mockLocationResponse));
      jest.spyOn(component, 'getEquipmentCraneRequest').mockImplementation(() => {});

      component.getLocationForEditNdr();

      expect(component.equipmentList).toHaveLength(2); // Only crane type equipment
      expect(component.equipmentList[0].equipmentName).toBe('Crane');
      expect(component.equipmentList[1].equipmentName).toBe('Another Crane');
    });

    it('should handle empty location response', () => {
      const mockLocationResponse = { data: null };
      mockProjectService.getLocations = jest.fn().mockReturnValue(of(mockLocationResponse));
      jest.spyOn(component, 'getEquipmentCraneRequest').mockImplementation(() => {});

      component.getLocationForEditNdr();

      expect(component.getEquipmentCraneRequest).not.toHaveBeenCalled();
    });
  });

  describe('getMembers', () => {
    it('should call projectService.listAllMember and set member list', () => {
      const mockMemberResponse = {
        data: [
          { id: 1, name: 'Member 1' },
          { id: 2, name: 'Member 2' }
        ]
      };
      mockProjectService.listAllMember = jest.fn().mockReturnValue(of(mockMemberResponse));

      component.getMembers();

      expect(mockProjectService.listAllMember).toHaveBeenCalledWith({
        ProjectId: component.ProjectId,
        ParentCompanyId: component.ParentCompanyId
      });
      expect(component.memberList).toEqual(mockMemberResponse.data);
    });

    it('should handle empty member response', () => {
      const mockMemberResponse = null;
      mockProjectService.listAllMember = jest.fn().mockReturnValue(of(mockMemberResponse));

      component.getMembers();

      expect(mockProjectService.listAllMember).toHaveBeenCalled();
      expect(component.memberList).toBeUndefined();
    });
  });

  describe('openContentModal', () => {
    it('should set modalLoader to false', () => {
      component.modalLoader = true;

      component.openContentModal();

      expect(component.modalLoader).toBe(false);
    });
  });

  describe('getEquipmentCraneRequest', () => {
    beforeEach(() => {
      component.craneRequestId = 'test-crane-id';
      component.ProjectId = 'test-project';
      component.ParentCompanyId = 'test-company';
      jest.spyOn(component, 'setlocation').mockImplementation(() => {});
      jest.spyOn(component, 'setCompany').mockImplementation(() => {});
      jest.spyOn(component, 'setDefine').mockImplementation(() => {});
      jest.spyOn(component, 'setMember').mockImplementation(() => {});
      jest.spyOn(component, 'setEquipment').mockImplementation(() => {});
      jest.spyOn(component, 'setRepeat').mockImplementation(() => {});
      jest.spyOn(component, 'openContentModal').mockImplementation(() => {});
      jest.spyOn(component, 'generateWeekDates').mockImplementation(() => {});
      jest.spyOn(component, 'getBookingData').mockImplementation(() => {});
    });

    it('should load and set crane request data', () => {
      const mockResponse = {
        data: {
          id: 1,
          CraneRequestId: 'CR-001',
          description: 'Test Description',
          craneDeliveryStart: '2024-03-20T10:00:00',
          craneDeliveryEnd: '2024-03-20T12:00:00',
          escort: true,
          additionalNotes: 'Test Notes',
          pickUpLocation: 'Pickup Location',
          dropOffLocation: 'Drop Location',
          isAssociatedWithDeliveryRequest: false,
          gateDetails: [{ Gate: { id: 1 } }],
          recurrence: {
            id: 1,
            recurrence: 'Weekly',
            recurrenceEndDate: '2024-12-31',
            repeatEveryCount: 2,
            repeatEveryType: 'Weeks',
            chosenDateOfMonthValue: 1,
            chosenDateOfMonth: true,
            dateOfMonth: 15,
            monthlyRepeatType: 'date',
            days: ['Monday', 'Wednesday']
          }
        }
      };
      mockDeliveryService.getEquipmentCraneRequest = jest.fn().mockReturnValue(of(mockResponse));

      component.getEquipmentCraneRequest();

      expect(mockDeliveryService.getEquipmentCraneRequest).toHaveBeenCalledWith({
        CraneRequestId: 'test-crane-id',
        ParentCompanyId: 'test-company',
        ProjectId: 'test-project'
      });

      expect(component.currentEditItem).toEqual(mockResponse.data);
      expect(component.craneEditRequest.get('id').value).toBe(1);
      expect(component.craneEditRequest.get('CraneRequestId').value).toBe('CR-001');
      expect(component.craneEditRequest.get('description').value).toBe('Test Description');
      expect(component.craneEditRequest.get('isEscortNeeded').value).toBe(true);
      expect(component.craneEditRequest.get('additionalNotes').value).toBe('Test Notes');
      expect(component.craneEditRequest.get('GateId').value).toBe(1);
      expect(component.craneEditRequest.get('pickUpLocation').value).toBe('Pickup Location');
      expect(component.craneEditRequest.get('dropOffLocation').value).toBe('Drop Location');
      expect(component.craneEditRequest.get('isAssociatedWithDeliveryRequest').value).toBe(false);

      // Recurrence fields
      expect(component.craneEditRequest.get('recurrenceId').value).toBe(1);
      expect(component.craneEditRequest.get('recurrence').value).toBe('Weekly');
      expect(component.craneEditRequest.get('repeatEveryCount').value).toBe(2);
      expect(component.craneEditRequest.get('repeatEveryType').value).toBe('Weeks');
      expect(component.craneEditRequest.get('chosenDateOfMonth').value).toBe(1);
      expect(component.craneEditRequest.get('dateOfMonth').value).toBe(15);
      expect(component.craneEditRequest.get('monthlyRepeatType').value).toBe('date');

      expect(component.setlocation).toHaveBeenCalled();
      expect(component.setCompany).toHaveBeenCalled();
      expect(component.setDefine).toHaveBeenCalled();
      expect(component.setMember).toHaveBeenCalled();
      expect(component.setEquipment).toHaveBeenCalled();
      expect(component.setRepeat).toHaveBeenCalledWith(mockResponse.data.recurrence);
      expect(component.openContentModal).toHaveBeenCalled();
      expect(component.generateWeekDates).toHaveBeenCalled();
      expect(component.getBookingData).toHaveBeenCalled();
      expect(component.loader).toBe(true);
    });

    it('should handle response without recurrence data', () => {
      const mockResponse = {
        data: {
          id: 1,
          CraneRequestId: 'CR-001',
          description: 'Test Description',
          craneDeliveryStart: '2024-03-20T10:00:00',
          craneDeliveryEnd: '2024-03-20T12:00:00',
          escort: false,
          additionalNotes: '',
          gateDetails: [{ Gate: { id: 1 } }],
          recurrence: null
        }
      };
      mockDeliveryService.getEquipmentCraneRequest = jest.fn().mockReturnValue(of(mockResponse));

      component.getEquipmentCraneRequest();

      expect(component.craneEditRequest.get('recurrenceId').value).toBeNull();
      expect(component.craneEditRequest.get('recurrence').value).toBeNull();
    });
  });

  // Recurrence Methods Tests
  describe('Recurrence Methods', () => {
    describe('repeatEveryRecurrence', () => {
      it('should handle "Does Not Repeat" selection', () => {
        component.repeatEveryRecurrence('Does Not Repeat');

        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('');
      });

      it('should handle "Daily" selection', () => {
        component.repeatEveryRecurrence('Daily');

        expect(component.craneEditRequest.get('repeatEveryCount').value).toBe(1);
        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('Day');
      });

      it('should handle "Weekly" selection', () => {
        component.repeatEveryRecurrence('Weekly');

        expect(component.craneEditRequest.get('repeatEveryCount').value).toBe(1);
        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('Week');
      });

      it('should handle "Monthly" selection', () => {
        component.repeatEveryRecurrence('Monthly');

        expect(component.craneEditRequest.get('repeatEveryCount').value).toBe(1);
        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('Month');
      });

      it('should handle "Yearly" selection', () => {
        component.repeatEveryRecurrence('Yearly');

        expect(component.craneEditRequest.get('repeatEveryCount').value).toBe(1);
        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('Year');
      });
    });
  });

  describe('Time Selection Methods', () => {
    describe('selectAM', () => {
      it('should set isAM to true', () => {
        component.isAM = false;
        component.selectAM();
        expect(component.isAM).toBe(true);
      });
    });

    describe('selectPM', () => {
      it('should set isAM to false', () => {
        component.isAM = true;
        component.selectPM();
        expect(component.isAM).toBe(false);
      });
    });

    describe('selectTime', () => {
      it('should update form values and set NDRTimingChanged', () => {
        const startStr = '2024-03-20T10:00:00';
        const endStr = '2024-03-20T12:00:00';

        component.selectTime(startStr, endStr);

        expect(component.craneEditRequest.get('craneDeliveryStart').value).toEqual(new Date(startStr));
        expect(component.craneEditRequest.get('craneDeliveryEnd').value).toEqual(new Date(endStr));
        expect(component.craneEditRequest.get('deliveryDate').value).toEqual(new Date(startStr));
        expect(component.NDRTimingChanged).toBe(true);
        expect(component.selectedTime).toContain('10:00:00 AM');
      });
    });

    describe('scrollToTime', () => {
      it('should scroll to selected time button', () => {
        const mockContainer = {
          querySelectorAll: jest.fn().mockReturnValue([
            { offsetTop: 100 },
            { offsetTop: 200 }
          ]),
          offsetTop: 50,
          scrollTop: 0
        };
        component.timeSlotsContainer = { nativeElement: mockContainer } as any;

        component.scrollToTime(1);

        expect(mockContainer.scrollTop).toBe(150); // 200 - 50
      });

      it('should handle invalid index gracefully', () => {
        const mockContainer = {
          querySelectorAll: jest.fn().mockReturnValue([]),
          offsetTop: 50,
          scrollTop: 0
        };
        component.timeSlotsContainer = { nativeElement: mockContainer } as any;

        expect(() => component.scrollToTime(5)).not.toThrow();
      });
    });
  });

  describe('Date Generation Methods', () => {
    describe('updateWeeklyDates', () => {
      it('should update weekly dates based on end date', () => {
        component.craneEditRequest.patchValue({
          endDate: '2024-03-25'
        });
        component.weekDates = [{
          name: 'Mon',
          date: '20',
          fullDate: '2024-03-20'
        }];

        component.updateWeeklyDates();

        expect(component.weekDates.length).toBeGreaterThan(1);
      });

      it('should handle empty weekDates array', () => {
        component.craneEditRequest.patchValue({
          endDate: '2024-03-25'
        });
        component.weekDates = [];

        expect(() => component.updateWeeklyDates()).not.toThrow();
      });
    });

    describe('generateWeekDates', () => {
      it('should generate 5 consecutive dates', () => {
        const selectedDate = '2024-03-20';

        component.generateWeekDates(selectedDate);

        expect(component.weekDates).toHaveLength(5);
        expect(component.weekDates[0].fullDate).toBe('2024-03-20');
        expect(component.weekDates[4].fullDate).toBe('2024-03-24');
        expect(component.selectedDate).toEqual(component.weekDates[0]);
      });
    });

    describe('selectDate', () => {
      it('should update selected date and form value', () => {
        const mockDay = {
          name: 'Wed',
          date: '20',
          fullDate: '2024-03-20'
        };

        component.selectDate(mockDay);

        expect(component.selectedDate).toEqual(mockDay);
        expect(component.craneEditRequest.get('deliveryDate').value).toBe('03/20/2024');
      });
    });
  });

  describe('Available Slots Methods', () => {
    describe('getAvailableSlots', () => {
      beforeEach(() => {
        component.craneEditRequest.patchValue({
          EquipmentId: [{ id: 1 }, { id: 2 }],
          LocationId: [{ id: 3 }]
        });
        component.timeZone = 'America/New_York';
        component.selectedMinutes = 60;
      });

      it('should call deliveryService.getAvailableTimeSlots with correct payload', () => {
        const mockResponse = {
          slots: {
            AM: [{ time: '09:00', available: true }],
            PM: [{ time: '14:00', available: true }]
          }
        };
        mockDeliveryService.getAvailableTimeSlots = jest.fn().mockReturnValue(of(mockResponse));

        component.getAvailableSlots('2024-03-20');

        expect(mockDeliveryService.getAvailableTimeSlots).toHaveBeenCalledWith(
          component.ProjectId,
          {
            date: '2024-03-20',
            equipmentId: [1, 2],
            timeZone: 'America/New_York',
            duration: 60,
            LocationId: 3,
            bookingType: 'crane'
          }
        );
        expect(component.availableTimes).toEqual(mockResponse.slots);
        expect(component.isSlotsNull).toBe(false);
      });

      it('should set isSlotsNull to true when no slots available', () => {
        const mockResponse = {
          slots: {
            AM: [],
            PM: []
          }
        };
        mockDeliveryService.getAvailableTimeSlots = jest.fn().mockReturnValue(of(mockResponse));

        component.getAvailableSlots('2024-03-20');

        expect(component.isSlotsNull).toBe(true);
      });

      it('should not call service when EquipmentId is not set', () => {
        component.craneEditRequest.patchValue({ EquipmentId: null });
        mockDeliveryService.getAvailableTimeSlots = jest.fn();

        component.getAvailableSlots('2024-03-20');

        expect(mockDeliveryService.getAvailableTimeSlots).not.toHaveBeenCalled();
      });
    });
  });

  describe('Duration and Time Selection Methods', () => {
    describe('selectDuration', () => {
      it('should calculate selectedMinutes correctly', () => {
        component.selectedHour = 2;
        component.selectedMinute = 30;

        component.selectDuration(null);

        expect(component.selectedMinutes).toBe(150); // (2 * 60) + 30
      });
    });

    describe('selectHour', () => {
      beforeEach(() => {
        component.craneEditRequest.patchValue({
          craneDeliveryStart: new Date('2024-03-20T10:00:00')
        });
        component.selectedMinute = 30;
        jest.spyOn(component, 'durationCloseDropdown').mockImplementation(() => {});
        jest.spyOn(component, 'updateDropdownState').mockImplementation(() => {});
        jest.spyOn(component, 'selectDuration').mockImplementation(() => {});
      });

      it('should update delivery end time and form values', () => {
        component.selectHour(2);

        expect(component.selectedHour).toBe(2);
        expect(component.craneEditRequest.get('craneDeliveryEnd').value).toBeDefined();
        expect(component.endPickerTime).toBeDefined();
        expect(component.durationCloseDropdown).toHaveBeenCalled();
        expect(component.updateDropdownState).toHaveBeenCalled();
        expect(component.selectDuration).toHaveBeenCalledWith(2);
      });
    });

    describe('selectMinute', () => {
      beforeEach(() => {
        component.craneEditRequest.patchValue({
          craneDeliveryStart: new Date('2024-03-20T10:00:00')
        });
        component.selectedHour = 2;
        jest.spyOn(component, 'durationCloseDropdown').mockImplementation(() => {});
        jest.spyOn(component, 'updateDropdownState').mockImplementation(() => {});
        jest.spyOn(component, 'selectDuration').mockImplementation(() => {});
      });

      it('should update delivery end time and form values', () => {
        component.selectMinute(45);

        expect(component.selectedMinute).toBe(45);
        expect(component.craneEditRequest.get('craneDeliveryEnd').value).toBeDefined();
        expect(component.endPickerTime).toBeDefined();
        expect(component.durationCloseDropdown).toHaveBeenCalled();
        expect(component.updateDropdownState).toHaveBeenCalled();
        expect(component.selectDuration).toHaveBeenCalledWith(45);
      });
    });

    describe('durationCloseDropdown', () => {
      it('should set durationisOpen to false', () => {
        component.durationisOpen = true;
        component.durationCloseDropdown();
        expect(component.durationisOpen).toBe(false);
      });
    });

    describe('updateDropdownState', () => {
      it('should close dropdown when both hour and minute are selected', () => {
        component.selectedHour = 2;
        component.selectedMinutes = 30;
        component.durationisOpen = true;

        component.updateDropdownState();

        expect(component.durationisOpen).toBe(false);
      });

      it('should not close dropdown when hour is not selected', () => {
        component.selectedHour = null;
        component.selectedMinutes = 30;
        component.durationisOpen = true;

        component.updateDropdownState();

        expect(component.durationisOpen).toBe(true);
      });

      it('should not close dropdown when minutes is not selected', () => {
        component.selectedHour = 2;
        component.selectedMinutes = null;
        component.durationisOpen = true;

        component.updateDropdownState();

        expect(component.durationisOpen).toBe(true);
      });
    });

    describe('durationToggleDropdown', () => {
      it('should toggle durationisOpen from false to true', () => {
        component.durationisOpen = false;
        component.durationToggleDropdown();
        expect(component.durationisOpen).toBe(true);
      });

      it('should toggle durationisOpen from true to false', () => {
        component.durationisOpen = true;
        component.durationToggleDropdown();
        expect(component.durationisOpen).toBe(false);
      });
    });
  });

  describe('Service Integration Methods', () => {
    describe('getDefinableForEditNdr', () => {
      it('should call projectService.getDefinableWork and set settings', () => {
        const mockResponse = {
          data: [
            { id: 1, DFOW: 'Work 1' },
            { id: 2, DFOW: 'Work 2' }
          ]
        };
        mockProjectService.getDefinableWork = jest.fn().mockReturnValue(of(mockResponse));
        jest.spyOn(component, 'getLocationForEditNdr').mockImplementation(() => {});

        component.getDefinableForEditNdr();

        expect(mockProjectService.getDefinableWork).toHaveBeenCalledWith({
          ProjectId: component.ProjectId,
          ParentCompanyId: component.ParentCompanyId
        });
        expect(component.definableFeatureOfWorkList).toEqual(mockResponse.data);
        expect(component.editNdrDefinableDropdownSettings).toBeDefined();
        expect(component.editNdrDefinableDropdownSettings.singleSelection).toBe(false);
        expect(component.editNdrDefinableDropdownSettings.idField).toBe('id');
        expect(component.editNdrDefinableDropdownSettings.textField).toBe('DFOW');
        expect(component.getLocationForEditNdr).toHaveBeenCalled();
      });

      it('should handle empty definable work response', () => {
        const mockResponse = { data: null };
        mockProjectService.getDefinableWork = jest.fn().mockReturnValue(of(mockResponse));
        jest.spyOn(component, 'getLocationForEditNdr').mockImplementation(() => {});

        component.getDefinableForEditNdr();

        expect(component.getLocationForEditNdr).not.toHaveBeenCalled();
      });
    });

    describe('getLocationForEditNdr', () => {
      it('should call projectService.getLocations and set location data', () => {
        const mockResponse = {
          data: [{
            id: 1,
            locationPath: 'Location 1',
            gateDetails: [{ id: 1, name: 'Gate 1' }],
            EquipmentId: [
              { id: 1, PresetEquipmentType: { isCraneType: true } },
              { id: 2, PresetEquipmentType: { isCraneType: false } }
            ]
          }]
        };
        mockProjectService.getLocations = jest.fn().mockReturnValue(of(mockResponse));
        jest.spyOn(component, 'getEquipmentCraneRequest').mockImplementation(() => {});

        component.getLocationForEditNdr();

        expect(mockProjectService.getLocations).toHaveBeenCalledWith({
          ProjectId: component.ProjectId,
          ParentCompanyId: component.ParentCompanyId
        });
        expect(component.locationList).toEqual(mockResponse.data);
        expect(component.gateList).toEqual(mockResponse.data[0].gateDetails);
        expect(component.equipmentList).toHaveLength(1); // Only crane type equipment
        expect(component.editNdrLocationDropdownSettings).toBeDefined();
        expect(component.getEquipmentCraneRequest).toHaveBeenCalled();
      });

      it('should handle location data without equipment', () => {
        const mockResponse = {
          data: [{
            id: 1,
            locationPath: 'Location 1',
            gateDetails: [{ id: 1, name: 'Gate 1' }],
            EquipmentId: null
          }]
        };
        mockProjectService.getLocations = jest.fn().mockReturnValue(of(mockResponse));
        jest.spyOn(component, 'getEquipmentCraneRequest').mockImplementation(() => {});

        component.getLocationForEditNdr();

        expect(component.equipmentList).toEqual([]);
      });
    });
  });

  describe('Location and Equipment Selection', () => {
    describe('locationSelected', () => {
      beforeEach(() => {
        component.locationList = [
          {
            id: 1,
            locationPath: 'Location 1',
            gateDetails: [{ id: 1, name: 'Gate 1' }],
            EquipmentId: [
              { id: 1, equipmentName: 'Equipment 1' },
              { id: 2, equipmentName: 'Equipment 2' }
            ],
            TimeZoneId: [{ location: 'America/New_York' }]
          }
        ];
      });

      it('should update gates and equipment when location is selected', () => {
        const selectedLocation = { id: 1 };

        component.locationSelected(selectedLocation);

        expect(component.getChosenLocation).toHaveLength(1);
        expect(component.gateList).toEqual(component.locationList[0].gateDetails);
        expect(component.equipmentList).toHaveLength(2);
        expect(component.selectedLocationId).toBe(1);
        expect(component.timeZone).toBe('America/New_York');
      });

      it('should handle location without equipment', () => {
        component.locationList[0].EquipmentId = null;
        const selectedLocation = { id: 1 };

        component.locationSelected(selectedLocation);

        expect(component.craneEditRequest.get('EquipmentId').value).toEqual([]);
      });

      it('should handle location without timezone', () => {
        component.locationList[0].TimeZoneId = null;
        const selectedLocation = { id: 1 };

        component.locationSelected(selectedLocation);

        expect(component.timeZone).toBe('');
      });
    });

    describe('setlocation', () => {
      it('should set location data from currentEditItem', () => {
        component.currentEditItem = {
          location: {
            id: 1,
            locationPath: 'Test Location',
            TimeZoneId: [{ location: 'America/New_York' }]
          }
        };

        component.setlocation();

        expect(component.getChosenLocation).toHaveLength(1);
        expect(component.getChosenLocation[0].id).toBe(1);
        expect(component.craneEditRequest.get('LocationId').value).toEqual(component.getChosenLocation);
        expect(component.selectedLocationId).toBe(1);
        expect(component.timeZone).toBe('America/New_York');
      });

      it('should handle currentEditItem without location', () => {
        component.currentEditItem = {};

        expect(() => component.setlocation()).not.toThrow();
      });
    });

    describe('equipmentSelected', () => {
      it('should handle equipment selection', () => {
        const mockValue = { id: 1, equipmentName: 'Test Equipment' };

        expect(() => component.equipmentSelected(mockValue)).not.toThrow();
      });
    });
  });

  describe('Member and Company Management', () => {
    describe('setMember', () => {
      beforeEach(() => {
        component.currentEditItem = {
          memberDetails: [
            {
              Member: {
                id: 1,
                User: {
                  firstName: 'John',
                  lastName: 'Doe',
                  email: '<EMAIL>'
                },
                isGuestUser: false
              }
            },
            {
              Member: {
                id: 2,
                User: {
                  firstName: null,
                  lastName: null,
                  email: '<EMAIL>'
                },
                isGuestUser: true
              }
            }
          ]
        };
        component.authUser = { User: { email: '<EMAIL>' } };
        mockDeliveryService.getMemberRole = jest.fn().mockReturnValue(of({ data: component.authUser }));
      });

      it('should set member details correctly', () => {
        component.setMember();

        expect(mockDeliveryService.getMemberRole).toHaveBeenCalledWith({
          ProjectId: component.ProjectId,
          ParentCompanyId: component.ParentCompanyId
        });

        // Check that the form is updated with member data
        const responsiblePersons = component.craneEditRequest.get('responsiblePersons').value;
        expect(responsiblePersons).toHaveLength(2);
        expect(responsiblePersons[0].email).toContain('John Doe');
        expect(responsiblePersons[0].readonly).toBe(true); // Current user should be readonly
        expect(responsiblePersons[1].email).toContain('Guest');
      });

      it('should handle members without user details', () => {
        component.currentEditItem.memberDetails = [
          {
            Member: {
              id: 1,
              User: null,
              isGuestUser: false
            }
          }
        ];

        expect(() => component.setMember()).not.toThrow();
      });
    });

    describe('setDefine', () => {
      it('should set definable work details', () => {
        component.currentEditItem = {
          defineWorkDetails: [
            {
              DeliverDefineWork: {
                id: 1,
                DFOW: 'Work Type 1'
              }
            },
            {
              DeliverDefineWork: {
                id: 2,
                DFOW: 'Work Type 2'
              }
            }
          ]
        };

        component.setDefine();

        expect(component.editBeforeDFOW).toHaveLength(2);
        expect(component.editBeforeDFOW[0].id).toBe(1);
        expect(component.editBeforeDFOW[0].DFOW).toBe('Work Type 1');
        expect(component.craneEditRequest.get('definableFeatureOfWorks').value).toEqual(component.editBeforeDFOW);
      });

      it('should handle undefined defineWorkDetails', () => {
        component.currentEditItem = {};

        expect(() => component.setDefine()).not.toThrow();
        expect(component.editBeforeDFOW).toEqual([]);
      });
    });

    describe('setCompany', () => {
      it('should set company details', () => {
        component.currentEditItem = {
          companyDetails: [
            {
              Company: {
                id: 1,
                companyName: 'Company 1'
              }
            },
            {
              Company: {
                id: 2,
                companyName: 'Company 2'
              }
            }
          ]
        };

        component.setCompany();

        expect(component.editBeforeCompany).toHaveLength(2);
        expect(component.editBeforeCompany[0].id).toBe(1);
        expect(component.editBeforeCompany[0].companyName).toBe('Company 1');
        expect(component.craneEditRequest.get('companies').value).toEqual(component.editBeforeCompany);
      });

      it('should handle undefined companyDetails', () => {
        component.currentEditItem = {};

        expect(() => component.setCompany()).not.toThrow();
        expect(component.editBeforeCompany).toEqual([]);
      });
    });

    describe('setEquipment', () => {
      it('should set equipment details', () => {
        component.currentEditItem = {
          equipmentDetails: [
            {
              Equipment: {
                id: 1,
                equipmentName: 'Equipment 1'
              }
            },
            {
              Equipment: {
                id: 2,
                equipmentName: 'Equipment 2'
              }
            }
          ]
        };

        component.setEquipment();

        expect(component.editBeforeEquipment).toHaveLength(2);
        expect(component.editBeforeEquipment[0].id).toBe(1);
        expect(component.editBeforeEquipment[0].equipmentName).toBe('Equipment 1');
        expect(component.craneEditRequest.get('EquipmentId').value).toEqual(component.editBeforeEquipment);
      });

      it('should handle undefined equipmentDetails', () => {
        component.currentEditItem = {};

        expect(() => component.setEquipment()).not.toThrow();
        expect(component.editBeforeEquipment).toEqual([]);
      });
    });
  });

  describe('Utility and Helper Methods', () => {
    describe('convertStart', () => {
      it('should convert delivery date and time correctly', () => {
        const deliveryDate = new Date('2024-03-20T00:00:00');
        const startHours = 10;
        const startMinutes = 30;

        const result = component.convertStart(deliveryDate, startHours, startMinutes);

        expect(result.getFullYear()).toBe(2024);
        expect(result.getMonth()).toBe(2); // March is month 2 (0-indexed)
        expect(result.getDate()).toBe(20);
        expect(result.getHours()).toBe(10);
        expect(result.getMinutes()).toBe(30);
      });
    });

    describe('checkEditDeliveryFutureDate', () => {
      beforeEach(() => {
        component.deliveryWindowTime = 2;
        component.deliveryWindowTimeUnit = 'hours';
      });

      it('should return true when both dates are in the future', () => {
        const futureDate1 = new Date(Date.now() + 5 * 60 * 60 * 1000); // 5 hours from now
        const futureDate2 = new Date(Date.now() + 6 * 60 * 60 * 1000); // 6 hours from now

        const result = component.checkEditDeliveryFutureDate(futureDate1, futureDate2);

        expect(result).toBe(true);
      });

      it('should return false when start date is in the past', () => {
        const pastDate = new Date(Date.now() - 1 * 60 * 60 * 1000); // 1 hour ago
        const futureDate = new Date(Date.now() + 5 * 60 * 60 * 1000); // 5 hours from now

        const result = component.checkEditDeliveryFutureDate(pastDate, futureDate);

        expect(result).toBe(false);
      });
    });

    describe('getMembers', () => {
      it('should call projectService.listAllMember and set memberList', () => {
        const mockResponse = {
          data: [
            { id: 1, name: 'Member 1' },
            { id: 2, name: 'Member 2' }
          ]
        };
        mockProjectService.listAllMember = jest.fn().mockReturnValue(of(mockResponse));

        component.getMembers();

        expect(mockProjectService.listAllMember).toHaveBeenCalledWith({
          ProjectId: component.ProjectId,
          ParentCompanyId: component.ParentCompanyId
        });
        expect(component.memberList).toEqual(mockResponse.data);
      });

      it('should handle empty member response', () => {
        mockProjectService.listAllMember = jest.fn().mockReturnValue(of(null));

        expect(() => component.getMembers()).not.toThrow();
      });
    });
  });

  describe('Recurrence Management Methods', () => {
    beforeEach(() => {
      component.craneEditRequest.patchValue({
        recurrence: 'Weekly',
        repeatEveryCount: 1,
        repeatEveryType: 'Week',
        deliveryDate: '03/20/2024',
        chosenDateOfMonth: 1,
        dateOfMonth: '20',
        monthlyRepeatType: null,
        recurrenceEndDate: new Date('2024-12-31')
      });
      component.weekDays = [
        { value: 'Monday', checked: false, isDisabled: false },
        { value: 'Tuesday', checked: false, isDisabled: false },
        { value: 'Wednesday', checked: false, isDisabled: false },
        { value: 'Thursday', checked: false, isDisabled: false },
        { value: 'Friday', checked: false, isDisabled: false },
        { value: 'Saturday', checked: false, isDisabled: false },
        { value: 'Sunday', checked: false, isDisabled: false }
      ];
      component.checkform = component.craneEditRequest.get('days') as any;
      jest.spyOn(component, 'occurMessage').mockImplementation(() => {});
      jest.spyOn(component, 'onEditSubmitForm').mockImplementation(() => {});
    });

    describe('repeatEveryRecurrence', () => {
      it('should handle "Does Not Repeat" selection', () => {
        component.repeatEveryRecurrence('Does Not Repeat');
        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('');
      });

      it('should handle "Daily" selection', () => {
        component.repeatEveryRecurrence('Daily');
        expect(component.craneEditRequest.get('repeatEveryCount').value).toBe(1);
        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('Day');
      });

      it('should handle "Weekly" selection', () => {
        component.repeatEveryRecurrence('Weekly');
        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('Week');
      });

      it('should handle "Monthly" selection', () => {
        component.repeatEveryRecurrence('Monthly');
        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('Month');
      });

      it('should handle "Yearly" selection', () => {
        component.repeatEveryRecurrence('Yearly');
        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('Year');
      });
    });

    describe('onRecurrenceSelect', () => {
      beforeEach(() => {
        jest.spyOn(component, 'repeatEveryRecurrence').mockImplementation(() => {});
        jest.spyOn(component, 'changeMonthlyRecurrence').mockImplementation(() => {});
        jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});
      });

      it('should handle Daily recurrence with count > 1', () => {
        component.craneEditRequest.patchValue({ repeatEveryCount: 2 });

        component.onRecurrenceSelect('Daily');

        expect(component.isRepeatWithMultipleRecurrence).toBe(false);
        expect(component.isRepeatWithSingleRecurrence).toBe(false);
        expect(component.selectedRecurrence).toBe('Daily');
        expect(component.repeatEveryRecurrence).toHaveBeenCalledWith('Daily');
      });

      it('should handle Weekly recurrence with count > 1', () => {
        component.craneEditRequest.patchValue({ repeatEveryCount: 2 });

        component.onRecurrenceSelect('Weekly');

        expect(component.isRepeatWithMultipleRecurrence).toBe(true);
        expect(component.isRepeatWithSingleRecurrence).toBe(false);
        expect(component.selectedRecurrence).toBe('Weekly');
      });

      it('should handle Weekly recurrence with count = 1', () => {
        component.craneEditRequest.patchValue({ repeatEveryCount: 1 });

        component.onRecurrenceSelect('Weekly');

        expect(component.isRepeatWithMultipleRecurrence).toBe(false);
        expect(component.isRepeatWithSingleRecurrence).toBe(true);
      });

      it('should handle Monthly recurrence with count = 1', () => {
        component.craneEditRequest.patchValue({ repeatEveryCount: 1 });

        component.onRecurrenceSelect('Monthly');

        expect(component.isRepeatWithMultipleRecurrence).toBe(false);
        expect(component.isRepeatWithSingleRecurrence).toBe(true);
        expect(component.changeMonthlyRecurrence).toHaveBeenCalled();
        expect(component.showMonthlyRecurrence).toHaveBeenCalled();
      });

      it('should handle Yearly recurrence with count > 1', () => {
        component.craneEditRequest.patchValue({ repeatEveryCount: 2 });

        component.onRecurrenceSelect('Yearly');

        expect(component.isRepeatWithMultipleRecurrence).toBe(true);
        expect(component.isRepeatWithSingleRecurrence).toBe(false);
        expect(component.changeMonthlyRecurrence).toHaveBeenCalled();
        expect(component.showMonthlyRecurrence).toHaveBeenCalled();
      });
    });

    describe('changeRecurrenceCount', () => {
      beforeEach(() => {
        jest.spyOn(component, 'updateRecurrenceUIState').mockImplementation(() => {});
        jest.spyOn(component, 'updateRecurrenceFormValues').mockImplementation(() => {});
      });

      it('should update recurrence when value > 0', () => {
        component.craneEditRequest.patchValue({ recurrence: 'Weekly' });

        component.changeRecurrenceCount(3);

        expect(component.updateRecurrenceUIState).toHaveBeenCalledWith(3);
        expect(component.updateRecurrenceFormValues).toHaveBeenCalledWith(3);
        expect(component.selectedRecurrence).toBe('Weekly');
        expect(component.occurMessage).toHaveBeenCalled();
      });

      it('should set count to 1 when value < 0', () => {
        component.changeRecurrenceCount(-1);
        expect(component.craneEditRequest.get('repeatEveryCount').value).toBe(1);
      });

      it('should not update when value = 0', () => {
        component.changeRecurrenceCount(0);
        expect(component.updateRecurrenceUIState).not.toHaveBeenCalled();
      });
    });

    describe('updateRecurrenceUIState', () => {
      it('should set single recurrence for value = 1', () => {
        component.craneEditRequest.patchValue({ recurrence: 'Daily' });

        component.updateRecurrenceUIState(1);

        expect(component.isRepeatWithSingleRecurrence).toBe(true);
        expect(component.isRepeatWithMultipleRecurrence).toBe(false);
        expect(component.showRecurrenceTypeDropdown).toBe(false);
      });

      it('should set multiple recurrence for value > 1 and Weekly', () => {
        component.craneEditRequest.patchValue({ recurrence: 'Weekly' });

        component.updateRecurrenceUIState(2);

        expect(component.isRepeatWithSingleRecurrence).toBe(false);
        expect(component.isRepeatWithMultipleRecurrence).toBe(true);
        expect(component.showRecurrenceTypeDropdown).toBe(false);
      });

      it('should show dropdown for Daily with value > 1', () => {
        component.craneEditRequest.patchValue({ recurrence: 'Daily' });

        component.updateRecurrenceUIState(2);

        expect(component.isRepeatWithSingleRecurrence).toBe(false);
        expect(component.isRepeatWithMultipleRecurrence).toBe(false);
        expect(component.showRecurrenceTypeDropdown).toBe(true);
      });

      it('should handle non-standard recurrence types', () => {
        component.craneEditRequest.patchValue({ recurrence: 'Custom' });

        component.updateRecurrenceUIState(2);

        // Should not change UI state for unknown recurrence types
        expect(component.isRepeatWithSingleRecurrence).toBe(false);
        expect(component.isRepeatWithMultipleRecurrence).toBe(false);
      });
    });
  });

  describe('Form Value Update Methods', () => {
    describe('updateRecurrenceFormValues', () => {
      beforeEach(() => {
        jest.spyOn(component, 'changeMonthlyRecurrence').mockImplementation(() => {});
        jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});
      });

      it('should handle Daily recurrence with value = 1', () => {
        component.craneEditRequest.patchValue({ recurrence: 'Daily' });

        component.updateRecurrenceFormValues(1);

        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('Day');
      });

      it('should handle Daily recurrence with value > 1', () => {
        component.craneEditRequest.patchValue({ recurrence: 'Daily' });

        component.updateRecurrenceFormValues(3);

        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('Days');
      });

      it('should handle Weekly recurrence with value = 1', () => {
        component.craneEditRequest.patchValue({ recurrence: 'Weekly' });

        component.updateRecurrenceFormValues(1);

        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('Week');
      });

      it('should handle Weekly recurrence with value > 1', () => {
        component.craneEditRequest.patchValue({ recurrence: 'Weekly' });

        component.updateRecurrenceFormValues(2);

        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('Weeks');
      });

      it('should handle Monthly recurrence with value = 1', () => {
        component.craneEditRequest.patchValue({ recurrence: 'Monthly' });

        component.updateRecurrenceFormValues(1);

        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('Month');
        expect(component.changeMonthlyRecurrence).toHaveBeenCalled();
        expect(component.showMonthlyRecurrence).toHaveBeenCalled();
      });

      it('should handle Monthly recurrence with value > 1', () => {
        component.craneEditRequest.patchValue({ recurrence: 'Monthly' });

        component.updateRecurrenceFormValues(3);

        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('Months');
        expect(component.changeMonthlyRecurrence).toHaveBeenCalled();
        expect(component.showMonthlyRecurrence).toHaveBeenCalled();
      });

      it('should handle Yearly recurrence with value = 1', () => {
        component.craneEditRequest.patchValue({ recurrence: 'Yearly' });

        component.updateRecurrenceFormValues(1);

        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('Year');
      });

      it('should handle Yearly recurrence with value > 1', () => {
        component.craneEditRequest.patchValue({ recurrence: 'Yearly' });

        component.updateRecurrenceFormValues(2);

        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('Years');
      });

      it('should handle unknown recurrence type', () => {
        component.craneEditRequest.patchValue({ recurrence: 'Unknown' });
        const originalValue = component.craneEditRequest.get('repeatEveryType').value;

        component.updateRecurrenceFormValues(2);

        // Should not change the value for unknown types
        expect(component.craneEditRequest.get('repeatEveryType').value).toBe(originalValue);
      });
    });

    describe('chooseRepeatEveryType', () => {
      beforeEach(() => {
        component.weekDays = [
          { value: 'Monday', checked: false, isDisabled: false },
          { value: 'Tuesday', checked: false, isDisabled: false },
          { value: 'Wednesday', checked: false, isDisabled: false },
          { value: 'Thursday', checked: false, isDisabled: false },
          { value: 'Friday', checked: false, isDisabled: false },
          { value: 'Saturday', checked: false, isDisabled: false },
          { value: 'Sunday', checked: false, isDisabled: false }
        ];
        component.checkform = component.craneEditRequest.get('days') as any;
        jest.spyOn(component, 'occurMessage').mockImplementation(() => {});
        jest.spyOn(component, 'onEditSubmitForm').mockImplementation(() => {});
      });

      it('should handle Day selection', () => {
        component.chooseRepeatEveryType('Day', null);

        expect(component.craneEditRequest.get('recurrence').value).toBe('Daily');
        expect(component.isRepeatWithSingleRecurrence).toBe(true);
        expect(component.isRepeatWithMultipleRecurrence).toBe(false);
        expect(component.weekDays.every(day => day.checked)).toBe(true);
      });

      it('should handle Days selection', () => {
        component.chooseRepeatEveryType('Days', null);

        expect(component.craneEditRequest.get('recurrence').value).toBe('Daily');
        expect(component.isRepeatWithSingleRecurrence).toBe(false);
        expect(component.isRepeatWithMultipleRecurrence).toBe(true);
        expect(component.weekDays.every(day => day.checked)).toBe(true);
      });

      it('should handle Week selection', () => {
        component.chooseRepeatEveryType('Week', null);

        expect(component.craneEditRequest.get('recurrence').value).toBe('Weekly');
        expect(component.isRepeatWithSingleRecurrence).toBe(true);
        expect(component.isRepeatWithMultipleRecurrence).toBe(false);
        expect(component.weekDays.find(day => day.value === 'Monday').checked).toBe(true);
      });

      it('should handle Weeks selection with eventDetail days', () => {
        const eventDetail = { days: ['Monday', 'Wednesday', 'Friday'] };

        component.chooseRepeatEveryType('Weeks', eventDetail);

        expect(component.craneEditRequest.get('recurrence').value).toBe('Weekly');
        expect(component.isRepeatWithSingleRecurrence).toBe(false);
        expect(component.isRepeatWithMultipleRecurrence).toBe(true);
        expect(component.weekDays.find(day => day.value === 'Monday').checked).toBe(true);
        expect(component.weekDays.find(day => day.value === 'Wednesday').checked).toBe(true);
        expect(component.weekDays.find(day => day.value === 'Friday').checked).toBe(true);
        expect(component.weekDays.find(day => day.value === 'Tuesday').checked).toBe(false);
      });

      it('should handle Month selection', () => {
        component.chooseRepeatEveryType('Month', null);

        expect(component.craneEditRequest.get('recurrence').value).toBe('Monthly');
        expect(component.isRepeatWithSingleRecurrence).toBe(true);
        expect(component.isRepeatWithMultipleRecurrence).toBe(false);
      });

      it('should handle Year selection', () => {
        component.chooseRepeatEveryType('Year', null);

        expect(component.craneEditRequest.get('recurrence').value).toBe('Yearly');
        expect(component.isRepeatWithSingleRecurrence).toBe(true);
        expect(component.isRepeatWithMultipleRecurrence).toBe(false);
      });

      it('should show dropdown when repeatEveryCount > 1', () => {
        component.craneEditRequest.patchValue({ repeatEveryCount: 2 });

        component.chooseRepeatEveryType('Days', null);

        expect(component.showRecurrenceTypeDropdown).toBe(true);
        expect(component.isRepeatWithMultipleRecurrence).toBe(false);
      });
    });
  });

  describe('Monthly Recurrence Methods', () => {
    beforeEach(() => {
      component.craneEditRequest.patchValue({
        deliveryDate: new Date('2024-03-20'),
        chosenDateOfMonth: 1,
        dateOfMonth: '20',
        monthlyRepeatType: null
      });
      jest.spyOn(component, 'setMonthlyOrYearlyRecurrenceOption').mockImplementation(() => {});
      jest.spyOn(component, 'updateFormValidation').mockImplementation(() => {});
      jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});
      jest.spyOn(component, 'occurMessage').mockImplementation(() => {});
      jest.spyOn(component, 'onEditSubmitForm').mockImplementation(() => {});
    });

    describe('changeMonthlyRecurrence', () => {
      it('should call all required methods', () => {
        component.changeMonthlyRecurrence();

        expect(component.setMonthlyOrYearlyRecurrenceOption).toHaveBeenCalled();
        expect(component.updateFormValidation).toHaveBeenCalled();
        expect(component.showMonthlyRecurrence).toHaveBeenCalled();
        expect(component.occurMessage).toHaveBeenCalled();
        expect(component.onEditSubmitForm).toHaveBeenCalled();
      });
    });

    describe('setMonthlyOrYearlyRecurrenceOption', () => {
      it('should set dateOfMonth when chosenDateOfMonth is 1', () => {
        component.craneEditRequest.patchValue({ chosenDateOfMonth: 1 });
        // Temporarily restore the original method for this test
        const originalMethod = component.setMonthlyOrYearlyRecurrenceOption;
        component.setMonthlyOrYearlyRecurrenceOption = EditCraneRequestComponent.prototype.setMonthlyOrYearlyRecurrenceOption;

        component.setMonthlyOrYearlyRecurrenceOption();

        expect(component.craneEditRequest.get('dateOfMonth').value).toBe('20');
        expect(component.craneEditRequest.get('monthlyRepeatType').value).toBe(null);

        // Restore the mock
        component.setMonthlyOrYearlyRecurrenceOption = originalMethod;
      });

      it('should set monthlyRepeatType when chosenDateOfMonth is 2', () => {
        component.craneEditRequest.patchValue({ chosenDateOfMonth: 2 });
        component.monthlyDayOfWeek = 'Third Wednesday';
        // Temporarily restore the original method for this test
        const originalMethod = component.setMonthlyOrYearlyRecurrenceOption;
        component.setMonthlyOrYearlyRecurrenceOption = EditCraneRequestComponent.prototype.setMonthlyOrYearlyRecurrenceOption;

        component.setMonthlyOrYearlyRecurrenceOption();

        expect(component.craneEditRequest.get('dateOfMonth').value).toBe(null);
        expect(component.craneEditRequest.get('monthlyRepeatType').value).toBe('Third Wednesday');

        // Restore the mock
        component.setMonthlyOrYearlyRecurrenceOption = originalMethod;
      });

      it('should set monthlyLastDayOfWeek when chosenDateOfMonth is 3', () => {
        component.craneEditRequest.patchValue({ chosenDateOfMonth: 3 });
        component.monthlyLastDayOfWeek = 'Last Wednesday';
        // Temporarily restore the original method for this test
        const originalMethod = component.setMonthlyOrYearlyRecurrenceOption;
        component.setMonthlyOrYearlyRecurrenceOption = EditCraneRequestComponent.prototype.setMonthlyOrYearlyRecurrenceOption;

        component.setMonthlyOrYearlyRecurrenceOption();

        expect(component.craneEditRequest.get('dateOfMonth').value).toBe(null);
        expect(component.craneEditRequest.get('monthlyRepeatType').value).toBe('Last Wednesday');

        // Restore the mock
        component.setMonthlyOrYearlyRecurrenceOption = originalMethod;
      });
    });

    describe('updateFormValidation', () => {
      it('should set validators for dateOfMonth when chosenDateOfMonth is 1', () => {
        component.craneEditRequest.patchValue({ chosenDateOfMonth: 1 });
        // Temporarily restore the original method for this test
        const originalMethod = component.updateFormValidation;
        component.updateFormValidation = EditCraneRequestComponent.prototype.updateFormValidation;

        const dateOfMonth = component.craneEditRequest.get('dateOfMonth');
        const monthlyRepeatType = component.craneEditRequest.get('monthlyRepeatType');

        jest.spyOn(dateOfMonth, 'setValidators');
        jest.spyOn(monthlyRepeatType, 'clearValidators');

        component.updateFormValidation();

        expect(dateOfMonth.setValidators).toHaveBeenCalled();
        expect(monthlyRepeatType.clearValidators).toHaveBeenCalled();

        // Restore the mock
        component.updateFormValidation = originalMethod;
      });

      it('should set validators for monthlyRepeatType when chosenDateOfMonth is not 1', () => {
        component.craneEditRequest.patchValue({ chosenDateOfMonth: 2 });
        // Temporarily restore the original method for this test
        const originalMethod = component.updateFormValidation;
        component.updateFormValidation = EditCraneRequestComponent.prototype.updateFormValidation;

        const dateOfMonth = component.craneEditRequest.get('dateOfMonth');
        const monthlyRepeatType = component.craneEditRequest.get('monthlyRepeatType');

        jest.spyOn(dateOfMonth, 'clearValidators');
        jest.spyOn(monthlyRepeatType, 'setValidators');

        component.updateFormValidation();

        expect(monthlyRepeatType.setValidators).toHaveBeenCalled();
        expect(dateOfMonth.clearValidators).toHaveBeenCalled();

        // Restore the mock
        component.updateFormValidation = originalMethod;
      });
    });
  });

  describe('Day Change and Occurrence Methods', () => {
    beforeEach(() => {
      component.weekDays = [
        { value: 'Monday', checked: false, isDisabled: false },
        { value: 'Tuesday', checked: false, isDisabled: false },
        { value: 'Wednesday', checked: false, isDisabled: false },
        { value: 'Thursday', checked: false, isDisabled: false },
        { value: 'Friday', checked: false, isDisabled: false },
        { value: 'Saturday', checked: false, isDisabled: false },
        { value: 'Sunday', checked: false, isDisabled: false }
      ];
      component.checkform = component.craneEditRequest.get('days') as any;
      component.selectedRecurrence = 'Weekly';
      jest.spyOn(component, 'occurMessage').mockImplementation(() => {});
      jest.spyOn(component, 'onEditSubmitForm').mockImplementation(() => {});
    });

    describe('onChange', () => {
      it('should add day when checked is true', () => {
        const event = { target: { value: 'Monday', checked: true } };

        component.onChange(event);

        expect(component.daysEdited).toBe(true);
        expect(component.weekDays.find((day: any) => day.value === 'Monday').checked).toBe(true);
      });

      it('should remove day when checked is false and recurrence is Weekly', () => {
        // First add a day
        component.checkform.push(new UntypedFormControl('Monday'));
        component.checkform.push(new UntypedFormControl('Tuesday'));
        component.weekDays.find((day: any) => day.value === 'Monday').checked = true;

        const event = { target: { value: 'Monday', checked: false } };

        component.onChange(event);

        expect(component.weekDays.find((day: any) => day.value === 'Monday').checked).toBe(false);
      });

      it('should handle Daily recurrence when unchecking', () => {
        component.selectedRecurrence = 'Daily';
        component.checkform.push(new UntypedFormControl('Monday'));
        component.checkform.push(new UntypedFormControl('Tuesday'));

        const event = { target: { value: 'Monday', checked: false } };

        component.onChange(event);

        expect(component.weekDays.find((day: any) => day.value === 'Monday').checked).toBe(false);
      });

      it('should set recurrence to Daily when all 7 days are selected', () => {
        // Add all 7 days
        component.weekDays.forEach((day: any) => {
          component.checkform.push(new UntypedFormControl(day.value));
        });
        component.craneEditRequest.patchValue({ repeatEveryCount: 1 });

        const event = { target: { value: 'Sunday', checked: true } };

        component.onChange(event);

        expect(component.craneEditRequest.get('recurrence').value).toBe('Daily');
        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('Day');
      });

      it('should set recurrence to Weekly when less than 7 days are selected', () => {
        // Add only 3 days
        component.checkform.push(new UntypedFormControl('Monday'));
        component.checkform.push(new UntypedFormControl('Wednesday'));
        component.craneEditRequest.patchValue({ repeatEveryCount: 1 });

        const event = { target: { value: 'Friday', checked: true } };

        component.onChange(event);

        expect(component.craneEditRequest.get('recurrence').value).toBe('Weekly');
        expect(component.craneEditRequest.get('repeatEveryType').value).toBe('Week');
      });
    });

    describe('repeatEveryMessage', () => {
      it('should set message for single Day', () => {
        component.craneEditRequest.patchValue({ repeatEveryType: 'Day' });

        component.repeatEveryMessage();

        expect(component.message).toBe('Occurs every day');
      });

      it('should set message for multiple Days with count 2', () => {
        component.craneEditRequest.patchValue({
          repeatEveryType: 'Days',
          repeatEveryCount: 2
        });

        component.repeatEveryMessage();

        expect(component.message).toBe('Occurs every other day');
      });

      it('should set message for multiple Days with count > 2', () => {
        component.craneEditRequest.patchValue({
          repeatEveryType: 'Days',
          repeatEveryCount: 3
        });

        component.repeatEveryMessage();

        expect(component.message).toBe('Occurs every 3 days');
      });
    });

    describe('occurMessage', () => {
      beforeEach(() => {
        component.craneEditRequest.patchValue({
          recurrenceEndDate: new Date('2024-12-31')
        });
        jest.spyOn(component, 'repeatEveryMessage').mockImplementation(() => {
          component.message = 'Test message';
        });
      });

      it('should handle Week recurrence', () => {
        component.craneEditRequest.patchValue({ repeatEveryType: 'Week' });
        component.weekDays[0].checked = true; // Monday
        component.weekDays[2].checked = true; // Wednesday
        // Temporarily restore the original method for this test
        const originalMethod = component.occurMessage;
        component.occurMessage = EditCraneRequestComponent.prototype.occurMessage;

        component.occurMessage();

        expect(component.message).toContain('Occurs every Monday,Wednesday');

        // Restore the mock
        component.occurMessage = originalMethod;
      });

      it('should handle Weeks recurrence with count 2', () => {
        component.craneEditRequest.patchValue({
          repeatEveryType: 'Weeks',
          repeatEveryCount: 2
        });
        component.weekDays[0].checked = true; // Monday
        // Temporarily restore the original method for this test
        const originalMethod = component.occurMessage;
        component.occurMessage = EditCraneRequestComponent.prototype.occurMessage;

        component.occurMessage();

        expect(component.message).toContain('Occurs every other');

        // Restore the mock
        component.occurMessage = originalMethod;
      });

      it('should handle Month recurrence with chosenDateOfMonth 1', () => {
        component.craneEditRequest.patchValue({
          repeatEveryType: 'Month',
          chosenDateOfMonth: 1
        });
        component.monthlyDate = '20';
        // Temporarily restore the original method for this test
        const originalMethod = component.occurMessage;
        component.occurMessage = EditCraneRequestComponent.prototype.occurMessage;

        component.occurMessage();

        expect(component.message).toContain('Occurs on day 20');

        // Restore the mock
        component.occurMessage = originalMethod;
      });

      it('should handle Month recurrence with chosenDateOfMonth 2', () => {
        component.craneEditRequest.patchValue({
          repeatEveryType: 'Month',
          chosenDateOfMonth: 2
        });
        component.monthlyDayOfWeek = 'Third Wednesday';
        // Temporarily restore the original method for this test
        const originalMethod = component.occurMessage;
        component.occurMessage = EditCraneRequestComponent.prototype.occurMessage;

        component.occurMessage();

        expect(component.message).toContain('Occurs on the Third Wednesday');

        // Restore the mock
        component.occurMessage = originalMethod;
      });

      it('should handle Month recurrence with chosenDateOfMonth 3', () => {
        component.craneEditRequest.patchValue({
          repeatEveryType: 'Month',
          chosenDateOfMonth: 3
        });
        component.monthlyLastDayOfWeek = 'Last Wednesday';
        // Temporarily restore the original method for this test
        const originalMethod = component.occurMessage;
        component.occurMessage = EditCraneRequestComponent.prototype.occurMessage;

        component.occurMessage();

        expect(component.message).toContain('Occurs on the Last Wednesday');

        // Restore the mock
        component.occurMessage = originalMethod;
      });

      it('should append end date to message', () => {
        component.craneEditRequest.patchValue({ repeatEveryType: 'Day' });
        // Temporarily restore the original method for this test
        const originalMethod = component.occurMessage;
        component.occurMessage = EditCraneRequestComponent.prototype.occurMessage;

        component.occurMessage();

        expect(component.message).toContain('until December 31, 2024');

        // Restore the mock
        component.occurMessage = originalMethod;
      });
    });
  });

  describe('Final Submission and Utility Methods', () => {
    beforeEach(() => {
      component.weekDays = [
        { value: 'Monday', checked: false, isDisabled: false },
        { value: 'Tuesday', checked: false, isDisabled: false },
        { value: 'Wednesday', checked: false, isDisabled: false },
        { value: 'Thursday', checked: false, isDisabled: false },
        { value: 'Friday', checked: false, isDisabled: false },
        { value: 'Saturday', checked: false, isDisabled: false },
        { value: 'Sunday', checked: false, isDisabled: false }
      ];
      component.payloadDays = [];
      jest.spyOn(component, 'openRecurrencePopup').mockImplementation(() => {});
    });

    describe('onSubmit', () => {
      it('should collect checked days for Weekly recurrence', () => {
        component.craneEditRequest.patchValue({ recurrence: 'Weekly' });
        component.weekDays[0].checked = true; // Monday
        component.weekDays[2].checked = true; // Wednesday

        component.onSubmit();

        expect(component.payloadDays).toEqual(['Monday', 'Wednesday']);
        expect(component.openRecurrencePopup).toHaveBeenCalled();
      });

      it('should collect checked days for Daily recurrence', () => {
        component.craneEditRequest.patchValue({ recurrence: 'Daily' });
        component.weekDays.forEach(day => day.checked = true);

        component.onSubmit();

        expect(component.payloadDays).toHaveLength(7);
        expect(component.openRecurrencePopup).toHaveBeenCalled();
      });

      it('should clear payloadDays for non-Weekly/Daily recurrence', () => {
        component.craneEditRequest.patchValue({ recurrence: 'Monthly' });
        component.payloadDays = ['Monday', 'Tuesday']; // Pre-existing data

        component.onSubmit();

        expect(component.payloadDays).toEqual([]);
        expect(component.openRecurrencePopup).toHaveBeenCalled();
      });

      it('should set recurrenceEdited to true when recurrence changes', () => {
        component.previousRecurrence = 'Weekly';
        component.craneEditRequest.patchValue({ recurrence: 'Daily' });

        component.onSubmit();

        expect(component.recurrenceEdited).toBe(true);
      });

      it('should set recurrenceEdited to true when repeatEveryCount changes', () => {
        component.previousRepeatEveryCount = 1;
        component.craneEditRequest.patchValue({ repeatEveryCount: 2 });

        component.onSubmit();

        expect(component.recurrenceEdited).toBe(true);
      });

      it('should set recurrenceEdited to true when repeatEveryType changes', () => {
        component.previousRepeatEveryType = 'Day';
        component.craneEditRequest.patchValue({ repeatEveryType: 'Days' });

        component.onSubmit();

        expect(component.recurrenceEdited).toBe(true);
      });

      it('should set recurrenceEdited to true when days change', () => {
        component.previousDays = ['Monday'];
        component.craneEditRequest.patchValue({ recurrence: 'Weekly' });
        component.weekDays[0].checked = true; // Monday
        component.weekDays[1].checked = true; // Tuesday

        component.onSubmit();

        expect(component.recurrenceEdited).toBe(true);
      });

      it('should set recurrenceEdited to true when dateOfMonth changes', () => {
        component.previousDateOfMonth = '15';
        component.craneEditRequest.patchValue({ dateOfMonth: '20' });

        component.onSubmit();

        expect(component.recurrenceEdited).toBe(true);
      });

      it('should set recurrenceEdited to true when monthlyRepeatType changes', () => {
        component.previousMonthlyRepeatType = 'First Monday';
        component.craneEditRequest.patchValue({ monthlyRepeatType: 'Second Monday' });

        component.onSubmit();

        expect(component.recurrenceEdited).toBe(true);
      });

      it('should set recurrenceEdited to true when daysEdited is true', () => {
        component.daysEdited = true;

        component.onSubmit();

        expect(component.recurrenceEdited).toBe(true);
      });

      it('should set recurrenceEdited to false when nothing changes', () => {
        component.previousRecurrence = 'Weekly';
        component.previousRepeatEveryCount = 1;
        component.previousRepeatEveryType = 'Week';
        component.previousDays = [];
        component.previousDateOfMonth = null;
        component.previousMonthlyRepeatType = null;
        component.daysEdited = false;

        component.craneEditRequest.patchValue({
          recurrence: 'Weekly',
          repeatEveryCount: 1,
          repeatEveryType: 'Week',
          dateOfMonth: null,
          monthlyRepeatType: null
        });

        component.onSubmit();

        expect(component.recurrenceEdited).toBe(false);
      });
    });

    describe('openRecurrencePopup', () => {
      it('should open modal with correct configuration', () => {
        component.cancelRecurrenceTemplate = { template: 'mock' } as any;

        component.openRecurrencePopup();

        expect(mockBsModalService.show).toHaveBeenCalledWith(
          component.cancelRecurrenceTemplate,
          {
            keyboard: false,
            class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
          }
        );
      });
    });

    describe('recurrenceSubmit', () => {
      beforeEach(() => {
        component.modalRef2 = { hide: jest.fn() } as any;
        jest.spyOn(component, 'onEditSubmit').mockImplementation(() => {});
      });

      it('should hide modal when action is "no"', () => {
        component.recurrenceSubmit('no');

        expect(component.modalRef2.hide).toHaveBeenCalled();
        expect(component.onEditSubmit).not.toHaveBeenCalled();
      });

      it('should hide modal and call onEditSubmit when action is "yes"', () => {
        component.recurrenceSubmit('yes');

        expect(component.modalRef2.hide).toHaveBeenCalled();
        expect(component.onEditSubmit).toHaveBeenCalled();
      });

      it('should handle null modalRef2', () => {
        component.modalRef2 = null;

        expect(() => component.recurrenceSubmit('yes')).not.toThrow();
        expect(component.onEditSubmit).toHaveBeenCalled();
      });
    });

    describe('sortWeekDays', () => {
      it('should sort days in correct order', () => {
        const unsortedDays = ['Friday', 'Monday', 'Wednesday', 'Sunday'];

        const result = component.sortWeekDays(unsortedDays);

        expect(result).toEqual(['Sunday', 'Monday', 'Wednesday', 'Friday']);
      });

      it('should handle empty array', () => {
        const result = component.sortWeekDays([]);

        expect(result).toBeUndefined();
      });

      it('should handle single day', () => {
        const result = component.sortWeekDays(['Wednesday']);

        expect(result).toEqual(['Wednesday']);
      });

      it('should handle all days', () => {
        const allDays = ['Saturday', 'Tuesday', 'Thursday', 'Monday', 'Friday', 'Sunday', 'Wednesday'];

        const result = component.sortWeekDays(allDays);

        expect(result).toEqual(['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']);
      });
    });

    describe('formControlValueChanged', () => {
      it('should set up form control value change subscriptions', () => {
        const mockValueChanges = {
          subscribe: jest.fn()
        };
        const mockRepeatEveryTypeControl = {
          valueChanges: mockValueChanges
        };

        jest.spyOn(component.craneEditRequest, 'get').mockReturnValue(mockRepeatEveryTypeControl as any);

        component.formControlValueChanged();

        expect(mockValueChanges.subscribe).toHaveBeenCalled();
      });
    });

    describe('setRepeat', () => {
      beforeEach(() => {
        component.weekDays = [
          { value: 'Monday', checked: false, isDisabled: false },
          { value: 'Tuesday', checked: false, isDisabled: false },
          { value: 'Wednesday', checked: false, isDisabled: false },
          { value: 'Thursday', checked: false, isDisabled: false },
          { value: 'Friday', checked: false, isDisabled: false },
          { value: 'Saturday', checked: false, isDisabled: false },
          { value: 'Sunday', checked: false, isDisabled: false }
        ];
        component.checkform = component.craneEditRequest.get('days') as any;
        jest.spyOn(component, 'changeMonthlyRecurrence').mockImplementation(() => {});
        jest.spyOn(component, 'showMonthlyRecurrence').mockImplementation(() => {});
        jest.spyOn(component, 'occurMessage').mockImplementation(() => {});
      });

      it('should handle Daily recurrence with count > 1', () => {
        component.craneEditRequest.patchValue({
          recurrence: 'Daily',
          repeatEveryCount: 2
        });
        const data = { days: ['Monday', 'Wednesday', 'Friday'] };

        component.setRepeat(data);

        expect(component.isRepeatWithMultipleRecurrence).toBe(false);
        expect(component.isRepeatWithSingleRecurrence).toBe(false);
        expect(component.weekDays.every(day => day.checked)).toBe(true);
      });

      it('should handle Weekly recurrence with count > 1', () => {
        component.craneEditRequest.patchValue({
          recurrence: 'Weekly',
          repeatEveryCount: 2
        });
        const data = { days: ['Monday', 'Wednesday', 'Friday'] };

        component.setRepeat(data);

        expect(component.isRepeatWithMultipleRecurrence).toBe(true);
        expect(component.isRepeatWithSingleRecurrence).toBe(false);
        expect(component.weekDays.find(day => day.value === 'Monday').checked).toBe(true);
        expect(component.weekDays.find(day => day.value === 'Wednesday').checked).toBe(true);
        expect(component.weekDays.find(day => day.value === 'Friday').checked).toBe(true);
        expect(component.weekDays.find(day => day.value === 'Tuesday').checked).toBe(false);
      });

      it('should handle Weekly recurrence with count = 1', () => {
        component.craneEditRequest.patchValue({
          recurrence: 'Weekly',
          repeatEveryCount: 1
        });
        const data = { days: ['Monday', 'Friday'] };

        component.setRepeat(data);

        expect(component.isRepeatWithMultipleRecurrence).toBe(false);
        expect(component.isRepeatWithSingleRecurrence).toBe(true);
        expect(component.weekDays.find(day => day.value === 'Monday').checked).toBe(true);
        expect(component.weekDays.find(day => day.value === 'Friday').checked).toBe(true);
      });

      it('should handle Daily recurrence with count = 1', () => {
        component.craneEditRequest.patchValue({
          recurrence: 'Daily',
          repeatEveryCount: 1
        });
        const data = { days: [] };

        component.setRepeat(data);

        expect(component.isRepeatWithMultipleRecurrence).toBe(false);
        expect(component.isRepeatWithSingleRecurrence).toBe(true);
        expect(component.weekDays.every(day => day.checked)).toBe(true);
      });

      it('should handle Monthly recurrence with count = 1', () => {
        component.craneEditRequest.patchValue({
          recurrence: 'Monthly',
          repeatEveryCount: 1
        });
        const data = { days: [] };

        component.setRepeat(data);

        expect(component.isRepeatWithMultipleRecurrence).toBe(false);
        expect(component.isRepeatWithSingleRecurrence).toBe(true);
        expect(component.changeMonthlyRecurrence).toHaveBeenCalled();
        expect(component.showMonthlyRecurrence).toHaveBeenCalled();
      });

      it('should handle Yearly recurrence with count > 1', () => {
        component.craneEditRequest.patchValue({
          recurrence: 'Yearly',
          repeatEveryCount: 2
        });
        const data = { days: [] };

        component.setRepeat(data);

        expect(component.isRepeatWithMultipleRecurrence).toBe(true);
        expect(component.isRepeatWithSingleRecurrence).toBe(false);
        expect(component.changeMonthlyRecurrence).toHaveBeenCalled();
        expect(component.showMonthlyRecurrence).toHaveBeenCalled();
      });

      it('should call occurMessage at the end', () => {
        component.craneEditRequest.patchValue({
          recurrence: 'Daily',
          repeatEveryCount: 1
        });
        const data = { days: [] };

        component.setRepeat(data);

        expect(component.occurMessage).toHaveBeenCalled();
      });
    });
  });

  describe('Core Component Methods for Coverage', () => {
    beforeEach(() => {
      // Reset component state for tests
      component.weekDates = [];
      component.availableTimes = [];
      component.selectedDate = null;
      component.timeZone = 'America/New_York';
      component.selectedMinutes = 60;
      component.selectedHour = 2;
      component.selectedMinute = 30;
      component.durationisOpen = false;
      component.isSlotsNull = false;
      component.isAM = true;
      component.deliveryWindowTime = 24;
      component.deliveryWindowTimeUnit = 'hours';
    });

    describe('Time and Date Management Methods', () => {
      describe('generateWeekDates', () => {
        it('should generate 5 consecutive dates starting from selected date', () => {
          const selectedDate = '2024-01-15';

          component.generateWeekDates(selectedDate);

          expect(component.weekDates).toHaveLength(5);
          expect(component.weekDates[0].fullDate).toBe('2024-01-15');
          expect(component.weekDates[4].fullDate).toBe('2024-01-19');
          expect(component.selectedDate).toEqual(component.weekDates[0]);
        });

        it('should handle Date object input', () => {
          const selectedDate = new Date('2024-01-15');

          component.generateWeekDates(selectedDate);

          expect(component.weekDates).toHaveLength(5);
          expect(component.weekDates[0].fullDate).toBe('2024-01-15');
        });

        it('should format day names and dates correctly', () => {
          const selectedDate = '2024-01-15'; // Monday

          component.generateWeekDates(selectedDate);

          expect(component.weekDates[0].name).toBe('Mon');
          expect(component.weekDates[0].date).toBe('15');
          expect(component.weekDates[1].name).toBe('Tue');
          expect(component.weekDates[1].date).toBe('16');
        });
      });

      describe('selectDate', () => {
        it('should set selected date and update form', () => {
          const day = {
            name: 'Wed',
            date: '17',
            fullDate: '2024-01-17'
          };

          component.selectDate(day);

          expect(component.selectedDate).toEqual(day);
          expect(component.craneEditRequest.get('deliveryDate').value).toBe('01/17/2024');
        });
      });

      describe('updateWeeklyDates', () => {
        beforeEach(() => {
          component.weekDates = [
            { name: 'Mon', date: '15', fullDate: '2024-01-15' }
          ];
        });

        it('should add new dates up to end date', () => {
          component.craneEditRequest.patchValue({ endDate: '2024-01-18' });

          component.updateWeeklyDates();

          expect(component.weekDates.length).toBeGreaterThan(1);
          expect(component.weekDates.some(date => date.fullDate === '2024-01-18')).toBe(true);
        });

        it('should not add duplicate dates', () => {
          component.craneEditRequest.patchValue({ endDate: '2024-01-15' });
          const initialLength = component.weekDates.length;

          component.updateWeeklyDates();

          expect(component.weekDates.length).toBe(initialLength);
        });

        it('should handle empty weekDates array', () => {
          component.weekDates = [];
          component.craneEditRequest.patchValue({ endDate: '2024-01-18' });

          component.updateWeeklyDates();

          expect(component.weekDates.length).toBeGreaterThan(0);
        });

        it('should handle null end date', () => {
          component.craneEditRequest.patchValue({ endDate: null });

          expect(() => component.updateWeeklyDates()).not.toThrow();
        });
      });

      describe('selectTime', () => {
        it('should update form with selected time range', () => {
          const startStr = '2024-01-15T09:00:00Z';
          const endStr = '2024-01-15T11:00:00Z';

          component.selectTime(startStr, endStr);

          expect(component.craneEditRequest.get('craneDeliveryStart').value).toEqual(new Date(startStr));
          expect(component.craneEditRequest.get('craneDeliveryEnd').value).toEqual(new Date(endStr));
          expect(component.craneEditRequest.get('deliveryDate').value).toEqual(new Date(startStr));
          expect(component.selectedTime).toContain('9:00:00 AM');
          expect(component.NDRTimingChanged).toBe(true);
        });
      });

      describe('scrollToTime', () => {
        beforeEach(() => {
          // Mock the timeSlotsContainer
          const mockContainer = {
            nativeElement: {
              querySelectorAll: jest.fn().mockReturnValue([
                { offsetTop: 100 },
                { offsetTop: 200 },
                { offsetTop: 300 }
              ]),
              offsetTop: 50,
              scrollTop: 0
            }
          };
          component.timeSlotsContainer = mockContainer as any;
        });

        it('should scroll to the selected time button', () => {
          component.scrollToTime(1);

          expect(component.timeSlotsContainer.nativeElement.scrollTop).toBe(150); // 200 - 50
        });

        it('should handle invalid index gracefully', () => {
          expect(() => component.scrollToTime(10)).not.toThrow();
        });
      });
    });

    describe('Duration and Time Selection Methods', () => {
      describe('selectHour', () => {
        beforeEach(() => {
          component.craneEditRequest.patchValue({
            craneDeliveryStart: new Date('2024-01-15T09:00:00')
          });
          jest.spyOn(component, 'durationCloseDropdown').mockImplementation(() => {});
          jest.spyOn(component, 'updateDropdownState').mockImplementation(() => {});
          jest.spyOn(component, 'selectDuration').mockImplementation(() => {});
        });

        it('should calculate and set end time based on selected hour', () => {
          component.selectHour(2);

          expect(component.selectedHour).toBe(2);
          expect(component.deliveryEnd.getTime()).toBeGreaterThan(component.deliveryStart.getTime());
          expect(component.durationCloseDropdown).toHaveBeenCalled();
          expect(component.updateDropdownState).toHaveBeenCalled();
          expect(component.selectDuration).toHaveBeenCalledWith(2);
        });

        it('should handle zero hour selection', () => {
          component.selectHour(0);

          expect(component.selectedHour).toBe(0);
          expect(component.deliveryEnd).toBeDefined();
        });
      });

      describe('selectMinute', () => {
        beforeEach(() => {
          component.craneEditRequest.patchValue({
            craneDeliveryStart: new Date('2024-01-15T09:00:00')
          });
          component.selectedHour = 1;
          jest.spyOn(component, 'durationCloseDropdown').mockImplementation(() => {});
          jest.spyOn(component, 'updateDropdownState').mockImplementation(() => {});
          jest.spyOn(component, 'selectDuration').mockImplementation(() => {});
        });

        it('should calculate and set end time based on selected minute', () => {
          component.selectMinute(45);

          expect(component.selectedMinute).toBe(45);
          expect(component.deliveryEnd.getTime()).toBeGreaterThan(component.deliveryStart.getTime());
          expect(component.durationCloseDropdown).toHaveBeenCalled();
          expect(component.updateDropdownState).toHaveBeenCalled();
          expect(component.selectDuration).toHaveBeenCalledWith(45);
        });
      });

      describe('selectDuration', () => {
        beforeEach(() => {
          component.selectedHour = 2;
          component.selectedMinute = 30;
        });

        it('should calculate total minutes correctly', () => {
          component.selectDuration(null);

          expect(component.selectedMinutes).toBe(150); // (2 * 60) + 30
        });
      });

      describe('durationCloseDropdown', () => {
        it('should close the duration dropdown', () => {
          component.durationisOpen = true;

          component.durationCloseDropdown();

          expect(component.durationisOpen).toBe(false);
        });
      });

      describe('durationToggleDropdown', () => {
        it('should toggle dropdown state from closed to open', () => {
          component.durationisOpen = false;

          component.durationToggleDropdown();

          expect(component.durationisOpen).toBe(true);
        });

        it('should toggle dropdown state from open to closed', () => {
          component.durationisOpen = true;

          component.durationToggleDropdown();

          expect(component.durationisOpen).toBe(false);
        });
      });

      describe('updateDropdownState', () => {
        it('should close dropdown when both hour and minutes are selected', () => {
          component.selectedHour = 2;
          component.selectedMinutes = 120;
          component.durationisOpen = true;

          component.updateDropdownState();

          expect(component.durationisOpen).toBe(false);
        });

        it('should keep dropdown open when hour is null', () => {
          component.selectedHour = null;
          component.selectedMinutes = 120;
          component.durationisOpen = true;

          component.updateDropdownState();

          expect(component.durationisOpen).toBe(true);
        });

        it('should keep dropdown open when minutes is null', () => {
          component.selectedHour = 2;
          component.selectedMinutes = null;
          component.durationisOpen = true;

          component.updateDropdownState();

          expect(component.durationisOpen).toBe(true);
        });
      });
    });

    describe('Available Slots and Booking Methods', () => {
      describe('getAvailableSlots', () => {
        beforeEach(() => {
          component.craneEditRequest.patchValue({
            EquipmentId: [{ id: 1 }, { id: 2 }],
            LocationId: [{ id: 10 }]
          });
          component.timeZone = 'America/New_York';
          component.selectedMinutes = 60;
          component.ProjectId = 'test-project';
        });

        it('should handle empty equipment selection', () => {
          component.craneEditRequest.patchValue({ EquipmentId: null });

          component.getAvailableSlots('2024-01-15');

          // Should not call service when no equipment selected
          expect(component.availableTimes).toEqual([]);
        });

        it('should handle empty equipment array', () => {
          component.craneEditRequest.patchValue({ EquipmentId: [] });

          component.getAvailableSlots('2024-01-15');

          // Should not call service when equipment array is empty
          expect(component.availableTimes).toEqual([]);
        });
      });

      describe('getBookingData', () => {
        beforeEach(() => {
          component.craneEditRequest.patchValue({
            EquipmentId: [{ id: 1 }],
            LocationId: [{ id: 10 }],
            GateId: 5
          });
          component.timeZone = 'America/New_York';
          component.timeSlotComponent = {
            getEventNDR: jest.fn()
          } as any;
        });

        it('should call timeSlotComponent.getEventNDR with correct parameters', () => {
          component.getBookingData();

          expect(component.timeSlotComponent.getEventNDR).toHaveBeenCalledWith(
            [{ id: 1 }],
            10,
            5,
            'America/New_York',
            ''
          );
        });

        it('should handle missing timeSlotComponent', () => {
          component.timeSlotComponent = null;

          expect(() => component.getBookingData()).not.toThrow();
        });
      });
    });

    describe('Location and Equipment Management', () => {
      describe('locationSelected', () => {
        beforeEach(() => {
          component.locationList = [
            {
              id: 1,
              gateDetails: [{ id: 1, name: 'Gate 1' }],
              EquipmentId: [
                { id: 1, equipmentName: 'Crane 1' },
                { id: 2, equipmentName: 'Crane 2' }
              ],
              TimeZoneId: [{ location: 'America/New_York' }]
            },
            {
              id: 2,
              gateDetails: [{ id: 2, name: 'Gate 2' }],
              EquipmentId: null,
              TimeZoneId: [{ location: 'America/Los_Angeles' }]
            }
          ];
        });

        it('should update gates and equipment when location is selected', () => {
          const data = { id: 1 };

          component.locationSelected(data);

          expect(component.gateList).toEqual([{ id: 1, name: 'Gate 1' }]);
          expect(component.equipmentList).toHaveLength(2);
          expect(component.timeZone).toBe('America/New_York');
          expect(component.selectedLocationId).toBe(1);
        });

        it('should handle location with no equipment', () => {
          const data = { id: 2 };

          component.locationSelected(data);

          expect(component.equipmentList).toEqual([]);
          expect(component.craneEditRequest.get('EquipmentId').value).toEqual([]);
        });

        it('should handle location with null EquipmentId', () => {
          component.locationList[0].EquipmentId = null;
          const data = { id: 1 };

          component.locationSelected(data);

          expect(component.equipmentList).toEqual([]);
        });
      });

      describe('setlocation', () => {
        beforeEach(() => {
          component.currentEditItem = {
            location: {
              id: 1,
              locationPath: 'Building A / Floor 1',
              TimeZoneId: [{ location: 'America/New_York' }]
            }
          };
        });

        it('should set location data from currentEditItem', () => {
          component.setlocation();

          expect(component.getChosenLocation).toHaveLength(1);
          expect(component.getChosenLocation[0].id).toBe(1);
          expect(component.getChosenLocation[0].locationPath).toBe('Building A / Floor 1');
          expect(component.timeZone).toBe('America/New_York');
          expect(component.selectedLocationId).toBe(1);
        });

        it('should handle missing location in currentEditItem', () => {
          component.currentEditItem = {};

          expect(() => component.setlocation()).not.toThrow();
        });

        it('should handle location without TimeZoneId', () => {
          component.currentEditItem.location.TimeZoneId = null;

          component.setlocation();

          expect(component.timeZone).toBe('');
        });
      });
    });

    describe('Form Validation and Error Handling', () => {
      describe('checkEditDeliveryFutureDate', () => {
        beforeEach(() => {
          component.deliveryWindowTime = 24;
          component.deliveryWindowTimeUnit = 'hours';
        });

        it('should return true for future dates', () => {
          const futureStart = moment().add(2, 'days').toDate();
          const futureEnd = moment().add(2, 'days').add(2, 'hours').toDate();

          const result = component.checkEditDeliveryFutureDate(futureStart, futureEnd);

          expect(result).toBe(true);
        });

        it('should return false for past dates', () => {
          const pastStart = moment().subtract(1, 'day').toDate();
          const pastEnd = moment().subtract(1, 'day').add(2, 'hours').toDate();

          const result = component.checkEditDeliveryFutureDate(pastStart, pastEnd);

          expect(result).toBe(false);
        });

        it('should handle string date inputs', () => {
          const futureStart = moment().add(2, 'days').toISOString();
          const futureEnd = moment().add(2, 'days').add(2, 'hours').toISOString();

          const result = component.checkEditDeliveryFutureDate(futureStart, futureEnd);

          expect(result).toBe(true);
        });
      });

      describe('convertStart', () => {
        it('should convert date and time components to new Date', () => {
          const deliveryDate = new Date('2024-01-15');
          const startHours = 14;
          const startMinutes = 30;

          const result = component.convertStart(deliveryDate, startHours, startMinutes);

          expect(result.getFullYear()).toBe(2024);
          expect(result.getMonth()).toBe(0); // January is 0
          expect(result.getDate()).toBe(15);
          expect(result.getHours()).toBe(14);
          expect(result.getMinutes()).toBe(30);
        });

        it('should handle edge case times', () => {
          const deliveryDate = new Date('2024-12-31');
          const startHours = 23;
          const startMinutes = 59;

          const result = component.convertStart(deliveryDate, startHours, startMinutes);

          expect(result.getHours()).toBe(23);
          expect(result.getMinutes()).toBe(59);
        });
      });

      describe('gatecheck', () => {
        beforeEach(() => {
          component.memberList = [
            { id: 1, name: 'John Doe' },
            { id: 2, name: 'Jane Smith' },
            { id: 3, name: 'Bob Johnson' }
          ];
        });

        it('should set errmemberenable to false when all members exist', () => {
          const value = [{ id: 1 }, { id: 2 }];

          component.gatecheck(value, 'Person');

          expect(component.errmemberenable).toBe(false);
        });

        it('should set errmemberenable to true when some members do not exist', () => {
          const value = [{ id: 1 }, { id: 99 }]; // id 99 doesn't exist

          component.gatecheck(value, 'Person');

          expect(component.errmemberenable).toBe(true);
        });

        it('should only process Person menuname', () => {
          const value = [{ id: 99 }];
          component.errmemberenable = false;

          component.gatecheck(value, 'Equipment');

          expect(component.errmemberenable).toBe(false); // Should not change
        });
      });

      describe('getIndexValue', () => {
        beforeEach(() => {
          component.memberList = [
            { id: 1, name: 'John Doe' },
            { id: 2, name: 'Jane Smith' }
          ];
        });

        it('should return 0 when all responsible persons exist in member list', () => {
          const formValue = {
            responsiblePersons: [{ id: 1 }, { id: 2 }]
          };

          const result = component.getIndexValue(formValue);

          expect(result).toBe(0);
        });

        it('should return -1 when some responsible persons do not exist', () => {
          const formValue = {
            responsiblePersons: [{ id: 1 }, { id: 99 }]
          };

          const result = component.getIndexValue(formValue);

          expect(result).toBe(-1);
        });

        it('should handle empty responsible persons array', () => {
          const formValue = {
            responsiblePersons: []
          };

          const result = component.getIndexValue(formValue);

          expect(result).toBe(0);
        });
      });
    });
  });

  describe('Critical Methods for 90% Coverage', () => {
    beforeEach(() => {
      // Setup for critical method testing
      component.authUser = { RoleId: 1, User: { email: '<EMAIL>' } };
      component.currentEditItem = {
        status: 'Pending',
        craneDeliveryStart: '2024-01-15T09:00:00Z',
        craneDeliveryEnd: '2024-01-15T11:00:00Z',
        deliveryDate: '2024-01-15'
      };
      component.deliveryWindowTime = 24;
      component.deliveryWindowTimeUnit = 'hours';
      component.memberList = [
        { id: 1, name: 'John Doe' },
        { id: 2, name: 'Jane Smith' }
      ];
    });

    describe('onEditSubmit - Complex Submission Logic', () => {
      beforeEach(() => {
        component.craneEditRequest.patchValue({
          EquipmentId: [{ id: 1 }],
          deliveryDate: '01/15/2024',
          craneDeliveryStart: new Date('2024-01-15T09:00:00'),
          craneDeliveryEnd: new Date('2024-01-15T11:00:00'),
          responsiblePersons: [{ id: 1 }, { id: 2 }]
        });
        jest.spyOn(component, 'checkEditDeliveryFutureDate').mockReturnValue(true);
        jest.spyOn(component, 'getIndexValue').mockReturnValue(0);
        jest.spyOn(component, 'checkRequestStatusAndUserRole').mockImplementation(() => {});
      });

      it('should handle successful submission with future dates', () => {
        component.onEditSubmit();

        expect(component.editSubmitted).toBe(true);
        expect(component.checkRequestStatusAndUserRole).toHaveBeenCalled();
      });

      it('should handle submission with past dates', () => {
        component.checkEditDeliveryFutureDate = jest.fn().mockReturnValue(false);

        component.onEditSubmit();

        expect(component.checkRequestStatusAndUserRole).toHaveBeenCalled();
      });

      it('should fail when equipment is not selected', () => {
        component.craneEditRequest.patchValue({ EquipmentId: [] });

        component.onEditSubmit();

        expect(mockToastrService.error).toHaveBeenCalledWith('Equipment is required');
        expect(component.formEditSubmitted).toBe(false);
      });

      it('should fail when form is invalid', () => {
        component.craneEditRequest.patchValue({ description: '' }); // Required field

        component.onEditSubmit();

        expect(component.formEditSubmitted).toBe(false);
      });

      it('should handle member validation failure', () => {
        component.getIndexValue = jest.fn().mockReturnValue(-1);

        component.onEditSubmit();

        expect(component.errmemberenable).toBe(true);
        expect(component.formEditSubmitted).toBe(false);
      });
    });

    describe('checkRequestStatusAndUserRole - Authorization Logic', () => {
      beforeEach(() => {
        const mockParams = {
          formValue: component.craneEditRequest.value,
          deliveryStart: new Date('2024-01-15T09:00:00'),
          deliveryEnd: new Date('2024-01-15T11:00:00'),
          companies: [],
          persons: [],
          define: [],
          equipments: [],
          gates: []
        };
        jest.spyOn(component, 'updateCraneDelivery').mockImplementation(() => {});
        jest.spyOn(component, 'throwError').mockImplementation(() => {});
        jest.spyOn(component, 'checkEditDeliveryFutureDate').mockReturnValue(false);
        component.craneEditRequest.patchValue({
          deliveryDate: '01/15/2024',
          craneDeliveryStart: new Date('2024-01-15T09:00:00'),
          craneDeliveryEnd: new Date('2024-01-15T11:00:00')
        });
      });

      it('should prevent date/time changes for completed requests by non-admin users', () => {
        component.currentEditItem.status = 'Completed';
        component.authUser.RoleId = 1; // Non-admin
        component.craneEditRequest.patchValue({
          deliveryDate: '01/16/2024' // Different date
        });

        const params = {
          formValue: component.craneEditRequest.value,
          deliveryStart: new Date('2024-01-16T09:00:00'),
          deliveryEnd: new Date('2024-01-16T11:00:00'),
          companies: [],
          persons: [],
          define: [],
          equipments: [],
          gates: []
        };

        component.checkRequestStatusAndUserRole(params);

        expect(mockToastrService.error).toHaveBeenCalledWith('You are not allowed to change the date/time ');
        expect(component.formEditSubmitted).toBe(false);
      });

      it('should allow date/time changes for completed requests by admin users', () => {
        component.currentEditItem.status = 'Completed';
        component.authUser.RoleId = 2; // Admin
        component.craneEditRequest.patchValue({
          deliveryDate: '01/16/2024'
        });

        const params = {
          formValue: component.craneEditRequest.value,
          deliveryStart: new Date('2024-01-16T09:00:00'),
          deliveryEnd: new Date('2024-01-16T11:00:00'),
          companies: [],
          persons: [],
          define: [],
          equipments: [],
          gates: []
        };

        component.checkRequestStatusAndUserRole(params);

        expect(component.updateCraneDelivery).toHaveBeenCalled();
      });

      it('should handle approved requests with past dates for non-admin users', () => {
        component.currentEditItem.status = 'Approved';
        component.authUser.RoleId = 1; // Non-admin
        component.currentEditItem.deliveryDate = '2024-01-14'; // Different date
        component.checkEditDeliveryFutureDate = jest.fn().mockReturnValue(false);

        const params = {
          formValue: component.craneEditRequest.value,
          deliveryStart: new Date('2024-01-15T09:00:00'),
          deliveryEnd: new Date('2024-01-15T11:00:00'),
          companies: [],
          persons: [],
          define: [],
          equipments: [],
          gates: []
        };

        component.checkRequestStatusAndUserRole(params);

        expect(component.throwError).toHaveBeenCalledWith('error');
        expect(component.formEditSubmitted).toBe(false);
      });

      it('should allow changes for approved requests with future dates', () => {
        component.currentEditItem.status = 'Approved';
        component.authUser.RoleId = 1; // Non-admin
        component.currentEditItem.deliveryDate = '2024-01-14'; // Different date
        component.checkEditDeliveryFutureDate = jest.fn().mockReturnValue(true);

        const params = {
          formValue: component.craneEditRequest.value,
          deliveryStart: new Date('2024-01-15T09:00:00'),
          deliveryEnd: new Date('2024-01-15T11:00:00'),
          companies: [],
          persons: [],
          define: [],
          equipments: [],
          gates: []
        };

        component.checkRequestStatusAndUserRole(params);

        expect(component.updateCraneDelivery).toHaveBeenCalled();
      });
    });

    describe('Data Setting Methods', () => {
      describe('setEquipment', () => {
        beforeEach(() => {
          component.currentEditItem = {
            equipmentDetails: [
              { Equipment: { id: 1, equipmentName: 'Crane A' } },
              { Equipment: { id: 2, equipmentName: 'Crane B' } }
            ]
          };
        });

        it('should set equipment data from currentEditItem', () => {
          component.setEquipment();

          const expectedEquipment = [
            { id: 1, equipmentName: 'Crane A' },
            { id: 2, equipmentName: 'Crane B' }
          ];
          expect(component.editBeforeEquipment).toEqual(expectedEquipment);
          expect(component.craneEditRequest.get('EquipmentId').value).toEqual(expectedEquipment);
        });

        it('should handle undefined equipmentDetails', () => {
          component.currentEditItem = {};

          expect(() => component.setEquipment()).not.toThrow();
        });
      });

      describe('setMember', () => {
        beforeEach(() => {
          component.currentEditItem = {
            memberDetails: [
              {
                Member: {
                  id: 1,
                  isGuestUser: false,
                  User: { firstName: 'John', lastName: 'Doe', email: '<EMAIL>' }
                }
              },
              {
                Member: {
                  id: 2,
                  isGuestUser: true,
                  User: { firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>' }
                }
              }
            ]
          };
          component.authUser = { User: { email: '<EMAIL>' } };
        });

        it('should handle members without firstName', () => {
          component.currentEditItem.memberDetails[0].Member.User.firstName = null;

          // Test the member processing logic without service call
          const member = component.currentEditItem.memberDetails[0].Member;
          const expectedEmail = `(${member.User.email})`;

          expect(expectedEmail).toBe('(<EMAIL>)');
        });

        it('should handle guest users correctly', () => {
          const guestMember = component.currentEditItem.memberDetails[1].Member;
          const expectedEmail = `${guestMember.User.firstName} ${guestMember.User.lastName} (${guestMember.User.email} - Guest)`;

          expect(expectedEmail).toBe('Jane Smith (<EMAIL> - Guest)');
        });
      });
    });

    describe('Lifecycle and Initialization Methods', () => {
      describe('ngAfterViewInit', () => {
        beforeEach(() => {
          component.modalRef = {
            content: { seriesOption: 2 }
          } as any;
          component.seriesoption = 1;
          mockDeliveryService.EditCraneRequestId = new BehaviorSubject('test-id');
          mockDeliveryService.loginUser = new BehaviorSubject({ id: 1, name: 'Test User' });
          jest.spyOn(component, 'getOverAllGate').mockImplementation(() => {});
        });

        it('should initialize with modal series option', () => {
          component.ngAfterViewInit();

          expect(component.seriesOption).toBe(2);
          expect(component.isDisabledDate).toBe(true);
        });

        it('should fallback to component series option when modal is undefined', () => {
          component.modalRef = null;

          component.ngAfterViewInit();

          expect(component.seriesOption).toBe(1);
          expect(component.isDisabledDate).toBe(false);
        });

        it('should handle EditCraneRequestId subscription', () => {
          component.ngAfterViewInit();

          expect(component.craneRequestId).toBe('test-id');
          expect(component.getOverAllGate).toHaveBeenCalled();
        });

        it('should handle loginUser subscription', () => {
          component.ngAfterViewInit();

          expect(component.authUser).toEqual({ id: 1, name: 'Test User' });
        });

        it('should handle undefined/null/empty loginUser', () => {
          mockDeliveryService.loginUser.next(undefined);

          component.ngAfterViewInit();

          expect(component.authUser).not.toEqual(undefined);
        });
      });

      describe('getProjectSettings', () => {
        it('should not call service when ProjectId is not set', () => {
          component.ProjectId = null;

          component.getProjectSettings();

          // Should not make service call when ProjectId is null
          expect(component.ProjectId).toBeNull();
        });

        it('should have default delivery window settings', () => {
          expect(component.deliveryWindowTime).toBeDefined();
          expect(component.deliveryWindowTimeUnit).toBeDefined();
        });
      });

      describe('openContentModal', () => {
        it('should set modalLoader to false', () => {
          component.modalLoader = true;

          component.openContentModal();

          expect(component.modalLoader).toBe(false);
        });
      });
    });
  });
});
