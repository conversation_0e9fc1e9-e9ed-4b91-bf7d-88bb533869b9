/* eslint-disable no-underscore-dangle */
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Component, TemplateRef, ViewChild } from '@angular/core';
import { FullCalendarComponent } from '@fullcalendar/angular';
import { Calendar, CalendarOptions } from '@fullcalendar/core';
import interactionPlugin from '@fullcalendar/interaction';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import moment from 'moment';
import { Router } from '@angular/router';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { SchedulerFormComponent } from '../scheduler-form/scheduler-form.component';
import { ReportsService } from '../../services/reports/reports.service';
import { SaveReportFormComponent } from '../save-report-form/save-report-form.component';

@Component({
  selector: 'app-weeklycalendar',
  templateUrl: './weekly-calendar.component.html',
  })
export class WeeklycalendarComponent {
  public modalRef: BsModalRef;

  public scheduleFormModalRef: BsModalRef;

  public currentView = 'Week';

  public calendarApi;

  public Range: any = {};

  public calendar: Calendar;

  public events: any = [];

  public ProjectId: any;

  public ParentCompanyId;

  public pageSize = 25;

  public pageNo = 1;

  public deliveryList: any = [];

  public currentViewMonth: moment.MomentInput;

  public eventData: any = {};

  public calendarCurrentDeliveryIndex = -1;

  public descriptionPopup = false;

  public deliveryRequestWithCraneEquipmentType: any[];

  public loader = true;

  public isKeepOpen: boolean = false;

  public wholeStatus = ['Approved', 'Declined', 'Delivered', 'Pending', 'Expired'];

  public memberList: any = [];

  public filterForm: UntypedFormGroup;

  public formSubmitted = false;

  public filterCount = 0;

  public gateList: any = [];

  public equipmentList: any = [];

  public companyList: any = [];

  public defineList: any = [];

  public deliveryStart: Date;

  public deliveryEnd: Date;

  public newRequestTypeDropdownSettings: IDropdownSettings;

  public dfowDropdownSettings: IDropdownSettings;

  public equipmentDropdownSettings: IDropdownSettings;

  public responsiblepersonDropdownSettings: IDropdownSettings;

  public responsiblecompanyDropdownSettings: IDropdownSettings;

  public gateDropdownSettings: IDropdownSettings;

  public statusDropdownSettings: IDropdownSettings;

  public defineListData: any;

  public defaultRequestType;

  public exportForm: UntypedFormGroup;

  public exportSubmitted = false;

  public reportType = 'EXCEL';

  public reportName = 'Weekly Calendar Report';

  public selectedArray = [];

  public updatedMemberList: any = [];

  public exportType = ['EXCEL', 'PDF'];

  public scheduleData = {};

  public locationList: any = [];

  public locationDropdownSettings: IDropdownSettings;

  @ViewChild('fullcalendar', { static: true }) public calendarComponent1: FullCalendarComponent;

  public calendarOptions: CalendarOptions = {
    selectable: true,
    initialView: 'timeGridWeek',
    plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin],
    customButtons: {
      prev: {
        text: 'PREV',
        click: (): void => this.goPrev(),
      },
      next: {
        text: 'Next',
        click: (): void => this.goNext(),
      },
      prevYear: {
        text: 'PREV',
        click: (): void => this.goPrevYear(),
      },
      nextYear: {
        text: 'Next',
        click: (): void => this.goNextYear(),
      },
      timeGridWeek: {
        text: 'Week',
        click: (): void => this.goTimeGridWeekOrDay('timeGridWeek'),
      },
      timeGridDay: {
        text: 'Day',
        click: (): void => this.goTimeGridWeekOrDay('timeGridDay'),
      },
      dayGridMonth: {
        text: 'Month',
        click: (): void => this.goDayGridMonth(),
      },
    },
    showNonCurrentDates: false,
    headerToolbar: {
      right: '',
      center: 'prevYear,prev,title,next,nextYear',
      left: '',
    },
    aspectRatio: 2,
    slotEventOverlap: false,
    contentHeight: 'liquid',
    fixedWeekCount: false,
    expandRows: true,
    nowIndicator: true,
    moreLinkClick: 'popover',
    firstDay: 0,
    events: this.events,
    eventTimeFormat: {
      hour: 'numeric',
      minute: '2-digit',
      meridiem: 'short',
    },
    eventClick: (arg): void => {
      this.eventData = [];
    },
    datesSet: (info): void => {
      this.currentViewMonth = info.view.title;
    },
    eventDidMount(info): void {
      const argument = info;
      const deliveryRequestDescription = argument.event._def.extendedProps.description;
      const fromTimeDuration = argument.event._def.extendedProps.fromTime;
      const toTimeDuration = argument.event._def.extendedProps.toTime;
      const requestType1 = argument.event._def.extendedProps.listType;
      if (requestType1 === 'deliveryRequest' || requestType1 === 'deliveryRequestWithCrane') {
        argument.el.querySelector('.fc-event-title').innerHTML = `
          <div class="d-flex flex-column">
          <p class="m-0"> <img src="./assets/images/sidemenu-icons/delivery.svg"  style="color:green;"class="w10 h10" alt="image"/> ${deliveryRequestDescription}   </p>
          <small class="text-wrap"> ${fromTimeDuration}  - ${toTimeDuration}</small>
          </div>`;
      }
      if (requestType1 === 'craneRequest' || requestType1 === 'CraneEquipment') {
        argument.el.querySelector('.fc-event-title').innerHTML = `
          <div class="d-flex flex-column">
          <p class="m-0">  <img src="./assets/images/cranesvg.svg" class="w10 h10" alt="image"/> ${deliveryRequestDescription}  </p>
          <small class="text-wrap"> ${fromTimeDuration}  - ${toTimeDuration}</small>
          </div>`;
      }
      if (requestType1 === 'concreteRequest') {
        argument.el.querySelector('.fc-event-title').innerHTML = `
          <div class="d-flex flex-column">
          <p class="m-0"><img src="./assets/images/sidemenu-icons/concretesvg.svg" class="w10 h10" alt="image"/>  ${deliveryRequestDescription} </p>
          <small class="text-wrap"> ${fromTimeDuration}  - ${toTimeDuration}</small>
          </div>`;
      }
      if (argument.event._def.allDay) {
        argument.el.querySelector('.fc-event-title').innerHTML = `
        <div class="d-flex flex-column">
        <p class="m-0"> <img class="w10 h10" src="./assets/images/noun-event-alert.svg" class="form-icon" alt="allday" >  ${deliveryRequestDescription} </p>
        <small class="text-wrap"> ${fromTimeDuration}  - ${toTimeDuration}</small>
        </div>`;
      }
      if (requestType1 === 'calendarEvent') {
        argument.el.querySelector('.fc-event-title').innerHTML = `
        <div class="d-flex flex-column">
        <p class="m-0"> <img class="w10 h10" src="./assets/images/sidemenu-icons/calendarsettings.svg" class="form-icon" alt="allday" > ${deliveryRequestDescription}  </p>
        <small class="text-wrap"> ${fromTimeDuration}  - ${toTimeDuration}</small>
        </div>`;
      }
      if (requestType1 === 'inspectionRequest' || requestType1 === 'inspectionRequestWithCrane') {
        argument.el.querySelector('.fc-event-title').innerHTML = `
          <div class="d-flex flex-column">
          <p class="m-0"> <img src="./assets/images/sidemenu-icons/inspection2.png"  style="color:green;"class="w10 h10" alt="image"/> ${deliveryRequestDescription}   </p>
          <small class="text-wrap"> ${fromTimeDuration}  - ${toTimeDuration}</small>
          </div>`;
      }
    },
    dayMaxEventRows: true,
    views: {
      timeGridWeek: {
        dayMaxEventRows: 2,
        allDaySlot: true,
      },
      dayGridMonth: {
        allDaySlot: true,
      },
      timeGridDay: {
        allDaySlot: true,
      },
    },
  };

  public filterPayload: {
    start: string;
    end: string;
    companyFilter: number;
    startDate: any;
    endDate: any;
    statusFilter: any;
    memberFilter: number;
    locationFilter: number;
    gateFilter: number;
    equipmentFilter: number;
    templateType: any;
    defineFilter: number;
    startTime: any;
    endTime: any;
    ParentCompanyId: any;
    eventStartTime: string;
    eventEndTime: string;
    currentViewMonth: string;
    timezone: string;
    isDST: boolean;
  };

  public constructor(
    private readonly modalService: BsModalService,
    public projectService: ProjectService,
    private readonly deliveryService: DeliveryService,
    public router: Router,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly toastr: ToastrService,
    private readonly reportsService: ReportsService,
  ) {
    this.filterDetailsForm();
    this.exportDetailsForm();
    this.setDefaultDateAndTime();
    this.setDefaultDateRangePicker();
    this.defineListData = [
      { id: 0, name: 'Delivery' },
      { id: 1, name: 'Crane' },
      { id: 2, name: 'Concrete' },
      { id: 3, name: 'Calendar Events' },
      { id: 4, name: 'Inspection' },
    ];
    this.defaultRequestType = this.defineListData.filter(
      (a: { name: any }): any => a.name === 'Delivery',
    );
    this.defaultFilterSettings();
    this.setDropdownSettings();
  }

  public setDropdownSettings(): void {
    this.newRequestTypeDropdownSettings = {
      singleSelection: false,
      idField: 'id',
      textField: 'name',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 6,
      allowSearchFilter: false,
    };
    this.dfowDropdownSettings = {
      singleSelection: true,
      idField: 'id',
      textField: 'DFOW',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 6,
      allowSearchFilter: false,
      closeDropDownOnSelection: true,
    };
    this.equipmentDropdownSettings = {
      singleSelection: true,
      idField: 'id',
      textField: 'equipmentName',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 6,
      allowSearchFilter: false,
      closeDropDownOnSelection: true,
    };
    this.responsiblepersonDropdownSettings = {
      singleSelection: true,
      idField: 'id',
      textField: 'name',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 6,
      allowSearchFilter: false,
      closeDropDownOnSelection: true,
    };
    this.responsiblecompanyDropdownSettings = {
      singleSelection: true,
      idField: 'id',
      textField: 'companyName',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 6,
      allowSearchFilter: false,
      closeDropDownOnSelection: true,
    };
    this.gateDropdownSettings = {
      singleSelection: true,
      idField: 'id',
      textField: 'gateName',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 6,
      allowSearchFilter: false,
      closeDropDownOnSelection: true,
    };
    this.statusDropdownSettings = {
      singleSelection: true,
      idField: 'id',
      textField: 'STATUS',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 6,
      allowSearchFilter: false,
      closeDropDownOnSelection: true,
    };
    this.locationDropdownSettings = {
      singleSelection: true,
      idField: 'id',
      textField: 'locationPath',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 6,
      allowSearchFilter: false,
      closeDropDownOnSelection: true,
    };
  }

  public onSelect(item: any, filteredField): any {
    if (filteredField === 'equipment') {
      this.filterForm.get('equipmentFilter').setValue(item.id);
    }
  }

  public defaultFilterSettings(): void {
    this.filterForm.get('templateType').setValue(this.defaultRequestType);
    if (this.filterForm.get('startTime').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('templateType').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('dateFilter').value !== '') {
      this.filterCount += 1;
    }
  }

  public redirect(path: any): void {
    this.router.navigate([`/${path}`]);
  }

  public ngAfterViewInit(): void {
    this.projectService.projectParent.subscribe((response): void => {
      if (response !== undefined && response !== null && response !== '') {
        this.ParentCompanyId = response.ParentCompanyId;
        this.ProjectId = response.ProjectId;
        this.setCalendar();
        this.getRequestDetails();
        this.getMembers();
      }
    });
  }

  public openModalSave(): void {
    const modalRef = this.modalService.show(SaveReportFormComponent, { class: 'modal-save' });
    modalRef.content.ProjectId = this.ProjectId;
    modalRef.content.reportType = 'Weekly Calender';
    modalRef.content.updatedMemberList = this.updatedMemberList;
    modalRef.content.exportType = this.exportType;
    modalRef.content.filterPayload = this.filterPayload;
    modalRef.content.pageSize = this.pageSize;
    modalRef.content.pageNo = this.pageNo;
    modalRef.content.sortOrder = 'asc';
    modalRef.content.void = 0;
  }

  public openModalExport(template: TemplateRef<any>): void {
    this.modalRef = this.modalService.show(template, {
      class: 'reportexport-modal',
    });
  }

  public openModal(template: TemplateRef<any>): void {
    this.getOverAllGateForNdrGrid();
    this.getLocationsForNdrGrid();
    this.modalRef = this.modalService.show(template, {
      class: 'heatmap-modal',
    });
  }

  public openSchedulePopup(): void {
    this.scheduleFormModalRef = this.modalService.show(SchedulerFormComponent);
    this.scheduleFormModalRef.content.ProjectId = this.ProjectId;
    this.scheduleFormModalRef.content.ParentCompanyId = this.ParentCompanyId;
    this.scheduleFormModalRef.content.reportType = 'Weekly Calendar';
    this.scheduleFormModalRef.content.updatedMemberList = this.updatedMemberList;
    this.scheduleFormModalRef.content.exportType = this.exportType;
    this.scheduleFormModalRef.content.filterPayload = this.filterPayload;
  }

  public goTimeGridWeekOrDay(view): void {
    if (view === 'timeGridWeek') {
      this.currentView = 'Week';
    } else {
      this.currentView = 'Day';
    }
    this.calendarApi.changeView(view);
    this.setCalendar();
  }

  public setCalendar(): void {
    this.calendarApi = this.calendarComponent1.getApi();
    this.Range = this.calendarApi?.currentData?.dateProfile?.activeRange;
    this.getRequestDetails();
  }

  public goPrev(): void {
    this.calendarApi.prev();
    this.setCalendar();
  }

  public goNextYear(): void {
    this.calendarApi.nextYear();
    this.setCalendar();
  }

  public goPrevYear(): void {
    this.calendarApi.prevYear();
    this.setCalendar();
  }

  public goNext(): void {
    this.calendarApi.next();
    this.setCalendar();
  }

  public goDayGridMonth(): void {
    this.currentView = 'Month';
    this.calendarApi.changeView('dayGridMonth');
    this.setCalendar();
  }

  public getRequestDetails(): void {
    const filterParams = {
      ProjectId: this.ProjectId,
      void: 0,
    };

    this.selectedArray = [];

    if (!this.filterForm.value?.templateType?.length) {
      this.formSubmitted = false;
      this.loader = false;
      this.toastr.error('Please choose any one of the booking type');
      return;
    }

    this.loader = true;
    const payload = this.buildPayload();

    if (this.isInvalidTimeRange(payload)) {
      this.loader = false;
      this.formSubmitted = false;
      return;
    }

    this.payloadService(filterParams, payload);
    this.selectedArray.push(payload);
    this.filterPayload = payload;
  }

  public buildPayload(): any {
    const formValue = this.filterForm.value;

    const { dateFilter } = formValue;
    const startDate = dateFilter ? moment(dateFilter[0]).format('YYYY-MM-DD') : dateFilter;
    const endDate = dateFilter ? moment(dateFilter[1]).format('YYYY-MM-DD') : dateFilter;

    return {
      start: moment(this.Range?.start).format('YYYY-MM-DD'),
      end: moment(this.Range?.end).add(1, 'days').format('YYYY-MM-DD'),
      startDate,
      endDate,
      statusFilter: formValue.statusFilter?.[0] || '',
      memberFilter: +formValue.memberFilter?.[0]?.id || 0,
      gateFilter: +formValue.gateFilter?.[0]?.id || 0,
      equipmentFilter: +formValue.equipmentFilter?.[0]?.id || 0,
      defineFilter: +formValue.defineFilter?.[0]?.id || 0,
      companyFilter: +formValue.companyFilter?.[0]?.id || 0,
      templateType: formValue.templateType,
      locationFilter: +formValue.locationFilter?.[0]?.id || 0,
      startTime: formValue.startTime,
      endTime: formValue.endTime,
      ParentCompanyId: this.ParentCompanyId,
      eventStartTime: moment(formValue.startTime).format('HH:mm:00'),
      eventEndTime: moment(formValue.endTime).format('HH:mm:00'),
      currentViewMonth: 'Week',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      isDST: moment(new Date()).isDST(),
    };
  }

  public isInvalidTimeRange(payload: any): boolean {
    const sameDay = payload.startDate === payload.endDate;

    if (!sameDay) return false;

    if (payload.eventStartTime === payload.eventEndTime) {
      this.toastr.error('Time Range Start time and End time should not be the same');
      return true;
    }

    if (payload.eventStartTime > payload.eventEndTime) {
      this.toastr.error('Please enter Time Range From Time lesser than To Time');
      return true;
    }

    return false;
  }


  public payloadService(filterParams, payload): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.deliveryService
      .weeklyDeliveryList(filterParams, payload)
      .subscribe((NdrResponse: any): void => {
        const responseData = NdrResponse.data.rows;
        this.events = [];
        this.deliveryList = responseData;
        this.loader = false;
        this.formSubmitted = false;
        this.eventsForCalendar();
      });
  }

  public eventsForCalendar(): void {
    if (this.deliveryList && this.deliveryList.length > 0) {
      this.deliveryList.forEach((element): void => {
        const assignData: any = {
          description: '',
          title: '',
          id: '',
          start: '',
          end: '',
          fromTime: '',
          toTime: '',
          listType: '',
          uniqueNumber: '',
          allDay: false,
          allDaySlot: false,
        };
        assignData.title = element.description;
        assignData.description = element.description;
        assignData.id = element.id;
        assignData.uniqueNumber = element.uniqueNumber;
        if (element.requestType === 'craneRequest') {
          assignData.start = element.craneDeliveryStart;
          assignData.end = element.craneDeliveryEnd;
          assignData.fromTime = moment(element.craneDeliveryStart).format('hh:mm A');
          assignData.toTime = moment(element.craneDeliveryEnd).format('hh:mm A');
        }
        if (element.requestType === 'concreteRequest') {
          assignData.start = element.concretePlacementStart;
          assignData.end = element.concretePlacementEnd;
          assignData.fromTime = moment(element.concretePlacementStart).format('hh:mm A');
          assignData.toTime = moment(element.concretePlacementEnd).format('hh:mm A');
        }
        if (element.requestType === 'deliveryRequest') {
          assignData.start = element.deliveryStart;
          assignData.end = element.deliveryEnd;
          assignData.fromTime = moment(element.deliveryStart).format('hh:mm A');
          assignData.toTime = moment(element.deliveryEnd).format('hh:mm A');
        }
        if (element.requestType) {
          assignData.listType = element.requestType;
        }
        if (element.requestType === 'deliveryRequestWithCrane') {
          const chosenCraneTemplate = this.filterForm.value.templateType.filter(
            (object): any => object.name === 'Crane',
          );
          if (chosenCraneTemplate && chosenCraneTemplate.length > 0) {
            assignData.listType = 'CraneEquipment';
          }
          assignData.start = element.deliveryStart;
          assignData.end = element.deliveryEnd;
          assignData.fromTime = moment(element.deliveryStart).format('hh:mm A');
          assignData.toTime = moment(element.deliveryEnd).format('hh:mm A');
        }
        if (element.requestType === 'calendarEvent') {
          assignData.start = element.fromDate;
          assignData.end = element.toDate;
          assignData.fromTime = moment(element.fromDate).format('hh:mm A');
          assignData.toTime = moment(element.toDate).format('hh:mm A');
        }
        if (element.requestType === 'calendarEvent' && element.isAllDay === true) {
          delete assignData.allDay;
          delete assignData.allDaySlot;
          assignData.allDay = true;
          assignData.allDaySlot = true;
        }
        if (element.requestType === 'inspectionRequest' || element.requestType === 'inspectionRequestWithCrane') {
          assignData.start = element.inspectionStart;
          assignData.end = element.inspectionEnd;
          assignData.fromTime = moment(element.inspectionStart).format('hh:mm A');
          assignData.toTime = moment(element.inspectionEnd).format('hh:mm A');
        }
        this.renderCalendarEvents(element, assignData);
      });
    }
    this.calendarApi.removeAllEventSources();
    this.calendarApi.addEventSource(this.events);
  }

  public renderCalendarEvents(element, assignData): void {
    const calendarEvents = assignData;
    if (element.status === 'Pending' || element.status === 'Tentative') {
      calendarEvents.className = 'orange_event';
      this.events.push(calendarEvents);
    } else if (element.status === 'Approved') {
      calendarEvents.className = 'green_event';
      this.events.push(calendarEvents);
    } else if (element.status === 'Declined') {
      calendarEvents.className = 'red_event';
      this.events.push(calendarEvents);
    } else if (element.status === 'Expired') {
      calendarEvents.className = 'grey_event';
      this.events.push(calendarEvents);
    } else if (element.status === 'Delivered' || element.status === 'Completed') {
      calendarEvents.className = 'full_blue_event_weekly';
      this.events.push(calendarEvents);
    } else if (!element.status) {
      calendarEvents.className = 'calendar_event';
      this.events.push(calendarEvents);
    }
  }

  public setDefaultDateRangePicker(): void {
    const start = moment();
    const beginWeek = start.startOf('week').toDate();
    const newDate = start.startOf('week').clone().add(6, 'days').endOf('day')
      .toDate();
    this.filterForm.controls.dateFilter.setValue([beginWeek, newDate]);
  }

  public setDefaultDateAndTime(): void {
    const setStartTime = 7;
    this.deliveryStart = new Date();
    this.deliveryEnd = new Date();
    this.deliveryStart.setHours(setStartTime);
    this.deliveryStart.setMinutes(0);
    this.deliveryEnd.setHours(setStartTime + 10);
    this.deliveryEnd.setMinutes(0);
    this.filterForm.get('startTime').setValue(this.deliveryStart);
    this.filterForm.get('endTime').setValue(this.deliveryEnd);
  }

  public getOverAllGateForNdrGrid(): void {
    const getOverAllGateForNdrGridParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .gateList(getOverAllGateForNdrGridParams, { isFilter: true, showActivatedAlone: true })
      .subscribe((getOverAllGateForNdrGridResponse): void => {
        this.gateList = getOverAllGateForNdrGridResponse.data;
        this.getOverAllEquipmentForNdrGrid();
      });
  }

  public getOverAllEquipmentForNdrGrid(): void {
    const getOverAllEquipmentForNdrGridParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listEquipment(getOverAllEquipmentForNdrGridParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((getOverAllEquipmentForNdrGridResponse): void => {
        this.equipmentList = getOverAllEquipmentForNdrGridResponse.data;
        this.getDefinableForNdrGrid();
      });
  }

  public getDefinableForNdrGrid(): void {
    const getDefinableForNdrGridParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getDefinableWork(getDefinableForNdrGridParams)
      .subscribe((getDefinableForNdrGridResponse: any): void => {
        if (getDefinableForNdrGridResponse) {
          const { data } = getDefinableForNdrGridResponse;
          this.defineList = data;
          this.getCompaniesForNdrGrid();
        }
      });
  }

  public getCompaniesForNdrGrid(): void {
    const getCompaniesForNdrGridParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getCompanies(getCompaniesForNdrGridParams)
      .subscribe((getCompaniesForNdrGridResponse: any): void => {
        if (getCompaniesForNdrGridResponse) {
          this.companyList = getCompaniesForNdrGridResponse.data;
          this.getLocationsForNdrGrid();
        }
      });
  }

  public focus(): void {
    this.isKeepOpen = true;
  }

  public focuslost(): void {
    this.isKeepOpen = false;
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
        this.updatedMemberList = this.memberList.map((item): any => {
          let name;
          if (item.status === 'pending') {
            name = item.isGuestUser ? `${item.User.email}(Guest)` : item.User.email;
          } else {
            name = `${item.firstName} ${item.User.lastName}`;
          }
          return {
            ...item,
            UserEmail: item.User.email,
            name,
          };
        });
      }
    });
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      templateType: [''],
      startTime: [''],
      endTime: [''],
      companyFilter: [''],
      descriptionFilter: [''],
      statusFilter: [''],
      dateFilter: [''],
      memberFilter: [''],
      gateFilter: [''],
      equipmentFilter: [''],
      defineFilter: [''],
      locationFilter: [''],
    });
    this.getOverAllGateForNdrGrid();
    this.getLocationsForNdrGrid();
  }

  public changeDate(event: any): void {
    const startTime = new Date(event).getHours();
    const minutes = new Date(event).getMinutes();
    this.deliveryEnd = new Date();
    this.deliveryEnd.setHours(startTime + 1);
    this.deliveryEnd.setMinutes(minutes);
    this.filterForm.get('endTime').setValue(this.deliveryEnd);
  }

  public numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public filterSubmit(): void {
    this.formSubmitted = true;
    this.filterCount = 0;
    if (this.filterForm.get('dateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('companyFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('memberFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('gateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('equipmentFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('statusFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('defineFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('startTime').value !== '') {
      this.filterCount += 1;
    }

    if (this.filterForm.get('templateType').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('locationFilter').value !== '') {
      this.filterCount += 1;
    }
    this.getRequestDetails();
  }

  public resetFilter(): void {
    if (this.filterForm.invalid) {
      this.formSubmitted = true;
    }
    this.modalRef.hide();
    this.filterForm.reset();
    this.filterDetailsForm();
    this.filterCount = 0;
    this.setDefaultDateAndTime();
    this.setDefaultDateRangePicker();
    this.defaultFilterSettings();
    this.getRequestDetails();
  }

  public exportDetailsForm(): void {
    this.exportForm = this.formBuilder.group({
      reportName: ['', Validators.required],
      reportType: ['', Validators.required],
    });
    this.exportForm.get('reportName').setValue(this.reportName);
    this.exportForm.get('reportType').setValue(this.reportType);
  }

  public cancelExport(): void {
    this.modalRef.hide();
    this.exportSubmitted = false;
    this.exportForm.get('reportName').setValue('Weekly Calendar Report');
    this.exportForm.get('reportType').setValue('EXCEL');
  }

  public closeIcon(): void {
    this.modalRef.hide();

    if (!this.selectedArray) return;

    const selected = this.selectedArray[0];
    this.filterForm.get('templateType').setValue(selected.templateType);

    const filterFields = [
      'companyFilter',
      'defineFilter',
      'equipmentFilter',
      'gateFilter',
      'memberFilter',
      'locationFilter',
    ];

    filterFields.forEach((field) => {
      const value = selected[field];
      this.filterForm.get(field).setValue(value > 0 ? value : '', { emitEvent: false });
    });

    const optionalFields = ['statusFilter', 'startTime', 'endTime'];

    optionalFields.forEach((field) => {
      const value = selected[field];
      this.filterForm.get(field).setValue(value || '', { emitEvent: false });
    });
  }



  public export(): void {
    this.exportSubmitted = true;
    this.formSubmitted = true;

    if (this.exportForm.invalid) {
      this.exportSubmitted = false;
      return;
    }

    if (!this.hasTemplateType()) {
      this.exportSubmitted = false;
      this.toastr.error('Please choose any one of the booking type');
      return;
    }

    const getWeeklyCalendarParam = { ProjectId: this.ProjectId, void: 0 };
    const getWeeklyCalendarPayload = this.buildExportPayload();

    this.filterPayload = getWeeklyCalendarPayload;
    this.payloadExportService(getWeeklyCalendarParam, getWeeklyCalendarPayload);
  }

  public hasTemplateType(): boolean {
    return this.filterForm.value.templateType.length > 0;
  }

  public buildExportPayload(): any {
    const { dateFilter } = this.filterForm.value;
    const { locationFilter } = this.filterForm.value;
    const startDate = dateFilter ? moment(dateFilter[0]).format('YYYY-MM-DD') : dateFilter;
    const endDate = dateFilter ? moment(dateFilter[1]).format('YYYY-MM-DD') : dateFilter;

    const payload = {
      start: moment(this.Range?.start).format('YYYY-MM-DD'),
      end: moment(this.Range?.end).format('YYYY-MM-DD'),
      companyFilter: +this.filterForm.value.companyFilter,
      startDate,
      endDate,
      statusFilter: this.filterForm.value.statusFilter,
      memberFilter: +this.filterForm.value.memberFilter,
      gateFilter: +this.filterForm.value.gateFilter,
      equipmentFilter: this.filterForm.value.equipmentFilter === null || this.filterForm.value.equipmentFilter === '' ? null : +this.filterForm.value.equipmentFilter,
      templateType: this.filterForm.value.templateType,
      defineFilter: +this.filterForm.value.defineFilter,
      startTime: this.filterForm.value.startTime,
      endTime: this.filterForm.value.endTime,
      ParentCompanyId: this.ParentCompanyId,
      eventStartTime: moment(this.filterForm.value.startTime).format('HH:mm:00'),
      eventEndTime: moment(this.filterForm.value.endTime).format('HH:mm:00'),
      locationFilter: locationFilter?.[0]?.id ? +locationFilter[0].id : 0,
      exportType: this.exportForm.get('reportType').value,
      reportName: this.exportForm.get('reportName').value,
      generatedDate: moment(new Date()).format('ddd, MMM DD YYYY'),
      currentViewMonth: 'Week',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      isDST: moment(new Date()).isDST(),
      currentStart: moment(this.Range.start).format('YYYY-MM-DD HH:mm:ss'),
      currentEnd: moment(this.Range.end).format('YYYY-MM-DD HH:mm:ss'),
    } as any;

    if (startDate && endDate) {
      payload.typeFormat = 'export';
    }

    return payload;
  }


  public payloadExportService(getWeeklyCalendarParam, getWeeklyCalendarPayload): void {
    if (getWeeklyCalendarPayload.exportType === 'EXCEL') {
      this.deliveryService
        .exportWeeklyCalendarRequestInExcelFormat(getWeeklyCalendarParam, getWeeklyCalendarPayload)
        .subscribe((response: any): void => {
          if (response) {
            this.deliveryService.saveAsExcelFile(response, this.exportForm.get('reportName').value);
            this.toastr.success('Weekly Calendar exported successfully');
            this.cancelExport();
          }
        });
    } else {
      this.deliveryService
        .exportWeeklyCalendarRequestInExcelFormat(getWeeklyCalendarParam, getWeeklyCalendarPayload)
        .subscribe((response: any): void => {
          if (response) {
            if (response.message === 'No data found') {
              this.toastr.error('No data found');
              this.cancelExport();
              return;
            }
            if (response.message.includes('Currently')) {
              this.toastr.error(response.message);
              this.cancelExport();
              return;
            }
            const link = document.createElement('a');
            link.setAttribute('target', '_self');
            link.setAttribute('href', response.data);
            document.body.appendChild(link);
            link.click();
            link.remove();
            this.toastr.success('Weekly Calendar exported successfully');
            this.cancelExport();
          }
        });
    }
  }

  public getLocationsForNdrGrid(): void {
    const getLocationsForNdrGridParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getLocations(getLocationsForNdrGridParams)
      .subscribe((getLocationsForNdrGridResponse: any): void => {
        if (getLocationsForNdrGridResponse) {
          this.locationList = getLocationsForNdrGridResponse.data;
        }
      });
  }
}
