/* eslint-disable max-lines-per-function */
import { Component, ViewChild, TemplateRef } from '@angular/core';
import { FullCalendarComponent } from '@fullcalendar/angular';
import { CalendarOptions } from '@fullcalendar/core';
import interactionPlugin from '@fullcalendar/interaction';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import moment from 'moment';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { Title } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { ProjectSharingService } from '../../../services';
import { NewCraneRequestCreationFormComponent } from '../../../crane-requests/new-crane-request-creation-form/new-crane-request-creation-form.component';
import { ProjectService } from '../../../services/profile/project.service';
import { CalendarService } from '../../../services/profile/calendar.service';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { EditDeliveryFormComponent } from '../../../delivery-requests/delivery-details/edit-delivery-form/edit-delivery-form.component';
import { DeliveryDetailsNewComponent } from '../../../delivery-requests/delivery-details/delivery-details-new/delivery-details-new.component';
import { GuestCraneDetailsComponent } from '../crane/guest-crane-details/guest-crane-details.component';

@Component({
  selector: 'app-guestuser-cranecalendar',
  templateUrl: './guestuser-cranecalendar.component.html',
})
export class GuestuserCranecalendarComponent {
  public events: any = [];

  public voidSubmitted = false;

  public submitted = false;

  public formSubmitted = false;

  public descriptionPopup = false;

  public calendarDescriptionPopup = false;

  public viewEventData: any;

  public message = '';

  public calendarApi: any;

  public Range: any = {};

  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public lastId: any = 0;

  public loader = true;

  public eventData: any = {};

  public deliveryRequestWithCraneEquipmentType: any[];

  public ProjectId: any;

  public filterForm: UntypedFormGroup;

  public search = '';

  public craneRequestId: any;

  public calendarCurrentDeliveryIndex: string | number;

  public authUser: any = {};

  public ParentCompanyId: any;

  public companyList: any = [];

  public defineList: any = [];

  public gateList: any = [];

  public equipmentList: any = [];

  public modalLoader = false;

  public dropdownSettings: IDropdownSettings;

  public definableDropdownSettings: IDropdownSettings;

  public filterCount = 0;

  public wholeStatus = ['Approved', 'Completed', 'Declined', 'Delivered', 'Pending'];

  public memberList: any = [];

  public statusValue: any = [];

  public showSearchbar = false;

  public toolTipContent = '';

  public currentViewMonth: moment.MomentInput;

  public currentView = 'Day';

  public monthlyEventloader = true;

  public monthEvents = [];

  public subscription: any;

  public subscription1: any;

  public seriesOptions = [];

  public allRequestIsOpened = false;

  public approved: string;

  public pending: string;

  public rejected: string;

  public delivered: string;

  public expired: string;

  public locationList: any = [];

  public isGuestUser = false;

  public isGuestReq = false;

  public id: number;

  public email: string;

  public guestMemberId: any;

  @ViewChild('fullcalendar', { static: true }) public calendarComponent1: FullCalendarComponent;

  public calendarOptions: CalendarOptions = {
    selectable: true,
    initialView: 'timeGridDay',
    plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin],
    aspectRatio: 2,
    expandRows: true,
    nowIndicator: true,
    dayMaxEvents: true,
    contentHeight: 'liquid',
    fixedWeekCount: false,
    slotEventOverlap: false,
    events: this.events,

    eventDidMount(info): void {
      const argument = info;
      const isGuest = argument.event._def.extendedProps.isGuestUser;
      const { isGuestRequest } = argument.event.extendedProps;
      const requestCreatorId = argument.event._def.extendedProps.creatorId;
      const calendarType = argument.event._def.extendedProps.requestType;
      const loginMemberId = argument.event._def.extendedProps.guestMemberId;
      const line3 = 'Booking';
      const line1 = argument.event._def.extendedProps.description;
      const { line2 } = argument.event._def.extendedProps;
      const start = argument.event._def.extendedProps.deliveryStartTime;
      const end = argument.event._def.extendedProps.deliveryEndTime;
      const eventTitleElement: HTMLElement = argument.el.querySelector('.fc-event-title');
      eventTitleElement.style.backgroundColor = info.backgroundColor;
      eventTitleElement.style.color = info.textColor;
      eventTitleElement.style.borderLeft = `4px solid ${info.borderColor}`;
      if (argument.event._def.allDay) {
        if (isGuest && isGuestRequest && (requestCreatorId === loginMemberId)) {
          eventTitleElement.innerHTML = `
        <div class="d-flex flex-column">
        <p class="m-0"> <img  class="w10 h10" src="./assets/images/noun-event-alert.svg" class="form-icon" alt="allday" >  ${line1} </p>
        <small> ${line2}</small>
        </div>`;
        } else if (calendarType === 'calendarEvent') {
          eventTitleElement.innerHTML = `
      <div class="d-flex flex-column">
      <p class="m-0"> <img class="w10 h10" src="./assets/images/noun-event-alert.svg" class="form-icon" alt="allday" >  ${line1} </p>
      <small> ${line2} </small>
      </div>`;
        } else {
          eventTitleElement.innerHTML = `
        <div class="d-flex flex-column">
        <p class="m-0"> <img  class="w10 h10" src="./assets/images/noun-event-alert.svg" class="form-icon" alt="allday" >  ${line3} </p>
        <small class="text-wrap"> ${start} - ${end} </small>
        </div>`;
        }
      } else if (isGuest && isGuestRequest && (requestCreatorId === loginMemberId)) {
        eventTitleElement.innerHTML = `
          <div class="d-flex flex-column">
          <p class="m-0"> ${line1} </p>
          <small class="truncate"> ${line2}</small>
          </div>`;
      } else if (calendarType === 'calendarEvent') {
        eventTitleElement.innerHTML = `
        <div class="d-flex flex-column">
        <p class="m-0"> ${line1} </p>
        <small class="truncate"> ${line2}</small>
        </div>`;
      } else {
        eventTitleElement.innerHTML = `
        <div class="d-flex flex-column">
        <p class="m-0"> ${line3} </p>
        <small class="text-wrap"> ${start} - ${end} </small>
        </div>`;
      }
    },
    customButtons: {
      prev: {
        text: 'PREV',
        click: (): void => this.goPrev(),
      },
      next: {
        text: 'Next',
        click: (): void => this.goNext(),
      },
      prevYear: {
        text: 'PREV',
        click: (): void => this.goPrevYear(),
      },
      nextYear: {
        text: 'Next',
        click: (): void => this.goNextYear(),
      },
      timeGridWeek: {
        text: 'Week',
        click: (): void => this.goTimeGridWeekOrDay('timeGridWeek'),
      },
      timeGridDay: {
        text: 'Day',
        click: (): void => this.goTimeGridWeekOrDay('timeGridDay'),
      },
      dayGridMonth: {
        text: 'Month',
        click: (): void => this.goDayGridMonth(),
      },
    },
    showNonCurrentDates: false,
    headerToolbar: {
      right: '',
      center: 'prevYear,prev,title,next,nextYear',
      left: 'dayGridMonth,timeGridWeek,timeGridDay',
    },
    firstDay: 0,
    eventClick: (arg): void => {
      this.deliveryDescription(arg);
    },
    datesSet: (info): void => {
      this.currentViewMonth = info.view.title;
    },
    eventTimeFormat: {
      hour: 'numeric',
      minute: '2-digit',
      meridiem: 'short',
    },
    views: {
      timeGrid: {
        dayMaxEventRows: 2, // adjust to 6 only for timeGridWeek/timeGridDay
      },
      dayGridMonth: {
        allDaySlot: true,
      },
      timeGridWeek: {
        allDaySlot: true,
      },
      timeGridDay: {
        allDaySlot: true,
      },
    },
    dateClick: (info): void => {
    },
  };

  public constructor(
    public bsModalRef: BsModalRef,
    private readonly modalService: BsModalService,
    public projectService: ProjectService,
    private readonly ProjectSharingServices: ProjectSharingService,
    private readonly formBuilder: UntypedFormBuilder,
    public calendarService: CalendarService,
    private readonly router: Router,
    private readonly toastr: ToastrService,
    public deliveryService: DeliveryService,
    private readonly titleService: Title,
  ) {
    this.titleService.setTitle('Follo - Crane Calendar');
    this.filterDetailsForm();
    this.id = +window.atob(localStorage.getItem('guestId'));
    this.email = window.atob(localStorage.getItem('guestEmail'));
    this.ProjectId = +window.atob(localStorage.getItem('guestProjectId'));
    this.ParentCompanyId = +window.atob(localStorage.getItem('guestParentCompanyId'));
    this.checkGuest();
    this.getDeliveryRequestWithCraneEquipmentType();
  }

  public checkGuest(): void {
    const payload = {
      email: this.email,
      ProjectId: this.ProjectId,
      // email:"<EMAIL>",
    };
    this.ProjectSharingServices.checkIsGuest(payload).subscribe({
      next: (response: any): void => {
        if (response?.data) {
          this.isGuestUser = response.data.isGuestUser;
          this.guestMemberId = response?.data.id;
        } else {
          this.isGuestUser = false;
        }
      },
      error: (error: any) => {
        console.error('Error occurred:', error);
        this.toastr.error('Try again later.!', 'Something went wrong.');
      },
    });
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'show':
          this.clear();
          break;
        case 'filter':
          this.openFilterModal(data);
          break;
        case 'modal':
          this.openIdModal(data);
          break;
        case 'close':
          this.closeCalendarDescription();
          break;
        case 'edit':
          this.openEditModal(data, item);
          break;
        default:
          break;
      }
    }
  }

  public setCalendar(): void {
    this.calendarApi = this.calendarComponent1.getApi();
    this.Range = this.calendarApi?.currentData?.dateProfile.activeRange;
    this.getDeliveryRequestWithCraneEquipmentType();
  }

  public openAddCraneRequestModal(dateArg): void {
    if (this.isGuestUser) {
      return;
    }
    const passData = {
      date: dateArg.dateStr,
      currentView: this.currentView,
    };
    const initialState = {
      data: passData,
      title: 'Modal with component',
    };
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(NewCraneRequestCreationFormComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
      initialState,
    });
    this.modalRef.content.lastId = this.lastId;
    this.modalRef.content.closeBtnName = 'Close';
  }

  public goNext(): void {
    this.closeDescription();
    this.calendarApi.next();
    this.setCalendar();
  }

  public goTimeGridWeekOrDay(view: string): void {
    if (view === 'timeGridWeek') {
      this.currentView = 'Week';
    } else {
      this.currentView = 'Day';
    }
    this.closeDescription();
    this.calendarApi.changeView(view);
    this.setCalendar();
  }

  public changeRequestCollapse(data): void {
    this.initializeSeriesOption();
    if (!moment(data.craneDeliveryStart).isAfter(moment())) {
      this.seriesOptions = this.seriesOptions.filter((object): any => {
        const seriesObject = object;
        if (seriesObject.option !== 1) {
          seriesObject.disabled = true;
        }
        return seriesObject;
      });
    }
    this.allRequestIsOpened = !this.allRequestIsOpened;
  }

  public goPrevYear(): void {
    this.closeDescription();
    this.calendarApi.prevYear(); // call a method on the Calendar object
    this.setCalendar();
  }

  public initializeSeriesOption(): void {
    this.seriesOptions = [
      {
        option: 1,
        text: 'This event',
        disabled: false,
      },
      {
        option: 2,
        text: 'This and all following events',
        disabled: false,
      },
      // {
      //   option: 3,
      //   text: 'All events in the series',
      //   disabled: false,
      // },
    ];
  }

  public goNextYear(): void {
    this.closeDescription();
    this.calendarApi.nextYear(); // call a method on the Calendar object
    this.setCalendar();
  }

  public goDayGridMonth(): void {
    this.currentView = 'Month';
    this.closeDescription();
    this.calendarApi.changeView('dayGridMonth');
    this.setCalendar();
  }

  public goPrev(): void {
    this.closeDescription();
    this.calendarApi.prev(); // call a method on the Calendar object
    this.setCalendar();
  }

  public closeDescription(): void {
    this.descriptionPopup = false;
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.getDeliveryRequestWithCraneEquipmentType();
  }

  public getSearchNDR(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    if (data.length >= 1) {
      this.search = data;
      this.getDeliveryRequestWithCraneEquipmentType();
    }
    if (data.length === 0) {
      this.search = '';
      this.getDeliveryRequestWithCraneEquipmentType();
    }
  }

  public getResponsiblePeople(object: { firstName: any; lastName: any }): any {
    if (object?.firstName && object?.lastName) {
      const string = `${object.firstName} ${object.lastName}`;
      const matches = string.match(/\b(\w)/g);
      const acronym = matches.join('').toUpperCase();
      return acronym;
    }
    return 'UU';
  }

  public deliveryDescription(arg): void {
    this.isGuestReq = arg.event._def.extendedProps.isGuestRequest;
    const requestCreatorId = arg.event._def.extendedProps.creatorId;
    const loginMemberId = arg.event._def.extendedProps.guestMemberId;
    const calendarType = arg.event._def.extendedProps.requestType;

    if (calendarType !== 'calendarEvent') {
      if (this.isGuestUser && !this.isGuestReq) {
        return;
      }
      if (this.isGuestUser && this.isGuestReq && (requestCreatorId !== loginMemberId)) {
        return;
      }
    }

    this.descriptionPopup = false;
    this.allRequestIsOpened = false;
    this.calendarDescriptionPopup = false;
    this.eventData = {};

    if (Object.keys(arg).length !== 0) {
      this.processRequestEvent(arg);
    }
  }

  public processRequestEvent(arg): void {
    let index: number;
    const { event } = arg;

    if (event.extendedProps.uniqueNumber) {
      index = this.deliveryRequestWithCraneEquipmentType.findIndex(
        (
          item: { id: any; uniqueNumber: any },
        ): any => item.id === +event.id && item.uniqueNumber === event.extendedProps.uniqueNumber,
      );
    } else {
      index = this.deliveryRequestWithCraneEquipmentType.findIndex(
        (item): any => item.CraneRequestId === +event.id,
      );
    }

    this.eventData = this.deliveryRequestWithCraneEquipmentType[index];

    if (this.eventData.requestType === 'craneRequest') {
      this.calendarCurrentDeliveryIndex = index;
      this.eventData.startDate = moment(this.eventData.craneDeliveryStart).format('MM/DD/YYYY');
      if (this.eventData.approved_at !== null) {
        this.eventData.approvedAt = moment(this.eventData.approved_at).format('lll');
      }
      this.eventData.startTime = moment(this.eventData.craneDeliveryStart).format('hh:mm A');
      this.eventData.endTime = moment(this.eventData.craneDeliveryEnd).format('hh:mm A');
      this.getCraneRequest(arg);
      this.openIdModal(this.eventData);
    } else if (this.eventData.requestType === 'deliveryRequestWithCrane') {
      this.calendarCurrentDeliveryIndex = index;
      this.eventData.startDate = moment(this.eventData.deliveryStart).format('MM/DD/YYYY');
      if (this.eventData.approved_at !== null) {
        this.eventData.approvedAt = moment(this.eventData.approved_at).format('lll');
      }
      this.eventData.startTime = moment(this.eventData.deliveryStart).format('hh:mm A');
      this.eventData.endTime = moment(this.eventData.deliveryEnd).format('hh:mm A');
      this.getNDR(arg);
      this.openIdModal(this.eventData);
    } else {
      this.calendarDescription(arg);
    }
  }


  public getCraneRequest(data: { event: { id: string | number } }): void {
    const param = {
      CraneRequestId: +data.event.id,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    this.ProjectSharingServices.guestGetEquipmentCraneRequest(param).subscribe((res): void => {
      if (this.authUser.RoleId === 4 || this.authUser.RoleId === 3) {
        const newMember = res.data.memberDetails;
        const index = newMember.findIndex(
          (i: { Member: { id: any } }): boolean => i.Member.id === this.authUser.id,
        );
        if (index !== -1) {
          this.eventData.edit = true;
        } else {
          this.eventData.edit = false;
        }
      } else {
        this.eventData.edit = true;
      }
      this.eventData.gateDetails = res.data.gateDetails;
      this.toolTipContent = '';
      if (this.eventData.memberDetails.length > 3) {
        const slicedArray = this.eventData?.memberDetails?.slice(3) || [];
        slicedArray.map(
          (a: { Member: { User: { firstName: any; lastName: any; email: any } } }): any => {
            if (a.Member.User.firstName) {
              this.toolTipContent += `${a.Member.User.firstName} ${a.Member.User.lastName}, `;
            } else {
              this.toolTipContent += `${a.Member.User.email}, `;
            }
          },
        );
      }
    });
  }

  public getNDR(data: { event: { id: string | number } }): void {
    const index = this.deliveryRequestWithCraneEquipmentType.findIndex(
      (item): boolean => item.CraneRequestId === +data.event.id,
    );
    const clickedEventData = this.deliveryRequestWithCraneEquipmentType[index];
    const param = {
      DeliveryRequestId: clickedEventData.id,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices.guestGetNDRData(param).subscribe((res): void => {
      if (this.authUser.RoleId === 4 || this.authUser.RoleId === 3) {
        const newMember = res.data.memberDetails;
        const index1 = newMember.findIndex(
          (i: { Member: { id: any } }): boolean => i.Member.id === this.authUser.id,
        );
        if (index1 !== -1) {
          this.eventData.edit = true;
        } else {
          this.eventData.edit = false;
        }
      } else {
        this.eventData.edit = true;
      }
      this.eventData.gateDetails = res.data?.gateDetails;
      this.toolTipContent = '';
      if (this.eventData.memberDetails.length > 3) {
        const slicedArray = this.eventData?.memberDetails?.slice(3) || [];
        slicedArray.map(
          (a: { Member: { User: { firstName: any; lastName: any; email: any } } }): any => {
            if (a.Member.User.firstName) {
              this.toolTipContent += `${a.Member.User.firstName} ${a.Member.User.lastName}, `;
            } else {
              this.toolTipContent += `${a.Member.User.email}, `;
            }
          },
        );
      }
    });
  }

  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public openEditModal(item, action): void {
    this.closeDescription();
    if (this.modalRef) {
      this.close();
    }
    if (!item.isAssociatedWithDeliveryRequest && !item.isAssociatedWithCraneRequest) {
      this.deliveryService.updatedEditCraneRequestId(+item.CraneRequestId);
      const className = 'modal-lg new-delivery-popup custom-modal';
      this.modalRef = this.modalService.show(GuestCraneDetailsComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
      });
    } else {
      this.deliveryService.updatedDeliveryId(item.id);
      const className = 'modal-lg new-delivery-popup custom-modal';
      this.modalRef = this.modalService.show(EditDeliveryFormComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
      });
    }
    this.modalRef.content.closeBtnName = 'Close';
    this.modalRef.content.seriesOption = item?.recurrence && item?.recurrence?.recurrence !== 'Does Not Repeat' ? action : 1;
    this.modalRef.content.recurrenceId = item?.recurrence ? item?.recurrence?.id : null;
    this.modalRef.content.recurrenceEndDate = item?.recurrence
      ? item?.recurrence?.recurrenceEndDate
      : null;
  }

  // public openAddNDRModal(): void {
  //   const className = 'modal-lg new-delivery-popup custom-modal';
  //   this.modalRef = this.modalService.show(NewCraneRequestCreationFormComponent, {
  //     backdrop: 'static',
  //     keyboard: false,
  //     class: className,
  //   });
  //   this.modalRef.content.lastId = this.lastId;
  //   this.modalRef.content.closeBtnName = 'Close';
  // }

  public openFilterModal(template: TemplateRef<any>): void {
    this.calendarGetOverAllGate();
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-sm filter-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }

  public calendarGetOverAllGate(): void {
    this.modalLoader = true;
    const calendarGetGateParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices
      .guestGateList(calendarGetGateParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((calendarGetGateListResponse): void => {
        this.gateList = calendarGetGateListResponse.data;
        this.calendarGetOverAllEquipment();
      });
  }

  public calendarGetOverAllEquipment(): void {
    const calendarGetEquipmentParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices
      .guestListCraneEquipment(calendarGetEquipmentParams, {
        showActivatedAlone: true,
      })
      .subscribe((calendarGetEquipmentListParams): void => {
        this.equipmentList = calendarGetEquipmentListParams.data.rows;
        this.calendarGetCompany();
      });
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.search = '';
    this.filterDetailsForm();
    this.getDeliveryRequestWithCraneEquipmentType();
    this.modalRef.hide();
  }

  public calendarGetDefinable(): void {
    const calendarGetDefinableParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices
      .guestGetDefinableWork(calendarGetDefinableParams)
      .subscribe((calendarGetDefinableListResponse: any): void => {
        if (calendarGetDefinableListResponse) {
          const { data } = calendarGetDefinableListResponse;
          this.defineList = data;
          this.definableDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'DFOW',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
          this.getLocations();
        }
      });
  }

  public getLocations(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices.guestGetLocations(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.locationList = data;
        this.openContentModal();
      }
    });
  }

  public openContentModal(): void {
    this.modalLoader = false;
  }

  public calendarGetCompany(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices.guestGetCompanies(params).subscribe((companyResponse: any): void => {
      if (companyResponse) {
        if (this.companyList.length === 0) {
          this.companyList = companyResponse.data;
        }
        this.calendarGetDefinable();
        this.dropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'companyName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
      }
    });
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('descriptionFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('dateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('companyFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('memberFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('gateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('equipmentFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('locationFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('statusFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('pickFrom').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('pickTo').value !== '') {
      this.filterCount += 1;
    }
    this.getDeliveryRequestWithCraneEquipmentType();
    this.modalRef.hide();
  }

  public ngAfterViewInit(): void {
    this.setCalendar();
    this.getMembers();
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.ProjectSharingServices.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }

  // eslint-disable-next-line max-lines-per-function
  public getDeliveryRequestWithCraneEquipmentType(): void {
    this.loader = true;
    this.deliveryRequestWithCraneEquipmentType = [];
    const filterParams = {
      ProjectId: this.ProjectId,
      void: 0,
    };
    let getEventNdrPayload: any = {};
    if (this.filterForm !== undefined) {
      const getEventNdrDateInFilterForm = this.filterForm.value.dateFilter
        ? moment(this.filterForm.value.dateFilter).format('YYYY-MM-DD')
        : this.filterForm.value.dateFilter;
      getEventNdrPayload = {
        companyFilter: +this.filterForm.value.companyFilter,
        descriptionFilter: this.filterForm.value.descriptionFilter,
        dateFilter: getEventNdrDateInFilterForm,
        statusFilter: this.filterForm.value.statusFilter,
        memberFilter: +this.filterForm.value.memberFilter,
        gateFilter: +this.filterForm.value.gateFilter,
        equipmentFilter: this.filterForm.value.equipmentFilter === null || this.filterForm.value.equipmentFilter === '' ? null : +this.filterForm.value.equipmentFilter,
        locationFilter: this.filterForm.value.locationFilter,
        pickFrom: this.filterForm.value.pickFrom,
        pickTo: this.filterForm.value.pickTo,
        ParentCompanyId: this.ParentCompanyId,
        search: this.search,
        id: this.id,
      };
    }
    getEventNdrPayload.search = this.search;
    getEventNdrPayload.start = moment(this.Range?.start).format('YYYY-MM-DD');
    getEventNdrPayload.end = moment(this.Range?.end).format('YYYY-MM-DD');
    getEventNdrPayload.filterCount = this.filterCount;
    getEventNdrPayload.calendarView = this.currentView;
    this.ProjectSharingServices
      .getDeliveryRequestWithCraneEquipmentType(filterParams, getEventNdrPayload)
      .subscribe((NdrResponse: any): void => {
        if (NdrResponse) {
          const responseData = NdrResponse.data;
          const statusData = JSON.parse(NdrResponse.statusData.statusColorCode);
          const cardData = JSON.parse(NdrResponse.cardData.craneCard);
          const UseTextColorAsLegendColor = JSON.parse(NdrResponse.statusData.useTextColorAsLegend);
          const isDefaultColor = JSON.parse(NdrResponse.statusData.isDefaultColor);
          const approved = statusData.find((item) => item.status === 'approved');
          const pending = statusData.find((item) => item.status === 'pending');
          const delivered = statusData.find((item) => item.status === 'delivered');
          const rejected = statusData.find((item) => item.status === 'rejected');
          const expired = statusData.find((item) => item.status === 'expired');

          if (UseTextColorAsLegendColor) {
            this.approved = approved.fontColor;
            this.pending = pending.fontColor;
            this.expired = expired.fontColor;
            this.rejected = rejected.fontColor;
            this.delivered = delivered.fontColor;
          } else {
            this.approved = approved.backgroundColor;
            this.pending = pending.backgroundColor;
            this.expired = expired.backgroundColor;
            this.rejected = rejected.backgroundColor;
            this.delivered = delivered.backgroundColor;
          }
          if (isDefaultColor) {
            this.delivered = delivered.backgroundColor;
          }
          this.lastId = NdrResponse.lastId.CraneRequestId;
          this.loader = false;
          this.deliveryRequestWithCraneEquipmentType = responseData;
          this.events = [];
          const previewSelected = cardData.filter((item) => item.selected);
          this.deliveryRequestWithCraneEquipmentType.forEach((element): void => {
            const assignData: any = {
              description: '',
              // equipment: '',
              title: '',
              start: '',
              end: '',
              id: '',
              uniqueNumber: '',
              companyName: '',
              allDay: false,
              allDaySlot: false,
              line2: '',
              isGuestRequest: false,
              deliveryStartTime: '',
              deliveryEndTime: '',
            };
            assignData.isGuestRequest = element.isCreatedByGuestUser;
            assignData.isGuestUser = this.isGuestUser;
            if (element?.createdUserDetails?.id) {
              assignData.creatorId = element.createdUserDetails.id;
            } else {
              assignData.creatorId = null;
            }
            assignData.guestMemberId = this.guestMemberId;
            assignData.requestType = element.requestType;
            assignData.deliveryStartTime = moment(element.craneDeliveryStart).format('hh:mm A');
            assignData.deliveryEndTime = moment(element.craneDeliveryEnd).format('hh:mm A');
            if (element.requestType === 'craneRequest') {
              assignData.start = element.craneDeliveryStart;
              assignData.end = element.craneDeliveryEnd;
            } else if (element.requestType === 'deliveryRequestWithCrane') {
              assignData.start = element.deliveryStart;
              assignData.end = element.deliveryEnd;
            } else {
              assignData.start = element.fromDate;
              assignData.end = element.toDate;
            }

            assignData.id = element.requestType === 'craneRequest'
              || element.requestType === 'deliveryRequestWithCrane'
              ? element.CraneRequestId
              : element.id;
            assignData.uniqueNumber = element.uniqueNumber;
            if (element.requestType === 'calendarEvent') {
              assignData.description = element.description;
              assignData.title = element.description;
            }

            if (element.requestType === 'calendarEvent' && element.isAllDay === true) {
              delete assignData.allDay;
              delete assignData.allDaySlot;
              assignData.allDay = true;
              assignData.allDaySlot = true;
            }
            if (element.requestType === 'craneRequest' || element.requestType === 'deliveryRequestWithCrane') {
              previewSelected.forEach((item) => {
                const value = this.getValueByLabel(item.label, element);
                if (value !== null) {
                  if (item.line === 1) assignData.description = value;
                  if (item.line === 2) assignData.line2 = value;
                }
              });
            }

            const statusStyles = {
              Pending: pending,
              Approved: approved,
              Declined: rejected,
              Expired: expired,
              Delivered: delivered,
              Completed: delivered,
            };

            if (element.status && statusStyles[element.status]) {
              const style = statusStyles[element.status];
              assignData.color = style.backgroundColor;
              assignData.textColor = style.fontColor;
              assignData.borderColor = element.status === 'Delivered' || element.status === 'Completed'
                ? style.backgroundColor
                : style.fontColor;
            } else if (!element.status) {
              assignData.className = 'calendar_event';
            }

            this.events.push(assignData);
          });
          this.calendarApi.removeAllEventSources();
          this.calendarApi.addEventSource(this.events);
        }
      });
  }

  public getValueByLabel(label, element) {
    switch (label) {
      case 'Description':
        return element.description;

      case 'Responsible Company':
        return element?.companyDetails?.[0]?.Company?.companyName || '';

      case 'Responsible Person': {
        const user = element?.memberDetails?.[0]?.Member?.User;
        return user ? `${user.firstName} ${user.lastName}` : '';
      }

      case 'Crane Pick ID':
        return element.CraneRequestId;

      case 'Definable Feature Of Work':
        return element.defineWorkDetails?.[0]?.DeliverDefineWork?.DFOW || '';

      case 'Equipment':
        return element.equipmentDetails?.[0]?.Equipment?.equipmentName;

      case 'Picking From':
        return element.pickUpLocation;

      case 'Picking To':
        return element.dropOffLocation;

      default:
        return null;
    }
  }

  public close(): void {
    this.submitted = false;
    this.formSubmitted = false;
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public openIdModal(item): void {
    if (
      !this.eventData.isAssociatedWithDeliveryRequest
      && !this.eventData.isAssociatedWithCraneRequest
    ) {
      const newPayload = {
        id: item.CraneRequestId,
        ProjectId: item.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
        isGuestUser: this.isGuestUser,
      };
      const initialState = {
        data: newPayload,
        title: 'Modal with component',
      };
      this.modalRef = this.modalService.show(GuestCraneDetailsComponent, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal',
        initialState,
      });
    } else {
      const newPayload = {
        id: item.id,
        ProjectId: item.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
        isGuestUser: this.isGuestUser,
      };
      this.deliveryService.updatedCurrentStatus(item.id);
      const initialState = {
        data: newPayload,
        title: 'Modal with component',
      };
      this.modalRef = this.modalService.show(DeliveryDetailsNewComponent, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal',
        initialState,
      });
    }
    this.modalRef.content.closeBtnName = 'Close';
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      companyFilter: [''],
      descriptionFilter: [''],
      statusFilter: [''],
      memberFilter: [''],
      gateFilter: [''],
      equipmentFilter: [''],
      locationFilter: [''],
      dateFilter: [''],
      pickFrom: [''],
      pickTo: [''],
    });
  }

  public calendarDescription(arg: { event?: any }): void {
    this.calendarDescriptionPopup = false;
    this.descriptionPopup = false;
    this.viewEventData = '';
    if (Object.keys(arg).length !== 0) {
      const index = this.events.findIndex(
        (item: any): any => item.description === arg.event.title
          && item.uniqueNumber === arg.event.extendedProps.uniqueNumber,
      );
      this.viewEventData = this.deliveryRequestWithCraneEquipmentType[index];
      this.occurMessage(this.viewEventData);
      this.calendarDescriptionPopup = true;
    }
  }

  public occurMessage(data: {
    repeatEveryType: string;
    repeatEveryCount: string | number;
    days: any[];
    chosenDateOfMonth: any;
    dateOfMonth: any;
    monthlyRepeatType: any;
    endTime: moment.MomentInput;
  }): void {
    this.message = 'Occurs every day';
    if (data.repeatEveryType === 'Day') {
      this.message = '';
      this.message = 'Occurs every day';
    }
    if (data.repeatEveryType === 'Days') {
      this.message = '';
      if (+data.repeatEveryCount === 2) {
        this.message = 'Occurs every other day';
      } else {
        this.message = `Occurs every ${data.repeatEveryCount} days`;
      }
    }
    if (data.repeatEveryType === 'Week') {
      this.message = '';
      let weekDays = '';
      data.days.forEach((day1: any): any => {
        weekDays = `${weekDays + day1},`;
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message += `Occurs every ${weekDays}`;
    }
    if (data.repeatEveryType === 'Weeks') {
      let weekDays = '';
      data.days.forEach((day1: any): any => {
        weekDays = `${weekDays + day1},`;
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message = '';
      if (+data.repeatEveryCount === 2) {
        this.message = `Occurs every other  ${weekDays}`;
      } else {
        this.message = `Occurs every ${data.repeatEveryCount} weeks on ${weekDays}`;
      }
    }
    if (
      data.repeatEveryType === 'Month'
      || data.repeatEveryType === 'Months'
      || data.repeatEveryType === 'Year'
      || data.repeatEveryType === 'Years'
    ) {
      if (data.chosenDateOfMonth) {
        this.message = `Occurs on day ${data.dateOfMonth}`;
      } else {
        this.message = `Occurs on the ${data.monthlyRepeatType}`;
      }
    }
    this.message += ` until ${moment(data.endTime).format('MM-DD-YYYY')}`;
  }

  public closeCalendarDescription(): void {
    this.calendarDescriptionPopup = false;
    this.descriptionPopup = false;
    this.viewEventData = '';
  }

  public changeFormat(fromDate: any): any {
    if (fromDate) {
      const dayFormat = moment(new Date(fromDate)).format('ddd MM/DD/YYYY');
      return dayFormat;
    }
  }
}
