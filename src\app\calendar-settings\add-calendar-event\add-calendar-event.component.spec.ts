import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AddCalendarEventComponent } from './add-calendar-event.component';
import { UntypedFormBuilder, UntypedFormArray, UntypedFormControl } from '@angular/forms';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { ProjectService } from '../../services/profile/project.service';
import { CalendarService } from '../../services/profile/calendar.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { of, throwError, Subject } from 'rxjs';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import moment from 'moment';

// Mocks
class MockBsModalRef {
  hide = jest.fn();
}
class MockBsModalService {
  show = jest.fn();
}
class MockToastrService {
  error = jest.fn();
  success = jest.fn();
}
class MockProjectService {
  projectParent = new Subject();
  getLocations = jest.fn(() => of({ data: [{ id: 1, gateDetails: [], EquipmentId: [], TimeZoneId: [{ location: 'TZ' }] }] }));
  getTimeZoneList = jest.fn(() => of({ data: [{ id: 1, location: 'TZ' }] }));
  getSingleProject = jest.fn(() => of({ data: { TimeZoneId: 1 } }));
}
class MockCalendarService {
  addCalendarEvent = jest.fn(() => of({ message: 'Success' }));
  updateCalendarEvents = jest.fn();
}
class MockDeliveryService {
  loginUser = new Subject();
}

describe('AddCalendarEventComponent', () => {
  let component: AddCalendarEventComponent;
  let fixture: ComponentFixture<AddCalendarEventComponent>;
  let modalRef: MockBsModalRef;
  let modalService: MockBsModalService;
  let toastr: MockToastrService;
  let projectService: MockProjectService;
  let calendarService: MockCalendarService;
  let deliveryService: MockDeliveryService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [AddCalendarEventComponent],
      providers: [
        UntypedFormBuilder,
        { provide: BsModalRef, useClass: MockBsModalRef },
        { provide: BsModalService, useClass: MockBsModalService },
        { provide: ToastrService, useClass: MockToastrService },
        { provide: ProjectService, useClass: MockProjectService },
        { provide: CalendarService, useClass: MockCalendarService },
        { provide: DeliveryService, useClass: MockDeliveryService },
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  });

  class MockTemplateRef {
  elementRef = {};
  createEmbeddedView = jest.fn();
}

  beforeEach(() => {
    fixture = TestBed.createComponent(AddCalendarEventComponent);
    component = fixture.componentInstance;
    modalRef = TestBed.inject(BsModalRef) as any;
    modalService = TestBed.inject(BsModalService) as any;
    toastr = TestBed.inject(ToastrService) as any;
    projectService = TestBed.inject(ProjectService) as any;
    calendarService = TestBed.inject(CalendarService) as any;
    deliveryService = TestBed.inject(DeliveryService) as any;
    // Set up required form
    component.addCalendarEvent();
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call ngOnInit and get locations', () => {
    const spy = jest.spyOn(component, 'getLocations');
    component.ngOnInit();
    expect(spy).toHaveBeenCalled();
  });

  it('should toggle all day', () => {
    const resetSpy = jest.spyOn(component, 'resetTimer');
    const setSpy = jest.spyOn(component, 'setCurrentTiming');
    component.toggleAllDay(true);
    expect(resetSpy).toHaveBeenCalled();
    component.toggleAllDay(false);
    expect(setSpy).toHaveBeenCalled();
  });

  it('should select time zone', () => {
    component.timezoneList = [{ id: 1, location: 'TZ' }] as any;
    component.timeZoneSelected(1);
    expect(component.selectedValue).toEqual({ id: 1, location: 'TZ' });
  });

  it('should get locations and set dropdowns', () => {
    component.ProjectId = 1;
    component.ParentCompanyId = 1;
    component.getLocations();
    expect(component.locationList).toBeDefined();
    expect(component.gateList).toBeDefined();
    expect(component.equipmentList).toBeDefined();
    expect(component.equipmentDropdownSettings).toBeDefined();
    expect(component.gateDropdownSettings).toBeDefined();
    expect(component.locationDropdownSettings).toBeDefined();
  });

  it('should get timezone list and handle project', () => {
    component.ProjectId = 1;
    component.getTimeZoneList();
    expect(projectService.getTimeZoneList).toHaveBeenCalled();
  });

  it('should handle timezone list error', () => {
    projectService.getTimeZoneList = jest.fn(() => throwError({ message: { statusCode: 400, details: [{ message: 'err' }] } }));
    component.getTimeZoneList();
    expect(toastr.error).toHaveBeenCalled();
  });

  it('should get selected date for Month, Week, Day', () => {
    component.data = { date: '2024-01-01', currentView: 'Month' };
    component.getSelectedDate();
    component.data = { date: '2024-01-01', currentView: 'Week' };
    component.getSelectedDate();
    component.data = { date: '2024-01-01', currentView: 'Day' };
    component.getSelectedDate();
    expect(component.calendarEvent.get('fromDate').value).toBeDefined();
  });

  it('should handle onRecurrenceSelect for all branches', () => {
    component.calendarEvent.get('repeatEveryCount').setValue(2);
    component.onRecurrenceSelect('Daily');
    component.onRecurrenceSelect('Weekly');
    component.calendarEvent.get('repeatEveryCount').setValue(1);
    component.onRecurrenceSelect('Weekly');
    component.onRecurrenceSelect('Daily');
    component.onRecurrenceSelect('Monthly');
    component.onRecurrenceSelect('Yearly');
    component.calendarEvent.get('repeatEveryCount').setValue(2);
    component.onRecurrenceSelect('Monthly');
    component.onRecurrenceSelect('Yearly');
    expect(component.selectedRecurrence).toBeDefined();
  });

  it('should set form value for all recurrence types', () => {
    component.setFormValue('Does Not Repeat');
    component.setFormValue('Daily');
    component.setFormValue('Weekly');
    component.setFormValue('Monthly');
    component.setFormValue('Yearly');
    expect(component.calendarEvent.get('repeatEveryType').value).toBeDefined();
  });

  it('should show monthly recurrence', () => {
    component.calendarEvent.get('fromDate').setValue('2024-01-01');
    component.calendarEvent.get('chosenDateOfMonth').setValue(3);
    component.showMonthlyRecurrence();
    expect(component.monthlyDayOfWeek).toBeDefined();
  });

  it('should change monthly recurrence', () => {
    const spy = jest.spyOn(component, 'showMonthlyRecurrence');
    component.changeMonthlyRecurrence();
    expect(spy).toHaveBeenCalled();
  });

  it('should set monthly or yearly recurrence option', () => {
    component.calendarEvent.get('fromDate').setValue('2024-01-01');
    component.monthlyDayOfWeek = 'Monday';
    component.monthlyLastDayOfWeek = 'Last Monday';
    component.calendarEvent.get('chosenDateOfMonth').setValue(1);
    component.setMonthlyOrYearlyRecurrenceOption();
    component.calendarEvent.get('chosenDateOfMonth').setValue(2);
    component.setMonthlyOrYearlyRecurrenceOption();
    component.calendarEvent.get('chosenDateOfMonth').setValue(3);
    component.setMonthlyOrYearlyRecurrenceOption();
    expect(component.calendarEvent.get('dateOfMonth').value).toBeDefined();
  });

  it('should choose repeat every type for all branches', () => {
    component.calendarEvent.get('repeatEveryCount').setValue(2);
    component.chooseRepeatEveryType('Day');
    component.chooseRepeatEveryType('Week');
    component.chooseRepeatEveryType('Month');
    component.chooseRepeatEveryType('Year');
    component.chooseRepeatEveryType('Days');
    component.chooseRepeatEveryType('Weeks');
    component.chooseRepeatEveryType('Months');
    component.chooseRepeatEveryType('Years');
    expect(component.selectedRecurrence).toBeDefined();
  });

  it('should handle onChange for checked and unchecked events', () => {
    component.selectedRecurrence = 'Weekly';
    component.weekDays = [
      { value: 'Monday', checked: false, isDisabled: false },
      { value: 'Tuesday', checked: false, isDisabled: false },
    ];
    component.calendarEvent.setControl('days', new UntypedFormArray([new UntypedFormControl('Monday')]));
    component.onChange({ target: { value: 'Tuesday', checked: true } });
    component.onChange({ target: { value: 'Monday', checked: false } });
    component.selectedRecurrence = 'Daily';
    component.calendarEvent.setControl('days', new UntypedFormArray([new UntypedFormControl('Monday'), new UntypedFormControl('Tuesday')]));
    component.onChange({ target: { value: 'Tuesday', checked: false } });
    expect(component.selectedRecurrence).toBeDefined();
  });

  it('should change recurrence count for all branches', () => {
    component.calendarEvent.get('recurrence').setValue('Daily');
    component.changeRecurrenceCount(1);
    component.changeRecurrenceCount(2);
    component.calendarEvent.get('recurrence').setValue('Weekly');
    component.changeRecurrenceCount(1);
    component.changeRecurrenceCount(2);
    component.calendarEvent.get('recurrence').setValue('Monthly');
    component.changeRecurrenceCount(1);
    component.changeRecurrenceCount(2);
    component.calendarEvent.get('recurrence').setValue('Yearly');
    component.changeRecurrenceCount(1);
    component.changeRecurrenceCount(2);
    expect(component.selectedRecurrence).toBeDefined();
  });

  it('should generate repeat message for all types', () => {
    component.calendarEvent.get('repeatEveryType').setValue('Day');
    component.calendarEvent.get('repeatEveryCount').setValue(1);
    expect(component.generateRepeatMessage()).toContain('Occurs every day');
    component.calendarEvent.get('repeatEveryType').setValue('Days');
    component.calendarEvent.get('repeatEveryCount').setValue(2);
    expect(component.generateRepeatMessage()).toContain('Occurs every other day');
    component.calendarEvent.get('repeatEveryType').setValue('Week');
    component.weekDays = [{ value: 'Monday', checked: true }];
    expect(component.generateRepeatMessage()).toContain('Occurs every Monday');
    component.calendarEvent.get('repeatEveryType').setValue('Weeks');
    component.calendarEvent.get('repeatEveryCount').setValue(3);
    expect(component.generateRepeatMessage()).toContain('Occurs every 3 weeks on Monday');
    component.calendarEvent.get('repeatEveryType').setValue('Month');
    component.calendarEvent.get('chosenDateOfMonth').setValue(1);
    component.monthlyDate = '01';
    expect(component.generateRepeatMessage()).toContain('Occurs on day 01');
    component.calendarEvent.get('repeatEveryType').setValue('Months');
    component.calendarEvent.get('chosenDateOfMonth').setValue(2);
    component.monthlyDayOfWeek = 'Monday';
    expect(component.generateRepeatMessage()).toContain('Occurs on the Monday');
    component.calendarEvent.get('repeatEveryType').setValue('Year');
    component.calendarEvent.get('chosenDateOfMonth').setValue(3);
    component.monthlyLastDayOfWeek = 'Last Monday';
    expect(component.generateRepeatMessage()).toContain('Occurs on the Last Monday');
  });

  it('should append end date message for all types', () => {
    component.message = 'msg';
    component.calendarEvent.get('recurrence').setValue('Daily');
    component.calendarEvent.get('toDate').setValue('2024-01-01');
    component.appendEndDateMessage();
    component.calendarEvent.get('recurrence').setValue('Weekly');
    component.calendarEvent.get('endDate').setValue('2024-01-01');
    component.appendEndDateMessage();
    component.calendarEvent.get('recurrence').setValue('Monthly');
    component.appendEndDateMessage();
    component.calendarEvent.get('recurrence').setValue('Yearly');
    component.appendEndDateMessage();
    expect(component.message).toBeDefined();
  });

  it('should allow only numbers', () => {
    expect(component.numberOnly({ which: 48 })).toBe(true);
    expect(component.numberOnly({ which: 65 })).toBe(false);
  });

  it('should close and reset form', () => {
    const openSpy = jest.spyOn(component, 'openConfirmationModalPopup');
    const resetSpy = jest.spyOn(component, 'resetForm');
    component.calendarEvent.markAsTouched();
    component.calendarEvent.markAsDirty();
    const templateRef = new MockTemplateRef() as any;
    component.close(templateRef);
    expect(openSpy).toHaveBeenCalled();
    component.calendarEvent.markAsUntouched();
    component.calendarEvent.markAsPristine();
    component.close(templateRef);
    expect(resetSpy).toHaveBeenCalled();
  });

  it('should open confirmation modal popup', () => {
    const templateRef = new MockTemplateRef() as any;
    component.openConfirmationModalPopup(templateRef);
    expect(modalService.show).toHaveBeenCalled();
  });

  it('should reset form for both actions', () => {
    const templateRef = new MockTemplateRef() as any;
    component.openConfirmationModalPopup(templateRef);
    component.resetForm('no');
    component.resetForm('yes');
    expect(component.selectedRecurrence).toBe('Does Not Repeat');
  });

  it('should submit invalid form', () => {
    component.calendarEvent.get('description').setValue('');
    component.onSubmit();
    expect(component.formSubmitted).toBe(false);
  });

  it('should submit form with no applicable checkboxes', () => {
    component.calendarEvent.get('description').setValue('desc');
    component.calendarEvent.get('fromDate').setValue('2024-01-01');
    component.calendarEvent.get('toDate').setValue('2024-01-02');
    component.calendarEvent.get('isApplicableToDelivery').setValue(false);
    component.calendarEvent.get('isApplicableToCrane').setValue(false);
    component.calendarEvent.get('isApplicableToConcrete').setValue(false);
    component.calendarEvent.get('isApplicableToInspection').setValue(false);
    component.onSubmit();
    expect(component.formSubmitted).toBe(false);
  });

  it('should submit form with invalid time', () => {
    component.calendarEvent.get('description').setValue('desc');
    component.calendarEvent.get('fromDate').setValue('2024-01-01');
    component.calendarEvent.get('toDate').setValue('2024-01-02');
    component.calendarEvent.get('isApplicableToDelivery').setValue(true);
    component.calendarEvent.get('isApplicableToCrane').setValue(false);
    component.calendarEvent.get('isApplicableToConcrete').setValue(false);
    component.calendarEvent.get('isApplicableToInspection').setValue(false);
    component.calendarEvent.get('isAllDay').setValue(false);
    component.calendarEvent.get('startTime').setValue('2024-01-01T10:00:00');
    component.calendarEvent.get('endTime').setValue('2024-01-01T10:00:00');
    component.onSubmit();
    expect(toastr.error).toHaveBeenCalled();
    component.calendarEvent.get('endTime').setValue('2024-01-01T09:00:00');
    component.onSubmit();
    expect(toastr.error).toHaveBeenCalled();
  });

  it('should submit form with invalid date', () => {
    component.calendarEvent.get('description').setValue('desc');
    component.calendarEvent.get('fromDate').setValue('2020-01-01');
    component.calendarEvent.get('toDate').setValue('2020-01-02');
    component.calendarEvent.get('isApplicableToDelivery').setValue(true);
    component.calendarEvent.get('isApplicableToCrane').setValue(false);
    component.calendarEvent.get('isApplicableToConcrete').setValue(false);
    component.calendarEvent.get('isApplicableToInspection').setValue(false);
    component.calendarEvent.get('isAllDay').setValue(true);
    component.onSubmit();
    expect(toastr.error).toHaveBeenCalled();
  });

  it('should submit form with recurrence and invalid end date', () => {
    component.calendarEvent.get('description').setValue('desc');
    component.calendarEvent.get('fromDate').setValue('2024-01-01');
    component.calendarEvent.get('toDate').setValue('2024-01-02');
    component.calendarEvent.get('endDate').setValue('2020-01-01');
    component.calendarEvent.get('isApplicableToDelivery').setValue(true);
    component.calendarEvent.get('isApplicableToCrane').setValue(false);
    component.calendarEvent.get('isApplicableToConcrete').setValue(false);
    component.calendarEvent.get('isApplicableToInspection').setValue(false);
    component.calendarEvent.get('isAllDay').setValue(true);
    component.calendarEvent.get('recurrence').setValue('Weekly');
    component.onSubmit();
    expect(toastr.error).toHaveBeenCalled();
  });

  it('should submit valid form and call processValidForm', () => {
    const spy = jest.spyOn(component, 'processValidForm');
    component.calendarEvent.get('description').setValue('desc');
    component.calendarEvent.get('fromDate').setValue('2024-12-01');
    component.calendarEvent.get('toDate').setValue('2024-12-02');
    component.calendarEvent.get('isApplicableToDelivery').setValue(true);
    component.calendarEvent.get('isApplicableToCrane').setValue(false);
    component.calendarEvent.get('isApplicableToConcrete').setValue(false);
    component.calendarEvent.get('isApplicableToInspection').setValue(false);
    component.calendarEvent.get('isAllDay').setValue(true);
    component.onSubmit();
    expect(spy).toHaveBeenCalled();
  });

  it('should process valid form and handle all branches', () => {
    component.authUser = { RoleId: 1 };
    const formValue = {
      description: 'desc',
      fromDate: '2024-12-01',
      toDate: '2024-12-02',
      startTime: '2024-12-01T10:00:00',
      endTime: '2024-12-01T11:00:00',
      TimeZoneId: [{ id: 1 }],
      GateId: [{ id: 1 }],
      LocationId: [{ id: 1 }],
      EquipmentId: [{ id: 1 }],
      isAllDay: true,
      isApplicableToDelivery: true,
      isApplicableToCrane: false,
      isApplicableToConcrete: false,
      isApplicableToInspection: false,
      recurrence: 'Monthly',
      chosenDateOfMonth: 1,
      dateOfMonth: '01',
      monthlyRepeatType: 'Monday',
      days: ['Monday'],
      repeatEveryType: 'Month',
      repeatEveryCount: 1,
      endDate: '2024-12-02',
    };
    component.ProjectId = 1;
    component.ParentCompanyId = 1;
    component.monthlyDate = '01';
    component.processValidForm(formValue);
    expect(calendarService.addCalendarEvent).toHaveBeenCalled();
  });

  it('should handle addCalendarEvent error', () => {
    calendarService.addCalendarEvent = jest.fn(() => throwError({ message: { statusCode: 400, details: [{ message: 'err' }] } }));
    component.authUser = { RoleId: 1 };
    const formValue = {
      description: 'desc',
      fromDate: '2024-12-01',
      toDate: '2024-12-02',
      startTime: '2024-12-01T10:00:00',
      endTime: '2024-12-01T11:00:00',
      TimeZoneId: [{ id: 1 }],
      GateId: [{ id: 1 }],
      LocationId: [{ id: 1 }],
      EquipmentId: [{ id: 1 }],
      isAllDay: true,
      isApplicableToDelivery: true,
      isApplicableToCrane: false,
      isApplicableToConcrete: false,
      isApplicableToInspection: false,
      recurrence: 'Monthly',
      chosenDateOfMonth: 1,
      dateOfMonth: '01',
      monthlyRepeatType: 'Monday',
      days: ['Monday'],
      repeatEveryType: 'Month',
      repeatEveryCount: 1,
      endDate: '2024-12-02',
    };
    component.ProjectId = 1;
    component.ParentCompanyId = 1;
    component.monthlyDate = '01';
    component.processValidForm(formValue);
    expect(toastr.error).toHaveBeenCalled();
  });

  it('should sort week days', () => {
    expect(component.sortWeekDays(['Monday', 'Sunday', 'Saturday'])).toEqual(['Sunday', 'Monday', 'Saturday']);
  });

  it('should show error', () => {
    component.showError({ message: { details: [{ message: 'err' }] } });
    expect(toastr.error).toHaveBeenCalled();
  });

  it('should reset timer', () => {
    component.resetTimer();
    expect(component.calendarEvent.get('startTime').value).toBeDefined();
  });

  it('should set current timing', () => {
    component.setCurrentTiming();
    expect(component.calendarEvent.get('startTime').value).toBeDefined();
  });

  it('should update form validation', () => {
    component.calendarEvent.get('chosenDateOfMonth').setValue(1);
    component.updateFormValidation();
    component.calendarEvent.get('chosenDateOfMonth').setValue(2);
    component.updateFormValidation();
    expect(component.calendarEvent.get('chosenDateOfMonth').value).toBeDefined();
  });

  it('should select location', () => {
    component.locationList = [{ id: 1, gateDetails: [{ id: 1 }], EquipmentId: [{ id: 1 }], TimeZoneId: [{ location: 'TZ' }] }];
    component.calendarEvent.get('GateId').setValue('');
    component.calendarEvent.get('EquipmentId').setValue('');
    component.locationSelected({ id: 1 });
    expect(component.selectedLocationId).toBe(1);
  });

  it('should handle form control value changed', () => {
    component.formControlValueChanged();
    expect(component.calendarEvent.get('startTime')).toBeDefined();
  });
});
