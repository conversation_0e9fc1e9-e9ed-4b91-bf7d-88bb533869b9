import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { HTTP_INTERCEPTORS, HttpClient, HttpErrorResponse } from '@angular/common/http';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { of } from 'rxjs';

import { AuthInterceptor } from './auth.interceptor';
import { LogoutAlertComponent } from '../logout-alert/logout-alert.component';

describe('AuthInterceptor', () => {
  let httpClient: HttpClient;
  let httpTestingController: HttpTestingController;
  let modalService: jest.Mocked<BsModalService>;
  let bsModalRef: jest.Mocked<BsModalRef>;

  beforeEach(() => {
    // Create mocks
    const modalRefMock = {
      onHidden: of({}),
      onHide: of({})
    };

    const modalServiceMock = {
      show: jest.fn().mockReturnValue(modalRefMock)
    };

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        {
          provide: HTTP_INTERCEPTORS,
          useClass: AuthInterceptor,
          multi: true
        },
        {
          provide: BsModalService,
          useValue: modalServiceMock
        },
        {
          provide: BsModalRef,
          useValue: modalRefMock
        }
      ]
    });

    httpClient = TestBed.inject(HttpClient);
    httpTestingController = TestBed.inject(HttpTestingController);
    modalService = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    bsModalRef = TestBed.inject(BsModalRef) as jest.Mocked<BsModalRef>;
  });

  afterEach(() => {
    // Verify that no unmatched requests are outstanding
    httpTestingController.verify();
  });

  it('should be created', () => {
    const interceptor = TestBed.inject(HTTP_INTERCEPTORS).find(
      (interceptor) => interceptor instanceof AuthInterceptor
    );
    expect(interceptor).toBeTruthy();
  });

  it('should pass through non-error responses', () => {
    const testData = { data: 'test data' };

    // Make an HTTP GET request
    httpClient.get('/api/test').subscribe(response => {
      expect(response).toEqual(testData);
    });

    // The following `expectOne()` will match the request's URL
    const req = httpTestingController.expectOne('/api/test');

    // Respond with mock data
    req.flush(testData);
  });

  it('should show logout modal on 401 error', () => {
    // Mock document.querySelectorAll to return an array with a mock element
    document.querySelectorAll = jest.fn().mockReturnValue([
      { style: { display: 'block' } }
    ]);

    // Make an HTTP GET request
    httpClient.get('/api/test').subscribe({
      next: () => fail('should have failed with 401 error'),
      error: (error: HttpErrorResponse) => {
        expect(error.status).toBe(401);
        expect(modalService.show).toHaveBeenCalledWith(LogoutAlertComponent, {
          backdrop: 'static',
          keyboard: false,
        });
      }
    });

    // The following `expectOne()` will match the request's URL
    const req = httpTestingController.expectOne('/api/test');

    // Respond with a 401 error
    req.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' });
  });

  it('should not show multiple modals for consecutive 401 errors', () => {
    // Make first HTTP request that will return 401
    httpClient.get('/api/test1').subscribe({
      error: () => {
        // The modal should be shown for the first 401 error
        expect(modalService.show).toHaveBeenCalledTimes(1);

        // Make a second HTTP request that will also return 401
        httpClient.get('/api/test2').subscribe({
          error: () => {
            // The modal should still have been called only once
            expect(modalService.show).toHaveBeenCalledTimes(1);
          }
        });

        // Respond to the second request with a 401 error
        const req2 = httpTestingController.expectOne('/api/test2');
        req2.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' });
      }
    });

    // Respond to the first request with a 401 error
    const req1 = httpTestingController.expectOne('/api/test1');
    req1.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' });
  });

  it('should reset isModalShown flag when modal is hidden', () => {
    // Make an HTTP GET request that will return 401
    httpClient.get('/api/test').subscribe({
      error: () => {
        // Verify the modal was shown
        expect(modalService.show).toHaveBeenCalled();

        // Simulate the modal being hidden
        bsModalRef.onHidden.subscribe(() => {
          // Make another request that returns 401
          httpClient.get('/api/test2').subscribe({
            error: () => {
              // The modal should be shown again
              expect(modalService.show).toHaveBeenCalledTimes(2);
            }
          });

          // Respond to the second request with a 401 error
          const req2 = httpTestingController.expectOne('/api/test2');
          req2.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' });
        });
      }
    });

    // Respond to the first request with a 401 error
    const req1 = httpTestingController.expectOne('/api/test');
    req1.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' });
  });

  it('should pass through other error responses', () => {
    // Make an HTTP GET request
    httpClient.get('/api/test').subscribe({
      next: () => fail('should have failed with 500 error'),
      error: (error: HttpErrorResponse) => {
        expect(error.status).toBe(500);
        // Modal should not be shown for non-401 errors
        expect(modalService.show).not.toHaveBeenCalled();
      }
    });

    // The following `expectOne()` will match the request's URL
    const req = httpTestingController.expectOne('/api/test');

    // Respond with a 500 error
    req.flush('Server error', { status: 500, statusText: 'Server Error' });
  });
});
