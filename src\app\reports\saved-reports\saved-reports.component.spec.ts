import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SavedReportsComponent } from './saved-reports.component';
import { ReportsService } from '../../services/reports/reports.service';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';
import { BsModalService } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { of, throwError } from 'rxjs';
import { NO_ERRORS_SCHEMA, Pipe, PipeTransform } from '@angular/core';

// Mock paginate pipe
@Pipe({
  name: 'paginate'
})
class MockPaginatePipe implements PipeTransform {
  transform(value: any[], config: any): any[] {
    return value;
  }
}

describe('SavedReportsComponent', () => {
  let component: SavedReportsComponent;
  let fixture: ComponentFixture<SavedReportsComponent>;
  let reportsServiceMock: jest.Mocked<ReportsService>;
  let modalServiceMock: jest.Mocked<BsModalService>;
  let toastrServiceMock: jest.Mocked<ToastrService>;

  const mockSavedReports = {
    data: {
      scheduledReports: [
        {
          id: 1,
          reportName: 'Test Report',
          reportType: 'PDF',
          outputFormat: 'PDF',
          lastRun: '2024-03-20',
          s3_url: 'http://example.com/report.pdf',
          createdUser: {
            firstName: 'John',
            lastName: 'Doe',
          },
          createdBy: 1,
        },
        {
          id: 2,
          reportName: 'Test Report 2',
          reportType: 'Excel',
          outputFormat: 'Excel',
          lastRun: '2024-03-21',
          createdUser: {
            firstName: 'Jane',
            lastName: 'Smith',
          },
          createdBy: 2,
        },
      ],
      count: 2,
    },
  };

  beforeEach(async () => {
    const mockReportsService = {
      getSavedReports: jest.fn(),
      deleteScheduledReports: jest.fn(),
      runNowScheduledOrSavedReport: jest.fn(),
    };

    const mockProjectService = {
      projectParent: of({ ProjectId: 1 }),
    };

    const mockDeliveryService = {
      loginUser: of({ UserId: 1, RoleId: 2 }),
    };

    const mockModalService = {
      show: jest.fn(),
    };

    const mockToastrService = {
      success: jest.fn(),
      error: jest.fn(),
      info: jest.fn(),
    };

    await TestBed.configureTestingModule({
      declarations: [SavedReportsComponent, MockPaginatePipe],
      providers: [
        { provide: ReportsService, useValue: mockReportsService },
        { provide: ProjectService, useValue: mockProjectService },
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: BsModalService, useValue: mockModalService },
        { provide: ToastrService, useValue: mockToastrService },
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();

    reportsServiceMock = TestBed.inject(ReportsService) as jest.Mocked<ReportsService>;
    modalServiceMock = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    toastrServiceMock = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
  });

  beforeEach(() => {
    // Setup default mock return values before component creation
    reportsServiceMock.getSavedReports.mockReturnValue(of(mockSavedReports));
    reportsServiceMock.deleteScheduledReports.mockReturnValue(of({ message: 'Deleted successfully' }));
    reportsServiceMock.runNowScheduledOrSavedReport.mockReturnValue(of({ data: 'http://example.com/report.pdf' }));

    fixture = TestBed.createComponent(SavedReportsComponent);
    component = fixture.componentInstance;

    fixture.detectChanges();
  });

  describe('Basic Component Tests', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should initialize with default values', () => {
      expect(component.pageSize).toBe(25);
      expect(component.pageNo).toBe(1);
      expect(component.sortColumn).toBe('id');
      expect(component.sort).toBe('DESC');
      expect(component.deleteSavedReportSubmitted).toBe(false);
    });

    it('should load saved reports on initialization', () => {
      expect(reportsServiceMock.getSavedReports).toHaveBeenCalled();
      expect(component.savedReports).toEqual(mockSavedReports.data.scheduledReports);
      expect(component.totalSavedCount).toBe(mockSavedReports.data.count);
    });
  });

  describe('Sorting and Pagination - Positive Cases', () => {
    it('should handle sort by field correctly', () => {
      component.sortByField('reportName', 'ASC');

      expect(component.sortColumn).toBe('reportName');
      expect(component.sort).toBe('ASC');
      expect(reportsServiceMock.getSavedReports).toHaveBeenCalled();
    });

    it('should handle page size change', () => {
      const newPageSize = 50;
      component.changePageSize(newPageSize);

      expect(component.pageSize).toBe(newPageSize);
      expect(reportsServiceMock.getSavedReports).toHaveBeenCalled();
    });

    it('should handle page number change', () => {
      const newPageNo = 3;
      component.changePageNo(newPageNo);

      expect(component.pageNo).toBe(newPageNo);
      expect(reportsServiceMock.getSavedReports).toHaveBeenCalled();
    });
  });

  describe('Modal Operations', () => {
    it('should open delete modal with correct parameters', () => {
      const mockTemplate = {} as any;
      const mockData = { id: 1, reportName: 'Test Report' };
      const mockModalRef = { hide: jest.fn() };

      modalServiceMock.show.mockReturnValue(mockModalRef as any);

      component.openDeleteModal(mockData, mockTemplate);

      expect(component.deleteSavedReportData).toEqual(mockData);
      expect(modalServiceMock.show).toHaveBeenCalledWith(mockTemplate, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-md new-gate-popup custom-modal',
      });
    });

    it('should reset and close modal', () => {
      component.modalRef = { hide: jest.fn() } as any;
      component.deleteSavedReportData = { id: 1, reportName: 'Test' };

      component.resetAndClose();

      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(component.deleteSavedReportData).toEqual({});
    });
  });

  describe('Delete Operations - Positive Cases', () => {
    it('should successfully delete a saved report', () => {
      const mockResponse = { message: 'Report deleted successfully' };
      reportsServiceMock.deleteScheduledReports.mockReturnValue(of(mockResponse));

      component.deleteSavedReportData = { id: 1 };
      component.ProjectId = 1;
      component.modalRef = { hide: jest.fn() } as any;

      component.deleteSavedReport();

      expect(reportsServiceMock.deleteScheduledReports).toHaveBeenCalledWith({
        id: 1,
        ProjectId: 1,
      });
      expect(toastrServiceMock.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(component.deleteSavedReportData).toEqual({});
    });
  });

  describe('Delete Operations - Negative Cases', () => {
    it('should handle delete error with status code 400', () => {
      const mockError = {
        message: {
          statusCode: 400,
          details: [{ error: 'Bad request error' }],
        },
      };

      reportsServiceMock.deleteScheduledReports.mockReturnValue(throwError(() => mockError));
      component.deleteSavedReportData = { id: 1 };
      component.ProjectId = 1;

      const showErrorSpy = jest.spyOn(component, 'showError');

      component.deleteSavedReport();

      expect(component.deleteSavedReportSubmitted).toBe(false);
      expect(showErrorSpy).toHaveBeenCalledWith(mockError);
    });

    it('should handle delete error without message', () => {
      const mockError = {};

      reportsServiceMock.deleteScheduledReports.mockReturnValue(throwError(() => mockError));
      component.deleteSavedReportData = { id: 1 };
      component.ProjectId = 1;

      component.deleteSavedReport();

      expect(component.deleteSavedReportSubmitted).toBe(false);
      expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should handle delete error with custom message', () => {
      const mockError = { message: 'Custom error message' };

      reportsServiceMock.deleteScheduledReports.mockReturnValue(throwError(() => mockError));
      component.deleteSavedReportData = { id: 1 };
      component.ProjectId = 1;

      component.deleteSavedReport();

      expect(component.deleteSavedReportSubmitted).toBe(false);
      expect(toastrServiceMock.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
    });
  });

  describe('Download Operations - Negative Cases', () => {
    it('should show info message when data is "No data found"', () => {
      const mockData = 'No data found';
      const mockAction = 'reRun';

      component.downloadSavedReport(mockData, mockAction);

      expect(toastrServiceMock.info).toHaveBeenCalledWith(
        'There are no events available within the scheduled date range'
      );
    });

    it('should show info message when data has no s3_url', () => {
      const mockData = { reportName: 'Test Report' };
      const mockAction = 'lastRun';

      component.downloadSavedReport(mockData, mockAction);

      expect(toastrServiceMock.info).toHaveBeenCalledWith(
        'There are no events available within the scheduled date range'
      );
    });

    it('should show info message when data is null', () => {
      const mockData = null;
      const mockAction = 'lastRun';

      component.downloadSavedReport(mockData, mockAction);

      expect(toastrServiceMock.info).toHaveBeenCalledWith(
        'There are no events available within the scheduled date range'
      );
    });
  });

  describe('Auto Download with Run Option - Positive Cases', () => {
    it('should handle auto download successfully', () => {
      const mockData = { id: 1, runNowLoader: false };
      const mockResponse = { data: 'http://example.com/report.pdf' };

      reportsServiceMock.runNowScheduledOrSavedReport.mockReturnValue(of(mockResponse));
      const downloadSpy = jest.spyOn(component, 'downloadSavedReport');

      component.autoDownloadReportWithRunOption(mockData);

      expect(reportsServiceMock.runNowScheduledOrSavedReport).toHaveBeenCalledWith({
        id: 1,
        ProjectId: 1,
      });
      expect(downloadSpy).toHaveBeenCalledWith(mockResponse.data, 'reRun');
      expect(mockData.runNowLoader).toBe(false);
    });
  });

  describe('Auto Download with Run Option - Negative Cases', () => {
    it('should handle auto download error with status code 400', () => {
      const mockData = { id: 1, runNowLoader: false };
      const mockError = {
        message: {
          statusCode: 400,
          details: [{ error: 'Bad request error' }],
        },
      };

      reportsServiceMock.runNowScheduledOrSavedReport.mockReturnValue(throwError(() => mockError));
      const showErrorSpy = jest.spyOn(component, 'showError');

      component.autoDownloadReportWithRunOption(mockData);

      expect(mockData.runNowLoader).toBe(false);
      expect(showErrorSpy).toHaveBeenCalledWith(mockError);
    });

    it('should handle auto download error without message', () => {
      const mockData = { id: 1, runNowLoader: false };
      const mockError = {};

      reportsServiceMock.runNowScheduledOrSavedReport.mockReturnValue(throwError(() => mockError));

      component.autoDownloadReportWithRunOption(mockData);

      expect(mockData.runNowLoader).toBe(false);
      expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });
  });

  describe('Keyboard Events', () => {
    it('should handle keyboard events for sorting with Enter key', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      const sortByFieldSpy = jest.spyOn(component, 'sortByField');

      component.handleToggleKeydown(mockEvent, 'reportName', 'ASC');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(sortByFieldSpy).toHaveBeenCalledWith('reportName', 'ASC');
    });

    it('should handle keyboard events for sorting with Space key', () => {
      const mockEvent = { key: ' ', preventDefault: jest.fn() } as any;
      const sortByFieldSpy = jest.spyOn(component, 'sortByField');

      component.handleToggleKeydown(mockEvent, 'lastRun', 'DESC');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(sortByFieldSpy).toHaveBeenCalledWith('lastRun', 'DESC');
    });

    it('should not handle keyboard events for sorting with other keys', () => {
      const mockEvent = { key: 'Tab', preventDefault: jest.fn() } as any;
      const sortByFieldSpy = jest.spyOn(component, 'sortByField');

      component.handleToggleKeydown(mockEvent, 'reportName', 'ASC');

      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      expect(sortByFieldSpy).not.toHaveBeenCalled();
    });

    it('should handle keyboard events for download action', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      const mockData = { id: 1 };
      const downloadSpy = jest.spyOn(component, 'downloadSavedReport');

      component.handleDownKeydown(mockEvent, mockData, 'lastRun', 'download');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(downloadSpy).toHaveBeenCalledWith(mockData, 'lastRun');
    });

    it('should handle keyboard events for delete action', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      const mockData = { id: 1 };
      const mockTemplate = {} as any;
      const openDeleteModalSpy = jest.spyOn(component, 'openDeleteModal');

      component.handleDownKeydown(mockEvent, mockData, mockTemplate, 'delete');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(openDeleteModalSpy).toHaveBeenCalledWith(mockData, mockTemplate);
    });

    it('should handle keyboard events for auto download action', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      const mockData = { id: 1, runNowLoader: false };
      const autoDownloadSpy = jest.spyOn(component, 'autoDownloadReportWithRunOption');

      component.handleDownKeydown(mockEvent, mockData, '', 'auto');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(autoDownloadSpy).toHaveBeenCalledWith(mockData);
    });
  });

  describe('Error Handling', () => {
    it('should show error message from error details', () => {
      const mockError = {
        message: {
          details: [{ errorField: 'Error message from details' }],
        },
      };

      component.showError(mockError);

      expect(toastrServiceMock.error).toHaveBeenCalledWith(['Error message from details']);
    });
  });

  describe('Data Loading Edge Cases', () => {
    it('should not load reports when ProjectId is not set', () => {
      component.ProjectId = null;
      reportsServiceMock.getSavedReports.mockClear();

      component.getSavedReports();

      expect(reportsServiceMock.getSavedReports).not.toHaveBeenCalled();
    });

    it('should handle empty response data', () => {
      const emptyResponse = { data: null };
      reportsServiceMock.getSavedReports.mockReturnValue(of(emptyResponse));

      component.getSavedReports();

      expect(component.loader).toBe(false);
      expect(component.savedReports).toEqual([]);
    });

    it('should handle response without data property', () => {
      const invalidResponse = {};
      reportsServiceMock.getSavedReports.mockReturnValue(of(invalidResponse));

      component.getSavedReports();

      expect(component.loader).toBe(false);
      expect(component.savedReports).toEqual([]);
    });

    it('should handle API error during data loading', () => {
      const mockError = { message: 'API Error' };
      reportsServiceMock.getSavedReports.mockReturnValue(throwError(() => mockError));

      component.getSavedReports();

      expect(component.loader).toBe(false);
      expect(toastrServiceMock.error).toHaveBeenCalledWith('API Error', 'OOPS!');
    });
  });

  describe('Payload Building', () => {
    it('should build basic payload without filters', () => {
      component.ProjectId = 1;
      component.pageSize = 25;
      component.pageNo = 1;
      component.sort = 'DESC';
      component.sortColumn = 'id';
      component.getSearchText = '';
      component.filterValues = null;

      const payload = component.buildReportPayload();

      expect(payload).toEqual({
        ProjectId: 1,
        pageSize: 25,
        pageNo: 1,
        sort: 'DESC',
        sortByField: 'id',
        isSaved: true,
        search: '',
      });
    });

    it('should build payload with all filters', () => {
      component.ProjectId = 1;
      component.pageSize = 50;
      component.pageNo = 2;
      component.sort = 'ASC';
      component.sortColumn = 'reportName';
      component.getSearchText = 'test search';
      component.filterValues = {
        createdUserId: 123,
        reportName: 'Test Report',
        templateType: 'PDF',
        lastRun: '2024-03-20',
      };

      const payload = component.buildReportPayload();

      expect(payload).toEqual({
        ProjectId: 1,
        pageSize: 50,
        pageNo: 2,
        sort: 'ASC',
        sortByField: 'reportName',
        isSaved: true,
        search: 'test search',
        createdUserId: 123,
        reportName: 'Test Report',
        templateType: 'PDF',
        lastRun: '2024-03-20',
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      });
    });

    it('should build payload with partial filters', () => {
      component.ProjectId = 1;
      component.filterValues = {
        createdUserId: 123,
        reportName: '',
        templateType: null,
        lastRun: undefined,
      };

      const payload = component.buildReportPayload();

      expect(payload.createdUserId).toBe(123);
      expect(payload.reportName).toBeUndefined();
      expect(payload.templateType).toBeUndefined();
      expect(payload.lastRun).toBeUndefined();
    });
  });

  describe('Component Lifecycle', () => {
    it('should call getSavedReports on ngOnChanges', () => {
      const getSavedReportsSpy = jest.spyOn(component, 'getSavedReports');

      component.ngOnChanges();

      expect(getSavedReportsSpy).toHaveBeenCalled();
    });
  });

  describe('Edge Cases and Boundary Tests', () => {
    it('should handle very large page numbers', () => {
      const largePageNo = 999999;
      component.changePageNo(largePageNo);

      expect(component.pageNo).toBe(largePageNo);
      expect(reportsServiceMock.getSavedReports).toHaveBeenCalled();
    });

    it('should handle very large page sizes', () => {
      const largePageSize = 10000;
      component.changePageSize(largePageSize);

      expect(component.pageSize).toBe(largePageSize);
      expect(reportsServiceMock.getSavedReports).toHaveBeenCalled();
    });

    it('should handle empty search text', () => {
      component.getSearchText = '';
      const payload = component.buildReportPayload();

      expect(payload.search).toBe('');
    });

    it('should handle special characters in search text', () => {
      component.getSearchText = '!@#$%^&*()';
      const payload = component.buildReportPayload();

      expect(payload.search).toBe('!@#$%^&*()');
    });

    it('should handle undefined deleteSavedReportData', () => {
      component.deleteSavedReportData = undefined;
      component.ProjectId = 1;

      component.deleteSavedReport();

      expect(toastrServiceMock.error).toHaveBeenCalledWith('Invalid report data', 'Error');
      expect(reportsServiceMock.deleteScheduledReports).not.toHaveBeenCalled();
    });

    it('should handle null ProjectId in delete operation', () => {
      component.deleteSavedReportData = { id: 1 };
      component.ProjectId = null;

      component.deleteSavedReport();

      expect(reportsServiceMock.deleteScheduledReports).toHaveBeenCalledWith({
        id: 1,
        ProjectId: null,
      });
    });
  });

  describe('Multiple Sort Operations', () => {
    it('should handle multiple sort operations in sequence', () => {
      // First sort
      component.sortByField('reportName', 'ASC');
      expect(component.sortColumn).toBe('reportName');
      expect(component.sort).toBe('ASC');

      // Second sort
      component.sortByField('lastRun', 'DESC');
      expect(component.sortColumn).toBe('lastRun');
      expect(component.sort).toBe('DESC');

      // Third sort
      component.sortByField('createdBy', 'ASC');
      expect(component.sortColumn).toBe('createdBy');
      expect(component.sort).toBe('ASC');
    });
  });

  describe('Complex Filter Scenarios', () => {
    it('should handle mixed valid and invalid filter values', () => {
      component.filterValues = {
        createdUserId: 123,
        reportName: '',
        templateType: 'PDF',
        lastRun: null,
        invalidField: 'should be ignored',
      };

      const payload = component.buildReportPayload();

      expect(payload.createdUserId).toBe(123);
      expect(payload.templateType).toBe('PDF');
      expect(payload.reportName).toBeUndefined();
      expect(payload.lastRun).toBeUndefined();
      expect(payload.invalidField).toBeUndefined();
    });
  });
});
