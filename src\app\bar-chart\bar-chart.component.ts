/* eslint-disable max-lines-per-function */
import * as Highcharts from 'highcharts';
import boost from 'highcharts/modules/boost';
import nodata from 'highcharts/modules/no-data-to-display';
import more from 'highcharts/highcharts-more';

import { Component, Input, OnInit } from '@angular/core';

boost(Highcharts);
nodata(Highcharts);
more(Highcharts);
nodata(Highcharts);

@Component({
  selector: 'app-bar-chart',
  templateUrl: './bar-chart.component.html',
  })
export class BarChartComponent implements OnInit {
  public barChartData: any;

  @Input() public baroptions: any;

  public ngOnInit(): void { /* */ }

  public ngOnChanges(): void {
    this.fetchData();
  }

  public fetchData(): void {
    if (this.baroptions) {
      this.barChartData = {
        chart: {
          type: 'column',
          height: '190px',

          marginBottom: 30,
        },
        credits: {
          enabled: false,
        },
        title: {
          text: this.baroptions.title,
        },
        xAxis: {
          categories: this.baroptions.categories,
          crosshair: true,
          labels: {
            style: {
              color: '#43425D',
            },
          },
        },
        yAxis: {
          min: 0,
          title: {
            text: '',
          },
          labels: {
            style: {
              color: '#43425D',
            },
          },
        },
        tooltip: {
          headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
          pointFormat:
            '<tr><td style="color:{series.color};padding:0">{series.name}: </td>'
            + '<td style="padding:0"><b>{point.y:.0f}</b></td></tr>',
          footerFormat: '</table>',
          shared: true,
          useHTML: true,
        },
        plotOptions: {
          column: {
            pointPadding: 0.2,
            borderWidth: 0,
          },
          series: {
            pointWidth: 6,
          },
        },
        series: [
          {
            name: 'Delivery Bookings',
            data: this.baroptions.data,
            color: '#28A2F4',
          },
          {
            name: 'Crane Bookings',
            data: this.baroptions.data1,
            color: '#FFCE2D',
          },
          {
            name: 'Concrete Bookings',
            data: this.baroptions.data2,
            color: '#838383',
          },
          {
            name: 'Inspection Bookings',
            data: this.baroptions.data3,
            color: '#A234FD',
          },
        ],
      };
      Highcharts.chart('containers', this.barChartData);
    }
  }
}
