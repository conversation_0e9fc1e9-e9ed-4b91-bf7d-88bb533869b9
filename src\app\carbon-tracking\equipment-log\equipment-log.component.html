<section class="page-section ">
  <div class="page-inner-content">
  <div class="top-header my-3">
      <div class="row pt-md-15px">
        <div class="col-md-8">
          <div class="top-btn">
            <ul class="list-group list-group-horizontal">
              <li class="list-group-item p0 border-0 bg-transparent me-4">
                <button class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular" (click)="openModal(newequipment)">New Equipment Log</button>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent">
                <button class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular" (click)="openDeleteModal(-1,deleteList)" [disabled]="checkSelectedRow()">Remove</button>
              </li>
            </ul>
          </div>
        </div>
        <div class="col-md-4">
          <div class="top-filter">
            <ul class="list-group list-group-horizontal justify-content-end">
              <li class="list-group-item p0 border-0 bg-transparent me-2">
                <div class="search-icon">
                  <input class="form-control fs12 color-grey8" [ngClass]="showSearchbar ? 'input-hover-disable':
                  'input-search'" placeholder="What are you looking for?" (input)="getSearchEquipment($event.target.value)" [(ngModel)]="search">
                  <div class="icon">
                    <img src="./assets/images/cross-close.svg" *ngIf="showSearchbar" (click)="clear()" alt="close-cross" (keydown)="handleDownKeydown($event, '','','clear')">
                    <em class="fa fa-search fs12 color-grey8" *ngIf="!showSearchbar"></em>
                  </div>
                </div>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent me-2 position-relative">
                <div class="filter-icon" (click)="openFilterModal(filter)"  (keydown)="handleDownKeydown($event, filter,'','filter')"><img src="./assets/images/filter.svg" class="h-12px icon" alt="Filter"></div>
                <div class="bg-orange rounded-circle position-absolute text-white filter-count" *ngIf="filterCount>0"><p class="m-0 text-center fs10">{{filterCount}}</p></div>
              </li>
            </ul>
           </div>
        </div>
      </div>
  </div>
  <div class="page-card bg-white rounded">
     <div class="table-responsive rounded tab-grid">
         <table class="table table-custom mb-0" aria-describedby="Memtable">
           <thead>
            <th scope="col">
              <div class="custom-checkbox form-check">
                <input class="form-check-input float-none ms-0" type="checkbox"
                (change)="selectAllEquipmentsData()"
                [checked]="selectAll"
                id="tblData"
                name="tblData">
                <label class="form-check-label c-pointer fs12" for="tblData">
                </label>
              </div>
                </th>
               <th scope="col" class="text-center" resizable>Equipment Name
                <span>
                  <img src="./assets/images/down-chevron.svg" alt="down-arrow" class="h-10px ms-2" (click)="sortByField('id','ASC')"  (keydown)="handleToggleKeydown($event, 'id', 'ASC')" *ngIf="sortColumn !== 'id'"/>
                  <img src="./assets/images/down-chevron.svg" alt="down-arrow" class="h-10px ms-2" (click)="sortByField('id','ASC')"  (keydown)="handleToggleKeydown($event, 'id', 'ASC')" *ngIf="sort === 'DESC' && sortColumn === 'id'"/>
                  <img src="./assets/images/up-chevron.svg" alt="up-arrow" class="h-10px ms-2" (click)="sortByField('id','DESC')"  (keydown)="handleToggleKeydown($event, 'id', 'DESC')" *ngIf="sort === 'ASC' && sortColumn === 'id'"/>
                </span>
               </th>
               <th scope="col" class="text-center" resizable>Manufacturer
                <span>
                  <img src="./assets/images/down-chevron.svg" alt="down-arrow" class="h-10px ms-2" (click)="sortByField('equipmentName','ASC')"  (keydown)="handleToggleKeydown($event, 'equipmentName', 'ASC')" *ngIf="sortColumn !== 'equipmentName'"/>
                  <img src="./assets/images/down-chevron.svg" alt="down-arrow" class="h-10px ms-2" (click)="sortByField('equipmentName','ASC')" (keydown)="handleToggleKeydown($event, 'equipmentName', 'ASC')" *ngIf="sort === 'DESC' && sortColumn === 'equipmentName'"/>
                  <img src="./assets/images/up-chevron.svg" alt="up-arrow" class="h-10px ms-2" (click)="sortByField('equipmentName','DESC')" (keydown)="handleToggleKeydown($event, 'equipmentName', 'DESC')" *ngIf="sort === 'ASC' && sortColumn === 'equipmentName'"/>
                </span>
               </th>
               <th scope="col" class="text-center" resizable>Model
                <span>
                  <img src="./assets/images/down-chevron.svg" alt="down-arrow" class="h-10px ms-2" (click)="sortByField('equipmentType','ASC')" (keydown)="handleToggleKeydown($event, 'equipmentType', 'ASC')"  *ngIf="sortColumn !== 'equipmentType'"/>
                  <img src="./assets/images/down-chevron.svg" alt="down-arrow" class="h-10px ms-2" (click)="sortByField('equipmentType','ASC')" (keydown)="handleToggleKeydown($event, 'equipmentType', 'ASC')"  *ngIf="sort === 'DESC' && sortColumn === 'equipmentType'"/>
                  <img src="./assets/images/up-chevron.svg" alt="up-arrow" class="h-10px ms-2" (click)="sortByField('equipmentType','DESC')" (keydown)="handleToggleKeydown($event, 'equipmentType', 'DESC')"  *ngIf="sort === 'ASC' && sortColumn === 'equipmentType'"/>
                </span>
               </th>
               <th scope="col" class="text-center" resizable>Fuel Type
                <span>
                  <img src="./assets/images/down-chevron.svg" alt="down-arrow" class="h-10px ms-2" (click)="sortByField('companyName','ASC')" (keydown)="handleToggleKeydown($event, 'companyName', 'ASC')"  *ngIf="sortColumn !== 'companyName'"/>
                  <img src="./assets/images/down-chevron.svg" alt="down-arrow" class="h-10px ms-2" (click)="sortByField('companyName','ASC')" (keydown)="handleToggleKeydown($event, 'companyName', 'ASC')"  *ngIf="sort === 'DESC' && sortColumn === 'companyName'"/>
                  <img src="./assets/images/up-chevron.svg" alt="up-arrow" class="h-10px ms-2" (click)="sortByField('companyName','DESC')" (keydown)="handleToggleKeydown($event, 'companyName', 'DESC')"  *ngIf="sort === 'ASC' && sortColumn === 'companyName'"/>
                </span>
               </th>
               <th scope="col" class="text-center" resizable>Mobilized Date
                <span>
                  <img src="./assets/images/down-chevron.svg" alt="down-arrow" class="h-10px ms-2" (click)="sortByField('contactPerson','ASC')" (keydown)="handleToggleKeydown($event, 'contactPerson', 'ASC')"  *ngIf="sortColumn !== 'contactPerson'"/>
                  <img src="./assets/images/down-chevron.svg" alt="down-arrow" class="h-10px ms-2" (click)="sortByField('contactPerson','ASC')" (keydown)="handleToggleKeydown($event, 'contactPerson', 'ASC')"*ngIf="sort === 'DESC' && sortColumn === 'contactPerson'"/>
                  <img src="./assets/images/up-chevron.svg" alt="up-arrow" class="h-10px ms-2" (click)="sortByField('contactPerson','DESC')" (keydown)="handleToggleKeydown($event, 'contactPerson', 'DESC')"*ngIf="sort === 'ASC' && sortColumn === 'contactPerson'"/>
                </span>
               </th>
               <th scope="col" class="text-center" resizable>Demobilized Date
                <span>
                  <img src="./assets/images/down-chevron.svg" alt="down-arrow" class="h-10px ms-2" (click)="sortByField('contactPerson','ASC')" (keydown)="handleToggleKeydown($event, 'contactPerson', 'ASC')"*ngIf="sortColumn !== 'contactPerson'"/>
                  <img src="./assets/images/down-chevron.svg" alt="down-arrow" class="h-10px ms-2" (click)="sortByField('contactPerson','ASC')" (keydown)="handleToggleKeydown($event, 'contactPerson', 'ASC')"*ngIf="sort === 'DESC' && sortColumn === 'contactPerson'"/>
                  <img src="./assets/images/up-chevron.svg" alt="up-arrow" class="h-10px ms-2" (click)="sortByField('contactPerson','DESC')" (keydown)="handleToggleKeydown($event, 'contactPerson', 'DESC')"*ngIf="sort === 'ASC' && sortColumn === 'contactPerson'"/>
                </span>
               </th>
             <th scope="col" class="text-center" resizable>Action</th>

           </thead>
           <tbody  *ngIf="loader==false && equipmentlogList.length>0">
             <tr *ngFor= "let item of equipmentlogList
             | paginate: { itemsPerPage: pageSize, currentPage: pageNo, totalItems: totalCount}; let i = index">
              <td class="custom-checkbox">
                <div class="form-check text-start ps-0">
                      <input class="form-check-input float-none ms-0" type="checkbox"
                      [checked]="item.isChecked"
                      (change)="setSelectedItem(i)"
                      id="tblData1_{{ i }}"
                      name="tblData1">
                      <label class="form-check-label c-pointer fs12" for="tblData1_{{ i }}">
                      </label>
                    </div>
              </td>
              <td class="text-center">{{item.EquipmentName}}</td>
              <td class="text-center">{{item.EquipmentManufacturer}}</td>
              <td class="text-center">{{item.EquipmentModel}}</td>
              <td class="text-center">{{item.fuelType}}</td>
              <td class="text-center">{{item.mobilizedDate  | date: 'dd/mm/yyyy'}}</td>
              <td class="text-center">{{item.deMobilizedDate  | date: 'dd/mm/yyyy'}}</td>

              <td class="text-center p-md-0">
                <ul class="list-inline mb-0">

                    <li class="list-inline-item mx-2" tooltip="Delete"
                    placement="top" (click)="openDeleteModal(i,deleteList)"  (keydown)="handleDownKeydown($event,i,deleteList,'delete')"><a href="javascript:void(0)" ><img src="./assets/images/delete.svg" alt="delete" class=" h-15px"  /></a></li>
                </ul>
                </td>
            </tr>
           </tbody>
           <tr *ngIf="loader==true">
            <td colspan=7 class="text-center">
              <div class="fs18 fw-bold cairo-regular my-5 text-black text-center">
                Loading...
              </div>
            </td>
          </tr>
          <tr *ngIf="loader==false && equipmentlogList.length==0">
            <td colspan=7 class="text-center">
              <div class="fs18 fw-bold cairo-regular my-5 text-black">
                No Records Found
              </div>
            </td>
          </tr>
         </table>
       </div>
       <div class="tab-pagination px-2" id="tab-pagination5" *ngIf="loader==false && totalCount > 25">
        <div class="row">
          <div class="col-md-2 align-items-center">
           <ul class="list-inline my-3">
             <li class="list-inline-item notify-pagination">
               <label class="fs12 color-grey4" for="shwEnt">Show entries</label>
             </li>
               <li class="list-inline-item">
                  <select  id="shwEnt" class="w-auto form-select fs12 color-grey4" (change)="changePageSize($event.target.value)" [ngModel]="pageSize">
                    <option value="25">25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                    <option value="150">150</option>
                 </select>
             </li>
           </ul>
          </div>
          <div class="col-md-8 text-center">
             <div class="my-3 position-relative d-inline-block">
               <pagination-controls (pageChange)="changePageNo($event)"  previousLabel=""
               nextLabel="">
             </pagination-controls>
           </div>
          </div>
         </div>
        </div>
     </div>
    </div>
</section>
<!-- modal -->
<ng-template #newequipment>
  <div class="modal-header">
    <h4 class="fs14 fw-bold cairo-regular color-text7 my-1">New Equipment Log</h4>
    <button type="button" class="close ms-auto" aria-label="Close" (click)="close(cancelConfirmation,'newequipment')">
      <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close"></span>
    </button>
  </div>
  <div class="modal-body" *ngIf="!modalLoader">
    <form name="form" class="custom-material-form" [formGroup]="equipmentDetailsForm" (ngSubmit)="onSubmit()" novalidate>
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label class=" fs12 fw600 " for="equipmentType1">Equipment Name<span class="color-red">*</span></label>
            <select class="form-control fs12  material-input" id="equipmentType1" formControlName="EquipmentName" placeholder="Equipment Name" placeholder="Equipment Name">
              <option value="" disabled selected hidden>Equipment Name</option>
              <option  *ngFor="let item of equipmentList" value={{item.equipmentName}}>{{item.equipmentName}}</option>
            </select>
            <div class="color-red" *ngIf="submitted && equipmentDetailsForm.get('EquipmentName').errors">
              <small *ngIf="equipmentDetailsForm.get('EquipmentName').errors.required">*Equipment Name Required.</small>
            </div>
          </div>
          <div class="form-group">
            <label class=" fs12 fw600 " for="manufacturer">Equipment Manufacturer<span class="color-red">*</span></label>
            <input type="text" class="form-control fs12  material-input" placeholder="Equipment Manufacturer*"  id="manufacturer"
            formControlName="EquipmentManufacturer" (keypress)="alphaNumericForEquipments($event)">
            <div class="color-red" *ngIf="submitted && equipmentDetailsForm.get('EquipmentManufacturer').errors">
              <small *ngIf="equipmentDetailsForm.get('EquipmentManufacturer').errors.required">*Equipment Type
                Required.</small>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label class=" fs12 fw600 "  for="model">Equipment Model<span class="color-red">*</span></label>
            <input type="text" class="form-control fs12  material-input" placeholder="Equipment Model*"  id="model"
              formControlName="EquipmentModel" (keypress)="alphaNumericForEquipments($event)">
            <div class="color-red" *ngIf="submitted && equipmentDetailsForm.get('EquipmentModel').errors">
              <small *ngIf="equipmentDetailsForm.get('EquipmentModel').errors.required">*Equipment Model is Required.</small>
            </div>
          </div>
          <div class="form-group">
            <label class=" fs12 fw600 "  for="equipmentType1">Fuel Type<span class="color-red">*</span></label>
            <select class="form-control fs12  material-input" id="equipmentType1" formControlName="fuelType" placeholder="Fuel Type*" placeholder="Fuel Type*">
              <option value="" disabled selected hidden>Fuel Type*</option>
              <option  *ngFor="let item of fuelTypeList" value={{item}}>{{item}}</option>
            </select>
            <div class="color-red" *ngIf="submitted && equipmentDetailsForm.get('fuelType').errors">
              <small *ngIf="equipmentDetailsForm.get('fuelType').errors.required">*Fuel Type is
                Required.</small>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <label class=" fs12 fw600 " for="mobilizeddate">Mobilized Date<span class="color-red">*</span></label>
          <div class="form-group input-group new-delivery-form-date carbon-calendar" style="margin-bottom: 3px;">
            <input class="form-control fs12 ps-0 fw500 material-input" #dp="bsDatepicker" bsDatepicker  id="mobilizeddate"
              formControlName="mobilizedDate" placeholder="Mobilized Date *" [bsConfig]="{
                              isAnimated: true,
                              showWeekNumbers: false,
                              customTodayClass: 'today'
                            }" />
            <span class="input-group-text">
              <img src="./assets/images/date.svg" class="h-12px" alt="Date" (click)="dp.toggle()" (keydown)="dp.toggle()"
                [attr.aria-expanded]="dp.isOpen" />
            </span>
          </div>
          <div class="color-red" *ngIf="submitted && equipmentDetailsForm.get('mobilizedDate').errors">
            <small *ngIf="equipmentDetailsForm.get('mobilizedDate').errors.required">*Mobilized Date is Required.</small>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label class=" fs12 fw600 " for="equipmentType1">Responsible Company<span class="color-red">*</span></label>
            <select class="form-control fs12  material-input" id="equipmentType1" formControlName="responsibleCompany" placeholder="Responsible Company*" placeholder="Responsible Company*" >
              <option value="" disabled selected hidden>Responsible Company*</option>
              <option  *ngFor="let item of companiesList" value={{item.id}}>{{item.companyName}}</option>
            </select>
            <div class="color-red" *ngIf="submitted && equipmentDetailsForm.get('responsibleCompany').errors">
              <small *ngIf="equipmentDetailsForm.get('responsibleCompany').errors.required">*Responsible Company is Required.</small>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <label class=" fs12 fw600 " for="demdate">Demobilized Date</label>
          <div class="form-group input-group new-delivery-form-date carbon-calendar" style="margin-bottom: 3px;">
            <input class="form-control fs12 ps-0 fw500 material-input" #dp="bsDatepicker" bsDatepicker  id="demdate"
            formControlName="deMobilizedDate" placeholder="Demobilized Date" [bsConfig]="{
                            isAnimated: true,
                            showWeekNumbers: false,
                            customTodayClass: 'today'
                          }" />
            <span class="input-group-text">
              <img src="./assets/images/date.svg" class="h-12px" alt="Date" (click)="dp.toggle()" (keydown)="dp.toggle()"
                [attr.aria-expanded]="dp.isOpen" />
            </span>
          </div>
        </div>
        <div class="col-md-6">
          <label class=" fs12 fw600 " for="status">Status</label>
          <div class="form-group">
            <label class="status_slider" for="status1"> &nbsp;
              <input type="checkbox" id="status1" formControlName="status" (change)="onStatusChange($event)" />
              <span class="slider round"></span>
            </label>
            <span class="carbon-status-text">{{ isActive ? 'Active' : 'Inactive' }}</span>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <label class=" fs12 fw600 " for="date1">Select Date<span class="color-red">*</span></label>
          <div class="form-group input-group new-delivery-form-date carbon-calendar" style="margin-bottom: 3px;">
            <input class="form-control fs12 ps-0 fw500 material-input" #dp="bsDatepicker" bsDatepicker  id="date1"
              formControlName="date" placeholder="Select Date *" [bsConfig]="{
                              isAnimated: true,
                              showWeekNumbers: false,
                              customTodayClass: 'today'
                            }" />
            <span class="input-group-text">
              <img src="./assets/images/date.svg" class="h-12px" alt="Date" (click)="dp.toggle()" (keydown)="dp.toggle()"
                [attr.aria-expanded]="dp.isOpen" />
            </span>
          </div>
          <div class="color-red" *ngIf="submitted && equipmentDetailsForm.get('date').errors">
            <small *ngIf="equipmentDetailsForm.get('date').errors.required">*Date is Required.</small>
          </div>
        </div>
        <div class="col-md-3">
          <label class=" fs12 fw600 " for="equipmentType1">Select Unit<span class="color-red">*</span></label>
            <select class="form-control fs12  material-input" id="equipmentType1" formControlName="unit" placeholder="Select Unit*" placeholder="Select Unit*">
              <option value="" disabled selected hidden>Select Unit*</option>
              <option  *ngFor="let item of unitTypeList" value={{item}}>{{item}}</option>
            </select>

            <div class="color-red" *ngIf="submitted && equipmentDetailsForm.get('unit').errors">
              <small *ngIf="equipmentDetailsForm.get('unit').errors.required">*Unit Type is
                Required.</small>
            </div>
        </div>
        <div class="col-3">
          <div class="form-group">
            <label class=" fs12 fw600 " for="value">Enter Value<span class="color-red">*</span></label>
            <input type="text" class="form-control fs12  material-input" placeholder="Enter Value*" id="value"
              formControlName="value" (keypress)="alphaNumericForEquipments($event)">
            <div class="color-red" *ngIf="submitted && equipmentDetailsForm.get('value').errors">
              <small *ngIf="equipmentDetailsForm.get('value').errors.required">*Value is Required.</small>
            </div>
          </div>
        </div>
      </div>
       <ngx-file-drop
       dropZoneLabel="Drop files here"
       multiple="true"
     >
       <ng-template ngx-file-drop-content-tmp let-openFileSelector="openFileSelector">
         <div class="bulkupload-content text-center" (click)="openFileSelector()" (keydown)="openFileSelector()">
           <img src="./assets/images/file.svg" alt="Excel" />
           <p class="fs14 fw600 mb3 pt10 color-grey7">Drag & Drop your file here</p>
           <p class="fs12 color-grey8 fw500 mb3">Or</p>
           <label class="color-blue4 fs14 mb10 fw500 text-underline" for="browse">Click here to browse</label>
         </div>
       </ng-template>
     </ngx-file-drop>
     <p class="fs10 color-grey8 fw500 my-2 text-end">
       *Supported formats are .jpg, .png, .doc and .pdf
     </p>
     <div class="text-center mt30 mb-4">
      <button class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3  px-2rem" (click)="close(cancelConfirmation,'newequipment')" type="button">Cancel</button>
      <button class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular  px-2rem" type="submit"[disabled]="formSubmitted && equipmentDetailsForm.valid"><em class="fa fa-spinner" aria-hidden="true" *ngIf="formSubmitted && equipmentDetailsForm.valid"></em>Submit</button>
     </div>
    </form>
  </div>
  <div class="modal-body text-center" *ngIf="modalLoader">
    Loading...
  </div>
</ng-template>
<!-- modal -->

  <ng-template #editEquipment>
    <div class="modal-header border-0 pb-0">
      <h4 class="fs14 fw-bold cairo-regular color-text7 my-0">Edit Equipment</h4>
      <button type="button" class="close ms-auto" aria-label="Close" (click)="close(cancelConfirmation,'editEquipment')">
        <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close"></span>
      </button>
    </div>
    <div class="modal-body" *ngIf="!modalLoader">
      <div class="filter-content">
        <form name="form" class="custom-material-form" [formGroup]="equipmentEditForm" (ngSubmit)="onEditSubmit()" novalidate>
          <div class="row">
            <div class="col-md-12">
              <div class="form-group mb-4">
                <label class=" fs12 fw600 " for="equipment">Equipment ID</label>
                <input type="text" class="form-control fs12 color-orange  material-input px-2" placeholder="" id="equipment"
                  formControlName="equipmentAutoId" disabled="disabled">
              </div>
              <div class="form-group">
                <input type="text" class="form-control fs12  material-input" placeholder="Equipment Name*"
                  formControlName="equipmentName" (keypress)="alphaNumericForEquipments($event)">
                <div class="color-red" *ngIf="editSubmitted && equipmentEditForm.get('equipmentName').errors">
                  <small *ngIf="equipmentEditForm.get('equipmentName').errors.required">*Equipment Name Required.</small>
                </div>
              </div>
              <div class="form-group">
                <select class="form-control fs12  material-input" id="equipmentType1" formControlName="equipmentType"
                  placeholder="Equipment Type">
                  <option value="" disabled selected hidden>Equipment Type</option>
                  <option *ngFor="let item of presetEquipmentTypeList" value={{item.id}}>{{item.equipmentType}}</option>
                </select>
                <div class="color-red" *ngIf="editSubmitted && equipmentEditForm.get('equipmentType').errors">
                  <small *ngIf="equipmentEditForm.get('equipmentType').errors.required">*Equipment Type Required.</small>
                </div>
              </div>
              <div class="form-group">
                <select formControlName="controlledBy" class="form-control fs12  material-input">
                  <option value="" disabled selected hidden>*Contact Person</option>
                  <option *ngFor="let item of memberList" [value]="item.id">
                    {{item.User?.firstName}} {{item.User?.lastName}} ({{item.User?.email}})</option>
                </select>
              </div>
              <div class="text-center mt30 mb-4">
                <button type="button" class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3  px-2rem"
                  (click)="close(cancelConfirmation,'editEquipment')">Cancel</button>
                <button class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular  px-2rem" type="submit"
                  [disabled]="formEditSubmitted && equipmentEditForm.valid"><em class="fa fa-spinner" aria-hidden="true"
                    *ngIf="formEditSubmitted && equipmentEditForm.valid"></em>Submit</button>
              </div>
            </div>
          </div>

        </form>
      </div>
    </div>
    <div class="modal-body text-center" *ngIf="modalLoader">
      Loading...
    </div>
  </ng-template>
  <div id="fiter-temp4">
  <!--Filter Modal-->
  <ng-template #filter>
    <div class="modal-header border-0 pb-0">
      <h4 class="fs14 fw-bold cairo-regular color-text7 my-0">Filter</h4>
      <button type="button" class="close ms-auto" aria-label="Close" (click)="resetAndClose()">
        <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close"></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="filter-content">
        <form class="custom-material-form" [formGroup]="filterForm" (ngSubmit)="filterSubmit()">
          <div class="row">
            <div class="col-md-12">
              <div class="input-group mb-3">
                <input type="text" class="form-control fs12   material-input" placeholder="Equipment Name" formControlName="nameFilter">
                 <span class="input-group-text">
                    <img src="./assets/images/search-icon.svg" alt="Search">
                  </span>
              </div>
              <div class="input-group mb-3">
                <input type="text" class="form-control fs12   material-input" placeholder="Company Name" formControlName="companyNameFilter">
                 <span class="input-group-text">
                    <img src="./assets/images/search-icon.svg" alt="Search">
                  </span>
              </div>
              <div class="input-group mb-3">
                <input type="text" class="form-control fs12   material-input" placeholder="ID" formControlName="idFilter">
                  <span class="input-group-text">
                    <img src="./assets/images/search-icon.svg" alt="Search">
                  </span>
              </div>
              <div class="form-group">
                <select formControlName="typeFilter" class="form-control fs12 material-input px-2">
                  <option value="" disabled selected hidden>Type</option>
                  <option *ngFor="let item of equipmentTypeList" [value]="item.equipmentType">
                    {{item.equipmentType}}</option>
                </select>
              </div>
              <div class="form-group">
                <select formControlName="memberFilter" class="form-control fs12 material-input">
                  <option value="" disabled selected hidden>Contact Person</option>
                  <option *ngFor="let item of memberList" [value]="item.id">
                    {{item.User?.email}}</option>
                </select>
              </div>
              <div class="row justify-content-end">
                <button class="btn btn-orange radius20 col-4 mt-2 fs12 fw-bold cairo-regular mx-1"
                type="submit">Apply</button>
                <button class="btn btn-orange radius20 fs12 col-4 mt-2 fw-bold cairo-regular mx-1"
                type="button" (click)="resetFilter()">Reset</button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
<!--Filter Modal-->
</div>
<ng-template #deleteList>
<div class="modal-body">
  <div class="text-center my-4" *ngIf="!remove">
    <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">Are you sure you want to delete '{{equipmentlogList[currentDeleteId]?.equipmentName | titlecase}}'?</p>
    <button class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular   px-5"
    (click)="resetAndClose()">No</button>
    <button class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular  px-5" type="submit" (click)="deleteEquipment()" [disabled]="deleteSubmitted"><em class="fa fa-spinner" aria-hidden="true" *ngIf="deleteSubmitted"></em>Yes</button>

  </div>
  <div class="text-center my-4" id="remove-popup3" *ngIf="remove">
    <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">Are you sure you want to delete?</p>
    <button class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular   px-5"
    (click)="resetAndClose()">No</button>
    <button class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular  px-5" type="submit" (click)="removeItem()" [disabled]="deleteSubmitted"><em class="fa fa-spinner" aria-hidden="true" *ngIf="deleteSubmitted"></em>Yes</button>

  </div>
</div>
</ng-template>
<!--Confirmation Popup-->
<div id="confirm-popup9">
<ng-template #cancelConfirmation>
  <div class="modal-body">
    <div class="text-center my-4">
      <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">Are you sure you want to cancel?</p>
      <button class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular   px-5"
      (click)="resetForm('no')">No</button>
      <button class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular  px-5" (click)="resetForm('yes')">Yes</button>
    </div>
  </div>
  </ng-template>
</div>

<ng-template #deactivateEquipment>
  <div class="modal-header">
     <h4 class="fs14 fw-bold cairo-regular color-text7 my-1"><img src="./assets/images/equipments.svg" alt="Gate" class="me-2">
        Deactivate Equipment
     </h4>
     <button type="button" class="close ms-auto" aria-label="Close" (click)="close(cancelConfirmation,'deactivateEquipment')">
     <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close"></span>
     </button>
  </div>
  <div class="modal-body" *ngIf="!getMappedRequestLoader">
        <div class="row">
           <div class="col-md-12">
              <div class="form-group">
                 <p class="text-center fw-bold fs14">Are you sure you want to deactivate this Equipment?</p>
                 <div *ngIf="mappedRequestList.length > 0">
                 <p class="text-center fs13 content-deactivate">
                 This Equipment is associated with the following items. If applicable, please switch the Equipment assignment for the respective items.
                 </p>
                 <div class="table-responsive rounded tab-grid fixTableHead equipmentTableHead">
                    <table class="table table-custom mb-0 members-table" aria-describedby="Memtable">
                    <thead class="deactivate-table-head">
                      <tr>
                       <th scope="col"></th>
                       <th scope="col" resizable>ID and Description </th>
                       <th scope="col" resizable>Date and Time</th>
                       <th scope="col" resizable>Equipment</th>
                      </tr>
                       </thead>
                    <tbody>
                       <tr *ngFor="let item of mappedRequestList" class="member-table-row">
                        <td>
                          <img src="./assets/images/cranelift.png" alt="cranelift" *ngIf="(item?.requestType === 'deliveryRequestWithCrane') || (item?.requestType === 'craneRequest')">
                          <img src="./assets/images/bluetruck.svg" alt="bluetruck" *ngIf="item?.requestType === 'deliveryRequest'">
                       </td>
                        <td *ngIf="item?.requestType === 'deliveryRequest'  || item?.requestType === 'deliveryRequestWithCrane'">
                          <p class="mb-0">{{item?.DeliveryId}}-{{item?.description}} </p>
                    </td>
                       <td *ngIf="item?.requestType === 'craneRequest'">
                             <p class="mb-0">{{item?.CraneRequestId}}-{{item?.description}} </p>
                       </td>
                       <td *ngIf="item?.requestType === 'deliveryRequest'  || item?.requestType === 'deliveryRequestWithCrane'">{{item?.deliveryStart | date: 'medium'}}</td>
                       <td  *ngIf="item?.requestType === 'craneRequest'">{{item?.craneDeliveryStart | date: 'medium'}}</td>
                       <td class="deactivate-select-control" *ngIf="item?.requestType === 'deliveryRequest'">
                        <select class="form-control fs12 material-input ps-2" (change)=" switchEquipment(item,$event.target.value)" >
                              <option *ngFor="let itemvalue of nonCraneEquipmentDropdownList" value="{{itemvalue.id}}" [ngValue]="itemvalue?.id" [selected]="item?.equipmentDetails[0]?.Equipment?.id == itemvalue?.id">{{itemvalue.equipmentName}}</option>
                     </select>
                  </td>
                       <td class="deactivate-select-control" *ngIf="item?.requestType === 'craneRequest' || item?.requestType === 'deliveryRequestWithCrane'">
                             <select class="form-control fs12 material-input ps-2" (change)="switchEquipment(item,$event.target.value)" >
                                   <option *ngFor="let itemvalue of craneEquipmentDropdownList" value="{{itemvalue.id}}" [ngValue]="itemvalue?.id" [selected]="item?.equipmentDetails[0]?.Equipment?.id == itemvalue?.id">{{itemvalue.equipmentName}}</option>
                          </select>
                       </td>
                       </tr>
                    </tbody>
                    </table>
                 </div>
                 <p class="text-center fw-bold fs12">*Note: If you need to deactivate this equipment, you will have to switch the equipment for all the future bookings listed above</p>
                 </div>
              </div>
          </div>
        </div>
        <div class="text-center pb-4">
          <button class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular mb-3 px-2rem me-2" (click)="close(cancelConfirmation,'newequipment')" type="button">Cancel</button>
          <button class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular mb-3 px-2rem" [disabled]="deactivateEquipmentLoader" [disabled]="deactivateEquipmentLoader" (click)="deactiveEquipment()">
            <em class="fa fa-spinner" aria-hidden="true" *ngIf="deactivateEquipmentLoader"></em>Confirm</button>
          </div>
  </div>
  <div class="modal-body fw-bold text-center text-black" *ngIf="getMappedRequestLoader">
     Loading...
  </div>
</ng-template>
