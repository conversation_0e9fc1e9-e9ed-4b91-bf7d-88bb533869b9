import { FormGroup, FormControl, AbstractControl } from '@angular/forms';
import { ConfirmPasswordValidator } from './confirm-password.validator';

describe('ConfirmPasswordValidator', () => {
  let formGroup: FormGroup;

  beforeEach(() => {
    formGroup = new FormGroup({
      newPassword: new FormControl(''),
      confirmPassword: new FormControl('')
    });
  });

  it('should not set errors when passwords match', () => {
    // Arrange
    formGroup.get('newPassword').setValue('Password123');
    formGroup.get('confirmPassword').setValue('Password123');

    // Act
    ConfirmPasswordValidator.MatchPassword(formGroup);

    // Assert
    expect(formGroup.get('confirmPassword').errors).toBeNull();
  });

  it('should set ConfirmPassword error when passwords do not match', () => {
    // Arrange
    formGroup.get('newPassword').setValue('Password123');
    formGroup.get('confirmPassword').setValue('DifferentPassword');

    // Act
    ConfirmPasswordValidator.MatchPassword(formGroup);

    // Assert
    expect(formGroup.get('confirmPassword').errors).toEqual({ ConfirmPassword: true });
  });

  it('should handle empty password fields', () => {
    // Arrange - both empty (matching)
    formGroup.get('newPassword').setValue('');
    formGroup.get('confirmPassword').setValue('');

    // Act
    ConfirmPasswordValidator.MatchPassword(formGroup);

    // Assert
    expect(formGroup.get('confirmPassword').errors).toBeNull();
  });

  it('should handle null password values', () => {
    // Arrange
    formGroup.get('newPassword').setValue(null);
    formGroup.get('confirmPassword').setValue(null);

    // Act
    ConfirmPasswordValidator.MatchPassword(formGroup);

    // Assert
    expect(formGroup.get('confirmPassword').errors).toBeNull();
  });

  it('should handle case-sensitive password comparison', () => {
    // Arrange
    formGroup.get('newPassword').setValue('Password123');
    formGroup.get('confirmPassword').setValue('password123');

    // Act
    ConfirmPasswordValidator.MatchPassword(formGroup);

    // Assert
    expect(formGroup.get('confirmPassword').errors).toEqual({ ConfirmPassword: true });
  });
});