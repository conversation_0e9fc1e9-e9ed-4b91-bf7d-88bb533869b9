import { ComponentFixture, TestBed } from '@angular/core/testing';
import { InspectionCommentsComponent } from './comments.component';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';
import { MixpanelService } from '../../../services/mixpanel.service';
import { of, throwError } from 'rxjs';
import { BehaviorSubject } from 'rxjs';

describe('InspectionCommentsComponent', () => {
  let component: InspectionCommentsComponent;
  let fixture: ComponentFixture<InspectionCommentsComponent>;
  let deliveryServiceMock: jest.Mocked<DeliveryService>;
  let projectServiceMock: jest.Mocked<ProjectService>;
  let toastrMock: jest.Mocked<ToastrService>;
  let socketMock: jest.Mocked<Socket>;
  let mixpanelServiceMock: jest.Mocked<MixpanelService>;

  const mockParentCompanyId = new BehaviorSubject<string>('123');
  const mockProjectParent = new BehaviorSubject<any>({
    ProjectId: '456',
    ParentCompanyId: '123'
  });
  const mockInspectionRequestId = new BehaviorSubject<string>('789');

  beforeEach(async () => {
    jest.useFakeTimers();

    deliveryServiceMock = {
      InspectionRequestId: mockInspectionRequestId,
      inspectionUpdated1: new BehaviorSubject<any>({}),
      getInspectionRequestComment: jest.fn(),
      createInspectionComment: jest.fn(),
      updatedInspectionHistory1: jest.fn(),
    } as any;

    projectServiceMock = {
      ParentCompanyId: mockParentCompanyId,
      projectParent: mockProjectParent,
    } as any;

    toastrMock = {
      success: jest.fn(),
      error: jest.fn(),
    } as any;

    socketMock = {
      emit: jest.fn(),
    } as any;

    mixpanelServiceMock = {
      addMixpanelEvents: jest.fn(),
    } as any;

    await TestBed.configureTestingModule({
      declarations: [InspectionCommentsComponent],
      imports: [ReactiveFormsModule],
      providers: [
        FormBuilder,
        { provide: DeliveryService, useValue: deliveryServiceMock },
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: ToastrService, useValue: toastrMock },
        { provide: Socket, useValue: socketMock },
        { provide: MixpanelService, useValue: mixpanelServiceMock },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(InspectionCommentsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with empty comment form', () => {
    expect(component.commentDetailsForm.get('comment')).toBeTruthy();
    expect(component.commentDetailsForm.get('comment')?.valid).toBeFalsy();
  });

  it('should get history when inspection request ID changes', () => {
    const mockComments = {
      data: {
        rows: [{ id: 1, comment: 'Test comment' }],
      },
    };
    deliveryServiceMock.getInspectionRequestComment.mockReturnValue(of(mockComments));

    mockProjectParent.next({
      ProjectId: '456',
      ParentCompanyId: '123',
    });

    deliveryServiceMock.inspectionUpdated1.next({});
    mockInspectionRequestId.next('789');

    jest.advanceTimersByTime(100);
    fixture.detectChanges();

    expect(deliveryServiceMock.getInspectionRequestComment).toHaveBeenCalledWith({
      InspectionRequestId: '789',
      ParentCompanyId: '123',
      ProjectId: '456',
    });
    expect(component.commentList).toEqual(mockComments.data.rows);
    expect(component.loader).toBeFalsy();
  });

  it('should handle empty string validation', () => {
    const formValue = { comment: '   ' };
    expect(component.checkStringEmptyValues(formValue)).toBeTruthy();

    const formValue2 = { comment: 'Valid comment' };
    expect(component.checkStringEmptyValues(formValue2)).toBeFalsy();
  });

  it('should submit comment successfully', () => {
    const mockResponse = { message: 'Comment added successfully' };
    deliveryServiceMock.createInspectionComment.mockReturnValue(of(mockResponse));

    component.commentDetailsForm.patchValue({ comment: 'Test comment' });
    component.onSubmit();

    expect(deliveryServiceMock.createInspectionComment).toHaveBeenCalledWith({
      comment: 'Test comment',
      InspectionRequestId: '789',
      ParentCompanyId: '123'
    });
    expect(toastrMock.success).toHaveBeenCalledWith('Comment added successfully', 'Success');
    expect(mixpanelServiceMock.addMixpanelEvents).toHaveBeenCalledWith('Comment added against a inspection Booking');
    expect(socketMock.emit).toHaveBeenCalledWith('InspectionCommentHistory', mockResponse);
    expect(component.submitted).toBeFalsy();
    expect(component.formSubmitted).toBeFalsy();
  });

  it('should handle comment submission error', () => {
    const error = {
      message: {
        statusCode: 400,
        details: [{ message: 'Invalid comment' }],
      },
    };
    deliveryServiceMock.createInspectionComment.mockReturnValue(throwError(() => error));

    component.commentDetailsForm.patchValue({ comment: 'Test comment' });
    component.onSubmit();

    expect(toastrMock.error).toHaveBeenCalledWith(['Invalid comment']);
    expect(component.submitted).toBeFalsy();
    expect(component.formSubmitted).toBeFalsy();
  });

  it('should show error for empty comment submission', () => {
    component.commentDetailsForm.patchValue({ comment: '   ' });
    component.onSubmit();

    expect(toastrMock.error).toHaveBeenCalledWith('Please enter a valid comment.', 'OOPS!');
    expect(component.submitted).toBeFalsy();
    expect(component.formSubmitted).toBeFalsy();
  });

  it('should unsubscribe on destroy', () => {
    const unsubscribeSpy = jest.spyOn(component['subscription'], 'unsubscribe');
    component.ngOnDestroy();
    expect(unsubscribeSpy).toHaveBeenCalled();
  });
});
