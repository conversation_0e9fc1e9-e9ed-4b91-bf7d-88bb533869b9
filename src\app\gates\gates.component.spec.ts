import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { GatesComponent } from './gates.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { ProjectService } from '../services/profile/project.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { MixpanelService } from '../services/mixpanel.service';
import { of, throwError } from 'rxjs';
import { HttpClientTestingModule } from '@angular/common/http/testing';

describe('GatesComponent', () => {
  let component: GatesComponent;
  let fixture: ComponentFixture<GatesComponent>;
  let projectServiceMock: jest.Mocked<ProjectService>;
  let deliveryServiceMock: jest.Mocked<DeliveryService>;
  let toastrServiceMock: jest.Mocked<ToastrService>;
  let modalServiceMock: jest.Mocked<BsModalService>;

  beforeEach(async () => {
    const mockProjectService = {
      gateList: jest.fn().mockReturnValue(of({ data: { rows: [], count: 0 }, lastId: null })),
      updateGate: jest.fn(),
      getMappedRequests: jest.fn(),
      addGate: jest.fn(),
      deactivateGate: jest.fn(),
      projectId: of('123'),
      ParentCompanyId: of('456')
    };

    const mockDeliveryService = {
      loginUser: of({ id: 1, name: 'Test User' })
    };

    const mockToastrService = {
      success: jest.fn(),
      error: jest.fn()
    };

    const mockModalService = {
      show: jest.fn(),
      hide: jest.fn()
    };

    const mockMixpanelService = {
      addMixpanelEvents: jest.fn(),
      track: jest.fn()
    };

    await TestBed.configureTestingModule({
      declarations: [GatesComponent],
      imports: [
        ReactiveFormsModule,
        FormsModule,
        HttpClientTestingModule
      ],
      providers: [
        { provide: ProjectService, useValue: mockProjectService },
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: BsModalService, useValue: mockModalService },
        { provide: Title, useValue: { setTitle: jest.fn() } },
        { provide: MixpanelService, useValue: mockMixpanelService },
        UntypedFormBuilder
      ]
    }).compileComponents();

    projectServiceMock = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    deliveryServiceMock = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    toastrServiceMock = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    modalServiceMock = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(GatesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', fakeAsync(() => {
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.getGateList();
    tick();
    fixture.detectChanges();

    expect(component.currentPageNo).toBe(1);
    expect(component.pageSize).toBe(25);
    expect(component.userData).toEqual([]);
    expect(component.gateList).toEqual([]);
    expect(component.loader).toBe(false);
  }));

  it('should handle alpha numeric validation for gate name', () => {
    const validEvent = { which: 65, keyCode: 65 }; // 'A'
    const invalidEvent = { which: 33, keyCode: 33 }; // '!'
    const backspaceEvent = { which: 8, keyCode: 8 }; // Backspace

    expect(component.allowAlphaNumericForGateName(validEvent)).toBe(true);
    expect(component.allowAlphaNumericForGateName(invalidEvent)).toBe(false);
    expect(component.allowAlphaNumericForGateName(backspaceEvent)).toBe(true);
  });

  it('should open modal with correct configuration', () => {
    const template = {} as any;
    component.openModal(template);

    expect(modalServiceMock.show).toHaveBeenCalledWith(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-md new-gate-popup custom-modal'
    });
  });

  it('should handle gate activation successfully', () => {
    const mockGateData = {
      id: 1,
      gateName: 'Test Gate',
      gateAutoId: 123
    };
    const mockResponse = { message: 'Gate activated successfully' };

    projectServiceMock.updateGate.mockReturnValue(of(mockResponse));
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    component.activateGate(mockGateData);

    expect(projectServiceMock.updateGate).toHaveBeenCalledWith({
      id: mockGateData.id,
      gateName: mockGateData.gateName.trim(),
      ProjectId: '123',
      ParentCompanyId: '456',
      isActive: true
    });
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Gate activated successfully', 'Success');
  });

  it('should handle gate activation error', fakeAsync(() => {
    const mockGateData = {
      id: 1,
      gateName: 'Test Gate',
      gateAutoId: 123
    };
    const mockError = {
      message: {
        statusCode: 400,
        details: [{ message: 'Error message' }]
      }
    };

    projectServiceMock.updateGate.mockReturnValue(throwError(() => mockError));
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    component.activateGate(mockGateData);
    tick();

    expect(toastrServiceMock.error).toHaveBeenCalled();
  }));

  it('should handle search functionality', () => {
    const searchTerm = 'test';
    component.searchGate(searchTerm);
    expect(component.search).toBe(searchTerm);
    expect(component.currentPageNo).toBe(1);
  });

  it('should handle sorting', () => {
    const fieldName = 'gateName';
    const sortType = 'ASC';

    component.sortByField(fieldName, sortType);

    expect(component.sortColumn).toBe(fieldName);
    expect(component.sort).toBe(sortType);
    expect(component.currentPageNo).toBe(1);
  });

  it('should handle form submission', fakeAsync(() => {
    const mockResponse = { message: 'Gate created successfully' };
    projectServiceMock.addGate.mockReturnValue(of(mockResponse));

    // Make sure the form is initialized
    component.gateNewForm();

    // Set valid form values
    component.gateForm.patchValue({
      gateName: 'Test Gate'
    });

    // Set up required properties
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.modalRef = { hide: jest.fn() } as unknown as BsModalRef;

    // Call the method
    component.onSubmit();
    tick(); // Process the async operations

    // Check that submitted is true
    expect(component.submitted).toBe(false);
    expect(projectServiceMock.addGate).toHaveBeenCalledWith({
      gateName: 'Test Gate',
      ProjectId: '123',
      ParentCompanyId: '456'
    });
  }));

  it('should handle form reset', fakeAsync(() => {
    component.gateForm.patchValue({
      gateName: 'Test Gate'
    });

    component.resetForm('yes');
    tick();

    expect(component.gateForm.get('gateName')?.value).toBe(null);
    expect(component.submitted).toBe(false);
  }));

  it('should handle page number change', () => {
    const spy = jest.spyOn(component, 'getGateList');
    component.changePageNo(2);

    expect(component.currentPageNo).toBe(2);
    expect(spy).toHaveBeenCalled();
  });

  it('should handle page size change', () => {
    const spy = jest.spyOn(component, 'getGateList');
    component.changePageSize(50);

    expect(component.pageSize).toBe(50);
    expect(component.currentPageNo).toBe(1);
    expect(spy).toHaveBeenCalled();
  });

  it('should handle gate deactivation successfully', () => {
    const mockResponse = { message: 'Gate deactivated successfully' };
    projectServiceMock.deactivateGate.mockReturnValue(of(mockResponse));

    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.gateData = {
      id: 1,
      gateName: 'Test Gate',
      gateAutoId: 123
    };

    // Mock the deliveryGateRequestList to be empty to take the simpler path
    component.deliveryGateRequestList = [];
    component.deactivateGate();

    expect(projectServiceMock.deactivateGate).toHaveBeenCalledWith(expect.objectContaining({
      id: component.gateData.id,
      ProjectId: '123',
      ParentCompanyId: '456'
    }));
    expect(toastrServiceMock.success).toHaveBeenCalled();
  });

  it('should handle form validation errors', () => {
    component.gateForm.patchValue({
      gateName: ''  // Empty name should be invalid
    });

    component.onSubmit();

    expect(component.submitted).toBe(true);
    expect(component.gateForm.valid).toBe(false);
    expect(projectServiceMock.addGate).not.toHaveBeenCalled();
  });

  it('should handle error when adding a gate', fakeAsync(() => {
    const mockError = {
      message: {
        statusCode: 400,
        details: [{ message: 'Gate name already exists' }]
      }
    };

    projectServiceMock.addGate.mockReturnValue(throwError(() => mockError));

    component.gateForm.patchValue({
      gateName: 'Test Gate'
    });

    component.onSubmit();
    tick();

    expect(toastrServiceMock.error).toHaveBeenCalled();
  }));

  it('should close modal on successful gate creation', fakeAsync(() => {
    const mockResponse = { message: 'Gate created successfully' };
    projectServiceMock.addGate.mockReturnValue(of(mockResponse));

    // Set up the component with the necessary properties
    component.gateForm.patchValue({
      gateName: 'Test Gate'
    });

    component.modalRef = { hide: jest.fn() } as unknown as BsModalRef;
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    // Mock the mixpanelService
    const mixpanelService = TestBed.inject(MixpanelService);

    component.onSubmit();
    tick();

    expect(component.modalRef.hide).toHaveBeenCalled();
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Gate created successfully', 'Success');
    expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Added Gate');
  }));

  it('should handle keyboard events for toggle', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const spy = jest.spyOn(component, 'sortByField');

    component.handleToggleKeydown(event, 'gateName', 'ASC');

    expect(spy).toHaveBeenCalledWith('gateName', 'ASC');
  });

  it('should handle keyboard events for toggle with space key', () => {
    const event = new KeyboardEvent('keydown', { key: ' ' });
    const spy = jest.spyOn(component, 'sortByField');
    const preventDefaultSpy = jest.spyOn(event, 'preventDefault');

    component.handleToggleKeydown(event, 'gateName', 'DESC');

    expect(preventDefaultSpy).toHaveBeenCalled();
    expect(spy).toHaveBeenCalledWith('gateName', 'DESC');
  });

  it('should not handle keyboard events for toggle with other keys', () => {
    const event = new KeyboardEvent('keydown', { key: 'Tab' });
    const spy = jest.spyOn(component, 'sortByField');

    component.handleToggleKeydown(event, 'gateName', 'ASC');

    expect(spy).not.toHaveBeenCalled();
  });

  // Test constructor subscription behaviors
  it('should handle projectId subscription with valid data', () => {
    const spy = jest.spyOn(component, 'getGateList');
    component.ProjectId = 'test-project-id';

    expect(component.ProjectId).toBe('test-project-id');
  });

  it('should handle projectId subscription with empty data', () => {
    const spy = jest.spyOn(component, 'getGateList');
    component.ProjectId = '';

    expect(component.ProjectId).toBe('');
  });

  // Test form validation methods
  it('should validate empty gate name in checkStringEmptyValues', () => {
    const formValue = { gateName: '   ' }; // Only whitespace
    const result = component.checkStringEmptyValues(formValue);
    expect(result).toBe(true);
  });

  it('should validate non-empty gate name in checkStringEmptyValues', () => {
    const formValue = { gateName: 'Valid Gate Name' };
    const result = component.checkStringEmptyValues(formValue);
    expect(result).toBe(false);
  });

  it('should validate alphaOnly method with valid characters', () => {
    const validEvents = [
      { keyCode: 65 }, // A
      { keyCode: 97 }, // a
      { keyCode: 8 },  // Backspace
      { keyCode: 32 }  // Space
    ];

    validEvents.forEach(event => {
      expect(component.alphaOnly(event)).toBe(true);
    });
  });

  it('should validate alphaOnly method with invalid characters', () => {
    const invalidEvents = [
      { keyCode: 48 }, // 0
      { keyCode: 33 }, // !
      { keyCode: 64 }  // @
    ];

    invalidEvents.forEach(event => {
      expect(component.alphaOnly(event)).toBe(false);
    });
  });

  // Test modal operations
  it('should open edit modal with correct data', () => {
    component.gateList = [
      { id: 1, gateName: 'Test Gate', gateAutoId: 123 },
      { id: 2, gateName: 'Another Gate', gateAutoId: 456 }
    ];
    component.gateEditFormGroup();

    const template = {} as any;
    component.openEditModal(template, 0);

    expect(component.editIndex).toBe(0);
    expect(component.gateEditForm.get('gateName')?.value).toBe('Test Gate');
    expect(component.gateEditForm.get('id')?.value).toBe(1);
    expect(component.gateEditForm.get('gateAutoId')?.value).toBe(123);
    expect(modalServiceMock.show).toHaveBeenCalledWith(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-md new-gate-popup custom-modal'
    });
  });

  it('should open delete modal for single item', () => {
    component.gateList = [
      { id: 1, gateName: 'Test Gate', gateAutoId: 123 }
    ];
    const template = {} as any;
    const spy = jest.spyOn(component, 'openModal');

    component.openDeleteModal(0, template);

    expect(component.deleteIndex[0]).toBe(1);
    expect(component.currentDeleteId).toBe(0);
    expect(component.remove).toBe(false);
    expect(spy).toHaveBeenCalledWith(template);
  });

  it('should open delete modal for multiple items', () => {
    const template = {} as any;
    const spy = jest.spyOn(component, 'openModal');

    component.openDeleteModal(-1, template);

    expect(component.remove).toBe(true);
    expect(spy).toHaveBeenCalledWith(template);
  });

  it('should open modal popup with correct configuration', () => {
    const template = {} as any;
    component.openModalPopup(template);

    expect(modalServiceMock.show).toHaveBeenCalledWith(template, {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
    });
  });

  // Test selection operations
  it('should select all gates when selectAll is false', () => {
    component.gateList = [
      { id: 1, gateName: 'Gate 1', isChecked: false },
      { id: 2, gateName: 'Gate 2', isChecked: false }
    ];
    component.selectAll = false;

    component.selectAllGatesData();

    expect(component.selectAll).toBe(true);
    expect(component.gateList[0].isChecked).toBe(true);
    expect(component.gateList[1].isChecked).toBe(true);
  });

  it('should deselect all gates when selectAll is true', () => {
    component.gateList = [
      { id: 1, gateName: 'Gate 1', isChecked: true },
      { id: 2, gateName: 'Gate 2', isChecked: true }
    ];
    component.selectAll = true;

    component.selectAllGatesData();

    expect(component.selectAll).toBe(false);
    expect(component.gateList[0].isChecked).toBe(false);
    expect(component.gateList[1].isChecked).toBe(false);
  });

  it('should toggle individual item selection', () => {
    component.gateList = [
      { id: 1, gateName: 'Gate 1', isChecked: false }
    ];

    component.setSelectedItem(0);

    expect(component.gateList[0].isChecked).toBe(true);

    component.setSelectedItem(0);

    expect(component.gateList[0].isChecked).toBe(false);
  });

  it('should check if any row is selected - with selectAll true', () => {
    component.selectAll = true;
    const result = component.checkSelectedRow();
    expect(result).toBe(false);
  });

  it('should check if any row is selected - with individual selection', () => {
    component.selectAll = false;
    component.gateList = [
      { id: 1, gateName: 'Gate 1', isChecked: true }
    ];

    const result = component.checkSelectedRow();
    expect(result).toBe(false);
  });

  it('should check if no row is selected', () => {
    component.selectAll = false;
    component.gateList = [
      { id: 1, gateName: 'Gate 1', isChecked: false }
    ];

    const result = component.checkSelectedRow();
    expect(result).toBe(true);
  });

  // Test delete operations
  it('should remove items when selectAll is true', () => {
    component.selectAll = true;
    const spy = jest.spyOn(component, 'deleteGateDetail');

    component.removeItem();

    expect(component.deleteSubmitted).toBe(true);
    expect(spy).toHaveBeenCalled();
  });

  it('should remove selected items when selectAll is false', () => {
    component.selectAll = false;
    component.gateList = [
      { id: 1, gateName: 'Gate 1', isChecked: true },
      { id: 2, gateName: 'Gate 2', isChecked: false },
      { id: 3, gateName: 'Gate 3', isChecked: true }
    ];
    component.deleteIndex = [];
    const spy = jest.spyOn(component, 'deleteGateDetail');

    component.removeItem();

    expect(component.deleteSubmitted).toBe(true);
    expect(component.deleteIndex).toContain(1);
    expect(component.deleteIndex).toContain(3);
    expect(component.deleteIndex).not.toContain(2);
    expect(spy).toHaveBeenCalled();
  });

  it('should handle successful gate deletion', () => {
    const mockResponse = { message: 'Gates deleted successfully' };
    projectServiceMock.deleteGate = jest.fn().mockReturnValue(of(mockResponse));
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.deleteIndex = [1, 2];
    component.selectAll = false;
    component.modalRef = { hide: jest.fn() } as unknown as BsModalRef;
    const getGateListSpy = jest.spyOn(component, 'getGateList');

    component.deleteGateDetail();

    expect(projectServiceMock.deleteGate).toHaveBeenCalledWith({
      id: [1, 2],
      ProjectId: '123',
      isSelectAll: false,
      ParentCompanyId: '456'
    });
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Gates deleted successfully', 'Success');
    expect(component.deleteSubmitted).toBe(false);
    expect(component.modalRef.hide).toHaveBeenCalled();
    expect(getGateListSpy).toHaveBeenCalled();
  });

  it('should handle gate deletion error', () => {
    const mockError = {
      message: {
        statusCode: 400,
        details: [{ message: 'Cannot delete gate' }]
      }
    };
    projectServiceMock.deleteGate = jest.fn().mockReturnValue(throwError(() => mockError));
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.deleteIndex = [1];
    const showErrorSpy = jest.spyOn(component, 'showError');
    const getGateListSpy = jest.spyOn(component, 'getGateList');

    component.deleteGateDetail();

    expect(component.deleteSubmitted).toBe(false);
    expect(showErrorSpy).toHaveBeenCalledWith(mockError);
    expect(getGateListSpy).toHaveBeenCalled();
  });

  it('should handle gate deletion error without message', () => {
    const mockError = {};
    projectServiceMock.deleteGate = jest.fn().mockReturnValue(throwError(() => mockError));
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.deleteIndex = [1];

    component.deleteGateDetail();

    expect(component.deleteSubmitted).toBe(false);
    expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should handle gate deletion error with generic message', () => {
    const mockError = { message: 'Generic error message' };
    projectServiceMock.deleteGate = jest.fn().mockReturnValue(throwError(() => mockError));
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.deleteIndex = [1];

    component.deleteGateDetail();

    expect(component.deleteSubmitted).toBe(false);
    expect(toastrServiceMock.error).toHaveBeenCalledWith('Generic error message', 'OOPS!');
  });

  // Test edit operations
  it('should handle successful gate edit', fakeAsync(() => {
    const mockResponse = { message: 'Gate updated successfully' };
    projectServiceMock.updateGate.mockReturnValue(of(mockResponse));

    component.gateEditFormGroup();
    component.gateEditForm.patchValue({
      id: 1,
      gateName: 'Updated Gate Name',
      gateAutoId: 123
    });

    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.modalRef = { hide: jest.fn() } as unknown as BsModalRef;
    const getGateListSpy = jest.spyOn(component, 'getGateList');
    const mixpanelService = TestBed.inject(MixpanelService);

    component.editSubmit();
    tick();

    expect(projectServiceMock.updateGate).toHaveBeenCalledWith({
      id: 1,
      gateName: 'Updated Gate Name',
      ProjectId: '123',
      ParentCompanyId: '456'
    });
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Gate updated successfully', 'Success');
    expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Edited Gate');
    expect(component.editSubmitted).toBe(false);
    expect(component.formEditSubmitted).toBe(false);
    expect(component.modalRef.hide).toHaveBeenCalled();
    expect(getGateListSpy).toHaveBeenCalled();
  }));

  it('should handle gate edit form validation errors', () => {
    component.gateEditFormGroup();
    component.gateEditForm.patchValue({
      id: '',
      gateName: '',
      gateAutoId: 123
    });

    component.editSubmit();

    expect(component.editSubmitted).toBe(true);
    expect(component.formEditSubmitted).toBe(false);
    expect(component.gateEditForm.valid).toBe(false);
    expect(projectServiceMock.updateGate).not.toHaveBeenCalled();
  });

  it('should handle gate edit with empty string validation', () => {
    component.gateEditFormGroup();
    component.gateEditForm.patchValue({
      id: 1,
      gateName: '   ', // Only whitespace
      gateAutoId: 123
    });

    component.editSubmit();

    expect(component.editSubmitted).toBe(false);
    expect(component.formEditSubmitted).toBe(false);
    expect(toastrServiceMock.error).toHaveBeenCalledWith('Please Enter valid Gate Name.', 'OOPS!');
    expect(projectServiceMock.updateGate).not.toHaveBeenCalled();
  });

  it('should handle gate edit error', fakeAsync(() => {
    const mockError = {
      message: {
        statusCode: 400,
        details: [{ message: 'Gate name already exists' }]
      }
    };
    projectServiceMock.updateGate.mockReturnValue(throwError(() => mockError));

    component.gateEditFormGroup();
    component.gateEditForm.patchValue({
      id: 1,
      gateName: 'Test Gate',
      gateAutoId: 123
    });

    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    const showErrorSpy = jest.spyOn(component, 'showError');

    component.editSubmit();
    tick();

    expect(component.editSubmitted).toBe(false);
    expect(component.formEditSubmitted).toBe(false);
    expect(showErrorSpy).toHaveBeenCalledWith(mockError);
  }));

  // Test error handling
  it('should handle showError method', () => {
    const mockError = {
      message: {
        details: [{ message: 'Test error message' }]
      }
    };

    component.showError(mockError);

    expect(component.submitted).toBe(false);
    expect(component.editSubmitted).toBe(false);
    expect(toastrServiceMock.error).toHaveBeenCalledWith('Test error message');
  });

  // Test keyboard event handlers
  it('should handle handleDownKeydown for edit action', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const spy = jest.spyOn(component, 'openEditModal');
    const preventDefaultSpy = jest.spyOn(event, 'preventDefault');

    component.handleDownKeydown(event, 'data', 'item', 'edit');

    expect(preventDefaultSpy).toHaveBeenCalled();
    expect(spy).toHaveBeenCalledWith('data', 'item');
  });

  it('should handle handleDownKeydown for clear action', () => {
    const event = new KeyboardEvent('keydown', { key: ' ' });
    const spy = jest.spyOn(component, 'clear');
    const preventDefaultSpy = jest.spyOn(event, 'preventDefault');

    component.handleDownKeydown(event, 'data', 'item', 'clear');

    expect(preventDefaultSpy).toHaveBeenCalled();
    expect(spy).toHaveBeenCalled();
  });

  it('should handle handleDownKeydown for unknown action', () => {
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    const editSpy = jest.spyOn(component, 'openEditModal');
    const clearSpy = jest.spyOn(component, 'clear');

    component.handleDownKeydown(event, 'data', 'item', 'unknown');

    expect(editSpy).not.toHaveBeenCalled();
    expect(clearSpy).not.toHaveBeenCalled();
  });

  it('should not handle handleDownKeydown for other keys', () => {
    const event = new KeyboardEvent('keydown', { key: 'Tab' });
    const spy = jest.spyOn(component, 'openEditModal');

    component.handleDownKeydown(event, 'data', 'item', 'edit');

    expect(spy).not.toHaveBeenCalled();
  });

  // Test gate switching
  it('should switch gate when different gate is selected', () => {
    const data: any = {
      gateDetails: [{ Gate: { id: 1 } }]
    };
    const gateId = 2;

    component.switchGate(data, gateId);

    expect(data.changedGateId).toBe(2);
  });

  it('should not switch gate when same gate is selected', () => {
    const data: any = {
      gateDetails: [{ Gate: { id: 1 } }]
    };
    const gateId = 1;

    component.switchGate(data, gateId);

    expect(data.changedGateId).toBe(null);
  });

  // Test deactivate modal
  it('should open deactivate modal and fetch mapped requests', () => {
    const template = {} as any;
    const data = {
      id: 1,
      gateName: 'Test Gate',
      gateAutoId: 123
    };
    const mockResponse = {
      data: {
        mappedRequest: [{ id: 1, name: 'Request 1' }],
        gates: [{ id: 1, name: 'Gate 1' }]
      }
    };

    projectServiceMock.getMappedRequests = jest.fn().mockReturnValue(of(mockResponse));
    component.ProjectId = '123';

    component.openDeactivateModal(template, data, false);

    expect(component.gateData).toBe(data);
    expect(modalServiceMock.show).toHaveBeenCalledWith(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-md new-gate-popup custom-modal deactivate-modal'
    });
    expect(component.getMappedRequestLoader).toBe(true);
    expect(projectServiceMock.getMappedRequests).toHaveBeenCalledWith({
      ProjectId: '123',
      id: 123,
      gateName: 'Test Gate'
    });
  });

  it('should activate gate when event is true', () => {
    const data = { id: 1, gateName: 'Test Gate' };
    const spy = jest.spyOn(component, 'activateGate');

    component.openDeactivateModal({} as any, data, true);

    expect(spy).toHaveBeenCalledWith(data);
  });

  // Test reset operations
  it('should reset and close modal', () => {
    component.gateForm.patchValue({ gateName: 'Test' });
    component.submitted = true;
    component.formSubmitted = true;
    component.editSubmitted = true;
    component.formEditSubmitted = true;
    component.modalRef = { hide: jest.fn() } as unknown as BsModalRef;

    component.resetAndClose();

    expect(component.gateForm.get('gateName')?.value).toBe(null);
    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
    expect(component.editSubmitted).toBe(false);
    expect(component.formEditSubmitted).toBe(false);
    expect(component.modalRef.hide).toHaveBeenCalled();
  });

  // Test close method
  it('should close addGate form when dirty', () => {
    const template = {} as any;
    component.gateForm.markAsDirty();
    const spy = jest.spyOn(component, 'openModalPopup');

    component.close(template, 'addGate');

    expect(spy).toHaveBeenCalledWith(template);
  });

  it('should close addGate form when not dirty', () => {
    const template = {} as any;
    const spy = jest.spyOn(component, 'resetForm');

    component.close(template, 'addGate');

    expect(spy).toHaveBeenCalledWith('yes');
  });

  it('should close editGate form when dirty', () => {
    const template = {} as any;
    component.gateEditFormGroup();
    component.gateEditForm.markAsDirty();
    const spy = jest.spyOn(component, 'openModalPopup');

    component.close(template, 'editGate');

    expect(spy).toHaveBeenCalledWith(template);
  });

  it('should close editGate form when not dirty', () => {
    const template = {} as any;
    component.gateEditFormGroup();
    const spy = jest.spyOn(component, 'resetForm');

    component.close(template, 'editGate');

    expect(spy).toHaveBeenCalledWith('yes');
  });

  it('should close deactivateGate modal', () => {
    const template = {} as any;
    component.modalRef = { hide: jest.fn() } as unknown as BsModalRef;
    const spy = jest.spyOn(component, 'getGateList');

    component.close(template, 'deactivateGate');

    expect(component.modalRef.hide).toHaveBeenCalled();
    expect(spy).toHaveBeenCalled();
  });

  // Test resetForm method
  it('should handle resetForm with "no" action', () => {
    component.modalRef1 = { hide: jest.fn() } as unknown as BsModalRef;

    component.resetForm('no');

    expect(component.modalRef1.hide).toHaveBeenCalled();
  });

  it('should handle resetForm with "yes" action', () => {
    component.gateForm.patchValue({ gateName: 'Test' });
    component.submitted = true;
    component.formSubmitted = true;
    component.editSubmitted = true;
    component.formEditSubmitted = true;
    component.modalRef = { hide: jest.fn() } as unknown as BsModalRef;
    component.modalRef1 = { hide: jest.fn() } as unknown as BsModalRef;

    component.resetForm('yes');

    expect(component.modalRef1.hide).toHaveBeenCalled();
    expect(component.gateForm.get('gateName')?.value).toBe(null);
    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
    expect(component.editSubmitted).toBe(false);
    expect(component.formEditSubmitted).toBe(false);
    expect(component.modalRef.hide).toHaveBeenCalled();
  });

  // Test clear method
  it('should clear search and reset pagination', () => {
    component.showSearchbar = true;
    component.search = 'test search';
    component.currentPageNo = 3;
    const spy = jest.spyOn(component, 'getGateList');

    component.clear();

    expect(component.showSearchbar).toBe(false);
    expect(component.search).toBe('');
    expect(component.currentPageNo).toBe(1);
    expect(spy).toHaveBeenCalled();
  });

  // Test searchGate method
  it('should handle search with data', () => {
    component.currentPageNo = 3;
    const spy = jest.spyOn(component, 'getGateList');

    component.searchGate('test search');

    expect(component.showSearchbar).toBe(true);
    expect(component.search).toBe('test search');
    expect(component.currentPageNo).toBe(1);
    expect(spy).toHaveBeenCalled();
  });

  it('should handle search with empty data', () => {
    component.showSearchbar = true;
    component.currentPageNo = 3;
    const spy = jest.spyOn(component, 'getGateList');

    component.searchGate('');

    expect(component.showSearchbar).toBe(false);
    expect(component.search).toBe('');
    expect(component.currentPageNo).toBe(1);
    expect(spy).toHaveBeenCalled();
  });

  // Test sortByField method
  it('should handle sorting and reset pagination', () => {
    component.currentPageNo = 3;
    const spy = jest.spyOn(component, 'getGateList');

    component.sortByField('gateName', 'ASC');

    expect(component.sortColumn).toBe('gateName');
    expect(component.sort).toBe('ASC');
    expect(component.currentPageNo).toBe(1);
    expect(spy).toHaveBeenCalled();
  });

  // Test getGateList method edge cases
  it('should not call gateList when required parameters are missing', () => {
    component.ProjectId = '';
    component.ParentCompanyId = '456';
    component.pageSize = 25;
    component.currentPageNo = 1;

    component.getGateList();

    expect(projectServiceMock.gateList).not.toHaveBeenCalled();
  });

  it('should handle getGateList with selectAll true', () => {
    const mockResponse = {
      data: {
        rows: [
          { id: 1, gateName: 'Gate 1', isChecked: false },
          { id: 2, gateName: 'Gate 2', isChecked: false }
        ],
        count: 2
      },
      lastId: 123
    };

    projectServiceMock.gateList.mockReturnValue(of(mockResponse));
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.pageSize = 25;
    component.currentPageNo = 1;
    component.selectAll = true;

    component.getGateList();

    expect(component.gateList[0].isChecked).toBe(true);
    expect(component.gateList[1].isChecked).toBe(true);
    expect(component.totalCount).toBe(2);
    expect(component.lastId).toBe(123);
    expect(component.loader).toBe(false);
  });

  it('should handle getGateList with selectAll false', () => {
    const mockResponse = {
      data: {
        rows: [
          { id: 1, gateName: 'Gate 1', isChecked: true },
          { id: 2, gateName: 'Gate 2', isChecked: true }
        ],
        count: 2
      },
      lastId: 123
    };

    projectServiceMock.gateList.mockReturnValue(of(mockResponse));
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.pageSize = 25;
    component.currentPageNo = 1;
    component.selectAll = false;

    component.getGateList();

    expect(component.selectAll).toBe(false);
    expect(component.gateList[0].isChecked).toBe(false);
    expect(component.gateList[1].isChecked).toBe(false);
  });

  // Test deactivateGate method edge cases
  it('should handle deactivateGate with partial gate switching', () => {
    component.gateData = { id: 1 };
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.deliveryGateRequestList = [
      { id: 1, changedGateId: 2 },
      { id: 2, changedGateId: null }
    ];

    component.deactivateGate();

    expect(component.deactivateGateLoader).toBe(false);
    expect(projectServiceMock.deactivateGate).not.toHaveBeenCalled();
  });

  it('should handle deactivateGate error with no message', () => {
    const mockError = {};
    projectServiceMock.deactivateGate = jest.fn().mockReturnValue(throwError(() => mockError));

    component.gateData = { id: 1 };
    component.ProjectId = '123';
    component.ParentCompanyId = '456';
    component.deliveryGateRequestList = [];

    component.deactivateGate();

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  // Test form submission with checkStringEmptyValues returning true
  it('should handle form submission with empty gate name after trim', () => {
    component.gateForm.patchValue({
      gateName: '   ' // Only whitespace
    });
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    component.onSubmit();

    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
    expect(toastrServiceMock.error).toHaveBeenCalledWith('Please Enter valid Gate Name.', 'OOPS!');
    expect(projectServiceMock.addGate).not.toHaveBeenCalled();
  });

  // Test activateGate error scenarios
  it('should handle activateGate error with no message object', () => {
    const mockError = {};
    projectServiceMock.updateGate.mockReturnValue(throwError(() => mockError));

    const mockGateData = {
      id: 1,
      gateName: 'Test Gate',
      gateAutoId: 123
    };
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    component.activateGate(mockGateData);

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should handle activateGate error with generic message', () => {
    const mockError = { message: 'Generic error' };
    projectServiceMock.updateGate.mockReturnValue(throwError(() => mockError));

    const mockGateData = {
      id: 1,
      gateName: 'Test Gate',
      gateAutoId: 123
    };
    component.ProjectId = '123';
    component.ParentCompanyId = '456';

    component.activateGate(mockGateData);

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Generic error', 'OOPS!');
  });

  // Test allowAlphaNumericForGateName edge cases
  it('should allow numbers in gate name', () => {
    const numberEvents = [
      { which: 48, keyCode: 48 }, // 0
      { which: 57, keyCode: 57 }  // 9
    ];

    numberEvents.forEach(event => {
      expect(component.allowAlphaNumericForGateName(event)).toBe(true);
    });
  });

  it('should handle allowAlphaNumericForGateName with keyCode fallback', () => {
    const event = { which: null, keyCode: 65 }; // A
    expect(component.allowAlphaNumericForGateName(event)).toBe(true);
  });

  // Test edge case for alphaOnly with boundary values
  it('should handle alphaOnly boundary values', () => {
    const boundaryEvents = [
      { keyCode: 64 }, // @ (just before A)
      { keyCode: 91 }, // [ (just after Z)
      { keyCode: 96 }, // ` (just before a)
      { keyCode: 129 } // just after valid range
    ];

    boundaryEvents.forEach(event => {
      expect(component.alphaOnly(event)).toBe(false);
    });
  });
});
