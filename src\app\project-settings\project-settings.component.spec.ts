import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ProjectSettingsComponent } from './project-settings.component';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { ProjectInformationComponent } from './project-information/project-information.component';
import { ProjectSettingsTabComponent } from './project-settings-tab/project-settings-tab.component';
import { LocationSettingsComponent } from './location-settings/location-settings.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { GoogleMapsModule } from '@angular/google-maps';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RendererFactory2, Renderer2 } from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { ProjectService } from '../services/profile/project.service';
import { ApiService } from '../services/api_base/api.service';
import { of } from 'rxjs';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { NgxPaginationModule } from 'ngx-pagination';

// Mock ApiService
class MockApiService {
  get = jest.fn().mockReturnValue(of({}));
  post = jest.fn().mockReturnValue(of({}));
  put = jest.fn().mockReturnValue(of({}));
  delete = jest.fn().mockReturnValue(of({}));
}

// Mock ProjectService
class MockProjectService {
  projectParent = of({});
  getProjectSubscription = jest.fn().mockReturnValue(of({}));
  getProjectSharingSettings = jest.fn().mockReturnValue(of({}));
  getProjectLogisticPlan = jest.fn().mockReturnValue(of({}));
}

// Mock Renderer2
class MockRenderer2 implements Renderer2 {
  data: { [key: string]: any } = {};
  destroyNode: ((node: any) => void) | null = null;
  createElement(name: string, namespace?: string | null): any {
    return document.createElement(name);
  }
  createComment(value: string): any {
    return document.createComment(value);
  }
  createText(value: string): any {
    return document.createTextNode(value);
  }
  appendChild(parent: any, newChild: any): void {
    parent.appendChild(newChild);
  }
  insertBefore(parent: any, newChild: any, refChild: any): void {
    parent.insertBefore(newChild, refChild);
  }
  removeChild(parent: any, oldChild: any): void {
    parent.removeChild(oldChild);
  }
  selectRootElement(selectorOrNode: string | any): any {
    return document.querySelector(selectorOrNode);
  }
  parentNode(node: any): any {
    return node.parentNode;
  }
  nextSibling(node: any): any {
    return node.nextSibling;
  }
  setAttribute(el: any, name: string, value: string, namespace?: string | null): void {
    el.setAttribute(name, value);
  }
  removeAttribute(el: any, name: string, namespace?: string | null): void {
    el.removeAttribute(name);
  }
  addClass(el: any, name: string): void {
    el.classList.add(name);
  }
  removeClass(el: any, name: string): void {
    el.classList.remove(name);
  }
  setStyle(el: any, style: string, value: any, flags?: any): void {
    el.style[style] = value;
  }
  removeStyle(el: any, style: string, flags?: any): void {
    el.style[style] = '';
  }
  setProperty(el: any, name: string, value: any): void {
    el[name] = value;
  }
  setValue(node: any, value: string): void {
    node.nodeValue = value;
  }
  listen(target: any, eventName: string, callback: (event: any) => boolean | void): () => void {
    target.addEventListener(eventName, callback);
    return () => target.removeEventListener(eventName, callback);
  }
  destroy(): void {
    // Cleanup if needed
  }
}

// Mock RendererFactory2
class MockRendererFactory2 implements RendererFactory2 {
  createRenderer(hostElement: any, type: any): Renderer2 {
    return new MockRenderer2();
  }
}

// Mock Google Maps API
const mockGoogleMaps = {
  maps: {
    MapTypeId: {
      SATELLITE: 'satellite',
      ROADMAP: 'roadmap'
    },
    Animation: {
      DROP: 'DROP',
      BOUNCE: 'BOUNCE'
    },
    MapMouseEvent: class {
      latLng: { lat: () => number; lng: () => number };
      constructor() {
        this.latLng = {
          lat: () => 38.897957,
          lng: () => -77.036560
        };
      }
    },
    Geocoder: class {
      geocode = jest.fn().mockImplementation((request, callback) => {
        callback([{ formatted_address: 'Test Address' }], 'OK');
      });
    }
  }
};

// Add Google Maps to window object
(window as any).google = mockGoogleMaps;

describe('ProjectSettingsComponent', () => {
  let component: ProjectSettingsComponent;
  let fixture: ComponentFixture<ProjectSettingsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        ProjectSettingsComponent,
        ProjectInformationComponent,
        ProjectSettingsTabComponent,
        LocationSettingsComponent
      ],
      imports: [
        TabsModule,
        GoogleMapsModule,
        BrowserAnimationsModule,
        HttpClientModule,
        ToastrModule.forRoot(),
        NgxPaginationModule
      ],
      providers: [
        BsModalService,
        { provide: BsModalRef, useValue: {} },
        { provide: RendererFactory2, useClass: MockRendererFactory2 },
        { provide: DOCUMENT, useValue: document },
        { provide: ApiService, useClass: MockApiService },
        { provide: ProjectService, useClass: MockProjectService },
        ToastrService
      ],
      schemas: [NO_ERRORS_SCHEMA] // This will ignore any unknown elements and attributes
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ProjectSettingsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should render the component with three tabs', () => {
    const compiled = fixture.nativeElement;
    const tabs = compiled.querySelectorAll('tab');
    expect(tabs.length).toBe(3);
  });

  it('should have correct tab headings', () => {
    const compiled = fixture.nativeElement;
    const tabHeadings = compiled.querySelectorAll('tab');
    
    expect(tabHeadings[0].getAttribute('heading')).toBe('Project Information');
    expect(tabHeadings[1].getAttribute('heading')).toBe('Project Settings');
    expect(tabHeadings[2].getAttribute('heading')).toBe('Location Settings');
  });

  it('should contain the required child components', () => {
    const compiled = fixture.nativeElement;
    
    expect(compiled.querySelector('app-project-information')).toBeTruthy();
    expect(compiled.querySelector('app-project-settings-tab')).toBeTruthy();
    expect(compiled.querySelector('app-location-settings')).toBeTruthy();
  });
});
