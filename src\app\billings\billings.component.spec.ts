import { ComponentFixture, TestBed } from '@angular/core/testing';
import { BillingsComponent } from './billings.component';

describe('BillingsComponent', () => {
  let component: BillingsComponent;
  let fixture: ComponentFixture<BillingsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ BillingsComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(BillingsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct default values', () => {
    expect(component.billingList).toBe(true);
    expect(component.showbillingHistory).toBe(false);
  });

  describe('handleToggleKeydown', () => {
    it('should call showHistory when Enter key is pressed', () => {
      const showHistorySpy = jest.spyOn(component, 'showHistory');
      const event = {
        key: 'Enter',
        preventDefault: jest.fn()
      } as unknown as KeyboardEvent;

      component.handleToggleKeydown(event);

      expect(showHistorySpy).toHaveBeenCalled();
      expect(event.preventDefault).toHaveBeenCalled();
    });

    it('should call showHistory when Space key is pressed', () => {
      const showHistorySpy = jest.spyOn(component, 'showHistory');
      const event = {
        key: ' ',
        preventDefault: jest.fn()
      } as unknown as KeyboardEvent;

      component.handleToggleKeydown(event);

      expect(showHistorySpy).toHaveBeenCalled();
      expect(event.preventDefault).toHaveBeenCalled();
    });

    it('should not call showHistory for other keys', () => {
      const showHistorySpy = jest.spyOn(component, 'showHistory');
      const event = {
        key: 'A',
        preventDefault: jest.fn()
      } as unknown as KeyboardEvent;

      component.handleToggleKeydown(event);

      expect(showHistorySpy).not.toHaveBeenCalled();
      expect(event.preventDefault).not.toHaveBeenCalled();
    });
  });

  describe('showHistory', () => {
    it('should update component state to show history', () => {
      component.showHistory();

      expect(component.billingList).toBe(false);
      expect(component.showbillingHistory).toBe(true);
    });
  });

  describe('backtoList', () => {
    it('should update component state to show billing list', () => {
      // First show history
      component.showHistory();
      expect(component.billingList).toBe(false);
      expect(component.showbillingHistory).toBe(true);

      // Then go back to list
      component.backtoList();

      expect(component.billingList).toBe(true);
      expect(component.showbillingHistory).toBe(false);
    });
  });
});
