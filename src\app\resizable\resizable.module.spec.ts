import { TestBed } from '@angular/core/testing';
import { ResizableModule } from './resizable.module';
import { ResizableComponent } from './resizable.component';
import { ResizableDirective } from './resizable.directive';

describe('ResizableModule', () => {
  let module: ResizableModule;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [ResizableModule]
    });

    module = TestBed.inject(ResizableModule);
  });

  it('should create an instance', () => {
    expect(module).toBeTruthy();
  });

  it('should provide ResizableComponent', () => {
    const fixture = TestBed.createComponent(ResizableComponent);
    expect(fixture.componentInstance).toBeTruthy();
  });

  it('should declare and export necessary components and directives', () => {
    // Check that the module has the expected declarations and exports
    // This is a more direct way to test the module configuration

    // Get the module definition
    const moduleDefinition = (ResizableModule as any).ɵmod;

    // Check declarations
    expect(moduleDefinition.declarations).toContain(ResizableComponent);
    expect(moduleDefinition.declarations).toContain(ResizableDirective);

    // Check exports
    expect(moduleDefinition.exports).toContain(ResizableComponent);
  });
});
