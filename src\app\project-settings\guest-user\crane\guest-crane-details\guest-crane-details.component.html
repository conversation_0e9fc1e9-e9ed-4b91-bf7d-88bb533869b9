<div class="modal-header">
  <h2 class="fs14 fw-bold cairo-regular color-text7 my-1">
    <img src="./assets/images/delivery-pop.svg" alt="Delivery" class="me-2" />Crane Booking Details
  </h2>
  <button
    type="button"
    class="close ms-auto"
    aria-label="Close"
    (click)="confirmationClose(cancelConfirmationPopup)"
  >
    <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close" /></span>
  </button>
</div>
<div class="modal-body pt-0 px-0 pb-4">
  <div class="delivery-details-content" id="delivery-content3">
    <tabset>
      <tab heading="Details" id="tab1" active="active" [active]="currentTabId === 0">
        <div class="delivery-ddetails-content my-3 mx-2 text-center" *ngIf="loader">
          <div class="fs18 fw-bold cairo-regular my-5 text-black">Loading...</div>
        </div>
        <div class="delivery-ddetails-content my-3 mx-2" *ngIf="!loader">
          <div class="col-md-10 my-4 px-2">
            <h4 class="color-grey14 fs12 font-weight-normal">Description</h4>
            <p class="color-grey15 fs12 fw500">{{ NDRData.description }}</p>
          </div>
          <div class="row px-2 my-4">
            <div class="col-md-4 col-lg-4">
              <h4 class="color-grey14 fs12 mb5 font-weight-normal">Crane Booking ID</h4>
              <p class="color-grey15 fs13 mb-2 fw500">{{ NDRData.CraneRequestId }}</p>
            </div>
            <div class="col-md-4 col-lg-4">
              <h4 class="color-grey14 fs12 mb5 font-weight-normal">Time - Date</h4>
              <p class="color-grey15 fs13 mb-1 fw500" *ngIf="NDRData.craneDeliveryStart">
                {{ NDRData.craneDeliveryStart | date : 'shortTime' }} - {{ NDRData.craneDeliveryEnd | date : 'shortTime' }}
                {{ NDRData.craneDeliveryStart | date : 'MM/dd/yyyy' }}
              </p>
              <p class="color-grey15 fs13 mb-1 fw500" *ngIf="!NDRData.craneDeliveryStart">-</p>
            </div>
            <div class="col-md-5 col-lg-4">
              <h4 class="color-grey14 fs12 mb5 font-weight-normal">Responsible Company</h4>
              <div *ngIf="NDRData.companyDetails && NDRData.companyDetails?.length > 0">
                <p class="color-grey15 fs13 mb-2 fw500" *ngFor="let item of NDRData.companyDetails">
                  {{ item?.Company?.companyName }}
                </p>
              </div>
              <p
                class="color-grey15 fs13 mb-2 fw500"
                *ngIf="!NDRData.companyDetails || NDRData.companyDetails?.length === 0"
              >
                -
              </p>
            </div>
          </div>

          <div class="row px-2 my-4">
            <div class="col-md-5 col-lg-4">
              <h4 class="color-grey14 fs12 mb5 font-weight-normal">Responsible Person</h4>
              <div *ngIf="NDRData.memberDetails && NDRData.memberDetails?.length > 0">
                <p
                  class="bg-grey14 rounded-circle w30 h30 text-center d-inline-block me-2 text-white fs10 fw700 lh-30 eye-cursor"
                  *ngFor="let item of NDRData.memberDetails?.slice(0, 3); let i = index"
                >
                  <span
                    *ngIf="item?.Member?.User?.firstName"
                    tooltip="{{ item?.Member?.User?.firstName }} {{ item?.Member?.User?.lastName }}"
                    placement="top"
                  >
                    {{ getResponsiblePeople(item?.Member?.User) }}
                  </span>
                  <span
                    *ngIf="!item?.Member?.User?.firstName"
                    tooltip="{{ item?.Member?.User?.email }}"
                    placement="top"
                  >
                    {{ getResponsiblePeople(item?.Member?.User) }}
                  </span>
                </p>
              </div>
            </div>

            <div class="col-md-4 col-lg-4">
              <h4 class="color-grey14 fs12 mb5 font-weight-normal">Definable Feature of work</h4>
              <div *ngIf="NDRData.defineWorkDetails && NDRData.defineWorkDetails?.length > 0">
                <p
                  class="color-grey15 fs13 mb-2 fw500"
                  *ngFor="let item of NDRData.defineWorkDetails"
                >
                  {{ item?.DeliverDefineWork?.DFOW }}
                </p>
              </div>
              <p
                class="color-grey15 fs13 mb-2 fw500"
                *ngIf="!NDRData.defineWorkDetails || NDRData.defineWorkDetails?.length === 0"
              >
                -
              </p>
            </div>
            <div class="col-md-4 col-lg-4">
              <h4 class="color-grey14 fs12 mb5 font-weight-normal">Crane Booking Status</h4>
              <p class="color-grey15 fs13 mb-2 fw500" *ngIf="NDRData?.status === 'Approved'">
                {{ NDRData?.status }} by {{ NDRData?.approverDetails?.User?.firstName }}
                {{ NDRData?.approverDetails?.User?.lastName }} on
                {{ NDRData?.approved_at | date : 'mediumDate' }}
              </p>
              <p class="color-grey15 fs13 mb-2 fw500" *ngIf="NDRData?.status === 'Pending'">
                Pending
              </p>
              <p
                class="color-grey15 fs13 mb-2 fw500"
                *ngIf="NDRData?.status === 'Completed' || NDRData?.status === 'Delivered'"
              >
                Completed
              </p>
              <p class="color-grey15 fs13 mb-2 fw500" *ngIf="NDRData?.status === 'Expired'">
                Expired
              </p>
              <p class="color-grey15 fs13 mb-2 fw500" *ngIf="NDRData?.status === 'Declined'">
                Declined
              </p>
            </div>
          </div>
          <div class="row px-2 my-4">
            <div class="col-md-4 col-lg-4">
              <h4 class="color-grey14 fs12 mb5 font-weight-normal">Equipment Needed</h4>
              <div *ngIf="NDRData.equipmentDetails && NDRData.equipmentDetails?.length > 0">
                <p
                  class="color-grey15 fs13 mb0 fw500"
                  *ngFor="let item of NDRData.equipmentDetails"
                >
                  {{ item?.Equipment?.equipmentName }}
                </p>
              </div>
              <p
                class="color-grey15 fs13 mb0 fw500"
                *ngIf="!NDRData.equipmentDetails || NDRData.equipmentDetails?.length === 0"
              >
                -
              </p>
            </div>
            <div class="col-md-4 col-lg-4">
              <h4 class="color-grey14 fs12 mb5 font-weight-normal">Picking From</h4>
              <p class="color-grey15 fs13 mb-2 fw500" *ngIf="NDRData.pickUpLocation">
                {{ NDRData?.pickUpLocation }}
              </p>
              <p class="color-grey15 fs13 mb-2 fw500" *ngIf="!NDRData.pickUpLocation">-</p>
            </div>
            <div class="col-md-4 col-lg-4">
              <h4 class="color-grey14 fs12 mb5 font-weight-normal">Picking To</h4>
              <p class="color-grey15 fs13 mb-2 fw500" *ngIf="NDRData.dropOffLocation">
                {{ NDRData?.dropOffLocation }}
              </p>
              <p class="color-grey15 fs13 mb-2 fw500" *ngIf="!NDRData.dropOffLocation">-</p>
            </div>
            <div class="col-md-4 col-lg-4 pt-4" *ngIf="NDRData?.recurrence?.recurrence">
              <h4 class="color-grey14 fs12 font-weight-normal">Recurrence</h4>
              <p class="color-grey15 fs12 fw500">{{ NDRData?.recurrence?.recurrence }}</p>
            </div>
            <div class="col-md-4 col-lg-4 pt-4" *ngIf="NDRData?.location">
              <h4 class="color-grey14 fs12 font-weight-normal">Location</h4>
              <p class="color-grey15 fs12 fw500">{{ NDRData?.location?.locationPath }}</p>
            </div>
          </div>
          <div class="row px-2 my-4">
            <div class="col-md-12 col-lg-12">
              <h4
                class="color-grey14 fs12 mb5 mt-3 font-weight-normal ps-2 ps-md-0"
                *ngIf="NDRData?.additionalNotes"
              >
                Note
              </h4>
              <p class="color-grey15 fs12 mb-2 fw500 col-md-10 ps-2 ps-md-0">
                {{ NDRData.additionalNotes }}
              </p>
            </div>
          </div>
        </div>
      </tab>
      <tab heading="Attachments" [active]="currentTabId === 1">
        <div class="mx-2 mx-md-5 my-5 bulkupload">
          <ngx-file-drop dropZoneLabel="Drop files here"
          (onFileDrop)="dropped($event)"
          (onFileOver)="fileOver($event)"
          (onFileLeave)="fileLeave($event)"
          multiple="true">
            <ng-template ngx-file-drop-content-tmp let-openFileSelector="openFileSelector">
              <div class="bulkupload-content text-center" (click)="openFileSelector()" (keydown)="openFileSelector()">
                <img src="./assets/images/file.svg" alt="Excel" />
                <p class="fs14 fw600 mb3 pt10 color-grey7">Drag & Drop your file here</p>
                <p class="fs12 color-grey8 fw500 mb3">Or</p>
                <label for="browse" class="color-blue4 fs14 mb10 fw500 text-underline"
                  >Click here to browse</label
                >
              </div>
            </ng-template>
          </ngx-file-drop>
          <p class="fs10 color-grey8 fw500 my-2 text-end">
            *Supported formats are .jpg, .png, .doc and .pdf
          </p>
          <div *ngFor="let data of fileData; let firstIndex = index">
            <div class="row upload-table py-3 m-0" *ngFor="let item of data; let i = index">
              <div class="col-1 col-md-1 ps-0">
                <img
                  src="{{ item.image }}"
                  alt="attached-file"
                  class="rounded upload-img"
                  *ngIf="item.extension != 'pdf' && item.extension != 'doc'"
                />
                <img
                  src="./assets/images/pdf-icon.png"
                  alt="attached-file"
                  class="rounded upload-img"
                  *ngIf="item.extension === 'pdf'"
                />
                <img
                  src="./assets/images/doc-icon.png"
                  alt="attached-file"
                  class="rounded upload-img"
                  *ngIf="item.extension === 'doc'"
                />
              </div>
              <div class="col-9 col-md-9">
                <h1 class="fs16 ms-4 color-grey7">{{ item?.relativePath }}</h1>
                <p class="ms-4 mb-0 color-grey8"><span class="ms-3"></span></p>
              </div>
              <div
                class="col-1 col-md-2 text-end d-flex justify-content-end pe-0"
                (click)="removeFile(firstIndex, i)"  (keydown)="handleDownKeydown($event, firstIndex,i ,'remove')"
              >
                <img src="./assets/images/delete.svg" alt="delete" class="h-15px c-pointer" />
              </div>
            </div>
          </div>
          <div class="text-center">
            <button
              class="btn btn-orange radius20 fs12 fw-bold cairo-regular my-4 px-5"
              type="submit"
              (click)="uploadData()"
              *ngIf="fileData[0]?.length > 0"
              [disabled]="uploadSubmitted"
            >
              <em class="fa fa-spinner" aria-hidden="true" *ngIf="uploadSubmitted"></em>
              Done
            </button>
          </div>

          <h5 class="fw600 fs14" *ngIf="fileArray.length > 0">Uploaded Documents</h5>
          <div
            class="row align-items-center upload-table py-3 m-0"
            *ngFor="let item of fileArray; let i = index"
            [ngClass]="fileArray.length - 1 == i ? '' : 'upload-layout'"
          >
            <div class="col-1 col-md-1 ps-0">
              <a href="{{ item.attachement }}">
                <img
                  src="{{ item.attachement }}"
                  alt="attached-file"
                  class="rounded upload-img"
                  *ngIf="item.extension != 'pdf' && item.extension != 'doc'"
                />
                <img
                  src="./assets/images/pdf-icon.png"
                  alt="attached-file"
                  class="rounded upload-img"
                  *ngIf="item.extension === 'pdf'"
                />
                <img
                  src="./assets/images/doc-icon.png"
                  alt="attached-file"
                  class="rounded upload-img"
                  *ngIf="item.extension === 'doc'"
                />
              </a>
            </div>
            <div class="col-9 col-md-9">
              <h1 class="fs16 ms-4 color-grey7">
                <a href="{{ item.attachement }}">{{ item.filename }}</a>
              </h1>
              <p class="ms-4 mb-0 color-grey8">
                {{ item.createdAt | date : 'medium' }}<span class="ms-3"></span>
              </p>
            </div>
          </div>
        </div>
      </tab>
      <tab heading="Comments" [active]="currentTabId === 2">
        <div class="comments-form my-3 mx-3">
          <ul class="list-group radius0">
            <li
              class="list-group-item px-0 py-2 d-flex align-content-end border-start-0 border-end-0 border-top-0"
              *ngFor="let item of commentList"
            >
              <img
                src="./assets/images/default-user.svg"
                *ngIf="!item.Member?.User?.profilePic"
                alt="user-profile"
                class="rounded-circle h-30px w-30px"
              />
              <img
                src="{{ item.Member?.User?.profilePic }}"
                *ngIf="item.Member?.User?.profilePic"
                alt="user-profile"
                class="rounded-circle h-30px w-30px"
              />
              <div class="ps-3">
                <p class="color-grey4 fs12 fw600 mb-1">
                  {{ item?.Member?.User?.firstName }} {{ item?.Member?.User?.lastName }}
                </p>
                <p class="color-grey4 fs10 mb-1">{{ item.comment }}</p>
                <p class="color-grey8 fs10 mb-0">{{ item.createdAt | date : 'medium' }}</p>
              </div>
            </li>
          </ul>
          <form
            class="custom-material-form"
            [formGroup]="commentDetailsForm"
            (ngSubmit)="onCommentSubmit()"
            novalidate
          >
            <div class="row">
              <div class="col-md-9">
                <div class="form-group">
                  <label class="fs12 fw500" for="comments">Enter your comments here</label>
                  <textarea id="comments"
                    class="form-control fs12"
                    formControlName="comment"
                    placeholder=""
                    rows="3"
                    maxlength="150"
                  ></textarea>
                  <div
                    class="color-red"
                    *ngIf="submitted && commentDetailsForm.get('comment').errors"
                  >
                    <small *ngIf="commentDetailsForm.get('comment').errors.required"
                      >*Comment is required.</small
                    >
                  </div>
                </div>
                <div class="d-flex justify-content-end">
                  <button
                    class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem"
                    type="submit"
                    [disabled]="formSubmitted && commentDetailsForm.valid"
                  >
                    <em
                      class="fa fa-spinner"
                      aria-hidden="true"
                      *ngIf="formSubmitted && commentDetailsForm.valid"
                    ></em
                    >Submit
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </tab>
      <tab heading="History" [active]="currentTabId === 3">
        <div class="history-list my-3 mx-3">
          <div
            class="fs18 fw-bold cairo-regular my-5 text-black text-center"
            *ngIf="loader"
          >
            Loading...
          </div>
          <div
            class="fs18 fw-bold cairo-regular my-5 text-black text-center"
            *ngIf="!loader && historyList.length === 0"
          >
            No History Found.
          </div>
          <ul class="list-group" *ngIf="historyList.length > 0">
            <li class="list-group-item px-0 py-2 list-item" *ngFor="let item of historyList">
              <p class="color-grey8 fs12 mb3">{{ item.createdAt | date : 'medium' }}</p>
              <p class="color-grey4 fs12 mb3 fw600">{{ item.description }}</p>
            </li>
          </ul>
        </div>
      </tab>
    </tabset>
  </div>
  <div class="text-center mt-4 mb-4">
    <button
      class="fs12 cairo-regular btn btn-green radius20 px-5 mx-2 edit-btn-popover"
      (click)="openEditModal(currentDeliverySaveItem, null)"
      *ngIf="
        (ProjectId && !currentDeliverySaveItem?.recurrence) ||
        (ProjectId &&
          currentDeliverySaveItem?.recurrence &&
          currentDeliverySaveItem?.recurrence?.recurrence === 'Does Not Repeat')
      "
    >
      Edit
    </button>
    <button
      class="fs12 cairo-regular btn btn-green radius20 px-5 mx-2 edit-btn-popover"
      *ngIf="
        ProjectId &&
        currentDeliverySaveItem?.recurrence &&
        currentDeliverySaveItem?.recurrence?.recurrence !== 'Does Not Repeat'
      "
      (click)="changeRequestCollapse(currentDeliverySaveItem)"
      [popover]="popTemplate"
      popoverTitle=""
      placement="top"
    >
      Edit
      <img
        src="./assets/images/green-downarrow.svg"
        alt="Down Arrow"
        class="arrow float-end eye-cursor mb-1 ms-2 mt-0"
        [ngClass]="{ rotateup: allRequestIsOpened }"
        containerClass="editcustomClass"
      />
    </button>
  </div>
</div>

<!--Confirmation Popup-->
<div id="confirm-popup5">
  <ng-template #cancelConfirmationPopup>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure you want to cancel?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetForm('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="resetForm('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>

<!-- PopTemplate -->
<ng-template #popTemplate>
  <ul class="list-group c-pointer event-selection-popup my-2 w190">
    <li
      class="list-group-item border-0 p-1 cairo-regular c-pointer fw700 fs12"
      *ngFor="let data of seriesOptions"
      [ngClass]="{ disabled: data.disabled }"
      [tabindex]="data.disabled ? '-1' : '0'"
      (click)="openEditModal(currentDeliverySaveItem, data?.option)"
      (keydown)="handleDownKeydown($event, currentDeliverySaveItem,data?.option,'open')"
    >
      <span class="ps-2">{{ data?.text }}</span>
    </li>
  </ul>
</ng-template>
