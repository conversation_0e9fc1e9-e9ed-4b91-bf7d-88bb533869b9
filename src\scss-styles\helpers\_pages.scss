.mb34 {
  margin-bottom: 34px;
}

.admin-profile {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 80px;
  vertical-align: middle;
}

.side-menu-li {
  width: 200px;
  white-space: normal;
  vertical-align: middle;
  word-wrap: break-word !important;
}
.space-normal {
  white-space: normal;
}
.custom-full-calendarsetting {
  .primary-full-calendar {
    .fc-view-harness {
      .fc-timegrid {
        table {
          tbody {
            tr {
              &:first-child {
                td {
                  .fc-scroller-harness {
                    max-height: 90px !important;
                    overflow: auto !important;
                    .fc-scroller {
                      .fc-daygrid-body {
                        table {
                          tbody {
                            tr {
                              td {
                                .fc-timegrid-axis-frame {
                                  span {
                                    font-size: 14px;
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
.page-dashboard-header {
  top: 0px;
  height: 48px;
  background: #041e39 0% 0% no-repeat padding-box;
  opacity: 1;
  .dashboard-content {
    display: flex;
    justify-content: left;
    padding: 3px;
  }
}
.new-update-modal {
  top: 100px;
  .modal-dialog {
    max-width: 460px;
  }
}
.terms-conditions-modal {
  top: 38px;
  max-width: 990px;
  margin: auto;
  .terms-modal-content {
    height: 55vh;
    overflow-y: auto;
    padding-right: 10px;
  }
}

.report-heatmap-details {
  .tag-layout-report {
    border-bottom: 1px solid #ddd;
    height: 30px;
  }
  .custom-tag-input input {
    font-size: 12px !important;
    height: 14px;
  }
  .custom-tag-input .tag-wrapper .tag__text {
    line-height: 23px;
  }

  .custom-tag-input .tag-wrapper delete-icon {
    margin-top: -5px;
  }
  .custom-material-form.add-concrete-material-form .form-control {
    border-bottom: 1px solid $light-grey8;
    border-top: 0px;
    border-left: 0px;
    border-right: 0px;
    border-radius: 0px;
    font-size: 12px;
    position: relative;
  }
  .range-image {
    position: absolute;
    right: 5px;
    bottom: 5px;
    z-index: 999;
  }
  .modal-dialog {
    max-width: 65%;
  }
}
.report-page-content {
  .show-time-column {
    overflow: auto;
    height: calc(100vh - 300px);
  }
  .timelistitem {
    background-color: $rgray;
    border-radius: 0px;
  }
  .time-picker-col {
    position: absolute;
    left: 50;
    left: 50%;
    bottom: -15px;
    .bs-timepicker-field {
      font-size: 12px;
    }
  }
}
.heatmap-schedule-form {
  .btn-link {
    color: $orange;
  }
  .custom-tag-input .tag-wrapper .tag__text {
    line-height: 20px;
  }
  .ng2-tag-input__text-input {
    font-size: 10px !important;
  }
  .ng2-tag-input.ng2-tag-input--focused {
    border-color: $light-grey8 !important;
  }
  .form-control,
  .btn-default {
    font-size: 12px;
  }
  .time-picker-col {
    position: absolute;
    bottom: -40px;
    left: 50%;
    margin-top: 20px;
  }
  .custom-tag-input .tag-wrapper delete-icon {
    margin-top: -7px;
  }
}

.ng2-dropdown-container {
  width: 750px !important;
}
.modal-save {
  .modal-content {
    border-radius: 20px;
  }
}
.event-selection-popup {
  .list-group-item {
    &:hover {
      background-color: rgba(117, 117, 117, 0.2);
      color: #757575;
      border-radius: 0px;
    }
  }
}

.custom-modal.filter-popup.modal-dialog.report-filter.report-filter-modal.modal-content {
  padding: 0px !important;
}

.modal {
  background: rgba(0, 0, 0, 0.3);
}

/*Date Range Modal */
.date-range-modal {
  &.modal-dialog {
    max-width: 800px;
    .modal-content {
      padding: 0px;
    }
    .modal-header {
      padding: 15px;
      .modal-title {
        font-size: 16px;
        color: $black;
        font-weight: bold;
      }
    }
    .modal-body {
      padding: 0px 0px 15px;
      .bs-datepicker {
        box-shadow: none;
        background-color: transparent;
        &.theme-green {
          .bs-datepicker-head {
            background-color: transparent;
            padding-left: 25px;
            button {
              color: $black;
              &:hover,
              &:focus {
                background-color: transparent;
              }
            }
            .current {
              float: left;
              padding: 0px 5px;
              font-weight: bold;
              color: $black;
              font-size: 14px;
            }
            .previous {
              position: absolute;
              right: 55px;
            }
          }
        }
        .bs-datepicker-body {
          border-radius: 0;
          min-width: 175px;
          border: 0;
          th {
            font-weight: bold;
          }
          td,
          th {
            font-size: 12px;
            color: $blue12;
          }
        }
      }
    }
    .date-range-footer {
      width: calc(100% - 300px);
      margin-left: auto;
      margin-right: 50px;
      .date-range-info {
        font-size: 12px;
        color: $blue12;
        font-weight: bold;
      }
    }
  }
}

.custom-vertical-tab {
  display: flex;
  .nav-pills {
    width: 200px;
    border-right: 2px solid $grey9;
    padding: 0px;
    .nav-link {
      font-size: 12px;
      font-weight: bold;
      color: $black;
      margin-bottom: 10px;
      &.active {
        background-color: $orange4;
        border-radius: 0;
        border-right: 2px solid $orange;
      }
    }
  }
  .tab-content {
    width: calc(100% - 200px);
  }
}
.select-concrete-input {
  width: 100px;
}
.reset-success-card-container {
  background: $white;
  border-radius: 30px;
  width: 32%;
  margin: auto;
}
.tag-input-dropdown {
  font-size: 10px;
}
.project-settings-tabfile {
  input[type='file']::file-selector-button {
    margin-right: 20px;

    border: none;

    background-color: $red8;

    transition: all 1s;

    opacity: 1;

    color: $orange;

    padding: 3px 10px;

    border-radius: 30px;

    font-size: 12px;

    cursor: pointer;
  }

  input[type='file']::file-selector-button:hover {
    background: $orange;

    color: $white;
  }
}
.addLocationModal {
  max-height: calc(100vh - 300px);
  overflow-x: hidden;
  overflow-y: auto;
}
.desktop-thumbnail-heading {
  width: calc(100vw - 280px);
  border-radius: 10px;
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
}

.notify-pagination {
  .form-select {
    height: calc(1.5em + 0.5rem + 0px);
    padding: 0rem 2rem 0rem 0.6rem;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 0.2rem;
    --bs-form-select-bg-img: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23686868' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 4 5 5 5-5'/%3e%3c/svg%3e");
  }
}

.table > :not(caption) > * > * {
  background-color: transparent;
  padding: 0.75rem;
}
.highcharts-axis-labels {
  color: $gray-g;
  cursor: default;
  font-size: 13px;
  fill: $gray-g;
}
.highcharts-axis.highcharts-xaxis .highcharts-axis-line {
  stroke: $grey18;
}

