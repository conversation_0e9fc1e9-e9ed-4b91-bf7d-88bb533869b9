.menu-button-bars {
  width: 29px;
  margin-top: 3px;
  cursor: pointer;

  .bar-line {
    transition: 0.5s;
    background: $orange;
    height: 3px;
    border-radius: 30px;
    display: block;

    @extend .mb7;

  }


  &:focus {
    outline: 0 !important;
  }
}

.menu-button-bars-home {
  width: 29px;
  margin-top: 3px;
  cursor: pointer;

  .bar-line {
    transition: 0.5s;
    background: $white;
    height: 3px;
    border-radius: 30px;
    display: block;

    @extend .mb7;

    &:first-child {
      width: 100%;
    }

    &:nth-child(2) {
      width: 50%;
    }

    &:nth-child(3) {
      width: 75%;

      @extend .mb0;
    }
  }
}

.navbar {
  background-color: $white;
  padding: 4px 14px;

  .navbar-brand {
    width: 256px;
    overflow: hidden;
    margin-right: 0;
    transition: 1s;
  }

  .main-menu {
    .nav-link {
      color: $black5;

      @extend .fw500;

      &.active-menu {
        color: $black6;

        @extend .fw600;
      }
    }
  }

  button:focus {
    outline: 0 !important;
  }
}

.navbar-toggler {
  line-height: 0;
}

.navbar-light .navbar-toggler {
  border-color: transparent !important;
}
