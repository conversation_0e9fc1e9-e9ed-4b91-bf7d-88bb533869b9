import {
  Component, ViewChild, ElementRef, ViewEncapsulation, Input, EventEmitter, Output,
} from '@angular/core';
import { CalendarOptions } from '@fullcalendar/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { FullCalendarComponent } from '@fullcalendar/angular';
import { DeliveryService } from 'src/app/services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';
import { CalendarService } from '../../services/profile/calendar.service';
import { ProjectSettingsService } from '../../services/project_settings/project-settings.service';


@Component({
  selector: 'app-time-slot',
  templateUrl: './time-slot.component.html',
  encapsulation: ViewEncapsulation.None,
})
export class TimeslotComponent {
  @Output() closeModal = new EventEmitter<void>();

  @Output() selectTime = new EventEmitter<{ start: string; end: string }>();

  @Input() selectedBookingDate: any;

  @Input() timeZone: any;

  @Input() equipmentId: any;

  @Input() LocationId: any;

  @Input() gateId: any;

  showCalendar = false;

  timeslotForm: FormGroup;

  selectedDate = new Date();

  currentDate: any;

  selectedEvents: any[] = [];

  @Input() isEditMode: boolean;

  @Input() selectedId: any;

  @Input() selectedBookingType: any;

  timeSlotList: any[] = [
    '00:00', '00:15', '00:30', '00:45',
    '01:00', '01:15', '01:30', '01:45',
    '02:00', '02:15', '02:30', '02:45',
    '03:00', '03:15', '03:30', '03:45',
    '04:00', '04:15', '04:30', '04:45',
    '05:00', '05:15', '05:30', '05:45',
    '06:00', '06:15', '06:30', '06:45',
    '07:00', '07:15', '07:30', '07:45',
    '08:00', '08:15', '08:30', '08:45',
    '09:00', '09:15', '09:30', '09:45',
    '10:00', '10:15', '10:30', '10:45',
    '11:00', '11:15', '11:30', '11:45',
    '12:00', '12:15', '12:30', '12:45',
    '13:00', '13:15', '13:30', '13:45',
    '14:00', '14:15', '14:30', '14:45',
    '15:00', '15:15', '15:30', '15:45',
    '16:00', '16:15', '16:30', '16:45',
    '17:00', '17:15', '17:30', '17:45',
    '18:00', '18:15', '18:30', '18:45',
    '19:00', '19:15', '19:30', '19:45',
    '20:00', '20:15', '20:30', '20:45',
    '21:00', '21:15', '21:30', '21:45',
    '22:00', '22:15', '22:30', '22:45',
    '23:00', '23:15', '23:30', '23:45',
  ];

  @ViewChild('datePicker') datePicker!: ElementRef;

  @ViewChild('fullCalendar') calendarComponent!: FullCalendarComponent;


  calendarOptions: CalendarOptions = {
    initialView: 'timeGridDay',
    selectable: true,
    selectOverlap: false,
    eventOverlap: false,
    editable: true, // Enables dragging and resizing
    eventResizableFromStart: true, // Allows resizing from both start and end
    droppable: true, // Allows dropping events in new time slots
    select: this.handleTimeSelection.bind(this),
    eventResize: this.handleEventResize.bind(this),
    // eventDrop: this.handleEventDrop.bind(this), // Handle dragging
    dateClick: this.handleDateClick.bind(this),
    plugins: [timeGridPlugin, interactionPlugin],
    nowIndicator: true,
    allDaySlot: false,
    slotDuration: '00:15:00',
    slotLabelInterval: '01:00',
    aspectRatio: 2,
    slotEventOverlap: false,
    fixedWeekCount: false,
    expandRows: true,
    headerToolbar: false,
    moreLinkClick: 'popover',
    eventColor: 'grey',
    events: this.selectedEvents,
    eventContent: this.renderCustomEventContent.bind(this),
    slotLabelFormat: {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    },
  };


  loader: boolean;

  deliveryList: any[];

  ProjectId: any;

  ParentCompanyId: any;

  calendarApi: any;

  approved: any;

  pending: any;

  expired: any;

  rejected: any;

  delivered: any;

  events: any[];

  startHour: any;

  startMinute: any;

  endHour: any;

  endMinute: any;

  GateId: any;

  projectSettingDetails: any;

  constructor(private readonly fb: FormBuilder, public projectService: ProjectService, public calendarService: CalendarService, public deliveryService: DeliveryService, public projectSettingsService: ProjectSettingsService) {
    this.timeslotForm = this.fb.group({
      fromTime: ['12:15'],
      toTime: ['12:15'],
      selectedDate: [new Date()],
    });
    this.events = [];
    this.selectedEvents = [];
    this.projectService.projectParent.subscribe((response7): void => {
      if (response7 !== undefined && response7 !== null && response7 !== '') {
        this.loader = true;
        this.ProjectId = response7.ProjectId;
        this.ParentCompanyId = response7.ParentCompanyId;
        this.loader = true;
      }
    });
  }

  ngOnInit() {
    console.log('OnInit - Selected Booking Date:', this.selectedBookingDate);
    if (this.selectedBookingDate) {
      const formattedDate = this.convertToISODate(this.selectedBookingDate);
      this.calendarOptions = { ...this.calendarOptions, initialDate: formattedDate };
      this.timeslotForm.patchValue({ selectedDate: formattedDate });
    }
    if (this.ProjectId) {
      const params = {
        ProjectId: this.ProjectId,
      };
      this.projectSettingsService.getProjectSettings(params).subscribe((res): void => {
        this.startHour = res?.data.projectSettings.workingWindowStartHours ?? 0;
        this.startMinute = res?.data.projectSettings.workingWindowStartMinutes ?? 0;
        this.endHour = res?.data.projectSettings.workingWindowEndHours ?? 23;
        this.endMinute = res?.data.projectSettings.workingWindowEndMinutes ?? 59;

        this.projectSettingDetails = res?.data.projectSettings;

        this.calendarOptions = {
          ...this.calendarOptions,
          slotMinTime: this.formatHourAndMinute(this.startHour, this.startMinute),
          slotMaxTime: this.formatHourAndMinute(this.endHour, this.endMinute),
        };
      });
    }


  }

  ngAfterViewInit(){
    if(this.isEditMode && this.selectedId){
      if (this.gateId && this.LocationId?.length && this.equipmentId?.length) {
        const locationId = this.LocationId[0]?.id;

        this.getEventNDR(this.equipmentId, locationId, this.gateId, this.timeZone, this.selectedBookingDate);
      }
    }
  }


  formatHourAndMinute(hour: number, minute: number): string {
    const h = hour.toString().padStart(2, '0');
    const m = minute.toString().padStart(2, '0');
    return `${h}:${m}:00`;
  }

  renderCustomEventContent(arg: any) {
    const startTime = this.formatTime(arg.event.start);
    const endTime = this.formatTime(arg.event.end);
    const timeRange = `${startTime} - ${endTime}`;

    const { isNew } = arg.event.extendedProps;
    const { isEdit } = arg.event.extendedProps;


    const container = document.createElement('div');
    container.style.fontSize = '14px';
    container.style.padding = '3px';

    const title = document.createElement('div');

    const time = document.createElement('div');
    time.innerText = timeRange;

    if (isNew && !isEdit) {
      title.style.fontWeight = 'bold';
      title.style.color = '#000';
      time.style.color = '#000';
      title.innerText = 'New Booking';
    } else if (!isNew && !isEdit) {
      title.style.fontWeight = 'bold';
      title.style.color = '#888';
      time.style.color = '#888';
      title.innerText = 'Booked Slot';
    } else if (isEdit) {
      title.innerText = 'Current Slot';
    }

    container.appendChild(title);
    container.appendChild(time);

    return { domNodes: [container] };
  }



  public formatWorkingTime(hour: number, minute: number): string {
    const h = hour % 12 || 12; // convert to 12-hour format
    const m = minute.toString().padStart(2, '0');
    const suffix = hour >= 12 ? 'PM' : 'AM';
    return `${h}:${m} ${suffix}`;
  }

  convertToISODate(dateString: any): string {
    try {
      if (!dateString || typeof dateString !== 'string') {
        throw new Error('Invalid input: dateString must be a string');
      }

      if (dateString.includes('T')) {
        return dateString;
      }

      const parsedDate = new Date(dateString);
      if (Number.isNaN(parsedDate.getTime())) {
        throw new Error('Invalid date format');
      }

      // Set time to midnight UTC
      parsedDate.setUTCHours(0, 0, 0, 0);
      return parsedDate.toISOString();

    } catch (error) {
      console.error('Date conversion error:', error.message, 'Input:', dateString);
      return '';
    }
  }



  handleTimeSelection(info: any) {
    const isOverlapping = this.checkOverlap(info);
    const newColor = isOverlapping ? 'rgba(255, 0, 0, 0.5)' : '#a1f74c';

    const updatedEvent : any = {
      id: this.selectedId || `new-${Date.now()}`,
      start: info.startStr,
      end: info.endStr,
      color: newColor,
      backgroundColor: newColor,
      borderColor: newColor,
      display: 'block',
      requestType: info.event?.extendedProps?.requestType || null,
      isEdit: true,
    };

    this.timeslotForm.patchValue({
      fromTime: this.formatTime(info.start),
      toTime: this.formatTime(info.end)
    });

    if (this.isEditMode) {
      const eventIndex = this.events.findIndex((event) => event.id === this.selectedId);

      if (eventIndex !== -1) {
        this.events[eventIndex] = { ...this.events[eventIndex], ...updatedEvent };
      } else {
        // Add as a new event if the original one is not found (e.g., different date)
        this.events.push(updatedEvent);
      }
    } else {
      updatedEvent.isNew = true;
      this.selectedEvents = [updatedEvent];
    }

    this.updateCalendarEvents();
  }




  handleEventResize(info: any) {
    const resizedEvent = info.event;
    const resizedId = Number(resizedEvent._def.publicId);

    console.log('Current Selected ID:', this.selectedId, 'Resized Event ID:', resizedId);

    if (this.isEditMode) {
      // Block editing if it's not the selected one
      if (resizedId !== this.selectedId) {
        info.revert();
        return;
      }
    }

    const isOverlapping = this.checkOverlap(resizedEvent, this.selectedId);
    const newColor = isOverlapping ? 'rgba(255, 0, 0, 0.5)' : '#a1f74c';

    const updatedEvent = {
      id: this.selectedId || `new-${Date.now()}`,
      start: resizedEvent.startStr,
      end: resizedEvent.endStr,
      color: newColor,
      backgroundColor: newColor,
      borderColor: newColor,
      requestType: resizedEvent.extendedProps.requestType,
      display: 'block',
      isEdit: true
    };

    if (this.isEditMode) {
      const eventIndex = this.events.findIndex(event => event.id === this.selectedId);

      if (eventIndex !== -1) {
        this.events[eventIndex] = { ...this.events[eventIndex], ...updatedEvent };
      } else {
        // Fallback: push new event in case original is not found due to date change
        this.events.push(updatedEvent);
      }
    } else if (this.selectedEvents.length > 0) {
      this.selectedEvents[0] = { ...this.selectedEvents[0], ...updatedEvent, isNew: true };
    }

    this.timeslotForm.patchValue({
      fromTime: this.formatTime(resizedEvent.start),
      toTime: this.formatTime(resizedEvent.end)
    });

    this.calendarOptions.events = [...this.events, ...this.selectedEvents];
    this.updateCalendarEvents();
  }






  handleEventDrop(info: any) {
    const droppedEvent = info.event;
    const isOverlapping = this.checkOverlap(droppedEvent, this.selectedId);

    if (this.isEditMode) {
      if (this.selectedEvents.length > 0) {
        const currentEvent = this.selectedEvents[0];

        currentEvent.start = droppedEvent.startStr;
        currentEvent.end = droppedEvent.endStr;
        currentEvent.color = isOverlapping ? 'rgba(255, 0, 0, 0.5)' : 'rgba(161, 247, 76, 0.5)';

        this.updateCalendarEvents();
      }
    } else {
      this.selectedEvents[0].start = droppedEvent.startStr;
      this.selectedEvents[0].end = droppedEvent.endStr;
      this.selectedEvents[0].color = isOverlapping ? 'rgba(255, 0, 0, 0.5)' : 'rgba(161, 247, 76, 0.5)';

      this.updateCalendarEvents();
    }
  }

  checkOverlap(event: any, currentId?: any): boolean {
    if (!this.events || !Array.isArray(this.events)) {
      return false;
    }

    return this.events.some((existingEvent) => {
      const isSameEvent = this.isEditMode ? existingEvent.id === currentId : false;

      const eventStart = new Date(event.startStr);
      const eventEnd = new Date(event.endStr);
      const existingStart = new Date(existingEvent.start);
      const existingEnd = new Date(existingEvent.end);

      const isOverlapping = eventStart < existingEnd
        && eventEnd > existingStart
        && !isSameEvent
        && this.selectedBookingType === existingEvent.requestType;

      if (isOverlapping) {
        console.log('Conflict Detected with Event ID:', existingEvent.id);
        return true;
      }

      return false;
    });
  }



  public updateCalendarEvents() {
    this.calendarOptions = { ...this.calendarOptions, events: [...this.selectedEvents, ...this.events] };
    this.updateSlot();
  }


  updateResizedEvent(eventIndex: number, resizedEvent: any) {
    const isOverlapping = this.events.some((event) => event.start < resizedEvent.endStr
      && event.end > resizedEvent.startStr);

    this.selectedEvents[eventIndex] = {
      ...this.selectedEvents[eventIndex],
      start: resizedEvent.startStr,
      end: resizedEvent.endStr,
      color: isOverlapping ? '#f79a9a' : '#a1f74c',
    };

    this.timeslotForm.patchValue({
      fromTime: this.formatTime(resizedEvent.start),
      toTime: this.formatTime(resizedEvent.end),
    });

    this.updateCalendarEvents();
  }


  public setToday() {
    const today = new Date();
    this.timeslotForm.patchValue({ selectedDate: today });

    this.currentDate = today;
    this.calendarOptions = { ...this.calendarOptions, initialDate: today };
    this.calendarApi = this.calendarComponent.getApi();
    this.calendarApi.gotoDate(today); // Change calendar date
    // this.getEventNDR()
  }



  public formatTime(date: Date): string {
    const d = new Date(date);
    const hours = d.getHours().toString().padStart(2, '0');
    const minutes = d.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }


  public changeDay(direction: number) {
    const currentDate = new Date(this.timeslotForm.get('selectedDate')?.value);
    currentDate.setDate(currentDate.getDate() + direction);
    this.timeslotForm.patchValue({ selectedDate: currentDate });
    if (this.calendarComponent) {
      this.calendarApi = this.calendarComponent.getApi();
      this.calendarApi.gotoDate(currentDate); // Change calendar date
    }
    this.getEventNDR(this.equipmentId, this.LocationId, this.GateId, this.timeZone, currentDate);
  }

  handleDateClick(info: any) {
    this.selectedDate = new Date(info.dateStr);
    this.showCalendar = false;
  }


  onDateChange(event: any) {
    const selectedDate = event.target ? event.target.value : event.value;
    this.timeslotForm.get('selectedDate')?.setValue(selectedDate);
    this.currentDate = selectedDate;
    if (this.calendarComponent) {
      this.calendarApi = this.calendarComponent.getApi();
      this.calendarApi.gotoDate(this.currentDate); // Change calendar date
    }
    console.log('Selected Date:', selectedDate);
  }


  closePopup() {
    if (this.closeModal) {
      this.closeModal.emit(); // Call the function to close the modal
    }
  }

  public openDatePicker() {
    this.datePicker.nativeElement.showPicker();
  }


  public getEventNDR(equipmentId: any, locationId: any, gateId: any, timeZone: any, date: any): void {
    this.LocationId = locationId
    this.equipmentId = equipmentId
    this.gateId = gateId ? Number(gateId) : '';
    this.timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone
    if(date){
      this.timeslotForm.get('selectedDate').setValue(new Date(date))
    }
    const selectedDate = date ? new Date(date) : this.timeslotForm.get('selectedDate')?.value;
    const dateObj = new Date(selectedDate);
    const formattedDate = selectedDate
    ? `${dateObj.getFullYear()}-${String(dateObj.getMonth() + 1).padStart(2, '0')}-${String(dateObj.getDate()).padStart(2, '0')}`
    : '';
    const equipmentIds = Array.isArray(this.equipmentId) ? this.equipmentId.map((equipment: any) => equipment.id) : [];
    if (this.equipmentId || this.gateId) {
      const payload = {
        date: formattedDate,
        equipmentId: equipmentIds,
        timeZone: this.timeZone,
        LocationId: this.LocationId,
        GateId: this.gateId,
        bookingType: this.selectedBookingType
      }
      this.deliveryService.getAvailableTimeSlots(this.ProjectId, payload).subscribe((res): void => {
        this.setEventNDRData(res.data);
      })

    }

  }

  public canEventsOverlap(stillEvent: any, movingEvent: any): boolean {
    const settings = this.projectSettingDetails;

    const stillCal = !!stillEvent.extendedProps?.calendarEvent;
    const moveCal = !!movingEvent.extendedProps?.calendarEvent;

    const overlapKeyMap = {
      deliveryRequest: 'delivery',
      inspectionRequest: 'inspection',
      craneRequest: 'crane',
      concreteRequest: 'concrete',
    };

    const bookingTypeKey = overlapKeyMap[this.selectedBookingType];

    if (!bookingTypeKey) return false;

    if ((stillCal || moveCal) && settings[`${bookingTypeKey}AllowOverlappingCalenderEvents`] === false) {
      return false;
    }

    if ((!stillCal) && settings[`${bookingTypeKey}AllowOverlappingBooking`] === false) {
      return false;
    }

    const bookingTypes = {
      deliveryRequest: 'Delivery',
      inspectionRequest: 'Inspection',
      craneRequest: 'Crane',
      concreteRequest: 'Concrete',
    };

    if (this.equipmentId.length && this.projectSettingDetails[`equipmentExceptionsFor${bookingTypes[this.selectedBookingType]}`]) {
      const equipmentIds: number[] = this.equipmentId?.map((item: any) => item.id);
      const equipmentExceptions: number[] = JSON.parse(this.projectSettingDetails[`equipmentExceptionsFor${bookingTypes[this.selectedBookingType]}`]);

      const allEquipmentPresent = equipmentIds.every(id => equipmentExceptions.includes(id));

      if (allEquipmentPresent) {
        return false;
      }
    }

    if (this.gateId && this.projectSettingDetails[`gateExceptionsFor${bookingTypes[this.selectedBookingType]}`]) {
      const gateExceptions: number[] = JSON.parse(this.projectSettingDetails[`gateExceptionsFor${bookingTypes[this.selectedBookingType]}`]);
      const gateMatched = gateExceptions.includes(this.gateId);

      if (gateMatched) {
        return false;
      }
    }

    return true;
  }




  public canEventBeOverlapped(event: any): boolean {
    const settings = this.projectSettingDetails;
    const isCalEvent = !!event.extendedProps?.calendarEvent;

    const overlapKeyMap = {
      deliveryRequest: 'delivery',
      inspectionRequest: 'inspection',
      craneRequest: 'crane',
      concreteRequest: 'concrete',
    };

    const bookingTypeKey = overlapKeyMap[this.selectedBookingType];

    if (!bookingTypeKey) {
      console.warn('Invalid selectedBookingType:', this.selectedBookingType);
      return false;
    }

    if (isCalEvent && settings[`${bookingTypeKey}AllowOverlappingCalenderEvents`] === false) {
      return false;
    }

    if (!isCalEvent && settings[`${bookingTypeKey}AllowOverlappingBooking`] === false) {
      return false;
    }

    return true;
  }


  public setEventNDRData(data : any): void {
    try{
    this.calendarApi = this.calendarComponent.getApi();

    if (!this.calendarApi || !data || !Array.isArray(data)) return;

    this.calendarApi.removeAllEventSources();
    this.calendarApi.getEvents().forEach((event) => event.remove());

    let backgroundEvents = [];

    if (this.projectSettingDetails) {

      this.calendarOptions = {
        ...this.calendarOptions,
        eventOverlap: false,
        selectOverlap: false,
      };

      backgroundEvents = data
        .filter((slot) => slot.id !== this.selectedId)
        .map((slot) => ({
          title: slot.calendarEvent ? 'Calender Event' : 'Booked Slot',
          start: slot.start,
          end: slot.end,
          display: 'background',
          color: '#000',
          extendedProps: {
            calendarEvent: slot.calendarEvent ?? false,
          },
        }));
    }


    // Add the editable event (current one)
    const currentSlot = data.find((slot) => slot.id === this.selectedId);
    const editableEvent = currentSlot ? [{
      id: currentSlot.id,
      title: 'Your Booking',
      start: currentSlot.start,
      end: currentSlot.end,
      editable: true,
      color: '#90caf9', // optional: distinguish the current booking
    }] : [];

    this.events = [...backgroundEvents, ...editableEvent];
    this.calendarApi.addEventSource(this.events);
    } catch (error) {
      console.error('Error setting event NDR data:', error);
    }
  }


  canSelectionBeAllowed(selectInfo: any): boolean {
    const newStart = selectInfo.start;
    const newEnd = selectInfo.end;

    // Check overlap with existing events (excluding current editable event)
    return !this.events.some(event => {
      if (event.display === 'background') {
        const existingStart = new Date(event.start);
        const existingEnd = new Date(event.end);
        return newStart < existingEnd && newEnd > existingStart;
      }
      return false;
    });
  }




  updateSlot() {
    console.log('Updated Time:', this.timeslotForm.value);
    if (!this.isEditMode) {
      const { start, end } = this.selectedEvents[0];
      this.selectTime.emit({ start, end });
    } else {
      const { start, end } = this.events.find((event) => event.id === this.selectedId);
      this.selectTime.emit({ start, end });
    }
  }

  setTodayDate() {
    this.timeslotForm.get('selectedDate')?.setValue(new Date());
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'date':
          this.openDatePicker();
          break;
        case 'day':
          this.changeDay(data);
          break;
        default:
          break;
      }
    }
  }
}
