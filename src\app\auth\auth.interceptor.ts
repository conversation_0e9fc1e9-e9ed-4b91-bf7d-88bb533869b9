import { Injectable } from '@angular/core';
import {
  HttpEvent,
  HttpInterceptor,
  HttpHandler,
  HttpRequest,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { LogoutAlertComponent } from '../logout-alert/logout-alert.component';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  public modalRef: BsModalRef | null = null;

  private isModalShown = false;

  constructor(
    public bsModalRef: BsModalRef,
    private readonly modalService: BsModalService,
  ) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      catchError((err: HttpErrorResponse) => {
        if (err && err.status === 401 && !this.isModalShown) {
          const loaderElements = document.querySelectorAll('ngx-loading .backdrop.full-screen');
          loaderElements.forEach((el) => {
            (el as HTMLElement).style.display = 'none';
          });
          this.isModalShown = true; // set flag

          this.modalRef = this.modalService.show(LogoutAlertComponent, {
            backdrop: 'static',
            keyboard: false,
          });

          // Reset flag when modal is hidden or closed
          this.modalRef.onHidden?.subscribe(() => {
            this.isModalShown = false;
          });

          this.modalRef.onHide?.subscribe(() => {
            this.isModalShown = false;
          });
        }
        return throwError(() => err);
      }),
    );
  }
}
