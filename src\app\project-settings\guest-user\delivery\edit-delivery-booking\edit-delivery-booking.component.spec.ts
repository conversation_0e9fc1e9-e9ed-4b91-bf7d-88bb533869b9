import { ComponentFixture, TestBed, fakeAsync, tick, discardPeriodicTasks } from '@angular/core/testing';
import { EditDeliveryBookingComponent } from './edit-delivery-booking.component';
import { UntypedFormBuilder, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { BsModalService, BsModalRef, ModalOptions } from 'ngx-bootstrap/modal';
import { Router } from '@angular/router';
import { Socket } from 'ngx-socket-io';
import { ProjectSharingService } from '../../../../services/projectSharingService/project-sharing.service';
import { DeliveryService } from '../../../../services/profile/delivery.service';
import { ProjectService } from '../../../../services/profile/project.service';
import { MixpanelService } from '../../../../services/mixpanel.service';
import { ProjectSettingsService } from '../../../../services/project_settings/project-settings.service';
import { of, throwError, Subject } from 'rxjs';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { NO_ERRORS_SCHEMA, Component } from '@angular/core';
import { IDropdownSettings } from 'ng-multiselect-dropdown';

// Create a host component with no template to avoid template-related errors
@Component({
  selector: 'app-test-host',
  template: ''
})
class TestHostComponent extends EditDeliveryBookingComponent {
  constructor(
    formBuilder: UntypedFormBuilder,
    socket: Socket,
    toastr: ToastrService,
    router: Router,
    modalRef: BsModalRef,
    modalService: BsModalService,
    modalRef1: BsModalRef,
    mixpanelService: MixpanelService,
    deliveryService: DeliveryService,
    projectService: ProjectService,
    projectSettingsService: ProjectSettingsService,
    projectSharingService: ProjectSharingService,
    option: ModalOptions
  ) {
    super(
      formBuilder,
      socket,
      toastr,
      router,
      modalRef,
      modalService,
      modalRef1,
      mixpanelService,
      deliveryService,
      projectService,
      projectSettingsService,
      projectSharingService,
      option
    );
  }
}

describe('EditDeliveryBookingComponent', () => {
  let component: TestHostComponent;
  let fixture: ComponentFixture<TestHostComponent>;
  let mockToastr: jest.Mocked<ToastrService>;
  let mockModalService: jest.Mocked<BsModalService>;
  let mockRouter: jest.Mocked<Router>;
  let mockSocket: jest.Mocked<Socket>;
  let mockDeliveryService: jest.Mocked<DeliveryService>;
  let mockProjectService: jest.Mocked<ProjectService>;
  let mockMixpanelService: jest.Mocked<MixpanelService>;
  let mockProjectSettingsService: jest.Mocked<ProjectSettingsService>;
  let mockProjectSharingService: any; // Changed to any to avoid TypeScript errors

  beforeEach(async () => {
    mockToastr = {
      success: jest.fn(),
      error: jest.fn()
    } as any;

    mockModalService = {
      show: jest.fn()
    } as any;

    const routerEventsSubject = new Subject();
    mockRouter = {
      navigate: jest.fn(),
      events: routerEventsSubject.asObservable()
    } as any;

    mockSocket = {
      emit: jest.fn(),
      on: jest.fn()
    } as any;

    mockDeliveryService = {
      submitQueuedNDR: jest.fn().mockReturnValue(of({}))
    } as any;

    mockProjectService = {} as any;

    mockMixpanelService = {
      track: jest.fn(),
      addGuestUserMixpanelEvents: jest.fn()
    } as any;

    mockProjectSettingsService = {
      getGuestProjectSettings: jest.fn().mockReturnValue(of({
        data: {
          deliveryWindowTime: 30,
          deliveryWindowTimeUnit: 'minutes'
        }
      }))
    } as any;

    mockProjectSharingService = {
      guestGateList: jest.fn().mockReturnValue(of({ data: [{ id: 1, gateName: 'Gate 1' }] })),
      guestListEquipment: jest.fn().mockReturnValue(of({ data: [{ id: 1, equipmentName: 'Equipment 1', PresetEquipmentType: { isCraneType: false } }] })),
      guestGetCompanies: jest.fn().mockReturnValue(of({ data: [{ id: 1, companyName: 'Company 1' }] })),
      guestGetDefinableWork: jest.fn().mockReturnValue(of({ data: [{ id: 1, DFOW: 'DFOW 1' }] })),
      guestGetLocations: jest.fn().mockReturnValue(of({ data: [{ id: 1, locationPath: 'Location 1' }] })),
      guestEditNDR: jest.fn().mockReturnValue(of({ message: 'Success' })),
      listAllMember: jest.fn().mockReturnValue(of({ data: [{ id: 1, User: { email: '<EMAIL>' } }] })),
      guestSearchNewMember: jest.fn().mockReturnValue(of([])),
      guestGetNDRData: jest.fn().mockReturnValue(of({
        data: {
          id: 123,
          DeliveryId: 'DEL123',
          CraneRequestId: 'CR123',
          description: 'Test Delivery',
          notes: 'Test Notes',
          vehicleDetails: 'Test Vehicle',
          deliveryStart: new Date(),
          deliveryEnd: new Date(),
          escort: false,
          gateDetails: [{ Gate: { id: 1, gateName: 'Gate 1' } }],
          companyDetails: [{ Company: { id: 1, companyName: 'Company 1' } }],
          memberDetails: [{ Member: { id: 1, User: { email: '<EMAIL>', firstName: 'Test', lastName: 'User' } } }],
          equipmentDetails: [{ Equipment: { id: 1, equipmentName: 'Equipment 1' } }],
          defineWorkDetails: [{ DeliverDefineWork: { id: 1, DFOW: 'DFOW 1' } }],
          location: { id: 1, locationPath: 'Location 1' },
          isAssociatedWithCraneRequest: false,
          status: 'Pending',
          recurrence: { id: 1, recurrence: 'Daily', recurrenceEndDate: new Date() }
        }
      })),
      guestGetMemberRole: jest.fn().mockReturnValue(of({
        data: {
          id: 1,
          User: { email: '<EMAIL>' },
          RoleId: 1
        }
      })),
      guestGetLastCraneRequestId: jest.fn().mockReturnValue(of({ lastId: { CraneRequestId: 'CR123' } }))
    };

    await TestBed.configureTestingModule({
      declarations: [TestHostComponent],
      imports: [
        ReactiveFormsModule,
        FormsModule,
        BsDatepickerModule.forRoot()
      ],
      providers: [
        UntypedFormBuilder,
        { provide: ToastrService, useValue: mockToastr },
        { provide: BsModalService, useValue: mockModalService },
        { provide: Router, useValue: mockRouter },
        { provide: Socket, useValue: mockSocket },
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: ProjectService, useValue: mockProjectService },
        { provide: MixpanelService, useValue: mockMixpanelService },
        { provide: ProjectSettingsService, useValue: mockProjectSettingsService },
        { provide: ProjectSharingService, useValue: mockProjectSharingService },
        { provide: BsModalRef, useValue: { hide: jest.fn() } },
        { provide: ModalOptions, useValue: {} }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    // Mock localStorage
    jest.spyOn(Storage.prototype, 'getItem').mockImplementation((key: string) => {
      if (key === 'guestProjectId') return btoa('1');
      if (key === 'guestParentCompanyId') return btoa('2');
      if (key === 'guestId') return btoa('3');
      return null;
    });

    fixture = TestBed.createComponent(TestHostComponent);
    component = fixture.componentInstance;

    // Set up component properties manually
    component.data = {
      DeliveryRequestId: '123',
      seriesOption: 1
    };
    component.title = 'Edit Delivery';
    component.ProjectId = 1;
    component.ParentCompanyId = 2;
    component.guestUserId = 3;

    // Initialize dropdown settings before change detection
    component.editNdrCompanyDropdownSettings = {
      singleSelection: false,
      idField: 'id',
      textField: 'companyName',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 6,
      allowSearchFilter: true
    };

    // Override problematic methods
    component.ngOnInit = jest.fn();
    component.getMembers = jest.fn();
    component.getProjectSettings = jest.fn();

    // Create a real form using the actual FormBuilder
    component.editDetailsForm();

    component.gateList = [];
    component.equipmentList = [];
    component.defineList = [];
    component.locationList = [];
    component.companyList = [];
    component.memberList = [];
    component.deliveryWindowTime = 30;
    component.deliveryWindowTimeUnit = 'minutes';
    component.currentEditItem = {};
    component.authUser = {
      User: { email: '<EMAIL>' },
      id: 1,
      RoleId: 1
    };

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    expect(component.deliverEditForm).toBeDefined();
    expect(component.deliverEditForm.get('description')).toBeDefined();
    expect(component.deliverEditForm.get('GateId')).toBeDefined();
  });

  it('should load NDR data successfully', fakeAsync(() => {
    const mockNDRData = {
      data: {
        id: 123,
        DeliveryId: 'DEL123',
        CraneRequestId: 'CR123',
        description: 'Test Delivery',
        notes: 'Test Notes',
        GateId: 1,
        deliveryStart: new Date(),
        deliveryEnd: new Date(),
        escort: false,
        gateDetails: [{ Gate: { id: 1, name: 'Gate 1' } }],
        companyDetails: [{ Company: { id: 1, companyName: 'Test Company' } }],
        personDetails: [{ User: { id: 1, email: '<EMAIL>' } }],
        equipmentDetails: [{ Equipment: { id: 1, equipmentName: 'Test Equipment' } }],
        defineDetails: [{ DFOW: { id: 1, name: 'Test Define' } }],
        isAssociatedWithCraneRequest: false,
        status: 'Pending'
      }
    };
    mockProjectSharingService.guestGetNDRData.mockReturnValue(of(mockNDRData));

    // Mock the methods that are called in getNDR
    component.setCompany = jest.fn();
    component.setMember = jest.fn();
    component.setDefine = jest.fn(); // Changed from setDFOW to setDefine
    component.setEquipment = jest.fn();

    component.getNDR();
    tick();

    expect(mockProjectSharingService.guestGetNDRData).toHaveBeenCalled();
    expect(component.currentEditItem).toEqual(mockNDRData.data);
  }));

  it('should validate form submission', () => {
    const formValue = {
      description: 'Test Delivery',
      deliveryDate: new Date(),
      deliveryStart: new Date(),
      deliveryEnd: new Date(new Date().getTime() + 3600000), // 1 hour later
      escort: false,
      vehicleDetails: 'Test Vehicle',
      notes: 'Test Notes',
      companyItems: [{ id: 1 }],
      person: [{ id: 1 }],
      EquipmentId: [{ id: 1 }],
      GateId: 1,
      LocationId: 1
    };

    component.deliverEditForm.patchValue(formValue);
    component.onEditSubmit('save');

    expect(component.saveQueuedNDR).toBe(true);
    expect(component.editSubmitted).toBe(true);
  });

  it('should check future date validation', () => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 1);

    const result = component.checkEditDeliveryFutureDate(
      futureDate,
      new Date(futureDate.getTime() + 3600000),
      'submit'
    );

    expect(result).toBe(true);
  });

  it('should handle form reset', () => {
    // Setup the form with values
    component.deliverEditForm.patchValue({
      description: 'Test',
      deliveryDate: new Date()
    });

    // Mock the modalRef1.hide method
    component.modalRef1 = { hide: jest.fn() } as any;

    // Override the resetForm method to avoid actual form reset
    const originalResetForm = component.resetForm;
    component.resetForm = function(action) {
      if (action === 'cancel') {
        this.modalRef1.hide();
        this.deliverEditForm.reset();
        this.submitted = false;
      }
    };

    component.resetForm('cancel');

    expect(component.deliverEditForm.get('description').value).toBeFalsy();
    expect(component.submitted).toBe(false);

    // Restore original method
    component.resetForm = originalResetForm;
  });

  it('should validate start and end time', () => {
    const startDate = new Date();
    const endDate = new Date(startDate.getTime() + 3600000); // 1 hour later

    const result = component.checkStartEnd(startDate, endDate);

    expect(result).toBe(true);
  });

  it('should handle invalid start and end time', () => {
    const startDate = new Date();
    const endDate = new Date(startDate.getTime() - 3600000); // 1 hour earlier

    const result = component.checkStartEnd(startDate, endDate);

    expect(result).toBe(false);
  });

  it('should set default person', () => {
    component.authUser = {
      User: { email: '<EMAIL>' },
      id: 1,
      RoleId: 1
    };

    component.setDefaultPerson();

    const personValue = component.deliverEditForm.get('person').value;
    expect(personValue[0].email).toBe('<EMAIL>');
    expect(personValue[0].id).toBe(1);
    expect(personValue[0].readonly).toBe(true);
  });

  it('should handle number only input validation', () => {
    const validEvent = { which: 48, keyCode: 48 }; // '0' key
    const invalidEvent = { which: 65, keyCode: 65 }; // 'A' key

    expect(component.numberOnly(validEvent)).toBe(true);
    expect(component.numberOnly(invalidEvent)).toBe(false);
  });

  describe('Component Initialization and Setup', () => {
    it('should initialize with correct default values', () => {
      expect(component.ProjectId).toBe(1);
      expect(component.ParentCompanyId).toBe(2);
      expect(component.guestUserId).toBe(3);
      expect(component.submitted).toBe(false);
      expect(component.escort).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(component.modalLoader).toBe(false);
      expect(component.loader).toBe(false);
      expect(component.NDRTimingChanged).toBe(false);
      expect(component.isQueuedNDR).toBe(false);
      expect(component.saveQueuedNDR).toBe(false);
      expect(component.formEdited).toBe(true);
      expect(component.craneEquipmentTypeChosen).toBe(false);
    });

    it('should initialize arrays and objects', () => {
      expect(Array.isArray(component.gateList)).toBe(true);
      expect(Array.isArray(component.equipmentList)).toBe(true);
      expect(Array.isArray(component.defineList)).toBe(true);
      expect(Array.isArray(component.locationList)).toBe(true);
      expect(Array.isArray(component.companyList)).toBe(true);
      expect(Array.isArray(component.memberList)).toBe(true);
      expect(Array.isArray(component.editBeforeDFOW)).toBe(true);
      expect(Array.isArray(component.editBeforeCompany)).toBe(true);
      expect(Array.isArray(component.editBeforeEquipment)).toBe(true);
      expect(Array.isArray(component.valueExists)).toBe(true);
      expect(component.authUser).toBeDefined();
    });

    it('should initialize form correctly', () => {
      expect(component.deliverEditForm).toBeDefined();
      expect(component.deliverEditForm.get('id')).toBeDefined();
      expect(component.deliverEditForm.get('description')).toBeDefined();
      expect(component.deliverEditForm.get('GateId')).toBeDefined();
      expect(component.deliverEditForm.get('deliveryDate')).toBeDefined();
      expect(component.deliverEditForm.get('deliveryStart')).toBeDefined();
      expect(component.deliverEditForm.get('deliveryEnd')).toBeDefined();
      expect(component.deliverEditForm.get('escort').value).toBe(false);
    });
  });

  describe('Data Loading Methods', () => {
    it('should load gate list successfully', fakeAsync(() => {
      component.getOverAllGate();
      tick();

      expect(mockProjectSharingService.guestGateList).toHaveBeenCalledWith(
        expect.objectContaining({
          ProjectId: 1,
          ParentCompanyId: 2,
          pageSize: 0,
          pageNo: 0
        }),
        { isFilter: true, showActivatedAlone: true }
      );
      expect(component.gateList).toEqual([{ id: 1, gateName: 'Gate 1' }]);
    }));

    it('should load equipment list successfully', fakeAsync(() => {
      component.getOverAllEquipmentforEditNdr();
      tick();

      expect(mockProjectSharingService.guestListEquipment).toHaveBeenCalledWith(
        expect.objectContaining({
          ProjectId: 1,
          ParentCompanyId: 2,
          pageSize: 0,
          pageNo: 0
        }),
        { isFilter: true, showActivatedAlone: true }
      );
      expect(component.equipmentList).toEqual([{ id: 1, equipmentName: 'Equipment 1', PresetEquipmentType: { isCraneType: false } }]);
    }));

    it('should load companies successfully', fakeAsync(() => {
      component.getCompaniesForEditNdr();
      tick();

      expect(mockProjectSharingService.guestGetCompanies).toHaveBeenCalledWith(
        expect.objectContaining({
          ProjectId: 1,
          ParentCompanyId: 2
        })
      );
      expect(component.companyList).toEqual([{ id: 1, companyName: 'Company 1' }]);
    }));

    it('should load definable work successfully', fakeAsync(() => {
      component.getDefinableForEditNdr();
      tick();

      expect(mockProjectSharingService.guestGetDefinableWork).toHaveBeenCalledWith(
        expect.objectContaining({
          ProjectId: 1,
          ParentCompanyId: 2
        })
      );
      expect(component.defineList).toEqual([{ id: 1, DFOW: 'DFOW 1' }]);
    }));

    it('should load locations successfully', fakeAsync(() => {
      component.getLocationForEditNdr();
      tick();

      expect(mockProjectSharingService.guestGetLocations).toHaveBeenCalledWith(
        expect.objectContaining({
          ProjectId: 1,
          ParentCompanyId: 2
        })
      );
      expect(component.locationList).toEqual([{ id: 1, locationPath: 'Location 1' }]);
    }));

    it('should load members successfully', fakeAsync(() => {
      // Reset the mock to ensure clean state
      mockProjectSharingService.listAllMember.mockReturnValue(of({
        data: [{ id: 1, User: { email: '<EMAIL>' } }]
      }));

      component.getMembers();
      tick();

      expect(mockProjectSharingService.listAllMember).toHaveBeenCalledWith(
        expect.objectContaining({
          ProjectId: 1,
          ParentCompanyId: 2
        })
      );
      expect(component.memberList).toEqual([{ id: 1, User: { email: '<EMAIL>' } }]);
    }));
  });

  describe('Form Validation and Submission', () => {
    beforeEach(() => {
      component.gateList = [{ id: 1, gateName: 'Gate 1' }];
      component.memberList = [{ id: 1, User: { email: '<EMAIL>' } }];
      component.equipmentList = [{ id: 1, equipmentName: 'Equipment 1', PresetEquipmentType: { isCraneType: false } }];
    });

    it('should validate form submission with valid data', () => {
      const formValue = {
        id: 123,
        description: 'Test Delivery',
        deliveryDate: new Date(),
        deliveryStart: new Date(),
        deliveryEnd: new Date(new Date().getTime() + 3600000),
        escort: false,
        vehicleDetails: 'Test Vehicle',
        notes: 'Test Notes',
        companyItems: [{ id: 1, companyName: 'Company 1' }],
        person: [{ id: 1, email: '<EMAIL>' }],
        EquipmentId: [{ id: 1, equipmentName: 'Equipment 1' }],
        GateId: 1,
        LocationId: 1,
        isAssociatedWithCraneRequest: false
      };

      component.deliverEditForm.patchValue(formValue);
      component.deliveryWindowTime = 30;
      component.deliveryWindowTimeUnit = 'minutes';
      component.currentEditItem = { status: 'Pending' };
      component.authUser = { RoleId: 1 };

      // Mock the methods to avoid complex validation chains
      jest.spyOn(component, 'checkEditDeliveryFutureDate').mockReturnValue(false);
      jest.spyOn(component, 'checkRequestStatusAndUserRole').mockImplementation(() => {});

      component.onEditSubmit('submitCurrentNDR');

      expect(component.editSubmitted).toBe(true);
      expect(component.formEditSubmitted).toBe(true);
    });

    it('should handle invalid form submission', () => {
      component.deliverEditForm.patchValue({
        description: '', // Invalid - required field
        EquipmentId: []
      });

      component.onEditSubmit('submitCurrentNDR');

      expect(component.formEditSubmitted).toBe(false);
    });

    it('should handle empty equipment validation', () => {
      component.deliverEditForm.patchValue({
        description: 'Test',
        EquipmentId: []
      });

      component.onEditSubmit('submitCurrentNDR');

      expect(mockToastr.error).toHaveBeenCalledWith('Equipment is required');
      expect(component.formEditSubmitted).toBe(false);
    });

    it('should handle save action', () => {
      const formValue = {
        description: 'Test Delivery',
        EquipmentId: [{ id: 1 }],
        companyItems: [{ id: 1 }],
        person: [{ id: 1 }],
        GateId: 1
      };

      component.deliverEditForm.patchValue(formValue);
      component.onEditSubmit('save');

      expect(component.saveQueuedNDR).toBe(true);
      expect(component.editSubmitted).toBe(true);
    });
  });

  describe('Equipment and Crane Handling', () => {
    it('should handle crane equipment type selection', () => {
      const craneEquipment = [{ id: 1, equipmentName: 'Crane 1' }];
      component.equipmentList = [{ id: 1, equipmentName: 'Crane 1', PresetEquipmentType: { isCraneType: true } }];

      jest.spyOn(component, 'getLastCraneRequestId');

      component.onEditSubmitForm(craneEquipment);

      expect(component.craneEquipmentTypeChosen).toBe(true);
      expect(component.deliverEditForm.get('isAssociatedWithCraneRequest').value).toBe(true);
      expect(component.getLastCraneRequestId).toHaveBeenCalled();
    });

    it('should handle non-crane equipment type selection', () => {
      const regularEquipment = [{ id: 1, equipmentName: 'Regular Equipment' }];
      component.equipmentList = [{ id: 1, equipmentName: 'Regular Equipment', PresetEquipmentType: { isCraneType: false } }];

      component.onEditSubmitForm(regularEquipment);

      expect(component.craneEquipmentTypeChosen).toBe(false);
      expect(component.deliverEditForm.get('isAssociatedWithCraneRequest').value).toBe(false);
    });

    it('should handle empty equipment selection', () => {
      component.onEditSubmitForm([]);

      expect(component.craneEquipmentTypeChosen).toBe(false);
      expect(component.deliverEditForm.get('isAssociatedWithCraneRequest').value).toBe(false);
    });

    it('should get last crane request ID successfully', fakeAsync(() => {
      component.getLastCraneRequestId();
      tick();

      expect(mockProjectSharingService.guestGetLastCraneRequestId).toHaveBeenCalledWith(
        expect.objectContaining({
          ProjectId: 1,
          ParentCompanyId: 2
        })
      );
      expect(component.craneEquipmentTypeChosen).toBe(true);
    }));
  });

  describe('Date and Time Handling', () => {
    it('should convert start time correctly', () => {
      const deliveryDate = new Date(2024, 0, 15); // January 15, 2024
      const startHours = 10;
      const startMinutes = 30;

      const result = component.convertStart(deliveryDate, startHours, startMinutes);

      expect(result.getFullYear()).toBe(2024);
      expect(result.getMonth()).toBe(0);
      expect(result.getDate()).toBe(15);
      expect(result.getHours()).toBe(10);
      expect(result.getMinutes()).toBe(30);
    });

    it('should handle date change correctly', () => {
      const newDate = new Date();
      component.modalLoader = false;

      jest.spyOn(component, 'onEditSubmitForm');

      component.changeDate(newDate);

      expect(component.NDRTimingChanged).toBe(true);
      expect(component.onEditSubmitForm).toHaveBeenCalledWith(null);
    });

    it('should handle delivery end time change detection', () => {
      jest.spyOn(component, 'onEditSubmitForm');

      component.deliveryEndTimeChangeDetection();

      expect(component.NDRTimingChanged).toBe(true);
      expect(component.onEditSubmitForm).toHaveBeenCalledWith(null);
    });

    it('should check date input correctly', () => {
      const mockEvent = { target: { value: 30 } };
      jest.spyOn(component.deliverEditForm.get('deliveryStart'), 'setValue');

      component.checkDate(mockEvent);

      expect(component.deliverEditForm.get('deliveryStart').setValue).toHaveBeenCalled();
    });

    it('should not change date for values less than 25', () => {
      const mockEvent = { target: { value: 20 } };
      jest.spyOn(component.deliverEditForm.get('deliveryStart'), 'setValue');

      component.checkDate(mockEvent);

      expect(component.deliverEditForm.get('deliveryStart').setValue).not.toHaveBeenCalled();
    });
  });

  describe('Validation Methods', () => {
    it('should validate gate correctly', () => {
      component.gateList = [{ id: 1, gateName: 'Gate 1' }];
      component.errtextenable = true;

      component.gatecheck(1, 'Gate');

      expect(component.errtextenable).toBe(false);
    });

    it('should validate equipment correctly', () => {
      component.equipmentList = [{ id: 1, equipmentName: 'Equipment 1' }];
      component.errequipmentenable = true;

      component.gatecheck(1, 'Equipment');

      expect(component.errequipmentenable).toBe(false);
    });

    it('should validate person correctly', () => {
      component.memberList = [{ id: 1 }, { id: 2 }];
      const validPersons = [{ id: 1 }, { id: 2 }];

      component.gatecheck(validPersons, 'Person');

      expect(component.errmemberenable).toBe(false);
    });

    it('should handle invalid person validation', () => {
      component.memberList = [{ id: 1 }];
      const invalidPersons = [{ id: 1 }, { id: 2 }]; // id: 2 doesn't exist in memberList

      component.gatecheck(invalidPersons, 'Person');

      expect(component.errmemberenable).toBe(true);
    });

    it('should validate string empty values correctly', () => {
      const formValueWithEmptyDescription = {
        description: '   ', // Empty after trim
        notes: 'Valid notes'
      };

      const result = component.checkEditDeliveryStringEmptyValues(formValueWithEmptyDescription);

      expect(result).toBe(true);
      expect(mockToastr.error).toHaveBeenCalledWith('Please Enter valid description.', 'OOPS!');
    });

    it('should validate notes empty values correctly', () => {
      const formValueWithEmptyNotes = {
        description: 'Valid description',
        notes: '   ' // Empty after trim
      };

      const result = component.checkEditDeliveryStringEmptyValues(formValueWithEmptyNotes);

      expect(result).toBe(true);
      expect(mockToastr.error).toHaveBeenCalledWith('Please Enter valid notes.', 'OOPS!');
    });

    it('should pass validation with valid values', () => {
      const validFormValue = {
        description: 'Valid description',
        notes: 'Valid notes'
      };

      const result = component.checkEditDeliveryStringEmptyValues(validFormValue);

      expect(result).toBe(false);
    });

    it('should validate and extract delivery form data successfully', () => {
      const formValue = {
        companyItems: [{ id: 1 }],
        person: [{ id: 1 }],
        defineItems: [{ id: 1 }],
        EquipmentId: [{ id: 1 }],
        description: 'Valid description',
        notes: 'Valid notes'
      };

      const companies = [];
      const persons = [];
      const define = [];
      const equipments = [];

      jest.spyOn(component, 'checkEditDeliveryStringEmptyValues').mockReturnValue(false);

      const result = component.validateAndExtractDeliveryFormData(formValue, companies, persons, define, equipments);

      expect(result).toBe(true);
      expect(companies).toEqual([1]);
      expect(persons).toEqual([1]);
      expect(define).toEqual([1]);
      expect(equipments).toEqual([1]);
    });

    it('should handle missing company items', () => {
      const formValue = {
        companyItems: [],
        person: [{ id: 1 }],
        defineItems: [{ id: 1 }],
        EquipmentId: [{ id: 1 }]
      };

      const result = component.validateAndExtractDeliveryFormData(formValue, [], [], [], []);

      expect(result).toBe(false);
      expect(mockToastr.error).toHaveBeenCalledWith('Responsible Company is required');
    });

    it('should handle missing person items', () => {
      const formValue = {
        companyItems: [{ id: 1 }],
        person: [],
        defineItems: [{ id: 1 }],
        EquipmentId: [{ id: 1 }]
      };

      const result = component.validateAndExtractDeliveryFormData(formValue, [], [], [], []);

      expect(result).toBe(false);
      expect(mockToastr.error).toHaveBeenCalledWith('Responsible Person is required');
    });
  });

  describe('Data Setting Methods', () => {
    beforeEach(() => {
      component.currentEditItem = {
        companyDetails: [{ Company: { id: 1, companyName: 'Company 1' } }],
        defineWorkDetails: [{ DeliverDefineWork: { id: 1, DFOW: 'DFOW 1' } }],
        equipmentDetails: [{ Equipment: { id: 1, equipmentName: 'Equipment 1' } }],
        memberDetails: [{ Member: { id: 1, User: { email: '<EMAIL>', firstName: 'Test', lastName: 'User' } } }],
        location: { id: 1, locationPath: 'Location 1' }
      };
    });

    it('should set company data correctly', () => {
      jest.spyOn(component.deliverEditForm.get('companyItems'), 'patchValue');

      component.setCompany();

      expect(component.editBeforeCompany).toEqual([{ id: 1, companyName: 'Company 1' }]);
      expect(component.deliverEditForm.get('companyItems').patchValue).toHaveBeenCalledWith([{ id: 1, companyName: 'Company 1' }]);
    });

    it('should set define data correctly', () => {
      jest.spyOn(component.deliverEditForm.get('defineItems'), 'patchValue');

      component.setDefine();

      expect(component.editBeforeDFOW).toEqual([{ id: 1, DFOW: 'DFOW 1' }]);
      expect(component.deliverEditForm.get('defineItems').patchValue).toHaveBeenCalledWith([{ id: 1, DFOW: 'DFOW 1' }]);
    });

    it('should set equipment data correctly', () => {
      jest.spyOn(component.deliverEditForm.get('EquipmentId'), 'patchValue');

      component.setEquipment();

      expect(component.editBeforeEquipment).toEqual([{ id: 1, equipmentName: 'Equipment 1' }]);
      expect(component.deliverEditForm.get('EquipmentId').patchValue).toHaveBeenCalledWith([{ id: 1, equipmentName: 'Equipment 1' }]);
    });

    it('should set location data correctly', () => {
      jest.spyOn(component.deliverEditForm.get('LocationId'), 'patchValue');

      component.setlocation();

      expect(component.getChosenLocation).toEqual([{ id: 1, locationPath: 'Location 1' }]);
      expect(component.selectedLocationId).toBe(1);
    });

    it('should handle location selection', () => {
      component.locationList = [{ id: 1, locationPath: 'Location 1' }];
      const data = { id: 1 };

      component.locationSelected(data);

      expect(component.selectedLocationId).toBe(1);
      expect(component.getChosenLocation).toEqual([{ id: 1, locationPath: 'Location 1' }]);
    });
  });

  describe('Error Handling and Utility Methods', () => {
    it('should throw time error correctly', () => {
      component.throwError('time error');

      expect(component.editSubmitted).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
      expect(component.saveQueuedNDR).toBe(false);
      expect(mockToastr.error).toHaveBeenCalledWith('Please Enter Start time Lesser than End time');
    });

    it('should throw future date error correctly', () => {
      component.throwError('other');

      expect(component.editSubmitted).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
      expect(component.saveQueuedNDR).toBe(false);
      expect(mockToastr.error).toHaveBeenCalledWith('Please Enter Future Date.');
    });

    it('should reset form correctly', () => {
      component.formEditSubmitted = true;
      component.editSubmitted = true;

      component.formReset();

      expect(component.formEditSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
    });

    it('should show error correctly', () => {
      const mockError = {
        message: {
          details: [{ error: 'Test error message' }]
        }
      };

      component.showError(mockError);

      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(mockToastr.error).toHaveBeenCalledWith('Test error message');
    });

    it('should check array differences correctly', () => {
      const array1 = [{ id: 1, name: 'Item 1' }, { id: 2, name: 'Item 2' }];
      const array2 = [{ id: 1, name: 'Item 1' }, { id: 3, name: 'Item 3' }];

      const result = component.areDifferentByProperty(array1, array2, 'name');

      expect(result).toBe(true);
      expect(component.array3Value).toEqual(['Item 1', 'Item 2', 'Item 3']);
    });

    it('should return false for identical arrays', () => {
      const array1 = [{ id: 1, name: 'Item 1' }];
      const array2 = [{ id: 1, name: 'Item 1' }];

      const result = component.areDifferentByProperty(array1, array2, 'name');

      expect(result).toBe(false);
    });

    it('should construct delivery payload correctly', () => {
      const formValue = {
        id: 123,
        description: 'Test Delivery',
        escort: true,
        notes: 'Test Notes',
        vehicleDetails: 'Test Vehicle',
        GateId: 1,
        isAssociatedWithCraneRequest: false,
        DeliveryId: 'DEL123',
        recurrenceId: 1,
        recurrenceEndDate: new Date('2024-12-31'),
        deliveryStart: new Date('2024-01-15T10:00:00'),
        deliveryEnd: new Date('2024-01-15T11:00:00')
      };

      const companies = [1, 2];
      const persons = [1];
      const define = [1];
      const equipments = [1];
      const deliveryStart = new Date('2024-01-15T10:00:00');
      const deliveryEnd = new Date('2024-01-15T11:00:00');

      component.selectedLocationId = 1;
      component.seriesOption = 1;

      const payload = component.constructDeliveryPayload(
        formValue,
        companies,
        persons,
        define,
        equipments,
        deliveryStart,
        deliveryEnd
      );

      expect(payload.id).toBe(123);
      expect(payload.description).toBe('Test Delivery');
      expect(payload.escort).toBe(true);
      expect(payload.companies).toEqual([1, 2]);
      expect(payload.persons).toEqual([1]);
      expect(payload.define).toEqual([1]);
      expect(payload.EquipmentId).toEqual([1]);
      expect(payload.ProjectId).toBe(1);
      expect(payload.ParentCompanyId).toBe(2);
      expect(payload.requestType).toBe('deliveryRequest');
    });
  });

  describe('Modal and Form Operations', () => {
    it('should open content modal correctly', () => {
      component.modalLoader = true;

      component.openContentModal();

      expect(component.modalLoader).toBe(false);
    });

    it('should close form correctly', () => {
      component.modalRef1 = { hide: jest.fn() } as any;
      component.formEditSubmitted = true;
      component.editSubmitted = true;
      component.saveQueuedNDR = true;
      component.NDRTimingChanged = true;

      component.closeForm();

      expect(component.modalRef1.hide).toHaveBeenCalled();
      expect(component.formEditSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(component.saveQueuedNDR).toBe(false);
      expect(component.NDRTimingChanged).toBe(false);
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/guest-delivery-calendar']);
    });

    it('should reset form with "no" action', () => {
      component.modalRef1 = { hide: jest.fn() } as any;

      component.resetForm('no');

      expect(component.modalRef1.hide).toHaveBeenCalled();
    });

    it('should reset form with "yes" action', () => {
      component.modalRef1 = { hide: jest.fn() } as any;
      component.formEditSubmitted = true;
      component.editSubmitted = true;
      component.saveQueuedNDR = true;
      component.NDRTimingChanged = true;

      component.resetForm('yes');

      expect(component.modalRef1.hide).toHaveBeenCalled();
      expect(component.formEditSubmitted).toBe(false);
      expect(component.editSubmitted).toBe(false);
      expect(component.saveQueuedNDR).toBe(false);
      expect(component.NDRTimingChanged).toBe(false);
    });

    it('should open confirmation modal popup', () => {
      const template = {} as any;

      component.openConfirmationModalPopupForEditNDR(template);

      expect(mockModalService.show).toHaveBeenCalledWith(template, {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
      });
    });
  });

  describe('API Integration Methods', () => {
    it('should handle successful NDR edit', fakeAsync(() => {
      const mockPayload = {
        id: 123,
        description: 'Test Delivery',
        companies: [1],
        persons: [1],
        define: [1],
        EquipmentId: [1]
      };

      jest.spyOn(component, 'editNDRSuccess');

      component.editNDR(mockPayload);
      tick();

      expect(mockProjectSharingService.guestEditNDR).toHaveBeenCalledWith(mockPayload);
    }));

    it('should handle successful queued NDR submission', fakeAsync(() => {
      const mockPayload = {
        id: 123,
        description: 'Test Delivery',
        isAssociatedWithCraneRequest: false
      };

      jest.spyOn(component, 'editNDRSuccess');

      component.submitQueuedNDR(mockPayload);
      tick();

      expect(mockDeliveryService.submitQueuedNDR).toHaveBeenCalledWith(mockPayload);
    }));

    it('should handle edit NDR success', () => {
      const mockResponse = { message: 'Success' };
      jest.spyOn(component, 'formReset');
      jest.spyOn(component, 'closeForm');

      component.editNDRSuccess(mockResponse);

      expect(mockToastr.success).toHaveBeenCalledWith('Success', 'Success');
      expect(mockMixpanelService.addGuestUserMixpanelEvents).toHaveBeenCalledWith(' Guest User Edited New Delivery Booking');
      expect(component.formReset).toHaveBeenCalled();
      expect(component.closeForm).toHaveBeenCalled();
      expect(component.NDRTimingChanged).toBe(false);
    });

    it('should get project settings successfully', fakeAsync(() => {
      component.getProjectSettings();
      tick();

      expect(mockProjectSettingsService.getGuestProjectSettings).toHaveBeenCalledWith({
        ProjectId: 1
      });
      expect(component.deliveryWindowTime).toBe(30);
      expect(component.deliveryWindowTimeUnit).toBe('minutes');
    }));

    it('should handle autocomplete search', () => {
      const searchText = 'test';
      const result = component.requestAutoEditcompleteItems(searchText);

      expect(mockProjectSharingService.guestSearchNewMember).toHaveBeenCalledWith({
        ProjectId: 1,
        search: searchText,
        ParentCompanyId: 2
      });
      expect(result).toBeDefined();
    });
  });

  describe('Additional Edge Cases and Error Handling', () => {
    it('should handle setMember with member details correctly', fakeAsync(() => {
      component.currentEditItem = {
        memberDetails: [
          {
            Member: {
              id: 1,
              User: {
                email: '<EMAIL>',
                firstName: 'Test',
                lastName: 'User'
              }
            }
          }
        ]
      };

      mockProjectSharingService.guestGetMemberRole.mockReturnValue(of({
        data: { id: 1, User: { email: '<EMAIL>' }, RoleId: 1 }
      }));

      jest.spyOn(component.deliverEditForm.get('person'), 'patchValue');

      component.setMember();
      tick();

      expect(component.deliverEditForm.get('person').patchValue).toHaveBeenCalled();
    }));

    it('should handle setMember with member without firstName', fakeAsync(() => {
      component.currentEditItem = {
        memberDetails: [
          {
            Member: {
              id: 1,
              User: {
                email: '<EMAIL>',
                firstName: null,
                lastName: null
              }
            }
          }
        ]
      };

      mockProjectSharingService.guestGetMemberRole.mockReturnValue(of({
        data: { id: 1, User: { email: '<EMAIL>' }, RoleId: 1 }
      }));

      component.setMember();
      tick();

      expect(mockProjectSharingService.guestGetMemberRole).toHaveBeenCalled();
    }));

    it('should handle close method with form changes', () => {
      const template = {} as any;
      component.deliverEditForm.markAsTouched();
      component.NDRTimingChanged = true;

      jest.spyOn(component, 'openConfirmationModalPopupForEditNDR');

      component.close(template);

      expect(component.openConfirmationModalPopupForEditNDR).toHaveBeenCalledWith(template);
    });

    it('should handle close method without form changes', () => {
      const template = {} as any;
      jest.spyOn(component, 'resetForm');

      component.close(template);

      expect(component.resetForm).toHaveBeenCalledWith('yes');
    });

    it('should handle updateDelivery with crane request validation error', () => {
      const params = {
        editNdrFormValue: {
          isAssociatedWithCraneRequest: true,
          cranePickUpLocation: '',
          craneDropOffLocation: ''
        },
        deliveryStart: new Date(),
        deliveryEnd: new Date(),
        companies: [],
        persons: [],
        define: [],
        action: 'submitCurrentNDR',
        equipments: []
      };

      jest.spyOn(component, 'checkStartEnd').mockReturnValue(true);
      jest.spyOn(component, 'validateAndExtractDeliveryFormData').mockReturnValue(true);
      jest.spyOn(component, 'constructDeliveryPayload').mockReturnValue({
        isAssociatedWithCraneRequest: true
      });

      component.deliverEditForm.get('cranePickUpLocation').setValue('');
      component.deliverEditForm.get('craneDropOffLocation').setValue('');

      component.updateDelivery(params);

      expect(mockToastr.error).toHaveBeenCalledWith('Please enter Picking From and Picking To');
      expect(component.formSubmitted).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
    });

    it('should handle submitQueuedNDR with crane validation error', () => {
      const payload = { isAssociatedWithCraneRequest: true };
      component.deliverEditForm.get('isAssociatedWithCraneRequest').setValue(true);
      component.deliverEditForm.get('cranePickUpLocation').setValue('');
      component.deliverEditForm.get('craneDropOffLocation').setValue('');

      component.submitQueuedNDR(payload);

      expect(mockToastr.error).toHaveBeenCalledWith('Please enter Picking From and Picking To');
      expect(component.formSubmitted).toBe(false);
      expect(component.formEditSubmitted).toBe(false);
    });

    it('should handle API error responses', fakeAsync(() => {
      const mockError = {
        message: {
          statusCode: 400,
          details: [{ error: 'API Error' }]
        }
      };

      mockProjectSharingService.guestEditNDR.mockReturnValue(throwError(mockError));
      jest.spyOn(component, 'showError');

      const payload = { id: 123 };
      component.editNDR(payload);
      tick();

      expect(component.showError).toHaveBeenCalledWith(mockError);
    }));

    it('should handle API error without message', fakeAsync(() => {
      const mockError = {};

      mockProjectSharingService.guestEditNDR.mockReturnValue(throwError(mockError));

      const payload = { id: 123 };
      component.editNDR(payload);
      tick();

      expect(mockToastr.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    }));

    it('should handle checkEditDeliveryFutureDate with save action', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() - 1); // Past date

      const result = component.checkEditDeliveryFutureDate(futureDate, futureDate, 'save');

      expect(result).toBe(true); // Should return true for save action regardless of date
    });

    it('should handle checkEditDeliveryFutureDate with past date', () => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);
      component.deliveryWindowTime = 0;
      component.deliveryWindowTimeUnit = 'minutes';

      const result = component.checkEditDeliveryFutureDate(pastDate, pastDate, 'submit');

      expect(result).toBe(false);
    });
  });
});







