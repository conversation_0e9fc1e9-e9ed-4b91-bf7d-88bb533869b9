import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TimeslotComponent } from './time-slot.component';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { ProjectService } from '../../services/profile/project.service';
import { CalendarService } from '../../services/profile/calendar.service';
import { DeliveryService } from 'src/app/services/profile/delivery.service';
import { ProjectSettingsService } from '../../services/project_settings/project-settings.service';
import { of } from 'rxjs';
import { FullCalendarModule } from '@fullcalendar/angular';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('TimeslotComponent', () => {
  let component: TimeslotComponent;
  let fixture: ComponentFixture<TimeslotComponent>;
  let projectServiceMock: jest.Mocked<ProjectService>;
  let calendarServiceMock: jest.Mocked<CalendarService>;
  let deliveryServiceMock: jest.Mocked<DeliveryService>;
  let projectSettingsServiceMock: jest.Mocked<ProjectSettingsService>;

  beforeEach(async () => {
    projectServiceMock = {
      projectParent: of({ ProjectId: '123', ParentCompanyId: '456' })
    } as any;

    calendarServiceMock = {
      // Add mock methods as needed
    } as any;

    deliveryServiceMock = {
      getAvailableTimeSlots: jest.fn().mockReturnValue(of({
        data: [
          { id: 'slot-1', start: '2024-03-20T09:00:00', end: '2024-03-20T10:00:00' },
          { id: 'slot-2', start: '2024-03-20T11:00:00', end: '2024-03-20T12:00:00' }
        ]
      }))
    } as any;

    projectSettingsServiceMock = {
      getProjectSettings: jest.fn().mockReturnValue(of({
        data: {
          projectSettings: {
            workingWindowStartHours: 9,
            workingWindowStartMinutes: 0,
            workingWindowEndHours: 17,
            workingWindowEndMinutes: 0
          }
        }
      }))
    } as any;

    await TestBed.configureTestingModule({
      declarations: [TimeslotComponent],
      imports: [ReactiveFormsModule, FullCalendarModule],
      providers: [
        FormBuilder,
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: CalendarService, useValue: calendarServiceMock },
        { provide: DeliveryService, useValue: deliveryServiceMock },
        { provide: ProjectSettingsService, useValue: projectSettingsServiceMock }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TimeslotComponent);
    component = fixture.componentInstance;

    // Mock the calendar component
    component.calendarComponent = {
      getApi: jest.fn().mockReturnValue({
        gotoDate: jest.fn(),
        removeAllEventSources: jest.fn(),
        getEvents: jest.fn().mockReturnValue([]),
        addEventSource: jest.fn()
      })
    } as any;

    // Setup required inputs
    component.equipmentId = [{ id: 'equip-1' }];
    component.GateId = 'gate-1';
    component.LocationId = 'loc-1';
    component.timeZone = 'UTC';
    component.selectedBookingType = 'delivery';

    // Mock getEventNDR to prevent actual API calls
    jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.showCalendar).toBeFalsy();
    expect(component.selectedEvents).toEqual([]);
    expect(component.timeslotForm).toBeTruthy();
  });

  it('should format hour and minute correctly', () => {
    expect(component.formatHourAndMinute(9, 30)).toBe('09:30:00');
    expect(component.formatHourAndMinute(14, 5)).toBe('14:05:00');
  });

  it('should format working time correctly', () => {
    expect(component.formatWorkingTime(9, 30)).toBe('9:30 AM');
    expect(component.formatWorkingTime(14, 5)).toBe('2:05 PM');
    expect(component.formatWorkingTime(12, 0)).toBe('12:00 PM');
    expect(component.formatWorkingTime(0, 0)).toBe('12:00 AM');
  });

  it('should handle time selection', () => {
    const mockInfo = {
      startStr: '2024-03-20T10:00:00',
      endStr: '2024-03-20T11:00:00',
      start: new Date('2024-03-20T10:00:00'),
      end: new Date('2024-03-20T11:00:00')
    };

    const selectTimeSpy = jest.spyOn(component.selectTime, 'emit');
    component.handleTimeSelection(mockInfo);

    expect(selectTimeSpy).toHaveBeenCalledWith({
      start: '10:00',
      end: '11:00'
    });
  });

  it('should close popup', () => {
    const closeModalSpy = jest.spyOn(component.closeModal, 'emit');
    component.closePopup();
    expect(closeModalSpy).toHaveBeenCalled();
  });

  it('should convert date to ISO format', () => {
    const date = '2024-03-20';
    const result = component.convertToISODate(date);
    expect(result).toContain('2024-03-20');
    expect(result).toContain('T00:00:00');
  });

  it('should check for event overlap', () => {
    component.events = [{
      id: 1,
      start: '2024-03-20T10:00:00',
      end: '2024-03-20T11:00:00',
      requestType: 'delivery'
    }];

    component.selectedBookingType = 'delivery';

    // Overlapping event
    const overlappingEvent = {
      startStr: '2024-03-20T10:30:00',
      endStr: '2024-03-20T11:30:00'
    };
    expect(component.checkOverlap(overlappingEvent)).toBe(true);

    // Non-overlapping event
    const nonOverlappingEvent = {
      startStr: '2024-03-20T11:30:00',
      endStr: '2024-03-20T12:30:00'
    };
    expect(component.checkOverlap(nonOverlappingEvent)).toBe(false);
  });

  // Additional tests
  it('should handle date click', () => {
    const mockDateClickArg = {
      dateStr: '2024-03-20T10:00:00'
    };

    component.handleDateClick(mockDateClickArg);
    expect(component.selectedDate).toEqual(new Date(mockDateClickArg.dateStr));
  });

  it('should handle event resize', () => {
    // Initialize events array first
    component.events = [];

    const mockResizeInfo = {
      event: {
        id: 'slot-1',
        startStr: '2024-03-20T09:00:00',
        endStr: '2024-03-20T10:30:00',
        start: new Date('2024-03-20T09:00:00'),
        end: new Date('2024-03-20T10:30:00'),
        setStart: jest.fn(),
        setEnd: jest.fn()
      }
    };

    // Mock the checkOverlap method to return false (no overlap)
    jest.spyOn(component, 'checkOverlap').mockReturnValue(false);

    // Mock the updateResizedEvent method
    component.selectedEvents = [{id: 'slot-1', start: '', end: ''}];
    jest.spyOn(component, 'updateResizedEvent').mockImplementation(() => {
      component.events.push({id: 'slot-1', start: '', end: ''});
    });

    component.handleEventResize(mockResizeInfo);
    expect(component.events.find(e => e.id === 'slot-1')).toBeDefined();
  });

  it('should call getEventNDR when changing day', () => {
    // Create a fresh spy for getEventNDR
    const getEventNDRSpy = jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});

    // Call the method directly
    component.changeDay(1);

    // Only verify that getEventNDR was called
    expect(getEventNDRSpy).toHaveBeenCalled();
  });

  it('should update date value when handling date change', () => {
    const mockEvent = {
      target: {
        value: '2024-04-15'
      }
    };

    // Create a direct spy on the timeslotForm.get('selectedDate').setValue method
    const setValueSpy = jest.fn();
    const getFormControlSpy = jest.spyOn(component.timeslotForm, 'get')
      .mockReturnValue({ setValue: setValueSpy } as any);

    // Call the method directly
    component.onDateChange(mockEvent);

    // Verify the form control was accessed and setValue was called
    expect(getFormControlSpy).toHaveBeenCalledWith('selectedDate');
    expect(setValueSpy).toHaveBeenCalledWith('2024-04-15');
  });

  it('should update slot and emit selected time in non-edit mode', () => {
    // Setup for non-edit mode
    component.isEditMode = false;
    component.selectedEvents = [{
      start: '2024-03-20T10:00:00',
      end: '2024-03-20T11:00:00'
    }];

    const selectTimeSpy = jest.spyOn(component.selectTime, 'emit');
    component.updateSlot();

    expect(selectTimeSpy).toHaveBeenCalledWith({
      start: '2024-03-20T10:00:00',
      end: '2024-03-20T11:00:00'
    });
  });

  it('should update slot and emit selected time in edit mode', () => {
    // Setup for edit mode
    component.isEditMode = true;
    component.selectedId = 'slot-1';
    component.events = [{
      id: 'slot-1',
      start: '2024-03-20T09:00:00',
      end: '2024-03-20T10:00:00'
    }];

    const selectTimeSpy = jest.spyOn(component.selectTime, 'emit');
    component.updateSlot();

    expect(selectTimeSpy).toHaveBeenCalledWith({
      start: '2024-03-20T09:00:00',
      end: '2024-03-20T10:00:00'
    });
  });

  it('should handle keyboard navigation', () => {
    const mockEvent = {
      key: 'Enter',
      preventDefault: jest.fn()
    } as unknown as KeyboardEvent;

    const changeDaySpy = jest.spyOn(component, 'changeDay');
    component.handleDownKeydown(mockEvent, 1, 'day');

    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(changeDaySpy).toHaveBeenCalledWith(1);
  });

  it('should fetch project settings on init', () => {
    component.ngOnInit();
    expect(projectSettingsServiceMock.getProjectSettings).toHaveBeenCalled();
  });

  it('should set today date', () => {
    const formSpy = jest.spyOn(component.timeslotForm, 'get');
    const mockFormControl = { setValue: jest.fn() };
    formSpy.mockReturnValue(mockFormControl as any);

    component.setTodayDate();

    expect(formSpy).toHaveBeenCalledWith('selectedDate');
    expect(mockFormControl.setValue).toHaveBeenCalled();
  });

  it('should correctly format working time', () => {
    // Test morning hours
    expect(component.formatWorkingTime(0, 0)).toBe('12:00 AM');
    expect(component.formatWorkingTime(8, 30)).toBe('8:30 AM');

    // Test noon
    expect(component.formatWorkingTime(12, 0)).toBe('12:00 PM');

    // Test afternoon/evening hours
    expect(component.formatWorkingTime(13, 15)).toBe('1:15 PM');
    expect(component.formatWorkingTime(23, 59)).toBe('11:59 PM');
  });

  it('should handle event click correctly', () => {
    // Mock event info
    const mockEventInfo = {
      event: {
        id: 'event-1',
        start: new Date('2024-04-01T10:00:00'),
        end: new Date('2024-04-01T11:00:00'),
        startStr: '2024-04-01T10:00:00',
        endStr: '2024-04-01T11:00:00',
        extendedProps: {
          isNew: true,
          isEdit: false
        }
      }
    };

    // Create spy for selectTime emit
    const selectTimeSpy = jest.spyOn(component.selectTime, 'emit');

    // Manually trigger what would happen in an event click
    component.selectedEvents = [{
      id: mockEventInfo.event.id,
      start: mockEventInfo.event.startStr,
      end: mockEventInfo.event.endStr
    }];

    component.selectTime.emit({
      start: component.formatTime(mockEventInfo.event.start),
      end: component.formatTime(mockEventInfo.event.end)
    });

    // Verify the event was selected
    expect(component.selectedEvents.length).toBe(1);
    expect(component.selectedEvents[0].id).toBe('event-1');
    expect(selectTimeSpy).toHaveBeenCalled();
  });

  it('should update calendar events when needed', () => {
    // Setup calendar API mock
    const calendarApi = {
      gotoDate: jest.fn(),
      removeAllEventSources: jest.fn(),
      addEventSource: jest.fn()
    };

    // Set the calendar component
    component.calendarComponent = {
      getApi: jest.fn().mockReturnValue(calendarApi)
    } as any;

    // Initialize selectedEvents to prevent error
    component.selectedEvents = [{
      id: 'event-1',
      start: '2024-04-01T10:00:00',
      end: '2024-04-01T11:00:00'
    }];

    // Mock updateSlot to prevent it from being called
    jest.spyOn(component, 'updateSlot').mockImplementation(() => {});

    // Call updateCalendarEvents
    component.updateCalendarEvents();

    // Verify calendarOptions was updated
    expect(component.calendarOptions.events).toBeDefined();
  });

  it('should handle keyboard navigation for day change', () => {
    // Mock keyboard event
    const mockEvent = {
      key: 'Enter',
      preventDefault: jest.fn()
    } as unknown as KeyboardEvent;

    // Spy on changeDay method
    const changeDaySpy = jest.spyOn(component, 'changeDay').mockImplementation(() => {});

    // Call keyboard handler for day
    component.handleDownKeydown(mockEvent, 1, 'day');

    // Verify changeDay was called
    expect(changeDaySpy).toHaveBeenCalledWith(1);
  });

  it('should have proper calendar options', () => {
    // Verify calendar options exist and have expected properties
    expect(component.calendarOptions).toBeDefined();
    expect(component.calendarOptions.initialView).toBe('timeGridDay');
    expect(component.calendarOptions.selectable).toBe(true);
    expect(component.calendarOptions.editable).toBe(true);
  });

  it('should handle time selection correctly', () => {
    const mockInfo = {
      startStr: '2024-04-01T10:00:00',
      endStr: '2024-04-01T11:00:00',
      start: new Date('2024-04-01T10:00:00'),
      end: new Date('2024-04-01T11:00:00')
    };

    const selectTimeSpy = jest.spyOn(component.selectTime, 'emit');
    component.handleTimeSelection(mockInfo);

    expect(selectTimeSpy).toHaveBeenCalledWith({
      start: '10:00',
      end: '11:00'
    });
  });

  it('should update selected events', () => {
    // Set initial state
    component.selectedEvents = [];

    // Add a new event
    const newEvent = {
      id: 'event-3',
      start: '2024-04-01T14:00:00',
      end: '2024-04-01T15:00:00'
    };

    component.selectedEvents.push(newEvent);

    // Verify
    expect(component.selectedEvents.length).toBe(1);
    expect(component.selectedEvents[0]).toEqual(newEvent);
  });

  // Additional tests for TimeslotComponent

  it('should test canEventsOverlap functionality', () => {
    // Setup project settings
    component.projectSettingDetails = {
      deliveryAllowOverlappingBooking: true,
      deliveryAllowOverlappingCalenderBooking: false
    };

    // Test case: both calendar events, not allowed to overlap
    const stillEvent = { extendedProps: { calendarEvent: true } };
    const movingEvent = { extendedProps: { calendarEvent: true } };
    component.selectedBookingType = 'deliveryRequest';

    expect(component.canEventsOverlap(stillEvent, movingEvent)).toBe(false);

    // Test case: one calendar event, one booking, allowed to overlap
    const calendarEvent = { extendedProps: { calendarEvent: true } };
    const bookingEvent = { extendedProps: { calendarEvent: false } };
    component.projectSettingDetails.deliveryAllowOverlappingCalenderBooking = true;

    expect(component.canEventsOverlap(calendarEvent, bookingEvent)).toBe(true);
  });

  it('should test canEventBeOverlapped functionality', () => {
    // Setup project settings
    component.projectSettingDetails = {
      deliveryAllowOverlappingBooking: false,
      deliveryAllowOverlappingCalenderBooking: true
    };

    // Test case: calendar event, allowed to overlap
    const calendarEvent = { extendedProps: { calendarEvent: true } };
    component.selectedBookingType = 'deliveryRequest';

    expect(component.canEventBeOverlapped(calendarEvent)).toBe(true);

    // Test case: booking event, not allowed to overlap
    const bookingEvent = { extendedProps: { calendarEvent: false } };

    expect(component.canEventBeOverlapped(bookingEvent)).toBe(false);
  });

  it('should handle event drop correctly', () => {
    // Setup
    component.isEditMode = false;
    component.selectedEvents = [{
      id: 'event-1',
      start: '2024-04-01T10:00:00',
      end: '2024-04-01T11:00:00'
    }];

    // Mock the event drop info
    const mockDropInfo = {
      event: {
        startStr: '2024-04-01T11:00:00',
        endStr: '2024-04-01T12:00:00'
      }
    };

    // Mock checkOverlap to return false
    jest.spyOn(component, 'checkOverlap').mockReturnValue(false);

    // Mock updateCalendarEvents to prevent errors
    jest.spyOn(component, 'updateCalendarEvents').mockImplementation(() => {});

    // Call the method
    component.handleEventDrop(mockDropInfo);

    // Verify the event was updated
    expect(component.selectedEvents[0].start).toBe('2024-04-01T11:00:00');
    expect(component.selectedEvents[0].end).toBe('2024-04-01T12:00:00');
  });

  it('should format hour and minute correctly', () => {
    expect(component.formatHourAndMinute(9, 30)).toBe('09:30:00');
    expect(component.formatHourAndMinute(14, 5)).toBe('14:05:00');
    expect(component.formatHourAndMinute(0, 0)).toBe('00:00:00');
    expect(component.formatHourAndMinute(23, 59)).toBe('23:59:00');
  });

  it('should set today date correctly', () => {
    // Mock the calendar API
    const calendarApi = {
      gotoDate: jest.fn()
    };
    component.calendarComponent = {
      getApi: jest.fn().mockReturnValue(calendarApi)
    } as any;

    // Mock the form control
    const setValueSpy = jest.fn();
    component.timeslotForm = {
      patchValue: setValueSpy
    } as any;

    // Call setToday
    component.setToday();

    // Verify today's date was set
    expect(setValueSpy).toHaveBeenCalled();
    expect(calendarApi.gotoDate).toHaveBeenCalled();
  });

  it('should update resized event correctly', () => {
    // Setup
    component.selectedEvents = [{
      id: 'event-1',
      start: '2024-03-20T09:00:00',
      end: '2024-03-20T10:00:00'
    }];

    const resizedEvent = {
      startStr: '2024-03-20T09:30:00',
      endStr: '2024-03-20T10:30:00',
      start: new Date('2024-03-20T09:30:00'),
      end: new Date('2024-03-20T10:30:00')
    };

    // Mock events array to check for overlap
    component.events = [];

    // Mock form control
    const patchValueSpy = jest.spyOn(component.timeslotForm, 'patchValue');

    // Call updateResizedEvent
    component.updateResizedEvent(0, resizedEvent);

    // Verify event was updated
    expect(component.selectedEvents[0].start).toBe('2024-03-20T09:30:00');
    expect(component.selectedEvents[0].end).toBe('2024-03-20T10:30:00');
    expect(patchValueSpy).toHaveBeenCalled();
  });

  it('should format time correctly', () => {
    const date1 = new Date('2024-03-20T09:30:00');
    expect(component.formatTime(date1)).toBe('09:30');

    const date2 = new Date('2024-03-20T14:05:00');
    expect(component.formatTime(date2)).toBe('14:05');

    const date3 = new Date('2024-03-20T00:00:00');
    expect(component.formatTime(date3)).toBe('00:00');
  });

  // Additional comprehensive test cases for 90% coverage

  it('should handle null input in convertToISODate', () => {
    const result = component.convertToISODate(null);
    expect(result).toBe('');
  });

  it('should handle undefined input in convertToISODate', () => {
    const result = component.convertToISODate(undefined);
    expect(result).toBe('');
  });

  it('should handle non-string input in convertToISODate', () => {
    const result = component.convertToISODate(123);
    expect(result).toBe('');
  });

  it('should handle invalid date string in convertToISODate', () => {
    const result = component.convertToISODate('invalid-date');
    expect(result).toBe('');
  });

  it('should handle date string that already contains T in convertToISODate', () => {
    const isoString = '2024-03-20T10:00:00.000Z';
    const result = component.convertToISODate(isoString);
    expect(result).toBe(isoString);
  });

  it('should handle empty string in convertToISODate', () => {
    const result = component.convertToISODate('');
    expect(result).toBe('');
  });

  it('should return early if info is null in handleTimeSelection', () => {
    const selectTimeSpy = jest.spyOn(component.selectTime, 'emit');
    component.handleTimeSelection(null);
    expect(selectTimeSpy).not.toHaveBeenCalled();
  });

  it('should return early if info.start is null in handleTimeSelection', () => {
    const selectTimeSpy = jest.spyOn(component.selectTime, 'emit');
    component.handleTimeSelection({ start: null, end: new Date() });
    expect(selectTimeSpy).not.toHaveBeenCalled();
  });

  it('should return early if info.end is null in handleTimeSelection', () => {
    const selectTimeSpy = jest.spyOn(component.selectTime, 'emit');
    component.handleTimeSelection({ start: new Date(), end: null });
    expect(selectTimeSpy).not.toHaveBeenCalled();
  });

  it('should handle edit mode when event is not found in handleTimeSelection', () => {
    component.isEditMode = true;
    component.selectedId = 'non-existent-id';
    component.events = [{ id: 'different-id', start: '', end: '' }];

    const mockInfo = {
      startStr: '2024-03-20T10:00:00',
      endStr: '2024-03-20T11:00:00',
      start: new Date('2024-03-20T10:00:00'),
      end: new Date('2024-03-20T11:00:00')
    };

    const selectTimeSpy = jest.spyOn(component.selectTime, 'emit');
    component.handleTimeSelection(mockInfo);

    expect(selectTimeSpy).toHaveBeenCalledWith({
      start: '10:00',
      end: '11:00'
    });
  });

  it('should return early if info is null in handleEventResize', () => {
    const updateResizedEventSpy = jest.spyOn(component, 'updateResizedEvent');
    component.handleEventResize(null);
    expect(updateResizedEventSpy).not.toHaveBeenCalled();
  });

  it('should return early if info.event is null in handleEventResize', () => {
    const updateResizedEventSpy = jest.spyOn(component, 'updateResizedEvent');
    component.handleEventResize({ event: null });
    expect(updateResizedEventSpy).not.toHaveBeenCalled();
  });

  it('should revert event if overlap detected in handleEventResize', () => {
    const mockRevert = jest.fn();
    const mockInfo = {
      event: {
        id: 'event-1',
        startStr: '2024-03-20T10:00:00',
        endStr: '2024-03-20T11:00:00',
        start: new Date('2024-03-20T10:00:00'),
        end: new Date('2024-03-20T11:00:00')
      },
      revert: mockRevert
    };

    component.events = [
      { id: 'other-event', start: '2024-03-20T10:30:00', end: '2024-03-20T11:30:00', requestType: 'delivery' }
    ];
    component.selectedBookingType = 'delivery';

    jest.spyOn(component, 'checkOverlap').mockReturnValue(true);

    component.handleEventResize(mockInfo);

    expect(mockRevert).toHaveBeenCalled();
  });

  it('should handle case when events array is null in handleEventResize', () => {
    component.events = null;
    const mockInfo = {
      event: {
        id: 'event-1',
        startStr: '2024-03-20T10:00:00',
        endStr: '2024-03-20T11:00:00',
        start: new Date('2024-03-20T10:00:00'),
        end: new Date('2024-03-20T11:00:00')
      }
    };

    component.selectedEvents = [{ id: 'event-1', start: '', end: '' }];
    const updateResizedEventSpy = jest.spyOn(component, 'updateResizedEvent');

    component.handleEventResize(mockInfo);

    expect(updateResizedEventSpy).toHaveBeenCalled();
  });

  it('should return false if events array is null in checkOverlap', () => {
    component.events = null;
    const mockEvent = {
      startStr: '2024-03-20T10:00:00',
      endStr: '2024-03-20T11:00:00'
    };

    const result = component.checkOverlap(mockEvent);
    expect(result).toBe(false);
  });

  it('should return false if events array is not an array in checkOverlap', () => {
    component.events = 'not-an-array' as any;
    const mockEvent = {
      startStr: '2024-03-20T10:00:00',
      endStr: '2024-03-20T11:00:00'
    };

    const result = component.checkOverlap(mockEvent);
    expect(result).toBe(false);
  });

  it('should handle same event in edit mode in checkOverlap', () => {
    component.isEditMode = true;
    component.events = [
      { id: 'same-event', start: '2024-03-20T10:00:00', end: '2024-03-20T11:00:00', requestType: 'delivery' }
    ];
    component.selectedBookingType = 'delivery';

    const mockEvent = {
      startStr: '2024-03-20T10:30:00',
      endStr: '2024-03-20T11:30:00'
    };

    const result = component.checkOverlap(mockEvent, 'same-event');
    expect(result).toBe(false);
  });

  it('should handle different booking types in checkOverlap', () => {
    component.events = [
      { id: 'event-1', start: '2024-03-20T10:00:00', end: '2024-03-20T11:00:00', requestType: 'inspection' }
    ];
    component.selectedBookingType = 'delivery';

    const mockEvent = {
      startStr: '2024-03-20T10:30:00',
      endStr: '2024-03-20T11:30:00'
    };

    const result = component.checkOverlap(mockEvent);
    expect(result).toBe(false);
  });

  it('should call showPicker on date picker element in openDatePicker', () => {
    const mockShowPicker = jest.fn();
    component.datePicker = {
      nativeElement: {
        showPicker: mockShowPicker
      }
    } as any;

    component.openDatePicker();

    expect(mockShowPicker).toHaveBeenCalled();
  });

  // Additional comprehensive test cases for 90% coverage

  describe('renderCustomEventContent', () => {
    it('should render new booking event content', () => {
      const mockArg = {
        event: {
          start: new Date('2024-03-20T10:00:00'),
          end: new Date('2024-03-20T11:00:00'),
          extendedProps: {
            isNew: true,
            isEdit: false
          }
        }
      };

      const result = component.renderCustomEventContent(mockArg);
      expect(result.domNodes).toHaveLength(1);
      expect((result.domNodes[0].children[0] as HTMLElement).innerText).toBe('New Booking');
    });

    it('should render booked slot event content', () => {
      const mockArg = {
        event: {
          start: new Date('2024-03-20T10:00:00'),
          end: new Date('2024-03-20T11:00:00'),
          extendedProps: {
            isNew: false,
            isEdit: false
          }
        }
      };

      const result = component.renderCustomEventContent(mockArg);
      expect(result.domNodes).toHaveLength(1);
      expect((result.domNodes[0].children[0] as HTMLElement).innerText).toBe('Booked Slot');
    });

    it('should render current slot event content', () => {
      const mockArg = {
        event: {
          start: new Date('2024-03-20T10:00:00'),
          end: new Date('2024-03-20T11:00:00'),
          extendedProps: {
            isNew: false,
            isEdit: true
          }
        }
      };

      const result = component.renderCustomEventContent(mockArg);
      expect(result.domNodes).toHaveLength(1);
      expect((result.domNodes[0].children[0] as HTMLElement).innerText).toBe('Current Slot');
    });
  });

  describe('getEventNDR', () => {
    beforeEach(() => {
      component.ProjectId = '123';
      deliveryServiceMock.getAvailableTimeSlots.mockReturnValue(of({
        data: [
          { id: 'slot-1', start: '2024-03-20T09:00:00', end: '2024-03-20T10:00:00' }
        ]
      }));
    });

    it('should call getAvailableTimeSlots with correct parameters', () => {
      const equipmentId = [{ id: 'equip-1' }];
      const locationId = 'loc-1';
      const gateId = 'gate-1';
      const timeZone = 'UTC';
      const date = '2024-03-20';

      component.getEventNDR(equipmentId, locationId, gateId, timeZone, date);

      expect(deliveryServiceMock.getAvailableTimeSlots).toHaveBeenCalledWith('123', {
        date: '2024-03-20',
        equipmentId: ['equip-1'],
        timeZone: 'UTC',
        LocationId: 'loc-1',
        GateId: 1,
        bookingType: component.selectedBookingType
      });
    });

    it('should handle null date parameter', () => {
      const equipmentId = [{ id: 'equip-1' }];
      const locationId = 'loc-1';
      const gateId = 'gate-1';
      const timeZone = 'UTC';

      // Mock form control
      const mockFormControl = { value: new Date('2024-03-20') };
      jest.spyOn(component.timeslotForm, 'get').mockReturnValue(mockFormControl as any);

      component.getEventNDR(equipmentId, locationId, gateId, timeZone, null);

      expect(deliveryServiceMock.getAvailableTimeSlots).toHaveBeenCalled();
    });

    it('should not call service if equipmentId or GateId is missing', () => {
      component.getEventNDR(null, 'loc-1', null, 'UTC', '2024-03-20');
      expect(deliveryServiceMock.getAvailableTimeSlots).not.toHaveBeenCalled();
    });

    it('should set form control value when date is provided', () => {
      const setValueSpy = jest.fn();
      const mockFormControl = { setValue: setValueSpy };
      jest.spyOn(component.timeslotForm, 'get').mockReturnValue(mockFormControl as any);

      const equipmentId = [{ id: 'equip-1' }];
      const date = '2024-03-20';

      component.getEventNDR(equipmentId, 'loc-1', 'gate-1', 'UTC', date);

      expect(setValueSpy).toHaveBeenCalledWith(new Date(date));
    });
  });

  describe('setEventNDRData', () => {
    beforeEach(() => {
      const mockCalendarApi = {
        removeAllEventSources: jest.fn(),
        getEvents: jest.fn().mockReturnValue([]),
        addEventSource: jest.fn()
      };
      component.calendarComponent = {
        getApi: jest.fn().mockReturnValue(mockCalendarApi)
      } as any;
      component.calendarApi = mockCalendarApi;
    });

    it('should handle null or undefined data', () => {
      component.setEventNDRData(null);
      expect(component.calendarApi.removeAllEventSources).not.toHaveBeenCalled();

      component.setEventNDRData(undefined);
      expect(component.calendarApi.removeAllEventSources).not.toHaveBeenCalled();
    });

    it('should handle non-array data', () => {
      component.setEventNDRData('not-an-array');
      expect(component.calendarApi.removeAllEventSources).not.toHaveBeenCalled();
    });

    it('should process data correctly with project settings', () => {
      component.projectSettingDetails = {
        deliveryAllowOverlappingBooking: true
      };
      component.selectedId = 'selected-slot';

      const mockData = [
        { id: 'slot-1', start: '2024-03-20T09:00:00', end: '2024-03-20T10:00:00', calendarEvent: false },
        { id: 'selected-slot', start: '2024-03-20T11:00:00', end: '2024-03-20T12:00:00', calendarEvent: false }
      ];

      component.setEventNDRData(mockData);

      expect(component.calendarApi.removeAllEventSources).toHaveBeenCalled();
      expect(component.calendarApi.addEventSource).toHaveBeenCalled();
      expect(component.events).toHaveLength(2); // 1 background + 1 editable
    });

    it('should handle calendar events correctly', () => {
      component.projectSettingDetails = {
        deliveryAllowOverlappingBooking: false
      };

      const mockData = [
        { id: 'cal-event', start: '2024-03-20T09:00:00', end: '2024-03-20T10:00:00', calendarEvent: true }
      ];

      component.setEventNDRData(mockData);

      expect(component.events[0].title).toBe('Calender Event');
      expect(component.events[0].extendedProps.calendarEvent).toBe(true);
    });

    it('should return early if calendarApi is not available', () => {
      component.calendarApi = null;
      const mockData = [{ id: 'slot-1', start: '2024-03-20T09:00:00', end: '2024-03-20T10:00:00' }];

      component.setEventNDRData(mockData);

      // Should not throw error and should return early
      expect(component.events).toBeDefined();
    });
  });

  describe('canEventsOverlap edge cases', () => {
    beforeEach(() => {
      component.projectSettingDetails = {
        deliveryAllowOverlappingBooking: true,
        deliveryAllowOverlappingCalenderBooking: false,
        inspectionAllowOverlappingBooking: false,
        inspectionAllowOverlappingCalenderBooking: true
      };
    });

    it('should return false for unknown booking type', () => {
      component.selectedBookingType = 'unknownType';
      const stillEvent = { extendedProps: { calendarEvent: false } };
      const movingEvent = { extendedProps: { calendarEvent: false } };

      const result = component.canEventsOverlap(stillEvent, movingEvent);
      expect(result).toBe(false);
    });

    it('should handle inspection booking type', () => {
      component.selectedBookingType = 'inspectionRequest';
      const stillEvent = { extendedProps: { calendarEvent: true } };
      const movingEvent = { extendedProps: { calendarEvent: false } };

      const result = component.canEventsOverlap(stillEvent, movingEvent);
      expect(result).toBe(true);
    });

    it('should handle crane booking type', () => {
      component.selectedBookingType = 'craneRequest';
      component.projectSettingDetails.craneAllowOverlappingBooking = true;
      const stillEvent = { extendedProps: { calendarEvent: false } };
      const movingEvent = { extendedProps: { calendarEvent: false } };

      const result = component.canEventsOverlap(stillEvent, movingEvent);
      expect(result).toBe(true);
    });

    it('should handle concrete booking type', () => {
      component.selectedBookingType = 'concreteRequest';
      component.projectSettingDetails.concreteAllowOverlappingBooking = false;
      const stillEvent = { extendedProps: { calendarEvent: false } };
      const movingEvent = { extendedProps: { calendarEvent: false } };

      const result = component.canEventsOverlap(stillEvent, movingEvent);
      expect(result).toBe(false);
    });
  });

  describe('canEventBeOverlapped edge cases', () => {
    beforeEach(() => {
      component.projectSettingDetails = {
        deliveryAllowOverlappingBooking: true,
        deliveryAllowOverlappingCalenderBooking: false
      };
    });

    it('should return false and log warning for unknown booking type', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      component.selectedBookingType = 'unknownType';
      const event = { extendedProps: { calendarEvent: false } };

      const result = component.canEventBeOverlapped(event);

      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith('Invalid selectedBookingType:', 'unknownType');
      consoleSpy.mockRestore();
    });

    it('should handle missing extendedProps', () => {
      component.selectedBookingType = 'deliveryRequest';
      const event = { extendedProps: null };

      const result = component.canEventBeOverlapped(event);
      expect(result).toBe(true); // Should default to false for calendarEvent
    });

    it('should handle missing calendarEvent property', () => {
      component.selectedBookingType = 'deliveryRequest';
      const event = { extendedProps: {} };

      const result = component.canEventBeOverlapped(event);
      expect(result).toBe(true); // Should default to false for calendarEvent
    });
  });

  describe('constructor and initialization', () => {
    it('should initialize with correct default values', () => {
      expect(component.timeslotForm).toBeDefined();
      expect(component.timeslotForm.get('fromTime')?.value).toBe('12:15');
      expect(component.timeslotForm.get('toTime')?.value).toBe('12:15');
      expect(component.events).toEqual([]);
      expect(component.selectedEvents).toEqual([]);
    });

    it('should handle projectService subscription with valid response', () => {
      expect(component.ProjectId).toBe('123');
      expect(component.ParentCompanyId).toBe('456');
      expect(component.loader).toBe(true);
    });

    it('should handle projectService subscription with invalid response', () => {
      // Create a new component instance to test different subscription scenarios
      const newProjectServiceMock = {
        projectParent: of(null)
      } as any;

      TestBed.overrideProvider(ProjectService, { useValue: newProjectServiceMock });
      const newFixture = TestBed.createComponent(TimeslotComponent);
      const newComponent = newFixture.componentInstance;

      expect(newComponent.ProjectId).toBeUndefined();
      expect(newComponent.ParentCompanyId).toBeUndefined();
    });
  });

  describe('ngOnInit edge cases', () => {
    it('should handle missing selectedBookingDate', () => {
      component.selectedBookingDate = null;
      component.ngOnInit();

      // Should not throw error
      expect(component.timeslotForm).toBeDefined();
    });

    it('should handle missing ProjectId', () => {
      component.ProjectId = null;
      component.ngOnInit();

      expect(projectSettingsServiceMock.getProjectSettings).not.toHaveBeenCalled();
    });

    it('should handle edit mode with missing required fields', () => {
      component.isEditMode = true;
      component.gateId = null;
      component.LocationId = null;
      component.equipmentId = null;

      const getEventNDRSpy = jest.spyOn(component, 'getEventNDR');
      component.ngOnInit();

      expect(getEventNDRSpy).not.toHaveBeenCalled();
    });

    it('should handle project settings response with missing data', () => {
      projectSettingsServiceMock.getProjectSettings.mockReturnValue(of({
        data: {
          projectSettings: null
        }
      }));

      component.ngOnInit();

      expect(component.startHour).toBe(0);
      expect(component.startMinute).toBe(0);
      expect(component.endHour).toBe(23);
      expect(component.endMinute).toBe(59);
    });
  });

  describe('keyboard navigation edge cases', () => {
    it('should handle Space key for date picker', () => {
      const mockEvent = {
        key: ' ',
        preventDefault: jest.fn()
      } as unknown as KeyboardEvent;

      const openDatePickerSpy = jest.spyOn(component, 'openDatePicker').mockImplementation();
      component.handleDownKeydown(mockEvent, null, 'date');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(openDatePickerSpy).toHaveBeenCalled();
    });

    it('should handle unknown type', () => {
      const mockEvent = {
        key: 'Enter',
        preventDefault: jest.fn()
      } as unknown as KeyboardEvent;

      const openDatePickerSpy = jest.spyOn(component, 'openDatePicker');
      const changeDaySpy = jest.spyOn(component, 'changeDay');

      component.handleDownKeydown(mockEvent, 1, 'unknown');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(openDatePickerSpy).not.toHaveBeenCalled();
      expect(changeDaySpy).not.toHaveBeenCalled();
    });

    it('should not trigger actions for non-Enter/Space keys', () => {
      const mockEvent = {
        key: 'Tab',
        preventDefault: jest.fn()
      } as unknown as KeyboardEvent;

      const openDatePickerSpy = jest.spyOn(component, 'openDatePicker');
      component.handleDownKeydown(mockEvent, null, 'date');

      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      expect(openDatePickerSpy).not.toHaveBeenCalled();
    });
  });

  describe('error handling and edge cases', () => {
    it('should handle missing calendarComponent in changeDay', () => {
      component.calendarComponent = null;
      const getEventNDRSpy = jest.spyOn(component, 'getEventNDR').mockImplementation();

      component.changeDay(1);

      expect(getEventNDRSpy).toHaveBeenCalled();
      // Should not throw error even without calendar component
    });

    it('should handle missing calendarComponent in onDateChange', () => {
      component.calendarComponent = null;
      const mockEvent = { target: { value: '2024-04-15' } };

      // Should not throw error
      expect(() => component.onDateChange(mockEvent)).not.toThrow();
    });

    it('should handle missing closeModal in closePopup', () => {
      component.closeModal = null as any;

      // Should not throw error
      expect(() => component.closePopup()).not.toThrow();
    });

    it('should handle event object without target in onDateChange', () => {
      const mockEvent = { value: '2024-04-15' };
      const setValueSpy = jest.fn();
      jest.spyOn(component.timeslotForm, 'get').mockReturnValue({ setValue: setValueSpy } as any);

      component.onDateChange(mockEvent);

      expect(setValueSpy).toHaveBeenCalledWith('2024-04-15');
    });
  });

  describe('updateSlot edge cases', () => {
    it('should handle empty selectedEvents in non-edit mode', () => {
      component.isEditMode = false;
      component.selectedEvents = [];

      expect(() => component.updateSlot()).toThrow();
    });

    it('should handle missing event in edit mode', () => {
      component.isEditMode = true;
      component.selectedId = 'non-existent';
      component.events = [];

      expect(() => component.updateSlot()).toThrow();
    });
  });

  describe('timeSlotList property', () => {
    it('should have correct time slot list', () => {
      expect(component.timeSlotList).toContain('00:00');
      expect(component.timeSlotList).toContain('12:00');
      expect(component.timeSlotList).toContain('23:45');
      expect(component.timeSlotList).toHaveLength(96); // 24 hours * 4 slots per hour
    });
  });

  describe('calendar options configuration', () => {
    it('should have correct calendar options', () => {
      expect(component.calendarOptions.initialView).toBe('timeGridDay');
      expect(component.calendarOptions.selectable).toBe(true);
      expect(component.calendarOptions.editable).toBe(true);
      expect(component.calendarOptions.slotDuration).toBe('00:15:00');
      expect(component.calendarOptions.allDaySlot).toBe(false);
    });
  });
});
