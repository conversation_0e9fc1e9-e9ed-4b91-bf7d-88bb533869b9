import { ComponentFixture, TestBed } from '@angular/core/testing';
import { InviteLinkComponent } from './invite-link.component';
import { Router } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';

describe('InviteLinkComponent', () => {
  let component: InviteLinkComponent;
  let fixture: ComponentFixture<InviteLinkComponent>;
  let router: Router;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [InviteLinkComponent],
      imports: [RouterTestingModule],
    }).compileComponents();

    fixture = TestBed.createComponent(InviteLinkComponent);
    component = fixture.componentInstance;
    router = TestBed.inject(Router);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with router injected', () => {
    expect(component.router).toBeTruthy();
  });

  describe('submit method', () => {
    it('should navigate to decoded URL when localStorage has url', () => {
      // Mock localStorage
      const mockUrl = '/test-url';
      const encodedUrl = btoa(mockUrl);

      jest.spyOn(localStorage, 'getItem').mockReturnValue(encodedUrl);
      jest.spyOn(window, 'atob').mockReturnValue(mockUrl);
      jest.spyOn(router, 'navigate').mockImplementation(() => Promise.resolve(true));

      component.submit();

      expect(localStorage.getItem).toHaveBeenCalledWith('url');
      expect(window.atob).toHaveBeenCalledWith(encodedUrl);
      expect(router.navigate).toHaveBeenCalledWith([mockUrl]);
    });

    it('should navigate to null when localStorage has no url', () => {
      // Mock localStorage to return null
      jest.spyOn(localStorage, 'getItem').mockReturnValue(null);
      jest.spyOn(router, 'navigate').mockImplementation(() => Promise.resolve(true));

      component.submit();

      expect(localStorage.getItem).toHaveBeenCalledWith('url');
      expect(router.navigate).toHaveBeenCalledWith([null]);
    });

    it('should navigate to null when localStorage returns empty string', () => {
      // Mock localStorage to return empty string
      jest.spyOn(localStorage, 'getItem').mockReturnValue('');
      jest.spyOn(router, 'navigate').mockImplementation(() => Promise.resolve(true));

      component.submit();

      expect(localStorage.getItem).toHaveBeenCalledWith('url');
      expect(router.navigate).toHaveBeenCalledWith([null]);
    });
  });

  describe('ngOnInit', () => {
    it('should not perform any actions', () => {
      // The ngOnInit method is empty, so we just verify it exists and can be called
      expect(() => component.ngOnInit()).not.toThrow();
    });
  });
});
