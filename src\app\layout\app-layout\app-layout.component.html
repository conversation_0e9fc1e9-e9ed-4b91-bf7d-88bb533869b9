<div class="admin-layout">
  <!-- Header -->
  <app-header1 (sidemenuToggle)="sideMenuChange($event)"></app-header1>
  <!-- Header -->
  <!-- Sidemenu -->
  <aside class="own-sidemenu-main" [ngClass]="{ show: !toggleClass, closed: toggleClass }">
    <app-sidemenu></app-sidemenu>
    <app-footer></app-footer>
  </aside>
  <!-- Sidemenu -->

  <!-- Page content -->
  <section
    class="page-content"
    [ngClass]="{ show_pagecontent: !toggleClass, closed_page: toggleClass }"
  >
    <router-outlet></router-outlet>
  </section>
  <!-- Page content -->
</div>

<div class="sidemenu-overlay d-none"></div>

<!-- Delivery Request Notification Card -->
<div class="notification-card" *ngIf="showNotification" (click)="redirection('notification')"  (keydown)="handleDownKeydown($event, 'notification','notify')">
  <div class="card-container cmpny-card orange-card">
    <div class="card-body px-4 py-3">
      <div class="position-relative">
        <div
          class="position-absolute top-minus-3px"
          *ngIf="
            notifyData?.profilePic == null ||
            notifyData?.profilePic == undefined ||
            notifyData?.profilePic == ''
          "
        >
          <img
            src="./assets/images/default-user.svg"
            alt="user-profile"
            class="rounded-circle notification-profile-img"
          />
        </div>
        <div
          class="position-absolute top-minus-3px"
          *ngIf="
            notifyData?.profilePic != null &&
            notifyData?.profilePic != undefined &&
            notifyData?.profilePic != ''
          "
        >
          <img
            src="{{ notifyData?.profilePic }}"
            alt="user-profile"
            class="rounded-circle notification-profile-img"
          />
        </div>
        <div class="ps-5">
          <h1 class="fs12 color-grey4 mb-1 text-truncate w-80">
            {{ notifyData?.firstName }} {{ notifyData?.lastName }}
          </h1>
          <p class="m-0 fs12 color-grey30">{{ notifyData?.createdAt | date : 'medium' }}</p>
        </div>
        <div class="position-absolute close-img" (click)="closePopup()"  (keydown)="handleDownKeydown($event, '','close')">
          <img src="./assets/images/noun_Close.svg" alt="close-img" />
        </div>
      </div>
      <div class="mt-2">
        <p class="m-0 fs12 color-grey4">Project Name- {{ notifyData?.projectName | titlecase }}</p>
        <p class="m-0 fs12 color-grey4">Delivery ID- {{ notifyData?.DeliveryId }}</p>
        <p class="m-0 fs12 color-grey4">{{ notifyData?.description }}</p>
      </div>
    </div>
  </div>
</div>
<!-- Delivery Request Notification Card -->

<!-- Crane Request Notification Card -->
<div
  class="notification-card"
  *ngIf="showCraneRequestNotification"
  (click)="redirection('notification')"  (keydown)="handleDownKeydown($event, 'notification','notify')"
>
  <div class="card-container cmpny-card orange-card">
    <div class="card-body px-4 py-3">
      <div class="position-relative">
        <div
          class="position-absolute top-minus-3px"
          *ngIf="
            notifyCraneRequestData?.profilePic == null ||
            notifyCraneRequestData?.profilePic == undefined ||
            notifyCraneRequestData?.profilePic == ''
          "
        >
          <img
            src="./assets/images/default-user.svg"
            alt="user-profile"
            class="rounded-circle notification-profile-img"
          />
        </div>
        <div
          class="position-absolute top-minus-3px"
          *ngIf="
            notifyCraneRequestData?.profilePic != null &&
            notifyCraneRequestData?.profilePic != undefined &&
            notifyCraneRequestData?.profilePic != ''
          "
        >
          <img
            src="{{ notifyCraneRequestData?.profilePic }}"
            alt="user-profile"
            class="rounded-circle notification-profile-img"
          />
        </div>
        <div class="ps-5">
          <h1 class="fs12 color-grey4 mb-1 text-truncate w-80">
            {{ notifyCraneRequestData?.firstName }} {{ notifyCraneRequestData?.lastName }}
          </h1>
          <p class="m-0 fs12 color-grey30">
            {{ notifyCraneRequestData?.createdAt | date : 'medium' }}
          </p>
        </div>
        <div class="position-absolute close-img" (click)="closePopup1()"  (keydown)="handleDownKeydown($event, '','close1')">
          <img src="./assets/images/noun_Close.svg" alt="close-img" />
        </div>
      </div>
      <div class="mt-2">
        <p class="m-0 fs12 color-grey4">
          Project Name- {{ notifyCraneRequestData?.projectName | titlecase }}
        </p>
        <p class="m-0 fs12 color-grey4">
          Crane Pick ID- {{ notifyCraneRequestData?.CraneRequestId }}
        </p>
        <p class="m-0 fs12 color-grey4">{{ notifyCraneRequestData?.description }}</p>
      </div>
    </div>
  </div>
</div>
<!-- Crane Request Notification Card -->

<!-- Concrete Request Notification Card -->
<div
  class="notification-card"
  *ngIf="showConcreteRequestNotification"
  (click)="redirection('notification')"  (keydown)="handleDownKeydown($event, 'notification','notify')"
>
  <div class="card-container cmpny-card orange-card">
    <div class="card-body px-4 py-3">
      <div class="position-relative">
        <div
          class="position-absolute top-minus-3px"
          *ngIf="
            notifyConcreteRequestData?.profilePic == null ||
            notifyConcreteRequestData?.profilePic == undefined ||
            notifyConcreteRequestData?.profilePic == ''
          "
        >
          <img
            src="./assets/images/default-user.svg"
            alt="user-profile"
            class="rounded-circle notification-profile-img"
          />
        </div>
        <div
          class="position-absolute top-minus-3px"
          *ngIf="
            notifyConcreteRequestData?.profilePic != null &&
            notifyConcreteRequestData?.profilePic != undefined &&
            notifyConcreteRequestData?.profilePic != ''
          "
        >
          <img
            src="{{ notifyConcreteRequestData?.profilePic }}"
            alt="user-profile"
            class="rounded-circle notification-profile-img"
          />
        </div>
        <div class="ps-5">
          <h1 class="fs12 color-grey4 mb-1 text-truncate w-80"> &nbsp;
            <!-- {{notifyConcreteRequestData?.firstName}} {{notifyConcreteRequestData?.lastName}} -->
          </h1>
          <p class="m-0 fs12 color-grey30">
            {{ notifyConcreteRequestData?.createdAt | date : 'medium' }}
          </p>
        </div>
        <div class="position-absolute close-img" (click)="closePopup2()"  (keydown)="handleDownKeydown($event, '', 'close2')">
          <img src="./assets/images/noun_Close.svg" alt="close-img"/>
        </div>
      </div>
      <div class="mt-2">
        <p class="m-0 fs12 color-grey4">
          Project Name- {{ notifyConcreteRequestData?.projectName | titlecase }}
        </p>
        <p class="m-0 fs12 color-grey4">
          Concrete Placement ID- {{ notifyConcreteRequestData?.ConcreteRequestId }}
        </p>
        <p class="m-0 fs12 color-grey4">{{ notifyConcreteRequestData?.title }}</p>
      </div>
    </div>
  </div>
</div>
<!-- Concrete Request Notification Card -->
