import { Component, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-header1',
  templateUrl: './header1.component.html',
  })
export class Header1Component {
  public navbarOpen = false;

  public shouldShow: boolean;

  @Output() private readonly sidemenuToggle = new EventEmitter<boolean>();

  public toggleNavbar(): void {
    this.navbarOpen = !this.navbarOpen;
  }

  public sidebarToggle(): void {
    this.shouldShow = !this.shouldShow;
    this.sidemenuToggle.emit(this.shouldShow);
  }

  public handleToggleKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sidebarToggle();
    }
  }
}
