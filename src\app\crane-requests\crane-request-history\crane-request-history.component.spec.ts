import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of, Subject } from 'rxjs';
import { CraneRequestHistoryComponent } from './crane-request-history.component';
import { DeliveryService } from '../../services/profile/delivery.service';
import { ProjectService } from '../../services/profile/project.service';

describe('CraneRequestHistoryComponent', () => {
  let component: CraneRequestHistoryComponent;
  let fixture: ComponentFixture<CraneRequestHistoryComponent>;
  let deliveryServiceMock: jest.Mocked<DeliveryService>;
  let projectServiceMock: jest.Mocked<ProjectService>;

  const mockProjectResponse = {
    ProjectId: '123',
    ParentCompanyId: '456',
  };

  const mockHistoryResponse = {
    data: [
      {
        type: 'status_change',
        description: 'Status changed to Approved',
        createdAt: '2024-03-20T10:00:00Z',
      },
      {
        type: 'comment',
        description: 'Test comment',
        createdAt: '2024-03-20T11:00:00Z',
      },
    ],
  };

  beforeEach(async () => {
    deliveryServiceMock = {
      EditCraneRequestId: new Subject(),
      fetchData: new Subject(),
      fetchData1: new Subject(),
      getCraneRequestHistory: jest.fn().mockReturnValue(of(mockHistoryResponse)),
      updateCraneRequestHistory: jest.fn(),
    } as any;

    projectServiceMock = {
      projectParent: of(mockProjectResponse),
    } as any;

    await TestBed.configureTestingModule({
      declarations: [CraneRequestHistoryComponent],
      providers: [
        { provide: DeliveryService, useValue: deliveryServiceMock },
        { provide: ProjectService, useValue: projectServiceMock },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CraneRequestHistoryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.historyList).toEqual([]);
    expect(component.loader).toBe(true);
  });

  it('should subscribe to project service and update project details', () => {
    expect(component.ProjectId).toBe(mockProjectResponse.ProjectId);
    expect(component.ParentCompanyId).toBe(mockProjectResponse.ParentCompanyId);
  });

  it('should fetch history when EditCraneRequestId emits', (done) => {
    const craneRequestId = '789';
    component.CraneRequestId = craneRequestId;
    component.ProjectId = mockProjectResponse.ProjectId;
    component.ParentCompanyId = mockProjectResponse.ParentCompanyId;

    deliveryServiceMock.EditCraneRequestId.next(craneRequestId);

    // Use setTimeout to allow for debounceTime to complete
    setTimeout(() => {
      expect(deliveryServiceMock.getCraneRequestHistory).toHaveBeenCalledWith({
        CraneRequestId: craneRequestId,
        ParentCompanyId: mockProjectResponse.ParentCompanyId,
        ProjectId: mockProjectResponse.ProjectId,
      });
      done();
    }, 200); // More than the debounceTime(100) in the component
  });

  it('should filter out comments from history list', (done) => {
    const craneRequestId = '789';
    component.CraneRequestId = craneRequestId;
    component.ProjectId = mockProjectResponse.ProjectId;
    component.ParentCompanyId = mockProjectResponse.ParentCompanyId;

    deliveryServiceMock.EditCraneRequestId.next(craneRequestId);

    // Use setTimeout to allow for debounceTime to complete
    setTimeout(() => {
      expect(component.historyList.length).toBe(1);
      expect(component.historyList[0].type).toBe('status_change');
      done();
    }, 200);
  });

  it('should set loader to false after fetching history', (done) => {
    const craneRequestId = '789';
    component.CraneRequestId = craneRequestId;
    component.ProjectId = mockProjectResponse.ProjectId;
    component.ParentCompanyId = mockProjectResponse.ParentCompanyId;

    deliveryServiceMock.EditCraneRequestId.next(craneRequestId);

    // Use setTimeout to allow for debounceTime to complete
    setTimeout(() => {
      expect(component.loader).toBe(false);
      done();
    }, 200);
  });

  it('should unsubscribe from subscriptions on destroy', () => {
    const unsubscribeSpy = jest.spyOn(component['subscription'], 'unsubscribe');

    component.ngOnDestroy();

    expect(unsubscribeSpy).toHaveBeenCalled();
  });

  it('should handle fetchData trigger', (done) => {
    const craneRequestId = '789';
    component.CraneRequestId = craneRequestId;
    component.ProjectId = mockProjectResponse.ProjectId;
    component.ParentCompanyId = mockProjectResponse.ParentCompanyId;

    deliveryServiceMock.fetchData.next(true);

    // Use setTimeout to allow for debounceTime to complete
    setTimeout(() => {
      expect(deliveryServiceMock.getCraneRequestHistory).toHaveBeenCalled();
      done();
    }, 200);
  });

  it('should handle fetchData1 trigger', (done) => {
    const craneRequestId = '789';
    component.CraneRequestId = craneRequestId;
    component.ProjectId = mockProjectResponse.ProjectId;
    component.ParentCompanyId = mockProjectResponse.ParentCompanyId;

    deliveryServiceMock.fetchData1.next(true);

    // Use setTimeout to allow for debounceTime to complete
    setTimeout(() => {
      expect(deliveryServiceMock.getCraneRequestHistory).toHaveBeenCalled();
      done();
    }, 200);
  });
});
