//Colors
$blue: #0084ff;
$blue2: #0061c6;
$blue3: #7f9bb9;
$blue4: #1031b4;
$blue5: #009ddd;
$blue6: #0078d4;
$blue7: #007bb5;
$blue8: #346ff1;
$blue9: #3b5998;
$blue10: #55acee;
$blue11: #28A2F4;
$blue12:#171D35;
$white: #fff;
$white1: rgba(255, 255, 255, 1);
$black: #000;
$black1: #00000029;
$black2: #25265e1a;
$black3: #2c359319;
$black4: #0000000d;
$black5: rgba(3, 3, 3, 0.4);
$black6: rgba(3, 3, 3, 1);
$black7: #1A1A1A;
$dark-black: #1d1d1d;
$btn-light-blue: #CCE4FF;
$grey: #eaeffd;
$grey2: #cbcbcb;
$grey3: #8b8888;
$grey4: #5b5b5b;
$grey5: #262626;
$grey6: #959595;
$grey7: #292529;
$grey8: #a8b2b9;
$grey9: #f5f5f5;
$grey10: #fcfbfc;
$grey11: #bebebe;
$grey12: #efefef;
$grey13: #fcfcfc;
$grey14: #787993;
$grey15: #555561;
$grey16: #d9d9d9;
$grey17: #575e63;
$grey18: #e6e6e6;
$grey19: #f3f3f3;
$grey20: #2925298b;
$grey21: #a5a5a5;
$grey22: #4a4a4a;
$grey23: #636363;
$grey24: #dedede;
$grey25: #d8d8d8;
$grey26: #9b9b9b;
$grey27: #8a888a;
$grey28: #aaa;
$grey29: #80bdff;
$blue20: #0479FF;
$grey30: #cecece;
$grey31: #2e3039;
$grey32: #242e42;
$grey33: #54617a;
$grey40: #b1b1b1;
$grey41: #2d2d2d;
$grey42: #686868;
$grey43: #8492af;
$grey44: #d7dee366;
$grey45: #d7dee365;
$grey46: #adadad;
$grey47: #888;
$grey48: rgba(168, 178, 185, 0.09);
$grey49: #dcdcdc;
$grey50: #3B4450;
$grey51: #212529;
$dark-grey: #757575;
$dark-grey-1: rgba(117, 117, 117, 0.2);
$gray: #707070;
$gray-a:#BEBEBE;
$gray-b: #E8E5E9;
$gray-c : #ECECEC;
$gray-d : #494949;
$gray-e : #414E63;
$gray-f : #E9ECEF;
$gray-g : #43425d;
$orange: #f45e28;
$orange2: #FFF3EF;
$orange1: rgba(255, 125, 78, 0.2);
$orange3: #FDDFD4;
$orange4:#FEF3EF;
$orange-light: #ff7d4e;
$orange-dark: #F364310D;
$light-grey: #acacac;
$light-grey2: #f4f5f9;
$light-grey3: #cfd5da;
$light-grey4: #ffe8e7;
$light-grey5: #e5fbe5;
$light-grey5: #d7ebf2;
$light-grey6: #ffefd8;
$light-grey7: #d7dae2;
$light-grey8: #ced4da;
$light-grey9:#9C999F;
$grayed-out: #ECEDF5;
$grayed-out-1: #2C3593;

$navy-blue: #013974;
$dark-blue: #25265e;
$dark-blue-1: #2c3593;
$dark-blue-2: #335eea;

$boxshadow: #BD9FBD1D;
$green: #00c507;
$green2: #02a808;
$green3: #00d623;
$green4: #1f0;
$green5: #c4ef12;
$green6: #03de73;
$green7: rgba(0, 214, 35, 0.2);
$green8: #3CBA58;
$green9: #C6F2CE;
$green10: #00BBA1;

$red: #f44336;
$red1: #ff1414;
$red2: #ed5454;
$red3: #ff3939;
$red4: #f52d56;
$red5: #dd4b39;
$red6: #f45e287a;
$red7: #f45e286c;
$red8: rgba(244, 96, 42, 0.2);
$red9: rgba(243, 68, 45, 0.2);
$red10: rgba(255, 20, 20, 0.2);
$red11: #495057;
$red12: #ffd0d0;

$red-color: #f00;
$lightpink: #ffebe4;

$lightorange: #fd9b5b;
$grey27: #68717f;
$yellow: #e1ad01;
$yellow1: #fd7e14;
$yellow2: #FFFBC8;
$yellow3: #FFF5D5;

/*report heat map*/
$rgray: #F0F0F0;
$rorange: #FFE2D8;
$rorange1: #FED2C2;
$rorange2: #FCBEA9;
$rorange3: #F69F81;
$rorange4: #FA916D;
$rorange5: #FB8056;
$rorange6: #F86E3E;
$rorange7: #F15824;
$rorange8: #E64E1A;
$rorange9: #CB3B09;

/* Value */
$value5: 5px;
$value6: 6px;
$value7: 7px;
$value8: 8px;
$value10: 10px;
$value12: 12px;
$value15: 15px;
$value18: 18px;
$value20: 20px;
$value25: 25px;
$value28: 28px;
$value30: 30px;
$value35: 35px;
$value40: 40px;
$value45: 45px;
$value50: 50px;
$value55: 55px;
$value60: 60px;
$value70: 70px;
$value75: 75px;
$value80: 80px;
$value90: 90px;
$value100: 100px;
$value110: 110px;
$value130: 130px;
$value155: 155px;
$value300: 300px;
$value350: 350px;
$value550: 550px;
$rem2: 2rem;
$rem2-5: 2.5rem;
$mnb28: -28px;
$value180: 180px;