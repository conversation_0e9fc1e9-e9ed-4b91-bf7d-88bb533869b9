name: Unit test on devops-dev branch

on:
  pull_request:
    types: [assigned, opened, synchronize, reopened]
    branches: [devops-dev]

jobs:
  build:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x]

    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node-version }}
      - name: Install dependencies
        run: npm install
      - name: Run tests
        if: ${{ always() }}
        run: npm test
      - name: Upload to sonarcube
        if: ${{ always() }}
        run: npm run sonar
