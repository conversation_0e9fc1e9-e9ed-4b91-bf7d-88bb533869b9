import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CalendarComponent } from './calendar.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { CalendarService } from '../services/profile/calendar.service';
import { ProjectService } from '../services/profile/project.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import { Router } from '@angular/router';
import { UntypedFormBuilder } from '@angular/forms';
import { MixpanelService } from '../services/mixpanel.service';
import { Title } from '@angular/platform-browser';
import { of, throwError, BehaviorSubject, tap } from 'rxjs';
import { fakeAsync, tick } from '@angular/core/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import moment from 'moment';

describe('CalendarComponent', () => {
  let component: CalendarComponent;
  let fixture: ComponentFixture<CalendarComponent>;
  let calendarService: jest.Mocked<CalendarService>;
  let projectService: jest.Mocked<ProjectService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let toastrService: jest.Mocked<ToastrService>;
  let modalService: jest.Mocked<BsModalService>;
  let router: jest.Mocked<Router>;
  let socket: any;
  let mixpanelService: jest.Mocked<MixpanelService>;
  let mockCalendarApi: any;
  let mockFullCalendarComponent: any;

  // Add a BehaviorSubject to mock deliveryService.refresh observable
  let refresh1Subject = new BehaviorSubject<any>('');

  beforeEach(async () => {
    refresh1Subject = new BehaviorSubject<any>('');

    const calendarServiceMock = {
      getCalendar: jest.fn(),
      getEventNDR: jest.fn().mockReturnValue(of({
        data: [],
        statusData: { statusColorCode: JSON.stringify([]), useTextColorAsLegend: JSON.stringify(false), isDefaultColor: JSON.stringify(false) },
        cardData: { deliveryCard: JSON.stringify([]) },
        lastId: 1
      }))
    };
    const projectServiceMock = {
      getProject: jest.fn(),
      projectParent: new BehaviorSubject<any>({ ParentCompanyId: 1, ProjectId: 1 }),
      gateList: jest.fn().mockReturnValue(of({ data: [] })),
      listEquipment: jest.fn().mockReturnValue(of({ data: [] })),
      getCompanies: jest.fn().mockReturnValue(of({ data: [] })),
      getDefinableWork: jest.fn().mockReturnValue(of({ data: [] })),
      getLocations: jest.fn().mockReturnValue(of({ data: [] })),
      listAllMember: jest.fn().mockReturnValue(of({ data: [] }))
    };
    const deliveryServiceMock = {
      getDeliveries: jest.fn(),
      refresh1: refresh1Subject.asObservable(),
      getNDRData: jest.fn().mockReturnValue(of({ data: { memberDetails: [], companyDetails: [], gateDetails: [], equipmentDetails: [] } })),
      getCurrentStatus: new BehaviorSubject<any>(''),
      refresh: new BehaviorSubject<any>(''),
      loginUser: new BehaviorSubject<any>({ RoleId: 2 }),
      updatedDeliveryId: jest.fn(),
      updatedCurrentStatus: jest.fn(),
      createVoid: jest.fn().mockReturnValue(of({ message: 'Success' }))
    };

    // Mock calendar API
    mockCalendarApi = {
      next: jest.fn(),
      prev: jest.fn(),
      prevYear: jest.fn(),
      nextYear: jest.fn(),
      changeView: jest.fn(),
      getDate: jest.fn().mockReturnValue(new Date()),
      getView: jest.fn().mockReturnValue({ type: 'dayGridMonth' }),
      currentData: {
        dateProfile: {
          activeRange: {
            start: new Date(),
            end: new Date()
          }
        }
      }
    };

    // Mock FullCalendarComponent
    mockFullCalendarComponent = {
      getApi: jest.fn().mockReturnValue(mockCalendarApi)
    };

    const toastrServiceMock = { success: jest.fn(), error: jest.fn() };
    const modalServiceMock = {
      show: jest.fn().mockReturnValue({
        content: { lastId: null, closeBtnName: '' },
        hide: jest.fn(),
        setClass: jest.fn()
      } as BsModalRef)
    };
    const routerMock = { navigate: jest.fn() };
    const socketMock = { on: jest.fn(), emit: jest.fn() };
    const mixpanelServiceMock = { track: jest.fn() };

    await TestBed.configureTestingModule({
      declarations: [CalendarComponent],
      providers: [
        { provide: CalendarService, useValue: calendarServiceMock },
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: DeliveryService, useValue: deliveryServiceMock },
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: BsModalService, useValue: modalServiceMock },
        { provide: Router, useValue: routerMock },
        { provide: Socket, useValue: socketMock },
        { provide: MixpanelService, useValue: mixpanelServiceMock },
        { provide: Title, useValue: { setTitle: jest.fn() } },
        { provide: BsModalRef, useValue: { hide: jest.fn() } },
        UntypedFormBuilder
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    calendarService = TestBed.inject(CalendarService) as jest.Mocked<CalendarService>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    modalService = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    router = TestBed.inject(Router) as jest.Mocked<Router>;
    socket = TestBed.inject(Socket);
    mixpanelService = TestBed.inject(MixpanelService) as jest.Mocked<MixpanelService>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CalendarComponent);
    component = fixture.componentInstance;

    // Mock the ViewChild directly on the component instance
    (component as any).calendarComponent1 = mockFullCalendarComponent;

    // Initialize calendarApi
    component.calendarApi = mockCalendarApi;

    // Initialize modalRef
    component.modalRef = {
      hide: jest.fn(),
      setClass: jest.fn(),
      content: {
        lastId: 1,
        closeBtnName: 'Close',
        seriesOption: 1,
        recurrenceId: null,
        recurrenceEndDate: null
      }
    } as BsModalRef;

    component.modalRef1 = {
      hide: jest.fn(),
      setClass: jest.fn()
    } as BsModalRef;

    // Initialize component properties
    component.ProjectId = 1;
    component.ParentCompanyId = 1;
    component.authUser = { RoleId: 2, id: 1 };
    component.Range = {
      start: new Date('2024-01-01'),
      end: new Date('2024-01-31')
    };

    // Initialize filterForm with all required controls
    const formBuilder = TestBed.inject(UntypedFormBuilder);
    component.filterForm = formBuilder.group({
      companyFilter: [''],
      descriptionFilter: [''],
      statusFilter: [''],
      memberFilter: [''],
      gateFilter: [''],
      equipmentFilter: [''],
      locationFilter: [''],
      dateFilter: [''],
      pickFrom: [''],
      pickTo: ['']
    });

    // Initialize test data
    component.deliveryList = [{
      id: 1,
      uniqueNumber: 'DEL001',
      requestType: 'deliveryRequest',
      description: 'Test Delivery',
      status: 'Approved',
      deliveryStart: new Date(),
      deliveryEnd: new Date(),
      companyDetails: [{ Company: { companyName: 'Test Company' } }],
      memberDetails: [{ Member: { User: { firstName: 'John', lastName: 'Doe' } } }],
      gateDetails: [{ Gate: { gateName: 'Gate 1' } }],
      equipmentDetails: [{ Equipment: { equipmentName: 'Equipment 1' } }],
      approved_at: new Date(),
      recurrence: { id: 1, recurrenceEndDate: new Date(), recurrence: 'Daily' }
    }];

    component.eventData = {
      id: 1,
      requestType: 'deliveryRequest',
      startDate: moment().format('MM/DD/YYYY'),
      endDate: moment().toDate(),
      status: 'Approved',
      edit: true
    };

    // Mock methods that cause issues
    jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});
    jest.spyOn(component, 'setCalendar').mockImplementation(() => {});

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.events).toEqual([]);
    expect(component.loader).toBe(true);
    expect(component.search).toBe('');
    expect(component.showStatus).toBe(false);
    expect(component.descriptionPopup).toBe(false);
    expect(component.calendarDescriptionPopup).toBe(false);
    expect(component.filterCount).toBe(0);
    expect(component.voidSubmitted).toBe(false);
    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
    expect(component.statusSubmitted).toBe(false);
    expect(component.modalLoader).toBe(false);
    expect(component.showSearchbar).toBe(false);
    expect(component.allRequestIsOpened).toBe(false);
    expect(component.currentView).toBe('Month');
  });

  it('should initialize calendar options', () => {
    expect(component.calendarOptions).toBeDefined();
    expect(component.calendarOptions.selectable).toBe(true);
    expect(component.calendarOptions.initialView).toBe('dayGridMonth');
    expect(component.calendarOptions.aspectRatio).toBe(2);
    expect(component.calendarOptions.nowIndicator).toBe(true);
    expect(component.calendarOptions.expandRows).toBe(true);
  });

  it('should set title on initialization', () => {
    const titleService = TestBed.inject(Title);
    expect(titleService.setTitle).toHaveBeenCalledWith('Follo - Delivery Calendar');
  });

  describe('Calendar Navigation', () => {
    beforeEach(() => {
      jest.spyOn(component, 'setCalendar').mockImplementation(() => {});
      jest.spyOn(component, 'closeDescription').mockImplementation(() => {});
    });

    it('should navigate to next month', () => {
      component.goNext();
      expect(mockCalendarApi.next).toHaveBeenCalled();
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should navigate to previous month', () => {
      component.goPrev();
      expect(mockCalendarApi.prev).toHaveBeenCalled();
      expect(component.closeDescription).toHaveBeenCalled();
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should navigate to previous year', () => {
      component.goPrevYear();
      expect(mockCalendarApi.prevYear).toHaveBeenCalled();
      expect(component.closeDescription).toHaveBeenCalled();
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should navigate to next year', () => {
      component.goNextYear();
      expect(mockCalendarApi.nextYear).toHaveBeenCalled();
      expect(component.closeDescription).toHaveBeenCalled();
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should change to week view', () => {
      component.goTimeGridWeekOrDay('timeGridWeek');
      expect(component.currentView).toBe('Week');
      expect(mockCalendarApi.changeView).toHaveBeenCalledWith('timeGridWeek');
      expect(component.closeDescription).toHaveBeenCalled();
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should change to day view', () => {
      component.goTimeGridWeekOrDay('timeGridDay');
      expect(component.currentView).toBe('Day');
      expect(mockCalendarApi.changeView).toHaveBeenCalledWith('timeGridDay');
      expect(component.closeDescription).toHaveBeenCalled();
      expect(component.setCalendar).toHaveBeenCalled();
    });

    it('should change to month view', () => {
      component.goDayGridMonth();
      expect(component.currentView).toBe('Month');
      expect(mockCalendarApi.changeView).toHaveBeenCalledWith('dayGridMonth');
      expect(component.closeDescription).toHaveBeenCalled();
      expect(component.setCalendar).toHaveBeenCalled();
    });
  });

  describe('Search and Filter', () => {
    it('should handle search with data', () => {
      const searchData = 'test search';
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});
      component.getSearchNDR(searchData);
      expect(component.search).toBe(searchData);
      expect(component.showSearchbar).toBe(true);
      expect(component.getEventNDR).toHaveBeenCalled();
    });

    it('should handle search with empty data', () => {
      const searchData = '';
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});
      component.getSearchNDR(searchData);
      expect(component.search).toBe(searchData);
      expect(component.showSearchbar).toBe(false);
      expect(component.getEventNDR).toHaveBeenCalled();
    });

    it('should clear search', () => {
      component.showSearchbar = true;
      component.search = 'test';
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});
      component.clear();
      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
      expect(component.getEventNDR).toHaveBeenCalled();
    });

    it('should reset filter', () => {
      component.filterCount = 5;
      component.search = 'test';
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});
      jest.spyOn(component, 'filterDetailsForm').mockImplementation(() => {});
      component.resetFilter();
      expect(component.filterCount).toBe(0);
      expect(component.search).toBe('');
      expect(component.filterDetailsForm).toHaveBeenCalled();
      expect(component.getEventNDR).toHaveBeenCalled();
      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should submit filter with all fields filled', () => {
      component.filterForm.patchValue({
        descriptionFilter: 'test',
        dateFilter: new Date(),
        companyFilter: '1',
        memberFilter: '1',
        gateFilter: '1',
        equipmentFilter: '1',
        locationFilter: 'location',
        statusFilter: 'Approved',
        pickFrom: new Date(),
        pickTo: new Date()
      });
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});
      component.filterSubmit();
      expect(component.filterCount).toBe(10);
      expect(component.getEventNDR).toHaveBeenCalled();
      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should submit filter with no fields filled', () => {
      // Reset form to empty values
      component.filterForm.patchValue({
        descriptionFilter: '',
        dateFilter: '',
        companyFilter: '',
        memberFilter: '',
        gateFilter: '',
        equipmentFilter: '',
        locationFilter: '',
        statusFilter: '',
        pickFrom: '',
        pickTo: ''
      });
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});
      component.filterSubmit();
      expect(component.filterCount).toBe(0);
      expect(component.getEventNDR).toHaveBeenCalled();
      expect(component.modalRef.hide).toHaveBeenCalled();
    });
  });

  describe('Filter and Search', () => {
    // it('should reset filter', () => {
    //   component.filterCount = 5;
    //   component.filterForm.get('search').setValue('test');
    //   component.resetFilter();
    //   expect(component.filterCount).toBe(0);
    //   expect(component.filterForm.get('search')).toBe('');
    //   expect(component.modalRef.hide).toHaveBeenCalled();
    // });

    // it('should handle search', () => {
    //   const searchData = { search: 'test' };
    //   component.getSearchNDR(searchData);
    //   expect(component.search).toBe(searchData.search);
    // });
  });

  describe('Modal Operations', () => {
    beforeEach(() => {
      jest.spyOn(component, 'calendarGetOverAllGate').mockImplementation(() => {});
      jest.spyOn(component, 'closeDescription').mockImplementation(() => {});
      jest.spyOn(component, 'close').mockImplementation(() => {});
    });

    it('should open add delivery request modal', () => {
      const dateArg = { dateStr: '2024-03-20' };
      component.openAddDeliveryRequestModal(dateArg);
      expect(modalService.show).toHaveBeenCalled();
      expect(component.modalRef.content.lastId).toBeDefined();
      expect(component.modalRef.content.closeBtnName).toBe('Close');
    });

    it('should open add NDR modal', () => {
      component.openAddNDRModal();
      expect(modalService.show).toHaveBeenCalled();
      expect(component.modalRef.content.lastId).toBeDefined();
      expect(component.modalRef.content.closeBtnName).toBe('Close');
    });

    it('should open edit modal', () => {
      const item = { id: 1, recurrence: { id: 1, recurrenceEndDate: new Date() } };
      const action = 1;
      component.openEditModal(item, action);
      expect(component.closeDescription).toHaveBeenCalled();
      expect(deliveryService.updatedDeliveryId).toHaveBeenCalledWith(1);
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should open edit modal without recurrence', () => {
      const item = { id: 1 };
      const action = 1;
      component.openEditModal(item, action);
      expect(modalService.show).toHaveBeenCalled();
      expect(component.modalRef.content.seriesOption).toBe(1);
      expect(component.modalRef.content.recurrenceId).toBeNull();
    });

    it('should open ID modal', () => {
      const item: any = { id: 1 };
      component.openIdModal(item);
      expect(item.ParentCompanyId).toBe(1);
      expect(deliveryService.updatedCurrentStatus).toHaveBeenCalledWith(1);
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should open filter modal', () => {
      const template = {} as any;
      component.openFilterModal(template);
      expect(component.calendarGetOverAllGate).toHaveBeenCalled();
      expect(modalService.show).toHaveBeenCalledWith(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-sm filter-popup custom-modal'
      });
    });

    it('should open delete modal', () => {
      const template = {} as any;
      component.openDeleteModal(template);
      expect(modalService.show).toHaveBeenCalledWith(template);
    });

    it('should open confirmation modal', () => {
      const template = {} as any;
      component.openModal(template);
      expect(modalService.show).toHaveBeenCalledWith(template, {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
      });
    });

    it('should close modal', () => {
      // Reset the spy to allow actual implementation
      jest.spyOn(component, 'close').mockRestore();
      component.submitted = true;
      component.formSubmitted = true;
      component.close();
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should open content modal', () => {
      component.modalLoader = true;
      component.openContentModal();
      expect(component.modalLoader).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should show error message', () => {
      const error = { message: { details: [{ message: 'Test Error' }] } };
      component.showError(error);
      expect(toastrService.error).toHaveBeenCalledWith(['Test Error']);
    });
  });

  describe('Data Loading', () => {
    it('should load events successfully', fakeAsync(() => {
      const mockResponse = {
        data: [{
          id: 1,
          uniqueNumber: 'DEL001',
          requestType: 'deliveryRequest',
          description: 'Test Delivery',
          status: 'Approved',
          deliveryStart: new Date(),
          deliveryEnd: new Date(),
          companyDetails: [{ Company: { companyName: 'Test Company' } }],
          memberDetails: [{ Member: { User: { firstName: 'John', lastName: 'Doe' } } }],
          gateDetails: [{ Gate: { gateName: 'Gate 1' } }],
          equipmentDetails: [{ Equipment: { equipmentName: 'Equipment 1' } }],
          approved_at: new Date(),
          recurrence: { id: 1, recurrenceEndDate: new Date(), recurrence: 'Daily' }
        }],
        statusData: { statusColorCode: JSON.stringify([{ status: 'approved', backgroundColor: '#green', fontColor: '#white' }]), useTextColorAsLegend: JSON.stringify(false), isDefaultColor: JSON.stringify(false) },
        cardData: { deliveryCard: JSON.stringify([]) },
        lastId: 1
      };
      calendarService.getEventNDR.mockReturnValue(of(mockResponse));

      component.getEventNDR();
      tick(); // Wait for async operations to complete

      expect(component.loader).toBe(true);
      // Use a custom matcher that ignores Date precision
    }));

    // it('should handle error when loading events', fakeAsync(() => {
    //   const error = { message: 'Failed to load' };
    //   calendarService.getEventNDR.mockReturnValue(throwError(() => error));

    //   component.getEventNDR();
    //   tick();  // simulate async passage

    //   expect(toastrService.error).toHaveBeenCalledWith(error.message);
    // }));

    it('should get events', () => {
      const mockResponse = {
        data: [{ id: 1, title: 'Test Event', requestType: 'deliveryRequest', status: 'Approved' }],
        statusData: { statusColorCode: JSON.stringify([{ status: 'approved', backgroundColor: '#green', fontColor: '#white' }]), useTextColorAsLegend: JSON.stringify(false), isDefaultColor: JSON.stringify(false) },
        cardData: { deliveryCard: JSON.stringify([]) },
        lastId: 1
      };
      calendarService.getEventNDR.mockReturnValue(of(mockResponse));
      component.getEventNDR();
      expect(component.loader).toBe(true);
    });

    it('should handle error when loading events', () => {
      calendarService.getEventNDR.mockReturnValue(throwError(() => new Error('API Error')));
      component.getEventNDR();
      // Since there's no error handling in getEventNDR, loader remains true
      expect(component.loader).toBe(true);
    });

    it('should get members', () => {
      component.getMembers();
      expect(projectService.listAllMember).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 1
      });
    });

    it('should get companies for calendar', () => {
      jest.spyOn(component, 'calendarGetDefinable').mockImplementation(() => {});
      component.calendarGetCompany();
      expect(projectService.getCompanies).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 1
      });
      expect(component.calendarGetDefinable).toHaveBeenCalled();
    });

    it('should get gates', () => {
      jest.spyOn(component, 'calendarGetOverAllEquipment').mockImplementation(() => {});
      component.calendarGetOverAllGate();
      expect(component.modalLoader).toBe(true);
      expect(projectService.gateList).toHaveBeenCalledWith(
        {
          ProjectId: 1,
          pageSize: 0,
          pageNo: 0,
          ParentCompanyId: 1
        },
        {
          isFilter: true,
          showActivatedAlone: true
        }
      );
      expect(component.calendarGetOverAllEquipment).toHaveBeenCalled();
    });

    it('should get equipment', () => {
      jest.spyOn(component, 'calendarGetCompany').mockImplementation(() => {});
      component.calendarGetOverAllEquipment();
      expect(projectService.listEquipment).toHaveBeenCalledWith(
        {
          ProjectId: 1,
          pageSize: 0,
          pageNo: 0,
          ParentCompanyId: 1
        },
        {
          isFilter: true,
          showActivatedAlone: true
        }
      );
      expect(component.calendarGetCompany).toHaveBeenCalled();
    });

    it('should get definable work', () => {
      jest.spyOn(component, 'getLocations').mockImplementation(() => {});
      component.calendarGetDefinable();
      expect(projectService.getDefinableWork).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 1
      });
      expect(component.getLocations).toHaveBeenCalled();
    });

    it('should get locations', () => {
      jest.spyOn(component, 'openContentModal').mockImplementation(() => {});
      component.getLocations();
      expect(projectService.getLocations).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 1
      });
      expect(component.openContentModal).toHaveBeenCalled();
    });
  });

  describe('Event Description and Calendar Events', () => {
    beforeEach(() => {
      jest.spyOn(component, 'getNDR').mockImplementation(() => {});
      jest.spyOn(component, 'openIdModal').mockImplementation(() => {});
      jest.spyOn(component, 'calendarDescription').mockImplementation(() => {});
      jest.spyOn(component, 'occurMessage').mockImplementation(() => {});
    });

    it('should handle delivery description for delivery request', () => {
      const arg = {
        event: {
          id: '1',
          extendedProps: {
            uniqueNumber: 'DEL001'
          }
        }
      };
      component.deliveryDescription(arg);
      expect(component.eventData).toBeDefined();
      expect(component.calendarCurrentDeliveryIndex).toBe(0);
      expect(component.getNDR).toHaveBeenCalledWith(arg);
      expect(component.openIdModal).toHaveBeenCalled();
    });

    it('should handle delivery description for calendar event', () => {
      component.deliveryList = [{
        id: 1,
        requestType: 'calendarEvent',
        description: 'Test Event'
      }];

      // Mock the findIndex to return a valid index
      jest.spyOn(component.deliveryList, 'findIndex').mockReturnValue(0);

      const arg = {
        event: {
          id: '1',
          title: 'Test Event',
          extendedProps: {
            uniqueNumber: 'CAL001'
          }
        }
      };

      component.deliveryDescription(arg);
      expect(component.calendarDescription).toHaveBeenCalledWith(arg);
    });

    it('should handle calendar description', () => {
      // Setup the component state properly
      component.events = [{
        description: 'Test Event',
        uniqueNumber: 'CAL001'
      }];
      component.deliveryList = [{
        id: 1,
        description: 'Test Event',
        uniqueNumber: 'CAL001',
        repeatEveryType: 'Day'
      }];

      // Mock the occurMessage function
      jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

      const arg = {
        event: {
          title: 'Test Event',
          extendedProps: {
            uniqueNumber: 'CAL001'
          }
        }
      };

      // Reset the state to ensure test is accurate
      component.calendarDescriptionPopup = false;

      component.calendarDescription(arg);
      expect(component.calendarDescriptionPopup).toBe(false);
    });

    it('should close calendar description', () => {
      component.calendarDescriptionPopup = true;
      component.descriptionPopup = true;
      component.allRequestIsOpened = true;
      component.viewEventData = 'test';
      component.closeCalendarDescription();
      expect(component.calendarDescriptionPopup).toBe(false);
      expect(component.descriptionPopup).toBe(false);
      expect(component.allRequestIsOpened).toBe(false);
      expect(component.viewEventData).toBe('');
    });

    it('should close description', () => {
      component.descriptionPopup = true;
      component.closeDescription();
      expect(component.descriptionPopup).toBe(false);
    });
  });

  describe('Status Operations', () => {
    it('should set status for different roles', () => {
      component.authUser = { RoleId: 1 };
      const item = { id: 1, status: 'Pending' };
      component.setStatus(item);
      expect(component.deliveryId).toBe(-1);
      expect(deliveryService.updatedDeliveryId).toHaveBeenCalledWith(1);
    });

    it('should get responsible people acronym', () => {
      const user = { firstName: 'John', lastName: 'Doe' };
      const result = component.getResponsiblePeople(user);
      expect(result).toBe('JD');
    });

    it('should handle responsible people with no name', () => {
      const user = {};
      const result = component.getResponsiblePeople(user);
      expect(result).toBe('UU');
    });

    it('should format date correctly', () => {
      const testDate = new Date('2024-03-20');
      const result = component.changeFormat(testDate);
      expect(result).toContain('03/20/2024');
    });

    it('should handle date formatting with null', () => {
      const result = component.changeFormat(null);
      expect(result).toBeUndefined();
    });

    it('should check future date correctly', () => {
      const futureStart = new Date();
      futureStart.setDate(futureStart.getDate() + 1);
      const futureEnd = new Date();
      futureEnd.setDate(futureEnd.getDate() + 2);
      const result = component.checkFutureDate(futureStart, futureEnd);
      expect(result).toBe(true);
    });

    it('should check past date correctly', () => {
      const pastStart = new Date();
      pastStart.setDate(pastStart.getDate() - 2);
      const pastEnd = new Date();
      pastEnd.setDate(pastEnd.getDate() - 1);
      const result = component.checkFutureDate(pastStart, pastEnd);
      expect(result).toBe(false);
    });
  });

  describe('Utility Functions', () => {
    it('should handle error messages', () => {
      const error = { message: { details: [{ message: 'Test Error' }] } };
      component.showError(error);
      expect(toastrService.error).toHaveBeenCalledWith(['Test Error']);
    });

    it('should handle error with status code 400', () => {
      const error = { message: { statusCode: 400, details: [{ error: 'Validation Error' }] } };
      component.showError(error);
      expect(toastrService.error).toHaveBeenCalledWith(['Validation Error']);
    });

  });

  describe('Date and Time Utilities', () => {
    it('should convert start time correctly', () => {
      const deliveryDate = new Date('2024-03-20');
      const startHours = 10;
      const startMinutes = 30;
      const result = component.convertStart(deliveryDate, startHours, startMinutes);
      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
    });

    it('should select status correctly', () => {
      const status = 'Approved';
      component.selectStatus(status);
      expect(component.statusSubmitted).toBe(false);
    });
  });

  describe('Void Operations', () => {
    beforeEach(() => {
      component.calendarCurrentDeliveryIndex = 0;
      component.deliveryList = [{
        id: 1,
        uniqueNumber: 'DEL001',
        requestType: 'deliveryRequest',
        description: 'Test Delivery',
        status: 'Approved'
      }];
    });

    it('should move to void list successfully', () => {
      component.voidSubmitted = false;
      jest.spyOn(component, 'closeDescription').mockImplementation(() => {});
      jest.spyOn(component, 'close').mockImplementation(() => {});
      component.moveToVoidList();
      expect(component.voidSubmitted).toBe(true);
      expect(deliveryService.createVoid).toHaveBeenCalledWith({
        DeliveryRequestId: 1,
        ProjectId: 1
      });
    });

    it('should not move to void list if already submitted', () => {
      component.voidSubmitted = true;
      jest.clearAllMocks();
      component.moveToVoidList();
      expect(deliveryService.createVoid).not.toHaveBeenCalled();
    });

    it('should handle void confirmation - no action', () => {
      component.voidConfirmationResponse('no');
      expect(component.modalRef1.hide).toHaveBeenCalled();
    });

    it('should handle void confirmation - yes action', () => {
      jest.spyOn(component, 'moveToVoidList').mockImplementation(() => {});
      component.voidConfirmationResponse('yes');
      expect(component.modalRef1.hide).toHaveBeenCalled();
      expect(component.moveToVoidList).toHaveBeenCalled();
    });
  });

  describe('Calendar Setup and Event Loading', () => {
    it('should set calendar and get event NDR', () => {
      jest.spyOn(component, 'setCalendar').mockImplementation(() => {
        if (component.calendarApi) {
          component.Range = component.calendarApi.currentData.dateProfile.activeRange;
          component.getEventNDR();
        }
      });
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});

      component.setCalendar();
      expect(component.calendarApi).toBeDefined();
      expect(component.Range).toBeDefined();
      expect(component.getEventNDR).toHaveBeenCalled();
    });

    it('should get event NDR with project data', () => {
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {
        if (component.ProjectId && component.ParentCompanyId) {
          component.loader = true;
          calendarService.getEventNDR({}, {}).subscribe();
        }
      });

      component.getEventNDR();
      expect(component.loader).toBe(true);
      expect(calendarService.getEventNDR).toHaveBeenCalled();
    });

    it('should not get event NDR without project data', () => {
      jest.clearAllMocks();
      component.ProjectId = null;
      component.ParentCompanyId = null;

      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {
        if (!component.ProjectId || !component.ParentCompanyId) {
          return;
        }
        calendarService.getEventNDR({}, {}).subscribe();
      });

      component.getEventNDR();
      expect(calendarService.getEventNDR).not.toHaveBeenCalled();
    });
  });

  describe('Component Lifecycle and Subscriptions', () => {
    it('should handle project parent subscription', () => {
      const projectData = { ParentCompanyId: 2, ProjectId: 2 };
      projectService.projectParent.next(projectData);
      expect(component.ParentCompanyId).toBe(2);
      expect(component.ProjectId).toBe(2);
    });

    it('should handle login user subscription for different roles', () => {
      const userData = { RoleId: 1, id: 1 };
      deliveryService.loginUser.next(userData);
      expect(component.authUser).toEqual(userData);
    });

    it('should handle delivery refresh subscription', () => {
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});
      deliveryService.refresh.next('refresh');
      expect(component.getEventNDR).toHaveBeenCalled();
    });
  });

  describe('Filter Details Form', () => {
    it('should create filter details form', () => {
      component.filterDetailsForm();
      expect(component.filterForm).toBeDefined();
      expect(component.filterForm.get('companyFilter')).toBeDefined();
      expect(component.filterForm.get('descriptionFilter')).toBeDefined();
      expect(component.filterForm.get('statusFilter')).toBeDefined();
      expect(component.filterForm.get('memberFilter')).toBeDefined();
      expect(component.filterForm.get('gateFilter')).toBeDefined();
      expect(component.filterForm.get('equipmentFilter')).toBeDefined();
      expect(component.filterForm.get('locationFilter')).toBeDefined();
      expect(component.filterForm.get('dateFilter')).toBeDefined();
      expect(component.filterForm.get('pickFrom')).toBeDefined();
      expect(component.filterForm.get('pickTo')).toBeDefined();
    });
  });

  describe('Calendar Options Event Handlers', () => {
    it('should handle date click', () => {
      jest.spyOn(component, 'openAddDeliveryRequestModal').mockImplementation(() => {});
      const arg: any = { dateStr: '2024-03-20' };
      component.calendarOptions.dateClick(arg);
      expect(component.openAddDeliveryRequestModal).toHaveBeenCalledWith(arg);
    });

    it('should handle event click', () => {
      jest.spyOn(component, 'deliveryDescription').mockImplementation(() => {});
      const arg: any = {
        event: {
          id: '1',
          extendedProps: {
            uniqueNumber: 'DEL001'
          }
        }
      };
      component.calendarOptions.eventClick(arg);
      expect(component.eventData).toEqual([]);
      expect(component.deliveryDescription).toHaveBeenCalledWith(arg);
    });

    it('should handle dates set', () => {
      const arg: any = { view: { title: 'March 2024' } };
      component.calendarOptions.datesSet(arg);
      expect(component.currentViewMonth).toBe('March 2024');
    });

    it('should handle event did mount configuration', () => {
      expect(component.calendarOptions.eventDidMount).toBeDefined();
      expect(typeof component.calendarOptions.eventDidMount).toBe('function');
    });
  });

  describe('checkRequestType Method', () => {
    it('should handle Description label', () => {
      const previewSelected = [{ label: 'Description', line: 1 }];
      const assignData = { description: '', line2: '' };
      const element = { description: 'Test Description' };

      const result = component.checkRequestType(previewSelected, assignData, element);
      expect(result.description).toBe('Test Description');
    });

    it('should handle Responsible Company label', () => {
      const previewSelected = [{ label: 'Responsible Company', line: 1 }];
      const assignData = { description: '', line2: '' };
      const element = {
        companyDetails: [{ Company: { companyName: 'Test Company' } }]
      };

      const result = component.checkRequestType(previewSelected, assignData, element);
      expect(result.description).toBe('Test Company');
    });

    it('should handle Responsible Person label', () => {
      const previewSelected = [{ label: 'Responsible Person', line: 2 }];
      const assignData = { description: '', line2: '' };
      const element = {
        memberDetails: [{ Member: { User: { firstName: 'John', lastName: 'Doe' } } }]
      };

      const result = component.checkRequestType(previewSelected, assignData, element);
      expect(result.line2).toBe('John Doe');
    });

    it('should handle Gate label', () => {
      const previewSelected = [{ label: 'Gate', line: 1 }];
      const assignData = { description: '', line2: '' };
      const element = {
        gateDetails: [{ Gate: { gateName: 'Gate A' } }]
      };

      const result = component.checkRequestType(previewSelected, assignData, element);
      expect(result.description).toBe('Gate A');
    });

    it('should handle Delivery ID label', () => {
      const previewSelected = [{ label: 'Delivery ID', line: 1 }];
      const assignData = { description: '', line2: '' };
      const element = { DeliveryId: 'DEL123' };

      const result = component.checkRequestType(previewSelected, assignData, element);
      expect(result.description).toBe('DEL123');
    });

    it('should handle Definable Feature Of Work label', () => {
      const previewSelected = [{ label: 'Definable Feature Of Work', line: 1 }];
      const assignData = { description: '', line2: '' };
      const element = {
        defineWorkDetails: [{ DeliverDefineWork: { DFOW: 'Test DFOW' } }]
      };

      const result = component.checkRequestType(previewSelected, assignData, element);
      expect(result.description).toBe('Test DFOW');
    });

    it('should handle Equipment label', () => {
      const previewSelected = [{ label: 'Equipment', line: 1 }];
      const assignData = { description: '', line2: '' };
      const element = {
        equipmentDetails: [{ Equipment: { equipmentName: 'Crane' } }]
      };

      const result = component.checkRequestType(previewSelected, assignData, element);
      expect(result.description).toBe('Crane');
    });

    it('should handle unknown label', () => {
      const previewSelected = [{ label: 'Unknown Label', line: 1 }];
      const assignData = { description: '', line2: '' };
      const element = {};

      const result = component.checkRequestType(previewSelected, assignData, element);
      expect(result.description).toBe('');
    });

    it('should handle missing nested properties', () => {
      const previewSelected = [{ label: 'Responsible Company', line: 1 }];
      const assignData = { description: '', line2: '' };
      const element = { companyDetails: [] };

      const result = component.checkRequestType(previewSelected, assignData, element);
      expect(result.description).toBe('');
    });

    it('should handle previewSelected with empty array', () => {
      const previewSelected = [];
      const assignData = { description: '', line2: '' };
      const element = {};
      const result = component.checkRequestType(previewSelected, assignData, element);
      expect(result.description).toBe('');
      expect(result.line2).toBe('');
    });
    it('should handle previewSelected with unknown line', () => {
      const previewSelected = [{ label: 'Description', line: 3 }];
      const assignData = { description: '', line2: '' };
      const element = { description: 'Test' };
      const result = component.checkRequestType(previewSelected, assignData, element);
      expect(result.description).toBe('');
      expect(result.line2).toBe('');
    });
  });

// Additional edge case tests for getEventNDR, modal, and utility methods

describe('getEventNDR edge cases', () => {
  beforeEach(() => {
    component.ProjectId = 1;
    component.ParentCompanyId = 1;
    component.Range = {
      start: new Date('2024-01-01'),
      end: new Date('2024-01-31')
    };
    component.filterForm = TestBed.inject(UntypedFormBuilder).group({
      companyFilter: [''],
      descriptionFilter: [''],
      statusFilter: [''],
      memberFilter: [''],
      gateFilter: [''],
      equipmentFilter: [''],
      locationFilter: [''],
      dateFilter: [''],
      pickFrom: [''],
      pickTo: ['']
    });
    component.search = '';
    component.filterCount = 0;
    component.currentView = 'Month';
  });

  it('should handle getEventNDR with malformed statusData', () => {
    const mockResponse = {
      data: [],
      statusData: {
        statusColorCode: 'not-json',
        useTextColorAsLegend: 'not-json',
        isDefaultColor: 'not-json'
      },
      cardData: { deliveryCard: 'not-json' },
      lastId: 1
    };
    calendarService.getEventNDR.mockReturnValue(of(mockResponse));
    expect(() => component.getEventNDR()).not.toThrow();
  });

  it('should handle getEventNDR with missing statusData/cardData', () => {
    const mockResponse = {
      data: [],
      statusData: {},
      cardData: {},
      lastId: 1
    };
    calendarService.getEventNDR.mockReturnValue(of(mockResponse));
    expect(() => component.getEventNDR()).not.toThrow();
  });

  it('should handle getEventNDR with missing statuses', () => {
    const mockResponse = {
      data: [{ status: 'Unknown' }],
      statusData: {
        statusColorCode: JSON.stringify([]),
        useTextColorAsLegend: JSON.stringify(false),
        isDefaultColor: JSON.stringify(false)
      },
      cardData: { deliveryCard: JSON.stringify([]) },
      lastId: 1
    };
    calendarService.getEventNDR.mockReturnValue(of(mockResponse));
    expect(() => component.getEventNDR()).not.toThrow();
  });

  it('should handle getEventNDR with previewSelected as empty', () => {
    const mockResponse = {
      data: [{ status: 'Pending', requestType: 'deliveryRequest' }],
      statusData: {
        statusColorCode: JSON.stringify([{ status: 'pending', backgroundColor: '#ccc', fontColor: '#000' }]),
        useTextColorAsLegend: JSON.stringify(false),
        isDefaultColor: JSON.stringify(false)
      },
      cardData: { deliveryCard: JSON.stringify([]) },
      lastId: 1
    };
    calendarService.getEventNDR.mockReturnValue(of(mockResponse));
    expect(() => component.getEventNDR()).not.toThrow();
  });
});

describe('modal and utility edge cases', () => {
  it('should handle openContentModal', () => {
    component.modalLoader = true;
    component.openContentModal();
    expect(component.modalLoader).toBe(false);
  });
  it('should handle openDeleteModal with undefined template', () => {
    expect(() => component.openDeleteModal(undefined)).not.toThrow();
  });
  it('should handle openFilterModal with undefined template', () => {
    jest.spyOn(component, 'calendarGetOverAllGate').mockImplementation(() => {});
    expect(() => component.openFilterModal(undefined)).not.toThrow();
  });
  it('should handle openModal with undefined template', () => {
    expect(() => component.openModal(undefined)).not.toThrow();
  });
  it('should handle close when modalRef is undefined', () => {
    component.modalRef = undefined;
    expect(() => component.close()).not.toThrow();
  });
  it('should handle voidConfirmationResponse with undefined modalRef1', () => {
    component.modalRef1 = undefined;
    expect(() => component.voidConfirmationResponse('no')).not.toThrow();
    expect(() => component.voidConfirmationResponse('yes')).not.toThrow();
  });
});

  describe('occurMessage Method', () => {
    it('should handle Day repeat type', () => {
      const data = {
        repeatEveryType: 'Day',
        endTime: new Date('2024-12-31')
      };
      component.occurMessage(data);
      expect(component.message).toContain('Occurs every day');
      expect(component.message).toContain('until 12-31-2024');
    });

    it('should handle Days repeat type with count 2', () => {
      const data = {
        repeatEveryType: 'Days',
        repeatEveryCount: 2,
        endTime: new Date('2024-12-31')
      };
      component.occurMessage(data);
      expect(component.message).toContain('Occurs every other day');
    });

    it('should handle Days repeat type with count > 2', () => {
      const data = {
        repeatEveryType: 'Days',
        repeatEveryCount: 3,
        endTime: new Date('2024-12-31')
      };
      component.occurMessage(data);
      expect(component.message).toContain('Occurs every 3 days');
    });

    it('should handle Week repeat type', () => {
      const data = {
        repeatEveryType: 'Week',
        days: ['Monday', 'Wednesday', 'Friday'],
        endTime: new Date('2024-12-31')
      };
      component.occurMessage(data);
      expect(component.message).toContain('Occurs every Monday,Wednesday,Friday');
    });

    it('should handle Weeks repeat type with count 2', () => {
      const data = {
        repeatEveryType: 'Weeks',
        repeatEveryCount: 2,
        days: ['Monday', 'Friday'],
        endTime: new Date('2024-12-31')
      };
      component.occurMessage(data);
      expect(component.message).toContain('Occurs every other  Monday,Friday');
    });

    it('should handle Weeks repeat type with count > 2', () => {
      const data = {
        repeatEveryType: 'Weeks',
        repeatEveryCount: 3,
        days: ['Monday', 'Friday'],
        endTime: new Date('2024-12-31')
      };
      component.occurMessage(data);
      expect(component.message).toContain('Occurs every 3 weeks on Monday,Friday');
    });

    it('should handle Month repeat type with chosen date', () => {
      const data = {
        repeatEveryType: 'Month',
        chosenDateOfMonth: true,
        dateOfMonth: 15,
        endTime: new Date('2024-12-31')
      };
      component.occurMessage(data);
      expect(component.message).toContain('Occurs on day 15');
    });

    it('should handle Month repeat type with monthly repeat type', () => {
      const data = {
        repeatEveryType: 'Month',
        chosenDateOfMonth: false,
        monthlyRepeatType: 'first Monday',
        endTime: new Date('2024-12-31')
      };
      component.occurMessage(data);
      expect(component.message).toContain('Occurs on the first Monday');
    });

    it('should handle Year repeat type', () => {
      const data = {
        repeatEveryType: 'Year',
        chosenDateOfMonth: true,
        dateOfMonth: 25,
        endTime: new Date('2024-12-31')
      };
      component.occurMessage(data);
      expect(component.message).toContain('Occurs on day 25');
    });

    it('should handle occurMessage with undefined repeatEveryType', () => {
      const data = { endTime: new Date('2024-12-31') };
      component.occurMessage(data);
      expect(component.message).toContain('until 12-31-2024');
    });
    it('should handle occurMessage with undefined days', () => {
      const data = { repeatEveryType: 'Week', endTime: new Date('2024-12-31') };
      component.occurMessage(data);
      expect(component.message).toContain('Occurs every');
    });
    it('should handle occurMessage with undefined endTime', () => {
      const data = { repeatEveryType: 'Day' };
      component.occurMessage(data);
      expect(component.message).toContain('Occurs every day');
    });
    it('should handle occurMessage with undefined chosenDateOfMonth', () => {
      const data = { repeatEveryType: 'Month', monthlyRepeatType: 'first Monday', endTime: new Date('2024-12-31') };
      component.occurMessage(data);
      expect(component.message).toContain('Occurs on the first Monday');
    });
  });

  describe('handleDownKeydown Method', () => {
    let mockEvent: any;

    beforeEach(() => {
      mockEvent = {
        key: 'Enter',
        preventDefault: jest.fn()
      };
    });

    it('should handle Enter key for edit action', () => {
      jest.spyOn(component, 'openEditModal').mockImplementation(() => {});
      const data = { id: 1 };
      const item = { action: 'edit' };

      component.handleDownKeydown(mockEvent, data, item, 'edit');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.openEditModal).toHaveBeenCalledWith(data, item);
    });

    it('should handle Space key for clear action', () => {
      mockEvent.key = ' ';
      jest.spyOn(component, 'clear').mockImplementation(() => {});

      component.handleDownKeydown(mockEvent, {}, {}, 'clear');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.clear).toHaveBeenCalled();
    });

    it('should handle filter action', () => {
      jest.spyOn(component, 'openFilterModal').mockImplementation(() => {});
      const data = {};

      component.handleDownKeydown(mockEvent, data, {}, 'filter');

      expect(component.openFilterModal).toHaveBeenCalledWith(data);
    });

    it('should handle open action', () => {
      jest.spyOn(component, 'openIdModal').mockImplementation(() => {});
      const data = { id: 1 };

      component.handleDownKeydown(mockEvent, data, {}, 'open');

      expect(component.openIdModal).toHaveBeenCalledWith(data);
    });

    it('should handle close action', () => {
      jest.spyOn(component, 'closeCalendarDescription').mockImplementation(() => {});

      component.handleDownKeydown(mockEvent, {}, {}, 'close');

      expect(component.closeCalendarDescription).toHaveBeenCalled();
    });

    it('should handle default case', () => {
      component.handleDownKeydown(mockEvent, {}, {}, 'unknown');
      expect(mockEvent.preventDefault).toHaveBeenCalled();
    });

    it('should not handle other keys', () => {
      mockEvent.key = 'Tab';
      jest.spyOn(component, 'clear').mockImplementation(() => {});

      component.handleDownKeydown(mockEvent, {}, {}, 'clear');

      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      expect(component.clear).not.toHaveBeenCalled();
    });
  });

  describe('changeRequestCollapse Method', () => {
    beforeEach(() => {
      jest.spyOn(component, 'initializeSeriesOption').mockImplementation(() => {
        component.seriesOptions = [
          { option: 1, text: 'This event', disabled: false },
          { option: 2, text: 'This and all following events', disabled: false }
        ];
      });
    });

    it('should toggle allRequestIsOpened and handle future date', () => {
      const futureDate = moment().add(1, 'day').toISOString();
      const data = { deliveryStart: futureDate };
      component.allRequestIsOpened = false;

      component.changeRequestCollapse(data);

      expect(component.allRequestIsOpened).toBe(true);
      expect(component.initializeSeriesOption).toHaveBeenCalled();
    });

    it('should disable series options for past dates', () => {
      const pastDate = moment().subtract(1, 'day').toISOString();
      const data = { deliveryStart: pastDate };
      component.allRequestIsOpened = false;

      component.changeRequestCollapse(data);

      expect(component.allRequestIsOpened).toBe(true);
      expect(component.seriesOptions[1].disabled).toBe(true);
    });

    it('should disable options for current date', () => {
      const currentDate = moment().toISOString();
      const data = { deliveryStart: currentDate };

      component.changeRequestCollapse(data);

      expect(component.seriesOptions[1].disabled).toBe(true);
    });
  });

  describe('initializeSeriesOption Method', () => {
    it('should initialize series options correctly', () => {
      component.initializeSeriesOption();

      expect(component.seriesOptions).toHaveLength(2);
      expect(component.seriesOptions[0]).toEqual({
        option: 1,
        text: 'This event',
        disabled: false
      });
      expect(component.seriesOptions[1]).toEqual({
        option: 2,
        text: 'This and all following events',
        disabled: false
      });
    });
  });

  describe('selectStatus Method', () => {
    it('should set current status', () => {
      const status = 'Approved';
      component.selectStatus(status);
      expect(component.currentStatus).toBe(status);
    });

    it('should not reset statusSubmitted', () => {
      component.statusSubmitted = true;
      component.selectStatus('Pending');
      expect(component.statusSubmitted).toBe(true);
    });
  });

  describe('getNDR Method', () => {
    beforeEach(() => {
      component.authUser = { RoleId: 2, id: 1 };
      component.ParentCompanyId = 1;
      component.toolTipContent = '';
    });

    it('should get NDR data and set edit permissions for role 4', () => {
      component.authUser = { RoleId: 4, id: 1 };
      const data = { event: { id: '1' } };
      const mockResponse = {
        data: {
          memberDetails: [{ Member: { id: 1 } }],
          companyDetails: [],
          gateDetails: [],
          equipmentDetails: []
        }
      };
      deliveryService.getNDRData.mockReturnValue(of(mockResponse));

      component.getNDR(data);

      expect(deliveryService.getNDRData).toHaveBeenCalledWith({
        DeliveryRequestId: 1,
        ParentCompanyId: 1
      });
      expect(component.eventData.edit).toBe(true);
    });

    it('should get NDR data and set edit permissions for role 3 with member match', () => {
      component.authUser = { RoleId: 3, id: 1 };
      const data = { event: { id: '1' } };
      const mockResponse = {
        data: {
          memberDetails: [{ Member: { id: 1 } }],
          companyDetails: [],
          gateDetails: [],
          equipmentDetails: []
        }
      };
      deliveryService.getNDRData.mockReturnValue(of(mockResponse));

      component.getNDR(data);

      expect(component.eventData.edit).toBe(true);
    });

    it('should set edit to false when member not found', () => {
      component.authUser = { RoleId: 3, id: 2 };
      const data = { event: { id: '1' } };
      const mockResponse = {
        data: {
          memberDetails: [{ Member: { id: 1 } }],
          companyDetails: [],
          gateDetails: [],
          equipmentDetails: []
        }
      };
      deliveryService.getNDRData.mockReturnValue(of(mockResponse));

      component.getNDR(data);

      expect(component.eventData.edit).toBe(false);
    });

    it('should generate tooltip content for more than 3 members', () => {
      const data = { event: { id: '1' } };
      const mockResponse = {
        data: {
          memberDetails: [
            { Member: { User: { firstName: 'John', lastName: 'Doe' } } },
            { Member: { User: { firstName: 'Jane', lastName: 'Smith' } } },
            { Member: { User: { firstName: 'Bob', lastName: 'Johnson' } } },
            { Member: { User: { firstName: 'Alice', lastName: 'Brown' } } }
          ],
          companyDetails: [],
          gateDetails: [],
          equipmentDetails: []
        }
      };
      deliveryService.getNDRData.mockReturnValue(of(mockResponse));

      component.getNDR(data);

      expect(component.toolTipContent).toContain('Alice Brown');
    });

    it('should handle members without firstName', () => {
      const data = { event: { id: '1' } };
      const mockResponse = {
        data: {
          memberDetails: [
            { Member: { User: { firstName: 'John', lastName: 'Doe' } } },
            { Member: { User: { firstName: 'Jane', lastName: 'Smith' } } },
            { Member: { User: { firstName: 'Bob', lastName: 'Johnson' } } },
            { Member: { User: { email: '<EMAIL>' } } }
          ],
          companyDetails: [],
          gateDetails: [],
          equipmentDetails: []
        }
      };
      deliveryService.getNDRData.mockReturnValue(of(mockResponse));

      component.getNDR(data);

      expect(component.toolTipContent).toContain('<EMAIL>');
    });
  });

  describe('getEditValue Method', () => {
    beforeEach(() => {
      component.authUser = { RoleId: 2, id: 1 };
      component.ParentCompanyId = 1;
      component.eventData = {};
    });

    it('should get edit value and set status', () => {
      const data = { id: 1 };
      const mockResponse = {
        data: {
          memberDetails: [{ Member: { id: 1 } }],
          companyDetails: [],
          gateDetails: []
        }
      };
      deliveryService.getNDRData.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'setStatus').mockImplementation(() => {});

      component.getEditValue(data);

      expect(deliveryService.getNDRData).toHaveBeenCalledWith({
        DeliveryRequestId: 1,
        ParentCompanyId: 1
      });
      expect(component.eventData.edit).toBe(true);
      expect(component.setStatus).toHaveBeenCalledWith(component.eventData);
    });

    it('should set edit to false when member not found', () => {
      const data = { id: 1 };
      const mockResponse = {
        data: {
          memberDetails: [{ Member: { id: 2 } }],
          companyDetails: [],
          gateDetails: []
        }
      };
      deliveryService.getNDRData.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'setStatus').mockImplementation(() => {});

      component.getEditValue(data);

      expect(component.eventData.edit).toBe(false);
    });
  });

  describe('ngOnInit Method', () => {
    it('should handle login user subscription for role 2', () => {
      const userData = { RoleId: 2, id: 1 };

      component.ngOnInit();
      deliveryService.loginUser.next(userData);

      expect(component.authUser).toEqual(userData);
      expect(component.statusValue).toEqual(['Approved', 'Declined']);
    });

    it('should handle login user subscription for role 3', () => {
      const userData = { RoleId: 3, id: 1 };

      component.ngOnInit();
      deliveryService.loginUser.next(userData);

      expect(component.authUser).toEqual(userData);
      expect(component.statusValue).toEqual(['Delivered']);
    });

    it('should handle login user subscription for other roles', () => {
      const userData = { RoleId: 1, id: 1 };

      component.ngOnInit();
      deliveryService.loginUser.next(userData);

      expect(component.authUser).toEqual(userData);
      expect(component.statusValue).toEqual(['Approved', 'Declined']);
    });

    it('should not process undefined user data', () => {
      component.ngOnInit();
      deliveryService.loginUser.next(undefined);

      expect(component.authUser).toEqual({ RoleId: 2 }); // Should remain unchanged
    });

    it('should not process null user data', () => {
      component.ngOnInit();
      deliveryService.loginUser.next(null);

      expect(component.authUser).toEqual({ RoleId: 2 }); // Should remain unchanged
    });

    it('should not process empty string user data', () => {
      component.ngOnInit();
      deliveryService.loginUser.next('');

      expect(component.authUser).toEqual({ RoleId: 2 }); // Should remain unchanged
    });
  });

  describe('Error Handling in moveToVoidList', () => {
    beforeEach(() => {
      component.calendarCurrentDeliveryIndex = 0;
      component.deliveryList = [{ id: 1 }];
      component.ProjectId = 1;
      jest.spyOn(component, 'closeDescription').mockImplementation(() => {});
      jest.spyOn(component, 'close').mockImplementation(() => {});
    });

    it('should handle error with status code 400', () => {
      const error = {
        message: {
          statusCode: 400,
          details: [{ error: 'Validation Error' }]
        }
      };
      deliveryService.createVoid.mockReturnValue(throwError(() => error));
      jest.spyOn(component, 'showError').mockImplementation(() => {});

      component.moveToVoidList();

      expect(component.showError).toHaveBeenCalledWith(error);
      expect(component.voidSubmitted).toBe(false);
    });

    it('should handle error without message', () => {
      const error = {};
      deliveryService.createVoid.mockReturnValue(throwError(() => error));

      component.moveToVoidList();

      expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
      expect(component.voidSubmitted).toBe(false);
    });

    it('should handle error with message but no status code', () => {
      const error = { message: 'General error' };
      deliveryService.createVoid.mockReturnValue(throwError(() => error));

      component.moveToVoidList();

      expect(toastrService.error).toHaveBeenCalledWith('General error', 'OOPS!');
      expect(component.voidSubmitted).toBe(false);
    });

    it('should handle error with message.details as string', () => {
      const error = { message: { details: 'Some string' } };
      deliveryService.createVoid.mockReturnValue(throwError(() => error));
      jest.spyOn(component, 'showError');
      component.moveToVoidList();
      expect(component.showError).toHaveBeenCalledWith(error);
    });

    it('should handle error with message.details as undefined', () => {
      const error = { message: { details: undefined } };
      deliveryService.createVoid.mockReturnValue(throwError(() => error));
      jest.spyOn(component, 'showError');
      component.moveToVoidList();
      expect(component.showError).toHaveBeenCalledWith(error);
    });
  });

  describe('showError edge cases', () => {
    it('should handle showError with message.details as string', () => {
      const error = { message: { details: 'Some string' } };
      component.showError(error);
      expect(toastrService.error).toHaveBeenCalled();
    });
    it('should handle showError with message.details as undefined', () => {
      const error = { message: { details: undefined } };
      component.showError(error);
      expect(toastrService.error).toHaveBeenCalled();
    });
    it('should handle showError with message.details as object', () => {
      const error = { message: { details: [{ foo: 'bar', baz: 'qux' }] } };
      component.showError(error);
      expect(toastrService.error).toHaveBeenCalledWith(['bar', 'qux']);
    });
    it('should handle showError with message.details as empty array', () => {
      const error = { message: { details: [] } };
      component.showError(error);
      expect(toastrService.error).toHaveBeenCalled();
    });
  });

  describe('getEventNDR Complex Scenarios', () => {
    beforeEach(() => {
      component.ProjectId = 1;
      component.ParentCompanyId = 1;
      component.Range = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };
      component.filterForm = TestBed.inject(UntypedFormBuilder).group({
        companyFilter: [''],
        descriptionFilter: [''],
        statusFilter: [''],
        memberFilter: [''],
        gateFilter: [''],
        equipmentFilter: [''],
        locationFilter: [''],
        dateFilter: [''],
        pickFrom: [''],
        pickTo: ['']
      });
      component.search = '';
      component.filterCount = 0;
      component.currentView = 'Month';
    });

    it('should handle getEventNDR service call', () => {
      const mockResponse = {
        data: [],
        statusData: {
          statusColorCode: JSON.stringify([]),
          useTextColorAsLegend: JSON.stringify(false),
          isDefaultColor: JSON.stringify(false)
        },
        cardData: { deliveryCard: JSON.stringify([]) },
        lastId: 1
      };
      calendarService.getEventNDR.mockReturnValue(of(mockResponse));

      component.getEventNDR();

      expect(calendarService.getEventNDR).toHaveBeenCalled();
      expect(component.events).toEqual([]);
    });

    it('should handle calendar events processing', () => {
      const mockResponse = {
        data: [
          {
            id: 1,
            requestType: 'calendarEvent',
            isAllDay: true,
            description: 'All Day Event',
            fromDate: new Date(),
            toDate: new Date(),
            uniqueNumber: 'CAL001'
          }
        ],
        statusData: {
          statusColorCode: JSON.stringify([]),
          useTextColorAsLegend: JSON.stringify(false),
          isDefaultColor: JSON.stringify(false)
        },
        cardData: { deliveryCard: JSON.stringify([]) },
        lastId: 1
      };
      calendarService.getEventNDR.mockReturnValue(of(mockResponse));

      component.getEventNDR();

      expect(component.events).toHaveLength(1);
      expect(component.events[0].className).toBe('calendar_event');
    });

    it('should handle status color configuration', () => {
      const mockResponse = {
        data: [],
        statusData: {
          statusColorCode: JSON.stringify([
            { status: 'approved', backgroundColor: '#green', fontColor: '#white' }
          ]),
          useTextColorAsLegend: JSON.stringify(true),
          isDefaultColor: JSON.stringify(false)
        },
        cardData: { deliveryCard: JSON.stringify([]) },
        lastId: 1
      };
      calendarService.getEventNDR.mockReturnValue(of(mockResponse));

      component.getEventNDR();

      expect(component.approved).toBeDefined();
    });

    it('should handle default color configuration', () => {
      const mockResponse = {
        data: [],
        statusData: {
          statusColorCode: JSON.stringify([
            { status: 'delivered', backgroundColor: '#blue', fontColor: '#white' }
          ]),
          useTextColorAsLegend: JSON.stringify(false),
          isDefaultColor: JSON.stringify(true)
        },
        cardData: { deliveryCard: JSON.stringify([]) },
        lastId: 1
      };
      calendarService.getEventNDR.mockReturnValue(of(mockResponse));

      component.getEventNDR();

      expect(component.delivered).toBeDefined();
    });
  });

  describe('Status Setting Edge Cases', () => {
    beforeEach(() => {
      component.deliveryList = [{ id: 1, status: 'Pending' }];
    });

    it('should show status for role 2 with non-expired/delivered status', () => {
      component.authUser = { RoleId: 2 };
      const item = { id: 1, status: 'Pending', edit: true };

      component.setStatus(item);

      expect(component.showStatus).toBe(true);
    });

    it('should not show status for role 2 with expired status', () => {
      component.authUser = { RoleId: 2 };
      const item = { id: 1, status: 'Expired', edit: true };

      component.setStatus(item);

      expect(component.showStatus).toBe(false);
    });

    it('should not show status for role 2 with delivered status', () => {
      component.authUser = { RoleId: 2 };
      const item = { id: 1, status: 'Delivered', edit: true };

      component.setStatus(item);

      expect(component.showStatus).toBe(false);
    });

    it('should show status for role 3 with approved status', () => {
      component.authUser = { RoleId: 3 };
      const item = { id: 1, status: 'Approved', edit: true };

      component.setStatus(item);

      expect(component.showStatus).toBe(true);
    });

    it('should not show status for role 3 with pending status', () => {
      component.authUser = { RoleId: 3 };
      const item = { id: 1, status: 'Pending', edit: true };

      component.setStatus(item);

      expect(component.showStatus).toBe(false);
    });
  });

  describe('Additional Edge Cases and Coverage', () => {
    it('should handle delivery description with empty argument', () => {
      component.deliveryDescription({});
      expect(component.eventData).toEqual([]);
    });

    it('should handle delivery description without uniqueNumber', () => {
      component.deliveryList = [{ id: 1, requestType: 'deliveryRequest' }];
      const arg = {
        event: {
          id: '1',
          extendedProps: {}
        }
      };
      jest.spyOn(component, 'getNDR').mockImplementation(() => {});
      jest.spyOn(component, 'openIdModal').mockImplementation(() => {});

      component.deliveryDescription(arg);

      expect(component.calendarCurrentDeliveryIndex).toBe(0);
    });

    it('should handle calendar description with empty events array', () => {
      component.events = [];
      component.deliveryList = [];
      const arg = {
        event: {
          title: 'Test Event',
          extendedProps: {
            uniqueNumber: 'CAL001'
          }
        }
      };

      expect(() => component.calendarDescription(arg)).not.toThrow();
    });

    it('should handle setCalendar without calendarApi', () => {
      component.calendarComponent1 = null;
      jest.spyOn(component, 'getEventNDR').mockImplementation(() => {});

      expect(() => component.setCalendar()).not.toThrow();
    });

    it('should handle getEventNDR without ProjectId or ParentCompanyId', () => {
      component.ProjectId = null;
      component.ParentCompanyId = null;

      component.getEventNDR();

      expect(calendarService.getEventNDR).not.toHaveBeenCalled();
    });

    it('should handle getEventNDR with undefined filterForm', () => {
      component.filterForm = undefined;
      component.ProjectId = 1;
      component.ParentCompanyId = 1;

      expect(() => component.getEventNDR()).not.toThrow();
    });

    it('should handle checkFutureDate with equal start and end dates', () => {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 1);

      const result = component.checkFutureDate(futureDate, futureDate);
      expect(result).toBe(false); // Start should be less than end
    });

    it('should handle checkFutureDate with start date after end date', () => {
      const futureStart = new Date();
      futureStart.setDate(futureStart.getDate() + 2);
      const futureEnd = new Date();
      futureEnd.setDate(futureEnd.getDate() + 1);

      const result = component.checkFutureDate(futureStart, futureEnd);
      expect(result).toBe(false);
    });

    it('should handle convertStart method', () => {
      const deliveryDate = new Date('2024-03-20');
      const startHours = 10;
      const startMinutes = 30;

      const result = component.convertStart(deliveryDate, startHours, startMinutes);

      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result).toContain('2024');
    });

    it('should handle getResponsiblePeople with partial names', () => {
      const userWithFirstNameOnly = { firstName: 'John', lastName: null };
      const result1 = component.getResponsiblePeople(userWithFirstNameOnly);
      expect(result1).toBe('UU');

      const userWithLastNameOnly = { firstName: null, lastName: 'Doe' };
      const result2 = component.getResponsiblePeople(userWithLastNameOnly);
      expect(result2).toBe('UU');
    });

    it('should handle changeFormat with invalid date', () => {
      const result = component.changeFormat('invalid-date');
      expect(result).toContain('Invalid date');
    });

    it('should handle occurMessage with empty days array', () => {
      const data = {
        repeatEveryType: 'Week',
        days: [],
        endTime: new Date('2024-12-31')
      };
      component.occurMessage(data);
      expect(component.message).toContain('Occurs every');
    });

    it('should handle showError with different error structures', () => {
      const errorWithArray = {
        message: {
          details: [
            { field1: 'value1', field2: 'value2' }
          ]
        }
      };
      component.showError(errorWithArray);
      expect(toastrService.error).toHaveBeenCalledWith(['value1', 'value2']);
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
    });

    it('should handle close method when modalRef is null', () => {
      component.modalRef = null;
      component.submitted = true;
      component.formSubmitted = true;

      expect(() => component.close()).not.toThrow();
      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
    });

    it('should handle voidConfirmationResponse with different actions', () => {
      // Test with 'no' action
      component.voidConfirmationResponse('no');
      expect(component.modalRef1.hide).toHaveBeenCalled();

      // Test with 'yes' action
      jest.spyOn(component, 'moveToVoidList').mockImplementation(() => {});
      component.voidConfirmationResponse('yes');
      expect(component.modalRef1.hide).toHaveBeenCalled();
      expect(component.moveToVoidList).toHaveBeenCalled();
    });

    it('should handle calendar options configuration', () => {
      expect(component.calendarOptions.selectable).toBe(true);
      expect(component.calendarOptions.initialView).toBe('dayGridMonth');
      expect(component.calendarOptions.aspectRatio).toBe(2);
      expect(component.calendarOptions.slotEventOverlap).toBe(false);
      expect(component.calendarOptions.contentHeight).toBe('liquid');
      expect(component.calendarOptions.fixedWeekCount).toBe(false);
      expect(component.calendarOptions.expandRows).toBe(true);
      expect(component.calendarOptions.nowIndicator).toBe(true);
      expect(component.calendarOptions.moreLinkClick).toBe('popover');
      expect(component.calendarOptions.showNonCurrentDates).toBe(false);
      expect(component.calendarOptions.firstDay).toBe(0);
      expect(component.calendarOptions.dayMaxEventRows).toBe(true);
    });

    it('should handle calendar options views configuration', () => {
      expect(component.calendarOptions.views.timeGrid.dayMaxEventRows).toBe(2);
      expect(component.calendarOptions.views.dayGridMonth.allDaySlot).toBe(true);
      expect(component.calendarOptions.views.timeGridWeek.allDaySlot).toBe(true);
      expect(component.calendarOptions.views.timeGridDay.allDaySlot).toBe(true);
    });

    it('should handle calendar options header toolbar configuration', () => {
      expect(component.calendarOptions.headerToolbar).toBeDefined();
    });

    it('should handle calendar options event time format', () => {
      expect(component.calendarOptions.eventTimeFormat).toBeDefined();
    });

    it('should handle calendar options custom buttons configuration', () => {
      expect(component.calendarOptions.customButtons).toBeDefined();
      expect(component.calendarOptions.customButtons.prev).toBeDefined();
      expect(component.calendarOptions.customButtons.next).toBeDefined();
      expect(component.calendarOptions.customButtons.prevYear).toBeDefined();
      expect(component.calendarOptions.customButtons.nextYear).toBeDefined();
      expect(component.calendarOptions.customButtons.timeGridWeek).toBeDefined();
      expect(component.calendarOptions.customButtons.timeGridDay).toBeDefined();
      expect(component.calendarOptions.customButtons.dayGridMonth).toBeDefined();
    });
  });
});
