<section class="page-section pt-md-50px">
    <div class="page-inner-content">
      <div class="page-card bg-white rounded delivery-request-card">
        <div
          class="float-end fs12 me-3 p2 color-orange fw-bold"
        >
        <button
        class="btn btn-white btn-template-white fs12 color-orange radius5 fw-bold
        cairo-regular me-3 py-0 px-4"
        (click)="openDeleteModal(-1, deleteList)"
        [disabled]="checkSelectedRow()"
      >
        Delete
      </button>
        <button
        type="button"
        class="btn btn-orange-dark2 px-4 pt7 pb7 radius5 fs12"
        (click)="openTemplate(null)"
      >
        <em class="fa fa-plus me-1"></em> Add
      </button>
        </div>
        <tabset>
          <tab heading="Delivery">
            <div class="table-responsive tab-grid">
              <table class="table table-custom request-resizable mb-0" aria-describedby="Emptable">
                <thead>
                  <th scope="col" *ngIf="templateList?.length > 0">
                   <div class="custom-checkbox form-check">
                      <input class="form-check-input float-none ms-0" type="checkbox"
                      (change)="selectAllTemplatesData()"
                      [checked]="selectAll"
                      id="tblData"
                      name="tblData">
                      <label class="form-check-label c-pointer fs12" for="tblData"> &nbsp;
                      </label>
                    </div>
                  </th>
                  <th scope="col" resizable>
                    Template Name
                  </th>
                  <th scope="col" resizable>
                    Created By
                  </th>
                  <th scope="col" resizable>
                    Date and Time
                  </th>
                  <th scope="col" resizable>Action</th>
                </thead>
                <tbody *ngIf="templateList?.length > 0 && loader == false">
                  <tr *ngFor="let data of templateList | paginate
                  : {
                      itemsPerPage: pageSize,
                      currentPage: pageNo,
                      totalItems: totalCount
                    };
                  let j = index">
                  <td class="custom-checkbox">
                    <div class="form-check text-start ps-0">
                      <input class="form-check-input float-none ms-0" type="checkbox"
                      [checked]="data.isChecked"
                      (change)="setSelectedItem(j)"
                      id="tblData1_{{ j }}"
                      name="tblData1">
                      <label class="form-check-label c-pointer fs12" for="tblData1_{{ j }}"> &nbsp;
                      </label>
                    </div>
                  </td>
                    <td>
                      <span
                        >{{data.template_name}}</span
                      >
                    </td>
                    <td>
                      <span
                        >{{data?.createdUser.firstName}} {{data?.createdUser.lastName}}</span
                      >
                    </td>
                    <td>
                      <span
                        >{{data?.createdAt | date : 'MMM dd, yyyy, hh:mm a'}}</span
                      >
                    </td>
                    <td class="p-0">
                      <ul class="list-inline mb-0">
                        <li
                          class="list-inline-item me-0"
                          tooltip="Edit"
                          placement="top"
                          (click)="openTemplate(data)" (keydown)="handleToggleKeydown($event, data)"
                        >
                          <a href="javascript:void(0)"
                            ><img
                              src="./assets/images/edit.svg"
                              alt="edit"
                              class="h-15px action-icon ms-3"
                            />
                          </a>
                        </li>
                        <li
                          class="list-inline-item ms-0"
                          tooltip="Delete"
                          placement="top"
                          (click)="openDeleteModal(j, deleteList)" (keydown)="handleDeleteKeydown($event, j, deleteList)"
                        >
                          <a href="javascript:void(0)"
                            ><img
                              src="./assets/images/delete.svg"
                              alt="edit"
                              class="h-15px action-icon ms-1"
                            />
                          </a>
                        </li>
                      </ul>
                    </td>
                  </tr>
                </tbody>
                <tr *ngIf="loader">
                  <td colspan="7" class="text-center">
                    <div class="fs18 fw-bold cairo-regular my-5 text-black">Loading...</div>
                  </td>
                </tr>
                <tr *ngIf="!loader && templateList?.length === 0">
                  <td colspan="7" class="text-center">
                    <div class="fs18 fw-bold cairo-regular my-5 text-black">
                      No Records Found
                    </div>
                  </td>
                </tr>
              </table>
            </div>
            <div
            class="tab-pagination px-2"
            id="tab-pagination6"
            *ngIf="loader == false && templateList?.length > 25"
          >
            <div class="row">
              <div class="col-md-3 align-items-center">
                <ul class="list-inline my-3">
                  <li class="list-inline-item notify-pagination">
                    <label class="fs12 color-grey4" for = "showPage">Show entries</label>
                  </li>
                  <li class="list-inline-item">
                    <select id = "showPage"
                      class="w-auto form-select fs12 color-grey4"
                      (change)="changePageSize($event.target.value)"
                      [ngModel]="pageSize"
                    >
                      <option value="25">25</option>
                      <option value="50">50</option>
                      <option value="100">100</option>
                      <option value="150">150</option>
                    </select>
                  </li>
                </ul>
              </div>
              <div class="col-md-8 text-center">
                <div class="my-3 position-relative d-inline-block">
                  <pagination-controls
                    (pageChange)="changePageNo($event)"
                    previousLabel=""
                    nextLabel=""
                  >
                  </pagination-controls>
                </div>
              </div>
            </div>
          </div>
          </tab>
        </tabset>
      </div>
    </div>
  </section>

  <ng-template #deleteList>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure you want to delete?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetAndClose()"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          type="submit"
          (click)="removeItem()"
          [disabled]="deleteSubmitted"
        >
          <em class="fa fa-spinner" aria-hidden="true" *ngIf="deleteSubmitted"></em>Yes
        </button>
      </div>
    </div>
  </ng-template>
