import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TemplateGridComponent } from './template-grid.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { ProjectService } from '../../services/profile/project.service';
import { BookingTemplatesService } from '../../services/booking-templates/booking-templates.service';
import { MixpanelService } from '../../services/mixpanel.service';
import { of, throwError } from 'rxjs';
import { TemplateRef } from '@angular/core';

describe('TemplateGridComponent', () => {
  let component: TemplateGridComponent;
  let fixture: ComponentFixture<TemplateGridComponent>;
  let modalService: BsModalService;
  let toastrService: ToastrService;
  let projectService: ProjectService;
  let bookingTemplateService: BookingTemplatesService;
  let mixpanelService: MixpanelService;

  const mockModalRef = {
    hide: jest.fn(),
    content: {
      closeBtnName: ''
    }
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TemplateGridComponent],
      providers: [
        {
          provide: BsModalService,
          useValue: {
            show: jest.fn().mockReturnValue(mockModalRef)
          }
        },
        {
          provide: ToastrService,
          useValue: {
            success: jest.fn(),
            error: jest.fn()
          }
        },
        {
          provide: ProjectService,
          useValue: {
            projectParent: of({ ProjectId: '123', ParentCompanyId: '456' })
          }
        },
        {
          provide: BookingTemplatesService,
          useValue: {
            fetchTemplates: of(true),
            getTemplates: jest.fn().mockReturnValue(of({ data: { rows: [], count: 0 } })),
            deleteBookingTemplate: jest.fn().mockReturnValue(of({ message: 'Deleted' }))
          }
        },
        {
          provide: MixpanelService,
          useValue: {
            addMixpanelEvents: jest.fn()
          }
        }
      ]
    }).compileComponents();

    modalService = TestBed.inject(BsModalService);
    toastrService = TestBed.inject(ToastrService);
    projectService = TestBed.inject(ProjectService);
    bookingTemplateService = TestBed.inject(BookingTemplatesService);
    mixpanelService = TestBed.inject(MixpanelService);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TemplateGridComponent);
    component = fixture.componentInstance;

    // Initialize default states used by tests
    component.templateList = [];
    component.deleteIndex = [];
    component.modalRef = mockModalRef as any;

    jest.clearAllMocks();

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('getTemplates', () => {
    it('should fetch templates successfully', () => {
      const mockResponse = {
        data: {
          rows: [{ id: 1, name: 'Template 1' }],
          count: 1
        }
      };
      jest.spyOn(bookingTemplateService, 'getTemplates').mockReturnValue(of(mockResponse));

      component.loader = true; // simulate loading state
      component.getTemplates();

      expect(component.loader).toBeFalsy();
      expect(component.templateList).toEqual(mockResponse.data.rows);
      expect(component.totalCount).toEqual(mockResponse.data.count);
    });

    // it('should handle error when fetching templates', async () => {
    //   jest.spyOn(bookingTemplateService, 'getTemplates').mockReturnValue(throwError(new Error('API Error')));
    
    //   component.getTemplates();
    
    //   // Wait a tick for the async error handling to run
    //   await Promise.resolve();
    
    //   expect(component.loader).toBeFalsy();
    // });
    
  });

  describe('deleteTemplate', () => {
    beforeEach(() => {
      component.templateList = [{ id: 1 }];
      component.deleteIndex = [0];
      component.modalRef = mockModalRef as any;
    });

    it('should delete template successfully', () => {
      const mockResponse = { message: 'Template deleted successfully' };
      jest.spyOn(bookingTemplateService, 'deleteBookingTemplate').mockReturnValue(of(mockResponse));

      component.deleteTemplate();

      expect(toastrService.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
      expect(mixpanelService.addMixpanelEvents).toHaveBeenCalledWith('Deleted Delivery Template');
      expect(mockModalRef.hide).toHaveBeenCalled();
    });

    it('should handle error when deleting template', () => {
      const error = { message: { statusCode: 400, details: [{ message: 'Error message' }] } };
      jest.spyOn(bookingTemplateService, 'deleteBookingTemplate').mockReturnValue(throwError(() => error));

      component.deleteTemplate();

      expect(toastrService.error).toHaveBeenCalled();
      expect(component.deleteSubmitted).toBeFalsy();
    });
  });

  describe('pagination', () => {
    beforeEach(() => {
      // Ensure getTemplates returns observable to avoid errors
      jest.spyOn(bookingTemplateService, 'getTemplates').mockReturnValue(of({ data: { rows: [], count: 0 } }));
    });

    it('should change page size', () => {
      const getTemplatesSpy = jest.spyOn(component, 'getTemplates');
      component.changePageSize(50);

      expect(component.pageSize).toBe(50);
      expect(getTemplatesSpy).toHaveBeenCalled();
    });

    it('should change page number', () => {
      const getTemplatesSpy = jest.spyOn(component, 'getTemplates');
      component.changePageNo(2);

      expect(component.pageNo).toBe(2);
      expect(getTemplatesSpy).toHaveBeenCalled();
    });
  });

  describe('template selection', () => {
    it('should select all templates', () => {
      component.templateList = [
        { id: 1, isChecked: false },
        { id: 2, isChecked: false }
      ];

      component.selectAllTemplatesData();

      expect(component.selectAll).toBeTruthy();
      expect(component.templateList.every(t => t.isChecked)).toBeTruthy();
    });

    it('should unselect all templates', () => {
      component.templateList = [
        { id: 1, isChecked: true },
        { id: 2, isChecked: true }
      ];
      component.selectAll = true;

      component.selectAllTemplatesData();

      expect(component.selectAll).toBeFalsy();
      expect(component.templateList.every(t => !t.isChecked)).toBeTruthy();
    });

    it('should toggle individual template selection', () => {
      component.templateList = [
        { id: 1, isChecked: false },
        { id: 2, isChecked: false }
      ];

      component.setSelectedItem(0);

      expect(component.templateList[0].isChecked).toBeTruthy();
    });
  });

  describe('modal operations', () => {
    beforeEach(() => {
      component.templateList = [{ id: 123 }];
      component.deleteIndex = [];
    });

    it('should open delete modal', () => {
      const templateRef = {} as TemplateRef<any>;
      component.openDeleteModal(0, templateRef);

      expect(modalService.show).toHaveBeenCalled();
      expect(component.deleteIndex[0]).toBe(123);
    });

    it('should open template modal', () => {
      const templateData = { id: 1, name: 'Template 1' };
      component.openTemplate(templateData);

      expect(modalService.show).toHaveBeenCalled();
    });

    it('should reset and close modal', () => {
      component.deleteSubmitted = true;

      component.resetAndClose();

      expect(component.deleteSubmitted).toBeFalsy();
      expect(mockModalRef.hide).toHaveBeenCalled();
    });
  });

  describe('keyboard events', () => {
    beforeEach(() => {
      component.templateList = [{ id: 1 }];
      component.deleteIndex = [];
    });

    it('should handle toggle keydown', () => {
      const event = { key: 'Enter', preventDefault: jest.fn() } as unknown as KeyboardEvent;
      const data = { id: 1 };
      const openTemplateSpy = jest.spyOn(component, 'openTemplate');

      component.handleToggleKeydown(event, data);

      expect(event.preventDefault).toHaveBeenCalled();
      expect(openTemplateSpy).toHaveBeenCalledWith(data);
    });

    it('should handle delete keydown', () => {
      const event = { key: 'Enter', preventDefault: jest.fn() } as unknown as KeyboardEvent;
      const data = 0;
      const templateRef = {} as TemplateRef<any>;
      const openDeleteModalSpy = jest.spyOn(component, 'openDeleteModal');

      // Use index 0 for templateList
      component.handleDeleteKeydown(event, data, templateRef);

      expect(event.preventDefault).toHaveBeenCalled();
      expect(openDeleteModalSpy).toHaveBeenCalledWith(data, templateRef);
    });
  });
});
