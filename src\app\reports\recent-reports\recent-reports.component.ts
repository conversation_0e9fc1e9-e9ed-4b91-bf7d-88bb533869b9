import {
  Component, Input, OnInit, TemplateRef,
} from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import moment from 'moment';
import { ReportsService } from '../../services/reports/reports.service';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';

@Component({
  selector: 'app-recent-reports',
  templateUrl: './recent-reports.component.html',
})
export class RecentReportsComponent implements OnInit {
  @Input() public filterValues: any;

  @Input() public getSearchText: any;

  public totalRecentCount = 0;

  public pageSize = 25;

  public pageNo = 1;

  public loader = true;

  public recentReports: any = [];

  public ProjectId: string | number;

  public sortColumn = 'id';

  public sort = 'DESC';

  public deleteRecentReportData: any = {};

  public modalRef: BsModalRef;

  public deleteRecentReportSubmitted = false;

  public authUser: any = {};

  public constructor(
    private readonly reportsService: ReportsService,
    public projectService: ProjectService,
    private readonly modalService: BsModalService,
    private readonly toastr: ToastrService,
    private readonly deliveryService: DeliveryService,
  ) {
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
      }
    });
    this.projectService.projectParent.subscribe((response19): void => {
      if (response19 !== undefined && response19 !== null && response19 !== '') {
        this.ProjectId = response19.ProjectId;
        this.getRecentReports();
      }
    });
  }

  public ngOnInit(): void { /* */ }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public handleDownKeydown(event: KeyboardEvent, data: any, item: any, type: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'download':
          this.downloadRecentReport(data, item);
          break;
        case 'delete':
          this.openDeleteModal(data, item);
          break;
        case 'auto':
          this.autoDownloadReportWithRunOption(data);
          break;
        default:
          break;
      }
    }
  }

  public ngOnChanges(): void {
    this.getRecentReports();
  }

  public changePageNo(pageNo: number): void {
    this.pageNo = pageNo;
    this.getRecentReports();
  }

  public getRecentReports(): void {
    if (!this.ProjectId) return;

    this.loader = true;
    this.recentReports = [];

    const basePayload = {
      ProjectId: this.ProjectId,
      pageSize: this.pageSize,
      pageNo: this.pageNo,
      sort: this.sort,
      sortByField: this.sortColumn,
      search: this.getSearchText || '',
    };

    const payload = this.buildFilterPayload(basePayload);

    this.reportsService.getRecentReports(payload).subscribe((response: any): void => {
      this.loader = false;
      if (response) {
        const responseData = response.data;
        this.recentReports = responseData.scheduledReports;
        this.totalRecentCount = responseData.count;
      }
    });
  }


  public buildFilterPayload(basePayload: any): any {
    if (!this.filterValues) return basePayload;

    const {
      createdUserId, reportName, templateType, lastRun,
    } = this.filterValues;

    return {
      ...basePayload,
      ...(createdUserId && { createdUserId }),
      ...(reportName && { reportName }),
      ...(templateType && { templateType }),
      ...(lastRun && {
        lastRun: moment(lastRun).format('YYYY-MM-DD'),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      }),
    };
  }

  public autoDownloadReportWithRunOption(data): void {
    const selectedRow = data;
    selectedRow.runNowLoader = true;
    this.reportsService
      .runNowScheduledOrSavedReport({
        id: selectedRow.id,
        ProjectId: this.ProjectId,
      })
      .subscribe({
        next: (response: any): void => {
          if (response) {
            this.downloadRecentReport(response.data, 'reRun');
            selectedRow.runNowLoader = false;
          }
        },
        error: (runNowScheduledReportError): void => {
          selectedRow.runNowLoader = false;
          if (runNowScheduledReportError.message?.statusCode === 400) {
            this.showError(runNowScheduledReportError);
          } else if (!runNowScheduledReportError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(runNowScheduledReportError.message, 'OOPS!');
          }
        },
      });
  }

  public changePageSize(pageSize: number): void {
    this.pageSize = pageSize;
    this.getRecentReports();
  }

  public sortByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getRecentReports();
  }

  public openDeleteModal(deleteData: any, template: TemplateRef<any>): void {
    this.deleteRecentReportData = {};
    this.deleteRecentReportData = deleteData;
    let data = {};
    data = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-delivery-popup custom-modal',
    };
    this.modalRef = this.modalService.show(template, data);
  }

  public downloadRecentReport(data, action): void {
    if ((data && action === 'reRun') || (data?.s3_url && action === 'lastRun')) {
      const link = document.createElement('a');
      link.setAttribute('target', '_self');
      if (action === 'reRun') {
        link.setAttribute('href', data);
      } else {
        link.setAttribute('href', data.s3_url);
      }
      document.body.appendChild(link);
      link.click();
      link.remove();
      this.toastr.success('Report downloaded successfully');
    } else {
      this.toastr.info('There are no events available within the scheduled date range');
    }
  }

  public deleteRecentReport(): void {
    this.deleteRecentReportSubmitted = true;
    this.reportsService
      .deleteScheduledReports({
        id: this.deleteRecentReportData.id,
        ProjectId: this.ProjectId,
        // ParentCompanyId: this.ParentCompanyId,
      })
      .subscribe({
        next: (response: any): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.getRecentReports();
            this.deleteRecentReportData = {};
            this.deleteRecentReportSubmitted = false;
            this.modalRef.hide();
          }
        },
        error: (deleteConcreteRequestError): void => {
          this.deleteRecentReportSubmitted = false;
          if (deleteConcreteRequestError.message?.statusCode === 400) {
            this.showError(deleteConcreteRequestError);
          } else if (!deleteConcreteRequestError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(deleteConcreteRequestError.message, 'OOPS!');
          }
        },
      });
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.toastr.error(errorMessage);
  }

  public resetAndClose(): void {
    this.modalRef.hide();
    this.deleteRecentReportData = {};
  }
}
