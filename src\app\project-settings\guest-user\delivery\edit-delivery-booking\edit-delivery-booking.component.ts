import { Component, TemplateRef, OnInit, Input } from '@angular/core';
import {
  UntypedFormBuilder, UntypedFormGroup, Validators, UntypedFormArray,
} from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { BsModalService, BsModalRef, ModalOptions } from 'ngx-bootstrap/modal';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import moment from 'moment';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { Socket } from 'ngx-socket-io';
import { ProjectSharingService } from '../../../../services/projectSharingService/project-sharing.service';
import { DeliveryService } from '../../../../services/profile/delivery.service';
import { ProjectService } from '../../../../services/profile/project.service';
import { MixpanelService } from '../../../../services/mixpanel.service';
import { ProjectSettingsService } from '../../../../services/project_settings/project-settings.service';

type DateInput = string | number | Date;

@Component({
  selector: 'app-edit-delivery-booking',
  templateUrl: './edit-delivery-booking.component.html',
  })
export class EditDeliveryBookingComponent implements OnInit {
  @Input() data: any;

  @Input() title: string;

  public deliverEditForm: UntypedFormGroup;

  public ProjectId: any;

  public submitted = false;

  public escort = false;

  public formSubmitted = false;

  public editSubmitted = false;

  public modalLoader = false;

  public authUser: any = {};

  public loader = false;

  public gateList: any = [];

  public equipmentList: any = [];

  public defineList: any = [];

  public locationList: any = [];

  public editNdrCompanyDropdownSettings: IDropdownSettings;

  public companyList: any = [];

  public lastId: any = {};

  public formEditSubmitted = false;

  public editNdrDefinableDropdownSettings: IDropdownSettings;

  public editNdrLocationDropdownSettings: IDropdownSettings;

  public currentEditItem: any = {};

  public deliveryId: any;

  public editProjectId: any;

  public ParentCompanyId: any;

  public deliveryEnd: Date;

  public deliveryStart: Date;

  public NDRTimingChanged: boolean = false;

  public editBeforeDFOW = [];

  public editBeforeCompany = [];

  public array3Value = [];

  public isQueuedNDR = false;

  public saveQueuedNDR = false;

  public formEdited = true;

  public craneEquipmentTypeChosen: boolean = false;

  public errtextenable: boolean = false;

  public errequipmentenable: boolean = false;

  public errmemberenable: boolean = false;

  public memberList: any = [];

  public checkform: any = new UntypedFormArray([]);

  public message = '';

  public enableOption = false;

  public valueExists = [];

  public endTime: Date;

  public startTime: Date;

  public deliveryWindowTime;

  public deliveryWindowTimeUnit;

  public seriesOption: number;

  public recurrenceId: number;

  public recurrenceEndDate;

  public minDateOfrecurrenceEndDate;

  public isDisabledDate: boolean = false;

  public selectedLocationId: any;

  public getChosenLocation: any;

  public guestUserId: any;

  public equipmentDropdownSettings: IDropdownSettings;

  public editBeforeEquipment = [];

    public noEquipmentOption = { id: 0, equipmentName: 'No Equipment Needed' };
  public constructor(
    private readonly formBuilder: UntypedFormBuilder,
    public socket: Socket,
    private readonly toastr: ToastrService,
    public router: Router,
    public modalRef: BsModalRef,
    private readonly modalService: BsModalService,
    public modalRef1: BsModalRef,
    private readonly mixpanelService: MixpanelService,
    private readonly deliveryService: DeliveryService,
    public projectService: ProjectService,
    public projectSettingsService: ProjectSettingsService,
    public projectSharingService: ProjectSharingService,
    private readonly option: ModalOptions,
  ) {
    this.ProjectId = +window.atob(localStorage.getItem('guestProjectId'));
    this.ParentCompanyId = +window.atob(localStorage.getItem('guestParentCompanyId'));
    this.guestUserId = +window.atob(localStorage.getItem('guestId'));
    this.editDetailsForm();
    this.getMembers();
    this.getProjectSettings();
  }

  public setDefaultPerson(): void {
    const newMemberList = [
      {
        email: this.authUser.User.email,
        id: this.authUser.id,
        readonly: true,
      },
    ];
    this.deliverEditForm.get('person').patchValue(newMemberList);
  }

  public getNDR(): void {
    this.loader = true;
    const param = {
      DeliveryRequestId: this.deliveryId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService.guestGetNDRData(param).subscribe((res): void => {
      this.currentEditItem = res.data;
      this.deliverEditForm.get('id').setValue(this.currentEditItem.id);
      this.deliverEditForm.get('DeliveryId').setValue(this.currentEditItem.DeliveryId);
      this.deliverEditForm.get('CraneRequestId').setValue(this.currentEditItem.CraneRequestId);
      this.deliverEditForm.get('description').setValue(this.currentEditItem.description);
      this.deliverEditForm
        .get('deliveryDate')
        .setValue(moment(this.currentEditItem.deliveryStart).format('MM/DD/YYYY'));
      this.deliverEditForm
        .get('deliveryStart')
        .setValue(new Date(this.currentEditItem.deliveryStart));
      this.deliverEditForm.get('deliveryEnd').setValue(new Date(this.currentEditItem.deliveryEnd));
      this.deliverEditForm.get('escort').setValue(this.currentEditItem.escort);
      this.deliverEditForm.get('vehicleDetails').setValue(this.currentEditItem.vehicleDetails);
      this.deliverEditForm.get('notes').setValue(this.currentEditItem.notes);
      this.deliverEditForm.get('recurrenceId').setValue(this.currentEditItem?.recurrence?.id);
      this.deliverEditForm.get('recurrence').setValue(this.currentEditItem?.recurrence?.recurrence);
      if (this.currentEditItem?.recurrence?.recurrenceEndDate) {
        this.deliverEditForm
          .get('recurrenceEndDate')
          .setValue(
            moment(this.currentEditItem?.recurrence?.recurrenceEndDate).format('MM/DD/YYYY'),
          );
      }
      this.minDateOfrecurrenceEndDate = new Date(
        this.currentEditItem?.recurrence?.recurrenceEndDate,
      );
      this.deliverEditForm.get('GateId').setValue(this.currentEditItem.gateDetails[0]?.Gate?.id);
      const { isAssociatedWithCraneRequest } = this.currentEditItem;
      if (isAssociatedWithCraneRequest) {
        this.deliverEditForm
          .get('isAssociatedWithCraneRequest')
          .setValue(this.currentEditItem.isAssociatedWithCraneRequest);
      } else {
        this.deliverEditForm.get('isAssociatedWithCraneRequest').setValue(false);
      }
      this.deliverEditForm
        .get('cranePickUpLocation')
        .setValue(this.currentEditItem.cranePickUpLocation);
      this.deliverEditForm
        .get('craneDropOffLocation')
        .setValue(this.currentEditItem.craneDropOffLocation);
      if (this.deliverEditForm.get('isAssociatedWithCraneRequest').value) {
        this.craneEquipmentTypeChosen = true;
      }
      this.setlocation();
      this.setCompany();
      this.setDefine();
      this.setMember();
      this.setEquipment();
      this.openContentModal();
      this.seriesOption = this.modalRef?.content?.seriesOption;
      this.isDisabledDate = this.seriesOption !== 1;
    });
  }

  public convertStart(deliveryDate: Date, startHours: number, startMinutes: number): Date {
    const fullYear = deliveryDate.getFullYear();
    const fullMonth = deliveryDate.getMonth();
    const date = deliveryDate.getDate();
    const deliveryNewStart = new Date(fullYear, fullMonth, date, startHours, startMinutes);
    return deliveryNewStart;
  }

  public requestAutoEditcompleteItems = (text: string): Observable<any> => {
    const param = {
      ProjectId: this.ProjectId,
      search: text,
      ParentCompanyId: this.ParentCompanyId,
    };
    return this.projectSharingService.guestSearchNewMember(param);
  };

  public setCompany(): void {
    const { companyDetails } = this.currentEditItem;
    const newCompanyList = [];
    if (companyDetails !== undefined) {
      companyDetails.forEach((element: { Company: { id: any; companyName: any } }): void => {
        const data = {
          id: element.Company.id,
          companyName: element.Company.companyName,
        };
        newCompanyList.push(data);
      });
    }
    this.editBeforeCompany = newCompanyList;
    this.deliverEditForm.get('companyItems').patchValue(newCompanyList);
  }

  public areDifferentByProperty(a: any[], b: any[], prop: string): boolean {
    const array1 = a.map((x: { [x: string]: any }): any => x[prop]);
    const array2 = b.map((x: { [x: string]: any }): any => x[prop]);
    this.array3Value = array1.concat(array2);
    this.array3Value = [...new Set([...array1, ...array2])];
    return this.array3Value.length !== array1.length;
  }

  public setDefine(): void {
    const { defineWorkDetails } = this.currentEditItem;
    const newDefineList = [];
    if (defineWorkDetails !== undefined) {
      defineWorkDetails.forEach((element: { DeliverDefineWork: { id: any; DFOW: any } }): void => {
        const data = {
          id: element.DeliverDefineWork.id,
          DFOW: element.DeliverDefineWork.DFOW,
        };
        newDefineList.push(data);
      });
    }
    this.editBeforeDFOW = newDefineList;
    this.deliverEditForm.get('defineItems').patchValue(newDefineList);
  }

  public setMember(): void {
    const { memberDetails } = this.currentEditItem;
    const newMemberList = [];
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
      id: this.guestUserId,
    };
    this.projectSharingService.guestGetMemberRole(params).subscribe((res): void => {
      this.authUser = res.data;

      if (memberDetails !== undefined) {
        memberDetails.forEach(
          (element: {
            Member: { User: { firstName: any; lastName: any; email: any }; id: any };
          }): void => {
            let email: string;
            if (element.Member?.User?.firstName != null) {
              email = `${element.Member?.User?.firstName} ${element.Member?.User?.lastName} (${element.Member?.User?.email})`;
            } else {
              email = `(${element.Member?.User?.email})`;
            }
            const data: any = {
              email,
              id: element.Member?.id,
            };
            if (element.Member?.User?.email === this.authUser?.User?.email) {
              data.readonly = true;
            }
            if (element.Member?.User?.email) {
              newMemberList.push(data);
            }
          },
        );
      }
    });
    this.deliverEditForm.get('person').patchValue(newMemberList);
  }

  public ngOnInit(): void {
    const stateValue = this.data;
    this.deliveryId = stateValue.DeliveryRequestId;
    this.seriesOption = stateValue.seriesOption;
    this.isDisabledDate = this.seriesOption !== 1;

    this.router.events.subscribe((e): void => {
      this.modalRef.hide();
    });
  }

  public ngAfterViewInit(): void {
    this.getOverAllGate();
  }

  public getOverAllGate(): void {
    this.modalLoader = true;
    const params = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService
      .guestGateList(params, { isFilter: true, showActivatedAlone: true })
      .subscribe((res): void => {
        this.gateList = res.data;
        this.getOverAllEquipmentforEditNdr();
      });
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }

  public checkDate(data: { target: { value: number } }): void {
    if (data.target.value >= 25) {
      this.deliverEditForm.get('deliveryStart').setValue(new Date());
    }
  }

  public numberOnly(event: { which: any; keyCode: any }): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public onEditSubmitForm(value: any): void {
    if (this.deliverEditForm.dirty || this.NDRTimingChanged) {
      this.formEdited = false;
    }
    if (value) {
      let count = 0;

       if(value.length == this.equipmentList.length -1 && this.equipmentList[0].id == 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
      }
      if(value.length != this.equipmentList.length && this.equipmentList[0].id != 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
      }
      if(value.length == 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
      }
      let hasNoEquipmentOption = false;
      let hasOtherEquipment = false;
      // Check if "No Equipment Needed" (id = 0) is selected
      hasNoEquipmentOption = value.some((item: any) => item.id === 0);

      // Check if other equipment is selected
      hasOtherEquipment = value.some((item: any) => item.id !== 0);

      const previousSelection = this.deliverEditForm.get('EquipmentId').value || [];
      const previousHasOther = previousSelection.some((item: any) => item.id !== 0);

      // Rule 1: If "No Equipment Needed" is selected and other items are selected, keep only "No Equipment Needed"
      if (hasNoEquipmentOption && hasOtherEquipment && !previousHasOther) {
        this.toastr.warning('When "No Equipment Needed" is selected, other equipment options cannot be selected.', 'Warning');
        const noEquipmentOnly = value.filter((item: any) => item.id === 0);
        this.deliverEditForm.get('EquipmentId').setValue(noEquipmentOnly);
        value = noEquipmentOnly;
        hasOtherEquipment = false;
      }

      // Rule 2: If other equipment is already selected and "No Equipment Needed" is now selected, remove it
      if (previousHasOther && hasNoEquipmentOption) {
        this.toastr.warning('When other equipment is selected, "No Equipment Needed" cannot be selected.', 'Warning');
        const filteredSelection = value.filter((item: any) => item.id !== 0);
        this.deliverEditForm.get('EquipmentId').setValue(filteredSelection);
        value = filteredSelection;
        hasNoEquipmentOption = false;
      }
      value.some((val) => {
        const findEquipmentType = this.equipmentList.find(
          (item) => item.id === val.id,
        );

        if (findEquipmentType) {
          this.craneEquipmentTypeChosen = findEquipmentType.PresetEquipmentType.isCraneType;

          if (this.craneEquipmentTypeChosen) {
            count += 1;
            this.deliverEditForm
              .get('isAssociatedWithCraneRequest')
              .setValue(true);
            return true;
          }
          this.deliverEditForm
            .get('isAssociatedWithCraneRequest')
            .setValue(false);
        }
        return false;
      });


      if (count > 0) {
        this.getLastCraneRequestId();
      }
      if (value.length === 0) {
        this.craneEquipmentTypeChosen = false;
        this.deliverEditForm.get('isAssociatedWithCraneRequest').setValue(false);
      }
    }
  }

  public getLastCraneRequestId(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService.guestGetLastCraneRequestId(params).subscribe((response): void => {
      this.craneEquipmentTypeChosen = true;
      const isValueExists = this.deliverEditForm.get('CraneRequestId').value;
      if (!isValueExists) {
        this.deliverEditForm.get('CraneRequestId').setValue(response.lastId?.CraneRequestId);
      }
    });
  }

  public gatecheck(value, menuname): void {
    if (menuname === 'Gate') {
      const index = this.gateList.findIndex((i): boolean => i.id === value);
      if (index !== -1) {
        this.errtextenable = false;
      }
    } else if (menuname === 'Equipment') {
      const index = this.equipmentList.findIndex((i): boolean => i.id === value);
      if (index !== -1) {
        this.errequipmentenable = false;
      }
    } else if (menuname === 'Person') {
      const result = this.memberList.filter((o) => value.some(({ id }) => o.id === id));
      if (value.length !== result.length) {
        this.errmemberenable = true;
      } else {
        this.errmemberenable = false;
      }
    }
  }

  public onEditSubmit(action: string): void {
    if (action === 'save') {
      this.saveQueuedNDR = true;
    } else {
      this.formEditSubmitted = true;
    }
    this.editSubmitted = true;
    this.errtextenable = false;
    const companies = [];
    const persons = [];
    const define = [];
    const equipments = [];
    if (this.deliverEditForm.invalid && action !== 'save') {
      this.formEditSubmitted = false;
      return;
    }
    const formValue = this.deliverEditForm.value;
    if (formValue.EquipmentId.length <= 0) {
      this.toastr.error('Equipment is required');
      this.formEditSubmitted = false;
      return;
    }
    const deliveryDate = new Date(formValue.deliveryDate);
    const startNewDate = new Date(formValue.deliveryStart);
    const startHours = startNewDate.getHours();
    const startMinutes = startNewDate.getMinutes();
    const deliveryStart = this.convertStart(deliveryDate, startHours, startMinutes);
    const endNewDate = new Date(formValue.deliveryEnd);
    const endHours = endNewDate.getHours();
    const endMinutes = endNewDate.getMinutes();
    const deliveryEnd = this.convertStart(deliveryDate, endHours, endMinutes);
    if (this.checkEditDeliveryFutureDate(deliveryStart, deliveryEnd, action)) {
      this.checkFutureDate({
        formValue,
        deliveryStart,
        deliveryEnd,
        companies,
        persons,
        define,
        action,
        equipments,
      });
    } else {
      this.checkRequestStatusAndUserRole({
        formValue,
        deliveryStart,
        deliveryEnd,
        companies,
        persons,
        define,
        action,
        equipments,
      });
    }
  }

  public checkFutureDate({
    formValue,
    deliveryStart,
    deliveryEnd,
    companies,
    persons,
    define,
    action,
    equipments,
  }) {
    const index = this.gateList.findIndex((i): boolean => i.id === formValue.GateId);
    const arr1 = this.memberList;
    const arr2 = formValue.person;
    let index2: number;
    const result = arr1.filter((o) => arr2.some(({ id }) => o.id === id));
    if (formValue.person.length !== result.length) {
      index2 = -1;
    } else {
      index2 = 0;
    }
    if ((index === -1 || index2 === -1) && action !== 'save') {
      if (index === -1) {
        this.errtextenable = true;
      }
      if (index2 === -1) {
        this.errmemberenable = true;
      }

      this.formEditSubmitted = false;
    } else {
      if (index === -1) {
        this.errtextenable = false;
      }
      if (index2 === -1) {
        this.errmemberenable = false;
      }
      this.checkRequestStatusAndUserRole({
        formValue,
        deliveryStart,
        deliveryEnd,
        companies,
        persons,
        define,
        action,
        equipments,
      });
    }
  }

  public checkRequestStatusAndUserRole(params: {
    formValue: any;
    deliveryStart;
    deliveryEnd;
    companies;
    persons;
    define;
    action;
    equipments;
  }): void {
    const {
      formValue,
      deliveryStart,
      deliveryEnd,
      companies,
      persons,
      define,
      action,
      equipments,
    } = params;
    if (this.currentEditItem.status === 'Delivered' && this.authUser.RoleId !== 2) {
      if (
        moment(this.currentEditItem.deliveryStart).format('MM/DD/YYYY')
            !== moment(this.deliverEditForm.get('deliveryDate').value).format('MM/DD/YYYY')
          || new Date(this.currentEditItem.deliveryStart).getTime()
            !== new Date(this.deliverEditForm.get('deliveryStart').value).getTime()
          || new Date(this.currentEditItem.deliveryEnd).getTime()
            !== new Date(this.deliverEditForm.get('deliveryEnd').value).getTime()
      ) {
        this.toastr.error('You are not allowed to change the date/time ');
        this.formEditSubmitted = false;
        return;
      }
    }
    if (this.currentEditItem.status === 'Approved' && this.authUser.RoleId !== 2) {
      if (
        moment(this.currentEditItem.deliveryDate).format('MM/DD/YYYY')
            !== moment(this.deliverEditForm.get('deliveryDate').value).format('MM/DD/YYYY')
          || new Date(this.currentEditItem.deliveryStart).getTime()
            !== new Date(this.deliverEditForm.get('deliveryStart').value).getTime()
          || new Date(this.currentEditItem.deliveryEnd).getTime()
            !== new Date(this.deliverEditForm.get('deliveryEnd').value).getTime()
      ) {
        if (!this.checkEditDeliveryFutureDate(deliveryStart, deliveryEnd, action)) {
          this.toastr.error(
            'Booking not allowed to edit. Please contact the project administrator to edit this booking',
          );
          this.formEditSubmitted = false;
          return;
        }
      }
    }
    if (action === 'submitCurrentNDR' || action === 'submitQueuedNDR') {
      this.updateDelivery({
        editNdrFormValue: formValue,
        deliveryStart,
        deliveryEnd,
        companies,
        persons,
        define,
        action,
        equipments,
      });
    }
  }

  public locationSelected(data): void {
    this.getChosenLocation = this.locationList.filter((obj: any): any => +obj.id === +data.id);
    this.selectedLocationId = this.getChosenLocation[0]?.id;
  }

  public setlocation(): void {
    if (this.currentEditItem.location) {
      this.getChosenLocation = [];
      const data = {
        id: this.currentEditItem.location.id,
        locationPath: this.currentEditItem.location.locationPath,
      };
      this.getChosenLocation.push(data);
      this.deliverEditForm.get('LocationId').patchValue(this.getChosenLocation);
      this.selectedLocationId = this.currentEditItem?.location?.id;
    }
  }

  public updateDelivery(params: {
    editNdrFormValue: any;
    deliveryStart: Date;
    deliveryEnd: Date;
    companies: any[];
    persons: any[];
    define: any[];
    action: string;
    equipments: any[];
  }): void {
    const {
      editNdrFormValue,
      deliveryStart,
      deliveryEnd,
      companies,
      persons,
      define,
      action,
      equipments,
    } = params;

    if (!this.checkStartEnd(deliveryStart, deliveryEnd)) {
      this.throwError('time error');
      return;
    }

    const isValid = this.validateAndExtractDeliveryFormData(
      editNdrFormValue,
      companies,
      persons,
      define,
      equipments,
    );
    if (!isValid) return;

    const payload = this.constructDeliveryPayload(
      editNdrFormValue,
      companies,
      persons,
      define,
      equipments,
      deliveryStart,
      deliveryEnd,
    );

    if (payload.isAssociatedWithCraneRequest) {
      if (!this.deliverEditForm.get('cranePickUpLocation').value || !this.deliverEditForm.get('craneDropOffLocation').value) {
        this.formSubmitted = false;
        this.formEditSubmitted = false;
        this.toastr.error('Please enter Picking From and Picking To');
        return;
      }
      payload.cranePickUpLocation = editNdrFormValue.cranePickUpLocation.trim();
      payload.craneDropOffLocation = editNdrFormValue.craneDropOffLocation.trim();
      payload.CraneRequestId = editNdrFormValue.CraneRequestId;
      payload.requestType = 'deliveryRequestWithCrane';
    }

    if (action === 'submitQueuedNDR') {
      payload.updateQueuedRequest = 1;
      payload.ndrStatus = 'submitQueuedNDR';
      this.submitQueuedNDR(payload);
    } else {
      this.editNDR(payload);
    }
  }

  public validateAndExtractDeliveryFormData(
    formValue,
    companies,
    persons,
    define,
    equipments,
  ): boolean {
    const { companyItems } = formValue;
    const personItems = formValue.person;
    const { defineItems } = formValue;
    const equipmentItems = formValue.EquipmentId;

    if (!companyItems || companyItems.length === 0) {
      this.formReset();
      this.toastr.error('Responsible Company is required');
      return false;
    }

    if (!personItems || personItems.length === 0) {
      this.formReset();
      this.toastr.error('Responsible Person is required');
      return false;
    }

    companyItems.forEach((element: { id: any }) => companies.push(element.id));
    personItems.forEach((element: { id: any }) => persons.push(element.id));

    if (defineItems && defineItems.length > 0) {
      defineItems.forEach((element: { id: any }) => define.push(element.id));
    }

    if (equipmentItems && equipmentItems.length > 0) {
      equipmentItems.forEach((element: { id: any }) => equipments.push(element.id));
    }

    if (this.checkEditDeliveryStringEmptyValues(formValue)) {
      this.formReset();
      return false;
    }

    return true;
  }

  public constructDeliveryPayload(
    formValue,
    companies,
    persons,
    define,
    equipments,
    deliveryStart: Date,
    deliveryEnd: Date,
  ): any {
    return {
      id: formValue.id,
      description: formValue.description,
      companies,
      escort: formValue.escort || false,
      ProjectId: this.ProjectId,
      userId: this.guestUserId,
      GateId: formValue.GateId,
      notes: formValue.notes,
      EquipmentId: equipments,
      vehicleDetails: formValue.vehicleDetails,
      deliveryStart,
      deliveryEnd,
      ParentCompanyId: this.ParentCompanyId,
      persons,
      define,
      isAssociatedWithCraneRequest: formValue.isAssociatedWithCraneRequest,
      requestType: 'deliveryRequest',
      DeliveryId: formValue.DeliveryId,
      seriesOption: this.seriesOption,
      recurrenceId: formValue.recurrenceId,
      LocationId: this.selectedLocationId,
      recurrenceEndDate: formValue.recurrenceEndDate
        ? moment(formValue.recurrenceEndDate).format('YYYY-MM-DD')
        : null,
      recurrenceSeriesStartDate: moment(formValue.deliveryStart).format('YYYY-MM-DD'),
      recurrenceSeriesEndDate: moment(formValue.deliveryEnd).format('YYYY-MM-DD'),
      previousSeriesRecurrenceEndDate: moment(formValue.deliveryStart).add(-1, 'days').format('YYYY-MM-DD'),
      nextSeriesRecurrenceStartDate: moment(formValue.deliveryStart).add(1, 'days').format('YYYY-MM-DD'),
      deliveryStartTime: moment(formValue.deliveryStart).format('HH:mm'),
      deliveryEndTime: moment(formValue.deliveryEnd).format('HH:mm'),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      cranePickUpLocation: null,
      craneDropOffLocation: null,
      CraneRequestId: null,
    };
  }


  public throwError(action: string): void {
    this.editSubmitted = false;
    this.formEditSubmitted = false;
    this.saveQueuedNDR = false;
    if (action === 'time error') {
      this.toastr.error('Please Enter Start time Lesser than End time');
    } else {
      this.toastr.error('Please Enter Future Date.');
    }
  }

  public formReset(): void {
    this.formEditSubmitted = false;
    this.editSubmitted = false;
  }

  public checkEditDeliveryStringEmptyValues(formValue: {
    description: string;
    notes: string;
  }): boolean {
    if (formValue.description.trim() === '') {
      this.toastr.error('Please Enter valid description.', 'OOPS!');
      return true;
    }
    if (formValue.notes) {
      if (formValue.notes.trim() === '') {
        this.toastr.error('Please Enter valid notes.', 'OOPS!');
        return true;
      }
    }
    return false;
  }


  public editNDRSuccess(response: { message: string }): void {
    this.toastr.success(response.message, 'Success');
    this.mixpanelService.addGuestUserMixpanelEvents(' Guest User Edited New Delivery Booking');
    this.formReset();
    this.closeForm();
    this.NDRTimingChanged = false;
  }

  public submitQueuedNDR(payload: any): void {
    if (this.deliverEditForm.get('isAssociatedWithCraneRequest').value) {
      if (
        !this.deliverEditForm.get('cranePickUpLocation').value
          || !this.deliverEditForm.get('craneDropOffLocation').value
      ) {
        this.formSubmitted = false;
        this.formEditSubmitted = false;
        this.toastr.error('Please enter Picking From and Picking To');
        return;
      }
    }
    this.deliveryService.submitQueuedNDR(payload).subscribe({
      next: (submitQueuedNdrResponse: any): void => {
        if (submitQueuedNdrResponse) {
          this.editNDRSuccess(submitQueuedNdrResponse);
        }
      },
      error: (submitQueuedNDRError): void => {
        this.formReset();
        this.NDRTimingChanged = false;
        this.saveQueuedNDR = false;
        if (submitQueuedNDRError.message?.statusCode === 400) {
          this.showError(submitQueuedNDRError);
        } else if (!submitQueuedNDRError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(submitQueuedNDRError.message, 'OOPS!');
        }
      },
    });
  }

  public editNDR(payload: any): void {
    this.projectSharingService.guestEditNDR(payload).subscribe({
      next: (editNdrResponse: any): void => {
        if (editNdrResponse) {
          this.editNDRSuccess(editNdrResponse);
        }
      },
      error: (editNDRHistoryError): void => {
        this.formReset();
        this.NDRTimingChanged = false;
        if (editNDRHistoryError.message?.statusCode === 400) {
          this.showError(editNDRHistoryError);
        } else if (!editNDRHistoryError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(editNDRHistoryError.message, 'OOPS!');
        }
      },
    });
  }

  public deliveryEndTimeChangeDetection(): void {
    this.NDRTimingChanged = true;
    this.onEditSubmitForm(null);
  }

  public changeDate(event: any): void {
    if (!this.modalLoader) {
      const startTime = new Date(event).getHours();
      const minutes = new Date(event).getMinutes();
      this.deliveryEnd = new Date(event);
      this.deliveryEnd.setHours(startTime + 1);
      this.deliveryEnd.setMinutes(minutes);
      this.deliverEditForm.get('deliveryEnd').setValue(this.deliveryEnd);
      this.NDRTimingChanged = true;
    }
    this.onEditSubmitForm(null);
  }

  public close(template: TemplateRef<any>): void {
    if (
      this.deliverEditForm.touched
        || this.NDRTimingChanged
        || (this.deliverEditForm.get('defineItems').dirty
          && this.deliverEditForm.get('defineItems').value
          && this.deliverEditForm.get('defineItems').value.length > 0
          && this.areDifferentByProperty(
            this.editBeforeDFOW,
            this.deliverEditForm.get('defineItems').value,
            'DFOW',
          ))
        || (this.deliverEditForm.get('companyItems').dirty
          && this.deliverEditForm.get('companyItems').value
          && this.deliverEditForm.get('companyItems').value.length > 0
          && this.areDifferentByProperty(
            this.editBeforeCompany,
            this.deliverEditForm.get('companyItems').value,
            'companyName',
          ))
          || (this.deliverEditForm.get('EquipmentId').dirty
        && this.deliverEditForm.get('EquipmentId').value
        && this.deliverEditForm.get('EquipmentId').value.length > 0
        && this.areDifferentByProperty(
          this.editBeforeEquipment,
          this.deliverEditForm.get('EquipmentId').value,
          'Equipment',
        ))
    ) {
      this.openConfirmationModalPopupForEditNDR(template);
    } else {
      this.resetForm('yes');
    }
  }

  public openConfirmationModalPopupForEditNDR(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.formEditSubmitted = false;
      this.editSubmitted = false;
      this.saveQueuedNDR = false;
      this.modalRef.hide();
      this.NDRTimingChanged = false;
    }
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public checkEditDeliveryFutureDate(
    editDeliveryStart: DateInput,
    editDeliveryEnd: DateInput,
    action: string,
  ): boolean {
    const editStartDate = moment(new Date(editDeliveryStart));
    const editCurrentDate = moment()
      .clone()
      .add(this.deliveryWindowTime, this.deliveryWindowTimeUnit);
    const editEndDate = moment(new Date(editDeliveryEnd));
    if (
      (editStartDate.isAfter(editCurrentDate) && editEndDate.isAfter(editCurrentDate))
        || action === 'save'
    ) {
      return true;
    }
    return false;
  }

  public getProjectSettings(): void {
    if (this.ProjectId) {
      const params = {
        ProjectId: this.ProjectId,
      };
      this.projectSettingsService.getGuestProjectSettings(params).subscribe((res): void => {
        const responseData = res.data;
        this.deliveryWindowTime = responseData.deliveryWindowTime;
        this.deliveryWindowTimeUnit = responseData.deliveryWindowTimeUnit;
      });
    }
  }

  public checkStartEnd(
    deliveryStart: string | number | Date,
    deliveryEnd: string | number | Date,
  ): boolean {
    const startDate = new Date(deliveryStart).getTime();
    const endDate = new Date(deliveryEnd).getTime();
    if (startDate < endDate) {
      return true;
    }
    return false;
  }

  public getOverAllEquipmentforEditNdr(): void {
    const editNdrGetEquipmentParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService
      .guestListEquipment(editNdrGetEquipmentParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((editNdrEquipmentResponse): void => {
        this.equipmentList = [this.noEquipmentOption, ...editNdrEquipmentResponse.data];
        this.equipmentDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'equipmentName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
        this.getCompaniesForEditNdr();
      });
  }

  public getCompaniesForEditNdr(): void {
    const getCompaniesForEditNdrParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService
      .guestGetCompanies(getCompaniesForEditNdrParams)
      .subscribe((getCompaniesForEditNdrResponse: any): void => {
        if (getCompaniesForEditNdrResponse) {
          this.companyList = getCompaniesForEditNdrResponse.data;
          this.getDefinableForEditNdr();
          this.editNdrCompanyDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'companyName',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
        }
      });
  }

  public getDefinableForEditNdr(): void {
    const getDefinableForEditNdrParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService
      .guestGetDefinableWork(getDefinableForEditNdrParams)
      .subscribe((getDefinableForEditNdrResponse: any): void => {
        if (getDefinableForEditNdrResponse) {
          const { data } = getDefinableForEditNdrResponse;
          this.defineList = data;
          this.editNdrDefinableDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'DFOW',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            allowSearchFilter: true,
          };
          this.getLocationForEditNdr();
        }
      });
  }

  public getLocationForEditNdr(): void {
    const getLocationForEditNdrParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectSharingService
      .guestGetLocations(getLocationForEditNdrParams)
      .subscribe((getLocationForEditNdrResponse: any): void => {
        if (getLocationForEditNdrResponse) {
          const { data } = getLocationForEditNdrResponse;
          this.locationList = data;
          this.editNdrLocationDropdownSettings = {
            singleSelection: true,
            idField: 'id',
            textField: 'locationPath',
            allowSearchFilter: true,
            closeDropDownOnSelection: true,
          };
          this.getNDR();
        }
      });
  }

  public openContentModal(): void {
    this.modalLoader = false;
  }

  public editDetailsForm(): void {
    this.deliverEditForm = this.formBuilder.group({
      id: [''],
      defineItems: [this.formBuilder.array([])],
      EquipmentId: [this.formBuilder.array([])],
      GateId: ['', Validators.compose([Validators.required])],
      notes: [''],
      CraneRequestId: [''],
      person: [''],
      description: ['', Validators.compose([Validators.required])],
      DeliveryId: [''],
      vehicleDetails: [''],
      deliveryDate: ['', Validators.compose([Validators.required])],
      deliveryEnd: ['', Validators.compose([Validators.required])],
      escort: [false],
      deliveryStart: ['', Validators.compose([Validators.required])],
      companyItems: [this.formBuilder.array([])],
      cranePickUpLocation: [''],
      craneDropOffLocation: [''],
      isAssociatedWithCraneRequest: [false, Validators.compose([Validators.required])],
      recurrenceId: [''],
      LocationId: ['', Validators.compose([Validators.required])],
      recurrence: [''],
      recurrenceEndDate: [''],
    });
    this.deliverEditForm.get('escort').setValue(false);
    const newDate = moment().format('MM/DD/YYYY');
    this.deliverEditForm.get('deliveryDate').setValue(newDate);
    this.deliverEditForm.get('deliveryStart').setValue(this.deliveryStart);
    this.deliverEditForm.get('deliveryEnd').setValue(this.deliveryEnd);
  }

  public closeForm(): void {
    if (this.modalRef1) {
      this.modalRef1.hide();
    }
    this.formEditSubmitted = false;
    this.editSubmitted = false;
    this.saveQueuedNDR = false;
    this.modalRef.hide();
    this.NDRTimingChanged = false;
    this.router.navigate(['/guest-delivery-calendar']);
  }

  public setEquipment(): void {
    const { equipmentDetails } = this.currentEditItem;
    if(equipmentDetails.length && !equipmentDetails[0].Equipment){
        equipmentDetails[0].Equipment = { id: 0, equipmentName: 'No Equipment Needed' }
    }
    const newEquipmentList = [];
    if (equipmentDetails !== undefined) {
      equipmentDetails.forEach((element: { Equipment: { id: any; equipmentName: any } }): void => {
        const data = {
          id: element.Equipment.id,
          equipmentName: element.Equipment.equipmentName,
        };
        newEquipmentList.push(data);
      });
    }
    this.editBeforeEquipment = newEquipmentList;
    this.deliverEditForm.get('EquipmentId').patchValue(newEquipmentList);
  }
}
