import {
  Component, TemplateRef, OnInit, ViewChild,
} from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { GoogleMap } from '@angular/google-maps';
import { Socket } from 'ngx-socket-io';
import { ProfileService } from '../services/profile/profile.service';
import { ProjectService } from '../services/profile/project.service';
import { UpgradewarningComponent } from '../upgradewarning/upgradewarning.component';

declare let google: any;

@Component({
  selector: 'app-plans-and-project',
  templateUrl: './plans-and-project.component.html',
})
export class PlansAndProjectComponent implements OnInit {
  @ViewChild(GoogleMap, { static: false }) map!: GoogleMap;

  public pageNo = 1;

  public pageSize = 25;

  public serialNo = 0;

  public projectList: any = [];

  public loader = true;

  public redirectToStripeLoader = false;

  public modalRef: BsModalRef;

  public deleteSubmitted = false;

  public cancelIndex: string | number;

  public subscriptionStatus = false;

  public totalCount = 0;

  public sort = 'ASC';

  public sortColumn = 'projectName';

  public search = '';

  public editProjectLoader = false;

  public submitted = false;

  public projectDetailsForm: UntypedFormGroup;

  public latitude = 38.897957;

  public longitude = -77.036560;

  public location: string;

  public geoCoder: { geocode: (arg0: { location: { lat: any; lng: any } }, arg1: (results: any, status: any) => void) => void };

  public zoom: number;

  public _map: any;

  public address: any;

  public ProjectId: any;

  public ParentCompanyId: any;

  public modalRef1: BsModalRef;

  public showSearchbar = false;

  public timezoneList: [];

  public subscription: any;

  public mapOptions: google.maps.MapOptions = {
    center: { lat: this.latitude, lng: this.longitude },
    zoomControl: true,
    mapTypeControl: true,
    streetViewControl: false,
    fullscreenControl: true,
    draggable: true,
    zoom: 18,
    mapTypeId: google.maps.MapTypeId.SATELLITE,
  };

  public marker = {
    position: { lat: this.latitude, lng: this.longitude },
    options: {
      animation: google.maps.Animation.DROP,
      draggable: true,
    },
  };

  public constructor(private readonly modalService: BsModalService,
    public bsModalRef: BsModalRef, public socket: Socket,
    private readonly profileService: ProfileService, public projectService: ProjectService,
    private readonly toastr: ToastrService, public router: Router,
    private readonly formBuilder: UntypedFormBuilder) {
    this.projectService.projectParent.subscribe((getResponse): void => {
      this.ParentCompanyId = getResponse.ParentCompanyId;
    });
    this.getTimeZoneList();
    this.projectForm();
    this.getProjects();
    this.projectService.subStatus.subscribe((res): void => {
      if (res !== undefined && res !== '') {
        if (!this.subscriptionStatus) {
          this.subscriptionStatus = res.status;
          if (this.subscriptionStatus) {
            this.openWarning();
          }
        }
      } else {
        this.subscriptionStatus = false;
      }
    });
  }

  public ngOnInit(): void {
    this.socket.on('planChanged', (response: any): void => {
      if (response) {
        this.getProjects();
      }
    });
  }

  public handleToggleKeydown(event: KeyboardEvent, data: any, item: any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.sortByField(data, item);
    }
  }

  public openWarning(): void {
    this.bsModalRef = this.modalService.show(UpgradewarningComponent, { backdrop: 'static', keyboard: false });
    this.bsModalRef.content.closeBtnName = 'Close';
  }

  public openModal(template: TemplateRef<any>, index: any): void {
    this.cancelIndex = index;
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-lg new-delivery-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }

  public openEditProjectModal(template: TemplateRef<any>, item: { id: string | number }): void {
    this.editProjectLoader = true;
    this.ProjectId = item.id;
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'custom-modal' };
    this.modalRef = this.modalService.show(template, data);
    const getProject = this.projectList.filter((project: { id: string | number }): any => +project.id === +item.id);
    if (getProject) {
      this.editProjectDetail(getProject[0]);
    }
  }

  public close(): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  public redirect(path: any, data: { id: any }): void {
    this.router.navigate([`/${path}/${data.id}`]);
  }

  public redirect1(): void {
    this.redirectToStripeLoader = true;
    this.projectService.upgradePlan().subscribe({
      next: (response: any): void => {
        if (response) {
          this.redirectToStripeLoader = false;
          window.open(response.data.url, '_blank');
        }
      },
      error: (getTimeZoneListErr): void => {
        if (getTimeZoneListErr.message?.statusCode === 400) {
          this.showError(getTimeZoneListErr);
        } else if (!getTimeZoneListErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(getTimeZoneListErr.message, 'OOPS!');
        }
      },
    });
  }

  public getTimeZoneList(): void {
    this.projectService.getTimeZoneList().subscribe({
      next: (response: any): void => {
        if (response) {
          this.timezoneList = response.data;
        }
      },
      error: (getTimeZoneListErr): void => {
        if (getTimeZoneListErr.message?.statusCode === 400) {
          this.showError(getTimeZoneListErr);
        } else if (!getTimeZoneListErr.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(getTimeZoneListErr.message, 'OOPS!');
        }
      },
    });
  }

  public getProjects(): void {
    this.loader = true;
    const param = {
      pageNo: this.pageNo,
      pageSize: this.pageSize,
      sort: this.sort,
      sortByField: this.sortColumn,
    };
    const payload = { search: this.search };
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
    this.subscription = this.profileService.getPlansAndProjects(param, payload).subscribe((response: any): void => {
      if (response) {
        const responseData = response.data;
        this.projectList = responseData.rows;
        this.totalCount = responseData.count;
        this.loader = false;
      }
    });
  }

  public cancelSubscription(): void {
    this.deleteSubmitted = true;
    const params = {
      ProjectId: this.projectList[this.cancelIndex].id,
    };
    this.profileService.cancelSubscription(params).subscribe({
      next: (response: any): void => {
        if (response) {
          this.toastr.success(response.data.message, 'Success');
          this.modalRef.hide();
          this.deleteSubmitted = false;
          this.getProjects();
        }
      },
      error: (planAndProjectsError): void => {
        if (planAndProjectsError.message?.statusCode === 400) {
          this.showError(planAndProjectsError);
        } else {
          this.toastr.error(planAndProjectsError.message, 'OOPS!');
          this.deleteSubmitted = false;
        }
      },
    });
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.pageNo = 1;
    this.getProjects();
  }

  public searchProject(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.search = data;
    this.pageNo = 1;
    this.getProjects();
  }

  public showError(err: { message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] } }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.deleteSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public changePageSize(pageSize: number): void {
    this.pageNo = 1;
    this.pageSize = pageSize;
    this.getProjects();
  }

  public sortByField(fieldName: string, sortType: string): void {
    this.sortColumn = fieldName;
    this.sort = sortType;
    this.getProjects();
  }

  public changePageNo(pageNo: number): void {
    this.pageNo = pageNo;
    this.getProjects();
  }

  public projectForm(): void {
    this.projectDetailsForm = this.formBuilder.group(
      {
        projectName: [
          '',
          Validators.compose([
            Validators.required,
          ]),
        ],
        projectLocation: [
          '',
          Validators.compose([
            Validators.required,
          ]),
        ],
        projectLocationLatitude: [
          '',
        ],
        projectLocationLongitude: [
          '',
        ],
        TimeZoneId: [
          '',
          Validators.compose([
            Validators.required,
          ]),
        ],
      },
    );
  }

  public editProjectDetail(projectObject: { projectName: any; projectLocation: any; projectLocationLatitude: any; projectLocationLongitude: any; TimeZoneId: any }): void {
    this.projectForm();
    this.projectDetailsForm.get('projectName').setValue(projectObject.projectName);
    this.projectDetailsForm.get('projectLocation').setValue(projectObject.projectLocation);
    this.projectDetailsForm.get('projectLocationLatitude').setValue(projectObject.projectLocationLatitude);
    this.projectDetailsForm.get('projectLocationLongitude').setValue(projectObject.projectLocationLongitude);
    this.projectDetailsForm.get('TimeZoneId').setValue(projectObject.TimeZoneId);
    this.latitude = Number(projectObject.projectLocationLatitude);
    this.longitude = Number(projectObject.projectLocationLongitude);
    this.address = projectObject.projectLocation;
    this.mapForProjectLocationLoader(projectObject.projectLocationLatitude,
      projectObject.projectLocationLongitude);
    this.editProjectLoader = false;
  }

  public mapForProjectLocationLoader(latitude: any, longitude: any): void {
    this.geoCoder = new google.maps.Geocoder();
    this.projectLocation(latitude, longitude);
  }

  public projectLocation(latitude: string, longitude: string): void {
    const lat = parseFloat(latitude);
    const long = parseFloat(longitude);
    if (navigator.geolocation) {
      this.latitude = lat;
      this.longitude = long;
      this.zoom = 18;
      this.mapOptions.center = {
        lat: this.latitude,
        lng: this.longitude,
      };
      this.marker.position = { lat: this.latitude, lng: this.longitude };
      this.mapOptions.zoom = 18;
      this.getLocationAddress(this.latitude, this.longitude, null);
    }
  }

  public markerDragEnd(event: google.maps.MapMouseEvent) {
    if (event.latLng) {
      this.latitude = event.latLng.lat();
      this.longitude = event.latLng.lng();
      this.mapOptions.center = {
        lat: this.latitude,
        lng: this.longitude,
      };
      this.marker.position = {
        lat: this.latitude,
        lng: this.longitude,
      };
      this.map.panTo(this.marker.position);
      this.getLocationAddress(this.latitude, this.longitude, null);
    }
  }

  public handleProjectAddressLocation(address: { geometry: { location: { lat: () => number; lng: () => number } }; formatted_address: any }): void {
    this.getLocationAddress(address.geometry.location.lat(),
      address.geometry.location.lng(), address.formatted_address);
    this.latitude = address.geometry.location.lat();
    this.longitude = address.geometry.location.lng();
    this.mapOptions.center = {
      lat: this.latitude,
      lng: this.longitude,
    };
    this.marker.position = {
      lat: this.latitude,
      lng: this.longitude,
    };
    this.map.panTo(this.marker.position);
  }

  public getLocationAddress(latitude: number, longitude: number, address: any): void {
    if (address) {
      this.address = address;
      this.projectDetailsForm.get('projectLocation').setValue(this.address);
      this.projectDetailsForm.get('projectLocationLatitude').setValue(latitude);
      this.projectDetailsForm.get('projectLocationLongitude').setValue(longitude);
    } else {
      this.geoCoder.geocode({ location: { lat: latitude, lng: longitude } },
        (results: { formatted_address: any }[], status: string): void => {
          if (status === 'OK') {
            if (results[0]) {
              this.address = results[0].formatted_address;
              this.projectDetailsForm.get('projectLocation').setValue(this.address);
              this.projectDetailsForm.get('projectLocationLatitude').setValue(latitude);
              this.projectDetailsForm.get('projectLocationLongitude').setValue(longitude);
            }
          }
        });
    }
  }

  public checkStringEmptyValues(formValue: { projectName: string }): boolean {
    if (formValue.projectName.trim() === '') {
      return true;
    }
    return false;
  }

  public onSubmit(): void {
    this.submitted = true;
    if (this.projectDetailsForm.invalid) {
      this.submitted = false;
      return;
    }
    if (!this.checkStringEmptyValues(this.projectDetailsForm.value)) {
      const payload = {
        projectName: this.projectDetailsForm.value.projectName.trim(),
        projectLocation: this.projectDetailsForm.value.projectLocation.trim(),
        projectLocationLatitude: this.projectDetailsForm.value.projectLocationLatitude,
        projectLocationLongitude: this.projectDetailsForm.value.projectLocationLongitude,
        ProjectId: this.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
        TimeZoneId: this.projectDetailsForm.value.TimeZoneId,
      };
      this.projectService.editProjectDetail(payload).subscribe({
        next: (response): void => {
          if (response) {
            this.toastr.success(response.message, 'Success');
            this.projectService.updatedProjectNameInSideMenu(this.ProjectId);
            this.getProjects();
            this.submitted = false;
            this.modalRef.hide();
          }
        },
        error: (updateEditProjectErr): void => {
          if (updateEditProjectErr.message?.statusCode === 400) {
            this.showError(updateEditProjectErr);
          } else if (!updateEditProjectErr.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(updateEditProjectErr.message, 'OOPS!');
          }
          this.submitted = false;
        },
      });
    } else {
      this.toastr.error('Please Enter valid Project Name.', 'OOPS!');
      this.submitted = false;
    }
  }


  public alphaNum(event: { which: any; keyCode: any }): boolean {
    const key = (event.which) ? event.which : event.keyCode;
    const firstCondition = (key >= 65 && key <= 90);
    const secondCondition = (key >= 97 && key <= 128);
    const thirdCondition = (key === 32) || (key > 31 && (key < 48 || key > 57));
    if ((key > 31 && (key < 48 || key > 57)) && !firstCondition && !secondCondition
    && !thirdCondition) {
      return false;
    }

    return true;
  }

  public closeEditProject(template: any): void {
    if (this.projectDetailsForm.dirty && this.projectDetailsForm.touched) {
      this.openModalPopup(template);
    } else {
      this.close();
    }
  }

  public openModalPopup(template: any): void {
    let data = {};
    data = { keyboard: false, class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal' };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    }
    if (action === 'yes') {
      this.modalRef1.hide();
      this.projectDetailsForm.reset();
      this.modalRef.hide();
    }
  }
}
