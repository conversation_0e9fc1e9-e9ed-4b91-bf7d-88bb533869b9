<div class="modal-header">
    <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">
      <img src="./assets/images/add-concrete.svg" alt="Delivery" class="me-2" />Edit Concrete Booking
    </h1>
    <button
      type="button"
      class="close ms-auto"
      aria-label="Close"
      (click)="close(cancelConfirmation)"
    >
      <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close" /></span>
    </button>
  </div>
  <div class="modal-body newupdate-alignments taginput--heightfix editconcrete" *ngIf="!modalLoader">
    <form
      name="form"
      class="custom-material-form add-concrete-material-form"
      [formGroup]="concreteRequest"
      novalidate
    >
      <div class="row">
        <div class="col-md-6">
          <div class="form-group mb-0">
            <label class="fs12 fw600" for="description">Description<sup>*</sup></label>
            <textarea id="description"
              class="form-control fs11 radius0"
              rows="2"
              maxlength="150"
              formControlName="description"
              (ngModelChange)="onEditSubmitForm()"
            ></textarea>
            <div class="color-red" *ngIf="submitted && concreteRequest.get('description').errors">
              <small *ngIf="concreteRequest.get('description').errors.required"
                >*Description is Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group pt-0 mt-0">
            <label class="fs12 fw600 mb-0" for="concId">Concrete Booking ID</label>
            <input id="concId"
              type="text"
              class="form-control fs11 material-input p-0 h-25 mt-2"
              disabled="disabled"
              value="{{ concreteRequest.get('ConcreteRequestId').value }}"
            />
          </div>
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-md-6 primary-tooltip">
          <div class="form-group mb-0 mt-2 timezone-formgroup timezone-buttonpadding">
            <label class="fs12 fw600"  for="location">Location<span class="color-red"><sup>*</sup></span>
              <div class="dot-border-info location-border-info tooltip location-tooltip">
                <span class="fw700 info-icon fs12">i</span>
                <span class="tooltiptext tooltiptext-info"
                  >Where will the materials/equipment be installed</span
                >
                <div class="arrow-down"></div>
              </div></label>
            <ng-multiselect-dropdown id="location"
            [placeholder]="'Choose Location'"
            [settings]="locationDropdownSettings"
            [data]="locationDropdown"
            (onSelect)="locationSelected($event)"
            formControlName="LocationId"
            (ngModelChange)="onEditSubmitForm()"
          >
          </ng-multiselect-dropdown>
          <div class="color-red" *ngIf="submitted && concreteRequest.get('LocationId').errors">
            <small *ngIf="concreteRequest.get('LocationId').errors.required"
              >*Location is Required.</small
            >
          </div>
          </div>
        </div>
        <div class="col-md-6 mt-4 additional-location-detail mt-29rem">
          <div class="floating-concrete mb-3">
            <div class="form-group floating-label">
              <input  id="addLoc"
                class="floating-input form-control fs12 px-0"
                type="text"
                placeholder=" "
                formControlName="location"
                (ngModelChange)="onEditSubmitForm()"
              />
              <label class="fs12 fw600 m-0 color-grey11"  for="addLoc"
                >Additional Location Details
              </label>

            </div>
          </div>
        </div>

      </div>
      <div class="row">
      <div class="col-md-6">
        <div class="form-group taginput-height">
          <label class="fs12 fw600" for="resPerson">Responsible Person<sup>*</sup></label>
          <tag-input  id="resPerson"
            [onlyFromAutocomplete]="true"
            [placeholder]="' '"
            formControlName="responsiblePersons"
            [onTextChangeDebounce]="500"
            class="tag-layout"
            [identifyBy]="'id'"
            [displayBy]="'email'"
            (ngModelChange)="onEditSubmitForm()"
            (ngModelChange)="gatecheck($event, 'Person')"
          >
            <tag-input-dropdown
              [showDropdownIfEmpty]="false"
              [displayBy]="'email'"
              [identifyBy]="'id'"
              [appendToBody]="false"
              [autocompleteObservable]="requestAutoEditcompleteItems"
            >
              <ng-template let-item="item" let-index="index">
                {{ item.email }}
              </ng-template>
            </tag-input-dropdown>
          </tag-input>
          <small class="color-red" *ngIf="errmemberenable"
            >Please select an active member,the existing member is deactivated.</small
          >
          <div
            class="color-red"
            *ngIf="submitted && concreteRequest.get('responsiblePersons').errors"
          >
            <small>*Please choose Responsible person.</small>
          </div>
        </div>
      </div>
    </div>
      <div class="row">
        <div class="col-md-6">
          <label class="fs12 fw600" for="date">Date<sup>*</sup></label>
          <div class="input-group mx-0 pt-2">
            <input id="date"
              class="form-control fs12 ps-2 fw500 material-input"
              #dp1="bsDatepicker"
              bsDatepicker
              placeholder="Select Date"
              [bsConfig]="{ isAnimated: true, showWeekNumbers: false, customTodayClass: 'today' }"
              formControlName="concretePlacementDate"
              (ngModelChange)="onEditSubmitForm()"
              [ngClass]="{ disabled: isDisabledDate }"
            />
              <span class="input-group-text">
                <img
                  src="./assets/images/date.svg"
                  class="h-12px"
                  alt="Date"
                  (click)="dp1.toggle()"
                  [attr.aria-expanded]="dp1.isOpen"
                  [ngClass]="{ disabled: isDisabledDate }"
                  (click)="!isDisabledDate ? dp1.toggle() : null"
                  (keydown)="!isDisabledDate ? dp1.toggle() : null"
                />
              </span>
          </div>
          <div
            class="color-red"
            *ngIf="submitted && concreteRequest.get('concretePlacementDate').errors"
          >
            <small *ngIf="concreteRequest.get('concretePlacementDate').errors.required"
              >*Date is Required.</small
            >
          </div>
        </div>

        <div class="col-md-6">
          <div class="d-flex pt-0 mobile-display-block">
            <div class="input-group mb-0 delivery-time">
              <label class="fs12 fw600" for="startime">Placement Start Time<sup>*</sup></label>
              <timepicker id="startime"
                [formControlName]="'concretePlacementStart'"
                (ngModelChange)="changeDate($event)"
                (keypress)="numberOnly($event)"
                class="mt-2"
              >
              </timepicker>
            </div>
            <div
              class="color-red"
              *ngIf="submitted && concreteRequest.get('concretePlacementStart').errors"
            >
              <small *ngIf="concreteRequest.get('concretePlacementStart').errors.required"
                >*Placement Start Time is Required</small
              >
            </div>
            <div class="input-group mb-0 delivery-time">
              <label class="fs12 fw600"  for="compTime">Anticipated Completion Time<sup>*</sup></label>
              <timepicker id="compTime"
                [formControlName]="'concretePlacementEnd'"
                class="mt-2"
                (ngModelChange)="deliveryEndTimeChangeDetection()"
                (keypress)="numberOnly($event)"
              >
              </timepicker>
            </div>
            <div
              class="color-red"
              *ngIf="submitted && concreteRequest.get('concretePlacementEnd').errors"
            >
              <small *ngIf="concreteRequest.get('concretePlacementEnd').errors.required"
                >*Anticipated Completion Time is Required</small
              >
            </div>
          </div>
        </div>
      </div>
      <div class="row" *ngIf="seriesOption !== 1">
        <div class="col-md-6">
          <div class="form-group mt-2">
            <label class="fs12 fw600"  for="recurrence">Recurrence<sup>*</sup></label>
            <input  id="recurrence"
              type="text"
              class="form-control material-input ps-3 disabled"
              placeholder="Recurrence"
              formControlName="recurrence"
            />
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group mt-2">
            <label class="fs12 fw600" for="recEndDate">Recurrence End Date<sup>*</sup></label>
            <div class="input-group mb-3">
              <input  id="recEndDate"
                class="form-control fs10 material-input"
                #dp="bsDatepicker"
                bsDatepicker
                formControlName="recurrenceEndDate"
                placeholder="Recurrence End Date *"
                [bsConfig]="{ isAnimated: true, showWeekNumbers: false, customTodayClass: 'today' }"
                (ngModelChange)="onEditSubmitForm()"
                [minDate]="minDateOfrecurrenceEndDate"
              /><br />

              <div
                class="color-red"
                *ngIf="submitted && concreteRequest.get('recurrenceEndDate').errors"
              >
                <small *ngIf="concreteRequest.get('recurrenceEndDate').errors.required"
                  >*Recurrence End Date is Required.</small
                >
              </div>

                <span class="input-group-text">
                  <img
                    src="./assets/images/date.svg"
                    class="h-12px"
                    alt="Date"
                    (click)="dp.toggle()"
                    (keydown)="dp.toggle()"
                    [attr.aria-expanded]="dp.isOpen"
                  />
                </span>
            </div>
          </div>
        </div>
      </div>
      <div class="row py-3">
        <div class="col-md-3 concrete-header">
          <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Concrete Details</h1>
        </div>
        <div class="col-md-9 line-1">
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 pt-0">
          <div class="form-group company-select mb-0" id="company-select8">
            <label class="fs12 fw600" for="concSup">Concrete Supplier<sup>*</sup></label>
            <ng-multiselect-dropdown id="concSup"
              [placeholder]="' '"
              [settings]="concreteSupplierDropdownSettings"
              [data]="concreteSupplierDropdown"
              formControlName="concreteSupplier"
              (ngModelChange)="onEditSubmitForm()"
              class="mt-2"
            >
            </ng-multiselect-dropdown>
            <div
              class="color-red"
              *ngIf="submitted && concreteRequest.get('concreteSupplier').errors"
            >
              <small *ngIf="concreteRequest.get('concreteSupplier').errors.required"
                >*Concrete Supplier is Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group customised-tag-input mb-0">
            <label class="fs12 fw600 mb-0" for="mixDes">Mix Design</label>
            <tag-input id="mixDes"
              [onlyFromAutocomplete]="false"
              [placeholder]="' '"
              formControlName="mixDesign"
              [(ngModel)]="mixDesignList"
              (ngModelChange)="checkMixDesignDuplication($event)"
              secondaryPlaceholder=" "
              [onTextChangeDebounce]="500"
              class="tag-layout"
              [identifyBy]="'id'"
              [displayBy]="'mixDesign'"
              (ngModelChange)="onEditSubmitForm()"
            >
              <tag-input-dropdown
                [showDropdownIfEmpty]="false"
                [displayBy]="'mixDesign'"
                [identifyBy]="'id'"
                [appendToBody]="false"
                [autocompleteItems]="mixDesignDropdown"
              >
                <ng-template let-item="item" let-index="index">
                  <div class="tag-input-sample fs12">
                    {{ item.mixDesign }}
                  </div>
                </ng-template>
              </tag-input-dropdown>
            </tag-input>
            <p class="color-grey11 fs12 mb-0">*Type and press enter to create the mix design tag</p>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="floating-concrete mt-3">
            <div class="form-group floating-label">
              <input id="orderNum"
                class="floating-input form-control fs12 px-0"
                type="text"
                placeholder=" "
                formControlName="concreteOrderNumber"
                (ngModelChange)="onEditSubmitForm()"
              />
              <label class="fs12 fw600 m-0 color-grey11"  for="orderNum"
                >Order Number
              </label>

            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="floating-concrete mt-3">
            <div class="form-group floating-label">
              <input id="slump"
                class="floating-input form-control fs12 px-0"
                type="text"
                placeholder=" "
                formControlName="slump"
                (ngModelChange)="onEditSubmitForm()"
              />
              <label class="fs12 fw600 m-0 color-grey11"  for="slump"
                >Slump
              </label>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="floating-concrete mt-1">
            <div class="form-group floating-label">
              <input  id="truckSpac"
                class="floating-input form-control fs12 px-0"
                type="text"
                placeholder=" "
                formControlName="truckSpacingHours"
                (ngModelChange)="onEditSubmitForm()"
              />

              <label class="fs12 fw600 m-0 color-grey11" for="truckSpac"
                >Truck Spacing
              </label>

            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="floating-concrete mt-1">
            <div class="form-group floating-label">
              <input id="ordQnt"
                class="floating-input form-control fs12 px-0"
                type="text"
                placeholder=" "
                formControlName="concreteQuantityOrdered"
                (ngModelChange)="onEditSubmitForm()"
              />

              <label class="fs12 fw600 m-0 color-grey11" for="ordQnt"
                >Quantity Ordered (CY)
              </label>

            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="floating-concrete mt-1">
            <div class="form-group floating-label">
              <input id="primeOrd"
                class="floating-input form-control fs12 px-0"
                type="text"
                placeholder=" "
                formControlName="primerForPump"
                (ngModelChange)="onEditSubmitForm()"
              />
              <label class="fs12 fw600 m-0 color-grey11" for="primeOrd"
                >Primer ordered for the Pump
              </label>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <ul
            class="small-switch list-group list-group-horizontal justify-content-start mnb28 mb-1"
            id="switch-control4"
          >
            <span class="fs12 my-auto">Concrete Confirmed</span>
            <li class="fs12 list-group-item border-0 mt-1 p-0 color-grey11">
              <ui-switch
                switchColor="#fff"
                defaultBoColor="#CECECE"
                defaultBgColor="#CECECE"
                formControlName="isConcreteConfirmed"
                (change)="changeConcreteConfirmed($event)"
                class="ms-2"
                (ngModelChange)="onEditSubmitForm()"
              >
              </ui-switch>
            </li>
          </ul>
          <div
            class="fs12 fw700 text-black mt-1"
            *ngIf="concreteRequest.get('isConcreteConfirmed').value === true"
          >
            Confirmed on
            {{ concreteRequest.get('concreteConfirmedOn').value | date : 'medium' }}
          </div>
          <div
            class="color-red"
            *ngIf="submitted && concreteRequest.get('isConcreteConfirmed').errors"
          >
            <small *ngIf="concreteRequest.get('isConcreteConfirmed').errors.required"
              >*Concrete Confirmed is Required.</small
            >
          </div>
        </div>
      </div>
      <div class="row py-3">
        <div class="col-md-2 pump-header">
          <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Pump Details</h1>
        </div>
        <div class="col-md-9">
          <div class="line-1 line-2"></div>
        </div>
      </div>
      <div class="row pb-2">
        <div class="col-md-6">
          <ul
            class="small-switch list-group list-group-horizontal justify-content-start mnb28"
            id="switch-control4"
          >
            <span class="fs12 my-auto">Pump Required</span>
            <li class="fs12 list-group-item border-0 p-0 mt-1 color-grey11">
              <ui-switch
                switchColor="#fff"
                defaultBoColor="#CECECE"
                defaultBgColor="#CECECE"
                class="ms-2"
                formControlName="isPumpRequired"
                (ngModelChange)="onEditSubmitForm()"
              >
              </ui-switch>
            </li>
          </ul>
        </div>
      </div>
      <div
        class="row align-items-end"
        *ngIf="concreteRequest.get('isPumpRequired').value === false ? false : true"
      >
        <div class="col-md-6 mt-2 pump-sizefield">
          <div class="form-group customised-tag-input mb-0">
            <label class="fs12 fw600" for="pumpSize">Pump Size<sup>*</sup></label>
            <tag-input id="pumpSize"
              [onlyFromAutocomplete]="false"
              [placeholder]="' '"
              formControlName="pumpSize"
              [(ngModel)]="pumpSizeList"
              (ngModelChange)="checkPumpSizeDuplication($event)"
              secondaryPlaceholder=" "
              [onTextChangeDebounce]="500"
              class="tag-layout"
              [identifyBy]="'id'"
              [displayBy]="'pumpSize'"
              (ngModelChange)="onEditSubmitForm()"
            >
              <tag-input-dropdown
                [showDropdownIfEmpty]="false"
                [displayBy]="'pumpSize'"
                [identifyBy]="'id'"
                [appendToBody]="false"
                [autocompleteItems]="pumpSizeDropdown"
              >
                <ng-template let-item="item" let-index="index">
                  <div class="tag-input-sample fs12">
                    {{ item.pumpSize }}
                  </div>
                </ng-template>
              </tag-input-dropdown>
            </tag-input>
            <p class="color-grey11 fs12 mb-0">*Type and press enter to create the pump size tag</p>
            <div class="color-red" *ngIf="submitted && concreteRequest.get('pumpSize').errors">
              <small>*Pump Size is Required</small>
            </div>
          </div>
        </div>
        <div class="col-md-6 mt-2">
          <label class="fs12 fw600 m-0" for="pumpOrd">Pump Ordered</label>
          <div class="input-group mb-2">
            <input id="pumpOrd"
              class="form-control fs12 ps-2 fw500 material-input"
              #dp="bsDatepicker"
              bsDatepicker
              placeholder="Pump Ordered"
              [bsConfig]="{ isAnimated: true, showWeekNumbers: false, customTodayClass: 'today' }"
              formControlName="pumpOrderedDate"
              (ngModelChange)="onEditSubmitForm()"
            />
              <span class="input-group-text">
                <img
                  src="./assets/images/date.svg"
                  class="h-12px"
                  alt="Date"
                  (click)="dp.toggle()"
                  (keydown)="dp.toggle()"
                  [attr.aria-expanded]="dp.isOpen"
                />
              </span>
          </div>
          <div class="color-red" *ngIf="submitted && concreteRequest.get('pumpOrderedDate').errors">
            <small *ngIf="concreteRequest.get('pumpOrderedDate').errors.required"
              >*Pump Ordered is Required.</small
            >
          </div>
        </div>
      </div>

      <div class="row" *ngIf="concreteRequest.get('isPumpRequired').value === false ? false : true">
        <div class="col-md-6 mt-0">
          <div class="floating-concreted mt-3">
            <div class="form-group mb-0">
              <label class="fs12 fw600 m-0" for="pumpLoc">Pump Location</label>
              <input id="pumpLoc"
                class="floating-input form-control fs12 px-0"
                type="text"
                placeholder=" "
                formControlName="pumpLocation"
                (ngModelChange)="onEditSubmitForm()"
              />
            </div>
          </div>
          <div class="color-red" *ngIf="submitted && concreteRequest.get('pumpLocation').errors">
            <small *ngIf="concreteRequest.get('pumpLocation').errors.required"
              >*Pump Location is Required.</small
            >
          </div>
        </div>
        <div class="col-md-6 mt-1">
          <div class="form-group">
            <div class="d-flex mobile-display-block pt-0">
              <div class="input-group mb-0 delivery-time">
                <label class="fs12 fw600" for="pumpShow">Pump Show up Time:</label>
                <timepicker id="pumpShow"
                  [formControlName]="'pumpWorkStart'"
                  (ngModelChange)="changeDate1($event)"
                  (keypress)="numberOnly($event)"
                  class="mt-2"
                >
                </timepicker>
              </div>
              <div class="color-red" *ngIf="submitted && concreteRequest.get('pumpWorkStart').errors">
                <small *ngIf="concreteRequest.get('pumpWorkStart').errors.required"
                  >*Pump Show up Time is Required</small
                >
              </div>
              <div class="input-group mb-0 delivery-time">
                <label class="fs12 fw600" for="pumpCom">Pump Completion Time:</label>
                <timepicker id="pumpCom"
                  [formControlName]="'pumpWorkEnd'"
                  class="mt-2"
                  (ngModelChange)="deliveryEndTimeChangeDetection()"
                  (keypress)="numberOnly($event)"
                >
                </timepicker>
              </div>
              <div class="color-red" *ngIf="submitted && concreteRequest.get('pumpWorkEnd').errors">
                <small *ngIf="concreteRequest.get('pumpWorkEnd').errors.required"
                  >*Pump Completion Time is Required</small
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="row pb-2"
        *ngIf="concreteRequest.get('isPumpRequired').value === false ? false : true"
      >
        <div class="col-md-6">
          <div class="form-group mb-0">
            <ul
              class="small-switch list-group list-group-horizontal justify-content-start mnb28 mb-2"
              id="switch-control4"
            >
              <span class="fs12 my-auto">Pump Confirmed</span>
              <li class="fs12 list-group-item border-0 p-0 mt-1 color-grey11">
                <ui-switch
                  switchColor="#fff"
                  defaultBoColor="#CECECE"
                  defaultBgColor="#CECECE"
                  formControlName="isPumpConfirmed"
                  (change)="changePumpConfirmed($event)"
                  class="ms-2"
                  (ngModelChange)="onEditSubmitForm()"
                >
                </ui-switch>
              </li>
            </ul>
            <div
              class="fs12 fw700 text-black ng-star-inserted mb-0"
              *ngIf="concreteRequest.get('pumpConfirmedOn').value"
            >
              Confirmed on
              {{ concreteRequest.get('pumpConfirmedOn').value | date : 'medium' }}
            </div>
          </div>
        </div>
      </div>

      <div class="row py-2 mt-2" *ngIf="currentEditItem.status === 'Completed'">
        <div class="col-md-3 pump-header">
          <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">Other Details</h1>
        </div>
        <div class="col-md-9">
          <div class="line-1 line-2"></div>
        </div>
      </div>
      <div class="row mt-2" *ngIf="currentEditItem.status === 'Completed'">
        <div class="w-50 float-start">
          <div class="col-md-12">
            <label class="fs12 fw600 m-0" for="compTime">Completion time</label>
          </div>
          <div class="col-md-6 float-start">
            <div class="form-group d-flex">
              <select id="hrsDown"
                class="form-control fs12 material-input px-2"
                formControlName="hoursToCompletePlacement"
                (ngModelChange)="onEditSubmitForm()"
              >
                <option value="" disabled selected hidden>Select</option>
                <option *ngFor="let hrs of hoursDropdown" value="{{ hrs }}">
                  {{ hrs }}
                </option>
              </select>
              <label class="fs10 my-auto" for="hrsDown">Hrs</label>
            </div>
          </div>
          <div class="col-md-6 float-start">
            <div class="form-group d-flex">
              <select id="minDown"
                class="form-control fs12 material-input px-2"
                formControlName="minutesToCompletePlacement"
                (ngModelChange)="onEditSubmitForm()"
              >
                <option value="" disabled selected hidden>Select</option>
                <option *ngFor="let mins of minutesDropdown" value="{{ mins }}">
                  {{ mins }}
                </option>
              </select>
              <label class="fs10 my-auto"  for="minDown">Mins</label>
            </div>
          </div>
        </div>
        <div class="col-md-6 float-start">
          <div class="floating-concreted mt-1">
            <div class="form-group">
              <label class="fs12 fw600 m-0"  for="total">Total Cubic Yards Placed</label>
              <input id="total"
                class="floating-input form-control fs12 px-0"
                type="text"
                placeholder=" "
                formControlName="cubicYardsTotal"
                (ngModelChange)="onEditSubmitForm()"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="row pb-2 ms-0 mb-0 mt-2">
        <div class="col-md-12 pl0 ps-0">
          <label class="fs12 fw600" for="notes">Notes</label>
          <textarea  id="notes"
            class="form-control fs11 radius0 mt-1"
            rows="2"
            formControlName="notes"
            (ngModelChange)="onEditSubmitForm()"
          ></textarea>
        </div>
      </div>
      <div class="modal-footer border-0 justify-content-center add-calendar-footer">
        <div class="mt-0 mb15 text-center">
          <button
            class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3 px-2rem"
            type="button"
            (click)="close(cancelConfirmation)"
          >
            Cancel
          </button>
          <button
            class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem"
            [disabled]="(formSubmitted && concreteRequest.valid) || formEdited"
            (click)="onSubmit()"
          >
            <em
              class="fa fa-spinner"
              aria-hidden="true"
              *ngIf="formSubmitted && concreteRequest.valid"
            ></em>
            Submit
          </button>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-body text-center" *ngIf="modalLoader">Loading...</div>

  <!--Confirmation Popup-->
  <div id="confirm-popup6">
    <ng-template #cancelConfirmation>
      <div class="modal-body">
        <div class="text-center my-4">
          <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
            Are you sure you want to cancel?
          </p>
          <button
            class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
            (click)="resetForm('no')"
          >
            No
          </button>
          <button
            class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
            (click)="resetForm('yes')"
          >
            Yes
          </button>
        </div>
      </div>
    </ng-template>
  </div>
