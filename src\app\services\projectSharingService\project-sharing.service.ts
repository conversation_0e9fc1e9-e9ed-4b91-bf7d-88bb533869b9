import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, BehaviorSubject } from 'rxjs';
import { ApiService } from '../api_base/api.service';


@Injectable({
  providedIn: 'root'
  })
export class ProjectSharingService {
  private readonly projectDetails = new BehaviorSubject<any>({});

  constructor(private readonly api: ApiService, public router: Router) { }

  public guestLogin(payload): Observable<any> {
    return this.api.post('guest_user/create_guest_user', payload);
  }

  public alreadyVisited(payload): Observable<any> {
    return this.api.post('guest_user/alreadyVisited', payload);
  }

  public getCompanyList(params): Observable<any> {
    return this.api.get(`guest_user/company_list/${params.ProjectId}/${params.ParentCompanyId}`);
  }

  public checkIsGuest(payload): Observable<any> {
    return this.api.post('guest_user/guestUser_detail', payload);
  }

  public lastDeliverId(payload): Observable<any> {
    return this.api.post('guest_user/last_deliveryId', payload);
  }

  public listAllMember(params): Observable<any> {
    return this.api.get(`guest_user/list_members/${params.ProjectId}/${params.ParentCompanyId}`);
  }

  public getEventNDR(params, payload): Observable<any> {
    return this.api.post(`guest_user/guest_event_NDR/${params.ProjectId}/${params.void}`, payload);
  }

  public getDeliveryRequestWithCraneEquipmentType(params, payload): Observable<any> {
    return this.api.post(
      `guest_user/guest_crane_associated_request/${params.ProjectId}/${params.void}`,
      payload,
    );
  }

  public getConcreteRequest(params, payload): Observable<any> {
    return this.api.post(
      `guest_user/guest_concrete_request/${params.ProjectId}/${params.void}`,
      payload,
    );
  }

  public getLocations(params): Observable<any> {
    return this.api.get('guest_user/guest_get_locations', params);
  }

  public guestGateList(params, payload): Observable<any> {
    return this.api.post(`guest_user/gate_list/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.ParentCompanyId}`, payload);
  }

  public guestListEquipment(params, payload): Observable<any> {
    return this.api.post(`guest_user/equipment_list/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.ParentCompanyId}`, payload);
  }

  public guestGetCompanies(params): Observable<any> {
    return this.api.get(`guest_user/get_newcompanies/${params.ProjectId}/${params.ParentCompanyId}`);
  }

  public guestGetDefinableWork(params): Observable<any> {
    return this.api.get(`guest_user/get_definable_work/${params.ProjectId}/${params.ParentCompanyId}`);
  }

  public guestGetLocations(params): Observable<any> {
    return this.api.get('guest_user/get_locations', params);
  }

  public guestGetLastCraneRequestId(params): Observable<any> {
    return this.api.get(`guest_user/get_last_crane_request_id/${params.ProjectId}/${params.ParentCompanyId}`);
  }

  public guestGetTimeZoneList(): Observable<any> {
    return this.api.get('guest_user/get_timezone_list');
  }

  public guestGetSingleProject(params): Observable<any> {
    return this.api.get(`guest_user/get_single_project/${params.ProjectId}`);
  }

  public guestGetMemberRole(params): Observable<any> {
    return this.api.get(`guest_user/get_user_role/${params.ProjectId}/${params.ParentCompanyId}/${params.id}`);
  }

  public guestSearchNewMember(params): Observable<any> {
    return this.api.get(
      `guest_user/search_member/${params.ProjectId}/${params.search}/${params.ParentCompanyId}`,
    );
  }

  public guestCreateNDR(payload): Observable<any> {
    return this.api.post('guest_user/new_delivery_request/', payload);
  }

  public guestListCraneEquipment(params, payload): Observable<any> {
    const p = payload;
    return this.api.post(`guest_user/crane_equipment_list/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.ParentCompanyId}`, p);
  }

  public guestCreateCraneRequest(payload): Observable<any> {
    return this.api.post('guest_user/create_crane_request/', payload);
  }

  public guestGetConcreteRequestDropdownData(payload): Observable<any> {
    return this.api.get('guest_user/concrete_dropdown_detail', payload);
  }

  public guestCreateConcreteRequest(payload): Observable<any> {
    return this.api.post('guest_user/create_concrete_request/', payload);
  }

  public guestGetNDRData(params): Observable<any> {
    return this.api.get(
      `guest_user/guest_single_NDR/${params.DeliveryRequestId}/${params.ParentCompanyId}`,
    );
  }

  public guestGetEquipmentCraneRequest(params): Observable<any> {
    return this.api.get(
      `guest_user/guest_single_crane_request/${params.CraneRequestId}/${params.ProjectId}/${params.ParentCompanyId}`,
    );
  }

  public guestGetConcreteRequestDetail(payload): Observable<any> {
    return this.api.get(
      `guest_user/guest_single_Concrete_request/${payload.ConcreteRequestId}/${payload.ProjectId}/${payload.ParentCompanyId}`,
    );
  }

  public guestEditNDR(payload): Observable<any> {
    return this.api.post('guest_user/edit_request', payload);
  }

  public guestEditCraneRequest(payload): Observable<any> {
    return this.api.post('guest_user/edit_crane_request', payload);
  }

  public guestEditConcreteRequest(payload): Observable<any> {
    return this.api.post('guest_user/edit_concrete_request', payload);
  }

  public guestAddAttachement(params, payload): Observable<any> {
    return this.api.requestImage(
      `guest_user/add_attachement/${params.DeliveryRequestId}/${params.ParentCompanyId}/${params.userId}`,
      payload,
    );
  }

  public guestCreateComment(payload): Observable<any> {
    return this.api.post('guest_user/create_comment/', payload);
  }

  public guestAddCraneRequestAttachment(params, payload): Observable<any> {
    return this.api.requestImage(
      `guest_user/add_crane_request_attachement/${params.CraneRequestId}/${params.ParentCompanyId}/${params.ProjectId}/${params.userId}`,
      payload,
    );
  }

  public guestCreateCraneRequestComment(payload): Observable<any> {
    return this.api.post('guest_user/create_crane_request_comment/', payload);
  }

  public guestAddConcreteRequestAttachment(params, payload): Observable<any> {
    return this.api.requestImage(
      `guest_user/add_concrete_request_attachment/${params.ConcreteRequestId}/${params.ParentCompanyId}/${params.ProjectId}/${params.userId}`,
      payload,
    );
  }

  public guestCreateConcreteRequestComment(payload): Observable<any> {
    return this.api.post('guest_user/create_concrete_request_comment/', payload);
  }

  public isRequestToMember(payload): Observable<any> {
    return this.api.post('guest_user/is_request_to_member', payload);
  }

  public updateGuestMember(payload): Observable<any> {
    return this.api.put('guest_user/update_guest_member', payload);
  }

  public addGuestAsMember(payload): Observable<any> {
    return this.api.put('member/add_guest_as_member', payload);
  }

  public rejectGuestRequest(payload): Observable<any> {
    return this.api.put('member/reject_guest_request', payload);
  }
}
