import { ToastrModule } from 'ngx-toastr';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import * as Highcharts from 'highcharts';

import { BarChartComponent } from './bar-chart.component';
import { AuthGuard } from '../auth.guard';

// Mock the entire Highcharts module
jest.mock('highcharts', () => {
  const actual = jest.requireActual('highcharts');
  return {
    ...actual,
    chart: jest.fn(),
    hasOwnProperty: jest.fn().mockReturnValue(true), // <- this line might be key
  };
});


describe('BarChartComponent', () => {
  let component: BarChartComponent;
  let fixture: ComponentFixture<BarChartComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [BarChartComponent],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        ToastrModule.forRoot(),
        RouterTestingModule.withRoutes([]),
        HttpClientTestingModule,
      ],
      providers: [AuthGuard],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(BarChartComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.barChartData).toBeUndefined();
    expect(component.baroptions).toBeUndefined();
  });

  describe('fetchData', () => {
    const mockBarOptions = {
      title: 'Test Chart',
      categories: ['Jan', 'Feb', 'Mar'],
      data: [10, 20, 30],
      data1: [15, 25, 35],
      data2: [5, 15, 25],
      data3: [8, 18, 28],
    };

    beforeEach(() => {
      component.baroptions = mockBarOptions;
      component.fetchData();
    });

    it('should generate correct chart data structure', () => {
      expect(component.barChartData).toBeDefined();
      expect(component.barChartData.chart.type).toBe('column');
      expect(component.barChartData.title.text).toBe(mockBarOptions.title);
      expect(component.barChartData.xAxis.categories).toEqual(mockBarOptions.categories);
    });

    it('should call Highcharts.chart with correct parameters', () => {
      expect(Highcharts.chart).toHaveBeenCalledWith('containers', component.barChartData);
    });

    it('should include all series data', () => {
      expect(component.barChartData.series.length).toBe(4);
      expect(component.barChartData.series[0].name).toBe('Delivery Bookings');
      expect(component.barChartData.series[1].name).toBe('Crane Bookings');
      expect(component.barChartData.series[2].name).toBe('Concrete Bookings');
      expect(component.barChartData.series[3].name).toBe('Inspection Bookings');
    });

    it('should set correct colors for each series', () => {
      expect(component.barChartData.series[0].color).toBe('#28A2F4');
      expect(component.barChartData.series[1].color).toBe('#FFCE2D');
      expect(component.barChartData.series[2].color).toBe('#838383');
      expect(component.barChartData.series[3].color).toBe('#A234FD');
    });

    it('should set correct data values for each series', () => {
      expect(component.barChartData.series[0].data).toEqual(mockBarOptions.data);
      expect(component.barChartData.series[1].data).toEqual(mockBarOptions.data1);
      expect(component.barChartData.series[2].data).toEqual(mockBarOptions.data2);
      expect(component.barChartData.series[3].data).toEqual(mockBarOptions.data3);
    });

    it('should not generate chart data when baroptions is undefined', () => {
      component.barChartData = undefined; // Reset chart data explicitly
      component.baroptions = undefined;
      (Highcharts.chart as jest.Mock).mockClear(); // Clear previous calls
    
      component.fetchData();
    
      expect(component.barChartData).toBeUndefined();
      expect(Highcharts.chart).not.toHaveBeenCalled();
    });

    it('should set correct chart options', () => {
      expect(component.barChartData.chart.height).toBe('190px');
      expect(component.barChartData.chart.marginBottom).toBe(30);
      expect(component.barChartData.credits.enabled).toBe(false);
      expect(component.barChartData.plotOptions.column.pointPadding).toBe(0.2);
      expect(component.barChartData.plotOptions.column.borderWidth).toBe(0);
      expect(component.barChartData.plotOptions.series.pointWidth).toBe(6);
    });
  });

  describe('ngOnChanges', () => {
    it('should call fetchData when changes occur', () => {
      const fetchDataSpy = jest.spyOn(component, 'fetchData');
      component.ngOnChanges();
      expect(fetchDataSpy).toHaveBeenCalled();
    });
  });
});
