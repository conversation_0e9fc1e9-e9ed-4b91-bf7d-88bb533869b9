import { Component, OnInit } from '@angular/core';
import { merge, Subject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { ProjectService } from '../../services/profile/project.service';
import { DeliveryService } from '../../services/profile/delivery.service';

@Component({
  selector: 'app-concrete-detail-content',
  templateUrl: './concrete-detail-content.component.html',
})
export class ConcreteDetailContentComponent implements OnInit {
  public loader: boolean;

  public ConcreteRequestId: any;

  public ParentCompanyId: any;

  public ProjectId: any;

  public concreteRequest: any;

  private readonly unsubscribe$ = new Subject<void>();

  public constructor(
    private readonly deliveryService: DeliveryService,
    public projectService: ProjectService,
  ) {
    this.deliveryService.showFooterButtonsVisible(false);
    this.projectService.projectParent.subscribe((response3): void => {
      if (response3 !== undefined && response3 !== null && response3 !== '') {
        this.ParentCompanyId = response3.ParentCompanyId;
        this.ProjectId = response3.ProjectId;
      }
    });
  }

  public ngOnInit(): void {
    merge(
      this.deliveryService.EditConcreteRequestId.pipe(
        filter((id) => !!id),
      ),
      this.deliveryService.fetchConcreteData.pipe(
        filter(() => !!this.ConcreteRequestId),
      ),
      this.deliveryService.fetchConcreteData1.pipe(
        filter(() => !!this.ConcreteRequestId),
      ),
    )
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((value) => {
        if (typeof value === 'string' || typeof value === 'number') {
          this.ConcreteRequestId = value;
        }

        this.loader = true;
        this.getConcreteRequest();
      });
  }

  public getResponsiblePeople(object): any {
    if (object?.firstName && object?.lastName) {
      const string = `${object.firstName} ${object.lastName}`;
      const matches = string.match(/\b(\w)/g);
      const acronym = matches.join('').toUpperCase();
      return acronym;
    }
    return 'UU';
  }

  public getConcreteRequest(): void {
    const param = {
      ConcreteRequestId: this.ConcreteRequestId,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    if (this.ProjectId && this.ParentCompanyId) {
      this.deliveryService.getConcreteRequestDetail(param).subscribe((res): void => {
        this.concreteRequest = res.data;
        this.loader = false;
        this.deliveryService.showFooterButtonsVisible(true);
      });
    }
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
