import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Router } from '@angular/router';
import * as FileSaver from 'file-saver';
import * as XLSX from 'xlsx';
import { ApiService } from '../api_base/api.service';

const EXCEL_TYPE = 'application/vnd.openxmlformats';
const PDF_TYPE = 'application/pdf';
const EXCEL_EXTENSION = '.xlsx';
const PDF_EXTENSION = '.pdf';
@Injectable({
  providedIn: 'root',
  })
export class DeliveryService {
  public DeliveryRequestId: BehaviorSubject<any> = new BehaviorSubject<any>('');
  public InspectionRequestId: BehaviorSubject<any> = new BehaviorSubject<any>('');
  public AllCalendarRespData: BehaviorSubject<any> = new BehaviorSubject<any>([]);
  AllCalendarRespData$ = this.AllCalendarRespData.asObservable();
  public AllCalendarPermanentRespData: BehaviorSubject<any> = new BehaviorSubject<any>([]);
  AllCalendarPermanentRespData$ = this.AllCalendarPermanentRespData.asObservable();

  public AllCalendarStatusCard: BehaviorSubject<any> = new BehaviorSubject<any>([]);
  AllCalendarStatusCard$ = this.AllCalendarStatusCard.asObservable();

  public dataChanged = new BehaviorSubject<boolean>(false);
  public dataChanged$ = this.dataChanged.asObservable();

  public selectedBookingTypes: BehaviorSubject<string[]> = new BehaviorSubject<string[]>([]);
  public selectedBookingTypes$ = this.selectedBookingTypes.asObservable();

  public EditCraneRequestId: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public EditConcreteRequestId: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public refresh: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public refresh1: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public fetchData: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public fetchData1: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public fetchConcreteData: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public fetchConcreteData1: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public loginUser: BehaviorSubject<any> = new BehaviorSubject<any>({});

  public getCurrentStatus: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public getCurrentCraneRequestStatus: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public getCurrentConcreteRequestStatus: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public completeConcreteRequest: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public footerDropdown: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public showFooterButtons: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public refreshCount: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public refreshnotifyCount: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public isQueuedNDR: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public inspectionUpdated: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public inspectionUpdated1: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public getInspectionCurrentStatus: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public isQueuedInspectionNDR: BehaviorSubject<any> = new BehaviorSubject<any>('');

  public constructor(private readonly api: ApiService, public router: Router) {
    // constructor
  }

  public updateLoginUser(data): any {
    return this.loginUser.next(data);
  }

  public updatedDeliveryId(data): any {
    return this.DeliveryRequestId.next(data);
  }

  public updatedInspectionId(data): any {
    return this.InspectionRequestId.next(data);
  }

  public updateAllCalendarRespData(data): any {
    return this.AllCalendarRespData.next(data)
  }

  public updateAllCalendarPermanentRespData(data): any {
    return this.AllCalendarPermanentRespData.next(data)
  }

  public updateStatusCardValues(data): any {
    return this.AllCalendarStatusCard.next(data)
  }

  public updatedEditCraneRequestId(data): any {
    return this.EditCraneRequestId.next(data);
  }

  public updatedEditConcreteRequestId(data): any {
    return this.EditConcreteRequestId.next(data);
  }

  public completeConcreteRequestStatus(data): any {
    return this.completeConcreteRequest.next(data);
  }

  public footerDropdownStatus(data): any {
    return this.footerDropdown.next(data);
  }

  public showFooterButtonsVisible(data): any {
    return this.showFooterButtons.next(data);
  }

  public updatedRefreshCount(data): any {
    return this.refreshCount.next(data);
  }

  public notificationRefreshCount(data): any {
    return this.refreshnotifyCount.next(data);
  }

  public updateStateOfNDR(data): any {
    return this.isQueuedNDR.next(data);
  }

  public updatedHistory(data, action): any {
    const nextData = data;
    nextData.action = action;
    return this.refresh.next(nextData);
  }

  public updatedHistory1(data, action): any {
    const nextData = data;
    nextData.action = action;
    return this.refresh1.next(nextData);
  }

  public updateCraneRequestHistory(data, action): any {
    const nextData = data;
    nextData.action = action;
    return this.fetchData.next(nextData);
  }

  public updateCraneRequestHistory1(data, action): any {
    const nextData = data;
    nextData.action = action;
    return this.fetchData1.next(nextData);
  }

  public updateConcreteRequestHistory(data, action): any {
    const nextData = data;
    nextData.action = action;
    return this.fetchConcreteData.next(nextData);
  }

  public updateConcreteRequestHistory1(data, action): any {
    const nextData = data;
    nextData.action = action;
    return this.fetchConcreteData1.next(nextData);
  }

  public updatedInspectionHistory(data, action): any {
    const nextData = data;
    nextData.action = action;
    return this.inspectionUpdated.next(nextData);
  }

  public updatedInspectionHistory1(data, action): any {
    const nextData = data;
    nextData.action = action;
    return this.inspectionUpdated1.next(nextData);
  }

  public updatedCurrentStatus(data): any {
    return this.getCurrentStatus.next(data);
  }

  public updatedCurrentCraneRequestStatus(data): any {
    return this.getCurrentCraneRequestStatus.next(data);
  }

  public updatedCurrentConcreteRequestStatus(data): any {
    return this.getCurrentConcreteRequestStatus.next(data);
  }

  public updatedInspectionCurrentStatus(data): any {
    return this.getInspectionCurrentStatus.next(data);
  }

  public updateStateOfInspectionNDR(data): any {
    return this.isQueuedInspectionNDR.next(data);
  }

  public listNDR(params, payload): Observable<any> {
    return this.api.post(
      `delivery/list_NDR/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public listInspectionNDR(params, payload): Observable<any> {
    return this.api.post(
      `inspection/list_NDR/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public getCraneRequestList(params, payload): Observable<any> {
    return this.api.post(
      `crane_request/get_crane_request_list/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public getVoidList(params, payload): Observable<any> {
    return this.api.post(
      `void/get_void_list/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public getConcreteRequestList(params, payload): Observable<any> {
    return this.api.post(
      `concrete_request/get_concrete_request_list/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.void}`,
      payload,
    );
  }

  public gateList(params): Observable<any> {
    return this.api.get(`gates/gate_all_list/${params.ProjectId}`);
  }

  public listNotification(params, data): Observable<any> {
    return this.api.post(
      `notification/list_notification/${params.pageSize}/${params.pageNo}`,
      data,
    );
  }

  public ReadallNotification(params): Observable<any> {
    return this.api.get(`delivery/readall_notification/?ProjectId=${params.ProjectId}`);
  }

  public readallNotifications(params): Observable<any> {
    return this.api.get(`delivery/markall_notification/${params.ProjectId}`);
  }

  public deleteNotification(data): Observable<any> {
    return this.api.get(
      `notification/delete_notification/?id=${data.id}&ParentCompanyId=${data.ParentCompanyId}`,
    );
  }

  public searchNewMember(params): Observable<any> {
    return this.api.get(
      `member/search_member/${params.ProjectId}/${params.search}/${params.ParentCompanyId}`,
    );
  }

  public searchNewMemberForAutoApprove(params): Observable<any> {
    return this.api.get(
      `member/search_member_auto_approve/${params.ProjectId}/${params.search}/${params.ParentCompanyId}`,
    );
  }

  public setReadNotification(params): Observable<any> {
    return this.api.get(
      `notification/read_notification/?id=${params.id}&ParentCompanyId=${params.ParentCompanyId}`,
    );
  }

  public createNDR(payload): Observable<any> {
    return this.api.post('delivery/new_request/', payload);
  }

  public createInspectionNDR(payload): Observable<any> {
    return this.api.post('inspection/new_request/', payload);
  }

  public createCraneRequest(payload): Observable<any> {
    return this.api.post('crane_request/create_crane_request/', payload);
  }

  public createConcreteRequest(payload): Observable<any> {
    return this.api.post('concrete_request/create_concrete_request/', payload);
  }

  public editNDR(payload): Observable<any> {
    return this.api.post('delivery/edit_request/', payload);
  }

  public editInspectionNDR(payload): Observable<any> {
    return this.api.post('inspection/edit_request/', payload);
  }

  public editCraneRequest(payload): Observable<any> {
    return this.api.post('crane_request/edit_crane_request/', payload);
  }

  public editConcreteRequest(payload): Observable<any> {
    return this.api.post('concrete_request/edit_concrete_request/', payload);
  }

  public updateRequest(payload): Observable<any> {
    return this.api.post('delivery/edit_multiple_request/', payload);
  }


  public updateInspectionRequest(payload): Observable<any> {
    return this.api.post('inspection/edit_multiple_request/', payload);
  }

  public updateCraneRequest(payload): Observable<any> {
    return this.api.post('crane_request/edit_multiple_request/', payload);
  }

  public updateConcreteRequest(payload): Observable<any> {
    return this.api.post('concrete_request/edit_multiple_request/', payload);
  }

  public editQueuedNDRForm(payload): Observable<any> {
    return this.api.post('delivery/edit_queued_request/', payload);
  }

  public editQueuedInspectionNDRForm(payload): Observable<any> {
    return this.api.post('inspection/edit_queued_request/', payload);
  }

  public submitQueuedNDR(payload): Observable<any> {
    return this.api.post('delivery/update_to_current_NDR/', payload);
  }

  public attachement(params, payload): Observable<any> {
    return this.api.requestImage(
      `attachement/add_attachement/${params.DeliveryRequestId}/${params.ParentCompanyId}`,
      payload,
    );
  }

  public inspectionAttachement(params, payload): Observable<any> {
    return this.api.requestImage(
      `attachement/add_inspection_attachement/${params.InspectionRequestId}/${params.ParentCompanyId}`,
      payload,
    );
  }

  public addCraneRequestAttachment(params, payload): Observable<any> {
    return this.api.requestImage(
      `crane_request_attachment/add_crane_request_attachement/${params.CraneRequestId}/${params.ParentCompanyId}/${params.ProjectId}`,
      payload,
    );
  }

  public addConcreteRequestAttachment(params, payload): Observable<any> {
    return this.api.requestImage(
      `concrete_request_attachment/add_concrete_request_attachment/${params.ConcreteRequestId}/${params.ParentCompanyId}/${params.ProjectId}`,
      payload,
    );
  }

  public removeAttachement(params): Observable<any> {
    return this.api.get(`attachement/remove_attachement/${params.id}/${params.ParentCompanyId}`);
  }

  public removeInspectionAttachement(params): Observable<any> {
    return this.api.get(`attachement/remove_inspection_attachement/${params.id}/${params.ParentCompanyId}`);
  }

  public removeCraneRequestAttachement(params): Observable<any> {
    return this.api.get(
      `crane_request_attachment/remove_crane_request_attachement/${params.id}/${params.ParentCompanyId}/${params.ProjectId}`,
    );
  }

  public removeConcreteRequestAttachment(params): Observable<any> {
    return this.api.get(
      `concrete_request_attachment/remove_concrete_request_attachment/${params.id}/${params.ParentCompanyId}/${params.ProjectId}`,
    );
  }

  public getAttachement(params): Observable<any> {
    return this.api.get(
      `attachement/get_attachement/${params.DeliveryRequestId}/${params.ParentCompanyId}`,
    );
  }

  public getInspectionAttachement(params): Observable<any> {
    return this.api.get(
      `attachement/get_inspection_attachement/${params.InspectionRequestId}/${params.ParentCompanyId}`,
    );
  }

  public getCraneRequestAttachements(params): Observable<any> {
    return this.api.get(
      `crane_request_attachment/get_crane_request_attachements/${params.CraneRequestId}/${params.ParentCompanyId}/${params.ProjectId}`,
    );
  }

  public getConcreteRequestAttachments(params): Observable<any> {
    return this.api.get(
      `concrete_request_attachment/get_concrete_request_attachments/${params.ConcreteRequestId}/${params.ParentCompanyId}/${params.ProjectId}`,
    );
  }

  public getHistory(payload): Observable<any> {
    return this.api.get(
      `history/get_history/${payload.DeliveryRequestId}/${payload.ParentCompanyId}`,
    );
  }

  public getInspectionHistory(payload): Observable<any> {
    return this.api.get(
      `history/get_inspection_history/${payload.inspectionRequestId}/${payload.ParentCompanyId}`,
    );
  }

  public getDeliveryRequestComment(payload): Observable<any> {
    return this.api.get(
      `comment/get_comment/${payload.DeliveryRequestId}/${payload.ParentCompanyId}/${payload.ProjectId}`,
    );
  }

  public getInspectionRequestComment(payload): Observable<any> {
    return this.api.get(
      `comment/get_inspection_comment/${payload.InspectionRequestId}/${payload.ParentCompanyId}/${payload.ProjectId}`,
    );
  }

  public getConcreteRequestDropdownData(payload): Observable<any> {
    return this.api.get('concrete_request/concrete_dropdown_detail', payload);
  }

  public getConcreteRequestDetail(payload): Observable<any> {
    return this.api.get(
      `concrete_request/get_single_Concrete_request/${payload.ConcreteRequestId}/${payload.ProjectId}/${payload.ParentCompanyId}`,
    );
  }

  public getCraneRequestComment(payload): Observable<any> {
    return this.api.get(
      `crane_request_comment/get_crane_request_comments/${payload.CraneRequestId}/${payload.ParentCompanyId}/${payload.ProjectId}`,
    );
  }

  public getConcreteRequestComment(payload): Observable<any> {
    return this.api.get(
      `concrete_request_comment/get_concrete_request_comments/${payload.ConcreteRequestId}/${payload.ParentCompanyId}/${payload.ProjectId}`,
    );
  }

  public getCraneRequestHistory(payload): Observable<any> {
    return this.api.get(
      `crane_request_history/get_crane_request_histories/${payload.CraneRequestId}/${payload.ParentCompanyId}/${payload.ProjectId}`,
    );
  }

  public getConcreteRequestHistory(payload): Observable<any> {
    return this.api.get(
      `concrete_request_history/get_concrete_request_histories/${payload.ConcreteRequestId}/${payload.ParentCompanyId}/${payload.ProjectId}`,
    );
  }

  public getDefinable(params, payload): Observable<any> {
    return this.api.post(
      `definable/get_definable/${params.ProjectId}/${params.pageSize}/${params.pageNo}/${params.sort}/${params.export}/${params.sortByField}`,
      payload,
    );
  }

  public exportDefinable(params): Observable<any> {
    return this.api.getExcel(
      `definable/export_definable/${params.ProjectId}/${params.sort}/${params.ParentCompanyId}`,
    );
  }

  public importDefinable(params, payload): Observable<any> {
    return this.api.requestImage(
      `definable/create_definable/${params.ProjectId}/${params.ParentCompanyId}`,
      payload,
    );
  }

  public importCompany(params, payload): Observable<any> {
    return this.api.requestImage(
      `company/create_company/${params.ProjectId}/${params.ParentCompanyId}`,
      payload,
    );
  }

  public updateDefinable(params, payload): Observable<any> {
    return this.api.post(`definable/update_definable/${params.ProjectId}`, payload);
  }

  public deleteDefinable(payload): Observable<any> {
    return this.api.post('definable/delete_definable', payload);
  }

  public createComment(payload): Observable<any> {
    return this.api.post('comment/create_comment/', payload);
  }

  public createInspectionComment(payload): Observable<any> {
    return this.api.post('comment/create_inspection_comment/', payload);
  }

  public createCraneRequestComment(payload): Observable<any> {
    return this.api.post('crane_request_comment/create_crane_request_comment/', payload);
  }

  public createConcreteRequestComment(payload): Observable<any> {
    return this.api.post('concrete_request_comment/create_concrete_request_comment/', payload);
  }

  public getNDRData(params): Observable<any> {
    return this.api.get(
      `delivery/get_single_NDR/${params.DeliveryRequestId}/${params.ParentCompanyId}`,
    );
  }

  public getInspectionNDRData(params): Observable<any> {

    return this.api.get(
      `inspection/get_single_NDR/${params.inspectionRequestId}/${params.ParentCompanyId}`,
    );
  }

  public getEquipmentCraneRequest(params): Observable<any> {
    return this.api.get(
      `crane_request/get_single_crane_request/${params.CraneRequestId}/${params.ProjectId}/${params.ParentCompanyId}`,
    );
  }

  public getMemberRole(params): Observable<any> {
    return this.api.get(`delivery/get_user_role/${params.ProjectId}/${params.ParentCompanyId}`);
  }

  public getAvailableTimeSlots(ProjectId,payload): Observable<any> {
    return this.api.post(`location/available_timeslots?ProjectId=${ProjectId}`,payload);
  }

  public updateStatus(payload): Observable<any> {
    return this.api.post('delivery/update_status', payload);
  }

  public updateInspectionStatus(payload): Observable<any> {
    return this.api.post('inspection/update_status', payload);
  }

  public updateCraneRequestStatus(payload): Observable<any> {
    return this.api.post('crane_request/update_crane_request_status', payload);
  }

  public updateConcreteRequestStatus(payload): Observable<any> {
    return this.api.post('concrete_request/update_concrete_request_status', payload);
  }

  public getUpcomingList(params): Observable<any> {
    return this.api.get('concrete_request/upcoming_request_list', params);
  }

  public createVoid(payload): Observable<any> {
    return this.api.post('void/create_void', payload);
  }

  public createInspectionVoid(payload): Observable<any> {
    return this.api.post('void/create_inspection_void', payload);
  }

  public addCraneRequestToVoid(payload): Observable<any> {
    return this.api.post('void/add_crane_request_to_void', payload);
  }

  public addConcreteRequestToVoid(payload): Observable<any> {
    return this.api.post('void/add_concrete_request_to_void', payload);
  }

  public removeVoid(payload): Observable<any> {
    return this.api.post('void/remove_void', payload);
  }

  public checkOverlapping(payload): Observable<any> {
    return this.api.post('calendar/checkOverlapping', payload);
  }

  public importBulkNDRTemplate(payload): Observable<any> {
    return this.api.getByHeadersFile(
      `delivery/sample_delivery_request_template/${payload.ProjectId}/${payload.ParentCompanyId}`,
      '',
    );
  }

  public importCompanyTemplate(payload): Observable<any> {
    return this.api.getByHeadersFile(
      `company/sample_company_template/${payload.ProjectId}/${payload.ParentCompanyId}`,
      '',
    );
  }

  public importBulkNDR(params, payload): Observable<any> {
    return this.api.requestImage(
      `delivery/bulk_upload_delivery_request/${params.ProjectId}/${params.ParentCompanyId}`,
      payload,
    );
  }

  public deleteQueuedNdr(payload): Observable<any> {
    return this.api.post('delivery/delete_queued_Ndr', payload);
  }

  public deleteConcreteRequest(payload): Observable<any> {
    return this.api.post('concrete_request/delete_concrete_request', payload);
  }

  public exportAsExcelFile(json: any[], excelFileName: string): void {
    const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(json);
    const workbook: XLSX.WorkBook = { Sheets: { data: worksheet }, SheetNames: ['data'] };
    const excelBuffer: any = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    this.saveAsExcelFile(excelBuffer, excelFileName);
  }

  public saveAsExcelFile(buffer: any, fileName: string): void {
    const data: Blob = new Blob([buffer], { type: EXCEL_TYPE });
    FileSaver.saveAs(data, `${fileName}_${new Date().getTime()}${EXCEL_EXTENSION}`);
  }

  public saveAsPdfFile(buffer: any, fileName: string): void {
    const data: Blob = new Blob([buffer], { type: PDF_TYPE });
    FileSaver.saveAs(data, `${fileName}_${new Date().getTime()}${PDF_EXTENSION}`);
  }

  public decryption(payload): Observable<any> {
    return this.api.post('delivery/decrypt', payload);
  }

  public weeklyDeliveryList(params, payload): Observable<any> {
    return this.api.post(`reports/weekly_calendar_request/${params.ProjectId}/${params.void}`, payload);
  }

  public noAuthWeeklyDeliveryList(params, payload): Observable<any> {
    return this.api.post(`reports/weekly_calendar_request/no_auth/${params.ProjectId}/${params.void}`, payload);
  }

  public exportWeeklyCalendarRequest(params, payload): Observable<any> {
    return this.api.post(
      `reports/export_weekly_calendar/${params.ProjectId}/${params.void}`,
      payload,
    );
  }

  public saveReportWeeklyCalendarRequest(params, payload): Observable<any> {
    return this.api.post(
      `reports/saved/export_weekly_calendar/${params.ProjectId}/${params.void}`,
      payload,
    );
  }

  public exportWeeklyCalendarRequestInExcelFormat(params, payload): Observable<any> {
    return this.api.getExcel1(
      `reports/export_weekly_calendar/${params.ProjectId}/${params.void}`,
      payload,
    );
  }
}
