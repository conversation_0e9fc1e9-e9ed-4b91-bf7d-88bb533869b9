.dashboard-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
}

/* Card Layout */
.carbon-card-container {
  justify-content: center;
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.total-emission-card {
  background: #f44336;
  color: white;
  font-weight: bold;
}

.chart-wrapper {
  height: 300px; /* Set fixed height */
  max-height: 300px; /* Prevent expansion */
  overflow: hidden; /* Prevent resizing issues */
  padding: 10px;
}


.card {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #ddd;
  text-align: center;
  width: 180px;
}

.card h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.card p {
  font-size: 22px;
  font-weight: bold;
  color: #5AA454;
}

.chart-container {
  border: 2px solid #ddd; /* Light gray border */
  border-radius: 8px; /* Rounded corners */
  padding: 15px;
  margin: 10px;
  background: #fff; /* White background */
  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1); /* Subtle shadow */
}

.carbon-card-container .card {
  background: #fff;
  border-radius: 8px;
  padding: 10px 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  width: 200px;  /* Increased width */
  height: 60px;  /* Reduced height */
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.carbon-card-container .card p {
  color: #f45e28; /* Set the value color */
  font-weight: bold;
  margin: 5px 0 0 0;
  font-size: 16px;
}

