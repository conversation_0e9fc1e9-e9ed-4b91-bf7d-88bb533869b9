/* eslint-disable max-lines-per-function */
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { BsModalService, ModalModule } from 'ngx-bootstrap/modal';

import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { of, throwError } from 'rxjs';
import { Title } from '@angular/platform-browser';
import { NO_ERRORS_SCHEMA } from '@angular/core';

import { LoginComponent } from './login.component';
import { AuthService } from '../../services/auth/auth.service';
import { MixpanelService } from '../../services/mixpanel.service';

describe('LoginComponent', () => {
  let component: LoginComponent;
  let fixture: ComponentFixture<LoginComponent>;
  let mockAuthService: any;
  let mockRouter: any;
  let mockToastrService: any;
  let mockTitleService: any;
  let mockMixpanelService: any;
  let mockBsModalService: any;
  let mockActivatedRoute: any;

  beforeEach(async () => {
    mockAuthService = {
      login: jest.fn(),
      checkAuthentication: jest.fn()
    };

    mockRouter = {
      navigate: jest.fn(),
      navigateByUrl: jest.fn(),
      createUrlTree: jest.fn(),
      serializeUrl: jest.fn(),
      events: of({}),
      url: '/login'
    };

    mockActivatedRoute = {
      snapshot: {
        paramMap: {
          get: jest.fn()
        },
        queryParamMap: {
          get: jest.fn()
        },
        data: {}
      },
      paramMap: of(new Map()),
      queryParamMap: of(new Map())
    };

    mockToastrService = {
      success: jest.fn(),
      error: jest.fn()
    };

    mockTitleService = {
      setTitle: jest.fn(),
      getTitle: jest.fn().mockReturnValue('Follo - Login')
    };

    mockMixpanelService = {
      addMixpanelEvents: jest.fn(),
      updateUserProfile: jest.fn()
    };

    mockBsModalService = {
      show: jest.fn().mockReturnValue({ hide: jest.fn() })
    };

    await TestBed.configureTestingModule({
      declarations: [LoginComponent],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        HttpClientTestingModule,
        ToastrModule.forRoot(),
        ModalModule.forRoot()
      ],
      providers: [
        UntypedFormBuilder,
        { provide: AuthService, useValue: mockAuthService },
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: Title, useValue: mockTitleService },
        { provide: MixpanelService, useValue: mockMixpanelService },
        { provide: BsModalService, useValue: mockBsModalService }
      ],
      schemas: [NO_ERRORS_SCHEMA] // This helps ignore template errors
    }).compileComponents();

    fixture = TestBed.createComponent(LoginComponent);
    component = fixture.componentInstance;

    // Create the form manually if needed
    if (!component.loginForm) {
      const formBuilder = TestBed.inject(UntypedFormBuilder);
      component.loginForm = formBuilder.group({
        email: [''],
        password: ['']
      });
    }

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct title', () => {
    expect(mockTitleService.setTitle).toHaveBeenCalledWith('Follo - Login');
  });

  it('should initialize form with empty email and password', () => {
    expect(component.loginForm.get('email').value).toBe('');
    expect(component.loginForm.get('password').value).toBe('');
  });

  it('form should not be submitted initially', () => {
    expect(component.submitted).toBeFalsy();
  });

  it('form should be submitted after login call', () => {
    component.loginForm.setValue({
      email: '',
      password: ''
    });
    component.login();
    expect(component.submitted).toBeTruthy();
  });

  it('should toggle password visibility', () => {
    expect(component.isShowPassword).toBeFalsy();
    component.showHide();
    expect(component.isShowPassword).toBeTruthy();
    component.showHide();
    expect(component.isShowPassword).toBeFalsy();
  });

  it('form should be invalid when empty', () => {
    component.loginForm.setValue({
      email: '',
      password: ''
    });
    expect(component.loginForm.invalid).toBeTruthy();
  });

  it('form should be invalid with invalid email', () => {
    component.loginForm.setValue({
      email: 'invalid-email',
      password: 'password123'
    });
    expect(component.loginForm.invalid).toBeTruthy();
  });

  it('form should be valid with valid inputs', () => {
    component.loginForm.setValue({
      email: '<EMAIL>',
      password: 'password123'
    });
    expect(component.loginForm.valid).toBeTruthy();
  });

  it('should not call auth service when form is invalid', () => {
    component.loginForm.setValue({
      email: '',
      password: ''
    });
    component.login();
    expect(mockAuthService.login).not.toHaveBeenCalled();
  });

  it('should handle successful login', fakeAsync(() => {
    const mockResponse = {
      token: 'test-token',
      userDetails: { id: 1, name: 'Test User', isAccount: false }
    };
    mockAuthService.login.mockReturnValue(of(mockResponse));

    component.loginForm.setValue({
      email: '<EMAIL>',
      password: 'password123'
    });

    component.login();
    tick();

    expect(mockAuthService.login).toHaveBeenCalled();
    expect(localStorage.getItem('token')).toBe('test-token');
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/all-calender']);
    expect(mockToastrService.success).toHaveBeenCalledWith("Let's get to work!", 'Success!');
    expect(component.submitted).toBeFalsy();
  }));

  it('should redirect to dashboard for account users', fakeAsync(() => {
    const mockResponse = {
      token: 'test-token',
      userDetails: { id: 1, name: 'Test User', isAccount: true }
    };
    mockAuthService.login.mockReturnValue(of(mockResponse));

    component.loginForm.setValue({
      email: '<EMAIL>',
      password: 'password123'
    });

    component.login();
    tick();

    expect(mockRouter.navigate).toHaveBeenCalledWith(['/dashboard']);
  }));

  it('should handle login error with message', fakeAsync(() => {
    const mockError = { message: { statusCode: 400, details: [{ error: 'Invalid credentials' }] } };
    mockAuthService.login.mockReturnValue(throwError(() => mockError));

    component.loginForm.setValue({
      email: '<EMAIL>',
      password: 'wrong-password'
    });

    component.login();
    tick();

    expect(mockToastrService.error).toHaveBeenCalled();
    expect(component.submitted).toBeFalsy();
  }));

  it('should handle login error without message', fakeAsync(() => {
    const mockError = {};
    mockAuthService.login.mockReturnValue(throwError(() => mockError));

    component.loginForm.setValue({
      email: '<EMAIL>',
      password: 'password123'
    });

    component.login();
    tick();

    expect(mockToastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    expect(component.submitted).toBeFalsy();
  }));

  it('should check authentication on init', () => {
    component.ngOnInit();
    expect(mockAuthService.checkAuthentication).toHaveBeenCalled();
  });

  it('should handle keyboard events for password toggle', () => {
    const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
    const spaceEvent = new KeyboardEvent('keydown', { key: ' ' });
    const otherEvent = new KeyboardEvent('keydown', { key: 'a' });

    const showHideSpy = jest.spyOn(component, 'showHide');

    component.handleToggleKeydown(enterEvent);
    expect(showHideSpy).toHaveBeenCalled();
    showHideSpy.mockClear();

    component.handleToggleKeydown(spaceEvent);
    expect(showHideSpy).toHaveBeenCalled();
    showHideSpy.mockClear();

    component.handleToggleKeydown(otherEvent);
    expect(showHideSpy).not.toHaveBeenCalled();
  });
});
