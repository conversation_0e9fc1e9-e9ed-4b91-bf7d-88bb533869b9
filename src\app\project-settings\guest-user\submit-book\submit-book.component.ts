import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { ProjectSharingService } from '../../../services/projectSharingService/project-sharing.service';

@Component({
  selector: 'app-submit-book',
  templateUrl: './submit-book.component.html',
  })
export class SubmitBookComponent implements OnInit {
  public guestUserId: any;

  public ProjectId: any;

  public ParentCompanyId: any;

  public constructor(
    public router: Router,
    public projectSharingService: ProjectSharingService,
    private readonly toastr: ToastrService,
  ) {
    this.ProjectId = +window.atob(localStorage.getItem('guestProjectId'));
    this.ParentCompanyId = +window.atob(localStorage.getItem('guestParentCompanyId'));
    this.guestUserId = +window.atob(localStorage.getItem('guestId'));
  }

  public ngOnInit(): void { /* */ }

  public submit(type: any) {
    if (type === 'no') {
      const url = window.atob(localStorage.getItem('url'));
      this.router.navigate([url]);
    } else {
      const payload = {
        ProjectId: this.ProjectId,
        // ParentCompanyId : this.ParentCompanyId,
        userId: this.guestUserId,
      };
      this.projectSharingService.updateGuestMember(payload).subscribe({
        next: (res): void => {
          if (res) {
            this.router.navigate(['/invite-link']);
          }
        },
        error: (error): void => {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        },
      });
    }
  }
}
