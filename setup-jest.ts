import 'jest-preset-angular/setup-jest';

// Add proper TypeScript declarations for the global object
declare const global: {
  setImmediate?: (callback: (...args: any[]) => void, ...args: any[]) => void;
  [key: string]: any;
};

// Add this at the top of your setup-jest.ts file
if (typeof global.setImmediate !== 'function') {
  global.setImmediate = (callback, ...args) => setTimeout(callback, 0, ...args);
}

/* global mocks for jsdom */
const mock = () => {
  let storage: { [key: string]: string } = {};
  return {
    getItem: (key: string) => (key in storage ? storage[key] : null),
    setItem: (key: string, value: string) => (storage[key] = value || ''),
    removeItem: (key: string) => delete storage[key],
    clear: () => (storage = {}),
  };
};

Object.defineProperty(window, 'localStorage', { value: mock() });
Object.defineProperty(window, 'sessionStorage', { value: mock() });
Object.defineProperty(window, 'getComputedStyle', {
  value: () => ['-webkit-appearance'],
});

Object.defineProperty(document.body, 'clientWidth', { value: 1024 });
Object.defineProperty(document.body, 'clientHeight', { value: 768 });

// Mock FormData for file upload tests
class MockFormData {
  private readonly data: Map<string, any> = new Map();

  append(name: string, value: any, filename?: string): void {
    this.data.set(name, { value, filename });
  }

  get(name: string): any {
    return this.data.get(name)?.value;
  }

  has(name: string): boolean {
    return this.data.has(name);
  }

  delete(name: string): void {
    this.data.delete(name);
  }

  entries(): IterableIterator<[string, any]> {
    return this.data.entries();
  }

  keys(): IterableIterator<string> {
    return this.data.keys();
  }

  values(): IterableIterator<any> {
    return this.data.values();
  }

  forEach(callback: (value: any, key: string, parent: FormData) => void): void {
    this.data.forEach((value, key) => callback(value, key, this as any));
  }
}

(global as any).FormData = MockFormData;

// Mock Blob constructor
class MockBlob {
  public size: number;
  public type: string;
  private readonly _blobParts: any[];

  constructor(blobParts: any[] = [], options: any = {}) {
    this._blobParts = blobParts;
    this.size = blobParts.reduce((acc, part) => acc + (part.length || 0), 0);
    this.type = options.type || '';
  }

  // Add methods that FileReader expects
  slice(start?: number, end?: number, contentType?: string): MockBlob {
    return new MockBlob(this._blobParts, { type: contentType || this.type });
  }

  stream(): ReadableStream {
    throw new Error('stream() not implemented in mock');
  }

  text(): Promise<string> {
    return Promise.resolve(this._blobParts.join(''));
  }

  arrayBuffer(): Promise<ArrayBuffer> {
    const str = this._blobParts.join('');
    const buffer = new ArrayBuffer(str.length);
    const view = new Uint8Array(buffer);
    for (let i = 0; i < str.length; i++) {
      view[i] = str.charCodeAt(i);
    }
    return Promise.resolve(buffer);
  }
}

(global as any).Blob = MockBlob;

// Mock File constructor that extends MockBlob
class MockFile extends MockBlob {
  public name: string;
  public lastModified: number;
  public webkitRelativePath: string;

  constructor(bits: any[], filename: string, options: any = {}) {
    super(bits, options);
    this.name = filename;
    this.lastModified = options.lastModified || Date.now();
    this.webkitRelativePath = options.webkitRelativePath || '';
  }
}

(global as any).File = MockFile;

// Mock FileReader
class MockFileReader {
  public result: string | ArrayBuffer | null = null;
  public error: any = null;
  public readyState: number = 0;
  public onload: ((event: ProgressEvent<FileReader>) => void) | null = null;
  public onerror: ((event: ProgressEvent<FileReader>) => void) | null = null;
  public onabort: ((event: ProgressEvent<FileReader>) => void) | null = null;
  public onloadstart: ((event: ProgressEvent<FileReader>) => void) | null = null;
  public onloadend: ((event: ProgressEvent<FileReader>) => void) | null = null;
  public onprogress: ((event: ProgressEvent<FileReader>) => void) | null = null;

  // FileReader constants
  static readonly EMPTY = 0;
  static readonly LOADING = 1;
  static readonly DONE = 2;

  private createMockEvent(): ProgressEvent<FileReader> {
    return {
      target: this,
      currentTarget: this,
      lengthComputable: false,
      loaded: 0,
      total: 0,
      bubbles: false,
      cancelBubble: false,
      cancelable: false,
      composed: false,
      defaultPrevented: false,
      eventPhase: 0,
      isTrusted: false,
      returnValue: false,
      srcElement: this,
      timeStamp: Date.now(),
      type: 'load',
      composedPath: () => [],
      initEvent: () => {},
      preventDefault: () => {},
      stopImmediatePropagation: () => {},
      stopPropagation: () => {}
    } as any;
  }

  readAsDataURL(blob: Blob): void {
    this.readyState = 1; // LOADING
    setTimeout(() => {
      try {
        // Create a mock data URL
        const mockDataUrl = `data:${blob.type || 'application/octet-stream'};base64,dGVzdA==`;
        this.result = mockDataUrl;
        this.readyState = 2; // DONE

        if (this.onload) {
          this.onload(this.createMockEvent());
        }
      } catch (error) {
        this.error = error;
        this.readyState = 2; // DONE
        if (this.onerror) {
          this.onerror(this.createMockEvent());
        }
      }
    }, 0);
  }

  readAsText(blob: Blob): void {
    this.readyState = 1; // LOADING
    setTimeout(() => {
      try {
        this.result = 'mock text content';
        this.readyState = 2; // DONE

        if (this.onload) {
          this.onload(this.createMockEvent());
        }
      } catch (error) {
        this.error = error;
        this.readyState = 2; // DONE
        if (this.onerror) {
          this.onerror(this.createMockEvent());
        }
      }
    }, 0);
  }

  readAsArrayBuffer(blob: Blob): void {
    this.readyState = 1; // LOADING
    setTimeout(() => {
      try {
        this.result = new ArrayBuffer(8);
        this.readyState = 2; // DONE

        if (this.onload) {
          this.onload(this.createMockEvent());
        }
      } catch (error) {
        this.error = error;
        this.readyState = 2; // DONE
        if (this.onerror) {
          this.onerror(this.createMockEvent());
        }
      }
    }, 0);
  }

  abort(): void {
    this.readyState = 2; // DONE
    if (this.onabort) {
      this.onabort(this.createMockEvent());
    }
  }

  addEventListener(type: string, listener: EventListener): void {
    // Mock implementation
  }

  removeEventListener(type: string, listener: EventListener): void {
    // Mock implementation
  }

  dispatchEvent(event: Event): boolean {
    return true;
  }
}

(global as any).FileReader = MockFileReader;

// Enhanced DOM mocking for Angular testing environment
const originalSetAttribute = Element.prototype.setAttribute;
const originalRemoveAttribute = Element.prototype.removeAttribute;
const originalCreateElement = document.createElement.bind(document);

// Mock setAttribute for all elements to prevent Angular renderer errors
Element.prototype.setAttribute = function(name: string, value: string) {
  if (this && typeof this === 'object') {
    try {
      if (originalSetAttribute && typeof originalSetAttribute === 'function') {
        return originalSetAttribute.call(this, name, value);
      }
    } catch (e) {
      // Silently handle setAttribute errors in test environment
    }
    // Fallback for elements without setAttribute
    this[name] = value;
  }
};

// Mock removeAttribute for all elements
Element.prototype.removeAttribute = function(name: string) {
  if (this && typeof this === 'object') {
    try {
      if (originalRemoveAttribute && typeof originalRemoveAttribute === 'function') {
        return originalRemoveAttribute.call(this, name);
      }
    } catch (e) {
      // Silently handle removeAttribute errors in test environment
    }
    // Fallback for elements without removeAttribute
    delete this[name];
  }
};

// Mock DOM element creation and manipulation
document.createElement = jest.fn().mockImplementation((tagName: string, options?: ElementCreationOptions) => {
  let element;

  try {
    element = originalCreateElement(tagName, options);
  } catch (e) {
    // Create a basic mock element if creation fails
    element = {
      tagName: tagName.toUpperCase(),
      nodeType: 1,
      nodeName: tagName.toUpperCase(),
      setAttribute: function(name: string, value: string) { this[name] = value; },
      removeAttribute: function(name: string) { delete this[name]; },
      getAttribute: function(name: string) { return this[name]; },
      hasAttribute: function(name: string) { return this[name] !== undefined;},
      appendChild: jest.fn(),
      removeChild: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
      click: jest.fn(),
      focus: jest.fn(),
      blur: jest.fn(),
      style: {},
      classList: {
        add: jest.fn(),
        remove: jest.fn(),
        contains: jest.fn(),
        toggle: jest.fn()
      }
    } as any;
  }

  // Ensure all elements have required methods
  if (!element.setAttribute || typeof element.setAttribute !== 'function') {
    element.setAttribute = function(name: string, value: string) {
     this[name] = value;
    };
  }

  if (!element.removeAttribute || typeof element.removeAttribute !== 'function') {
    element.removeAttribute = function(name: string) {
      delete this[name];
    };
  }

  // Add missing properties and methods for anchor elements
  if (tagName.toLowerCase() === 'a') {
    element.click = jest.fn();
    Object.defineProperty(element, 'href', {
      get: function() { return this._href || ''; },
      set: function(value) { this._href = value; },
      configurable: true
    });
    Object.defineProperty(element, 'target', {
      get: function() { return this._target || ''; },
      set: function(value) { this._target = value; },
      configurable: true
    });
  }

  return element;
});



// Mock appendChild to prevent JSDOM errors
const originalAppendChild = Node.prototype.appendChild;
Node.prototype.appendChild = jest.fn().mockImplementation(function(child: Node) {
  if (child && typeof child === 'object') {
    return originalAppendChild.call(this, child);
  }
  return child;
});

// Mock Image constructor
class MockImage {
  public onload: ((event: Event) => void) | null = null;
  public onerror: ((event: Event) => void) | null = null;
  public src: string = '';
  public width: number = 0;
  public height: number = 0;

  constructor() {
    // Simulate async loading
    setTimeout(() => {
      if (this.onload) {
        this.onload({} as Event);
      }
    }, 0);
  }
}

(global as any).Image = MockImage;

// Mock navigator.clipboard
Object.defineProperty(navigator, 'clipboard', {
  value: {
    writeText: jest.fn().mockResolvedValue(undefined),
    readText: jest.fn().mockResolvedValue(''),
  },
  configurable: true,
});

/* output shorter and more meaningful Zone error stack traces */
declare global {
  interface ErrorConstructor {
    stackTraceLimit: number;
  }
}

(Error as any).stackTraceLimit = 2;

// Google Maps API mocks for Jest tests

// Mock the global google object
(global as any).google = {
  maps: {
    Map: class MockMap {
      panTo(latLng: any): void {/* */}
    },
    Geocoder: class MockGeocoder {
      geocode(request: any, callback: (results: any[], status: string) => void): void {
        callback([{
          formatted_address: 'Mock Address',
          geometry: {
            location: {
              lat: () => 0,
              lng: () => 0
            }
          }
        }], 'OK');
      }
    },
    MapTypeId: {
      HYBRID: 'hybrid',
      ROADMAP: 'roadmap',
      SATELLITE: 'satellite',
      TERRAIN: 'terrain'
    },
    Animation: {
      BOUNCE: 1,
      DROP: 2
    }
  }
};

