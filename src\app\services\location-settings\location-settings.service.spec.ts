import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { LocationSettingsService } from './location-settings.service';
import { ApiService } from '../api_base/api.service';
import { of, throwError } from 'rxjs';

describe('LocationSettingsService', (): void => {
  let service: LocationSettingsService;
  let apiServiceMock: jest.Mocked<ApiService>;

  beforeEach((): void => {
    // Create mock for ApiService
    apiServiceMock = {
      get: jest.fn(),
      postMethod: jest.fn(),
      putMethod: jest.fn(),
      put: jest.fn(),
      deleteMethod: jest.fn(),
      getByHeadersFile: jest.fn(),
      bulkUploadLocation: jest.fn()
    } as unknown as jest.Mocked<ApiService>;

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        LocationSettingsService,
        { provide: ApiService, useValue: apiServiceMock }
      ]
    });

    service = TestBed.inject(LocationSettingsService);
  });

  it('should be created', (): void => {
    expect(service).toBeTruthy();
  });

  describe('addLocation', (): void => {
    it('should call postMethod with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        mainCategory: 'Building A',
        paths: [{ subCategory: 'Floor 1' }],
        GateId: 1,
        EquipmentId: 2,
        TimeZoneId: 3
      };

      const mockParams = {
        ProjectId: '123',
        ParentCompanyId: '456'
      };

      const mockResponse = {
        success: true,
        message: 'Location added successfully'
      };

      apiServiceMock.postMethod.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.addLocation(mockPayload, mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.postMethod).toHaveBeenCalledWith('location', mockPayload, mockParams);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('editLocation', (): void => {
    it('should call putMethod with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        id: 1,
        mainCategory: 'Building A Updated',
        paths: [{ subCategory: 'Floor 1 Updated' }],
        GateId: 1,
        EquipmentId: 2,
        TimeZoneId: 3
      };

      const mockParams = {
        ProjectId: '123',
        ParentCompanyId: '456'
      };

      const mockResponse = {
        success: true,
        message: 'Location updated successfully'
      };

      apiServiceMock.putMethod.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.editLocation(mockPayload, mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.putMethod).toHaveBeenCalledWith('location', mockPayload, mockParams);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getLocations', (): void => {
    it('should call get with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: '123',
        ParentCompanyId: '456',
        pageSize: 10,
        pageNo: 1,
        search: '',
        sort: 'DESC',
        sortColumn: 'id'
      };

      const mockResponse = {
        data: {
          rows: [
            {
              id: 1,
              mainCategory: 'Building A',
              paths: [{ subCategory: 'Floor 1' }],
              GateId: 1,
              EquipmentId: 2,
              TimeZoneId: 3
            }
          ],
          count: 1,
          defaultLocation: {
            id: 2,
            mainCategory: 'Default Location',
            paths: []
          }
        }
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getLocations(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith('location', mockParams);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getLocationList', (): void => {
    it('should call get with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: '123',
        ParentCompanyId: '456'
      };

      const mockResponse = {
        data: [
          {
            id: 1,
            mainCategory: 'Building A',
            paths: [{ subCategory: 'Floor 1' }]
          },
          {
            id: 2,
            mainCategory: 'Building B',
            paths: [{ subCategory: 'Floor 1' }]
          }
        ]
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getLocationList(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith('location/locations', mockParams);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('updateMemberLocationPreference', (): void => {
    it('should call put with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        MemberId: '123',
        ProjectId: '456',
        locations: [
          { id: 1, follow: true },
          { id: 2, follow: false }
        ]
      };

      const mockResponse = {
        success: true,
        message: 'Preferences updated successfully'
      };

      apiServiceMock.put.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.updateMemberLocationPreference(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.put).toHaveBeenCalledWith('location/member_location_preference', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getLocation', (): void => {
    it('should call get with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        LocationId: '1',
        ProjectId: '123',
        ParentCompanyId: '456'
      };

      const mockResponse = {
        data: {
          id: 1,
          mainCategory: 'Building A',
          paths: [{ subCategory: 'Floor 1' }],
          GateId: 1,
          EquipmentId: 2,
          TimeZoneId: 3
        }
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getLocation(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.get).toHaveBeenCalledWith('location/get_location', mockParams);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('deleteLocation', (): void => {
    it('should call deleteMethod with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        LocationId: '1',
        ProjectId: '123',
        ParentCompanyId: '456'
      };

      const mockResponse = {
        success: true,
        message: 'Location deleted successfully'
      };

      apiServiceMock.deleteMethod.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.deleteLocation(mockParams).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.deleteMethod).toHaveBeenCalledWith('location', mockParams);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('downloadSampleExcelFile', (): void => {
    it('should call getByHeadersFile with correct parameters', (): void => {
      // Arrange
      const mockPayload = {
        ProjectId: '123',
        ParentCompanyId: '456'
      };

      const mockResponse = new Blob(['sample excel content'], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

      apiServiceMock.getByHeadersFile.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.downloadSampleExcelFile(mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.getByHeadersFile).toHaveBeenCalledWith('location/sample_location_excel_download', mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('importLocationFile', (): void => {
    it('should call bulkUploadLocation with correct parameters', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: '123',
        ParentCompanyId: '456'
      };

      const mockPayload = new FormData();
      mockPayload.append('file', new Blob(['file content'], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }), 'locations.xlsx');

      const mockResponse = {
        success: true,
        message: 'Locations imported successfully',
        data: {
          imported: 5,
          failed: 0
        }
      };

      apiServiceMock.bulkUploadLocation.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.importLocationFile(mockParams, mockPayload).subscribe(response => {
        result = response;
      });

      // Assert
      expect(apiServiceMock.bulkUploadLocation).toHaveBeenCalledWith('location/import_location', mockParams, mockPayload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Error handling', (): void => {
    it('should propagate errors from the API service', (done): void => {
      // Arrange
      const mockParams = { ProjectId: '123' };
      const mockError = 'API Error';

      apiServiceMock.get.mockReturnValue(throwError(() => mockError));

      // Act & Assert
      service.getLocations(mockParams).subscribe({
        next: () => {
          done.fail('Expected error but got success');
        },
        error: (error) => {
          expect(error).toBe(mockError);
          done();
        }
      });
    });
  });

  describe('Service dependencies', (): void => {
    it('should have ApiService injected', (): void => {
      // This test verifies that the ApiService is properly injected
      expect(TestBed.inject(ApiService)).toBeDefined();
    });
  });

  describe('Integration with LocationSettingsComponent', (): void => {
    it('should provide data in the format expected by LocationSettingsComponent', (): void => {
      // Arrange
      const mockParams = {
        ProjectId: '123',
        ParentCompanyId: '456',
        pageSize: 5,
        pageNo: 1
      };

      const mockResponse = {
        data: {
          rows: [
            {
              id: 1,
              mainCategory: 'Building A',
              paths: [{ subCategory: 'Floor 1', isDeleted: 0 }],
              notes: 'Test notes',
              GateId: 1,
              EquipmentId: 2,
              TimeZoneId: 3
            }
          ],
          count: 1,
          defaultLocation: {
            id: 2,
            mainCategory: 'Default Location',
            paths: []
          }
        }
      };

      apiServiceMock.get.mockReturnValue(of(mockResponse));

      // Act
      let result: any;
      service.getLocations(mockParams).subscribe(response => {
        result = response;
      });

      // Assert - verify the structure matches what's expected in the component
      expect(result.data).toHaveProperty('rows');
      expect(result.data).toHaveProperty('count');
      expect(result.data).toHaveProperty('defaultLocation');
      expect(result.data.rows[0]).toHaveProperty('id');
      expect(result.data.rows[0]).toHaveProperty('mainCategory');
      expect(result.data.rows[0]).toHaveProperty('paths');
      expect(result.data.rows[0]).toHaveProperty('GateId');
      expect(result.data.rows[0]).toHaveProperty('EquipmentId');
      expect(result.data.rows[0]).toHaveProperty('TimeZoneId');
    });
  });
});
