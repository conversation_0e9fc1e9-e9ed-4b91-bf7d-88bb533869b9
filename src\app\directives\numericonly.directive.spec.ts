import { Component, DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { NumericOnlyDirective } from './numericonly.directive';

@Component({
  template: '<input appNumericonly>'
})
class TestComponent {}

describe('NumericOnlyDirective', () => {
  let component: TestComponent;
  let fixture: ComponentFixture<TestComponent>;
  let inputEl: DebugElement;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [NumericOnlyDirective, TestComponent]
    });

    fixture = TestBed.createComponent(TestComponent);
    component = fixture.componentInstance;
    inputEl = fixture.debugElement.query(By.css('input'));
    fixture.detectChanges();
  });

  it('should create an instance', () => {
    const directive = new NumericOnlyDirective();
    expect(directive).toBeTruthy();
  });

  it('should allow numeric keys', () => {
    // Test all numeric keys (0-9)
    for (let i = 0; i <= 9; i++) {
      const event = new KeyboardEvent('keydown', {
        key: i.toString()
      });

      // Spy on preventDefault
      jest.spyOn(event, 'preventDefault');

      inputEl.triggerEventHandler('keydown', event);
      expect(event.preventDefault).not.toHaveBeenCalled();
    }
  });

  it('should allow navigation keys', () => {
    const allowedKeys = ['Tab', 'Enter', 'Backspace', 'ArrowLeft', 'ArrowRight', 'Delete'];

    allowedKeys.forEach(key => {
      const event = new KeyboardEvent('keydown', { key });

      // Spy on preventDefault
      jest.spyOn(event, 'preventDefault');

      inputEl.triggerEventHandler('keydown', event);
      expect(event.preventDefault).not.toHaveBeenCalled();
    });
  });

  it('should prevent non-numeric and non-navigation keys', () => {
    const disallowedKeys = ['a', 'Z', '!', '@', ' ', '+', '-', '.'];

    disallowedKeys.forEach(key => {
      const event = new KeyboardEvent('keydown', { key });

      // Spy on preventDefault
      jest.spyOn(event, 'preventDefault');

      inputEl.triggerEventHandler('keydown', event);
      expect(event.preventDefault).toHaveBeenCalled();
    });
  });

  it('should directly call onKeydown method with expected results', () => {
    const directive = new NumericOnlyDirective();

    // Test allowed keys
    const allowedEvents = [
      new KeyboardEvent('keydown', { key: '5' }),
      new KeyboardEvent('keydown', { key: 'Tab' }),
      new KeyboardEvent('keydown', { key: 'Enter' }),
      new KeyboardEvent('keydown', { key: 'Backspace' }),
      new KeyboardEvent('keydown', { key: 'ArrowLeft' }),
      new KeyboardEvent('keydown', { key: 'ArrowRight' }),
      new KeyboardEvent('keydown', { key: 'Delete' })
    ];

    allowedEvents.forEach(event => {
      jest.spyOn(event, 'preventDefault');
      directive.onKeydown(event);
      expect(event.preventDefault).not.toHaveBeenCalled();
    });

    // Test disallowed keys
    const disallowedEvent = new KeyboardEvent('keydown', { key: 'a' });
    jest.spyOn(disallowedEvent, 'preventDefault');
    directive.onKeydown(disallowedEvent);
    expect(disallowedEvent.preventDefault).toHaveBeenCalled();
  });
});
