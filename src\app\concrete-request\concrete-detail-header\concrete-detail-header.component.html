<div class="modal-header">
  <h2 class="fs14 fw-bold cairo-regular color-text7 my-1">
    <img src="./assets/images/delivery-pop.svg" alt="Delivery" class="me-2" />Concrete Details
  </h2>
  <ul
    class="list-group list-group-horizontal text-center c-pointer position-absolute details-control d-none d-md-flex d-lg-flex align-items-center"
    *ngIf="!void && show"
  >
    <li class="list-group-item border-0 ctl-list py-0 px-3">
      <form class="custom-material-form">
        <div class="form-group mb-0">
          <select
            class="form-control fs12 material-input select-status-input select-concrete-input pe-5"
            (change)="selectStatus($event.target.value)"
            *ngIf="showStatus && statusValue.length > 1"
          >
            <option value="" disabled selected hidden>Select Status*</option>
            <option *ngFor="let item of statusValue">{{ item }}</option>
          </select>
        </div>
      </form>
    </li>
    <li
      class="list-group-item border-0 ctl-list py-0 px-3"
      *ngIf="showStatus && statusValue.length > 1"
      (click)="savestatus1()" (keydown)="handleDownKeydown($event,'','','save')"
    >
      <img src="./assets/images/save-details.svg" alt="save" />
      <p class="fs12 color-grey7 fw600 mb0">
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="statusSubmitted"></em>Save
      </p>
    </li>
  </ul>
  <button type="button" class="close ms-auto" aria-label="Close" (click)="closeModal()">
    <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close" /></span>
  </button>
</div>
<div class="modal-body pt-0 px-0 pb-0">
  <div class="delivery-details-content" id="delivery-content3">
    <tabset>
      <tab
        heading="Details"
        id="tab1"
        active="active"
        [active]="currentTabId === 0"
        (selectTab)="onSelect($event)"
      >
        <app-concrete-detail-content> </app-concrete-detail-content>
      </tab>
      <tab heading="Attachments" [active]="currentTabId === 1" (selectTab)="onSelect($event)">
        <app-concrete-attachments> </app-concrete-attachments>
      </tab>
      <tab heading="Comments" [active]="currentTabId === 2" (selectTab)="onSelect($event)">
        <app-concrete-comment> </app-concrete-comment>
      </tab>
      <tab heading="History" [active]="currentTabId === 3" (selectTab)="onSelect($event)">
        <app-concrete-history> </app-concrete-history>
      </tab>
    </tabset>
  </div>
  <div class="text-center mt-4 mb-4">
    <button
      class="fs12 cairo-regular btn btn-light-blue radius20 px-5 mx-2"
      (click)="openModal1(otherDetailsForm)"
      [disabled]="statusSubmitted"
      *ngIf="showSaveButton === true"
    >
      <em class="fa fa-spinner" aria-hidden="true" *ngIf="statusSubmitted"></em>Mark as Complete
    </button>
    <button
      *ngIf="
        currentDeliverySaveItem.edit &&
        currentDeliverySaveItem &&
        currentDeliverySaveItem.status == 'Completed'
      "
      class="fs12 cairo-regular btn btn-green radius20 px-5 mx-2"
      (click)="revertstatus(statusConfirmationPopup)"
    >
      <em class="fa fa-spinner" aria-hidden="true" *ngIf="statusSubmitted"></em>Completed
    </button>
    <button
      *ngIf="currentDeliverySaveItem.edit"
      class="fs12 cairo-regular btn btn-grey-light radius20 px-5 mx-2"
      (click)="openModal2(voidNdrConfirmation)"
      (onclick)="clickAndDisable(this)"
    >
      void
    </button>
    <button
      class="fs12 cairo-regular btn btn-green radius20 px-5 mx-2"
      (click)="openEditModal(currentDeliverySaveItem, null)"
      *ngIf="currentDeliverySaveItem?.edit && currentDeliverySaveItem?.recurrence?.recurrence === 'Does Not Repeat' "
    >
      Edit
    </button>
    <button
      *ngIf="currentDeliverySaveItem?.edit && currentDeliverySaveItem?.recurrence?.recurrence !== 'Does Not Repeat' "
      class="fs12 cairo-regular btn btn-green radius20 px-5 mx-2 edit-btn-popover"
      (click)="changeRequestCollapse(currentDeliverySaveItem)"
      [popover]="popTemplate"
      popoverTitle=""
      placement="top"
    >
      Edit
      <img
        src="./assets/images/green-downarrow.svg"
        alt="Down Arrow"
        class="arrow float-end eye-cursor mb-1 ms-2 mt-0"
        [ngClass]="{ rotateup: allRequestIsOpened }"
        containerClass="editcustomClass"
      />
    </button>
  </div>
</div>

<!--void delivery request confirmation Popup-->
<div id="confirm-popup9">
  <ng-template #voidNdrConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure you want to Void?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="voidConfirmationResponse('no')"
          [disabled]="voidSubmitted"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="voidConfirmationResponse('yes')"
          [disabled]="voidSubmitted"
        >
          <em class="fa fa-spinner" aria-hidden="true" *ngIf="voidSubmitted"></em>Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
<!--cancel confirmation Popup-->

<!--void delivery request confirmation Popup-->
<div id="confirm-popup9" class="concrete-other-request">
  <ng-template #otherDetailsForm>
    <div class="modal-header border-0 pb-0">
      <button
        type="button"
        class="close ms-auto"
        aria-label="Close"
        (click)="this.modalRef1.hide()"
      >
        <span aria-hidden="true">
          <img src="./assets/images/modal-close.svg" alt="Modal Close" />
        </span>
      </button>
    </div>
    <div class="modal-body">
      <form
        name="form"
        class="custom-material-form add-concrete-material-form"
        [formGroup]="otherForm"
        novalidate
      >
        <div class="text-center my-0">
          <p class="color-grey7 fs14 fw-bold cairo-regular mb-4">
            Please enter actual Concrete Placement details
          </p>
          <div class="row">
            <div class="col-md-12 text-left">
              <label class="fs12 fw600 m-0 color-grey11 py-2" for="completiontime">Completion Time</label>
            </div>
            <div class="col-md-6">
              <div class="form-group d-flex">
                <select id="hrs"
                  class="form-control fs12 material-input px-2"
                  formControlName="hoursToCompletePlacement"
                >
                  <option value="" disabled selected hidden>Select</option>
                  <option *ngFor="let hrs of hoursDropdown" value="{{ hrs }}">
                    {{ hrs }}
                  </option>
                </select>
                <label class="fs10 my-auto" for="hrs">Hrs</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group d-flex">
                <select id="mins"
                  class="form-control fs12 material-input px-2"
                  formControlName="minutesToCompletePlacement"
                >
                  <option value="" disabled selected hidden>Select</option>
                  <option *ngFor="let mins of minutesDropdown" value="{{ mins }}">
                    {{ mins }}
                  </option>
                </select>
                <label class="fs10 my-auto" for="mins">Mins</label>
              </div>
            </div>
          </div>
          <div class="row mb-4">
            <div class="col-md-12">
              <div class="floating-concrete mt-3">
                <div class="form-group pt-0 mt-0 floating-label">
                  <input
                    class="floating-input form-control fs12 px-0 py-2 h-25 mt-2 ps-2 ms-2"
                    type="text"
                    placeholder=" " id="cubic"
                    formControlName="cubicYardsTotal"
                  />
                  <label class="fs12 fw600 m-0 color-grey11" for="cubic">Total Cubic Yards Placed</label>
                </div>
              </div>
            </div>
          </div>
          <div class="mb-2">
            <button
              class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
              (click)="saveStatus('skip')"
              [disabled]="statusChanged"
            >
              <em class="fa fa-spinner" aria-hidden="true" *ngIf="skipped"></em>Skip
            </button>
            <button
              class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
              (click)="saveStatus('submit')"
              [disabled]="
                statusChanged ||
                (otherForm?.value?.cubicYardsTotal === '' &&
                  otherForm?.value?.hoursToCompletePlacement === '')
              "
            >
              <em class="fa fa-spinner" aria-hidden="true" *ngIf="statusSubmitted"></em>Submit
            </button>
          </div>
        </div>
      </form>
    </div>
  </ng-template>
</div>
<!--cancel confirmation Popup-->

<div id="confirm-popup6">
  <ng-template #statusConfirmationPopup>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Do you want to revert the status from Completed to approved for this bookin?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="statusupdate('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="statusupdate('yes')"
        >
          <p class="fs12 color-grey7 fw600 mb0">
            <em class="fa fa-spinner" aria-hidden="true" *ngIf="statusSubmitted"></em>Yes
          </p>
        </button>
      </div>
    </div>
  </ng-template>
</div>

<div id="confirm-popup7">
  <ng-template #personConfirmationPopup>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure? The responsible person mapped to the booking is deactivated, do you still
          want to deliver the booking?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="gatestatus('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="gatestatus('yes')"
        >
          <p class="fs12 color-grey7 fw600 mb0">
            <em class="fa fa-spinner" aria-hidden="true" *ngIf="gatesubmit"></em>Yes
          </p>
        </button>
      </div>
    </div>
  </ng-template>
</div>

<ng-template #popTemplate>
  <ul class="list-group c-pointer event-selection-popup my-2 w190">
    <li
      class="list-group-item border-0 p-1 cairo-regular c-pointer fw700 fs12"
      *ngFor="let data of seriesOptions"
      [ngClass]="{ disabled: data.disabled }"
      [tabindex]="data.disabled ? '-1' : '0'"
      (click)="openEditModal(currentDeliverySaveItem, data?.option)" (keydown)="handleDownKeydown($event,currentDeliverySaveItem, data?.option,'edit')"
    >
      <span class="ps-2">{{ data?.text }}</span>
    </li>
  </ul>
</ng-template>
