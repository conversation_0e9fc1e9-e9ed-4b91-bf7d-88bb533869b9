import { Component, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';

import { countryNames } from '../../../services/countryNames';
import { ProjectSharingService } from '../../../services/projectSharingService/project-sharing.service';
import { AuthService } from '../../../services/auth/auth.service';
import { countryCodes } from '../../../services/countryCodes';

@Component({
  selector: 'app-enter-details',
  templateUrl: './enter-details.component.html',
  })
export class EnterDetailsComponent implements OnInit {
  public codeDropdownSettings: IDropdownSettings;

  public companyDropdownSettings: IDropdownSettings;

  public guestUserFormData: UntypedFormGroup;

  public defaultCompany: any;

  public defaultCode: any = '+1';

  public countryCode = [];

  public selectedCountryCode: any;

  public detailsSubmitted = false;

  public phoneMask = '(*************';

  public companyLists = [];

  public selectedCompany: any;

  public data: any;

  public projectId: number;

  public parentCompanyId: number;

  public companyId: number;

  public requestType: any;

  public constructor(
    private readonly formBuilder: UntypedFormBuilder,
    public router: Router,
    private readonly authService: AuthService,
    private readonly route: ActivatedRoute,
    private readonly ProjectSharingServices: ProjectSharingService,
    private readonly toastr: ToastrService,
  ) {
    this.guestDetailForm();
    this.codeDropdownSettings = {
      singleSelection: true,
      idField: 'name',
      textField: 'countryDialCode',
      allowSearchFilter: false,
      closeDropDownOnSelection: true,
    };
    this.companyDropdownSettings = {
      singleSelection: true,
      idField: 'id',
      textField: 'companyName',
      allowSearchFilter: false,
      closeDropDownOnSelection: true,
    };
    this.route.queryParams.subscribe((params) => {
      this.requestType = params.type;
    });
    this.projectId = +window.atob(localStorage.getItem('guestProjectId'));
    this.parentCompanyId = +window.atob(localStorage.getItem('guestParentCompanyId'));
    this.getCountryCode();
    this.getCompanies();
  }

  public ngOnInit(): void { /* */ }

  public getCountryCode(): void {
    Object.keys(countryCodes).forEach((key): void => {
      const countryName = countryNames[key];
      this.countryCode.push({ countryDialCode: key, name: countryName });
    });
    this.countryCode.sort((a, b): number => (a.name > b.name ? 1 : -1));
    this.selectedCountryCode = '+1';
    const foundCode = this.countryCode.find(
      (item) => item.countryDialCode === this.selectedCountryCode,
    );
    this.defaultCode = [foundCode];
    this.guestUserFormData.get('phoneCode').setValue(this.defaultCode);
  }

  public handleToggleKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.previousPage();
    }
  }

  public changeMask(value): void {
    this.phoneMask = this.authService.checkDialCode(value);
  }

  public numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public alphaOnly(event): boolean {
    const key = event.which ? event.which : event.keyCode;
    return (
      (key >= 65 && key <= 90)
      || (key >= 97 && key <= 128)
      || key === 8
      || key === 32
      || (key >= 48 && key <= 57)
      || (key > 31 && (key < 48 || key > 57))
    );
  }

  public alphaNum(event): boolean {
    const key = event.which ? event.which : event.keyCode;
    const firstCondition = key >= 65 && key <= 90;
    const secondCondition = key >= 97 && key <= 128;
    const thirdCondition = key === 32 || (key > 31 && (key < 48 || key > 57));
    if (
      key > 31
      && (key < 48 || key > 57)
      && !firstCondition
      && !secondCondition
      && !thirdCondition
    ) {
      return false;
    }

    return true;
  }

  public submit(): void {
    this.detailsSubmitted = true;
    if (this.guestUserFormData.valid) {
      this.setGuestUserForm();
      const payload: any = {
        email: this.guestUserFormData.value.email,
        phoneCode: this.guestUserFormData.value.phoneCode,
        phoneNumber: this.guestUserFormData.value.phoneNumber,
        firstName: this.guestUserFormData.value.firstName,
        lastName: this.guestUserFormData.value.lastName,
        companyName: this.guestUserFormData.value.companyName,
        ProjectId: +this.projectId,
        ParentCompanyId: +this.parentCompanyId,
        companyId: +this.companyId,
      };
      this.selectedCountryCode = payload.phoneCode;
      const foundCode = this.countryCode.find(
        (item) => item.countryDialCode === this.selectedCountryCode,
      );
      this.defaultCode = [foundCode];
      this.companyId = payload.companyId;
      const foundCompany = this.companyLists.find((item) => item.id === this.companyId);
      this.guestUserFormData.get('phoneCode').setValue(this.defaultCode);
      this.defaultCompany = [foundCompany];
      this.selectedCompany = this.defaultCompany[0].companyName;
      this.guestUserFormData.get('companyName').setValue(this.defaultCompany);

      this.ProjectSharingServices.guestLogin(payload).subscribe({
        next: (response: any): void => {
          if (this.requestType === 'deliveryCalendar') {
            this.router.navigate(['/guest-delivery-calendar']);
          } else if (this.requestType === 'craneCalendar') {
            this.router.navigate(['/guest-crane-calendar']);
          } else if (this.requestType === 'concreteCalendar') {
            this.router.navigate(['/guest-concrete-calendar']);
          } else if (this.requestType === 'deliveryBooking') {
            this.router.navigate(['/guest-delivery-booking']);
          } else if (this.requestType === 'craneBooking') {
            this.router.navigate(['/guest-crane-booking']);
          } else if (this.requestType === 'concreteBooking') {
            this.router.navigate(['/guest-concrete-booking']);
          }
          if (response.existUser) {
            this.savingGuestData(payload, response.existUser.id);
            this.toastr.success("Let's get to work!", 'Success!');
          } else if (response.newUser) {
            this.savingGuestData(payload, response.newUser.id);
            this.toastr.success("Let's get to work!", 'Success!');
          }
          // } else if (response?.activeMember) {
          //   this.toastr.error('You are already a Member in this Project!');
          // }
          this.detailsSubmitted = false;
          this.guestUserFormData.get('email').setValue('');
          this.resetResponseData();
        },
        error: (error: any) => {
          console.error('Error occurred:', error);
          if (error.message?.details[0]?.email) {
            this.toastr.error(error.message.details[0]?.email);
          } else {
            this.toastr.error(error.message.message);
          }
          this.detailsSubmitted = false;
        },
      });
    }
  }

  public setGuestUserForm(): void {
    this.guestUserFormData.get('phoneCode').setValue(this.selectedCountryCode);
    this.guestUserFormData.get('companyName').setValue(this.selectedCompany);
  }

  public getCompanies(): void {
    const params = {
      ProjectId: +this.projectId,
      ParentCompanyId: +this.parentCompanyId,
    };
    this.ProjectSharingServices.getCompanyList(params).subscribe({
      next: (response: any): void => {
        this.companyLists = response.companyList;
      },
      error: (error: any) => {
        console.error('Error occurred:', error);
      },
    });
  }

  public previousPage(): void {
    this.router.navigate([window.atob(localStorage.getItem('url'))]);
  }

  public selectCode(event): void {
    this.selectedCountryCode = event.countryDialCode;
    this.changeMask(this.selectedCountryCode);
  }

  public selectCompany(event): void {
    this.selectedCompany = event.companyName;
    this.companyId = event.id;
  }

  public ifValidEmail(): void {
    if (this.guestUserFormData.get('email').valid) {
      const payload: any = {
        email: this.guestUserFormData.value.email,
        ProjectId: this.projectId,
      };
      this.ProjectSharingServices.alreadyVisited(payload).subscribe({
        next: (response: any): void => {
          if (response?.data) {
            this.settingResponseData(response);
          } else if (response?.newUser) {
            this.resetResponseData();
          } else if (response?.activeMember) {
            this.toastr.error(response?.activeMember);
            this.resetResponseData();
          }
        },
        error: (error: any) => {
          console.error('Error occurred:', error);
        },
      });
    }
  }

  public settingResponseData(response): void {
    this.data = response.data;
    this.selectedCountryCode = this.data.phoneCode;
    const foundCode = this.countryCode.find(
      (item) => item.countryDialCode === this.selectedCountryCode,
    );
    this.defaultCode = [foundCode];
    this.guestUserFormData.get('phoneCode').setValue(this.defaultCode);
    this.changeMask(this.defaultCode[0].countryDialCode);
    this.guestUserFormData.get('phoneNumber').setValue(this.data.phoneNumber);
    this.guestUserFormData.get('firstName').setValue(this.data.firstName);
    this.guestUserFormData.get('lastName').setValue(this.data.lastName);
    this.companyId = this.data.Members[0].Company.id;
    const foundCompany = this.companyLists.find((item) => item.id === this.companyId);
    this.defaultCompany = [foundCompany];
    this.selectedCompany = this.defaultCompany[0].companyName;
    this.guestUserFormData.get('companyName').setValue(this.defaultCompany);
  }

  public resetResponseData(): void {
    this.guestUserFormData.get('phoneNumber').setValue('');
    this.guestUserFormData.get('firstName').setValue('');
    this.guestUserFormData.get('lastName').setValue('');
    this.guestUserFormData.get('companyName').setValue('');
    this.selectedCountryCode = '+1';
    const foundCode = this.countryCode.find(
      (item) => item.countryDialCode === this.selectedCountryCode,
    );
    this.defaultCode = [foundCode];
    this.guestUserFormData.get('phoneCode').setValue(this.defaultCode);
  }

  public savingGuestData(payload: any, id: any): void {
    localStorage.setItem('guestId', window.btoa(id.toString()));
    localStorage.setItem('guestEmail', window.btoa(payload.email));
  }

  private guestDetailForm(): void {
    this.guestUserFormData = this.formBuilder.group({
      email: [
        '',
        Validators.compose([
          Validators.required,
          Validators.pattern("^[a-zA-Z0-9._%+'-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$"),
        ]),
      ],
      phoneCode: ['', Validators.compose([Validators.required])],
      phoneNumber: ['', Validators.compose([Validators.required])],
      firstName: ['', Validators.compose([Validators.required, Validators.minLength(3)])],
      lastName: ['', Validators.compose([Validators.required, Validators.minLength(3)])],
      companyName: ['', Validators.compose([Validators.required])],
    });
  }
}
