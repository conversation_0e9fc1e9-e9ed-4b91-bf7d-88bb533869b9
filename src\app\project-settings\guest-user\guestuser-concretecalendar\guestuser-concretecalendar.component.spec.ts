import { ComponentFixture, TestBed } from '@angular/core/testing';
import { GuestuserConcretecalendarComponent } from './guestuser-concretecalendar.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Router } from '@angular/router';
import { ProjectService } from '../../../services/profile/project.service';
import { ProjectSharingService } from 'src/app/services';
import { ToastrService } from 'ngx-toastr';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { UntypedFormBuilder, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { FullCalendarComponent } from '@fullcalendar/angular';
import { of, throwError } from 'rxjs';
import moment from 'moment';
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('GuestuserConcretecalendarComponent', () => {
  let component: GuestuserConcretecalendarComponent;
  let fixture: ComponentFixture<GuestuserConcretecalendarComponent>;
  let modalService: jest.Mocked<BsModalService>;
  let router: jest.Mocked<Router>;
  let projectService: jest.Mocked<ProjectService>;
  let projectSharingService: jest.Mocked<ProjectSharingService>;
  let toastr: jest.Mocked<ToastrService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let formBuilder: UntypedFormBuilder;
  let titleService: jest.Mocked<Title>;

  // Mock FullCalendarComponent
  class MockFullCalendarComponent {
    getApi() {
      return {
        next: jest.fn(),
        prev: jest.fn(),
        nextYear: jest.fn(),
        prevYear: jest.fn(),
        changeView: jest.fn(),
        removeAllEventSources: jest.fn(),
        addEventSource: jest.fn(),
        currentData: {
          dateProfile: {
            activeRange: {
              start: new Date('2023-01-01'),
              end: new Date('2023-01-31')
            }
          }
        }
      };
    }
  }

  beforeEach(async () => {
    const modalServiceSpy = {
      show: jest.fn().mockReturnValue({
        content: {
          onClose: of({}),
          onHidden: of({})
        }
      })
    };
    const routerSpy = {
      navigate: jest.fn()
    };
    const projectServiceSpy = {
      getProjectDetails: jest.fn().mockReturnValue(of({ data: { name: 'Test Project' } }))
    };
    const projectSharingServiceSpy = {
      checkIsGuest: jest.fn().mockReturnValue(of({ data: { isGuestUser: true, id: 789 } })),
      getConcreteRequest: jest.fn().mockReturnValue(of({
        data: [
          {
            id: 1,
            requestType: 'concreteRequest',
            status: 'Approved',
            concretePlacementStart: '2024-03-20T10:00:00',
            concretePlacementEnd: '2024-03-20T12:00:00',
            isCreatedByGuestUser: true,
            createdUserDetails: { id: 789 },
            ConcreteRequestId: 123,
            ProjectId: 123,
            description: 'Test concrete request',
            uniqueNumber: 'CR001',
            memberDetails: [{ Member: { User: { firstName: 'John', lastName: 'Doe' } } }],
            concreteSupplierDetails: [{ Company: { companyName: 'Test Supplier' } }],
            locationDetails: [{ ConcreteLocation: { location: 'Test Location' } }]
          },
          {
            id: 2,
            requestType: 'calendarEvent',
            fromDate: '2024-03-21T09:00:00',
            toDate: '2024-03-21T17:00:00',
            isAllDay: true,
            description: 'Test calendar event',
            uniqueNumber: 'CE001'
          }
        ],
        statusData: {
          statusColorCode: JSON.stringify([
            { status: 'approved', backgroundColor: '#28a745', fontColor: '#ffffff' },
            { status: 'pending', backgroundColor: '#ffc107', fontColor: '#000000' },
            { status: 'rejected', backgroundColor: '#dc3545', fontColor: '#ffffff' },
            { status: 'expired', backgroundColor: '#6c757d', fontColor: '#ffffff' },
            { status: 'delivered', backgroundColor: '#17a2b8', fontColor: '#ffffff' }
          ]),
          useTextColorAsLegend: 'false',
          isDefaultColor: 'false'
        },
        cardData: {
          concreteCard: JSON.stringify([
            { label: 'Description', line: 1, selected: true },
            { label: 'Responsible Person', line: 2, selected: true }
          ])
        }
      }))
    };
    const toastrSpy = {
      error: jest.fn(),
      success: jest.fn()
    };
    const deliveryServiceSpy = {
      updatedCurrentConcreteRequestStatus: jest.fn()
    };
    const titleServiceSpy = {
      setTitle: jest.fn()
    };

    await TestBed.configureTestingModule({
      declarations: [GuestuserConcretecalendarComponent],
      imports: [ReactiveFormsModule, FormsModule],
      providers: [
        { provide: BsModalService, useValue: modalServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: ProjectService, useValue: projectServiceSpy },
        { provide: ProjectSharingService, useValue: projectSharingServiceSpy },
        { provide: ToastrService, useValue: toastrSpy },
        { provide: DeliveryService, useValue: deliveryServiceSpy },
        { provide: UntypedFormBuilder, useValue: new UntypedFormBuilder() },
        { provide: Title, useValue: titleServiceSpy },
        { provide: FullCalendarComponent, useClass: MockFullCalendarComponent }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();

    modalService = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    router = TestBed.inject(Router) as jest.Mocked<Router>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    projectSharingService = TestBed.inject(ProjectSharingService) as jest.Mocked<ProjectSharingService>;
    toastr = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    formBuilder = TestBed.inject(UntypedFormBuilder);
    titleService = TestBed.inject(Title) as jest.Mocked<Title>;
  });

  beforeEach(() => {
    // Mock localStorage
    const mockData = {
      'guestId': '1',
      'guestEmail': '<EMAIL>',
      'guestProjectId': '123',
      'guestParentCompanyId': '456'
    };

    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn((key) => btoa(mockData[key])),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn()
      },
      writable: true
    });

    fixture = TestBed.createComponent(GuestuserConcretecalendarComponent);
    component = fixture.componentInstance;

    // Mock the calendar component
    const mockCalendar = new MockFullCalendarComponent();
    component.calendarComponent1 = mockCalendar as any;
    component.calendarApi = mockCalendar.getApi();

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with correct title', () => {
    expect(titleService.setTitle).toHaveBeenCalledWith('Follo - Concrete Calendar');
  });

  it('should load guest user data from localStorage', () => {
    expect(component.id).toBe(1);
    expect(component.email).toBe('<EMAIL>');
    expect(component.ProjectId).toBe(123);
    expect(component.ParentCompanyId).toBe(456);
  });

  it('should check guest user status on initialization', () => {
    const mockResponse = { data: { isGuestUser: true, id: 789 } };
    projectSharingService.checkIsGuest.mockReturnValue(of(mockResponse));

    component.checkGuest();

    expect(projectSharingService.checkIsGuest).toHaveBeenCalledWith({
      email: '<EMAIL>',
      ProjectId: 123
    });
    expect(component.isGuestUser).toBe(true);
    expect(component.guestMemberId).toBe(789);
  });

  it('should handle error when checking guest status', () => {
    projectSharingService.checkIsGuest.mockReturnValue(throwError(() => new Error('API Error')));

    component.checkGuest();

    expect(toastr.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  });

  it('should get concrete requests', () => {
    const mockResponse = {
      data: [{
        id: 1,
        requestType: 'concreteRequest',
        status: 'Approved',
        concretePlacementStart: '2024-03-20T10:00:00',
        concretePlacementEnd: '2024-03-20T12:00:00',
        isCreatedByGuestUser: true,
        createdUserDetails: { id: 789 }
      }]
    };
    projectSharingService.getConcreteRequest.mockReturnValue(of(mockResponse));

    component.getConcreteRequest();

    expect(projectSharingService.getConcreteRequest).toHaveBeenCalled();
    expect(component.concreteRequest).toEqual(mockResponse.data);
  });

  it('should handle calendar navigation methods', () => {
    // Create fresh mocks for this specific test
    const mockNext = jest.fn();
    const mockPrev = jest.fn();
    const mockNextYear = jest.fn();
    const mockPrevYear = jest.fn();
    const mockSetCalendar = jest.spyOn(component, 'setCalendar').mockImplementation(() => {});

    component.calendarApi = {
      next: mockNext,
      prev: mockPrev,
      nextYear: mockNextYear,
      prevYear: mockPrevYear,
      changeView: jest.fn(),
      removeAllEventSources: jest.fn(),
      addEventSource: jest.fn(),
      currentData: {
        dateProfile: {
          activeRange: {
            start: new Date('2023-01-01'),
            end: new Date('2023-01-31')
          }
        }
      }
    };

    // Test each navigation method separately
    component.goNext();
    expect(mockNext).toHaveBeenCalled();
    expect(mockSetCalendar).toHaveBeenCalled();
    mockSetCalendar.mockClear();

    component.goPrev();
    expect(mockPrev).toHaveBeenCalled();
    expect(mockSetCalendar).toHaveBeenCalled();
    mockSetCalendar.mockClear();

    component.goNextYear();
    expect(mockNextYear).toHaveBeenCalled();
    expect(mockSetCalendar).toHaveBeenCalled();
    mockSetCalendar.mockClear();

    component.goPrevYear();
    expect(mockPrevYear).toHaveBeenCalled();
    expect(mockSetCalendar).toHaveBeenCalled();
  });

  it('should change calendar view to month', () => {
    // Create a fresh mock for this specific test
    const mockChangeView = jest.fn();
    component.calendarApi = {
      next: jest.fn(),
      prev: jest.fn(),
      nextYear: jest.fn(),
      prevYear: jest.fn(),
      changeView: mockChangeView,
      removeAllEventSources: jest.fn(),
      addEventSource: jest.fn(),
      currentData: {
        dateProfile: {
          activeRange: {
            start: new Date('2023-01-01'),
            end: new Date('2023-01-31')
          }
        }
      }
    };

    component.goDayGridMonth();
    expect(component.currentView).toBe('Month');
    expect(mockChangeView).toHaveBeenCalledWith('dayGridMonth');
  });

  it('should change calendar view to week', () => {
    // Create a fresh mock for this specific test
    const mockChangeView = jest.fn();
    component.calendarApi = {
      next: jest.fn(),
      prev: jest.fn(),
      nextYear: jest.fn(),
      prevYear: jest.fn(),
      changeView: mockChangeView,
      removeAllEventSources: jest.fn(),
      addEventSource: jest.fn(),
      currentData: {
        dateProfile: {
          activeRange: {
            start: new Date('2023-01-01'),
            end: new Date('2023-01-31')
          }
        }
      }
    };

    component.goTimeGridWeekOrDay('timeGridWeek');
    expect(component.currentView).toBe('Week');
    expect(mockChangeView).toHaveBeenCalledWith('timeGridWeek');
  });

  it('should change calendar view to day', () => {
    // Create a fresh mock for this specific test
    const mockChangeView = jest.fn();
    component.calendarApi = {
      next: jest.fn(),
      prev: jest.fn(),
      nextYear: jest.fn(),
      prevYear: jest.fn(),
      changeView: mockChangeView,
      removeAllEventSources: jest.fn(),
      addEventSource: jest.fn(),
      currentData: {
        dateProfile: {
          activeRange: {
            start: new Date('2023-01-01'),
            end: new Date('2023-01-31')
          }
        }
      }
    };

    component.goTimeGridWeekOrDay('timeGridDay');
    expect(component.currentView).toBe('Day');
    expect(mockChangeView).toHaveBeenCalledWith('timeGridDay');
  });

  it('should handle search functionality', () => {
    component.search = 'test';
    component.showSearchbar = true;

    component.clear();

    expect(component.showSearchbar).toBe(false);
    expect(component.search).toBe('');
  });

  it('should handle calendar description popup', () => {
    const mockEvent = {
      event: {
        title: 'Test Event',
        extendedProps: {
          uniqueNumber: '123'
        }
      }
    };

    component.events = [{
      description: 'Test Event',
      uniqueNumber: '123'
    }];

    component.concreteRequest = [{
      description: 'Test Event',
      uniqueNumber: '123'
    }];

    component.calendarDescription(mockEvent);

    expect(component.calendarDescriptionPopup).toBe(true);
    expect(component.viewEventData).toBeDefined();
  });

  it('should close calendar description', () => {
    component.calendarDescriptionPopup = true;
    component.descriptionPopup = true;
    component.viewEventData = { some: 'data' };

    component.closeCalendarDescription();

    expect(component.calendarDescriptionPopup).toBe(false);
    expect(component.descriptionPopup).toBe(false);
    expect(component.viewEventData).toBe('');
  });

  it('should open add concrete request modal', () => {
    // Set isGuestUser to false to allow the modal to be shown
    component.isGuestUser = false;

    const dateArg = { dateStr: '2023-01-15' };
    component.openAddConcreteRequestModal(dateArg);
    expect(modalService.show).toHaveBeenCalled();
  });

  it('should handle occurrence message formatting for daily events', () => {
    const mockData = {
      repeatEveryType: 'Day',
      repeatEveryCount: 1,
      days: [],
      chosenDateOfMonth: null,
      dateOfMonth: null,
      monthlyRepeatType: null,
      endTime: '2023-01-31'
    };

    component.occurMessage(mockData);
    expect(component.message).toContain('Occurs every day until');
  });

  it('should handle occurrence message formatting for multiple days', () => {
    const mockData = {
      repeatEveryType: 'Days',
      repeatEveryCount: 3,
      days: [],
      chosenDateOfMonth: null,
      dateOfMonth: null,
      monthlyRepeatType: null,
      endTime: '2023-01-31'
    };

    component.occurMessage(mockData);
    expect(component.message).toContain('Occurs every 3 days until');
  });

  it('should format date in changeFormat method', () => {
    const date = '2023-01-15';
    const result = component.changeFormat(date);
    expect(result).toContain('01/15/2023');
  });

  it('should return undefined when changeFormat is called with no date', () => {
    const result = component.changeFormat(null);
    expect(result).toBeUndefined();
  });

  it('should handle opening request detail', () => {
    // Create a properly structured mock event argument
    const mockArg = {
      event: {
        id: '123',
        _def: {
          extendedProps: {
            uniqueNumber: '123',
            isGuestRequest: true,
            creatorId: 789,
            guestMemberId: 789,
            requestType: 'concreteRequest'
          }
        },
        extendedProps: {
          uniqueNumber: '123'
        }
      }
    };

    // Setup mock concrete request data that matches the event ID
    component.concreteRequest = [{
      id: 123,
      uniqueNumber: '123',
      ConcreteRequestId: 123,
      ProjectId: 123,
      requestType: 'concreteRequest'
    }];

    // Mock the modalService.show method
    modalService.show.mockReturnValue({
      content: { closeBtnName: 'Close' }
    } as BsModalRef);

    // Call the method being tested
    component.openRequestDetail(mockArg);

    // Verify the modal was shown
    expect(modalService.show).toHaveBeenCalled();
  });

  describe('Additional Coverage Tests', () => {
    beforeEach(() => {
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
      component.id = 1;
      component.email = '<EMAIL>';
      component.isGuestUser = true;
      component.guestMemberId = 789;
    });

    it('should handle checkGuest when response has no data', () => {
      projectSharingService.checkIsGuest.mockReturnValue(of({ data: null }));

      component.checkGuest();

      expect(component.isGuestUser).toBe(false);
    });

    it('should handle keyboard events for different action types', () => {
      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'clear').mockImplementation(() => {});
      jest.spyOn(component, 'openFilterModal').mockImplementation(() => {});
      jest.spyOn(component, 'closeCalendarDescription').mockImplementation(() => {});

      // Test clear action
      component.handleDownKeydown(event, null, 'clear');
      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.clear).toHaveBeenCalled();

      // Test filter action
      const template = {} as any;
      component.handleDownKeydown(event, template, 'filter');
      expect(component.openFilterModal).toHaveBeenCalledWith(template);

      // Test close action
      component.handleDownKeydown(event, null, 'close');
      expect(component.closeCalendarDescription).toHaveBeenCalled();

      // Test default case
      component.handleDownKeydown(event, null, 'unknown');
      // Should not throw error
    });

    it('should handle keyboard events with Space key', () => {
      const event = new KeyboardEvent('keydown', { key: ' ' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'clear').mockImplementation(() => {});

      component.handleDownKeydown(event, null, 'clear');
      expect(event.preventDefault).toHaveBeenCalled();
      expect(component.clear).toHaveBeenCalled();
    });

    it('should not handle keyboard events for other keys', () => {
      const event = new KeyboardEvent('keydown', { key: 'Tab' });
      jest.spyOn(event, 'preventDefault');
      jest.spyOn(component, 'clear').mockImplementation(() => {});

      component.handleDownKeydown(event, null, 'clear');
      expect(event.preventDefault).not.toHaveBeenCalled();
      expect(component.clear).not.toHaveBeenCalled();
    });

    it('should open filter modal', () => {
      const template = {} as any;
      component.openFilterModal(template);

      expect(modalService.show).toHaveBeenCalledWith(template, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-sm filter-popup custom-modal'
      });
    });

    it('should open add modal', () => {
      component.openAddModal();

      expect(modalService.show).toHaveBeenCalledWith(expect.anything(), {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal'
      });
    });

    it('should open modal with template', () => {
      const template = {} as any;
      component.openModal(template);

      expect(modalService.show).toHaveBeenCalledWith(template, {
        class: 'modal-lg new-delivery-popup custom-modal'
      });
    });

    it('should reset filter', () => {
      component.filterForm = formBuilder.group({
        descriptionFilter: ['test'],
        locationFilter: ['test'],
        locationPathFilter: ['test'],
        concreteSupplierFilter: ['test'],
        orderNumberFilter: ['test'],
        statusFilter: ['test'],
        mixDesignFilter: ['test']
      });
      component.filterCount = 5;
      component.search = 'test search';
      component.modalRef = { hide: jest.fn() } as any;

      jest.spyOn(component, 'getConcreteRequest').mockImplementation(() => {});

      component.resetFilter();

      expect(component.filterCount).toBe(0);
      expect(component.search).toBe('');
      expect(component.getConcreteRequest).toHaveBeenCalled();
      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should submit filter with all fields filled', () => {
      component.filterForm = formBuilder.group({
        descriptionFilter: ['test desc'],
        locationFilter: ['test location'],
        locationPath: ['test path'],
        orderNumberFilter: ['test order'],
        concreteSupplierFilter: ['test supplier'],
        statusFilter: ['test status'],
        mixDesignFilter: ['test mix']
      });
      component.modalRef = { hide: jest.fn() } as any;

      jest.spyOn(component, 'getConcreteRequest').mockImplementation(() => {});

      component.filterSubmit();

      expect(component.filterCount).toBe(7);
      expect(component.getConcreteRequest).toHaveBeenCalled();
      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should handle search with data', () => {
      jest.spyOn(component, 'getConcreteRequest').mockImplementation(() => {});

      component.getSearch('test search');

      expect(component.showSearchbar).toBe(true);
      expect(component.search).toBe('test search');
      expect(component.getConcreteRequest).toHaveBeenCalled();
    });

    it('should handle search with empty data', () => {
      jest.spyOn(component, 'getConcreteRequest').mockImplementation(() => {});

      component.getSearch('');

      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
      expect(component.getConcreteRequest).toHaveBeenCalled();
    });

    it('should test getValueByLabel method for all cases', () => {
      const element = {
        description: 'Test Description',
        memberDetails: [{ Member: { User: { firstName: 'John', lastName: 'Doe' } } }],
        concreteSupplierDetails: [{ Company: { companyName: 'Test Supplier' } }],
        ConcreteRequestId: 'CR123',
        locationDetails: [{ ConcreteLocation: { location: 'Test Location' } }]
      };

      expect(component.getValueByLabel('Description', element)).toBe('Test Description');
      expect(component.getValueByLabel('Responsible Person', element)).toBe('John Doe');
      expect(component.getValueByLabel('Concrete Supplier', element)).toBe('Test Supplier');
      expect(component.getValueByLabel('Concrete Request ID', element)).toBe('CR123');
      expect(component.getValueByLabel('Location', element)).toBe('Test Location');
      expect(component.getValueByLabel('Unknown', element)).toBeNull();
    });

    it('should handle getValueByLabel with missing data', () => {
      const element = {
        memberDetails: null,
        concreteSupplierDetails: null,
        locationDetails: null
      };

      expect(component.getValueByLabel('Responsible Person', element)).toBe('');
      expect(component.getValueByLabel('Concrete Supplier', element)).toBe('');
      expect(component.getValueByLabel('Location', element)).toBe('');
    });

    it('should handle getValueByLabel with empty arrays', () => {
      const element = {
        memberDetails: [],
        concreteSupplierDetails: [],
        locationDetails: []
      };

      expect(component.getValueByLabel('Responsible Person', element)).toBe('');
      expect(component.getValueByLabel('Concrete Supplier', element)).toBe('');
      expect(component.getValueByLabel('Location', element)).toBe('');
    });

    it('should test occurMessage for different repeat types', () => {
      // Test Days with count 2
      const mockData1 = {
        repeatEveryType: 'Days',
        repeatEveryCount: 2,
        days: [],
        chosenDateOfMonth: null,
        dateOfMonth: null,
        monthlyRepeatType: null,
        endTime: '2023-01-31'
      };
      component.occurMessage(mockData1);
      expect(component.message).toContain('Occurs every other day until');

      // Test Week
      const mockData2 = {
        repeatEveryType: 'Week',
        repeatEveryCount: 1,
        days: ['Monday', 'Wednesday'],
        chosenDateOfMonth: null,
        dateOfMonth: null,
        monthlyRepeatType: null,
        endTime: '2023-01-31'
      };
      component.occurMessage(mockData2);
      expect(component.message).toContain('Occurs every Monday,Wednesday until');

      // Test Weeks with count 2
      const mockData3 = {
        repeatEveryType: 'Weeks',
        repeatEveryCount: 2,
        days: ['Tuesday', 'Thursday'],
        chosenDateOfMonth: null,
        dateOfMonth: null,
        monthlyRepeatType: null,
        endTime: '2023-01-31'
      };
      component.occurMessage(mockData3);
      expect(component.message).toContain('Occurs every other  Tuesday,Thursday until');

      // Test Weeks with count > 2
      const mockData4 = {
        repeatEveryType: 'Weeks',
        repeatEveryCount: 3,
        days: ['Friday'],
        chosenDateOfMonth: null,
        dateOfMonth: null,
        monthlyRepeatType: null,
        endTime: '2023-01-31'
      };
      component.occurMessage(mockData4);
      expect(component.message).toContain('Occurs every 3 weeks on Friday until');

      // Test Month with chosenDateOfMonth
      const mockData5 = {
        repeatEveryType: 'Month',
        repeatEveryCount: 1,
        days: [],
        chosenDateOfMonth: true,
        dateOfMonth: 15,
        monthlyRepeatType: null,
        endTime: '2023-01-31'
      };
      component.occurMessage(mockData5);
      expect(component.message).toContain('Occurs on day 15 until');

      // Test Month without chosenDateOfMonth
      const mockData6 = {
        repeatEveryType: 'Month',
        repeatEveryCount: 1,
        days: [],
        chosenDateOfMonth: false,
        dateOfMonth: null,
        monthlyRepeatType: 'first Monday',
        endTime: '2023-01-31'
      };
      component.occurMessage(mockData6);
      expect(component.message).toContain('Occurs on the first Monday until');
    });

    it('should handle openAddConcreteRequestModal when user is guest', () => {
      component.isGuestUser = true;
      const dateArg = { dateStr: '2023-01-15' };

      const result = component.openAddConcreteRequestModal(dateArg);

      expect(result).toBeUndefined();
      expect(modalService.show).not.toHaveBeenCalled();
    });

    it('should handle openRequestDetail for calendar event', () => {
      const mockArg = {
        event: {
          id: '2',
          _def: {
            extendedProps: {
              uniqueNumber: 'CE001',
              isGuestRequest: false,
              creatorId: 123,
              guestMemberId: 789,
              requestType: 'calendarEvent'
            }
          },
          extendedProps: {
            uniqueNumber: 'CE001'
          }
        }
      };

      component.concreteRequest = [{
        id: 2,
        uniqueNumber: 'CE001',
        requestType: 'calendarEvent',
        description: 'Test calendar event'
      }];

      jest.spyOn(component, 'calendarDescription').mockImplementation(() => {});

      component.openRequestDetail(mockArg);

      expect(component.calendarDescription).toHaveBeenCalledWith(mockArg);
    });

    it('should handle openRequestDetail when guest user cannot access', () => {
      component.isGuestUser = true;
      component.isGuestReq = false;

      const mockArg = {
        event: {
          id: '1',
          _def: {
            extendedProps: {
              uniqueNumber: 'CR001',
              isGuestRequest: false,
              creatorId: 123,
              guestMemberId: 789,
              requestType: 'concreteRequest'
            }
          },
          extendedProps: {
            uniqueNumber: 'CR001'
          }
        }
      };

      const result = component.openRequestDetail(mockArg);

      expect(result).toBeUndefined();
    });

    it('should handle openRequestDetail when guest user is not creator', () => {
      component.isGuestUser = true;
      component.isGuestReq = true;

      const mockArg = {
        event: {
          id: '1',
          _def: {
            extendedProps: {
              uniqueNumber: 'CR001',
              isGuestRequest: true,
              creatorId: 123, // Different from guestMemberId
              guestMemberId: 789,
              requestType: 'concreteRequest'
            }
          },
          extendedProps: {
            uniqueNumber: 'CR001'
          }
        }
      };

      const result = component.openRequestDetail(mockArg);

      expect(result).toBeUndefined();
    });

    it('should handle getConcreteRequest with comprehensive data processing', () => {
      component.filterForm = formBuilder.group({
        locationFilter: ['test location'],
        locationPathFilter: ['test path'],
        descriptionFilter: ['test desc'],
        statusFilter: ['approved'],
        mixDesignFilter: ['test mix'],
        orderNumberFilter: ['test order'],
        concreteSupplierFilter: ['test supplier']
      });
      component.search = 'test search';
      component.filterCount = 3;
      component.currentView = 'Day';
      component.Range = {
        start: new Date('2024-03-01'),
        end: new Date('2024-03-31')
      };

      // Mock the calendar API
      component.calendarApi = {
        removeAllEventSources: jest.fn(),
        addEventSource: jest.fn()
      };

      component.getConcreteRequest();

      expect(projectSharingService.getConcreteRequest).toHaveBeenCalledWith(
        { ProjectId: 123, void: 0 },
        expect.objectContaining({
          locationFilter: 'test location',
          locationPathFilter: 'test path',
          descriptionFilter: 'test desc',
          statusFilter: 'approved',
          mixDesignFilter: 'test mix',
          orderNumberFilter: 'test order',
          concreteSupplierFilter: 'test supplier',
          search: 'test search',
          ParentCompanyId: 456,
          start: '2024-03-01',
          end: '2024-03-31',
          filterCount: 3,
          calendarView: 'Day',
          id: 1
        })
      );

      expect(component.loader).toBe(false);
      expect(component.events).toBeDefined();
      expect(component.calendarApi.removeAllEventSources).toHaveBeenCalled();
      expect(component.calendarApi.addEventSource).toHaveBeenCalled();
    });

    it('should handle getConcreteRequest when filterForm is undefined', () => {
      component.filterForm = undefined;
      component.search = 'test';
      component.Range = {
        start: new Date('2024-03-01'),
        end: new Date('2024-03-31')
      };

      // Mock the calendar API
      component.calendarApi = {
        removeAllEventSources: jest.fn(),
        addEventSource: jest.fn()
      };

      component.getConcreteRequest();

      expect(projectSharingService.getConcreteRequest).toHaveBeenCalled();
    });

    it('should test ngAfterViewInit', () => {
      jest.spyOn(component, 'setCalendar').mockImplementation(() => {});
      jest.spyOn(component, 'filterDetailsForm').mockImplementation(() => {});
      jest.spyOn(component, 'getDropdownValue').mockImplementation(() => {});

      component.ngAfterViewInit();

      expect(component.setCalendar).toHaveBeenCalled();
      expect(component.filterDetailsForm).toHaveBeenCalled();
      expect(component.getDropdownValue).toHaveBeenCalled();
    });

    it('should test getDropdownValue method', () => {
      const result = component.getDropdownValue();
      expect(result).toBeUndefined();
    });

    it('should test closeDescription method', () => {
      component.descriptionPopup = true;
      component.closeDescription();
      expect(component.descriptionPopup).toBe(false);
    });

    it('should handle getConcreteRequest with useTextColorAsLegend true', () => {
      // Update the mock to return useTextColorAsLegend as true
      const mockResponseWithTextColor = {
        data: [
          {
            id: 1,
            requestType: 'concreteRequest',
            status: 'Approved',
            concretePlacementStart: '2024-03-20T10:00:00',
            concretePlacementEnd: '2024-03-20T12:00:00',
            isCreatedByGuestUser: true,
            createdUserDetails: { id: 789 },
            ConcreteRequestId: 123,
            ProjectId: 123,
            description: 'Test concrete request',
            uniqueNumber: 'CR001'
          }
        ],
        statusData: {
          statusColorCode: JSON.stringify([
            { status: 'approved', backgroundColor: '#28a745', fontColor: '#ffffff' },
            { status: 'pending', backgroundColor: '#ffc107', fontColor: '#000000' },
            { status: 'rejected', backgroundColor: '#dc3545', fontColor: '#ffffff' },
            { status: 'expired', backgroundColor: '#6c757d', fontColor: '#ffffff' },
            { status: 'delivered', backgroundColor: '#17a2b8', fontColor: '#ffffff' }
          ]),
          useTextColorAsLegend: 'true', // Changed to true
          isDefaultColor: 'false'
        },
        cardData: {
          concreteCard: JSON.stringify([
            { label: 'Description', line: 1, selected: true }
          ])
        }
      };

      projectSharingService.getConcreteRequest.mockReturnValue(of(mockResponseWithTextColor));
      component.calendarApi = {
        removeAllEventSources: jest.fn(),
        addEventSource: jest.fn()
      };
      component.Range = {
        start: new Date('2024-03-01'),
        end: new Date('2024-03-31')
      };

      component.getConcreteRequest();

      expect(component.approved).toBe('#ffffff'); // Should use fontColor
      expect(component.pending).toBe('#000000');
    });

    it('should handle getConcreteRequest with isDefaultColor true', () => {
      // Update the mock to return isDefaultColor as true
      const mockResponseWithDefaultColor = {
        data: [
          {
            id: 1,
            requestType: 'concreteRequest',
            status: 'Completed',
            concretePlacementStart: '2024-03-20T10:00:00',
            concretePlacementEnd: '2024-03-20T12:00:00',
            isCreatedByGuestUser: true,
            createdUserDetails: { id: 789 },
            ConcreteRequestId: 123,
            ProjectId: 123,
            description: 'Test concrete request',
            uniqueNumber: 'CR001'
          }
        ],
        statusData: {
          statusColorCode: JSON.stringify([
            { status: 'approved', backgroundColor: '#28a745', fontColor: '#ffffff' },
            { status: 'pending', backgroundColor: '#ffc107', fontColor: '#000000' },
            { status: 'rejected', backgroundColor: '#dc3545', fontColor: '#ffffff' },
            { status: 'expired', backgroundColor: '#6c757d', fontColor: '#ffffff' },
            { status: 'delivered', backgroundColor: '#17a2b8', fontColor: '#ffffff' }
          ]),
          useTextColorAsLegend: 'false',
          isDefaultColor: 'true' // Changed to true
        },
        cardData: {
          concreteCard: JSON.stringify([
            { label: 'Description', line: 1, selected: true }
          ])
        }
      };

      projectSharingService.getConcreteRequest.mockReturnValue(of(mockResponseWithDefaultColor));
      component.calendarApi = {
        removeAllEventSources: jest.fn(),
        addEventSource: jest.fn()
      };
      component.Range = {
        start: new Date('2024-03-01'),
        end: new Date('2024-03-31')
      };

      component.getConcreteRequest();

      expect(component.delivered).toBe('#17a2b8'); // Should use backgroundColor for delivered when isDefaultColor is true
    });

    it('should handle getConcreteRequest with element without createdUserDetails', () => {
      const mockResponseWithoutCreatedUser = {
        data: [
          {
            id: 1,
            requestType: 'concreteRequest',
            status: 'Approved',
            concretePlacementStart: '2024-03-20T10:00:00',
            concretePlacementEnd: '2024-03-20T12:00:00',
            isCreatedByGuestUser: true,
            createdUserDetails: null, // No created user details
            ConcreteRequestId: 123,
            ProjectId: 123,
            description: 'Test concrete request',
            uniqueNumber: 'CR001'
          }
        ],
        statusData: {
          statusColorCode: JSON.stringify([
            { status: 'approved', backgroundColor: '#28a745', fontColor: '#ffffff' },
            { status: 'pending', backgroundColor: '#ffc107', fontColor: '#000000' },
            { status: 'delivered', backgroundColor: '#17a2b8', fontColor: '#ffffff' },
            { status: 'rejected', backgroundColor: '#dc3545', fontColor: '#ffffff' },
            { status: 'expired', backgroundColor: '#6c757d', fontColor: '#ffffff' }
          ]),
          useTextColorAsLegend: 'false',
          isDefaultColor: 'false'
        },
        cardData: {
          concreteCard: JSON.stringify([
            { label: 'Description', line: 1, selected: true }
          ])
        }
      };

      projectSharingService.getConcreteRequest.mockReturnValue(of(mockResponseWithoutCreatedUser));
      component.calendarApi = {
        removeAllEventSources: jest.fn(),
        addEventSource: jest.fn()
      };
      component.Range = {
        start: new Date('2024-03-01'),
        end: new Date('2024-03-31')
      };

      component.getConcreteRequest();

      expect(component.events[0].creatorId).toBeNull();
    });

    it('should handle openRequestDetail with empty arg', () => {
      // Mock the component to handle the case where arg.event is undefined
      component.openRequestDetail({});
      // Should not throw error and return early
      expect(true).toBe(true); // Test passes if no error is thrown
    });

    it('should handle openRequestDetail with event without uniqueNumber', () => {
      // Set up component state to allow modal to be shown
      component.isGuestUser = false; // Not a guest user, so no restrictions
      component.ParentCompanyId = 456;

      const mockArg = {
        event: {
          id: '1',
          _def: {
            extendedProps: {
              isGuestRequest: false,
              creatorId: 123,
              guestMemberId: 789,
              requestType: 'concreteRequest'
            }
          },
          extendedProps: {} // No uniqueNumber, so it will use findIndex by id
        }
      };

      component.concreteRequest = [{
        id: 1,
        ConcreteRequestId: 123,
        ProjectId: 123,
        requestType: 'concreteRequest' // This is NOT 'calendarEvent', so modal should be shown
      }];

      modalService.show.mockReturnValue({
        content: { closeBtnName: 'Close' }
      } as BsModalRef);

      component.openRequestDetail(mockArg);

      expect(modalService.show).toHaveBeenCalled();
    });

    it('should handle calendarDescription with empty arg', () => {
      component.calendarDescription({});
      expect(component.calendarDescriptionPopup).toBe(false);
      expect(component.descriptionPopup).toBe(false);
      expect(component.viewEventData).toBe('');
    });

    it('should handle calendarDescription with valid event', () => {
      const mockArg = {
        event: {
          title: 'Test Event',
          extendedProps: {
            uniqueNumber: '123'
          }
        }
      };

      component.events = [{
        description: 'Test Event',
        uniqueNumber: '123'
      }];

      component.concreteRequest = [{
        description: 'Test Event',
        uniqueNumber: '123',
        repeatEveryType: 'Day',
        repeatEveryCount: 1,
        days: [],
        chosenDateOfMonth: null,
        dateOfMonth: null,
        monthlyRepeatType: null,
        endTime: '2023-01-31'
      }];

      jest.spyOn(component, 'occurMessage').mockImplementation(() => {});

      component.calendarDescription(mockArg);

      expect(component.calendarDescriptionPopup).toBe(true);
      expect(component.viewEventData).toBeDefined();
      expect(component.occurMessage).toHaveBeenCalled();
    });
  });
});
