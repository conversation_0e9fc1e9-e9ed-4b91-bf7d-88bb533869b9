<div *ngIf="billingList">
  <div class="card notify-card white-border my-3 pb-4 c-pointer" (click)="showHistory()" (keydown)="handleToggleKeydown($event)">
    <div class="card-body pb-2">
      <h1 class="fw600 fs14 mb-4 color-grey7">Project Name 1</h1>
      <div class="row">
        <div class="col-sm-4 col-md-3 col-lg-3">
          <h2 class="color-grey8 fs13 fw600 mb-1">Project ID</h2>
          <p class="color-grey7 fs13 fw600">12154</p>
        </div>
        <div class="col-sm-4 col-md-3 col-lg-3">
          <h2 class="color-grey8 fs13 fw600 mb-1">Subscription Type</h2>
          <p class="color-grey7 fs13 fw600">Trial Plan</p>
        </div>
        <div class="col-sm-4 col-md-3 col-lg-3">
          <h2 class="color-grey8 fs13 fw600 mb-1">Subscribed On</h2>
          <p class="color-grey7 fs13 fw600">8/19/2020</p>
        </div>
        <div class="col-sm-4 col-md-3 col-lg-3">
          <h2 class="color-grey8 fs13 fw600 mb-1">Last Payment On</h2>
          <p class="color-grey7 fs13 fw600">3/21/2021</p>
        </div>
      </div>
    </div>
  </div>

  <div class="card notify-card white-border my-3 pb-4 c-pointer">
    <div class="card-body pb-2">
      <h1 class="fw600 fs14 mb-4 color-grey7">Project Name 2</h1>
      <div class="row">
        <div class="col-sm-4 col-md-3 col-lg-3">
          <h2 class="color-grey8 fs13 fw600 mb-1">Project ID</h2>
          <p class="color-grey7 fs13 fw600">56695</p>
        </div>
        <div class="col-sm-4 col-md-3 col-lg-3">
          <h2 class="color-grey8 fs13 fw600 mb-1">Subscription Type</h2>
          <p class="color-grey7 fs13 fw600">Enterprises Plan</p>
        </div>
        <div class="col-sm-4 col-md-3 col-lg-3">
          <h2 class="color-grey8 fs13 fw600 mb-1">Subscribed On</h2>
          <p class="color-grey7 fs13 fw600">8/19/2020</p>
        </div>
        <div class="col-sm-4 col-md-3 col-lg-3">
          <h2 class="color-grey8 fs13 fw600 mb-1">Last Payment On</h2>
          <p class="color-grey7 fs13 fw600">3/21/2021</p>
        </div>
      </div>
    </div>
  </div>
</div>
<!--Billing History-->
<div *ngIf="showbillingHistory">
  <a href="javascript:void(0)" class="color-orange text-underline fw600 fs12" (click)="backtoList()"
    >Back</a
  >
  <h2 class="fs18 fw-bold color-grey7 mt-3">Project Name 1</h2>
  <div class="card notify-card white-border my-3 pb-4">
    <div class="card-body p-0">
      <h1 class="fw600 fs14 mb-0 color-grey7 p-3">Payment History</h1>
      <div class="table-responsive tab-grid">
        <table class="table table-custom mb-0" aria-describedby="Emptable">
          <thead>
            <th scope="col" resizable>Amount</th>
            <th scope="col" resizable>Status</th>
            <th scope="col" resizable>Recepient</th>
            <th scope="col" resizable>Date Paid</th>
            <th scope="col" resizable>Payment Method</th>
            <th scope="col" resizable>Invoice</th>
          </thead>
          <tbody>
            <tr>
              <td>$90.00</td>
              <td class="status-success">Completed</td>
              <td>John Doe</td>
              <td>06/02/2020</td>
              <td>Offline</td>
              <td>
                <button
                  class="btn btn-primary-grey-outline fs10 fw600 color-grey20 radius20 px-2 py-1"
                >
                  Download
                </button>
              </td>
            </tr>
            <tr>
              <td>$90.00</td>
              <td class="status-danger">Pendiing</td>
              <td>John Doe</td>
              <td>06/02/2020</td>
              <td>Offline</td>
              <td>
                <button
                  class="btn btn-primary-grey-outline fs10 fw600 color-grey20 radius20 px-2 py-1"
                >
                  Download
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
