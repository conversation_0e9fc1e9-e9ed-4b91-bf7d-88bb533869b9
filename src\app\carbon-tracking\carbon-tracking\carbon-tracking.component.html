<ngx-loading [show]="loader" [config]="{ backdropBorderRadius: '3px' }"> </ngx-loading>

<section class="dashboard-container" *ngIf="loader===false">
  <!-- CO₂ Emissions Summary Cards -->
  <h3>CO₂ Emissions Summary</h3>
  <div class="carbon-card-container">
    <div class="card">
      <h3>Total CO₂ Emissions</h3>
      <p>{{ totalCO2Emissions | number: '1.2-2' }} kg</p>
    </div>
    <div *ngFor="let card of groupedChartData" class="card">
      <h3>{{ card?.name }}</h3>
      <p>{{ card?.co2Emission | number: '1.2-2' }} kg</p>
    </div>
  </div>

  <!-- Charts Row -->
  <div class="row">
    <!-- Bar Chart Section -->
    <div class="col-md-6">
      <div class="chart-container">
        <h3>Cost Breakdown by Utility</h3>
        <div class="chart-wrapper">
          <ngx-charts-bar-vertical
            [results]="chartData"
            [legend]="true"
            [showXAxis]="true"
            [showYAxis]="true"
            [showLegend]="true"
            [xAxis]="true"
            [yAxis]="true"
            [gradient]="false"
            [scheme]="colorScheme"
            [legendPosition]="'below'"
            [activeEntries]="activeEntries"
            [animations]="false"
            preventLegendToggle="true"
            (activate)="onActivate($event)"
            (deactivate)="onDeactivate($event)"
            [rotateXAxisTicks]="false"
            [xAxisTickFormatting]="formatXAxisTick">
          </ngx-charts-bar-vertical>
        </div>
      </div>
    </div>

    <!-- Pie Chart Section -->
    <div class="col-md-6">
      <div class="chart-container">
        <h3>CO₂ Emissions Distribution</h3>
        <div class="chart-wrapper">
          <ngx-charts-pie-chart
            [results]="pieChartData"
            [legend]="true"
            [showLabels]="true"
            [explodeSlices]="false"
            [doughnut]="false"
            [gradient]="false"
            [scheme]="colorScheme"
            [legendPosition]="'right'"
            [doughnut]="true"
            >
          </ngx-charts-pie-chart>
        </div>
      </div>
    </div>
  </div>
</section>
