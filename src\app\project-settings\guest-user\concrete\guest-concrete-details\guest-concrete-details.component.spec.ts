import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UntypedFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { BsModalService, BsModalRef, ModalOptions } from 'ngx-bootstrap/modal';
import { Socket } from 'ngx-socket-io';
import { ToastrService } from 'ngx-toastr';
import { of, throwError } from 'rxjs';
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { DeliveryService } from '../../../../services/profile/delivery.service';
import { MixpanelService } from '../../../../services/mixpanel.service';
import { ProjectService } from '../../../../services/profile/project.service';
import { ProjectSharingService } from '../../../../services/projectSharingService/project-sharing.service';
import { GuestConcreteDetailsComponent } from './guest-concrete-details.component';

describe('GuestConcreteDetailsComponent', () => {
  let component: GuestConcreteDetailsComponent;
  let fixture: ComponentFixture<GuestConcreteDetailsComponent>;
  let modalService: jest.Mocked<BsModalService>;
  let modalRef: jest.Mocked<BsModalRef>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let socket: jest.Mocked<Socket>;
  let mixpanelService: jest.Mocked<MixpanelService>;
  let formBuilder: UntypedFormBuilder;
  let router: jest.Mocked<Router>;
  let toastr: jest.Mocked<ToastrService>;
  let projectService: jest.Mocked<ProjectService>;
  let projectSharingService: jest.Mocked<ProjectSharingService>;

  beforeEach(async () => {
    const modalServiceSpy = {
      show: jest.fn(),
    };
    const modalRefSpy = {
      hide: jest.fn(),
    };
    const deliveryServiceSpy = {};
    const socketSpy = {
      emit: jest.fn()
    };
    const mixpanelServiceSpy = {
      addGuestUserMixpanelEvents: jest.fn(),
    };
    const routerSpy = {
      navigate: jest.fn()
    };
    const toastrSpy = {
      success: jest.fn(),
      error: jest.fn(),
    };
    const projectServiceSpy = {};
    const projectSharingServiceSpy = {
      guestGetConcreteRequestDetail: jest.fn(),
      guestCreateConcreteRequestComment: jest.fn(),
      guestAddConcreteRequestAttachment: jest.fn()
    };

    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule],
      declarations: [GuestConcreteDetailsComponent],
      providers: [
        { provide: BsModalService, useValue: modalServiceSpy },
        { provide: BsModalRef, useValue: modalRefSpy },
        { provide: DeliveryService, useValue: deliveryServiceSpy },
        { provide: Socket, useValue: socketSpy },
        { provide: MixpanelService, useValue: mixpanelServiceSpy },
        { provide: UntypedFormBuilder, useValue: new UntypedFormBuilder() },
        { provide: Router, useValue: routerSpy },
        { provide: ToastrService, useValue: toastrSpy },
        { provide: ProjectService, useValue: projectServiceSpy },
        { provide: ProjectSharingService, useValue: projectSharingServiceSpy },
        { provide: ModalOptions, useValue: {} },
      ],
    })
    .compileComponents();

    modalService = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    modalRef = TestBed.inject(BsModalRef) as jest.Mocked<BsModalRef>;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    socket = TestBed.inject(Socket) as jest.Mocked<Socket>;
    mixpanelService = TestBed.inject(MixpanelService) as jest.Mocked<MixpanelService>;
    formBuilder = TestBed.inject(UntypedFormBuilder);
    router = TestBed.inject(Router) as jest.Mocked<Router>;
    toastr = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    projectSharingService = TestBed.inject(ProjectSharingService) as jest.Mocked<ProjectSharingService>;
  });

  beforeEach(() => {
    // Mock window.atob to return the actual values we want
    window.atob = jest.fn().mockImplementation((str: string) => {
      const values = {
        [window.btoa('1')]: '1',
        [window.btoa('2')]: '2',
        [window.btoa('3')]: '3'
      };
      return values[str] || '';
    });

    // Mock localStorage
    const mockLocalStorage = {
      getItem: jest.fn((key: string) => {
        const values = {
          guestProjectId: window.btoa('1'),
          guestParentCompanyId: window.btoa('2'),
          guestId: window.btoa('3')
        };
        return values[key] || null;
      })
    };
    Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

    // Mock the service method before creating component
    projectSharingService.guestGetConcreteRequestDetail.mockReturnValue(of({
      data: {
        id: 1,
        attachments: [],
        comments: [],
        history: []
      }
    }));

    fixture = TestBed.createComponent(GuestConcreteDetailsComponent);
    component = fixture.componentInstance;
    component.data = { id: 1 };
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call getConcreteRequest on ngOnInit', () => {
    const getConcreteRequestSpy = jest.spyOn(component, 'getConcreteRequest');
    component.ngOnInit();
    expect(getConcreteRequestSpy).toHaveBeenCalled();
  });

  it('should get concrete request details successfully', () => {
    const mockResponse = {
      data: {
        id: 1,
        attachments: [{ id: 1, fileName: 'test.pdf' }],
        comments: [{ id: 1, comment: 'Test comment' }],
        history: [
          { id: 1, type: 'comment', action: 'added' },
          { id: 2, type: 'status', action: 'changed' }
        ]
      }
    };

    // Ensure the component has the required properties set
    component.ProjectId = 1;
    component.ParentCompanyId = 2;
    component.ConcreteRequestId = 1;

    projectSharingService.guestGetConcreteRequestDetail.mockReturnValue(of(mockResponse));

    // Call the method directly
    component.getConcreteRequest();

    expect(projectSharingService.guestGetConcreteRequestDetail).toHaveBeenCalledWith({
      ConcreteRequestId: 1,
      ParentCompanyId: 2,
      ProjectId: 1
    });
    expect(component.concreteRequest).toEqual(mockResponse.data);
    expect(component.fileArray).toEqual(mockResponse.data.attachments);
    expect(component.commentList).toEqual(mockResponse.data.comments);
    expect(component.historyList).toEqual([{ id: 2, type: 'status', action: 'changed' }]);
    expect(component.loader).toBe(false);
  });

  it('should handle error in getConcreteRequest', () => {
    projectSharingService.guestGetConcreteRequestDetail.mockReturnValue(throwError(() => new Error('API Error')));
    component.getConcreteRequest();
    expect(component.loader).toBe(true);
  });

  it('should initialize comment form correctly', () => {
    component.commentForm();
    const commentControl = component.commentDetailsForm.get('comment');
    expect(commentControl).toBeTruthy();
    // Check if the validator exists in the validators array
    const validators = commentControl.validator && commentControl.validator({} as any);
    expect(validators?.required).toBeTruthy();
  });

  it('should handle comment submission successfully', () => {
    const mockResponse = { message: 'Comment added successfully' };
    projectSharingService.guestCreateConcreteRequestComment.mockReturnValue(of(mockResponse));

    component.commentForm();
    component.commentDetailsForm.patchValue({ comment: 'Test comment' });
    component.onSubmit();

    expect(projectSharingService.guestCreateConcreteRequestComment).toHaveBeenCalled();
    expect(toastr.success).toHaveBeenCalledWith('Comment added successfully', 'Success');
    expect(mixpanelService.addGuestUserMixpanelEvents).toHaveBeenCalledWith('Comment added against a Concrete Booking');
    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
  });

  it('should handle comment submission error', () => {
    const error = {
      message: {
        details: [{ message: 'Error message' }],
      },
    };
    projectSharingService.guestCreateConcreteRequestComment.mockReturnValue(throwError(() => error));

    component.commentForm();
    component.commentDetailsForm.patchValue({ comment: 'Test comment' });
    component.onSubmit();

    expect(toastr.error).toHaveBeenCalled();
    expect(component.submitted).toBe(false);
  });

  it('should handle empty comment submission', () => {
    component.commentForm();
    component.commentDetailsForm.patchValue({ comment: '   ' });
    component.onSubmit();

    expect(toastr.error).toHaveBeenCalledWith('Please enter a valid comment.', 'OOPS!');
    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
  });

  it('should get responsible people initials correctly', () => {
    const person = { firstName: 'John', lastName: 'Doe' };
    expect(component.getResponsiblePeople(person)).toBe('JD');
  });

  it('should return default initials for missing name', () => {
    expect(component.getResponsiblePeople({})).toBe('UU');
  });

  it('should validate file extensions correctly', () => {
    expect(component.isValidExtension('jpg')).toBe(true);
    expect(component.isValidExtension('pdf')).toBe(true);
    expect(component.isValidExtension('doc')).toBe(true);
    expect(component.isValidExtension('exe')).toBe(false);
  });

  it('should get file extension correctly', () => {
    expect(component.getFileExtension('test.jpg')).toBe('jpg');
    expect(component.getFileExtension('document.pdf')).toBe('pdf');
  });

  it('should handle file drop events correctly', () => {
    const mockFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile),
      name: 'test.pdf',
      isFile: true,
      isDirectory: false
    } as FileSystemFileEntry;

    const mockDropEvent: NgxFileDropEntry[] = [{
      relativePath: 'test.pdf',
      fileEntry: mockFileEntry
    }];

    component.dropped(mockDropEvent);
    expect(component.files).toEqual(mockDropEvent);
  });

  it('should handle file over event', () => {
    const event = {};
    component.fileOver(event);
    // Just verifying the method can be called without errors
    expect(component).toBeTruthy();
  });

  it('should handle file leave event', () => {
    const event = {};
    component.fileLeave(event);
    // Just verifying the method can be called without errors
    expect(component).toBeTruthy();
  });

  it('should handle error in file upload', () => {
    projectSharingService.guestAddConcreteRequestAttachment.mockReturnValue(
      throwError(() => new Error('Upload failed'))
    );

    // Setup file data
    const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });
    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile),
      name: 'test.pdf',
      isFile: true,
      isDirectory: false
    } as FileSystemFileEntry;

    component.fileData = [[{
      relativePath: 'test.pdf',
      fileEntry: mockFileEntry
    }]];

    component.uploadData();
    expect(toastr.error).toHaveBeenCalled();
  });

  it('should handle file drop with invalid extension', () => {
    const mockFileEntry = {
      file: jest.fn(),
      name: 'test.exe',
      isFile: true,
      isDirectory: false
    } as unknown as FileSystemFileEntry;

    const mockDropEntry: NgxFileDropEntry = {
      relativePath: 'test.exe',
      fileEntry: mockFileEntry
    };

    component.dropped([mockDropEntry]);
    expect(toastr.error).toHaveBeenCalledWith(
      'Please select a valid file. Supported file format (.jpg,.jpeg,.png,.pdf,.doc)',
      'OOPS!'
    );
  });

  it('should handle file upload successfully', () => {
    const mockResponse = { message: 'File uploaded successfully' };
    projectSharingService.guestAddConcreteRequestAttachment.mockReturnValue(of(mockResponse));

    // Create a mock FormData
    const originalFormData = window.FormData;
    const mockFormData = {
      append: jest.fn()
    };
    window.FormData = jest.fn(() => mockFormData) as any;

    // Setup file data
    const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });
    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile),
      name: 'test.pdf',
      isFile: true,
      isDirectory: false
    } as FileSystemFileEntry;

    const mockDropEntry: NgxFileDropEntry = {
      relativePath: 'test.pdf',
      fileEntry: mockFileEntry
    };

    component.fileData = [[mockDropEntry]];
    component.uploadData();

    expect(projectSharingService.guestAddConcreteRequestAttachment).toHaveBeenCalled();
    expect(toastr.success).toHaveBeenCalledWith('File uploaded successfully', 'SUCCESS!');

    // Restore original FormData
    window.FormData = originalFormData;
  });

  it('should handle file upload error', () => {
    const error = { message: 'Upload failed' };
    projectSharingService.guestAddConcreteRequestAttachment.mockReturnValue(throwError(() => error));

    // Create a mock FormData
    const originalFormData = window.FormData;
    const mockFormData = {
      append: jest.fn()
    };
    window.FormData = jest.fn(() => mockFormData) as any;

    // Setup file data
    const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });
    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile),
      name: 'test.pdf',
      isFile: true,
      isDirectory: false
    } as FileSystemFileEntry;

    const mockDropEntry: NgxFileDropEntry = {
      relativePath: 'test.pdf',
      fileEntry: mockFileEntry
    };

    component.fileData = [[mockDropEntry]];
    component.uploadData();

    expect(toastr.error).toHaveBeenCalled();

    // Restore original FormData
    window.FormData = originalFormData;
  });

  it('should open modal', () => {
    const template = {} as any;
    component.closeModal(template);
    expect(modalService.show).toHaveBeenCalled();
  });

  it('should reset form when action is "yes"', () => {
    component.modalRef1 = modalRef;
    component.bsModalRef = modalRef;
    component.resetForm('yes');
    expect(modalRef.hide).toHaveBeenCalledTimes(2);
  });

  it('should reset form when action is "no"', () => {
    component.modalRef1 = modalRef;
    component.resetForm('no');
    expect(modalRef.hide).toHaveBeenCalledTimes(1);
  });

  it('should handle keyboard events correctly', () => {
    const mockEvent = { key: 'Enter', preventDefault: jest.fn() };

    // Create a properly structured fileData array with the expected properties
    component.fileData = [
      [{ relativePath: 'test.pdf' }]
    ];

    // Create a mock formData object
    component.formData = new FormData();

    // Mock the methods to avoid actual implementation
    jest.spyOn(component, 'removeFile').mockImplementation(() => {});
    jest.spyOn(component, 'openEditModal').mockImplementation(() => {});

    // Test 'remove' type
    component.handleDownKeydown(mockEvent as any, 0, 0, 'remove');
    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(component.removeFile).toHaveBeenCalledWith(0, 0);

    // Test 'open' type
    component.handleDownKeydown(mockEvent as any, 0, 0, 'open');
    expect(component.openEditModal).toHaveBeenCalledWith(0, 0);
  });

  // Additional positive test cases
  it('should handle keyboard events with space key', () => {
    const mockEvent = { key: ' ', preventDefault: jest.fn() };
    jest.spyOn(component, 'removeFile').mockImplementation(() => {});

    component.handleDownKeydown(mockEvent as any, 0, 0, 'remove');
    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(component.removeFile).toHaveBeenCalledWith(0, 0);
  });

  it('should not handle keyboard events for other keys', () => {
    const mockEvent = { key: 'Tab', preventDefault: jest.fn() };
    jest.spyOn(component, 'removeFile').mockImplementation(() => {});

    component.handleDownKeydown(mockEvent as any, 0, 0, 'remove');
    expect(mockEvent.preventDefault).not.toHaveBeenCalled();
    expect(component.removeFile).not.toHaveBeenCalled();
  });

  it('should handle default case in keyboard events', () => {
    const mockEvent = { key: 'Enter', preventDefault: jest.fn() };

    component.handleDownKeydown(mockEvent as any, 0, 0, 'unknown');
    expect(mockEvent.preventDefault).toHaveBeenCalled();
  });

  it('should initialize constructor properties correctly', () => {
    expect(component.ProjectId).toBe(1);
    expect(component.ParentCompanyId).toBe(2);
    expect(component.guestUserId).toBe(3);
    expect(component.formData).toBeInstanceOf(FormData);
    expect(component.commentDetailsForm).toBeDefined();
  });

  it('should process file with valid size and extension', () => {
    const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });
    Object.defineProperty(mockFile, 'size', { value: 1000000 }); // 1MB

    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile)
    } as FileSystemFileEntry;

    // Initialize fileData structure
    component.fileData = [[]];
    component.fileData[0][0] = { relativePath: 'test.jpg' };

    // Mock FileReader
    const mockFileReader = {
      onload: null,
      readAsDataURL: jest.fn(),
      result: 'data:image/jpeg;base64,test'
    };
    (window as any).FileReader = jest.fn(() => mockFileReader);

    component.processFile(mockFileEntry, 0, 0, 'jpg');

    // Simulate FileReader onload
    mockFileReader.onload();

    expect(component.fileData[0][0].extension).toBe('jpg');
    expect(mockFileReader.readAsDataURL).toHaveBeenCalledWith(mockFile);
  });

  it('should check string empty values correctly for valid input', () => {
    const result = component.checkStringEmptyValues({ comment: 'Valid comment' });
    expect(result).toBe(false);
  });

  it('should initialize series options correctly', () => {
    component.initializeSeriesOption();
    expect(component.seriesOptions).toHaveLength(2);
    expect(component.seriesOptions[0]).toEqual({
      option: 1,
      text: 'This event',
      disabled: false
    });
    expect(component.seriesOptions[1]).toEqual({
      option: 2,
      text: 'This and all following events',
      disabled: false
    });
  });

  it('should handle changeRequestCollapse for future dates', () => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 1);

    const mockData = {
      concretePlacementStart: futureDate.toISOString()
    };

    component.changeRequestCollapse(mockData);
    expect(component.allRequestIsOpened).toBe(true);
    expect(component.seriesOptions).toHaveLength(2);
    expect(component.seriesOptions.every(option => !option.disabled)).toBe(true);
  });

  it('should open edit modal with recurrence data', () => {
    const mockItem = {
      ProjectId: 1,
      recurrence: {
        id: 123,
        recurrence: 'Weekly',
        recurrenceEndDate: '2024-12-31'
      }
    };

    modalService.show.mockReturnValue(modalRef);
    modalRef.content = {
      closeBtnName: '',
      seriesOption: 0,
      recurrenceId: null,
      recurrenceEndDate: null
    };

    component.openEditModal(mockItem, 2);

    expect(modalService.show).toHaveBeenCalled();
    expect(modalRef.content.seriesOption).toBe(2);
    expect(modalRef.content.recurrenceId).toBe(123);
    expect(modalRef.content.recurrenceEndDate).toBe('2024-12-31');
  });

  it('should handle successful file upload with mixpanel and socket events', () => {
    const mockResponse = { message: 'File uploaded successfully' };
    projectSharingService.guestAddConcreteRequestAttachment.mockReturnValue(of(mockResponse));

    const getConcreteRequestSpy = jest.spyOn(component, 'getConcreteRequest').mockImplementation(() => {});

    // Setup file data
    const mockFile = new File([''], 'test.pdf', { type: 'application/pdf' });
    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile)
    } as FileSystemFileEntry;

    component.fileData = [[{
      relativePath: 'test.pdf',
      fileEntry: mockFileEntry
    }]];

    component.uploadData();

    expect(mixpanelService.addGuestUserMixpanelEvents).toHaveBeenCalledWith('Attachment added against a Concrete Booking');
    expect(socket.emit).toHaveBeenCalledWith('ConcreteApproveHistory', mockResponse);
    expect(getConcreteRequestSpy).toHaveBeenCalled();
    expect(component.fileData).toEqual([]);
    expect(component.uploadSubmitted).toBe(false);
  });

  // Additional negative test cases
  it('should handle missing localStorage values gracefully', () => {
    // Mock localStorage to return null
    const mockLocalStorage = {
      getItem: jest.fn().mockReturnValue(null)
    };
    Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

    // Mock atob to handle null values
    window.atob = jest.fn().mockImplementation(() => {
      throw new Error('Invalid base64');
    });

    // This should not throw an error
    expect(() => {
      new GuestConcreteDetailsComponent(
        modalService,
        modalRef,
        deliveryService,
        socket,
        modalRef,
        mixpanelService,
        formBuilder,
        router,
        toastr,
        projectService,
        projectSharingService,
        {}
      );
    }).not.toThrow();
  });

  it('should handle processFile with oversized file', () => {
    const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });
    Object.defineProperty(mockFile, 'size', { value: 3000000 }); // 3MB (over limit)

    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile)
    } as FileSystemFileEntry;

    component.fileData = [[]];
    component.processFile(mockFileEntry, 0, 0, 'jpg');

    expect(toastr.error).toHaveBeenCalledWith('Please choose a attachment less than or equal to 2MB');
    expect(component.fileData).toEqual([]);
  });

  it('should handle processFile when fileData group is removed', () => {
    const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });
    Object.defineProperty(mockFile, 'size', { value: 1000000 }); // 1MB

    const mockFileEntry = {
      file: (callback: (file: File) => void) => callback(mockFile)
    } as FileSystemFileEntry;

    // Start with empty fileData
    component.fileData = [];

    component.processFile(mockFileEntry, 0, 0, 'jpg');

    // Should not throw error when accessing undefined fileData[0]
    expect(component).toBeTruthy();
  });

  it('should check string empty values correctly for empty input', () => {
    const result = component.checkStringEmptyValues({ comment: '   ' });
    expect(result).toBe(true);
  });

  it('should check string empty values correctly for empty string', () => {
    const result = component.checkStringEmptyValues({ comment: '' });
    expect(result).toBe(true);
  });

  it('should handle showError with complex error structure', () => {
    const error = {
      message: {
        details: [{ field: 'test', message: 'Test error message' }]
      }
    };

    component.showError(error);
    expect(toastr.error).toHaveBeenCalledWith(['test', 'Test error message']);
  });

  it('should handle showCommentError with complex error structure', () => {
    const error = {
      message: {
        details: [{ field: 'comment', message: 'Comment error message' }]
      }
    };

    component.showCommentError(error);
    expect(toastr.error).toHaveBeenCalledWith(['comment', 'Comment error message']);
    expect(component.submitted).toBe(false);
    expect(component.formSubmitted).toBe(false);
  });

  it('should handle removeFile method correctly', () => {
    // Setup fileData with mock data
    component.fileData = [
      [{ relativePath: 'file1.pdf' }, { relativePath: 'file2.jpg' }],
      [{ relativePath: 'file3.png' }]
    ];

    // Mock FormData delete method
    component.formData = {
      delete: jest.fn()
    } as any;

    // Mock splice methods
    const spliceSpy1 = jest.spyOn(component.fileData[0], 'splice');
    const spliceSpy2 = jest.spyOn(component.fileData, 'splice');

    component.removeFile(0, 1);

    expect(component.formData.delete).toHaveBeenCalledWith('file2.jpg');
    expect(spliceSpy1).toHaveBeenCalledWith(1);
    expect(spliceSpy2).toHaveBeenCalledWith(0);
  });

  it('should handle changeRequestCollapse for past dates', () => {
    const pastDate = new Date();
    pastDate.setDate(pastDate.getDate() - 1);

    const mockData = {
      concretePlacementStart: pastDate.toISOString()
    };

    component.changeRequestCollapse(mockData);
    expect(component.allRequestIsOpened).toBe(true);
    expect(component.seriesOptions[1].disabled).toBe(true);
  });

  it('should handle openEditModal without recurrence data', () => {
    const mockItem = {
      ProjectId: 1,
      recurrence: null
    };

    modalService.show.mockReturnValue(modalRef);
    modalRef.content = {
      closeBtnName: '',
      seriesOption: 0,
      recurrenceId: null,
      recurrenceEndDate: null
    };

    component.openEditModal(mockItem, 1);

    expect(modalRef.content.seriesOption).toBe(1);
    expect(modalRef.content.recurrenceId).toBeNull();
    expect(modalRef.content.recurrenceEndDate).toBeNull();
  });

  it('should handle openEditModal with "Does Not Repeat" recurrence', () => {
    const mockItem = {
      ProjectId: 1,
      recurrence: {
        id: 123,
        recurrence: 'Does Not Repeat',
        recurrenceEndDate: '2024-12-31'
      }
    };

    modalService.show.mockReturnValue(modalRef);
    modalRef.content = {
      closeBtnName: '',
      seriesOption: 0,
      recurrenceId: null,
      recurrenceEndDate: null
    };

    component.openEditModal(mockItem, 2);

    expect(modalRef.content.seriesOption).toBe(1);
  });

  it('should handle openEditModal when modalRef exists', () => {
    component.modalRef = modalRef;
    const mockItem = { ProjectId: 1 };

    modalService.show.mockReturnValue(modalRef);
    modalRef.content = {
      closeBtnName: '',
      seriesOption: 0,
      recurrenceId: null,
      recurrenceEndDate: null
    };

    component.openEditModal(mockItem, 1);

    expect(modalRef.hide).toHaveBeenCalled();
  });

  it('should handle upload error with statusCode 400', () => {
    const error = {
      message: {
        statusCode: 400,
        details: [{ field: 'attachment', message: 'Invalid file' }]
      }
    };

    projectSharingService.guestAddConcreteRequestAttachment.mockReturnValue(throwError(() => error));

    const showErrorSpy = jest.spyOn(component, 'showError').mockImplementation(() => {});

    component.fileData = [[{
      relativePath: 'test.pdf',
      fileEntry: {
        file: (callback: (file: File) => void) => callback(new File([''], 'test.pdf'))
      } as FileSystemFileEntry
    }]];

    component.uploadData();

    expect(showErrorSpy).toHaveBeenCalledWith(error);
    expect(component.uploadSubmitted).toBe(false);
  });

  it('should handle comment submission error with statusCode 400', () => {
    const error = {
      message: {
        statusCode: 400,
        details: [{ field: 'comment', message: 'Invalid comment' }]
      }
    };

    projectSharingService.guestCreateConcreteRequestComment.mockReturnValue(throwError(() => error));

    const showCommentErrorSpy = jest.spyOn(component, 'showCommentError').mockImplementation(() => {});

    component.commentForm();
    component.commentDetailsForm.patchValue({ comment: 'Test comment' });
    component.onSubmit();

    expect(showCommentErrorSpy).toHaveBeenCalledWith(error);
  });

  it('should handle getConcreteRequest when ProjectId or ParentCompanyId is missing', () => {
    component.ProjectId = null;
    component.ParentCompanyId = null;

    component.getConcreteRequest();

    expect(projectSharingService.guestGetConcreteRequestDetail).not.toHaveBeenCalled();
    expect(component.loader).toBe(true);
  });

  it('should handle form submission when form is invalid', () => {
    component.commentForm();
    component.commentDetailsForm.patchValue({ comment: '' });

    component.onSubmit();

    expect(component.submitted).toBe(true);
    expect(component.formSubmitted).toBe(false);
    expect(projectSharingService.guestCreateConcreteRequestComment).not.toHaveBeenCalled();
  });

  it('should handle getResponsiblePeople with partial name data', () => {
    expect(component.getResponsiblePeople({ firstName: 'John' })).toBe('UU');
    expect(component.getResponsiblePeople({ lastName: 'Doe' })).toBe('UU');
    expect(component.getResponsiblePeople(null)).toBe('UU');
    expect(component.getResponsiblePeople(undefined)).toBe('UU');
  });

  it('should handle file extension extraction for files without extension', () => {
    expect(component.getFileExtension('filename')).toBe('filename');
    expect(component.getFileExtension('')).toBe('');
  });

  it('should validate file extensions correctly for edge cases', () => {
    expect(component.isValidExtension('JPG')).toBe(false); // case sensitive
    expect(component.isValidExtension('PDF')).toBe(false); // case sensitive
    expect(component.isValidExtension('')).toBe(false);
    expect(component.isValidExtension('txt')).toBe(false);
  });
});
