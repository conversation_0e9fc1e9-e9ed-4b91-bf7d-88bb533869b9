import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { ScheduledReportsComponent } from './scheduled-reports.component';
import { ProjectService } from '../../services/profile/project.service';
import { ReportsService } from '../../services/reports/reports.service';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { DeliveryService } from '../../services/profile/delivery.service';
import { of, throwError, BehaviorSubject } from 'rxjs';
import { TemplateRef } from '@angular/core';
import { NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA, Pipe, PipeTransform } from '@angular/core';
import { PaginationModule } from 'ngx-bootstrap/pagination';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { TooltipModule } from 'ngx-bootstrap/tooltip';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

// Mock paginate pipe
@Pipe({
  name: 'paginate'
})
class MockPaginatePipe implements PipeTransform {
  transform(value: any[], config: any): any[] {
    return value;
  }
}

describe('ScheduledReportsComponent', () => {
  let component: ScheduledReportsComponent;
  let fixture: ComponentFixture<ScheduledReportsComponent>;
  let projectServiceMock: any;
  let reportsServiceMock: any;
  let modalServiceMock: any;
  let toastrServiceMock: any;
  let deliveryServiceMock: any;
  let projectParentSubject: BehaviorSubject<any>;
  let loginUserSubject: BehaviorSubject<any>;

  beforeEach(async () => {
    projectParentSubject = new BehaviorSubject({ ProjectId: '123' });
    loginUserSubject = new BehaviorSubject({ UserId: 1, RoleId: 2 });

    projectServiceMock = {
      projectParent: projectParentSubject
    };

    reportsServiceMock = {
      getScheduledReports: jest.fn().mockReturnValue(of({ data: { scheduledReports: [], count: 0 } })),
      deleteScheduledReports: jest.fn().mockReturnValue(of({ message: 'Success' })),
      runNowScheduledOrSavedReport: jest.fn().mockReturnValue(of({ data: 'https://example.com/report.pdf' }))
    };

    modalServiceMock = {
      show: jest.fn().mockReturnValue({
        hide: jest.fn(),
        setClass: jest.fn()
      } as unknown as BsModalRef)
    };

    toastrServiceMock = {
      success: jest.fn(),
      error: jest.fn(),
      info: jest.fn()
    };

    deliveryServiceMock = {
      loginUser: loginUserSubject
    };

    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        FormsModule,
        PaginationModule.forRoot(),
        TooltipModule.forRoot(),
        NoopAnimationsModule
      ],
      declarations: [ScheduledReportsComponent, MockPaginatePipe],
      providers: [
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: ReportsService, useValue: reportsServiceMock },
        { provide: BsModalService, useValue: modalServiceMock },
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: DeliveryService, useValue: deliveryServiceMock }
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ScheduledReportsComponent);
    component = fixture.componentInstance;
    // Don't call detectChanges() yet to check initial values
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should create', () => {
    fixture.detectChanges();
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    // Check initial values before change detection
    expect(component.scheduleReports).toEqual([]);
    expect(component.pageSize).toBe(25);
    expect(component.pageNo).toBe(1);
    expect(component.sortColumn).toBe('id');
    expect(component.sort).toBe('DESC');
    expect(component.filterCount).toBe(0);
    expect(component.count).toBe(0);

    // Now trigger change detection
    fixture.detectChanges();
  });

  // Test all methods directly on the component instance from TestBed
  it('should get scheduled reports when ProjectId is available', fakeAsync(() => {
    fixture.detectChanges();
    const mockResponse = {
      data: {
        scheduledReports: [{ id: 1, reportName: 'Test Report' }],
        count: 1
      }
    };
    reportsServiceMock.getScheduledReports.mockReturnValue(of(mockResponse));

    component.getScheduleReports();
    tick();

    expect(reportsServiceMock.getScheduledReports).toHaveBeenCalled();
    expect(component.scheduleReports).toEqual(mockResponse.data.scheduledReports);
    expect(component.count).toBe(mockResponse.data.count);
    expect(component.loader).toBe(false);
  }));

  it('should handle page size change', fakeAsync(() => {
    fixture.detectChanges();
    const mockResponse = {
      data: {
        scheduledReports: [{ id: 1, reportName: 'Test Report' }],
        count: 50
      }
    };
    reportsServiceMock.getScheduledReports.mockReturnValue(of(mockResponse));

    component.changePageSize(50);
    tick();

    expect(component.pageSize).toBe(50);
    expect(reportsServiceMock.getScheduledReports).toHaveBeenCalled();
  }));

  it('should handle page number change', fakeAsync(() => {
    fixture.detectChanges();
    const mockResponse = {
      data: {
        scheduledReports: [{ id: 2, reportName: 'Page 2 Report' }],
        count: 50
      }
    };
    reportsServiceMock.getScheduledReports.mockReturnValue(of(mockResponse));

    component.changePageNo(2);
    tick();

    expect(component.pageNo).toBe(2);
    expect(reportsServiceMock.getScheduledReports).toHaveBeenCalled();
  }));

  it('should delete scheduled report successfully', fakeAsync(() => {
    fixture.detectChanges();
    const mockResponse = { message: 'Report deleted successfully' };
    reportsServiceMock.deleteScheduledReports.mockReturnValue(of(mockResponse));
    reportsServiceMock.getScheduledReports.mockReturnValue(of({ data: { scheduledReports: [], count: 0 } }));
    component.deleteSchedulerReportData = { id: 1 };
    component.modalRef = { hide: jest.fn(), setClass: jest.fn() } as unknown as BsModalRef;

    component.deleteScheduleReport();
    tick();

    expect(reportsServiceMock.deleteScheduledReports).toHaveBeenCalledWith({
      id: 1,
      ProjectId: '123'
    });
    expect(toastrServiceMock.success).toHaveBeenCalledWith(mockResponse.message, 'Success');
    expect(reportsServiceMock.getScheduledReports).toHaveBeenCalled();
    expect(component.deleteSchedulerReportData).toEqual({});
    expect(component.deleteSchedulerReportSubmitted).toBe(false);
    expect(component.modalRef.hide).toHaveBeenCalled();
  }));

  it('should handle error when deleting scheduled report', fakeAsync(() => {
    fixture.detectChanges();
    const error = { message: 'Error deleting report' };
    reportsServiceMock.deleteScheduledReports.mockReturnValue(throwError(() => error));
    component.deleteSchedulerReportData = { id: 1 };
    component.modalRef = { hide: jest.fn(), setClass: jest.fn() } as unknown as BsModalRef;

    component.deleteScheduleReport();
    tick();

    expect(reportsServiceMock.deleteScheduledReports).toHaveBeenCalled();
    expect(toastrServiceMock.error).toHaveBeenCalled();
    expect(component.deleteSchedulerReportSubmitted).toBe(false);
  }));

  it('should download scheduled report when action is lastRun and s3_url exists', () => {
    fixture.detectChanges();
    const data = { s3_url: 'https://example.com/report.pdf', lastRun: '2023-01-01' };
    const action = 'lastRun';

    // Mock document.createElement
    const mockAnchor = {
      setAttribute: jest.fn(),
      click: jest.fn(),
      remove: jest.fn()
    };
    const createElementSpy = jest.spyOn(document, 'createElement').mockReturnValue(mockAnchor as unknown as HTMLElement);
    const appendChildSpy = jest.spyOn(document.body, 'appendChild').mockImplementation(() => null);

    component.downloadScheduledReport(data, action);

    expect(createElementSpy).toHaveBeenCalledWith('a');
    expect(mockAnchor.setAttribute).toHaveBeenCalledWith('href', data.s3_url);
    expect(mockAnchor.click).toHaveBeenCalled();
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Report downloaded successfully');

    createElementSpy.mockRestore();
    appendChildSpy.mockRestore();
  });

  it('should download scheduled report when action is reRun', () => {
    fixture.detectChanges();
    const data = 'https://example.com/report.pdf';
    const action = 'reRun';

    // Mock document.createElement
    const mockAnchor = {
      setAttribute: jest.fn(),
      click: jest.fn(),
      remove: jest.fn()
    };
    const createElementSpy = jest.spyOn(document, 'createElement').mockReturnValue(mockAnchor as unknown as HTMLElement);
    const appendChildSpy = jest.spyOn(document.body, 'appendChild').mockImplementation(() => null);

    component.downloadScheduledReport(data, action);

    expect(createElementSpy).toHaveBeenCalledWith('a');
    expect(mockAnchor.setAttribute).toHaveBeenCalledWith('href', data);
    expect(mockAnchor.click).toHaveBeenCalled();
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Report downloaded successfully');

    createElementSpy.mockRestore();
    appendChildSpy.mockRestore();
  });

  it('should show info message when no events available for lastRun without s3_url', () => {
    fixture.detectChanges();
    const data = { lastRun: null, s3_url: null };
    const action = 'lastRun';

    component.downloadScheduledReport(data, action);

    expect(toastrServiceMock.info).toHaveBeenCalledWith('There are no events available within the scheduled date range');
  });

  it('should show info message when data exists but no s3_url for lastRun', () => {
    fixture.detectChanges();
    const data = { lastRun: '2023-01-01', s3_url: null };
    const action = 'lastRun';

    component.downloadScheduledReport(data, action);

    expect(toastrServiceMock.info).toHaveBeenCalledWith('There are no events available within the scheduled date range');
  });

  it('should return early when action is lastRun and no lastRun and no s3_url', () => {
    fixture.detectChanges();
    const data = { lastRun: null, s3_url: null };
    const action = 'lastRun';

    const createElementSpy = jest.spyOn(document, 'createElement');

    component.downloadScheduledReport(data, action);

    expect(createElementSpy).not.toHaveBeenCalled();
    createElementSpy.mockRestore();
  });

  it('should handle ngOnChanges', () => {
    fixture.detectChanges();
    const getScheduleReportsSpy = jest.spyOn(component, 'getScheduleReports');

    component.ngOnChanges();

    expect(getScheduleReportsSpy).toHaveBeenCalled();
  });

  it('should handle error when getting scheduled reports with statusCode 400', fakeAsync(() => {
    fixture.detectChanges();
    const error = { message: { statusCode: 400, details: [{ field: 'error message' }] } };
    reportsServiceMock.getScheduledReports.mockReturnValue(throwError(() => error));
    const showErrorSpy = jest.spyOn(component, 'showError');

    component.getScheduleReports();
    tick();

    expect(showErrorSpy).toHaveBeenCalledWith(error);
    expect(component.loader).toBe(false);
  }));

  it('should handle error when getting scheduled reports without message', fakeAsync(() => {
    fixture.detectChanges();
    const error = {};
    reportsServiceMock.getScheduledReports.mockReturnValue(throwError(() => error));

    component.getScheduleReports();
    tick();

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    expect(component.loader).toBe(false);
  }));

  it('should handle error when getting scheduled reports with message', fakeAsync(() => {
    fixture.detectChanges();
    const error = { message: 'Custom error message' };
    reportsServiceMock.getScheduledReports.mockReturnValue(throwError(() => error));

    component.getScheduleReports();
    tick();

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
    expect(component.loader).toBe(false);
  }));

  it('should return early from getScheduleReports when no ProjectId', () => {
    fixture.detectChanges();
    component.ProjectId = null;

    component.getScheduleReports();

    expect(component.loader).toBe(true);
    expect(component.scheduleReports).toEqual([]);
    expect(reportsServiceMock.getScheduledReports).not.toHaveBeenCalled();
  });

  it('should build payload without filter values', () => {
    fixture.detectChanges();
    component.ProjectId = '123';
    component.pageSize = 50;
    component.pageNo = 2;
    component.sort = 'ASC';
    component.sortColumn = 'reportName';
    component.getSearchText = 'test search';
    component.filterValues = null;

    const payload = component.buildPayload();

    expect(payload).toEqual({
      ProjectId: '123',
      pageSize: 50,
      pageNo: 2,
      sort: 'ASC',
      sortByField: 'reportName',
      search: 'test search'
    });
  });

  it('should build payload with filter values', () => {
    fixture.detectChanges();
    component.ProjectId = '123';
    component.pageSize = 25;
    component.pageNo = 1;
    component.sort = 'DESC';
    component.sortColumn = 'id';
    component.getSearchText = '';
    component.filterValues = {
      createdUserId: 'user123',
      reportName: 'Test Report',
      templateType: 'PDF',
      lastRun: new Date('2023-01-01')
    };

    const payload = component.buildPayload();

    expect(payload).toEqual({
      ProjectId: '123',
      pageSize: 25,
      pageNo: 1,
      sort: 'DESC',
      sortByField: 'id',
      search: '',
      createdUserId: 'user123',
      reportName: 'Test Report',
      templateType: 'PDF',
      lastRun: '2023-01-01',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    });
  });

  it('should build payload with partial filter values', () => {
    fixture.detectChanges();
    component.ProjectId = '123';
    component.filterValues = {
      createdUserId: 'user123',
      reportName: null,
      templateType: '',
      lastRun: null
    };

    const payload = component.buildPayload();

    expect(payload.createdUserId).toBe('user123');
    expect(payload.reportName).toBeUndefined();
    expect(payload.templateType).toBeUndefined();
    expect(payload.lastRun).toBeUndefined();
    expect(payload.timezone).toBeUndefined();
  });

  it('should auto download report with run option successfully', fakeAsync(() => {
    fixture.detectChanges();
    component.ProjectId = '123';
    const data = { id: 1, runNowLoader: false };
    const mockResponse = { data: 'https://example.com/report.pdf' };
    reportsServiceMock.runNowScheduledOrSavedReport.mockReturnValue(of(mockResponse));
    const downloadSpy = jest.spyOn(component, 'downloadScheduledReport');

    component.autoDownloadReportWithRunOption(data);
    tick();

    expect(data.runNowLoader).toBe(false);
    expect(reportsServiceMock.runNowScheduledOrSavedReport).toHaveBeenCalledWith({
      id: 1,
      ProjectId: '123'
    });
    expect(downloadSpy).toHaveBeenCalledWith(mockResponse.data, 'reRun');
  }));

  it('should handle no data found in auto download report', fakeAsync(() => {
    fixture.detectChanges();
    component.ProjectId = '123';
    const data = { id: 1, runNowLoader: false };
    const mockResponse = { data: 'No data found' };
    reportsServiceMock.runNowScheduledOrSavedReport.mockReturnValue(of(mockResponse));

    component.autoDownloadReportWithRunOption(data);
    tick();

    expect(data.runNowLoader).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith('There are no events available within the scheduled date range');
  }));

  it('should handle invalid URL in auto download report', fakeAsync(() => {
    fixture.detectChanges();
    component.ProjectId = '123';
    const data = { id: 1, runNowLoader: false };
    const mockResponse = { data: 'invalid-url' };
    reportsServiceMock.runNowScheduledOrSavedReport.mockReturnValue(of(mockResponse));

    component.autoDownloadReportWithRunOption(data);
    tick();

    expect(data.runNowLoader).toBe(false);
    expect(toastrServiceMock.error).toHaveBeenCalledWith('Something went wrong.');
  }));

  it('should handle error in auto download report with statusCode 400', fakeAsync(() => {
    fixture.detectChanges();
    component.ProjectId = '123';
    const data = { id: 1, runNowLoader: false };
    const error = { message: { statusCode: 400, details: [{ field: 'error message' }] } };
    reportsServiceMock.runNowScheduledOrSavedReport.mockReturnValue(throwError(() => error));
    const showErrorSpy = jest.spyOn(component, 'showError');

    component.autoDownloadReportWithRunOption(data);
    tick();

    expect(data.runNowLoader).toBe(false);
    expect(showErrorSpy).toHaveBeenCalledWith(error);
  }));

  it('should handle error in auto download report without message', fakeAsync(() => {
    fixture.detectChanges();
    component.ProjectId = '123';
    const data = { id: 1, runNowLoader: false };
    const error = {};
    reportsServiceMock.runNowScheduledOrSavedReport.mockReturnValue(throwError(() => error));

    component.autoDownloadReportWithRunOption(data);
    tick();

    expect(data.runNowLoader).toBe(false);
    expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
  }));

  it('should handle error in auto download report with message', fakeAsync(() => {
    fixture.detectChanges();
    component.ProjectId = '123';
    const data = { id: 1, runNowLoader: false };
    const error = { message: 'Custom error message' };
    reportsServiceMock.runNowScheduledOrSavedReport.mockReturnValue(throwError(() => error));

    component.autoDownloadReportWithRunOption(data);
    tick();

    expect(data.runNowLoader).toBe(false);
    expect(toastrServiceMock.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
  }));

  it('should reset and close modal', () => {
    fixture.detectChanges();
    component.modalRef = { hide: jest.fn(), setClass: jest.fn() } as unknown as BsModalRef;
    component.deleteSchedulerReportData = { id: 1, name: 'test' };

    component.resetAndClose();

    expect(component.modalRef.hide).toHaveBeenCalled();
    expect(component.deleteSchedulerReportData).toEqual({});
  });

  it('should show error message', () => {
    fixture.detectChanges();
    const error = {
      message: {
        details: [{ field: 'Test error message' }]
      }
    };

    component.showError(error);

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Test error message');
  });

  it('should handle keyboard events with space key for sorting', () => {
    fixture.detectChanges();
    const event = { key: ' ', preventDefault: jest.fn() } as unknown as KeyboardEvent;
    const sortByFieldSpy = jest.spyOn(component, 'sortByField');

    component.handleToggleKeydown(event, 'reportName', 'ASC');

    expect(event.preventDefault).toHaveBeenCalled();
    expect(sortByFieldSpy).toHaveBeenCalledWith('reportName', 'ASC');
  });

  it('should handle keyboard events with space key for actions', () => {
    fixture.detectChanges();
    const event = { key: ' ', preventDefault: jest.fn() } as unknown as KeyboardEvent;
    const data = { id: 1, reportName: 'Test Report' };
    const autoDownloadSpy = jest.spyOn(component, 'autoDownloadReportWithRunOption');

    component.handleDownKeydown(event, data, 'item', 'auto');

    expect(event.preventDefault).toHaveBeenCalled();
    expect(autoDownloadSpy).toHaveBeenCalledWith(data);
  });

  it('should not handle keyboard events for non-Enter/Space keys', () => {
    fixture.detectChanges();
    const event = { key: 'Tab', preventDefault: jest.fn() } as unknown as KeyboardEvent;
    const sortByFieldSpy = jest.spyOn(component, 'sortByField');

    component.handleToggleKeydown(event, 'reportName', 'ASC');

    expect(event.preventDefault).not.toHaveBeenCalled();
    expect(sortByFieldSpy).not.toHaveBeenCalled();
  });

  it('should handle default case in handleDownKeydown', () => {
    fixture.detectChanges();
    const event = { key: 'Enter', preventDefault: jest.fn() } as unknown as KeyboardEvent;
    const data = { id: 1, reportName: 'Test Report' };
    const downloadSpy = jest.spyOn(component, 'downloadScheduledReport');

    component.handleDownKeydown(event, data, 'item', 'unknown');

    expect(event.preventDefault).toHaveBeenCalled();
    expect(downloadSpy).not.toHaveBeenCalled();
  });

  it('should handle constructor subscriptions for loginUser', () => {
    const mockUser = { UserId: 1, RoleId: 2 };
    loginUserSubject.next(mockUser);

    expect(component.authUser).toEqual(mockUser);
  });

  it('should handle constructor subscriptions for projectParent', () => {
    const mockProject = { ProjectId: '456' };
    const getScheduleReportsSpy = jest.spyOn(component, 'getScheduleReports');

    projectParentSubject.next(mockProject);

    expect(component.ProjectId).toBe('456');
    expect(getScheduleReportsSpy).toHaveBeenCalled();
  });

  it('should not set authUser when loginUser is undefined', () => {
    component.authUser = { existing: 'data' };
    loginUserSubject.next(undefined);

    expect(component.authUser).toEqual({ existing: 'data' });
  });

  it('should not set authUser when loginUser is null', () => {
    component.authUser = { existing: 'data' };
    loginUserSubject.next(null);

    expect(component.authUser).toEqual({ existing: 'data' });
  });

  it('should not set authUser when loginUser is empty string', () => {
    component.authUser = { existing: 'data' };
    loginUserSubject.next('');

    expect(component.authUser).toEqual({ existing: 'data' });
  });

  it('should not call getScheduleReports when projectParent is undefined', () => {
    const getScheduleReportsSpy = jest.spyOn(component, 'getScheduleReports');
    projectParentSubject.next(undefined);

    expect(getScheduleReportsSpy).not.toHaveBeenCalled();
  });

  it('should not call getScheduleReports when projectParent is null', () => {
    const getScheduleReportsSpy = jest.spyOn(component, 'getScheduleReports');
    projectParentSubject.next(null);

    expect(getScheduleReportsSpy).not.toHaveBeenCalled();
  });

  it('should not call getScheduleReports when projectParent is empty string', () => {
    const getScheduleReportsSpy = jest.spyOn(component, 'getScheduleReports');
    projectParentSubject.next('');

    expect(getScheduleReportsSpy).not.toHaveBeenCalled();
  });

  it('should handle error when deleting scheduled report with statusCode 400', fakeAsync(() => {
    fixture.detectChanges();
    const error = { message: { statusCode: 400, details: [{ field: 'error message' }] } };
    reportsServiceMock.deleteScheduledReports.mockReturnValue(throwError(() => error));
    component.deleteSchedulerReportData = { id: 1 };
    component.modalRef = { hide: jest.fn(), setClass: jest.fn() } as unknown as BsModalRef;
    const showErrorSpy = jest.spyOn(component, 'showError');

    component.deleteScheduleReport();
    tick();

    expect(showErrorSpy).toHaveBeenCalledWith(error);
    expect(component.deleteSchedulerReportSubmitted).toBe(false);
  }));

  it('should handle error when deleting scheduled report without message', fakeAsync(() => {
    fixture.detectChanges();
    const error = {};
    reportsServiceMock.deleteScheduledReports.mockReturnValue(throwError(() => error));
    component.deleteSchedulerReportData = { id: 1 };
    component.modalRef = { hide: jest.fn(), setClass: jest.fn() } as unknown as BsModalRef;

    component.deleteScheduleReport();
    tick();

    expect(toastrServiceMock.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    expect(component.deleteSchedulerReportSubmitted).toBe(false);
  }));

});

// Isolated unit tests for ScheduledReportsComponent methods (without TestBed)
describe('ScheduledReportsComponent - Isolated Unit Tests', () => {
  let testComponent: ScheduledReportsComponent;
  let mockProjectService: any;
  let mockDeliveryService: any;
  let mockReportsService: any;
  let mockModalService: any;
  let mockToastrService: any;

  beforeEach(() => {
    // Create mocks that don't trigger subscriptions
    mockProjectService = {
      projectParent: {
        subscribe: jest.fn()
      }
    };
    mockDeliveryService = {
      loginUser: {
        subscribe: jest.fn()
      }
    };
    mockReportsService = {
      getScheduledReports: jest.fn().mockReturnValue(of({ data: { scheduledReports: [], count: 0 } })),
      deleteScheduledReports: jest.fn().mockReturnValue(of({ message: 'Success' })),
      runNowScheduledOrSavedReport: jest.fn().mockReturnValue(of({ data: 'https://example.com/report.pdf' }))
    };
    mockModalService = {
      show: jest.fn().mockReturnValue({
        hide: jest.fn(),
        setClass: jest.fn()
      })
    };
    mockToastrService = {
      success: jest.fn(),
      error: jest.fn(),
      info: jest.fn()
    };

    // Create component instance and manually set up properties to avoid constructor subscriptions
    testComponent = Object.create(ScheduledReportsComponent.prototype);
    testComponent.projectService = mockProjectService;
    (testComponent as any).reportsService = mockReportsService;
    (testComponent as any).modalService = mockModalService;
    (testComponent as any).toastr = mockToastrService;
    (testComponent as any).deliveryService = mockDeliveryService;

    // Initialize component properties
    testComponent.scheduleReports = [];
    testComponent.pageSize = 25;
    testComponent.pageNo = 1;
    testComponent.sortColumn = 'id';
    testComponent.sort = 'DESC';
    testComponent.filterCount = 0;
    testComponent.count = 0;
    testComponent.loader = true;
    testComponent.deleteSchedulerReportData = {};
    testComponent.deleteSchedulerReportSubmitted = false;
    testComponent.authUser = {};
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should sort by field', fakeAsync(() => {
    // Set up the component with a ProjectId to avoid early return
    testComponent.ProjectId = '123';
    const mockResponse = {
      data: {
        scheduledReports: [{ id: 1, reportName: 'Test Report' }],
        count: 1
      }
    };
    mockReportsService.getScheduledReports.mockReturnValue(of(mockResponse));

    testComponent.sortByField('reportName', 'ASC');
    tick();

    expect(testComponent.sortColumn).toBe('reportName');
    expect(testComponent.sort).toBe('ASC');
    expect(mockReportsService.getScheduledReports).toHaveBeenCalled();
  }));

  it('should handle keyboard events for sorting', () => {
    const event = { key: 'Enter', preventDefault: jest.fn() } as unknown as KeyboardEvent;
    const sortByFieldSpy = jest.spyOn(testComponent, 'sortByField').mockImplementation(() => {});

    testComponent.handleToggleKeydown(event, 'reportName', 'ASC');

    expect(event.preventDefault).toHaveBeenCalled();
    expect(sortByFieldSpy).toHaveBeenCalledWith('reportName', 'ASC');
  });

  it('should handle keyboard events for actions', () => {
    const event = { key: 'Enter', preventDefault: jest.fn() } as unknown as KeyboardEvent;
    const data = { id: 1, reportName: 'Test Report' };
    const downloadSpy = jest.spyOn(testComponent, 'downloadScheduledReport').mockImplementation(() => {});
    const openDeleteModalSpy = jest.spyOn(testComponent, 'openDeleteModal').mockImplementation(() => {});

    // Test download action
    testComponent.handleDownKeydown(event, data, 'lastRun', 'download');
    expect(event.preventDefault).toHaveBeenCalled();
    expect(downloadSpy).toHaveBeenCalledWith(data, 'lastRun');

    // Test delete action
    const template = {} as TemplateRef<any>;
    testComponent.handleDownKeydown(event, data, template, 'delete');
    expect(openDeleteModalSpy).toHaveBeenCalledWith(data, template);
  });

  it('should open modal', () => {
    const template = {} as TemplateRef<any>;

    testComponent.openModal(template);

    expect(mockModalService.show).toHaveBeenCalledWith(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-sm filter-popup custom-modal',
    });
  });

  it('should open delete modal', () => {
    const template = {} as TemplateRef<any>;
    const deleteData = { id: 1, reportName: 'Test Report' };

    testComponent.openDeleteModal(deleteData, template);

    expect(testComponent.deleteSchedulerReportData).toEqual(deleteData);
    expect(mockModalService.show).toHaveBeenCalledWith(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-delivery-popup custom-modal',
    });
  });

  it('should reset deleteSchedulerReportData before setting new data in openDeleteModal', () => {
    const template = {} as TemplateRef<any>;
    const deleteData = { id: 1, reportName: 'Test Report' };
    testComponent.deleteSchedulerReportData = { id: 999, oldData: 'old' };

    testComponent.openDeleteModal(deleteData, template);

    expect(testComponent.deleteSchedulerReportData).toEqual(deleteData);
  });

  it('should handle buildPayload with empty search text', () => {
    testComponent.ProjectId = '123';
    testComponent.getSearchText = undefined;

    const payload = testComponent.buildPayload();

    expect(payload.search).toBe('');
  });

  it('should handle buildPayload with null filter values', () => {
    testComponent.ProjectId = '123';
    testComponent.filterValues = null;

    const payload = testComponent.buildPayload();

    expect(payload.createdUserId).toBeUndefined();
    expect(payload.reportName).toBeUndefined();
    expect(payload.templateType).toBeUndefined();
    expect(payload.lastRun).toBeUndefined();
  });

  it('should handle buildPayload with undefined filter values', () => {
    testComponent.ProjectId = '123';
    testComponent.filterValues = undefined;

    const payload = testComponent.buildPayload();

    expect(payload.createdUserId).toBeUndefined();
    expect(payload.reportName).toBeUndefined();
    expect(payload.templateType).toBeUndefined();
    expect(payload.lastRun).toBeUndefined();
  });

  it('should handle resetAndClose method', () => {
    testComponent.modalRef = { hide: jest.fn() } as unknown as BsModalRef;
    testComponent.deleteSchedulerReportData = { id: 1, name: 'test' };

    testComponent.resetAndClose();

    expect(testComponent.modalRef.hide).toHaveBeenCalled();
    expect(testComponent.deleteSchedulerReportData).toEqual({});
  });

  it('should handle showError with complex error structure', () => {
    const error = {
      message: {
        details: [{
          field1: 'Error message 1',
          field2: 'Error message 2'
        }]
      }
    };

    testComponent.showError(error);

    expect(mockToastrService.error).toHaveBeenCalledWith(['Error message 1', 'Error message 2']);
  });

  it('should handle changePageNo method', () => {
    testComponent.ProjectId = '123';
    const mockResponse = {
      data: {
        scheduledReports: [{ id: 1, reportName: 'Test Report' }],
        count: 1
      }
    };
    mockReportsService.getScheduledReports.mockReturnValue(of(mockResponse));

    testComponent.changePageNo(3);

    expect(testComponent.pageNo).toBe(3);
    expect(mockReportsService.getScheduledReports).toHaveBeenCalled();
  });

  it('should handle changePageSize method', () => {
    testComponent.ProjectId = '123';
    const mockResponse = {
      data: {
        scheduledReports: [{ id: 1, reportName: 'Test Report' }],
        count: 1
      }
    };
    mockReportsService.getScheduledReports.mockReturnValue(of(mockResponse));

    testComponent.changePageSize(100);

    expect(testComponent.pageSize).toBe(100);
    expect(mockReportsService.getScheduledReports).toHaveBeenCalled();
  });

  it('should handle downloadScheduledReport with early return conditions', () => {
    const createElementSpy = jest.spyOn(document, 'createElement');

    // Test early return when data is null
    testComponent.downloadScheduledReport(null, 'lastRun');
    expect(createElementSpy).not.toHaveBeenCalled();

    // Test early return when action is lastRun but no lastRun and no s3_url
    testComponent.downloadScheduledReport({ lastRun: null, s3_url: null }, 'lastRun');
    expect(createElementSpy).not.toHaveBeenCalled();

    createElementSpy.mockRestore();
  });

  it('should handle autoDownloadReportWithRunOption with trimmed response data', () => {
    testComponent.ProjectId = '123';
    const data = { id: 1, runNowLoader: false };
    const mockResponse = { data: '  https://example.com/report.pdf  ' };
    mockReportsService.runNowScheduledOrSavedReport.mockReturnValue(of(mockResponse));
    const downloadSpy = jest.spyOn(testComponent, 'downloadScheduledReport').mockImplementation(() => {});

    testComponent.autoDownloadReportWithRunOption(data);

    expect(downloadSpy).toHaveBeenCalledWith(mockResponse.data, 'reRun');
    expect(data.runNowLoader).toBe(false);
  });

  it('should handle getScheduleReports with null response', fakeAsync(() => {
    testComponent.ProjectId = '123';
    mockReportsService.getScheduledReports.mockReturnValue(of(null));

    testComponent.getScheduleReports();
    tick();

    expect(testComponent.loader).toBe(false);
    expect(testComponent.scheduleReports).toEqual([]);
  }));

  it('should handle getScheduleReports with undefined response data', fakeAsync(() => {
    testComponent.ProjectId = '123';
    mockReportsService.getScheduledReports.mockReturnValue(of({ data: undefined }));

    testComponent.getScheduleReports();
    tick();

    expect(testComponent.loader).toBe(false);
  }));

  it('should handle deleteScheduleReport with null response', fakeAsync(() => {
    testComponent.ProjectId = '123';
    testComponent.deleteSchedulerReportData = { id: 1 };
    testComponent.modalRef = { hide: jest.fn() } as unknown as BsModalRef;
    mockReportsService.deleteScheduledReports.mockReturnValue(of(null));

    testComponent.deleteScheduleReport();
    tick();

    expect(testComponent.deleteSchedulerReportSubmitted).toBe(false);
  }));

  // Additional edge case tests for better coverage
  it('should handle downloadScheduledReport with undefined data', () => {
    const createElementSpy = jest.spyOn(document, 'createElement');

    testComponent.downloadScheduledReport(undefined, 'lastRun');

    expect(createElementSpy).not.toHaveBeenCalled();
    createElementSpy.mockRestore();
  });

  it('should handle downloadScheduledReport with false data', () => {
    const createElementSpy = jest.spyOn(document, 'createElement');

    testComponent.downloadScheduledReport(false, 'lastRun');

    expect(createElementSpy).not.toHaveBeenCalled();
    createElementSpy.mockRestore();
  });

  it('should handle downloadScheduledReport with empty object and lastRun action', () => {
    testComponent.downloadScheduledReport({}, 'lastRun');

    expect(mockToastrService.info).toHaveBeenCalledWith('There are no events available within the scheduled date range');
  });

  it('should handle downloadScheduledReport with data but different action', () => {
    const data = { s3_url: 'https://example.com/report.pdf' };

    testComponent.downloadScheduledReport(data, 'someOtherAction');

    expect(mockToastrService.info).toHaveBeenCalledWith('There are no events available within the scheduled date range');
  });

  it('should handle downloadScheduledReport with data.s3_url but not lastRun action', () => {
    const data = { s3_url: 'https://example.com/report.pdf' };
    const mockAnchor = {
      setAttribute: jest.fn(),
      click: jest.fn(),
      remove: jest.fn()
    };
    const createElementSpy = jest.spyOn(document, 'createElement').mockReturnValue(mockAnchor as unknown as HTMLElement);
    const appendChildSpy = jest.spyOn(document.body, 'appendChild').mockImplementation(() => null);

    testComponent.downloadScheduledReport(data, 'lastRun');

    expect(createElementSpy).toHaveBeenCalledWith('a');
    expect(mockAnchor.setAttribute).toHaveBeenCalledWith('target', '_self');
    expect(mockAnchor.setAttribute).toHaveBeenCalledWith('href', data.s3_url);
    expect(mockToastrService.success).toHaveBeenCalledWith('Report downloaded successfully');

    createElementSpy.mockRestore();
    appendChildSpy.mockRestore();
  });

  it('should handle autoDownloadReportWithRunOption with response data containing spaces', () => {
    testComponent.ProjectId = '123';
    const data = { id: 1, runNowLoader: false };
    const mockResponse = { data: '   https://example.com/report.xlsx   ' };
    mockReportsService.runNowScheduledOrSavedReport.mockReturnValue(of(mockResponse));
    const downloadSpy = jest.spyOn(testComponent, 'downloadScheduledReport').mockImplementation(() => {});

    testComponent.autoDownloadReportWithRunOption(data);

    expect(downloadSpy).toHaveBeenCalledWith(mockResponse.data, 'reRun');
    expect(data.runNowLoader).toBe(false);
  });

  it('should handle autoDownloadReportWithRunOption with CSV file URL', () => {
    testComponent.ProjectId = '123';
    const data = { id: 1, runNowLoader: false };
    const mockResponse = { data: 'https://example.com/report.csv' };
    mockReportsService.runNowScheduledOrSavedReport.mockReturnValue(of(mockResponse));
    const downloadSpy = jest.spyOn(testComponent, 'downloadScheduledReport').mockImplementation(() => {});

    testComponent.autoDownloadReportWithRunOption(data);

    expect(downloadSpy).toHaveBeenCalledWith(mockResponse.data, 'reRun');
    expect(data.runNowLoader).toBe(false);
  });

  it('should handle autoDownloadReportWithRunOption with invalid URL format', () => {
    testComponent.ProjectId = '123';
    const data = { id: 1, runNowLoader: false };
    const mockResponse = { data: 'not-a-valid-url' };
    mockReportsService.runNowScheduledOrSavedReport.mockReturnValue(of(mockResponse));

    testComponent.autoDownloadReportWithRunOption(data);

    expect(data.runNowLoader).toBe(false);
    expect(mockToastrService.error).toHaveBeenCalledWith('Something went wrong.');
  });

  it('should handle autoDownloadReportWithRunOption with URL without proper extension', () => {
    testComponent.ProjectId = '123';
    const data = { id: 1, runNowLoader: false };
    const mockResponse = { data: 'https://example.com/report' };
    mockReportsService.runNowScheduledOrSavedReport.mockReturnValue(of(mockResponse));

    testComponent.autoDownloadReportWithRunOption(data);

    expect(data.runNowLoader).toBe(false);
    expect(mockToastrService.error).toHaveBeenCalledWith('Something went wrong.');
  });

  it('should handle autoDownloadReportWithRunOption with null response', () => {
    testComponent.ProjectId = '123';
    const data = { id: 1, runNowLoader: false };
    mockReportsService.runNowScheduledOrSavedReport.mockReturnValue(of(null));

    testComponent.autoDownloadReportWithRunOption(data);

    expect(data.runNowLoader).toBe(false);
  });

  it('should handle autoDownloadReportWithRunOption with undefined response data', () => {
    testComponent.ProjectId = '123';
    const data = { id: 1, runNowLoader: false };
    mockReportsService.runNowScheduledOrSavedReport.mockReturnValue(of({ data: undefined }));

    testComponent.autoDownloadReportWithRunOption(data);

    expect(data.runNowLoader).toBe(false);
  });

  it('should handle getScheduleReports with response but no data property', fakeAsync(() => {
    testComponent.ProjectId = '123';
    mockReportsService.getScheduledReports.mockReturnValue(of({ someOtherProperty: 'value' }));

    testComponent.getScheduleReports();
    tick();

    expect(testComponent.loader).toBe(false);
  }));

  it('should handle getScheduleReports with response.data but no scheduledReports', fakeAsync(() => {
    testComponent.ProjectId = '123';
    mockReportsService.getScheduledReports.mockReturnValue(of({ data: { someOtherProperty: 'value' } }));

    testComponent.getScheduleReports();
    tick();

    expect(testComponent.loader).toBe(false);
  }));

  it('should handle buildPayload with filterValues having empty string values', () => {
    testComponent.ProjectId = '123';
    testComponent.filterValues = {
      createdUserId: '',
      reportName: '',
      templateType: '',
      lastRun: ''
    };

    const payload = testComponent.buildPayload();

    expect(payload.createdUserId).toBeUndefined();
    expect(payload.reportName).toBeUndefined();
    expect(payload.templateType).toBeUndefined();
    expect(payload.lastRun).toBeUndefined();
  });

  it('should handle buildPayload with filterValues having whitespace values', () => {
    testComponent.ProjectId = '123';
    testComponent.filterValues = {
      createdUserId: '   ',
      reportName: '   ',
      templateType: '   ',
      lastRun: null
    };

    const payload = testComponent.buildPayload();

    expect(payload.createdUserId).toBe('   ');
    expect(payload.reportName).toBe('   ');
    expect(payload.templateType).toBe('   ');
    expect(payload.lastRun).toBeUndefined();
  });

  it('should handle showError with single field error', () => {
    const error = {
      message: {
        details: [{
          singleField: 'Single error message'
        }]
      }
    };

    testComponent.showError(error);

    expect(mockToastrService.error).toHaveBeenCalledWith(['Single error message']);
  });

  it('should handle showError with empty details array', () => {
    const error = {
      message: {
        details: []
      }
    };

    expect(() => testComponent.showError(error)).toThrow();
  });

  it('should handle openDeleteModal with existing deleteSchedulerReportData', () => {
    const template = {} as TemplateRef<any>;
    const deleteData = { id: 1, reportName: 'Test Report' };
    testComponent.deleteSchedulerReportData = { id: 999, existingData: 'existing' };

    testComponent.openDeleteModal(deleteData, template);

    expect(testComponent.deleteSchedulerReportData).toEqual(deleteData);
    expect(mockModalService.show).toHaveBeenCalledWith(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-delivery-popup custom-modal',
    });
  });

  // Additional comprehensive tests for maximum coverage
  it('should handle downloadScheduledReport with data having lastRun but no s3_url', () => {
    const data = { lastRun: '2023-01-01', s3_url: null };

    testComponent.downloadScheduledReport(data, 'lastRun');

    expect(mockToastrService.info).toHaveBeenCalledWith('There are no events available within the scheduled date range');
  });

  it('should handle downloadScheduledReport with data having s3_url but no lastRun', () => {
    const data = { lastRun: null, s3_url: 'https://example.com/report.pdf' };
    const mockAnchor = {
      setAttribute: jest.fn(),
      click: jest.fn(),
      remove: jest.fn()
    };
    const createElementSpy = jest.spyOn(document, 'createElement').mockReturnValue(mockAnchor as unknown as HTMLElement);
    const appendChildSpy = jest.spyOn(document.body, 'appendChild').mockImplementation(() => null);

    testComponent.downloadScheduledReport(data, 'lastRun');

    expect(createElementSpy).toHaveBeenCalledWith('a');
    expect(mockAnchor.setAttribute).toHaveBeenCalledWith('target', '_self');
    expect(mockAnchor.setAttribute).toHaveBeenCalledWith('href', data.s3_url);
    expect(mockToastrService.success).toHaveBeenCalledWith('Report downloaded successfully');

    createElementSpy.mockRestore();
    appendChildSpy.mockRestore();
  });

  it('should handle downloadScheduledReport with reRun action and string data', () => {
    const data = 'https://example.com/report.pdf';
    const mockAnchor = {
      setAttribute: jest.fn(),
      click: jest.fn(),
      remove: jest.fn()
    };
    const createElementSpy = jest.spyOn(document, 'createElement').mockReturnValue(mockAnchor as unknown as HTMLElement);
    const appendChildSpy = jest.spyOn(document.body, 'appendChild').mockImplementation(() => null);

    testComponent.downloadScheduledReport(data, 'reRun');

    expect(createElementSpy).toHaveBeenCalledWith('a');
    expect(mockAnchor.setAttribute).toHaveBeenCalledWith('target', '_self');
    expect(mockAnchor.setAttribute).toHaveBeenCalledWith('href', data);
    expect(mockToastrService.success).toHaveBeenCalledWith('Report downloaded successfully');

    createElementSpy.mockRestore();
    appendChildSpy.mockRestore();
  });

  it('should handle autoDownloadReportWithRunOption with response data exactly "No data found"', () => {
    testComponent.ProjectId = '123';
    const data = { id: 1, runNowLoader: false };
    const mockResponse = { data: 'No data found' };
    mockReportsService.runNowScheduledOrSavedReport.mockReturnValue(of(mockResponse));

    testComponent.autoDownloadReportWithRunOption(data);

    expect(data.runNowLoader).toBe(false);
    expect(mockToastrService.info).toHaveBeenCalledWith('There are no events available within the scheduled date range');
  });

  it('should handle autoDownloadReportWithRunOption with valid XLSX URL', () => {
    testComponent.ProjectId = '123';
    const data = { id: 1, runNowLoader: false };
    const mockResponse = { data: 'https://example.com/report.xlsx' };
    mockReportsService.runNowScheduledOrSavedReport.mockReturnValue(of(mockResponse));
    const downloadSpy = jest.spyOn(testComponent, 'downloadScheduledReport').mockImplementation(() => {});

    testComponent.autoDownloadReportWithRunOption(data);

    expect(downloadSpy).toHaveBeenCalledWith(mockResponse.data, 'reRun');
    expect(data.runNowLoader).toBe(false);
  });

  it('should handle autoDownloadReportWithRunOption with URL that fails regex test', () => {
    testComponent.ProjectId = '123';
    const data = { id: 1, runNowLoader: false };
    const mockResponse = { data: 'http://example.com/report.pdf' }; // http instead of https
    mockReportsService.runNowScheduledOrSavedReport.mockReturnValue(of(mockResponse));

    testComponent.autoDownloadReportWithRunOption(data);

    expect(data.runNowLoader).toBe(false);
    expect(mockToastrService.error).toHaveBeenCalledWith('Something went wrong.');
  });

  it('should handle autoDownloadReportWithRunOption with URL having extra characters', () => {
    testComponent.ProjectId = '123';
    const data = { id: 1, runNowLoader: false };
    const mockResponse = { data: 'https://example.com/report.pdf extra' };
    mockReportsService.runNowScheduledOrSavedReport.mockReturnValue(of(mockResponse));

    testComponent.autoDownloadReportWithRunOption(data);

    expect(data.runNowLoader).toBe(false);
    expect(mockToastrService.error).toHaveBeenCalledWith('Something went wrong.');
  });

  it('should handle getScheduleReports with empty response data', fakeAsync(() => {
    testComponent.ProjectId = '123';
    mockReportsService.getScheduledReports.mockReturnValue(of({ data: {} }));

    testComponent.getScheduleReports();
    tick();

    expect(testComponent.loader).toBe(false);
  }));

  it('should handle getScheduleReports with response.data.scheduledReports as null', fakeAsync(() => {
    testComponent.ProjectId = '123';
    mockReportsService.getScheduledReports.mockReturnValue(of({ data: { scheduledReports: null, count: 0 } }));

    testComponent.getScheduleReports();
    tick();

    expect(testComponent.scheduleReports).toBe(null);
    expect(testComponent.count).toBe(0);
    expect(testComponent.loader).toBe(false);
  }));

  it('should handle getScheduleReports with response.data.count as null', fakeAsync(() => {
    testComponent.ProjectId = '123';
    mockReportsService.getScheduledReports.mockReturnValue(of({ data: { scheduledReports: [], count: null } }));

    testComponent.getScheduleReports();
    tick();

    expect(testComponent.scheduleReports).toEqual([]);
    expect(testComponent.count).toBe(null);
    expect(testComponent.loader).toBe(false);
  }));

  it('should handle deleteScheduleReport with response but no message', fakeAsync(() => {
    testComponent.ProjectId = '123';
    testComponent.deleteSchedulerReportData = { id: 1 };
    testComponent.modalRef = { hide: jest.fn() } as unknown as BsModalRef;
    mockReportsService.deleteScheduledReports.mockReturnValue(of({ someOtherProperty: 'value' }));
    mockReportsService.getScheduledReports.mockReturnValue(of({ data: { scheduledReports: [], count: 0 } }));

    testComponent.deleteScheduleReport();
    tick();

    expect(testComponent.deleteSchedulerReportData).toEqual({});
    expect(testComponent.deleteSchedulerReportSubmitted).toBe(false);
    expect(testComponent.modalRef.hide).toHaveBeenCalled();
  }));

  it('should handle buildPayload with moment date formatting', () => {
    testComponent.ProjectId = '123';
    const testDate = new Date('2023-12-25');
    testComponent.filterValues = {
      lastRun: testDate
    };

    const payload = testComponent.buildPayload();

    expect(payload.lastRun).toBe('2023-12-25');
    expect(payload.timezone).toBe(Intl.DateTimeFormat().resolvedOptions().timeZone);
  });

  it('should handle buildPayload with all filter values as truthy', () => {
    testComponent.ProjectId = '123';
    testComponent.filterValues = {
      createdUserId: 'user123',
      reportName: 'Test Report',
      templateType: 'PDF',
      lastRun: new Date('2023-01-01')
    };

    const payload = testComponent.buildPayload();

    expect(payload.createdUserId).toBe('user123');
    expect(payload.reportName).toBe('Test Report');
    expect(payload.templateType).toBe('PDF');
    expect(payload.lastRun).toBe('2023-01-01');
    expect(payload.timezone).toBe(Intl.DateTimeFormat().resolvedOptions().timeZone);
  });

  it('should handle showError with multiple fields in error details', () => {
    const error = {
      message: {
        details: [{
          field1: 'Error 1',
          field2: 'Error 2',
          field3: 'Error 3'
        }]
      }
    };

    testComponent.showError(error);

    expect(mockToastrService.error).toHaveBeenCalledWith(['Error 1', 'Error 2', 'Error 3']);
  });

  it('should handle openModal with template', () => {
    const template = {} as TemplateRef<any>;

    testComponent.openModal(template);

    expect(mockModalService.show).toHaveBeenCalledWith(template, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-sm filter-popup custom-modal',
    });
  });

  it('should handle sortByField with different parameters', () => {
    testComponent.ProjectId = '123';
    const mockResponse = {
      data: {
        scheduledReports: [{ id: 1, reportName: 'Test Report' }],
        count: 1
      }
    };
    mockReportsService.getScheduledReports.mockReturnValue(of(mockResponse));

    testComponent.sortByField('createdDate', 'DESC');

    expect(testComponent.sortColumn).toBe('createdDate');
    expect(testComponent.sort).toBe('DESC');
    expect(mockReportsService.getScheduledReports).toHaveBeenCalled();
  });

  it('should handle handleToggleKeydown with different key combinations', () => {
    const event1 = { key: 'Enter', preventDefault: jest.fn() } as unknown as KeyboardEvent;
    const event2 = { key: ' ', preventDefault: jest.fn() } as unknown as KeyboardEvent;
    const event3 = { key: 'Escape', preventDefault: jest.fn() } as unknown as KeyboardEvent;
    const sortByFieldSpy = jest.spyOn(testComponent, 'sortByField').mockImplementation(() => {});

    // Test Enter key
    testComponent.handleToggleKeydown(event1, 'reportName', 'ASC');
    expect(event1.preventDefault).toHaveBeenCalled();
    expect(sortByFieldSpy).toHaveBeenCalledWith('reportName', 'ASC');

    // Test Space key
    testComponent.handleToggleKeydown(event2, 'reportName', 'DESC');
    expect(event2.preventDefault).toHaveBeenCalled();
    expect(sortByFieldSpy).toHaveBeenCalledWith('reportName', 'DESC');

    // Test other key (should not trigger)
    testComponent.handleToggleKeydown(event3, 'reportName', 'ASC');
    expect(event3.preventDefault).not.toHaveBeenCalled();
  });

  it('should handle handleDownKeydown with all action types', () => {
    const event = { key: 'Enter', preventDefault: jest.fn() } as unknown as KeyboardEvent;
    const data = { id: 1, reportName: 'Test Report' };
    const template = {} as TemplateRef<any>;

    const downloadSpy = jest.spyOn(testComponent, 'downloadScheduledReport').mockImplementation(() => {});
    const openDeleteModalSpy = jest.spyOn(testComponent, 'openDeleteModal').mockImplementation(() => {});
    const autoDownloadSpy = jest.spyOn(testComponent, 'autoDownloadReportWithRunOption').mockImplementation(() => {});

    // Test download action
    testComponent.handleDownKeydown(event, data, 'lastRun', 'download');
    expect(downloadSpy).toHaveBeenCalledWith(data, 'lastRun');

    // Test delete action
    testComponent.handleDownKeydown(event, data, template, 'delete');
    expect(openDeleteModalSpy).toHaveBeenCalledWith(data, template);

    // Test auto action
    testComponent.handleDownKeydown(event, data, 'item', 'auto');
    expect(autoDownloadSpy).toHaveBeenCalledWith(data);

    // Test default case
    testComponent.handleDownKeydown(event, data, 'item', 'unknown');
    // Should not call any specific method for unknown action
  });

});
