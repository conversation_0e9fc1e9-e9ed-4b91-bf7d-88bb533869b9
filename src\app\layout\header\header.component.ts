import { Component, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth/auth.service';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  })
export class HeaderComponent {
  public navbarOpen = false;

  public loginToken = false;

  public shouldShow: boolean;

  public mobilenavshow: boolean;


  @Output() private readonly sidemenuToggle = new EventEmitter<boolean>();

  public constructor(public router: Router, private readonly authService: AuthService) {
    const url = this.router.url.split('/');
    const memberId = url[2];
    const ParentCompanyId = url[3];
    if (memberId && ParentCompanyId) {
      this.authService.contentLogout();
      this.loginToken = false;
    } else if (this.authService.loggeduserIn()) {
      this.loginToken = true;
    }
  }

  public toggleNavbar(): void {
    this.navbarOpen = !this.navbarOpen;
  }

  public sidebarToggle(): void {
    this.shouldShow = !this.shouldShow;
    this.sidemenuToggle.emit(this.shouldShow);
  }

  public redirect(path): void {
    this.router.navigate([`/${path}`]);
  }



  public mobilenNavOpen(): void {
    this.mobilenavshow = !this.mobilenavshow;
  }
}
