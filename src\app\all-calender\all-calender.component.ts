/* eslint-disable no-lonely-if */
/* eslint-disable max-lines-per-function */
/* eslint-disable no-underscore-dangle */
import { Component, TemplateRef, ViewChild, OnInit, OnDestroy } from '@angular/core';
import { FullCalendarComponent } from '@fullcalendar/angular'; // useful for typechecking
import { Calendar, CalendarOptions } from '@fullcalendar/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { FormBuilder, FormGroup , Validators} from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { Socket } from 'ngx-socket-io';
import { Router, ActivatedRoute } from '@angular/router';
import moment from 'moment';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { Title } from '@angular/platform-browser';
import { CalendarService } from '../services/profile/calendar.service';
import { ProjectService } from '../services/profile/project.service';
import { DeliveryService } from '../services/profile/delivery.service';
import { NewinspectionFormComponent } from '../inspection-request/inspection_details/new-inspection-form/new-inspection-form.component';
import { EditinspectionFormComponent } from '../inspection-request/inspection_details/edit-inspection-form/edit-inspection-form.component';
import { InspectionDetailsNewComponent } from '../inspection-request/inspection_details/inspection-details-new/inspection-details-new.component';
import { MixpanelService } from '../services/mixpanel.service';
import { Subscription, take } from 'rxjs';
import { DeliveryDetailsNewComponent } from '../delivery-requests/delivery-details/delivery-details-new/delivery-details-new.component';
import { CraneRequestDetailViewHeaderComponent } from '../crane-requests/crane-request-detail-view-header/crane-request-detail-view-header.component';
import { ConcreteDetailHeaderComponent } from '../concrete-request/concrete-detail-header/concrete-detail-header.component';
import { NewDeliveryFormComponent } from '../delivery-requests/delivery-details/new-delivery-form/new-delivery-form.component';
import { NewCraneRequestCreationFormComponent } from '../crane-requests/new-crane-request-creation-form/new-crane-request-creation-form.component';
import { AddConcreteRequestComponent } from '../concrete-request/add-concrete-request/add-concrete-request.component';
import { inspectionType } from '../services/common';

@Component({
  selector: 'app-all-calendar',
  templateUrl: './all-calender.component.html',
})
export class AllCalendarComponent implements OnInit, OnDestroy {
  public modalRef: BsModalRef;

  public modalRef1: BsModalRef;

  public modalRef2: BsModalRef;

  public descriptionPopup = false;

  public events: any = [];

  public calendar: Calendar;

  public calendarApi: any;

  public ProjectId: any;

  public Range: any = {};

  public loader = true;

  public deliveryList: any = [];

  public concreteRequest: any[];

  public search = '';

  public lastId: any = 0;

  public dropdownSettings: IDropdownSettings;

  public eventData: any = {};

  public calendarCurrentDeliveryIndex = -1;

  public showStatus = false;

  public authUser: any = {};

  public filterForm: FormGroup;

  public filterCount = 0;

  public definableDropdownSettings: IDropdownSettings;

  public statusValue: any = [];

  public isSubFilterVisible = true;

  public showHideText : any = 'Hide';

  public showClear: boolean ;

  public currentStatus = '';

  public currentEditItem: any = {};

  public modalLoader = false;

  public companyList: any = [];

  public defineList: any = [];

  public voidSubmitted = false;

  public gateList: any = [];

  public equipmentList: any = [];

  public submitted = false;

  public formSubmitted = false;

  public memberList: any = [];

  public statusSubmitted = false;

  public deliveryId;

  public currentDeliverySaveItem: any = {};

  public ParentCompanyId;

  public showSearchbar = false;

  public calendarDescriptionPopup = false;

  public viewEventData: any;

  public message = '';

  public toolTipContent = '';

  public wholeStatus = ['Approved', 'Declined', 'Delivered', 'Pending','Tentative','Completed'];

  public currentViewMonth: moment.MomentInput;

  public deliveryRequestWithCraneEquipmentType: any[];

  public currentView = 'Month';

  public subscription: any;

  public subscription1: any;

  public monthlyEventloader = true;

  public monthEvents = [];

  public allRequestIsOpened = false;

  public seriesOptions = [];

  public approved: string;

  public pending: string;

  public rejected: string;

  public delivered: string;

  public expired: string;

  public locationList: any = [];

  public type: any;

  public isFilterPopupVisible: boolean = false;

  public isSubFilterPopupVisible: boolean = false;

  public inspectionTypeList: any = inspectionType;

  public currentFilter: string = '';

  public currentFilterOptions: any;

  public appliedFilters: any[] = [];

  public selectedSubFilterOptions: any[] = [];

  public isMainFilterVisible: boolean = true;

  public activeSubFilter: any;

  public selectedFilter: string[] = [];

  public filterSearch: string = '';

  public filterOptions = [
    'Description',
    'Date',
    'Company',
    'Responsible Person',
    'Gate',
    'Equipment',
    'Location',
    'Status',
    'Pick From',
    'Pick To',
    'pumpConfirmed',
    'ConcreteConfirmed',
    'Order Number',
    'Inspection Type',
    'Inspection Status'
  ];

  public noEquipmentOption = { id: 0, equipmentName: 'No Equipment Needed' };

  public filteredFilterOptions: string[] = [...this.filterOptions];

  @ViewChild('fullcalendar', { static: true }) public calendarComponent1: FullCalendarComponent;

  public calendarOptions: CalendarOptions = {
    selectable: true,
    initialView: 'dayGridMonth',
    plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin],
    aspectRatio: 2,
    slotEventOverlap: false,
    contentHeight: 'liquid',
    fixedWeekCount: false,
    expandRows: true,
    nowIndicator: true,
    moreLinkClick: 'popover',
    events: this.events,
    customButtons: {
      prev: {
        text: 'PREV',
        click: (): void => this.goPrev(),
      },
      next: {
        text: 'Next',
        click: (): void => this.goNext(),
      },
      prevYear: {
        text: 'PREV',
        click: (): void => this.goPrevYear(),
      },
      nextYear: {
        text: 'Next',
        click: (): void => this.goNextYear(),
      },
      timeGridWeek: {
        text: 'Week',
        click: (): void => this.goTimeGridWeekOrDay('timeGridWeek'),
      },
      timeGridDay: {
        text: 'Day',
        click: (): void => this.goTimeGridWeekOrDay('timeGridDay'),
      },
      dayGridMonth: {
        text: 'Month',
        click: (): void => this.goDayGridMonth(),
      },
    },
    showNonCurrentDates: false,
    headerToolbar: {
      right: '',
      center: 'prevYear,prev,title,next,nextYear',
      left: 'dayGridMonth,timeGridWeek,timeGridDay',
    },
    firstDay: 0,
    eventClick: (arg): void => {
      this.eventData = [];
      this.deliveryDescription(arg);
    },
    datesSet: (info): void => {
      this.currentViewMonth = info.view.title;
    },
    eventTimeFormat: {
      hour: 'numeric',
      minute: '2-digit',
      meridiem: 'short',
    },
    eventDidMount(info): void {
      const argument = info;
      const line1 = argument.event._def.extendedProps.description;
      const { line2 } = argument.event._def.extendedProps;
      const eventTitleElement: HTMLElement = argument.el.querySelector('.fc-event-title');
      eventTitleElement.style.backgroundColor = info.backgroundColor;
      eventTitleElement.style.color = info.textColor;
      eventTitleElement.style.borderLeft = `4px solid ${info.borderColor}`;
      const requestType = argument.event._def.extendedProps.requestType
      let imgURL = ''
      if (requestType == 'inspectionRequest' || requestType == 'inspectionRequestWithCrane') {
        imgURL = './assets/images/sidemenu-icons/inspection.svg'
      } else if (requestType == 'craneRequest') {
        imgURL = './assets/images/sidemenu-icons/equipments-active.svg'
      } else if (requestType == 'concreteRequest') {
        imgURL = './assets/images/sidemenu-icons/concrete-active.svg'
      } else {
        imgURL = './assets/images/sidemenu-icons/calendar-active.svg'
      }
      if (argument.event._def.allDay) {
        eventTitleElement.innerHTML = `
        <div class="d-flex flex-column">
        <p class="m-0"> <img class="w10 h10" src="./assets/images/noun-event-alert.svg" class="form-icon" alt="allday" >  ${line1} </p>
        <small> ${line2} </small>
        </div>`;
      } else {
        // eslint-disable-next-line no-param-reassign
        eventTitleElement.innerHTML = `
          <div class="d-flex flex-column">
          <p class="m-0"> <img *ngIf="imgURL" src="${imgURL}" class="active-img" /> ${line1} </p>
          <small class="text-wrap"> ${line2} </small>
          </div>`;
      }
    },
    dayMaxEventRows: true, // for all non-TimeGrid views
    views: {
      timeGrid: {
        dayMaxEventRows: 2, // adjust to 6 only for timeGridWeek/timeGridDay
      },
      dayGridMonth: {
        allDaySlot: true,
      },
      timeGridWeek: {
        allDaySlot: true,
      },
      timeGridDay: {
        allDaySlot: true,
      },
    },
    dateClick: (info): void => {
      this.openAddRequestModal(info);
    },
  };

  private queryParamSubscription: Subscription;

  private dataChangeSubscription: Subscription;

  public constructor(
    public bsModalRef: BsModalRef,
    private readonly modalService: BsModalService,
    public calendarService: CalendarService,
    public projectService: ProjectService,
    private readonly formBuilder: FormBuilder,
    public deliveryService: DeliveryService,
    private readonly toastr: ToastrService,
    public router: Router,
    public socket: Socket,
    private readonly mixpanelService: MixpanelService,
    private readonly titleService: Title,
    private readonly route: ActivatedRoute
  ) {
    this.titleService.setTitle('Follo - All Calendar');
    this.filterDetailsForm();
    this.deliveryService.refresh.subscribe((getEventNdrResponse): void => {
      if (
        getEventNdrResponse !== undefined &&
        getEventNdrResponse !== null &&
        getEventNdrResponse !== ''
      ) {
        this.getEventNDR();
      }
    });
    this.deliveryService.refresh1.subscribe((getEventNdrResponse): void => {
      if (
        getEventNdrResponse !== undefined
        && getEventNdrResponse !== null
        && getEventNdrResponse !== ''
      ) {
        this.getEventNDR();
      }
    });
    this.deliveryService.inspectionUpdated.subscribe((getEventNdrResponse): void => {
      if (
        getEventNdrResponse !== undefined &&
        getEventNdrResponse !== null &&
        getEventNdrResponse !== ''
      ) {
        this.getEventNDR();
      }
    });
    this.deliveryService.inspectionUpdated1.subscribe((getEventNdrResponse): void => {
      if (
        getEventNdrResponse !== undefined &&
        getEventNdrResponse !== null &&
        getEventNdrResponse !== ''
      ) {
        this.getEventNDR();
      }
    });
    this.deliveryService.fetchConcreteData.subscribe((getEventNDR): void => {
      if (getEventNDR !== undefined && getEventNDR !== null && getEventNDR !== '') {
        this.getEventNDR();
      }
    });
    this.deliveryService.fetchConcreteData1.subscribe((getEventNDR): void => {
      if (getEventNDR !== undefined && getEventNDR !== null && getEventNDR !== '') {
        this.getEventNDR();
      }
    });
    this.deliveryService.fetchData.subscribe((getEventNDR): void => {
      if (getEventNDR !== undefined && getEventNDR !== null && getEventNDR !== '') {
        this.getEventNDR();
      }
    });
    this.deliveryService.fetchData1.subscribe((getEventNDR): void => {
      if (getEventNDR !== undefined && getEventNDR !== null && getEventNDR !== '') {
        this.getEventNDR();
      }
    });
    this.deliveryService.getCurrentStatus.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.deliveryId = res;
        this.getEventNDR();
      }
    });
  }

  ngOnInit(): void {
    this.queryParamSubscription = this.route.queryParams.subscribe(params => {
      const type = params['type'];
      // Handle the type parameter change
      this.type = type;
    });
    this.dataChangeSubscription = this.deliveryService.dataChanged$.subscribe((changed) => {
      if (changed) {
        this.deliveryService.AllCalendarRespData$.subscribe((data) => {
          this.setEventNDRData(data);
        })
      }
    });
  }

  ngOnDestroy() {
    if (this.queryParamSubscription) {
      this.queryParamSubscription.unsubscribe();
    }
    if (this.dataChangeSubscription) {
      this.dataChangeSubscription.unsubscribe();
    }
  }

  public openFilterModalCustom() {
    this.calendarGetOverAllGate();
    this.isFilterPopupVisible = true;
  }

  public isOptionSelected(filterName: string, option: string): boolean {
    if (option === 'DescriptionValue') {
      option = this.filterForm.value.descriptionFilter

    }
    const filter = this.selectedSubFilterOptions.find(f => f.name === filterName);
    return filter ? filter.selected.includes(option) : false;
  }

  public isSubOptionSelected(filterName: string, option: string , selected: any): boolean {
    this.activeSubFilter = filterName
    return selected.includes(option);
  }

  public oncloseSubfilter(){
    this.isSubFilterPopupVisible = false
    this.isFilterPopupVisible = false;
    this.modalRef.hide()
  }

  public removeDescriptionFilter(filterType): void {
    const descriptionFilterIndex = this.selectedSubFilterOptions.findIndex(
      filter => filter.name === filterType
    );
    let filterFormValue : any;
    if(filterType === 'Description'){
      filterFormValue = 'descriptionFilter'
    } else if(filterType === 'Date'){
      filterFormValue = 'dateFilter'
    } else if(filterType === 'Pick From'){
      filterFormValue = 'pickFrom'
    } else if(filterType === 'Pick To'){
      filterFormValue = 'pickTo'
    }

    if (descriptionFilterIndex !== -1) {
      this.selectedSubFilterOptions[descriptionFilterIndex].selected = [];

      this.selectedSubFilterOptions.splice(descriptionFilterIndex, 1);
    }

    this.filterForm.get(filterFormValue).setValue('');

    this.filterAllCalendarData();
  }

  public onSubFilterOptionChange(option: string, event: any) {
    this.submitted = true;
    const isChecked = event.target.checked;
    const filterIndex = this.selectedSubFilterOptions.findIndex(
      (filter) => filter.name === this.currentFilter,
    );

    if (isChecked) {
      this.showClear = true;
      if (filterIndex !== -1) {
        this.selectedSubFilterOptions[filterIndex].selected.push(option);
      } else {
        this.selectedSubFilterOptions.push({
          name: this.currentFilter,
          selected: [option],
        });
      }
    } else {
      if (filterIndex !== -1) {
        this.selectedSubFilterOptions[filterIndex].selected =
        this.selectedSubFilterOptions[filterIndex].selected.filter(
          (selectedOption) => selectedOption !== option,
        );

        if (this.selectedSubFilterOptions[filterIndex].selected.length === 0) {
          this.selectedSubFilterOptions.splice(filterIndex, 1);
        }
      }
    }

    this.handleSpecialOptions(option, filterIndex);
  }

  public handleSpecialOptions(option: string, filterIndex: number) {
    const formValues = this.filterForm.value;

    const optionValueMap: { [key: string]: any } = {
      DescriptionValue: formValues.descriptionFilter,
      DateValue: formValues.dateFilter ? new Date(formValues.dateFilter) : '',
      PickFromValue: formValues.pickFrom,
      PickToValue: formValues.pickTo,
      OrderNumberValue: formValues.orderNumberFilter,
    };

    const value = optionValueMap[option];
    if (value !== undefined && value !== '') {
      if (filterIndex !== -1) {
        const { selected } = this.selectedSubFilterOptions[filterIndex];
        const alreadyIncluded = option === 'DateValue'
          ? selected.some((date) => date.getTime?.() === value.getTime?.())
          : selected.includes(value);

        if (!alreadyIncluded) {
          selected.push(value);
        }
      } else {
        this.selectedSubFilterOptions.push({
          name: this.currentFilter,
          selected: [value],
        });
      }
    }
    this.filterAllCalendarData();
  }


  public changeFilterOptionList(event) {
    this.filteredFilterOptions = this.filterOptions.filter(option =>
      option.toLowerCase().includes(event.target.value.toLowerCase())
    );
  }

  public onClearSubfilter(filter) {
    this.selectedSubFilterOptions = this.selectedSubFilterOptions.filter(
      item => item.name !== filter.name);
    this.filterAllCalendarData()
  }


  public filterAllCalendarData() {
    this.deliveryService.AllCalendarRespData$.subscribe((data) => {
      const deepCopiedData = JSON.parse(JSON.stringify(data));
      const filteredData = this.applyFilters(deepCopiedData, this.selectedSubFilterOptions);

      if (this.selectedSubFilterOptions.length !== 0) {
        this.showClear = true;
      }

      this.updateCalendarUI(filteredData);
    });
  }

  public applyFilters(data: any, filters: any[]): any {
    let delivery = data.deliveryData.data;
    let inspection = data.inspectionData.data;
    let crane = data.craneData.data;
    let concrete = data.concreteData.data;

    const timezoneOffset = new Date().getTimezoneOffset();

    filters.forEach(({ name, selected }) => {
      const matchIncludes = (itemVal: any) => selected.includes(itemVal);

      const descriptionMatches = (item, selectedData) => selectedData.some(
        (val) => item.description?.toLowerCase().includes(val.toLowerCase()),
      );

      // Filters a group by description
      const filterByDescription = (group, selectedData) => group.filter(
        (item) => descriptionMatches(item, selectedData),
      );

      switch (name) {
        case 'Description': {
          const groups = [delivery, inspection, crane, concrete];
          const filteredGroups = groups.map((group) => filterByDescription(group, selected));
          [delivery, inspection, crane, concrete] = filteredGroups;
          break;
        }

        case 'Company':
          [delivery, inspection, crane] = [delivery, inspection, crane].map(
            (group) => group.filter(
              (item) => matchIncludes(
                item.companyDetails[0]?.Company.companyName,
              ),
            ),
          );
          break;

        case 'Location':
          [delivery, inspection, crane, concrete] = [delivery, inspection, crane, concrete].map(
            (group) => group.filter((item) => matchIncludes(item.location.locationPath)),
          );
          break;

        case 'Responsible Person':
          [delivery, inspection, crane, concrete] = [delivery, inspection, crane, concrete].map(
            (group) => group.filter(
              (item) => matchIncludes(item.memberDetails[0].Member.User.email),
            ),
          );
          break;

        case 'Equipment':
          [delivery, inspection, crane] = [delivery, inspection, crane].map(
            (group) => group.filter(
              (item) => matchIncludes(item.equipmentDetails[0].Equipment.equipmentName || 'No Equipment Needed'),
            ),
          );
          break;

        case 'Gate':
          [delivery, inspection] = [delivery, inspection].map(
            (group) => group.filter(
              (item) => matchIncludes(item.gateDetails[0].Gate.gateName),
            ),
          );
          break;

        case 'Status':
          [delivery, inspection, crane, concrete] = [delivery, inspection, crane, concrete].map(
            (group) => group.filter((item) => matchIncludes(item.status)),
          );
          break;

        case 'pumpConfirmed':
          concrete = concrete.filter((item) => matchIncludes(item.isPumpConfirmed.toString()));
          break;

        case 'ConcreteConfirmed':
          concrete = concrete.filter((item) => matchIncludes(item.isConcreteConfirmed.toString()));
          break;

        case 'Order Number':
          concrete = concrete.filter((item) => matchIncludes(item.concreteOrderNumber));
          break;

        case 'Inspection Type':
          [delivery, inspection, crane, concrete] = [delivery, inspection, crane, concrete].map(
            (group) => group.filter((item) => matchIncludes(item.inspectionType)),
          );
          break;

        case 'Inspection Status':
          [delivery, inspection, crane, concrete] = [delivery, inspection, crane, concrete].map(
            (group) => group.filter((item) => matchIncludes(item.inspectionStatus)),
          );
          break;

        case 'Date': {
          let [del, ins, cra, con] = [[], [], [], []];
          selected.forEach((dateStr) => {
            const start = moment(dateStr, 'ddd MMM DD YYYY HH:mm:ss [GMT]ZZ').startOf('day').utcOffset(-timezoneOffset, true);
            const end = moment(dateStr, 'ddd MMM DD YYYY HH:mm:ss [GMT]ZZ').endOf('day').utcOffset(-timezoneOffset, true);

            del = [...del, ...delivery.filter((item) => moment(item.deliveryStart).utcOffset(-timezoneOffset, true).isBetween(start, end, null, '[]'))];
            ins = [...ins, ...inspection.filter((item) => moment(item.inspectionStart).utcOffset(-timezoneOffset, true).isBetween(start, end, null, '[]'))];
            cra = [...cra, ...crane.filter((item) => moment(item.craneDeliveryStart).utcOffset(-timezoneOffset, true).isBetween(start, end, null, '[]'))];
            con = [...con, ...concrete.filter((item) => moment(item.concretePlacementStart).utcOffset(-timezoneOffset, true).isBetween(start, end, null, '[]'))];
          });
          [delivery, inspection, crane, concrete] = [del, ins, cra, con];
          break;
        }

        default:
          break;
      }
    });

    return {
      ...data,
      deliveryData: {
        ...data.deliveryData,
        data: delivery,
      },
      inspectionData: {
        ...data.inspectionData,
        data: inspection,
      },
      craneData: {
        ...data.craneData,
        data: crane,
      },
      concreteData: {
        ...data.concreteData,
        data: concrete,
      },
    };
  }

  public updateCalendarUI(allCalendarData) {
    console.log('Filtered Calendar Data:', allCalendarData);
    this.setEventNDRData(allCalendarData);
  }


  public toggleSubFilter(filter: any) {
    if (this.activeSubFilter === filter.name) {
      this.activeSubFilter = null;
    } else {
      this.activeSubFilter = filter.name;
    }
    if (filter.name === 'Description' || filter.name === 'Date' || filter.name === 'Pick From' || filter.name === 'Pick To') {
      this.currentFilter = filter.name;
      this.currentFilterOptions = filter.selected
    } else {
      this.getFilterOptions(filter.name)
    }
  }

  public getFilterOptions(filter: string) {
    this.isFilterPopupVisible = false;
    this.currentFilter = filter;

    switch (filter) {
      case 'Company':
        this.currentFilterOptions = this.companyList.map((item) => item.companyName);
        break;

      case 'Responsible Person':
        this.currentFilterOptions = this.memberList
          .filter((item) => ['pending', 'completed'].includes(item.status))
          .map((item) => item.User?.email)
          .filter((email) => email !== null);
        break;

      case 'Gate':
        this.currentFilterOptions = this.gateList.map((item) => item.gateName);
        break;

      case 'Equipment':
        this.currentFilterOptions = this.equipmentList.map((item) => item.equipmentName);
        break;

      case 'Inspection Type':
        this.currentFilterOptions = this.inspectionTypeList.map((item) => item);
        break;

      case 'Inspection Status':
        this.currentFilterOptions = ['Pass', 'Fail'];
        break;

      case 'Location':
        this.currentFilterOptions = this.locationList.map((item) => item.locationPath);
        break;

      case 'Status':
        this.currentFilterOptions = this.wholeStatus;
        break;

      case 'pumpConfirmed':
        this.currentFilterOptions = ['True', 'False'];
        break;

      case 'ConcreteConfirmed':
        this.currentFilterOptions = ['True', 'False'];
        break;

      default:
        break;
    }
  }

  public openSubFilter(filter: string) {
    this.isMainFilterVisible = false;
    this.currentFilter = filter;
    this.getFilterOptions(filter);
    this.isSubFilterPopupVisible = true;
  }

  getDescriptions(): string[] {
    // Logic to return an array of description options
    return ['Description 1', 'Description 2', 'Description 3'];
  }

  getDates(): string[] {
    // Logic to return an array of dates or date ranges
    return ['2024-08-01', '2024-08-02', '2024-08-03'];
  }

  getPickFromOptions(): string[] {
    // Logic to return options for 'Pick From'
    return ['Location A', 'Location B', 'Location C'];
  }

  getPickToOptions(): string[] {
    // Logic to return options for 'Pick To'
    return ['Location X', 'Location Y', 'Location Z'];
  }

  public closeSubFilter() {
    this.filteredFilterOptions = [...this.filterOptions];
    this.isMainFilterVisible = true; // Show the main filter
    this.isSubFilterPopupVisible = false;
    this.currentFilterOptions = [];
  }


  // Handle filter selection
  public onFilterSelect(selected: string[]) {
    this.selectedFilter = selected;
  }

  public toggleSelection(item: string) {
    const filter = this.appliedFilters.find(f => f.name === this.currentFilter);
    if (filter) {
      const index = filter.selected.indexOf(item);
      if (index === -1) {
        filter.selected.push(item);
      } else {
        filter.selected.splice(index, 1);
      }
    } else {
      this.appliedFilters.push({ name: this.currentFilter, selected: [item] });
    }
  }

  public isSelected(item: string): boolean {
    const filter = this.appliedFilters.find(f => f.name === this.currentFilter);
    return filter ? filter.selected.includes(item) : false;
  }

  public removeFilter(filterToRemove: any) {
    this.appliedFilters = this.appliedFilters.filter(filter => filter !== filterToRemove);
  }


  public openAddRequestModal(dateArg): void {
    const passData = {
      date: dateArg.dateStr,
      currentView: this.currentView,
    };
    const initialState = {
      data: passData,
      title: 'Modal with component',
      currentPage: 'allCalendar',
      selectfunction: this.selectBookingDropdown.bind(this),
      selectedParams: ''
    };
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.deliveryService.selectedBookingTypes$.pipe(take(1)).subscribe((checkedTypes) => {
      let navigateForm: any;
      if (checkedTypes[0] === "deliveryData") {
        navigateForm = NewDeliveryFormComponent
      } else if (checkedTypes[0] === "craneData") {
        navigateForm = NewCraneRequestCreationFormComponent
      } else if (checkedTypes[0] === "concreteData") {
        navigateForm = AddConcreteRequestComponent
      } else if (checkedTypes[0] === "inspectionData") {
        navigateForm = NewinspectionFormComponent
      }
      if (checkedTypes.length) {
        this.modalRef = this.modalService.show(navigateForm, {
          backdrop: 'static',
          keyboard: false,
          class: className,
          initialState
        });
        this.modalRef.content.lastId = this.lastId;
        this.modalRef.content.closeBtnName = 'Close';
      }
    })

  }

  public initializeSeriesOption(): void {
    this.seriesOptions = [
      {
        option: 1,
        text: 'This event',
        disabled: false,
      },
      {
        option: 2,
        text: 'This and all following events',
        disabled: false,
      },
      {
        option: 3,
        text: 'All events in the series',
        disabled: false,
      },
    ];
  }

  public selectBookingDropdown(type,date) {
    this.close()
    const initialState = {
      title: 'Modal with component',
      currentPage: 'allCalendar',
      selectfunction: this.selectBookingDropdown.bind(this),
      selectedParams: '',
      selectedEventDate: date
    };
    if (type === 'New Delivery Booking') {
      const className = 'modal-lg new-delivery-popup custom-modal';
      this.modalRef = this.modalService.show(NewDeliveryFormComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
        initialState
      });
      this.modalRef.content.lastId = this.lastId;
      this.modalRef.content.closeBtnName = 'Close';
    } else if (type === 'New Inspection Booking') {
      const className = 'modal-lg new-delivery-popup custom-modal';
      this.modalRef = this.modalService.show(NewinspectionFormComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
        initialState
      });
      this.modalRef.content.lastId = this.lastId;
      this.modalRef.content.closeBtnName = 'Close';
    } else if (type === 'New Crane Booking') {
      const className = 'modal-lg new-delivery-popup custom-modal';
      this.modalRef = this.modalService.show(NewCraneRequestCreationFormComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
        initialState
      });
      this.modalRef.content.lastId = this.lastId;
      this.modalRef.content.closeBtnName = 'Close';
    } else if (type === 'New Concrete Booking') {
      const className = 'modal-lg new-delivery-popup custom-modal';
      this.modalRef = this.modalService.show(AddConcreteRequestComponent, {
        backdrop: 'static',
        keyboard: false,
        class: className,
        initialState
      });
    }
  }

  public clearAllSubFilters() {
    this.selectedSubFilterOptions = [];
    this.showHideText = 'Hide'
    this.showClear = false
    this.filterForm.reset();
    this.getEventNDR()
  }

  // Method to hide subfilters
  public hideSubFilters(type) {
    if(type === 'Hide'){
      this.isSubFilterVisible = false;
      this.showHideText = 'Show'
      this.showClear = false
    }else{
      this.isSubFilterVisible = true;
      this.showClear = true
      this.showHideText = 'Hide'
    }
  }

  public changeRequestCollapse(data): void {
    this.initializeSeriesOption();
    if (!moment(data.inspectionStart).isAfter(moment())) {
      this.seriesOptions = this.seriesOptions.filter((object): any => {
        const seriesObject = object;
        if (seriesObject.option !== 1) {
          seriesObject.disabled = true;
        }
        return seriesObject;
      });
    }
    this.allRequestIsOpened = !this.allRequestIsOpened;
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }

  public clear(): void {
    this.showSearchbar = false;
    this.search = '';
    this.getEventNDR();
  }

  public getSearchNDR(data: any): void {
    if (data.length > 0) {
      this.showSearchbar = true;
    } else {
      this.showSearchbar = false;
    }
    this.search = data;
    this.getEventNDR();
  }

  public resetFilter(): void {
    this.filterCount = 0;
    this.filterForm.reset();
    this.search = '';
    this.filterDetailsForm();
    this.getEventNDR();
    this.modalRef.hide();
  }

  public calendarGetCompany(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getCompanies(params).subscribe((companyResponse: any): void => {
      if (companyResponse) {
        this.companyList = companyResponse.data;
        this.calendarGetDefinable();
        this.dropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'companyName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
      }
    });
  }

  public filterDetailsForm(): void {
    this.filterForm = this.formBuilder.group({
      companyFilter: [''],
      descriptionFilter: ['' , Validators.compose([Validators.required])],
      statusFilter: [''],
      memberFilter: [''],
      gateFilter: [''],
      equipmentFilter: [''],
      locationFilter: [''],
      dateFilter: ['', Validators.compose([Validators.required])],
      pickFrom: ['', Validators.compose([Validators.required])],
      pickTo: ['', Validators.compose([Validators.required])],
      orderNumberFilter: ['' , Validators.compose([Validators.required])],
      inspectionTypeFilter: ['' , Validators.compose([Validators.required])]
    });
  }

  public ngAfterViewInit(): void {
    this.projectService.projectParent.subscribe((response): void => {
      if (response !== undefined && response !== null && response !== '') {
        this.ParentCompanyId = response.ParentCompanyId;
        this.ProjectId = response.ProjectId;
        this.getMembers();
        this.setCalendar();
      }
    });
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
        if (this.authUser.RoleId === 2) {
          this.statusValue = ['Approved', 'Declined'];
        } else if (this.authUser.RoleId === 3) {
          this.statusValue = ['Delivered'];
        }
      }
    });
  }

  public setCalendar(): void {
    this.calendarApi = this.calendarComponent1.getApi();
    this.Range = this.calendarApi?.currentData?.dateProfile?.activeRange;

    this.getEventNDR();
  }

  public selectStatus(status): void {
    this.currentStatus = status;
  }

  public filterSubmit(): void {
    this.filterCount = 0;
    if (this.filterForm.get('descriptionFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('dateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('companyFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('memberFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('gateFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('equipmentFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('locationFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('statusFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('pickFrom').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('pickTo').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('orderNumberFilter').value !== '') {
      this.filterCount += 1;
    }
    if (this.filterForm.get('inspectionTypeFilter').value !== '') {
      this.filterCount += 1;
    }
    this.getEventNDR();
    this.modalRef.hide();
  }

  public getEventNDR(): void {
    this.loader = true;
    this.deliveryList = [];
    if (this.ProjectId && this.ParentCompanyId) {
      const filterParams = {
        ProjectId: this.ProjectId,
        void: 0,
      };
      let getEventNdrPayload: any = {};
      if (this.filterForm !== undefined) {
        const getEventNdrDateInFilterForm = this.filterForm.value.dateFilter
          ? moment(this.filterForm.value.dateFilter).format('YYYY-MM-DD')
          : this.filterForm.value.dateFilter;
        getEventNdrPayload = {
          companyFilter: +this.filterForm.value.companyFilter,
          descriptionFilter: this.filterForm.value.descriptionFilter,
          dateFilter: getEventNdrDateInFilterForm,
          statusFilter: this.filterForm.value.statusFilter,
          memberFilter: +this.filterForm.value.memberFilter,
          gateFilter: +this.filterForm.value.gateFilter,
          equipmentFilter: this.filterForm.value.equipmentFilter === null || this.filterForm.value.equipmentFilter === '' ? null : +this.filterForm.value.equipmentFilter,
          locationFilter: this.filterForm.value.locationFilter,
          pickFrom: this.filterForm.value.pickFrom,
          pickTo: this.filterForm.value.pickTo,
          inspectionTypeFilter: this.filterForm.value.inspectionTypeFilter,
          ParentCompanyId: this.ParentCompanyId,
          search: this.search,
        };
      }
      getEventNdrPayload.search = this.search;
      getEventNdrPayload.start = moment(this.Range?.start).format('YYYY-MM-DD');
      getEventNdrPayload.end = moment(this.Range?.end).format('YYYY-MM-DD');
      getEventNdrPayload.filterCount = this.filterCount;
      getEventNdrPayload.calendarView = this.currentView;
      this.calendarService
        .getAllCalendarEventNDR(filterParams, getEventNdrPayload)
        .subscribe({
          next: (NdrResponse: any): void => {
            if (!this.type && NdrResponse) {
              this.loader = false;
              this.deliveryService.updateAllCalendarPermanentRespData(NdrResponse.datas)
              this.deliveryService.updateStatusCardValues(NdrResponse.datas.inspectionData)
              this.deliveryService.selectedBookingTypes$.pipe(take(1)).subscribe((checkedTypes) => {
                let tempData = {};
                if (checkedTypes.length > 0) {
                  checkedTypes.forEach(type => {
                    if (NdrResponse.datas[type]) {
                      tempData[type] = NdrResponse.datas[type];
                    }
                  });
                }
                this.deliveryService.updateAllCalendarRespData(tempData)
                this.setEventNDRData(tempData);
              });
            }
          },
          error: (reportError): void => {
            this.loader = false;
          },
        });
    }
  }

  public setEventNDRData(data): void {
    if (data) {
      const responseData = data;
      const clonedTempData = JSON.parse(JSON.stringify(responseData));
      this.deliveryService.selectedBookingTypes$.pipe(take(1)).subscribe((checkedTypes) => {
        if (checkedTypes.includes('deliveryData') && checkedTypes.includes('craneData')) {
          clonedTempData['craneData'].data = clonedTempData['craneData'].data.filter(
            (craneData) => craneData.requestType !== 'deliveryRequestWithCrane',
          );
        } else if (!checkedTypes.includes('deliveryData') && checkedTypes.includes('craneData')) {
          responseData['deliveryData'].data.forEach(craneData => {
            if(craneData.requestType === 'deliveryRequestWithCrane') {
              clonedTempData['craneData'].data.push(craneData)
            }
          })
        }
      })
      let allCalendarFetchData = [...clonedTempData.inspectionData?.data || [], ...clonedTempData.deliveryData?.data || [], ...clonedTempData.craneData?.data || [], ...clonedTempData.concreteData?.data || []];

      const calendarEventData = allCalendarFetchData.filter(item => item.requestType === "calendarEvent");

      const otherData = allCalendarFetchData.filter(item => item.requestType !== "calendarEvent");

      const filteredCalendarEvent = calendarEventData.reduce((acc, current) => {
        const found = acc.find(item => item.fromDate === current.fromDate);
        if (!found) {
          acc.push(current);
        }
        return acc;
      }, []);
      const allCalendarData = [...otherData , ...filteredCalendarEvent]

      let CardDataValue: any
      this.deliveryService.AllCalendarStatusCard$.subscribe((data) => {
        CardDataValue = data
      })
      const statusData = JSON.parse(CardDataValue.statusData.statusColorCode);
      const UseTextColorAsLegendColor = JSON.parse(CardDataValue.statusData.useTextColorAsLegend);
      const isDefaultColor = JSON.parse(CardDataValue.statusData.isDefaultColor);
      const approved = statusData.find((item) => item.status === 'approved');
      const pending = statusData.find((item) => item.status === 'pending');
      const delivered = statusData.find((item) => item.status === 'delivered');
      const rejected = statusData.find((item) => item.status === 'rejected');
      const expired = statusData.find((item) => item.status === 'expired');
      if (UseTextColorAsLegendColor) {
        this.approved = approved.fontColor;
        this.pending = pending.fontColor;
        this.expired = expired.fontColor;
        this.rejected = rejected.fontColor;
        this.delivered = delivered.fontColor;
      } else {
        this.approved = approved.backgroundColor;
        this.pending = pending.backgroundColor;
        this.expired = expired.backgroundColor;
        this.rejected = rejected.backgroundColor;
        this.delivered = delivered.backgroundColor;
      }
      if (isDefaultColor) {
        this.delivered = delivered.backgroundColor;
      }
      this.lastId = CardDataValue.lastId;
      this.deliveryList = allCalendarData;
      this.events = [];
      this.deliveryList.forEach((element): void => {
        const assignData: any = {
          description: '',
          title: '',
          start: '',
          end: '',
          id: '',
          uniqueNumber: '',
          companyName: '',
          allDay: false,
          allDaySlot: false,
          line2: '',
          requestType: '',
          inspectionStatus: '',
        };
        let cardData;
        assignData.requestType = element.requestType;
        assignData.inspectionStatus = element.inspectionStatus;
        if (element.requestType === 'inspectionRequest'
          || element.requestType === 'inspectionRequestWithCrane') {
          assignData.start = element.inspectionStart;
          assignData.end = element.inspectionEnd;
          cardData = JSON.parse(CardDataValue.cardData.inspectionCard);
        } else if (element.requestType === 'deliveryRequest'
          || element.requestType === 'deliveryRequestWithCrane') {
          assignData.start = element.deliveryStart;
          assignData.end = element.deliveryEnd;
          cardData = JSON.parse(CardDataValue.cardData.deliveryCard);
        } else if (element.requestType === 'craneRequest') {
          assignData.start = element.craneDeliveryStart;
          assignData.end = element.craneDeliveryEnd;
          cardData = JSON.parse(CardDataValue.cardData.craneCard);
        } else if (element.requestType === 'concreteRequest') {
          assignData.start = element.concretePlacementStart;
          assignData.end = element.concretePlacementEnd;
          assignData.id = element.requestType === 'craneRequest'
            || element.requestType === 'deliveryRequestWithCrane'
            ? element.CraneRequestId
            : element.id;
          cardData = JSON.parse(CardDataValue.cardData.concreteCard);
        }
        const previewSelected = cardData?.filter((item) => item.selected);
        assignData.id = element.id;
        assignData.uniqueNumber = element.uniqueNumber;
        if (element.requestType === 'calendarEvent') {
          assignData.description = element.description;
          assignData.title = element.description;
          assignData.start = element.fromDate;
          assignData.end = element.toDate;
        }

        if (element.requestType === 'calendarEvent' && element.isAllDay === true) {
          delete assignData.allDay;
          delete assignData.allDaySlot;
          assignData.allDay = true;
          assignData.allDaySlot = true;
        }
        previewSelected?.forEach((item): any => {
          const { line } = item;
          let value = '';
          switch (item.label) {
            case 'Description':
              value = element.description;
              break;
            case 'Responsible Company':
              value = element?.companyDetails?.[0]?.Company?.companyName || '';
              break;
            case 'Concrete Supplier':
              value = element?.concreteSupplierDetails?.[0]?.Company?.companyName || '';
              break;
            case 'Concrete Request ID':
              value = element.ConcreteRequestId;
              break;
            case 'Responsible Person': {
              const user = element?.memberDetails?.[0]?.Member?.User;
              value = user ? `${user.firstName} ${user.lastName}` : '';
              break;
            }
            case 'Gate':
              value = element?.gateDetails?.[0]?.Gate?.gateName || '';
              break;
            case 'Inspection ID':
              value = element.InspectionId;
              break;
            case 'Crane Pick ID':
              value = element.CraneRequestId;
              break;
            case 'Definable Feature Of Work':
              value = element.defineWorkDetails?.[0]?.DeliverDefineWork?.DFOW || '';
              break;
            case 'Location':
              value = element.locationDetails?.[0]?.ConcreteLocation?.location || '';
              break;
            case 'Equipment':
              value = element.equipmentDetails?.[0]?.Equipment?.equipmentName || 'No Equipment Needed';
              break;
            default:
              return;
          }
          if (line === 1) assignData.description = value;
          if (line === 2) assignData.line2 = value;
        });

        if (element.status === 'Pending' || element.status === 'Tentative') {
          assignData.color = pending.backgroundColor;
          assignData.textColor = pending.fontColor;
          assignData.borderColor = pending.fontColor;
          this.events.push(assignData);
        } else if (element.status === 'Completed' || element.status === 'Delivered') {
          assignData.color = delivered.backgroundColor;
          assignData.textColor = delivered.fontColor;
          assignData.borderColor = delivered.backgroundColor;
          this.events.push(assignData);
        } else if (element.status === 'Approved') {
          assignData.color = approved.backgroundColor;
          assignData.textColor = approved.fontColor;
          assignData.borderColor = approved.fontColor;
          this.events.push(assignData);
        } else if (element.status === 'Declined') {
          assignData.color = rejected.backgroundColor;
          assignData.textColor = rejected.fontColor;
          assignData.borderColor = rejected.fontColor;
          this.events.push(assignData);
        } else if (element.status === 'Expired') {
          assignData.color = expired.backgroundColor;
          assignData.textColor = expired.fontColor;
          assignData.borderColor = expired.fontColor;
          this.events.push(assignData);
        } else if (!element.status) {
          assignData.className = 'calendar_event';
          this.events.push(assignData);
        }
      });
      this.calendarApi.removeAllEventSources();
      this.calendarApi.addEventSource(this.events);
    }
  }

  public goNext(): void {
    this.closeDescription();
    this.calendarApi.next();
    this.selectedSubFilterOptions = [];
    this.showHideText = 'Hide'
    this.showClear = false
    this.filterForm.reset();
    this.setCalendar();
  }

  public goTimeGridWeekOrDay(view): void {
    if (view === 'timeGridWeek') {
      this.currentView = 'Week';
    } else {
      this.currentView = 'Day';
    }
    this.closeDescription();
    this.calendarApi.changeView(view);
    this.setCalendar();
  }

  public goDayGridMonth(): void {
    this.currentView = 'Month';
    this.closeDescription();
    this.calendarApi.changeView('dayGridMonth');
    this.setCalendar();
  }

  public goPrev(): void {
    this.closeDescription();
    this.calendarApi.prev(); // call a method on the Calendar object
    this.selectedSubFilterOptions = [];
    this.showHideText = 'Hide'
    this.showClear = false
    this.filterForm.reset();
    this.setCalendar();
  }

  public goPrevYear(): void {
    this.closeDescription();
    this.calendarApi.prevYear(); // call a method on the Calendar object
    this.setCalendar();
  }

  public goNextYear(): void {
    this.closeDescription();
    this.calendarApi.nextYear(); // call a method on the Calendar object
    this.setCalendar();
  }

  public deliveryDescription(arg): void {
    this.resetPopupStates();
    const { event } = arg;
    const uniqueNumber = event.extendedProps?.uniqueNumber;
    const requestType = event.extendedProps?.requestType;

    const index = this.findDeliveryIndex(+event.id, uniqueNumber, requestType);
    this.calendarCurrentDeliveryIndex = index;
    this.eventData = this.deliveryList[index];

    this.handleRequestType(requestType, arg);
  }

  public resetPopupStates(): void {
    this.eventData = [];
    this.allRequestIsOpened = false;
    this.calendarDescriptionPopup = false;
    this.descriptionPopup = false;
  }

  public findDeliveryIndex(id: number, uniqueNumber?: any, requestType?: string): number {
    return this.deliveryList.findIndex((item) => item.id === id
      && (!uniqueNumber || item.uniqueNumber === uniqueNumber)
      && (!requestType || item.requestType === requestType));
  }

  public handleRequestType(requestType: string, arg: any): void {
    const formatDate = (date) => moment(date).format('MM/DD/YYYY');
    const formatTime = (time) => moment(time).format('hh:mm A');

    switch (requestType) {
      case 'inspectionRequest':
      case 'inspectionRequestWithCrane':
        this.eventData.startDate = formatDate(
          this.deliveryList[this.calendarCurrentDeliveryIndex].inspectionStart,
        );
        this.eventData.startTime = formatTime(
          this.deliveryList[this.calendarCurrentDeliveryIndex].inspectionStart,
        );
        this.eventData.endTime = formatTime(this.eventData.inspectionEnd);
        if (this.eventData.approved_at) this.eventData.approvedAt = moment(this.eventData.approved_at).format('lll');
        this.getNDR(arg);
        this.openIdModal(this.eventData);
        break;

      case 'craneRequest':
        this.resetPopupStates();
        this.eventData = this.deliveryList[this.calendarCurrentDeliveryIndex];
        this.eventData.startDate = formatDate(this.eventData.craneDeliveryStart);
        this.eventData.startTime = formatTime(this.eventData.craneDeliveryStart);
        this.eventData.endTime = formatTime(this.eventData.craneDeliveryEnd);
        if (this.eventData.approved_at) this.eventData.approvedAt = moment(this.eventData.approved_at).format('lll');
        this.getCraneRequest(arg);
        this.openCraneIdModal(this.eventData);
        break;

      case 'deliveryRequestWithCrane':
      case 'deliveryRequest':
        this.eventData.startDate = formatDate(this.eventData.deliveryStart);
        this.eventData.startTime = formatTime(this.eventData.deliveryStart);
        this.eventData.endTime = formatTime(this.eventData.deliveryEnd);
        if (this.eventData.approved_at) this.eventData.approvedAt = moment(this.eventData.approved_at).format('lll');
        this.getDeliveryNDR(arg);
        this.openDeliveryIdModal(this.eventData);
        break;

      case 'concreteRequest': {
        const getConcreteRequest = this.deliveryList[this.calendarCurrentDeliveryIndex];
        const initialState = {
          data: {
            id: getConcreteRequest.ConcreteRequestId,
            ProjectId: getConcreteRequest.ProjectId,
            ParentCompanyId: this.ParentCompanyId,
          },
          title: 'Modal with component',
        };
        this.deliveryService.updatedCurrentConcreteRequestStatus(
          +getConcreteRequest.ConcreteRequestId,
        );
        this.modalRef1 = this.modalService.show(ConcreteDetailHeaderComponent, {
          backdrop: 'static',
          keyboard: false,
          class: 'modal-lg new-delivery-popup custom-modal',
          initialState,
        });
        this.modalRef1.content.closeBtnName = 'Close';
        break;
      }
      case 'calendarEvent':
        this.calendarDescription(arg);
        break;

      default:
        break;
    }
  }

  public getCraneRequest(data: { event: { id: string | number } }): void {
    const param = {
      CraneRequestId: +data.event.id,
      ParentCompanyId: this.ParentCompanyId,
      ProjectId: this.ProjectId,
    };
    this.deliveryService.getEquipmentCraneRequest(param).subscribe((res): void => {
      if (this.authUser.RoleId === 4 || this.authUser.RoleId === 3) {
        const newMember = res.data.memberDetails;
        const index = newMember.findIndex(
          (i: { Member: { id: any } }): boolean => i.Member.id === this.authUser.id,
        );
        if (index !== -1) {
          this.eventData.edit = true;
        } else {
          this.eventData.edit = false;
        }
      } else {
        this.eventData.edit = true;
      }
      this.eventData.gateDetails = res.data.gateDetails;
      this.toolTipContent = '';
      if (this.eventData.memberDetails.length > 3) {
        const slicedArray = this.eventData.memberDetails.slice(3);
        slicedArray.map(
          (a: { Member: { User: { firstName: any; lastName: any; email: any } } }): any => {
            if (a.Member.User.firstName) {
              this.toolTipContent += `${a.Member.User.firstName} ${a.Member.User.lastName}, `;
            } else {
              this.toolTipContent += `${a.Member.User.email}, `;
            }
          },
        );
      }
    });
  }

  public closeCalendarDescription(): void {
    this.calendarDescriptionPopup = false;
    this.descriptionPopup = false;
    this.allRequestIsOpened = false;
    this.viewEventData = '';
  }

  public calendarDescription(arg): void {
    this.calendarDescriptionPopup = false;
    this.descriptionPopup = false;
    this.viewEventData = '';
    const index = this.events.findIndex(
      (item: any): any =>
        item.description === arg.event.title &&
        item.uniqueNumber === arg.event.extendedProps.uniqueNumber,
    );
    this.viewEventData = this.deliveryList[index];
    this.occurMessage(this.viewEventData);
    this.calendarDescriptionPopup = true;
  }

  public occurMessage(data): void {
    this.message = 'Occurs every day';
    if (data.repeatEveryType === 'Day') {
      this.message = '';
      this.message = 'Occurs every day';
    }
    if (data.repeatEveryType === 'Days') {
      this.message = '';
      if (+data.repeatEveryCount === 2) {
        this.message = 'Occurs every other day';
      } else {
        this.message = `Occurs every ${data.repeatEveryCount} days`;
      }
    }
    if (data.repeatEveryType === 'Week') {
      this.message = '';
      let weekDays = '';
      data.days.forEach((day1: any): any => {
        weekDays = `${weekDays + day1},`;
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message += `Occurs every ${weekDays}`;
    }
    if (data.repeatEveryType === 'Weeks') {
      let weekDays = '';
      data.days.forEach((day1: any): any => {
        weekDays = `${weekDays + day1},`;
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message = '';
      if (+data.repeatEveryCount === 2) {
        this.message = `Occurs every other  ${weekDays}`;
      } else {
        this.message = `Occurs every ${data.repeatEveryCount} weeks on ${weekDays}`;
      }
    }
    if (
      data.repeatEveryType === 'Month' ||
      data.repeatEveryType === 'Months' ||
      data.repeatEveryType === 'Year' ||
      data.repeatEveryType === 'Years'
    ) {
      if (data.chosenDateOfMonth) {
        this.message = `Occurs on day ${data.dateOfMonth}`;
      } else {
        this.message = `Occurs on the ${data.monthlyRepeatType}`;
      }
    }
    this.message += ` until ${moment(data.endTime).format('MM-DD-YYYY')}`;
  }

  public closeDescription(): void {
    this.descriptionPopup = false;
  }

  public changeFormat(fromDate: any): any {
    if (fromDate) {
      const dayFormat = moment(new Date(fromDate)).format('ddd MM/DD/YYYY');
      return dayFormat;
    }
  }

  public setStatus(item): void {
    this.deliveryId = -1;
    this.deliveryService.updatedDeliveryId(item.id);
    this.calendarCurrentDeliveryIndex = this.deliveryList.findIndex(
      (i): boolean => i.id === item.id,
    );
    this.currentDeliverySaveItem = this.deliveryList[this.calendarCurrentDeliveryIndex];
    this.currentDeliverySaveItem.edit = item.edit;
    const condition = item.status !== 'Expired' && item.status !== 'Delivered';
    if (
      (this.authUser.RoleId === 2 && condition) ||
      (this.authUser.RoleId === 3 && item.status === 'Approved')
    ) {
      this.showStatus = true;
    } else {
      this.showStatus = false;
    }
  }


  public openDeliveryIdModal(item): void {
    const data = item;
    data.ParentCompanyId = this.ParentCompanyId;
    this.deliveryService.updatedCurrentStatus(data.id);
    const initialState =
    {
      data,
      title: 'Modal with component',
    };
    this.modalRef = this.modalService.show(DeliveryDetailsNewComponent, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-delivery-popup custom-modal',
      initialState,
    });
    this.modalRef.content.closeBtnName = 'Close';
  }

  public openCraneIdModal(item): void {
    const data = item;
    data.ParentCompanyId = this.ParentCompanyId;
    if (
      !this.eventData.isAssociatedWithDeliveryRequest
      && !this.eventData.isAssociatedWithCraneRequest
    ) {
      const newPayload = {
        id: item.CraneRequestId,
        ProjectId: item.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.deliveryService.updatedCurrentCraneRequestStatus(data.CraneRequestId);
      const initialState =
      {
        data: newPayload,
        title: 'Modal with component',
      };
      this.modalRef = this.modalService.show(CraneRequestDetailViewHeaderComponent, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal',
        initialState,
      });
    } else {
      const newPayload = {
        id: item.id,
        ProjectId: item.ProjectId,
        ParentCompanyId: this.ParentCompanyId,
      };
      this.deliveryService.updatedCurrentStatus(data.id);
      const initialState =
      {
        data: newPayload,
        title: 'Modal with component',
      };
      this.modalRef = this.modalService.show(DeliveryDetailsNewComponent, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal',
        initialState,
      });
    }
    this.modalRef.content.closeBtnName = 'Close';
  }

  public openIdModal(item): void {
    const data = item;
    data.ParentCompanyId = this.ParentCompanyId;
    this.deliveryService.updatedCurrentStatus(data.id);
    const initialState =
    {
      data,
      title: 'Modal with component',
    };
    this.modalRef = this.modalService.show(InspectionDetailsNewComponent, {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg new-delivery-popup custom-modal',
      initialState,
    });
    this.modalRef.content.closeBtnName = 'Close';
  }

  public moveToVoidList(): void {
    if (!this.voidSubmitted) {
      this.voidSubmitted = true;
      const calendarCurrentDeliveryItem = this.deliveryList[this.calendarCurrentDeliveryIndex];
      const addToVoidPayload = {
        inspectionRequestId: calendarCurrentDeliveryItem.id,
        ProjectId: this.ProjectId,
      };
      this.deliveryService.createVoid(addToVoidPayload).subscribe({
        next: (createVoidResponse: any): void => {
          if (createVoidResponse) {
            this.toastr.success(createVoidResponse.message, 'Success');
            this.mixpanelService.addMixpanelEvents('Delivery Booking Voided');
            this.voidSubmitted = false;
            this.router.navigate(['/void-list']);
            this.closeDescription();
            this.close();
          }
        },
        error: (addToVoidError): void => {
          this.voidSubmitted = false;
          if (addToVoidError.message?.statusCode === 400) {
            this.showError(addToVoidError);
          } else if (!addToVoidError.message) {
            this.toastr.error('Try again later.!', 'Something went wrong.');
          } else {
            this.toastr.error(addToVoidError.message, 'OOPS!');
          }
        },
      });
    }
  }

  public showError(err): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public calendarGetOverAllGate(): void {
    this.modalLoader = true;
    const calendarGetGateParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .gateList(calendarGetGateParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((calendarGetGateListResponse): void => {
        this.gateList = calendarGetGateListResponse.data;
        this.calendarGetOverAllEquipment();
      });
  }

  public calendarGetOverAllEquipment(): void {
    const calendarGetEquipmentParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listEquipment(calendarGetEquipmentParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((calendarGetEquipmentListParams): void => {
        this.equipmentList = [this.noEquipmentOption, ...calendarGetEquipmentListParams.data];
        this.calendarGetCompany();
      });
  }

  public calendarGetDefinable(): void {
    const calendarGetDefinableParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getDefinableWork(calendarGetDefinableParams)
      .subscribe((calendarGetDefinableListResponse: any): void => {
        if (calendarGetDefinableListResponse) {
          const { data } = calendarGetDefinableListResponse;
          this.defineList = data;
          this.definableDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'DFOW',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
          this.getLocations();
        }
      });
  }

  public getLocations(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getLocations(param).subscribe((response: any): void => {
      if (response) {
        const { data } = response;
        this.locationList = data;
        this.openContentModal();
      }
    });
  }

  public close(): void {
    this.submitted = false;
    this.formSubmitted = false;
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  public convertStart(deliveryDate, startHours, startMinutes): string {
    if (!deliveryDate) return null;

    const fullYear = deliveryDate.getFullYear();
    const fullMonth = deliveryDate.getMonth();
    const date = deliveryDate.getDate();
    const deliveryNewStart = new Date(fullYear, fullMonth, date, startHours, startMinutes);
    const inspectionStart = deliveryNewStart.toUTCString();
    return inspectionStart;
  }

  public checkFutureDate(inspectionStart, inspectionEnd): boolean {
    const startDate = new Date(inspectionStart).getTime();
    const currentDate = new Date().getTime();
    const endDate = new Date(inspectionEnd).getTime();
    if (startDate > currentDate && endDate > currentDate) {
      if (startDate < endDate) {
        return true;
      }
      return false;
    }
    return false;
  }

  public getResponsiblePeople(person): string {
    if (!person) return 'UU';

    const firstInitial = person.firstName ? person.firstName.charAt(0).toUpperCase() : 'U';
    const lastInitial = person.lastName ? person.lastName.charAt(0).toUpperCase() : 'U';

    return firstInitial + lastInitial;
  }

  public getDeliveryNDR(data): void {
    this.toolTipContent = '';
    const param = {
      DeliveryRequestId: +data.event.id,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.getNDRData(param).subscribe((res): void => {
      if (this.authUser.RoleId === 4 || this.authUser.RoleId === 3) {
        const newMember = res.data.memberDetails;
        const index = newMember.findIndex((i): boolean => i.Member.id === this.authUser.id);
        if (index !== -1) {
          this.eventData.edit = true;
        } else {
          this.eventData.edit = false;
        }
      } else {
        this.eventData.edit = true;
      }
      this.eventData.companyDetails = res.data.companyDetails;
      this.eventData.gateDetails = res.data.gateDetails;
      this.eventData.memberDetails = res.data.memberDetails;
      this.eventData.equipmentDetails = res.data.equipmentDetails;
      if (this.eventData.memberDetails.length > 3) {
        const slicedArray = this.eventData.memberDetails.slice(3);
        slicedArray.map((a): any => {
          if (a.Member.User.firstName) {
            this.toolTipContent += `${a.Member.User.firstName} ${a.Member.User.lastName}, `;
          } else {
            this.toolTipContent += `${a.Member.User.email}, `;
          }
        });
      }
    });
  }

  public getCraneNDR(data: { event: { id: string | number } }): void {
    const index = this.deliveryList.findIndex(
      (item): boolean => item.id === +data.event.id,
    );
    const clickedEventData = this.deliveryList[index];
    const param = {
      DeliveryRequestId: clickedEventData.id,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.getNDRData(param).subscribe((res): void => {
      if (this.authUser.RoleId === 4 || this.authUser.RoleId === 3) {
        const newMember = res.data.memberDetails;
        const index1 = newMember.findIndex(
          (i: { Member: { id: any } }): boolean => i.Member.id === this.authUser.id,
        );
        if (index1 !== -1) {
          this.eventData.edit = true;
        } else {
          this.eventData.edit = false;
        }
      } else {
        this.eventData.edit = true;
      }
      this.eventData.gateDetails = res.data?.gateDetails;
      this.toolTipContent = '';
      if (this.eventData.memberDetails.length > 3) {
        const slicedArray = this.eventData.memberDetails.slice(3);
        slicedArray.map(
          (a: { Member: { User: { firstName: any; lastName: any; email: any } } }): any => {
            if (a.Member.User.firstName) {
              this.toolTipContent += `${a.Member.User.firstName} ${a.Member.User.lastName}, `;
            } else {
              this.toolTipContent += `${a.Member.User.email}, `;
            }
          },
        );
      }
    });
  }

  public getNDR(data): void {
    this.toolTipContent = '';
    const param = {
      inspectionRequestId: +data.event.id,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.getInspectionNDRData(param).subscribe((res): void => {
      if (this.authUser.RoleId === 4 || this.authUser.RoleId === 3) {
        const newMember = res.data.memberDetails;
        const index = newMember.findIndex((i): boolean => i.Member.id === this.authUser.id);
        if (index !== -1) {
          this.eventData.edit = true;
        } else {
          this.eventData.edit = false;
        }
      } else {
        this.eventData.edit = true;
      }
      this.eventData.companyDetails = res.data.companyDetails;
      this.eventData.gateDetails = res.data.gateDetails;
      this.eventData.memberDetails = res.data.memberDetails;
      this.eventData.equipmentDetails = res.data.equipmentDetails;
      if (this.eventData.memberDetails.length > 3) {
        const slicedArray = this.eventData.memberDetails.slice(3);
        slicedArray.map((a): any => {
          if (a.Member.User.firstName) {
            this.toolTipContent += `${a.Member.User.firstName} ${a.Member.User.lastName}, `;
          } else {
            this.toolTipContent += `${a.Member.User.email}, `;
          }
        });
      }
    });
  }

  public getEditValue(data): void {

    const param = {
      inspectionRequestId: +data.id,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.getInspectionNDRData(param).subscribe((res): void => {
      const newMember = res.data.memberDetails;
      const index = newMember.findIndex((i): boolean => i.Member.id === this.authUser.id);
      if (index !== -1) {
        this.eventData.edit = true;
      } else {
        this.eventData.edit = false;
      }
      this.eventData.companyDetails = res.data.companyDetails;
      this.eventData.gateDetails = res.data.gateDetails;
      this.setStatus(this.eventData);
    });
  }

  public openAddNDRModal(): void {
    const initialState = {
      title: 'Modal with component',
      currentPage: 'allCalendar',
      selectfunction: this.selectBookingDropdown.bind(this),
      selectedParams: ''
    };
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(NewDeliveryFormComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
      initialState
    });
    this.modalRef.content.lastId = this.lastId;
    this.modalRef.content.closeBtnName = 'Close';
  }

  public openEditModal(item, action): void {
    this.closeDescription();
    if (this.modalRef) {
      this.close();
    }
    this.deliveryService.updatedDeliveryId(item.id);
    const className = 'modal-lg new-delivery-popup custom-modal';
    this.modalRef = this.modalService.show(EditinspectionFormComponent, {
      backdrop: 'static',
      keyboard: false,
      class: className,
    });
    this.modalRef.content.closeBtnName = 'Close';
    this.modalRef.content.seriesOption =
      item?.recurrence && item?.recurrence?.recurrence !== 'Does Not Repeat' ? action : 1;
    this.modalRef.content.recurrenceId = item?.recurrence ? item?.recurrence?.id : null;
    this.modalRef.content.recurrenceEndDate = item?.recurrence
      ? item?.recurrence?.recurrenceEndDate
      : null;
  }

  public openContentModal(): void {
    this.modalLoader = false;
  }

  public openDeleteModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template);
  }

  public openFilterModal(template: TemplateRef<any>): void {
    this.activeSubFilter = null;
    this.isMainFilterVisible = true
    this.isFilterPopupVisible = true;
    this.calendarGetOverAllGate();
    let data = {};
    data = { backdrop: 'static', keyboard: false, class: 'modal-sm filter-popup custom-modal' };
    this.modalRef = this.modalService.show(template, data);
  }

  public openModal(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public voidConfirmationResponse(action): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.moveToVoidList();
    }
  }

  handleToggleKeydown(event: KeyboardEvent, filter: any, type : any, data? : any): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'toggle':
          this.toggleSubFilter(filter);
          break;
        case 'clear':
          this.onClearSubfilter(filter);
          break;
        case 'hide':
          this.hideSubFilters(filter);
          break;
        case 'clearAll':
          this.clearAllSubFilters();
          break;
        case 'closeImg':
          this.clear();
          break;
        case 'openModal':
          this.openFilterModal(filter);
          break;
        case 'openIdModal':
          this.openIdModal(filter);
          break;
        case 'openSubFil':
          this.openSubFilter(filter);
          break;
        case 'closeCal':
          this.closeCalendarDescription();
          break;
        case 'openEditMod':
          this.openEditModal(filter, data);
          break;
        default:
          break;
      }
    }
  }
}
function getEventNDR() {
  throw new Error('Function not implemented.');
}
