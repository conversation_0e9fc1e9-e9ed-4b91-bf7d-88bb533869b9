import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from '../api_base/api.service';

@Injectable({
  providedIn: 'root',
  })
export class NotificationPreferenceService {
  public constructor(private readonly api: ApiService) {
  }

  public getNotificationList(payload): Observable<any> {
    return this.api.get('notification_preference/get_notification_preference_list', payload);
  }

  public setNotificationPreference(params, payload): Observable<any> {
    return this.api.postMethod('notification_preference/set_notification_preference', payload, params);
  }
}
