import { Component, OnInit } from '@angular/core';
import { ProjectService } from '../../services/profile/project.service';

@Component({
  selector: 'app-members-settings',
  templateUrl: './members-settings.component.html'
  })
export class MembersSettingsComponent implements OnInit {
  public userData: any = {};

  public isAdmin = false;

  constructor(
    public projectService: ProjectService,
  ) {
    this.projectService.userData.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.userData = res;
        if (
          this.userData.RoleId === 2
          || this.userData.RoleId === 1) {
          this.isAdmin = true;
        } else {
          this.isAdmin = false;
        }
      }
    });
  }

  ngOnInit(): void { /* */ }
}
