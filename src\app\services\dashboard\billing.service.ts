import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from '../api_base/api.service';

@Injectable({
  providedIn: 'root',
  })
export class BillingService {
  public constructor(private readonly api: ApiService) {
    // constructor
  }

  public getBilling(params): Observable<any> {
    return this.api.get(`billing/listBilling?ParentCompanyId=${params.ParentCompanyId}`);
  }

  public payOffline(params, payload): Observable<any> {
    return this.api.post(`billing/payOffline?ParentCompanyId=${params.ParentCompanyId}`, payload);
  }

  public payOnline(params, payload): Observable<any> {
    return this.api.post(`billing/payOnline?ParentCompanyId=${params.ParentCompanyId}`, payload);
  }
}
