import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DFOWComponent } from './dfow.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { Title } from '@angular/platform-browser';
import { DeliveryService } from '../services/profile/delivery.service';
import { ProjectService } from '../services/profile/project.service';
import { MixpanelService } from '../services/mixpanel.service';
import { of, throwError } from 'rxjs';
import { NgxFileDropEntry, FileSystemFileEntry } from 'ngx-file-drop';
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'paginate'
})
class MockPaginatePipe implements PipeTransform {
  transform(value: any[], config: any): any[] {
    return value;
  }
}

describe('DFOWComponent', () => {
  let component: DFOWComponent;
  let fixture: ComponentFixture<DFOWComponent>;
  let modalService: jest.Mocked<BsModalService>;
  let toastrService: jest.Mocked<ToastrService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let projectService: jest.Mocked<ProjectService>;
  let titleService: jest.Mocked<Title>;
  let mixpanelService: jest.Mocked<MixpanelService>;

  const mockModalRef = {
    hide: jest.fn(),
  };

  const mockDefinableResponse = {
    data: {
      rows: [],
      count: 0
    },
    lastId: {
      id: 1
    }
  };

  beforeEach(async () => {
    modalService = {
      show: jest.fn().mockReturnValue(mockModalRef),
    } as any;

    toastrService = {
      success: jest.fn(),
      error: jest.fn(),
    } as any;

    deliveryService = {
      loginUser: of({ id: 1, name: 'Test User' }),
      importDefinable: jest.fn(),
      getDefinable: jest.fn().mockReturnValue(of(mockDefinableResponse)),
      deleteDefinable: jest.fn(),
      exportDefinable: jest.fn(),
      saveAsExcelFile: jest.fn(),
      exportAsExcelFile: jest.fn(),
      updateDefinable: jest.fn(),
    } as any;

    projectService = {
      projectParent: of({ ProjectId: 123, ParentCompanyId: 456 }),
    } as any;

    titleService = {
      setTitle: jest.fn(),
    } as any;

    mixpanelService = {
      track: jest.fn(),
      addMixpanelEvents: jest.fn(),
    } as any;

    await TestBed.configureTestingModule({
      declarations: [DFOWComponent, MockPaginatePipe],
      providers: [
        { provide: BsModalService, useValue: modalService },
        { provide: ToastrService, useValue: toastrService },
        { provide: DeliveryService, useValue: deliveryService },
        { provide: ProjectService, useValue: projectService },
        { provide: Title, useValue: titleService },
        { provide: MixpanelService, useValue: mixpanelService },
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DFOWComponent);
    component = fixture.componentInstance;
    // Reset component state before each test
    component.defineList = [];
    component.addRecord = false;
    component.editRecord = false;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set title on initialization', () => {
    expect(titleService.setTitle).toHaveBeenCalledWith('Follo - Definable Feature of Work');
  });

  it('should initialize with default values', () => {
    expect(component.pageSize).toBe(25);
    expect(component.pageNo).toBe(1);
    expect(component.sort).toBe('ASC');
    expect(component.search).toBe('');
  });

  describe('Modal Operations', () => {
    it('should open modal', () => {
      const template = {} as any;
      component.openModal(template);
      expect(modalService.show).toHaveBeenCalledWith(template, {
        backdrop: 'static',
        class: 'modal-lg custom-modal',
      });
    });

    it('should close modal and reset form', () => {
      component.modalRef = mockModalRef as any;
      component.dfowFiles = [{ fileEntry: {} }] as any;
      const template = {} as any;

      component.close(template);
      expect(modalService.show).toHaveBeenCalled();
    });
  });

  describe('File Operations', () => {
    it('should handle valid file drop', () => {
      const mockFile = new File([''], 'test.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const mockFileEntry = {
        file: (callback: (file: File) => void) => callback(mockFile),
        isFile: true,
      } as FileSystemFileEntry;

      const mockDropEntry: NgxFileDropEntry = {
        fileEntry: mockFileEntry,
        relativePath: 'test.xlsx',
      };

      component.dfowImportFiledropped([mockDropEntry]);
      expect(component.dfowFiles.length).toBe(1);
    });

    it('should reject invalid file format', () => {
      const mockFileEntry = {
        file: (callback: (file: File) => void) => callback(new File([''], 'test.txt')),
        isFile: true,
      } as FileSystemFileEntry;

      const mockDropEntry: NgxFileDropEntry = {
        fileEntry: mockFileEntry,
        relativePath: 'test.txt',
      };

      component.dfowImportFiledropped([mockDropEntry]);
      expect(toastrService.error).toHaveBeenCalledWith(
        'Please select a valid file. Supported file format (.xlsx,.xls,.csv)',
        'OOPS!'
      );
    });
  });

  describe('Data Operations', () => {
    it('should add new row', () => {
      component.addRow();
      expect(component.defineList.length).toBe(1);
      expect(component.defineList[0].autoId).toBe(1);
    });

    it('should change page size and fetch data', () => {
      const getDefinableSpy = jest.spyOn(component, 'getDefinable');
      component.changePageSize(50);
      expect(component.pageSize).toBe(50);
      expect(component.pageNo).toBe(1);
      expect(getDefinableSpy).toHaveBeenCalled();
    });

    it('should handle search', () => {
      const getDefinableSpy = jest.spyOn(component, 'getDefinable');
      component.searchDFOW('test');
      expect(component.search).toBe('test');
      expect(getDefinableSpy).toHaveBeenCalled();
    });
  });

  describe('Service Interactions', () => {
    it('should fetch definable data', () => {
      // Reset component state
      component.defineList = [];
      component.addRecord = false;
      component.editRecord = false;

      // Mock the service response
      deliveryService.getDefinable.mockReturnValueOnce(of(mockDefinableResponse));

      component.getDefinable();
      expect(deliveryService.getDefinable).toHaveBeenCalled();
      expect(component.defineList).toEqual([]);
      expect(component.lastId).toBe(1);
    });

    it('should handle error in getDefinable', () => {
      deliveryService.getDefinable.mockReturnValueOnce(throwError(() => new Error('Test error')));

      component.getDefinable();
      expect(toastrService.error).toHaveBeenCalled();
    });

    it('should delete definable items', () => {
      // Setup
      component.deleteIndex = [1];
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
      component.selectAll = false;
      component.modalRef = mockModalRef as any;

      deliveryService.deleteDefinable.mockReturnValueOnce(of({ success: true, message: 'Success' }));

      // Execute
      component.deleteDefinable();

      // Verify
      expect(deliveryService.deleteDefinable).toHaveBeenCalled();
      expect(toastrService.success).toHaveBeenCalled();
    });

    it('should handle error when deleting definable items', () => {
      // Setup
      component.deleteIndex = [1];
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
      component.modalRef = mockModalRef as any;

      deliveryService.deleteDefinable.mockReturnValueOnce(throwError(() => ({ message: 'Error' })));

      // Execute
      component.deleteDefinable();

      // Verify
      expect(toastrService.error).toHaveBeenCalled();
    });

    it('should import data successfully', () => {
      // Setup
      component.formData = new FormData();
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
      component.modalRef = mockModalRef as any;
      component.dfowFiles = [new File([''], 'test.xlsx')] as any;

      deliveryService.importDefinable.mockReturnValueOnce(of({ message: 'Success' }));

      // Execute
      component.importData();

      // Verify
      expect(deliveryService.importDefinable).toHaveBeenCalled();
      expect(toastrService.success).toHaveBeenCalledWith('Success', 'SUCCESS!');
      expect(component.dfowFiles.length).toBe(0);
    });

    it('should handle error when importing data', () => {
      // Setup
      component.formData = new FormData();
      component.ProjectId = 123;
      component.ParentCompanyId = 456;

      deliveryService.importDefinable.mockReturnValueOnce(throwError(() => ({ message: 'Error' })));

      // Execute
      component.importData();

      // Verify
      expect(deliveryService.importDefinable).toHaveBeenCalled();
      expect(toastrService.error).toHaveBeenCalled();
    });

    it('should export DFOW data', () => {
      // Setup
      deliveryService.exportDefinable.mockReturnValueOnce(of({}));

      // Execute
      component.exportDFOW();

      // Verify
      expect(deliveryService.exportDefinable).toHaveBeenCalled();
      expect(deliveryService.saveAsExcelFile).toHaveBeenCalled();
    });

    it('should save changes for new records', () => {
      // Setup
      component.addRecord = true;
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
      component.defineList = [
        { autoId: 1, DFOW: 'New DFOW', Specification: 'New Spec', addRow: true }
      ];
      deliveryService.updateDefinable.mockReturnValueOnce(of({ success: true, message: 'Success' }));

      // Execute
      component.saveChanges();

      // Verify
      expect(deliveryService.updateDefinable).toHaveBeenCalled();
      expect(toastrService.success).toHaveBeenCalled();
    });

    it('should handle error when saving changes', () => {
      // Setup
      component.addRecord = true;
      component.ProjectId = 123;
      component.ParentCompanyId = 456;
      component.defineList = [
        { autoId: 1, DFOW: 'New DFOW', Specification: 'New Spec', addRow: true }
      ];
      deliveryService.updateDefinable.mockReturnValueOnce(throwError(() => ({ message: 'Error' })));

      // Execute
      component.saveChanges();

      // Verify
      expect(deliveryService.updateDefinable).toHaveBeenCalled();
      expect(toastrService.error).toHaveBeenCalled();
    });
  });

  describe('UI Interactions', () => {
    it('should reset changes', () => {
      // Setup
      component.pageNo = 5;
      component.search = 'test';
      const cancelChangesSpy = jest.spyOn(component, 'cancelChanges');
      const getDefinableSpy = jest.spyOn(component, 'getDefinable');

      // Execute
      component.resetChanges();

      // Verify
      expect(component.pageNo).toBe(1);
      expect(component.search).toBe('');
      expect(cancelChangesSpy).toHaveBeenCalled();
      expect(getDefinableSpy).toHaveBeenCalled();
    });

    it('should cancel changes', () => {
      // Setup
      component.addRecord = true;
      component.editRecord = true;
      component.duplicateData = [{ id: 1 }];

      // Execute
      component.cancelChanges();

      // Verify
      expect(component.addRecord).toBe(false);
      expect(component.editRecord).toBe(false);
      expect(component.duplicateData).toEqual([]);
    });

    it('should handle keyboard events for toggle', () => {
      const event = { key: 'Enter', preventDefault: jest.fn() } as any;
      const sortByFieldSpy = jest.spyOn(component, 'sortByField');

      component.handleToggleKeydown(event, 'field', 'ASC');
      expect(event.preventDefault).toHaveBeenCalled();
      expect(sortByFieldSpy).toHaveBeenCalledWith('field', 'ASC');
    });

    it('should handle keyboard events for delete action', () => {
      const event = { key: 'Enter', preventDefault: jest.fn() } as any;
      const openDeleteModalSpy = jest.spyOn(component, 'openDeleteModal');

      // Setup component state
      component.defineList = [{
        id: 1,
        DFOW: 'Test',
        Specification: 'Test Spec',
        addRow: false
      }];

      component.handleDownKeydown(event, 0, {}, 'delete');
      expect(event.preventDefault).toHaveBeenCalled();
      expect(openDeleteModalSpy).toHaveBeenCalled();
    });

    it('should handle keyboard events for remove action', () => {
      const event = { key: 'Enter', preventDefault: jest.fn() } as any;
      const removeFileSpy = jest.spyOn(component, 'removeFile');

      component.handleDownKeydown(event, 0, '', 'remove');
      expect(event.preventDefault).toHaveBeenCalled();
      expect(removeFileSpy).toHaveBeenCalledWith(0);
    });

    it('should handle keyboard events for clear action', () => {
      const event = { key: 'Enter', preventDefault: jest.fn() } as any;
      const clearSpy = jest.spyOn(component, 'clear');

      component.handleDownKeydown(event, 0, '', 'clear');
      expect(event.preventDefault).toHaveBeenCalled();
      expect(clearSpy).toHaveBeenCalled();
    });

    it('should handle keyboard events with space key', () => {
      const event = { key: ' ', preventDefault: jest.fn() } as any;
      const sortByFieldSpy = jest.spyOn(component, 'sortByField');

      component.handleToggleKeydown(event, 'field', 'ASC');
      expect(event.preventDefault).toHaveBeenCalled();
      expect(sortByFieldSpy).toHaveBeenCalledWith('field', 'ASC');
    });

    it('should not handle keyboard events for other keys', () => {
      const event = { key: 'Tab', preventDefault: jest.fn() } as any;
      const sortByFieldSpy = jest.spyOn(component, 'sortByField');

      component.handleToggleKeydown(event, 'field', 'ASC');
      expect(event.preventDefault).not.toHaveBeenCalled();
      expect(sortByFieldSpy).not.toHaveBeenCalled();
    });

    it('should handle default case in handleDownKeydown', () => {
      const event = { key: 'Enter', preventDefault: jest.fn() } as any;

      component.handleDownKeydown(event, 0, '', 'unknown');
      expect(event.preventDefault).toHaveBeenCalled();
    });

    it('should open delete modal', () => {
      // Setup
      const template = {} as any;
      component.defineList = [{ id: 1, DFOW: 'Test' }];

      // Execute
      component.openDeleteModal(0, template);

      // Verify
      expect(modalService.show).toHaveBeenCalled();
      expect(component.currentDeleteId).toBeDefined();
    });

    it('should set selected item', () => {
      // Setup
      component.defineList = [{ id: 1, DFOW: 'Test', isChecked: false }];

      // Execute
      component.setSelectedItem(0);

      // Verify
      expect(component.defineList[0].isChecked).toBe(true);
    });

    it('should toggle selected item when already checked', () => {
      // Setup
      component.defineList = [{ id: 1, DFOW: 'Test', isChecked: true }];

      // Execute
      component.setSelectedItem(0);

      // Verify
      expect(component.defineList[0].isChecked).toBe(false);
    });

    it('should clear search', () => {
      // Setup
      component.search = 'test';
      component.showSearchbar = true;
      const getDefinableSpy = jest.spyOn(component, 'getDefinable');

      // Execute
      component.clear();

      // Verify
      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
      expect(getDefinableSpy).toHaveBeenCalled();
    });

    it('should toggle input edit mode', () => {
      // Setup
      component.toggleInput = false;

      // Execute
      component.inputEdit();

      // Verify
      expect(component.toggleInput).toBe(true);
    });

    it('should toggle input edit mode from true to false', () => {
      // Setup
      component.toggleInput = true;

      // Execute
      component.inputEdit();

      // Verify
      expect(component.toggleInput).toBe(false);
    });

    it('should update content when entering text', () => {
      // Setup
      component.defineList = [{ DFOW: '' }];

      // Execute
      component.enterContent(0, 'New Content');

      // Verify
      expect(component.editRecord).toBe(true);
      expect(component.defineList[0].DFOW).toBe('New Content');
    });

    it('should update specification when entering text', () => {
      // Setup
      component.defineList = [{ Specification: '' }];

      // Execute
      component.enterSpecification(0, 'New Spec');

      // Verify
      expect(component.editRecord).toBe(true);
      expect(component.defineList[0].Specification).toBe('New Spec');
    });

    it('should sort by field', () => {
      const getDefinableSpy = jest.spyOn(component, 'getDefinable');
      component.sortByField('name', 'DESC');

      expect(component.sortColumn).toBe('name');
      expect(component.sort).toBe('DESC');
      expect(getDefinableSpy).toHaveBeenCalled();
    });

    it('should remove file from dfowFiles', () => {
      component.dfowFiles = [new File([''], 'test.xlsx')] as any;
      component.removeFile(0);

      expect(component.dfowFiles.length).toBe(0);
    });
  });

  describe('Additional Coverage Tests', () => {
    describe('Modal and Form Operations', () => {
      it('should close modal with confirmation when files exist', () => {
        component.dfowFiles = [new File([''], 'test.xlsx')] as any;
        const template = {} as any;

        component.close(template);

        expect(modalService.show).toHaveBeenCalledWith(template, {
          keyboard: false,
          class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
        });
      });

      it('should reset form directly when no files exist', () => {
        component.dfowFiles = [];
        const resetFormSpy = jest.spyOn(component, 'resetForm');
        const template = {} as any;

        component.close(template);

        expect(resetFormSpy).toHaveBeenCalledWith('yes');
      });

      it('should reset and close modal', () => {
        component.modalRef = mockModalRef as any;

        component.resetAndClose();

        expect(mockModalRef.hide).toHaveBeenCalled();
      });

      it('should reset form with "no" action', () => {
        component.modalRef1 = mockModalRef as any;

        component.resetForm('no');

        expect(mockModalRef.hide).toHaveBeenCalled();
      });

      it('should reset form with "yes" action', () => {
        component.modalRef = mockModalRef as any;
        component.modalRef1 = mockModalRef as any;
        component.dfowFiles = [new File([''], 'test.xlsx')] as any;

        component.resetForm('yes');

        expect(mockModalRef.hide).toHaveBeenCalledTimes(2);
        expect(component.dfowFiles).toEqual([]);
      });

      it('should reset form with "yes" action when modalRef1 is null', () => {
        component.modalRef = mockModalRef as any;
        component.modalRef1 = null;
        component.dfowFiles = [new File([''], 'test.xlsx')] as any;

        component.resetForm('yes');

        expect(mockModalRef.hide).toHaveBeenCalled();
        expect(component.dfowFiles).toEqual([]);
      });
    });

    describe('Pagination and Navigation', () => {
      it('should change page number and fetch data', () => {
        const getDefinableSpy = jest.spyOn(component, 'getDefinable');

        component.changePageNo(3);

        expect(component.pageNo).toBe(3);
        expect(getDefinableSpy).toHaveBeenCalled();
      });
    });

    describe('File Drop Operations', () => {
      it('should reject multiple files', () => {
        const mockFile1 = new File([''], 'test1.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const mockFile2 = new File([''], 'test2.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

        const mockFileEntry1 = {
          file: (callback: (file: File) => void) => callback(mockFile1),
          isFile: true,
        } as FileSystemFileEntry;

        const mockFileEntry2 = {
          file: (callback: (file: File) => void) => callback(mockFile2),
          isFile: true,
        } as FileSystemFileEntry;

        const mockDropEntries: NgxFileDropEntry[] = [
          { fileEntry: mockFileEntry1, relativePath: 'test1.xlsx' },
          { fileEntry: mockFileEntry2, relativePath: 'test2.xlsx' }
        ];

        component.dfowImportFiledropped(mockDropEntries);

        expect(toastrService.error).toHaveBeenCalledWith('Please import single file', 'OOPS!');
      });

      it('should handle .xls file format', () => {
        const mockFile = new File([''], 'test.xls', { type: 'application/vnd.ms-excel' });
        const mockFileEntry = {
          file: (callback: (file: File) => void) => callback(mockFile),
          isFile: true,
        } as FileSystemFileEntry;

        const mockDropEntry: NgxFileDropEntry = {
          fileEntry: mockFileEntry,
          relativePath: 'test.xls',
        };

        component.dfowImportFiledropped([mockDropEntry]);
        expect(component.dfowFiles.length).toBe(1);
      });

      it('should handle .csv file format', () => {
        const mockFile = new File([''], 'test.csv', { type: 'text/csv' });
        const mockFileEntry = {
          file: (callback: (file: File) => void) => callback(mockFile),
          isFile: true,
        } as FileSystemFileEntry;

        const mockDropEntry: NgxFileDropEntry = {
          fileEntry: mockFileEntry,
          relativePath: 'test.csv',
        };

        component.dfowImportFiledropped([mockDropEntry]);
        expect(component.dfowFiles.length).toBe(1);
      });

      it('should handle non-file entries', () => {
        const mockFileEntry = {
          isFile: false,
          isDirectory: true,
          file: jest.fn(),
          name: 'test-directory'
        } as unknown as FileSystemFileEntry;

        const mockDropEntry: NgxFileDropEntry = {
          fileEntry: mockFileEntry,
          relativePath: 'test.xlsx',
        };

        component.dfowImportFiledropped([mockDropEntry]);
        expect(component.dfowFiles.length).toBe(1);
      });
    });

    describe('Row Management', () => {
      it('should add row when defineList is not empty', () => {
        component.defineList = [
          { autoId: 5, DFOW: 'Existing', Specification: 'Existing Spec' }
        ];

        component.addRow();

        expect(component.addRecord).toBe(true);
        expect(component.defineList.length).toBe(2);
        expect(component.defineList[1].autoId).toBe(6);
        expect(component.defineList[1].addRow).toBe(true);
      });
    });

    describe('Search Operations', () => {
      it('should show searchbar when search data length > 0', () => {
        component.showSearchbar = false;
        const getDefinableSpy = jest.spyOn(component, 'getDefinable');

        component.searchDFOW('test search');

        expect(component.showSearchbar).toBe(true);
        expect(component.search).toBe('test search');
        expect(getDefinableSpy).toHaveBeenCalled();
      });

      it('should hide searchbar when search data is empty', () => {
        component.showSearchbar = true;
        const getDefinableSpy = jest.spyOn(component, 'getDefinable');

        component.searchDFOW('');

        expect(component.showSearchbar).toBe(false);
        expect(component.search).toBe('');
        expect(getDefinableSpy).toHaveBeenCalled();
      });
    });

    describe('Error Handling', () => {
      it('should handle getDefinable error with status code 400', () => {
        const errorResponse = {
          message: {
            statusCode: 400,
            details: [{ field: 'error message' }]
          }
        };
        const showErrorSpy = jest.spyOn(component, 'showError');
        deliveryService.getDefinable.mockReturnValueOnce(throwError(() => errorResponse));

        component.getDefinable();

        expect(showErrorSpy).toHaveBeenCalledWith(errorResponse);
        expect(component.loader).toBe(false);
      });

      it('should handle getDefinable error without message', () => {
        const errorResponse = {};
        deliveryService.getDefinable.mockReturnValueOnce(throwError(() => errorResponse));

        component.getDefinable();

        expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
        expect(component.loader).toBe(false);
      });

      it('should handle getDefinable error with message', () => {
        const errorResponse = { message: 'Custom error message' };
        deliveryService.getDefinable.mockReturnValueOnce(throwError(() => errorResponse));

        component.getDefinable();

        expect(toastrService.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
        expect(component.loader).toBe(false);
      });

      it('should handle importData error with status code 400', () => {
        const errorResponse = {
          message: {
            statusCode: 400,
            details: [{ field: 'error message' }]
          }
        };
        const showErrorSpy = jest.spyOn(component, 'showError');
        deliveryService.importDefinable.mockReturnValueOnce(throwError(() => errorResponse));

        component.importData();

        expect(showErrorSpy).toHaveBeenCalledWith(errorResponse);
        expect(component.importSubmitted).toBe(false);
      });

      it('should handle importData error without message', () => {
        const errorResponse = {};
        deliveryService.importDefinable.mockReturnValueOnce(throwError(() => errorResponse));

        component.importData();

        expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
        expect(component.importSubmitted).toBe(false);
      });

      it('should handle importData error with message', () => {
        const errorResponse = { message: 'Import failed' };
        deliveryService.importDefinable.mockReturnValueOnce(throwError(() => errorResponse));

        component.importData();

        expect(toastrService.error).toHaveBeenCalledWith('Import failed', 'OOPS!');
        expect(component.importSubmitted).toBe(false);
      });

      it('should show error with details', () => {
        const errorObj = {
          message: {
            details: [{ field1: 'Error message 1', field2: 'Error message 2' }]
          }
        };

        component.showError(errorObj);

        expect(component.submitted).toBe(false);
        expect(component.editSubmitted).toBe(false);
        expect(toastrService.error).toHaveBeenCalledWith(['Error message 1', 'Error message 2']);
      });
    });

    describe('Save Changes Operations', () => {
      it('should save changes for edit records', () => {
        component.addRecord = false;
        component.editRecord = true;
        component.ProjectId = 123;
        component.ParentCompanyId = 456;
        component.defineList = [
          { id: 1, DFOW: 'Updated DFOW', Specification: 'Updated Spec' }
        ];
        deliveryService.updateDefinable.mockReturnValueOnce(of({ success: true, message: 'Updated successfully' }));

        component.saveChanges();

        expect(deliveryService.updateDefinable).toHaveBeenCalledWith(
          { ProjectId: 123 },
          { editData: component.defineList, ParentCompanyId: 456 }
        );
        expect(toastrService.success).toHaveBeenCalledWith('Updated successfully', 'Success');
        expect(component.saveSubmitted).toBe(false);
        expect(component.addRecord).toBe(false);
        expect(component.editRecord).toBe(false);
      });

      it('should handle saveChanges error with status code 400', () => {
        const errorResponse = {
          message: {
            statusCode: 400,
            details: [{ field: 'error message' }]
          }
        };
        const showErrorSpy = jest.spyOn(component, 'showError');
        component.addRecord = true;
        deliveryService.updateDefinable.mockReturnValueOnce(throwError(() => errorResponse));

        component.saveChanges();

        expect(showErrorSpy).toHaveBeenCalledWith(errorResponse);
        expect(component.saveSubmitted).toBe(false);
      });

      it('should handle saveChanges error without message', () => {
        const errorResponse = {};
        component.addRecord = true;
        deliveryService.updateDefinable.mockReturnValueOnce(throwError(() => errorResponse));

        component.saveChanges();

        expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
        expect(component.saveSubmitted).toBe(false);
      });

      it('should handle saveChanges error with duplicate data', () => {
        const errorResponse = {
          message: 'Duplicate data found',
          data: [{ id: 1, DFOW: 'Duplicate' }]
        };
        component.addRecord = true;
        deliveryService.updateDefinable.mockReturnValueOnce(throwError(() => errorResponse));

        component.saveChanges();

        expect(component.duplicateData).toEqual([{ id: 1, DFOW: 'Duplicate' }]);
        expect(toastrService.error).toHaveBeenCalledWith('Duplicate data found', 'OOPS!');
        expect(component.saveSubmitted).toBe(false);
      });

      it('should handle saveChanges error with message only', () => {
        const errorResponse = { message: 'Save failed' };
        component.addRecord = true;
        deliveryService.updateDefinable.mockReturnValueOnce(throwError(() => errorResponse));

        component.saveChanges();

        expect(toastrService.error).toHaveBeenCalledWith('Save failed', 'OOPS!');
        expect(component.saveSubmitted).toBe(false);
      });
    });

    describe('Delete Operations', () => {
      it('should open delete modal for existing row', () => {
        const template = {} as any;
        component.defineList = [
          { id: 1, DFOW: 'Test', addRow: false }
        ];

        component.openDeleteModal(0, template);

        expect(component.deleteIndex[0]).toBe(1);
        expect(component.currentDeleteId).toBe(0);
        expect(component.remove).toBe(false);
        expect(modalService.show).toHaveBeenCalledWith(template, {
          backdrop: 'static',
          class: 'modal-lg custom-modal',
        });
      });

      it('should remove row directly for new rows', () => {
        component.defineList = [
          { id: 1, DFOW: 'Test', addRow: true }
        ];

        component.openDeleteModal(0, {} as any);

        expect(component.defineList.length).toBe(0);
      });

      it('should open delete modal for bulk delete', () => {
        const template = {} as any;

        component.openDeleteModal(-1, template);

        expect(component.remove).toBe(true);
        expect(modalService.show).toHaveBeenCalled();
      });

      it('should handle deleteDefinable error with status code 400', () => {
        const errorResponse = {
          message: {
            statusCode: 400,
            details: [{ field: 'error message' }]
          }
        };
        const showErrorSpy = jest.spyOn(component, 'showError');
        deliveryService.deleteDefinable.mockReturnValueOnce(throwError(() => errorResponse));

        component.deleteDefinable();

        expect(showErrorSpy).toHaveBeenCalledWith(errorResponse);
        expect(component.deleteSubmitted).toBe(false);
      });

      it('should handle deleteDefinable error without message', () => {
        const errorResponse = {};
        deliveryService.deleteDefinable.mockReturnValueOnce(throwError(() => errorResponse));

        component.deleteDefinable();

        expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
        expect(component.deleteSubmitted).toBe(false);
      });

      it('should handle deleteDefinable error with message', () => {
        const errorResponse = { message: 'Delete failed' };
        deliveryService.deleteDefinable.mockReturnValueOnce(throwError(() => errorResponse));

        component.deleteDefinable();

        expect(toastrService.error).toHaveBeenCalledWith('Delete failed', 'OOPS!');
        expect(component.deleteSubmitted).toBe(false);
      });
    });

    describe('Selection Operations', () => {
      it('should check if selected row exists when selectAll is true', () => {
        component.selectAll = true;

        const result = component.checkSelectedRow();

        expect(result).toBe(false);
      });

      it('should check if selected row exists when no items are checked', () => {
        component.selectAll = false;
        component.defineList = [
          { isChecked: false },
          { isChecked: false }
        ];

        const result = component.checkSelectedRow();

        expect(result).toBe(true);
      });

      it('should check if selected row exists when some items are checked', () => {
        component.selectAll = false;
        component.defineList = [
          { isChecked: true },
          { isChecked: false }
        ];

        const result = component.checkSelectedRow();

        expect(result).toBe(false);
      });

      it('should remove items when selectAll is true', () => {
        component.selectAll = true;
        const deleteDefinableSpy = jest.spyOn(component, 'deleteDefinable');

        component.removeItem();

        expect(component.deleteSubmitted).toBe(true);
        expect(deleteDefinableSpy).toHaveBeenCalled();
      });

      it('should remove selected items when selectAll is false', () => {
        component.selectAll = false;
        component.defineList = [
          { isChecked: true, id: 1 },
          { isChecked: false, id: 2 },
          { isChecked: true, id: 3 }
        ];
        const deleteDefinableSpy = jest.spyOn(component, 'deleteDefinable');

        component.removeItem();

        expect(component.deleteIndex).toEqual([1, 3]);
        expect(deleteDefinableSpy).toHaveBeenCalled();
      });

      it('should select all DFOW data', () => {
        component.selectAll = false;
        component.defineList = [
          { isChecked: false },
          { isChecked: false }
        ];

        component.selectAllDFOWData();

        expect(component.selectAll).toBe(true);
        expect(component.defineList[0].isChecked).toBe(true);
        expect(component.defineList[1].isChecked).toBe(true);
      });

      it('should deselect all DFOW data', () => {
        component.selectAll = true;
        component.defineList = [
          { isChecked: true },
          { isChecked: true }
        ];

        component.selectAllDFOWData();

        expect(component.selectAll).toBe(false);
        expect(component.defineList[0].isChecked).toBe(false);
        expect(component.defineList[1].isChecked).toBe(false);
      });
    });

    describe('Utility Functions', () => {
      it('should check for duplicate data - found', () => {
        component.duplicateData = [
          { id: 1 },
          { id: 2 }
        ];

        const result = component.getDuplicate(1);

        expect(result).toBe(true);
      });

      it('should check for duplicate data - not found', () => {
        component.duplicateData = [
          { id: 1 },
          { id: 2 }
        ];

        const result = component.getDuplicate(3);

        expect(result).toBe(false);
      });

      it('should download template', () => {
        const exportExcelSpy = jest.spyOn(component, 'exportExcel');

        component.download();

        expect(exportExcelSpy).toHaveBeenCalledWith([
          { id: 1, Specification: '12 23 45', DFOW: 'First' },
          { id: 2, Specification: '56 45 89', DFOW: 'Second' },
          { id: 3, Specification: '58 44 75', DFOW: 'Third' }
        ]);
      });

      it('should export excel', () => {
        const testData = [{ id: 1, name: 'test' }];

        component.exportExcel(testData);

        expect(component.export).toBe(0);
        expect(deliveryService.exportAsExcelFile).toHaveBeenCalledWith(testData, 'DFOW_Import_Template');
      });
    });

    describe('Data Processing', () => {
      it('should handle getDefinable with selectAll true and data', () => {
        component.selectAll = true;
        const mockResponse = {
          data: {
            rows: [
              { id: 1, DFOW: 'Test1' },
              { id: 2, DFOW: 'Test2' }
            ],
            count: 2
          },
          lastId: { id: 5 }
        };
        deliveryService.getDefinable.mockReturnValueOnce(of(mockResponse));

        component.getDefinable();

        expect(component.defineList[0].isChecked).toBe(true);
        expect(component.defineList[1].isChecked).toBe(true);
        expect(component.totalCount).toBe(2);
        expect(component.lastId).toBe(5);
        expect(component.loader).toBe(false);
      });

      it('should handle getDefinable with selectAll false', () => {
        component.selectAll = false;
        const mockResponse = {
          data: {
            rows: [
              { id: 1, DFOW: 'Test1' },
              { id: 2, DFOW: 'Test2' }
            ],
            count: 2
          },
          lastId: { id: 5 }
        };
        deliveryService.getDefinable.mockReturnValueOnce(of(mockResponse));

        component.getDefinable();

        expect(component.selectAll).toBe(false);
        expect(component.defineList[0].isChecked).toBe(false);
        expect(component.defineList[1].isChecked).toBe(false);
      });

      it('should handle getDefinable with empty data and selectAll true', () => {
        component.selectAll = true;
        const mockResponse = {
          data: {
            rows: [],
            count: 0
          },
          lastId: { id: 0 }
        };
        deliveryService.getDefinable.mockReturnValueOnce(of(mockResponse));

        component.getDefinable();

        expect(component.selectAll).toBe(false);
        expect(component.defineList).toEqual([]);
      });
    });
  });
});
