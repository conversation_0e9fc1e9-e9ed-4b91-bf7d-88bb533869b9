import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { Router } from '@angular/router';
import { ApiService } from '../api_base/api.service';
import { countryCodes } from '../countryCodes';

@Injectable({
  providedIn: 'root',
  })
export class AuthService {
  public constructor(private readonly api: ApiService,
    public router: Router) {
    // constructor
  }

  public login(payload): Observable<any> {
    return this.api.post('auth/login', payload);
  }

  public existEmail(payload): Observable<any> {
    return this.api.post('auth/exist_user', payload);
  }

  public removeAlreadyInvitedMember(payload): Observable<any> {
    return this.api.post('auth/remove_invited_member', payload);
  }

  public requestInvitedLink(payload): Observable<any> {
    return this.api.post('auth/request_invited_link', payload);
  }

  public checkDialCode(dialCode): string {
    let value = '';
    Object.keys(countryCodes).forEach((key): void => {
      if (key === dialCode) {
        value = countryCodes[key];
      }
    });
    return value;
  }

  public logout(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('newproject');
    localStorage.removeItem('interval');
    localStorage.removeItem('planid');
    localStorage.removeItem('ProjectId');
    localStorage.removeItem('currentCompanyId');
    localStorage.removeItem('newproject');
    window.location.reload();
    this.router.navigate(['/login']);
  }

  public contentLogout(): void {
    localStorage.removeItem('token');
    localStorage.removeItem('newproject');
    localStorage.removeItem('interval');
    localStorage.removeItem('planid');
    localStorage.removeItem('ProjectId');
    localStorage.removeItem('currentCompanyId');
    localStorage.removeItem('newproject');
  }


  public register(data): Observable<any> {
    const payload = data;
    payload.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    return this.api.post('auth/register', payload);
  }

  public getUserDetail(payload): Observable<any> {
    return this.api.post('member/get_user_detail', payload);
  }

  public loggeduserIn(): boolean {
    return !!localStorage.getItem('token');
  }

  public checkAuthentication(): void {
    if (this.loggeduserIn()) {
      this.router.navigate(['/calendar']);
    }
  }

  public checkToken(params): Observable<any> {
    return this.api.get(`auth/check_reset_token/${params}`);
  }

  public getCountry(): Observable<any> {
    return this.api.get('address/get_country');
  }

  public getState(params): Observable<any> {
    return this.api.get(`address/get_state/${params}`);
  }

  public getPlans(params): Observable<any> {
    return this.api.get(`payment/list_all_plans/${params}`);
  }

  public getUpgradePlans(params): Observable<any> {
    return this.api.get(`payment/list_upgrade_plans/${params}`);
  }

  public getCity(params): Observable<any> {
    return this.api.get(`address/get_city/${params}`);
  }

  public forgotPassword(payload): Observable<any> {
    return this.api.post('auth/forgot_password', payload);
  }

  public changePassword(payload): Observable<any> {
    return this.api.post('user/change_password', payload);
  }

  public resetPassword(payload, params): Observable<any> {
    return this.api.post(`auth/reset_password_email/${params}`, payload);
  }

  public getUser(): Observable<any> {
    return this.api.get('user/authenticated_user');
  }
}
