<div class="modal-header">
  <div class="form-group d-flex align-items-center">
    <img *ngIf="currentPage === 'allCalendar'" src="./assets/images/noun_request.svg" alt="inspection" class="me-2" />
    <div *ngIf="currentPage === 'allCalendar'">
      <select
        class="styled-select"
        [(ngModel)]="selectedType"
        (change)="selectedBookingtype($event.target.value)"
      >
        <option class="styled-option" *ngFor="let item of bookingTypesList" [value]="item">
          {{ item }}
        </option>
      </select>
    </div>
  </div>
  <h1 *ngIf="currentPage !== 'allCalendar'" class="styled-h1">
    <img src="./assets/images/noun_request.svg" alt="inspection" class="me-2" />New Crane
    Booking
  </h1>
  <div class="d-flex justify-content-between align-items-center ms-auto">
    <button type="button" class="close" aria-label="Close" (click)="close(cancelConfirmation)">
      <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close" /></span>
    </button>
  </div>
</div>
<div class="modal-body newupdate-alignments taginput--heightfix craneedit" *ngIf="!modalLoader">
  <div class="addcalendar-details">
    <form
      name="form"
      class="custom-material-form add-concrete-material-form"
      id="deliverydetails-form3"
      [formGroup]="craneRequest"
      (ngSubmit)="onSubmit()"
      novalidate
    >
      <div class="row">
        <div class="col-md-6">
          <div class="form-group mb-0">
            <label class="fs12 fw600" for="description">Description<sup>*</sup></label>
            <textarea  id="description"
              class="form-control fs11 radius0"
              placeholder="Enter Description"
              rows="2"
              formControlName="description"
              maxlength="150"
            ></textarea>
            <div class="color-red" *ngIf="submitted && craneRequest.get('description').errors">
              <small *ngIf="craneRequest.get('description').errors.required"
                >*Description is Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6" *ngIf="lastId">
          <div class="form-group mb-0">
            <label class="fs12 fw600"  for="craneid">Crane Pick ID</label>
            <input   id="craneid"
              type="text"
              class="form-control fs10 color-orange fw500 material-input ps-3"
              placeholder=""
              value="{{ lastId }}"
              disabled="disabled"
            />
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <div class="form-group mb-0 company-select" id="company-select4">
            <label class="fs12 fw600"   for="rescomp">Responsible Company<sup>*</sup></label>
            <ng-multiselect-dropdown  id="rescomp"
              [placeholder]="'Responsible Company*'"
              [settings]="newNdrCompanyDropdownSettings"
              [data]="companyList"
              formControlName="companies"
              [(ngModel)]="responsibleCompanySelectedItems"
            >
            </ng-multiselect-dropdown>
            <div class="color-red" *ngIf="submitted && craneRequest.get('companies').errors">
              <small *ngIf="craneRequest.get('companies').errors.required"
                >*Company is Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6 mt-1">
          <div class="form-group mb-0 company-select" id="company-select8">
            <label class="fs12 fw600"  for="defwork">Definable Feature Of Work</label>
            <ng-multiselect-dropdown  id="defwork"
              [placeholder]="'Definable Feature Of Work (Scope)'"
              [settings]="newNdrDefinableDropdownSettings"
              [data]="definableFeatureOfWorkList"
              formControlName="definableFeatureOfWorks"
            >
            </ng-multiselect-dropdown>
            <div
              class="color-red"
              *ngIf="submitted && craneRequest.get('definableFeatureOfWorks').errors"
            >
              <small *ngIf="craneRequest.get('definableFeatureOfWorks').errors.required"
                >*Definable Feature Of Work is Required.
              </small>
            </div>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-12">
          <div class="form-group taginput-height mt-4 mb-0">
            <label class="fs12 fw600" for="resperson">Responsible Person<sup>*</sup></label>
            <ul
              class="follo-switch list-group list-group-horizontal justify-content-end float-end newfixswitch"
              id="switch-control4"
            >
              <li class="fs12 list-group-item border-0 pe-3 escort--text py-0">Escort Needed?</li>
              <li class="list-group-item border-0 px-0 py-0">
                <ui-switch
                  switchColor="#fff"
                  defaultBoColor="#CECECE"
                  defaultBgColor="#CECECE"
                  formControlName="isEscortNeeded"
                >
                </ui-switch>
              </li>
            </ul>

            <div class="w-100 float-start mt-3">
              <tag-input
                formControlName="responsiblePersons"
                [onlyFromAutocomplete]="true"
                [placeholder]="' '"
                [onTextChangeDebounce]="500"
                class="tag-layout newdelivery-taglayout w-100 crane-tag-layout"
                [identifyBy]="'id'"
                [displayBy]="'email'"
              >
                <tag-input-dropdown
                  [showDropdownIfEmpty]="false"
                  [displayBy]="'email'"
                  [identifyBy]="'id'"
                  [autocompleteObservable]="requestAutocompleteItems"
                  [appendToBody]="false"
                >
                  <ng-template let-item="item" let-index="index">
                    <span class="fs10">{{ item.email }}</span>
                  </ng-template>
                </tag-input-dropdown>
              </tag-input>
            </div>
            <div
              class="color-red"
              *ngIf="submitted && craneRequest.get('responsiblePersons').errors"
            >
              <small *ngIf="craneRequest.get('responsiblePersons').errors.required"
                >*Please choose Responsible person.</small
              >
            </div>
          </div>

        </div>

      </div>

      <div class="row">
        <div class="col-md-6 mt-2 primary-tooltip">
          <div class="form-group mb-0 timezone-formgroup">
            <label class="fs12 fw600"  for="location"
              >Location
              <span class="color-red"><sup>*</sup></span>
              <div class="dot-border-info location-border-info tooltip location-tooltip">
                <span class="fw700 info-icon fs12">i</span>
                <span class="tooltiptext tooltiptext-info"
                  >Where will the materials/equipment be installed</span
                >
                <div class="arrow-down"></div>
              </div>
            </label>
            <ng-multiselect-dropdown  id="location"
              [placeholder]="'Choose Location'"
              [settings]="locationDropdownSettings"
              [data]="locationList"
              (onSelect)="locationSelected($event)"
              formControlName="LocationId"
              [(ngModel)]="defaultLocationValue"
            >
            </ng-multiselect-dropdown>
            <div class="color-red" *ngIf="submitted && craneRequest.get('LocationId').errors">
              <small *ngIf="craneRequest.get('LocationId').errors.required"
                >*Location is Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label class="fs12 fw600 mt-2"  for="gate">Gate<sup>*</sup></label>
            <select class="form-control fs12 material-input px-0 mt-1" formControlName="GateId" (change)="getBookingData()"  id="gate">
              <option value="" disabled selected hidden>Gate<sup>*</sup></option>
              <option *ngFor="let item of gateList" value="{{ item?.id }}">
                {{ item.gateName }}
              </option>
            </select>
            <div class="color-red" *ngIf="submitted && craneRequest.get('GateId').errors">
              <small *ngIf="craneRequest.get('GateId').errors.required">*Gate is Required.</small>
            </div>
          </div>
        </div>

      </div>

      <div class="row mt-3">
        <div class="col-md-6 mt-2">
          <div class="form-group mb-0 company-select equipment-select">
            <label class="fs12 fw600 mb-3"  for="equipment">Equipment<sup>*</sup></label>
            <ng-multiselect-dropdown id="equipment"
            [placeholder]="'Equipment'"
            [settings]="equipmentDropdownSettings"
            [data]="equipmentList"
            formControlName="EquipmentId"
            (ngModelChange)="getSlots()"
            >
          </ng-multiselect-dropdown>
            <div class="color-red" *ngIf="submitted && craneRequest.get('EquipmentId').errors">
              <small *ngIf="craneRequest.get('EquipmentId').errors.required"
                >*Equipment is Required.</small
              >
            </div>
          </div>
        </div>

        <div class="col-md-6 mt-2">
          <div class="row mt-2">
            <div class="col-md-6 mt-2">
              <div class="form-group mb-0">
                <label class="fs12 fw600" for="pickfrom"
                  >Picking From<span class="color-red"><sup>*</sup></span></label
                >
                <textarea  id="pickfrom"
                  class="form-control fs11 radius0"
                  placeholder="Picking From"
                  rows="2"
                  formControlName="pickUpLocation"
                  maxlength="150"
                ></textarea>
                <div class="color-red" *ngIf="submitted && craneRequest.get('pickUpLocation').errors">
                  <small *ngIf="craneRequest.get('pickUpLocation').errors.required"
                    >*Picking From is Required.</small
                  >
                </div>
              </div>
            </div>
            <div class="col-md-6 mt-2">
              <div class="form-group mb-0">
                <label class="fs12 fw600"  for="pickto"
                  >Picking To<span class="color-red"><sup>*</sup></span></label
                >
                <textarea  id="pickto"
                  class="form-control fs11 radius0"
                  placeholder="Picking To"
                  rows="2"
                  formControlName="dropOffLocation"
                  maxlength="150"
                ></textarea>
                <div class="color-red" *ngIf="submitted && craneRequest.get('dropOffLocation').errors">
                  <small *ngIf="craneRequest.get('dropOffLocation').errors.required"
                    >*Picking To is Required.</small
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="row mt-3">
        <div
        class="color-red"
        *ngIf="submitted && !isTimeSlotChoosen"
      >
        <small *ngIf="submitted && !isTimeSlotChoosen"
          >*Please select a time slot..</small
        >
      </div>
        <div class="col-md-12">
          <app-time-slot
            #timeSlotRef
            (selectTime)="selectTime($event.start, $event.end)"
            [selectedBookingDate]="craneRequest.get('craneDeliveryStart').value"
            [timeZone]="timeZone"
            [selectedBookingType]="'craneRequest'"
            [equipmentId] = "craneRequest.get('EquipmentId').value"
            [LocationId] = "craneRequest.get('LocationId').value"
          >
          </app-time-slot>
        </div>
      </div>



      <div class="row">
           <!-- Recurrance code start -->
        <div class="col-md-6">
          <div class="float-start w-100 mt-3">
            <label class="fs12 fw600"  for="recurrence">Recurrence<sup>*</sup></label>
            <select  id="recurrence"
              class="form-control fs12 material-input px-0"
              formControlName="recurrence"
              (change)="onRecurrenceSelect($event.target.value)"
            >
              <option value="" disabled selected hidden>Select Recurrence</option>
              <option *ngFor="let type of recurrence" value="{{ type.value }}">
                {{ type.value }}
              </option>
            </select>
          </div>
          <div
            class="col-md-12 p-0 float-start mt-2"
            *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
          >
            <div
              class="col-md-12 p-0 mt-md-0"
              *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
            >
              <label class="fs12 fw600"  for="repevery">Repeat Every</label>
            </div>
            <div
              class="col-md-6 ps-0 float-start mt-md-0"
              *ngIf="selectedRecurrence !== 'Does Not Repeat' && selectedRecurrence !== 'Yearly'"
            >
              <div class="form-group mb-0">
                <input  id="repevery"
                  type="text"
                  formControlName="repeatEveryCount"
                  class="form-control fs12 material-input p-0"
                  (input)="changeRecurrenceCount($event.target.value)"
                  min="1"
                />
              </div>
            </div>
            <div class="col-md-6 p-0 float-start mt-md-0" *ngIf="isRepeatWithSingleRecurrence">
              <div class="form-group mb-0">
                <select
                  class="form-control fs12 material-input px-2"
                  formControlName="repeatEveryType"
                  (change)="chooseRepeatEveryType($event.target.value)"
                >
                  <option value="" disabled selected hidden>Select Recurrence</option>
                  <option *ngFor="let type of repeatWithSingleRecurrence" value="{{ type.value }}">
                    {{ type.value }}
                  </option>
                </select>
              </div>
            </div>
            <div
              class="col-md-6 p-0 float-start mt-md-0"
              *ngIf="isRepeatWithMultipleRecurrence || showRecurrenceTypeDropdown"
            >
              <div class="form-group">
                <select
                  class="form-control fs12 material-input px-2"
                  formControlName="repeatEveryType"
                  (change)="chooseRepeatEveryType($event.target.value)"
                >
                  <option value="" disabled selected hidden>Select Recurrence</option>
                  <option
                    *ngFor="let type of repeatWithMultipleRecurrence"
                    value="{{ type.value }}"
                  >
                    {{ type.value }}
                  </option>
                </select>
              </div>
            </div>
          </div>
          <div class="row addcalendar-displaydays">
            <div
              class="col-md-12 pt-0"
              *ngIf="
                (selectedRecurrence === 'Weekly' ||
                  isRepeatWithMultipleRecurrence ||
                  isRepeatWithSingleRecurrence) &&
                selectedRecurrence !== 'Monthly' &&
                selectedRecurrence !== 'Yearly'
              "
            >
              <ul class="displaylists ps-0">
                <li *ngFor="let item of weekDays; let i = index" class="fs12 list-inline-item">
                  <input
                    type="checkbox"
                    [disabled]="item.isDisabled"
                    [value]="item.value"
                    class="d-none"
                    id="days-{{ i }}"
                    (change)="onChange($event)"
                    [checked]="item.checked"
                  />
                  <label for="days-{{ i }}">{{ item.display }}</label>
                </li>
                <div class="color-red" *ngIf="submitted && craneRequest.controls['days'].errors">
                  <small *ngIf="craneRequest.controls['days'].errors.required">*Required </small>
                </div>
              </ul>
            </div>
          </div>
          <div
            class="row"
            *ngIf="selectedRecurrence === 'Monthly' || selectedRecurrence === 'Yearly'"
          >
            <div class="col-md-12 float-start mt-md-0">
              <div class="w-100 float-start mt-1">
                <div class="form-check">
                  <input
                    class="form-check-input c-pointer"
                    type="radio"
                    formControlName="chosenDateOfMonth"
                    id="flexRadioDefault1"
                    [value]="1"
                    (change)="changeMonthlyRecurrence()"
                  />
                  <label class="form-check-label fs12 color-orange" for="flexRadioDefault1">
                    On day {{ monthlyDate }}
                  </label>
                </div>
                <div class="form-check">
                  <input
                    class="form-check-input c-pointer"
                    type="radio"
                    formControlName="chosenDateOfMonth"
                    id="flexRadioDefault2"
                    [value]="2"
                    (change)="changeMonthlyRecurrence()"
                  />
                  <label class="form-check-label fs12 color-orange" for="flexRadioDefault2">
                    On the {{ monthlyDayOfWeek }}
                    <p *ngIf="selectedRecurrence === 'Yearly'">
                      of
                      {{ craneRequest.get('deliveryDate').value | date : 'LLLL' }}
                    </p>
                  </label>
                </div>
                <div class="form-check" *ngIf="enableOption">
                  <input
                    class="form-check-input c-pointer"
                    type="radio"
                    formControlName="chosenDateOfMonth"
                    id="flexRadioDefault3"
                    [value]="3"
                    (change)="changeMonthlyRecurrence()"
                  />
                  <label class="form-check-label fs12 color-orange" for="flexRadioDefault3">
                    On the
                    {{ monthlyLastDayOfWeek }}
                    <p *ngIf="selectedRecurrence === 'Yearly'">
                      of
                      {{ craneRequest.get('deliveryDate').value | date : 'LLLL' }}
                    </p>
                  </label>
                </div>
              </div>

              <div>
                <div
                  class="color-red"
                  *ngIf="
                    submitted &&
                    (craneRequest.get('monthlyRepeatType')?.errors ||
                      craneRequest.get('dateOfMonth')?.errors)
                  "
                >
                  <small *ngIf="craneRequest.get('monthlyRepeatType')?.errors?.required"
                    >*required</small
                  >
                  <small *ngIf="craneRequest.get('dateOfMonth')?.errors?.required">*required</small>
                </div>
              </div>
            </div>
          </div>
          <div class="row addcalendar-displaydays messagetext">
            <div class="col-md-6 mt-md-0 pb-0" *ngIf="message">
              <p class="fs12 color-grey11">
                <span class="color-red fw-bold">*</span>
                {{ message }}
              </p>
            </div>
          </div>

        </div>
        <div
        class="col-md-6 mt-3"
        *ngIf="
          selectedRecurrence === 'Daily' ||
          selectedRecurrence === 'Monthly' ||
          selectedRecurrence === 'Yearly' ||
          selectedRecurrence === 'Weekly'
        "
      >
          <label class="fs12 fw600"  for="endate">End Date<sup>*</sup></label>
          <div class="input-group">
            <input  id="enddate"
              class="form-control fs12 fw500 material-input"
              #dp="bsDatepicker"
              bsDatepicker
              formControlName="endDate"
              [minDate]="recurrenceMinDate"
              placement="top"
              placeholder="End Date *"
              [bsConfig]="{
                isAnimated: true,
                showWeekNumbers: false,
                customTodayClass: 'today'
              }"
              (ngModelChange)="showMonthlyRecurrence()"
            />
              <span class="input-group-text">
                <img
                  src="./assets/images/date.svg"
                  class="h-12px"
                  alt="Date"
                  (click)="dp.toggle()" (keydown)="dp.toggle()"
                  [attr.aria-expanded]="dp.isOpen"
                />
              </span>
        </div>
      </div>
          <!-- Recurrance code End -->
        <div class="col-md-12 mt-2">
          <div class="form-group mb-0">
            <label class="fs12 fw600" for="addnotes">Additional Notes</label>
            <textarea  id="addnotes"
              class="form-control fs12 min-h-65px"
              placeholder=""
              rows="2"
              formControlName="additionalNotes"
            ></textarea>
            <div class="color-red" *ngIf="submitted && craneRequest.get('additionalNotes').errors">
              <small *ngIf="craneRequest.get('additionalNotes').errors.required"
                >*Notes is Required.</small
              >
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-12 mt-2">
          <p class="fs11 color-grey17 fw500">
            The crane delivery needs to be approved by Project Administrator to be added to the
            calendar. Please click on submit to send the booking for approval.
          </p>
        </div>
      </div>
      <div class="my-1 text-center">
        <button
          class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3 px-2rem"
          type="button"
          (click)="close(cancelConfirmation)"
        >
          Cancel
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem"
          type="submit"
          [disabled]="formSubmitted && craneRequest.valid"
        >
          <em
            class="fa fa-spinner"
            aria-hidden="true"
            *ngIf="formSubmitted && craneRequest.valid"
          ></em
          >Submit
        </button>
      </div>
    </form>
  </div>
</div>
<div class="modal-body text-center" *ngIf="modalLoader">Loading...</div>

<!--Confirmation Popup-->
<div id="confirm-popup7">
  <ng-template #cancelConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure you want to cancel?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetForm('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="resetForm('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
