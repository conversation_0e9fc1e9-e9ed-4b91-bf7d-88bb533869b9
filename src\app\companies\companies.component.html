<section class="page-section">
  <div class="page-inner-content">
    <div class="top-header my-3">
      <div class="row pt-md-15px">
        <div class="col-md-8">
          <div class="top-btn" *ngIf="isAdmin">
            <ul class="list-group list-group-horizontal">
              <li class="list-group-item p0 border-0 bg-transparent me-4">
                <button
                  (click)="setDefinable(newMemeber)"
                  class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular"
                >
                  New Company
                </button>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent me-4">
                <button
                  class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular px-3"
                  (click)="openImportModal(import)"
                >
                  Import Multiple
                </button>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent">
                <button
                  class="btn btn-white fs12 color-orange radius5 fw-bold cairo-regular"
                  (click)="openDeleteModal(-1, deleteList)"
                  [disabled]="checkSelectedRow()"
                >
                  Remove
                </button>
              </li>
            </ul>
          </div>
        </div>
        <div class="col-md-4">
          <div class="top-filter">
            <ul class="list-group list-group-horizontal justify-content-end">
              <li class="list-group-item p0 border-0 bg-transparent me-2">
                <div class="search-icon">
                  <input
                    class="form-control fs12 color-grey8"
                    [ngClass]="showSearchbar ? 'input-hover-disable' : 'input-search'"
                    placeholder="What are you looking for?"
                    (input)="getSearchCompany($event.target.value)"
                    [(ngModel)]="search"
                  />
                  <div class="icon">
                    <img
                      src="./assets/images/cross-close.svg"
                      *ngIf="showSearchbar"
                      (click)="clear()" (keydown)="handleDownKeydown($event, '', '','clear')"
                      alt="close-cross"
                    />
                    <em class="fa fa-search fs12 color-grey8" *ngIf="!showSearchbar"></em>
                  </div>
                </div>
              </li>
              <li class="list-group-item p0 border-0 bg-transparent me-2 position-relative">
                <div class="filter-icon" (click)="openModal1(filter)" (keydown)="handleDownKeydown($event,filter, '','filter')">
                  <img src="./assets/images/filter.svg" class="h-12px icon" alt="Filter" />
                </div>
                <div
                  class="bg-orange rounded-circle position-absolute text-white filter-count"
                  *ngIf="filterCount > 0"
                >
                  <p class="m-0 text-center fs10">{{ filterCount }}</p>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div class="page-card bg-white rounded">
      <div class="table-responsive rounded tab-grid companies-tab">
        <span
          class="float-end fs12 me-3 p2 color-orange fw-bold"
          *ngIf="bulkCompanyUploadInProgress"
        >
          <em class="fas fa-sync fa-spin" width="10px" alt="uploading"></em>
          Uploading
        </span>
        <table class="table table-custom mb-0 text-left settings-comapanies-table" aria-describedby="Memtable">
          <thead>
            <th scope="col" *ngIf="isAdmin">
              <div class="custom-checkbox form-check text-center">
                <input class="form-check-input float-none ms-0" type="checkbox"
                [checked]="selectAll"
                (change)="selectAllCompaniesData()"
                id="tblData"
                name="tblData">
                <label class="form-check-label c-pointer fs12" for="tblData">
                </label>
              </div>
            </th>
            <th scope="col" class="text-center text-md-start" resizable>
              Company Name
              <span>
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('companyName', 'ASC')" (keydown)="handleToggleKeydown($event, 'companyName', 'ASC')"
                  *ngIf="sortColumn !== 'companyName'"
                />
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('companyName', 'ASC')"  (keydown)="handleToggleKeydown($event, 'companyName', 'ASC')"
                  *ngIf="sort === 'DESC' && sortColumn === 'companyName'"
                />
                <img
                  src="./assets/images/up-chevron.svg"
                  alt="up-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('companyName', 'DESC')"  (keydown)="handleToggleKeydown($event, 'companyName', 'DESC')"
                  *ngIf="sort === 'ASC' && sortColumn === 'companyName'"
                />
              </span>
            </th>
            <th scope="col" resizable>
              Definable Feature Of Work
              <span>
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('DFOW', 'ASC')"  (keydown)="handleToggleKeydown($event, 'DFOW', 'ASC')"
                  *ngIf="sortColumn !== 'DFOW'"
                />
                <img
                  src="./assets/images/down-chevron.svg"
                  alt="down-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('DFOW', 'ASC')"  (keydown)="handleToggleKeydown($event, 'DFOW', 'ASC')"
                  *ngIf="sort === 'DESC' && sortColumn === 'DFOW'"
                />
                <img
                  src="./assets/images/up-chevron.svg"
                  alt="up-arrow"
                  class="h-10px ms-2"
                  (click)="sortByField('DFOW', 'DESC')"  (keydown)="handleToggleKeydown($event, 'DFOW', 'DESC')"
                  *ngIf="sort === 'ASC' && sortColumn === 'DFOW'"
                />
              </span>
            </th>
            <th scope="col" resizable>Members</th>
            <th scope="col" *ngIf="isAdmin" resizable>Action</th>
          </thead>
          <tbody *ngIf="loader == false">
            <tr>
              <td *ngIf="isAdmin" class="custom-checkbox">
                <div class="form-check text-center ps-0">
                  <input class="form-check-input float-none ms-0" disabled type="checkbox"
                  id="tblData1">
                  <label class="form-check-label c-pointer fs12" for="tblData1">
                  </label>
                </div>
               </td>
              <td class="text-left">
                <div class="d-flex">
                  <div>
                    <img
                      src="{{ parentCompany?.logo }}"
                      class="member-img"
                      alt="Member"
                      *ngIf="parentCompany?.logo != '' && parentCompany?.logo != null"
                    />
                    <img
                      src="./assets/images/default-user.svg"
                      class="member-img"
                      alt="Profile"
                      *ngIf="parentCompany?.logo == '' || parentCompany?.logo == null"
                    />
                  </div>
                  <div class="my-auto">
                    {{ parentCompany?.companyName }}
                  </div>
                </div>
              </td>
              <td *ngIf="parentCompany?.define && parentCompany?.define?.length > 0">
                <p
                  class="d-inline-block color-grey15 fs13 mb-2 fw500"
                  *ngFor="let data of parentCompany?.define; let isLast = last"
                >
                  {{ data?.DeliverDefineWork?.DFOW }}{{ isLast ? '' : ',' }}
                </p>
              </td>
              <td *ngIf="!parentCompany?.define || parentCompany?.define?.length === 0">-</td>
              <td>{{ parentCompany?.Members?.length }}</td>
              <td *ngIf="isAdmin">
                <ul class="list-inline mb-0" *ngIf="parentCompany">
                  <li
                    class="list-inline-item"
                    tooltip="Edit"
                    placement="top"
                    (click)="openEditModal(-1, editCompany)" (keydown)="handleDownKeydown($event,-1, editCompany,'edit')"
                  >
                    <a href="javascript:void(0)"
                      ><img src="./assets/images/edit.svg" alt="edit" class="h-15px"
                    /></a>
                  </li>
                </ul>
              </td>
            </tr>
            <tr
              *ngFor="
                let item of companiesList
                  | paginate
                    : {
                        itemsPerPage: pageSize,
                        currentPage: currentPageNo,
                        totalItems: totalCount
                      };
                let i = index
              "
            >
              <td *ngIf="isAdmin" class="custom-checkbox">
                <div class="form-check text-center ps-0">
                  <input class="form-check-input float-none ms-0" type="checkbox"
                  id="tblData1_{{ i }}"
                  name="tblData1"
                  [checked]="item.isChecked"
                  (change)="setSelectedItem(i)">
                  <label class="form-check-label c-pointer fs12" for="tblData1_{{ i }}">
                  </label>
                </div>
              </td>
              <td>
                <div class="d-flex">
                  <div>
                    <img
                      src="{{ item.logo }}"
                      class="member-img"
                      alt="Member"
                      *ngIf="item.logo != '' && item.logo != null"
                    />
                    <img
                      src="./assets/images/default-user.svg"
                      class="member-img"
                      alt="Profile"
                      *ngIf="item.logo == '' || item.logo == null"
                    />
                  </div>
                  <div class="my-auto">
                    {{ item.companyName }}
                  </div>
                </div>
              </td>
              <td *ngIf="item.define && item.define?.length > 0">
                <p
                  class="d-inline-block color-grey15 fs13 mb-2 fw500"
                  *ngFor="let data of item?.define; let isLast = last"
                >
                  {{ data?.DeliverDefineWork?.DFOW }}{{ isLast ? '' : ',' }}
                </p>
              </td>
              <td *ngIf="item.define && item.define?.length === 0">-</td>
              <td>
                {{ item?.Members?.length }}
              </td>
              <td *ngIf="isAdmin">
                <ul class="list-inline mb-0 d-flex">
                  <li
                    class="list-inline-item"
                    tooltip="Edit"
                    placement="top"
                    (click)="openEditModal(i, editCompany)" (keydown)="handleDownKeydown($event,i, editCompany,'edit')"
                  >
                    <a href="javascript:void(0)"
                      ><img src="./assets/images/edit.svg" alt="edit" class="h-15px"
                    /></a>
                  </li>
                  <li
                    class="list-inline-item mx-2"
                    tooltip="Delete"
                    placement="top"
                    (click)="openDeleteModal(i, deleteList)" (keydown)="handleDownKeydown($event,i, deleteList,'delete')"
                  >
                    <a href="javascript:void(0)"
                      ><img src="./assets/images/delete.svg" alt="delete" class="h-15px"
                    /></a>
                  </li>
                </ul>
              </td>
            </tr>
          </tbody>
          <tr *ngIf="loader == true">
            <td colspan="8" class="text-center">
              <div class="fs18 fw-bold cairo-regular my-5 text-black">Loading...</div>
            </td>
          </tr>
        </table>
      </div>
      <div
        class="tab-pagination px-2"
        id="tab-pagination2"
        *ngIf="loader == false && totalCount > 25"
      >
        <div class="row">
          <div class="col-md-2 align-items-center">
            <ul class="list-inline my-3">
              <li class="list-inline-item notify-pagination">
                <label class="fs12 color-grey4" for="showEnt">Show entries</label>
              </li>
              <li class="list-inline-item">
                <select  id="showEnt"
                  class="w-auto form-select fs12 color-grey4"
                  (change)="changePageSizeOfCompanyGrid($event.target.value)"
                  ngModel="{{ pageSize }}"
                >
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                  <option value="150">150</option>
                </select>
              </li>
            </ul>
          </div>
          <div class="col-md-8 text-center">
            <div class="my-3 position-relative d-inline-block">
              <pagination-controls
                (pageChange)="changePageNo($event)"
                previousLabel=""
                nextLabel=""
              >
              </pagination-controls>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- modal -->
<ng-template #newMemeber>
  <div class="modal-header">
    <h4 class="fs14 fw-bold cairo-regular color-text7 my-1">
      <img src="./assets/images/company1.svg" alt="Delivery" class="me-2" />Add New Company
    </h4>
    <button
      type="button"
      class="close ms-auto"
      aria-label="Close"
      (click)="close(cancelConfirmation, 'newCompany')"
    >
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body" *ngIf="!modalLoader">
    <form
      name="form"
      class="custom-material-form"
      [formGroup]="companyDetailsForm"
      (ngSubmit)="checkCompanyNameDuplication(companyNameConfirmationPopup, 'addCompany')"
      novalidate
    >
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <div class="upload-profile">
              <div class="member cmpny-profile">
                <div
                  class="default-profile rec-default-profile"
                  *ngIf="companyLogo.logo == undefined || companyLogo.logo == null"
                >
                  <img src="./assets/images/company2.svg" class="profile-img" alt="Profile" />
                </div>
                <div
                  class="default-profile rec-default-profile"
                  *ngIf="companyLogo.logo != undefined && companyLogo.logo != null"
                >
                  <img
                    src="{{ companyLogo.logo }}"
                    alt="Profile"
                    class="rounded upload-cmpny-img"
                  />
                </div>
                <div class="image-upload">
                  <label for="file-input">
                    <em class="fa fa-plus p-2 plus-icon" aria-hidden="true"></em>
                  </label>
                  <input id="file-input" type="file" (change)="onFileChangeEvent($event)" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <input
              type="text"
              class="form-control fs12 material-input"
              placeholder="Company Name*"
              formControlName="companyName"
              (keypress)="alphaNumericOnlyForCompanyName($event)"
            />
            <div
              class="color-red"
              *ngIf="submitted && companyDetailsForm.get('companyName').errors"
            >
              <small *ngIf="companyDetailsForm.get('companyName').errors.required"
                >*Company Name Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-2">
          <div class="form-group company-select" id="company-select5">
            <ng-multiselect-dropdown
              [placeholder]="'Definable Feature Of Work (Scope)'"
              [settings]="dropdownSettings"
              [data]="defineList"
              formControlName="selectedItems"
            >
            </ng-multiselect-dropdown>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <input
              type="text"
              class="form-control fs12 material-input"
              placeholder="Address Liness 1"
              formControlName="address"
              ngx-gp-autocomplete
              (onAddressChange)="handleCompanyAddressChange($event, 'add')"
              #placesRef="ngx-places"
            />
          </div>
        </div>
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <input
              type="text"
              class="form-control fs12 material-input"
              placeholder="Address Line 2"
              formControlName="secondAddress"
            />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <input
              type="text"
              class="form-control fs12 material-input px-2"
              placeholder="City"
              formControlName="city"
            />
          </div>
        </div>
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <input
              type="text"
              class="form-control fs12 material-input px-2"
              placeholder="State"
              formControlName="state"
            />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <input
              type="text"
              class="form-control fs12 material-input"
              placeholder="Zipcode"
              formControlName="zipCode"
              (keypress)="alphaNumForZipCode($event)"
            />
          </div>
        </div>
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <input
              type="text"
              class="form-control fs12 material-input px-2"
              placeholder="Country"
              formControlName="country"
            />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <input
              type="text"
              class="form-control fs12 material-input"
              placeholder="Website"
              formControlName="website"
            />
            <div class="color-red" *ngIf="submitted && companyDetailsForm.get('website').errors">
              <small *ngIf="companyDetailsForm.get('website').errors.pattern"
                >Enter Valid Website Url</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-2 location-settings-textarea">
          <div class="form-group">
            <label class="font-weight fs12 fw600 mt-2" for="addnotes">Additional Notes (Optional)</label>
            <textarea class="form-control fs12" placeholder="" formControlName="scope"  id="addnotes"></textarea>
          </div>
        </div>
      </div>
      <div class="text-center mt-3 mb-4">
        <button
          class="btn btn-grey color-dark-grey radius20 fs12 mb-3 me-md-3 fw-bold cairo-regular px-5"
          (click)="close(cancelConfirmation, 'newCompany')"
          type="button"
        >
          Cancel
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular mb-3 px-5"
          type="submit"
          [disabled]="formSubmitted && companyDetailsForm.valid"
        >
          <em
            class="fa fa-spinner"
            aria-hidden="true"
            *ngIf="formSubmitted && companyDetailsForm.valid"
          ></em
          >Submit
        </button>
      </div>
    </form>
  </div>
  <div class="modal-body text-center" *ngIf="modalLoader">Loading...</div>
</ng-template>
<!-- modal -->
<!-- modal -->
<ng-template #editCompany>
  <div class="modal-header">
    <h4 class="fs14 fw-bold cairo-regular color-text7 my-1">
      <img src="./assets/images/company1.svg" alt="Delivery" class="me-2" />Edit Company Details
    </h4>
    <button
      type="button"
      class="close ms-auto"
      aria-label="Close"
      (click)="close(cancelConfirmation, 'editCompany')"
    >
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
  </div>
  <div class="modal-body" *ngIf="!modalLoader">
    <form
      name="form"
      class="custom-material-form"
      [formGroup]="companyEditForm"
      (ngSubmit)="checkCompanyNameDuplication(companyNameConfirmationPopup, 'editCompany')"
      novalidate
    >
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <div class="upload-profile">
              <div class="member cmpny-profile">
                <div
                  class="default-profile rec-default-profile"
                  *ngIf="
                    (editLogo == undefined || editLogo == null) &&
                    (editCompanyLogo?.logo == null || editCompanyLogo?.logo == undefined)
                  "
                >
                  <img src="./assets/images/company2.svg" class="profile-img" alt="Profile" />
                </div>
                <div
                  class="default-profile rec-default-profile"
                  *ngIf="
                    editLogo != undefined &&
                    editLogo != '' &&
                    (editCompanyLogo?.logo == null || editCompanyLogo?.logo == undefined)
                  "
                >
                  <img src="{{ editLogo }}" alt="Profile" class="rounded upload-cmpny-img" />
                </div>
                <div
                  class="default-profile rec-default-profile"
                  *ngIf="editCompanyLogo?.logo != null && editCompanyLogo?.logo != undefined"
                >
                  <img
                    src="{{ editCompanyLogo.logo }}"
                    alt="Profile"
                    class="rounded upload-cmpny-img"
                  />
                </div>
                <div class="image-upload">
                  <label for="file-input">
                    <em class="fa fa-plus p-2 plus-icon" aria-hidden="true"></em>
                  </label>
                  <input id="file-input" type="file" (change)="onEditFileChangeEvent($event)" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <input
              type="text"
              class="form-control fs12 material-input"
              placeholder="Company Name*"
              formControlName="companyName"
              (keypress)="alphaNumericOnlyForCompanyName($event)"
            />
            <div
              class="color-red"
              *ngIf="editSubmitted && companyEditForm.get('companyName').errors"
            >
              <small *ngIf="companyEditForm.get('companyName').errors.required"
                >*Company Name Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-2">
          <div class="form-group company-select" id="company-select6">
            <ng-multiselect-dropdown
              [placeholder]="'Definable Feature Of Work (Scope)'"
              [settings]="dropdownSettings"
              [data]="defineList"
              formControlName="selectedItems"
              unSelectAllText="unSelect($event)"
            >
            </ng-multiselect-dropdown>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <input
              type="text"
              class="form-control fs12 material-input"
              placeholder="Address Line 1"
              formControlName="address"
              ngx-gp-autocomplete
              (onAddressChange)="handleCompanyAddressChange($event, 'edit')"
              #placesRef="ngx-places"
            />
          </div>
        </div>
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <input
              type="text"
              class="form-control fs12 material-input"
              placeholder="Address Line 2"
              formControlName="secondAddress"
            />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <input
              type="text"
              class="form-control fs12 material-input px-2"
              placeholder="Country"
              formControlName="country"
            />
          </div>
        </div>
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <input
              type="text"
              class="form-control fs12 material-input px-2"
              placeholder="State"
              formControlName="state"
            />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <input
              type="text"
              class="form-control fs12 material-input px-2"
              placeholder="City"
              formControlName="city"
            />
          </div>
        </div>
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <input
              type="text"
              class="form-control fs12 material-input"
              placeholder="Zipcode"
              formControlName="zipCode"
              (keypress)="numberOnlyForCompanyZipCode($event)"
            />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6 mb-2">
          <div class="form-group">
            <input
              type="text"
              class="form-control fs12 material-input"
              placeholder="Website"
              formControlName="website"
            />
            <div class="color-red" *ngIf="editSubmitted && companyEditForm.get('website').errors">
              <small *ngIf="companyEditForm.get('website').errors.pattern"
                >Enter Valid Website Url</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6 mb-2 location-settings-textarea">
          <div class="form-group">
            <label class="font-weight fs12 fw600 mt-2"  for="addnotes1">Additional Notes (Optional)</label>
            <textarea class="form-control fs12" placeholder=""   for="addnotes1" formControlName="scope"></textarea>
          </div>
        </div>
      </div>
      <div class="text-center mt-3 mb-4">
        <button
          class="btn btn-grey color-dark-grey radius20 fs12 mb-3 me-md-3 fw-bold cairo-regular px-5"
          (click)="close(cancelConfirmation, 'editCompany')"
          type="button"
        >
          Cancel
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular mb-3 px-5"
          type="submit"
          [disabled]="editFormSubmitted && companyEditForm.valid"
        >
          <em
            class="fa fa-spinner"
            aria-hidden="true"
            *ngIf="editFormSubmitted && companyEditForm.valid"
          ></em
          >Submit
        </button>
      </div>
    </form>
  </div>
  <div class="modal-body text-center" *ngIf="modalLoader">Loading...</div>
</ng-template>
<!-- modal -->
<div id="fiter-temp2">
  <!--Filter Modal-->
  <ng-template #filter>
    <div class="modal-header border-0 pb-0">
      <h4 class="fs14 fw-bold cairo-regular color-text7 my-0">Filter</h4>
      <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true"
          ><img src="./assets/images/modal-close.svg" alt="Modal Close"
        /></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="filter-content">
        <form class="custom-material-form" [formGroup]="filterForm" (ngSubmit)="filterSubmit()">
          <div class="row">
            <div class="col-md-12">
              <div class="input-group mb-3">
                <input
                  type="text"
                  class="form-control fs12 material-input"
                  placeholder="Company Name"
                  formControlName="companyFilter"
                />
                  <span class="input-group-text">
                    <img src="./assets/images/search-icon.svg" alt="Search" />
                  </span>
              </div>
              <div class="form-group">
                <select class="form-control fs12 material-input" formControlName="dfowFilter">
                  <option value="" disabled selected hidden>
                    Definable Feature Of work (Scope)
                  </option>
                  <option *ngFor="let item of defineList" [value]="item.id" [ngValue]="item.id">
                    {{ item.DFOW }}
                  </option>
                </select>
              </div>
              <div class="row justify-content-end">
                <button
                  class="btn btn-orange radius20 col-4 mt-2 fs12 fw-bold cairo-regular mx-1"
                  type="submit"
                >
                  Apply
                </button>
                <button
                  class="btn btn-orange radius20 fs12 col-4 mt-2 fw-bold cairo-regular mx-1"
                  type="button"
                  (click)="resetFilter()"
                >
                  Reset
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
  <!--Filter Modal-->
</div>
<ng-template #deleteList>
  <div class="modal-body">
    <div class="text-center my-4" *ngIf="!remove">
      <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
        Are you sure you want to delete '{{
          companiesList[currentDeleteId]?.companyName | titlecase
        }}'?
      </p>
      <button
        class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
        (click)="resetAndClose()"
      >
        No
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
        type="submit"
        (click)="deleteCompany()"
        [disabled]="deleteSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="deleteSubmitted"></em>yes
      </button>
    </div>
    <div class="text-center my-4" id="remove-popup1" *ngIf="remove">
      <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
        Are you sure you want to delete?
      </p>
      <button
        class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
        (click)="resetAndClose()"
      >
        No
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
        type="submit"
        (click)="removeItem()"
        [disabled]="deleteSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="deleteSubmitted"></em> yes
      </button>
    </div>
  </div>
</ng-template>
<!--Cancel Confirmation Popup-->
<div id="confirm-popup2">
  <ng-template #cancelConfirmation>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          Are you sure you want to cancel?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="resetForm('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="resetForm('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
<!--Cancel Confirmation Popup-->

<!--Add Company Confirmation Popup-->
<div id="confirm-popup24">
  <ng-template #companyNameConfirmationPopup>
    <div class="modal-body">
      <div class="text-center my-4">
        <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
          <span class="fw-bold fs14">"{{ newCompanyTriedToAdd }}"</span>
          <span class="fs14"> closely resembles another company already added to Follo.</span>
          <br />
          Are you sure you want to proceed?
        </p>
        <button
          class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
          (click)="companyNameConfirmationToAdd('no')"
        >
          No
        </button>
        <button
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
          (click)="companyNameConfirmationToAdd('yes')"
        >
          Yes
        </button>
      </div>
    </div>
  </ng-template>
</div>
<!--Add Company Confirmation Popup-->

<!-- Import Companies list -->
<ng-template #import>
  <div class="mx-2 mx-md-5 my-5 bulkupload">
    <button
      type="button"
      class="close upload-close bg-transparent border-0"
      aria-label="Close"
      (click)="close(cancelConfirmation, 'importCompanies')"
    >
      <span aria-hidden="true"
        ><img src="./assets/images/modal-close.svg" alt="Modal Close"
      /></span>
    </button>
    <h1 class="fw-bold cairo-regular fs20 text-center pb-4">Import Multiple Companies</h1>
    <ngx-file-drop
      dropZoneLabel="Drop files here"
      (onFileDrop)="companyImportFiledropped($event)"
      multiple="true"
    >
      <ng-template ngx-file-drop-content-tmp let-openFileSelector="openFileSelector">
        <div class="bulkupload-content text-center" (click)="openFileSelector()" (keydown)="openFileSelector()">
          <img src="./assets/images/file.svg" alt="Excel" />
          <p class="fs14 fw600 mb3 pt10 color-grey7">Drag & Drop your file here</p>
          <p class="fs12 color-grey8 fw500 mb3">Or</p>
          <label class="color-blue4 fs14 mb10 fw500 text-underline" for="browse">Click here to browse</label>
        </div>
      </ng-template>
    </ngx-file-drop>
    <p class="fs10 color-grey8 fw500 my-2 text-end">
      *Supported formats are .xlsx, .xls and .csv
    </p>
    <div class="row upload-table py-3 m-0" *ngFor="let item of companiesFiles; let i = index">
      <div class="col-1 col-md-1 ps-0">
        <img src="./assets/images/xlsx.png" alt="attached-file" class="rounded upload-img" />
      </div>
      <div class="col-9 col-md-9">
        <h1 class="fs16 ms-4 color-grey7">{{ item.relativePath }}</h1>
        <p class="ms-4 mb-0 color-grey8">
          {{ todayDate | date : 'short' }}<span class="ms-3"></span>
        </p>
      </div>
      <div
        class="col-1 col-md-2 text-end d-flex justify-content-end pe-0"
        (click)="removeFile(i)" (keydown)="handleDownKeydown($event,i, '','remove')"
      >
        <img src="./assets/images/delete.svg" alt="delete" class="h-15px c-pointer" />
      </div>
    </div>
    <div class="text-center">
      <button
        class="btn btn-orange radius20 fs12 fw-bold cairo-regular my-4 px-5"
        type="submit"
        (click)="importData()"
        *ngIf="companiesFiles.length > 0"
        [disabled]="importSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="importSubmitted"></em>
        Done
      </button>
      <p class="color-orange c-pointer fs12" (click)="download()" (keydown)="handleDownKeydown($event,'', '','download')">
        <u>Download sample template here</u>
      </p>
    </div>
  </div>
</ng-template>
<!-- Import Companies list -->
