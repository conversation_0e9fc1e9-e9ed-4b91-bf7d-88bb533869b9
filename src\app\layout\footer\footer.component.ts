import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../services/auth/auth.service';
import { ProjectService } from '../../services/profile/project.service';
import { ProfileService } from '../../services/profile/profile.service';
import { DeliveryService } from '../../services/profile/delivery.service';

declare let Intercom: any;
@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  })
export class FooterComponent implements OnInit {
  public isCollapsed = true;

  public currentUser: any = {};

  public ProjectId;

  public ParentCompanyId;

  public constructor(
    private readonly deliveryService: DeliveryService,
    public router: Router,
    public authService: AuthService,
    public profileService: ProfileService,
    public projectService: ProjectService,
  ) {
    this.deliveryService.footerDropdownStatus(false);
    this.profileService.overViewDetail.subscribe((res): void => {
      this.currentUser.profilePic = res.User?.profilePic;
      this.currentUser.firstName = '';
      if (res.User?.firstName) {
        this.currentUser.firstName = `${res.User?.firstName} ${res.User?.lastName}`;
      }
    });
    this.projectService.projectParent.subscribe((response9): void => {
      if (response9 !== undefined && response9 !== null && response9 !== '') {
        this.ProjectId = response9.ProjectId;
        this.ParentCompanyId = response9.ParentCompanyId;
        this.getOverView();
      }
    });
  }

  public ngOnInit(): void {
    this.getAuthUser();
  }

  public logout(): void {
    this.authService.logout();
    Intercom('shutdown');
  }

  public changeSettingsCollapse(): void {
    this.isCollapsed = !this.isCollapsed;
    if (this.isCollapsed) {
      this.deliveryService.footerDropdownStatus(false);
    } else {
      this.deliveryService.footerDropdownStatus(true);
    }
  }

  public getOverView(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.profileService.getOverView(param).subscribe((response: any): void => {
      if (response) {
        const userData = response.data;
        this.currentUser.profilePic = userData.User?.profilePic;
        this.currentUser.firstName = '';
        if (userData.User?.firstName) {
          this.currentUser.firstName = `${userData.User?.firstName} ${userData.User?.lastName}`;
        }
      }
    });
  }

  public getAuthUser(): void {
    this.authService.getUser().subscribe((response: any): void => {
      this.currentUser = response;
      this.currentUser.firstName = '';
      if (response.User?.firstName) {
        this.currentUser.firstName = `${response.User?.firstName} ${response.User?.lastName}`;
      }
    });
  }

  public handleDownKeydown(event: KeyboardEvent, type: any) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      switch (type) {
        case 'logout':
          this.logout();
          break;
        case 'change':
          this.changeSettingsCollapse();
          break;
        default:
          break;
      }
    }
  }
}
