import { ComponentFixture, TestBed,fakeAsync, tick } from '@angular/core/testing';
import { BillingComponent } from './billing.component';
import { ToastrService } from 'ngx-toastr';
import { BsModalService } from 'ngx-bootstrap/modal';
import { UntypedFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ProjectService } from '../services/profile/project.service';
import { BillingService } from '../services/dashboard/billing.service';
import { AuthService } from '../services/auth/auth.service';
import { of, throwError } from 'rxjs';

describe('BillingComponent', () => {
  let component: BillingComponent;
  let fixture: ComponentFixture<BillingComponent>;
  let mockBillingService: jest.Mocked<BillingService>;
  let mockProjectService: jest.Mocked<ProjectService>;
  let mockAuthService: jest.Mocked<AuthService>;
  let mockToastr: jest.Mocked<ToastrService>;
  let mockModalService: jest.Mocked<BsModalService>;
  let mockRouter: jest.Mocked<Router>;

  beforeEach(async () => {
    mockBillingService = {
      getBilling: jest.fn(),
      payOffline: jest.fn(),
      payOnline: jest.fn()
    } as any;

    mockProjectService = {
      ParentCompanyId: of('123')
    } as any;

    mockAuthService = {
      getCountry: jest.fn().mockReturnValue(of({ countryList: ['USA', 'UK'] }))
    } as any;

    mockToastr = {
      success: jest.fn(),
      error: jest.fn()
    } as any;

    mockModalService = {
      show: jest.fn()
    } as any;

    mockRouter = {
      navigate: jest.fn()
    } as any;

    await TestBed.configureTestingModule({
      declarations: [BillingComponent],
      imports: [ReactiveFormsModule],
      providers: [
        { provide: BillingService, useValue: mockBillingService },
        { provide: ProjectService, useValue: mockProjectService },
        { provide: AuthService, useValue: mockAuthService },
        { provide: ToastrService, useValue: mockToastr },
        { provide: BsModalService, useValue: mockModalService },
        { provide: Router, useValue: mockRouter },
        UntypedFormBuilder
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(BillingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.currentPageNo).toBe(1);
    expect(component.pageSize).toBe(25);
    expect(component.loader).toBe(true);
    expect(component.submitted).toBe(false);
    expect(component.payOfflineChosen).toBe(false);
    expect(component.payOnlineChosen).toBe(false);
  });

  it('should get billing data when ParentCompanyId is available', () => {
    const mockResponse = {
      data: {
        rows: [{ id: 1 }],
        count: 1,
        nextPayDet: { amount: 100 }
      }
    };
    mockBillingService.getBilling.mockReturnValue(of(mockResponse));

    component.getBilling();

    expect(mockBillingService.getBilling).toHaveBeenCalledWith({
      pageSize: 25,
      pageNo: 1,
      ParentCompanyId: '123'
    });
    expect(component.billingData).toEqual(mockResponse.data.rows);
    expect(component.totalCount).toBe(mockResponse.data.count);
    expect(component.nextPayDet).toEqual(mockResponse.data.nextPayDet);
  });

  it('should handle offline payment successfully', fakeAsync(() => {
    const mockResponse = { message: 'Payment successful' };
    mockBillingService.payOffline.mockReturnValue(of(mockResponse));
    mockBillingService.getBilling.mockReturnValue(of({
      data: { rows: [], count: 0, nextPayDet: {} }
    }));
    component.modalRef = { hide: jest.fn() } as any;
    component.nextPayDet = { amount: 100 };
    component.submitted = true;
    component.payOfflineChosen = true;
  
    component.payOffline();
    tick();
  
    expect(mockBillingService.payOffline).toHaveBeenCalled();
    expect(mockToastr.success).toHaveBeenCalledWith('Payment successful', 'Success');
    expect(component.submitted).toBe(false);
    expect(component.payOfflineChosen).toBe(false);
  }));
  

  it('should handle offline payment error', fakeAsync(() => {
    const mockError = {
      message: {
        details: [['Invalid payment']]
      }
    };
    mockBillingService.payOffline.mockReturnValue(throwError(() => mockError));
    component.submitted = true;
    component.payOfflineChosen = true;
  
    component.payOffline();
    tick();
  
    expect(mockToastr.error).toHaveBeenCalled();
    expect(component.submitted).toBe(false);
    expect(component.payOfflineChosen).toBe(false);
  }));
  

  it('should validate card form', () => {
    component.cardForm(); // <-- this initializes the form
    const form = component.billingCardDetailsForm;
  
    expect(form.valid).toBeFalsy();
    expect(form.get('number')).toBeTruthy();
    expect(form.get('exp_month')).toBeTruthy();
    expect(form.get('exp_year')).toBeTruthy();
    expect(form.get('cvc')).toBeTruthy();
  });
  
  

  it('should validate alpha numeric input', () => {
    const validEvent = { which: 65 }; // 'A'
    const invalidEvent = { which: 33 }; // '!'

    expect(component.alphaNum(validEvent)).toBe(true);
    expect(component.alphaNum(invalidEvent)).toBe(false);
  });

  it('should validate numeric input', () => {
    const validEvent = { which: 48 }; // '0'
    const invalidEvent = { which: 65 }; // 'A'

    expect(component.numberOnly(validEvent)).toBe(true);
    expect(component.numberOnly(invalidEvent)).toBe(false);
  });

  it('should get country list on initialization', () => {
    const mockResponse = { countryList: ['USA', 'UK'] };
    mockAuthService.getCountry.mockReturnValue(of(mockResponse));

    component.getCountry();

    expect(mockAuthService.getCountry).toHaveBeenCalled();
    expect(component.countryList).toEqual(mockResponse.countryList);
  });

  it('should handle page size change', () => {
    const newPageSize = 50;
    const mockResponse = {
      data: {
        rows: [],
        count: 0,
        nextPayDet: {}
      }
    };
    mockBillingService.getBilling.mockReturnValue(of(mockResponse));

    component.changePageSize(newPageSize);

    expect(component.pageSize).toBe(newPageSize);
    expect(component.currentPageNo).toBe(1);
  });

  it('should handle page number change', () => {
    const newPageNo = 2;
    const mockResponse = {
      data: {
        rows: [],
        count: 0,
        nextPayDet: {}
      }
    };
    mockBillingService.getBilling.mockReturnValue(of(mockResponse));

    component.changePageNo(newPageNo);

    expect(component.currentPageNo).toBe(newPageNo);
  });

  // Additional tests for 90% coverage

  it('should initialize billing card years array in ngOnInit', () => {
    component.ngOnInit();

    expect(component.billingCardYears).toHaveLength(21);
    expect(component.billingCardYears[0]).toBe(component.startyear);
    expect(component.billingCardYears[20]).toBe(component.startyear + 20);
  });

  it('should call reset when Enter key is pressed in handleToggleKeydown', () => {
    component.modalRef = { hide: jest.fn() } as any;
    component.cardForm();
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    jest.spyOn(event, 'preventDefault');
    jest.spyOn(component, 'reset');

    component.handleToggleKeydown(event);

    expect(event.preventDefault).toHaveBeenCalled();
    expect(component.reset).toHaveBeenCalled();
  });

  it('should call reset when Space key is pressed in handleToggleKeydown', () => {
    component.modalRef = { hide: jest.fn() } as any;
    component.cardForm();
    const event = new KeyboardEvent('keydown', { key: ' ' });
    jest.spyOn(event, 'preventDefault');
    jest.spyOn(component, 'reset');

    component.handleToggleKeydown(event);

    expect(event.preventDefault).toHaveBeenCalled();
    expect(component.reset).toHaveBeenCalled();
  });

  it('should not call reset for other keys in handleToggleKeydown', () => {
    const event = new KeyboardEvent('keydown', { key: 'Tab' });
    jest.spyOn(event, 'preventDefault');
    jest.spyOn(component, 'reset');

    component.handleToggleKeydown(event);

    expect(event.preventDefault).not.toHaveBeenCalled();
    expect(component.reset).not.toHaveBeenCalled();
  });

  it('should return true for uppercase letters in alphaOnly', () => {
    const event = { keyCode: 65 }; // 'A'
    expect(component.alphaOnly(event)).toBe(true);
  });

  it('should return true for lowercase letters in alphaOnly', () => {
    const event = { keyCode: 97 }; // 'a'
    expect(component.alphaOnly(event)).toBe(true);
  });

  it('should return true for backspace in alphaOnly', () => {
    const event = { keyCode: 8 }; // Backspace
    expect(component.alphaOnly(event)).toBe(true);
  });

  it('should return false for numbers in alphaOnly', () => {
    const event = { keyCode: 48 }; // '0'
    expect(component.alphaOnly(event)).toBe(false);
  });

  it('should return false for special characters in alphaOnly', () => {
    const event = { keyCode: 33 }; // '!'
    expect(component.alphaOnly(event)).toBe(false);
  });

  it('should hide modal and reset form in reset method', () => {
    component.modalRef = { hide: jest.fn() } as any;
    component.cardForm();
    component.billingCardDetailsForm.patchValue({
      name: 'Test Name',
      number: '1234567890123456',
      exp_month: '12',
      exp_year: '2025',
      country: 'USA'
    });

    component.reset();

    expect(component.modalRef.hide).toHaveBeenCalled();
    expect(component.billingCardDetailsForm.get('exp_year')?.value).toBe('');
    expect(component.billingCardDetailsForm.get('exp_month')?.value).toBe('');
    expect(component.billingCardDetailsForm.get('country')?.value).toBe('');
  });

  it('should handle download receipt call', () => {
    const url = 'http://example.com/receipt.pdf';

    // Since the method is empty, we just verify it doesn't throw
    expect(() => component.downloadReceipt(url)).not.toThrow();
  });

  it('should open modal with correct configuration', () => {
    const template = {} as any;
    const expectedConfig = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-sm billing-modal  custom-modal'
    };

    component.openModal(template);

    expect(mockModalService.show).toHaveBeenCalledWith(template, expectedConfig);
  });

  it('should hide current modal and open card modal', () => {
    component.modalRef = { hide: jest.fn() } as any;
    const template = {} as any;
    const expectedConfig = {
      backdrop: 'static',
      keyboard: false,
      class: 'modal-lg'
    };

    component.openCardModal(template);

    expect(component.modalRef.hide).toHaveBeenCalled();
    expect(mockModalService.show).toHaveBeenCalledWith(template, expectedConfig);
  });

  // Tests for onSubmit method
  it('should handle valid form submission in onSubmit', fakeAsync(() => {
    const mockResponse = { message: 'Payment successful' };
    mockBillingService.payOnline.mockReturnValue(of(mockResponse));
    mockBillingService.getBilling.mockReturnValue(of({
      data: { rows: [], count: 0, nextPayDet: {} }
    }));

    component.modalRef = { hide: jest.fn() } as any;
    component.nextPayDet = { amount: 100 };
    component.cardForm();
    component.billingCardDetailsForm.patchValue({
      name: 'Test User',
      number: '1234567890123456',
      exp_month: '12',
      exp_year: '2025',
      cvc: '123'
    });

    component.onSubmit();
    tick();

    expect(component.submitted).toBe(true);
    expect(component.formSubmitted).toBe(true);
    expect(mockBillingService.payOnline).toHaveBeenCalled();
  }));

  it('should handle invalid form in onSubmit', () => {
    component.cardForm();
    component.billingCardDetailsForm.patchValue({
      name: '', // Invalid - required field
      number: '',
      exp_month: '',
      exp_year: '',
      cvc: ''
    });

    component.onSubmit();

    expect(component.submitted).toBe(true);
    expect(component.formSubmitted).toBe(false);
  });

  it('should handle expired card in onSubmit', () => {
    component.cardForm();
    component.startyear = 2023;
    component.month = 6;
    component.billingCardDetailsForm.patchValue({
      name: 'Test User',
      number: '1234567890123456',
      exp_month: '05', // Earlier month
      exp_year: '2023', // Same year
      cvc: '123'
    });

    component.onSubmit();

    expect(mockToastr.error).toHaveBeenCalledWith('Card Expired');
    expect(component.formSubmitted).toBe(false);
    expect(component.submitted).toBe(false);
  });

  it('should handle payOnline error in onSubmit', fakeAsync(() => {
    const mockError = {
      message: {
        statusCode: 400,
        details: [['Payment failed']]
      }
    };
    mockBillingService.payOnline.mockReturnValue(throwError(() => mockError));

    component.cardForm();
    component.nextPayDet = { amount: 100 };
    component.billingCardDetailsForm.patchValue({
      name: 'Test User',
      number: '1234567890123456',
      exp_month: '12',
      exp_year: '2025',
      cvc: '123'
    });

    component.onSubmit();
    tick();

    expect(component.submitted).toBe(false);
    expect(component.payOnlineChosen).toBe(false);
  }));

  // Tests for payOnline method
  it('should set flags correctly in payOnline', () => {
    component.payOnline();

    expect(component.submitted).toBe(true);
    expect(component.payOnlineChosen).toBe(true);
  });

  // Tests for showError method
  it('should display error message in showError', () => {
    const mockError = {
      message: {
        details: [['Invalid payment details']]
      }
    };

    component.showError(mockError);

    expect(component.submitted).toBe(false);
    expect(mockToastr.error).toHaveBeenCalledWith(['Invalid payment details']);
  });

  // Tests for getBilling error scenarios
  it('should handle getBilling error', () => {
    const mockError = { message: 'Network error' };
    mockBillingService.getBilling.mockReturnValue(throwError(() => mockError));

    component.getBilling();

    // Since there's no error handling in getBilling, we just verify the call was made
    expect(mockBillingService.getBilling).toHaveBeenCalled();
  });

  // Tests for getCountry error scenarios
  it('should handle getCountry error', () => {
    const mockError = { message: 'Network error' };
    mockAuthService.getCountry.mockReturnValue(throwError(() => mockError));

    component.getCountry();

    expect(mockAuthService.getCountry).toHaveBeenCalled();
  });

  it('should handle getCountry with no response', () => {
    mockAuthService.getCountry.mockReturnValue(of(null));

    component.getCountry();

    expect(mockAuthService.getCountry).toHaveBeenCalled();
    expect(component.countryList).toEqual([]);
  });

  // Tests for alphaNum edge cases
  it('should handle alphaNum with different key codes', () => {
    // Test boundary values
    expect(component.alphaNum({ which: 64 })).toBe(false); // Just before 'A'
    expect(component.alphaNum({ which: 91 })).toBe(false); // Just after 'Z'
    expect(component.alphaNum({ which: 96 })).toBe(false); // Just before 'a'
    expect(component.alphaNum({ which: 129 })).toBe(false); // Just after valid range
    expect(component.alphaNum({ which: 47 })).toBe(false); // Just before '0'
    expect(component.alphaNum({ which: 58 })).toBe(false); // Just after '9'
    expect(component.alphaNum({ which: 31 })).toBe(true); // Control character
  });

  // Tests for numberOnly edge cases
  it('should handle numberOnly with different scenarios', () => {
    expect(component.numberOnly({ which: 47 })).toBe(false); // Just before '0'
    expect(component.numberOnly({ which: 58 })).toBe(false); // Just after '9'
    expect(component.numberOnly({ which: 31 })).toBe(true); // Control character
    expect(component.numberOnly({ keyCode: 49 })).toBe(true); // '1' using keyCode
  });
});
