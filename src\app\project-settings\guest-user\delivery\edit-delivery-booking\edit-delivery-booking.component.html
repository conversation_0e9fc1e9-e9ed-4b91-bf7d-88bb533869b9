<div class="modal-header">
    <h1 class="fs14 fw-bold cairo-regular color-text7 my-1">
      <img src="./assets/images/delivery-pop.svg" alt="Delivery" class="me-2" />Edit Delivery Booking
    </h1>
    <button
    type="button"
    class="close ms-auto"
    aria-label="Close"
    (click)="close(cancelConfirmation)"
  >
    <span aria-hidden="true"><img src="./assets/images/modal-close.svg" alt="Modal Close" /></span>
  </button>
  </div>
  <div class="modal-body newupdate-alignments" *ngIf="!modalLoader">
    <!-- Edit NDR -->
    <form
      name="form"
      class="custom-material-form"
      id="delivery-edit2"
      [formGroup]="deliverEditForm"
      novalidate
    >
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label class="fs12 fw600" for="description">Description<sup>*</sup></label>
            <textarea id="description"
              class="form-control fs11 radius0"
              placeholder="Enter Description"
              rows="2"
              formControlName="description"
              maxlength="150"
              (ngModelChange)="onEditSubmitForm('')"
            ></textarea>
            <div class="color-red" *ngIf="editSubmitted && deliverEditForm.get('description').errors">
              <small *ngIf="deliverEditForm.get('description').errors.required"
                >*Description is Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6" *ngIf="!craneEquipmentTypeChosen">
          <div class="form-group">
            <label class="fs12 fw600" for="delId">Delivery ID</label>
            <input id="delId"
              type="text"
              class="form-control fs10 color-orange fw500 material-input ps-3"
              placeholder=""
              formControlName="DeliveryId"
              disabled="disabled"
            />
          </div>
        </div>
        <div class="col-md-3" *ngIf="craneEquipmentTypeChosen">
          <div class="form-group">
            <label class="fs12 fw600" for="delId">Delivery ID</label>
            <input id="delId"
              type="text"
              class="form-control fs10 color-orange fw500 material-input ps-3"
              placeholder=""
              formControlName="DeliveryId"
              disabled="disabled"
            />
          </div>
        </div>
        <div class="col-md-3" *ngIf="craneEquipmentTypeChosen">
          <div class="form-group">
            <label class="fs12 fw600" for="cranId">Crane Pick Request ID</label>
            <input id="cranId"
              type="text"
              class="form-control fs10 color-orange fw500 material-input ps-3"
              placeholder=""
              formControlName="CraneRequestId"
              disabled="disabled"
            />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="form-group company-select" id="company-select3">
            <label class="fs12 fw600"  for="respCom">Responsible Company<sup>*</sup></label>
            <ng-multiselect-dropdown  id="respCom"
              [placeholder]="'Responsible Company*'"
              [settings]="editNdrCompanyDropdownSettings"
              [data]="companyList"
              formControlName="companyItems"
              (ngModelChange)="onEditSubmitForm('')"
            >
            </ng-multiselect-dropdown>
            <div
              class="color-red"
              *ngIf="editSubmitted && !saveQueuedNDR && deliverEditForm.get('companyItems').errors"
            >
              <small *ngIf="deliverEditForm.get('companyItems').errors.required"
                >*Company is Required.</small
              >
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group company-select" id="company-select7">
            <label class="fs12 fw600"  for="featWork">Definable Feature Of Work</label>
            <ng-multiselect-dropdown  id="featWork"
              [placeholder]="'Definable Feature Of Work (Scope)'"
              [settings]="editNdrDefinableDropdownSettings"
              [data]="defineList"
              formControlName="defineItems"
              (ngModelChange)="onEditSubmitForm('')"
            >
            </ng-multiselect-dropdown>
            <div
              class="color-red"
              *ngIf="editSubmitted && !saveQueuedNDR && deliverEditForm.get('defineItems').errors"
            >
              <small *ngIf="deliverEditForm.get('defineItems').errors.required"
                >*Definable Feature Of Work is Required.</small
              >
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label class="fs12 fw600"  for="respPerson">Responsible Person<sup>*</sup></label>
            <ul
              class="follo-switch list-group list-group-horizontal justify-content-end float-end newfixswitch"
              id="switch-control3"
            >
              <li class="fs12 list-group-item border-0 escort--text pe-3 py-0">
                Escort Needed?<sup>*</sup>
              </li>
              <li class="list-group-item border-0 p-0">
                <ui-switch
                  switchColor="#fff"
                  defaultBoColor="#CECECE"
                  defaultBgColor="#CECECE"
                  formControlName="escort"
                  (ngModelChange)="onEditSubmitForm('')"
                >
                </ui-switch>
              </li>
            </ul>
            <div class="w-100 float-start">
              <tag-input
                formControlName="person"
                [onlyFromAutocomplete]="true"
                [placeholder]="'Responsible Person *'"
                [onTextChangeDebounce]="500"
                [identifyBy]="'id'"
                [displayBy]="'email'"
                class="tag-layout"
                (ngModelChange)="onEditSubmitForm('')"
                (ngModelChange)="gatecheck($event, 'Person')"
              >
                <tag-input-dropdown
                  [showDropdownIfEmpty]="true"
                  [displayBy]="'email'"
                  [identifyBy]="'id'"
                  [autocompleteObservable]="requestAutoEditcompleteItems"
                >
                  <ng-template let-item="item" let-index="index">
                    <span class="fs10">{{ item.email }}</span>
                  </ng-template>
                </tag-input-dropdown>
              </tag-input>
            </div>
            <small class="color-red" *ngIf="errmemberenable"
              >Please select an active member,the existing member is deactivated.</small
            >
            <div
              class="color-red"
              *ngIf="editSubmitted && !saveQueuedNDR && deliverEditForm.get('person').errors"
            >
              <small *ngIf="deliverEditForm.get('person').errors.required"
                >*Please choose Responsible person.</small
              >
            </div>
          </div>
          <div class="w-100 pt-4 float-start">
            <label class="fs12 fw600"  for="gate">Gate<sup>*</sup></label>
            <select  id="gate"
              class="form-control fs12 material-input px-2"
              formControlName="GateId"
              (ngModelChange)="onEditSubmitForm('')"
              (ngModelChange)="gatecheck($event, 'Gate')"
            >
              <option
                *ngIf="
                  currentEditItem &&
                  currentEditItem?.gateDetails &&
                  currentEditItem?.gateDetails[0]?.Gate?.id
                "
                [ngValue]="currentEditItem?.gateDetails[0]?.Gate?.id"
                disabled
                selected
                hidden
              >
                {{ currentEditItem?.gateDetails[0]?.Gate?.gateName }}
              </option>
              <option value="" disabled selected hidden>Gate<sup>*</sup></option>
              <option *ngFor="let item of gateList" [ngValue]="item.id">{{ item.gateName }}</option>
            </select>

            <small class="color-red" *ngIf="errtextenable"
              >Please select an active gate,the existing gate is deactivated.</small
            >

            <div
              class="color-red"
              *ngIf="editSubmitted && !saveQueuedNDR && deliverEditForm.get('GateId').errors"
            >
              <small *ngIf="deliverEditForm.get('GateId').errors.required">*Gate is Required.</small>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="row">
            <div class="col-md-12 float-start">
              <label class="fs12 fw600"  for="delDate">Delivery Date<sup>*</sup></label>
              <div class="input-group new-delivery-form-date mb-3">
                <input  id="delDate"
                  class="form-control fs10 material-input"
                  #dp="bsDatepicker"
                  bsDatepicker
                  formControlName="deliveryDate"
                  placeholder="Delivery Date *"
                  [bsConfig]="{ isAnimated: true, showWeekNumbers: false, customTodayClass: 'today' }"
                  (ngModelChange)="onEditSubmitForm('')"
                  [ngClass]="{ disabled: isDisabledDate }"
                /><br />

                <div
                  class="color-red"
                  *ngIf="
                    editSubmitted && !saveQueuedNDR && deliverEditForm.get('deliveryDate').errors
                  "
                >
                  <small *ngIf="deliverEditForm.get('deliveryDate').errors.required"
                    >*Date is Required.</small
                  >
                </div>
                  <span class="input-group-text">
                    <img
                      src="./assets/images/date.svg"
                      class="h-12px"
                      alt="Date"
                      [attr.aria-expanded]="dp.isOpen"
                      [ngClass]="{ disabled: isDisabledDate }"
                      (click)="!isDisabledDate ? dp.toggle() : null"
                      (keydown)="!isDisabledDate ? dp.toggle() : null"
                    />
                  </span>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6 pe-md-0">
              <div class="input-group delivery-time">
                <label class="fs12 fw600" for="fromTme">From Time:</label>
                <timepicker  id="fromTme"
                  [formControlName]="'deliveryStart'"
                  (keypress)="numberOnly($event)"
                  (ngModelChange)="changeDate($event)"
                >
                </timepicker>
                <div
                  class="color-red mt35"
                  *ngIf="
                    editSubmitted && !saveQueuedNDR && deliverEditForm.get('deliveryStart').errors
                  "
                >
                  <small *ngIf="deliverEditForm.get('deliveryStart').errors.required"
                    >*Start Time is Required.</small
                  >
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="input-group delivery-time">
                <label class="fs12 fw600"  for="toTme">To Time:</label>
                <timepicker  id="toTme"
                  [formControlName]="'deliveryEnd'"
                  (keypress)="numberOnly($event)"
                  (ngModelChange)="deliveryEndTimeChangeDetection()"
                ></timepicker>
                <div
                  class="color-red mt35"
                  *ngIf="editSubmitted && !saveQueuedNDR && deliverEditForm.get('deliveryEnd').errors"
                >
                  <small *ngIf="deliverEditForm.get('deliveryEnd').errors.required"
                    >*End Time is Required.</small
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12 mt-md-2">
              <div class="form-group mt-1 company-select equipment-select">
                <label class="fs12 fw600" for="equipment">Equipment<sup>*</sup></label>
                <ng-multiselect-dropdown  id="equipment"
                  [placeholder]="'Equipment'"
                  [settings]="equipmentDropdownSettings"
                  [data]="equipmentList"
                  formControlName="EquipmentId"
                  (ngModelChange)="onEditSubmitForm($event)">
                </ng-multiselect-dropdown>

                <div
                  class="color-red"
                  *ngIf="editSubmitted && !saveQueuedNDR && deliverEditForm.get('EquipmentId').errors"
                >
                  <small *ngIf="deliverEditForm.get('EquipmentId').errors.required"
                    >*Equipment is Required.</small
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row" *ngIf="craneEquipmentTypeChosen">
        <div class="col-md-6">
          <div class="form-group">
            <label class="fs12 fw600"  for="pickup">Picking From<sup>*</sup></label>
            <textarea id="pickup"
              class="form-control fs10 radius0"
              placeholder="Picking From"
              rows="2"
              formControlName="cranePickUpLocation"
              maxlength="150"
              (ngModelChange)="onEditSubmitForm('')"
            ></textarea>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label class="fs12 fw600" for="pickto">Picking To<sup>*</sup></label>
            <textarea id="pickto"
              class="form-control fs10 radius0"
              placeholder="Picking To"
              rows="2"
              formControlName="craneDropOffLocation"
              maxlength="150"
              (ngModelChange)="onEditSubmitForm('')"
            ></textarea>
          </div>
        </div>
      </div>
      <div class="row" *ngIf="seriesOption !== 1">
        <div class="col-md-6">
          <div class="form-group">
            <label class="fs12 fw600" for="recurrance">Recurrence<sup>*</sup></label>
            <input id="recurrance"
              type="text"
              class="form-control material-input ps-3 disabled"
              placeholder="Recurrence"
              formControlName="recurrence"
            />
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label class="fs12 fw600" for="recurranceEnd">Recurrence End Date<sup>*</sup></label>
            <div class="input-group mb-3">
              <input  id="recurranceEnd"
                class="form-control fs10 material-input"
                #dp="bsDatepicker"
                bsDatepicker
                formControlName="recurrenceEndDate"
                placeholder="Recurrence End Date *"
                [bsConfig]="{ isAnimated: true, showWeekNumbers: false, customTodayClass: 'today' }"
                (ngModelChange)="onEditSubmitForm('')"
                [minDate]="minDateOfrecurrenceEndDate"
              /><br />

              <div
                class="color-red"
                *ngIf="
                  editSubmitted && !saveQueuedNDR && deliverEditForm.get('recurrenceEndDate').errors
                "
              >
                <small *ngIf="deliverEditForm.get('recurrenceEndDate').errors.required"
                  >*Recurrence End Date is Required.</small
                >
              </div>

                <span class="input-group-text">
                  <img
                    src="./assets/images/date.svg"
                    class="h-12px"
                    alt="Date"
                    (click)="dp.toggle()"
                    (keydown) = "dp.toggle()"
                    [attr.aria-expanded]="dp.isOpen"
                  />
                </span>
            </div>
          </div>
        </div>
      </div>
      <div class="row mt-0">
        <div class="col-md-6 primary-tooltip">
          <div class="form-group timezone-formgroup">
            <label class="fs12 fw600" for="location">Location<sup>*</sup>
              <div class="dot-border-info location-border-info tooltip location-tooltip">
                <span class="fw700 info-icon fs12">i</span>
                <span class="tooltiptext tooltiptext-info"
                  >Where will the materials/equipment be installed</span
                >
                <div class="arrow-down"></div>
              </div></label>
            <ng-multiselect-dropdown  id="location"
            [placeholder]="'Choose Location'"
            [settings]="editNdrLocationDropdownSettings"
            [data]="locationList"
            (onSelect)="locationSelected($event)"
            formControlName="LocationId"
            (ngModelChange)="onEditSubmitForm('')"
          >
          </ng-multiselect-dropdown>
          <div class="color-red" *ngIf="submitted && deliverEditForm.get('LocationId').errors">
            <small *ngIf="deliverEditForm.get('LocationId').errors.required"
              >*Location is Required.</small
            >
          </div>
            </div>
        </div>
      </div>
      <div class="row mt-2">
        <div class="col-md-12">
          <div class="form-group">
            <label class="fs12 fw600"  for="addNotes">Additional Notes</label>
            <textarea  id="addNotes"
              class="form-control fs12 min-h-65px"
              placeholder=""
              rows="2"
              formControlName="notes"
            ></textarea>
          </div>
          <p class="fs11 color-grey17 fw500 mt-2">
            The delivery needs to be approved by Project Administrator to be added to the calendar.
            Please click on submit to send the booking for approval.
          </p>
        </div>
      </div>
      <div class="mt-4 mb30 text-center editdelivery-booking-btn">
        <button
          class="btn btn-grey color-dark-grey radius20 fs12 fw-bold cairo-regular me-3 px-2rem"
          type="button"
          (click)="close(cancelConfirmation)"
        >
          Cancel
        </button>
        <button
          *ngIf="!isQueuedNDR"
          class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-2rem"
          [disabled]="(formEditSubmitted && deliverEditForm.valid) || formEdited"
          (click)="onEditSubmit('submitCurrentNDR')"
        >
          <em
            class="fa fa-spinner"
            aria-hidden="true"
            *ngIf="formEditSubmitted && deliverEditForm.valid"
          ></em
          >Submit
        </button>

      </div>
    </form>
    <!-- Edit NDR -->
  </div>
  <div class="modal-body text-center" *ngIf="modalLoader">Loading...</div>
  <!--Confirmation Popup-->
  <div id="confirm-popup6">
    <ng-template #cancelConfirmation>
      <div class="modal-body">
        <div class="text-center my-4">
          <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">
            Are you sure you want to cancel?
          </p>
          <button
            class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
            (click)="resetForm('no')"
          >
            No
          </button>
          <button
            class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
            (click)="resetForm('yes')"
          >
            Yes
          </button>
        </div>
      </div>
    </ng-template>
  </div>

