import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { ChangePasswordComponent } from './change-password.component';
import { AuthService } from '../services/auth/auth.service';
import { ProjectService } from '../services/profile/project.service';
import { of, throwError } from 'rxjs';
import { Router } from '@angular/router';

describe('ChangePasswordComponent', () => {
  let component: ChangePasswordComponent;
  let fixture: ComponentFixture<ChangePasswordComponent>;
  let authService: jest.Mocked<AuthService>;
  let projectService: jest.Mocked<ProjectService>;
  let toastrService: jest.Mocked<ToastrService>;
  let router: jest.Mocked<Router>;

  beforeEach(async () => {
    const authServiceMock = {
      changePassword: jest.fn(),
      logout: jest.fn()
    };
    const projectServiceMock = {
      ParentCompanyId: of('123')
    };
    const toastrServiceMock = {
      success: jest.fn(),
      error: jest.fn()
    };
    const routerMock = {
      navigate: jest.fn()
    };

    await TestBed.configureTestingModule({
      declarations: [ChangePasswordComponent],
      imports: [ReactiveFormsModule, ToastrModule.forRoot()],
      providers: [
        { provide: AuthService, useValue: authServiceMock },
        { provide: ProjectService, useValue: projectServiceMock },
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: Router, useValue: routerMock }
      ]
    }).compileComponents();

    authService = TestBed.inject(AuthService) as jest.Mocked<AuthService>;
    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    router = TestBed.inject(Router) as jest.Mocked<Router>;
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ChangePasswordComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the form with empty values', () => {
    expect(component.changePasswordForm.get('currentPassword').value).toBe('');
    expect(component.changePasswordForm.get('newPassword').value).toBeNull();
    expect(component.changePasswordForm.get('confirmPassword').value).toBe('');
  });

  it('should validate required fields', () => {
    component.changeFormSubmitted = true;
    component.changePassword();
    
    expect(component.changePasswordForm.get('currentPassword').errors?.required).toBeTruthy();
    expect(component.changePasswordForm.get('newPassword').errors?.required).toBeTruthy();
    expect(component.changePasswordForm.get('confirmPassword').errors?.required).toBeTruthy();
  });

  it('should validate password requirements', () => {
    const form = component.changePasswordForm;
    const newPasswordControl = form.get('newPassword');
    
    // Set a weak password that fails all validations
    newPasswordControl.setValue('weak');
    
    // Force validation
    newPasswordControl.markAsTouched();
    newPasswordControl.updateValueAndValidity({ emitEvent: true });
    
    // Wait for validation to complete
    fixture.detectChanges();
    
    // Debug output
    console.log('Form errors:', form.errors);
    console.log('New password errors:', newPasswordControl.errors);
    console.log('New password value:', newPasswordControl.value);
    console.log('New password validators:', newPasswordControl.validator);
    
    const errors = newPasswordControl.errors;
    expect(errors).toBeTruthy();
    expect(errors.hasNumber).toBeTruthy();
    expect(errors.hasCapitalCase).toBeTruthy();
    // expect(errors.hasSmallCase).toBeTruthy();
    expect(errors.hasSpecialCharacters).toBeTruthy();
    expect(errors.minlength).toBeTruthy();
  });

  it('should validate password match', () => {
    const form = component.changePasswordForm;
    form.get('newPassword').setValue('ValidPass1!');
    form.get('confirmPassword').setValue('DifferentPass1!');
    
    expect(form.get('confirmPassword').errors?.mustMatch).toBeTruthy();
  });

  it('should toggle password visibility', () => {
    expect(component.togglePassword).toBe(false);
    component.passwordToggle();
    expect(component.togglePassword).toBe(true);
  });

  it('should toggle new password visibility', () => {
    expect(component.newTogglePassword).toBe(false);
    component.newPasswordToggle();
    expect(component.newTogglePassword).toBe(true);
  });

  it('should toggle confirm password visibility', () => {
    expect(component.confirmTogglePassword).toBe(false);
    component.confirmPasswordToggle();
    expect(component.confirmTogglePassword).toBe(true);
  });

  it('should handle password validation', () => {
    const event = { target: { value: 'ValidPass1!' } };
    component.passwordValid(event);
    expect(component.passwordError).toBe(false);
  });

  it('should handle invalid password validation', () => {
    const event = { target: { value: 'weak' } };
    component.passwordValid(event);
    expect(component.passwordError).toBe(true);
  });

  it('should successfully change password', () => {
    const form = component.changePasswordForm;
    form.get('currentPassword').setValue('OldPass1!');
    form.get('newPassword').setValue('NewPass1!');
    form.get('confirmPassword').setValue('NewPass1!');

    authService.changePassword.mockReturnValue(of({ message: 'Password changed successfully' }));
    authService.logout.mockReturnValue();

    component.changePassword();

    expect(authService.changePassword).toHaveBeenCalledWith({
      oldPassword: 'OldPass1!',
      newPassword: 'NewPass1!',
      ParentCompanyId: '123'
    });
    expect(toastrService.success).toHaveBeenCalledWith('Password changed successfully', 'Success');
    expect(authService.logout).toHaveBeenCalled();
  });

  it('should handle password change error', () => {
    const form = component.changePasswordForm;
    form.get('currentPassword').setValue('OldPass1!');
    form.get('newPassword').setValue('NewPass1!');
    form.get('confirmPassword').setValue('NewPass1!');

    const error = {
      message: {
        statusCode: 400,
        details: [{ message: 'Invalid current password' }]
      }
    };

    authService.changePassword.mockReturnValue(throwError(() => error));

    component.changePassword();

    expect(toastrService.error).toHaveBeenCalledWith(['Invalid current password']);
  });

  it('should handle general error', () => {
    const form = component.changePasswordForm;
    form.get('currentPassword').setValue('OldPass1!');
    form.get('newPassword').setValue('NewPass1!');
    form.get('confirmPassword').setValue('NewPass1!');

    const error = {
      message: 'Server error'
    };

    authService.changePassword.mockReturnValue(throwError(() => error));

    component.changePassword();

    expect(toastrService.error).toHaveBeenCalledWith('Server error', 'OOPS!');
  });
});
