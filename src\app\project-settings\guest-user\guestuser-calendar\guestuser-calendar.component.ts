import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-guestuser-calendar',
  templateUrl: './guestuser-calendar.component.html',
  })
export class GuestuserCalendarComponent implements OnInit {
  public toggleClass = false;
  // public loader = true;

  public constructor() { }

  ngOnInit(): void { /* */ }

  public sideMenuChange(val: boolean): void {
    this.toggleClass = val;
    this.toggleClass = !this.toggleClass;
  }

  public hideSidebar() {
    /* */
  }

  public closeToggle() {
    this.toggleClass = false;
  }

  public handleDownKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.hideSidebar();
    }
  }
}
