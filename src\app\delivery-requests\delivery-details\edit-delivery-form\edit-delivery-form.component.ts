/* eslint-disable max-len */
/* eslint-disable max-lines-per-function */
import {
  Component, TemplateRef, OnInit, ViewChild, ElementRef,
  Input
} from '@angular/core';
import {
  UntypedFormBuilder, UntypedFormGroup, Validators, UntypedFormArray, UntypedFormControl,
} from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import moment from 'moment';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { Socket } from 'ngx-socket-io';
import { DeliveryService } from '../../../services/profile/delivery.service';
import { ProjectService } from '../../../services/profile/project.service';
import { MixpanelService } from '../../../services/mixpanel.service';
import { ProjectSettingsService } from '../../../services/project_settings/project-settings.service';
import {
  weekDays, editRecurrence, repeatWithSingleRecurrence, repeatWithMultipleRecurrence,
} from '../../../services/common';
import { TimeslotComponent } from 'src/app/layout/time-slot/time-slot.component';

type DateInput = string | number | Date;
@Component({
  selector: 'app-edit-delivery-form',
  templateUrl: './edit-delivery-form.component.html',
})
export class EditDeliveryFormComponent implements OnInit {
  @ViewChild('cancelRecurrence') cancelRecurrenceTemplate: TemplateRef<any>;

  @ViewChild('timeSlotsContainer') timeSlotsContainer: ElementRef;

  @ViewChild('timeSlotRef') timeSlotComponent: TimeslotComponent;

  @Input() seriesoption: number;

  public deliverEditForm: UntypedFormGroup;

  public ProjectId: any;

  public submitted = false;

  public escort = false;

  public formSubmitted = false;

  public editSubmitted = false;

  public timeZone: any ;

  public modalLoader = false;

  public authUser: any = {};

  public weekDates : any = [];

  public minutes: number[] = [0 ,15, 30, 45, 60]; // Common minute intervals

  hours = Array.from({ length: 24 }, (_, i) => i)

  public selectedHour: number | null = null;

  public selectedMinute: number | null = 30;

  public selectedMinutes: any;

  public durationisOpen = false; // Controls dropdown visibility

  public availableTimes = [];

  public selectedTime: string | null = null;

  public isAM = true;

  public selectedDate;

  public loader = false;

  public gateList: any = [];

  public equipmentList: any = [];

  public defineList: any = [];

  public locationList: any = [];

  public editNdrCompanyDropdownSettings: IDropdownSettings;

  public companyList: any = [];

  public lastId: any = {};

  public formEditSubmitted = false;

  public editNdrDefinableDropdownSettings: IDropdownSettings;

  public editNdrLocationDropdownSettings: IDropdownSettings;

  public currentEditItem: any = {};

  public deliveryId: any;

  public editProjectId: any;

  public ParentCompanyId: any;

  public deliveryEnd: Date;

  public deliveryStart: Date;

  public NDRTimingChanged = false;

  public editBeforeDFOW = [];

  public editBeforeCompany = [];

  public array3Value = [];

  public isQueuedNDR = false;

  public saveQueuedNDR = false;

  public formEdited = true;

  public craneEquipmentTypeChosen = false;

  public errtextenable = false;

  public errequipmentenable = false;

  public errmemberenable = false;

  public memberList: any = [];

  public checkform: any = new UntypedFormArray([]);

  public message = '';

  public enableOption = false;

  public valueExists = [];

  public endTime: Date;

  public startTime: Date;

  public deliveryWindowTime;

  public deliveryWindowTimeUnit;

  public seriesOption: number;

  public recurrenceId: number;

  public recurrenceEndDate;

  public minDateOfrecurrenceEndDate;

  public isDisabledDate = false;

  public selectedLocationId: any;

  public getChosenLocation: any;

  public isGuestBookingSelected: boolean;

  public equipmentDropdownSettings: IDropdownSettings;

  public editBeforeEquipment = [];

  endPickerTime: any;

  public vehicleTypes = [
    { id: 1, type: 'Medium and Heavy Duty Truck' },
    { id: 2, type: 'Passenger Car' },
    { id: 3, type: 'Light Duty Truck' },
  ];

  public vehicleTypeDropdownSettings: IDropdownSettings = {
    singleSelection: true,
    idField: 'id',
    textField: 'type',
    allowSearchFilter: true,
    closeDropDownOnSelection: true,
  };

  public selectedVehicleType: any;

  public vehicleTypeChosen: any;

  public recurrence = editRecurrence;

  public selectedRecurrence = 'Does Not Repeat';

  public isRepeatWithSingleRecurrence = false;

  public isRepeatWithMultipleRecurrence = false;

  public showRecurrenceTypeDropdown = false;

  public weekDays: any = weekDays;

  public monthlyDayOfWeek = '';

  public monthlyDate = '';

  public monthlyLastDayOfWeek = '';

  public repeatWithSingleRecurrence = repeatWithSingleRecurrence;

  public repeatWithMultipleRecurrence = repeatWithMultipleRecurrence;

  public recurrenceMinDate = new Date();

  public daysEdited = false;

  public valueEdited = false;

  public previousRecurrence: any;

  public previousEndDate: Date;

  public previousRepeatEveryCount: any;

  public previousRepeatEveryType: any;

  public previousChosenDateOfMonth: any;

  public previousDays: any;

  public previousDateOfMonth: any;

  public previousMonthlyRepeatType: any;

  public previousChosenDateOfMonthValue: any;

  public payloadDays: any = [];

  public recurrenceEdited = false;

  public action2: any;

  public noEquipmentOption = { id: 0, equipmentName: 'No Equipment Needed' };

  public constructor(
    private readonly formBuilder: UntypedFormBuilder,
    public socket: Socket,
    private readonly toastr: ToastrService,
    public router: Router,
    public modalRef: BsModalRef,
    private readonly modalService: BsModalService,
    public modalRef1: BsModalRef,
    public modalRef2: BsModalRef,
    private readonly mixpanelService: MixpanelService,
    private readonly deliveryService: DeliveryService,
    public projectService: ProjectService,
    public projectSettingsService: ProjectSettingsService,
  ) {
    this.projectService.projectParent.subscribe((response): void => {
      this.getProjectIdAndParentCompanyId(response);
    });
    this.projectService.accountProjectParent.subscribe((res): void => {
      this.getProjectIdAndParentCompanyId(res);
    });
    this.editDetailsForm();
    this.getMembers();
    this.getProjectSettings();
  }

  public getProjectIdAndParentCompanyId(response: any): void {
    if (response !== undefined && response !== null && response !== '') {
      this.ProjectId = response.ProjectId;
      this.ParentCompanyId = response.ParentCompanyId;
      this.loader = true;
    }
  }

  public vehicleTypeSelected(data: any): void {
    const vehicleTypeChosen = this.vehicleTypes.find((obj: any): any => +obj.id === +data.id);
    this.selectedVehicleType = vehicleTypeChosen?.type;
  }

  public setDefaultPerson(): void {
    const newMemberList = [
      {
        email: this.authUser.User.email,
        id: this.authUser.id,
        readonly: true,
      },
    ];
    this.deliverEditForm.get('person').patchValue(newMemberList);
  }

  getBookingData() {
    const equipmentId = this.deliverEditForm.get('EquipmentId').value;
    const locationId = this.deliverEditForm.get('LocationId').value[0].id;
    const gateId = this.deliverEditForm.get('GateId').value;

    if (this.timeSlotComponent) {
      this.timeSlotComponent.getEventNDR(equipmentId, locationId, gateId, this.timeZone,this.deliverEditForm.get('deliveryDate').value);
    }
  }

  public getNDR(): void {
    this.loader = true;
    const param = {
      DeliveryRequestId: this.deliveryId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.getNDRData(param).subscribe((res): void => {
      this.currentEditItem = res.data;
      this.deliverEditForm.get('id').setValue(this.currentEditItem.id);
      this.deliverEditForm.get('DeliveryId').setValue(this.currentEditItem.DeliveryId);
      this.deliverEditForm.get('CraneRequestId').setValue(this.currentEditItem.CraneRequestId);
      this.deliverEditForm.get('description').setValue(this.currentEditItem.description);
      this.deliverEditForm
        .get('deliveryDate')
        .setValue(moment(this.currentEditItem.deliveryStart).format('MM/DD/YYYY'));
      this.deliverEditForm
        .get('deliveryStart')
        .setValue(new Date(this.currentEditItem.deliveryStart));
      this.deliverEditForm.get('deliveryEnd').setValue(new Date(this.currentEditItem.deliveryEnd));
      this.previousEndDate = this.deliverEditForm.get('deliveryEnd').value;
      this.deliverEditForm.get('escort').setValue(this.currentEditItem.escort);
      this.deliverEditForm.get('vehicleDetails').setValue(this.currentEditItem.vehicleDetails);
      this.deliverEditForm.get('notes').setValue(this.currentEditItem.notes);
      this.deliverEditForm.get('recurrenceId').setValue(this.currentEditItem?.recurrence?.id);
      this.deliverEditForm.get('recurrence').setValue(this.currentEditItem?.recurrence?.recurrence);
      this.previousRecurrence = this.deliverEditForm.get('recurrence').value;
      this.selectedRecurrence = this.deliverEditForm.get('recurrence').value;
      this.deliverEditForm.get('repeatEveryCount').setValue(+this.currentEditItem?.recurrence?.repeatEveryCount);
      this.previousRepeatEveryCount = this.deliverEditForm.get('repeatEveryCount').value;
      this.deliverEditForm.get('repeatEveryType').setValue(this.currentEditItem?.recurrence?.repeatEveryType);
      this.previousRepeatEveryType = this.deliverEditForm.get('repeatEveryType').value;
      this.deliverEditForm.get('chosenDateOfMonth').setValue(this.currentEditItem?.recurrence?.chosenDateOfMonthValue);
      this.previousChosenDateOfMonth = this.currentEditItem?.recurrence?.chosenDateOfMonth;
      this.previousChosenDateOfMonthValue = this.currentEditItem?.recurrence?.chosenDateOfMonthValue;
      this.deliverEditForm.get('dateOfMonth').setValue(this.currentEditItem?.recurrence?.dateOfMonth);
      this.previousDateOfMonth = this.deliverEditForm.get('dateOfMonth').value;
      this.deliverEditForm.get('monthlyRepeatType').setValue(this.currentEditItem?.recurrence?.monthlyRepeatType);
      this.previousMonthlyRepeatType = this.deliverEditForm.get('monthlyRepeatType').value;
      this.previousDays = this.currentEditItem?.recurrence?.days;
      this.deliverEditForm.get('originationAddress').setValue(this.currentEditItem?.OriginationAddress);
      if (this.currentEditItem?.recurrence?.recurrenceEndDate) {
        this.deliverEditForm
          .get('recurrenceEndDate')
          .setValue(
            new Date(this.currentEditItem?.recurrence?.recurrenceEndDate),
          );
      }
      this.minDateOfrecurrenceEndDate = new Date(
        this.currentEditItem?.recurrence?.recurrenceEndDate,
      );
      this.deliverEditForm.get('GateId').setValue(this.currentEditItem.gateDetails[0]?.Gate?.id);
      const { isAssociatedWithCraneRequest } = this.currentEditItem;
      if (isAssociatedWithCraneRequest) {
        this.deliverEditForm
          .get('isAssociatedWithCraneRequest')
          .setValue(this.currentEditItem.isAssociatedWithCraneRequest);
      } else {
        this.deliverEditForm.get('isAssociatedWithCraneRequest').setValue(false);
      }
      this.deliverEditForm
        .get('cranePickUpLocation')
        .setValue(this.currentEditItem.cranePickUpLocation);
      this.deliverEditForm
        .get('craneDropOffLocation')
        .setValue(this.currentEditItem.craneDropOffLocation);
      if (this.deliverEditForm.get('isAssociatedWithCraneRequest').value) {
        this.craneEquipmentTypeChosen = true;
      }
      this.setRepeat(this.currentEditItem?.recurrence);
      this.setlocation();
      this.setVehicleType();
      this.setCompany();
      this.setEquipment();
      this.setDefine();
      this.setMember();
      this.openContentModal();
      this.seriesOption = this.modalRef?.content?.seriesOption?this.modalRef.content.seriesOption:this.seriesoption;
      this.isDisabledDate = this.seriesOption !== 1;
      this.getBookingData()
    });
  }

  public durationToggleDropdown() {
    this.durationisOpen = !this.durationisOpen;
  }

  public durationCloseDropdown() {
    this.durationisOpen = false;
  }

  public selectHour(hour: number) {
    this.selectedHour = hour;
    const totalMinutes = (this.selectedHour * 60) + this.selectedMinute;
    const deliveryStartValue = this.deliverEditForm.get('deliveryStart')?.value;
    this.deliveryStart = new Date(deliveryStartValue);
    this.deliveryEnd = new Date(deliveryStartValue); // Start with the same date
    this.deliveryEnd.setMinutes(this.deliveryStart.getMinutes() + totalMinutes);

    this.deliverEditForm.get('deliveryEnd')?.setValue(this.deliveryEnd);
    this.endPickerTime = moment(this.deliveryEnd).format('HH:mm');
    this.durationCloseDropdown()
    this.updateDropdownState();
    this.selectDuration(hour)
  }



  public selectMinute(minute: number) {
    this.selectedMinute = minute;
    const totalMinutes = (this.selectedHour * 60) + this.selectedMinute;
    const deliveryStartValue = this.deliverEditForm.get('deliveryStart')?.value;
    this.deliveryStart = new Date(deliveryStartValue);
    this.deliveryEnd = new Date(deliveryStartValue); // Start with the same date
    this.deliveryEnd.setMinutes(this.deliveryStart.getMinutes() + totalMinutes);

    this.deliverEditForm.get('deliveryEnd')?.setValue(this.deliveryEnd);
    this.endPickerTime = moment(this.deliveryEnd).format('HH:mm');
    this.durationCloseDropdown()
    this.updateDropdownState();
    this.selectDuration(minute)
  }


  public updateDropdownState() {
    // Close the dropdown after selecting both hour and minute
    if (this.selectedHour !== null && this.selectedMinutes !== null) {
      this.durationisOpen = false;
    }
  }

  public generateWeekDates(selectedDate: Date | string): void {
    this.weekDates = [];

    for (let i = 0; i < 5; i++) {
      const nextDate = moment(selectedDate).add(i, 'days'); // Get the next 5 dates

      const dayName = nextDate.format('ddd'); // Get short day name like 'Wed'
      const day = nextDate.format('DD'); // Extract the day (e.g., '05')
      const fullDate = nextDate.format('YYYY-MM-DD'); // Full date like '2024-11-06'

      this.weekDates.push({
        name: dayName,
        date: day,
        fullDate: fullDate,
      });
    }
    this.selectedDate = this.weekDates[0]
  }

  public selectDate(day: any) {
    this.selectedDate = day;
  }

  public convertStart(deliveryDate: Date, startHours: number, startMinutes: number): Date {
    const fullYear = deliveryDate.getFullYear();
    const fullMonth = deliveryDate.getMonth();
    const date = deliveryDate.getDate();
    const deliveryNewStart = new Date(fullYear, fullMonth, date, startHours, startMinutes);
    return deliveryNewStart;
  }

  public requestAutoEditcompleteItems = (text: string): Observable<any> => {
    const param = {
      ProjectId: this.ProjectId,
      search: text,
      ParentCompanyId: this.ParentCompanyId,
    };
    return this.deliveryService.searchNewMember(param);
  };


  public selectTime(startStr: string, endStr: string) {
    const startDate = new Date(startStr);
    const endDate = new Date(endStr);

    this.deliverEditForm.patchValue({
      deliveryStart: startDate,
      deliveryEnd: endDate,
      deliveryDate: startDate
    });

    this.selectedTime = `${startDate.toLocaleTimeString()} - ${endDate.toLocaleTimeString()}`;
    this.NDRTimingChanged = true;
    this.onEditSubmitForm(null);
  }

  public scrollToTime(index: number) {
    const container = this.timeSlotsContainer.nativeElement;
    const buttons = container.querySelectorAll('button');

    if (buttons[index]) {
      const selectedButton = buttons[index];
      container.scrollTop = selectedButton.offsetTop - container.offsetTop;
    }
  }

  public setCompany(): void {
    const { companyDetails } = this.currentEditItem;
    const newCompanyList = [];
    if (companyDetails !== undefined) {
      companyDetails.forEach((element: { Company: { id: any; companyName: any } }): void => {
        const data = {
          id: element.Company.id,
          companyName: element.Company.companyName,
        };
        newCompanyList.push(data);
      });
    }
    this.editBeforeCompany = newCompanyList;
    this.deliverEditForm.get('companyItems').patchValue(newCompanyList);
  }

  public selectDuration(event) {
    this.selectedMinutes = (this.selectedHour * 60) + this.selectedMinute
  }

  public areDifferentByProperty(a: any[], b: any[], prop: string): boolean {
    const array1 = a.map((x: { [x: string]: any }): any => x[prop]);
    const array2 = b.map((x: { [x: string]: any }): any => x[prop]);
    this.array3Value = array1.concat(array2);
    this.array3Value = [...new Set([...array1, ...array2])];
    return this.array3Value.length !== array1.length;
  }

  public setDefine(): void {
    const { defineWorkDetails } = this.currentEditItem;
    const newDefineList = [];
    if (defineWorkDetails !== undefined) {
      defineWorkDetails.forEach((element: { DeliverDefineWork: { id: any; DFOW: any } }): void => {
        const data = {
          id: element.DeliverDefineWork.id,
          DFOW: element.DeliverDefineWork.DFOW,
        };
        newDefineList.push(data);
      });
    }
    this.editBeforeDFOW = newDefineList;
    this.deliverEditForm.get('defineItems').patchValue(newDefineList);
  }

  public setMember(): void {
    const { memberDetails } = this.currentEditItem;
    const newMemberList = [];
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.deliveryService.getMemberRole(params).subscribe((res): void => {
      this.authUser = res.data;

      if (memberDetails !== undefined) {
        memberDetails.forEach(
          (element: {
            Member: { User: { firstName: any; lastName: any; email: any }; id: any; isGuestUser: any };
          }): void => {
            let email: string;
            if (element.Member?.User?.firstName != null && element.Member?.isGuestUser === false) {
              email = `${element.Member?.User?.firstName} ${element.Member?.User?.lastName} (${element.Member?.User?.email})`;
            } else if (element.Member?.User?.firstName != null && element.Member?.isGuestUser === true) {
              email = `${element.Member?.User?.firstName} ${element.Member?.User?.lastName} (${element.Member?.User?.email} - Guest)`;
            } else if (element.Member?.User?.firstName == null && element.Member?.isGuestUser === false) {
              email = `(${element.Member?.User?.email})`;
            } else if (element.Member?.User?.firstName == null && element.Member?.isGuestUser === true) {
              email = `(${element.Member?.User?.email} - Guest)`;
            }
            const data: any = {
              email,
              id: element.Member?.id,
            };
            if (element.Member?.User?.email === this.authUser?.User?.email) {
              data.readonly = true;
            }
            if (element.Member?.User?.email) {
              newMemberList.push(data);
            }
          },
        );
      }
    });
    this.deliverEditForm.get('person').patchValue(newMemberList);
  }

  public ngOnInit(): void {
    this.seriesOption = this.modalRef?.content?.seriesOption ? this.modalRef?.content?.seriesOption : this.seriesoption;
    this.isDisabledDate = this.seriesOption !== 1;
    this.router.events.subscribe((e): void => {
      this.modalRef.hide();
    });
    this.deliveryService.isQueuedNDR.subscribe((res): void => {
      if (res === 'queued') {
        this.isQueuedNDR = true;
      }
    });
  }

  public ngAfterViewInit(): void {
    this.deliveryService.DeliveryRequestId.subscribe((getDeliveryRequestIdResponse4): void => {
      this.deliveryId = getDeliveryRequestIdResponse4;
      this.getOverAllGate();
    }).unsubscribe();
    this.deliveryService.loginUser.subscribe((res): void => {
      if (res !== undefined && res !== null && res !== '') {
        this.authUser = res;
      }
    }).unsubscribe();
  }

  public getOverAllGate(): void {
    this.modalLoader = true;
    const params = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .gateList(params, { isFilter: true, showActivatedAlone: true })
      .subscribe((res): void => {
        this.gateList = res.data;
        this.getOverAllEquipmentforEditNdr();
      });
  }

  public getMembers(): void {
    const param = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.listAllMember(param).subscribe((response: any): void => {
      if (response) {
        this.memberList = response.data;
      }
    });
  }

  public checkDate(data: { target: { value: number } }): void {
    if (data.target.value >= 25) {
      this.deliverEditForm.get('deliveryStart').setValue(new Date());
    }
  }

  public numberOnly(event: { which: any; keyCode: any }): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }

  public onEditSubmitForm(value: any): void {
    if (this.deliverEditForm.dirty || this.NDRTimingChanged || this.daysEdited) {
      this.formEdited = false;
      this.valueEdited = true;
    }
    if (value) {
      let findEquipmentType: any;
      let count = 0;
      if(value.length == this.equipmentList.length -1 && this.equipmentList[0].id == 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
      }
      if(value.length != this.equipmentList.length && this.equipmentList[0].id != 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
      }
      if(value.length == 0) {
        this.equipmentList = this.equipmentList.filter(item => item.id !== 0);
        this.equipmentList = [this.noEquipmentOption, ...this.equipmentList];
      }
      let hasNoEquipmentOption = false;
      let hasOtherEquipment = false;
      // Check if "No Equipment Needed" (id = 0) is selected
      hasNoEquipmentOption = value.some((item: any) => item.id === 0);

      // Check if other equipment is selected
      hasOtherEquipment = value.some((item: any) => item.id !== 0);

      const previousSelection = this.deliverEditForm.get('EquipmentId').value || [];
      const previousHasOther = previousSelection.some((item: any) => item.id !== 0);

      // Rule 1: If "No Equipment Needed" is selected and other items are selected, keep only "No Equipment Needed"
      if (hasNoEquipmentOption && hasOtherEquipment && !previousHasOther) {
        this.toastr.warning('When "No Equipment Needed" is selected, other equipment options cannot be selected.', 'Warning');
        const noEquipmentOnly = value.filter((item: any) => item.id === 0);
        this.deliverEditForm.get('EquipmentId').setValue(noEquipmentOnly);
        value = noEquipmentOnly;
        hasOtherEquipment = false;
      }

      // Rule 2: If other equipment is already selected and "No Equipment Needed" is now selected, remove it
      if (previousHasOther && hasNoEquipmentOption) {
        this.toastr.warning('When other equipment is selected, "No Equipment Needed" cannot be selected.', 'Warning');
        const filteredSelection = value.filter((item: any) => item.id !== 0);
        this.deliverEditForm.get('EquipmentId').setValue(filteredSelection);
        value = filteredSelection;
        hasNoEquipmentOption = false;
      }

      for (let i = 0; i < value.length; i++) {
        findEquipmentType = this.equipmentList.find((item) => item.id === value[i].id);
        if (findEquipmentType) {
          this.craneEquipmentTypeChosen = findEquipmentType?.PresetEquipmentType?.isCraneType;
          if (this.craneEquipmentTypeChosen) {
            count++;
            break; // stop iteration
          }
        }
      }
      if (count > 0) {
        this.getLastCraneRequestId();
      }
      if (value.length === 0) {
        this.craneEquipmentTypeChosen = false;
        this.deliverEditForm.get('isAssociatedWithCraneRequest').setValue(false);
      }
      if (findEquipmentType) {
        this.getBookingData();
      }
    }
  }

  public getLastCraneRequestId(): void {
    const params = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService.getLastCraneRequestId(params).subscribe((response): void => {
      this.craneEquipmentTypeChosen = true;
      const isValueExists = this.deliverEditForm.get('CraneRequestId').value;
      if (!isValueExists) {
        this.deliverEditForm.get('CraneRequestId').setValue(response.lastId?.CraneRequestId);
      }
    });
  }

  public gatecheck(value, menuname): void {
    if (menuname === 'Gate') {
      const index = this.gateList.findIndex((i): boolean => i.id === value);
      if (index !== -1) {
        this.errtextenable = false;
      }
    } else if (menuname === 'Equipment') {
      const index = this.equipmentList.findIndex((i): boolean => i.id === value);
      if (index !== -1) {
        this.errequipmentenable = false;
      }
    } else if (menuname === 'Person') {
      const result = this.memberList.filter((o) => value.some(({ id }) => o.id === id));
      if (value.length !== result.length) {
        this.errmemberenable = true;
      } else {
        this.errmemberenable = false;
      }
    }
  }

  public indexBasedSubmit({
    formValue,
    deliveryStart,
    deliveryEnd,
    companies,
    persons,
    define,
    action,
    equipments,
  }) {
    const index = this.gateList.findIndex((i): boolean => i.id === formValue.GateId);
    const arr1 = this.memberList;
    const arr2 = formValue.person;
    let index2: number;
    const result = arr1.filter((o) => arr2.some(({ id }) => o.id === id));
    if (formValue.person.length !== result.length) {
      index2 = -1;
    } else {
      index2 = 0;
    }
    if ((index === -1 || index2 === -1) && action !== 'save') {
      if (index === -1) {
        this.errtextenable = true;
      }
      if (index2 === -1) {
        this.errmemberenable = true;
      }

      this.formEditSubmitted = false;
    } else {
      if (index === -1) {
        this.errtextenable = false;
      }
      if (index2 === -1) {
        this.errmemberenable = false;
      }
      this.checkRequestStatusAndUserRole({
        formValue,
        deliveryStart,
        deliveryEnd,
        companies,
        persons,
        define,
        action,
        equipments,
      });
    }
  }

  public onEditSubmit(action: string): void {
    if (action === 'save') {
      this.saveQueuedNDR = true;
    } else {
      this.formEditSubmitted = true;
    }
    this.editSubmitted = true;
    this.errtextenable = false;
    const companies = [];
    const persons = [];
    const define = [];
    const equipments = [];
    if (this.deliverEditForm.invalid && action !== 'save') {
      this.formEditSubmitted = false;
      return;
    }
    const formValue = this.deliverEditForm.value;
    if (formValue.EquipmentId.length <= 0) {
      this.toastr.error('Equipment is required');
      this.formEditSubmitted = false;
      return;
    }
    const deliveryDate = new Date(formValue.deliveryDate);
    const startNewDate = new Date(formValue.deliveryStart);
    const startHours = startNewDate.getHours();
    const startMinutes = startNewDate.getMinutes();
    const deliveryStart = this.convertStart(deliveryDate, startHours, startMinutes);
    const endNewDate = new Date(formValue.deliveryEnd);
    const endHours = endNewDate.getHours();
    const endMinutes = endNewDate.getMinutes();
    const deliveryEnd = this.convertStart(deliveryDate, endHours, endMinutes);
    if (this.checkEditDeliveryFutureDate(deliveryStart, deliveryEnd, action)) {
      this.indexBasedSubmit({
        formValue,
        deliveryStart,
        deliveryEnd,
        companies,
        persons,
        define,
        action,
        equipments,
      });
    } else {
      this.checkRequestStatusAndUserRole({
        formValue,
        deliveryStart,
        deliveryEnd,
        companies,
        persons,
        define,
        action,
        equipments,
      });
    }
  }

  public checkRequestStatusAndUserRole({
    formValue,
    deliveryStart,
    deliveryEnd,
    companies,
    persons,
    define,
    action,
    equipments,
  }): void {
    if (this.currentEditItem.status === 'Delivered' && this.authUser.RoleId !== 2) {
      if (
        moment(this.currentEditItem.deliveryStart).format('MM/DD/YYYY')
          !== moment(this.deliverEditForm.get('deliveryDate').value).format('MM/DD/YYYY')
        || new Date(this.currentEditItem.deliveryStart).getTime()
          !== new Date(this.deliverEditForm.get('deliveryStart').value).getTime()
        || new Date(this.currentEditItem.deliveryEnd).getTime()
          !== new Date(this.deliverEditForm.get('deliveryEnd').value).getTime()
      ) {
        this.toastr.error('You are not allowed to change the date/time ');
        this.formEditSubmitted = false;
        return;
      }
    }
    if (this.currentEditItem.status === 'Approved' && this.authUser.RoleId !== 2) {
      if (
        moment(this.currentEditItem.deliveryDate).format('MM/DD/YYYY')
          !== moment(this.deliverEditForm.get('deliveryDate').value).format('MM/DD/YYYY')
        || new Date(this.currentEditItem.deliveryStart).getTime()
          !== new Date(this.deliverEditForm.get('deliveryStart').value).getTime()
        || new Date(this.currentEditItem.deliveryEnd).getTime()
          !== new Date(this.deliverEditForm.get('deliveryEnd').value).getTime()
      ) {
        if (!this.checkEditDeliveryFutureDate(deliveryStart, deliveryEnd, action)) {
          this.toastr.error(
            'Booking not allowed to edit. Please contact the project administrator to edit this booking',
          );
          this.formEditSubmitted = false;
          return;
        }
      }
    }
    if (action === 'submitCurrentNDR' || action === 'submitQueuedNDR') {
      this.updateDelivery({
        editNdrFormValue: formValue,
        deliveryStart,
        deliveryEnd,
        companies,
        persons,
        define,
        action,
        equipments,
      });
    }
    if (action === 'save') {
      this.updateQueuedDelivery(formValue, deliveryStart, deliveryEnd, companies, persons, define, equipments);
    }
  }


  public constructQueueData(updateQueuedNdrFormValue, companies, persons, define, equipments, updateQueuedNdrPersonsDetails): void {
    if (updateQueuedNdrFormValue.companyItems.length > 0) {
      updateQueuedNdrFormValue.companyItems.forEach((element: { id: any }): void => {
        companies.push(element.id);
      });
    }
    if (updateQueuedNdrPersonsDetails.length > 0) {
      updateQueuedNdrPersonsDetails.forEach((element: { id: any }): void => {
        persons.push(element.id);
      });
    }
    if (updateQueuedNdrFormValue.defineItems.length > 0) {
      updateQueuedNdrFormValue.defineItems.forEach((element: { id: any }): void => {
        define.push(element.id);
      });
    }
    if (updateQueuedNdrFormValue.EquipmentId.length > 0) {
      updateQueuedNdrFormValue.EquipmentId.forEach((element: { id: any }): void => {
        equipments.push(element.id);
      });
    }
  }

  public updateQueuedDelivery(
    updateQueuedNdrFormValue,
    deliveryStart: any,
    deliveryEnd: any,
    companies: any[],
    persons: any[],
    define: any[],
    equipments: any[],
  ): void {
    // eslint-disable-next-line no-restricted-globals
    if (deliveryStart && deliveryEnd && !isNaN(deliveryStart) && !isNaN(deliveryEnd)) {
      if (!this.checkStartEnd(deliveryStart, deliveryEnd)) {
        this.throwError('time error');
        return;
      }
    }
    const updateQueuedNdrCompanyDetails = updateQueuedNdrFormValue.companyItems;
    const updateQueuedNdrPersonsDetails = updateQueuedNdrFormValue.person;
    const updateQueuedNdrDefinableDetails = updateQueuedNdrFormValue.defineItems;
    const updateQueuedNdrEquipmentDetails = updateQueuedNdrFormValue.EquipmentId;
    if (
      updateQueuedNdrCompanyDetails !== null
      && updateQueuedNdrPersonsDetails !== null
      && updateQueuedNdrDefinableDetails !== null
      && updateQueuedNdrEquipmentDetails !== null
    ) {
      this.constructQueueData(
        updateQueuedNdrFormValue,
        companies,
        persons,
        define,
        equipments,
        updateQueuedNdrPersonsDetails,
      );
      if (!this.checkEditDeliveryStringEmptyValues(updateQueuedNdrFormValue)) {
        const queuedNdrPayload = this.constructNdrPayload({
          updateQueuedNdrFormValue,
          deliveryStart,
          deliveryEnd,
          companies,
          persons,
          define,
          equipments,
        });
        this.editQueuedNDR(queuedNdrPayload);
      } else {
        this.formReset();
      }
    }
  }

  public constructNdrPayload({
    updateQueuedNdrFormValue,
    deliveryStart,
    deliveryEnd,
    companies,
    persons,
    define,
    equipments,
  }: {
    updateQueuedNdrFormValue: any;
    deliveryStart: any;
    deliveryEnd: any;
    companies: any[];
    persons: any[];
    define: any[];
    equipments: any[];
  }) {
    const escortCondition = updateQueuedNdrFormValue.escort === null
          || updateQueuedNdrFormValue.escort === undefined
          || updateQueuedNdrFormValue.escort === '';
    const queuedNdrPayload: any = {
      id: updateQueuedNdrFormValue.id,
      description: updateQueuedNdrFormValue.description,
      companies,
      escort: escortCondition ? false : updateQueuedNdrFormValue.escort,
      ProjectId: this.ProjectId,
      GateId: updateQueuedNdrFormValue.GateId,
      notes: updateQueuedNdrFormValue.notes,
      EquipmentId: equipments,
      vehicleDetails: updateQueuedNdrFormValue.vehicleDetails,
      deliveryStart,
      deliveryEnd,
      ParentCompanyId: this.ParentCompanyId,
      persons,
      define,
      isAssociatedWithCraneRequest: updateQueuedNdrFormValue.isAssociatedWithCraneRequest,
      requestType: 'deliveryRequest',
      seriesOption: this.seriesOption,
      recurrenceId: updateQueuedNdrFormValue.recurrenceId,
      LocationId: this.selectedLocationId,
      recurrenceEndDate: updateQueuedNdrFormValue.recurrenceEndDate
        ? moment(updateQueuedNdrFormValue.recurrenceEndDate).format('YYYY-MM-DD')
        : null,
      recurrenceSeriesStartDate: moment(updateQueuedNdrFormValue.deliveryStart).format(
        'YYYY-MM-DD',
      ),
      recurrenceSeriesEndDate: moment(updateQueuedNdrFormValue.deliveryEnd).format(
        'YYYY-MM-DD',
      ),
      previousSeriesRecurrenceEndDate: moment(updateQueuedNdrFormValue.deliveryStart)
        .add(-1, 'days')
        .format('YYYY-MM-DD'),
      nextSeriesRecurrenceStartDate: moment(updateQueuedNdrFormValue.deliveryStart)
        .add(1, 'days')
        .format('YYYY-MM-DD'),
      deliveryStartTime: moment(updateQueuedNdrFormValue.deliveryStart).format('HH:mm'),
      deliveryEndTime: moment(updateQueuedNdrFormValue.deliveryEnd).format('HH:mm'),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    };
    if (updateQueuedNdrFormValue.isAssociatedWithCraneRequest) {
      queuedNdrPayload.cranePickUpLocation = updateQueuedNdrFormValue.cranePickUpLocation.trim();
      queuedNdrPayload.craneDropOffLocation = updateQueuedNdrFormValue.craneDropOffLocation.trim();
      queuedNdrPayload.CraneRequestId = updateQueuedNdrFormValue.CraneRequestId;
      queuedNdrPayload.requestType = 'deliveryRequestWithCrane';
    } else {
      queuedNdrPayload.cranePickUpLocation = null;
      queuedNdrPayload.craneDropOffLocation = null;
      queuedNdrPayload.CraneRequestId = null;
      queuedNdrPayload.requestType = 'deliveryRequest';
    }
    queuedNdrPayload.ndrStatus = 'saveQueuedNDR';
  }

  public locationSelected(data): void {
    this.getChosenLocation = this.locationList.filter((obj: any): any => +obj.id === +data.id);
    if (this.getChosenLocation) {
      this.gateList = []
      this.gateList = this.getChosenLocation[0].gateDetails;
      this.equipmentList = []
      this.equipmentList = this.getChosenLocation[0].EquipmentId?this.getChosenLocation[0].EquipmentId : [];
      if (this.equipmentList.length) {
        let newEquipmentList = []
        this.equipmentList.forEach((element) => {
          const equipmentdata = {
            id: element.id,
            equipmentName: element.equipmentName,
          };
          newEquipmentList.push(equipmentdata);
        });
        this.editBeforeEquipment = newEquipmentList;
        this.deliverEditForm.get('EquipmentId').patchValue(newEquipmentList);
      } else {
        this.deliverEditForm.get('EquipmentId').patchValue([]);
      }
      this.timeZone = this.getChosenLocation[0].TimeZoneId?this.getChosenLocation[0].TimeZoneId[0].location:''
    }
    this.selectedLocationId = this.getChosenLocation[0]?.id;
  }

  public setlocation(): void {
    if (this.currentEditItem.location) {
      this.getChosenLocation = [];
      const data = {
        id: this.currentEditItem.location.id,
        locationPath: this.currentEditItem.location.locationPath,
      };
      this.getChosenLocation.push(data);
      this.deliverEditForm.get('LocationId').patchValue(this.getChosenLocation);
      this.selectedLocationId = this.currentEditItem?.location?.id;
      this.timeZone = this.currentEditItem.location.TimeZoneId?this.currentEditItem.location.TimeZoneId[0].location:''
    }
  }

  public setVehicleType(): void {
    if (this.currentEditItem.vehicleType) {
      this.vehicleTypeChosen = [];
      const getVehicleChosen = this.vehicleTypes.find((obj: any): any => obj.type === this.currentEditItem.vehicleType);
      const data = {
        id: getVehicleChosen.id,
        type: getVehicleChosen.type,
      };
      this.vehicleTypeChosen.push(data);
      this.deliverEditForm.get('vehicleType').patchValue(this.vehicleTypeChosen);
      this.selectedVehicleType = getVehicleChosen?.type;
    }
  }

  public handleAddressChange(address): void {
    this.deliverEditForm.get('originationAddress').setValue(address.formatted_address);
  }

  public updateDelivery(params: {
    editNdrFormValue;
    deliveryStart: Date;
    deliveryEnd: Date;
    companies: any[];
    persons: any[];
    define: any[];
    action: string;
    equipments: any[];
  }): void {
    const {
      editNdrFormValue, deliveryStart, deliveryEnd, companies, persons, define, action, equipments,
    } = params;

    if (!this.checkStartEnd(deliveryStart, deliveryEnd)) {
      this.throwError('time error');
      return;
    }

    const valid = this.validateAndExtractFormValues(editNdrFormValue, companies, persons, define, equipments);
    if (!valid) return;

    const editNdrPayload = this.constructEditNdrPayload(editNdrFormValue, companies, persons, define, equipments, deliveryStart, deliveryEnd);
    this.submitEditNdrRequest(editNdrPayload, action);
  }

  public validateAndExtractFormValues(editNdrFormValue, companies, persons, define, equipments): boolean {
    const companyDetails = editNdrFormValue.companyItems || [];
    const personDetails = editNdrFormValue.person || [];
    const defineItems = editNdrFormValue.defineItems || [];

    if (companyDetails.length === 0) {
      this.formReset();
      this.toastr.error('Responsible Company is required');
      return false;
    }

    if (personDetails.length === 0) {
      this.formReset();
      this.toastr.error('Responsible Person is required');
      return false;
    }

    companyDetails.forEach((el: { id: any }) => companies.push(el.id));
    personDetails.forEach((el: { id: any }) => persons.push(el.id));
    defineItems.forEach((el: { id: any }) => define.push(el.id));
    (editNdrFormValue.EquipmentId || []).forEach((el: { id: any }) => equipments.push(el.id));

    if (this.checkEditDeliveryStringEmptyValues(editNdrFormValue)) {
      this.formReset();
      return false;
    }

    return true;
  }

  public constructEditNdrPayload(editNdrFormValue, companies, persons, define, equipments, deliveryStart: Date, deliveryEnd: Date): any {
    const escortCondition = !editNdrFormValue.escort;

    const payload: any = {
      id: editNdrFormValue.id,
      description: editNdrFormValue.description,
      companies,
      escort: escortCondition ? false : editNdrFormValue.escort,
      ProjectId: this.ProjectId,
      GateId: editNdrFormValue.GateId,
      notes: editNdrFormValue.notes,
      EquipmentId: equipments,
      vehicleDetails: editNdrFormValue.vehicleDetails,
      deliveryStart,
      deliveryEnd,
      ParentCompanyId: this.ParentCompanyId,
      persons,
      define,
      isAssociatedWithCraneRequest: editNdrFormValue.isAssociatedWithCraneRequest,
      requestType: 'deliveryRequest',
      DeliveryId: editNdrFormValue.DeliveryId,
      seriesOption: this.seriesOption,
      recurrenceId: editNdrFormValue.recurrenceId,
      LocationId: this.selectedLocationId,
      recurrenceEndDate: editNdrFormValue.recurrenceEndDate ? moment(editNdrFormValue.recurrenceEndDate).format('YYYY-MM-DD') : null,
      recurrenceSeriesStartDate: moment(editNdrFormValue.deliveryStart).format('YYYY-MM-DD'),
      recurrenceSeriesEndDate: moment(editNdrFormValue.deliveryEnd).format('YYYY-MM-DD'),
      previousSeriesRecurrenceEndDate: moment(editNdrFormValue.deliveryStart).add(-1, 'days').format('YYYY-MM-DD'),
      nextSeriesRecurrenceStartDate: moment(editNdrFormValue.deliveryStart).add(1, 'days').format('YYYY-MM-DD'),
      deliveryStartTime: moment(editNdrFormValue.deliveryStart).format('HH:mm'),
      deliveryEndTime: moment(editNdrFormValue.deliveryEnd).format('HH:mm'),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      originationAddress: editNdrFormValue.originationAddress,
      vehicleType: this.selectedVehicleType,
      recurrence: editNdrFormValue.recurrence,
      chosenDateOfMonthValue: editNdrFormValue.chosenDateOfMonth !== false ? editNdrFormValue.chosenDateOfMonth : 1,
      chosenDateOfMonth: editNdrFormValue.chosenDateOfMonth === 1,
      dateOfMonth: editNdrFormValue.dateOfMonth,
      monthlyRepeatType: editNdrFormValue.monthlyRepeatType,
      days: this.payloadDays,
      repeatEveryType: editNdrFormValue.repeatEveryType || null,
      repeatEveryCount: editNdrFormValue.repeatEveryCount ? editNdrFormValue.repeatEveryCount.toString() : null,
      recurrenceEdited: this.recurrenceEdited,
    };

    if (payload.isAssociatedWithCraneRequest) {
      if (!this.deliverEditForm.get('cranePickUpLocation').value || !this.deliverEditForm.get('craneDropOffLocation').value) {
        this.formSubmitted = false;
        this.formEditSubmitted = false;
        this.toastr.error('Please enter Picking From and Picking To');
        return null;
      }
      payload.cranePickUpLocation = editNdrFormValue.cranePickUpLocation.trim();
      payload.craneDropOffLocation = editNdrFormValue.craneDropOffLocation.trim();
      payload.CraneRequestId = editNdrFormValue.CraneRequestId;
      payload.requestType = 'deliveryRequestWithCrane';
    } else {
      payload.cranePickUpLocation = null;
      payload.craneDropOffLocation = null;
      payload.CraneRequestId = null;
      payload.requestType = 'deliveryRequest';
    }

    return payload;
  }

  public submitEditNdrRequest(editNdrPayload: any, action: string): void {
    if (!editNdrPayload) return;

    if (action === 'submitQueuedNDR') {
      const updatedPayload = { ...editNdrPayload, updateQueuedRequest: 1, ndrStatus: 'submitQueuedNDR' };
      this.submitQueuedNDR(updatedPayload);
    } else {
      this.editNDR(editNdrPayload);
    }
  }

  public throwError(action: string): void {
    this.editSubmitted = false;
    this.formEditSubmitted = false;
    this.saveQueuedNDR = false;
    if (action === 'time error') {
      this.toastr.error('Please Enter Start time Lesser than End time');
    } else {
      this.toastr.error('Please Enter Future Date.');
    }
  }

  public formReset(): void {
    this.formEditSubmitted = false;
    this.editSubmitted = false;
  }

  public checkEditDeliveryStringEmptyValues(formValue: {
    description: string;
    notes: string;
  }): boolean {
    if (formValue.description.trim() === '') {
      this.toastr.error('Please Enter valid description.', 'OOPS!');
      return true;
    }
    if (formValue.notes) {
      if (formValue.notes.trim() === '') {
        this.toastr.error('Please Enter valid notes.', 'OOPS!');
        return true;
      }
    }
    return false;
  }

  public editQueuedNDR(payload: any): void {
    this.deliveryService.editQueuedNDRForm(payload).subscribe({
      next: (editQueuedNdrResponse: any): void => {
        if (editQueuedNdrResponse) {
          this.editNDRSuccess(editQueuedNdrResponse);
        }
      },
      error: (editQueuedNDRError): void => {
        this.formReset();
        this.NDRTimingChanged = false;
        if (editQueuedNDRError.message?.statusCode === 400) {
          this.showError(editQueuedNDRError);
        } else if (!editQueuedNDRError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(editQueuedNDRError.message, 'OOPS!');
        }
      },
    });
  }

  public editNDRSuccess(response: { message: string }): void {
    this.toastr.success(response.message, 'Success');
    this.socket.emit('NDREditHistory', response);
    this.mixpanelService.addMixpanelEvents('Edited New Delivery Booking');
    this.formReset();
    this.deliveryService.updatedHistory({ status: true }, 'NDREditHistory');
    this.deliveryService.updateCraneRequestHistory({ status: true }, 'NDREditHistory');
    this.resetForm('yes');
    this.NDRTimingChanged = false;
  }

  public submitQueuedNDR(payload: any): void {
    if (this.deliverEditForm.get('isAssociatedWithCraneRequest').value) {
      if (
        !this.deliverEditForm.get('cranePickUpLocation').value
        || !this.deliverEditForm.get('craneDropOffLocation').value
      ) {
        this.formSubmitted = false;
        this.formEditSubmitted = false;
        this.toastr.error('Please enter Picking From and Picking To');
        return;
      }
    }
    this.deliveryService.submitQueuedNDR(payload).subscribe({
      next: (submitQueuedNdrResponse: any): void => {
        if (submitQueuedNdrResponse) {
          this.editNDRSuccess(submitQueuedNdrResponse);
        }
      },
      error: (submitQueuedNDRError): void => {
        this.formReset();
        this.NDRTimingChanged = false;
        this.saveQueuedNDR = false;
        if (submitQueuedNDRError.message?.statusCode === 400) {
          this.showError(submitQueuedNDRError);
        } else if (!submitQueuedNDRError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(submitQueuedNDRError.message, 'OOPS!');
        }
      },
    });
  }

  public editNDR(payload: any): void {
    this.deliveryService.editNDR(payload).subscribe({
      next: (editNdrResponse: any): void => {
        if (editNdrResponse) {
          this.editNDRSuccess(editNdrResponse);
        }
      },
      error: (editNDRHistoryError): void => {
        this.formReset();
        this.NDRTimingChanged = false;
        if (editNDRHistoryError.message?.statusCode === 400) {
          this.showError(editNDRHistoryError);
        } else if (!editNDRHistoryError.message) {
          this.toastr.error('Try again later.!', 'Something went wrong.');
        } else {
          this.toastr.error(editNDRHistoryError.message, 'OOPS!');
        }
      },
    });
  }

  public deliveryEndTimeChangeDetection(): void {
    this.NDRTimingChanged = true;
    this.onEditSubmitForm(null);
  }

  public changeDate(event: any): void {
    if (!this.modalLoader) {
      const startTime = new Date(event).getHours();
      const minutes = new Date(event).getMinutes();
      this.deliveryEnd = new Date(event);
      this.deliveryEnd.setHours(startTime + 1);
      this.deliveryEnd.setMinutes(minutes);
      this.deliverEditForm.get('deliveryEnd').setValue(this.deliveryEnd);
      this.NDRTimingChanged = true;
    }
    this.onEditSubmitForm(null);
  }

  public close(template: TemplateRef<any>): void {
    if (
      this.deliverEditForm.touched
      || this.NDRTimingChanged
      || (this.deliverEditForm.get('defineItems').dirty
        && this.deliverEditForm.get('defineItems').value
        && this.deliverEditForm.get('defineItems').value.length > 0
        && this.areDifferentByProperty(
          this.editBeforeDFOW,
          this.deliverEditForm.get('defineItems').value,
          'DFOW',
        ))
      || (this.deliverEditForm.get('companyItems').dirty
        && this.deliverEditForm.get('companyItems').value
        && this.deliverEditForm.get('companyItems').value.length > 0
        && this.areDifferentByProperty(
          this.editBeforeCompany,
          this.deliverEditForm.get('companyItems').value,
          'companyName',
        ))
        || (this.deliverEditForm.get('EquipmentId').dirty
        && this.deliverEditForm.get('EquipmentId').value
        && this.deliverEditForm.get('EquipmentId').value.length > 0
        && this.areDifferentByProperty(
          this.editBeforeEquipment,
          this.deliverEditForm.get('EquipmentId').value,
          'Equipment',
        ))
    ) {
      this.openConfirmationModalPopupForEditNDR(template);
    } else {
      this.resetForm('yes');
    }
  }

  public openConfirmationModalPopupForEditNDR(template: TemplateRef<any>): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef1 = this.modalService.show(template, data);
  }

  public resetForm(action: string): void {
    if (action === 'no') {
      this.modalRef1.hide();
    } else {
      if (this.modalRef1) {
        this.modalRef1.hide();
      }
      this.formEditSubmitted = false;
      this.editSubmitted = false;
      this.saveQueuedNDR = false;
      this.modalRef.hide();
      this.NDRTimingChanged = false;
    }
  }

  public showError(err: {
    message: { details: ({ [s: string]: unknown } | ArrayLike<unknown>)[] };
  }): void {
    let errorMessage: any = '';
    errorMessage = Object.values(err.message.details[0]);
    this.submitted = false;
    this.formSubmitted = false;
    this.toastr.error(errorMessage);
  }

  public checkEditDeliveryFutureDate(
    editDeliveryStart: DateInput,
    editDeliveryEnd: DateInput,
    action: string,
  ): boolean {
    const editStartDate = moment(new Date(editDeliveryStart));
    const editCurrentDate = moment()
      .clone()
      .add(this.deliveryWindowTime, this.deliveryWindowTimeUnit);
    const editEndDate = moment(new Date(editDeliveryEnd));
    if (
      (editStartDate.isAfter(editCurrentDate) && editEndDate.isAfter(editCurrentDate))
      || action === 'save'
    ) {
      return true;
    }
    return false;
  }

  public getProjectSettings(): void {
    if (this.ProjectId) {
      const params = {
        ProjectId: this.ProjectId,
      };
      this.projectSettingsService.getProjectSettings(params).subscribe((res): void => {
        const responseData = res.data;
        this.deliveryWindowTime = responseData.deliveryWindowTime;
        this.deliveryWindowTimeUnit = responseData.deliveryWindowTimeUnit;
      });
    }
  }

  public checkStartEnd(
    deliveryStart: DateInput,
    deliveryEnd: DateInput,
  ): boolean {
    const startDate = new Date(deliveryStart).getTime();
    const endDate = new Date(deliveryEnd).getTime();
    if (startDate < endDate) {
      return true;
    }
    return false;
  }

  public getOverAllEquipmentforEditNdr(): void {
    const editNdrGetEquipmentParams = {
      ProjectId: this.ProjectId,
      pageSize: 0,
      pageNo: 0,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .listEquipment(editNdrGetEquipmentParams, {
        isFilter: true,
        showActivatedAlone: true,
      })
      .subscribe((editNdrEquipmentResponse): void => {
        this.equipmentList = [this.noEquipmentOption, ...editNdrEquipmentResponse.data];
        this.equipmentDropdownSettings = {
          singleSelection: false,
          idField: 'id',
          textField: 'equipmentName',
          selectAllText: 'Select All',
          unSelectAllText: 'UnSelect All',
          itemsShowLimit: 6,
          allowSearchFilter: true,
        };
        this.getCompaniesForEditNdr();
      });
  }

  public getCompaniesForEditNdr(): void {
    const getCompaniesForEditNdrParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getCompanies(getCompaniesForEditNdrParams)
      .subscribe((getCompaniesForEditNdrResponse: any): void => {
        if (getCompaniesForEditNdrResponse) {
          this.companyList = getCompaniesForEditNdrResponse.data;
          this.getDefinableForEditNdr();
          this.editNdrCompanyDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'companyName',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            itemsShowLimit: 6,
            allowSearchFilter: true,
          };
        }
      });
  }

  public getDefinableForEditNdr(): void {
    const getDefinableForEditNdrParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getDefinableWork(getDefinableForEditNdrParams)
      .subscribe((getDefinableForEditNdrResponse: any): void => {
        if (getDefinableForEditNdrResponse) {
          const { data } = getDefinableForEditNdrResponse;
          this.defineList = data;
          this.editNdrDefinableDropdownSettings = {
            singleSelection: false,
            idField: 'id',
            textField: 'DFOW',
            selectAllText: 'Select All',
            unSelectAllText: 'UnSelect All',
            allowSearchFilter: true,
          };
          this.getLocationForEditNdr();
        }
      });
  }

  public getLocationForEditNdr(): void {
    const getLocationForEditNdrParams = {
      ProjectId: this.ProjectId,
      ParentCompanyId: this.ParentCompanyId,
    };
    this.projectService
      .getLocations(getLocationForEditNdrParams)
      .subscribe((getLocationForEditNdrResponse: any): void => {
        if (getLocationForEditNdrResponse) {
          const { data } = getLocationForEditNdrResponse;
          this.locationList = data;
          this.editNdrLocationDropdownSettings = {
            singleSelection: true,
            idField: 'id',
            textField: 'locationPath',
            allowSearchFilter: true,
            closeDropDownOnSelection: true,
          };
          this.getNDR();
        }
      });
  }

  public openContentModal(): void {
    this.modalLoader = false;
  }

  public selectAM() {
    this.isAM = true;
  }

  public selectPM() {
    this.isAM = false;
  }


  public editDetailsForm(): void {
    this.deliverEditForm = this.formBuilder.group({
      id: [''],
      defineItems: [this.formBuilder.array([])],
      EquipmentId: [this.formBuilder.array([])],
      GateId: ['', Validators.compose([Validators.required])],
      notes: [''],
      CraneRequestId: [''],
      person: [''],
      description: ['', Validators.compose([Validators.required])],
      DeliveryId: [''],
      vehicleDetails: [''],
      deliveryDate: ['', Validators.compose([Validators.required])],
      deliveryEnd: ['', Validators.compose([Validators.required])],
      escort: [false],
      deliveryStart: ['', Validators.compose([Validators.required])],
      companyItems: [this.formBuilder.array([])],
      cranePickUpLocation: [''],
      craneDropOffLocation: [''],
      isAssociatedWithCraneRequest: [false, Validators.compose([Validators.required])],
      recurrenceId: [''],
      LocationId: ['', Validators.compose([Validators.required])],
      recurrence: [''],
      recurrenceEndDate: [''],
      originationAddress: [''],
      vehicleType: [''],
      repeatEveryCount: [''],
      repeatEveryType: [''],
      days: new UntypedFormArray([]),
      chosenDateOfMonth: [false, ''],
      dateOfMonth: [''],
      monthlyRepeatType: [''],
    });
    this.deliverEditForm.get('escort').setValue(false);
    const newDate = moment().format('MM/DD/YYYY');
    this.deliverEditForm.get('deliveryDate').setValue(newDate);
    this.deliverEditForm.get('deliveryStart').setValue(this.deliveryStart);
    this.deliverEditForm.get('deliveryEnd').setValue(this.deliveryEnd);
    this.formControlValueChanged();
  }

  public setEquipment(): void {
    const { equipmentDetails } = this.currentEditItem;
    if(equipmentDetails.length && !equipmentDetails[0].Equipment){
        equipmentDetails[0].Equipment = { id: 0, equipmentName: 'No Equipment Needed' }
    }
    const newEquipmentList = [];
    if (equipmentDetails !== undefined) {
      equipmentDetails.forEach((element: { Equipment: { id: any; equipmentName: any } }): void => {
        const data = {
          id: element.Equipment.id,
          equipmentName: element.Equipment.equipmentName,
        };
        newEquipmentList.push(data);
      });
    }
    this.editBeforeEquipment = newEquipmentList;
    this.deliverEditForm.get('EquipmentId').patchValue(newEquipmentList);
  }

  public getRepeatEveryType(value) {
    if (value === 'Does Not Repeat') {
      this.deliverEditForm.get('repeatEveryType').setValue('');
    } else {
      this.deliverEditForm.get('repeatEveryCount').setValue(1);
    }
    if (value === 'Daily') {
      this.deliverEditForm.get('repeatEveryType').setValue('Day');
    }
    if (value === 'Weekly') {
      this.deliverEditForm.get('repeatEveryType').setValue('Week');
    }
    if (value === 'Monthly') {
      this.deliverEditForm.get('repeatEveryType').setValue('Month');
    }
    if (value === 'Yearly') {
      this.deliverEditForm.get('repeatEveryType').setValue('Year');
    }
  }

  public onRecurrenceSelect(value: string): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    this.selectedRecurrence = value;
    this.getRepeatEveryType(value);

    if (this.deliverEditForm.get('repeatEveryCount').value > 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.checkform = this.deliverEditForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day11: any): void => {
        const dayObj11 = day11;
        dayObj11.checked = true;
        dayObj11.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj11.value));
        return dayObj11;
      });
    }
    if (this.deliverEditForm.get('repeatEveryCount').value > 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.weekDays = this.weekDays.map((day12: any): void => {
        const dayObj12 = day12;
        if (dayObj12.value === 'Monday') {
          dayObj12.checked = true;
        } else {
          dayObj12.checked = false;
        }
        dayObj12.isDisabled = false;
        return dayObj12;
      });
      this.checkform = this.deliverEditForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.deliverEditForm.get('repeatEveryCount').value === 1 && value === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.weekDays = this.weekDays.map((day13: any): void => {
        const dayObj13 = day13;
        if (dayObj13.value === 'Monday') {
          dayObj13.checked = true;
        } else {
          dayObj13.checked = false;
        }
        dayObj13.isDisabled = false;
        return dayObj13;
      });
      this.checkform = this.deliverEditForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.checkform.push(new UntypedFormControl('Monday'));
    }
    if (this.deliverEditForm.get('repeatEveryCount').value === 1 && value === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.checkform = this.deliverEditForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day14: any): void => {
        const dayObj14 = day14;
        dayObj14.checked = true;
        dayObj14.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj14.value));
        return dayObj14;
      });
    }
    if (
      this.deliverEditForm.get('repeatEveryCount').value === 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.deliverEditForm.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showMonthlyRecurrence();
    }
    if (
      this.deliverEditForm.get('repeatEveryCount').value > 1
      && (value === 'Monthly' || value === 'Yearly')
    ) {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.deliverEditForm.get('chosenDateOfMonth').setValue(1);
      this.changeMonthlyRecurrence();
      this.showMonthlyRecurrence();
    }
    this.occurMessage();
    this.onEditSubmitForm(null);
  }

  public changeRecurrenceCount(value: number): void {
    if (value > 0) {
      const recurrencedata = this.deliverEditForm.get('recurrence').value;

      this.updateRecurrenceFlags(recurrencedata, value);
      this.updateRepeatEveryType(recurrencedata, value);

      this.selectedRecurrence = recurrencedata;
      this.occurMessage();
    } else if (value < 0) {
      this.deliverEditForm.get('repeatEveryCount').setValue(1);
    }
  }

  public updateRecurrenceFlags(recurrencedata: string, value: number): void {
    const isSingle = +value === 1;

    if (recurrencedata === 'Daily') {
      this.isRepeatWithSingleRecurrence = isSingle;
      this.isRepeatWithMultipleRecurrence = false;
      this.showRecurrenceTypeDropdown = !isSingle;
    }

    if (recurrencedata === 'Weekly') {
      this.isRepeatWithSingleRecurrence = isSingle;
      this.isRepeatWithMultipleRecurrence = !isSingle;
      this.showRecurrenceTypeDropdown = false;
    }

    if (recurrencedata === 'Monthly' || recurrencedata === 'Yearly') {
      this.isRepeatWithSingleRecurrence = isSingle;
      this.isRepeatWithMultipleRecurrence = !isSingle;
      this.showRecurrenceTypeDropdown = false;
    }
  }

  public updateRepeatEveryType(recurrencedata: string, value: number): void {
    const repeatEveryTypeControl = this.deliverEditForm.get('repeatEveryType');

    switch (recurrencedata) {
      case 'Daily':
        repeatEveryTypeControl.setValue(value > 1 ? 'Days' : 'Day');
        break;

      case 'Weekly':
        repeatEveryTypeControl.setValue(value > 1 ? 'Weeks' : 'Week');
        break;

      case 'Monthly':
        repeatEveryTypeControl.setValue(value > 1 ? 'Months' : 'Month');
        this.changeMonthlyRecurrence();
        this.showMonthlyRecurrence();
        break;

      case 'Yearly':
        repeatEveryTypeControl.setValue(value > 1 ? 'Years' : 'Year');
        break;

      default:
        break;
    }
  }

  public chooseRepeatEveryType(value: string, eventDetail: any): void {
    this.isRepeatWithMultipleRecurrence = false;
    this.isRepeatWithSingleRecurrence = false;
    this.showRecurrenceTypeDropdown = false;
    if (value === 'Day' || value === 'Days') {
      this.deliverEditForm.get('recurrence').setValue('Daily');
    }
    if (value === 'Week' || value === 'Weeks') {
      this.deliverEditForm.get('recurrence').setValue('Weekly');
    }
    if (value === 'Month' || value === 'Months') {
      this.deliverEditForm.get('recurrence').setValue('Monthly');
    }
    if (value === 'Year' || value === 'Years') {
      this.deliverEditForm.get('recurrence').setValue('Yearly');
    }
    if (value === 'Day' || value === 'Days') {
      this.checkform = this.deliverEditForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day: any): any => {
        const dayObj = day;
        dayObj.checked = true;
        dayObj.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj.value));
        return dayObj;
      });
    }
    if (value === 'Week' || value === 'Weeks') {
      if (eventDetail?.days?.length > 0) {
        this.checkform = this.deliverEditForm.get('days') as UntypedFormArray;
        this.checkform.controls = [];
        this.weekDays = this.weekDays.map((day1: any): void => {
          const dayObj1 = day1;
          if (eventDetail.days.includes(dayObj1.value)) {
            dayObj1.checked = true;
            this.checkform.push(new UntypedFormControl(dayObj1.value));
          } else {
            dayObj1.checked = false;
          }
          dayObj1.isDisabled = false;
          return dayObj1;
        });
      } else {
        this.weekDays = this.weekDays.map((day2: any): void => {
          const dayObj2 = day2;
          if (dayObj2.value === 'Monday') {
            dayObj2.checked = true;
          } else {
            dayObj2.checked = false;
          }
          dayObj2.isDisabled = false;
          return dayObj2;
        });
        this.checkform = this.deliverEditForm.get('days') as UntypedFormArray;
        this.checkform.controls = [];
        this.checkform.push(new UntypedFormControl('Monday'));
      }
    }
    if (value === 'Day' || value === 'Week' || value === 'Month' || value === 'Year') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showRecurrenceTypeDropdown = false;
    }
    if (value === 'Days' || value === 'Weeks' || value === 'Months' || value === 'Years') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.showRecurrenceTypeDropdown = false;
    }
    if (this.deliverEditForm.get('repeatEveryCount').value > 1) {
      this.showRecurrenceTypeDropdown = true;
      this.isRepeatWithMultipleRecurrence = false;
    }
    this.selectedRecurrence = this.deliverEditForm.get('recurrence').value;
    this.occurMessage();
    this.onEditSubmitForm(null);
  }

  public changeMonthlyRecurrence(): void {
    this.setMonthlyOrYearlyRecurrenceOption();
    this.updateFormValidation();
    this.showMonthlyRecurrence();
    this.occurMessage();
    this.onEditSubmitForm(null);
  }

  public setMonthlyOrYearlyRecurrenceOption(): void {
    if (this.deliverEditForm.get('chosenDateOfMonth').value === 1) {
      this.deliverEditForm
        .get('dateOfMonth')
        .setValue(moment(this.deliverEditForm.get('deliveryDate').value).format('DD'));
      this.deliverEditForm.get('monthlyRepeatType').setValue(null);
    } else if (this.deliverEditForm.get('chosenDateOfMonth').value === 2) {
      this.deliverEditForm.get('dateOfMonth').setValue(null);
      this.deliverEditForm.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
    } else if (this.deliverEditForm.get('chosenDateOfMonth').value === 3) {
      this.deliverEditForm.get('dateOfMonth').setValue(null);
      this.deliverEditForm.get('monthlyRepeatType').setValue(this.monthlyLastDayOfWeek);
    }
  }

  public updateFormValidation(): void {
    const chosenDateOfMonth = this.deliverEditForm.get('chosenDateOfMonth');
    const dateOfMonth = this.deliverEditForm.get('dateOfMonth');
    const monthlyRepeatType = this.deliverEditForm.get('monthlyRepeatType');
    if (this.deliverEditForm.get('chosenDateOfMonth').value === 1) {
      dateOfMonth.setValidators([Validators.required]);
      monthlyRepeatType.clearValidators();
    } else {
      monthlyRepeatType.setValidators([Validators.required]);
      dateOfMonth.clearValidators();
    }
    chosenDateOfMonth.updateValueAndValidity();
    dateOfMonth.updateValueAndValidity();
    monthlyRepeatType.updateValueAndValidity();
  }

  public showMonthlyRecurrence(): void {
    if (this.deliverEditForm.get('deliveryDate').value) {
      const startDate = moment(this.deliverEditForm.get('deliveryDate').value).format('YYYY-MM');
      const chosenDay = moment(this.deliverEditForm.get('deliveryDate').value).format('dddd');
      this.monthlyDate = moment(this.deliverEditForm.get('deliveryDate').value).format('DD');
      const day = moment(startDate, 'YYYY-MM').startOf('month').day(chosenDay);
      const getAllDays = [];
      if (day.date() > 7) day.add(7, 'd');
      const month = day.month();
      while (month === day.month()) {
        getAllDays.push(day.format('YYYY-MM-DD'));
        day.add(7, 'd');
      }
      let week: string;
      let extraOption: string;
      this.enableOption = false;
      getAllDays.forEach((element, i): void => {
        if (
          moment(this.deliverEditForm.get('deliveryDate').value).format('YYYY-MM-DD')
          === moment(element).format('YYYY-MM-DD')
        ) {
          const number = i + 1;
          if (number === 1) {
            week = 'First';
          }
          if (number === 2) {
            week = 'Second';
          }
          if (number === 3) {
            week = 'Third';
          }
          if (number === 4) {
            this.enableOption = true;
            extraOption = 'Last';
            week = 'Fourth';
          }
          if (number === 5) {
            week = 'Last';
          }
          if (number === 6) {
            week = 'Last';
          }
        }
      });
      this.monthlyDayOfWeek = `${week} ${chosenDay}`;
      this.monthlyLastDayOfWeek = `${extraOption} ${chosenDay}`;
      if (!this.enableOption && this.deliverEditForm.get('chosenDateOfMonth').value === 3) {
        this.deliverEditForm.get('chosenDateOfMonth').setValue(2);
        this.deliverEditForm.get('dateOfMonth').setValue(null);
        this.deliverEditForm.get('monthlyRepeatType').setValue(this.monthlyDayOfWeek);
      }
      this.setMonthlyOrYearlyRecurrenceOption();
      this.occurMessage();
      this.onEditSubmitForm(null);
    }
  }

  public updateWeeklyDates() {
    const deliveryEnd = this.deliverEditForm.get('endDate').value;

    if (deliveryEnd) {
      let endDate = moment(deliveryEnd, 'YYYY-MM-DD');

      let currentDate;
      if (this.weekDates.length > 0) {
        currentDate = moment(this.weekDates[0].fullDate, 'YYYY-MM-DD');
      } else {
        currentDate = moment();
      }

      while (currentDate.isSameOrBefore(endDate)) {
        const exists = this.weekDates.some(weekDate => weekDate.fullDate === currentDate.format('YYYY-MM-DD'));

        if (!exists) {
          this.weekDates.push({
            name: currentDate.format('ddd'),
            date: currentDate.format('DD'),
            fullDate: currentDate.format('YYYY-MM-DD'),
          });
        }

        currentDate.add(1, 'day');
      }
    }
  }

  public repeatEveryTypeMessage() {
    if (this.deliverEditForm.get('repeatEveryType').value === 'Day') {
      this.message = 'Occurs every day';
    }
    if (this.deliverEditForm.get('repeatEveryType').value === 'Days') {
      if (+this.deliverEditForm.get('repeatEveryCount').value === 2) {
        this.message = 'Occurs every other day';
      } else {
        this.message = `Occurs every ${this.deliverEditForm.get('repeatEveryCount').value} days`;
      }
    }
  }

  public occurMessage(): void {
    this.message = '';
    this.repeatEveryTypeMessage();
    if (this.deliverEditForm.get('repeatEveryType').value === 'Week') {
      let weekDays = '';
      this.weekDays.map((dayObj1: any): any => {
        if (dayObj1.checked) {
          weekDays = `${weekDays + dayObj1.value},`;
        }
        return false;
      });
      weekDays = weekDays.replace(/,\s*$/, '');
      this.message += `Occurs every ${weekDays}`;
    }
    if (this.deliverEditForm.get('repeatEveryType').value === 'Weeks') {
      let weekDays = '';
      this.weekDays.map((dayObj2: any): any => {
        if (dayObj2.checked) {
          weekDays = `${weekDays + dayObj2.value},`;
        }
        return false;
      });
      if (+this.deliverEditForm.get('repeatEveryCount').value === 2) {
        this.message = `Occurs every other  ${weekDays}`;
      } else {
        this.message = `Occurs every ${
          this.deliverEditForm.get('repeatEveryCount').value
        } weeks on ${weekDays}`;
      }
      weekDays = weekDays.replace(/,\s*$/, '');
    }
    if (
      this.deliverEditForm.get('repeatEveryType').value === 'Month'
      || this.deliverEditForm.get('repeatEveryType').value === 'Months'
      || this.deliverEditForm.get('repeatEveryType').value === 'Year'
      || this.deliverEditForm.get('repeatEveryType').value === 'Years'
    ) {
      if (this.deliverEditForm.get('chosenDateOfMonth').value === 1) {
        this.message = `Occurs on day ${this.monthlyDate}`;
      } else if (this.deliverEditForm.get('chosenDateOfMonth').value === 2) {
        this.message = `Occurs on the ${this.monthlyDayOfWeek}`;
      } else {
        this.message = `Occurs on the ${this.monthlyLastDayOfWeek}`;
      }
    }
    if (this.message) {
      this.message += ` until ${moment(this.deliverEditForm.get('recurrenceEndDate').value).format(
        'MMMM DD, YYYY',
      )}`;
    }
  }

  public onChange(event: { target: { value: any; checked: any } }): void {
    this.checkform = this.deliverEditForm.get('days') as UntypedFormArray;
    this.daysEdited = true;
    this.valueExists = this.checkform.controls.filter(
      (object: { value: any }): any => object.value === event.target.value,
    );
    if (event.target.checked) {
      this.checkform.push(new UntypedFormControl(event.target.value));
      this.weekDays = this.weekDays.map((day16: any): void => {
        const dayObj16 = day16;
        if (day16.value === event.target.value) {
          dayObj16.checked = true;
        }
        return dayObj16;
      });
      if (this.checkform.controls.length === 2) {
        this.weekDays = this.weekDays.map((day17: any): void => {
          const dayObj17 = day17;
          dayObj17.isDisabled = false;
          return dayObj17;
        });
      }
    } else if (this.selectedRecurrence === 'Weekly') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day18: any): void => {
            const dayObj18 = day18;
            if (dayObj18.value === event.target.value) {
              dayObj18.checked = false;
            }
            return dayObj18;
          });
        }
        if (this.checkform.controls.length === 1) {
          this.weekDays = this.weekDays.map((day19: any): void => {
            const dayObj19 = day19;
            if (dayObj19.value === this.checkform.controls[0].value) {
              dayObj19.isDisabled = true;
              dayObj19.checked = true;
            }
            return dayObj19;
          });
          return;
        }
        i += 1;
      });
    } else if (this.selectedRecurrence === 'Daily') {
      let i = 0;
      this.checkform.controls.forEach((ctrl: UntypedFormControl): void => {
        if (ctrl.value === event.target.value) {
          this.checkform.removeAt(i);
          this.weekDays = this.weekDays.map((day: any): void => {
            const dayObj = day;
            if (dayObj.value === event.target.value) {
              dayObj.checked = false;
              dayObj.isDisabled = false;
            }
            return dayObj;
          });
          return;
        }
        i += 1;
      });
    }
    if (this.checkform.controls.length !== 7) {
      this.deliverEditForm.get('recurrence').setValue('Weekly');
      if (+this.deliverEditForm.get('repeatEveryCount').value === 1) {
        this.deliverEditForm.get('repeatEveryType').setValue('Week');
      } else {
        this.deliverEditForm.get('repeatEveryType').setValue('Weeks');
      }
      this.selectedRecurrence = this.deliverEditForm.get('recurrence').value;
    }
    if (this.checkform.controls.length === 7) {
      this.deliverEditForm.get('recurrence').setValue('Daily');
      if (+this.deliverEditForm.get('repeatEveryCount').value === 1) {
        this.deliverEditForm.get('repeatEveryType').setValue('Day');
      } else {
        this.deliverEditForm.get('repeatEveryType').setValue('Days');
      }
      this.selectedRecurrence = this.deliverEditForm.get('recurrence').value;
    }
    this.occurMessage();
    this.onEditSubmitForm(null);
  }

  public formControlValueChanged(): void {
    this.deliverEditForm.get('repeatEveryType').valueChanges.subscribe((value: string): void => {
      const days = this.deliverEditForm.get('days');
      const chosenDateOfMonth = this.deliverEditForm.get('chosenDateOfMonth');
      const dateOfMonth = this.deliverEditForm.get('dateOfMonth');
      const monthlyRepeatType = this.deliverEditForm.get('monthlyRepeatType');
      if (value === 'Week' || value === 'Day' || value === 'Weeks') {
        days.setValidators([Validators.required]);
      } else {
        days.clearValidators();
      }
      if (value === 'Month' || value === 'Months' || value === 'Year' || value === 'Years') {
        if (this.deliverEditForm.get('chosenDateOfMonth').value === 1) {
          dateOfMonth.setValidators([Validators.required]);
          monthlyRepeatType.clearValidators();
        } else {
          monthlyRepeatType.setValidators([Validators.required]);
          dateOfMonth.clearValidators();
        }
      } else {
        chosenDateOfMonth.clearValidators();
        dateOfMonth.clearValidators();
        monthlyRepeatType.clearValidators();
      }
      chosenDateOfMonth.updateValueAndValidity();
      dateOfMonth.updateValueAndValidity();
      monthlyRepeatType.updateValueAndValidity();
      days.updateValueAndValidity();
    });
  }

  public setRepeat(data: any): void {
    const setValue = this.deliverEditForm.get('recurrence').value;
    const selectedDays = data.days;
    if (this.deliverEditForm.get('repeatEveryCount').value > 1 && setValue === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = false;
      this.checkform = this.deliverEditForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day11: any): void => {
        const dayObj11 = day11;
        dayObj11.checked = true;
        dayObj11.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj11.value));
        return dayObj11;
      });
    }
    if (this.deliverEditForm.get('repeatEveryCount').value > 1 && setValue === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.checkform = this.deliverEditForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day13: any): any => {
        if (selectedDays.includes(day13.value)) {
          day13.checked = true;
        } else {
          day13.checked = false;
        }
        day13.isDisabled = false;
        this.checkform.push(new UntypedFormControl(day13.value));
        return day13;
      });
    }
    if (this.deliverEditForm.get('repeatEveryCount').value === 1 && setValue === 'Weekly') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.checkform = this.deliverEditForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day13: any): any => {
        // Check if the day13.value is in the days array
        if (selectedDays.includes(day13.value)) {
          day13.checked = true;
        } else {
          day13.checked = false;
        }
        day13.isDisabled = false;
        this.checkform.push(new UntypedFormControl(day13.value));
        return day13;
      });
    }
    if (this.deliverEditForm.get('repeatEveryCount').value === 1 && setValue === 'Daily') {
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.checkform = this.deliverEditForm.get('days') as UntypedFormArray;
      this.checkform.controls = [];
      this.weekDays = this.weekDays.map((day14: any): void => {
        const dayObj14 = day14;
        dayObj14.checked = true;
        dayObj14.isDisabled = false;
        this.checkform.push(new UntypedFormControl(dayObj14.value));
        return dayObj14;
      });
    }
    if (
      this.deliverEditForm.get('repeatEveryCount').value === 1
      && (setValue === 'Monthly' || setValue === 'Yearly')
    ) {
      this.changeMonthlyRecurrence();
      this.isRepeatWithMultipleRecurrence = false;
      this.isRepeatWithSingleRecurrence = true;
      this.showMonthlyRecurrence();
    }
    if (
      this.deliverEditForm.get('repeatEveryCount').value > 1
      && (setValue === 'Monthly' || setValue === 'Yearly')
    ) {
      this.isRepeatWithMultipleRecurrence = true;
      this.isRepeatWithSingleRecurrence = false;
      this.changeMonthlyRecurrence();
      this.showMonthlyRecurrence();
    }
    this.occurMessage();
  }

  public sortWeekDays(data: any[]): any {
    const order = {
      Sunday: 1,
      Monday: 2,
      Tuesday: 3,
      Wednesday: 4,
      Thursday: 5,
      Friday: 6,
      Saturday: 7,
    };

    if (data.length > 0) {
      return data.sort((a, b): any => order[a] - order[b]);
    }
  }

  public onSubmit(action: any): void {
    this.action2 = action;
    const formValue = this.deliverEditForm.value;
    if (this.deliverEditForm.get('recurrence').value === 'Weekly' || this.deliverEditForm.get('recurrence').value === 'Daily') {
      this.weekDays.forEach((day11) => {
        if (day11.checked === true) {
          this.payloadDays.push(day11.value);
        }
      });
    } else {
      this.payloadDays = [];
    }
    if (
      formValue.recurrence !== this.previousRecurrence
      || this.previousRepeatEveryCount !== formValue.repeatEveryCount
      || this.previousRepeatEveryType !== formValue.repeatEveryType
      || JSON.stringify(this.previousDays) !== JSON.stringify(this.payloadDays)
      || this.previousDateOfMonth !== formValue.dateOfMonth
      || this.previousMonthlyRepeatType !== formValue.monthlyRepeatType
      || this.daysEdited
    ) {
      this.recurrenceEdited = true;
    } else {
      this.recurrenceEdited = false;
    }
      this.openRecurrencePopup();
  }

  public openRecurrencePopup(): void {
    let data = {};
    data = {
      keyboard: false,
      class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal',
    };
    this.modalRef2 = this.modalService.show(this.cancelRecurrenceTemplate, data);
  }

  public recurrenceSubmit(action: string): any {
    if (action === 'no') {
      this.modalRef2.hide();
    } else {
      if (this.modalRef2) {
        this.modalRef2.hide();
      }
      this.onEditSubmit(this.action2);
    }
  }
}
