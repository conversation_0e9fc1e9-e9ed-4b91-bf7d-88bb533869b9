<nav class="navbar navbar-light navbar-expand-md">
  <div class="container-fluid">
    <a
      href="https://www.folloit.com/"
      target="_blank"
      rel="noopener"
      class="navbar-brand text-left text-md-center text-lg-center"
      [ngClass]="{ 'sidmenu-open': !shouldShow }"
    >
      <img
        src="/assets/images/logo.svg"
        class="full-logo"
        width="100%"
        height="100%"
        alt="Follo Logo"
      />
    </a>
    <button class="navbar-toggler outline-0" type="button" (click)="mobilenNavOpen()">
      <span class="mx-4 text-black fs22 menu-button-bars">
        <span class="bar-line"></span>
        <span class="bar-line"></span>
        <span class="bar-line"></span>
      </span>
    </button>
    <div [ngClass]="{ 'mobile-view-nav': mobilenavshow }">
      <ul class="list-inline mb-0 navbar-nav main-menu text-center">
        <li class="nav-item p10">
          <button
            class="btn btn-orange-outline fs14 color-orange px-5 radius20 cairo-regular"
            (click)="redirect('login')"
            *ngIf="!loginToken"
          >
            Login
          </button>
          <button
            class="btn btn-orange-outline fs14 color-orange px-5 radius20 cairo-regular"
            (click)="redirect('dashboard')"
            *ngIf="loginToken"
          >
            Dashboard
          </button>
        </li>
      </ul>
    </div>
  </div>
</nav>
