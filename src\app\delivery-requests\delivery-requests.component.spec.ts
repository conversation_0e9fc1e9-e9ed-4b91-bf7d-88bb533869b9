import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule, FormBuilder } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { ModalModule, BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { SocketIoModule, SocketIoConfig } from 'ngx-socket-io';
import { ToastrModule, ToastrService } from 'ngx-toastr';
import { NgxPaginationModule } from 'ngx-pagination';
import { of, Subject, throwError } from 'rxjs';
import { NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

import { DeliveryRequestsComponent } from './delivery-requests.component';
import { environment } from '../../environments/environment';
import { ProjectService } from '../services/profile/project.service';
import { DeliveryService } from '../services/profile/delivery.service';

const config: SocketIoConfig = { url: environment.apiSocketUrl, options: {} };

describe('DeliveryRequestsComponent', () => {
  let component: DeliveryRequestsComponent;
  let fixture: ComponentFixture<DeliveryRequestsComponent>;
  let projectService: jest.Mocked<ProjectService>;
  let deliveryService: jest.Mocked<DeliveryService>;
  let modalService: jest.Mocked<BsModalService>;
  let toastrService: jest.Mocked<ToastrService>;
  let router: jest.Mocked<Router>;
  let routerEventsSubject: Subject<any>;

  const mockProjectService = {
    projectParent: of({ ProjectId: 1, ParentCompanyId: 1 }),
    ParentCompanyId: of(1),
    fileUpload: of({ status: 'uploadDone' }),
    getDefinableWork: jest.fn(),
    getCompanies: jest.fn(),
    getOverAllGateInNewDelivery: jest.fn(),
    listAllMember: jest.fn(),
    gateList: jest.fn(),
    listEquipment: jest.fn(),
    getLocations: jest.fn(),
    getProject: jest.fn(),
    uploadBulkNdrFile: jest.fn(),
  };

  const mockDeliveryService = {
    loginUser: of({ RoleId: 2 }),
    getCurrentStatus: of(1),
    refresh: of(true),
    listNDR: jest.fn(),
    updateStateOfNDR: jest.fn(),
    updatedDeliveryId: jest.fn(),
    updatedEditCraneRequestId: jest.fn(),
    decryption: jest.fn(),
    updateRequest: jest.fn(),
    importBulkNDR: jest.fn(),
    importBulkNDRTemplate: jest.fn(),
    getMemberRole: jest.fn(),
    searchNewMember: jest.fn(),
    updateLoginUser: jest.fn(),
    updatedHistory: jest.fn(),
  };

  beforeEach(async () => {
    routerEventsSubject = new Subject();

    await TestBed.configureTestingModule({
      declarations: [DeliveryRequestsComponent],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        RouterTestingModule,
        HttpClientTestingModule,
        ModalModule.forRoot(),
        SocketIoModule.forRoot(config),
        ToastrModule.forRoot(),
        NgxPaginationModule,
      ],
      providers: [
        { provide: ProjectService, useValue: mockProjectService },
        { provide: DeliveryService, useValue: mockDeliveryService },
        { provide: BsModalService, useValue: { show: jest.fn(), hide: jest.fn() } },
        { provide: ToastrService, useValue: { success: jest.fn(), error: jest.fn() } },
        { provide: Title, useValue: { setTitle: jest.fn() } },
        {
          provide: Router,
          useValue: {
            navigateByUrl: jest.fn(),
            navigate: jest.fn(),
            url: '/test',
            events: routerEventsSubject.asObservable()
          }
        },
        { provide: ActivatedRoute, useValue: { queryParams: of({}) } },
        BsModalRef,
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();

    projectService = TestBed.inject(ProjectService) as jest.Mocked<ProjectService>;
    deliveryService = TestBed.inject(DeliveryService) as jest.Mocked<DeliveryService>;
    modalService = TestBed.inject(BsModalService) as jest.Mocked<BsModalService>;
    toastrService = TestBed.inject(ToastrService) as jest.Mocked<ToastrService>;
    router = TestBed.inject(Router) as jest.Mocked<Router>;
  });

  beforeEach(() => {
    // Create component without DOM rendering
    component = new DeliveryRequestsComponent(
      modalService,
      projectService,
      new FormBuilder(),
      router,
      {} as any, // socket
      toastrService,
      deliveryService,
      { setTitle: jest.fn() } as any, // titleService
      {} as ActivatedRoute
    );

    // Initialize component properties
    component.ProjectId = 1;
    component.ParentCompanyId = 2;
    component.authUser = { RoleId: 1, id: 1 };
    component.deliveryList = [];
    component.memberList = [];
    component.companyList = [];
    component.gateList = [];
    component.equipmentList = [];
    component.defineList = [];
    component.locationList = [];
    component.statusValue = [];
    component.files = [];
    component.selectedDeliveryRequestIdForMultipleEdit = [];

    // Initialize forms
    component.filterDetailsForm();
    component.deliverForm();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // Test methods directly without DOM rendering
  describe('Method Testing Without DOM', () => {
    beforeEach(() => {
      // Initialize required properties
      component.ProjectId = 1;
      component.ParentCompanyId = 2;
      component.authUser = { RoleId: 1, id: 1 };
      component.deliveryList = [];
      component.filterForm = new FormBuilder().group({
        companyFilter: [''],
        descriptionFilter: [''],
        statusFilter: [''],
        dateFilter: [''],
        memberFilter: [''],
        gateFilter: [''],
        equipmentFilter: [''],
        locationFilter: [''],
        pickFrom: [''],
        pickTo: ['']
      });
      component.deliverEditMultipleForm = new FormBuilder().group({
        companyItems: [[]],
        defineItems: [[]],
        person: [[]],
        EquipmentId: [[]],
        escort: [false],
        GateId: [''],
        status: [''],
        deliveryDate: [''],
        deliveryStart: [''],
        deliveryEnd: [''],
        cranePickUpLocation: [''],
        craneDropOffLocation: ['']
      });
    });

    it('should call getDeliveryRequest method', () => {
      const mockResponse = {
        data: { rows: [], count: 0 },
        lastId: 0,
        statusData: { statusColorCode: JSON.stringify([]) }
      };
      deliveryService.listNDR.mockReturnValue(of(mockResponse));

      component.getDeliveryRequest();

      expect(deliveryService.listNDR).toHaveBeenCalled();
      expect(component.loader).toBe(false);
    });

    it('should call getMembers method', () => {
      const mockResponse = { data: [{ id: 1, name: 'Test Member' }] };
      projectService.listAllMember.mockReturnValue(of(mockResponse));

      component.getMembers();

      expect(projectService.listAllMember).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 2
      });
      expect(component.memberList).toEqual(mockResponse.data);
    });

    it('should call getDefinable method', () => {
      const mockResponse = { data: [{ id: 1, name: 'Test Definable' }] };
      projectService.getDefinableWork.mockReturnValue(of(mockResponse));

      component.getDefinable();

      expect(projectService.getDefinableWork).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 2
      });
      expect(component.defineList).toEqual(mockResponse.data);
      expect(component.modalLoader).toBe(false);
    });

    it('should handle setDefaultPerson method', () => {
      const mockResponse = {
        data: {
          id: 1,
          User: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>'
          }
        }
      };
      deliveryService.getMemberRole.mockReturnValue(of(mockResponse));

      component.setDefaultPerson();

      expect(deliveryService.getMemberRole).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 2
      });
    });

    it('should handle requestAutocompleteItems method', () => {
      const mockResponse = { data: [{ id: 1, name: 'Test User' }] };
      deliveryService.searchNewMember.mockReturnValue(of(mockResponse));

      const result = component.requestAutocompleteItems('test');

      expect(deliveryService.searchNewMember).toHaveBeenCalledWith({
        ProjectId: 1,
        search: 'test',
        ParentCompanyId: 2
      });
      expect(result).toBeDefined();
    });

    it('should handle onEditSubmitForm for companies', () => {
      component.onEditSubmitForm('companies');
      expect(component.companyEdited).toBe(true);
    });

    it('should handle onEditSubmitForm for dfow', () => {
      component.onEditSubmitForm('dfow');
      expect(component.dfowEdited).toBe(true);
    });

    it('should handle onEditSubmitForm for escort', () => {
      component.onEditSubmitForm('escort');
      expect(component.escortEdited).toBe(true);
    });

    it('should handle onEditSubmitForm for persons', () => {
      component.onEditSubmitForm('persons');
      expect(component.responsiblePersonEdited).toBe(true);
    });

    it('should handle onEditSubmitForm for gate', () => {
      component.onEditSubmitForm('gate');
      expect(component.gateEdited).toBe(true);
    });

    it('should handle onEditSubmitForm for void', () => {
      component.onEditSubmitForm('void');
      expect(component.voidEdited).toBe(true);
    });

    it('should handle onEditSubmitForm for status', () => {
      component.onEditSubmitForm('status');
      expect(component.statusEdited).toBe(true);
    });

    it('should handle onEditSubmitForm for deliveryDate with value', () => {
      component.deliverEditMultipleForm.get('deliveryDate').setValue('2024-12-31');
      jest.spyOn(component, 'setDefaultDeliveryTime');

      component.onEditSubmitForm('deliveryDate');

      expect(component.setDefaultDeliveryTime).toHaveBeenCalled();
      expect(component.deliveryDateEdited).toBe(true);
    });

    it('should handle onEditSubmitForm for deliveryDate without value', () => {
      component.deliverEditMultipleForm.get('deliveryDate').setValue('');

      component.onEditSubmitForm('deliveryDate');

      expect(component.deliveryDateEdited).toBe(false);
      expect(component.deliverEditMultipleForm.get('deliveryStart').value).toBe('');
      expect(component.deliverEditMultipleForm.get('deliveryEnd').value).toBe('');
    });

    it('should handle checkEquipmentType with crane equipment', () => {
      component.equipmentList = [
        { id: 1, PresetEquipmentType: { isCraneType: true } },
        { id: 2, PresetEquipmentType: { isCraneType: false } }
      ];
      const event = [{ id: 1 }];

      component.checkEquipmentType(event);

      expect(component.equipmentEdited).toBe(true);
      expect(component.craneEquipmentTypeChosen).toBe(true);
      expect(component.isAssociatedWithCraneRequest).toBe(true);
    });

    it('should handle checkEquipmentType with non-crane equipment', () => {
      component.equipmentList = [
        { id: 1, PresetEquipmentType: { isCraneType: true } },
        { id: 2, PresetEquipmentType: { isCraneType: false } }
      ];
      const event = [{ id: 2 }];

      component.checkEquipmentType(event);

      expect(component.equipmentEdited).toBe(true);
      expect(component.isAssociatedWithCraneRequest).toBe(false);
    });

    it('should handle checkEquipmentType with empty event', () => {
      component.checkEquipmentType([]);
      expect(component.craneEquipmentTypeChosen).toBe(false);
    });

    it('should handle checkEquipmentType with null event', () => {
      component.checkEquipmentType(null);
      expect(component.craneEquipmentTypeChosen).toBe(false);
    });

    it('should handle checkEquipmentType with undefined event', () => {
      component.checkEquipmentType(undefined);
      expect(component.craneEquipmentTypeChosen).toBe(false);
    });

    it('should handle checkEquipmentType with mixed equipment types', () => {
      component.equipmentList = [
        { id: 1, PresetEquipmentType: { isCraneType: true } },
        { id: 2, PresetEquipmentType: { isCraneType: false } },
        { id: 3, PresetEquipmentType: { isCraneType: true } }
      ];
      const event = [{ id: 1 }, { id: 2 }];

      component.checkEquipmentType(event);

      expect(component.equipmentEdited).toBe(true);
      expect(component.craneEquipmentTypeChosen).toBe(true);
      expect(component.isAssociatedWithCraneRequest).toBe(true);
    });

    it('should handle checkEquipmentType with equipment not in list', () => {
      component.equipmentList = [
        { id: 1, PresetEquipmentType: { isCraneType: true } }
      ];
      const event = [{ id: 999 }];

      component.checkEquipmentType(event);

      expect(component.equipmentEdited).toBe(true);
      expect(component.craneEquipmentTypeChosen).toBe(false);
    });

    it('should handle onEditSubmitForm for equipment', () => {
      component.onEditSubmitForm('equipment');
      expect(component.equipmentEdited).toBe(true);
    });

    it('should handle onEditSubmitForm for deliveryTime', () => {
      component.onEditSubmitForm('deliveryTime');
      expect(component.formEdited).toBe(false); // This field doesn't set any specific flag
    });

    it('should handle onEditSubmitForm for location', () => {
      component.onEditSubmitForm('location');
      expect(component.formEdited).toBe(false); // This field doesn't set any specific flag
    });

    it('should handle onEditSubmitForm for unknown field', () => {
      component.onEditSubmitForm('unknownField');
      // Should not throw error and not set any flags
      expect(component.companyEdited).toBe(false);
      expect(component.dfowEdited).toBe(false);
    });

    it('should handle setDefaultDeliveryTime with different date', () => {
      const mockDate = new Date('2025-01-15T14:30:00');
      component.deliverEditMultipleForm.patchValue({
        deliveryDate: mockDate
      });

      component.setDefaultDeliveryTime();

      const startTime = component.deliverEditMultipleForm.get('deliveryStart').value;
      const endTime = component.deliverEditMultipleForm.get('deliveryEnd').value;

      expect(startTime).toBeDefined();
      expect(endTime).toBeDefined();
      expect(new Date(endTime).getTime()).toBeGreaterThan(new Date(startTime).getTime());
    });

    it('should handle convertStart method with different parameters', () => {
      const testDate = new Date('2024-12-25');
      const result = component.convertStart(testDate, 10, 30);

      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
    });

    it('should handle numberOnly with valid number key', () => {
      const event = { which: 53, keyCode: 53 }; // Key '5'
      const result = component.numberOnly(event);
      expect(result).toBe(true);
    });

    it('should handle numberOnly with invalid character key', () => {
      const event = { which: 65, keyCode: 65 }; // Key 'A'
      const result = component.numberOnly(event);
      expect(result).toBe(false);
    });

    it('should handle numberOnly with special keys', () => {
      const event = { which: 8, keyCode: 8 }; // Backspace
      const result = component.numberOnly(event);
      expect(result).toBe(true);
    });

    it('should handle deliveryEndTimeChangeDetection', () => {
      component.deliveryEndTimeChangeDetection();
      expect(component.NDRTimingChanged).toBe(true);
    });

    it('should handle changeDate with valid event', () => {
      const mockEvent = new Date('2024-12-31T15:45:00');
      component.editModalLoader = false;

      component.changeDate(mockEvent);

      expect(component.NDRTimingChanged).toBe(true);
    });

    it('should handle changeDate when editModalLoader is true', () => {
      const mockEvent = new Date('2024-12-31T15:45:00');
      component.editModalLoader = true;
      component.NDRTimingChanged = false;

      component.changeDate(mockEvent);

      expect(component.NDRTimingChanged).toBe(false);
    });
  });

  // Error Handling Tests
  describe('Error Handling', () => {
    it('should handle getDeliveryRequest service error', () => {
      const mockError = { message: 'Service error' };
      deliveryService.listNDR.mockReturnValue(throwError(() => mockError));

      component.getDeliveryRequest();

      expect(component.loader).toBe(true);
      expect(deliveryService.listNDR).toHaveBeenCalled();
    });

    it('should handle getMembers service error', () => {
      const mockError = { message: 'Service error' };
      projectService.listAllMember.mockReturnValue(throwError(() => mockError));

      component.getMembers();

      expect(projectService.listAllMember).toHaveBeenCalled();
    });

    it('should handle getDefinable service error', () => {
      const mockError = { message: 'Service error' };
      projectService.getDefinableWork.mockReturnValue(throwError(() => mockError));

      component.getDefinable();

      expect(projectService.getDefinableWork).toHaveBeenCalled();
    });

    it('should handle setDefaultPerson service error', () => {
      const mockError = { message: 'Service error' };
      deliveryService.getMemberRole.mockReturnValue(throwError(() => mockError));

      component.setDefaultPerson();

      expect(deliveryService.getMemberRole).toHaveBeenCalled();
    });

    it('should handle requestAutocompleteItems service error', () => {
      const mockError = { message: 'Service error' };
      deliveryService.searchNewMember.mockReturnValue(throwError(() => mockError));

      const result = component.requestAutocompleteItems('test');

      expect(deliveryService.searchNewMember).toHaveBeenCalled();
      expect(result).toBeDefined();
    });

    it('should handle showErrorMessage with status code 400', () => {
      const mockError = {
        message: {
          statusCode: 400,
          details: [{ field: 'Test error message' }]
        }
      };
      jest.spyOn(component, 'showError');

      component.showErrorMessage(mockError);

      expect(component.editMultipleSubmitted).toBe(false);
      expect(component.showError).toHaveBeenCalledWith(mockError);
    });

    it('should handle showErrorMessage with different status code', () => {
      const mockError = {
        message: {
          statusCode: 500,
          details: [{ field: 'Server error' }]
        }
      };
      toastrService.error = jest.fn();

      component.showErrorMessage(mockError);

      expect(component.editMultipleSubmitted).toBe(false);
      expect(toastrService.error).toHaveBeenCalledWith('Server error', 'OOPS!');
    });

    it('should handle showErrorMessage without message object', () => {
      const mockError = { error: 'Simple error' };
      toastrService.error = jest.fn();

      component.showErrorMessage(mockError);

      expect(component.editMultipleSubmitted).toBe(false);
      expect(toastrService.error).toHaveBeenCalledWith('Something went wrong', 'OOPS!');
    });
  });

  // Edge Cases Tests
  describe('Edge Cases', () => {
    it('should handle selectStatus with empty string', () => {
      component.selectStatus('');
      expect(component.currentStatus).toBe('');
    });

    it('should handle selectStatus with null', () => {
      component.selectStatus(null);
      expect(component.currentStatus).toBe(null);
    });

    it('should handle extractIds with null form control', () => {
      component.deliverEditMultipleForm.patchValue({
        testField: null
      });

      const result = component.extractIds('testField');

      expect(result).toBeNull();
    });

    it('should handle extractIds with undefined form control', () => {
      component.deliverEditMultipleForm.patchValue({
        testField: undefined
      });

      const result = component.extractIds('testField');

      expect(result).toBeNull();
    });

    it('should handle extractIds with objects without id property', () => {
      component.deliverEditMultipleForm.patchValue({
        testField: [{ name: 'test1' }, { name: 'test2' }]
      });

      const result = component.extractIds('testField');

      expect(result).toEqual([undefined, undefined]);
    });

    it('should handle preparePayload with all validations passing', () => {
      component.deliveryDateEdited = false;
      component.isAssociatedWithCraneRequest = false;
      component.editedFields = 'Test Fields';
      component.voidvalue = false;
      component.selectedDeliveryRequestIdForMultipleEdit = [1, 2];
      component.deliverEditMultipleForm.patchValue({
        escort: false,
        GateId: '',
        status: '',
        companyItems: [],
        person: [],
        defineItems: [],
        EquipmentId: []
      });

      const result = component.preparePayload();

      expect(result).toBeDefined();
      expect(result.deliveryRequestIds).toEqual([1, 2]);
      expect(result.escort).toBe(false);
      expect(result.void).toBe(false);
    });

    it('should handle preparePayload with crane request and valid locations', () => {
      component.deliveryDateEdited = false;
      component.isAssociatedWithCraneRequest = true;
      component.editedFields = 'Test Fields';
      component.voidvalue = true;
      component.selectedDeliveryRequestIdForMultipleEdit = [1];
      component.deliverEditMultipleForm.patchValue({
        escort: true,
        GateId: 1,
        status: 'Approved',
        companyItems: [{ id: 1 }],
        person: [{ id: 1 }],
        defineItems: [{ id: 1 }],
        EquipmentId: [{ id: 1 }],
        cranePickUpLocation: 'Location A',
        craneDropOffLocation: 'Location B'
      });

      const result = component.preparePayload();

      expect(result).toBeDefined();
      expect(result.cranePickUpLocation).toBe('Location A');
      expect(result.craneDropOffLocation).toBe('Location B');
    });
  });

  it('should initialize component properties correctly', () => {
    expect(component.deliveryList).toBeDefined();
    expect(component.filterForm).toBeDefined();
    expect(component.deliverEditMultipleForm).toBeDefined();
  });

  it('should handle ngOnInit', () => {
    jest.spyOn(component, 'getDeliveryRequest');
    jest.spyOn(component, 'getMembers');

    component.ngOnInit();

    expect(component.getDeliveryRequest).toHaveBeenCalled();
    expect(component.getMembers).toHaveBeenCalled();
  });

  it('should handle getDeliveryRequest with all parameters', () => {
    const mockResponse = {
      data: { rows: [], count: 0 },
      lastId: 0,
      statusData: { statusColorCode: JSON.stringify([]) }
    };
    deliveryService.listNDR.mockReturnValue(of(mockResponse));

    component.ProjectId = 1;
    component.ParentCompanyId = 2;
    component.pageSize = 50;
    component.pageNo = 1;
    component.search = 'test';
    component.voidvalue = true;
    component.sortColumn = 'name';
    component.sort = 'ASC';

    component.filterForm.patchValue({
      companyFilter: '1',
      descriptionFilter: 'test desc',
      statusFilter: 'Approved',
      dateFilter: '2024-01-01',
      memberFilter: '1',
      gateFilter: '1',
      equipmentFilter: '1',
      locationFilter: 'test location',
      pickFrom: '2024-01-01',
      pickTo: '2024-01-31'
    });

    component.getDeliveryRequest();

    expect(deliveryService.listNDR).toHaveBeenCalledWith(expect.objectContaining({
      ProjectId: 1,
      pageSize: 50,
      pageNo: 1,
      void: 1,
      search: 'test',
      sortColumn: 'name',
      sort: 'ASC'
    }));
    expect(component.loader).toBe(false);
  });

  it('should handle getDeliveryRequest with status color processing', () => {
    const mockResponse = {
      data: { rows: [], count: 0 },
      lastId: 0,
      statusData: {
        statusColorCode: JSON.stringify([
          { status: 'approved', backgroundColor: '#green', fontColor: '#white' },
          { status: 'pending', backgroundColor: '#yellow', fontColor: '#black' },
          { status: 'delivered', backgroundColor: '#blue', fontColor: '#white' },
          { status: 'rejected', backgroundColor: '#red', fontColor: '#white' },
          { status: 'expired', backgroundColor: '#gray', fontColor: '#white' }
        ])
      }
    };
    deliveryService.listNDR.mockReturnValue(of(mockResponse));

    component.getDeliveryRequest();

    expect(component.approvedBackgroundColor).toBe('#green');
    expect(component.approvedFontColor).toBe('#white');
    expect(component.pendingBackgroundColor).toBe('#yellow');
    expect(component.pendingFontColor).toBe('#black');
    expect(component.deliveredBackgroundColor).toBe('#blue');
    expect(component.deliveredFontColor).toBe('#white');
    expect(component.rejectedBackgroundColor).toBe('#red');
    expect(component.rejectedFontColor).toBe('#white');
    expect(component.expiredBackgroundColor).toBe('#gray');
    expect(component.expiredFontColor).toBe('#white');
  });

  it('should handle getDeliveryRequest with delivery list processing', () => {
    const mockResponse = {
      data: {
        rows: [
          {
            id: 1,
            DeliveryId: 'D001',
            status: 'Approved',
            memberDetails: [{ Member: { id: 1 } }]
          },
          {
            id: 2,
            DeliveryId: 'D002',
            status: 'Pending',
            memberDetails: [{ Member: { id: 2 } }]
          }
        ],
        count: 2
      },
      lastId: 123,
      statusData: { statusColorCode: JSON.stringify([]) }
    };
    deliveryService.listNDR.mockReturnValue(of(mockResponse));
    component.authUser = { RoleId: 2, id: 1 };

    component.getDeliveryRequest();

    expect(component.deliveryList.length).toBe(2);
    expect(component.deliveryList[0].isAllowedToEdit).toBe(true);
    expect(component.deliveryList[1].isAllowedToEdit).toBe(false);
    expect(component.deliveryList[0].isChecked).toBe(false);
    expect(component.deliveryList[1].isChecked).toBe(false);
    expect(component.totalCount).toBe(2);
    expect(component.lastId).toBe(123);
  });

  it('should handle getDeliveryRequest with role 1 permissions', () => {
    const mockResponse = {
      data: {
        rows: [
          {
            id: 1,
            DeliveryId: 'D001',
            status: 'Approved',
            memberDetails: []
          }
        ],
        count: 1
      },
      lastId: 123,
      statusData: { statusColorCode: JSON.stringify([]) }
    };
    deliveryService.listNDR.mockReturnValue(of(mockResponse));
    component.authUser = { RoleId: 1, id: 1 };

    component.getDeliveryRequest();

    expect(component.deliveryList[0].isAllowedToEdit).toBe(true);
  });

  it('should handle getDeliveryRequest with role 4 permissions', () => {
    const mockResponse = {
      data: {
        rows: [
          {
            id: 1,
            DeliveryId: 'D001',
            status: 'Approved',
            memberDetails: []
          }
        ],
        count: 1
      },
      lastId: 123,
      statusData: { statusColorCode: JSON.stringify([]) }
    };
    deliveryService.listNDR.mockReturnValue(of(mockResponse));
    component.authUser = { RoleId: 4, id: 1 };

    component.getDeliveryRequest();

    expect(component.deliveryList[0].isAllowedToEdit).toBe(true);
  });

  it('should handle getMembers successfully', () => {
    const mockResponse = { data: [{ id: 1, name: 'Test Member' }] };
    projectService.listAllMember.mockReturnValue(of(mockResponse));
    component.ProjectId = 1;
    component.ParentCompanyId = 2;

    component.getMembers();

    expect(projectService.listAllMember).toHaveBeenCalledWith({
      ProjectId: 1,
      ParentCompanyId: 2
    });
    expect(component.memberList).toEqual(mockResponse.data);
  });

  it('should handle getDefinable successfully', () => {
    const mockResponse = { data: [{ id: 1, name: 'Test Definable' }] };
    projectService.getDefinableWork.mockReturnValue(of(mockResponse));
    component.ProjectId = 1;
    component.ParentCompanyId = 2;

    component.getDefinable();

    expect(projectService.getDefinableWork).toHaveBeenCalledWith({
      ProjectId: 1,
      ParentCompanyId: 2
    });
    expect(component.defineList).toEqual(mockResponse.data);
    expect(component.modalLoader).toBe(false);
  });

  it('should handle setDefaultDeliveryTime', () => {
    const mockDate = new Date('2024-12-31T10:00:00');
    component.deliverEditMultipleForm.patchValue({
      deliveryDate: mockDate
    });

    component.setDefaultDeliveryTime();

    const startTime = component.deliverEditMultipleForm.get('deliveryStart').value;
    const endTime = component.deliverEditMultipleForm.get('deliveryEnd').value;

    expect(startTime).toBeDefined();
    expect(endTime).toBeDefined();
  });

  it('should handle preparePayload with delivery date validation errors', () => {
    component.deliveryDateEdited = true;
    const sameTime = new Date();
    component.deliverEditMultipleForm.patchValue({
      deliveryDate: sameTime,
      deliveryStart: sameTime,
      deliveryEnd: sameTime
    });
    toastrService.error = jest.fn();

    const result = component.preparePayload();

    expect(result).toBeNull();
    expect(toastrService.error).toHaveBeenCalledWith('Delivery Start time and End time should not be the same');
  });

  it('should handle preparePayload with start time after end time', () => {
    component.deliveryDateEdited = true;
    const startTime = new Date();
    const endTime = new Date(startTime.getTime() - 3600000); // 1 hour earlier
    component.deliverEditMultipleForm.patchValue({
      deliveryDate: startTime,
      deliveryStart: startTime,
      deliveryEnd: endTime
    });
    toastrService.error = jest.fn();

    const result = component.preparePayload();

    expect(result).toBeNull();
    expect(toastrService.error).toHaveBeenCalledWith('Please Enter Start time Lesser than End time');
  });

  it('should handle preparePayload with past date', () => {
    component.deliveryDateEdited = true;
    const pastDate = new Date(Date.now() - 86400000); // Yesterday
    component.deliverEditMultipleForm.patchValue({
      deliveryDate: pastDate,
      deliveryStart: pastDate,
      deliveryEnd: pastDate
    });
    toastrService.error = jest.fn();

    const result = component.preparePayload();

    expect(result).toBeNull();
    expect(toastrService.error).toHaveBeenCalledWith('Please Enter Future Date.');
  });

  it('should handle preparePayload with crane request missing locations', () => {
    component.isAssociatedWithCraneRequest = true;
    component.deliverEditMultipleForm.patchValue({
      cranePickUpLocation: '',
      craneDropOffLocation: ''
    });
    toastrService.error = jest.fn();

    const result = component.preparePayload();

    expect(result).toBeNull();
    expect(toastrService.error).toHaveBeenCalledWith('Please enter Picking From and Picking To');
  });

  it('should handle preparePayload successfully', () => {
    component.deliveryDateEdited = false;
    component.isAssociatedWithCraneRequest = false;
    component.editedFields = 'Test Fields';
    component.voidvalue = true;
    component.selectedDeliveryRequestIdForMultipleEdit = [1, 2, 3];
    component.deliverEditMultipleForm.patchValue({
      escort: true,
      GateId: 1,
      status: 'Approved',
      companyItems: [{ id: 1 }],
      person: [{ id: 1 }],
      defineItems: [{ id: 1 }],
      EquipmentId: [{ id: 1 }]
    });

    const result = component.preparePayload();

    expect(result).toBeDefined();
    expect(result.deliveryRequestIds).toEqual([1, 2, 3]);
    expect(result.escort).toBe(true);
    expect(result.GateId).toBe(1);
    expect(result.status).toBe('Approved');
    expect(result.void).toBe(true);
    expect(result.editedFields).toBe('Test Fields');
    expect(result.companies).toEqual([1]);
    expect(result.persons).toEqual([1]);
    expect(result.define).toEqual([1]);
    expect(result.EquipmentId).toEqual([1]);
  });

  it('should handle extractIds with valid data', () => {
    component.deliverEditMultipleForm.patchValue({
      testField: [{ id: 1 }, { id: 2 }]
    });

    const result = component.extractIds('testField');

    expect(result).toEqual([1, 2]);
  });

  it('should handle extractIds with empty data', () => {
    component.deliverEditMultipleForm.patchValue({
      testField: []
    });

    const result = component.extractIds('testField');

    expect(result).toBeNull();
  });

  it('should initialize with default values', () => {
    expect(component.currentPageNo).toBe(1);
    expect(component.pageSize).toBe(25);
    expect(component.loader).toBeDefined();
    expect(component.filterForm).toBeDefined();
  });

  it('should load delivery requests on initialization', () => {
    const mockResponse = {
      data: {
        rows: [],
        count: 0
      },
      lastId: 0,
      statusData: {
        statusColorCode: JSON.stringify([
          { status: 'approved', backgroundColor: '#fff', fontColor: '#000' },
          { status: 'pending', backgroundColor: '#fff', fontColor: '#000' },
          { status: 'delivered', backgroundColor: '#fff', fontColor: '#000' },
          { status: 'rejected', backgroundColor: '#fff', fontColor: '#000' },
          { status: 'expired', backgroundColor: '#fff', fontColor: '#000' }
        ])
      }
    };

    deliveryService.listNDR.mockReturnValue(of(mockResponse));
    component.getDeliveryRequest();
    expect(deliveryService.listNDR).toHaveBeenCalled();
  });

  it('should handle filter form submission', () => {
    const mockFilterData = {
      companyFilter: '1',
      descriptionFilter: 'test',
      statusFilter: 'Approved',
      dateFilter: '2024-03-20',
      memberFilter: '1',
      gateFilter: '1',
      equipmentFilter: '1',
      locationFilter: 'test',
      pickFrom: '',
      pickTo: ''
    };

    component.filterForm.patchValue(mockFilterData);
    component.getDeliveryRequest();
    expect(deliveryService.listNDR).toHaveBeenCalled();
  });

  it('should open new delivery form modal', () => {
    const mockModalRef = { content: { lastId: 1, closeBtnName: 'Close' } };
    modalService.show.mockReturnValue(mockModalRef as any);

    component.setGateEquipment();
    expect(modalService.show).toHaveBeenCalled();
  });

  it('should handle delivery request selection', () => {
    const mockDelivery = {
      DeliveryId: 1,
      id: 1,
      status: 'Approved',
      isChecked: false
    };

    component.deliveryList = [mockDelivery];
    component.setSelectedCurrentDeliveryRequestItem(0);
    expect(component.selectedDeliveryRequestId).toContain('1');
  });

  it('should handle status update', () => {
    const mockItem = { id: 1, status: 'Approved' };
    component.setStatus(mockItem);
    expect(component.deliveryId).toBe(-1);
    expect(deliveryService.updatedDeliveryId).toHaveBeenCalledWith(mockItem.id);
  });

  it('should open edit modal', () => {
    const mockItem = {
      id: 1,
      ProjectId: 1,
      isAssociatedWithDeliveryRequest: false,
      isAssociatedWithCraneRequest: false
    };

    const mockModalRef = { content: { closeBtnName: 'Close' } };
    modalService.show.mockReturnValue(mockModalRef as any);

    component.openEditModal(mockItem, 'edit');
    expect(modalService.show).toHaveBeenCalled();
  });

  it('should handle form initialization', () => {
    component.deliverForm();
    expect(component.deliverEditMultipleForm).toBeDefined();
    expect(component.deliverEditMultipleForm.get('EquipmentId')).toBeDefined();
    expect(component.deliverEditMultipleForm.get('GateId')).toBeDefined();
  });

  it('should handle definable work loading', () => {
    const mockResponse = {
      data: [
        { id: 1, DFOW: 'Test' }
      ]
    };

    projectService.getDefinableWork.mockReturnValue(of(mockResponse));
    component.getDefinable();
    expect(projectService.getDefinableWork).toHaveBeenCalled();
    expect(component.defineListData).toEqual(mockResponse.data);
  });

  describe('Constructor Subscriptions', () => {
    beforeEach(() => {
      // Reset mocks
      jest.clearAllMocks();
    });

    it('should handle projectParent subscription', () => {
      const mockProjectData = { ProjectId: 2, ParentCompanyId: 3 };
      mockProjectService.projectParent = of(mockProjectData);

      // Create new component instance to trigger constructor
      const newFixture = TestBed.createComponent(DeliveryRequestsComponent);
      const newComponent = newFixture.componentInstance;

      expect(newComponent.ProjectId).toBe(2);
      expect(newComponent.ParentCompanyId).toBe(1); // This comes from the original mock
    });

    it('should handle ParentCompanyId subscription', () => {
      const mockCompanyId = 5;
      mockProjectService.ParentCompanyId = of(mockCompanyId);

      const newFixture = TestBed.createComponent(DeliveryRequestsComponent);
      const newComponent = newFixture.componentInstance;

      expect(newComponent.ParentCompanyId).toBe(5);
    });

    it('should handle loginUser subscription with role 2', () => {
      const mockUser = { RoleId: 2, id: 1 };
      mockDeliveryService.loginUser = of(mockUser);

      const newFixture = TestBed.createComponent(DeliveryRequestsComponent);
      const newComponent = newFixture.componentInstance;

      expect(newComponent.authUser).toEqual(mockUser);
      expect(newComponent.statusValue).toEqual(['Approved', 'Declined']);
    });

    it('should handle loginUser subscription with role 3', () => {
      const mockUser = { RoleId: 3, id: 1 };
      mockDeliveryService.loginUser = of(mockUser);

      const newFixture = TestBed.createComponent(DeliveryRequestsComponent);
      const newComponent = newFixture.componentInstance;

      expect(newComponent.statusValue).toEqual(['Delivered', 'Approved']);
    });

    it('should handle getCurrentStatus subscription', () => {
      const mockDeliveryId = 123;
      mockDeliveryService.getCurrentStatus = of(mockDeliveryId);

      const newFixture = TestBed.createComponent(DeliveryRequestsComponent);
      const newComponent = newFixture.componentInstance;

      expect(newComponent.deliveryId).toBe(123);
    });

    it('should handle fileUpload subscription - uploading', () => {
      mockProjectService.fileUpload = of({ status: 'uploading' });

      const newFixture = TestBed.createComponent(DeliveryRequestsComponent);
      const newComponent = newFixture.componentInstance;

      expect(newComponent.bulkNdrUploadInProgress).toBe(true);
    });

    it('should handle fileUpload subscription - uploadDone', () => {
      mockProjectService.fileUpload = of({ status: 'uploadDone' });

      const newFixture = TestBed.createComponent(DeliveryRequestsComponent);
      const newComponent = newFixture.componentInstance;

      expect(newComponent.bulkNdrUploadInProgress).toBe(false);
    });
  });

  describe('ngOnInit', () => {
    it('should subscribe to router events', () => {
      component.modalRef = { hide: jest.fn() } as any;

      component.ngOnInit();
      routerEventsSubject.next({});

      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should handle refresh subscription', () => {
      jest.spyOn(component, 'getDeliveryRequest');
      mockDeliveryService.refresh = of(true);

      component.ngOnInit();

      expect(component.getDeliveryRequest).toHaveBeenCalled();
    });
  });

  describe('Form Handling', () => {
    beforeEach(() => {
      component.filterDetailsForm();
    });

    it('should create filter form with correct controls', () => {
      expect(component.filterForm.get('companyFilter')).toBeTruthy();
      expect(component.filterForm.get('descriptionFilter')).toBeTruthy();
      expect(component.filterForm.get('statusFilter')).toBeTruthy();
      expect(component.filterForm.get('dateFilter')).toBeTruthy();
      expect(component.filterForm.get('memberFilter')).toBeTruthy();
      expect(component.filterForm.get('gateFilter')).toBeTruthy();
      expect(component.filterForm.get('equipmentFilter')).toBeTruthy();
      expect(component.filterForm.get('locationFilter')).toBeTruthy();
      expect(component.filterForm.get('pickFrom')).toBeTruthy();
      expect(component.filterForm.get('pickTo')).toBeTruthy();
    });

    it('should reset filter form', () => {
      component.filterForm.patchValue({ companyFilter: '1', descriptionFilter: 'test' });
      component.modalRef = { hide: jest.fn() } as any;
      jest.spyOn(component, 'getDeliveryRequest');

      component.resetFilter();

      expect(component.filterCount).toBe(0);
      expect(component.search).toBe('');
      expect(component.pageNo).toBe(1);
      expect(component.filterForm.get('companyFilter').value).toBe('');
      expect(component.getDeliveryRequest).toHaveBeenCalled();
      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should count filters correctly in filterSubmit', () => {
      component.filterForm.patchValue({
        descriptionFilter: 'test',
        dateFilter: '2024-01-01',
        companyFilter: '1',
        memberFilter: '1',
        gateFilter: '1'
      });
      component.modalRef = { hide: jest.fn() } as any;
      jest.spyOn(component, 'getDeliveryRequest');

      component.filterSubmit();

      expect(component.filterCount).toBe(5);
      expect(component.pageNo).toBe(1);
      expect(component.getDeliveryRequest).toHaveBeenCalled();
      expect(component.modalRef.hide).toHaveBeenCalled();
    });
  });

  describe('Data Loading Methods', () => {
    beforeEach(() => {
      component.ProjectId = 1;
      component.ParentCompanyId = 2;
    });

    it('should get members', () => {
      const mockResponse = { data: [{ id: 1, name: 'Test User' }] };
      projectService.listAllMember.mockReturnValue(of(mockResponse));

      component.getMembers();

      expect(projectService.listAllMember).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 2
      });
      expect(component.memberList).toEqual(mockResponse.data);
    });

    it('should get companies for NDR grid', () => {
      const mockResponse = { data: [{ id: 1, companyName: 'Test Company' }] };
      projectService.getCompanies.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getDefinableForNdrGrid');

      component.getCompaniesForNdrGrid();

      expect(projectService.getCompanies).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 2
      });
      expect(component.companyList).toEqual(mockResponse.data);
      expect(component.getDefinableForNdrGrid).toHaveBeenCalled();
    });

    it('should get gates for NDR grid', () => {
      const mockResponse = { data: [{ id: 1, gateName: 'Gate 1' }] };
      projectService.gateList.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getOverAllEquipmentForNdrGrid');

      component.getOverAllGateForNdrGrid();

      expect(component.modalLoader).toBe(true);
      expect(component.gateList).toEqual(mockResponse.data);
      expect(component.getOverAllEquipmentForNdrGrid).toHaveBeenCalled();
    });

    it('should get equipment for NDR grid', () => {
      const mockResponse = { data: [{ id: 1, equipmentName: 'Equipment 1' }] };
      projectService.listEquipment.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getCompaniesForNdrGrid');

      component.getOverAllEquipmentForNdrGrid();

      expect(component.equipmentList).toEqual(mockResponse.data);
      expect(component.getCompaniesForNdrGrid).toHaveBeenCalled();
    });

    it('should get definable work for NDR grid', () => {
      const mockResponse = { data: [{ id: 1, DFOW: 'Test Work' }] };
      projectService.getDefinableWork.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getLocations');

      component.getDefinableForNdrGrid();

      expect(component.defineList).toEqual(mockResponse.data);
      expect(component.getLocations).toHaveBeenCalled();
    });

    it('should get locations', () => {
      const mockResponse = { data: [{ id: 1, locationName: 'Location 1' }] };
      projectService.getLocations.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'closePopupContentModal');

      component.getLocations();

      expect(component.locationList).toEqual(mockResponse.data);
      expect(component.closePopupContentModal).toHaveBeenCalled();
    });

    it('should get delivery requests with complex response processing', () => {
      const mockResponse = {
        data: {
          rows: [
            {
              id: 1,
              DeliveryId: 'D001',
              status: 'Approved',
              memberDetails: [{ Member: { id: 1 } }]
            }
          ],
          count: 1
        },
        lastId: 123,
        statusData: {
          statusColorCode: JSON.stringify([
            { status: 'approved', backgroundColor: '#green', fontColor: '#white' },
            { status: 'pending', backgroundColor: '#yellow', fontColor: '#black' },
            { status: 'delivered', backgroundColor: '#blue', fontColor: '#white' },
            { status: 'rejected', backgroundColor: '#red', fontColor: '#white' },
            { status: 'expired', backgroundColor: '#gray', fontColor: '#white' }
          ])
        }
      };

      deliveryService.listNDR.mockReturnValue(of(mockResponse));
      component.authUser = { RoleId: 4, id: 1 };
      component.filterDetailsForm();
      component.filterForm.patchValue({
        companyFilter: '1',
        descriptionFilter: 'test',
        statusFilter: 'Approved',
        dateFilter: '2024-01-01',
        memberFilter: '1',
        gateFilter: '1',
        equipmentFilter: '1',
        locationFilter: 'test',
        pickFrom: '',
        pickTo: ''
      });

      component.getDeliveryRequest();

      expect(component.loader).toBe(false);
      expect(component.deliveryList).toEqual(mockResponse.data.rows);
      expect(component.totalCount).toBe(1);
      expect(component.lastId).toBe(123);
      expect(component.approvedBackgroundColor).toBe('#green');
      expect(component.deliveryList[0].isAllowedToEdit).toBe(true);
    });
  });

  describe('Modal Operations', () => {
    it('should open new delivery form modal', () => {
      const mockModalRef = { content: { lastId: 0, closeBtnName: '' } };
      modalService.show.mockReturnValue(mockModalRef as any);

      component.setGateEquipment();

      expect(modalService.show).toHaveBeenCalled();
      expect(mockModalRef.content.lastId).toBe(component.lastId);
      expect(mockModalRef.content.closeBtnName).toBe('Close');
    });

    it('should open modal with template', () => {
      const mockTemplate = {} as any;
      const mockModalRef = {};
      modalService.show.mockReturnValue(mockModalRef as any);

      component.openModal(mockTemplate);

      expect(component.currentTemplate).toBe(mockTemplate);
      expect(modalService.show).toHaveBeenCalledWith(mockTemplate, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal'
      });
    });

    it('should open filter modal', () => {
      const mockTemplate = {} as any;
      jest.spyOn(component, 'getOverAllGateForNdrGrid');

      component.openModal1(mockTemplate);

      expect(component.getOverAllGateForNdrGrid).toHaveBeenCalled();
      expect(modalService.show).toHaveBeenCalledWith(mockTemplate, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-sm filter-popup custom-modal'
      });
    });

    it('should close modal and reset states', () => {
      component.submitted = true;
      component.formSubmitted = true;
      component.deleteDeliveryRequestSubmitted = true;
      component.modalRef = { hide: jest.fn() } as any;

      component.close();

      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(component.deleteDeliveryRequestSubmitted).toBe(false);
      expect(component.modalRef.hide).toHaveBeenCalled();
    });

    it('should close edit multiple popup and reset all states', () => {
      component.deliveryList = [{ isChecked: true, DeliveryId: 1, id: 1 }];
      component.selectedDeliveryRequestId = '1,2,3';
      component.modalRef = { hide: jest.fn() } as any;

      component.closeEditMultiplePopup();

      expect(component.formEditSubmitted).toBe(false);
      expect(component.editModalLoader).toBe(false);
      expect(component.selectedDeliveryRequestId).toBe('');
      expect(component.currentDeliveryRequestSelectAll).toBe(false);
      expect(component.deliveryList[0].isChecked).toBe(false);
    });
  });

  describe('Search and Navigation', () => {
    it('should handle search with data', () => {
      jest.spyOn(component, 'getDeliveryRequest');

      component.getSearchNDR('test search');

      expect(component.showSearchbar).toBe(true);
      expect(component.pageNo).toBe(1);
      expect(component.search).toBe('test search');
      expect(component.getDeliveryRequest).toHaveBeenCalled();
    });

    it('should handle search with empty data', () => {
      jest.spyOn(component, 'getDeliveryRequest');

      component.getSearchNDR('');

      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
      expect(component.getDeliveryRequest).toHaveBeenCalled();
    });

    it('should clear search', () => {
      component.showSearchbar = true;
      component.search = 'test';
      component.pageNo = 5;
      jest.spyOn(component, 'getDeliveryRequest');

      component.clear();

      expect(component.showSearchbar).toBe(false);
      expect(component.search).toBe('');
      expect(component.pageNo).toBe(1);
      expect(component.getDeliveryRequest).toHaveBeenCalled();
    });

    it('should redirect to path', () => {
      router.navigate = jest.fn();

      component.redirect('test-path');

      expect(router.navigate).toHaveBeenCalledWith(['/test-path']);
    });

    it('should change page size', () => {
      jest.spyOn(component, 'getDeliveryRequest');

      component.changePageSize(50);

      expect(component.pageSize).toBe(50);
      expect(component.getDeliveryRequest).toHaveBeenCalled();
    });

    it('should change page number', () => {
      jest.spyOn(component, 'getDeliveryRequest');

      component.changePageNo(3);

      expect(component.pageNo).toBe(3);
      expect(component.getDeliveryRequest).toHaveBeenCalled();
    });
  });

  describe('File Operations', () => {
    beforeEach(() => {
      component.ProjectId = 1;
      component.ParentCompanyId = 2;
    });

    it('should handle file drop with valid xlsx file', () => {
      const mockFile = {
        relativePath: 'test.xlsx',
        fileEntry: {
          isFile: true,
          file: jest.fn((callback) => {
            callback(new File([''], 'test.xlsx'));
          })
        }
      };
      const files = [mockFile] as any;

      component.dropped(files);

      expect(component.files).toEqual(files);
    });

    it('should handle file drop with invalid file extension', () => {
      const mockFile = {
        relativePath: 'test.txt',
        fileEntry: { isFile: true }
      };
      const files = [mockFile] as any;
      toastrService.error = jest.fn();

      component.dropped(files);

      expect(toastrService.error).toHaveBeenCalledWith(
        'Please select a valid file. Supported file format (.xlsx)',
        'OOPS!'
      );
    });

    it('should handle multiple files drop', () => {
      const files = [{}, {}] as any;
      toastrService.error = jest.fn();

      component.dropped(files);

      expect(toastrService.error).toHaveBeenCalledWith('Please import single file', 'OOPS!');
    });

    it('should import delivery request successfully', () => {
      const mockResponse = { success: true };
      deliveryService.importBulkNDR.mockReturnValue(of(mockResponse));
      component.formData = new FormData();
      component.modalRef = { hide: jest.fn() } as any;
      component.importSubmitted = false;

      component.importDeliveryRequest();

      expect(deliveryService.importBulkNDR).toHaveBeenCalled();
      expect(projectService.uploadBulkNdrFile).toHaveBeenCalledWith({ status: 'uploading' });
    });

    it('should handle import delivery request error', () => {
      const mockError = { message: { statusCode: 400, details: [{ field: 'error message' }] } };
      deliveryService.importBulkNDR.mockReturnValue(throwError(() => mockError));
      jest.spyOn(component, 'showError');

      component.importDeliveryRequest();

      expect(component.importSubmitted).toBe(false);
      expect(component.showError).toHaveBeenCalledWith(mockError);
    });

    it('should download template file', () => {
      const mockBlob = new Blob(['test'], { type: 'application/xlsx' });
      const mockProjectResponse = {
        data: [{ id: 1, projectName: 'Test Project' }]
      };

      deliveryService.importBulkNDRTemplate.mockReturnValue(of(mockBlob));
      projectService.getProject.mockReturnValue(of(mockProjectResponse));

      // Mock DOM methods
      const mockLink = {
        href: '',
        download: '',
        click: jest.fn()
      };
      jest.spyOn(document, 'createElement').mockReturnValue(mockLink as any);

      // Mock URL.createObjectURL
      Object.defineProperty(window.URL, 'createObjectURL', {
        value: jest.fn().mockReturnValue('mock-url'),
        writable: true
      });

      component.download();

      expect(deliveryService.importBulkNDRTemplate).toHaveBeenCalled();
      expect(projectService.getProject).toHaveBeenCalled();
      expect(mockLink.click).toHaveBeenCalled();
    });

    it('should remove file from list', () => {
      component.files = [{ relativePath: 'file1.xlsx' }, { relativePath: 'file2.xlsx' }] as any;

      component.removeFile(0);

      expect(component.files.length).toBe(1);
      expect(component.files[0].relativePath).toBe('file2.xlsx');
    });

    it('should reset form with "no" action', () => {
      component.modalRef1 = { hide: jest.fn() } as any;

      component.resetForm('no');

      expect(component.modalRef1.hide).toHaveBeenCalled();
    });

    it('should reset form with "yes" action', () => {
      component.modalRef = { hide: jest.fn() } as any;
      component.modalRef1 = { hide: jest.fn() } as any;
      component.files = [{ name: 'test' }] as any;

      component.resetForm('yes');

      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(component.files).toEqual([]);
    });

    it('should close modal with files present', () => {
      component.files = [{ name: 'test' }] as any;
      const mockTemplate = {} as any;

      component.closeModal(mockTemplate);

      expect(modalService.show).toHaveBeenCalledWith(mockTemplate, {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
      });
    });

    it('should close modal without files', () => {
      component.files = [];
      jest.spyOn(component, 'resetForm');

      component.closeModal({} as any);

      expect(component.resetForm).toHaveBeenCalledWith('yes');
    });

    it('should handle openEditMultipleModal', () => {
      const mockTemplate = {} as any;
      jest.spyOn(component, 'deliverForm');
      jest.spyOn(component, 'setDefaultPerson');
      component.selectedDeliveryRequestId = 'D001,D002,';

      component.openEditMultipleModal(mockTemplate);

      expect(component.deliverForm).toHaveBeenCalled();
      expect(component.setDefaultPerson).toHaveBeenCalled();
      expect(modalService.show).toHaveBeenCalledWith(mockTemplate, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal edit-multiple-modal'
      });
      expect(component.selectedDeliveryRequestId).toBe('D001,D002');
    });

    it('should handle getOverAllGateInNewDelivery', () => {
      const mockResponse = { data: [{ id: 1, gateName: 'Gate 1' }] };
      projectService.gateList.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getOverAllEquipmentInNewDelivery');
      component.ProjectId = 1;
      component.ParentCompanyId = 2;

      component.getOverAllGateInNewDelivery();

      expect(component.modalLoader).toBe(true);
      expect(projectService.gateList).toHaveBeenCalledWith({
        ProjectId: 1,
        pageSize: 0,
        pageNo: 0,
        ParentCompanyId: 2
      }, { isFilter: true, showActivatedAlone: true });
      expect(component.gateList).toEqual(mockResponse.data);
      expect(component.getOverAllEquipmentInNewDelivery).toHaveBeenCalled();
    });

    it('should handle getOverAllEquipmentInNewDelivery', () => {
      const mockResponse = { data: [{ id: 1, equipmentName: 'Equipment 1' }] };
      projectService.listEquipment.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'newNdrgetCompanies');
      component.ProjectId = 1;
      component.ParentCompanyId = 2;

      component.getOverAllEquipmentInNewDelivery();

      expect(projectService.listEquipment).toHaveBeenCalledWith({
        ProjectId: 1,
        pageSize: 0,
        pageNo: 0,
        ParentCompanyId: 2
      }, { isFilter: true, showActivatedAlone: true });
      expect(component.equipmentList).toEqual(mockResponse.data);
      expect(component.newNdrgetCompanies).toHaveBeenCalled();
    });

    it('should handle newNdrgetCompanies', () => {
      const mockResponse = { data: [{ id: 1, companyName: 'Company 1' }] };
      projectService.getCompanies.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getDefinable');
      component.ProjectId = 1;
      component.ParentCompanyId = 2;

      component.newNdrgetCompanies();

      expect(projectService.getCompanies).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 2
      });
      expect(component.companyList).toEqual(mockResponse.data);
      expect(component.getDefinable).toHaveBeenCalled();
    });
  });

  // Additional Method Tests for 100% Coverage
  describe('Additional Method Coverage', () => {
    it('should handle handleInputChange with checked checkbox', () => {
      // Mock DOM element
      const mockCheckbox = { checked: true };
      jest.spyOn(document, 'getElementById').mockReturnValue(mockCheckbox as any);

      component.handleInputChange({});

      expect(component.voidvalue).toBe(true);
    });

    it('should handle handleInputChange with unchecked checkbox', () => {
      // Mock DOM element
      const mockCheckbox = { checked: false };
      jest.spyOn(document, 'getElementById').mockReturnValue(mockCheckbox as any);

      component.handleInputChange({});

      expect(component.voidvalue).toBe(false);
    });

    it('should handle handleInputChange with null checkbox', () => {
      jest.spyOn(document, 'getElementById').mockReturnValue(null);

      expect(() => component.handleInputChange({})).not.toThrow();
    });

    it('should handle sortByField method', () => {
      jest.spyOn(component, 'getDeliveryRequest');

      component.sortByField('name', 'ASC');

      expect(component.sortColumn).toBe('name');
      expect(component.sort).toBe('ASC');
      expect(component.getDeliveryRequest).toHaveBeenCalled();
    });

    it('should handle editNDRSuccess method', () => {
      const mockResponse = { message: 'Success message' };
      const mockSocket = { emit: jest.fn() };
      component.socket = mockSocket as any;
      jest.spyOn(component, 'deliverForm');
      jest.spyOn(component, 'closeEditMultiplePopup');

      component.editNDRSuccess(mockResponse);

      expect(toastrService.success).toHaveBeenCalledWith('Success message', 'Success');
      expect(mockSocket.emit).toHaveBeenCalledWith('NDREditHistory', mockResponse);
      expect(component.deliverForm).toHaveBeenCalled();
      expect(component.closeEditMultiplePopup).toHaveBeenCalled();
    });

    it('should handle submitEditMultiple success', () => {
      const mockPayload = { test: 'data' };
      const mockResponse = { message: 'Updated successfully' };
      deliveryService.updateRequest.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'editNDRSuccess');

      component.submitEditMultiple(mockPayload);

      expect(component.editMultipleSubmitted).toBe(true);
      expect(deliveryService.updateRequest).toHaveBeenCalledWith(mockPayload);
      expect(component.editNDRSuccess).toHaveBeenCalledWith(mockResponse);
    });

    it('should handle submitEditMultiple error', () => {
      const mockPayload = { test: 'data' };
      const mockError = { message: 'Error occurred' };
      deliveryService.updateRequest.mockReturnValue(throwError(() => mockError));
      jest.spyOn(component, 'showErrorMessage');

      component.submitEditMultiple(mockPayload);

      expect(component.editMultipleSubmitted).toBe(true);
      expect(component.showErrorMessage).toHaveBeenCalledWith(mockError);
    });

    it('should handle onEditMultipleSubmit with valid payload', () => {
      jest.spyOn(component, 'preparePayload').mockReturnValue({ valid: 'payload' });
      jest.spyOn(component, 'submitEditMultiple');

      component.onEditMultipleSubmit();

      expect(component.preparePayload).toHaveBeenCalled();
      expect(component.submitEditMultiple).toHaveBeenCalledWith({ valid: 'payload' });
    });

    it('should handle onEditMultipleSubmit with null payload', () => {
      jest.spyOn(component, 'preparePayload').mockReturnValue(null);
      jest.spyOn(component, 'submitEditMultiple');

      component.onEditMultipleSubmit();

      expect(component.preparePayload).toHaveBeenCalled();
      expect(component.submitEditMultiple).not.toHaveBeenCalled();
    });

    it('should handle editMultipleConfirmation with "no" action', () => {
      component.modalRef2 = { hide: jest.fn() } as any;

      component.editMultipleConfirmation('no');

      expect(component.modalRef2.hide).toHaveBeenCalled();
    });

    it('should handle editMultipleConfirmation with "yes" action', () => {
      component.modalRef2 = { hide: jest.fn() } as any;
      jest.spyOn(component, 'onEditMultipleSubmit');

      component.editMultipleConfirmation('yes');

      expect(component.modalRef2.hide).toHaveBeenCalled();
      expect(component.onEditMultipleSubmit).toHaveBeenCalled();
    });

    it('should handle openConfirmationPopup with form values', () => {
      const mockTemplate = {} as any;
      component.deliverEditMultipleForm.patchValue({
        companyItems: [{ id: 1 }],
        defineItems: [{ id: 2 }],
        person: [{ id: 3 }],
        EquipmentId: [{ id: 4 }],
        escort: true,
        GateId: 5,
        status: 'Approved',
        deliveryDate: '2024-12-31',
        deliveryStart: '08:00',
        deliveryEnd: '17:00',
        cranePickUpLocation: 'Location A',
        craneDropOffLocation: 'Location B'
      });
      component.companyEdited = true;
      component.dfowEdited = true;
      component.responsiblePersonEdited = true;
      component.equipmentEdited = true;
      component.escortEdited = true;
      component.gateEdited = true;
      component.statusEdited = true;
      component.deliveryDateEdited = true;

      component.openConfirmationPopup(mockTemplate);

      expect(component.editedFields).toContain('Company');
      expect(component.editedFields).toContain('DFOW');
      expect(component.editedFields).toContain('Responsible Person');
      expect(component.editedFields).toContain('Equipment');
      expect(component.editedFields).toContain('Escort');
      expect(component.editedFields).toContain('Gate');
      expect(component.editedFields).toContain('Status');
      expect(component.editedFields).toContain('Delivery Date');
      expect(modalService.show).toHaveBeenCalledWith(mockTemplate, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-md confirmation-popup modal-dialog-centered custom-modal'
      });
    });
  });

  describe('Keyboard Event Handlers', () => {
    it('should handle toggle keydown with Enter key', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      jest.spyOn(component, 'sortByField');

      component.handleToggleKeydown(mockEvent, 'field', 'ASC');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.sortByField).toHaveBeenCalledWith('field', 'ASC');
    });

    it('should handle toggle keydown with Space key', () => {
      const mockEvent = { key: ' ', preventDefault: jest.fn() } as any;
      jest.spyOn(component, 'sortByField');

      component.handleToggleKeydown(mockEvent, 'field', 'DESC');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.sortByField).toHaveBeenCalledWith('field', 'DESC');
    });

    it('should handle down keydown with clear action', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      jest.spyOn(component, 'clear');

      component.handleDownKeydown(mockEvent, null, null, 'clear');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.clear).toHaveBeenCalled();
    });

    it('should handle down keydown with filter action', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      const mockData = { template: 'test' };
      jest.spyOn(component, 'openModal1');

      component.handleDownKeydown(mockEvent, mockData, null, 'filter');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.openModal1).toHaveBeenCalledWith(mockData);
    });

    it('should handle down keydown with open action', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      const mockData = { id: 1 };
      const mockItem = 'current';
      jest.spyOn(component, 'openIdModal');

      component.handleDownKeydown(mockEvent, mockData, mockItem, 'open');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.openIdModal).toHaveBeenCalledWith(mockData, mockItem);
    });

    it('should handle down keydown with edit action', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      const mockData = { id: 1 };
      const mockItem = 'edit';
      jest.spyOn(component, 'openEditModal');

      component.handleDownKeydown(mockEvent, mockData, mockItem, 'edit');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.openEditModal).toHaveBeenCalledWith(mockData, mockItem);
    });

    it('should handle down keydown with file action', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      jest.spyOn(component, 'removeFile');

      component.handleDownKeydown(mockEvent, 0, null, 'file');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.removeFile).toHaveBeenCalledWith(0);
    });

    it('should handle down keydown with download action', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;
      jest.spyOn(component, 'download');

      component.handleDownKeydown(mockEvent, null, null, 'download');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.download).toHaveBeenCalled();
    });

    it('should not handle keydown with other keys', () => {
      const mockEvent = { key: 'a', preventDefault: jest.fn() } as any;
      jest.spyOn(component, 'clear');

      component.handleDownKeydown(mockEvent, null, null, 'clear');

      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      expect(component.clear).not.toHaveBeenCalled();
    });

    it('should handle toggle keydown with non-Enter/Space key', () => {
      const mockEvent = { key: 'Tab', preventDefault: jest.fn() } as any;
      jest.spyOn(component, 'sortByField');

      component.handleToggleKeydown(mockEvent, 'field', 'ASC');

      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      expect(component.sortByField).not.toHaveBeenCalled();
    });

    it('should handle down keydown with non-Enter/Space key', () => {
      const mockEvent = { key: 'Tab', preventDefault: jest.fn() } as any;
      jest.spyOn(component, 'clear');

      component.handleDownKeydown(mockEvent, null, null, 'clear');

      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      expect(component.clear).not.toHaveBeenCalled();
    });
  });

  describe('Additional Method Coverage', () => {
    beforeEach(() => {
      component.ProjectId = 1;
      component.ParentCompanyId = 2;
      component.deliverForm();
    });

    it('should handle setDefaultPerson method', () => {
      const mockResponse = {
        data: {
          id: 1,
          User: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>'
          }
        }
      };
      deliveryService.getMemberRole.mockReturnValue(of(mockResponse));
      deliveryService.updateLoginUser = jest.fn();

      component.setDefaultPerson();

      expect(deliveryService.getMemberRole).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 2
      });
      expect(deliveryService.updateLoginUser).toHaveBeenCalledWith(mockResponse.data);
    });

    it('should handle setDefaultPerson with no lastName', () => {
      const mockResponse = {
        data: {
          id: 1,
          User: {
            firstName: 'John',
            lastName: null,
            email: '<EMAIL>'
          }
        }
      };
      deliveryService.getMemberRole.mockReturnValue(of(mockResponse));

      component.setDefaultPerson();

      const expectedEmail = 'John (<EMAIL>)';
      expect(component.deliverEditMultipleForm.get('person').value[0].email).toBe(expectedEmail);
    });

    it('should handle requestAutocompleteItems', () => {
      const mockResponse = { data: [{ id: 1, name: 'Test User' }] };
      deliveryService.searchNewMember.mockReturnValue(of(mockResponse));

      const result = component.requestAutocompleteItems('test');

      expect(deliveryService.searchNewMember).toHaveBeenCalledWith({
        ProjectId: 1,
        search: 'test',
        ParentCompanyId: 2
      });
      expect(result).toBeDefined();
    });

    it('should handle onEditSubmitForm for different field types', () => {
      // Test companies
      component.onEditSubmitForm('companies');
      expect(component.companyEdited).toBe(true);

      // Test dfow
      component.onEditSubmitForm('dfow');
      expect(component.dfowEdited).toBe(true);

      // Test escort
      component.onEditSubmitForm('escort');
      expect(component.escortEdited).toBe(true);

      // Test persons
      component.onEditSubmitForm('persons');
      expect(component.responsiblePersonEdited).toBe(true);

      // Test gate
      component.onEditSubmitForm('gate');
      expect(component.gateEdited).toBe(true);

      // Test void
      component.onEditSubmitForm('void');
      expect(component.voidEdited).toBe(true);

      // Test status
      component.onEditSubmitForm('status');
      expect(component.statusEdited).toBe(true);
    });

    it('should handle onEditSubmitForm for deliveryDate with value', () => {
      component.deliverEditMultipleForm.get('deliveryDate').setValue('2024-12-31');
      jest.spyOn(component, 'setDefaultDeliveryTime');

      component.onEditSubmitForm('deliveryDate');

      expect(component.setDefaultDeliveryTime).toHaveBeenCalled();
      expect(component.deliveryDateEdited).toBe(true);
    });

    it('should handle onEditSubmitForm for deliveryDate without value', () => {
      component.deliverEditMultipleForm.get('deliveryDate').setValue('');

      component.onEditSubmitForm('deliveryDate');

      expect(component.deliveryDateEdited).toBe(false);
      expect(component.deliverEditMultipleForm.get('deliveryStart').value).toBe('');
      expect(component.deliverEditMultipleForm.get('deliveryEnd').value).toBe('');
    });

    it('should handle checkEquipmentType with crane equipment', () => {
      component.equipmentList = [
        { id: 1, PresetEquipmentType: { isCraneType: true } },
        { id: 2, PresetEquipmentType: { isCraneType: false } }
      ];
      const event = [{ id: 1 }];

      component.checkEquipmentType(event);

      expect(component.equipmentEdited).toBe(true);
      expect(component.craneEquipmentTypeChosen).toBe(true);
      expect(component.isAssociatedWithCraneRequest).toBe(true);
    });

    it('should handle checkEquipmentType with non-crane equipment', () => {
      component.equipmentList = [
        { id: 1, PresetEquipmentType: { isCraneType: true } },
        { id: 2, PresetEquipmentType: { isCraneType: false } }
      ];
      const event = [{ id: 2 }];

      component.checkEquipmentType(event);

      expect(component.equipmentEdited).toBe(true);
      expect(component.isAssociatedWithCraneRequest).toBe(false);
    });

    it('should handle checkEquipmentType with empty event', () => {
      component.checkEquipmentType([]);

      expect(component.craneEquipmentTypeChosen).toBe(false);
    });

    it('should handle checkEquipmentType with null event', () => {
      component.checkEquipmentType(null);

      expect(component.craneEquipmentTypeChosen).toBe(false);
    });
  });

  describe('Selection and Status Handling', () => {
    beforeEach(() => {
      component.deliveryList = [
        { id: 1, DeliveryId: 'D001', status: 'Approved', isChecked: false, isAllowedToEdit: true },
        { id: 2, DeliveryId: 'D002', status: 'Pending', isChecked: false, isAllowedToEdit: true },
        { id: 3, DeliveryId: 'D003', status: 'Delivered', isChecked: false, isAllowedToEdit: false }
      ];
    });

    it('should select all delivery requests for edit', () => {
      component.selectAllCurrentDeliveryRequestForEdit();

      expect(component.currentDeliveryRequestSelectAll).toBe(true);
      expect(component.deliveryList[0].isChecked).toBe(true);
      expect(component.deliveryList[1].isChecked).toBe(true);
      expect(component.deliveryList[2].isChecked).toBe(false); // Not allowed to edit
      expect(component.selectedDeliveryRequestId).toBe('D001,D002');
    });

    it('should deselect all delivery requests', () => {
      component.currentDeliveryRequestSelectAll = true;
      component.deliveryList.forEach((item: any) => item.isChecked = true);

      component.selectAllCurrentDeliveryRequestForEdit();

      expect(component.currentDeliveryRequestSelectAll).toBe(false);
      expect(component.deliveryList.every((item: any) => !item.isChecked)).toBe(true);
    });

    it('should set selected delivery request item', () => {
      component.setSelectedCurrentDeliveryRequestItem(0);

      expect(component.deliveryList[0].isChecked).toBe(true);
      expect(component.selectedDeliveryRequestId).toContain('D001');
      expect(component.selectedDeliveryRequestIdForMultipleEdit).toContain(1);
    });

    it('should unset selected delivery request item', () => {
      component.deliveryList[0].isChecked = true;
      component.selectedDeliveryRequestId = 'D001,';
      component.selectedDeliveryRequestIdForMultipleEdit = [1];

      component.setSelectedCurrentDeliveryRequestItem(0);

      expect(component.deliveryList[0].isChecked).toBe(false);
      expect(component.selectedDeliveryRequestId).toBe('');
    });

    it('should check if delivery request row is selected', () => {
      expect(component.checkIfCurrentDeliveryRequestRowSelected()).toBe(true);

      component.deliveryList[0].isChecked = true;
      expect(component.checkIfCurrentDeliveryRequestRowSelected()).toBe(false);

      component.currentDeliveryRequestSelectAll = true;
      expect(component.checkIfCurrentDeliveryRequestRowSelected()).toBe(false);
    });

    it('should set status for delivery item', () => {
      const mockItem = { id: 1, status: 'Approved' };
      component.authUser = { RoleId: 2 };

      component.setStatus(mockItem);

      expect(component.deliveryId).toBe(-1);
      expect(component.showStatus).toBe(true);
      expect(deliveryService.updatedDeliveryId).toHaveBeenCalledWith(1);
    });

    it('should not show status for expired item', () => {
      const mockItem = { id: 1, status: 'Expired' };
      component.authUser = { RoleId: 2 };

      component.setStatus(mockItem);

      expect(component.showStatus).toBe(false);
    });

    it('should select status', () => {
      component.selectStatus('Approved');
      expect(component.currentStatus).toBe('Approved');
    });

    it('should sort by field', () => {
      jest.spyOn(component, 'getDeliveryRequest');

      component.sortByField('name', 'ASC');

      expect(component.sortColumn).toBe('name');
      expect(component.sort).toBe('ASC');
      expect(component.getDeliveryRequest).toHaveBeenCalled();
    });
  });

  describe('Utility Methods', () => {
    it('should handle input change for void checkbox', () => {
      // Mock DOM element
      const mockCheckbox = { checked: true };
      jest.spyOn(document, 'getElementById').mockReturnValue(mockCheckbox as any);

      component.handleInputChange({});

      expect(component.voidvalue).toBe(true);
    });

    it('should handle input change for unchecked void checkbox', () => {
      const mockCheckbox = { checked: false };
      jest.spyOn(document, 'getElementById').mockReturnValue(mockCheckbox as any);

      component.handleInputChange({});

      expect(component.voidvalue).toBe(false);
    });

    it('should validate number only input - valid', () => {
      const mockEvent = { which: 53, keyCode: 53 }; // '5'
      expect(component.numberOnly(mockEvent)).toBe(true);
    });

    it('should validate number only input - invalid', () => {
      const mockEvent = { which: 65, keyCode: 65 }; // 'A'
      expect(component.numberOnly(mockEvent)).toBe(false);
    });

    it('should validate number only input - special keys', () => {
      const mockEvent = { which: 8, keyCode: 8 }; // Backspace
      expect(component.numberOnly(mockEvent)).toBe(true);
    });

    it('should show error message', () => {
      const mockError = {
        message: {
          details: [{ field: 'Test error message' }]
        }
      };
      toastrService.error = jest.fn();

      component.showError(mockError);

      expect(component.submitted).toBe(false);
      expect(component.formSubmitted).toBe(false);
      expect(toastrService.error).toHaveBeenCalledWith('Test error message');
    });

    it('should close popup content modal', () => {
      component.modalLoader = true;

      component.closePopupContentModal();

      expect(component.modalLoader).toBe(false);
    });

    it('should open ID modal', () => {
      const mockItem = { id: 1, ProjectId: 1 };
      component.ProjectId = 1;
      component.ParentCompanyId = 2;

      component.openIdModal(mockItem, 'current');

      expect(deliveryService.updateStateOfNDR).toHaveBeenCalledWith('current');
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should open edit modal for crane request', () => {
      const mockItem = {
        id: 1,
        CraneRequestId: 123,
        isAssociatedWithDeliveryRequest: false,
        isAssociatedWithCraneRequest: false,
        recurrence: { id: 1, recurrenceEndDate: '2024-12-31' }
      };

      component.openEditModal(mockItem, 'crane');

      expect(deliveryService.updateStateOfNDR).toHaveBeenCalledWith('crane');
      expect(deliveryService.updatedEditCraneRequestId).toHaveBeenCalledWith(123);
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should open edit modal for delivery request', () => {
      const mockItem = {
        id: 1,
        isAssociatedWithDeliveryRequest: true,
        isAssociatedWithCraneRequest: false
      };

      component.openEditModal(mockItem, 'edit');

      expect(deliveryService.updateStateOfNDR).toHaveBeenCalledWith('edit');
      expect(deliveryService.updatedDeliveryId).toHaveBeenCalledWith(1);
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should close existing modal before opening edit modal', () => {
      component.modalRef = { hide: jest.fn() } as any;
      jest.spyOn(component, 'close');
      const mockItem = { id: 1, isAssociatedWithDeliveryRequest: true };

      component.openEditModal(mockItem, 'edit');

      expect(component.close).toHaveBeenCalled();
    });
  });

  describe('Form Validation and Time Handling', () => {
    beforeEach(() => {
      component.deliverForm();
    });

    it('should convert start time correctly', () => {
      const testDate = new Date(2024, 0, 15); // January 15, 2024
      const result = component.convertStart(testDate, 9, 30);

      expect(result).toContain('Mon, 15 Jan 2024 09:30:00 GMT');
    });

    it('should check if start and end times are the same', () => {
      const startTime = new Date(2024, 0, 15, 9, 0);
      const endTime = new Date(2024, 0, 15, 9, 0);

      expect(component.checkNewDeliveryStartEndSame(startTime, endTime)).toBe(true);

      const differentEndTime = new Date(2024, 0, 15, 10, 0);
      expect(component.checkNewDeliveryStartEndSame(startTime, differentEndTime)).toBe(false);
    });

    it('should check start time is before end time', () => {
      const startTime = new Date(2024, 0, 15, 9, 0);
      const endTime = new Date(2024, 0, 15, 10, 0);

      expect(component.checkStartEnd(startTime, endTime)).toBe(true);

      const laterStartTime = new Date(2024, 0, 15, 11, 0);
      expect(component.checkStartEnd(laterStartTime, endTime)).toBe(false);
    });

    it('should check future date validation', () => {
      const futureStart = new Date(Date.now() + 86400000); // Tomorrow
      const futureEnd = new Date(Date.now() + 90000000); // Day after tomorrow

      expect(component.checkEditDeliveryFutureDate(futureStart, futureEnd)).toBe(true);

      const pastStart = new Date(Date.now() - 86400000); // Yesterday
      expect(component.checkEditDeliveryFutureDate(pastStart, futureEnd)).toBe(false);
    });

    it('should set default delivery time', () => {
      component.setDefaultDeliveryTime();

      expect(component.deliveryStart).toBeDefined();
      expect(component.deliveryEnd).toBeDefined();
      expect(component.deliverEditMultipleForm.get('deliveryStart').value).toBeDefined();
      expect(component.deliverEditMultipleForm.get('deliveryEnd').value).toBeDefined();
    });

    it('should change delivery end time when start time changes', () => {
      const startTime = new Date(2024, 0, 15, 9, 30);
      component.editModalLoader = false;

      component.changeDate(startTime);

      expect(component.NDRTimingChanged).toBe(true);
      expect((component.deliveryEnd as Date).getHours()).toBe(10);
      expect((component.deliveryEnd as Date).getMinutes()).toBe(30);
    });

    it('should not change time when modal is loading', () => {
      const startTime = new Date(2024, 0, 15, 9, 30);
      component.editModalLoader = true;
      component.NDRTimingChanged = false;

      component.changeDate(startTime);

      expect(component.NDRTimingChanged).toBe(false);
    });

    it('should detect delivery end time change', () => {
      component.NDRTimingChanged = false;

      component.deliveryEndTimeChangeDetection();

      expect(component.NDRTimingChanged).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle download error', () => {
      const mockError = { message: 'Download failed' };
      deliveryService.importBulkNDRTemplate.mockReturnValue(of(new Blob()));
      projectService.getProject.mockReturnValue(throwError(() => mockError));
      toastrService.error = jest.fn();

      component.download();

      expect(toastrService.error).toHaveBeenCalledWith('Download failed', 'OOPS!');
    });

    it('should handle import error without message', () => {
      const mockError = {};
      deliveryService.importBulkNDR.mockReturnValue(throwError(() => mockError));
      toastrService.error = jest.fn();

      component.importDeliveryRequest();

      expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should handle import error with custom message', () => {
      const mockError = { message: 'Custom error message' };
      deliveryService.importBulkNDR.mockReturnValue(throwError(() => mockError));
      toastrService.error = jest.fn();

      component.importDeliveryRequest();

      expect(toastrService.error).toHaveBeenCalledWith('Custom error message', 'OOPS!');
    });
  });

  describe('Edit Multiple Operations', () => {
    beforeEach(() => {
      component.deliverForm();
      component.ProjectId = 1;
      component.ParentCompanyId = 2;
      component.selectedDeliveryRequestIdForMultipleEdit = [1, 2, 3];
    });

    it('should handle openConfirmationPopup with edited fields', () => {
      const mockTemplate = {} as any;
      component.companyEdited = true;
      component.dfowEdited = true;
      component.deliverEditMultipleForm.patchValue({
        companyItems: [{ id: 1 }],
        defineItems: [{ id: 1 }]
      });

      component.openConfirmationPopup(mockTemplate);

      expect(component.editedFields).toContain('Responsible Company');
      expect(component.editedFields).toContain('Definable Feature Of Work');
      expect(modalService.show).toHaveBeenCalledWith(mockTemplate, {
        keyboard: false,
        class: 'modal-md cancelConfirmation-popup modal-dialog-centered custom-modal'
      });
    });

    it('should handle openConfirmationPopup with no edited fields', () => {
      const mockTemplate = {} as any;
      component.companyEdited = false;
      component.dfowEdited = false;

      component.openConfirmationPopup(mockTemplate);

      expect(component.editedFields).toBe('');
      expect(modalService.show).not.toHaveBeenCalled();
    });

    it('should handle editMultipleConfirmation with "no" action', () => {
      component.modalRef2 = { hide: jest.fn() } as any;

      component.editMultipleConfirmation('no');

      expect(component.modalRef2.hide).toHaveBeenCalled();
    });

    it('should handle editMultipleConfirmation with "yes" action', () => {
      component.modalRef2 = { hide: jest.fn() } as any;
      jest.spyOn(component, 'onEditMultipleSubmit');

      component.editMultipleConfirmation('yes');

      expect(component.modalRef2.hide).toHaveBeenCalled();
      expect(component.onEditMultipleSubmit).toHaveBeenCalled();
    });

    it('should handle preparePayload with delivery date validation errors', () => {
      component.deliveryDateEdited = true;
      const sameTime = new Date();
      component.deliverEditMultipleForm.patchValue({
        deliveryDate: sameTime,
        deliveryStart: sameTime,
        deliveryEnd: sameTime
      });
      toastrService.error = jest.fn();

      const result = component.preparePayload();

      expect(result).toBeNull();
      expect(toastrService.error).toHaveBeenCalledWith('Delivery Start time and End time should not be the same');
    });

    it('should handle preparePayload with start time after end time', () => {
      component.deliveryDateEdited = true;
      const startTime = new Date();
      const endTime = new Date(startTime.getTime() - 3600000); // 1 hour earlier
      component.deliverEditMultipleForm.patchValue({
        deliveryDate: startTime,
        deliveryStart: startTime,
        deliveryEnd: endTime
      });
      toastrService.error = jest.fn();

      const result = component.preparePayload();

      expect(result).toBeNull();
      expect(toastrService.error).toHaveBeenCalledWith('Please Enter Start time Lesser than End time');
    });

    it('should handle preparePayload with past date', () => {
      component.deliveryDateEdited = true;
      const pastDate = new Date(Date.now() - 86400000); // Yesterday
      component.deliverEditMultipleForm.patchValue({
        deliveryDate: pastDate,
        deliveryStart: pastDate,
        deliveryEnd: pastDate
      });
      toastrService.error = jest.fn();

      const result = component.preparePayload();

      expect(result).toBeNull();
      expect(toastrService.error).toHaveBeenCalledWith('Please Enter Future Date.');
    });

    it('should handle preparePayload with crane request missing locations', () => {
      component.isAssociatedWithCraneRequest = true;
      component.deliverEditMultipleForm.patchValue({
        cranePickUpLocation: '',
        craneDropOffLocation: ''
      });
      toastrService.error = jest.fn();

      const result = component.preparePayload();

      expect(result).toBeNull();
      expect(toastrService.error).toHaveBeenCalledWith('Please enter Picking From and Picking To');
    });

    it('should handle preparePayload successfully', () => {
      component.deliveryDateEdited = false;
      component.isAssociatedWithCraneRequest = false;
      component.editedFields = 'Test Fields';
      component.voidvalue = true;
      component.deliverEditMultipleForm.patchValue({
        escort: true,
        GateId: 1,
        status: 'Approved',
        companyItems: [{ id: 1 }],
        person: [{ id: 1 }],
        defineItems: [{ id: 1 }],
        EquipmentId: [{ id: 1 }]
      });

      const result = component.preparePayload();

      expect(result).toBeDefined();
      expect(result.deliveryRequestIds).toEqual([1, 2, 3]);
      expect(result.escort).toBe(true);
      expect(result.GateId).toBe(1);
      expect(result.status).toBe('Approved');
      expect(result.void).toBe(true);
      expect(result.editedFields).toBe('Test Fields');
      expect(result.companies).toEqual([1]);
      expect(result.persons).toEqual([1]);
      expect(result.define).toEqual([1]);
      expect(result.EquipmentId).toEqual([1]);
    });

    it('should handle extractIds with valid data', () => {
      component.deliverEditMultipleForm.patchValue({
        testField: [{ id: 1 }, { id: 2 }]
      });

      const result = component.extractIds('testField');

      expect(result).toEqual([1, 2]);
    });

    it('should handle extractIds with empty data', () => {
      component.deliverEditMultipleForm.patchValue({
        testField: []
      });

      const result = component.extractIds('testField');

      expect(result).toBeNull();
    });

    it('should handle submitEditMultiple success', () => {
      const mockPayload = { test: 'data' };
      const mockResponse = { message: 'Success' };
      deliveryService.updateRequest.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'editNDRSuccess');

      component.submitEditMultiple(mockPayload);

      expect(component.editMultipleSubmitted).toBe(true);
      expect(deliveryService.updateRequest).toHaveBeenCalledWith(mockPayload);
      expect(component.editNDRSuccess).toHaveBeenCalledWith(mockResponse);
    });

    it('should handle submitEditMultiple error', () => {
      const mockPayload = { test: 'data' };
      const mockError = { message: 'Error occurred' };
      deliveryService.updateRequest.mockReturnValue(throwError(() => mockError));
      jest.spyOn(component, 'showErrorMessage');

      component.submitEditMultiple(mockPayload);

      expect(component.showErrorMessage).toHaveBeenCalledWith(mockError);
    });

    it('should handle showErrorMessage with 400 status code', () => {
      const mockError = { message: { statusCode: 400 } };
      jest.spyOn(component, 'showError');

      component.showErrorMessage(mockError);

      expect(component.editMultipleSubmitted).toBe(false);
      expect(component.showError).toHaveBeenCalledWith(mockError);
    });

    it('should handle showErrorMessage without message', () => {
      const mockError = {};
      toastrService.error = jest.fn();

      component.showErrorMessage(mockError);

      expect(component.editMultipleSubmitted).toBe(false);
      expect(toastrService.error).toHaveBeenCalledWith('Try again later.!', 'Something went wrong.');
    });

    it('should handle showErrorMessage with custom message', () => {
      const mockError = { message: 'Custom error' };
      toastrService.error = jest.fn();

      component.showErrorMessage(mockError);

      expect(component.editMultipleSubmitted).toBe(false);
      expect(toastrService.error).toHaveBeenCalledWith('Custom error', 'OOPS!');
    });

    it('should handle editNDRSuccess', () => {
      const mockResponse = { message: 'Success message' };
      component.modalRef = { hide: jest.fn() } as any;
      toastrService.success = jest.fn();
      jest.spyOn(component, 'deliverForm');
      deliveryService.updatedHistory = jest.fn();

      component.editNDRSuccess(mockResponse);

      expect(toastrService.success).toHaveBeenCalledWith('Success message', 'Success');
      expect(component.deliverForm).toHaveBeenCalled();
      expect(deliveryService.updatedHistory).toHaveBeenCalledWith({ status: true }, 'NDREditHistory');
      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(component.editMultipleSubmitted).toBe(false);
      expect(component.selectedDeliveryRequestId).toBe('');
      expect(component.currentDeliveryRequestSelectAll).toBe(false);
    });
  });

  describe('Additional Methods Coverage', () => {
    beforeEach(() => {
      component.ProjectId = 1;
      component.ParentCompanyId = 2;
      component.deliverForm();
    });

    it('should handle setDefaultPerson method', () => {
      const mockResponse = {
        data: {
          id: 1,
          User: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>'
          }
        }
      };
      deliveryService.getMemberRole.mockReturnValue(of(mockResponse));
      deliveryService.updateLoginUser = jest.fn();

      component.setDefaultPerson();

      expect(deliveryService.getMemberRole).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 2
      });
      expect(deliveryService.updateLoginUser).toHaveBeenCalledWith(mockResponse.data);
    });

    it('should handle setDefaultPerson with no lastName', () => {
      const mockResponse = {
        data: {
          id: 1,
          User: {
            firstName: 'John',
            lastName: null,
            email: '<EMAIL>'
          }
        }
      };
      deliveryService.getMemberRole.mockReturnValue(of(mockResponse));

      component.setDefaultPerson();

      const expectedEmail = 'John (<EMAIL>)';
      expect(component.deliverEditMultipleForm.get('person').value[0].email).toBe(expectedEmail);
    });

    it('should handle requestAutocompleteItems', () => {
      const mockResponse = { data: [{ id: 1, name: 'Test User' }] };
      deliveryService.searchNewMember.mockReturnValue(of(mockResponse));

      const result = component.requestAutocompleteItems('test');

      expect(deliveryService.searchNewMember).toHaveBeenCalledWith({
        ProjectId: 1,
        search: 'test',
        ParentCompanyId: 2
      });
      expect(result).toBeDefined();
    });

    it('should handle openEditMultipleModal', () => {
      const mockTemplate = {} as any;
      jest.spyOn(component, 'deliverForm');
      jest.spyOn(component, 'setDefaultPerson');
      component.selectedDeliveryRequestId = 'D001,D002,';

      component.openEditMultipleModal(mockTemplate);

      expect(component.deliverForm).toHaveBeenCalled();
      expect(component.setDefaultPerson).toHaveBeenCalled();
      expect(modalService.show).toHaveBeenCalledWith(mockTemplate, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg new-delivery-popup custom-modal edit-multiple-modal'
      });
      expect(component.selectedDeliveryRequestId).toBe('D001,D002');
    });

    it('should handle getOverAllGateInNewDelivery', () => {
      const mockResponse = { data: [{ id: 1, gateName: 'Gate 1' }] };
      projectService.gateList.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getOverAllEquipmentInNewDelivery');

      component.getOverAllGateInNewDelivery();

      expect(component.modalLoader).toBe(true);
      expect(projectService.gateList).toHaveBeenCalledWith({
        ProjectId: 1,
        pageSize: 0,
        pageNo: 0,
        ParentCompanyId: 2
      }, { isFilter: true, showActivatedAlone: true });
      expect(component.gateList).toEqual(mockResponse.data);
      expect(component.getOverAllEquipmentInNewDelivery).toHaveBeenCalled();
    });

    it('should handle getOverAllEquipmentInNewDelivery', () => {
      const mockResponse = { data: [{ id: 1, equipmentName: 'Equipment 1' }] };
      projectService.listEquipment.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'newNdrgetCompanies');

      component.getOverAllEquipmentInNewDelivery();

      expect(projectService.listEquipment).toHaveBeenCalledWith({
        ProjectId: 1,
        pageSize: 0,
        pageNo: 0,
        ParentCompanyId: 2
      }, { isFilter: true, showActivatedAlone: true });
      expect(component.equipmentList).toEqual(mockResponse.data);
      expect(component.newNdrgetCompanies).toHaveBeenCalled();
    });

    it('should handle newNdrgetCompanies', () => {
      const mockResponse = { data: [{ id: 1, companyName: 'Company 1' }] };
      projectService.getCompanies.mockReturnValue(of(mockResponse));
      jest.spyOn(component, 'getDefinable');

      component.newNdrgetCompanies();

      expect(projectService.getCompanies).toHaveBeenCalledWith({
        ProjectId: 1,
        ParentCompanyId: 2
      });
      expect(component.companyList).toEqual(mockResponse.data);
      expect(component.getDefinable).toHaveBeenCalled();
    });

    it('should handle onEditSubmitForm for different field types', () => {
      // Test companies
      component.onEditSubmitForm('companies');
      expect(component.companyEdited).toBe(true);

      // Test dfow
      component.onEditSubmitForm('dfow');
      expect(component.dfowEdited).toBe(true);

      // Test escort
      component.onEditSubmitForm('escort');
      expect(component.escortEdited).toBe(true);

      // Test persons
      component.onEditSubmitForm('persons');
      expect(component.responsiblePersonEdited).toBe(true);

      // Test gate
      component.onEditSubmitForm('gate');
      expect(component.gateEdited).toBe(true);

      // Test void
      component.onEditSubmitForm('void');
      expect(component.voidEdited).toBe(true);

      // Test status
      component.onEditSubmitForm('status');
      expect(component.statusEdited).toBe(true);
    });

    it('should handle onEditSubmitForm for deliveryDate with value', () => {
      component.deliverEditMultipleForm.get('deliveryDate').setValue('2024-12-31');
      jest.spyOn(component, 'setDefaultDeliveryTime');

      component.onEditSubmitForm('deliveryDate');

      expect(component.setDefaultDeliveryTime).toHaveBeenCalled();
      expect(component.deliveryDateEdited).toBe(true);
    });

    it('should handle onEditSubmitForm for deliveryDate without value', () => {
      component.deliverEditMultipleForm.get('deliveryDate').setValue('');

      component.onEditSubmitForm('deliveryDate');

      expect(component.deliveryDateEdited).toBe(false);
      expect(component.deliverEditMultipleForm.get('deliveryStart').value).toBe('');
      expect(component.deliverEditMultipleForm.get('deliveryEnd').value).toBe('');
    });

    it('should handle checkEquipmentType with crane equipment', () => {
      component.equipmentList = [
        { id: 1, PresetEquipmentType: { isCraneType: true } },
        { id: 2, PresetEquipmentType: { isCraneType: false } }
      ];
      const event = [{ id: 1 }];

      component.checkEquipmentType(event);

      expect(component.equipmentEdited).toBe(true);
      expect(component.craneEquipmentTypeChosen).toBe(true);
      expect(component.isAssociatedWithCraneRequest).toBe(true);
    });

    it('should handle checkEquipmentType with non-crane equipment', () => {
      component.equipmentList = [
        { id: 1, PresetEquipmentType: { isCraneType: true } },
        { id: 2, PresetEquipmentType: { isCraneType: false } }
      ];
      const event = [{ id: 2 }];

      component.checkEquipmentType(event);

      expect(component.equipmentEdited).toBe(true);
      expect(component.isAssociatedWithCraneRequest).toBe(false);
    });

    it('should handle checkEquipmentType with empty event', () => {
      component.checkEquipmentType([]);

      expect(component.craneEquipmentTypeChosen).toBe(false);
    });

    it('should handle checkEquipmentType with null event', () => {
      component.checkEquipmentType(null);

      expect(component.craneEquipmentTypeChosen).toBe(false);
    });
  });

  describe('Constructor Subscription Edge Cases', () => {
    it('should handle loginUser subscription with role 1', () => {
      const mockUser = { RoleId: 1, id: 1 };
      mockDeliveryService.loginUser = of(mockUser);

      const newFixture = TestBed.createComponent(DeliveryRequestsComponent);
      const newComponent = newFixture.componentInstance;

      expect(newComponent.authUser).toEqual(mockUser);
      expect(newComponent.statusValue).toEqual(['Approved', 'Declined']);
    });

    it('should handle loginUser subscription with other roles', () => {
      const mockUser = { RoleId: 4, id: 1 };
      mockDeliveryService.loginUser = of(mockUser);

      const newFixture = TestBed.createComponent(DeliveryRequestsComponent);
      const newComponent = newFixture.componentInstance;

      expect(newComponent.authUser).toEqual(mockUser);
      expect(newComponent.statusValue).toEqual([]);
    });

    it('should handle getCurrentStatus subscription and call getDeliveryRequest', () => {
      const mockDeliveryId = 456;
      mockDeliveryService.getCurrentStatus = of(mockDeliveryId);

      const newFixture = TestBed.createComponent(DeliveryRequestsComponent);
      const newComponent = newFixture.componentInstance;
      jest.spyOn(newComponent, 'getDeliveryRequest');

      expect(newComponent.deliveryId).toBe(456);
    });

    it('should handle projectParent subscription and call methods', () => {
      const mockProjectData = { ProjectId: 5, ParentCompanyId: 6 };
      mockProjectService.projectParent = of(mockProjectData);

      const newFixture = TestBed.createComponent(DeliveryRequestsComponent);
      const newComponent = newFixture.componentInstance;
      jest.spyOn(newComponent, 'getDeliveryRequest');
      jest.spyOn(newComponent, 'getOverAllGateInNewDelivery');
      jest.spyOn(newComponent, 'getMembers');

      expect(newComponent.ProjectId).toBe(5);
      expect(newComponent.ParentCompanyId).toBe(6);
      expect(newComponent.loader).toBe(true);
      expect(newComponent.deliveryList).toEqual([]);
    });
  });

  describe('Error Handling Edge Cases', () => {
    it('should handle getDeliveryRequest error', () => {
      const mockError = { message: 'API Error' };
      deliveryService.listNDR.mockReturnValue(throwError(() => mockError));

      component.getDeliveryRequest();

      expect(component.loader).toBe(true);
    });

    it('should handle getMembers error', () => {
      const mockError = { message: 'Members API Error' };
      projectService.listAllMember.mockReturnValue(throwError(() => mockError));

      component.getMembers();

      expect(projectService.listAllMember).toHaveBeenCalled();
    });

    it('should handle getDefinable error', () => {
      const mockError = { message: 'Definable API Error' };
      projectService.getDefinableWork.mockReturnValue(throwError(() => mockError));

      component.getDefinable();

      expect(projectService.getDefinableWork).toHaveBeenCalled();
    });
  });

  describe('Additional Utility Methods', () => {
    it('should handle handleDownKeydown with Space key', () => {
      const mockEvent = { key: ' ', preventDefault: jest.fn() } as any;
      jest.spyOn(component, 'clear');

      component.handleDownKeydown(mockEvent, null, null, 'clear');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(component.clear).toHaveBeenCalled();
    });

    it('should handle handleDownKeydown with invalid action', () => {
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as any;

      component.handleDownKeydown(mockEvent, null, null, 'invalid');

      expect(mockEvent.preventDefault).toHaveBeenCalled();
    });

    it('should handle getDeliveryRequest with different filter combinations', () => {
      const mockResponse = {
        data: { rows: [], count: 0 },
        lastId: 0,
        statusData: { statusColorCode: JSON.stringify([]) }
      };
      deliveryService.listNDR.mockReturnValue(of(mockResponse));
      component.filterForm.patchValue({
        pickFrom: '2024-01-01',
        pickTo: '2024-01-31'
      });

      component.getDeliveryRequest();

      expect(deliveryService.listNDR).toHaveBeenCalled();
    });

    it('should handle getDeliveryRequest with search parameter', () => {
      const mockResponse = {
        data: { rows: [], count: 0 },
        lastId: 0,
        statusData: { statusColorCode: JSON.stringify([]) }
      };
      deliveryService.listNDR.mockReturnValue(of(mockResponse));
      component.search = 'test search';

      component.getDeliveryRequest();

      expect(deliveryService.listNDR).toHaveBeenCalled();
    });

    it('should handle getDeliveryRequest with void parameter', () => {
      const mockResponse = {
        data: { rows: [], count: 0 },
        lastId: 0,
        statusData: { statusColorCode: JSON.stringify([]) }
      };
      deliveryService.listNDR.mockReturnValue(of(mockResponse));
      component.voidvalue = true;

      component.getDeliveryRequest();

      expect(deliveryService.listNDR).toHaveBeenCalled();
    });

    it('should handle delivery list processing with different member details', () => {
      const mockResponse = {
        data: {
          rows: [
            {
              id: 1,
              DeliveryId: 'D001',
              status: 'Approved',
              memberDetails: []
            }
          ],
          count: 1
        },
        lastId: 123,
        statusData: {
          statusColorCode: JSON.stringify([])
        }
      };
      deliveryService.listNDR.mockReturnValue(of(mockResponse));
      component.authUser = { RoleId: 2, id: 1 };

      component.getDeliveryRequest();

      expect(component.deliveryList[0].isAllowedToEdit).toBe(false);
    });

    it('should handle delivery list processing with matching member', () => {
      const mockResponse = {
        data: {
          rows: [
            {
              id: 1,
              DeliveryId: 'D001',
              status: 'Approved',
              memberDetails: [{ Member: { id: 1 } }]
            }
          ],
          count: 1
        },
        lastId: 123,
        statusData: {
          statusColorCode: JSON.stringify([])
        }
      };
      deliveryService.listNDR.mockReturnValue(of(mockResponse));
      component.authUser = { RoleId: 2, id: 1 };

      component.getDeliveryRequest();

      expect(component.deliveryList[0].isAllowedToEdit).toBe(true);
    });

    it('should handle setStatus with different role and status combinations', () => {
      const mockItem = { id: 1, status: 'Pending' };
      component.authUser = { RoleId: 3 };

      component.setStatus(mockItem);

      expect(component.showStatus).toBe(true);
    });

    it('should handle dropped files with directory entry', () => {
      const mockFile = {
        relativePath: 'test.xlsx',
        fileEntry: { isFile: false }
      };
      const files = [mockFile] as any;

      component.dropped(files);

      expect(component.files).toEqual(files);
    });

    it('should handle resetForm with modalRef1 undefined', () => {
      component.modalRef1 = undefined;
      component.modalRef = { hide: jest.fn() } as any;
      component.files = [{ name: 'test' }] as any;

      component.resetForm('yes');

      expect(component.modalRef.hide).toHaveBeenCalled();
      expect(component.files).toEqual([]);
    });

    it('should handle onEditMultipleSubmit with valid payload', () => {
      jest.spyOn(component, 'preparePayload').mockReturnValue({ test: 'payload' });
      jest.spyOn(component, 'submitEditMultiple');

      component.onEditMultipleSubmit();

      expect(component.preparePayload).toHaveBeenCalled();
      expect(component.submitEditMultiple).toHaveBeenCalledWith({ test: 'payload' });
    });

    it('should handle onEditMultipleSubmit with invalid payload', () => {
      jest.spyOn(component, 'preparePayload').mockReturnValue(null);
      jest.spyOn(component, 'submitEditMultiple');

      component.onEditMultipleSubmit();

      expect(component.preparePayload).toHaveBeenCalled();
      expect(component.submitEditMultiple).not.toHaveBeenCalled();
    });

    it('should handle ngOnInit without modalRef', () => {
      component.modalRef = undefined;

      component.ngOnInit();
      routerEventsSubject.next({});

      // Should not throw error when modalRef is undefined
      expect(component.modalRef).toBeUndefined();
    });

    it('should handle constructor subscriptions with undefined values', () => {
      mockProjectService.projectParent = of(undefined);
      mockProjectService.ParentCompanyId = of(undefined);
      mockDeliveryService.loginUser = of(undefined);
      mockDeliveryService.getCurrentStatus = of(undefined);

      const newFixture = TestBed.createComponent(DeliveryRequestsComponent);
      const newComponent = newFixture.componentInstance;

      // Should handle undefined values gracefully
      expect(newComponent).toBeTruthy();
    });

    it('should handle constructor subscriptions with null values', () => {
      mockProjectService.projectParent = of(null);
      mockProjectService.ParentCompanyId = of(null);
      mockDeliveryService.loginUser = of(null);
      mockDeliveryService.getCurrentStatus = of(null);

      const newFixture = TestBed.createComponent(DeliveryRequestsComponent);
      const newComponent = newFixture.componentInstance;

      // Should handle null values gracefully
      expect(newComponent).toBeTruthy();
    });

    it('should handle constructor subscriptions with empty string values', () => {
      mockProjectService.projectParent = of({ ProjectId: '', ParentCompanyId: '' } as any);
      mockProjectService.ParentCompanyId = of('' as any);
      mockDeliveryService.loginUser = of({ RoleId: '' } as any);
      mockDeliveryService.getCurrentStatus = of('' as any);

      const newFixture = TestBed.createComponent(DeliveryRequestsComponent);
      const newComponent = newFixture.componentInstance;

      // Should handle empty string values gracefully
      expect(newComponent).toBeTruthy();
    });

    it('should handle getDeliveryRequest with all filter parameters', () => {
      const mockResponse = {
        data: { rows: [], count: 0 },
        lastId: 0,
        statusData: { statusColorCode: JSON.stringify([]) }
      };
      deliveryService.listNDR.mockReturnValue(of(mockResponse));

      component.ProjectId = 1;
      component.ParentCompanyId = 2;
      component.pageSize = 50;
      component.pageNo = 2;
      component.search = 'test search';
      component.voidvalue = true;
      component.sortColumn = 'name';
      component.sort = 'ASC';

      component.filterForm.patchValue({
        companyFilter: '1',
        descriptionFilter: 'test desc',
        statusFilter: 'Approved',
        dateFilter: '2024-01-01',
        memberFilter: '1',
        gateFilter: '1',
        equipmentFilter: '1',
        locationFilter: 'test location',
        pickFrom: '2024-01-01',
        pickTo: '2024-01-31'
      });

      component.getDeliveryRequest();

      expect(deliveryService.listNDR).toHaveBeenCalledWith(expect.objectContaining({
        ProjectId: 1,
        pageSize: 50,
        pageNo: 2,
        void: 1,
        search: 'test search',
        sortColumn: 'name',
        sort: 'ASC',
        companyFilter: '1',
        descriptionFilter: 'test desc',
        statusFilter: 'Approved',
        dateFilter: '2024-01-01',
        memberFilter: '1',
        gateFilter: '1',
        equipmentFilter: '1',
        locationFilter: 'test location',
        pickFrom: '2024-01-01',
        pickTo: '2024-01-31'
      }));
    });

    it('should handle status color processing in getDeliveryRequest', () => {
      const mockResponse = {
        data: { rows: [], count: 0 },
        lastId: 0,
        statusData: {
          statusColorCode: JSON.stringify([
            { status: 'approved', backgroundColor: '#green', fontColor: '#white' },
            { status: 'pending', backgroundColor: '#yellow', fontColor: '#black' },
            { status: 'delivered', backgroundColor: '#blue', fontColor: '#white' },
            { status: 'rejected', backgroundColor: '#red', fontColor: '#white' },
            { status: 'expired', backgroundColor: '#gray', fontColor: '#white' }
          ])
        }
      };
      deliveryService.listNDR.mockReturnValue(of(mockResponse));

      component.getDeliveryRequest();

      expect(component.approvedBackgroundColor).toBe('#green');
      expect(component.approvedFontColor).toBe('#white');
      expect(component.pendingBackgroundColor).toBe('#yellow');
      expect(component.pendingFontColor).toBe('#black');
      expect(component.deliveredBackgroundColor).toBe('#blue');
      expect(component.deliveredFontColor).toBe('#white');
      expect(component.rejectedBackgroundColor).toBe('#red');
      expect(component.rejectedFontColor).toBe('#white');
      expect(component.expiredBackgroundColor).toBe('#gray');
      expect(component.expiredFontColor).toBe('#white');
    });

    it('should handle delivery list item processing with edit permissions', () => {
      const mockResponse = {
        data: {
          rows: [
            {
              id: 1,
              DeliveryId: 'D001',
              status: 'Approved',
              memberDetails: [{ Member: { id: 1 } }, { Member: { id: 2 } }]
            },
            {
              id: 2,
              DeliveryId: 'D002',
              status: 'Pending',
              memberDetails: [{ Member: { id: 3 } }]
            }
          ],
          count: 2
        },
        lastId: 123,
        statusData: { statusColorCode: JSON.stringify([]) }
      };
      deliveryService.listNDR.mockReturnValue(of(mockResponse));
      component.authUser = { RoleId: 2, id: 1 };

      component.getDeliveryRequest();

      expect(component.deliveryList[0].isAllowedToEdit).toBe(true);
      expect(component.deliveryList[1].isAllowedToEdit).toBe(false);
      expect(component.deliveryList[0].isChecked).toBe(false);
      expect(component.deliveryList[1].isChecked).toBe(false);
    });

    it('should handle delivery list item processing with role 1 or 4', () => {
      const mockResponse = {
        data: {
          rows: [
            {
              id: 1,
              DeliveryId: 'D001',
              status: 'Approved',
              memberDetails: []
            }
          ],
          count: 1
        },
        lastId: 123,
        statusData: { statusColorCode: JSON.stringify([]) }
      };
      deliveryService.listNDR.mockReturnValue(of(mockResponse));
      component.authUser = { RoleId: 1, id: 1 };

      component.getDeliveryRequest();

      expect(component.deliveryList[0].isAllowedToEdit).toBe(true);
    });

    // Additional Coverage for Selection Methods
    it('should handle selectAllCurrentDeliveryRequestForEdit with mixed checked items', () => {
      component.deliveryList = [
        { isChecked: true, DeliveryId: 'D001', id: 1, status: 'Approved' },
        { isChecked: false, DeliveryId: 'D002', id: 2, status: 'Pending' },
        { isChecked: true, DeliveryId: 'D003', id: 3, status: 'Delivered' }
      ];
      component.currentDeliveryRequestSelectAll = false;

      component.selectAllCurrentDeliveryRequestForEdit();

      expect(component.currentDeliveryRequestSelectAll).toBe(true);
      expect(component.deliveryList[0].isChecked).toBe(true);
      expect(component.deliveryList[1].isChecked).toBe(true);
      expect(component.deliveryList[2].isChecked).toBe(true);
      expect(component.selectedDeliveryRequestId).toContain('D001');
      expect(component.selectedDeliveryRequestId).toContain('D002');
      expect(component.selectedDeliveryRequestId).toContain('D003');
    });

    it('should handle selectAllCurrentDeliveryRequestForEdit when all already selected', () => {
      component.deliveryList = [
        { isChecked: true, DeliveryId: 'D001', id: 1, status: 'Approved' },
        { isChecked: true, DeliveryId: 'D002', id: 2, status: 'Pending' }
      ];
      component.currentDeliveryRequestSelectAll = true;

      component.selectAllCurrentDeliveryRequestForEdit();

      expect(component.currentDeliveryRequestSelectAll).toBe(false);
      expect(component.deliveryList[0].isChecked).toBe(false);
      expect(component.deliveryList[1].isChecked).toBe(false);
      expect(component.selectedDeliveryRequestId).toBe('');
    });

    it('should handle checkIfCurrentDeliveryRequestRowSelected when selectAll is true', () => {
      component.currentDeliveryRequestSelectAll = true;

      const result = component.checkIfCurrentDeliveryRequestRowSelected();

      expect(result).toBe(false);
    });

    it('should handle checkIfCurrentDeliveryRequestRowSelected when no items selected', () => {
      component.currentDeliveryRequestSelectAll = false;
      component.selectedDeliveryRequestId = '';

      const result = component.checkIfCurrentDeliveryRequestRowSelected();

      expect(result).toBe(false);
    });

    it('should handle checkIfCurrentDeliveryRequestRowSelected when items selected', () => {
      component.currentDeliveryRequestSelectAll = false;
      component.selectedDeliveryRequestId = 'D001,D002,';

      const result = component.checkIfCurrentDeliveryRequestRowSelected();

      expect(result).toBe(true);
    });

    it('should handle setSelectedCurrentDeliveryRequestItem when unchecking item', () => {
      component.deliveryList = [
        { isChecked: true, DeliveryId: 'D001', id: 1, status: 'Approved' }
      ];
      component.selectedDeliveryRequestId = 'D001,';
      component.selectedDeliveryRequestIdForMultipleEdit = [1];

      component.setSelectedCurrentDeliveryRequestItem(0);

      expect(component.deliveryList[0].isChecked).toBe(false);
      expect(component.selectedDeliveryRequestId).toBe('');
      expect(component.selectedDeliveryRequestIdForMultipleEdit).toEqual([]);
    });

    it('should handle setSelectedCurrentDeliveryRequestItem when checking item', () => {
      component.deliveryList = [
        { isChecked: false, DeliveryId: 'D001', id: 1, status: 'Approved' }
      ];
      component.selectedDeliveryRequestId = '';
      component.selectedDeliveryRequestIdForMultipleEdit = [];

      component.setSelectedCurrentDeliveryRequestItem(0);

      expect(component.deliveryList[0].isChecked).toBe(true);
      expect(component.selectedDeliveryRequestId).toBe('D001,');
      expect(component.selectedDeliveryRequestIdForMultipleEdit).toEqual([1]);
    });

    // Additional Modal Tests
    it('should handle openIdModal with different parameters', () => {
      const mockItem = { id: 123, ProjectId: 456 };
      const mockNdrStatus = 'current';
      const mockModalRef = { content: { closeBtnName: 'Close' } };
      modalService.show.mockReturnValue(mockModalRef as any);

      component.openIdModal(mockItem, mockNdrStatus);

      expect(deliveryService.updateStateOfNDR).toHaveBeenCalledWith(mockNdrStatus);
      expect(modalService.show).toHaveBeenCalled();
    });

    it('should handle openEditModal with different ndrState values', () => {
      const mockItem = {
        id: 1,
        ProjectId: 1,
        isAssociatedWithDeliveryRequest: true,
        isAssociatedWithCraneRequest: false
      };
      const mockModalRef = { content: { closeBtnName: 'Close' } };
      modalService.show.mockReturnValue(mockModalRef as any);

      component.openEditModal(mockItem, 'history');

      expect(modalService.show).toHaveBeenCalled();
    });

    it('should handle openEditModal with crane request association', () => {
      const mockItem = {
        id: 1,
        ProjectId: 1,
        isAssociatedWithDeliveryRequest: false,
        isAssociatedWithCraneRequest: true
      };
      const mockModalRef = { content: { closeBtnName: 'Close' } };
      modalService.show.mockReturnValue(mockModalRef as any);

      component.openEditModal(mockItem, 'edit');

      expect(modalService.show).toHaveBeenCalled();
    });

    it('should handle openEditModal with no associations', () => {
      const mockItem = {
        id: 1,
        ProjectId: 1,
        isAssociatedWithDeliveryRequest: false,
        isAssociatedWithCraneRequest: false
      };
      const mockModalRef = { content: { closeBtnName: 'Close' } };
      modalService.show.mockReturnValue(mockModalRef as any);

      component.openEditModal(mockItem, 'edit');

      expect(modalService.show).toHaveBeenCalled();
    });

    // Additional Service Error Tests
    it('should handle getOverAllGateForNdrGrid service error', () => {
      const mockError = { message: 'Service error' };
      projectService.gateList.mockReturnValue(throwError(() => mockError));

      component.getOverAllGateForNdrGrid();

      expect(projectService.gateList).toHaveBeenCalled();
    });

    it('should handle getOverAllEquipmentForNdrGrid service error', () => {
      const mockError = { message: 'Service error' };
      projectService.listEquipment.mockReturnValue(throwError(() => mockError));

      component.getOverAllEquipmentForNdrGrid();

      expect(projectService.listEquipment).toHaveBeenCalled();
    });

    it('should handle getDefinableForNdrGrid service error', () => {
      const mockError = { message: 'Service error' };
      projectService.getDefinableWork.mockReturnValue(throwError(() => mockError));

      component.getDefinableForNdrGrid();

      expect(projectService.getDefinableWork).toHaveBeenCalled();
    });

    it('should handle getLocations service error', () => {
      const mockError = { message: 'Service error' };
      projectService.getLocations.mockReturnValue(throwError(() => mockError));

      component.getLocations();

      expect(projectService.getLocations).toHaveBeenCalled();
    });

    it('should handle getCompaniesForNdrGrid service error', () => {
      const mockError = { message: 'Service error' };
      projectService.getCompanies.mockReturnValue(throwError(() => mockError));

      component.getCompaniesForNdrGrid();

      expect(projectService.getCompanies).toHaveBeenCalled();
    });
  });
});
