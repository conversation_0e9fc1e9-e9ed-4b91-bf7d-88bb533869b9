import { ComponentFixture, TestBed } from '@angular/core/testing';
import { EnterDetailsComponent } from './enter-details.component';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { ProjectSharingService } from '../../../services/projectSharingService/project-sharing.service';
import { AuthService } from '../../../services/auth/auth.service';
import { of, throwError } from 'rxjs';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';

describe('EnterDetailsComponent', () => {
  let component: EnterDetailsComponent;
  let fixture: ComponentFixture<EnterDetailsComponent>;
  let mockRouter: jest.Mocked<Router>;
  let mockActivatedRoute: any;
  let mockToastrService: jest.Mocked<ToastrService>;
  let mockProjectSharingService: jest.Mocked<ProjectSharingService>;
  let mockAuthService: jest.Mocked<AuthService>;

  beforeEach(async () => {
    mockRouter = {
      navigate: jest.fn()
    } as any;

    mockActivatedRoute = {
      queryParams: of({ type: 'deliveryCalendar' })
    };

    mockToastrService = {
      success: jest.fn(),
      error: jest.fn()
    } as any;

    mockProjectSharingService = {
      guestLogin: jest.fn(),
      getCompanyList: jest.fn().mockReturnValue(of({ companyList: [] })),
      alreadyVisited: jest.fn()
    } as any;

    mockAuthService = {
      checkDialCode: jest.fn()
    } as any;

    await TestBed.configureTestingModule({
      declarations: [EnterDetailsComponent],
      imports: [
        ReactiveFormsModule,
        NgMultiSelectDropDownModule
      ],
      providers: [
        FormBuilder,
        { provide: Router, useValue: mockRouter },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        { provide: ToastrService, useValue: mockToastrService },
        { provide: ProjectSharingService, useValue: mockProjectSharingService },
        { provide: AuthService, useValue: mockAuthService }
      ]
    }).compileComponents();

    // Mock localStorage
    Storage.prototype.getItem = jest.fn((key) => {
      if (key === 'guestProjectId') return btoa('123');
      if (key === 'guestParentCompanyId') return btoa('456');
      if (key === 'url') return btoa('/some-url');
      return null;
    });
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(EnterDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    expect(component.guestUserFormData).toBeTruthy();
    expect(component.guestUserFormData.get('phoneCode').value).toBeTruthy();
  });

  it('should get country codes on initialization', () => {
    expect(component.countryCode.length).toBeGreaterThan(0);
    expect(component.selectedCountryCode).toBe('+1');
  });

  it('should handle form submission successfully', () => {
    const mockResponse = {
      existUser: { id: 1 }
    };

    const mockCompany = {
      id: 1,
      companyName: 'Test Company'
    };

    mockProjectSharingService.guestLogin.mockReturnValue(of(mockResponse));
    component.companyLists = [mockCompany];
    component.companyId = 1;

    // Set form values
    component.guestUserFormData.patchValue({
      email: '<EMAIL>',
      phoneCode: '+1',
      phoneNumber: '1234567890',
      firstName: 'John',
      lastName: 'Doe',
      companyName: mockCompany
    });

    component.submit();

    expect(mockProjectSharingService.guestLogin).toHaveBeenCalled();
    expect(mockToastrService.success).toHaveBeenCalledWith("Let's get to work!", 'Success!');
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/guest-delivery-calendar']);
  });



  it('should validate number only input', () => {
    const validEvent = { which: 48 }; // '0' key
    const invalidEvent = { which: 65 }; // 'A' key

    expect(component.numberOnly(validEvent)).toBe(true);
    expect(component.numberOnly(invalidEvent)).toBe(false);
  });


  it('should handle form validation errors', () => {
    // Set invalid form values
    component.guestUserFormData.patchValue({
      email: 'invalid-email',
      phoneCode: '+1',
      phoneNumber: '123', // Too short
      firstName: '',
      lastName: '',
      companyName: null
    });

    component.submit();

    // The component sets detailsSubmitted to true when submit is called
    expect(component.detailsSubmitted).toBeTruthy();
    // The form is invalid, so guestLogin should not be called
    expect(mockProjectSharingService.guestLogin).not.toHaveBeenCalled();

    // The component doesn't call toastr.error for form validation errors
    // It just shows validation messages in the template
    // So we remove this expectation
  });

  it('should handle API error during form submission', () => {
    // Create an error response that matches the structure in the component
    const errorResponse = {
      message: {
        details: [{ email: 'Email already exists' }]
      }
    };

    // Mock the guestLogin method to call the error callback directly
    mockProjectSharingService.guestLogin = jest.fn().mockImplementation((options) => {
      // Immediately call the error callback with our error response
      if (options && typeof options.error === 'function') {
        options.error(errorResponse);
      }
      // Return an observable that never emits (since we're handling the error path)
      return throwError(() => errorResponse);
    });

    // Set valid form values
    component.guestUserFormData.patchValue({
      email: '<EMAIL>',
      phoneCode: '+1',
      phoneNumber: '1234567890',
      firstName: 'John',
      lastName: 'Doe',
      companyName: { id: 1, companyName: 'Test Company' }
    });

    component.companyLists = [{ id: 1, companyName: 'Test Company' }];
    component.companyId = 1;

    // Reset the mock to ensure we can check if it was called
    mockToastrService.error.mockClear();

    // Call submit which should trigger our mocked guestLogin
    component.submit();

    // Verify guestLogin was called
    expect(mockProjectSharingService.guestLogin).toHaveBeenCalled();

    // Verify toastr.error was called
    expect(mockToastrService.error).toHaveBeenCalled();
  });

  it('should handle company selection', () => {
    const mockCompany = { id: 1, companyName: 'Test Company' };
    component.selectCompany(mockCompany);

    expect(component.companyId).toBe(1);
    expect(component.selectedCompany).toEqual(mockCompany.companyName);
  });

  it('should handle new user registration', () => {
    const mockResponse = {
      existUser: null, // No existing user
      newUser: { id: 2 }
    };

    mockProjectSharingService.guestLogin.mockReturnValue(of(mockResponse));

    // Set form values
    component.guestUserFormData.patchValue({
      email: '<EMAIL>',
      phoneCode: '+1',
      phoneNumber: '1234567890',
      firstName: 'New',
      lastName: 'User',
      companyName: { id: 1, companyName: 'Test Company' }
    });

    component.companyLists = [{ id: 1, companyName: 'Test Company' }];
    component.companyId = 1;

    component.submit();

    expect(mockProjectSharingService.guestLogin).toHaveBeenCalled();
    expect(mockToastrService.success).toHaveBeenCalled();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/guest-delivery-calendar']);
  });

  it('should handle country code selection', () => {
    const mockCode = {
      countryDialCode: '+44'
    };

    mockAuthService.checkDialCode.mockReturnValue('(*************');
    component.selectCode(mockCode);

    expect(component.selectedCountryCode).toBe('+44');
    expect(component.phoneMask).toBe('(*************');
  });

  // it('should navigate to previous page', () => {
  //   component.previousPage();
  //   expect(mockRouter.navigate).toHaveBeenCalledWith(['/some-url']);
  // });

  it('should get companies list', () => {
    const mockCompanies = {
      companyList: [
        { id: 1, companyName: 'Company 1' },
        { id: 2, companyName: 'Company 2' }
      ]
    };

    mockProjectSharingService.getCompanyList.mockReturnValue(of(mockCompanies));
    component.getCompanies();

    expect(component.companyLists).toEqual(mockCompanies.companyList);
  });

  it('should handle error when getting companies list', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    mockProjectSharingService.getCompanyList.mockReturnValue(throwError(() => new Error('API Error')));

    component.getCompanies();

    expect(consoleSpy).toHaveBeenCalledWith('Error occurred:', expect.any(Error));
    consoleSpy.mockRestore();
  });

  it('should validate alphaOnly input - valid characters', () => {
    const validEvents = [
      { which: 65 }, // 'A'
      { which: 97 }, // 'a'
      { which: 8 },  // backspace
      { which: 32 }, // space
      { which: 50 }  // '2'
    ];

    validEvents.forEach(event => {
      expect(component.alphaOnly(event)).toBe(true);
    });
  });

  it('should validate alphaOnly input - invalid characters', () => {
    const invalidEvent = { which: 33 }; // '!' character
    expect(component.alphaOnly(invalidEvent)).toBe(true); // This method allows most characters
  });

  it('should validate alphaNum input - valid characters', () => {
    const validEvents = [
      { which: 65 }, // 'A'
      { which: 97 }, // 'a'
      { which: 50 }, // '2'
      { which: 32 }  // space
    ];

    validEvents.forEach(event => {
      expect(component.alphaNum(event)).toBe(true);
    });
  });

  it('should validate alphaNum input - invalid characters', () => {
    const invalidEvent = { which: 35 }; // '#' character
    expect(component.alphaNum(invalidEvent)).toBe(false);
  });

  it('should handle keyboard navigation for previousPage', () => {
    const mockEvent = {
      key: 'Enter',
      preventDefault: jest.fn()
    } as any;

    component.handleToggleKeydown(mockEvent);

    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/some-url']);
  });

  it('should handle space key for previousPage', () => {
    const mockEvent = {
      key: ' ',
      preventDefault: jest.fn()
    } as any;

    component.handleToggleKeydown(mockEvent);

    expect(mockEvent.preventDefault).toHaveBeenCalled();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/some-url']);
  });

  it('should not handle other keys for previousPage', () => {
    const mockEvent = {
      key: 'Tab',
      preventDefault: jest.fn()
    } as any;

    component.handleToggleKeydown(mockEvent);

    expect(mockEvent.preventDefault).not.toHaveBeenCalled();
    expect(mockRouter.navigate).not.toHaveBeenCalled();
  });

  it('should change phone mask', () => {
    const mockMask = '(*************';
    mockAuthService.checkDialCode.mockReturnValue(mockMask);

    component.changeMask('+1');

    expect(mockAuthService.checkDialCode).toHaveBeenCalledWith('+1');
    expect(component.phoneMask).toBe(mockMask);
  });

  it('should set guest user form data', () => {
    component.selectedCountryCode = '+44';
    component.selectedCompany = 'Test Company';

    component.setGuestUserForm();

    expect(component.guestUserFormData.get('phoneCode').value).toBe('+44');
    expect(component.guestUserFormData.get('companyName').value).toBe('Test Company');
  });

  it('should navigate to previous page', () => {
    component.previousPage();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/some-url']);
  });

  it('should validate email and handle already visited user', () => {
    const mockResponse = {
      data: {
        phoneCode: '+44',
        phoneNumber: '1234567890',
        firstName: 'John',
        lastName: 'Doe',
        Members: [{
          Company: { id: 1 }
        }]
      }
    };

    component.guestUserFormData.patchValue({ email: '<EMAIL>' });
    component.companyLists = [{ id: 1, companyName: 'Test Company' }];
    mockProjectSharingService.alreadyVisited.mockReturnValue(of(mockResponse));

    component.ifValidEmail();

    expect(mockProjectSharingService.alreadyVisited).toHaveBeenCalledWith({
      email: '<EMAIL>',
      ProjectId: 123
    });
    expect(component.data).toEqual(mockResponse.data);
  });

  it('should handle new user in ifValidEmail', () => {
    const mockResponse = { newUser: true };

    component.guestUserFormData.patchValue({ email: '<EMAIL>' });
    mockProjectSharingService.alreadyVisited.mockReturnValue(of(mockResponse));

    const resetSpy = jest.spyOn(component, 'resetResponseData');
    component.ifValidEmail();

    expect(resetSpy).toHaveBeenCalled();
  });

  it('should handle active member in ifValidEmail', () => {
    const mockResponse = { activeMember: 'You are already a member!' };

    component.guestUserFormData.patchValue({ email: '<EMAIL>' });
    mockProjectSharingService.alreadyVisited.mockReturnValue(of(mockResponse));

    const resetSpy = jest.spyOn(component, 'resetResponseData');
    component.ifValidEmail();

    expect(mockToastrService.error).toHaveBeenCalledWith('You are already a member!');
    expect(resetSpy).toHaveBeenCalled();
  });

  it('should handle error in ifValidEmail', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    component.guestUserFormData.patchValue({ email: '<EMAIL>' });
    mockProjectSharingService.alreadyVisited.mockReturnValue(throwError(() => new Error('API Error')));

    component.ifValidEmail();

    expect(consoleSpy).toHaveBeenCalledWith('Error occurred:', expect.any(Error));
    consoleSpy.mockRestore();
  });

  it('should not call alreadyVisited if email is invalid', () => {
    component.guestUserFormData.patchValue({ email: 'invalid-email' });

    component.ifValidEmail();

    expect(mockProjectSharingService.alreadyVisited).not.toHaveBeenCalled();
  });

  it('should set response data correctly', () => {
    const mockResponse = {
      data: {
        phoneCode: '+44',
        phoneNumber: '1234567890',
        firstName: 'John',
        lastName: 'Doe',
        Members: [{
          Company: { id: 1 }
        }]
      }
    };

    component.companyLists = [{ id: 1, companyName: 'Test Company' }];
    component.countryCode = [{ countryDialCode: '+44', name: 'UK' }];
    mockAuthService.checkDialCode.mockReturnValue('(*************');

    component.settingResponseData(mockResponse);

    expect(component.data).toEqual(mockResponse.data);
    expect(component.selectedCountryCode).toBe('+44');
    expect(component.companyId).toBe(1);
    expect(component.selectedCompany).toBe('Test Company');
  });

  it('should reset response data correctly', () => {
    component.countryCode = [{ countryDialCode: '+1', name: 'US' }];

    component.resetResponseData();

    expect(component.guestUserFormData.get('phoneNumber').value).toBe('');
    expect(component.guestUserFormData.get('firstName').value).toBe('');
    expect(component.guestUserFormData.get('lastName').value).toBe('');
    expect(component.guestUserFormData.get('companyName').value).toBe('');
    expect(component.selectedCountryCode).toBe('+1');
  });

  it('should save guest data to localStorage', () => {
    const setItemSpy = jest.spyOn(Storage.prototype, 'setItem');
    const payload = { email: '<EMAIL>' };
    const userId = 123;

    component.savingGuestData(payload, userId);

    expect(setItemSpy).toHaveBeenCalledWith('guestId', btoa('123'));
    expect(setItemSpy).toHaveBeenCalledWith('guestEmail', btoa('<EMAIL>'));
  });

  // Additional test cases for different request types in submit method
  it('should navigate to crane calendar for craneCalendar request type', () => {
    mockActivatedRoute.queryParams = of({ type: 'craneCalendar' });
    component.requestType = 'craneCalendar';

    const mockResponse = { existUser: { id: 1 } };
    mockProjectSharingService.guestLogin.mockReturnValue(of(mockResponse));

    component.guestUserFormData.patchValue({
      email: '<EMAIL>',
      phoneCode: '+1',
      phoneNumber: '1234567890',
      firstName: 'John',
      lastName: 'Doe',
      companyName: { id: 1, companyName: 'Test Company' }
    });

    component.companyLists = [{ id: 1, companyName: 'Test Company' }];
    component.companyId = 1;

    component.submit();

    expect(mockRouter.navigate).toHaveBeenCalledWith(['/guest-crane-calendar']);
  });

  it('should navigate to concrete calendar for concreteCalendar request type', () => {
    component.requestType = 'concreteCalendar';

    const mockResponse = { existUser: { id: 1 } };
    mockProjectSharingService.guestLogin.mockReturnValue(of(mockResponse));

    component.guestUserFormData.patchValue({
      email: '<EMAIL>',
      phoneCode: '+1',
      phoneNumber: '1234567890',
      firstName: 'John',
      lastName: 'Doe',
      companyName: { id: 1, companyName: 'Test Company' }
    });

    component.companyLists = [{ id: 1, companyName: 'Test Company' }];
    component.companyId = 1;

    component.submit();

    expect(mockRouter.navigate).toHaveBeenCalledWith(['/guest-concrete-calendar']);
  });

  it('should navigate to delivery booking for deliveryBooking request type', () => {
    component.requestType = 'deliveryBooking';

    const mockResponse = { existUser: { id: 1 } };
    mockProjectSharingService.guestLogin.mockReturnValue(of(mockResponse));

    component.guestUserFormData.patchValue({
      email: '<EMAIL>',
      phoneCode: '+1',
      phoneNumber: '1234567890',
      firstName: 'John',
      lastName: 'Doe',
      companyName: { id: 1, companyName: 'Test Company' }
    });

    component.companyLists = [{ id: 1, companyName: 'Test Company' }];
    component.companyId = 1;

    component.submit();

    expect(mockRouter.navigate).toHaveBeenCalledWith(['/guest-delivery-booking']);
  });

  it('should navigate to crane booking for craneBooking request type', () => {
    component.requestType = 'craneBooking';

    const mockResponse = { existUser: { id: 1 } };
    mockProjectSharingService.guestLogin.mockReturnValue(of(mockResponse));

    component.guestUserFormData.patchValue({
      email: '<EMAIL>',
      phoneCode: '+1',
      phoneNumber: '1234567890',
      firstName: 'John',
      lastName: 'Doe',
      companyName: { id: 1, companyName: 'Test Company' }
    });

    component.companyLists = [{ id: 1, companyName: 'Test Company' }];
    component.companyId = 1;

    component.submit();

    expect(mockRouter.navigate).toHaveBeenCalledWith(['/guest-crane-booking']);
  });

  it('should navigate to concrete booking for concreteBooking request type', () => {
    component.requestType = 'concreteBooking';

    const mockResponse = { existUser: { id: 1 } };
    mockProjectSharingService.guestLogin.mockReturnValue(of(mockResponse));

    component.guestUserFormData.patchValue({
      email: '<EMAIL>',
      phoneCode: '+1',
      phoneNumber: '1234567890',
      firstName: 'John',
      lastName: 'Doe',
      companyName: { id: 1, companyName: 'Test Company' }
    });

    component.companyLists = [{ id: 1, companyName: 'Test Company' }];
    component.companyId = 1;

    component.submit();

    expect(mockRouter.navigate).toHaveBeenCalledWith(['/guest-concrete-booking']);
  });

  it('should handle error message without details array', () => {
    const errorResponse = {
      message: {
        message: 'General error message'
      }
    };

    mockProjectSharingService.guestLogin = jest.fn().mockImplementation((options) => {
      if (options && typeof options.error === 'function') {
        options.error(errorResponse);
      }
      return throwError(() => errorResponse);
    });

    component.guestUserFormData.patchValue({
      email: '<EMAIL>',
      phoneCode: '+1',
      phoneNumber: '1234567890',
      firstName: 'John',
      lastName: 'Doe',
      companyName: { id: 1, companyName: 'Test Company' }
    });

    component.companyLists = [{ id: 1, companyName: 'Test Company' }];
    component.companyId = 1;

    mockToastrService.error.mockClear();
    component.submit();

    expect(mockToastrService.error).toHaveBeenCalledWith('General error message');
  });

  it('should handle form submission with minimum valid data', () => {
    const mockResponse = { newUser: { id: 2 } };
    mockProjectSharingService.guestLogin.mockReturnValue(of(mockResponse));

    component.guestUserFormData.patchValue({
      email: '<EMAIL>',
      phoneCode: '+1',
      phoneNumber: '1234567890',
      firstName: 'Min',
      lastName: 'User',
      companyName: { id: 1, companyName: 'Test Company' }
    });

    component.companyLists = [{ id: 1, companyName: 'Test Company' }];
    component.companyId = 1;
    component.requestType = 'deliveryCalendar';

    component.submit();

    expect(mockProjectSharingService.guestLogin).toHaveBeenCalled();
    expect(component.detailsSubmitted).toBe(false);
    expect(component.guestUserFormData.get('email').value).toBe('');
  });

  // Edge cases and negative scenarios
  it('should handle numberOnly with keyCode instead of which', () => {
    const validEvent = { keyCode: 48, which: null }; // '0' key
    const invalidEvent = { keyCode: 65, which: null }; // 'A' key

    expect(component.numberOnly(validEvent)).toBe(true);
    expect(component.numberOnly(invalidEvent)).toBe(false);
  });

  it('should handle alphaOnly with keyCode instead of which', () => {
    const validEvent = { keyCode: 65, which: null }; // 'A' key
    expect(component.alphaOnly(validEvent)).toBe(true);
  });

  it('should handle alphaNum with keyCode instead of which', () => {
    const validEvent = { keyCode: 65, which: null }; // 'A' key
    const invalidEvent = { keyCode: 35, which: null }; // '#' key

    expect(component.alphaNum(validEvent)).toBe(true);
    expect(component.alphaNum(invalidEvent)).toBe(false);
  });

  it('should handle special keys in numberOnly', () => {
    const backspaceEvent = { which: 8 }; // backspace
    const deleteEvent = { which: 46 }; // delete
    const tabEvent = { which: 9 }; // tab

    expect(component.numberOnly(backspaceEvent)).toBe(true);
    expect(component.numberOnly(deleteEvent)).toBe(true);
    expect(component.numberOnly(tabEvent)).toBe(true);
  });

  it('should handle boundary values in numberOnly', () => {
    const char0Event = { which: 48 }; // '0'
    const char9Event = { which: 57 }; // '9'
    const charBelow0Event = { which: 47 }; // '/' (before '0')
    const charAbove9Event = { which: 58 }; // ':' (after '9')

    expect(component.numberOnly(char0Event)).toBe(true);
    expect(component.numberOnly(char9Event)).toBe(true);
    expect(component.numberOnly(charBelow0Event)).toBe(false);
    expect(component.numberOnly(charAbove9Event)).toBe(false);
  });

  it('should handle empty company list in settingResponseData', () => {
    const mockResponse = {
      data: {
        phoneCode: '+44',
        phoneNumber: '1234567890',
        firstName: 'John',
        lastName: 'Doe',
        Members: [{
          Company: { id: 999 } // Non-existent company ID
        }]
      }
    };

    component.companyLists = []; // Empty company list
    component.countryCode = [{ countryDialCode: '+44', name: 'UK' }];
    mockAuthService.checkDialCode.mockReturnValue('(*************');

    // This should not throw an error even with empty company list
    expect(() => component.settingResponseData(mockResponse)).not.toThrow();
    expect(component.companyId).toBe(999);
  });

  it('should handle missing country code in settingResponseData', () => {
    const mockResponse = {
      data: {
        phoneCode: '+999', // Non-existent country code
        phoneNumber: '1234567890',
        firstName: 'John',
        lastName: 'Doe',
        Members: [{
          Company: { id: 1 }
        }]
      }
    };

    component.companyLists = [{ id: 1, companyName: 'Test Company' }];
    component.countryCode = [{ countryDialCode: '+44', name: 'UK' }];
    mockAuthService.checkDialCode.mockReturnValue('(*************');

    component.settingResponseData(mockResponse);

    expect(component.selectedCountryCode).toBe('+999');
    // foundCode will be undefined, so defaultCode will be [undefined]
    expect(component.defaultCode).toEqual([undefined]);
  });

  it('should handle missing company in settingResponseData', () => {
    const mockResponse = {
      data: {
        phoneCode: '+44',
        phoneNumber: '1234567890',
        firstName: 'John',
        lastName: 'Doe',
        Members: [{
          Company: { id: 999 } // Non-existent company ID
        }]
      }
    };

    component.companyLists = [{ id: 1, companyName: 'Test Company' }];
    component.countryCode = [{ countryDialCode: '+44', name: 'UK' }];
    mockAuthService.checkDialCode.mockReturnValue('(*************');

    component.settingResponseData(mockResponse);

    expect(component.companyId).toBe(999);
    // foundCompany will be undefined, so defaultCompany will be [undefined]
    expect(component.defaultCompany).toEqual([undefined]);
  });

  it('should handle form validation with empty required fields', () => {
    // Set all required fields to empty
    component.guestUserFormData.patchValue({
      email: '',
      phoneCode: '',
      phoneNumber: '',
      firstName: '',
      lastName: '',
      companyName: ''
    });

    component.submit();

    expect(component.detailsSubmitted).toBe(true);
    expect(component.guestUserFormData.valid).toBe(false);
    expect(mockProjectSharingService.guestLogin).not.toHaveBeenCalled();
  });

  it('should handle form validation with invalid email format', () => {
    component.guestUserFormData.patchValue({
      email: 'invalid.email.format',
      phoneCode: '+1',
      phoneNumber: '1234567890',
      firstName: 'John',
      lastName: 'Doe',
      companyName: { id: 1, companyName: 'Test Company' }
    });

    component.submit();

    expect(component.detailsSubmitted).toBe(true);
    expect(component.guestUserFormData.get('email').valid).toBe(false);
    expect(mockProjectSharingService.guestLogin).not.toHaveBeenCalled();
  });

  it('should handle form validation with short first name', () => {
    component.guestUserFormData.patchValue({
      email: '<EMAIL>',
      phoneCode: '+1',
      phoneNumber: '1234567890',
      firstName: 'Jo', // Too short (minimum 3 characters)
      lastName: 'Doe',
      companyName: { id: 1, companyName: 'Test Company' }
    });

    component.submit();

    expect(component.detailsSubmitted).toBe(true);
    expect(component.guestUserFormData.get('firstName').valid).toBe(false);
    expect(mockProjectSharingService.guestLogin).not.toHaveBeenCalled();
  });

  it('should handle form validation with short last name', () => {
    component.guestUserFormData.patchValue({
      email: '<EMAIL>',
      phoneCode: '+1',
      phoneNumber: '1234567890',
      firstName: 'John',
      lastName: 'Do', // Too short (minimum 3 characters)
      companyName: { id: 1, companyName: 'Test Company' }
    });

    component.submit();

    expect(component.detailsSubmitted).toBe(true);
    expect(component.guestUserFormData.get('lastName').valid).toBe(false);
    expect(mockProjectSharingService.guestLogin).not.toHaveBeenCalled();
  });

  it('should initialize component with correct default values', () => {
    expect(component.detailsSubmitted).toBe(false);
    expect(component.phoneMask).toBe('(*************');
    expect(component.selectedCountryCode).toBe('+1');
    expect(component.defaultCode).toBe('+1');
    expect(component.companyLists).toEqual([]);
    expect(component.countryCode.length).toBeGreaterThan(0);
  });

  it('should handle ngOnInit method', () => {
    // ngOnInit is empty but should not throw error
    expect(() => component.ngOnInit()).not.toThrow();
  });
});
