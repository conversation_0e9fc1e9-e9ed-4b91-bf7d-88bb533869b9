import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MycardsComponent } from './mycards.component';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Component, TemplateRef } from '@angular/core';

describe('MycardsComponent', () => {
  let component: MycardsComponent;
  let fixture: ComponentFixture<MycardsComponent>;
  let modalService: BsModalService;
  let mockModalRef: Partial<BsModalRef>;

  beforeEach(async () => {
    mockModalRef = {
      hide: jest.fn()
    };

    // Mock window.getComputedStyle
    Object.defineProperty(window, 'getComputedStyle', {
      value: () => ({
        getPropertyValue: () => ''
      })
    });

    await TestBed.configureTestingModule({
      declarations: [ MycardsComponent ],
      providers: [
        BsModalService
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(MycardsComponent);
    component = fixture.componentInstance;
    modalService = TestBed.inject(BsModalService);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('openCardModal', () => {
    it('should open modal with correct configuration', () => {
      // Arrange
      const mockTemplate = {} as TemplateRef<any>;
      const showSpy = jest.spyOn(modalService, 'show').mockReturnValue(mockModalRef as BsModalRef);

      // Act
      component.openCardModal(mockTemplate);

      // Assert
      expect(showSpy).toHaveBeenCalledWith(mockTemplate, {
        backdrop: 'static',
        keyboard: false,
        class: 'modal-lg custom-modal'
      });
      expect(component.modalRef).toBe(mockModalRef);
    });
  });

  describe('handleToggleKeydown', () => {
    it('should open modal when Enter key is pressed', () => {
      // Arrange
      const mockTemplate = {} as TemplateRef<any>;
      const mockEvent = { key: 'Enter', preventDefault: jest.fn() } as unknown as KeyboardEvent;
      const openCardModalSpy = jest.spyOn(component, 'openCardModal');

      // Act
      component.handleToggleKeydown(mockEvent, mockTemplate);

      // Assert
      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(openCardModalSpy).toHaveBeenCalledWith(mockTemplate);
    });

    it('should open modal when Space key is pressed', () => {
      // Arrange
      const mockTemplate = {} as TemplateRef<any>;
      const mockEvent = { key: ' ', preventDefault: jest.fn() } as unknown as KeyboardEvent;
      const openCardModalSpy = jest.spyOn(component, 'openCardModal');

      // Act
      component.handleToggleKeydown(mockEvent, mockTemplate);

      // Assert
      expect(mockEvent.preventDefault).toHaveBeenCalled();
      expect(openCardModalSpy).toHaveBeenCalledWith(mockTemplate);
    });

    it('should not open modal when other keys are pressed', () => {
      // Arrange
      const mockTemplate = {} as TemplateRef<any>;
      const mockEvent = { key: 'A', preventDefault: jest.fn() } as unknown as KeyboardEvent;
      const openCardModalSpy = jest.spyOn(component, 'openCardModal');

      // Act
      component.handleToggleKeydown(mockEvent, mockTemplate);

      // Assert
      expect(mockEvent.preventDefault).not.toHaveBeenCalled();
      expect(openCardModalSpy).not.toHaveBeenCalled();
    });
  });
});
