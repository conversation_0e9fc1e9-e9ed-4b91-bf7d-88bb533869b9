<section class="page-section pt-md-50px">
  <div class="page-inner-content">
    <div class="top-header my-3">
      <div class="row px-md-4">
        <div class="col-md-4 col-7">
          <h1 class="notification-header-desktop notification-header-mobile fw-bold cairo-regular">
            Notifications
          </h1>
        </div>
        <div class="col-md-8 col-5">
          <div class="top-filter">
            <ul class="list-group list-group-horizontal-md justify-content-end">
              <li class="list-group-item notification-listitem p0 border-0 bg-transparent me-2">
                <div class="search-icon">
                  <input
                    class="form-control fs12 color-grey8"
                    [ngClass]="showSearchbar ? 'input-hover-disable' : 'input-search'"
                    placeholder="What are you looking for?"
                    (input)="getSearchNotification($event.target.value)"
                    [(ngModel)]="search"
                  />
                  <div class="icon">
                    <img
                      src="./assets/images/cross-close.svg"
                      *ngIf="showSearchbar"
                      (click)="clear()"
                      alt="close-cross"
                      (keydown)="handleDownKeydown($event, '','', 'clear')"
                    />
                    <em class="fa fa-search fs12 color-grey8" *ngIf="!showSearchbar"></em>
                  </div>
                </div>
              </li>
              <li
                class="list-group-item notification-listitem p0 border-0 bg-transparent me-2 position-relative"
              >
                <div class="filter-icon" (click)="openModal1(filter)" (keydown)="handleDownKeydown($event, filter,'', 'open')">
                  <img src="./assets/images/filter.svg" class="h-12px icon" alt="Filter" />
                </div>
                <div
                  class="bg-orange rounded-circle position-absolute text-white filter-count"
                  *ngIf="filterCount > 0"
                >
                  <p class="m-0 text-center fs10">{{ filterCount }}</p>
                </div>
              </li>
              <li
                class="list-group-item notification-listitem markread-btn p0 border-0 bg-transparent me-2 position-relative"
              >
                <button
                  type="submit"
                  [disabled]="unReadCount === 0"
                  class="btn btn-orange color-orange radius20 fs12 fw-bold markread-padding-desktop markread-padding-mobile"
                  (click)="markall()"
                >
                  Mark All As Read
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <div
      class="card notify-card white-border m-md-3 br-10 mb-3 mb-md-0"
      *ngFor="
        let data of notificationList
          | paginate : { itemsPerPage: pageSize, currentPage: pageNo, totalItems: totalCount };
        let i = index
      "
    >
      <div
        class="card-body br-10 pb-2"
        (click)="setRead(data, i)"
        (keydown)="handleDownKeydown($event, data, i, 'read')"
        [ngStyle]="{ 'background-color': data?.DeliveryNotification[0]?.seen ? '' : '#FFDED2' }"
      >
        <button
          type="button"
          class="close notify-close bg-transparent border-0"
          aria-label="Close"
          (click)="setDeleteContent(deleteList, data)"
        >
          <span aria-hidden="true"
            ><img src="./assets/images/modal-close.svg" alt="Modal Close"
          /></span>
        </button>
        <h6 class="fw500 mb-2 pe-4">
          <span class="color-orange me-2">{{ data.title }}</span>
        </h6>
        <div class="row" (click)="openIdModal(data)" (keydown)="handleToggleKeydown($event, data)">
          <div class="col-6 col-sm-4 col-md-3 col-lg-3 col-xl-2">
            <label class="color-grey8 fs13 fw500 m-0" for="proName">Project name</label>
            <p class="color-grey7 fs13 fw500">{{ data.Project?.projectName }}</p>
          </div>
          <div
            class="col-6 col-sm-4 col-md-3 col-lg-3 col-xl-2 ps-0"
            *ngIf="data?.requestType === 'craneRequest'"
          >
            <label class="color-grey8 fs13 fw500 m-0" for="craId">Crane Booking ID</label>
            <p class="color-grey7 fs13 fw500">{{ data?.CraneRequest?.CraneRequestId }}</p>
          </div>
          <div
            class="col-6 col-sm-4 col-md-3 col-lg-3 col-xl-2 ps-0"
            *ngIf="data?.requestType === 'concreteRequest'"
          >
            <label class="color-grey8 fs13 fw500 m-0" for="concId">Concrete Booking ID</label>
            <p class="color-grey7 fs13 fw500">{{ data?.ConcreteRequest?.ConcreteRequestId }}</p>
          </div>
          <div
            class="col-6 col-sm-4 col-md-3 col-lg-3 col-xl-2 ps-0"
            *ngIf="data?.requestType === 'deliveryRequest'"
          >
            <label class="color-grey8 fs13 fw500 m-0" for="delId">Delivery ID</label>
            <p class="color-grey7 fs13 fw500">{{ data?.DeliveryRequest?.DeliveryId }}</p>
          </div>

          <div class="col-6 col-sm-6 col-md-6 col-lg-6 col-xl-6" *ngIf="data?.recurrenceType">
            <label class="color-grey8 fs13 fw500 m-0" for="rectype">Recurrence Type and Date Time</label>
            <p class="color-grey7 fs13 fw500">{{ data?.recurrenceType }}</p>
          </div>
        </div>
        <p
          class="color-grey7 fs13 fw500 pe-md-5 mb-2"
          (click)="openIdModal(data)" (keydown)="handleToggleKeydown($event, data)"
          *ngIf="!data?.DeliveryNotification[0]?.isLocationFollowNotification"
        >
          {{ data.description }}
        </p>
        <p
          class="color-grey7 fs13 fw500 pe-md-5 mb-2"
          (click)="openIdModal(data)"  (keydown)="handleToggleKeydown($event, data)"
          *ngIf="data?.DeliveryNotification[0]?.isLocationFollowNotification"
        >
          {{ data.locationFollowDescription }}
        </p>
        <p class="color-grey8 fs12 m-0" (click)="openIdModal(data)"  (keydown)="handleToggleKeydown($event, data)">
          {{ data.createdAt | date : 'medium' }}
        </p>
      </div>
    </div>
    <div class="row" *ngIf="loader == true">
      <div class="col-12 text-center">
        <div class="fs18 fw-bold cairo-regular my-5 text-black">Loading...</div>
      </div>
    </div>
    <div class="row" *ngIf="loader == false && notificationList.length == 0">
      <div class="col-12 text-center">
        <div class="fs18 fw-bold cairo-regular my-5 text-black">No Records Found</div>
      </div>
    </div>

    <div
      class="tab-pagination px-2"
      id="tab-pagination8"
      *ngIf="loader == false && totalCount > 25"
    >
      <div class="row">
        <div class="col-md-2 align-items-center">
          <ul class="list-inline my-3">
            <li class="list-inline-item notify-pagination">
              <label class="fs12 color-grey4" for="shwEnt">Show entries</label>
            </li>
            <li class="list-inline-item">
              <select id="shwEnt"
                class="w-auto form-select fs12 color-grey4 h30"
                (change)="changePageSize($event.target.value)"
                [ngModel]="pageSize"
              >
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
                <option value="150">150</option>
              </select>
            </li>
          </ul>
        </div>
        <div class="col-md-8 text-center">
          <div class="my-3 position-relative d-inline-block">
            <pagination-controls previousLabel="" nextLabel="" (pageChange)="changePageNo($event)">
            </pagination-controls>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<div id="filter-temp6">
  <!--Filter Modal-->
  <ng-template #filter>
    <div class="modal-header border-0 pb-0">
      <h3 class="fs12 fw-bold cairo-regular color-text7 my-0">Filter</h3>
      <button type="button" class="close ms-auto" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true"
          ><img src="./assets/images/modal-close.svg" alt="Modal Close"
        /></span>
      </button>
    </div>
    <div class="modal-body">
      <div class="filter-content">
        <form class="custom-material-form" [formGroup]="filterForm" (ngSubmit)="filterSubmit()">
          <div class="row">
            <div class="col-md-12">
              <div class="input-group mb-3">
                <select
                  class="form-control fs12 material-input"
                  formControlName="projectNameFilter"
                >
                  <option value="" disabled selected hidden>Project Name</option>
                  <option *ngFor="let item of projectList" value="{{ item.id }}">
                    {{ item.projectName }}
                  </option>
                </select>
              </div>
              <div class="input-group mb-3">
                <input
                  type="text"
                  class="form-control fs12 material-input"
                  placeholder="Desciription"
                  formControlName="descriptionFilter"
                />
                  <span class="input-group-text">
                    <img src="./assets/images/search-icon.svg" alt="Search" />
                  </span>
              </div>
              <div class="input-group mb-3">
                <input
                  class="form-control fs10 material-input"
                  #dp="bsDatepicker"
                  bsDatepicker
                  formControlName="dateFilter"
                  placeholder="Notification Date"
                  [bsConfig]="{
                    isAnimated: true,
                    showWeekNumbers: false,
                    customTodayClass: 'today'
                  }"
                />
              </div>
              <div class="row justify-content-end">
                <button
                  class="btn btn-orange radius20 col-4 mt-2 fs12 fw-bold cairo-regular mx-1"
                  type="submit"
                >
                  Apply
                </button>
                <button
                  class="btn btn-orange radius20 fs12 col-4 mt-2 fw-bold cairo-regular mx-1"
                  type="button"
                  (click)="resetFilter()"
                >
                  Reset
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </ng-template>
</div>
<!--Filter Modal-->
<ng-template #deleteList>
  <div class="modal-body">
    <div class="text-center my-4">
      <p class="color-grey15 fs14 fw-bold cairo-regular mb-5">Are you sure you want to delete?</p>
      <button
        class="btn btn-grey-light color-dark-grey radius20 fs12 me-3 fw-bold cairo-regular px-5"
        (click)="modalRef.hide()"
      >
        No
      </button>
      <button
        class="btn btn-orange color-orange radius20 fs12 fw-bold cairo-regular px-5"
        type="submit"
        (click)="delete()"
        [disabled]="deleteSubmitted"
      >
        <em class="fa fa-spinner" aria-hidden="true" *ngIf="deleteSubmitted"></em>yes
      </button>
    </div>
  </div>
</ng-template>
