import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-billings',
  templateUrl: './billings.component.html',
  })
export class BillingsComponent implements OnInit {
  public billingList: boolean = true;

  public showbillingHistory: boolean = false;

  public ngOnInit(): void { /* */ }

  public handleToggleKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.showHistory();
    }
  }

  public showHistory(): void {
    this.billingList = false;
    this.showbillingHistory = true;
  }

  public backtoList(): void {
    this.billingList = true;
    this.showbillingHistory = false;
  }
}
